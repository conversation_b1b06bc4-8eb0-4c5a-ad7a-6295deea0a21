package com.raycloud.dmj.services.trades.wave;

import com.raycloud.dmj.domain.enums.AiPackmaOpEnum;
import com.raycloud.dmj.domain.trades.vo.*;
import com.raycloud.dmj.domain.wave.CrossBorderWavePrintInfo;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageListBase;
import com.raycloud.dmj.domain.Status;
import com.raycloud.dmj.domain.TradeValidatorException;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.HotSaleParams;
import com.raycloud.dmj.domain.trades.params.WorkloadSupplementQueryParams;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.dto.WaveAutoCreateRuleDTO;
import com.raycloud.dmj.domain.wave.model.*;
import com.raycloud.dmj.domain.wave.params.WaveBatchUpdateRemarkParams;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.PickingType;
import com.raycloud.dmj.domain.wms.vo.FastInOutSimpleItemVO;
import com.raycloud.dmj.services.trades.WavePickingScanResult;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.web.model.wms.LogisticsOrderQueryParams;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 波次对外提供 dubbo 服务接口
 */
public interface IWaveServiceDubbo {

    /**
     * 根据条形码收货（商家编码 拣选号 位置号 唯一码形式）
     *
     * @param staff
     * @param barcodes
     * @param scanType 是否允许多件波次
     * @return
     */
    List<WaveUniqueCode> receiveByBarcodes(Staff staff, List<String> barcodes, Integer scanType) throws WaveScanException;

    /**
     * 查询已创建波次列表 会实时更新订单数和商品数
     *
     * @param staff
     * @param params
     * @param page
     * @return
     */
    PageListBase<Wave> queryCreatedWaves(Staff staff, WaveFilterParams params, Page page);

    PdaIndexNotifyResult queryPdaIndexNotify(Staff staff, PdaIndexNotifyParams params);

    /**
     * 根据条形码扫码（商家编码 拣选号 位置号 唯一码形式）
     *
     * @param staff
     * @param barcodes
     * @param scanType 是否允许多件波次
     * @return
     */
    List<WaveUniqueCode> scanByBarcodes(Staff staff, List<String> barcodes, Integer scanType) throws WaveScanException;

    /**
     * 查询波次
     *
     * @param staff
     * @param waveFilterParams
     */
    Wave queryWave(Staff staff, WaveFilterParams waveFilterParams);

    /**
     * 根据波次id查询 sid列表
     *
     * @param staff
     * @param waveId
     * @return
     */
    List<Long> querySidsByWaveId(Staff staff, Long waveId);

    /**
     * 根据波次id查询未打印 sid列表
     *
     * @param staff
     * @param waveId
     * @return
     */
    List<Long> querySidsByWaveId4Print(Staff staff, Long waveId, boolean printed);

    /**
     * 完结波次
     *  波次也可以操作完结，当操作完结时，该波次内，状态为未打印的订单踢出该波次。只留下已完成的订单。同时该波次的状态为处理完成。（只有拣选完成状态的波次才可以点击完结）
     * @param staff
     * @param waveId
     */
    void finishWave(Staff staff, Long waveId);

    /**
     * 完结波次
     *  波次也可以操作完结，当操作完结时，该波次内，状态为未打印的订单踢出该波次。只留下已完成的订单。同时该波次的状态为处理完成。（只有拣选完成状态的波次才可以点击完结）
     * @param staff
     * @param waveId
     * @param check ture:不完结拣选中波次 false:完结拣选中波次
     */
    void finishWave(Staff staff, Long waveId ,boolean check);

    /**
     * 根据波次ids获取波次中订单
     * @param staff
     * @param waveIds
     * @return
     */
    List<WaveTrade> queryWaveTradesByWaveIds(Staff staff, List<Long> waveIds);

    /**
     * 根据商品id获取踢出波次订单
     *
     * @param staff
     * @return
     */
    List<WaveTrade> queryRemovedWaveTradeBySysItemSkuId(Staff staff, WavePickingParam param);

    List<WaveTrade> queryRemovedWaveTrade(Staff staff, WavePickingParam param);

    /**
     * 根据波次id查询分拣信息
     *
     * @param staff
     * @param waveId
     * @param withDetail
     * @return
     */
    List<WaveSorting> queryWaveSortingsByWaveId(Staff staff, Long waveId, boolean withDetail);

    /**
     * 根据波次ids查询分拣信息
     *
     * @param staff
     * @param waveIds
     * @param withDetail
     * @return
     */
    List<WaveSorting> queryWaveSortingsByWaveIds(Staff staff, List<Long> waveIds, boolean withDetail);

    /**
     * 根据波次id查询分拣信息(拣选绩效)
     *
     * @param staff
     * @param waveId
     * @param withDetail
     * @return
     */
    List<WaveSorting> queryWaveSortingsByWaveIdFromReport(Staff staff, Long waveId, boolean withDetail);

    /**
     * 查询波次列表
     *
     * @param staff
     * @param params
     * @param page
     * @return
     */
    PageListBase<Wave> queryWaves(Staff staff, WaveFilterParams params, Page page);

    List<Wave> queryPageList(Staff staff, WaveFilterParams params, Page page);

    /**
     * 创建拣选
     *
     * @param staff
     * @param waveId      波次ID
     * @param pickingCode 拣选编号（不能与未完成的重复）
     */
    Boolean validatePickingCode(Staff staff, Long waveId, String pickingCode) throws Exception;

    /**
     * 开始拣选
     *
     * @param staff
     * @param wavePicking
     */
    WavePicking beginWavePicking(Staff staff, WavePicking wavePicking);

    WavePicking addPickingCode(Staff staff, Long pickingId, String pickingCode, boolean repeatPickingCodeForce);

    /**
     * 分段开始拣选
     * @param staff
     * @param wavePicking
     */
    void subSectionBeginWavePicking(Staff staff, WavePicking wavePicking);

    /**
     * 更新拣选订单详情
     *
     * @param staff   staff
     * @param param   param
     * @param details update details
     * @return
     */
    WavePicking updateWavePicking(Staff staff, WavePickingParam param, List<WaveSortingDetail> details);

    /**
     * PDA拣选完成重新配货反向更新缺货数
     * @param staff
     * @param param
     * @param details
     */
    void reAllocateUpdateWaveSorting(Staff staff, WavePickingParam param, List<WaveSortingDetail> details);

    /**
     * 补拣放弃拣选清空
     * @param staff
     * @param param
     * @param details
     */
    void clearTaskSortingDetails(Staff staff, WavePickingParam param, List<WaveSortingDetail> details);

    /**
     * 更新拣选统计信息
     * @param staff
     * @param wavePicking
     * @return
     */
    WavePicking updateWavePickingStatistics(Staff staff, WavePicking wavePicking);

    /**
     * 接力拣选
     * @param staff
     * @param wavePicking
     * @return
     */
    WavePicking relayWavePicking(Staff staff, WavePicking wavePicking);

    /**
     * 完成拣选
     *
     * @param staff
     * @param wavePicking
     */
    WavePicking endWavePicking(Staff staff, WavePicking wavePicking);

    /**
     * 完成拣选(波次补拣)
     * @param staff
     * @param wavePicking
     * @param sids
     * @return
     */
    WavePicking endWavePickingPickAfter(Staff staff, WavePicking wavePicking, List<Long> sids);

    /**
     * 根据拣选编号查询拣选信息
     * 多品多件开启播种后置打印扫描拣选号需要判断波次状态是否播种完成
     *
     * @param staff
     * @param packingCode
     * @param scanType    扫描类型 1：后置打印   2：播种
     * @return
     */
    WavePicking scanByPickingCode(Staff staff, String packingCode, Integer scanType) throws Exception;

    /**
     * 根据波次Id查询拣选号
     *
     * @param staff
     * @param waveId       波次Id
     * @param enableStatus 拣选号状态
     * @return
     */
    WavePicking getPickingByWaveId(Staff staff, Long waveId, Integer enableStatus);


    WavePickContext getWaveAndPickingByWaveId(Staff staff, Long waveId);
    /**
     * 取消波次拣选
     * 回收已分配的拣选号，修改波次配货状态
     *
     * @param staff
     * @param waveId
     */
    int cancelWavePicking(Staff staff, Long waveId) throws Exception;

    /**
     * 根据拣选id查询订单和位置号
     *
     * @param staff
     * @param pickingId
     * @return
     */
    Map<Long, Long> querySidPositionNoMapByPickingId(Staff staff, Long pickingId);

    /**
     * 根据商家编码在当前波次中进行播种，返回位置号
     *
     * @param staff
     * @param param       扫描数量
     * @return
     */
    WavePickingScanResult waveSeed(Staff staff, WavePickingParam param) throws WaveScanException, TradeValidatorException;

    /**
     * 直接进行快速播种操作
     *
     * @param staff
     */
    void seedByDirect(Staff staff, WavePickingParam param);

    /**
     * 播种
     * @param staff
     * @param param
     * @return
     */
    WavePickingScanResult seed(Staff staff, WavePickingParam param);

    /**
     * 取消播种
     *
     * @param staff
     * @param waveId
     * @param pickingId
     */
    void cancelSeed(Staff staff, Long waveId, Long pickingId);

    void cancelSeeds(Staff staff, List<Long> waveIds, List<Long> pickingIds);

    /**
     * 查询波次播种订单明细
     * @param staff
     * @param waveId
     * @param pickingId
     * @param status 0:未完成 1:已完成 2:已打印
     * @param page @return
     */
    WavePageList<WaveSorting> queryWaveSeedTrades(Staff staff, Long waveId, Long pickingId, Integer status, Page page);

    WavePageList<WaveSorting> queryWavesSeedTrades(Staff staff, List<Long> waveIds, List<Long> pickingIds, Integer status, Page page);

    /**
     * 查询波次播种商品明细
     * @param staff
     * @param param
     * @param page @return
     */
    WavePageList<WaveSortingDetail> queryWaveSeedItems(Staff staff, WavePickingParam param, Page page);

    WavePageList<WaveSortingDetail> queryWaveSeedItemsSimple(Staff staff, WavePickingParam param, Page page);

    /**
     * 查询波次配置
     * @param staff
     * @return
     */
    WaveConfig queryWaveConfig(Staff staff);

    boolean updateWaveConfig(Staff staff, WaveConfig waveConfig);

    List<WaveSorting> queryByPickingId(Staff staff, Long pickingId);

    PageListBase<WaveTrade> queryWaveTradesByWaveId(Staff staff, WaveFilterParams params, Long waveId, Page page);

    /**
     * 验货登记波次
     * @param staff
     * @param param
     * @return
     */
    Wave registerWithWave(Staff staff, WavePickingParam param);

    /**
     * 根据运单号进行验货登记
     * @param staff
     * @param outSid
     * @return
     */
    Trade registerWithOutSid(Staff staff, String outSid);

    /**
     * 根据运单号进行验货登记(支持强制登记)
     * @param staff
     * @param outSid
     * @return
     */
    Trade registerWithOutSid(Staff staff, String outSid, boolean force);

    Trade registerWithOutSid(Staff staff, CheckGoodsParam param);

    List<WaveTrade> queryWaveTradeByParams(Staff staff, WaveTradeQueryParams params);

    /**
     * 完成验货登记 波次
     * @param staff
     * @param waveIds
     * @return
     */
    List<Wave> checkGoodsForWaves(Staff staff, List<Long> waveIds);

    /**
     * 完成验货登记 波次
     * @param staff
     * @return
     */
    List<Wave> checkGoodsForWaves(Staff staff, CheckGoodsParam param);

    /**
     * 完成验货登记 订单
     * @param staff
     * @param sids
     * @return
     */
    List<Trade> checkGoodsForTrades(Staff staff, List<Long> sids);

    /**
     * 完成验货登记 订单(支持强制登记)
     * @param staff
     * @param sids
     * @return
     */
    List<Trade> checkGoodsForTrades(Staff staff, List<Long> sids, boolean force);

    /**
     * 完成验货登记 订单(支持重复扫描)
     * @param staff
     * @return
     */
    List<Trade> checkGoodsForTrades(Staff staff, CheckGoodsParam param);

    /**
     * 根据时间查询验货登记记录
     *
     * @param staff
     * @param startTime
     * @param endTime
     * @return
     */
    List<TradeCheckGoods> queryTradeCheckGoodsByTime(Staff staff, Date startTime, Date endTime);

    List<WaveSorting> queryByPickingIds(Staff staff, List<Long> pickingIds);

    WavePicking queryWavePickingById(Staff staff, Long id);

    /**
     * 根据波次号查询波次详细信息
     * @param staff
     * @param waveIds
     * @return
     */
    List<Wave> queryFullWaveInfoByIds(Staff staff, List<Long> waveIds);

    Wave queryWaveById(Staff staff, Long waveId);

    List<Wave> queryWaveByIds(Staff staff, List<Long> waveIds);

    Wave queryWaveByCode(Staff staff, String code);

    List<WaveRule> queryWaveRuleByIds(Staff staff, List<Long> ids);

    List<WaveRule> queryWaveRules(Staff staff, WaveRule waveRuleQuery, Boolean needRuleCondition);

    List<AssoWaveItem> queryAssoWaveItemsByWaveId(Staff staff, Long waveId);

    /**
     * 批量修改波次备注
     * @param staff
     * @param params
     */
    void batchUpdateWaveRemark(Staff staff, WaveBatchUpdateRemarkParams params);

    /**
     * 创建波次
     * @param staff
     * @param waves
     * @return
     */
    List<Wave> createWaves(Staff staff, List<Wave> waves);

    /**
     * pda 扫码创建波次
     * @param staff
     * @param scanCode
     * @param warehouseId
     * @param minGenerateWaveNum
     * @param maxGenerateWaveNum
     * @return
     */
    Status pdaScanCreateWaves(Staff staff, String scanCode, Long warehouseId, Integer minGenerateWaveNum, Integer maxGenerateWaveNum,String ip);


    /**
     * 取消波次
     */
    void cancelWave(Staff staff, Long waveId);

    void cancelWave(Staff staff, Long waveId, String content);

    /**
     * 批量取消波次
     * @param staff
     * @param waveIds
     */
    void cancelWaves(Staff staff, List<Long> waveIds);


    void cancelWavesWithContent(Staff staff, List<Long> waveIds, String content);

    /**
     * 拣选中强制完成波次,调用时候注意是否适用,目前只针对采退波次
     */
    void forceFinishWave(Staff staff, Long waveId);

    /**
     * 创建波次，自动生成波次
     * @param staff
     * @param waves
     * @param autoCreate 自动生成的波次
     * @return
     */
    List<Wave> createWaves(Staff staff, List<Wave> waves, Boolean autoCreate);

    /**
     * 创建波次，自动生成波次
     * @param staff
     * @param waves
     * @param autoCreate 自动生成的波次
     * @param withPositionNo sorting是否根据位置号匹配
     * @return
     */
    List<Wave> createWaves(Staff staff, List<Wave> waves, Boolean autoCreate,Boolean withPositionNo);

    /**
     * 将订单移出波次
     *
     * @param staff
     * @param sids 订单号
     * @param opName 操作
     * @param operateEnum 波次日志记录类型
     * @return
     */
    List<Trade> removeSids(Staff staff, Long[] sids, String opName, WaveTraceOperateEnum operateEnum);

    /**
     * 将部分订单移出波次
     *
     * @param staff
     * @param sids 订单号
     * @param opName 操作
     * @return
     */
    List<Trade> removePartSids(Staff staff, Long[] sids, String opName);

    /**
     * 根据仓库和拣选类型查询分拣明细并根据商品分组
     *
     * @param staff
     * @param warehouseId 仓库id
     * @param waveStatus 波次状态
     * @param pickingType 拣选类型
     * @param sysItemIds 商品id
     * @param sysSkuIds 规格id
     * @return
     */
    List<WaveSortingDetail> queryGroupItemSortingDetails(Staff staff, Long warehouseId, Integer waveStatus, PickingType pickingType, List<Long> sysItemIds, List<Long> sysSkuIds);

    List<WaveSortingDetail> queryGroupItemSortingDetails(Staff staff, WaveSortingParam param);

    /**
     * 结束播种
     *
     * @param staff
     * @param waveId
     */
    List<Trade> seedFinish(Staff staff, Long waveId);

    List<Trade> seedFinish(Staff staff, List<Long> waveIds);

    /**
     * 删除 WaveSorting
     * @Title: deleteWaveSorting
     * 安排波次操作人员
     *
     * @param staff
     * @param waveIds
     * @param operatorId
     * @param assignType 安排类型，0：拣选 1：播种，2：全部
     */
    void assignWaveOperator(Staff staff, List<Long> waveIds, Long operatorId, Integer assignType);

    /**
     * 删除 WaveSorting
     * @Title: deleteWaveSorting
     * @param staff
     */
    void deleteWaveSortingByPickingId(Staff staff, Long pickingId);

    /**
     * 非订单波次放弃拣选清除
     * @param staff
     * @param pickingId
     */
    void clearSortingDetails(Staff staff, Long pickingId);

    /**
     * 分段拣选放弃拣选处理sortingDetails
     * @param staff
     * @param waveId
     * @param waveSortingDetailUpdateMaps (orderId, pickedNum)
     */
    void subSectionPickClearSortingDetail(Staff staff, Long waveId, Map<Long, Integer> waveSortingDetailUpdateMaps);

    Map<Long, Pair<Integer, Integer>> queryOrderUniqueCodeUsed(Staff staff, Long warehouseId);

    Map<Long, Pair<Integer, Integer>> queryOrderUniqueCodeUsedNew(Staff staff, Long warehouseId, boolean containItemUniqueCode);

    /**
     * 统计波次拣选员
     * @param staff
     * @param waveId
     */
    Wave countWavePickers(Staff staff, Long waveId);

    /**
     * 根据唯一码查询信息
     *
     * @param staff
     * @param uniqueCodes
     * @return
     */
    List<WaveUniqueCode> queryByUniqueCodes(Staff staff, List<String> uniqueCodes);

    /**
     * 根据条件查询订单唯一码
     * @param staff
     * @param params
     * @return
     */
    List<WaveUniqueCode> queryOrderUniqueCode(Staff staff, OrderUniqueCodeQueryParams params);

    /**
     * 根据唯一码进行收货
     *
     * @param staff
     * @param uniqueCodes
     * @param scanType
     * @return
     */
    List<WaveUniqueCode> receiveByUniqueCodes(Staff staff, List<String> uniqueCodes, Integer scanType) throws WaveScanException;

    /**
     * 查询波次中未打印订单,没有排序
     * @param staff
     * @param waveId
     * @return
     */
    List<Long> querySidsByWaveId4Print(Staff staff, Long waveId);

    /**
     * 根据波次ids，商品id查询波次分拣明细
     * @param staff
     * @param waveIds
     * @param sysItemId
     * @param sysSkuId
     * @return
     */
    List<WaveSortingDetail> queryWavePickingDetails(Staff staff, List<Long> waveIds, Long sysItemId, Long sysSkuId);

    List<WaveSortingDetail> queryWaveSortingDetailsByParam(Staff staff, WaveSortingParam param);

    /**
     * 创建唯一码
     * @param staff
     * @param details
     * @param wave
     * @param tradeConfig
     * @return
     */
    List<WaveUniqueCode> createUniqueCodeStock(Staff staff, List<WaveSortingDetail> details, Wave wave, TradeConfig tradeConfig);

    /**
     * 根据波次规则id查询波次规则
     * @param staff
     * @param waveRuleId
     * @return
     */
    WaveRule getWaveRuleByRuleId(Staff staff, Long waveRuleId);

    /**
     * 分段拣选完成，删除分段拣选人
     * @param staff
     * @param waveId
     */
    void deleteSubSectionPicker(Staff staff, Long waveId);

    /**
     * 通过波次短号/波次短号查询
     * @param staff
     * @param shortIds
     * @return <波次号，波次短号>
     */
    Map<Long, Long> queryWaveIdAndShortId(Staff staff, List<Long> shortIds, List<Long> waveIds);

    /**
     * 订单唯一码查询
     * @param staff
     * @param params
     * @return
     */
    PageListBase<WaveUniqueCode> querySupplierWaveUniqueCodeList(Staff staff, OrderUniqueCodeQueryParams params);

    /**
     * 订单唯一码查询
     * @param staff
     * @param params
     * @return
     */
    PageListBase<WaveUniqueCode> queryOrderUniqueCodeList(Staff staff, OrderUniqueCodeQueryParams params);

    /**
     * 订单唯一码批量收货，目前只有pda在用
     * @param staff
     * @param params
     * @throws WaveScanException
     * @return WaveScanException 返回收货异常的唯一码
     */
    List<WaveScanException> orderUniqueCodeBatchReceive(Staff staff, ReceiveUniqueCodeParams params);
    ReceiveUniqueCodeVO uniqueCodeBatchReceive(Staff staff, ReceiveUniqueCodeParams params);
    WaveUniqueCode checkReceiveOverTime(Staff staff, WaveUniqueCode code) throws WaveScanException;
    void checkPurchaseReturnReceive(Staff staff, WaveUniqueCode code) throws WaveScanException;

    /**
     * 唯一码收货校验，只有pda在用
     * @param staff
     * @param uniqueCode
     * @param opType 0：一次收货；1：二次收货
     * @return
     */
    WaveUniqueCode orderUniqueCodeReceiveCheck(Staff staff, String uniqueCode, Integer opType) throws WaveScanException;

    WaveUniqueCode orderUniqueCodeReceiveCheck(Staff staff, ReceiveUniqueCodeParams params) throws WaveScanException;

    WaveUniqueCode orderUniqueCodeReceiveCheck(Staff staff, String uniqueCode) throws WaveScanException;

    void updateWaveFailedLast(Staff staff, List<Long> waveIds, Integer failedLast);

    /**
     * 取消分段波次拣选
     * @param staff
     * @param wavePicking
     */
    void cancelSubWavePicking(Staff staff, WavePicking wavePicking);

    List<String> queryOrderUniqueCodeHotSaleKeys(Staff staff);

    OrderUniqueCodeConfig getOrderUniqueCodeConfig(Staff staff);

    void saveOrderUniqueConfig(Staff staff, OrderUniqueCodeConfig config);

    /**
     * 批量下架/取消唯一码
     * @param staff
     * @param ids 唯一码id串
     * @param opType 操作类型：（0：下架；1：取消）
     * @return 成功的条数
     */
    Integer orderUniqueCodeBatchOffShelfOrCancel(Staff staff, List<Long> ids, Integer opType);

    /**
     * 查询扫描打印记录
     * @param uniqueCodeScanRecord
     * @return
     */
    List<UniqueCodeScanRecord> queryUniqueCodeScanRecords(Staff staff, UniqueCodeScanRecord uniqueCodeScanRecord);

    /**
     * 商品 + 订单唯一码 查询
     * @param staff
     * @param params
     * @return
     */
    List<WaveUniqueCode> queryItemUniqueCodeByCondition(Staff staff, ItemUniqueCodeQueryParams params);

    /**
     * 根据唯一码查询(pda使用)
     */
    WaveUniqueCode getItemUniqueInfo(Staff staff, String uniqueCode) throws WaveScanException;

    /**
     * 商品唯一码 验货与绑定订单(pda使用)
     */
    void examine(Staff staff, Long sid, String uniqueCodes);

    /**
     * 获取所有的订单唯一码配置的公司
     * @param dbKey 分库号
     * @return
     */
    @Deprecated
    List<Long> getCompanyByOrderUniqueCodeConfig(Integer dbKey);

    /**
     * 商品唯一码查询（临时解决方案，供开放平台使用）
     * @param staff
     * @param params
     * @return
     */
    PageListBase<WaveUniqueCode> queryItemUniqueCodeList(Staff staff, ItemUniqueCodeParams params);

    /**
     * 查询缺拣信息
     * @param staff
     * @param sids
     * @return
     */
    List<WaveSortingDetail> queryShortageWaveSortingDetailBySids(Staff staff, List<Long> sids);

    /**
     * 查询爆款打印列表
     * @param staff
     * @param hotSaleParams
     * @return
     */
    HotSaleTradeVO queryHotSaleList(Staff staff, HotSaleParams hotSaleParams);

    /**
     * 查询爆款打印列表 - 简易返回
     * @param staff
     * @param hotSaleParams
     * @return <仓库Id，<快递模版Id, 商品keyList>>
     */
    Map<Long, Map<String, Set<String>>> queryHotSaleSimpleList(Staff staff, HotSaleParams hotSaleParams);

    /**
     * 获取生成爆款码商品信息和阈值
     *
     * @param staff
     * @param hotSaleParams
     * @return
     */
    Map<String, Long> queryHotTradeThresholdMap(Staff staff, HotSaleParams hotSaleParams);

    /**
     * 其他功能更新唯一码回调
     * @param staff
     * @param asUniqueCodeBatchDTO
     */
    void updateAsUniqueCode(Staff staff, AsUniqueCodeBatchDTO asUniqueCodeBatchDTO);

    /**
     * 查询商品唯一码分页
     * @param staff
     * @param params
     * @return
     */
    PageListBase<WaveUniqueCode> queryItemUniqueCodePageList(Staff staff, ItemUniqueCodeQueryParams params);
    /**
     * 唯一码收货
     */
    WaveUniqueCode receiveUniqueCode(Staff staff, String uniqueCode, Integer source, boolean rescan) throws WaveScanException;

    List<WavePickerInfo> queryPickingParams(Staff staff, WaveFilterParams params, Page page);

    /**
     * 查询单件缺货商品数量
     * @param staff
     * @param warehouseId
     * @param sysItemIds
     * @param sysSkuIds
     * @return
     */
    Map<String, Integer> queryFastInOutTradeCount(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds);

    Map<String, Integer> queryFastInOutTradeCount(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds, FastInOutTradeQueryParam param);

    WaveItem queryFastInOutMultiTrade(Staff staff, Long warehouseId, List<FastInOutSimpleItemVO> simpleItemVOS, FastInOutTradeQueryParam param);

    /**
     * 查询单件缺货订单sid
     * @param staff
     * @param warehouseId
     * @param sysItemIds
     * @param sysSkuIds
     * @return <商品key，缺货订单sid列表>
     */
    Map<String, List<Long>> queryFastInOutTradeList(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds);

    Map<String, List<Long>> queryFastInOutTradeList(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds, FastInOutTradeQueryParam param);

    /**
     * 新增、删除 订单快销标签
     * @param staff
     * @param needAddTagSids
     * @param needRemoveTagSids
     */
    void modifyTradeTag4FastInOut(Staff staff, List<Long> needAddTagSids, List<Long> needRemoveTagSids);

    /**
     * 唯一码批量修改
     * @param staff
     * @param waveUniqueCodes
     */
    void batchUpdateUniqueCodes(Staff staff, List<WaveUniqueCode> waveUniqueCodes);

    List<WaveUniqueCode> queryWaveUniqueCodeByCondition(Staff staff, WaveUniqueCodeParams waveUniqueCodeParams);

    void updateWaveUniqueCode(Staff staff, List<WaveUniqueCode> waveUniqueCodes);
    /**
     * 通过运单号获取波次信息
     * @param staff
     * @param outSid 运单号
     * @param forceRegister 是否支持强制(特定异常)
     * @return
     */
    Wave getWaveInfoByOutSid(Staff staff, String outSid, boolean forceRegister, boolean repeatable);

    List<CustomWorkloadSupplement> queryListByCustom(Staff staff, CustomWorkloadSupplement customWorkloadSupplement);

    WorkloadSupplementQueryResult queryInfo(Staff staff, WorkloadSupplementQueryParams params) throws WaveScanException;

    List<Trade> finish(Staff staff, WorkloadSupplementQueryParams params);

    Map<String, Object> finishSupplement(Staff staff, WorkloadSupplementQueryParams params);

    Wave queryWaveInfoByCustom(Staff staff, WorkloadSupplementQueryParams params);

    Wave finishWorkloadSupplement(Staff staff, String firstOutSid, String lastOutSid, WorkloadSupplementQueryParams params);


    /**
     * 拣选号转波次号
     * @param staff
     * @param pickingCodes
     * @return
     */
    List<Long> queryWaveIdListByPickingCodes(Staff staff, List<String> pickingCodes);

    Long countOrderUniqueCode(Staff staff, OrderUniqueCodeQueryParams params);

    /**
     * 验货登记时填充波次中的异常订单列表
     * KMERP-79844
     * @param staff
     * @param waveId
     * @param page
     */
    PageListBase<WaveTrade> queryExcepAndRemoveWaveTradeList(Staff staff, Long waveId, Page page);

    /**
     * 验货登记时填充波次中的异常商品列表
     * KMERP-79844
     * @param staff
     * @param waveId
     * @param page
     */
    PageListBase<WaveItem> queryExcepAndRemoveWaveOrderList(Staff staff, Long waveId, Page page);

    /**
     * waveSorting 所有状态查询
     */
    List<WaveSorting> queryAllStatusWaveSortingsByWaveId(Staff staff, Long waveId);

    List<Map<String, Object>> wavePickByHand(Staff staff, List<WavePicking> wavePickings);

    WaveTradeCondition getWaveTradeConditionByName(Staff staff, String name);

    void updateWaveSeedTime(Staff staff, Long waveId, Date time, Integer type);

    List<WaveSorting> queryByWaveIdsAndSids(Staff staff, List<Long> sids, List<Long> waveIds);

    /**
     * 根据波次id查询分拣信息(开放平台)
     *
     * @param staff
     * @param waveId
     * @return
     */
    List<WaveSorting> queryWaveSortingsByWaveIdForOpen(Staff staff, Long waveId);

    /**
     * 根据波次id播种(开放平台)
     *
     * @return
     */
    WaveSeedForOpenResult seedForOpen(Staff staff, String jobNum, WavePickingParam param);
    /**
     * 获取波次自动创建规则
     */
    List<WaveAutoCreateRuleDTO> getWaveAutoCreateRuleByGroupIds(Staff staff, List<Long> waveGroupIds, Integer autoWaveType);

    /**
     * 保存波次自动创建规则
     */
    void saveWaveAutoCreateRule(Staff staff, WaveAutoCreateRuleDTO waveAutoCreateRuleDTO);

    Integer getPackNumByStaffId(Staff staff);

    /**
     * 查询各个标签数量
     */
    ShareUrlVO queryShareTabTotal(Staff staff, Long supplierId);
    Long countItemUniqueCode(Staff staff, ItemUniqueCodeQueryParams params);

    void createPackmaLogisticsOrder(Staff staff, WavePackmaItemParam wavePackmaItemParam);

    List<LogisticsOrderDetail> queryAllPackmaDetail(Staff staff, LogisticsOrderQueryParams queryParams);

    /**
     * 查询订单的包材
     * @param staff
     * @param sids
     * @return 返回值结构 <sid, List<包材>>
     */
    Map<Long, List<LogisticsOrderDetail>> queryPackageMaterialBySids(Staff staff, List<Long> sids);

    void handlePackmaCancelConsign(Staff staff, List<Long> orderIds);

    List<LogisticsOrder> queryLogisticsOrderAndDetail(Staff staff, LogisticsOrderQueryParams queryParams);


    List<Long> querySidsByWaveIds(Staff staff, List<Long> waveIds, WaveFilterParams waveFilterParams);

    /**
     * 跨境印花波次详情
     * @return
     */
    CrossBorderWavePrintInfo queryCrossBorderWavePrintInfo(Staff staff, Long waveId);

    /**
     * 查询waveSortingDetail，支持过滤免检免验
     * @param staff
     * @param waveIds
     * @param pickingIds
     * @param pickAndCheckList
     * @return
     */
    List<WaveSortingDetail> queryDetailsByWaveId(Staff staff, List<Long> waveIds, List<Long> pickingIds, List<Integer> pickAndCheckList);

    Integer countPerformanceBySidAllStatus(Staff staff, Long sid, Integer enableStatus);

    /**
     * 修改波次标签
     * @param staff
     * @param tagIds
     * @param waveIds
     * @param operateType 0:增加标签；1:删除标签
     */
    void modifyWaveTag(Staff staff, List<Long> tagIds, List<Long> waveIds, int operateType);


    boolean asyncFinishWave(Staff staff, List<Long> waveIds,String source);



    PickingCart queryPickingCart(Staff staff,Long waveId);


    PickingCartType queryPickingCartType(Staff staff,Long pickingCartTypeId);



    /**
     * 包材修正
     *
     * @param staff
     * @param simplePackmas
     * @param logisticsOrderVOS
     * @param aiPackmaOpEnum
     * @param packmaMap
     */
    void packmaModify(Staff staff, List<SimplePackma> simplePackmas, List<? extends LogisticsOrder> logisticsOrderVOS, AiPackmaOpEnum aiPackmaOpEnum, Map<Long, List<LogisticsOrderDetail>> oldpackmaMap);


    /**
     * 查询订单的包材
     * @param staff
     * @param sids
     * @return 返回值结构 <sid, List<包材>>
     */
    Map<Long, List<SimplePackmaVO>> queryPackmaInfo(Staff staff, List<Long> sids);

}

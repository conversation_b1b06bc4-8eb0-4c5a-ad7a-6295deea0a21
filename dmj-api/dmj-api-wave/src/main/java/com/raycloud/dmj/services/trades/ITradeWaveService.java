package com.raycloud.dmj.services.trades;

import com.raycloud.dmj.domain.*;
import com.raycloud.cache.CacheException;
import com.raycloud.dmj.domain.DateRange;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageListBase;
import com.raycloud.dmj.domain.TradeValidator;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.enums.WaybillGetOperationEnum;
import com.raycloud.dmj.domain.item.DmjItems;
import com.raycloud.dmj.domain.item.ItemQueryParams;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradePerformanceOptLog;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.model.*;
import com.raycloud.dmj.domain.wave.params.WaveBatchUpdateRemarkParams;
import com.raycloud.dmj.domain.wms.vo.FastInOutSimpleItemVO;
import com.raycloud.erp.buffer.model.ErpBuffer;
import org.apache.commons.lang3.tuple.Pair;
import com.raycloud.dmj.domain.wms.GoodsSectionOrderRecord;
import com.raycloud.dmj.domain.sku.DmjSku;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by guzy on 16/10/26.
 */
public interface ITradeWaveService {

    /**
     * 查询波次订单sid
     *
     * @param staff
     * @param waveId 波次号
     * @param params
     * @param page   分页
     * @return
     */
    List<Long> querySidsByWaveId(Staff staff, Long waveId, WaveFilterParams params, Page page);

    /**
     * 批量查询波次订单sid
     *
     * @param staff
     * @param waveIds
     * @param params
     * @param page
     * @return
     */
    List<Long> querySidsByWaveIds(Staff staff, List<Long> waveIds, WaveFilterParams params, Page page);

    /**
     * 填充波次模板名称
     *
     * @param staff
     * @param waves
     */
    void fillTemplateName(Staff staff, List<Wave> waves);

    /**
     * 填充订单模板名称
     *
     * @param staff
     * @param trades
     */
    void fillTradeTemplateName(Staff staff, List<Trade> trades);

    /**
     * 根据波次id查询未打印 sid列表
     *
     * @param staff
     * @param waveId
     * @return
     */
    List<Long> querySidsByWaveId4Print(Staff staff, Long waveId, boolean printed);

    List<Long> querySidsByWaveIds4Print(Staff staff, List<Long> waveIds, boolean printed);

    /**
     * 根据波次id、是否异常 查询 sid列表
     * @param staff
     * @param waveId 波次id
     * @param isExcep 是否异常
     * @return
     */
    List<Long> querySidsByWaveIdAndExcep(Staff staff, Long waveId, Integer isExcep);

    /**
     * 根据波次id查询 sid列表
     *
     * @param staff
     * @param waveId
     * @return
     */
    List<Long> querySidsByWaveId(Staff staff, Long waveId);

    List<Long> querySidsByWaveId(Staff staff, Long waveId, Long warehouseId, boolean groupByTrade);

    /**
     * 根据波次id查询 待包装验货的无异常 sid列表
     *
     * @param staff
     * @param waveId
     * @return
     */
    List<Long> querySids4PackByWaveId(Staff staff, Long waveId);

    /**
     * 根据波次ids查询 sid列表
     *
     * @param staff
     * @param waveIds
     * @return
     */
    List<Long> querySidsByWaveIds(Staff staff, List<Long> waveIds);

    /**
     * @param staff
     * @param waveId
     * @return
     */
    Integer getWaveUnPickNum(Staff staff, Long waveId);

    /**
     * 根据波次id查订单信息
     * @param staff
     * @param waveIds
     * @return
     */
    List<Trade> queryTradesByWaveIds(Staff staff, List<Long> waveIds);

    /**
     * 根据波次id查询订单数量
     *
     * @param staff
     * @param waveId
     * @param params
     * @return
     */
    Long queryWaveTradeCount(Staff staff, Long waveId, WaveFilterParams params);

    /**
     * 根据waveIds查询波次订单
     *
     * @param staff
     * @param waveIds
     * @return
     */
    List<Trade> queryTradesByWaveIds4Print(Staff staff, List<Long> waveIds, int maxSize ,boolean showdetail, boolean isWaveAutoPrint);

    List<Trade> queryTradesByWaveIds4Print(Staff staff, List<Long> waveIds, int maxSize ,boolean showdetail, boolean isWaveAutoPrint, boolean fromController);

    void cancelInsufficientByWave(Staff staff, List<Long> waveIds);

    void cancelInsufficientBySid(Staff staff, List<Long> sids);

    /**
     * 查询波次中未打印订单,没有排序
     */
    List<Long> querySidsByWaveId4Print(Staff staff, Long waveId);


    /**
     * 保存波次规则
     *
     * @param staff
     * @param waveRule
     */
    void saveWaveRule(Staff staff, WaveRule waveRule);
    /**
     * 批量保存波次规则
     *
     * @param staff
     * @param waveRules
     */
    Map<String, List<WaveRule>> batchSaveRules(Staff staff, WaveRules waveRules);

    /**
     * 查询规则列表
     *
     * @param staff
     * @return
     */
    List<WaveRule> queryRules(Staff staff);

    /**
     * 查询规则列表
     *
     * @param staff
     * @param waveRuleQuery
     * @return
     * @Title: queryRules
     */
    List<WaveRule> queryRules(Staff staff, WaveRule waveRuleQuery);

    /**
     * 删除规则
     *
     * @param staff
     * @param id
     * @return
     */
    int deleteRule(Staff staff, Long id);

    /**
     * 根据波次规则以及仓库id查询波次分组
     *
     * @param staff
     * @param ruleId      波次规则 id
     * @param warehouseId 仓库id
     * @return
     */
    @Deprecated
    List<Wave> queryWaveByRule(Staff staff, Long ruleId, Long warehouseId);

    List<Wave> queryWaveByRule(Staff staff, Long ruleId, Long warehouseId, DateRange payTimeRange);

    List<Wave> queryWaveByRule(Staff staff, Long ruleId, Long warehouseId, DateRange payTimeRange, WaveRule waveRule);

    /**
     * 查询满足条件的波次订单关联记录列表
     *
     * @param staff
     * @param wave   波次
     * @param ruleId 波次规则 id
     * @return
     */
    PageListBase<AssoWaveTrade> queryTrades(Staff staff, Wave wave, Long ruleId, Page page);

    /**
     * 查询未生成波次的订单
     *
     * @param staff
     * @param page
     * @param querySids
     * @return
     */
    PageListBase<Long> queryNotInWaveSids(Staff staff, Long warehouseId, List<Long> querySids, Page page);

    PageListBase<Long> queryNotInWaveSids(Staff staff, Long warehouseId, List<Long> querySids, DateRange payTimeRange, Integer isExcep, List<Long> userIds, Page page);

    PageListBase<Long> queryNotInWaveSids(Staff staff, NotInWaveTradesQueryParams params, Page page);

    /**
     * 创建波次
     *
     * @param staff
     * @param waves
     * @return
     */
    List<Wave> createWaves(Staff staff, List<Wave> waves);

    /**
     * 创建波次，系统自动
     *
     * @param staff
     * @param waves
     * @param autoCreate
     * @return
     */
    List<Wave> createWaves(Staff staff, List<Wave> waves, Boolean autoCreate);


    Status pdaScanCreateWaves(Staff staff, String scanCode, Long warehouseId, Integer minGenerateWaveNum, Integer maxGenerateWaveNum,String ip);


    /**
     * 创建波次，系统自动
     *
     * @param staff
     * @param waves
     * @param autoCreate
     * @return
     */
    List<Wave> createWaves(Staff staff, List<Wave> waves, Boolean autoCreate,Boolean withPositionNo);

    /**
     * 根据sids 删除波次订单记录，并移除没有订单信息的波次
     * 如果是普通订单和拆分，根据sid进行移出 如果是合单的一部分，不要调用该方法，否则会全部移出波次
     *
     * @param staff
     * @param sids
     * @param opName 操作名称
     */
    List<Trade> removeSids(Staff staff, Long[] sids, String opName);

    List<Trade> removeSids(Staff staff, Long[] sids, String opName, WaveTraceOperateEnum operateEnum);

    /**
     * 根据部分的sids移出波次，一般是合单的一部分完成或关闭，不需要整个合单都移出
     *
     * @param staff
     * @param sids
     * @param opName
     * @return
     */
    List<Trade> removePartSids(Staff staff, Long[] sids, String opName);

    /**
     * 更新波次是否可以完成
     *
     * @param staff
     * @param waveId
     * @param sids
     * @param removed
     * @param openPackageExamine
     */
    void updateWaveFinished(Staff staff, Long waveId, List<Long> sids, boolean removed, boolean openPackageExamine);

    void updateWaveFinished(Staff staff, Long waveId, List<Long> sids, boolean removed, boolean openPackageExamine, String opName);

    /**
     * 取消拣选增加商品日志
     * @param staff
     * @param waves
     */
    void addItemTraceLogByCancelPick(Staff staff, List<Wave> waves);

    /**
     * 根据波次id查询波次统计信息
     *
     * @param staff
     * @param waveId
     * @return
     */
    Wave queryWaveStatistic(Staff staff, Long waveId, Boolean printed);

    /**
     * 生成波次页面的订单统计信息 + 波次统计信息
     *
     * @param staff
     * @param warehouseId
     * @return
     */
    @Deprecated
    Map<String, Object> waveStatsWithTrades(Staff staff, Long warehouseId, Long waveRuleGroupId);

    Map<String, Object> waveStatsWithTrades(Staff staff, Long warehouseId, Long waveRuleGroupId, DateRange dateRange);


    ProgressData waveStatsProgress(Staff staff, Long warehouseId, Long waveRuleGroupId, DateRange dateRange) throws CacheException;


    ProgressData waveNumStats(Staff staff, Long warehouseId, DateRange dateRange) throws CacheException;


    /**
     * 查询波次列表（只包含{@link Wave#STATUS_CREATED}状态的波次）
     *
     * @param staff
     * @param params
     * @param page
     * @return
     */
    PageListBase<Wave> queryCreatedWaves(Staff staff, WaveFilterParams params, Page page);

    PdaIndexNotifyResult queryPdaIndexNotify(Staff staff, PdaIndexNotifyParams params);

    /**
     * 查询波次列表
     *
     * @param staff
     * @param params
     * @param page
     * @return
     */
    PageListBase<Wave> queryWaves(Staff staff, WaveFilterParams params, Page page);

    /**
     * 查询波次列表
     *
     * @param staff
     * @param params
     * @param page
     * @return
     *//*
    PageListBase<Wave> queryWaves(Staff staff, WaveFilterParams params, Page page);*/

    /**
     * 查询波次
     *
     * @param staff
     * @param waveFilterParams
     * @return
     */
    Wave queryWave(Staff staff, WaveFilterParams waveFilterParams);

    /**
     * 查询波次记录
     *
     * @param staff
     * @param params
     * @param page
     * @return
     */
    PageListBase<Wave> queryWavesLog(Staff staff, WaveFilterParams params, Page page);

    /**
     * 更新波次的状态
     *
     * @param staff
     * @param wave  波次
     */
    void updateWaveStatus(Staff staff, Wave wave);

    /**
     * 更新波次拣货单打印状态
     * @param staff
     * @param waves
     */
    void updateWavePickListPrintStatus(Staff staff, List<Wave> waves);

    /**
     * 修改波次备注
     *
     * @param staff
     * @param waves
     */
    void updateWaveRemakes(Staff staff, List<Wave> waves);

    /**
     * 批量修改波次备注
     * @param staff
     * @param params
     */
    void batchUpdateWaveRemark(Staff staff, WaveBatchUpdateRemarkParams params);

    /**
     * 放弃拣选
     * @param staff
     * @param waveIds
     * @param cancelWave 是否取消波次
     */
    void giveUpPick(Staff staff, List<Long> waveIds, boolean cancelWave);

    /**
     * 修改波次配货状态
     *
     * @param staff
     * @param waveId
     * @param distributionStatus
     */
    void updateDistributionStatus(Staff staff, Long waveId, Integer distributionStatus);

    void batchUpdateDistributionStatus(Staff staff, List<Long> waveId, Integer distributionStatus);

    /**
     * 修改获取面单号状态
     *
     * @param staff
     * @param waveId
     * @param outsidStatus
     */
    void updateOutsidStatus(Staff staff, Long waveId, Integer outsidStatus);

    /**
     * 完成验货登记
     *
     * @param staff
     * @param waves
     */
    void updateCheckGoodsFinished(Staff staff, List<Wave> waves);

    /**
     * 取消波次
     *
     * @param staff
     * @param waveId 波次id
     */
    void cancelWave(Staff staff, Long waveId);

    void cancelWave(Staff staff, Long waveId, String content);

    /**
     *
     */
    void forceFinishWave(Staff staff, Long waveId);

    /**
     * 取消波次 批量
     * @param staff
     * @param waveIds
     * @param content
     */
    void cancelWavesWithContent(Staff staff, List<Long> waveIds, String content);

    /**
     * 取消波次 批量
     *  @param staff
     * @param waveIds
     * @param progressData
     */
    void cancelWaves(Staff staff, List<Long> waveIds, ProgressData progressData);

    /**
     * 波次取消后事件，异步处理wavesorting、wavetrade
     * @param staff
     * @param waveIds
     */
    void waveCancelAfter(Staff staff, List<Long> waveIds);

    /**
     * 批量删除波次规则
     *
     * @param staff
     * @param ids   波次规则ids
     */
    void deleteRules(Staff staff, List<Long> ids);

    /**
     * 根据ids 查询波次规则列表
     *
     * @param staff
     * @param ids
     * @return
     */
    List<WaveRule> queryRulesByIds(Staff staff, List<Long> ids);

    /**
     * 根据id查询波次
     *
     * @param staff
     * @param waveId 波次id
     * @param fillTemplateName 是否填充模板名称
     * @return
     */
    Wave queryWaveById(Staff staff, Long waveId, Boolean fillTemplateName);

    /**
     * 根据id查询波次
     *
     * @param staff
     * @param waveId 波次id
     * @return
     */
    Wave queryWaveById(Staff staff, Long waveId);

    /**
     * 根据波次号或者拣选号查询波次
     * @param staff
     * @param code
     * @return
     */
    Wave queryByCode(Staff staff, String code);

    /**
     * 根据ids查询波次
     *
     * @param staff
     * @param ids
     * @return
     */
    List<Wave> queryWaveByIds(Staff staff, Long[] ids);

    /**
     * 根据ids查询波次，needRemovedSidCount是否需要踢出波次订单数量
     * @param staff
     * @param ids
     * @param needRemovedSidCount
     * @return
     */
    List<Wave> queryWaveByIds(Staff staff, Long[] ids, boolean needRemovedSidCount);

    /**
     * 根据ids查询波次
     * @Title: queryWaveByIds
     * @param staff
     * @param ids
     * @param isFillInfo
     * @return
     */
    List<Wave> queryWaveByParam(Staff staff, WaveQueryFilterParam queryParam, Boolean isFillInfo);

    /**
     * 查询订单的拣选员
     * @param staff
     * @param sids
     * @param waveIds
     * @return
     */
    Map<Long, WaveSorting> queryPickerNamesBySid(Staff staff, List<Long> sids, List<Long> waveIds);

    Map<Long, TradePerformanceOptLog> queryTradePackLogBySid(Staff staff, List<Long> sids);

    /**
     * 根据波次号查询波次详细信息
     *
     * @param staff
     * @param waveIds
     * @return
     */
    List<Wave> queryFullWaveInfoByIds(Staff staff, List<Long> waveIds);

    /**
     * 根据波次号获取波次信息
     * @param staff
     * @param waveIds
     * @param needPickerSorterInfo
     * @return
     */
    List<Wave> queryWaveInfoByIds(Staff staff, List<Long> waveIds, boolean needPickerSorterInfo);

    /**
     * 检查是否有配货中或配货完成的波次
     *
     * @param staff
     * @return
     */
    boolean checkHasDistributionWaves(Staff staff, String distributionStatus);

    boolean checkWaveTypeQueryFeature(Staff staff);

    /**
     * 删除已经发货的明细
     *
     * @param staff
     */
    @Deprecated
    void deleteAfterSendGoodsDetails(Staff staff);

    void deleteAfterSendGoodsDetails(Staff staff, List<Long> warehouseIds);

    /**
     * 查询波次中订单分页列表 波次记录
     *
     * @param staff
     * @param params
     * @param page   @return
     */
    PageListBase<WaveTrade> queryWaveTradeLogPageList(Staff staff, WaveTradeQueryParams params, Page page);

    /**
     * 根据波次id获取波次中订单
     *
     * @param staff
     * @param waveId
     * @return
     */
    List<WaveTrade> queryWaveTradesByWaveId(Staff staff, Long waveId);

    /**
     * 根据波次ids获取波次中订单
     *
     * @param staff
     * @param waveIds
     * @return
     */
    List<WaveTrade> queryWaveTradesByWaveIds(Staff staff, List<Long> waveIds);

    /**
     * 根据商品id获取踢出波次订单
     *
     * @param staff
     * @return
     */
    List<WaveTrade> queryRemovedWaveTradeBySysItemSkuId(Staff staff, WavePickingParam param);

    List<WaveTrade> queryRemovedWaveTrade(Staff staff, WavePickingParam param);

    /**
     * 根据商品维度查询波次拣选的信息以及位置数量对应关系
     *
     * @param staff
     * @param waveId
     * @param queryType
     * @return
     */
    List<WaveItem> queryWavePickingDetailsByItem(Staff staff, Long waveId, Integer queryType);

    /**
     * 根据波次ids，商品id查询波次分拣明细
     * @param staff
     * @param waveIds
     * @param sysItemId
     * @param sysSkuId
     * @return
     */
    List<WaveSortingDetail> queryWavePickingDetails(Staff staff, List<Long> waveIds, Long sysItemId, Long sysSkuId);

    List<WaveSortingDetail> queryWaveSortingDetailsByParam(Staff staff, WaveSortingParam param);

    /**
     * 查询波次商品信息
     * @Title: queryWaveItemByPrintStatus
     * @param staff
     * @param waveId
     * @return
     */
    List<WaveSortingDetail> queryWaveItemByWaveId(Staff staff, Long waveId, Integer showType, String outerId, boolean usePostStatus);

    /**
     * 检查关闭的波次订单
     *
     * @param staff
     */
    Map<Long, List<Long>> checkCloseWaveTrades(Staff staff, Boolean fix);

    /**
     * 查询波次记录 给小程序
     *
     * @param staff
     * @param params
     * @param page
     * @return
     */
    PageListBase<Wave> queryWavesLog4Wuxian(Staff staff, WaveFilterParams params, Page page);

    /**
     * 获取该公司下所有的波次规则
     *
     * @param staff
     * @return
     */
    List<WaveRule> queryList4Wuxian(Staff staff);

    /**
     * 根据波次号查询波次订单商品总数
     *
     * @param staff
     * @param warehouseId
     * @param waveIds
     * @return
     */
    Integer queryItemNumByWaveIds(Staff staff, Long warehouseId, List<Long> waveIds);

    /**
     * 重新分配波次库区
     *
     * @param staff
     * @param warehouseId
     * @param waveIds     @return
     */
    WaveGoodsChangeResult changeWaveStockRegion(Staff staff, Long warehouseId, List<Long> waveIds, Long toStockRegionId);

    /**
     * 修改波次的库区
     *
     * @param staff
     * @param waves
     */
    void updateWaveSectionAreas(Staff staff, List<Wave> waves);

    void updateWaveSectionAreasByBuffer(Staff staff, List<ErpBuffer> list);

    List<Wave> queryPageList(Staff staff, WaveFilterParams params, Page page);

    List<AssoWaveItem> queryAssoWaveItemsByWaveId(Staff staff, Long waveId);

    List<AssoWaveItem> queryAssoWaveItemsByWaveIds(Staff staff, List<Long> waveIds);

    /**
     * 安排波次操作人员
     *
     * @param staff
     * @param waveIds
     * @param operatorId
     * @param assignType 安排类型，0：拣选 1：播种，2：全部
     */
    void assignWaveOperator(Staff staff, List<Long> waveIds, Long operatorId, Integer assignType);

    void assignWaveOperator(Staff staff, List<Long> waveIds, Long operatorId, Integer assignType, Map<Long, List<String>> cannotAssignPickReason);

    /**
     * 搜索波次详情当中的商品
     */
    DmjItems queryAssignItemList(Staff staff, ItemQueryParams queryParams, Long waveRuleId);

    PageListBase<DmjSku> queryAssignSkuList(Staff staff, ItemQueryParams itemQueryParams, Long waveRuleId, Integer type);

    /**
     * 根据波次id查询波次 pda 接力 拣选 记录
     */
    List<WavePicker> getWavePickerByWaveId(Staff staff, Long waveId);

    /**
     * 查询波次分组列表
     *
     * @param staff
     * @return
     * @Title: queryRuleGroups
     */
    List<WaveRuleGroup> queryRuleGroups(Staff staff, WaveRuleGroup waveRuleGroup);

    /**
     * 查询波次分组列表并判断是否需要返回未分组
     *
     * @param staff
     * @return
     * @Title: queryRuleGroups
     */
    List<WaveRuleGroup> queryRuleGroups(Staff staff, WaveRuleGroup waveRuleGroup, boolean ungrouped);

    /**
     * 根据ids 查询波次规则分组列表
     *
     * @param staff
     * @param ids
     * @return
     * @Title: queryRuleGroupsByIds
     */
    List<WaveRuleGroup> queryRuleGroupsByIds(Staff staff, List<Long> ids);

    /**
     * 新增或修改波次分组
     *
     * @param staff
     * @param waveRuleGroup
     * @Title: saveWaveRuleGroups
     */
    void saveWaveRuleGroup(Staff staff, WaveRuleGroup waveRuleGroup);

    /**
     * 新增或修改波次分组列表
     * @param staff
     * @param waveRuleGroupList
     */
    void saveWaveRuleGroupList(Staff staff, List<WaveRuleGroup> waveRuleGroupList);

    /**
     * 删除波次分组
     *
     * @param staff
     * @param id
     * @return
     * @Title: deleteWaveRuleGroup
     */
    int deleteWaveRuleGroup(Staff staff, Long id);


    /**
     * 结束播种
     *
     * @param staff
     * @param waveId
     * @return
     * @Title: seedFinish
     */
    List<Trade> seedFinish(Staff staff, Long waveId);

    /**
     * 批量结束播种
     *
     * @param staff
     * @param waveIds
     * @return
     */
    List<Trade> batchSeedFinish(Staff staff, List<Long> waveIds);

    /**
     * 获取打印需要的 拣选人信息
     */
    Map<String, String> getWavePickers(Staff staff, Wave wave);

    /**
     * 统计波次拣选员
     *
     * @param staff
     * @param waveId
     */
    Wave countWavePickers(Staff staff, Long waveId);


    /**
     * 波次管理界面
     */
    PageListBase<Wave> queryAllWaves(Staff staff, WaveFilterParams params, Page page);


    /**
     * 删除波次规则信息缓存
     *
     * @param staff
     * @param ruleGroupId
     * @param warehouseIdList
     * @Title: deleteWaveRuleCache
     */
    void deleteWaveRuleCache(Staff staff, Long ruleGroupId, List<Long> warehouseIdList);

    /**
     * 删除波次规则信息缓存
     *
     * @param staff
     * @param ruleGroupId
     * @Title: deleteWaveRuleCache
     */
    void deleteWaveRuleCache(Staff staff, Long ruleGroupId);

    List<Long> querySidsByWaveIds(Staff staff, List<Long> waveIds, WaveFilterParams waveFilterParams);

    /**
     * 分配订单的库区类型
     *
     * @param staff           staff
     * @param sids            sids
     * @param stockRegionType stockRegionType
     */
    List<Long> allocateTradeStockRegion(Staff staff, List<Long> sids, Integer stockRegionType);

    /**
     * 波次用 异步 获取单号 都用审核后获取单号的事件
     */
    void getWaybillCode(Staff staff, List<Long> waveIds, WaybillGetOperationEnum operationEnum);

    void getWaybillCode(Staff staff, List<Long> waveIds, WaybillGetOperationEnum operationEnum, ProgressEnum progress);

    /**
     * 获取没有运单号的波次
     *
     * @param staff
     * @param waveIds
     * @return
     */
    List<Long> queryNoOutSidWave(Staff staff, List<Long> waveIds);

    List<Long> queryMergeSidBySid(Staff staff, List<Long> sids);

    /**
     * 查询所有merge订单通父trade
     * @param staff
     * @param list
     * @return
     */
    List<TbTrade> queryMergeTradesByParentTrades(Staff staff, List<Trade> list);

    /**
     * 根据一品多码查询商家编码
     * @param staff
     * @param multiCodes
     * @return
     */
    Map<String, String> queryOuterIdByMultiCodes(Staff staff, List<String> multiCodes);

    /**
     * 根据商品信息查询一品多码
     * @param staff
     * @param pairs
     * @return
     */
    Map<Pair<Long, Long>, List<String>> queryMultiCodeByItemInfo(Staff staff, List<Pair<Long, Long>> pairs);

    List<WaveItemPosition> queryWaveItemPositionWithNum(Staff staff, List<Long> waveIds, List<Long> sids, List<WaveItem> waveItems, Integer stockStatus, Integer suitType, List<String> uniqueCodes);

    WavePageList<WaveItem> queryWavePrintItemPageList(Staff staff, QueryWaveItemPageParams params);

    /**
     * 校验是否存在拣选完成但是未完结的波次
     * @param staff
     * @return
     */
    boolean switchPickPerformanceValidate(Staff staff);


    /**
     * 通过波次短号/波次短号查询
     * @param staff
     * @param shortIds
     * @return <波次号，波次短号>
     */
    Map<Long, Long> queryWaveIdAndShortId(Staff staff, List<Long> shortIds, List<Long> waveIds);

    /**
     * 查询波次商品
     * @param staff
     * @param waveIds
     * @return
     */
    List<WaveItem> queryWavesItems(Staff staff, List<Long> waveIds);

    /**
     * 查询波次商品(不含套件明细商品)
     * @param staff
     * @param waveIds
     * @return
     */
    List<WaveItem> queryWavesUnCombineItems(Staff staff, List<Long> waveIds);

    /**
     * 查询波次商品
     * @param staff
     * @param waveIds
     */
    List<WaveItemGroupVO> queryWavesItemsGroup(Staff staff, String waveIds);

    /**
     * 更新波次领取失败标识
     * @param staff
     * @param waveIds
     * @param failedLast
     */
    void updateWaveFailedLast(Staff staff, List<Long> waveIds, Integer failedLast);

    void updateWavesExpressId(Staff staff,List<Long> waveIds,Long expressId);

    void updateWavesLogisticsCompanyId(Staff staff,List<Long> waveIds,Long logisticsCompanyId, Long expressId);

    List<Map<String, Object>> pickingHand(Staff staff, List<Long> waveIds, List<WavePicking> pickingList,ProgressData progressData);

    /**
     * 查询sid 对应货位信息记录
     * @param staff
     * @param sids
     * @param trades
     * @param showSkuGood
     * @return
     */
    Map<Long, List<GoodsSectionOrderRecord>> getSid2GsOrderMap(Staff staff, List<Long> sids, List<Trade> trades, boolean showSkuGood);

    /**
     * 合并数据，更新踢出数
     */
    void mergeUpdateRemovedNum(Staff staff, List<ErpBuffer> list);

    void updateRemoveWaveTrades(Staff staff, List<Trade> trades);

    List<WavePickerInfo> queryPickingParams(Staff staff, WaveFilterParams params, Page page);

    /**
     * 查询单件缺货数量
     * @param staff
     * @param warehouseId
     * @param sysItemIds
     * @param sysSkuIds
     * @return <商品key，单件缺货订单数>
     */
    Map<String, Integer> queryFastInOutTradeCount(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds);

    Map<String, Integer> queryFastInOutTradeCount(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds, FastInOutTradeQueryParam param);

    WaveItem queryFastInOutMultiTrade(Staff staff, Long warehouseId, List<FastInOutSimpleItemVO> simpleItemVOS, FastInOutTradeQueryParam param);

    /**
     * 查询单件缺货订单sid
     * @param staff
     * @param warehouseId
     * @param sysItemIds
     * @param sysSkuIds
     * @return <商品key，缺货订单sid列表>
     */
    Map<String, List<Long>> queryFastInOutTradeList(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds);

    Map<String, List<Long>> queryFastInOutTradeList(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds, FastInOutTradeQueryParam param);

    /**
     * 新增、删除 订单快销标签
     * @param staff
     * @param needAddTagSids
     * @param needRemoveTagSids
     */
    void modifyTradeTag4FastInOut(Staff staff, List<Long> needAddTagSids, List<Long> needRemoveTagSids);

    /**
     * 通过波次位置号查询区间订单号
     * @param staff
     * @param waveId
     * @param firstSid
     * @param lastSid
     * @return
     */
    List<Long> querySidsBySection(Staff staff, Long waveId, Long firstSid, Long lastSid);

    /**
     * 通过波次位置号查询区间订单号 (查询所有状态)
     * @param staff
     * @param waveId
     * @param firstSid
     * @param lastSid
     * @return
     */
    List<Long> queryAllStatusSidsBySection(Staff staff, Long waveId, Long firstSid, Long lastSid, List<Integer> statusList);


    /**
     * 验货登记时填充波次中的异常订单列表
     * KMERP-79844
     * @param staff
     * @param waveId
     * @param page
     */
    PageListBase<WaveTrade> queryExcepAndRemoveWaveTradeList(Staff staff, Long waveId, Page page);

    /**
     * 验货登记时填充波次中的异常商品列表
     * KMERP-79844
     * @param staff
     * @param waveId
     * @param page
     */
    PageListBase<WaveItem> queryExcepAndRemoveWaveOrderList(Staff staff, Long waveId, Page page);

    public void checkWaveStatistics(Staff staff, List<Wave> wavelist, WaveFilterParams params);
    /**
     * 查询波次订单
     */
    List<Trade> queryWaveTradeByCondition(Staff staff, WaveFilterTradeParams params);

    /**
     * 校验过滤波次数据
     * @param staff
     * @param waveIds
     * @param isWaveAutoPrint
     * @return
     */
    List<Wave> filterWaveList(Staff staff, List<Long> waveIds, boolean isWaveAutoPrint);

    void addWavePickingStatLogOnlyEs(Staff staff, WavePicking old, WavePicking update, String source);

    /**
     * 查询已取消波次未踢出订单sid
     */
    List<Long> queryCanceledUnRemoveSids(Staff staff);
    /**
     * 获取波次唯一码对应的波次拣选货位
     * @param staff
     * @param waveIds
     * @return 返回值 <唯一码， 波次拣选货位>
     */
    Map<String, String> getGoodsSectionCodeOfWaveUniqueCode(Staff staff, List<Long> waveIds);

    void fillWaveLogisticsCompanyName(Staff staff, List<Wave> waves);

    void fillTradeLogisticsCompanyName(Staff staff, List<Trade> tradeList);

    void fillTradeExpressCompanyName(Staff staff, List<Trade> tradeList);

    void fillPrintedTradeNum(Staff staff, List<Wave> waves, Long pageId);
    /**
     ** 查询波次拣选信息
     */
    List<WavePicking> getWavePickingByWaveIds(Staff staff, List<Long> waveIds);

    List<WaveTrade> queryWaveTradeByParams(Staff staff, WaveTradeQueryParams params);

    void updateWaveSeedTime(Staff staff, Long waveId, Date time, Integer type);

    void checkTradePickStatus(Staff staff, Trade trade, WaveConfig waveConfig, TradeValidator validator);

    /**
     * 校验波次的快递打印状态和配货状态(仅取消波次使用）
     * @param staff
     * @param waveId
     * @return
     */
    Map<String, Object> checkPrintStatusAndDistributionStatus(Staff staff, List<Long> waveId);

    /**
     * 打印拣货单查询订单
     * @param staff
     * @param waveIds
     * @param pickBillGetDataFromTrade false:查waveTrade true:查订单
     * @return
     */
    List<WaveTrade> queryPrintPickWaveTradesByWaveIds(Staff staff, List<Long> waveIds, boolean pickBillGetDataFromTrade);


    List<UnderstockedGroup> queryWaveIdUnderStockedGroupMap(Staff staff);

    void fillShipper(Staff staff, Trade trade);

    boolean checkWaveTradeSyncFeature(Staff staff);


    CrossBorderWavePrintInfo queryCrossBorderWavePrintInfo(Staff staff, Long waveId);

    void stallWaveSplitConsign(Staff staff, String waveIds);

    /**
     * 直接拣选商品日志输出,拷贝于WaveTradePostPrintListener
     */
    void waveBeginPickingLogFromListener(WavePicking wavePicking, Staff staff);

    /**
     * 按照时间对播种绩效进行修复
     * @param staff
     * @param startTime
     * @param endTime
     */
    void repairSeedRecordByDate(Staff staff, Date startTime,Date endTime);


    /**
     * 修改波次标签
     * @param staff
     * @param tagIds
     * @param waveIds
     * @param operateType 0:增加标签；1:删除标签
     */
    void modifyWaveTag(Staff staff, List<Long> tagIds, List<Long> waveIds, int operateType);
    /**
     * 更新trade、trade_not_consign的波次号
     * @param staff
     * @param trades
     */
    void updateTradeWaveId(Staff staff, List<Trade> trades);

    void rollBackStatusByWaveId(Staff staff, Long waveId);

    /**
     * 查询所有仓库
     * @param staff
     * @return
     */
    List<Long> queryWarehouseIds(Staff staff);
}
package com.raycloud.dmj.services.trades;

import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.params.TradePickingParams;
import com.raycloud.dmj.domain.trades.search.BaseTrade;
import com.raycloud.dmj.domain.trades.search.ItemQueryParams;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;
import com.raycloud.dmj.domain.trades.search.TradeQueryRequest;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单搜索服务
 *
 * <AUTHOR>
 */
public interface ITradeSearchService {

    /**
     * 有关订单的搜索方法
     * <pre>
     * 对于首次进入订单列表的页面，一般只需要设置{@link TradeQueryParams#(Integer)},分页和排序即可。
     * 对于一些自定义的查询，需要设置其他参数
     * </pre>
     *
     * @param params
     * @return
     */
    Trades search(Staff staff, TradeQueryParams params);

    Trades backTradeSearchUnfuse(Staff staff, TradeQueryParams params);

    /**
     * 查询套件子订单下的单品子订单
     *
     * @param staff
     * @param orderId
     * @return
     */
    List<Order> listSuits(Staff staff, Long orderId);

    /**
     * 根据指定的关键子查询订单
     *
     * @param staff
     * @param tradeFields 要查询的trade字段
     * @param orderFields 要查询的order字段
     * @param key         根据那个字段查询，目前支持 sid、tid、mergeSid、user_id,warehouse_id,orderId、oid
     * @param values      查询条件值
     * @return
     *
     * @deprecated 推荐使用 queryAndAssemblyByKeys,需特别注意的是 如果用user_id,warehouse_id等条件,由于没有分页限制 有可能有oom的风险
     */
    @Deprecated
    <T> List<Trade> queryByKeys(Staff staff, String tradeFields, String orderFields, String key, boolean filter, T... values);

    /**
     * 根据指定的关键子查询订单
     *
     * @param fields 要查询的trade字段
     * @param key    根据那个字段查询，目前支持 sid、tid、mergeSid、orderId、oid
     * @param values 查询条件值
     * @deprecated 推荐使用 queryAndAssemblyByKeys
     */
    @Deprecated
    <T> List<TbTrade> queryByKeys(Staff staff, String fields, String key, T... values);


    <T> List<Trade> queryAndAssemblyByKeys(Staff staff, TradeAssembleParams params, String key, T... values);

    /**
     * 查询订单详情，tid是指平台的订单
     *
     * @param tids
     * @return
     */
    List<TbTrade> queryByTids(Staff staff, boolean showDetail, String... tids);

    List<TbTrade> queryByTids(Staff staff, boolean showDetail, boolean queryTradePay, String... tids);


    List<TbTrade> queryByTidsWithOutFill(Staff staff, boolean showDetail, String... tids);
    /**
     * 查询订单详情，sid是指系统单号
     *
     * sid  mergeSid  order
     * 1    1         order1
     * 2    1         order2
     *
     * showDetail=false
     *   sids=1  返回 sid=1的trade1
     *   sids=2  返回 sid=2的trade2
     * showDetail=true
     *   sids=1  返回 sid=1的trade1和order1,order2
     *   sids=2  返回 sid=1的trade1和order1,order2
     *
     * @param staff
     * @param sids
     * @param showDetail false- 不查询order，如果是合单trade只返回跟sid对应的trade，不会转成对应的主单
     *                   true -  查询order ,如果是合单trade返回主单，order会返回主单和子单的order
     * @return
     */
    List<Trade> queryBySids(Staff staff, boolean showDetail, Long... sids);

    /**
     * 根据前端的操作场景 返回需要更新的数据
     *
     * @param staff
     * @param sids
     * @param showDetail
     * @return
     */
    List<Trade> queryLightTradeByScene(Staff staff, SenceCodeEnum senceCode,List<Integer> fieldGroups, Long... sids);

    /**
     * 查询订单详情，spiltSid是指系统单号
     *
     * @param staff
     * @param splitSids
     * @param showDetail
     * @return
     */
    List<Trade> queryBySplitSids(Staff staff, boolean showDetail, Long... splitSids);


    /**
     * 查询订单详情，sid是指系统单号
     *
     * @param staff
     * @param sids
     * @param showDetail
     * @return
     */
    List<Trade> queryBySids(Staff staff, boolean showDetail, boolean showTradeExt, Long... sids);

    /**
     * 查询订单详情，sid是指系统单号
     *
     * @param staff
     * @param showDetail
     * @param showTradeExt
     * @param sids
     * @return
     */
    List<Trade> queryBySids(Staff staff, boolean showDetail, boolean showTradeExt, boolean pddMaskData, Long... sids);
    /**
     * 查询订单详情，sid是指系统单号(不调用filer)
     *
     * @param staff
     * @param showDetail
     * @param sids
     * @return
     */
    List<Trade> queryBySidsNoFilter(Staff staff, boolean showDetail, Long... sids);

    List<Trade> queryBySidsNoFilter(Staff staff, boolean showDetail, boolean needFill, Long... sids);

    List<Trade> queryBySidsNoFilter(Staff staff, TradeQueryNoFilterParams params, Long... sids);

    /**
     * 查询订单详情，sid是指系统单号
     *
     * @param staff
     * @param sid
     * @param showDetail
     * @return
     */
    Trade queryBySid(Staff staff, boolean showDetail, Long sid);


    Trade queryBySidWithOutFill(Staff staff, boolean showDetail,Long sid);

    /**
     * 查询订单详情，sid是指系统单号
     *
     * @param staff
     * @param sid
     * @param showDetail
     * @return
     */
    Trade queryBySid(Staff staff, boolean showDetail, int pddDecrypt, Long sid);


    default Orders queryHotItem(Staff staff, TradeQueryParams params){
        return null;
    }

    /**
     * 根据快递运单号查询系统订单
     *
     * @param staff
     * @param outSid
     * @return
     */
    List<Trade> queryByOutSid(Staff staff, String outSid, boolean queryOrder, String... sysStatusList);

    List<Trade> queryByOutSid(Staff staff, String outSid, boolean queryOrder,boolean needFill, String... sysStatusList);

    /**
     * 根据订单短号查询
     *
     * @param staff
     * @param showDetail
     * @param shortIds
     * @return
     */
    List<Trade> queryByShortId(Staff staff, boolean showDetail, Long... shortIds);

    List<Trade> queryByShortIdWithOutFill(Staff staff, boolean showDetail, Long... shortIds);

    /**
     * 根据平台商品ID查询匹配或未匹配的未发货订单
     *
     * @param staff
     * @param itemIds 平台商品ID集合
     * @param matched true 只查询已匹配且没有"改"的订单，false 只查询没有匹配的订单
     * @param page    分页对象
     * @return 系统订单号列表
     */
    List<Long> querySidsByItemIds(Staff staff, List<String> itemIds, Boolean matched, Page page);

    /**
     * 根据平台商品ID查询匹配或未匹配的未发货订单
     *
     * @param staff
     * @param skuIds  平台SKUID集合
     * @param matched true 只查询已匹配且没有"改"的订单，false 只查询没有匹配的订单
     * @param page    分页对象
     * @return 系统订单号列表
     */
    List<Long> querySidsBySkuIds(Staff staff, List<String> skuIds, Boolean matched, Page page);

    /**
     * 根据平台商品ID查询匹配或未匹配的未发货订单
     *
     * @param staff
     * @param itemIds 平台商品编码集合
     * @param skuIds  平台SKUID集合
     * @param matched true 只查询已匹配且没有"改"的订单，false 只查询没有匹配的订单
     * @param page    分页对象
     * @return 系统订单号列表
     */
    List<Long> querySidsByItemSkuIds(Staff staff, List<String> itemIds, List<String> skuIds, Boolean matched, Page page);

    /**
     * 根据平台商品ID查询匹配或未匹配的未发货订单信息
     * @param staff
     * @param params
     * @return
     */
    List<TbOrder> queryFieldByItemSkuIds(Staff staff, ItemQueryParams params);

    /**
     * 根据系统商品编号和系统SKU编号，查询交易系统中的编号，这里返回的sids只包括待付款和待发货的订单，其他状态的订单不会返回
     *
     * @param staff
     * @param sysItemId
     * @param sysSkuId
     * @param page
     * @return
     */
    List<Long> querySidsBySysItemSkuId(Staff staff, Long sysItemId, Long sysSkuId, Page page, String... stockStatuss);

    /**
     * 根据系统商品编码和sku编码批量查询sid
     *
     * @param staff
     * @param sysItemIds
     * @param sysSkuIds
     * @param page
     * @return
     */
    List<Long> querySidsByItemSkuIdBatch(Staff staff, List<Long> sysItemIds, List<Long> sysSkuIds, Page page);

    /**
     * 按照更新时间区间搜索全部订单，但是不包括order信息
     * 目前用在财务的同步订单中
     *
     * @param staff
     * @param page
     * @param statusList
     * @param startDate
     * @param endDate
     * @return
     */
    List<Trade> searchDbTradeByDate(Staff staff, Page page, List<String> statusList, Date startDate, Date endDate);

    /**
     * 按照TradeQueryParams进行查询,目前queryParams只实现了部分参数
     * 目前用于开放api
     *
     * @param staff
     * @param tradeQueryParams
     * @return
     */
    List<Trade> searchTradeByQueryParams(Staff staff, TradeQueryParams tradeQueryParams);

    /**
     * 按照更新时间区间得到搜索全部订单的数量
     * 目前用在财务的同步订单中
     *
     * @param staff
     * @param statusList
     * @param startDate
     * @param endDate
     * @return
     */
    Long searchDbTradeByDateOfCount(Staff staff, List<String> statusList, Date startDate, Date endDate);

    /**
     * 根据sids查询发货记录
     *
     * @param staff
     * @param sids
     * @return
     */
    List<ConsignRecord> queryConsignRecordBySids(Staff staff, Long... sids);

    /**
     * 根据sids查询包括已删除订单
     * @param staff
     * @param showDetail
     * @param sids
     * @return
     */
    List<Trade> queryBySidsContainDeleteTrade(Staff staff, boolean showDetail, Long... sids);

    /**
     * 根据sids查询订单，合单后隐藏的订单也会显示， 子订单的组装与平台保持一致
     *
     * @param staff
     * @param sids
     * @return
     */
    List<Trade> queryBySidsContainMergeTrade(Staff staff, Long... sids);

    /**
     * 根据sids查询订单，合单后隐藏的订单也会显示， 子订单的组装与平台保持一致
     *
     * @param staff
     * @param queryOrder 是否查询子订单
     * @param sids
     * @return
     */
    List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, Long... sids);

    /**
     * 根据sids查询订单，合单后隐藏的订单也会显示， 子订单的组装与平台保持一致
     *
     * @param staff
     * @param queryOrder 是否查询子订单
     * @param filter     是否对订单过滤
     * @param sids
     * @return
     */
    List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter, Long... sids);

    /**
     * 根据sids查询订单，合单后隐藏的订单也会显示， 子订单的组装与平台保持一致
     *
     * @param staff
     * @param queryOrder     是否查询子订单
     * @param filter         是否对订单过滤
     * @param containConsign 是否包含发货的订单
     * @param sids
     * @return
     */
    List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter, boolean containConsign, Long... sids);

    /**
     * 根据sids查询订单，合单后隐藏的订单也会显示， 子订单的组装与平台保持一致
     *
     * @param staff
     * @param queryOrder     是否查询子订单
     * @param queryExt       是否查询Ext信息
     * @param filter         是否对订单过滤
     * @param containConsign 是否包含发货的订单
     * @param sids
     * @return
     */
    List<Trade> queryBySidsContainMergeTrade(Staff staff, TradeAssembleParams params, Long... sids);




    List<String> queryTids(Staff staff, Long... sids);

    /**
     * 查询已存在的tid
     *
     * @param staff
     * @param tidList
     * @return
     */
    List<String> queryTidsExist(Staff staff, List<String> tidList);

    /**
     * 查询未完成的退款中和空包商品和已分配库存数量
     *
     * @param staff
     * @param warehouseId
     * @return
     */
    Map<String, Integer> queryNotFinishedRefundOrScalpingItemNumMap(Staff staff, Long warehouseId);

    /**
     * 是否有tid,oid的平台order
     *
     * @param staff
     * @param tid
     * @param oid
     * @return
     */
    Long hasPlatformOrderInfo(Staff staff, String tid, Long oid);

    /**
     * 获取tid,oid的order信息，
     *
     * @param staff
     * @param tid
     * @param oid
     * @return
     */
    List<Order> queryPlatformOrderInfo(Staff staff, String tid, Long oid);


    /**
     * 补充订单里面没有的商品属性（查商品）
     */
    void fillTradeInfo(Staff staff, List<Trade> trades);

    /**
     * 指定波次号查询订单
     *
     * @param staff
     * @param showDetail
     * @param waveId
     * @return
     */
    List<Trade> queryByWaveId(Staff staff, Page page, boolean showDetail, String tradeFields, Long warehouseId, Long waveId);

    List<String> checkIds(Staff staff, TradeQueryParams params);

    /**
     * 根据查询条件统计淘系订单数
     *
     * @param staff
     * @param params
     * @return
     */
    Long countTaobaoSeries(Staff staff, TradeQueryParams params);

    /***
     * @DESCRIPTION: 更据拣选的条件查询订单
     * <AUTHOR>
     * @params: [staff, tradePickingParams, b, sysStatus]
     * @return: java.util.List<com.raycloud.dmj.domain.trades.Trade>
     * @Date: 2021/3/19 9:39 上午
     * @Modified By:
     */
    List<Trade> queryByPickingParams(Staff staff, TradePickingParams tradePickingParams, boolean queryOrder, String... sysStatus);

    /**
     * 根据手机号后四位查询订单
     *
     * @param staff
     * @param tradeFields
     * @param mobileTail
     * @return
     */
    List<Trade> queryByMobileTail(Staff staff, String tradeFields, String ... mobileTail);

    List<TradeExt> getTradeExtBySid(Staff staff, List<Long> sids);

    /**
     * 查询商品异常的商品
     * @param staff
     * @return
     */
    List<InvaildItem> queryItemExceptionItem(Staff staff, Page page, InvaildItemQueryParams params);

    /**
     * 查询商品异常的商品
     * @param staff
     * @return
     */
    List<InvaildItem> queryItemExceptionItem(Staff staff, Page page, Sort sort, InvaildItemQueryParams params,List<Long> sids);

    Long queryItemExceptionItemCount(Staff staff, InvaildItemQueryParams params,List<Long> sids);

    List<InvaildItemTrade> queryItemExceptionTrade(Staff staff, Page page, InvaildItemTradeQueryParams params);

    Long queryItemExceptionTradeCount(Staff staff, InvaildItemTradeQueryParams params);

    /**
     * 查询非系统订单根据tid 只查部分字段
     */
    List<Trade> queryNotSysTradeByTids(Staff staff, List<String> tids);

    /**
     *  根据波次号查询订单（返回部分字段）
     * @param staff
     * @param fields 需要查询的字段
     * @param waveIds   波次号
     * @return
     */
    List<Trade> querySomeFieldsByWaveIds(Staff staff, String fields, List<Long> waveIds);
    /**
     * 未发货-物流预警统计
     */
    Long statisticsWithNotConsign(Staff staff, Date startTime, Date endTime);

    List<Trade> querySomeFieldsBySids(Staff staff, String fields, List<Long> sids, List<Long> mergeSids);

    List<Order> querySomeFieldsBySids(Staff staff, String fields, List<Long> sids);

    /**
     * 根据tid查询订单并且查询ext
     * @param staff
     * @param showDetail
     * @param needExt
     * @param tids
     * @return
     */
    List<TbTrade> queryByTidsNeedExt(Staff staff, boolean showDetail, boolean needExt, String... tids);

    /**
     * @description: 获取订单中的智能单品
     * <AUTHOR>
     * @date 2022/8/29 8:15 下午
     */
    Trades setTradeExtItemQuantity(Staff staff, Trades trades);

    /**
     * @description: 获取订单中的智能单品
     * <AUTHOR>
     * @date 2022/8/29 8:15 下午
     */
    List<Trade> setTradeExtItemQuantity(Staff staff, List<Trade> tradeList);


    List<TbTrade> statisticsWithNotConsign(Staff staff, LogisticsWarningTradeQueryParams params);

    Long statisticsCountWithNotConsign(Staff staff, LogisticsWarningTradeQueryParams params);



    /**
     * 校验对应平台订单是否有补发订单
     * @return 有补发订单sid列表
     */
    List<ReplenishTrades> checkReplenishTrade(Staff staff, Long... sids);


    List<Trade> querySomeFieldsByCondition(Staff staff, String fields, Map<String, Object> condition);

    /**
     * 打印后发货用的查询订单取号
     */
    List<Trade> queryTrades4PrintEnd(Staff staff,List<Long> sids,boolean needMerge);

    /**
     * 查询合单订单*
     * @param staff
     * @param mergeSids
     * @return
     */
    List<Trade> queryMergeTradeList(Staff staff, List<Long> mergeSids);

    /**
     * 打印用的查询订单取号
     */
    List<Trade> queryTrades4Print(Staff staff, List<Long> sids, boolean needOrder);

    void doBeforeSearch(Staff staff, TradeQueryParams params);

    CursorListBase<BaseTrade> searchBaseTrade(TradeQueryRequest request);

    CursorListBase<TbTrade> searchTrade(TradeQueryRequest request,TradeAssembleParams params);

    CursorListBase<TbTrade> searchTrade(Staff staff, TradeQueryParams conditions,TradeAssembleParams assembles);

    Long countTrade(TradeQueryRequest request,TradeAssembleParams params);


    /**
     * 根据outerIId查询匹配或未匹配的未发货订单
     *
     * @param staff
     * @param outerIIds 商品编码集合
     * @param matched true 只查询已匹配且没有"改"的订单，false 只查询没有匹配的订单
     * @param page    分页对象
     * @return 系统订单号列表
     */
    List<Long> querySidsByOuterIIds(Staff staff, List<String> outerIIds, boolean matched, Page page);
}

package com.raycloud.dmj.services.utils;

import com.google.common.collect.Lists;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.customer.basis.CmCustomerBalanceVO;
import com.raycloud.dmj.domain.enums.TradeStatusEnum;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trade.except.TradeExceptOldUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import com.raycloud.dmj.services.trades.deliverylimit.DeliveryLimitBusiness;
import com.raycloud.dmj.services.trades.deliverylimit.DeliveryLimitException;
import com.raycloud.dmj.services.trades.deliverylimit.PddLogisticsAccess;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.apache.log4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.utils.CommonConstants.WMS_BTAS_EXPRESS_PRODUCT;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2017-06-07 19:59
 */
@Component
public final class ConsignUtils {

    private static Logger logger = Logger.getLogger(ConsignUtils.class);

    public static final String ERR_MSG_FX_HAS_EXCPTION = "分销异常订单";

    private static DeliveryLimitBusiness deliveryLimitBusiness;

    private static IExpressCompanyService expressCompanyService;

    private static IUserWlbExpressTemplateService userWlbExpressTemplateService;

    /**
     * 供销发货是否从分销开始触发
     */
    public static final String IF_GX_CONSIGN_START_WITH_FX = "ifGxConsignStartWithFx";

    private static List<String> needCheckPlatform = Lists.newArrayList(CommonConstants.PLAT_FORM_TYPE_TAO_BAO,
            CommonConstants.PLAT_FORM_TYPE_TIAN_MAO,
            CommonConstants.PLAT_FORM_TYPE_1688,
            CommonConstants.PLAT_FORM_TYPE_1688_C2M,
            CommonConstants.PLAT_FORM_TYPE_PDD);

    private static List<String> canDummyConsignPlatform = Lists.newArrayList(
            CommonConstants.PLAT_FORM_TYPE_SHOPEE,
            CommonConstants.PLAT_FORM_TYPE_KJ_LAZADA,

            CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU
    );
    @Autowired
    public static void setDeliveryLimitBusiness(DeliveryLimitBusiness deliveryLimitBusiness) {
        ConsignUtils.deliveryLimitBusiness = deliveryLimitBusiness;
    }

    @Autowired
    public static void setExpressCompanyService(IExpressCompanyService expressCompanyService) {
        ConsignUtils.expressCompanyService = expressCompanyService;
    }

    @Autowired
    public static void setUserWlbExpressTemplateService(IUserWlbExpressTemplateService userWlbExpressTemplateService) {
        ConsignUtils.userWlbExpressTemplateService = userWlbExpressTemplateService;
    }

    /**
     * 是否开启了 打印、包装、称重 上传发货的开关
     *
     * @param tradeConfig
     * @return
     */
    public static boolean ifOpenUpload(TradeConfig tradeConfig) {
        return tradeConfig.getOpenUploadConsign() != null && tradeConfig.getOpenUploadConsign() - 1 == 0;
    }

    /**
     * 当前操作是否需要触发上传发货
     *
     * @param tradeConfig
     * @param operation   : 打印:1 包装:2 称重:3
     * @return
     */
    public static boolean ifTriggerUpload(TradeConfig tradeConfig, int operation) {
        return tradeConfig.getUploadConsignName() != null && tradeConfig.getUploadConsignName() - operation == 0;
    }

    /**
     * 正常订单系统发货校验
     *
     * @param trade
     * @return
     */
    public static String validateConsign(Staff staff, Trade trade, boolean validateStatus, SendType consignType, TradeConfig tradeConfig, Map<Long, Integer> warehouseId_Type, Map<String, Trade> fxTradeMap, Map<Long, CmCustomerBalanceVO> customerBalanceMap, Integer dymmyType, String noLogisticsName, String noLogisticsTel,Boolean isExternal) {
        return validateConsign(staff, trade, validateStatus, consignType, tradeConfig, warehouseId_Type, fxTradeMap, customerBalanceMap, dymmyType, noLogisticsName, noLogisticsTel,null, null,isExternal);
    }

    public static String validateConsign(Staff staff, Trade trade, boolean validateStatus, SendType consignType, TradeConfig tradeConfig, Map<Long, Integer> warehouseId_Type, Map<String, Trade> fxTradeMap, Map<Long, CmCustomerBalanceVO> customerBalanceMap, Integer dymmyType, String noLogisticsName, String noLogisticsTel, TradeCombineParcel tradeCombineParcel,Boolean isExternal) {
        return validateConsign(staff, trade, validateStatus, consignType, tradeConfig, warehouseId_Type, fxTradeMap, customerBalanceMap, dymmyType, noLogisticsName, noLogisticsTel, tradeCombineParcel,null,isExternal);
    }

    public static String validateConsign(Staff staff, Trade trade, boolean validateStatus, SendType consignType, TradeConfig tradeConfig, Map<Long, Integer> warehouseId_Type, Map<String, Trade> fxTradeMap, Map<Long, CmCustomerBalanceVO> customerBalanceMap, Integer dymmyType, String noLogisticsName, String noLogisticsTel, TradeCombineParcel tradeCombineParcel, Map<Long, Map<Long, String>> fxCashFlowData,Boolean isExternal) {
        if (trade.getUserId() > 0 && !TradeUtils.isGxTrade(trade)) {//非出库单
            //供销发货时不校验
            if (!TradeUtils.isGxTrade(trade)) {
                if (trade.getUserId() > 0) {//非出库单
                    User user = staff.getUserByUserId(trade.getUserId());
                    if (user != null && user.getActive() != null && user.getActive() == 0) {
                        return "店铺已停用";
                    }

                }
            }
        }
        if(TradeUtils.isKttHelpSaleTrade(trade)){
            return "快团团帮卖订单禁止发货";
        }
        if(PlatformUtils.isHuaweiGovTrade(trade)){
            return "华为国补订单禁止发货";
        }
        if (trade.getIsCancel() != null && trade.getIsCancel() - 1 == 0) {
            return "订单已作废";
        }
        // isGx =1 为供销触发分销发货。此时分销订单没有运单号 所以不校验
        if (SendType.DUMMY != consignType && StringUtils.isEmpty(trade.getOutSid())&&!CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(trade.getSource())&&!CommonConstants.PLAT_FORM_TYPE_TIKTOK.equals(trade.getSource()) && !CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource()) && !isSupportNonOutSid(trade)) {
            if(!PlatformUtils.is1688ZiTiTrade(trade)&&!PlatformUtils.isHuaweiGovTrade(trade)&&!PlatformUtils.is1688SmtNoConsignTrade(trade)) {
                return "缺少运单号";
            }
        }
        if (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
            return "订单交易关闭";
        }

//        // 淘系和pdd订单需要做快递单号的限制
//        String msg = checkDeliveryLimit(staff, trade);
//        if (null != msg) {
//            return msg;
//        }
        //异常打印发货上传、提前发货上传、系统已发货在上传 不校验异常
        if ((!isExcepTradeParty3SendGoods(trade, tradeConfig, warehouseId_Type) && !trade.isForce() && SendType.MANUAL_UPLOAD != consignType&&SendType.UPLOAD != consignType)
                && !Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus())&&(isExternal==null||!isExternal)) {
            if (TradeExceptUtils.isContainExcept(staff,trade, ExceptEnum.HALT)) {
                return "订单已挂起";
            }
            if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.REFUNDING)) {
                return "订单退款中";
            }
            //如果订单仅包含缺货异常，继续系统发货和继续上传发货
            if (!Trade.STOCK_STATUS_NORMAL.equals(trade.getStockStatus()) && TradeExceptUtils.isOnlyContainsExcept(staff,trade,ExceptEnum.INSUFFICIENT) && !TradeExceptUtils.isContainCustomExcept(staff,trade)) {
                //系统发货，如果订单状态是已审核，则发货
                if (validateStatus && Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
                    return null;
                }
                //上传发货，不校验订单是否审核
                if (!validateStatus) {
                    return null;
                }
            }
            if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.ADDRESS_CHANGED) ){
                return "平台改地址异常";
            }
            if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.ITEM_CHANGED) ){
                return "平台换商品异常";
            }
            //分销订单
            if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.FX_AMBIGUITY) ){
                return "不明确供应商异常";
            }
            if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.FX_WAITPAY) ){
                return "分销待付款异常";
            }
            if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.FX_UNAUDIT) ){
                return "分销反审核异常";
            }
            if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.FX_REPULSE) ){
                return "供销商打回异常";
            }
            List<String> exceptChineseNames = TradeExceptUtils.getExceptChineseNames(staff, trade, null);
            // 上传异常不属于异常，不需要校验
            exceptChineseNames.remove(ExceptEnum.UPLOAD_EXCEPT.getChinese());
            if (exceptChineseNames.contains(ExceptEnum.INSUFFICIENT.getChinese()) && !TradeExceptOldUtils.isContainExcept(staff, trade, ExceptEnum.INSUFFICIENT)) {
                exceptChineseNames.remove(ExceptEnum.INSUFFICIENT.getChinese());
            }
            if(CollectionUtils.isNotEmpty(exceptChineseNames)){
                return "异常订单:"+StringUtils.join(exceptChineseNames,",");
            }
            Set<Long> customExceptIds = TradeExceptUtils.getCustomExceptIds(staff, trade);
            if(CollectionUtils.isNotEmpty(customExceptIds)){
                return "自定义异常订单:"+StringUtils.join(customExceptIds,",");
            }

        }
        if (validateStatus&&(isExternal==null||!isExternal)) {
            //force=false 非异常发货时，需校验系统状态是否为已审核
            //force=true 异常发货时，需校验系统状态是否为待审核或已审核
            if ((!trade.isForce() && !Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) ||
                    trade.isForce() && !TradeStatusUtils.isWaitSellerSend(trade.getSysStatus())) {
                return "非待发货订单[" + TradeStatusEnum.getNameBycode(trade.getSysStatus()) + "]";
            }
        }

        if (consignType == SendType.DUMMY) {
            if(PlatformUtils.is1688ZiTiTrade(trade)) {
                return "1688平台「现场自提」订单不支持无需物流发货";
            }
            Object fxSource = TradeExtUtils.getExtraFieldValue(trade.getTradeExt(),"fxSource");
            if (dymmyType != null && !"sys".equals(trade.getSource())&&!Objects.equals("sys", fxSource)&&!canDummyConsignPlatform.contains(trade.getSource())) {
                //1-买家自提2-卖家发货 仅支持1688手工单
                if (!TradeTypeUtils.isFxgBtasTrade(trade) && !TradeTypeUtils.isWmsBtasTrade(trade) && !CommonConstants.PLAT_FORM_TYPE_1688.equals(trade.getSource())&&!CommonConstants.PLAT_FORM_TYPE_SELF_BUILT.equals(trade.getSource()) && !CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(trade.getSource()) && ! TradeUtils.isTbTrade(trade.getSource())) {
                    return "请选择符合条件（手工订单和1688平台、淘系订单、开放平台）的订单进行发货~";
                }
                if (dymmyType == 1&&TradeUtils.isTbTrade(trade.getSource())) {
                    return "淘宝、天猫非手工订单不支持买家自提";
                }
                if (dymmyType == 2) {
                    if (StringUtils.isBlank(noLogisticsName)) {
                        return "卖家发货请填写配送人员";
                    }
                    if (StringUtils.isBlank(noLogisticsTel)) {
                        return "卖家发货请填写联系方式";
                    }
                }
            }
        }
        //供销订单发货时也要校验分销订单是否异常
        String checkFxTradeExceptStr = checkFxTradeExcept(staff,trade, fxTradeMap, consignType);
        if (StringUtils.isNotBlank(checkFxTradeExceptStr)){
            return checkFxTradeExceptStr;
        }

        //判断是否校验店铺客户的余额
        if (MapUtils.isNotEmpty(customerBalanceMap) && tradeConfig.tradeConsignVerifyCustomerBalance()) {
            CmCustomerBalanceVO cmCustomerBalanceVO = customerBalanceMap.get(trade.getUserId());
            if (Objects.nonNull(cmCustomerBalanceVO)) {
                // 判断当前余额是否大于警戒值 大于，发货成功（同时满足其他发货的条件）
                // 小于，发货失败且弹框提示：客户余额不足，请先充值再发货
                cmCustomerBalanceVO.setAccountBalance(cmCustomerBalanceVO.getAccountBalance().subtract(cmCustomerBalanceVO.getCommission()));
                if (cmCustomerBalanceVO.getAccountBalance().doubleValue() < 0) {
                    return "客户编码【" + cmCustomerBalanceVO.getCmCode() + "】余额不足，请先充值再发货!";
                }
            }
        }

//        //放心购BTAS订单只支持组包出库发货、wms触发的发货
//        if (null == tradeCombineParcel && TradeTypeUtils.isFxgBtasTrade(trade)) {
//            if (!btasCheckTrade(trade)){
//                return "放心购BTAS质检订单,不支持线下组包出库之外的发货方式!";
//            }
//        }
//        //WMS BTAS订单，只支持组包出库发货
//        if (null == tradeCombineParcel && TradeTypeUtils.isWmsBtasTrade(trade)) {
//            return "WMS BTAS质检订单,不支持线下组包出库之外的发货方式!";
//        }

        //判断分销商是否余额充足,为空则不校验
        if (TradeUtils.isQimenFxSource(trade) && !((trade.getIfFxForcePushTrade() || trade.getIfFxWaitPay())
                && !(trade.getV() != null && (trade.getV() | TradeConstants.V_TRADE_QIMEN_CASH_FLOW) - trade.getV() == 0))
                && MapUtils.isNotEmpty(fxCashFlowData)) {
            Map<Long, String> errorMap = fxCashFlowData.get(trade.getSourceId());
            if (null != errorMap && errorMap.containsKey(trade.getSid())) {
                return "分销商【" + (StringUtils.isNotEmpty(trade.getSourceAliasName()) ? trade.getSourceAliasName() : trade.getSourceName()) + "】余额不足，请先充值再发货!";
            }
        }
        return null;
    }

    public static boolean btasCheckTrade(Trade trade) {
        String tradeExtraFiled = Optional.ofNullable(trade).map(Trade::getTradeExt).map(TradeExt::getExtraFields).orElse("");
        return tradeExtraFiled.contains(WMS_BTAS_EXPRESS_PRODUCT);
    }

    private static String btasPicUploadCheckOrders(List<Order> orderList) {
        Set<String> results = new HashSet<>();
        for (Order order : orderList) {
            List<AbstractMap.SimpleEntry> btasPictureQualityResult = OrderUtils.getBtasPictureQualityResult(order);
            for (AbstractMap.SimpleEntry simpleEntry : btasPictureQualityResult) {
                results.add(simpleEntry.getValue().toString());
            }
        }
        if (results.contains(OrderUtils.BtasPictureQualityResultEnum.FAIL.descAndCode)) {
            if (results.size() == 1) {
                return "该笔订单质检失败，请检查后重新发货！";
            }
            return "该订单存在商品质检失败，请检查后重新发货！";
        }
        if (results.contains(OrderUtils.BtasPictureQualityResultEnum.UNDEFINED.descAndCode)) {
            if (results.size() == 1) {
                return "该笔订单未质检，请检查后重新发货！";
            }
            return "该订单存在商品未质检，请检查后重新发货！";
        }
        return null;
    }

    private static String checkDeliveryLimit(Staff staff, Trade trade) {
        if (needCheckPlatform.contains(trade.getSource())) {
            try {
                List<UserWlbExpressTemplate> userWlbExpressTemplateList = userWlbExpressTemplateService.getUserExpressTemplateIdNameExpressNeme(staff, new Long[]{trade.getTemplateId()});
                if (CollectionUtils.isEmpty(userWlbExpressTemplateList)) {
                    return "sid=" + trade.getSid() + " templateId=" + trade.getTemplateId() + " 找不到对应的电子面单快递模板";
                }
                ExpressCompany expressCompany = expressCompanyService.getExpressCompanyById(userWlbExpressTemplateList.get(0).getExpressId());
                PddLogisticsAccess.PddExpressCompany expressCompanyByCode = PddLogisticsAccess.getExpressCompanyByCode(expressCompany.getCode());
                deliveryLimitBusiness.checkDeliveryLimit(trade.getSource(), expressCompanyByCode.getWp_code(), trade.getOutSid());
            } catch (DeliveryLimitException e) {
                logger.error(e.getMessage(), e);
                return e.getMessage();
            }
        }
        return null;
    }

    /**
     * 是否配置开启了第三方仓库异常订单发货的功能
     *
     * @param tradeConfig
     * @return
     */
    private static boolean isExcepTradeParty3SendGoods(Trade trade, TradeConfig tradeConfig, Map<Long, Integer> warehouseId_Type) {
        if (warehouseId_Type == null) {
            if (logger.isDebugEnabled()) {
                logger.debug(trade.getSid() + "warehouseId_Type为空");
            }
            return false;
        }
        Integer type = warehouseId_Type.get(trade.getWarehouseId());
        if (type == null) {
            if (logger.isDebugEnabled()) {
                logger.debug(trade.getSid() + "找不到该仓库id:" + trade.getWarehouseId());
            }
            return false;
        }
        if (type != Warehouse.TYPE_EXTRA) {
            return false;
        }

        if (tradeConfig == null) {
            return true;//默认开启
        }
        if (tradeConfig.getExcepTradeParty3SendGoods() == null ||
                tradeConfig.getExcepTradeParty3SendGoods() == 1) {
            return true;
        }
        return false;
    }


    /**
     * 出库单系统发货校验
     *
     * @param trade
     * @return
     */
    public static String validateOutConsign(Trade trade) {
        if (!trade.isOutstock()) {
            return "非出库单";
        }
        if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
            return "出库单已发货";
        }
        if (!Trade.STOCK_STATUS_NORMAL.equals(trade.getStockStatus())) {
            return "出库单库存异常[" + trade.getStockStatus() + "]";
        }
        if (trade.getIsExcep() - 1 == 0) {
            return "异常出库单";
        }
        return null;
    }

    public static ConsignRecord buildFullRecord(Trade trade, SendType type, Integer isError, Integer enableStatus) {
        ConsignRecord record = new ConsignRecord();
        record.setSid(trade.getSid());
        record.setConsignType(type != null ? type.name() : null);
        if (SendType.DUMMY == type) {
            record.setTemplateId(0L);
            record.setTemplateType(0);
            record.setTemplateName("无需物流");
            record.setLogisticsCompanyId(0L);
            record.setLogisticsCompanyName("无需物流");
            record.setOutSid("");
        } else {
            record.setTemplateId(trade.getTemplateId());
            record.setTemplateType(trade.getTemplateType());
            record.setTemplateName(trade.getTemplateName());
            record.setLogisticsCompanyId(trade.getLogisticsCompanyId());
            record.setLogisticsCompanyName(StringUtils.isNotBlank(trade.getLogisticsCompanyName()) ? trade.getLogisticsCompanyName() : trade.getTemplateName());
            record.setOutSid(trade.getOutSid());
        }
        record.setSource(trade.getSource());
        //如果是延时发货,trade的consignTime如果为2000-01-01 00:00:00。则将设为当前时间
        Date consigned = trade.getConsignTime();
        if (2 == isError && trade.getConsignTime() != null) {
            Date defaultDate = null;
            try {
                defaultDate = DateUtils.parseDate("2000-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");//变更时间
            } catch (ParseException e) {
                logger.error("延时发货逻辑,转换为date数据格式出现异常", e);
            }
            if (consigned.compareTo(defaultDate) == 0) {
                consigned = new Date();
            }
        }
        record.setConsigned(consigned);
        record.setUserId(trade.getUserId());
        record.setWarehouseId(trade.getWarehouseId());
        record.setWarehouseName(trade.getWarehouseName());
        record.setTid(trade.getTid());
        record.setIsError(isError);
        record.setEnableStatus(enableStatus);
        record.setTradePay(trade.getPayTime());
        record.setMergeSid(trade.getMergeSid());
        record.setTradeType(TradeUtils.isGxOrMixTrade(trade) ? 1 : 0);
        return record;
    }

    public static ConsignRecord buildSimpleRecord(Long sid, SendType type, Integer isError, String errorDesc) {
        ConsignRecord record = new ConsignRecord();
        record.setSid(sid);
        record.setIsError(isError);
        record.setConsignType(type != null ? type.name() : null);
        record.setErrorDesc(errorDesc);
        return record;
    }

    public static SendType getSendType(String consignType) {
        return getSendType(consignType, SendType.OFFLINE);
    }

    public static SendType getSendType(String consignType, SendType defaultConsignType) {
        if (SendType.OFFLINE.name().equals(consignType) || SendType.DELAY.name().equals(consignType)) {
            return SendType.OFFLINE;
        } else if (SendType.UPLOAD.name().equals(consignType)) {
            return SendType.UPLOAD;
        } else if (SendType.DUMMY.name().equals(consignType) || "虚拟发货".equals(consignType)) {
            return SendType.DUMMY;
        } else if (SendType.ONLINE.name().equals(consignType)) {
            return SendType.ONLINE;
        } else if (SendType.ONLINE_CONFIRM.name().equals(consignType)) {
            return SendType.ONLINE_CONFIRM;
        } else if (SendType.RESEND.name().equals(consignType)) {
            return SendType.RESEND;
        } else if (SendType.OFFLINE_MANUAL.name().equals(consignType)) {
            return SendType.OFFLINE_MANUAL;
        } else if (SendType.OFFLINE_SCAN.name().equals(consignType)) {
            return SendType.OFFLINE_SCAN;
        } else if (SendType.OFFLINE_ONLY.name().equals(consignType)) {
            return SendType.OFFLINE_ONLY;
        } else if (SendType.OFFLINE_PRINT.name().equals(consignType)) {
            return SendType.OFFLINE_PRINT;
        } else if (SendType.OFFLINE_DIRECT.name().equals(consignType)) {
            return SendType.OFFLINE_DIRECT;
        } else if (SendType.BIC_BATCH.name().equals(consignType)) {
            return SendType.BIC_BATCH;
        } else if(SendType.BTAS_COMBINE_PARCEL.name().equals(consignType)){
            return SendType.BTAS_COMBINE_PARCEL;
        } else if (consignType!=null && consignType.startsWith(TradeConstants.TYPE_TRADE_OUT)){
            //兼容旧逻辑 出库单的发货类型为空
            return null;
        } else if (SendType.MUL_PACK_UPLOAD.name().equals(consignType)) {
            return SendType.MUL_PACK_UPLOAD;
        }else if (SendType.MANUAL_UPLOAD.name().equals(consignType)) {
            return SendType.MANUAL_UPLOAD;
        }else if (SendType.AHEAD_PAY_TIME.name().equals(consignType)) {
            return SendType.AHEAD_PAY_TIME;
        }
        return defaultConsignType != null ? defaultConsignType : SendType.OFFLINE;
    }

    public static Long[] getSids(List<ConsignRecord> consignRecords) {
        List<Long> sids = new ArrayList<Long>(consignRecords.size());
        for (ConsignRecord consignRecord : consignRecords) {
            if (!sids.contains(consignRecord.getSid())) {
                sids.add(consignRecord.getSid());
            }
        }
        return sids.toArray(new Long[0]);
    }

    public static List<Long> getPlatformSids(List<Trade> trades) {
        List<Long> list = new ArrayList<Long>();
        for (Trade trade : trades) {
            //系统订单也要放上去
//            if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            list.add(trade.getSid());
//            }
        }
        return list;
    }

    public static Set<Trade> filterRiskTrade(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return null;
        }
        //发货过滤风控订单
        List<Trade> riskTrades = trades.stream().filter(t -> TradeExceptUtils.isContainExcept(staff,t,ExceptEnum.RISK)).collect(Collectors.toList());
        Set<Trade> rmTrades = new HashSet<>();
        if (CollectionUtils.isNotEmpty(riskTrades)) {
            rmTrades.addAll(riskTrades);
            for (Trade riskTrade : riskTrades) {
                if (TradeUtils.isMerge(riskTrade)) {
                    for (Trade meTrade : trades) {
                        if (riskTrade.getMergeSid().equals(meTrade.getMergeSid())) {
                            rmTrades.add(meTrade);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(rmTrades)) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLogHead(staff).append("风控订单不能发货：").append(Strings.join(",", rmTrades.stream().map(tr -> tr.getSid() + "_" + tr.getItemExcep()).collect(Collectors.toList()))));
                    // todo 线上风控订单问题日志跟踪,解决问题后删除
                    logger.debug(LogHelper.buildLogHead(staff).append("风控订单删除前trades.size：").append(CollectionUtils.isEmpty(trades) ? 0 : trades.size()).append(",rmTrades.size:").append(rmTrades.size()));
                }
                trades.removeAll(rmTrades);
                // todo 线上风控订单问题日志跟踪,解决问题后删除
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLogHead(staff).append("风控订单删除后trades.size：").append(CollectionUtils.isEmpty(trades) ? 0 : trades.size()));
                    logger.debug(LogHelper.buildLogHead(staff).append("风控订单删除后trades：").append(Strings.join(",", trades.stream().map(tr -> tr.getSid() + "_" + tr.getItemExcep()).collect(Collectors.toList()))));
                }
            }
        }

        return rmTrades;
    }

    public static void filterWmsTrade(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        //发货过滤快麦wms订单
        List<Trade> wmsTrades = trades.stream().filter(t -> "qimen".equals(TradeExtUtils.getExtraFieldValue(t.getTradeExt(), "source"))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(wmsTrades)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append("快麦wms订单不能上传发货：").append(Strings.join(",", wmsTrades.stream().map(TradeBase::getSid).collect(Collectors.toList()))));
            }
            trades.removeAll(wmsTrades);
        }
    }


    private static final List<String> SUPPORT_NON_OUT_SID_PLATS = Lists.newArrayList(CommonConstants.PLAT_FORM_TYPE_SHEIN, CommonConstants.PLAT_FORM_TYPE_SMTQTG, CommonConstants.PLAT_FORM_TYPE_TEMU,CommonConstants.PLAT_FORM_TYPE_TIK_TOK_QTG,CommonConstants.PLAT_FORM_TYPE_SHOPEE_QTG,CommonConstants.PLAT_FORM_TYPE_TAO_BAO_XSD);

    static {
        SUPPORT_NON_OUT_SID_PLATS.add(CommonConstants.PLAT_FORM_TYPE_OZON);
        SUPPORT_NON_OUT_SID_PLATS.add(CommonConstants.PLAT_FORM_TYPE_JOOM);
    }

    public static boolean isSupportNonOutSid(Trade trade) {
        return SUPPORT_NON_OUT_SID_PLATS.contains(trade.getSource()) || SUPPORT_NON_OUT_SID_PLATS.contains(trade.getSplitSource());
    }

    /**
     * 校验供销、供销且分销订单发货时，其对应的分销订单是否有异常
     * @param trade
     * @param fxTradeMap
     * @return true:有异常
     */
    public static String  checkFxTradeExcept(Staff staff,Trade trade, Map<String, Trade> fxTradeMap, SendType consignType){
        if (null == trade || !TradeUtils.isGxOrMixTrade(trade)){
            return null;
        }
        if (SendType.MANUAL_UPLOAD == consignType && !trade.isForce() && TradeUtils.isGxOrMixTrade(trade)
                && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_UNAUDIT)) {
            return "供销订单有分销商反审核异常，不支持该操作";
        }
        if (MapUtils.isEmpty(fxTradeMap)){
            return null;
        }
        Trade fxOrMixTrade = fxTradeMap.get(trade.getTid());
        if (fxOrMixTrade != null && SendType.UPLOAD != consignType && !Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(fxOrMixTrade.getSysStatus())) {
            if (fxOrMixTrade.getIsExcep() - 1 == 0) {
                return ERR_MSG_FX_HAS_EXCPTION;
            }
        }
        // 新的发货流程不需要交易分销是否完结
        boolean ifGxConsignStartWithFx = Objects.equals(1, MDC.get(IF_GX_CONSIGN_START_WITH_FX));
        if (!ifGxConsignStartWithFx && (fxOrMixTrade != null && SendType.UPLOAD != consignType &&
                (Trade.SYS_STATUS_FINISHED.equals(fxOrMixTrade.getSysStatus())|| Trade.SYS_STATUS_CLOSED.equals(fxOrMixTrade.getSysStatus())))) {
                return "分销订单已完结";
        }
        //分销且供销的订单，继续校验其上级订单的异常状态
        return checkFxTradeExcept(staff,fxOrMixTrade, fxTradeMap, consignType);
    }

    public static boolean judgeUserAuthority(ConsignRecordQueryParams params,Staff staff){
        String wareHouseId = params.getWarehouseId();
        String wareHouseGroup = staff.getWarehouseGroup();
        String extWareHouseGroup = staff.getWarehouseExtraGroup();
        String storeWareHouseGroup = staff.getWarehouseStoreGroup();
        Set<String> wareHouseGroupSet = new HashSet<>();
        Set<String> userWareHouseIdSet = ArrayUtils.toStringSet(wareHouseId);
        if(StringUtils.isNotBlank(wareHouseGroup)){//自有仓库
            wareHouseGroupSet.addAll(ArrayUtils.toStringSet(wareHouseGroup));
        }
        if(StringUtils.isNotBlank(extWareHouseGroup)){//三方仓
            wareHouseGroupSet.addAll(ArrayUtils.toStringSet(extWareHouseGroup));
        }
        if(StringUtils.isNotBlank(storeWareHouseGroup)){//门店仓
            wareHouseGroupSet.addAll(ArrayUtils.toStringSet(storeWareHouseGroup));
        }
        if(wareHouseGroupSet.size()==0){//用户无仓库权限
            return false;
        }
        if (StringUtils.isBlank(wareHouseId)) {
            params.setWarehouseId(wareHouseGroupSet.stream().collect(Collectors.joining(",")));
            return true;
        }
        List<String> resultIdList = new ArrayList<>();
        for(String id : userWareHouseIdSet){
            if(wareHouseGroupSet.contains(id)){
                resultIdList.add(id);
            }
        }
        if(resultIdList.size()==0){
            return false;
        }
        params.setWarehouseId(resultIdList.stream().collect(Collectors.joining(",")));
        return true;
    }
    public static void filterKttTrade(Staff staff, List<Trade> trades , List<Trade> failTradeList) {
        if(CollectionUtils.isEmpty(trades)){
            return;
        }
        //发货过滤快麦快团团订单
        List<Trade> kttTrades = trades.stream().filter(t -> TradeUtils.isKttHelpSaleTrade(t)).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(kttTrades)){
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append("快团团帮卖订单禁止发货：").append(Strings.join(",", kttTrades.stream().map(TradeBase::getSid).collect(Collectors.toList()))));
            }
            failTradeList.addAll(kttTrades);
            trades.removeAll(kttTrades);
        }
    }
}

package com.raycloud.dmj.services.trade.split;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.split.SplitCheckResult;
import com.raycloud.dmj.domain.trade.split.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.split.*;

import java.util.*;

/**
 * @ClassName ITradeSplitService
 * @Description 拆单接口
 * <AUTHOR>
 * @Date 2023/12/21
 * @Version 1.0
 */
public interface ITradeSplitService {

    /**
     * 按sku拆分
     *
     * @param sid     需要拆分的系统单号
     * @param orderId 需要拆分的子订单号
     * @return 拆单结果
     */
    SplitResult splitSku(Staff staff, Long sid, Long orderId);

    /**
     * 按数量拆分
     *
     * @param sid     需要拆分的系统单号
     * @param orderId 需要拆分的子订单号
     * @param count   需要拆分成多少单
     * @param num     拆分出来的订单，每个子订单的数量
     * @return 拆单结果
     */
    SplitResult splitNum(Staff staff, Long sid, Long orderId, int count, int num);

    /**
     * 混合拆分
     *
     * @param mixTrade      需要拆分的订单
     * @param groupCount    拆分成多少组
     * @param splitFx2GxNew 采取供分销拆分新模式
     * @return 拆单结果
     */
    SplitResult splitMix(Staff staff, TbTrade mixTrade, String groupCount, boolean splitFx2GxNew);

    /**
     * 批量混合拆分
     *
     * @param tradeGroupCount 批量拆分参数
     * @param splitFx2GxNew   是否采取供分销拆分新模式
     * @return 拆单结果
     */

    SplitResult splitMixBatch(Staff staff, Map<Trade, String> tradeGroupCount, boolean splitFx2GxNew);


    /**
     * 退款拆分
     *
     * @param sids 需要拆分的系统单号
     * @return 拆单结果
     */
    SplitResult splitRefund(Staff staff, Long[] sids);


    /**
     * 未匹配拆分
     *
     * @param sids 需要拆分的系统单号
     * @return 拆单结果
     */
    SplitResult splitUnmatched(Staff staff, Long[] sids);

    /**
     * 缺货拆分
     *
     * @param sids 需要拆分的系统单号
     * @return 拆单结果
     */
    SplitResult splitInsufficient(Staff staff, Long[] sids);

    /**
     * 按供销拆单
     *
     * @param sids 需要拆分的系统单号
     * @return 拆单结果
     */
    SplitResult splitSupply(Staff staff, Long[] sids, boolean isContinue);

    /**
     * @param sids   需要拆分的系统单号
     * @param weight 重量
     * @return 拆单结果
     */
    SplitResult splitWeight(Staff staff, Long[] sids, double weight);

    SplitResult splitConfig(Staff staff, Long[] sids, List<SplitConfigRule> rules);

    /**
     * 按配置拆分（一键拆分、自动拆分都走这个。splitOneClick、splitAuto）
     *
     * @param sids  需要拆分的系统单号
     * @param rules 配置的规则
     * @param splitFx2GxNew 供分销拆分模式
     * @return 拆单结果
     */
    SplitResult splitConfig(Staff staff, Long[] sids, List<SplitConfigRule> rules, boolean splitFx2GxNew);

    SplitResult splitConfigAuto(Staff staff, Long[] sids, List<SplitConfigRule> rules, EventEnum event);

    /**
     * 已审核按sku拆分
     *
     * @param sid      需要拆分的系统单号
     * @param orderIds 需要拆分的子订单号
     * @return 拆单结果
     */
    SplitResult splitAuditedKind(Staff staff, Long sid, Long[] orderIds);

    /**
     * 已审核按数量拆分
     *
     * @param splitEnum              拆分类型枚举
     * @param sid                    需要拆分的系统单号
     * @param splitOrders            需要拆分的子订单
     * @param sourceAndNewOrderIdMap 需要拆分的子订单
     * @param needUnAudit            拆分出来的订单是否需要反审核
     * @param splitFx2GxNew          是否采取供分销拆分新模式
     * @return 拆单结果
     */
    SplitResult splitAuditedNum(Staff staff, TradeSplitEnum splitEnum, Long sid, List<Order> splitOrders, Map<Long, Long> sourceAndNewOrderIdMap, boolean needUnAudit, boolean splitFx2GxNew);

    SplitAuditedNumResponse splitAuditedNum(Staff staff, SplitAuditedNumRequest req);

    /**
     * 拣选拆分
     *
     * @param sid         需要拆分的系统单号
     * @param splitOrders 需要拆分的子订单
     * @return 拆单结果
     */
    SplitResult splitPick(Staff staff, Long sid, List<Order> splitOrders);

    /**
     * 商品停用拆单
     *
     * @param sids             需要拆分的系统单号
     * @param inactiveItemKeys 需要操作的商品（商品通过事件传递过来）
     * @return 拆单结果
     */
    SplitResult splitInactiveItem(Staff staff, Long[] sids, Set<String> inactiveItemKeys);

    /**
     * 按箱规则拆单
     *
     * @param sids           需要拆分的系统单号
     * @param includeSplit 是否包含拆单
     * @param includeMerge 是否包含合单
     * @return 拆单结果
     */
    SplitResult splitDefaultBoxRule(Staff staff, Long[] sids, boolean includeSplit, boolean includeMerge);

    /**
     * 自动流转拆分
     *
     * @param sids 需要拆分的系统单号
     * @return 拆单结果
     */
    SplitResult splitAutoLz(Staff staff, Long[] sids);

    /**
     * 取消拆分
     *
     * @param splitSids  需要取消拆分的系统单号
     * @param shopeeUndo
     * @return 拆单结果
     */
    List<Trade> splitUndo(Staff staff, Long[] splitSids, boolean shopeeUndo);

    /**
     * 取消拆单 重新匹配赠品，仓库，快递模板
     */
    void splitUndoRecalculationUpdate(Staff staff, List<Trade> originTrades);

    List<SplitCheckResult> splitPlatGiftCheck(Staff staff, List<Long> orderIds);


    public SplitResult splitOZONOrder(Staff staff, List<Trade> trades);


}

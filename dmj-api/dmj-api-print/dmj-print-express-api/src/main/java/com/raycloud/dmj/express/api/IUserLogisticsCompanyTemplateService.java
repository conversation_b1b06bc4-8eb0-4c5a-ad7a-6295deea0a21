package com.raycloud.dmj.express.api;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompany;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompanyTemplate;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.express.request.template.*;
import com.raycloud.dmj.express.response.template.CompanyAbroadTemplateResponse;
import com.raycloud.dmj.express.response.template.CompanyTemplateResponse;
import com.raycloud.dmj.express.response.template.LogisticsCompanyTemplateQueryDTO;
import com.raycloud.dmj.express.vo.TemplateBranchAddressVO;
import com.raycloud.dmj.express.vo.TemplateBranchVO;
import com.raycloud.dmj.waybill.common.params.ResultInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Date 2022/12/27
 * <AUTHOR>
 */
public interface IUserLogisticsCompanyTemplateService {

    /**
     * 查询电子面单列表
     */
    List<LogisticsCompanyTemplateQueryDTO> queryTemplateList(CompanyTemplateQueryRequest param);

    /**
     * 保存电子面单配置
     */
    void save(CompanyTemplateSaveRequest request);

    /**
     * 新增网点
     */
    TemplateBranchVO saveBranch(CompanyTemplateSaveBranchRequest request);

    /**
     * 修改网点
     */
    void updateBranch(CompanyTemplateSaveBranchRequest request);

    /**
     * 删除网点
     */
    void deleteBranch(CompanyTemplateDeleteBranchRequest request);

    /**
     * 新增网点地址
     */
    TemplateBranchAddressVO saveBranchAddress(CompanyTemplateSaveBranchAddressRequest request);

    /**
     * 修改网点地址
     */
    void updateBranchAddress(CompanyTemplateUpdateBranchAddressRequest request);

    /**
     * 删除网点地址
     */
    void deleteBranchAddress(CompanyTemplateDeleteBranchAddressRequest request);

    /**
     * 设置默认模板
     */
    void defaultTemplate(CompanyTemplateDefaultRequest param);

    /**
     * 修改快递公司
     * @param staff
     * @param request
     * @return
     */
    CompanyTemplateResponse saveTemplateIdToTrade(Staff staff, CompanyTemplateSaveRequest request);

    /**
     * 批量回收运单号*
     * @param staff
     * @param param
     * @return
     */
    Object batchCannelWaybill(Staff staff, WaybillBatcjCancelRequest param);

    /**
     * * 强制回收更新处理
     * @param staff
     * @param errorList
     * @param tradeList
     */
    void doForceRecycling(Staff staff, List<ResultInfo> errorList, List<Trade> tradeList);


    /**
     * userLogisticsCompanyList
     */
    Map<Long, Map<Long,UserExpressTemplate>> matchOldTemplateMap(Staff staff, List<Trade> tradeList, List<UserLogisticsCompany> userLogisticsCompanyList, Staff gxStaff);


    //通过快递公司id获取对应的wleTemplateTypes
    Map<Long, Set<Integer>> getLogisticsCompanyWlbTemplateTypes(Staff staff, Set<Long> userLogisticsCompanyIds);

    /**
     * 根据logisticsCompanyId匹配老模版数据
     * @param staff
     * @param tradeList
     * @param userLogisticsCompany
     * @return
     */
    Map<Long, UserExpressTemplate> matchOldTemplateProxy(Staff staff, List<Trade> tradeList, UserLogisticsCompany userLogisticsCompany);

    /**
     * 跨境订单修改模板
     *
     * @param request
     * @return
     */
    CompanyAbroadTemplateResponse replaceAbroad(CompanyAbroadTemplateSaveRequest request);

    /**
     * @param staff
     * @param logisticsCompany
     * @return com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompanyTemplate
     * @description: 获取公司与模版关联设置 没有则新增
     * @author: tanyi
     * @date: 2025-03-13 16:50
     */
    UserLogisticsCompanyTemplate getZiTiUserLogisticsCompanyTemplate(Staff staff, UserLogisticsCompany logisticsCompany);
}

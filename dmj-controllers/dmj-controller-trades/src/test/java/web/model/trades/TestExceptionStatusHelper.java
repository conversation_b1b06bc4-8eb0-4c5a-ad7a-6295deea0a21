package web.model.trades;//package com.raycloud.dmj.web.model.trades;
//
//import com.raycloud.dmj.domain.account.Company;
//import com.raycloud.dmj.domain.account.CompanyProfile;
//import com.raycloud.dmj.domain.account.Staff;
//import com.raycloud.dmj.domain.trades.Order;
//import com.raycloud.dmj.domain.trades.TbOrder;
//import com.raycloud.dmj.domain.trades.TbTrade;
//import com.raycloud.dmj.domain.trades.Trade;
//import com.raycloud.dmj.domain.trades.utils.TradeUtils;
//import com.raycloud.dmj.domain.user.User;
//import com.raycloud.dmj.domain.utils.CommonConstants;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//public class TestExceptionStatusHelper {
//
//	private Long companyId = 1000001L;
//
//	private Staff staff;
//
//	private User user;
//
//	@Before
//	public void setUp(){
//		staff = new Staff();
//		staff.setCompanyId(companyId);
//		staff.setName("Jacky");
//		Company company = new Company();
//		company.setId(companyId);
//		CompanyProfile p = new CompanyProfile();
//		p.setCompanyId(companyId);
//		company.setProfile(p);
//		staff.setCompany(company);
//
//		user = new User();
//		user.setActive(1);
//		user.setId(10L);
//		user.setTaobaoId(10001L);
//		user.setCompanyId(staff.getCompanyId());
//		user.setStaff(staff);
//
//		Map<Long, User> userIdMap = new HashMap<Long, User>();
//		userIdMap.put(user.getId(), user);
//		staff.setUserIdMap(userIdMap);
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试正常的订单
//	 */
//	@Test
//	public void testAnalyze1() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		Order order = TradeUtils.getOrders4Trade(trade).get(0);
//		Assert.assertEquals("", ExceptionStatusHelper.analyze(trade, order));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试挂起的订单
//	 */
//	@Test
//	public void testAnalyze2() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		trade.setIsHalt(1);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(""));
//		Assert.assertEquals(TradeModel.ES_HALT, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试店铺停用的订单
//	 */
//	@Test
//	public void testAnalyze3() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		user.setActive(0);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(""));
//		Assert.assertEquals(TradeModel.ES_USER_UNACTIVE, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试子订单退款的订单，其中退款状态等待卖家同意退款
//	 */
//	@Test
//	public void testAnalyze4() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		Order order = TradeUtils.getOrders4Trade(trade).get(0);
//		order.setRefundId("123");
//		order.setRefundStatus(TbOrder.REFUND_WAIT_SELLER_AGREE);
//		Assert.assertEquals(TradeModel.ES_REFUND, ExceptionStatusHelper.analyze(trade, order));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试子订单退款的订单，其中退款状态为退款成功，那么异常状态为空
//	 */
//	@Test
//	public void testAnalyze5() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		Order order = TradeUtils.getOrders4Trade(trade).get(0);
//		order.setRefundId("123");
//		order.setRefundStatus(TbOrder.REFUND_SUCCESS);
//		Assert.assertEquals("", ExceptionStatusHelper.analyze(trade, order));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试子订单为商品未匹配状态
//	 */
//	@Test
//	public void testAnalyze6() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		Order order = TradeUtils.getOrders4Trade(trade).get(0);
//		order.setStockStatus(Trade.STOCK_STATUS_UNALLOCATED);
//		Assert.assertEquals(TradeModel.ES_UNALLOCATED, ExceptionStatusHelper.analyze(trade, order));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试子订单为商品库存不足状态
//	 */
//	@Test
//	public void testAnalyze7() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		Order order = TradeUtils.getOrders4Trade(trade).get(0);
//		order.setStockStatus(Trade.STOCK_STATUS_INSUFFICIENT);
//		Assert.assertEquals(TradeModel.ES_STOCK_INSUFFICIENT, ExceptionStatusHelper.analyze(trade, order));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试子订单为商品对应关系修改的状态
//	 */
//	@Test
//	public void testAnalyze8() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		Order order = TradeUtils.getOrders4Trade(trade).get(0);
//		order.setStockStatus(Trade.STOCK_STATUS_RELATION_MODIFIED);
//		Assert.assertEquals(TradeModel.ES_ITEM_RELATION_MODIFIED, ExceptionStatusHelper.analyze(trade, order));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试主订单为挂起状态，子订单为退款状态，那么异常状态为退款状态
//	 */
//	@Test
//	public void testAnalyze9() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		trade.setIsHalt(1);
//		Order order = TradeUtils.getOrders4Trade(trade).get(0);
//		order.setRefundId("123");
//		order.setRefundStatus(TbOrder.REFUND_WAIT_SELLER_AGREE);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, order)));
//		Assert.assertEquals(TradeModel.ES_REFUND, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试一笔订单状态即为店铺停用也为挂起状态的订单，那么异常状态为店铺停用
//	 */
//	@Test
//	public void testAnalyze10() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		user.setActive(0);
//		trade.setIsHalt(1);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(""));
//		Assert.assertEquals(TradeModel.ES_USER_UNACTIVE, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试一笔订单有两笔子订单，如果其中一笔为退款，另一笔为商品未匹配，那么异常状态未退款
//	 */
//	@Test
//	public void testAnalyze11() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L, 2L);
//		List<Order> orders = TradeUtils.getOrders4Trade(trade);
//		Order o1 = orders.get(0);
//		Order o2 = orders.get(1);
//		o1.setRefundId("123");
//		o1.setRefundStatus(TbOrder.REFUND_WAIT_SELLER_AGREE);
//		o2.setStockStatus(Trade.STOCK_STATUS_UNALLOCATED);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o1)));
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o2)));
//		Assert.assertEquals(TradeModel.ES_REFUND, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试一笔订单有两笔子订单，如果其中一笔为退款，另一笔为商品库存不足，那么异常状态为退款
//	 */
//	@Test
//	public void testAnalyze12() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L, 2L);
//		List<Order> orders = TradeUtils.getOrders4Trade(trade);
//		Order o1 = orders.get(0);
//		Order o2 = orders.get(1);
//		o1.setRefundId("123");
//		o1.setRefundStatus(TbOrder.REFUND_WAIT_SELLER_AGREE);
//		o2.setStockStatus(Trade.STOCK_STATUS_INSUFFICIENT);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o1)));
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o2)));
//		Assert.assertEquals(TradeModel.ES_REFUND, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试一笔订单有两笔子订单，如果其中一笔为商品未匹配，另一笔为商品库存不足，那么异常状态为商品未匹配
//	 */
//	@Test
//	public void testAnalyze13() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L, 2L);
//		List<Order> orders = TradeUtils.getOrders4Trade(trade);
//		Order o1 = orders.get(0);
//		Order o2 = orders.get(1);
//		o1.setStockStatus(Trade.STOCK_STATUS_UNALLOCATED);
//		o2.setStockStatus(Trade.STOCK_STATUS_INSUFFICIENT);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o1)));
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o2)));
//		Assert.assertEquals(TradeModel.ES_UNALLOCATED, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试一笔订单，如果其中一笔为商品库存不足，主订单为挂起状态，那么异常状态为商品库存不足状态
//	 */
//	@Test
//	public void testAnalyze14() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L);
//		trade.setIsHalt(1);
//		List<Order> orders = TradeUtils.getOrders4Trade(trade);
//		Order o1 = orders.get(0);
//		o1.setStockStatus(Trade.STOCK_STATUS_INSUFFICIENT);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o1)));
//		Assert.assertEquals(TradeModel.ES_STOCK_INSUFFICIENT, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，订单为天猫订单，测试一笔订单有两笔子订单，如果其中一笔为退款，另一笔为商品库存不足，那么异常状态为退款
//	 */
//	@Test
//	public void testAnalyze15() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L, 2L);
//		trade.setSource(CommonConstants.PLAT_FORM_TYPE_TIAN_MAO);
//		List<Order> orders = TradeUtils.getOrders4Trade(trade);
//		Order o1 = orders.get(0);
//		o1.setSource(CommonConstants.PLAT_FORM_TYPE_TIAN_MAO);
//		Order o2 = orders.get(1);
//		o2.setSource(CommonConstants.PLAT_FORM_TYPE_TIAN_MAO);
//		o1.setRefundId("123");
//		o1.setRefundStatus(TbOrder.REFUND_WAIT_SELLER_AGREE);
//		o2.setStockStatus(Trade.STOCK_STATUS_INSUFFICIENT);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o1)));
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o2)));
//		Assert.assertEquals(TradeModel.ES_REFUND, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，订单为天猫订单，测试一笔订单有两笔子订单，如果其中一笔为退款成功的订单，另一笔为商品库存不足，那么异常状态为库存不足
//	 */
//	@Test
//	public void testAnalyze16() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L, 2L);
//		trade.setSource(CommonConstants.PLAT_FORM_TYPE_TIAN_MAO);
//		List<Order> orders = TradeUtils.getOrders4Trade(trade);
//		Order o1 = orders.get(0);
//		o1.setSource(CommonConstants.PLAT_FORM_TYPE_TIAN_MAO);
//		Order o2 = orders.get(1);
//		o2.setSource(CommonConstants.PLAT_FORM_TYPE_TIAN_MAO);
//		o1.setRefundId("123");
//		o1.setRefundStatus(TbOrder.REFUND_SUCCESS);
//		o2.setStockStatus(Trade.STOCK_STATUS_INSUFFICIENT);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o1)));
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o2)));
//		Assert.assertEquals(TradeModel.ES_STOCK_INSUFFICIENT, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	/**
//	 * 测试订单的异常状态分析，测试一笔订单有两笔子订单，如果其中一笔为退款中但是售后改为继续发货的退款状态的订单，另一笔为商品库存不足，那么异常状态为库存不足
//	 */
//	@Test
//	public void testAnalyze17() {
//		Trade trade = createEmptyTrade(Trade.SYS_STATUS_WAIT_AUDIT, 1L, 1L, 2L);
//		List<Order> orders = TradeUtils.getOrders4Trade(trade);
//		Order o1 = orders.get(0);
//		Order o2 = orders.get(1);
//		o1.setRefundId("123");
//		o1.setRefundStatus(TbOrder.REFUND_SELLER_CONTINUE_CONSIGN);
//		o2.setStockStatus(Trade.STOCK_STATUS_INSUFFICIENT);
//		TradeModel tm = new TradeModel();
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o1)));
//		tm.getOrders().add(new OrderModel().setExceptionStatus(ExceptionStatusHelper.analyze(trade, o2)));
//		Assert.assertEquals(TradeModel.ES_STOCK_INSUFFICIENT, ExceptionStatusHelper.analyze(staff, trade, tm));
//	}
//
//	private Trade createEmptyTrade(String sysStatus, long sid, long... oids ){
//		TbTrade trade = new TbTrade();
//		trade.setSid(sid);
//		trade.setSysStatus(sysStatus);
//		trade.setTaobaoId(user.getTaobaoId());
//		trade.setUserId(user.getId());
//		trade.setCreated(new Date());
//		trade.setModified(new Date());
//		trade.setCompanyId(staff.getCompanyId());
//		trade.setSource("tb");
//		trade.setTid(sid+"");
//		trade.setStockStatus(Trade.STOCK_STATUS_NORMAL);
//		trade.setWarehouseId(1L);
//		trade.setWarehouseName("默认仓库");
//
//		for(long oid : oids){
//			TbOrder order = new TbOrder();
//			order.setOid(oid);
//			order.setId(oid);
//			order.setSid(sid);
//			order.setTid(trade.getTid());
//			order.setNum(1);
//			order.setSysStatus(sysStatus);
//			order.setStockStatus(Trade.STOCK_STATUS_NORMAL);
//			order.setStockNum(order.getNum());
//			trade.getOrders().add(order);
//		}
//
//		return trade;
//	}
//
//}

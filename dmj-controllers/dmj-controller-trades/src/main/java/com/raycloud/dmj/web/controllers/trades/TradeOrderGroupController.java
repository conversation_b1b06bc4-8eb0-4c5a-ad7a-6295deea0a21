package com.raycloud.dmj.web.controllers.trades;

import com.alibaba.fastjson.JSONArray;
import com.raycloud.dmj.business.trade.TradeSwitchBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.trades.IProgressService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

@RestController
@RequestMapping("/trade/order")
@Scope("prototype")
@LogTag(value = "trade", enableArgs = "true", enableResponse = "true")
public class TradeOrderGroupController extends Sessionable {

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeQueryService;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private IProgressService progressService;
    @Resource
    private TradeSwitchBusiness tradeSwitchBusiness;
    @Resource
    private IDownloadCenterService downloadCenterService;

    /**
     * 解析jsonArray  并填充数据
     *
     * @param array
     * @return
     */
    public static List<List<String>> parseJsonArray(JSONArray array) {
        List<List<String>> result = new ArrayList<>();
        int maxLength = 0;
        for (int i = 0; i < array.size(); i++) {
            Object o = array.get(i);
            JSONArray objects1 = JSONArray.parseArray(String.valueOf(o));
            List<String> list2 = new ArrayList<>();
            for (Object o1 : objects1) {
                list2.add(String.valueOf(o1));
            }
            if (list2.size() > maxLength) {
                maxLength = list2.size();
            }
            result.add(list2);
        }
        for (List<String> list : result) {
            int emptyStrCount = maxLength - list.size();
            if (emptyStrCount > 0) {
                for (int i = 0; i < emptyStrCount; i++) {
                    list.add("");
                }
            }
        }
        return result;
    }

    /**
     * 商品汇总功能 https://gykj.yuque.com/docs/share/78f499e6-b53e-404d-9f35-dad7823551f1?#
     */
    @RequestMapping(value = "/group", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object group(TradeControllerParams queryParams, String api_name, Integer filterId, Boolean querySuitStock, String warehouseIds, String sortFields, String orderBy) throws SessionException {
        Staff staff = getStaff();
        tradeSwitchBusiness.checkOpen(ProgressEnum.PROGRESS_TRADE_ORDER_GROUP.getKey() + "_switch", staff.getCompanyId());
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_TRADE_ORDER_GROUP), "正在进行订单商品分组，请稍后重试！");
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_TRADE_ORDER_GROUP), "其他员工正在进行订单商品分组，请稍后重试!");
        try {
            initAndCheckSearchInsufficientParams(staff, queryParams);
        } catch (Exception ex) {
            progressService.clearProgress(staff, ProgressEnum.PROGRESS_TRADE_ORDER_GROUP);
            throw ex;
        }
        TradeOrderGroupSummaryParams groupSummaryParams = TradeOrderGroupSummaryParams.builder()
                .sortFields(sortFields)
                .filterId(filterId)
                .querySuitStock(querySuitStock)
                .warehouseIds(warehouseIds)
                .orderBy(orderBy)
                .build();
        TradeOrderGroupQueryParams params = new TradeOrderGroupQueryParams(TradeQueryParams.copyParams(queryParams), groupSummaryParams);
        eventCenter.fireEvent(this, new EventInfo("trade.order.group").setArgs(new Object[]{staff, params, IpUtils.getClientIP(request)}), null);
        return successResponse();
    }

    @RequestMapping(value = "/group/export", method = RequestMethod.POST)
    @ResponseBody
    public Object exportData(@RequestBody JSONArray array, String name, String api_name) throws SessionException, UnsupportedEncodingException {
        if (array == null) {
            throw new IllegalArgumentException("请传入需要导出的数据");
        }
        Staff staff = getStaff();
        List<List<String>> datas = null;
        try {
            datas = parseJsonArray(array);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "转换导出数据出错"));
            throw new IllegalArgumentException("转换导出数据出错");
        }
        if (CollectionUtils.isEmpty(datas)) {
            throw new IllegalArgumentException("无需要导出的数据");
        }
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.TRADE.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }
        //需要导出的数据
        String[][] excelHeader = new String[1][];
        String[] EXPORT_EXCEL = new String[datas.get(0).size()];
        for (int i = 0; i < EXPORT_EXCEL.length; i++) {
            EXPORT_EXCEL[i] = "";
        }
        excelHeader[0] = EXPORT_EXCEL;
        if (StringUtils.isEmpty(name)) {
            name = "商品汇总";
        }
        String fileName = new String(name.getBytes(), StandardCharsets.UTF_8);
        fileName += DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";
        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle(name);
        param.setTitleArr(excelHeader);
        param.setModule(EnumDownloadCenterModule.TRADE.getCode());
        eventCenter.fireEvent(this, new EventInfo("trades.print.data.export").setArgs(new Object[]{staff, param, array}), false);
        return successResponse();
    }

    private void initAndCheckSearchInsufficientParams(Staff staff, TradeControllerParams queryParams) {
        if (StringUtils.isNotEmpty(queryParams.getWarehouseId())) {
            if (StringUtils.split(queryParams.getWarehouseId(), ",").length > 6) {
                throw new IllegalArgumentException("最多选择6个仓库，请检查仓库数量！");
            }
        }
//        if (queryParams.getQueryId() != null && SystemTradeQueryParamsContext.QUERY_UN_CONSIGNED == queryParams.getQueryId()){
//            //TODO 感觉有问题
//            if (StringUtils.isEmpty(queryParams.getSysStatus())){
//                queryParams.setSysStatus(Strings.join(",", COUNT_STATUS));
//            }else {
//                queryParams.setSysStatus(Strings.join(",",CollectionUtils.intersection(Collections.singletonList(queryParams.getSysStatus()),COUNT_STATUS)));
//            }
//        }
        TradeQueryParams query = TradeQueryParams.copyParams(queryParams);
        query.setQueryFlag(2);//只查询总数
        query.setQueryOrder(false);//不查询order
        query.setIsCancel(0);
        Trades searchResult = tradeQueryService.search(staff, query);
        if (searchResult.getTotal() != null && searchResult.getTotal() <= 0) {
            throw new IllegalArgumentException("请选择满足要求的订单后操作！");
        }
        query.setQueryFlag(null);
        query.setQueryOrder(null);
    }
}

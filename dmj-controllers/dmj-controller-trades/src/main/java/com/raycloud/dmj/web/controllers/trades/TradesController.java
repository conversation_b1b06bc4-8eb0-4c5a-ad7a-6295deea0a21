package com.raycloud.dmj.web.controllers.trades;

import com.alibaba.fastjson.*;
import com.google.common.collect.*;
import com.raycloud.dmj.*;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.except.TradeRefundItemNumExceptBusines;
import com.raycloud.dmj.business.fx.*;
import com.raycloud.dmj.business.gift.GiftMatchManualBusiness;
import com.raycloud.dmj.business.item.replace.TradeReplaceNullityItemBusiness;
import com.raycloud.dmj.business.modify.GxTradeItemModifyBusiness;
import com.raycloud.dmj.business.operate.*;
import com.raycloud.dmj.business.order.OrderModifyLogBusiness;
import com.raycloud.dmj.business.packma.ItemPackmaBusiness;
import com.raycloud.dmj.business.payment.*;
import com.raycloud.dmj.business.tag.PlatSellerFlagTagBusiness;
import com.raycloud.dmj.business.trade.*;
import com.raycloud.dmj.business.tradeext.TradeExtBusiness;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.*;
import com.raycloud.dmj.domain.annotation.*;
import com.raycloud.dmj.domain.base.CommonModel;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.item.box.*;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.payment.*;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.ExpressPercentageMatch;
import com.raycloud.dmj.domain.pt.vo.PrintTradeLogDetailVO;
import com.raycloud.dmj.domain.request.SecurityEventTrackingBatchOrderRequest;
import com.raycloud.dmj.domain.trade.common.TradeBusinessUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.except.ExceptHandlerDto;
import com.raycloud.dmj.domain.trade.except.ExceptHandlerDto;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.history.OrderModifyLogUtils;
import com.raycloud.dmj.domain.trade.memo.*;
import com.raycloud.dmj.domain.trade.sync.TradeSyncUtils;
import com.raycloud.dmj.domain.trade.type.*;
import com.raycloud.dmj.domain.trade.warehouse.TradeWarehouseUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.audit.CancelData;
import com.raycloud.dmj.domain.trades.params.*;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.request.*;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.vo.MultiUserTradeDownVo;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.utils.diamond.TradeSyncConfigUtils;
import com.raycloud.dmj.domain.wave.WaveConfig;
import com.raycloud.dmj.domain.wave.enums.WaveChatConfigsEnum;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.except.domain.ExceptData;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.repair.domain.Response.RepairOrderResponse;
import com.raycloud.dmj.repair.service.IAsRepairService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.ec.TradeUpdateSuiteLatestListener;
import com.raycloud.dmj.services.filter.support.DefaultTemplateFilter;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.platform.basis.*;
import com.raycloud.dmj.services.platform.trades.*;
import com.raycloud.dmj.services.pt.smart_match.IExpressPercentageMatchService;
import com.raycloud.dmj.services.trace.IItemTraceService;
import com.raycloud.dmj.services.trade.audit.ITradeAuditService;
import com.raycloud.dmj.services.trade.warehouse.TradeWarehouseChangeBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.fill.*;
import com.raycloud.dmj.services.trades.filter.TradeFilterException;
import com.raycloud.dmj.services.trades.support.*;
import com.raycloud.dmj.services.trades.support.utils.*;
import com.raycloud.dmj.services.trades.wave.IWaveServiceDubbo;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.storage.domain.FileResult;
import com.raycloud.dmj.storage.service.IFileUploadService;
import com.raycloud.dmj.storage.utils.ExcelConverter;
import com.raycloud.dmj.tb.common.TbAppInfo;
import com.raycloud.dmj.utils.ItemTraceMessageBuilder;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.web.*;
import com.raycloud.dmj.web.controllers.TradeBaseController;
import com.raycloud.dmj.web.controllers.help.OrderStockNumBusiness;
import com.raycloud.dmj.web.helper.ImportHelper;
import com.raycloud.dmj.web.interceptors.RequestBodyParamsUtils;
import com.raycloud.dmj.web.model.trades.*;
import com.raycloud.dmj.web.model.trades.as.AsRepairWeighInfo;
import com.raycloud.dmj.web.util.*;
import com.raycloud.dmj.web.utils.*;
import com.raycloud.ec.api.EventInfo;
import com.taobao.api.internal.spi.*;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.*;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.Strings.*;

@Controller
@RequestMapping("/trade")
@Scope("prototype")
@LogTag(value = "trade", enableArgs = "true", enableResponse = "true")
public class TradesController extends TradeBaseController {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    ISysTradeService sysTradeService;

    @Resource
    ITradeFillService tradeFillService;

    @Resource
    ISysTradeDmsService sysTradeDmsService;

    @Resource
    FxBusiness fxBusiness;

    @Resource
    ITradeService tradeService;
    @Resource
    ITradeAuditService tradeAuditService;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    ITradeDataService tradeDataService;

    @Resource
    ILogisticsTrackingRecordService logisticsTrackingRecordService;

    @Resource
    IItemServiceWrapper itemServiceWrapper;

    @Resource
    ITradeCancelExceptService tradeCancelExceptService;

    @Resource
    DefaultTemplateFilter defaultTemplateFilter;

    @Resource
    IExpressPercentageMatchService expressPercentageMatchService;

    @Resource
    TradeSwitchBusiness tradeSwitchBusiness;

    @Resource
    ITradeTraceService tradeTraceService;

    @Autowired
    IItemTraceService itemTraceService;

    @Autowired
    HttpServletRequest request;

    @Resource
    ITradeQueryService tradeQueryService;
    @Resource
    TradeValidateBusiness tradeValidateBusiness;

    @Resource
    OrderStockNumBusiness orderStockNumBusiness;

    @Resource
    StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    IFileUploadService fileUploadService;

    @Resource
    IFastSysMemoService fastSysMemoService;

    @Resource
    FxgTradeDecryptBusiness fxgTradeDecryptBusiness;

    @Resource
    KsTradeDecryptBusiness ksTradeDecryptBusiness;

    @Resource
    FxgSpiService fxgSpiService;

    @Resource
    IWaveServiceDubbo waveServiceDubbo;

    @Resource
    OrderModifyLogBusiness orderModifyLogBusiness;

    @Resource
    IStaffService staffService;

    @Resource
    CommonTradeDecryptBusiness commonTradeDecryptBusiness;

    @Resource
    ITradeFill tradeOrderSyncItemTagFill;

    @Resource
    TradeExtBusiness tradeExtBusiness;

    @Resource
    TradeStaffConfigService tradeStaffConfigService;

    @Resource
    TradeOpenUidTransformBusiness tradeOpenUidTransformBusiness;

    @Resource
    IOrderProductService orderProductService;
    @Resource
    TradeRefundItemNumExceptBusines tradeRefundItemNumExceptBusines;

    @Resource
    TradeItemExchangePaymentBusinessService tradeItemExchangePaymentBusinessService;

    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    GiftMatchManualBusiness giftMatchManualBusiness;

    @Resource
    Configurable config;

    @Resource
    GxTradeItemModifyBusiness gxTradeItemModifyBusiness;

    @Resource
    TradeItemChangeBusinessService tradeItemChangeBusinessService;

    @Resource
    PlatSellerFlagTagBusiness platSellerFlagTagBusiness;

    @Resource
    TradeUpdateSellerMemoFlagBusiness tradeUpdateSellerMemoFlagBusiness;

    @Resource
    ItemPackmaBusiness itemPackmaBusiness;


    @Autowired(required = false)
    IAsRepairService asRepairService;

    @Resource
    SecretBusiness secretBusiness;

    @Resource
    FxMemoBusiness fxMemoBusiness;
    @Resource
    private SyncTradeBusiness syncTradeBusiness;

    @Resource
    TradeWarehouseChangeBusiness tradeWarehouseChangeBusiness;

    @Resource
    TradeTraceBusiness tradeTraceBusiness;

    @Resource
    PlatformManagement platformManagement;

    @RequestMapping(value = "/order/suits", method = RequestMethod.GET)
    @ResponseBody
    public Object querySuits(Long sid, Long orderId, String api_name) throws Exception {
        Staff staff = getStaff();
        TradeConfig tc = tradeConfigService.get(staff);

        List<Order> orders = tradeSearchService.listSuits(staff, orderId);
        if (CollectionUtils.isNotEmpty(orders)) {
            orderProductService.fill(staff, orders);
            // 填充异常信息
            List<Trade> tradeList = tradeSearchService.queryBySidsContainMergeTrade(staff, false, OrderUtils.toSids(orders));
            Map<Long, Trade> collect = tradeList.stream().collect(Collectors.toMap(Trade::getSid, Function.identity()));

            for (Order order : orders) {
                Trade trade = collect.get(order.getSid());
                if (trade != null) {
                    ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(trade);
                    List<ExceptData> tradeExceptDatas = TradeExceptUtils.getTradeExceptDatas(staff, tradeList);
                    // 归并一下子单的异常
                    TradeExceptUtils.joinOrderExcept(staff, tradeExceptData, tradeExceptDatas);
                    order.setExceptData(tradeExceptData);
                }
            }
        }
        List<OrderModel> orderModels = OrderModel.entity2Vo(staff, orders, false, tc);
        TradeModels tradeModels = TradeModels.toTradeModels(staff, tradeSearchService.queryBySids(staff, true, sid), shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
        if (!Objects.isNull(tradeModels) && CollectionUtils.isNotEmpty(tradeModels.getList())) {
            tradeModels.getList().get(0).setOrders(orderModels);
            stockInfoModel(staff, tradeModels);
        }
        orderStockNumBusiness.fillSuitStockNum(staff, orderModels);
        return orderModels;
    }

    /**
     * 更新系统备注
     */
    @RequestMapping(value = "/sysmemo/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateSysMemo(TradeSysMemoUpdateParams tradeSysMemoUpdateParams, TradeControllerParams params, HttpServletRequest request, String api_name) throws SessionException {
        Staff staff = getStaff();
        tradeSysMemoUpdateParams.setParams(params);
        String fastSysMemoId = tradeSysMemoUpdateParams.getFastSysMemoId();
        Long[] longs = ArrayUtils.toLongArray(fastSysMemoId);
        if (longs != null && longs.length > 0) {  //如果需要更新快捷备注使用时间
            fastSysMemoService.updateUseTime(staff, longs);
        }
        TradeQueryParams queryParams = TradeQueryParams.copyParams(tradeSysMemoUpdateParams.getParams());
        String updateSysMemo = WipeEmojiUtils.wipeEmoji(StringUtils.trimToEmpty(tradeSysMemoUpdateParams.getUpdateSysMemo()));
        if (Boolean.TRUE.equals(tradeSysMemoUpdateParams.getNotifyFx())){
            fxMemoBusiness.notifySysMemo2Fx(staff, updateSysMemo, tradeSysMemoUpdateParams.getSids());
        }
        //10条之内走同步逻辑 防止同步查询量过大
        boolean isSync = StringUtils.isNotEmpty(tradeSysMemoUpdateParams.getSids()) && ArrayUtils.toLongArray(tradeSysMemoUpdateParams.getSids()).length <= 10;
        TradeMemo tradeMemo = TradeMemo.builder()
                .handType(TradeMemoTypeEnum.SYS_EMEO.getType())
                .sysMemo(updateSysMemo)
                .handFrom(TradeMemoFromEnum.WEB.getType())
                .handleMergeTrade(false)
                .notifyFx(tradeSysMemoUpdateParams.getNotifyFx())
                .build();
        if (tradeSysMemoUpdateParams.getUpdateType() == 1 || !isSync) {
            Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_TRADE_BACTH_UPDATE_SYSMEMO), "上一次批量修改系统备注未完成，请稍后重试!");
            eventCenter.fireEvent(this, new EventInfo("trade.update.memo").setArgs(new Object[]{staff, tradeMemo, queryParams}), null);
            OpLogHelper.recodeOpLog(opLogService, request, staff, "update_sysmemo", tradeSysMemoUpdateParams.getSids(), String.format("修改了系统备注，系统单号为：%s，内容为：%s", tradeSysMemoUpdateParams.getSids(), tradeSysMemoUpdateParams.getUpdateSysMemo()), JSON.toJSONString(tradeSysMemoUpdateParams));
            return ProgressUtils.getResponse(staff, ProgressEnum.PROGRESS_TRADE_BACTH_UPDATE_SYSMEMO.getKey(), 0);
        } else if (tradeSysMemoUpdateParams.getUpdateType() == null || tradeSysMemoUpdateParams.getUpdateType() == 0) {
            Assert.notNull(tradeSysMemoUpdateParams.getSids(), "请输入系统订单号");
            ProgressData progressData = new ProgressData();
            tradeUpdateSellerMemoFlagBusiness.update(staff, tradeMemo, queryParams, progressData);
            OpLogHelper.recodeOpLog(opLogService, request, staff, "update_sysmemo", tradeSysMemoUpdateParams.getSids(), String.format("修改了系统备注，系统单号为：%s，内容为：%s", tradeSysMemoUpdateParams.getSids(), tradeSysMemoUpdateParams.getUpdateSysMemo()), JSON.toJSONString(tradeSysMemoUpdateParams));
            TradeModels tradeModels = new TradeModels();
            if (progressData.getSuccSids() != null && progressData.getSuccSids().length != 0) {
                tradeModels = toTradeModels(staff, tradeSearchService.queryBySids(staff, true, progressData.getSuccSids()));
            }
            Map<String, String> errorMsg = new HashMap<>();
            errorMsg.put("update_sysmemo", CollectionUtils.isEmpty(progressData.getErrorMsg()) ? "" : progressData.getErrorMsg().get(0));
            return CommonModel.initNotProgress(tradeModels, errorMsg);
        }
        throw new IllegalArgumentException("更新类型不存在");
    }

    @RequestMapping(value = "/sysmemo/update/progress", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Object queryUpdateSysMemoProgress() throws SessionException {
        Staff staff = getStaff();
        ProgressData progress = progressService.queryProgress(staff, ProgressEnum.PROGRESS_TRADE_BACTH_UPDATE_SYSMEMO);
        return ProgressUtils.getCurrentProgress(progress, staff);
    }

    /**
     * 更新发票抬头
     */
    @RequestMapping(value = "/invoiceName/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateInvoiceName(TbTrade trade, String api_name) throws SessionException {
        Assert.notNull(trade.getSid(), "请输入系统订单号");
        sysTradeService.updateInvoiceName(getStaff(), trade);
        return successResponse();
    }

    /**
     * 查看某个订单的历史系统备注修改记录
     */
    @RequestMapping(value = "/sysmemo/list", method = RequestMethod.GET)
    @ResponseBody
    public Object listSysMemo(Long sid, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入系统订单号");
        return sysTradeService.querySysMemoLogs(getStaff(), sid);
    }

    /**
     * 指定下载
     */
    @RequestMapping(value = "/sync/single", method = RequestMethod.POST)
    @LogTag(key = "#tid", content = "'同步或者下载订单，id：' + #tid + '，用户平台编号：' + #userId", action = "sync_single_trade")
    @ResponseBody
    public Object syncSingleTrade(Long userId, String tid, String api_name) throws SessionException {
        Assert.notNull(userId, "请选择店铺后下载");
        // 前端放开了限制，后端要限制一下逗号
        Assert.isTrue((StringUtils.isNotBlank(tid) && !tid.contains(",")), "平台交易号为空或者不合法，请重新输入");
        Staff staff = getStaff();
        User user = staff.getUserIdMap().get(userId);
        Assert.notNull(user, "您没有所选店铺的权限");
        Assert.isTrue(user.getActive() != null && user.getActive().intValue() != CommonConstants.JUDGE_NO, String.format("所选店铺已停用[userId:%s]", user.getId()));
        if (!CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(user.getSource())) {
            Assert.isTrue(user.getUserConf().isInitItem(), String.format("该店铺商品未完成初始化,不能指定下载[userId:%s,nick:%s]", user.getId(), user.getNick()));
        }
        TradeImportResult result = new TradeImportResult();
        if(TradeSyncConfigUtils.canUpdatePaymentUserIds(userId)){
            result.setCanUpdatePayment(true);
        }
        return TradeModels.toTradeModels(staff, syncTradeBusiness.syncTradeByTid(user, tid, result, true), shopService, expressCompanyService, false, null, tradeConfigService.get(staff)).getList().get(0);
    }

    /**
     * 更新卖家备注和旗帜
     */
    @RequestMapping(value = "/sellermemoflag/update", method = RequestMethod.POST)
    @LogTag(key = "#sid", content = "'更新卖家备注和旗帜，系统单号：' + #sid + '，备注：' + #memo + '，旗帜类型：' + #flag", action = "update_sellermemoflag")
    @ResponseBody
    public Object updateSellerMemoFlag(Long sid, String memo, Long flag, String api_name, Integer memoType, String sellerFlagTag, Boolean notifyFx) throws SessionException {
        Assert.isTrue(sid != null, "请选择订单号");
        Assert.isTrue(StringUtils.isNotEmpty(memo) || flag != null, "备注不能为空");
        memoType = NumberUtils.nvlInteger(memoType, 0);
        Assert.isTrue(memoType == 0 || memoType - 1 == 0, "备注标记超出范围【0：未处理 1：已处理】");
        Staff staff = getStaff();
        TradeMemo tradeMemo = TradeMemo.builder()
                .handType(TradeMemoTypeEnum.PLAT_SELLER_EMEO_FLAG.getType())
                .sellerMemo(memo)
                .isHandlerMemo(memoType)
                .sellerFlag(flag)
                .append(false)
                .sellerFlagTag(sellerFlagTag)
                .handFrom(TradeMemoFromEnum.WEB.getType())
                .handleMergeTrade(false)
                .notifyFx(notifyFx)
                .build();
        if (Boolean.TRUE.equals(notifyFx)){
            fxMemoBusiness.notifySellerMemo2Fx(staff, tradeMemo, sid);
        }
        tradeUpdateSellerMemoFlagBusiness.update(staff, tradeMemo, Lists.newArrayList(sid).toArray(new Long[]{}));
        return successResponse();
    }

    /**
     * 查询平台旗帜标签
     */
    @RequestMapping(value = "/sellermemoflag/queryPlatSellerFlagTag", method = RequestMethod.POST)
    @ResponseBody
    public Object queryPlatSellerFlagTag(Long userId) throws SessionException {

        Staff staff = getStaff();
        User user = null;
        if (userId != null && staff.getUserIdMap() != null) {
            user = staff.getUserIdMap().get(userId);
        }
        return platSellerFlagTagBusiness.queryPlatSellerFlagTag(staff, user);
    }

    @RequestMapping(value = "/type/list")
    @ResponseBody
    public Object getTradeTypes(String api_name) {
        return tradeService.getTradeTypeList();
    }

    /**
     * 更新订单保价
     */
    @RequestMapping(value = "/insurancecost/update", method = RequestMethod.POST)
    @LogTag(key = "#sid", content = "'更新订单保价，系统单号：' + #sid + '，保价价格：' + #insuranceCost", action =
            "update_insurancecost")
    @ResponseBody
    public Object updateInsuranceCost(Long sid, Double insuranceCost, String api_name) throws SessionException {
        Assert.isTrue(sid != null, "请选择订单号");

        Staff staff = getStaff();
        tradeExtBusiness.updateInsuranceCost(staff, sid, insuranceCost);
        return successResponse();
    }

    /**
     * 批量更新卖家备注和旗帜
     *
     * @param params
     * @param updatemMemo           卖家备注
     * @param memoType              备注是否已处理  0：未处理 1：已处理
     * @param flag                  旗帜
     * @param append                是否追加
     */
    @RequestMapping(value = "/sellerMemo/batchUpdate", method = RequestMethod.POST)
    @LogTag(key = "#params.sids", content = "'批量更新卖家备注和旗帜，系统单号：' + #params.sids + '，备注：' + #updatemMemo + '，旗帜类型：' + #flag", action = "sellerMemo.batchUpdate")
    @ResponseBody
    public Object batchUpdateSellerMemo(TradeControllerParams params, String updatemMemo, Integer memoType, Long flag, Boolean append, String api_name) throws SessionException {
        Staff staff = getStaff();
        Assert.isTrue(StringUtils.isNotEmpty(updatemMemo) || flag != null, "备注不能为空");
        memoType = NumberUtils.nvlInteger(memoType, 0);
        Assert.isTrue(memoType == 0 || memoType - 1 == 0, "备注标记超出范围【0：未处理 1：已处理】");
        if (append == null) {
            append = false;
        }
        if (append && StringUtils.isBlank(updatemMemo)) {
            updatemMemo = null;
        }
        TradeQueryParams tradeQueryParams = TradeQueryParams.copyParams(params);
        TradeMemo tradeMemo = TradeMemo.builder()
                .handType(TradeMemoTypeEnum.PLAT_SELLER_EMEO_FLAG.getType())
                .sellerMemo(updatemMemo)
                .isHandlerMemo(memoType)
                .sellerFlag(flag)
                .append(append)
                .handFrom(TradeMemoFromEnum.WEB.getType())
                .handleMergeTrade(true)
                .build();
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_UPDATE_SELLER_MEMO_FLAG), "上一次批量更新卖家备注还未执行完毕，请稍等！");
        eventCenter.fireEvent(this, new EventInfo("trade.update.memo").setArgs(new Object[]{staff, tradeMemo, tradeQueryParams}), null);
        return successResponse();
    }

    /**
     * 卖家备注关键字批量替换
     */
    @RequestMapping(value = "/sellermemo/batchReplace", method = RequestMethod.POST)
    @ResponseBody
    public Object batchReplaceSellerMemo(TradeControllerParams params, String keyWard, String repaceMemo) throws SessionException {
        Staff staff = getStaff();
        if (StringUtils.isBlank(keyWard)) {
            throw new TradeException("关键字不能为空");
        }
        Assert.notNull(repaceMemo, "替换后的备注内容不能为空");
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_UPDATE_SELLER_MEMO_FLAG), "上一次批量更新卖家备注还未执行完毕，请稍等！");
        TradeMemo tradeMemo = TradeMemo.builder()
                .handType(TradeMemoTypeEnum.PLAT_SELLER_EMEO_REPLACE.getType())
                .keyWard(keyWard)
                .repaceMemo(repaceMemo)
                .handFrom(TradeMemoFromEnum.WEB.getType())
                .handleMergeTrade(true)
                .build();
        TradeQueryParams tradeQueryParams = TradeQueryParams.copyParams(params);
        eventCenter.fireEvent(this, new EventInfo("trade.update.memo").setArgs(new Object[]{staff, tradeMemo, tradeQueryParams}), null);
        return successResponse();
    }

    /**
     * 标记空单或取消空单
     *
     * @param cancel true 取消空单, false标记空单
     */
    @RequestMapping(value = "/scalp", method = RequestMethod.POST)
    public @ResponseBody
    Object scalp(TradeControllerParams params, Boolean cancel, String api_name) throws Exception {
        cancel = cancel != null && cancel;
        String type = StringUtils.isNotBlank(params.getUserSearch()) ? params.getUserSearch() : "0";
        if ("0".equals(type) || !cancel) {
            Assert.notNull(params.getSids(), "请选择要添加或取消标记的订单!");
            sysTradeService.scalp(getStaff(), cancel, ArrayUtils.toLongArray(params.getSids()));
        } else {
            Assert.isTrue(progressService.addProgress(getStaff(), ProgressEnum.PROGRESS_TRADE_CANCEL_SCALP), "当前正在进行取消空单操作,请稍后再试");
            eventCenter.fireEvent(this, new EventInfo("trade.cancel.scalp").setArgs(new Object[]{getStaff(), params, true}), null);
        }
        return successResponse();
    }


    /**
     * 菜鸟仓-自发转换
     */
    @RequestMapping(value = "/cainiao/convert", method = RequestMethod.POST)
    public @ResponseBody
    Object cainiaoConvert(TradeControllerParams params, String api_name) throws Exception {
        Assert.notNull(params.getSids(), "请选择需要转换菜鸟仓-自发的订单!");
        sysTradeService.cainiaoConvert(getStaff(), ArrayUtils.toLongArray(params.getSids()));
        List<Trade> trades = tradeSearchService.queryBySids(getStaff(), true, ArrayUtils.toLongArray(params.getSids()));
        return TradeModels.toTradeModels(getStaff(), trades, shopService, expressCompanyService, false, null, tradeConfigService.get(getStaff())).getList();
    }


    @RequestMapping(value = "/scalp/{field}", method = RequestMethod.POST)
    public @ResponseBody
    Object scalp(MultipartFile file, @PathVariable("field") String field, String api_name) throws Exception {
        Staff staff = getStaff();
        String[][] data = ExcelConverter.excel2DataArr(file.getInputStream(), 1);
        Assert.notNull(data, "读取Excel文件报错");
        Assert.isTrue(data.length > 0, "上传的Excel没有订单数据");
        Set<String> values = new HashSet<String>(data.length);
        for (String[] arr : data) {
            if (arr[0] != null && arr[0].trim().length() > 0) {
                values.add(arr[0].trim());
            }
        }
        Assert.isTrue(!values.isEmpty(), "excel中没有有效数据");
        return sysTradeService.scalp(staff, field, values.toArray(new String[0]));
    }

    @RequestMapping(value = "/import/remark", method = RequestMethod.POST)
    @ResponseBody
    public Object import2Remark(MultipartFile file, String field, Integer containAudit, Integer needHalt, String api_name) throws Exception {
        Staff staff = getStaff();
        String[][] data = ExcelConverter.excel2DataArr(file.getInputStream(), 1);
        Assert.notNull(data, "读取Excel文件报错");
        Assert.notNull(data.length > 0, "上传Excel文件无数据");
        Set<String> values = new HashSet<>(data.length);
        for (String[] dataArr : data) {
            String value = removeAllBlank(dataArr[0]);
            if (StringUtils.isNotBlank(value)) {
                values.add(value);
            }
        }
        Assert.isTrue(!values.isEmpty(), "excel中没有有效数据");
        return sysTradeService.scalp(staff, field, containAudit != null && containAudit != 0, needHalt != null && needHalt != 0, values.toArray(new String[0]));
    }

    /**
     * 取消作废订单
     */
    @RequestMapping(value = "/uncancel", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'取消作废订单，系统单号：' + #sids", action = "uncancel")
    public @ResponseBody
    Object uncancel(String sids, String api_name) throws SessionException {
        Assert.notNull(sids, "请输入订单号!");
        Staff staff = getLightStaff();

        String tradeCompanyIdsCloseSync = config.getProperty(TradeLocalConfigurableConstants.TRADE_COMPANY_IDS_CLOSE_SYNC);
        Set<Long> companyIds = Strings.getAsLongSet(tradeCompanyIdsCloseSync, ",", false);
        Assert.isTrue(!companyIds.contains(staff.getCompanyId()), "订单同步已关闭，无法取消作废，请开启订单同步后再试");

        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_UNCANCEL_BATCH), "上次批量取消作废还未结束，请稍等！");
        eventCenter.fireEvent(this, new EventInfo(CancelUndoBusiness.TRADE_UNCANCEL_BATCH_EVENT_NAME).setArgs(new Object[]{staff, ArrayUtils.toLongArray(sids), IpUtils.getClientIP(request)}), null);
        OpLogHelper.recodeOpLog(opLogService, getRequest(), staff, "uncancel_batch", "uncancel_batch", "批量取消作废订单操作成功!", sids);
        return successResponse();
    }

    /**
     * 挂起订单
     */
    @RequestMapping(value = "/halt", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'挂起订单，系统单号：' + #sids", action = "halt")
    public @ResponseBody
    Object halt(String sids, String api_name, @RequestParam(required = false) Integer hours) throws SessionException {
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        if (sidArray == null || sidArray.length == 0) {
            throw new TradeException("请输入系统单号!");
        }
        if (null == hours || hours <= 0) {
            if (sidArray.length >= 100) {
                Assert.isTrue(progressService.addProgress(getStaff(), ProgressEnum.PROGRESS_TRADE_HALT), "上次批量挂起订单还未结束，请稍等！");
                eventCenter.fireEvent(this, new EventInfo("trade.halt.batch").setArgs(new Object[]{getStaff(), sids, null}), null);
            } else {
                return sysTradeService.halt(getStaff(), sidArray, 1);
            }
        } else {
            Assert.isTrue(hours <= 999, "最大支持填写999，请重新填写");
            if (sidArray.length >= 100) {
                Assert.isTrue(progressService.addProgress(getStaff(), ProgressEnum.PROGRESS_TRADE_HALT_LIMIT_TIME), "上次限时批量挂起订单还未结束，请稍等！");
                eventCenter.fireEvent(this, new EventInfo("trade.halt.batch").setArgs(new Object[]{getStaff(), sids, hours}), null);
            } else {
                return sysTradeService.haltWithHours(getStaff(), sidArray, 1, hours);
            }
        }
        return successResponse();
    }

    /**
     * 取消挂起订单
     */
    @Deprecated //统一走取消异常
    @RequestMapping(value = "/unhalt", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'取消挂起订单，系统单号：' + #sids", action = "unhalt")
    public @ResponseBody
    Object unhalt(String sids, String api_name) throws SessionException {
        Assert.notNull(sids, "请输入订单号!");
        Staff staff = getStaff();
        List<Trade> trades = sysTradeService.halt(staff, ArrayUtils.toLongArray(sids), 0);

        tradeFillService.fill(staff, trades);
        return toTradeModels(getStaff(), trades);
    }

    /**
     * 删除订单
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'删除订单，系统单号：' + #sids", action = "delete")
    public @ResponseBody
    Object delete(String sids, boolean force, String api_name) throws SessionException {
        Assert.notNull(sids, "请输入订单号!");
        sysTradeService.delete(getStaff(), ArrayUtils.toLongArray(sids));
        return successResponse();
    }

    /**
     * 取消异常订单
     */
    @RequestMapping(value = "/exception/cancel", method = RequestMethod.POST)
    @LogTag(key = "#queryParams.sids", content = "'取消异常,系统单号: ' + #queryParams.sids", action = "trade/exception/cancel")
    public @ResponseBody
    Object cancelExcep(TradeControllerParams queryParams, String systemExceptions, String customExceptions,Integer isCancelSmallRefund, String api_name) throws SessionException {
        Staff staff = getStaff();
        String type = StringUtils.isNotBlank(queryParams.getUserSearch()) ? queryParams.getUserSearch() : "0";
        List<String> systems = null;
        List<Long> customs = null;
        if (null != systemExceptions) {
            systems = Strings.getAsStringList(systemExceptions, ",", true);
        }
        if (null != customExceptions) {
            customs = Strings.getAsLongList(customExceptions, ",", true);
        }
        ExceptHandlerDto exceptHandlerDto = new ExceptHandlerDto();
        exceptHandlerDto.setCustoms(customs);
        exceptHandlerDto.setSystems(systems);
        if (StringUtils.isNotBlank(queryParams.getSids())) {
            exceptHandlerDto.setSids(ArrayUtils.toLongArray(queryParams.getSids()));
        }
        exceptHandlerDto.setIsCancelSmallRefund(isCancelSmallRefund);
        exceptHandlerDto.setRecordTrace(true);
        exceptHandlerDto.setQueryParams(queryParams);
        if ("0".equals(type)) {
            Assert.notNull(queryParams.getSids(), "请输入订单号");
            List<Trade> updated = tradeCancelExceptService.cancelExcept(staff, exceptHandlerDto);
            List<Trade> trades = tradeSearchService.queryBySids(staff, true, false, true, TradeUtils.toSids(updated));
            return TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfigService.get(staff)).getList();
        } else {
            Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_CANCEL_EXCEP), "当前正在进行取消异常操作,请稍后再试");
            eventCenter.fireEvent(this, new EventInfo("trade.batch.cancel.excep").setArgs(new Object[]{staff, exceptHandlerDto}), null);
            return successResponse();
        }
    }

    /**
     * 取消平台换地址、换商品异常订单
     */
    @RequestMapping(value = "/addAndItemException/cancel", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'取消平台换地址、换商品异常,系统单号: ' + #sids", action = "trade/addAndItemException/cancel")
    public @ResponseBody
    Object cancelAddAndItemExcep(String sids, String api_name) throws SessionException {
        Assert.notNull(sids, "请输入订单号");
        Staff staff = getStaff();
        ExceptHandlerDto exceptHandlerDto = new ExceptHandlerDto();
        exceptHandlerDto.setRecordTrace(false);
        exceptHandlerDto.setSids(ArrayUtils.toLongArray(sids));

        List<Trade> updated = tradeCancelExceptService.cancelPlatformExcept(staff, exceptHandlerDto);
        Long[] sidArr = CollectionUtils.isEmpty(updated) ? ArrayUtils.toLongArray(sids) : TradeUtils.toSids(updated);
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, false, true, sidArr);
        return TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfigService.get(staff)).getList();
    }

    /**
     * 取消缺货异常
     */
    @RequestMapping(value = "/insufficient/cancel", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'取消缺货异常,系统单号: ' + #sids", action = "trade/insufficient/cancel")
    public @ResponseBody
    Object cancelInsufficient(String sids, String api_name) throws SessionException {
        Assert.notNull(sids, "请输入订单号");
        Staff staff = getStaff();
        return tradeService.cancelInsufficient(staff, ArrayUtils.toLongArray(sids));

    }

    /**
     * 加急订单
     */
    @RequestMapping(value = "/urgent", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'加急订单，系统单号：' + #sids", action = "urgent")
    public @ResponseBody
    Object urgent(String sids, String api_name) throws SessionException {
        Assert.notNull(sids, "请输入订单号!");
        Map<String, Object> result = getUrgentResult(1, sids);
        return result;
    }

    private Map<String, Object> getUrgentResult(Integer isUrgent, String sids) throws SessionException {
        Map<String, String> failSidMap = new LinkedHashMap<>();
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        sysTradeService.urgent(getStaff(), sidArray, isUrgent, failSidMap);
        Map<String, Object> result = new HashMap<>();
        int successSidNum = sidArray.length - failSidMap.size();
        Map<String, String> resultFailSidsMap = new LinkedHashMap<>();
        if (failSidMap.size() > 100) {//失败订单超过100，仅返回前100给前端
            int j = 0;
            for (String failSid : failSidMap.keySet()) {
                if (j++ < 100) {
                    resultFailSidsMap.put(failSid, failSidMap.get(failSid));
                }
            }
        } else {
            resultFailSidsMap.putAll(failSidMap);
        }
        result.put("failSids", resultFailSidsMap);
        result.put("successSidNum", successSidNum);
        return result;
    }

    /**
     * 取消加急订单
     */
    @RequestMapping(value = "/unurgent", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'取消加急订单，系统单号：' + #sids", action = "unurgent")
    public @ResponseBody
    Object unurgent(String sids, String api_name) throws SessionException {
        Assert.notNull(sids, "请输入订单号!");
        Map<String, Object> result = getUrgentResult(0, sids);
        return result;
    }

    private void checkCustomer(Staff staff, Trade trade) {
        if (trade.isCustomerTrade() && StringUtils.isBlank(trade.getBuyerNick())) {
            throw new TradeException("客户类型订单没有昵称，不能保存！");
        }
    }

    private void checkTid(Staff staff, Trade trade) {
        if (StringUtils.isNotEmpty(trade.getTid())) {
            String tid = StringUtils.replace(trade.getTid(), "-", "");
            if (StringUtils.isEmpty(tid)) {
                throw new TradeException(String.format("请输入有效的平台单号，当前平台单号[%s]", trade.getTid()));
            }
        }
    }

    /**
     * 新建订单，这个接口适合系统订单
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @LogTag(key = "#trade.sid", content = "'保存系统订单，系统单号：' + #trade.sid", action = "save_systrade")
    @ResponseBody
    public Object saveTrade(@RequestBody TbTrade trade, String orderType, String ifChangeTradeDmsAttr, String ifAutoAudit, String isSelfPick, String ifAutoMatchSalesMan) throws SessionException {
        if (StringUtils.isNotEmpty(orderType)) {
            trade.setType(orderType);
        }
        if (Objects.equals("1", isSelfPick)) {
            //添加快卖通现场自提的标识
            trade.addV(32);
        }
        if (Objects.equals("1", ifAutoMatchSalesMan)) {
            //添加是否匹配业务员的标识
            trade.addV(TradeConstants.V_IF_AUTO_MATCH_SALESMAN);
        }
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, trade);
        checkCustomer(staff, trade);
        checkTid(staff, trade);
        Trade result = sysTradeService.safeSaveTrade(staff, trade, trade.isForce());
        Logs.debug(LogHelper.buildLog(staff, "保存订单" + JSONObject.toJSONString(trade)));
        giftSaveLog(staff, result, TradeUtils.getOrders4Trade(result));
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, false, true, result.getSid());
        tradeOrderSyncItemTagFill.fill(staff, trades);
        if (Objects.equals("1", ifChangeTradeDmsAttr)) {
            Long[] sids = TradeUtils.toSids(trades);
            sysTradeDmsService.addDistributorAttribute(staff, sids, 1);
        } else {
            try {
                trades = fxBusiness.matchSupplyIdsWithDmsAttrTypeAndSave(staff.getUser(), trades, 3, 1);
            } catch (Exception e) {
                logger.error(LogHelper.buildLogHead(staff).append("新增分销属性出错"), e);
            }
        }
        if (Objects.equals("1", ifAutoAudit)) {
            Map<Long, TradeResult> errors = tradeAuditService.audit(staff, new Long[]{result.getSid()}, IpUtils.getClientIP(request));
            TradeResultVoWrapper resultVoWrapper = TradeResultVoWrapper.models2Vo(errors.values());
            if (CollectionUtils.isNotEmpty(resultVoWrapper.getErrorResult())) {
                // 新增分销属性时如果勾选了配置会触发审核，这里批判下订单状态如果是已经审核通过就表示已经审核成功
                Trade tradeCurrent = tradeSearchService.queryBySid(staff, false, result.getSid());
                if (tradeCurrent != null && TradeStatusUtils.isFinishAuditButNotPrint(tradeCurrent)) {
                    List<TradeResultVo> successList = new ArrayList<>();
                    TradeResultVo successResult = new TradeResultVo();
                    successResult.setSuccess(true);
                    successResult.setSid(String.valueOf(result.getSid()));
                    successList.add(successResult);
                    resultVoWrapper.setSuccessResult(successList);
                    // 成功的时候把错误信息去掉。
                    resultVoWrapper.setErrorResult(null);
                    return resultVoWrapper;
                }
                Logs.debug(LogHelper.buildLog(staff, "审核订单失败数据" + JSONObject.toJSONString(resultVoWrapper)));
                if (tradeCurrent != null && StringUtils.equals(CommonConstants.PLAT_FORM_TYPE_FXXZ, tradeCurrent.getSubSource())) {
                    //KMERP-218068
                    TradeResult tradeResult = errors.get(tradeCurrent.getSid());
                    String errorMsg = "分销账户余额不足";
                    if (tradeResult != null && StringUtils.isNotEmpty(tradeResult.getErrorMsg())
                            && tradeResult.getErrorMsg().endsWith(errorMsg)) {
                        fxBusiness.cancelFxTrade(CancelData.builder()
                                .staff(getStaff()).sidArr(new Long[]{tradeCurrent.getSid()})
                                .gxTrades(null).isCancelAttr(0)
                                .ifBatch(false)
                                .opName(staff.getName())
                                .opMsg("作废订单! 原因：供销商资金不足，会自动作废分销订单")
                                .build());
                    }
                } else {
                    sysTradeService.cancel(getStaff(), TradeUtils.toSids(trades));
                }
            }
            return resultVoWrapper;
        }
        return TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
    }

    /**
     * 保存订单，这个接口适合系统订单
     */
    @RequestMapping(value = "/platform/save", method = RequestMethod.POST)
    public @ResponseBody
    Object savePlatformTrade(@RequestBody TradePlatformRequest request) throws SessionException {
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, request);
        TbTrade trade = request.getTrade();
        String ifChangeTradeDmsAttr = request.getIfChangeTradeDmsAttr();
        Integer isImport = request.getIsImport();
        Integer sameGoodFillType = request.getSameGoodFillType();
        Integer importType = request.getImportType();
        Assert.isTrue(trade != null, "请输入订单信息");
        if (StringUtils.isNotEmpty(request.getOrderType())) {
            trade.setType(request.getOrderType());
        }
        checkCustomer(staff, trade);
        if (tradeLocalConfigurable == null) {
            logger.debug("tradeLocalConfigurable 为空 跳过新接口跳转判断");
        } else if (tradeLocalConfigurable.isNewItemSave(staff.getCompanyId(), trade.getUserId())) {
            TradeItemChangeRequest model = new TradeItemChangeRequest();
            model.setSid(trade.getSid());
            model.setPayAmount(trade.getPayAmount());
            List<TradeItemChangeRequest.ItemChange> itemModels = new ArrayList<>();
            model.setOrders(itemModels);
            model.setSaveOrderSupplier(trade.getSaveOrderSupplier());
            model.setRematchItemReplace(trade.getRematchItemReplace());
            model.setImportType(importType);
            model.setSameGoodFillType(sameGoodFillType);
            model.setIsImport(isImport);
            for (TbOrder order : trade.getOrders()) {
                TradeItemChangeRequest.ItemChange item = new TradeItemChangeRequest.ItemChange(order);
                itemModels.add(item);
            }
            return saveTradeItemChange(model, ifChangeTradeDmsAttr);
        }

        //以下逻辑修改时 记得同步修改/item/save里的内容
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, false, true, trade.getSid());
        if (CollectionUtils.isNotEmpty(originTrades)) {
            Assert.isTrue(trade.getSid().equals(originTrades.get(0).getSid()), "当前合单主单已发生变化，请查询后重新进行修改。");
        }
//        Assert.isTrue(!TradeUtils.isSmtqtgTrade(trade), String.format("速卖通全托管订单，无法换商品[sid:%s]", trade.getSid()));
        List<Long> giftOrders = TradeUtils.getOrders4Trade(originTrades).parallelStream().filter(Order::isGift).map(Order::getId).collect(Collectors.toList());
        //填充商品标签信息到order
        ((TradeOrderSyncItemTagFill) tradeOrderSyncItemTagFill).fillByItemDubbo(staff, TradeUtils.getOrders4Trade(trade));
        //标记是否拣选验货
        OrderUtils.toFullOrderList(TradeUtils.getOrders4Trade(trade), false).stream().filter(order -> !giftOrders.contains(order.getId())).forEach(OrderUtils::fillIsPick);
        ModifyData modifyData = sysTradeService.updateTrade(staff, trade);
        orderModifyLogBusiness.addLog(staff, modifyData.orderModifyLogs);
        saveLog(staff, trade.getSid() + "", getPlatformSaveContent(trade, modifyData));

        giftSaveLog(staff, trade, modifyData.inserts);
        //记录商品操作日志
        itemTraceService.batchRecord(staff, ItemTraceMessageBuilder.buildItemTraceMessageList(staff, modifyData));
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, false, true, trade.getSid());
        tradeOrderSyncItemTagFill.fill(staff, trades);
        try {
            trades = fxBusiness.matchSupplyIdsWithDmsAttrTypeAndSave(staff.getUser(), trades, 3, 1);
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("新增分销属性出错"), e);
        }
        TradeModels tradeModels = TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
        stockInfoModel(staff, tradeModels);

        //保存order修改日志
        saveOrderModifyLog(staff, modifyData);
        if (Objects.nonNull(isImport) && isImport == 1 && modifyData.isChange()) {
            for (Trade update : trades) {
                update.getOperations().put(OpEnum.ITEM_IMPORT, String.format("通过导入商品修改商品成功。模版中存在多行相同商家编码时：%s。导入形式：%s", sameGoodFillType == 1 ? "只导入第一行" : "累加数量", importType == 1 ? "仅新增" : "覆盖"));
            }
            tradeTraceBusiness.asyncTrace(staff, trades, OpEnum.ITEM_IMPORT);
        }

        return tradeModels;
    }


    /**
     * 前端 修改商品页面 进行换商品操作时 对应金额计算回填
     */
    @RequestMapping(value = "/item/exchange/calculate", method = RequestMethod.POST)
    public @ResponseBody Object procalculateItemExChange(@RequestBody TradeItemExchangeProCalculateRequest request) throws SessionException {
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, request);
        TradeItemExchangeProCalculateResponse response = tradeItemExchangePaymentBusinessService.preCalculateItemExChange(staff, request);
        return response;
    }


    /**
     * 前端 修改商品 页面对应保存
     */
    @RequestMapping(value = "/item/save", method = RequestMethod.POST)
    public @ResponseBody Object saveTradeItemChange(@RequestBody TradeItemChangeRequest changeModel, String ifChangeTradeDmsAttr) throws SessionException {
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, changeModel);
        Assert.isTrue(CollectionUtils.isNotEmpty(changeModel.getOrders()), "一笔订单下至少需要有一个商品");

        Trade tradeDb = tradeSearchService.queryBySid(staff, true, changeModel.getSid());
        if (tradeDb != null && TradeUtils.isGxTrade(tradeDb)) {
            changeModel.setTrade(tradeDb);
            List<Trade> gxTrades = gxTradeItemModifyBusiness.saveTradeItem(staff, changeModel, IpUtils.getClientIP(request));
            TradeModels tradeModels = TradeModels.toTradeModels(staff, gxTrades, shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
            stockInfoModel(staff, tradeModels);
            return tradeModels;
        }
        List<Trade> trades = tradeItemChangeBusinessService.saveTradeItemChange(staff,changeModel,ifChangeTradeDmsAttr, request);
        tradeOrderSyncItemTagFill.fill(staff, trades);

        TradeModels tradeModels = TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
        stockInfoModel(staff, tradeModels);

        try {
            fxBusiness.matchSupplyIdsWithDmsAttrTypeAndSave(staff.getUser(), trades, 3, 1);
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("新增分销属性出错"), e);
        }
        return tradeModels;
    }

    /**
     * 订单管理赠品
     */
    @RequestMapping(value = "/gift/save", method = RequestMethod.POST)
    @ResponseBody
    public Object giftSave(@RequestBody TbTrade trade) throws SessionException {
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, trade);
        ModifyData modifyData = sysTradeService.updateTradeGift(staff, trade);
        orderModifyLogBusiness.addLog(staff, modifyData.orderModifyLogs);
        saveLog(staff, trade.getSid() + "", getPlatformSaveContent(trade, modifyData, "赠品"));
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, false, true, trade.getSid());
        tradeOrderSyncItemTagFill.fill(staff, trades);
        TradeModels tradeModels = TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
        stockInfoModel(staff, tradeModels);
        // 记录赠品变更日志
        itemTraceService.batchRecord(staff, ItemTraceMessageBuilder.buildGiftItemTraceMessageList(staff, modifyData));
        return tradeModels;
    }

    /**
     * 保存订单，这个接口适合系统订单
     */
    @RequestMapping(value = "/suit/update", method = RequestMethod.POST)
    public @ResponseBody
    Object updateTradeSuit(@RequestBody TbTrade trade, String orderType) throws SessionException {
        Assert.isTrue(TradeUtils.getOrders4Trade(trade).size() > 0, "没有需要修改的子订单");
        if (StringUtils.isNotEmpty(orderType)) {
            trade.setType(orderType);
        }
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, trade);
        ModifyData modifyData = sysTradeService.updateTrade4Suit(staff, trade);
        saveLog(staff, trade.getSid() + "", getSuitUpdateContent(trade, modifyData));
        return TradeModels.toTradeModels(staff, tradeSearchService.queryBySids(staff, true, false, true, trade.getSid()), shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
    }


    /**
     * 快速批量替换套间内单品
     */
    @RequestMapping(value = "/suit/batch/update", method = RequestMethod.POST)
    public @ResponseBody
    Object updateTradeSuitBatch(@RequestBody TbTrade trade, String orderType) throws SessionException {
        Assert.isTrue(TradeUtils.getOrders4Trade(trade).size() > 0, "没有需要修改的商品信息！");

        if (StringUtils.isNotEmpty(orderType)) {
            trade.setType(orderType);
        }

        Long orderId = trade.getNumIid();
        Assert.isTrue(orderId != null, "套件id传递为空!");
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, trade);
        ModifyData modifyData = sysTradeService.updateTrade4SuitBatch(staff, trade, orderId);
        saveLog(staff, trade.getSid() + "", "订单快速换单品，系统订单号：" + trade.getSid() + ";更改后的商品为 " + modifyData.getChangeItemLog());
        return TradeModels.toTradeModels(staff, tradeSearchService.queryBySids(staff, true, false, true, trade.getSid()), shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
    }

    @RequestMapping(value = "/suite/update/latest", method = RequestMethod.POST)
    public @ResponseBody
    Object updateSuite2Latest(String sid, String api_name) throws SessionException {
        Staff staff = getStaff();
        Long[] sids = ArrayUtils.toLongArray(sid);
        Assert.isTrue(sids.length > 0, "请输入正确的系统订单号");
        int count = TradeBusinessUtils.getSwitchProgressCount(staff.getCompanyId(), "tradeSuiteUpdateLatest");
        if (sids.length > count) {
            Assert.isTrue(progressService.addProgress(staff, ProgressEnum.TRADE_SUITE_UPDATE_LATEST), "当前正在进行处理套件修改异常操作,请稍后再试");
            eventCenter.fireEvent(this, new EventInfo(TradeUpdateSuiteLatestListener.EVENT_NAME).setArgs(new Object[]{staff, sid}), null);
            return successResponse();
        } else {
            List<Trade> trades = tradeService.updateSuite2Latest(staff, sids);
            return TradeModels.toTradeModels(staff, tradeSearchService.queryBySids(staff, true, false, true, TradeUtils.toSids(trades)), shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
        }
    }

    /**
     * 普通商品转加工
     *
     * @param sid
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/process/update/latest", method = RequestMethod.POST)
    public @ResponseBody
    Object updateNormal2Process(String sid, String api_name) throws SessionException {
        Long[] sids = ArrayUtils.toLongArray(sid);
        Assert.isTrue(sids.length > 0, "请输入正确的系统订单号");
        return tradeService.updateProcess2Latest(getStaff(), sids);
    }


    private String getSuitUpdateContent(Trade trade, ModifyData modifyData) {
        StringBuilder content = new StringBuilder();
        content.append("套件商品修改明细，系统订单号：" + trade.getSid() + ";");
        //修改明细的日志

        if (modifyData.itemChanges.size() > 0) {
            content.append("[");
            for (Order order : modifyData.itemChanges) {
                content.append("{商家编码：").append(order.getOldSysOuterId()).append("修改为").append(order.getSysOuterId()).append("};");
            }
            content.append("]");
        }
        return content.toString();
    }


    /**
     * excel订单导入
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public @ResponseBody
    Object importTrades(MultipartFile file, Long userId,Integer mode,Integer isGenerateAnnal, Boolean multiGoodsInSingleRow, String api_name) throws SessionException, IOException {
        Assert.notNull(file, "请选择要导入的excel文件");
        Assert.isTrue(file.getSize() < 1024 * 1024 * 2, "上传的excel文件大小不能超过2MB");
        Staff staff = getStaff();
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT), "上一次订单导入还未执行完毕，请稍等！");

        User user = null;
        if (userId != null && userId > 0) {
            user = staff.getUserByUserId(userId);
            Assert.notNull(user, "您没有该店铺的权限");
            if (user.getActive() != null && user.getActive() == CommonConstants.JUDGE_NO) {
                throw new IllegalArgumentException("所选店铺已停用");
            }
            if (CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(user.getSource())) {
                throw new IllegalArgumentException("奇门店铺不支持导入订单");
            }

        }
        String[][] data;
        long start = System.currentTimeMillis();
        try {
            data = ExcelConverter.excel2DataArr(file.getInputStream(), 1);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append("解析Excel订单,共" + data.length + "行，耗时" + (System.currentTimeMillis() - start) + "ms"));
            }
            Assert.notNull(data, "读取Excel文件报错");
            Assert.isTrue(data.length > 1, "上传的Excel没有订单数据");
        } catch (Exception e) {
            throw new TradeException("上传的文件格式不正确");
        }
        TradeIOService.fixAliaColumnName(data);
//        String requiredColumnTip = null;
//        Set<String> errTipColumns = Sets.newHashSet("错误信息");
//        Set<String> customRequiredColumns = null;
//        if(user == null) {
//            customRequiredColumns = Sets.newHashSet("店铺编号");
//        }
//        requiredColumnTip = ExcelImportUtil.checkRequiredColumn(ImportTradeRow.class, data, null, customRequiredColumns, true, errTipColumns);
//        if(StringUtils.isNotBlank(requiredColumnTip)){
//            throw new IllegalArgumentException(requiredColumnTip);
//        }
        Map<String, Integer> titleMapToCol = ImportHelper.checkRequiredColumn(data[0], user == null ? TradeIOService.TRADE_IMPORT_MULTI_TITLE_MINI : TradeIOService.TRADE_IMPORT_SINGLE_TITLE_MINI);
        ImportHelper.checkUnrecognizedColumn(data[0], user == null ? TradeIOService.TRADE_IMPORT_MULTI_TITLE : TradeIOService.TRADE_IMPORT_SINGLE_TITLE);
        //解析完后设置缓存(去标标题行得到实际数据行)
        progressService.setProgress(staff, ProgressEnum.PROGRESS_TRADE_MANUAL_IMPORT, data.length - 1);
        FileResult fileResult = fileUploadService.upload(file.getOriginalFilename(), data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        //异步处理导入
        eventCenter.fireEvent(this, new EventInfo("trade.manual.import").setArgs(new Object[]{staff, mode, isGenerateAnnal, user, titleMapToCol, multiGoodsInSingleRow}), fileResult.getUrl());
        //返回成功
        return successResponse();
    }

    /**
     * 更新订单的仓库
     */
    @RequestMapping(value = "/warehouse/change", method = RequestMethod.POST)
    public @ResponseBody
    Object updateWarehouse(Long[] sids, Long warehouseId, String warehouseName, boolean force, String checkPdd, String api_name) throws SessionException {
        Assert.isTrue(null != sids && sids.length > 0, "请指定要更换仓库的订单");
        Assert.isTrue(null != warehouseId && warehouseId > 0, "请指定要更换的仓库");
        Staff staff = getStaff();
        List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, sids);
        //只取仓库不一样的订单
        List<Long> changeWarehouseSids = tradeList.stream().filter(t -> !warehouseId.equals(t.getWarehouseId())).map(TradeBase::getSid).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isNotEmpty(changeWarehouseSids), "需要处理的订单数为空!");
        if (!tradeConfigNewService.get(staff, TradeConfigEnum.PRE_UPLOAD_WAREHOUSE_CHANGE_KEEP_EXPRESS).isOpen() && StringUtils.equals(checkPdd, "1")) {
            // 若开启了订单修改仓库保留快递模板和单号配置，则不需要做预发货订单修改单号的风险提示
            TradeWarehouseUtils.checkTemplateOutsid(tradeList, "修改仓库");
        }
        tradeWarehouseChangeBusiness.change(staff, OpEnum.WAREHOUSE_CHANGE, warehouseId, changeWarehouseSids.toArray(new Long[0]), force);
        String sidJoin = StringUtils.join(sids, ",");
        String content = "更换仓库，系统单号：" + sidJoin + "，更换的仓库名为：" + warehouseName;
        saveLog(staff, "warehouse_change", sidJoin, content);
        return successResponse();
    }

    @RequestMapping(value = "/batch/change/warehouse", method = RequestMethod.POST)
    @ResponseBody
    public Object batchChangeWarehouse(TradeControllerParams queryParams, Sort sort, Long changeWarehouseId, String api_name) throws SessionException {
        Assert.isTrue(changeWarehouseId != null && changeWarehouseId > 0, "请指定要更换的仓库");
        Staff staff = getStaff();
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_BATCH_CHANGE_WAREHOUSE), "上一次批量修改仓库还未执行完毕，请稍等！");
        eventCenter.fireEvent(this, new EventInfo("batch.change.warehouse").setArgs(new Object[]{staff, TradeQueryParams.copyParams(queryParams).setSort(sort), changeWarehouseId, IpUtils.getClientIP(request)}), null);
        return successResponse();
    }

    /**
     * 包装验货订单
     *
     * @param sids
     * @param orderIdIdentCodes
     * @param packmaOuterIds    包材商品的结构 商家编码:数量  [{"outerId":"abc", "amount":1}, {"outerId":"def", "amount":3}]
     * @param uniqueCodes       扫描唯一码
     * @param api_name
     * @return
     * @throws SessionException
     */
    @Deprecated
    @RequestMapping(value = "/pack")
    public @ResponseBody
    Object packTrades(String sids, Long waveId, String orderIdIdentCodes,
                      String orderScanInfos, String packmaOuterIds,
                      Long staffId, String uniqueCodes, String api_name, String scanWaveByBussineNo) throws SessionException {
        Staff staff = getStaff();
        if (null != staffId) {
            staff = staffAssembleBusiness.getStaffById(staffId);
            Assert.notNull(staff, "请选择正确的员工账号!");
            WaveUtils.validStaffCompany(getStaff(), staff);
            WaveConfig waveConfig = waveServiceDubbo.queryWaveConfig(staff);
            if (waveConfig != null && waveConfig.getInteger(WaveChatConfigsEnum.PACK_SUPPORT_SWITCH_ACCOUNT.getKey()) == 0) {
                Assert.isTrue(false, "页面已过期或配置已变更，请刷新页面重试！");
            }
        }
        waveId = getWaveIdByOutSid(waveId, scanWaveByBussineNo, staff);
        String ip = IpUtils.getClientIP(request);
        if (waveId != null && waveId > 0) {
            Object result = sysTradeService.packTradesWave(staff, waveId, ip);
            String content = "包装验货，波次号：" + waveId + "；具体包装验货人：" + staff.getName();
            OpLogHelper.recodeOpLog(opLogService, request, staff, "pack", null, content, null);
            return result;
        } else {
            Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入快递单号并回车,并确认右侧有订单数据.");
            Assert.isTrue(StringUtils.isNotEmpty(orderIdIdentCodes), "请输入子订单与识别码的对应参数");
            List<Map<String, Object>> packmaOuterIdArr = getPackmaOuterIdArr(sids, packmaOuterIds);
            List<Order> orders = TradeUtils.parseIdentCodes(sids, orderIdIdentCodes);
            List<TradePackScanInfo> packScanInfos = TradeUtils.parsePackScanInfos(sids, orderScanInfos);
            TradePackParams params = new TradePackParams.Builder()
                    .sids(ArrayUtils.toLongArray(sids)).orders(orders).packScanInfos(packScanInfos)
                    .uniqueCodes(ArrayUtils.toStringList(uniqueCodes)).clientIp(IpUtils.getClientIP(request))
                    .packmaOuterIds(TradeUtils.handlePackmaOuterIds(sids, packmaOuterIds))
                    .builder();

            TradeStaffConfig tradeStaffConfig = tradeStaffConfigService.get(staff);
            params.setSuitPack(Objects.equals(tradeStaffConfig.getOpenSuitselfPack(), 1));
            params.setValidClosedStatus(true);
            sysTradeService.packTradesByParams(staff, params);
            if (packmaOuterIdArr != null) {
                eventCenter.fireEvent(this, new EventInfo("trade.pack.packma.item").setArgs(new Object[]{staff, Long.parseLong(sids), packmaOuterIdArr, IpUtils.getClientIP(request)}), null);
            }
            String content = "包装验货，系统单号：" + sids + ",识别码信息:" + (orderIdIdentCodes == null ? "" : orderIdIdentCodes) + "；具体包装验货人：" + staff.getName();
            OpLogHelper.recodeOpLog(opLogService, request, getStaff(), "pack", null, content, null);
            return successResponse();
        }
    }

    /**
     * 批量包装验货
     */
    @Deprecated
    @RequestMapping(value = "/batch/pack", method = RequestMethod.POST)
    @ResponseBody
    public Object packTrades(String sids, Long staffId, String packmaOuterIds, String api_name) throws SessionException {
        Staff staff = getStaff();
        if (progressService.hasProgress(staff, ProgressEnum.PROGRESS_WAVE_BATCH_PACK)) {
            throw new IllegalArgumentException("正在进行批量验货，请稍候再试！");
        }
        if (null != staffId) {
            staff = staffAssembleBusiness.getStaffById(staffId);
            Assert.notNull(staff, "请选择正确的员工账号!");
            WaveConfig waveConfig = waveServiceDubbo.queryWaveConfig(staff);
            if (waveConfig != null && waveConfig.getInteger(WaveChatConfigsEnum.PACK_SUPPORT_SWITCH_ACCOUNT.getKey()) == 0) {
                Assert.isTrue(false, "页面已过期或配置已变更，请刷新页面重试！");
            }
        }
        Assert.notNull(sids, "请选择订单!");
        List<Long> sidList = ArrayUtils.toLongList(sids);

        progressService.setProgress(staff, ProgressEnum.PROGRESS_WAVE_BATCH_PACK, sidList.size());
        eventCenter.fireEvent(this, new EventInfo("trade.wave.batch.pack.pc").setArgs(new Object[]{staff, sidList, packmaOuterIds, IpUtils.getClientIP(request)}), null);
        return successResponse();
    }

    /**
     * 根据快递单号 获取波次信息
     *
     * @param waveId
     * @param outSid
     * @param staff
     * @return
     */
    private Long getWaveIdByOutSid(Long waveId, String outSid, Staff staff) {
        if (null != outSid) {
            List<Trade> trades = tradeSearchService.queryByOutSid(staff, outSid, false, null);
            Assert.notEmpty(trades, "根据快递单号未查询到订单！");
            Assert.isTrue(DataUtils.checkLongNotEmpty(trades.get(0).getWaveId()), "该快递单号未加入波次号！");
            return trades.get(0).getWaveId();
        }
        return waveId;
    }

    private List<Map<String, Object>> getPackmaOuterIdArr(String sids, String packmaOuterIds) {
        if (StringUtils.isEmpty(packmaOuterIds)) {
            return null;
        }
        try {
            if (sids.contains(",")) {
                throw new IllegalArgumentException("包材商品扫描不允许跨多笔订单");
            }
            return JSON.parseObject(packmaOuterIds, new TypeReference<List<Map<String, Object>>>() {
            });
        } catch (Exception e) {
            throw new IllegalArgumentException("包材商品格式有问题.");
        }
    }

    /**
     * 计算包裹的运费
     */
    @RequestMapping(value = "/freight/cost", method = RequestMethod.POST)
    public @ResponseBody
    Object freightCost(Long sid, String weight, String api_name) throws SessionException {
        return sysTradeService.calculateFreightCost(new CalculateFreightCostParams.Builder().sid(sid).weight(weight).staff(getStaff()).build());
    }

    /**
     * 称重
     * modify by wangyue.pqs 称重接口合并
     */
    @RequestMapping(value = "/weight", method = RequestMethod.POST)
//    @LogTag(key = "#sids", content = "'称重包裹，系统单号：' + #sids + '，重量：'+ #weight + '，运费：' + #cost +'，kind：'+ #type ", action = "weight")
    public @ResponseBody
    Object weightTrades(String sids, String weight, String cost, Integer kind, Long staffId, String packmaOuterIds, String outSid,Integer isRepairOrder,String repairOrderNum, Long packStaffId,String api_name, String actualVolume, String actualLengthWidthAndHeight) throws SessionException {
        Assert.notNull(sids, "请输入sid参数");
        Assert.isTrue(StringUtils.isNotEmpty(weight), "请输入weight参数");
        if (!weight.matches("^\\d{1,7}\\.?\\d{0,4}$")) {
            throw new IllegalArgumentException("重量长度过长或格式不正确，请重新输入!");
        }
        if (NumberUtils.isGreaterThan(Double.valueOf(weight), 50000D)) {
            throw new IllegalArgumentException("包裹重量大于50000kg，请检查后重新输入");
        }
        if (StringUtils.isNotEmpty(cost) && NumberUtils.isGreaterThan(Double.valueOf(cost), 100000D)) {
            throw new IllegalArgumentException("cost运费大于100000，请检查后重新输入");
        }
        if (StringUtils.isNotEmpty(actualVolume) && NumberUtils.isGreaterThan(Double.valueOf(actualVolume), 100000D)) {
            throw new IllegalArgumentException("实际体积大于100000，请检查后重新输入!");
        }
        //如果指定staffId，则需判断配置是否打开
        Staff staff = null;
        if (staffId == null) {
            staff = getStaff();
        } else {
            staff = staffAssembleBusiness.getStaffById(staffId);
            Assert.notNull(staff, "请选择正确的员工账号!");
            TradeConfig config = tradeConfigService.get(staff);
            if (config != null && (int) TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.WEIGH_SUPPORT_SWITCH_ACCOUNT.getKey()) == 0) {
                Assert.isTrue(false, "页面已过期或配置已变更，请刷新页面重试！");
            }
        }
        if (Objects.equals(isRepairOrder,1)) {
            String orderWeigh = asRepairService.repairOrderWeigh(staff, repairOrderNum, new BigDecimal(weight));
            if (orderWeigh == null) {
                return successResponse();
            }else {
                Assert.isTrue(false, orderWeigh);
            }
        }

        Staff packStaff = null;
        if (packStaffId != null && packStaffId > 0) {
            packStaff = staffAssembleBusiness.getStaffById(packStaffId);
            Assert.notNull(packStaff, "请选择正确的打包员工账号!");
        }

        TradeConfig config = tradeConfigService.get(staff);
        Long companyId = staff.getCompanyId();
        if (11369L == companyId || 49365L == companyId) {
            // 校验上传异常,只开11369和49365这两个公司
            List<Trade> tradeList = tradeSearchService.queryBySidsContainMergeTrade(staff, false, Long.valueOf(sids));
            validateTradeUploadExcept(staff, tradeList);
        }
        Long sid = Long.valueOf(sids);
        sysTradeService.tradeNewWeight(new TradeWeightParams.Builder().staff(staff).packStaff(packStaff).sid(sid).weight(weight).cost(cost).kind(kind)
                .clientIp(IpUtils.getClientIP(request)).outSid(outSid).packmaOuterIds(packmaOuterIds).actualVolume(actualVolume)
                .actualLengthWidthAndHeight(actualLengthWidthAndHeight).build());
        List<Map<String, Object>> packmaOuterIdArr = getPackmaOuterIdArr(sids, packmaOuterIds);
        if (packmaOuterIdArr != null) {
            if(itemPackmaBusiness.isNewPackma(kind, packmaOuterIdArr)){
                itemPackmaBusiness.sendCreateNewPackmaLogisticsOrderEvent(staff,kind,Collections.singletonList(sid),packmaOuterIdArr,ItemPackmaBusiness.TRADE_WEIGHT);
            }else{
                eventCenter.fireEvent(this, new EventInfo("trade.pack.packma.item").setArgs(new Object[]{staff, Long.parseLong(sids), packmaOuterIdArr, IpUtils.getClientIP(request), 1}), null);

            }
        }
        // 虾皮是否开启首公里预报功能  SHOPEE_FIRST_MILE_PUSH_STEP 为0 称重后组包 1发货后组包
        if (config != null
                && TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.SHOPEE_IS_FIRST_MILE_PUSH.getKey()) != null &&
                (int) TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.SHOPEE_IS_FIRST_MILE_PUSH.getKey()) == 1
                && TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.SHOPEE_FIRST_MILE_PUSH_STEP.getKey()) != null &&
                (int) TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.SHOPEE_FIRST_MILE_PUSH_STEP.getKey()) == 0
        ) {
            Object shoppeKey = TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.SHOPEE_SHOP_TAOBAO_ID.getKey());
            if (shoppeKey != null && StringUtils.isNotBlank(String.valueOf(shoppeKey))) {
                Long shopeeId = Long.valueOf(String.valueOf(shoppeKey));
                Trade trade = tradeSearchService.queryBySid(staff, true, Long.valueOf(sids));
                if (trade != null && Objects.equals(trade.getSource(), "shopee")) {
                    eventCenter.fireEvent(this, new EventInfo("combineParcel.first.mile.push").setArgs(new Object[]{staff, Long.parseLong(sids), shopeeId}), null);
                }
            }
        }
        StringBuilder content = new StringBuilder("称重包裹，系统单号：").append(sids).append("，重量：").append(weight).append("，运费：").append("，kind：").append(kind).append(",包材：").append(packmaOuterIds).append(",包材：").append(packmaOuterIds).append("，具体称重人").append(staff.getName());
        if (packStaff != null) {
            content.append("，打包人：").append(packStaff.getName());
        }
        OpLogHelper.recodeOpLog(opLogService, request, getStaff(), "weight", null, content.toString(), null);
        return successResponse();
    }

    @RequestMapping(value = {"/wave/weight", "/by/wave/weight"}, method = RequestMethod.POST)
    @ResponseBody
    public Object weightTradeByWaveId(Long waveId, String weight, String cost, Integer kind, Long staffId, String api_name, String packmaOuterIds,Long packStaffId) throws SessionException {
        Assert.notNull(waveId, "请传入波次号");
        Staff staff = getStaff();

        TradeConfig tradeConfig = tradeConfigService.get(staff);
        //如果需要进行沉重称重
        Assert.notNull(weight, "重量不能为空！");
        Assert.notNull(kind, "称重类型不能为空！");

        //包材商品格式校验
        if (StringUtils.isNotEmpty(packmaOuterIds)) {
            try {
                JSON.parseObject(packmaOuterIds, new TypeReference<List<Map<String, Object>>>() {
                });
            } catch (Exception e) {
                throw new IllegalArgumentException("包材商品格式有问题！");
            }
        }
        Staff packStaff = null;
        if (packStaffId != null && packStaffId > 0) {
            packStaff =  staffAssembleBusiness.getStaffById(packStaffId);
            Assert.notNull(packStaff, "请选择正确的打包员工账号!");
        }

        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_ACCORDING_WAVE_WEIGHT_BATCH), "上一次批量称重还未执行完毕，请稍等！");
        if (null != staffId && staffId > 0) {
            staff = staffAssembleBusiness.getStaffById(staffId);
            Assert.notNull(staff, "请选择正确的员工账号!");
            if (tradeConfig != null && tradeConfig.get(TradeExtendConfigsEnum.WEIGH_SUPPORT_SWITCH_ACCOUNT.getKey()).equals(0)) {
                Assert.isTrue(false, "页面已过期或配置已变更，请刷新页面重试！");
            }
        }
        //异步处理
        eventCenter.fireEvent(this, new EventInfo("trade.weight.batch").setArgs(new Object[]{staff, waveId, weight, cost, kind, IpUtils.getClientIP(request), null, packmaOuterIds,packStaff}), null);

        StringBuilder content = new StringBuilder("称重包裹，通过波次号批量称重，波次号：").append(waveId).append("，重量：").append(weight).append("，运费：").append("，kind：").append(kind).append(",包材：").append(packmaOuterIds).append(",包材：").append(packmaOuterIds).append("，具体称重人").append(staff.getName());
        if (packStaff != null) {
            content.append("，打包人：").append(packStaff.getName());
        }
        OpLogHelper.recodeOpLog(opLogService, request, getStaff(), "weight.batch", null, content.toString(), null);
        return successResponse();
    }

    /*    *//**
     * 重复称重
     *//*
    @RequestMapping(value = "/weight/redo", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'重复称重包裹，系统单号：' + #sid + ',重量：'+ #weight + ',运费：' + #cost ", action = "re_weight")
    public @ResponseBody
    Object reWeightTrades(Long sid, String weight, String cost, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入sid参数");
        Assert.isTrue(StringUtils.isNotEmpty(weight), "请输入weight参数");
        Assert.isTrue(StringUtils.isNotEmpty(cost), "请输入cost参数");
        sysTradeService.reWeightTrade(getStaff(), sid, Double.parseDouble(weight.trim()), cost);
        return successResponse();
    }

    *//**
     * 已发货称重
     *//*
    @RequestMapping(value = "/delivered/weigh", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'已发货包裹称重，系统单号：' + #sid + ',重量：'+ #weight + ',运费：' + #cost ", action = "weighDeliveredTrade")
    @ResponseBody
    public Object weighDeliveredTrade(Long sid, String weight, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入sid参数");
        Assert.isTrue(StringUtils.isNotEmpty(weight), "请输入weight参数");
        sysTradeService.weighDeliveredTrade(getStaff(), sid, Double.parseDouble(weight.trim()), null);
        return successResponse();
    }*/

    /**
     * 订单称重/批量称重
     * 已经统一迁移到/trade/weight
     */
    @RequestMapping(value = "/weigh", method = RequestMethod.POST)
    public @ResponseBody
    Object weigh(String mixKey, Double weight, Long staffId, Long waveId, Integer kind,Long packStaffId, String api_name) throws Exception {
        Assert.isTrue(weight != null && weight >= 0, "重量输入有误");
        if (NumberUtils.isGreaterThan(weight, 50000D)) {
            throw new IllegalArgumentException("包裹重量大于50000kg，请检查后重新输入");
        }
        Staff staff = getStaff();
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        if (null != staffId && staffId > 0) {
            staff = staffAssembleBusiness.getStaffById(staffId);
            Assert.notNull(staff, "请选择正确的员工账号!");
            Assert.isTrue(!(tradeConfig != null && tradeConfig.get(TradeExtendConfigsEnum.WEIGH_SUPPORT_SWITCH_ACCOUNT.getKey()).equals(0)), "页面已过期或配置已变更，请刷新页面重试！");
        }
        Staff packStaff = null;
        if (packStaffId != null && packStaffId > 0) {
            packStaff = staffAssembleBusiness.getStaffById(packStaffId);
            Assert.notNull(packStaff, "请选择正确的打包员工账号!");
        }
        // 兼容原接口功能，4为发货前批量称重，1为发货后批量称重
        kind = kind == null ? 4 : kind;
        //根据波次号批量称重
        if (null != waveId && waveId > 0) {
            Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_ACCORDING_WAVE_WEIGHT_BATCH), "上一次批量称重还未执行完毕，请稍等！");

            //异步处理
            eventCenter.fireEvent(this, new EventInfo("trade.weight.batch").setArgs(new Object[]{staff, waveId, MathUtils.toBigDecimal(weight).toPlainString(), null, kind, IpUtils.getClientIP(request), 1,null,packStaff}), null);
            return successResponse();
        }
        Assert.isTrue(StringUtils.isNotBlank(mixKey), "单号错误");
        TradeValidator validator = new TradeValidator();
        validator.setThrowExceptionIfError(false);
        //批量称重忽略缺货异常
        validator.setIgnoreInsufficient(true);
        Long companyId = staff.getCompanyId();
        if (11369L == companyId || 49365L == companyId) {
            // 校验上传异常,只开11369和49365这两个公司
            validator.setCheckUploadException(true);
        }
        String queryKey = TradeWeighUtils.restoreJdOutSid(staff.getCompanyId(), mixKey);
        Trade trade = tradeQueryService.queryByMixKey(staff, queryKey);

        if (trade == null && Objects.equals(kind,4)
                &&  ConfigHolder.TRADE_SEND_PACKAGE_INFO_CONFIG.isAsRepairOrderCompanyId(staff.getCompanyId())) {
            Object status = queryAndWeighRepairOrder(staff, trade, kind, queryKey,weight);
            if (status != null) {
                return status;
            }
        }

        Assert.notNull(trade, "订单未找到！");
        if (trade != null && !trade.getSid().toString().equals(mixKey) && !trade.getShortId().toString().equals(mixKey)) {
            Assert.isTrue(trade.getOutSid() !=null && trade.getOutSid().equalsIgnoreCase(TradeWeighUtils.getJdMainOutSid(companyId, queryKey)), "批量称重只支持主单号");
        }
        Assert.isTrue(trade != null, "未查询到订单");
        if (kind.equals(4)) {
            tradeValidateBusiness.checkWeighBeforeConsign(staff, trade, validator, tradeConfig, trade == null ? "" : trade.getOutSid());
        } else {
            validator.setCheckAfterSendGoodsRefundException(true);
            tradeValidateBusiness.checkWeighAfterConsign(staff, trade, validator, tradeConfig, kind, trade.getOutSid(), 1);
        }
        if (validator.hasError()) {
            if (TradeValidator.Error.NOT_FOUND.getCode() != validator.getCode()) {
                List<Trade> o1 = TradeTraceUtils.createTradeS(TradeUtils.toSidList(tradeSearchService.queryBySidsContainMergeTrade(staff, trade.getSid())), "称重失败，原因：" + validator.getMessage());
                eventCenter.fireEvent(this, new EventInfo("trade.custom.trace.save").setArgs(new Object[]{staff, "称重失败"}),
                        o1);
            }
            return ResponseDataWrapperBuilder.build(api_name, 0, validator.getMessage(), null).setSubCode(validator.getCode());
        }
        Trade temp = new TbTrade();
        temp.setSid(trade.getSid());
        temp.setWeight(weight);
        Map<Long, Trade> tradeMap = Collections.singletonMap(temp.getSid(), temp);
        Map<String, String> errors = sysTradeService.batchWeigh(staff, tradeMap, IpUtils.getClientIP(request), kind,packStaff);
        if (errors == null || errors.isEmpty()) {
            return TradeModels.toTradeModels(staff, tradeSearchService.queryBySids(staff, true, false, true, temp.getSid()), shopService, expressCompanyService, false, null, tradeConfig);
        }
        return errors;
    }

    public PageListBase<AsRepairWeighInfo> queryAndWeighRepairOrder(Staff staff, Trade trade, Integer kind, String mixKey, Double weight){

        if (StringUtils.isBlank(mixKey)) {
            return null;
        }
        List<RepairOrderResponse> response = asRepairService.getWeighingRepairOrderListBySendLogisticsNo(staff, mixKey);
        if (CollectionUtils.isEmpty(response) || response.get(0) == null) {
            return null;
        }
        RepairOrderResponse repairOrder = response.get(0);
        String repairOrderNum = repairOrder.getRepairOrderNum();
        String orderWeigh = asRepairService.repairOrderWeigh(staff, repairOrderNum, new BigDecimal(weight));
        if (orderWeigh == null) {
            AsRepairWeighInfo asInfo = AsRepairWeighInfo.convertRepairWeighInfo(staff,response);
            PageListBase<AsRepairWeighInfo> model = new PageListBase<AsRepairWeighInfo>(){};
            model.setList(Arrays.asList(asInfo));
            model.setTotal(1L);
            return model;
        }else {
            Assert.isTrue(false, orderWeigh);
        }
        return null;
    }

    @RequestMapping(value = "/box/get", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object queryBox(String code, String api_name) throws Exception {
        Staff staff = getStaff();
        ItemBox object = itemServiceWrapper.queryByCode(staff, code);
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        if (object != null && object.getItemBoxSingles() != null) {
            for (ItemBoxSingle single : object.getItemBoxSingles()) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("num", single.getNums());
                map.put("outerId", single.getOuterId());
                map.put("sysItemId", single.getSysItemId());
                map.put("sysSkuId", single.getSysSkuId());
                result.add(map);
            }
        }
        return result;
    }

    @RequestMapping(value = "/logistics/exceptlist")
    @AccessShield(value = "221", condition = @AccessCondition(field = "queryId", expected = {"70"}))
    public @ResponseBody
    Object queryConsignDetail(LogisticsTrackingPollPoolQueryParams params, String express, Page page, Sort sort, String api_name) throws SessionException, TradeFilterException {
        Staff staff = getStaff();
        params.setPage(page);
        params.setSort(sort);
        params.express2TemplateIds(express);
        List<LogisticsTrackingPollPool> records;
        LogisticsTrackingRecordVos vos = null;
        params.getStartDate();
        params.getEndDate();
        //参数转换
        params.setSids(getAsLongArray(params.getSidStr(), ",", true));
        params.setTids(getAsStringArray(params.getTid(), ",", true));
        params.setOutSids(getAsStringArray(params.getOutSid(), ",", true));

        records = logisticsTrackingRecordService.listLogisticsExcept(staff, params);
        //放心购订单需要替换buyerNick和receiver_name为脱敏数据
        fxgTradeDecryptBusiness.replaceSensitiveInfo(staff, records);
        commonTradeDecryptBusiness.batchTradeSensitive(staff, records);
        defaultTemplateFilter.filterTrades(staff, records);
        vos = LogisticsTrackingRecordVos.toConsignRecordVos(staff, records, false, shopService, staffService);
        vos.setTotal(logisticsTrackingRecordService.countListLogisticsExcept(staff, params).longValue());
        vos.setPage(page);
        return vos;
    }

    /**
     * 用户关注/取消关注
     *
     * @param params
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/logistics/updateTracking")
    @ResponseBody
    public Object updateLogisticsExceptTrackingStatus(LogisticsTrackingPollPoolQueryParams params) throws SessionException {
        Staff staff = getStaff();
        Long sid = params.getSid();
        Integer needTracking = params.getNeedTracking();
        logisticsTrackingRecordService.updateLogisticsExceptTrackingStatus(staff, sid, needTracking);
        return successResponse();
    }

    /**
     * 批量用户关注/取消关注
     *
     * @param sids
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/logistics/updateTracking/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object batchUpdateLogisticsExceptTrackingStatus(String sids, Integer needTracking) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请选择要操作的记录！");
        Staff staff = getStaff();
        return logisticsTrackingRecordService.batchUpdateLogisticsExceptTrackingStatus(staff, ArrayUtils.toLongArray(sids), needTracking);
    }

    /**
     * 校验订单，需要传递打印日志编号
     */
    @RequestMapping(value = "/validate", method = RequestMethod.POST)
    @LogTag(key = "#logId", content = "'校验订单，打印日志编号：' + #logId", action = "validate")
    public
    @ResponseBody
    Object validateTrades(Long logId, Integer startSeq, String startOutSid, Integer endSeq, String endOutSid, String api_name) throws SessionException {
        Staff staff = getStaff();
        return sysTradeService.validateTrades(staff, logId, startSeq, startOutSid, endSeq, endOutSid);
    }

    /**
     * 查询某个打印记录下需要重新打印的订单
     */
    @RequestMapping(value = "/reprint/query", method = RequestMethod.GET)
    @ResponseBody
    public Object queryReprintTrades(Long logId, String api_name) throws SessionException {
        Staff staff = getStaff();
        return TradeModels.toTradeModels(staff, sysTradeService.queryReprintTrades(staff, logId), shopService, expressCompanyService, false, null, tradeConfigService.get(staff), false);
    }

    /**
     * 查询某个打印记录下需要重新打印的订单
     */
    @RequestMapping(value = "/reprint/query/detail", method = RequestMethod.GET)
    @ResponseBody
    public Object queryReprintTradesDetail(Long logId, Page page, TradeControllerParams params, String api_name) throws SessionException {
        Staff staff = getStaff();
        PageListBase<PrintTradeLogDetailVO> tradeLogDetailList = sysTradeService.queryReprintTradesDetail(staff, logId, page, params);
        if (CollectionUtils.isNotEmpty(tradeLogDetailList.getList())) {
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            List<PrintTradeLogDetailVO> detailVOList = tradeLogDetailList.getList();
            List<Long> longList = detailVOList.stream().map(PrintTradeLogDetailVO::getSid).collect(Collectors.toList());
            List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, longList.stream().toArray(Long[]::new));
            Map<Long, Trade> tradeMap = tradeList.stream().collect(Collectors.toMap(Trade::getSid, Function.identity(), (k1, k2) -> k1));
            TradeModels.fillTrade(staff, detailVOList, tradeMap, tradeConfig);
        }
        return tradeLogDetailList;
    }

    /**
     * 查询物流流转信息
     */
    @RequestMapping(value = "/logistics/trace/search", method = RequestMethod.GET)
    @ResponseBody
    public Object searchTraceLogistics(Long sid, String oids, String api_name) throws SessionException {
        Staff staff = getStaff();
        return sysTradeService.searchTraceLogistics(staff, sid, oids);
    }

    /**
     * 查询异常物流流转信息
     */
    @RequestMapping(value = "/logistics/excepttrace/search", method = RequestMethod.GET)
    @ResponseBody
    public Object searchTraceLogistics(Long sid, String api_name) throws SessionException {
        Staff staff = getStaff();
        return sysTradeService.searchTraceLogistics(staff, sid, null);
    }

    /**
     * 延长收货时间
     */
    @RequestMapping(value = "/receivetime/delay", method = RequestMethod.POST)
    @ResponseBody
    public Object delayReceiveTime(Long sid, Integer days, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入系统单号");
        Assert.notNull(days, "请输入延长的天数");

        if (!(days == 3 || days == 5 || days == 7 || days == 10)) {
            throw new IllegalArgumentException("可选值为：3, 5, 7, 10");
        }
        Staff staff = getStaff();
        sysTradeService.delayReceiveTime(staff, sid, days);
        return successResponse();
    }

    @AccessShield(value = "211", condition = @AccessCondition(field = "queryId", expected = {"9"}))
    @RequestMapping(value = "/search3month", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    Object search3month(TradeControllerParams queryParams, Page page, Sort sort, String api_name) throws SessionException {
        long start = System.currentTimeMillis();
        TradeQueryParams params = TradeQueryParams.copyParams(queryParams);
        params.setPage(page);
        params.setSort(sort);
        Staff staff = getStaff();
        try {
            Trades trades = tradeDataService.search3Month(staff, params, true);
            boolean highlight = queryParams.getHighlight() == null ? false : queryParams.getHighlight();
            return TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, highlight, queryParams.getText(), queryParams.getHighlightTid(), tradeConfigService.get(staff));
        } finally {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append("search3month took:").append(System.currentTimeMillis() - start).append(" ms."));
            }
        }
    }

    public  void batchSensitive (Staff staff, List <Trade> trades){
        //3个月之前的订单 不再调平台脱敏接口
        try {
            if (CollectionUtils.isEmpty(trades)) {
                return;
            }
            List<Trade> list = trades.stream().filter(x -> secretBusiness.isSysEncode(x)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                secretBusiness.decodeTrades(staff, list);
            }
            for (Trade trade : trades) {
                trade.setReceiverName(TradeSysDigestUtils.toReceiverName(trade.getReceiverName()));
                trade.setBuyerNick(TradeSysDigestUtils.toReceiverName(trade.getBuyerNick()));

                trade.setReceiverAddress("*****");

                trade.setReceiverMobile(TradeSysDigestUtils.toPhone(trade.getReceiverMobile()));
                trade.setReceiverPhone(TradeSysDigestUtils.toPhone(trade.getReceiverPhone()));
            }
        } catch (Throwable e) {
            new QueryLogBuilder(staff).appendError("数据脱敏处理失败",e).printWarn(logger,e);
        }
    }

    @RequestMapping(value = "/search3month/sids", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object search3monthBySids(String sids, Boolean highlight, String text, Integer blurTrade,
                                     @RequestParam(value = "highlightTid", required = false) String highlightTid, String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");
        if (highlight == null) {
            highlight = false;
        }

        Long[] sidArray = ArrayUtils.toLongArray(sids);
        Assert.isTrue(org.apache.commons.lang3.ArrayUtils.isNotEmpty(sidArray), "请输入正确的sids参数");

        Staff staff = getStaff();

        long start = System.currentTimeMillis();
        TradeQueryParams queryParams = new TradeQueryParams();
        queryParams.setSid(sidArray);
        queryParams.setPage(new Page().setPageSize(100).setPageNo(1));
        queryParams.setQueryOrder(true);
        queryParams.setExportSource(TradeColdDataService.EXPORT_THREE_MONTH_AGO);
        if (queryParams.getSysMask() != 2) {
            queryParams.setSysMask(1);
        }
        try {
            Trades trades = tradeDataService.search3Month(staff, queryParams, false);
            return TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, highlight, text, highlightTid, tradeConfigService.get(staff));
        } finally {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append("search3monthBySids took:").append(System.currentTimeMillis() - start).append(" ms."));
            }
        }
    }

    /**
     * 复制订单
     */
    @RequestMapping(value = "/copyTrade", method = RequestMethod.POST)
    public @ResponseBody
    Object copyTrade(HttpServletRequest request, @RequestBody TradeCopyParams tradeCopyParams, String api_name) throws SessionException {
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, tradeCopyParams);
        Assert.isTrue(StringUtils.isNotEmpty(tradeCopyParams.getSid() + ""), "请输入系统订单号");
        Assert.isTrue(tradeCopyParams.getCopyNum() != null, "请输入复制数量");
        Assert.isTrue(tradeCopyParams.getCopyNum() <= 100, "最多复制100笔");
        SecurityEventTrackingBatchOrderRequest fxgSecurityRequest = FxgSecurityUtil.buildFxgSecurityRequest(request);
        tradeCopyParams.setFxgSecurityRequest(fxgSecurityRequest);
        List<Trade> trades = sysTradeService.copy(staff, tradeCopyParams);
        fxgTradeDecryptBusiness.batchSensitive(staff, trades);//复制新建不解密 脱密显示
        ksTradeDecryptBusiness.batchSensitive(staff, trades);//复制新建不解密 脱密显示
        return TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
    }

    /**
     * 批量复制订单
     */
    @RequestMapping(value = "/copyTrade/batch", method = RequestMethod.POST)
    public @ResponseBody
    Object copyTrade(String sids, String api_name, String userId, Boolean copyOrderExtOrderRemark,Boolean copyFilterClosedOrders) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请先选择您需要操作的订单！");
        Staff staff = getStaff();
        SecurityEventTrackingBatchOrderRequest fxgSecurityRequest = FxgSecurityUtil.buildFxgSecurityRequest(request);
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        if (sidArray.length > 100) {
            Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_TRADE_COPY_BATCH), "当前正在批量复制订单，请稍后再试！");
            eventCenter.fireEvent(this, new EventInfo("trade.copyTrade.batch").setArgs(new Object[]{staff, sids, fxgSecurityRequest, userId, copyOrderExtOrderRemark,copyFilterClosedOrders}), null);
        } else {
            TradeCopyParams tradeCopyParams = TradeCopyParams.builder().sids(sidArray).force(true).copyNum(1).build();
            if (StringUtils.isNotEmpty(userId)) {
                tradeCopyParams.setUserId(Long.valueOf(userId));
            }
            if(copyOrderExtOrderRemark != null){
                tradeCopyParams.setCopyOrderExtOrderRemark(copyOrderExtOrderRemark);
            }
            if (copyFilterClosedOrders != null){
                tradeCopyParams.setCopyFilterClosedOrders(copyFilterClosedOrders);
            }
            List<Trade> allCopyTrades = sysTradeService.copyBatch(staff, tradeCopyParams);
            ksTradeDecryptBusiness.batchSensitive(staff, allCopyTrades);//复制新建不解密 脱密显示
            fxgTradeDecryptBusiness.batchSensitive(staff, allCopyTrades);//复制新建不解密 脱密显示
            Map<String, Object> result = new HashMap<>();
            result.put("failSid", MapUtils.isEmpty(tradeCopyParams.getFailSid()) ? "" : JSONObject.toJSON(tradeCopyParams.getFailSid()));
            result.put("tradeModels", TradeModels.toTradeModels(staff, allCopyTrades, shopService, expressCompanyService, false, null, tradeConfigService.get(staff)));
            result.put("failCount", MapUtils.isEmpty(tradeCopyParams.getFailSid()) ? 0 : tradeCopyParams.getFailSid().size());
            result.put("successCount", allCopyTrades.size());
            return result;
        }
        return successResponse();
    }

    @RequestMapping(value = "/boxingList/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateBoxingList(@RequestBody TbTrade trade, String api_name) throws SessionException {
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, trade);
        return sysTradeService.updateBoxingList(staff, trade, true);
    }

    /**
     * 更新运费
     *
     * @param api_name trade_update_postfee
     */
    @RequestMapping(value = "/postfee/update", method = RequestMethod.POST)
    @LogTag(key = "#sid", content = "'修改运费，系统单号为：' + #sid + '，内容为：' + #postfee", action = "update_postfee")
    @ResponseBody
    public Object updatePostfee(Long sid, String postfee, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入系统订单号");
        Assert.notNull(postfee, "请输入运费");
        return sysTradeService.updatePostfee(getStaff(), sid, postfee);
    }

    /**
     * 更新理论运费
     *
     * @param api_name trade_update_theoryPostFee
     */
    @RequestMapping(value = "/theoryPostFee/update", method = RequestMethod.POST)
    @LogTag(key = "#sid", content = "'修改理论运费，系统单号为：' + #sid + '，内容为：' + #theoryPostFee", action = "update_theoryPostFee")
    @ResponseBody
    public Object updateTheoryFreight(Long sid, String theoryPostFee, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入系统订单号");
        Assert.notNull(theoryPostFee, "请输入理论运费");
        sysTradeService.updateTheoryPostFee(getStaff(), sid, theoryPostFee);
        return successResponse();
    }

    /**
     * 更新实际运费
     */
    @RequestMapping(value = "/actualPostFee/update", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'修改实际运费，系统单号为：' + #sids + '，内容为：' + #actualPostFee", action = "update_actualPostFee")
    @ResponseBody
    public Object updateActualPostFee(TradeControllerParams queryParams, String sids, String actualPostFee, String api_name) throws SessionException {
        Assert.notNull(actualPostFee, "请输入实际运费");
        Staff staff = getStaff();
        if (StringUtils.isBlank(sids)) {
            Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_UPDATE_ACTUAL_POSTFEE), "已经在执行批量修改实际运费任务，请稍后再试！");
        }
        TradeQueryParams params = TradeQueryParams.copyParams(queryParams);
        eventCenter.fireEvent(this, new EventInfo("trade.update.actual.postfee").setArgs(new Object[]{staff, sids, params, actualPostFee}), null);
        return successResponse();
    }

    /**
     * 拒绝退款
     */
    @RequestMapping(value = "/refuse/refund", method = RequestMethod.POST)
    @ResponseBody
    public Object refuseRefund(String sids, String api_name) throws SessionException {
        Assert.notNull(sids, "请输入系统订单号");
        Staff staff = getStaff();
        return toTradeModels(staff, sysTradeService.refuseRefund(staff, ArrayUtils.toLongArray(sids)));
    }

    /**
     * 智能分配快递
     */
    @RequestMapping(value = "/express/match", method = RequestMethod.POST)
    @ResponseBody
    public Object matchTemplate(TradeControllerParams queryParams, Sort sort, Integer revert, String api_name) throws SessionException {
        Staff staff = getStaff();
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_EXPRESS_MATCH), "已经在执行快递匹配任务，请稍后再试！");
        TradeQueryParams params = TradeQueryParams.copyParams(queryParams);
        params.setSort(sort);
        //初始化进度条
        progressService.setProgress(staff, ProgressEnum.PROGRESS_EXPRESS_MATCH, 1);
        eventCenter.fireEvent(this, new EventInfo("trade.express.match.manual").setArgs(new Object[]{staff, params, revert}), null);
        return successResponse();
    }

    /**
     * excel运单号导入
     */
    @RequestMapping(value = "/import/outsid", method = RequestMethod.POST)
    public @ResponseBody
    Object importTrades(MultipartFile file, Integer outsidImportType, Integer needCover, String api_name) throws SessionException, IOException {
        Staff staff = getStaff();
//        Long waitPrintCount = tradeCountService.countWaitPrint(staff);
//        Assert.isTrue(waitPrintCount > 0, "当前页面无订单数据，不需要导入运单号");
        Assert.notNull(file, "请选择要导入的excel文件");
        Assert.isTrue(file.getSize() < 1024 * 1024 * 2, "上传的excel文件大小不能超过2MB");
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT), "上一次运单号导入还未执行完毕，请稍等！");

        String[][] data;
        long start = System.currentTimeMillis();
        try {
            data = ExcelConverter.excel2DataArr(file.getInputStream(), 1);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append("解析运单号Excel,共" + data.length + "行，耗时" + (System.currentTimeMillis() - start) + "ms"));
            }
            Assert.notNull(data, "读取Excel文件报错");
            if (data.length == 0) {
                progressService.setProgress(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT, data.length, 2);
                return successResponse();
            }
            //data[0].length == 6 是因为老版本解析excel数据多出一个空白行  erp.0.5-SNAPSHOT版本dmj-storage-utils 有这个问题 暂时做兼容
            Assert.isTrue((data[0].length == 5 || data[0].length == 6) && StringUtils.isNotBlank(data[0][3]), "Excel文件格式不对");
        } catch (Exception e) {
            throw new TradeException("上传的文件格式不正确");
        }
        //解析完后设置缓存
        progressService.setProgress(staff, ProgressEnum.PROGRESS_TRADE_OUTSID_IMPORT, data.length);
        //异步处理导入
        FileResult fileResult = fileUploadService.upload(file.getOriginalFilename(), data);
        Assert.isTrue(fileResult != null && !StringUtils.isEmpty(fileResult.getUrl()), "导入过程中上传失败");
        eventCenter.fireEvent(this, new EventInfo("trade.outsid.import").setArgs(new Object[]{staff, outsidImportType, needCover == null ? 0 : needCover}), fileResult.getUrl());
        //返回成功
        return successResponse();
    }

    /**
     * 按比例分配快递
     */
    @RequestMapping(value = "/express/percentage/match", method = RequestMethod.POST)
    public @ResponseBody
    Object percentageMatchTemplate(Long queryId, String api_name, String sids) throws Exception {
        if (queryId == null && StringUtils.isBlank(sids)) {
            throw new IllegalArgumentException("未获取到订单号列表和处理订单页面!");
        }
        Staff staff = getStaff();
        TradeQueryParams params = new TradeQueryParams();
        if (queryId != null) {
            params.setQueryId(queryId);
        }
        if (StringUtils.isNotBlank(sids)) {
            Long sid[] = (Long[]) ConvertUtils.convert(sids.split(","), Long.class);
            params.setSid(sid);
        }
        tradeSwitchBusiness.checkOpen(TradeSwitchBusiness.CLOSE_EXPRESS_PERCENTAGE_MATCH, staff.getCompanyId());
        List<ExpressPercentageMatch> rules = expressPercentageMatchService.list(staff, null);
        Assert.isTrue(rules != null && rules.size() > 0, "未设置快递比例，请前往快递智能匹配进行设置！");
        //解析完后设置缓存
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_TRADE_EXPRESS_PERCENTAGE_MATCH), "前一次按比例分配快递还未执行完毕，请稍等！");
        //解析完后设置缓存
        progressService.setProgress(staff, ProgressEnum.PROGRESS_TRADE_EXPRESS_PERCENTAGE_MATCH, 1);
        //异步处理导入
        eventCenter.fireEvent(this, new EventInfo("trade.express.percentagematch").setArgs(new Object[]{staff, params}), null);
        //返回成功
        return successResponse();
    }

    /**
     * 包装拆分
     */
    @RequestMapping(value = "/pack/split", method = RequestMethod.POST)
    @LogTag(key = "#tradePackSplit.sid", content = "#tradePackSplit.sid+'拆分装箱'", action = "split_pack")
    public @ResponseBody
    Object packSplitTrade(@RequestBody TradePackSplit tradePackSplit, String api_name) throws SessionException {
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, tradePackSplit);
        return sysTradeService.packSplitTrade(staff, tradePackSplit);
    }

    /**
     * 查询包装拆分
     */
    @RequestMapping(value = "/search/pack/split", method = RequestMethod.POST)
    public @ResponseBody
    Object searchPackSplitTrade(String sid, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入系统订单号");
        return sysTradeService.searchPackSplitTrade(getStaff(), Long.parseLong(sid));
    }

    /**
     * 查询包装拆分明细
     */
    @RequestMapping(value = "/search/pack/split/detail", method = RequestMethod.POST)
    public @ResponseBody
    Object searchPackSplitTradeDetail(String packId, String api_name) throws SessionException {
        Assert.notNull(packId, "请输入装箱编号");
        return sysTradeService.searchPackSplitTradeDetail(getStaff(), Long.parseLong(packId));
    }

    /**
     * 奇门接入自助修改地址接口
     *   TODO 平台文档，内部实现流程，常见问题见：https://gykj.yuque.com/entavv/eltb2u/oxpa75
     */
    @RequestMapping(value = "/address/canbemodified")
    public void addressModify(HttpServletRequest request, HttpServletResponse response) {
        Map map = request.getParameterMap();
        String[] eagleEye = (String[]) map.get("tb_eagleeyex_t");
        if (eagleEye != null && eagleEye.length > 0 && "1".equals(eagleEye[0])) {
            JSONObject jsonObject = new JSONObject();
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("errorCode", "200");
            result.put("errorMsg", "");
            jsonObject.put("result", result);
            TradeParty3RedirectUtils.getJSONResult(jsonObject, response);
            return;
        }
        JSONObject jsonObject = new JSONObject();
        String requestParam = null;
        JSONObject result = new JSONObject();
        try {
            CheckResult checkResult = SpiUtils.checkSign(request, TbAppInfo.APPSECRET);
            if (checkResult.isSuccess()) {
                // 业务逻辑 地址修改。。
                // do modify address
                requestParam = checkResult.getRequestBody();
                JSONObject paramsObject = JSON.parseObject(requestParam);
                // ............获取paramsObject中的参数, 进行业务处理，成功返回如下，失败返回错误信息和错误码
                String sellerNick = paramsObject.getString("sellerNick");
                User user = userService.queryEffectiveUserByNick(sellerNick, CommonConstants.PLAT_FORM_TYPE_TAO_BAO);
                if (user == null) {
                    user = userService.queryEffectiveUserByNick(sellerNick, CommonConstants.PLAT_FORM_TYPE_TIAN_MAO);
                }
                if (user == null) {
                    result.put("success", false);
                    result.put("errorCode", "modify-address-forbid");
                    result.put("errorMsg", "ERP中不存在对应卖家!");
                    jsonObject.put("result", result);
                    logger.debug(String.format("奇门自助改地址根据卖家昵称未查询到店铺信息，不进行处理，sellerNick:%s,bizOrderId:%s",
                            sellerNick, paramsObject.getString("bizOrderId")));
                    TradeParty3RedirectUtils.getJSONResult(jsonObject, response);
                    return;
                }
                //店铺未启用或者关闭订单同步不处理
                Staff staff = staffAssembleBusiness.getDefaultStaff(user.getCompanyId());
                Company company = staff.getCompany();
                if (isCompanyOrUserExpire(company, user)) {
                    //TODO 可以干掉
                    result.put("success", true);
                    result.put("errorCode", "200");
                    result.put("errorMsg", "");
                    jsonObject.put("result", result);
                    Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("奇门自助改地址公司/店铺已到期或不活跃，不进行处理，sellerNick:%s,bizOrderId:%s",
                            sellerNick, paramsObject.getString("bizOrderId"))));
                    TradeParty3RedirectUtils.getJSONResult(jsonObject, response);
                    return;
                }
                staff.setCompany(getCompany(staff));
                staff.setName("系统");
                paramsObject.put("sysUserId", user.getId());
                String grayDomain = companyService.getDomainByCompany(staff.getCompanyId());
                if (TradeParty3RedirectUtils.needRedirectGrayEnv(request, response, grayDomain, requestParam)) {
                    return;
                }
                result = sysTradeService.smartAddressModify(staff, paramsObject);
            } else { // 验签失败固定返回格式
                result.put("success", false);
                result.put("errorCode", "sign-check-failure");
                result.put("errorMsg", "Illegal request");
                jsonObject.put("result", result);
                TradeParty3RedirectUtils.getJSONResult(jsonObject, response);
                return;
            }
        } catch (Exception e) {
            // 异常日志处理，返回错误信息
            logger.error(String.format("ERP自助修改地址发生后台错误!requestParam:{}", requestParam), e);
            result.put("success", false);
            result.put("errorCode", "modify-address-failed");
            result.put("errorMsg", "ERP自助修改地址发生后台错误!");
        }
        jsonObject.put("result", result);
        TradeParty3RedirectUtils.getJSONResult(jsonObject, response);
        return;
    }

    private boolean isCompanyOrUserExpire(Company company, User user) {
        Date now = new Date();
        // 判断公司
        if (Objects.nonNull(company)) {
            // orderType 为1，2不算公司到期
            Integer orderType = company.getOrderType();
            if (!Objects.equals(orderType, 1) && !Objects.equals(orderType, 2)) {
                if (Objects.nonNull(company.getDeadLine()) && company.getDeadLine().before(now)) {
                    return true;
                }
            }
        }
        // 判断店铺
        return (user.getActive() != null && user.getActive() - 1 != 0)
                || (user.getTradeSyncStatus() != null && user.getTradeSyncStatus() == 0)
                || (Objects.nonNull(user.getDeadline()) && user.getDeadline().before(now));
    }

    @RequestMapping(value = "/cloud/match/intelligent/smart", method = RequestMethod.POST)
    @ResponseBody
    public Object intelligentSmart(String sids, String api_name) throws Exception {
        Assert.notNull(sids, "请选择需要物流智选的订单");
        Staff staff = getStaff();
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_TRADE_INTELLIGENT_SMART), "正在进行智选物流，请稍等！");
        String cacheKey = progressService.getProgressKey(staff, ProgressEnum.PROGRESS_TRADE_INTELLIGENT_SMART);

        progressService.setProgress(staff, ProgressEnum.PROGRESS_TRADE_INTELLIGENT_SMART, 1);
        Map<String, Object> result = Maps.newHashMap();
        result.put("cacheKey", cacheKey);
        result.put("progressType", ProgressEnum.PROGRESS_TRADE_INTELLIGENT_SMART.getType());
        result.put("isSuccess", true);

        eventCenter.fireEvent(this, new EventInfo("cloud.match.intelligent.smart").setArgs(new Object[]{staff, sids}), null);
        return result;
    }

    private void validMatch(List<Trade> tradeList) {
        for (Trade trade : tradeList) {
            Assert.isTrue((trade.getSysStatus().equals(Trade.SYS_STATUS_WAIT_AUDIT) || trade.getSysStatus().equals(Trade.SYS_STATUS_FINISHED_AUDIT)), String.format("订单%s系统状态为%s", trade.getSid(), TradeStatusUtils.getChSysStatusView(trade.getSysStatus())));
            Assert.isTrue(TradeStatusUtils.getPrintStatus(trade.getExpressPrintTime()).equals(0), String.format("订单[%s]已打印", trade.getSid()));
        }
    }

/*    @RequestMapping(value = "/manual/giftmatch", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'手工匹配赠品，系统单号为：' + #sids", action = "manual_giftmatch")
    public @ResponseBody
    Object manualGiftMatch(String sids, Integer reMatch, String api_name) throws Exception {
        Assert.notNull(sids, "请选择需要手工匹配赠品的订单");
        Assert.notNull(reMatch, "请选择手工匹配方式");
        Staff staff = getStaff();
        // 0. 参数检查
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        List<Trade> trades = sysTradeService.giftMatch(staff, reMatch, sidArray);
        return TradeModels.toTradeModels(staff, tradeSearchService.queryBySids(staff, true, TradeUtils.toSids(trades)), shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
    }*/

    @RequestMapping(value = "/manual/giftmatch", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object manual(TradeControllerParams queryParams, Integer reMatch, Sort sort, String api_name) throws SessionException {
        Staff staff = getStaff();
        Assert.notNull(reMatch, "请选择手工匹配方式");
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_TRADE_GIFT_MATCH), "其他员工正在进行赠品匹配操作，请稍后重试!");

        eventCenter.fireEvent(this, new EventInfo("trade.gift.match.manual").setArgs(new Object[]{staff, TradeQueryParams.copyParams(queryParams).setSort(sort), reMatch}), null);
        OpLogHelper.recodeOpLog(opLogService, getRequest(), staff, "manual_giftmatch", "giftMatchManual", "[" + staff.getAccountName() + "]手工赠品匹配成功!", JSON.toJSONString(queryParams));
        return successResponse();
    }

    @RequestMapping(value = "/simulation/giftmatch", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object simulation(String sids, String api_name) throws SessionException {

        SimulationMatchGiftResponse response = new SimulationMatchGiftResponse();
        Staff staff = getStaff();
        String[] split = sids.split(",");
        if (split.length == 0) {
            return response;
        }

        for (String sid : split) {
            Assert.isTrue(NumberUtils.str2Long(sid) > 0, "系统单号异常:" + sid);
        }
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, Arrays.stream(split).map(Long::parseLong).toArray(Long[]::new));
        if (CollectionUtils.isEmpty(trades)) {
            response.getFilterMsg().addAll(Arrays.stream(split).map(sid -> "系统订单号：" + sid + "，模拟运行失败，失败原因：系统订单号不存在，请检查后重新输入").collect(Collectors.toList()));
            return response;
        }

        if (trades.size() < split.length) {
            List<Long> tradeSids = trades.stream().map(Trade::getSid).collect(Collectors.toList());
            List<String> errMsg = Arrays.stream(split).map(Long::parseLong).filter(sid -> !tradeSids.contains(sid)).map(sid -> "系统订单号：" + sid + "，模拟运行失败，失败原因：系统订单号不存在，请检查后重新输入").collect(Collectors.toList());
            response.getFilterMsg().addAll(errMsg);
        }
        List<String> checkMessage = new ArrayList<>();
        trades = trades.stream().filter(trade -> giftMatchManualBusiness.checkTrade(staff, trade, checkMessage)).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(checkMessage)) {
            response.getFilterMsg().addAll(checkMessage);
        }

        if (CollectionUtils.isEmpty(trades)) {
            return response;
        }

        trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, trades.stream().map(Trade::getSid).toArray(Long[]::new));
        if (CollectionUtils.isEmpty(trades)) {
            return response;
        }

        Map<Long, List<Trade>> mergeGroupTrades = new HashMap<>();
        trades.forEach(trade -> {
            Long sid = trade.getSid();
            if (trade.getMergeSid() > 0) {
                sid = trade.getMergeSid();
            }
            mergeGroupTrades.computeIfAbsent(sid, k -> new ArrayList<>()).add(trade);
        });

        mergeGroupTrades.forEach((k, v) -> response.getSimulationDataList().add(giftMatchManualBusiness.simulationMatch(staff, v, false)));
        return response;
    }


    /**
     * 批量处理订单备注或留言
     */
    @RequestMapping(value = "/handle/messagememo", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'备注/留言标记为：' + (#isHandlerMemo ==1 ? '已处理' :'未处理') + '/' + (#isHandlerMessage ==1 ? '已处理' :'未处理') ", action = "update_messagememo")
    @ResponseBody
    public Object handleMessagememo(String sids, String handleType, Integer isHandleMessage, Integer isHandlerMemo, String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入系统订单号");
        Staff staff = getStaff();
        List<Trade> trades = sysTradeService.handleMessagememo(staff, ArrayUtils.toLongArray(sids), handleType, isHandleMessage, isHandlerMemo);
        return TradeModels.toTradeModels(staff, tradeSearchService.queryBySids(staff, true, false, true, TradeUtils.toSids(trades)), shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
    }

    /**
     * 批量替换商品(不同于换商品)
     * 将订单下所有的订单替换成修改的商品，删掉多余的商品
     * http://doc.raycloud.com/pages/viewpage.action?pageId=30549715
     */
    @RequestMapping(value = "/change/replaceRemoveItem", method = RequestMethod.POST)
    @ResponseBody
    public Object replaceRemoveItem(String sids, Order orderFront, String api_name, String ifChangeTradeDmsAttr) throws SessionException {
        Assert.notNull(sids, "请输入系统订单号");
        Assert.notNull(orderFront.getItemSysId(), "请选择新商品");
        Assert.notNull(orderFront.getSkuSysId(), "请选择新商品");
        Staff staff = getStaff();
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_REPLACE_REMOVE_ITEM), "上一次批量替换删除商品还未执行完毕，请稍等！");
        eventCenter.fireEvent(this, new EventInfo("trade.replaceremove.item").setArgs(new Object[]{staff, sids, orderFront, ifChangeTradeDmsAttr}), null);
        return successResponse();
    }

    @RequestMapping(value = "/aiParseAddress", method = RequestMethod.GET)
    @ResponseBody
    public Object aiParseAddress(String rawAddress) throws Exception {
        return AddressParseUtil.getParseAddress(getStaff().getId(), rawAddress);
    }

    //去除字符串首尾空白符
    private String removeAllBlank(String origin) {
        if (StringUtils.isBlank(origin)) {
            return "";
        } else {
            return origin.replaceAll("^[\\p{Zs}*|\\s*]*", "").replaceAll("[\\p{Zs}*|\\s*]*$", "");
        }
    }

    @RequestMapping(value = "/tmcs/callback/outOfStock", method = RequestMethod.POST)
    @ResponseBody
    public Object tmcsOutOfStockCallBack(String sids, String api_name) throws Exception {
        Assert.isTrue(sids != null && !(sids = sids.trim()).isEmpty(), "请选择订单号");
        Staff staff = getStaff();
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_TRADE_TMCS_OUTOFSTOCK), "上一次批量猫超缺货回告还未执行完毕，请稍等！");
        Long[] sidArr = Strings.getAsLongArray(sids, ",", true);
        eventCenter.fireEvent(this, new EventInfo("trade.tmcs.callback").setArgs(new Object[]{staff, sidArr, 0}), null);
        return successResponse();
    }


    /**
     * 处理订单取消请求
     */
    @RequestMapping(value = "/handleTradeCancelRequest", method = RequestMethod.POST)
    @LogTag(key = "#tid", content = "'处理订单取消请求，id：' + #tid + '，用户平台编号：' + #userId", action = "handle_trade_cancel_request")
    @ResponseBody
    public Object handleTradeCancelRequest(Long userId, String tids, boolean agree, String refuseReason, String api_name) throws SessionException {
        Assert.notNull(userId, "请传入userId编号");
        Staff staff = getStaff();
        User user = staff.getUserIdMap().get(userId);
        Assert.notNull(user, "您没有所选店铺的权限");
        Assert.isTrue(user.getActive() != null && user.getActive().intValue() != CommonConstants.JUDGE_NO, String.format("所选店铺已停用[userId:%s]", user.getId()));
        Assert.isTrue(user.getUserConf().isInitItem(), String.format("该店铺商品未完成初始化,无法执行指定操作[userId:%s,nick:%s]", user.getId(), user.getNick()));
        return sysTradeService.handleTradeCancelRequest(user, tids, agree, refuseReason);
    }

    @RequestMapping(value = "/spite/report", method = RequestMethod.POST)
    @ResponseBody
    public Object spiteReport(MultipartFile file, String sids, Integer reason, Integer status, String relationName, String relationPhone, String memo) throws Exception {
        Staff staff = getStaff();
        Assert.isTrue(StringUtils.isNotBlank(sids) && null != reason && null != status && StringUtils.isNotBlank(relationName) && StringUtils.isNotBlank(relationPhone), "订单号、上报类型、订单商品状态、售后联系人手机号、售后联系人姓名需要同时传入，请检查");
        SpiteReportDTO reportDTO = new SpiteReportDTO();
        reportDTO.setReason(reason);
        reportDTO.setStatus(status);
        reportDTO.setRelationName(relationName);
        reportDTO.setRelationPhone(relationPhone);
        reportDTO.setMemo(memo);
        if (null != file) {
            StringBuilder sb = new StringBuilder();
            String fileName = file.getName();
            sb.append(fileName);
            String fileExtendName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf('.'));
            sb.append(fileExtendName);
            reportDTO.setFileName(sb.toString());
            reportDTO.setFileByte(file.getBytes());
        }
        List<Long> sidList = Strings.getAsLongList(sids, ",", false);
        return sysTradeService.spiteReport(staff, sidList, reportDTO);
    }

    @RequestMapping(value = "/spi/address/modify")
    public void spiAddressModify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        JSONObject jsonObject = new JSONObject();
        try {
            SecurityEventTrackingBatchOrderRequest fxgSecurityRequest = FxgSecurityUtil.buildFxgSecurityRequest(request);
            jsonObject = fxgSpiService.addressModify(request, fxgSecurityRequest);
        } catch (Exception e) {
            logger.error("抖店自助修改地址失败，失败信息:" + e.getMessage(), e);
            jsonObject.put("code", FxgOperationCodeEnum.SYSTEM_ERROR.getCode());
            jsonObject.put("message", FxgOperationCodeEnum.SYSTEM_ERROR.getDesc());
        }
        String result = JSON.toJSONString(jsonObject);
        logger.info("抖店spi改地址，返回信息：" + result);
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(200);
        PrintWriter printWriter = response.getWriter();
        printWriter.write(result);
        printWriter.close();
        response.flushBuffer();
    }

    @RequestMapping(value = "/outBound/batch", method = RequestMethod.POST)
    @LogTag(key = "#sids", content = "'订单出库:' + #sids ", action = "outBound")
    @ResponseBody
    public Object outBoundBatch(String sids, TradeControllerParams queryParams, Sort sort, String api_name) throws SessionException {
        Staff staff = getStaff();
        if (StringUtils.isEmpty(sids) && queryParams == null) {
            throw new IllegalArgumentException("无需出库");
        }
        TradeQueryParams params = null;
        if (queryParams != null) {
            params = TradeQueryParams.copyParams(queryParams);
            params.setSort(sort);
        }
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_TRADE_OUT_BOUND), "已经在执行出库，请稍后再试！");
        eventCenter.fireEvent(this, new EventInfo("trade.manual.out.bound").setArgs(new Object[]{staff, sids, params}), null);
        return successResponse();
    }

    private void saveOrderModifyLog(Staff staff, ModifyData modifyData) {
        orderModifyLogBusiness.addLog(staff, OrderModifyLogUtils.build(modifyData.inserts, OrderModifyLogTypeEnum.MODIFY_ITEM));
        orderModifyLogBusiness.addLog(staff, OrderModifyLogUtils.build(modifyData.updates, OrderModifyLogTypeEnum.MODIFY_ITEM));
        orderModifyLogBusiness.addLog(staff, OrderModifyLogUtils.build(modifyData.numChanges, OrderModifyLogTypeEnum.MODIFY_ITEM));
        orderModifyLogBusiness.addLog(staff, OrderModifyLogUtils.build(modifyData.itemChanges, OrderModifyLogTypeEnum.MODIFY_ITEM));
    }

    @RequestMapping(value = "/manual/packages/notice", method = RequestMethod.POST)
    @ResponseBody
    public Object packagesNotice(String sids, String api_name) throws Exception {
        Assert.isTrue(sids != null && !(sids = sids.trim()).isEmpty(), "请选择订单号");
        Long[] sidArr = Strings.getAsLongArray(sids, ",", true);
        return sysTradeService.packagesNotice(getStaff(), sidArr);
    }

    /**
     * 查询订单报关信息
     */
    @RequestMapping(value = "/query/declare", method = RequestMethod.GET)
    @ResponseBody
    public Object queryDeclare(Long sid, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入系统单号");
        return sysTradeService.queryTradeDeclare(getStaff(), sid);
    }

    /**
     * 保存订单报关信息
     */
    @RequestMapping(value = "/save/declare", method = RequestMethod.POST)
    @ResponseBody
    public Object saveDeclare(@RequestBody List<TradeDeclare> tradeDeclareList) throws SessionException {
        Assert.isTrue(CollectionUtils.isNotEmpty(tradeDeclareList), "请输入报关信息");
        sysTradeService.saveTradeDeclare(getStaff(), tradeDeclareList);
        return successResponse();
    }

    /**
     * openUid转换买家旺旺昵称
     */
    @RequestMapping(value = "/openuid/transform", method = RequestMethod.GET)
    @ResponseBody
    public Object consignUpload(Long userId, String sid, String openUid, String api_name) throws SessionException {
        Assert.notNull(userId, "需要转换旺旺昵称订单的店铺id不能为空");
        Staff staff = getStaff();
        User user = staff.getUserIdMap().get(userId);
        Assert.notNull(user, "您没有当前订单对应店铺的权限");
        Assert.isTrue(user.getActive() != null && user.getActive() != CommonConstants.JUDGE_NO, String.format("所选店铺已停用[userId:%s]", user.getId()));
        String loginId = tradeOpenUidTransformBusiness.openUid2LoginId(user, openUid);
        Map<String, String> map = new HashMap<>();
        map.put("buyerNick", loginId);
        return map;
    }

    /**
     * 处理常态合作编码未匹配异常
     *
     * @param companyId
     * @param sids
     * @return
     */
    @RequestMapping(value = "/handle/cooperation/except", method = RequestMethod.POST)
    @ResponseBody
    public Object handleCooperationNoExcept(Long companyId, String sids) throws SessionException {
        Long[] sidArr = getAsLongArray(sids, ",", false);
        Assert.isTrue(sidArr.length > 0, "请输入正确的订单号");
        return tradeService.handleCooperationNoExcept(getStaff(), sidArr);
    }

    /**
     * 获取放心购订单open_id （用于新建工单）
     *
     * @param tid
     * @param api_name
     * @return
     */
    @RequestMapping("/fxg/openId/get")
    @ResponseBody
    public Object getFxgOpenId(String tid, String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotBlank(tid), "平台订单号不能为空");
        return tradeService.getFxgOpenId(getStaff(), tid);
    }

    /**
     * 普通商品免拣免验功能上线时刷新isPick值
     */
    @RequestMapping(value = "/refresh/order/isPick", method = RequestMethod.GET)
    @ResponseBody
    public Object refreshIsPick(Long companyId) throws SessionException {
        Assert.notNull(companyId, "公司Id不可以为空");
        sysTradeService.refreshIsPick(getStaff(), companyId, DateUtil.addDate(new Date(), 1));
        return successResponse();
    }

    /**
     * 无效商品功能，支持批量换成系统商品
     * <a href="https://tb.raycloud.com/task/646dad27c9729b001dae5af0"> 无效商品功能，支持批量换成系统商品 </a>
     */
    @RequestMapping(value = "/replace/nullity/item", method = RequestMethod.POST)
    @ResponseBody
    public Object replaceNullityItem(@RequestBody ReplaceNullityItemParams replaceNullityItemParams) throws SessionException {

        Staff staff = getStaff();
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_BATCH_CHANGE_ITEMS), "正在执行批量修改商品，请稍后再试！");
        eventCenter.fireEvent(this, new EventInfo(TradeReplaceNullityItemBusiness.EVENT_NAME).setArgs(new Object[]{staff.getId(), replaceNullityItemParams}), null);
        return successResponse();
    }


    private void validateTradeUploadExcept(Staff staff, List<Trade> trades) {
        for (Trade needWeightTrade : trades) {
            if (TradeExceptUtils.isContainExcept(staff, needWeightTrade, ExceptEnum.UPLOAD_EXCEPT)) {
                Assert.isTrue(false, String.format("系统单号[%s]存在上传发货异常，不允许称重！", needWeightTrade.getSid()));
            }
        }

    }

    @ResponseBody
    @RequestMapping(value = "/sync/batch", method = RequestMethod.POST)
    public Object tradeBatchDownload(Long userId, String tids, String api_name) throws SessionException {
        Assert.notNull(userId, "店铺编号不能为空");
        Assert.isTrue(StringUtils.isNotBlank(tids), "平台订单号不能为空");

        Staff staff = getStaff();
        User user = staff.getUserIdMap().get(userId);
        Assert.notNull(user, "您没有所选店铺的权限");

        String validateMsg = TradeSyncUtils.validate(user);
        Assert.isTrue(validateMsg == null, validateMsg);

        tids = tids.trim();
        if (tids.contains("\r\n")) {
            tids = tids.replaceAll("\r\n", ",");
        } else if (tids.contains(" ")) {
            tids = tids.replaceAll("[ ]+", ",");
        }

        Set<String> tidSet = ArrayUtils.toStringSet(tids);
        if (CollectionUtils.isNotEmpty(tidSet) && tidSet.size() <= 100) {
            ProgressData progressData = progressService.queryProgress(user.getStaff(), ProgressEnum.PROGRESS_TRADE_BATCH_DOWNLOAD);
            if (progressData != null && Objects.equals(progressData.getProgress(), TradeBatchDownloadService.PROGRESS_DOWNLOADING)) {
                throw new IllegalArgumentException("当前账号正在进行批量指定下载，请稍后再试");
            }
            eventCenter.fireEvent(this, new EventInfo("trade.batch.download").setArgs(new Object[]{user, tidSet}), null);
        } else {
            throw new IllegalArgumentException("平台订单号不能为空且不超过100个");
        }

        return successResponse();
    }

    @ResponseBody
    @RequestMapping(value = "multiUser/sync/batch", method = RequestMethod.POST)
    public Object multiUserTradeBatchDownload(@RequestBody MultiUserTradeDownParams params) throws SessionException {
        List<MultiUserTradeDownVo> userTrades = params.getUserTrades();
        if (CollectionUtils.isEmpty(userTrades)){
            throw new IllegalArgumentException("参数不能为空");
        }
        Staff staff = getStaff();
        for (MultiUserTradeDownVo userTrade : userTrades) {
            User user = staff.getUserIdMap().get(userTrade.getUserId());
            Assert.notNull(user, String.format("您没有所选%s的权限", userTrade.getUserId()));
            if (CollectionUtils.isEmpty(userTrade.getTids())) {
                throw new IllegalArgumentException( String.format("您勾选的店铺【%s】没有选择订单", userTrade.getUserId()));
            }
        }
        User user = staff.getUserByUserId(userTrades.get(0).getUserId());
        ProgressData progressData = progressService.queryProgress(getStaff(), ProgressEnum.PROGRESS_TRADE_BATCH_DOWNLOAD);
        if (progressData != null && Objects.equals(progressData.getProgress(), TradeBatchDownloadService.PROGRESS_DOWNLOADING)) {
            throw new IllegalArgumentException("当前账号正在进行批量指定下载，请稍后再试");
        }
        eventCenter.fireEvent(this, new EventInfo("trade.batch.download").setArgs(new Object[]{user, new HashSet<>(),userTrades}), null);
        return successResponse();
    }

    @ResponseBody
    @LogTag(key = "#params.sids", content = "'批量更新订单状态为生产中，系统单号：' + #params.sids ", action = "update.trade.platform.status")
    @RequestMapping(value = "/update/trade/platform/status", method = RequestMethod.POST)
    public Object updateTradePlatformStatus(TradePlatformStatusUpdateParams params, TradeControllerParams tradeControllerParams, String api_name) throws SessionException {
        Staff staff = getStaff();
        String sids = params.getSids();
        Assert.isTrue(sids != null && !(sids = sids.trim()).isEmpty(), "请选择订单号");
        Long[] sidArr = Strings.getAsLongArray(sids, ",", true);
        List<Trade> tbTrades = tradeSearchService.queryBySids(staff, false, sidArr);
        List<Trade> hasOtherPlatform =
                tbTrades.stream().filter(trade -> !CommonConstants.PLAT_FORM_TYPE_BIYAO.equals(trade.getSource())).collect(Collectors.toList());
        if (hasOtherPlatform.size() == tbTrades.size()) {
            throw new IllegalArgumentException("操作仅限必要商城订单使用");
        }
        tbTrades = tbTrades.stream().filter(trade -> "biyao_2".equals(trade.getStatus())).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Trade::getTid))), ArrayList::new));
        if (CollectionUtils.isEmpty(tbTrades)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append(String.format("本次操作选中必要商城订单非已支付状态的订单，不能改成生产中 sid: %s", sids)));
            }
            return successResponse();
        }
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_TRADE_BATCH_UPDATE_STATUS), "上一次批量更新平台订单状态还未执行完毕，请稍等！");
        eventCenter.fireEvent(this, new EventInfo("trade.status.update").setArgs(new Object[]{staff, tbTrades}), null);
        return successResponse();
    }

    /**
     * 继续发货校验
     */
    @RequestMapping(value = "/refuse/refund/check", method = RequestMethod.POST)
    @ResponseBody
    public Object refuseRefundCheck(String sids, String api_name) throws SessionException {
        Assert.notNull(sids, "请输入系统订单号");
        Staff staff = getStaff();
        List<Trade> tradeList = tradeSearchService.queryBySidsContainMergeTrade(staff, true, ArrayUtils.toLongArray(sids));
        List<Map<String, String>> checkMsgMapList = tradeRefundItemNumExceptBusines.checkRefundItemNumExcept(staff, tradeList);
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("resultFlag", CollectionUtils.isEmpty(checkMsgMapList));
        result.put("resultMsg", checkMsgMapList);
        return result;
    }


    /**
     * 修改归档订单系统备注
     */
    @RequestMapping(value = "/archive/sysmemo/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateArchiveTradeSysMemo(String sids, String updateSysMemo, String fastSysMemoId) throws SessionException {
        Assert.notNull(sids, "请输入系统订单号");
        updateSysMemo = StringUtils.trimToEmpty(updateSysMemo);
        Assert.isTrue(updateSysMemo.length() <= 1024, "当前备注长度为" + updateSysMemo.length() + "超出预设长度1024，修改备注失败");
        sysTradeService.updateArchiveTradeSysMemo(getStaff(), Long.parseLong(sids), updateSysMemo, ArrayUtils.toLongArray(fastSysMemoId));
        return successResponse();
    }

    @RequestMapping(value = "/query/order/modify/log", method = RequestMethod.POST)
    @ResponseBody
    public Object getOrderModifyLog(String sid, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入系统订单号");
        Staff staff = getStaff();
        List<OrderModifyLog> orderModifyLogs = orderModifyLogBusiness.queryBySid(staff, Long.parseLong(sid));

        if (CollectionUtils.isEmpty(orderModifyLogs)) {
            return orderModifyLogs;
        }

        Integer changeItemType = OrderModifyLogTypeEnum.CHANGE_ITEM.getType();
        Integer suitReplaceType = OrderModifyLogTypeEnum.SUIT_REPLACE.getType();
        Integer modifyItemType = OrderModifyLogTypeEnum.MODIFY_ITEM.getType();

        Map<Long, OrderModifyLog> changeItemModifyLogMap = orderModifyLogs.stream()
                .filter(log -> Objects.equals(suitReplaceType, log.getModifyType()) || (Objects.equals(modifyItemType, log.getModifyType()) && (log.getOriginOrderId() != null && log.getOriginOrderId() > 0)) || (Objects.equals(changeItemType, log.getModifyType()) && (log.getOriginOrderId() != null && log.getOriginOrderId() > 0)))
                .collect(Collectors.toMap(OrderModifyLog::getOrderId, log -> log, (v1, v2) -> v1));

        orderModifyLogs.stream().sorted(Comparator.comparing(OrderModifyLog::getInsertTime).reversed()).forEach(log -> {

            String content = fillOrderModifyLog(log, changeItemModifyLogMap);
            if (StringUtils.isNotBlank(content)) {
                log.setModifyType(OrderModifyLogTypeEnum.SUIT_REPLACE.getType());
                log.setContent(content);
            }

            if (!Objects.equals(suitReplaceType, log.getModifyType())) {
                return;
            }

            try {

                if (StringUtils.isBlank(log.getContent()) || !log.getContent().contains("{")) {
                    return;
                }
                log.setContentInfo(JSONObject.parseObject(log.getContent(), OrderModifyLogContent.class));
            } catch (Exception e) {
                // 单纯捕获一下异常
            }

        });

        return orderModifyLogs;
    }

    @RequestMapping(value = "/update/timeoutActionTime", method = RequestMethod.POST)
    @ResponseBody
    public Object updateTimeoutActionTime(@RequestBody TradeUpdateTimeoutActionTimeRequest req) throws SessionException {
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, req);

        tradeService.updateTimeoutAction(staff, req);
        return successResponse();
    }

    private String fillOrderModifyLog(OrderModifyLog log, Map<Long, OrderModifyLog> changeItemModifyLogMap) {

        if (MapUtils.isEmpty(changeItemModifyLogMap)) {
            return null;
        }

        OrderModifyLog orderModifyLog = changeItemModifyLogMap.get(log.getOriginOrderId());
        if (Objects.isNull(orderModifyLog)) {
            return null;
        }

        if (!Objects.equals(OrderModifyLogTypeEnum.CHANGE_ITEM.getType(), orderModifyLog.getModifyType()) && !Objects.equals(OrderModifyLogTypeEnum.MODIFY_ITEM.getType(), orderModifyLog.getModifyType()) && !Objects.equals(OrderModifyLogTypeEnum.SUIT_REPLACE.getType(), orderModifyLog.getModifyType())) {
            return fillOrderModifyLog(orderModifyLog, changeItemModifyLogMap);
        }

        //尝试继续往上层取
        if (StringUtils.isBlank(orderModifyLog.getContent())) {
            return fillOrderModifyLog(orderModifyLog, changeItemModifyLogMap);
        }

        if (StringUtils.isNotBlank(orderModifyLog.getContent()) && orderModifyLog.getContent().contains("{")) {
            return orderModifyLog.getContent();
        }

        return fillOrderModifyLog(orderModifyLog, changeItemModifyLogMap);

    }

    /**
     * 取消【换货前商品平台信息变更异常】
     */
    @RequestMapping(value = "/exception/cancel/exchangeItemPlatModifyExcept", method = RequestMethod.POST)
    public @ResponseBody Object cancelExchangeItemPlatModifyExcept(TradeControllerParams queryParams) throws SessionException {
        Staff staff = getStaff();
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_CANCEL_EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT), "当前正在进行取消换货前商品平台信息变更异常操作,请稍后再试");
        eventCenter.fireEvent(this, new EventInfo("trade.batch.cancel.exchangeItemPlatModifyExcept").setArgs(new Object[]{staff.getId(), queryParams}), null);
        OpLogHelper.recodeOpLog(opLogService, request, staff, "consignExchangeItemPlatModifyExcept", "trade.batch.cancel.exchangeItemPlatModifyExcept", "取消换货前商品平台信息变更异常", Optional.ofNullable(queryParams.toPlainString()).orElse(""));
        return successResponse();
    }

    /**
     * 订单商品重量/体积更新
     * doc:https://gykj.yuque.com/entavv/xb9xi5/rgatlw
     *
     * @return
     */
    @RequestMapping(value = "/weightAndVolume/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateTradeWeightAndVolume(TradeControllerParams params, String api_name) throws SessionException {
        Staff staff = getStaff();
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_TRADE_BATCH_UPDATE_WEIGHT_VOLUME), "上一次订单商品重量/体积更新还未执行完毕，请稍后再试！");
        eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_UPDATE_WEIGHT_AND_VOLUME).setArgs(new Object[]{staff.getId(), params}), null);
        return ProgressUtils.getResponse(staff, ProgressEnum.PROGRESS_TRADE_BATCH_UPDATE_WEIGHT_VOLUME.getKey(), 0);
    }

    @RequestMapping(value = "/tb/refund/open", method = RequestMethod.POST)
    @ResponseBody
    public Object tbRefundOpen(Long sid) throws SessionException {
        Staff staff = getStaff();
        Trade trade = tradeSearchService.queryBySid(staff, false, sid);
        Assert.notNull(trade, "订单号错误，未找到该订单");
        User user = staff.getUserIdMap().get(trade.getUserId());
        if(CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())){
            IPlatformRefundToolAccess access = platformManagement.getAccess(trade.getSource(), IPlatformRefundToolAccess.class);
            PlatformResponse response = access.refundOpen(user, trade);
            if(response.isSuccess()){
                //填加日志
                TradeTrace tradeTrace = TradeTraceUtils.createTradeTraceWithTrade(user.getCompanyId(), trade, OpEnum.REFUND_OPEN.getName(), staff.getName(), new Date(), OpEnum.REFUND_OPEN.getName());
                tradeTraceService.addTradeTrace(user.getStaff(), tradeTrace);
                return successResponse();
            } else {
                throw new IllegalArgumentException(String.format("操作失败,平台返回错误信息：%s %s",response.getMsg(), response.getSubMsg()));
            }
        } else {
            throw new IllegalStateException("该订单不支持该操作");
        }
    }

    @RequestMapping(value = "/jd/govSubsidy/urlUpload", method = RequestMethod.POST)
    @ResponseBody
    public Object jdGovSubsidyUrlUpload(@RequestParam(required = false) List<String> urls, @RequestParam Long sid) throws SessionException {
        Staff staff = getStaff();
        Trade trade = tradeSearchService.queryBySid(staff, true, sid);
        Assert.notNull(trade, "订单号错误，未找到该订单");
        Assert.isTrue(CollectionUtils.isNotEmpty(urls), "图片列表不能为空");
        User user = staff.getUserIdMap().get(trade.getUserId());
        if(CommonConstants.PLAT_FORM_TYPE_JD.equals(trade.getSource()) || CommonConstants.PLAT_FORM_TYPE_JD_VC.equals(trade.getSource())){
            IPlatformGovSubsidyAccess access = platformManagement.getAccess(trade.getSource(), IPlatformGovSubsidyAccess.class);
            PlatformResponse response = access.uploadSubsidyUrl(user, trade, urls);
            if(response.isSuccess()){
                TradeExt dbTradeExt = trade.getTradeExt();
                TradeExt tradeExt = new TradeExt();
                tradeExt.setTid(dbTradeExt.getTid());
                tradeExt.setCompanyId(dbTradeExt.getCompanyId());
                tradeExt.setUserId(dbTradeExt.getUserId());
                tradeExt.setSid(dbTradeExt.getSid());
                TradeExtUtils.setExtraFieldValue(tradeExt, "snImgRuleUrls", JSONObject.toJSONString(urls));
                tradeExtBusiness.updateBatch(staff, Lists.newArrayList(tradeExt));
                //填加日志
                TradeTrace tradeTrace = TradeTraceUtils.createTradeTraceWithTrade(user.getCompanyId(), trade, OpEnum.GOV_SUBSIDY_URL_UPLOAD.getName(), staff.getName(), new Date(), OpEnum.GOV_SUBSIDY_URL_UPLOAD.getName());
                tradeTraceService.addTradeTrace(user.getStaff(), tradeTrace);
                return successResponse();
            } else {
                throw new IllegalArgumentException(String.format("操作失败,平台返回错误信息：%s %s",response.getMsg(), response.getSubMsg()));
            }
        } else {
            throw new IllegalStateException("该订单不支持该操作");
        }
    }

    /**
     * 更新定金/代收金额
     */
    @RequestMapping(value = "/selfBuildAmount/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateSelfBuildAmount(@RequestBody TbTrade trade) throws SessionException {
        Assert.notNull(trade.getSid(), "系统单号不能为空");
        Assert.notNull(StringUtils.isNotEmpty(trade.getSelfBuiltDepositAmount())
                && StringUtils.isNotEmpty(trade.getSelfBuiltPaymentReceivable()), "请输入定金/代收金额");
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, trade);
        eventCenter.fireEvent(this, new EventInfo("trade.update.selfbuilt.amount").setArgs(new Object[]{staff, trade}), null);
        return successResponse();
    }
}

package com.raycloud.dmj.web.controllers.trades;

import com.alibaba.fastjson.*;
import com.google.api.client.util.*;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.*;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.bo.LockItemStock;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.operate.*;
import com.raycloud.dmj.business.payment.support.PaymentCalculateSupports;
import com.raycloud.dmj.business.trade.*;
import com.raycloud.dmj.business.tradepay.TradePayBusiness;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.utils.ConfigUtils;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.item.*;
import com.raycloud.dmj.domain.item.box.*;
import com.raycloud.dmj.domain.annotation.AccessCondition;
import com.raycloud.dmj.domain.annotation.AccessShield;
import com.raycloud.dmj.domain.annotation.AccessShields;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.pt.JdvcCarrierInfo;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.pt.enums.SysKuaiYunEnum;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompany;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.config.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.search.SenceCodeEnum;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.search.ReturnFieldGroupEnum;
import com.raycloud.dmj.domain.trades.search.exception.SearchTimeoutException;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.tradepay.TradePayDetail;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.utils.PaymentUtils;
import com.raycloud.dmj.domain.trades.vo.*;
import com.raycloud.dmj.domain.user.*;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.enums.WaveChatConfigsEnum;
import com.raycloud.dmj.domain.wave.model.WaveTradeQueryParams;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.enums.ContainerTypeEnum;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyService;
import com.raycloud.dmj.item.search.api.*;
import com.raycloud.dmj.item.search.dto.*;
import com.raycloud.dmj.item.search.request.*;
import com.raycloud.dmj.item.search.response.*;
import com.raycloud.dmj.jd.trade.JdTradeAccess;
import com.raycloud.dmj.order.IOrderSortService;
import com.raycloud.dmj.repair.domain.Response.RepairOrderResponse;
import com.raycloud.dmj.repair.service.IAsRepairService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.filter.support.DefaultTemplateFilter;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.items.shipper.IShipperDubbo;
import com.raycloud.dmj.services.platform.basis.*;
import com.raycloud.dmj.services.platform.trades.PlatformExpressInfoBusiness;
import com.raycloud.dmj.services.platform.trades.jdvc.IJdvcGxdPreDecisionService;
import com.raycloud.dmj.services.pt.IMultiPacksPrintTradeLogService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.pt.dubbo.IExpressTemplateDubboService;
import com.raycloud.dmj.services.response.ItemCatIdAndSellerCidsResponse;
import com.raycloud.dmj.services.stock.VirtualStockService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.*;
import com.raycloud.dmj.services.trades.fill.*;
import com.raycloud.dmj.services.trades.filter.ITradeFilterService;
import com.raycloud.dmj.services.trades.support.*;
import com.raycloud.dmj.services.trades.vip.IVipDeliveryDataService;
import com.raycloud.dmj.services.trades.wave.IItemUniqueCodeService;
import com.raycloud.dmj.services.trades.wave.IWaveServiceDubbo;
import com.raycloud.dmj.services.user.*;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.tb.trade.TbTradeAccess;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.*;
import com.raycloud.dmj.web.*;
import com.raycloud.dmj.web.controllers.TradeBaseController;
import com.raycloud.dmj.web.controllers.help.*;
import com.raycloud.dmj.web.interceptors.RequestBodyParamsUtils;
import com.raycloud.dmj.web.model.trades.*;
import com.raycloud.dmj.web.model.trades.as.AsRepairWeighInfo;
import com.raycloud.dmj.web.search.scene.SceneConverterContext;
import com.raycloud.dmj.web.search.scene.SceneModelConverter;
import com.raycloud.dmj.web.util.TradeSortConfigUtils;
import com.raycloud.dmj.web.utils.*;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.erp.db.router.jdbc.JdbcTemplateAdapter;
import com.raycloud.erp.trade.search.db.TradeStatCountBusiness;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.*;
import org.apache.log4j.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.util.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.*;
import java.util.function.Function;
import java.util.regex.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.raycloud.dmj.domain.enums.OpEnum.*;
import static com.raycloud.dmj.domain.trade.label.TradeSystemLabelEnum.TAG_1000000044;
import static com.raycloud.dmj.domain.trades.InvaildItem.*;
import static com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext.*;
import static java.util.stream.Collectors.*;

/**
 * creatd 18/5/8
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/trade")
@Scope("prototype")
@LogTag(value = "trade", enableArgs = "true", enableResponse = "true")
public class TradeQueryController extends TradeBaseController {

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private ITradeHotItemSearchService tradeHotItemSearchService;

    @Resource
    ITradeDataService tradeDataService;

    @Resource(name = "tradeSqlCountService")
    private ITradeCountService tradeCountService;

    @Resource
    private ITradeFilterService tradeFilterService;

    @Resource
    private ITradeFillService tradeFillService;


    @Resource
    private TbTradeAccess tbTradeAccess;

    @Resource
    private ITradeTraceService tradeTraceService;
    @Resource
    ITradeQueryService tradeQueryService;

    @Resource
    TradeValidateBusiness tradeValidateBusiness;
    @Resource
    JdbcTemplateAdapter jdbcTemplateAdapter;

    @Resource
    TradeStatCountBusiness tradeStatCountBusiness;

    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;

    @Resource
    IItemServiceWrapper itemServiceWrapper;

    @Resource
    private OrderStockNumBusiness orderStockNumBusiness;

    @Resource
    ILogisticsTrackingRecordService logisticsTrackingRecordService;
    @Resource
    private StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    private TradePayBusiness tradePayBusiness;

    @Resource
    private IStaffService staffService;

    @Autowired
    ITradeWaveService tradeWaveService;

    @Resource
    IWaveServiceDubbo waveServiceDubbo;

    @Resource
    private TradePddTradeBusiness tradePddTradeBusiness;

    @Resource
    DmjItemCommonSearchApi dmjItemCommonSearchApi;

    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    IExpressTemplateDubboService iExpressTemplateDubboService;

    @Resource
    FxBusiness fxBusiness;

    @Resource
    DefaultTemplateFilter defaultTemplateFilter;

    @Resource
    IUserService userService;

    @Resource
    PlatformManagement platformManagement;

    @Resource
    private FxgTradeDecryptBusiness fxgTradeDecryptBusiness;

    @Resource
    private TbItemSearchApi tbItemSearchApi;

    @Resource
    private CommonTradeDecryptBusiness commonTradeDecryptBusiness;

    @Resource
    private RuleOperateLogBusiness ruleOperateLogBusiness;

    @Resource
    private IItemServiceDubbo itemServiceDubbo;

    @Resource
    private PackBusiness packBusiness;


    @Resource
    private IItemServiceDubbo itemsService;

    @Resource
    private IShipperDubbo shipperDubbo;

    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;

    @Resource
    private ITradeItemPackService tradeItemPackService;

    @Resource
    IWmsService wmsService;

    @Resource
    VirtualStockService virtualStockService;

    @Resource
    private FxgDfMallService fxgDfMallService;

    @Resource
    private ITradeFill tradeOrderSyncItemTagFill;

    @Resource
    private ITradeConfigNewService tradeConfigNewService;

    @Resource
    private TradeStaffConfigService tradeStaffConfigService;
    @Resource
    private IColumnConfService columnConfService;

    @Resource
    private CancelInsufficientBusiness cancelInsufficientBusiness;


    @Resource
    private FeatureService featureService;

    @Resource
    private IJdvcGxdPreDecisionService jdvcGxdPreDecisionService;

    @Resource
    private IUserLogisticsCompanyService userLogisticsCompanyService;

    @Resource
    private Map<String, IOrderSortService> orderSortServiceMap;

    @Resource
    private PaymentCalculateSupports paymentCalculateSupports;

    @Resource
    private QueryFillBusiness queryFillBusiness;

    @Resource
    private OrderGoodsSectionFillBusiness orderGoodsSectionFillBusiness;
    @Resource
    SecretBusiness secretBusiness;

    @Resource
    private IVipDeliveryDataService vipDeliveryDataService;

    @Autowired(required = false)
    private IAsRepairService asRepairService;
    @Resource
    private TradeExtDao tradeExtDao;

    @Resource
    private SceneModelConverter sceneModelConverter;



    private final Logger logger = Logger.getLogger(TradeQueryController.class);

    @RequestMapping(value = "/search/sids", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object searchBySids(String sids, Boolean highlight, String text, boolean withStock, Integer blurTrade,
                               @RequestParam(value = "highlightTid", required = false) String highlightTid, String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");
        if (highlight == null) {
            highlight = false;
        }

        Long[] __sids = Strings.getAsLongArray(sids,",",true);
        Staff staff = getStaff();
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, __sids);

        Map<Long, TradeOriginReceiverInfos> tradeOriginReceiverInfos = getTradeOriginReceiverInfos(trades);
        TradeSysDigestUtils.batchSensitive(staff, trades);
        tradePddTradeBusiness.pddMaskDataReplace(staff, trades);
        fxgTradeDecryptBusiness.batchSensitive(staff, trades);
        commonTradeDecryptBusiness.batchSensitive(staff, trades);
        fillItemInfo(staff, null, trades, null);
        tradeQueryService.fillDestAndSourceName(trades);

        Map<Long, Trade> map = TradeUtils.toMapBySid(trades);
        List<Trade> result = new ArrayList<>();
        for (Long sid : __sids) {
            Trade trade = map.get(sid);
            if (trade != null) {
                result.add(trade);
            }
        }

        if (blurTrade != null) {
            Map<String, Object> configMap = TradeConfigUtils.parseExtendConfig(tradeConfig);
            configMap.put("blurTrade", blurTrade);
            tradeConfig.setChatConfigs(JSONObject.toJSONString(configMap));
        }
        TradeModels tradeModels = result.size() == 0 ? TradeModels.toEmpty() : TradeModels.toTradeModels(staff, result, shopService, expressCompanyService, highlight, text, highlightTid, tradeConfig);
        handleShowItemTag(staff, tradeModels);
        queryFillBusiness.fillTradeInfos(staff, tradeModels.getList(),AbsLogBuilder.getNvlInstance(),trades, InfoLevelEnum.EXTEND);
        handleNewFxTrade(staff, tradeModels);
        fillGxSourceTid(staff, tradeModels, map);
        boolean tradeSearchNotFillStock = tradeLocalConfigurable.isTradeSearchNotFillStock(staff.getCompanyId());
        if (withStock && !tradeSearchNotFillStock) {
            stockInfoModel(staff, tradeModels);
        }
        jdlog(staff, tradeModels);
        vipLog(staff, tradeModels);
        if (!tradeSearchNotFillStock) {
            orderStockNumBusiness.fillStockNum(staff, tradeModels.getList());
            fillVirtualStock(staff, tradeModels);
        }
        fullTradePay(staff, tradeModels);
        fillTradeOriginReceiverInfos(tradeOriginReceiverInfos, tradeModels);
        orderGoodsSectionFillBusiness.fillOrderGoodsSectionCode(staff, tradeModels,trades,false);
        handleVipJit(staff, tradeModels);
        handleTradeTrackingOrder(staff, tradeModels);
        handleVipJit(staff, tradeModels);
        handleTradeTrackingOrder(staff, tradeModels);
        return tradeModels;
    }

    /**
     * 商品标签名称显示
     * 未开起配置不做显示
     */
    private void handleShowItemTag(Staff staff, TradeModels tradeModels) {
        List<TradeModel> models;
        if (tradeModels == null ||
                (models = tradeModels.getList()) == null ||
                models.isEmpty()
        ) {
            return;
        }
        boolean showItemTag = tradeConfigNewService.get(staff, TradeConfigEnum.SHOW_ITEM_TAG).isOpen();
        if (showItemTag) {
            return;
        }
        for (TradeModel tradeModel : models) {
            List<OrderModel> orders = tradeModel.getOrders();
            if (orders == null || orders.isEmpty()) {
                continue;
            }
            for (OrderModel order : orders) {
                order.setOrderItemTagNames(null);
            }
        }
    }

    private void handleTradeTrackingOrder(Staff staff, TradeModels tradeModels) {
        try {
            if (Objects.isNull(tradeModels) || CollectionUtils.isEmpty(tradeModels.getList())){
                return;
            }
            //暂时过滤 只处理拼多多平台的
            Map<Long, List<TradeModel>> collect = tradeModels.getList().stream()
                    .filter(t -> CommonConstants.PLAT_FORM_TYPE_PDD.equals(t.getSource()))
                    .collect(Collectors.groupingBy(TradeModel::getUserId));

            if (MapUtils.isNotEmpty(collect)) {
                String userIp = IpUtils.getClientIP(request);
                String cookieOfPati = CookieUtils.read(request, "_pati");
                String url = "https://erp.superboss.cc/trade/search/sids";

                for (Map.Entry<Long, List<TradeModel>> entry : collect.entrySet()) {
                    Long key = entry.getKey();

                    for (List<TradeModel> trades : Lists.partition(entry.getValue(), 100)) {
                        User user = staff.getUserIdMap().get(key);
                        if (user == null) {
                            String tidStr = entry.getValue().stream().map(TradeModel::getTid).collect(Collectors.joining(","));
                            Logs.warn(LogHelper.buildLog(staff, String.format("订单查询日志采集失败，找不到user，userId=%s,tids=%s", entry.getKey(), tidStr)));
                            continue;
                        }
                        List<Trade> tradeList = simpleTradeList(trades);
                        eventCenter.fireEvent(this, new EventInfo("tracking.order.query").setArgs(new Object[]{user, TradeUtils.toSids(tradeList), userIp, cookieOfPati,url}), tradeList);
                    }
                }
            }
        } catch (Exception e) {
            Logs.error("订单查询日志采集发送事件失败" + e.getMessage(), e);
        }
    }

    private List<Trade> simpleTradeList(List<TradeModel> originList) {
        List<Trade> simpleList = new ArrayList<>();
        if (CollectionUtils.isEmpty(originList)) {
            return simpleList;
        }
        for (TradeModel origin : originList) {
            simpleList.add(tradeCopy(origin, new TbTrade()));
        }
        return simpleList;
    }

    private Trade tradeCopy(TradeModel origin, Trade simple) {
        simple.setSource(origin.getSource());
        simple.setUserId(origin.getUserId());
        simple.setTaobaoId(origin.getTaobaoId());
        simple.setTid(origin.getTid());

        simple.setSid(Long.valueOf(origin.getSid()));
        return simple;
    }

    private Map<Long, TradeOriginReceiverInfos> getTradeOriginReceiverInfos(List<Trade> trades) {
        Map<Long, TradeOriginReceiverInfos> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            for (Trade trade : trades) {
                map.put(trade.getSid(), new TradeOriginReceiverInfos(trade));
            }
        }
        return map;
    }

    /**
     * 根据前端操作的业务场景 返回局部数据更新所需要的内容
     * @param sids
     * @param highlight
     * @param text
     * @param blurTrade
     * @param highlightTid
     * @param sceneCode
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/search/light", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object searchByScene(String sids, Boolean highlight, String text,boolean withStock,String fieldGroup, Integer blurTrade,
                                @RequestParam(value = "highlightTid", required = false) String highlightTid, String sceneCode, String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");
        Assert.notNull(sceneCode,"请输入sceneCode参数");
        SenceCodeEnum senceCodeEnum = SenceCodeEnum.valueOf(sceneCode.toUpperCase());
        Assert.notNull(senceCodeEnum,"非法的sceneCode参数:"+sceneCode);
        Staff staff = getStaff();
        if (!ConfigHolder.TRADE_SEARCH_CONFIG.isSceneLightOn(senceCodeEnum.name(),staff.getCompanyId())) {
            return searchBySids(sids,highlight,text,withStock,blurTrade,highlightTid,"");
        }

        List<Integer> fieldGroups = null;
        if (StringUtils.isNotBlank(fieldGroup)) {
            Set<Integer> list = Strings.getAsIntSet(fieldGroup,",",true);
            fieldGroups = new ArrayList<>(list.size());
            for (Integer integer : list) {
                ReturnFieldGroupEnum byKey = ReturnFieldGroupEnum.getByKey(integer);
                Assert.notNull(byKey,"非法的fieldGroup值:"+integer);
                fieldGroups.add(integer);
            }
        }

        Long[] __sids = ArrayUtils.toLongArray(sids);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        List<Trade> trades = tradeSearchService.queryLightTradeByScene(staff, senceCodeEnum, fieldGroups,__sids);
        if (CollectionUtils.isEmpty(trades)) {
            return TradeModels.toEmpty();
        }
        if (blurTrade != null) {
            Map<String, Object> configMap = TradeConfigUtils.parseExtendConfig(tradeConfig);
            configMap.put("blurTrade", blurTrade);
            tradeConfig.setChatConfigs(JSONObject.toJSONString(configMap));
        }
        if (highlight == null) {
            highlight = false;
        }
        SceneConverterContext context = new SceneConverterContext();
        context.setHighlight(highlight);
        context.setTradeConfig(tradeConfig);
        context.setText(text);
        context.setFieldGroup(fieldGroups);
        context.setHighlightTid(highlightTid);

        return sceneModelConverter.toTradeModels(staff, senceCodeEnum,context,trades);

    }

    private void fillTradeOriginReceiverInfos(Map<Long, TradeOriginReceiverInfos> map, TradeModels tradeModels) {
        if (map.isEmpty() || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        for (TradeModel model : tradeModels.getList()) {
            fillTradeOriginReceiverInfos(map,model);
        }
    }

    private void fillTradeOriginReceiverInfos(Map<Long,TradeOriginReceiverInfos> map,TradeModel model){
        TradeOriginReceiverInfos originReceiverInfos = map.get(Long.parseLong(model.getSid()));
        if (originReceiverInfos != null) {
            model.setOriginReceiverAddress(originReceiverInfos.getOriginReceiverAddress());
            model.setOriginReceiverMobile(originReceiverInfos.getOriginReceiverMobile());
            model.setOriginReceiverName(originReceiverInfos.getOriginReceiverName());
        }
    }


    /**
     * 填充虚拟仓库存信息
     */
    void fillVirtualStock(Staff staff, TradeModels tradeModels) {
        if (Objects.isNull(tradeModels)) {
            return;
        }
        List<TradeModel> tradeModelList = tradeModels.getList();
        if (CollectionUtils.isEmpty(tradeModelList)) {
            return;
        }
        for (TradeModel tradeModel : tradeModelList) {
            fillVirtualStock(staff, tradeModel);
        }
    }

    /**
     * 填充虚拟仓库存信息
     */
    void fillVirtualStock(Staff staff, TradeModel tradeModel) {
        List<OrderModel> orders = tradeModel.getOrders();
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        List<Long> orderIds = orders.stream().map(OrderModel::getId).map(id -> Long.parseLong(id.trim())).collect(toList());
        Map<Long, LockItemStock> virtualStockMap = virtualStockService.getVirtualStock(staff, orderIds);
        fillOrderVirtualStock(orders, virtualStockMap);
    }

    /**
     * 根据虚拟库存记录填充orders的虚拟库存信息
     */
    void fillOrderVirtualStock(List<OrderModel> orders, Map<Long, LockItemStock> virtualStockMap) {
        if (CollectionUtils.isEmpty(orders) || MapUtils.isEmpty(virtualStockMap)) {
            return;
        }
        for (OrderModel order : orders) {
            if (virtualStockMap.get(Long.parseLong(order.getId().trim())) == null) {
                continue;
            }
            fillOrderVirtualStock(order, virtualStockMap.get(Long.parseLong(order.getId().trim())));
        }
    }

    /**
     * 填充orderModel虚拟库存相关字段
     *
     * @param order     子订单模型
     * @param itemStock 子订单对应的虚拟库存记录
     */
    void fillOrderVirtualStock(OrderModel order, LockItemStock itemStock) {
        if (Objects.isNull(order) || Objects.isNull(itemStock)) {
            return;
        }
        order.setUsePrivateStock(true);
        order.setVirtualWarehouseName(itemStock.getVirtualWarehouseName());
        order.setTotalStock(itemStock.getTotalStock());
        order.setTradeNotUsedCount(itemStock.getTotalStock() - itemStock.getTradeLockStock());
    }

    @Resource
    IPlatformOrderLogUploadService vipLogAccess;

    private void vipLog(Staff staff, TradeModels tradeModels) {
        try {

            String deviceId = cache.get("vipjitDeviceId_" + staff.getId());
            if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList()) || StringUtils.isEmpty(deviceId)) {
                return;
            }
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "唯品会日志上传，订单查询").toString());
            }
            String[] deviceIdUserName = deviceId.split("&");
            deviceId = deviceIdUserName[0];
            if (StringUtils.isEmpty(deviceId) || "null".equals(deviceId)) {
                return;
            }
            staff.setAccountName(deviceIdUserName[1]);

            Map<Long, List<TradeModel>> collect = tradeModels.getList().stream().filter(t -> CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(t.getSource())).collect(Collectors.groupingBy(TradeModel::getUserId));
            if (MapUtils.isNotEmpty(collect)) {
                for (Map.Entry<Long, List<TradeModel>> entry : collect.entrySet()) {
                    Long key = entry.getKey();
                    for (List<String> tids : Lists.partition(entry.getValue().stream().map(TradeModel::getTid).collect(Collectors.toList()), 100)) {
                        String tidStr = StringUtils.join(tids, ",");
                        vipLogAccess.orderUploadLog(IpUtils.getClientIP(request),
                                deviceId,
                                tidStr,
                                5,
                                "https://erp.superboss.cc/trade/search/sids",
                                staff.getUserIdMap().get(key),
                                staff);

                        vipLogAccess.dbUploadLog(IpUtils.getClientIP(request),
                                deviceId,
                                "https://erp.superboss.cc/trade/search/sids",
                                "select * from trade where tid in (" + tidStr + ")",
                                staff.getUserIdMap().get(key),
                                staff);
                    }
                }
            }
        } catch (Exception e) {
            Logs.error("唯品会订单导出时上传日志失败" + e.getMessage(), e);
        }
    }


    @Resource
    JdTradeAccess jdTradeAccess;

    private void jdlog(Staff staff, TradeModels tradeModels) {

        try {
            String deviceId = cache.get("jdDeviceId_" + staff.getId());
            if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList()) || StringUtils.isEmpty(deviceId)) {
                return;
            }

            String[] deviceIdUserName = deviceId.split("&");
            deviceId = deviceIdUserName[0];
            if (StringUtils.isEmpty(deviceId) || "null".equals(deviceId)) {
                return;
            }
            staff.setAccountName(deviceIdUserName[1]);

            Map<Long, List<TradeModel>> collect = tradeModels.getList().stream().filter(t -> CommonConstants.PLAT_FORM_TYPE_JD.equals(t.getSource())).collect(Collectors.groupingBy(TradeModel::getUserId));
            if (MapUtils.isNotEmpty(collect)) {
                for (Map.Entry<Long, List<TradeModel>> entry : collect.entrySet()) {
                    Long key = entry.getKey();
                    for (List<String> tids : Lists.partition(entry.getValue().stream().map(TradeModel::getTid).collect(Collectors.toList()), 100)) {
                        String tidStr = StringUtils.join(tids, ",");
                        User user = staff.getUserIdMap().get(key);
                        if (user == null) {
                            Logs.warn(LogHelper.buildLog(staff, String.format("京东订单上传日志失败，找不到user，userId=%s,tids=%s", entry.getKey(), tidStr)));
                            continue;
                        }
                        jdTradeAccess.uploadOrderInfoLog(IpUtils.getClientIP(request),
                                deviceId,
                                tidStr,
                                5,
                                "https://erp.superboss.cc/trade/search/sids",
                                user,
                                staff);

                        jdTradeAccess.uploadDBOperationLog(IpUtils.getClientIP(request),
                                deviceId,
                                "https://erp.superboss.cc/trade/search/sids",
                                "select * from trade where tid in (" + tidStr + ")",
                                user,
                                staff);
                    }
                }
            }
        } catch (Exception e) {
            Logs.error("京东订单上传日志失败" + e.getMessage(), e);
        }
    }

    @RequestMapping(value = "/search/full/sids", method = RequestMethod.POST)
    @ResponseBody
    public Object searchFullBySids(TradeControllerParams queryParams, Integer needOrder, String orderFields, Integer useCompress, String api_name) throws SessionException {
        Assert.notNull(queryParams, "请传递参数");
        Assert.isTrue(StringUtils.isNotEmpty(queryParams.getSid()), "请输入sid参数");

        return search(queryParams, null, null, needOrder, orderFields, useCompress, null, null, null, api_name);
    }

    /**
     * http://doc.raycloud.com/pages/viewpage.action?pageId=30547829
     *
     * @param staff
     * @param tradeConfig
     * @param params
     * @return
     */
    public void fillSysOuterByMainOuterId(Staff staff, TradeConfig tradeConfig, TradeControllerParams params) {
        if (StringUtils.isBlank(params.getKey()) || !"mainOuterId".equals(params.getKey())) {
            return;
        }
        if (StringUtils.isBlank(params.getText())) {
            return;
        }
        params.setMainOuterId(params.getText());

    }

    @RequestMapping(value = "/search/hotItem", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object searchHotItem(TradeControllerParams queryParams, Page page, String api_name) throws SessionException {
        Staff staff = getStaff();
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        parameterCheck(queryParams);
        TradeQueryParams params = TradeQueryParams.copyParams(queryParams);
        params.setUseHasNext(queryParams.getUseHasNext());
        params.setMiniHotItem(queryParams.getMiniHotItem());
        params.setUseNewQuery(tradeConfig.getUseNewQuery());
        params.setAllowedPgl(true);
        params.setPage(page);
        params.setQueryFlag(queryParams.getQueryFlag());
        params.setHotItemType(queryParams.getHotItemType());

        Orders orders = tradeSearchService.queryHotItem(staff, params);
        OrderModels orderModels = new OrderModels();
        orderModels.setList(OrderModel.entity2Vo(staff, orders.getList(), false, tradeConfig));
        orderModels.setPage(params.getPage());
        orderModels.setTotal(orders.getTotal());

        return orderModels;
    }

    @RequestMapping(value = "/search/hotItem/v2", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object searchHotItemV2(@RequestBody TradeHotItemParams params, String api_name) throws SessionException {

        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, params);

        return tradeHotItemSearchService.queryHotItem(staff, params);
    }

    @RequestMapping(value = "/search/count", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object searchCount(TradeControllerParams queryParams, Page page, String api_name) throws SessionException {
        queryParams.setUseHasNext(null);
        //只查询总数 不查询订单
        queryParams.setQueryFlag(2);
        Object search = null;
        try {
            Staff staff = getStaff();
            if (tradeLocalConfigurable.isTradeSearchCountFastReturn(staff.getCompanyId())) {
                TradeModels tradeModels = new TradeModels();
                tradeModels.setTotal(page != null ? NumberUtils.Integer2Long(page.getStartRow() + page.getOffsetRow() + 1) : 20L);
                tradeModels.setPage(page);
                return tradeModels;
            }
            search = search(queryParams, page, null, 0, null, null, null, null, null, api_name);
        } catch (HystrixRuntimeException | SearchTimeoutException e) {
            //超时默认返回成功
            return TradeModels.toEmpty();
        }
        return search;
    }

    /**
     * 订单查询统一接口
     *
     * @param queryParams
     * @param page
     * @param sort
     * @param needOrder   是否需要查询order 默认查询  1=需要  0=不需要
     * @param orderFields 需要查询order的哪些字段 默认查询所有
     * @param useCompress 是否需要进行压缩  1=需要  0=不需要
     * @param api_name
     * @return
     * @throws SessionException
     */
    @AccessShields(shield = {
            @AccessShield(value = "211", condition = @AccessCondition(field = "queryId", expected = {"8"})),
            @AccessShield(value = "213", condition = @AccessCondition(field = "queryId", expected = {"20", "21", "22", "23", "52", "53", "60", "62", "84"})),
            @AccessShield(value = "217", condition = @AccessCondition(field = "queryId", expected = {"25", "26", "27", "28", "41"})),
            @AccessShield(value = "216", condition = @AccessCondition(field = "queryId", expected = {"24"})),
            @AccessShield(value = "221", condition = @AccessCondition(field = "queryId", expected = {"29", "30", "110"})),
            @AccessShield(value = "258", condition = @AccessCondition(field = "queryId", expected = {"50", "51"})),
            @AccessShield(value = "223", condition = @AccessCondition(field = "queryId", expected = {"27"})),
            @AccessShield(value = "218", condition = @AccessCondition(field = "queryId", expected = {"33"})),
            @AccessShield(value = "219", condition = @AccessCondition(field = "queryId", expected = {"34"})),
            @AccessShield(value = "220", condition = @AccessCondition(field = "queryId", expected = {"35"})),
            @AccessShield(value = "260", condition = @AccessCondition(field = "queryId", expected = {"36"})),
            @AccessShield(value = "261", condition = @AccessCondition(field = "queryId", expected = {"61"})),
            @AccessShield(value = "262", condition = @AccessCondition(field = "queryId", expected = {"38"})),
            @AccessShield(value = "291", condition = @AccessCondition(field = "queryId", expected = {"77"})),
            @AccessShield(value = "295", condition = @AccessCondition(field = "queryId", expected = {"26001", "26002", "26003", "26004"}))
    })
    @RequestMapping(value = "/search", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object search(TradeControllerParams queryParams, Page page, Sort sort, Integer needOrder, String orderFields,
                         Integer useCompress, Integer needAllocateStock, Integer blurTrade, String sortExcepLevels, String api_name) throws SessionException {
        Staff staff = getStaff();
        TradeConfig tradeConfig = tradeConfigService.get(staff);
//      todo  重复代码 删除
//        if (null != queryParams.getWaveShortId()) {
//            Map<Long,Long> waveIdMap = tradeWaveService.queryWaveIdAndShortId(staff, Lists.newArrayList(queryParams.getWaveShortId()), null);
//            if (MapUtils.isNotEmpty(waveIdMap)) {
//                queryParams.setWaveId(waveIdMap.keySet().iterator().next());
//            }
//        }
        TradeQueryParams params = null;
        AbsLogBuilder logBuilder = new QueryLogBuilder(staff).append("search耗时统计").setBaseTooklmt(100L).append("queryFlag", queryParams.getQueryFlag()).append("useHasNext", queryParams.getUseHasNext()).startTimer();
        try {
            long start = System.currentTimeMillis();
            params = buildTradeQueryParams(queryParams, page, sort, needOrder, sortExcepLevels, staff, tradeConfig);
            if(orderFields != null && orderFields.contains("goodsSectionCodes")) {
                params.setContainsSuitSingle(true);
            }
            logBuilder.recordTimer("buildTradeQueryParams");
            params.getContext().setLogBuilder(logBuilder);
            Trades trades = tradeSearchService.search(staff, params);
            try {
                TradeModels tradeModels = buildTradeModels(queryParams, useCompress, needAllocateStock, blurTrade, needOrder, orderFields, staff, tradeConfig, params, trades, logBuilder);
                logBuilder.reBaseTimer();
                fullTradeTrace(staff, tradeModels, params.getQueryId() != null && params.getQueryId() == QUERY_SELLER_SEND_GOODS);
                logBuilder.recordTimer("fullTradeTrace");
                fullTradePay(staff, tradeModels);
                logBuilder.recordTimer("fullTradePay");
                //fullPddTradeData(staff, tradeModels);
                Optional.ofNullable(params.getQueryId()).ifPresent(queryId -> MDC.put("queryId", queryId));
                handleNewFxTrade(staff, tradeModels);
                logBuilder.recordTimer("handleNewFxTrade");

                queryFillBusiness.fillTradeInfos(staff, tradeModels.getList(),logBuilder,trades.getList(),InfoLevelEnum.LIST);


                boolean notShowTips = featureService.checkHasFeature(staff.getCompanyId(), Feature.SLOW_SEARCH_NO_TIPS);
                long took = System.currentTimeMillis() - start;
                if (took > 20000 && !notShowTips) {
                    tradeModels.setQuerySuggests(params.getContext().getSuggestString(took,false));
                }
                handleTradeTotal(params,tradeModels,took);
                return tradeModels;
            } catch (Throwable e) {
                logger.error(LogHelper.buildLog(staff, "订单查询结果处理出错:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
                throw new TradeException("订单查询结果处理出错,clueId:" + ClueIdUtil.getClueId());
            }
        } finally {
            logBuilder.append("suggests", params !=null?params.getContext().getSuggestString(null,false):null).startWatch().appendTook(queryTookThreshold(staff,queryParams)).multiPrintInfo(logger);
        }
    }


    protected Long queryTookThreshold(Staff staff,TradeControllerParams queryParams){
        if (Objects.equals(queryParams.getDebug(),1)) {
            return 1L;
        }
        if (DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV,staff.getCompanyId(), LogBusinessEnum.QUERY.getSign())) {
            return 500L;
        }
        return 3500L;
    }

    /**
     * 对于订单列表页面 部分批量的操作 是直接将当前页面的查询参数传入后端去过滤数据的 如 /trade/audit
     * 这些批量接口都只会走TradeQueryParams.copyParams()方法
     * 因此 为保证列表所见与批量处理的数据范围一致 所有前端支持的查询条件传递都必需写入copyParams()方法
     * @return
     */
    public TradeQueryParams buildTradeQueryParams(TradeControllerParams queryParams, Page page, Sort sort, Integer needOrder, String sortExcepLevels, Staff staff, TradeConfig tradeConfig) {
        //参数检查
        parameterCheck(queryParams);
        TradeQueryParams params = TradeQueryParams.copyParams(queryParams);
        //
        params.setUseHasNext(queryParams.getUseHasNext());

        //对接oaid后查询淘宝天猫订单
//        fillSpecialParams(staff, params);
        params.setUseNewQuery(tradeConfig.getUseNewQuery());//使用新的query方式
        params.setAllowedPgl(true);
        params.setPage(page);
        params.setQueryFlag(queryParams.getQueryFlag());
        //前端发起的请求，才允许过滤部分商品关闭的订单
        params.setAllowExcludeClosedTrade(true);

        if (StringUtils.isNotBlank(sortExcepLevels)) {
            List<SortExcepLevel> sortExcepLevelsList = JSON.parseArray(sortExcepLevels, SortExcepLevel.class);
            sort.setSortExcepLevel(sortExcepLevelsList);
        }
        initParamsSort(staff, params, sort);
        params.setNeedOrder(needOrder);
        params.setAllowQueryUnboundUniqueCode(true);

        fillContainsSuitSingle(params);

        params.getContext().setClientType("http");
        return params;
    }

    protected TradeModels buildTradeModels(TradeControllerParams queryParams, Integer useCompress,Integer needAllocateStock, Integer blurTrade,
                                           Integer needOrder, String orderFields,Staff staff, TradeConfig tradeConfig, TradeQueryParams params, Trades trades,AbsLogBuilder logBuilder) {
        return buildTradeModels(queryParams,useCompress,needAllocateStock,blurTrade,needOrder,orderFields,staff,tradeConfig,params,trades,logBuilder,null);
    }

    protected TradeModels buildTradeModels(TradeControllerParams queryParams, Integer useCompress, Integer needAllocateStock, Integer blurTrade,
                                           Integer needOrder, String orderFields, Staff staff, TradeConfig tradeConfig, TradeQueryParams params, Trades trades, AbsLogBuilder logBuilder,TradeModels instance) {
        //显示毛利润计算公式
        fillGrossProfitDisplay(staff, params, trades.getList());
        logBuilder.reBaseTimer();
        fillItemInfo(staff, params, trades.getList(), logBuilder);
        //  params.setUseNewQuery(false);//再改回去
        //查询待打包的订单、查询待称重的订单、查询待发货的订单支持系统订单号的查询，如果快递单号与系统订单号存在重复，可能会出现多条，优先显示快单号
        tradeQueryService.filterOutSid(trades, params.getQueryId(), params.getMixKey());
        logBuilder.recordTimer("filterOutSid");
        //过滤虚拟商品
        filterVirtualItemSourceAndDest(staff, trades.getList(), params);
        logBuilder.recordTimer("filterVirtualItemSourceAndDest");
        //填充订单所属平台
        tradeQueryService.fillSubSourceName(trades.getList());
        logBuilder.recordTimer("fillSubSourceName");

        Map<Long, TradeOriginReceiverInfos> tradeOriginReceiverInfos = getTradeOriginReceiverInfos(trades.getList());
        try {
            if(BooleanUtils.isNotFalse(queryParams.getIfSensitive()) || ConfigUtils.isConfigOn(ConfigHolder.TRADE_SEARCH_CONFIG.getInvokePlatformSensitiveApi(),String.valueOf(staff.getCompanyId()))){
                //如果传了 pddMask=true 那么在SolrTradeSearchService.search 方法里已经脱敏了 这里不需要重复处理
                if (!params.isPddMask()) {
                    tradePddTradeBusiness.pddMaskDataReplace(staff, trades.getList());
                    logBuilder.recordTimer("pddMaskDataReplace");
                }
                fxgTradeDecryptBusiness.batchSensitive(staff, trades.getList());
                logBuilder.recordTimer("fxgTradeSensitive");
                commonTradeDecryptBusiness.batchSensitive(staff, trades.getList());
                logBuilder.recordTimer("commonTradeSensitive");
            }
        } catch (Throwable e) {
            logger.error(LogHelper.buildLog(staff, "数据脱敏处理失败:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
        }
        boolean highlight = queryParams.getHighlight() == null ? false : queryParams.getHighlight();
        if (blurTrade != null) {
            Map<String, Object> configMap = TradeConfigUtils.parseExtendConfig(tradeConfig);
            configMap.put("blurTradeFromWeb", blurTrade);
            tradeConfig.setChatConfigs(JSONObject.toJSONString(configMap));
        }
        logBuilder.reBaseTimer();
        // 获取订单上一单多包数量
        getWaybillCount(staff, trades.getList());
        TradeModels tradeModels = TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, highlight, queryParams.getText(), tradeConfig, true,useCompress,instance);
        logBuilder.recordTimer("toTradeModels");
        //仅仅在单页排序，视图层排序，其他业务功能不做排序
        sortTradeModels(staff, tradeModels, params);
        logBuilder.recordTimer("sortTradeModels");
        handleOrder(tradeModels, needOrder, orderFields, useCompress);
        logBuilder.recordTimer("handleOrder");
        if (needAllocateStock != null && needAllocateStock.longValue() == 1) {
            stockInfoModel(staff, tradeModels);
            logBuilder.recordTimer("stockInfoModel");
        }
        fillTradeOriginReceiverInfos(tradeOriginReceiverInfos, tradeModels);
        //vipjit拣货单
        handleVipJit(staff, tradeModels);
        logBuilder.recordTimer("handleVipJit");
        fillShipperBatch(staff, tradeModels, params);
        logBuilder.recordTimer("fillShipperBatch");
        if(orderFields != null && orderFields.contains("goodsSectionCodes")) {
            Long queryId = params.getQueryId();
            //填充货位的情况下会额外查询套件子商品对应的order 这里如果本身返回不需要的仍然需要剔除
            //这个判断来源于 com.raycloud.dmj.web.controllers.trades.TradeQueryController.fillContainsSuitSingle 不是这几个queryId的不返回子订单
            boolean  removeSuits = queryId != null && (queryId - QUERY_WAIT_PACK != 0 && queryId - QUERY_WAIT_WEIGH != 0 && queryId - QUERY_BOXING != 0);
            orderGoodsSectionFillBusiness.fillOrderGoodsSectionCode(staff, tradeModels,trades.getList(),removeSuits);
            logBuilder.recordTimer("goodsSectionCode");
        }

        fillJdVCGongXiaoDaInfo(staff, params, tradeModels.getList(), logBuilder);
        return tradeModels;
    }

    private void fillJdVCGongXiaoDaInfo(Staff staff, TradeQueryParams params, List<TradeModel> tradeModelList, AbsLogBuilder logBuilder) {
        if (CollectionUtils.isEmpty(tradeModelList) || staff.getUser() == null) {
            return;
        }
        try{
            Map<String,TradeModel> jdGxdTradeMap = tradeModelList.stream().filter(o->CommonConstants.PLAT_FORM_TYPE_JD_VC.equals(o.getSource()) && Objects.equals(o.getSubSource(),"jd_gxd"))
                    .collect(Collectors.toMap(TradeModel::getTid,a->a,(a,b)->b));
            if(MapUtils.isEmpty(jdGxdTradeMap)){
                return;
            }

            List<JdvcCarrierInfo> jdvcCarrierInfos = jdvcGxdPreDecisionService.queryByTids(staff.getUser(), Lists.newArrayList(jdGxdTradeMap.keySet()));

            if(CollectionUtils.isEmpty(jdvcCarrierInfos)){
                return;
            }
            jdvcCarrierInfos.forEach(o->{
                TradeModel tradeModel = jdGxdTradeMap.get(o.getTid());
                if(tradeModel != null){
                    String  carrierLogisticsName = getJdVCGongXiaoDaInfo(o.getCarrierPerformanceInfo(),"【平台结算】");
                    String  selfLogisticsName = getJdVCGongXiaoDaInfo(o.getSelfSettlePerformanceInfo(),"【自行结算】");
                    String logisticsName = Stream.of(carrierLogisticsName, selfLogisticsName)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining("\n"));
                    tradeModel.setLogisticsName(logisticsName);
                }
            });

            if (logBuilder != null) {
                logBuilder.recordTimer("fillJdVCGongXiaoDaInfo");
            }
        }catch (Error e){
            logger.error(LogHelper.buildLog(staff, "填充京东工小达物流推荐失败:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
        }

    }

    private String getJdVCGongXiaoDaInfo(String codes,String suffix){
        if(StringUtils.isBlank(codes)){
            return "";
        }
        List<String> carrierPerformanceInfo = Splitter.on(",").splitToList(codes);
        if(CollectionUtils.isNotEmpty(carrierPerformanceInfo)){
            return carrierPerformanceInfo.stream().map(TradeConstants.JDVC_GXD_CARRIER_MAP::get).filter(StringUtils::isNotBlank).collect(Collectors.joining(",","",suffix));
        }
        return "";
    }

    public void getWaybillCount(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> needWaybillCountList = trades.stream()
                .filter(trade -> StringUtils.isNotEmpty(trade.getOutSid()) && TradeTagUtils.checkIfExistTag(trade, TAG_1000000044.getSystemTag()))
                .collect(toList());

        if (CollectionUtils.isEmpty(needWaybillCountList)) {
            return;
        }

        Map<String, List<MultiPacksPrintTradeLogDetail>> logDetailsMap = multiPacksPrintTradeLogService.queryPrintLogDetailByOutSids(staff, needWaybillCountList);
        for (Trade originTrade : needWaybillCountList) {
            // 打印记录页面打印数量默认为1
            originTrade.setWaybillCount(1);
            List<MultiPacksPrintTradeLogDetail> multiPacksPrintTradeLogDetailList = logDetailsMap.get(originTrade.getOutSid());
            if (!CollectionUtils.isEmpty(multiPacksPrintTradeLogDetailList)) {
                List<String> detailOutSidList = multiPacksPrintTradeLogDetailList.stream()
                        .filter(t -> t.getIsCancel() == 0)
                        .map(MultiPacksPrintTradeLogDetail::getOutSid)
                        .collect(Collectors.toList());
                originTrade.setWaybillCount(detailOutSidList.size());
            }
        }
    }

    @RequestMapping(value = "/query/split/outSids", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object querySplitOutSids(TradeControllerParams queryParams, Page page, Sort sort, Integer needOrder, String orderFields, String sortExcepLevels, String api_name) throws Exception {
        String outSids = queryParams.getOutSid();
        Assert.isTrue(outSids != null, "运单号不能为空！");
        Staff staff = getStaff();
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        TradeQueryParams params = buildTradeQueryParams(queryParams, page, sort, needOrder, sortExcepLevels, staff, tradeConfig);
        long start = System.currentTimeMillis();
        Trades trades = tradeSearchService.search(staff, params);

        List<String> splitGetOutSidList = multiPacksPrintTradeLogService.querySplitGetByOutSids(staff, trades.getList());
        splitGetOutSidList.addAll(Arrays.asList(outSids.split(",")));
        Map<String, Object> result = new HashMap<>();
        result.put("outSidList", splitGetOutSidList.stream()
                .distinct()
                .collect(toList()));
        return result;
    }

    /**
     * vipjit拣货单相关业务
     *
     * @param staff
     * @param tradeModels
     */
    void handleVipJit(Staff staff, TradeModels tradeModels) {
        if (staff == null || tradeModels == null) {
            return;
        }
        List<TradeModel> models = tradeModels.getList();
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        List<TradeModel> vipJitTrades = models.stream().filter(tm -> CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(tm.getSource())
                && CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(tm.getSubSource())).collect(toList());
        if (CollectionUtils.isNotEmpty(vipJitTrades)) {
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            Integer hourNum = (Integer) tradeConfig.get("vipJitLastTimeHourNum");
            List<Long> userIds = vipJitTrades.stream().map(TradeModel::getUserId).collect(Collectors.toList());
            Map<Long, String> userIdMap = vipDeliveryDataService.getVendorNamesByUserIds(staff, userIds);
            userIdMap = userIdMap == null ? new HashMap<>() : userIdMap;
            for (TradeModel vipJitTrade : vipJitTrades) {
                if (hourNum != null) {
                    Date endTime = DateUtil.addDateByHour(vipJitTrade.getCreated(), 24);
                    vipJitTrade.setRemainDeliveryTime(endTime.getTime() - System.currentTimeMillis());
                }
                String name = userIdMap.get(vipJitTrade.getUserId());
                vipJitTrade.setVendorName(name);
            }
        }
    }

    private void sortTradeModels(Staff staff, TradeModels tradeModels, TradeQueryParams params) {
        List<TradeModel> sortReulst = new ArrayList<>();
        boolean sorted = false;
        if (CollectionUtils.isEmpty(tradeModels.getList()) || tradeModels.getList().size() == 1 || CollectionUtils.isEmpty(params.getSortConfigs())) {
            sortReulst = tradeModels.getList();
        }else {
            // sortString CONVERT( t.`sys_outer_id` USING 'GBK') ASC 跟数据排序有差异，itemKindCount,sortString,itemCount 既然已经排序好了，不需要再次排序
            if(StringUtils.equals("single",params.getItemOrder()) || StringUtils.equals("multi",params.getItemOrder()) || (params.getSortConfigs().stream().anyMatch(o->StringUtils.equals(o.getDataType(),SortConfig.SINGLE_DATA_TYPE)))){
                Map<String, List<TradeModel>> groupedData = tradeModels.getList().stream()
                        .collect(Collectors.groupingBy(
                                data -> data.getItemKindCount() + "_" + data.getSortString() + "_" + data.getItemCount(), // 分组键
                                LinkedHashMap::new, // 使用 LinkedHashMap 保持顺序
                                Collectors.toList()
                        ));
                for (Map.Entry<String, List<TradeModel>> entry : groupedData.entrySet()) {
                    String k = entry.getKey();
                    List<TradeModel> v = entry.getValue();
                    sortReulst.addAll(TradeSortConfigUtils.sort(staff, params.getSortConfigs(), v));
                }
            }else {
                sortReulst = TradeSortConfigUtils.sort(staff, params.getSortConfigs(), tradeModels.getList());
            }
            sorted = true;
        }
        int index = 1;
        String text = params.getText();
        if (CollectionUtils.isNotEmpty(sortReulst)) {
            for (TradeModel tradeModel : sortReulst) {
                if (sorted) {
                    tradeModel.setIndex(index++);
                }
                //重新组织成适合前端展示的内容 不同的点在于 原来数量是 *001,这里改为 *1
                tradeModel.setSortString(TradeModel.buildSortString(tradeModel));
                if (StringUtils.isNotBlank(text)) {
                    HighlightUtils.HighlightInfo info = HighlightUtils.highlight(tradeModel.getSortString(), text, "<em>", "</em>");
                    if (info.isHighlighting()){
                        Map<String, Object> highlights = tradeModel.getHighlights();
                        if (highlights == null) {
                            highlights = new HashMap<>();
                            tradeModel.setHighlights(highlights);
                        }
                        highlights.put("sortString",info.getContent());
                    }
                }
                sortOrderModels(staff,tradeModel,params);
            }
        }
        tradeModels.setList(sortReulst);
    }

    private void initParamsSort(Staff staff, TradeQueryParams params, Sort sort) {
        //TODO   现有查询中排序的逻辑有三套，如果后续需要支持所有排序，需要讲三套逻辑整合
        if (StringUtils.isNotBlank(params.getItemOrder()) && sort != null && StringUtils.isNotBlank(sort.getField())) {
            sort = null;
        }
        //单独处理排序会导致其他业务排序不对
        if (sort != null) {
            List<SortConfig> sortConfigs = TradeSortConfigUtils.convertSort(staff, sort);
            if (sortConfigs == null) {
                params.setSort(sort);
            } else {
                params.setSortConfigs(sortConfigs);
            }
        }

        //V2版本后端处理无变化 只是加了前端处理
        if (Objects.equals("outerIdAscV2", params.getItemOrder())) {
            params.setItemOrder("outerIdAsc");
        }
        if (Objects.equals("outerIdDescV2", params.getItemOrder())) {
            params.setItemOrder("outerIdDesc");
        }

        if (StringUtils.isNotBlank(params.getItemOrder())) {
            if (Objects.equals("outerIdAsc", params.getItemOrder())
                    || Objects.equals("outerIdDesc", params.getItemOrder())) {
                return;
            }
            Sort sort1 = new Sort();
            sort1.setField("singleItemOrder");
            //兼容其他的排序
            if ("single".equals(params.getItemOrder())) {
                sort1.setOrder("ASC");
            } else if ("multi".equals(params.getItemOrder())) {
                sort1.setOrder("DESC");
            }
            if (params.getSortConfigs() == null) {
                params.setSortConfigs(TradeSortConfigUtils.convertSort(staff, sort1));
            } else {
                params.getSortConfigs().addAll(TradeSortConfigUtils.convertSort(staff, sort1));
            }
        }
    }

    private void sortOrderModels(Staff staff, TradeModel tradeModel, TradeQueryParams params) {
        if (!Objects.equals(params.getItemOrder(),"orderTypeSort")) {
            return;
        }
        List<OrderModel> orders = tradeModel.getOrders();
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        orders.sort(new Comparator<OrderModel>() {
            @Override
            public int compare(OrderModel o1, OrderModel o2) {
                return  calOrderSortScore(staff,o2) - calOrderSortScore(staff,o1);
            }
        });
        tradeModel.setOrders(orders);
    }


    private int calOrderSortScore(Staff staff,OrderModel order){
        int score = 0;
        if (order.getGiftNum() != null && order.getGiftNum() > 0) {
            score  += 10;
        }
        if (Objects.equals(order.getSource(),CommonConstants.PLAT_FORM_TYPE_SYS)) {
            score  += 5;
        }
        return score;
    }

    /**
     * 批量查询时检查id是否存在
     */
    @RequestMapping(value = "/trace/checkids", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object checkIds(TradeControllerParams queryParams, Integer idType) throws SessionException {
        Staff staff = getStaff();
        TradeQueryParams params = TradeQueryParams.copyParams(queryParams);
        List<String> notInids = tradeSearchService.checkIds(staff, params);
        return notInids;
    }

    @RequestMapping(value = "/item/queryPureItem", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object queryPureItem(QueryPureItemByParamsRequest request) throws SessionException {
        Staff staff = getStaff();
        request.setStaffRequest(StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build());
        return dmjItemCommonSearchApi.queryPureItem(request).getData();
    }

    /**
     * 填充商品信息
     * 不想做没办法
     *
     * @param staff
     * @param params
     * @param trades
     */
    protected void fillItemInfo(Staff staff, TradeQueryParams params, List<Trade> trades, AbsLogBuilder logBuilder) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        try {
            //是否开启了订单列表中显示商品主商家编码
            TradeConfigNew showMainSysOuterId = TradeConfigGetUtil.get(staff, TradeConfigEnum.SHOW_MAIN_SYS_OUTER_ID);
            if (showMainSysOuterId.isOpen() || (!Objects.isNull(params) && params.isShowSysItemOuterId())) {
                //queryId 为这些的才会填充主商家编码
                if (params != null && params.getQueryId() != null) {
                    Long queryId = params.getQueryId();
                    //查询订单处理页面/订单管理/订单查询(3个月前)/快递打印（未分配快递/快递单未打印/打印发货/快递单打印记录）
                    if (isFillItemInfoPage(queryId)) {
                        fillItemInfo(staff, trades);
                    }
                } else {
                    //为空是bySid查询也要填充
                    fillItemInfo(staff, trades);
                }
                if (logBuilder != null) {
                    logBuilder.recordTimer("fillItemInfo");
                }
            }
        } catch (Throwable e) {
            logger.error(LogHelper.buildLog(staff, "填充商品数据失败:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
        }
        try {
            tradeOrderSyncItemTagFill.fill(staff, trades);
            if (logBuilder != null) {
                logBuilder.recordTimer("fillOrderSyncItemTag");
            }
        } catch (Throwable e) {
            logger.error(LogHelper.buildLog(staff, "填充商品标签失败:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
        }
    }

    private boolean isFillItemInfoPage(Long queryId) {
        return queryId == QUERY_UN_CONSIGNED || queryId == QUERY_WAIT_PAY || queryId == QUERY_WAIT_AUDIT || queryId == QUERY_FINISHE_AUDIT || queryId == QUERY_EXCEP || queryId == QUERY_PRESELL || queryId == QUERY_COMMON ||
                queryId == QUERY_WAIT_SET_TEMPLATE || queryId == QUERY_WAIT_PRINT || queryId == QUERY_FINISH_PRINT || queryId == QUERY_PRINT_FAHUO ||
                queryId == QUERY_PRINT_V2 ||
                queryId == QUERY_CLOSED || queryId == QUERY_FINISHED || queryId == QUERY_CANCEL
                || queryId ==  QUERY_SELLER_SEND_GOODS || queryId == QUERY_WAIT_DEST_SEND_GOODS || queryId ==QUERY_WAIT_SELF_SEND_GOODS;
    }

    private void fillItemInfo(Staff staff, List<Trade> trades) {
        Set<Long> sysItemIds = new HashSet<>();
        //如果纯商品Id<0那么是纯商品
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                //判断是不是系统商品
                if (order.getItemSysId() > 0) {
                    sysItemIds.add(order.getItemSysId());
                }
            }
        }

        Map<Long, SysItemSku> map = new HashMap<Long, SysItemSku>();
        if (!sysItemIds.isEmpty()) {//纯商品
            List<List<Long>> partition = Lists.partition(Lists.newArrayList(sysItemIds), 500);
            for (List<Long> itemIds : partition) {
                List<DmjItem> itemList = itemServiceWrapper.queryBySysItemIds(staff, itemIds, "sysItemId,outerId");
                for (DmjItem item : itemList) {
                    map.put(item.getSysItemId(), buildItemSku(item.getSysItemId(), null, item.getOuterId(), item.getBarcode()));
                }
            }
        }
        //放入order返回
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order o : orders) {
                SysItemSku target = map.get(o.getItemSysId());
                if (target != null) {
                    o.setMainOuterId(target.getOuterId());
                    o.setSysItemOuterId(target.getOuterId());
                }
            }
        }
    }

    public void fullTradePay(Staff staff, TradeModels tradeModels) {
        try {
            if (!tradePayBusiness.isOpenTradePay(staff)) return;
            if (tradeModels != null && CollectionUtils.isNotEmpty(tradeModels.getList())) {
                List<Long> sids = tradeModels.getList().stream().map(trade -> Long.parseLong(trade.getSid())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(sids)) {
                    return;
                }
                Map<Long, TradePayDetail> maps = tradePayBusiness.tradePayDetailMapGet(staff, sids);
                for (TradeModel tradeModel : tradeModels.getList()) {
                    TradePayDetail tradePayDetail = maps.get(Long.parseLong(tradeModel.getSid()));
                    if (tradePayDetail != null) {
                        if (StringUtils.isNotBlank(tradeModel.getMergeSid()) && !"-1".equals(tradeModel.getMergeSid())) {
                            if (logger.isDebugEnabled()) {
                                logger.debug(LogHelper.buildLogHead(staff).append(" tradeModel.getMergeSid():").append(tradeModel.getMergeSid()));
                            }
                            tradePayBusiness.mergeAmountHandle(staff, tradePayDetail, tradeModel.getSid(), tradeModel.getMergeSid());
                        }
                        tradeModel.setTradePurchaseAmount(tradePayDetail.getTradePurchaseAmount().doubleValue());
                        tradeModel.setTradePays(tradePayDetail.getTradePays());
                        tradeModel.setManualPaymentAmount(tradePayDetail.getManualPaymentAmount().doubleValue());
                        tradeModel.setPlatformPaymentAmount(tradePayDetail.getPlatformPaymentAmount().doubleValue());
                    }
                }
            }
        } catch (Throwable e) {
            logger.error(LogHelper.buildLog(staff, "填充订支付单数据失败:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
        }

    }

    private void fullPddTradeData(Staff staff, TradeModels tradeModels) {
        try {
            if (tradeModels != null && CollectionUtils.isNotEmpty(tradeModels.getList())) {
                Map<Long, List<TradeModel>> userTradeMap = tradeModels.getList().stream().
                        filter(tradeModel -> CommonConstants.PLAT_FORM_TYPE_PDD.equals(tradeModel.getSource())).collect(groupingBy(TradeModel::getUserId));
                Map<String, String> resultMap = Maps.newHashMap();
                for (Map.Entry<Long, List<TradeModel>> it : userTradeMap.entrySet()) {
                    List<DataListItemVO> dataListItemVOList = Lists.newArrayList();
                    User user = staff.getUserIdMap().get(it.getKey());
                    for (TradeModel tradeModel : it.getValue()) {
                        if ("fds".equals(tradeModel.getSubSource())) continue;
                        if (StringUtils.isNotBlank(tradeModel.getReceiverAddress())) {
                            dataListItemVOList.add(new DataListItemVO(tradeModel.getTid(), tradeModel.getReceiverAddress()));
                        }
                        if (StringUtils.isNotBlank(tradeModel.getReceiverMobile())) {
                            dataListItemVOList.add(new DataListItemVO(tradeModel.getTid(), tradeModel.getReceiverMobile()));
                        }
                        if (StringUtils.isNotBlank(tradeModel.getReceiverName())) {
                            dataListItemVOList.add(new DataListItemVO(tradeModel.getTid(), tradeModel.getReceiverName()));
                        }
                    }

                    resultMap.putAll(tradePddTradeBusiness.getMaskDatas(user, dataListItemVOList));
                }
                if (MapUtils.isNotEmpty(resultMap)) {
                    for (TradeModel tradeModel : tradeModels.getList()) {
                        if (!CommonConstants.PLAT_FORM_TYPE_PDD.equals(tradeModel.getSource())) continue;
                        if (resultMap.get(tradeModel.getReceiverAddress()) != null && !PddSecretUtils.isEncryptData(resultMap.get(tradeModel.getReceiverAddress()))) {
                            tradeModel.setReceiverAddress(resultMap.get(tradeModel.getReceiverAddress()));
                        } else {
                            tradeModel.setReceiverAddress("****");
                        }
                        if (resultMap.get(tradeModel.getReceiverName()) != null && !PddSecretUtils.isEncryptData(resultMap.get(tradeModel.getReceiverName()))) {
                            tradeModel.setReceiverName(resultMap.get(tradeModel.getReceiverName()));
                            tradeModel.setBuyerNick(resultMap.get(tradeModel.getReceiverName()));
                        } else {
                            tradeModel.setReceiverName("****");
                            tradeModel.setBuyerNick("****");
                        }
                        if (resultMap.get(tradeModel.getReceiverMobile()) != null && !PddSecretUtils.isEncryptData(resultMap.get(tradeModel.getReceiverMobile()))) {
                            tradeModel.setReceiverMobile(resultMap.get(tradeModel.getReceiverMobile()));
                        } else {
                            tradeModel.setReceiverMobile("****");
                        }
                    }
                }
            }
        } catch (Throwable e) {
            logger.error(LogHelper.buildLog(staff, "填充拼多多数据失败:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
        }

    }

    protected void fullTradeTrace(Staff staff, TradeModels tradeModels, boolean need) {
        try {
            if (need && tradeModels != null && CollectionUtils.isNotEmpty(tradeModels.getList())) {
                Long[] sids = tradeModels.getList().stream().map(trade -> Long.parseLong(trade.getSid())).toArray(Long[]::new);
                List<LogisticsTrackingPollPool> logisticsTrackingPollPools = logisticsTrackingRecordService.exceptLogisticsQueryBysids(staff, sids);
                Map<String, LogisticsTrackingPollPool> poolMap = logisticsTrackingPollPools.stream().collect(Collectors.toMap(pool -> String.valueOf(pool.getSid()), Function.identity(), (a, b) -> b));
                for (TradeModel tradeModel : tradeModels.getList()) {
                    LogisticsTrackingPollPool logisticsTrackingPollPool = poolMap.get(tradeModel.getSid());
                    if (logisticsTrackingPollPool != null) {
                        tradeModel.setLogisticsTraceCount(logisticsTrackingPollPool.getLogisticsTraceCount());
                        tradeModel.setLastLogisticsTrace(logisticsTrackingPollPool.getLastLogisticsTrace());
                        if (logisticsTrackingPollPool.getLogisticsModified() != null && logisticsTrackingPollPool.getLogisticsModified().after(TradeTimeUtils.INIT_DATE)) {
                            tradeModel.setLastLogisticsTime(logisticsTrackingPollPool.getLogisticsModified());
                        }
                    }
                }
            }
        } catch (Throwable e) {
            logger.error(LogHelper.buildLog(staff, "填充订单日志数据失败:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
        }
    }

    private void handleNewFxTrade(Staff staff, TradeModel tradeModel){
        TradeModels tradeModels = new TradeModels();
        tradeModels.setList(Lists.newArrayList(tradeModel));
        handleNewFxTrade(staff,tradeModels);
    }
    //处理NewFx订单
    protected void handleNewFxTrade(Staff staff, TradeModels tradeModels) {
        List<TradeModel> tradeModelList = tradeModels.getList();
        Map<Long, MultiValueMap<Long, TradeModel>> companyIdTidModelSourceMap = new HashMap<>();
        Map<Long, Map<Long, TradeModel>> companyIdTidModelDestMap = new HashMap<>();

        Set<Long> fxOrGxCompanyIds = new HashSet<>();

        if (tradeModelList != null) {
            try {
                for (TradeModel tradeModel : tradeModelList) {
                    boolean isFxSource = tradeModel.getConvertType() != null && tradeModel.getConvertType() == 1 && tradeModel.getSourceId() != null && tradeModel.getDestId() != null && tradeModel.getBelongType() != null;
                    if (isFxSource) {
                        //供销订单
                        if (tradeModel.getBelongType() == 2 || tradeModel.getBelongType() == 3) {
                            MultiValueMap<Long, TradeModel> tradeModelMap = companyIdTidModelSourceMap.get(tradeModel.getSourceId());
                            if (tradeModelMap == null) {
                                tradeModelMap = new LinkedMultiValueMap<>();
                                companyIdTidModelSourceMap.put(tradeModel.getSourceId(), tradeModelMap);
                                fxOrGxCompanyIds.add(tradeModel.getSourceId());
                            }
                            tradeModelMap.add(Long.parseLong(tradeModel.getTid()), tradeModel);
                        }
                        if (tradeModel.getBelongType() == 1 || tradeModel.getBelongType() == 3) {
                            //分销订单(发货后，优先使用自己的快递模版，自己没有再展示供销订单的) + 待供销商发货需要填充其他信息
                            String sysStatus = tradeModel.getSysStatus();
                            if (!(TradeUtils.ifContainV(tradeModel.getV(),TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID) ||
                                    Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS.equals(sysStatus) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(sysStatus) ||
                                    Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus) || Trade.SYS_STATUS_FINISHED.equals(sysStatus) || Trade.SYS_STATUS_CLOSED.equals(sysStatus))) {
                                continue;
                            }
                            if (Objects.equals(tradeModel.getDestId(), 0L)) {
                                continue;
                            }
                            Map<Long, TradeModel> tradeModelDestMap = companyIdTidModelDestMap.get(tradeModel.getDestId());
                            if (tradeModelDestMap == null) {
                                tradeModelDestMap = new HashMap<>();
                                companyIdTidModelDestMap.put(tradeModel.getDestId(), tradeModelDestMap);
                                fxOrGxCompanyIds.add(tradeModel.getDestId());
                            }
                            tradeModelDestMap.put(Long.parseLong(tradeModel.getSid()), tradeModel);
                        }
                    }
                }

                Map<Long, Staff> companyId2StaffMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(fxOrGxCompanyIds)) {
                    List<Staff> staffList = staffAssembleBusiness.listDefaultStaff4Trade(fxOrGxCompanyIds.toArray(new Long[0]));
                    companyId2StaffMap = Optional.of(staffList).map(list -> list.stream().collect(Collectors.toMap(Staff::getCompanyId, a -> a, (a, b) -> a))).orElse(Maps.newHashMap());
                }

                if (companyIdTidModelSourceMap.size() > 0) {
                    for (Map.Entry<Long, MultiValueMap<Long, TradeModel>> entry : companyIdTidModelSourceMap.entrySet()) {
                        Long companyId = entry.getKey();
                        MultiValueMap<Long, TradeModel> tidModelMap = entry.getValue();
                        Set<Long> sourceSidSet = tidModelMap.keySet();
                        Staff sourceStaff = companyId2StaffMap.get(companyId);
                        if (sourceStaff != null) {
                            List<Trade> sourceTradeList = tradeSearchService.queryBySidsContainDeleteTrade(sourceStaff, false, sourceSidSet.toArray(new Long[0]));
                            if (CollectionUtils.isNotEmpty(sourceTradeList)) {
                                Set<Long> userIds = new HashSet<>();
                                for (Trade trade : sourceTradeList) {
                                    if (TradeUtils.isGxOrMixTrade(trade)) {
                                        userIds.add(trade.getTaobaoId());
                                    } else {
                                        userIds.add(trade.getUserId());
                                    }
                                }
                                List<Shop> shops = shopService.queryByUserIds(null, userIds.toArray(new Long[0]));
                                Map<Long, Shop> shopMap = shops.stream().collect(Collectors.toMap(Shop::getUserId, shop -> shop));
                                for (Trade trade : sourceTradeList) {
                                    Shop shop = shopMap.get(TradeUtils.isGxOrMixTrade(trade) ? trade.getTaobaoId() : trade.getUserId());
                                    if (shop != null) {
                                        List<TradeModel> tradeModels1 = tidModelMap.get(trade.getSid());
                                        if (tradeModels1 != null) {
                                            //设置source为null,供销订单都属于平台单子, 供销订单中，分销店铺展示优先级：对外名称 > 简称 > 真名
                                            tradeModels1.forEach(tradeModel -> {
                                                String subSource = StringUtils.defaultIfBlank(trade.getSubSource(), trade.getSource());
                                                tradeModel.setShopSource(shop.getSource())
                                                        .setShopFlag(shop.getFlag()).setShopName(TradeUtils.getFxShopShowName(shop))
                                                        .setSubSource(StringUtils.equalsIgnoreCase(subSource,CommonConstants.PLAT_FORM_TYPE_SYS)?shop.getSource():subSource).setFxTid(trade.getTid());
                                            });
                                        }
                                    }
                                }
                            }
                        } else {
                            logger.debug(LogHelper.buildLogHead(staff).append(" sourceStaff is null:").append(companyId));
                        }
                    }
                }

                //处理分销订单
                if (companyIdTidModelDestMap.size() > 0) {
                    for (Map.Entry<Long, Map<Long, TradeModel>> entry : companyIdTidModelDestMap.entrySet()) {
                        Long companyId = entry.getKey();
                        //key sid value 分销订单
                        Map<Long, TradeModel> sidModelMap = entry.getValue();
                        Collection<TradeModel> fxTradeModels = sidModelMap.values();
                        Map<String, TradeModel> fxTradeModelMap = new HashMap<>();
                        for (TradeModel fxTradeModel : fxTradeModels) {
                            fxTradeModel.setGuid(fxTradeModel.getSourceId() + fxTradeModel.getDestId() + Long.parseLong(fxTradeModel.getSid()));
                            fxTradeModelMap.put(fxTradeModel.getSid(), fxTradeModel);
                        }
                        Set<Long> destTidSet = sidModelMap.keySet();
                        Staff destStaff = companyId2StaffMap.get(companyId);
                        if (destStaff != null) {
                            fillGxInfo(destStaff, destTidSet, fxTradeModelMap, fxTradeModels);
                        } else {
                            logger.debug(LogHelper.buildLogHead(staff).append(" sourceStaff is null:").append(companyId));
                        }
                    }
                }
            } catch (Throwable e) {
                logger.error(LogHelper.buildLog(staff, "[分销业务]分销订单填充供销信息报错:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
            }
        }
    }


    private void fillGxInfo(Staff destStaff, Set<Long> destTidSet, Map<String, TradeModel> fxTradeModelMap, Collection<TradeModel> fxTradeModels) {
        boolean ifNeedFillDestOtherInfo = Objects.equals(52L, MDC.get("queryId"));
        handleFxAppointTemplateId(destStaff, fxTradeModels);
        //根据tid查询供销订单
        List<TbTrade> destTradeLists = tradeSearchService.queryByTids(destStaff, ifNeedFillDestOtherInfo, Strings.listToString(destTidSet));
        List<TbTrade> destTradeList = destTradeLists.stream().filter(tbTrade -> !TradeUtils.isCancel(tbTrade)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(destTradeList)) {
            return;
        }
        destTradeList.forEach(tbTrade -> {
            TradeModel tradeModel = fxTradeModelMap.get(tbTrade.getTid());
            if (tradeModel == null) {
                return;
            }
            tbTrade.setGuid(tradeModel.getGuid());
        });
        List<Trade> gxTrades = Lists.newArrayListWithCapacity(destTradeList.size());
        fxBusiness.findGxTradesByMix(TradeUtils.toTrades(destTradeList), gxTrades);
        if (CollectionUtils.isEmpty(gxTrades)) {
            return;
        }
        Map<Long, List<Trade>> gx = gxTrades.stream().collect(groupingBy(Trade::getCompanyId));
        Map<Long, List<TradeModel>> collect = fxTradeModels.stream().collect(groupingBy(TradeModel::getGuid));
        gx.forEach((k, v) -> {
            for (Trade gxTrade : v) {
                List<TradeModel> tradeModels1 = collect.get(gxTrade.getGuid());
                if (CollectionUtils.isEmpty(tradeModels1)) {
                    continue;
                }
                Long templateId = gxTrade.getTemplateId();
                TradeModel tradeModel = tradeModels1.get(0);
                if (tradeModel == null) {
                    continue;
                }
                // 只有queryId 等于52 才设置对应供销的状态和异常信息
                if (ifNeedFillDestOtherInfo && (Trade.SYS_STATUS_FINISHED_AUDIT.equals(tradeModel.getSysStatus()) || Trade.SYS_STATUS_WAIT_DEST_SEND_GOODS.equals(tradeModel.getSysStatus()))) {
                    tradeModel.setDestSysStatus(TradeStatusUtils.convertSysStatus(gxTrade, tradeConfigService.get(destStaff)));
                    List<String> tradeExceptNames = gxTrade.getExceptNames();
                    if (CollectionUtils.isNotEmpty(tradeExceptNames)) {
                        tradeModel.setDestExceptNames(Strings.join(",", tradeExceptNames));
                    }

                    List<String> oldExceptionRemark = tradeModel.getExceptionRemark();
                    tradeModel.setDestExceptions(ExceptionStatusHelper.analyze(destStaff, gxTrade, tradeModel));
                    // 回复原来的异常备注。
                    tradeModel.setExceptionRemark(oldExceptionRemark);
                }
                //分销订单(发货后，优先使用自己的快递模版，自己没有再展示供销订单的)
                String sysStatus = tradeModel.getSysStatus();
                if (!(Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus) || Trade.SYS_STATUS_FINISHED.equals(sysStatus) || Trade.SYS_STATUS_CLOSED.equals(sysStatus))) {
                    continue;
                }

                if (templateId == null || templateId == 0) {
                    continue;
                }

                tradeModel.setExpressTemplateType(gxTrade.getTemplateType());
                tradeModel.setExpressTemplateName(gxTrade.getTemplateName());
                tradeModel.setIsManulfill(gxTrade.getIsManulfill());
                tradeModel.setLogisticsCompanyId(gxTrade.getLogisticsCompanyId());
                tradeModel.setLogisticsCompanyName(gxTrade.getLogisticsCompanyName());
            }
        });

    }

    /**
     *
     * 分销指定供销模板，分销还没有审核和已审核的情况供销需要显示供销快递模板
     *
     * @param destStaff
     * @param fxTradeModels
     */
    private void handleFxAppointTemplateId(Staff destStaff,  Collection<TradeModel> fxTradeModels){
        List<Trade> needFillTemplateTrades = Lists.newArrayListWithExpectedSize(8);
        List<TradeModel> needFillTemplateTradeModels = fxTradeModels.stream().filter(tradeModel -> TradeUtils.ifContainV(tradeModel.getV(),TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID) &&
                        (Objects.equals(tradeModel.getConvertType(),1) && (Objects.equals(tradeModel.getBelongType(),1)) || Objects.equals(tradeModel.getBelongType(),3)) &&
                        (Trade.SYS_STATUS_WAIT_AUDIT.equals(tradeModel.getOriginSysStatus()) ||Trade.SYS_STATUS_FINISHED_AUDIT.equals(tradeModel.getOriginSysStatus()) || Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(tradeModel.getOriginSysStatus()) || Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(tradeModel.getOriginSysStatus())))
                .collect(toList());

        if(CollectionUtils.isEmpty(needFillTemplateTradeModels)){
            return;
        }
        needFillTemplateTradeModels.forEach(o->{
            Trade trade = new TbTrade();
            trade.setSid(org.apache.commons.lang3.math.NumberUtils.toLong(o.getSid(),0L));
            trade.setTemplateId(o.getExpressTemplateId());
            trade.setTemplateType(o.getExpressTemplateType());
            trade.setLogisticsCompanyId(o.getLogisticsCompanyId());
            needFillTemplateTrades.add(trade);
        });

        try {
            defaultTemplateFilter.filterTrades(destStaff, needFillTemplateTrades);
        } catch (Exception e) {
            Logs.warn(LogHelper.buildErrorLog(destStaff, e, String.format("过滤快递公司出错，sid=%s",TradeUtils.toSidList(needFillTemplateTrades))));
        }

        Map<String, TradeModel> needFillTemplateTradeModelsMap = needFillTemplateTradeModels.stream().collect(Collectors.toMap(TradeModel::getSid,a->a,(a,b)->a));
        needFillTemplateTrades.forEach(t->{
            TradeModel tradeModel = needFillTemplateTradeModelsMap.get(String.valueOf(t.getSid()));
            if(tradeModel == null){
                return;
            }
            tradeModel.setExpressTemplateType(t.getTemplateType());
            tradeModel.setExpressTemplateName(t.getTemplateName());
            tradeModel.setIsManulfill(t.getIsManulfill());
            tradeModel.setLogisticsCompanyId(t.getLogisticsCompanyId());
            tradeModel.setLogisticsCompanyName(t.getLogisticsCompanyName());
        });
    }

    /**
     * 订单详情展示源平台单号
     * @param staff
     * @param tradeModels
     * @param map
     */
    private void fillGxSourceTid(Staff staff, TradeModels tradeModels, Map<Long, Trade> map) {
        try {
            List<TradeModel> modelList = Optional.ofNullable(tradeModels).map(TradeModels::getList).orElse(new ArrayList<>());
            Map<Trade, List<MessageMemo>> needFill = new HashMap<>();
            List<Long> sids = new ArrayList<>();
            for (TradeModel tradeModel : modelList){
                Trade source = map.get(Long.valueOf(tradeModel.getSid()));
                if (!(TradeUtils.isGxOrMixTrade(source) || TradeUtils.isQimenFxSource(source) || StringUtils.isEmpty(source.getSource()) || "qimen".equals(source.getSource()))) {
                    continue;
                }
                Map<String, MessageMemo> messageMemos = tradeModel.getMessageMemos();
                if (MapUtils.isEmpty(messageMemos)){
                    continue;
                }
                messageMemos.forEach((tid, memo) ->{
                    if (Objects.equals(memo.getSid(), source.getSid()) && Objects.nonNull(source.getTradeExt())){
                        memo.setFxPlatformTid(TradeModel.fillTradeExtFxPlatTid(source, Optional.ofNullable(source.getTradeExt()).map(TradeExt::getExtraFields).orElse(null)));
                    }else {
                        needFill.computeIfAbsent(source, t ->new ArrayList<>()).add(memo);
                        sids.add(memo.getSid());
                    }
                });
            }
            if (MapUtils.isNotEmpty(needFill)){
                Map<Long, TradeExt> extMap = tradeExtDao.tradeExtsGetBySids(staff, sids).stream().collect(toMap(TradeExt::getSid, Function.identity(), (k1, k2) ->k1));
                needFill.forEach((source, memos) ->{
                    memos.forEach(messageMemo -> { messageMemo.setFxPlatformTid(TradeModel.fillTradeExtFxPlatTid(source, Optional.ofNullable(extMap.get(messageMemo.getSid())).map(TradeExt::getExtraFields).orElse(null)));
                    });
                });
            }
            fillFxMergePlatformId(staff, modelList, map);
        }catch (Exception e){
            logger.error(LogHelper.buildLog(staff, "[公销业务]供销订单填充原始平台单号报错:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
        }
    }

    private void fillFxMergePlatformId(Staff staff, List<TradeModel> modelList, Map<Long, Trade> map){
        //开启白名单的，查询原始分销订单去获取原始平台单号
        if (!ConfigHolder.FX_GLOBAL_CONFIG.open("gx_platId_merge_key", staff.getCompanyId())){
            return;
        }
        List<Trade> gxTrades = new ArrayList<>();
        List<TradeModel> fill = new ArrayList<>();
        for (TradeModel model : modelList){
            Trade source = map.get(Long.valueOf(model.getSid()));
            if (!TradeUtils.isGxOrMixTrade(source)){
                continue;
            }
            //商品种类小于等于1，分销单肯定不会是合单
            if (TradeUtils.getOrders4Trade(source).size() <= 1){
                continue;
            }
            Map<String, MessageMemo> messageMemos = model.getMessageMemos();
            if (MapUtils.isEmpty(messageMemos)){
                continue;
            }
            gxTrades.add(source);
            fill.add(model);
        }
        Map<Long, String> mergePlatformIdMap = fxBusiness.getPlatformIdContainMergeFromGx(staff, gxTrades);
        if (MapUtils.isEmpty(mergePlatformIdMap)){
            return;
        }
        fill.forEach(model ->{
            model.getMessageMemos().forEach((tid, memo) ->{
                String platformId = mergePlatformIdMap.get(memo.getSid());
                if (StringUtils.isBlank(platformId)){
                    return;
                }
                memo.setFxPlatformTid(platformId);
            });
        });
    }

    protected void handleOrder(TradeModels tradeModels, Integer needOrder, String orderFields, Integer useCompress) {
        tradeModels.setNeedOrder(needOrder == null ? 1 : needOrder);

        if (CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        if (needOrder == null || needOrder == 1) {
            return;
        }

        if (StringUtils.isNotEmpty(orderFields)) {
            copyOrderModelProp(tradeModels, orderFields);
        } else {
            for (TradeModel tradeModel : tradeModels.getList()) {
                tradeModel.setOrders(new ArrayList<>());
            }
        }

        if (useCompress != null && useCompress == 1) {
            List<TradeModel> list = tradeModels.getList();
            if (list.size() > 0 && list.get(0) instanceof  TradeModelJSONField) {
                tradeModels.setFieldMapping(TradeModelJSONField.fieldMapping);
                tradeModels.setMemoMapping(MessageMemoJSONField.fieldMapping);
                tradeModels.setFieldDefaultMap(TradeModelJSONField.fieldDefaultMap);
            }
        }
    }

    private void copyOrderModelProp(TradeModels tradeModels, String orderFields) {
        ArrayList<String> fields = new ArrayList<>(Arrays.asList(orderFields.split(",")));
        fields.add("sysConsigned");
        fields.add("unifiedStatus");
        orderFields = fields.stream().collect(Collectors.joining(","));

        for (TradeModel tradeModel : tradeModels.getList()) {
            List<OrderModel> orderModels = tradeModel.getOrders();
            if (CollectionUtils.isEmpty(orderModels)) {
                continue;
            }

            List<OrderModel> newOrderModels = new ArrayList<>();
            for (OrderModel orderModel : orderModels) {
                newOrderModels.add(OrderModel.copyQueryNeedOrder(orderModel, orderFields));
            }

            tradeModel.setOrders(newOrderModels);
        }
    }

    /**
     * 保存订单商品验货记录
     */
    @Deprecated
    @RequestMapping(value = "/itemPackInfo/save", method = RequestMethod.POST)
    @ResponseBody
    public Object saveTradeItemPackInfo(@RequestBody List<TradeItemPackLog> tradeItemPackLogs) throws Exception {
        Staff staff = getStaff();
        tradeItemPackService.update(staff, tradeItemPackLogs);
        return successResponse();
    }

    /**
     * 部分验货逻辑处理
     *
     * @param staff
     */
    private void buildItemPackInfo(Staff staff, TradeModels models) {
        List<TradeModel> tradeModels = models.getList();
        if (CollectionUtils.isNotEmpty(tradeModels)) {
            for (TradeModel tradeModel : tradeModels) {
                TradeItemPackLog tradeItemPackLog = new TradeItemPackLog();
                tradeItemPackLog.setCompanyId(staff.getCompanyId());
                tradeItemPackLog.setSid(Long.valueOf(tradeModel.getSid()));
                List<TradeItemPackLog> logs = tradeItemPackService.queryById(staff, tradeItemPackLog);
                Map<String, TradeItemPackLog> tradeItemPackLogMap = logs.stream().collect(Collectors.toMap(pack -> (pack.getCompanyId() + "_" + pack.getSid() + "_" + pack.getOrderId() + "_" + pack.getSysItemId() + "_" + pack.getSysSkuId()), java.util.function.Function.identity(), (k1, k2) -> k2));
                List<OrderModel> orderModels = tradeModel.getOrders();
                for (OrderModel orderModel : orderModels) {
                    String key = staff.getCompanyId() + "_" + tradeItemPackLog.getSid() + "_" + orderModel.getId() + "_" + orderModel.getItemSysId() + "_" + orderModel.getSkuSysId();
                    logger.debug(LogHelper.buildLog(staff, "order key：" + key));
                    TradeItemPackLog log = tradeItemPackLogMap.get(key);
                    if (log != null) {
                        if (StringUtils.isNotEmpty(log.getUniqueCodes())) {
                            String[] uniqueCodes = ArrayUtils.toStringArray(log.getUniqueCodes());
                            List<WaveUniqueCode> codes = itemUniqueCodeService.checkAndFilterScanUniqueCodes(staff, Arrays.asList(uniqueCodes));
                            orderModel.setCurrNum(log.getPackNum() - (uniqueCodes.length - codes.size()));
                            orderModel.setUniqueCodes(codes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.joining(",")));
                        } else {
                            orderModel.setCurrNum(log.getPackNum());
                        }
                    } else {
                        orderModel.setCurrNum(0);
                    }
                }
                tradeModel.setCurrNum(orderModels.stream().mapToInt(OrderModel::getCurrNum).sum());
                if ((checkGiftV2HasFeature(staff) ? orderSortServiceMap.get("orderSortV2Service") : orderSortServiceMap.get("orderSortV1Service")).checkHasFeature(staff)) {
                    return;
                }
                orderModels = orderModels.stream().sorted((pre, next) -> {
                    Integer preNum = pre.getNum() - pre.getCurrNum();
                    Integer nextNum = next.getNum() - next.getCurrNum();
                    return nextNum.compareTo(preNum);
                }).collect(Collectors.toList());
                tradeModel.setOrders(orderModels);
            }
        }
    }

    @RequestMapping(value = "/search/mix", method = RequestMethod.GET)
    @ResponseBody
    public Object queryByMixKey(TradeQueryParams params, Integer kind, Boolean force, String weight, Long staffId, String cpCode, String api_name) throws Exception {
        Staff staff = getStaff();
        long queryId = params.getQueryId() != null ? params.getQueryId() : 0L;
        if (params.getSysMask() != 2) {
            params.setSysMask(1);
        }

        if (queryId - SystemTradeQueryParamsContext.QUERY_WAIT_WEIGH == 0
                && ConfigHolder.TRADE_SEND_PACKAGE_INFO_CONFIG.isIndependentQueryCompanyIds(staff.getCompanyId())) {
            return queryForWeigh(params, kind, weight, staffId, cpCode, api_name);
        }

        Trade trade = tradeQueryService.queryByMixKey(staff, params.getMixKey(), true, queryId);

        if (trade == null && Objects.equals(SystemTradeQueryParamsContext.QUERY_WAIT_WEIGH,queryId)
                &&  ConfigHolder.TRADE_SEND_PACKAGE_INFO_CONFIG.isAsRepairOrderCompanyId(staff.getCompanyId())) {
            String mixKey = params.getMixKey();
            AsRepairWeighInfo asInfo = getAsRepairWeighInfo(staff, mixKey);
            if (asInfo != null){
                PageListBase<AsRepairWeighInfo> model = new PageListBase<AsRepairWeighInfo>(){};
                model.setList(Arrays.asList(asInfo));
                model.setTotal(1L);
                return model;
            }
        }


        TradeValidator validator = new TradeValidator();
        validator.setThrowExceptionIfError(false);
        validator.setCpCode(cpCode);
        // 判断订单是否存在

        if (ObjectUtils.isEmpty(trade)) {
            validator.setError(TradeValidator.Error.NOT_FOUND);
            Logs.error(LogHelper.buildLog(staff, validator.getMessage()));
            return ResponseDataWrapperBuilder.build(api_name, 0, validator.getMessage(), null).setSubCode(validator.getCode());
        }
        //扫码打印不需要忽略库存缺货异常
        TradeConfig tradeConfig = null;
        List<Order> closeOrders = new ArrayList<>();
        TradeStaffConfig tradeStaffConfig = tradeStaffConfigService.get(staff);
        WaveConfig waveConfig = waveServiceDubbo.queryWaveConfig(staff);
        if (queryId - SystemTradeQueryParamsContext.QUERY_WAIT_PACK == 0) {
            tradeConfig = tradeConfigService.get(staff);
            boolean supportSellerSend = WaveUtils.supportSellerSend(waveConfig, trade.getSysStatus());
            closeOrders = getCloseOrderList(trade, tradeStaffConfig);
            validator.setIgnoreInsufficient(true);
            validator.setSupportSellerSend(supportSellerSend);
            checkPack(staff, trade, validator, tradeStaffConfig, waveConfig);
            // 商品详情新增拣货货位字段
            fillOrderGoodsSectionCode(staff, Lists.newArrayList(trade), tradeStaffConfig);
            recordFailPackTradeTrace(staff, trade, validator, closeOrders);
        } else if (queryId - SystemTradeQueryParamsContext.QUERY_WAIT_WEIGH == 0) {
            tradeConfig = tradeConfigService.get(staff);
            String outSidByMixKey = getOutSidByMixKey(params.getMixKey());
            String outSid = StringUtils.isEmpty(outSidByMixKey) ? trade.getOutSid() : outSidByMixKey;
            tradeValidateBusiness.checkTradeWeight(staff, trade, validator, tradeConfig, kind, false, outSid, 0);
            if ((kind == null || kind == 0 || kind == 2) && !validator.hasError()) {//发货前称重查询
                filterOrders(trade, false);
            }
            Staff weightStaff = staffAssembleBusiness.getStaffById(staffId);
            String weightStaffName = (weightStaff == null ? staff.getName() : weightStaff.getName());
            if (validator.hasError() && TradeValidator.Error.NOT_FOUND.getCode() != validator.getCode()) {
                String content = "称重失败，原因：" + validator.getMessage() + (validator.getMessage().contains("已称重") ? ("，重量：" + (weight == null ? "无" : (weight + "kg"))) : "") + ";具体称重人:" + weightStaffName;
                List<Trade> o1 = TradeTraceUtils.createTradeS(TradeUtils.toSidList(tradeSearchService.queryBySidsContainMergeTrade(staff, false, false, true, trade.getSid())), content);
                eventCenter.fireEvent(this, new EventInfo("trade.custom.trace.save").setArgs(new Object[]{staff, "称重失败"}),
                        o1);
            }
        } else if (queryId - SystemTradeQueryParamsContext.QUERY_WAIT_PRINT == 0) {//扫码打印
            Trade checkTrade = getPenetrateTrade(trade, validator);
            if (checkTrade != null) {
                tradeValidateBusiness.checkScanPrint(staff, checkTrade, validator, (force != null ? force : false));
                if (!validator.hasError()) {
                    expandSuit(trade);
                }
            }
        } else if (queryId - SystemTradeQueryParamsContext.QUERY_TRANSPOND_PRINT == 0) { // 订单转发
            // 快递转发是否校验禁发规则
            validator.setCheckAddressRule(params.isCheckAddressRule());
            tradeValidateBusiness.checkTranspondPrint(staff, trade, validator);
        }
        if (validator.hasError() && !(TradeValidator.Error.REFUND.getCode() == validator.getCode()
                && queryId - SystemTradeQueryParamsContext.QUERY_WAIT_WEIGH == 0
                && Objects.equals(kind, 1))) {
            Logs.error(LogHelper.buildLog(staff, validator.getMessage()));
            return ResponseDataWrapperBuilder.build(api_name, 0, validator.getMessage(), null).setSubCode(validator.getCode());
        }
        Trades trades = new Trades();
        trades.setTotal(1L).setList(Collections.singletonList(trade));
        tradeFillService.fill(staff, trade);
        tradeFilterService.filterTrade(staff, trade);
        fillLogisticsCompany(staff, trade);
        if (tradeConfig == null) {
            tradeConfig = tradeConfigService.get(staff);
        }
        (checkGiftV2HasFeature(staff) ? orderSortServiceMap.get("orderSortV2Service") : orderSortServiceMap.get("orderSortV1Service")).orderSortByGift(staff, trades.getList());
        TradeModels tradeModels = TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfig);
        handleSFExpressTemplateMark(staff, tradeModels);
        buildItemPackInfo(staff, tradeModels);
        //填充商品唯一码类型字段
        fillUniqueCodeTypeFiled(staff, tradeModels);
        //根据配置填充商品类目名称
        fillItemCatName(staff, tradeModels, params.getCategoryVoice());
        // 填充批次/生产日期
        fillBatchNoAndProductDate(staff, params.getQueryId(), tradeModels);
        fillPackItemInfo(staff, tradeModels, params);
        // 打印已打印的订单时需要提示
        buildHintMessage(staff, trade, waveConfig, tradeModels);
        //根据权限设置字段展示
        handlePrivilegeShow(staff, tradeModels, params);
        // 填充退款子订单信息
        fillCloseOrdersInfo(staff, tradeModels, closeOrders, trade, tradeConfig);
        fillItemBox(staff, tradeStaffConfig, tradeModels);
        //填充波次信息
        fillWaveInfo(staff, tradeModels, params);
        //填充首尾单中间范围数据
        fillMiddleTrades(staff, params.getFirstSid(), tradeModels, tradeConfig);
        fillTipMsg(params, tradeModels, trade, kind);
        fillShipper(staff, tradeModels, params);
        return tradeModels;
    }



    public Object queryForWeigh(TradeQueryParams params, Integer kind,  String weight, Long staffId, String cpCode, String api_name) throws Exception {
        Staff staff = getStaff();
        long queryId = params.getQueryId() != null ? params.getQueryId() : 0L;

        Trade trade = tradeQueryService.queryForWeigh(staff, params.getMixKey(), kind,true,null);

        if (trade == null &&  ConfigHolder.TRADE_SEND_PACKAGE_INFO_CONFIG.isAsRepairOrderCompanyId(staff.getCompanyId())) {
            String mixKey = params.getMixKey();
            AsRepairWeighInfo asInfo = getAsRepairWeighInfo(staff, mixKey);
            if (asInfo != null){
                PageListBase<AsRepairWeighInfo> model = new PageListBase<AsRepairWeighInfo>(){};
                model.setList(Arrays.asList(asInfo));
                model.setTotal(1L);
                return model;
            }
        }

        TradeValidator validator = new TradeValidator();
        validator.setThrowExceptionIfError(false);
        validator.setCpCode(cpCode);
        // 判断订单是否存在

        if (ObjectUtils.isEmpty(trade)) {
            validator.setError(TradeValidator.Error.NOT_FOUND);
            Logs.error(LogHelper.buildLog(staff, validator.getMessage()));
            return ResponseDataWrapperBuilder.build(api_name, 0, validator.getMessage(), null).setSubCode(validator.getCode());
        }


        TradeConfig tradeConfig = tradeConfigService.get(staff);
        String outSidByMixKey = getOutSidByMixKey(params.getMixKey());
        String outSid = StringUtils.isEmpty(outSidByMixKey) ? trade.getOutSid() : outSidByMixKey;
        tradeValidateBusiness.checkTradeWeight(staff, trade, validator, tradeConfig, kind, false, outSid, 0);
        if ((kind == null || kind == 0 || kind == 2) && !validator.hasError()) {//发货前称重查询
            filterOrders(trade, false);
        }
        Staff weightStaff = staffAssembleBusiness.getStaffById(staffId);
        String weightStaffName = (weightStaff == null ? staff.getName() : weightStaff.getName());
        if (validator.hasError() && TradeValidator.Error.NOT_FOUND.getCode() != validator.getCode()) {
            String content = "称重失败，原因：" + validator.getMessage() + (validator.getMessage().contains("已称重") ? ("，重量：" + (weight == null ? "无" : (weight + "kg"))) : "") + ";具体称重人:" + weightStaffName;
            List<Trade> o1 = TradeTraceUtils.createTradeS(TradeUtils.toSidList(tradeSearchService.queryBySidsContainMergeTrade(staff, false, false, true, trade.getSid())), content);
            eventCenter.fireEvent(this, new EventInfo("trade.custom.trace.save").setArgs(new Object[]{staff, "称重失败"}),
                    o1);
        }

        Trades trades = new Trades();
        trades.setTotal(1L).setList(Collections.singletonList(trade));
        fillLogisticsCompany(staff, trade);
        if (tradeConfig == null) {
            tradeConfig = tradeConfigService.get(staff);
        }
        TradeModels tradeModels = TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfig);
        handleSFExpressTemplateMark(staff, tradeModels);
        fillTipMsg(params, tradeModels, trade, kind);
        return tradeModels;

    }

    /**
     * 根据运单号查询售后维修单
     * @param staff
     * @param mixKey
     * @return
     */
    private AsRepairWeighInfo getAsRepairWeighInfo(Staff staff, String mixKey) {
        if (StringUtils.isBlank(mixKey) || !mixKey.contains("_") || mixKey.charAt(mixKey.length() - 2) != '_') {
            return null;
        }
        String[] mixIds = new String[2];
        mixIds[0] = mixKey.substring(0, mixKey.length() - 2);
        mixIds[1] = mixKey.substring(mixKey.length() - 1);

        if (Integer.parseInt(mixIds[1]) != 3) {
            return null;
        }

        List<RepairOrderResponse> response = asRepairService.getWeighingRepairOrderListBySendLogisticsNo(staff, mixIds[0]);
        if (CollectionUtils.isEmpty(response) || response.get(0) == null) {
            return null;
        }

        return AsRepairWeighInfo.convertRepairWeighInfo(staff,response);
    }

    @RequestMapping(value = "/search/batch/mix", method = RequestMethod.GET)
    @ResponseBody
    public Object queryBatchMix(TradeQueryParams params, Integer kind, Boolean force, String weight, Long staffId, String cpCode, String api_name) throws Exception {
        Staff staff = getStaff();
        long queryId = params.getQueryId() != null ? params.getQueryId() : 0L;
        //如果是快递转发，这里初始化一个分页。不然只能返回10条
        if (queryId - SystemTradeQueryParamsContext.QUERY_TRANSPOND_PRINT == 0) {
            Page page = new Page();
            page.setPageSize(Page.MAX_PAGE_SIZE);
            page.setPageNo(Page.DEFAULT_PAGE_NUM);
            params.setPage(page);
        }
        if (params.getSysMask() != 2) {
            params.setSysMask(1);
        }
        Trades trades = tradeSearchService.search(staff, params);
        TradeValidator validator = new TradeValidator();
        validator.setThrowExceptionIfError(false);
        validator.setCpCode(cpCode);
        // 判断订单是否存在
        if (CollectionUtils.isEmpty(trades.getList())) {
            validator.setError(TradeValidator.Error.NOT_FOUND);
            Logs.error(LogHelper.buildLog(staff, validator.getMessage()));
            return ResponseDataWrapperBuilder.build(api_name, 0, validator.getMessage(), null).setSubCode(validator.getCode());
        }
        //扫码打印不需要忽略库存缺货异常
        List<Order> closeOrders = new ArrayList<>();
        TradeStaffConfig tradeStaffConfig = tradeStaffConfigService.get(staff);
        if (queryId - SystemTradeQueryParamsContext.QUERY_TRANSPOND_PRINT == 0) { // 订单转发
            // 快递转发是否校验禁发规则
            validator.setCheckAddressRule(params.isCheckAddressRule());
            List<String> messageList = new ArrayList<>();
            for (Trade trade : trades.getList()) {
                validator.reset();
                tradeValidateBusiness.checkTranspondPrint(staff, trade, validator);
                if (validator.hasError()) {
                    Logs.error(LogHelper.buildLog(staff, validator.getMessage()));
                    String key = trade.getOutSid();
                    if (params.getSid() != null && params.getSid().length > 0) {
                        key = trade.getSid().toString();
                    }
                    if (params.getTid() != null && params.getTid().length > 0) {
                        key = trade.getTid();
                    }
                    if (params.getShortId() != null && params.getShortId().length > 0) {
                        key = trade.getShortId().toString();
                    }
                    messageList.add(key + ":" + validator.getMessage());
                }
            }
            // 多个订单错误用&&分隔
            if (CollectionUtils.isNotEmpty(messageList)) {
                return ResponseDataWrapperBuilder.build(api_name, 0, String.join("&&", messageList), null).setSubCode(validator.getCode());
            }
        }

        TradeConfig tradeConfig = tradeConfigService.get(staff);
        WaveConfig waveConfig = waveServiceDubbo.queryWaveConfig(staff);
        (checkGiftV2HasFeature(staff) ? orderSortServiceMap.get("orderSortV2Service") : orderSortServiceMap.get("orderSortV1Service")).orderSortByGift(staff, trades.getList());
        TradeModels tradeModels = TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfig);
        for (Trade trade : trades.getList()) {
//            tradeFillService.fill(staff, trade);
//            tradeFilterService.filterTrade(staff, trade);
//            fillLogisticsCompany(staff, trade);
//            // 打印已打印的订单时需要提示
            buildHintMessage(staff, trade, waveConfig, tradeModels);
//            // 填充退款子订单信息
//            fillCloseOrdersInfo(staff, tradeModels, closeOrders, trade, tradeConfig);
//            fillTipMsg(params, tradeModels, trade, kind);
        }
//        handleSFExpressTemplateMark(staff, tradeModels);
//        buildItemPackInfo(staff, tradeModels);
//        //填充商品唯一码类型字段
//        fillUniqueCodeTypeFiled(staff, tradeModels);
//        //根据配置填充商品类目名称
//        fillItemCatName(staff, tradeModels, params.getCategoryVoice());
//        // 填充批次/生产日期
//        fillBatchNoAndProductDate(staff, params.getQueryId(), tradeModels);
//        fillPackItemInfo(staff, tradeModels, params);
//        fillItemBox(staff, tradeStaffConfig, tradeModels);
//        //填充波次信息
//        fillWaveInfo(staff, tradeModels, params);
//        //填充首尾单中间范围数据
//        fillMiddleTrades(staff, params.getFirstSid(), tradeModels, tradeConfig);
        return tradeModels;
    }

    private void handlePrivilegeShow(Staff staff, TradeModels tradeModels, TradeQueryParams params) {
        String powerDataPrivilegeSettings = staff.getPowerDataPrivilegeSettings();
        if (!Objects.equals(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_PACK)) {
            return;
        }
        if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        Set<String> dataPrivilegeSettingSet = ArrayUtils.toStringSet(powerDataPrivilegeSettings);
        //数据权限-包装验货-成交金额
        if (CollectionUtils.isEmpty(dataPrivilegeSettingSet) || !dataPrivilegeSettingSet.contains("100322")) {
            for (TradeModel tradeModel : tradeModels.getList()) {
                tradeModel.setPayAmount("***");
                if (CollectionUtils.isEmpty(tradeModel.getOrders())) {
                    continue;
                }
                for (OrderModel order : tradeModel.getOrders()) {
                    order.setAcPayment("***");
                }
            }
        }
    }

    /**
     * 发货后称重 && 合单 && 有退款中的order
     *
     * @param params
     * @param tradeModels
     * @param trade
     * @param kind
     */
    private void fillTipMsg(TradeQueryParams params, TradeModels tradeModels, Trade trade, Integer kind) {
        if (!Objects.equals(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_WEIGH) ||
                !TradeWeightUtils.isAfterConsignWeight(kind) || Objects.isNull(trade) ||
                CollectionUtils.isEmpty(TradeUtils.getOrders4Trade(trade))) {
            return;
        }
        boolean isMerge = TradeUtils.isMerge(trade);
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        Map<String, String> tipMsgMap = new HashMap<>(4);
        for (Order order : orders) {
            if (isMerge && TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) && RefundUtils.isRefundOrder(order)) {
                tipMsgMap.put(String.valueOf(trade.getSid()), TradeValidator.Error.REFUND.getMessage());
                tradeModels.setTipMsgMap(tipMsgMap);
                return;
            } else if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                if (Order.REFUND_SUCCESS.equals(order.getRefundStatus())) {
                    tipMsgMap.put(String.valueOf(trade.getSid()), "包含退款商品");
                    tradeModels.setTipMsgMap(tipMsgMap);
                    break;
                } else if (RefundUtils.isRefundOrder(order)) {
                    tipMsgMap.put(String.valueOf(trade.getSid()), TradeValidator.Error.REFUND.getMessage());
                    tradeModels.setTipMsgMap(tipMsgMap);
                    break;
                }
            }
        }
        for (TradeModel tradeModel : tradeModels.getList()) {
            if (tipMsgMap.containsKey(tradeModel.getSid())) {
                tradeModel.getOrders().sort(Comparator.comparing((Function<OrderModel, Integer>) orderModel -> {
                    int sort = 0;
                    if (Order.NO_REFUND.equals(orderModel.getRefundStatus())) {
                        sort = 0;
                    } else if (RefundUtils.isRefundOrder(orderModel.getRefundStatus())) {
                        sort = 1;
                    } else if (Order.REFUND_SUCCESS.equals(orderModel.getRefundStatus())) {
                        sort = 2;
                    }
                    return sort;
                }).thenComparing(OrderModel::getSid));
            }
        }
    }

    /**
     * 穿透订单获取上游订单
     */
    public Trade getPenetrateTrade(Trade trade, TradeValidator validator) {
        if (trade.isPenetrateTrade()) {
            //穿透面单要做一些校验
            if (trade.getTemplateId() == null || trade.getTemplateId() < 1) {
                validator.setError(30009, "穿透单未选择模板!");
                return null;
            }
            String penetrateCode = trade.getTid();
            String[] data = penetrateCode.split("-");
            Long companyId = Long.valueOf(data[0]);
            Staff staff = staffService.queryFullByCompanyId(companyId);
            if (staff == null) {
                validator.setError(30009, "穿透单未获取到上游账户信息!");
                return null;
            }
            Long sid = Long.valueOf(data[1]);
            Trade fxTrade = tradeSearchService.queryBySid(staff, false, sid);
            if (fxTrade == null) {
                validator.setError(30009, "穿透单未获取到上游订单信息!");
                return null;
            }
            fxTrade.setPenetrateTrade(true);
            return fxTrade;
        }
        return trade;
    }

    private void fillItemBox(Staff staff, TradeStaffConfig tradeStaffConfig, TradeModels tradeModels) {
        //开启扫描箱规码，查询直接返回商品对应的箱规码，后面单独扫码就不用再掉接口查询了
        if (!Objects.equals(tradeStaffConfig.getOpenBoxCodeScan(), CommonConstants.JUDGE_YES)) {
            return;
        }
        List<ItemIdInfo> itemIdInfoList = new ArrayList<>();
        List<TradeModel> list = tradeModels.getList();
        for (TradeModel tradeModel : list) {
            List<OrderModel> orders = tradeModel.getOrders();
            for (OrderModel order : orders) {
                ItemIdInfo itemIdInfo = new ItemIdInfo();
                itemIdInfo.setSysItemId(order.getItemSysId());
                itemIdInfo.setSysSkuId(order.getSkuSysId() < 0L ? 0L : order.getSkuSysId());
                itemIdInfoList.add(itemIdInfo);
            }
        }

        if (CollectionUtils.isEmpty(itemIdInfoList)) {
            return;
        }
        List<ItemBox> itemBoxes = itemsService.queryItemBox(staff, itemIdInfoList);
        if (CollectionUtils.isEmpty(itemBoxes)) {
            return;
        }

        Map<String, List<Map<String, Object>>> itemCodeMap = new HashMap<>();
        for (ItemBox itemBox : itemBoxes) {
            List<ItemBoxSingle> itemBoxSingles = itemBox.getItemBoxSingles();
            List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
            for (ItemBoxSingle single : itemBoxSingles) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("num", single.getNums());
                map.put("outerId", single.getOuterId());
                map.put("sysItemId", single.getSysItemId());
                map.put("sysSkuId", single.getSysSkuId());
                result.add(map);
            }
            itemCodeMap.put(itemBox.getBoxCode(), result);
        }
        tradeModels.setItemCodeMap(itemCodeMap);
    }

    /**
     * 填充快递公司名称
     */
    private void fillLogisticsCompany(Staff staff, Trade trade) {
        if (trade == null || trade.getLogisticsCompanyId() == null || trade.getLogisticsCompanyId() < 1) {
            return;
        }
        UserLogisticsCompany userLogisticsCompany = userLogisticsCompanyService.queryById(staff, trade.getLogisticsCompanyId(), 1);
        if (userLogisticsCompany != null && com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(userLogisticsCompany.getName())) {
            trade.setLogisticsCompanyName(userLogisticsCompany.getName());
        }
    }

    /**
     * 将套件转为普通商品展示
     */
    private void expandSuit(Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        List<Order> result = new ArrayList<>(orders.size());
        for (Order order : orders) {
            if (order.isSuit(false)) {
                if (CollectionUtils.isNotEmpty(order.getSuits())) {
                    for (Order son : order.getSuits()) {
                        son.setIsPick(order.getIsPick());
                        result.add(son);
                    }
                }else {
                    new PaymentLogBuilder(trade.getCompanyId()).appendTrade(trade).appendOrderIdentify(order).append(" 套件商品 但是没有对应的子商品数据").printWarn(logger);
                }
            } else {
                result.add(order);
            }
        }
        TradeUtils.setOrders(trade, result);
    }

    /**
     * 包装验货界面新增商品拣货货位字段，填充订单列表中的子订单的货位编码字段
     *
     * @param trades 需要填充的订单列表
     */
    void fillOrderGoodsSectionCode(Staff staff, List<Trade> trades, TradeStaffConfig tradeStaffConfig) {
        if (CollectionUtils.isEmpty(trades) || Objects.equals(tradeStaffConfig.getOpenSuitselfPack(), 1)) {
            return;
        }

        List<Order> orders = new ArrayList<>();
        trades.forEach(trade -> orders.addAll(((TbTrade) trade).getOrders()));

        List<GoodsSectionOrderRecord> goodsSectionOrderRecords = wmsService.queryGoodsSectionInfoByOrderIds(staff, OrderUtils.toIdList(orders));

        if (CollectionUtils.isEmpty(goodsSectionOrderRecords)) {
            return;
        }

        Map<Long, List<GoodsSectionOrderRecord>> orderIdCodesMap = goodsSectionOrderRecords.stream().collect(groupingBy(GoodsSectionOrderRecord::getOrderId));
        Map<Long, List<String>> codeMap = Maps.newHashMapWithExpectedSize(orderIdCodesMap.size());
        orderIdCodesMap.forEach((k, v) -> {
            codeMap.put(k, v.stream().map(GoodsSectionOrderRecord::getGoodsSectionCode).collect(Collectors.toList()));
        });
        orders.forEach(order -> order.setGoodsSectionCodes(CollectionUtils.isNotEmpty(codeMap.get(order.getId())) ? codeMap.get(order.getId()) : null));
    }

    private void recordFailPackTradeTrace(Staff staff, Trade trade, TradeValidator validator, List<Order> closeOrders) {
        Long sid = trade.getSid();
        String content = null;
        if (validator.hasError() && Objects.equals(validator.getCode(), TradeValidator.Error.REFUND.getCode())) {
            content = "包装验货，订单存在退款异常无法验货";
        }
        if (CollectionUtils.isNotEmpty(closeOrders) && StringUtils.isEmpty(content)) {
            String outerIds = closeOrders.stream().filter(o -> StringUtils.isNotEmpty(o.getSysOuterId())).map(Order::getSysOuterId).distinct().collect(Collectors.joining(","));
            if (StringUtils.isNotEmpty(outerIds)) {
                content = String.format("包装验货，%s商品已退款", outerIds);
            }
        }
        if (StringUtils.isNotEmpty(content)) {
            TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff, sid, null, "tradePackScanFail", content);
            tradeTraceService.batchAddTradeTrace(staff, Lists.newArrayList(tradeTrace));
        }
    }

    private void fillShipper(Staff staff, TradeModels tradeModels, TradeQueryParams params) {
        if (!Objects.equals(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_PACK)) {
            return;
        }
        if (!CompanyUtils.openMultiShipper(staff)) {
            return;
        }
        if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        TradeModel tradeModel = tradeModels.getList().get(0);
        if (tradeModel.getUserId() == null) {
            return;
        }
        List<User> userList = shipperDubbo.queryByUserId(staff, Lists.newArrayList(tradeModel.getUserId()));
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        WaveAssembleInfo waveAssembleInfo = tradeModel.getWaveAssembleInfo() == null ? new WaveAssembleInfo() : tradeModel.getWaveAssembleInfo();
        waveAssembleInfo.setShipperName(userList.get(0).getShipperName());
        waveAssembleInfo.setShipperId(userList.get(0).getShipperId());
        tradeModel.setWaveAssembleInfo(waveAssembleInfo);
    }

    private void fillShipperBatch(Staff staff, TradeModels tradeModels, TradeQueryParams params) {
        try {
            if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
                return;
            }
            if (!Objects.equals(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_PACK)) {
                return;
            }
            if (!CompanyUtils.openMultiShipper(staff)) {
                return;
            }
            Set<Long> userIdSet = tradeModels.getList().stream().filter(t -> t.getUserId() != null).map(TradeModel::getUserId).collect(Collectors.toSet());
            List<User> userList = shipperDubbo.queryByUserId(staff, Lists.newArrayList(userIdSet));
            if (CollectionUtils.isEmpty(userList)) {
                return;
            }
            Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, u -> u, (k1, k2) -> k1));

            for (TradeModel tradeModel : tradeModels.getList()) {
                Long userId = tradeModel.getUserId();
                if (userId == null) {
                    continue;
                }
                User user = userMap.get(userId);
                if (user == null) {
                    continue;
                }
                WaveAssembleInfo waveAssembleInfo = tradeModel.getWaveAssembleInfo() == null ? new WaveAssembleInfo() : tradeModel.getWaveAssembleInfo();
                waveAssembleInfo.setShipperName(user.getShipperName());
                waveAssembleInfo.setShipperId(user.getShipperId());
                tradeModel.setWaveAssembleInfo(waveAssembleInfo);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "填充订单货主信息失败"), e);
        }
    }

    private void fillWaveInfo(Staff staff, TradeModels tradeModels, TradeQueryParams params) {
        if (!Objects.equals(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_PACK)) {
            return;
        }
        if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        List<TradeModel> waveTradeModelList = tradeModels.getList().stream().filter(t -> t.getWaveId() != null && t.getWaveId() > 0L).collect(toList());
        if (CollectionUtils.isEmpty(waveTradeModelList)) {
            return;
        }
        List<Wave> waves = tradeWaveService.queryWaveByIds(staff, waveTradeModelList.stream().map(TradeModel::getWaveId).toArray(Long[]::new));
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }
        Map<Long, Wave> waveMap = waves.stream().collect(Collectors.toMap(Wave::getId, w -> w, (k1, k2) -> k1));
        for (TradeModel tradeModel : waveTradeModelList) {
            Wave wave = waveMap.get(tradeModel.getWaveId());
            if (wave == null) {
                continue;
            }
            WaveAssembleInfo waveAssembleInfo = tradeModel.getWaveAssembleInfo() == null ? new WaveAssembleInfo() : tradeModel.getWaveAssembleInfo();
            if (StringUtils.isEmpty(wave.getRuleName())) {
                WaveRuleType waveRuleType = WaveRuleType.parseRuleId(wave.getRuleId());
                if (waveRuleType != WaveRuleType.TRADE) {
                    wave.setRuleName(waveRuleType.getName());
                }
                WaveUtils.fillCheckedTradeWaveRuleName(wave, waveRuleType);
            }
            waveAssembleInfo.setWaveRuleName(wave.getRuleName());
            tradeModel.setWaveAssembleInfo(waveAssembleInfo);
        }
    }

    /**
     * 快递模版网点业务类型，顺丰处理，对应mark字段：
     * 1   顺丰标快
     * 208 特惠专配
     * 242 丰网速运
     *
     * @param staff
     * @param tradeModels
     */
    private void handleSFExpressTemplateMark(Staff staff, TradeModels tradeModels) {
        List<TradeModel> list = tradeModels.getList();
        list.forEach(tradeModel -> {
            if (null != tradeModel.getExpressTemplateId() && tradeModel.getExpressTemplateId() >= 0L
                    && null != tradeModel.getExpressCode() && "SF".equals(tradeModel.getExpressCode())) {
                TemplateBranch templateBranch = iExpressTemplateDubboService.queryTemplateBranch(staff, tradeModel.getWarehouseId(), tradeModel.getExpressTemplateId(), tradeModel.getExpressTemplateType());
                if (templateBranch != null) {
                    tradeModel.setExpressTemplateMark(templateBranch.getMark());
                } else {
                    tradeModel.setExpressTemplateMark("1");
                }
            }
        });
    }

    private void checkPack(Staff staff, Trade trade, TradeValidator validator, TradeStaffConfig tradeStaffConfig, WaveConfig waveConfig) {
        tradeValidateBusiness.checkPack(staff, trade, validator, Objects.equals(waveConfig.getInteger(WaveChatConfigsEnum.ALLOW_UNPRINT_PACK.getKey()), 1));
        tradeWaveService.checkTradePickStatus(staff, trade, waveConfig, validator);
        if (!validator.hasError()) {
            filterOrders(trade, Objects.equals(tradeStaffConfig.getOpenSuitselfPack(), 1), validator.isSupportSellerSend());
            setBarcode(staff, trade);
        }
    }

    private void checkBatchPack(Staff staff, Trade trade, TradeValidator validator, TradeStaffConfig tradeStaffConfig) {
        tradeValidateBusiness.checkPackUnCancelInsufficient(staff, trade, validator);
        if (!validator.hasError()) {
            filterOrders(trade, Objects.equals(tradeStaffConfig.getOpenSuitselfPack(), 1));
        }
    }

    private void filterOrders(Trade trade, boolean showSuitSelf) {
        filterOrders(trade, showSuitSelf, false);
    }

    private void filterOrders(Trade trade, boolean showSuitSelf, boolean supportSellerSend) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        List<Order> result = new ArrayList<>(orders.size());
        for (Order order : orders) {
            if (!OrderUtils.isVirtualOrNonConsign(order) && checkSysStatus(order, supportSellerSend)) {
                //套件本身
                if (order.isSuit(false)) {
                    if (showSuitSelf) {
                        //包装验货扫描套件显示套件本身信息
                        result.add(order);
                    } else {
                        for (Order son : order.getSuits()) {
                            OrderUtils.buildPickCheckFlag(son);
                            fillSuitInfo(son, order);
                            result.add(son);
                        }
                    }
                } else {
                    //单品 或者其他商品
                    result.add(order);
                }
                OrderUtils.buildPickCheckFlag(order);
            }
        }
        TradeUtils.setOrders(trade, result);
    }

    private void fillSuitInfo(Order son, Order order) {
        son.setCombineSysTitle(order.getSysTitle());//设置套件本身的名称
        son.setItemSuitOuterId(order.getSysOuterId());//设置套件本身的商家编码
        son.setCombinePayment(order.getPayment());
    }

    private boolean checkSysStatus(Order order, boolean supportSellerSend) {
        if (supportSellerSend) {
            return TradeStatusUtils.isWaitSellerSend(order.getSysStatus()) || Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(order.getSysStatus()) || Trade.SYS_STATUS_FINISHED.equals(order.getSysStatus());
        } else {
            return TradeStatusUtils.isWaitSellerSend(order.getSysStatus());
        }
    }

    /**
     * 订单数量统计统一接口
     */
    @RequestMapping(value = "/count", method = RequestMethod.POST)
    @ResponseBody
    public Object count(TradeControllerParams queryParams, String queryIds, String api_name) throws Exception {
        Staff staff = getStaff();
        return tradeQueryService.cachedCount(staff, new TradeCountParams.Builder().staff(staff).queryParams(queryParams).queryIds(queryIds).build());
    }

    /**
     * 首页数据手动刷新
     */
    @RequestMapping(value = "/count/refresh", method = RequestMethod.POST)
    @ResponseBody
    public Object refresh(TradeControllerParams queryParams, String queryIds, String api_name) throws Exception {
        Staff staff = getStaff();
        return tradeQueryService.count(new TradeCountParams.Builder().staff(staff).queryParams(queryParams).queryIds(queryIds).build(), true);
    }

    /**
     * 订单数量统计查询
     */
    @RequestMapping(value = "/queryCount", method = RequestMethod.GET)
    @ResponseBody
    public Object queryCount(String queryIds, String api_name, Integer isExcep) throws SessionException {
        Staff staff = getStaff();
        String[] queryIdArr = queryIds.split(",");
        Map<String, Object> result = new HashMap<>(6);
        // 兼容新增的接口，默认就是不查询异常
        Integer isExcepReal = isExcep == null ? 0 : isExcep;
        for (String queryId : queryIdArr) {
            int qid = Integer.parseInt(queryId);
            if (20 == qid) {//待付款
                result.put("waitPayNum", tradeCountService.countWaitPay(staff));
            } else if (21 == qid) {//待审核
                result.put("waitAuditNum", tradeCountService.countWaitAudit(staff, isExcepReal));
            } else if (22 == qid) {//已审核
                result.put("finishAuditNum", tradeCountService.countFinishAudit(staff));
            } else if (23 == qid) {//异常订单
                result.put("excepNum", tradeCountService.countExcep(staff));
            } else if (25 == qid) {//未分配快递
                result.put("waitSetTemplateNum", tradeCountService.countWaitSetTemplate(staff));
            } else if (26 == qid) {//快递单未打印
                result.put("waitPrintNum", tradeCountService.countWaitPrint(staff));
            } else if (29 == qid) {//已发货
                result.put("consignedNum", tradeCountService.countFinishConsign(staff));
            } else if (30 == qid) {//已完成
                result.put("finishedNum", tradeCountService.countFinished(staff));
            } else if (33 == qid) {//待包装
                result.put("waitPackNum", tradeCountService.countWaitPack(staff, tradeConfigService.get(staff), true));
            } else if (34 == qid) {//待称重
                result.put("waitWeighNum", tradeCountService.countWaitWeigh(staff, tradeConfigService.get(staff), true));
            } else if (35 == qid) {//待发货
                result.put("waitConsignNum", tradeCountService.countWaitConsign(staff, tradeConfigService.get(staff), isExcepReal));
            } else if (50 == qid) {//待出库单
                result.put("waitOutNum", tradeCountService.countWaitOut(staff));
            } else if (51 == qid) {//已出库单
                result.put("alreadyOutNum", tradeCountService.countFinishOut(staff));
            } else if (60 == qid) {//预售
                result.put("presellNum", tradeCountService.countPresell(staff));
            } else if (62 == qid) {//待发货订单
                result.put("unConsignedNum", tradeCountService.countUnConsigned(staff));
            }
        }
        return result;
    }

    /**
     * 查看订单详情
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ResponseBody
    @Cacheable(value = "defaultCache#5", key = "'trade_detail_' + #tid + '_' + #sid")
    public Object detail(Long sid, String tid, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入sid参数");
        Staff staff = getStaff();
        Trade tbTrade = tradeSearchService.queryBySid(staff, true, 0, sid);

        Boolean ifArchived = null;
        // 用三个月前的数据去兜底。
        if (Objects.isNull(tbTrade)) {
            TradeQueryParams queryParams = new TradeQueryParams();
            queryParams.setSid(sid).setQueryId(QUERY_3MONTH_AGO)
                    .setPage(new Page(1, 1)).setNeedOrder(1);
            queryParams.setDirectQueryType(0);
            Trades trades = tradeDataService.search3Month(staff, queryParams, false);
            List<Trade> tradesList = trades.getList();
            if (CollectionUtils.isNotEmpty(tradesList)) {
                tbTrade = tradesList.get(0);
                ifArchived = Boolean.TRUE;
            }
        }
        Assert.notNull(tbTrade, "此订单查找不到");
        List<Trade> trades = Lists.newArrayList(tbTrade);
        Map<Long, TradeOriginReceiverInfos> tradeOriginReceiverInfos = getTradeOriginReceiverInfos(trades);

        trades = tradePddTradeBusiness.pddMaskDataReplace(staff, trades);
        fxgTradeDecryptBusiness.batchSensitive(staff, trades);
        commonTradeDecryptBusiness.batchSensitive(staff, trades);
        TradeSysDigestUtils.batchSensitive(staff, trades);

        TradeModel tm;
        if (!(tbTrade.isOutstock() || TradeUtils.isGxOrMixTrade(tbTrade))) {//非出库单
            User user = staff.getUserIdMap().get(tbTrade.getUserId());
            Assert.notNull(user, "查找不到此订单对应的平台用户");
            // 先查询几个基本的订单数据
            Shop shop = shopService.queryByUserId(staff, tbTrade.getUserId());
            ExpressCompany expressCompany = (null != tbTrade.getExpressCompanyId()) ? expressCompanyService.getExpressCompanyById(tbTrade.getExpressCompanyId()) : null;
            tm = TradeModel.toDetailTradeModel(user, tbTrade, shop, expressCompany, tbTradeAccess, tradeConfigService.get(staff));
        } else {
            ExpressCompany expressCompany = (null != tbTrade.getExpressCompanyId()) ? expressCompanyService.getExpressCompanyById(tbTrade.getExpressCompanyId()) : null;
            tm = TradeModel.toDetailTradeOutModel(staff, tbTrade, expressCompany, tradeConfigService.get(staff));
            handleNewFxTrade(staff, tm);
        }

        fillTradeTrace(staff, tm, tbTrade.getSid(),false);
        if (!tradeLocalConfigurable.isTradeSearchNotFillStock(staff.getCompanyId())) {
            stockInfoModel(staff, tm);
            orderStockNumBusiness.fillStockNum(staff, Collections.singletonList(tm));
        }
        tm.setIfArchived(ifArchived);

        TradeModels tradeModels = new TradeModels();
        tradeModels.setList(Collections.singletonList(tm));
        handleVipJit(staff, tradeModels);
        //支付单信息
        try {
            if (tradePayBusiness.isOpenTradePay(staff)) {
                TradePayDetail tradePayDetail = tradePayBusiness.tradePayGet(staff, tbTrade);
                tm.setTradePurchaseAmount(tradePayDetail.getTradePurchaseAmount().doubleValue());
                tm.setTradePays(tradePayDetail.getTradePays());
                tm.setManualPaymentAmount(tradePayDetail.getManualPaymentAmount().doubleValue());
                tm.setPlatformPaymentAmount(tradePayDetail.getPlatformPaymentAmount().doubleValue());
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, ""), e);
        }
        queryFillBusiness.fillTradeInfos(staff, Lists.newArrayList(tm),null, Collections.singletonList(tbTrade),InfoLevelEnum.DETAIL);
        fillTradeOriginReceiverInfos(tradeOriginReceiverInfos,tm);
        return tm;
    }

    /**
     * 统计模版后面的数量
     */
    @RequestMapping(value = "/queryTemplateNum", method = RequestMethod.POST)
    @ResponseBody
    public Object queryTemplateNum(TradeControllerParams queryParams) throws SessionException {
        try {
            Staff staff = getStaff();
            return queryTemplateNum(staff, queryParams);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Maps.newHashMap();
    }

    @Cacheable(value = "defaultCache#600", key = "'template_num_'+ #staff.companyId + '_' + #queryParams.queryId")
    public Object queryTemplateNum(Staff staff, TradeControllerParams queryParams) {
        TradeQueryParams params = TradeQueryParams.copyParams(queryParams);
        TradeConfig tradeConfig = tradeConfigService.get(staff);


        params.setUseNewQuery(tradeConfig.getUseNewQuery());//使用新的query方式
        params.setAllowedPgl(true);
        params.setQueryOrder(false);
        params.setQueryId(org.apache.commons.lang3.ObjectUtils.defaultIfNull(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_PRINT));
        Page page = new Page().setPageSize(500);
        params.setPage(page);
        params.setHasSqlGroupBy(Boolean.TRUE);
        params.setBreakQuery(true);
        params.setNeedFill(false);
        params.setUseHasNext(1);
        boolean hasPrintTemplateIntegrate = featureService.checkHasFeature(staff.getCompanyId(), Feature.PRINT_TEMPLATE_INTEGRATE);
        if (hasPrintTemplateIntegrate) {
            logger.debug(LogHelper.buildLog(staff, "订单模板统计，开启[printTemplateIntegrate]显示快递公司名称维度白名单").toString());
            params.setFields("t.sid,t.logistics_company_id,count(*) item_kind_num ");
            params.setCustomWhere("t.logistics_company_id > 0) group by (t.logistics_company_id ");
        } else {
            params.setFields("t.sid,concat(if(t.template_type=1,'w',''),t.template_id) ac_payment,count(*) item_kind_num ");
            params.setCustomWhere("t.template_id > 0) group by concat(if(t.template_type=1,'w',''),t.template_id ");
        }

        Trades tradeSearch = tradeSearchService.search(staff, params);
        List<Trade> trades = tradeSearch.getList();
        if (CollectionUtils.isEmpty(trades)) {
            return Maps.newHashMap();
        }
        Map<String, Integer> templateMap = new HashMap<>();
        if (hasPrintTemplateIntegrate) {
            for (Trade trade : trades) {
                templateMap.put(String.valueOf(trade.getLogisticsCompanyId()), trade.getItemKindNum());
            }
        } else {
            for (Trade trade : trades) {
                templateMap.put(trade.getAcPayment(), trade.getItemKindNum());
            }
        }
        if (MapUtils.isEmpty(templateMap)) {
            return Maps.newHashMap();
        }
        return templateMap;
    }

    /**
     * 查看订单操作记录
     */
    @RequestMapping(value = "/trace/detail", method = RequestMethod.GET)
    @ResponseBody
    @Cacheable(value = "defaultCache#5", key = "'trade_trace_detail_' + #taobaoId + '_' + #tid + '_' + #sid")
    public Object traceDetail(Long sid, String tid, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入sid参数");
        Staff staff = getStaff();
        TradeModel tm = new TradeModel();
        tm.setSid(sid + "");
        tm.setTid(tid);
        fillTradeTrace(staff, tm, sid,false);
        return tm;
    }

    /**
     * 查看订单修改操作记录
     */
    @RequestMapping(value = "/trace/itemModify/detail", method = RequestMethod.GET)
    @ResponseBody
    @Cacheable(value = "defaultCache#5", key = "'trade_trace_item_modify_detail_' + #taobaoId + '_' + #tid + '_' + #sid")
    public Object tradeItemModifyTraceDetail(Long sid, String tid, String api_name) throws SessionException {
        Assert.notNull(sid, "请输入sid参数");
        Staff staff = getStaff();
        TradeModel tm = new TradeModel();
        tm.setSid(sid + "");
        tm.setTid(tid);
        fillTradeTrace(staff, tm, sid,true);
        return tm;
    }


    @RequestMapping(value = "/deleted/query", method = RequestMethod.GET)
    @ResponseBody
    public Object queryDeletedTrades(Long startTime, Long endTime, Page page, String api_name) throws Exception {
        Staff staff = getStaff();
        if (page == null) {
            page = new Page();
        }
        if (page.getPageSize() > 10000) {
            throw new IllegalArgumentException("pageSize must be less equal than 10000");
        }
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("startTime and endTime must not be null");
        }
        if (endTime - startTime <= 0) {
            throw new IllegalArgumentException("startTime must be less than endTime");
        }
        StringBuilder sql = new StringBuilder("SELECT sid,tid FROM trade_");
        sql.append(staff.getDbInfo().getTradeDbNo()).append(" WHERE company_id = ? AND upd_time >= ? AND upd_time <= ? AND enable_status = 0 LIMIT ?, ?");
        return jdbcTemplateAdapter.get(staff).query(sql.toString(), new BeanPropertyRowMapper<TbTrade>(TbTrade.class), staff.getCompanyId(), new Date(startTime), new Date(endTime), page.getStartRow(), page.getPageSize());
    }


    @RequestMapping(value = "/receiver/detail", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @Cacheable(value = "defaultCache#5", key = "'trade_receiver_detail_' + #tid")
    public Object detail(String tid, String api_name) throws SessionException {
        Assert.notNull(tid, "请输入tid参数");
        Staff staff = getStaff();
        List<TbTrade> tbTrades = tradeSearchService.queryByTids(staff, false, tid);
        TbTrade tbTrade = null;
        if (CollectionUtils.isNotEmpty(tbTrades)) {
            tbTrade = tbTrades.get(0);
        }
        Assert.notNull(tbTrade, "此订单查找不到");

        TradeModel tm = new TradeModel();
        tm.setReceiverMobile(tbTrade.getReceiverMobile()).setReceiverName(tbTrade.getReceiverName()).
                setReceiverPhone(tbTrade.getReceiverPhone()).setReceiverAddress(tbTrade.getReceiverAddress());
        tm.setTaobaoId(tbTrade.getTaobaoId());
        return tm;
    }

    private void fillContainsSuitSingle(TradeQueryParams params) {
        if (params.getContainsSuitSingle() != null) {
            return;
        }
        Long queryId = params.getQueryId();
        if (queryId != null && (queryId - QUERY_WAIT_PACK != 0 && queryId - QUERY_WAIT_WEIGH != 0 && queryId - QUERY_BOXING != 0)) {
            params.setContainsSuitSingle(false);
        }
    }

    protected void filterVirtualItemSourceAndDest(Staff staff, List<Trade> trades, TradeQueryParams params) {
        try {
            if (params.getQueryOrder() != null && !params.getQueryOrder()) {
                if (params.getQueryId() == null || params.getQueryId() - QUERY_BOXING != 0) {//目前只过滤装箱清单查询的订单
                    return;
                }
                if (trades == null || trades.isEmpty()) {
                    return;
                }
                tradeQueryService.fillDestAndSourceName(trades);
                for (Trade trade : trades) {
                    List<Order> orders = TradeUtils.getOrders4Trade(trade);
                    orders.removeIf(order -> order.getIsVirtual() - 1 == 0);
                    //去除套件本身
                    TradeUtils.setOrders(trade, OrderUtils.toFullOrderList(orders, true));
                }
            } else {
                if (CollectionUtils.isNotEmpty(trades)) {
                    tradeQueryService.fillDestAndSourceName(trades);
                }
            }
        } catch (Throwable e) {
            logger.error(LogHelper.buildLog(staff, "填充分销商名称，供销商名称失败:").append(e.getMessage()), StackTracesUtils.filterStackTraces(e));
        }


    }


    private List<TradeTrace> filterNullString(Staff staff, List<TradeTrace> list,boolean itemModifyLog) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        Iterator<TradeTrace> iterator = list.iterator();
        while (iterator.hasNext()) {
            TradeTrace trace = iterator.next();
            if (null != trace.getAction() && trace.getAction().contains("null")) {
                trace.setAction(trace.getAction().replaceAll("null", ""));
            }
            //KMERP-99955
            if (null != trace.getContent() && trace.getContent().indexOf("\\u") > 0) {
                trace.setContent(Strings.regainUtf8mb4(trace.getContent()));
            }
            //权限过滤
            if (null != trace.getContent()) {
                Set<String> opSet = new HashSet<>();
                opSet.add(TRADE_REFRESH_FX_CASH_FLOW.getName());
                opSet.add(TRADE_REFRESH_FX_PRICE.getName());
                opSet.add(TRADE_PAY_DISTRIBUTOR.getName());
                boolean hasPriceDataPrivilege = StringUtils.contains(staff.getPowerDataPrivilegeSettings(), "1000301");//商品单价
                boolean hasPaymentDataPrivilege = StringUtils.contains(staff.getPowerDataPrivilegeSettings(), "1000302");//订单应付金额
                boolean hasFxPriceDataPrivilege = StringUtils.contains(staff.getPowerDataPrivilegeSettings(), "1000309");//商品分销价
                if (opSet.contains(trace.getAction())) {
                    boolean hasDataPrivilege = StringUtils.contains(staff.getPowerDataPrivilegeSettings(), "1000309");
                    if (!hasDataPrivilege) {
                        trace.setContent(null != trace.getContent() ? trace.getContent()
                                .replaceAll("(?<=由)\\d+(\\.\\d+)?", "***")
                                .replaceAll("(?<=为)\\d+(\\.\\d+)?", "***")
                                : trace.getContent());
                    }
                } else if ("修改理论运费".equals(trace.getAction())
                        || "修改实际运费".equals(trace.getAction())) {
                    boolean hasDataPrivilege = StringUtils.contains(staff.getPowerDataPrivilegeSettings(), "1000313");
                    if (!hasDataPrivilege) {
                        trace.setContent(null != trace.getContent() ? trace.getContent()
                                .replaceAll("(?:[-+]?\\d+\\.?\\d+|\\b\\d+\\b)", "***") : trace.getContent());
                    }
                } else if ("重复称重".equals(trace.getAction())
                        || "包裹称重".equals(trace.getAction())) {
                    boolean hasDataPrivilege = StringUtils.contains(staff.getPowerDataPrivilegeSettings(), "1000313");
                    if (!hasDataPrivilege) {
                        trace.setContent(null != trace.getContent() ? trace.getContent()
                                .replaceFirst("(?<=实际运费)\\d+", "***") : trace.getContent());
                    }
                } else if (SUITE2SINGLE_PAYMENT_SHARE.getName().equals(trace.getAction())
                        || COMBINE2SINGLE_PAYMENT_SHARE.getName().equals(trace.getAction())
                        || PROCESS2SINGLE_PAYMENT_SHARE.getName().equals(trace.getAction())) {
                    if (!hasPriceDataPrivilege) {
                        //隐藏单价
                        trace.setContent(replaceBracket(trace.getContent(), 1));
                    }
                    if (!hasPaymentDataPrivilege) {
                        //隐藏实付
                        trace.setContent(replaceBracket(trace.getContent(), 2));
                    }
                } else if (!itemModifyLog && (TRADE_ADD.getName().equals(trace.getAction())
                        || TRADE_UPDATE.getName().equals(trace.getAction())
                        || ITEM_ADD.getName().equals(trace.getAction())
                        || ITEM_CHANGE.getName().equals(trace.getAction())
                        || ITEM_REPLACE_BY_FORMULA.getName().equals(trace.getAction())
                        || ITEM_REPLACE.getName().equals(trace.getAction()))
                ) {
                    if (null != trace.getContent()) {
                        String content = trace.getContent();
                        if (!hasPriceDataPrivilege) {
                            content = content.replaceAll("(?<=单价:)\\d+(\\.\\d+)?", "***");
                            content = content.replaceAll("(?<=价格:)\\d+(\\.\\d+)?", "***");
                        }
                        if (!hasPaymentDataPrivilege) {
                            content = content.replaceAll("(实付:|->)(\\d+\\.\\d{2})", "$1***")
                                    .replaceAll("(?<=实付: )\\d+(\\.\\d+)?", "***")
                                    .replaceAll("(?<=实付:)\\d+(\\.\\d+)?", "***")
                                    .replaceAll("(?<=金额：)\\d+(\\.\\d+)?", "***")
                                    .replaceAll("实付:null", "实付:***");
                        }
                        if (!hasFxPriceDataPrivilege) {
                            content = content.replaceAll("(?<=分销金额:)\\d+(\\.\\d+)?", "***");
                        }
                        trace.setContent(content);
                    }
                } else if (ITEM_RELATION_BIND.getName().equals(trace.getAction())) {//KMERP-257873
                    replaceItemMatchContent(staff, trace);
                }
                if (itemModifyLog) {
                    if (TRADE_UPDATE.getName().equals(trace.getAction())
                            || ITEM_ADD.getName().equals(trace.getAction())
                            || ITEM_CHANGE.getName().equals(trace.getAction())) {
                        String content = trace.getContent();
                        switch (trace.getAction()) {
                            case "更新订单":
                                content = Arrays.stream(content.split(";"))
                                        .map(String::trim)
                                        .filter(StringUtils::isNotEmpty)
                                        .map(s -> {
                                            if (s.contains("新增商品")) {
                                                return addItem(s);
                                            } else if (s.contains("换商品")) {
                                                return exchangeItem(s);
                                            } else if (s.contains("删除商品")) {
                                                return deleteItem(s);
                                            } else {
                                                return "";
                                            }
                                        })
                                        .filter(StringUtils::isNotEmpty)
                                        .collect(Collectors.joining("\r\n"));
                                break;
                            case "添加商品":
                                content = addItem(content);
                                break;
                            case "更换商品":
                                content = fastReplace(content);
                                break;
                        }
                        if (StringUtils.isBlank(content)) {
                            iterator.remove();
                        } else {
                            trace.setContent(content);
                        }
                    } else {
                        iterator.remove();
                    }
                }
            }
        }
        return list;
    }

    private static String deleteItem(String input) {
        List<String> results = new ArrayList<>();
        Pattern pattern = Pattern.compile("删除商品:\\s*([^;]+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String productsStr = matcher.group(1).trim();
            String[] products = productsStr.split("\\s*,\\s*");
            for (String product : products) {
                results.add("商品编码:"+product.trim());
            }
        }
        return "删除商品:\n"+StringUtils.join(results, ";\r\n")+";";
    }

    private static String exchangeItem(String input) {
        String regex = "\\{(平台商家编码|商家编码):\\s*([^},]+).*?->\\{商家编码:\\s*([^},]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        List<String> results = new ArrayList<>();
        while (matcher.find()) {
            String leftKey = matcher.group(1);
            String leftValue = matcher.group(2).trim();
            String rightValue = matcher.group(3).trim();
            results.add(String.format("%s:%s->商家编码:%s", leftKey, leftValue, rightValue));
        }
        return CollectionUtils.isEmpty(results) ? "" : "换商品:\r\n" + StringUtils.join(results, ";\r\n") + ";";
    }

    private static String addItem(String input) {
        String operationType = input.contains("新增商品") ? "新增商品" : "添加商品";
        Pattern pattern = Pattern.compile("商家编码\\s*[:：]\\s*(\\d+)");
        if ("新增商品".equals(operationType)) {
            pattern = Pattern.compile("商家编码:\\s*([^,}]+)");
        }
        Matcher matcher = pattern.matcher(input);
        List<String> codes = new ArrayList<>();
        while (matcher.find()) {
            codes.add("商家编码:" + matcher.group(1));
        }
        if (codes.isEmpty()) {
            return "";
        }
        StringJoiner codeJoiner = new StringJoiner(";\r\n");
        codes.forEach(codeJoiner::add);
        return String.format("%s:\r\n%s;",
                operationType,
                codeJoiner.toString());
    }

    private static String fastReplace(String input)
    {
        String regex = "快速换商品:\\s*(\\S+)\\s*->\\s*(\\S+)(,.*)";
        String replacement = "快速换商品:\r\n商家编码:$1 -> 商家编码:$2;";
        return input.replaceAll(regex,replacement);
    }

    /**
     * 替换(1,2,3)成(***,***,***)
     * index:0
     *
     * @param input
     * @param index
     * @return
     */
    String replaceBracket(String input, int index) {
        StringBuilder sb = new StringBuilder();
        String regex = "\\(([^\\)]*)\\)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        int lastEnd = 0;
        while (matcher.find()) {
            sb.append(input, lastEnd, matcher.start());
            String bracketContent = matcher.group(1);
            String[] parts = bracketContent.split(",\\s*");
            if (parts.length > index) {
                parts[index] = "***";
                sb.append("(").append(String.join(", ", parts)).append(")");
            } else {
                sb.append(matcher.group());
            }
            lastEnd = matcher.end();
        }
        sb.append(input.substring(lastEnd));
        return sb.toString();
    }

    @RequestMapping(value = "/stat", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object count(String api_name,Integer statDestStockInsufficient) throws Exception {
        Staff staff = getStaff();
        TradeCount tradeCount = new TradeCount();
        tradeCount.setStatDestStockInsufficient(statDestStockInsufficient);
        return tradeStatCountBusiness.count(staff,tradeCount);
    }

    @RequestMapping(value = "/stat/print", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object countPrint(String api_name) throws Exception {
        Staff staff = getStaff();
        return tradeStatCountBusiness.countPrint(staff);
    }

    @RequestMapping(value = "/stat/pg", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object countPg(String api_name) {
        return tradeStatCountBusiness.countPgTest();
    }

    @RequestMapping(value = "/query/useWave", method = RequestMethod.GET)
    @ResponseBody
    public Object queryByWave(Long waveId, Integer pageNo, Integer pageSize) throws SessionException {
        Staff staff = getStaff();
        Page page = new Page();
        Wave wave = tradeWaveService.queryWaveById(staff, waveId);
        Assert.notNull(wave, "该波次不存在");
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        TradeQueryParams params = new TradeQueryParams();
        params.setWaveId(waveId);
        params.setQueryId(62L);
        params.setWarehouseIds(wave.getWarehouseId());
        params.setPage(page);
        params.setAssignTrade(true);
        params.setSysMask(1);
        Trades trades = tradeSearchService.search(staff, params);
        Integer pickingType = wave.getPickingType() != null ? wave.getPickingType() : 0;
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("waveId", waveId);
        resultMap.put("trades", trades);
        resultMap.put("pickingType", pickingType);
        resultMap.put("pickingName", PickingType.parseType(pickingType).getName());
        return resultMap;
    }

    /**
     * 是否是排除商家ID查询白名单
     */
    @RequestMapping(value = "/isWhitelist/excludeOuterId", method = RequestMethod.POST)
    @ResponseBody
    public Object isWhitelistOfExcludeOuterId() throws SessionException {
        Staff staff = getStaff();
        return tradeLocalConfigurable.isWhitelistOfExcludeOuterId(staff.getCompanyId());
    }

    /**
     *
     * @param page
     * @param sort
     * @param queryParams
     * @param selectType 0 全部 1 当前选择sid 2 当前页面查询条件
     * @param params
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/exception/item", method = RequestMethod.POST)
    @ResponseBody
    public Object queryExceptionItem(Page page,Sort sort,TradeControllerParams queryParams,Integer selectType, InvaildItemQueryParams params) throws SessionException {

        List<Long> sids = null;
        if (Objects.equals(1,selectType)) {
            sids = Strings.getAsLongList(queryParams.getSids(),",",true);
        }else if(Objects.equals(2,selectType)){
            Staff staff = getStaff();
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            TradeQueryParams tradeparams = null;
            Page tradePage = new Page();
            tradePage.setPageSize(20000);
            Sort tradeSort = new Sort();
            tradeSort.setField("pay_time");
            tradeparams = buildTradeQueryParams(queryParams, tradePage, tradeSort, 0, null, staff, tradeConfig);

            List<String> beforeAudit = Arrays.asList(Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_WAIT_MANUAL_AUDIT);


            if (tradeparams.getSysStatus() !=null && tradeparams.getSysStatus().length > 0) {
                List<String> status = new ArrayList<>();
                for (String sysStatus : tradeparams.getSysStatus()) {
                    if (beforeAudit.contains(sysStatus)) {
                        status.add(sysStatus);
                    }
                }
                if (CollectionUtils.isEmpty(status)) {
                    ArrayMap<String, Object> result = ArrayMap.create(2);
                    result.put("total", 0);
                    result.put("list", Collections.emptyList());
                    return result;
                }
                tradeparams.setSysStatus(status.toArray(new String[0]));
            }else {
                tradeparams.setSysStatus(beforeAudit.toArray(new String[0]));
            }

            tradeparams.setNeedFill(false);
            tradeparams.setUseHasNext(1);
            tradeparams.setPddMask(false);
            tradeparams.setFillItemQuantity(false);
            tradeparams.setBreakQuery(true);
            tradeparams.setSysMask(0);
            tradeparams.setExceptionStatus(TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED);

            tradeparams.setFields("t.sid");

            Trades trades = tradeSearchService.search(staff, tradeparams);
            List<Trade> list = trades.getList();
            if (CollectionUtils.isEmpty(list)) {
                throw new IllegalArgumentException("所选订单中没有无效商品");
            }
            sids = list.stream().map(x->x.getSid()).collect(toList());
        }

        Staff staff = getStaff();
        List<InvaildItem> list = tradeSearchService.queryItemExceptionItem(staff, page,sort, params,sids);
        Long count = tradeSearchService.queryItemExceptionItemCount(staff, params,sids);
        fillInvaildItem(staff, list, shopService);

        ArrayMap<String, Object> result = ArrayMap.create(2);
        result.put("total", count);
        result.put("list", list);
        return result;
    }


    /**
     * 获取系统支持的pdd物流信息
     */
    @RequestMapping(value = "/get/pdd/express", method = RequestMethod.GET)
    @ResponseBody
    public Object getPddExpress() throws SessionException {
        Staff staff = getStaff();
        List<User> pddUsers = userService.queryUsersByCompanyId(staff.getCompanyId(), CommonConstants.PLAT_FORM_TYPE_PDD);
        List<User> dwUsers = userService.queryUsersByCompanyId(staff.getCompanyId(), CommonConstants.PLAT_FORM_TYPE_POISON);
        List<User> qmUsers = userService.queryUsersByCompanyId(staff.getCompanyId(), CommonConstants.PLAT_FORM_TYPE_QIMEN);
        List<Long> expressIds = new ArrayList<>();
        List<ExpressCompany> expressCompanys = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(pddUsers)) {
            Map<String, List<String>> expressInfoMap = platformManagement.getAccess(CommonConstants.PLAT_FORM_TYPE_PDD, PlatformExpressInfoBusiness.class).getExpressMap(pddUsers.get(0));
            expressIds.addAll(expressInfoMap.values()
                    .stream()
                    .map(idStr -> idStr.stream()
                            .map(id -> Long.parseLong(id.trim()))
                            .collect(toList()))
                    .flatMap(List::stream)
                    .collect(toList()));
            // expressInfoMap.entrySet().forEach(value -> expressIds.addAll(value.getValue().stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList())));
        }

        if (CollectionUtils.isNotEmpty(dwUsers)) {
            Map<String, List<String>> expressInfoMap = platformManagement.getAccess(CommonConstants.PLAT_FORM_TYPE_POISON, PlatformExpressInfoBusiness.class).getExpressMap(dwUsers.get(0));
            expressIds.addAll(expressInfoMap.values()
                    .stream()
                    .map(idStr -> idStr.stream()
                            .map(id -> Long.parseLong(id.trim()))
                            .collect(toList()))
                    .flatMap(List::stream)
                    .collect(toList()));
            // expressInfoMap.entrySet().forEach(value -> expressIds.addAll(value.getValue().stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList())));
        }


        if (CollectionUtils.isNotEmpty(qmUsers)) {
            expressCompanys = expressCompanyService.getExpressCompanys();
            if (CollectionUtils.isNotEmpty(expressIds)) {
                List<Long> idList = expressCompanys.stream()
                        .map(ExpressCompany::getId)
                        .collect(toList());

                expressIds = expressIds.stream()
                        .filter(id -> !idList.contains(id))
                        .collect(toList());
            }
        }

        if (CollectionUtils.isNotEmpty(expressIds)) {
            List<ExpressCompany> expressCompanyByIds = expressCompanyService.getExpressCompanyByIds(expressIds.stream()
                    .filter(Objects::nonNull)
                    .distinct()
                    .toArray(Long[]::new));
            expressCompanys.addAll(expressCompanyByIds);
        }
        return CollectionUtils.isNotEmpty(expressCompanys) ? expressCompanys : successResponse();
    }

    /**
     * 规则操作日志查询
     */
    @RequestMapping(value = "/ruleOperate/log/list", method = RequestMethod.GET)
    @ResponseBody
    public Object getRuleOperateLogList(String ruleType, Long ruleId) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(ruleType), "请输入ruleType参数");
        Assert.notNull(ruleId, "请输入ruleId参数");
        Staff staff = getStaff();
        return ruleOperateLogBusiness.queryByRuleId(staff, ruleId, ruleType);
    }


    /**
     * 批发收银v2 根据sid查询拆单数据
     *
     * @param sid
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/search/stalls/sid", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object searchStallsBySid(Long sid, String api_name) throws SessionException {
        Assert.isTrue(Objects.nonNull(sid), "请输入sid参数");
        Staff staff = getStaff();
        // showDetail为false，查询的为该sid数据
        Trade trade = tradeSearchService.queryBySid(staff, false, sid);
        Assert.isTrue(Objects.nonNull(trade), "您输入的sid不存在");

        List<TbTrade> tbTrades = tradeSearchService.queryByTids(staff, true, trade.getTid());
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        return TradeModels.toTradeModels(staff, TradeUtils.toTrades(tbTrades), shopService, expressCompanyService, false, null, tradeConfig);
    }


    @RequestMapping(value = "/exception/item/trade", method = RequestMethod.POST)
    @ResponseBody
    public Object queryExceptionItemTrade(Page page, InvaildItemTradeQueryParams params) throws SessionException {
        Staff staff = getStaff();
        List<InvaildItemTrade> list = tradeSearchService.queryItemExceptionTrade(staff, page, params);
        Long count = tradeSearchService.queryItemExceptionTradeCount(staff, params);

        ArrayMap<String, Object> result = ArrayMap.create(2);
        result.put("total", count);
        result.put("list", list);
        return result;
    }

    @RequestMapping(value = "/queryCompanyFxgDfMalls", method = RequestMethod.POST)
    @ResponseBody
    public Object queryFxgDfMallByCompany() throws SessionException {
        Staff staff = getStaff();

        return fxgDfMallService.queryByCompanyId(staff);
    }

    /**
     * 是否为未匹配的商品
     *
     * @return
     */
    boolean isUnMatchItem(InvaildItem invaildItem) {
        // 这段代码是不是有可能sysItemId为空?
        // 原代码: invaildItem.getSysItemId()!=-1&&invaildItem.getStatus()==UN_MATCH
        return invaildItem.getSysItemId() != null && invaildItem.getSysItemId() != -1 && invaildItem.getStatus().equals(UN_MATCH);
    }

    private void fillInvaildItem(Staff staff, List<InvaildItem> list, IShopService shopService) {
        StaffRequest staffRequest = StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build();
        QuerySkuBridgeByPlatformItemRequest request = new QuerySkuBridgeByPlatformItemRequest();
        request.setStaffRequest(staffRequest);
        LinkedList<TbItemInfo> tbItemInfos = new LinkedList<>();
        request.setTbItemInfos(tbItemInfos);


        HashMap<Long, TbItemInfo> tbItemInfoMap = new HashMap<Long, TbItemInfo>();
        for (InvaildItem invaildItem : list) {
            String skuId = invaildItem.getSkuId();
            if (StringUtils.isBlank(skuId)) {
                skuId = "0";
            }
            invaildItem.setNewItemSkuId(StockItemUtils.getFullSkuIdWithUser(staff.getUserByUserId(invaildItem.getUserId()), skuId, invaildItem.getNumIid()));
            //初始化获取商品下载状态请求
            tbItemInfoMap.computeIfAbsent(invaildItem.getUserId(), k -> {
                TbItemInfo tbItemInfo = new TbItemInfo();
                tbItemInfo.setUserId(invaildItem.getUserId());
                tbItemInfo.setItemInfos(new ArrayList<>());
                return tbItemInfo;
            });
            TbItemInfo tbItemInfo = tbItemInfoMap.get(invaildItem.getUserId());

            TbItemInfo.ItemInfo itemInfo = new TbItemInfo.ItemInfo();
            itemInfo.setNumIid(invaildItem.getNumIid());
            itemInfo.setSkuId(invaildItem.getNewItemSkuId());

            tbItemInfo.getItemInfos().add(itemInfo);
        }

        for (TbItemInfo value : tbItemInfoMap.values()) {
            tbItemInfos.add(value);
        }

        Set<Long> userIds = tbItemInfoMap.keySet();
        List<Shop> shops = shopService.queryByUserIds(staff, userIds.toArray(new Long[0]));
        Map<Long, Shop> shopMap = shops.stream().collect(Collectors.toMap(Shop::getUserId, x -> {
            return x;
        }));

        QuerySkuBridgeByPlatformItemResponse response = tbItemSearchApi.QuerySkuBridgeByPlatformItem(request);
        List<SkuBridgeDto> itemInfos = response.getItemInfos();
        Map<String, List<SkuBridgeDto>> match = new HashMap<>();
        if (CollectionUtils.isNotEmpty(itemInfos)) {
            match = itemInfos.stream().collect(groupingBy(SkuBridgeDto::getNumIid));
        }
        HashMap<String, InvaildItem> itemIdSkuIdToInvaildItem = new HashMap<>();
        ArrayList<ItemIdInfoDto> requestInfo = new ArrayList<>();
        for (InvaildItem invaildItem : list) {
            if (match.get(invaildItem.getNumIid()) != null) {
                for (SkuBridgeDto skuBridgeDto : match.get(invaildItem.getNumIid())) {
                    if (invaildItem.getNewItemSkuId().equals(skuBridgeDto.getSkuId())) {//商品那边有
                        invaildItem.setSysSkuId(skuBridgeDto.getSysSkuId());
                        invaildItem.setSysItemId(skuBridgeDto.getSysItemId());
                        invaildItem.setStatus(UN_MATCH);
                        itemIdSkuIdToInvaildItem.put(invaildItem.getSysSkuId() + "-" + invaildItem.getSysItemId(), invaildItem);
                        ItemIdInfoDto itemIdInfoDto = new ItemIdInfoDto();
                        itemIdInfoDto.setSysItemId(invaildItem.getSysItemId());
                        itemIdInfoDto.setSysSkuId(invaildItem.getSysSkuId());
                        requestInfo.add(itemIdInfoDto);
                    }
                }
            }
            Shop shop = shopMap.get(invaildItem.getUserId());
            if (shop == null) continue;
            invaildItem.setItemSpecial(staff.getUserByUserId(invaildItem.getUserId()).getItemSpecial());
            invaildItem.setSource(shop.getSource());
            invaildItem.setSubSource(shop.getSubSource());
            invaildItem.setShopFlag(shop.getFlag());
            invaildItem.setShopName(shop.getShortTitle());
        }

        for (InvaildItem invaildItem : list) {
            if (invaildItem.getSysItemId() == null) {
                continue;
            }
            if (isUnMatchItem(invaildItem)) {
                invaildItem.setStatus(InvaildItem.MATCH_ING);
            }
        }
        if (requestInfo.isEmpty()) return;
        QueryByItemIdInfoListRequest queryItemRequest = new QueryByItemIdInfoListRequest();
        queryItemRequest.setStaffRequest(staffRequest);
        queryItemRequest.setItemIdInfoList(requestInfo);
        QueryByItemIdInfoListResponse itemInfoResponse = dmjItemCommonSearchApi.queryByItemIdInfoList(queryItemRequest);
        if (itemInfoResponse == null || itemInfoResponse.getDmjItemList() == null) return;
        for (DmjItemDto dmjItemDto : itemInfoResponse.getDmjItemList()) {
            InvaildItem invaildItem = itemIdSkuIdToInvaildItem.get(dmjItemDto.getSysSkuId() + "-" + dmjItemDto.getSysItemId());
            if (invaildItem != null) {
                if (StringUtils.isNotBlank(dmjItemDto.getOuterId().trim())) {
                    invaildItem.setOutIid(dmjItemDto.getOuterId());
                }
                invaildItem.setSysTitle(dmjItemDto.getTitle());
                invaildItem.setSysSkuPropertiesName(dmjItemDto.getPropertiesName());
                invaildItem.setSysSkuPropertiesAlias(dmjItemDto.getPropertiesAlias());
                invaildItem.setSkuOuterId(dmjItemDto.getSkuOuterId());
                invaildItem.setSysPicPath(dmjItemDto.getPicPath());
                invaildItem.setItemOuterId(dmjItemDto.getOuterId());
                InvaildItem.InvaildItemSysItemInfo sysInfo = new InvaildItem.InvaildItemSysItemInfo();
                invaildItem.setItemSystem(sysInfo);
                sysInfo.setPicPath(dmjItemDto.getPicPath());
                sysInfo.setTitle(dmjItemDto.getTitle());
                sysInfo.setSysItemId(dmjItemDto.getSysItemId());
                sysInfo.setTypeTag(dmjItemDto.getTypeTag());
                sysInfo.setType(dmjItemDto.getType());
            }
        }
    }

    /**
     * 包装验货返回条码信息
     *
     * @param staff
     * @param trade
     */
    private void setBarcode(Staff staff, Trade trade) {
        List<Long> sysItemIds = new ArrayList<>(), sysSkuIds = new ArrayList<>();
        //如果纯商品Id<0那么是纯商品
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        orders.stream().filter(order -> order.getItemSysId() > 0).forEach(order -> {
            Long skuSysId = order.getSkuSysId();
            //如果sku规格 <= 0  说明是纯商品
            if (skuSysId == null || skuSysId <= 0) {
                sysItemIds.add(order.getItemSysId());
            } else { //否则是规格商品
                sysSkuIds.add(skuSysId);
            }
        });
        //所有纯商品和规格商品Map
        Map<Long, SysItemSku> map = new HashMap<>();
        if (!sysItemIds.isEmpty()) {//纯商品
            List<DmjItem> itemList = itemServiceWrapper.queryBySysItemIds(staff, sysItemIds, "sysItemId,outerId,barcode");
            for (DmjItem item : itemList) {
                map.put(item.getSysItemId(), buildItemSku(item.getSysItemId(), null, item.getOuterId(), item.getBarcode()));
            }
        }
        if (!sysSkuIds.isEmpty()) {//规格
            List<DmjSku> skuList = itemServiceWrapper.queryBySysSkuIds(staff, sysSkuIds, "sysItemId,sysSkuId,outerId,barcode");
            for (DmjSku sku : skuList) {
                map.put(sku.getSysSkuId(), buildItemSku(sku.getSysItemId(), sku.getSysSkuId(), sku.getOuterId(), sku.getBarcode()));
            }
        }
        for (Order o : orders) {
            SysItemSku target = (o.getSkuSysId() == null || o.getSkuSysId() <= 0) ? map.get(o.getItemSysId()) : map.get(o.getSkuSysId());
            if (target != null) {
                o.setBarcode(target.getBarcode());
            }
        }
    }

    private SysItemSku buildItemSku(Long sysItemId, Long sysSkuId, String outerId, String barcode) {
        SysItemSku itemSku = new SysItemSku();
        itemSku.setSysItemId(sysItemId);
        itemSku.setSysSkuId(sysSkuId);
        itemSku.setOuterId(outerId);
        itemSku.setBarcode(barcode);
        return itemSku;
    }

    private void fillUniqueCodeTypeFiled(Staff staff, TradeModels tradeModels) {
        if (CollectionUtils.isNotEmpty(tradeModels.getList())) {
            List<OrderModel> orders = tradeModels.getList().get(0).getOrders();
            List<Long> sysSkuIds = orders.stream().map(OrderModel::getSkuSysId).distinct().collect(toList());
            List<Long> sysItemIds = orders.stream().map(OrderModel::getItemSysId).distinct().collect(toList());
            //找出强唯一码类型的商品
            List<String> outerIdList = packBusiness.checkExitUniqueCodeItem(staff, sysItemIds, sysSkuIds);
            tradeModels.setUniqueCodeItemList(outerIdList);
        }
    }


/*    private void fillSpecialParams(Staff staff, TradeQueryParams params) {
        //添加一个可选参数，选择查平台，需要输入userId和时间可查询
        if (null != params.getQueryPlatform() && 1 == params.getQueryPlatform() && (StringUtils.isNotBlank(params.getReceiverMobile()) || StringUtils.isNotBlank(params.getReceiverName()))) {
            if (null == params.getUserIds() || 0 == params.getUserIds().length) {
                return;
            }
            if (null != params.setTid()) {
                return;
            }
            List<Long> userIds = Lists.newArrayList(params.getUserIds());
            Map<Long, User> userMap = new HashMap<>();
            List<String> result = new ArrayList<>();
            for (Long userId:userIds) {
                User user = userMap.get(userId);
                if (null != user) {
                    List<String> tids = tbDesensitizationBusiness.queryByReceiverMoblie(user, params.getReceiverMobile(), params.getReceiverPhone(), params.getReceiverName(), params.getStartTime(), params.getEndTime());
                    if (CollectionUtils.isNotEmpty(tids)) {
                        result.addAll(tids);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(result)) {
                params.setTid(result.toArray(new String[0]));
            }
        }
    }*/

    private String getOutSidByMixKey(String mixKey) {
        if (mixKey.contains("_") && mixKey.substring(mixKey.length() - 2, mixKey.length() - 1).equals("_")) {
            String[] mixIds = new String[2];
            mixIds[0] = mixKey.substring(0, mixKey.length() - 2);
            mixIds[1] = mixKey.substring(mixKey.length() - 1);
            Integer flag = Integer.parseInt(mixIds[1]);
            if (3 == flag) {
                return mixIds[0];
            }
        }
        return null;
    }

    /**
     * 填充商品类目名称
     *
     * @param staff
     * @param tradeModels
     * @param categoryVoice 播报商品类目 0-否 1-是
     */
    private void fillItemCatName(Staff staff, TradeModels tradeModels, Integer categoryVoice) {
        if (!Objects.equals(categoryVoice, CommonConstants.VALUE_YES) || tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        List<String> sysOuterIds = new ArrayList<>();
        for (TradeModel model : tradeModels.getList()) {
            if (model == null || CollectionUtils.isEmpty(model.getOrders())) {
                continue;
            }
            sysOuterIds.addAll(model.getOrders().stream().map(OrderModel::getSysOuterId).collect(Collectors.toList()));
        }
        ItemCatIdAndSellerCidsResponse response = itemsService.queryItemCatIdAndSellerCids(ItemStaffRequest.buildStaffRequest(staff), sysOuterIds);
        if (response == null || CollectionUtils.isEmpty(response.getItemList())) {
            return;
        }

        List<ItemCatIdAndSellerCidsResponse.SimpleItem> itemList = response.getItemList();
        Map<String, ItemCatIdAndSellerCidsResponse.SimpleItem> map = itemList.stream().collect(Collectors.toMap(ItemCatIdAndSellerCidsResponse.SimpleItem::getOuterId, v -> v, (v1, v2) -> v2));
        for (TradeModel model : tradeModels.getList()) {
            for (OrderModel order : model.getOrders()) {
                ItemCatIdAndSellerCidsResponse.SimpleItem simpleItem = map.get(order.getSysOuterId());
                if (null != simpleItem) {
                    order.setItemCatName(simpleItem.getItemCatName());
                }
            }
        }
    }

    private void fillBatchNoAndProductDate(Staff staff, Long queryId, TradeModels tradeModels) {
        if (!Objects.equals(queryId, 33L) || tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        List<OrderModel> orders = tradeModels.getList().get(0).getOrders();
        if (CollectionUtils.isEmpty(orders) || !WmsUtils.isNewWms(staff)) {
            return;
        }

        try {
            List<Long> orderIds = new ArrayList<>();
            Set<String> outerIds = new HashSet<>();
            for (OrderModel order : orders) {
                orderIds.add(Long.valueOf(order.getId()));
                if (CollectionUtils.isEmpty(order.getSuits())) {
                    outerIds.add(order.getOuterId());
                } else {
                    outerIds.addAll(order.getSuits().stream().map(OrderModel::getOuterId).collect(Collectors.toSet()));
                }
            }
            Map<String, Object> itemMap = itemServiceDubbo.queryItemSkuByOuterId(staff, Lists.newArrayList(outerIds), 12, null);
            Map<Long, List<AllocateGoodsRecord>> recordMap = wmsService.queryAllocateGoodsRecords(staff, new QueryAllocateGoodsRecordParams.Builder()
                            .orderIds(orderIds).containerTypes(Lists.newArrayList(ContainerTypeEnum.GOODS_SECTION.getValue(), ContainerTypeEnum.WORKING_STORAGE_SECTION.getValue())).build())
                    .stream().collect(Collectors.groupingBy(AllocateGoodsRecord::getOrderId));

            fillOrderModels(orders, itemMap, recordMap);
            tradeModels.getList().get(0).setOrders(orders);
        } catch (Exception e) {
            if (logger.isDebugEnabled()) {
                logger.error(LogHelper.buildLog(staff, "包装验货填充批次/生产日期失败"), e);
            }
        }
    }

    private void fillOrderModels(List<OrderModel> orders, Map<String, Object> itemMap, Map<Long, List<AllocateGoodsRecord>> recordMap) {
        for (OrderModel order : orders) {
            if (CollectionUtils.isEmpty(order.getSuits())) {
                convertObjects(order, itemMap);
                fillBatchNoAndProductDateInfo(order, recordMap);
            } else {
                for (OrderModel suit : order.getSuits()) {
                    convertObjects(suit, itemMap);
                    fillBatchNoAndProductDateInfo(suit, recordMap);
                }
            }
        }
    }

    private void fillMiddleTrades(Staff staff, Long firstSid, TradeModels tradeModels, TradeConfig tradeConfig) {
        if (firstSid == null || firstSid <= 0L || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        TradeStaffConfig tradeStaffConfig = tradeStaffConfigService.get(staff);
        TradeModel currentTradeModel = tradeModels.getList().get(0);
        Long currentSid = Long.valueOf(tradeModels.getList().get(0).getSid());
        if (currentTradeModel.getWaveId() == null || currentTradeModel.getWaveId() <= 0L) {
            throw new IllegalArgumentException("验货订单非波次订单");
        }
        WaveTradeQueryParams waveTradeQueryParams = new WaveTradeQueryParams();
        waveTradeQueryParams.setWaveId(currentTradeModel.getWaveId());
        List<WaveTrade> waveTrades = tradeWaveService.queryWaveTradeByParams(staff, waveTradeQueryParams);
        List<WaveTrade> middleWaveTrades = Lists.newArrayList();
        // 首单和尾单的扫描顺序，可能为正序，也可能为倒序，取中间部分即可
        boolean startAdd = false;
        for (WaveTrade data : waveTrades) {
            if (startAdd) {
                middleWaveTrades.add(data);
            }
            if (Objects.equals(firstSid, data.getSid()) || Objects.equals(currentSid, data.getSid())) {
                startAdd = !startAdd;
                if (startAdd) {
                    middleWaveTrades.add(data);
                }
                if (Objects.equals(firstSid, currentSid)) {
                    break;
                }
            }
        }
        Assert.notEmpty(middleWaveTrades, "订单未进入波次，不允许批量验货");
        Map<Long, Long> positionNoMap = middleWaveTrades.stream().collect(Collectors.toMap(WaveTrade::getSid, t -> DataUtils.getZeroIfDefault(t.getPositionNo()), (k1, k2) -> k1));
        Long[] sids = middleWaveTrades.stream().map(WaveTrade::getSid).toArray(Long[]::new);
        Assert.isTrue(sids.length <= 3000, "验货的订单超过3000单，请缩小范围后再验货");
        List<Trade> middleTrades = tradeSearchService.queryBySids(staff, true, sids);

        List<SimplePackMiddleTrade> normalPackTrades = new ArrayList<>();
        List<SimplePackMiddleTrade> errorPackTrades = new ArrayList<>();
        List<Long> cancelInsufficientSidList = new ArrayList<>();
        Map<String, Integer> itemCountMap = new HashMap<>();
        for (Trade middleTrade : middleTrades) {
            try {
                TradeValidator validator = new TradeValidator();
                validator.setThrowExceptionIfError(false);
                validator.setIgnoreInsufficient(true);
                checkBatchPack(staff, middleTrade, validator, tradeStaffConfig);
                SimplePackMiddleTrade simplePackMiddleTrade = SimplePackMiddleTrade.buildFieldsFromTrade(middleTrade, tradeConfig);
                if (positionNoMap.containsKey(middleTrade.getSid())) {
                    simplePackMiddleTrade.setPositionNo(positionNoMap.get(middleTrade.getSid()));
                }
                if (validator.hasError()) {
                    errorPackTrades.add(simplePackMiddleTrade);
                    simplePackMiddleTrade.setErrorPackReason(validator.getMessage());
                } else {
                    normalPackTrades.add(simplePackMiddleTrade);
                    List<Order> orders = TradeUtils.getOrders4Trade(middleTrade);
                    for (Order order : orders) {
                        String key = order.getItemSysId() + "_" + (order.getSkuSysId() < 0L ? 0L : order.getSkuSysId());
                        if (itemCountMap.containsKey(key)) {
                            itemCountMap.put(key, itemCountMap.get(key) + order.getNum());
                        } else {
                            itemCountMap.put(key, order.getNum());
                        }
                    }
                }
                //只有缺货异常，检查是否需要取消缺货异常
                if (!validator.hasError() && validator.isCancelInsufficient()) {
                    cancelInsufficientSidList.add(middleTrade.getSid());
                }
            } catch (Exception e) {
                throw new IllegalArgumentException(String.format("%s，订单号:%s", e.getMessage(), middleTrade.getSid()));
            }
        }
        //一单多包
        List<String> normalOutSidList = normalPackTrades.stream().map(SimplePackMiddleTrade::getOutSid).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, List<MultiPacksPrintTradeLogDetail>> outSidMultiPackMap = multiPacksPrintTradeLogService.queryPrintLogDetailByOutSids(staff, middleTrades);
        int multiPackOustSidNum = 0;
        if (MapUtils.isNotEmpty(outSidMultiPackMap)) {
            for (String normalOutSid : normalOutSidList) {
                List<MultiPacksPrintTradeLogDetail> multiPacksPrintTradeLogDetails = outSidMultiPackMap.get(normalOutSid);
                if (CollectionUtils.isNotEmpty(multiPacksPrintTradeLogDetails)) {
                    multiPackOustSidNum += multiPacksPrintTradeLogDetails.size() - 1;//一单多包除去主单的数量
                }
            }
            for (SimplePackMiddleTrade errorPackTrade : errorPackTrades) {
                List<MultiPacksPrintTradeLogDetail> multiPacksPrintTradeLogDetails = outSidMultiPackMap.get(errorPackTrade.getOutSid());
                if (CollectionUtils.isNotEmpty(multiPacksPrintTradeLogDetails)) {
                    errorPackTrade.setOutSid(Joiner.on(";").join(multiPacksPrintTradeLogDetails.stream().map(MultiPacksPrintTradeLogDetail::getOutSid).collect(Collectors.toList())));
                }
            }
        }

        normalPackTrades.sort(Comparator.comparing(SimplePackMiddleTrade::getPositionNo).thenComparing(SimplePackMiddleTrade::getSid));
        errorPackTrades.sort(Comparator.comparing(SimplePackMiddleTrade::getPositionNo).thenComparing(SimplePackMiddleTrade::getSid));

        currentTradeModel.setNormalMiddleTrades(normalPackTrades);
        currentTradeModel.setErrorMiddleTrades(errorPackTrades);
        currentTradeModel.setNormalMiddleTradeOutSidCount(normalPackTrades.size() + multiPackOustSidNum);
        currentTradeModel.setNormalMiddleTradeItemCount(itemCountMap.values().stream().mapToInt(Integer::intValue).sum());
        currentTradeModel.setNormalMiddleTradeItemKindCount(itemCountMap.keySet().size());

        checkBatchPackTrades(currentTradeModel, firstSid, middleWaveTrades, middleTrades);
        // 取消缺货异常
        cancelInsufficient(staff, cancelInsufficientSidList);
    }

    private void convertObjects(OrderModel order, Map<String, Object> itemMap) {
        Object obj = itemMap.get(order.getOuterId());
        if (obj == null) {
            return;
        }

        if (obj instanceof DmjSku) {
            order.setHasBatch(((DmjSku) obj).getHasBatch());
            order.setHasProduct(((DmjSku) obj).getHasProduct());
        } else if (obj instanceof DmjItem) {
            order.setHasBatch(((DmjItem) obj).getHasBatch());
            order.setHasProduct(((DmjItem) obj).getHasProduct());
        }
    }

    public void fillBatchNoAndProductDateInfo(OrderModel orderModel, Map<Long, List<AllocateGoodsRecord>> recordMap) {
        if (recordMap == null || recordMap.isEmpty()) {
            return;
        }
        List<AllocateGoodsRecord> records = recordMap.get(Long.valueOf(orderModel.getId()));
        if (CollectionUtils.isNotEmpty(records)) {
            Map<String, List<AllocateGoodsRecord>> batchNoMap = records.stream().filter(r -> StringUtils.isNotEmpty(r.getBatchNo())).collect(Collectors.groupingBy(AllocateGoodsRecord::getBatchNo));
            Map<String, List<AllocateGoodsRecord>> productTimeMap = records.stream().filter(r -> r.getProductTime() != null).collect(Collectors.groupingBy(v -> DateUtils.date2Str(v.getProductTime())));
            if (batchNoMap != null && !batchNoMap.isEmpty()) {
                List<String> batchNos = new ArrayList<>();
                for (Map.Entry<String, List<AllocateGoodsRecord>> entry : batchNoMap.entrySet()) {
                    batchNos.add(entry.getKey() + " * " + entry.getValue().stream().mapToInt(AllocateGoodsRecord::getAllocatedNum).sum());
                }
                orderModel.setBatchNos(batchNos);
            }
            if (productTimeMap != null && !productTimeMap.isEmpty()) {
                List<String> productDates = new ArrayList<>();
                for (Map.Entry<String, List<AllocateGoodsRecord>> entry : productTimeMap.entrySet()) {
                    productDates.add(entry.getKey() + " * " + entry.getValue().stream().mapToInt(AllocateGoodsRecord::getAllocatedNum).sum());
                }
                orderModel.setProductDates(productDates);
            }
        }
    }

    /**
     * 填充包装商品信息
     */
    private void fillPackItemInfo(Staff staff, TradeModels tradeModels, TradeQueryParams params) {
        if (!Objects.equals(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_PACK)) {
            return;
        }
        if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        List<String> visibleColumnFieldList = columnConfService.getVisibleColumnFieldList(staff, 38);
        if (CollectionUtils.isEmpty(visibleColumnFieldList) || !visibleColumnFieldList.contains("itemPrice")) {
            return;
        }
        List<String> sysOuterIds = tradeModels.getList().stream().flatMap(data -> Optional.ofNullable(data.getOrders()).orElse(Lists.newArrayList()).stream())
                .map(OrderModel::getSysOuterId).collect(toList());
        if (CollectionUtils.isEmpty(sysOuterIds)) {
            return;
        }
        Map<String, Object> itemMap = WmsUtils.toLowerCase(itemsService.queryItemSkuByOuterId(staff, sysOuterIds, 12));
        for (TradeModel model : tradeModels.getList()) {
            if (CollectionUtils.isEmpty(model.getOrders())) {
                continue;
            }
            for (OrderModel order : model.getOrders()) {
                if (order.getOuterId() == null) { // 商品未下载成功的情况下outerId会为null
                    continue;
                }
                Object item = itemMap.get(order.getOuterId().toLowerCase());
                if (item == null) {
                    continue;
                }
                if (item instanceof DmjSku) {
                    DmjSku sku = (DmjSku) item;
                    order.setItemPrice(sku.getPriceOutput());
                } else {
                    order.setItemPrice(((DmjItem) item).getPriceOutput());
                }
            }
        }
    }

    /**
     * 获取交易关闭子订单
     *
     * @param trade
     * @param tradeStaffConfig
     * @return
     */
    private List<Order> getCloseOrderList(Trade trade, TradeStaffConfig tradeStaffConfig) {
        if (trade == null || CollectionUtils.isEmpty(TradeUtils.getOrders4Trade(trade))) {
            return new ArrayList<>();
        }

        List<Order> closeOrders = TradeUtils.getOrders4Trade(trade).stream().filter(t -> Objects.equals(Trade.SYS_STATUS_CLOSED, t.getSysStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(closeOrders)) {
            return new ArrayList<>();
        }

        if (Objects.equals(tradeStaffConfig.getOpenSuitselfPack(), CommonConstants.VALUE_YES)) {
            return closeOrders;
        }

        return OrderUtils.toEffectiveOrders(closeOrders);
    }

    private void fillCloseOrdersInfo(Staff staff, TradeModels models, List<Order> closeOrders, Trade trade, TradeConfig tradeConfig) {
        if (CollectionUtils.isEmpty(closeOrders) || models == null || CollectionUtils.isEmpty(models.getList())) {
            return;
        }

        List<OrderModel> orderModels = new ArrayList<>();
        for (Order closeOrder : closeOrders) {
            OrderModel orderModel = OrderModel.toBaseOrderModel(staff, trade, closeOrder, false, tradeConfig);
            orderModels.add(orderModel);
        }

        models.getList().get(0).setCloseOrders(orderModels);
    }

    /**
     * 检查特殊字符
     *
     * @param str
     * @return
     */
    private Boolean checkSpecialCharacter(String str) {
        Boolean isSpecailCharacter = false;
        if (StringUtils.isNotBlank(str)) {
            final int LAST_BMP = 0xFFFF;
            for (int i = 0; i < str.length(); i++) {
                if (str.codePointAt(i) >= LAST_BMP) {
                    isSpecailCharacter = true;
                    break;
                }
            }
            return isSpecailCharacter;
        }
        return isSpecailCharacter;
    }

    /**
     * 参数检查
     *
     * @param params
     */
    private void parameterCheck(TradeControllerParams params) {
        String matchStr = "^[0-9]+(,[0-9]+)*$";
        //特殊字符检查
        Assert.isTrue(!checkSpecialCharacter(JSON.toJSONString(params.getBuyerNick())), "查询参数昵称中含有特殊字符！");
        //系统单号检查
        checkNum(params.getSid(), "请输入正确的系统单号");
        //内部单号检查
        checkNum(params.getShortId(), "请输入正确的内部单号");
        //手机号码检查
        if (StringUtils.isNotBlank(params.getText()) && StringUtils.equals("receiverMobile", params.getKey())) {
            Assert.isTrue(!checkSpecialCharacter(params.getText()), "手机号码中含有特殊字符,请检查输入的手机号！");
//            Assert.isTrue(params.getText().matches(matchStr), "手机号码中含有其它字符,请检查输入的手机号");
        }
    }

    private void checkNum(String str, String msg) {
        if (StringUtils.isBlank(str)) {
            return;
        }
        List<String> err = new ArrayList<>();
        for (String s : ArrayUtils.toStringArray(str)) {
            try {
                Long.parseLong(s);
            } catch (NumberFormatException e) {
                err.add(s);
            }
        }
        if (CollectionUtils.isNotEmpty(err))
            throw new IllegalArgumentException(msg + " 不合法数据明细：" + Strings.join(",", err));
    }

    @RequestMapping(value = "/batchCopy", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Object batchCopy(@RequestBody BatchCopyParams batchCopyParams) throws SessionException {
        Assert.isTrue(!Objects.isNull(batchCopyParams), "参数 batchCopyParams 为null");
        Assert.isTrue(CollectionUtils.isNotEmpty(batchCopyParams.getSids()), "请输入sids参数");
        Assert.isTrue(CollectionUtils.isNotEmpty(batchCopyParams.getTradeFields()), "请输入TradeFields参数");
        Assert.isTrue(CollectionUtils.isNotEmpty(batchCopyParams.getOrderFields()), "请输入OrderFields参数");

        Staff staff = getStaff();
        List<String> dbTradeFields = new ArrayList<>();
        for (String temp : batchCopyParams.getTradeFields()) {
            String tempDb = CommonConstants.TRADE_FIELDS_MAP.get(temp.trim());
            Assert.isTrue(!Objects.isNull(tempDb), String.format(" tradeFields中%s, 未找到对应字段", temp));
            dbTradeFields.add(tempDb);
        }
        String tradeFields = StringUtils.join(dbTradeFields, ",");
        List<String> dbOrderFields = new ArrayList<>();
        for (String temp : batchCopyParams.getOrderFields()) {
            String tempDb = CommonConstants.ORDER_FIELDS_MAP.get(temp.trim());
            Assert.isTrue(!Objects.isNull(tempDb), String.format(" orderFields中%s, 未找到对应字段", temp));
            dbOrderFields.add(tempDb);
        }
        String orderFields = StringUtils.join(dbOrderFields, ",");
        Long[] sids = batchCopyParams.getSids().toArray(new Long[0]);
        List<Trade> trades = tradeSearchService.queryByKeys(staff, tradeFields, orderFields, "sid", true, sids);
        List<Trade> result = getResult(staff, tradeFields, orderFields, trades);
        Map<Long, Trade> mainMergeSidMap = new HashMap<>();
        for (Trade temp : result) {
            if (temp.getMergeSid().equals(temp.getSid())) {
                mainMergeSidMap.put(temp.getSid(), temp);
            }
        }

        List<OrderVo> orderVos = new ArrayList<>();
        for (Trade trade : result) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                OrderVo orderVo = new OrderVo();
                orderVo.setTrade(trade);
                if (TradeUtils.isMerge(trade)) {
                    orderVo.setTrade(mainMergeSidMap.get(trade.getMergeSid()));
                }
                orderVo.setOrder(order);
                orderVos.add(orderVo);
            }
        }
        int copyLimitNum = 10000;
        if (ConfigHolder.GLOBAL_CONFIG != null && ConfigHolder.GLOBAL_CONFIG.getCopyLimitNum() != null && ConfigHolder.GLOBAL_CONFIG.getCopyLimitNum() > 0) {
            copyLimitNum = ConfigHolder.GLOBAL_CONFIG.getCopyLimitNum();
        }
        if (orderVos.size() > copyLimitNum) {
            String warnMsg = String.format("批量复制数据太大被截断，当前数据长度=%s, 最大返回长度=%s", orderVos.size(), copyLimitNum);
            Logs.warn(LogHelper.buildLog(staff, warnMsg));
            orderVos = new ArrayList<>(orderVos.subList(0, copyLimitNum));
//            batchCopyParams.setWarnMsg(warnMsg);
        }
        batchCopyParams.setOrderVos(orderVos);
        return batchCopyParams;
    }

    private List<Trade> getResult(Staff staff, String tradeFields, String orderFields, List<Trade> trades) {
        List<Trade> mergeTrades = new ArrayList<>();
        List<Long> mergeSids = new ArrayList<>();
        List<Trade> result = new ArrayList<>();
        for (Trade trade : trades) {
            if (TradeUtils.isMerge(trade)) {
                mergeSids.add(trade.getMergeSid());
            } else {
                result.add(trade);
            }
        }
        if (CollectionUtils.isNotEmpty(mergeSids)) {
            mergeTrades = tradeSearchService.queryByKeys(staff, tradeFields, orderFields, "merge_sid", true, mergeSids.toArray(new Long[0]));
            result.addAll(mergeTrades);
        }
        return result;
    }

    private void buildHintMessage(Staff staff, Trade trade, WaveConfig waveConfig, TradeModels tradeModels) {
        // 读取配置,为1的时候才提示,这个是在语音提醒包配置中,开已打印直接补打的配置
        if (waveConfig != null && Objects.equals(waveConfig.getInteger(WaveChatConfigsEnum.OPEN_PRINT_VOICE_HINTS.getKey()), 1) &&
                trade.getExpressPrintTime() != null && trade.getExpressPrintTime().compareTo(TradeTimeUtils.INIT_DATE) > 0) {
            tradeModels.setHintMessage("该订单已打印");
        }
    }

    /**
     * 校验批量包装验货
     */
    private void checkBatchPackTrades(TradeModel model, Long preSid, List<WaveTrade> waveTrades, List<Trade> allMiddleTrades) {

        if (!waveTrades.stream().map(WaveTrade::getSid).collect(Collectors.toList()).contains(preSid)) {
            throw new IllegalArgumentException("波次号不同，不允许批量验货");
        }

        List<Order> orders = allMiddleTrades.stream()
                .filter(trade -> Objects.equals(trade.getSid(), preSid))
                .findFirst().map(TradeUtils::getOrders4Trade)
                .orElse(Lists.newArrayList());
        List<OrderModel> currentOrderModels = model.getOrders();
        if (CollectionUtils.isEmpty(orders) || orders.size() != currentOrderModels.size()) {
            throw new IllegalArgumentException("首单和尾单的商品和数量必须完全相同");
        }
        Map<String, List<Order>> skuMap = orders.stream().collect(Collectors.groupingBy(order -> order.getItemSysId() + "_" + (order.getSkuSysId() < 0L ? 0L : order.getSkuSysId()) + "_" + order.getNum()));
        Map<String, List<OrderModel>> currentSkuMap = currentOrderModels.stream().collect(Collectors.groupingBy(sku -> sku.getItemSysId() + "_" + (sku.getSkuSysId() < 0L ? 0L : sku.getSkuSysId()) + "_" + sku.getNum()));
        for (Map.Entry<String, List<Order>> entryMap : skuMap.entrySet()) {
            String key = entryMap.getKey();
            List<Order> value = entryMap.getValue();
            if (currentSkuMap.get(key) == null || currentSkuMap.get(key).isEmpty()) {
                throw new IllegalArgumentException("首单和尾单的商品和数量必须完全相同");
            }

            if (currentSkuMap.get(key).size() != value.size()) {
                throw new IllegalArgumentException("首单和尾单的商品和数量必须完全相同");
            }
        }
    }

    private void cancelInsufficient(Staff staff, List<Long> cancelInsufficientSidList) {
        if (CollectionUtils.isEmpty(cancelInsufficientSidList)) {
            return;
        }

        cancelInsufficientBusiness.cancel(staff, cancelInsufficientSidList.toArray(new Long[0]));
    }

    void fillTradeTrace(Staff staff, TradeModel tm, Long sid,boolean itemModifyLog) {
        if (tm == null || sid == null) {
            return;
        }
        try {
            List<TradeTrace> tradeTraces = tradeTraceService.getTradeTraceBySid(staff, sid);
            tm.setTraces(TradeTraceModel.valueOfList(filterNullString(staff, tradeTraces,itemModifyLog)));
        } catch (Exception ex) {
            logger.error(LogHelper.buildErrorLog(staff, ex, ""), ex);
            //https://gykj.yuque.com/entavv/eltb2u/bievzf6h0qh6dotu
            tm.setTraces(TradeTraceModel.valueOfList(Collections.singletonList(TradeTrace.builder()
                    .content("加载操作记录失败，请稍后重新打开当前页面")
                    .operateTime(new Date(946656000000L))
                    .operator("系统")
                    .build())));
        }
    }


    /**
     * 显示毛利润计算公式
     *
     * @param staff
     * @param trades
     */
    private void fillGrossProfitDisplay(Staff staff, TradeQueryParams params, List<Trade> trades) {
        try {
            if (staff == null || CollectionUtils.isEmpty(trades) || params == null) {
                return;
            }
            //【订单管理】【订单查询】【订单处理】【订单财审】指定页面显示
            long queryId = params.getQueryId() != null ? params.getQueryId() : 0L;
            if (!(8L == queryId
                    || 62L == queryId
                    || 21L == queryId
                    || 61L == queryId)) {
                return;
            }
            boolean hasPowerPayment = StringUtils.contains(staff.getPowerDataPrivilegeSettings(), "1000302");
            boolean grossProfitFormula = paymentCalculateSupports.hasFeature(staff, "GrossProfitFormula");
            if (!grossProfitFormula || !hasPowerPayment) {
                return;
            }
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            if (tradeConfig == null) {
                return;
            }
            //开启白名单后订单毛利润展示计算公式
            for (Trade trade : trades) {
                trade.setGrossProfitDisplay(hasPowerPayment ? PaymentUtils.generateGrossProfitDisplay(trade, tradeConfig) : "***");
            }
        } catch (Exception ignored) {
            //ignored
        }
    }

    public boolean checkGiftV2HasFeature(Staff staff) {
        return featureService.checkHasFeatureByCode(staff.getCompanyId(), Feature.GIFT_V2.getCode());
    }

    /**
     * 按买家旺旺统计订单
     *
     * @param page
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/queryBuyerNickNum", method = RequestMethod.POST)
    @ResponseBody
    public Object queryBuyerNickNum(TradeControllerParams params, Page page, String api_name) throws Exception {
        return queryBuyerNickNum(getStaff(),params, page);
    }


    //    @Cacheable(value = "defaultCache#600", key = "'buyerNick_num_'+ #staff.companyId + '_' + #params.queryId")
    public JSONObject queryBuyerNickNum(Staff staff,TradeControllerParams params, Page page) {
        JSONObject jsonObject = new JSONObject();
        if (page == null) {
            page = new Page(1, 500);
        }
        Trades trades = tradeSearchService.search(staff, TradeQueryParams.copyParams(params)
                .setFields("t.sid,t.buyer_nick,count(*) item_kind_num")
                .setCustomWhere(" 1=1 ) group by (t.buyer_nick ")
                .setHasSqlGroupBy(Boolean.TRUE)
                .setIsHalt(0)
                .setIsCancel(0)
                .setBreakQuery(true)
                .setCheckItem(false)
                .setNeedFill(false)
                .setQueryOrder(false)
                .setPage(page)
                .setAllowedPgl(true));
        if (trades != null && CollectionUtils.isNotEmpty(trades.getList())) {
            List<Trade> tradesList = trades.getList();
            tradesList.sort(Comparator.comparing(Trade::getItemKindNum).reversed());
            secretBusiness.decodeTrades(staff, tradesList, false, true, false, false, false);
            for (Trade trade : tradesList) {
                if (StringUtils.isEmpty(trade.getBuyerNick())) {
                    //未匹配,跳过
                    continue;
                }
                jsonObject.put(trade.getBuyerNick(), trade.getItemKindNum());
            }
        }
        return jsonObject;
    }
}

package com.raycloud.dmj.web.controllers.backend;

import com.raycloud.dmj.domain.trades.FxgDfMall;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeExt;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.trades.FxgDfMallService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
public class FxgDfMallBackendController extends Sessionable {

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private FxgDfMallService fxgDfMallService;


    @PostMapping("/trade/backend/insertOrUpdateFxgDfMalls")
    public Object insertOrUpdateFxgDfMalls(@RequestBody List<FxgDfMall> fxgDfMallList) throws SessionException {
        Assert.isTrue(fxgDfMallList != null && !fxgDfMallList.isEmpty(), "fxgDfMallList must not be empty");

        eventCenter.fireEvent(this, new EventInfo("fxgDf.mall.insertOrUpdate").setArgs(new Object[]{getStaff(), generationTradeList(fxgDfMallList), null}), null);

        return successResponse();
    }

    @PostMapping("/trade/backend/batchUpdateFxgDfMalls")
    public Object batchUpdateFxgDfMalls(@RequestBody List<FxgDfMall> fxgDfMallList) throws SessionException {
        Assert.isTrue(fxgDfMallList != null && !fxgDfMallList.isEmpty(), "fxgDfMallList must not be empty");

        fxgDfMallService.batchUpdate(getStaff(),fxgDfMallList);
        return successResponse();
    }

    private List<Trade> generationTradeList(List<FxgDfMall> fxgDfMallList) {
        List<Trade> tradeList = new ArrayList<>();

        for (FxgDfMall fxgDfMall : fxgDfMallList) {
            Trade trade = new Trade();
            String source = fxgDfMall.getTradeSource();
            source = StringUtils.isEmpty(source)?CommonConstants.PLAT_FORM_TYPE_FXG_DF:source;
            trade.setSource(source);
            trade.setSubSource(fxgDfMall.getSubSource());
            TradeExt tradeExt = new TradeExt();
            tradeExt.setMallMaskId(fxgDfMall.getMallMaskId());
            tradeExt.setMallMaskName(fxgDfMall.getMallMaskName());
            trade.setTradeExt(tradeExt);

            tradeList.add(trade);
        }

        return tradeList;
    }

}

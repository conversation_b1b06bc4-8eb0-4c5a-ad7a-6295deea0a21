package com.raycloud.dmj.web.model.trades;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageListBase;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLogDetail;
import com.raycloud.dmj.domain.pt.vo.PrintTradeLogDetailVO;
import com.raycloud.dmj.domain.trade.split.SplitResult;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.fx.FxOperateResult;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.user.QiMenShop;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.newfx.trades.Constants;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 交易模型集合
 *
 * <AUTHOR>
 */
public class TradeModels extends PageListBase<TradeModel> {

    private final static Logger logger = Logger.getLogger(TradeModels.class);

    /**
     *
     */
    private static final long serialVersionUID = -3853056878814449520L;

    private Map<String, String> fieldMapping;

    private Map<String, String> memoMapping;

    private Map<String, Object> fieldDefaultMap;

    private Integer needOrder;
    /**
     * 是否需要 count false：不需要 null/true： 需要
     */
    private Boolean ifNeedCount;

    private SplitResult splitResult;

    /**
     * 强唯一码类型的商品
     */
    private List<String> uniqueCodeItemList;

    private String hintMessage;

    private Map<String, String> tipMsgMap;

    private FxOperateResult fxOperateResult;

    private Boolean hasNext;

    private Map<String, List<Map<String, Object>>> itemCodeMap;


    /**
     * key: userId
     * value: shop
     */
    private static final Cache<Long, Shop> SHOP_CACHE = CacheBuilder.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    public Map<String, List<Map<String, Object>>> getItemCodeMap() {
        return itemCodeMap;
    }

	private String querySuggests;

    public void setItemCodeMap(Map<String, List<Map<String, Object>>> itemCodeMap) {
        this.itemCodeMap = itemCodeMap;
    }

    public static TradeModels toEmpty() {
        TradeModels models = new TradeModels();
        models.setPage(new Page());
        models.setTotal(0L);
        models.setHasNext(false);
        return models;
    }

    public static TradeModels toTradeModels(Staff staff, Trades tbTrades, IShopService shopService, IExpressCompanyService expressSer, boolean highlight, String text, String highlightTid, TradeConfig tradeConfig) {
        String keyWords = getKeyWords(highlight, text, highlightTid);
        return toTradeModels(staff, tbTrades, shopService, expressSer, highlight, keyWords, tradeConfig, true, 0);
    }

    public static TradeModels toTradeModels(Staff staff, Trades tbTrades, IShopService shopService, IExpressCompanyService expressSer, boolean highlight, String keywords, TradeConfig tradeConfig) {
        return toTradeModels(staff, tbTrades, shopService, expressSer, highlight, keywords, tradeConfig, true, 0);
    }


    public static TradeModels toTradeModels(Staff staff, Trades tbTrades, IShopService shopService, IExpressCompanyService expressSer, boolean highlight, String keywords, TradeConfig tradeConfig, Integer useCompress) {
        return toTradeModels(staff, tbTrades, shopService, expressSer, highlight, keywords, tradeConfig, true, useCompress);
    }

	public static TradeModels   toTradeModels(Staff staff, Trades tbTrades, IShopService shopService,IExpressCompanyService expressSer, boolean highlight, String keywords, TradeConfig tradeConfig, boolean toBaseTradeModel, Integer useCompress){
		return  toTradeModels(staff,tbTrades,shopService,expressSer,highlight,keywords,tradeConfig,toBaseTradeModel,useCompress,null);
	}

	public static TradeModels   toTradeModels(Staff staff, Trades tbTrades, IShopService shopService,IExpressCompanyService expressSer, boolean highlight, String keywords, TradeConfig tradeConfig, boolean toBaseTradeModel, Integer useCompress,TradeModels models){
		if (models == null) {
			models = new TradeModels();
		}
		models.setPage(tbTrades.getPage());
		models.setSort(tbTrades.getSort());
		models.setTotal(tbTrades.getTotal());
		models.setHasNext(tbTrades.getHasNext());

        List<Trade> trades = tbTrades.getList();
        if (CollectionUtils.isEmpty(trades)) {
            return models;
        }
        QueryLogBuilder logBuilder = new QueryLogBuilder(staff).append("toTradeModelsDetail(size:"+trades.size()+")耗时统计").startTimer();

        Set<Long> userIds = new HashSet<Long>();
        Set<Long> expressCompanyIds = new HashSet<Long>();
        HashMap<String, QiMenShop> qimenShopMap = new HashMap<>();
        logBuilder.reBaseTimer();
        for (Trade trade : trades) {
            Long userId = trade.getUserId();
            if (userId != null && userId > 0) {
                userIds.add(userId);
            }
            Long expressCompanyId = trade.getExpressCompanyId();
            if (expressCompanyId != null) {
                expressCompanyIds.add(expressCompanyId);
            }
			if (CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(trade.getSource())) {
				String uniqueKey = trade.getUserId() + trade.getSellerNick() + trade.getSubSource();
				QiMenShop qiMenShop = qimenShopMap.computeIfAbsent(uniqueKey, (key) ->
				{
					try {
						return shopService.getQiMenShop(staff, trade.getUserId(), trade.getSellerNick(), trade.getSubSource());
					} catch (Exception ex) {
						logger.warn(LogHelper.buildLog(staff, String.format("获取奇门店铺信息异常,店铺编号:%s,店铺名称:%s,平台编码:%s", trade.getUserId(), trade.getSellerNick(), trade.getSubSource())), ex);
					}
					return null;
				});
				if (null != qiMenShop) {
					trade.setQimenShopShortName(qiMenShop.getShortName());//奇门店铺简称
				}
            }
        }
        logBuilder.recordTimer("setQimenShopShortName");
        Map<Long, Shop> shops = getShops(staff, shopService, userIds);
        logBuilder.recordTimer("getShops");
        //此处批量查询需要用到的ExpressCompany
        Map<Long, ExpressCompany> expressCompanyMap = getExpressCompany(expressSer, expressCompanyIds);
        logBuilder.recordTimer("getExpressCompany");

        List<TradeModel> list = new ArrayList<TradeModel>(trades.size());
        int index = 1;
        for (Trade trade : trades) {
            logBuilder.reBaseTimer();
            Shop shop = shops.get(trade.getUserId());
            ExpressCompany expressCompany = expressCompanyMap.get(trade.getExpressCompanyId());
            try {
                if (trade.isDeleted()) {
                    //已删除的订单
                    list.add(TradeModel.toDeletedTradeModel(trade).setIndex(index++));
                } else if (toBaseTradeModel) {
                    list.add(TradeModel.toBaseTradeModel(staff, trade, shop, expressCompany, highlight, keywords, tradeConfig, useCompress).setIndex(index++).setWaybillCount(trade.getWaybillCount()));
                } else {
                    list.add(TradeModel.toReprintQueryTradeModel(staff, trade, shop, expressCompany, highlight, keywords, tradeConfig).setIndex(index++));
                }
            } catch (Exception e) {
                new QueryLogBuilder(staff).appendTrade(trade).appendError("toModel异常",e).printError(logger,e);
            }
            logBuilder.recordMutiTimer("toSingleModel");
        }
        logBuilder.startWatch().appendTook(DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV,staff.getCompanyId(), LogBusinessEnum.QUERY.getSign())?1L: (Math.min(trades.size()*5L,5000L))).multiPrintInfo(logger);
        models.setList(list);
        return models;
    }

    public static TradeModels toTradeModels(Staff staff, List<Trade> trades, IShopService shopService, IExpressCompanyService expressSer, boolean highlight, String keywords, String highlightTid, TradeConfig tradeConfig) {
        String keyWords = getKeyWords(highlight, keywords, highlightTid);
        return toTradeModels(staff, trades, shopService, expressSer, highlight, keyWords, tradeConfig, true);
    }

    private static String getKeyWords(boolean highlight, String keywords, String highlightTid) {
        String keyWords = keywords;
        if (highlight && StringUtils.isNotBlank(highlightTid)) {
            if (null == keyWords) {
                keyWords = highlightTid;
            } else {
                keyWords = keyWords + "," + highlightTid;
            }
        }
        return keyWords;
    }

    public static TradeModels toTradeModels(Staff staff, List<Trade> trades, IShopService shopService, IExpressCompanyService expressSer, boolean highlight, String keywords, TradeConfig tradeConfig) {
        return toTradeModels(staff, trades, shopService, expressSer, highlight, keywords, tradeConfig, true);
    }

    public static TradeModels toTradeModels(Staff staff, List<Trade> trades, IShopService shopService, IExpressCompanyService expressSer, boolean highlight, String keywords, TradeConfig tradeConfig, boolean toBaseTradeModel) {
        Set<Long> userIds = new HashSet<Long>();
        Set<Long> expressCompanyIds = new HashSet<Long>();

        for (Trade trade : trades) {
            Long userId = trade.getUserId();
            if (userId != null && userId > 0) {
                userIds.add(userId);
            }
            Long expressCompanyId = trade.getExpressCompanyId();
            if (expressCompanyId != null) {
                expressCompanyIds.add(expressCompanyId);
            }
        }

        //此处批量查询需要用到的Shop
        Map<Long, Shop> shopMap = getShops(staff, shopService, userIds);
        //此处批量查询需要用到的ExpressCompany
        Map<Long, ExpressCompany> expressCompanyMap = getExpressCompany(expressSer, expressCompanyIds);

        TradeModels models = new TradeModels();
        models.setPage(new Page().setPageNo(1).setPageSize(trades.size()));
        models.setTotal(trades.size() + 0L);
        if (toBaseTradeModel) {
            models.setList(getBaseTradeModelList(staff, trades, shopMap, expressCompanyMap, highlight, keywords, tradeConfig));
        } else {
            models.setList(getReprintTradeModelList(staff, trades, shopMap, expressCompanyMap, highlight, keywords, tradeConfig));
        }
        return models;
    }


    /**
     * toBaseTradeModel
     *
     * @param staff
     * @param trades
     * @param shopMap
     * @param expressCompanyMap
     * @param highlight
     * @param keywords
     * @param tradeConfig
     * @return
     */
    private static List<TradeModel> getBaseTradeModelList(Staff staff, List<Trade> trades, Map<Long, Shop> shopMap, Map<Long, ExpressCompany> expressCompanyMap, boolean highlight, String keywords, TradeConfig tradeConfig) {
        List<TradeModel> list = new ArrayList<TradeModel>(trades.size());
        for (Trade trade : trades) {
            list.add(TradeModel.toBaseTradeModel(staff, trade, shopMap.get(trade.getUserId()), expressCompanyMap.get(trade.getExpressCompanyId()), highlight, keywords, tradeConfig));
        }
        return list;
    }

    /**
     * toReprintQueryTradeModel
     *
     * @param staff
     * @param trades
     * @param shopMap
     * @param expressCompanyMap
     * @param highlight
     * @param keywords
     * @param tradeConfig
     * @return
     */
    private static List<TradeModel> getReprintTradeModelList(Staff staff, List<Trade> trades, Map<Long, Shop> shopMap, Map<Long, ExpressCompany> expressCompanyMap, boolean highlight, String keywords, TradeConfig tradeConfig) {
        List<TradeModel> list = new ArrayList<TradeModel>(trades.size());
        for (Trade trade : trades) {
            list.add(TradeModel.toReprintQueryTradeModel(staff, trade, shopMap.get(trade.getUserId()), expressCompanyMap.get(trade.getExpressCompanyId()), highlight, keywords, tradeConfig));
        }
        return list;
    }

    public static Map<Long, Shop> getShops(Staff staff, IShopService shopService, Set<Long> userIds) {
        userIds = userIds.stream().filter(id-> !Objects.equals(Constants.FxDefaultUserId,id)).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(userIds)) {
            return new HashMap<>();
        }
        Map<Long, Shop> shopMap = Maps.newHashMapWithExpectedSize(userIds.size());

        ImmutableMap<Long,Shop> shopImmutableMap = SHOP_CACHE.getAllPresent(userIds);
        if(MapUtils.isNotEmpty(shopImmutableMap)){
            shopMap.putAll(shopImmutableMap);
        }
        ImmutableSet<Long> hasExistsIds = shopImmutableMap.keySet();
        Set<Long> needLoadUserIds = userIds.stream().filter(o->!hasExistsIds.contains(o)).collect(Collectors.toSet());

        if(CollectionUtils.isEmpty(needLoadUserIds)){
            return shopMap;
        }

        List<Shop> shops = shopService.queryByUserIds(staff,needLoadUserIds.toArray(new Long[0]));

        if(CollectionUtils.isNotEmpty(shops)){
            shops.forEach(shop->{
                shopMap.put(shop.getUserId(),shop);
                SHOP_CACHE.put(shop.getUserId(),shop);
            });
        }

        return shopMap;
		/*
		//TODO 双11后改成批量
		Long[] userIdArr = new Long[userIds.size()];
		userIds.toArray(userIdArr);
		List<Shop> shops = shopService.queryByUserIds(staff,userIdArr);

		Map<Long, Shop> shopMap = new HashMap<Long, Shop>();
		for(Shop shop:shops){
			shopMap.put(shop.getUserId(),shop);
		}
		return shopMap;
		*/
    }

    private static Map<Long, ExpressCompany> getExpressCompany(IExpressCompanyService expressCompanyService, Set<Long> expressCompanyIds) {
        if (CollectionUtils.isEmpty(expressCompanyIds)) {
            return new HashMap<Long, ExpressCompany>();
        }

        Long[] expressCompanyIdsrr = new Long[expressCompanyIds.size()];
        expressCompanyIds.toArray(expressCompanyIdsrr);
        List<ExpressCompany> expressCompanyList = expressCompanyService.getExpressCompanyByIds(expressCompanyIdsrr);

        Map<Long, ExpressCompany> expressCompanyMap = new HashMap<Long, ExpressCompany>();
        for (ExpressCompany expressCompany : expressCompanyList) {
            expressCompanyMap.put(expressCompany.getId(), expressCompany);
        }
        return expressCompanyMap;
    }

    public static void fillTrade(Staff staff, List<PrintTradeLogDetailVO> detailVOList, Map<Long, Trade> tradeMap, TradeConfig tradeConfig) {
        for (PrintTradeLogDetailVO logDetail : detailVOList) {
            Trade trade = tradeMap.get(logDetail.getSid());
            if (trade == null) {
                logger.warn("TradeModels#fillTrade trade is null, sid:" + logDetail.getSid());
                continue;
            }
            logDetail.setTemplateName(trade.getTemplateName());
            logDetail.setSysStatus(TradeStatusUtils.convertSysStatus(trade, tradeConfig));
            logDetail.setExceptions(ExceptionStatusHelper.analyze(staff, trade, new TradeModel().setOrders(new ArrayList<>())));
        }
    }

    /**
     * build 一个空的TradeModels 保证数据格式避免前端出现问题
     *
     * @param needOrder 是否需要子单
     * @param params    查询入参
     */
    public static TradeModels buildNullModels(Integer needOrder, TradeQueryParams params) {
        TradeModels tradeModels = new TradeModels();
        tradeModels.setList(new ArrayList<>());
        tradeModels.setTotal(0L);
        tradeModels.setNeedOrder(needOrder);
        tradeModels.setPage(params.getPage());
        return tradeModels;
    }

    public Boolean getIfNeedCount() {
        return ifNeedCount;
    }

    public void setIfNeedCount(Boolean ifNeedCount) {
        this.ifNeedCount = ifNeedCount;
    }

    public Map<String, String> getFieldMapping() {
        return fieldMapping;
    }

    public void setFieldMapping(Map<String, String> fieldMapping) {
        this.fieldMapping = fieldMapping;
    }

    public Integer getNeedOrder() {
        return needOrder;
    }

    public void setNeedOrder(Integer needOrder) {
        this.needOrder = needOrder;
    }

    public Map<String, String> getMemoMapping() {
        return memoMapping;
    }

    public void setMemoMapping(Map<String, String> memoMapping) {
        this.memoMapping = memoMapping;
    }

    public Map<String, Object> getFieldDefaultMap() {
        return fieldDefaultMap;
    }

    public void setFieldDefaultMap(Map<String, Object> fieldDefaultMap) {
        this.fieldDefaultMap = fieldDefaultMap;
    }

    public SplitResult getSplitResult() {
        return splitResult;
    }

    public void setSplitResult(SplitResult splitResult) {
        this.splitResult = splitResult;
    }

    public List<String> getUniqueCodeItemList() {
        return uniqueCodeItemList;
    }

    public void setUniqueCodeItemList(List<String> uniqueCodeItemList) {
        this.uniqueCodeItemList = uniqueCodeItemList;
    }

    public Boolean getHasNext() {
        return hasNext;
    }

    public void setHasNext(Boolean hasNext) {
        this.hasNext = hasNext;
    }

    public String getHintMessage() {
        return hintMessage;
    }

    public void setHintMessage(String hintMessage) {
        this.hintMessage = hintMessage;
    }

    public FxOperateResult getFxOperateResult() {
        return fxOperateResult;
    }

    public void setFxOperateResult(FxOperateResult fxOperateResult) {
        this.fxOperateResult = fxOperateResult;
    }

    public Map<String, String> getTipMsgMap() {
        return tipMsgMap;
    }

    public void setTipMsgMap(Map<String, String> tipMsgMap) {
        this.tipMsgMap = tipMsgMap;
    }

	public String getQuerySuggests() {
		return querySuggests;
	}

	public void setQuerySuggests(String querySuggests) {
		this.querySuggests = querySuggests;
	}
}

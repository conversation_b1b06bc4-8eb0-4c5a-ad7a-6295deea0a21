package com.raycloud.dmj.web.controllers.backend;

import com.alibaba.fastjson.*;
import com.google.common.collect.*;
import com.kdzs.wly.domain.*;
import com.kdzs.wly.service.*;
import com.kdzs.wly.vo.*;
import com.raycloud.cache.*;
import com.raycloud.data.api.SyncUserRegistRequest;
import com.raycloud.data.api.dubbo.TestInfoContext;
import com.raycloud.dmj.*;
import com.raycloud.dmj.business.clear.*;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.conf.MigrateBusiness;
import com.raycloud.dmj.business.except.TradeRefundItemNumExceptBusines;
import com.raycloud.dmj.business.logistics.*;
import com.raycloud.dmj.business.modify.TradeCostCalculateBusiness;
import com.raycloud.dmj.business.operate.*;
import com.raycloud.dmj.business.orderext.OrderExtBusiness;
import com.raycloud.dmj.business.part3.TradeParty3Business;
import com.raycloud.dmj.business.payment.TradeSuitCalculateBusiness;
import com.raycloud.dmj.business.split.*;
import com.raycloud.dmj.business.split.support.SplitParams;
import com.raycloud.dmj.business.stock.*;
import com.raycloud.dmj.business.sync.TbTradeQueryBusiness;
import com.raycloud.dmj.business.sync.utils.RdsServerUtils;
import com.raycloud.dmj.business.trade.*;
import com.raycloud.dmj.business.warehouse.WarehouseAllocateBusiness;
import com.raycloud.dmj.business.wms.TradeWmsBusiness;
import com.raycloud.dmj.dao.JdbcQueryDao;
import com.raycloud.dmj.dao.backend.LogicClearDataDao;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.performance.TradePickDao;
import com.raycloud.dmj.dao.stock.StockOrderRecordDAO;
import com.raycloud.dmj.dao.trade.*;
import com.raycloud.dmj.dao.wave.WaveRuleDao;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.*;
import com.raycloud.dmj.domain.diamond.*;
import com.raycloud.dmj.domain.diamond.trade.TradeSyncConfig;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.gift.GiftPromotion;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.party3.TradeParty3BusinessTypeEnum;
import com.raycloud.dmj.domain.performance.TradePick;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockOrderRecord;
import com.raycloud.dmj.domain.trade.except.ExceptHandlerDto;
import com.raycloud.dmj.domain.trade.split.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.oSActivity.*;
import com.raycloud.dmj.domain.trades.params.PageColumnConfUpdateParam;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.user.*;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.diamond.TradeSyncConfigUtils;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.jd.trade.business.JdTradeDownloader;
import com.raycloud.dmj.services.backend.*;
import com.raycloud.dmj.services.context.ProjectContextEnum;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.ec.ScanTimeoutActionListener;
import com.raycloud.dmj.services.ec.oSActivity.*;
import com.raycloud.dmj.services.gift.IGiftPromotionService;
import com.raycloud.dmj.services.gift.adapter.IGiftRuleAdapter;
import com.raycloud.dmj.services.gift.extend.GiftPromotionExtendBusiness;
import com.raycloud.dmj.services.logistics.TradeLogisticsTrackingAdapter;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.pt.smart_match.IExpressMatchEngine;
import com.raycloud.dmj.services.trade.audit.TradeAuditDubboBusiness;
import com.raycloud.dmj.services.trade.split.ITradeSplitService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.fill.ITradeFillService;
import com.raycloud.dmj.services.trades.im.business.*;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.trades.support.search.OrderHotItemService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.services.utils.item.TradeItemContext;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.tb.common.TbAppInfo;
import com.raycloud.dmj.tb.trade.*;
import com.raycloud.dmj.tb.trade.business.*;
import com.raycloud.dmj.utils.CommonSecretUtils;
import com.raycloud.dmj.web.interceptors.RequestBodyParamsUtils;
import com.raycloud.dmj.web.utils.*;
import com.raycloud.domain.RegistUser;
import com.raycloud.ec.api.*;
import com.raycloud.erp.db.migrate.MigrationGroup;
import com.raycloud.erp.db.router.jdbc.JdbcTemplateAdapter;
import com.raycloud.erp.trade.search.db.*;
import com.raycloud.secret_api.api.SecretRequest;
import com.raycloud.sync.api.DubboResponse;
import org.apache.commons.collections.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.*;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trade.config.TradeConfigEnum.TRADE_ITEM_COST_CONFIG;


/**
 * 订单后台控制器
 *
 * <AUTHOR>
 */
@Controller()
@RequestMapping("/trade/backend")
@Scope("prototype")
// TODO 先暂时暴露出接口，上线之后需要加强安全方面的校验
public class TradeBackendController extends BackendController {

    private static final Set<String> REFUND_ACTIONS = new HashSet<String>();

    private static final String LOGISTICS_UPDATE_KEY = "LOGISTICS_UPDATE_KEY";
    private static final Integer DEFAULT_PAGE_SIZE = 50000;

    private static final Integer SYNC_LABEL_PAGE_SIZE = 500;

    public static List<String> CONFIG_HOLDER_FIELD_LIST = Lists.newArrayListWithExpectedSize(8);

    static {
        ReflectionUtils.doWithFields(ConfigHolder.class,(fc)-> CONFIG_HOLDER_FIELD_LIST.add(fc.getName()));
    }

    static {
        REFUND_ACTIONS.add("WAIT_SELLER_AGREE");
        REFUND_ACTIONS.add("SUCCESS");
        REFUND_ACTIONS.add("CLOSED");
        REFUND_ACTIONS.add("SELLER_CONTINUE_CONSIGN");
        REFUND_ACTIONS.add("SELLER_REFUSE_BUYER");
    }

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService solrTradeSearchService;
    @Resource
    private ITradeTraceService tradeTraceService;


    @Resource
    private ScanTimeoutActionListener scanTimeoutActionListener;

    @Resource
    private TbTradeQueryBusiness tbTradeQueryBusiness;

    @Resource(name = "itemMatchBusiness")
    private ITradeBusiness itemMatchBusiness;

    @Resource
    private ISysTradeService sysTradeService;

    @Resource
    private OptimizeBusiness optimizeBusiness;

    @Resource
    private ClearAllDataBusiness clearAllDataBusiness;

    @Resource
    private Clear3MonthBeforeDataBusiness clear3MonthBeforeDataBusiness;

    @Resource
    private ITradeUpdateService tradeUpdateService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private TbTradeAccess tbTradeAccess;

    @Resource
    TbTradeCopier tbTradeCopier;

    @Resource
    private JdTradeDownloader jdTradeDownloader;

    @Resource
    private ITradeBatchService tradeBatchService;

    @Resource
    private JdbcTemplateAdapter jdbcTemplateAdapter;

    @Resource
    private ITradeDownloadService tradeDownloadService;

    @Resource
    private ITradeInspectService tradeInspectService;

    @Resource
    private ITradeRecoverService tradeRecoverService;

    @Resource
    private TbTradeDao tbTradeDao;

    @Resource
    private TbOrderDAO tbOrderDAO;

    @Resource
    private LogicClearDataDao logicClearDataDao;

    @Resource
    OrderExtBusiness orderExtBusiness;

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    private ICache cache;

    @Resource
    private TaobaoSecretBusiness taobaoSecretBusiness;

    @Resource
    private TradeAllCompanyDealer tradeAllCompanyDealer;
    @Resource
    private ITradeCancelExceptService tradeCancelExceptService;

    @Resource
    private ConsignStockRetryBusiness consignStockRetryBusiness;

    @Resource
    private TradeStockCheckBusiness tradeStockCheckBusiness;

    @Resource
    private IOrderStockService orderStockService;

    @Resource
    private ILogisticsQueryService logisticsQueryService;

    @Resource
    private ILogisticsSubService logisticsSubService;

    @Resource
    private ITradeImportService tradeImportService;

    @Resource
    private TradeWmsBusiness tradeWmsBusiness;

    @Resource
    private SyncTradeBusiness syncTradeBusiness;

    @Resource
    private ITradeConfigService tradeConfigService;

    @Resource
    private MigrateBusiness migrateBusiness;

    @Resource
    private SecretRequest secretRequest;

    @Resource
    private IGiftRuleAdapter giftRuleAdapter;

    @Resource
    private SyncUserRegistRequest syncUserRegisterService;

    @Autowired(required = false)
    private SyncUserRegistRequest syncUserRegisterServiceNewAppKey;

    @Resource
    private Configurable config;

    @Resource
    private IItemServiceDubbo itemServiceDubbo;

    @Resource
    TradeSeparationBusiness tradeSeparationBusiness;
    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    @Resource
    ITradeSplitService tradeSplitService;
    @Resource
    ITradeWaveService tradeWaveService;
    @Resource
    WaveRuleDao waveRuleDao;

    @Resource
    private IPickBackService pickBackService;
    @Resource
    TradeStatCountBusiness tradeStatCountBusiness;
    @Resource
    TradeCostCalculateBusiness tradeCostCalculateBusiness;
    @Resource
    StaffAssembleBusiness staffAssembleBusiness;
    @Resource
    private ILogisticsTrackingRecordService logisticsTrackingRecordService;
    @Resource
    private TradeLogisticsTrackingAdapter tradeLogisticsTrackingAdapter;

    @Resource
    private TradeItemExcepBusiness tradeItemExcepBusiness;

    @Resource
    private OrderFixBusiness orderFixBusiness;

    @Resource
    IUserWlbExpressTemplateService userWlbExpressTemplateService;

    @Resource
    JdbcQueryDao jdbcQueryDao;

    @Resource
    TradePerformanceCalBusiness tradePerformanceCalBusiness;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    TradePickDao tradePickDao;
    @Resource
    ITradeFillService tradeFillService;

    @Resource
    private ITradeCheckGoodsService tradeCheckGoodsService;

    @Resource
    private StockOrderRecordDAO stockOrderRecordDAO;

    @Resource
    private RepairOrderBusiness repairOrderBusiness;

    @Resource
    private IWmsService wmsService;

    @Resource
    IItemServiceDubbo itemServiceDubboImpl;

    @Resource
    private TradeStatusUploadBusiness tradeStatusUploadBusiness;

    @Resource
    private ITradeService tradeService;

    @Resource
    private UploadRetryBusiness uploadRetryBusiness;

    @Resource
    private TbTradeDecryptBusiness tbTradeDecryptBusiness;

    @Resource
    private UniqueCodeScanRecordDao uniqueCodeScanRecordDao;

    @Resource
    private CommonTradeDecryptBusiness commonTradeDecryptBusiness;


    @Resource
    private SecretBusiness secretBusiness;

    @Resource
    TbSpiderPlatformServiceImpl tbSpiderPlatformService;

    private final Logger logger = Logger.getLogger(this.getClass());

    private static final String TRADE_SYNC_LIST = "trade_sync_list";
    @Resource
    private LogisticBusiness logisticBusiness;

    @Resource
    ITradeItemPackService tradeItemPackService;

    @Resource
    private TradeParty3Business tradeParty3Business;

    @Resource
    private GrossProfitBusiness grossProfitBusiness;
    @Resource
    private TradeRefundItemNumExceptBusines tradeRefundItemNumExceptBusines;

    @Resource
    private SplitAllInOneBusiness splitAllInOneBusiness;

    @Resource
    TradeSuitCalculateBusiness tradeSuitCalculateBusiness;

    @Resource
    public CostFixBusiness costFixBusiness;

    @Resource
    TradeConfigStockBusiness tradeConfigStockBusiness;
    @Resource
    private TradeOrderStockCheckBusiness tradeOrderStockCheckBusiness;

    @Resource
    TradeAuditDubboBusiness tradeAuditDubboBusiness;

    @Resource
    OrderHotItemService orderHotItemService;

    @RequestMapping(value = "/index")
    public String index() {
        if (IpUtils.isSelfVisitor(request)) {
            return "trade-index";
        } else {
            return "error";
        }
    }

    @RequestMapping(value = "/host")
    @ResponseBody
    public Object getHost(String api_name) {
        return IpUtils.getHostName();
    }

    @RequestMapping(value = "/index/migrate", method = RequestMethod.POST)
    @ResponseBody
    public Object migrateTradeIndex(String companyIds, String type, String api_name) throws Exception {
        Staff[] staffs = getStaffs(companyIds);
        Map<Long, Long> result = new HashMap<Long, Long>(staffs.length, 1);
        for (Staff staff : staffs) {
            if ("stat".equals(type)) {
                result.put(staff.getCompanyId(), tradeStatCountBusiness.migrate(staff));
            } else if ("itemExcep".equals(type)) {
                result.put(staff.getCompanyId(), tradeItemExcepBusiness.migrate(staff));
            } else if ("useNewQuery".equals(type)) {
                result.put(staff.getCompanyId(), orderFixBusiness.migrate(staff));
            } else {
                result.put(staff.getCompanyId(), tradeSeparationBusiness.migrate(staff));
            }
        }
        return result;
    }


    /**
     * 分开写，防止手动执行时手抖执行错了
     *
     * @param companyIds
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/index/useNewQuery/switch", method = RequestMethod.POST)
    @ResponseBody
    public Object switchUserNewQuery(String companyIds, String api_name, boolean value) throws Exception {
        Staff[] staffs = (getStaffs(companyIds));
        for (Staff staff : staffs) {
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            tradeConfig.setUseNewQuery(value);
            tradeConfigService.update(staff, "use_new_query", value);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("用户%s switch useNewQuery数据成功,useNewQuery切换为%s", staff.getCompanyId(), value)));
            }
        }
        return "yes";
    }


    /**
     * 分开写，防止手动执行时手抖执行错了
     *
     * @param companyIds
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/index/itemExcepOpen/switch", method = RequestMethod.POST)
    @ResponseBody
    public Object switchItemExcepOpen(String companyIds, String api_name, int value) throws Exception {
        Staff[] staffs = (getStaffs(companyIds));
        for (Staff staff : staffs) {
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            tradeConfig.setItemExcepOpen(1);
            tradeConfigService.update(staff, "item_excep_open", value);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("用户%s switch itemExcepOpen数据成功,itemExcepOpen切换%s", staff.getCompanyId(), value)));
            }
        }
        return "yes";
    }

    @RequestMapping(value = "/index/clear", method = RequestMethod.POST)
    @ResponseBody
    public Object clearIndex(String companyIds, String api_name) throws Exception {
        Staff[] staffs = getStaffs(companyIds);
        for (Staff staff : staffs) {
            try {
                tradeSeparationBusiness.clear(staff, false);
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "清理数据出错"), e);
            }
        }
        return "yes";
    }

    @RequestMapping(value = "/index/repair", method = RequestMethod.POST)
    @ResponseBody
    public Object repairIndex(String companyIds, Integer v, String sids, String type, String api_name) throws Exception {
        Staff[] staffs = getStaffs(companyIds);
        for (Staff staff : staffs) {
            if (StringUtils.isNotBlank(sids)) {
                Long[] sidArr = Strings.getAsLongArray(sids, ",", true);
                if ("stat".equals(type)) {
                    tradeStatCountBusiness.repair(staff, sidArr);
                } else {
                    tradeSeparationBusiness.repair(staff, sidArr);
                }
            } else {
                tradeSeparationBusiness.repairAll(staff, v);
            }
        }
        return "yes";
    }

    private Staff[] getStaffs(String companyIds) throws Exception {
        Set<Long> companyIdSet = Strings.getAsLongSet(companyIds, ",", true);
        if (companyIdSet.isEmpty()) {
            return new Staff[]{getLightStaff()};
        }
        Set<Staff> staffSet = new HashSet<Staff>(companyIdSet.size());
        for (Long companyId : companyIdSet) {
            Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
            if (staff != null) {
                staffSet.add(staff);
            }
        }
        return staffSet.toArray(new Staff[0]);
    }

    @RequestMapping(value = "/secret", method = RequestMethod.POST)
    @ResponseBody
    public Object secret(String str, String action) throws Exception {
        return "decode".equals(action) ? secretRequest.decode(str) : secretRequest.encode(str);
    }

    @RequestMapping(value = "/tradeImportTime/set", method = RequestMethod.POST)
    public @ResponseBody
    Object setTradeImportTime(String userIds, String date) {
        Date dateObj = com.raycloud.dmj.domain.trades.utils.DateUtils.str2Date(date, null);
        if (dateObj == null) {
            return "请指定新的时间";
        }
        List<Long> errors = new ArrayList<Long>();
        List<Long> userIdList = Strings.getAsLongList(userIds, ",", true);
        for (Long userId : userIdList) {
            User origin = userService.queryById(userId);
            if (origin == null) {
                errors.add(userId);
                continue;
            }
            User user = new User();
            user.setId(userId);
            user.setLastImportTradeTime(dateObj);
            user.setNick(origin.getNick());
            user.setCompanyId(origin.getCompanyId());
            userService.updateUser((Staff) null, user);
        }
        return errors.isEmpty() ? successResponse() : errors;
    }

    @RequestMapping(value = "/invalidSession/set", method = RequestMethod.POST)
    public @ResponseBody
    Object setInvalidSession(String userIds, Integer value) {
        if (value == null) {
            return "请指定新的value";
        }
        List<Long> errors = new ArrayList<Long>();
        List<Long> userIdList = Strings.getAsLongList(userIds, ",", true);
        for (Long userId : userIdList) {
            User origin = userService.queryById(userId);
            if (origin == null) {
                errors.add(userId);
                continue;
            }
            User user = new User();
            user.setId(userId);
            user.setNick(origin.getNick());
            user.setCompanyId(origin.getCompanyId());
            user.setInvalidSession(value);
            userService.updateUser((Staff) null, user);
        }
        return errors.isEmpty() ? successResponse() : errors;
    }

    @RequestMapping(value = "/tradeImportTime/back", method = RequestMethod.POST)
    @ResponseBody
    public Object setActiveUserTradeImportTime(int days) {
        Page page = new Page().setPageNo(1).setPageSize(500);
        List<Company> list;
        Map<String, String> map = new HashMap<String, String>();
        Date sd = DateUtils.addDays(new Date(), -10);
        while (!(list = companyService.getActiveCompany(page, 3)).isEmpty()) {
            for (Company company : list) {
                List<User> users = userService.queryByCompanyId(company.getId());
                for (User user : users) {

                    if (user.getLastImportTradeTime() != null && user.getLastImportTradeTime().after(sd) && (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(user.getSource()) || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(user.getSource()))) {

                        User toUpdate = new User();
                        toUpdate.setId(user.getId());
                        toUpdate.setNick(user.getNick());
                        toUpdate.setCompanyId(user.getCompanyId());
                        toUpdate.setLastImportItemTime(DateUtils.addDays(user.getLastImportTradeTime(), -1 * days));
                        userService.updateUser((Staff) null, user);
                        map.put(user.getCompanyId() + ", " + user.getId() + ", " + user.getNick(), com.raycloud.dmj.domain.trades.utils.DateUtils.date2Str(toUpdate.getLastImportTradeTime()));
                    }
                }
            }
            if (list.size() < page.getPageSize()) {
                break;
            }
            page.setPageNo(page.getPageNo() + 1);
        }
        return map;
    }

    @RequestMapping(value = "/import/cache/clear", method = RequestMethod.POST)
    @ResponseBody
    public Object clearImportCache(Long taobaoId, String sellerNick, String api_name) throws CacheException {
        User user = getUser(taobaoId, sellerNick);
        cleanImportCache(user);
        return successResponse();
    }


    /**
     * 查询隐藏订单有备注的主订单
     *
     * @param time
     * @param memo
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/queryHidenMemos")
    @ResponseBody
    public Object queryHidenMemos(@RequestParam(required = false, defaultValue = "false") Boolean simple, @RequestParam(required = false) String time, @RequestParam(required = false) String memo) throws SessionException {
        if (StringUtils.isEmpty(time)) {
            time = "2017-11-11 00:00:00";
        }
        if (StringUtils.isEmpty(memo)) {
            memo = "退款";
        }
        memo = "%" + memo + "%";

        Staff staff = getStaff();
        String sql = String.format(" SELECT %s FROM trade_%d t WHERE company_id=? AND (seller_flag is null or seller_flag=0)  AND seller_memo not LIKE ? AND t.enable_status=1 " +
                        " AND pay_time>=? AND exists(select 1 from trade_%d t1 WHERE t1.company_id=? AND t1.enable_status=2  AND (t1.seller_flag>0 or seller_memo LIKE ? )  AND t1.merge_sid=t.sid  AND t1.pay_time>=?) ",
                simple ? "t.sid" : "t.*", staff.getDbInfo().getTradeDbNo(), staff.getDbInfo().getTradeDbNo());
        return jdbcTemplateAdapter.get(staff).queryForList(sql, new Object[]{staff.getCompanyId(), memo, time, staff.getCompanyId(), memo, time});
    }


    @RequestMapping(value = "/cache", method = RequestMethod.POST)
    @ResponseBody
    public Object handleCache(String action, String key, String value, String api_name) throws Exception {
        if ("get".equalsIgnoreCase(action)) {
            return cache.get(key);
        }
        if ("set".equalsIgnoreCase(action)) {
            Assert.isTrue(value != null, "请设置要缓存的值");
            String[] vs = value.split(":");
            int expires = 86400;//默认有效时间为一天
            String type = "string";
            if (vs.length <= 1) {
                return cache.set(key, value, expires);
            } else if (vs.length == 2 && vs[1].matches("\\d+")) {
                expires = Integer.parseInt(vs[1]) * 60 * 60;//value:expire
            } else if (vs.length >= 3) {
                type = vs[1];
                expires = Integer.parseInt(vs[2]) * 60 * 60;//value:type:expire
            }
            if ("string".equalsIgnoreCase(type)) {
                return cache.set(key, vs[0], expires);
            } else if ("int".equalsIgnoreCase(type)) {
                return cache.set(key, Integer.parseInt(vs[0]), expires);
            } else if ("long".equalsIgnoreCase(type)) {
                return cache.set(key, Long.parseLong(vs[0]), expires);
            } else if ("double".equalsIgnoreCase(type)) {
                return cache.set(key, Double.parseDouble(vs[0]), expires);
            } else if ("boolean".equalsIgnoreCase(type)) {
                return cache.set(key, Boolean.parseBoolean(vs[0]), expires);
            }
        }
        if ("remove".equalsIgnoreCase(action)) {
            return cache.delete(key);
        }
        if ("touch".equalsIgnoreCase(action)) {
            return cache.getAndTouch(key, Integer.parseInt(value));
        }
        if ("incr".equals(action)) {
            cache.incr(key, Long.valueOf(value));
            return cache.get(key);
        }
        if ("keys".equals(action)) {
            Set<String> keys = cache.keys();
            List<String> result = new ArrayList<>();
            if (StringUtils.isBlank(key)) {
                return keys;
            }
            for (String s : keys) {
                if (s.contains(key)) {
                    result.add(s);
                }
            }
            return result;
        }
        throw new IllegalArgumentException("暂不支持此action: " + action + "!");
    }

    @RequestMapping(value = "/sync/switch", method = RequestMethod.POST)
    @ResponseBody
    public Object updateTradeSyncStatus(Long companyId, Long userId, Integer value, String api_name) {
        tradeConfigService.updateTradeSyncStatus(companyId, userId, value);
        return successResponse();
    }

    @RequestMapping(value = "/optimize", method = RequestMethod.POST)
    @ResponseBody
    public Object optimizeTable(Integer dbNo, String tables, Integer kind, String api_name) throws Exception {
        String[] tableList = Strings.getAsStringArray(tables, ",", true);
        if (kind == null || kind - ********* != 0) {
            Assert.isTrue(tableList.length > 0, "请输入要优化的表名");
            optimizeBusiness.optimizeTable(dbNo, tableList);
        } else {
            eventCenter.fireEvent(this, new EventInfo("trade.table.optimize").setArgs(new Object[]{dbNo}), null);
        }
        return successResponse();
    }

    /**
     * 清除公司的订单数据
     *
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/clear", method = RequestMethod.POST)
    @ResponseBody
    public Object clearTradeData(Long companyId, String api_name) throws Exception {
        Assert.notNull(companyId, "传入公司id");
        Staff staff = getStaff();
        Assert.isTrue(staff.getCompanyId().equals(companyId), "当前会话公司不是要清空数据的公司");
        if (staff.getUsers().size() == 0) {
            return successResponse();
        }
        clearAllDataBusiness.clearTradeData(staff);
        return successResponse();
    }


    /**
     * 清除一个月前已作废的订单
     *
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/clearCancelTradeData", method = RequestMethod.POST)
    @ResponseBody
    public Object clearCancelTradeData(Long companyId, Long userId,String api_name) throws  Exception {

        JSONObject result = new JSONObject();
        Staff staff = companyId != null ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
        Assert.notNull(staff, "获取不到对应的staff");
        Assert.notNull(userId, "传入店铺Id");

        Query q = new Query();
        q.append("SELECT ").append(" sid ").append(" FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" WHERE company_id = ? AND enable_status > 0 AND created < DATE_ADD(NOW(), INTERVAL -1 MONTH) AND is_cancel = 1 ").add(staff.getCompanyId());
        if(!Objects.isNull(userId)){
            q.and().append(" user_id=? ").add(userId);
        }
        Page page = new Page().setPageSize(1000).setPageNo(1);
        q.append(" LIMIT ?, ? ").add(page.getStartRow()).add(page.getPageSize());
        List<TbTrade> list;
        int r = 0;
        while (!(list = jdbcQueryDao.queryList(staff.getDbNo(), TbTrade.class, q.getQ().toString(), q.getArgs().toArray())).isEmpty()) {
            try {

                if(CollectionUtils.isEmpty(list)){
                    break;
                }
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("sid=%s", TradeUtils.toSidList(list))));
                }

                String inStr = TradeUtils.toSidList(list).stream().map(String::valueOf).collect(Collectors.joining(",", "sid in (", ")"));

                logicClearDataDao.clearByTable(staff, "trade_" + staff.getDbInfo().getTradeDbNo(), inStr);
                logicClearDataDao.clearByTable(staff, "trade_not_consign_" + staff.getDbInfo().getTradeDbNo(), inStr);
                logicClearDataDao.clearByTable(staff, "trade_stat_" + staff.getDbInfo().getTradeDbNo(), inStr);
                //清空子订单
                logicClearDataDao.clearByTable(staff, "order_" + staff.getDbInfo().getOrderDbNo(), inStr);
                logicClearDataDao.clearByTable(staff, "order_not_consign_" + staff.getDbInfo().getOrderDbNo(), inStr);

                r += list.size();

                if (list.size() < page.getPageSize()) {
                    break;
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, String.format("清除已作废的订单[companyId=%s]", staff.getCompanyId())));
            } finally {
                page.setPageNo(page.getPageNo() + 1);
            }
        }
        result.put("total",r);
        return result;
    }

    /**
     * 清除3个月前绩效流水日志
     *
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/clear/before/optlog", method = RequestMethod.POST)
    @ResponseBody
    public Object clear3MonthBeforeOptLog(Long companyId, String api_name) throws Exception {
        Staff staff = companyId != null ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
        clear3MonthBeforeDataBusiness.clearPerformanceOptLogData(staff);
        return successResponse();
    }

    /**
     * 清除公司3个月前的订单数据
     *
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/clear/before", method = RequestMethod.POST)
    @ResponseBody
    public Object clear3MonthBefore(Long companyId, String api_name) throws Exception {
        Staff staff = companyId != null ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
        clear3MonthBeforeDataBusiness.clearAll(staff);
        return successResponse();
    }

    /**
     * 清除公司3个月前的订单数据
     *
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/clear/before/all", method = RequestMethod.POST)
    @ResponseBody
    public Object clear3MonthBeforeAll(String api_name) throws Exception {
        return tradeAllCompanyDealer.dealWithAllCompanys(staff -> {
            clear3MonthBeforeDataBusiness.clearAll(staff);
            return successResponse();
        });
    }

    @RequestMapping(value = "/clear/old", method = RequestMethod.POST)
    @ResponseBody
    public Object clearOld(String companyId, Integer oldDbNo, String api_name) throws Exception {
        Long[] companyIds = Strings.getAsLongArray(companyId, ",", true);
        Map<Long, Boolean> result = new HashMap<>();
        for (Long cid : companyIds) {
            Staff staff = staffService.queryDefaultStaffByCompanyId(cid);
            Integer dbNo = staff.getDbNo();
            staff.getCompany().getProfile().setDbNo(oldDbNo);
            try {
                result.put(cid, clearAllDataBusiness.clearAll(staff, false));
            } finally {
                staff.getCompany().getProfile().setDbNo(dbNo);
            }
        }
        return result;
    }

    /**
     * 清除已过期公司所有数据
     */
    @RequestMapping(value = "/clear/all/company", method = RequestMethod.POST)
    @ResponseBody
    public Object clearCompanyAll(String companyIdStr, String api_name) throws Exception {
        Assert.notNull(companyIdStr, "请输入公司id");
        Long[] companyIds = ArrayUtils.toLongArray(companyIdStr);
        List<Company> companys = companyService.querComapanyListByIds(companyIds);
        Assert.notNull(companys, String.format("找不到公司信息[companyIds=%s]", companyIdStr));
        Assert.isTrue(companys.size() > 0, String.format("找不到公司信息[companyIds=%s]", companyIdStr));
        List<Staff> staffs = tradeAllCompanyDealer.getStaffs(companys);
        Assert.notNull(staffs, String.format("找不到员工信息[companyIds=%s]", companyIdStr));
        Assert.isTrue(companys.size() > 0, String.format("找不到员工信息[companyIds=%s]", companyIdStr));
        for (Staff staff : staffs) {
            try {
                clearAllDataBusiness.clearAll(staff, true);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, String.format("清理公司数据出错[companyId=%s]", staff.getCompanyId())));
            }
        }
        return successResponse();
    }

    @RequestMapping(value = "/event/fire", method = RequestMethod.POST)
    @ResponseBody
    public Object fireEvent(String event, String companyId, String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(event), "请传入event参数");
        List<Long> companyIds = Strings.getAsLongList(companyId, ",", true);
        for (Long cid : companyIds) {
            Staff staff = staffAssembleBusiness.getDefaultStaff(cid);
            eventCenter.fireEvent(this, new EventInfo(event).setArgs(new Object[]{staff}), null);
        }
        return successResponse();
    }

    @RequestMapping(value = "/clear/before/data", method = RequestMethod.POST)
    @ResponseBody
    public Object clearBeforeData(String event, String companyIds, String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(event), "请传入event参数");
        Assert.isTrue(StringUtils.isNotEmpty(companyIds), "请传入companyIds参数");
        eventCenter.fireEvent(this, new EventInfo(event).setArgs(new Object[]{getStaff()}), Strings.getAsLongList(companyIds, ",", true));
        return successResponse();
    }

    @RequestMapping(value = "/buffer/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateBuffer(String oldType, String newType) {
        List<Integer> dbs = config.getCurrentDbs();
        Map<Integer, Integer> map = new HashMap<Integer, Integer>(dbs.size(), 1);
        for (int dbNo : dbs) {
            if (jdbcTemplateAdapter.get(dbNo) != null) {
                map.put(dbNo, jdbcTemplateAdapter.get(dbNo).update("UPDATE erp_buffer SET type = ? WHERE type = ?", newType, oldType));
            }
        }
        return map;
    }

    @RequestMapping(value = "/event/fire/wms", method = RequestMethod.POST)
    @ResponseBody
    public Object fireEventWms(Long companyId, String eventName, String noDepotOrderIds) throws SessionException {
        if (StringUtils.isEmpty(noDepotOrderIds)) {
            throw new IllegalArgumentException("noDepotOrderIds不能为空");
        }

        Staff staff = companyId != null && companyId > 0L ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
        tradeWmsBusiness.sendEvent(staff, eventName, ArrayUtils.toLongList(noDepotOrderIds));
        return successResponse();
    }

    /**
     * 修改订单状态，只改数据
     *
     * @param trade
     */
    @RequestMapping(value = "/modify", method = RequestMethod.POST)
    public
    @ResponseBody
    Object modifyTrade(@RequestBody TbTrade trade,Boolean ifUpdateOrderExt) throws SessionException {
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, trade);
        Assert.notNull(trade);
        Assert.notNull(trade.getSid(), "请设置sid");
        List<Order> updateOrders = TradeUtils.getOrders4Trade(trade);
        for (Order order : updateOrders) {
            Assert.notNull(order.getId(), "请设置子订单的id");
        }
        List<Trade> updateTrades = new ArrayList<Trade>();
        updateTrades.add(trade);

        tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
        if(BooleanUtils.isTrue(ifUpdateOrderExt)){
            List<Order> hasOrderExtList = TradeUtils.getOrders4Trade(trade).stream().filter(order -> order.getOrderExt() != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hasOrderExtList)) {
                orderExtBusiness.updateOrderExt(staff, hasOrderExtList);
            }
        }

        Trade dbTrade = solrTradeSearchService.queryBySid(staff, true, trade.getSid());

        TradeUtils.setTradeExcep(staff,dbTrade);
        TradeStockUtils.resetTradeStockStatus(staff, dbTrade, tradeConfigService.get(staff));
        dbTrade.setSysStatus(TradeStatusUtils.getTradeSysStatus(dbTrade));
        dbTrade.setCost(TradeUtils.calculateCost(dbTrade));
        dbTrade.setSaleFee(TradeUtils.calculateTradeSaleFee(dbTrade));
        dbTrade.setNetWeight(TradeUtils.calculateTradeNetWeight(dbTrade));
        dbTrade.setSource(TradeUtils.getTradeSource(dbTrade));
        tradeUpdateService.updateTrades(staff, dbTrade);
        return successResponse();
    }

    /**
     * 模拟退款的事件
     *
     * @param tid
     * @param oid
     * @param refundAction
     * @param api_name
     * @return
     */
    @RequestMapping(value = "/refund/simulator", method = RequestMethod.POST)
    @ResponseBody
    public Object simulateRefund(String tid, Long oid, Long refundId, String refundAction, String api_name) throws SessionException {
        Assert.notNull(tid, "请输入sid参数");
        Assert.notNull(oid, "请输入oid参数");
        Assert.notNull(refundId, "请输入refundId参数");
        Assert.notNull(refundAction, "请输入refundAction参数");
        Assert.isTrue(REFUND_ACTIONS.contains(refundAction), "refundAction只支持:" + REFUND_ACTIONS);
        Staff staff = getStaff();

        TbTrade refundTrade = null;
        List<TbTrade> tbTrades = tbTradeDao.queryByTids(staff, tid);
        Assert.isTrue(tbTrades.size() != 0, String.format("根据[tid=%s]找不到订单！", tid));
        if (tbTrades.size() == 1) {
            refundTrade = tbTrades.get(0);
        } else {
            for (TbTrade tbTrade : tbTrades) {
                if (tbTrade.getSplitSid().longValue() == tbTrade.getSid().longValue()) {
                    refundTrade = tbTrade;
                    break;
                }
            }
        }
        Assert.notNull(refundTrade, String.format("根据[tid=%s]找不到订单！", tid));

        List<TbOrder> tbOrders = tbOrderDAO.queryByTids(staff, tid);
        for (Order _order : tbOrders) {
            if (oid.longValue() == _order.getOid()) {
                _order.setRefundId(refundId + "");
                _order.setRefundStatus(refundAction);
                break;
            }
        }

        refundTrade.setOrders(tbOrders);

        Assert.notNull(refundTrade, "找不到子订单oid:" + oid);
        User user = staff.getUserByTaobaoId(refundTrade.getTaobaoId());
        Assert.notNull(user, "您无权操作此店铺的订单数据");
        List<Trade> refundTrades = new ArrayList<Trade>();
        refundTrades.add(refundTrade);
        TradeImportResult result = new TradeImportResult();
        tradeBatchService.importToDB(user, refundTrades, result);
        return result;
    }

    /**
     * 触发当前公司下所有的订单同步
     *
     * @param api_name
     * @return
     */
    @RequestMapping(value = "/sync/all", method = RequestMethod.POST)
    @ResponseBody
    public Object syncAll(String api_name) throws SessionException {
        Staff staff = getStaff();
        eventCenter.fireEvent(this, new EventInfo("trade.sync").setArgs(new Object[]{staff}), null);
        logger.warn(LogHelper.buildLogHead(staff).append("触发了订单同步事件").append(JSON.toJSONString(staff.getUser().getUserConf())));
        return successResponse();
    }

    /**
     * 指定下载
     *
     * @param userId
     * @param tids
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/sync/single/batch", method = RequestMethod.POST)
    @ResponseBody
    public void syncSingleBatch(Long userId, String tids, String api_name) throws SessionException {
        Assert.notNull(userId, "请传入userId编号");
        Staff staff = getStaff();
        User user = staff.getUserIdMap().get(userId);
        Assert.notNull(user, "您没有所选店铺的权限");
        Assert.isTrue(user.getActive() != null && user.getActive() != CommonConstants.JUDGE_NO, String.format("所选店铺已停用[userId:%s]", user.getId()));
        Assert.isTrue(user.getUserConf().isInitItem(), String.format("该店铺商品未完成初始化,不能指定下载[userId:%s,nick:%s]", user.getId(), user.getNick()));
        String[] tidArr = tids.split(",");
        TradeImportResult result = new TradeImportResult();
        if(TradeSyncConfigUtils.canUpdatePaymentUserIds(userId)){
            result.setCanUpdatePayment(true);
        }
        for (String tid : tidArr) {
            try {
//                sysTradeService.syncTradeByTid(user, tid);
                syncTradeBusiness.syncTradeByTid(user, tid, result, true);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, String.format("订单下载失败[tid=%s]", tid)));
            }

        }
    }


    /**
     * 根据tid下载订单，根据配置走是否覆盖逻辑
     * spider 会调用该接口
     * @param userId
     * @param tids
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/sync/trade/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object syncTradeCoverOrNot(Long userId, String tids, String api_name) throws SessionException {
        Assert.notNull(userId, "请传入userId编号");
        Staff staff = getStaff();
        User user = staff.getUserIdMap().get(userId);
        Assert.notNull(user, "您没有所选店铺的权限");
        Assert.isTrue(user.getActive() != null && user.getActive() != CommonConstants.JUDGE_NO, String.format("所选店铺已停用[userId:%s]", user.getId()));
        Assert.isTrue(user.getUserConf().isInitItem(), String.format("该店铺商品未完成初始化,不能指定下载[userId:%s,nick:%s]", user.getId(), user.getNick()));

        boolean forceUpdate = false;
        TradeConfig tradeConfig = tradeConfigService.get(user.getStaff());
        //覆盖模式
        if ((tradeConfig.getCoverSysStatus() == null || tradeConfig.getCoverSysStatus() == 1)) {
            forceUpdate = true;
        }
        TradeImportResult result = new TradeImportResult();
        if(TradeSyncConfigUtils.canUpdatePaymentUserIds(userId)){
            result.setCanUpdatePayment(true);
            forceUpdate = true;
        }
        syncTradeBusiness.syncTradeByTid(user, tids, result, forceUpdate);
        return Status.buildSuccessStatus();
    }


    /**
     * 按时间同步订单
     *
     * @param userId
     * @param start
     * @param end
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/sync/fullapi", method = RequestMethod.POST)
    @ResponseBody
    public Object syncTrades(Long userId, String start, String end) throws Exception {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("请指定要同步订单的店铺！");
        }
        Staff staff = getStaff();
        User user = staff.getUserByUserId(userId);
        if (user == null) {
            throw new IllegalArgumentException("您没有该店铺的权限！");
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(user).append(String.format("同步订单[%s, %s, %s, %s]", user.getId(), user.getNick(), start, end)));
        }
        return tradeImportService.importTrades(user, com.raycloud.dmj.domain.trades.utils.DateUtils.str2Date(start, null), com.raycloud.dmj.domain.trades.utils.DateUtils.str2Date(end, null), null, TradeConstants.SYNC_FULL_API);
    }

    /**
     * 查看平台订单原始数据
     *
     * @param userId
     * @param tid
     * @param rds
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/sync/single", method = RequestMethod.POST)
    @ResponseBody
    public Object syncSingleTrade(Long userId, String tid, Integer rds, String api_name) throws Exception {
        Assert.notNull(userId, "请传入userId编号");
        Assert.notNull(tid, "请传入tid编号");
        Staff staff = getStaff();
        User user = staff.getUserIdMap().get(userId);
        Assert.notNull(user, "您没有所选店铺的权限");
        Assert.isTrue(user.getActive() != null && user.getActive().intValue() != CommonConstants.JUDGE_NO, "所选店铺已停用");

        if (rds == null) {
            rds = 0;
        }

        if (CommonConstants.PLAT_FORM_TYPE_JD.equals(user.getSource())) {//京东
            return jdTradeDownloader.get(user, tid, null);
        } else if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(user.getSource()) || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(user.getSource())) {
            List<Long> tids = new ArrayList<Long>();
            tids.add(Long.parseLong(tid));
            if (rds == 1) {
                return tbTradeQueryBusiness.rdsQueryByTids(user, tids);
            } else {
                com.taobao.api.domain.Trade trade = tbTradeAccess.getFullinfoTrade(user, Long.parseLong(tid), null).getTrade();
                TbTrade copyTrade = tbTradeCopier.copy(trade, TradeUtils.initTradeUserInfo(new TbTrade(), user));
                taobaoSecretBusiness.decode(user, copyTrade);
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("tb", trade);
                map.put("sys", copyTrade);
                return map;
            }
        } else {
            return tradeDownloadService.downloadTrade(user, tid);
        }
    }

    /**
     * 注册rds后台接口
     *
     * @param userId
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/rds/register", method = RequestMethod.POST)
    @ResponseBody
    public Object registerUser(Long companyId, Long userId, String api_name) throws Exception {
        Assert.notNull(userId, "请传入userId编号");
        Staff staff = companyId != null ? staffAssembleBusiness.getDefaultStaff(companyId) : getStaff();
        User user = staff.getUserIdMap().get(userId);
        Assert.notNull(user, "您没有所选店铺的权限");

        registRefundMessage(user);
        RegistUser ru = new RegistUser();
        ru.setAppkey(TbAppInfo.getAppMap(user).get("APPKEY"));
        ru.setAppSecret(TbAppInfo.getAppMap(user).get("APPSECRET"));
        ru.setExpireTime(user.getDeadline() == null ? DateUtils.addMonths(new Date(), 1) : user.getDeadline());
        ru.setSessionkey(user.getSessionId());
        ru.setNick(user.getNick());
        ru.setTaobaoId(user.getTaobaoId());
        beforeInvoke();
        DubboResponse<Boolean> rsp = RdsServerUtils.getServices(user, syncUserRegisterService, syncUserRegisterServiceNewAppKey).regist(ru);
        if (rsp.getSuccess() != null && rsp.getSuccess()) {
            user.getUserConf().setLastRegistTradeSyncTime(new Date());
            userService.updateUser(user.getStaff().getCompanyId(), new User().setId(user.getId()).setUserConf(user.getUserConf()));
            return successResponse();
        } else {
            StringBuilder log = LogHelper.buildLogHead(user).append(String.format("店铺[%s, %s, %s]注册到RDS同步模块失败[%s, %s]", user.getSource(), user.getId(), user.getNick(), rsp.getCode(), rsp.getErrorMsg()));
            logger.error(log);
            throw new RuntimeException(log.toString());
        }
    }

    public void beforeInvoke() {//dubbo安全过滤器
        TestInfoContext.getContext().setThreadLocalBizId(CommonConstants.BIZ_ID);
        TestInfoContext.getContext().setThreadLocalBizSec(CommonConstants.BIZ_SEC);
        TestInfoContext.getContext().setAppKey(TbAppInfo.APPKEY);
    }


    /**
     * 注册退款消息，和用户注册放在同一个方法中进行
     */
    private void registRefundMessage(User user) {
        try {

            String needTopics = TbAppInfo.APPKEY.equals(TbAppInfo.getAppMap(user).get("APPKEY")) ? (ConfigConst.TB_MESSAGE_TOPICS + "," + ConfigConst.TB_MESSAGE_OS_TOPICS) : ConfigConst.TB_MESSAGE_TOPICS;
            String[] topics = needTopics.split(",");
            tbSpiderPlatformService.notifyRegister(user.getStaff(), user, topics);

        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(user).append(String.format("[userId=%s,nick=%s]注册淘宝退款消息出错", user.getId(), user.getNick())), e);
        }
    }


    /**
     * 模拟生成平台订单
     *
     * @param userId
     * @param count
     * @param numIidStr
     * @param skuIdStr
     * @param num
     * @param status
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/simulation/sync", method = RequestMethod.POST)
    @ResponseBody
    public Object syncTrades(Long userId, Integer count, String numIidStr, String skuIdStr, Integer num, String status, String type, Long sellerFlag) throws SessionException {
        Assert.notNull(count, "请输入count参数");
        Assert.notNull(num, "请输入num参数");
        Assert.isTrue(count <= 1000, "一次性最多生成1000笔");
        Assert.notNull(userId, "请输入userId参数");

        Assert.notNull(numIidStr, "请输入numIidStr参数");
        Assert.notNull(skuIdStr, "请输入skuIdStr参数");
        Staff staff = getStaff();
        User user = staff.getUserIdMap().get(userId);
        Assert.notNull(user, "输入的userId不正确");

        if (status == null) {
            status = TradeStatus.TB_WAIT_SELLER_SEND_GOODS;
        }
        if (type == null) {
            type = "fixed";
        }
        if (sellerFlag == null) {
            sellerFlag = 0L;
        }
        String[] numIids = ArrayUtils.toStringArray(numIidStr);
        String[] skuIds = ArrayUtils.toStringArray(skuIdStr);

        List<Trade> trades = createTrades(staff, user, count, numIids, skuIds, num, status, type, sellerFlag);

        tradeBatchService.importToDB(user, trades, new TradeImportResult());
        if (count > 100) {
            return trades.subList(0, 100);
        } else {
            return trades;
        }
    }

    /**
     * 模拟生成跨境平台订单
     *
     * @param userId
     * @param count
     * @param numIidStr
     * @param skuIdStr
     * @param num
     * @param status
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/simulation/kuaj/sync", method = RequestMethod.POST)
    @ResponseBody
    public Object syncKuajTrades(Long userId, Integer count, String numIidStr, String skuIdStr, Integer num, String status, String type, Long sellerFlag) throws SessionException {
        Assert.notNull(count, "请输入count参数");
        Assert.notNull(num, "请输入num参数");
        Assert.isTrue(count <= 1000, "一次性最多生成1000笔");
        Assert.notNull(userId, "请输入userId参数");

        Assert.notNull(numIidStr, "请输入numIidStr参数");
        Assert.notNull(skuIdStr, "请输入skuIdStr参数");
        Staff staff = getStaff();
        User user = staff.getUserIdMap().get(userId);
        Assert.notNull(user, "输入的userId不正确");

        if (status == null) {
            status = "kuaj_wish-APPROVED";
        }

        if (sellerFlag == null) {
            sellerFlag = 2L;
        }
        String[] numIids = ArrayUtils.toStringArray(numIidStr);
        String[] skuIds = ArrayUtils.toStringArray(skuIdStr);

        List<Trade> trades = createKuajTrades(staff, user, count, numIids, skuIds, num, status, type, sellerFlag);

        tradeBatchService.importToDB(user, trades, new TradeImportResult());
        if (count > 100) {
            return trades.subList(0, 100);
        } else {
            return trades;
        }
    }

    private List<Trade> createTrades(Staff staff, User user, int count, String[] numIids, String[] skuIds, int num, String status, String type, long sellerFlag) {
        List<Trade> trades = new ArrayList<Trade>();
        for (int i = 0; i < count; i++) {
            trades.add(createTrade(staff, user, numIids, skuIds, num, status, type, sellerFlag, i));
        }
        return trades;
    }

    private List<Trade> createKuajTrades(Staff staff, User user, int count, String[] numIids, String[] skuIds, int num, String status, String type, long sellerFlag) {
        List<Trade> trades = new ArrayList<Trade>();
        for (int i = 0; i < count; i++) {
            trades.add(createKuajTrade(staff, user, numIids, skuIds, num, status, type, sellerFlag, i));
        }
        return trades;
    }

    private Trade createTrade(Staff staff, User user, String[] numIids, String[] skuIds, int num, String status, String type, long sellerFlag, int i) {
        TbTrade trade = new TbTrade();
        trade.setCompanyId(staff.getCompanyId());
        trade.setSource(user.getSource());
        trade.setTaobaoId(user.getTaobaoId());
        trade.setUserId(user.getId());
        trade.setTid("" + Math.abs(UUID.randomUUID().getLeastSignificantBits()));
        trade.setStatus(status);
        trade.setModified(new Date());
        trade.setTotalFee("100");
        trade.setHasPostFee(1L);
        trade.setPostFee("10.00");
        trade.setNum(numIids.length);
        trade.setActualPostFee("10.00");
        trade.setAdjustFee("0.00");
        trade.setDiscountFee("10.00");
        trade.setBuyerNick("测试最棒" + i);
        trade.setPayment("100");
        trade.setReceiverName("无邪");
        trade.setReceiverMobile("13777369222");
        trade.setReceiverState("浙江省");
        trade.setReceiverCity("杭州市");
        trade.setReceiverDistrict("滨江区");
        trade.setReceiverAddress("滨江区江南大道588号恒鑫大厦15F");
        trade.setReceiverZip("000000");
        trade.setCompanyId(staff.getCompanyId());
        trade.setCreated(new Date());
        trade.setPayTime(new Date());
        trade.setType(type);
        trade.setSellerFlag(sellerFlag);
        trade.setBuyerMessage("测试使用，不能上传平台");
        TradeUtils.getOrders4Trade(trade).addAll(createOrders(staff, user, trade.getTid(), numIids, skuIds, num, status));
        return trade;
    }


    private Trade createKuajTrade(Staff staff, User user, String[] numIids, String[] skuIds, int num, String status, String type, long sellerFlag, int i) {
        TbTrade trade = new TbTrade();
        trade.setCompanyId(staff.getCompanyId());
        trade.setSource(user.getSource());
        trade.setTaobaoId(user.getTaobaoId());
        trade.setUserId(user.getId());
        trade.setTid("" + Math.abs(UUID.randomUUID().getLeastSignificantBits()));
        trade.setStatus(status);
        trade.setModified(new Date());
        trade.setHasPostFee(1L);
        trade.setNum(numIids.length);
        trade.setSubSource("kj_wish");
        trade.setActualPostFee("10.00");
        trade.setAdjustFee("0.00");
        trade.setDiscountFee("10.00");
        trade.setBuyerNick("不要上传平台" + i);
        trade.setReceiverName("决明");
        trade.setReceiverMobile("13777369222");
        trade.setReceiverCountry("US");
        trade.setReceiverState("Alabama");
        trade.setReceiverCity("Andalusia");
        trade.setReceiverAddress("310 Fletcher Rd.");
        trade.setReceiverZip("40361");
        trade.setCompanyId(staff.getCompanyId());
        trade.setCreated(new Date());
        trade.setPayTime(new Date());
        trade.setType(type);
        trade.setSellerFlag(sellerFlag);
        trade.setBuyerMessage("测试使用，不能上传平台");
        List<Order> orders = createOrders(staff, user, trade.getTid(), numIids, skuIds, num, status);
        TradeUtils.getOrders4Trade(trade).addAll(orders);
        int totalNum = 0;
        for (Order order : orders) {
            totalNum += order.getNum();
        }
        trade.setTotalFee((100 * totalNum + 10 * orders.size()) + "");
        trade.setTaxFee("10");
        trade.setPostFee("10.00");
        trade.setPayment((100 * totalNum + 10 + 10 - 10) + "");
        return trade;
    }


    List<Order> createOrders(Staff staff, User user, String tid, String[] numIids, String[] skuIds, Integer num, String status) {
        List<Order> orders = new ArrayList<Order>(numIids.length);
        for (int i = 0; i < numIids.length; i++) {
            Order order = new TbOrder();
            order.setTaobaoId(user.getTaobaoId());
            order.setUserId(user.getId());
            order.setNum(num);
            order.setNumIid(numIids[i]);
            order.setSkuId(skuIds[i]);
            order.setPayTime(new Date());
            order.setCreated(new Date());
            order.setRefundStatus("NO_REFUND");
            order.setTitle("跨境平台商品名称");
            order.setPicPath("http://img04.taobaocdn.com/bao/uploaded/i4/*********/TB2FGM0oXXXXXcZXpXXXXXXXXXX_!!*********.jpg");
            order.setPrice("100");
            order.setTotalFee((100 * Integer.valueOf(order.getNum()) + 10) + "");
            order.setPayment((100 * Integer.valueOf(order.getNum())) + "");
            order.setDiscountFee("10");
            order.setSource(user.getSource());
            order.setModified(new Date());
            order.setTid(tid);
            order.setStatus(status);
            order.setOid(Long.parseLong(tid) + i);
            order.setTaobaoId(user.getTaobaoId());
            order.setCompanyId(staff.getCompanyId());
            orders.add(order);
        }
        return orders;
    }

    /**
     * 模拟同步在系统中已经存在的订单
     *
     * @param tradeFront
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/sync/update", method = RequestMethod.POST)
    @ResponseBody
    public Object syncUpdateTrades(@RequestBody TbTrade tradeFront, String api_name) throws SessionException {
        Assert.notNull(tradeFront.getUserId(), "请输入userId参数");
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, tradeFront);
        User user = staff.getUserIdMap().get(tradeFront.getUserId());
        Assert.notNull(user, "输入的userId不正确");

        String tid = tradeFront.getTid();
        Assert.notNull(tid, "请输入sid参数");

        List<TbTrade> tbTrades = tbTradeDao.queryByTids(staff, tid);
        int size = tbTrades.size();
        Assert.isTrue(size != 0, String.format("根据[tid=%s]找不到订单", tid));

tradeFillService.fill(staff, tbTrades);
        TbTrade tbTrade = null;

        if (size == 1) {//没有拆单
            tbTrade = tbTrades.get(0);
        } else {//拆单
            for (TbTrade trade : tbTrades) {
                if (tbTrade.getSid().longValue() == tbTrade.getSplitSid().longValue()) {
                    tbTrade = trade;
                    break;
                }
            }
        }
        Assert.notNull(tbTrade, String.format("根据[tid=%s]找不到订单", tid));
        //处理订单
        handleUpdateTrade(tbTrade, tradeFront);

        List<TbOrder> tbOrders = tbOrderDAO.queryByTids(staff, tid);
        List<TbOrder> orders = new ArrayList<TbOrder>();
        for (TbOrder tbOrder : tbOrders) {
            if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(tbOrder)) {
                continue;
            }
            if (tbOrder.getType() == 1) {
                continue;
            }
            if (tbOrder.getType() == 2 && tbOrder.getCombineId() > 0) {
                continue;
            }
            orders.add(tbOrder);
        }
        Assert.isTrue(orders.size() >= 1, String.format("根据[tid=%s]找不到对应的子订单", tid));
        //处理子订单
        handleUpdateOrder(orders, tradeFront);

        tbTrade.setOrders(orders);
        List<Trade> trades = new ArrayList<Trade>();
        tbTrade.setAddressChanged(0);
        tbTrade.setModified(new Date());
        trades.add(tbTrade);
        tradeBatchService.importToDB(user, trades, new TradeImportResult());
        return tbTrade;
    }

    private void handleUpdateTrade(TbTrade tbTrade, TbTrade tradeFront) {
        tbTrade.setSid(null);
        tbTrade.setSysStatus(null);
        //订单平台状态
        if (StringUtils.isNotEmpty(tradeFront.getStatus())) {
            tbTrade.setStatus(tradeFront.getStatus());
        }
        //地址
        if (StringUtils.isNotEmpty(tradeFront.getReceiverAddress())) {
            tbTrade.setReceiverAddress(tradeFront.getReceiverAddress());
        }
    }

    private void handleUpdateOrder(List<TbOrder> tbOrders, TbTrade tradeFront) {
        if (tradeFront.getOrders() == null && tradeFront.getOrders().size() == 0) {
            for (TbOrder tbOrder : tbOrders) {
                tbOrder.setId(null);
                tbOrder.setSysStatus(null);
                if (StringUtils.isNotEmpty(tradeFront.getStatus())) {
                    tbOrder.setStatus(tradeFront.getStatus());
                }
            }
            return;
        }
        Map<Long, Order> id_order_front = OrderUtils.toMap(tradeFront.getOrders());
        for (TbOrder tbOrder : tbOrders) {
            Order orderFront = id_order_front.get(tbOrder.getId());
            tbOrder.setId(null);
            tbOrder.setSysStatus(null);
            if (orderFront == null) {
                continue;
            }

            if (StringUtils.isNotEmpty(orderFront.getStatus())) {
                tbOrder.setStatus(orderFront.getStatus());
            }

            if (StringUtils.isNotEmpty(orderFront.getNumIid())) {
                tbOrder.setNumIid(orderFront.getNumIid());
            }

            if (StringUtils.isNotEmpty(orderFront.getSkuId())) {
                tbOrder.setSkuId(orderFront.getSkuId());
            }

            if (StringUtils.isNotEmpty(orderFront.getRefundId())) {
                tbOrder.setRefundId(orderFront.getRefundId());
            }

            if (StringUtils.isNotEmpty(orderFront.getRefundStatus())) {
                tbOrder.setRefundStatus(orderFront.getRefundStatus());
            }
        }
    }

    @Resource
    IExpressMatchEngine expressMatchEngine;
    @Resource
    WarehouseAllocateBusiness warehouseAllocateBusiness;

    @RequestMapping(value = "/sync/inner", method = RequestMethod.POST)
    public @ResponseBody
    Object matchSysItem(String api_name, String sids, Long userId) throws Exception {
        Staff staff = getStaff();
        if (StringUtils.isNotBlank(sids)) {
            Long[] sidArr = Strings.getAsLongArray(sids, ",", true);
            List<Trade> trades = solrTradeSearchService.queryBySidsContainMergeTrade(staff, true, true, sidArr);
            innerSync(staff, trades);
        } else if (userId != null && userId > 0) {
            User user = staff.getUserByUserId(userId);
            if (user == null) {
                return "[userId=" + userId + "]店铺不存在";
            }
            Date now = new Date();
            Query q = new Query().append("company_id = ?").add(staff.getCompanyId());
            q.append(" AND sys_status = ? AND stock_status = ?").add(Trade.SYS_STATUS_WAIT_AUDIT).add(Trade.STOCK_STATUS_UNALLOCATED);
            q.append(" AND enable_status > 0 AND is_cancel = 0 AND upd_time < ? ").add(now);
            Page page = new Page().setPageNo(1).setPageSize(100);
            List<TbOrder> list;
            while (!(list = tbOrderDAO.queryOrders(staff, null, q, page)).isEmpty()) {
                List<Trade> trades = solrTradeSearchService.queryBySidsContainMergeTrade(staff, true, true, OrderUtils.toSids(list));
                innerSync(staff, trades);
                if (list.size() < page.getPageSize()) {
                    break;
                }
                page.setPageNo(page.getPageNo() + 1);
            }
        }
        return successResponse();
    }

    private void innerSync(Staff staff, List<Trade> trades) {
        Map<User, List<Trade>> tradesMap = new HashMap<User, List<Trade>>();
        for (Trade trade : trades) {
            User user = staff.getUserByUserId(trade.getUserId());
            if (user == null) {
                continue;
            }
            List<Trade> tradeList = tradesMap.get(user);
            if (tradeList == null) {
                tradeList = new ArrayList<Trade>();
                tradesMap.put(user, tradeList);
            }
            tradeList.add(trade);
        }
        for (Map.Entry<User, List<Trade>> entry : tradesMap.entrySet()) {
            tradeBatchService.importToDB(entry.getKey(), entry.getValue(), true);
        }
    }

    /**
     * 删除某笔订单
     *
     * @param sid
     * @param api_name
     * @return
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteTrade(String sid, Long userId, String api_name) throws SessionException {
        Assert.isTrue(StringUtils.isNotBlank(sid), "请传入系统单号！");
        Staff staff = getStaff();
        List<TbTrade> trades = tbTradeDao.queryBySids(staff, Strings.getAsLongArray(sid, ",", true));
        sysTradeService.deleteTrades(staff, trades);
        return successResponse();
    }

    private void delete(Staff staff, List<TbTrade> trades) {
        List<Trade> deleteList = new ArrayList<Trade>(trades.size());
        trades.forEach(trade -> {
            Trade toDelete = new TbTrade();
            toDelete.setSid(trade.getSid());
            toDelete.setCompanyId(staff.getCompanyId());
            toDelete.setEnableStatus(0);
            deleteList.add(toDelete);
        });
        List<TbOrder> orders = tbOrderDAO.queryBySids(staff, TradeUtils.toSids(trades));
        tradeUpdateService.updateTrades(staff, deleteList);
        tbOrderDAO.deleteByIds(staff, orders);
        orderStockService.resumeOrderStockLocal(staff, OrderUtils.toTree(orders), null);
    }

    /**
     * 对某个公司下的订单进行体检
     *
     * @param companyId
     */
    @RequestMapping(value = "/inspect", method = RequestMethod.POST)
    @ResponseBody
    public Object inspectTrades(Long companyId) {
        Assert.isTrue(null != companyId && companyId > 0, "请传入公司id");
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        Assert.isTrue(null != staff, "该公司不存在默认员工信息");
        List<User> users = userService.queryByCompanyId(companyId);
        staff.setUsers(users);
        for (User user : users) {
            user.setStaff(staff);
        }
        return tradeInspectService.inspectTrades(staff);
    }

    /**
     * 对某个公司下的订单进行修复
     *
     * @param companyId
     */
    @RequestMapping(value = "/recover", method = RequestMethod.POST)
    @ResponseBody
    public Object recoverTrades(Long companyId, String sids) {
        Assert.hasText(sids, "请传入系统订单号");
        Long[] sidArr = ArrayUtils.toLongArray(sids);
        Assert.isTrue(null != companyId && companyId > 0, "请传入公司id");
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        Assert.isTrue(null != staff, "该公司不存在默认员工信息");
        List<User> users = userService.queryByCompanyId(companyId);
        staff.setUsers(users);
        for (User user : users) {
            user.setStaff(staff);
        }
        TradeInspectResult result = new TradeInspectResult();
        for (int i = 0; i < sidArr.length; i++) {
            result.addSidInspectError(sidArr[i], TradeInspectErrorEnum.TRADE_ORDER_STOCK_STATUS_UNCONFORMITY);
        }
        return tradeRecoverService.recoverTrades(staff, result);
    }

    /**
     * 坏数据修复
     *
     * @param userId
     * @param tids
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/fail/data/repair", method = RequestMethod.POST)
    @ResponseBody
    public Object failDataRepair(Long userId, String tids, String api_name) throws Exception {
        Assert.notNull(userId, "请输入userId");
        Assert.notNull(tids, "请输入userId");
        Staff staff = getStaff();
        User user = staff.getUserByUserId(userId);
        Assert.notNull(user, "无权执行");
        String[] tidArr = ArrayUtils.toStringArray(tids);
        for (String tid : tidArr) {
            try {
                sysTradeService.syncTradeByTid(user, StringUtils.strip(tid));
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, String.format("订单下载失败[tid=%s]", tid)));
            }
        }
        return "success";
    }

    /**
     * 取消异常订单
     *
     * @param sids
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/exception/cancel", method = RequestMethod.POST)
    public @ResponseBody
    Object cancelExcep(String sids, String api_name) throws SessionException {
        Assert.notNull(sids, "请输入订单号");
        Staff staff = getStaff();
        ExceptHandlerDto exceptHandlerDto = new ExceptHandlerDto();
        exceptHandlerDto.setRecordTrace(false);
        exceptHandlerDto.setSids(ArrayUtils.toLongArray(sids));
        List<Trade> updated = tradeCancelExceptService.cancelExcept(staff, exceptHandlerDto);
        return solrTradeSearchService.queryBySids(staff, true, TradeUtils.toSids(updated));
    }

    @RequestMapping(value = "/stock/check", method = RequestMethod.POST)
    public @ResponseBody
    Object checkStock(String event, String api_name) throws SessionException {
        Staff staff = getStaff();
        if ("consign.stock.retry".equals(event)) {
            consignStockRetryBusiness.handleStock(staff);
        } else if ("consign.stock.handle".equals(event)) {
            consignStockRetryBusiness.deleteLockNum(staff);
        } else if ("stock.check".equals(event)) {
            tradeStockCheckBusiness.check(staff);
        }
        return successResponse();
    }

    @RequestMapping(value = "/stock/resume", method = RequestMethod.POST)
    @ResponseBody
    public Object resume(String sids, String api_name) throws Exception {
        Long[] sidArr = Strings.getAsLongArray(sids, ",", true);
        Staff staff = getStaff();
        List<Trade> trades = solrTradeSearchService.queryBySidsContainMergeTrade(staff, true, sidArr);
        orderStockService.resumeTradeStockLocal(staff, trades);
        return TradeUtils.toSids(trades);
    }

    /**
     * 触发发货提醒
     *
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/scan/timeout", method = RequestMethod.GET)
    public @ResponseBody
    Object scanTimeoutAction(String command) throws SessionException, CacheException {
        if ("on".equals(command)) {
            cache.delete("consign.remind.switcher");
        } else if ("off".equals(command)) {
            cache.set("consign.remind.switcher", 0, 0);
        } else {
            scanTimeoutActionListener.execute();
        }
        return successResponse();
    }


    /**
     * 清除地址变化日志
     *
     * @param sids
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/clear/address/change/log", method = RequestMethod.POST)
    public @ResponseBody
    Object clearAddressChangeLog(String sids, String api_name) throws SessionException {
        Staff staff = getStaff();


        StringBuilder updateSql = new StringBuilder("update address_change_log_");
        updateSql.append(staff.getDbInfo().getAddressChangeLogDbNo()).append(" set enable_status=0 where company_id=").append(staff.getCompanyId()).append(" and enable_status=1 ").append(" and sid in (").append(sids).append(")");
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(staff).append(String.format("清除地址变换日志,updateSql=%s", updateSql)));
        }
        try {
            jdbcTemplateAdapter.get(staff).execute(updateSql.toString());
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "清除地址变换日志异常"), e);
        }
        return successResponse();
    }


    /**
     * order存在,record不存在
     *
     * @return
     */
    @RequestMapping(value = "/handle/stock/no/record", method = RequestMethod.POST)
    @ResponseBody
    public Object handleNoRecord(Integer loopMax, Integer pageStart, Integer pageSize) throws SessionException {
        if (loopMax == null || loopMax < 1 || loopMax > 100) {
            loopMax = 10;
        }
        if (pageStart == null || pageStart < 0) {
            pageStart = 0;
        }

        if (pageSize == null || pageSize < 0 || pageSize > 500) {
            pageSize = 500;
        }
        Staff staff = getStaff();
        Integer dbNoRecord = staff.getDbInfo().getStockOrderRecordDbNo();
        Integer dbNoOrder = staff.getDbInfo().getOrderDbNo();
        Integer dbNoTrade = staff.getDbInfo().getTradeDbNo();
        Long companyId = staff.getCompanyId();
        String sql = "SELECT a.sid,a.id from order_" + dbNoOrder + " a inner join trade_" + dbNoTrade + " b on a.sid=b.sid and b.is_cancel=0 where a.company_id=" + companyId + " and a.is_virtual=0 and a.enable_status=1 and a.stock_status not in ('UNALLOCATED') and (a.sys_status in ('WAIT_AUDIT','FINISHED_AUDIT') or (a.sys_status='WAIT_BUYER_PAY' and a.sub_stock=0)) and not exists (select 1 from stock_order_record_" + dbNoRecord + " t where t.company_id=" + companyId + " and t.enable_status=1 and a.id=t.order_id) limit " + pageStart + "," + pageSize;
        int loop = 0;
        while (loop++ < loopMax) {
            List<Map<String, Object>> list = jdbcTemplateAdapter.get(staff).queryForList(sql);
            if (list == null || list.size() == 0) {
                break;
            }
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("order存在,record不存在sql=%s,orderIds=%s", sql, list)));
            }

            Set<Long> sids = new HashSet<Long>();
            List<Long> orderIds = new ArrayList<Long>();
            for (Map<String, Object> map : list) {
                sids.add(Long.parseLong(map.get("sid").toString()));
                orderIds.add(Long.parseLong(map.get("id").toString()));
            }
            List<Trade> trades = solrTradeSearchService.queryBySidsContainMergeTrade(staff, true, sids.toArray(new Long[sids.size()]));
            filterOrders(trades, orderIds);
            //直接调用取消异常的接口
            tradeCancelExceptService.cancelExcept(staff, trades,new ExceptHandlerDto());
            if (list.size() < pageSize) {
                break;
            }
        }

        return successResponse();
    }

    private void filterOrders(List<Trade> trades, List<Long> orderIds) {
        for (Trade trade : trades) {
            trade.setStockStatus(Trade.STOCK_STATUS_EMPTY);
            trade.setIsExcep(1);
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                if (orderIds.contains(order.getId())) {
                    order.setStockStatus(Trade.STOCK_STATUS_EMPTY);
                    order.setStockNum(0);
                }
                List<Order> suits = order.getSuits();
                if (suits == null || suits.size() == 0) {
                    continue;
                }
                for (Order suit : suits) {
                    if (orderIds.contains(suit.getId())) {
                        suit.setStockStatus(Trade.STOCK_STATUS_EMPTY);
                        suit.setStockNum(0);
                    }
                }
            }
        }
    }

    @Resource
    IGiftPromotionService giftPromotionService;
    @Resource
    TradeGiftMatchBusiness tradeGiftMatchBusiness;

    @RequestMapping("/gift/match")
    @Transactional
    public @ResponseBody
    Object matchGift(String sids, Long promotionId) throws Exception {
        Long[] sidArr = Strings.getAsLongArray(sids, ",", true);
        Staff staff = getStaff();
        List<Trade> trades = solrTradeSearchService.queryBySidsContainMergeTrade(staff, true, true, sidArr);
        GiftPromotion p = giftPromotionService.queryDetailById(staff, promotionId);
        if (p.getCurMatchTradeCount() == null) {
            p.setCurMatchTradeCount(0);
        }
        if (p.getCurMatchOrderCount() == null) {
            p.setCurMatchOrderCount(0);
        }
        List<GiftPromotion> promotions = new ArrayList<GiftPromotion>();
        promotions.add(p);
        giftRuleAdapter.adapter(staff, trades, promotions);
        tradeGiftMatchBusiness.matchGiftTradesAfter(staff, promotions, trades);
        List<Order> stockOrders = new ArrayList<Order>();
        Map<Long, Trade> tradeMap = new HashMap<Long, Trade>();
        for (Trade trade : trades) {
            if (CollectionUtils.isEmpty(trade.getGiftOrders())) {
                continue;
            }
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                //新增的赠品
                if (order.getType() != null && order.getType() - Order.TypeOfGiftOrder == 0 &&
                        (Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus()) || Trade.STOCK_STATUS_EXCEP.equals(order.getStockStatus()))) {
                    stockOrders.add(order);
                    tradeMap.put(trade.getSid(), trade);
                }
            }
        }
        List<Trade> updateTrades = new ArrayList<Trade>();
        if (!stockOrders.isEmpty()) {
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            orderStockService.applyOrderStockLocal(staff, stockOrders);
            for (Trade trade : tradeMap.values()) {
                TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
                Trade updateTrade = new TbTrade();
                updateTrade.setSid(trade.getSid());
                updateTrade.setStockStatus(trade.getStockStatus());
                TradeUtils.setTradeExcep(staff,trade, updateTrade);
                TradeUtils.resetTradeItemNum(trade, tradeConfig);
                updateTrade.setItemKindNum(trade.getItemKindNum());
                updateTrade.setItemNum(trade.getItemNum());
                updateTrades.add(updateTrade);
            }
            tradeUpdateService.updateTrades(staff, updateTrades, null, null, stockOrders);
          //  tradeExceptAdapter.saveExcept(staff, updateTrades, ExceptSignWayEnum.AUTO.getAutoSign());
        }
        return stockOrders.size() + ", " + updateTrades.size();
    }


    @Resource
    private GiftPromotionExtendBusiness giftPromotionExtendBusiness;

    @RequestMapping("/gift/match/query")
    public @ResponseBody
    Object matchGiftQuery(Long sid, Long userId) throws Exception {
        Staff staff = getStaff();
        Trade trade = solrTradeSearchService.queryBySid(staff, true, sid);

        List<GiftPromotion> giftPromotions = giftPromotionService.findGiftPromotionByShop(staff, trade.getUserId(), Collections.singletonList(trade.getBuyerNick()), new TradeImportResult());
        giftPromotionExtendBusiness.fillExtend(staff, giftPromotions, Collections.singletonList(trade.getBuyerNick()));
        //匹配赠品
        giftRuleAdapter.adapter(staff, Arrays.asList(trade), giftPromotions);

        return trade;
    }

    @RequestMapping("/migrate")
    public @ResponseBody
    Object migrate(Long companyId, Integer sourceDb, Integer targetDb, Integer async, String api_name) throws Exception {
        if (async == null || async - 1 != 0) {
            return migrateBusiness.migrate(staffService.queryDefaultStaffByCompanyId(companyId), sourceDb, targetDb);
        }
        eventCenter.fireEvent(this, new EventInfo("company.data.migrate").setArgs(new Object[]{companyId, sourceDb, targetDb}), null);
        return successResponse();
    }

    @RequestMapping("/migrate/group")
    public @ResponseBody
    Object migrateTable(@RequestBody MigrationGroup mg, String api_name) throws Exception {
        RequestBodyParamsUtils.setParams(getStaff(), mg);
        return migrateBusiness.migrate(mg);
    }

    @RequestMapping("/migrate/delete")
    public @ResponseBody
    Object deleteCompanyData(Integer dbNo, Long companyId, String api_name) throws Exception {
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        return migrateBusiness.clear(staff, dbNo);
    }

    @RequestMapping("/getLogisticsCompany")
    public @ResponseBody
    Object getLogisticsCompany(String type) {
        GetLogisticsCompanyResponse rsp = logisticsQueryService.getLogisticsCompany("ERP");
        logger.debug(JSON.toJSONString(rsp));
        return rsp;
    }

    @RequestMapping("/getLogisticsInfo")
    public @ResponseBody
    Object getLogisticsInfo(String cpCode, String outsid) {
        GetLogisticsInfoReq req = new GetLogisticsInfoReq();
        req.setCpCode(cpCode);
        req.setMailNo(outsid);
        req.setPlatformType("ERP");
        GetLogisticsInfoResponse rsp = logisticsQueryService.getLogisticsInfo(req);
        logger.debug(JSON.toJSONString(rsp));
        return rsp;
    }

    @RequestMapping("/getLogisticsInfoList")
    public @ResponseBody
    Object getLogisticsInfoList(String cpCode, String outsid) {
        GetLogisticsInfoReq req = new GetLogisticsInfoReq();
        req.setCpCode(cpCode);
        req.setMailNo(outsid);
        req.setPlatformType("ERP");
        QueryLogisticsInfoListResponse rsp = logisticsSubService.queryLogisticsInfoList(Lists.<GetLogisticsInfoReq>newArrayList(req));
        logger.debug(JSON.toJSONString(rsp));
        return rsp;
    }

    @RequestMapping("/subLogisticsTrace")
    public @ResponseBody
    Object subLogisticsInfo(@RequestBody SubLogisticsTraceReq req) throws Exception {
        RequestBodyParamsUtils.setParams(getStaff(), req);
//        Response rsp = logisticsSubService.subLogisticsTrace(req);
        Response rsp = new Response();
        rsp.setResult(100);
        req.setPlatformType("ERP");
        return rsp;
    }


    @RequestMapping("/updateLogisticsTrackingConfig")
    public Object updateLogisticsTrackingConfig(@RequestBody TradeConfig tradeConfig) throws SessionException {
        Staff staff = getStaff();
        RequestBodyParamsUtils.setParams(staff, tradeConfig);
        TradeConfig oldConfig = tradeConfigService.get(staff);
        tradeConfigService.update(staff, tradeConfig);
        if (1 == tradeConfig.getOpenLogisticsTracking() && (null == oldConfig.getOpenLogisticsTracking() || 0 == oldConfig.getOpenLogisticsTracking())) {
            //开启操作
            eventCenter.fireEvent(this, new EventInfo("trade.logistics.switch.on").setArgs(new Object[]{staff}), null);
        } else if (0 == tradeConfig.getOpenLogisticsTracking() && (null != oldConfig.getOpenLogisticsTracking() || 1 == oldConfig.getOpenLogisticsTracking())) {
            //关闭操作
            eventCenter.fireEvent(this, new EventInfo("trade.logistics.switch.off").setArgs(new Object[]{staff}), null);
        }
        return Status.buildSuccessStatus();
    }

    @RequestMapping("/tongji/trade")
    @ResponseBody
    public Object tongjiTrade() throws Exception {
        IpUtils.assertSelfVisitor(request);

        Staff staff = getStaff();
        int dbNoNum = 100;
        StringBuilder sql = new StringBuilder("select date_format(created,'%Y-%m-%d') date, COUNT(*) count from (select * from (\n");
        for (int i = 0; i < dbNoNum; i++) {
            sql.append("SELECT `created` FROM trade_").append(i).append(" where source not in( 'tb', 'tm', '1688', 'jd') and template_type = 1 AND enable_status = 1 and (created between '2018-03-06' and '2018-03-12') union all \n");
        }
        if (sql.lastIndexOf("union all \n") > -1) {
            sql.delete(sql.lastIndexOf("union all \n"), sql.length());
        }
        sql.append(") a) t group by date_format(created,'%Y-%m-%d') order by date_format(created,'%Y-%m-%d') desc;");
        List<Map<String, Object>> result = jdbcTemplateAdapter.get(staff).queryForList(sql.toString());
        logger.debug("统计交易订单数量为：" + JSON.toJSONString(result));
        return result;
    }

    /**
     * 添加沙箱店铺白订单
     */
    @RequestMapping(value = "/add/whiteMixSyncList", method = RequestMethod.POST)
    @ResponseBody
    public Object whiteList(String companyId, String action, String api_name) throws SessionException {
        if (StringUtils.isEmpty(companyId)) {
            throw new IllegalArgumentException("companyId参数不能为空");
        }
        String whiteMixSyncList = config.getProperty(TRADE_SYNC_LIST);
        if ("add".equals(action)) {
            if (StringUtils.contains(whiteMixSyncList, companyId)) {
                return successResponse();
            }
            config.setProperty(TRADE_SYNC_LIST, whiteMixSyncList + "," + companyId);
        } else if ("remove".equals(action)) {
            if (!StringUtils.contains(whiteMixSyncList, companyId)) {
                return successResponse();
            }
            List<String> list = Lists.newArrayList(StringUtils.split(whiteMixSyncList, ","));
            list.remove(companyId);
            config.setProperty(TRADE_SYNC_LIST, StringUtils.join(list, ","));
        }
        return Status.buildSuccessStatus();
    }

    @RequestMapping(value = "/order/cancel", method = RequestMethod.POST)
    @ResponseBody
    public Object handleCancelOrder(Long companyId, String api_name) {
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        Date before3Month = DateUtils.addMonths(new Date(), -3);
        StringBuilder sql = new StringBuilder("UPDATE order_").append(staff.getDbInfo().getOrderDbNo()).append(" t SET is_cancel = 1 WHERE company_id = ? AND created < ? AND enable_status = 1 AND SYS_STATUS IN(?, ?, ?) AND NOT EXISTS(SELECT sid FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" WHERE sid = t.sid)");
        return jdbcTemplateAdapter.get(staff).update(sql.toString(), companyId, before3Month, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT);
    }

    /**
     * 修改用户订单同步策略 一次修改有效时间为12小时
     *
     * @param userId
     * @param syncType
     * @param api_name
     * @return
     */
    @RequestMapping(value = "/sync/type/update", method = RequestMethod.POST)
    @ResponseBody
    public Object syncTypeUpdate(Long userId, Integer syncType, String api_name) {
        Assert.isTrue(syncType != null && (0 == syncType || 1 == syncType || 2 == syncType || 3 == syncType), "同步方式错误");
        tradeConfigService.updateTradeSyncType(userId, syncType);
        return userService.queryById(userId).getUserConf().getTradeSyncType();
    }

    /**
     * 修改用户订单同步策略 一次修改有效时间为12小时
     *
     * @param userIds
     * @param syncType
     * @param api_name
     * @return
     */
    @RequestMapping(value = "/sync/type/update/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object syncTypeUpdateBatch(String userIds, Integer syncType, String source, String api_name) {
        Assert.isTrue(StringUtils.isNotBlank(userIds)
                && StringUtils.isNotBlank(userIds.replaceAll(",", ""))
                && syncType != null
                && (0 == syncType || 1 == syncType || 2 == syncType || 3 == syncType), "参数错误");
        Set<Long> userIdSet = Strings.getAsLongSet(userIds, ",", false);
        Map<Long, Object> result = new HashMap<Long, Object>(userIdSet.size(), 1);
        for (Long userId : userIdSet) {
            tradeConfigService.updateTradeSyncType(userId, syncType, source);
            User user = userService.queryById(userId);
            if (user != null) {
                result.put(userId, user.getUserConf());
            }
        }
        return result;
    }

    @RequestMapping(value = "/handle/itemRepeat", method = RequestMethod.POST)
    @ResponseBody
    public Object handleItemRepeat(Long companyId, String sids, String api_name) {
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        Long[] sidArr = Strings.getAsLongArray(sids, ",", true);
        List<Trade> trades = solrTradeSearchService.queryBySids(staff, true, sidArr);
        List<Order> toDeletes = new ArrayList<Order>();
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            Map<Long, Order> orderMap = new HashMap<Long, Order>();
            for (Order order : orders) {
                if (TradeUtils.isTbTrade(order.getSource())) {
                    if (orderMap.containsKey(order.getOid())) {
                        toDeletes.add(order);
                    } else {
                        orderMap.put(order.getOid(), order);
                    }
                }
            }
        }
        if (!toDeletes.isEmpty()) {
            tbOrderDAO.deleteByIds(staff, toDeletes);
            orderStockService.resumeOrderStockLocal(staff, toDeletes, null);
        }
        return toDeletes.size();
    }


    @RequestMapping(value = "/masterUser/set", method = RequestMethod.POST)
    @ResponseBody
    public Object masterUserSet(Long companyId, Long userId, String api_name) {
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        User masterUser = userService.getMasterUser(staff.getCompanyId());
        JSONObject result = new JSONObject();
        if (masterUser == null) {
            User user = userService.queryById(userId);
            Assert.isTrue(user != null, "要修改的user未找到");
            User update = new User();
            update.setId(user.getId());
            update.setMasterUser(1);
            userService.updateUser(staff.getCompanyId(), update);
            result.put("user", update);
        }
        return result;
    }

    @RequestMapping(value = "/split/undo/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object splitUndoBatch(String splitSidStr, String api_name) throws SessionException {
        Assert.notNull(splitSidStr, "请输入splitSidStr");
        Long[] splitSidArr = ArrayUtils.toLongArray(splitSidStr);
        if (splitSidArr.length > 0) {
            Staff staff = getStaff();
            tradeSplitService.splitUndo(staff, splitSidArr, true);
        }
        return successResponse();
    }

    /**
     * 处理共享位置波次规则的数据
     *
     * @param type           处理类型（1：旧到新 2：新到旧）
     * @param companyIds
     * @param forceHandleAll
     * @return
     */
    @RequestMapping(value = "/sharepositionwaverule/transform", method = RequestMethod.POST)
    @ResponseBody
    public Object transformSharePositionWaveRule(final String type, String companyIds, Boolean forceHandleAll) {
        Assert.isTrue(IpUtils.isSelfVisitor(request), "无权限操作");
        Assert.isTrue(StringUtils.isNotBlank(companyIds) || Boolean.TRUE.equals(forceHandleAll), "请传入companyIds");
        Assert.isTrue("1".equals(type) || "2".equals(type), "请传入正确的处理类型");

        final Map<Long, Integer> result = new LinkedHashMap<Long, Integer>();

        doCompanyCallback(companyIds, new Callback<Company, Object>() {
            @Override
            public Object call(Company company) {
                Staff staff = assembleStaff(company);
                List<WaveRule> waveRules = tradeWaveService.queryRules(staff);

                if (CollectionUtils.isEmpty(waveRules)) {
                    return null;
                }

                List<WaveRule> waveRulesToUpdate = new ArrayList<WaveRule>(waveRules.size());
                for (WaveRule waveRule : waveRules) {
                    WaveRuleCondition ruleCondition = waveRule.getRuleCondition();
                    boolean sharePosition = BooleanUtils.isTrue(ruleCondition.getSharePosition());
                    if (!sharePosition) {
                        continue;
                    }

                    if ("1".equals(type)) {
                        // 如果已有ruleCondition.positionCodeNumRange说明已是新数据，不需要处理
                        if (null != ruleCondition.getPositionCodeNumRange()) {
                            continue;
                        }
                        NumberRange<Integer> positionCodeNumRange = new NumberRange<Integer>();
                        positionCodeNumRange.setMin(waveRule.getNumDown());
                        positionCodeNumRange.setMax(waveRule.getNumUp());
                        ruleCondition.setPositionCodeNumRange(positionCodeNumRange);
                        waveRule.setNumDown(WaveRule.DEFAULT_NUM_RANGE_MIN);
                        waveRule.setNumUp(WaveRule.DEFAULT_NUM_RANGE_MAX);
                        waveRulesToUpdate.add(assembleWaveRuleToUpdate(waveRule));
                    } else if ("2".equals(type)) {
                        NumberRange<Integer> positionCodeNumRange = ruleCondition.getPositionCodeNumRange();
                        // 如果没有有ruleCondition.positionCodeNumRange说明已是老数据，不需要处理
                        if (null == positionCodeNumRange) {
                            continue;
                        }
                        waveRule.setNumDown(WaveUtils.toInteger(positionCodeNumRange.getMin()));
                        waveRule.setNumUp(WaveUtils.toInteger(positionCodeNumRange.getMax()));
                        ruleCondition.setPositionCodeNumRange(null);
                        waveRulesToUpdate.add(assembleWaveRuleToUpdate(waveRule));
                    }
                }

                if (CollectionUtils.isEmpty(waveRulesToUpdate)) {
                    return null;
                }

                waveRuleDao.batchUpdate(staff, waveRulesToUpdate);
                result.put(company.getId(), waveRulesToUpdate.size());

                return null;
            }
        });

        return result;
    }

    @RequestMapping(value = "/tag/reset", method = RequestMethod.POST)
    @ResponseBody
    public Object resetTag(Long companyId, String sids, String api_name) throws Exception {
        Long[] sidList = Strings.getAsLongArray(sids, ",", true);
        Staff staff = companyId != null ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
        Map<String, String> map = new HashMap<String, String>();
        if (sidList.length > 0) {
            List<Trade> updateList = new ArrayList<Trade>();
            List<TbTrade> list = tbTradeDao.queryByKeys(staff, "sid, tag_ids", "sid", sidList);
            for (Trade trade : list) {
                if (StringUtils.isNotBlank(trade.getTagIds())) {
                    Set<Long> tagIds = new HashSet<Long>();
                    String[] strArr = Strings.getAsStringArray(trade.getTagIds(), "|", true);
                    for (String str : strArr) {
                        tagIds.addAll(Strings.getAsLongSet(str, ",", false));
                    }
                    if (!tagIds.isEmpty()) {
                        Trade toUpdate = new TbTrade();
                        toUpdate.setSid(trade.getSid());
                        toUpdate.setTagIds(Strings.join(",", tagIds));
                        updateList.add(toUpdate);
                        map.put(toUpdate.getSid().toString(), toUpdate.getTagIds());
                    }
                }
            }
            if (!updateList.isEmpty()) {
                tradeUpdateService.updateTrades(staff, updateList);
            }
        }
        return map;
    }

    private void doCompanyCallback(String companyIds, Callback<Company, Object> callback) {
        int pageSize = 2000;
        List<Company> companies;
        if (StringUtils.isBlank(companyIds)) {
            Page page = new Page();
            int pageNo = 1;
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            while (true) {
                companies = companyService.list(page);
                if (CollectionUtils.isEmpty(companies)) {
                    break;
                }
                doCompanyCallback(companies, callback);
                if (companies.size() < pageSize) {
                    break;
                }
                page.setPageNo(++pageNo);
            }
        } else {
            companies = companyService.querComapanyListByIds(ArrayUtils.toLongArray(companyIds));
            doCompanyCallback(companies, callback);
        }
    }

    private void doCompanyCallback(List<Company> companies, Callback<Company, Object> callback) {
        if (CollectionUtils.isEmpty(companies)) {
            return;
        }
        for (Company company : companies) {
            callback.call(company);
        }
    }

    private WaveRule assembleWaveRuleToUpdate(WaveRule waveRule) {
        WaveRule waveRuleToUpdate = new WaveRule();
        waveRuleToUpdate.setId(waveRule.getId());
        waveRuleToUpdate.setNumDown(waveRule.getNumDown());
        waveRuleToUpdate.setNumUp(waveRule.getNumUp());
        waveRuleToUpdate.setCondition(JSONObject.toJSONString(waveRule.getRuleCondition()));
        return waveRuleToUpdate;
    }

    private Staff assembleStaff(Company company) {
        CompanyProfile profile = companyService.getCompanyProfile(company.getId());
        company.setProfile(profile);
        Staff staff = new Staff();
        staff.setCompanyId(company.getId());
        staff.setCompany(company);
        return staff;
    }

    public interface Callback<P, R> {
        R call(P var1);
    }

    @RequestMapping(value = "/pick/back/finish", method = RequestMethod.POST)
    @ResponseBody
    public Object finishAllPickBack(Long companyId) throws Exception {
        Assert.notNull(companyId, "请选择公司！");
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);

        pickBackService.finishAll(staff);
        return successResponse();
    }

    @RequestMapping(value = "/jd/fix", method = RequestMethod.POST)
    @ResponseBody
    public Object jdFix(Long companyId) throws Exception {
        Assert.notNull(companyId, "请选择公司！");
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT sid, tid, user_id, 0 as enable_status FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" t WHERE company_id = ? AND enable_status > 0 AND is_cancel = 0 AND `source` = 'jd' AND NOT EXISTS(SELECT 1 FROM order_");
        sql.append(staff.getDbInfo().getOrderDbNo()).append(" WHERE sid = t.sid)");
        List<TbTrade> trades = jdbcTemplateAdapter.get(staff).query(sql.toString(), new BeanPropertyRowMapper<TbTrade>(TbTrade.class), companyId);
        Map<Long, User> userMap = new HashMap<Long, User>();
        Set<String> tidSet = new HashSet<String>();
        //删除没有商品的订单
        tradeUpdateService.updateTrades(staff, TradeUtils.toTrades(trades));
        for (Trade trade : trades) {
            try {
                User user = userMap.get(trade.getUserId());
                if (user == null) {
                    user = userService.queryById(trade.getUserId());
                    if (user == null) {
                        continue;
                    }
                    user.setStaff(staff);
                    userMap.put(user.getId(), user);
                }
                syncTradeBusiness.syncTradeByTid(user, trade.getTid(), true);
                tidSet.add(trade.getTid());
            } catch (Throwable e) {
                logger.error(LogHelper.buildLog(staff, String.format("订单[tid=%s]下载出错", trade.getTid())), e);
            }
        }
        return tidSet;
    }

    @RequestMapping(value = "/payment/fix", method = RequestMethod.POST)
    @ResponseBody
    public Object paymentFix(Long companyId, String source, Integer days, Integer limit) throws Exception {
        Assert.notNull(companyId, "请选择公司！");
        Assert.notNull(source, "请选择要处理的平台！");
        Assert.notNull(days, "请选择要处理多少天内支付的订单！");

        if(limit == null || limit > 5000) limit = 5000;
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        logger.debug(LogHelper.buildLog(staff, String.format("开始修复金额数据, companyId:%s,source:%s, days:%s, limit:%s", companyId, source, days, limit)));
        Date fixPayTime = DateUtils.addDays(new Date() , -1* days);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT tid, user_id FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" t WHERE company_id = ? AND enable_status > 0 AND is_cancel = 0 AND `source` = ? and sys_status <> 'CLOSED' AND payment in('0.00') and pay_time > ? limit ?");
        List<TbTrade> trades = jdbcTemplateAdapter.get(staff).query(sql.toString(), new BeanPropertyRowMapper<TbTrade>(TbTrade.class), companyId, source, fixPayTime, limit);
        logger.debug(LogHelper.buildLog(staff, "查询到总数：" + trades.size()));
        Map<Long, User> userMap = new HashMap<Long, User>();
        Set<String> tidSet = new HashSet<String>();
        TradeImportResult result = new TradeImportResult();
        result.setCanUpdatePayment(true);

        for (Trade trade : trades) {
            try {
                User user = userMap.get(trade.getUserId());
                if (user == null) {
                    user = userService.queryById(trade.getUserId());
                    if (user == null) {
                        continue;
                    }
                    user.setStaff(staff);
                    userMap.put(user.getId(), user);
                }
                TradeSyncConfig syncConfig = ConfigHolder.TRADE_SYNC_CONFIG;
                if(syncConfig != null && syncConfig.getCanUpdatePaymentUserIds() != null){
                    syncConfig.getCanUpdatePaymentUserIds().add(user.getId());
                }
                List<Trade> downloadTradeList = new ArrayList<>();
                syncTradeBusiness.syncTradeByTids(staff, user, Sets.newHashSet(trade.getTid()), true, result, downloadTradeList);
                tidSet.add(trade.getTid());
                if(tidSet.size() % 100 == 0){
                    logger.debug(LogHelper.buildLog(staff, "已同步成功笔数：" + tidSet.size()));
                }
            } catch (Throwable e) {
                logger.error(LogHelper.buildLog(staff, String.format("订单[tid=%s]下载出错", trade.getTid())), e);
            }
        }
        logger.debug(LogHelper.buildLog(staff, "同步成功总笔数：" + tidSet.size()));
        return tidSet;
    }

    @RequestMapping(value = "/moving/cost/search", method = RequestMethod.POST)
    @ResponseBody
    public Object movingCostSearch(String sysItemIds, String sysSkuIds, String warehouseIds, String api_name) throws SessionException {
        Staff staff = getStaff();
        List<Long> item = ArrayUtils.toLongList(sysItemIds);
        List<Long> sku = new ArrayList<Long>();
        if (StringUtils.isNotBlank(sysSkuIds) && StringUtils.isNotBlank(sysSkuIds.replaceAll(",", ""))) {
            sku = ArrayUtils.toLongList(sysSkuIds);
        }
        List<Long> warehouse = ArrayUtils.toLongList(warehouseIds);
        return itemServiceDubbo.getLatestMovingCost(staff, item, sku, warehouse);
    }


    @RequestMapping(value = "/fix/merge/waveid", method = RequestMethod.POST)
    @ResponseBody
    public Object fixMergeWaveId(String companyIdStr) throws Exception {
        Assert.hasText(companyIdStr, "请选择公司！");
        for (Long companyId : ArrayUtils.toLongSet(companyIdStr)) {
            Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
            String sql = String.format("select t1.sid, t2.wave_id, t1.merge_sid from trade_%s t1, trade_%s t2 where t1.company_id = ? and t1.merge_sid > 0 and t1.enable_status > 0 and t1.sys_status = 'FINISHED_AUDIT' and t1.merge_sid = t2.sid and t2.wave_id > 0", staff.getDbInfo().getTradeDbNo(), staff.getDbInfo().getTradeDbNo());
            List<TbTrade> trades = jdbcTemplateAdapter.get(staff).query(sql, new BeanPropertyRowMapper<TbTrade>(TbTrade.class), companyId);
            if (CollectionUtils.isNotEmpty(trades)) {
                List<Trade> updates = Lists.newArrayListWithExpectedSize(trades.size());
                StringBuilder log = new StringBuilder("修改合单的隐藏订单波次号，sid,waveId:[");
                for (TbTrade trade : trades) {
                    if (!trade.getSid().equals(trade.getMergeSid())) {
                        TbTrade update = new TbTrade();
                        update.setSid(trade.getSid());
                        update.setWaveId(trade.getWaveId());
                        updates.add(update);
                        log.append(trade.getSid()).append("-").append(trade.getWaveId()).append(",");
                    }
                }

                if (!updates.isEmpty()) {
                    logger.debug(LogHelper.buildLog(staff, log.deleteCharAt(log.length() - 1).append("]").toString()));
                    tradeWaveService.updateTradeWaveId(staff, updates);
                }
            }
        }
        return successResponse();
    }

    @RequestMapping("/wave/split/event/fire")
    @ResponseBody
    public Object sendWaveSplitEvent(Long companyId, String sids) throws Exception {
        Assert.notNull(companyId, "companyId not null!");
        Assert.hasText(sids, "sids not empty!");

        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        Assert.notNull(staff, "staff not exist!");
        if (staff.getConf().isOpenWms()) {
            eventCenter.fireEvent(this, new EventInfo("trade.split.audited.sku").setArgs(new Object[]{staff}), ArrayUtils.toLongArray(sids));
        }

        return successResponse();
    }

    @RequestMapping(value = "/jd/warehouse/fix", method = RequestMethod.POST)
    @ResponseBody
    public Object jdWarehouseFix(String companyIds) throws Exception {
        Assert.notNull(companyIds, "companyId not null!");
        List<Company> companies = companyService.querComapanyListByIds(ArrayUtils.toLongArray(companyIds));
        for (Company company : companies) {
            Logs.debug(String.format("开始处理公司【%s - %s】jd直发仓数据", company.getName(), company.getId()));
            Staff staff = staffService.queryDefaultStaffByCompanyId(company.getId());
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT sid FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" t WHERE company_id = ? AND `source` = 'jd' AND `type` = 'jd_warehouse' ");
            List<TbTrade> trades = jdbcTemplateAdapter.get(staff).query(sql.toString(), new BeanPropertyRowMapper<TbTrade>(TbTrade.class), company.getId());
            if (CollectionUtils.isNotEmpty(trades)) {
                for (Trade trade : trades) {
                    trade.setSubSource("jd_warehouse");
                }
                tradeUpdateService.updateTrades(staff, TradeUtils.toTrades(trades));
            }
        }
        return successResponse();
    }

    @RequestMapping(value = "/cost/fix", method = RequestMethod.POST)
    @ResponseBody
    public Object costFix(String sids,Boolean needUpdate,Integer tradeItemCostConfig) throws Exception {
        Assert.notNull(sids, "sids not null!");
        Long[] sidArray = ArrayUtils.toLongArray(sids);
        Staff staff = getStaff();
        if ((tradeItemCostConfig != null && tradeItemCostConfig == 0) || !tradeCostCalculateBusiness.isOpenHistoryPriceImport(staff, tradeConfigService.get(staff))) {
            //按档案成本价修复
            costFixBusiness.fix(staff, sidArray, Optional.ofNullable(needUpdate).orElse(false));
            return successResponse();
        }
        List<Trade> tradeList = solrTradeSearchService.queryBySids(staff, true, sidArray);
        List<Trade> updateTrades = new ArrayList<Trade>();
        List<Order> updateOrders = new ArrayList<Order>();
        if (CollectionUtils.isNotEmpty(tradeList)) {
            for (Trade trade : tradeList) {
                trade.setForce(true);
                tradeCostCalculateBusiness.matchCost(staff, trade);
                TbTrade update = new TbTrade();
                update.setSid(trade.getSid());
                update.setCost(trade.getCost());
                updateTrades.add(update);
                List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
                for (Order order : orders4Trade) {
                    TbOrder updateOrder = new TbOrder();
                    updateOrder.setId(order.getId());
                    updateOrder.setCost(order.getCost());
                    updateOrders.add(updateOrder);
                    List<Order> suits = order.getSuits();
                    if (CollectionUtils.isNotEmpty(suits)) {
                        for (Order suit : suits) {
                            TbOrder updateOrder1 = new TbOrder();
                            updateOrder1.setId(suit.getId());
                            updateOrder1.setCost(suit.getCost());
                            updateOrders.add(updateOrder1);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(updateTrades)) {
                Logs.debug("updateTradesCost:" + JSONObject.toJSONString(updateTrades));
                Logs.debug("updateOrdersCost:" + JSONObject.toJSONString(updateOrders));
                tradeUpdateService.updateTrades(staff, updateTrades, updateOrders, null);
            }
        }
        return successResponse();
    }

    @RequestMapping("/invoice/get")
    @ResponseBody
    public Object getInvoice(Long companyId, Long userId, String tid) {
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        User user = userService.queryById(userId);
        user.setStaff(staff);
        return tbTradeAccess.getInvoice(user, tid);
    }

    /**
     * 用于手动跟新某个用户的物流信息
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/update/logisticsInfo", method = RequestMethod.POST)
    @ResponseBody
    public String updateLogisticsInfo(Long companyId, String code, String api_name) throws Exception {
        if ("1".equals(code)) {
            tradeLogisticsTrackingAdapter.invokeLogisticsInfo(getStaff(), false);
        } else {
            Assert.isTrue(null != companyId && companyId > 0, "请传入公司id");
            Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
            Assert.isTrue(null != staff, "该公司不存在默认员工信息");
            if (cache.add(LOGISTICS_UPDATE_KEY + "_" + companyId, String.valueOf(companyId), 10)) {
                boolean flag = logisticsTrackingRecordService.synLogisticsInfo(staff);
                if (flag) {
                    return "success";
                } else {
                    return "fail";
                }
            } else {
                return "access limit, please wait a moment";
            }
        }
        return null;
    }

    @RequestMapping(value = "/recover/LostMsg", method = RequestMethod.POST)
    @ResponseBody
    public Object recoverLostMsg(String companyIds) throws Exception {
        Assert.notNull(companyIds, "companyId not null!");
        List<Company> companies = companyService.querComapanyListByIds(ArrayUtils.toLongArray(companyIds));

        for (Company company : companies) {
            Staff staff = staffService.queryDefaultStaffByCompanyId(company.getId());
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append(String.format("开始处理公司【%s - %s】信息丢失异常", company.getName(), company.getId())));
            }
            recoverLostMsg(staff);
        }
        return successResponse();
    }

    private void recoverLostMsg(Staff staff) {
        long companyId = staff.getCompanyId();
        int tradeDbNo = staff.getDbInfo().getTradeDbNo();
        int orderDbNo = staff.getDbInfo().getOrderDbNo();
        final String tradeSql = "select a.sid " +
                " from trade_not_consign_" + tradeDbNo + " a " +
                " where a.company_id = " + companyId + " and a.sys_status in ('FINISHED_AUDIT','WAIT_AUDIT') and a.enable_status = 1 and a.is_excep=1 and a.is_lost_msg = 0 and a.type not like 'trade_out%' " +
                " and (length(a.payment)=0 or " +
                " length(a.receiver_name)=0 or " +
                " length(a.receiver_address)=0 or " +
                " (length(a.receiver_mobile)=0 and length(a.receiver_phone)=0) or " +
                " a.item_excep & (1<<8 )  > 0 or  " +
                " not exists (select 1 from order_not_consign_" + orderDbNo + " t where t.company_id =" + companyId + " and a.enable_status=1 and t.sid=a.sid))";

        List<TbTrade> trades = jdbcTemplateAdapter.get(staff).query(tradeSql, new BeanPropertyRowMapper<>(TbTrade.class));
        if (trades != null && trades.size() > 0) {
            StringBuilder sb = new StringBuilder("后台接口处理信息丢失异常,sids=");
            List<Trade> updateTrades = new ArrayList<>();
            for (TbTrade trade : trades) {
                sb.append(trade.getSid()).append(",");
                Trade update = new TbTrade();
                update.setSid(trade.getSid());
                update.setIsExcep(1);
                update.setIsLostMsg(1);
                updateTrades.add(update);
            }
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append(sb.toString()));
            }
            tradeUpdateService.updateTrades(staff, updateTrades);
        }
    }


    @Resource
    private IColumnConfService columnConfService;

    @RequestMapping("/columnConf/manage")
    @ResponseBody
    public Object columnConfManage(PageColumnConf pageColumnConf) {
        if (IpUtils.isSelfVisitor(request)) {
            columnConfService.updateDeFaultColumn(null, pageColumnConf);
            return Status.buildSuccessStatus();
        } else {
            throw new IllegalArgumentException("非法IP");
        }
    }

    /**
     * 修改列配置基础数据接口,修改完要手动去删缓存
     *
     * @param pageColumnConf
     * @return
     * @throws SessionException
     * @Title: columnConfManage
     */
    @RequestMapping("/columnConf/repairEnv")
    @ResponseBody
    public Object columnConfEnv(PageColumnConfUpdateParam pageColumnConf) throws SessionException {
        Assert.hasText(pageColumnConf.getToEnv(), "请输入修改的环境变量！");
        Assert.notEmpty(pageColumnConf.getFromEnvs(), "请输入已经存在的环境变量");
        Assert.isTrue(StringUtils.isNoneBlank(pageColumnConf.getToken()) && StringUtils.equals("trade_env", pageColumnConf.getToken()), "请传入token");
        Staff staff = getLightStaff();
        if (!checkEnv(pageColumnConf.getToEnv())) {
            throw new IllegalArgumentException("请输入正确的环境 env:{" + pageColumnConf.getToEnv() + "}");
        }
        try {
            columnConfService.createTempConf(pageColumnConf);
            columnConfService.repairUpdateColumnConfEnv(pageColumnConf);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "修改交易配置失败:" + e.getMessage()));
        }
        return successResponse();
    }

    @Resource
    private ColumnConfigDao columnConfigDao;

    /**
     * 添加列配置基础数据接口
     *
     * @return
     * @throws SessionException
     * @Title: columnConfManage
     */
    @RequestMapping("/columnConf/add")
    @ResponseBody
    public Object columnConfAdd(String colCode,String colTitle,String pageIds,String env,String envAdd,Integer isDefault,Integer width) throws SessionException {
        Assert.hasText(colCode, "请输入列编码");
        Assert.hasText(colTitle, "请输入列名称");
        Assert.hasText(pageIds, "请输入pageIds");
        Staff staff = getLightStaff();
        List<Long> pages = Strings.getAsLongList(pageIds, ",", true);

        List<PageColumnConf> columnConfList = columnConfigDao.getPageColumnConfList(pages, null);
        Assert.notEmpty(columnConfList, "不存在的pageIds:"+pageIds);
        Map<Long, List<PageColumnConf>> map = columnConfList.stream().collect(Collectors.groupingBy(x -> x.getPageId()));

        List<String> envs = Arrays.stream(ProjectContextEnum.values()).map(x -> x.getValue()).collect(Collectors.toList());
        String all = Strings.join(",", envs);

        JSONArray array = new JSONArray();
        for (Long page : pages) {
            List<PageColumnConf> columnConfs = map.get(page);
            Assert.notEmpty(columnConfs, "不存在的pageId:"+page);
            int maxSort = 0;
            PageColumnConf curr = null;
            for (PageColumnConf columnConf : columnConfs) {
                if (Objects.equals(colCode,columnConf.getColCode())) {
                    curr = columnConf;
                }
                if (columnConf.getSortNo() > maxSort) {
                    maxSort = columnConf.getSortNo();
                }
            }

            if (curr == null) {
                PageColumnConf params = new PageColumnConf();
                params.setPageId(page);
                params.setColCode(colCode);
                params.setColTitle(colTitle);
                params.setIsDefault(isDefault == null? 0:isDefault);
                params.setWidth(width == null? 120:width);
                params.setSortNo(maxSort +5);
                params.setEnv(env == null || env.equalsIgnoreCase("all")?all :env.equalsIgnoreCase("dev")?"gray3" :env);
                columnConfigDao.insertOrUpdateColumnConf(staff,params);
                JSONObject object = new JSONObject();
                object.put("acttion","add");
                object.put("pageId",page);
                array.add(object);
                continue;
            }

            boolean changed = false;
            if (Objects.equals(curr.getColTitle(),colTitle)) {
                curr.setColTitle(colTitle);
                changed = true;
            }
            if (StringUtils.isNotBlank(envAdd)) {
                if (envAdd.equalsIgnoreCase("all")) {
                    curr.setEnv(all);
                    changed = true;
                }else {
                    List<String> list = Strings.getAsStringList(curr.getEnv(), ",", true);
                    if (!list.contains(envAdd)) {
                        list.add(envAdd);
                        curr.setEnv(Strings.join(",", list));
                        changed = true;
                    }
                }
            }else if (StringUtils.isNotBlank(env)) {
                if (env.equalsIgnoreCase("all")) {
                    curr.setEnv(all);
                    changed = true;
                }else {
                    curr.setEnv(env);
                    changed = true;
                }
            }
            if (changed) {
                JSONObject object = new JSONObject();
                object.put("acttion","update");
                object.put("pageId",page);
                array.add(object);
                columnConfigDao.insertOrUpdateColumnConf(staff,curr);
            }
        }

        return array;
    }

    private boolean checkEnv(String env) {
        if (StringUtils.isBlank(env)) {
            return false;
        }
        List<String> envs = ArrayUtils.toStringList(env);
        Boolean[] isFlags = new Boolean[envs.size()];
        int index = 0;
        for (String e : envs) {
            for (ProjectContextEnum enums : ProjectContextEnum.values()) {
                isFlags[index] = false;
                if (StringUtils.equals(e, enums.getValue())) {
                    isFlags[index] = true;
                    break;
                }
            }
            index++;
        }
        if (Arrays.stream(isFlags).filter(Boolean::booleanValue).count() == envs.size()) {
            return true;
        }
        return false;
    }

    @RequestMapping("/cancel/trade")
    @ResponseBody
    public Object ca(String sids, String apiName) throws SessionException {
        Assert.notNull(sids, "sid not null!");
        String[] split = sids.split(",");
        Long[] sidLong = new Long[split.length];
        int index = 0;
        for (String sidString : split) {
            sidLong[index++] = Long.parseLong(sidString);
        }
        sysTradeService.cancel(getStaff(), sidLong);
        return successResponse();
    }

    /**
     * 分开写，防止手动执行时手抖执行错了
     *
     * @param companyId
     * @param pageSize
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/picking/fix", method = RequestMethod.POST)
    @ResponseBody
    public Object pinckingFix(Long companyId, int pageSize, String fromTime, String endTime) throws Exception {
        if (true) {
            return "waveId没有处理，暂时不可用";
        }
        Staff defaultStaff = staffService.queryDefaultStaffByCompanyId(companyId);
        List<Staff> staffList = staffService.queryByCompanyId(defaultStaff.getCompanyId(), defaultStaff.getCompanyName());
        Map<String, Long> staffMap = new HashMap<String, Long>();
        for (Staff staff : staffList) {
            staffMap.put(staff.getName(), staff.getId());
        }
        Timestamp fromDate = Timestamp.valueOf(fromTime);
        Timestamp endDate = Timestamp.valueOf(endTime);
        Query q = new Query();
        q.append("SELECT ").append(" * ").append(" FROM trade_trace_").append(defaultStaff.getDbInfo().getTradeTraceDbNo()).append(" WHERE company_id = ? AND created >=? and  created <=? and action like '%拣选领单%' LIMIT ?, ?").add(defaultStaff.getCompanyId()).add(fromDate).add(endDate);
        Page page = new Page().setPageSize(pageSize).setPageNo(1);
        q.add(page.getStartRow()).add(page.getPageSize());
        int staffIdNotFound = 0;
        int tradeNotFound = 0;
        List<TradeTrace> list = jdbcQueryDao.queryList(defaultStaff.getDbNo(), TradeTrace.class, q.getQ().toString(), q.getArgs().toArray());
        if (list != null && list.size() > 0) {
            List<TradePick> tradePickList = new ArrayList<>();
            Map<Long, TradePick> tradePickMap = new HashMap<>();
            Set<Long> sidSet = new HashSet<Long>();
            for (TradeTrace tradeTrace : list) {
                sidSet.add(tradeTrace.getSid());
                String action = tradeTrace.getAction();
                String staffName = action.substring(5, action.length() - 1);
                Long staffId = staffMap.get(staffName);
                if (staffId != null && staffId > 0) {
                    TradePick tradePick = new TradePick();
                    tradePick.setSid(tradeTrace.getSid());
                    tradePick.setCompanyId(defaultStaff.getCompanyId());
                    tradePick.setPickedTime(new Timestamp(tradeTrace.getCreated().getTime()));
                    tradePick.setStaffId(staffId);
                    tradePick.setStaffName(staffName);

                    tradePickMap.put(tradeTrace.getSid(), tradePick);
                } else {
                    staffIdNotFound++;
                }
            }

            List<Trade> trades = tradeSearchService.queryBySids(defaultStaff, true, sidSet.toArray(new Long[0]));
            for (Trade trade : trades) {
                TradePick tradePick = tradePickMap.get(trade.getSid());
                if (tradePick != null) {
                    Map<String, Object> infoJson = new HashMap<>();
                    tradePerformanceCalBusiness.calItemInfo(defaultStaff, trade, infoJson);
                    tradePick.setInfoJson(JSONObject.toJSONString(infoJson));
                    tradePickList.add(tradePick);
                } else {
                    tradeNotFound++;
                }
            }

            if (tradePickList != null && tradePickList.size() > 0) {
                tradePickDao.insertBatch(defaultStaff, tradePickList);
            }
        }

        return list.size() + "@@" + tradeNotFound + "@@" + staffIdNotFound;


    }

    @RequestMapping("/fix/checkgoods")
    @ResponseBody
    public Object fixCheckGoodsNumByCompany(String start, String end) throws SessionException {
        if (!IpUtils.isSelfVisitor(request)) {
            throw new IllegalArgumentException("您无权访问");
        }
        if (start == null || end == null) {
            logger.error("checkBaseParam : 缺少时间参数");
            throw new IllegalArgumentException("糟糕，缺少时间参数");
        }
        Staff staff = getStaff();
        Date startDate;
        Date endDate;
        try {
            startDate = DateUtil.getDate(start);
            if (end != null) {
                endDate = DateUtil.getDate(end);
            } else {
                endDate = new Date();
            }
            if (endDate.getTime() < startDate.getTime()) {
                logger.error("checkBaseParam : 结束时间不应该小于开始时间");
                throw new IllegalArgumentException("糟糕，结束时间不应该小于开始时间");
            }
            if (DateUtil.diffDate(endDate, startDate) + 1 > 1) {
                logger.error("checkBaseParam : 时间跨度不能超过1天");
                throw new IllegalArgumentException("糟糕，时间跨度超过1天了");
            }
            tradeCheckGoodsService.fixCheckGoodsTrades(staff, startDate, endDate);
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff), e);
            throw new IllegalArgumentException(e.getMessage(), e);
        }
        return successResponse();
    }

    /**
     * 设置未正确消费的订单(测试用)
     *
     * @throws SessionException
     */
    @RequestMapping("/setRepairOrder")
    @ResponseBody
    public Object setRepairOrder(String sids) throws SessionException {
        Staff staff = getStaff();
        List<Long> orderIds = Lists.newArrayList();
        List<Trade> trades = solrTradeSearchService.queryBySidsContainMergeTrade(staff, true, Strings.getAsLongArray(sids, ",", true));
        if (CollectionUtils.isEmpty(trades)) {
            return successResponse();
        }
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                orderIds.add(order.getId());
            }
        }
        List<StockOrderRecord> records = stockOrderRecordDAO.queryByOrderIds(staff, 0, orderIds.toArray(new Long[orderIds.size()]));
        if (CollectionUtils.isNotEmpty(records)) {
            Map<Long, StockOrderRecord> recordMap = records.stream().sorted(new Comparator<StockOrderRecord>() {
                @Override
                public int compare(StockOrderRecord pre, StockOrderRecord next) {
                    return pre.getCreated().compareTo(next.getCreated());
                }
            }).collect(Collectors.toMap(StockOrderRecord::getOrderId, Function.identity(), (v1, v2) -> v2));
            for (StockOrderRecord stockOrderRecord : recordMap.values()) {
                stockOrderRecord.setEnableStatus(1);
                stockOrderRecord.setChangeEnableStatus(1);
            }
            stockOrderRecordDAO.batchUpdate(staff, Lists.newArrayList(recordMap.values()));
        }
        logger.debug(LogHelper.buildLog(staff, String.format("设置异常子订单信息 %s", StringUtils.join(orderIds, ","))));
        if (staff.getConf() != null && staff.getConf().openWmsStorageSection()) {
            //删除消费记录
            wmsService.delGoodsSectionOrderRecordByOrderIds(staff, orderIds);
        }
        return successResponse();
    }

    /**
     * 修复未正确消费的订单
     *
     * @throws SessionException
     */
    @RequestMapping("/repairOrder")
    @ResponseBody
    public Object repairOrder() throws SessionException {
        repairOrderBusiness.repairOrderConsume(getStaff());
        return successResponse();
    }


    @RequestMapping("/fix/acPayment")
    @ResponseBody
    public Object fixAcPayment(Long companyId) throws SessionException {
        Staff staff = companyId != null ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
        Query q = new Query();
        q.append("SELECT ").append(" sid,company_id,payment,ac_payment ").append(" FROM trade_not_consign_").append(staff.getDbInfo().getTradeDbNo()).append(" WHERE company_id = ? AND enable_status > 0  and sys_status in('WAIT_AUDIT','FINISHED_AUDIT') LIMIT ?, ?").add(staff.getCompanyId());
        Page page = new Page().setPageSize(1000).setPageNo(1);
        q.add(page.getStartRow()).add(page.getPageSize());
        List<TbTrade> list;
        int r = 0;
        while (!(list = jdbcQueryDao.queryList(staff.getDbNo(), TbTrade.class, q.getQ().toString(), q.getArgs().toArray())).isEmpty()) {
            List<Trade> updateTradeList = new ArrayList<>();
            for (Trade trade : list) {
                if ((trade.getAcPayment() == null || NumberUtils.str2Double(trade.getAcPayment()) < 0.0001) && trade.getPayment() != null && NumberUtils.str2Double(trade.getPayment()) > 0.0001) {
                    trade.setAcPayment(trade.getPayment());
                    TbTrade updateTrade = new TbTrade();
                    updateTrade.setCompanyId(staff.getCompanyId());
                    updateTrade.setSid(trade.getSid());
                    updateTrade.setAcPayment(trade.getAcPayment());
                    updateTradeList.add(updateTrade);
                }
            }
            if (updateTradeList.size() > 0) {
                tradeUpdateService.updateTrades(staff, updateTradeList);
            }
            r += updateTradeList.size();
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("用户%s acPayment,已修复%s", staff.getCompanyId(), r)));
            }
            if (list.size() < page.getPageSize()) {
                break;
            }
            page.setPageNo(page.getPageNo() + 1);
            q.getArgs().set(q.getArgs().size() - 2, page.getStartRow());
        }
        return r;
    }


    @RequestMapping("/fix/acPayment/fentan/taojian")
    @ResponseBody
    public Object fixAcPaymentFenTanTaoJian(Long companyId, String sids) throws SessionException {
        try {
            Staff staff = companyId != null ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
            Query q = new Query();
            q.append("SELECT ").append(" sid,tid,company_id,payment,ac_payment,post_fee ").append(" FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" WHERE company_id = ? AND enable_status = 1 AND sid in (" + sids + ") limit ?,?").add(staff.getCompanyId());
            Page page = new Page().setPageSize(10).setPageNo(1);
            q.add(page.getStartRow()).add(page.getPageSize());
            List<TbTrade> list;
            int r = 0;
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("sql=%s,r=%s", q.getQ().toString().trim(), r)));
            }
            TradeItemContext tradeItemContext = new TradeItemContext().setTradeConfig(tradeConfigService.get(staff));
            while (!(list = jdbcQueryDao.queryList(staff.getDbNo(), TbTrade.class, q.getQ().toString(), q.getArgs().toArray())).isEmpty()) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("list=%s", TradeUtils.toSidList(list))));
                }
                List<Trade> updateTradeList = new ArrayList<>();
                Map<Long, List<TbTrade>> map = list.stream().collect(Collectors.groupingBy(TbTrade::getSid));
                Iterator<Map.Entry<Long, List<TbTrade>>> iterator = map.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Long, List<TbTrade>> next = iterator.next();
                    List<TbTrade> value = next.getValue();
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, String.format("value=%s", TradeUtils.toSidList(value))));
                    }

                    List<Order> updateOrders = Lists.newArrayList();
                    for (TbTrade tbTrade : value) {
                        Long sid1 = tbTrade.getSid();
                        Trade trade = tradeSearchService.queryBySid(staff, true, sid1);

                        if (logger.isDebugEnabled()) {
                            logger.debug(LogHelper.buildLog(staff, String.format("payment=%s", trade.getPayment())));
                        }
                        if (StringUtils.isBlank(trade.getPayment())) {
                            if (logger.isDebugEnabled()) {
                                logger.debug(LogHelper.buildLog(staff, String.format("实付金额为空=%s", trade.getSid())));
                            }
                        }
                        Order tempOrder = new Order();
                        tempOrder.setPayment(trade.getPayment());
                        List<Order> orders = TradeUtils.getOrders4Trade(trade);
                        fillPrice(staff, orders);
                        tradeSuitCalculateBusiness.calculate(staff, tempOrder, orders, tradeItemContext, false);
                        updateOrders.addAll(orders);
                    }
                    if (updateOrders.size() > 0) {
                        tradeUpdateService.updateTrades(staff, null, updateOrders);
                    }
                }
                r += list.size();
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("用户%s acPayment,已修复%s", staff.getCompanyId(), r)));
                }
                if (list.size() < page.getPageSize()) {
                    break;
                }
                page.setPageNo(page.getPageNo() + 1);
                q.getArgs().set(q.getArgs().size() - 2, page.getStartRow());
            }
            return r;
        } catch (Exception e) {
            logger.error("公司id:" + companyId + "处理异常:" + e.getMessage(), e);
        }
        return 0;
    }


    private void fillPrice(Staff staff, List<Order> orders) {
        for (Order order : orders) {
            boolean b = StringUtils.isBlank(order.getPrice()) || (StringUtils.isNotBlank(order.getPrice()) && Double.parseDouble(order.getPrice()) <= 0);
            if (b) {
                List<DmjItem> items = itemServiceDubboImpl.queryItemDetail(staff, Collections.singletonList(order.getItemSysId()), Collections.singletonList(order.getSkuSysId()), false);
                if (CollectionUtils.isEmpty(items)) {
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, String.format("商品可能被删除了=%s", order.getId())));
                    }
                } else {
                    DmjItem dmjItem = items.get(0);
                    if (dmjItem != null) {
                        //纯商品
                        if (order.getItemSysId() > 0) {
                            if (order.getSkuSysId() <= 0) {
                                String pitem = dmjItem.getPriceOutput() != null ? dmjItem.getPriceOutput().toString() : "0";
                                if (logger.isDebugEnabled()) {
                                    logger.debug(LogHelper.buildLog(staff, String.format("纯商品=%s，price=%s", order.getId(), pitem)));
                                }
                                order.setPrice(pitem);
                            } else {
                                List<DmjSku> skus = dmjItem.getSkus();
                                for (DmjSku dmjSku : skus) {
                                    if (order.getSkuSysId() - dmjSku.getSysSkuId() == 0) {
                                        String pSku = dmjSku.getPriceOutput() != null ? dmjSku.getPriceOutput().toString() : "0";
                                        if (logger.isDebugEnabled()) {
                                            logger.debug(LogHelper.buildLog(staff, String.format("sku商品=%s，price=%s", order.getId(), pSku)));
                                        }
                                        order.setPrice(pSku);
                                    }
                                }
                            }
                        }

                    } else {
                        if (logger.isDebugEnabled()) {
                            logger.debug(LogHelper.buildLog(staff, String.format("商品可能被删除了=%s", order.getId())));
                        }
                    }
                }
            }
        }
    }


    @RequestMapping("/fix/priceCost/byItem")
    @ResponseBody
    public Object priceCost(Long companyId) throws SessionException {
        Staff staff = companyId != null ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
        Query q = new Query();
        q.append("SELECT ").append(" * ").append(" FROM order_").append(staff.getDbInfo().getOrderDbNo()).append(" WHERE company_id = ? AND enable_status > 0 and  created > '2020-06-24'  and is_cancel = 0 and ((price ='0.0' or price = '0' or price = '0.00') or  (cost ='0.0' or cost = '0' or cost = '0.00')) LIMIT ?, ?").add(staff.getCompanyId());
        Page page = new Page().setPageSize(1000).setPageNo(1);
        q.add(page.getStartRow()).add(page.getPageSize());
        List<TbOrder> list;
        int r = 0;
        while (!(list = jdbcQueryDao.queryList(staff.getDbNo(), TbOrder.class, q.getQ().toString(), q.getArgs().toArray())).isEmpty()) {
            try {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("list=%s", OrderUtils.toIdList(list))));
                }
                List<Order> updateOrderList = new ArrayList<>();
                Map<String, String> outerIdMap = getOuterIdMap(list, staff);
                if (MapUtils.isEmpty(outerIdMap)) {
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, String.format("商品查询就是空的=%s", 1)));
                    }
                    page.setPageNo(page.getPageNo() + 1);
                    continue;
                }
                for (TbOrder order : list) {
                    TbOrder order1 = new TbOrder();
                    order1.setId(order.getId());
                    String priceInfo = outerIdMap.get(getKey(order.getItemSysId(), order.getSkuSysId()));
                    if (StringUtils.isNotBlank(priceInfo)) {
                        String[] split = priceInfo.split(",");
                        String s1 = split[0] != null ? split[0] : "0";
                        order1.setPrice(s1);
                        String s2 = split[1] != null ? split[1] : "0";
                        order1.setCost(Double.parseDouble(s2));
                        updateOrderList.add(order1);
                    } else {
                        if (logger.isDebugEnabled()) {
                            logger.debug(LogHelper.buildLog(staff, String.format("价格为空=%s", 2)));
                        }
                    }
                }
                if (updateOrderList.size() > 0) {
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, String.format("updateOrderList=%s", updateOrderList.size())));
                    }
                    tradeUpdateService.updateTrades(staff, null, updateOrderList);
                }
                r += updateOrderList.size();
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("用户%s acPayment,已修复%s", staff.getCompanyId(), r)));
                }
                if (list.size() < page.getPageSize()) {
                    break;
                }
            } catch (Exception e) {
                logger.error("公司id:" + companyId + "处理异常:" + e.getMessage(), e);
            } finally {
                page.setPageNo(page.getPageNo() + 1);
            }
        }
        return r;
    }

    public String getKey(Long sysItemId, Long sysSkuId) {
        return sysItemId + "_" + sysSkuId;
    }

    public Map<String, String> getOuterIdMap(List<TbOrder> orders, Staff staff) {
        try {
            Set<Long> sysItemIds = new HashSet<>();
            Set<Long> sysSkuIds = new HashSet<>();
            for (Order order : orders) {
                sysItemIds.add(order.getItemSysId());
                sysSkuIds.add(order.getSkuSysId());
            }
            List<DmjItem> dmjItems = itemServiceDubbo.queryItemDetail(staff, Lists.newArrayList(sysItemIds), Lists.newArrayList(sysSkuIds), false);
            Map<String, String> outerIdMap = new HashMap<>(16);
            if (CollectionUtils.isNotEmpty(dmjItems)) {
                for (DmjItem dmjItem : dmjItems) {
                    if (dmjItem.getIsSkuItem() == 0) {
                        outerIdMap.put(getKey(dmjItem.getSysItemId(), -1L), dmjItem.getPriceOutput() + "," + dmjItem.getPriceImport());
                    } else {
                        for (DmjSku dmjSku : dmjItem.getSkus()) {
                            outerIdMap.put(getKey(dmjSku.getSysItemId(), dmjSku.getSysSkuId()), dmjSku.getPriceOutput() + "," + dmjSku.getPriceImport());
                        }
                    }
                }
            }
            return outerIdMap;
        } catch (Exception e) {
            logger.error("公司111id: 处理异常:" + e.getMessage(), e);
        }
        return Maps.newHashMap();
    }


    @RequestMapping("/fix/priceCost/byItem2")
    @ResponseBody
    public Object priceCost2(Long companyId) throws SessionException {
        Staff staff = companyId != null ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
        Query q = new Query();
        q.append("SELECT ").append(" * ").append(" FROM order_").append(staff.getDbInfo().getOrderDbNo()).append(" WHERE company_id = ? AND enable_status > 0 and  created > '2020-06-24'  and is_cancel = 0 and ((price ='0.0' or price = '0' or price = '0.00') or  (cost ='0.0' or cost = '0' or cost = '0.00')) LIMIT ?, ?").add(staff.getCompanyId());
        Page page = new Page().setPageSize(2000).setPageNo(1);
        q.add(page.getStartRow()).add(page.getPageSize());
        List<TbOrder> list;
        int r = 0;
        while (!(list = jdbcQueryDao.queryList(staff.getDbNo(), TbOrder.class, q.getQ().toString(), q.getArgs().toArray())).isEmpty()) {
            try {
                List<Order> updateOrderList = new ArrayList<>();
                for (TbOrder order : list) {
                    boolean b = (StringUtils.isBlank(order.getPrice()) || (StringUtils.isNotBlank(order.getPrice()) && Double.parseDouble(order.getPrice()) <= 0)) || (order.getCost() == null || order.getCost() <= 0);
                    if (b) {
                        List<DmjItem> items = itemServiceDubboImpl.queryItemDetail(staff, Collections.singletonList(order.getItemSysId()), Collections.singletonList(order.getSkuSysId()), false);
                        if (CollectionUtils.isEmpty(items)) {
                            if (logger.isDebugEnabled()) {
                                logger.debug(LogHelper.buildLog(staff, String.format("商品可能被删除了22=%s", order.getId())));
                            }
                        } else {
                            DmjItem dmjItem = items.get(0);
                            if (dmjItem != null) {
                                //纯商品
                                if (order.getItemSysId() > 0) {
                                    TbOrder updateOrder = new TbOrder();
                                    updateOrder.setId(order.getId());
                                    if (order.getSkuSysId() <= 0) {
                                        String priceItem = dmjItem.getPriceOutput() != null ? dmjItem.getPriceOutput().toString() : "0";
                                        String costItem = dmjItem.getPriceImport() != null ? dmjItem.getPriceImport().toString() : "0";
                                        if (logger.isDebugEnabled()) {
                                            logger.debug(LogHelper.buildLog(staff, String.format("纯商品Id=%s，price=%s,cost=%s", order.getId(), priceItem, costItem)));
                                        }
                                        updateOrder.setPrice(priceItem);
                                        updateOrder.setCost(Double.parseDouble(costItem));
                                    } else {
                                        List<DmjSku> skus = dmjItem.getSkus();
                                        for (DmjSku dmjSku : skus) {
                                            if (order.getSkuSysId() - dmjSku.getSysSkuId() == 0) {
                                                String priceSku = dmjSku.getPriceOutput() != null ? dmjSku.getPriceOutput().toString() : "0";
                                                String costSku = dmjSku.getPriceImport() != null ? dmjSku.getPriceImport().toString() : "0";

                                                if (logger.isDebugEnabled()) {
                                                    logger.debug(LogHelper.buildLog(staff, String.format("sku商品Id=%s，price=%s，cost=%s", order.getId(), priceSku, costSku)));
                                                }
                                                updateOrder.setPrice(priceSku);
                                                updateOrder.setCost(Double.parseDouble(costSku));
                                            }
                                        }
                                    }
                                    updateOrderList.add(updateOrder);
                                }

                            } else {
                                if (logger.isDebugEnabled()) {
                                    logger.debug(LogHelper.buildLog(staff, String.format("商品可能被删除了=%s", order.getId())));
                                }
                            }
                        }
                    }
                }
                if (updateOrderList.size() > 0) {
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, String.format("updateOrderList=%s", updateOrderList.size())));
                    }
                    tradeUpdateService.updateTrades(staff, null, updateOrderList);
                }
                r += updateOrderList.size();
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("用户%s acPayment,已修复%s", staff.getCompanyId(), r)));
                }
                if (list.size() < page.getPageSize()) {
                    break;
                }
            } catch (Exception e) {
                logger.error("公司id:" + companyId + "处理异常:" + e.getMessage(), e);
            } finally {
                page.setPageNo(page.getPageNo() + 1);
            }
        }
        return r;
    }


    /**
     * 初始化jitx模板
     */
    @RequestMapping("/initJITXTemplate")
    @ResponseBody
    public void initJITXTemplate(Long companyId) {
        Staff staff = staffAssembleBusiness.getDefaultStaff(companyId);
        userWlbExpressTemplateService.initJITXTemplate(staff);
    }

    /**
     * pdd分单授权
     *
     * @param userId
     * @return
     * @throws Exception
     */
    @RequestMapping("/getAuthToken")
    @ResponseBody
    public Object getAuthToken(Long userId) throws Exception {
        Assert.notNull(userId, "请传入userId");
        Staff staff = getStaff();
        String key = "express_divide_auth" + "_" + staff.getCompanyId() + "_" + userId;
        String result = cache.get(key);
        if (StringUtils.isNotBlank(result)) {
            return result;
        }
        String value = UUID.randomUUID().toString() + staff.getCompanyId();
        Logs.debug(String.format("分单系统生成验证码[%s]", value));
        boolean flag = cache.add(key, value, 60 * 30);
        if (flag) {
            return value;
        } else {
            return "设置缓存失败，请稍后重试";
        }
    }


    /**
     * 根据运单号查询物流信息
     */
    @RequestMapping("/getLogisticsPdd")
    @ResponseBody
    public Object searchTraceLogisticssFromPddWlbByOutsids(String outsids) throws SessionException {
        Staff staff = getStaff();
        Assert.isTrue(StringUtils.isNotBlank(outsids), "请传入运单号!");
        List<String> outsidList = Arrays.asList(outsids.split(","));
        return logisticBusiness.searchTraceLogisticssFromPddWlbByOutsids(staff, outsidList);
    }

    @RequestMapping("/switch/pick/performance")
    @ResponseBody
    public Object switchPickPerformanceValidate() throws Exception {
        Staff staff = getStaff();
        return tradeWaveService.switchPickPerformanceValidate(staff);
    }

    /**
     * 刷新订单排序数据
     */
    @RequestMapping("/flushSortString")
    @ResponseBody
    public Object flushSortString(TradeControllerParams queryParams, Integer maxPage, String api_name) throws SessionException {
        Staff staff = getStaff();
        TradeQueryParams params = TradeQueryParams.copyParams(queryParams);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        if (maxPage == null) {
            maxPage = 1000;
        }
        params.setUseNewQuery(tradeConfig.getUseNewQuery());//使用新的query方式
        for (int i = 1; i < maxPage; i++) {
            params.setPage(new Page(i, 1000));
            Trades search = solrTradeSearchService.search(staff, params);
            List<Trade> list = search.getList();
            if (CollectionUtils.isEmpty(list)) {
                logger.debug("无需更新" + i);
                break;
            }
            logger.debug("flush的条数：" + list.size());
            TradeUtils.fillTradeOfSort(list,tradeConfig);
            List<Trade> tradeList = new ArrayList<>();
            try {
                for (Trade var : list) {
                    TbTrade trade = new TbTrade();
                    trade.setSid(var.getSid());
                    trade.setSysOuterId(var.getSysOuterId());
                    tradeList.add(trade);
                    logger.debug("sortString:" + var.getSysOuterId() + " sid:" + var.getSid());
                }
                tradeUpdateService.updateTrades(staff, tradeList, false);
                logger.debug("更新完成" + i);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
        return successResponse();
    }


    /**
     * 根据时间修复播种绩效
     *
     * @param start
     * @param end
     * @return
     * @throws Exception
     */
    @RequestMapping("/repair/seed/record")
    @ResponseBody
    public Object repairSeedRecordByDate(String start, String end) throws Exception {
        Assert.hasText(start, "start not null");
        Assert.hasText(start, "end not null");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        final Date startTime = sdf.parse(start);
        final Date endTime = sdf.parse(end);

        Staff staff = getStaff();
        tradeWaveService.repairSeedRecordByDate(staff, startTime, endTime);
        return successResponse();
    }

    @RequestMapping("/status/upload")
    @ResponseBody
    public Object tradeStatusUpload(Long userId, String startTime, String endTime) throws SessionException {
        Staff staff = getStaff();
        if (null == startTime || null == endTime) {
            throw new IllegalArgumentException("请传入开始时间和结束时间");
        }
        User user = userService.queryById(userId);
        user.setStaff(staff);
        Date startDate = null;
        Date endDate = null;
        try {
            startDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startTime);
            endDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endTime);
        } catch (Exception e) {
            throw new IllegalArgumentException("输入时间格式不对，请输入正确的时间格式：yyyy-MM-dd HH:mm:ss");
        }
        tradeStatusUploadBusiness.doOrderLogPushAuto(user, startDate, endDate);
        return successResponse();
    }



    @RequestMapping("/status/upload/tids")
    @ResponseBody
    public Object tradeStatusUploadByTid(String tids) throws SessionException {
        Staff staff = getStaff();
        Assert.isTrue(StringUtils.isNotBlank(tids),"tids 不能为空!");
        String[] split = tids.split(",");
        List<TbTrade> trades = tradeSearchService.queryByTids(staff, true, split);
        tradeStatusUploadBusiness.upload(staff,trades,Maps.newHashMap());
        return successResponse();
    }


    /**
     * 前N有礼测试接口
     */
    @Resource
    private TradeOSActivityScheduleListener tradeOSActivityScheduleListener;
    @Resource
    private TradeOSActivityNotifyListener tradeOSActivityNotifyListener;
    @Resource
    private TradeOSActivityBusiness tradeOSActivityBusiness;

    @RequestMapping("/osActivity/schedule")
    @ResponseBody
    public Object testSchedule(Long companyId, String activityId, Integer dbKey, Integer tableId) {
        tradeOSActivityScheduleListener.handleTest(companyId, activityId, dbKey, tableId);
        return successResponse();
    }

    @RequestMapping("/osActivity/notify")
    @ResponseBody
    public Object testNotify(Long companyId, Long userId, String activityId, String completeTag) {
        tradeOSActivityNotifyListener.handleTest(companyId, userId, activityId, completeTag);
        return successResponse();
    }

    @RequestMapping("/osActivity/addInfo")
    @ResponseBody
    public Object addInfo(TradeOSActivityInfo tradeOSActivityInfo) throws SessionException {
        Staff staff = getStaff();
        User user = userService.queryById(tradeOSActivityInfo.getUserId());
        tradeOSActivityBusiness.insertOrUpdateActivityInfo(staff, user, tradeOSActivityInfo);
        return successResponse();
    }

    @RequestMapping("/osActivity/deleteInfo")
    @ResponseBody
    public Object deleteInfo(Long id) throws SessionException {
        Staff staff = getStaff();
        tradeOSActivityBusiness.deleteActivityInfo(staff, id);
        return successResponse();
    }

    @RequestMapping("/osActivity/addAwardOrder")
    @ResponseBody
    public Object addAwardOrder(TradeOSActivityAwardOrder awardOrder) throws SessionException {
        Staff staff = getStaff();
        User user = userService.queryById(awardOrder.getUserId());
        tradeOSActivityBusiness.insertOrUpdateActivityAwardOrder(staff, user, awardOrder);
        return successResponse();
    }

    @RequestMapping("/osActivity/deleteAwardOrder")
    @ResponseBody
    public Object deleteAwardOrder(Long id) throws SessionException {
        Staff staff = getStaff();
        tradeOSActivityBusiness.deleteActivityAwardOrder(staff, id);
        return successResponse();
    }

    @RequestMapping("/osActivity/addRelateOrder")
    @ResponseBody
    public Object addRelateOrder(TradeOSActivityRelateOrder relateOrder) throws SessionException {
        Staff staff = getStaff();
        User user = userService.queryById(relateOrder.getUserId());
        tradeOSActivityBusiness.insertOrUpdateActivityRelateOrder(staff, user, relateOrder);
        return successResponse();
    }

    @RequestMapping("/osActivity/deleteRelateOrder")
    @ResponseBody
    public Object deleteRelateOrder(Long id) throws SessionException {
        Staff staff = getStaff();
        tradeOSActivityBusiness.deleteActivityRelateOrder(staff, id);
        return successResponse();
    }

    /**
     * 修复未正确消费的订单
     *
     * @throws SessionException
     */
    @RequestMapping("/repairOrderByParam")
    @ResponseBody
    public Object repairOrder(String companyIdStr, String envStrs) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(companyIdStr) || StringUtils.isNotEmpty(envStrs), "companyIdStr || envStrs not null!");
        if (StringUtils.isNotEmpty(companyIdStr)) {
            for (Staff staff : tradeAllCompanyDealer.getStaffs(getLightStaff(), ArrayUtils.toLongArray(companyIdStr))) {
                try {
                    repairOrderBusiness.repairOrderConsume(staff);
                } catch (Exception e) {
                    // TODO: handle exception
                }
            }
        } else {
            tradeAllCompanyDealer.dealWithAllCompanys(new CompanyDealOp() {
                @Override
                public Object deal(Staff staff) {
                    try {
                        String companyEnvir = companyService.getCompanyEnvir(staff.getCompanyId());
                        if (ArrayUtils.toStringList(envStrs).contains(companyEnvir)) {
                            repairOrderBusiness.repairOrderConsume(staff);
                        }
                    } catch (Exception e) {
                        // TODO: handle exception
                    }
                    return null;
                }
            });
        }
        return successResponse();
    }

    /**
     * 清理淘宝、天猫的 AddressMd5 字段
     */
    @RequestMapping(value = "/eraser/address/md5/all", method = RequestMethod.POST)
    @ResponseBody
    public Object eraserAddressMd5All(Integer pageNo, Integer pageSize, String excludeCompanyIds, String api_name) throws Exception {
        Page page = new Page();
        if (pageNo == null || pageNo <= 0) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 20;
        }
        page.setPageNo(pageNo).setPageSize(pageSize);
        tradeAllCompanyDealer.dealWithAllCompanys(new CompanyDealOp() {
            @Override
            public Object deal(Staff staff) {
                doEraserAddressMd5(staff);
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("清理address_md5进度，pageNo=%s，companyId=%s", page.getPageNo(), staff.getCompanyId())));
                }
                return null;
            }

            @Override
            public String getBreakKey() {
                return "eraser_address_md5";
            }

            @Override
            public Page getPage() {
                return page;
            }

            @Override
            public Set<Long> getExcludeCompanyId() {
                return ArrayUtils.toLongSet(excludeCompanyIds);
            }
        });
        return successResponse();
    }

    /**
     * 清理淘宝、天猫的 AddressMd5 字段
     */
    @RequestMapping(value = "/eraser/address/md5", method = RequestMethod.POST)
    @ResponseBody
    public Object eraserAddressMd5(String companyIds, String api_name) throws Exception {
        Staff[] staffs = getStaffs(companyIds);
        for (Staff staff : staffs) {
            doEraserAddressMd5(staff);
        }
        return successResponse();
    }

    private void doEraserAddressMd5(Staff staff) {
        doEraserAddressMd5(staff, "trade");
        doEraserAddressMd5(staff, "trade_not_consign");
    }

    private void doEraserAddressMd5(Staff staff, String tableName) {
        Long companyId = staff.getCompanyId();
        Integer tbNo = staff.getDbInfo().getTradeDbNo();
        List<Long> sids;
        int limit = 500;
        int count = 0;
        String querySql = "select sid from " + tableName + "_" + tbNo + " where company_id=" + companyId + " and source in ('tb','tm') and length(address_md5)>0 limit 0," + limit;
        try {
            while ((sids = jdbcTemplateAdapter.get(staff).queryForList(querySql, Long.class)).size() > 0) {
                StringBuilder updateSql = new StringBuilder("update ").append(tableName).append("_").append(tbNo).append(" set address_md5='' where company_id=").append(companyId).append(" and sid in (0");
                for (Long sid : sids) {
                    updateSql.append(",").append(sid);
                }
                updateSql.append(")");
                jdbcTemplateAdapter.get(staff).execute(updateSql.toString());
                count += sids.size();
                if (sids.size() < limit) {
                    break;
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("清理address_md5出错，companyId=%s，tableName=%s", staff.getCompanyId(), tableName)), e);
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("清理address_md5结束，companyId=%s,count=%s", staff.getCompanyId(), count)));
        }
    }

    /**
     * 淘宝平台查询可以合并订单
     *
     * @param userId
     * @param tids
     * @return
     * @throws SessionException
     */
    @RequestMapping("/queryMergeable")
    @ResponseBody
    public Object queryMergeable(Long userId, String tids) throws SessionException {
        Staff staff = getStaff();
        Assert.isTrue(userId != null, "请传入对应的店铺id");
        Assert.isTrue(tids != null, "请传入淘宝订单编号");
        User user = staff.getUserIdMap().get(userId);
        if (null == user) {
            user = userService.queryById(userId);
        }
        if (!TradeUtils.isTbTrade(user.getSource())) {
            throw new IllegalArgumentException("传入的店铺id对应店铺不是淘宝店铺！");
        }
        List<String> tidList = Strings.getAsStringList(tids, ",", false);
        List<TbTrade> tbTrades = tradeSearchService.queryByTids(staff, false, tidList.toArray(new String[0]));
        Set<String> oaids = new HashSet<>();
        for (Trade trade : tbTrades) {
            if (StringUtils.isBlank(trade.getAddressMd5())) {
                throw new IllegalArgumentException("传入订单存在未脱敏订单！");
            }
            oaids.add(trade.getAddressMd5());
        }
        if (1 == oaids.size()) {
            return "传入订单oaid都相同，可以合并！";
        }
        return tbTradeDecryptBusiness.queryMerge(user, TradeUtils.toTrades(tbTrades));
    }

    /**
     * 清除oaid后台接口，只针对淘宝店铺的订单
     *
     * @param sids
     * @return
     * @throws SessionException
     */
    @RequestMapping("/clear/oaid")
    @ResponseBody
    public Object clearOaid(String sids) throws SessionException {
        Staff staff = getStaff();
        Assert.notNull(sids, "请传入订单号");
        List<TbTrade> trades = tbTradeDao.queryBySids(staff, Strings.getAsLongArray(sids, ",", false));
        if (CollectionUtils.isEmpty(trades)) {
            throw new IllegalArgumentException("未查询到订单，请检查传入订单号是否正确");
        }
        List<Long> needUpdates = trades.stream().filter(t -> StringUtils.isNotBlank(t.getAddressMd5())).map(Trade::getSid).collect(Collectors.toList());
        jdbcTemplateAdapter.get(staff).execute(buildClearOaidSql("trade_not_consign_" + staff.getDbInfo().getTradeDbNo(), staff.getCompanyId(), needUpdates));
        jdbcTemplateAdapter.get(staff).execute(buildClearOaidSql("trade_" + staff.getDbInfo().getTradeDbNo(), staff.getCompanyId(), needUpdates));
        return successResponse();
    }

    private String buildClearOaidSql(String tableName, Long companyId, List<Long> sids) {
        StringBuilder sql = new StringBuilder(" update ").append(tableName).append(" set address_md5='' where company_id= ").append(companyId).append(" and sid in (-1");
        for (Long sid : sids) {
            sql.append(",").append(sid);
        }
        sql.append(")");
        return sql.toString();
    }

    @RequestMapping("/getTableNo")
    @ResponseBody
    public Object getTableNo(Long companyId, Integer dbNo, String baseTableName, Integer tableCount) throws SessionException {
        return companyService.dynamicDynamicGenerate(companyId, dbNo, baseTableName, tableCount);
    }

    @RequestMapping("/repair/upload/consign")
    @ResponseBody
    public Object repairUploadConsign(Long companyId, String sidStr) throws Exception {
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        Long[] sidArray = ArrayUtils.toLongArray(sidStr);
        if (staff != null) {
            tradeService.consignUpload(staff, sidArray, null, null, false, 0);
        }
        return successResponse();
    }

    @RequestMapping(value = "/repair/sysStatus/finished", method = RequestMethod.POST)
    @ResponseBody
    public Object repairSysStatusFinished(String companyIds, String minUpdTime, String maxUpdTime, Integer maxloop) throws Exception {
        if (StringUtils.isEmpty(minUpdTime) || StringUtils.isEmpty(maxUpdTime)) {
            throw new IllegalArgumentException("请输入minUpdTime和maxUpdTime参数！");
        }
        if (maxloop == null) {
            maxloop = Integer.MAX_VALUE - 1;
        }
        List<Staff> staffs = tradeAllCompanyDealer.getStaffs(getStaff(), ArrayUtils.toLongArray(companyIds));
        for (Staff staff : staffs) {
            Long companyId = staff.getCompanyId();
            Integer tbNo = staff.getDbInfo().getTradeDbNo();
            String querySql = "select user_id,tid from trade_" + tbNo + " where company_id=" + companyId + " and sys_status='SELLER_SEND_GOODS' and unified_status='FINISHED' and enable_status>=1 and source!='sys' and is_upload !=4 and upd_time>='" + minUpdTime + "' and upd_time<='" + maxUpdTime + "' limit 0,1000";
            List<TbTrade> trades;
            int loop = 0;
            while ((trades = jdbcTemplateAdapter.get(staff).query(querySql, new BeanPropertyRowMapper<>(TbTrade.class))).size() > 0) {
                loop++;
                for (Trade trade : trades) {
                    try {
                        sysTradeService.syncTradeByTid(staff.getUserIdMap().get(trade.getUserId()), trade.getTid());
                    } catch (Exception e) {
                        logger.error(LogHelper.buildErrorLog(staff, e, String.format("订单下载失败[tid=%s]", trade.getTid())));
                    }
                }
                if (++loop >= maxloop) {
                    break;
                }
            }
        }
        return successResponse();
    }


    /**
     * 清理淘宝、天猫的 AddressMd5 字段
     */
    @RequestMapping(value = "/remove/consign/upload", method = RequestMethod.POST)
    @ResponseBody
    public Object removeConsignUpload(Long companyId, String orderIds, Integer delete, String api_name) throws Exception {
        if (StringUtils.isEmpty(orderIds)) {
            return successResponse();
        }
        Staff staff = companyId == null ? getStaff() : staffService.queryDefaultStaffByCompanyId(companyId);
        if (staff == null) {
            return successResponse();
        }
        if (delete == null) {
            delete = 0;
        }
        int uploadRecordDbNo = staff.getDbInfo().getOrderDbNo();
        StringBuilder updateSql = new StringBuilder();
        if (delete == 1) {
            updateSql.append("delete from upload_record_").append(uploadRecordDbNo);
        } else {
            updateSql.append("update upload_record_").append(uploadRecordDbNo).append(" set enable_status=0 ");
        }
        updateSql.append(" where company_id=").append(staff.getCompanyId()).append(" and order_id in (").append(orderIds).append(")");
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(staff).append(String.format("清除上传记录,updateSql=%s", updateSql)));
        }
        try {
            jdbcTemplateAdapter.get(staff).execute(updateSql.toString());
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "清除上传记录异常"), e);
        }
        return successResponse();
    }

    /**
     * 清理淘宝、天猫的 AddressMd5 字段
     */
    @RequestMapping(value = "/upload/retry", method = RequestMethod.POST)
    @ResponseBody
    public Object uploadRetry(String companyIds, String api_name) throws Exception {
        Staff[] staffs = getStaffs(companyIds);
        for (Staff staff : staffs) {
            uploadRetryBusiness.uploadRetry(staff);
        }
        return successResponse();
    }


    /**
     * 根据店铺+taobaoId作废订单
     */
    @RequestMapping(value = "/cancelTradesByDeleteUser", method = RequestMethod.POST)
    @ResponseBody
    public Object cancelTradesByUser(Long companyId, Long userId, Long taobaoId) throws Exception {
        Staff staff = companyId == null ? getStaff() : staffService.queryDefaultStaffByCompanyId(companyId);
        //shopDeleteListener.cancelWaitConsign(staff, userId, taobaoId);
        return successResponse();
    }


    /**
     * 清空订单商品验货记录接口
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @RequestMapping("/clear/tradeItemPackLog")
    @ResponseBody
    public Object deleteTradeItemPackLog(String ids) throws Exception {
        Staff staff = getStaff();
        List<Long> idList = Arrays.asList(Strings.getAsLongArray(ids, ",", false));
        List<TradeItemPackLog> tradeItemPackLogs = new ArrayList<>();
        for (Long id : idList) {
            TradeItemPackLog tradeItemPackLog = new TradeItemPackLog();
            tradeItemPackLog.setId(id);
            tradeItemPackLog.setCompanyId(staff.getCompanyId());
            tradeItemPackLogs.add(tradeItemPackLog);
        }
        tradeItemPackService.delete(staff, tradeItemPackLogs);
        return successResponse();
    }

    /**
     * 删除波次规则包含的userId
     *
     * @param userIds
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/delete/user/waveRule", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteWaveRuleByUserId(String userIds) throws SessionException {
        Staff staff = getStaff();
        if (StringUtils.isNotEmpty(userIds)) {
            eventCenter.fireEvent(this, new EventInfo("shop.delete.wave.rule").setArgs(new Object[]{staff, ArrayUtils.toLongArray(userIds)}), null);
        }
        return successResponse();
    }

    /**
     * 修复三方仓数据
     */
    @RequestMapping("/party3/repair")
    @ResponseBody
    public Object party3Repair(String unCreateSids, String reCreateSids, String createSids) throws SessionException {
        if (StringUtils.isNotEmpty(unCreateSids) || StringUtils.isNotEmpty(reCreateSids) || StringUtils.isNotEmpty(createSids)) {
            tradeParty3Business.invokeParty3Async(getStaff(), ArrayUtils.toLongArray(unCreateSids), ArrayUtils.toLongArray(reCreateSids), ArrayUtils.toLongArray(createSids), TradeParty3BusinessTypeEnum.BAKCEND_REPAIR);
        }
        return successResponse();
    }


    /**
     * 删除扫描打印记录
     *
     * @param ids
     * @return
     * @throws SessionException
     */
    @RequestMapping("/delete/uniqueCodeScanRecord/byIds")
    @ResponseBody
    public Object deleteUniqueCodeScanRecord(@RequestBody List<Long> ids) throws SessionException {
        uniqueCodeScanRecordDao.deleteById(getStaff(), ids);
        return successResponse();
    }


    @RequestMapping("/encrypt/historical")
    @ResponseBody
    public Object encryptHistoricalTrade(String companyIds, String endTime) throws SessionException {
        if (StringUtils.isBlank(companyIds)) {
            return new IllegalArgumentException("请传入公司id或店铺id");
        }
        String endUpdTime;
        if (StringUtils.isBlank(endTime)) {
            endUpdTime = "2021-10-12 00:00:00";
        } else {
            endUpdTime = endTime;
        }
        if (StringUtils.isNotBlank(companyIds)) {
            List<Long> companyIdList = Strings.getAsLongList(companyIds, ",", false);
            for (Long companyId : companyIdList) {
                Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
                if (null == staff) {
                    continue;
                }
                JdbcTemplate jdbcTemplate = jdbcTemplateAdapter.get(staff);
                int tradeDbNo = staff.getDbInfo().getTradeDbNo();
                String querySql = "select sid,company_id,user_id,source,receiver_name,receiver_mobile,receiver_address from trade_" + tradeDbNo + " where company_id=" + staff.getCompanyId() + " and source = 'yz'" + " and upd_time < '" + endUpdTime + "' limit ";
                int count = 0;
                int updateCount = 0;
                int startRow = 0;
                int pageSize = 500;
                while (true) {
                    String tempSql = querySql + startRow + "," + pageSize;
                    List<Trade> tradeList = jdbcTemplate.query(tempSql, new BeanPropertyRowMapper<>(Trade.class));
                    if (CollectionUtils.isEmpty(tradeList)) {
                        break;
                    }

                    tradeFillService.fill(staff, tradeList);
                    secretBusiness.decodeTrades(staff, tradeList);
                    List<Trade> needUpdates = commonTradeDecryptBusiness.encryptHistoryTrade(staff, tradeList);
                    if (CollectionUtils.isNotEmpty(needUpdates)) {
                        List<String> signTradeSql = new ArrayList<>();
                        List<String> tradeSql = new ArrayList<>();
                        for (Trade trade : needUpdates) {
                            StringBuilder sb = new StringBuilder();
                            boolean flag = false;
                            if (CommonSecretUtils.isYZEncrypt(trade.getReceiverName())) {
                                sb.append(" receiver_name = '").append(trade.getReceiverName()).append("'");
                                flag = true;
                            }
                            if (CommonSecretUtils.isYZEncrypt(trade.getReceiverMobile())) {
                                if (flag) {
                                    sb.append(" ,receiver_mobile = '").append(trade.getReceiverMobile()).append("'");
                                } else {
                                    sb.append(" receiver_mobile = '").append(trade.getReceiverMobile()).append("'");
                                    flag = true;
                                }
                            }
                            if (CommonSecretUtils.isYZEncrypt(trade.getReceiverAddress())) {
                                if (flag) {
                                    sb.append(" ,receiver_address = '").append(trade.getReceiverMobile()).append("'");
                                } else {
                                    sb.append(" receiver_address = '").append(trade.getReceiverMobile()).append("'");
                                }
                            }
                            if (sb.length() > 0) {
                                String value = sb.toString();
                                signTradeSql.add("update trade_not_consign_" + tradeDbNo + " set " + value + " where sid = " + trade.getSid());
                                tradeSql.add("update trade_" + tradeDbNo + " set " + value + " where sid = " + trade.getSid());
                            }

                        }
                        try {
                            jdbcTemplate.batchUpdate(signTradeSql.toArray(new String[0]));
                        } catch (Exception e) {
                            logger.info(LogHelper.buildLog(staff, "更新小表失败！"));
                        }
                        jdbcTemplate.batchUpdate(tradeSql.toArray(new String[0]));
                        updateCount += needUpdates.size();
                    } else {
                        startRow += pageSize;
                    }
                    count += tradeList.size();
                }
                logger.info(LogHelper.buildLog(staff, "加密有赞历史订单收件人信息完成，订单总数：" + count + "加密数：" + updateCount));

            }
        }
        return successResponse();
    }

    @RequestMapping(value = "/buffer/halt", method = RequestMethod.POST)
    @ResponseBody
    public Object bufferHalt(String type, Long groupId) throws SessionException {
        try {
            cache.set(type + "_" + groupId, "1");
        } catch (CacheException e) {
            logger.error(LogHelper.buildLog(getStaff(), "buffer挂起失败"), e);
        }
        return successResponse();
    }

    @RequestMapping(value = "/get/config", method = RequestMethod.POST)
    @ResponseBody
    public Object getConfig(String key) {
        return tradeLocalConfig.get(key);
    }

    @RequestMapping(value = "/get/diamond/config", method = RequestMethod.POST)
    @ResponseBody
    public Object getDiamondConfig(String moduleName) throws SessionException {
        Staff staff = getStaff();
        ErpPlatformConfig erpPlatformConfig = ConfigHolder.ERP_PLATFORM_CONFIG;
        return erpPlatformConfig.getUseExpressCompanyMappingMap();
    }


    @RequestMapping(value = "/get/diamond/config/v2", method = RequestMethod.GET)
    @ResponseBody
    public Object getDiamondConfigV2(String fieldName) throws SessionException {

        if(!CONFIG_HOLDER_FIELD_LIST.contains(fieldName)){
            return new Object();
        }

        return  ReflectionUtils.getField(ReflectionUtils.findField(ConfigHolder.class,fieldName), null);
    }


    /**
     * 对历史订单收件人详细地址加密
     *
     * @param companyIds
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/history/rinse/new", method = RequestMethod.POST)
    @ResponseBody
    public Object historyRinseNew(String companyIds, Integer dbNo, String endUpdTime) throws Exception {
        if (StringUtils.isBlank(companyIds)) {
            throw new IllegalArgumentException("请传入公司id");
        }
        if (StringUtils.isBlank(endUpdTime)) {
            endUpdTime = "2021-06-01 00:00:00";
        }
        Staff[] staffs = getStaffs(companyIds);
        //传入dbNo是为了处理切换过数据库的公司
        boolean needTemplate = true;
        JdbcTemplate jdbcTemplate = null;
        if (null != dbNo) {
            jdbcTemplate = jdbcTemplateAdapter.get(dbNo);
            if (null != jdbcTemplate) {
                needTemplate = false;
            }
        }
        for (Staff staff : staffs) {
            if (null == jdbcTemplate || needTemplate) {
                jdbcTemplate = jdbcTemplateAdapter.get(staff);
                needTemplate = false;
            }
            int tradeDbNo = staff.getDbInfo().getTradeDbNo();
            String querySql = "select sid,company_id,receiver_address from trade_" + tradeDbNo + " where company_id=" + staff.getCompanyId() + " and upd_time < '" + endUpdTime + "' limit ";
            int count = 0;
            int updateCount = 0;
            int startRow = 0;
            int pageSize = 500;
            while (true) {
                String tempSql = querySql + startRow + "," + pageSize;
                List<Trade> tradeList = jdbcTemplate.query(tempSql, new BeanPropertyRowMapper<>(Trade.class));
                if (CollectionUtils.isEmpty(tradeList)) {
                    break;
                }

                tradeFillService.fill(staff, tradeList);
                List<Trade> updateTrades = secretBusiness.buildEncodeAddressTrade(staff, tradeList);
                if (CollectionUtils.isNotEmpty(updateTrades)) {
                    //TODO更新数据库
                    List<String> signTradeSql = new ArrayList<>();
                    List<String> tradeSql = new ArrayList<>();
                    for (Trade trade : updateTrades) {
                        signTradeSql.add("update trade_not_consign_" + tradeDbNo + " set receiver_address = '" + trade.getReceiverAddress() + "' where sid = " + trade.getSid());
                        tradeSql.add("update trade_" + tradeDbNo + " set receiver_address = '" + trade.getReceiverAddress() + "' where sid = " + trade.getSid());
                    }
                    try {
                        jdbcTemplate.batchUpdate(signTradeSql.toArray(new String[0]));
                    } catch (Exception e) {
                        logger.info(LogHelper.buildLog(staff, "更新小表失败！"));
                    }
                    jdbcTemplate.batchUpdate(tradeSql.toArray(new String[0]));
                    updateCount += updateTrades.size();
                } else {
                    startRow += pageSize;
                }
                count += tradeList.size();
            }
            logger.info(LogHelper.buildLog(staff, "加密历史订单收件人信息完成，订单总数：" + count + "加密数：" + updateCount));

        }
        return successResponse();
    }


    /**
     * 对历史订单收件人详细地址加密
     *
     * @param companyIds
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/history/rinse", method = RequestMethod.POST)
    @ResponseBody
    public Object historyRinse(String companyIds, Long endTime, String sids, String api_name) throws Exception {
        if (StringUtils.isNotBlank(companyIds)) {
            Staff[] staffs = getStaffs(companyIds);
            for (Staff staff : staffs) {
                TradeQueryParams query = new TradeQueryParams();
                Page page = new Page();
                int count = 0;
                int updateCount = 0;
                int pageNo = 1;
                int pageSize = 500;
                page.setPageSize(pageSize);
                query.setPage(page);
                query.setCompanyId(staff.getCompanyId());
                if (null == endTime) {
                    query.setEndTime(new Date(1622476800000L));//2021-06-01
                } else {
                    query.setEndTime(new Date(endTime));
                }
                query.setTimeType("upd_time");
                while (true) {
                    page.setPageNo(pageNo);
                    List<Trade> tradeList = tradeSearchService.searchTradeByQueryParams(staff, query);
                    if (CollectionUtils.isEmpty(tradeList)) {
                        break;
                    }
                    List<Trade> updateTrades = secretBusiness.buildEncodeAddressTrade(staff, tradeList);
                    if (CollectionUtils.isNotEmpty(updateTrades)) {
                        tradeUpdateService.updateTrades(staff, updateTrades);
                        updateCount += updateTrades.size();
                    } else {
                        pageNo++;
                    }
                    count += tradeList.size();
                }
                logger.info(LogHelper.buildLog(staff, "加密历史订单收件人信息完成，订单总数：" + count + "加密数：" + updateCount));
            }
        } else if (StringUtils.isNotBlank(sids)) {
            Staff staff = getStaff();
            Long[] sidArr = Strings.getAsLongArray(sids, ",", false);
            List<Trade> tradeList = tradeSearchService.queryBySids(staff, false, sidArr);
            if (CollectionUtils.isEmpty(tradeList)) {
                return successResponse();
            }
            List<Trade> updateTrades = secretBusiness.buildEncodeAddressTrade(staff, tradeList);
            if (CollectionUtils.isNotEmpty(updateTrades)) {
                tradeUpdateService.updateTrades(staff, updateTrades);
            }
        }
        return successResponse();
    }

    /**
     * 修复历史数据（bug引起）
     *
     * @param amount 每次修复多长时间的数据 单位分钟
     */
    @RequestMapping(value = "/repair/gross/profit", method = RequestMethod.POST)
    @ResponseBody
    public Object repairGrossProfit(String companyIds, String oldMinUpdTimeStr, String oldMaxUpdTimeStr, Integer amount) throws Exception {
        if (StringUtils.isEmpty(oldMinUpdTimeStr) || StringUtils.isEmpty(oldMaxUpdTimeStr)) {
            throw new IllegalArgumentException("请输入minUpdTime和maxUpdTime参数！");
        }
        if (amount == null) {
            amount = 60;//默认1小时
        }
        Date oldMaxUpdTime = com.raycloud.dmj.domain.trades.utils.DateUtils.str2Date(oldMaxUpdTimeStr);
        List<Staff> staffs = tradeAllCompanyDealer.getStaffs(getStaff(), ArrayUtils.toLongArray(companyIds));
        for (Staff staff : staffs) {
            boolean forceUpdate = false;
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            //覆盖模式
            if ((tradeConfig.getCoverSysStatus() == null || tradeConfig.getCoverSysStatus() == 1)) {
                forceUpdate = true;
            }
            Date minUpdTime = com.raycloud.dmj.domain.trades.utils.DateUtils.str2Date(oldMinUpdTimeStr);
            Date maxUpdTime = DateUtils.addMinutes(minUpdTime, amount);
            do {
                String minUpdTimeStr = com.raycloud.dmj.domain.trades.utils.DateUtils.datetime2Str(minUpdTime);
                String maxUpdTimeStr = com.raycloud.dmj.domain.trades.utils.DateUtils.datetime2Str(maxUpdTime);
                String querySql = "select user_id,tid from trade_" + staff.getDbInfo().getTradeDbNo() + " where company_id=" + staff.getCompanyId() + " and enable_status>=1 and source!= 'sys' and user_id != ********* and upd_time>='" + minUpdTimeStr + "' and upd_time<'" + maxUpdTimeStr + "'";
                List<TbTrade> trades = jdbcTemplateAdapter.get(staff).query(querySql, new BeanPropertyRowMapper<>(TbTrade.class));
                minUpdTime = maxUpdTime;
                maxUpdTime = DateUtils.addMinutes(minUpdTime, amount);
                if (CollectionUtils.isEmpty(trades)) {
                    continue;
                }
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("修复毛利润数据，minUpdTimeStr=%s, maxUpdTimeStr=%s, size=%s", minUpdTimeStr, maxUpdTimeStr, trades.size())));
                Map<Long, Set<String>> userTidsMap = new HashMap<>();
                trades.forEach(trade -> userTidsMap.computeIfAbsent(trade.getUserId(), k -> new HashSet<>()).add(trade.getTid()));
                Map<Long, Set<String>> errorUserTidsMap = repairGrossProfit(staff, forceUpdate, userTidsMap, minUpdTimeStr, maxUpdTimeStr);
                if (errorUserTidsMap.size() > 0) {
                    int retry = 1;
                    while (retry++ < 10 && errorUserTidsMap.size() > 0) {
                        Logs.warn(LogHelper.buildLog(staff, String.format("修复毛利润字段报错重试，retry=%s, minUpdTimeStr=%s, maxUpdTimeStr=%s", retry, minUpdTimeStr, maxUpdTimeStr)));
                        errorUserTidsMap = repairGrossProfit(staff, forceUpdate, errorUserTidsMap, minUpdTimeStr, maxUpdTimeStr);
                    }
                }
            } while (oldMaxUpdTime.after(maxUpdTime));
        }
        return successResponse();
    }

    private Map<Long, Set<String>> repairGrossProfit(Staff staff, boolean forceUpdate, Map<Long, Set<String>> userTidsMap, String minUpdTimeStr, String maxUpdTimeStr) {
        Map<Long, Set<String>> errorUserTidsMap = new HashMap<>();
        int DEFAULT_BATCH_SIZE = 50;
        for (Map.Entry<Long, Set<String>> entry : userTidsMap.entrySet()) {
            User user = staff.getUserIdMap().get(entry.getKey());
            if (user == null) {
                Logs.warn(LogHelper.buildLog(staff, String.format("修复毛利润字段，找不到user，userId=%s,minUpdTimeStr=%s,maxUpdTimeStr=%s,tids=%s", entry.getKey(), minUpdTimeStr, maxUpdTimeStr, entry.getValue())));
                continue;
            }
            List<String> tids = new ArrayList<>(entry.getValue());
            List<List<String>> partitions = Lists.partition(tids, DEFAULT_BATCH_SIZE);
            for (List<String> subTids : partitions) {
                try {
                    Logs.ifDebug(LogHelper.buildLog(staff, String.format("修复毛利润数据，userId=%s, minUpdTimeStr=%s, maxUpdTimeStr=%s, size=%s,subTids=%s", user.getId(), minUpdTimeStr, maxUpdTimeStr, tids.size(), subTids)));
                    syncTradeBusiness.syncTradeByTids(staff, user, new HashSet<>(subTids), forceUpdate);
                } catch (Exception e) {
                    Logs.error(LogHelper.buildLog(staff, String.format("修复毛利润数据，指定下载出错，userId=%s,subTids=%s", user.getId(), tids)));
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException interruptedException) {

                    }
                    errorUserTidsMap.computeIfAbsent(user.getId(), k -> new HashSet<>()).addAll(subTids);
                }
            }
        }
        return errorUserTidsMap;
    }

    /**
     * 修复历史毛利润数据
     *
     * @param amount 每次修复多长时间的数据 单位分钟
     */
    @RequestMapping(value = "/repair/old/gross/profit", method = RequestMethod.POST)
    @ResponseBody
    public Object repairOldGrossProfit(String companyIds, String oldMinUpdTimeStr, String oldMaxUpdTimeStr, Integer amount) throws Exception {
        if (StringUtils.isEmpty(oldMinUpdTimeStr) || StringUtils.isEmpty(oldMaxUpdTimeStr)) {
            throw new IllegalArgumentException("请输入startTime和endTime参数！");
        }
        if (amount == null) {
            amount = 60;//默认1小时
        }
        Date oldMaxUpdTime = com.raycloud.dmj.domain.trades.utils.DateUtils.str2Date(oldMaxUpdTimeStr);
        List<Staff> staffs = tradeAllCompanyDealer.getStaffs(getStaff(), ArrayUtils.toLongArray(companyIds));
        for (Staff staff : staffs) {
            Date minUpdTime = com.raycloud.dmj.domain.trades.utils.DateUtils.str2Date(oldMinUpdTimeStr);
            Date maxUpdTime = DateUtils.addMinutes(minUpdTime, amount);
            do {
                String minUpdTimeStr = com.raycloud.dmj.domain.trades.utils.DateUtils.datetime2Str(minUpdTime);
                String maxUpdTimeStr = com.raycloud.dmj.domain.trades.utils.DateUtils.datetime2Str(maxUpdTime);
                String querySql = "select sid from trade_" + staff.getDbInfo().getTradeDbNo() + " where company_id=" + staff.getCompanyId() + " and enable_status>=1 and upd_time>='" + minUpdTimeStr + "' and upd_time<'" + maxUpdTimeStr + "'";
                List<Long> sids = jdbcTemplateAdapter.get(staff).queryForList(querySql, Long.class);
                minUpdTime = maxUpdTime;
                maxUpdTime = DateUtils.addMinutes(minUpdTime, amount);
                if (CollectionUtils.isEmpty(sids)) {
                    continue;
                }
                //这里需要考虑下upd_time的更新，（更新500W，报表全部也要更新要重新同步）
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("修复历史毛利润数据，minUpdTimeStr=%s, maxUpdTimeStr=%s,size=%s", minUpdTimeStr, maxUpdTimeStr, sids.size())));
                List<List<Long>> partitions = Lists.partition(sids, 1000);
                for (List<Long> partition : partitions) {
                    Long[] sidArr = partition.toArray(new Long[0]);
                    try {
                        grossProfitBusiness.updateGrossProfit(staff, sidArr);
                    } catch (Exception e) {
                        Logs.error(LogHelper.buildLog(staff, String.format("修复历史毛利润数据失败,sids=%s", partition)), e);
                    }
                }
            } while (oldMaxUpdTime.after(maxUpdTime));
        }
        return successResponse();
    }

    @RequestMapping("/fix/discountfee")
    @ResponseBody
    public Object fixDiscountFee(Long companyId, String tids) throws SessionException {
        Assert.notNull(companyId, "companyId不能为空！");
        Assert.notNull(tids, "tids不能为空！");
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);

        StringBuilder querySql = new StringBuilder("SELECT o.id,o.sid,o.tid,o.price,o.num,o.total_fee totalFee,o.payment,o.ac_payment acPayment,o.discount_fee discountFee, o.sale_fee saleFee, o.sale_price salePrice")
                .append(" FROM trade_").append(staff.getDbInfo().getTradeDbNo()).append(" t ")
                .append(" inner join order_").append(staff.getDbInfo().getOrderDbNo()).append(" o on t.company_id=o.company_id and t.sid=o.sid ")
                .append(" WHERE t.company_id = ").append(companyId).append(" AND t.enable_status > 0 AND o.enable_status > 0 and t.split_sid>0 and t.created>'2022-02-10 10:30:00' and t.created<'2022-02-28 15:30:00' ");
        Set<String> tidSet = ArrayUtils.toStringSet(tids);
        querySql.append(" and t.tid in (");
        for (String tid : tidSet) {
            querySql.append("'").append(tid).append("',");
        }
        querySql.append(" '111111')");
        List<TbOrder> list = jdbcTemplateAdapter.get(staff).query(querySql.toString(), new BeanPropertyRowMapper<>(TbOrder.class));
        Set<Long> sids = new HashSet<>();
        for (Order order : list) {
            String paymentStr =order.getPayment();
            String discountFee = new BigDecimalWrapper(order.getTotalFee()).subtract(order.getPayment()).getString();
            String updateOrder = "update order_" + staff.getDbInfo().getOrderDbNo() + " set discount_fee=" + discountFee + ",payment=" + paymentStr + ",ac_payment=" + paymentStr + " where company_id=" + companyId + " and id =" + order.getId();
            Logs.ifDebug(LogHelper.buildLog(staff, "order金额修复，" + updateOrder));
            jdbcTemplateAdapter.get(staff).execute(updateOrder);
            sids.add(order.getSid());
        }
        StringBuilder sb = new StringBuilder("金额修复，trade=[");
        List<Trade> updateTradeList = new ArrayList<>();
        List<Trade> trades = solrTradeSearchService.queryBySids(staff, true, false, false, sids.toArray(new Long[0]));
        for (Trade trade : trades) {
            sb.append("sid=").append(trade.getSid()).append(",oldPayment=").append(trade.getPayment());
            PaymentUtils.calculateTrade(trade);
            sb.append(",newPayment=").append(trade.getPayment()).append(";");
            Trade updateTrade = new TbTrade();
            updateTrade.setSid(trade.getSid());
            updateTrade.setPayment(trade.getPayment());
            updateTrade.setAcPayment(trade.getPayment());
            updateTradeList.add(updateTrade);
        }
        if (updateTradeList.size() > 0) {
            tradeUpdateService.updateTrades(staff, updateTradeList);
        }
        Logs.ifDebug(LogHelper.buildLog(staff, sb.toString()));
        return successResponse();
    }

    @RequestMapping("/fix/salefee")
    @ResponseBody
    public Object fixSalefee(Long companyId, String tids) throws SessionException {
        Assert.notNull(companyId, "companyId不能为空！");
        Assert.notNull(tids, "tids不能为空！");
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        StringBuilder sb = new StringBuilder("分销价修复，trade=[");
        List<Trade> updateTradeList = new ArrayList<>();
        List<TbTrade> trades = solrTradeSearchService.queryByTids(staff, true, ArrayUtils.toStringArray(tids));
        for (Trade trade : trades) {
            sb.append("sid=").append(trade.getSid()).append(",oldSalePrice=").append(trade.getSalePrice());
            PaymentUtils.calculateTrade(trade);
            String totalSaleFee = TradeUtils.calculateTradeSaleFee(trade);
            sb.append(",newSalePrice=").append(totalSaleFee).append(";");
            Trade updateTrade = new TbTrade();
            updateTrade.setSid(trade.getSid());
            updateTrade.setSaleFee(totalSaleFee);
            updateTrade.setSalePrice(totalSaleFee);
            updateTradeList.add(updateTrade);
        }
        if (updateTradeList.size() > 0) {
            tradeUpdateService.updateTrades(staff, updateTradeList);
        }
        Logs.ifDebug(LogHelper.buildLog(staff, sb.toString()));
        return successResponse();
    }

    @RequestMapping("/fix/no/order")
    @ResponseBody
    public Object fixNoOrder(Long companyId, String tids) throws SessionException {
        Assert.notNull(companyId, "companyId不能为空！");
        Assert.notNull(tids, "tids不能为空！");
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        List<TbTrade> tbTrades = solrTradeSearchService.queryByTids(staff, true, ArrayUtils.toStringArray(tids));
        Map<Long, Map<String, List<TbTrade>>> userTidTradeMap = new HashMap<>();
        for (TbTrade trade : tbTrades) {
            userTidTradeMap.computeIfAbsent(trade.getUserId(), k -> new HashMap<>()).computeIfAbsent(trade.getTid(), k -> new ArrayList<>()).add(trade);
        }
        userTidTradeMap.forEach((userId, tidTradeMap) -> fixNoOrder(staff, userId, tidTradeMap));
        return successResponse();
    }

    @RequestMapping("/set/master/user")
    @ResponseBody
    public Object setMasteUser(Long companyId, Long userId) throws SessionException {
        Assert.notNull(companyId, "companyId不能为空！");
        Assert.notNull(userId, "用户id不能为空！");
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        userService.setMasterUser(staff, userId);
        return successResponse();
    }

    private void fixNoOrder(Staff staff, Long userId, Map<String, List<TbTrade>> tidTradeMap) {
        User user = staff.getUserByUserId(userId);
        if (user == null) {
            user = userService.queryById(userId);
        }
        if (user == null) {
            return;
        }
        for (Map.Entry<String, List<TbTrade>> entry : tidTradeMap.entrySet()) {
            Set<Long> oids = new HashSet<>();
            List<Trade> cancelTrades = new ArrayList<>();
            for (TbTrade trade : entry.getValue()) {
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                if (CollectionUtils.isNotEmpty(orders)) {
                    orders.forEach(order -> oids.add(order.getId()));
                } else {
                    Logs.ifDebug(LogHelper.buildLog(staff, "作废订单，sid=" + trade.getSid() + ",tid=" + trade.getTid()));
                    sysTradeService.cancel(staff, new Long[]{trade.getSid()});
                    cancelTrades.add(trade);
                }
            }
            if (cancelTrades.size() > 1) {
                Logs.warn(LogHelper.buildLog(staff, "作废订单了多个订单，只需要取消作废一个，sids=" + TradeUtils.toSidList(cancelTrades)));
            }
            Trade unCancelTrade = cancelTrades.get(0);
            List<Order> unCancelOrders = new ArrayList<>();
            Trade trade = tradeDownloadService.downloadTrade(user, entry.getKey());

            List<Order> platOrders = TradeUtils.getOrders4Trade(trade);
            for (Order platOrder : platOrders) {
                if (!oids.contains(platOrder.getOid())) {
                    platOrder.setSid(unCancelTrade.getSid());
                    unCancelOrders.add(platOrder);
                }
            }
            if (unCancelOrders.size() == 0) {
                Logs.warn(LogHelper.buildLog(staff, "修复失败，tid=" + entry.getKey()));
                return;
            }
            tradeUpdateService.updateTrades(staff, new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), unCancelOrders);
            sysTradeService.uncancel(staff, new Long[]{unCancelTrade.getSid()}, true);
        }
    }

    /**
     * 修改用户订单同步方式（api/rds）
     * syncType：0 rds增量, 1 api增量, 2 api全量
     * newSync: 0 api，1 订单池v1（无用，已迁v2），2 订单池v2
     */
    @ResponseBody
    @RequestMapping(value = "/update/conf/syncType", method = RequestMethod.POST)
    public Object updateTradeSyncType(String userIds, Integer syncType, Integer newSync) {
        Assert.notNull(userIds, "店铺id不能为空");
        // 取值只能为0，1，2
        Assert.isTrue(Objects.nonNull(syncType) && syncType >= 0 && syncType < 3, "请输入正确的tradeSyncType");

        List<Long> userIdList = ArrayUtils.toLongList(userIds);
        List<User> users = userService.queryByIds(userIdList);
        if (CollectionUtils.isNotEmpty(users)) {
            boolean isUpdateNewSync = Objects.nonNull(newSync) && newSync >= 0 && newSync < 3;
            for (User origin : users) {
                if (!CommonConstants.PLAT_FORM_TYPE_FXG.equalsIgnoreCase(origin.getSource())) {
                    logger.debug("非抖店用户不处理:" + origin.getId());
                    continue;
                }
                User user = new User();
                user.setId(origin.getId());
                user.setNick(origin.getNick());
                user.setCompanyId(origin.getCompanyId());

                UserConf userConf = origin.getUserConf();
                if (Objects.isNull(userConf)) {
                    userConf = new UserConf();
                }
                if (isUpdateNewSync) {
                    // 回退下游标
                    Date originCursor = origin.getLastImportTradeTime();
                    if (Objects.nonNull(originCursor)) {
                        Date newCursor = DateUtils.addHours(originCursor, -1);
                        user.setLastImportTradeTime(newCursor);
                    }
                    userService.updateNewSync(user.getCompanyId(), user.getId(), newSync);
                }
                userConf.setTradeSyncType(syncType);
                user.setUserConf(userConf);
                userService.updateUser((Staff) null, user);
            }
            return successResponse();
        } else {
            throw new IllegalArgumentException("没有需要操作的店铺");
        }
    }

    /**
     * BTAS订单重新匹配 交易配置信息
     * @param sids 系统单号，用 , 隔开
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/btas/config/match",method = RequestMethod.POST)
    public Object matchBtasConfig(String sids) throws Exception {
        Assert.hasText(sids, "请传入系统订单号");
        Long[] sidArr = ArrayUtils.toLongArray(sids);
        if (null == sidArr || sidArr.length == 0){
            throw new IllegalArgumentException("请提供符合格式要求的系统单号");
        }
        Staff staff = getStaff();
        List<TbTrade> tbTrades = tbTradeDao.queryBySids(staff, sidArr);
        List<Long> btasSids = tbTrades.stream().filter(TradeTypeUtils::isFxgBtasTrade).map(TbTrade::getSid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(btasSids)){
            throw new IllegalArgumentException("没有查询到BTAS订单");
        }
        Map<Long, List<TbTrade>> tbTradeMap = tbTrades.stream().filter(TradeTypeUtils::isFxgBtasTrade).collect(Collectors.groupingBy(TbTrade::getSid));
        List<TradeExt> updateTradeExts = tradeExtDao.tradeExtsGetBySids(staff, btasSids);
        List<TradeExt> insertTradeExts = new ArrayList<>();
        List<TradeExt> matchTradeExt = new ArrayList<>(updateTradeExts);
        btasSids.removeAll(updateTradeExts.stream().map(TradeExt::getSid).collect(Collectors.toList()));
        for (Long sid : btasSids){
            Trade trade = tbTradeMap.get(sid).get(0);
            TradeExt tradeExt = new TradeExt();
            tradeExt.setCompanyId(trade.getCompanyId());
            tradeExt.setUserId(trade.getUserId());
            tradeExt.setSid(trade.getSid());
            tradeExt.setTid(trade.getTid());
            insertTradeExts.add(tradeExt);
        }
        matchTradeExt.addAll(insertTradeExts);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        for (TradeExt tradeExt : matchTradeExt){
            TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtendConfigsEnum.BTAS_EXPRESS_PRODUCT.getKey(), tradeConfig.get(TradeExtendConfigsEnum.BTAS_EXPRESS_PRODUCT.getKey()));
            TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtendConfigsEnum.BTAS_INSURED_PRICE.getKey(), tradeConfig.get(TradeExtendConfigsEnum.BTAS_INSURED_PRICE.getKey()));
            TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtendConfigsEnum.BTAS_GOODS_REJECTED_INTERCEPT.getKey(), tradeConfig.get(TradeExtendConfigsEnum.BTAS_GOODS_REJECTED_INTERCEPT.getKey()));
            TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtendConfigsEnum.BTAS_QUALITY_CHECK_RETURN_TYPE.getKey(), tradeConfig.get(TradeExtendConfigsEnum.BTAS_QUALITY_CHECK_RETURN_TYPE.getKey()));
            Integer insuranceCostType = TradeConfigUtils.getInsuredPriceType(tradeConfig, tradeExt.getUserId());
            TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.INSURANCE_COST_TYPE.getField(), insuranceCostType);
            if (insuranceCostType != 2){
                TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.INSURED_PRICE.getField(), 0.00D);
            }
        }
        tradeExtDao.batchUpdate(staff, updateTradeExts);
        tradeExtDao.batchInsert(staff, insertTradeExts);
        return successResponse();
    }

    /**
     *  修复按数量退款的订单的库存数量
     * @param sids
     * @return
     * @throws SessionException
     */
    @ResponseBody
    @RequestMapping(value = "/repair/refundnum/stock",method = RequestMethod.POST)
    public Object repairRefundNumStock(String sids) throws SessionException {
        Staff staff = getStaff();
        Set<Long> sidsSet = ArrayUtils.toLongSet(sids);
        tradeRefundItemNumExceptBusines.handlerTradeItemNumExceptStock(staff,sidsSet);
        return successResponse();
    }



    /**
     * 修复成本价工具v2
     * 参数:按时间范围+指定商品修复
     * 文档:https://tb.raycloud.com/task/643dfb7d81d6d7001e53d6fb
     * @param sysItemIds
     * @param sysSkuIds
     * @param startTime
     * @param endTime
     * @param amount
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/v2/cost/fix", method = RequestMethod.POST)
    @ResponseBody
    public Object costFix(Long companyId,String sysItemIds, String sysSkuIds, String startTime, String endTime, Integer amount, Integer tradeItemCostConfig,String timeField) throws Exception {
//        Assert.notNull(sysItemIds, "sysItemIds not null!");
        Assert.notNull(startTime, "startTime not null!");
        Assert.notNull(endTime, "endTime not null!");
        Staff staff = getStaff();
        if (companyId != null) {
            staff = staffService.queryDefaultStaffByCompanyId(companyId);
        }
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            throw new IllegalArgumentException("请输入startTime和endTime参数！");
        }
        if (StringUtils.isEmpty(timeField)) {
            timeField = "upd_time";
        }
        if (amount == null) {
            amount = 60;//默认1小时
        }
        Date temp = com.raycloud.dmj.domain.trades.utils.DateUtils.str2Date(endTime);
        Date minUptTime = com.raycloud.dmj.domain.trades.utils.DateUtils.str2Date(startTime);
        Date maxUptTime = DateUtils.addMinutes(minUptTime, amount);
        if (maxUptTime.after(temp)) {
            maxUptTime = temp;
        }
        while(true){
            String minUptTimeStr = com.raycloud.dmj.domain.trades.utils.DateUtils.datetime2Str(minUptTime);
            String maxUptTimeStr = com.raycloud.dmj.domain.trades.utils.DateUtils.datetime2Str(maxUptTime);
            String sqlCondition = null;
            if (StringUtils.isNotEmpty(sysItemIds)) {
                sqlCondition = " and item_sys_id in(" + sysItemIds + ")";
            }
            if (StringUtils.isNotEmpty(sysSkuIds)) {
                sqlCondition += " and sku_sys_id in(" + sysSkuIds + ")";
            }
            String querySql = "select sid from order_" + staff.getDbInfo().getOrderDbNo() + " where company_id=" + staff.getCompanyId() + " and enable_status>=1 " + (StringUtils.isNotEmpty(sqlCondition) ? sqlCondition : "") + " and " + timeField + ">='" + minUptTimeStr + "' and " + timeField + "<='" + maxUptTimeStr + "'";
            List<Long> sids = jdbcTemplateAdapter.get(staff).queryForList(querySql, Long.class);
            minUptTime = maxUptTime;
            maxUptTime = DateUtils.addMinutes(minUptTime, amount);
            if (CollectionUtils.isEmpty(sids)) {
                continue;
            }
            List<List<Long>> partitions = Lists.partition(sids, 100);
            Long counter = 0L;
            for (List<Long> partition : partitions) {
                counter += partition.size();
                new PaymentLogBuilder(staff).append("修复成本价数据").append("minUptTime",minUptTimeStr).append("maxUptTime",maxUptTimeStr)
                        .append(" ").append(counter).append("/").append(sids.size()).printDebug(logger);
                Long[] sidArr = partition.toArray(new Long[0]);
                try {
                    costFix(StringUtils.join(sidArr, ","), true, tradeItemCostConfig);
                } catch (Exception e) {
                    new PaymentLogBuilder(staff).append("修复成本价数据失败 ").appendArray(sids,partition).printWarn(logger,e);
                }
            }
            if (temp.equals(maxUptTime)) {
                break;
            }
        }
        return successResponse();
    }


    @ResponseBody
    @RequestMapping(value = "/clear/trade/except/three/month", method = RequestMethod.POST)
    public Object clearTradeExcept(Long companyId,String api_name) throws SessionException {
        Staff staff = companyId != null ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
        try {
            clear3MonthBeforeDataBusiness.clearTradeExcept(staff);
            return successResponse();
        }catch (Exception e){
            Logs.error(LogHelper.buildErrorLog(staff,e,"清除数据失败！"));
            return Status.buildFailStatus();
        }

    }

    @ResponseBody
    @RequestMapping(value = "/clear/trade/except/three/month/sids", method = RequestMethod.POST)
    public Object clearTradeExceptBySid(Long companyId, String sids, Integer isClearLog) throws SessionException {
        Staff staff = companyId != null ? staffService.queryDefaultStaffByCompanyId(companyId) : getStaff();
        try {
            Assert.isTrue(StringUtils.isBlank(sids),"sid必填!");
            clear3MonthBeforeDataBusiness.clearTradeExcept(staff,ArrayUtils.toLongList(sids),Objects.equals(isClearLog,1));
            return successResponse();
        }catch (Exception e){
            Logs.error(LogHelper.buildErrorLog(staff,e,"清除数据失败！"));
            return Status.buildFailStatus();
        }
    }

    /**
     * 修复合单运费
     * 非主单的理论运费清除掉
     * 非主单的实际运费清除掉
     * @param sids 订单系统单号 (主单单号)
     * @param fields 需要操作的字段
     * 理论运费：theoryPostFee； 实际运费：actualPostFee
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/fix/merge/postFee", method = RequestMethod.POST)
    @ResponseBody
    public Object fixMergePostFee(String sids, String fields ) throws Exception {
        Assert.hasText(sids, "请传入系统订单号");
        Assert.hasText(fields, "请传入需要更新的字段");
        List<Long> sidList = Strings.getAsLongList(sids, ",", true);
        if (CollectionUtils.isEmpty(sidList)){
            throw new IllegalArgumentException("请提供符合格式要求的系统单号");
        }
        List<String> fieldList = Strings.getAsStringList(fields, ",", true);
        if (CollectionUtils.isEmpty(fieldList)){
            throw new IllegalArgumentException("请提供符合格式要求的字段名称");
        }
        StringBuilder field = new StringBuilder("");
        if (fieldList.contains("theoryPostFee")){
            field.append("theory_post_fee");
        }
        if (fieldList.contains("actualPostFee")){
            if (StringUtils.isNotBlank(field)){
                field.append(",");
            }
            field.append("actual_post_fee");
        }
        if (StringUtils.isBlank(field)){
            throw new IllegalArgumentException("更新字段暂不支持");
        }

        Staff staff = getStaff();
        Assert.isTrue(null != staff, "员工信息为空");
        AtomicInteger update = new AtomicInteger();
        String queryField = "sid, merge_sid" + "," + field.toString();
        Lists.partition(sidList, 200).forEach(t ->{
            List<Trade> updateTrades = new ArrayList<>();
            List<TbTrade> trades = tbTradeDao.queryByKeys(staff, queryField, "merge_sid", t.toArray(new Long[0]));
            for (TbTrade origin : trades){
                //合单、非主单、理论运费不是0的进行更新
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, origin) && !Objects.equals(origin.getSid(), origin.getMergeSid())){
                    Trade trade = null;
                    if (fieldList.contains("theoryPostFee") && !MathUtils.equalsZero(origin.getTheoryPostFee())){
                        trade = new TbTrade();
                        trade.setTheoryPostFee(0.00D);
                    }
                    if (fieldList.contains("actualPostFee") && !MathUtils.equalsZero(origin.getActualPostFee())){
                        if (null == trade){
                            trade = new TbTrade();
                        }
                        trade.setActualPostFee("0.00");
                    }
                    if (null != trade){
                        trade.setSid(origin.getSid());
                        updateTrades.add(trade);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(updateTrades)){
                update.addAndGet(updateTrades.size());
                tradeUpdateService.updateTrades(staff, updateTrades);
            }
        });
        return "queryField: " + queryField + "; updateSize: " + update;
    }

    /**
     * 订单拆分模式后端测试接口
     * @param tradeSplitEnum 拆分类型， TradeSplitEnum的名称
     * @param fx2Gx 是否供分销拆分新模式
     * @param splitData 拆分数据
     * [
     *   {
     *      "sid":231234231213,
     * 		"order":[
     *          {
     * 			    "id":21323323132,
     * 				"num":2
     *          },
     *          {
     * 				"id":21323323131,
     * 				"num":1
     *          }
     * 		]
     *    }
     * 	]
     * @return 拆分结果
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/split/all",method = RequestMethod.POST)
    public Object splitAllBackend(String tradeSplitEnum, boolean fx2Gx, @RequestBody List<TbTrade> splitData) throws Exception {
        Staff staff = getStaff();
        Assert.isTrue(null != staff, "员工信息为空");
        Assert.isTrue(StringUtils.isNotEmpty(tradeSplitEnum), "拆分类型不能为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(splitData), "拆分信息不能为空");
        TradeSplitEnum[] splitEnums = TradeSplitEnum.values();
        TradeSplitEnum splitType = null;
        for (TradeSplitEnum splitEnum : splitEnums){
            if (splitEnum.name().equals(tradeSplitEnum)){
                splitType = splitEnum;
            }
        }
        Assert.isTrue(null != splitType, "拆分类型不存在");
        SplitParams params = new SplitParams();
        Map<Long, List<Trade>> splitDataMap = splitData.stream().collect(Collectors.groupingBy(Trade::getSid));
        splitDataMap.forEach((k, v) ->{
            Trade trade = new TbTrade();
            trade.setSid(k);
            params.splitDataMap.put(trade, v);
        });
        if (fx2Gx){
            params.splitFx2GxNew = TradeSplitConstant.FX_AND_GX_MODE;
        }
        SplitResultData splitResultData = splitAllInOneBusiness.split(staff, splitType, params);
        Map<String, Object> result = new HashMap<>();
        result.put("sidSplitSidsMap", splitResultData.sidSplitSidsMap);
        result.put("mergeSidSplitMap", splitResultData.mergeSidSplitMap);
        if (null != params.splitFx2GxData){
            result.put("originSids", params.splitFx2GxData.originTrades.keySet());
            result.put("allSplitDataSids", params.splitFx2GxData.allSplitDataMap.keySet());
            result.put("allTradeSids", params.splitFx2GxData.trades.keySet());
            result.put("fx2GxMap", params.splitFx2GxData.fx2GxMap);
            result.put("Fx2GxSucSplitSids", params.splitFx2GxData.sucSplitSids);
            result.put("Fx2GxUndoSplitSids", params.splitFx2GxData.undoSplitSids);
        }
        return result;
    }


    @ResponseBody
    @RequestMapping(value = "/repair/stock/record/sids", method = {RequestMethod.POST})
    public Object repairstockrecord(String sids,Integer mode) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入sids参数");
        Staff staff = getStaff();
        List<Long> sidList = ArrayUtils.toLongList(sids);
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sidList.toArray(new Long[0]));
        Assert.isTrue(CollectionUtils.isNotEmpty(trades), "未找到订单!");
        List<Trade> updateTrades=new ArrayList<>();
        List<Order> updateOrders=new ArrayList<>();
        List<Trade> updateOriginTrades=new ArrayList<>();
        List<Order> updateOriginOrders=new ArrayList<>();

        for(Trade trade:trades){
            TbTrade tbTrade = new TbTrade();
            tbTrade.setSid(trade.getSid());
            tbTrade.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
            tbTrade.setTid(trade.getTid());
            updateTrades.add(tbTrade);

            TbTrade originTrade = new TbTrade();
            originTrade.setSid(trade.getSid());
            originTrade.setSysStatus(trade.getSysStatus());
            originTrade.setTid(trade.getTid());
            updateOriginTrades.add(originTrade);

            List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
            for(Order order:orders4Trade){
                TbOrder tbOrder = new TbOrder();
                tbOrder.setId(order.getId());
                tbOrder.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
                tbOrder.setRefundStatus(Order.NO_REFUND);
                updateOrders.add(tbOrder);

                TbOrder originOrder = new TbOrder();
                originOrder.setId(order.getId());
                originOrder.setSysStatus(order.getSysStatus());
                originOrder.setRefundStatus(order.getRefundStatus());
                updateOriginOrders.add(originOrder);

                if(CollectionUtils.isNotEmpty(order.getSuits())){
                    for(Order suit:order.getSuits()){
                        TbOrder updateOrder = new TbOrder();
                        updateOrder.setId(suit.getId());
                        updateOrder.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
                        updateOrder.setRefundStatus(Order.NO_REFUND);
                        updateOrders.add(updateOrder);

                        TbOrder originsuit = new TbOrder();
                        originsuit.setId(suit.getId());
                        originsuit.setSysStatus(suit.getSysStatus());
                        originsuit.setRefundStatus(order.getRefundStatus());
                        updateOriginOrders.add(originsuit);

                    }
                }
            }
        }
        tradeUpdateService.updateTrades(staff,updateTrades,updateOrders);
        tradeConfigStockBusiness.handleStock(staff, sidList.toArray(new Long[0]), new TradeImportResult());
        // 更新回去
        if(Objects.equals(0,mode)){
            tradeUpdateService.updateTrades(staff,updateOriginTrades,updateOriginOrders);
        }
        return successResponse();
    }

    @ResponseBody
    @RequestMapping(value = "/update/sysstatus", method = {RequestMethod.POST})
    public  Object updateStatus(String sids,String sysStatus,String orderStatus) throws SessionException {
        Staff staff = getStaff();
        List<Long> sidList = ArrayUtils.toLongList(sids);
        if(CollectionUtils.isEmpty(sidList)||StringUtils.isBlank(sysStatus)){
            return Status.buildFailStatus();
        }
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sidList.toArray(new Long[0]));
        List<Trade> updateTrades=new ArrayList<>();
        List<Order> updateOrders=new ArrayList<>();
        for(Trade trade:trades){
            TbTrade tbTrade = new TbTrade();
            tbTrade.setSid(trade.getSid());
            tbTrade.setTid(trade.getTid());
            tbTrade.setSysStatus(sysStatus);
            updateTrades.add(tbTrade);
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
            for(Order order:orders4Trade){
                TbOrder tbOrder = new TbOrder();
                tbOrder.setId(order.getId());
                tbOrder.setSysStatus(sysStatus);
                if(StringUtils.isNotBlank(orderStatus)){
                    tbOrder.setRefundStatus(orderStatus);
                }
                updateOrders.add(tbOrder);
                if(CollectionUtils.isNotEmpty(order.getSuits())){
                    for(Order suit:order.getSuits()){
                        TbOrder updateOrder = new TbOrder();
                        updateOrder.setId(suit.getId());
                        updateOrder.setSysStatus(sysStatus);
                        if(StringUtils.isNotBlank(orderStatus)){
                            updateOrder.setRefundStatus(orderStatus);
                        }
                        updateOrders.add(updateOrder);
                    }
                }
            }
        }
        tradeUpdateService.updateTrades(staff,updateTrades,updateOrders);
        return successResponse();
    }


    @RequestMapping(value = "/stock/except/check/repair", method = RequestMethod.POST)
    @ResponseBody
    public Object checkExceptStock(String companyIds, String api_name) throws SessionException {
        if(StringUtils.isBlank(companyIds)){
            Staff staff = getStaff();
            tradeOrderStockCheckBusiness.checkTradeOrderStock(staff);
        }else {
            List<Long> compnayIds = ArrayUtils.toLongList(companyIds);
            for(Long companyId:compnayIds){
                Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
                tradeOrderStockCheckBusiness.checkTradeOrderStock(staff);
            }
        }
        return successResponse();
    }

    @RequestMapping(value = "/stock/except/check/test", method = RequestMethod.POST)
    @ResponseBody
    public Object checkExceptStockTest(String sids,Integer notice, String api_name) throws SessionException {
        Staff staff = getStaff();
        Set<Long> sidList = ArrayUtils.toSet(sids);
        tradeOrderStockCheckBusiness.save(staff, sidList,notice);
        return successResponse();
    }

    @ResponseBody
    @RequestMapping(value = "/audit/force", method = RequestMethod.POST)
    public Object auditForce(Long companyId, String sids) {
        return tradeAuditDubboBusiness.auditForce(companyId, ArrayUtils.toLongList(sids));
    }

    @ResponseBody
    @RequestMapping(value = "/mock/search", method = RequestMethod.GET)
    public Object searchTrade(String source, String subSource, Integer companyCount, Integer tradeCount) {

        Assert.hasText(source, "source不能为空");
        companyCount = Objects.isNull(companyCount) ? 10 : companyCount;
        tradeCount = Objects.isNull(tradeCount) ? 10 : tradeCount;

        Map<Long, List<Long>> result = new LinkedHashMap<>();

        List<User> users = userService.queryBySources(Collections.singletonList(source), new Page(1, companyCount));

        Map<Long, List<User>> company2UsersMap = CollectionsUtils.group(users, User::getCompanyId);

        for (Map.Entry<Long, List<User>> entry : company2UsersMap.entrySet()) {
            Long companyId = entry.getKey();
            User user = entry.getValue().get(0);
            Staff defaultStaff = staffAssembleBusiness.getDefaultStaff(companyId);
            TradeQueryParams tradeQueryParams = new TradeQueryParams()
                    .setCompanyId(companyId)
                    .setSource(user.getSource())
                    .setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT)
                    .setQueryOrder(false)
                    .setNeedFill(false)
                    .setQueryFlag(1)
                    .setFields("t.sid,t.tid")
                    .setSort(new Sort("created", "DESC"))
                    .setPage(new Page().setPageNo(1).setPageSize(tradeCount));
            if (StringUtils.isNotBlank(subSource)) {
                tradeQueryParams.setSubSource(subSource);
            }
            List<Trade> trades = tradeSearchService.backTradeSearchUnfuse(defaultStaff, tradeQueryParams).getList();

            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(trades)) {
                result.computeIfAbsent(companyId, k -> new ArrayList<>()).addAll(TradeUtils.toSidList(trades));
            }
        }

        return result;
    }


    @ResponseBody
    @RequestMapping(value = "/fix/hotItem", method = RequestMethod.POST)
    public Object hotItemFix(TradeControllerParams params, Page page) throws Exception {
        Staff staff = getStaff();
        if (params == null) {
            return Status.buildFailStatus();
        }
        TradeQueryParams tradeQueryParams = TradeQueryParams.copyParams(params);
        tradeQueryParams.setPage(page != null ? page : new Page(1, 200000));
        tradeQueryParams.setQueryOrder(false);
        tradeQueryParams.setQueryFlag(1);
        tradeQueryParams.setFields("t.sid");
        tradeQueryParams.setIgnoreFilter(1);
        Trades trades = tradeSearchService.search(staff, tradeQueryParams);
        if (trades != null && trades.getList() != null) {
            for (List<Long> part : Lists.partition(TradeUtils.toSidList(trades.getList()), 5000)) {
                orderHotItemService.write(staff, part);
            }
        }
        return successResponse();
    }


    @ResponseBody
    @RequestMapping(value = "/fire/tradeSyncCompleteEvent", method = RequestMethod.GET)
    public Object fireTradeSyncCompleteEvent(String companyIds) throws SessionException {
        if(StringUtils.isBlank(companyIds)) {
            eventCenter.fireEvent(this, new EventInfo("trade.sync.complete").setArgs(new Object[]{getStaff()}), null);
        } else {
            for(Long companyId: ArrayUtils.toLongList(companyIds)){
                Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
                eventCenter.fireEvent(this, new EventInfo("trade.sync.complete").setArgs(new Object[]{staff}), null);
            }
        }
        return successResponse();
    }


}

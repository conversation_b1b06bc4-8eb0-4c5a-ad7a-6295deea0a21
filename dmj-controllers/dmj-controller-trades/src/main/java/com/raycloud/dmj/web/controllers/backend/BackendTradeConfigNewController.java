package com.raycloud.dmj.web.controllers.backend;

import com.google.common.base.Preconditions;
import com.raycloud.dmj.dao.trade.TradeConfigDao;
import com.raycloud.dmj.domain.Status;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.config.TradeConfigNewUtils;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.cache.CommonMixedSharedCache;
import com.raycloud.dmj.services.dubbo.TradeServiceDubbo;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.trades.config.TradeConfigNewService;
import com.raycloud.dmj.services.trades.config.dao.TradeConfigNewDao;
import com.raycloud.dmj.services.trades.config.inner.cache.IConfigMixedSharedCache;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.web.controllers.Sessionable;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;

@Controller
@RequestMapping("/trade/config/new/backend")
public class BackendTradeConfigNewController extends Sessionable {

    @Resource
    TradeConfigNewDao tradeConfigNewDao;

    @Resource
    TradeConfigDao tradeConfigDao;

    @Resource
    IStaffService staffService;

    @Resource
    CommonMixedSharedCache commonMixedSharedCache;

    @Resource
    TradeConfigNewService tradeConfigNewWithoutLocalService;

    @Resource
    IConfigMixedSharedCache configMixedSharedCache;

    @Resource(name = "dubboTradeService")
    private TradeServiceDubbo tradeServiceDubbo;

    @Resource
    ITradeConfigNewService tradeConfigNewService;

    /**
     * 模拟后端配置的查询 本地缓存+中心缓存+数据库
     */
    @RequestMapping(value = "/getsFromLocalCenter", method = RequestMethod.POST)
    @ResponseBody
    public Object getsFromLocalCenter(@RequestBody Set<String> keys) throws SessionException {
        Preconditions.checkArgument(keys != null && !keys.isEmpty(), "key must not be empty");

        Staff staff = getStaff();
        TradeConfigEnum[] enums = new TradeConfigEnum[keys.size()];
        int i = 0;
        for (String key : keys) {
            TradeConfigEnum configEnum = TradeConfigEnum.KEY_TO_ENUM.get(key);
            if (configEnum == null) {
                throw new IllegalArgumentException(String.format("unknow key '%s'", key));
            }
            enums[i++] = configEnum;
        }
        return tradeConfigNewService.getMap(staff, enums);
    }

    @RequestMapping(value = "/updateById", method = RequestMethod.POST)
    @ResponseBody
    public Object updateById(Long id, String key, String value) throws SessionException {
        Preconditions.checkArgument(id != null && id > 0 && key != null && !key.isEmpty() && value != null && !value.isEmpty(), "id或value不能为空");
        Preconditions.checkArgument(TradeConfigEnum.KEY_TO_ENUM.containsKey(key), "key 不存在");

        Staff staff = getStaff();
        List<TradeConfigNew> delConfigNews = new ArrayList<>();
        List<TradeConfigNew> insertConfigNews = new ArrayList<>();
        TradeConfigNew delConfigNew = new TradeConfigNew();
        delConfigNew.setId(id);
        delConfigNew.setConfigKey(key);
        delConfigNews.add(delConfigNew);

        TradeConfigNew insertConfigNew = new TradeConfigNew();
        insertConfigNew.setConfigKey(key);
        insertConfigNew.setConfigValue(value);
        insertConfigNews.add(insertConfigNew);
        tradeConfigNewDao.delAndInsert(staff, delConfigNews, insertConfigNews);
        configMixedSharedCache.deleteAll(staff);
        configMixedSharedCache.deletes(staff, delConfigNews);
        return Status.buildSuccessStatus();
    }

    @RequestMapping(value = "/batchUpdate", method = RequestMethod.POST)
    @ResponseBody
    public Object partialUpdateTradeConfig(@RequestBody BatchUpdateReq req) {
        if (req.companyIds == null || req.companyIds.isEmpty() ||
                req.key == null || req.key.isEmpty()
        ) {
            return Status.buildFailStatus();
        }

        for (Long companyId : req.companyIds) {
            Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
            if (staff == null) {
                continue;
            }

            tradeConfigNewWithoutLocalService.update(staff, req.key, req.value);
        }
        return Status.buildSuccessStatus();
    }

    @RequestMapping(value = "/partialUpdateTradeConfig", method = RequestMethod.POST)
    @ResponseBody
    public Object partialUpdateTradeConfig(@RequestBody TradeConfig tradeConfig) throws SessionException {
        Staff staff = getStaff();

        tradeConfigDao.update(staff, tradeConfig);
        commonMixedSharedCache.delete("trade_config_" + staff.getCompanyId());
        return successResponse();
    }


    @RequestMapping(value = "/clearDirtyData", method = RequestMethod.GET)
    @ResponseBody
    public Object clearDirtyData() throws SessionException {
        Staff staff = getStaff();

        doClearDirtyData(staff);
        return this.successResponse();
    }

    @RequestMapping(value = "/clearDirtyDataByCompanyIds", method = RequestMethod.POST)
    @ResponseBody
    public Object clearDirtyDataByCompanyIds(@RequestBody Set<Long> companyIds) {
        Assert.isTrue(companyIds != null && !companyIds.isEmpty(),
                "companyIds must not be empty");

        for (Long companyId : companyIds) {
            Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
            if (staff == null) {
                continue;
            }
            doClearDirtyData(staff);
        }
        return this.successResponse();
    }

    private void doClearDirtyData(Staff staff) {
        List<TradeConfigNew> query = tradeConfigNewDao.query(staff);
        if (query == null || query.isEmpty()) {
            return;
        }
        Set<Long> ids = new HashSet<>(query.size());
        for (TradeConfigNew config : query) {
            ids.add(config.getId());
        }

        Map<String, TradeConfigNew> keyToConfig = TradeConfigNewUtils.config2Map(query);
        Set<Long> actualIds = new HashSet<>(keyToConfig.size());
        for (String k : keyToConfig.keySet()) {
            actualIds.add(keyToConfig.get(k).getId());
        }

        if (actualIds.size() == ids.size()) {
            return;
        }

        List<TradeConfigNew> deleteConfigs = new ArrayList<>(ids.size());
        for (Long id : ids) {
            if (actualIds.contains(id)) {
                continue;
            }

            TradeConfigNew config = new TradeConfigNew();
            config.setId(id);
            config.setCompanyId(staff.getCompanyId());
            deleteConfigs.add(config);
        }

        tradeConfigNewDao.delete(staff, deleteConfigs);
    }

    @Setter
    @Getter
    public static class BatchUpdateReq implements Serializable {
        private static final long serialVersionUID = -9198525027607154311L;

        Set<Long> companyIds;
        String key;
        String value;
    }

    @RequestMapping(value = "/getOpenLogisticsTracking", method = RequestMethod.GET)
    @ResponseBody
    public Object getOpenLogisticsTracking(Long companyId) {
        return tradeServiceDubbo.getOpenLogisticsTracking(companyId);
    }

}

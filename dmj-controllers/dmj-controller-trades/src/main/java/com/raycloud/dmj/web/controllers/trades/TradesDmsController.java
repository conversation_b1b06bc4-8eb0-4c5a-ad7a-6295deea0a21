package com.raycloud.dmj.web.controllers.trades;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.common.StaffAssembleBusiness;
import com.raycloud.dmj.business.fx.*;
import com.raycloud.dmj.business.split.SplitBatchBusiness;
import com.raycloud.dmj.dms.domain.dto.*;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.trades.fx.FxOperateResult;
import com.raycloud.dmj.business.trade.CommonTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.FxgTradeDecryptBusiness;
import com.raycloud.dmj.dms.request.scm.ChangeDistributionItemByErpItemRequest;
import com.raycloud.dmj.dms.response.scm.ChangeDistributionItemByErpItemResponse;
import com.raycloud.dmj.dms.service.trade.api.IDmsScmFxService;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.log.LogTag;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.audit.AuditData;
import com.raycloud.dmj.domain.trades.fx.gxSplit.TradeGxSplitParams;
import com.raycloud.dmj.domain.trades.fx.refreshPrice.TradeRefreshParams;
import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import com.raycloud.dmj.services.utils.LogKit;
import com.raycloud.dmj.web.ResponseDataWrapper;
import com.raycloud.dmj.web.controllers.Sessionable;
import com.raycloud.dmj.web.model.trades.TradeModels;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@Controller
@RequestMapping("/trade/dms")
@Scope("prototype")
@LogTag(value = "trade", enableArgs = "true", enableResponse = "true")
public class TradesDmsController extends Sessionable {
    private static final List<String> QUERY_TRADE_FIELDS = Lists.newArrayList("t.sid,t.dest_id,t.source_id,t.convert_type,t.belong_type");

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private IDmsTradeService dmsTradeService;

    @Resource
    private IShopService shopService;

    @Resource
    private IExpressCompanyService expressCompanyService;

    @Resource
    private ITradeConfigService tradeConfigService;

    @Resource
    TradeQueryController tradeQueryController;

    @Resource
    ISysTradeDmsService sysTradeDmsService;

    @Resource
    IDmsScmFxService dmsScmFxService;

    @Resource
    FxBusiness fxBusiness;

    @Resource
    AlibabaFxBusiness alibabaFxBusiness;

    @Resource
    StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    ITradeConfigNewService tradeConfigNewService;

    @Resource
    IStaffService staffService;

    @Resource
    FxgTradeDecryptBusiness fxgTradeDecryptBusiness;

    @Resource(name = "tbTradeSearchService")
    TradeSearchService tbTradeSearchService;
    @Resource
    ITradeQueryService tradeQueryService;
    @Resource
    IProgressService progressService;
    @Resource
    IEventCenter eventCenter;
    @Resource
    private CommonTradeDecryptBusiness commonTradeDecryptBusiness;
    @Resource
    private FxUrgingSupplierShipBusiness fxUrgingSupplierShipBusiness;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @RequestMapping(value = "/queryDmsDistributorConfigInfo", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Object queryDmsDistributorConfigInfo(Long fxCompanyId) throws Exception {
        Assert.isTrue(fxCompanyId != null && fxCompanyId > 0, "请输入正确的分销公司Id!");
        Staff staff = getStaff();

        Integer dmsRole = fxBusiness.queryDmsRole(staff.getCompanyId(),2);
        if(!Objects.equals(dmsRole,2)){
            throw new IllegalArgumentException(String.format("当前公司跟公司Id【%s】，不存在供分销关系",fxCompanyId));
        }

        DmsDistributorConfigDto configDto = dmsTradeService.queryDmsDistributorConfig(fxCompanyId);
        configDto.setDmsAllowGxEditTradeItem(tradeConfigNewService.get(staffService.queryFullByCompanyId(fxCompanyId), TradeConfigEnum.DMS_ALLOW_GX_EDIT_TRADE_ITEM).isOpen());
        return configDto;
    }


    /**
     * 根据供应商id查询下面所有的分销商
     *
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/queryDmsDistributorInfoList", method = RequestMethod.POST)
    @ResponseBody
    public Object queryDmsDistributorInfoList(String api_name) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        Staff staff = getStaff();
        Long companyId = staff.getCompanyId();
        List<DmsSupplierInfoDto> dmsSupplierInfoDtos = fxBusiness.queryDmsSupplierInfoListByDmsDistributorId(companyId);
        List<DmsDistributorInfoDto> dmsDistributorInfoDtos = fxBusiness.queryDmsDistributorInfoListBySupperlierId(companyId);
        result.put("supplierList",dmsSupplierInfoDtos);
        result.put("distributorList",dmsDistributorInfoDtos);
        return result;
    }

    @RequestMapping(value = "/queryDmsSupplierInfo", method = RequestMethod.POST)
    @ResponseBody
    public Object queryDmsSupplierInfo(Long gxCompanyId) throws Exception {
        Assert.isTrue(gxCompanyId != null && gxCompanyId > 0, "请输入正确的供销公司Id!");
        Staff staff = getStaff();
        Long companyId = staff.getCompanyId();

        Integer dmsRole = fxBusiness.queryDmsRole(companyId,1);
        if(!Objects.equals(dmsRole,1)){
            throw new IllegalArgumentException(String.format("当前公司跟公司Id【%s】，不存在供分销关系",gxCompanyId));
        }

        DmsSupplierForDisConfigDto configDto = dmsTradeService.queryDmsSupplierConfig(gxCompanyId,companyId);
        configDto.setDmsAllowFxAppointTemplateId(tradeConfigNewService.get(staffService.queryFullByCompanyId(gxCompanyId), TradeConfigEnum.DMS_ALLOW_FX_APPOINT_TEMPLATE_ID).isOpen());
        return configDto;
    }



    /**
     * 分销商付款
     *
     * @param sids
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/distributorPay", method = RequestMethod.POST)
    @ResponseBody
    public Object distributorPay(String sids, String api_name) throws Exception {
        Long[] sidsArray = getSidLongArray(sids);
        Staff staff = getStaff();
        List<Trade> trades = sysTradeDmsService.distributorPay(staff, ArrayUtils.toLongArray(sids));
        return toTradeModels(staff,tbTradeSearchService.queryBySids(staff, true, sidsArray));
    }


    /**
     * 新增分销属性
     *
     * @param params
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/addDistributorAttribute", method = RequestMethod.POST)
    @ResponseBody
    public Object addDistributorAttribute(TradeControllerParams params, ChangeDistributionItemByErpItemRequest itemRequest, String api_name, Integer addDistributorAttributeType,Long currentSid) throws Exception {
        Staff staff = getStaff();
        String type = StringUtils.isNotBlank(params.getUserSearch()) ? params.getUserSearch() : "0";
        if ("0".equals(type)) {
            String sids = params.getSids();
            Assert.notNull(sids, "请输入订单号!");
            Long[] sidsArray = ArrayUtils.toLongArray(sids);
            FxOperateResult fxOperateResult = new FxOperateResult(FxOperateResult.ADD_DISTRIBUTOR_ATTRIBUTE);
            // 直接将普通商品“转为分销商品”并刷新对应表
            if(itemRequest!=null && Objects.nonNull(itemRequest.getErpSysItemId())){
                fxOperateResult.getParam().put("erpSysItemId", itemRequest.getErpSysItemId());
                fxOperateResult.getParam().put("erpSysSkuId", itemRequest.getErpSysSkuId());
                itemRequest.setStaff(staff);
                itemRequest.setCompanyId(staff.getCompanyId());
                fxBusiness.changeDistributionItem(params,itemRequest,currentSid);
                List<Trade> trades = tbTradeSearchService.queryAndAssemblyByKeys(staff, TradeAssembleParams.justTrade().setQueryFields(QUERY_TRADE_FIELDS), "t.sid", sidsArray);
                fxBusiness.matchSupplyIdsWithDmsAttrTypeAndSave(staff.getUser(),trades,3,1, fxOperateResult);
            }else {
                sysTradeDmsService.addDistributorAttribute(staff, sidsArray, addDistributorAttributeType, fxOperateResult);
            }
            TradeModels tradeModels = toTradeModels(staff,tbTradeSearchService.queryBySids(staff, true, false, true, sidsArray));
            tradeModels.setFxOperateResult(fxOperateResult);
            return tradeModels;
        } else {
            Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_ADD_DISTRIBUTORATTRIBUTE), "当前正在进行新增分销属性操作,请稍后再试");
            Boolean kmtOperate = false;
            // 直接将普通商品“转为分销商品”并刷新对应表
            if(itemRequest!=null && Objects.nonNull(itemRequest.getErpSysItemId())){
                itemRequest.setStaff(staff);
                itemRequest.setCompanyId(staff.getCompanyId());
                fxBusiness.changeDistributionItem(params,itemRequest,currentSid);
                params.setExcludeExceptionStatus(TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED);
                kmtOperate = true;
            }
            Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_ADD_DISTRIBUTORATTRIBUTE), "当前正在进行新增分销属性操作,请稍后再试");
            eventCenter.fireEvent(this, new EventInfo("trade.batch.addDistributorAttribute").setArgs(new Object[]{staff, params, addDistributorAttributeType, kmtOperate}), null);
            return successResponse();
        }




    }

    /**
     * 强制制定分销商
     *
     * @param sids
     * @param destId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/forceDistributorAttribute", method = RequestMethod.POST)
    @ResponseBody
    public Object forceDistributorAttribute(String sids,String destId) throws Exception {
        Long[] sidArray = getSidLongArray(sids);
        Staff staff = getStaff();
        sysTradeDmsService.forceDistributorAttribute(staff,sidArray , destId);
        List<Trade> trades = tbTradeSearchService.queryBySids(staff, true, sidArray);
        fxgTradeDecryptBusiness.batchSensitive(staff, trades);
        tradeQueryService.fillDestAndSourceName(trades);
        return toTradeModels(staff,trades);
    }

    /**
     * 强制制定分销商
     *
     * @param sids
     * @param destId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/appointAlibabaSupply", method = RequestMethod.POST)
    @ResponseBody
    public Object appointAlibabaSupply(String sids,Long destId) throws Exception {
        Long[] sidArray = getSidLongArray(sids);
        Staff staff = getStaff();
        sysTradeDmsService.appointAlibabaSupply(DmsAttrData.builder().staff(staff).sids(sidArray).destId(destId).build());
        List<Trade> trades = tbTradeSearchService.queryBySids(staff, true, sidArray);
        fxgTradeDecryptBusiness.batchSensitive(staff, trades);
        tradeQueryService.fillDestAndSourceName(trades);
        return toTradeModels(staff,trades);
    }

    /**
     * 强制制定分销商
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/syncAlibabaSupply", method = RequestMethod.POST)
    @ResponseBody
    public Object syncAlibabaSupply(Long userId) throws Exception {
        Assert.isTrue(userId != null && userId > 0, "请输入正确的店铺userId!");
        Staff staff = getStaff();
        User user = staffAssembleBusiness.getUser(staff, userId, staff.getUserIdMap());
        Assert.isTrue(user != null , "请输入正确的店铺userId!");
        if(!(StringUtils.equals(user.getSource(), CommonConstants.PLAT_FORM_TYPE_1688_FX) && StringUtils.equals(user.getSubSource(),CommonConstants.PLAT_FORM_TYPE_1688_FX_ROLE))){
            throw  new IllegalArgumentException(String.format("【%s】不是1688分销小店分销店铺",user.getNick()));
        }
        alibabaFxBusiness.syncSupply(user,staff);
        return successResponse();
    }

    /**
     * 强制推单到供销商
     *
     * @param sids
     * @param destId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/forcePushTradeToGx", method = RequestMethod.POST)
    @ResponseBody
    public Object forcePushTradeToGx(String sids,String destId) throws Exception {
        Long[] sidArray = getSidLongArray(sids);
        Staff staff = getStaff();
        AuditData auditData =  sysTradeDmsService.forcePushTradeToGx(staff,sidArray , destId);
        return TradeResultVoWrapper.models2Vo(auditData.results.values());
    }

    /**
     * 获取支付信息
     *
     * @param sids
     * @param destId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/getPaymentInfo", method = RequestMethod.POST)
    @ResponseBody
    public Object getPaymentInfo(String sids, String destId) throws Exception {
        Long[] sidArray = getSidLongArray(sids);
        Staff staff = getStaff();
        return  sysTradeDmsService.getPaymentInfo(staff,sidArray , destId);
    }

    /**
     * 取消分销属性
     *
     * @param sids
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/cancelDistributorAttribute", method = RequestMethod.POST)
    @ResponseBody
    public Object cancelDistributorAttribute(String sids, String api_name) throws Exception {
        Long[] sidsArray = getSidLongArray(sids);
        Staff staff = getStaff();
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_CANCEL_DISTRIBUTOR_ATTRIBUTE), "正在进行取消分销属性操作，请稍后重试！");
        progressService.setProgress(staff, ProgressEnum.PROGRESS_CANCEL_DISTRIBUTOR_ATTRIBUTE, 1);

        TradeQueryParams params = new TradeQueryParams();
        params.setSid(sidsArray);
        eventCenter.fireEvent(this, new EventInfo("common.group.event").setArgs(new Object[]{staff, "trade.batch.cancelDistributorAttribute", params, ProgressEnum.PROGRESS_CANCEL_DISTRIBUTOR_ATTRIBUTE}), null);
        return successResponse();
    }


    /**
     * 供销订单打回
     *
     * @param sids
     * @param excepMsg
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/repulseGxTrade", method = RequestMethod.POST)
    @ResponseBody
    public Object repulseGxTrade(String sids,String excepMsg,Integer isCover) throws Exception {
        Long[] sidsArray = getSidLongArray(sids);
        Staff staff = getStaff();
        sysTradeDmsService.repulseGxTrade(staff, sidsArray,excepMsg,isCover);
        return toTradeModels(staff,tbTradeSearchService.queryBySids(staff, true, sidsArray));
    }



    /**
     * 供销商缺货拆分
     *
     * @param sids
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/splitDest/insufficient/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object splitDestInsufficientBatch(String sids) throws Exception {
        getSidLongArray(sids);
        Staff staff = getStaff();
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_GX_SPLIT_INSUFFICIENT), "上一次批量缺货拆分还未执行完毕，请稍等！");
        TradeControllerParams params = new TradeControllerParams();
        params.setSid(sids);
        eventCenter.fireEvent(this, new EventInfo("gx.split.insufficient.batch").setArgs(new Object[]{staff, params}), null);
        return successResponse();
    }



    private Long[] getSidLongArray(String sids) throws Exception{
        Assert.notNull(sids, "请输入订单号!");
        return ArrayUtils.toLongArray(sids);
    }


    private TradeModels toTradeModels(Staff staff, List<Trade> trades) {
        if (trades.size() == 0) {
            return TradeModels.toEmpty();
        }
        TradeModels tradeModels = TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfigService.get(staff));
        tradeQueryController.stockInfoModel(staff,tradeModels);
        return tradeModels;
    }

    /**
     * 供销订单运费修改
     *
     * @param sid
     * @param freightCost
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/updateGxFreight", method = RequestMethod.POST)
    @ResponseBody
    public Object updateGxFreight(Long sid, Double freightCost) throws Exception {
        Assert.notNull(sid, "请输入订单号!");
        Assert.isTrue(freightCost != null && freightCost > 0, "请输入正确的运费!");
        Staff staff = getStaff();
        return sysTradeDmsService.updateGxFreight(staff, sid, freightCost);
    }

    /**
     * 供销订单运费重算
     *
     * @param sid
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/reCalculateGxFreight", method = RequestMethod.POST)
    @ResponseBody
    public Object reCalculateGxFreight(Long sid) throws Exception {
        Assert.notNull(sid, "请输入订单号!");
        Staff staff = getStaff();
        Trade trade = sysTradeDmsService.reCalculateGxFreight(staff, sid);
        Map<String, Object> result = new HashMap<>();
        result.put("freight", trade.getPostFee());
        return result;
    }

    /**
     * 供销订单运费重算
     *
     * @param sid
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/matchGroupFreight", method = RequestMethod.POST)
    @ResponseBody
    public Object matchGroupFreight(Long sid) throws Exception {
        Assert.notNull(sid, "请输入订单号!");
        Staff staff = getStaff();
        Map<String, Object> result = new HashMap<>();
        result.put("freight", sysTradeDmsService.matchGroupFreight(staff, sid));
        return result;
    }

    /**
     * 普通-天猫分销订单互转
     */
    @RequestMapping(value = "commonPlatformFx/mutualConvert/batch")
    @ResponseBody
    public Object mutualConvert(String sids) throws Exception {
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入订单号!");
        Staff staff = getStaff();
        sysTradeDmsService.mutualConvertPlatformFxTrade(staff, getSidLongArray(sids));
        return toTradeModels(staff,tbTradeSearchService.queryBySids(staff, true, false, true, getSidLongArray(sids)));
    }

    /**
     * 供销商混合拆分
     *
     */
    @RequestMapping(value = "/splitDest/mix", method = RequestMethod.POST)
    @ResponseBody
    public Object splitDestMix(@RequestBody TbTrade trade, String groupCount, String api_name) throws Exception {
        Assert.notNull(trade, "请输入trade参数");
        Assert.notNull(groupCount, "请输入分组参数");
        Staff staff = getStaff();
        sysTradeDmsService.splitDestMix(staff, trade, groupCount);
        return successResponse();
    }

    /**
     * 刷新订单分销价
     * @param params 查询参数
     * @param refreshParams 刷新参数
     */
    @RequestMapping(value = "/refreshPrice", method = RequestMethod.POST)
    @ResponseBody
    public Object refreshPrice(TradeControllerParams params, TradeRefreshParams refreshParams, String api_name) throws Exception {
        Staff staff = getStaff();
        Assert.notNull(params.getSysStatus(), "请输入订单系统状态");
        Assert.isTrue(progressService.addProgress(staff, ProgressEnum.PROGRESS_REFRESH_GX_PRICE), "正在进行供销订单分销价重算，请稍后再试！");
        if (null == refreshParams){
            refreshParams = new TradeRefreshParams();
        }
        refreshParams.initTradeRefreshParams();
        eventCenter.fireEvent(this, new EventInfo(TradeEvents.GX_REFRESH_PRICE_BATCH).setArgs(new Object[]{staff, params, refreshParams}), null);
        return successResponse();
    }

    @RequestMapping(value = "/urging/supplier/ship",method = RequestMethod.POST)
    @ResponseBody
    public Object urgingSupplierShip(String sids) throws Exception {
        Staff staff = getStaff();
        int result = fxUrgingSupplierShipBusiness.urgeSupplierShip(staff,sids);
        String message = result>1 ? "本次成功处理"+result+"条，可能有一些不符合条件被过滤掉了，就不在成功内" : "操作成功";
        return new ResponseDataWrapper<>().setApi_name("/urging/supplier/ship")
                .setData(successResponse())
                .setMessage(message)
                .setResult(1)
                .setClueId(String.valueOf(ClueIdUtil.getClueId()));
    }





    @Resource
    FxLogisticsWarningBusiness fxLogisticsWarningBusiness;

    @RequestMapping(value = "/logistics/statistics", method = RequestMethod.POST)
    @ResponseBody
    public Object logisticsStatistics(Integer days,Long queryId,String logisticsWarningTypes, String api_name) throws Exception {
        Staff staff = getStaff();
        Assert.notNull(days, "请输入统计日期范围");
        return fxLogisticsWarningBusiness.queryLogisticsWarningStatistics(staff, days,logisticsWarningTypes,queryId);
    }

    @RequestMapping(value = "/logistics/sids", method = RequestMethod.POST)
    @ResponseBody
    public Object logisticsStatistics(Integer days,Integer logisticsWarningType,String warningTypeInterval,Long queryId,String api_name) throws Exception {
        Staff staff = getStaff();
        Assert.notNull(days, "请输入统计日期范围");
        Assert.notNull(logisticsWarningType, "请输入统计类型");
        Assert.notNull(warningTypeInterval, "请输入统计时间分段");
        return fxLogisticsWarningBusiness.queryLogisticsWarningDetailIdList(staff,days,queryId,logisticsWarningType,warningTypeInterval);
    }




    /**
     * 供销批量处理-指定规则拆分
     * 与交易的指定规则拆分用一个进度条
     * @param queryParams
     * @param ruleId
     * @param api_name
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/split/config/batch", method = RequestMethod.POST)
    @ResponseBody
    public Object splitConfigBatch(TradeControllerParams queryParams, Long ruleId, String api_name) throws Exception {
        Assert.notNull(ruleId, "请输入规则Id");
        Assert.notNull(queryParams, "请输入请求参数");
        Staff staff = getStaff();
        Assert.isTrue(progressService.addProgress(staff, TradeGxSplitBatchBusiness.progressEnum), "上次拆分还未结束，请稍等！");
        TradeGxSplitParams splitParams = new TradeGxSplitParams();
        splitParams.setRuleId(ruleId);
        splitParams.setTradeSplitEnum(TradeSplitEnum.SPLIT_CONFIG);
        eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_GX_SPLIT_BATCH).setArgs(new Object[]{staff, queryParams, splitParams}), null);
        return successResponse();
    }

    /**
     * 供销批量处理-一键拆分
     */
    @RequestMapping(value = "/split/one/click", method = RequestMethod.POST)
    @ResponseBody
    public Object splitOneClick(TradeControllerParams queryParams, String api_name) throws Exception {
        Assert.notNull(queryParams, "请输入请求参数");
        Staff staff = getStaff();
        Assert.isTrue(progressService.addProgress(staff, TradeGxSplitBatchBusiness.progressEnum), "上次拆分还未结束，请稍等！");
        TradeGxSplitParams splitParams = new TradeGxSplitParams();
        splitParams.setTradeSplitEnum(TradeSplitEnum.SPLIT_ONE_CLICK);
        eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_GX_SPLIT_BATCH).setArgs(new Object[]{staff, queryParams, splitParams}), null);
        return successResponse();
    }
}

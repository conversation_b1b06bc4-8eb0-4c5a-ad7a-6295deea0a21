package com.raycloud.dmj.web.model.trades;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.common.CipherTextUtils;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.domain.OrderConstant;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.domian.TradeSourcePlatformCodeRelation;
import com.raycloud.dmj.domain.enums.OpVEnum;
import com.raycloud.dmj.domain.enums.TmallAsdpBizTypeEnum;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptWhiteUtils;
import com.raycloud.dmj.domain.trade.invoice.TradeInvoice;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.invoice.TradeInvoice;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.tradepay.TradePayVO;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.utils.PaymentUtils;
import com.raycloud.dmj.domain.trades.vo.GovSubsidyInfo;
import com.raycloud.dmj.domain.trades.vo.SmtEstimatedRevenueVo;
import com.raycloud.dmj.domain.trades.vo.WaveAssembleInfo;
import com.raycloud.dmj.domain.user.*;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.utils.UserUtils;
import com.raycloud.dmj.domain.utils.diamond.TradeSourcePlatformCodeDiamondUtils;
import com.raycloud.dmj.except.domain.ExceptData;
import com.raycloud.dmj.except.utils.ExceptUtils;
import com.raycloud.dmj.services.platform.trades.dto.PlatformTradeInfoDTO;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.platform.trades.dto.PlatformTradeInfoDTO;
import com.raycloud.dmj.services.trades.deliverylimit.PddLogisticsAccess;
import com.raycloud.dmj.services.trades.support.search.TradeFieldUtils;
import com.raycloud.dmj.services.trades.support.search.TradeSearchBlurService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.tb.trade.TbTradeAccess;
import com.raycloud.dmj.utils.CommonSecretUtils;
import com.raycloud.dmj.web.model.trades.tag.TradeTagVo;
import com.raycloud.dmj.web.utils.TypeConvertUtils;
import com.taobao.api.domain.Refund;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.*;
import org.apache.log4j.Logger;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.web.model.trades.ExceptionStatusHelper.*;

/**
 * 有关交易的Model
 *
 * <AUTHOR>
 */
public class TradeModel implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -2936632669622581998L;

    private static final Logger logger = Logger.getLogger(TradeModel.class);
    private List<Long> exceptIds;
    private List<Long> typeIds;
    private String[] tids;
    private String tid;
    private String sid;
    private String shortId;

    private Long taobaoId;

    private Long waveId;

    private Long waveShortId;

    private String status;

    private String unifiedStatus;

    private String chStatus;

    private Date created;

    /**
     * 是否退款订单，包括退款中于退款完成
     */
    private Integer isRefund;

    /**
     * 是否有已退款完成的商品
     */
    private Integer hasRefunded;

    private Integer isExcep;

    private Integer isStockOut;

    private Integer isHalt;

    /**
     * 快递单打印状态
     */
    private Integer expressStatus;

    /**
     * 发货单打印状态
     */
    private Integer deliverStatus;

    /**
     * 配货单打印状态
     */
    private Integer assemblyStatus;

    private Integer mergeSplitType;

    private Integer checkManualMergeCount;

    private Double payment;

    private String acPayment;

    private Double saleFee;

    private Double postFee;

    /**
     * 理论运费
     */
    private Double theoryPostFee;

    private String actualPostFee;

    private Integer itemCount;

    private Integer itemKindCount;

    private Long expressTemplateId;

    private String expressTemplateName;

    private String outSid;
    /**
     * 快递模板类型，0表示普通面单，1表示电子面单
     */
    // TODO 还未处理
    private Integer expressTemplateType;

    /**
     * 快递公司的代码
     */
    private String expressCode;

    /**
     * 快递公司的代码
     */
    private String expressName;

    /**
     * 快递的收货地址是否可送达
     */
    private Integer canDelivered;

    private String ydId;

    private String buyerMessage;

    private String sellerMemo;

    private Integer sellerMemoUpdate;

    private Integer sellerFlag;

    private String shopFlag;

    private String shopName;

    private Date payTime;

    private String receiverName;

    private String receiverMobile;

    private String receiverPhone;

    private String receiverState;

    private String receiverCity;

    private String receiverDistrict;

    private String receiverStreet;

    private String receiverDoorplate;

    private String receiverAddress;

    private Integer addressHandled;

    private String receiverZip;

    /**
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     */
    private String buyerNick;

    /**
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     */
    private String openUid;

    private String type;

    private String alipayId;

    private String invoiceName;

    private String invoiceType;

    private String invoiceKind;

    private String buyerTaxNo;

    private Integer needInvoice;

    private String invoiceRemark;

    private Integer invoiceFormat;

    /**
     * 赠品名称
     */
    private String giftName;

    private Date timeoutActionTime;

    private Date endTime;

    private Date refundTime;

    private Date refundConfirmTime;

    private Date finishedTime;

    /**
     * 快递单打印时间
     */
    private Date expressPrintTime;

    /**
     * 发货单打印时间
     */
    private Date deliverPrintTime;

    //查看订单发货状态
    private String codStatus;
    /**
     * 发货时间
     */
    private Date consignTime;
    /**
     * 平台发货时间
     */
    private Date ptConsignTime;

    /**
     * 总金额，不包括优惠金额
     */
    private String totalFee;

    /**
     * 总优惠金额，包括了子订单的优惠和店铺优惠
     */
    private String discountFee;

    /**
     * 总共优惠的金额，包括店铺优惠，子订单的优惠和子订单的卖家优惠
     */
    private String totalDiscountFee;

    /**
     * 支付金额的展现公式
     */
    private String paymentDisplay;

    /**
     * 是否能够在线发货确认
     */
    private Integer canConfirmSend;

    /**
     * 信用卡支付金额
     */
    private String stepPaidFee;

    /**
     * 偏僻地区的关键字，这里在TradeModel中会扫描receiverAddress中是否存在偏僻关键字，如果存在，则设置到这个字段中
     */
    private String backlandKey;

    /**
     * 是否打包
     */
    private Integer isPackage;

    /**
     * 订单平台来源，sys,tb,jd
     */
    private String source;

    /**
     * 是否称重
     */
    private Integer isWeigh;

    /**
     * 包裹重量
     */
    private String weight;

    /**
     * 订单总净重，单位kg
     */
    private String netWeight = "0.0";

    private Double cost;
    /**
     * 订单体积
     */
    private String volume;

    /**
     * 退款信息，只有isRefund为1时，这里将会有数据
     */
    private List<Refund> refunds;

    private Map<String, MessageMemo> messageMemos;

    /**
     * 这个是给详情页面展现用
     */
    private List<MessageMemo> messageMemoList;

    private List<OrderModel> orders = new ArrayList<>();

    private List<TradeTraceModel> traces;

    /**
     * 订单列表序号，从1开始
     */
    private Integer index;

    /**
     * 仓库编号
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 订单系统状态
     */
    private String sysStatus;

    /**
     * 订单系统状态
     */
    private String originSysStatus;

    /**
     * 系统备注
     */
    private String sysMemo;

    /**
     * 是否为作废订单
     */
    private Integer isCancel;

    /**
     * 是否天猫直送订单
     */
    private Integer isTmallDelivery;

    /**
     * 是否是加急订单
     */
    private Integer isUrgent;

    /**
     * 是否预售。1预售 0 非预售。2 预售转非预售
     */
    private Integer isPresell;
    /**
     * 库存状态
     */
    private String stockStatus;

    /**
     * 仓库状态
     */
    private String warehouseStatus;

    /**
     * 审核时间
     */
    private Date auditTime;

    private Shop shop;

    private Long userId;

    /**
     * 关联店铺userId
     */
    private Long associatedUserId;

    private Integer enableStatus;
    /**
     * 系统异常状态，通过stockStatus,isHalt,isRefund,user.active,item.active等几个字段综合判断优先设置权重高的异常状态
     */
    private Set<String> exceptions;

    /**
     * 波次填充信息，不再新增新的关于波次的字段
     */
    private WaveAssembleInfo waveAssembleInfo;

    /**
     * 供销系统异常状态
     */
    private Set<String> destExceptions;

    /**
     * 供销订单系统状态
     */
    private String destSysStatus;

    /**
     * 供销自定义异常
     */
    private String destExceptNames;

    /**
     * 是否归档
     */
    private Boolean ifArchived;

    /**
     * 是否不允许供销拆分
     */
    private Boolean ifNotAllowGxSplit;

    /**
     * 毛利润公式展示
     */
    private String grossProfitDisplay;

    private Boolean ifQimenFxSource;

    public Boolean getIfQimenFxSource() {
        return ifQimenFxSource;
    }

    public TradeModel setIfQimenFxSource(Boolean qimenFxSource) {
        if (BooleanUtils.isNotTrue(qimenFxSource)) {
            return this;
        }
        this.ifQimenFxSource = qimenFxSource;
        return this;
    }

    public Boolean getIfArchived() {
        return ifArchived;
    }

    public void setIfArchived(Boolean ifArchived) {
        this.ifArchived = ifArchived;
    }

    public Boolean getIfNotAllowGxSplit() {
        return ifNotAllowGxSplit;
    }

    public TradeModel setIfNotAllowGxSplit(Boolean ifNotAllowGxSplit) {
        if (BooleanUtils.isNotTrue(ifNotAllowGxSplit)) {
            return this;
        }
        this.ifNotAllowGxSplit = ifNotAllowGxSplit;
        return this;
    }

    public Set<String> getDestExceptions() {
        return destExceptions;
    }

    public void setDestExceptions(Set<String> destExceptions) {
        this.destExceptions = destExceptions;
    }

    public String getDestSysStatus() {
        return destSysStatus;
    }

    public void setDestSysStatus(String destSysStatus) {
        this.destSysStatus = destSysStatus;
    }

    public String getDestExceptNames() {
        return destExceptNames;
    }

    public void setDestExceptNames(String destExceptNames) {
        this.destExceptNames = destExceptNames;
    }

    /**
     * 高亮的字段信息
     */
    private Map<String, Object> highlights;

    /**
     * 店铺类型 tb淘宝 jd京东
     */
    private String shopSource;
    /**
     * 店铺名称
     */
    private String shopSourceName;
    /**
     * 是否刷单 1 是, 0 否
     */
    private Integer scalping;

    /**
     * 列配置的时候需要显示的实付金额
     */
    private String columnPayment;

    private String columnAcPayment;

    private Integer isManulfill;

    private String boxingList;

    private String poNos;

    private String vipPickNo;

    private String vipStorageNo;

    private Integer mergeType;

    private String mergeSid;

    private Integer splitType;

    private String splitSid;

    private Integer isStore;

    /**
     * 拆分装箱
     */
    private List<TradePackSplit> tradePackSplits;

    /**
     * 是否智选物流·
     */
    private Integer isSmart;

    /**
     * 是否家装订单
     */
    private Integer isJz;

    /**
     * 收件国家，跨境订单使用
     */
    private String receiverCountry;

    /**
     * 次来源地 {@link com.raycloud.dmj.domain.user.User#subSource}
     * jd 保存订单来源
     */
    private String subSource;


    /**
     * 订单所属平台
     */
    private String subSourceName;

    /**
     * 税费
     */
    private String taxFee;

    private String tagName;

    private List<TradeTagVo> tagList;

    private String exceptNames;

    /**
     * 订单来源
     */
    private String tradeFrom;

    private Double packmaCost;
    /**
     * 异常信息备注
     */
    private List<String> exceptionRemark;

    /**
     * 自定义异常备注
     */
    private String exceptMemo;

    private Date updTime;
    /**
     * 是否处理买家留言： 0：未处理 1：已处理
     */
    private Integer isHandlerMessageDisplay;

    /**
     * 是否处理买家留言： 0：未处理 1：已处理
     */
    private Integer isHandlerMemoDisplay;


    /**
     * 毛利润
     */
    private String grossProfit;
    /**
     * 毛利率
     */
    private String grossProfitRate;

    //买家姓名
    private String buyerName;
    //商品图片
    private String itemImage;
    //商家编码
    private String outerId;
    //商品规格
    private String propertiesName;

    /**
     * 服务身份标识
     * tmallPromised 代表天猫时效承诺
     */
    private String timingPromise;

    /**
     * 承诺服务类型，会有多个服务值，以英文半角逗号","切割，其中 tmallpromise.arrival.timing 代表到货承诺时效 tmallpromise.consign.timing代表发货承诺时效
     */
    private String promiseService;

    /**
     * 最晚发货时间，日期，格式2019-04-12 16:00:00 时间等同于最晚揽收时间；
     */
    private Date deliveryTime;

    /**
     * 发货承诺时效服务类型
     */
    private String promiseConsignType;

    /**
     * 最晚揽收时间，日期，格式2019-04-12 16:00:00 因发货以有物流揽收记录为准，因此发货和到货订单都会基于该字段进行发货的判责；
     */
    private Date collectTime;

    /**
     * 最晚签收时间，日期，格式2019-04-12 16:00:00 到货订单会依据该字段进行到货的判责；
     */
    private Date signTime;

    /**
     * 相对到达时间，单位为天，0当日达，1次日达，2三日达，3四日达，4五日达
     */
    private Integer esTime;
    /**
     * 到货承诺时效服务类型
     */
    private String promiseArrivalType;

    /**
     * 是否为天猫时效承诺订单 1:是 0:否
     */
    private Integer isTmallPromise;

    /**
     * 天猫时效描述：天猫时效·24h发货，天猫时效·三日达
     */
    private String promiseMsg;

    /**
     * 打印次数
     */
    private Integer printCount;

    /**
     * 订单属于soure or dest ,0表示正常订单，1表示source，2表示dest
     */
    private Integer belongType;


    /**
     * 订单转化类型 0表示正常订单，1表示分销系统
     */
    private Integer convertType;

    /**
     * 订单来源于哪里 分销系统里记为分销商id（companyId）
     */
    private Long sourceId;

    /**
     * 订单归属于哪里 分销系统里记为供销商id（companyId）
     */
    private Long destId;

    /**
     * 订单来源于哪里 分销系统里记为分销商name（不持久化）
     */
    private String sourceName;

    /**
     * 订单归属于哪里 分销系统里记为供销商nname（不持久化）
     */
    private String destName;

    /**
     * 供销商简称
     */
    private String destAliasName;


    /**
     * 对应分销订单的tid （不持久化）
     */
    private String fxTid;

    private String logisticsName;

    private String wrapperDescription;

    private String logisticsCode;

    /**
     * 网点业务类型
     */
    private String expressTemplateMark;

    /**
     * 原始收件人手机
     */
    private String originReceiverMobile;

    /**
     * 原始收件人姓名
     */
    private String originReceiverName;

    /**
     * 原始收件人详细地址
     */
    private String originReceiverAddress;

    /**
     * 用于前端展示的成本
     */
    private String visualCost;

    /**
     * 平台仓名称
     */
    private String platformStoreName;

    /**
     * 上传异常类型
     */
    private String uploadErrorTypeDesc;

    /**
     * 存储订单oaid
     *
     * @see com.raycloud.dmj.domain.trades.Trade#getAddressMd5
     */
    private String addressMd5;
    /**
     * 加运费发某快递公司  SF-顺丰
     */
    private String increaseFreightExpress;

    /**
     * 加运费发某快递公司费用
     * 比如：顺丰加价服务费
     */
    private String increaseFreightFee;

    /**
     * 社区团购  团id
     */
    private String communityGroupId;

    /**
     * 社区团购  团身份
     */
    private String communityGroupRole;

    /**
     * 箱唛打印状态
     */
    private String printBoxLabelStatus;

    /**
     * 业务员
     */
    private String salesmanName;

    /**
     * 图片备注 https://tb.raycloud.com/task/63281b894d3a0a001db56736
     */
    private List<String> tradePictureMemoUris;
    /**
     * （京东、自营）国补非京配订单 妥投规则  json格式
     */
    private String snImgRule;
    /**
     * （京东、自营）国补非京配订单 妥投照片
     */
    private List<String> snImgRuleUrls;
    /**
     * 快递公司名称
     */
    private String logisticsCompanyName;

    /**
     * 买家信息加密
     */
    private Boolean buyerNeedEncode;

    /**
     * 批次打印记录的打印序号
     */
    private Integer seq;
    /**
     * 可验首尾单中间订单
     */
    private List<SimplePackMiddleTrade> normalMiddleTrades;
    /**
     * 异常首尾单中间订单
     */
    private List<SimplePackMiddleTrade> errorMiddleTrades;
    /**
     * 可验快递单数量（包含字母）
     */
    private Integer normalMiddleTradeOutSidCount;
    /**
     * 可验首尾单中间订单总商品数
     */
    private Integer normalMiddleTradeItemCount;
    /**
     * 可验首尾单中间订单总商品种类数
     */
    private Integer normalMiddleTradeItemKindCount;

    /**
     * 快递公司id
     */
    private Long logisticsCompanyId;

    public String getSnImgRule() {
        return snImgRule;
    }

    public void setSnImgRule(String snImgRule) {
        this.snImgRule = snImgRule;
    }

    public List<String> getSnImgRuleUrls() {
        return snImgRuleUrls;
    }

    public void setSnImgRuleUrls(List<String> snImgRuleUrls) {
        this.snImgRuleUrls = snImgRuleUrls;
    }

    public List<String> getTradePictureMemoUris() {
        return tradePictureMemoUris;
    }

    public void setTradePictureMemoUris(List<String> tradePictureMemoUris) {
        this.tradePictureMemoUris = tradePictureMemoUris;
    }

    public Boolean isBuyerNeedEncode() {
        return buyerNeedEncode;
    }

    public TradeModel setBuyerNeedEncode(Boolean buyerNeedEncode) {
        this.buyerNeedEncode = buyerNeedEncode;
        return this;
    }

    public String getIncreaseFreightExpress() {
        return increaseFreightExpress;
    }

    public void setIncreaseFreightExpress(String increaseFreightExpress) {
        this.increaseFreightExpress = increaseFreightExpress;
    }

    public String getIncreaseFreightFee() {
        return increaseFreightFee;
    }

    public void setIncreaseFreightFee(String increaseFreightFee) {
        this.increaseFreightFee = increaseFreightFee;
    }

    public String getCommunityGroupId() {
        return communityGroupId;
    }

    public void setCommunityGroupId(String communityGroupId) {
        this.communityGroupId = communityGroupId;
    }

    public String getCommunityGroupRole() {
        return communityGroupRole;
    }

    public void setCommunityGroupRole(String communityGroupRole) {
        this.communityGroupRole = communityGroupRole;
    }

    /**
     * 售后换货单、补发单 售后id
     */
    private String platformAfterSaleTid;
    /**
     * 售后换货单、补发单 数据来源 trade(订单) wo(售后单)
     */
    private String platformAfterSaleIdSource;
    /**
     * 第三方单号
     */
    private String thirdPlatTid;

    /**
     * 实付金额
     */
    private String payAmount;

    /**
     * 商品成交总额
     */
    private String orderPayment;

    /**
     * 平台优惠
     */
    private String platformDiscountFee;

    public String getPlatformDiscountFee() {
        return platformDiscountFee;
    }

    public void setPlatformDiscountFee(String platformDiscountFee) {
        this.platformDiscountFee = platformDiscountFee;
    }

    public String getAddressMd5() {
        return addressMd5;
    }

    public TradeModel setAddressMd5(String addressMd5) {
        this.addressMd5 = addressMd5;
        return this;
    }

    public String getPlatformStoreName() {
        return platformStoreName;
    }

    public void setPlatformStoreName(String platformStoreName) {
        this.platformStoreName = platformStoreName;
    }

    /**
     * 合单后总优惠金额，不包括包括了子订单的优惠和店铺优惠
     */
    private String mergeDiscountFee;

    public String getMergeDiscountFee() {
        return mergeDiscountFee;
    }

    public void setMergeDiscountFee(String mergeDiscountFee) {
        this.mergeDiscountFee = mergeDiscountFee;
    }

    public String getExpressTemplateMark() {
        return expressTemplateMark;
    }

    public void setExpressTemplateMark(String expressTemplateMark) {
        this.expressTemplateMark = expressTemplateMark;
    }

    private Long guid;

    public Long getGuid() {
        return guid;
    }

    public void setGuid(Long guid) {
        this.guid = guid;
    }

    /**
     * 天猫送货上门 201 订单需要打上“送货上门”标记
     */
    private Set<String> tmallAsdpAdsTypeSet;

    /**
     * 天猫物流升级 logistics_upgrade 订单需要打上“物流升级”标记
     */
    private String tmallAsdpBizType;

    /**
     * 天猫物流升级 tmallAsdpBizType= 203 对应 承诺送达时间
     */
    private Date promiseDeliveryTime;

    private SmtEstimatedRevenueVo smtEstimatedRevenueVo;
    public Date getPromiseDeliveryTime() {
        return promiseDeliveryTime;
    }

    public void setPromiseDeliveryTime(Date promiseDeliveryTime) {
        this.promiseDeliveryTime = promiseDeliveryTime;
    }

    public Set<String> getTmallAsdpAdsTypeSet() {
        if (CollectionUtils.isEmpty(tmallAsdpAdsTypeSet)) {
            tmallAsdpAdsTypeSet = new HashSet<>();
        }
        return tmallAsdpAdsTypeSet;
    }

    public void setTmallAsdpAdsTypeSet(Set<String> tmallAsdpAdsTypeSet) {
        this.tmallAsdpAdsTypeSet = tmallAsdpAdsTypeSet;
    }

    public String getTmallAsdpBizType() {
        return tmallAsdpBizType;
    }

    public void setTmallAsdpBizType(String tmallAsdpBizType) {
        this.tmallAsdpBizType = tmallAsdpBizType;
    }

    public Integer getConvertType() {
        return convertType;
    }

    public TradeModel setConvertType(Integer convertType) {
        this.convertType = convertType;
        return this;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public TradeModel setSourceId(Long sourceId) {
        this.sourceId = sourceId;
        return this;
    }

    public Long getDestId() {
        return destId;
    }

    public TradeModel setDestId(Long destId) {
        this.destId = destId;
        return this;

    }

    public String getSourceName() {
        return sourceName;
    }

    public TradeModel setSourceName(String sourceName) {
        this.sourceName = sourceName;
        return this;

    }

    public String getDestName() {
        return destName;
    }

    public TradeModel setDestName(String destName) {
        this.destName = destName;
        return this;
    }

    public String getDestAliasName() {
        return destAliasName;
    }

    public TradeModel setDestAliasName(String destAliasName) {
        this.destAliasName = destAliasName;
        return this;
    }

    public Integer getBelongType() {
        return belongType;
    }

    public TradeModel setBelongType(Integer belongType) {
        this.belongType = belongType;
        return this;
    }


    /**
     * 物流记录条数
     */
    private Integer logisticsTraceCount;

    /**
     * 最近物流记录
     */
    private String lastLogisticsTrace;

    private Date lastLogisticsTime;

    /**
     * 是否在系统发货 1 是，0 否
     */
    private Integer sysConsigned;

    private Integer isUpload;

    private String sortString;

    /**
     * 库区类型
     *
     * @see com.raycloud.dmj.domain.wms.enums.StockRegionTypeEnum
     */
    private Integer stockRegionType;

    /**
     * 订单实收金额
     */
    private Double tradePurchaseAmount;
    /**
     * 订单支付信息
     */
    private List<TradePayVO> tradePays;
    /**
     * 平台支付金额
     */
    private Double platformPaymentAmount;
    /**
     * 手工支付金额
     */
    private Double manualPaymentAmount;

    /**
     * 订单商品已验货数量
     */
    private Integer currNum;

    public Integer getCurrNum() {
        return currNum;
    }

    public void setCurrNum(Integer currNum) {
        this.currNum = currNum;
    }

    /**
     * shopee:物流渠道
     */
    private String shippingCarrier;

    private Long combineParcelId;

    /**
     * 代打店铺名称
     */
    private String mallMaskName;

    /**
     * 代打店铺id
     */
    private String mallMaskId;

    private String cooperationNo;

    private String sellSite;

    /**
     * Btas订单保价价格
     */
    private Double insuranceCost;

    /**
     * Btas订单保价类型
     * 0：未开启，1：开启未匹配店铺，2：开启且成功匹配店铺
     */
    private Integer insuranceCostType;

    /**
     * 经分销id 团长id
     */
    private String distributorId;

    /**
     * 经分销昵称 团长昵称
     */
    private String distributorName;

    /**
     * 交易关闭的子订单
     */
    private List<OrderModel> closeOrders;

    /**
     * 翱象标示
     */
    private Boolean aox = false;

    private Integer waybillCount;

    public Integer getWaybillCount() {
        return waybillCount;
    }

    public TradeModel setWaybillCount(Integer waybillCount) {
        this.waybillCount = waybillCount;
        return this;
    }

    public String getSelfBuiltDepositAmount() {
        return selfBuiltDepositAmount;
    }

    public void setSelfBuiltDepositAmount(String selfBuiltDepositAmount) {
        this.selfBuiltDepositAmount = selfBuiltDepositAmount;
    }

    public String getSelfBuiltPaymentReceivable() {
        return selfBuiltPaymentReceivable;
    }

    public void setSelfBuiltPaymentReceivable(String selfBuiltPaymentReceivable) {
        this.selfBuiltPaymentReceivable = selfBuiltPaymentReceivable;
    }

    /**
     * 定金
     */
    String selfBuiltDepositAmount;
    /**
     * 代收金额
     */
    String selfBuiltPaymentReceivable;


    public List<OrderModel> getCloseOrders() {
        return closeOrders;
    }

    public void setCloseOrders(List<OrderModel> closeOrders) {
        this.closeOrders = closeOrders;
    }


    /**
     * o2o订单
     */
    private TradeO2o tradeO2o;

    /**
     * 业务操作标志--不落库。
     *
     */
    @JSONField(serialize = false)
    private Set<OpVEnum> opVEnumSet;

    public TradeO2o getTradeO2o() {
        return tradeO2o;
    }

    public void setTradeO2o(TradeO2o tradeO2o) {
        this.tradeO2o = tradeO2o;
    }


    public void addOpV(OpVEnum opVEnum) {
        if(opVEnum ==null){
            return;
        }
        if(CollectionUtils.isEmpty(opVEnumSet)){
            opVEnumSet = Sets.newHashSetWithExpectedSize(4);
        }
        opVEnumSet.add(opVEnum);
    }

    public void removeOpV(OpVEnum opVEnum) {
        if(CollectionUtils.isEmpty(opVEnumSet) || opVEnum ==null){
            return;
        }
        opVEnumSet.remove(opVEnum);
    }
    /**
     *
     * @return
     */
    public boolean hasOpV(OpVEnum opVEnum) {
        return CollectionUtils.isNotEmpty(opVEnumSet) && opVEnumSet.contains(opVEnum);
    }

    /**
     * 订单发票信息
     */
    private TradeInvoice tradeInvoice;

    public TradeInvoice getTradeInvoice() {
        return tradeInvoice;
    }

    public void setTradeInvoice(TradeInvoice tradeInvoice) {
        this.tradeInvoice = tradeInvoice;
    }

    public Integer getIsHandlerMessageDisplay() {
        return isHandlerMessageDisplay;
    }

    public void setIsHandlerMessageDisplay(Integer isHandlerMessageDisplay) {
        this.isHandlerMessageDisplay = isHandlerMessageDisplay;
    }

    public Integer getIsHandlerMemoDisplay() {
        return isHandlerMemoDisplay;
    }

    public void setIsHandlerMemoDisplay(Integer isHandlerMemoDisplay) {
        this.isHandlerMemoDisplay = isHandlerMemoDisplay;
    }

    public Integer getNeedInvoice() {
        return needInvoice;
    }

    public TradeModel setNeedInvoice(Integer needInvoice) {
        this.needInvoice = needInvoice;
        return this;
    }

    public String getInvoiceRemark() {
        return invoiceRemark;
    }

    public TradeModel setInvoiceRemark(String invoiceRemark) {
        this.invoiceRemark = invoiceRemark;
        return this;
    }

    public Integer getInvoiceFormat() {
        return invoiceFormat;
    }

    public TradeModel setInvoiceFormat(Integer invoiceFormat) {
        this.invoiceFormat = invoiceFormat;
        return this;
    }

    public String getColumnPayment() {
        return columnPayment;
    }

    public void setColumnPayment(String columnPayment) {
        this.columnPayment = columnPayment;
    }

    public String getColumnAcPayment() {
        return columnAcPayment;
    }

    public void setColumnAcPayment(String columnAcPayment) {
        this.columnAcPayment = columnAcPayment;
    }

    public String[] getTids() {
        return tids;
    }

    public void setTids(String... tids) {
        this.tids = tids;
    }

    public String getTid() {
        return tid;
    }

    public TradeModel setTid(String tid) {
        this.tid = tid;
        return this;
    }

    public String getCodStatus() {
        return codStatus;
    }

    public TradeModel setCodStatus(String codStatus) {
        this.codStatus = codStatus;
        return this;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public TradeModel setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public TradeModel setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getChStatus() {
        return chStatus;
    }

    public TradeModel setChStatus(String chStatus) {
        this.chStatus = chStatus;
        return this;
    }

    public Date getTimeoutActionTime() {
        return timeoutActionTime;
    }

    public TradeModel setTimeoutActionTime(Date timeoutActionTime) {
        this.timeoutActionTime = timeoutActionTime;
        return this;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    /**
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     *
     * @param buyerNick
     * @return
     */
    public TradeModel setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
        return this;
    }

    public String getOpenUid() {
        return openUid;
    }

    /**
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     * 淘系buyerNick与openUid页面展示互换  注意不要被覆盖掉
     *
     * @param openUid
     * @return
     */
    public TradeModel setOpenUid(String openUid) {
        this.openUid = openUid;
        return this;
    }

    public Integer getCheckManualMergeCount() {
        return checkManualMergeCount;
    }

    public TradeModel setCheckManualMergeCount(Integer checkManualMergeCount) {
        this.checkManualMergeCount = checkManualMergeCount;
        return this;
    }

    public String getType() {
        return type;
    }

    public TradeModel setType(String type) {
        this.type = type;
        return this;
    }

    public String getAlipayId() {
        return alipayId;
    }

    public TradeModel setAlipayId(String alipayId) {
        this.alipayId = alipayId;
        return this;
    }

    public String getInvoiceName() {
        return invoiceName;
    }

    public TradeModel setInvoiceName(String invoiceName) {
        this.invoiceName = invoiceName;
        return this;
    }

    public Date getCreated() {
        return created;
    }

    public TradeModel setCreated(Date created) {
        this.created = created;
        return this;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public TradeModel setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
        return this;
    }

    public String getInvoiceKind() {
        return invoiceKind;
    }

    public TradeModel setInvoiceKind(String invoiceKind) {
        this.invoiceKind = invoiceKind;
        return this;
    }

    public String getBuyerTaxNo() {
        return buyerTaxNo;
    }

    public TradeModel setBuyerTaxNo(String buyerTaxNo) {
        this.buyerTaxNo = buyerTaxNo;
        return this;
    }

    public String getGiftName() {
        return giftName;
    }

    public TradeModel setGiftName(String giftName) {
        this.giftName = giftName;
        return this;
    }

    public Integer getIsRefund() {
        return isRefund;
    }

    public TradeModel setIsRefund(Integer isRefund) {
        this.isRefund = isRefund;
        return this;
    }

    public Integer getHasRefunded() {
        return hasRefunded;
    }

    public void setHasRefunded(Integer hasRefunded) {
        this.hasRefunded = hasRefunded;
    }

    public Integer getIsExcep() {
        return isExcep;
    }

    public TradeModel setIsExcep(Integer isExcep) {
        this.isExcep = isExcep;
        return this;
    }

    public Integer getIsStockOut() {
        return isStockOut;
    }

    public TradeModel setIsStockOut(Integer isStockOut) {
        this.isStockOut = isStockOut;
        return this;
    }

    public Integer getMergeSplitType() {
        return mergeSplitType;
    }

    public TradeModel setMergeSplitType(Integer mergeSplitType) {
        this.mergeSplitType = mergeSplitType;
        return this;
    }

    public Double getPayment() {
        return payment;
    }

    public TradeModel setPayment(Double payment) {
        this.payment = payment;
        return this;
    }


    public String getAcPayment() {
        return acPayment;
    }

    public TradeModel setAcPayment(String acPayment) {
        this.acPayment = acPayment;
        return this;
    }

    public Double getSaleFee() {
        return saleFee;
    }

    public TradeModel setSaleFee(Double saleFee) {
        this.saleFee = saleFee;
        return this;
    }

    public Double getPostFee() {
        return postFee;
    }

    public TradeModel setPostFee(Double postFee) {
        this.postFee = postFee;
        return this;
    }

    public Double getTheoryPostFee() {
        return theoryPostFee;
    }

    public TradeModel setTheoryPostFee(Double theoryPostFee) {
        this.theoryPostFee = theoryPostFee;
        return this;
    }

    public String getActualPostFee() {
        return actualPostFee;
    }

    public TradeModel setActualPostFee(String actualPostFee) {
        this.actualPostFee = actualPostFee;
        return this;
    }

    public Integer getItemCount() {
        return itemCount;
    }

    public TradeModel setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
        return this;
    }

    public Integer getItemKindCount() {
        return itemKindCount;
    }

    public TradeModel setItemKindCount(Integer itemKindCount) {
        this.itemKindCount = itemKindCount;
        return this;
    }


    public Long getExpressTemplateId() {
        return expressTemplateId;
    }

    public TradeModel setExpressTemplateId(Long expressTemplateId) {
        this.expressTemplateId = expressTemplateId;
        return this;
    }

    public String getExpressTemplateName() {
        return expressTemplateName;
    }

    public TradeModel setExpressTemplateName(String expressTemplateName) {
        this.expressTemplateName = expressTemplateName;
        return this;
    }

    public String getYdId() {
        return ydId;
    }

    public TradeModel setYdId(String ydId) {
        this.ydId = ydId;
        return this;
    }

    public String getBacklandKey() {
        return backlandKey;
    }

    public TradeModel setBacklandKey(String backlandKey) {
        this.backlandKey = backlandKey;
        return this;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public TradeModel setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
        return this;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public TradeModel setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
        return this;
    }

    public Integer getSellerMemoUpdate() {
        return sellerMemoUpdate;
    }

    public TradeModel setSellerMemoUpdate(Integer sellerMemoUpdate) {
        this.sellerMemoUpdate = sellerMemoUpdate;
        return this;
    }

    public Integer getSellerFlag() {
        return sellerFlag;
    }

    public TradeModel setSellerFlag(Integer sellerFlag) {
        this.sellerFlag = sellerFlag;
        return this;
    }

    public String getShopFlag() {
        return shopFlag;
    }

    public TradeModel setShopFlag(String shopFlag) {
        this.shopFlag = shopFlag;
        return this;
    }

    public Date getPayTime() {
        return payTime;
    }

    public TradeModel setPayTime(Date payTime) {
        this.payTime = payTime;
        return this;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public TradeModel setReceiverName(String receiverName) {
        this.receiverName = receiverName;
        return this;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public TradeModel setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
        return this;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public TradeModel setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
        return this;
    }

    public String getReceiverState() {
        return receiverState;
    }

    public TradeModel setReceiverState(String receiverState) {
        this.receiverState = receiverState;
        return this;
    }

    public String getReceiverCity() {
        return receiverCity;
    }

    public TradeModel setReceiverCity(String receiverCity) {
        this.receiverCity = receiverCity;
        return this;
    }

    public String getReceiverDistrict() {
        return receiverDistrict;
    }

    public TradeModel setReceiverDistrict(String receiverDistrict) {
        this.receiverDistrict = receiverDistrict;
        return this;
    }

    public String getReceiverStreet() {
        return receiverStreet;
    }

    public TradeModel setReceiverStreet(String receiverStreet) {
        this.receiverStreet = receiverStreet;
        return this;
    }

    public String getReceiverDoorplate() {
        return receiverDoorplate;
    }

    public TradeModel setReceiverDoorplate(String receiverDoorplate) {
        this.receiverDoorplate = receiverDoorplate;
        return this;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public TradeModel setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
        return this;
    }

    /**
     * 是否平台分销
     */
    private Boolean ifPlatformFx;

    public Boolean getIfPlatformFx() {
        return ifPlatformFx;
    }

    public TradeModel setIfPlatformFx(Boolean ifPlatformFx) {
        this.ifPlatformFx = ifPlatformFx;
        return this;
    }

    /**
     * 是否快麦通代发
     */
    private Boolean ifKmtDf;

    /**
     *
     * 合单类型（快手）
     *
     */
    private  Integer mergeDeliveryType;

    public Integer getMergeDeliveryType() {
        return mergeDeliveryType;
    }

    public void setMergeDeliveryType(Integer mergeDeliveryType) {
        this.mergeDeliveryType = mergeDeliveryType;
    }

    private Long v;

    public Long getV() {
        return v;
    }

    public TradeModel setV(Long v) {
        this.v = v;
        return this;
    }

    public String getOriginSysStatus() {
        return originSysStatus;
    }

    public TradeModel setOriginSysStatus(String originSysStatus) {
        this.originSysStatus = originSysStatus;
        return this;
    }

    public SmtEstimatedRevenueVo getSmtEstimatedRevenueVo() {
        return smtEstimatedRevenueVo;
    }

    public void setSmtEstimatedRevenueVo(SmtEstimatedRevenueVo smtEstimatedRevenueVo) {
        this.smtEstimatedRevenueVo = smtEstimatedRevenueVo;
    }

    public Boolean getIfKmtDf() {
        return ifKmtDf;
    }

    public TradeModel setIfKmtDf(Boolean ifKmtDf) {
        this.ifKmtDf = ifKmtDf;
        return this;
    }

    public Integer getAddressHandled() {
        return addressHandled;
    }

    public TradeModel setAddressHandled(Integer addressHandled) {
        this.addressHandled = addressHandled;
        return this;
    }

    public Date getConsignTime() {
        return consignTime;
    }

    public TradeModel setConsignTime(Date consignTime) {
        this.consignTime = consignTime;
        return this;
    }

    public Date getPtConsignTime() {
        return ptConsignTime;
    }

    public TradeModel setPtConsignTime(Date ptConsignTime) {
        this.ptConsignTime = ptConsignTime;
        return this;
    }

    public String getReceiverZip() {
        return receiverZip;
    }

    public TradeModel setReceiverZip(String receiverZip) {
        this.receiverZip = receiverZip;
        return this;
    }

    public List<OrderModel> getOrders() {
        return orders;
    }

    public TradeModel setOrders(List<OrderModel> orders) {
        this.orders = orders;
        return this;
    }

    public List<TradeTraceModel> getTraces() {
        return traces;
    }

    public TradeModel setTraces(List<TradeTraceModel> traces) {
        this.traces = traces;
        return this;
    }

    public Integer getIsHalt() {
        return isHalt;
    }

    public TradeModel setIsHalt(Integer isHalt) {
        this.isHalt = isHalt;
        return this;
    }

    public Integer getExpressStatus() {
        return expressStatus;
    }

    public TradeModel setExpressStatus(Integer expressStatus) {
        this.expressStatus = expressStatus;
        return this;
    }

    public Integer getDeliverStatus() {
        return deliverStatus;
    }

    public TradeModel setDeliverStatus(Integer deliverStatus) {
        this.deliverStatus = deliverStatus;
        return this;
    }

    public Integer getAssemblyStatus() {
        return assemblyStatus;
    }

    public TradeModel setAssemblyStatus(Integer assemblyStatus) {
        this.assemblyStatus = assemblyStatus;
        return this;
    }

    public Integer getExpressTemplateType() {
        return expressTemplateType;
    }

    public TradeModel setExpressTemplateType(Integer expressTemplateType) {
        this.expressTemplateType = expressTemplateType;
        return this;
    }

    public Map<String, MessageMemo> getMessageMemos() {
        return messageMemos;
    }

    public TradeModel setMessageMemos(Map<String, MessageMemo> messageMemos) {
        this.messageMemos = messageMemos;
        return this;
    }

    public String getOutSid() {
        return outSid;
    }

    public TradeModel setOutSid(String outSid) {
        this.outSid = outSid;
        return this;
    }

    public Integer getCanDelivered() {
        return canDelivered;
    }

    public TradeModel setCanDelivered(Integer canDelivered) {
        this.canDelivered = canDelivered;
        return this;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public TradeModel setExpressCode(String expressCode) {
        this.expressCode = expressCode;
        return this;
    }

    public String getExpressName() {
        return expressName;
    }

    public void setExpressName(String expressName) {
        this.expressName = expressName;
    }

    public Integer getCanConfirmSend() {
        return canConfirmSend;
    }

    public TradeModel setCanConfirmSend(Integer canConfirmSend) {
        this.canConfirmSend = canConfirmSend;
        return this;
    }

    public String getTotalFee() {
        return totalFee;
    }

    public TradeModel setTotalFee(String totalFee) {
        this.totalFee = totalFee;
        return this;
    }

    public String getDiscountFee() {
        return discountFee;
    }

    public TradeModel setDiscountFee(String discountFee) {
        this.discountFee = discountFee;
        return this;
    }

    public String getPaymentDisplay() {
        return paymentDisplay;
    }

    public TradeModel setPaymentDisplay(String paymentDisplay) {
        this.paymentDisplay = paymentDisplay;
        return this;
    }

    public String getTotalDiscountFee() {
        return totalDiscountFee;
    }

    public TradeModel setTotalDiscountFee(String totalDiscountFee) {
        this.totalDiscountFee = totalDiscountFee;
        return this;
    }

    public String getStepPaidFee() {
        return stepPaidFee;
    }

    public TradeModel setStepPaidFee(String stepPaidFee) {
        this.stepPaidFee = stepPaidFee;
        return this;
    }

    public List<MessageMemo> getMessageMemoList() {
        return messageMemoList;
    }

    public TradeModel setMessageMemoList(List<MessageMemo> messageMemoList) {
        this.messageMemoList = messageMemoList;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public TradeModel setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public List<Refund> getRefunds() {
        return refunds;
    }

    public TradeModel setRefunds(List<Refund> refunds) {
        this.refunds = refunds;
        return this;
    }

    public Integer getIndex() {
        return index;
    }

    public TradeModel setIndex(Integer index) {
        this.index = index;
        return this;
    }

    public String getShopName() {
        return shopName;
    }

    public TradeModel setShopName(String shopName) {
        this.shopName = shopName;
        return this;
    }

    public String getNetWeight() {
        return netWeight;
    }

    public TradeModel setNetWeight(String netWeight) {
        this.netWeight = netWeight;
        return this;
    }

    public Double getCost() {
        return cost;
    }

    public void setCost(Double cost) {
        this.cost = cost;
    }

    public String getVolume() {
        return volume;
    }

    public TradeModel setVolume(String volume) {
        this.volume = volume;
        return this;
    }

    public String getShopSource() {
        return shopSource;
    }

    public TradeModel setShopSource(String shopSource) {
        this.shopSource = shopSource;
        return this;
    }

    public Integer getScalping() {
        return scalping;
    }

    public TradeModel setScalping(Integer scalping) {
        this.scalping = scalping;
        return this;
    }

    public Integer getIsSmart() {
        return isSmart;
    }

    public TradeModel setIsSmart(Integer isSmart) {
        this.isSmart = isSmart;
        return this;
    }

    public Integer getIsJz() {
        return isJz;
    }

    public TradeModel setIsJz(Integer isJz) {
        this.isJz = isJz;
        return this;
    }

    public String getTagName() {
        return tagName;
    }

    public TradeModel setTagName(String tagName) {
        this.tagName = tagName;
        return this;
    }


    public String getExceptNames() {
        return exceptNames;
    }

    public void setExceptNames(String exceptNames) {
        this.exceptNames = exceptNames;
    }

    public String getTradeFrom() {
        return tradeFrom;
    }

    public TradeModel setTradeFrom(String tradeFrom) {
        this.tradeFrom = tradeFrom;
        return this;
    }

    public List<String> getExceptionRemark() {
        return exceptionRemark;
    }

    public void setExceptionRemark(List<String> exceptionRemark) {
        this.exceptionRemark = exceptionRemark;
    }

    public String getExceptMemo() {
        return exceptMemo;
    }

    public TradeModel setExceptMemo(String exceptMemo) {
        this.exceptMemo = exceptMemo;
        return this;
    }

    public Date getUpdTime() {
        return updTime;
    }

    public TradeModel setUpdTime(Date updTime) {
        this.updTime = updTime;
        return this;
    }

    public String getGrossProfit() {
        return grossProfit;
    }

    public TradeModel setGrossProfit(String grossProfit) {
        this.grossProfit = grossProfit;
        return this;
    }

    public String getGrossProfitRate() {
        return grossProfitRate;
    }

    public TradeModel setGrossProfitRate(String grossProfitRate) {
        this.grossProfitRate = grossProfitRate;
        return this;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public TradeModel setBuyerName(String buyerName) {
        this.buyerName = buyerName;
        return this;
    }

    public String getItemImage() {
        return itemImage;
    }

    public TradeModel setItemImage(String itemImage) {
        this.itemImage = itemImage;
        return this;
    }

    public String getOuterId() {
        return outerId;
    }

    public TradeModel setOuterId(String outerId) {
        this.outerId = outerId;
        return this;
    }

    public String getPropertiesName() {
        return propertiesName;
    }

    public void setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
    }

    public String getVisualCost() {
        return visualCost;
    }

    public void setVisualCost(String visualCost) {
        this.visualCost = visualCost;
    }

    public String getUploadErrorTypeDesc() {
        return uploadErrorTypeDesc;
    }

    public void setUploadErrorTypeDesc(String uploadErrorTypeDesc) {
        this.uploadErrorTypeDesc = uploadErrorTypeDesc;
    }

    public Double getInsuranceCost() {
        return insuranceCost;
    }

    public void setInsuranceCost(Double insuranceCost) {
        this.insuranceCost = insuranceCost;
    }

    public Integer getInsuranceCostType() {
        return insuranceCostType;
    }

    public void setInsuranceCostType(Integer insuranceCostType) {
        this.insuranceCostType = insuranceCostType;
    }

    public String getSalesmanName() {
        return salesmanName;
    }

    public void setSalesmanName(String salesmanName) {
        this.salesmanName = salesmanName;
    }

    /**
     * 将domain中的TbTrade和Shop转换为TradeModel，这个模型主要是查询订单列表时使用
     *
     * @param tbTrade
     * @param shop
     * @param expressCompany
     * @param highlight      是否要高亮，如果需要高亮，则返回的TradeModel，将会设置highlight属性，这个需要依赖searchKey参数
     * @param searchKey      搜索关键词
     * @return
     */
    public static TradeModel toBaseTradeModel(Staff staff, Trade tbTrade, Shop shop, ExpressCompany expressCompany, boolean highlight, String searchKey, TradeConfig tradeConfig) {
        return toBaseTradeModel(staff, tbTrade, shop, expressCompany, highlight, searchKey, tradeConfig, 0);
    }

    public static TradeModel toDeletedTradeModel(Trade tbTrade) {
        TradeModel tradeModel = new TradeModel();
        tradeModel.setSid(tbTrade.getSid().toString());
        tradeModel.setOutSid(tbTrade.getOutSid());
        Set<String> exceptionSet = new HashSet<>();
        // 对应前端提示信息：订单已手工删除或订单过期已自动删除，可请前往【交易-订单-订单查询-归档订单】查看3个月前的已自动删除订单
        exceptionSet.add(EX_AUTO_DELETE);
        tradeModel.setExceptions(exceptionSet);
        tradeModel.setSeq(tbTrade.getSeq());
        return tradeModel;
    }

    public static TradeModel toBaseTradeModel(Staff staff, Trade tbTrade, Shop shop, ExpressCompany expressCompany, boolean highlight, String searchKey, TradeConfig tradeConfig, Integer useCompress) {
        User user = staff.getUserByUserId(tbTrade.getUserId());

        TradeModel model = createTradeModel(useCompress);
        model.setOriginReceiverAddress(tbTrade.getReceiverAddress());
        model.setOriginReceiverMobile(tbTrade.getReceiverMobile());
        model.setOriginReceiverName(tbTrade.getReceiverName());
        boolean hasPowerPayment = StringUtils.contains(staff.getPowerDataPrivilegeSettings(), "1000302");
        if (null == shop) {
            shop = new Shop();
        }
        model.setOriginReceiverAddress(tbTrade.getReceiverAddress());
        model.setOriginReceiverMobile(tbTrade.getReceiverMobile());
        model.setOriginReceiverName(tbTrade.getReceiverName());

        TradeSearchBlurService.getInstance().blurData(staff,tbTrade, tradeConfig);

        model.setSid(tbTrade.getSid().toString()).setShortId(tbTrade.getShortId() != null ? tbTrade.getShortId().toString() : null).setMergeType(tbTrade.getMergeType()).setMergeSid(object2String(tbTrade.getMergeSid())).setSplitType(tbTrade.getSplitType()).setSplitSid(object2String(tbTrade.getSplitSid()))
                .setUserId(tbTrade.getUserId()).setScalping(lvn(tbTrade.getScalping())).setIsExcep(lvn(tbTrade.getIsExcep()))
                .setConsignTime(tbTrade.getConsignTime()).setPtConsignTime(tbTrade.getPtConsignTime()).setReceiverName(tbTrade.getReceiverName())
                .setReceiverMobile(lvn(tbTrade.getReceiverMobile())).setReceiverPhone(lvn(tbTrade.getReceiverPhone()))
                .setReceiverState(tbTrade.getReceiverState()).setReceiverCity(tbTrade.getReceiverCity())
                .setReceiverDistrict(tbTrade.getReceiverDistrict()).setReceiverStreet(tbTrade.getReceiverStreet()).setReceiverAddress(tbTrade.getReceiverAddress()).setAddressHandled(tbTrade.isAddressHandled() ? 1 : 0).setReceiverDoorplate(tbTrade.getReceiverDoorplate())
                .setReceiverZip(lvn(tbTrade.getReceiverZip())).setShopSource(shop.getSource()).setShopSourceName(shop.getSourceName())
                .setIfKmtDf(tbTrade.getIfKmtDf()&&TradeUtils.isFxOrMixTrade(tbTrade)).setIfQimenFxSource(TradeUtils.isQimenFxSource(tbTrade)).setV(tbTrade.getV()).setIfPlatformFx(TradeUtils.isPlatformFx(tbTrade))
                .setIfNotAllowGxSplit(TradeUtils.ifContainV(tbTrade, TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX))
                .setShopFlag(shop.getFlag()).setExpressStatus(lvn(getPrintStatus(tbTrade.getExpressPrintTime())))
                .setDeliverStatus(lvn(getPrintStatus(tbTrade.getDeliverPrintTime()))).setAssemblyStatus(lvn(getPrintStatus(tbTrade.getAssemblyPrintTime())))
                .setIsHalt(lvn(TradeExceptUtils.isContainExcept(staff, tbTrade, ExceptEnum.HALT) ? 1 : 0)).setCanDelivered(lvn(tbTrade.getCanDelivered()))
                .setCreated(tbTrade.getCreated()).setCheckManualMergeCount(lvn(tbTrade.getCheckManualMergeCount()))
                .setOutSid(lvn(tbTrade.getOutSid())).setExpressPrintTime(tbTrade.getExpressPrintTime()).setDeliverPrintTime(tbTrade.getDeliverPrintTime())
                .setCanConfirmSend(lvn(tbTrade.getCanConfirmSend() == null ? 0 : tbTrade.getCanConfirmSend()))
                .setWarehouseId(tbTrade.getWarehouseId()).setWarehouseName(tbTrade.getWarehouseName())
                .setOriginSysStatus(tbTrade.getSysStatus()).setSysStatus(TradeStatusUtils.convertSysStatus(tbTrade, tradeConfig)).setSysMemo(tbTrade.getSysMemo()).setExceptMemo(tbTrade.getExceptMemo())
                .setIsCancel(lvn(tbTrade.getIsCancel())).setStockStatus(tbTrade.getStockStatus())
                .setIsUrgent(lvn(tbTrade.getIsUrgent())).setDiscountFee(tbTrade.getDiscountFee()).setTotalFee(tbTrade.getTotalFee())
                .setPostFee(TypeConvertUtils.toMoney(tbTrade.getPostFee())).setActualPostFee(tbTrade.getActualPostFee())
                .setPayment(TypeConvertUtils.toMoney(tbTrade.getPayment())).setBuyerNick(lvn(tbTrade.getBuyerNick()))
                .setOpenUid(tbTrade.getOpenUid())
                .setAcPayment(tbTrade.getAcPayment())
                .setGrossProfit(hasPowerPayment ? (MathUtils.toScaleString(staff.getCompanyId(),tbTrade.getGrossProfit())) : "***")
                .setGrossProfitRate(hasPowerPayment ? tbTrade.getGrossProfitRate() == null ? "" : (MathUtils.toScaleString(staff.getCompanyId(), tbTrade.getGrossProfitRate())) : "***")
                .setSource(tbTrade.getSource()).setIsPackage(lvn(tbTrade.getIsPackage()))
                .setIsWeigh(lvn(tbTrade.getIsWeigh())).setWeight(MathUtils.toString(tbTrade.getWeight(),4)).setIsPresell(lvn(tbTrade.getIsPresell()))
                .setEnableStatus(tbTrade.getEnableStatus())
                .setBoxingList(tbTrade.getBoxingList())
                .setPoNos(lvn(handlePoNos(staff, tbTrade)))
                .setVipPickNo(lvn(tbTrade.getVipPickNo()))
                .setVipStorageNo(lvn(tbTrade.getVipStorageNo()))
                .setIsSmart(lvn(tbTrade.getIsSmart()))
                .setIsJz(lvn(tbTrade.getIsJz()))
                .setReceiverCountry(lvn(tbTrade.getReceiverCountry()))
                .setSubSource(lvn(tbTrade.getSubSource()))
                .setTaxFee(tbTrade.getTaxFee())
                .setWaveId(lvn(tbTrade.getWaveId()))
                .setWaveShortId(lvn(tbTrade.getWaveShortId()))
                .setSaleFee(TypeConvertUtils.toMoney(tbTrade.getSaleFee()))
                .setPackmaCost(tbTrade.getPackmaCost())
                .setIsTmallDelivery(lvn(tbTrade.getIsTmallDelivery()))
                .setPrintCount(tbTrade.getPrintCount())
                .setBelongType(tbTrade.getBelongType())
                .setConvertType(tbTrade.getConvertType())
                .setSourceName(tbTrade.getSourceName())
                .setSubSourceName(tbTrade.getSubSourceName())
                .setDestName(tbTrade.getDestName()).setDestAliasName(tbTrade.getDestAliasName())
                .setSourceId(tbTrade.getSourceId())
                .setDestId(tbTrade.getDestId())
                .setTheoryPostFee(tbTrade.getTheoryPostFee())
                .setSysConsigned(tbTrade.getSysConsigned())
                .setIsUpload(tbTrade.getIsUpload())
                .setUnifiedStatus(tbTrade.getUnifiedStatus())
                .setStockRegionType(tbTrade.getStockRegionType())
                .setSortString(tbTrade.getSysOuterId())
                .setAddressMd5(tbTrade.getAddressMd5())
                .setPayAmount(tbTrade.getPayAmount() == null ? null : String.valueOf(tbTrade.getPayAmount()))
                .setBuyerNeedEncode(TradeUtils.buyerNeedEncode(tbTrade))
                .setUpdTime(tbTrade.getUpdTime()).setTradeFrom(Strings.join(",", Strings.getAsStringSet(tbTrade.getTradeFrom(), ",", true)));

        if (MapUtils.isNotEmpty(tbTrade.getTradeTypeMap())) {
            model.setTypeIds(tbTrade.getTradeTypeMap().keySet().stream().filter(e -> StringUtils.isNotBlank(e)).map(Long::valueOf).collect(Collectors.toList()));
        }

        model.setTid(String.valueOf(tbTrade.getTid()));
        model.setTids(OrderUtils.toTids(TradeUtils.getOrders4Trade(tbTrade)));
        //上传异常类型
        model.setUploadErrorTypeDesc(tbTrade.getUploadErrorTypeDesc());
        //发票
        model.setInvoiceName(lvn(tbTrade.getInvoiceName())).setInvoiceType(tbTrade.getInvoiceType()).setBuyerTaxNo(lvn(tbTrade
                .getBuyerTaxNo())).setTimeoutActionTime(tbTrade.getTimeoutActionTime()).setInvoiceKind(tbTrade
                .getInvoiceKind()).setInvoiceRemark(tbTrade.getInvoiceRemark()).setInvoiceFormat(tbTrade
                .getInvoiceFormat()).setNeedInvoice(tbTrade.getNeedInvoice());

        //天猫时效
        model.setIsTmallPromise(TradePromiseUtils.isTmallPromise(tbTrade) ? 1 : 0)
                .setTimingPromise(tbTrade.getTimingPromise())
                .setPromiseService(tbTrade.getPromiseService())
                .setDeliveryTime(tbTrade.getDeliveryTime())
                .setPromiseConsignType(TradePromiseUtils.isPromiseConsign(tbTrade) ? TradePromiseUtils.getPromiseConsignType(tbTrade.getDeliveryTime(), tbTrade.getPayTime()) : "")
                .setCollectTime(tbTrade.getCollectTime())
                .setEsTime(tbTrade.getEsTime())
                .setSignTime(tbTrade.getSignTime())
                .setPromiseArrivalType(TradePromiseUtils.isPromiseArrival(tbTrade) ? TradePromiseUtils.getPromiseArrivalType(tbTrade.getEsTime()) : "")
                .setPromiseMsg(TradePromiseUtils.getPromiseMsg(tbTrade.getPromiseService(), tbTrade.getDeliveryTime(), tbTrade.getPayTime(), tbTrade.getEsTime()));
        //商品成交总额
        model.setOrderPayment(MathUtils.toString(PaymentUtils.totalPayment(tbTrade, true)));
        model.setVolume(MathUtils.toString(tbTrade.getVolume()));
        if (shop.getSimpleTitleConfig() != null && shop.getSimpleTitleConfig() == 1) {
            model.setShopName(StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : StringUtils.trimToEmpty(shop.getTitle()));
        } else {
            model.setShopName(shop.getTitle());
        }
        //奇门货主店铺展示平台名称-平台店铺名称
        if (CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(model.getShopSource()) &&
                StringUtils.isNotBlank(tbTrade.getSellerNick()) &&
                !tbTrade.getSellerNick().equals(model.getShopName())) {
            TradeSourcePlatformCodeRelation relation = TradeSourcePlatformCodeDiamondUtils.getPlatformBySource(tbTrade.getSubSource(), "qimen");
            model.setShopName(relation.getName() + "-" + model.getShopName() + "-" + (StringUtils.isNotEmpty(tbTrade.getQimenShopShortName()) ? tbTrade.getQimenShopShortName() : tbTrade.getSellerNick()));
        }
        //合单后订单优惠金额
        if (StringUtils.isNotBlank(tbTrade.getMergeDiscountFee())) {
            model.setMergeDiscountFee(tbTrade.getMergeDiscountFee());
        }

        calculateItemCount(tbTrade, model);
        //把canDeliverd为3,9的设置为2
        if (model.getCanDelivered() == null || model.getCanDelivered() - 3 == 0 || model.getCanDelivered() - 9 == 0) {
            model.setCanDelivered(2);
        }

        if (null != expressCompany) {
            model.setExpressCode(expressCompany.getCode());
            model.setExpressName(expressCompany.getName());
        }

        model.setNetWeight(MathUtils.toString(tbTrade.getNetWeight(),4));

        boolean openBic = TradeUtils.isFxgBicTrade(tbTrade, tradeConfig);
        if (null != tbTrade.getTemplateId() && tbTrade.getTemplateId() >= 0L) {
            model.setExpressTemplateId(tbTrade.getTemplateId()).setExpressTemplateType(tbTrade.getTemplateType());
            if (tbTrade.getTemplateId() == 0L) {
                model.setExpressTemplateName("无需快递");
            } else if (CommonConstants.PLAT_FORM_TYPE_BIC.equals(tbTrade.getSubSource()) && openBic) {
                model.setExpressTemplateName("QIC质检电子面单");
                model.setExpressTemplateType(1);
            } else {
                model.setExpressTemplateName(tbTrade.getTemplateName());
            }
            model.setIsManulfill(lvn(tbTrade.getIsManulfill()));
        } else {
            model.setExpressTemplateId(-1L);
            if (TradeUtils.isFxgBicTrade(tbTrade, tradeConfig)) {
                model.setExpressTemplateName("QIC质检电子面单").setExpressTemplateType(-1);
            } else {
                model.setExpressTemplateName("").setExpressTemplateType(0);
            }
        }

        if (null != tbTrade.getLogisticsCompanyId() && tbTrade.getLogisticsCompanyId() > 0L) {
            model.setLogisticsCompanyId(tbTrade.getLogisticsCompanyId());
            if (CommonConstants.PLAT_FORM_TYPE_BIC.equals(tbTrade.getSubSource()) && openBic) {
                model.setLogisticsCompanyName("QIC质检电子面单");
            } else if (Long.valueOf(0).equals(tbTrade.getTemplateId())) {
                model.setLogisticsCompanyName("无需快递");
            } else {
                model.setLogisticsCompanyName(tbTrade.getLogisticsCompanyName());
            }
            model.setIsManulfill(lvn(tbTrade.getIsManulfill()));
        } else {
            if (TradeUtils.isFxgBicTrade(tbTrade, tradeConfig)) {
                model.setLogisticsCompanyName("QIC质检电子面单");
            } else {
                //老订单展示模板id
                model.setLogisticsCompanyName(tbTrade.getTemplateName());
            }
        }
        // 设置退款状态
        setIsRefund(staff, model, tbTrade);

        // 过滤订单的买家留言和卖家备注
        model = generatePaymentDisplay(toMessageMemos(staff, model, tbTrade, false, useCompress), tbTrade, false);

        setIsHandleMemo_message(model);

        toBaseTbTradeModel(staff, model, tbTrade);

        // 设置异常状态
        model.setExceptions(ExceptionStatusHelper.analyze(staff, tbTrade, model));
        // 设置拆分装箱信息
        model.setTradePackSplits(tbTrade.getTradePackSplits());

        if (CollectionUtils.isNotEmpty(tbTrade.getTags())) {
            model.setTagName(buildTagName(tbTrade.getTags()));
            List<TradeTagVo> tagVoList = new ArrayList<>();
            for (TradeTag tradeTag : tbTrade.getTags()) {
                TradeTagVo tagVo = new TradeTagVo();
                tagVo.setId(tradeTag.getId());
                tagVo.setTagName(tradeTag.getTagName());
                tagVo.setBgColor(tradeTag.getBgColor());
                tagVo.setFontColor(tradeTag.getFontColor());
                tagVo.setType(tradeTag.getType());
                tagVo.setRemark(tradeTag.getRemark());
                tagVo.setCanDelete(1);
                tagVo.setSupportManualUpdate(tradeTag.getSupportManualUpdate());

                tagVoList.add(tagVo);
            }
            model.setTagList(tagVoList);
        }

        if (CollectionUtils.isNotEmpty(tbTrade.getExceptNames())) {
            model.setExceptNames(Strings.join(",", tbTrade.getExceptNames()));
        }else{
            model.setExceptNames("");
        }
        model.setEndTime(tbTrade.getEndTime());
        model.setCost(tbTrade.getCost());
        model.setVisualCost(String.valueOf(tbTrade.getCost()));

        model.setBuyerName(tbTrade.getReceiverName());
        model.setBuyerNick(tbTrade.getBuyerNick());
        //淘系 买家昵称和openUid互换展示
        TradeFieldUtils.BuyerNickResult nickResult = TradeFieldUtils.handleBuyerNickAndOpenUid(staff, tbTrade);
        model.setBuyerNick(nickResult.getBuyerNick());
        model.setOpenUid(nickResult.getOpenUid());

        if (CollectionUtils.isNotEmpty(TradeUtils.getOrders4Trade(tbTrade))) {
            //增加买家名，买家昵称，商品图片，商品编码
            model.setOrders(toBaseOrderModels(staff, tbTrade, tradeConfig));
            //商品成交总额
            model.setOrderPayment(NumberUtils.decimal2Str(PaymentUtils.totalPayment(tbTrade,true),2));
            List<OrderModel> orderModels = model.getOrders();

            if (CollectionUtils.isNotEmpty(orderModels)) {
                model.setItemImage(orderModels.get(0).getPicPath());
                model.setOuterId(orderModels.get(0).getSysOuterId());
                model.setPropertiesName(orderModels.get(0).getSkuPropertiesName());
            }
        }

        dataPermissionProcess(staff, model);

        TradeExt tradeExt = tbTrade.getTradeExt();
        if (tradeExt != null) {
            initExtraFieldsMap(tradeExt, staff);
            fillTradeModelByExtraField(tradeExt, tradeExt.getExtraFieldsMap(), model);
            if (StringUtils.isNotBlank(tradeExt.getLogisticsCode()) && (!"noCode".equals(tradeExt.getLogisticsCode()) || TradeUtils.isPoisonBrandDeliverTrade(tbTrade))) {

                model.setLogisticsName(PddLogisticsAccess.getExpressCompanyByCodes(tradeExt.getLogisticsCode()));
                model.setLogisticsCode(tradeExt.getLogisticsCode());
                if (TradeUtils.platformMatch(staff, tbTrade, CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU)
                    || TradeUtils.platformMatch(staff, tbTrade, CommonConstants.PLAT_FORM_TYPE_YIDINGHUO2)
                ) {
                    model.setLogisticsName(tradeExt.getLogisticsCode());
                }
                // name 从 ext 获取
                if (TradeUtils.platformContain(staff, tbTrade, TradeOptionalLogisticsUtils.GET_NAME_FROM_EXT_SOURCES)) {
                    model.setLogisticsName(TradeOptionalLogisticsUtils.getLogisticsName(tradeExt));
                }
            }
            if (TradeUtils.isTbNeedGovSnCheck(tbTrade)) {
                model.setGovSubsidyInfo(TradeExtUtils.parseGovSubsidyInfo(tradeExt));
            }

            if (tbTrade.isTmallDesignatedLogistics()) {
                model.setLogisticsName(tradeExt.getLogisticsCode());
            }

            model.setWrapperDescription(tradeExt.getWrapperDescription());
            model.setPlatformStoreName(tradeExt.getStoreName());
            if (StringUtils.isNotBlank(tradeExt.getTmallAsdpAds())) {
                String[] temps = tradeExt.getTmallAsdpAds().split(",");
                model.getTmallAsdpAdsTypeSet().addAll(Arrays.asList(temps));
            }
            model.setPromiseDeliveryTime(tradeExt.getPromiseDeliveryTime());

            model.setShippingCarrier(tradeExt.getShippingCarrier());
            model.setCombineParcelId(tradeExt.getCombineParcelId());
            //暂时只处理抖音代发的
            model.setMallMaskName(TradeUtils.isDfSource(tbTrade) ? tradeExt.getMallMaskName() : "");
            model.setMallMaskId(tradeExt.getMallMaskId() == null ? "" : String.valueOf(tradeExt.getMallMaskId()));
            model.setCooperationNo(tradeExt.getCooperationNo());
            model.setSellSite(tradeExt.getSellSite());
            // 获取第三方订单号/订单解密号用于端解密
            String extraFields = tradeExt.getExtraFields();
            String thirdPlatTid = fillTradeExtThirdPlatTid(extraFields);
            if (StringUtils.isNotEmpty(thirdPlatTid)) {
                model.setThirdPlatTid(thirdPlatTid);
            }
            //btas订单填充订单保价
            if (TradeTypeUtils.isFxgBtasTrade(tbTrade)) {
                model.setInsuranceCost(TradeExtUtils.getInsuranceCost(tradeExt));
                model.setInsuranceCostType(TradeExtUtils.getInsuredPriceType(tradeExt));
            }

            setXsdFulfillmentInfo(staff, user, tbTrade, model, tradeExt);
            if(PlatformUtils.isTradeO2o(tbTrade)){
                model.addOpV(OpVEnum.TRADE_IF_O2O);
            }

            model.setFxPlatformTid(fillTradeExtFxPlatTid(tbTrade,tradeExt.getExtraFields()));
        }
        model.setTradeInvoice(tbTrade.getTradeInvoice());

        //设置团长信息
        fillDistributorInfo(model, tbTrade);

        // 设置关联店铺ID
        if (!ObjectUtils.isEmpty(staff) && !ObjectUtils.isEmpty(shop) && !ObjectUtils.isEmpty(shop.getUserId())) {
//            User user = staff.getUserByUserId(shop.getUserId());
            Long associatedShopId = model.getUserByAssociatedUserId(user);
            if (!ObjectUtils.isEmpty(associatedShopId) && associatedShopId > 0) {
                model.setAssociatedUserId(associatedShopId);
            }
        }
        //业务员
        model.setSalesmanName(tbTrade.getSalesmanName());
        handleMerge(tbTrade, model);
        //智能单品数量
        model.setItemQuantity(tbTrade.getItemQuantity());
        // 翱象订单标示
        handleAox(staff, model, tbTrade);
        //展示计算毛利公式
        model.setGrossProfitDisplay(hasPowerPayment ? tbTrade.getGrossProfitDisplay() : "***");

        model.setPackageStatus("");
        if (TradeStatusUtils.isAfterSendGoods(tbTrade.getSysStatus())) {
            if (Objects.equals(tbTrade.getIsPackage(),0)) {
                model.setPackageStatus("已发未验");
            }else {
                model.setPackageStatus("已发已验");
            }
        }


        //分销商简称
        model.setSourceAliasName(tbTrade.getSourceAliasName());
        handelSysMemo(model, tbTrade);

        if (highlight && StringUtils.isNotEmpty(searchKey)) {
            model.setHighlights(buildHighlights(model, searchKey));
        }

        return handleBacklandKey(model);
    }

    private static void handelSysMemo(TradeModel model, Trade trade){
        if (model == null || trade == null) {
            return;
        }
        if (CommonConstants.PLAT_FORM_TYPE_1688.equals(trade.getSource()) && Objects.equals(trade.getSysConsigned(), 2)) {
            Object extraFieldValue = TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "logisticsBillNo");
            if (extraFieldValue != null) {
                String logisticsBillNo = String.valueOf(extraFieldValue);
                if (StringUtils.isBlank(model.getSysMemo())) {
                    model.setSysMemo(logisticsBillNo);
                } else {
                    if (!model.getSysMemo().contains(logisticsBillNo)){
                        model.setSysMemo(model.getSysMemo().concat("【").concat(logisticsBillNo).concat("】"));
                    }
                }
            }
        }
    }

    public static String buildSortString(TradeModel model) {
        List<OrderModel> orders = model.getOrders();
        if (CollectionUtils.isNotEmpty(orders)) {
            orders = orders.stream().sorted(Comparator.comparing(TradeModel::getSortOuterId)).collect(Collectors.toList());
            List<String> sortOuterIds = new ArrayList<>(orders.size());
            Map<String,Integer> nums = new HashMap<>();
            for (OrderModel order : orders) {
                String sortOuterId = getSortOuterId(order);
                if (Objects.equals("zzzzzz",sortOuterId)) {
                    sortOuterId = order.getTitle();
                }
                int num = order.getNum() == null?0:order.getNum();
                if (!sortOuterIds.contains(sortOuterId)) {
                    sortOuterIds.add(sortOuterId);
                    nums.put(sortOuterId,num);
                }else{
                    nums.put(sortOuterId,nums.get(sortOuterId) + num);
                }
            }
            StringBuilder s = new StringBuilder();
            for (int i = 0; i < sortOuterIds.size(); i++) {
                if (i>0) {
                    s.append(",");
                }
                if (i >= 20) {
                    s.append("...");
                    break;
                }
                String sortOuterId = sortOuterIds.get(i);
                s.append(sortOuterId).append("*").append(nums.get(sortOuterId));
            }
            return s.toString();
        }
        return "";
    }

    /**淘宝小时达业务*/
    private static void setXsdFulfillmentInfo(Staff staff, User user, Trade tbTrade, TradeModel model, TradeExt tradeExt) {
        try {
            Map<String, Object> fieldsMap = tradeExt.getExtraFieldsMap();
            if (fieldsMap == null) {
                return;
            }

            String storeName = (String)fieldsMap.get("storeName");
            if(CommonConstants.PLAT_FORM_TYPE_TAO_BAO_XSD.equals(model.getShopSource())
                    && UserUtils.isTbXsdHeadStore(user)
                    && StringUtils.isNotBlank(storeName)
                    && !Objects.equals(storeName, model.getShopName())
            ){ //淘宝小时达  显示店铺名 加上门店名称
                model.setShopName( model.getShopName() + "-" +  storeName);
            }

            if (fieldsMap.containsKey("xsdFulfillmentInfo")) {
                JSONObject jsonObject = JSONObject.parseObject(String.valueOf(fieldsMap.get("xsdFulfillmentInfo")));
                model.setXsdDelivererName(jsonObject.getString("delivererName"));
                model.setXsdDelivererPhone(jsonObject.getString("delivererPhone"));
                model.setXsdOrderNo(jsonObject.getString("orderNo"));
            }

        }catch (Throwable e){
            new QueryLogBuilder(staff).appendError("填充小时达信息异常",e).printError(logger,e);
        }

    }

    private static void handleAox(Staff staff, TradeModel model, Trade trade) {
        if (trade == null) {
            return;
        }
        PlatformRecommendation pr = trade.getPlatformRecommendation();
        if (pr == null) {
            return;
        }

        TbRecommendation tbRecommendation = pr.getTbRecommendation();
        if (tbRecommendation == null) {
            return;
        }

        TbRecommendation.LogisticsAgreement logisticsAgreement = tbRecommendation.getLogisticsAgreement();
        if (logisticsAgreement == null) {
            return;
        }

        if (Objects.equals(CommonConstants.PLAT_FORM_TYPE_AOX, logisticsAgreement.getAsdpBizType())) {
            model.setAox(true);
        }
        // 计划送达时间
        if (StringUtils.isNotEmpty(logisticsAgreement.getSignTime())) {
            model.setSignTime(DateUtils.str2Date(logisticsAgreement.getSignTime()));
        }
        // 承诺/最晚送达时间
        if (StringUtils.isNotEmpty(logisticsAgreement.getPromiseSignTime())) {
            model.setPromiseDeliveryTime(DateUtils.str2Date(logisticsAgreement.getPromiseSignTime()));
        }

        // 处理合单
        Map<String, PlatformRecommendation> tidToPr = trade.getTidToPr();
        if (tidToPr == null || tidToPr.size() <= 1) {
            return;
        }

        for (PlatformRecommendation p : tidToPr.values()) {
            if (p == null) {
                continue;
            }

            TbRecommendation r = p.getTbRecommendation();
            if (r == null) {
                continue;
            }

            TbRecommendation.LogisticsAgreement l = r.getLogisticsAgreement();
            if (l == null) {
                continue;
            }

            Date signT = DateUtils.str2Date(l.getSignTime());
            if (signT == null) {
                continue;
            }
            if (model.getSignTime() == null || signT.before(model.getSignTime())) {
                model.setSignTime(signT);
            }
            Date promiseDeliveryT = DateUtils.str2Date(l.getPromiseSignTime());
            if (promiseDeliveryT == null) {
                continue;
            }
            if (model.getPromiseDeliveryTime() == null || promiseDeliveryT.before(model.getPromiseDeliveryTime())) {
                model.setPromiseDeliveryTime(promiseDeliveryT);
            }
        }
    }



    /**
     * 设置经分销信息  团长信息
     *
     * @param model
     * @param tbTrade
     */
    public static void fillDistributorInfo(TradeModel model, Trade tbTrade) {
        TradeDistributor tradeDistributor = tbTrade.getTradeDistributor();
        if (tradeDistributor != null) {
            model.setDistributorId(tradeDistributor.getDistributorId());
            model.setDistributorName(tradeDistributor.getDistributorName());
        }

    }


    private static void initExtraFieldsMap(TradeExt tradeExt, Staff staff) {
        if (tradeExt == null || StringUtils.isBlank(tradeExt.getExtraFields())) {
            return;
        }
        if (tradeExt.getExtraFieldsMap() == null || tradeExt.getExtraFieldsMap().isEmpty()) {
            try {
                Map extraFieldsMap = JSONObject.parseObject(tradeExt.getExtraFields(), Map.class);
                tradeExt.setExtraFieldsMap(extraFieldsMap);
            } catch (Exception e) {
                logger.warn(LogHelper.buildLog(staff, String.format("订单%s的extraField转换Map异常，extraField：%s", tradeExt.getTid(), tradeExt.getExtraFields())));
            }
        }
    }

    /**
     * tradeExt表的extraField字段填充到TradeModel
     *
     * @param tradeExt
     * @param extraFieldsMap
     * @param model
     */
    private static void fillTradeModelByExtraField(TradeExt tradeExt, Map<String, Object> extraFieldsMap, TradeModel model) {
        try {
            if (model == null || tradeExt == null || extraFieldsMap == null || extraFieldsMap.isEmpty()) {
                return;
            }
            model.setIncreaseFreightExpress((String) extraFieldsMap.get("increaseFreightExpress"));
            model.setIncreaseFreightFee((String) extraFieldsMap.get("increaseFreightFee"));
            model.setCommunityGroupId((String) extraFieldsMap.get("communityGroupId"));
            model.setCommunityGroupRole((String) extraFieldsMap.get("communityGroupRole"));
            model.setSelfBuiltDepositAmount((String) extraFieldsMap.get("selfBuiltDepositAmount"));
            model.setSelfBuiltPaymentReceivable((String) extraFieldsMap.get("selfBuiltPaymentReceivable"));
            model.setPlatformAfterSaleTid((String) extraFieldsMap.get("platformId"));//换货单会用到
            model.setPlatformAfterSaleIdSource((String) extraFieldsMap.get("platformIdSource"));
            model.setPurchaseBatch((String) extraFieldsMap.get("purchaseBatch"));
            model.setMergeDeliveryType((Integer) extraFieldsMap.get("mergeDeliveryType"));

            Optional.ofNullable(extraFieldsMap.get("actualVolume"))
                    .ifPresent(alv -> model.setActualVolume(Objects.toString(alv)));
            model.setActualLengthWidthAndHeight((String) extraFieldsMap.get("actualLengthWidthAndHeight"));
            model.setOutStoreName((String) extraFieldsMap.get("outStoreName"));

            model.setPerformanceType( extraFieldsMap.get("performance_type") == null? null:(String)extraFieldsMap.get("performance_type"));
            if (PlatformUtils.isGovSubsidy3COrder(tradeExt) || PlatformUtils.isUseGovSubsidy(tradeExt)) {
                GovSubsidyInfo govSubsidyInfo = TradeExtUtils.parseGovSubsidyInfo(tradeExt);
                model.setGovSubsidyInfo(govSubsidyInfo);
            }

            model.setAlibabaOfficialPickUp(extraFieldsMap.get("alibabaOfficialPickUp") == null ? null : (String)extraFieldsMap.get("alibabaOfficialPickUp"));
            //速卖通小额批发订单
            handleSmtEstimatedRevenue(extraFieldsMap, model);
            Object tradePictureMemoUris = extraFieldsMap.get("tradePictureMemoUris");
            if (tradePictureMemoUris != null) {
                if (tradePictureMemoUris instanceof List) {
                    try {
                        List<String> tempList = (List<String>) tradePictureMemoUris;
                        model.setTradePictureMemoUris(tempList);
                    } catch (RuntimeException e) {
                        logger.warn(String.format("公司:%s,订单sid:%s,tradePictureMemoUris转换异常,tradePictureMemoUris:%s", tradeExt.getCompanyId(), tradeExt.getSid(), JSONObject.toJSONString(tradePictureMemoUris)));
                    }
                } else {
                    logger.warn(String.format("公司:%s,订单sid:%s,tradePictureMemoUris不是List,tradePictureMemoUris:%s", tradeExt.getCompanyId(), tradeExt.getSid(), JSONObject.toJSONString(tradePictureMemoUris)));
                }
            }
            model.setSnImgRule((String) extraFieldsMap.get("snImgRule"));

            if (extraFieldsMap.containsKey("snImgRuleUrls")) {
                Object snImgRuleUrls = extraFieldsMap.get("snImgRuleUrls");
                if (snImgRuleUrls instanceof List) {
                    try {
                        model.setSnImgRuleUrls((List<String>) snImgRuleUrls);
                    } catch (RuntimeException e) {
                        logger.warn(String.format("公司:%s,订单sid:%s,snImgRuleUrls转换异常,snImgRuleUrls:%s", tradeExt.getCompanyId(), tradeExt.getSid(), JSONObject.toJSONString(snImgRuleUrls)));
                    }
                } else {
                    logger.warn(String.format("公司:%s,订单sid:%s,snImgRuleUrls不是List,snImgRuleUrls:%s", tradeExt.getCompanyId(), tradeExt.getSid(), JSONObject.toJSONString(snImgRuleUrls)));
                }
            }
            //设置拼多多集运类型
            model.setConsolidateType(extraFieldsMap.get("consolidate_type") == null ? null : (Integer)extraFieldsMap.get("consolidate_type"));
        } catch (Exception e){
            logger.warn(String.format("fillTradeModelByExtraField处理异常，extraFieldsMap：%s", JSONObject.toJSONString(extraFieldsMap)), e);
        }
    }

    /**
     * 处理速卖通小额批发订单预估金额
     * @param extraFieldsMap
     * @param model
     */
    private static void handleSmtEstimatedRevenue(Map<String, Object> extraFieldsMap, TradeModel model) {
        if (CommonConstants.PLAT_FORM_TYPE_SMT.equals(model.getSource()) && "1".equals((String) extraFieldsMap.get("s_s"))){
            SmtEstimatedRevenueVo vo = new SmtEstimatedRevenueVo();
            vo.setSelfShipAmount((String) extraFieldsMap.get("selfShipAmount"));
            vo.setSelfShipCurrencyCode((String) extraFieldsMap.get("selfShipCurrencyCode"));
            vo.setSemiShipAmount((String) extraFieldsMap.get("semiShipAmount"));
            vo.setSemiShipCurrencyCode((String) extraFieldsMap.get("semiShipCurrencyCode"));
            vo.setSemiSwitchShipDeadline((String) extraFieldsMap.get("semiSwitchShipDeadline"));
            model.setSmtEstimatedRevenueVo(vo);
        }
    }

    /**
     * 获取第三方订单号
     *
     * @param extraFields
     * @return
     */
    private static String fillTradeExtThirdPlatTid(String extraFields) {
        try {
            if (StringUtils.isNotEmpty(extraFields)) {
                JSONObject jsonObject = JSONObject.parseObject(extraFields);
                if (!ObjectUtils.isEmpty(jsonObject) && !StringUtils.isEmpty(jsonObject.getString("thirdPlatTid"))) {
                    return jsonObject.getString("thirdPlatTid");
                }
            }
        } catch (Exception e) {
            logger.error("订单扩展信息转换异常:" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取供销原始订单号
     * @param extraFields tradeExt.extraFields
     * @return
     */
    public static String fillTradeExtFxPlatTid(Trade tbTrade, String extraFields) {
        try {
            if (StringUtils.isNotEmpty(extraFields)) {
                JSONObject jsonObject = JSONObject.parseObject(extraFields);
                if (TradeUtils.isGxOrMixTrade(tbTrade) && !ObjectUtils.isEmpty(jsonObject)) {
                    //如果是有分销合单的原始单号信息，先填充
                    if(TradeUtils.isReissueOrChangeitem(tbTrade) && StringUtils.isNotBlank(jsonObject.getString("fxTid"))){
                        return jsonObject.getString("fxTid");
                    }
                    if (!StringUtils.isEmpty(jsonObject.getString("mergePlatformId"))){
                        return jsonObject.getString("mergePlatformId");
                    }else if (!StringUtils.isEmpty(jsonObject.getString("platformId"))){
                        return jsonObject.getString("platformId");
                    }
                }
                if ((TradeUtils.isQimenFxSource(tbTrade) || StringUtils.isNotEmpty(tbTrade.getSource()) && "qimen".equals(tbTrade.getSource())) && !ObjectUtils.isEmpty(jsonObject) && !StringUtils.isEmpty(jsonObject.getString("thirdPlatTid"))) {
                    return jsonObject.getString("thirdPlatTid");
                }
            }
        } catch (Exception e) {
            logger.error("订单扩展信息转换异常:" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 根据关联店铺ID获取用户信息
     *
     * @param user
     * @return
     */
    public Long getUserByAssociatedUserId(User user) {
        if (!ObjectUtils.isEmpty(user)) {
            if (user.getSource().equals(CommonConstants.PLAT_FORM_TYPE_TMCS)
                    || user.getSource().equals(CommonConstants.PLAT_FORM_TYPE_TMGJZY)
                    || user.getSource().equals(CommonConstants.PLAT_FORM_TYPE_TMYP)
                    || user.getSource().equals(CommonConstants.PLAT_FORM_TYPE_MIAOSUDA)) {
                if (!ObjectUtils.isEmpty(user.getUserConf()) && !ObjectUtils.isEmpty(user.getUserConf().getSellerInfo())) {
                    Map<String, Object> sellerInfo = user.getUserConf().getSellerInfo();
                    if (sellerInfo.containsKey("associatedShopId")) {
                        String tempAssociatedShopId = String.valueOf(sellerInfo.get("associatedShopId"));
                        if (!ObjectUtils.isEmpty(tempAssociatedShopId)) {
                            Long associatedShopId = Long.valueOf(tempAssociatedShopId);
                            return associatedShopId;
                        }
                    }
                    if (sellerInfo.containsKey("associationId")) {
                        String tempAssociatedShopId = String.valueOf(sellerInfo.get("associationId"));
                        if (!ObjectUtils.isEmpty(tempAssociatedShopId)) {
                            Long associatedShopId = Long.valueOf(tempAssociatedShopId);
                            return associatedShopId;
                        }
                    }

                }
            }
        }
        return null;
    }

    /**
     * 合单处理
     *
     * @param tbTrade
     * @param model
     */
    private static void handleMerge(Trade tbTrade, TradeModel model) {
        if (!TradeUtils.isMerge(tbTrade) || CollectionUtils.isEmpty(tbTrade.getMessageMemos())) {
            return;
        }
        Set<String> salesmanNameSet = new HashSet<>();
        List<String> selfBuiltDepositAmount = new ArrayList<>();
        List<String> selfBuiltPaymentReceivable = new ArrayList<>();
        for (MessageMemo messageMemo : tbTrade.getMessageMemos()) {
            if (StringUtils.isNotBlank(messageMemo.getSalesmanName())) {
                salesmanNameSet.add(messageMemo.getSalesmanName());
            }
            if (messageMemo.getSelfBuiltDepositAmount() != null) {
                selfBuiltDepositAmount.add(messageMemo.getSelfBuiltDepositAmount());
            }
            if (messageMemo.getSelfBuiltPaymentReceivable() != null) {
                selfBuiltPaymentReceivable.add(messageMemo.getSelfBuiltPaymentReceivable());
            }
        }
        if (CollectionUtils.isNotEmpty(salesmanNameSet)) {
            model.setSalesmanName(salesmanNameSet.stream().collect(Collectors.joining(";")));
        }
        //定金
        if (CollectionUtils.isNotEmpty(selfBuiltDepositAmount)) {
            model.setSelfBuiltDepositAmount(MathUtils.add(selfBuiltDepositAmount.toArray(new String[0])).toString());
        }
        //代收金额
        if (CollectionUtils.isNotEmpty(selfBuiltPaymentReceivable)) {
            model.setSelfBuiltPaymentReceivable(MathUtils.add(selfBuiltPaymentReceivable.toArray(new String[0])).toString());
        }

        //非天猫、淘宝订单直接逃过
        if (!(CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(tbTrade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(tbTrade.getSource()))) {
            return;
        }

        List<String> logisticsCodeList = new ArrayList<>();

        TradeExt mainTradeExt = tbTrade.getTradeExt();
        if (Objects.nonNull(mainTradeExt) && StringUtils.isNotBlank(mainTradeExt.getLogisticsCode())) {
            logisticsCodeList.add(mainTradeExt.getLogisticsCode());
        }
        for (MessageMemo messageMemo : tbTrade.getMessageMemos()) {

            if (StringUtils.isNotBlank(messageMemo.getTmallAsdpAds())) {
                String[] temps = messageMemo.getTmallAsdpAds().split(",");
                model.getTmallAsdpAdsTypeSet().addAll(Arrays.asList(temps));
            }

            if (TmallAsdpBizTypeEnum.LOGISTICS_UPGRADE.getCode().equals(messageMemo.getTmallAsdpBizType())) {
                model.setTmallAsdpBizType(messageMemo.getTmallAsdpBizType());
            }

            if (StringUtils.isNotBlank(messageMemo.getLogisticsCode())) {
                logisticsCodeList.add(messageMemo.getLogisticsCode());
            }
        }

        if (CollectionUtils.isNotEmpty(logisticsCodeList)) {
            logisticsCodeList = logisticsCodeList.stream().distinct().collect(Collectors.toList());
            //删除多余指定物流编码
            if (logisticsCodeList.size() >= 2) {
                logisticsCodeList.remove("noCode");
            }
            model.setLogisticsName(logisticsCodeList.get(0));
            model.setLogisticsCode(logisticsCodeList.get(0));
        }
    }

    private static void dataPermissionProcess(Staff staff, TradeModel model) {
        boolean needDesensitization = staff.getPowerDataPrivilegeSettings() != null && staff.getPowerDataPrivilegeSettings().contains("1000304");//商品成本数据权限
        model.setVisualCost(needDesensitization ? String.valueOf(model.getCost()) : "***");
        if (CollectionUtils.isNotEmpty(model.getOrders()) && !needDesensitization) {
            for (OrderModel orderModel : model.getOrders()) {
                orderModel.setVisualCost("***");
            }
        }
    }

    private static String buildTagName(List<TradeTag> tags) {
        StringBuilder buf = new StringBuilder();
        for (TradeTag tag : tags) {
            if (buf.length() > 0) {
                buf.append(",");
            }
            buf.append(StringUtils.isNotBlank(tag.getBgColor()) ? tag.getBgColor().trim() : "0");
            buf.append("_");
            buf.append(StringUtils.isNotBlank(tag.getFontColor()) ? tag.getFontColor().trim() : "0");
            buf.append("_");
            buf.append(tag.getTagName());
            buf.append("_");
            buf.append(tag.getId());
        }
        return buf.toString();
    }

    private static TradeModel createTradeModel(Integer useCompress) {
        if (useCompress == null || useCompress == 0) {
            return new TradeModel();
        }

        return new TradeModelJSONField();
    }

    private static String object2String(Object v) {
        return v != null ? v.toString() : "";
    }

    public static String handlePoNos(Staff staff, Trade tbTrade) {
        if (StringUtils.isBlank(tbTrade.getPoNos()) || staff.isYccrm()) {
            return tbTrade.getPoNos();
        } else {
            if (CommonConstants.PLAT_FORM_TYPE_YSK.equals(tbTrade.getSource())) {
                return tbTrade.getPoNos();
            }
            StringBuffer poNos = new StringBuffer(tbTrade.getPoNos());
            poNos.deleteCharAt(0);
            poNos.deleteCharAt(poNos.length() - 1);
            return poNos.toString();
        }
    }

    public static Integer lvn(Integer val) {
        if (val != null && val == 0) {
            return null;
        }
        return val;
    }

    private static Long lvn(Long val) {
        if (val != null && val == 0) {
            return null;
        }
        return val;
    }

    private static String lvn(String val) {
        if (val != null && "".equals(val)) {
            return null;
        }
        return val;
    }

    public static TradeModel toReprintQueryTradeModel(Staff staff, Trade tbTrade, Shop shop, ExpressCompany expressCompany, boolean highlight, String searchKey, TradeConfig tradeConfig) {
        TradeModel model = new TradeModel();

        if (null == shop) {
            shop = new Shop();
        }
        //通过TJ进入模糊敏感信息
        if (!StringUtils.isEmpty(staff.getShadowToken()) && "2".equals(staff.getIsVague())) {
            TradeUtils.blurTrade(tbTrade);
        }
        model.setSid(tbTrade.getSid().toString()).setShortId(tbTrade.getShortId() != null ? tbTrade.getShortId().toString() : null)
                .setMergeType(tbTrade.getMergeType()).setMergeSid(object2String(tbTrade.getMergeSid()))
                .setSplitType(tbTrade.getSplitType()).setSplitSid(object2String(tbTrade.getSplitSid()))
                .setUserId(tbTrade.getUserId()).setReceiverName(tbTrade.getReceiverName())
                .setReceiverMobile(tbTrade.getReceiverMobile()).setReceiverPhone(tbTrade.getReceiverPhone())
                .setReceiverState(tbTrade.getReceiverState()).setReceiverCity(tbTrade.getReceiverCity())
                .setReceiverDistrict(tbTrade.getReceiverDistrict()).setReceiverStreet(tbTrade.getReceiverStreet()).setReceiverAddress(tbTrade.getReceiverAddress()).setAddressHandled(tbTrade.isAddressHandled() ? 1 : 0).setReceiverDoorplate(tbTrade.getReceiverDoorplate())
                .setReceiverZip(tbTrade.getReceiverZip()).setShopSource(shop.getSource()).setShopSourceName(shop.getSourceName())
                .setIfKmtDf(tbTrade.getIfKmtDf()&&TradeUtils.isFxOrMixTrade(tbTrade)).setIfQimenFxSource(TradeUtils.isQimenFxSource(tbTrade)).setV(tbTrade.getV()).setIfPlatformFx(TradeUtils.isPlatformFx(tbTrade))
                .setIfNotAllowGxSplit(TradeUtils.ifContainV(tbTrade, TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX))
                .setShopFlag(shop.getFlag()).setShopName(StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : StringUtils.trimToEmpty(shop.getTitle())).setExpressStatus(getPrintStatus(tbTrade.getExpressPrintTime()))
                .setIsHalt(TradeExceptUtils.isContainExcept(staff, tbTrade, ExceptEnum.HALT) ? 1 : 0).setCanDelivered(tbTrade.getCanDelivered())
                .setOutSid(tbTrade.getOutSid()).setIsStore(tbTrade.getIsStore())
                .setOriginSysStatus(tbTrade.getSysStatus()).setSysStatus(TradeStatusUtils.convertSysStatus(tbTrade, tradeConfig)).setSysMemo(tbTrade.getSysMemo()).setExceptMemo(tbTrade.getExceptMemo())
                .setBuyerNick(tbTrade.getBuyerNick())
                .setOpenUid(tbTrade.getOpenUid())
                .setEnableStatus(tbTrade.getEnableStatus())
                .setWarehouseId(tbTrade.getWarehouseId())
                .setWaveId(tbTrade.getWaveId())
                .setIsTmallDelivery(tbTrade.getIsTmallDelivery()).setSeq(tbTrade.getSeq());

        //淘系 买家昵称和openUid互换展示
        TradeFieldUtils.BuyerNickResult nickResult = TradeFieldUtils.handleBuyerNickAndOpenUid(staff, tbTrade);
        model.setBuyerNick(nickResult.getBuyerNick());
        model.setOpenUid(nickResult.getOpenUid());

        model.setTid(String.valueOf(tbTrade.getTid()));
        model.setSubSource(tbTrade.getSubSource());


        if (null != expressCompany) {
            model.setExpressCode(expressCompany.getCode());
        }

        if (null != tbTrade.getTemplateId() && tbTrade.getTemplateId() >= 0L) {
            model.setExpressTemplateId(tbTrade.getTemplateId()).setExpressTemplateType(tbTrade.getTemplateType());
            if (tbTrade.getTemplateId() == 0L) {
                model.setExpressTemplateName("无需快递");
            } else {
                model.setExpressTemplateName(tbTrade.getTemplateName());
            }
        } else {
            model.setExpressTemplateId(-1L).setExpressTemplateType(0).setExpressTemplateName("");
        }
        if (null != tbTrade.getLogisticsCompanyId() && tbTrade.getLogisticsCompanyId() >= 0L) {
            model.setLogisticsCompanyName(tbTrade.getLogisticsCompanyName());
            if (tbTrade.getLogisticsCompanyId() == 0L) {
                model.setLogisticsCompanyName(tbTrade.getTemplateName());
            } else {
                model.setLogisticsCompanyName(tbTrade.getLogisticsCompanyName());
            }
        } else {
            model.setLogisticsCompanyName("");
        }

        // 设置退款状态
        setIsRefund(staff, model, tbTrade);

        toBaseTbTradeModel(staff, model, tbTrade);

        // 设置异常状态
        model.setOrders(toBaseOrderModels(staff, tbTrade, tradeConfig));
        model.setExceptions(ExceptionStatusHelper.analyze(staff, tbTrade, model));
        model.setOrders(null);
        // 设置拆分装箱信息
        model.setTradePackSplits(tbTrade.getTradePackSplits());
        //智能单品数量
        model.setItemQuantity(tbTrade.getItemQuantity());
        return handleBacklandKey(model);
    }

    /**
     * 转换为淘宝的订单模型
     *
     * @param model
     * @param tbTrade
     * @return
     */
    private static TradeModel toBaseTbTradeModel(Staff staff, TradeModel model, Trade tbTrade) {
        model.setStatus(tbTrade.getStatus());
        model.setChStatus(TradeStatusUtils.getChStatus(tbTrade.getUnifiedStatus(), true));
        model.setType(lvn(tbTrade.getType()));
        model.setSellerMemo(lvn(tbTrade.getSellerMemo())).setSellerMemoUpdate(lvn(tbTrade.getSellerMemoUpdate())).setBuyerMessage(lvn(tbTrade.getBuyerMessage()));
        model.setPayTime(tbTrade.getPayTime());
        model.setAuditTime(tbTrade.getAuditTime());
        model.setTaobaoId(tbTrade.getTaobaoId());
        if (tbTrade instanceof TbTrade) {
            TbTrade trade = (TbTrade) tbTrade;
            model.setStepPaidFee(trade.getStepPaidFee() == null ? null : trade.getStepPaidFee().toString());
            model.setCodStatus(tbTrade.getCanConfirmSend() == null ? "" : trade.getCodStatus());
        }
        // 部分发货的订单，并且是在线发货的状态时，需要将canConfirmSend值改为0
        if (null != tbTrade.getCanConfirmSend() && tbTrade.getCanConfirmSend() == 1 && isConsignedPart(tbTrade)) {
            model.setCanConfirmSend(0);
        }

        return model;
    }

    /**
     * 判断订单是否是部分发货
     *
     * @param trade
     * @return
     */
    private static boolean isConsignedPart(Trade trade) {
        if (!CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource()) && !CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())) {
            return false;
        }

        if (TradeStatus.TB_SELLER_CONSIGNED_PART.equals(trade.getStatus())) {
            return true;
        }

        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (orders.size() == 0) {
            return false;
        }

        if (!TradeStatus.TB_WAIT_SELLER_SEND_GOODS.equals(trade.getStatus())) {
            return false;
        }

        for (Order order : orders) {
            if (TradeStatus.TB_WAIT_BUYER_CONFIRM_GOODS.equals(order.getStatus())) {
                return true;
            }
        }
        return false;
    }

    /**
     * <p>生成订单价格展现公式，格式如下：payment = total_fee - post_fee - total_discount
     * <p>total_discount = discount_fee(店铺优惠) + 所有子订单的adjust_fee + 所有子订单的discount_fee
     * <p> 正常的订单（未合单和拆单的订单），将这个公式==>payment = total_fee - post_fee - total_discount，按照订单中的值运算出来，
     * 子订单的优惠和手动调价从订单中取出来，即可
     * <p>合单的订单要复杂点，首先获取trade下的messageMemos，这里面包含了订单的payment和totalFee以及discount_fee，然后分析子订单，根据子订单的tid
     * 进行分组，然后将每一组的相关金额和之前获取的messageMemo中的字段进行运算，从而生成展现公式
     *
     * @param trade
     * @return
     */
    @SuppressWarnings("unchecked")
    private static TradeModel generatePaymentDisplay(TradeModel model, Trade trade, boolean useList) {
        try {
            if (trade.getMessageMemos() == null || trade.getMessageMemos().size() == 0) {
                String totalDiscountFee = PaymentUtils.totalDiscountFee(trade);
                model.setTotalDiscountFee(totalDiscountFee);
                model.setPaymentDisplay(PaymentUtils.paymentDisplay(trade));
                return model;
            }

            if (!useList) {
                return generatePaymentDisplay4Base(model, trade);
            }
            return generatePaymentDisplay4Detail(model, trade);
        } catch (Exception e) {
            logger.error("生成价格公式出错:" + e.getMessage(), e);
        }
        return model;
    }

    private static TradeModel generatePaymentDisplay4Base(TradeModel model, Trade trade) {
        if (model.getMessageMemos() == null || model.getMessageMemos().size() == 0)
            return model;

        Set<String> tids = model.getMessageMemos().keySet();
        List<String> listTids = new ArrayList<String>(tids);
        BigDecimalWrapper postFee = new BigDecimalWrapper();
        BigDecimalWrapper theoryPostFee = new BigDecimalWrapper();
        BigDecimalWrapper discountFee = new BigDecimalWrapper();
        BigDecimalWrapper mergeTheoryPostFee = new BigDecimalWrapper();
        BigDecimalWrapper taxFee = new BigDecimalWrapper();
        for (int i = 0; i < listTids.size(); i++) {
            MessageMemo meassageMomo = model.getMessageMemos().get(listTids.get(i));
            String totalDiscountFee = generateTotalDiscountFee(meassageMomo.getPayment(), meassageMomo.getTotalFee(), meassageMomo.getPostFee(), meassageMomo.getDiscountFee(), findOrdersFromMergeTrade(trade, meassageMomo.getTid()));
            meassageMomo.setTotalDiscountFee(totalDiscountFee);
            meassageMomo.setPaymentDisplay(PaymentUtils.paymentDisplay(trade));
            postFee.add(meassageMomo.getPostFee());
            theoryPostFee.add(meassageMomo.getTheoryPostFee());
            discountFee.add(meassageMomo.getDiscountFee());
            if ((Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus()) || Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus())) && trade.getMergeSid() != null && meassageMomo.getSid() != null && trade.getMergeSid().equals(meassageMomo.getSid())) {
                //取出主订单的理论运费显示
                mergeTheoryPostFee.add(meassageMomo.getTheoryPostFee());
            }
            taxFee.add(meassageMomo.getTaxFee());
        }
        model.setPostFee(postFee.getDouble());
        //如果是合单 则返回所有订单的优惠金额总和
        if (TradeUtils.isMerge(trade)) {
            model.setDiscountFee(discountFee.getString());
        }

        if (!mergeTheoryPostFee.isZero()) {
            model.setTheoryPostFee(mergeTheoryPostFee.getDouble());
        } else {
            model.setTheoryPostFee(theoryPostFee.getDouble());
        }
        model.setTaxFee(taxFee.getString());
        model.setPaymentDisplay(PaymentUtils.paymentDisplay(trade));
        return model;
    }

    private static TradeModel generatePaymentDisplay4Detail(TradeModel model, Trade trade) {
        if (model.getMessageMemoList() == null || model.getMessageMemoList().size() == 0)
            return model;

        // 除了MessageMemo单独计算以外，总订单也需要计算一份总的数据
        for (MessageMemo mm : model.getMessageMemoList()) {
            String totalDiscountFee = generateTotalDiscountFee(mm.getPayment(), mm.getTotalFee(), mm.getPostFee(), mm.getDiscountFee(), findOrdersFromMergeTrade(trade, mm.getTid()));
            mm.setTotalDiscountFee(totalDiscountFee);
            mm.setPaymentDisplay(PaymentUtils.paymentDisplay(trade));
        }
        model.setPaymentDisplay(PaymentUtils.paymentDisplay(trade));
        return model;
    }


    /**
     * 从合单中的订单找出其中一个订单的子订单集合
     *
     * @param trade
     * @return
     */
    @SuppressWarnings("unchecked")
    private static List<Order> findOrdersFromMergeTrade(Trade trade, String tid) {
        List<Order> orders = new ArrayList<Order>();
        Orderable<Order> orderable = (Orderable<Order>) trade;
        if (orderable.getOrders() == null || orderable.getOrders().size() == 0)
            return orders;
        for (Order order : orderable.getOrders()) {
            if (order.getTid().equals(tid)) {
                orders.add(order);
            }
        }
        return orders;
    }

    private static String generateTotalDiscountFee(String payment, String totalFee, String postFee, String discountFee, List<Order> orders) {
        // 先校验数据，如果有一个数据不对，则返回空字符串
        if (StringUtils.isEmpty(payment) || StringUtils.isEmpty(totalFee) || StringUtils.isEmpty(postFee) || StringUtils.isEmpty(discountFee))
            return "";
        for (Order order : orders) {
            if (StringUtils.isEmpty(order.getAdjustFee())) {
                order.setAdjustFee("0");
            }
            if (StringUtils.isEmpty(order.getDiscountFee())) {
                order.setDiscountFee("0");
            }
        }

        /**
         * 所有子订单的卖家优惠的金额
         */
        double totalOrderAdjust = 0.0;
        /**
         * 所有子订单的商品优惠的金额
         */
        double totalOrderDiscount = 0.0;

        double totalDiscountFee = Double.parseDouble(discountFee);
        for (Order order : orders) {
            double adjustFee = Double.parseDouble(order.getAdjustFee());
            if (adjustFee < 0)
                adjustFee = Math.abs(adjustFee);
            else
                adjustFee = 0.0 - adjustFee;
            totalOrderAdjust += adjustFee;
            totalOrderDiscount += Double.parseDouble(order.getDiscountFee());
        }

        totalDiscountFee += totalOrderAdjust + totalOrderDiscount;
        return MathUtils.toString(totalDiscountFee);
    }

    /**
     * 处理偏僻地区的关键字
     *
     * @param model
     * @return
     */
    private static TradeModel handleBacklandKey(TradeModel model) {
        if (StringUtils.isEmpty(model.getReceiverAddress())) {
            return model;
        }

        return model.setBacklandKey(lvn(BacklandUtils.getFirstBacklandKey(model.getReceiverAddress())));
    }

    private static Map<String, Object> buildHighlights(TradeModel trade, String searchKey) {
        String[] words = HighlightUtils.filterSameWords(StringUtils.split(searchKey.trim(), ","));
        Map<String, Object> highlights = new HashMap<String, Object>();
        buildHighlight("tid", trade.getTid(), words, highlights);
        buildHighlight("receiverDistrict", trade.getReceiverDistrict(), words, highlights);
        buildHighlight("receiverCity", trade.getReceiverCity(), words, highlights);
        buildHighlight("receiverState", trade.getReceiverState(), words, highlights);

        if (!CommonSecretUtils.ifEncrypt(trade.getReceiverMobile())) {
            buildHighlight("receiverMobile", trade.getReceiverMobile(), words, highlights);
        }
        if (!CommonSecretUtils.ifEncrypt(trade.getReceiverPhone())) {
            buildHighlight("receiverPhone", trade.getReceiverPhone(), words, highlights);
        }
        if (!CommonSecretUtils.ifEncrypt(trade.getReceiverAddress())) {
            buildHighlight("receiverAddress", trade.getReceiverAddress(), words, highlights);
        }
        if (!CommonSecretUtils.ifEncrypt(trade.getReceiverName())) {
            buildHighlight("receiverName", trade.getReceiverName(), words, highlights);
        }

        buildHighlight("receiverZip", trade.getReceiverZip(), words, highlights);
        buildHighlight("sysMemo", trade.getSysMemo(), words, highlights);
        if (trade.getBuyerMessage() != null) {
            buildHighlight("buyerMessage", trade.getBuyerMessage(), words, highlights);
        }
        if (trade.getSellerMemo() != null) {
            buildHighlight("sellerMemo", trade.getSellerMemo(), words, highlights);
        }
        // 商品中的高亮
        Map<String, Map<String, Object>> ordersHighlight = new HashMap<String, Map<String, Object>>(trade.getOrders().size());
        for (OrderModel order : trade.getOrders()) {
            Map<String, Object> orderHighlight = new HashMap<String, Object>();
            buildHighlight("tid", order.getTid(), words, orderHighlight);
            buildHighlight("outerId", order.getOuterId(), words, orderHighlight);
            buildHighlight("title", order.getTitle(), words, orderHighlight);
            buildHighlight("sysSkuPropertiesName", order.getSysSkuPropertiesName(), words, orderHighlight);
            buildHighlight("sysSkuPropertiesAlias", order.getSysSkuPropertiesAlias(), words, orderHighlight);
            buildHighlight("skuPropertiesName", order.getSkuPropertiesName(), words, orderHighlight);
            buildHighlight("sysRemark", order.getSysRemark(), words, orderHighlight);
            if (orderHighlight.size() == 0)
                continue;
            ordersHighlight.put(order.getId(), orderHighlight);
        }
        highlights.put("orders", ordersHighlight);
        return highlights;
    }

    private static void buildHighlight(String propertyName, String value, String[] words, final Map<String, Object> highlights) {
        HighlightUtils.HighlightInfo info = HighlightUtils.highlight(value, words, "<em>", "</em>");
        if (!info.isHighlighting())
            return;

        highlights.put(propertyName, info.getContent());
    }

    private static TradeModel toMessageMemos(Staff staff, TradeModel model, Trade tbTrade, boolean useList, Integer useCompress) {
        boolean hasPaymentPower = staff.getPowerDataPrivilegeSettings() != null && staff.getPowerDataPrivilegeSettings().contains("1000302");//订单售价的数据权限
        boolean hasFxCostPower = staff.getPowerDataPrivilegeSettings() != null && staff.getPowerDataPrivilegeSettings().contains("100030015");//分销总成本数据权限
        boolean hasFxTotalCommissionPower = staff.getPowerDataPrivilegeSettings() != null && staff.getPowerDataPrivilegeSettings().contains("100030016");//佣金数据权限
        BigDecimalWrapper columnPayment = new BigDecimalWrapper();
        BigDecimalWrapper columnAcPayment = new BigDecimalWrapper();
        BigDecimalWrapper payAmount = new BigDecimalWrapper();
        BigDecimalWrapper fxTotalPrice = new BigDecimalWrapper();
        BigDecimalWrapper fxTotalCost = new BigDecimalWrapper();
        BigDecimalWrapper fxTotalCommission = new BigDecimalWrapper();
        if (!useList) {
            Map<String, MessageMemo> map = new HashMap<String, MessageMemo>();
            if (null == tbTrade.getMessageMemos() || tbTrade.getMessageMemos().size() == 0) {
                MessageMemo memo = MessageMemo.valueOf(staff,tbTrade);
                map.put(tbTrade.getTid(), createMessageMemo(memo, useCompress));
                columnPayment = new BigDecimalWrapper(tbTrade.getPayment());
                columnAcPayment = new BigDecimalWrapper(tbTrade.getAcPayment());
                fxTotalCost.add(memo.getFxCost());
                fxTotalCommission.add(memo.getFxCommission());
                fxTotalPrice.add(memo.getFxTotalPrice());
            } else {
                List<MessageMemo> memos = tbTrade.getMessageMemos();
                for (MessageMemo memo : memos) {
                    if (!hasPaymentPower) {//没有订单售价的数据权限
                        memo.setPayment("***");
                        memo.setAcPayment("***");
                    } else {
                        columnPayment.add(memo.getPayment());
                        columnAcPayment.add(memo.getAcPayment());
                        payAmount.add(memo.getPayAmount());
                    }
                    if (TradeUtils.isMerge(tbTrade) && Objects.equals(tbTrade.getMergeSid(), memo.getSid())) {
                        fxTotalCost.add(memo.getFxCost());
                        fxTotalCommission.add(memo.getFxCommission());
                        fxTotalPrice.add(memo.getFxTotalPrice());
                    }
                    map.put(memo.getTid(), createMessageMemo(memo, useCompress));

                }
            }
            if (TradeUtils.isFxOrMixTrade(tbTrade)) {
                model.setFxTotalPrice(fxTotalPrice.getString());
                model.setFxCommission(hasFxTotalCommissionPower ? fxTotalCommission.getString() : "***");
                model.setFxCost(hasFxCostPower ? fxTotalCost.getString() : "***");
            }
            model.setMessageMemos(map);
            model.setColumnPayment(hasPaymentPower ? columnPayment.getString() : "***");
            model.setColumnAcPayment(hasPaymentPower ? columnAcPayment.getString() : "***");
            model.setGrossProfit(hasPaymentPower ? model.getGrossProfit() : "***");
            //如果memo里累加的payAmount不为0，则将payAmount进行赋值替换
            if (!payAmount.isZero()) {
                model.setPayAmount(payAmount.getString());
            }
            model.setPlatformDiscountFee(tbTrade.getPlatformDiscountFee());
            return model;
        }

        return model.setMessageMemoList(tbTrade.getMessageMemos());
    }

    private static MessageMemo createMessageMemo(MessageMemo memo, Integer useCompress) {
        if (useCompress == null || useCompress == 0) {
            memo.setOrderList(null);
            return memo;
        } else {
            return MessageMemoJSONField.valueOf(memo);
        }
    }

    public static Integer getPrintStatus(Date printDate) {
        if (null == printDate)
            return 0;
        return printDate.after(TradeTimeUtils.INIT_DATE) ? 1 : 0;
    }

    private static TradeModel setIsRefund(Staff staff, TradeModel model, Trade trade) {
        int isRefund = 0;
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.REFUNDING)) {
            isRefund = 1;
        }
        model.setIsRefund(isRefund);
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            if (Order.REFUND_SUCCESS.equals(order.getRefundStatus())) {
                model.setHasRefunded(1);
            }
        }
        return model;
    }

    private static TradeModel setIsHandleMemo_message(TradeModel model) {
        Map<String, MessageMemo> messageMemosMap = model.getMessageMemos();
        if (messageMemosMap != null && messageMemosMap.size() > 0) {
            List<MessageMemo> messageMemos = new ArrayList<>(messageMemosMap.values());
            if (messageMemos != null && messageMemos.size() > 0) {
                List<Integer> memoList = messageMemos.stream().filter(memo -> StringUtils.isNotBlank(memo.getSellerMemo())).map(MessageMemo::getIsHandlerMemo).collect(Collectors.toList());
                List<Integer> messageList = messageMemos.stream().filter(message -> StringUtils.isNotBlank(message.getBuyerMessage())).map(MessageMemo::getIsHandlerMessage).collect(Collectors.toList());
                if (memoList.contains(0)) {
                    model.setIsHandlerMemoDisplay(0);
                } else {
                    model.setIsHandlerMemoDisplay(1);
                }

                if (messageList.contains(0)) {
                    model.setIsHandlerMessageDisplay(0);
                } else {
                    model.setIsHandlerMessageDisplay(1);
                }
            }
        }
        return model;
    }

    public static TradeModel toDetailTradeOutModel(Staff staff, Trade tbTrade, ExpressCompany expressCompany, TradeConfig tradeConfig) {
        TradeModel model = toBaseTradeModel(staff, tbTrade, null, expressCompany, false, null, tradeConfig);

        model.setTimeoutActionTime(tbTrade.getTimeoutActionTime());

        if (model.getIsRefund() == null || model.getIsRefund() == 0) {
            return model;
        }

        return model;
    }

    public static TradeModel toDetailTradeModel(User user, Trade tbTrade, Shop shop, ExpressCompany expressCompany, TbTradeAccess tbTradeAccess, TradeConfig tradeConfig) {
        TradeModel model = toBaseTradeModel(user.getStaff(), tbTrade, shop, expressCompany, false, null, tradeConfig);

        model.setShop(shop);
        model.setTimeoutActionTime(tbTrade.getTimeoutActionTime());

        if (model.getIsRefund() == null || model.getIsRefund() == 0) {
            return model;
        }
        String source = tbTrade.getSource();
        if ((CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(source) || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(source)) && tbTrade instanceof TbTrade) {
            return getRefundInfos(user, model, tbTradeAccess);
        }

        return model;
    }

    private static String maskBuyerAlipayNo(String buyerAlipayNo) {
        if (StringUtils.isEmpty(buyerAlipayNo))
            return buyerAlipayNo;

        return buyerAlipayNo.substring(0, 1) + "***";
    }

    /**
     * 全托管-揽收接口信息
     * smt temu
     */
    private PlatformTradeInfoDTO kjTradeExtInfo;

    public PlatformTradeInfoDTO getKjTradeExtInfo() {
        return kjTradeExtInfo;
    }

    public TradeModel setKjTradeExtInfo(PlatformTradeInfoDTO kjTradeExtinfo) {
        this.kjTradeExtInfo = kjTradeExtinfo;
        return this;
    }

    /**
     * 获取退款信息
     *
     * @param model
     * @return
     */
    private static TradeModel getRefundInfos(User user, TradeModel model, TbTradeAccess tbTradeAccess) {
        try {
            model.setRefunds(tbTradeAccess.getReceiveRefunds(user, model.getBuyerNick(), model.getOpenUid(), Long.parseLong(model.getTid()), model.getCreated()));
        } catch (Exception e) {
            Logger.getLogger(TradeModel.class).error(LogHelper.buildErrorLog(user, e, "查询退款信息出错"), e);
        }
        return model;
    }

    /**
     * 计算商品数量
     *
     * @param trade
     * @return
     */
    private static void calculateItemCount(Trade trade, TradeModel model) {
        if (trade.getItemNum() != null && trade.getItemNum() >= 0) {
            model.setItemCount(trade.getItemNum());
        }
        if (trade.getItemKindNum() != null && trade.getItemKindNum() >= 0) {
            model.setItemKindCount(trade.getItemKindNum());
        }
    }

    private static List<OrderModel> toBaseOrderModels(Staff staff, Trade tbTrade, TradeConfig tradeConfig) {
        List<OrderModel> list = new ArrayList<OrderModel>();
        List<Order> orders = TradeUtils.getOrders4Trade(tbTrade);
        for (Order tbOrder : orders) {
            list.add(OrderModel.toBaseOrderModel(staff, tbTrade, tbOrder, false, tradeConfig));
        }
        return list;
    }

    /**
     * @see TradeSortParams#toString()
     * @param order
     * @return
     */
    private static String getSortOuterId(Order order) {
        String outerId = StringUtils.defaultIfEmpty(order.getSysOuterId(), order.getOuterIid());
        if (StringUtils.isEmpty(outerId)){
            //排序到最后
            outerId = "zzzzzz";
        }
        return outerId;
    }

    /**
     * @see TradeSortParams#toString()
     * @param order
     * @return
     */
    private static String getSortOuterId(OrderModel order) {
        String outerId = StringUtils.defaultIfEmpty(order.getSysOuterId(), order.getOuterIid());
        if (StringUtils.isEmpty(outerId)){
            //排序到最后
            outerId = "zzzzzz";
        }
        return outerId;
    }

    public Map<String, Object> getHighlights() {
        return highlights;
    }

    public void setHighlights(Map<String, Object> highlights) {
        this.highlights = highlights;
    }

    public String getSid() {
        return sid;
    }

    public TradeModel setSid(String sid) {
        this.sid = sid;
        return this;
    }

    public String getShortId() {
        return shortId;
    }

    public TradeModel setShortId(String shortId) {
        this.shortId = shortId;
        return this;
    }

    public Shop getShop() {
        return shop;
    }

    public TradeModel setShop(Shop shop) {
        this.shop = shop;
        return this;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public TradeModel setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
        return this;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public TradeModel setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
        return this;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public TradeModel setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
        return this;
    }

    public String getSysMemo() {
        return sysMemo;
    }

    public TradeModel setSysMemo(String sysMemo) {
        this.sysMemo = sysMemo;
        return this;
    }

    public Integer getIsCancel() {
        return isCancel;
    }

    public TradeModel setIsCancel(Integer isCancel) {
        this.isCancel = isCancel;
        return this;
    }

    public String getStockStatus() {
        return stockStatus;
    }

    public TradeModel setStockStatus(String stockStatus) {
        this.stockStatus = stockStatus;
        return this;
    }

    public String getWarehouseStatus() {
        return warehouseStatus;
    }

    public TradeModel setWarehouseStatus(String warehouseStatus) {
        this.warehouseStatus = warehouseStatus;
        return this;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public TradeModel setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
        return this;
    }

    public Integer getIsUrgent() {
        return isUrgent;
    }

    public TradeModel setIsUrgent(Integer isUrgent) {
        this.isUrgent = isUrgent;
        return this;
    }

    public Integer getIsPackage() {
        return isPackage;
    }

    public TradeModel setIsPackage(Integer isPackage) {
        this.isPackage = isPackage;
        return this;
    }

    public String getSource() {
        return source;
    }

    public TradeModel setSource(String source) {
        this.source = source;
        return this;
    }

    public Integer getIsWeigh() {
        return isWeigh;
    }

    public TradeModel setIsWeigh(Integer isWeigh) {
        this.isWeigh = isWeigh;
        return this;
    }

    public String getWeight() {
        return weight;
    }

    public TradeModel setWeight(String weight) {
        this.weight = weight;
        return this;
    }

    public Long getUserId() {
        return userId;
    }

    public TradeModel setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public TradeModel setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
        return this;
    }

    public Set<String> getExceptions() {
        return exceptions;
    }

    public void setExceptions(Set<String> exceptions) {
        this.exceptions = exceptions;
    }

    public WaveAssembleInfo getWaveAssembleInfo() {
        return waveAssembleInfo;
    }

    public void setWaveAssembleInfo(WaveAssembleInfo waveAssembleInfo) {
        this.waveAssembleInfo = waveAssembleInfo;
    }

    public Date getExpressPrintTime() {
        return expressPrintTime;
    }

    public TradeModel setExpressPrintTime(Date expressPrintTime) {
        this.expressPrintTime = expressPrintTime;
        return this;
    }

    public Date getDeliverPrintTime() {
        return deliverPrintTime;
    }

    public TradeModel setDeliverPrintTime(Date deliverPrintTime) {
        this.deliverPrintTime = deliverPrintTime;
        return this;
    }

    public Integer getIsManulfill() {
        return isManulfill;
    }

    public TradeModel setIsManulfill(Integer isManulfill) {
        this.isManulfill = isManulfill;
        return this;
    }

    public String getBoxingList() {
        return boxingList;
    }

    public TradeModel setBoxingList(String boxingList) {
        this.boxingList = boxingList;
        return this;
    }

    public Integer getIsPresell() {
        return isPresell;
    }

    public TradeModel setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
        return this;
    }

    public String getPoNos() {
        return poNos;
    }

    public TradeModel setPoNos(String poNos) {
        this.poNos = poNos;
        return this;
    }

    public String getVipPickNo() {
        return vipPickNo;
    }

    public TradeModel setVipPickNo(String vipPickNo) {
        this.vipPickNo = vipPickNo;
        return this;
    }

    public String getVipStorageNo() {
        return vipStorageNo;
    }

    public TradeModel setVipStorageNo(String vipStorageNo) {
        this.vipStorageNo = vipStorageNo;
        return this;
    }

    public String getSplitSid() {
        return splitSid;
    }

    public TradeModel setSplitSid(String splitSid) {
        this.splitSid = splitSid;
        return this;
    }

    public Integer getMergeType() {
        return mergeType;
    }

    public TradeModel setMergeType(Integer mergeType) {
        this.mergeType = mergeType;
        return this;
    }

    public String getMergeSid() {
        return mergeSid;
    }

    public TradeModel setMergeSid(String mergeSid) {
        this.mergeSid = mergeSid;
        return this;
    }

    public Integer getSplitType() {
        return splitType;
    }

    public TradeModel setSplitType(Integer splitType) {
        this.splitType = splitType;
        return this;
    }

    public Integer getIsStore() {
        return isStore;
    }

    public TradeModel setIsStore(Integer isStore) {
        this.isStore = isStore;
        return this;
    }

    public List<TradePackSplit> getTradePackSplits() {
        return tradePackSplits;
    }

    public TradeModel setTradePackSplits(List<TradePackSplit> tradePackSplits) {
        this.tradePackSplits = tradePackSplits;
        return this;
    }

    public String getSubSource() {
        return subSource;
    }

    public TradeModel setSubSource(String subSource) {
        this.subSource = subSource;
        return this;
    }

    public String getReceiverCountry() {
        return receiverCountry;
    }

    public TradeModel setReceiverCountry(String receiverCountry) {
        this.receiverCountry = receiverCountry;
        return this;
    }

    public String getTaxFee() {
        return taxFee;
    }

    public TradeModel setTaxFee(String taxFee) {
        this.taxFee = taxFee;
        return this;
    }

    public Long getWaveId() {
        return waveId;
    }

    public TradeModel setWaveId(Long waveId) {
        this.waveId = waveId;
        return this;
    }

    public Long getWaveShortId() {
        return waveShortId;
    }

    public TradeModel setWaveShortId(Long waveShortId) {
        this.waveShortId = waveShortId;
        return this;
    }

    public Double getPackmaCost() {
        return packmaCost;
    }

    public TradeModel setPackmaCost(Double packmaCost) {
        this.packmaCost = packmaCost;
        return this;
    }

    public Integer getIsTmallDelivery() {
        return isTmallDelivery;
    }

    public TradeModel setIsTmallDelivery(Integer isTmallDelivery) {
        this.isTmallDelivery = isTmallDelivery;
        return this;
    }

    public String getTimingPromise() {
        return timingPromise;
    }

    public TradeModel setTimingPromise(String timingPromise) {
        this.timingPromise = timingPromise;
        return this;
    }

    public String getPromiseService() {
        return promiseService;
    }

    public TradeModel setPromiseService(String promiseService) {
        this.promiseService = promiseService;
        return this;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public TradeModel setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
        return this;
    }

    public String getPromiseConsignType() {
        return promiseConsignType;
    }

    public TradeModel setPromiseConsignType(String promiseConsignType) {
        this.promiseConsignType = promiseConsignType;
        return this;
    }

    public Date getCollectTime() {
        return collectTime;
    }

    public TradeModel setCollectTime(Date collectTime) {
        this.collectTime = collectTime;
        return this;
    }

    public Date getSignTime() {
        return signTime;
    }

    public TradeModel setSignTime(Date signTime) {
        this.signTime = signTime;
        return this;
    }

    public Integer getEsTime() {
        return esTime;
    }

    public TradeModel setEsTime(Integer esTime) {
        this.esTime = esTime;
        return this;
    }

    public String getPromiseArrivalType() {
        return promiseArrivalType;
    }

    public TradeModel setPromiseArrivalType(String promiseArrivalType) {
        this.promiseArrivalType = promiseArrivalType;
        return this;
    }

    public Integer getIsTmallPromise() {
        return isTmallPromise;
    }

    public TradeModel setIsTmallPromise(Integer isTmallPromise) {
        this.isTmallPromise = isTmallPromise;
        return this;
    }

    public String getPromiseMsg() {
        return promiseMsg;
    }

    public TradeModel setPromiseMsg(String promiseMsg) {
        this.promiseMsg = promiseMsg;
        return this;
    }


    public Integer getPrintCount() {
        return printCount;
    }

    public TradeModel setPrintCount(Integer printCount) {
        this.printCount = printCount;
        return this;
    }

    public Integer getLogisticsTraceCount() {
        return logisticsTraceCount;
    }

    public void setLogisticsTraceCount(Integer logisticsTraceCount) {
        this.logisticsTraceCount = logisticsTraceCount;
    }

    public String getLastLogisticsTrace() {
        return lastLogisticsTrace;
    }

    public void setLastLogisticsTrace(String lastLogisticsTrace) {
        this.lastLogisticsTrace = lastLogisticsTrace;
    }

    public Date getLastLogisticsTime() {
        return lastLogisticsTime;
    }

    public void setLastLogisticsTime(Date lastLogisticsTime) {
        this.lastLogisticsTime = lastLogisticsTime;
    }


    public Integer getSysConsigned() {
        return sysConsigned;
    }

    public TradeModel setSysConsigned(Integer sysConsigned) {
        this.sysConsigned = sysConsigned;
        return this;
    }

    public Integer getIsUpload() {
        return isUpload;
    }

    public TradeModel setIsUpload(Integer isUpload) {
        this.isUpload = isUpload;
        return this;
    }

    public String getUnifiedStatus() {
        return unifiedStatus;
    }

    public TradeModel setUnifiedStatus(String unifiedStatus) {
        this.unifiedStatus = unifiedStatus;
        return this;
    }

    public Integer getStockRegionType() {
        return stockRegionType;
    }

    public TradeModel setStockRegionType(Integer stockRegionType) {
        this.stockRegionType = stockRegionType;
        return this;
    }

    public Double getTradePurchaseAmount() {
        return tradePurchaseAmount;
    }

    public void setTradePurchaseAmount(Double tradePurchaseAmount) {
        this.tradePurchaseAmount = tradePurchaseAmount;
    }

    public List<TradePayVO> getTradePays() {
        return tradePays;
    }

    public void setTradePays(List<TradePayVO> tradePays) {
        this.tradePays = tradePays;
    }

    public Double getPlatformPaymentAmount() {
        return platformPaymentAmount;
    }

    public void setPlatformPaymentAmount(Double platformPaymentAmount) {
        this.platformPaymentAmount = platformPaymentAmount;
    }

    public Double getManualPaymentAmount() {
        return manualPaymentAmount;
    }

    public void setManualPaymentAmount(Double manualPaymentAmount) {
        this.manualPaymentAmount = manualPaymentAmount;
    }

    public String getMallMaskName() {
        return mallMaskName;
    }

    public void setMallMaskName(String mallMaskName) {
        this.mallMaskName = mallMaskName;
    }

    public String getMallMaskId() {
        return mallMaskId;
    }

    public void setMallMaskId(String mallMaskId) {
        this.mallMaskId = mallMaskId;
    }

    public String getFxTid() {
        return fxTid;
    }

    public void setFxTid(String fxTid) {
        this.fxTid = fxTid;
    }

    public String getSortString() {
        return sortString;
    }

    public TradeModel setSortString(String sortString) {
        this.sortString = sortString;
        return this;
    }

    public String getLogisticsName() {
        return logisticsName;
    }

    public void setLogisticsName(String logisticsName) {
        this.logisticsName = logisticsName;
    }

    public String getWrapperDescription() {
        return wrapperDescription;
    }

    public void setWrapperDescription(String wrapperDescription) {
        this.wrapperDescription = wrapperDescription;
    }

    public String getLogisticsCode() {
        return logisticsCode;
    }

    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    public String getOriginReceiverMobile() {
        return originReceiverMobile;
    }

    public void setOriginReceiverMobile(String originReceiverMobile) {
        this.originReceiverMobile = originReceiverMobile;
    }

    public String getOriginReceiverName() {
        return originReceiverName;
    }

    public void setOriginReceiverName(String originReceiverName) {
        this.originReceiverName = originReceiverName;
    }

    public String getOriginReceiverAddress() {
        return originReceiverAddress;
    }

    public void setOriginReceiverAddress(String originReceiverAddress) {
        this.originReceiverAddress = originReceiverAddress;
    }

    public String getShippingCarrier() {
        return shippingCarrier;
    }

    public void setShippingCarrier(String shippingCarrier) {
        this.shippingCarrier = shippingCarrier;
    }

    public Long getCombineParcelId() {
        return combineParcelId;
    }

    public void setCombineParcelId(Long combineParcelId) {
        this.combineParcelId = combineParcelId;
    }

    public List<TradeTagVo> getTagList() {
        return tagList;
    }

    public void setTagList(List<TradeTagVo> tagList) {
        this.tagList = tagList;
    }

    public String getCooperationNo() {
        return cooperationNo;
    }

    public void setCooperationNo(String cooperationNo) {
        this.cooperationNo = cooperationNo;
    }

    public String getSellSite() {
        return sellSite;
    }

    public void setSellSite(String sellSite) {
        this.sellSite = sellSite;
    }

    public String getPlatformAfterSaleTid() {
        return platformAfterSaleTid;
    }

    public void setPlatformAfterSaleTid(String platformAfterSaleTid) {
        this.platformAfterSaleTid = platformAfterSaleTid;
    }

    public String getPlatformAfterSaleIdSource() {
        return platformAfterSaleIdSource;
    }

    public void setPlatformAfterSaleIdSource(String platformAfterSaleIdSource) {
        this.platformAfterSaleIdSource = platformAfterSaleIdSource;
    }

    public String getThirdPlatTid() {
        return thirdPlatTid;
    }

    public void setThirdPlatTid(String thirdPlatTid) {
        this.thirdPlatTid = thirdPlatTid;
    }

    public Long getAssociatedUserId() {
        return associatedUserId;
    }

    public void setAssociatedUserId(Long associatedUserId) {
        this.associatedUserId = associatedUserId;
    }

    public String getPayAmount() {
        return payAmount;
    }

    public TradeModel setPayAmount(String payAmount) {
        this.payAmount = payAmount;
        return this;
    }

    public List<Long> getExceptIds() {
        return exceptIds;
    }

    public void setExceptIds(List<Long> exceptIds) {
        this.exceptIds = exceptIds;
    }

    public List<Long> getTypeIds() {
        return typeIds;
    }

    public void setTypeIds(List<Long> typeIds) {
        this.typeIds = typeIds;
    }

    /**
     * 智能计算订单单品数量
     */
    private Integer itemQuantity = 0;

    public Integer getItemQuantity() {
        return itemQuantity;
    }

    public void setItemQuantity(Integer itemQuantity) {
        this.itemQuantity = itemQuantity;
    }

    public String getDistributorId() {
        return distributorId;
    }

    public void setDistributorId(String distributorId) {
        this.distributorId = distributorId;
    }

    public String getDistributorName() {
        return distributorName;
    }

    public void setDistributorName(String distributorName) {
        this.distributorName = distributorName;
    }

    public String getOrderPayment() {
        return orderPayment;
    }

    public TradeModel setOrderPayment(String orderPayment) {
        this.orderPayment = orderPayment;
        return this;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getLogisticsCompanyName() {
        return logisticsCompanyName;
    }

    public void setLogisticsCompanyName(String logisticsCompanyName) {
        this.logisticsCompanyName = logisticsCompanyName;
    }

    public String getPrintBoxLabelStatus() {
        return printBoxLabelStatus;
    }

    public void setPrintBoxLabelStatus(String printBoxLabelStatus) {
        this.printBoxLabelStatus = printBoxLabelStatus;
    }

    public Long getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Long logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public List<SimplePackMiddleTrade> getNormalMiddleTrades() {
        return normalMiddleTrades;
    }

    public void setNormalMiddleTrades(List<SimplePackMiddleTrade> normalMiddleTrades) {
        this.normalMiddleTrades = normalMiddleTrades;
    }

    public List<SimplePackMiddleTrade> getErrorMiddleTrades() {
        return errorMiddleTrades;
    }

    public void setErrorMiddleTrades(List<SimplePackMiddleTrade> errorMiddleTrades) {
        this.errorMiddleTrades = errorMiddleTrades;
    }

    public Integer getNormalMiddleTradeOutSidCount() {
        return normalMiddleTradeOutSidCount;
    }

    public void setNormalMiddleTradeOutSidCount(Integer normalMiddleTradeOutSidCount) {
        this.normalMiddleTradeOutSidCount = normalMiddleTradeOutSidCount;
    }

    public Integer getNormalMiddleTradeItemCount() {
        return normalMiddleTradeItemCount;
    }

    public void setNormalMiddleTradeItemCount(Integer normalMiddleTradeItemCount) {
        this.normalMiddleTradeItemCount = normalMiddleTradeItemCount;
    }

    public Integer getNormalMiddleTradeItemKindCount() {
        return normalMiddleTradeItemKindCount;
    }

    public void setNormalMiddleTradeItemKindCount(Integer normalMiddleTradeItemKindCount) {
        this.normalMiddleTradeItemKindCount = normalMiddleTradeItemKindCount;
    }

    public String getShopSourceName() {
        return shopSourceName;
    }

    public TradeModel setShopSourceName(String shopSourceName) {
        this.shopSourceName = shopSourceName;
        return this;
    }

    public Boolean getAox() {
        return aox;
    }

    public void setAox(Boolean aox) {
        this.aox = aox;
    }

    public String getSubSourceName() {
        return subSourceName;
    }

    public TradeModel setSubSourceName(String subSourceName) {
        this.subSourceName = subSourceName;
        return this;
    }

    public void setGrossProfitDisplay(String grossProfitDisplay) {
        this.grossProfitDisplay = grossProfitDisplay;
    }

    public String getGrossProfitDisplay() {
        return grossProfitDisplay;
    }

    public String getSourceAliasName() {
        return sourceAliasName;
    }

    public void setSourceAliasName(String sourceAliasName) {
        this.sourceAliasName = sourceAliasName;
    }

    /**
     * 分销商简称
     */
    String sourceAliasName;

    /**
     * 是否已经送打 已送打为1 已打印为2
     */
    String printing;

    public String getPrinting() {
        return printing;
    }

    public void setPrinting(String printing) {
        this.printing = printing;
    }


    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public Long getRemainDeliveryTime() {
        return remainDeliveryTime;
    }

    public void setRemainDeliveryTime(Long remainDeliveryTime) {
        this.remainDeliveryTime = remainDeliveryTime;
    }

    public String getVipStatus() {
        if (!(CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(getSource())
                && CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(getSubSource()))) {
            return null;
        }
        if (StringUtils.isNotEmpty(vipStatus)) {
            return vipStatus;
        }
        if (StringUtils.isNotEmpty(getVipStorageNo())) {
            vipStatus = "已匹配出仓单";
        } else {
            if (Trade.SYS_STATUS_WAIT_SEND_GOODS.equals(getUnifiedStatus())) {
                vipStatus = "已下载";
            } else if (Trade.SYS_STATUS_FINISHED.equals(getUnifiedStatus())) {
                vipStatus = "已完成";
            }
        }
        return vipStatus;
    }

    public void setVipStatus(String vipStatus) {
        this.vipStatus = vipStatus;
    }

    /**
     * 剩余发货时间
     */
    private Long remainDeliveryTime;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 唯品会订单状态
     */
    private String vipStatus;


    public String getPurchaseBatch() {
        return purchaseBatch;
    }

    public void setPurchaseBatch(String purchaseBatch) {
        this.purchaseBatch = purchaseBatch;
    }

    /**
     * 备货批次
     */
    String purchaseBatch;

    /**
     * 商品编码打印状态 1.已打印 0.未打印
     */
    Integer itemPrintStatus;

    public Integer getItemPrintStatus() {
        return itemPrintStatus;
    }

    public void setItemPrintStatus(Integer itemPrintStatus) {
        this.itemPrintStatus = itemPrintStatus;
    }
    /**
     * 箱唛下载状态
     */
    private String boxDownloadStatus;

    public String getBoxDownloadStatus() {
        return boxDownloadStatus;
    }

    public void setBoxDownloadStatus(String boxDownloadStatus) {
        this.boxDownloadStatus = boxDownloadStatus;
    }


    /** 淘宝小时达业务 start */
    /**骑手名*/
    public String xsdDelivererName;
    /**运单小号*/
    public String xsdOrderNo;
    /**骑手电话*/
    public String xsdDelivererPhone;

    public String getXsdDelivererName() {
        return xsdDelivererName;
    }

    public void setXsdDelivererName(String xsdDelivererName) {
        this.xsdDelivererName = xsdDelivererName;
    }

    public String getXsdOrderNo() {
        return xsdOrderNo;
    }

    public void setXsdOrderNo(String xsdOrderNo) {
        this.xsdOrderNo = xsdOrderNo;
    }

    public String getXsdDelivererPhone() {
        return xsdDelivererPhone;
    }

    public void setXsdDelivererPhone(String xsdDelivererPhone) {
        this.xsdDelivererPhone = xsdDelivererPhone;
    }

    /** 淘宝小时达业务 end */

    public String getFxTotalPrice() {
        return fxTotalPrice;
    }

    public void setFxTotalPrice(String fxTotalPrice) {
        this.fxTotalPrice = fxTotalPrice;
    }

    public String getFxCommission() {
        return fxCommission;
    }

    public void setFxCommission(String fxCommission) {
        this.fxCommission = fxCommission;
    }

    public String getFxCost() {
        return fxCost;
    }

    public void setFxCost(String fxCost) {
        this.fxCost = fxCost;
    }

    /**
     * 分销金额 分销单才有
     */
    String fxTotalPrice;

    /**
     * 佣金 分销单才有
     */
    String fxCommission;
    /**
     * 分销成本 分销单才有
     */
    String fxCost;



    public String performanceType;

    public String getPerformanceType() {
        return performanceType;
    }

    public void setPerformanceType(String performanceType) {
        this.performanceType = performanceType;
    }
    /**
     * 国补信息
     */
    private GovSubsidyInfo govSubsidyInfo;

    public GovSubsidyInfo getGovSubsidyInfo() {
        return govSubsidyInfo;
    }

    public void setGovSubsidyInfo(GovSubsidyInfo govSubsidyInfo) {
        this.govSubsidyInfo = govSubsidyInfo;
    }
    /**
     * 包裹实际体积
     */
    private String actualVolume;

    /**
     * 包裹实际长宽高
     */
    private String actualLengthWidthAndHeight;

    public String getActualVolume() {
        return actualVolume;
    }

    public TradeModel setActualVolume(String actualVolume) {
        this.actualVolume = actualVolume;
        return this;
    }

    public String getActualLengthWidthAndHeight() {
        return actualLengthWidthAndHeight;
    }

    public TradeModel setActualLengthWidthAndHeight(String actualLengthWidthAndHeight) {
        this.actualLengthWidthAndHeight = actualLengthWidthAndHeight;
        return this;
    }

    /**
     * 平台仓
     */
    private String outStoreName;

    public String getOutStoreName() {
        return outStoreName;
    }

    public void setOutStoreName(String outStoreName) {
        this.outStoreName = outStoreName;
    }


    private String agedProductCode;//时效产品

    public String getAgedProductCode() {
        return agedProductCode;
    }

    public void setAgedProductCode(String agedProductCode) {
        this.agedProductCode = agedProductCode;
    }


    /**
     * 分销原始平台单号
     */
    private String fxPlatformTid;

    public String getFxPlatformTid() {
        return fxPlatformTid;
    }

    public void setFxPlatformTid(String fxPlatformTid) {
        this.fxPlatformTid = fxPlatformTid;
    }
    /**
     * 原始平台状态
     */
    private String platformStatusStr;

    public String getPlatformStatusStr() {
        return platformStatusStr;
    }

    public void setPlatformStatusStr(String platformStatusStr) {
        this.platformStatusStr = platformStatusStr;
    }

    /**
     * 拼多多集运类型
     */
    private Integer consolidateType;

    public Integer getConsolidateType() {
        return consolidateType;
    }

    public void setConsolidateType(Integer consolidateType) {
        this.consolidateType = consolidateType;
    }

    /**
     * 是否验货
     */
    String packageStatus;

    public String getPackageStatus() {
        return packageStatus;
    }

    public void setPackageStatus(String packageStatus) {
        this.packageStatus = packageStatus;
    }

    private String alibabaOfficialPickUp;

    public String getAlibabaOfficialPickUp() {
        return alibabaOfficialPickUp;
    }

    public void setAlibabaOfficialPickUp(String alibabaOfficialPickUp) {
        this.alibabaOfficialPickUp = alibabaOfficialPickUp;
    }
}

package com.raycloud.dmj.web.waves.controllers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.raycloud.dmj.ItemStaffRequest;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.SimplePackMiddleTrade;
import com.raycloud.dmj.business.wave.UniqueCodeHelpBusiness;
import com.raycloud.dmj.business.wave.WaveHelpBusiness;
import com.raycloud.dmj.business.wave.WavePackBusiness;
import com.raycloud.dmj.business.wave.WaveProgressBusiness;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.TradeValidator;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.AiPackmaOpEnum;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemConfig;
import com.raycloud.dmj.domain.item.ItemIdInfo;
import com.raycloud.dmj.domain.item.PackmaConsume;
import com.raycloud.dmj.domain.item.box.ItemBox;
import com.raycloud.dmj.domain.item.box.ItemBoxSingle;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLogDetail;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradePackParams;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.trades.vo.SimplePackma;
import com.raycloud.dmj.domain.trades.vo.SimplePackmaVO;
import com.raycloud.dmj.domain.trades.vo.WaveAssembleInfo;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.CompanyUtils;
import com.raycloud.dmj.domain.wave.Wave;
import com.raycloud.dmj.domain.wave.WaveRuleType;
import com.raycloud.dmj.domain.wave.WaveTrade;
import com.raycloud.dmj.domain.wave.WaveUniqueCode;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.enums.WaveChatConfigsEnum;
import com.raycloud.dmj.domain.wave.model.WaveTradeQueryParams;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.AllocateGoodsRecord;
import com.raycloud.dmj.domain.wms.LogisticsOrderDetail;
import com.raycloud.dmj.domain.wms.QueryAllocateGoodsRecordParams;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.domain.wms.enums.ContainerTypeEnum;
import com.raycloud.dmj.domain.wms.enums.WmsConfigExtInfoEnum;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.items.shipper.IShipperDubbo;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.response.ItemCatIdAndSellerCidsResponse;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.support.wave.business.TradeWavePackBusiness;
import com.raycloud.dmj.services.trades.support.wave.WaveLogisticsOrderService;
import com.raycloud.dmj.services.trades.wave.IItemUniqueCodeService;
import com.raycloud.dmj.services.trades.wave.IWaveConfigService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.services.trades.support.wave.CustomWorkloadSupplementService;
import com.raycloud.dmj.services.trades.ITradeItemPackService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.support.wave.WavePerformanceService;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.ResponseDataWrapperBuilder;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.dmj.web.waves.models.OpLogHelper;
import com.raycloud.dmj.web.waves.models.OrderModel;
import com.raycloud.dmj.web.waves.models.TradeModel;
import com.raycloud.dmj.web.waves.models.TradeModels;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @ClassName TradeWavePackController.java
 * @Description TODO
 * @createTime 2024年01月23日 11:27:00
 */
@Controller
@RequestMapping("/trade/wave")
public class TradeWavePackController extends WaveBaseController {

    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;
    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    private WaveProgressBusiness waveProgressBusiness;
    @Resource
    protected IEventCenter eventCenter;
    @Resource
    protected IOpLogService opLogService;
    @Resource
    private ITradeItemPackService tradeItemPackService;
    @Resource
    protected IShopService shopService;
    @Resource
    protected IExpressCompanyService expressCompanyService;
    @Resource
    private WavePackBusiness wavePackBusiness;
    @Resource
    private TradeWavePackBusiness tradeWavePackBusiness;
    @Resource
    private ITradeWaveService tradeWaveService;
    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;
    @Resource
    private IItemServiceDubbo itemsService;
    @Resource
    private IColumnConfService columnConfService;
    @Resource
    private IWmsService wmsService;
    @Resource
    private CustomWorkloadSupplementService customWorkloadSupplementService;
    @Resource
    private IShipperDubbo shipperDubbo;
    @Resource
    private WaveHelpBusiness waveHelpBusiness;
    @Resource
    private WavePerformanceService wavePerformanceService;
    @Resource
    private WaveLogisticsOrderService waveLogisticsOrderService;
    @Resource
    private IWaveConfigService waveConfigService;

    /**
     * 包装验货订单
     *
     * @param sids
     * @param orderIdIdentCodes
     * @param packmaOuterIds    包材商品的结构 商家编码:数量  [{"outerId":"abc", "amount":1}, {"outerId":"def", "amount":3}]
     * @param uniqueCodes       扫描唯一码
     * @param api_name
     * @return
     * @throws SessionException
     */
    @RequestMapping(value = "/pack")
    @ResponseBody
    public Object packTrades(String sids, Long waveId, String orderIdIdentCodes,
                             String orderScanInfos, String packmaOuterIds, boolean packSplit, Long combineParcelId,
                             Long staffId, String uniqueCodes, String api_name,
                             String scanWaveByBussineNo, Integer openPackAutoReceiveGoods) throws SessionException {
        Staff staff = getStaff();
        if (null != staffId) {
            staff = getStaffById(staffId);
            Assert.notNull(staff, "请选择正确的员工账号!");
            WaveUtils.validStaffCompany(getStaff(), staff);
            WaveConfig waveConfig = waveConfigService.get(staff);
            if (waveConfig != null && waveConfig.getInteger(WaveChatConfigsEnum.PACK_SUPPORT_SWITCH_ACCOUNT.getKey()) == 0) {
                Assert.isTrue(false, "页面已过期或配置已变更，请刷新页面重试！");
            }
        }
        waveId = getWaveIdByOutSid(waveId, scanWaveByBussineNo, staff);
        String ip = IpUtils.getClientIP(request);
        if (waveId != null && waveId > 0) {
            Object result = tradeServiceDubbo.packTradesWave(staff, waveId, ip);
            String content = "包装验货，波次号：" + waveId + "；具体包装验货人：" + staff.getName();
            OpLogHelper.recodeOpLog(opLogService, request, staff, "pack", null, content, null);
            return result;
        } else if (combineParcelId != null && combineParcelId > 0) {
            Object result = tradeServiceDubbo.packTradesCombineParcel(staff, combineParcelId, ip);
            String content = "包装验货，组包号：" + combineParcelId + "；具体包装验货人：" + staff.getName();
            OpLogHelper.recodeOpLog(opLogService, request, staff, "pack", null, content, null);
            return result;
        } else {
            Assert.isTrue(StringUtils.isNotEmpty(sids), "请输入快递单号并回车,并确认右侧有订单数据.");
            Assert.isTrue(StringUtils.isNotEmpty(orderIdIdentCodes), "请输入子订单与识别码的对应参数");
            List<Map<String, Object>> packmaOuterIdArr = getPackmaOuterIdArr(sids, packmaOuterIds);
            List<Order> orders = TradeUtils.parseIdentCodes(sids, orderIdIdentCodes);
            List<TradePackScanInfo> packScanInfos = TradeUtils.parsePackScanInfos(sids, orderScanInfos);
            TradePackParams params = new TradePackParams.Builder()
                    .sids(ArrayUtils.toLongArray(sids)).orders(orders).packScanInfos(packScanInfos)
                    .uniqueCodes(ArrayUtils.toStringList(uniqueCodes)).clientIp(IpUtils.getClientIP(request))
                    .packmaOuterIds(TradeUtils.handlePackmaOuterIds(sids, packmaOuterIds))
                    .builder();
            if (packSplit) {
                params.setPackSplit(true);
            }
            TradeStaffConfig tradeStaffConfig = tradeServiceDubbo.queryTradeStaffConfig(staff);
            params.setSuitPack(Objects.equals(tradeStaffConfig.getOpenSuitselfPack(), 1));
            params.setValidClosedStatus(true);
            params.setOpenPackAutoReceiveGoods(openPackAutoReceiveGoods);
            TradeWavePackResult result = tradeServiceDubbo.packTradesWithResult(staff, params);
            if (packmaOuterIdArr != null) {
                if (WaveUtils.checkNewPackma(packmaOuterIds)) {
                    eventCenter.fireEvent(this, new EventInfo("wave.new.packma.consume").setArgs(new Object[]{staff, Lists.newArrayList(Long.parseLong(sids)), packmaOuterIdArr,null, AiPackmaOpEnum.INSPECTION_MODIFY.getOpType()}), null);
                } else {
                    eventCenter.fireEvent(this, new EventInfo("trade.pack.packma.item").setArgs(new Object[]{staff, Long.parseLong(sids), packmaOuterIdArr, IpUtils.getClientIP(request)}), null);
                }
            }else {
                //开启新包材料，如果为空需要删除原有包材
                ItemConfig itemConfig = itemsService.queryItemConfigByCompanyId(staff.getCompanyId());
                Integer enablePackageItem = itemConfig.getEnablePackageItem();
                boolean enablePackageItemFlag = Integer.valueOf(1).equals(enablePackageItem);
                if (enablePackageItemFlag) {
                    eventCenter.fireEvent(this, new EventInfo("wave.new.packma.consume").setArgs(new Object[]{staff, Lists.newArrayList(Long.parseLong(sids)), new ArrayList<>(),null, AiPackmaOpEnum.INSPECTION_MODIFY.getOpType()}), null);
                }
            }
            String content = "包装验货，系统单号：" + sids + ",识别码信息:" + (orderIdIdentCodes == null ? "" : orderIdIdentCodes) + "；具体包装验货人：" + staff.getName();
            OpLogHelper.recodeOpLog(opLogService, request, getStaff(), "pack", null, content, null);
            if (result != null && result.getPackSplitPrintSid() != null) {
                return result;
            } else {
                return successResponse();
            }
        }
    }

    /**
     * 批量包装验货
     */
    @RequestMapping(value = "/batch/pack", method = RequestMethod.POST)
    @ResponseBody
    public Object packTrades(String sids, Long staffId, String packmaOuterIds, String api_name) throws SessionException {
        Staff staff = getStaff();
        if (waveProgressBusiness.hasProgress(staff, ProgressEnum.PROGRESS_WAVE_BATCH_PACK)) {
            throw new IllegalArgumentException("正在进行批量验货，请稍候再试！");
        }
        if (null != staffId) {
            staff = getStaffById(staffId);
            Assert.notNull(staff, "请选择正确的员工账号!");
            WaveConfig waveConfig = waveConfigService.get(staff);
            if (waveConfig != null && waveConfig.getInteger(WaveChatConfigsEnum.PACK_SUPPORT_SWITCH_ACCOUNT.getKey()) == 0) {
                Assert.isTrue(false, "页面已过期或配置已变更，请刷新页面重试！");
            }
        }
        Assert.notNull(sids, "请选择订单!");
        List<Long> sidList = ArrayUtils.toLongList(sids);

        waveProgressBusiness.setProgress(staff, ProgressEnum.PROGRESS_WAVE_BATCH_PACK, sidList.size());
        eventCenter.fireEvent(this, new EventInfo("trade.wave.batch.pack.pc").setArgs(new Object[]{staff, sidList, packmaOuterIds, IpUtils.getClientIP(request)}), null);
        return successResponse();
    }

    /**
     * 保存订单商品验货记录
     */
    @RequestMapping(value = "/itemPackInfo/save", method = RequestMethod.POST)
    @ResponseBody
    public Object saveTradeItemPackInfo(@RequestBody List<TradeItemPackLog> tradeItemPackLogs) throws Exception {
        Staff staff = getStaff();
        tradeItemPackService.update(staff, tradeItemPackLogs);
        return successResponse();
    }

    @RequestMapping(value = "/mix/count", method = RequestMethod.POST)
    @ResponseBody
    public Object queryPackCount(Integer queryType, Long staffId, String api_name) throws SessionException {
        Assert.notNull(queryType, "输入查询类型");
        Staff staff = getStaff();
        Map<String, Object> result = new HashMap<>();
        if (queryType == 1) {
            Integer packCount = wavePerformanceService.getPackNumByStaffId(staff, staffId);
            result.put("packCount", packCount);
            return result;
        }
        return result;
    }

    /**
     * 包装验货消费包材
     */
    @RequestMapping(value = "/pack/consume/packma", method = RequestMethod.POST)
    @ResponseBody
    public Object packConsumePackma(String sids, Long staffId, String packmaOuterIds, String api_name,Integer packMaOptSource) throws SessionException {
        Assert.isTrue(StringUtils.isNotEmpty(packmaOuterIds), "请传入包材编码");
        Assert.isTrue(StringUtils.isNotEmpty(sids), "请传入订单号");
        Staff staff = getStaff();
        if (null != staffId) {
            staff = getStaffById(staffId);
            Assert.notNull(staff, "请选择正确的员工账号!");
            WaveConfig waveConfig = waveConfigService.get(staff);
            if (waveConfig != null && waveConfig.getInteger(WaveChatConfigsEnum.PACK_SUPPORT_SWITCH_ACCOUNT.getKey()) == 0) {
                Assert.isTrue(false, "页面已过期或配置已变更，请刷新页面重试！");
            }
        }
        List<Long> sidList = ArrayUtils.toLongList(sids);
        tradeWavePackBusiness.consumePackma(staff, WavePackmaItemParam.builder().sidList(sidList).packmaOuterIds(packmaOuterIds).ip(IpUtils.getClientIP(request)).packMaOptSource(packMaOptSource).throwExcept(true).build());
        return successResponse();
    }

    @RequestMapping(value = "/pack/search", method = RequestMethod.GET)
    @ResponseBody
    public Object packSearch(TradeQueryParams params, Integer kind, Boolean force, String weight, Long staffId, String cpCode, String api_name) throws Exception {
        Staff staff = getStaff();
        if (params.getSysMask() != 2) {
            params.setSysMask(1);
        }

        Trade trade = wavePackBusiness.queryTrade(staff, params);
        TradeValidator validator = new TradeValidator();
        validator.setThrowExceptionIfError(false);
        validator.setCpCode(cpCode);
        // 判断订单是否存在
        if (ObjectUtils.isEmpty(trade)) {
            validator.setError(TradeValidator.Error.NOT_FOUND);
            Logs.error(LogHelper.buildLog(staff, validator.getMessage()));
            return ResponseDataWrapperBuilder.build(api_name, 0, validator.getMessage(), null).setSubCode(validator.getCode());
        }
        //扫码打印不需要忽略库存缺货异常
        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        WaveConfig waveConfig = waveConfigService.get(staff);
        TradeStaffConfig tradeStaffConfig = tradeServiceDubbo.queryTradeStaffConfig(staff);
        WmsConfig wmsConfig = wmsService.getConfig(staff);
        List<String> visibleColumnFieldList = columnConfService.getVisibleColumnFieldList(staff, 38);
        boolean supportSellerSend = WaveUtils.supportSellerSend(waveConfig, trade.getSysStatus());
        List<Order> closeOrders = getCloseOrderList(trade, tradeStaffConfig);
        validator.setIgnoreInsufficient(true);
        validator.setSupportSellerSend(supportSellerSend);
        checkPack(staff, trade, validator, tradeStaffConfig, waveConfig);
        // 商品详情新增拣货货位字段
        wavePackBusiness.fillOrderGoodsSectionCode(staff, Lists.newArrayList(trade), tradeStaffConfig, visibleColumnFieldList);
        wavePackBusiness.recordFailPackTradeTrace(staff, trade, validator, closeOrders);
        if (validator.hasError()) {
            Logs.error(LogHelper.buildLog(staff, validator.getMessage()));
            return ResponseDataWrapperBuilder.build(api_name, 0, validator.getMessage(), null).setSubCode(validator.getCode());
        }
        Trades trades = new Trades();
        trades.setTotal(1L).setList(Collections.singletonList(trade));

        TradeModels tradeModels = TradeModels.toTradeModels(staff, trades, shopService, expressCompanyService, false, null, tradeConfig);

        buildItemPackInfo(staff, tradeModels);
        //填充商品唯一码类型字段
        fillUniqueCodeTypeFiled(staff, tradeModels, wmsConfig);
        //根据配置填充商品类目名称
        fillItemCatName(staff, tradeModels, params.getCategoryVoice());
        // 填充批次/生产日期
        fillBatchNoAndProductDate(staff, params.getQueryId(), tradeModels, visibleColumnFieldList);
        fillPackItemInfo(staff, tradeModels, params, visibleColumnFieldList);
        // 打印已打印的订单时需要提示
        buildHintMessage(staff, trade, tradeModels);
        //根据权限设置字段展示
        handlePrivilegeShow(staff, tradeModels, params);
        // 填充退款子订单信息
        fillCloseOrdersInfo(staff, tradeModels, closeOrders, trade, tradeConfig);
        fillItemBox(staff, tradeStaffConfig, tradeModels);
        //填充波次信息
        fillWaveInfo(staff, tradeModels, params);
        //填充首尾单中间范围数据
        fillMiddleTrades(staff, params.getFirstSid(), tradeModels, tradeConfig);
        fillShipper(staff, tradeModels, params);
        fillMainOuterId(tradeModels, staff);
        //新版包材
        fillPackmaInfo(staff, tradeModels, params);
        return tradeModels;
    }

    @RequestMapping(value = "/combine/parcel/search", method = RequestMethod.GET)
    @ResponseBody
    public Object combineParcelSearch(Long combineParcelId, String api_name) throws Exception {
        Staff staff = getStaff();
        Assert.notNull(combineParcelId, "大包单号为空！");
        TradeCombineParcelDetail tradeCombineParcelDetail = tradeServiceDubbo.getTradeCombineParcelDetail(staff, combineParcelId);
        Assert.isTrue(tradeCombineParcelDetail != null && CollectionUtils.isNotEmpty(tradeCombineParcelDetail.getParcelDetails()), "大包单号不存在！");
        if (!Objects.equals(tradeCombineParcelDetail.getStatus(), 2)) {
            String errorMsg = Objects.equals(tradeCombineParcelDetail.getStatus(), 1) ? "请扫描已出库的大包，目前仅支持发货后验货的大包订单" : Objects.equals(tradeCombineParcelDetail.getStatus(), 3) ? "已取消大包不允许扫描" : "扫描大包非已出库状态";
            throw new IllegalArgumentException(errorMsg);
        }
        Integer combineParcelCount = customWorkloadSupplementService.countBySidAllStatus(staff, combineParcelId, 0);
        if (combineParcelCount > 0) {
            throw new IllegalArgumentException("大包不可重复扫描");
        }
        return tradeCombineParcelDetail;
    }

    private void fillPackmaInfo(Staff staff, TradeModels tradeModels, TradeQueryParams params) {
        ItemConfig itemConfig = itemsService.queryItemConfigByCompanyId(staff.getCompanyId());
        Integer enablePackageItem = itemConfig.getEnablePackageItem();
        boolean enablePackageItemFlag = Integer.valueOf(1).equals(enablePackageItem);
        if (!enablePackageItemFlag) {
            return;
        }
        if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        TradeModel tradeModel = tradeModels.getList().get(0);
        WaveAssembleInfo waveAssembleInfo = tradeModel.getWaveAssembleInfo() == null ? new WaveAssembleInfo() : tradeModel.getWaveAssembleInfo();

        Map<Long, List<SimplePackmaVO>> packmaMap = waveLogisticsOrderService.queryPackmaInfo(staff, Lists.newArrayList(Long.valueOf(tradeModel.getSid())));
        List<SimplePackma> simplePackmas = packmaMap.values().stream().flatMap(List::stream).map(logisticsOrderDetail -> {
            SimplePackma simplePackma = new SimplePackma();
            simplePackma.setSysItemId(logisticsOrderDetail.getSysItemId());
            simplePackma.setSysSkuId(logisticsOrderDetail.getSysSkuId());
            simplePackma.setOuterId(StringUtils.isNotEmpty(logisticsOrderDetail.getSkuOuterId()) ? logisticsOrderDetail.getSkuOuterId() : logisticsOrderDetail.getItemOuterId());
            simplePackma.setNum(logisticsOrderDetail.getNum());
            simplePackma.setName(logisticsOrderDetail.getName());
            return simplePackma;
        }).collect(toList());
        waveAssembleInfo.setSimplePackmas(simplePackmas);
        tradeModel.setWaveAssembleInfo(waveAssembleInfo);
    }


    private void fillMainOuterId(TradeModels tradeModels, Staff staff) {
        //查询订单行的主商家编码
        if (CollectionUtils.isNotEmpty(tradeModels.getList())&&waveHelpBusiness.isCheckedColumn(staff,WaveHelpBusiness.PACK_PAGE_ID,WaveHelpBusiness.MAIN_OUTER_ID_COLUMN_NAME)) {
            List<OrderModel> orders = tradeModels.getList().get(0).getOrders();
            List<Long> itemSysId = orders.stream().map(OrderModel::getItemSysId).filter(Objects::nonNull).distinct().collect(toList());
            if(CollectionUtils.isNotEmpty(itemSysId)){
                Map<Long, String> outerIdMap = waveHelpBusiness.queryByItemSysIds(staff, itemSysId).stream()
                        .filter(dmjItem -> dmjItem.getSysItemId() != null)
                        .collect(Collectors.toMap(DmjItem::getSysItemId,
                                dmjItem -> StringUtils.isEmpty(dmjItem.getOuterId()) ? "" : dmjItem.getOuterId(), (a, b) -> a
                        ));
                orders.forEach(order -> order.setMainOuterId(outerIdMap.get(order.getItemSysId())));
            }
        }
    }

    /**
     * 根据快递单号 获取波次信息
     *
     * @param waveId
     * @param outSid
     * @param staff
     * @return
     */
    private Long getWaveIdByOutSid(Long waveId, String outSid, Staff staff) {
        if (null != outSid) {
            List<Trade> trades = tradeSearchService.queryByOutSid(staff, outSid, false, null);
            Assert.notEmpty(trades, "根据快递单号未查询到订单！");
            Assert.isTrue(DataUtils.checkLongNotEmpty(trades.get(0).getWaveId()), "该快递单号未加入波次号！");
            return trades.get(0).getWaveId();
        }
        return waveId;
    }

    private List<Map<String, Object>> getPackmaOuterIdArr(String sids, String packmaOuterIds) {
        if (StringUtils.isEmpty(packmaOuterIds)) {
            return null;
        }
        try {
            if (sids.contains(",")) {
                throw new IllegalArgumentException("包材商品扫描不允许跨多笔订单");
            }
            return JSON.parseObject(packmaOuterIds, new TypeReference<List<Map<String, Object>>>() {
            });
        } catch (Exception e) {
            throw new IllegalArgumentException("包材商品格式有问题.");
        }
    }

    private List<Order> getCloseOrderList(Trade trade, TradeStaffConfig tradeStaffConfig) {
        if (trade == null || CollectionUtils.isEmpty(TradeUtils.getOrders4Trade(trade))) {
            return new ArrayList<>();
        }

        List<Order> closeOrders = TradeUtils.getOrders4Trade(trade).stream().filter(t -> Objects.equals(Trade.SYS_STATUS_CLOSED, t.getSysStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(closeOrders)) {
            return new ArrayList<>();
        }

        if (Objects.equals(tradeStaffConfig.getOpenSuitselfPack(), CommonConstants.VALUE_YES)) {
            return closeOrders;
        }

        return OrderUtils.toEffectiveOrders(closeOrders);
    }

    private void checkPack(Staff staff, Trade trade, TradeValidator validator, TradeStaffConfig tradeStaffConfig, WaveConfig waveConfig) {
        wavePackBusiness.checkPack(staff, trade, validator, Objects.equals(waveConfig.getInteger(WaveChatConfigsEnum.ALLOW_UNPRINT_PACK.getKey()), 1));
        tradeWaveService.checkTradePickStatus(staff, trade, waveConfig, validator);
        if (!validator.hasError()) {
            wavePackBusiness.filterOrders(trade, Objects.equals(tradeStaffConfig.getOpenSuitselfPack(), 1), validator.isSupportSellerSend());
            wavePackBusiness.setBarcode(staff, trade);
        }
    }

    public void buildItemPackInfo(Staff staff, TradeModels models) {
        List<TradeModel> tradeModels = models.getList();
        if (CollectionUtils.isNotEmpty(tradeModels)) {
            for (TradeModel tradeModel : tradeModels) {
                TradeItemPackLog tradeItemPackLog = new TradeItemPackLog();
                tradeItemPackLog.setCompanyId(staff.getCompanyId());
                tradeItemPackLog.setSid(Long.valueOf(tradeModel.getSid()));
                List<TradeItemPackLog> logs = tradeItemPackService.queryById(staff, tradeItemPackLog);
                Map<String, TradeItemPackLog> tradeItemPackLogMap = logs.stream().collect(Collectors.toMap(pack -> (pack.getCompanyId() + "_" + pack.getSid() + "_" + pack.getOrderId() + "_" + pack.getSysItemId() + "_" + pack.getSysSkuId()), java.util.function.Function.identity(), (k1, k2) -> k2));
                List<OrderModel> orderModels = tradeModel.getOrders();
                for (OrderModel orderModel : orderModels) {
                    String key = staff.getCompanyId() + "_" + tradeItemPackLog.getSid() + "_" + orderModel.getId() + "_" + orderModel.getItemSysId() + "_" + orderModel.getSkuSysId();
                    logger.debug(LogHelper.buildLog(staff, "order key：" + key));
                    TradeItemPackLog log = tradeItemPackLogMap.get(key);
                    if (log != null) {
                        if (StringUtils.isNotEmpty(log.getUniqueCodes())) {
                            String[] uniqueCodes = ArrayUtils.toStringArray(log.getUniqueCodes());
                            List<WaveUniqueCode> codes = itemUniqueCodeService.checkAndFilterScanUniqueCodes(staff, Arrays.asList(uniqueCodes));
                            orderModel.setCurrNum(log.getPackNum() - (uniqueCodes.length - codes.size()));
                            orderModel.setUniqueCodes(codes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.joining(",")));
                        } else {
                            orderModel.setCurrNum(log.getPackNum());
                        }
                    } else {
                        orderModel.setCurrNum(0);
                    }
                }
                tradeModel.setCurrNum(orderModels.stream().mapToInt(OrderModel::getCurrNum).sum());

                orderModels = orderModels.stream().sorted((pre, next) -> {
                    Integer preNum = pre.getNum() - pre.getCurrNum();
                    Integer nextNum = next.getNum() - next.getCurrNum();
                    return nextNum.compareTo(preNum);
                }).collect(Collectors.toList());
                tradeModel.setOrders(orderModels);
            }
        }
    }

    private void fillUniqueCodeTypeFiled(Staff staff, TradeModels tradeModels, WmsConfig wmsConfig) {
        if (CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        if (wmsConfig == null || !Objects.equals(wmsConfig.getInteger(WmsConfigExtInfoEnum.OPEN_ITEM_UNIQUE_CODE.getKey()),1)||!(staff.getConf() != null && staff.getConf().openOrderUniqueCode())) {
            return;
        }
        if (CollectionUtils.isNotEmpty(tradeModels.getList())) {
            List<OrderModel> orders = tradeModels.getList().get(0).getOrders();
            List<Long> sysSkuIds = orders.stream().map(OrderModel::getSkuSysId).distinct().collect(toList());
            List<Long> sysItemIds = orders.stream().map(OrderModel::getItemSysId).distinct().collect(toList());
            //找出强唯一码类型的商品
            Map<Integer,List<String>> uniqueCodeTypeMap = wavePackBusiness.uniqueCodeTypeMap(staff, sysItemIds, sysSkuIds);
            List<String> outerIdList = CollectionUtils.isNotEmpty(uniqueCodeTypeMap.get(1)) ? uniqueCodeTypeMap.get(1) : new ArrayList<>();
            List<String> weakOuterIdList = CollectionUtils.isNotEmpty(uniqueCodeTypeMap.get(0)) ? uniqueCodeTypeMap.get(0) : new ArrayList<>();
            List<String> universalCodeList = CollectionUtils.isNotEmpty(uniqueCodeTypeMap.get(2)) ? uniqueCodeTypeMap.get(2) : new ArrayList<>();

            tradeModels.setUniqueCodeItemList(outerIdList);
            tradeModels.setWeakUniqueCodeItemList(weakOuterIdList);
            tradeModels.setUniversalCodeTypeItemList(universalCodeList);
        }
    }

    private void fillItemCatName(Staff staff, TradeModels tradeModels, Integer categoryVoice) {
        if (!Objects.equals(categoryVoice, CommonConstants.VALUE_YES) || tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        List<String> sysOuterIds = new ArrayList<>();
        for (TradeModel model : tradeModels.getList()) {
            if (model == null || CollectionUtils.isEmpty(model.getOrders())) {
                continue;
            }
            sysOuterIds.addAll(model.getOrders().stream().map(OrderModel::getSysOuterId).collect(Collectors.toList()));
        }
        ItemCatIdAndSellerCidsResponse response = itemsService.queryItemCatIdAndSellerCids(ItemStaffRequest.buildStaffRequest(staff), sysOuterIds);
        if (response == null || CollectionUtils.isEmpty(response.getItemList())) {
            return;
        }

        List<ItemCatIdAndSellerCidsResponse.SimpleItem> itemList = response.getItemList();
        Map<String, ItemCatIdAndSellerCidsResponse.SimpleItem> map = itemList.stream().collect(Collectors.toMap(ItemCatIdAndSellerCidsResponse.SimpleItem::getOuterId, v -> v, (v1, v2) -> v2));
        for (TradeModel model : tradeModels.getList()) {
            for (OrderModel order : model.getOrders()) {
                ItemCatIdAndSellerCidsResponse.SimpleItem simpleItem = map.get(order.getSysOuterId());
                if (null != simpleItem) {
                    order.setItemCatName(simpleItem.getItemCatName());
                }
            }
        }
    }

    private void fillBatchNoAndProductDate(Staff staff, Long queryId, TradeModels tradeModels, List<String> visibleColumnFieldList) {
        if (!Objects.equals(queryId, 33L) || tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        List<OrderModel> orders = tradeModels.getList().get(0).getOrders();
        if (CollectionUtils.isEmpty(orders) || !WmsUtils.isNewWms(staff)) {
            return;
        }
        if (CollectionUtils.isEmpty(visibleColumnFieldList) || (!visibleColumnFieldList.contains("batchNos") && !visibleColumnFieldList.contains("productDates"))) {
            return;
        }
        try {
            List<Long> orderIds = new ArrayList<>();
            Set<String> outerIds = new HashSet<>();
            for (OrderModel order : orders) {
                orderIds.add(Long.valueOf(order.getId()));
                if (CollectionUtils.isEmpty(order.getSuits())) {
                    outerIds.add(order.getOuterId());
                } else {
                    outerIds.addAll(order.getSuits().stream().map(OrderModel::getOuterId).collect(Collectors.toSet()));
                }
            }
            Map<String, Object> itemMap = itemsService.queryItemSkuByOuterId(staff, Lists.newArrayList(outerIds), 12, null);
            Map<Long, List<AllocateGoodsRecord>> recordMap = wmsService.queryAllocateGoodsRecords(staff, new QueryAllocateGoodsRecordParams.Builder()
                            .orderIds(orderIds).containerTypes(Lists.newArrayList(ContainerTypeEnum.GOODS_SECTION.getValue(), ContainerTypeEnum.WORKING_STORAGE_SECTION.getValue())).build())
                    .stream().collect(Collectors.groupingBy(AllocateGoodsRecord::getOrderId));

            fillOrderModels(orders, itemMap, recordMap);
            tradeModels.getList().get(0).setOrders(orders);
        } catch (Exception e) {
            if (logger.isDebugEnabled()) {
                logger.error(LogHelper.buildLog(staff, "包装验货填充批次/生产日期失败"), e);
            }
        }
    }

    private void fillOrderModels(List<OrderModel> orders, Map<String, Object> itemMap, Map<Long, List<AllocateGoodsRecord>> recordMap) {
        for (OrderModel order : orders) {
            if (CollectionUtils.isEmpty(order.getSuits())) {
                convertObjects(order, itemMap);
                fillBatchNoAndProductDateInfo(order, recordMap);
            } else {
                for (OrderModel suit : order.getSuits()) {
                    convertObjects(suit, itemMap);
                    fillBatchNoAndProductDateInfo(suit, recordMap);
                }
            }
        }
    }

    private void convertObjects(OrderModel order, Map<String, Object> itemMap) {
        Object obj = itemMap.get(order.getOuterId());
        if (obj == null) {
            return;
        }

        if (obj instanceof DmjSku) {
            order.setHasBatch(((DmjSku) obj).getHasBatch());
            order.setHasProduct(((DmjSku) obj).getHasProduct());
        } else if (obj instanceof DmjItem) {
            order.setHasBatch(((DmjItem) obj).getHasBatch());
            order.setHasProduct(((DmjItem) obj).getHasProduct());
        }
    }

    public void fillBatchNoAndProductDateInfo(OrderModel orderModel, Map<Long, List<AllocateGoodsRecord>> recordMap) {
        if (recordMap == null || recordMap.isEmpty()) {
            return;
        }
        List<AllocateGoodsRecord> records = recordMap.get(Long.valueOf(orderModel.getId()));
        if (CollectionUtils.isNotEmpty(records)) {
            Map<String, List<AllocateGoodsRecord>> batchNoMap = records.stream().filter(r -> StringUtils.isNotEmpty(r.getBatchNo())).collect(Collectors.groupingBy(AllocateGoodsRecord::getBatchNo));
            Map<String, List<AllocateGoodsRecord>> productTimeMap = records.stream().filter(r -> r.getProductTime() != null).collect(Collectors.groupingBy(v -> DateUtils.date2Str(v.getProductTime())));
            if (batchNoMap != null && !batchNoMap.isEmpty()) {
                List<String> batchNos = new ArrayList<>();
                for (Map.Entry<String, List<AllocateGoodsRecord>> entry : batchNoMap.entrySet()) {
                    batchNos.add(entry.getKey() + " * " + entry.getValue().stream().mapToInt(AllocateGoodsRecord::getAllocatedNum).sum());
                }
                orderModel.setBatchNos(batchNos);
            }
            if (productTimeMap != null && !productTimeMap.isEmpty()) {
                List<String> productDates = new ArrayList<>();
                for (Map.Entry<String, List<AllocateGoodsRecord>> entry : productTimeMap.entrySet()) {
                    productDates.add(entry.getKey() + " * " + entry.getValue().stream().mapToInt(AllocateGoodsRecord::getAllocatedNum).sum());
                }
                orderModel.setProductDates(productDates);
            }
        }
    }

    private void fillPackItemInfo(Staff staff, TradeModels tradeModels, TradeQueryParams params, List<String> visibleColumnFieldList) {
        if (!Objects.equals(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_PACK)) {
            return;
        }
        if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        if (CollectionUtils.isEmpty(visibleColumnFieldList) || (!visibleColumnFieldList.contains("itemPrice") || !visibleColumnFieldList.contains("barcode"))) {
            return;
        }
        List<String> sysOuterIds = tradeModels.getList().stream().flatMap(data -> Optional.ofNullable(data.getOrders()).orElse(Lists.newArrayList()).stream())
                .map(OrderModel::getSysOuterId).collect(toList());
        if (CollectionUtils.isEmpty(sysOuterIds)) {
            return;
        }
        Map<String, Object> itemMap = WmsUtils.toLowerCase(itemsService.queryItemSkuByOuterId(staff, sysOuterIds, 12));
        for (TradeModel model : tradeModels.getList()) {
            if (CollectionUtils.isEmpty(model.getOrders())) {
                continue;
            }
            for (OrderModel order : model.getOrders()) {
                if (order.getOuterId() == null) { // 商品未下载成功的情况下outerId会为null
                    continue;
                }
                Object item = itemMap.get(order.getOuterId().toLowerCase());
                if (item == null) {
                    continue;
                }
                if (item instanceof DmjSku) {
                    DmjSku sku = (DmjSku) item;
                    order.setItemPrice(sku.getPriceOutput());
                    // 规格商品取规格条形码
                    order.setBarcode(sku.getSkuBarcode());
                } else {
                    DmjItem dmjItem = (DmjItem) item;
                    order.setItemPrice(dmjItem.getPriceOutput());
                    // 纯商品取商品条形码
                    order.setBarcode(dmjItem.getBarcode());
                }
            }
        }
    }

    private void buildHintMessage(Staff staff, Trade trade, TradeModels tradeModels) {
        // 读取配置,为1的时候才提示,这个是在语音提醒包配置中,开已打印直接补打的配置
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (waveConfig != null && Objects.equals(waveConfig.getInteger(WaveChatConfigsEnum.OPEN_PRINT_VOICE_HINTS.getKey()), 1) &&
                trade.getExpressPrintTime() != null && trade.getExpressPrintTime().compareTo(TradeTimeUtils.INIT_DATE) > 0) {
            tradeModels.setHintMessage("该订单已打印");
        }
    }

    private void handlePrivilegeShow(Staff staff, TradeModels tradeModels, TradeQueryParams params) {
        String powerDataPrivilegeSettings = staff.getPowerDataPrivilegeSettings();
        if (!Objects.equals(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_PACK)) {
            return;
        }
        if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        Set<String> dataPrivilegeSettingSet = ArrayUtils.toStringSet(powerDataPrivilegeSettings);
        //数据权限-包装验货-成交金额
        if (CollectionUtils.isEmpty(dataPrivilegeSettingSet) || !dataPrivilegeSettingSet.contains("100322")) {
            for (TradeModel tradeModel : tradeModels.getList()) {
                tradeModel.setPayAmount("***");
                if (CollectionUtils.isEmpty(tradeModel.getOrders())) {
                    continue;
                }
                for (OrderModel order : tradeModel.getOrders()) {
                    order.setAcPayment("***");
                }
            }
        }
    }

    private void fillCloseOrdersInfo(Staff staff, TradeModels models, List<Order> closeOrders, Trade trade, TradeConfig tradeConfig) {
        if (CollectionUtils.isEmpty(closeOrders) || models == null || CollectionUtils.isEmpty(models.getList())) {
            return;
        }

        List<OrderModel> orderModels = new ArrayList<>();
        for (Order closeOrder : closeOrders) {
            OrderModel orderModel = OrderModel.toBaseOrderModel(staff, trade, closeOrder, false, tradeConfig);
            orderModels.add(orderModel);
        }

        models.getList().get(0).setCloseOrders(orderModels);
    }

    private void fillItemBox(Staff staff, TradeStaffConfig tradeStaffConfig, TradeModels tradeModels) {
        //开启扫描箱规码，查询直接返回商品对应的箱规码，后面单独扫码就不用再掉接口查询了
        if (!Objects.equals(tradeStaffConfig.getOpenBoxCodeScan(), CommonConstants.JUDGE_YES)) {
            return;
        }
        List<ItemIdInfo> itemIdInfoList = new ArrayList<>();
        List<TradeModel> list = tradeModels.getList();
        for (TradeModel tradeModel : list) {
            List<OrderModel> orders = tradeModel.getOrders();
            for (OrderModel order : orders) {
                ItemIdInfo itemIdInfo = new ItemIdInfo();
                itemIdInfo.setSysItemId(order.getItemSysId());
                itemIdInfo.setSysSkuId(order.getSkuSysId() < 0L ? 0L : order.getSkuSysId());
                itemIdInfoList.add(itemIdInfo);
            }
        }

        if (CollectionUtils.isEmpty(itemIdInfoList)) {
            return;
        }
        List<ItemBox> itemBoxes = itemsService.queryItemBox(staff, itemIdInfoList);
        if (CollectionUtils.isEmpty(itemBoxes)) {
            return;
        }

        Map<String, List<Map<String, Object>>> itemCodeMap = new HashMap<>();
        for (ItemBox itemBox : itemBoxes) {
            List<ItemBoxSingle> itemBoxSingles = itemBox.getItemBoxSingles();
            List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
            for (ItemBoxSingle single : itemBoxSingles) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("num", single.getNums());
                map.put("outerId", single.getOuterId());
                map.put("sysItemId", single.getSysItemId());
                map.put("sysSkuId", single.getSysSkuId());
                result.add(map);
            }
            itemCodeMap.put(itemBox.getBoxCode(), result);
        }
        tradeModels.setItemCodeMap(itemCodeMap);
    }

    private void fillWaveInfo(Staff staff, TradeModels tradeModels, TradeQueryParams params) {
        if (!Objects.equals(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_PACK)) {
            return;
        }
        if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        List<TradeModel> waveTradeModelList = tradeModels.getList().stream().filter(t -> t.getWaveId() != null && t.getWaveId() > 0L).collect(toList());
        if (CollectionUtils.isEmpty(waveTradeModelList)) {
            return;
        }
        List<Wave> waves = tradeWaveService.queryWaveByIds(staff, waveTradeModelList.stream().map(TradeModel::getWaveId).toArray(Long[]::new));
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }
        Map<Long, Wave> waveMap = waves.stream().collect(Collectors.toMap(Wave::getId, w -> w, (k1, k2) -> k1));
        for (TradeModel tradeModel : waveTradeModelList) {
            Wave wave = waveMap.get(tradeModel.getWaveId());
            if (wave == null) {
                continue;
            }
            WaveAssembleInfo waveAssembleInfo = tradeModel.getWaveAssembleInfo() == null ? new WaveAssembleInfo() : tradeModel.getWaveAssembleInfo();
            if (StringUtils.isEmpty(wave.getRuleName())) {
                WaveRuleType waveRuleType = WaveRuleType.parseRuleId(wave.getRuleId());
                if (waveRuleType != WaveRuleType.TRADE) {
                    wave.setRuleName(waveRuleType.getName());
                }
                WaveUtils.fillCheckedTradeWaveRuleName(wave, waveRuleType);
            }
            waveAssembleInfo.setWaveRuleName(wave.getRuleName());
            tradeModel.setWaveAssembleInfo(waveAssembleInfo);
        }
    }

    private void fillMiddleTrades(Staff staff, Long firstSid, TradeModels tradeModels, TradeConfig tradeConfig) {
        if (firstSid == null || firstSid <= 0L || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        TradeStaffConfig tradeStaffConfig = tradeServiceDubbo.queryTradeStaffConfig(staff);
        TradeModel currentTradeModel = tradeModels.getList().get(0);
        Long currentSid = Long.valueOf(tradeModels.getList().get(0).getSid());
        if (currentTradeModel.getWaveId() == null || currentTradeModel.getWaveId() <= 0L) {
            throw new IllegalArgumentException("验货订单非波次订单");
        }
        WaveTradeQueryParams waveTradeQueryParams = new WaveTradeQueryParams();
        waveTradeQueryParams.setWaveId(currentTradeModel.getWaveId());
        List<WaveTrade> waveTrades = tradeWaveService.queryWaveTradeByParams(staff, waveTradeQueryParams);
        List<WaveTrade> middleWaveTrades = Lists.newArrayList();
        // 首单和尾单的扫描顺序，可能为正序，也可能为倒序，取中间部分即可
        boolean startAdd = false;
        for (WaveTrade data : waveTrades) {
            if (startAdd) {
                middleWaveTrades.add(data);
            }
            if (Objects.equals(firstSid, data.getSid()) || Objects.equals(currentSid, data.getSid())) {
                startAdd = !startAdd;
                if (startAdd) {
                    middleWaveTrades.add(data);
                }
                if (Objects.equals(firstSid, currentSid)) {
                    break;
                }
            }
        }
        Assert.notEmpty(middleWaveTrades, "订单未进入波次，不允许批量验货");
        Map<Long, Long> positionNoMap = middleWaveTrades.stream().collect(Collectors.toMap(WaveTrade::getSid, t -> DataUtils.getZeroIfDefault(t.getPositionNo()), (k1, k2) -> k1));
        Long[] sids = middleWaveTrades.stream().map(WaveTrade::getSid).toArray(Long[]::new);
        Assert.isTrue(sids.length <= 3000, "验货的订单超过3000单，请缩小范围后再验货");
        List<Trade> middleTrades = tradeSearchService.queryBySids(staff, true, sids);

        List<SimplePackMiddleTrade> normalPackTrades = new ArrayList<>();
        List<SimplePackMiddleTrade> errorPackTrades = new ArrayList<>();
        List<Long> cancelInsufficientSidList = new ArrayList<>();
        Map<String, Integer> itemCountMap = new HashMap<>();
        for (Trade middleTrade : middleTrades) {
            try {
                TradeValidator validator = new TradeValidator();
                validator.setThrowExceptionIfError(false);
                validator.setIgnoreInsufficient(true);
                wavePackBusiness.checkBatchPack(staff, middleTrade, validator, tradeStaffConfig);
                SimplePackMiddleTrade simplePackMiddleTrade = SimplePackMiddleTrade.buildFieldsFromTrade(middleTrade, tradeConfig);
                if (positionNoMap.containsKey(middleTrade.getSid())) {
                    simplePackMiddleTrade.setPositionNo(positionNoMap.get(middleTrade.getSid()));
                }
                if (validator.hasError()) {
                    errorPackTrades.add(simplePackMiddleTrade);
                    simplePackMiddleTrade.setErrorPackReason(validator.getMessage());
                } else {
                    normalPackTrades.add(simplePackMiddleTrade);
                    List<Order> orders = TradeUtils.getOrders4Trade(middleTrade);
                    for (Order order : orders) {
                        String key = order.getItemSysId() + "_" + (order.getSkuSysId() < 0L ? 0L : order.getSkuSysId());
                        if (itemCountMap.containsKey(key)) {
                            itemCountMap.put(key, itemCountMap.get(key) + order.getNum());
                        } else {
                            itemCountMap.put(key, order.getNum());
                        }
                    }
                }
                //只有缺货异常，检查是否需要取消缺货异常
                if (!validator.hasError() && validator.isCancelInsufficient()) {
                    cancelInsufficientSidList.add(middleTrade.getSid());
                }
            } catch (Exception e) {
                throw new IllegalArgumentException(String.format("%s，订单号:%s", e.getMessage(), middleTrade.getSid()));
            }
        }
        //一单多包
        List<String> normalOutSidList = normalPackTrades.stream().map(SimplePackMiddleTrade::getOutSid).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, List<MultiPacksPrintTradeLogDetail>> outSidMultiPackMap = wavePackBusiness.queryPrintLogDetailByOutSids(staff, middleTrades);
        int multiPackOustSidNum = 0;
        if (MapUtils.isNotEmpty(outSidMultiPackMap)) {
            for (String normalOutSid : normalOutSidList) {
                List<MultiPacksPrintTradeLogDetail> multiPacksPrintTradeLogDetails = outSidMultiPackMap.get(normalOutSid);
                if (CollectionUtils.isNotEmpty(multiPacksPrintTradeLogDetails)) {
                    multiPackOustSidNum += multiPacksPrintTradeLogDetails.size() - 1;//一单多包除去主单的数量
                }
            }
            for (SimplePackMiddleTrade errorPackTrade : errorPackTrades) {
                List<MultiPacksPrintTradeLogDetail> multiPacksPrintTradeLogDetails = outSidMultiPackMap.get(errorPackTrade.getOutSid());
                if (CollectionUtils.isNotEmpty(multiPacksPrintTradeLogDetails)) {
                    errorPackTrade.setOutSid(Joiner.on(";").join(multiPacksPrintTradeLogDetails.stream().map(MultiPacksPrintTradeLogDetail::getOutSid).collect(Collectors.toList())));
                }
            }
        }

        normalPackTrades.sort(Comparator.comparing(SimplePackMiddleTrade::getPositionNo).thenComparing(SimplePackMiddleTrade::getSid));
        errorPackTrades.sort(Comparator.comparing(SimplePackMiddleTrade::getPositionNo).thenComparing(SimplePackMiddleTrade::getSid));

        currentTradeModel.setNormalMiddleTrades(normalPackTrades);
        currentTradeModel.setErrorMiddleTrades(errorPackTrades);
        currentTradeModel.setNormalMiddleTradeOutSidCount(normalPackTrades.size() + multiPackOustSidNum);
        currentTradeModel.setNormalMiddleTradeItemCount(itemCountMap.values().stream().mapToInt(Integer::intValue).sum());
        currentTradeModel.setNormalMiddleTradeItemKindCount(itemCountMap.keySet().size());

        checkBatchPackTrades(currentTradeModel, firstSid, middleWaveTrades, middleTrades);
        // 取消缺货异常
        cancelInsufficient(staff, cancelInsufficientSidList);
    }

    /**
     * 校验批量包装验货
     */
    private void checkBatchPackTrades(TradeModel model, Long preSid, List<WaveTrade> waveTrades, List<Trade> allMiddleTrades) {

        if (!waveTrades.stream().map(WaveTrade::getSid).collect(Collectors.toList()).contains(preSid)) {
            throw new IllegalArgumentException("波次号不同，不允许批量验货");
        }

        List<Order> orders = allMiddleTrades.stream()
                .filter(trade -> Objects.equals(trade.getSid(), preSid))
                .findFirst().map(TradeUtils::getOrders4Trade)
                .orElse(Lists.newArrayList());
        List<OrderModel> currentOrderModels = model.getOrders();
        if (CollectionUtils.isEmpty(orders) || orders.size() != currentOrderModels.size()) {
            throw new IllegalArgumentException("首单和尾单的商品和数量必须完全相同");
        }
        Map<String, List<Order>> skuMap = orders.stream().collect(Collectors.groupingBy(order -> order.getItemSysId() + "_" + (order.getSkuSysId() < 0L ? 0L : order.getSkuSysId()) + "_" + order.getNum()));
        Map<String, List<OrderModel>> currentSkuMap = currentOrderModels.stream().collect(Collectors.groupingBy(sku -> sku.getItemSysId() + "_" + (sku.getSkuSysId() < 0L ? 0L : sku.getSkuSysId()) + "_" + sku.getNum()));
        for (Map.Entry<String, List<Order>> entryMap : skuMap.entrySet()) {
            String key = entryMap.getKey();
            List<Order> value = entryMap.getValue();
            if (currentSkuMap.get(key) == null || currentSkuMap.get(key).isEmpty()) {
                throw new IllegalArgumentException("首单和尾单的商品和数量必须完全相同");
            }

            if (currentSkuMap.get(key).size() != value.size()) {
                throw new IllegalArgumentException("首单和尾单的商品和数量必须完全相同");
            }
        }
    }

    private void cancelInsufficient(Staff staff, List<Long> cancelInsufficientSidList) {
        if (CollectionUtils.isEmpty(cancelInsufficientSidList)) {
            return;
        }
        tradeServiceDubbo.cancelInsufficient(staff, OpEnum.INSUFFICIENT_CANCEL, cancelInsufficientSidList.toArray(new Long[0]));
    }

    private void fillShipper(Staff staff, TradeModels tradeModels, TradeQueryParams params) {
        if (!Objects.equals(params.getQueryId(), SystemTradeQueryParamsContext.QUERY_WAIT_PACK)) {
            return;
        }
        if (!CompanyUtils.openMultiShipper(staff)) {
            return;
        }
        if (tradeModels == null || CollectionUtils.isEmpty(tradeModels.getList())) {
            return;
        }
        TradeModel tradeModel = tradeModels.getList().get(0);
        if (tradeModel.getUserId() == null) {
            return;
        }
        List<User> userList = shipperDubbo.queryByUserId(staff, Lists.newArrayList(tradeModel.getUserId()));
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        WaveAssembleInfo waveAssembleInfo = tradeModel.getWaveAssembleInfo() == null ? new WaveAssembleInfo() : tradeModel.getWaveAssembleInfo();
        waveAssembleInfo.setShipperName(userList.get(0).getShipperName());
        waveAssembleInfo.setShipperId(userList.get(0).getShipperId());
        tradeModel.setWaveAssembleInfo(waveAssembleInfo);
    }
}

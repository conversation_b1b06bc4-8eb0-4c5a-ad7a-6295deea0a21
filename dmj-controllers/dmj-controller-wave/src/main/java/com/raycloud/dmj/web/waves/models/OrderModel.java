package com.raycloud.dmj.web.waves.models;

import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.NewTradeExtendConfigEnum;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.trade.except.TradeExceptViewUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.RefundUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.DMJItemUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.io.Serializable;
import java.text.ParseException;
import java.util.*;

/**
 * 子订单的Model
 *
 * <AUTHOR>
 */
@Deprecated
public class OrderModel implements Serializable {

    static Date DEF_DATE = null;
    static Map<String, String> URL_MAP = new HashMap<String, String>();


    static {
        try {
            DEF_DATE = DateUtils.parseDate("2000-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    /**
     *
     */
    private static final long serialVersionUID = -9145778202019554378L;

    /**
     * 系统编号
     */
    private String id;

    private String tid;

    private String destId;

    private String destName;

    private Long taobaoId;

    private String oid;

    private String sid;

    private String status;

    private String picPath;
    private String sysPicPath;
    // 暴露给前端应该展示 "系" 或 "平" ,默认"系"
    private String showPicSwitch = "系";

    private Integer num;
    // 平台原始数量
    private Integer oriNum;

    private String numIid;

    //视图层这个字段即是系统名称又可以是平台名称
    private String title;

    private String platFormTitle;

    //系统商家编码
    private String outerId;

    //主商家编码
    private String mainOuterId;

    //系统主商家编码
    private String sysItemOuterId;

    private String skuId;

    private String skuPropertiesName;

    private String platSkuPropertiesName;

    private Double price;

    private Double payment;

    private String payAmount;

    private String acPayment;

    private String saleFee;

    private String salePrice;

    private Double discountFee;

    private Double adjustFee;

    private String refundId;

    private Integer isRefund;

    private String refundStatus;

    private Date payTime;

    private Date consignTime;

    private Date created;

    /**
     * 拷贝主订单的created
     */
    private Date tradeCreated;

    private String outerSkuId;

    /**
	 * 是否在系统发货 1 是，0 否
	 */
	private Integer sysConsigned;

	/**
     * 订单系统状态
     */
    private String sysStatus;

    /**
     * 库存状态
     */
    private String stockStatus;

    /**
     * 类目名称
     */
    private String itemCatName;

    /**
     * 最新商品销售价
     */
    private Double itemPrice;

    public Integer getAllocateStock() {
        return allocateStock;
    }

    public void setAllocateStock(Integer allocateStock) {
        this.allocateStock = allocateStock;
    }

    private Integer allocateStock;
    /**
     * 此值不能给默认值，前端会判断是否存在。再去取库存
     */
    private Integer unAllocateStock;

    public Integer getUnAllocateStock() {
        return unAllocateStock;
    }

    public void setUnAllocateStock(Integer unAllocateStock) {
        this.unAllocateStock = unAllocateStock;
    }

    private Integer insufficientCanceled;

    /**
     * 商品系统编号
     */
    private Long itemSysId;

    /**
     * sku系统编号
     */
    private Long skuSysId;

    /**
     * 分配的库存数量
     */
    private Integer stockNum;

    /**
     * num * price总价，或者是手动修改
     */
    private String totalFee;


    private Set<String> exceptions;

    /**
     * 如果为0表示这个商品为商品，1表示SKU
     */
    private Integer isItemSku;

    /**
     * 系统的商家编码
     */
    private String sysOuterId;

    /**
	 * 平台的商家编码
     * 优先取sku级别的平台编码[outer_sku_id]，没有的话取item级别的平台编码[outerIid]
	 */
	private String platOuterId;

	/**
     * 系统的sku的属性规格
     */
    private String sysSkuPropertiesName;

    /**
     * 系统的sku的属性规格别名
     */
    private String sysSkuPropertiesAlias;
    /**
     * 系统规格备注
     */
    private String sysRemark;

    /**
     * 系统商品标题
     */
    private String sysTitle;

    /**
     * 商品简称
     */
    private String shortTitle;
    /**
     * 子订单类型 sys系统子订单 tb淘宝子订单 jd京东子订单 tm天猫子订单
     */
    private String source;

    /**
     * 整笔子订单的净重值
     */
    private String netWeight;

    /**
     * 体积
     */
    private String volume;
    /**
     * 订单修改时是否允许修改该商品, 1 允许修改, 0 不允许修改
     */
    private boolean operable = true;

    /**
     * 子订单的交易快照
     *
     * @return
     */
    private String snapshotUrl;
    /**
     * 识别码
     */
    private String identCode;
    /**
     * 商品类型 0 普通商品, 1 赠品, 2 套件
     */
    private String type;
    /**
     * 套件子订单下的单品
     */
    private List<OrderModel> suits;

    /**
     * giftNum > 0为赠品
     */
    private Integer giftNum;

    /**
     * 中文报关名
     */
    private String declareNameZh;

    /**
     * 英文报关名
     */
    private String declareNameEn;

    /**
     * 海关编码
     */
    private String hsCode;

    /**
     * 申报金额
     */
    private String declareAmount;

    /**
     * 申报重量
     */
    private Double declareWeight;

    /**
     * 是否是虚拟商品
     */
    private Integer isVirtual;

    /**
     * 折扣率
     */
    private Double discountRate;

    private Long customGiftType;

    private Date endTime;

    private Double cost;

    private Date updTime;

    private Integer isPick;

    private Integer forcePackNum;

    /**
     * 条形码
     */
    private String barcode;

    private long availableStock;

    /**
     * 实际总库存
     */
    private long availableStockSum;

    private Integer beforeStockAllocationCount;
    /**
     * 订单转化类型 0表示正常订单，1表示分销系统
     */
    private Integer convertType;

    /**
     * 订单属于soure or dest ,0表示正常订单，1表示source，2表示dest
     */
    private Integer belongType;

    /**
	 * 各平台状态映射后的统一状态，
	 */
	private String unifiedStatus;

    private String divideOrderFee;

    /**
     * 预计发货时间
     */
	private Date estimateConTime;
    /**
     * 退款状态，用于前端展示
     */
	private String refundMessage;

    /**
     * 是否为预售订单 0:正常订单，1：是预售；2预售转正常；3：系统预售；4：平台预售
     */
    private Integer isPresell;

    private String supplierName;

    private Long combineId;

    private String combineSysTitle;

    private String combinePayment;

    private String itemSuitOuterId;

    /**
     * 商品标签
     */
    private List<String> orderItemTagNames;

    /**
     * 商品货位编码
     */
    private List<String> goodsSectionCodes;

    /**
     * 生产信息（生产批次、日期）
     */
    private List<OrderProduct> orderProducts;

    /**
     * 翱象
     * 承诺/最晚出库时间
     */
    private String promiseOutBoundTime;

    /**
     * 翱象订单标示
     */
    private Boolean aox = false;

    public List<String> getGoodsSectionCodes() {
        return goodsSectionCodes;
    }

    public OrderModel setGoodsSectionCodes(List<String> goodsSectionCodes) {
        this.goodsSectionCodes = goodsSectionCodes;
        return this;
    }

    /**
     * 是否使用虚拟库存
     */
    private Boolean usePrivateStock;

    /**
     * 虚拟库存仓名
     */
    private String virtualWarehouseName;

    /**
     * 虚拟库存可用数
     */
    private Integer tradeNotUsedCount;

    /**
     * 虚拟库存总库存
     */
    private Integer totalStock;
    /**
     * 免拣免验标记
     */
    private Integer pickCheckFlag;

    public Boolean getUsePrivateStock() {
        return usePrivateStock;
    }

    public void setUsePrivateStock(Boolean usePrivateStock) {
        this.usePrivateStock = usePrivateStock;
    }

    public String getVirtualWarehouseName() {
        return virtualWarehouseName;
    }

    public void setVirtualWarehouseName(String virtualWarehouseName) {
        this.virtualWarehouseName = virtualWarehouseName;
    }

    public Integer getTradeNotUsedCount() {
        return tradeNotUsedCount;
    }

    public void setTradeNotUsedCount(Integer tradeNotUsedCount) {
        this.tradeNotUsedCount = tradeNotUsedCount;
    }

    public Integer getTotalStock() {
        return totalStock;
    }

    public void setTotalStock(Integer totalStock) {
        this.totalStock = totalStock;
    }

    public Integer getIsPresell() {
        return isPresell;
    }

    public OrderModel setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
        return this;
    }

    /**
     * 用于前端展示的成本
     */
    private String visualCost;
    /**
     * 平台主商家编码
     */
    private String outerIid;

	private OrderExt orderExt;

    /**
     * BTAS订单订单码
     */
	private List<String> btasOrderCode;

    /**
     * BTAS质检结果
     */
	private List<AbstractMap.SimpleEntry> btasResultCh;

    public Integer getOriNum() {
        return oriNum;
    }

    public void setOriNum(Integer oriNum) {
        this.oriNum = oriNum;
    }

    /**
     * 订单商品已验货数量
     */
    private Integer currNum;

    /**
     * 订单数量，不做持久化
     */
    private Integer tradeNum;

    /**
     * 保存的唯一码
     */
    private String uniqueCodes;

    /**
     * 按单采购单号
     */
    private String caigouCode;

    public String getCaigouCode() {
        return caigouCode;
    }

    public void setCaigouCode(String caigouCode) {
        this.caigouCode = caigouCode;
    }

    /**
     * 是否开启批次设置  0-否 1-是
     */
    private Integer hasBatch;

    /**
     * 批次号
     */
    private List<String> batchNos;

    /**
     * 是否开启生产日期设置  0-否 1-是
     */
    private Integer hasProduct;

    /**
     * 生产日期
     */
    private List<String> productDates;

    private Integer sfFreeShipping;

    public Integer getSfFreeShipping() {
        return sfFreeShipping;
    }

    public void setSfFreeShipping(Integer sfFreeShipping) {
        this.sfFreeShipping = sfFreeShipping;
    }

    /**
     *  商品无需发货 0/null -否、1-是
     */
    private Integer nonConsign;

    public Integer getNonConsign() {
        return nonConsign;
    }

    public OrderModel setNonConsign(Integer nonConsign) {
        this.nonConsign = nonConsign;
        return this;
    }

    public Integer getTradeNum() {
        return tradeNum;
    }

    public OrderModel setTradeNum(Integer tradeNum) {
        this.tradeNum = tradeNum;
        return this;
    }

    public Integer getCurrNum() {
        return currNum;
    }

    public void setCurrNum(Integer currNum) {
        this.currNum = currNum;
    }

    public String getUniqueCodes() {
        return uniqueCodes;
    }

    public void setUniqueCodes(String uniqueCodes) {
        this.uniqueCodes = uniqueCodes;
    }

    public long getAvailableStock() {
		return availableStock;
	}

    public void setAvailableStock(long availableStock) {
        this.availableStock = availableStock;
    }

    /**
     * 对订单查询需要needOrder的时候进行赋值order字段
     *
     * @param orderModel
     * @param orderFields
     * @return
     */
    public static OrderModel copyQueryNeedOrder(OrderModel orderModel, String orderFields) {
        if (StringUtils.isEmpty(orderFields)) {
            return null;
        }

        OrderModel newOrder = new OrderModel();
        String[] orderFieldArr = orderFields.split(",");
        for (String orderField : orderFieldArr) {
            if ("id".equals(orderField)) {
                newOrder.setId(orderModel.getId());
            } else if ("sid".equals(orderField)) {
                newOrder.setSid(orderModel.getSid());
            } else if ("oid".equals(orderField)) {
                newOrder.setOid(orderModel.getOid());
            } else if ("tid".equals(orderField)) {
                newOrder.setTid(orderModel.getTid());
            } else if ("num".equals(orderField)) {
                newOrder.setNum(orderModel.getNum());
            } else if ("picPath".equalsIgnoreCase(orderField)) {
                newOrder.setPicPath(orderModel.getPicPath());
            } else if ("sysSkuPropertiesAlias".equalsIgnoreCase(orderField)) {
                newOrder.setSysSkuPropertiesAlias(orderModel.getSysSkuPropertiesAlias());
            } else if ("skuPropertiesName".equalsIgnoreCase(orderField)) {
                newOrder.setSkuPropertiesName(orderModel.getSkuPropertiesName());
            } else if ("platSkuPropertiesName".equalsIgnoreCase(orderField)) {
                newOrder.setPlatSkuPropertiesName(orderModel.getPlatSkuPropertiesName());
            } else if ("shortTitle".equalsIgnoreCase(orderField)) {
                newOrder.setShortTitle(orderModel.getShortTitle());
            } else if ("platFormTitle".equalsIgnoreCase(orderField)) {
                newOrder.setPlatFormTitle(orderModel.getPlatFormTitle());
            } else if ("sysOuterId".equalsIgnoreCase(orderField)) {
                newOrder.setSysOuterId(orderModel.getSysOuterId());
            } else if ("sysRemark".equalsIgnoreCase(orderField)) {
                newOrder.setSysRemark(orderModel.getSysRemark());
            } else if ("platOuterId".equalsIgnoreCase(orderField)) {
                newOrder.setPlatOuterId(orderModel.getPlatOuterId());
            } else if ("outerIid".equalsIgnoreCase(orderField)) {
                newOrder.setOuterIid(orderModel.getOuterIid());
            } else if ("outerId".equalsIgnoreCase(orderField)) {
                newOrder.setOuterId(orderModel.getOuterId());
            } else if ("refundStatus".equalsIgnoreCase(orderField)) {
                newOrder.setIsRefund(orderModel.getIsRefund());
                newOrder.setRefundStatus(orderModel.getRefundStatus());
            } else if ("sysConsigned".equalsIgnoreCase(orderField)) {
				newOrder.setSysConsigned(orderModel.getSysConsigned());
			} else if ("unifiedStatus".equalsIgnoreCase(orderField)) {
				newOrder.setUnifiedStatus(orderModel.getUnifiedStatus());
			} else if ("mainOuterId".equalsIgnoreCase(orderField)) {
                newOrder.setMainOuterId(orderModel.getMainOuterId());
            } else if ("sysItemOuterId".equalsIgnoreCase(orderField)) {
                newOrder.setSysItemOuterId(orderModel.getSysItemOuterId());
            } else if ("title".equalsIgnoreCase(orderField)) {
                newOrder.setTitle(StringUtils.isNotBlank(orderModel.getTitle()) ? orderModel.getTitle() : "");
            } else if ("sysTitle".equalsIgnoreCase(orderField)) {
                newOrder.setSysTitle(StringUtils.isNotBlank(orderModel.getSysTitle()) ? orderModel.getSysTitle() : "");
            } else if ("giftNum".equalsIgnoreCase(orderField)){
                newOrder.setGiftNum(orderModel.getGiftNum());
            } else if ("source".equalsIgnoreCase(orderField)){
                newOrder.setSource(orderModel.getSource());
            } else if("destId".equalsIgnoreCase(orderField)){
                newOrder.setDestId(orderModel.getDestId());
            } else if ("sysPicPath".equalsIgnoreCase(orderField)) {
                newOrder.setSysPicPath(orderModel.getSysPicPath());
            } else if ("oriNum".equalsIgnoreCase(orderField)) {
                newOrder.setOriNum(orderModel.getOriNum());
            }


        }
        newOrder.setShowPicSwitch(orderModel.getShowPicSwitch());
        newOrder.setItemSysId(orderModel.getItemSysId());
        newOrder.setSkuSysId(orderModel.getSkuSysId());
        newOrder.setCaigouCode(orderModel.getCaigouCode());
        return newOrder;
    }

    public String getPlatFormTitle() {
        return platFormTitle;
    }

    public void setPlatFormTitle(String platFormTitle) {
        this.platFormTitle = platFormTitle;
    }

    public Integer getConvertType() {
        return convertType;
    }

    public OrderModel setConvertType(Integer convertType) {
        this.convertType = convertType;
        return this;

    }

    public Integer getBelongType() {
        return belongType;
    }

    public OrderModel setBelongType(Integer belongType) {
        this.belongType = belongType;
        return this;
    }

    public long getAvailableStockSum() {
        return availableStockSum;
    }

    public void setAvailableStockSum(long availableStockSum) {
        this.availableStockSum = availableStockSum;
    }

    public String getIdentCode() {
        return identCode;
    }

    public OrderModel setIdentCode(String identCode) {
        this.identCode = identCode;
        return this;
    }

    public String getSnapshotUrl() {
        return snapshotUrl;
    }

    public OrderModel setSnapshotUrl(String snapshotUrl) {
        this.snapshotUrl = snapshotUrl;
        return this;
    }

    public String getTid() {
        return tid;
    }

    public OrderModel setTid(String tid) {
        this.tid = tid;
        return this;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public OrderModel setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
        return this;
    }

    public Date getPayTime() {
        return payTime;
    }

    public OrderModel setPayTime(Date payTime) {
        this.payTime = payTime;
        return this;
    }

    public Date getConsignTime() {
        return consignTime;
    }

    public OrderModel setConsignTime(Date consignTime) {
        this.consignTime = consignTime;
        return this;
    }

    public Date getCreated() {
        return created;
    }

    public OrderModel setCreated(Date created) {
        this.created = created;
        return this;
    }

    public String getOid() {
        return oid;
    }

    public OrderModel setOid(String oid) {
        this.oid = oid;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public OrderModel setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getPicPath() {
        return DMJItemUtils.optimizePicUrl(picPath);
    }

    public OrderModel setPicPath(String picPath) {
        this.picPath = picPath;
        return this;
    }

    public Integer getNum() {
        return num;
    }

    public OrderModel setNum(Integer num) {
        this.num = num;
        return this;
    }

    public String getNumIid() {
        return numIid;
    }

    public String getSkuId() {
        return skuId;
    }

    public OrderModel setSkuId(String skuId) {
        this.skuId = skuId;
        return this;
    }

    public OrderModel setNumIid(String numIid) {
        this.numIid = numIid;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public OrderModel setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getOuterId() {
        return outerId;
    }

    public OrderModel setOuterId(String outerId) {
        this.outerId = outerId;
        return this;
    }

    public String getSkuPropertiesName() {
        return skuPropertiesName;
    }

    public OrderModel setSkuPropertiesName(String skuPropertiesName) {
        this.skuPropertiesName = skuPropertiesName;
        return this;
    }

    public Double getPrice() {
        return price;
    }

    public OrderModel setPrice(Double price) {
        this.price = price;
        return this;
    }

    public OrderModel setPrice(String price) {
        if (StringUtils.isEmpty(price) || "null".equals(price.trim()))
            this.price = 0.00;
        else
            this.price = Double.parseDouble(price.trim());
        return this;
    }

    public Double getPayment() {
        return payment;
    }

    public OrderModel setPayment(Double payment) {
        this.payment = payment;
        return this;
    }

    public String getPayAmount() {
        return payAmount;
    }

    public OrderModel setPayAmount(String payAmount) {
        this.payAmount = payAmount;
        return this;
    }

    public String getAcPayment() {
        return acPayment;
    }

    public OrderModel setAcPayment(String acPayment) {
        this.acPayment =  acPayment;
        return this;
    }

    public OrderModel setPayment(String payment) {
        if (StringUtils.isEmpty(payment))
            this.payment = 0.00;
        else
            this.payment = Double.parseDouble(payment.trim());
        return this;
    }

    public String getSaleFee() {
        return saleFee;
    }

    public OrderModel setSaleFee(String saleFee) {
        this.saleFee = saleFee;
        return this;
    }

    public OrderModel setSaleFeeCondition(String saleFee) {
        if (StringUtils.isEmpty(saleFee))
            this.saleFee = "0.00";
        else
            this.saleFee = saleFee.trim();
        return this;
    }

    public String getSalePrice() {
        return salePrice;
    }

    public OrderModel setSalePrice(String salePrice) {
        this.salePrice = salePrice;
        return this;
    }

    public OrderModel setSalePriceCondition(String salePrice) {
        if (StringUtils.isEmpty(salePrice))
            this.salePrice = "0.00";
        else
            this.salePrice = salePrice.trim();
        return this;
    }

    public Double getDiscountFee() {
        return discountFee;
    }

    public OrderModel setDiscountFee(Double discountFee) {
        this.discountFee = discountFee;
        return this;
    }

    public OrderModel setDiscountFee(String discountFee) {
        if (StringUtils.isEmpty(discountFee))
            this.discountFee = 0.00;
        else
            this.discountFee = Double.parseDouble(discountFee.trim());
        return this;
    }

    public Double getAdjustFee() {
        return adjustFee;
    }

    public OrderModel setAdjustFee(Double adjustFee) {
        this.adjustFee = adjustFee;
        return this;
    }

    public OrderModel setAdjustFee(String adjustFee) {
        if (StringUtils.isEmpty(adjustFee))
            this.adjustFee = 0.00;
        else
            this.adjustFee = Double.parseDouble(adjustFee.trim());
        return this;
    }

    public Integer getIsRefund() {
        return isRefund;
    }

    public OrderModel setIsRefund(Integer isRefund) {
        this.isRefund = isRefund;
        return this;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public OrderModel setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
        return this;
    }

    public String getRefundId() {
        return refundId;
    }

    public OrderModel setRefundId(String refundId) {
        this.refundId = refundId;
        return this;
    }

    public String getOuterSkuId() {
        return outerSkuId;
    }

    public OrderModel setOuterSkuId(String outerSkuId) {
        this.outerSkuId = outerSkuId;
        return this;
    }

    public Date getTradeCreated() {
        return tradeCreated;
    }

    public OrderModel setTradeCreated(Date tradeCreated) {
        this.tradeCreated = tradeCreated;
        return this;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public OrderModel setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
        return this;
    }

    public String getItemCatName() {
        return itemCatName;
    }

    public void setItemCatName(String itemCatName) {
        this.itemCatName = itemCatName;
    }

    public Integer getInsufficientCanceled() {
        return insufficientCanceled;
    }

    public OrderModel setInsufficientCanceled(Integer insufficientCanceled) {
        this.insufficientCanceled = insufficientCanceled;
        return this;
    }

    public String getStockStatus() {
        return stockStatus;
    }

    public OrderModel setStockStatus(String stockStatus) {
        this.stockStatus = stockStatus;
        return this;
    }

    public Integer getStockNum() {
        return stockNum;
    }

    public OrderModel setStockNum(Integer stockNum) {
        this.stockNum = stockNum;
        return this;
    }

    public Long getItemSysId() {
        return itemSysId;
    }

    public OrderModel setItemSysId(Long itemSysId) {
        this.itemSysId = itemSysId;
        return this;
    }

    public Long getSkuSysId() {
        return skuSysId;
    }

    public OrderModel setSkuSysId(Long skuSysId) {
        this.skuSysId = skuSysId;
        return this;
    }

    public String getId() {
        return id;
    }

    public OrderModel setId(String id) {
        this.id = id;
        return this;
    }

    public String getSid() {
        return sid;
    }

    public OrderModel setSid(String sid) {
        this.sid = sid;
        return this;
    }

    public Integer getIsItemSku() {
        return isItemSku;
    }

    public OrderModel setIsItemSku(Integer isItemSku) {
        this.isItemSku = isItemSku;
        return this;
    }

    public String getSysOuterId() {
        return sysOuterId;
    }

    public OrderModel setSysOuterId(String sysOuterId) {
        this.sysOuterId = sysOuterId;
        return this;
    }

    public String getPlatOuterId() {
		return platOuterId;
	}

	public OrderModel setPlatOuterId(String platOuterId) {
		this.platOuterId = platOuterId;
		return this;
	}

    public String getSysSkuPropertiesName() {
        return sysSkuPropertiesName;
    }

    public OrderModel setSysSkuPropertiesName(String sysSkuPropertiesName) {
        this.sysSkuPropertiesName = sysSkuPropertiesName;
        return this;
    }

    public String getPlatSkuPropertiesName() {
        return platSkuPropertiesName;
    }

    public OrderModel setPlatSkuPropertiesName(String platSkuPropertiesName) {
        this.platSkuPropertiesName = platSkuPropertiesName;
        return this;
    }

    public String getSysSkuPropertiesAlias() {
        return sysSkuPropertiesAlias;
    }

    public OrderModel setSysSkuPropertiesAlias(String sysSkuPropertiesAlias) {
        this.sysSkuPropertiesAlias = sysSkuPropertiesAlias;
        return this;
    }

    public String getSysRemark() {
        return sysRemark;
    }

    public void setSysRemark(String sysRemark) {
        this.sysRemark = sysRemark;
    }

    public String getSysTitle() {
        return sysTitle;
    }

    public OrderModel setSysTitle(String sysTitle) {
        this.sysTitle = sysTitle;
        return this;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public OrderModel setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
        return this;
    }

    public String getNetWeight() {
        return netWeight;
    }

    public OrderModel setNetWeight(String netWeight) {
        this.netWeight = netWeight;
        return this;
    }

    public String getVolume() {
        return volume;
    }

    public OrderModel setVolume(String volume) {
        this.volume = volume;
        return this;
    }

    public boolean isOperable() {
        return operable;
    }

    public OrderModel setOperable(boolean operable) {
        this.operable = operable;
        return this;
    }

    public String getType() {
        return type;
    }

    public OrderModel setType(String type) {
        this.type = type;
        return this;
    }

    public List<OrderModel> getSuits() {
        return suits;
    }

    public OrderModel setSuits(List<OrderModel> suits) {
        this.suits = suits;
        return this;
    }

    public static List<OrderModel> entity2Vo(Staff staff, List<Order> orders, boolean showInDetail, TradeConfig tradeConfig) {
        List<OrderModel> list = new ArrayList<OrderModel>(orders.size());
        for (Order order : orders) {
            list.add(toBaseOrderModel(staff,null, order, showInDetail, tradeConfig));
        }
        return list;
    }

    public Long getCustomGiftType() {
        return customGiftType;
    }

    public OrderModel setCustomGiftType(Long customGiftType) {
        this.customGiftType = customGiftType;
        return this;
    }

    public Date getUpdTime() {
        return updTime;
    }
    public OrderModel setUpdTime(Date updTime) {
        this.updTime = updTime;
        return this;
    }

    public String getVisualCost() {
        return visualCost;
    }

    public void setVisualCost(String visualCost) {
        this.visualCost = visualCost;
    }

    public Integer getHasBatch() {
        return hasBatch;
    }

    public void setHasBatch(Integer hasBatch) {
        this.hasBatch = hasBatch;
    }

    public Integer getHasProduct() {
        return hasProduct;
    }

    public void setHasProduct(Integer hasProduct) {
        this.hasProduct = hasProduct;
    }

    public List<String> getBatchNos() {
        return batchNos;
    }

    public void setBatchNos(List<String> batchNos) {
        this.batchNos = batchNos;
    }

    public List<String> getProductDates() {
        return productDates;
    }

    public void setProductDates(List<String> productDates) {
        this.productDates = productDates;
    }

    /**
     * 转化为基本的子订单模型，也就是
     *
     * @param tbOrder
     */
    public static OrderModel toBaseOrderModel(Staff staff,Trade trade, Order tbOrder, boolean showInDetail, TradeConfig tradeConfig) {
        boolean hasPowerFxPrice = StringUtils.contains(staff.getPowerDataPrivilegeSettings(), "1000309");

        OrderModel model = new OrderModel().setSource(tbOrder.getSource())
                .setConsignTime(getConsignTime(trade, tbOrder)).setCreated(tbOrder.getCreated())
                .setStatus(tbOrder.getStatus()).setOuterSkuId(tbOrder.getOuterSkuId())
                .setNum(tbOrder.getNum()).setPrice(tbOrder.getPrice())
                .setRefundStatus(queryRefundStatus(tbOrder.getRefundStatus())).setRefundId(tbOrder.getRefundId())
                .setTradeCreated(trade == null ? tbOrder.getCreated() : trade.getCreated()).setSysStatus(tbOrder.getSysStatus())
                .setStockStatus(tbOrder.getStockStatus()).setStockNum(tbOrder.getStockNum())
                .setItemSysId(tbOrder.getItemSysId()).setSid(tbOrder.getSid().toString())
                .setDestName(tbOrder.getDestName())
                .setTradeNum(Optional.ofNullable(tbOrder.getTradeNum()).orElse(null))
                .setSkuSysId(tbOrder.getSkuSysId()).setId(tbOrder.getId() + "").setDestId(tbOrder.getDestId()+"").setOid(String.valueOf(tbOrder.getOid()))
                .setPayment(tbOrder.getPayment()).setDiscountFee(tbOrder.getDiscountFee())
                .setAcPayment(tbOrder.getAcPayment())
                .setSaleFeeCondition(hasPowerFxPrice ? (tbOrder.getSaleFee() != null ? tbOrder.getSaleFee() + "" : "") : "***").setSalePriceCondition(hasPowerFxPrice ? tbOrder.getSalePrice()+"" : "***")
                .setAdjustFee(tbOrder.getAdjustFee()).setTotalFee(tbOrder.getTotalFee())
                .setSysOuterId(tbOrder.getSysOuterId()).setSysSkuPropertiesName(tbOrder.getSysSkuPropertiesName())
                .setShortTitle(tbOrder.getShortTitle()).setSysSkuPropertiesAlias(tbOrder.getSysSkuPropertiesAlias())
                .setTid(tbOrder.getTid()).setPayTime(tbOrder.getPayTime())
                .setNumIid(tbOrder.getNumIid()).setSkuId(tbOrder.getSkuId())
                .setTaobaoId(tbOrder.getTaobaoId()).setIsRefund(isRefund(tbOrder, showInDetail))
                .setNetWeight(String.valueOf(tbOrder.getNetWeight())).setInsufficientCanceled(tbOrder.getInsufficientCanceled())
                .setOperable(tbOrder.canUpdate()).setIdentCode(tbOrder.getIdentCode()).setType(String.valueOf(tbOrder.getType()))
                .setGiftNum(tbOrder.getGiftNum()).setDiscountRate(tbOrder.getDiscountRate()).setIsVirtual(tbOrder.getIsVirtual())
                .setPlatSkuPropertiesName(tbOrder.getSkuPropertiesName())
                .setCustomGiftType(tbOrder.getCustomGiftType())
                .setUpdTime(tbOrder.getUpdTime())
                .setIsPick(tbOrder.getIsPick())
                .setBarcode(tbOrder.getBarcode()).setUnifiedStatus(TradeStatusUtils.getUnifiedStatus(tbOrder.getStatus()))
				.setSysConsigned(tbOrder.getSysConsigned())
        		.setForcePackNum(tbOrder.getForcePackNum()).setEstimateConTime(tbOrder.getEstimateConTime())
				.setConvertType(tbOrder.getConvertType())
				.setBelongType(tbOrder.getBelongType())
                .setDivideOrderFee(StringUtils.isEmpty(tbOrder.getDivideOrderFee()) ? "0.00" : tbOrder.getDivideOrderFee())
                .setOrderExt(tbOrder.getOrderExt(),tradeConfig)
                .setSysTitle(tbOrder.getSysTitle())
                .setVolume(String.valueOf(tbOrder.getVolume()))
                .setIsPresell(tbOrder.getIsPresell())
                .setPayAmount(tbOrder.getPayAmount())
                .setNonConsign(TradeModel.lvn(tbOrder.getNonConsign()))
                .setGoodsSectionCodes(tbOrder.getGoodsSectionCodes())
                .setOrderProducts(tbOrder.getOrderProducts())
                .setCombineId(tbOrder.getCombineId())
                .setCombineSysTitle(tbOrder.getCombineSysTitle())
                .setCombinePayment(tbOrder.getCombinePayment())
                .setItemSuitOuterId(tbOrder.getItemSuitOuterId())
                .setPickCheckFlag(tbOrder.getPickCheckFlag());
        setSysInfo(model, tbOrder, tradeConfig, (null == trade)?"":trade.getSubSource());
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade == null ? tbOrder.getSource() : trade.getSource())) {
            if (null == tbOrder.getSkuSysId() || -1L == tbOrder.getSkuSysId()) {
                model.setIsItemSku(0);
            } else {
                model.setIsItemSku(1);
            }
        } else {
            if (StringUtils.isEmpty(tbOrder.getSkuId())) {
                model.setIsItemSku(0);
            } else {
                model.setIsItemSku(1);
            }
        }
        model.setExceptions(TradeExceptViewUtils.getOrderExceptEnglish(staff,tbOrder));
        model.setRefundMessage(RefundUtils.orderRefundMessage(tbOrder));
        //设置orders的交易快照地址
        setSnapshotUrlByTrade(model);
        //单品组装
        if (tbOrder.isSuit(false)) {
            if (tbOrder.getSuits() != null) {
                String identCode = null;
                List<OrderModel> subModels = new ArrayList<OrderModel>(tbOrder.getSuits().size());
                for (Order son : tbOrder.getSuits()) {
                    subModels.add(toBaseOrderModel(staff,trade, son, false, tradeConfig));
                    if (son.getIdentCode() != null && son.getIdentCode().trim().length() > 0) {
                        if (identCode == null) {
                            identCode = son.getIdentCode();
                        } else {
                            identCode += "," + son.getIdentCode();
                        }
                    }
                }
                model.setSuits(subModels);
                model.setIdentCode(identCode);
            }
        }
        model.setEndTime(tbOrder.getEndTime());
        model.setCost(tbOrder.getCost());
        model.setVisualCost(String.valueOf(tbOrder.getCost()));
        model.setOuterIid(tbOrder.getOuterIid());
        //BTAS订单 -- 订单码组装
        model.setBtasOrderCode(OrderUtils.getBtasOrderCode(tbOrder));
        model.setBtasResultCh(OrderUtils.getBtasPictureQualityResult(tbOrder));
        model.setSfFreeShipping(OrderUtils.getSfFreeShipping(tbOrder));
        model.setOriNum(OrderUtils.getOriNum(tbOrder));
        model.setCaigouCode(tbOrder.getCaigouCode());
        // 翱象 承诺/最晚出库时间 处理
        handleAox(staff, model, trade, tbOrder, tradeConfig);
        return model;
    }

    //设置orders的交易快照地址
    private static void setSnapshotUrlByTrade(OrderModel model) {
        Order order = new Order();
        order.setSource(model.getSource());
        order.setNumIid(model.getNumIid());
        order.setSkuId(model.getSkuId());

        model.setSnapshotUrl(OrderUtils.getSnapshotUrl(order));

    }

    private static String queryRefundStatus(String refundStatus) {
        if (Order.REFUND_SELLER_CONTINUE_CONSIGN.equals(refundStatus))
            return Order.NO_REFUND;
        return refundStatus;
    }

    private static void setSysInfo(OrderModel model, Order order, TradeConfig tc, String subSource) {
        if (order.getItemSysId() != null && order.getItemSysId() > 0) {//
            model.setTitle(order.getSysTitle());
            model.setPlatFormTitle(order.getTitle());
            model.setOuterId(order.getSysOuterId());
            model.setMainOuterId(order.getMainOuterId());
            model.setSysItemOuterId(order.getSysItemOuterId());
            model.setSkuPropertiesName(order.getSysSkuPropertiesName());
            model.setSysRemark(OrderUtils.getSysRemark(order));
            model.setSupplierName(StringUtils.defaultIfBlank(order.getSupplierName(),""));
            model.setOrderItemTagNames(order.getOrderItemTagNames());
        } else {
            if (OrderUtils.isGxOrder(order)) {
                model.setTitle(order.getSysTitle());
            } else {
                model.setTitle(order.getTitle());
            }
            model.setTitle(order.getTitle());
            model.setSkuPropertiesName(order.getSkuPropertiesName());
            model.setSysSkuPropertiesAlias(null);
            model.setSysSkuPropertiesName(null);
            model.setOuterId(order.getOuterSkuId());
            if (StringUtils.isBlank(model.getOuterId())) {
                model.setOuterId(order.getOuterId());
            }
            if (StringUtils.isBlank(model.getOuterId())) {
                model.setOuterId(order.getOuterIid());
            }
        }

        if(!CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())){
                model.setPlatOuterId(order.getOuterSkuId());
                if (StringUtils.isBlank(model.getPlatOuterId())) {
                    model.setPlatOuterId(order.getOuterId());
                }
        }

        model.setPicPath(order.getPicPath());
        model.setSysPicPath(order.getSysPicPath());
        model.setShowPicSwitch(calculateShowPicSwitch(order,tc,subSource));
        if (model.getPicPath() == null || model.getPicPath().isEmpty()) {
            model.setPicPath(StockConstants.PATH_NO_PIC);
        }
        if (model.getSysPicPath() == null || model.getSysPicPath().trim().isEmpty()) {
            model.setSysPicPath(StockConstants.PATH_NO_PIC);
        }
    }

    private static String calculateShowPicSwitch(Order order, TradeConfig tc, String subSource){
        if (order.getItemSysId() == null || order.getItemSysId() <=0){
            return "平";
        }
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource()) || order.getSysItemChanged() - 1 == 0 || (tc.getOrderImageSource() == null || tc.getOrderImageSource() != 1)) {
            return "系";
        }
        String platform = (String) tc.get(TradeExtendConfigsEnum.PICTURE_FROM_PLATFORM.getKey());
        if (null== platform){
            return "系";
        }
        List<String> platformList = Strings.getAsStringList(platform,",",true);
        if (platformList.contains("all") || platformList.contains(order.getSource()) || (StringUtils.isNotBlank(subSource) && platformList.contains(subSource))) {
            if (StringUtils.isBlank(order.getPicPath())){
                return "系";
            }
            return "平";
        } else {
            return "系";
        }
    }

    private static void handleAox(Staff staff, OrderModel model, Trade trade, Order tbOrder, TradeConfig tradeConfig) {
        if (trade == null) {
            return;
        }
        PlatformRecommendation pr = trade.getPlatformRecommendation();
        if (pr == null) {
            return;
        }

        TbRecommendation tbRecommendation = pr.getTbRecommendation();
        if (tbRecommendation == null) {
            return;
        }

        if (
                tbRecommendation.getLogisticsAgreement() != null &&
                        Objects.equals(CommonConstants.PLAT_FORM_TYPE_AOX, tbRecommendation.getLogisticsAgreement().getAsdpBizType())
        ) {
            model.setAox(true);
        }

        List<TbRecommendation.LogisticsInfo> logisticsInfos = tbRecommendation.getLogisticsInfos();
        if (logisticsInfos == null || logisticsInfos.isEmpty()) {
            return;
        }

        for (TbRecommendation.LogisticsInfo logisticsInfo : logisticsInfos) {
            if (!Objects.equals(logisticsInfo.getSubTradeId(), tbOrder.getOid())) {
                continue;
            }
            // 承诺/最晚出库时间
            if (StringUtils.isNotEmpty(logisticsInfo.getPromiseOutboundTime())) {
                model.setPromiseOutBoundTime(logisticsInfo.getPromiseOutboundTime());
            }
            // 承诺揽收时间
            if (StringUtils.isNotEmpty(logisticsInfo.getPromiseCollectTime())) {
                OrderExt ext = model.getOrderExt();
                if (ext == null) {
                    ext = new OrderExt();
                    model.setOrderExt(ext, tradeConfig);
                }
                ext.setPromiseAcceptTime(com.raycloud.dmj.domain.trades.utils.DateUtils.str2Date(logisticsInfo.getPromiseCollectTime()));
            }
            break;
        }
    }

    private static Date getConsignTime(Trade trade, Order tbOrder) {
        if (tbOrder.getConsignTime() != null && tbOrder.getConsignTime().after(DEF_DATE)) {
            return tbOrder.getConsignTime();
        }

        if (trade != null) {
            return trade.getConsignTime();
        }

        return null;

    }

    private static int isRefund(Order order, boolean showInDetail) {
        if (StringUtils.isEmpty(order.getRefundId()) ||
                "null".equalsIgnoreCase(order.getRefundId()) ||
                TbOrder.NO_REFUND.equals(order.getRefundStatus())) {
            return 0;
        }

        if (TbOrder.REFUND_CLOSED.equals(order.getRefundStatus())
                || TbOrder.REFUND_SELLER_REFUSE_BUYER.equals(order.getRefundStatus())
                || TbOrder.REFUND_SUCCESS.equals(order.getRefundStatus())
                || TbOrder.REFUND_SELLER_CONTINUE_CONSIGN.equals(order.getRefundStatus())) {
            if (showInDetail)
                return 1;
            return 0;
        }

        return 1;
    }

    public Set<String> getExceptions() {
        return exceptions;
    }

    public void setExceptions(Set<String> exceptions) {
        this.exceptions = exceptions;
    }

    public String getTotalFee() {
        return totalFee;
    }

    public OrderModel setTotalFee(String totalFee) {
        this.totalFee = totalFee;
        return this;
    }

    public String getSource() {
        return source;
    }

    public OrderModel setSource(String source) {
        this.source = source;
        return this;
    }

    public Integer getGiftNum() {
        return giftNum;
    }

    public OrderModel setGiftNum(Integer giftNum) {
        this.giftNum = giftNum;
        return this;
    }

    public String getDeclareNameZh() {
        return declareNameZh;
    }

    public OrderModel setDeclareNameZh(String declareNameZh) {
        this.declareNameZh = declareNameZh;
        return this;
    }

    public String getDeclareNameEn() {
        return declareNameEn;
    }

    public OrderModel setDeclareNameEn(String declareNameEn) {
        this.declareNameEn = declareNameEn;
        return this;
    }

    public String getHsCode() {
        return hsCode;
    }

    public OrderModel setHsCode(String hsCode) {
        this.hsCode = hsCode;
        return this;
    }

    public String getDeclareAmount() {
        return declareAmount;
    }

    public OrderModel setDeclareAmount(String declareAmount) {
        this.declareAmount = declareAmount;
        return this;
    }

    public Double getDeclareWeight() {
        return declareWeight;
    }

    public OrderModel setDeclareWeight(Double declareWeight) {
        this.declareWeight = declareWeight;
        return this;
    }

    public Integer getIsVirtual() {
        return isVirtual;
    }

    public OrderModel setIsVirtual(Integer isVirtual) {
        this.isVirtual = isVirtual;
        return this;
    }

    public Double getDiscountRate() {
        return discountRate;
    }

    public OrderModel setDiscountRate(Double discountRate) {
        this.discountRate = discountRate;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Double getCost() {
        return cost;
    }

    public void setCost(Double cost) {
        this.cost = cost;
    }

    public Integer getIsPick() {
        return isPick;
    }

    public OrderModel setIsPick(Integer isPick) {
        this.isPick = isPick;
        return this;
    }

    public String getBarcode() {
        return barcode;
    }

    public OrderModel setBarcode(String barcode) {
        this.barcode = barcode;
        return this;
    }

    public Integer getBeforeStockAllocationCount() {
        return beforeStockAllocationCount;
    }

    public OrderModel setBeforeStockAllocationCount(Integer beforeStockAllocationCount) {
        this.beforeStockAllocationCount = beforeStockAllocationCount;
        return this;
    }

	public Integer getSysConsigned() {
		return sysConsigned;
	}

	public OrderModel setSysConsigned(Integer sysConsigned) {
		this.sysConsigned = sysConsigned;
		return this;
	}

	public String getUnifiedStatus() {
		return unifiedStatus;
	}

	public OrderModel setUnifiedStatus(String unifiedStatus) {
		this.unifiedStatus = unifiedStatus;
		return this;
	}

    public Integer getForcePackNum() {
        return forcePackNum;
    }

    public OrderModel setForcePackNum(Integer forcePackNum) {
        this.forcePackNum = forcePackNum;
        return this;

    }

    public String getDivideOrderFee() {
        return divideOrderFee;
    }

    public OrderModel setDivideOrderFee(String divideOrderFee) {
        this.divideOrderFee = divideOrderFee;
        return this;
    }

    public Date getEstimateConTime() {
        return estimateConTime;
    }

    public OrderModel setEstimateConTime(Date estimateConTime) {
        this.estimateConTime = estimateConTime;
        return this;
    }

    public String getMainOuterId() {
        return mainOuterId;
    }

    public OrderModel setMainOuterId(String mainOuterId) {
        this.mainOuterId = mainOuterId;
        return this;
    }

    public String getSysItemOuterId() {
        return sysItemOuterId;
    }

    public void setSysItemOuterId(String sysItemOuterId) {
        this.sysItemOuterId = sysItemOuterId;
    }

    public String getRefundMessage() {
        return refundMessage;
    }

    public void setRefundMessage(String refundMessage) {
        this.refundMessage = refundMessage;
    }

    public String getOuterIid() {
        return outerIid;
    }

    public void setOuterIid(String outerIid) {
        this.outerIid = outerIid;
    }

    public OrderExt getOrderExt() {
        return orderExt;
    }

    public OrderModel setOrderExt(OrderExt orderExt, TradeConfig tradeConfig) {
        if(null == orderExt){
            return this;
        }
        int auterHideOrDisplay = tradeConfig.getInteger(NewTradeExtendConfigEnum.AUTHOR_HIDE_OR_DISPLAY.getKey());

        if(0 == auterHideOrDisplay) {
            orderExt.setAuthorId(null);
            orderExt.setAuthorNo(null);
            orderExt.setAuthorName(null);
        }
        if (StringUtils.isBlank(orderExt.getAuthorNo()) && orderExt.getAuthorId() != null){
            orderExt.setAuthorNo(orderExt.getAuthorId().toString());
        }

        this.orderExt = orderExt;
        return this;
    }

    public List<String> getBtasOrderCode() {
        return btasOrderCode;
    }

    public void setBtasOrderCode(List<String> btasOrderCode) {
        this.btasOrderCode = btasOrderCode;
    }

    public List<AbstractMap.SimpleEntry> getBtasResultCh() {
        return btasResultCh;
    }

    public void setBtasResultCh(List<AbstractMap.SimpleEntry> btasResultCh) {
        this.btasResultCh = btasResultCh;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Long getCombineId() {
        return combineId;
    }

    public OrderModel setCombineId(Long combineId) {
        this.combineId = combineId;
        return this;
    }

    public String getCombineSysTitle() {
        return combineSysTitle;
    }

    public OrderModel setCombineSysTitle(String combineSysTitle) {
        this.combineSysTitle = combineSysTitle;
        return this;
    }

    public String getCombinePayment() {
        return combinePayment;
    }

    public OrderModel setCombinePayment(String combinePayment) {
        this.combinePayment = combinePayment;
        return this;
    }

    public String getItemSuitOuterId() {
        return itemSuitOuterId;
    }

    public OrderModel setItemSuitOuterId(String itemSuitOuterId) {
        this.itemSuitOuterId = itemSuitOuterId;
        return this;
    }

    public List<OrderProduct> getOrderProducts() {
        return orderProducts;
    }

    public OrderModel setOrderProducts(List<OrderProduct> orderProducts) {
        this.orderProducts = orderProducts;
        return this;
    }

    public List<String> getOrderItemTagNames() {
        return orderItemTagNames;
    }

    public void setOrderItemTagNames(List<String> orderItemTagNames) {
        this.orderItemTagNames = orderItemTagNames;
    }


    public String getDestId() {
        return destId;
    }

    public OrderModel setDestId(String destId) {
        this.destId = destId;
        return this;
    }

    public String getDestName() {
        return destName;
    }

    public OrderModel setDestName(String destName) {
        this.destName = destName;
        return this;
    }

    public Integer getPickCheckFlag() {
        return pickCheckFlag;
    }

    public OrderModel setPickCheckFlag(Integer pickCheckFlag) {
        this.pickCheckFlag = pickCheckFlag;
        return this;
    }

    public Double getItemPrice() {
        return itemPrice;
    }

    public void setItemPrice(Double itemPrice) {
        this.itemPrice = itemPrice;
    }

    public String getSysPicPath() {
        return sysPicPath;
    }

    public void setSysPicPath(String sysPicPath) {
        this.sysPicPath = sysPicPath;
    }

    public String getShowPicSwitch() {
        return showPicSwitch;
    }

    public void setShowPicSwitch(String showPicSwitch) {
        this.showPicSwitch = showPicSwitch;
    }

    public String getPromiseOutBoundTime() {
        return promiseOutBoundTime;
    }

    public void setPromiseOutBoundTime(String promiseOutBoundTime) {
        this.promiseOutBoundTime = promiseOutBoundTime;
    }

    public Boolean getAox() {
        return aox;
    }

    public void setAox(Boolean aox) {
        this.aox = aox;
    }
}

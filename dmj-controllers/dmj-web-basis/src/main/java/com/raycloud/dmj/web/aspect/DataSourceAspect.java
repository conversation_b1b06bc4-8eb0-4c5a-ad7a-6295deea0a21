package com.raycloud.dmj.web.aspect;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ibatis.sqlmap.engine.mapping.parameter.ParameterMap;
import com.ibatis.sqlmap.engine.mapping.parameter.ParameterMapping;
import com.ibatis.sqlmap.engine.mapping.result.ResultMap;
import com.ibatis.sqlmap.engine.mapping.statement.DefaultRowHandler;
import com.ibatis.sqlmap.engine.mapping.statement.MappedStatement;
import com.ibatis.sqlmap.engine.mapping.statement.RowHandlerCallback;
import com.ibatis.sqlmap.engine.scope.StatementScope;
import com.mysql.cj.jdbc.exceptions.MySQLTimeoutException;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.MDCKeys;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.SqlCoreRecordConfig;
import com.raycloud.dmj.domain.diamond.SqlMaxRowConfig;
import com.raycloud.dmj.domain.diamond.SqlNoLimitConfig;
import com.raycloud.dmj.web.log.BigSqlLog;
import com.raycloud.dmj.web.statistics.StatisticsUtils;
import com.raycloud.dmj.web.utils.ClassUtils;
import com.raycloud.dmj.web.utils.SqlMatchUtils;
import com.raycloud.dmj.web.utils.SqlParserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.log4j.MDC;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022-11-07 11:39
 * @description：
 * @modified By：
 * @version: $
 */
@Component
@Aspect
@Slf4j
public class DataSourceAspect {

    private Logger logger = Logger.getLogger(DataSourceAspect.class);

    @Resource
    protected ICache cache;

    @Value("${application.name:}")
    private String applicationName;


    private final static Integer noLimitSize = 500;

    private final static Integer maxSize = 10000;
    private final static Integer SqlStringMaxSize = 200000;

    private final static Integer allMaxSize = 100000;


    /**
     * ibatis sql拦截
     * @param invocation
     * @return
     * @throws Throwable
     */
    @Around(value = "execution(* com.ibatis.sqlmap.engine.execution.SqlExecutor.executeQuery(..)) " +
            "|| execution(* com.ibatis.sqlmap.engine.execution.SqlExecutor.executeUpdate(..)) " +
            "||execution(* com.ibatis.sqlmap.engine.impl.SqlMapExecutorDelegate.executeBatch(..))")
    public Object exec(ProceedingJoinPoint invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        if (args == null || args.length <= 0) {
            return invocation.proceed(args);
        }
        String methodName = invocation.getSignature().getName();
        if("executeUpdate".equals(methodName) || args.length <= 4){
            long start = System.currentTimeMillis();
            Object proceed = invocation.proceed(args);
            long took = System.currentTimeMillis() - start;
            statistics(args, took);
            recordStatements((StatementScope) args[0]);
            return proceed;
        }

        Integer sqlResultMaxSize = acquireSqlResultMaxSize();
        StatementScope statementScope = (StatementScope)args[0];
        Connection conn = (Connection) args[1];
        String sqlString = (String) args[2];
        Object[] parameters = (Object[]) args[3];
        RowHandlerCallback callback = (RowHandlerCallback) args[6];
        beforeSqlExecute(statementScope, conn, sqlString, parameters, callback, sqlResultMaxSize, args);
        long start = System.currentTimeMillis();
        try {
            Object invocationResult = invocation.proceed(args);
            long took = System.currentTimeMillis() - start;
            afterSqlExecute(statementScope, conn, sqlString, parameters, callback, sqlResultMaxSize, took);
            statistics(args, took);
            return invocationResult;
        } catch (Exception e) {
            long took = System.currentTimeMillis() - start;
            throwSqlExecute(statementScope, conn, sqlString, parameters, callback, sqlResultMaxSize, took, e);
            throw e;
        }
    }

    /**
     * 针对更新类的sql，进行statementName的收集
     */
    private void recordStatements(StatementScope statementScope) {
        MappedStatement statement = statementScope.getStatement();
        // 设置 ibatis 的 statementName, 配合 RayCloudStateFilter 使用的
        MDC.put(MDCKeys.STATEMENT_NAME, statement.getId());
    }

    private void statistics(Object[] args, long took) {
        try{
            StatementScope statementScope = (StatementScope)args[0];
            if(statementScope != null && statementScope.getStatement() != null){
                MappedStatement statement = statementScope.getStatement();
                StatisticsUtils.statistics(statement.getId(), took);
            }
        }catch (Exception e){
            log.error("统计statementName失败", e);
        }
    }

    protected  Integer acquireSqlResultMaxSize() {
        Integer sqlResultMaxSize = null;
        if (ConfigHolder.GLOBAL_CONFIG.getModuleSwitch() != null) {
            sqlResultMaxSize = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getBase().getSqlResultMaxSize();
        }
        if (sqlResultMaxSize == null) {
            sqlResultMaxSize = maxSize;
        }
        return sqlResultMaxSize;
    }

    protected  Integer acquireAllSqlResultMaxSize() {
        Integer sqlResultMaxSize = null;
        if (ConfigHolder.GLOBAL_CONFIG.getModuleSwitch() != null) {
            sqlResultMaxSize = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getBase().getAllSqlResultMaxSize();
        }
        if (sqlResultMaxSize == null) {
            sqlResultMaxSize = allMaxSize;
        }
        return sqlResultMaxSize;
    }

    protected  Integer acquireNoLimitSize() {
        Integer sqlResultMaxSize = null;
        if (ConfigHolder.GLOBAL_CONFIG.getModuleSwitch() != null) {
            sqlResultMaxSize = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getBase().getNoLimitSize();
        }
        if (sqlResultMaxSize == null) {
            sqlResultMaxSize = noLimitSize;
        }
        return sqlResultMaxSize;
    }

    private void throwSqlExecute(StatementScope statementScope, Connection conn, String sqlString, Object[] parameters, RowHandlerCallback callback, Integer sqlResultMaxSize, Long took, Exception throwException) {
        try {
            // 目前只处理超时异常
            if (!(throwException instanceof MySQLTimeoutException)) {
                return;
            }
            MappedStatement statement = statementScope.getStatement();
            ParameterMap parameterMap = statementScope.getParameterMap();
            String resultClassName = (String)MDC.get(MDCKeys.RESULT_CLASS_NAME);
            String params = StringUtils.join(parameters, ",");
            Integer timeOut = statement.getTimeout() == null ? null : statement.getTimeout() * 1000;
            // 未加limit 记录,可能造成big-sql  类型为0
            if(CollectionUtils.isNotEmpty(ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG.getNoLimitRecordList())) {
                noLimitSqlRecord(statement.getId(), sqlString, params, resultClassName, 0, took, timeOut, false);
            }
            if(CollectionUtils.isNotEmpty(ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG.getNoCompanyWarehouseIdList())) {
                noCompanyWarehouseIdSqlRecord(statement.getId(), sqlString, params, resultClassName, 0, took, timeOut, false);
            }
            if(CollectionUtils.isNotEmpty(ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG.getCoreRecordList())) {
                coreSqlRecord(sqlString, params);
            }
            // 去除 ibatis 的 statementName, 配合 RayCloudStateFilter 使用的
            MDC.remove(MDCKeys.STATEMENT_NAME);
            // 去除 ibatis 的 resultClassName, 配合 MaxRowsAspect 使用的
            MDC.remove(MDCKeys.RESULT_CLASS_NAME);
        } catch (Exception e){
            logger.error("sql执行失败后处理数据失败!", e);
        }
    }

    private void afterSqlExecute(StatementScope statementScope, Connection conn, String sqlString, Object[] parameters, RowHandlerCallback callback, Integer sqlResultMaxSize, Long took) {
        try {
            String url = conn.getMetaData().getURL();
            MappedStatement statement = statementScope.getStatement();
            ParameterMap parameterMap = statementScope.getParameterMap();
            String resultClassName = (String)MDC.get(MDCKeys.RESULT_CLASS_NAME);
            String params = StringUtils.join(parameters, ",");
            Integer timeOut = statement.getTimeout() == null ? null : statement.getTimeout() * 1000;
            Integer resultSize = 0;
            if(callback.getRowHandler() != null && callback.getRowHandler() instanceof DefaultRowHandler) {
                DefaultRowHandler rowHandler = (DefaultRowHandler)callback.getRowHandler();
                resultSize = CollectionUtils.isNotEmpty(rowHandler.getList()) ? rowHandler.getList().size() : 0;
            }
            // 已经是big-sql 记录  类型为1
            if (resultSize >= sqlResultMaxSize) {
                if (maxRowLimit(statement, sqlString, resultClassName, resultSize)) {
                    logger.debug(new BigSqlLog(statement.getId(), sqlString, params, resultSize, sqlResultMaxSize, took, timeOut, true, 1, true));
                } else {
                    logger.debug(new BigSqlLog(statement.getId(), sqlString, params, resultSize, sqlResultMaxSize, took, timeOut, true, 1, false));
                }
                recordAllSqlResultSize(statement, resultSize, parameterMap, parameters);
            }
            // 未加limit 记录,可能造成big-sql  类型为0
            if(CollectionUtils.isNotEmpty(ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG.getNoLimitRecordList())) {
                noLimitSqlRecord(statement.getId(), sqlString, params, resultClassName, resultSize, took, timeOut, true);
            }
            if(CollectionUtils.isNotEmpty(ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG.getNoCompanyWarehouseIdList())) {
                noCompanyWarehouseIdSqlRecord(statement.getId(), sqlString, params, resultClassName, resultSize, took, timeOut, true);
            }
            if(CollectionUtils.isNotEmpty(ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG.getCoreRecordList())) {
                coreSqlRecord(sqlString, params);
            }
            // 去除 ibatis 的 statementName, 配合 RayCloudStateFilter 使用的
            MDC.remove(MDCKeys.STATEMENT_NAME);
            // 去除 ibatis 的 resultClassName, 配合 MaxRowsAspect 使用的
            MDC.remove(MDCKeys.RESULT_CLASS_NAME);
        } catch (Exception e){
            logger.error("sql执行成功后处理数据失败!", e);
        }
    }

    public boolean maxRowLimit(MappedStatement statement, String sqlString, String resultClassName, Integer resultSize) {
        Object companyIdObj = MDC.get(MDCKeys.COMPANY_ID);
        for (SqlMaxRowConfig sqlMaxRowConfig : ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG.getMaxRowList()) {
            // 配置项目名是否符合
            if (!SqlMatchUtils.isContainApplication(sqlMaxRowConfig, companyIdObj, applicationName)) {
                continue;
            }
            if (!SqlMatchUtils.isContainStatement(sqlMaxRowConfig, statement.getId()) && !SqlMatchUtils.isContainTable(sqlMaxRowConfig, sqlString)) {
                continue;
            }
            // 返回值是自定义排除类型的,不会引起OOM,不进行设置
            if (ClassUtils.isSimpleClass(resultClassName, sqlMaxRowConfig.getExcludeResultClassStr())) {
                continue;
            }
            Integer maxRowLimit = ObjectUtils.defaultIfNull(sqlMaxRowConfig.getMaxRowLimit(), 0);
            Integer oomMaxRowLimit = ObjectUtils.defaultIfNull(sqlMaxRowConfig.getOomMaxRowLimit(), 0);
            // 返回的结果如果等于任意一个maxRow配置，则说明该SQL被配置了maxRow，并且进行了limit截断
            if (resultSize.equals(maxRowLimit) || resultSize.equals(oomMaxRowLimit)) {
                return true;
            }
        }
        return false;
    }

    public void recordAllSqlResultSize(MappedStatement statement, Integer resultSize, ParameterMap parameterMap, Object[] parameters) throws CacheException {
        try {
            Object companyId = MDC.get(MDCKeys.COMPANY_ID);
            //远程缓存 clueId + companyId  所有项目全局的--只用于提醒
            String allBigSqlKey = "big_sql_2_" + MDC.get(MDCKeys.CLUE_ID) + "_" + companyId;
            Integer allSqlResultMaxSize = acquireAllSqlResultMaxSize();
            // 10分钟过期
            if (!cache.add(allBigSqlKey, resultSize.toString(), 10 * 60)) {
                cache.incr(allBigSqlKey, resultSize.longValue());
            }
            // TODO 后续 incr 支持返回值后,这里废弃掉
            String resultSizeStr = cache.get(allBigSqlKey);
            Integer allSqlResultSize = StringUtils.isNotBlank(resultSizeStr) && StringUtils.isNumeric(resultSizeStr) ? Integer.parseInt(resultSizeStr) : 0;
            if (allSqlResultSize >= allSqlResultMaxSize) {
                logger.debug(new BigSqlLog(statement.getId(), "该clueId+companyId全部sql查询总和超过阈值", allBigSqlKey, allSqlResultSize, allSqlResultMaxSize, 0L, 0, true, 2, false));
            }
            // 远程缓存 clueId + companyId(可能为空) + application + ip;这里直接使用本地缓存,用clueId + companyId(可能为空) 即可
            String oomMaxRowKey = "oom_max_row_key_" + MDC.get(MDCKeys.CLUE_ID) + "_" + MDC.get(MDCKeys.COMPANY_ID);
            Integer localSqlResultSize = DruidSqlManageAspect.oomMaxRowFlagMap.getIfPresent(oomMaxRowKey);
            if (localSqlResultSize == null) {
                DruidSqlManageAspect.oomMaxRowFlagMap.put(oomMaxRowKey, resultSize);
            } else {
                DruidSqlManageAspect.oomMaxRowFlagMap.put(oomMaxRowKey, localSqlResultSize + resultSize);
            }
        } catch (Exception e){
            logger.error("记录全部sql查询big-sql数据失败!", e);
        }
    }

    private void beforeSqlExecute(StatementScope statementScope, Connection conn, String sqlString, Object[] parameters, RowHandlerCallback callback, Integer sqlResultMaxSize, Object[] args) {
        try {
            String url = conn.getMetaData().getURL();
            ParameterMap parameterMap = statementScope.getParameterMap();
            MappedStatement statement = statementScope.getStatement();
            ResultMap resultMap = statementScope.getResultMap();
            Class resultClass = resultMap.getResultClass();
            //pg库
            if (url.toLowerCase().contains("jdbc:postgresql") && sqlString.toLowerCase().contains(" like ")) {
                sqlString = sqlString.replaceAll(" like ", " ILIKE ");
                sqlString = sqlString.replaceAll(" LIKE ", " ILIKE ");
                args[2] = sqlString;
                logger.info(String.format("pgsql替换: %s", sqlString));
            }
            Object companyId = MDC.get(MDCKeys.COMPANY_ID);
            // 若  ec  lts  http dubbo 入口未设置company信息,从sql参数里面去
            if (companyId == null) {
                for (int i = 0; i < parameterMap.getParameterMappings().length; i++) {
                    ParameterMapping parameterMapping = parameterMap.getParameterMappings()[i];
                    if (StringUtils.isEmpty(parameterMapping.getPropertyName())){
                        continue;
                    }
                    String propertyLowerCase = parameterMapping.getPropertyName().toLowerCase();
                    if (propertyLowerCase.equalsIgnoreCase("companyId")) {
                        companyId = parameters[i];
                        MDC.put(MDCKeys.COMPANY_ID, companyId);
                    }
                }
            }
            // 设置 ibatis 的 statementName, 配合 RayCloudStateFilter 使用的
            MDC.put(MDCKeys.STATEMENT_NAME, statement.getId());
            // 设置 ibatis 的 resultClassName, 配合 MaxRowsAspect 使用的
            MDC.put(MDCKeys.RESULT_CLASS_NAME, resultClass.getName());

            warnSqlParamsOverMax(sqlString);
        } catch (Exception e) {
            logger.error("sql执行前处理数据失败!", e);
        }
    }

    private void warnSqlParamsOverMax(String sqlString) {
        int sqlLen = sqlString.length();
        if (sqlLen > SqlStringMaxSize) {
            int paramsSize = sqlLen - sqlString.replace("?", "").length();
            if (paramsSize > 10000) {
                logger.info(String.format("执行的SQL参数过多: sql长度=%s, sql参数个数=%s, sql=%s", sqlLen, paramsSize, sqlString));
            }
        }
    }

    private void noLimitSqlRecord(String statementName, String sqlString, String params, String resultClassName, Integer resultSize, Long took, Integer timeOut, Boolean success) {
        sqlString = sqlString.toLowerCase();
        int maxNoLimitSize = 1000;
        // 非查询语句 +  已加limit +  返回值是基础数据类型
        if (!sqlString.contains("select") || sqlString.contains(" limit ") || ClassUtils.isSimpleClass(resultClassName) || (BooleanUtils.isTrue(success) && resultSize < maxNoLimitSize)) {
            return;
        }
        Object companyIdObj = MDC.get(MDCKeys.COMPANY_ID);
        Map<String, String> tableColumnRuleMap = Maps.newHashMap();
        boolean noLimitFlag = false;
        for (SqlNoLimitConfig  sqlNoLimitConfig : ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG.getNoLimitRecordList()) {
            // 配置项目名是否符合
            if (!SqlMatchUtils.isContainApplication(sqlNoLimitConfig, companyIdObj, applicationName)) {
                continue;
            }
            if (!SqlMatchUtils.isContainTable(sqlNoLimitConfig, sqlString)) {
                continue;
            }
            // 返回值是自定义排除类型的,不会引起OOM,不进行设置
            if (ClassUtils.isSimpleClass(resultClassName, sqlNoLimitConfig.getExcludeResultClassStr())) {
                continue;
            }
            noLimitFlag = true;
            tableColumnRuleMap.put(sqlNoLimitConfig.getTableNameStartStr(), sqlNoLimitConfig.getExcludeColumnAccordStr());
            if (sqlNoLimitConfig.getNoLimitSize() != null && sqlNoLimitConfig.getNoLimitSize() > maxNoLimitSize) {
                maxNoLimitSize = sqlNoLimitConfig.getNoLimitSize();
            }
        }
        if (noLimitFlag && (BooleanUtils.isFalse(success) || resultSize >= maxNoLimitSize) && !SqlParserUtils.isTableColumnAccord(sqlString, tableColumnRuleMap)) {
            logger.debug(new BigSqlLog(statementName, sqlString, params, resultSize, maxNoLimitSize, took, timeOut, success, 0, false));
        }
    }

    private void noCompanyWarehouseIdSqlRecord(String statementName, String sqlString, String params, String resultClassName, Integer resultSize, Long took, Integer timeOut, Boolean success) {
        sqlString = sqlString.toLowerCase();
        int maxNoLimitSize = 2000;
        // 非查询语句 +  已加limit +  返回值是基础数据类型
        if ((sqlString.contains(" company_id ") && sqlString.contains(" warehouse_id ")) || ClassUtils.isSimpleClass(resultClassName) || (BooleanUtils.isTrue(success) && resultSize < maxNoLimitSize)) {
            return;
        }
        Object companyIdObj = MDC.get(MDCKeys.COMPANY_ID);
        Map<String, String> tableColumnRuleMap = Maps.newHashMap();
        boolean noLimitFlag = false;
        for (SqlNoLimitConfig  sqlNoLimitConfig : ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG.getNoCompanyWarehouseIdList()) {
            // 配置项目名是否符合
            if (!SqlMatchUtils.isContainApplication(sqlNoLimitConfig, companyIdObj, applicationName)) {
                continue;
            }
            if (!SqlMatchUtils.isContainTable(sqlNoLimitConfig, sqlString)) {
                continue;
            }
            // 返回值是自定义排除类型的,不会引起OOM,不进行设置
            if (ClassUtils.isSimpleClass(resultClassName, sqlNoLimitConfig.getExcludeResultClassStr())) {
                continue;
            }
            noLimitFlag = true;
            tableColumnRuleMap.put(sqlNoLimitConfig.getTableNameStartStr(), sqlNoLimitConfig.getExcludeColumnAccordStr());
            if (sqlNoLimitConfig.getNoLimitSize() != null && sqlNoLimitConfig.getNoLimitSize() > maxNoLimitSize) {
                maxNoLimitSize = sqlNoLimitConfig.getNoLimitSize();
            }
        }
        if (noLimitFlag && (BooleanUtils.isFalse(success) || resultSize >= maxNoLimitSize) && !SqlParserUtils.isTableColumnAccord(sqlString, tableColumnRuleMap)) {
            logger.debug(new BigSqlLog(statementName, sqlString, params, resultSize, maxNoLimitSize, took, timeOut, success, 3, false));
        }
    }


    private void coreSqlRecord(String sqlString, String params) {
        try {
            sqlString = sqlString.toLowerCase();
            String statementName = (String)MDC.get(MDCKeys.STATEMENT_NAME);
            Object companyIdObj = MDC.get(MDCKeys.COMPANY_ID);
            if (!sqlString.contains("select")) {
                return;
            }
            Map<String, String> tableColumnRuleMap = Maps.newHashMap();
            boolean statementFlag = false;
            for (SqlCoreRecordConfig coreRecordConfig : ConfigHolder.SQL_MANAGE_DIAMOND_CONFIG.getCoreRecordList()) {
                // 配置项目名是否符合
                if (!SqlMatchUtils.isContainApplication(coreRecordConfig, companyIdObj, applicationName)) {
                    continue;
                }
                // statementName
                if (SqlMatchUtils.isContainStatement(coreRecordConfig, statementName)) {
                    statementFlag = true;
                    break;
                }
                if (SqlMatchUtils.isContainTable(coreRecordConfig, sqlString) && StringUtils.isNotEmpty(coreRecordConfig.getColumnAccordStr())) {
                    tableColumnRuleMap.put(coreRecordConfig.getTableNameStartStr(), coreRecordConfig.getColumnAccordStr());
                }
            }
            //设置force_master
            if (statementFlag || SqlParserUtils.isTableColumnAccord(sqlString, tableColumnRuleMap)) {
                List<String> recordSqlList = splitRecordSql(sqlString, params);
                for (int i = 0; i < recordSqlList.size(); i++) {
                    logger.info(String.format("核心业务记录%s-%s[%s]", statementName, i, recordSqlList.get(i)));
                }
            }
        } catch (Exception e) {
            logger.error(String.format("核心业务记录sql报错,params:%s,sql %s", params, sqlString), e);
        }
    }

    private List<String> splitRecordSql(String sqlString, String params) {
        List<String> recordSqlList = Lists.newArrayList();
        try {
            String wholeSqlString = String.format("params: %s, sql: %s", params, sqlString);
            int size = wholeSqlString.length() % 950 == 0 ? (wholeSqlString.length() / 950) : (wholeSqlString.length() / 950 + 1);
            for (int j = 0; j < size; j++) {
                String subSqlStr;
                if (j < size - 1) {
                    subSqlStr = wholeSqlString.substring(j * 950, (j + 1) * 950);
                } else {
                    subSqlStr = wholeSqlString.substring(j * 950);
                }
                recordSqlList.add(subSqlStr);
            }
        } catch (Exception e) {
            logger.error(String.format("格式化记录报错, params:%s,sql %s", params, sqlString), e);
        }
        return recordSqlList;
    }
}
# 1.67.0
2018-12-26 by weixin


# 1.66.0
2018-12-20 by weixin


# 1.65.0
2018-12-5 by weixin





# 1.47.0
2018-09-27

* 【基础】针对账号可以配置供应商权限
* 【交易】快递单未打印增加重新审核
* 【交易】快递单未打印tab增加未分配快递订单数据
* 【交易】物流信息支持筛选未分配快递单
* 【交易】单品赠品规则同id设置最大赠送次数
* 【库存】新增调拨功能
* 【商品】商品模块所有页面和弹框查询搜索优化
* 【交易】异常订单处理增加一个单品排序或者多规格排序
* 
* 【修复】天猫接口改动，修复修改sku编码且上传平台，会将平台编码置空


# 1.46.0
2018-09-19

* 【交易】分配仓库增加京东仓直营发货的订单类型
* 【商品】智能采购单采购在途勾选加上，增加配置退款中和空单不采购
* 【波次】波次生成中完善跨区库生成条件
* 【交易】包装验货增加语音包
* 【波次】支持多波次同时播种，后置打印和播种打印都需要支持
* 【交易】按重量拆单多个平台商品对应同一个系统商品，会导致多锁定库存，商品数量计算不准确
* 【交易】按比例分配快递超500单问题
* 【库存】加工商品被处理成套件bug
* 【交易】异常订单库存有，但是订单在异常
* 【交易】异常订单套件转单品会取消异常


# 1.45.0
2018-09-18

* 商品 规格支持修改
* 商品 商品对应表编码支持为空或中文，不支持复制



# 1.44.0
2018-09-12

* 商品档案批量更新
* 客户商品档案搜索
* 补货优化
* 交易并发优化
* 波次订单商品数计算
* 预发货
* 批量返库
* 合单查询优化二期
* 套件转单品优化


# 1.43.0
2018-09-10

* 商品变更
* memcached ocs直连
* 波次bug修复


#1.42.0
2018-09-05

* 商品箱码支持一箱多品

```sql
CREATE TABLE `item_box` (
  `id` bigint(20) NOT NULL,
  `company_id` bigint(20) NOT NULL,
  `box_code` varchar(255) NOT NULL DEFAULT '' COMMENT '箱码',
  `box_num` bigint(20) NOT NULL DEFAULT '0',
  `type` varchar(64) NOT NULL DEFAULT 'single' COMMENT '类型:single 一箱一品 multipart 一箱多品',
  `x` double(8,2) DEFAULT '0.00' COMMENT '长',
  `y` double(8,2) DEFAULT '0.00' COMMENT '宽',
  `z` double(8,2) DEFAULT '0.00' COMMENT '高',
  `status` int(3) DEFAULT '1' COMMENT '箱码状态:0：停用 1：启用 ',
  `enable_status` int(3) DEFAULT '1' COMMENT '状态：0：删除 1：正常 ',
  `created` datetime DEFAULT '2000-01-01 00:00:00',
  `modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `company_id_idx` (`company_id`,`box_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

create table `item_box_0` like item_box;
create table `item_box_1` like item_box;
create table `item_box_2` like item_box;
create table `item_box_3` like item_box;
create table `item_box_4` like item_box;
create table `item_box_5` like item_box;
create table `item_box_6` like item_box;
create table `item_box_7` like item_box;
create table `item_box_8` like item_box;
create table `item_box_9` like item_box;
create table `item_box_10` like item_box;
create table `item_box_11` like item_box;
create table `item_box_12` like item_box;
create table `item_box_13` like item_box;
create table `item_box_14` like item_box;
create table `item_box_15` like item_box;
create table `item_box_16` like item_box;
create table `item_box_17` like item_box;
create table `item_box_18` like item_box;
create table `item_box_19` like item_box;

DROP TABLE IF EXISTS `item_box_single`;
CREATE TABLE `item_box_single` (
  `id` bigint(20)  UNSIGNED AUTO_INCREMENT,
  `company_id` bigint(20) NOT NULL ,
  `created` datetime NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
  `modified` datetime NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '修改时间',
  `enable_status` int(2) NOT NULL DEFAULT '1' COMMENT '逻辑删除 0 删除 1 存在',
  `item_box_id` bigint(20) not null default 0 comment '商品箱id',
  `nums` bigint(20) not null default 0 comment '商品数',
  `sys_item_Id` bigint(20) NOT NULL  COMMENT '商品系统id',
  `sys_sku_Id` bigint(20) NOT NULL DEFAULT 0 COMMENT '商品系统sku id',
  `title` varchar(1024) DEFAULT NULL comment '商品名称',
  `short_title` varchar(64) DEFAULT '' comment '商品简称',
  `outer_id` varchar(255) DEFAULT '' comment '商品商家编码',
  `properties_name` varchar(1024) DEFAULT '' COMMENT '商品规格名',
  `properties_alias` varchar(1024) DEFAULT '' COMMENT '商品规格别名',
  `remark` varchar(128) DEFAULT '' comment' 商品备注',
  `pic_path` varchar(1024) DEFAULT '' comment'图片链接',
  PRIMARY KEY (`id`),
  KEY `company_id_item` (`company_id`,`sys_item_Id`),
  KEY `company_id_item_sku` (`company_id`,`sys_sku_Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `item_box_single_0`;
create table `item_box_single_0` like `item_box_single`;
```


# 1.34.0
2018-08-01

* 商品批量修改套件
* 订单新增分销金额

```sql
ALTER TABLE trade  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_0  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_1  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_2  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_3  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_4  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_5  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_6  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_7  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_8  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_9  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_10  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_11  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_12  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_13  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_14  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_15  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_16  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_17  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_18  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_19  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_20  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_21  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_22  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_23  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_24  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_25  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_26  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_27  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_28  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_29  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_30  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_31  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_32  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_33  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_34  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_35  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_36  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_37  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_38  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_39  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_40  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_41  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_42  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_43  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_44  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_45  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_46  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_47  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_48  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_49  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_50  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_51  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_52  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_53  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_54  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_55  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_56  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_57  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_58  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_59  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_60  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_61  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_62  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_63  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_64  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_65  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_66  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_67  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_68  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_69  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_70  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_71  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_72  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_73  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_74  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_75  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_76  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_77  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_78  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_79  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_80  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_81  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_82  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_83  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_84  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_85  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_86  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_87  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_88  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_89  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_90  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_91  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_92  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_93  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_94  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_95  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_96  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_97  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_98  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE trade_99  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order` add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order` add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_0`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_0`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_1`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_1`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_2`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_2`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_3`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_3`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_4`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_4`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_5`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_5`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_6`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_6`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_7`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_7`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_8`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_8`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_9`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_9`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_10`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_10`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_11`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_11`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_12`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_12`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_13`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_13`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_14`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_14`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_15`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_15`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_16`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_16`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_17`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_17`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_18`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_18`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_19`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_19`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_20`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_20`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_21`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_21`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_22`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_22`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_23`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_23`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_24`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_24`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_25`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_25`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_26`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_26`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_27`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_27`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_28`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_28`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_29`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_29`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_30`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_30`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_31`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_31`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_32`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_32`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_33`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_33`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_34`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_34`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_35`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_35`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_36`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_36`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_37`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_37`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_38`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_38`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_39`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_39`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_40`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_40`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_41`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_41`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_42`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_42`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_43`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_43`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_44`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_44`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_45`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_45`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_46`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_46`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_47`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_47`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_48`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_48`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_49`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_49`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_50`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_50`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_51`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_51`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_52`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_52`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_53`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_53`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_54`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_54`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_55`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_55`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_56`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_56`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_57`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_57`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_58`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_58`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_59`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_59`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_60`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_60`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_61`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_61`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_62`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_62`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_63`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_63`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_64`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_64`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_65`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_65`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_66`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_66`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_67`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_67`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_68`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_68`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_69`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_69`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_70`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_70`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_71`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_71`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_72`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_72`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_73`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_73`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_74`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_74`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_75`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_75`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_76`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_76`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_77`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_77`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_78`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_78`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_79`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_79`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_80`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_80`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_81`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_81`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_82`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_82`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_83`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_83`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_84`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_84`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_85`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_85`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_86`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_86`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_87`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_87`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_88`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_88`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_89`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_89`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_90`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_90`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_91`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_91`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_92`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_92`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_93`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_93`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_94`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_94`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_95`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_95`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_96`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_96`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_97`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_97`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_98`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_98`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_99`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_99`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_100`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_100`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_101`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_101`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_102`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_102`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_103`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_103`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_104`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_104`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_105`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_105`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_106`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_106`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_107`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_107`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_108`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_108`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_109`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_109`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_110`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_110`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_111`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_111`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_112`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_112`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_113`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_113`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_114`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_114`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_115`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_115`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_116`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_116`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_117`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_117`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_118`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_118`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_119`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_119`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_120`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_120`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_121`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_121`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_122`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_122`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_123`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_123`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_124`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_124`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_125`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_125`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_126`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_126`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_127`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_127`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_128`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_128`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_129`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_129`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_130`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_130`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_131`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_131`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_132`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_132`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_133`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_133`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_134`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_134`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_135`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_135`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_136`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_136`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_137`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_137`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_138`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_138`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_139`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_139`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_140`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_140`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_141`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_141`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_142`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_142`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_143`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_143`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_144`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_144`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_145`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_145`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_146`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_146`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_147`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_147`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_148`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_148`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_149`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_149`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_150`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_150`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_151`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_151`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_152`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_152`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_153`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_153`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_154`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_154`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_155`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_155`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_156`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_156`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_157`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_157`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_158`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_158`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_159`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_159`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_160`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_160`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_161`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_161`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_162`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_162`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_163`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_163`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_164`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_164`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_165`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_165`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_166`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_166`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_167`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_167`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_168`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_168`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_169`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_169`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_170`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_170`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_171`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_171`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_172`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_172`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_173`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_173`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_174`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_174`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_175`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_175`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_176`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_176`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_177`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_177`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_178`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_178`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_179`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_179`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_180`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_180`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_181`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_181`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_182`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_182`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_183`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_183`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_184`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_184`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_185`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_185`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_186`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_186`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_187`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_187`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_188`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_188`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_189`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_189`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_190`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_190`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_191`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_191`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_192`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_192`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_193`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_193`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_194`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_194`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_195`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_195`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_196`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_196`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_197`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_197`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_198`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_198`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_199`  add COLUMN sale_price VARCHAR (30) DEFAULT NULL  AFTER cost;
ALTER TABLE  `order_199`  add COLUMN sale_fee VARCHAR (30) DEFAULT NULL  AFTER cost;

```


# 1.31.0-SNAPSHOT
2018-07-19

* 实现会话的重构

# 1.30.2
2018-07-18

* 商品套件导入sku类型错误修复。type由0改为1.
* 商品供应商优化。
* 商品对应关系优化。

# 1.30.0
2018-07-16

* 多平台对接京东厂家直送业务

# 1.28.0
2018-07-11

* 快速出库
* 接力拣选
* 对接中通


# 1.27.0
2018-07-05
*  【流行色彩坐标】已打印订单后台已发货，订单商品部分退款，会进入到异常订单显示退，验货拦截，后台同意退款后再验货还是会出现那个退款成功的商品，并且该订单无退款标记，日志无记录，覆盖模式。系统订单号：1777989494294530
*  【凡程商贸有限公司】 订单号：77909296198056，明明是2种商品，显示的是1种

# 1.26.0
2018-07-03


# 1.25.0
2018-07-02
* 【盐城龙凤极光照明有限公司】 订单号146319400429253138 订单上传失败，显示 CD01#发货方式不匹配 用户这个是家装类的订单
*   【交易】导出订单明细时，如果是套件，期望也能把套件的商品明细导出来。
*   (深圳市乐其酷)【交易】客户这边创建快递的分配策略里面需要按照订单商品数量进行匹配，比如超过两件发韵达。
*   橡皮筋 交易--自动化设置 分配仓库 增加重量这个选择
*  【交易】增加一个配置，在订单新增保存时，会把订单的优惠按商品的实付金额比例分摊到相应的商品金额上面
bug
*   波次打印，复验是点击覆盖弹窗异常
*  修复批量添加货位时，提示货位重复


# 1.24.0
2018-06-28
* 【交易】赠品匹配详情查看
* 【交易】后置打印可扫描识别码
* 库存导入
* 店铺删除后，清除平台店铺商品信息

```sql
ALTER TABLE trade_config ADD COLUMN `post_open_ident_code` TINYINT(4) DEFAULT 0 NOT NULL AFTER `open_ident_code`;
ALTER TABLE gift_promotion_match_trade_log ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
ALTER TABLE gift_promotion_match_trade_log_0 ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
ALTER TABLE gift_promotion_match_trade_log_1 ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
ALTER TABLE gift_promotion_match_trade_log_2 ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
ALTER TABLE gift_promotion_match_trade_log_3 ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
ALTER TABLE gift_promotion_match_trade_log_4 ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
ALTER TABLE gift_promotion_match_trade_log_5 ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
ALTER TABLE gift_promotion_match_trade_log_6 ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
ALTER TABLE gift_promotion_match_trade_log_7 ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
ALTER TABLE gift_promotion_match_trade_log_8 ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
ALTER TABLE gift_promotion_match_trade_log_9 ADD COLUMN order_id BIGINT(20) DEFAULT -1 AFTER sid;
```

# 1.23.0
2018-06-27
* 自动分配货位库存机制

# 1.22.2
2018-06-27
* 自动化标签匹配bug

# 1.22.1
2018-06-25
* 京东备注、标签匹配空指针

# 1.22.0
2018-06-25
* 库存上传。
* 标签支持删除，清空
* 订单审核执行对应的标签规则
* 商品导入未分类优化
* 商品多匹配，显示系统商品规格别名

# 1.21.0
2018-06-25

* 波次生成条件增加自定义标签
* 并发导致重复插入生成两条波次
* 波次打印条件多，引起的卡顿
* 顺丰仓对接。


# 1.20.1
2018-06-22
* 【库存】有库存显示缺货。修复



# 1.20.0
2018-06-21
* 【交易】新建手工单支持输入平台订单号。
* 【交易】设置邮费，理论运费。
* 【商品】平台商品标记了套件标识，批量复制过滤修复。


# 1.19.9
2018-06-13
* 多平台易订货对接。



# 1.19.8
2018-06-20
* 升级了事件中心的版本号为1.3.2-SNAPSHOT，并新增了ec-builder的模块

# 1.19.7
2018-06-13
* 商品系统分类添加子分类报错修复。
* 苏宁店铺商品库存上传报错。修复
* 交易订单匹配商品条件，传套件里单品的cost给订单。


# 1.19.2
2018-06-13
* 拼多多店铺上传无店铺权限错误

# 1.19.1
2018-06-13
* 自助修改地址加日志

# 1.19.0
2018-06-13

* 【波次】波次新增一列库区显示
* 【仓储】优化修复货位数量字段重新打印不出来问题
* 【交易】拆分订单修改地址进入异常
* 【交易】自动审核添加地址条件
* 【交易】excel导入支持套件
* 【交易】修改标签局部刷新

```sql
alter table wave add section_areas VARCHAR(256) default '' comment '波次订单的库区';
alter table wave_0 add section_areas VARCHAR(256) default '' comment '波次订单的库区';
alter table wave_1 add section_areas VARCHAR(256) default '' comment '波次订单的库区';
alter table wave_2 add section_areas VARCHAR(256) default '' comment '波次订单的库区';
alter table wave_3 add section_areas VARCHAR(256) default '' comment '波次订单的库区';
alter table wave_4 add section_areas VARCHAR(256) default '' comment '波次订单的库区';
alter table wave_5 add section_areas VARCHAR(256) default '' comment '波次订单的库区';
alter table wave_6 add section_areas VARCHAR(256) default '' comment '波次订单的库区';
alter table wave_7 add section_areas VARCHAR(256) default '' comment '波次订单的库区';
alter table wave_8 add section_areas VARCHAR(256) default '' comment '波次订单的库区';
alter table wave_9 add section_areas VARCHAR(256) default '' comment '波次订单的库区';

```



# gray3_upline
2018-05-31
 * 【交易】拆分订单修改地址标记异常
 * 【仓储】支持批量生成含有字母的货位编码
 * 【打印】一单多包重新打印逻辑优化

# 1.18.0
  2018-06-07
  
  * 【商品】填充商品编码需求。
  * 【商品】修复纯商品设置分销价无效问题。
  * 【商品】修复平台纯商品新增了sku，批量复制到系统商品图片的时候，不会把主商品图片复制过来。
  * 【商品】允许分销价设置为0。
  * 【商品】对规格编码只填空格校验。
  * 【商品】商品重构。
  * 【商品】自动计算批发价字段
  * 【交易】 拆分订单实付也拆分
  * 【交易】 京东拼团中订单不进入

 ```sql
 alter table dmj_sku add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_0 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_1 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_2 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_3 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_4 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_5 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_6 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_7 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_8 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_9 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_10 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_11 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_12 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_13 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_14 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_15 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_16 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_17 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_18 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_19 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_20 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_21 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_22 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_23 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_24 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_25 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_26 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_27 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_28 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_29 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_30 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_31 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_32 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_33 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_34 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_35 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_36 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_37 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_38 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_39 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_40 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_41 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_42 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_43 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_44 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_45 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_46 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_47 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_48 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_sku_49 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_0 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_1 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_2 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_3 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_4 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_5 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_6 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_7 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_8 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_9 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_10 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_11 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_12 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_13 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_14 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_15 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_16 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_17 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_18 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 alter table dmj_item_19 add `is_sys_wholesale_price` int(4) default 0 COMMENT '记录是否系统计算批发价 0-否 1-是';
 ```
# 1.17.2
2018-06-05

* 【交易】规格属性搜索

# 1.17.1
2018-06-05

* 【交易】返回给前端平台规格换字段

# 1.17.0
2018-06-04

* 【波次】波次生成规则增加选项
* 【交易】同步切换
* 【交易】订单自动标签
* 【交易】平台规格显示
* 【交易】订单导出sku商家编码
* 【客户】客户模块审核和排序的bug

````sql
ALTER TABLE trade_config ADD COLUMN show_sku_properties TINYINT(4) DEFAULT 0;

ALTER TABLE trade_1 ADD COLUMN tag_ids VARCHAR(1024) DEFAULT '';

INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',0,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',8,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',21,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',22,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',23,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',25,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',26,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',9,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',27,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',50,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',51,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',60,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',20,0,20,150);
INSERT INTO  page_column_conf (col_code,col_title,page_id,is_default,sort_no,width) VALUES ('tagName','标签',61,0,20,150);

CREATE TABLE `trade_tag` (
  `id` BIGINT(20) DEFAULT NULL,
  `company_id` BIGINT(20) DEFAULT NULL,
  `tag_name` VARCHAR(88) DEFAULT NULL,
  `created` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00',
  `modified` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP
) ENGINE=INNODB DEFAULT CHARSET=utf8;


CREATE TABLE `trade_tag_rule` (
  `id` BIGINT(20) NOT NULL,
  `tag_id` BIGINT(20) NOT NULL,
  `company_id` INT(10) NOT NULL,
  `priority` INT(10) NOT NULL,
  `is_open` TINYINT(4) NOT NULL DEFAULT '1',
  `condition_desc` TEXT DEFAULT NULL,
  `auto_halt` TINYINT(4) DEFAULT '0',
  `enable_status` TINYINT(4) DEFAULT '1',
  `created` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00',
  `modified` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE `trade_tag_rule_0` (
  `id` BIGINT(20) NOT NULL,
  `tag_id` BIGINT(20) NOT NULL,
  `company_id` INT(10) NOT NULL,
  `priority` INT(10) NOT NULL,
  `is_open` TINYINT(4) NOT NULL DEFAULT '1',
  `condition_desc` TEXT DEFAULT NULL,
  `auto_halt` TINYINT(4) DEFAULT '0',
  `enable_status` TINYINT(4) DEFAULT '1',
  `created` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00',
  `modified` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE `trade_tag_rule_1` (
  `id` BIGINT(20) NOT NULL,
  `tag_id` BIGINT(20) NOT NULL,
  `company_id` INT(10) NOT NULL,
  `priority` INT(10) NOT NULL,
  `is_open` TINYINT(4) NOT NULL DEFAULT '1',
  `condition_desc` TEXT DEFAULT NULL,
  `auto_halt` TINYINT(4) DEFAULT '0',
  `enable_status` TINYINT(4) DEFAULT '1',
  `created` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00',
  `modified` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE `trade_tag_rule_2` (
  `id` BIGINT(20) NOT NULL,
  `tag_id` BIGINT(20) NOT NULL,
  `company_id` INT(10) NOT NULL,
  `priority` INT(10) NOT NULL,
  `is_open` TINYINT(4) NOT NULL DEFAULT '1',
  `condition_desc` TEXT DEFAULT NULL,
  `auto_halt` TINYINT(4) DEFAULT '0',
  `enable_status` TINYINT(4) DEFAULT '1',
  `created` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00',
  `modified` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE `trade_tag_rule_3` (
  `id` BIGINT(20) NOT NULL,
  `tag_id` BIGINT(20) NOT NULL,
  `company_id` INT(10) NOT NULL,
  `priority` INT(10) NOT NULL,
  `is_open` TINYINT(4) NOT NULL DEFAULT '1',
  `condition_desc` TEXT DEFAULT NULL,
  `auto_halt` TINYINT(4) DEFAULT '0',
  `enable_status` TINYINT(4) DEFAULT '1',
  `created` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00',
  `modified` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8;

CREATE TABLE `trade_tag_rule_4` (
  `id` BIGINT(20) NOT NULL,
  `tag_id` BIGINT(20) NOT NULL,
  `company_id` INT(10) NOT NULL,
  `priority` INT(10) NOT NULL,
  `is_open` TINYINT(4) NOT NULL DEFAULT '1',
  `condition_desc` TEXT DEFAULT NULL,
  `auto_halt` TINYINT(4) DEFAULT '0',
  `enable_status` TINYINT(4) DEFAULT '1',
  `created` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00',
  `modified` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8;
````

# 1.16.0
2018-05-31

* 【仓储】修复批量添加货位校验问题
* 【仓储】移库优化
* 【交易】订阅物流修改安能快递


# 1.15.0
2018-05-28
 * 【交易】修复订单导入报错就不能再次导入
 * 【交易】优化称重流程
 * 【仓储】移库优化

# 1.14.2
2018****-05-25
 * 【交易】按比例分配快递修复

# 1.14.1
2018-05-25
 * 【交易】注释订单查询和导出上传淘宝日志 

# 1.14.0
2018-05-24

* 【仓储】货位编码排层位置号支持字母
* 【仓储】提供校验仓储开关校验dubbo接口
* 【交易】称重运费设置优化
* 【交易】批量取消合单
* 【商品】b方案生成12位商品编码工具

1.12.1
* 【交易】分销价计算bug

# 1.12.0
* 【交易】赠品规则显示两个方案优化
* 【交易】按重量拆分拆出平台订单

# 1.11.0
* 【交易】播种打印商品明细增加字段，订单增加备注留言高亮
* 【交易】支持旺旺昵称批量查询
* 【波次】在播种时在支持商家编码和位置号一起扫描判断


# 1.10.0
* 【交易】按比例分配快递
* 【多平台】阿里金额计算算法修改

# 1.9.1
* 【波次】无线打印相关dubbo接口

# 1.9.0
2018-05-15

## erp-core
版本：1.9.0
* 【交易】平台取消退款的订单系统自动取消退款异常
* 【交易】关闭订单同时关闭子订单。
* 【交易】仓库匹配增加商品明细。
* 【仓储】多商品货位所有商品清空以后无法用PDA一次性盘空
* 【波次】修复波次打印按模板排序卡死的问题

# 1.8.0
2018-05-10

* 【商品】重复铺货填充编码，新增按平台规格备注相同的填充。
* 【商品】修改平台商品编码，不会去覆盖之前的商品备注。
* 【商品】批量填充别名，新增以淘宝规格备注来填充到规格别名里。
* 【商品】批量新增图片，增加两个逻辑。目前一共4种情况都会增加。平台纯-系统纯，系统sku。平台sku-系统纯，系统sku。
* 【波次】在波次条件中增加一个条件是否加急，这样用户可以把加急的订单优先处理
* 【波次】波次条件增加一个，根据货位排序来生成波次



# 1.7.0
* 【交易】赠品规则--按数量促销，添加不参与商品
* 【交易】赠品的类型增加一种，可以根据留言或者备注进行匹配赠品，功能可以参考快递智能匹配
* 【交易】实付金额逻辑优化：当订单中子订单交易关闭后，实付金额踢出交易关闭的金额
* 【库存】可以批量设置代购字眼的SKU上传规则，如不参与上传等操作
* 【库存】去掉负库存开启库存上传强制校验

 ```sql
ALTER TABLE gift_promotion_rule ADD COLUMN `condition_desc` VARCHAR(128) DEFAULT '', ADD COLUMN `is_in_promotion` TINYINT(4) DEFAULT 1;
ALTER TABLE gift_promotion_rule_0 ADD COLUMN `condition_desc` VARCHAR(128) DEFAULT '', ADD COLUMN `is_in_promotion` TINYINT(4) DEFAULT 1;
ALTER TABLE gift_promotion_rule_1 ADD COLUMN `condition_desc` VARCHAR(128) DEFAULT '', ADD COLUMN `is_in_promotion` TINYINT(4) DEFAULT 1;
ALTER TABLE gift_promotion_rule_2 ADD COLUMN `condition_desc` VARCHAR(128) DEFAULT '', ADD COLUMN `is_in_promotion` TINYINT(4) DEFAULT 1;
ALTER TABLE gift_promotion_rule_3 ADD COLUMN `condition_desc` VARCHAR(128) DEFAULT '', ADD COLUMN `is_in_promotion` TINYINT(4) DEFAULT 1;
ALTER TABLE gift_promotion_rule_4 ADD COLUMN `condition_desc` VARCHAR(128) DEFAULT '', ADD COLUMN `is_in_promotion` TINYINT(4) DEFAULT 1;
```

# 1.6.0
* 商品打印高亮优化
* 交易赠品、套件优化
* 修复波次打印订单明细的位置号乱序问题
* 仓储相关页面按商家编码查询没有按照商家编码最小维度查询
* 订单套件转单品

# 1.5.4
* 系统备注查询sql报错

# 1.5.3
* 回滚合单查询优化

# 1.5.2
2018-05-03
* 订单增加折扣率

# 1.5.1
2018-05-03
* 订单增加折扣率

# 1.5.0
2018-05-03
 
* 同链接填充规格编码和重复铺货填充编码。
* 取消标记套件功能。
* 编码上传/不上传设置。
* 详细记录日志功能。
* 高亮显示后端已经做完，可以上线，不影响正常逻辑。前端下次上。
* 库存出入库记录界面，进行分页改为三个月之前和三个月之内的。都可以支持查询和导出
* 库存导入次品数bug修复
* 货位编码自定义配置
* 订单导出增加平台订单号、理论运费、仓库名称
* 订单增加折扣率
* 导入订单商家编码不大小写敏感

SQL脚本
 ```sql
* erp_trades
* ALTER TABLE order add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_0 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_1 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_2 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_3 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_4 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_5 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_6 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_7 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_8 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_9 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_10 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_11 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_12 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_13 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_14 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_15 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_16 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_17 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_18 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_19 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_20 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_21 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_22 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_23 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_24 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_25 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_26 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_27 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_28 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_29 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_30 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_31 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_32 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_33 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_34 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_35 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_36 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_37 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_38 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_39 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_40 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_41 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_42 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_43 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_44 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_45 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_46 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_47 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_48 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_49 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_50 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_51 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_52 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_53 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_54 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_55 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_56 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_57 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_58 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_59 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_60 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_61 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_62 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_63 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_64 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_65 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_66 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_67 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_68 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_69 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_70 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_71 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_72 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_73 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_74 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_75 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_76 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_77 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_78 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_79 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_80 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_81 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_82 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_83 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_84 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_85 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_86 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_87 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_88 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_89 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_90 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_91 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_92 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_93 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_94 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_95 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_96 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_97 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_98 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_99 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_100 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_101 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_102 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_103 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_104 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_105 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_106 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_107 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_108 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_109 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_110 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_111 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_112 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_113 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_114 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_115 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_116 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_117 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_118 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_119 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_120 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_121 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_122 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_123 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_124 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_125 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_126 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_127 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_128 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_129 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_130 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_131 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_132 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_133 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_134 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_135 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_136 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_137 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_138 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_139 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_140 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_141 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_142 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_143 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_144 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_145 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_146 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_147 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_148 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_149 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_150 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_151 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_152 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_153 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_154 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_155 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_156 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_157 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_158 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_159 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_160 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_161 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_162 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_163 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_164 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_165 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_166 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_167 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_168 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_169 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_170 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_171 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_172 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_173 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_174 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_175 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_176 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_177 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_178 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_179 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_180 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_181 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_182 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_183 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_184 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_185 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_186 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_187 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_188 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_189 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_190 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_191 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_192 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_193 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_194 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_195 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_196 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_197 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_198 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
  ALTER TABLE order_199 add COLUMN `discount_rate` DOUBLE(6,2) DEFAULT 100;
* alter table item_config add column (upload_plat_form TINYINT(4) NOT NULL DEFAULT 0 COMMENT '0:不上传到平台,1:上传到平台');
* CREATE TABLE `item_match_rule` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `company_id` bigint(20) NOT NULL ,
    `rule` varchar(1024) NOT NULL DEFAULT '' COMMENT '规则，多个规格用逗号分隔',
    `rule_type` int(4) NOT NULL DEFAULT '1' COMMENT '1-填充商品编码',
    `created` datetime NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',
    `modified` datetime NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE `company_id_type` (`company_id`,`rule_type`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
  
alter table wms_config add `code_rule_type` int(11) NOT NULL DEFAULT '0' COMMENT '货位编码规则，0：默认，1：自定义', add `region_limit_num` int(11) DEFAULT '2' COMMENT '库区限制位数', add `shelf_limit_num` int(11) DEFAULT '2' COMMENT '排数限制位数', add `level_limit_num` int(11) DEFAULT '1' COMMENT '层数限制位数', add `pos_limit_num` int(11) DEFAULT '3' COMMENT '位置号限制位数';
  
```




# 1.4.1
## erp-core
2018-05-02

* 修复波次单个商品跨库区计算问题


# 1.4.0
## erp-core
2018-05-02

* 增加统一异常处理器
* 波次计算规则重构
* 波次新增生成波次时，过滤虚拟商品不参与条件计算
* 【义乌市凡乐贸易】波次详情增加拣货员账号显示，拣选开启和完结时间，可以点击波次号查看波次内订单商品拣选单详情 
* 【波次】在波次规则中增加库区的判断，可以标记是否为跨库区拣货 
* 合单查询优化，所有使用交易接口的查询


# 1.3.4
## erp-core
2018-4-27

* 修复打印模块数据迁移数据库bug

# 1.3.3
## erp-core
2018-04-26

* 修复商品属性表 颜色新增查询报错。修复删除平台sku报错。

# 1.3.2
## erp-core
2018-04-26

* 修复智能采购方案一、二分页报错


# 1.3.1
## erp-core
2018-04-25

* 修复pda盘点、补货订单依然缺货问题


# 1.3.0
## erp-core
2018-04-24

* 智能采购规则，支持保存多条
* 仓储事务事件优化
****

# 1.2.0
## erp-core
2018-04-19 
* 【交易】增加财审指定店铺配置
* 【商品】修复商品导入空指针。
* 【商品】对应表复制，已经匹配的系统商品，还可以和平台商品匹配。

#### SQL脚本
 ```sql
 CREATE TABLE `finance_audit_config` (  `company_id` bigint(20) NOT NULL,  `user_ids_str` varchar(512) NOT NULL COMMENT '店铺userId拼接',  `created` timestamp NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '创建时间',  `modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',  `enable_status` tinyint(4) DEFAULT '1',  PRIMARY KEY (`company_id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8;
 ```

# 1.1.1
* 【交易】修复物流跟踪配置少加字段

# 1.1.0

2018-04-17 
 
*【商品】跨境店铺支持按照规格名称去匹配。
*【商品】系统修改平台店铺分类，实时更新。
*【商品】套件添加商品时候，去除虚拟商品。
*【商品】商品导入时，如果商品编码不变，不会修改交易订单的商品编码和图片
*【打印】拿货单目录页新增字段并支持按照买家维度信息统计
*【交易】一单多包上传平台备注
*【交易】增加物流异常类型
*【交易】修复一包多单导出订单明细无子单号
*【交易】修复极速退款订单不进入异常
*【交易】上传发货优化
*【库存】出入库记录慢sql优化
*【仓储】修复批量移库中调整可用数，当移库条件为错误状态，点击批量完成的时候会报错
 
SQL脚本
 ```sql
 update erp_trades.getter_template set template_fields_description = '[{"title":"拿货汇总目录","menuFieldList":[{"description":"供应商总数","field":"supplierNum"},{"description":"订单总数","field":"tradeNum"},{"description":"缺货成本合计","field":"tradeLackCostNum"},{"description":"打印时间","field":"tradePrintTime","options":[{"name":"精确到秒","value":"second"},{"name":"精确到日","value":"day"}]},{"description":"打印人","field":"printStaffName"}]},{"title":"表格字段","container":"tbody","menuFieldList":[{"description":"供应商名称","field":"supplierName"},{"description":"拿货商品种类","field":"lackItemTypeNum"},{"description":"拿货商品数量","field":"lackItemNum"},{"description":"拿货商品成本","field":"lackItemCostNum"},{"description":"买家旺旺","field":"buyerNick"},{"description":"收件人姓名","field":"buyerName"},{"description":"全部商品种类","field":"itemTypeNum"},{"description":"全部商品数量","field":"itemNum"},{"description":"有货商品种类","field":"inStockItemTypeNum"},{"description":"有货商品数量","field":"inStockItemNum"}]}]' where id = 3;
 
 ALTER TABLE trade_config ADD COLUMN open_multioutsid_upload TINYINT(4) DEFAULT '0';
 
 ALTER TABLE `trade_config` ADD COLUMN `open_re_upload_consign` tinyint(4) DEFAULT 0 COMMENT '是否开启重新上传发货：0:关闭 1:开启' AFTER `open_upload_consign`;
 ```

# 1.0.0
2018-04-11 
 
*【仓储/库存/交易】批次管理生产日期管理
*【仓储/库存/交易】批次管理对于调拨页面的影响，调拨页面前端屏蔽
*【商品】批次管理生产日期管理
*【库存】针对查询的结果做高亮处理
*【仓储/库存/交易/库存】各模块封核代码整理
* 核心模块的封核



package com.raycloud.dmj.jd.logistics.builder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.open.api.sdk.request.wujiemiandan.LdopAlphaWaybillApiUnbindRequest;
import com.jd.open.api.sdk.request.wujiemiandan.LdopAlphaWaybillReceiveRequest;
import com.jd.open.api.sdk.request.wujiemiandan.LdopAlphaWaybillUnbindRequest;
import com.raycloud.dmj.domain.pt.OutSidPool;
import com.raycloud.dmj.domain.pt.TemplateBranch;
import com.raycloud.dmj.domain.pt.TemplateBranchAddress;
import com.raycloud.dmj.domain.pt.enums.EnumFieldValueName;
import com.raycloud.dmj.domain.pt.enums.EnumServiceValueName;
import com.raycloud.dmj.domain.pt.model.waybill.bean.SenderAddress;
import com.raycloud.dmj.domain.pt.utils.TemplateUtil;
import com.raycloud.dmj.domain.pt.wlb.BranchAccount;
import com.raycloud.dmj.domain.pt.wlb.FieldValue;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trades.ExpressCompany;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.jd.logistics.enums.JdSalePlatformEnum;
import com.raycloud.dmj.jd.logistics.enums.JdTemplateEnum;
import com.raycloud.dmj.jd.logistics.model.JdAlphaWaybillReceiveDTO;
import com.raycloud.dmj.jd.logistics.model.JdExtendAttributeDTO;
import com.raycloud.dmj.jd.logistics.model.JdServiceDTO;
import com.raycloud.dmj.jd.logistics.model.WaybillAddressDTO;
import com.raycloud.dmj.jd.logistics.request.AlphaAppendRequest;
import com.raycloud.dmj.jd.logistics.request.ErpLdopAlphaWaybillReceiveRequest;
import com.raycloud.dmj.jd.logistics.utils.JdEclpLogisticsServiceUtil;
import com.raycloud.dmj.jd.logistics.utils.JdWaybillUtil;
import com.raycloud.dmj.service.business.waybill.context.PackageStrategyContext;
import com.raycloud.dmj.service.business.waybill.context.WaybillMultiTemplateContext;
import com.raycloud.dmj.service.business.waybill.context.WaybillTemplateContext;
import com.raycloud.dmj.service.util.ConvertUtil;
import com.raycloud.dmj.service.util.DataUtil;
import com.raycloud.dmj.service.util.PrintTemplateConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import com.raycloud.dmj.jd.logistics.enums.JdExpressEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 京东无界请求参数构建器
 *
 * @Date 2021/11/5
 * <AUTHOR>
 */
@Slf4j
public class JdAlphaRequestBuilder {

    /**
     * 构建获取面单请求参数
     * - 兼容 获取快递号、一单多包（传加包数量）
     *
     * @param templateContext 面单模板容器
     * @param trade           交易单
     * @param index           索引号
     * @return LdopAlphaWaybillReceiveRequest 京东无界获取面单请求参数
     * <AUTHOR>
     * @date 2021/11/5
     */
    public static ErpLdopAlphaWaybillReceiveRequest buildGetRequest(WaybillTemplateContext templateContext, Trade trade, Integer index) {
        User user = templateContext.getUser();
        if (user == null) {
            throw new IllegalArgumentException("用户信息缺失,请检查入参");
        }
        SenderAddress senderAddress = templateContext.getSenderAddress();
        UserWlbExpressTemplate userWlbExpressTemplate = templateContext.getUserWlbExpressTemplate();
        Map<Long, Integer> waybillCountMap = templateContext.getWaybillCountMap();

        // 请求对象
        ErpLdopAlphaWaybillReceiveRequest request = new ErpLdopAlphaWaybillReceiveRequest();

        // 构建京东快递获取面单号请求参数
        JdAlphaWaybillReceiveDTO alphaWaybillReceive = new JdAlphaWaybillReceiveDTO();

        if (TradeUtils.isReissueOrChangeitem(trade) && StringUtils.isNotEmpty(trade.getAddressMd5()) && trade.getTradeExt() != null) {
            JSONObject extraJson = JSON.parseObject(trade.getTradeExt().getExtraFields());
            if (extraJson != null) {
                String platformId = extraJson.getString("platformId");
                String platformIdSource = extraJson.getString("platformIdSource");
                if (StringUtils.isNotEmpty(platformId) && "wo".equals(platformIdSource)) {
                    alphaWaybillReceive.setOrderType(3);
                }
            }
        }

        // 运单类型：1.普通运单 2.生鲜 3.航空
        alphaWaybillReceive.setWaybillType(1);
        // 包裹数量
        alphaWaybillReceive.setWaybillCount(waybillCountMap.getOrDefault(trade.getSid(), 1));
        // 一单多包索引值不为空时，表示需要拆分请求，即每次只获取一个单号
        if (Objects.nonNull(index)) {
            alphaWaybillReceive.setWaybillCount(1);
        }
        // 初始化包id裹生成策略容器，并通过策略容器获取包裹id
        String packageId = new PackageStrategyContext(templateContext).generateId(trade, index);
        // 赋值包裹id
        alphaWaybillReceive.setVendorOrderCode(packageId);
        alphaWaybillReceive.setWeight(getCustomWeightValue(userWlbExpressTemplate, trade));
        alphaWaybillReceive.setVolume(getCustomVolumeValue(userWlbExpressTemplate, trade));

        log.info("获取京东电子面单号,订单号: " + trade.getSid() + "包裹号 : " + packageId + " 重量" + alphaWaybillReceive.getWeight() + "体积" + alphaWaybillReceive.getVolume());

        // 读取"是否使用子母件获取单号"配置
        String childWaybillGet = PrintTemplateConfigUtil.getByKey(userWlbExpressTemplate, JdTemplateEnum.CHILD_WAYBILL_GET.getCode(), JdTemplateEnum.CHILD_WAYBILL_GET.getDefaultValue());
        // 是否使用子母件获取单号：0.否 1.是
        alphaWaybillReceive.setChildMotherOrder(Objects.equals(childWaybillGet, "1"));
        // 京东平台物流商编码
        alphaWaybillReceive.setProviderId(templateContext.getExpressCompany().getExpressCompanyJdId());
        // 京东销售平台类型编码
        alphaWaybillReceive.setSalePlatform(JdSalePlatformEnum.getSalePlatform(trade.getSource(), trade.getSubSource()));
        // 平台单号
        alphaWaybillReceive.setPlatformOrderNo(trade.getTid().split("-")[0]);
        // 兼容自营店铺，优先取模版属性中的"vendor_code"值，为空则传taobaoId
        String vendorCode = templateContext.getTemplateBranchAddress() != null ? templateContext.getTemplateBranchAddress().getVendorCode() : null;
        if (StringUtils.isBlank(vendorCode)) {
            vendorCode = PrintTemplateConfigUtil.getByKey(Optional.ofNullable(templateContext.getAddressUserWlbExpressTemplate()).orElse(new UserWlbExpressTemplate()), JdTemplateEnum.VENDOR_CODE.getCode(), String.valueOf(user.getTaobaoId()));
        }
        alphaWaybillReceive.setVendorCode(vendorCode);
        // 店铺昵称
        alphaWaybillReceive.setVendorName(user.getNick());
        // 发货地址
        senderAddress(senderAddress, alphaWaybillReceive);
        // 收货地址
        receiveAddress(trade, alphaWaybillReceive);

        // 自营类型传：财务结算编码
        if (Objects.equals(userWlbExpressTemplate.getCpType(), 1L)) {
            // 无界结算编码现保存在地址维度，老数据维护在网点维度
            TemplateBranch templateBranch = templateContext.getTemplateBranch();
            TemplateBranchAddress templateBranchAddress = templateContext.getTemplateBranchAddress();
            if (templateBranchAddress == null && templateBranch == null) {
                throw new IllegalArgumentException(String.format("模版'%s'[%s]未找到网点信息，请检查网点是否正常", userWlbExpressTemplate.getName(), userWlbExpressTemplate.getId()));
            }
            alphaWaybillReceive.setSettlementCode(StringUtils.defaultIfBlank(templateBranchAddress == null ? templateBranch.getCusTid() : templateBranchAddress.getCusTid(), templateBranch.getCusTid()));
        } else {
            // 加盟类型传：承运商发货网点编码
            BranchAccount branchAccount = templateContext.getBranchAccount();
            alphaWaybillReceive.setBranchCode(branchAccount == null ? null : branchAccount.getBranchCode());
            if (StringUtils.isBlank(alphaWaybillReceive.getBranchCode())) {
                TemplateBranch templateBranch = templateContext.getTemplateBranch();
                alphaWaybillReceive.setBranchCode(templateBranch == null ? null : templateBranch.getBranchCode());
            }
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        // 商品名称取值调整
        alphaWaybillReceive.setGoodsName(JdWaybillUtil.getGoodsName(orders.get(0), userWlbExpressTemplate));
        if(StringUtils.isBlank(alphaWaybillReceive.getGoodsName())){
            alphaWaybillReceive.setGoodsName("普通商品");
        }
        alphaWaybillReceive.setGoodsMoney(new BigDecimal(StringUtils.defaultIfBlank(trade.getPayment(), "0")).setScale(2, RoundingMode.HALF_UP));
        alphaWaybillReceive.setGoodsCount(trade.getItemNum());
        Map<String, String> serviceValue = Optional.ofNullable(userWlbExpressTemplate.getServiceValue()).orElse(new HashMap<>());
        // 产品类型
        String productType = PrintTemplateConfigUtil.getByKey(userWlbExpressTemplate, EnumFieldValueName.PRODUCT_TYPE.getValue());
        // 顺丰特殊处理
        if (Objects.equals("SF", userWlbExpressTemplate.getCpCode())) {
            if (StringUtils.isBlank(productType)) {
                if (templateContext.isNewTemplateRule()) {
                    throw new IllegalArgumentException("顺丰速运取号时产品类型必传，请点击快递公司：<a target=\"_blank\" href='#/print/expressSheetSetting'>顺丰速运</a>选择产品类型后再取号");
                } else {
                    String format = String.format("顺丰速运取号时产品类型必传，请点击快递模板：<a target=\"_blank\" href='#/pt/template/?printType=fxg&id=%s&cpcode=%s'>顺丰速运</a>选择产品类型后再取号",
                            userWlbExpressTemplate.getId(), userWlbExpressTemplate.getCpCode());
                    throw new IllegalArgumentException(format);
                }
            }
            alphaWaybillReceive.setExpressType(productType);
            // 快递费付款方式(顺丰必填)	1:寄方付 2:收方付 3:第三方付
            String payMethod = serviceValue.get(EnumServiceValueName.SF_PAY_METHOD.getFieldName()) == null ?
                    PrintTemplateConfigUtil.getByKey(userWlbExpressTemplate, EnumServiceValueName.SF_PAY_METHOD.getFieldName(), "1") :
                    serviceValue.get(EnumServiceValueName.SF_PAY_METHOD.getFieldName());
            alphaWaybillReceive.setExpressPayMethod(payMethod);

        }

        // 德邦快递读取产品类型
        if (Objects.equals("DBKD", userWlbExpressTemplate.getCpCode())) {
            if (StringUtils.isNotEmpty(productType) && !"-1".equals(productType)) {
                alphaWaybillReceive.setExpressType(productType);
            }
        }

        //取号为明文时设置京东tid为sid
        String phone = alphaWaybillReceive.getToAddress().getPhone();
        String mobile = alphaWaybillReceive.getToAddress().getMobile();
        String oaid = alphaWaybillReceive.getToAddress().getOaid();
        if (StringUtils.isEmpty(oaid) && CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && !(phone.contains("*") || mobile.contains("*") || phone.contains("-") || mobile.contains("-"))) {
            //虚拟号也走密文处理
            alphaWaybillReceive.setPlatformOrderNo(String.valueOf(trade.getSid()));
        }

        List<JdServiceDTO> serviceList = new ArrayList<>();
        // 京东送货上门
        String deliveryToDoor = serviceValue.get(EnumFieldValueName.DELIVERY_TO_DOOR.getValue());
        if ("1".equals(deliveryToDoor) && !JdExpressEnum.JD.getCpCode().equals(userWlbExpressTemplate.getCpCode())) {
            JdServiceDTO jdServiceDTO = new JdServiceDTO();
            jdServiceDTO.setName(EnumFieldValueName.DELIVERY_TO_DOOR.getValue());
            JdExtendAttributeDTO extendAttributeDTO = new JdExtendAttributeDTO();
            extendAttributeDTO.setCode("serviceDefiner");
            extendAttributeDTO.setValue("JD");
            jdServiceDTO.setAttributes(Collections.singletonList(extendAttributeDTO));
            serviceList.add(jdServiceDTO);
        }
        // 保价
        if (Objects.equals("SF", userWlbExpressTemplate.getCpCode()) || Objects.equals("DBKD", userWlbExpressTemplate.getCpCode())) {
            String insureAmountValue = JdEclpLogisticsServiceUtil.getInsureAmount(userWlbExpressTemplate, trade);
            if (StringUtils.isNotBlank(insureAmountValue)) {
                alphaWaybillReceive.setNeedGuarantee(true);
                alphaWaybillReceive.setGuaranteeMoney(new BigDecimal(insureAmountValue));
            } else {
                alphaWaybillReceive.setNeedGuarantee(false);
                alphaWaybillReceive.setGuaranteeMoney(BigDecimal.ZERO);
            }
            if (Objects.equals("SF", userWlbExpressTemplate.getCpCode()) && StringUtils.isNotBlank(insureAmountValue)) {
                alphaWaybillReceive.setChildMotherOrder(true);
                if (StringUtils.isNotBlank(insureAmountValue)) {
                    JdServiceDTO jdServiceDTO = new JdServiceDTO();
                    jdServiceDTO.setName("INSURE");
                    jdServiceDTO.setValue(insureAmountValue);
                    serviceList.add(jdServiceDTO);
                }
            }
        }
        // 中通产品类型
        if (Objects.equals("ZTO", userWlbExpressTemplate.getCpCode()) && StringUtils.isNotEmpty(productType)) {
            JdServiceDTO jdServiceDTO = new JdServiceDTO();
            jdServiceDTO.setName(productType);
            jdServiceDTO.setValue("");
            serviceList.add(jdServiceDTO);
        }
        if (!CollectionUtils.isEmpty(serviceList)) {
            alphaWaybillReceive.setServiceList(serviceList);
        }

        Map<Long, BigDecimal> jdShouldPayMoney = templateContext.getJdShouldPayMoney();
        if (Objects.nonNull(jdShouldPayMoney) && jdShouldPayMoney.containsKey(trade.getSid())) {
            alphaWaybillReceive.setPayType(1);
            alphaWaybillReceive.setShouldPayMoney(jdShouldPayMoney.get(trade.getSid()).setScale(2, RoundingMode.HALF_UP));
        }

        request.setContent(JSON.toJSONString(alphaWaybillReceive));
        return request;

    }

    /**
     * 构建京东无界面单解绑请求参数
     *
     * @param multiTemplateContext 面单模板容器
     * @param outSidPool           面单池数据
     * @return LdopAlphaWaybillUnbindRequest 京东无界面单解绑请求参数
     * <AUTHOR>
     * @date 2021/11/5
     */
    public static LdopAlphaWaybillUnbindRequest buildUnbindRequest(WaybillMultiTemplateContext multiTemplateContext, OutSidPool outSidPool) {
        Map<Long, User> userMap = multiTemplateContext.getUserMap();
        Map<Long, UserWlbExpressTemplate> userWlbExpressTemplateMap = multiTemplateContext.getUserWlbExpressTemplateMap();
        Map<Long, Trade> tradeMap = ConvertUtil.toMap(multiTemplateContext.getTradeList(), Trade::getSid);

        User user = userMap.get(outSidPool.getTaobaoId());
        UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateMap.get(outSidPool.getTemplateId());
        ExpressCompany expressCompany = multiTemplateContext.getExpressCompanyMap().get(userWlbExpressTemplate.getExpressId());

        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String nowStr = sdf.format(now);

        // 构建请求参数
        LdopAlphaWaybillUnbindRequest request = new LdopAlphaWaybillUnbindRequest();
        request.setProviderId(expressCompany.getExpressCompanyJdId().intValue());
        request.setOperatorName(user.getNick());
        request.setOperatorTime(now);
        request.setTimestamp(nowStr);
        request.setWaybillCodeList(outSidPool.getOutSid());
        // 平台单号
        request.setPlatformOrderNo(tradeMap.get(outSidPool.getSid()).getTid());
        return request;
    }

    private static void receiveAddress(Trade trade, JdAlphaWaybillReceiveDTO alphaWaybillReceive) {
        WaybillAddressDTO receiveAddress = new WaybillAddressDTO();
        receiveAddress.setProvinceName(trade.getReceiverState());
        receiveAddress.setCityName(trade.getReceiverCity());
        receiveAddress.setCountryName(trade.getReceiverDistrict());
        receiveAddress.setCountrysideName("");
        receiveAddress.setAddress(trade.getReceiverAddress());
        receiveAddress.setContact(trade.getReceiverName());
        receiveAddress.setCountrysideName(trade.getReceiverStreet());
        receiveAddress.setProvinceId(0);
        receiveAddress.setCityId(0);
        receiveAddress.setCountryId(0);
        receiveAddress.setCountrysideId(0);
        // 联系电话处理
        String receiverPhone = StringUtils.defaultIfBlank(trade.getReceiverPhone(), trade.getReceiverMobile());
        String receiverMobile = StringUtils.defaultIfBlank(trade.getReceiverMobile(), trade.getReceiverPhone());
        receiveAddress.setPhone(receiverPhone);
        receiveAddress.setMobile(receiverMobile);
        // 满足以下条件：手机号码取receiverMobileIndex，同时情况电话号
        if (StringUtils.isNotEmpty(trade.getReceiverMobile())
                && StringUtils.isNotEmpty(trade.getReceiverMobile().replaceAll("\\*", ""))
                && receiverMobile.replaceAll("\\*", "").length() != receiverMobile.length()
                && trade.getTradeExt() != null
                && StringUtils.isNotEmpty(trade.getTradeExt().getReceiverMobileIndex())
                && trade.getTradeExt().getReceiverMobileIndex().length() >= 70) {
            receiveAddress.setPhone(trade.getTradeExt().getReceiverMobileIndex());
            receiveAddress.setMobile(trade.getTradeExt().getReceiverMobileIndex());
        }
        if (JdWaybillUtil.isJdOAIDTrade(trade)) {
            receiveAddress.setOaid(trade.getAddressMd5());
        }
        alphaWaybillReceive.setToAddress(receiveAddress);
    }

    private static void senderAddress(SenderAddress senderAddress, JdAlphaWaybillReceiveDTO alphaWaybillReceive) {
        WaybillAddressDTO fromAddress = new WaybillAddressDTO();
        fromAddress.setProvinceName(senderAddress.getProvince());
        fromAddress.setCityName(senderAddress.getCity());
        fromAddress.setCountryName(senderAddress.getArea());
        fromAddress.setCountrysideName("");
        fromAddress.setAddress(senderAddress.getAddressDetail());
        fromAddress.setContact(senderAddress.getName());
        fromAddress.setPhone(DataUtil.defaultIfBlank(senderAddress.getPhone(), senderAddress.getMobile()));
        fromAddress.setMobile(DataUtil.defaultIfBlank(senderAddress.getMobile(), senderAddress.getPhone()));
        fromAddress.setProvinceId(0);
        fromAddress.setCityId(0);
        fromAddress.setCountryId(0);
        fromAddress.setCountrysideId(0);
        alphaWaybillReceive.setFromAddress(fromAddress);
    }

    /**
     * 构建京东无界加包请求参数
     *
     * @param templateContext
     * @param trade
     * <AUTHOR>
     * @date 2021/12/31
     */
    public static AlphaAppendRequest buildAddPackRequest(WaybillTemplateContext templateContext, Trade trade) {
        User user = templateContext.getUser();
        SenderAddress senderAddress = templateContext.getSenderAddress();
        UserWlbExpressTemplate userWlbExpressTemplate = templateContext.getUserWlbExpressTemplate();
        // 面单池
        Map<Long, OutSidPool> outSidPoolMap = ConvertUtil.toMap(templateContext.getOutSidPoolList(), OutSidPool::getSid);
        // 包裹id：优先取tradeOrder，默认取sid
        String packageId = String.valueOf(trade.getSid());
        if (outSidPoolMap.containsKey(trade.getSid())) {
            packageId = outSidPoolMap.get(trade.getSid()).getTradeOrder();
        }

        // 兼容自营店铺，优先取模版属性中的"vendor_code"值，为空则传taobaoId
        String vendorCode = templateContext.getTemplateBranchAddress() != null ? templateContext.getTemplateBranchAddress().getVendorCode() : null;
        if (StringUtils.isBlank(vendorCode)) {
            vendorCode = PrintTemplateConfigUtil.getByKey(templateContext.getAddressUserWlbExpressTemplate(), JdTemplateEnum.VENDOR_CODE.getCode(), String.valueOf(user.getTaobaoId()));
        }

        // 构建请求参数
        AlphaAppendRequest alphaAppendRequest = new AlphaAppendRequest();
        // 快递单号
        alphaAppendRequest.setParentWaybillCode(trade.getOutSid());
        // 包裹id
        alphaAppendRequest.setVendorOrderCode(packageId);
        // 承运商编码 这里 erp code 可能和jd code不一样 暂时只有顺丰用所以没有暴露问题
        alphaAppendRequest.setProviderCode(templateContext.getExpressCompany().getJdCpCode());
        alphaAppendRequest.setVendorCode(vendorCode);
        alphaAppendRequest.setVendorName(user.getNick());
        alphaAppendRequest.setPlatformOrderNo(trade.getTid());
        alphaAppendRequest.setWaybillCount(1);
        alphaAppendRequest.setSalePlatform(JdSalePlatformEnum.getSalePlatform(trade.getSource(), trade.getSubSource()));

        // 发货地址
        alphaAppendRequest.setFromProvinceName(senderAddress.getProvince());
        alphaAppendRequest.setFromCityName(senderAddress.getCity());
        alphaAppendRequest.setFromCountryName(senderAddress.getArea());
        alphaAppendRequest.setFromAddressDetail(senderAddress.getAddressDetail());
        alphaAppendRequest.setFromContact(senderAddress.getName());
        alphaAppendRequest.setFromPhone(senderAddress.getPhone());
        alphaAppendRequest.setFromMobile(senderAddress.getMobile());

        // 收件地址
        alphaAppendRequest.setToProvinceName(trade.getReceiverState());
        alphaAppendRequest.setToCityName(trade.getReceiverCity());
        alphaAppendRequest.setToCountryName(trade.getReceiverDistrict());
        alphaAppendRequest.setToAddressDetail(trade.getReceiverAddress());
        alphaAppendRequest.setToCountrysideName(trade.getReceiverStreet());
        alphaAppendRequest.setToContact(trade.getReceiverName());

        /**
         * 顺丰加包不再传包裹重量,加包传重量平台计算重量累加
         */
        if (!"SF".equals(userWlbExpressTemplate.getCpCode())) {
            alphaAppendRequest.setWeight(getCustomWeightValue(userWlbExpressTemplate, trade));
            alphaAppendRequest.setVolume(getCustomVolumeValue(userWlbExpressTemplate, trade));
        }
        log.info("京东加包电子面单号,订单号: " + trade.getSid() + "包裹号 : " + packageId + " 重量" + alphaAppendRequest.getWeight() + "体积" + alphaAppendRequest.getVolume() + "CPCode" + userWlbExpressTemplate.getCpCode());
        // 收件人电话特殊处理
        String receiverPhone = StringUtils.isNotBlank(trade.getReceiverPhone()) ? trade.getReceiverPhone() : trade.getReceiverMobile();
        String receiverMobile = StringUtils.isNotBlank(trade.getReceiverMobile()) ? trade.getReceiverMobile() : trade.getReceiverPhone();
        alphaAppendRequest.setToPhone(receiverPhone);
        alphaAppendRequest.setToMobile(receiverMobile);
        // 满足以下条件：手机号码取receiverMobileIndex，同时情况电话号
        if (StringUtils.isNotEmpty(trade.getReceiverMobile())
                && StringUtils.isNotEmpty(trade.getReceiverMobile().replaceAll("\\*", ""))
                && receiverMobile.replaceAll("\\*", "").length() != receiverMobile.length()
                && trade.getTradeExt() != null
                && StringUtils.isNotEmpty(trade.getTradeExt().getReceiverMobileIndex())
                && trade.getTradeExt().getReceiverMobileIndex().length() >= 70) {
            alphaAppendRequest.setToPhone(trade.getTradeExt().getReceiverMobileIndex());
            alphaAppendRequest.setToMobile(trade.getTradeExt().getReceiverMobileIndex());
        }

        // 自营类型传：财务结算编码
        if (Objects.equals(userWlbExpressTemplate.getCpType(), 1L)) {
            // 无界结算编码现保存在地址维度，老数据维护在网点维度
            TemplateBranch templateBranch = templateContext.getTemplateBranch();
            TemplateBranchAddress templateBranchAddress = templateContext.getTemplateBranchAddress();
            alphaAppendRequest.setSettlementCode(StringUtils.defaultIfBlank(templateBranchAddress.getCusTid(), templateBranch.getCusTid()));
        } else {
            // 加盟类型传：承运商发货网点编码
            BranchAccount branchAccount = templateContext.getBranchAccount();
            alphaAppendRequest.setBranchCode(branchAccount == null ? null : branchAccount.getBranchCode());
            if (StringUtils.isBlank(alphaAppendRequest.getBranchCode())) {
                TemplateBranch templateBranch = templateContext.getTemplateBranch();
                alphaAppendRequest.setBranchCode(templateBranch == null ? null : templateBranch.getBranchCode());
            }
        }

        // 顺丰特殊处理
        if (Objects.equals("SF", userWlbExpressTemplate.getCpCode())) {
            String productType = PrintTemplateConfigUtil.getByKey(userWlbExpressTemplate, EnumFieldValueName.PRODUCT_TYPE.getValue());
            if (StringUtils.isBlank(productType)) {
                productType = PrintTemplateConfigUtil.getByKey(userWlbExpressTemplate, EnumFieldValueName.JD_PROMISE_TIME_TYPE.getValue());
                if (templateContext.isNewTemplateRule()) {
                    throw new IllegalArgumentException("顺丰速运取号时产品类型必传，请点击快递公司：<a target=\"_blank\" href='#/print/expressSheetSetting'>顺丰速运</a>选择产品类型后再取号");
                } else {
                    String format = String.format("顺丰速运取号时产品类型必传，请点击快递模板：<a target=\"_blank\" href='#/pt/template/?printType=fxg&id=%s&cpcode=%s'>顺丰速运</a>选择产品类型后再取号",
                            userWlbExpressTemplate.getId(), userWlbExpressTemplate.getCpCode());
                    throw new IllegalArgumentException(format);
                }
            }
            alphaAppendRequest.setExpressType(productType);
            // 快递费付款方式(顺丰必填)	1:寄方付 2:收方付 3:第三方付
            alphaAppendRequest.setExpressPayMethod("1");
        }

        //取号为明文时设置京东tid为sid
        String phone = alphaAppendRequest.getToPhone();
        String mobile = alphaAppendRequest.getToMobile();
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && !(phone.contains("*") || mobile.contains("*") || phone.contains("-") || mobile.contains("-"))) {
            if (StringUtils.isNumeric(phone.split("-")[0]) && StringUtils.isNumeric(mobile.split("-")[0])) {
                alphaAppendRequest.setPlatformOrderNo(String.valueOf(trade.getSid()));
            }
        }
        return alphaAppendRequest;
    }

    //获取模版自定义值
    private static BigDecimal getCustomWeightValue(UserWlbExpressTemplate userWlbExpressTemplate, Trade trade) {
        try {
            String flag = "0";
            String weight = "0";
            for (FieldValue fieldValue : userWlbExpressTemplate.getFieldValuesCustom()) {
                if (EnumFieldValueName.TOTAL_WEIGHT_SELECT.getValue().equals(fieldValue.getName())) {
                    flag = fieldValue.getValue();
                }
                if (EnumFieldValueName.TOTAL_WEIGHT.getValue().equals(fieldValue.getName())) {
                    weight = fieldValue.getValue();
                }
            }
            if ("1".equals(flag)) {
                return new BigDecimal(trade.getNetWeight().toString());
            }
            if ("2".equals(flag)) {
                return new BigDecimal(weight);
            }
        } catch (Exception e) {
            log.error("获取订单重量出错" + e);
        }
        return BigDecimal.ZERO;
    }

    public static BigDecimal getCustomVolumeValue(UserWlbExpressTemplate userWlbExpressTemplate, Trade singleTrade) {
        try {
            String flag = "0";
            for (FieldValue fieldValue : userWlbExpressTemplate.getFieldValuesCustom()) {
                if (EnumFieldValueName.TOTAL_VOLUME_SELECT.getValue().equals(fieldValue.getName())) {
                    flag = fieldValue.getValue();
                }
            }
            if ("0".equals(flag)) {
                return BigDecimal.ZERO;
            }
            if ("1".equals(flag)) {
                return new BigDecimal(Optional.ofNullable(singleTrade.getVolume()).orElse(Double.valueOf(0)));
            }
        } catch (AssertionError | NumberFormatException e) {
            log.error("获取订单体积异常" + e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 构建京东无界面单解绑请求参数
     */
    public static LdopAlphaWaybillApiUnbindRequest buildNewUnbindRequest(WaybillMultiTemplateContext multiTemplateContext, OutSidPool outSidPool) {
        Map<Long, UserWlbExpressTemplate> userWlbExpressTemplateMap = multiTemplateContext.getUserWlbExpressTemplateMap();
        UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateMap.get(outSidPool.getTemplateId());
        ExpressCompany expressCompany = multiTemplateContext.getExpressCompanyMap().get(userWlbExpressTemplate.getExpressId());

        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String nowStr = sdf.format(now);

        // 构建请求参数
        LdopAlphaWaybillApiUnbindRequest request = new LdopAlphaWaybillApiUnbindRequest();
        request.setProviderId(expressCompany.getExpressCompanyJdId().intValue());
        request.setTimestamp(nowStr);
        request.setWaybillCode(outSidPool.getOutSid());
        return request;
    }

}
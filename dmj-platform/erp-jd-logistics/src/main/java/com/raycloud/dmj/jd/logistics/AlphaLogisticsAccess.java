package com.raycloud.dmj.jd.logistics;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.jd.open.api.sdk.domain.seller.VenderBasicSafService.response.getBasicVenderInfoByVenderId.VenderBasicResult;
import com.jd.open.api.sdk.domain.seller.VenderBasicSafService.response.getBasicVenderInfoByVenderId.VenderBasicVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jd.open.api.sdk.domain.wujiemiandan.ProviderQueryApi.response.query.ProviderDTO;
import com.jd.open.api.sdk.domain.wujiemiandan.WaybillReceiveApi.response.receive.WaybillResponseDTO;
import com.jd.open.api.sdk.internal.util.HttpUtil;
import com.jd.open.api.sdk.internal.util.JsonUtil;
import com.jd.open.api.sdk.internal.util.StringUtil;
import com.jd.open.api.sdk.request.AbstractRequest;
import com.jd.open.api.sdk.request.JdRequest;
import com.jd.open.api.sdk.request.seller.VenderVbinfoGetBasicVenderInfoByVenderIdRequest;
import com.jd.open.api.sdk.request.supplier.UserCategory3InfoGetRequest;
import com.jd.open.api.sdk.request.wujiemiandan.*;
import com.jd.open.api.sdk.response.AbstractResponse;
import com.jd.open.api.sdk.response.seller.VenderVbinfoGetBasicVenderInfoByVenderIdResponse;
import com.jd.open.api.sdk.response.supplier.UserCategory3InfoGetResponse;
import com.jd.open.api.sdk.response.wujiemiandan.*;
import com.raycloud.dmj.domain.Configurable;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.jd.common.*;
import com.raycloud.dmj.jd.logistics.enums.JdErrorMsgAppend;
import com.raycloud.dmj.jd.logistics.model.AlphaSubscriptionDTO;
import com.raycloud.dmj.jd.logistics.request.LdopAlphaWaybillAppendreceiveRequest;
import com.raycloud.dmj.services.platform.basis.PlatformAccessException;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.web.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: ruanyaguang
 * @Date : 2017/11/28
 */
@Service
public class AlphaLogisticsAccess {
    @Resource
    Configurable config;
    private static final Logger LOGGER = Logger.getLogger(AlphaLogisticsAccess.class);

    public LdopAlphaVendorStockQueryResponse vendorStockQuery(User user, Integer providerId, String branchCode) {
        LdopAlphaVendorStockQueryRequest req = new LdopAlphaVendorStockQueryRequest();
        req.setVendorCode(String.valueOf(user.getTaobaoId()));
        req.setProviderId(providerId);
        req.setBranchCode(branchCode);
        return wrapResponse(user, req);
    }

    public LdopAlphaWaybillReceiveResponse waybillReceive(User user, LdopAlphaWaybillReceiveRequestContent content) {
        LdopAlphaWaybillReceiveRequest req = new LdopAlphaWaybillReceiveRequest();
        req.setContent(JSON.toJSONString(content));
        return wrapResponse(user, req);
    }

    /**
     * 查询供应商简码
     *
     * @param user
     * <AUTHOR>
     * @date 2021/9/24
     */
    public String getVendorCode(User user) {
        String apiServerUrl = useSandbox(user) ? JdAppInfo.SERVER_URL_PROXY : "https://api.jd.com/routerjson";
        VenderVbinfoGetBasicVenderInfoByVenderIdResponse response;
        try {
            // 匹配请求地址
            // 发起请求
            VenderVbinfoGetBasicVenderInfoByVenderIdRequest request = new VenderVbinfoGetBasicVenderInfoByVenderIdRequest();
            response = new JdLogisticsClient(apiServerUrl, user.getSessionId(),
                    JdAppKeyUtil.getAppKey(user), JdAppKeyUtil.getAppSecert(user)).execute(request);
        } catch (Exception e) {
            throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, "获取京东无界供应商编码异常");
        }
        if (Objects.isNull(response) || Objects.isNull(response.getGetbasicvenderinfobyvenderidResult())) {
            LOGGER.debug("获取京东无界供应商编码未获取到响应结果!");
            return null;
        }
        VenderBasicResult venderBasicResult = response.getGetbasicvenderinfobyvenderidResult();
        if (!venderBasicResult.getSuccess()) {
            LOGGER.debug("获取京东无界供应商编码失败,原因:" + venderBasicResult.getErrorMsg());
            return null;
        }
        VenderBasicVO venderBasicVO = venderBasicResult.getVenderBasicVO();
        if (venderBasicVO == null || StringUtils.isBlank(venderBasicVO.getVenderCode())) {
            LOGGER.debug("未获取京东无界供应商编码!");
            return null;
        }
        return venderBasicVO.getVenderCode();
    }

    /**
     * 对比 getVendorCode 不会校验是否是店铺化商家；只查询供应商账号，不会校验商家ID
     * 可以做补偿获取供应商简码
     * @param user 用户信息
     * @return vendorCode
     */
    public String getVendorCodeByCompensate(User user) {
        UserCategory3InfoGetResponse response;
        try {
            // 匹配请求地址
            String apiServerUrl = useSandbox(user) ? JdAppInfo.SERVER_URL_PROXY : "https://api.jd.com/routerjson";
            // 发起请求
            response = new JdLogisticsClient(apiServerUrl, user.getSessionId(),
                    JdAppKeyUtil.getAppKey(user), JdAppKeyUtil.getAppSecert(user)).execute(new UserCategory3InfoGetRequest());
        } catch (Exception e) {
            throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, "获取京东无界供应商编码异常");
        }
        // 未查询成功
        if (Objects.isNull(response) || Objects.isNull(response.getUserCategory3Info())) {
            LOGGER.debug("compensate-获取京东无界供应商编码失败:{}" + JSONObject.toJSONString(response));
            return null;
        }
        // 返回结果不为空
        if (Objects.nonNull(response.getUserCategory3Info())) {
            return response.getUserCategory3Info().getProviderCode();
        }
        return null;
    }

    public String getOldVendorCode(User user) {
        UserCategory3InfoGetResponse response = null;
        try {
            // 匹配请求地址
            String apiServerUrl = useSandbox(user) ? JdAppInfo.SERVER_URL_PROXY : "https://api.jd.com/routerjson";
            // 发起请求
            response = new JdLogisticsClient(apiServerUrl, user.getSessionId(),
                    JdAppKeyUtil.getAppKey(user), JdAppKeyUtil.getAppSecert(user)).execute(new UserCategory3InfoGetRequest());
        } catch (Exception e) {
            throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, "获取京东无界供应商编码异常");
        }
        // 未查询成功
        if (Objects.isNull(response) || Objects.isNull(response.getUserCategory3Info())) {
            throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, "获取京东无界供应商编码失败");
        }
        // 返回结果不为空
        if (Objects.nonNull(response.getUserCategory3Info())) {
            return response.getUserCategory3Info().getProviderCode();
        }
        return null;
    }

    /**
     * 查询供应商简码
     *
     * @param user
     * <AUTHOR>
     * @date 2021/9/24
     */
    public String getVendorCodeWithWrapper(User user) {
        String vendorCode = getVendorCode(user);
        if (StringUtils.isNotBlank(vendorCode)) {
            return "VC" + vendorCode;
        }
        // 补偿查询供应商账号返回vendorCode
        vendorCode = getVendorCodeByCompensate(user);
        if (StringUtils.isNotBlank(vendorCode)) {
            return "VC" + vendorCode;
        }
        return vendorCode;
    }

    public LdopAlphaWaybillReceiveResponse waybillReceiveByAssembleUrl(User user, LdopAlphaWaybillReceiveRequestContent content) {
        LdopAlphaWaybillReceiveRequest request = new LdopAlphaWaybillReceiveRequest();
        String response;
        try {
            final HttpClient httpClient = new HttpClient();
            String apiServerUrl = getApiServerUrl(user);
            String appKey = useSandbox(user) ? JdAppInfo.APPKEY_SANDBOX : JdAppKeyUtil.getAppKey(user);
            try {
                LdopAlphaWaybillReceiveRequest req = new LdopAlphaWaybillReceiveRequest();
                req.setContent(JSON.toJSONString(content));
                response = new JdLogisticsClient(apiServerUrl, user.getSessionId(), JdAppKeyUtil.getAppKey(user), JdAppKeyUtil.getAppSecert(user)).execute(req).getMsg();
            } catch (Exception e) {
                LOGGER.error(LogHelper.buildErrorLog(user.getStaff(), e, "京东无界获取单号异常"));
                if (e instanceof JdAccessException) {
                    //京东顺丰店铺授权过期仍会返回月结卡号不正确，特殊处理
                    String sf = "";
                    if (((JdAccessException) e).getErrMsg().equals("顺丰返回异常:月结卡号不合法")) {
                        sf = ",请检查京东店铺管理月结卡号是否正确或者该店铺是否授权";
                        sf = sf.replace("月结卡号", "月结卡号:" + content.getSettlementCode());
                    }
                    throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, "京东无界获取单号失败，原因：" + ((JdAccessException) e).getErrMsg() + sf);
                }
                throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, "京东无界获取单号异常");
            }
            if (StringUtils.contains(response, "error_response")) {
                JSONObject jo = JSON.parseObject(response);
                JSONObject errorResponse = jo.getJSONObject("error_response");
                JdErrorResponse jdErrorResponse = JSON.parseObject(errorResponse.toJSONString(), JdErrorResponse.class);
                String msg = String.format("拼串调用京东阿尔法接单接口报错，错误信息：%s", "19".equals(jdErrorResponse.getCode()) ? "授权失效，请重新授权" : jdErrorResponse.getZhDesc());
                LOGGER.error(LogHelper.buildLog(user.getStaff(), msg));
                throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, msg);
            }
            WaybillResponseDTO dto = JSON.parseObject(response, WaybillResponseDTO.class);
            if (!Integer.valueOf(0).equals(dto.getStatusCode())) {
                String msg = String.format("拼串调用京东阿尔法接单接口报错，错误信息：%s", dto.getStatusMessage());
                LOGGER.error(LogHelper.buildLog(user.getStaff(), msg));
                throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, msg);
            }
            LdopAlphaWaybillReceiveResponse result = new LdopAlphaWaybillReceiveResponse();
            result.setResultInfo(dto);
            return result;
        } catch (PlatformAccessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error(LogHelper.buildErrorLog(user.getStaff(), e, "拼串调用京东阿尔法接单接口报错"), e);
            throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, "拼串调用京东阿尔法接单接口报错," + e.getMessage(), e);
        }
    }

    //京东子母件加单接口
    public LdopAlphaWaybillReceiveResponse waybillAppendreceiveByAssembleUrl(User user, LdopAlphaWaybillAppendreceiveRequest content) {
        LdopAlphaWaybillAppendreceiveRequest request = new LdopAlphaWaybillAppendreceiveRequest();
        String response;
        try {
            final HttpClient httpClient = new HttpClient();
            String apiServerUrl = getApiServerUrl(user);
            String appKey = useSandbox(user) ? JdAppInfo.APPKEY_SANDBOX : JdAppKeyUtil.getAppKey(user);
            try {
                response = new JdLogisticsClient(apiServerUrl, user.getSessionId(), JdAppKeyUtil.getAppKey(user), JdAppKeyUtil.getAppSecert(user)).execute(content).getMsg();
            } catch (Exception e) {
                LOGGER.error(LogHelper.buildErrorLog(user.getStaff(), e, "京东无界子母件加包获取单号异常"));
                if (e instanceof JdAccessException) {
                    throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, "京东无界子母件加包获取单号失败，原因：" + ((JdAccessException) e).getErrMsg());
                }
                throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, "京东无界子母件加包获取单号异常");
            }

            if (StringUtils.contains(response, "error_response")) {
                return errorHandle(user, response);
            }
            String responce = getErrorString(response);
            WaybillResponseDTO dto = JSON.parseObject(responce, WaybillResponseDTO.class);
            if (!Integer.valueOf(0).equals(dto.getStatusCode())) {
                return errorStatusHandle(user, dto);
            }
            LdopAlphaWaybillReceiveResponse result = new LdopAlphaWaybillReceiveResponse();
            result.setResultInfo(dto);
            return result;

        } catch (PlatformAccessException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error(LogHelper.buildErrorLog(user.getStaff(), e, "拼串调用京东阿尔法接单接口报错"), e);
            throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, "拼串调用京东阿尔法接单接口报错", e);
        }
    }

    //处理返回日志
    private String getErrorString(String response) {
        String responce = JSONObject.parseObject(response).getJSONObject("jingdong_ldop_alpha_waybill_appendreceive_responce").getJSONObject("resultInfo").toJSONString();
        return responce;
    }

    //返回信息状态错误处理
    private LdopAlphaWaybillReceiveResponse errorStatusHandle(User user, WaybillResponseDTO dto) {
        String msg = String.format("加单接口报错，错误信息：%s", dto.getStatusMessage());
        LOGGER.error(LogHelper.buildLog(user.getStaff(), msg));
        String appenDmsgBy = JdErrorMsgAppend.getMsgByKey(msg);
        if (StringUtils.isNotBlank(appenDmsgBy)) {
            msg = appenDmsgBy;
        }
        throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, msg);
    }

    //返回错误处理
    private LdopAlphaWaybillReceiveResponse errorHandle(User user, String response) {
        JSONObject jo = JSON.parseObject(response);
        JSONObject errorResponse = jo.getJSONObject("error_response");
        JdErrorResponse jdErrorResponse = JSON.parseObject(errorResponse.toJSONString(), JdErrorResponse.class);
        String msg = String.format("拼串调用京东阿尔法加单接口报错，错误信息：%s", "19".equals(jdErrorResponse.getCode()) ? "授权失效，请重新授权" : jdErrorResponse.getZhDesc());
        LOGGER.error(LogHelper.buildLog(user.getStaff(), msg));
        throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, msg);
    }

    private String getApiServerUrl(User user) {
        return useSandbox(user) ? JdAppInfo.SERVER_URL_PROXY : "https://api.jd.com/routerjson";
    }

    public LdopAlphaVendorBigshotQueryResponse bigshotQuery(User user, String waybillCode, Integer providerId) {
        LdopAlphaVendorBigshotQueryRequest req = new LdopAlphaVendorBigshotQueryRequest();
        req.setWaybillCode(waybillCode);
        req.setProviderId(providerId);
        return wrapResponse(user, req);
    }

    public static void main(String[] args) {
        User user = new User();
        user.setSessionId("0d361719ea5949a1ae8e814195a26020e3mt");
        user.setTaobaoId(10083522L);
        LdopAlphaVendorBigshotQueryRequest req = new LdopAlphaVendorBigshotQueryRequest();
        req.setWaybillCode("ZYJ000001457924");
        req.setProviderId(881232);
        LdopAlphaVendorBigshotQueryResponse response = new JdClientHelper(user).request(req);
        System.out.println(JSONObject.toJSONString(response));
    }

    public LdopAlphaWaybillUnbindResponse waybillUnbind(User user, LdopAlphaWaybillUnbindRequest req) {
        return wrapResponse(user, req);
    }

    public List<ProviderDTO> providerQuery(User user) {
        List<ProviderDTO> providers = new ArrayList<ProviderDTO>();
        LdopAlphaProviderQueryRequest req = new LdopAlphaProviderQueryRequest();
        req.setProviderState(0);
        LdopAlphaProviderQueryResponse resp = wrapResponse(user, req);
        if (null != resp && null != resp.getResultInfo() && CollectionUtils.isNotEmpty(resp.getResultInfo().getData())) {
            providers.addAll(resp.getResultInfo().getData());
        }
        return providers;
    }

    private boolean isFail(AlphaLogisticsAccess.LdopAlphaProviderSignSuccessResponse.ResponseDTO resultInfo) {
        return resultInfo == null || CollectionUtils.isEmpty(resultInfo.getData())
                || (!Objects.equals(resultInfo.getStatusCode(), 0) && StringUtils.contains(resultInfo.getStatusMessage(), "此商家没有签约成功信息"));
    }

    public AlphaSubscriptionDTO queryAlphaSubscription(User user) {
        // 查询无界商家编码(VC + 供应商简码)
        String vendorCode = getVendorCodeWithWrapper(user);

        // 查询网点订阅信息-有VC
        LdopAlphaProviderSignSuccessResponse response = new LdopAlphaProviderSignSuccessResponse();
        if (StringUtils.isNotBlank(vendorCode)) {
            response = providerSignSuccessQuery(user, vendorCode);
        }

        // 查询网点订阅信息-无VC
        LdopAlphaProviderSignSuccessResponse resp2 = providerSignSuccessQuery(user, String.valueOf(user.getTaobaoId()));

        // 填充VC
        if (!isFail(response.getResultInfo())) {
            for (LdopAlphaProviderSignSuccessResponse.SignSuccessQueryDTO dto : response.getResultInfo().getData()) {
                dto.setVendorCode(vendorCode);
            }
        }
        if (!isFail(resp2.getResultInfo())) {
            for (LdopAlphaProviderSignSuccessResponse.SignSuccessQueryDTO dto : resp2.getResultInfo().getData()) {
                dto.setVendorCode(String.valueOf(user.getTaobaoId()));
            }
        }


        if (!isFail(response.getResultInfo()) && !isFail(resp2.getResultInfo())) {
            response.getResultInfo().getData().addAll(resp2.getResultInfo().getData());
        } else if (isFail(response.getResultInfo()) && !isFail(resp2.getResultInfo())) {
            response = resp2;
        }

        // 无界网点订信息
        AlphaSubscriptionDTO alphaSubscriptionDTO = new AlphaSubscriptionDTO();
        alphaSubscriptionDTO.setVendorCode(StringUtils.isNotBlank(vendorCode) ? vendorCode : String.valueOf(user.getTaobaoId()));
        alphaSubscriptionDTO.setResult(response);
        return alphaSubscriptionDTO;
    }

    public LdopAlphaProviderSignSuccessResponse providerSignSuccessQuery(User user) {
        return providerSignSuccessQuery(user, String.valueOf(user.getTaobaoId()));
    }

    public LdopAlphaVendorRechargeQueryResponse vendorRechargeQuery(User user, Long providerId) {
        LdopAlphaVendorRechargeQueryRequest request = new LdopAlphaVendorRechargeQueryRequest();
        request.setVendorCode(String.valueOf(user.getTaobaoId()));
        request.setProviderId(providerId);
        Date startDate = new Date();
        // 创建 Calendar 对象并设置为当前日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        // 将日期向前推 31 天
        calendar.add(Calendar.DAY_OF_MONTH, -31);
        // 获取 31 天前的日期
        Date date31DaysAgo = calendar.getTime();
        request.setStartTime(DateUtil.getDateTime(date31DaysAgo));
        request.setEndTime(DateUtil.getDateTime(new Date()));
        return wrapResponse(user, request);
    }

    public LdopAlphaProviderSignSuccessResponse providerSignSuccessQuery(User user, String vendorCode) {
        LdopAlphaProviderSignSuccessRequest req = new LdopAlphaProviderSignSuccessRequest();
        req.setVendorCode(StringUtils.defaultIfBlank(vendorCode, String.valueOf(user.getTaobaoId())));
        return wrapResponse(user, req);
    }

    private <T extends AbstractResponse> T wrapResponse(User user, JdRequest<T> req) {
        if (useSandbox(user)) {
            return wrapResponseSandbox(user, req);
        } else {
            return wrapResponseCommon(user, req);
        }
    }

    private <T extends AbstractResponse> T wrapResponseCommon(User user, JdRequest<T> req) {
        return new JdClientHelper(user).request(req);
    }

    private boolean useSandbox(User user) {
        if (config.getProperty("jd_alpha_sandbox_users", "") != null && user.getId() != null) {
            return config.getProperty("jd_alpha_sandbox_users", "").contains("," + user.getId() + ",");
        } else {
            return false;
        }
    }

    private <T extends AbstractResponse> T wrapResponseSandbox(User user, JdRequest<T> req) {
        try {
            return new JdLogisticsClient(JdAppInfo.SERVER_URL_PROXY, user.getSessionId(), JdAppInfo.APPKEY_SANDBOX, JdAppInfo.APPSECRET_SANDBOX).execute(req);
        } catch (Exception e) {
            throw new JdAccessException(e);
        }
    }

    private static String assembleApiUrl(AssembleApiUrlParamBean param) {
        StringBuilder wholeUrl = new StringBuilder(param.getApiServerUrl()).append("?v=").append(param.getVersion()).append("&method=").append(param.getApiMethod())
                .append("&app_key=").append(param.getAppKey()).append("&access_token=").append(param.getAccessToken()).append("&360buy_param_json=").append(param.getParam());
        return wholeUrl.toString();
    }


    private String buildUrl(AssembleApiUrlParamBean param) throws Exception {
        Map<String, String> sysParams = new HashMap<>();
        Map<String, String> pmap = new TreeMap();
        pmap.put("360buy_param_json", param.getParam());
        sysParams.put("method", param.getApiMethod());
        sysParams.put("access_token", param.getAccessToken());
        sysParams.put("app_key", param.getAppKey());
        pmap.putAll(sysParams);
        String sign = sign(pmap, param.getAccessToken());
        sysParams.put("sign", sign);
        StringBuilder sb = new StringBuilder(param.getApiServerUrl());
        sb.append("?");
        sb.append(HttpUtil.buildQuery(sysParams, "UTF-8"));
        return sb.toString();
    }

    private String sign(Map<String, String> pmap, String appSecret) throws Exception {
        StringBuilder sb = new StringBuilder(appSecret);
        Iterator var4 = pmap.entrySet().iterator();

        while (var4.hasNext()) {
            Map.Entry<String, String> entry = (Map.Entry) var4.next();
            String name = (String) entry.getKey();
            String value = (String) entry.getValue();
            if (StringUtil.areNotEmpty(new String[]{name, value})) {
                sb.append(name).append(value);
            }
        }

        sb.append(appSecret);
        String result = JdCodecUtils.md5(sb.toString());
        return result;
    }

    public LdopAlphaWaybillApiUnbindResponse waybillUnbind(User user, LdopAlphaWaybillApiUnbindRequest req) {
        return wrapResponse(user, req);
    }


    private static class AssembleApiUrlParamBean {
        private String apiServerUrl;
        private String version;
        private String apiMethod;
        private String appKey;
        private String accessToken;
        private String param;

        public String getApiServerUrl() {
            return apiServerUrl;
        }

        public AssembleApiUrlParamBean setApiServerUrl(String apiServerUrl) {
            this.apiServerUrl = apiServerUrl;
            return this;
        }

        public String getVersion() {
            return version;
        }

        public AssembleApiUrlParamBean setVersion(String version) {
            this.version = version;
            return this;
        }

        public String getApiMethod() {
            return apiMethod;
        }

        public AssembleApiUrlParamBean setApiMethod(String apiMethod) {
            this.apiMethod = apiMethod;
            return this;
        }

        public String getAppKey() {
            return appKey;
        }

        public AssembleApiUrlParamBean setAppKey(String appKey) {
            this.appKey = appKey;
            return this;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public AssembleApiUrlParamBean setAccessToken(String accessToken) {
            this.accessToken = accessToken;
            return this;
        }

        public String getParam() {
            return param;
        }

        public AssembleApiUrlParamBean setParam(String param) {
            this.param = param;
            return this;
        }
    }

    public static class LdopAlphaWaybillReceiveRequestContent implements Serializable {
        private static final long serialVersionUID = 8371097664999024197L;

        // 运单类型：1普通运单	是	1、 普通运单 2、 生鲜 3、 航空
        private Integer waybillType;
        // 所需运单的数量	是	其他快递都传1 京配传包裹数
        private Integer waybillCount;
        // 承运商id (providerId与providerCode两者必填一个)	条件	1234
        private Long providerId;
        // 承运商编码(providerId与providerCode两者必填一个)	条件
        private String providerCode;
        // 承运商发货网点编码	条件	A0001，加盟型快递公司必传 String	settlementCode	财务结算编码，直营型快递公司必传	条件
        private String branchCode;
        // 财务结算编码，直营型快递公司必传 条件
        private String settlementCode;
        // 销售平台	是	0010001 代表京东平台下的订单
        private String salePlatform;
        // 平台订单号，即pop订单号	是	20000000100 非京东平台填所对应平台的订单号
        private String platformOrderNo;
        // 商家编码	是	10001
        private String vendorCode;
        // 商家名称	是	XXX旗舰店
        private String vendorName;
        // 商家自有订单号	是	ELS9292003
        private String vendorOrderCode;
        // 京标发货四级地址	 是
        private WaybillAddress fromAddress;
        // 京标收货四级地址	 是
        private WaybillAddress toAddress;
        // 重量，单位为千克 两位小数	是	10.23，没有传0
        private BigDecimal weight;
        // 体积，单位为统一为立方厘米 两位小数	是	9000.00，没有传0
        private BigDecimal volume;
        // 承诺时效类型	是	无时效默认传0
        private Integer promiseTimeType;
        // 承诺完成时间，若未承诺时效，则不考虑此字段	否	2016/8/1 12:00
        private Date promiseCompleteTime;
        // 计划出库时间	否
        private Date promiseOutStockTime;
        // 付款方式0-在线支付	是目前暂时不支持货到付款业务
        private Integer payType;
        // 商品金额 两位小数	是	1000.01
        private BigDecimal goodsMoney;
        // 代收金额 两位小数	是	900
        private BigDecimal shouldPayMoney;
        // 是否要保价（系统暂不开放报价业务）	是	TRUE
        private Boolean needGuarantee;
        // 保价金额 两位小数	是	非保价默认传0.0
        private BigDecimal guaranteeMoney;
        // 收货时间类型，0任何时间，1工作日2节假日	是	0
        private Integer receiveTimeType;
        // 发货仓编码	否	1
        private String warehouseCode;
        // 二段码	否	京配路区
        private String secondSectionCode;
        // 三段码	否	京配站点ID
        private String thirdSectionCode;
        // 备注	否	速递
        private String remark;
        // 快递费付款方式(顺丰必填)	否	1:寄方付 2:收方付 3:第三方付
        private String expressPayMethod;
        // 快件产品类别(顺丰必填)	否	1.     顺丰次日2.     顺丰隔日
        private String expressType;
        // 扩展字段1	否
        private String extendField1;
        // 扩展字段2	否
        private String extendField2;
        // 扩展字段3	否
        private String extendField3;
        // 扩展字段4	否
        private Integer extendField4;
        // 扩展字段5	否
        private Integer extendField5;
        // 寄件人公司 否
        private String senderCompany;
        // 收件人公司 否
        private String receiveCompany;
        // 寄托物名称 否
        private String goods;
        // 寄托物数量 否
        private Integer goodsCount;
        // 是否子母件获取
        private Boolean childMotherOrder;

        public Integer getWaybillType() {
            return waybillType;
        }

        public void setWaybillType(Integer waybillType) {
            this.waybillType = waybillType;
        }

        public Integer getWaybillCount() {
            return waybillCount;
        }

        public void setWaybillCount(Integer waybillCount) {
            this.waybillCount = waybillCount;
        }

        public Long getProviderId() {
            return providerId;
        }

        public void setProviderId(Long providerId) {
            this.providerId = providerId;
        }

        public String getProviderCode() {
            return providerCode;
        }

        public void setProviderCode(String providerCode) {
            this.providerCode = providerCode;
        }

        public String getBranchCode() {
            return branchCode;
        }

        public void setBranchCode(String branchCode) {
            this.branchCode = branchCode;
        }

        public String getSettlementCode() {
            return settlementCode;
        }

        public void setSettlementCode(String settlementCode) {
            this.settlementCode = settlementCode;
        }

        public String getSalePlatform() {
            return salePlatform;
        }

        public void setSalePlatform(String salePlatform) {
            this.salePlatform = salePlatform;
        }

        public String getPlatformOrderNo() {
            return platformOrderNo;
        }

        public void setPlatformOrderNo(String platformOrderNo) {
            this.platformOrderNo = platformOrderNo;
        }

        public String getVendorCode() {
            return vendorCode;
        }

        public void setVendorCode(String vendorCode) {
            this.vendorCode = vendorCode;
        }

        public String getVendorName() {
            return vendorName;
        }

        public void setVendorName(String vendorName) {
            this.vendorName = vendorName;
        }

        public String getVendorOrderCode() {
            return vendorOrderCode;
        }

        public void setVendorOrderCode(String vendorOrderCode) {
            this.vendorOrderCode = vendorOrderCode;
        }

        public WaybillAddress getFromAddress() {
            return fromAddress;
        }

        public void setFromAddress(WaybillAddress fromAddress) {
            this.fromAddress = fromAddress;
        }

        public WaybillAddress getToAddress() {
            return toAddress;
        }

        public void setToAddress(WaybillAddress toAddress) {
            this.toAddress = toAddress;
        }

        public BigDecimal getWeight() {
            return weight;
        }

        public void setWeight(BigDecimal weight) {
            this.weight = weight;
        }

        public BigDecimal getVolume() {
            return volume;
        }

        public void setVolume(BigDecimal volume) {
            this.volume = volume;
        }

        public Integer getPromiseTimeType() {
            return promiseTimeType;
        }

        public void setPromiseTimeType(Integer promiseTimeType) {
            this.promiseTimeType = promiseTimeType;
        }

        public Date getPromiseCompleteTime() {
            return promiseCompleteTime;
        }

        public void setPromiseCompleteTime(Date promiseCompleteTime) {
            this.promiseCompleteTime = promiseCompleteTime;
        }

        public Date getPromiseOutStockTime() {
            return promiseOutStockTime;
        }

        public void setPromiseOutStockTime(Date promiseOutStockTime) {
            this.promiseOutStockTime = promiseOutStockTime;
        }

        public Integer getPayType() {
            return payType;
        }

        public void setPayType(Integer payType) {
            this.payType = payType;
        }

        public BigDecimal getGoodsMoney() {
            return goodsMoney;
        }

        public void setGoodsMoney(BigDecimal goodsMoney) {
            this.goodsMoney = goodsMoney;
        }

        public BigDecimal getShouldPayMoney() {
            return shouldPayMoney;
        }

        public void setShouldPayMoney(BigDecimal shouldPayMoney) {
            this.shouldPayMoney = shouldPayMoney;
        }

        public Boolean getNeedGuarantee() {
            return needGuarantee;
        }

        public void setNeedGuarantee(Boolean needGuarantee) {
            this.needGuarantee = needGuarantee;
        }

        public BigDecimal getGuaranteeMoney() {
            return guaranteeMoney;
        }

        public void setGuaranteeMoney(BigDecimal guaranteeMoney) {
            this.guaranteeMoney = guaranteeMoney;
        }

        public Integer getReceiveTimeType() {
            return receiveTimeType;
        }

        public void setReceiveTimeType(Integer receiveTimeType) {
            this.receiveTimeType = receiveTimeType;
        }

        public String getWarehouseCode() {
            return warehouseCode;
        }

        public void setWarehouseCode(String warehouseCode) {
            this.warehouseCode = warehouseCode;
        }

        public String getSecondSectionCode() {
            return secondSectionCode;
        }

        public void setSecondSectionCode(String secondSectionCode) {
            this.secondSectionCode = secondSectionCode;
        }

        public String getThirdSectionCode() {
            return thirdSectionCode;
        }

        public void setThirdSectionCode(String thirdSectionCode) {
            this.thirdSectionCode = thirdSectionCode;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getExpressPayMethod() {
            return expressPayMethod;
        }

        public void setExpressPayMethod(String expressPayMethod) {
            this.expressPayMethod = expressPayMethod;
        }

        public String getExpressType() {
            return expressType;
        }

        public void setExpressType(String expressType) {
            this.expressType = expressType;
        }

        public String getExtendField1() {
            return extendField1;
        }

        public void setExtendField1(String extendField1) {
            this.extendField1 = extendField1;
        }

        public String getExtendField2() {
            return extendField2;
        }

        public void setExtendField2(String extendField2) {
            this.extendField2 = extendField2;
        }

        public String getExtendField3() {
            return extendField3;
        }

        public void setExtendField3(String extendField3) {
            this.extendField3 = extendField3;
        }

        public Integer getExtendField4() {
            return extendField4;
        }

        public void setExtendField4(Integer extendField4) {
            this.extendField4 = extendField4;
        }

        public Integer getExtendField5() {
            return extendField5;
        }

        public void setExtendField5(Integer extendField5) {
            this.extendField5 = extendField5;
        }

        public String getSenderCompany() {
            return senderCompany;
        }

        public void setSenderCompany(String senderCompany) {
            this.senderCompany = senderCompany;
        }

        public String getReceiveCompany() {
            return receiveCompany;
        }

        public void setReceiveCompany(String receiveCompany) {
            this.receiveCompany = receiveCompany;
        }

        public String getGoods() {
            return goods;
        }

        public void setGoods(String goods) {
            this.goods = goods;
        }

        public Integer getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(Integer goodsCount) {
            this.goodsCount = goodsCount;
        }

        public Boolean getChildMotherOrder() {
            return childMotherOrder;
        }

        public void setChildMotherOrder(Boolean childMotherOrder) {
            this.childMotherOrder = childMotherOrder;
        }
    }

    public static class LdopAlphaWaybillUnbindRequestParams implements Serializable {
        private static final long serialVersionUID = -2123560054361361554L;

        // 	运单号	否
        private List<String> waybillCodeList;
        // 	平台订单号	是
        private String platformOrderNo;
        // 	承运商编码	是
        private String providerCode;
        // 	解绑时间	是	2016/8/1 12:00
        private Date operatorTime;
        // 	解绑操作人	是	张三
        private String operatorName;

        public List<String> getWaybillCodeList() {
            return waybillCodeList;
        }

        public void setWaybillCodeList(List<String> waybillCodeList) {
            this.waybillCodeList = waybillCodeList;
        }

        public String getPlatformOrderNo() {
            return platformOrderNo;
        }

        public void setPlatformOrderNo(String platformOrderNo) {
            this.platformOrderNo = platformOrderNo;
        }

        public String getProviderCode() {
            return providerCode;
        }

        public void setProviderCode(String providerCode) {
            this.providerCode = providerCode;
        }

        public Date getOperatorTime() {
            return operatorTime;
        }

        public void setOperatorTime(Date operatorTime) {
            this.operatorTime = operatorTime;
        }

        public String getOperatorName() {
            return operatorName;
        }

        public void setOperatorName(String operatorName) {
            this.operatorName = operatorName;
        }
    }

    private static class LdopAlphaVendorRechargeQueryRequest extends AbstractRequest implements JdRequest<LdopAlphaVendorRechargeQueryResponse> {
        private String vendorCode;
        // 	承运商ID	是
        private Long providerId;

        private String startTime;

        private String endTime;

        @Override
        public String getApiMethod() {
            return "jingdong.ldop.alpha.vendor.recharge.query";
        }

        @Override
        public String getAppJsonParams() throws IOException {
            TreeMap pmap = new TreeMap();
            pmap.put("vendorCode", this.vendorCode);
            pmap.put("providerId", this.providerId);
            pmap.put("startTime", this.startTime);
            pmap.put("endTime", this.endTime);
            return JsonUtil.toJson(pmap);
        }

        @Override
        public Class<LdopAlphaVendorRechargeQueryResponse> getResponseClass() {
            return LdopAlphaVendorRechargeQueryResponse.class;
        }

        public void setVendorCode(String vendorCode) {
            this.vendorCode = vendorCode;
        }

        public void setProviderId(Long providerId) {
            this.providerId = providerId;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }
    }

    private static class LdopAlphaProviderSignSuccessRequest extends AbstractRequest implements JdRequest<LdopAlphaProviderSignSuccessResponse> {
        private String vendorCode;

        public LdopAlphaProviderSignSuccessRequest() {
        }

        @Override
        public String getApiMethod() {
            return "jingdong.ldop.alpha.provider.sign.success";
        }

        @Override
        public String getAppJsonParams() throws IOException {
            TreeMap pmap = new TreeMap();
            pmap.put("vendorCode", this.vendorCode);
            return JsonUtil.toJson(pmap);
        }

        @Override
        public Class<LdopAlphaProviderSignSuccessResponse> getResponseClass() {
            return LdopAlphaProviderSignSuccessResponse.class;
        }

        public String getVendorCode() {
            return vendorCode;
        }

        public void setVendorCode(String vendorCode) {
            this.vendorCode = vendorCode;
        }
    }

    public static class LdopAlphaVendorRechargeQueryResponse extends AbstractResponse {

        private RechargeQueryResultInfo resultInfo;

        public LdopAlphaVendorRechargeQueryResponse() {
        }

        @JsonProperty("resultInfo")
        public RechargeQueryResultInfo getResultInfo() {
            return resultInfo;
        }

        @JsonProperty("resultInfo")
        public void setResultInfo(RechargeQueryResultInfo resultInfo) {
            this.resultInfo = resultInfo;
        }

        public static class RechargeQueryResultInfo implements Serializable {
            private Integer statusCode;
            private String statusMessage;
            private List<RechargeQueryResultInfoData> data;

            @JsonProperty("statusCode")
            public Integer getStatusCode() {
                return statusCode;
            }

            @JsonProperty("statusCode")
            public void setStatusCode(Integer statusCode) {
                this.statusCode = statusCode;
            }

            @JsonProperty("statusMessage")
            public String getStatusMessage() {
                return statusMessage;
            }

            @JsonProperty("statusMessage")
            public void setStatusMessage(String statusMessage) {
                this.statusMessage = statusMessage;
            }

            @JsonProperty("data")
            public List<RechargeQueryResultInfoData> getData() {
                return data;
            }

            @JsonProperty("data")
            public void setData(List<RechargeQueryResultInfoData> data) {
                this.data = data;
            }
        }

        public static class RechargeQueryResultInfoData implements Serializable {
            // 承运商编码
            private String providerCode;
            // 承运商名称
            private String providerName;
            // 网点编码
            private String branchCode;
            // 网点名称
            private String branchName;
            // 充值状态
            private Integer state;
            // 充值时间
            private Date operatorTime;
            // 操作人姓名
            private String operatorName;
            // 充值数量
            private Integer amount;

            public String getProviderCode() {
                return providerCode;
            }

            public void setProviderCode(String providerCode) {
                this.providerCode = providerCode;
            }

            public String getProviderName() {
                return providerName;
            }

            public void setProviderName(String providerName) {
                this.providerName = providerName;
            }

            public String getBranchCode() {
                return branchCode;
            }

            public void setBranchCode(String branchCode) {
                this.branchCode = branchCode;
            }

            public String getBranchName() {
                return branchName;
            }

            public void setBranchName(String branchName) {
                this.branchName = branchName;
            }

            public Integer getState() {
                return state;
            }

            public void setState(Integer state) {
                this.state = state;
            }

            public Date getOperatorTime() {
                return operatorTime;
            }

            public void setOperatorTime(Date operatorTime) {
                this.operatorTime = operatorTime;
            }

            public String getOperatorName() {
                return operatorName;
            }

            public void setOperatorName(String operatorName) {
                this.operatorName = operatorName;
            }

            public Integer getAmount() {
                return amount;
            }

            public void setAmount(Integer amount) {
                this.amount = amount;
            }
        }

    }

    public static class LdopAlphaProviderSignSuccessResponse extends AbstractResponse {
        private ResponseDTO resultInfo;

        public LdopAlphaProviderSignSuccessResponse() {

        }

        @JsonProperty("resultInfo")
        public void setResultInfo(ResponseDTO resultInfo) {
            this.resultInfo = resultInfo;
        }

        @JsonProperty("resultInfo")
        public ResponseDTO getResultInfo() {
            return this.resultInfo;
        }

        public static class ResponseDTO implements Serializable {
            private Integer statusCode;
            private String statusMessage;
            private List<SignSuccessQueryDTO> data;

            @JsonProperty("statusCode")
            public void setStatusCode(Integer statusCode) {
                this.statusCode = statusCode;
            }

            @JsonProperty("statusCode")
            public Integer getStatusCode() {
                return this.statusCode;
            }

            @JsonProperty("statusMessage")
            public void setStatusMessage(String statusMessage) {
                this.statusMessage = statusMessage;
            }

            @JsonProperty("statusMessage")
            public String getStatusMessage() {
                return this.statusMessage;
            }

            @JsonProperty("data")
            public void setData(List<SignSuccessQueryDTO> data) {
                this.data = data;
            }

            @JsonProperty("data")
            public List<SignSuccessQueryDTO> getData() {
                return this.data;
            }
        }

        public static class SignSuccessQueryDTO implements Serializable {
            /**
             * 无界商家编码
             */
            private String vendorCode;
            // 	承运商编码	是
            private String providerCode;
            // 	承运商ID	是
            private Long providerId;
            // 	承运商名称	是
            private String providerName;
            // 	承运商类型：1 快递公司， 2 物流公司， 3 安装公司， 4 生鲜冷链承运商	是
            private Byte providerType;
            // 	是否支持货到付款	否
            private Boolean supportCod;
            // 	经营类型：1 直营，2 加盟	是
            private Long operationType;
            // 	网点编码（加盟型快递公司必返回）	否
            private String branchCode;
            // 	网点名称（加盟型快递公司必返回）	否
            private String branchName;
            // 	财务结算编码（直营型快递公司必返回）	否
            private String settlementCode;
            // 	剩余单号量	否
            private Long amount;
            // 	与快递公司签约时使用的发货地址	是
            private WaybillAddress address;

            public String getVendorCode() {
                return vendorCode;
            }

            public void setVendorCode(String vendorCode) {
                this.vendorCode = vendorCode;
            }

            public String getProviderCode() {
                return providerCode;
            }

            public void setProviderCode(String providerCode) {
                this.providerCode = providerCode;
            }

            public Long getProviderId() {
                return providerId;
            }

            public void setProviderId(Long providerId) {
                this.providerId = providerId;
            }

            public String getProviderName() {
                return providerName;
            }

            public void setProviderName(String providerName) {
                this.providerName = providerName;
            }

            public Byte getProviderType() {
                return providerType;
            }

            public void setProviderType(Byte providerType) {
                this.providerType = providerType;
            }

            public Boolean getSupportCod() {
                return supportCod;
            }

            public void setSupportCod(Boolean supportCod) {
                this.supportCod = supportCod;
            }

            public Long getOperationType() {
                return operationType;
            }

            public void setOperationType(Long operationType) {
                this.operationType = operationType;
            }

            public String getBranchCode() {
                return branchCode;
            }

            public void setBranchCode(String branchCode) {
                this.branchCode = branchCode;
            }

            public String getBranchName() {
                return branchName;
            }

            public void setBranchName(String branchName) {
                this.branchName = branchName;
            }

            public String getSettlementCode() {
                return settlementCode;
            }

            public void setSettlementCode(String settlementCode) {
                this.settlementCode = settlementCode;
            }

            public Long getAmount() {
                return amount;
            }

            public void setAmount(Long amount) {
                this.amount = amount;
            }

            public WaybillAddress getAddress() {
                return address;
            }

            public void setAddress(WaybillAddress address) {
                this.address = address;
            }
        }
    }

    public static class JdErrorResponse {
        private String code;
        private String url;
        @JSONField(name = "zh_desc")
        private String zhDesc;
        @JSONField(name = "en_desc")
        private String enDesc;
        protected String msg;

        public JdErrorResponse() {
        }

        public String getCode() {
            return this.code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getZhDesc() {
            return this.zhDesc;
        }

        public void setZhDesc(String msg) {
            this.zhDesc = msg;
        }

        public String getEnDesc() {
            return this.enDesc;
        }

        public void setEnDesc(String enDesc) {
            this.enDesc = enDesc;
        }

        public String getMsg() {
            return this.msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public String getUrl() {
            return this.url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

    public static class WaybillAddress extends com.jd.open.api.sdk.domain.wujiemiandan.SignSuccessQueryApi.response.get.WaybillAddress {
        private String contact;
        private String phone;
        private String mobile;

        public WaybillAddress() {

        }

        @JsonProperty("contact")
        public void setContact(String contact) {
            this.contact = contact;
        }

        @JsonProperty("contact")
        public String getContact() {
            return this.contact;
        }

        @JsonProperty("phone")
        public void setPhone(String phone) {
            this.phone = phone;
        }

        @JsonProperty("phone")
        public String getPhone() {
            return this.phone;
        }

        @JsonProperty("mobile")
        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        @JsonProperty("mobile")
        public String getMobile() {
            return this.mobile;
        }

    }
}
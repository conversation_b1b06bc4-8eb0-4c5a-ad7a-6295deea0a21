package com.raycloud.dmj.jd.trade.consign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.open.api.sdk.domain.gxpt.PurchaseOrderJosService.request.serial.GxOrderSerialBindRequest;
import com.jd.open.api.sdk.domain.gxpt.PurchaseOrderJosService.request.serial.OrderSkuSerialDo;
import com.jd.open.api.sdk.domain.gxpt.PurchaseOrderJosService.response.serial.GxOrderSerialBindResponse;
import com.jd.open.api.sdk.domain.order.IPopOrderSplitService.response.orderSplitCommitXmlApi.ApiSafResult;
import com.jd.open.api.sdk.domain.order.OrderShipmentService.response.shipment.OperatorResult;
import com.jd.open.api.sdk.domain.order.OutShipJosService.request.outship.BatchStockOutOrder;
import com.jd.open.api.sdk.domain.order.OutShipJosService.request.outship.DeliveryNumberDto;
import com.jd.open.api.sdk.domain.order.OutShipJosService.request.outship.GoodsDto;
import com.jd.open.api.sdk.domain.order.OutShipJosService.request.outship.JosRequest;
import com.jd.open.api.sdk.domain.order.SnUploadJosService.request.upload.SerialCode;
import com.jd.open.api.sdk.domain.order.SnUploadJosService.request.upload.SnUploadReq;
import com.jd.open.api.sdk.domain.order.SnUploadJosService.response.upload.Result;
import com.jd.open.api.sdk.domain.order.SnUploadJosService.response.upload.SnUploadResp;
import com.jd.open.api.sdk.domain.order.WaybillJosService.request.update.PartialLogisticsInfoVo;
import com.jd.open.api.sdk.domain.order.WaybillJosService.request.update.WayBillInfoDto;
import com.jd.open.api.sdk.request.gxpt.PurchaseOrderBindSerialRequest;
import com.jd.open.api.sdk.request.order.PopOrderPartialOutshipRequest;

import com.jd.open.api.sdk.request.order.PopOrderSerialCodeUploadRequest;
import com.jd.open.api.sdk.request.order.PopOrderShipmentRequest;
import com.jd.open.api.sdk.request.order.PopOrderWaybillUpdateRequest;
import com.jd.open.api.sdk.response.gxpt.PurchaseOrderBindSerialResponse;
import com.jd.open.api.sdk.response.order.PopOrderPartialOutshipResponse;
import com.jd.open.api.sdk.response.order.PopOrderSerialCodeUploadResponse;
import com.jd.open.api.sdk.response.order.PopOrderShipmentResponse;
import com.jd.open.api.sdk.response.order.PopOrderWaybillUpdateResponse;
import com.raycloud.dmj.domain.consign.RequestType;
import com.raycloud.dmj.domain.consign.UploadType;
import com.raycloud.dmj.domain.trades.ExpressCompany;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.JDHFUtils;
import com.raycloud.dmj.jd.common.JdClientHelper;
import com.raycloud.dmj.jd.jdcloud.sdk.*;
import com.raycloud.dmj.jd.jingdong.api.domain.LogisticsGlobalModel;
import com.raycloud.dmj.jd.jingdong.api.domain.OneGlobalOrderModelNoLogistic;
import com.raycloud.dmj.jd.jingdong.api.request.PopOrderSopLogisticsUpdateRequest;
import com.raycloud.dmj.jd.jingdong.api.response.PopOrderSopLogisticsUpdateResponse;
import com.raycloud.dmj.services.platform.basis.PlatformAccessException;
import com.raycloud.dmj.services.platform.basis.annotation.Platform;
import com.raycloud.dmj.services.platform.trades.consign.*;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: banlan
 **/
@Platform(CommonConstants.PLAT_FORM_TYPE_JD)
@Component
public class JdNewCosignUploader implements PlatConsignUploader {
    private final Logger logger = Logger.getLogger(this.getClass());

    @Override
    public PlatResponse upload(PlatRequest request) {
        UploadType type = request.getType();
        UploadTrade trade = request.getTrade();
        User user = request.getUser();
        String subSource = trade.getExt("subSource");
        ExpressCompany expressCompany = request.getTrade().getExpressCompany();
        if (type == UploadType.DUMMY) {
            return PlatResponse.buildFail("[京东]暂不支持无需物流发货");
        }
        try {
            RequestType requestType = request.getRequestType();
            //灰度3测试日志
            logger.debug(LogHelper.buildUserLog(user, String.format("发货上传请求参数:[UploadType:%s,RequestType:%s,tid:%s,subSource:%s]",type.getDesc(),requestType.name(),trade.getTid(),subSource)));
            if (RequestType.FULL_UPLOAD == requestType ){
                //整单上传
                if (UploadType.RE_UPLOAD.equals(type)){
                    return resendUpload(user,trade,expressCompany,subSource);
                }else if (UploadType.UPLOAD.equals(type)){
                    return doUpload(user,trade,expressCompany,subSource);
                }
            }else if (RequestType.SKU_SPLIT_UPLOAD == requestType || RequestType.NUM_SPLIT_UPLOAD == requestType ){
                if (UploadType.RE_UPLOAD.equals(type)){
                    //拆分上传
                    return resendSplitUpload(user,trade,expressCompany,subSource);
                }else if (UploadType.UPLOAD.equals(type)){
                    //拆单上传
                    return doSplitUpload(user,trade,expressCompany,subSource);
                }
            }else {
                return PlatResponse.buildFail("[京东]暂不支持整单上传以及拆单上传以外的上传方式");
            }
        }catch (Exception e){
            logger.error(LogHelper.buildLogHead(user).append(String.format("tid:%s req:%s", trade.getTid(), JSON.toJSONString(request))));
            logger.error(LogHelper.buildLogHead(user).append(String.format("tid:%s error:%s", trade.getTid(), e.getMessage())), e);
            return PlatResponse.buildFail("调用京东平台发货接口失败" + e.getMessage());
        }
        return PlatResponse.buildSuccess();
    }

    private PlatResponse resendSplitUpload(User user, UploadTrade trade, ExpressCompany expressCompany,String subSource) {
        if(CommonConstants.PLAT_FORM_TYPE_JD_GXPT.equals(subSource)){
            return PlatResponse.buildFail("[京东供销]暂不支持拆单重新发货上传");
        }
        PopOrderWaybillUpdateRequest request = new PopOrderWaybillUpdateRequest();
        com.jd.open.api.sdk.domain.order.WaybillJosService.request.update.JosRequest req = new com.jd.open.api.sdk.domain.order.WaybillJosService.request.update.JosRequest();
        WayBillInfoDto wayBillInfoDto = buildWayBillInfoDto(user,trade,expressCompany);
        req.setData(wayBillInfoDto);
        req.setBelongType(200);
        request.setRequest(req);
        logger.debug(LogHelper.buildLogHead(user, String.format("tid:%s 拆单重新发货请求参数req: %s",trade.getTid(),JSONObject.toJSONString(req))));
        PopOrderWaybillUpdateResponse response = new JdClientHelper(user).request(request);
        logger.debug(LogHelper.buildLogHead(user, String.format("tid:%s 拆单重新发货返回参数resp: %s",trade.getTid(),JSONObject.toJSONString(response))));
        if (Objects.isNull(response) || Objects.isNull(response.getResponse())){
            return PlatResponse.buildFail("京东分批重新发货上传失败,平台无响应");
        }
        if (!Integer.valueOf(200).equals(response.getResponse().getCode())){
            return PlatResponse.buildFail("京东分批重新发货上传失败,msg:"+response.getResponse().getMsg());
        }
        return PlatResponse.buildSuccess();
    }

    private WayBillInfoDto buildWayBillInfoDto(User user,UploadTrade trade, ExpressCompany expressCompany) {
        WayBillInfoDto wayBillInfoDto = new WayBillInfoDto();
        wayBillInfoDto.setOrderId(Long.valueOf(trade.getTid()));
        List<PartialLogisticsInfoVo> partialShipmentInfoList = new ArrayList<>();
        String oldOutSid = trade.getExt("oldOutSid");
        Map<String, Integer> partialShipmentMap = getPartialShipmentMap(user, trade);
        Integer shipmentId = partialShipmentMap.get(oldOutSid);
        if(Objects.isNull(shipmentId)){
            throw new PlatformAccessException(PlatformAccessException.ERROR_SYSTEM,"缺少分批ID,重新发货上传失败");
        }
        PartialLogisticsInfoVo infoVo = new PartialLogisticsInfoVo();
        infoVo.setShipmentId(shipmentId);
        infoVo.setWaybillId(trade.getOutSid());
        infoVo.setLogicId(expressCompany.getExpressCompanyJdId());
        infoVo.setLogicName(expressCompany.getName());
        partialShipmentInfoList.add(infoVo);
        wayBillInfoDto.setPartialShipmentInfoList(partialShipmentInfoList);
        return wayBillInfoDto;
    }

    private Map<String, Integer> getPartialShipmentMap(User user, UploadTrade trade) {
        Map<String,Integer> partialShipmentMap = new HashMap<>();
        JingdongHufuOrderPopOrderGetResponse popOrderGetResponse = this.getJingdongHufuOrderPopOrderGetResponse(user, trade.getTid());
        if (popOrderGetResponse!=null && popOrderGetResponse.getResult()!=null && "0000".equals(popOrderGetResponse.getResult().getCode())) {
            Object orderInfo = popOrderGetResponse.getResult().getOrderInfo();
            String text = JSONObject.toJSONString(orderInfo);
            JSONObject jsonObject = JSONObject.parseObject(text);
            if (jsonObject.containsKey("partialLogisticsInfoModel")) {
                JSONArray partialLogisticsInfoModel = jsonObject.getJSONArray("partialLogisticsInfoModel");
                if (CollectionUtils.isNotEmpty(partialLogisticsInfoModel)){
                    for (int i = 0; i < partialLogisticsInfoModel.size(); i++) {
                        JSONObject jsObj = partialLogisticsInfoModel.getJSONObject(i);
                        partialShipmentMap.put(jsObj.getString("waybillId"),jsObj.getInteger("shipmentId"));
                    }
                }
            }
        }else {
            if (popOrderGetResponse==null || popOrderGetResponse.getResult()==null) {
                logger.error("指定下载失败：平台返回空,请检查订单是否存在，授权是否有效之后重试");
            } else if ("400".equals(popOrderGetResponse.getResult().getCode()) && popOrderGetResponse.getResult().getMsg().contains("没有找到该笔订单")) {
                logger.error("平台交易号不存在，请检查后重新输入");
            }
            logger.error("指定下载失败：" + popOrderGetResponse.getResult().getMsg()+",请检查订单是否存在，授权是否有效之后重试");
        }
        return partialShipmentMap;
    }

    public JingdongHufuOrderPopOrderGetResponse getJingdongHufuOrderPopOrderGetResponse(User user, String tid) {
        JingdongHufuOrderPopOrderGetRequest request = new JingdongHufuOrderPopOrderGetRequest();
        JingdongHufuOrderPopOrderGetBody body = new JingdongHufuOrderPopOrderGetBody();
        body.setOptionalFields("orderId,venderId,orderType,payType,orderTotalPrice,orderSellerPrice,orderPayment,freightPrice,sellerDiscount,orderState,orderStateRemark,deliveryType,invoiceInfo,invoiceCode,orderRemark,orderStartTime,orderEndTime,consigneeInfo,itemInfoList,couponDetailList,venderRemark,balanceUsed,pin,returnOrder,paymentConfirmTime,waybill,logisticsId,vatInfo,modified,directParentOrderId,parentOrderId,customs,orderSource,customsModel,storeOrder,idSopShipmenttype,scDT,serviceFee,pauseBizInfo,taxFee,tuiHuoWuYou,orderSign,storeId");
        body.setOrderId(tid);
        body.setToken(user.getSessionId());
        HashMap<String, String> extendProps = new HashMap<>();
        extendProps.put("userId", user.getId().toString());
        extendProps.put("version", "20210513");
        body.setExtendProps(extendProps);
        request.setJingdongHufuOrderPopOrderGetBody(body);
        JingdongHufuOrderPopOrderGetResponse response = new DefaultJdcloudExecutor().request(request);
        return response;
    }

    private PlatResponse doSplitUpload(User user, UploadTrade trade, ExpressCompany expressCompany,String subSource) throws InterruptedException {
        if(CommonConstants.PLAT_FORM_TYPE_JD_GXPT.equals(subSource)){
            return PlatResponse.buildFail("[京东供销]暂不支持拆单发货上传");
        }
        PopOrderPartialOutshipRequest request = new PopOrderPartialOutshipRequest();
        JosRequest req = new JosRequest();
        List<BatchStockOutOrder> dataList = new ArrayList<>();
        buildData(trade, expressCompany, dataList);
        req.setData(dataList);
        req.setBelongType(200);
        request.setRequest(req);
        logger.debug(LogHelper.buildLogHead(user, String.format("tid:%s 拆单发货请求参数req: %s",trade.getTid(),JSONObject.toJSONString(req))));
        PopOrderPartialOutshipResponse response =new JdClientHelper(user).request(request);
        logger.debug(LogHelper.buildLogHead(user, String.format("tid:%s 拆单发货返回参数resp: %s",trade.getTid(),JSONObject.toJSONString(response))));
        PlatResponse buildFail = checkResponse(request,user,response,0);//改为不重试  由上游控制
        if (buildFail != null) return buildFail;
        return PlatResponse.buildSuccess();
    }

    private PlatResponse checkResponse(PopOrderPartialOutshipRequest request,User user,PopOrderPartialOutshipResponse response,Integer reCount) throws InterruptedException {
        if (Objects.isNull(response) || Objects.isNull(response.getResponse())){
            return PlatResponse.buildFail("京东分批发货上传失败,平台无响应");
        }
        if (!Integer.valueOf(200).equals(response.getResponse().getCode())){
            return PlatResponse.buildFail("京东分批发货上传失败,msg:" + response.getResponse().getMsg());
        }
        if(Objects.nonNull(response.getResponse()) && Objects.nonNull(response.getResponse().getData())
                &&  CollectionUtils.isNotEmpty(response.getResponse().getData().getFailList())){
            Map<Long, String> errorMap = response.getResponse().getData().getFailList().stream().collect(Collectors.toMap(k -> k
                    .getOrderId(), v -> v.getFailReason()));
            if (reCount>0){
                Optional<String> first = errorMap.values().stream().filter(reason -> "已出库订单不能重复出库".equals(reason)).findFirst();
                if (first.isPresent()){
                    Thread.sleep(1500);
                    PopOrderPartialOutshipResponse reResponse = new JdClientHelper(user).request(request);
                    logger.debug(LogHelper.buildLogHead(user, String.format("第%s次重试拆单发货返回参数resp: %s",reCount,JSONObject.toJSONString(reResponse))));
                    return checkResponse(request,user,reResponse,--reCount);
                }
            }
            return PlatResponse.buildFail("京东分批发货上传部分订单失败,msg:" + JSONObject.toJSONString(errorMap));
        }
        return null;
    }
    private void buildData(UploadTrade trade, ExpressCompany expressCompany, List<BatchStockOutOrder> dataList) {
        BatchStockOutOrder batchStockOutOrder = new BatchStockOutOrder();
        batchStockOutOrder.setDeliveryType(5);
        batchStockOutOrder.setOrderIds(Arrays.asList(Long.valueOf(trade.getTid())));
        List<DeliveryNumberDto> deliveryNumberDtoList = new ArrayList<>();
        DeliveryNumberDto numberDto = new DeliveryNumberDto();
        deliveryNumberDtoList.add(numberDto);
        numberDto.setDeliveryNums(Arrays.asList(trade.getOutSid()));
        numberDto.setDeliveryId(expressCompany.getExpressCompanyJdId().intValue());
        numberDto.setDeliveryName(expressCompany.getName());

        List<GoodsDto> goodsList = new ArrayList<>();
        Map<String,String> skuIdSkuUUidMap = JSONObject.parseObject(trade.getExt("skuIdSkuUUidMap"), Map.class);
        if (MapUtils.isEmpty(skuIdSkuUUidMap)){
            throw new IllegalArgumentException("京东拆单发货缺少skuUuid参数");
        }
        for (UploadOrder order : trade.getOrders()) {
            GoodsDto goodsDto = new GoodsDto();
            goodsDto.setSkuId(Long.valueOf(order.getSkuId()));
            goodsDto.setNum(order.getNum());
            goodsDto.setSkuUuid(skuIdSkuUUidMap.get(order.getSkuId()));
            goodsList.add(goodsDto);
        }

        numberDto.setPartialShipmentGoodsList(goodsList);
        batchStockOutOrder.setDeliveryNumberDtoList(deliveryNumberDtoList);
        dataList.add(batchStockOutOrder);
    }

    private PlatResponse resendUpload(User user, UploadTrade trade, ExpressCompany expressCompany,String subSource){
        if(CommonConstants.PLAT_FORM_TYPE_JD_GXPT.equals(subSource)){
            return uploadJdGxptOrder(user, trade, expressCompany);
        }
        PlatResponse response = new PlatResponse();
        PopOrderSopLogisticsUpdateResponse popOrderSopLogisticsUpdateResponse = reUpload(user, Long.parseLong(trade.getTid()), trade.getOutSid(),expressCompany.getExpressCompanyJdId());
        ApiSafResult returnType = popOrderSopLogisticsUpdateResponse.getReturnType();
        if (popOrderSopLogisticsUpdateResponse == null ||  returnType == null) {
            response.setMsg("京东修改运单接口没有返回响应");
            response.setSuccess(false);
        } else {
            //根据code判断接口调用是否成功
            response.setSuccess(returnType.getSuccess());
            response.setCode(returnType.getResultCode());
            response.setMsg(returnType.getResultDescribe());
        }
        return response;
    }
    private PlatResponse uploadJdGxptOrder(User user, UploadTrade trade, ExpressCompany expressCompany) {
        JingdongHufuGoodsGoodsSendSyncRequest request = new JingdongHufuGoodsGoodsSendSyncRequest();
        JingdongHufuGoodsGoodsSendSyncBody body = new JingdongHufuGoodsGoodsSendSyncBody();
        //LogisticsCompany company = LogisticsCompaniesUtil.queryCompany(user, express.getExpressCompanyJdId());
        body.setExpressNo(String.valueOf(expressCompany.getExpressCompanyJdId()));//快递公司类型 TODO 弄反了 改不过来了
        body.setOrdersNo(trade.getTid());
        body.setExpressType(trade.getOutSid());//运单号
        body.setToken(user.getSessionId());
        body.setSource(CommonConstants.PLAT_FORM_TYPE_JD_GXPT);
        body.setRequesId(trade.getTid() + "_" + DateUtils.format(new Date(), "yyyyMMddHHmmssSSS"));
        request.setJingdongHufuGoodsGoodsSendSyncBody(body);
        JingdongHufuGoodsGoodsSendSyncResponse resp = null;
        try {
            resp = new DefaultJdcloudExecutor().request(request);
        } catch (Exception e){
            logger.error(LogHelper.buildLogHead(user).append(String.format("【京东供销平台】上传发货发生异常，tid:%s,request:%s",
                    trade.getTid(), JSONObject.toJSONString(request))), e);
            throw e;
        }

        PlatResponse response = new PlatResponse();
        if(logger.isDebugEnabled()){
            logger.debug(LogHelper.buildLogHead(user).append(String.format("【京东供销平台】上传发货结果，tid:%s,request:%s,response:%s",
                    trade.getTid(), JSONObject.toJSONString(request), JSONObject.toJSONString(resp))));
        }
        response.setData(resp);
        if (resp == null || resp.getResult() == null) {
            response.setMsg("平台无响应");
            response.setSuccess(false);
        } else {
            //根据code判断接口调用是否成功
            JingdongHufuGoodsGoodsSendSyncResponse.Result result = resp.getResult();
            Object data = result.getGoodsSendSyncResult();
            if (null != result && "1".equals(result.getCode())){
                response.setSuccess(true);
                response.setMsg(result.getMsg());

            } else if (result != null){
                response.setSuccess(false);
                response.setCode(result.getCode());
                response.setMsg(result.getMsg());
            } else {
                response.setMsg("平台响应为空");
                response.setSuccess(false);
            }
        }
        return response;
    }

    private PlatResponse bindSerialForGxpt(User user, UploadTrade trade, ExpressCompany expressCompany) {
        PurchaseOrderBindSerialRequest request = new PurchaseOrderBindSerialRequest();
        GxOrderSerialBindRequest req = new GxOrderSerialBindRequest();
        request.setRequest(req);
        req.setJdOrderId(Long.parseLong(trade.getTid()));
        List<OrderSkuSerialDo> skuSerials = new ArrayList<>();
        OrderSkuSerialDo skuSerialDo = new OrderSkuSerialDo();
        for (UploadOrder order : trade.getOrders()) {
            skuSerialDo.setJdSkuId(null);
            skuSerialDo.setImei1(null);
            skuSerialDo.setImei2(null);
            skuSerialDo.setSn(null);
            skuSerialDo.setUpc(null);
            skuSerials.add(skuSerialDo);
        }
        req.setSkuSerials(skuSerials);
        PurchaseOrderBindSerialResponse resp = new JdClientHelper(user).request(request);
        PlatResponse response = new PlatResponse();
        response.setData(resp);
        if (resp == null) {
            response.setMsg("平台无响应");
            response.setSuccess(false);
        } else if(resp.getResult() == null) {
            logger.warn(LogHelper.buildUserLog(user, String.format("绑定4码上传失败，tid:%s, req:%s, resp:%s", trade.getTid(), JSONObject.toJSONString(request), JSONObject.toJSONString(resp))));
            response.setMsg(resp.getMsg());
            response.setCode(resp.getCode());
            response.setSuccess(false);
        } else {
            GxOrderSerialBindResponse result = resp.getResult();
            if(!result.getResult()){
                logger.warn(LogHelper.buildUserLog(user, String.format("绑定4码上传失败，tid:%s, req:%s, resp:%s", trade.getTid(), JSONObject.toJSONString(request), JSONObject.toJSONString(resp))));
            }
            response.setSuccess(result.getResult());
            response.setMsg(result.getMsg());
            response.setCode(result.getCode() + "");
        }
        return response;
    }

    private PlatResponse bindSerialForPop(User user, UploadTrade trade, ExpressCompany expressCompany) {


        List<SerialCode> skuSerials = new ArrayList<>();
        PlatResponse response = null;
        for (UploadOrder order : trade.getOrders()) {
            SnUploadReq req = new SnUploadReq();
            req.setOrderId(Long.parseLong(trade.getTid()));
//            req.setSerialCodeType(1);  todo
//            req.setSerialCode("sn码");
            /**
             * SN/MEI1/IMEI2/UPC列表。注意：3c品类国补订单同时上传4码可通过serialCodeList进行传参，无需在serialCode传参。
             * 3c品类序列号上传要求可通过jingdong.pop.order.get接口itemExt的serialCodeReqList获取
             */
//            for (;;) { todo
//                SerialCode serialCode = new SerialCode();
//                serialCode.setCodeType(null);//序列号类型：SN/IMEI1/IMEI2/UPC
//                serialCode.setCodeValue(null);
//                skuSerials.add(serialCode);
//            }
            req.setSkuId(Long.parseLong(order.getSkuId()));

            req.setSerialCodeList(skuSerials);
            response = doBindSerialForPop(user, req);
        }
        return response;
    }

    private PlatResponse doBindSerialForPop(User user,SnUploadReq req){
        PopOrderSerialCodeUploadRequest request = new PopOrderSerialCodeUploadRequest();
        request.setSnUploadReq(req);
        PopOrderSerialCodeUploadResponse resp = new JdClientHelper(user).request(request);
        PlatResponse response = new PlatResponse();
        response.setData(resp);
        if (resp == null) {
            response.setMsg("平台无响应");
            response.setSuccess(false);
        } else if(resp.getReturnType() == null ) {
            logger.warn(LogHelper.buildUserLog(user, String.format("绑定4码上传失败1，tid:%s,skuId:%s, req:%s, resp:%s", req.getOrderId(), req.getSkuId(), JSONObject.toJSONString(request), JSONObject.toJSONString(resp))));
            response.setMsg(resp.getMsg());
            response.setCode(resp.getCode());
            response.setSuccess(false);
        } else if(resp.getReturnType().getSnUploadResp() == null ) {
            logger.warn(LogHelper.buildUserLog(user, String.format("绑定4码上传失败2，tid:%s,skuId:%s, req:%s, resp:%s", req.getOrderId(), req.getSkuId(), JSONObject.toJSONString(request), JSONObject.toJSONString(resp))));
            response.setMsg(resp.getReturnType().getMessage());
            response.setCode(resp.getReturnType().getCode() + "");
            response.setSuccess(false);
        } else {
            SnUploadResp result = resp.getReturnType().getSnUploadResp();
            if(!Objects.equals(1, result.getStatus())){
                logger.warn(LogHelper.buildUserLog(user, String.format("绑定4码上传失败3，tid:%s,skuId:%s, req:%s, resp:%s", req.getOrderId(), req.getSkuId(), JSONObject.toJSONString(request), JSONObject.toJSONString(resp))));
                response.setMsg(result.getSubMsg() + StringUtils.trimToEmpty(result.getSuggestion()));
            }
            response.setSuccess(Objects.equals(1, result.getStatus()));
            response.setCode(result.getSubCode());
        }
        return response;
    }
    private PlatResponse doUpload(User user, UploadTrade trade, ExpressCompany expressCompany,String subSource){
        if(CommonConstants.PLAT_FORM_TYPE_JD_GXPT.equals(subSource)){
            return uploadJdGxptOrder(user, trade, expressCompany);
        }
        if (JDHFUtils.isWhiteUser(user.getCompanyId())){
            JingdongHufuGoodsGoodsSendSyncRequest request = new JingdongHufuGoodsGoodsSendSyncRequest();
            JingdongHufuGoodsGoodsSendSyncBody body = new JingdongHufuGoodsGoodsSendSyncBody();
            body.setExpressNo(String.valueOf(expressCompany.getExpressCompanyJdId()));//快递公司类型 TODO 弄反了 改不过来了
            body.setOrdersNo(trade.getTid());
            body.setExpressType(trade.getOutSid());//运单号
            body.setToken(user.getSessionId());
            request.setJingdongHufuGoodsGoodsSendSyncBody(body);
            JingdongHufuGoodsGoodsSendSyncResponse resp = new DefaultJdcloudExecutor().request(request);
            PlatResponse response = new PlatResponse();
            response.setData(resp);
            if (resp == null || resp.getResult() == null) {
                response.setMsg("平台无响应");
                response.setSuccess(false);
            } else {
                //根据code判断接口调用是否成功
                JingdongHufuGoodsGoodsSendSyncResponse.Result result = resp.getResult();
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(result.getGoodsSendSyncResult()));
                if (null != jsonObject) {
                    response.setSuccess(jsonObject.getBoolean("success"));
                    response.setCode(jsonObject.containsKey("errorCode") ? jsonObject.getString("errorCode") : result.getCode());
                    response.setMsg(jsonObject.containsKey("chineseErrCode") ? jsonObject.getString("chineseErrCode") : result.getMsg());
                } else if (StringUtils.isNotBlank(result.getCode()) || StringUtils.isNotBlank(result.getMsg())){
                    response.setSuccess(false);
                    response.setCode(result.getCode());
                    response.setMsg(result.getMsg());
                } else {
                    response.setMsg("平台响应为空");
                    response.setSuccess(false);
                }
            }
            return response;
        } else {
            PopOrderShipmentResponse popOrderShipmentResponse = sopConsign(user, trade.getTid(), String.valueOf(expressCompany.getExpressCompanyJdId()), trade.getOutSid());


            return sopOutStorageResp2Common(popOrderShipmentResponse);
        }
    }
    private PlatResponse sopOutStorageResp2Common(PopOrderShipmentResponse resp) {
        return jdResp2Common(resp, "京东SOP发货接口没有返回响应");
    }

    private PlatResponse jdResp2Common(PopOrderShipmentResponse resp, String nullMessage) {
        PlatResponse response = new PlatResponse();
        response.setData(resp);
        if (resp == null || resp.getSopjosshipmentResult() == null) {
            response.setMsg(StringUtils.isNotBlank(nullMessage) ? nullMessage : "京东接口没有返回响应");
            response.setSuccess(false);
        } else {
            //根据code判断接口调用是否成功
            OperatorResult result = resp.getSopjosshipmentResult();
            response.setSuccess(result != null && result.getSuccess());
            response.setCode(StringUtils.isEmpty(result.getErrorCode()) ? resp.getCode() : result.getErrorCode());
            response.setMsg(StringUtils.isEmpty(result.getChineseErrCode()) ? resp.getZhDesc() : result.getChineseErrCode());
        }
        return response;
    }

    public PopOrderShipmentResponse sopConsign(User user, String tid, String logisticsId, String wayBill) {
        PopOrderShipmentRequest request = new PopOrderShipmentRequest();
        request.setOrderId(Long.valueOf(tid));
        request.setLogiCoprId(logisticsId);
        if (wayBill != null && wayBill.trim().length() > 0) {
            request.setLogiNo(wayBill.trim());
        }
        PopOrderShipmentResponse popOrderShipmentResponse = new JdClientHelper(user).request(request);
        if (popOrderShipmentResponse != null && popOrderShipmentResponse.getSopjosshipmentResult() != null) {
            if(!popOrderShipmentResponse.getSopjosshipmentResult().getSuccess()){
                logger.error(LogHelper.buildLogHead(user, String.format("京东上传发货报错请求参数: %s",JSONObject.toJSONString(request))));
                logger.error(LogHelper.buildLogHead(user, String.format("京东上传发货报错请求响应: %s",JSONObject.toJSONString(popOrderShipmentResponse))));
            }
        }
        return  popOrderShipmentResponse;
    }

    public PopOrderSopLogisticsUpdateResponse reUpload(User user, Long tid, String outSid, Long companyCode) {
        PopOrderSopLogisticsUpdateRequest request = new PopOrderSopLogisticsUpdateRequest();

        OneGlobalOrderModelNoLogistic oneGlobalOrderModelNoLogistic = new OneGlobalOrderModelNoLogistic();
        oneGlobalOrderModelNoLogistic.setOrderId(tid);
        request.setOneGlobalOrderModelNoLogistic(oneGlobalOrderModelNoLogistic);

        List<LogisticsGlobalModel> logisticsGlobalModelList = new ArrayList();
        LogisticsGlobalModel logisticsGlobalModel = new LogisticsGlobalModel();
        List<String> logiNoList = new ArrayList<>();
        logiNoList.add(outSid);
        logisticsGlobalModel.setLogiNoList(logiNoList);
        logisticsGlobalModel.setLogiCoprId(companyCode);
        logisticsGlobalModel.setLogiScope(0);//默认国内段
        logisticsGlobalModelList.add(logisticsGlobalModel);
        request.setLogisticsGlobalModelList(logisticsGlobalModelList);

        return new JdClientHelper(user).request(request);
    }

    /**
     * jingdong.pop.order.serialCode.upload 订单序列号（SN码）校验、上传接口
     *
     * @param user
     * @param request
     * @return
     */
    PopOrderSerialCodeUploadResponse orderSerialCodeUpload(User user, PopOrderSerialCodeUploadRequest request) {
        return new JdClientHelper(user).request(request);
    }

}

package com.raycloud.dmj.tb.trade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeDecryptTypeEnum;
import com.raycloud.dmj.domain.platform.trades.MemoUpdateRequest;
import com.raycloud.dmj.domain.platform.trades.PackagesNoticeRequest;
import com.raycloud.dmj.domain.trades.ExpressCompany;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.trades.utils.TradeAddressUtil;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.Address;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.tb.common.IErrorMsgConverter;
import com.raycloud.dmj.tb.common.TaobaoClientHelper;
import com.taobao.api.TaobaoBatchResponse;
import com.taobao.api.TaobaoRequest;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.domain.*;
import com.taobao.api.request.*;
import com.taobao.api.response.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

//import com.taobao.api.request.TraderatesGetRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2017-09-22 19:41
 */
@Service
public class TbTradeAccess {


    private final Logger logger = Logger.getLogger(this.getClass());

    private static final long SECRET_NO_DAY_DEFAULT = 30;

    public static final String TRADE_FIELDS = "seller_nick,buyer_nick,title,type,created,tid,seller_rate," +
            "buyer_flag,buyer_rate,status,payment,adjust_fee,post_fee,total_fee,pay_time,end_time,modified," +
            "consign_time,buyer_obtain_point_fee,point_fee,real_point_fee,received_payment,commission_fee,buyer_memo," +
            "seller_memo,alipay_no,alipay_id,buyer_message,pic_path,num_iid,num,price,buyer_alipay_no,receiver_name," +
            "receiver_state,receiver_city,receiver_district,receiver_address,receiver_zip,receiver_mobile," +
            "receiver_phone,seller_flag,seller_alipay_no,seller_mobile,seller_phone,seller_name,seller_email," +
            "available_confirm_fee,has_post_fee,timeout_action_time,snapshot_url,cod_fee,cod_status,shipping_type," +
            "trade_memo,is_3D,buyer_email,buyer_area,trade_from,is_lgtype,is_force_wlb,is_brand_sale,buyer_cod_fee," +
            "discount_fee,seller_cod_fee,express_agency_fee,invoice_name,service_orders,credit_cardfee,step_trade_status," +
            "step_paid_fee,mark_desc,has_yfx,yfx_fee,yfx_id,yfx_type,trade_source,promotion_details,orders,order_tax_fee," +
            "sub_order_tax_fee,sub_order_tax_rate,sub_order_tax_promotion_fee,tax_free,tax_coupon_discount, order_tax_promotion_fee," +
            "est_con_time,trade_attr,deliveryCps,tmallDelivery,tmall_coupon_fee,3plTiming,buyerTaxNO," +
            "timing_promise,promise_service,delivery_time,collect_time,sign_time,es_time," +
            "os_activity_id,os_fg_item_id,os_gift_count,os_sort_num,asdp_biz_type,asdp_ads,delivery_cps,rx_audit_status,drug_register,is_daixiao,is_sh_ship,is_force_dc,receiver_town,post_gate_declare,promise_sign_time,logistics_infos,logistics_agreement,seller_flag_tag"
            + ",service_tags,consign_due_time,consign_interval,oldfornew,use_gov_predict,use_gov_subsidy,gov_subsidy_amount,gov_subsidy_amount_exact,real_receiver_open_id,real_receiver_display_nick,post_fee_type,real_post_fee,refund_post_fee,gift_post_fee_role,gov_sn_check,qn_distr,gov_main_subject"
            + ",expand_card_basic_price_used,expand_card_expand_price_used";

    public static final String TRADE_AMOUNT_FIELDS = "tid,oid,alipay_no,total_fee,post_fee";

    private static final String REFUND_FIELDS = "refund_id, alipay_no, tid, oid, buyer_nick, seller_nick, total_fee, status, created, refund_fee, good_status, has_good_return, payment, reason, desc, num_iid, title, price, num, good_return_time, company_name, sid, address, shipping_type, refund_remind_timeout, refund_phase, refund_version, operation_contraint, attribute, outer_id,sku,asdp_biz_type,asdp_ads,delivery_cps";

    public static final List<String> KUAI_YUN_CP_CORES = Arrays.asList("CN7000001009020", "BESTQJT", "CN7000001000869", "2744832184_543", "CN7000001021040", "3108002701_1011", "SURE", "CP471906", "SFKY");

    private IErrorMsgConverter errorMsgConverter;

    public void setErrorMsgConverter(IErrorMsgConverter errorMsgConverter) {
        this.errorMsgConverter = errorMsgConverter;
    }

    private TaobaoClientHelper buildTaobaoClientHelper(User user) {
        if (errorMsgConverter == null) {
            errorMsgConverter = new TradeErrorMsgConverter();
        }
        return new TaobaoClientHelper(user, errorMsgConverter);
    }

    //************************************************ 退款API ************************************************//

    public RefundGetResponse getRefund(User user, Long refundId) {
        if (null == refundId) {
            throw new IllegalArgumentException("请输入refundId参数");
        }
        RefundGetRequest req = new RefundGetRequest();
        req.setFields("refund_id, alipay_no, tid, oid, buyer_nick, seller_nick, total_fee, status, created, refund_fee, good_status, has_good_return, payment, reason, desc, num_iid, title, price, num, good_return_time, company_name, sid, address, shipping_type, refund_remind_timeout, refund_phase, refund_version, operation_contraint, attribute, outer_id, sku ,asdp_biz_type,asdp_ads,delivery_cps");
        req.setRefundId(refundId);
        return buildTaobaoClientHelper(user).request(req);
    }

    public TaobaoBatchResponse batchGetRefund(User user, List<Long> refundIds) {
        List<TaobaoRequest<RefundGetResponse>> requestList = new ArrayList<TaobaoRequest<RefundGetResponse>>(refundIds.size());
        for (Long refundId : refundIds) {
            RefundGetRequest req = new RefundGetRequest();
            req.setRefundId(refundId);
            req.setFields(REFUND_FIELDS);
            requestList.add(req);
        }
        return buildTaobaoClientHelper(user).batchRequest(requestList);
    }

    public List<Refund> getRefunds(User user, List<Long> refundIds) {
        List<Refund> refundList = new ArrayList<Refund>(refundIds.size());
        logger.debug(LogHelper.buildLog(user.getStaff(), "批量查询售后工单,refundIds:" + JSONObject.toJSONString(refundIds)));
        List<Long> list = new ArrayList<Long>(refundIds.size() > 20 ? 20 : refundIds.size());
        for (int i = 0; i < refundIds.size(); i++) {
            Long refundId = refundIds.get(i);
            list.add(refundId);
            if (list.size() == 20 || i == refundIds.size() - 1) {
                TaobaoBatchResponse resp = batchGetRefund(user, list);
                if (resp != null) {
                    if (resp.getResponseList() != null && !resp.getResponseList().isEmpty()) {
                        for (TaobaoResponse r : resp.getResponseList()) {
                            RefundGetResponse rsp = (RefundGetResponse) r;
                            if (rsp.getRefund() != null) {
                                refundList.add(rsp.getRefund());
                            }
                        }
                    } else {
                        logger.error(LogHelper.buildLog(user.getStaff(), String.format("批量查询售后工单没有空结果:%s,%s, %s", resp.getSubCode(), resp.getSubMsg(), list)));
                    }
                } else {
                    logger.error(LogHelper.buildLog(user.getStaff(), "批量查询售后工单没有返回结果:" + list));
                }
                list.clear();
            }
        }
        return refundList;
    }

    public List<Refund> getReceiveRefunds(User user, String buyerNick, String openUid, Long tid, Date startModified) {
        RefundsReceiveGetRequest req = new RefundsReceiveGetRequest();
        req.setFields("refund_id,tid,oid,good_status,title,buyer_nick,seller_nick,total_fee,status,created,modified,refund_fee,refund_phase");

        if(StringUtils.isNotBlank(openUid)){
            req.setBuyerOpenUid(openUid);
        } else {
            req.setBuyerNick(buyerNick);
        }
        req.setStartModified(startModified);
        RefundsReceiveGetResponse rsp = buildTaobaoClientHelper(user).request(req);
        if (null == rsp || rsp.getRefunds() == null || rsp.getRefunds().size() == 0) {
            return new ArrayList<Refund>();
        }
        if (null == tid || tid <= 0) {
            return rsp.getRefunds();
        }
        // 过滤tid
        List<Refund> list = new ArrayList<Refund>();
        for (Refund refund : rsp.getRefunds()) {
            if (refund.getTid() != null && refund.getTid().longValue() == tid) {
                list.add(refund);
            }
        }
        return list;
    }

    public TimeGetResponse getTaobaoTime(User user) {
        TimeGetRequest req = new TimeGetRequest();
        return buildTaobaoClientHelper(user).request(req);
    }


    //************************************************ 订单API ************************************************//

    public TradeGetResponse getTrade(User user, Long tid, String fields) {
        TradeGetRequest req = new TradeGetRequest();
        if (StringUtils.isEmpty(fields)) {
            req.setFields("seller_nick, buyer_nick, title, type, created, tid, seller_rate, buyer_rate, status, payment, discount_fee, adjust_fee, post_fee, total_fee, pay_time, end_time, modified, consign_time, buyer_obtain_point_fee, point_fee, real_point_fee, received_payment, commission_fee, buyer_memo, seller_memo, alipay_no, buyer_message, pic_path, num_iid, num, price, cod_fee, cod_status, shipping_type， is_daixiao，consign_interval，arrive_interval，arrive_cut_time,orders,order_tax_promotion_fee,order_tax_fee,asdp_biz_type,asdp_ads,delivery_cps,store_code,real_receiver_open_id,real_receiver_display_nick,post_fee_type,real_post_fee,refund_post_fee,gift_post_fee_role,qn_distr,general_new_presell");
        } else {
            req.setFields(fields);
        }
        req.setTid(tid);
        return buildTaobaoClientHelper(user).request(req);
    }

    public TradeFullinfoGetResponse getFullinfoTrade(User user, Long tid, String fields) {
        TradeFullinfoGetRequest req = new TradeFullinfoGetRequest();
        if (StringUtils.isEmpty(fields)) {
            fields = TRADE_FIELDS;
        }
        req.setFields(fields);
        req.setTid(tid);
        return buildTaobaoClientHelper(user).request(req);
    }

    public TradeAmountGetResponse getTradeAmount(User user, Long tid, String fields) {
        TradeAmountGetRequest req = new TradeAmountGetRequest();
        if (StringUtils.isEmpty(fields)) {
            fields = TRADE_AMOUNT_FIELDS;
        }
        req.setFields(fields);
        req.setTid(tid);
        return buildTaobaoClientHelper(user).request(req);
    }

    public TaobaoBatchResponse getFullInfoTrades(User user, List<Long> tidList, final String fields) {
        List<TaobaoRequest<TradeFullinfoGetResponse>> batchReq = new ArrayList<TaobaoRequest<TradeFullinfoGetResponse>>(tidList.size());
        for (Long tid : tidList) {
            TradeFullinfoGetRequest req = new TradeFullinfoGetRequest();
            req.setTid(tid);
            if (StringUtils.isNotEmpty(fields)) {
                req.setFields(fields);
            } else {
                req.setFields(TRADE_FIELDS);
            }
            batchReq.add(req);
        }
        return buildTaobaoClientHelper(user).batchRequest(batchReq);
    }

    public List<com.taobao.api.domain.Trade> getTrades(User user, List<Long> tidList, String fields) {
        List<com.taobao.api.domain.Trade> tradeList = new ArrayList<com.taobao.api.domain.Trade>(tidList.size());
        List<Long> list = new ArrayList<Long>(tidList.size() > 20 ? 20 : tidList.size());
        for (int i = 0; i < tidList.size(); i++) {
            Long tid = tidList.get(i);
            list.add(tid);
            if (list.size() == 20 || i == tidList.size() - 1) {
                TaobaoBatchResponse resp = getFullInfoTrades(user, tidList, fields);
                if (resp != null) {
                    if (resp.getResponseList() != null && !resp.getResponseList().isEmpty()) {
                        for (TaobaoResponse r : resp.getResponseList()) {
                            TradeFullinfoGetResponse rsp = (TradeFullinfoGetResponse) r;
                            if (rsp.getTrade() != null) {
                                tradeList.add(rsp.getTrade());
                            }
                        }
                    } else {
                        logger.error(LogHelper.buildLog(user.getStaff(), String.format("批量查询订单返回空列表:%s,%s, %s", resp.getSubCode(), resp.getSubMsg(), list)));
                    }
                } else {
                    logger.error(LogHelper.buildLog(user.getStaff(), "批量查询订单没有返回结果:" + list));
                }
                list.clear();
            }
        }
        return tradeList;
    }

    public TradesSoldGetResponse queryTradeCount(User user, Date startTime, Date endTime, String status) {
        TradesSoldGetRequest req = new TradesSoldGetRequest();
        req.setFields("total_fee");
        req.setStartCreated(startTime);
        req.setEndCreated(endTime);
        req.setStatus(status);
        req.setPageNo(1L);
        req.setPageSize(0L);
        req.setType("guarantee_trade,auto_delivery,ec,cod,step,fixed,tmall_i18n");
        return buildTaobaoClientHelper(user).request(req);
    }

    public TradesSoldGetResponse queryTradesSold(User user, Page page, Date startCreated, Date endCreated, String status, String fields) {
        TradesSoldGetRequest req = new TradesSoldGetRequest();
        if (StringUtils.isNotBlank(fields)) {
            req.setFields(fields);
        } else {
            req.setFields("seller_nick, buyer_nick, title, type, created, tid, status, payment, discount_fee, adjust_fee, post_fee, total_fee, pay_time, end_time, " +
                    "modified, consign_time, buyer_obtain_point_fee, point_fee, real_point_fee, received_payment, pic_path, num_iid, num, price, cod_fee, cod_status, " +
                    "shipping_type, receiver_name, receiver_state, receiver_city, receiver_district, receiver_address, receiver_zip, receiver_mobile, receiver_phone," +
                    "seller_flag,alipay_id,alipay_no,is_lgtype,is_force_wlb,is_brand_sale,buyer_area,has_buyer_message, credit_card_fee, lg_aging_type, lg_aging, " +
                    "step_trade_status,step_paid_fee,mark_desc,has_yfx,yfx_fee,yfx_id,yfx_type,trade_source,send_time,is_daixiao,is_wt,is_part_consign,zero_purchase," +
                    "orders,order_tax_promotion_fee,order_tax_fee,threepl_timing,tmall_delivery,buyerTaxNO");
        }
        req.setStartCreated(startCreated);
        req.setEndCreated(endCreated);
        req.setType("guarantee_trade,auto_delivery,ec,cod,step,fixed,tmall_i18n");
        if (page != null) {
            req.setPageNo((long) page.getPageNo());
            req.setPageSize((long) page.getPageSize());
        }
        if (status != null && !status.isEmpty()) {
            req.setStatus(status);
        }
        req.setUseHasNext(true);
        return buildTaobaoClientHelper(user).request(req);
    }

    public TradesSoldIncrementGetResponse queryTradesSoldIncrement(User user, Page page, Date startModified, Date endModified, String fields, boolean useHasNext) {
        TradesSoldIncrementGetRequest req = new TradesSoldIncrementGetRequest();
        req.setFields(StringUtils.isNotBlank(fields) ? fields : "seller_nick, buyer_nick, title, type, created, tid, seller_rate,seller_can_rate, buyer_rate,can_rate,status, payment, discount_fee, adjust_fee, post_fee, total_fee, pay_time, end_time, modified, consign_time, buyer_obtain_point_fee, point_fee, real_point_fee, received_payment,pic_path, num_iid, num, price, cod_fee, cod_status, shipping_type, receiver_name, receiver_state, receiver_city, receiver_district, receiver_address, receiver_zip, receiver_mobile, receiver_phone,alipay_id,alipay_no,is_lgtype,is_force_wlb,is_brand_sale,has_buyer_message,credit_card_fee,step_trade_status,step_paid_fee,mark_desc,send_time,,has_yfx,yfx_fee,yfx_id,yfx_type,trade_source,seller_flag,is_daixiao,is_part_consign,zero_purchase,orders,order_tax_promotion_fee,order_tax_fee,trade_attr");
        req.setStartModified(startModified);
        req.setEndModified(endModified);
        if (page != null) {
            req.setPageNo((long) page.getPageNo());
            req.setPageSize((long) page.getPageSize());
        }
        req.setUseHasNext(useHasNext);
        req.setType("guarantee_trade,auto_delivery,ec,cod,step,fixed,tmall_i18n");
        return buildTaobaoClientHelper(user).request(req);
    }

    public TradeCloseResponse closeTrade(User user, Long tid, String reason) {
        TradeCloseRequest req = new TradeCloseRequest();
        req.setTid(tid);
        req.setCloseReason(reason);
        return buildTaobaoClientHelper(user).request(req);
    }

    public TradeShippingaddressUpdateResponse updateShippingAddress(User user, Trade trade) {
        TradeShippingaddressUpdateRequest req = new TradeShippingaddressUpdateRequest();
        // 特殊地址处理回滚 https://tb.raycloud.com/task/629465071902a00076443c2e
        TradeAddressUtil.rollbackCompatibleSpecialAddress(trade);
        req.setTid(Long.parseLong(trade.getTid()));
        req.setReceiverName(trade.getReceiverName());
        req.setReceiverPhone(trade.getReceiverPhone());
        req.setReceiverMobile(trade.getReceiverMobile());
        req.setReceiverState(trade.getReceiverState());
        req.setReceiverCity(trade.getReceiverCity());
        req.setReceiverDistrict(trade.getReceiverDistrict());
        req.setReceiverAddress(trade.getReceiverAddress());
        req.setReceiverZip(trade.getReceiverZip());
        if (StringUtils.isNotEmpty(trade.getReceiverStreet())) {
            req.setReceiverTown(trade.getReceiverStreet());
        }
        return buildTaobaoClientHelper(user).request(req);
    }

    public TradeMemoUpdateResponse updateSellerMemoFlag(User user, MemoUpdateRequest param, long tid) {
        TradeMemoUpdateRequest req = new TradeMemoUpdateRequest();
        req.setTid(tid);
        req.setReset(param.isReset());
        req.setMemo(param.getSellerMemo());
        req.setFlag(param.getSellerFlag());
        req.setTag(param.getSellerFlagTag());
        return buildTaobaoClientHelper(user).request(req);
    }

    public TradePostageUpdateResponse updatePostage(User user, Long tid, Double postFee) {
        TradePostageUpdateRequest req = new TradePostageUpdateRequest();
        req.setTid(tid);
        req.setPostFee(postFee.toString());
        return buildTaobaoClientHelper(user).request(req);
    }

    public TradeReceivetimeDelayResponse delayReceiveTime(User user, Long tid, Long days) {
        TradeReceivetimeDelayRequest req = new TradeReceivetimeDelayRequest();
        req.setTid(tid);
        req.setDays(days);
        return buildTaobaoClientHelper(user).request(req);
    }

    /**
     * 合并/取消合并订单结果回传
     */
    public FulfillmentOrderAssembleResponse uploadMergeResult(Staff staff, User user, List<FulfillmentOrderAssembleRequest.AssembleOrder> assembleOrders, Boolean isMerge) {
        if (CollectionUtils.isEmpty(assembleOrders)) {
            return null;
        }
        FulfillmentOrderAssembleRequest req = new FulfillmentOrderAssembleRequest();
        req.setAssembleOrders(assembleOrders);
        req.setType("CANCEL_MERGE");
        if (isMerge) {
            req.setType("MERGE");
        }
        FulfillmentOrderAssembleResponse response = null;
        for (int i = 1; i <= 3; i++) {
            try {
                response = buildTaobaoClientHelper(user).request(req);
                if(ObjectUtils.isEmpty(response)) {
                    continue;
                }
                if (response.getResult()) {
                    return response;
                }

            } catch (Exception e) {
                logger.error(LogHelper.buildLogHead(staff).append("拆合单结果回传失败"), e);
            }
        }
        if (!ObjectUtils.isEmpty(response) && !response.getResult()) {
            logger.info(LogHelper.buildLogHead(staff).append("合并/取消合并订单结果回传失败 ").append(", sid:")
                    .append(assembleOrders.stream().map(FulfillmentOrderAssembleRequest.AssembleOrder::getGroupId).collect(Collectors.toList()))
                    .append(", error_code:").append(response.getCallErrorCode())
                    .append(", error_msg:").append(response.getCallErrorMsg()));
        }
        return response;
    }


    //************************************************ 物流API ************************************************//

    public LogisticsOnlineSendResponse sendOnlineLogistics(User user, Long tid, String subTids, String outSid, String companyCode) {
        LogisticsOnlineSendRequest req = new LogisticsOnlineSendRequest();
        req.setTid(tid);
        if (!StringUtils.isEmpty(subTids)) {
            req.setSubTid(subTids);
            req.setIsSplit(1L);
        }
        if (!StringUtils.isEmpty(outSid)) {
            req.setOutSid(outSid);
        }
        req.setCompanyCode(companyCode);
        return buildTaobaoClientHelper(user).request(req);
    }

    public LogisticsOnlineConfirmResponse confirmOnlineLogistics(User user, Long tid, Long isSplit, String subTids, String outSid) {
        LogisticsOnlineConfirmRequest req = new LogisticsOnlineConfirmRequest();
        req.setTid(tid);
        if (!StringUtils.isEmpty(subTids)) {
            req.setSubTid(subTids);
        }
        if (null != isSplit) {
            req.setIsSplit(isSplit);
        }
        if (!StringUtils.isEmpty(outSid)) {
            req.setOutSid(outSid);
        }
        return buildTaobaoClientHelper(user).request(req);
    }

    public LogisticsOnlineCancelResponse cancelOnlineLogistics(User user, Long tid) {
        LogisticsOnlineCancelRequest req = new LogisticsOnlineCancelRequest();
        req.setTid(tid);
        return buildTaobaoClientHelper(user).request(req);
    }

    public AlibabaAscpLogisticsOfflineSendResponse sendOfflineLogistics(User user, Long tid, String subTids, String outSid, String companyCode, String feature) {
        AlibabaAscpLogisticsOfflineSendRequest req = new AlibabaAscpLogisticsOfflineSendRequest();
        req.setTid(String.valueOf(tid));
        if (StringUtils.isNotBlank(subTids)) {
            req.setSubTid(subTids);
        }
        if (StringUtils.isNotEmpty(feature)) {
            req.setFeature(feature);
        }

        AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest topConsignPkgRequest =new AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest();
        topConsignPkgRequest.setCompanyCode(companyCode);
        topConsignPkgRequest.setOutSid(outSid);
        List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest> topConsignPkgRequestList =new ArrayList<>();
        topConsignPkgRequestList.add(topConsignPkgRequest);

        req.setConsignPkgs(topConsignPkgRequestList);
        return buildTaobaoClientHelper(user).request(req);
    }


    public AlibabaAscpLogisticsIdentcodeUploadResponse sendOfflineLogisticsIdentcode(User user, Long orderId, String codeList) {
        AlibabaAscpLogisticsIdentcodeUploadRequest req = new AlibabaAscpLogisticsIdentcodeUploadRequest();
        req.setOrderId(orderId);
        req.setCodeList(codeList);
        return buildTaobaoClientHelper(user).request(req);
    }

    public WlbOrderJzConsignResponse sendJzLogistics(User user, Trade trade, String companyCode, String expressCompanyName) {
        String jzCompanyCode = "";
        String jzCompanyName = "";
        if (StringUtils.isNotBlank(trade.getAddressMd5()) && (StringUtils.contains(trade.getReceiverName(),"*") || StringUtils.contains(trade.getReceiverPhone(),"*")
                || StringUtils.contains(trade.getReceiverMobile(),"*") || StringUtils.contains(trade.getReceiverAddress(), "*"))) {
            //如果订单是脱敏的，需要先拿到明文再去查询家装公司
            List<TopOaidDecryptRequest.ReceiverQuery> receiverQueryList = new ArrayList<>();
            TopOaidDecryptRequest.ReceiverQuery receiverQuery = new TopOaidDecryptRequest.ReceiverQuery();
            receiverQuery.setTid(String.valueOf(trade.getTid()));
            receiverQuery.setOaid(trade.getAddressMd5());
            receiverQuery.setScene(TradeDecryptTypeEnum.OFFLINE_DECRYPT_CODE.getValue());
            receiverQuery.setSecretNoDays(SECRET_NO_DAY_DEFAULT);
            receiverQueryList.add(receiverQuery);
            logger.debug(LogHelper.buildLogHead(user).append("拼装家装请求参数之前调用解密接口获取地址明文信息，解密参数:").append(JSON.toJSONString(receiverQuery)));
            TopOaidDecryptResponse decryptResponse = decrypt(user, receiverQueryList);
            if (decryptResponse.isSuccess()) {
                TopOaidDecryptResponse.Receiver receiver = decryptResponse.getReceiverList().get(0);
                trade.setReceiverName(receiver.getName());
                trade.setReceiverPhone(receiver.getPhone());
                trade.setReceiverMobile(receiver.getMobile());
                trade.setReceiverAddress(receiver.getAddressDetail());
            } else {
                WlbOrderJzConsignResponse rsp = new WlbOrderJzConsignResponse();
                rsp.setResultSuccess(false);
                rsp.setResultErrorMsg("家装订单上传查询安装公司时解密订单失败："+decryptResponse.getMessage()+";"+decryptResponse.getSubMessage());
                return rsp;
            }
        }
        WlbOrderJzQueryRequest wlbOrderJzQueryRequest = buildJzCompanyQueryRequest(trade);
        logger.debug(LogHelper.buildLogHead(user).append("拼装家装请求参数").append(JSON.toJSONString(wlbOrderJzQueryRequest)));
        WlbOrderJzQueryResponse jzCompanyResponse = buildTaobaoClientHelper(user).request(wlbOrderJzQueryRequest);
        if (jzCompanyResponse.getResultSuccess() && jzCompanyResponse.getResult() != null) {
            List<TPDTO> insTps = jzCompanyResponse.getResult().getInsTps();
            if (CollectionUtils.isNotEmpty(insTps)) {
                jzCompanyCode = insTps.get(0).getCode();
                jzCompanyName = insTps.get(0).getName();
            }
        } else {
            WlbOrderJzConsignResponse rsp = new WlbOrderJzConsignResponse();
            rsp.setResultSuccess(jzCompanyResponse.getResultSuccess());
            rsp.setResultErrorMsg(jzCompanyResponse.getResultErrorMsg());
            return rsp;
        }
        WlbOrderJzConsignRequest req = buildJzConsignRequest(trade, companyCode, expressCompanyName, jzCompanyCode, jzCompanyName);
        WlbOrderJzConsignResponse rsp = buildTaobaoClientHelper(user).request(req);
        if(rsp != null && (
                !rsp.isSuccess()
                || (rsp.getResultSuccess() == null || !rsp.getResultSuccess())
                || StringUtils.isNotBlank(rsp.getResultErrorMsg())
            )
        ){
            logger.error(LogHelper.buildLogHead(user).append(String.format("家装接口上传发货失败,req:%s", JSONObject.toJSONString(req))));
            logger.error(LogHelper.buildLogHead(user).append(String.format("家装接口上传发货失败,rsp:%s", JSONObject.toJSONString(rsp))));
        }
        return rsp;
    }

    private WlbOrderJzQueryRequest buildJzCompanyQueryRequest(Trade trade) {
        WlbOrderJzQueryRequest req = new WlbOrderJzQueryRequest();
        req.setTid(Long.valueOf(trade.getTid()));
        WlbOrderJzQueryRequest.JzReceiverTO obj1 = new WlbOrderJzQueryRequest.JzReceiverTO();
        obj1.setTelePhone(trade.getReceiverPhone() == null ? null : trade.getReceiverPhone().trim());
        obj1.setMobilePhone(trade.getReceiverMobile() == null ? null : trade.getReceiverMobile().trim());
        obj1.setAddress(trade.getReceiverAddress());
        obj1.setContactName(trade.getReceiverName());
//        obj1.setStreet("成山镇龙须岛农业银行西船电修理");
        obj1.setZipCode(trade.getReceiverZip());
        obj1.setProvince(trade.getReceiverState());
        obj1.setDistrict(trade.getReceiverDistrict());
        obj1.setCity(trade.getReceiverCity());
//        obj1.setCountry("demo");
        req.setJzReceiverTo(obj1);
        return req;
    }

    private WlbOrderJzConsignRequest buildJzConsignRequest(Trade trade, String companyCode, String expressCompanyName, String jzCompanyCode, String jzCompanyName) {
        WlbOrderJzConsignRequest req = new WlbOrderJzConsignRequest();
        req.setTid(Long.valueOf(trade.getTid()));
        WlbOrderJzConsignRequest.JzTopArgs obj2 = new WlbOrderJzConsignRequest.JzTopArgs();
        obj2.setMailNo(trade.getOutSid());
        obj2.setPackageWeight(trade.getWeight().toString());
        obj2.setZyConsignTime(DateUtils.date2Str(new Date()));
        obj2.setPackageVolume(trade.getVolume().toString());
        req.setJzTopArgs(obj2);
        WlbOrderJzConsignRequest.TPDTO obj3 = new WlbOrderJzConsignRequest.TPDTO();
        obj3.setName(expressCompanyName);
        obj3.setCode(companyCode);
        req.setLgTpDto(obj3);
        if(StringUtils.isNotBlank(jzCompanyCode) && StringUtils.isNotBlank(jzCompanyName)) {
            WlbOrderJzConsignRequest.TPDTO obj4 = new WlbOrderJzConsignRequest.TPDTO();
            obj4.setName(jzCompanyName);
            obj4.setCode(jzCompanyCode);
            req.setInsTpDto(obj4);
        }
        WlbOrderJzConsignRequest.JzReceiverTO obj1 = new WlbOrderJzConsignRequest.JzReceiverTO();
        obj1.setTelePhone(trade.getReceiverPhone());
        obj1.setMobilePhone(trade.getReceiverMobile());
        obj1.setAddress(trade.getReceiverAddress());
        obj1.setContactName(trade.getReceiverName());
        obj1.setZipCode(trade.getReceiverZip());
        obj1.setProvince(trade.getReceiverState());
        obj1.setDistrict(trade.getReceiverDistrict());
        obj1.setCity(trade.getReceiverCity());
        req.setJzReceiverTo(obj1);
        WlbOrderJzConsignRequest.JzReceiverTO obj5 = new WlbOrderJzConsignRequest.JzReceiverTO();
        obj5.setTelePhone(trade.getReceiverPhone());
        obj5.setMobilePhone(trade.getReceiverMobile());
        obj5.setAddress(trade.getReceiverAddress());
        obj5.setContactName(trade.getReceiverName());
//        obj5.setStreet("万塘路");
        obj5.setZipCode(trade.getReceiverZip());
        obj5.setProvince(trade.getReceiverState());
        obj5.setDistrict(trade.getReceiverDistrict());
        obj5.setCity(trade.getReceiverCity());
//        obj5.setCountry(trade.get);
        req.setInsReceiverTo(obj5);
        return req;
    }

    public WlbOrderJzConsignResponse sendOfflineJzLogistics(User user, Long tid, String expressName, String expressCode) {
        WlbOrderJzConsignRequest req = new WlbOrderJzConsignRequest();
        req.setTid(tid);
        //物流信息
        WlbOrderJzConsignRequest.TPDTO obj3 = new WlbOrderJzConsignRequest.TPDTO();
        obj3.setName(expressName);
        obj3.setCode(expressCode);
        req.setLgTpDto(obj3);

        return buildTaobaoClientHelper(user).request(req);
    }

    public LogisticsDummySendResponse sendDummyLogistics(User user, Long tid) {
        LogisticsDummySendRequest req = new LogisticsDummySendRequest();
        req.setTid(tid);
        return buildTaobaoClientHelper(user).request(req);
    }

    public AlibabaAscpLogisticsConsignResendResponse logisticsConsignResend(User user, Long tid, String subTids, String outSid, String companyCode,String feature) {
        AlibabaAscpLogisticsConsignResendRequest req = new AlibabaAscpLogisticsConsignResendRequest();
        req.setTid(String.valueOf(tid));
        if (StringUtils.isNotEmpty(subTids)) {
            req.setSubTids(subTids);
        }
        req.setFeature(feature);
        AlibabaAscpLogisticsConsignResendRequest.TopConsignPkgRequest topConsignPkgRequest =new AlibabaAscpLogisticsConsignResendRequest.TopConsignPkgRequest();
        topConsignPkgRequest.setCompanyCode(companyCode);
        topConsignPkgRequest.setOutSid(outSid);
        List<AlibabaAscpLogisticsConsignResendRequest.TopConsignPkgRequest> topConsignPkgRequestList =new ArrayList<>();
        topConsignPkgRequestList.add(topConsignPkgRequest);

        req.setConsignPkgs(topConsignPkgRequestList);
        return buildTaobaoClientHelper(user).request(req);
    }

    public LogisticsOrdersGetResponse LogisticsOrderGet(User user, Long tid) {
        LogisticsOrdersGetRequest req = new LogisticsOrdersGetRequest();
        req.setTid(tid);
        req.setFields("tid,status,order_code,out_sid,sub_tids");
        return buildTaobaoClientHelper(user).request(req);
    }

    public LogisticsOrdersGetResponse logisticsOrdersGet(User user, String status, Date startCreated, Date endCreated, Page page) {
        LogisticsOrdersGetRequest req = new LogisticsOrdersGetRequest();
        req.setStatus(status);
        req.setSellerConfirm("yes");
        req.setStartCreated(startCreated);
        req.setEndCreated(endCreated);
        req.setFields("tid,status,order_code,out_sid,sub_tids,company_name,seller_confirm,delivery_start");
        if (null != page) {
            req.setPageNo((long) page.getPageNo());
            req.setPageSize((long) page.getPageSize());
        }
        return buildTaobaoClientHelper(user).request(req);
    }

    public LogisticsAddressSearchResponse addressSearch(User user) {
        LogisticsAddressSearchRequest req = new LogisticsAddressSearchRequest();
        return buildTaobaoClientHelper(user).request(req);
    }

//    public LogisticsAddressReachableResponse addressReachable(User user, String expressCompanyCode, String address) {
//        LogisticsAddressReachableRequest req = new LogisticsAddressReachableRequest();
//        req.setAddress(address);
//        req.setPartnerIds(expressCompanyCode);
//        req.setServiceType(88L);
//        return buildTaobaoClientHelper(user).request(req);
//    }


    /**
     * 批量菜鸟获取可达 objectId 是 sid
     */
    public List<CainiaoReachableBatchjudgeResponse.ReachableServiceWaybillResponseDto> addressReachablebatch(User user, List<Trade> trades, ExpressCompany expressCompany) {
        CainiaoReachableBatchjudgeRequest req = new CainiaoReachableBatchjudgeRequest();
        CainiaoReachableBatchjudgeRequest.ClientInfoDto clientInfoDto = new CainiaoReachableBatchjudgeRequest.ClientInfoDto();
        clientInfoDto.setDescription("超级掌柜");
        req.setClientInfo(clientInfoDto);
        long addressType = KUAI_YUN_CP_CORES.contains(expressCompany.getCode()) ? 2L : 1L;
        req.setAddressType(addressType);
        CainiaoReachableBatchjudgeRequest.RoutingReachableBatchRequestDto routingReachableBatchRequestDto = new CainiaoReachableBatchjudgeRequest.RoutingReachableBatchRequestDto();
        routingReachableBatchRequestDto.setCpCode(expressCompany.getCode());
        List<CainiaoReachableBatchjudgeRequest.ReachableAddressAndServiceDto> addressAndServiceList = new ArrayList<>();
        int objId = 100;
        for (Trade trade : trades) {
            CainiaoReachableBatchjudgeRequest.ReachableAddressAndServiceDto reachableAddressAndServiceDto = new CainiaoReachableBatchjudgeRequest.ReachableAddressAndServiceDto();
            reachableAddressAndServiceDto.setObjectId(trade.getSid().toString());
            CainiaoReachableBatchjudgeRequest.ReceiveAddress receiveAddress = new CainiaoReachableBatchjudgeRequest.ReceiveAddress();
            receiveAddress.setProvinceName(trade.getReceiverState());
            receiveAddress.setCityName(trade.getReceiverCity());
            receiveAddress.setAreaName(trade.getReceiverDistrict());
            receiveAddress.setAddressDetail(trade.getReceiverAddress());
            reachableAddressAndServiceDto.setReceiveAddress(receiveAddress);
            if (TradeUtils.isTbTrade(user.getSource()) && StringUtils.isNotBlank(trade.getAddressMd5())) {//oaid
                reachableAddressAndServiceDto.setOaid(trade.getAddressMd5());
            }
/*            if (CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(user.getSource()) && StringUtils.isNotBlank(trade.getAddressMd5())) {
                reachableAddressAndServiceDto.setCaid(trade.getAddressMd5());
            }*/
            addressAndServiceList.add(reachableAddressAndServiceDto);
        }
        routingReachableBatchRequestDto.setAddressAndServiceList(addressAndServiceList);
        req.setData(routingReachableBatchRequestDto);
        CainiaoReachableBatchjudgeResponse reachableBatchjudgeResponse = buildTaobaoClientHelper(user).request(req);
        if (reachableBatchjudgeResponse != null && reachableBatchjudgeResponse.isSuccess()) {
            CainiaoReachableBatchjudgeResponse.BaseResultDto baseResultDto = reachableBatchjudgeResponse.getResult();
            if (baseResultDto != null && Boolean.TRUE.equals(baseResultDto.getSuccess())) {
                if (baseResultDto.getModule() != null && CollectionUtils.isNotEmpty(baseResultDto.getModule().getResultList())) {
                        return baseResultDto.getModule().getResultList();
                } else {
                    throw new IllegalArgumentException("未获取到可达数据!");
                }
            } else {
                if (baseResultDto != null && !Boolean.TRUE.equals(baseResultDto.getSuccess()) && baseResultDto.getOneErrorInfo() != null) {
                    CainiaoReachableBatchjudgeResponse.ErrorInfo errorInfo = baseResultDto.getOneErrorInfo();
                    throw new IllegalArgumentException("可达接口报错:" + errorInfo.getErrorMessage());
                }
                throw new IllegalArgumentException("可达接口未获取到响应结果!");
            }
        } else {
            throw new IllegalArgumentException("可达接口未获取到响应结果或失败:" + reachableBatchjudgeResponse.getMsg() + " " + reachableBatchjudgeResponse.getSubMessage());
        }


    }

//    public LogisticsAddressReachablebatchGetResponse addressReachablebatch(User user, List<String> expressCompanyCodes, List<String> addresses) {
//        LogisticsAddressReachablebatchGetRequest req = new LogisticsAddressReachablebatchGetRequest();
//        if (expressCompanyCodes == null || addresses == null || expressCompanyCodes.size() == 0 || addresses.size() == 0
//                || expressCompanyCodes.size() != addresses.size()) {
//            throw new IllegalArgumentException("请传入有效的参数！");
//        }
//        // 构建参数
//        JSONArray ja = new JSONArray(addresses.size());
//        for (int i = 0; i < addresses.size(); i++) {
//            String code = expressCompanyCodes.get(i);
//            String address = addresses.get(i);
//            JSONObject jo = new JSONObject();
//            jo.put("address", address);
//            jo.put("partner_id", code);
//            jo.put("service_type", "88");
//            ja.add(jo);
//        }
//        req.setAddressList(ja.toJSONString());
//        return buildTaobaoClientHelper(user).request(req);
//    }

//    public List<AddressReachableResult> addressReachableNew(User user, Address sendAddress, Address recipientAddress, String expressCompanyCode) {
//        CainiaoConsignPlatformLineAvailableRequest req = new CainiaoConsignPlatformLineAvailableRequest();
//        CainiaoConsignPlatformLineAvailableRequest.LineAvailableDto lineAvailableDto = new CainiaoConsignPlatformLineAvailableRequest.LineAvailableDto();
//        lineAvailableDto.setOrderChannelsType("TB");
//        lineAvailableDto.setTradeIdList(Collections.singletonList(System.currentTimeMillis() + ""));
//        lineAvailableDto.setRecipientAddress(convertAddress(recipientAddress));
//        lineAvailableDto.setSendAddress(convertAddress(sendAddress));
//        lineAvailableDto.setCpCodeList(Arrays.asList((StringUtils.isEmpty(expressCompanyCode) ? "" : expressCompanyCode).split(",")));
//        req.setLineAvailableDTO(lineAvailableDto);
//        CainiaoConsignPlatformLineAvailableResponse response = buildTaobaoClientHelper(user).request(req);
////        Logs.debug("test req:" + JSONObject.toJSONString(req));
////        Logs.debug("test response:" + JSONObject.toJSONString(response));
//        if (response.isSuccess() && response.getResult().getSuccess() && response.getResult().getData() != null && CollectionUtils.isNotEmpty(response.getResult().getData().getCpAvailableResultList())) {
//            List<AddressReachableResult> results = new ArrayList<>();
//            for (CainiaoConsignPlatformLineAvailableResponse.CpAvailableResultDto cpAvailableResultDto : response.getResult().getData().getCpAvailableResultList()) {
//                AddressReachableResult result = new AddressReachableResult();
//                result.setPartnerCode(cpAvailableResultDto.getCpCode());
//                result.setServiceType(88L);
//                result.setReachable("Y".equalsIgnoreCase(cpAvailableResultDto.getAvailableLevel()) ? 1L : 2L);
//                results.add(result);
//            }
//            return results;
//        }
//        return null;
//
//    }


//    public List<LogisticsAddressReachablebatchGetResponse.AddressReachableTopResult> addressReachableBatchNew(User user, Address sendAddress, List<Address> recipientAddress, List<String> companyCodes) {
//        List<LogisticsAddressReachablebatchGetResponse.AddressReachableTopResult> topResults = new ArrayList<>();
//        List<TaobaoRequest<CainiaoConsignPlatformLineAvailableResponse>> batchReq = new ArrayList<>();
//        for (int i = 0; i < recipientAddress.size(); i++) {
//            Address address = recipientAddress.get(i);
//            String companyCode = companyCodes.get(i);
//            CainiaoConsignPlatformLineAvailableRequest req = new CainiaoConsignPlatformLineAvailableRequest();
//            CainiaoConsignPlatformLineAvailableRequest.LineAvailableDto lineAvailableDto = new CainiaoConsignPlatformLineAvailableRequest.LineAvailableDto();
//            lineAvailableDto.setOrderChannelsType("TB");
//            lineAvailableDto.setTradeIdList(Collections.singletonList(System.currentTimeMillis() + ""));
//            lineAvailableDto.setRecipientAddress(convertAddress(address));
//            lineAvailableDto.setSendAddress(convertAddress(sendAddress));
//            lineAvailableDto.setCpCodeList(Arrays.asList((StringUtils.isEmpty(companyCode) ? "" : companyCode).split(",")));
//            req.setLineAvailableDTO(lineAvailableDto);
//            batchReq.add(req);
//            Logs.debug("test reqB:" + JSONObject.toJSONString(req));
//        }
//        TaobaoBatchResponse taobaoBatchResponse = buildTaobaoClientHelper(user).batchRequest(batchReq);
//        List<TaobaoResponse> responseList = taobaoBatchResponse.getResponseList();
//
//        for (TaobaoResponse taobaoResponse : responseList) {
//            CainiaoConsignPlatformLineAvailableResponse response = (CainiaoConsignPlatformLineAvailableResponse) taobaoResponse;
//            Logs.debug("test responseb:" + JSONObject.toJSONString(response));
//            LogisticsAddressReachablebatchGetResponse.AddressReachableTopResult reachableTopResult = new LogisticsAddressReachablebatchGetResponse.AddressReachableTopResult();
//            if (response.isSuccess() && response.getResult().getSuccess() && response.getResult().getData() != null && CollectionUtils.isNotEmpty(response.getResult().getData().getCpAvailableResultList())) {
//                List<LogisticsAddressReachablebatchGetResponse.AddressReachableResult> results = new ArrayList<>();
//                for (CainiaoConsignPlatformLineAvailableResponse.CpAvailableResultDto cpAvailableResultDto : response.getResult().getData().getCpAvailableResultList()) {
//                    LogisticsAddressReachablebatchGetResponse.AddressReachableResult result = new LogisticsAddressReachablebatchGetResponse.AddressReachableResult();
//                    result.setPartnerCode(cpAvailableResultDto.getCpCode());
//                    result.setServiceType("88");
//                    result.setReachable("Y".equalsIgnoreCase(cpAvailableResultDto.getAvailableLevel()) ? "1" : "2");
//                    results.add(result);
//                }
//                reachableTopResult.setReachableResultList(results);
//                topResults.add(reachableTopResult);
//            }
//        }
//        return topResults;
//
//    }

    private CainiaoConsignPlatformLineAvailableRequest.AddressDto convertAddress(Address address) {
        CainiaoConsignPlatformLineAvailableRequest.AddressDto addressDto = new CainiaoConsignPlatformLineAvailableRequest.AddressDto();
        if (address != null) {
            addressDto.setProvince(address.getProvince());
            addressDto.setCity(address.getCity());
            addressDto.setDistrict(address.getCountry());
            addressDto.setDetail(address.getAddr());
        }
        return addressDto;
    }


    //************************************************ 评价API ************************************************//

    /*public TraderatesGetResponse getTradeRate(User user, Long tid, String role) {
        if (tid == null) {
            throw new IllegalArgumentException("请输入tid参数");
        }
        if (StringUtils.isEmpty(role)) {
            throw new IllegalArgumentException("请输入role参数");
        }
        TraderatesGetRequest req=new TraderatesGetRequest();
        req.setFields("tid,oid,role,nick,result,created,rated_nick,item_title,item_price,content,reply,num_iid");
        req.setTid(tid);
        if ("buyer".equals(role)) {
            req.setRateType("get");
        } else if ("seller".equals(role)) {
            req.setRateType("give");
        } else {
            throw new IllegalArgumentException("role参数只能传buyer或者seller");
        }
        req.setRole(role);
        return buildTaobaoClientHelper(user).request(req);
    }*/


    //************************************************ 订单链路用户API ************************************************//
    public QimenTradeUserAddResponse addQimenEventUser(User user, String memo) {
        QimenTradeUserAddRequest req = new QimenTradeUserAddRequest();
        req.setMemo(memo);
        return buildTaobaoClientHelper(user).request(req);
    }

    public QimenTradeUsersGetResponse getQimenEventUser(User user, Page page) {
        QimenTradeUsersGetRequest req = new QimenTradeUsersGetRequest();
        req.setPageIndex(page.getPageNo().longValue());
        req.setPageSize(page.getPageSize().longValue());
        return buildTaobaoClientHelper(user).request(req);
    }

    public QimenTradeUserDeleteResponse deleteQimenEventUser(User user) {
        QimenTradeUserDeleteRequest req = new QimenTradeUserDeleteRequest();
        return buildTaobaoClientHelper(user).request(req);
    }


    //************************************************ 发票API ************************************************//
    public TaobaoBatchResponse batchGetInvoices(User user, List<String> tidList) {
        List<TaobaoRequest<AlibabaEinvoiceApplyGetResponse>> list = new ArrayList<TaobaoRequest<AlibabaEinvoiceApplyGetResponse>>(tidList.size());
        for (String tid : tidList) {
            AlibabaEinvoiceApplyGetRequest req = new AlibabaEinvoiceApplyGetRequest();
            req.setPlatformTid(tid);
            list.add(req);
        }
        return buildTaobaoClientHelper(user).batchRequest(list);
    }

    public AlibabaEinvoiceApplyGetResponse getInvoice(User user, String tid) {
        AlibabaEinvoiceApplyGetRequest request = new AlibabaEinvoiceApplyGetRequest();
        request.setPlatformTid(tid);
        return buildTaobaoClientHelper(user).request(request);
    }


    //oaid解密
    public TopOaidDecryptResponse decrypt(User user, List<TopOaidDecryptRequest.ReceiverQuery> decryptList) {
        TopOaidDecryptRequest request = new TopOaidDecryptRequest();
        request.setQueryList(decryptList);
        return buildTaobaoClientHelper(user).requestWithHttps(request);
    }
    //查询可以合并的订单
    public TopOaidMergeResponse queryMerge(User user, List<TopOaidMergeRequest.OrderMerge> mergeList) {
        TopOaidMergeRequest request = new TopOaidMergeRequest();
        request.setMergeList(mergeList);
        return buildTaobaoClientHelper(user).request(request);
    }

    public TradesSoldQueryResponse queryByReceiverMobile(User user, List<TradesSoldQueryRequest.OrderQuery> mobileList) {
        TradesSoldQueryRequest request = new TradesSoldQueryRequest();
        request.setQueryList(mobileList);
        request.setScene("1002");//1001查询近7天订单，1002查询近3个月订单
        return buildTaobaoClientHelper(user).request(request);
    }

    //批量接口
    public <T extends TaobaoResponse> TaobaoBatchResponse batchQueryByReceiver(List<TradesSoldQueryRequest> queryRequestList) {
        List<TaobaoRequest<T>> requestList = new ArrayList<>();
        for (TradesSoldQueryRequest query:queryRequestList) {
            requestList.add((TaobaoRequest<T>) query);
        }
        return new TaobaoClientHelper().batchRequest(requestList);
    }

    public RdcAligeniusLogisticsPackagesNoticeResponse packagesNotice(User user, PackagesNoticeRequest request) {
        Trade trade = request.getTrade();
        RdcAligeniusLogisticsPackagesNoticeRequest req = new RdcAligeniusLogisticsPackagesNoticeRequest();
        RdcAligeniusLogisticsPackagesNoticeRequest.LogisticsNoticeDto logisticsNoticeDto = new RdcAligeniusLogisticsPackagesNoticeRequest.LogisticsNoticeDto();
        List<RdcAligeniusLogisticsPackagesNoticeRequest.CommodityInfo> commodityInfoList = new ArrayList<RdcAligeniusLogisticsPackagesNoticeRequest.CommodityInfo>();
        for(com.raycloud.dmj.domain.trades.Order order : TradeUtils.getOrders4Trade(trade)){
            if(order.isNeedUploadConsign()){
                RdcAligeniusLogisticsPackagesNoticeRequest.CommodityInfo commodityInfo = new RdcAligeniusLogisticsPackagesNoticeRequest.CommodityInfo();
                commodityInfo.setGoodsQuantity(Long.valueOf(order.getNum()));
                commodityInfo.setItemId(order.getNumIid());
                commodityInfoList.add(commodityInfo);
            }
        }
        logisticsNoticeDto.setCommodityInfos(commodityInfoList);
        logisticsNoticeDto.setImportType(request.getPackageType()); //目前写死为1订单拆包，2赠品和3补发的目前都不支持上传
        logisticsNoticeDto.setMailNo(trade.getOutSid());
        logisticsNoticeDto.setParentOrderId(getTid(trade.getTid()));
        logisticsNoticeDto.setTpCode(request.getExpressCompany().getConsignCode());
        req.setParamLogisticsNoticeDTO(logisticsNoticeDto);
        return buildTaobaoClientHelper(user).request(req);
    }

    public WlbImportThreeplOfflineConsignResponse logisticsConsignPostGateDeclare(User user, Long tid, String outSid, String companyCode) {
        WlbImportThreeplOfflineConsignRequest request = new WlbImportThreeplOfflineConsignRequest();
        request.setTradeId(tid);
        request.setWaybillNo(outSid);
        request.setResCode(companyCode);

        return buildTaobaoClientHelper(user).request(request);
    }

/*    public static void main(String[] args) {
        WlbOrderJzQueryRequest wlbOrderJzQueryRequest = new WlbOrderJzQueryRequest();
        wlbOrderJzQueryRequest.setTid(1819991811471531603L);
        User user = new User();
        user.setSessionId("70002100c2544973c2396b9af8aa88fed7a361b61d4b82b61da02fddbaf401d44bd9d941040599884");
        WlbOrderJzQueryResponse jzCompanyResponse = new TaobaoClientHelper(user, new TradeErrorMsgConverter()).request(wlbOrderJzQueryRequest);
        System.out.println(JSON.toJSONString(jzCompanyResponse));
        System.out.println(JSON.toJSONString(jzCompanyResponse.getResult().getInsTps()));
    }*/
    /**
     * 复制的订单tid为原tid-乱码，需要获取原来的tid
     */
    private Long getTid(String tid) {
        if(StringUtils.isNumeric(tid)){
            return Long.valueOf(tid);
        }
        if(tid.contains("-")){
            String templeTid = tid.split("-")[0];
            if(StringUtils.isNumeric(templeTid)){
                return Long.valueOf(templeTid);
            }
        }
        throw new IllegalArgumentException("平台交易号不为数字");
    }


    public Map<Long, RefundStatusGetResponse.QueryRefundStatusResponse> getRefundStatusData(User user, Long tid){
        Map<Long, RefundStatusGetResponse.QueryRefundStatusResponse> refundMap = new HashMap<>();
        RefundStatusGetRequest request = new RefundStatusGetRequest();
        RefundStatusGetRequest.RefundQueryByOrderIdRequest param = new RefundStatusGetRequest.RefundQueryByOrderIdRequest();
        param.setBizOrderId(tid);
        request.setQueryParam(param);
        RefundStatusGetResponse refundStatusData = null;
        try {
            refundStatusData = buildTaobaoClientHelper(user).request(request);
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(user).append(String.format("订单%s获取退款状态发生异常", tid)), e);
            return refundMap;
        }
        if (refundStatusData == null || !refundStatusData.isSuccess() || refundStatusData.getResultPackage() == null ) {
            logger.warn(LogHelper.buildLogHead(user).append(String.format("订单%s获取退款状态，返回失败,data:%s", tid, JSONObject.toJSON(refundStatusData))));
            return refundMap;
        }
        RefundStatusGetResponse.ResultSet resultPackage = refundStatusData.getResultPackage();
        if (CollectionUtils.isEmpty(resultPackage.getResultList())) {
            logger.warn(LogHelper.buildLogHead(user).append(String.format("订单%s获取退款状态，未找到退款谢谢,data:%s", tid, JSONObject.toJSON(refundStatusData))));
            return refundMap;
        }

        resultPackage.getResultList().stream().forEach(o -> refundMap.put(o.getOid(), o));
        return refundMap;
    }

}

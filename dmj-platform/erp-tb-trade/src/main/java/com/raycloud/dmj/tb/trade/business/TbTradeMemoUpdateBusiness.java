package com.raycloud.dmj.tb.trade.business;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Utf8;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.platform.trades.MemoUpdateRequest;
import com.raycloud.dmj.domain.platform.trades.PlatSellerFlagTag;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.platform.basis.PlatformAccessException;
import com.raycloud.dmj.services.platform.basis.PlatformResponse;
import com.raycloud.dmj.services.platform.basis.annotation.Platform;
import com.raycloud.dmj.services.platform.trades.PlatformTradeMemoUpdateBusiness;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.tb.common.TaobaoAccessException;
import com.raycloud.dmj.tb.common.TaobaoClientHelper;
import com.raycloud.dmj.tb.trade.TbTradeAccess;
import com.raycloud.dmj.tb.utils.TbAccessUtils;
import com.taobao.api.request.TradeSellerflagsGetRequest;
import com.taobao.api.response.TradeMemoUpdateResponse;
import com.taobao.api.response.TradeSellerflagsGetResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2017-08-01 10:10
 */
@Platform(values = {CommonConstants.PLAT_FORM_TYPE_TAO_BAO, CommonConstants.PLAT_FORM_TYPE_TIAN_MAO})
@Component
public class TbTradeMemoUpdateBusiness extends PlatformTradeMemoUpdateBusiness {

    private Logger logger = Logger.getLogger(this.getClass());
    @Resource
    TbTradeAccess tbTradeAccess;

    @Resource
    public IShopService shopService;

    @Resource
    public ICache cache;

    @Override
    public PlatformResponse update(User user, MemoUpdateRequest req) {
        try {
            if (StringUtils.isNotEmpty(req.getSellerMemo()) && Utf8.encodedLength(req.getSellerMemo())>1000) {
                return TbAccessUtils.tbResp2Common(null, "卖家备注超长");
            }
            Trade trade = req.getTrade();
            if(trade.getTid().contains("-")){//开放平台 会传手工单过来
                return TbAccessUtils.tbResp2Common(null, "手工不上传");
            }
            TradeMemoUpdateResponse resp = tbTradeAccess.updateSellerMemoFlag(user, req, Long.parseLong(trade.getTid()));
            return memoUpdateResp2Common(resp, req);
        } catch (TaobaoAccessException e) {
            return TbAccessUtils.tbException2CommonResp(e);
        }
    }

    private PlatformResponse<String> memoUpdateResp2Common(TradeMemoUpdateResponse resp, MemoUpdateRequest req) {
        PlatformResponse<String> response = TbAccessUtils.tbResp2Common(resp, null);
        if (resp != null) {
            response.setData(resp.getBody());
            if (response.isSuccess() && resp.getTrade() != null) {
                updateTrade(req, resp.getTrade().getModified());
            }
        }
        return response;
    }

    private void updateTrade(MemoUpdateRequest req, Date modified) {
        Trade trade = req.getTrade();
        trade.setModified(modified);
        trade.setSellerMemo(req.isReset() ? "" : req.getSellerMemo());
        trade.setSellerFlag(req.getSellerFlag());
    }

    @Override
    public boolean supportQueryFlag(User user) {
        return true;
    }

    @Override
    public List<PlatSellerFlagTag> querySerllerFlags(Staff staff, User user) {
        List<PlatSellerFlagTag> list = null;
        try {
            Object o = cache.get(getSellerFlagUserKey(user));
            if(o != null){
                list = (List<PlatSellerFlagTag>)o;
                return list;
            }
        } catch (Exception e){
            logger.warn(LogHelper.buildUserLog(user, String.format("获取旗帜标签缓存异常， userId:%s", user.getId())), e);
        }

        TradeSellerflagsGetRequest req = new TradeSellerflagsGetRequest();
        TaobaoClientHelper clientHelper = new TaobaoClientHelper(user);
        TradeSellerflagsGetResponse response = clientHelper.request(req);
        if(!response.isSuccess()){
            throw new PlatformAccessException(PlatformAccessException.ERROR_INVALIDATE_OPERATE, response.getSubMessage());
        }
        if(CollectionUtils.isEmpty(response.getFlags())){
            list = new ArrayList<>();
        } else {
            Shop shop = shopService.queryByUserId(staff, user.getId());
            //标签为空的过滤掉
            list = response.getFlags().stream().filter(t -> StringUtils.isNotBlank(t.getTagContent())).map(k -> convertInfo(k, shop)).collect(Collectors.toList());
        }
        try {
            Random random = new Random();
            cache.set(getSellerFlagUserKey(user), list, 10 * 60 + (random.nextInt(10) * 60));//缓存10+分钟 避免集中过期
        } catch (Exception e){
            logger.warn(LogHelper.buildUserLog(user, String.format("设置旗帜标签缓存异常， userId:%s，list:%s", user.getId(), JSONObject.toJSONString(list))), e);
        }
        return list;
    }

    private PlatSellerFlagTag convertInfo(TradeSellerflagsGetResponse.SellerFlag sellerFlag, Shop shop){
        PlatSellerFlagTag flagInfo = new PlatSellerFlagTag();
        flagInfo.setFlagId(sellerFlag.getFlagId());
        flagInfo.setTagContent(sellerFlag.getTagContent());
        flagInfo.setShopTitle(shop.getTitle());
        flagInfo.setUserId(shop.getUserId());
        return flagInfo;
    }

    private String getSellerFlagUserKey(User user){
        return "plat_seller_flag_user" + user.getId();
    }
}

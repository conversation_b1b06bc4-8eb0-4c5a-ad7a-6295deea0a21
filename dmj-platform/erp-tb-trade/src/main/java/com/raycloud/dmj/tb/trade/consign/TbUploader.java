package com.raycloud.dmj.tb.trade.consign;

import com.alibaba.fastjson.JSONObject;
import com.github.ltsopensource.core.json.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.diamond.TradeConsignConfigDiamondUtils;
import com.raycloud.dmj.services.platform.basis.PlatformAccessException;
import com.raycloud.dmj.services.platform.basis.PlatformResponse;
import com.raycloud.dmj.services.platform.basis.annotation.Platform;
import com.raycloud.dmj.services.platform.trades.consign.*;
import com.raycloud.dmj.services.trades.TradeException;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.tb.common.IErrorMsgConverter;
import com.raycloud.dmj.tb.common.TaobaoAccessException;
import com.raycloud.dmj.tb.common.TaobaoClientHelper;
import com.raycloud.dmj.tb.trade.TradeErrorMsgConverter;
import com.raycloud.dmj.tb.trade.business.TbTradeDownloader;
import com.raycloud.dmj.tb.utils.TbAccessUtils;
import com.sun.org.apache.xpath.internal.operations.Bool;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.domain.CombineLogisticsDetail;
import com.taobao.api.domain.Order;
import com.taobao.api.domain.Trade;
import com.taobao.api.request.*;
import com.taobao.api.response.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trades.TradeStatus.*;
import static com.raycloud.dmj.domain.utils.TradeTypeConstants.TMGJZY_POST_GATE_DECLARE;
import static com.raycloud.dmj.services.platform.basis.PlatformAccessException.ERROR_SYSTEM;
import static com.raycloud.dmj.tb.trade.TbTradeAccess.TRADE_FIELDS;

/**
 * TbNewConsignUploader
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Component
@Platform(values = {CommonConstants.PLAT_FORM_TYPE_TAO_BAO, CommonConstants.PLAT_FORM_TYPE_TIAN_MAO})
public class TbUploader implements PlatConsignUploader {

    private static final Logger LOGGER = Logger.getLogger(TbUploader.class);

    public static final String IS_LAST = "isLast";
    public static final String FEATURE = "feature";
    public static final String TEMPLATE_NAME = "templateName";

    public static final String COMPANY_CODE = "companyCode";

    public static final String OUT_SID = "outSid";
    private static final IErrorMsgConverter ERROR_MSG_CONVERTER = new TradeErrorMsgConverter();

    public static final String TB_MODIFY = "modify";

    public static final String TB_RESEND = "resend";

    private final String yunDa = "YUNDA";
    private final String yunDaInternational = "DISTRIBUTOR_13180342";

    private final String REISSUE  = "REISSUE";

    private final List<String> fakeMsgs = new ArrayList<String>(Arrays.asList("交易#失败","CD01","发货失败#通知交易异常","通知交易异常","并发修改失败","请勿用重复运单号发货"));

    private final String ALL = "ALL";
    private final Logger logger = Logger.getLogger(this.getClass());

    @Override
    public PlatResponse upload(PlatRequest request) {
//        List<String> tbSuccessErrors = TradeConsignConfigDiamondUtils.getTbSuccessErrors();
//        Logs.ifDebug(LogHelper.buildLogHead(request.getUser()).append(String.format("实为成功错误参数测试", tbSuccessErrors)));
        String tradeType = request.getExt("tradeType");
        String transportType = request.getExt("transport_type");
        if("SELLER".equals(transportType)){
            return doSellerSend(request);
        }
        switch (request.getType()) {
            case UPLOAD:
//                if (REISSUE.equals(tradeType)) {
//                    return doReissueUpload(request);
//                }
                if (ALL.equals(tradeType)) {
                    return doUploadAll(request);
                }
                // 天猫国际直邮订单 - taobao.wlb.import.threepl.offline.consign
                if (TMGJZY_POST_GATE_DECLARE.equals(tradeType)) {
                    return doTMDirectMailUpload(request);
                }
                // COD货到付款在线订单 - taobao.logistics.online.send
                if ("cod".equals(tradeType)) {
                    return doOnlineUoload(request);
                }
                // 普通订单 - alibaba.ascp.logistics.offline.send
                return this.doUpload(request);
            case RE_UPLOAD:
                if(TB_MODIFY.equals(tradeType)){
                    return this.doModifyUpload(request);    //
                }
                return this.doReUpload(request);    // 修改物流公司和运单号参数构建: alibaba.ascp.logistics.consign.resend
            case DUMMY:
                return doDummyUpload(request);
            case MUL_PACK_UPLOAD:   // 多包裹上传运单号
                if (ALL.equals(tradeType)) {
                    return doUploadAll(request);
                }
                return doMultiPackUpload(request);
            default:
                return PlatResponse.buildFail(String.format("暂不支持当前发货类型: %s", request.getType()));
        }
    }

    private PlatResponse doTMDirectMailUpload(PlatRequest platRequest) {
        User user = platRequest.getUser();
        UploadTrade trade = platRequest.getTrade();
        Logs.ifDebug(LogHelper.buildLogHead(user).append(String.format("调用天猫国际直邮发货接口,tid=%s,outSid=%s", trade.getTid(), trade.getOutSid())));
        WlbImportThreeplOfflineConsignResponse response ;
        WlbImportThreeplOfflineConsignResponse firstResponse = null;
        String companyCode = null;
        try {
            companyCode = platRequest.getTrade().getExpressCompany().getConsignCode();
            response = this.logisticsConsignPostGateDeclare(user, Long.valueOf(trade.getTid()), trade.getOutSid(), yunDa.equals(companyCode) ? yunDaInternational : companyCode,platRequest);
            firstResponse = response;
            if(yunDa.equals(companyCode) && response != null && response.getResult() != null && !response.getResult().getSuccess()) {
                logger.warn(LogHelper.buildLogHead(user).append(String.format("天猫国际直邮发货,韵达国际失败,韵达快递重试,companyCode=%s", companyCode)));
                response = this.logisticsConsignPostGateDeclare(user, Long.valueOf(trade.getTid()), trade.getOutSid(), companyCode,platRequest);
            }
            if(Objects.nonNull(response)&&Objects.nonNull(response.getResult())&&!response.getResult().getSuccess()){
                PlatResponse response1 = this.doUpload(platRequest);
                if(response1.isSuccess()){
                    return response1;
                }
            }
        }catch (TaobaoAccessException e){
            PlatResponse response1 = this.doUpload(platRequest);
            if(response1.isSuccess()){
                return response1;
            }
            logger.error(LogHelper.buildLogHead(user).append(String.format("调用天猫国际直邮发货接口异常,tid=%s,outSid=%s,companyCode=%s,", trade.getTid(), trade.getOutSid(), companyCode)));
            if(firstResponse!=null){
                return postGateDeclare2Common(firstResponse);
            }
            throw e;
        }
        Logs.ifDebug(LogHelper.buildLogHead(user).append(String.format("调用天猫国际直邮接口发货成功,params=%s,errorCode=%s,msg=%s,subCode=%s,subMsg=%s,result=%s",
                com.alibaba.fastjson.JSON.toJSONString(response.getParams()),
                response.getErrorCode(),
                response.getMsg(),
                response.getSubCode(),
                response.getSubMsg(),
                com.alibaba.fastjson.JSON.toJSONString(response.getResult()))));
        PlatResponse response1 = postGateDeclare2Common(firstResponse);
        return response1;
    }

    private WlbImportThreeplOfflineConsignResponse logisticsConsignPostGateDeclare(User user, Long tid, String outSid, String companyCode,PlatRequest platRequest) {
        WlbImportThreeplOfflineConsignRequest request = new WlbImportThreeplOfflineConsignRequest();
        Long resId = platRequest.getExt("resId");
        Long fromId = platRequest.getExt("fromId");
        String resCode = platRequest.getExt("resCode");
        if(Objects.nonNull(resId)){
            request.setResId(resId);
        }
        if(Objects.nonNull(fromId)){
            request.setFromId(fromId);
        }
        request.setTradeId(tid);
        request.setWaybillNo(outSid);
        if(Objects.nonNull(resCode)){
            request.setResCode(resCode);
        }else{
            request.setResCode(companyCode);
        }
        return buildClient(user).request(request);
    }

    private PlatResponse postGateDeclare2Common(WlbImportThreeplOfflineConsignResponse resp) {
        PlatResponse response = new PlatResponse();
        if (resp == null) {
            response.setSuccess(false);
            response.setMsg("淘宝3PL直邮线下发货接口没有返回响应");
        } else {
            if(resp.getResult() != null) {
                response.setSuccess(resp.getResult().getSuccess());
                fillResponse(response,
                        resp.getSubCode(),
                        resp.getResult().getErrorMsg(),
                        resp.getResult().getErrorCode(),
                        resp.getMsg());
            }else {
                response.setSuccess(resp.isSuccess());
                fillResponse(response,
                        resp.getSubCode(),
                        resp.getSubMsg(),
                        resp.getErrorCode(),
                        resp.getMsg());
            }
        }
        return response;
    }

    public static void fillResponse(PlatResponse response, String errCode, String errMsg, String code, String msg) {
        response.setCode(errCode);
        response.setMsg(errMsg);
        response.setCode(code);
    }


    private PlatResponse doDummyUpload(PlatRequest platRequest) {
        User user = platRequest.getUser();
        UploadTrade trade = platRequest.getTrade();
        return dummyUpload(user,trade);
    }

    private PlatResponse dummyUpload(User user, UploadTrade trade) {
        LogisticsDummySendResponse resp = this.sendDummyLogistics(user, Long.parseLong(trade.getTid()));
        return dummyResp2Common(resp);
    }

    private PlatResponse dummyResp2Common(LogisticsDummySendResponse resp) {
        PlatResponse response = new PlatResponse();
        if (resp != null && resp.getShipping() != null && resp.getShipping().getIsSuccess() != null) {
            response.setSuccess(resp.getShipping().getIsSuccess());
        }
        response.setCode(resp.getCode());
        response.setMsg(resp.getMsg());
        response.setData(resp.getBody());
        return response;
    }

    private LogisticsDummySendResponse sendDummyLogistics(User user, Long tid) {
        LogisticsDummySendRequest req = new LogisticsDummySendRequest();
        req.setTid(tid);
        return buildClient(user).request(req);

    }

    private PlatResponse doOnlineUoload(PlatRequest platRequest) {
        User user = platRequest.getUser();
        UploadTrade trade = platRequest.getTrade();
        return onlineUpload(user,trade,platRequest);
    }

    private PlatResponse onlineUpload(User user, UploadTrade trade, PlatRequest req) {
        String subTids = getSubTids(user, trade, false,req);
        LogisticsOnlineSendResponse resp = null;
        try {
            resp = this.sendOnlineLogistics(user, Long.parseLong(trade.getTid()), subTids, StringUtils.isNotBlank(trade.getOutSid()) ? trade.getOutSid().trim() : "", trade.getExpressCompany().getConsignCode());
        } catch (TaobaoAccessException e){
            resp = reTrySendOnlineLogistics(user, trade, req, subTids, e);
        }
        // 平台订单状态已发货，但是没有面单情况处理
        if (resp != null && "B150".equals(resp.getSubCode()) && "发货系统出现异常,请稍后重试".equals(resp.getSubMsg())) {
            Logs.debug("发货系统异常，二次重试");
            try {
                resp = this.sendOnlineLogistics(user, Long.parseLong(trade.getTid()), subTids, trade.getOutSid(),trade.getExpressCompany().getConsignCode());
            } catch (TaobaoAccessException e){
                resp = reTrySendOnlineLogistics(user, trade, req, subTids, e);
            }
        }
        return onlineResp2Common(resp);
    }

    public LogisticsOnlineSendResponse sendOnlineLogistics(User user, Long tid, String subTids, String outSid, String companyCode) {
        LogisticsOnlineSendRequest req = new LogisticsOnlineSendRequest();
        req.setTid(tid);
        if (!StringUtils.isEmpty(subTids)) {
            req.setSubTid(subTids);
            req.setIsSplit(1L);
        }
        if (!StringUtils.isEmpty(outSid)) {
            req.setOutSid(outSid);
        }
        req.setCompanyCode(companyCode);
        return buildClient(user).request(req);
    }

    private PlatResponse onlineResp2Common(LogisticsOnlineSendResponse resp) {
        PlatResponse response = new PlatResponse();
        if (resp != null && resp.getShipping() != null && resp.getShipping().getIsSuccess() != null) {
            response.setSuccess(resp.getShipping().getIsSuccess());
        }
        response.setMsg(resp.getMsg());
        response.setCode(resp.getCode());
        response.setData(resp.getBody());
        return response;
    }


    private String getSubTids(User user, UploadTrade trade, boolean resendUpload, PlatRequest req) {
        StringBuilder uploadOids = new StringBuilder();
        List<UploadOrder> orders = trade.getOrders();
        if(CollectionUtils.isNotEmpty(orders)){
            for(UploadOrder order : orders){
                uploadOids.append(order.getOid()).append(",");
            }
            return uploadOids.substring(0, uploadOids.length() - 1);
        }
        return null;
    }

    /**
     * 平台返回异常，重试发货
     * @param user
     * @param trade
     * @param req
     * @param subTids
     * @param e   异常
     * @return
     */
    private LogisticsOnlineSendResponse reTrySendOnlineLogistics(User user, UploadTrade trade, PlatRequest req, String subTids, TaobaoAccessException e){
        Assert.isTrue(e != null, "平台未返回异常，不能通过该方式再次发起上传发货!");
        TaobaoResponse res = e.getTaobaoResp();
        if(res == null){
            throw e;
        }
        String subCode = res.getSubCode() != null ? res.getSubCode() : "";
        String subMsg = res.getSubMsg() != null ? res.getSubMsg() : "";
        //如果不支持中文，则传OTHER
        if ("OFFLINE_COMPANY_CODE".equals(subCode) || "BN03".equals(subCode) || subMsg.contains("companycode信息包含中文")) {
            logger.debug(LogHelper.buildLogHead(user)
                    .append(String.format("淘宝自定义快递模板线上发货上传失败，改传OTHER,sid:%s,tid:%s,ConsignCode:%s",
                            trade.getSid(), trade.getTid(), trade.getExpressCompany().getConsignCode())));
            return this.sendOnlineLogistics(user, Long.parseLong(trade.getTid()), subTids, trade.getOutSid(), trade.getExpressCompany().getConsignCode());
        } else {
            throw e;
        }
    }

    private PlatResponse doModifyUpload(PlatRequest platRequest) {
        UploadTrade trade = platRequest.getTrade();
        List<UploadOrder> orders = trade.getOrders();
        List<AlibabaAscpLogisticsConsignModifyRequest.TopConsignGoodsRequest> consignPkgs = Lists.newArrayList();
            for (UploadOrder order : orders) {
            AlibabaAscpLogisticsConsignModifyRequest.TopConsignGoodsRequest topConsignPkgRequest = new AlibabaAscpLogisticsConsignModifyRequest.TopConsignGoodsRequest();
            topConsignPkgRequest.setSubTid(String.valueOf(order.getOid()));
                consignPkgs.add(topConsignPkgRequest);
        }
        AlibabaAscpLogisticsConsignModifyRequest request = new AlibabaAscpLogisticsConsignModifyRequest();
        request.setFeature(platRequest.getExt(FEATURE));
        request.setTid(platRequest.getTrade().getTid());
        request.setGoods(consignPkgs);
        request.setOldCompanyCode(platRequest.getExt(COMPANY_CODE));
        request.setNewCompanyCode(platRequest.getTrade().getExpressCompany().getConsignCode());
        request.setOldOutSid(platRequest.getExt(OUT_SID));
        request.setNewOutSid(platRequest.getTrade().getOutSid());
        return doModifyUpload(platRequest.getUser(),request,trade);
    }

    private PlatResponse doModifyUpload(User user, AlibabaAscpLogisticsConsignModifyRequest request, UploadTrade trade){
        AlibabaAscpLogisticsConsignModifyResponse response;
        try {
            TaobaoClientHelper client = buildClient(user);
            response = client.request(request);
            boolean isSuccess = Optional.ofNullable(response).map(AlibabaAscpLogisticsConsignModifyResponse::getResult).map(AlibabaAscpLogisticsConsignModifyResponse.ResultDTO::getSuccess).orElse(false);
            if (isSuccess) {
                return PlatResponse.buildSuccess();
            }
            LOGGER.error(LogHelper.buildUserLog(user, String.format("修改运单号出错 sid:%s tid:%s req:", trade.getSid(), trade.getTid())).append(JSON.toJSONObject(request)));
            LOGGER.error(LogHelper.buildUserLog(user, String.format("修改运单号出错 sid:%s tid:%s rsp:", trade.getSid(), trade.getTid())).append(JSON.toJSONObject(response)));
        } catch (TaobaoAccessException e) {
            LOGGER.error(LogHelper.buildUserLog(user, String.format("修改运单号出错 sid:%s tid:%s req:", trade.getSid(), trade.getTid())).append(JSON.toJSONObject(request)));
            LOGGER.error(LogHelper.buildUserLog(user, String.format("修改运单号出错 sid:%s tid:%s", trade.getSid(), trade.getTid())), e);
            throw e;
        }
        String subCode = Optional.ofNullable(response).map(TaobaoResponse::getSubCode).orElse("-1");
        String subMessage = Optional.ofNullable(response).map(TaobaoResponse::getSubMessage).orElse("淘宝接口没有返回响应");
        return PlatResponse.buildFail(subCode, subMessage);
    }

    /**
     * 修改物流公司和运单号参数构建: alibaba.ascp.logistics.consign.resend
     */
    private PlatResponse doReUpload(PlatRequest platRequest) {
        User user = platRequest.getUser();
        UploadTrade trade = platRequest.getTrade();

        List<AlibabaAscpLogisticsConsignResendRequest.TopConsignPkgRequest> consignPkg = Lists.newArrayList();
        Set<String> outSids = Arrays.stream(trade.getOutSid().split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        for (String outSid : outSids) {
            AlibabaAscpLogisticsConsignResendRequest.TopConsignPkgRequest topConsignPkgRequest =new AlibabaAscpLogisticsConsignResendRequest.TopConsignPkgRequest();
            topConsignPkgRequest.setCompanyCode(trade.getExpressCompany().getConsignCode());
            topConsignPkgRequest.setOutSid(outSid);
            consignPkg.add(topConsignPkgRequest);
        }

        String subTids = null;
        if (CollectionUtils.isNotEmpty(trade.getOrders())) {
            subTids = String.join(",", trade.getOrders().stream().map(UploadOrder::getOid).map(String::valueOf).toArray(String[]::new));
        }

        AlibabaAscpLogisticsConsignResendRequest request = new AlibabaAscpLogisticsConsignResendRequest();
        request.setTid(trade.getTid());
        request.setConsignPkgs(consignPkg);
        request.setSubTids(subTids);
        request.setFeature(platRequest.getExt(FEATURE));

        AlibabaAscpLogisticsConsignResendResponse response;
        try {
            TaobaoClientHelper client = buildClient(user);
            response = client.request(request);
            boolean isSuccess = Optional.ofNullable(response).map(AlibabaAscpLogisticsConsignResendResponse::getResult).map(AlibabaAscpLogisticsConsignResendResponse.ResultDto::getSuccess).orElse(false);
            if (isSuccess) {
                return PlatResponse.buildSuccess();
            }
            LOGGER.error(LogHelper.buildUserLog(user, String.format("修改运单号出错 sid:%s tid:%s req:", trade.getSid(), trade.getTid())).append(JSON.toJSONObject(request)));
            LOGGER.error(LogHelper.buildUserLog(user, String.format("修改运单号出错 sid:%s tid:%s rsp:", trade.getSid(), trade.getTid())).append(JSON.toJSONObject(response)));
        } catch (TaobaoAccessException e) {
            LOGGER.error(LogHelper.buildUserLog(user, String.format("修改运单号出错 sid:%s tid:%s req:", trade.getSid(), trade.getTid())).append(JSON.toJSONObject(request)));
            LOGGER.error(LogHelper.buildUserLog(user, String.format("修改运单号出错 sid:%s tid:%s", trade.getSid(), trade.getTid())), e);
            throw e;
        }
        String subCode = Optional.ofNullable(response).map(TaobaoResponse::getSubCode).orElse("-1");
        String subMessage = Optional.ofNullable(response).map(TaobaoResponse::getSubMessage).orElse("淘宝接口没有返回响应");
        return PlatResponse.buildFail(subCode, subMessage);
    }


    /**
     * 自己联系物流发货: alibaba.ascp.logistics.offline.send
     */
    private PlatResponse doUpload(PlatRequest platRequest) {
        User user = platRequest.getUser();
        UploadTrade trade = platRequest.getTrade();
//        //先上传唯一码。
//        String feature = platRequest.getExt(FEATURE);
//        doUploadSnCode(feature, trade, user);
        // 构建发货信息
        List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest> consignPkgs = Lists.newArrayList();
        List<AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest> consignStatus = Lists.newArrayList();
        buildConsignInfo(user, trade, consignPkgs, consignStatus);
        PlatResponse platResponse = doUpload(buildClient(user), platRequest, consignPkgs, consignStatus, true,1L);
        if(Objects.nonNull(platRequest.getExt("lastTrade"))&&(Boolean)platRequest.getExt("lastTrade")) {
            if(platResponse.isSuccess()){
                doUploadAll(platRequest);
            }
        }

        // 请求平台
        return platResponse;
    }

    /**
     * sn码上传后，立即上传运单号，会报错，sn上传后，需要5分钟，或者更长时间审核，才能上传运单号成功。
     * 平台订单有sn码，且与待上传的sn相同时，则不上传。如果上传了，可能会重新开始审核。
     */
    public void doUploadSnCode(String featureUniqueCode, UploadTrade trade, User user) {
        if (StringUtils.isEmpty(featureUniqueCode)) {
            return;
        }
        logger.debug(LogHelper.buildLogHead(user).append(String.format("sid:%s,获取到sn码信息:%s", trade.getSid(), featureUniqueCode)));
        String uniqueCode = parseIdentCode(featureUniqueCode);
        if (StringUtils.isEmpty(uniqueCode)) {
            return;
        }
        Trade originTrade = null;
        TradeFullinfoGetResponse tradeResponse = getTrade(buildClient(user),trade.getTid());
        if (Objects.isNull(tradeResponse) || Objects.isNull(originTrade = tradeResponse.getTrade())) {
            logger.info(LogHelper.buildLogHead(user).append(String.format("tid=%s获取原始订单详情失败,直接上传SN码", trade.getTid())));
        }
        List<String> snList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(originTrade.getOrders())) {
            for (Order order : originTrade.getOrders()) {
                if(StringUtils.isEmpty(order.getBybtSnCodeTag())) {
                    continue;
                }
                snList.add(order.getBybtSnCodeTag());
            }
        }
        logger.debug(LogHelper.buildLogHead(user).append(String.format("tid:%s,在原始平台订单数据中获取到sn码信息:%s", trade.getTid(), JSON.toJSONString(snList))));
        String[] orderUniqueCode = uniqueCode.split("\\|");
        for (String singleOrder : orderUniqueCode) {
            String[] demo = singleOrder.split(":");
            if (demo.length == 2) {
                AlibabaAscpLogisticsIdentcodeUploadResponse response = null;
                try {
                    //判断平台订单中是否已存在当前sn码,如果存在,则不上传。
                    List<String> tradeExistSn = snList.stream().filter(e -> e.contains(demo[1])).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(tradeExistSn)) {
                        continue;
                    }
                    response = sendOfflineLogisticsIdentcode(user, Long.parseLong(demo[0]), demo[1]);
                    String responseMsg = response == null ? "无响应内容" : (response.getResult().getCode() == 0 ? "" : response.getResult().getMsg());
                    logger.info(LogHelper.buildLogHead(user).append(String.format("sid=%s调用sn码上传接口返回结果:%s,msg=%s", trade.getSid(), response != null && response.getResult().getCode() == 0, responseMsg)));
                } catch (TaobaoAccessException e) {
                    logger.error(LogHelper.buildLogHead(user).append(String.format("调用sn码上传接口,sid=%s,orderId=%s,sn=%s,", trade.getSid(), demo[0], demo[1])));
                }
            } else {
                logger.warn(LogHelper.buildLogHead(user).append(String.format("sid:%s,sn码上传平台流程，获取到的唯一码格式不正确%s", trade.getSid(), singleOrder)));
            }
        }
    }

    public static String parseIdentCode(String feature) {
        if (!feature.contains("identCode=")) {
            return null; // 如果没有找到 "identCode="，返回 null
        }
        int startIndex = feature.indexOf("identCode=") + "identCode=".length();
        int endIndex = feature.indexOf(";", startIndex);
        // 如果找到 ";"，返回从起始位置到 ";" 之间的内容
        // 如果没有找到 ";"，返回从起始位置到字符串末尾的内容
        return (endIndex != -1) ? feature.substring(startIndex, endIndex) : feature.substring(startIndex);
    }

    public AlibabaAscpLogisticsIdentcodeUploadResponse sendOfflineLogisticsIdentcode(User user, Long orderId, String codeList) {
        AlibabaAscpLogisticsIdentcodeUploadRequest req = new AlibabaAscpLogisticsIdentcodeUploadRequest();
        req.setOrderId(orderId);
        req.setCodeList(codeList);
        return buildClient(user).request(req);
    }


    private void buildConsignInfo(User user, UploadTrade trade,
                                  List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest> pkgRequests,
                                  List<AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest> consignStatus) {
        // 商品信息
        List<UploadOrder> orders = Optional.ofNullable(trade.getOrders()).orElse(Lists.newArrayList());
        List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignGoodsRequest> goodsRequests = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(orders)) {
            orders.forEach(order -> {
                AlibabaAscpLogisticsOfflineSendRequest.TopConsignGoodsRequest goods = new AlibabaAscpLogisticsOfflineSendRequest.TopConsignGoodsRequest();
                goods.setSubTid(String.valueOf(order.getOid()));
                goods.setNum(order.getNum().longValue());
                goodsRequests.add(goods);

                AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest status = new AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest();
                status.setSubTid(String.valueOf(order.getOid()));
                status.setIsPartConsign(!Optional.<Boolean>ofNullable(order.getExt(IS_LAST)).orElse(true)); // false-全部发货、true-部分发货
                consignStatus.add(status);
            });
        }

        // 包裹信息 (存在一单多包的场景)
        Map<Long, UploadOrder> oid2order = orders.stream().collect(Collectors.toMap(UploadOrder::getOid, Function.identity(), (o1, o2) -> o1));
        Set<String> outSids = Arrays.stream(trade.getOutSid().split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        for (String outSid : outSids) {
            // 剔除上传超过11个的运单号
            List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignGoodsRequest> subGoodsRequests = goodsRequests.stream().filter(goods -> {
                UploadOrder order = oid2order.get(Long.valueOf(goods.getSubTid()));
                List<String> mulPackList = order.getExt("mulPackList");
                if (CollectionUtils.isEmpty(mulPackList)) {
                    return true;
                }
                return mulPackList.contains(outSid);
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orders) && CollectionUtils.isEmpty(subGoodsRequests)) {
                LOGGER.warn(LogHelper.buildLogHead(user).append(String.format("sid:%s, tid:%s, outSid:%s 剔除超限运单号", trade.getSid(), trade.getTid(), trade.getOutSid())));
                continue;
            }

            AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest pkg = new AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest();
            pkg.setOutSid(outSid);
            pkg.setCompanyCode(trade.getExpressCompany().getConsignCode());
            pkg.setGoods(goodsRequests);
            pkgRequests.add(pkg);
        }
    }

    /**
     * 构建推推进到全部上传参数
     * @param user
     * @param trade
     * @param pkgRequests
     * @param consignStatus
     */
    private void buildUploadAllConsignInfo(User user, UploadTrade trade,
                                  List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest> pkgRequests,
                                  List<AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest> consignStatus) {

        List<UploadOrder> uploadOrders = trade.getOrders();
        for(UploadOrder order : uploadOrders){
            if(Objects.nonNull(order.getExt(IS_LAST))&&(Boolean)order.getExt(IS_LAST)){
                AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest req = new AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest();
                req.setIsPartConsign(false);
                req.setSubTid(String.valueOf(order.getOid()));
                consignStatus.add(req);
            }
        }
    }

    /**
     * 构建补发参数
     * @param trade
     * @param pkgRequests
     * @param consignStatus
     */
    private void buildUploadReissueConsignInfo(String outSid, UploadTrade trade,
                                               List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest> pkgRequests,
                                               List<AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest> consignStatus) {
        List<UploadOrder> uploadOrders = trade.getOrders();
        List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignGoodsRequest> goods = new ArrayList<>();

        for (UploadOrder order : uploadOrders) {
            AlibabaAscpLogisticsOfflineSendRequest.TopConsignGoodsRequest goodsRequest = new AlibabaAscpLogisticsOfflineSendRequest.TopConsignGoodsRequest();
            goodsRequest.setSubTid(String.valueOf(order.getOid()));
            goods.add(goodsRequest);
        }

        AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest topConsignPkgRequest = new AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest();
        //对已发货的进行补发
        topConsignPkgRequest.setCompanyCode(trade.getExpressCompany().getConsignCode());
        topConsignPkgRequest.setOutSid(outSid);
        topConsignPkgRequest.setGoods(goods);
        pkgRequests.add(topConsignPkgRequest);
    }


    /**
     * 多包裹发货上传运单号
     */
    private PlatResponse doMultiPackUpload(PlatRequest platRequest) {
        String errorCode = "";
        String errorMessage = "";
        List<String> failExpressNo = Lists.newArrayList();
        UploadTrade trade = platRequest.getTrade();
        String[] outSids = trade.getOutSid().split(",");
        for (String expressNo : outSids) {
            // 构建发货信息
            List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest> consignPkgs = Lists.newArrayList();
            List<AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest> consignStatus = Lists.newArrayList();
            buildUploadReissueConsignInfo(expressNo, trade, consignPkgs, consignStatus);
            PlatResponse rsp = doUpload(buildClient(platRequest.getUser()), platRequest, consignPkgs, consignStatus, false,3L);
            if (Objects.nonNull(rsp) && rsp.isSuccess()) {
                continue;
            }
            // 记录上传失败的运单号
            failExpressNo.add(expressNo);
            if (StringUtils.isBlank(errorMessage) || "未知错误".equals(errorMessage)) {
                errorCode = Optional.ofNullable(rsp).map(PlatResponse::getCode).orElse("-1");
                errorMessage = Optional.ofNullable(rsp).map(PlatResponse::getMsg).orElse("未知错误");
            }
        }

        // 多包裹上传，有一个上传成功了就认为上传成功了
        if (outSids.length > failExpressNo.size()) {
            return PlatResponse.buildSuccess(failExpressNo);
        }
        return PlatResponse.buildFail(errorCode, errorMessage);
    }

        private PlatResponse doUpload(TaobaoClientHelper client,
                                  PlatRequest platRequest,
                                  List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest> consignPkgs,
                                  List<AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest> consignStatus,
                                  boolean retry,Long consignType) {
        User user = platRequest.getUser();
        UploadTrade trade = platRequest.getTrade();

        // 构建请求参数
        AlibabaAscpLogisticsOfflineSendRequest request = new AlibabaAscpLogisticsOfflineSendRequest();
        request.setConsignType(consignType);
        request.setTid(trade.getTid());
        request.setFeature(platRequest.getExt(FEATURE));
        request.setConsignPkgs(consignPkgs);
        request.setConsignStatus(consignStatus);

        // 请求平台
        AlibabaAscpLogisticsOfflineSendResponse response;
        try {
            response = client.request(request);
            boolean isSuccess = Optional.ofNullable(response).map(AlibabaAscpLogisticsOfflineSendResponse::getResult).map(AlibabaAscpLogisticsOfflineSendResponse.ResultDto::getSuccess).orElse(false);
            if (isSuccess) {
                return PlatResponse.buildSuccess();
            }
            LOGGER.error(LogHelper.buildUserLog(user, String.format("自己联系物流发货出错 sid:%s tid:%s req:", trade.getSid(), trade.getTid())).append(JSON.toJSONObject(request)));
            LOGGER.error(LogHelper.buildUserLog(user, String.format("自己联系物流发货出错 sid:%s tid:%s rsp:", trade.getSid(), trade.getTid())).append(JSON.toJSONObject(response)));
        } catch (TaobaoAccessException e) {
            String subCode = Optional.ofNullable(e.getTaobaoResp()).map(TaobaoResponse::getSubCode).orElse("");
            String subMsg = Optional.ofNullable(e.getTaobaoResp()).map(TaobaoResponse::getSubMsg).orElse(e.getMessage());
            if (("OFFLINE_COMPANY_CODE".equals(subCode) || "BN03".equals(subCode) || subMsg.contains("companycode信息包含中文")) && retry) {
                // com.raycloud.dmj.tb.trade.business.TbConsignUploader.getCompanyCode
                String templateName = trade.getExt(TEMPLATE_NAME);
                LOGGER.debug(LogHelper.buildUserLog(user, String.format("sid:%s,tid:%s,consignCode:%s, templateName: %s 自己联系物流发货,改传模版名称", trade.getSid(), trade.getTid(), trade.getExpressCompany().getConsignCode(), templateName)));
                consignPkgs.forEach(pkg -> pkg.setCompanyCode(templateName));
                return doUpload(client, platRequest, consignPkgs, consignStatus, false,1L);
            }
            if ("子订单部分发货次数超过上限".equals(e.getMessage()) && retry) {
                LOGGER.debug(LogHelper.buildUserLog(user, String.format("sid:%s, tid:%s 子订单部分发货次数超过上限进行重试", trade.getSid(), trade.getTid())));
                removeOverLimitOrder(client, platRequest, consignPkgs, consignStatus);
                return doUpload(client, platRequest, consignPkgs, consignStatus, false,1L);
            }
            boolean isSplit = Optional.ofNullable(platRequest.<Boolean>getExt("isSplit")).orElse(false);
            if(!isSplit){
                for(String fakeMsg : fakeMsgs){
                    if(e.getMessage().contains(fakeMsg)){
                        PlatformResponse<Trade> trade1 = this.get(user,trade.getTid(),null);
                        Trade originTrade = trade1.getData();
                        if(originTrade!=null&&originTrade.getSid()!=null&&originTrade.getSid().equals(trade.getOutSid())){
                            return PlatResponse.buildSuccess();
                        }
                    }
                }
            }

            List<String> tbSuccessErrors = TradeConsignConfigDiamondUtils.getTbSuccessErrors();
            for(String retryUploadAllMsg : tbSuccessErrors){
                if(StringUtils.isNotEmpty(e.getMessage())&&e.getMessage().contains(retryUploadAllMsg)&& retry){
                    return doUploadAll(platRequest);
                }
            }

            if (StringUtils.isNotEmpty(e.getMessage()) && (e.getMessage().contains("请上传识别码后，再操作发货") || e.getMessage().contains("识别码无效，不允许操作发货"))) {
                //先上传唯一码。
                String feature = platRequest.getExt(FEATURE);
                doUploadSnCode(feature, trade, user);
            }

            LOGGER.error(LogHelper.buildUserLog(user, String.format("自己联系物流发货出错 sid:%s tid:%s req:", trade.getSid(), trade.getTid())).append(JSON.toJSONObject(request)));
            LOGGER.error(LogHelper.buildUserLog(user, String.format("自己联系物流发货出错 sid:%s tid:%s", trade.getSid(), trade.getTid())), e);
            throw e;
        }
        String subCode = Optional.ofNullable(response).map(TaobaoResponse::getSubCode).orElse("-1");
        String subMessage = Optional.ofNullable(response).map(TaobaoResponse::getSubMessage).orElse("淘宝接口没有返回响应");
        if (subMessage.contains("识别码无效，不允许操作发货")) {
            //先上传唯一码。
            String feature = platRequest.getExt(FEATURE);
            doUploadSnCode(feature, trade, user);
        }
        return PlatResponse.buildFail(subCode, subMessage);
    }

    private void removeOverLimitOrder(TaobaoClientHelper client, PlatRequest platRequest,
                                      List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest> consignPkgs,
                                      List<AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest> consignStatus) {
        Map<Long/*oid*/, Integer/*packageSize*/> oid2count = getPartOrderPackageSize(client, platRequest.getTrade().getTid());
        LOGGER.info(LogHelper.buildLogHead(platRequest.getUser()).append(String.format("sid:%s, tid:%s, oid2count:%s", platRequest.getTrade().getSid(), platRequest.getTrade().getTid(), oid2count)));

        Set<Long> isPartConsignOids = Sets.newHashSet();    // 部分发货的商品
        Set<Long> isNonPartConsignOids = Sets.newHashSet(); // 全部发货的商品

        // 重构consignPkgs
        Iterator<AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest> packIterator = consignPkgs.iterator();
        while (packIterator.hasNext()) {
            AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest pack = packIterator.next();
            Iterator<AlibabaAscpLogisticsOfflineSendRequest.TopConsignGoodsRequest> goodsIterator = pack.getGoods().iterator();
            while (goodsIterator.hasNext()) {
                AlibabaAscpLogisticsOfflineSendRequest.TopConsignGoodsRequest goods = goodsIterator.next();
                Long oid = Long.valueOf(goods.getSubTid());
                Integer packageSize = oid2count.get(oid);
                if (Objects.isNull(packageSize)) {
                    // 商品是完结状态
                    goodsIterator.remove();
                    continue;
                }

                // 包裹数小于10，还可以继续追加包裹
                if (packageSize < 10) {
                    isPartConsignOids.add(oid);
                    oid2count.put(oid, packageSize + 1);
                    continue;
                }

                // 包裹数为10，当前商品已经无法追加包裹了，且商品状态需要变为"已发货"
                if (packageSize == 10) {
                    isNonPartConsignOids.add(oid);
                    oid2count.put(oid, packageSize + 1);
                    continue;
                }

                // 移除超限的包裹信息
                goodsIterator.remove();
            }

            // 包裹下的商品为空，移除当前包裹
            if (CollectionUtils.isEmpty(pack.getGoods())) {
                LOGGER.info(LogHelper.buildLogHead(platRequest.getUser()).append(String.format("sid:%s, tid:%s, outSid:%s 运单号无法继续上传", platRequest.getTrade().getSid(), platRequest.getTrade().getTid(), pack.getOutSid())));
                packIterator.remove();
            }
        }
        if (CollectionUtils.isEmpty(consignPkgs)) {
            throw new PlatformAccessException(ERROR_SYSTEM, "订单所有商品已发货上传平台，发货数等于购买数，当前订单无法再上传平台");
        }

        // 重构consignStatus
        consignStatus.clear();
        isNonPartConsignOids.forEach(oid -> {
            AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest request = new AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest();
            request.setSubTid(oid + "");
            request.setIsPartConsign(false);
            consignStatus.add(request);
        });
        isPartConsignOids.forEach(oid -> {
            if (isNonPartConsignOids.contains(oid)) {
                return;
            }
            AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest request = new AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest();
            request.setSubTid(oid + "");
            request.setIsPartConsign(true);
            consignStatus.add(request);
        });
    }

    // 获取平台未全部发货的的oid 及 已上传的包裹数量
    private Map<Long/*oid*/, Integer/*packageSize*/> getPartOrderPackageSize(TaobaoClientHelper client, String tid) {
        Trade tbTrade = null;
        TradeFullinfoGetResponse response = getTrade(client, tid);
        if (Objects.isNull(response) || Objects.isNull(tbTrade = response.getTrade())) {
            throw new PlatformAccessException(ERROR_SYSTEM, "获取淘宝订单失败.");
        }

        Map<String/*oidStr*/, Integer/*packageSize*/> oidStr2logisticsCount = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(tbTrade.getCombineLogisticsDetails())) {
            for (CombineLogisticsDetail logistics : tbTrade.getCombineLogisticsDetails()) {
                oidStr2logisticsCount.put(logistics.getSubOrderId(), oidStr2logisticsCount.getOrDefault(logistics.getSubOrderId(), 0) + 1);
            }
        }

        Map<Long/*oid*/, Integer/*packageSize*/> result = Maps.newHashMap();
        for (Order order : tbTrade.getOrders()) {
            if (!TB_WAIT_BUYER_CONFIRM_GOODS.equals(order.getStatus()) && !TB_TRADE_FINISHED.equals(order.getStatus()) && !TB_TRADE_CLOSED.equals(order.getStatus())) {
                result.put(order.getOid(), oidStr2logisticsCount.getOrDefault(order.getOidStr(), 0));
            }
        }
        return result;
    }

    private TradeFullinfoGetResponse getTrade(TaobaoClientHelper client, String tid) {
        TradeFullinfoGetRequest req = new TradeFullinfoGetRequest();
        req.setTid(Long.valueOf(tid));
        // 仅仅设置 orders,combine_logistics_details 取不到 combine_logistics_details 值
        req.setFields("seller_nick,buyer_nick,title,type,created,tid,seller_rate,buyer_flag,buyer_rate,status,payment,adjust_fee,post_fee,total_fee,pay_time,end_time,modified,consign_time,buyer_obtain_point_fee,point_fee,real_point_fee,received_payment,commission_fee,buyer_memo,seller_memo,alipay_no,alipay_id,buyer_message,pic_path,num_iid,num,price,buyer_alipay_no,receiver_name,receiver_state,receiver_city,receiver_district,receiver_address,receiver_zip,receiver_mobile,receiver_phone,seller_flag,seller_alipay_no,seller_mobile,seller_phone,seller_name,seller_email,available_confirm_fee,has_post_fee,timeout_action_time,snapshot_url,cod_fee,cod_status,shipping_type,trade_memo,is_3D,buyer_email,buyer_area,trade_from,is_lgtype,is_force_wlb,is_brand_sale,buyer_cod_fee,discount_fee,seller_cod_fee,express_agency_fee,invoice_name,service_orders,credit_cardfee,step_trade_status,step_paid_fee,mark_desc,has_yfx,yfx_fee,yfx_id,yfx_type,trade_source,promotion_details,orders,order_tax_fee,sub_order_tax_fee,sub_order_tax_rate,sub_order_tax_promotion_fee,tax_free,tax_coupon_discount, order_tax_promotion_fee,est_con_time,trade_attr,deliveryCps,tmallDelivery,3plTiming,buyerTaxNO,timing_promise,promise_service,delivery_time,collect_time,sign_time,es_time,os_activity_id,os_fg_item_id,os_gift_count,os_sort_num,asdp_biz_type,asdp_ads,delivery_cps,rx_audit_status,drug_register,is_daixiao,is_sh_ship,is_force_dc,receiver_town,post_gate_declare,promise_sign_time,logistics_infos,combine_item_info,combine_logistics_details,logistics_agreement");
        return client.request(req);
    }

    /**
     * 商家配送
     * @param platRequest
     * @return
     */
    private PlatResponse doSellerSend(PlatRequest platRequest) {
        User user = platRequest.getUser();
        UploadTrade trade = platRequest.getTrade();

        String deliveryMobile = platRequest.getExt("deliveryMobile");
        String deliveryName = platRequest.getExt("deliveryName");
        Assert.hasText(deliveryMobile, "配送人员联系方式为空");
        Assert.hasText(deliveryName, "配送人员名字为空");
        AlibabaAscpLogisticsSellerSendRequest req = new AlibabaAscpLogisticsSellerSendRequest();
        req.setDeliveryMobile(deliveryMobile);
        req.setDeliveryName(deliveryName);
        req.setTid(trade.getTid());
        if(CollectionUtils.isNotEmpty(trade.getOrders())) {
            StringBuilder uploadOids = new StringBuilder();
            trade.getOrders().forEach(o -> uploadOids.append(o.getOid()).append(","));
            if (uploadOids.length() > 1) {
                req.setSubTid(uploadOids.substring(0, uploadOids.length() - 1));
            }
        }
        AlibabaAscpLogisticsSellerSendResponse response = buildClient(user).request(req);
        if(response == null){
            PlatResponse.buildFail("商家配送发货失败，平台未返回响应");
        } else if(!response.isSuccess()){
            logger.error(LogHelper.buildUserLog(user, String.format("商家配送发货失败，req:%s,resp:%s", JSONObject.toJSONString(req), JSONObject.toJSONString(response))));
            PlatResponse.buildFail(response.getSubCode(), response.getSubMsg());
        }
        return PlatResponse.buildSuccess();
    }

    private TaobaoClientHelper buildClient(User user) {
        return new TaobaoClientHelper(user, ERROR_MSG_CONVERTER);
    }

    //推进平台商品为全部发货
    private PlatResponse doUploadAll(PlatRequest platRequest){
        User user = platRequest.getUser();
        UploadTrade trade = platRequest.getTrade();
        // 构建发货信息
        List<AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest> consignPkgs = Lists.newArrayList();
        List<AlibabaAscpLogisticsOfflineSendRequest.ConsignStatusRequest> consignStatus = Lists.newArrayList();
        buildUploadAllConsignInfo(user, trade, consignPkgs, consignStatus);
        if(consignStatus.size()==0){
            return PlatResponse.buildSuccess();
        }
        // 请求平台
        return doUpload(buildClient(user), platRequest, consignPkgs, consignStatus, false,2L);
    }

    public TradeFullinfoGetResponse getFullinfoTrade(User user, Long tid, String fields) {
        TradeFullinfoGetRequest req = new TradeFullinfoGetRequest();
        if (StringUtils.isEmpty(fields)) {
            fields = TRADE_FIELDS;
        }
        req.setFields(fields);
        req.setTid(tid);
        return buildClient(user).request(req);
    }

    public PlatformResponse<Trade> get(User user, String tid, String fields) {
        try {
            TradeFullinfoGetResponse resp = this.getFullinfoTrade(user, Long.parseLong(tid), null);
            if(Objects.nonNull(resp) && Objects.nonNull(resp.getTrade())
                    && StringUtils.isBlank(resp.getTrade().getType())){
                logger.info(LogHelper.buildUserLog(user, String.format("不允许下载零售通订单:%s", tid)));
                throw new TradeException(String.format("不允许下载零售通订单[tid=%s]", tid));
            }
            return fullInfoResp2Common(resp);
        } catch (TaobaoAccessException e) {
            return TbAccessUtils.tbException2CommonResp(e);
        }
    }
    private PlatformResponse<Trade> fullInfoResp2Common(TradeFullinfoGetResponse resp) {
        PlatformResponse<Trade> response = TbAccessUtils.tbResp2Common(resp, "淘宝接口没有返回响应");
        if (resp != null) {
            response.setData(resp.getTrade());
            response.setMsg(resp.getBody());
            if (!response.isSuccess()) {
                if (response.getSubMsg() == null || response.getSubMsg().isEmpty()) {
                    response.setSubMsg("平台没有查询到该笔订单");
                }
            }
        }
        return response;
    }


}

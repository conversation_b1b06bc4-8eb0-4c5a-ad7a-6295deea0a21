package com.raycloud.dmj.tb.trade.business;

import com.google.common.collect.Lists;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.BatchRequestParameter;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeReceiverInfo;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.tb.trade.TbTradeAccess;
import com.taobao.api.request.TopOaidMergeRequest;
import com.taobao.api.request.TradesSoldQueryRequest;
import com.taobao.api.response.TopOaidDecryptResponse;
import com.taobao.api.response.TopOaidMergeResponse;
import com.taobao.api.response.TradesSoldQueryResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Deprecated
public class TbDesensitizationBusiness {

    private static final Logger logger = Logger.getLogger(TbDesensitizationBusiness.class);

    private static final int MAX_TRAVERSE_NUM = 10;
    @Resource
    private TbTradeAccess tbTradeAccess;


    /*public TradeReceiverInfo decrypt(User user, Trade trade) {
        List<TradeReceiverInfo> results = decrypt(user, Lists.newArrayList(trade));
        if (CollectionUtils.isNotEmpty(results)) {
            return results.get(0);
        }
        return null;
    }

    public List<TradeReceiverInfo> decrypt(User user, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return null;
        }
        List<TopOaidDecryptRequest.ReceiverQuery> receiverQueries = new ArrayList<>();
        Set<String> tids = new HashSet<>();
        for (Trade trade:trades) {
            if (!tids.add(trade.getTid())) {
                continue;
            }
            TopOaidDecryptRequest.ReceiverQuery receiverQuery = new TopOaidDecryptRequest.ReceiverQuery();
            receiverQuery.setTid(trade.getTid());
            receiverQuery.setOaid(trade.getAddressMd5());
            receiverQueries.add(receiverQuery);
        }
        logger.info(LogHelper.buildLogHead(user).append(String.format("准备调用淘宝解密接口，解密订单：num=%s,tids=%s",tids.size(),Strings.join(",", tids))));
        TopOaidDecryptResponse response = tbTradeAccess.decrypt(user, receiverQueries);
        if (response.isSuccess()) {
            List<TopOaidDecryptResponse.Receiver> receiverList = response.getReceiverList();
            if (CollectionUtils.isNotEmpty(receiverList)) {
                return convertInfo(receiverList);
            }
            return null;
        } else {
            String msg = new StringBuilder(response.getMessage()).append(";").append(response.getSubMessage()).toString();
            throw new RuntimeException(msg);
        }
    }*/

    public List<String> queryByReceiverMoblie(User user, String mobile, String phone, String name, Date startTime, Date endTime) {
        if (StringUtils.isBlank(mobile)) {
            return new ArrayList<>();
        }
        List<TradesSoldQueryRequest.OrderQuery> orderQueries = new ArrayList<>();
        orderQueries.add(buildOrderQuery(mobile, phone, name, startTime, endTime));
        TradesSoldQueryResponse response = tbTradeAccess.queryByReceiverMobile(user, orderQueries);
        if (response.isSuccess()) {
           return response.getTidList();
        } else {
            String msg = new StringBuilder(response.getMessage()).append(";").append(response.getSubMessage()).toString();
            throw new RuntimeException(msg);
        }
    }

    public List<List<String>> queryMerge(User user, List<Trade> trades) {
        List<List<String>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(trades)) {
            return result;
        }
        List<TopOaidMergeRequest.OrderMerge> orderMergeList = new ArrayList<>();
        for (Trade trade:trades) {
            TopOaidMergeRequest.OrderMerge orderMerge = new TopOaidMergeRequest.OrderMerge();
            orderMerge.setTid(trade.getTid());
            orderMerge.setOaid(trade.getAddressMd5());
            orderMergeList.add(orderMerge);
        }
        if (orderMergeList.size() > 100) {
            for (List<TopOaidMergeRequest.OrderMerge> temp:Lists.partition(orderMergeList, 100)) {
                TopOaidMergeResponse response = tbTradeAccess.queryMerge(user,temp);
                if (response.isSuccess()) {
                   List<String> tidList = response.getTidList();
                   for (String tid:tidList) {
                       result.add(Strings.getAsStringList(tid,",", false));
                   }
                }
            }
        } else {
            TopOaidMergeResponse response = tbTradeAccess.queryMerge(user, orderMergeList);
            if (response.isSuccess()) {
                List<String> tidList = response.getTidList();
                for (String tid:tidList) {
                    result.add(Strings.getAsStringList(tid,",", false));
                }
            }
        }
        return result;
    }

    public List<String> batchQueryByReceiver(Staff staff, BatchRequestParameter parameter) {
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(parameter.getUser())) {
            return result;
        }

        try {
            //遍历查询
            int count = 0;
            for (User user : parameter.getUser()) {

                List<String> temp = queryByReceiverMoblie(user,parameter.getMobile(),parameter.getPhone(),parameter.getName(),parameter.getStartTime(),parameter.getEndTime());
                if (CollectionUtils.isNotEmpty(temp)) {
                    result.addAll(temp);
                }
                count++;
                if (count > MAX_TRAVERSE_NUM) {
                    return result;
                }
            }
/*            List<TradesSoldQueryRequest> queryRequests = new ArrayList<>();
            for (User user : parameter.getUser()) {
                TradesSoldQueryRequest request = new TradesSoldQueryRequest();
                List<TradesSoldQueryRequest.OrderQuery> orderQueries = new ArrayList<>();
                orderQueries.add(buildOrderQuery(parameter.getMobile(), parameter.getPhone(), parameter.getName(), parameter.getStartTime(), parameter.getEndTime()));
                request.setQueryList(orderQueries);
                request.setBatchApiSession(user.getSessionId());
                queryRequests.add(request);
            }
            TaobaoBatchResponse batchResponse = tbTradeAccess.batchQueryByReceiver(queryRequests);
            if (batchResponse.isSuccess() && CollectionUtils.isNotEmpty(batchResponse.getResponseList())) {
                List<TaobaoResponse> batchResult = batchResponse.getResponseList();
                for (TaobaoResponse simple : batchResult) {
                    TradesSoldQueryResponse temp = (TradesSoldQueryResponse) simple;
                    if (CollectionUtils.isNotEmpty(temp.getTidList())) {
                        result.addAll(temp.getTidList());
                    }
                }
            }*/
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "TbDesensitizationBusiness，批量根据收件人信息查询平台订单号异常，异常信息:"+ ExceptionUtils.getFullStackTrace(e)));
        }
        return result;
    }

    private TradesSoldQueryRequest.OrderQuery buildOrderQuery(String mobile, String phone, String name, Date startTime, Date endTime) {
        TradesSoldQueryRequest.OrderQuery orderQuery = new TradesSoldQueryRequest.OrderQuery();
        orderQuery.setReceiverMobile(mobile);
        orderQuery.setReceiverPhone(phone);
        orderQuery.setReceiverName(name);
        orderQuery.setStartCreated(startTime);
        orderQuery.setEndCreated(endTime);
        return orderQuery;
    }

    private List<TradeReceiverInfo> convertInfo(List<TopOaidDecryptResponse.Receiver> receiverList) {
        List<TradeReceiverInfo> receiverInfos = new ArrayList<>();
        for (TopOaidDecryptResponse.Receiver receiver:receiverList) {
            TradeReceiverInfo tradeReceiverInfo = new TradeReceiverInfo();
            tradeReceiverInfo.setTid(receiver.getTid());
            tradeReceiverInfo.setOaid(receiver.getOaid());
            tradeReceiverInfo.setReceiverName(receiver.getName());
            tradeReceiverInfo.setReceiverMobile(receiver.getMobile());
            tradeReceiverInfo.setReceiverPhone(receiver.getPhone());
            tradeReceiverInfo.setReceiverState(receiver.getState());
            tradeReceiverInfo.setReceiverCity(receiver.getCity());
            tradeReceiverInfo.setReceiverDistrict(receiver.getDistrict());
            tradeReceiverInfo.setReceiverAddress(receiver.getAddressDetail());
            tradeReceiverInfo.setMatched(receiver.getMatched());
            receiverInfos.add(tradeReceiverInfo);
        }
        return receiverInfos;
    }

}

package com.raycloud.dmj.tb.logistics;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.enums.CloudBrandCodeEnum;
import com.raycloud.dmj.domain.pt.model.waybill.bean.ConsigneeAddress;
import com.raycloud.dmj.domain.pt.model.waybill.bean.LogisticsService;
import com.raycloud.dmj.domain.pt.model.waybill.bean.PackageItem;
import com.raycloud.dmj.domain.pt.model.waybill.bean.ShippingAddress;
import com.raycloud.dmj.domain.pt.model.waybill.get.TradeOrderInfoCol;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.pt.model.waybill.get.WaybillApplyNewRequest;
import com.raycloud.dmj.tb.common.TaobaoClientHelper;
import com.raycloud.dmj.tb.logistics.sdk.CainiaoWaybillIiGetRequest;
import com.taobao.api.TaobaoBatchResponse;
import com.taobao.api.TaobaoRequest;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.request.CainiaoWaybillIiCancelRequest;
import com.taobao.api.response.CainiaoWaybillIiCancelResponse;
import com.taobao.api.response.CainiaoWaybillIiGetResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * TbLogisticsAccessNew
 * 批量获取单号
 * 内部使用单个获取单号
 * 外部使用批量请求
 *
 * <AUTHOR>
 * @Date 2019/1/20
 * @Time 11:44
 */
@Component
public class TbLogisticsAccessNew {

    protected final Logger logger = Logger.getLogger(this.getClass());
    private static final List<String> needMultiPackagesShipment = new ArrayList<>();

    static {
        needMultiPackagesShipment.add("CP446881");
        needMultiPackagesShipment.add("DBKD");
        needMultiPackagesShipment.add("XFWL");
        needMultiPackagesShipment.add("CP449455");
    }
    /**
     * 单号获取，使用batchReques方式去获取，
     * 最多支持20笔订单同时获取
     */
    public CainiaoWaybillIiGetResponse waybillGet(User user, List<WaybillApplyNewRequest> waybillApplyNewRequest, List<CainiaoWaybillIiGetResponse.WaybillCloudPrintResponse> cancleModules) {
        WaybillApplyNewRequest batchRequest = null;
        for (WaybillApplyNewRequest request : waybillApplyNewRequest) {
            if (batchRequest == null) {
                batchRequest = request;
                continue;
            }
            batchRequest.getTrade_order_info_cols().addAll(request.getTrade_order_info_cols());
        }
        return waybillGet(user, batchRequest, null);


    }

    /**
     * 单号获取，使用batchReques方式去获取，
     * 最多支持20笔订单同时获取
     */
    public CainiaoWaybillIiGetResponse waybillGet(User user, WaybillApplyNewRequest waybillApplyNewRequest, List<CainiaoWaybillIiGetResponse.WaybillCloudPrintResponse> cancleModules) {
        handleSfCpCode(waybillApplyNewRequest);
        try {
            cancleWaybillCode(user, waybillApplyNewRequest, cancleModules);
        } catch (Exception e) {
            Logs.error("大头笔为null 取消单失败" + e.getMessage());
        }
        List<TradeOrderInfoCol> tradeOrderInfoColList = waybillApplyNewRequest.getTrade_order_info_cols();
        List<TaobaoRequest<CainiaoWaybillIiGetResponse>> batchReq = new ArrayList<>(tradeOrderInfoColList.size());
        for (TradeOrderInfoCol tradeOrderInfoCol : tradeOrderInfoColList) {
            batchReq.add(buildCainiaoWaybillIiGetRequest(user, waybillApplyNewRequest, tradeOrderInfoCol));
        }
        TaobaoBatchResponse taobaoBatchResponse = new TaobaoClientHelper(user).batchRequest(batchReq);
        CainiaoWaybillIiGetResponse result = new CainiaoWaybillIiGetResponse();
        if (!taobaoBatchResponse.isSuccess() && result.isSuccess()) {
            result.setSubMsg(taobaoBatchResponse.getSubMsg());
            result.setSubCode(taobaoBatchResponse.getSubCode());
            result.setMsg(taobaoBatchResponse.getMsg());
            result.setErrorCode(taobaoBatchResponse.getErrorCode());
            result.setCode(taobaoBatchResponse.getCode());
            result.setSubMessage(taobaoBatchResponse.getSubMessage());
            Logs.error("新版获取单号失败1：" + JSONObject.toJSONString(taobaoBatchResponse));
            return result;
        }
        for (TaobaoResponse response : taobaoBatchResponse.getResponseList()) {
            CainiaoWaybillIiGetResponse waybillIiGetResponse = (CainiaoWaybillIiGetResponse) response;
            if (!waybillIiGetResponse.isSuccess() && result.isSuccess()) {
                result.setSubMsg(waybillIiGetResponse.getSubMsg());
                result.setSubCode(waybillIiGetResponse.getSubCode());
                result.setMsg(waybillIiGetResponse.getMsg());
                result.setErrorCode(waybillIiGetResponse.getErrorCode());
                result.setCode(waybillIiGetResponse.getCode());
                result.setSubMessage(waybillIiGetResponse.getSubMessage());
                Logs.error("新版获取单号失败2：" + JSONObject.toJSONString(waybillIiGetResponse));
                return result;
            }
            List<CainiaoWaybillIiGetResponse.WaybillCloudPrintResponse> resultModules = result.getModules();
            if (resultModules == null) {
                resultModules = new ArrayList<>();
            }
            if (CollectionUtils.isNotEmpty(waybillIiGetResponse.getModules())) {
                resultModules.addAll(waybillIiGetResponse.getModules());
                result.setModules(resultModules);
            } else {
                Logs.error(JSONObject.toJSONString(waybillIiGetResponse));
            }
        }
        return result;
    }

    private void handleSfCpCode(WaybillApplyNewRequest waybillApplyNewRequest) {
        waybillApplyNewRequest.setCp_code(CloudBrandCodeEnum.getPlatformCpCodeByCpCode(waybillApplyNewRequest.getCp_code()));
    }

    private void cancleWaybillCode(User user, WaybillApplyNewRequest waybillApplyNewRequest, List<CainiaoWaybillIiGetResponse.WaybillCloudPrintResponse> modules) {
        handleSfCpCode(waybillApplyNewRequest);
        if (CollectionUtils.isNotEmpty(modules)) {
            List<TaobaoRequest<CainiaoWaybillIiCancelResponse>> batchReq = new ArrayList<>(modules.size());
            for (CainiaoWaybillIiGetResponse.WaybillCloudPrintResponse module : modules) {
                CainiaoWaybillIiCancelRequest req = new CainiaoWaybillIiCancelRequest();
                req.setCpCode(waybillApplyNewRequest.getCp_code());
                req.setWaybillCode(module.getWaybillCode());
                batchReq.add(req);
            }
            TaobaoResponse request = new TaobaoClientHelper(user).batchRequest(batchReq);
            if (request != null && !request.isSuccess()) {
                throw new IllegalArgumentException(request.getMessage() + "," + request.getSubMsg());
            }
        }
    }

    private CainiaoWaybillIiGetRequest buildCainiaoWaybillIiGetRequest(User user, WaybillApplyNewRequest waybillApplyNewRequest, TradeOrderInfoCol tradeOrderInfoCol) {
        handleSfCpCode(waybillApplyNewRequest);
        /*
            1.组装快递基础信息
         */
        CainiaoWaybillIiGetRequest req = new CainiaoWaybillIiGetRequest();
        CainiaoWaybillIiGetRequest.WaybillCloudPrintApplyNewRequest waybillCloudPrintApplyNewRequest = new CainiaoWaybillIiGetRequest.WaybillCloudPrintApplyNewRequest();
        waybillCloudPrintApplyNewRequest.setCpCode(waybillApplyNewRequest.getCp_code());
        waybillCloudPrintApplyNewRequest.setProductCode(tradeOrderInfoCol.getProduct_type());//仅顺丰支持传入的产品编码
        waybillCloudPrintApplyNewRequest.setThreePlTiming(tradeOrderInfoCol.getThreePlTiming());//3c属性
        waybillCloudPrintApplyNewRequest.setNeedEncrypt(true);
        // 平安达腾飞 德邦 子母单 走到快运这边了就代表是用子母单的
        if (needMultiPackagesShipment.contains(waybillApplyNewRequest.getCp_code())) {
            logger.debug("MultiPackagesShipment:true");
            waybillCloudPrintApplyNewRequest.setMultiPackagesShipment(true);
        }
        /*
            2.寄件人
         */
        CainiaoWaybillIiGetRequest.UserInfoDto sender = new CainiaoWaybillIiGetRequest.UserInfoDto();
        ShippingAddress shippingAddress = waybillApplyNewRequest.getShipping_address();
        sender.setName((shippingAddress != null && StringUtils.isNotBlank(shippingAddress.getName())) ? shippingAddress.getName() : user.getNick());
        sender.setPhone((shippingAddress != null && StringUtils.isNotBlank(shippingAddress.getPhone())) ? shippingAddress.getPhone() : "18711111111");
        CainiaoWaybillIiGetRequest.AddressDto senderAddress = new CainiaoWaybillIiGetRequest.AddressDto();
        sender.setAddress(senderAddress);
        if (null != shippingAddress) {
            senderAddress.setCity(shippingAddress.getCity());
            senderAddress.setDetail(shippingAddress.getAddress_detail());
            senderAddress.setDistrict(shippingAddress.getArea());
            senderAddress.setProvince(shippingAddress.getProvince());
            senderAddress.setTown(shippingAddress.getTown());
        }
        waybillCloudPrintApplyNewRequest.setSender(sender);

        /*
            3.面单信息 包含订单 收件人 包裹等
         */
        List<CainiaoWaybillIiGetRequest.TradeOrderInfoDto> tradeOrderInfoDtoList = new ArrayList<>();
        CainiaoWaybillIiGetRequest.TradeOrderInfoDto tradeOrderInfoDto = new CainiaoWaybillIiGetRequest.TradeOrderInfoDto();
        tradeOrderInfoDto.setObjectId(tradeOrderInfoCol.getTradeOrderPackageId());//请求id
        /*
            3.1 订单信息
         */
        CainiaoWaybillIiGetRequest.OrderInfoDto orderInfo = new CainiaoWaybillIiGetRequest.OrderInfoDto();
        orderInfo.setOrderChannelsType(tradeOrderInfoCol.getOrder_channels_type());//订单渠道平台编码
        orderInfo.setTradeOrderList(tradeOrderInfoCol.getTrade_order_list());//订单号,数量限制100，订单号 合单要一起给
        orderInfo.setOutTradeOrderList(tradeOrderInfoCol.getOut_trade_order_list());//外部电商平台交易单号集合，非必填，数量限制100
        tradeOrderInfoDto.setOrderInfo(orderInfo);//订单信息
        /*
            3.2 包裹信息
         */
        CainiaoWaybillIiGetRequest.PackageInfoDto packageInfo = new CainiaoWaybillIiGetRequest.PackageInfoDto();
        packageInfo.setId(tradeOrderInfoCol.getPackage_id());//包裹id
        packageInfo.setTotalPackagesCount(tradeOrderInfoCol.getTotalPackagesCount());//包裹总数
        List<PackageItem> packageItemList = tradeOrderInfoCol.getPackage_items();
        List<CainiaoWaybillIiGetRequest.Item> items = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(packageItemList)) {
            for (PackageItem packageItem : packageItemList) {
                CainiaoWaybillIiGetRequest.Item item = new CainiaoWaybillIiGetRequest.Item();
                item.setCount(packageItem.getCount());
                item.setName(packageItem.getItem_name());
                items.add(item);
            }
        }
        packageInfo.setItems(items);//商品信息
        waybillCloudPrintApplyNewRequest.setCustomerCode(waybillApplyNewRequest.getCustomerCode());
        // 顺丰必传字段
        if (Objects.equals(waybillApplyNewRequest.getCp_code(), "SF")) {
            // 包裹长宽高 默认1
            packageInfo.setLength(1L);
            packageInfo.setWidth(1L);
            packageInfo.setHeight(1L);
            // 商品描述
            packageInfo.setGoodsDescription("商品类目");
            // 品牌编码
            waybillCloudPrintApplyNewRequest.setBrandCode(waybillApplyNewRequest.getBrandCode());
        } else {
            Long weight = tradeOrderInfoCol.getWeight();
            if (weight != null && weight > 0L) {
                packageInfo.setWeight(weight);
            }
            Long valume = tradeOrderInfoCol.getValume();
            if (valume != null && valume > 0L) {
                packageInfo.setVolume(valume);
            }
        }
        tradeOrderInfoDto.setPackageInfo(packageInfo);//包裹信息
        tradeOrderInfoDto.setWaybillCode(tradeOrderInfoCol.getWaybillCode());//带面单号模式取号，目前仅顺丰支持

        List<LogisticsService> logistics_service_list = tradeOrderInfoCol.getLogistics_service_list();
        // 额外信息集合
        Map<String, String> extraMap = new HashMap<>();
        tradeOrderInfoDto.setLogisticsServices(TbLogisticsUtil.transferServiceInfo(waybillApplyNewRequest, extraMap, logistics_service_list));//增值服务
        waybillCloudPrintApplyNewRequest.setExtraInfo(JSON.toJSONString(extraMap));//品牌编码（顺丰使用）
        tradeOrderInfoDto.setTemplateUrl(waybillApplyNewRequest.getCloudTemplateUrl());//模板url
        tradeOrderInfoDto.setUserId(user.getTaobaoId());//订单店铺id  我们现在给的网点的id 可能平台没有强校验
        /*
            3.3 收件人信息
         */
        CainiaoWaybillIiGetRequest.UserInfoDto recipient = new CainiaoWaybillIiGetRequest.UserInfoDto();
        CainiaoWaybillIiGetRequest.AddressDto address = new CainiaoWaybillIiGetRequest.AddressDto();
        ConsigneeAddress consigneeAddress = tradeOrderInfoCol.getConsignee_address();
        if (null != consigneeAddress) {
            address.setCity(consigneeAddress.getCity());
            address.setDetail(consigneeAddress.getAddress_detail());
            address.setDistrict(consigneeAddress.getArea());
            address.setProvince(consigneeAddress.getProvince());
            address.setTown(consigneeAddress.getTown());
        }
        recipient.setAddress(address);
        recipient.setName(tradeOrderInfoCol.getConsignee_name());
        recipient.setPhone(tradeOrderInfoCol.getConsignee_phone());
        if (StringUtils.isNotBlank(tradeOrderInfoCol.getOaid()) && (StringUtils.contains(recipient.getAddress().getDetail(), "*") ||
                StringUtils.contains(recipient.getName(), "*") || StringUtils.contains(recipient.getPhone(), "*") || StringUtils.contains(recipient.getMobile(), "*"))) {
            recipient.setOaid(tradeOrderInfoCol.getOaid());
            recipient.setTid(tradeOrderInfoCol.getTid());
            recipient.setPhone(null);
            recipient.setMobile(null);
        }
        if (StringUtils.isNotBlank(tradeOrderInfoCol.getCaid()) && (StringUtils.contains(recipient.getAddress().getDetail(), "*") ||
                StringUtils.contains(recipient.getName(), "*") || StringUtils.contains(recipient.getPhone(), "*") || StringUtils.contains(recipient.getMobile(), "*"))) {
            if (StringUtils.contains(tradeOrderInfoCol.getCaid(), CommonConstants.CODE_SEGMENT_SYMBOL)) {
                recipient.setCaid(tradeOrderInfoCol.getCaid());
            } else {
                recipient.setOaid(tradeOrderInfoCol.getCaid());
            }
            recipient.setTid(tradeOrderInfoCol.getTid());
            recipient.setPhone(null);
            recipient.setMobile(null);
        }
        tradeOrderInfoDto.setRecipient(recipient);
        tradeOrderInfoDtoList.add(tradeOrderInfoDto);
        waybillCloudPrintApplyNewRequest.setTradeOrderInfoDtos(tradeOrderInfoDtoList);//取号单子信息
        req.setParamWaybillCloudPrintApplyNewRequest(waybillCloudPrintApplyNewRequest);
        return req;
    }

    //public static void main(String[] args) {
    //
    //    /*
    //        1.组装快递基础信息
    //     */
    //    CainiaoWaybillIiGetRequest req = new CainiaoWaybillIiGetRequest();
    //    CainiaoWaybillIiGetRequest.WaybillCloudPrintApplyNewRequest waybillCloudPrintApplyNewRequest = new CainiaoWaybillIiGetRequest.WaybillCloudPrintApplyNewRequest();
    //    waybillCloudPrintApplyNewRequest.setCpCode("DBKD");
    //    waybillCloudPrintApplyNewRequest.setNeedEncrypt(true);
    //    waybillCloudPrintApplyNewRequest.setMultiPackagesShipment(true);
    //
    //    /*
    //        2.寄件人
    //     */
    //    CainiaoWaybillIiGetRequest.UserInfoDto sender = new CainiaoWaybillIiGetRequest.UserInfoDto();
    //    ShippingAddress shippingAddress = new ShippingAddress();
    //    shippingAddress.setProvince("湖南省");
    //    shippingAddress.setCity("长沙市");
    //    shippingAddress.setArea("岳麓区");
    //    shippingAddress.setAddress_detail("快麦测试地址,无需揽收与发货!");
    //    shippingAddress.setName("快麦测试");
    //    shippingAddress.setPhone("18899008888");
    //    shippingAddress.setMobile("18800009999");
    //    sender.setName(shippingAddress.getName());
    //    sender.setPhone(shippingAddress.getPhone());
    //    CainiaoWaybillIiGetRequest.AddressDto senderAddress = new CainiaoWaybillIiGetRequest.AddressDto();
    //    sender.setAddress(senderAddress);
    //    senderAddress.setCity(shippingAddress.getCity());
    //    senderAddress.setDetail(shippingAddress.getAddress_detail());
    //    senderAddress.setDistrict(shippingAddress.getArea());
    //    senderAddress.setProvince(shippingAddress.getProvince());
    //    senderAddress.setTown(shippingAddress.getTown());
    //    waybillCloudPrintApplyNewRequest.setSender(sender);
    //    /*
    //        3.面单信息 包含订单 收件人 包裹等
    //     */
    //    List<CainiaoWaybillIiGetRequest.TradeOrderInfoDto> tradeOrderInfoDtoList = new ArrayList<>();
    //    CainiaoWaybillIiGetRequest.TradeOrderInfoDto tradeOrderInfoDto = new CainiaoWaybillIiGetRequest.TradeOrderInfoDto();
    //    tradeOrderInfoDto.setObjectId("1697096857000");//请求id
    //    /*
    //        3.1 订单信息
    //     */
    //    CainiaoWaybillIiGetRequest.OrderInfoDto orderInfo = new CainiaoWaybillIiGetRequest.OrderInfoDto();
    //    orderInfo.setOrderChannelsType("OTHERS");//订单渠道平台编码
    //    orderInfo.setTradeOrderList(Collections.singletonList("1697096857000"));//订单号,数量限制100，订单号 合单要一起给
    //    //orderInfo.setOutTradeOrderList(tradeOrderInfoCol.getOut_trade_order_list());//外部电商平台交易单号集合，非必填，数量限制100
    //    tradeOrderInfoDto.setOrderInfo(orderInfo);//订单信息
    //    /*
    //        3.2 包裹信息
    //     */
    //    CainiaoWaybillIiGetRequest.PackageInfoDto packageInfo = new CainiaoWaybillIiGetRequest.PackageInfoDto();
    //    packageInfo.setId("KM1697096857001");//包裹id
    //    packageInfo.setTotalPackagesCount(2L);//包裹总数
    //    List<CainiaoWaybillIiGetRequest.Item> items = new ArrayList<>();
    //    CainiaoWaybillIiGetRequest.Item item = new CainiaoWaybillIiGetRequest.Item();
    //    item.setCount(1L);
    //    item.setName("普通货物!");
    //    items.add(item);
    //    packageInfo.setItems(items);//商品信息
    //    // 顺丰必传字段
    //    //if (Objects.equals(waybillApplyNewRequest.getCp_code(), "SF")) {
    //    //    // 包裹长宽高 默认1
    //    //    packageInfo.setLength(1L);
    //    //    packageInfo.setWidth(1L);
    //    //    packageInfo.setHeight(1L);
    //    //    // 商品描述
    //    //    packageInfo.setGoodsDescription("商品类目");
    //    //    // 品牌编码
    //    //    waybillCloudPrintApplyNewRequest.setBrandCode(waybillApplyNewRequest.getBrandCode());
    //    //    waybillCloudPrintApplyNewRequest.setCustomerCode(waybillApplyNewRequest.getCustomerCode());
    //    //}
    //    tradeOrderInfoDto.setPackageInfo(packageInfo);//包裹信息
    //    //tradeOrderInfoDto.setWaybillCode(tradeOrderInfoCol.getWaybillCode());//带面单号模式取号，目前仅顺丰支持
    //
    //    //List<LogisticsService> logistics_service_list = tradeOrderInfoCol.getLogistics_service_list();
    //    // 额外信息集合
    //    Map<String, String> extraMap = new HashMap<>();
    //    //tradeOrderInfoDto.setLogisticsServices(TbLogisticsUtil.transferServiceInfo(waybillApplyNewRequest, extraMap, logistics_service_list));//增值服务
    //    //waybillCloudPrintApplyNewRequest.setExtraInfo(JSON.toJSONString(extraMap));//品牌编码（顺丰使用）
    //    tradeOrderInfoDto.setTemplateUrl("https://cloudprint.cainiao.com/template/standard/293905/39");//模板url
    //    tradeOrderInfoDto.setUserId(131159L);//订单店铺id  我们现在给的网点的id 可能平台没有强校验
    //    /*
    //        3.3 收件人信息
    //     */
    //    CainiaoWaybillIiGetRequest.UserInfoDto recipient = new CainiaoWaybillIiGetRequest.UserInfoDto();
    //    CainiaoWaybillIiGetRequest.AddressDto address = new CainiaoWaybillIiGetRequest.AddressDto();
    //    address.setProvince("湖南省");
    //    address.setCity("长沙市");
    //    address.setDistrict("岳麓区");
    //    //address.setTown(consigneeAddress.getTown());
    //    address.setDetail("麓谷信息港测试地址");
    //    recipient.setAddress(address);
    //    recipient.setName("光云");
    //    recipient.setPhone("18899008790");
    //    //if (StringUtils.isNotBlank(tradeOrderInfoCol.getOaid()) && (StringUtils.contains(recipient.getAddress().getDetail(), "*") ||
    //    //        StringUtils.contains(recipient.getName(), "*") || StringUtils.contains(recipient.getPhone(), "*") || StringUtils.contains(recipient.getMobile(), "*"))) {
    //    //    recipient.setOaid(tradeOrderInfoCol.getOaid());
    //    //    recipient.setTid(tradeOrderInfoCol.getTid());
    //    //    recipient.setPhone(null);
    //    //    recipient.setMobile(null);
    //    //}
    //    //if (StringUtils.isNotBlank(tradeOrderInfoCol.getCaid()) && (StringUtils.contains(recipient.getAddress().getDetail(), "*") ||
    //    //        StringUtils.contains(recipient.getName(), "*") || StringUtils.contains(recipient.getPhone(), "*") || StringUtils.contains(recipient.getMobile(), "*"))) {
    //    //    if (StringUtils.contains(tradeOrderInfoCol.getCaid(), CommonConstants.CODE_SEGMENT_SYMBOL)) {
    //    //        recipient.setCaid(tradeOrderInfoCol.getCaid());
    //    //    } else {
    //    //        recipient.setOaid(tradeOrderInfoCol.getCaid());
    //    //    }
    //    //    recipient.setTid(tradeOrderInfoCol.getTid());
    //    //    recipient.setPhone(null);
    //    //    recipient.setMobile(null);
    //    //}
    //    tradeOrderInfoDto.setRecipient(recipient);
    //    tradeOrderInfoDtoList.add(tradeOrderInfoDto);
    //    waybillCloudPrintApplyNewRequest.setTradeOrderInfoDtos(tradeOrderInfoDtoList);//取号单子信息
    //    req.setParamWaybillCloudPrintApplyNewRequest(waybillCloudPrintApplyNewRequest);
    //    User user = new User();
    //    user.setTaobaoId(2375282742L);
    //    user.setSessionId("70002100622f167a864d7ab9ac82e713901d32e3c3516baf75b6601ace3a8d0e543d8882375282742");
    //    System.out.println(JSONObject.toJSONString(req));
    //    CainiaoWaybillIiGetResponse response =  new TaobaoClientHelper(user).request(req);
    //
    //    System.out.println(JSONObject.toJSONString(response));
    //
    //}

}

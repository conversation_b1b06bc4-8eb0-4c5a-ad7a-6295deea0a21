package com.raycloud.dmj.domain.statics;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 统计天猫同意退款的成功次数
 * <AUTHOR> zhangxin
 * @Date : 2016/3/7 0007 17:03
 * @From : as-parent
 */
@Data
public class Statics implements Serializable{

    private Long id;

    /**
     * 操作人的id
     */
    private Long staffId;

    /**
     * 店铺id
     */
    private Long shopId;

    private Long taobaoId;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 淘宝平台的退款id
     */
    private Long refundId;

    /**
     * 调用是否成功  "success"  "fail"
     */
    private String status;

    /**
     * 是否删除   1:可用  0:删除
     */
    private Integer enableStatus;

    private Date created;

    private Date modified;
}

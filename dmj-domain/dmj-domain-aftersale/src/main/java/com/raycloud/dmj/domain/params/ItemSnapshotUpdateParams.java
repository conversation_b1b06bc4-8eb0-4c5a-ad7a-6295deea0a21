package com.raycloud.dmj.domain.params;

import com.alibaba.fastjson.annotation.JSONField;
import com.raycloud.dmj.domain.query.AsBaseQuery;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by guoyao on 2018/11/29.
 */
@Data
public class ItemSnapshotUpdateParams extends AsBaseQuery {

    private static final long serialVersionUID = -7126199045763224832L;

    /**
     * 修改 良品数
     */
    private Integer updateGoodItemCount;

    /**
     * 修改 次品数
     */
    private Integer updateBadItemCount;

    /**
     * 修改数量
     */
    private Integer updateCount;

    /**
     * 工单ids
     */
    private List<Long> ids;

    /**
     * 退换货品id信息
     */
    private List<Long> reissueIds;

    /**
     * 售后工单ids
     */
    private List<Long> workOrderIds;
    /**
     * 收货入仓时间
     */
    private Date updateReceiveGoodsTime;

    private String updateReceiveGoodsOperatorIds;

    private String updateSupplierName;

    private String updateRefundMoney;

    private String updatePayment;

    private String updateRemark;

    private Long updateWorkOrderId;

    /**
     * 商品维度的实退数量 （前端展示）
     */
    private Integer returnItemSum;

    /**
     * 平台订单号
     */
    private String updateTid;

    /**
     * 实付单价
     */
    private Long updateUnitPrice;

    /**
     * 实付单价（保留6位小数）
     */
    private Double updateItemUnitPrice;
    /**
     * 系统应退
     */
    private BigDecimal updateRefundableMoney;
    /**
     * 平台实退
     */
    private BigDecimal updateRawRefundMoney;

    /**
     * 修改平台图片
     */
    private String updatePlatformPicPath;
    /**
     * 订单商品状态
     */
    private String sysStatus;

    /**
     * 请不要操作
     */
    @JSONField(serialize = false)
    private BigDecimal updateRealRefundableMoney;

    /**
     * 请不要操作
     */
    @JSONField(serialize = false)
    private BigDecimal updateRealRawRefundMoney;

    @JSONField(serialize = false)
    private boolean isUpdateRealMoney = false;


    public BigDecimal getUpdateRealRefundableMoney() {
        if (null != updateRealRefundableMoney) {
            return updateRealRefundableMoney;
        }
        return null != updateRefundableMoney ? updateRefundableMoney.divide(BigDecimal.valueOf(100), 6, BigDecimal.ROUND_DOWN) : null;
    }

    public BigDecimal getUpdateRealRawRefundMoney() {
        if (null != updateRealRawRefundMoney) {
            return updateRealRawRefundMoney;
        }
        return null != updateRawRefundMoney ? updateRawRefundMoney.divide(BigDecimal.valueOf(100), 6, BigDecimal.ROUND_DOWN) : null;
    }

    public void setUpdateRawRefundMoney(BigDecimal updateRawRefundMoney) {
        if (null != updateRawRefundMoney) {
            this.updateRealRawRefundMoney = updateRawRefundMoney.divide(BigDecimal.valueOf(100), 6, BigDecimal.ROUND_DOWN);
        } else {
            this.updateRealRawRefundMoney = null;
        }
        this.updateRawRefundMoney = updateRawRefundMoney;
    }

    public void setUpdateRefundableMoney(BigDecimal updateRefundableMoney) {
        if (null != updateRefundableMoney) {
            this.updateRealRefundableMoney = updateRefundableMoney.divide(BigDecimal.valueOf(100), 6, BigDecimal.ROUND_DOWN);
        } else {
            this.updateRealRefundableMoney = null;
        }
        this.updateRefundableMoney = updateRefundableMoney;
    }

    /**
     * 更新申请数
     */
    private Integer updateReceivableCount;
}

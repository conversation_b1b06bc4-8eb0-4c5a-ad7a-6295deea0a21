package com.raycloud.dmj.utils.wms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.base.Joiner;
import com.google.common.base.Objects;
import com.google.common.collect.*;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.DiamondConfig;
import com.raycloud.dmj.domain.item.*;
import com.raycloud.dmj.domain.item.wash.params.ItemWashLabelVo;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.*;
import com.raycloud.dmj.domain.stock.InOutWarehouseType;
import com.raycloud.dmj.domain.stock.StockChangeBusiType;
import com.raycloud.dmj.domain.stock.StockInOutRecord;
import com.raycloud.dmj.domain.stock.StockInOutRecordType;
import com.raycloud.dmj.domain.stock.process.vo.ConversionFormulaVO;
import com.raycloud.dmj.domain.stock.process.vo.ItemUnitVO;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wave.WaveConfig;
import com.raycloud.dmj.domain.wave.WaveUniqueCode;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.enums.ContainerTypeEnum;
import com.raycloud.dmj.domain.wms.enums.WmsConfigExtInfoEnum;
import com.raycloud.dmj.domain.wms.enums.*;
import com.raycloud.dmj.enums.StockInOutRecordSourceEnum;
import com.raycloud.dmj.product.domain.OrderStockProduct;
import com.raycloud.dmj.product.enums.OrderStockProductStatusEnum;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.CustomSortUtils;
import com.raycloud.dmj.web.model.wms.GoodsSectionInventorySearchParams;
import com.raycloud.dmj.web.model.wms.GoodsSectionSkuVo;
import com.raycloud.dmj.web.model.wms.GoodsSectionVo;
import org.apache.log4j.Logger;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.text.Collator;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.reducing;
import static java.util.stream.Collectors.toMap;

/**
 * 仓储工具类
 * Created by shaoxianchang on 2017/8/14.
 */
public class WmsUtils {

    private static Logger logger = Logger.getLogger(WmsUtils.class);

    /**
     * 是否是默认货位编码存储类型
     *
     * @param wmsConfig wmsConfig
     * @return
     */
    public static boolean isSectionCodeStoreDefault(WmsConfig wmsConfig) {
        Integer sectionCodeType = wmsConfig.getSectionCodeType();
        if (sectionCodeType == null) sectionCodeType = 0;
        return sectionCodeType == WmsConfig.STORE_TYPE_DEFAULT;
    }

    /**
     * 是否是默认货位编码存储类型
     *
     * @param staff staff
     * @return
     */
    public static boolean isSectionCodeStoreDefault(Staff staff) {
        Integer sectionCodeType = staff.getConf().getSectionCodeType();
        if (sectionCodeType == null) sectionCodeType = 0;
        return sectionCodeType == WmsConfig.STORE_TYPE_DEFAULT;
    }

    /**
     * 将货位出入库记录转成库存出入库记录
     * @return
     */
    public static StockInOutRecord convertToStockInOutRecord(GoodsSectionInOutRecord gsInOutRecord) {
        StockInOutRecord stockInOutRecord = new StockInOutRecord();
        stockInOutRecord.setSysItemId(gsInOutRecord.getSysItemId());
        stockInOutRecord.setSysSkuId(gsInOutRecord.getSysSkuId());
        stockInOutRecord.setOuterId(gsInOutRecord.getOuterId());
        stockInOutRecord.setTitle(gsInOutRecord.getTitle());
        stockInOutRecord.setPropertiesName(gsInOutRecord.getPropertiesName());
        stockInOutRecord.setPropertiesAlias(gsInOutRecord.getPropertiesAlias());
        stockInOutRecord.setStockChange(gsInOutRecord.getStockChange());
        stockInOutRecord.setGoodsAllocation(gsInOutRecord.getGoodsAllocation());
        stockInOutRecord.setGoodsSectionId(gsInOutRecord.getGoodsSectionId());
        stockInOutRecord.setWarehouseId(gsInOutRecord.getWarehouseId());
        stockInOutRecord.setWarehouseName(gsInOutRecord.getWarehouseName());

        stockInOutRecord.setStaffId(gsInOutRecord.getStaffId());
        stockInOutRecord.setStaffName(gsInOutRecord.getStaffName());
        stockInOutRecord.setAccountId(gsInOutRecord.getAccountId());
        stockInOutRecord.setAccountName(gsInOutRecord.getAccountName());
        stockInOutRecord.setCompanyId(gsInOutRecord.getCompanyId());
        stockInOutRecord.setUserId(gsInOutRecord.getUserId());
        stockInOutRecord.setOperateTime(gsInOutRecord.getOperateTime());
        stockInOutRecord.setFromOperateTime(gsInOutRecord.getOperateTime());
        stockInOutRecord.setToOperateTime(gsInOutRecord.getOperateTime());
        stockInOutRecord.setOrderMoney(gsInOutRecord.getOrderMoney());
        stockInOutRecord.setShipper(gsInOutRecord.getShipper());
        stockInOutRecord.setShipperId(gsInOutRecord.getShipperId());

        //出入库记录存入子商品id（反库会根据这个id查询）
        stockInOutRecord.setOrderId(gsInOutRecord.getOrderId());
        stockInOutRecord.setContent(gsInOutRecord.getContent());
        stockInOutRecord.setOrderNumber(gsInOutRecord.getOrderNumber());
        stockInOutRecord.setPlatformOrderNumber(gsInOutRecord.getPlatformOrderNumber());
        stockInOutRecord.setOrderType(gsInOutRecord.getOrderType());
        stockInOutRecord.setType(gsInOutRecord.getType());
        stockInOutRecord.setCustomType(gsInOutRecord.getCustomType());
        stockInOutRecord.setItemType(BooleanUtils.isTrue(gsInOutRecord.getQualityType()) ? 1 : 0);
        stockInOutRecord.setBatchNo(gsInOutRecord.getBatchNo());
        stockInOutRecord.setRemark(gsInOutRecord.getRemark());
        stockInOutRecord.setProductTime(gsInOutRecord.getProductTime());
        stockInOutRecord.setExpireDate(gsInOutRecord.getExpireDate());
        stockInOutRecord.setSource(gsInOutRecord.getGoodsSource() != null ? gsInOutRecord.getGoodsSource().getCode() : StockInOutRecordSourceEnum.UN_KNOWN.getCode());
        stockInOutRecord.setLogisticsName(gsInOutRecord.getLogisticsName());
        stockInOutRecord.setExpressCode(gsInOutRecord.getExpressCode());
        stockInOutRecord.setLogisticsCode(gsInOutRecord.getLogisticsCode());
        return stockInOutRecord;
    }

    public static boolean isShowZhCode(WmsConfig config) {
        return !WmsUtils.isSectionCodeStoreDefault(config)
                && java.util.Objects.equals(config.getGsCodeDisplay(), WmsConfig.GS_CODE_DISPLAY_ZH);
    }

    public static String encodeGsCode(WmsConfig config, String code, GoodsSection goodsSection) {
        if (!isShowZhCode(config)) {
            return encodeGsCode(config, code);
        }

        if (goodsSection != null) {
            StringBuilder sb = new StringBuilder();
            sb.append(goodsSection.getCode(), 0, goodsSection.getCode().indexOf("-") > 0 ? goodsSection.getCode().indexOf("-") : goodsSection.getCode().length()).append("-")
                    .append(goodsSection.getShelfPosition()).append("-")
                    .append(goodsSection.getColumnPosition()).append("-")
                    .append(goodsSection.getLevelPosition()).append("-")
                    .append(goodsSection.getHorizontalPosition());
            code = sb.toString();
        }
        return encodeGsCode(config, code);
    }

    /**
     * 根据货位编码显示配置，转换编码
     */
    public static String encodeGsCode(WmsConfig config, String code) {
        if (StringUtils.isEmpty(code)) {
            return "";
        }
        return isSectionCodeStoreDefault(config) ? encodeGsGoodDefault(config, code) : encodeGsGoodCustom(config, code);
    }

    private static String encodeGsGoodDefault(WmsConfig config, String code) {
        Integer gsCodeDisplay = config.getGsCodeDisplay();
        if (BooleanUtils.isNotTrue(config.getOpenGsCodeDisplay()) || gsCodeDisplay == null || gsCodeDisplay == WmsConfig.GS_CODE_DISPLAY_DEFAULT || code.length() < WmsConstants.CODE_MIN_LENGTH || code.length() >= WmsConstants.CODE_MAX_LENGTH) {
            return code;
        }
        if (StringUtils.containsAny(code, "-区排层位".toCharArray())) {
            return code;
        }
        String fmt = "%s%s%s%s";
        if (gsCodeDisplay == WmsConfig.GS_CODE_DISPLAY_LINE) {
            fmt = "%s-%s-%s-%s";
        } else if (gsCodeDisplay == WmsConfig.GS_CODE_DISPLAY_ZH) {
            fmt = "%s区%s排%s层%s位";
        }

        int posStart = code.length() - (ObjectUtils.defaultIfNull(config.getPosLimitNum(), WmsConstants.DEFAULT_HORIZONTAL_LEN));
        int levelStart = posStart - (ObjectUtils.defaultIfNull(config.getLevelLimitNum(), WmsConstants.DEFAULT_LEVEL_LEN));
        int shelfStart = levelStart - (ObjectUtils.defaultIfNull(config.getShelfLimitNum(), WmsConstants.DEFAULT_SHELF_LEN));
        if (shelfStart <= 0) {
            return code;
        }
        return String.format(fmt, code.substring(0, shelfStart), code.substring(shelfStart, levelStart), code.substring(levelStart, posStart), code.substring(posStart));
    }

    private static String encodeGsGoodCustom(WmsConfig config, String code) {
        Integer gsCodeDisplay = config.getGsCodeDisplay();
        if (BooleanUtils.isNotTrue(config.getOpenGsCodeDisplay()) || gsCodeDisplay == null || gsCodeDisplay == WmsConfig.GS_CODE_DISPLAY_DEFAULT) {
            return code.replaceAll(WmsConstants.SPLIT_SHORT_MEDIAN_LINE, "");
        }
        if (gsCodeDisplay != WmsConfig.GS_CODE_DISPLAY_ZH) {
            return code;
        }
        String[] strings = StringUtils.splitByWholeSeparatorPreserveAllTokens(code, WmsConstants.SPLIT_SHORT_MEDIAN_LINE, 5);
        StringBuilder zhCode = new StringBuilder();
        String[] gridLayers = new String[]{"区", "排", "列", "层", "格"};
        for (int i = 0; i < strings.length; i++) {
            String string = strings[i];
            if (StringUtils.isEmpty(string)) {
                continue;
            }
            zhCode.append(string).append(gridLayers[i]);
        }
        return zhCode.toString();
    }

    /**
     * 根据货位排序配置进行排序
     *
     * @param assoSkus
     * @param config
     */
    @Deprecated
    public static void sortAssoSkusWithConfig(List<AssoGoodsSectionSku> assoSkus, final WmsConfig config) {
        Collections.sort(assoSkus, new Comparator<AssoGoodsSectionSku>() {
            @Override
            public int compare(AssoGoodsSectionSku pre, AssoGoodsSectionSku next) {
                if (!next.getStockRegionType().equals(pre.getStockRegionType())) {
                    return next.getStockRegionType().compareTo(pre.getStockRegionType());
                }
                return sortGoodsSectionCode(config, pre.getGoodsSectionCode(), next.getGoodsSectionCode());
            }
        });
    }

    /**
     * 根据sectionCode进行比较操作
     *
     * @param config   config
     * @param preCode  sectionCode
     * @param nextCode sectionCode
     * @return
     */
    public static int sortGoodsSectionCode(WmsConfig config, String preCode, String nextCode) {
        preCode = ObjectUtils.defaultIfNull(preCode, "");
        nextCode = ObjectUtils.defaultIfNull(nextCode, "");
        if (isSectionCodeStoreDefault(config)) {
            return sortGoodsSectionCodeDefault(config, preCode, nextCode);
        } else {
            return sortGoodsSectionCodeCustom(config, preCode, nextCode);
        }
    }

    private static int sortGoodsSectionCodeDefault(WmsConfig config, String preCode, String nextCode) {
        int regionStart = 1 + (config.getRegionLimitNum() == null ? WmsConstants.DEFAULT_REGION_LEN : config.getRegionLimitNum());
        int shelfStart = regionStart + (config.getShelfLimitNum() == null ? WmsConstants.DEFAULT_SHELF_LEN : config.getShelfLimitNum());
        int levelStart = shelfStart + (config.getLevelLimitNum() == null ? WmsConstants.DEFAULT_LEVEL_LEN : config.getLevelLimitNum());
        if (config.getOrderType() != null && config.getOrderType() == WmsConfig.ORDER_TYPE_POS_LEVEL) {
            if (config.getCodeRuleType() != null && config.getCodeRuleType() == WmsConfig.CODE_RULE_TYPE_CUSTOM) {
                preCode = String.format("%s%s%s%s", preCode.substring(0, regionStart), preCode.substring(regionStart, shelfStart), preCode.substring(levelStart), preCode.substring(shelfStart, levelStart));
                nextCode = String.format("%s%s%s%s", nextCode.substring(0, regionStart), nextCode.substring(regionStart, shelfStart), nextCode.substring(levelStart), nextCode.substring(shelfStart, levelStart));
            } else {
                preCode = String.format("%s%s%s%s", preCode.substring(0, 3), preCode.substring(3, 5), preCode.substring(6), preCode.substring(5, 6));
                nextCode = String.format("%s%s%s%s", nextCode.substring(0, 3), nextCode.substring(3, 5), nextCode.substring(6), nextCode.substring(5, 6));
            }
        }
        return preCode.compareToIgnoreCase(nextCode);
    }

    private static int sortGoodsSectionCodeCustom(WmsConfig wmsConfig, String preCode, String nextCode) {
        Integer shelfLen = (null == wmsConfig.getShelfLimitNum() ? WmsConstants.CUSTOM_SHELF_LEN : wmsConfig.getShelfLimitNum());
        Integer columnLen = (null == wmsConfig.getColumnLimitNum() ? WmsConstants.CUSTOM_COLUMN_LEN : wmsConfig.getColumnLimitNum());
        Integer levelLen = (null == wmsConfig.getLevelLimitNum() ? WmsConstants.CUSTOM_LEVEL_LEN : wmsConfig.getLevelLimitNum());
        Integer horizontalLen = (null == wmsConfig.getPosLimitNum() ? WmsConstants.CUSTOM_HORIZONTAL_LEN : wmsConfig.getPosLimitNum());
        String[] preArr = preCode.split(WmsConstants.SPLIT_SHORT_MEDIAN_LINE);
        String[] nextArr = nextCode.split(WmsConstants.SPLIT_SHORT_MEDIAN_LINE);
        int length = Math.min(preArr.length, nextArr.length);
        // 补10位
        int max = 10;
        int sort = 0;
        for (int i = 0; i < length; i++) {
            sort = padString(preArr[i], max).compareToIgnoreCase(padString(nextArr[i], max));
            if (sort != 0) {
                return sort;
            }
        }
        return preArr.length - nextArr.length;
    }

    private static String padString(String s,Integer max){
        if (StringUtils.isNumeric(s)) {
            //如果是纯数字向左补0
            return StringUtils.leftPad(s, max, "0");
        }
        //非纯数字 不补0 ，原样比较
        return s;
    }

    /**
     * 根据货位编码显示配置，转换多个编码
     */
    public static String encodeGsCodes(WmsConfig config, String codes) {
        if (StringUtils.isEmpty(codes)) {
            return "";
        }

        StringBuilder display = new StringBuilder();
        for (String code : codes.split(",")) {
            display.append(encodeGsCode(config, code)).append(",");
        }
        return display.length() > 0 ? display.substring(0, display.length() - 1) : "";
    }

    /**
     * 获取简化后的商家编码
     */
    public static String getSimplifyGsCode(String code) {
        return StringUtils.isEmpty(code) ? "" :
                code.replaceAll("-", "").replace("区", "").replace("排", "")
                        .replace("列", "").replace("层", "").replace("位", "").replace("格", "");
    }

    /**
     * 解码code去保存
     */
    public static String decodeGsCodeStore(Staff staff, String code) {
        String decode = decodeGsCodeSearchNotSplit(staff, code);
        if (decode.endsWith("-")) {
            return decode.substring(0, decode.length() - 1);
        } else {
            return decode;
        }
    }

    public static String decodeGsCodeSearchNotSplit(Staff staff, String code) {
        if (StringUtils.isEmpty(code)) {
            return "";
        }
        if (WmsUtils.isSectionCodeStoreDefault(staff)) {
            return code.replaceAll("-", "").replace("区", "").replace("排", "").replace("层", "").replace("位", "");
        } else {
            return code.replace("区", getReplaceAfter(code, "区"))
                    .replace("排", getReplaceAfter(code, "排"))
                    .replace("列", getReplaceAfter(code, "列"))
                    .replace("层", getReplaceAfter(code, "层"))
                    .replace("格", getReplaceAfter(code, "格"));
        }
    }

    private static String getReplaceAfter(String code, String replaceBefore) {
        return (code.lastIndexOf(replaceBefore)  + replaceBefore.length() == code.length()) ? "" : "-";
    }

    /**
     * 获取真正的code去查询数据
     */
    public static String decodeGsCodeSearch(Staff staff, String code) {
        //查询时后缀是 -,保留
        return decodeGsCodeSearchNotSplit(staff, code);
    }

    /**
     * 获取有权限的仓库
     * @param staff
     * @param params
     * @return
     */
    public static List<Long> getPrivilegeWarehouseIds(Staff staff, GoodsSectionInventorySearchParams params) {
        Set<Long> warehouseIds = Sets.newHashSet();
        if (params.getWarehouseId() != null && params.getWarehouseId() > 0L) {
            warehouseIds.add(params.getWarehouseId());
        } else if (StringUtils.isNotEmpty(params.getWarehouseIds())) {
            if (!DataUtils.checkIdsFormat(params.getWarehouseIds())) {
                throw new IllegalArgumentException("ids格式不正确！");
            }
            warehouseIds.addAll(Lists.newArrayList(ArrayUtils.toLongArray(params.getWarehouseIds())));
        }
        //校验权限仓库
        if (StringUtils.isNotEmpty(staff.getWarehouseGroup())) {
            List<Long> pWarehouseIds = Lists.newArrayList(ArrayUtils.toLongArray(staff.getWarehouseGroup()));
            if (warehouseIds.isEmpty()) {
                return pWarehouseIds;
            } else {
                Iterator<Long> iterator = warehouseIds.iterator();
                while (iterator.hasNext()) {
                    if (!pWarehouseIds.contains(iterator.next())) {
                        iterator.remove();
                    }
                }
            }
        }
        return warehouseIds.isEmpty() ? Lists.newArrayList(0L) : Lists.newArrayList(warehouseIds);
    }

    /**
     * 获取有权限的仓库
     * @param staff
     * @param warehouseIds
     * @return
     */
    public static List<Long> getPrivilegeWarehouseIds(Staff staff, Long... warehouseIds) {
        Set<Long> warehouseIdSet = null;
        Set<Long> pWarehouseIds = Sets.newHashSet();
        if (warehouseIds != null && warehouseIds.length > 0) {
            warehouseIdSet = Sets.newHashSetWithExpectedSize(warehouseIds.length);
            for (Long warehouseId : warehouseIds) {
                if (warehouseId != null) {
                    warehouseIdSet.add(warehouseId);
                }
            }
        }
        if (StringUtils.isNotEmpty(staff.getWarehouseExtraGroup())) {
           pWarehouseIds.addAll(ArrayUtils.toLongSet(staff.getWarehouseExtraGroup()));
        }
        if (StringUtils.isNotEmpty(staff.getWarehouseGroup())) {
            pWarehouseIds.addAll(ArrayUtils.toLongSet(staff.getWarehouseGroup()));
        }
        if (CollectionUtils.isEmpty(warehouseIdSet) || pWarehouseIds == null) {
            return pWarehouseIds != null ? Lists.newArrayList(pWarehouseIds) : null;
        }
        Iterator<Long> iterator = warehouseIdSet.iterator();
        while (iterator.hasNext()) {
            if (!pWarehouseIds.contains(iterator.next())) {
                iterator.remove();
            }
        }

        return warehouseIdSet == null ? null : Lists.newArrayList(warehouseIdSet);
    }

    public static List<String> toLowerCase(List<String> outerIds) {
        if (CollectionUtils.isEmpty(outerIds)) {
            return Lists.newArrayList();
        }

        List<String> lowerOuterIds = Lists.newArrayListWithCapacity(outerIds.size());
        for (String outerId : outerIds) {
            lowerOuterIds.add(StringUtils.trimToEmpty(outerId).toLowerCase());
        }
        return lowerOuterIds;
    }

    public static <T extends Object> Map<String, T> toLowerCase(Map<String, T> outerIdItemMap) {
        if (outerIdItemMap == null || outerIdItemMap.isEmpty()) {
            return Maps.newHashMap();
        }

        Map<String, T> lowerOuterIdItemMap = Maps.newHashMapWithExpectedSize(outerIdItemMap.size());
        for (Map.Entry<String, T> entry : outerIdItemMap.entrySet()) {
            lowerOuterIdItemMap.put(entry.getKey().toLowerCase(), entry.getValue());
        }
        return lowerOuterIdItemMap;
    }


    /**
     * 把多个货主的商家编码，转变为 货主名称_商家编码
     * @param itemMap
     * @return
     */
    public static Map<String,Object> buildShipperNameOuterIdMap(Staff staff,Map<String,List<Object>> itemMap) {
        if (MapUtils.isEmpty(itemMap)) {
            return new HashMap<>();
        }

        Map<String, Object> shipperNameMap = new HashMap<>();
        for (List<Object> objectList : itemMap.values()) {
            for (Object o : objectList) {
                if (o instanceof DmjSku) {
                    DmjSku dmjSku = (DmjSku) o;
                    shipperNameMap.put((Objects.equal("-1",dmjSku.getShipperId()) ? staff.getCompanyName() : dmjSku.getShipper()) + "_" + dmjSku.getOuterId(),o);
                } else {
                    DmjItem dmjItem = (DmjItem) o;
                    shipperNameMap.put((Objects.equal("-1",dmjItem.getShipperId()) ? staff.getCompanyName() : dmjItem.getShipper()) + "_" + dmjItem.getOuterId(),o);
                }
            }
        }
        return shipperNameMap;
    }

    public static Map<Long, List<GoodsSectionOrderRecord>> toOrderIdGsOrderRecordsMap(List<GoodsSectionOrderRecord> gsOrderRecords) {
        Map<Long, List<GoodsSectionOrderRecord>> orderIdGsOrderRecordsMap = Maps.newHashMap();
        for (GoodsSectionOrderRecord gsOrderRecord : gsOrderRecords) {
            List<GoodsSectionOrderRecord> orderRecords = orderIdGsOrderRecordsMap.get(gsOrderRecord.getOrderId());
            if (orderRecords == null) {
                orderIdGsOrderRecordsMap.put(gsOrderRecord.getOrderId(), Lists.newArrayList(gsOrderRecord));
            } else {
                orderRecords.add(gsOrderRecord);
            }
        }
        return orderIdGsOrderRecordsMap;
    }

    public static WorkingStorageSectionGoods buildGoodsFromAssoGoodsSectionSku(Staff staff, GoodsSectionSkuVo sku) {
        WorkingStorageSectionGoods goods = new WorkingStorageSectionGoods();
        goods.setCompanyId(staff.getCompanyId());
        goods.setWarehouseId(sku.getWarehouseId());
        goods.setSysItemId(sku.getSysItemId());
        goods.setSysSkuId(sku.getSysSkuId());
        goods.setBatchNo(sku.getBatchNo());
        goods.setProductTime(sku.getProductTime());
        goods.setCreated(new Date());
        goods.setQualityType(sku.getQualityType());
        goods.setNum(sku.getTotalNum());
        goods.setOprGoodsSectionId(sku.getGoodsSectionId());
        goods.setOprGoodsSectionCode(sku.getGoodsSectionCode());
        goods.setBatchNo(sku.getBatchNo());
        goods.setProductTime(sku.getProductTime());
        goods.setExpireDate(sku.getExpireDate());
        goods.setShipperId(sku.getShipperId());
        return goods;
    }

    public static WorkingStorageSectionGoods buildGoodsFromOrder(Staff staff, Order order, GoodsSection gs) {
        WorkingStorageSectionGoods goods = new WorkingStorageSectionGoods();
        goods.setCompanyId(staff.getCompanyId());
        goods.setWarehouseId(gs.getWarehouseId());
        goods.setSysItemId(order.getItemSysId());
        goods.setSysSkuId(DataUtils.getZeroIfDefault(order.getSkuSysId()));
        goods.setCreated(new Date());
        goods.setQualityType(true);
        goods.setNum(order.getNum());
        goods.setOprGoodsSectionId(gs.getId());
        goods.setOprGoodsSectionCode(gs.getCode());
        return goods;
    }

    public static List<AssoGoodsSectionSku> getAssoSkusByGoodsSectionSkuVos(List<GoodsSectionSkuVo> goodsSectionSkuVos) {
        List<AssoGoodsSectionSku> assoSkus = Lists.newArrayList();
        for (GoodsSectionSkuVo vo : goodsSectionSkuVos) {
            AssoGoodsSectionSku recommend = new AssoGoodsSectionSku();
            recommend.setSysItemId(vo.getSysItemId());
            recommend.setSysSkuId(vo.getSysSkuId());
            recommend.setQualityType(vo.getQualityType());
            recommend.setWarehouseId(vo.getWarehouseId());
            recommend.setBatchNo(vo.getBatchNo());
            recommend.setProductTime(vo.getProductTime());
            recommend.setExpireDate(vo.getExpireDate());
            recommend.setTotalNum(vo.getTotalNum());
            assoSkus.add(recommend);
        }
        return assoSkus;
    }

    public static Map<String, List<AssoGoodsSectionSku>> toItemAssoGoodsSectionSkusMap(List<AssoGoodsSectionSku> assos) {
        Map<String, List<AssoGoodsSectionSku>> itemAssosMap = Maps.newHashMap();
        for (AssoGoodsSectionSku asso : assos) {
            String itemkey = asso.getSysItemId() + "_" + asso.getSysSkuId();
            List<AssoGoodsSectionSku> skus = itemAssosMap.get(itemkey);
            if (skus == null) {
                itemAssosMap.put(itemkey, Lists.newArrayList(asso));
            } else {
                skus.add(asso);
            }
        }
        return itemAssosMap;
    }

    /**
     * 获得商品两个id集合
     * @param details
     * @param function
     * @param <T>
     * @return
     */
    public static <T> Pair<List<Long>, List<Long>> getSysItemSkuIds(List<T> details, Function<T, Long[]> function) {
        List<Long> sysItemIds = Lists.newArrayListWithCapacity(details.size());
        List<Long> sysSkuIds = Lists.newArrayListWithCapacity(details.size());
        for (T detail : details) {
            Long[] ids = function.apply(detail);
            if (ids == null || ids.length < 2) {
                throw new IllegalArgumentException("参数异常");
            }
            sysItemIds.add(ids[0]);
            sysSkuIds.add(ids[1]);
        }
        return new ImmutablePair<List<Long>, List<Long>>(sysItemIds, sysSkuIds);
    }

    /**
     * 判断仓储是否开启
     */
    public static boolean isOpenWms(Staff staff) {
        return staff.getConf() != null && staff.getConf().isOpenWms();
    }

    /**
     * 是否开启返库任务
     */
    public static boolean isOpenDepot(Staff staff) {
        return staff.getConf() != null && staff.getConf().getOpenDepot();
    }

    /**
     * 判断是否是新的仓储
     */
    public static boolean isNewWms(Staff staff) {
        return staff.getConf() != null && staff.getConf().isOpenWms() && staff.getConf().openWmsStorageSection();
    }

    /**
     * 判断是否开启采退暂存区
     * @param staff
     * @return
     */
    public static boolean isOpenPurchaseReturnStorage(Staff staff) {
        return (null != staff.getConf().openPurchaseReturnStorage() && staff.getConf().openPurchaseReturnStorage()==1);
    }

    /**
     * 是否是非暂存位仓储
     *
     * @param staff
     * @return
     */
    public static boolean isOldWms(Staff staff) {
        return isOpenWms(staff) && !isNewWms(staff);
    }

    /**
     * 是否开启Z货位
     */
    public static boolean isOpenGoodsZ(Staff staff){
        return staff.getConf().getStallType() == 1;
    }

    /**
     * 缺货发货锁定Z货位
     */
    public static boolean isInsufficientConsignLockZ(Staff staff, WmsConfig wmsConfig){
        return wmsConfig != null && isOpenGoodsZ(staff) && wmsConfig.getInsufficientConsignLockZ() != null && wmsConfig.getInsufficientConsignLockZ() == 1;
    }

    /**
     * 从货位编码中获取库区编码
     *
     * @param wmsConfig
     * @param goodsSectionCode
     * @return
     */
    public static String getStockRegionCode(WmsConfig wmsConfig, String goodsSectionCode) {
        if (WmsUtils.isSectionCodeStoreDefault(wmsConfig)) {
            int shelfStart = goodsSectionCode.length() - (ObjectUtils.defaultIfNull(wmsConfig.getPosLimitNum(), WmsConstants.DEFAULT_HORIZONTAL_LEN)) -
                    (ObjectUtils.defaultIfNull(wmsConfig.getLevelLimitNum(), WmsConstants.DEFAULT_LEVEL_LEN)) -
                    (ObjectUtils.defaultIfNull(wmsConfig.getShelfLimitNum(), WmsConstants.DEFAULT_SHELF_LEN));
            int regionStart = shelfStart - (ObjectUtils.defaultIfNull(wmsConfig.getRegionLimitNum(), WmsConstants.DEFAULT_REGION_LEN));
            return goodsSectionCode.substring(regionStart, shelfStart);
        }else {
            return goodsSectionCode.split("-")[0];
        }
    }

    /**
     * 将自定义拣货路径转成仓库id和配置明细的map结构
     *
     * @param routeConfig
     * @return
     */
    public static Map<Long, PickGoodsRouteConfigDetail> toRouteConfigDetailMap(PickGoodsRouteConfig routeConfig) {
        if (routeConfig == null || CollectionUtils.isEmpty(routeConfig.getDetails())) {
            return Collections.emptyMap();
        }

        return Maps.uniqueIndex(routeConfig.getDetails(), PickGoodsRouteConfigDetail::getWarehouseId);
    }

    public static Comparator<List<String>> getMultiCodesPickingRouteComparator(WmsConfig wmsConfig, List<String> stockRegionCodesOrder) {
        Ordering<String> goodsSectionOrdering = getGoodsSectionComparators(wmsConfig, stockRegionCodesOrder);
        return (preCodes, nextCodes) -> {
            if (CollectionUtils.isEmpty(preCodes) && CollectionUtils.isEmpty(nextCodes)) {
                return 0;
            }
            if (CollectionUtils.isEmpty(preCodes)) {
                return 1;
            }
            if (CollectionUtils.isEmpty(nextCodes)) {
                return -1;
            }
            int preSize = preCodes.size();
            int nextSize = nextCodes.size();
            int minSize = Math.min(preSize, nextSize);
            int sort = 0;
            for (int i = 0; i < minSize; i++) {
                sort = goodsSectionOrdering.compare(preCodes.get(i), nextCodes.get(i));
                if (sort != 0) {
                    return sort;
                }
            }
            return preSize - nextSize;
        };
    }

    /**
     * 创建一个暂存区
     *
     * @param staff
     * @param type
     * @param warehouseId
     * @return
     */
    public static WorkingStorageSection createSection(Staff staff, WorkingStorageSection.TypeEnum type, Long warehouseId) {
        WorkingStorageSection workingStorageSection = new WorkingStorageSection();
        workingStorageSection.setType(type);
        workingStorageSection.setName(type.getName());
        workingStorageSection.setCompanyId(staff.getCompanyId());
        workingStorageSection.setWarehouseId(warehouseId);
        return workingStorageSection;
    }

    /**
     * 得到一个根据自定义拣货路径排序货位编码相关集合副本
     *
     * @param sortList
     * @param wmsConfig
     * @param stockRegionCodesOrder 自定义拣货路径
     * @param goodsSectionCodeFunc 转换为货位编码字段的函数
     * @param <T>
     * @return
     */
    public static <T> List<T> sortCopyByPickRouteConfig(List<T> sortList, WmsConfig wmsConfig, List<String> stockRegionCodesOrder, java.util.function.Function<T, String> goodsSectionCodeFunc) {
        if (CollectionUtils.isEmpty(sortList) || sortList.size() == 1) {
            return sortList;
        }

        Ordering<String> ordering = getGoodsSectionComparators(wmsConfig, stockRegionCodesOrder);
        return sortList.stream().sorted((pre, next) -> ordering.compare(goodsSectionCodeFunc.apply(pre), goodsSectionCodeFunc.apply(next))).collect(Collectors.toList());
    }

    /**
     * 根据自定义拣货路径排序货位编码相关集合
     *
     * @param sortList
     * @param wmsConfig
     * @param stockRegionCodesOrder 自定义拣货路径
     * @param goodsSectionCodeFunc 转换为货位编码字段的函数
     * @param <T>
     */
    public static <T> void sortByPickRouteConfig(List<T> sortList, WmsConfig wmsConfig, List<String> stockRegionCodesOrder, java.util.function.Function<T, String> goodsSectionCodeFunc) {
        if (CollectionUtils.isEmpty(sortList) || sortList.size() == 1) {
            return;
        }

        Ordering<String> ordering = getGoodsSectionComparators(wmsConfig, stockRegionCodesOrder);
        sortList.sort((pre, next) -> ordering.compare(goodsSectionCodeFunc.apply(pre), goodsSectionCodeFunc.apply(next)));
    }

    /**
     * 路径 -> 货位 -> outerId
     * @param list
     * @param wmsConfig
     * @param stockRegionCodesOrder
     */
    public static void sortInventoryTask(List<GoodsSectionVo> list, final WmsConfig wmsConfig, List<String> stockRegionCodesOrder) {
            List<String> routeList = handlePickRoutes(wmsConfig, stockRegionCodesOrder);
        List<Comparator<GoodsSectionVo>> comparators = Lists.newArrayListWithCapacity(3);
        if (CollectionUtils.isNotEmpty(routeList)) {
            if (isSectionCodeStoreDefault(wmsConfig)) {
                comparators.add(Ordering.natural().nullsLast().onResultOf(goodsSectionVo -> {
                    if (routeList.contains(goodsSectionVo.getCode())) {
                        return routeList.indexOf(goodsSectionVo.getCode());
                    }
                    for(int i = 0; i < routeList.size();i++) {
                        if(goodsSectionVo.getCode().startsWith(routeList.get(i))) {
                            return i;
                        }
                    }
                    return null;
                }));
            }else {
                comparators.add(Ordering.natural().nullsLast().onResultOf(goodsSectionVo -> {
                    String goodsSectionCode = goodsSectionVo.getCode();
                    if(StringUtils.isEmpty(goodsSectionCode)) {
                        return null;
                    }
                    while(goodsSectionCode.length() != 0) {
                        int index = routeList.indexOf(goodsSectionCode);
                        if(index != -1) {
                            return index;
                        }
                        //能截取,截取"-"后面那一段
                        if(goodsSectionCode.contains("-")) {
                            if(goodsSectionCode.length() != 1) {
                                goodsSectionCode = goodsSectionCode.substring(0,goodsSectionCode.lastIndexOf("-"));
                            }else {
                                return null;
                            }
                            //不带"-",只剩下库区位
                        }else {
                            return null;
                        }
                    }
                    return null;
                }));
            }
        }
        //默认根据货位编码大小排序
        Collator chinaCompare = Collator.getInstance(java.util.Locale.CHINA);
        comparators.add((pre, next) -> WmsUtils.sortGoodsSectionCode(wmsConfig, pre.getCode(), next.getCode()));
        comparators.add((pre, next) -> chinaCompare.compare(
                StringUtils.isNotEmpty(pre.getSku().getSkuOuterId()) ? pre.getSku().getSkuOuterId() : pre.getSku().getOuterId(),
                StringUtils.isNotEmpty(next.getSku().getSkuOuterId()) ? next.getSku().getSkuOuterId() : next.getSku().getOuterId()
                )
        );
        list.sort(Ordering.compound(comparators));
    }


    public static void sortAssosForApplySection(Staff staff, List<AssoGoodsSectionSku> assos, WmsConfig wmsConfig, ItemSkuBatch itemSkuBatch, PickGoodsRouteConfigDetail routeConfigDetail) {
        if (CollectionUtils.isEmpty(assos)) {
            return;
        }
        List<Comparator<AssoGoodsSectionSku>> comparators = Lists.newArrayList();
        comparators.add(Comparator.comparing(AssoGoodsSectionSku::getStockRegionType, Comparator.nullsLast(Integer :: compareTo)));
        sortByProductBatchRule(assos, itemSkuBatch, comparators);
        if (null != routeConfigDetail && null != routeConfigDetail.getCustomSortRule()) {
            List<String> stockRegionCodesOrder = CustomSortUtils.getValuesInOrder(routeConfigDetail.getCustomSortRule());
            comparators.addAll(getGoodsSectionSortComparators(wmsConfig, stockRegionCodesOrder, AssoGoodsSectionSku::getGoodsSectionCode));
        }
        comparators.add((pre, next) -> WmsUtils.sortGoodsSectionCode(wmsConfig, pre.getGoodsSectionCode(), next.getGoodsSectionCode()));
        // 散件先处理,剩下根据箱码大小从前到后处理
        comparators.add(Comparator.comparing(AssoGoodsSectionSku::getBoxCode, Comparator.nullsFirst(Comparator.naturalOrder())));
        assos.sort(Ordering.compound(comparators));
    }


    public static void sortWssForAllocateGoods(Staff staff,List<AssoGoodsSectionSku> assos, ItemSkuBatch itemSkuBatch) {
        if (CollectionUtils.isEmpty(assos) || assos.size() == 1) {
            return;
        }
        List<Comparator<AssoGoodsSectionSku>> comparators = Lists.newArrayList();
        sortByProductBatchRule(assos, itemSkuBatch, comparators);
        // 散件先处理,剩下根据箱码大小从前到后处理
        comparators.add(Comparator.comparing(AssoGoodsSectionSku::getBoxCode, Comparator.nullsFirst(Comparator.naturalOrder())));
        assos.sort(Ordering.compound(comparators));
    }


    /**
     * 排序货位库存 （配货专用）
     *
     * @param wmsConfig
     * @param assos
     * @param stockRegionCodesOrder
     */
    public static void sortAssosForAllocateGoods(Staff staff,List<AssoGoodsSectionSku> assos, WmsConfig wmsConfig, List<String> stockRegionCodesOrder,ItemSkuBatch itemSkuBatch) {
        sortAssosForAllocateGoods(staff,assos, wmsConfig, null, stockRegionCodesOrder,itemSkuBatch);
    }

    public static void sortAssosForAllocateGoods(Staff staff,List<AssoGoodsSectionSku> assos, WmsConfig wmsConfig, AllocateGoodsParams params, List<String> stockRegionCodesOrder, ItemSkuBatch itemSkuBatch) {
        sortAssosForAllocateGoods(staff,assos,wmsConfig,params,stockRegionCodesOrder,itemSkuBatch,null);
    }

    /**
     * 排序货位库存 （配货专用）
     *
     * @param assos
     * @param stockRegionCodesOrder
     */
    public static void sortAssosForAllocateGoods(Staff staff,List<AssoGoodsSectionSku> assos, WmsConfig wmsConfig, AllocateGoodsParams params, List<String> stockRegionCodesOrder, ItemSkuBatch itemSkuBatch,Boolean expireDateSort) {
        if (CollectionUtils.isEmpty(assos)) {
            return;
        }
        List<Comparator<AssoGoodsSectionSku>> comparators = Lists.newArrayList();
        WmsAllocateRule allocateRule = wmsConfig.getAllocateRule();
        if (params != null && BooleanUtils.isTrue(params.getAllocateOutGoods())){
            sortByOrderAndRegionConfig(staff,comparators,wmsConfig,assos,params);
        }else {
            Integer stockRegionSortType = acquireStockRegionSortType(params);
            if (stockRegionSortType > 0) {
                if (stockRegionSortType == 2) {
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getStockRegionType, Comparator.nullsLast(Comparator.reverseOrder())));
                } else {
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getStockRegionType, Comparator.nullsFirst(Comparator.naturalOrder())));
                }
            }
        }

        sortByProductBatchRule(assos, itemSkuBatch, comparators,expireDateSort);

        if (allocateRule != null && allocateRule.getAllocateRuleType() != null) {
            switch (allocateRule.getAllocateRuleType()) {
                case ALLOCATE_NUM_ASC:
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getTotalNum));
                    break;
                case ALLOCATE_RULE_DESC:
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getTotalNum).reversed());
                    break;
                case ALLOCATE_NUM_RANGE:
                    List<NumberRange<Integer>> goodsStockNumRanges = allocateRule.getGoodsStockNumRanges();
                    if (CollectionUtils.isNotEmpty(goodsStockNumRanges)) {
                        comparators.add(Comparator.comparingInt(asso -> getRangeIndex(asso.getTotalNum(), goodsStockNumRanges)));
                    }
                    break;
                case BIND_TIME_ASC:
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getId, Comparator.nullsLast(Comparator.naturalOrder())));
                    break;
                default: break;
            }
        }

        comparators.addAll(getGoodsSectionSortComparators(wmsConfig, stockRegionCodesOrder, AssoGoodsSectionSku::getGoodsSectionCode));
        // 散件先处理,剩下根据箱码大小从前到后处理
        comparators.add(Comparator.comparing(AssoGoodsSectionSku::getBoxCode, Comparator.nullsFirst(Comparator.naturalOrder())));
        assos.sort(Ordering.compound(comparators));
    }

    /**
     * 排序箱货位库存
     *
     * @param assos
     * @param stockRegionCodesOrder
     */
    public static void sortBoxAssos(Staff staff,List<AssoGoodsSectionSku> assos, WmsConfig wmsConfig) {
        if (CollectionUtils.isEmpty(assos)) {
            return;
        }
        List<Comparator<AssoGoodsSectionSku>> comparators = Lists.newArrayList();

        comparators.add(Comparator.comparing(AssoGoodsSectionSku::getStockRegionType).reversed());

        if (ObjectUtils.defaultIfNull(wmsConfig.getRecommendSectionOrder(),0) == 0) {
            comparators.add(Comparator.comparing(AssoGoodsSectionSku::getTotalNum));
        }else {
            comparators.add(Comparator.comparing(AssoGoodsSectionSku::getTotalNum).reversed());
        }

        comparators.add((pre, next) -> WmsUtils.sortGoodsSectionCode(wmsConfig, pre.getGoodsSectionCode(), next.getGoodsSectionCode()));

        comparators.add(Comparator.comparing(AssoGoodsSectionSku::getOuterId));

        assos.sort(Ordering.compound(comparators));
    }

    public static WmsConfig.AllocateOrderAndRegionConfig getOrderAndRegionConfig(WmsConfig wmsConfig) {
        String orderAndRegionConfigStr = wmsConfig.getString(WmsConfigExtInfoEnum.ALLOCATE_ORDER_AND_REGION_CONFIG_JSON.getKey());
        if (StringUtils.isEmpty(orderAndRegionConfigStr)) {
            return (WmsConfig.AllocateOrderAndRegionConfig.init());
        }

        // 返回默认值
        WmsConfig.AllocateOrderAndRegionConfig config = JSON.parseObject(orderAndRegionConfigStr, WmsConfig.AllocateOrderAndRegionConfig.class);
        if (config.getNormalTradeOrder() == null) {
            config.setNormalTradeOrder(java.util.Objects.equals(wmsConfig.getPickRegionType(), CommonConstants.VALUE_YES) ? 2 : CommonConstants.ENABLE_STATUS_DELETED);
        }
        if (config.getNormalTradeRegion() == null) {
            config.setNormalTradeRegion(java.util.Objects.equals(wmsConfig.getPickRegionType(), CommonConstants.VALUE_YES) ? CommonConstants.ENABLE_STATUS_DELETED : CommonConstants.ENABLE_STATUS_NORMARL);
        }
        if (config.getDangKouTradeOrder() == null) {
            config.setDangKouTradeOrder(java.util.Objects.equals(wmsConfig.getPickRegionType(), CommonConstants.VALUE_YES) ? 2 : CommonConstants.ENABLE_STATUS_DELETED);
        }
        if (config.getDangKouTradeRegion() == null) {
            config.setDangKouTradeRegion(java.util.Objects.equals(wmsConfig.getPickRegionType(), CommonConstants.VALUE_YES) ? CommonConstants.ENABLE_STATUS_DELETED : CommonConstants.ENABLE_STATUS_NORMARL);
        }
        if (config.getAllocateOrder() == null) {
            config.setAllocateOrder(java.util.Objects.equals(wmsConfig.getPickRegionType(), CommonConstants.VALUE_YES) ? 0 : CommonConstants.ENABLE_STATUS_DELETED);
        }
        if (config.getAllocateRegion() == null) {
            config.setAllocateRegion(2);
        }
        return config;
    }

    public static void sortByOrderAndRegionConfig(Staff staff,List<Comparator<AssoGoodsSectionSku>> comparators,WmsConfig wmsConfig,List<AssoGoodsSectionSku> assos,AllocateGoodsParams params) {
        WmsConfig.AllocateOrderAndRegionConfig orderAndRegionConfig = wmsConfig.getOrderAndRegionConfig();
        if (orderAndRegionConfig == null){
            orderAndRegionConfig = getOrderAndRegionConfig(wmsConfig);
        }
        //获取订单对应类型的配货优先级
        //0.默认 1.拣货区 2.备货区
        if (java.util.Objects.equals(orderAndRegionConfig.getAllocateOrder(), WmsConstants.STOCK_REGION_TYPE_PICK)){
            comparators.add(Comparator.comparing(AssoGoodsSectionSku::getStockRegionType));
        }else if (java.util.Objects.equals(orderAndRegionConfig.getAllocateOrder(), WmsConstants.STOCK_REGION_TYPE_BACK)){
            if (isPickInBackUpRegionAdaptive(staff,wmsConfig,assos,params)){
                comparators.add(Comparator.comparing(AssoGoodsSectionSku::getStockRegionType).reversed());
            }
        } else {
            Integer stockRegionSortType = acquireStockRegionSortType(params);
            if (stockRegionSortType > 0) {
                if (stockRegionSortType == 2) {
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getStockRegionType, Comparator.nullsLast(Comparator.reverseOrder())));
                } else {
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getStockRegionType, Comparator.nullsFirst(Comparator.naturalOrder())));
                }
            }
        }

    }

    public static boolean getStockRegionTypesByRegionConfig(WmsConfig wmsConfig,AssoGoodsSectionSku sku,AllocateGoodsParams params) {
        WmsConfig.AllocateOrderAndRegionConfig orderAndRegionConfig = wmsConfig.getOrderAndRegionConfig();
        if (orderAndRegionConfig == null){
            orderAndRegionConfig = getOrderAndRegionConfig(wmsConfig);
        }

        //调拨单
        if (java.util.Objects.equals(orderAndRegionConfig.getAllocateRegion(), CommonConstants.VALUE_YES)) {
            //全部不支持
            return false;
        } else if (java.util.Objects.equals(orderAndRegionConfig.getAllocateRegion(), 2)) {
            //全部支持
            return true;
        } else if (java.util.Objects.equals(orderAndRegionConfig.getAllocateRegion(), CommonConstants.VALUE_NO)) {
            Map<ItemKey, ReserveAreaPickGoods> goodsMap = params.getPickGoodsMap();
            if (MapUtils.isEmpty(goodsMap) || sku == null) {
                return false;
            }
            ReserveAreaPickGoods pickGoods = goodsMap.get(new ItemKey(sku.getSysItemId(), sku.getSysSkuId()));
            if (pickGoods == null) {
                return false;
            }
            return true;
            //指定商品
        }
        return false;
    }

    public static boolean isPickInBackUpRegionAdaptive(Staff staff,WmsConfig wmsConfig,List<AssoGoodsSectionSku> assos,AllocateGoodsParams params) {
        WmsConfig.AllocateOrderAndRegionConfig orderAndRegionConfig = wmsConfig.getOrderAndRegionConfig();
        if (orderAndRegionConfig == null){
            orderAndRegionConfig = getOrderAndRegionConfig(wmsConfig);
        }

        //调拨单
        if (java.util.Objects.equals(orderAndRegionConfig.getAllocateRegion(), CommonConstants.VALUE_YES)) {
            //全部不支持
            return false;
        } else if (java.util.Objects.equals(orderAndRegionConfig.getAllocateRegion(), 2)) {
            //全部支持
            return true;
        } else if (java.util.Objects.equals(orderAndRegionConfig.getAllocateRegion(), CommonConstants.VALUE_NO)) {
            //指定商品
            return checkPickGoodsNum(assos,params);
        }
        return false;
    }

    public static boolean checkPickGoodsNum(List<AssoGoodsSectionSku> assos,AllocateGoodsParams params) {
        Map<ItemKey, ReserveAreaPickGoods> goodsMap = params.getPickGoodsMap();
        if (MapUtils.isEmpty(goodsMap) || CollectionUtils.isEmpty(assos)) {
            return false;
        }
        AssoGoodsSectionSku sku = assos.get(0);
        ReserveAreaPickGoods pickGoods = goodsMap.get(new ItemKey(sku.getSysItemId(), sku.getSysSkuId()));
        if (pickGoods == null) {
            return false;
        }
        return true;
    }

    public static void sortByProductBatchRule(List<AssoGoodsSectionSku> assos, ItemSkuBatch itemSkuBatch, List<Comparator<AssoGoodsSectionSku>> comparators) {
        sortByProductBatchRule(assos,itemSkuBatch,comparators,null);
    }
    // 商品开启生产日期批次规则，默认排序
    public static void sortByProductBatchRule(List<AssoGoodsSectionSku> assos, ItemSkuBatch itemSkuBatch, List<Comparator<AssoGoodsSectionSku>> comparators,Boolean expireDateSort) {
        if (itemSkuBatch == null) {
            return;
        }

        boolean hasProduct = Objects.equal(itemSkuBatch.getHasProduct(), CommonConstants.JUDGE_YES);
        boolean hasBatch = Objects.equal(itemSkuBatch.getHasBatch(), CommonConstants.JUDGE_YES);
        Integer batchRule = Optional.ofNullable(itemSkuBatch.getBatchRule()).orElse(ItemSkuBatch.ASC);




        //采退/下架/-1(临时先不过滤)不判断过期、临期
        if (!(AllocateType.PURCHASE_RETURN.getValue().equals(itemSkuBatch.getAllocateType())
                || AllocateType.DOWN_SHELF.getValue().equals(itemSkuBatch.getAllocateType())
                || Integer.valueOf(-1).equals(itemSkuBatch.getAllocateType()))) {
            removeAssosByProductDate(assos, itemSkuBatch);
        }

        if (hasProduct) {
            if (BooleanUtils.isNotFalse(expireDateSort)){
                //兜底计算到期日期，后面可以删除
                for (AssoGoodsSectionSku asso : assos) {
                    if (asso.getExpireDate() != null){
                        continue;
                    }
                    Date expireDate = new Date();
                    if (itemSkuBatch.getPeriodCast() == 0 || itemSkuBatch.getPeriodCast() == null || asso.getProductTime() == null) {
                        expireDate = asso.getProductTime();
                    } else {
                        expireDate = org.apache.commons.lang3.time.DateUtils.addDays(asso.getProductTime(), itemSkuBatch.getPeriodCast());
                    }
                    asso.setExpireDate(expireDate);
                }
                switch (batchRule) {
                    case ItemSkuBatch.ASC:
                        comparators.add(Comparator.comparing(AssoGoodsSectionSku::getExpireDate, Comparator.nullsLast(Comparator.naturalOrder())));
                        break;
                    case ItemSkuBatch.DESC:
                        comparators.add(Comparator.comparing(AssoGoodsSectionSku::getExpireDate, Comparator.nullsLast(Comparator.reverseOrder())));
                        break;
                }
            }
            switch (batchRule) {
                case ItemSkuBatch.ASC:
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getProductTime, Comparator.nullsLast(Comparator.naturalOrder())));
                    break;
                case ItemSkuBatch.DESC:
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getProductTime, Comparator.nullsLast(Comparator.reverseOrder())));
                    break;
            }
        }

        // 商品既开启生产日期又开启批次，按生产日期排序
        if (hasBatch) {
            switch (batchRule) {
                case ItemSkuBatch.ASC:
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getBatchNo, Comparator.nullsLast(Comparator.naturalOrder())));
                    break;
                case ItemSkuBatch.DESC:
                    comparators.add(Comparator.comparing(AssoGoodsSectionSku::getBatchNo, Comparator.nullsLast(Comparator.reverseOrder())));
                    break;
            }
        }
    }

    public static List<AssoGoodsSectionSku> sortByOrderAppointBatchRule(List<AssoGoodsSectionSku> assoSkus, List<ProductTimeBatchNo> productBatchNos) {
//        if (CollectionUtils.isEmpty(productBatchNos)) {
//            return assoSkus;
//        }
//
//        assoSkus.sort((sku1, sku2) -> equalSort(productBatchNos, sku1) ? -1 : 1);
//        return assoSkus;
        return filterByOrderAppointBatchRule(assoSkus, productBatchNos);
    }

    public static List<AssoGoodsSectionSku> filterByOrderAppointBatchRule(List<AssoGoodsSectionSku> assoSkus, List<ProductTimeBatchNo> productBatchNos) {
        if (CollectionUtils.isEmpty(productBatchNos)) {
            return assoSkus;
        }
        return assoSkus.stream().filter(sku -> equalSort(productBatchNos, sku)).collect(Collectors.toList());
    }

    public static boolean equalSort(List<ProductTimeBatchNo> productBatchNos, AssoGoodsSectionSku assoGoodsSectionSku) {
        return productBatchNos.stream().anyMatch(productBatchNo -> Objects.equal(WmsKeyUtils.buildDynamicExpireKey(assoGoodsSectionSku, productBatchNo),
                WmsKeyUtils.buildUpperKey(productBatchNo.getBatchNo(), WmsBatchProductUtils.productTimeShow(productBatchNo.getProductTime()), WmsBatchProductUtils.productTimeShow(productBatchNo.getExpireDate()))));
    }

    public static void removeAssosByProductDate(List<AssoGoodsSectionSku> assos, ItemSkuBatch itemSkuBatch) {
        if (itemSkuBatch == null || Objects.equal(itemSkuBatch.getHasProduct(), CommonConstants.JUDGE_NO) || CollectionUtils.isEmpty(assos)) {
            return;
        }

        List<AssoGoodsSectionSku> notSaleList = assos.stream().filter(a -> {
            if (a.getProductTime() == null) {
                return false;
            }
            DmjItem dmjItem = new DmjItem();
            dmjItem.setAllowSaleExpiring(itemSkuBatch.getAllowSaleExpiring());
            dmjItem.setHasProduct(itemSkuBatch.getHasProduct());
            dmjItem.setNearDate(itemSkuBatch.getNearDate());
            if (a.getExpireDate() != null) {
                //有到期日期的： 临保日期=到期日期-临保时长
                dmjItem.setPeriodCast(diffDate(a.getExpireDate(),a.getProductTime()));
            } else {
                dmjItem.setPeriodCast(itemSkuBatch.getPeriodCast());
            }

            return WmsBatchProductUtils.isNotAllowedSale(a, dmjItem);
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(notSaleList)) {
            for (List<AssoGoodsSectionSku> asso : Lists.partition(notSaleList, 50)) {
                List<Long> notSaleListIds = asso.stream().map(AssoGoodsSectionSku::getId).collect(Collectors.toList());
                logger.info(String.format("%s不允许被销售" , notSaleListIds));
            }
        }
        assos.removeAll(notSaleList);

    }

    /**
     * 按箱补货上架货位排序
     *   若商品在拣货区有多个关联货位，则推荐在架数最小的货位，若在架数相同，则按拣货路径取最靠前的货位。
     *
     * @param assos
     * @param stockRegionCodesOrder
     */
    public static void sortAssosForPickBox(List<AssoGoodsSectionSku> assos, WmsConfig wmsConfig, List<String> stockRegionCodesOrder) {
        if (CollectionUtils.isEmpty(assos) || assos.size() == 1) {
            return;
        }
        List<Comparator<AssoGoodsSectionSku>> comparators = Lists.newArrayList();
        comparators.add(Comparator.comparing(asso -> asso.getTotalNum() - asso.getLockNum() <= 0 ? null : asso.getTotalNum() - asso.getLockNum()));
        comparators.addAll(getGoodsSectionSortComparators(wmsConfig, stockRegionCodesOrder, AssoGoodsSectionSku::getGoodsSectionCode));
        assos.sort(Ordering.compound(comparators));
    }

    private static Integer acquireStockRegionSortType(AllocateGoodsParams params) {
        if (params == null) {
            return 0;
        }
        // 按商品生成波次
        if (params.getAllocateGoodsRule() != null && BooleanUtils.isTrue(params.getAllocateGoodsRule().isBackRegionPickFirst())) {
            return StockRegionTypeEnum.STOCK_REGION_TYPE_2.key;
        }
        return ObjectUtils.defaultIfNull(params.getStockRegionSortType(), 0);
    }

    /**
     * 备货区优先 ---早期配置,已废弃
     */
    @Deprecated
    public static Boolean backRegionPickFirst(Staff staff, WmsConfig wmsConfig) {
        if (!isNewWms(staff) || BooleanUtils.isNotTrue(wmsConfig.getOpenBackRegion()) || !Objects.equal(1, wmsConfig.getPickRegionType())) {
            return false;
        }
        return BooleanUtils.isTrue(wmsConfig.getBoolean(WmsConfigExtInfoEnum.BACK_REGION_PICK_FIRST.getKey()));
    }

    private static int getRangeIndex(Integer value, List<NumberRange<Integer>> ranges) {
        for (int i = 0; i < ranges.size(); i++) {
            if (containsRange(value, ranges.get(i))) {
                return i;
            }
        }

        return ranges.size();
    }

    private static boolean containsRange(Integer value, NumberRange<Integer> range) {
        Integer min = WaveUtils.toInteger(range.getMin());
        Integer max = WaveUtils.toInteger(range.getMax());
        return value != null && min != null && max != null && value >= min && value <= max;
    }

    /**
     * 得到自定义拣货路径的排序器
     *
     * @param wmsConfig 配置
     * @param routes 拣货路径
     * @return
     */
    private static Ordering<String> getGoodsSectionComparators(WmsConfig wmsConfig, List<String> routes) {
        return Ordering.compound(getGoodsSectionSortComparators(wmsConfig, routes, java.util.function.Function.identity()));
    }

    public static <T> List<Comparator<T>> getGoodsSectionSortComparators(WmsConfig wmsConfig, List<String> routes, java.util.function.Function<T, String> codeConvert) {
        //对于非自定义的货位,db存储不带"-",但是拣货路径可能是带"-"的,或者中文, 这时候需要去掉路径中的"-"
        List<String> routeList = handlePickRoutes(wmsConfig, routes);
        List<Comparator<T>> comparators;
        if (CollectionUtils.isNotEmpty(routes)) {
            comparators = Lists.newArrayListWithCapacity(2);
            // 如果配置了拣货路径，那么先根据拣货路径排序
            //默认
            if (isSectionCodeStoreDefault(wmsConfig)) {
                comparators.add(Comparator.comparingInt(code -> WmsUtils.getDefaultRouteCompareResult(codeConvert.apply(code), routeList)));
            } else {
                comparators.add(Comparator.comparingInt(code -> WmsUtils.getCustomRouteCompareResult(codeConvert.apply(code), routeList)));
            }
        } else {
            comparators = Lists.newArrayListWithExpectedSize(1);
        }
        //默认根据货位编码大小排序
        comparators.add((pre, next) -> WmsUtils.sortGoodsSectionCode(wmsConfig, codeConvert.apply(pre), codeConvert.apply(next)));
        return comparators;
    }

    private static int getCustomRouteCompareResult(String code, List<String> routes) {
        code = ObjectUtils.defaultIfNull(code, "");
        while(code.length() != 0) {
            int index = routes.indexOf(code);
            if(index != -1) {
                return index;
            }
            //能截取,截取"-"后面那一段
            if(code.contains("-")) {
                if(code.length() != 1) {
                    code = code.substring(0,code.lastIndexOf("-"));
                }else {
                    return Integer.MAX_VALUE;
                }
                //不带"-",只剩下库区位
            }else {
                return Integer.MAX_VALUE;
            }
        }
        return Integer.MAX_VALUE;
    }

    private static int getDefaultRouteCompareResult(String code, List<String> routes) {
        code = ObjectUtils.defaultIfNull(code, "");
        for(int i = 0; i < routes.size(); i++) {
            if(code.startsWith(routes.get(i))) {
                return i;
            }
        }
        return Integer.MAX_VALUE;
    }

    private static List<String> handlePickRoutes(WmsConfig wmsConfig, List<String> pickRoutes) {
        if (CollectionUtils.isEmpty(pickRoutes)) {
            return pickRoutes;
        }

        //对于非自定义的货位,db存储不带"-",但是拣货路径可能是带"-"的,或者中文, 这时候需要去掉路径中的"-"
        if (isSectionCodeStoreDefault(wmsConfig)) {
            return pickRoutes.stream().map(WmsUtils::getSimplifyGsCode).collect(Collectors.toList());
        }
        return pickRoutes;
    }

    public static <T> List<T> subListByPage(List<T> list, Page page) {
        if (CollectionUtils.isEmpty(list) || page == null) {
            return list;
        }

        int start = page.getStartRow();
        if (start >= list.size()) {
            return Collections.emptyList();
        }
        int end = Math.min(start + page.getPageSize(), list.size());
        return new ArrayList<>(list.subList(start, end));
    }

    /**
     * 验证字符串长度
     * @param remark
     * @param maxLength
     * @return
     */
    public static Boolean validateRemarkLength(String remark, Integer maxLength) {
        if (StringUtils.isNotEmpty(remark) && DataUtils.getZeroIfDefaultI(maxLength) > 0) {
            if (remark.length() > maxLength) return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 将null转化未空
     * @param str
     * @return
     */
    public static String getNullToEmpty(String str) {
        if (str == null) return "";
        return str;
    }

    /**
     * 校验配货规则区间
     *
     * @param allocateRule
     */
    public static void checkGoodsStockNumRanges(WmsAllocateRule allocateRule) {
        if (Objects.equal(allocateRule.getAllocateRuleType(), WmsAllocateRule.AllocateRuleType.ALLOCATE_NUM_RANGE)) {
            List<NumberRange<Integer>> goodsStockNumRanges = allocateRule.getGoodsStockNumRanges();
            Assert.notEmpty(goodsStockNumRanges, "请设置货位库存数量区间排序顺序！");

            for (NumberRange<Integer> goodsStockNumRange : goodsStockNumRanges) {
                Integer min = WaveUtils.toInteger(goodsStockNumRange.getMin());
                Integer max = WaveUtils.toInteger(goodsStockNumRange.getMax());

                Assert.isTrue(min != null && min > 0, "下限值不能为空！");
                Assert.isTrue(max != null && max > 0, "上限值不能为空！");
                Assert.isTrue(min <= max, "上限值不能小于下限值！");
            }

            List<NumberRange<Integer>> sorted = Ordering.from(Comparator.<NumberRange<Integer>>comparingInt(WaveUtils::toInteger)).sortedCopy(goodsStockNumRanges);
            Integer preMax = null;
            for (NumberRange<Integer> goodsStockNumRange : sorted) {
                if (preMax != null) {
                    Assert.isTrue(WaveUtils.toInteger(goodsStockNumRange.getMin()) > preMax, "货位库存区间范围有交叉，请重新设置！");
                }
                preMax = WaveUtils.toInteger(goodsStockNumRange.getMax());
            }
        }
    }

    /**
     * 两页查询工具类
     *
     * @param page 当前页数
     * @param firstCnt 第一个查询总数
     * @param secondCnt 第二个查询总数
     * @param firstFunc 第一个查询函数（返回查询结果）
     * @param secondFunc 第二个查询函数（返回查询结果）
     * @param <T>
     * @return
     */
    public static <T> List<T> queryTwoPageList(Page page, long firstCnt, long secondCnt, java.util.function.Function<Page, List<T>> firstFunc, java.util.function.Function<Page, List<T>> secondFunc) {
        //如果总数小于起始值，返回空列表
        int startRow = page.getStartRow();
        if (firstCnt + secondCnt <= startRow) {
            return Collections.emptyList();
        }

        List<T> list;
        int pageSize = page.getPageSize();
        if (firstCnt > startRow) {
            //如果第一个查询总数大于起始值说明需要进行第一个查询
            list = firstFunc.apply(page);
            //如果第一查询总数小于查询页最大值，且第二个查询总数大于0，说明要将不足的用第二个查询补充
            if (firstCnt < startRow + pageSize && secondCnt > 0L) {
                Page newPage = new Page();
                newPage.setPageNo(1);
                newPage.setPageSize(pageSize - list.size());
                List<T> secondList = secondFunc.apply(newPage);
                if (CollectionUtils.isNotEmpty(secondList)) {
                    list.addAll(secondList);
                }
            }
        } else {
            //如果第一个查询的总数小于等于起始值，直接进行第二个查询
            Page newPage = new Page();
            newPage.setPageSize(pageSize);
            newPage.setStartRow(startRow - (int) firstCnt);
            list = secondFunc.apply(newPage);
        }
        return list;
    }

    private static Integer getInOutWarehouseType(StockInOutRecordType opType, Boolean increase) {
        Integer type = InOutWarehouseType.DEFAULT.getIndex();
        if (opType == null || increase == null) return type;
        switch (opType) {
            case STOCK_PRODUCT:
                type = increase ? InOutWarehouseType.PROCESS_IN.getIndex() : InOutWarehouseType.PROCESS_OUT.getIndex();
                break;
            case MOVE_SECTION:
                type = increase ? InOutWarehouseType.WSS_IN.getIndex() : InOutWarehouseType.WSS_OUT.getIndex();
                break;
            default: break;
        }
        return type;
    }

    /**
     * 设置出入库记录的：进出仓类型
     * @param stockInOutRecords
     */
    public static void setInOutWarehouseTypes(List<StockInOutRecord> stockInOutRecords) {
        if (CollectionUtils.isEmpty(stockInOutRecords)) return;

        try {
            for (StockInOutRecord record : stockInOutRecords) {
                Integer finalInOutWarehouseType = InOutWarehouseType.DEFAULT.getIndex();
                InOutWarehouseType inOutWarehouseType = StockInOutRecordType.getInOutWarehouseType(record.getType());
                if (inOutWarehouseType != null) {
                    finalInOutWarehouseType = (Objects.equal(inOutWarehouseType.getIndex(), InOutWarehouseType.NO_RELATIVE.getIndex()))
                            ? getInOutWarehouseType(StockInOutRecordType.getInOutRecordType(record.getType()), DataUtils.checkLongNotEmpty(record.getStockChange()))
                            : inOutWarehouseType.getIndex();
                }
                record.setInOutWarehouseType(finalInOutWarehouseType);
            }
        } catch (Exception e) {

        }
    }

    /**
     * 设置暂存区出入库记录的：进出仓类型
     * @param records
     */
    public static void setWssInOutWarehouseTypes(List<WorkingStorageSectionInOutRecord> records) {
        if (CollectionUtils.isEmpty(records)) return;

        try {
            for (WorkingStorageSectionInOutRecord record : records) {
                Integer finalInOutWarehouseType = InOutWarehouseType.DEFAULT.getIndex();
                StockChangeBusiType busiType = record.getBusiType();
                if (busiType != null) {
                    finalInOutWarehouseType = (Objects.equal(busiType.getInOutWarehouseType().getIndex(), InOutWarehouseType.NO_RELATIVE.getIndex()))
                            ? getInOutWarehouseType(busiType.getInOutRecordType(), record.getIncNum() != null && record.getIncNum() > 0)
                            : busiType.getInOutWarehouseType().getIndex();
                }
                record.setInOutWarehouseType(finalInOutWarehouseType);
            }
        } catch (Exception e) {

        }
    }

    /**
     * 设置货位的库区属性
     * @param stockRegionType
     * @param goodsSections
     */
    public static void setStockRegionType(Integer stockRegionType, List<GoodsSection> goodsSections) {
        if (stockRegionType == null
                || CollectionUtils.isEmpty(goodsSections)
                || !Lists.newArrayList(1, 2).contains(stockRegionType)) {
            return;
        }
        goodsSections.forEach(g -> g.setStockRegionType(stockRegionType));
    }

    public static boolean isBackGsCode(String gsCode, Map<String, Long> gsCodeGsIdMap, List<Long> backRegionIds) {
        if (StringUtils.isEmpty(gsCode) || MapUtils.isEmpty(gsCodeGsIdMap) || CollectionUtils.isEmpty(backRegionIds)) {
            return true;
        }
        Long gsId = gsCodeGsIdMap.get(gsCode);
        if (gsId == null || !DataUtils.checkLongNotEmpty(gsId)) {
            return true;
        }
        return backRegionIds.contains(gsId);
    }

    public static List<AllocateGoodsRecord> convertReRecords(List<AllocateGoodsRecord> records, List<AllocateGoodsRecord> lackRecords) {
        if (CollectionUtils.isNotEmpty(lackRecords)) {
            Map<Long, AllocateGoodsRecord> recordMaps = lackRecords.stream().collect(Collectors.toMap(AllocateGoodsRecord::getId, a -> a, (v1, v2) -> v1));
            for (AllocateGoodsRecord record : records) {
                AllocateGoodsRecord lackRecord = recordMaps.get(record.getId());
                if (lackRecord == null || lackRecord.getLackNum() == null) {
                    record.setPickedNum(record.getAllocatedNum());
                    continue;
                }
                record.setPickedNum(record.getAllocatedNum() - lackRecord.getLackNum());
            }
        }
        return records;
    }

    public static Boolean includedInTotalStockWss(Staff staff, WorkingStorageSection.TypeEnum typeEnum) {
        if (staff == null || staff.getConf() == null
                || staff.getConf().getConfAttrInfo() == null || typeEnum == null) {
            return false;
        }
        Object includedInTotalStockWss = staff.getConf().getConfAttrInfo().get("includedInTotalStockWss");
        if (includedInTotalStockWss == null || StringUtils.isEmpty(includedInTotalStockWss.toString())) {
            return false;
        }
        List <String> types = Lists.newArrayList(includedInTotalStockWss.toString().split(","));
        return types.contains(typeEnum.getCode());
    }

    /**
     * 生成sql合并的key
     * @param fields
     * @param obj
     * @return
     */
    public static String createSqlMergeKey(Field[] fields, Object obj) {
        StringBuilder key = new StringBuilder();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                key.append(field.get(obj) == null ? "0" : "1");
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return key.toString();
    }

    /**
     * 批量插入 e.g PickMapTradeDao#batchInsert
     */
    public static <T> Integer batchMergeInsert(List<T> details, Consumer<Map> consumer) {
        if (CollectionUtils.isEmpty(details)) {
            return 0;
        }
        Field[] declaredFields = details.get(0).getClass().getDeclaredFields();
        Map<String, List<T>> mergeSqlMaps = details.stream().collect(Collectors.groupingBy(r -> WmsUtils.createSqlMergeKey(declaredFields, r)));
        for (Map.Entry<String, List<T>> entry : mergeSqlMaps.entrySet()) {
            Map<String, Object> map = Maps.newHashMap();
            List<T> values = entry.getValue();
            List<List<T>> partitions = Lists.partition(values, 2000);
            for (List<T> partition : partitions) {
                map.put("column", partition.get(0));
                map.put("values", partition);
                consumer.accept(map);
            }
        }
        return details.size();
    }


    /**
     * 配货数量类型---只是查询可配数时使用
     * @param boxAllocate  0: 货位无箱   1:货位有箱   2:货位无箱 + 有箱
     * @return
     */
    public static Integer getAllocateNumTypeByBoxAllocate(Integer boxAllocate) {
        return ObjectUtils.defaultIfNull(boxAllocate, 0);
    }

    /**
     * 配货记录容器类型---配货全程使用
     * @param boxAllocate  0: 货位无箱   1:货位有箱   2:货位无箱 + 有箱
     * @return
     */
    public static List<Integer> getRecordContainerTypesByBoxAllocate(Integer boxAllocate) {
        List<Integer> containerTypes = null;
        if (Integer.valueOf(2).equals(boxAllocate)) {
            //未拣配货记录 containerType 都是0, 已拣配货记录  containerType 是 0/2 --货位上拣的/箱里面拣的
            containerTypes = Lists.newArrayList(ContainerTypeEnum.GOODS_SECTION.getValue(), ContainerTypeEnum.BOX.getValue());
        } else if (Integer.valueOf(1).equals(boxAllocate)) {
            //未拣配货记录 containerType 都是0, 已拣配货记录  containerType 都是2 --箱里面拣的
            containerTypes = Lists.newArrayList(ContainerTypeEnum.GOODS_SECTION.getValue(), ContainerTypeEnum.BOX.getValue());
        } else {
            //不处理, 根据之前的 containerType 和 wssType等查询
        }
        return containerTypes;
    }

    /**
     * 仓储库存容器类型
     * @param boxAllocate  0: 货位无箱   1:货位有箱   2:货位无箱 + 有箱
     * @return
     */
    public static List<Integer> getAssoContainerTypesByBoxAllocate(Integer boxAllocate) {
        List<Integer> containerTypes = null;
        if (Integer.valueOf(2).equals(boxAllocate)) {
            //货位上 有箱 无箱  都拣
            containerTypes = Lists.newArrayList(ContainerTypeEnum.GOODS_SECTION.getValue(), ContainerTypeEnum.BOX.getValue());
        } else if (Integer.valueOf(1).equals(boxAllocate)) {
            //只拣货位上有箱的
            containerTypes = Lists.newArrayList(ContainerTypeEnum.BOX.getValue());
        } else {
            //只拣货位上无箱的
            containerTypes = Lists.newArrayList(ContainerTypeEnum.GOODS_SECTION.getValue());
        }
        return containerTypes;
    }

    /**
     * 按商品货位批次效期维度聚合货位上库存
     * @param records
     * @return
     */
    public static List<AssoGoodsSectionSku> groupAssos(List<AssoGoodsSectionSku> assos) {
        if (CollectionUtils.isEmpty(assos)) {
            return assos;
        }
        Map<String, AssoGoodsSectionSku> assoMap = assos.stream().map(v -> {
            if (StringUtils.isNotEmpty(v.getBoxCode())) {
                v.setBoxTotalNum(v.getTotalNum());
            } else {
                v.setBoxTotalNum(0);
            }
            return v;
        }).collect(Collectors.toMap(WmsKeyUtils :: buildGoodsItemBatchNoExpireKey, java.util.function.Function.identity(), (k1,k2) -> {
            k1.setTotalNum(k1.getTotalNum() + k2.getTotalNum());
            k1.setBoxTotalNum(k1.getBoxTotalNum() + k2.getBoxTotalNum());
            return k1;
        }));
        assos = Lists.newArrayList(assoMap.values());
        return assos;
    }

    /**
     * 按商品货位维度聚合货位上库存
     */
    public static List<AssoGoodsSectionSku> groupAssosByBatches(List<AssoGoodsSectionSku> assos) {
        if (CollectionUtils.isEmpty(assos)) {
            return assos;
        }
        Map<Long, List<AssoGoodsSectionSku>> sectionAssoMap = assos.stream().collect(Collectors.groupingBy(AssoGoodsSectionSku::getGoodsSectionId, LinkedHashMap::new, Collectors.toList()));
        List<AssoGoodsSectionSku> aggregatedAssos = new ArrayList<>();
        for (Entry<Long, List<AssoGoodsSectionSku>> entry : sectionAssoMap.entrySet()) {
            List<AssoGoodsSectionSku> sameGsIdAssos = entry.getValue();
            if (CollectionUtils.isEmpty(sameGsIdAssos)) {
                continue;
            }
            int totalNum = sameGsIdAssos.stream().mapToInt(AssoGoodsSectionSku::getTotalNum).sum();
            List<BatchInfoPojo> batchInfoPojos = sameGsIdAssos.stream().filter(asso -> StringUtils.isNotBlank(asso.getBatchNo()) || java.util.Objects.nonNull(asso.getProductTime()))
                    .map(asso -> new BatchInfoPojo(asso.getBatchNo(), Optional.ofNullable(asso.getProductTime()).map(Date::getTime).orElse(null), asso.getTotalNum())).collect(Collectors.toList());
            AssoGoodsSectionSku aggregatedAsso = sameGsIdAssos.get(0);
            aggregatedAsso.setTotalNum(totalNum);
            aggregatedAsso.setBatchAndProduct(batchInfoPojos);
            aggregatedAssos.add(aggregatedAsso);
        }
        return aggregatedAssos;
    }

    /**
     * 按商品货位维度聚合配货记录--暂时用不上,原先业务配货记录已经做了聚合
     * @param records
     * @return
     */
    public static List<AllocateGoodsRecord> groupRecords(List<AllocateGoodsRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return records;
        }
        Map<String, AllocateGoodsRecord> recordMap = records.stream().collect(Collectors.toMap(WmsUtils :: buildRecordItemKey, java.util.function.Function.identity(), (k1,k2) -> {
            k1.setAllocatedNum(k1.getAllocatedNum() + k2.getAllocatedNum());
            k1.setPickedNum(k1.getPickedNum() + k2.getPickedNum());
            return k1;
        }));
        records = Lists.newArrayList(recordMap.values());
        return records;
    }

    public static String buildSectionItemKey(AssoGoodsSectionSku asso) {
        return Joiner.on("_").join(asso.getWarehouseId(), asso.getGoodsSectionId(), asso.getSysItemId(), ObjectUtils.defaultIfNull(asso.getSysSkuId(), 0));
    }

    public static String buildRecordItemKey(AllocateGoodsRecord record) {
        return Joiner.on("_").join(record.getWarehouseId(), record.getGoodsSectionId(), record.getSysItemId(), ObjectUtils.defaultIfNull(record.getSysSkuId(), 0));
    }

    public static String buildUnShelveOrderDetailKey(UnShelveOrderDetail detail, Long warehouseId, Long goodsSectionId) {
        return Joiner.on("_").join(warehouseId, goodsSectionId, detail.getSysItemId(), ObjectUtils.defaultIfNull(detail.getSysSkuId(), 0));
    }

    public static<T extends FastInOutStock> String buildFastInOutStockItemKey(T fastInOutStock) {
        return fastInOutStock.getSysItemId() + "_" + DataUtils.getZeroIfDefault(fastInOutStock.getSysSkuId());
    }

    /**
     * 是否开启库存不一致校验任务
     * @return
     */
    public static boolean openStockCheckTask() {
        return DiamondConfig.SWITCH_ON.equals(ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getWms().getOpenWmsStockCheckTask());
    }

    /**
     * 是否开启仓储订单校验任务
     * @return
     */
    public static boolean openWmsTradeCheckTask() {
        return DiamondConfig.SWITCH_ON.equals(ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getWms().getOpenWmsTradeCheckTask());
    }

    /**
     * 是否开启门店仓库白名单
     * @return
     */
    public static boolean openStoreWarehouseWhiteList(Staff staff) {
        String openStoreWarehouseWhiteList = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getWms().getOpenStoreWarehouseWhiteList();
        if (StringUtils.isBlank(openStoreWarehouseWhiteList)) {
            return false;
        }
        return ArrayUtils.toLongList(openStoreWarehouseWhiteList).stream().anyMatch(v -> staff.getCompanyId().equals(v));
    }

    public static boolean filterStoreWarehouse(Staff staff, Warehouse warehouse) {
        if (WmsUtils.openStoreWarehouseWhiteList(staff) && Warehouse.isStoreWarehouse(warehouse)) {
            return true;
        }
        return false;
    }


    public static <K> Map<K, Integer> getGoodsSectionSkuTotalAllocateNumMap(List<AllocateGoodsRecord> records, Function<AllocateGoodsRecord, K> keyFunc) {
        return records.stream().collect(Collectors.toMap(record -> keyFunc.apply(record), record -> ObjectUtils.defaultIfNull(record.getAllocatedNum(), 0), (v1, v2) -> v1 + v2));
    }


    public static <K> Map<K, AllocateGoodsRecord> getGoodsSectionSkuAllocateNumMap(List<AllocateGoodsRecord> records, Function<AllocateGoodsRecord, K> keyFunc) {
        return records.stream().collect(Collectors.toMap(record -> keyFunc.apply(record), java.util.function.Function.identity(), (v1, v2) -> {
            v1.setAllocatedNum(ObjectUtils.defaultIfNull(v1.getAllocatedNum(), 0) + ObjectUtils.defaultIfNull(v2.getAllocatedNum(), 0));
            v1.setBoxAllocatedNum(ObjectUtils.defaultIfNull(v1.getBoxAllocatedNum(), 0) + ObjectUtils.defaultIfNull(v2.getBoxAllocatedNum(), 0));
            return v1;
        }));
    }

    /**
     * 根据配货记录获取对应货位商品的已配数
     * 逻辑如下：
     * ①若盘点类型的已配数≥正常的已配数，则已配数=盘点类型的已配数；
     * ②若盘点类型的已配数＜正常的已配数，则已配数=其他类型的已配数之和；
     * 新加逻辑【新已配数量】=Max(盘点占有数量，生成盘点前的所有订单已配数之和）+盘点生成后的订单已配库存数。
     * @param records 配货记录
     * @return Map<K, Integer> key，已配数
     */
    public static Map<String, AllocateGoodsRecord> calculateAllocateNumMap(List<AllocateGoodsRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }
        // 按时间排序,空值放最后;
        records.sort(Comparator.comparing(AllocateGoodsRecord :: getCreated, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(record ->  record.getAllocateType(), Comparator.nullsLast(Comparator.naturalOrder())));
        // 每个货位商品 盘点的已配数最大值
        Map<String, AllocateGoodsRecord> maxInventoryNumMaps = Maps.newHashMap();
        // 盘点前 每个货位商品已配数
        Map<String, AllocateGoodsRecord> beforeInventoryMaps = Maps.newHashMap();
        // 盘点后 每个货位商品已配数
        Map<String, AllocateGoodsRecord> afterInventoryAllocateMaps = Maps.newHashMap();
        // 是否已经开始盘点
        Map<String, Boolean> beforeInventoryFlagMap = Maps.newHashMap();
        for (AllocateGoodsRecord record : records) {
            // key:货位id_商品id_规格id
            // String key = WmsKeyUtils.buildAllocateGoodsRecordItemKey(record);
            String key = WmsKeyUtils.buildAllocateGoodsRecordItemBatchNoExpireKey(record);

            // 获取配货记录的已配数
            Integer allocateNum = DataUtils.getZeroIfDefaultI(record.getAllocatedNum());
            Integer boxAllocateNum = DataUtils.getZeroIfDefaultI(record.getBoxAllocatedNum());
            // 如果配货记录类型是盘点类型
            if (AllocateType.PICK_INVENTORY.getValue().equals(record.getAllocateType())) {
                AllocateGoodsRecord maxInventoryNumRecord = maxInventoryNumMaps.get(key);
                beforeInventoryFlagMap.put(key, false);
                // 取盘点的取最大值.盘点单目前没有箱配货数
                if (allocateNum > DataUtils.getZeroIfDefaultI(maxInventoryNumRecord != null ? maxInventoryNumRecord.getAllocatedNum() : 0)) {
                    if (maxInventoryNumRecord == null) {
                        maxInventoryNumRecord = new AllocateGoodsRecord();
                        maxInventoryNumRecord.setAllocatedNum(0);
                        maxInventoryNumRecord.setBoxAllocatedNum(0);
                        maxInventoryNumRecord.setGoodsSectionId(record.getGoodsSectionId());
                        maxInventoryNumRecord.setSysItemId(record.getSysItemId());
                        maxInventoryNumRecord.setSysSkuId(record.getSysSkuId());
                        maxInventoryNumMaps.put(key, maxInventoryNumRecord);
                    }
                    // 即时盘点取最大值
                    maxInventoryNumRecord.setAllocatedNum(allocateNum);
                }
            } else { // 其他配货类型，求和
                boolean beforeInventory = BooleanUtils.isNotFalse(beforeInventoryFlagMap.get(key));
                Map<String, AllocateGoodsRecord> allocateMaps = beforeInventory ? beforeInventoryMaps : afterInventoryAllocateMaps;
                AllocateGoodsRecord allocateGoodsRecord = allocateMaps.get(key);
                if (allocateGoodsRecord == null) {
                    allocateGoodsRecord = new AllocateGoodsRecord();
                    allocateGoodsRecord.setAllocatedNum(0);
                    allocateGoodsRecord.setBoxAllocatedNum(0);
                    allocateGoodsRecord.setGoodsSectionId(record.getGoodsSectionId());
                    allocateGoodsRecord.setSysItemId(record.getSysItemId());
                    allocateGoodsRecord.setSysSkuId(record.getSysSkuId());
                    allocateMaps.put(key, allocateGoodsRecord);
                }
                allocateGoodsRecord.setAllocatedNum(allocateGoodsRecord.getAllocatedNum() + allocateNum);
                allocateGoodsRecord.setBoxAllocatedNum(allocateGoodsRecord.getBoxAllocatedNum() + boxAllocateNum);
            }
        }

        if (MapUtils.isEmpty(maxInventoryNumMaps)) {
            return beforeInventoryMaps;
        }
        // 遍历有盘点的货位商品，比较盘点最大值和其他配货类型的和，取大的
        for (Map.Entry<String, AllocateGoodsRecord> entrySet : maxInventoryNumMaps.entrySet()) {
            AllocateGoodsRecord allocateGoodsRecord = beforeInventoryMaps.get(entrySet.getKey());
            if (entrySet.getValue().getAllocatedNum() > DataUtils.getZeroIfDefaultI(allocateGoodsRecord != null ? allocateGoodsRecord.getAllocatedNum() : 0)) {
                beforeInventoryMaps.put(entrySet.getKey(), entrySet.getValue());
            }
        }
        // 若货位中存在未完成的即时盘点，那么在之后上架了新增了可配库存后，如果再有订单需要对该货位的可配库存进行配货，
        // 则【新已配数量】=Max(盘点占有数量，生成盘点前的所有订单已配数之和）+盘点生成后的订单已配库存数。
        for (Map.Entry<String, AllocateGoodsRecord> entrySet : afterInventoryAllocateMaps.entrySet()) {
            AllocateGoodsRecord beforeAllocateGoodsRecord = beforeInventoryMaps.get(entrySet.getKey());
            if (entrySet.getValue() == null) {
                continue;
            }
            if (beforeAllocateGoodsRecord == null) {
                beforeInventoryMaps.put(entrySet.getKey(), entrySet.getValue());
            } else {
                beforeAllocateGoodsRecord.setAllocatedNum(DataUtils.getZeroIfDefaultI(beforeAllocateGoodsRecord.getAllocatedNum()) + DataUtils.getZeroIfDefaultI(entrySet.getValue().getAllocatedNum()));
                beforeAllocateGoodsRecord.setBoxAllocatedNum(DataUtils.getZeroIfDefaultI(beforeAllocateGoodsRecord.getBoxAllocatedNum()) + DataUtils.getZeroIfDefaultI(entrySet.getValue().getBoxAllocatedNum()));
            }
        }

        return beforeInventoryMaps;
    }

    public static void convertToStockInOutRecord(BoxStock boxStock, DmjItem dmjItem, DmjItem mainItem) {
        boxStock.setSysItemId(dmjItem.getSysItemId());
        boxStock.setSysSkuId(0L);
        boxStock.setTitle(dmjItem.getTitle());
        boxStock.setOuterId(dmjItem.getOuterId());
        ItemSupplierBridge itemBestSupplierBridge = dmjItem.getBestItemSupplierBridge();
        boxStock.setBestItemSupplierBridge(itemBestSupplierBridge);
        boxStock.setItemSupplierBridgeList(dmjItem.getItemSupplierBridgeList());
        boxStock.setHasBatch(dmjItem.getHasBatch());
        boxStock.setHasProduct(dmjItem.getHasProduct());
        if (dmjItem instanceof DmjSku) {
            DmjSku sku = (DmjSku) dmjItem;
            boxStock.setSysSkuId(sku.getSysSkuId());
            boxStock.setPropertiesName(sku.getPropertiesName());
            boxStock.setPicPath(sku.getSkuPicPath());
        }
        if (mainItem != null) {
            boxStock.setPicPath(mainItem.getPicPath());
            boxStock.setTitle(mainItem.getTitle());
        }
    }

    public static Integer getSumTotalAllocateNum(List<AllocateGoodsRecord> records) {
        AllocateGoodsRecord allocateNumRecord = getSumAllocateNum(records);
        return allocateNumRecord == null ? 0 : allocateNumRecord.getAllocatedNum();
    }

    /**
     * 获取货位商品的已配数总和
     * @param records
     * @return 已配数
     */
    public static AllocateGoodsRecord getSumAllocateNum(List<AllocateGoodsRecord> records) {
        AllocateGoodsRecord  allocateNumRecord = new AllocateGoodsRecord();
        allocateNumRecord.setAllocatedNum(0);
        allocateNumRecord.setBoxAllocatedNum(0);
        for (AllocateGoodsRecord  value : records) {
            allocateNumRecord.setAllocatedNum(allocateNumRecord.getAllocatedNum() + value.getAllocatedNum());
            allocateNumRecord.setBoxAllocatedNum(allocateNumRecord.getBoxAllocatedNum() + value.getBoxAllocatedNum());
        }
        return allocateNumRecord;
    }


    /**
     * 获取水洗唛json
     *
     * @param itemWashLabelVos
     * @return
     */
    public static String getWashLabelJson(List<ItemWashLabelVo> itemWashLabelVos) {
        if (CollectionUtils.isEmpty(itemWashLabelVos)) {
            return null;
        }
        JSONArray array = new JSONArray();
        for (ItemWashLabelVo itemWashLabelVo : itemWashLabelVos) {
            JSONObject json = new JSONObject();
            json.put("washLabelId", itemWashLabelVo.getWashLabelId());
            json.put("washLabelName", itemWashLabelVo.getWashLabelName());
            json.put("sourceType", itemWashLabelVo.getSourceType());
            json.put("image", itemWashLabelVo.getImage());
            array.add(json);
        }
        return array.toString();
    }

    /**
     * 根据order构造WmsChangeAffect
     */
    public static WmsChangeAffect buildWmsChangeAffect(Order order) {
        WmsChangeAffect affect = new WmsChangeAffect();
        affect.setBusiId(order.getSid());
        affect.setSubBusiId(order.getId());
        affect.setSysItemId(order.getItemSysId());
        affect.setSysSkuId(DataUtils.getZeroIfDefault(order.getSkuSysId()));
        affect.setOuterId(order.getOuterId());
        affect.setWarehouseId(order.getWarehouseId());
        affect.setTotalNum(order.getNum());
        affect.setWaveId(-1L);
        return affect;
    }

    private static WmsChangeAffect build4UniqueCode(WaveUniqueCode code, Integer dimension) {
        WmsChangeAffect affect = new WmsChangeAffect();
        affect.setSysItemId(code.getSysItemId());
        affect.setSysSkuId(code.getSysSkuId());
        affect.setWarehouseId(code.getWarehouseId());
        affect.setTotalNum(1);
        affect.setSubBusiId(DataUtils.checkLongNotEmpty(code.getOrderId()) ? code.getOrderId() : code.getId());
        /**
         * 设置成订单唯一码
         * @see AllocateGoodsRecord#getWorkOrderId()
         */
        if (Objects.equal(dimension, CommonConstants.JUDGE_YES)) {
            affect.setBusiCode(DataUtils.checkLongNotEmpty(code.getSid()) ? code.getSid().toString() : code.getUniqueCode());
        } else {
            affect.setBusiCode(code.getUniqueCode());
        }
        affect.setBusiId(DataUtils.checkLongNotEmpty(code.getSid()) ? code.getSid() : code.getId());
        affect.setQualityType(Boolean.TRUE);
        return affect;
    }

    /**
     * 构建WmsChangeAffect
     * @param dimension 维度 0 code,1 sid
     */
    public static List<WmsChangeAffect> build4UniqueCodes(List<WaveUniqueCode> codes, Integer dimension) {
        List<WmsChangeAffect> affects = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            affects.add(build4UniqueCode(code, dimension));
        }
        return affects;
    }

    public static Boolean isOpenGoodsSectionExport(Staff staff) {
        return staff.getConf() != null && WmsConstants.EXPORT_GOODS_SECTION.equals(staff.getConf().getOpenGoodsSectionExport());
    }


    /**
     * 盘点单的排序从单据维度转变到货位维度,历史数据与新数据的sortNo设置分开处理
     *
     * @param detailList 货位维度的所有盘点详情
     * @return
     */
    public static Long getSortNoBySectionDimension(List<InventoryDetail> detailList) {
        // 货位上商品等于1 或者 大于1时,不同商品的sortNo相等时 可用非历史数据处理sortNo值
        if (detailList.size() == 1 || (detailList.size() > 1 && detailList.get(0).getSortNo() == detailList.get(1).getSortNo())) {
            return detailList.get(0).getSortNo();
        } else {
            Long maxGoodsSectionSortNo = 0L;
            for (InventoryDetail detail : detailList) {
                maxGoodsSectionSortNo = maxGoodsSectionSortNo > detail.getSortNo() ? maxGoodsSectionSortNo : detail.getSortNo();
            }
            return maxGoodsSectionSortNo;
        }
    }
    /**
     * 根据暂存区code匹配inlocation
     *
     * @param workingStorageSectionType
     * @return
     */
    public static Integer getInLocationByCode(String workingStorageSectionType) {
        if (StringUtils.isEmpty(workingStorageSectionType)){
            return WorkingStorageLocationEnum.InLocation.TYPE_1.getCode();
        }
        for (WorkingStorageLocationEnum.InLocation inLocation : WorkingStorageLocationEnum.InLocation.values()) {
            if (inLocation.getName().equals(workingStorageSectionType)) {
                return inLocation.getCode();
            }
        }
        return WorkingStorageLocationEnum.InLocation.TYPE_1.getCode();
    }

    public static String getInLocationNameByLocationCode(Integer inLocationCode) {
        if (inLocationCode == null){
            return WorkingStorageLocationEnum.InLocation.TYPE_1.getName();
        }
        for (WorkingStorageLocationEnum.InLocation inLocation : WorkingStorageLocationEnum.InLocation.values()) {
            if (inLocation.getCode().equals(inLocationCode)) {
                return inLocation.getName();
            }
        }
        return WorkingStorageLocationEnum.InLocation.TYPE_1.getName();
    }

    /**
     * 根据暂存区code匹配outlocation
     *
     * @param workingStorageSectionType
     * @return
     */
    public static Integer getOutLocationByCode(String workingStorageSectionType) {
        if (StringUtils.isEmpty(workingStorageSectionType)){
            return WorkingStorageLocationEnum.OutLocation.TYPE_1.getCode();
        }
        for (WorkingStorageLocationEnum.OutLocation outLocation : WorkingStorageLocationEnum.OutLocation.values()) {
            if (outLocation.getName().equals(workingStorageSectionType)) {
                return outLocation.getCode();
            }
        }
        return WorkingStorageLocationEnum.OutLocation.TYPE_1.getCode();
    }

    /**
     * 根据暂存区code匹配assoGoodsSectionSku location
     *
     * @param workingStorageSectionType
     * @return
     */
    public static Integer getAssoGoodsSectionLocationByCode(String workingStorageSectionType) {
        if (StringUtils.isEmpty(workingStorageSectionType)){
            return WorkingStorageLocationEnum.AssoGoodsSectionLocation.TYPE_2.getCode();
        }
        for (WorkingStorageLocationEnum.AssoGoodsSectionLocation assoLocation : WorkingStorageLocationEnum.AssoGoodsSectionLocation.values()) {
            if (assoLocation.getName().equals(workingStorageSectionType)) {
                return assoLocation.getCode();
            }
        }
        return WorkingStorageLocationEnum.AssoGoodsSectionLocation.TYPE_2.getCode();
    }

    public static Integer getCheckSheetLocationCodeByWssType(String workingStorageSectionType) {
        if (StringUtils.isEmpty(workingStorageSectionType)){
            return WorkingStorageLocationEnum.CheckSheetLocation.TYPE_1.getCode();
        }
        for (WorkingStorageLocationEnum.CheckSheetLocation checkSheetLocation : WorkingStorageLocationEnum.CheckSheetLocation.values()) {
            if (checkSheetLocation.getWssType().getCode().equals(workingStorageSectionType)) {
                return checkSheetLocation.getCode();
            }
        }
        return WorkingStorageLocationEnum.CheckSheetLocation.TYPE_1.getCode();
    }

    public static WorkingStorageSection.TypeEnum getCheckSheetLocationWssTypeByCode(Integer locationCode) {
        if (locationCode == null){
            return WorkingStorageLocationEnum.CheckSheetLocation.TYPE_1.getWssType();
        }
        WorkingStorageLocationEnum.CheckSheetLocation[] values = WorkingStorageLocationEnum.CheckSheetLocation.values();
        for (WorkingStorageLocationEnum.CheckSheetLocation loation : values) {
            if (loation.getCode().equals(locationCode)) {
                return loation.getWssType();
            }
        }
        return WorkingStorageLocationEnum.CheckSheetLocation.TYPE_1.getWssType();
    }


    public static Integer getStockProductLocationCodeByWssType(String workingStorageSectionType) {
        if (StringUtils.isEmpty(workingStorageSectionType)){
            return WorkingStorageLocationEnum.StockProductLocation.TYPE_4.getCode();
        }
        WorkingStorageLocationEnum.StockProductLocation[] values = WorkingStorageLocationEnum.StockProductLocation.values();
        for (WorkingStorageLocationEnum.StockProductLocation loation : values) {
            if (loation.getWssType().getCode().equals(workingStorageSectionType)) {
                return loation.getCode();
            }
        }
        return WorkingStorageLocationEnum.StockProductLocation.TYPE_4.getCode();
    }

    public static WorkingStorageSection.TypeEnum getStockProductLocationByWssType(String workingStorageSectionType) {
        if (StringUtils.isEmpty(workingStorageSectionType)){
            return WorkingStorageLocationEnum.StockProductLocation.TYPE_4.getWssType();
        }
        WorkingStorageLocationEnum.StockProductLocation[] values = WorkingStorageLocationEnum.StockProductLocation.values();
        for (WorkingStorageLocationEnum.StockProductLocation loation : values) {
            if (loation.getWssType().getCode().equals(workingStorageSectionType)) {
                return loation.getWssType();
            }
        }
        return WorkingStorageLocationEnum.StockProductLocation.TYPE_4.getWssType();
    }

    public static WorkingStorageSection.TypeEnum getStockProductLocationWssTypeByCode(Integer locationCode) {
        if (locationCode == null){
            return WorkingStorageLocationEnum.StockProductLocation.TYPE_4.getWssType();
        }
        WorkingStorageLocationEnum.StockProductLocation[] values = WorkingStorageLocationEnum.StockProductLocation.values();
        for (WorkingStorageLocationEnum.StockProductLocation loation : values) {
            if (loation.getCode().equals(locationCode)) {
                return loation.getWssType();
            }
        }
        return WorkingStorageLocationEnum.StockProductLocation.TYPE_4.getWssType();
    }

    public static WorkingStorageSection.TypeEnum getStockProductLocationByWssTypeDefaultNull(Integer locationCode) {
        if (locationCode == null){
            return null;
        }
        WorkingStorageLocationEnum.StockProductLocation[] values = WorkingStorageLocationEnum.StockProductLocation.values();
        for (WorkingStorageLocationEnum.StockProductLocation loation : values) {
            if (loation.getCode().equals(locationCode)) {
                return loation.getWssType();
            }
        }
        return null;
    }

    public static Boolean isOpenSupportGsCapacity(WmsConfig config) {
        Integer judge = config.getInteger(WmsConfigExtInfoEnum.SUPPORT_SECTION_CAPACITY.getKey());
        return judge == CommonConstants.JUDGE_YES;
    }
    /**
     * 重新配货--在架数重新配货模式
     */
    public static boolean isReAllocateByTotalNumMode(Integer reAllocateMode) {
        // 在架数重新配货模式
        boolean reallocateByTotalNumMode = Objects.equal(reAllocateMode, CommonConstants.JUDGE_YES);
        if (reallocateByTotalNumMode) {
            Logs.ifDebug("重新分配货位按在架数分配");
        }
        return reallocateByTotalNumMode;
    }



    public static Boolean autoReceiveTransfer(Staff staff, WmsConfig wmsConfig) {
        return wmsConfig != null && wmsConfig.getAutoReceiveTransfer() != null && wmsConfig.getAutoReceiveTransfer() == 1;
    }

    /**
     * 生成波次的调拨出库单，部分拣货后不拆分创建新的调拨出库单
     * false 不需要拆分
     */
    public static Boolean outWithWaveIsSplit(WmsConfig wmsConfig,Long waveId){
        boolean createWave = DataUtils.checkLongNotEmpty(waveId);
        return !(Objects.equal(wmsConfig.getInteger(WmsConfigExtInfoEnum.SPLIT_ORDER_CONF.getKey()), 1) && createWave);
    }


    public static boolean getErpBufferOneByOneCompanyIds(Long companyId) {
        String companyIds = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getWms().getErpBufferOneByOne();
        if (StringUtils.isBlank(companyIds)) {
            return false;
        }
        List<Long> ids = DataUtils.ids2List(companyIds);
        if (ids.contains(companyId)) {
            return true;
        }
        return false;
    }


    public static boolean isOpenGsCodeRealDisplayZh(Long companyId, String env) {
        try {
            String whiteGsCodeDisplayZHCompanyIds = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getWms().getGsCodeDisplayZHProfiles();
            if (StringUtils.isNotEmpty(whiteGsCodeDisplayZHCompanyIds)) {
                List<String> openProfiles = Arrays.stream(whiteGsCodeDisplayZHCompanyIds.split(",")).map(String::trim).collect(Collectors.toList());
                if (openProfiles.contains(env)) {
                    return true;
                }
            }
            String gsCodeDisplayZHCompanyIds = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getWms().getGsCodeDisplayZHCompanyIds();
            return Arrays.stream(gsCodeDisplayZHCompanyIds.split(",")).map(String::trim).anyMatch(value -> StringUtils.equalsIgnoreCase(value, companyId.toString()));
        } catch (Exception e) {
            logger.error("获取自定义货位显示中文的公司白名单配置失败", e);
        }
        return false;
    }


    /**
     * 根据配货记录获取批次
     */
    public static String getBatchNoByAllocateRecord(List<AllocateGoodsRecord> allocateGoodsRecords, boolean findFirst) {
        if (findFirst) {
            return allocateGoodsRecords.stream()
                    .filter(data -> StringUtils.isNotEmpty(data.getBatchNo()))
                    .map(AllocateGoodsRecord::getBatchNo)
                    .findFirst().orElse("");
        }
        return allocateGoodsRecords.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getBatchNo()))
                .collect(Collectors.toMap(AllocateGoodsRecord::getBatchNo, AllocateGoodsRecord::getAllocatedNum, Integer::sum))
                .entrySet().stream().map(e -> e.getKey() + "*" + e.getValue()).collect(Collectors.joining(","));
    }

    /**
     * 根据配货记录获取生产日期
     */
    public static String getProductDateByAllocateRecord(List<AllocateGoodsRecord> records, boolean findFirst) {
        if (findFirst) {
            return records.stream()
                    .filter(data -> data.getProductTime() != null)
                    .map(data -> DateUtils.date2Str(data.getProductTime()))
                    .findFirst().orElse("");
        }
        return records.stream()
                .filter(data -> data.getProductTime() != null)
                .collect(Collectors.toMap(AllocateGoodsRecord::getProductTime, AllocateGoodsRecord::getAllocatedNum, Integer::sum))
                .entrySet().stream().map(e -> DateUtils.date2Str(e.getKey()) + "*" + e.getValue()).collect(Collectors.joining(","));
    }

    /**
     * 是否需要查询批次生产日期
     */
    public static boolean isFillBatchNoProductDate(DmjItem item) {
        if (item == null) {
            return false;
        }
        return Objects.equal(item.getHasProduct(), CommonConstants.JUDGE_YES) || Objects.equal(item.getHasBatch(), CommonConstants.JUDGE_YES);
    }

    /**
     * 是否暂存区配货
     */
    public static boolean isWorkStorageSectionAllocate(Staff staff, WaveConfig waveConfig, WmsConfig wmsConfig) {
        boolean workStorageSectionAllocate = waveConfig != null && Objects.equal(waveConfig.getNegativeOneBlindScanAllocate(), CommonConstants.JUDGE_YES);
        boolean unPickedConsumePickStorage = wmsConfig != null && Objects.equal(wmsConfig.getConsumeStockType(), WmsConfig.CONSUME_STOCK_TYPE_PICK_STORAGE);
        boolean noAllocateGoodsConsumePickStorage = wmsConfig != null && Objects.equal(wmsConfig.getNoAllocateGoodsConsume(), CommonConstants.JUDGE_NO);
        return workStorageSectionAllocate && unPickedConsumePickStorage && noAllocateGoodsConsumePickStorage;
    }

    public static List<WmsChangeAffect> buildAffects4WorkStorageSectionAllocate(List<Trade> trades, Long warehouseId, Long waveId) {
        if (CollectionUtils.isEmpty(trades)) {
            return Lists.newArrayList();
        }
        warehouseId = warehouseId == null ? trades.get(0).getWarehouseId() : warehouseId;
        List<WmsChangeAffect> affects = Lists.newArrayList();
        List<Order> orders = WaveUtils.getOrdersForWave(TradeUtils.getOrders4Trade(trades), true);
        Map<Long, Long> sid2WaveIdMap = trades.stream().collect(toMap(Trade::getSid, Trade::getWaveId));
        for (Order order : orders) {
            WmsChangeAffect affect = new WmsChangeAffect();
            affect.setBusiId(order.getSid());
            affect.setSubBusiId(order.getId());
            affect.setSysItemId(order.getItemSysId());
            affect.setSysSkuId(com.raycloud.dmj.utils.wms.DataUtils.getZeroIfDefault(order.getSkuSysId()));
            affect.setOuterId(order.getSysOuterId());
            affect.setWarehouseId(warehouseId);
            affect.setTotalNum(order.getNum());
            affect.setWaveId(waveId != null ? waveId : sid2WaveIdMap.get(order.getSid()));
            affects.add(affect);
        }
        return affects;
    }

    public static List<String> toStringList(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return ids.stream().map(String::valueOf).collect(Collectors.toList());
    }

    public static List<Long> toAllocateGoodsRecordKeys(List<AllocateGoodsRecord> records, Function<AllocateGoodsRecord, Long> func) {
        return records.stream().map(func::apply).distinct().collect(Collectors.toList());
    }

    public static List<Long> toLongList(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return ids.stream().map(Long::parseLong).collect(Collectors.toList());
    }

    public static List<Long> toLongList(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return ids.stream().map(Long::parseLong).collect(Collectors.toList());
    }


    public static List<String> toStringList(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return ids.stream().map(String::valueOf).collect(Collectors.toList());
    }

    public static List<String> toStringList(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return new ArrayList<>();
        }
        return toStringList(Lists.newArrayList(ids));
    }

    public static List<ProductTimeBatchNo> getProductTimeBatchNoList(List<OrderStockProduct> orderStockProducts){
        if (CollectionUtils.isEmpty(orderStockProducts)) {
            return Lists.newArrayList();
        }
        List<ProductTimeBatchNo> productTimeBatchNos = Lists.newArrayList();
        for (OrderStockProduct orderStockProduct : orderStockProducts) {
            ProductTimeBatchNo productTimeBatchNo = new ProductTimeBatchNo();
            productTimeBatchNo.setProductTime(orderStockProduct.getProductionDate());
            productTimeBatchNo.setExpireDate(orderStockProduct.getExpireDate());
            productTimeBatchNo.setBatchNo(orderStockProduct.getBatchNo());
            productTimeBatchNos.add(productTimeBatchNo);
        }
        return productTimeBatchNos;
    }

    /**
     * 合并需要覆写的OrderStockProduct数据
     * @param orderStockProducts   已存在的orderStockProducts数据
     * @param allocateGoodsRecords 新生成的配货记录
     */
    public static List<OrderStockProduct> getOrderStockProductsByAllocateGoodsRecords(List<OrderStockProduct> orderStockProducts, List<AllocateGoodsRecord> allocateGoodsRecords) {
        List<String> batchNoOrderIds = toStringList(Optional.ofNullable(allocateGoodsRecords).orElse(Lists.newArrayList()).stream().filter(r -> StringUtils.isNotEmpty(r.getBatchNo()) || r.getProductTime() != null).map(AllocateGoodsRecord::getOrderId).collect(Collectors.toList()));
        List<String> batchNoOrderStockProductsOrderIds = Optional.ofNullable(orderStockProducts).orElse(Lists.newArrayList()).stream().map(OrderStockProduct::getSubOrderNumber).collect(Collectors.toList());
        batchNoOrderIds.addAll(batchNoOrderStockProductsOrderIds);
        if (CollectionUtils.isEmpty(batchNoOrderIds)) {
            return Lists.newArrayList();
        }
        List<OrderStockProduct> orderStockProductList = Lists.newArrayList();
        for (String orderIds : batchNoOrderIds) {
            orderStockProductList.add(OrderStockProduct.builder().subOrderNumber(orderIds).build());
        }
        return orderStockProductList;
    }

    public static List<OrderStockProduct> getOrderStockProduct(List<GoodsSectionOrderRecord> records){
        Map<String, OrderStockProduct> orderProductMap = records.stream().map(record -> {
            OrderStockProduct orderStockProduct = new OrderStockProduct();
            orderStockProduct.setOrderNumber(record.getSid());
            orderStockProduct.setSubOrderNumber(record.getOrderId().toString());
            orderStockProduct.setSysItemId(record.getSysItemId());
            orderStockProduct.setSysSkuId(record.getSysSkuId());
            orderStockProduct.setWarehouseId(record.getWarehouseId());
            orderStockProduct.setBatchNo(record.getBatchNo());
            orderStockProduct.setProductionDate(record.getProductTime());
            orderStockProduct.setExpireDate(record.getExpireDate());
            orderStockProduct.setLockNum(record.getGetNum().longValue());
            orderStockProduct.setStatus(OrderStockProductStatusEnum.STATUS_1.getStatus());
            return orderStockProduct;
        }).collect(Collectors.toMap(WmsKeyUtils::buildOrderBatchKey, java.util.function.Function.identity(),(v1, v2) -> {
            v1.setLockNum(v1.getLockNum() + v2.getLockNum());
            return v1;
        }));
        return new ArrayList<>(orderProductMap.values());
    }

    public static String buildLogGoodsSectionOrderRecord(List<GoodsSectionOrderRecord> goodsSectionOrderRecords){
        if (CollectionUtils.isEmpty(goodsSectionOrderRecords)) {
            return StringUtils.EMPTY;
        }
        StringBuffer sb = new StringBuffer();
        for (GoodsSectionOrderRecord goodsSectionOrderRecord : goodsSectionOrderRecords) {
            sb.append(String.format("id:%s,orderIds:%s,batchNo:%s,productTime:%s,getNum:%s;",goodsSectionOrderRecord.getId(),goodsSectionOrderRecord.getOrderId(),goodsSectionOrderRecord.getBatchNo(),goodsSectionOrderRecord.getProductTime(),goodsSectionOrderRecord.getGetNum()));
        }
        return sb.toString();
    }

    /**
     * 根据原始集合copy一份back集合
     * @param goodsSkus
     * @return
     */
    public static List<AssoGoodsSectionSku> copyBackAssoGoodsSectionSkuList(List<AssoGoodsSectionSku> goodsSkus){
        List<AssoGoodsSectionSku> assoGoodsSectionSkuBackList = Lists.newArrayListWithCapacity(goodsSkus.size());
        assoGoodsSectionSkuBackList.addAll(goodsSkus);
        return assoGoodsSectionSkuBackList;
    }


    /**
     * 忽略过期临期的波次类型
     * @param allocateType
     * @return
     */
    public static boolean ignoreExpire(AllocateType allocateType){
        if (allocateType == null) {
            return false;
        }
        //下架单、采退单、其他出库单
        if (AllocateType.FREE_DOWN_SHELF.equals(allocateType) || AllocateType.APPOINT_DOWN_SHELF.equals(allocateType) || AllocateType.DOWN_SHELF.equals(allocateType) || AllocateType.PURCHASE_RETURN.equals(allocateType) || AllocateType.OTHER_CUSTOM_OUT.equals(allocateType) || AllocateType.ALLOCATE_OUT.equals(allocateType)) {
            return true;
        }
        return false;
    }

    /**
     * 忽略过期临期的波次类型
     * @param pickingType
     * @return
     */
    public static boolean ignoreExpire(PickingType pickingType){
        if (pickingType == null) {
            return false;
        }
        //下架单、采退单、其他出库单
        if (PickingType.isDownShelfType(pickingType) || PickingType.PURCHASE_RETURN.equals(pickingType) || PickingType.OTHER_CUSTOM_OUT.equals(pickingType) || PickingType.ALLOCATE_OUT.equals(pickingType)) {
            return true;
        }
        return false;
    }


    public static List<Page> pageAlgorithm(List<Integer> countList, Page page) {
        if (CollectionUtils.isEmpty(countList)) {
            return null;
        }
        int count = 0, before = 0, end = page.getStartRow() + page.getPageSize(), size = page.getPageSize();
        List<Page> result = new ArrayList<>(countList.size());
        for (int i = 0; i < countList.size(); i++) {
            count += countList.get(i);
            Page data = new Page();
            if (size <= 0 || count <= page.getStartRow()) {
                data.setStartRow(0);
                data.setOffsetRow(0);
            } else {
                data.setStartRow(page.getStartRow() > before ? page.getStartRow() - before : 0);
                data.setOffsetRow(count < end ? countList.get(i) - getDefaultValue(data.getStartRow()) : size);
            }
            size -= data.getOffsetRow();
            result.add(data);
            before += countList.get(i);
        }
        return result;
    }

    public static Integer getDefaultValue(Integer value) {
        return value == null ? 0 : value;
    }

    public static Long getDefaultValueL(Long value) {
        return value == null ? 0L : value;
    }



    /**
     * 改造商品库存单位信息转换公式集合
     * 应用场景：前端下拉选择的交互需要 基本单位+辅助单位
     * 转换公式排序：商品有默认的辅助单位，基本单位index为1 ，反之index为0
     *
     * @param list
     */
    public static void transformItemUnit(List<ItemUnitVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (ItemUnitVO vo : list) {
            List<ConversionFormulaVO> origins = Optional.ofNullable(vo.getConversionFormulas()).orElse(Lists.newArrayList());
            ConversionFormulaVO conversionFormulaVO = new ConversionFormulaVO();
            conversionFormulaVO.setAssistUnit(vo.getBaseUnit());
            conversionFormulaVO.setAssistUnitId(vo.getBaseUnitId());
            conversionFormulaVO.setConversionFactor(Double.valueOf(CommonConstants.JUDGE_YES));
            conversionFormulaVO.setIsDefault(0);
            if (CollectionUtils.isNotEmpty(origins) && Objects.equal(origins.get(0).getIsDefault(), CommonConstants.JUDGE_YES)) {
                origins.add(1, conversionFormulaVO);
            } else {
                origins.add(0, conversionFormulaVO);
            }
            vo.setConversionFormulas(origins);
        }
    }

    /**
     * 判断数字是否是小数
     * @param num
     * @return
     */
    public static boolean checkPointNum(Integer num) {
        return num % 1 == 0;
    }
    public static void fillNotAllocateTypes(Staff staff, QueryAllocateGoodsRecordParams params, AllocateType allocateType, WmsConfig wmsConfig, boolean reAllocateByTotalNumMode) {
        String allocatePriorityOrder = wmsConfig.getString(WmsConfigExtInfoEnum.ALLOCATE_PRIORITY.getKey());
        Logs.ifDebug(String.format("===>fillNotAllocateTypes params：allocateType %s, allocatePriorityOrder: %s, reAllocateByTotalNumMode %s", allocateType, allocatePriorityOrder, reAllocateByTotalNumMode));
        if (allocateType == null) {
            return;
        }
        // 单据类型是否支持抢占配货
        if (!Lists.newArrayList(AllocateType.ALLOCATE_OUT.getValue(), AllocateType.TRADE.getValue())
                .contains(allocateType.getValue())) {
            return;
        }
        List<Integer> notAllocateTypes = Arrays.stream(AllocateType.values()).map(AllocateType::getValue).collect(Collectors.toList());
        List<Long> priorityOrderList = StringUtils.isEmpty(allocatePriorityOrder) ? Lists.newArrayList() : ArrayUtils.toLongList(allocatePriorityOrder);
        // 选择【抢占配货】，执行重新配货时，同优先级的的单据类型之间先拣选的可以先占用库存，高优先级单据可占用低优先级库存
        if (reAllocateByTotalNumMode) {
            if (priorityOrderList.contains(Long.valueOf(allocateType.getValue()))) {
                // 高优先级单据执行配货时使用在架数进行配货
                params.setNotAllocateTypes(notAllocateTypes);
            } else {
                // 低优先级单据使用在架数排除高优先级单据占用库存进行配货
                params.setNotAllocateTypes(notAllocateTypes.stream()
                        .filter(t -> !priorityOrderList.contains(Long.valueOf(t))).collect(Collectors.toList()));
            }
        } else {
            //【非抢占配货】，执行重新配货时，同优先级的的单据类型之间不相互占用，高优先级单据可占用低优先级库存
            // 高优先级单据执行配货时使用在架数排除其他高优先级单据已占用的库存进行配货
            if (priorityOrderList.contains(Long.valueOf(allocateType.getValue()))) {
                params.setNotAllocateTypes(notAllocateTypes.stream()
                        .filter(t -> !priorityOrderList.contains(Long.valueOf(t))).collect(Collectors.toList()));
            }
        }
        Logs.ifDebug("===>配货可抢占类型：" + params.getNotAllocateTypes());
    }

    public static <T> Predicate<T> distinctByKey(java.util.function.Function<? super T, ?> keyExtractor) {
        ConcurrentHashMap<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }


    /**
     * @see com.raycloud.dmj.services.check.PurchaseSupplierProxy#matchBestItemSupplier(Staff, Long, List)
     */
    public static Map<String, ItemSupplierBridge> getBestItemSupplier(Long matchSupplierId, List<ItemSupplierBridge> itemSupplierBridges) {
        if (CollectionUtils.isEmpty(itemSupplierBridges)) {
            return Maps.newHashMap();
        }
        Map<String, ItemSupplierBridge> bestItemSupplierMap = Maps.newHashMap();
        for (ItemSupplierBridge itemSupplierBridge : itemSupplierBridges) {
            if (itemSupplierBridge.getSupplierPurchasePrice() == null) {
                itemSupplierBridge.setSupplierPurchasePrice(0d);
            }
            String itemKey = WmsKeyUtils.buildItemKey(itemSupplierBridge);
            ItemSupplierBridge bestItemSupplier = bestItemSupplierMap.get(itemKey);
            if (bestItemSupplier == null) {
                bestItemSupplierMap.put(itemKey, itemSupplierBridge);
            } else {
                boolean isBest;
                if (matchSupplierId != null) {
                    isBest = matchSupplierId.equals(itemSupplierBridge.getSupplierId()) ? matchSupplierId.equals(itemSupplierBridge.getSupplierId()) : (itemSupplierBridge.getSupplierPurchasePrice().compareTo(bestItemSupplier.getSupplierPurchasePrice()) < 0);
                } else {
                    isBest = itemSupplierBridge.getSupplierPurchasePrice().compareTo(bestItemSupplier.getSupplierPurchasePrice()) < 0;
                }
                if (isBest) {
                    bestItemSupplierMap.put(itemKey, itemSupplierBridge);
                }
            }
        }
        return bestItemSupplierMap;
    }


    public static <T extends AllocateTaskDetail> void resetExpireDateAllocateTaskDetail(T detail) {
        if (detail == null || detail.getProductTime() == null ||
                (detail.getExpireDate() == null && detail.getPeriodCast() == null)) {
            return;
        }
        // reset expireDate、periodCast
        Date expireDate = ObjectUtils.defaultIfNull(detail.getExpireDate(), DateUtils.addDate(detail.getProductTime(), ObjectUtils.defaultIfNull(detail.getPeriodCast(), 0)));
        Integer periodCast = DateUtils.diffDate(expireDate, detail.getProductTime());
        detail.setExpireDate(expireDate);
        detail.setPeriodCast(periodCast);
    }

    /**
     * @return false 为有问题
     */
    public static boolean checkGsParamsByConfig(Boolean allowDuplicateGsCodes, Long warehouseId, String gsCode, Long gsId) {
        return gsId != null || (StringUtils.isNotBlank(gsCode)  && (BooleanUtils.isNotTrue(allowDuplicateGsCodes) || warehouseId != null));
    }
    public static String getItemLogInfo(String outerId, String batchNo, Date productTime, Date expireDate) {
        if (StringUtils.isBlank(outerId)) {
            return "商家编码为空";
        }
        StringJoiner str = new StringJoiner(",");
        str.add("商家编码：" + outerId);

        Optional.ofNullable(batchNo)
                .filter(StringUtils::isNotBlank)
                .ifPresent(bn -> str.add("批次:" + bn));

        Optional.ofNullable(productTime)
                .ifPresent(pt -> str.add("生产日期:" + WmsBatchProductUtils.productTimeShow(pt)));

        Optional.ofNullable(expireDate)
                .ifPresent(ed -> str.add("到期日期:" + WmsBatchProductUtils.productTimeShow(ed)));
        return str.toString();
    }

    /**
     * 日期相减
     *
     * @param date
     *            日期
     * @param date1
     *            日期
     * @return 返回date - date1 相减后的日期
     */
    public static int diffDate(Date date, Date date1) {
        return (int) ((getMillis(date) - getMillis(date1)) / (24 * 3600 * 1000));
    }

    /**
     * @param date  日期
     * @return 返回毫秒
     */
    public static long getMillis(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.getTimeInMillis();
    }


    public static String getSimpleAffectLog(List<WmsChangeAffect> affects) {
        JSONArray res = new JSONArray();
        for (WmsChangeAffect affect : affects) {
            JSONObject object = new JSONObject();
            object.put("itemKey", WmsKeyUtils.buildUpperKey(affect.getWarehouseId(), affect.getItemKeyWithBatchAndProduct(), affect.getQualityType()));
            object.put("outerId", affect.getOuterId());
            object.put("totalNum", affect.getTotalNum());
            object.put("busiCode", affect.getBusiCode());
            object.put("busiId_subBusiId", WmsKeyUtils.buildUpperKey(affect.getBusiId(),affect.getSubBusiId()));
            object.put("platformOrderNumber", affect.getPlatformOrderNumber());
            object.put("busiType", affect.getBusiType());
            object.put("inc", affect.getInc());
            object.put("checkType", affect.getCheckType());
            object.put("outLocation", affect.getOutLocation());
            object.put("gsId_goodsSectionCode", WmsKeyUtils.buildUpperKey(affect.getGoodsSectionId(), affect.getGoodsSectionCode()));
            object.put("waveId_waveStatus", WmsKeyUtils.buildUpperKey(affect.getWaveId(),affect.getWaveStatus()));
            res.add(object);
        }

        return JSON.toJSONString(res);
    }

    public static boolean isConvertibleToDouble(String str) {
        if (str == null) {
            return false;
        }
        try {
            Double.parseDouble(str.trim()); // 处理前后空格
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static void saveBigLog(Staff staff, Logger loggerProxy, String logStr) {
        saveBigLog(staff, loggerProxy, logStr, null);
    }
    public static void saveBigLog(Staff staff, Logger loggerProxy, String logStr, Integer cycleTimes) {
        if (null == loggerProxy || StringUtils.isBlank(logStr)) {
            return;
        }
        final int MAX_LOG_LENGTH = 1015;
        int strLength = logStr.length();
        Random random = new Random();
        String logHead = random.ints(2, 'A', 'Z' + 1).mapToObj(i -> String.valueOf((char) i)).collect(Collectors.joining());
        cycleTimes = ObjectUtils.defaultIfNull(cycleTimes, 30);
        int totalCount = (strLength + MAX_LOG_LENGTH - 1) / MAX_LOG_LENGTH;
        for (int i = 0; i < strLength; i += MAX_LOG_LENGTH) {
            String subLog = logStr.substring(i, Math.min(i + MAX_LOG_LENGTH, strLength));
            int currentIndex = i / MAX_LOG_LENGTH + 1;
            if (currentIndex > cycleTimes) {
                break;
            }
            loggerProxy.debug(LogHelper.buildLog(staff, String.format("%s%s/%s:%s", logHead, currentIndex, totalCount, subLog)));
        }
    }

    public static List<List<String>> getPatternMatch(String input){
        // 提取中括号内的内容
        Pattern bracketPattern = Pattern.compile("【([^】]+)】");
        Matcher bracketMatcher = bracketPattern.matcher(input);

        List<List<String>> result = new ArrayList<>();

        while(bracketMatcher.find()) {
            String groupContent = bracketMatcher.group(1);
            List<String> alphanumericList = new ArrayList<>();

            // 提取字母数字组合
            Pattern alphanumericPattern = Pattern.compile("([a-zA-Z0-9]+)");
            Matcher alphanumericMatcher = alphanumericPattern.matcher(groupContent);

            while(alphanumericMatcher.find()) {
                alphanumericList.add(alphanumericMatcher.group());
            }

            result.add(alphanumericList);
        }
        return result;
    }
}

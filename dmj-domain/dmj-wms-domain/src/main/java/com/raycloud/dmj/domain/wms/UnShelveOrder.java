package com.raycloud.dmj.domain.wms;

import com.raycloud.dmj.domain.BaseModel;
import com.raycloud.dmj.domain.account.DbInfo;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.wave.WaveDistributionStatus;
import com.raycloud.dmj.domain.wms.enums.UnShelveStatusEnum;
import com.raycloud.dmj.domain.wms.enums.UnShelveTypeEnum;
import com.raycloud.erp.db.model.Column;
import com.raycloud.erp.db.model.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.sql.Types;
import java.util.Date;
import java.util.List;

/**
 * 下架单
 *
 * @Author: lit
 * @Date: 2021/4/12 1:47 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "unshelve_order")
public class UnShelveOrder extends BaseModel implements Serializable {

    private static final long serialVersionUID = -436858732953953322L;

    /**
     * 主键
     */
    @Column(name = "id", type = Types.BIGINT, key = true)
    private Long id;

    /**
     * 下架单单号
     */
    @Column(name = "code", type = Types.VARCHAR)
    private String code;

    /**
     * 单据日期
     */
    @Column(name = "bill_date", type = Types.DATE)
    private Date billDate;

    /**
     * 下架仓库Id
     */
    @Column(name = "warehouse_id", type = Types.BIGINT)
    private Long warehouseId;

    /**
     * 下架仓库名称
     */
    @Column(name = "warehouse_name", type = Types.VARCHAR)
    private String warehouseName;

    /**
     * 下架单状态
     *
     * @see UnShelveStatusEnum
     */
    @Column(name = "status", type = Types.INTEGER)
    private Integer status;

    /**
     * 下架类型
     *
     * @see UnShelveTypeEnum
     * 1:波次 2:普通 3：库存 4：自由货位 5 指定货位 默认是2
     */
    @Column(name = "type", type = Types.INTEGER)
    private Integer type;

    /**
     * 目标库存区域，下架到
     *
     * @see WorkingStorageSection.TypeEnum
     */
    @Column(name = "target_stock_type", type = Types.VARCHAR)
    private String targetStockType;

    /**
     * 总下架数量
     */
    @Column(name = "num", type = Types.INTEGER)
    private Long num;

    /**
     * 待下架数
     */
    @Column(name = "wait_quantity", type = Types.INTEGER)
    private Long waitQuantity;

    /**
     * 波次号ID
     */
    @Column(name = "wave_id", type = Types.BIGINT)
    private Long waveId;

    /**
     * 波次状态
     *
     * @see WaveDistributionStatus
     */
    @Column(name = "wave_status", type = Types.INTEGER)
    private Integer waveStatus;

    /**
     * 备注
     */
    @Column(name = "remark", type = Types.VARCHAR)
    private String remark;

    /**
     * 下架人
     */
    @Column(name = "unshelver_id", type = Types.BIGINT)
    private Long unShelverId;

    /**
     * 下架人名称
     */
    @Column(name = "unshelver_name", type = Types.VARCHAR)
    private String unShelverName;

    /**
     * 下架时间
     */
    @Column(name = "unshelve_date", type = Types.DATE)
    private Date unShelveDate;

    /**
     * 逻辑删除字段,1:正常，0:已删除
     */
    @Column(name = "enable_status", type = Types.TINYINT)
    private Integer enableStatus;

    /**
     * 是否良品,1良品，0次品
     */
    @Column(name = "is_good_products", type = Types.TINYINT)
    private Integer isGoodProducts;
    /**
     * 外部单号
     */
    @Column(name = "source_id", type = Types.VARCHAR)
    private String sourceId;

    /**
     * 货主ID 字符串拼接而成
     */
    private String shipperId;

    private String shipper;

    private String shipperIds;

    /**
     * 下架单明细列表
     */
    private List<UnShelveOrderDetail> details;

    /**
     * 库区编码
     */
    private String stockRegionCodes;

    /**
     * 商家编码，前端查询参数
     */
    private String outerId;

    /**
     * 下架单状态描述，仅前端显示
     */
    private String statusName;

    /**
     * 波次状态描述，仅前端显示
     */
    private String waveStatusName;

    /**
     * 下架单ID列表，前端传递，导出用
     */
    private List<Long> unShelveIds;

    /**
     * 开始时间，前端传递，查询用
     */
    private Long timeStart;

    /**
     * 结束时间，前端传递，查询用
     */
    private Long timeEnd;

    /**
     * 实际下架数，不落库
     */
    private Long actualUnShelveNum;

    /**
     * 实际良品数
     */
    private Long goodActualUnShelveNum;

    /**
     * 实际次品数
     */
    private Long badActualUnShelveNum;

    /**
     * 覆盖类型 0:货位数量覆盖 1:可配数覆盖,不落库
     */
    private Integer fillType;

    /**
     * 仓库ID列表，前端传递，查询用
     */
    private String warehouseIdArr;

    /**
     * 状态列表，前端传递，查询用
     */
    private String statusArr;


    /**
     * 创建人ID列表，前端传递，查询用
     */
    private String createStaffIdArr;
    /**
     * 起始时间 格式:yyyy-MM-dd HH:mm:ss
     */
    private Date startModified;

    /**
     * 结束时间 格式:yyyy-MM-dd HH:mm:ss
     */
    private Date endModified;

    /**
     * 货位编码
     */
    private String goodsSectionCode;

}

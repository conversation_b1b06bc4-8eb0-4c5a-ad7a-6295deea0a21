package com.raycloud.dmj.domain.wms.product;

import com.raycloud.dmj.domain.utils.DMJItemUtils;
import com.raycloud.erp.db.model.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 仓内库存加工原料单
 *
 * <AUTHOR>
 * @Date 2019-09-02
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class StockProductMaterial extends Model {
    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * enableStatus
     */
    private Integer enableStatus;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 加工单id
     */
    private Long productOrderId;
    /**
     * 仓库id
     */
    private Long warehouseId;
    /**
     * 系统item id
     */
    private Long sysItemId;
    /**
     * 系统sku id
     */
    private Long sysSkuId;
    /**
     * 原料数量
     */
    private Integer num;
    /**
     * 印花排版生成采购单，新增数量
     */
    private Integer incNum;
    /**
     * 分配拣选数量
     */
    private Integer allocateNum;

    /**
     * 出库数量
     */
    private Integer stockOutNum;
    /**
     * 备注
     */
    private String remark;
    /**
     * 货位上架记录,json存储
     * [{
     *     "sectionId":xx,
     *     "sectionCode":xx,
     *     "num":xx
     * }]
     */
    private String goodsSectionRoute;
    /**
     * 商品的标题
     */
    private String title;
    /**
     * 商品的商家编码
     */
    private String itemOuterId;
    /**
     * 商品图片URL
     */
    private String picPath;
    /**
     * 规格名称
     */
    private String propertiesName;
    /**
     * sku的商家编码
     */
    private String outerId;

    /**
     * 剩余数量
     */
    private Integer remainQty;



    /**
     * 父原料id
     */
    private Long combineId;

    /**
     * 原料对应的成品关系,落库
     * [
     *      origSysItemId:1
     *      origSysSkuId:0
     *      qty:1
     * ]
     */
    private String productRelation;

    private List<ProductRelation> productRelationArr;

    /**
     * 加工单单号（加工单导入 key）
     */
    private String stockProductOrderCode;


    /**
     * 原料成本价
     */
    private String materialCost;

    /**
     * 批次
     */
    private String batchNo;

    /**
     * 效期
     */
    private Date productTime;
    /**
     * 效期
     */
    private Date expireDate;

    /**
     * 实际上架数
     */
    private Integer actualUpNum;

    /**
     * 登记消耗数量
     */
    private Integer checkNum;
    /**
     * 单位
     */
    private Long unitId;

    /**
     * 箱数
     */
    private Long numOfBox;

    /**
     * 箱规
     */
    private Long boxNum;

    /**
     * 散装数量
     */
    private Long bulkNum;

    /**
     * 原料单价
     */
    private String materialPrice;

    /**
     * 原料成本
     */
    private String materialSingleCost;

    /**
     * 原料加工总额
     */
    private String productAmount;

    /**
     * 原料加工费
     */
    private String productPrice;

    /**
     * 改码原料前的原料商家编码
     */
    private String combineOuterId;

    public String getMaterialCost() {
        return materialCost;
    }

    public void setMaterialCost(String materialCost) {
        this.materialCost = materialCost;
    }

    /**
     * 关联的原料 信息
     */
    @Data
    public static class ProductRelation{

        private Long stockProductGoodId;

        private Long origSysItemId;

        private Long origSysSkuId;

        private String itemOuterId;
        private String outerId;

        /**
         * 原料数量
         */
        private Integer qty;
    }

    /**
     * @return id 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus enableStatus
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus enableStatus
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return companyId 公司id
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * @param companyId 公司id
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * @return productOrderId 加工单id
     */
    public Long getProductOrderId() {
        return productOrderId;
    }

    /**
     * @param productOrderId 加工单id
     */
    public void setProductOrderId(Long productOrderId) {
        this.productOrderId = productOrderId;
    }

    /**
     * @return warehouseId 仓库id
     */
    public Long getWarehouseId() {
        return warehouseId;
    }

    /**
     * @param warehouseId 仓库id
     */
    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    /**
     * @return sysItemId 系统item id
     */
    public Long getSysItemId() {
        return sysItemId;
    }

    /**
     * @param sysItemId 系统item id
     */
    public void setSysItemId(Long sysItemId) {
        this.sysItemId = sysItemId;
    }

    /**
     * @return sysSkuId 系统sku id
     */
    public Long getSysSkuId() {
        return sysSkuId;
    }

    /**
     * @param sysSkuId 系统sku id
     */
    public void setSysSkuId(Long sysSkuId) {
        this.sysSkuId = sysSkuId;
    }

    /**
     * @return num 原料数量
     */
    public Integer getNum() {
        return num;
    }

    /**
     * @param num 原料数量
     */
    public void setNum(Integer num) {
        this.num = num;
    }

    /**
     * @return allocateNum 分配拣选数量
     */
    public Integer getAllocateNum() {
        return allocateNum;
    }

    /**
     * @param allocateNum 分配拣选数量
     */
    public void setAllocateNum(Integer allocateNum) {
        this.allocateNum = allocateNum;
    }


    /**
     * @return picPath 商品图片URL
     */
    public String getPicPath() {
        return DMJItemUtils.optimizePicUrl(picPath);
    }

    /**
     * @param picPath 商品图片URL
     */
    public void setPicPath(String picPath) {
        this.picPath = picPath;
    }

    /**
     * @return propertiesName 规格名称
     */
    public String getPropertiesName() {
        return propertiesName;
    }

    /**
     * @param propertiesName 规格名称
     */
    public void setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
    }

    /**
     * @return outerId sku的商家编码
     */
    public String getOuterId() {
        return outerId;
    }

    /**
     * @param outerId sku的商家编码
     */
    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public Integer getRemainQty() {
        return remainQty;
    }

    public void setRemainQty(Integer remainQty) {
        this.remainQty = remainQty;
    }

    public String getProductRelation() {
        return productRelation;
    }

    public void setProductRelation(String productRelation) {
        this.productRelation = productRelation;
    }

    public List<ProductRelation> getProductRelationArr() {
        return productRelationArr;
    }

    public void setProductRelationArr(List<ProductRelation> productRelationArr) {
        this.productRelationArr = productRelationArr;
    }
    public String getStockProductOrderCode() {
        return stockProductOrderCode;
    }

    public void setStockProductOrderCode(String stockProductOrderCode) {
        this.stockProductOrderCode = stockProductOrderCode;
    }

    public Long getCombineId() {
        return combineId;
    }

    public void setCombineId(Long combineId) {
        this.combineId = combineId;
    }

    //根据箱规反推箱数/散装
    public void fillBoxNumInfo() {
        Long num = (null != getNum() ? getNum() : 0L);
        if(null != this.boxNum && 0 != this.boxNum) {
            this.numOfBox=Long.parseLong(String.valueOf(num/boxNum));
            this.bulkNum=Long.parseLong(String.valueOf(num%boxNum));
        }else {
            this.numOfBox=0L;
            this.boxNum=0L;
            this.bulkNum=num;
        }
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = Objects.equals(unitId, 0L) ? null : unitId;
    }

}

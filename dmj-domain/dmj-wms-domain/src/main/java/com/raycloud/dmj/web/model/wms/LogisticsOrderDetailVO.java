package com.raycloud.dmj.web.model.wms;

import com.raycloud.dmj.domain.enums.StockStatusEnum;
import com.raycloud.dmj.domain.item.tag.ItemSystemTag;
import com.raycloud.dmj.domain.trades.utils.RefundUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wms.LogisticsOrder;
import com.raycloud.dmj.domain.wms.LogisticsOrderDetail;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.utils.wms.WmsKeyUtils;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName LogisticsOrderVO.java
 * @Description 销售出库单明细
 * @createTime 2022年12月22日 16:37:00
 */
@Getter
@Setter
public class LogisticsOrderDetailVO extends LogisticsOrderDetail {
    /**
     * 状态
     */
    private String sysStatus;
    /**
     * 订单（主单）系统单号
     */
    private Long sid;
    /**
     * 订单系统单号
     */
    private Long belongSid;
    /**
     * 主表id
     */
    private Long mainId;
    /**
     * 赠品数量
     */
    private Integer giftNum;
    /**
     * 是否取消库存缺货异常（INSUFFICIENT,EMPTY,EXCEP）：0 否，1 是
     */
    private Integer insufficientCanceled;
    /**
     * 商品对应关系是否改动: 0 否，1 是
     */
    private Integer relationChanged;
    /**
     * 当前子订单的库存状态,如库存正常(NORMAL),库存不足(INSUFFICIENT)
     */
    private String stockStatus;
    /**
     * 子订单退款状态
     */
    private String refundStatus;
    /**
     * 用于订单同步时记录平台商品(规格)是否变更过 0:未变 1：有变
     */
    private Integer itemChanged;


    /**
     * 商品名称
     */
    private String sysTitle;
    /**
     * 图片链接
     */
    private String sysPicPath;
    /**
     * 商品简称
     */
    private String shortTitle;
    /**
     * 商品备注
     */
    private String sysItemRemark;
    /**
     * 规格备注
     */
    private String sysSkuRemark;
    /**
     * 规格名称
     */
    private String sysSkuPropertiesName;
    /**
     * 规格别名
     */
    private String sysSkuPropertiesAlias;

    /**
     * 标签
     */
    private List<ItemSystemTag> itemTags;
    /**
     * 异常
     */
    private Set<ExceptEnum> exceptEnums;
    /**
     * 异常
     */
    private List<String> exceptNames;

    /**
     * 商品类目
     */
    private String itemCatName;

    /**
     * 商品类别
     */
    private String itemCategoryNames;

    /**
     * 商品品牌
     */
    private String itemBrandName;
    /**
     * 标记有赠品
     */
    private Integer hasGift;
    /**
     * 长
     */
    private Double x;
    /**
     * 宽
     */
    private Double y;
    /**
     * 高
     */
    private Double z;

    /**
     * 重量，单位kg
     */
    private Double weight;

    /**
     * 填充异常
     */
    public Set<ExceptEnum> fillExceptions() {
        Set<ExceptEnum> exceptions = new HashSet<>();
        // 退款标记分析
        if (RefundUtils.isRefundOrder(getRefundStatus())) {
            exceptions.add(ExceptEnum.REFUNDING);
        }
        if (TradeStatusUtils.isAfterSendGoods(getSysStatus())) {
            return exceptions;
        }
        //商品未匹配
        if (StringUtils.equals(StockStatusEnum.STOCK_STATUS_UNALLOCATED.stockStatus, getStockStatus())) {
            exceptions.add(ExceptEnum.UNALLOCATED);
        }
        //对应关系改动
        if (getRelationChanged() != null && getRelationChanged() - 1 == 0) {
            exceptions.add(ExceptEnum.RELATION_CHANGED);
        }
        //库存不足
        if (StockStatusEnum.STOCK_STATUS_INSUFFICIENT.stockStatus.equals(getStockStatus()) ||
                StockStatusEnum.STOCK_STATUS_EXCEP.stockStatus.equals(getStockStatus()) ||
                StockStatusEnum.STOCK_STATUS_EMPTY.stockStatus.equals(getStockStatus())) {
            exceptions.add(ExceptEnum.INSUFFICIENT);
        }
        //订单同步商品有改动
        if (getItemChanged() != null && getItemChanged() == 1) {
            exceptions.add(ExceptEnum.ITEM_CHANGED);
        }
        setExceptNames(exceptions.stream().map(ExceptEnum::getChinese).distinct().collect(Collectors.toList()));
        setExceptEnums(exceptions);
        return exceptions;
    }

    public Integer getHasGift() {
        Integer giftNum = getGiftNum();
        return giftNum != null && giftNum > 0 ? CommonConstants.JUDGE_YES : CommonConstants.JUDGE_NO;
    }

    public String buildOrderKey() {
        return StringUtils.isNotEmpty(this.getSkuOuterId()) ? this.getSkuOuterId() : this.getItemOuterId();
    }

    public String buildItemKeyIdKey() {
        return WmsKeyUtils.buildItemKey(this.getSysItemId(), this.getSysSkuId());
    }
}

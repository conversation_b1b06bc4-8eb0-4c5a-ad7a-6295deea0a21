package com.raycloud.dmj.domain.wms.enums;

import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.stock.StockChangeBusiType;
import com.raycloud.dmj.domain.wave.UniqueCodeRelation;
import com.raycloud.dmj.domain.wave.model.UniqueCodeGenericParams;
import com.raycloud.dmj.domain.wms.WmsUniqueCodeUpdatePojo;
import com.raycloud.dmj.domain.wms.WorkingStorageSection;
import lombok.Getter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.function.Function;

@Getter
public enum WmsUniqueCodeUpdateEnum {
    /**
     * 注：添加枚举后，需要检查 SubBusinessType 是否存在下处（唯一码【状态】更新）Map中。
     */

    PDA_FAST_UP_SHELVE(DetailedBusinessOpEnum.PDA_FAST_UP_SHELVE, UniqueCodeRelation.BusinessType.OTHER_WAREHOUSING_ORDER, UniqueCodeGenericParams.SubBusinessType.ON_SHELF, "快速上架，唯一码状态更新：%s->%s,上架至%s货位", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, t -> null, t -> Objects.equal(t.getStockChangeBusiType(), StockChangeBusiType.FAST_UPSHELF)),
    PDA_MIX_UP_SHELVE(DetailedBusinessOpEnum.PDA_MIX_UP_SHELVE, UniqueCodeRelation.BusinessType.OTHER_WAREHOUSING_ORDER, UniqueCodeGenericParams.SubBusinessType.ON_SHELF, "混放上架,唯一码状态更新：%s->%s,上架至%s货位", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, t -> null, t -> Objects.equal(t.getStockChangeBusiType(), StockChangeBusiType.MIX_UPSHELF)),
    PDA_CAIGOU_MIX_UP_SHELVE(DetailedBusinessOpEnum.PDA_CAIGOU_MIX_UP_SHELVE, UniqueCodeRelation.BusinessType.OTHER_WAREHOUSING_ORDER, UniqueCodeGenericParams.SubBusinessType.ON_SHELF, "采购混放上架,唯一码状态更新：%s->%s,上架至%s货位", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, t -> null, t -> Objects.equal(t.getStockChangeBusiType(), StockChangeBusiType.PURCHASE_UPSHELF)),
    PDA_UP_SHELVE(DetailedBusinessOpEnum.PDA_UP_SHELVE, UniqueCodeRelation.BusinessType.OTHER_WAREHOUSING_ORDER, UniqueCodeGenericParams.SubBusinessType.ON_SHELF, "", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, t -> null, t -> Lists.newArrayList(StockChangeBusiType.MIX_UPSHELF, StockChangeBusiType.SECTION_UPSHELF, StockChangeBusiType.PURCHASE_UPSHELF).contains(t.getStockChangeBusiType())),
    PDA_DOWN_SHELVE_ITEM(DetailedBusinessOpEnum.PDA_DOWN_SHELVE_ITEM, UniqueCodeRelation.BusinessType.OTHER_OUTBOUND_ORDER, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_ESTABLISH_RELATION_AND_OUT, "库存下架（按商品），唯一码状态更新：%s -> %s，下架货位：%s，仓库更新：%s -> %s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),
    PDA_DOWN_SHELVE_GS(DetailedBusinessOpEnum.PDA_DOWN_SHELVE_GS, UniqueCodeRelation.BusinessType.OTHER_OUTBOUND_ORDER, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_ESTABLISH_RELATION_AND_OUT, "PDA库存下架（按货位），唯一码状态更新：%s -> %s，下架货位：%s，仓库更新：%s -> %s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),
    DOWN_SHELVE_COMMON(DetailedBusinessOpEnum.DOWN_SHELVE_COMMON, null, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF, "PDA普通下架，唯一码状态更新：%s -> %s，下架货位：%s，仓库更新：%s -> %s", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> WorkingStorageSection.TypeEnum.COMMON.getCode(), WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),
    FREE_PICK(DetailedBusinessOpEnum.FREE_PICK, null, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF, "PDA自由拣，唯一码状态更新：%s -> %s，下架货位：%s，仓库更新：%s -> %s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, t -> WorkingStorageSection.TypeEnum.PICK.getCode(), WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),
    PDA_DO_UNSHELVE(DetailedBusinessOpEnum.PDA_DO_UNSHELVE, UniqueCodeRelation.BusinessType.UNSHELVE_ORDER, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF, "PDA下架单，下架单%s，唯一码状态更新：%s->%s，仓库变更：%s → %s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBusinessCode,t -> true),
    PDA_OTHER_OUT(DetailedBusinessOpEnum.PDA_OTHER_OUT, UniqueCodeRelation.BusinessType.OTHER_OUTBOUND_ORDER, null, "pda其他出库，唯一码状态更新：%s->%s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),
    PDA_FULL_BOX_TRANSFER(DetailedBusinessOpEnum.PDA_FULL_BOX_TRANSFER, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.MOVE_BOX, "PDA整箱移库，移入货位：%s", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> null, t -> true),
    PDA_FULL_BOX_TRANSFER_UNPACK(DetailedBusinessOpEnum.PDA_FULL_BOX_TRANSFER, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.PART_MOVE_BOX, "PDA整箱移库，自动拆箱，移入货位：%s，清空关联箱码", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> null, t -> true),
    PDA_PART_BOX_TRANSFER(DetailedBusinessOpEnum.PDA_PART_BOX_TRANSFER, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.MOVE_BOX, "PDA开箱移库，移入货位：%s", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> null, t -> true),
    PDA_PART_BOX_TRANSFER_UNPACK(DetailedBusinessOpEnum.PDA_PART_BOX_TRANSFER, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.PART_MOVE_BOX, "PDA开箱移库，移入货位：%s,清空关联箱码", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> null, t -> true),
    PDA_UNBOXING(DetailedBusinessOpEnum.PDA_UNBOXING, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.SPLIT_BOX, "拆箱，清空关联箱码", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, t -> null, t -> true),
    PDA_ITEMS_EXCHANGE_BOX(DetailedBusinessOpEnum.PDA_ITEMS_EXCHANGE_BOX, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.CHANGE_BOX, "PDA商品换箱，从箱码：%s移至箱码：%s，移入位置：%s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBoxCode, t -> Objects.equal(t.getStockChangeBusiType(), StockChangeBusiType.TRANSFER_UPSHELF)),
    PDA_BOX_UP_SHELVE(DetailedBusinessOpEnum.PDA_BOX_UP_SHELVE, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.ON_SHELF_IN_WAREHOUSE, "PDA按箱上架，唯一码状态更新：%s->%s，上架货位：%s", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> null, t -> true),
    PDA_BOX_UP_SHELVE_UNPACK(DetailedBusinessOpEnum.PDA_BOX_UP_SHELVE, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.ON_SHELF_EMPTY_BOX, "PDA按箱上架，唯一码状态更新：%s->%s，上架货位：%s，清空关联箱码", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> null, t -> true),
    PDA_REPLENISH_UP_SHELF_BOX(DetailedBusinessOpEnum.PDA_REPLENISH_UP_SHELF_BOX, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.ON_SHELF_IN_WAREHOUSE, "PDA补货上架（按箱），唯一码状态更新：%s->%s，上架货位：%s", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> null, t -> true),
    PDA_REPLENISH_UP_SHELF_BOX_UNPACK(DetailedBusinessOpEnum.PDA_REPLENISH_UP_SHELF_BOX, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.ON_SHELF_EMPTY_BOX, "PDA补货上架（按箱），唯一码状态更新：%s->%s，上架货位：%s，清空关联箱码", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> null, t -> true),
    PDA_BOX_DOWN_SHELF(DetailedBusinessOpEnum.PDA_BOX_DOWN_SHELF, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF, "PDA按箱下架，唯一码状态更新：%s -> %s，下架至%s，仓库更新：%s -> %s", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBoxCode, t -> true),
    PDA_BOX_DOWN_SHELF_UNBOXING(DetailedBusinessOpEnum.PDA_BOX_DOWN_SHELF_UNBOXING, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_EMPTY_BOX, "PDA按箱下架，下架并拆箱至：%s，清空关联箱码", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBoxCode, t -> Objects.equal(t.getBoxOperateType(), BoxOperateEnum.BOX_DOWNSHELF)),
    PDA_UNBOXING_DOWN_SHELF(DetailedBusinessOpEnum.PDA_UNBOXING_DOWN_SHELF, UniqueCodeRelation.BusinessType.BOX_CODE, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_EMPTY_BOX, "PDA拆箱下架，下架至：%s，清空关联箱码", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBoxCode, t -> true),
    PDA_PART_BOX_WAVE_PICK(DetailedBusinessOpEnum.PDA_BOX_WAVE_PICK, UniqueCodeRelation.BusinessType.WAVE_PICKING_ID, UniqueCodeGenericParams.SubBusinessType.WAVE_PICK_ITEM_BOX, "波次拣选，下架至：%s", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, WmsUniqueCodeUpdatePojo::getGoodsSectionCode, t -> true),
    PDA_PART_BOX_WAVE_PICK_UNPACK(DetailedBusinessOpEnum.PDA_BOX_WAVE_PICK, UniqueCodeRelation.BusinessType.WAVE_PICKING_ID, UniqueCodeGenericParams.SubBusinessType.PART_WAVE_PICK_ITEM_BOX, "波次拣选，下架至：%s,清空关联箱码",WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, WmsUniqueCodeUpdatePojo::getGoodsSectionCode, t -> true),
    PDA_IN_WAREHOUSE_UP_SHELVE(DetailedBusinessOpEnum.PDA_IN_WAREHOUSE_UP_SHELVE, UniqueCodeRelation.BusinessType.OTHER_WAREHOUSING_ORDER, UniqueCodeGenericParams.SubBusinessType.ON_SHELF_IN_WAREHOUSE, "",WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> null, t -> true),
    PDA_OUT_WAREHOUSE_UP_SHELVE(DetailedBusinessOpEnum.PDA_OUT_WAREHOUSE_UP_SHELVE, UniqueCodeRelation.BusinessType.OTHER_WAREHOUSING_ORDER, UniqueCodeGenericParams.SubBusinessType.ON_SHELF, "",WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, t -> null, t -> true),
    PDA_OUTER_MOVE(DetailedBusinessOpEnum.PDA_OUTER_MOVE, UniqueCodeRelation.BusinessType.MOVE_STOCK, UniqueCodeGenericParams.SubBusinessType.MOVE_STOCK, "PDA商品移库，唯一码状态变更：%s → %s；移入货位：%s；仓库变更：%s → %s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getAssoOrderCode, t -> true),
    PDA_BATCH_OUTER_MOVE(DetailedBusinessOpEnum.PDA_BATCH_OUTER_MOVE, UniqueCodeRelation.BusinessType.MOVE_STOCK, UniqueCodeGenericParams.SubBusinessType.MOVE_STOCK, "PDA批量移库，唯一码状态变更：%s → %s；移入货位：%s；仓库变更：%s → %s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getAssoOrderCode, t -> true),
    PDA_MOVE_TASK(DetailedBusinessOpEnum.PDA_MOVE_TASK, UniqueCodeRelation.BusinessType.MOVE_STOCK, UniqueCodeGenericParams.SubBusinessType.MOVE_STOCK, "PDA移库任务，唯一码状态变更：%s → %s；移入货位：%s；仓库变更：%s → %s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getAssoOrderCode, t -> true),
    PC_MOVE_TASK(DetailedBusinessOpEnum.PC_MOVE_TASK, UniqueCodeRelation.BusinessType.MOVE_STOCK, UniqueCodeGenericParams.SubBusinessType.MOVE_STOCK, "PC移库任务，唯一码状态变更：%s → %s；移入货位：%s；仓库变更：%s → %s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getAssoOrderCode, t -> true),

    ALLOCATE_IN(DetailedBusinessOpEnum.ALLOCATE_IN, UniqueCodeRelation.BusinessType.ALLOT, UniqueCodeGenericParams.SubBusinessType.ALLOCATE_IN, "调拨入库单%s入库，唯一码状态更新：%s->%s", t -> WorkingStorageSection.TypeEnum.PURCHASE.getCode(), WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),
    ALLOCATE_OUT(DetailedBusinessOpEnum.ALLOCATE_OUT, UniqueCodeRelation.BusinessType.ALLOT, UniqueCodeGenericParams.SubBusinessType.ALLOCATE_OUT, "调拨出库单%s出库，唯一码状态更新：%s->%s", t -> null, WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),
    ALLOCATE_IN_UP_SHELF(DetailedBusinessOpEnum.ALLOCATE_IN_UP_SHELF, UniqueCodeRelation.BusinessType.ALLOT, UniqueCodeGenericParams.SubBusinessType.ALLOCATE_IN_GOODS_SECTION, "调拨入库单%s入库并上架，唯一码状态更新：%s->%s，上架至货位：%s", WmsUniqueCodeUpdatePojo::getOprGoodsSectionCode, WmsUniqueCodeUpdatePojo::getAssoOrderCode, t -> Objects.equal(t.getStockChangeBusiType(), StockChangeBusiType.ALLOCATE_UPSHELF)),
    PDA_PURCHASE_RETURN_BOX(DetailedBusinessOpEnum.PDA_PURCHASE_RETURN_BOX, UniqueCodeRelation.BusinessType.PURCHASE_RETURN_ORDER, null, "pda按箱采退，唯一码状态更新：%s->%s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),
    OTHER_IN(DetailedBusinessOpEnum.OTHER_IN, UniqueCodeRelation.BusinessType.OTHER_WAREHOUSING_ORDER, UniqueCodeGenericParams.SubBusinessType.OTHER_IN, "PC其他入库单入库完成，唯一码状态更新：%s → %s，仓库变更：%s→%s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),
    OTHER_IN_UP_SHELF(DetailedBusinessOpEnum.OTHER_IN_UP_SHELF, UniqueCodeRelation.BusinessType.OTHER_WAREHOUSING_ORDER, UniqueCodeGenericParams.SubBusinessType.OTHER_IN_ON_SHELF, "PC其他入库单入库并上架完成，唯一码状态更新：%s → %s，上架至货位：%s，仓库变更：%s→%s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getAssoOrderCode, t -> java.util.Objects.equals(t.getStockChangeBusiType(), StockChangeBusiType.OTHER_CUSTOM_UPSHELF)),

    STOCK_PROCESS_CHECK_IN(DetailedBusinessOpEnum.STOCK_PROCESS_CHECK_IN, UniqueCodeRelation.BusinessType.STOCK_PRODUCT_ORDER, UniqueCodeGenericParams.SubBusinessType.PRODUCT_ORDER_IN_WAREHOUSE, "加工登记，唯一码状态更新：%s->%s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getAssoOrderCode, t -> Objects.equal(t.getStockChangeBusiType(), StockChangeBusiType.STOCK_PRODUCT)),
    UNIQUE_CODE_SHIP_BOX(DetailedBusinessOpEnum.UNIQUE_CODE_SHIP_BOX, UniqueCodeRelation.BusinessType.BOX_CODE, null, "按唯一码发货装箱，装箱完成，箱码：%s", t -> null, WmsUniqueCodeUpdatePojo::getBoxCode, t -> true),
    STOCK_PRODUCT_FINISH(DetailedBusinessOpEnum.STOCK_PRODUCT_FINISH, UniqueCodeRelation.BusinessType.STOCK_PRODUCT_ORDER, UniqueCodeGenericParams.SubBusinessType.STOCK_PRODUCT_FINISH, "加工单%s入库，唯一码状态更新：%s->%s", WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),
    STOCK_PRODUCT_FINISH_SHELVE(DetailedBusinessOpEnum.STOCK_PRODUCT_FINISH_SHELVE, UniqueCodeRelation.BusinessType.STOCK_PRODUCT_ORDER, UniqueCodeGenericParams.SubBusinessType.STOCK_PRODUCT_FINISH, "加工上架%s，唯一码状态更新：%s->%s",WmsUniqueCodeUpdatePojo::getGoodsSectionCode, WmsUniqueCodeUpdatePojo::getBusinessCode, t -> true),

    UN_KNOW(DetailedBusinessOpEnum.UN_KNOW, null, null, null, null, null, null),
    ;


    private DetailedBusinessOpEnum detailedBusinessOpEnum;
    private UniqueCodeRelation.BusinessType businessType;
    private UniqueCodeGenericParams.SubBusinessType subBusinessType;
    private String logTemplate;
    /**
     * 原始位置
     */
    private Function<WmsUniqueCodeUpdatePojo, String> fromGoodsSectionCodeF;
    /**
     * 最终位置（货位or暂存区）
     */
    private Function<WmsUniqueCodeUpdatePojo, String> goodsSectionCodeF;
    private Function<WmsUniqueCodeUpdatePojo, String> businessCodeF;
    /**
     * 是否符合条件，flase 为不符合 (例：快速上架时会收货+上架，只用上架时才需要进行处理)
     */
    private Function<WmsUniqueCodeUpdatePojo, Boolean> passesTest;

    WmsUniqueCodeUpdateEnum(DetailedBusinessOpEnum detailedBusinessOpEnum, UniqueCodeRelation.BusinessType businessType, UniqueCodeGenericParams.SubBusinessType subBusinessType, String logTemplate, Function<WmsUniqueCodeUpdatePojo, String> goodsSectionCodeF, Function<WmsUniqueCodeUpdatePojo, String> businessCodeF, Function<WmsUniqueCodeUpdatePojo, Boolean> passesTest) {
        this.detailedBusinessOpEnum = detailedBusinessOpEnum;
        this.businessType = businessType;
        this.subBusinessType = subBusinessType;
        this.logTemplate = logTemplate;
        this.goodsSectionCodeF = goodsSectionCodeF;
        this.businessCodeF = businessCodeF;
        this.passesTest = passesTest;
    }

    WmsUniqueCodeUpdateEnum(DetailedBusinessOpEnum detailedBusinessOpEnum, UniqueCodeRelation.BusinessType businessType, UniqueCodeGenericParams.SubBusinessType subBusinessType, String logTemplate, Function<WmsUniqueCodeUpdatePojo, String> fromGoodsSectionCodeF, Function<WmsUniqueCodeUpdatePojo, String> goodsSectionCodeF, Function<WmsUniqueCodeUpdatePojo, String> businessCodeF, Function<WmsUniqueCodeUpdatePojo, Boolean> passesTest) {
        this.detailedBusinessOpEnum = detailedBusinessOpEnum;
        this.businessType = businessType;
        this.subBusinessType = subBusinessType;
        this.logTemplate = logTemplate;
        this.fromGoodsSectionCodeF = fromGoodsSectionCodeF;
        this.goodsSectionCodeF = goodsSectionCodeF;
        this.businessCodeF = businessCodeF;
        this.passesTest = passesTest;
    }

    public static WmsUniqueCodeUpdateEnum getByDetailedBusinessOpKey(String detailedBusinessOpKey) {
        for (WmsUniqueCodeUpdateEnum value : values()) {
            if (StringUtils.equals(detailedBusinessOpKey, value.getDetailedBusinessOpEnum().getKey())) {
                return value;
            }
        }
        return UN_KNOW;
    }

    public static WmsUniqueCodeUpdateEnum refine(WmsUniqueCodeUpdateEnum wmsUniqueCodeUpdateEnum, WmsUniqueCodeUpdatePojo wmsUniqueCodeUpdatePojo) {
        if (java.util.Objects.isNull(wmsUniqueCodeUpdateEnum) || java.util.Objects.isNull(wmsUniqueCodeUpdatePojo)) {
            return wmsUniqueCodeUpdateEnum;
        }
        if (Objects.equal(DetailedBusinessOpEnum.ALLOCATE_OUT.getKey(), wmsUniqueCodeUpdateEnum.getDetailedBusinessOpEnum().getKey())
                && (Objects.equal(wmsUniqueCodeUpdatePojo.getGsInventoryBusiType(), GoodsSectionInventoryBusiTypeEnum.ALLOCATE_IN) || Objects.equal(wmsUniqueCodeUpdatePojo.getStockChangeBusiType(), StockChangeBusiType.ALLOCATE_IN))) {
            // 出库直接入库
            return ALLOCATE_IN;
        } else if (Objects.equal(DetailedBusinessOpEnum.ALLOCATE_OUT.getKey(), wmsUniqueCodeUpdateEnum.getDetailedBusinessOpEnum().getKey())
                && Objects.equal(wmsUniqueCodeUpdatePojo.getStockChangeBusiType(), StockChangeBusiType.ALLOCATE_UPSHELF)) {
            return ALLOCATE_IN_UP_SHELF;
        }
        return refine4Unboxing(wmsUniqueCodeUpdateEnum, wmsUniqueCodeUpdatePojo.getIsUnPack());
    }


    public static WmsUniqueCodeUpdateEnum refine4Unboxing(WmsUniqueCodeUpdateEnum wmsUniqueCodeUpdateEnum, Boolean unboxing) {
        if (StringUtils.equals(DetailedBusinessOpEnum.PDA_FULL_BOX_TRANSFER.getKey(), wmsUniqueCodeUpdateEnum.getDetailedBusinessOpEnum().getKey())) {
            return BooleanUtils.isTrue(unboxing) ? PDA_FULL_BOX_TRANSFER_UNPACK : PDA_FULL_BOX_TRANSFER;
        } else if (StringUtils.equals(DetailedBusinessOpEnum.PDA_PART_BOX_TRANSFER.getKey(), wmsUniqueCodeUpdateEnum.getDetailedBusinessOpEnum().getKey())) {
            return BooleanUtils.isTrue(unboxing) ? PDA_PART_BOX_TRANSFER_UNPACK : PDA_PART_BOX_TRANSFER;
        } else if (StringUtils.equals(DetailedBusinessOpEnum.PDA_BOX_UP_SHELVE.getKey(), wmsUniqueCodeUpdateEnum.getDetailedBusinessOpEnum().getKey())) {
            return BooleanUtils.isTrue(unboxing) ? PDA_BOX_UP_SHELVE_UNPACK : PDA_BOX_UP_SHELVE;
        } else if (StringUtils.equals(DetailedBusinessOpEnum.PDA_REPLENISH_UP_SHELF_BOX.getKey(), wmsUniqueCodeUpdateEnum.getDetailedBusinessOpEnum().getKey())) {
            return BooleanUtils.isTrue(unboxing) ? PDA_REPLENISH_UP_SHELF_BOX_UNPACK : PDA_REPLENISH_UP_SHELF_BOX;
        } else if (StringUtils.equals(DetailedBusinessOpEnum.PDA_BOX_WAVE_PICK.getKey(), wmsUniqueCodeUpdateEnum.getDetailedBusinessOpEnum().getKey())) {
            return BooleanUtils.isTrue(unboxing) ? PDA_PART_BOX_WAVE_PICK_UNPACK : PDA_PART_BOX_WAVE_PICK;
        }
        return wmsUniqueCodeUpdateEnum;
    }

    /**
     * =================================== 唯一码【状态】更新 ========================================
     */
    private static final Set<UniqueCodeGenericParams.SubBusinessType> MOVE_STOCK_SUBTYPES = new HashSet<>(Arrays.asList(
            UniqueCodeGenericParams.SubBusinessType.MOVE_BOX, UniqueCodeGenericParams.SubBusinessType.PART_MOVE_BOX,
            UniqueCodeGenericParams.SubBusinessType.SPLIT_BOX, UniqueCodeGenericParams.SubBusinessType.CHANGE_BOX,
            UniqueCodeGenericParams.SubBusinessType.ON_SHELF_IN_WAREHOUSE, UniqueCodeGenericParams.SubBusinessType.ON_SHELF_EMPTY_BOX,
            UniqueCodeGenericParams.SubBusinessType.OFF_SHELF,UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_EMPTY_BOX,
            UniqueCodeGenericParams.SubBusinessType.WAVE_PICK_ITEM_BOX, UniqueCodeGenericParams.SubBusinessType.PART_WAVE_PICK_ITEM_BOX,
            UniqueCodeGenericParams.SubBusinessType.MOVE_STOCK

    ));
    private static final Set<UniqueCodeGenericParams.SubBusinessType> IN_WAREHOUSE_SUBTYPES = new HashSet<>(Arrays.asList(
            UniqueCodeGenericParams.SubBusinessType.ON_SHELF, UniqueCodeGenericParams.SubBusinessType.ALLOCATE_IN, UniqueCodeGenericParams.SubBusinessType.ALLOCATE_IN_GOODS_SECTION,
            UniqueCodeGenericParams.SubBusinessType.OTHER_IN,UniqueCodeGenericParams.SubBusinessType.OTHER_IN_ON_SHELF,UniqueCodeGenericParams.SubBusinessType.PRODUCT_ORDER_IN_WAREHOUSE,UniqueCodeGenericParams.SubBusinessType.STOCK_PRODUCT_FINISH
    ));
    private static final Set<UniqueCodeGenericParams.SubBusinessType> ESTABLISH_RELATION_AND_OUT_SUBTYPES = new HashSet<>(Arrays.asList(
            UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_ESTABLISH_RELATION_AND_OUT
    ));
    private static final Set<UniqueCodeGenericParams.SubBusinessType> OUT_SUBTYPES = new HashSet<>(Arrays.asList(
            UniqueCodeGenericParams.SubBusinessType.ALLOCATE_OUT, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_AND_OUT, UniqueCodeGenericParams.SubBusinessType.BOX_OUT
    ));
    private static final Set<UniqueCodeGenericParams.SubBusinessType> ESTABLISH_RELATION = new HashSet<>(Arrays.asList(

    ));

    public boolean isInWarehouse() {
        return IN_WAREHOUSE_SUBTYPES.contains(subBusinessType);
    }
    public boolean isMoveStock() {
        return MOVE_STOCK_SUBTYPES.contains(subBusinessType);
    }
    public boolean isEstablishRelationAndOut() {
        return ESTABLISH_RELATION_AND_OUT_SUBTYPES.contains(subBusinessType);
    }
    public boolean isOut() {
        return OUT_SUBTYPES.contains(subBusinessType);
    }
    public boolean isEstablishRelation() {
        return ESTABLISH_RELATION.contains(subBusinessType);
    }

    /**
     * =================================== 唯一码【关系】更新 ========================================
     */
    private static final Set<UniqueCodeGenericParams.SubBusinessType> CLEAR_CODE_SUBTYPES = new HashSet<>(Arrays.asList(
            UniqueCodeGenericParams.SubBusinessType.PART_MOVE_BOX, UniqueCodeGenericParams.SubBusinessType.SPLIT_BOX,
            UniqueCodeGenericParams.SubBusinessType.ON_SHELF_EMPTY_BOX,UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_EMPTY_BOX,UniqueCodeGenericParams.SubBusinessType.PART_WAVE_PICK_ITEM_BOX

    ));
    private static final Set<UniqueCodeGenericParams.SubBusinessType> CHANGE_RELATION_SUBTYPES = new HashSet<>(Arrays.asList(
            UniqueCodeGenericParams.SubBusinessType.CHANGE_BOX, UniqueCodeGenericParams.SubBusinessType.ALLOCATE_IN,
            UniqueCodeGenericParams.SubBusinessType.ALLOCATE_IN_GOODS_SECTION

    ));
    private static final Set<UniqueCodeGenericParams.SubBusinessType> BUILD_RELATION_SUBTYPES = new HashSet<>(Arrays.asList(
            UniqueCodeGenericParams.SubBusinessType.ALLOCATE_OUT
    ));

    public boolean isClearUniqueCode(){
        return CLEAR_CODE_SUBTYPES.contains(subBusinessType);
    }
    public boolean isBuildUniqueCodeRelation(){
        return BUILD_RELATION_SUBTYPES.contains(subBusinessType);
    }
    public boolean isChangeUniqueCodeRelation() {
        return CHANGE_RELATION_SUBTYPES.contains(subBusinessType);
    }
}

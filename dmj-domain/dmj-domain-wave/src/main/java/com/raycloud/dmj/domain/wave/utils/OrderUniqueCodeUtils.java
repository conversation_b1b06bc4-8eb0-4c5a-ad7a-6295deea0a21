package com.raycloud.dmj.domain.wave.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.TradeValidator;
import com.raycloud.dmj.domain.TradeValidatorException;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.WaveTypeEnum;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.DataUtils;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.enums.OrderUniqueCodeExtConfigEnum;
import com.raycloud.dmj.domain.wave.model.ItemUniqueCodeQueryParams;
import com.raycloud.dmj.domain.wave.model.OrderUniqueCodeQueryParams;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * @ProjectName: erp-core-new
 * @Author: qingfeng
 * @Description: 订单唯一码工具类
 * @Date: 2021-04-19 16:33
 */
public class OrderUniqueCodeUtils {

    public static final String KEY_JOINER = "_";

    private static final int UNIQUE_CODE_LEN = 13;
    /**
     * 时间间隔15天
     */
    private static final long PAGE_UNIQUE_TIME_INTERVAL = 15 * 24 * 60 * 60 * 1000;

    private static final long ONE_DAY_TIME = 1 * 24 * 60 * 60 * 1000;

    public static final Map<String, Integer> EXCEP_TO_INT_MAP = new HashMap<>();

    public static final Map<Integer, String> INT_TO_EXCEP_MAP = new HashMap<>();
    // 驳回状态回退Map
    public static final Map<Integer, Integer> REJECT_STATUS_MAP = ImmutableMap.<Integer, Integer>builder()
            .put(OrderUniqueCodeStatusEnum.RECIVED.getType(), OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType())
            .put(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType())
            .put(OrderUniqueCodeStatusEnum.PICKED.getType(), OrderUniqueCodeStatusEnum.WAIT_PICK.getType()).build();

    /**
     * 多件唯一码
     */
    public static final Integer CODE_TYPE_MULTI = 2;

    /**
     * 备货唯一码
     */
    public static final Integer CODE_TYPE_BACK = 3;

    /**
     * 退款异常
     */
    public static final String EXCEP_REFUND = "EXCEP_REFUND";

    /**
     * @see TradeConstants#IDX_HALT
     */
    static {
        // 挂起订单
        EXCEP_TO_INT_MAP.put("EXCEP_HALT", 0);
        // 对应关系改动
        EXCEP_TO_INT_MAP.put("EXCEP_ITEM_RELATION_MODIFIED", 4);
        // 平台更换地址
        EXCEP_TO_INT_MAP.put("EX_CHANGE_ADDRESS", 5);
        // 平台更换商品
        EXCEP_TO_INT_MAP.put("EX_CHANGE_ITEM", 6);
        // 平台修改备注
        EXCEP_TO_INT_MAP.put("EX_UPDATED_SELLERMEMO", 7);
        // 黑名单
        EXCEP_TO_INT_MAP.put("EX_BLACK", 13);
        // 快递异常
        EXCEP_TO_INT_MAP.put("EX_UNATTAINABLE", 17);
        // 发货异常
        EXCEP_TO_INT_MAP.put("EX_DELIVER", 16);
        // 套件信息修改
        EXCEP_TO_INT_MAP.put("EX_SUITE_QUANTITY_CHANGE", 14);
        // 重复货到付款订单
        EXCEP_TO_INT_MAP.put("EX_COD_REPEAT", 25);
        // 已缺货处理
        EXCEP_TO_INT_MAP.put("EX_STOCK_OUT", 23);
        // 等待退货入仓
        EXCEP_TO_INT_MAP.put("EX_WAITING_RETURN_WMS", 26);
        // 等待合并异常
        EXCEP_TO_INT_MAP.put("EX_WAIT_MERGE", 27);
        // 商品停用
        EXCEP_TO_INT_MAP.put("EX_ITEM_SHUTOFF", 28);

        INT_TO_EXCEP_MAP.put(0, "EXCEP_HALT");
        INT_TO_EXCEP_MAP.put(4, "EXCEP_ITEM_RELATION_MODIFIED");
        INT_TO_EXCEP_MAP.put(5, "EX_CHANGE_ADDRESS");
        INT_TO_EXCEP_MAP.put(6, "EX_CHANGE_ITEM");
        INT_TO_EXCEP_MAP.put(7, "EX_UPDATED_SELLERMEMO");
        INT_TO_EXCEP_MAP.put(13, "EX_BLACK");
        INT_TO_EXCEP_MAP.put(17, "EX_UNATTAINABLE");
        INT_TO_EXCEP_MAP.put(16, "EX_DELIVER");
        INT_TO_EXCEP_MAP.put(14, "EX_SUITE_QUANTITY_CHANGE");
        INT_TO_EXCEP_MAP.put(25, "EX_COD_REPEAT");
        INT_TO_EXCEP_MAP.put(23, "EX_STOCK_OUT");
        INT_TO_EXCEP_MAP.put(26, "EX_WAITING_RETURN_WMS");
        INT_TO_EXCEP_MAP.put(27, "EX_WAIT_MERGE");
        INT_TO_EXCEP_MAP.put(28, "EX_ITEM_SHUTOFF");
    }

    public static class IgnoreExcept {
        /**
         * 可以忽略的系统异常Ids
         */
        Set<Integer> fixExcepts = new HashSet<>();

        /**
         * 可以忽略的自定义异常Ids
         */
        Set<String> customExcepts = new HashSet<>();

        public Set<Integer> getFixExcepts() {
            return fixExcepts;
        }

        public Set<String> getCustomExcepts() {
            return customExcepts;
        }
    }

    public static List<String> getFixExceptString(Set<Integer> fixExcepts) {
        List<String> fixExceptString = new ArrayList<>();
        if (CollectionUtils.isEmpty(fixExcepts)) {
            return fixExceptString;
        }
        for (Integer fixExcept : fixExcepts) {
            fixExceptString.add(INT_TO_EXCEP_MAP.get(fixExcept));
        }
        return fixExceptString;
    }

    public static IgnoreExcept transferIgnoreExcepts(String ignoreExcepts) {
        IgnoreExcept ignoreExcept = new IgnoreExcept();
        if (StringUtils.isEmpty(ignoreExcepts)) {
            return ignoreExcept;
        }
        for (String ignore : ignoreExcepts.split(",")) {
            // 系统异常
            if (EXCEP_TO_INT_MAP.get(ignore) != null) {
                ignoreExcept.fixExcepts.add(EXCEP_TO_INT_MAP.get(ignore));
            } else {
                // 自定义异常
                ignoreExcept.customExcepts.add(ignore);
            }
        }
        return ignoreExcept;
    }

    /**
     * 判断唯一码是否可以和订单接触绑定
     * （等待收货 || 已拣选 || 已收货 || 包含驳回标签的唯一码 || 已下架 ），可以解除绑定
     *
     * 包含驳回标签的唯一码有两种状态：
     * 1：等待收货
     * 2：等待拣选
     *
     * 商品唯一码以下状态 可以解绑
     * 【在库】
     * 【待入库】
     * @param code
     * @return
     */
    public static boolean canRelease(WaveUniqueCode code) {
        return (Objects.equals(3, code.getStatus())
                || Objects.equals(4, code.getStatus())
                || Objects.equals(5, code.getStatus())
                || isIncludeRejectTag(code)
                || Objects.equals(OrderUniqueCodeStatusEnum.WAIT_IN.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.IN.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus()));
    }

    /**
     * 解除绑定，清空部分字段
     * @param update
     */
    public static void buildRelease(WaveUniqueCode update) {
        update.setSid(0L);
        update.setOrderId(0L);
        update.setPositionNo("");
        update.setPositionNoId(0L);
        update.setPositionNoSort(0);
        update.setGoodsSectionId(0L);
        update.setGoodsSectionCode("");
        update.setTemplateId(0L);
        update.setTemplateName("");
        update.setTemplateType(0);
        update.setBuyerNick("");
        update.setSalePrice("");
        update.setMatchedStatus(CommonConstants.JUDGE_NO);
    }

    /**
     * 是否解绑
     */
    public static boolean isRelease(WaveUniqueCode code) {
        return Objects.equals(code.getSid(), 0L);
    }

    /**
     * 转单件唯一码
     * @param code
     * @param clearPositionNo 是否情况分拣货位
     * @return
     */
    public static WaveUniqueCode buildMulti2Single(WaveUniqueCode code, boolean clearPositionNo) {
        WaveUniqueCode update = new WaveUniqueCode();
        update.setId(code.getId());
        update.setCodeType(1);
        if (clearPositionNo) {
            update.setPositionNo("");
            update.setPositionNoId(0L);
            update.setPositionNoSort(0);
        }
        return update;
    }

    /**
     * 判断唯一码是否可用，状态不等于已下架、已取消
     * @param code
     * @return
     */
    public static boolean codeEnableStatus(WaveUniqueCode code) {
        return !Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus()) && Objects.equals(CommonConstants.JUDGE_YES, code.getEnableStatus());
    }

    public static Trade buildUpdateTrade(Trade trade, Long waveId) {
        Trade updated = new TbTrade();
        updated.setSid(trade.getSid());
        updated.setWaveId(waveId);
        return updated;
    }

    public static void appendPositionNoSort(List<WaveUniqueCode> codes, OrderUniqueCodeConfig config) {
        if (config == null || config.getPositionShowSort() == null
                || config.getPositionShowSort() == 0 || CollectionUtils.isEmpty(codes)) {
            return;
        }
        for (WaveUniqueCode code : codes) {
            if (StringUtils.isNotEmpty(code.getPositionNo()) && code.getPositionNoSort() != null) {
                code.setPositionNo(code.getPositionNo() + "-" + code.getPositionNoSort());
            }
        }
    }

    /**
     * 同一个订单，过滤出最后一批次生成的唯一码
     * @param codes
     * @return
     */
    public static List<WaveUniqueCode> filterFinalGenerates(List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return codes;
        }

        List<WaveUniqueCode> finalGenerates = new ArrayList<>();
        Map<Long, List<WaveUniqueCode>> sidMap = codes.stream().collect(Collectors.groupingBy(WaveUniqueCode::getSid));
        Set<Map.Entry<Long, List<WaveUniqueCode>>> entries = sidMap.entrySet();
        for (Map.Entry<Long, List<WaveUniqueCode>> entry : entries) {
            List<WaveUniqueCode> value = entry.getValue();
            value.sort(Comparator.comparing(WaveUniqueCode::getId).reversed());

            // 获取最后一个批次的唯一码
            WaveUniqueCode finalCode = value.get(0);
            finalGenerates.addAll(value.stream().filter(v -> Objects.equals(OrderUniqueCodeUtils.buildKey(v),
                    OrderUniqueCodeUtils.buildKey(finalCode))).collect(Collectors.toList()));
        }
        return finalGenerates;
    }

    public static String buildKey(WaveUniqueCode code) {
        return code.getSid() + "_" + code.getDateNo() + "_" + code.getBatchNo();
    }

    /**
     * 判断订单是否需要踢出波次，条件：该订单下唯一码符合：已取消/已下架/订单状态变化
     * @param codes
     * @return
     */
    public static boolean needRemoveWave(List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return false;
        }
        return codes.stream().allMatch(OrderUniqueCodeUtils::needRemoveWaveSingle);
    }

    public static boolean needRemoveWaveSingle(WaveUniqueCode code) {
        return Objects.equals(7, code.getStatus())
                || Objects.equals(3, code.getMatchedStatus());
    }

    public static boolean isPick(WaveUniqueCode code) {
        return code.getStatus() != null && Objects.equals(code.getType(), 1) && Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus());
    }

    /**
     * 订单唯一码匹配完成是否可以打印
     * @param trade
     * @param tradeMatched
     * @return
     */
    public static boolean orderUniqueCodeCanPrint(Trade trade, Boolean tradeMatched) {
        return BooleanUtils.isTrue(tradeMatched)
                && trade != null
                && (trade.getExpressPrintTime() == null || !trade.getExpressPrintTime().after(TradeTimeUtils.INIT_DATE));
    }

    public static void validateTrade(Staff staff, Trade trade) {
        validateTrade(staff, trade, false, null);
    }

    /**
     * 订单异常校验
     * @param staff
     * @param trade
     */
    public static void validateTrade(Staff staff, Trade trade, boolean ignoreRelationChanged, WaveUniqueCode code) {
        TradeValidator validator = new TradeValidator();
        validator.setThrowExceptionIfError(false);
        validator.check(staff, trade);
        if(validator.getCode()==TradeValidator.Error.CUSTOM_EXCEPTION.getCode()&&trade.getExceptNames().size()>0){
            String exceptMsg = String.join(",", trade.getExceptNames());
            StringBuffer sb  = new StringBuffer(validator.getMessage());
            sb.append("[");
            sb.append(exceptMsg);
            sb.append("]");
            validator.setMessage(sb.toString());
        }

        reCheckOrderException(trade, code, validator);

        // 如果当前商品没有对应关系异常，则忽略
        if (ignoreRelationChanged && validator.hasError() && TradeValidator.Error.RELATION_CHANGED.getCode() == validator.getCode() && !haveRelationChangedException(trade, code)) {
            validator.setCode(0);
            validator.setMessage("");
        }

        if (validator.hasError() && TradeValidator.Error.INSUFFICIENT.getCode() != validator.getCode()) {
            if (TradeValidator.Error.NOT_FOUND.getCode() == validator.getCode() && code != null && DataUtils.checkLongNotEmpty(code.getSid())) {
                validator.setMessage(String.format("订单[%s]不存在或已归档!", code.getSid()));
            }
            throw new TradeValidatorException(validator.getCode(), validator.getMessage());
        }
    }

    /**
     * 校验子订单异常
     * validator.check(staff, trade)抛出INSUFFICIENT、Exception时没校验对应关系异常
     * @param trade
     * @param code
     * @param validator
     */
    private static void reCheckOrderException(Trade trade, WaveUniqueCode code, TradeValidator validator) {
        if (trade == null || validator == null) {
            return;
        }
        if (!validator.hasError()
                || (TradeValidator.Error.EXCEPTION.getCode() != validator.getCode()
                && TradeValidator.Error.INSUFFICIENT.getCode() != validator.getCode())) {
            return;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        // 没有唯一码时订单维度校验对应关系异常
        if (code == null) {
            if (orders.stream().anyMatch(order -> Objects.equals(1, order.getRelationChanged()))) {
                validator.setError(TradeValidator.Error.RELATION_CHANGED);
            }
        } else {
            // 有唯一码时子订单维度校验对应关系异常
            for (Order order : orders) {
                if (Objects.equals(order.getId(), code.getOrderId())) {
                    if (Objects.equals(1, order.getRelationChanged())) {
                        validator.setError(TradeValidator.Error.RELATION_CHANGED);
                    } else {
                        validator.setCode(0);
                        validator.setMessage("");
                    }
                }
            }
        }
    }

    public static boolean haveRelationChangedException(Order order) {
        return (order != null && (order.getRelationChanged() != null && order.getRelationChanged() - 1 == 0));
    }

    public static boolean haveRelationChangedException(Trade trade, WaveUniqueCode code) {
        if (trade == null || code == null) {
            return false;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            return false;
        }
        Map<Long, Order> orderMap = orders.stream().collect(toMap(Order::getId, (a -> a), (a1, a2) -> a1));
        Order order = orderMap.get(code.getOrderId());
        if (order != null && (order.getRelationChanged() != null && order.getRelationChanged() - 1 == 0)) {
            return true;
        }
        return false;
    }

    /**
     * 获取多打印机快递模版Id
     * @param multiplePrinterSettings
     * @return
     */
    public static List<String> getMultiplePrinterTemplates(String multiplePrinterSettings) {
        if (StringUtils.isEmpty(multiplePrinterSettings)) {
            return Collections.EMPTY_LIST;
        }
        JSONObject jsonObject = JSONObject.parseObject(multiplePrinterSettings);
        return new ArrayList<>(jsonObject.keySet());
    }

    /**
     * 拦截卖家备注、系统备注
     * @param interceptRemarks
     * @param trade
     */
    public static void interceptRemarks(String interceptRemarks, Trade trade) {
        if (StringUtils.isEmpty(interceptRemarks)) {
            return;
        }
        String sellerMemo = trade.getSellerMemo();
        String sysMemo = trade.getSysMemo();
        if (StringUtils.isEmpty(sellerMemo) && StringUtils.isEmpty(sysMemo)) {
            return;
        }

        List<String> intercepts = Lists.newArrayList(interceptRemarks.split(","));
        if (StringUtils.isNotEmpty(sellerMemo)
                && intercepts.stream().anyMatch(sellerMemo::contains)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_SELLER_MEMO, "备注异常，请核对！");
        }
        if (StringUtils.isNotEmpty(sysMemo)
                && intercepts.stream().anyMatch(sysMemo::contains)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_SYS_MEMO, "备注异常，请核对！");
        }
    }

    /**
     * 判断唯一码是否包含驳回标签
     * @param code
     * @return
     */
    public static boolean isIncludeRejectTag(WaveUniqueCode code) {
        if (code == null || StringUtils.isBlank(code.getTagIds())) {
            return false;
        }
        for (String tagId : code.getTagIds().split(",")) {
            if (OrderUniqueCodeTagEnum.REJECT.getId().equals(Long.valueOf(tagId))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断唯一码是否可以取消（复制新增），条件：等待收货/等待拣选/已拣选
     * @param code
     * @return
     */
    public static boolean canCancelReject(WaveUniqueCode code) {
        return (Objects.equals(2, code.getStatus())
                || Objects.equals(4, code.getStatus())
                || Objects.equals(3, code.getStatus())) && (code.getSid() != null && code.getSid() > 0L);
    }

    /**
     * 判断唯一码是否可以退档换货 状态已退回 标签是售后转发
     *
     * @param code
     * @return
     */
    public static boolean canReturnExchange(WaveUniqueCode code) {
        if (StringUtils.isBlank(code.getTagIds())) {
            return false;
        }
        List<String> tagIds = Lists.newArrayList(code.getTagIds().split(","));
        return Objects.equals(14, code.getStatus()) && tagIds.contains("5") && (code.getSid() != null && code.getSid() > 0L);
    }

    public static boolean canPickReturn(WaveUniqueCode code) {
        return Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.PICKED.getType()) && !DataUtils.checkLongNotEmpty(code.getSid());
    }

    /**
     * 获取订单唯一码首个自定义数字
     * @param config
     * @return
     */
    public static Integer getCustomFirstDigitNum(OrderUniqueCodeConfig config) {
        if (MapUtils.isEmpty(config.getExtConfigMap())) {
            return 0;
        }
        if (Objects.equals(config.getExtConfigMap().get(OrderUniqueCodeExtConfigEnum.CUSTOM_FIRST_DIGIT_SWITCH.getKey()), 0)) {
            return 0;
        }
        return (Integer) config.getExtConfigMap().getOrDefault(OrderUniqueCodeExtConfigEnum.CUSTOM_FIRST_DIGIT_NUM.getKey(), 0);
    }

    /**
     * 唯一码长度为13位数字，位数不足用0填充
     * @param uniqueCode
     * @return
     */
    public static String fillUniqueCodeZero(String uniqueCode, Integer length) {
        return fillUniqueCodeZero(uniqueCode, length, 0);
    }

    public static String fillUniqueCodeZero(String uniqueCode, Integer length, Integer customFirstDigitNum) {
        if (length == null || length <= 0) {
            length = UNIQUE_CODE_LEN;
        }
        if (StringUtils.isEmpty(uniqueCode) || uniqueCode.length() >= length) {
            return uniqueCode;
        }
        StringBuilder append = new StringBuilder();
        for (int i = 0; i < (length - uniqueCode.length()); i++) {
            if (i == 0) {
                append.append(DataUtils.getZeroIfDefaultI(customFirstDigitNum));
                continue;
            }
            append.append("0");
        }
        return append.append(uniqueCode).toString();
    }

    /**
     * 根据规则 反射生成商品唯一码
     * @param code
     * @param id
     * @param rule
     * @return
     */
    public static String buildUniqueCodeByRule(WaveUniqueCode code, String id, ItemUniqueCodeGenerateRule rule) throws SQLException {
        String customFieldSort = rule.getCustomFieldSort();
        if (StringUtils.isEmpty(customFieldSort)) {
            return id;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        List<String> uniqueCodeList = new ArrayList<>();
        for (String customField : customFieldSort.split(",")) {
            Method method = ReflectionUtils.findMethod(WaveUniqueCode.class,
                    "get" + customField.substring(0, 1).toUpperCase() + customField.substring(1));
            if (method == null) {
                continue;
            }
            ReflectionUtils.makeAccessible(method);
            Object o = ReflectionUtils.invokeJdbcMethod(method, code);
            if (o != null) {
                if (o instanceof Date) {
                    uniqueCodeList.add(sdf.format(o));
                } else {
                    uniqueCodeList.add(o.toString());
                }
            }
        }
        uniqueCodeList.add(fillUniqueCodeZero(id, 6));
        return StringUtils.join(uniqueCodeList, Objects.equals(CommonConstants.JUDGE_YES, rule.getStyle()) ? "-" : "");
    }

    public static boolean isValidDate(String str, SimpleDateFormat format) {
        boolean convertSuccess = true;
        try {
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            format.parse(str);
        } catch (ParseException e) {
            convertSuccess = false;
        }
        return convertSuccess;
    }

    public static boolean isItemUniqueCode(WaveUniqueCode code) {
        return Objects.equals(2, code.getType());
    }

    public static boolean isGenericCode(WaveUniqueCode code) {
        return Objects.equals(6, code.getType());
    }

    public static boolean isGenericCode(Integer type) {
        return Objects.equals(6, type);
    }

    public static boolean cannotReceive(WaveUniqueCode code) {
        return cannotReceive(code, false);
    }

    public static boolean cannotReceive(WaveUniqueCode code, boolean allowCancel) {
        return !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType(), code.getStatus())
                && !(Objects.equals(OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), code.getStatus()) && Objects.equals(code.getStockStatus(), CommonConstants.JUDGE_NO))
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_IN.getType(), code.getStatus())
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), code.getStatus());
    }

    public static boolean isItemUniqueCode(Trade trade) {
        return trade.getWaveId() != null && trade.getWaveId() == -6L;
    }

    public static String getTemplateKey(Integer templateType, Long templateId) {
        if (templateType == null || templateType == 0) {
            return templateId.toString();
        }
        return "w" + templateId.toString();
    }

    private static boolean isContain(List<WaveItem> collection, WaveItem item) {
        if (collection == null || CollectionUtils.isEmpty(collection)) {
            return false;
        }
        return collection.contains(item);
    }

    /**
     * 获取无法生成唯一码的订单
     * 未生成订单 = (全部订单 - 可以生成唯一码 - 热销商品)
     * @param orderItems 全部订单
     * @param havePositions 可以生成唯一码
     * @param hotSales 热销商品
     * @return
     */
    public static Set<Long> getUnGenerateSids(List<WaveItem> orderItems, List<WaveItem> havePositions, List<WaveItem> hotSales) {
        Set<Long> unGenerates = new HashSet<>();
        if (CollectionUtils.isEmpty(orderItems)) {
            return unGenerates;
        }

        return orderItems.stream().filter(item -> !isContain(havePositions, item) && !isContain(hotSales, item))
                .map(WaveItem::getSid).collect(Collectors.toSet());
    }

    public static Set<Long> getUnGenerateSids(List<WaveItem> orderItems, List<WaveItem> havePositions) {
        Set<Long> unGenerates = new HashSet<>();
        if (CollectionUtils.isEmpty(orderItems)) {
            return unGenerates;
        }

        return orderItems.stream().filter(item -> !isContain(havePositions, item)).map(WaveItem::getSid).collect(Collectors.toSet());
    }

    public static boolean isTrue(Integer value) {
        return value != null && value == 1;
    }

    /**
     * 校验打印模版
     * @param param
     * @param trade
     */
    public static void validateTemplate(WavePickingParam param, Trade trade) {
        if (trade == null || param == null
                || trade.getTemplateType() == null
                || trade.getTemplateType() == 0 // 非电子面单不在这里校验
                || !checkLongNotEmpty(trade.getTemplateId())) {
            return;
        }
        Long templateId = trade.getTemplateId();
        if (OrderUniqueCodeUtils.getMultiplePrinterTemplates(param.getMultiplePrinterSettings()).contains(templateId.toString())) {
            return;
        }
        Integer openDefaultPrinter = param.getOpenDefaultPrinter();
        if (openDefaultPrinter != null && openDefaultPrinter == 1) {
            return;
        }
        throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_PRINT_TEMPLATE, "订单快递模板与打印机设置不符，请调整打印机设置！");
    }

    public static Boolean checkLongNotEmpty(Long value){
        return value != null && value > 0L;
    }

    /**
     * 获取下架店铺id
     * @param orderUniqueCodeConfig 唯一码配置
     * @param value                 0 获取单个下架配置的店铺，1获取全部下架配置的店铺
     */
    public static Set<Long> getOffShelfShopUserIds(OrderUniqueCodeConfig orderUniqueCodeConfig, Integer value) {
        List<OrderUniqueCodeOffShelfConfig.OffShelfShop> offShelfShops = Optional.ofNullable(orderUniqueCodeConfig.getOffShelfConfig()).map(OrderUniqueCodeOffShelfConfig::getOffShelfShopList).orElse(Lists.newArrayList());
        return offShelfShops.stream().filter(data -> Objects.equals(data.getOffShelfMode(), value)).map(OrderUniqueCodeOffShelfConfig.OffShelfShop::getUserId).collect(toSet());
    }

    /**
     * 是否全部下架
     */
    public static boolean isOffAll(OrderUniqueCodeConfig orderUniqueCodeConfig, Set<Long> offShelfAllShops, Set<Long> offShelfSingleShops, WaveUniqueCode code) {
        boolean offAll = Objects.equals(orderUniqueCodeConfig.getOffShelfMode(), CommonConstants.JUDGE_YES);
        if (offShelfAllShops.contains(code.getUserId())) {
            offAll = true;
        } else if (offShelfSingleShops.contains(code.getUserId())) {
            offAll = false;
        }
        return offAll;
    }

    /**
     * 这家店铺的标签是否全部下架
     */
    public static boolean isOffAllByUserId(OrderUniqueCodeConfig orderUniqueCodeConfig, Set<Long> offShelfAllShops, Long userId) {
        return OrderUniqueCodeUtils.isTrue(orderUniqueCodeConfig.getOffShelfMode()) || offShelfAllShops.contains(userId);
    }

    /**
     * 唯一码是否已收货 | 已拣选
     * @param codeStatus
     * @return
     */
    public static boolean isReceivedPicked(Integer codeStatus) {
        if (codeStatus == null) {
            return false;
        }

        return !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PICK.getType(), codeStatus)
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType(), codeStatus)
                && !Objects.equals(OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType(), codeStatus)
                && !Objects.equals(OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), codeStatus);
    }
    /**
     * 是否使用pgl查询
     */
    public static boolean isPageUserPgl(OrderUniqueCodeQueryParams params) {
        // 查询条件创建时间超过15天走pg
        boolean condition1 = (params.isCountUserPgl() && params.getPage() != null
                && params.getCreateBegin() != null
                && params.getCreateEnd() != null
                && params.getCreateEnd().getTime() - params.getCreateBegin().getTime() >= PAGE_UNIQUE_TIME_INTERVAL);
        // 无时间按照商家编码模糊查询走pg
        boolean condition2 = (params.isCountUserPgl() && Objects.equals(params.getSkuOuterIdsVagueType(), 1) && !CollectionUtils.isEmpty(params.getOuterIds()) && params.getCreateBegin() == null && params.getCreateEnd() == null);
        // 打印时间超过15天走pg
        boolean condition3 = (params.isCountUserPgl() && params.getPage() != null
                && params.getPrintTimeBegin() != null
                && params.getPrintTimeEnd() != null
                && params.getPrintTimeBegin().getTime() - params.getPrintTimeEnd().getTime() >= PAGE_UNIQUE_TIME_INTERVAL);
        return condition1 || condition2 || condition3;
    }

    /**
     * count是否使用pgl查询
     * @param params
     * @return
     */
    public static boolean isCountUsePal(OrderUniqueCodeQueryParams params) {
        return params.getRelationShareUrl() == null && !params.getQueryMigrateData() && !isPierceThroughQuery(params)
                && !(params.getCreateBegin() != null && params.getCreateEnd() != null && (params.getCreateEnd().getTime() - params.getCreateBegin().getTime() < ONE_DAY_TIME) && !CollectionUtils.isEmpty(params.getOuterIds()))
                && !(params.getPrintTimeBegin() != null && params.getPrintTimeEnd() != null && (params.getPrintTimeBegin().getTime() - params.getPrintTimeEnd().getTime() < ONE_DAY_TIME) && !CollectionUtils.isEmpty(params.getOuterIds()));
    }

    /**
     * 查询订单唯一码-判断是否需要查询冷表数据
     */
    public static boolean needQueryMigrateData(OrderUniqueCodeQueryParams params){
        if (params == null) {
            return false;
        }
        return params.getPage() == null && (!CollectionUtils.isEmpty(params.getSids())
                || !CollectionUtils.isEmpty(params.getUniqueCodes())
                || !CollectionUtils.isEmpty(params.getUniqueCodeIds())
                || !CollectionUtils.isEmpty(params.getShortIds())
                || !CollectionUtils.isEmpty(params.getBusinessCodes()));
    }

    /**
     * 查询商品唯一码-判断是否需要查询冷表数据
     */
    public static boolean needQueryMigrateData(ItemUniqueCodeQueryParams params){
        if (params == null) {
            return false;
        }
        return params.getPage() == null && (!CollectionUtils.isEmpty(params.getSids())
                || !CollectionUtils.isEmpty(params.getUniqueCodes())
                || !CollectionUtils.isEmpty(params.getUniqueCodeIds())
                || !CollectionUtils.isEmpty(params.getShortIds()));
    }

    /**
     * 商品标签列表穿透查询
     * @param params
     */
    public static void pierceThroughQuery(OrderUniqueCodeQueryParams params) {
        if (params == null) {
            return;
        }
        if (isPierceThroughQuery(params)) {
            params.setCreateBegin(null);
            params.setCreateEnd(null);
            params.setCustomPrintTimeBegin(null);
            params.setCustomPrintTimeEnd(null);
            params.setPrintTimeBegin(null);
            params.setPrintTimeEnd(null);
            params.setPayTimeBegin(null);
            params.setPayTimeEnd(null);
            params.setOffTimeBegin(null);
            params.setOffTimeEnd(null);
        }
    }

    public static boolean isPierceThroughQuery(OrderUniqueCodeQueryParams params) {
        return !CollectionUtils.isEmpty(params.getUniqueCodes())
                || !CollectionUtils.isEmpty(params.getShortIds())
                || !CollectionUtils.isEmpty(params.getRelationCodes())
                || !CollectionUtils.isEmpty(params.getSids())
                || !CollectionUtils.isEmpty(params.getTids());
    }

    public static Map<String, List<Long>> mapToGroupByValue(Map<Long, String> idItemMap) {
        Map<String, List<Long>> res = Maps.newHashMap();
        if (CollectionUtils.isEmpty(idItemMap)) {
            return res;
        }

        for (Map.Entry<Long, String> entry : idItemMap.entrySet()) {
            String itemKey = entry.getValue();
            List<Long> ids = res.get(itemKey);
            if (CollectionUtils.isEmpty(ids)) {
                res.put(itemKey, Lists.newArrayList(entry.getKey()));
            } else {
                ids.add(entry.getKey());
            }
        }

        return res;
    }

    /**
     * 是否是订单唯一码
     * @param waveId
     * @return
     */
    public static boolean isOrderUniqueCode(Long waveId) {
        return Objects.equals(WaveTypeEnum.ORDER_UNIQUE_CODE.getWaveId(), waveId);
    }

    /**
     * 判断唯一码打印时间是否超过N天
     */
    public static boolean checkPrintTimeIsOverTime(Date printTime, int day) {
        if (printTime == null) {
            throw new IllegalArgumentException("日期不能为空!");
        }
        long time = printTime.getTime();
        long nowTime = System.currentTimeMillis();

        return nowTime - time > day * 24 * 3600 * 1000;
    }

    /**
     * 判断子单是否在退款中
     * @param order
     * @return
     */
    public static boolean isRefunding(Order order) {
        return order != null && (Objects.equals(order.getRefundStatus(), Order.REFUND_WAIT_SELLER_AGREE)
                || Objects.equals(order.getRefundStatus(), Order.REFUND_WAIT_BUYER_RETURN_GOODS)
                || Objects.equals(order.getRefundStatus(), Order.REFUND_WAIT_SELLER_CONFIRM_GOODS)
                || Objects.equals(order.getRefundStatus(), Order.REFUND_SELLER_REFUSE_BUYER));
    }

    /**
     * 判断子单是否是套件明细
     * @param order
     * @return
     */
    public static boolean isSuitDetail(Order order) {
        return order != null && Objects.equals(order.getType(), 2) && (order.getCombineId() != null && order.getCombineId() > 0);
    }

    /**
     * 增量新增/删除标签
     * @param waveUniqueCode
     * @param id
     * @param type 操作类型 0-新增 1-删除
     * @return
     */
    public static String incrementBuildTagIds(WaveUniqueCode waveUniqueCode, Long id, Integer type) {
        if (type == null || id == null || waveUniqueCode == null) {
            throw new IllegalArgumentException("唯一码/标签Id/操作类型参数不能为空! ");
        }

        if (Objects.equals(type, CommonConstants.VALUE_NO)) {
            // 避免重复添加标签
            if (StringUtils.isNotEmpty(waveUniqueCode.getTagIds()) && ArrayUtils.toLongList(waveUniqueCode.getTagIds()).contains(id)) {
                return waveUniqueCode.getTagIds();
            }
            return StringUtils.isEmpty(waveUniqueCode.getTagIds()) ? id.toString() : waveUniqueCode.getTagIds() + "," + id.toString();
        } else if (Objects.equals(type, CommonConstants.VALUE_YES)) {
            if (StringUtils.isEmpty(waveUniqueCode.getTagIds())) {
                return null;
            }

            List<String> tagIds = new ArrayList<>(Arrays.asList(waveUniqueCode.getTagIds().split(",")));
            if (tagIds.contains(id.toString())) {
                tagIds.remove(id.toString());
                return CollectionUtils.isEmpty(tagIds) ? null : String.join(",", tagIds);
            } else {
                return waveUniqueCode.getTagIds();
            }
        } else {
            throw new IllegalArgumentException("唯一码标签不支持的操作类型! ");
        }
    }

    public static List<WaveUniqueCodeLog> buildLogs(Staff staff, List<WaveUniqueCode> uniqueCodes, Throwable throwable, WaveUniqueOpType type,String content) {
        List<WaveUniqueCodeLog> logs = Lists.newArrayListWithCapacity(uniqueCodes.size());
        String errorMsg = throwable == null ? "" : throwable.getMessage();
        int isError = throwable != null ? 1 : 0;
        for (WaveUniqueCode uniqueCode : uniqueCodes) {
            WaveUniqueCodeLog log = new WaveUniqueCodeLog();
            log.setWaveId(uniqueCode.getWaveId());
            log.setUniqueCode(uniqueCode.getUniqueCode());
            log.setIsError(isError);
            log.setErrorMsg(errorMsg);
            log.setOpType(type);
            if(content!=null) {
                log.setContent(content);
            }else{
                log.setContent("唯一码：" + uniqueCode.getUniqueCode() + getLogContent(type));
            }
            log.setStaffId(staff.getId());
            log.setStaffName(staff.getName());
            log.setStatus(uniqueCode.getStatus());
            logs.add(log);
        }
        return logs;
    }

    private static String getLogContent(WaveUniqueOpType type) {
        switch (type) {
            case ORDER_OFF_SHELF:
                return "扫描下架";
            case ORDER_RECEIVE:
                return "扫描收货";
            case ORDER_CANCEL:
                return "取消";
            case TRANSLATION:
                return "暂存区平移，库存位置变更为销退暂存区";
            default:
                return "";
        }
    }

    /**
     * 筛选固定无货
     */
    public static List<WaveUniqueCode> filterForceSupplierOutStock(List<WaveUniqueCode> insertCodes, Set<Long> allocateGoodsSupplierIds) {
        return insertCodes.stream().filter(c -> c.getSupplierId() != null && !c.getSupplierId().equals(0L)
                && !OrderUniqueCodeUtils.isTrue(c.getStockStatus()) && allocateGoodsSupplierIds.contains(c.getSupplierId())).collect(toList());
    }

    /**
     * 是否需要执行库存变更
     * 加工状态为【加工中】/【完成】的唯一码状态变时库存不变更（打印标签/收货/验货出库）
     * @param waveUniqueCode
     * @return
     */
    public static boolean needChangeStockByProcessStatus(WaveUniqueCode waveUniqueCode) {
        if (Objects.equals(waveUniqueCode.getProcessStatus(), CommonConstants.VALUE_YES) ||  Objects.equals(waveUniqueCode.getProcessStatus(), 2)) {
            if (Objects.equals(waveUniqueCode.getType(), CommonConstants.VALUE_YES) && Objects.equals(waveUniqueCode.getStatus(), OrderUniqueCodeStatusEnum.WAIT_PICK.getType())) {
                return false;
            }
        }

        return true;
    }

    public static String getUserIdNames(Map<Long, String> userIdShopTitleMap, List<Long> userIdList) {
        if (MapUtils.isEmpty(userIdShopTitleMap) || org.apache.commons.collections.CollectionUtils.isEmpty(userIdList)) {
            return "";
        }
        List<String> names = new ArrayList<>();
        for (Long userId : userIdList) {
            String name = userIdShopTitleMap.get(userId);
            if (StringUtils.isNotEmpty(name)) {
                names.add(name);
            }
        }

        return Strings.join(",", names);
    }

    public static String buildItemKey(Long sysItemId, Long sysSkuId) {
        return sysItemId + KEY_JOINER + (sysSkuId == null || sysSkuId < 0L ? 0L : sysSkuId);
    }

    public static void baseCheckChangeItem(TradeChangeItemMiddle middle) {
        Assert.hasText(middle.getMainOuterId(), "更换前主商家编码不能为空！");
        Assert.hasText(middle.getSkuOuterId(), "更换前规格商家编码不能为空！");
        Assert.isTrue(DataUtils.checkLongNotEmpty(middle.getSysItemId()), "更换前主商品Id不能为空！");

        Assert.hasText(middle.getAfterOuterId(), "更换后商家编码不能为空！");
        Assert.isTrue(DataUtils.checkLongNotEmpty(middle.getAfterSysItemId()), "更换后主商品Id不能为空！");

        Assert.isTrue(!Objects.equals(buildItemKey(middle.getSysItemId(), middle.getSysSkuId()),
                buildItemKey(middle.getAfterSysItemId(), middle.getAfterSysSkuId())), "更换前后商品不能相等！");
        if (StringUtils.isNotEmpty(middle.getSids())) {
            try {
                ArrayUtils.toLongArray(middle.getSids());
            } catch (Exception e) {
                throw new IllegalArgumentException("订单号需要为数字！");
            }
        }
    }

    /**
     * 如果填写了订单号，则不需要填写多个字段
     * @param middle
     */
    public static void clearSidRelationFields(TradeChangeItemMiddle middle) {
        String sids = middle.getSids();
        if (StringUtils.isEmpty(sids)) {
            return;
        }
        middle.setUserIds("");
        middle.setUserIdShortNames("");
        middle.setUserIdsName("");
        middle.setNotNeedSplitWarehouseIds("");
    }

    /**
     * 判断是否开启单多混合扫描
     * @return
     */
    public static boolean isOpenHybridScan(Integer type) {
        if (Objects.equals(type, CommonConstants.VALUE_YES) || Objects.equals(type, 2) || Objects.equals(type, 3)) {
            return true;
        }

        return false;
    }

    public static Map<Long, Order> getFullTradeOrders(Trade trade) {
        List<Order> orderList = new ArrayList<>();
        for (Order order : TradeUtils.getOrders4Trade(trade)) {
            List<Order> suits = order.getSuits();
            if (!CollectionUtils.isEmpty(suits) && order.isSuit()) {
                // 套件本身
                orderList.add(order);
                // 套件明细
                for (Order suit : suits) {
                    orderList.add(suit);
                }
            } else {
                orderList.add(order);
            }
        }
        return orderList.stream().collect(Collectors.toMap(Order::getId, a -> a, (c1, c2) -> c1));
    }

    public static List<String> getUniqueCodeAndOrderIdsList(List<WaveUniqueCode> codes) {
        List<String> result = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            result.add(code.getUniqueCode() + "_" + code.getOrderId());
        }
        return result;
    }

    public static List<String> getFullOrderAndCombineId(Map<Long, Order> fullOrderMap) {
        List<String> result = new ArrayList<>();
        for (Map.Entry<Long, Order> entry : fullOrderMap.entrySet()) {
            Order order = entry.getValue();
            result.add(order.getId() + "_" + order.getCombineId());
        }
        return result;
    }

    public static List<String> buildSplitOrderLog(List<Order> splitOrders) {
        List<String> logs = new ArrayList<>();
        if (CollectionUtils.isEmpty(splitOrders)) {
            return logs;
        }
        for (Order o : splitOrders) {
            logs.add("orderId:" + o.getId() + ",num:" + o.getNum() + ",type:" + o.getType() + ",splitNum:" + o.getSplitNum() + ",combineId:" + o.getCombineId());
        }
        return logs;
    }

    public static String buildQueryBySidsContainMergeTradeResult(List<Trade> trades) {
        List<String> logs = new ArrayList<>();
        if (CollectionUtils.isEmpty(trades)) {
            return "";
        }

        for (Trade trade : trades) {
            logs.add("【sid：" + trade.getSid() + " merge_sid：" + trade.getMergeSid() + "]");
        }
        return Strings.join(",", logs);
    }
}
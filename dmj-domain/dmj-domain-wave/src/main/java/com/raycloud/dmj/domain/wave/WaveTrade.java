package com.raycloud.dmj.domain.wave;

import com.raycloud.dmj.domain.trades.Trade;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.wave.model.WaveTradeSkuGoodVO;
import com.raycloud.dmj.domain.wave.vo.WaveOrderVO;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.*;

/**
 * 波次订单
 * Created by shaoxianchang on 2017/8/22.
 */
@Table(name = "wave_trade", routerKey = "waveSortingDbNo")
public class WaveTrade extends Model {
    private static final long serialVersionUID = -952388406386674044L;

    public static final int TRADE_WAVE_STATUS_IN = 0;

    public static final int TRADE_WAVE_STATUS_OVER = 1;

    public static final int TRADE_WAVE_STATUS_NOT = 2;

    public static final int TRADE_WAVE_STATUS_IN_AND_OVER = 10;

    private Long id;

    private Long waveId;

    private Long sid;

    private String code;

    private Long newSid;

    private Long positionNo;

    private String positionCode;

    private String pickingCode;

    private String subSource;

    private Date payTime;

    private Date expressPrintTime;

    private String outSid;

    private Integer templateType;

    private Long templateId;

    private String templateName;

    /**
     * 订单下的商品数量
     */
    private Integer itemNum;

    /**
     * 订单下的商品种类数
     */
    private Integer itemKindNum;

    /**
     * 商品单品种类, 最小粒度
     */
    private Integer singleItemKindNum;
    /**
     * 单品数量
     */
    private Integer singleItemNum;

    private String sysStatus;

    private Long mergeSid;

    /**
     * 订单系统状态的中文说明
     */
    private String chSysStatus;

    private Set<String> exceptions;

    private List<String> exceptNames;

    /**
     * outerId1*num1,outerId2*num2
     * 商品商家编码拼接字符串
     */
    private String outerIdConcat;


    /**
     * itemOuterId1*num1,itemOuterId2*num2
     * 主商品编码拼接字符串
     */
    private String itemOuterIdConcat;

    /**
     * 订单波次中的状态 0：未完结，1：已完结，2：已剔出
     */
    private Integer tradeWaveStatus;

    private Integer printTimes;

    /**
     * 重量
     */
    private Double weight;

    /**
     * 订单净重，由子订单综合计算得出，参照Order#netWeight
     */
    private Double netWeight;

    private Double volume;

    private Long companyId;

    private Integer dbNo;
    
    /**
     * 店铺用户编号
     */
    private Long userId;
    
    /**
     * 店铺名称
     */
    private   String  shopName;

    /**
     * 不生成波次原因
     */
    private String notInWaveReason;

    /**
     * 波次同一位置订单数
     */
    private Integer  positionSidNum;

    /**
     * 波次同一位置订单索引
     */
    private Integer positionSidIndex;

    /**
     * 播种匹配状态
     */
    private Integer matchedStatus;

    /**
     * 包装时间
     */
    private Date packTime;

    /**
     * 包装人id
     */
    private Long packStaffId;

    /**
     * 订单系统短号
     */
    private Long shortId;

    /**
     * 是否异常订单
     */
    private Integer isExcep;

    /**
     * 异常类型 0 正常 1 缺货 2 踢出 3 订单异常
     */
    private Integer exceptionPositionType;

    /**
     * 退出波次类型字符串
     */
    private String outBusinessType;

    /**
     * 买家留言
     */
    private String buyerMessage;

    private String sysMemo;

    private String ruleName;

    /**
     * 未加入波次时间
     */
    private Date notInWaveTime;

    /**
     * 备注/旗帜（支持主子订单）
     */
    private List<WaveTradeMemo> waveTradeMemoList;
    /**
     * 平台状态
     */
    private String unifiedStatus;
    /**
     * 标签名称
     */
    private String tagNames;
    /**
     * 商品货位
     */
    List<WaveTradeSkuGoodVO> waveTradeSkuGoodVOList;

    /**
     * 生产日期*SKU*数量
     */
    private List<String> productDates;

    /**
     * 批次*SKU*数量
     */
    private List<String> batchNos;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 平台实付金额
     */
    private Double payment;

    /**
     * @see Trade#getTagIds()
     */
    private String tagIds;

    /**
     * 系统异常(包含单独字段和itemExcep中的异常)
     */
    private Set<Integer> exceptIdSet = Sets.newHashSet();

    /**
     * 自定义异常Id是，逗号拼接
     */
    private String exceptIds;

    private List<WaveOrderVO> orders;

    private String logisticsCompanyName;

    private String expressName;

    /**
     * 自定义异常标签
     * @return
     */
    private List<TradeTag> exceptTradeTags = Lists.newArrayList();

    /**
     * 空包
     */
    private Integer scalping;

    /**
     * 订单来源
     */
    private String source;
    /**
     * 平台订单号
     */
    private String tid;

    /**
     * 不持久化,计算字段
     */
    private String postFee;

    /**
     * 用来展示一包多单的数量
     */
    private Integer outSidNumber;

    /**
     * 一包多单子单物流单号集合
     * @return
     */
    private List<String> childOutSids;

    public Integer getOutSidNumber() {
        return outSidNumber;
    }

    public void setOutSidNumber(Integer outSidNumber) {
        this.outSidNumber = outSidNumber;
    }

    public List<String> getChildOutSids() {
        return childOutSids;
    }

    public void setChildOutSids(List<String> childOutSids) {
        this.childOutSids = childOutSids;
    }
    /**
     * 不是在波次具体原因
     */
    private LinkedHashMap<String, String> notInWaveSpecificReasonMap;

    public Set<Integer> getExceptIdSet() {
        return exceptIdSet;
    }

    public void setExceptIdSet(Set<Integer> exceptIdSet) {
        this.exceptIdSet = exceptIdSet;
    }

    public String getExceptIds() {
        return exceptIds;
    }

    public void setExceptIds(String exceptIds) {
        this.exceptIds = exceptIds;
    }

    public List<TradeTag> getExceptTradeTags() {
        return exceptTradeTags;
    }

    public void setExceptTradeTags(List<TradeTag> exceptTradeTags) {
        this.exceptTradeTags = exceptTradeTags;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getPayment() {
        return payment;
    }

    public void setPayment(Double payment) {
        this.payment = payment;
    }

    public Long getShortId() {
        return shortId;
    }

    public void setShortId(Long shortId) {
        this.shortId = shortId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getOutSid() {
        return outSid;
    }

    public void setOutSid(String outSid) {
        this.outSid = outSid;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Integer getItemNum() {
        return itemNum;
    }

    public void setItemNum(Integer itemNum) {
        this.itemNum = itemNum;
    }

    public Integer getItemKindNum() {
        return itemKindNum;
    }

    public void setItemKindNum(Integer itemKindNum) {
        this.itemKindNum = itemKindNum;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Date getExpressPrintTime() {
        return expressPrintTime;
    }

    public void setExpressPrintTime(Date expressPrintTime) {
        this.expressPrintTime = expressPrintTime;
    }

    public Set<String> getExceptions() {
        return exceptions;
    }

    public void setExceptions(Set<String> exceptions) {
        this.exceptions = exceptions;
    }

    public List<String> getExceptNames() {
        return exceptNames;
    }

    public void setExceptNames(List<String> exceptNames) {
        this.exceptNames = exceptNames;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus;
    }

    public Long getPositionNo() {
        return positionNo;
    }

    public void setPositionNo(Long positionNo) {
        this.positionNo = positionNo;
    }

    public String getOuterIdConcat() {
        return outerIdConcat;
    }

    public void setOuterIdConcat(String outerIdConcat) {
        this.outerIdConcat = outerIdConcat;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public Integer getTradeWaveStatus() {
        return tradeWaveStatus;
    }

    public void setTradeWaveStatus(Integer tradeWaveStatus) {
        this.tradeWaveStatus = tradeWaveStatus;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getDbNo() {
        return dbNo;
    }

    public void setDbNo(Integer dbNo) {
        this.dbNo = dbNo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getChSysStatus() {
        return chSysStatus;
    }

    public void setChSysStatus(String chSysStatus) {
        this.chSysStatus = chSysStatus;
    }

    public String getPickingCode() {
        return pickingCode;
    }

    public void setPickingCode(String pickingCode) {
        this.pickingCode = pickingCode;
    }

    public Long getNewSid() {
        return newSid;
    }

    public void setNewSid(Long newSid) {
        this.newSid = newSid;
    }

    public Integer getPrintTimes() {
        return printTimes;
    }

    public void setPrintTimes(Integer printTimes) {
        this.printTimes = printTimes;
    }

    public Integer getSingleItemKindNum() {
        return singleItemKindNum;
    }

    public void setSingleItemKindNum(Integer singleItemKindNum) {
        this.singleItemKindNum = singleItemKindNum;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(Double netWeight) {
        this.netWeight = netWeight;
    }

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public String getItemOuterIdConcat() {
		return itemOuterIdConcat;
	}

	public void setItemOuterIdConcat(String itemOuterIdConcat) {
		this.itemOuterIdConcat = itemOuterIdConcat;
	}

    public String getNotInWaveReason() {
        return notInWaveReason;
    }

    public void setNotInWaveReason(String notInWaveReason) {
        this.notInWaveReason = notInWaveReason;
    }

    public Integer getMatchedStatus() {
        return matchedStatus;
    }

    public void setMatchedStatus(Integer matchedStatus) {
        this.matchedStatus = matchedStatus;
    }

	public Integer getPositionSidNum() {
		return positionSidNum;
	}

	public void setPositionSidNum(Integer positionSidNum) {
		this.positionSidNum = positionSidNum;
	}

    public Integer getPositionSidIndex() {
        return positionSidIndex;
    }

    public void setPositionSidIndex(Integer positionSidIndex) {
        this.positionSidIndex = positionSidIndex;
    }

    public Date getPackTime() {
        return packTime;
    }

    public void setPackTime(Date packTime) {
        this.packTime = packTime;
    }

    public Long getPackStaffId() {
        return packStaffId;
    }

    public void setPackStaffId(Long packStaffId) {
        this.packStaffId = packStaffId;
    }

	public Integer getIsExcep() {
		return isExcep;
	}

	public void setIsExcep(Integer isExcep) {
		this.isExcep = isExcep;
	}

    public Integer getExceptionPositionType() {
        return exceptionPositionType;
    }

    public void setExceptionPositionType(Integer exceptionPositionType) {
        this.exceptionPositionType = exceptionPositionType;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public Date getNotInWaveTime() {
        return notInWaveTime;
    }

    public void setNotInWaveTime(Date notInWaveTime) {
        this.notInWaveTime = notInWaveTime;
    }

    public List<WaveTradeMemo> getWaveTradeMemoList() {
        return waveTradeMemoList;
    }

    public void setWaveTradeMemoList(List<WaveTradeMemo> waveTradeMemoList) {
        this.waveTradeMemoList = waveTradeMemoList;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getSysMemo() {
        return sysMemo;
    }

    public void setSysMemo(String sysMemo) {
        this.sysMemo = sysMemo;
    }

    public String getOutBusinessType() {
		return outBusinessType;
	}

	public void setOutBusinessType(String outBusinessType) {
		this.outBusinessType = outBusinessType;
	}


    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public String getUnifiedStatus() {
        return unifiedStatus;
    }

    public void setUnifiedStatus(String unifiedStatus) {
        this.unifiedStatus = unifiedStatus;
    }

    public String getTagNames() {
        return tagNames;
    }

    public void setTagNames(String tagNames) {
        this.tagNames = tagNames;
    }

    public List<WaveTradeSkuGoodVO> getWaveTradeSkuGoodVOList() {
        return waveTradeSkuGoodVOList;
    }

    public void setWaveTradeSkuGoodVOList(List<WaveTradeSkuGoodVO> waveTradeSkuGoodVOList) {
        this.waveTradeSkuGoodVOList = waveTradeSkuGoodVOList;
    }

    public List<String> getProductDates() {
        return productDates;
    }

    public void setProductDates(List<String> productDates) {
        this.productDates = productDates;
    }

    public List<String> getBatchNos() {
        return batchNos;
    }

    public void setBatchNos(List<String> batchNos) {
        this.batchNos = batchNos;
    }

    public Integer getSingleItemNum() {
        return singleItemNum;
    }

    public void setSingleItemNum(Integer singleItemNum) {
        this.singleItemNum = singleItemNum;
    }

    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public List<WaveOrderVO> getOrders() {
        return orders;
    }

    public void setOrders(List<WaveOrderVO> orders) {
        this.orders = orders;
    }

    public String getLogisticsCompanyName() {
        return logisticsCompanyName;
    }

    public void setLogisticsCompanyName(String logisticsCompanyName) {
        this.logisticsCompanyName = logisticsCompanyName;
    }

    public String getExpressName() {
        return expressName;
    }

    public void setExpressName(String expressName) {
        this.expressName = expressName;
    }

    public Integer getScalping() {
        return scalping;
    }

    public void setScalping(Integer scalping) {
        this.scalping = scalping;
    }

    public LinkedHashMap<String, String> getNotInWaveSpecificReasonMap() {
        return notInWaveSpecificReasonMap;
    }

    public void setNotInWaveSpecificReasonMap(LinkedHashMap<String, String> notInWaveSpecificReasonMap) {
        this.notInWaveSpecificReasonMap = notInWaveSpecificReasonMap;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getSubSource() {
        return subSource;
    }

    public void setSubSource(String subSource) {
        this.subSource = subSource;
    }

    public Long getMergeSid() {
        return mergeSid;
    }

    public void setMergeSid(Long mergeSid) {
        this.mergeSid = mergeSid;
    }

    public String getPostFee() {
        return postFee;
    }

    public void setPostFee(String postFee) {
        this.postFee = postFee;
    }
}

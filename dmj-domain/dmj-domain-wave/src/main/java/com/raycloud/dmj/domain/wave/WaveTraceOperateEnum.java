package com.raycloud.dmj.domain.wave;

import com.raycloud.dmj.domain.Domain;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-05-23 09:57
 * @Description 波次操作枚举
 */
public enum WaveTraceOperateEnum {

    //批发配货波次
    CREATE_STALL(Domain.TRADE, "生成波次", "/trade/stall/saveSaleTradeWave", "com.raycloud.dmj.services.trades.support.wave.StallWaveService#createWaves"),
    CREATE_STALL_V2(Domain.TRADE, "生成波次", "/trade/stall/saveSaleTradeWave", "com.raycloud.dmj.services.trades.support.wave.StallWaveService#createWaves"),
    //手工生成订单波次
    CREATE_TRADE(Domain.TRADE, "生成波次", "/trade/wave/save", "com.raycloud.dmj.services.ec.wave.TradeWaveCreateListener#saveWaves"),
    //手工生成采退波次
    CREATE_RETURN(Domain.CAIGOU, "生成波次", "/caigou/return/create/wave", "com.raycloud.dmj.services.caigou.support.PurchaseReturnServiceImpl#createWaves"),
    //手工生成调拨波次
    CREATE_ALLOCATE(Domain.WMS, "生成波次", "/wms/allocate/out/create/wave", "com.raycloud.dmj.services.caigou.support.PurchaseReturnServiceImpl#createWaves"),
    
    //手工生成补货波次
    CREATE_REPLENISH(Domain.WMS, "生成波次", "/wms/replenish/save", "com.raycloud.dmj.services.wms.ReplenishTaskService#saveReplenishWaves"),

    //手工取消波次
    WAVE_CANCEL(Domain.TRADE, "取消波次", "/trade/wave/cancel", "com.raycloud.dmj.services.trades.support.wave.TradeWaveService#cancelWave"),
    WAVE_FORCE_FINISH(Domain.CAIGOU, "强制完成波次", "/caigou/return/back/batch", "com.raycloud.dmj.services.trades.support.wave.TradeWaveService#forceFinishWave"),

    //手工完结波次
    PRINT_FINISH(Domain.TRADE, "批量结束波次/播种结束波次/后置结束波次", "/trade/post/print/finish", "com.raycloud.dmj.services.trades.support.wave.TradePostPrintService#finishWave"),

    //批量获取电子面单
    WAVE_WAYBILL_GET(Domain.TRADE, "获取电子面单", "/trade/wave/waybill/get", "com.raycloud.dmj.services.ec.wave.TradeWaveWayBillGetListener#execute"),
    //波次生成自动获取电子面单号
    //WAVE_WAYBILL_GET_AUTO(Domain.SYS, "波次生成自动获取电子面单号", "/trade/wave/save", "com.raycloud.dmj.services.ec.wave.TradeWaveCreateListener#autoGetWaveWaybill"),

    //打印商家编码，编码：A1234,A3456,134,2345
    POST_PRINT_UNSEED_ITEMS(Domain.TRADE, "编码打印", "/trade/post/print/unseed/items/call", "com.raycloud.dmj.web.controllers.trades.TradePostPrintController#printCallUnSeedItemPositions"),

    //打印拿货单
    PRINT_WAVEGETTER_DATA(Domain.TRADE, "波次拿货单打印", "/trade/print/wavegetter/data", "com.raycloud.dmj.controller。TradePrintController#printPosGetterData"),

    //拣货单打印
    PRINT_PICKER_DATA(Domain.TRADE, "拣货单打印", "/print/picker/large", "com.raycloud.dmj.controller.TradePrintController#printLargePickerData"),

    //wavePrintType = null,波次前置打印，踢出波次，位置号：1,2,3
    //wavePrintType = 4,播种全部打印：位置号：1,2.3
    //wavePrintType = 7,后置全部打印：位置号：1,2,3
    //wavePrintType = 8,播种仅打已播：位置号：1,2.3
    PRINT_DATA_PRINTER(Domain.TRADE, "PC打印", "/trade/print/cloud/data/printer,/trade/print/wlb/data/printer", "com.raycloud.dmj.controller.TradePrintController#printCloudDataEnd,com.raycloud.dmj.controller.TradePrintController#printWlbDataEnd,"),

    //预发货波次订单，订单号：123456,123456,1234567
    WAVE_CONSIGN(Domain.TRADE, "平台上传发货", "/trade/wave/consign", "com.raycloud.dmj.business.logistics.ConsignWaveBusiness#addWaveTrace"),

    //重新分配库区
    WAVE_CHANGE_REGION(Domain.TRADE, "重新分配库区", "/trade/wave/change/region", "com.raycloud.dmj.services.ec.wave.TradeWaveGoodsChangeListener#onObserved"),

    //安排播种员：XXXX    安排拣选员：XXXX  安排拣选播种员：XXXX
    WAVE_ASSIGN(Domain.TRADE, "安排播种员", "/trade/wave/assign", "com.raycloud.dmj.web.controllers.trades.TradeWaveController.assignWaveOperator"),

    //人工拣选完成
    POST_PRINT_PICKING_HAND(Domain.TRADE, "PC拣选完成", "/trade/post/print/picking/hand", "com.raycloud.dmj.web.controllers.trades.TradePostPrintController#pickingByHand"),

    //订单踢出波次，订单号：123456，原因：重新审核/订单覆盖已发货等（PS：记录详细踢出波次的原因）
    TRADE_OUT_WAVE(Domain.TRADE, "订单踢出波次", "", "com.raycloud.dmj.services.trades.support.wave.TradeWaveService#handleRemoveWaveTrades"),
    // PDA 结束播种
    FINISH_WAVE_PDA(Domain.PDA, "结束播种", "", "com.raycloud.dmj.services.trades.support.wave.TradeWaveService#handleRemoveWaveTrades"),

    //纸质领取拣选任务，拣选方式：总拣总分/边拣边分
    WAVE_PICKING_PAPER_BEGIN(Domain.TRADE, "波次拣选", "/trade/picking/paper/begin", "com.raycloud.dmj.services.trades.support.TradesCollarService#pickingPapersBegin"),

    //领取拣选任务，拣选方式：总拣总分/边拣边分
    WAVE_PICKING_BEGIN(Domain.PDA, "波次拣选", "/trade/wave/picking/begin", "com.raycloud.dmj.services.pda.PickService#saveWavePickTrade"),

    WAVE_SUBSECTION_PICKING_BEGIN(Domain.PDA, "波次分段拣选", "/trade/wave/subsection/pick/begin", "com.raycloud.dmj.services.pda.PickService#subSectionPickBegin"),

    // 挂起退出
    WAVE_PICKING_HANG_UP(Domain.PDA, "挂起退出", "/trade/hang/up", "com.raycloud.dmj.web.controller.common.TradePickController#hangUp"),
    // 取消挂起
    WAVE_PICKING_CANCEL_HANG_UP(Domain.PDA, "取消挂起", "/trade/hang/up", "com.raycloud.dmj.web.controller.common.TradePickController#hangUp"),

    //拣选完成：商家编码：A，货位：A-1-1-1-1，拣选数量：10 位置号：1,2,3
    PICK_NEXT_ITEM(Domain.PDA, "商品拣选完成", "/trade/pick/next", "com.raycloud.dmj.services.pda.business.TradeTaskBusiness#afterCurrentFinish"),

    PICK_NEXT_REMOVED_RETURN(Domain.PDA, "踢出波次归还商品", "/trade/pick/next", "com.raycloud.dmj.services.pda.PickService.returnRemovedPickMapTrade"),
    //波次拣选完成
    PICK_NEXT_WAVE(Domain.PDA, "全部拣选完成", "/trade/pick/next", "com.raycloud.dmj.services.pda.business.TradeTaskBusiness#afterAllFinish"),

    //拣选跳过：商家编码：A，货位：A-1-1-1-1
    PICK_SKIP(Domain.PDA, "拣选跳过", "/trade/pick/skip", "com.raycloud.dmj.services.pda.PickService#skip"),

    //更换货位：更换前货位：A-12-134,更换后货位：A-245-355
    SECTION_REFRESH(Domain.PDA, "更换货位", "/goods/section/refresh", "com.raycloud.dmj.services.pda.GoodsService#flushGoodsSection"),

    //放弃拣选
    PICK_DELETE(Domain.PDA, "放弃拣选", "/trade/pick/delete", "com.raycloud.dmj.services.pda.PickService#deletePickTaskByTaskId"),

    PICK_DELETE_PAPER(Domain.TRADE, "放弃拣选", "/trade/pick/delete", "com.raycloud.dmj.services.pda.PickService#deletePickTaskByTaskId"),

    //接力退出
    RELAY_OUT(Domain.PDA, "接力退出", "/trade/relay/out", "com.raycloud.dmj.services.pda.PickService#relayOut"),

    //接力拣选任务
    RELAY_START(Domain.PDA, "接力拣选", "/trade/relay/start", "com.raycloud.dmj.services.pda.PickService#relayStart"),

    //wavePicking 统计信息
    WAVE_PICKING_STAT(Domain.TRADE, "波次拣选统计数据修改记录", "", ""),
    WAVE_PICKING_STAT_PDA(Domain.PDA, "波次拣选统计数据修改记录", "", ""),

    WAVE_PRINT_PICK(Domain.WAVE, "打印拣货单", "", ""),

    //wavePrintType == 1, 拣选无线打印，位置号：1,2,3,4
    //wavePrintType == 2, 播种无线打印，位置号：1,2,3,4
    //wavePrintType == 3, 播种仅打已播，位置号：1,2,3,4
    WIRELESS_PRINT_SAVE(Domain.PDA, "无线打印", "/pda/wireless/print/save", "com.raycloud.dmj.web.controller.common.WirelessPrintController#save"),

    //领取播种任务
    WAVE_SCAN_CODE(Domain.PDA, "波次播种", "/trade/wave/scan/code", "com.raycloud.dmj.web.controller.common.WavePickController#scanCode"),

    //播种完成：商家编码：A,B 位置号：1
    WAVE_SEED_TRADE(Domain.PDA, "订单播种完成", "/trade/wave/seed", "com.raycloud.dmj.services.trades.support.wave.TradePostPrintService#seed"),

    //全部播种完成
    WAVE_SEED_ALL(Domain.PDA, "全部播种完成", "/trade/wave/seed", "com.raycloud.dmj.services.trades.support.wave.TradePostPrintService#seed"),

    //直接播种完成
    WAVE_SEED_DIRECT(Domain.PDA, "边拣边分直接播种完成", "/trade/pick/seed", "com.raycloud.dmj.web.controller.common.TradePickController.pickSeed"),
    WAVE_SEED_DIRECT_TASK_LIST(Domain.PDA, "总览拣选直接播种完成", "/trade/pick/seed", "com.raycloud.dmj.web.controller.common.TradePickController.pickSeed"),

    //scanType == 1 开始后置打印
    //scanType == 2 开始播种打印
    POST_PRINT_SCAN_CODE(Domain.TRADE, "PC打印", "/trade/post/print/scan/code", "com.raycloud.dmj.services.trades.support.wave.TradePostPrintService#scanPickingInfo"),
    //scanType == 3 PDA播种
    POST_PRINT_SCAN_CODE_PDA(Domain.PDA, "PDA播种", "", "com.raycloud.dmj.services.trades.support.wave.TradePostPrintService#scanPickingInfo"),

    //后置打印出单：位置号：1
    PRINT_POST(Domain.TRADE, "后置打印", "/trade/print/post,/trade/print/post/multi", "com.raycloud.dmj.controller.TradePrintController#printPost0"),

    // 后置-2盲扫
    NEGATIVE_2_PRINT_POST(Domain.TRADE, "负2后置打印", "/trade/print/post,/trade/print/post/multi", "com.raycloud.dmj.controller.TradePrintController#printPost0"),

    //后置连打出单：位置号：1,2,3,4
    PRINT_POST_BATCH(Domain.TRADE, "后置连续打印", "/trade/print/post/batch", "com.raycloud.dmj.controller.TradePrintController#printPostBatch"),

    //后置重新扫描 TODO 无接口
    SCAN_POST_REDO(Domain.TRADE, "后置重新扫描", "", ""),

    //播种打印出单：位置号：1
    PRINT_SEED(Domain.TRADE, "播种打印出单", "/trade/print/seed,/trade/print/seed/multi", "com.raycloud.dmj.controller.TradePrintController#printSeed0"),

    //开放平台播种
    WAVE_SEED_OPEN(Domain.TRADE, "开放平台播种", "", ""),

    //重新播种
    POST_PRINT_SEED_CANCEL(Domain.TRADE, "重新播种", "/trade/post/print/seed/cancel", "com.raycloud.dmj.web.controllers.trades.TradePostPrintController#cancelSeed"),

    //取消播种
    POST_PRINT_WAVE_SEED_CANCEL(Domain.PDA, "取消播种", "/trade/wave/seed/cancel", "com.raycloud.dmj.web.controller.common.WavePickController#cancelSeed"),

    //播种拆分订单，位置号：1
    POST_PRINT_SPLIT_TRADE(Domain.TRADE, "拆分订单", "/trade/post/print/split/trade", "com.raycloud.dmj.web.controllers.trades.TradePostPrintController#splitWaveTrade"),

    //波次订单验货完成，订单号：123454
    PACKGE(Domain.TRADE, "包装验货", "/trade/pack", "com.raycloud.dmj.business.operate.PackBusiness#afterPackTrades"),

    //播种复验
    POST_PRINT_SEED_CHECK_COVER(Domain.TRADE, "播种复验", "/trade/post/print/seed/check/cover", "com.raycloud.dmj.web.controllers.trades.TradePostPrintController#coverSeedCheckResult"),

    //播种复验拆分
    POST_PRINT_SEED_CHECK_COVER_SPLIT(Domain.TRADE, "播种复验拆分", "/trade/post/print/seed/check/cover/split", "com.raycloud.dmj.web.controllers.trades.TradePostPrintController#coverSeedCheckAndSplit"),

    //自动完结波次
    PRINT_FINISH_AUTO(Domain.TRADE, "自动完结波次", "", "com.raycloud.dmj.services.trades.support.wave.TradeWaveService#updateWaveFinished"),

    // 修改波次备注
    WAVE_UPDATE_REMARK(Domain.TRADE, "修改波次备注", "/trade/wave/update/remake", "com.raycloud.dmj.web.controllers.trades.TradeWaveController#updateWaveRemake"),

    // 波次拣选,过滤异常单
    WAVE_PICKING_BEGIN_FILTER(Domain.PDA, "波次拣选", "/trade/wave/picking/begin", "com.raycloud.dmj.services.pda.PickService#saveWavePickTrade"),

    WAVE_PICKING_CODE_ADD(Domain.PDA, "拣选号新增", "/trade/wave/picking/code/add", "com.raycloud.dmj.services.pda.PickService#pickingCodeAdd"),

    // 跳过且直接盘0
    SKIP_PICK_AND_CHECK_0(Domain.PDA, "跳过且直接盘0", "/trade/pick/next", "com.raycloud.dmj.services.pda.business.TradeTaskBusiness#addCheckTask"),

    // 确定且直接盘0
    PICK_AND_CHECK_0(Domain.PDA, "确定且直接盘0", "/trade/pick/next", "com.raycloud.dmj.services.pda.business.TradeTaskBusiness#addCheckTask"),

    // 跳过且生成盘点任务
    SKIP_PICK_AND_CHECK(Domain.PDA, "跳过且生成盘点任务", "/goods/inventory/save", "com.raycloud.dmj.services.pda.GoodsService#createStocktaking"),

    // 确定且生成盘点任务
    PICK_AND_CHECK(Domain.PDA, "确定且生成盘点任务", "/trade/pick/next", "com.raycloud.dmj.services.pda.business.TradeTaskBusiness#addCheckTask"),

    // 确定且生成移库任务
    PICK_AND_MOVE(Domain.PDA, "确定且生成移库任务", "/trade/pick/next", "com.raycloud.dmj.services.pda.business.TradeTaskBusiness#addCheckTask"),

    // 确定且生成草稿状态盘点单
    PICK_AND_CHECK_SHEET(Domain.PDA, "确定且生成草稿状态盘点单", "/trade/pick/next", "com.raycloud.dmj.services.pda.business.TradeTaskBusiness#addCheckTask"),

    // 跳过且生成草稿状态盘点单
    SKIP_PICK_AND_CHECK_SHEET(Domain.PDA, "跳过且生成草稿状态盘点单", "/goods/inventory/save", "com.raycloud.dmj.services.pda.GoodsService#createStocktaking"),

    // Z货位自动拣选完成
    Z_GOODS_SECTION_AUTO_FINISH(Domain.PDA, "Z货位自动拣选完成", "/trade/wave/picking/begin", "com.raycloud.dmj.services.pda.business.TaskHandleBusiness#calculateAutoFinish"),

    // 货位可配库存不足，自动完成任务
    UNDER_STOCK_AUTO_FINISH(Domain.PDA, "货位可配库存不足，自动完成任务", "/trade/wave/picking/begin", "com.raycloud.dmj.services.pda.business.TradeTaskBusiness#allInsufficientReAllocate"),

    //领取波次
    WAVE_ASSIGNED(Domain.PDA,"领取波次","/trade/wave/assign/picker","com.raycloud.dmj.web.controller.common.WavePickController#assignWavePicker"),

    // 直接发货
    DIRECT_CONSIGN(Domain.TRADE, "波次直接发货", "/trade/wave/consign/direct", "com.raycloud.dmj.web.controllers.trades.TradeWaveController.consignDirect"),
    // 修改快递
    UPDATE_EXPRESS(Domain.TRADE, "修改快递", "/trade/print/logistics/wlb/save", "com.raycloud.dmj.controller.TradePrintController.saveWlbLogisticsExpress"),

    // PDA波次称重
    PDA_WAVE_WEIGHT(Domain.PDA,"PDA波次称重","trade/wave/weigh","com.raycloud.dmj.web.controller.common.PdaTradeController.waveWeigh"),

    //自动完结波次
    WAVE_AUTO_PRINT(Domain.TRADE, "波次自动打印", "", "com.raycloud.dmj.services.ec.WaveAutoPrintListener"),

    // 拣选完成后记录操作日志
    PDA_OPERATE_LOG(Domain.PDA,"PDA拣选完成后记录操作日志","/trade/pick/operate/log","com.raycloud.dmj.web.controller.common.TradePickController.pickOperateLog"),

    //快销发货出单
    PRINT_FAST_CONSIGN(Domain.TRADE, "快销发货出单", "/trade/print/fast/consign", "com.raycloud.dmj.controller.TradePrintController#printFastConsign0"),
    //改码
    CHANGE_CODE(Domain.PDA, "改码", "/pickChange/saveOrUpdatePickChangeCodeRecord", "com.raycloud.dmj.web.controller.common.PickChangeCodeRecordController#saveOrUpdatePickChangeCodeRecord"),
    ;

    /**
     * 模块
     */
    private Domain domain;

    /**
     * 操作
     */
    private String operate;

    /**
     * 路径
     */
    private String path;

    /**
     * 方法
     */
    private String action;

    WaveTraceOperateEnum(Domain domain, String operate, String path, String action) {
        this.domain = domain;
        this.operate = operate;
        this.path = path;
        this.action = action;
    }

    public Domain getDomain() {
        return domain;
    }

    public void setDomain(Domain domain) {
        this.domain = domain;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }
}

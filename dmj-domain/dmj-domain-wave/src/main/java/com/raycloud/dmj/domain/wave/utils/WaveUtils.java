package com.raycloud.dmj.domain.wave.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Objects;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.account.UserRole;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.log.WavePrintType;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigContext;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeExceptionUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.SubListIterator;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.enums.WaveChatConfigsEnum;
import com.raycloud.dmj.domain.wave.model.WaveContext;
import com.raycloud.dmj.domain.wave.model.WaveFilterParams;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import javax.sql.DataSource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 波次工具类
 * Created by shaoxianchang on 2017/8/24.
 */
public class WaveUtils {
    private static final Logger LOGGER = Logger.getLogger(WaveUtils.class);

    public static final String WAVE_TRADE_LIMIT_CONFIG = "wave_trade_limit";

    public static final String OPEN_AUTO_CREATE_WAVE = "open_auto_create_wave";

    public static final String OPEN_AUTO_CREATE_WAVE_COMPANY = "open_auto_create_wave_company";

    public static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 非标准格式带中文日期时间的正则表达式
     * 可匹配 今天、昨天、前天、N天前、今天 12:00:00 10天前 11:11:11
     */
    private static final Pattern CN_TIME_PATTERN = Pattern.compile("(\\d*)?[\\u4e00-\\u9fa5]{2}(\\s(0[0-9]|1[0-9]|2[0-3]):[0-5]\\d:[0-5]\\d)?$");

    public static final ThreadLocal<WaveContext> WAVE_CONTEXT = new ThreadLocal<WaveContext>() {
        @Override
        protected WaveContext initialValue() {
            return new WaveContext();
        }
    };

    /**
     * 劳动力生成波次的数量
     */
    public static final ThreadLocal<Integer> DEMAND_NUMBER = new ThreadLocal<>();


    /**
     * PDA扫描生成波次的 分配的波次号
     */
    public static final ThreadLocal<Wave> PDA_SCAN_WAVE_ASSIGN_WAVE = new ThreadLocal<>();

    /**
     * 多波次播种扫描的条码数据格式（商家编码-位置号）
     */
    private static final Pattern SINGLE_WAVE_BARCODE_PATTERN = Pattern.compile("(.+)-(\\d+)");

    /**
     * 多波次播种扫描的条码数据格式（商家编码%2c拣选号%2c位置号）
     */
    private static final Pattern MULTI_WAVES_SEED_BARCODE_PATTERN = Pattern.compile("(.+)%2[c|C](.+)%2[c|C](\\d+)");

    /**
     * 多波次唯一码条码数据格式（商家编码%2c拣选号%2c位置号%2c唯一码）
     */
    private static final Pattern MULTI_WAVES_UNIQUE_BARCODE_PATTERN = Pattern.compile("(.+)%2[c|C](.+)%2[c|C](\\d+)%2[c|C](\\d+)");
    private static final Pattern MULTI_WAVES_UNIQUE_BARCODE_PATTERN_NEW = Pattern.compile("(.+)%2[c|C](.+)%2[c|C](.+)%2[c|C](\\d+)");

    /**
     * 多波次唯一码条码数据格式（简 商家编码%2c唯一码）
     */
    private static final Pattern MULTI_WAVES_UNIQUE_BARCODE_SHORT_PATTERN = Pattern.compile("(.+)%2[c|C](\\d+)");

    private static final String KEY_JOINER = "_";

    public static final int WAVE_PARTITION_SIZE = 5000;

    /**
     * 根据配置生成位置号编码
     * @param positionNo 原来位置号
     * @return 位置编码
     */
    public static String generatePositionCode(Staff staff, Long positionNo) {
        if (positionNo == null) {
            return "";
        }
        if (positionNo <= 0) {
            return "待分配";
        }
        boolean openSeedRowPosition = BooleanUtils.isTrue(staff.getConf().getOpenSeedRowPosition());
        Integer rowPositionNum = staff.getConf().getSeedRowPositionNum();
        Integer rowNum = staff.getConf().getSeedRowNum();
        if (openSeedRowPosition) {
            if (rowPositionNum == null) {
                rowPositionNum = 1;
            }
            if (rowNum != null && rowNum > 0) {
                int shelfNum = rowNum * rowPositionNum;//一个货架总数
                return String.format("%c-%s-%s", (int) ((positionNo - 1) / shelfNum) + 'A', ((positionNo - 1) % shelfNum) / rowPositionNum + 1, ((positionNo - 1) % shelfNum) % rowPositionNum + 1);
            }
            return String.format("%s-%s", (positionNo - 1) / rowPositionNum + 1, (positionNo - 1) % rowPositionNum + 1);
        } else {
            return String.valueOf(positionNo);
        }
    }

    /**
     * 计算波次订单的商品和种类数量
     * @param trades 订单
     */
    public static void calculateWaveTradeItemCount(List<Trade> trades) {
        for (Trade trade : trades) {
            calculateWaveTradeItemCount(trade);
        }
    }

    public static void calculateWaveTradeItemCount(Trade trade) {
        if (!(trade instanceof Orderable)) {
            return;
        }
        int itemCount = 0;
        Set<String> outerIds = new HashSet<String>();
        List<Order> orders = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(trade));
        for (Order order : orders) {
            if (OrderUtils.isVirtualOrNonConsign(order) || OrderUtils.isAfterSendGoods(order)) {
                continue;
            }
            itemCount += order.getNum();
            outerIds.add(order.getItemSysId() + "_" + order.getSkuSysId());
        }
        trade.setItemNum(itemCount);
        trade.setItemKindNum(outerIds.size());
    }

    public static String buildOuterIdNumStr(Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        StringBuilder outerIdConcatBuild = new StringBuilder();
        Map<String, Integer> outerId2Num = new HashMap<>();
        for (Order order : orders) {
            if (CollectionUtils.isNotEmpty(order.getSuits()) && order.isSuit(false)) {
                boolean hasEffectSuit = false;
                for (Order suit : order.getSuits()) {
                    if (!OrderUtils.isVirtualOrNonConsign(suit)) {
                        hasEffectSuit = true;
                    }
                }
                if (!hasEffectSuit) {
                    continue;
                }
            }
            if (OrderUtils.isVirtualOrNonConsign(order)) {
                continue;
            }
            Integer num = outerId2Num.get(order.getSysOuterId());
            if (num == null) {
                outerId2Num.put(order.getSysOuterId(), order.getNum());
            } else {
                outerId2Num.put(order.getSysOuterId(), num + order.getNum());
            }
        }
        int totalNum = 0;
        for (Map.Entry<String, Integer> entry : outerId2Num.entrySet()) {
            if (outerIdConcatBuild.length() > 0) {
                outerIdConcatBuild.append(",");
            }
            totalNum += entry.getValue();
            outerIdConcatBuild.append(entry.getKey()).append("*").append(entry.getValue());
        }
        trade.setItemNum(totalNum);
        trade.setItemKindNum(outerId2Num.size());
        return outerIdConcatBuild.toString();
    }

    public static String buildOuterIdNumStr(Trade trade, Map<Long, List<String>> orderIdUniqueCodesMap) {
        if (orderIdUniqueCodesMap == null || orderIdUniqueCodesMap.isEmpty()) {
            return buildOuterIdNumStr(trade);
        }

        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        StringBuilder outerIdConcatBuild = new StringBuilder();
        Set<String> outerIds = Sets.newHashSetWithExpectedSize(orders.size());
        int totalNum = 0;
        for (Order order : orders) {
            List<String> uniqueCodes = orderIdUniqueCodesMap.get(order.getId());
            if (CollectionUtils.isNotEmpty(uniqueCodes)) {
                for (String uniqueCode : uniqueCodes) {
                    outerIdConcatBuild.append(order.getSysOuterId()).append("(").append(uniqueCode).append(")").append("*1").append(",");
                }
                outerIds.add(order.getSysOuterId());
                totalNum += uniqueCodes.size();
            }
        }
        trade.setItemNum(totalNum);
        trade.setItemKindNum(outerIds.size());
        return outerIdConcatBuild.length() > 0 ? outerIdConcatBuild.substring(0, outerIdConcatBuild.length() - 1) : outerIdConcatBuild.toString();
    }

    public static String buildItemOuterIdNumStr(Trade trade, Map<Long, String> itemMap) {
        if (itemMap == null || itemMap.isEmpty()) {
            return "";
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        StringBuilder outerIdConcatBuild = new StringBuilder();
        Map<String, Integer> outerId2Num = new HashMap<>();
        for (Order order : orders) {
            if (CollectionUtils.isNotEmpty(order.getSuits()) && order.isSuit(false)) {
                boolean hasEffectSuit = false;
                for (Order suit : order.getSuits()) {
                    if (!OrderUtils.isVirtualOrNonConsign(suit)) {
                        hasEffectSuit = true;
                    }
                }
                if (!hasEffectSuit) {
                    continue;
                }
            }
            if (OrderUtils.isVirtualOrNonConsign(order)) {
                continue;
            }
            String outerId = itemMap.get(order.getItemSysId());
            if(order.getWaveAssembleInfo()!=null
                &&order.getWaveAssembleInfo().getLastSysItemId()!=null
                &&order.getWaveAssembleInfo().getLastSysItemId()>0){
                outerId=itemMap.get(order.getWaveAssembleInfo().getLastSysItemId());
            }
            if (StringUtils.isNotEmpty(outerId)) {
                outerId2Num.merge(outerId, order.getNum(), Integer::sum);
			}
        }
        for (Map.Entry<String, Integer> entry : outerId2Num.entrySet()) {
            if (outerIdConcatBuild.length() > 0) {
                outerIdConcatBuild.append(",");
            }
            outerIdConcatBuild.append(entry.getKey()).append("*").append(entry.getValue());
        }
        return outerIdConcatBuild.toString();
    }

    /**
     * 简化trade信息，方便添加日志
     */
    public static List<Trade> simplifyTrades(List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return Collections.emptyList();
        }

        List<Trade> tradeList = new ArrayList<Trade>(trades.size());
        for (Trade trade : trades) {
            tradeList.add(simplifyTrade(trade));
        }
        return tradeList;
    }

    public static Trade simplifyTrade(Trade trade) {
        if (trade == null) {
            return null;
        }
        Trade simpleTrade = new TbTrade();
        simpleTrade.setShortId(trade.getShortId());
        simpleTrade.setSid(trade.getSid());
        simpleTrade.setSource(trade.getSource());
        simpleTrade.setTaobaoId(trade.getTaobaoId());
        simpleTrade.setExpressPrintTime(trade.getExpressPrintTime());
        simpleTrade.setOutSid(trade.getOutSid());
        simpleTrade.setCompanyId(trade.getCompanyId());
        simpleTrade.setTemplateId(trade.getTemplateId());
        simpleTrade.setTemplateType(trade.getTemplateType());
        simpleTrade.setTemplateName(trade.getTemplateName());
        simpleTrade.setUserId(trade.getUserId());
        simpleTrade.setShopName(trade.getShopName());
        simpleTrade.setWarehouseId(trade.getWarehouseId());
        calculateWaveTradeItemCount(Collections.singletonList(trade));
        simpleTrade.setItemNum(trade.getItemNum());
        simpleTrade.setItemKindNum(trade.getItemKindNum());
        simpleTrade.setSellerMemo(trade.getSellerMemo());
        simpleTrade.setSysMemo(trade.getSysMemo());
        simpleTrade.setBuyerMessage(trade.getBuyerMessage());
        simpleTrade.setSellerFlag(trade.getSellerFlag());
        simpleTrade.setBuyerNick(trade.getBuyerNick());
        simpleTrade.setWaveId(trade.getWaveId());
        simpleTrade.setInvoiceName(trade.getInvoiceName());
        simpleTrade.setBuyerTaxNo(trade.getBuyerTaxNo());
        simpleTrade.setNeedInvoice(trade.getNeedInvoice());
        simpleTrade.setExceptNames(trade.getExceptNames());
        simpleTrade.setExceptMemo(trade.getExceptMemo());
        simpleTrade.setExpressCompanyId(trade.getExpressCompanyId());
        simpleTrade.setExpressCode(trade.getExpressCode());
        simpleTrade.setExpressName(trade.getExpressName());
        simpleTrade.setSalePrice(trade.getSalePrice());
        simpleTrade.setWlbTemplateType(trade.getWlbTemplateType());
        simpleTrade.setTagIds(trade.getTagIds());
        simpleTrade.setTags(trade.getTags());
        simpleTrade.setLogisticsCompanyId(trade.getLogisticsCompanyId());
        simpleTrade.setLogisticsCompanyName(trade.getLogisticsCompanyName());
        simpleTrade.setShipperName(trade.getShipperName());
        simpleTrade.setShipper(trade.getShipper());
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        boolean containGift = false;
        if (CollectionUtils.isNotEmpty(orders)) {
            for (Order order : orders) {
                if (!OrderUtils.isAfterSendGoods(order) && order.getGiftNum() != null && order.getGiftNum() > 0) {
                    containGift = true;
                    break;
                }
            }
        }
        simpleTrade.setContainGift(containGift);
        trade.setContainGift(containGift);
        return simpleTrade;
    }

    public static Wave getSimplePdaScanWave(Wave wave,Staff staff){
        if(wave==null){
            return wave;
        }
        Wave simplePdaScanWave = new Wave();
        simplePdaScanWave.setId(wave.getId());
        simplePdaScanWave.setAssignPicker(staff.getId());
        simplePdaScanWave.setAssignPickerName(staff.getName());
        simplePdaScanWave.setPickerName(staff.getName());
        simplePdaScanWave.setAssignSorter(wave.getAssignSorter());
        simplePdaScanWave.setAssignSorterName(wave.getAssignSorterName());
        simplePdaScanWave.setBoxAllocate(wave.getBoxAllocate());
        simplePdaScanWave.setCombineItemSingle(wave.getCombineItemSingle());
        simplePdaScanWave.setDistributionStatus(wave.getDistributionStatus());
        simplePdaScanWave.setExpressTemplateName(wave.getExpressTemplateName());
        simplePdaScanWave.setTradesCount(wave.getTradesCount());
        simplePdaScanWave.setItemCount(wave.getItemCount());
        simplePdaScanWave.setPublicExpressId(wave.getPublicExpressId());
        simplePdaScanWave.setPublicExpressName(wave.getPublicExpressName());
        simplePdaScanWave.setLogisticsCompanyId(wave.getLogisticsCompanyId());
        simplePdaScanWave.setLogisticsCompanyName(wave.getLogisticsCompanyName());
        simplePdaScanWave.setTagIds(wave.getTagIds());
        simplePdaScanWave.setUnPackNum(wave.getUnPackNum());
        simplePdaScanWave.setAssignPickerFailReasons(wave.getAssignPickerFailReasons());
        simplePdaScanWave.setCode(wave.getCode());
        simplePdaScanWave.setCreatorId(wave.getCreatorId());
        simplePdaScanWave.setCreatorName(wave.getCreatorName());
        simplePdaScanWave.setPickingType(wave.getPickingType());
        simplePdaScanWave.setOutsidStatus(wave.getOutsidStatus());
        simplePdaScanWave.setPickEndAutoConsign(wave.getPickEndAutoConsign());
        simplePdaScanWave.setRuleId(wave.getRuleId());
        simplePdaScanWave.setRuleName(wave.getRuleName());
        simplePdaScanWave.setShortId(wave.getShortId());
        simplePdaScanWave.setStatus(wave.getStatus());
        simplePdaScanWave.setWarehouseId(wave.getWarehouseId());
        return simplePdaScanWave;
    }




    public static List<Trade> buildSimpleTradeTraces(List<Trade> trades) {
        List<Trade> tradeTraces = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            Trade tradeTrace = new TbTrade();
            tradeTrace.setSid(trade.getSid());
            tradeTrace.setWaveId(trade.getWaveId());
            tradeTrace.setExpressPrintTime(trade.getExpressPrintTime());
            tradeTraces.add(tradeTrace);
        }
        return tradeTraces;
    }

    /**
     * 将波次中的订单排序，用于位置号分配
     * 单品需要将同商品放一起
     * 最后再根据付款时间排序
     * @param trades 订单
     */
    public static void sortTrades(List<Trade> trades) {

        Collections.sort(trades, new Comparator<Trade>() {
            @Override
            public int compare(Trade t1, Trade t2) {
                int defaultComp = t1.getPayTime().compareTo(t2.getPayTime()) == 0 ? t1.getSid().compareTo(t2.getSid()) : t1.getPayTime().compareTo(t2.getPayTime());
                if (t1.getPositionNo() != null) {
                    return t1.getPositionNo().compareTo(t2.getPositionNo()) == 0 ? defaultComp : t1.getPositionNo().compareTo(t2.getPositionNo());
                }
                return defaultComp;
            }
        });
    }

    public static List<Trade> createSimpleTradesForTrace(Staff staff, List<Trade> trades) {
        //记录日志
        List<Trade> simpleTrades = new ArrayList<Trade>(trades.size());
        for (Trade trade : trades) {
            Trade simpleTrade = new Trade();
            simpleTrade.setSid(trade.getSid());
            simpleTrade.setCompanyId(staff.getCompanyId());
            simpleTrade.setTaobaoId(0L);
            simpleTrade.setWaveId(trade.getWaveId());
            simpleTrade.setPositionNo(trade.getPositionNo());
            simpleTrades.add(simpleTrade);
        }
        return simpleTrades;
    }

    /**
     * 过滤出可以进行波次计算的子订单
     * @param orders
     * @return
     */
    public static List<Order> getOrdersForWave(List<Order> orders) {
        return WaveUtils.getOrdersForWave(new GetOrdersForWaveParams.Builder().orders(orders).build());
    }

    public static List<Order> getOrdersForWave(List<Order> orders, Boolean splitSuit) {
        return WaveUtils.getOrdersForWave(new GetOrdersForWaveParams.Builder().orders(orders).splitSuit(splitSuit).build());
    }

    public static List<Order> getOrdersForWave(GetOrdersForWaveParams params) {
        List<Order> orders = params.getOrders();
        final boolean containVirtual = params.isContainVirtual();
        boolean splitSuit = params.isSplitSuit();
        boolean skipJudgeAfterSendGoods = params.isSkipJudgeAfterSendGoods();
        if (CollectionUtils.isEmpty(orders)) {
            return orders;
        }

        Predicate<Order> predicate = new Predicate<Order>() {
            @Override
            public boolean apply(Order order) {
                if (!skipJudgeAfterSendGoods && OrderUtils.isAfterSendGoods(order)) {
                    return false;
                }
                if (!containVirtual && OrderUtils.isVirtualOrNonConsign(order)) {
                    return false;
                }
                // 货位不参与拣选
                if (Objects.equal(order.getGsNotPick(), CommonConstants.JUDGE_YES)) {
                    return false;
                }
                return true;
            }
        };
        List<Order> filter = new ArrayList(Collections2.filter(orders, predicate));

        if (!splitSuit) {
            return filter;
        }

        List<Order> filter2 = new ArrayList<Order>();
        for (Order order : filter) {
            List<Order> suits = order.getSuits();
            if (CollectionUtils.isEmpty(suits) || !order.isSuit()) {
                filter2.add(order);
                continue;
            }
            filter2.addAll(Collections2.filter(suits, predicate));
        }
        return filter2;
    }

    /**
     * 将订单信息填充值波次订单分拣信息中
     * @param waveSorting
     * @param trade
     * @return
     */
    public static WaveSorting fillWaveSortingWithTrade(Staff staff,WaveSorting waveSorting, Trade trade) {
        trade.setExceptions(TradeExceptionUtils.analyze(staff, trade));
        waveSorting.setBuyerMessage(TradeUtils.getBuyerMessage(staff, trade));
        waveSorting.setSellerMemo(TradeUtils.getSellerMemo(trade));
        waveSorting.setOutSid(trade.getOutSid());
        waveSorting.setTemplateType(trade.getTemplateType());
        waveSorting.setTemplateId(trade.getTemplateId());
        waveSorting.setTemplateName(trade.getTemplateName());
        waveSorting.setWarehouseId(trade.getWarehouseId());
        waveSorting.setIsExcep(trade.getIsExcep());
        waveSorting.setExcStauses(trade.getExcStauses());
        waveSorting.setExceptions(trade.getExceptions());
        waveSorting.setUserId(trade.getUserId());
        waveSorting.setSid(trade.getSid());
        waveSorting.setExpressPrintTime(trade.getExpressPrintTime());
        waveSorting.setSellerFlag(trade.getSellerFlag());
        waveSorting.setHasBuyerMessage(trade.getHasBuyerMessage());
        waveSorting.setShortId(trade.getShortId());
        waveSorting.setExceptMemo(trade.getExceptMemo());
        waveSorting.setExceptNames(trade.getExceptNames());
        waveSorting.setPayTime(trade.getPayTime());
        waveSorting.setTagNames(getTagNames(trade.getTags()));
        waveSorting.setReceiverName(trade.getReceiverName());
        waveSorting.setReceiverMobile(trade.getReceiverMobile());
        // 详细地址 省市区地址
        waveSorting.setReceiverDetailedAddress(Joiner.on("").skipNulls().join(trade.getReceiverState(), trade.getReceiverCity(), trade.getReceiverDistrict(), trade.getReceiverAddress()));
        waveSorting.setLogisticsCompanyId(trade.getLogisticsCompanyId());
        waveSorting.setLogisticsCompanyName(trade.getLogisticsCompanyName());
        return waveSorting;
    }

    /**
     * 获取标签名
     */
    public static String getTagNames(List<TradeTag> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }
        Set<String> tagNames = Sets.newHashSet();
        for (TradeTag tag : tags) {
            String buf = (StringUtils.isNotBlank(tag.getBgColor()) ? tag.getBgColor().trim() : "0") +
                    "_" +
                    (StringUtils.isNotBlank(tag.getFontColor()) ? tag.getFontColor().trim() : "0") +
                    "_" +
                    tag.getTagName();
            tagNames.add(buf);
        }
        return Joiner.on(",").skipNulls().join(tagNames);
    }

    public static Long[] toSids(List<WaveSorting> waveSortings, Map<Long,WaveSorting>  waveSortMap) {
        Long[] sids = new Long[waveSortings.size()];
        int i = 0;
        for (WaveSorting waveSorting : waveSortings) {
            sids[i++] = waveSorting.getSid();
            waveSortMap.put(waveSorting.getSid(), waveSorting);
        }
        return sids;
    }

    /** 是否需要播种包装验货
     * @param tradeConfig
     * @param waveConfig
     * @return
     */
    public static boolean needWavePackage(TradeConfig tradeConfig, WaveConfig waveConfig, WavePrintType printType) {
        return needWavePackage(tradeConfig, waveConfig, printType, null);
    }

    public static boolean needWavePackage(TradeConfig tradeConfig, WaveConfig waveConfig, Integer wavePrintType) {
        WavePrintType printType = WavePrintType.parseValue(wavePrintType);
        return printType != null && needWavePackage(tradeConfig, waveConfig, printType);
    }

    public static boolean needWavePackage(TradeConfig tradeConfig, WaveConfig waveConfig, WavePrintType printType, Boolean  isSeed) {
    	if (isSeed == null) {
    		isSeed = false;
		}
    	if (printType == WavePrintType.SEED_REFRESH
    			|| printType == WavePrintType.POST_REFRESH
    			|| printType == WavePrintType.ORDER_UNIQUE_CODE_REFRESH
    			|| printType == WavePrintType.BATCH_WAVE_REFRESH
    			|| printType == WavePrintType.GROUP_PACK_REFRESH
    			|| printType == WavePrintType.PRINT_EXPRESSS_RECEIPT
                || printType == WavePrintType.HOT_SALE_TRADE_ITEM) {
			return true;
		}
    	if ((printType == WavePrintType.POST || printType == WavePrintType.POST_ALL
    	        ||  printType == WavePrintType.POST_BATCH || printType == WavePrintType.POST_PART) && Integer.valueOf(0).equals(tradeConfig.getOpenPackAutoPost())) {
            return true;
        } else if (printType == WavePrintType.BATCH_WAVE && !isSeed && Integer.valueOf(0).equals(tradeConfig.getOpenPackAutoSingleWaveBatch())){
            return true;
        }
        return BooleanUtils.isTrue(waveConfig.getOpenWavePackage()
        		&& (printType == WavePrintType.SEED || printType == WavePrintType.SEED_ALL || printType == WavePrintType.SEED_PART
        			|| printType == WavePrintType.CHECK || printType == WavePrintType.WIRELESS || (printType == WavePrintType.BATCH_WAVE && isSeed )));
    }

    public static boolean needWavePackage(TradeConfig tradeConfig, WaveConfig waveConfig, Integer wavePrintType,Boolean  isSeed) {
        WavePrintType printType = WavePrintType.parseValue(wavePrintType);
        return printType != null && needWavePackage(tradeConfig, waveConfig, printType, isSeed);
    }


    public static List<Order> getCanSortingOrders(Trade trade) {
        return getCanSortingOrders(trade, null);
    }

    public static List<Order> getCanSortingOrders(Trade trade, Boolean isAfterSendPrint) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        List<Order> filter = Lists.newArrayListWithCapacity(orders.size());
        for (Order order : orders) {
            List<Order> suits = order.getSuits();
            if (CollectionUtils.isNotEmpty(suits) && order.isSuit(false)) {
                for (Order suit : suits) {
                    if (OrderUtils.isVirtualOrNonConsign(suit) || (OrderUtils.isAfterSendGoods(suit) && BooleanUtils.isNotTrue(isAfterSendPrint))) {
                        continue;
                    }
                    filter.add(suit);
                }
                //套件自身也进行添加操作
                if (OrderUtils.isVirtualOrNonConsign(order) || (OrderUtils.isAfterSendGoods(order) && BooleanUtils.isNotTrue(isAfterSendPrint))) {
                    continue;
                }
                filter.add(order);
            } else {
                if (OrderUtils.isVirtualOrNonConsign(order) || (OrderUtils.isAfterSendGoods(order) && BooleanUtils.isNotTrue(isAfterSendPrint))) {
                    continue;
                }
                filter.add(order);
            }
        }
        return filter;
    }

    public static boolean canPick(Order order) {
        return !OrderUtils.isVirtualOrNonConsign(order) && !OrderUtils.isAfterSendGoods(order) && !OrderUtils.notPick(order);
    }

    public static boolean canCheck(Order order) {
        return !order.isVirtual()  && !OrderUtils.isAfterSendGoods(order) && !OrderUtils.notCheck(order);
    }

    /**
     * 赠品不参与验货 0,1
     */
    public static boolean giftNotCheck(Order order) {
        return OrderUtils.notCheck(order);
    }

    public static WaveItem convertToWaveItem(Order order) {
        WaveItem item = new WaveItem();
        item.setSysItemId(order.getItemSysId());
        item.setSysSkuId(order.getSkuSysId() < 0L ? 0L : order.getSkuSysId());
        item.setOuterId(order.getSysOuterId());
        item.setPicPath(order.getSysPicPath());
        item.setTitle(order.getSysTitle());
        item.setShortTitle(order.getShortTitle());
        item.setPropertiesAlias(order.getSysSkuPropertiesAlias());
        item.setPropertiesName(order.getSysSkuPropertiesName());
        item.setRemark(StringUtils.isNotEmpty(order.getSysSkuRemark()) ? order.getSysSkuRemark() : order.getSysItemRemark());
        item.setNum(order.getNum());
        item.setIsGift(BooleanUtils.toInteger(order.getGiftNum() != null && order.getGiftNum() > 0));
        item.setIsPick(order.getIsPick());
        return item;
    }

    /**
     * 根据波次创建波次模板实例
     * @param wave 波次
     * @return 波次模板实例
     */
    public static WaveTemplateSimple buildWaveTemplate(Wave wave) {
        WaveTemplateSimple templateSimple = new WaveTemplateSimple();
        templateSimple.setTemplateId(wave.getExpressTemplateId());
        templateSimple.setTemplateType(wave.getExpressTemplateType());
        templateSimple.setTemplateName(wave.getExpressTemplateName());
        return templateSimple;
    }

    /**
     * 根据订单创建波次模板实例
     * @param trade 订单
     * @return 波次模板实例
     */
    public static WaveTemplateSimple buildWaveTemplate(Trade trade) {
        WaveTemplateSimple templateSimple = new WaveTemplateSimple();
        templateSimple.setTemplateId(trade.getTemplateId());
        templateSimple.setTemplateType(trade.getTemplateType());
        templateSimple.setTemplateName(trade.getTemplateName());
        return templateSimple;
    }

    /**
     * 获取店铺名称
     *
     * @param shop
     * @return
     */
    public static String getShopName(Shop shop) {
        if (shop == null) {
            return "";
        }
        return StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : shop.getTitle();
    }

    public static void setCurrentPickingParam(WavePickingParam param, WaveUtils.MultiWavesSeedKey current) {
        if (current.getPositionNo() != null) {
            param.setPositionNo(current.getPositionNo());
        }
        if (current.getPickingCode() != null) {
            param.setPickingCode(current.getPickingCode());
        }
        if (current.getSid() != null) {
            param.setSid(current.getSid());
        }
        if (current.getUniqueCode() != null) {
            param.setUniqueCode(current.getUniqueCode());
        }
    }

    public static MultiWavesSeedKey splitMultiWavesBarcode(String barcode, WaveConfig waveConfig) {
        if (Objects.equal(waveConfig.getOpenWaveUniqueCode(), 1)) {
            return WaveUtils.splitMultiWavesUniqueBarcode(barcode);
        } else {
            return WaveUtils.splitMultiWavesSeedBarcode(barcode);
        }
    }

    public static boolean isWaveUniqueCodeBarcode(String barcode) {
        return StringUtils.isNotEmpty(barcode) &&
                (MULTI_WAVES_UNIQUE_BARCODE_PATTERN.matcher(barcode).matches() || MULTI_WAVES_UNIQUE_BARCODE_SHORT_PATTERN.matcher(barcode).matches());
    }

    public static MultiWavesSeedKey splitWaveItemPositionBarcode(String barcode) {
        Matcher matcher = SINGLE_WAVE_BARCODE_PATTERN.matcher(barcode);
        if (!matcher.matches()) {
            return null;
        }

        try {
            return new MultiWavesSeedKey(matcher.group(1), null, Long.parseLong(matcher.group(2)));
        } catch (NumberFormatException e) {
            LOGGER.error(String.format("拆分商品位置号条码异常，条码数据为：%s", barcode), e);
            return null;
        }
    }

    /**
     * 对多波次播种扫描的条码数据进行拆分
     *
     * @param barcode
     * @return
     */
    public static MultiWavesSeedKey splitMultiWavesSeedBarcode(String barcode) {
        if (null == barcode) {
            return null;
        }
        Matcher matcher = MULTI_WAVES_SEED_BARCODE_PATTERN.matcher(barcode);
        if (!matcher.matches()) {
            return null;
        }
        try {
            return new MultiWavesSeedKey(matcher.group(1), matcher.group(2), Long.parseLong(matcher.group(3)));
        } catch (NumberFormatException e) {
            LOGGER.error(String.format("对多波次播种扫描的条码进行数据拆分时报错，条码数据为：%s", barcode), e);
            return null;
        }
    }

    /**
     * 对多波次播种扫描的条码数据进行拆分
     *
     * @param barcode
     * @return
     */
    public static MultiWavesSeedKey splitMultiWavesUniqueBarcode(String barcode) {
        if (null == barcode) {
            return null;
        }

        Matcher matcher = MULTI_WAVES_UNIQUE_BARCODE_PATTERN.matcher(barcode);
        if (matcher.matches()) {
            return new MultiWavesSeedKey(matcher.group(1), matcher.group(2), Long.parseLong(matcher.group(3)), String.valueOf(matcher.group(4)));
        } else if ((matcher = MULTI_WAVES_SEED_BARCODE_PATTERN.matcher(barcode)).matches()) {
            return new MultiWavesSeedKey(matcher.group(1), matcher.group(2), Long.parseLong(matcher.group(3)));
        } else if ((matcher = MULTI_WAVES_UNIQUE_BARCODE_SHORT_PATTERN.matcher(barcode)).matches()) {
            return new MultiWavesSeedKey(matcher.group(1), null, null, matcher.group(2));
        } else {
            return splitMultiWavesSeedBarcode(barcode);
        }
    }

    public static MultiWavesSeedKey splitMultiWavesUniqueBarcodeNew(String barcode) {
        if (StringUtils.isBlank(barcode)) {
            return null;
        }
        Matcher matcher = MULTI_WAVES_UNIQUE_BARCODE_PATTERN_NEW.matcher(barcode);
        if (matcher.matches()) {
            return new MultiWavesSeedKey(matcher.group(1), matcher.group(2), null, String.valueOf(matcher.group(3)), String.valueOf(matcher.group(4)));
        } else if ((matcher = MULTI_WAVES_SEED_BARCODE_PATTERN.matcher(barcode)).matches()) {
            return new MultiWavesSeedKey(matcher.group(1), matcher.group(2), Long.parseLong(matcher.group(3)));
        } else if ((matcher = MULTI_WAVES_UNIQUE_BARCODE_SHORT_PATTERN.matcher(barcode)).matches()) {
            return new MultiWavesSeedKey(matcher.group(1), null, null, matcher.group(2));
        } else {
            return splitMultiWavesSeedBarcode(barcode);
        }
    }

    /**
     * 获取商家编码
     */
    public static String getOuterId(String barcode) {
        if (null == barcode) {
            return null;
        }

        MultiWavesSeedKey multiWavesSeedKey = WaveUtils.splitMultiWavesUniqueBarcode(barcode);
        if (multiWavesSeedKey == null || StringUtils.isEmpty(multiWavesSeedKey.getOuterId())) {
            return barcode;
        }
        return multiWavesSeedKey.getOuterId();
    }

    public static String formatMultiWaveSeedBarcode(String barcode) {
        if (StringUtils.isEmpty(barcode)) {
            return barcode;
        }

        return barcode.replaceAll("%2[c|C]", "-");
    }

    /**
     * 获取单品数量
     */
    public static Integer getTradeSingleItemNum(Trade trade) {
        List<Order> orders = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(trade));
        Integer num = 0;
        for (Order order : orders) {
            if (OrderUtils.isVirtualOrNonConsign(order)) {
                continue;
            }
            num += order.getNum();
        }
        return num;
    }

    /**
     * 获取单品种类
     */
    public static Integer getTradeSingleItemKindNum(Trade trade) {
        List<Order> orders = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(trade));
        List<String> itemKeyList = orders.stream().filter(order -> !OrderUtils.isVirtualOrNonConsign(order))
                .map(order -> OrderUtils.buildItemKey(order.getItemSysId(), order.getSkuSysId())).distinct().collect(Collectors.toList());
        return itemKeyList.size();
    }

    /**
     * 多波次播种扫描的条码数据拆分后的结果
     */
    public static class MultiWavesSeedKey {
        public MultiWavesSeedKey(String outerId, String pickingCode, Long positionNo) {
            this.outerId = outerId;
            this.pickingCode = pickingCode;
            this.positionNo = positionNo;
        }

        public MultiWavesSeedKey(String outerId, String pickingCode, Long positionNo, String uniqueCode) {
            this.outerId = outerId;
            this.pickingCode = pickingCode;
            this.positionNo = positionNo;
            this.uniqueCode = uniqueCode;
        }

        public MultiWavesSeedKey(String outerId, String pickingCode, Long positionNo, String positionNoNew, String uniqueCode) {
            this.outerId = outerId;
            this.pickingCode = pickingCode;
            this.positionNo = positionNo;
            this.positionNoNew = positionNoNew;
            this.uniqueCode = uniqueCode;
        }

        public MultiWavesSeedKey(String outerId, WaveUniqueCode bindUniqueCode) {
            this.outerId = outerId;
            this.bindUniqueCode = bindUniqueCode;
        }

        // 商家编码
        private String outerId;

        // 拣选号
        private String pickingCode;

        // 位置号
        private Long positionNo;

        // 位置号(带非数字)
        private String positionNoNew;

        /**
         * 唯一码
         */
        private String uniqueCode;

        /**
         * 系统单号
         */
        private Long sid;

        /**
         * 数量
         */
        private Integer num;

        /**
         * 绑定的唯一码
         */
        private WaveUniqueCode bindUniqueCode;

        public String getOuterId() {
            return outerId;
        }

        public String getPickingCode() {
            return pickingCode;
        }

        public Long getPositionNo() {
            return positionNo;
        }

        public String getUniqueCode() {
            return uniqueCode;
        }

        public void setUniqueCode(String uniqueCode) {
            this.uniqueCode = uniqueCode;
        }

        public void setOuterId(String outerId) {
            this.outerId = outerId;
        }

        public void setPickingCode(String pickingCode) {
            this.pickingCode = pickingCode;
        }

        public void setPositionNo(Long positionNo) {
            this.positionNo = positionNo;
        }

        public Long getSid() {
            return sid;
        }

        public void setSid(Long sid) {
            this.sid = sid;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }

        public String getPositionNoNew() {
            return positionNoNew;
        }

        public void setPositionNoNew(String positionNoNew) {
            this.positionNoNew = positionNoNew;
        }

        public String getSeedKey() {
            return outerId + "_" + pickingCode + "_" + positionNo;
        }

        public WaveUniqueCode getBindUniqueCode() {
            return bindUniqueCode;
        }

        public void setBindUniqueCode(WaveUniqueCode bindUniqueCode) {
            this.bindUniqueCode = bindUniqueCode;
        }
    }

    /**
     * 过滤出波次计算子订单的参数
     */
    public static class GetOrdersForWaveParams {
        private List<Order> orders;

        // 是否包含虚拟商品
        private boolean containVirtual;

        // 是否拆分套件
        private boolean splitSuit;

        // 跳过订单发货的校验
        private boolean skipJudgeAfterSendGoods;

        public static class Builder {
            private List<Order> orders;

            private boolean containVirtual;

            private boolean splitSuit;

            private boolean skipJudgeAfterSendGoods;

            public GetOrdersForWaveParams build() {
                GetOrdersForWaveParams params = new GetOrdersForWaveParams();
                params.orders = orders;
                params.containVirtual = containVirtual;
                params.splitSuit = splitSuit;
                params.skipJudgeAfterSendGoods = skipJudgeAfterSendGoods;
                return params;
            }

            public Builder orders(List<Order> orders) {
                this.orders = orders;
                return this;
            }

            public Builder containVirtual(boolean containVirtual) {
                this.containVirtual = containVirtual;
                return this;
            }

            public Builder splitSuit(boolean splitSuit) {
                this.splitSuit = splitSuit;
                return this;
            }

            public Builder skipJudgeAfterSendGoods(boolean skipJudgeAfterSendGoods) {
                this.skipJudgeAfterSendGoods = skipJudgeAfterSendGoods;
                return this;
            }
        }

        public List<Order> getOrders() {
            return orders;
        }

        public boolean isContainVirtual() {
            return containVirtual;
        }

        public boolean isSplitSuit() {
            return splitSuit;
        }

        public boolean isSkipJudgeAfterSendGoods() {
            return skipJudgeAfterSendGoods;
        }
    }

    public static List<WaveSeedLog> buildSeedLogs(Long waveId, List<WaveSortingDetail> details) {
        return details.stream().map(detail -> {
            WaveSeedLog log = new WaveSeedLog();
            log.setSortingId(detail.getSortingId());
            log.setDetailId(detail.getId());
            log.setItemNum(detail.getChangeMatchedNum());
            log.setWaveId(waveId);
            log.setOuterId(detail.getOuterId());
            return log;
        }).collect(Collectors.toList());
    }

    public static String getItemInfoJson(WaveSorting waveSorting) {
        try {
            String itemInfoStr = waveSorting.getItemInfo();
            if (StringUtils.isEmpty(itemInfoStr)) {
                return null;
            }
            List<String> itemInfoList = ArrayUtils.toStringList(itemInfoStr);
            if (CollectionUtils.isEmpty(itemInfoList)) {
                return null;
            }
            List<Map<String, Object>> infoListMap = new ArrayList<>();
            for (String itemInfo : itemInfoList) {
                Map<String, Object> infoMap = new HashMap<>();
                String[] infoArray = itemInfo.split("_");
                infoMap.put("sysItemId", Long.parseLong(infoArray[0]));
                infoMap.put("sysSkuId", Long.parseLong(infoArray[1]));
                infoMap.put("num", Integer.valueOf(infoArray[2]));
                infoListMap.add(infoMap);
            }
            return JSON.toJSONString(infoListMap);
        } catch (Exception e) {
            LOGGER.error("生成商品信息失败", e);
            return null;
        }
    }

    public static String getItemInfoJson(Trade trade) {
        try {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isEmpty(orders)) {
                return null;
            }
            List<Map<String, Object>> infoListMap = new ArrayList<>();
            for (Order order : orders) {
                Map<String, Object> infoMap = new HashMap<>();
                infoMap.put("sysItemId", order.getItemSysId());
                infoMap.put("sysSkuId", order.getSkuSysId());
                infoMap.put("num", order.getNum());
                infoListMap.add(infoMap);
            }
            return JSON.toJSONString(infoListMap);
        } catch (Exception e) {
            LOGGER.error("生成商品信息失败", e);
            return null;
        }
    }

    public static List<WaveSeedLog> buildSeedLogs(Long waveId, List<WaveSortingDetail> details, String scanCode, Integer scanCodeType) {
        return details.stream().map(detail -> {
            WaveSeedLog log = new WaveSeedLog();
            log.setSortingId(detail.getSortingId());
            log.setDetailId(detail.getId());
            log.setItemNum(detail.getChangeMatchedNum());
            log.setWaveId(waveId);
            log.setOuterId(detail.getOuterId());
            log.setScanCode(scanCode);
            log.setScanCodeType(scanCodeType);
            //登记商品质检员
            log.setChecker(detail.getChecker());
            return log;
        }).collect(Collectors.toList());
    }
    /**
     * 设置订单不匹配波次原因
     *
     * @param trades 订单
     * @param reason 原因
     */
    public static void setNotMatchWave(List<Trade> trades, String reason) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        for (Trade trade : trades) {
            setNotMatchWave(trade, reason);
        }
    }

    public static void setNotMatchWave(Trade trade, String reason) {
        trade.setNotMatchWaveReason(reason);
        setNotMatchWaveId(trade);
    }

    public static void setNotMatchWaveId(Trade trade) {
        trade.setWaveId(Wave.NOT_MATCH_WAVE_ID);
    }

    /**
     * 设置订单不进入波次原因
     *
     * @param trades 订单
     * @param reason 原因
     */
    public static void setNotInWave(List<Trade> trades, String reason) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        for (Trade trade : trades) {
            setNotInWave(trade, reason);
        }
    }

    public static String getNotInWaveRuleName(WaveRule waveRule) {
        String ruleName = "";
        if (waveRule != null && waveRule.getCreateType() != null) {
            String createType = waveRule.getCreateType().getName();
            if (Lists.newArrayList(WaveCreateType.AUTO.getName(), WaveCreateType.BATCH.getName()).contains(createType)) {
                ruleName = createType;
            } else {
                ruleName = waveRule.getName();
            }
        }
        return ruleName;
    }

    public static void setNotInWave(Trade trade, String reason) {
        trade.setNotInWaveReason(reason);
        setNotInWaveId(trade);
    }

    public static void setNotInWaveId(Trade trade) {
        trade.setWaveId(Wave.NOT_IN_WAVE_ID);
    }


    public static NumberRange<Integer> getTradeNumRange(WaveRule rule) {
        int numDown = rule.getNumDown() == null ? WaveRule.DEFAULT_NUM_RANGE_MIN : rule.getNumDown();
        int numUp = rule.getNumUp() == null ? WaveRule.DEFAULT_NUM_RANGE_MAX : rule.getNumUp();

        return new NumberRange<>(numDown, numUp);
    }

    public static NumberRange<Integer> getBoxNumRange(WaveRule rule) {
        int numDown = (rule.getRuleCondition().getBoxNumDown() == null ? WaveRule.DEFAULT_NUM_RANGE_MIN : rule.getRuleCondition().getBoxNumDown());
        int numUp = (rule.getRuleCondition().getBoxNumUp() == null ? WaveRule.DEFAULT_NUM_RANGE_MAX : rule.getRuleCondition().getBoxNumUp());

        return new NumberRange<>(numDown, numUp);
    }

    public static NumberRange<Double> getPositionVolumeRange(WaveRule rule) {
        double numDown = (rule.getRuleCondition().getPositionVolumeDown() == null ? WaveRule.DEFAULT_NUM_RANGE_MIN : rule.getRuleCondition().getPositionVolumeDown());
        double numUp = (rule.getRuleCondition().getPositionVolumeUp() == null ? WaveRule.DEFAULT_SHARE_VOLUME_RANGE_MAX : rule.getRuleCondition().getPositionVolumeUp());
        return new NumberRange<>(numDown, numUp);
    }

    public static boolean waveCalculatorIsAutoCreate(WaveRule rule) {
        return WaveCreateType.AUTO.equals(rule.getCreateType())
                && BooleanUtils.isTrue(rule.getOpenAutoCreate())
                && rule.getRuleCondition() != null
                && rule.getRuleCondition().getAutoWaveNumRange() != null;
    }

    public static NumberRange<Integer> getSharePositionCodeNumRange(WaveRule rule) {
        if (waveCalculatorIsAutoCreate(rule)) {
            NumberRange<Integer> autoWaveNumRange = rule.getRuleCondition().getAutoWaveNumRange();
            if (autoWaveNumRange.getMax() != null && autoWaveNumRange.getMin() != null) {
                return rule.getRuleCondition().getAutoWaveNumRange();
            }
        }
        return rule.getRuleCondition().getPositionCodeNumRange();
    }

    /**
     * 是否需要波次验货
     * 开启包装验货且开启播种包装验货
     *
     * @param tradeConfig
     * @param waveConfig
     * @return
     */
    public static boolean needWaveExamine(TradeConfig tradeConfig, WaveConfig waveConfig) {
        return Objects.equal(tradeConfig.getOpenPackageExamine(), CommonConstants.JUDGE_YES) && BooleanUtils.isTrue(waveConfig.getOpenWavePackage());
    }

    public static WaveItemPosition buildWaveItemPosition(Staff staff, WaveSortingDetail detail) {
        WaveItemPosition itemPosition = new WaveItemPosition();
        itemPosition.setSid(detail.getSid());
        itemPosition.setItemNum(detail.getItemNum());
        itemPosition.setPickedNum(detail.getPickedNum());
        itemPosition.setMatchedNum(detail.getMatchedNum());
        itemPosition.setShortageNum(detail.getShortageNum());
        itemPosition.setSysItemId(detail.getSysItemId());
        itemPosition.setSysSkuId(detail.getSysSkuId());
        itemPosition.setPositionNo(detail.getPositionNo());
        itemPosition.setPositionCode(WaveUtils.generatePositionCode(staff, detail.getPositionNo()));
        itemPosition.setOrderId(detail.getOrderId());
        itemPosition.setCombineId(detail.getCombineId());
        itemPosition.setSuitType(detail.getSuitType());
        return itemPosition;
    }

    public static WaveItemPosition buildWaveItemPosition(Staff staff, Trade trade, Order order) {
        WaveItemPosition itemPosition = new WaveItemPosition();
        itemPosition.setSid(trade == null ? order.getSid() : trade.getSid());
        itemPosition.setItemNum(order.getNum());
        itemPosition.setPickedNum(0);
        itemPosition.setMatchedNum(0);
        itemPosition.setShortageNum(0);
        itemPosition.setSysItemId(order.getItemSysId());
        itemPosition.setSysSkuId(order.getSkuSysId());
        itemPosition.setOrderId(order.getId());
        itemPosition.setSuitType(WaveUtils.getWaveSortingDetailSuitType(order.getType()));
        itemPosition.setCombineId(order.getCombineId());
        return itemPosition;
    }

    /**
     * 是否拣选完成
     *
     * @param wavePicking
     * @return
     */
    public static boolean isPicked(WavePicking wavePicking) {
        return wavePicking != null && wavePicking.getEnableStatus() == WavePicking.STATUS_OVER;
    }

    public static String getExpressKey(Trade trade) {
        return trade.getTemplateId() + "_" + trade.getTemplateType();
    }

    public static boolean checkRule(WaveRule rule) {
        WaveRuleCondition condition = rule.getRuleCondition();
        return condition != null && Objects.equal(condition.getItemKindsDown(), 2) && Objects.equal(condition.getItemKindsUp(), 2)
                && Objects.equal(condition.getItemNumDown(), 2) && Objects.equal(condition.getItemNumUp(), 2);
    }

    public static boolean isAPlusNRule(WaveRule rule) {
        return Objects.equal(rule.getRuleType(), WaveRule.RULE_TYPE_A_N);
    }

    public static boolean isStallRule(WaveRule rule) {
        return Objects.equal(rule.getRuleType(), WaveRule.RULE_TYPE_STALL);
    }

    /**
     * 根据波次规则是否包含某些条件判断是否组装（供应商、品牌、分类）
     * @param rules
     * @param params
     */
    public static void assembleWhetherRuleExistCondition(List<WaveRule> rules, WaveFilterParams params) {
        params.setAssembleItemSupplier(assembleItemSupplier(rules));
        params.setAssembleItemBrand(assembleItemItemBrand(rules));
        params.setAssembleItemSellerCid(assembleItemSellerCid(rules));
        params.setAssembleItemCat(assembleItemCat(rules));
        params.setAssembleItemTag(assembleItemItemTag(rules));
        params.setAssembleBoxNum(assembleBoxNum(rules));
    }

    /**
     * 是否组装商品供应商
     *
     * @param rules
     * @return
     */
    public static boolean assembleItemSupplier(List<WaveRule> rules) {
        return rules.stream().anyMatch(WaveUtils::assembleItemSupplier);
    }

    /**
     * 是否组装商品类目
     */
    public static boolean assembleItemCat(List<WaveRule> rules) {
        return rules.stream().anyMatch(rule ->
                Optional.ofNullable(rule.getRuleCondition()).map(WaveRuleCondition::getSpecifyItemCatCondition).isPresent());
    }

    /**
     * 是否组装商品品牌
     * @param rules
     * @return
     */
    public static boolean assembleItemItemBrand(List<WaveRule> rules) {
        return rules.stream().anyMatch(rule ->
                Optional.ofNullable(rule.getRuleCondition()).map(WaveRuleCondition::getSpecifyItemBrandCondition).isPresent());
    }

    /**
     * 是否组装商品分类
     * @param rules
     * @return
     */
    public static boolean assembleItemSellerCid(List<WaveRule> rules) {
        boolean specify = rules.stream().anyMatch(rule ->
                Optional.ofNullable(rule.getRuleCondition()).map(WaveRuleCondition::getSpecifyItemCategoryCondition).isPresent());

        boolean itemCategoryEq = rules.stream().anyMatch(rule -> rule.getRuleCondition() != null && BooleanUtils.isTrue(rule.getRuleCondition().getItemCategoryEq()));

        boolean skuSpecify = rules.stream().anyMatch(rule ->
                Optional.ofNullable(rule.getRuleCondition()).map(WaveRuleCondition::getSpecifySkuCategoryCondition).isPresent());
        boolean skuCategoryEq = rules.stream().anyMatch(rule -> rule.getRuleCondition() != null && BooleanUtils.isTrue(rule.getRuleCondition().getSkuCategoryEq()));
        return specify || itemCategoryEq || skuSpecify || skuCategoryEq;
    }

    public static boolean assembleItemSupplier(WaveRule rule) {
        WaveRuleCondition condition = rule.getRuleCondition();
        return condition != null
                && ((condition.getSpecifySupplierCondition() != null && CollectionUtils.isNotEmpty(condition.getSpecifySupplierCondition().getSuppliers()))
                        || (condition.getSameSingleSupplier() != null && BooleanUtils.isTrue(condition.getSameSingleSupplier()))
                   );
    }

    public static boolean assembleItemItemTag(List<WaveRule> rules) {
        return rules.stream().anyMatch(rule ->
                Optional.ofNullable(rule.getRuleCondition()).map(WaveRuleCondition::getSpecifyItemTagCondition).isPresent());
    }

    public static boolean assembleBoxNum(List<WaveRule> rules) {
        for (WaveRule rule : rules) {
            WaveRuleCondition ruleCondition = rule.getRuleCondition();
            if (ruleCondition != null && ruleCondition.getBigPick() != null && BooleanUtils.isTrue(ruleCondition.getBigPick())) {
                return true;
            }
        }
        return false;
    }

    public static boolean needPick(Integer giftPickAndCheck) {
        return giftPickAndCheck != null && (giftPickAndCheck == 1 || giftPickAndCheck == 2);
    }

    /**
     * @see Order#getType()
     * order.type：0（普通商品）；1（赠品）；2（套件）；3（组合装）
     * @param orderType
     * @return
     */
    public static Integer getWaveSortingDetailSuitType(Integer orderType) {
        Integer suitType = 0;
        if (orderType != null && orderType == 2) {
            suitType = 1;
        }
        return suitType;
    }

    /**
     * 是否订单波次
     * @param pickType
     * @return
     */
    public static boolean isTradeWave(Integer pickType) {
        return pickType != null && pickType <= 3;
    }

    /**
     * 合单隐藏单系统状态存在待审核，该合单不进入波次
     * @param unableInWaveMergeSidMaps
     * @param fullMergeTrades
     */
    public static void fillNotInWaveMergeSidMaps(Map<Long, String> unableInWaveMergeSidMaps, List<TbTrade> fullMergeTrades) {
        if (CollectionUtils.isEmpty(fullMergeTrades) || unableInWaveMergeSidMaps == null) {
            return;
        }
        List<TbTrade> unableTrades = fullMergeTrades.stream().filter(t -> Objects.equal(t.getIsExcep(), 1)
                && !Objects.equal(t.getSid(), t.getMergeSid())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unableTrades)) {
            Map<Long, Long> map = unableTrades.stream().collect(Collectors.toMap(TbTrade::getMergeSid, TbTrade::getSid, (a, b) -> a));
            for (Map.Entry<Long, Long> entrySet : map.entrySet()) {
                unableInWaveMergeSidMaps.put(entrySet.getKey(), "合单隐藏单[" + entrySet.getValue() + "]异常");
            }
        }
    }

    /**
     * 合单主单is_excep=0，隐藏单is_excep=1，该合单不进入波次
     * @param unableInWaveMergeSidMaps
     * @param mergeSidOrdersMaps
     */
    public static void fillNotInWaveMergeSidMaps(Map<Long, String> unableInWaveMergeSidMaps, Map<Long, List<Order>> mergeSidOrdersMaps) {
        if (MapUtils.isEmpty(mergeSidOrdersMaps) || unableInWaveMergeSidMaps == null) {
            return;
        }
        for (Map.Entry<Long, List<Order>> entrySet : mergeSidOrdersMaps.entrySet()) {
            List<Order> unableOrders = entrySet.getValue().stream().filter(v -> Objects.equal(v.getSysStatus(), Trade.SYS_STATUS_WAIT_AUDIT)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(unableOrders)) {
                unableInWaveMergeSidMaps.put(entrySet.getKey(), "子订单[" + unableOrders.get(0).getSysOuterId() + "]系统状态为待审核");
            }
        }
    }

    public static List<Trade> fillNotInWaveReason(Map<Long, String> unableInWaveMergeSidMaps, List<Trade> trades) {
        for (Trade trade : trades) {
            String notInWaveReason = unableInWaveMergeSidMaps.get(trade.getSid());
            if (StringUtils.isNotEmpty(notInWaveReason)) {
                setNotInWave(trade, notInWaveReason);
            }
        }
        return trades;
    }

    public static boolean needLog(long start) {
        return (System.currentTimeMillis() - start) > 200;
    }

    public static Set<Long> tagIdsToLongSet(String tagIds) {
        if (StringUtils.isBlank(tagIds)) {
            return Sets.newHashSet();
        }
        String[] array = tagIds.split("[|,]");
        Set<Long> set = new HashSet<Long>();
        for (String v : array) {
            if (StringUtils.isBlank(v)) {
                continue;
            }
            Long l = Long.parseLong(v);
            set.add(l);
        }
        return set;
    }

    /**
     * 波次规则条件 指定库区编码格式转换
     *
     * @param sectionAreaCombineCodes
     * @return
     */
    public static Set<String> sectionAreaCombineCodesToStringSet(String sectionAreaCombineCodes) {
        if (StringUtils.isBlank(sectionAreaCombineCodes)) {
            return Collections.emptySet();
        }
        String[] array = processSectionAreaCombineCodes(sectionAreaCombineCodes);
        return Sets.newHashSet(array);
    }

    public static List<String> sectionAreaCombineCodesToStringList(String sectionAreaCombineCodes) {
        if (StringUtils.isBlank(sectionAreaCombineCodes)) {
            return Collections.emptyList();
        }
        String[] array = processSectionAreaCombineCodes(sectionAreaCombineCodes);
        return Lists.newArrayList(array);
    }

    private static String[] processSectionAreaCombineCodes(String sectionAreaCombineCodes) {
        sectionAreaCombineCodes = StringUtils.deleteWhitespace(sectionAreaCombineCodes).replaceAll("[,，]", Wave.SEPARATOR_SECTION_AREA_CODE);
        String[] array = sectionAreaCombineCodes.split("[;；]");
        return array;
    }

    public static void add2SimpleTradeTags(List<WaveRuleCondition.SimpleTradeTag> simpleTradeTags,
                                           WaveRuleCondition.SpecifyTradeTagCondition tagCondition) {
        if (tagCondition != null) {
            if (CollectionUtils.isNotEmpty(tagCondition.getTags())) {
                simpleTradeTags.addAll(tagCondition.getTags());
            }
        }
    }

    public static boolean deleteTradeTags(WaveRuleCondition ruleCondition, WaveRuleCondition.SpecifyTradeTagCondition tagCondition, final Set<Long> tradeTagIds) {
        boolean changed = false;
        if (CollectionUtils.isNotEmpty(tradeTagIds) && tagCondition != null &&
                CollectionUtils.isNotEmpty(tagCondition.getTags())) {
            Set<WaveRuleCondition.SimpleTradeTag> tags = tagCondition.getTags();
            Iterator<WaveRuleCondition.SimpleTradeTag> iterator = tags.iterator();
            while (iterator.hasNext()) {
                WaveRuleCondition.SimpleTradeTag simpleTradeTag = iterator.next();
                if (tradeTagIds.contains(simpleTradeTag.getId())) {
                    iterator.remove();
                    changed = true;
                }
                if (CollectionUtils.isEmpty(tags)) {
                    ruleCondition.setSpecifyTradeTagCondition(null);
                }
            }
        }
        return changed;
    }

    public static boolean deleteWaveRulePointUserIds(List<Long> userIdList, WaveRuleCondition ruleCondition){
        WaveRuleCondition.SpecifyUserCondition specifyUserCondition = ruleCondition.getSpecifyUserCondition();
        if (specifyUserCondition == null ) {
            return false;
        }
        Set<Long> userIds = specifyUserCondition.getUserIds();
        if(CollectionUtils.isNotEmpty(userIds)){
            return userIds.removeIf(userIdList :: contains);
        }
        return false;
    }

    public static boolean deleteWaveRulePickerRole(List<Long> pickerRoleList, WaveRuleCondition ruleCondition){
        String pickerRoleIds = ruleCondition.getPickerRoleIds();
        if (StringUtils.isEmpty(pickerRoleIds)) {
            return false;
        }
        Set<Long> pickerRoles = Strings.getAsLongSet(pickerRoleIds,",",true);
        if(CollectionUtils.isNotEmpty(pickerRoles)){
            boolean success = pickerRoles.removeIf(pickerRoleList::contains);
            if(success){
                ruleCondition.setPickerRoleIds(Strings.join(",",pickerRoles));
            }
            return success;
        }
        return false;
    }

    /**
     * 是否可以拣选中安排拣选员
     * @param pickingArrange
     * @param distributionStatus
     * @param picker
     * @return
     */
    public static boolean isPickingArrange(Integer pickingArrange, Integer distributionStatus, Staff picker) {
        return pickingArrange != null
                && pickingArrange == 1
                && Wave.DISTRIBUTION_STATUS_ON == distributionStatus
                && (picker != null && picker.getId() != null && picker.getId() > 0);
    }

    public static boolean isSeedingArrange(Integer seedingArrange) {
        return seedingArrange != null && seedingArrange == 1;
    }

    public static void buildUpdateWavePicking(List<WavePicking> updates, Staff picker, WavePicking wavePicking) {
        if (wavePicking != null) {
            WavePicking update = new WavePicking();
            update.setId(wavePicking.getId());
            update.setPickerId(picker.getId());
            update.setPickerName(picker.getName());
            update.setWaveId(wavePicking.getWaveId());
            updates.add(update);
        }
    }

    /**
     * 创建波次统计key
     *
     * @param staff
     * @param warehouseId
     * @param ruleGroupId
     * @return
     */
    @Deprecated
    public static String buildWaveStatsKey(Staff staff, Long warehouseId, Long ruleGroupId) {
        return "waveStatsInfo_" + staff.getCompanyId() + "_" + warehouseId + "_" + ruleGroupId;
    }

    public static String buildWaveStatsKey(Staff staff, Long warehouseId, Long ruleGroupId, boolean openStaffCache) {
        String cacheKey =  "waveStatsInfo_" + staff.getCompanyId() + "_" + warehouseId + "_" + ruleGroupId;
        if (openStaffCache) {
            return cacheKey + "_" + staff.getId();
        }
        return cacheKey;
    }

    public static ProgressData buildRuleStatsData(Staff staff, String key) {
        ProgressData progressData = new ProgressData();
        progressData.setCountAll(10);
        progressData.setCountCurrent(0);
        progressData.setProgress(0);
        progressData.setCacheKey(key);
        return progressData;
    }

    public static boolean directPickIntoWss(Wave wave, WaveConfig waveConfig) {
        return isTradeWave(wave.getPickingType()) && waveConfig.getOpenDirectPickIntoWss() != null && waveConfig.getOpenDirectPickIntoWss() == 1;
    }

    public static void tradeValidator(Staff staff, Trade trade, WaveConfig waveConfig, boolean matchedAfter) {
        if (trade == null) {
            return;
        }
        int fastFail = waveConfig.getInteger(WaveChatConfigsEnum.WAVE_SEED_FAST_FAIL.getKey());
        if ((BooleanUtils.toBoolean(fastFail) && matchedAfter)
                || (!BooleanUtils.toBoolean(fastFail) && !matchedAfter)) {
            return;
        }
        TradeValidator validator = new TradeValidator();
        validator.setThrowExceptionIfError(false);
        validator.check(staff, trade);
        if (validator.hasError() && TradeValidator.Error.INSUFFICIENT.getCode() != validator.getCode()) {
            throw new TradeValidatorException(validator.getCode(), validator.getMessage());
        }
    }

    /**
     * 是否支持分段拣选
     * @param pickingType
     * @param waveConfig
     * @return
     */
    public static Integer isSubSectionPick(Integer pickingType, WaveConfig waveConfig) {
        String subSectionPickTypes = waveConfig.getSubSectionPickTypes();
        if (waveConfig == null || pickingType == null
                || StringUtils.isEmpty(subSectionPickTypes)) {
            return 0;
        }
        List<String> types = Arrays.asList(subSectionPickTypes.split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
        return BooleanUtils.toInteger(types.contains(String.valueOf(pickingType)));
    }

    public static boolean includeAPlusN(List<WaveRule> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return false;
        }
        for (WaveRule rule : rules) {
            if (!WaveUtils.isAPlusNRule(rule)) {
                continue;
            }
            WaveRuleCondition ruleCondition = rule.getRuleCondition();
            if (ruleCondition == null) {
                continue;
            }
            return WaveRuleCondition.SORT_TYPE_SECTION.equals(ruleCondition.getSortType());
        }
        return false;
    }

    public static void setScanInfoV2(WavePickingParam param, String barcode, TradePackScanInfo.ScanCodeType type,boolean bindUniqueCodeFlag) {
        param.setScanCode(barcode);
        param.setScanCodeType(getScanCodeTypeV2(param.getOuterId(), barcode, type.getType(), bindUniqueCodeFlag));
    }

    public static Integer getScanCodeTypeV2(String outerId, String scanCode, Integer type,boolean bindUniqueCodeFlag) {
        if (bindUniqueCodeFlag) {
            return TradePackScanInfo.ScanCodeType.UNIQUE_CODE.getType();
        }
        return getScanCodeType(outerId, scanCode, type);
    }

    public static void setScanInfo(WavePickingParam param, String barcode, TradePackScanInfo.ScanCodeType type) {
        param.setScanCode(barcode);
        param.setScanCodeType(getScanCodeType(param.getOuterId(), barcode, type.getType()));
    }

    public static Integer getScanCodeType(String outerId, String scanCode, Integer type) {
        if (TradePackScanInfo.ScanCodeType.NORMAL.getType().equals(type) && !Objects.equal(outerId, scanCode)) {
            return TradePackScanInfo.ScanCodeType.MULTI_CODE.getType();
        }
        return type;
    }


    public static WaveRule buildStallWaveRule(WaveRule exist) {
        // 是否是新增规则
        boolean isNew = (exist == null ? true : false);
        WaveRule rule = (exist == null ? new WaveRule() : exist);
        rule.setName("档口配货");
        if (isNew) { // 如果是新加一个档口配货规则，设置sort为-1
            rule.setSort(-1);
        }
        rule.setRuleType(WaveRule.RULE_TYPE_STALL);
        rule.setNumDown(1);
        rule.setNumUp(1);
        WaveRuleCondition condition = Optional.ofNullable(rule.getRuleCondition()).orElse(new WaveRuleCondition());
        condition.setTradeType(CommonConstants.JUDGE_YES);
        condition.setAutoWaveNumRange(new NumberRange<>(1, 1));
        condition.setPositionCodeNumRange(new NumberRange<>(1, 1));
        rule.setRuleCondition(condition);
        return rule;
    }

    public static WaveRule buildAPlusNWaveRule() {
        WaveRule rule = new WaveRule();
        rule.setName("A+N波次");
        rule.setSort(0);
        rule.setRuleType(WaveRule.RULE_TYPE_A_N);
        rule.setNumDown(1);
        rule.setNumUp(20);
        WaveRuleCondition condition = new WaveRuleCondition();
        condition.setItemKindsDown(2);
        condition.setItemKindsUp(2);
        condition.setItemNumUp(2);
        condition.setItemNumDown(2);
        condition.setSuitBySingle(1);
        rule.setRuleCondition(condition);
        return rule;
    }

    public static List<WaveUniqueCode> buildOrderUniqueCodes(WaveItem waveItem, Integer stockStatus,
                                                             Integer num, Long supplierId,
                                                             String supplierName, Integer codeType,
                                                             Integer itemNum,
                                                             Deque<OrderUniqueCodeUnboundLog> reuseLogs) {
        if (num == null || num <= 0) {
            return Lists.newArrayList();
        }

        List<WaveUniqueCode> codes = Lists.newArrayList();
        for (int i = 0; i < num; i++) {
            WaveUniqueCode code = new WaveUniqueCode();
            code.setCreated(new Date());
            code.setCodeType(codeType);
            code.setStockStatus(stockStatus);
            code.setEnableStatus(CommonConstants.JUDGE_YES);
            code.setOrderId(waveItem.getOrderId());
            code.setOuterId(waveItem.getOuterId());
            code.setPositionNo(waveItem.getPositionNo());
            code.setPositionNoId(waveItem.getPositionNoId());
            code.setSid(waveItem.getSid());
            code.setSysItemId(waveItem.getSysItemId());
            code.setSysSkuId(waveItem.getSysSkuId());
            code.setType(CommonConstants.JUDGE_YES);
            code.setWarehouseId(waveItem.getWarehouseId());
            code.setItemNum(itemNum);
            code.setWaveId(-5L);
            code.setSupplierId(supplierId);
            code.setSupplierName(supplierName);
            code.setStatus(Objects.equal(CommonConstants.JUDGE_YES, stockStatus) ? 2 : 1);
            code.setTradeSource(waveItem.getSource());
            code.setOutSid(waveItem.getOutSid());
            // 生成已下架类型唯一码
            if (Objects.equal(waveItem.getGoodsStatus(), CommonConstants.JUDGE_NO)) {
                code.setStatus(OrderUniqueCodeStatusEnum.OFF_SHELF.getType());
                code.setOffTime(waveItem.getOffTime());
            }
            code.setTagIds(getOrderUniqueStockTagId(code));
            code.setLiveProperty(waveItem.getLiveProperty());
            // 不强制分配供应商用到, 真正的爆款码在最后生成时候填充
            code.setHotSaleCode(waveItem.isHotSaleCodeGenerate() ? "1" : null);
            // 唯一码复用
            OrderUniqueCodeUnboundLog reuseLog = getUnboundLog4Reuse(reuseLogs, stockStatus);
            if (reuseLog != null) {
                code.setId(reuseLog.getUniqueCodeId());
                code.setUniqueCode(reuseLog.getUniqueCode());
            }
            codes.add(code);
        }
        return codes;
    }

    private static OrderUniqueCodeUnboundLog getUnboundLog4Reuse(Deque<OrderUniqueCodeUnboundLog> reuseLogs, Integer stockStatus) {
        if (CollectionUtils.isEmpty(reuseLogs)) {
            return null;
        }
        OrderUniqueCodeUnboundLog log = reuseLogs.pollFirst();
        if (Objects.equal(stockStatus, CommonConstants.JUDGE_YES) || !Objects.equal(log.getStatus(), OrderUniqueCodeStatusEnum.WAIT_PICK.getType())) {
            return log;
        }
        // 外采不复用等待拣选的
        Deque<OrderUniqueCodeUnboundLog> reuseLogsCopy = new ArrayDeque<>();
        while (CollectionUtils.isNotEmpty(reuseLogs)) {
            reuseLogsCopy.addLast(log);
            log = reuseLogs.pollFirst();
            if (Objects.equal(log.getStatus(), OrderUniqueCodeStatusEnum.WAIT_PICK.getType())) {
                continue;
            }
            addDeque4Reuse(reuseLogs, reuseLogsCopy);
            return log;
        }
        reuseLogsCopy.addLast(log);
        addDeque4Reuse(reuseLogs, reuseLogsCopy);
        return null;
    }

    /**
     * 把reuseLogsCopy加到reuseLogs尾部
     *
     * @param reuseLogs
     * @param reuseLogsCopy
     */
    private static void addDeque4Reuse(Deque<OrderUniqueCodeUnboundLog> reuseLogs, Deque<OrderUniqueCodeUnboundLog> reuseLogsCopy) {
        if (CollectionUtils.isEmpty(reuseLogsCopy)) {
            return;
        }
        if (reuseLogs == null) {
            reuseLogs = new ArrayDeque<>();
        }
        while (CollectionUtils.isNotEmpty(reuseLogsCopy)) {
            reuseLogs.addLast(reuseLogsCopy.pollFirst());
        }
    }

    /**
     * 根据唯一码库存状态获取唯一码标签
     */
    public static String getOrderUniqueStockTagId(WaveUniqueCode waveUniqueCode) {
        return Objects.equal(waveUniqueCode.getStockStatus(), CommonConstants.JUDGE_YES) ? OrderUniqueCodeTagEnum.IN_STOCK.getId().toString() : OrderUniqueCodeTagEnum.OUT_STOCK.getId().toString();
    }

    public static boolean openOrderUniqueCode(Staff staff) {
        return  staff.getConf() == null ? false : staff.getConf().openOrderUniqueCode();
    }

    public static boolean openBlindScanIncludeSpecial(Staff staff) {
        return staff.getConf() == null ? false : staff.getConf().openBlindScanIncludeSpecial();
    }

    public static boolean containsAll(Set<WaveRuleCondition.SimpleDmjItem> simpleDmjItems, List<WaveRuleCondition.SimpleDmjItem> orderItems) {
        Map<String, WaveRuleCondition.SimpleDmjItem> ruleItemMap = simpleDmjItems.stream().collect(Collectors.toMap(WaveUtils::buildItemKey, a -> a, (c1, c2) -> c1));
        for (WaveRuleCondition.SimpleDmjItem orderItem : orderItems) {
            String itemKey = buildItemKey(orderItem);
            WaveRuleCondition.SimpleDmjItem ruleItem = ruleItemMap.get(itemKey);
            if (ruleItem == null || !inItemRange(orderItem.getItemNum(), ruleItem)) {
                return false;
            }
        }
        return true;
    }

    public static boolean containsAny(Set<WaveRuleCondition.SimpleDmjItem> simpleDmjItems, List<WaveRuleCondition.SimpleDmjItem> orderItems) {
        Map<String, WaveRuleCondition.SimpleDmjItem> ruleItemMap = simpleDmjItems.stream().collect(Collectors.toMap(WaveUtils::buildItemKey, a -> a, (c1, c2) -> c1));
        for (WaveRuleCondition.SimpleDmjItem orderItem : orderItems) {
            String itemKey = buildItemKey(orderItem);
            WaveRuleCondition.SimpleDmjItem ruleItem = ruleItemMap.get(itemKey);
            if (ruleItem != null && inItemRange(orderItem.getItemNum(), ruleItem)) {
                return true;
            }
        }
        return false;
    }

    private static String buildItemKey(WaveRuleCondition.SimpleDmjItem simpleDmjItem) {
        return simpleDmjItem.getSysItemId() + KEY_JOINER + (simpleDmjItem.getSysSkuId() == null || simpleDmjItem.getSysSkuId() < 0L ? 0L : simpleDmjItem.getSysSkuId());
    }

    private static boolean inItemRange(Integer orderItemNum, WaveRuleCondition.SimpleDmjItem ruleItem) {
        if (orderItemNum == null || (ruleItem.getItemNumDown() == null && ruleItem.getItemNumUp() == null)) {
            return true;
        }
        if (ruleItem.getItemNumDown() != null && ruleItem.getItemNumUp() != null) {
            return orderItemNum >= ruleItem.getItemNumDown() && orderItemNum <= ruleItem.getItemNumUp();
        }
        if (ruleItem.getItemNumDown() != null) {
            return orderItemNum >= ruleItem.getItemNumDown();
        }
        if (ruleItem.getItemNumUp() != null) {
            return orderItemNum <= ruleItem.getItemNumUp();
        }
        return false;
    }

    public static Integer calculateItemKindBySuitSingle(Trade trade) {
        return calculateItemKindBySuitSingle(trade, true);
    }

    /**
     * 按照套件单品返回订单的商品种类数
     * @param trade
     * @return 商品种类数
     */
    public static Integer calculateItemKindBySuitSingle(Trade trade, Boolean splitSuit) {
        Set<String> outerIds = Sets.newHashSet();
        List<Order> subOrders = WaveUtils.getOrdersForWave(new WaveUtils.GetOrdersForWaveParams.Builder()
                .orders(TradeUtils.getOrders4Trade(trade))
                .splitSuit(splitSuit == null ? true : splitSuit)
                .build());
        for (Order order : subOrders) {
            outerIds.add(order.getItemSysId() + "_" + order.getSkuSysId());
        }
        return outerIds.size();
    }

    /**
     * 按照套件单品返回订单的主商品种类数
     * @param trade
     * @return 商品种类数
     */
    public static int calculateMainItemKindBySuitSingle(Trade trade, Boolean splitSuit) {
        Set<Long> outerIds = Sets.newHashSet();
        List<Order> subOrders = WaveUtils.getOrdersForWave(new WaveUtils.GetOrdersForWaveParams.Builder()
                .orders(TradeUtils.getOrders4Trade(trade))
                .splitSuit(splitSuit == null ? true : splitSuit)
                .build());
        for (Order order : subOrders) {
            outerIds.add(order.getItemSysId());
        }
        return outerIds.size();
    }

    /**
     * 分段查询工具方法
     * @param partitionSize
     * @param all
     * @param function
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> List<R> partitionToQuery(int partitionSize, List<T> all, Function<List<T>, List<R>> function) {
        if (CollectionUtils.isEmpty(all)) {
            return new ArrayList<>();
        }

        // 参数去重
        all = all.stream().distinct().collect(Collectors.toList());
        Iterator<List<T>> iterator = new SubListIterator(all, partitionSize);
        List<R> result = Lists.newArrayList();
        while(iterator.hasNext()){
            List<T> list = iterator.next();
            List<R> resultList = function.apply(list);
            if (!CollectionUtils.isEmpty(resultList)) {
                result.addAll(resultList);
            }
        }
        return result;
    }

    /**
     * 构建波次生成查询时间范围
     * @param payTimeStart
     * @param payTimeEnd
     * @param auditTimeStart
     * @param auditTimeStartEnd
     * @return
     */
    public static DateRange initDateRange(Long payTimeStart, Long payTimeEnd, Long auditTimeStart, Long auditTimeStartEnd) {
        if (payTimeStart != null || payTimeEnd != null) {
            return DateRange.init(payTimeStart, payTimeEnd, 1);
        } else {
            return DateRange.init(auditTimeStart, auditTimeStartEnd, 2);
        }
    }

    public static boolean isInsufficientAllWaveNoAllocateRecord(WaveRule rule) {
        if (rule == null || rule.getRuleCondition() == null) {
            return false;
        }
        WaveRuleCondition ruleCondition = rule.getRuleCondition();
        return ruleCondition.isAllowUnderstocked() && ruleCondition.isInsufficientAllWaveNoAllocateRecord();
    }

    public static boolean isWaveSuitCanPrint(Wave wave) {
        return !WaveDistributionStatus.PICKING.getValue().equals(wave.getDistributionStatus())
                && !WaveDistributionStatus.PICKED.getValue().equals(wave.getDistributionStatus())
                && !WaveDistributionStatus.WAIT_SEED.getValue().equals(wave.getDistributionStatus())
                && !WaveDistributionStatus.WAIT_EXAMINE.getValue().equals(wave.getDistributionStatus())
                && !WaveDistributionStatus.SEEDING.getValue().equals(wave.getDistributionStatus())
                && !WaveDistributionStatus.EXAMINING.getValue().equals(wave.getDistributionStatus());
    }

    public static <T extends Wave> List<Long> toWaveIdList(List<T> waveList) {
        List<Long> waveIdList = new ArrayList<>();
        if (waveList != null && waveList.size() > 0) {
            for (Wave wave : waveList) {
                waveIdList.add(wave.getId());
            }
        }
        return waveIdList;
    }

    public static String addWaveTag(String tagIds, long tagId) {
        if (StringUtils.isEmpty(tagIds) || tagIds.equals("0")) {
            return "" + tagId;
        }
        Set<Long> tagSet = Strings.getAsLongSet(tagIds, ",", false);
        tagSet.add(tagId);
        return Strings.join(",", tagSet);
    }

    public static boolean containsWaveTag(String tagIds, long tagId) {
        if (StringUtils.isNotEmpty(tagIds) && !tagIds.equals("0")) {
            Set<Long> tagIdSet = Strings.getAsLongSet(tagIds, ",", false);
            return tagIdSet.contains(tagId);
        }
        return false;
    }

    public static Integer toInteger(Object num){
        if (num == null) {
            return null;
        }
        return Integer.parseInt(num.toString());
    }

    public static Double toDouble(Object num){
        if (num == null) {
            return null;
        }
        return Double.parseDouble(num.toString());
    }

    public static int calculateTradeItemNum(List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return 0;
        }

        TradeConfigContext configContext = TradeConfigContext.builder()
                .itemNumExcludeAfterSendGoods(false)
                .itemNumExcludeVirtual(0)
                .itemNumExcludeNonConsign(0)
                .build();

        int splitBeforeItemNum = 0;
        for (Trade trade : trades) {
            splitBeforeItemNum += TradeUtils.calculateItemNum(TradeUtils.getOrders4Trade(trade), configContext)[1];
        }
        return splitBeforeItemNum;
    }

    public static String listToString(List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return "";
        }
        return Strings.join(",", trades.stream().map(Trade::getSid).collect(Collectors.toList()));
    }

    /**
     * 有效员工公司
     */
    public static void validStaffCompany(Staff sessionStaff, Staff checkStaff) {
        if (sessionStaff.getCompanyId() == null || checkStaff.getCompanyId() == null) {
            return;
        }
        if (!Objects.equal(sessionStaff.getCompanyId(), checkStaff.getCompanyId())) {
            throw new IllegalArgumentException("请选择正确的员工账号!");
        }
    }

    public static boolean supportSellerSend(WaveConfig waveConfig, String sysStatus) {
        if (waveConfig == null || waveConfig.getInteger(WaveChatConfigsEnum.PACK_SUPPORT_SELLER_SEND.getKey()) == 0) {
            return false;
        }
        return Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus) || Trade.SYS_STATUS_FINISHED.equals(sysStatus);
    }


    /**
     * 打印状态
     */
    public static int getPrintStatus(SimpleDateFormat sdf, Date date) {
        try {
            return (date == null || Objects.equal(date, sdf.parse(TradeTimeUtils.INIT_DATE_STR))) ? CommonConstants.JUDGE_NO : CommonConstants.JUDGE_YES;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 填充不在波具体原因
     * key:ruleId
     * value:reason
     */
    public static void fillNotInWaveSpecificReasonMap(List<Trade> notMatchTrades, WaveRule waveRule) {
        if (CollectionUtils.isEmpty(notMatchTrades)) {
            return;
        }
        String ruleName = waveRule != null ? waveRule.getName() : "";
        boolean isAllowUnderstocked = Optional.ofNullable(waveRule).map(WaveRule::getRuleCondition).map(WaveRuleCondition::isAllowUnderstocked).orElse(false);
        for (Trade notMatchTrade : notMatchTrades) {
            // 正常订单不设置原因 || 允许缺货不设置配货原因
            if ((notMatchTrade == null || notMatchTrade.getWaveId() == null || notMatchTrade.getWaveId() >= 0) || (isAllowUnderstocked && Objects.equal(Wave.NOT_IN_WAVE_ID, notMatchTrade.getWaveId()))) {
                continue;
            }
            // -1 配货原因 -2 规则原因
            String reason = Objects.equal(Wave.NOT_IN_WAVE_ID, notMatchTrade.getWaveId()) ?
                    notMatchTrade.getNotInWaveReason() : notMatchTrade.getNotMatchWaveReason();
            LinkedHashMap<String, String> notInWaveSpecificReasonMap = notMatchTrade.getNotInWaveSpecificReasonMap();
            if (MapUtils.isEmpty(notInWaveSpecificReasonMap)) {
                notInWaveSpecificReasonMap = Maps.newLinkedHashMap();
                notInWaveSpecificReasonMap.put(ruleName, reason);
                notMatchTrade.setNotInWaveSpecificReasonMap(notInWaveSpecificReasonMap);
            } else {
                notInWaveSpecificReasonMap.put(ruleName, reason);
            }
        }
    }

    public static List<Long> getDefaultListIfNull(List<Long> list) {
        return list == null ? new ArrayList<>() : list;
    }
    public static void addNotNull(List<String> list, String str) {
        if (str != null && StringUtils.isNotBlank(str)) {
            list.add(str);
        }
    }

    public static void addsNotNull(List<String> list, List<String> strs) {
        if (CollectionUtils.isNotEmpty(strs)) {
            list.addAll(strs);
        }
    }
    public static List<Integer> getDbKeys(Map<Integer, DataSource> dataSourceMap) {
        if (MapUtils.isEmpty(dataSourceMap) || CollectionUtils.isEmpty(dataSourceMap.keySet())) {
            return new ArrayList<>();
        }
        // 777 是测试库
        // > 1000 是报表库
        return dataSourceMap.keySet().stream().filter(key -> !Objects.equal(key, 777) && key < 1000).collect(Collectors.toList());
    }

    public static Staff getNoSupplierInfoStaff(Staff staff) {
        if (staff == null) {
            return staff;
        }

        List<UserRole> userRoleList = staff.getUserRoleList();
        if (CollectionUtils.isNotEmpty(userRoleList)) {
            userRoleList.forEach(role -> role.setOtherPrivilegeSetting(""));
        }
        staff.setSupplierGroup("");
        staff.setUserRoleSupplierPrivilegeSettings("");
        return staff;
    }

    public static Trade findTarget(Staff staff, List<Trade> trades) {
        Trade target = null;
        Trade tempTrade = null;
        for (Trade trade : trades) {
            if (trade.getEnableStatus() - 1 == 0) {
                if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
                    target = trade;
                    break;
                }
                if (tempTrade == null ||
                        (!staff.getUserIdMap().containsKey(tempTrade.getUserId()) && staff.getUserIdMap().containsKey(trade.getUserId())) ||
                        (Trade.SYS_STATUS_CLOSED.equals(tempTrade.getSysStatus()) && !Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus()))) {
                    tempTrade = trade;
                }
            }
        }
        if (target == null) {
            target = tempTrade;
        }
        return target;
    }

    public static void fillCheckedTradeWaveRuleName(Wave wave, WaveRuleType waveRuleType) {
        if (waveRuleType == WaveRuleType.CHECKED_TRADE
                && StringUtils.isNotEmpty(wave.getCondition()) && !wave.getCondition().equals("{}")) {
            wave.setRuleName(wave.getCondition());
        }
    }

    /**
     * List转JSONString,超过最大字符长度限制时截取list
     * @param list
     * @param length 最大字符长度
     * @return
     * @param <T>
     */
    public static <T> String toJSONString(List<T> list, int length) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        try {
            String jsonStr;
            while (true) {
                if (list.isEmpty()) {
                    return null;
                }
                jsonStr = JSON.toJSONString(list);
                if (jsonStr.length() <= length) {
                    break;
                }
                list.remove(list.size() - 1);
            }
            return jsonStr;
        } catch (Exception e) {
            return null;
        }
    }

    public static boolean checkNewPackma(String packmaOuterIds) {
        if (StringUtils.isBlank(packmaOuterIds)) {
            return false;
        }
        try {
            JSONArray array = JSONArray.parseArray(packmaOuterIds);
            int size = array.size();
            for (int i = 0; i < size; i++) {
                JSONObject object = array.getJSONObject(i);
                if (java.util.Objects.equals("new", object.getString("v"))) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 获取指定日期时间的时间戳
     * 可转换日期时间格式：
     * 1. 指定格式标准日期时间
     * 2. 带中文的日期时间如今天、昨天、前天、N天前、今天 12:00:00 10天前 11:11:11
     * @param sdf 日期时间指定标准格式
     * @param dateTimeStr 日期时间字符串
     * @param beginOrEnd 字符串带中文的日期时间且未设置时分秒时设置 0-00:00:00/1-23:59:59
     * @return 时间戳
     */
    public static Long getTimesamp(SimpleDateFormat sdf, String dateTimeStr, Integer beginOrEnd)  {
        try {
            return sdf.parse(dateTimeStr).getTime();
        } catch (ParseException e) {
            if (StringUtils.isEmpty(dateTimeStr)) {
                return null;
            }
            boolean isCNDateTime = CN_TIME_PATTERN.matcher(dateTimeStr).matches();
            if (!isCNDateTime) {
                throw new IllegalArgumentException("输入日期时间格式有误！");
            }
            String[] strArr = dateTimeStr.split(" ");
            if (strArr.length == 1) {
                String dateStr = strArr[0];
                LocalDate localDate = getLocalDate(dateStr);
                return Objects.equal(beginOrEnd, 0) ? LocalDateTime.of(localDate, LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() :
                        LocalDateTime.of(localDate, LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            } else if (strArr.length == 2) {
                String dateStr = strArr[0];
                String timeStr = strArr[1];
                LocalDate localDate = getLocalDate(dateStr);
                LocalTime localTime = getLocalTime(timeStr);
                return LocalDateTime.of(localDate, localTime).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            } else {
                throw new IllegalArgumentException("输入日期时间格式有误！");
            }
        }
    }

    private static LocalDate getLocalDate(String dateStr) {
        LocalDate localDate;
        if ("今天".equals(dateStr)) {
            localDate = LocalDate.now();
        } else if ("昨天".equals(dateStr)) {
            localDate = LocalDate.now().minusDays(1L);
        } else if ("前天".equals(dateStr)) {
            localDate = LocalDate.now().minusDays(2L);
        } else if (dateStr != null && dateStr.contains("天前")) {
            int day = Integer.parseInt(dateStr.replace("天前", ""));
            localDate = LocalDate.now().minusDays(day);
        } else {
            throw new IllegalArgumentException("输入日期格式有误！");
        }
        return localDate;
    }

    private static LocalTime getLocalTime(String timeStr) {
        try {
            return LocalTime.parse(timeStr, DateTimeFormatter.ofPattern("HH:mm:ss"));
        } catch (Exception e) {
            throw new IllegalArgumentException("输入时分秒格式有误！");
        }
    }

    public static List<Trade> buildUpdateWave(Staff staff, List<Trade> trades, Long waveId) {
        List<Trade> updates = Lists.newArrayList();
        for (Trade trade : trades) {
            Trade update = new Trade();
            update.setSid(trade.getSid());
            update.setCompanyId(staff.getCompanyId());
            update.setWaveId(waveId);
            updates.add(trade);
        }
        return updates;
    }
}

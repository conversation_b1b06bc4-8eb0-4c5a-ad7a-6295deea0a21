package com.raycloud.dmj.domain.wave;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.erp.db.model.Model;
import com.raycloud.erp.db.model.Table;

import java.util.Date;
import java.util.List;

/**
 * 波次分拣明细
 * Created by s<PERSON>xianchang on 2017/2/13.
 */
@Table(name = "wave_sorting_detail", routerKey = "waveSortingDetailDbNo")
public class WaveSortingDetail extends Model {

    private static final long serialVersionUID = -6576905391855782781L;
    private Long id;

    private Long companyId;

    /**
     * 分拣Id
     */
    private Long sortingId;

    private Long waveId;

    /**
     * 商家编码
     */
    private String outerId;

    /**
     * order类型：0单品 1套件
     */
    private Integer suitType;

    /**
     * 如果是套件单品,则该字段的值为其所属的套件子订单的id,否则为0
     */
    private Long combineId;

    private String title;

    private String shortTitle;

    private String propertiesName;

    private String propertiesAlias;

    private String picPath;

    private Long orderId;

    private Long sysItemId;

    private Long sysSkuId;

    /**
     * 商品数量
     */
    private Integer itemNum;

    /**
     * 分拣明细所在订单的该商品总数（合单或套件中某种商品的明细会有多条）
     */
    private Integer totalNum;

    /**
     * 已匹配数量
     */
    private Integer matchedNum;

    /**
     * 已拣数量
     */
    private Integer pickedNum;

    private Integer stockNum;

    /**
     * 缺货数
     */
    private Integer shortageNum;

    /**
     * 匹配状态， 0：未匹配   1：匹配中   2：匹配完成
     */
    private Integer matchedStatus;

    /**
     * 箱规json
     */
    private String itemBoxJson;

    private Long sid;

    /**
     * 订单匹配 true表示匹配完成
     */
    private Boolean tradeMatched;

    private Integer printStatus;

    private Long positionNo;

    /**
     * 位置号编码
     */
    private String positionCode;

    /**
     * 货位编码
     */
    private String goodsSectionCode;

    private Integer enableStatus;

    private List<WaveItemPosition> positions;

    private String goodsSectionStr;

    private Date modified;

    private List<PickBackGoodsSection> goodsSections;

    private List<PickBackGoodsSection> goodsSectionsIncr;

    private Boolean qualityType;

    /**
     * 变更的播种数
     */
    private Integer changeMatchedNum;

    /**
     * 拣选员ids，同种商品可能是多个拣选员
     */
    private String pickerIds;

    /**
     * 拣选员名称
     */
    private String pickerNames;
    
    
    /**
     * 播种结束时间
     */
    private Date seedEndTime;
    
    
    /**
     * 播种时间列表
     */
    private List<Date> seedTimeList;

    /**
     * 唯一码
     */
    private String uniqueCode;

    /**
     * 唯一码
     */
    private List<WaveUniqueCode> uniqueCodes;

    /**
     * @see Order#getIsPick()
     * 0：不拣选不验货
     * 1：拣选不验货
     * 2：拣选验货
     * 3：不拣选验货
     */
    private Integer giftPickAndCheck;

    /**
     * 商品条形码
     */
    private String multiCode;

    /**
     * 商品多码
     */
    private List<String> multiCodes;

    private Long sourceOrderId;

    /**
     * 不播种原因，默认0：参与播种； 大于0：不参与播种
     * 1：退款商品
     */
    private Integer notSeedReason;

    /**
     * 质检员
     */
    private String checker;

    /**
     * @see Order#getPicPath()
     */
    private String platformPicPath;

    /**
     * 是否开启批次 0-否 1-是
     */
    private Integer hasBatch;

    /**
     * 是否开启生产日期管理设置 0-否 1-是
     */
    private Integer hasProduct;

    /**
     * 批次号
     */
    private List<String> batchNos;

    /**
     * 生产日期
     */
    private List<Date> productTimes;

    /**
     * 异常信息
     */
    private String exceptionMsg;

    /**
     * 已打印订单商品数量
     */
    private Integer printItemNum;

    private String remark;

    /**
     * 主商家编码
     */
    private String mainOuterId;

    /**
     * 拣选ID
     */
    private Long pickingId;

    /**
     * 商品销售价
     */
    private Double itemPrice;

    /**
     * 绑定的唯一码
     */
    private WaveUniqueCode bindUniqueCode;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 货主id
     */
    private String shipperId;

    /**
     * 货主name
     */
    private String shipperName;

    /**
     * 货主name 兼容pda 显示，和shipperName 一个语义
     */
    private String shipper;


    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getSortingId() {
        return sortingId;
    }

    public void setSortingId(Long sortingId) {
        this.sortingId = sortingId;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public Integer getSuitType() {
        return suitType;
    }

    public void setSuitType(Integer suitType) {
        this.suitType = suitType;
    }

    public Long getCombineId() {
        return combineId;
    }

    public void setCombineId(Long combineId) {
        this.combineId = combineId;
    }

    public Integer getItemNum() {
        return itemNum;
    }

    public void setItemNum(Integer itemNum) {
        this.itemNum = itemNum;
    }

    public Integer getMatchedNum() {
        return matchedNum;
    }

    public void setMatchedNum(Integer matchedNum) {
        this.matchedNum = matchedNum;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Integer getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(Integer printStatus) {
        this.printStatus = printStatus;
    }

    public Integer getMatchedStatus() {
        return matchedStatus;
    }

    public void setMatchedStatus(Integer matchedStatus) {
        this.matchedStatus = matchedStatus;
    }

    public Long getPositionNo() {
        return positionNo;
    }

    public void setPositionNo(Long positionNo) {
        this.positionNo = positionNo;
    }

    public Boolean getTradeMatched() {
        return tradeMatched;
    }

    public void setTradeMatched(Boolean tradeMatched) {
        this.tradeMatched = tradeMatched;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public String getPropertiesName() {
        return propertiesName;
    }

    public void setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getSysItemId() {
        return sysItemId;
    }

    public void setSysItemId(Long sysItemId) {
        this.sysItemId = sysItemId;
    }

    public Long getSysSkuId() {
        return sysSkuId;
    }

    public void setSysSkuId(Long sysSkuId) {
        this.sysSkuId = sysSkuId;
    }

    public String getPropertiesAlias() {
        return propertiesAlias;
    }

    public void setPropertiesAlias(String propertiesAlias) {
        this.propertiesAlias = propertiesAlias;
    }

    public String getPicPath() {
        return picPath;
    }

    public void setPicPath(String picPath) {
        this.picPath = picPath;
    }

    public Integer getStockNum() {
        return stockNum;
    }

    public void setStockNum(Integer stockNum) {
        this.stockNum = stockNum;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getPickedNum() {
        return pickedNum;
    }

    public void setPickedNum(Integer pickedNum) {
        this.pickedNum = pickedNum;
    }

    public List<WaveItemPosition> getPositions() {
        return positions;
    }

    public void setPositions(List<WaveItemPosition> positions) {
        this.positions = positions;
    }


    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getGoodsSectionStr() {
        return goodsSectionStr;
    }

    public void setGoodsSectionStr(String goodsSectionStr) {
        this.goodsSectionStr = goodsSectionStr;
    }

    public List<PickBackGoodsSection> getGoodsSections() {
        return goodsSections;
    }

    public void setGoodsSections(List<PickBackGoodsSection> goodsSections) {
        this.goodsSections = goodsSections;
    }

    public List<PickBackGoodsSection> getGoodsSectionsIncr() {
        return goodsSectionsIncr;
    }

    public void setGoodsSectionsIncr(List<PickBackGoodsSection> goodsSectionsIncr) {
        this.goodsSectionsIncr = goodsSectionsIncr;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Boolean getQualityType() {
        return qualityType;
    }

    public void setQualityType(Boolean qualityType) {
        this.qualityType = qualityType;
    }

    public Integer getChangeMatchedNum() {
        return changeMatchedNum;
    }

    public void setChangeMatchedNum(Integer changeMatchedNum) {
        this.changeMatchedNum = changeMatchedNum;
    }

    public String getPickerIds() {
        return pickerIds;
    }

    public void setPickerIds(String pickerIds) {
        this.pickerIds = pickerIds;
    }

    public String getPickerNames() {
        return pickerNames;
    }

    public void setPickerNames(String pickerName) {
        this.pickerNames = pickerName;
    }

    public Integer getShortageNum() {
        return shortageNum;
    }

    public void setShortageNum(Integer shortageNum) {
        this.shortageNum = shortageNum;
    }

	public Date getSeedEndTime() {
		return seedEndTime;
	}

	public void setSeedEndTime(Date seedEndTime) {
		this.seedEndTime = seedEndTime;
	}

	public List<Date> getSeedTimeList() {
		return seedTimeList;
	}

	public void setSeedTimeList(List<Date> seedTimeList) {
		this.seedTimeList = seedTimeList;
	}
    
    public String getUniqueCode() {
        return uniqueCode;
    }

    public void setUniqueCode(String uniqueCode) {
        this.uniqueCode = uniqueCode;
    }

    public List<WaveUniqueCode> getUniqueCodes() {
        return uniqueCodes;
    }

    public void setUniqueCodes(List<WaveUniqueCode> uniqueCodes) {
        this.uniqueCodes = uniqueCodes;
    }

    public Integer getGiftPickAndCheck() {
        return giftPickAndCheck;
    }

    public void setGiftPickAndCheck(Integer giftPickAndCheck) {
        this.giftPickAndCheck = giftPickAndCheck;
    }

    public String getMultiCode() {
        return multiCode;
    }

    public void setMultiCode(String multiCode) {
        this.multiCode = multiCode;
    }

    public List<String> getMultiCodes() {
        return multiCodes;
    }

    public void setMultiCodes(List<String> multiCodes) {
        this.multiCodes = multiCodes;
    }

    public Long getSourceOrderId() {
        return sourceOrderId;
    }

    public void setSourceOrderId(Long sourceOrderId) {
        this.sourceOrderId = sourceOrderId;
    }

    public Integer getNotSeedReason() {
        return notSeedReason;
    }

    public void setNotSeedReason(Integer notSeedReason) {
        this.notSeedReason = notSeedReason;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public String getPlatformPicPath() {
        return platformPicPath;
    }

    public void setPlatformPicPath(String platformPicPath) {
        this.platformPicPath = platformPicPath;
    }

    public Integer getHasBatch() {
        return hasBatch;
    }

    public void setHasBatch(Integer hasBatch) {
        this.hasBatch = hasBatch;
    }

    public Integer getHasProduct() {
        return hasProduct;
    }

    public void setHasProduct(Integer hasProduct) {
        this.hasProduct = hasProduct;
    }

    public List<String> getBatchNos() {
        return batchNos;
    }

    public void setBatchNos(List<String> batchNo) {
        this.batchNos = batchNos;
    }

    public List<Date> getProductTimes() {
        return productTimes;
    }

    public void setProductTimes(List<Date> productTime) {
        this.productTimes = productTimes;
    }

    public String getItemBoxJson() {
        return itemBoxJson;
    }

    public void setItemBoxJson(String itemBoxJson) {
        this.itemBoxJson = itemBoxJson;
    }

    public String getExceptionMsg() {
        return exceptionMsg;
    }

    public void setExceptionMsg(String exceptionMsg) {
        this.exceptionMsg = exceptionMsg;
    }

    public Integer getPrintItemNum() {
        return printItemNum;
    }

    public void setPrintItemNum(Integer printItemNum) {
        this.printItemNum = printItemNum;
    }

    public String getMainOuterId() {
        return mainOuterId;
    }

    public void setMainOuterId(String mainOuterId) {
        this.mainOuterId = mainOuterId;
    }

    public Long getPickingId() {
        return pickingId;
    }

    public void setPickingId(Long pickingId) {
        this.pickingId = pickingId;
    }

    public Double getItemPrice() {
        return itemPrice;
    }

    public void setItemPrice(Double itemPrice) {
        this.itemPrice = itemPrice;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getGoodsSectionCode() {
        return goodsSectionCode;
    }

    public void setGoodsSectionCode(String goodsSectionCode) {
        this.goodsSectionCode = goodsSectionCode;
    }

    public WaveUniqueCode getBindUniqueCode() {
        return bindUniqueCode;
    }

    public void setBindUniqueCode(WaveUniqueCode bindUniqueCode) {
        this.bindUniqueCode = bindUniqueCode;
    }


    public String getShipper() {
        return shipper;
    }

    public void setShipper(String shipper) {
        this.shipper = shipper;
    }

    public String getShipperId() {
        return shipperId;
    }

    public void setShipperId(String shipperId) {
        this.shipperId = shipperId;
    }

    public String getShipperName() {
        return shipperName;
    }

    public void setShipperName(String shipperName) {
        this.shipperName = shipperName;
    }



}

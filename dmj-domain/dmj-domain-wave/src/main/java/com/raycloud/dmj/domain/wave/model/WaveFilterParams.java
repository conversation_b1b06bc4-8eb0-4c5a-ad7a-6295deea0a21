package com.raycloud.dmj.domain.wave.model;

import com.raycloud.dmj.domain.DateRange;
import com.raycloud.dmj.domain.QueryParam;
import com.raycloud.dmj.domain.item.ItemKey;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.wave.PdaScanWaveConfig;
import com.raycloud.dmj.domain.wave.WaveCreateType;
import com.raycloud.dmj.domain.wave.WaveRule;
import com.raycloud.dmj.domain.wave.WaveRuleType;

import java.io.Serializable;
import java.util.*;

/**
 * Created by guzy on 16/11/25.
 */
public class WaveFilterParams implements Serializable {
    private static final long serialVersionUID = 1870506269126916468L;


    private Long warehouseId;

    private String warehouseIds;

    private Boolean printed;

    /**
     * 是否走 trade_not_consign 表，默认直接走 trade 表
     */
    private boolean useTradeNotConsignTable;

    /**
     *  判断是否需要波次中踢出的订单数
     */
    private boolean needRemoveTradeCount;
    /**
     * 是否已经设置了快递模板
     */
    private Boolean expressSpec;

    /**
     * 是否有发票
     */
    private Boolean hasTaxNo;

    /**
     * 是否包含异常订单
     */
    private Boolean withExcep;

    /**
     * 开启仅缺货异常生成波次
     */
    private Integer openExcepGenerateWave;

    /**
     * 忽略订单状态
     */
    private Boolean ignoreStatus;

    /**
     * 根据商品排序
     */
    private Boolean orderByItem;

    /**
     * 配货状态，多个用逗号分隔
     */
    private String distributionStatus;

    /**
     * 获取面单状态，多个用逗号分隔
     */
    private String outsidStatus;

    private List<Integer> notRuleTypes;

    /**
     * 波次规则类型：a+n、档口等 和 orPickingTypes 成对出现
     */
    private List<Integer> orRuleTypes;

    private List<Integer> orPickingTypes;

    /**
     * 是否获取组团标签
     */
    private Integer tagStatus;

    /**
     * 拣选状态
     */
    private String pickingTypes;

    /**
     * 波次状态
     */
    private String waveTypes;
    /**
     * 波次拣选顺序配置  0  按拣货顺序  1 按同款同商品
     */
    private String pickOrderConfigs;

    /**
     * 波次id
     */
    private Long waveId;

    private Long firstWaveId;

    private Long endWaveId;
    private String expressCompanyId;

    private List<Long> userIds;

    private List<Long> waveIds;

    private List<Long> notWaveIds;

    private List<Long> ignoreWaveIds;

    private List<Long> queryWarehouseIds;

    /**
     * 货主Ids
     */
    private List<String> shipperIds;

    public String getSpecifyShipperIds() {
        return specifyShipperIds;
    }

    public void setSpecifyShipperIds(String specifyShipperIds) {
        this.specifyShipperIds = specifyShipperIds;
    }

    private String specifyShipperIds;

    private String pickStyles;

    /**
     * 波次号
     */
    private String waveIdStr;

    /**
     * 短号
     */
    private String shortIdStr;

    /**
     * 拣选号
     */
    private String pickingCodeStr;

    /**
     * 是否查询多拣选号
     */
    private Integer queryMultiPickingCode;

    /**
     * 波次状态，多个逗号分隔
     */
    private String status;

    /**
     * 是否异常筛选（不过滤null/异常单1/无异常0）
     */
    private Integer isExcep;

    /*********** 以下是具体查询中用到传参 ***********/
    private String groupFields;

    /**
     * 快递模板id
     */
    private Long expressTemplateId;

    /**
     * 快递类型，0表示普通快递，1表示电子面单快递，默认为0
     */
    private Integer expressTemplateType;

    /**
     * 波次列表排序按照相同波次规则排序 0：关闭， 1：开启 默认0
     */
    private Integer waveSortRule;

    /**
     * 波次列表排序按照波次创建时间，0：正序，1：倒序，默认0
     */
    private Integer waveSortTime;

    /**
     * 库存排序 0-正序 1-倒序
     */
    private Integer sectionAreasSort;

    /**
     * 波次列表排序根据分派人排 0：拣选人 1：播种人
     */
    private Integer waveSortAssigner;

    /**
     * 波次列表排序按照加急标签排序 0：关闭， 1：开启
     */
    private Integer waveSortUrgent;

    /**
     * 波次列表对补拣波次特殊处理
     */
    private Integer wavePickAfter;
    /**
     * 波次列表不查接力退出的波次
     */
    private Integer excludeRelayPicker;

    /**
     * 随机排序 0：关闭， 1：开启
     */
    private Integer waveSortRandom;

    /**
     * 来源类型  PDA  CAIGOU  ...
     */
    private String sourceType;

    /**
     * 店铺id
     */
    private Long userId;

    /**
     * 商品种类数
     */
    private Integer itemKindNum;
    private Integer startRow;
    private Integer pageSize;

    private Date start;

    private Date end;

    private String key;

    /**
     * 是否完成验货登记
     */
    private Boolean checkGoodsFinished;

    private String pickingCode;

    /**
     * 忽略没有拣选的波次
     */
    private Boolean ignoreNotPick;

    /**
     * 拣选人，多个逗号分隔
     */
    private String pickerId;

    /**
     * 播种人，多个逗号分隔
     */
    private String sorterId;

    private Long ruleId;

    private String ruleIds;

    /**
     * 是否记录不参与波次计算的订单
     */
    private Boolean logFilterTrades;

    /**
     * 商品数量下限
     */
    private Integer itemNumDown;

    /**
     * 商品数量上限
     */
    private Integer itemNumUp;

    /**
     * 订单数量下限
     */
    private Integer tradeNumDown;

    /**
     * 订单数量上限
     */
    private Integer tradeNumUp;

    /**
     * 已安排拣选人
     */
    private String assignPickers;

    /**
     * 已安排播种人
     */
    private String assignSorters;

    /**
     * 播种人
     */
    private String sorters;

    /**
     * 安排状态
     * 0：未安排 1：已安排
     */
    private Integer assignStatus;

    /**
     * 缺货数比较
     * 0： 缺货数=0    1：缺货数>0
     */
    private Integer shortageNumCompare;

    /**
     * 未拣数比较
     * 0： 未拣数=0    1：未拣数>0
     */
    private Integer unPickNumCompare;

    /**
     * 商家编码
     */
    private String outerId;


    /**
     * 商家编码类型
     * 0: 规格商家编码
     * 1: 主商家编码
     */
    private Integer outerIdType;

    /**
     * 商家编码查询类型
     * 0：包含任意
     * 1：包含所有
     */
    private Integer outerIdQueryType;

    /**
     * 排序字段，可排序字段包括：订单数量/商品数量/商品种类
     */
    private String sortField;

    /**
     * asc/desc
     */
    private String sortOrder;

    /**
     *  是否重新计算商品总数、订单数量
     */
    private Integer reCalculationNum;


    /**
     * 波次规则分组id
     */
    private Long ruleGroupId;

    /**
     * 波次规则分组ids
     */
    private String ruleGroupIds;



    /**
     * 波次管理页面 多选状态字段,选择了未完成(未拣选/拣选中/等待播种/播种中/等待验货/验货中) + 已完成/已取消  多种状态时候生效
     */
    private Boolean statusAndDistributionStatus = false;

    /**
     * 快递
     */
    private String express;

    /**
     * 快递 内部处理逻辑使用
     */
    private Map<Integer, List<Long>> expressMap;



    private Integer openWaveUniqueCode;

    /**
     * 原版
     * 未打印 0：波次中有效订单全部未打印
     * 已打印 1：波次中有效订单全部已打印
     * 部分打印 2：波次中有效订单部分打印，部分未打印；
     * 存在状态 3：存在打印状态
     * -------------------
     * 20230427 版本
     * 未打印 0：      波次中存在未被踢出的订单，并且都没有被打印
     * 已打印 1：      波次中存在未被踢出的订单，并且全部都被打印了。
     * 部分打印 2：    波次中存在未被踢出的订单，并且有部分被打印，有部分没有打印
     * 无需打印状态 4: 指波次中的订单都被踢出了波次
     */
    private String printStatus;

    /**
     * 波次订单查询限制
     */
    private int waveTradeLimit = 0;

    /**
     * 上一次计算的最大sid
     */
    private Long lastMaxSid;

    /**
     * 是否组装商品供应商
     */
    private boolean assembleItemSupplier;
    /**
     * 是否组装商品品牌
     */
    private boolean assembleItemBrand;

    /**
     * 是否组装箱规
     */
    private boolean assembleBoxNum;

    /**
     * 是否组装商品供分类
     */
    private boolean assembleItemSellerCid;
    /**
     * 是否组装商品类目
     */
    private boolean assembleItemCat;
    /**
     * 是否组装商品标签
     */
    private boolean assembleItemTag;

    /**
     * 是否组装货主
     */
    private boolean assembleShipper;

    /**
     * 波次完结开始时间
     */
    private Long finishedStartTime;

    /**
     * 波次完结结束时间
     */
    private Long finishedEndTime;

    /**
     * 是否填充具体信息
     */
    private Boolean isFillInfo;

    /**
     * 查询类型集合
     */
    private List<QueryParam> queryParams;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 商品key 多个逗号分隔，item和sku之间"_"连接
     */
    private String itemKeys;

    /**
     * 波次生成类型
     */
    private WaveCreateType waveCreateType;


    /**
     * temu，shein 区分 ，其他场景没用
     */
    private WaveRuleType waveRuleType;


    /**
     * 跨境波次批次分组
     */
    private String crossBorderWaveUnderstockedGroup;

    /**
     * ip
     */
    private String ip;

    /**
     * 生成波次，勾选的序号
     */
    private List<Integer> indexs;

    /**
     * 波次备注
     */
    private String waveRemark;

    /**
     * 优先备货区配货
     */
    private Boolean backRegionPickFirst;

    private List<Long> notInWaveRuleGroupIds;

    /**
     * 是否查询多件订单（套件算多件）
     */
    private Boolean multi;

    private List<Long> sids;

    private List<Long> shortIdsList;

    private List<String> outSids;

    private List<String> tids;

    /**
     * 正在拣选中的拣选人Id
     */
    private Long pickingPickerId;

    /**
     * 波次备注
     */
    private String remarkKey;

    /**
     * 标签集合
     */
    private String tagIds;

    /**
     * 订单付款范围
     */
    private DateRange payTimeRange;

    /**
     * 是否分段拣选，0：否（默认）1：是
     */
    private Integer subSectionPick;

    private Long subSectionPickerId;

    private List<WaveRule> rules;

    private Boolean needSort;

    private Long waveRuleGroupId;

    private WaveRule waveRuleQuery;

    /**
     * 计算未验单量 0 否 1 是
     */
    private Integer countUnPackNum;

    /**
     * 有领取失败标识的波次，放在后面
     */
    private Integer failedLast;

    /**
     * 修改时间范围
     */
    private DateRange modifiedRange;

    /**
     * 是否预发货
     */
    private Integer isConsignInAdvance;

    /**
     * 波次订单库区
     */
    private String sectionAreas;

    private String shopUserIds;

    /**
     * 常态合作码
     */
    private String cooperationNo;

    /**
     * 送货仓库
     */
    private String sellSite;

    /**
     * 是否需要先分组然后配货
     */
    private boolean needGroupThenAllocate;

    private List<Long> templateIds;
    /**
     * 拣选开始时间
     */
    private Date pickStartTime;

    /**
     * 拣选结束时间
     */
    private Date pickEndTime;

    private String notInWaveReason;

    private List<Long> logisticsCompanyIdList;

    private boolean fromPage;

    /**
     * 是否是还款商品的查询
     */
    private Boolean itemChangeFlagQuery;

    /**
     * 波次送打状态 0 未送打  1 已送打  为空表示查询所有状态
     */
    private Integer waveSendPrintStatus;

    /**
     * 查询系统单号集合
     */
    private String querySids;

    /**
     * 查询系统短号集合
     */
    private String queryShortIds;

    /**
     * 查询平台单号集合
     */
    private String queryTids;

    /**
     * 查询快递单号集合
     */
    private String queryOutSids;

    /**
     * 四合一查询关键字
     */
    private String queryKey;


    private Boolean aiPackmaFlag;

    public String getQuerySids() {
        return querySids;
    }

    public void setQuerySids(String querySids) {
        this.querySids = querySids;
    }

    public String getQueryShortIds() {
        return queryShortIds;
    }

    public void setQueryShortIds(String queryShortIds) {
        this.queryShortIds = queryShortIds;
    }

    public String getQueryTids() {
        return queryTids;
    }

    public void setQueryTids(String queryTids) {
        this.queryTids = queryTids;
    }

    public String getQueryOutSids() {
        return queryOutSids;
    }

    public void setQueryOutSids(String queryOutSids) {
        this.queryOutSids = queryOutSids;
    }

    public String getQueryKey() {
        return queryKey;
    }

    public void setQueryKey(String queryKey) {
        this.queryKey = queryKey;
    }

    /**
     * 设置规则是否全部都是不允许缺货，打标，缺货订单配货无需反复配货
     */
    private boolean noRuleAllowUnderStocked;


    /**
     * 不允许缺货配货需要移除的sid 不是主流程，特定场景性能优化
     */
    private Set<Long> underStockedRemoveSids;

    private boolean backendWaveNotInReasonQuery;

    public Integer getWaveSendPrintStatus() {
        return waveSendPrintStatus;
    }

    public void setWaveSendPrintStatus(Integer waveSendPrintStatus) {
        this.waveSendPrintStatus = waveSendPrintStatus;
    }

    public boolean isFromPage() {
        return fromPage;
    }

    public void setFromPage(boolean fromPage) {
        this.fromPage = fromPage;
    }

    private Map<Long, Integer> sidIndexMap;

    /**
     * 包括空单
     */
    private Integer includeScalping;

    /**
     * 打印状态新逻辑
     */
    private boolean openPrintStatusV2;

    /**
     * 精确统计波次订单数量
     */
    private boolean accurateStatWaveTradeNum;

    /**
     * 波次
     * @return
     */
    private Long pageId;

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public String getShopUserIds() {
        return shopUserIds;
    }

    public void setShopUserIds(String shopUserIds) {
        this.shopUserIds = shopUserIds;
    }

    private Map<Long, List<Order>> sid2OrdersMap;

    public String getCooperationNo() {
        return cooperationNo;
    }

    public void setCooperationNo(String cooperationNo) {
        this.cooperationNo = cooperationNo;
    }

    public String getSellSite() {
        return sellSite;
    }

    public void setSellSite(String sellSite) {
        this.sellSite = sellSite;
    }

    /**
     * 是否包装验货
     */
    private Integer isPackage;

    /**
     * 不包含的标签，目前是即入即出业务使用，有该标签的订单不生成波次
     */
    private List<Long> notTagIds;

    private String logisticsCompanyIds;

    /**
     * 拣选货单打印状态，默认0
     */
    private Integer pickListPrintStatus;
    /**
     * 订单管理查询条件
     */
    private TradeQueryParams tradeQueryParams;

    /**
     * 创建人，多个逗号分隔
     */
    private String creatorId;
    /**
     * 是否根据规则过滤订单
     */
    private Boolean hasFilter;

    private Set<Long> sysItemIds;

    private Set<Long> sysSkuIds;

    private Set<ItemKey> itemKeyList;

    /**
     *  商家变阿妈查询用wave_id
     */
    private List<Long> waveIdsByItemSearch;


    /**
     * 拣货人如果是拣货组
     */
    private List<Long> assignPickerRoleIds;

    /**
     * 页面搜索用多个角色用,隔开
     */
    private String pickerRoleId;

    /**
     * 是否查询波次需要过滤当前人 拣货角色 是否符合，目前只有pda入口
     */
    private Boolean pickerRoleMatch;

    public Boolean getPickerRoleMatch() {
        return pickerRoleMatch;
    }

    public void setPickerRoleMatch(Boolean pickerRoleMatch) {
        this.pickerRoleMatch = pickerRoleMatch;
    }
    public String getPickerRoleId() {
        return pickerRoleId;
    }

    public void setPickerRoleId(String pickerRoleId) {
        this.pickerRoleId = pickerRoleId;
    }

    /**
     * KMERP-177656  在web页面调用"/trade/wave/not/in/trade"接口时候，如果使用多线程查询，会出现数据错乱的问题
     * 故需要区分一下访问来源，该字段为true，表示通过web访问，为null或者false都是非web入口访问
     */
    private Boolean webAccessPortalFlag;




    /**
     * 跨境波次配置
     */
    private CrossBorderWaveConfig crossborderWaveConfig;

    /**
     * 跨境订单是否生成拣货波次，有可能只有加工波次
     */
    private Boolean crossBorderGeneratePickWave;

    /**
     * 波次打印/波次管理商家编码查询类型
     * 1. 模糊搜索 2. 精确搜索
     */
    private Integer outerIdsVagueType;

    public Integer getOuterIdsVagueType() {
        return outerIdsVagueType;
    }

    public void setOuterIdsVagueType(Integer outerIdsVagueType) {
        this.outerIdsVagueType = outerIdsVagueType;
    }

    public CrossBorderWaveConfig getCrossborderWaveConfig() {
        return crossborderWaveConfig;
    }

    public void setCrossborderWaveConfig(CrossBorderWaveConfig crossborderWaveConfig) {
        this.crossborderWaveConfig = crossborderWaveConfig;
    }

    public Set<Long> getAllocateOrderIds() {
        return allocateOrderIds;
    }

    public void setAllocateOrderIds(Set<Long> allocateOrderIds) {
        this.allocateOrderIds = allocateOrderIds;
    }

    public Boolean getCrossBorderGeneratePickWave() {
        return crossBorderGeneratePickWave;
    }

    public void setCrossBorderGeneratePickWave(Boolean crossBorderGeneratePickWave) {
        this.crossBorderGeneratePickWave = crossBorderGeneratePickWave;
    }



    /**
     * 配货的子订单，有可能1个订单下存在部分子订单无需配货
     */
    private Set<Long> allocateOrderIds;



    /**
     * PDA波次拣选列表，是否只有暂时分配给自己的波次
     */
    private Boolean onlyShowAssignSelf=false;

    /**
     *  未生成波次原因按照订单数量排序字段asc/desc
     * @return
     */
    private String itemNumSort;

    public String getItemNumSort() {
        return itemNumSort;
    }

    public void setItemNumSort(String itemNumSort) {
        this.itemNumSort = itemNumSort;
    }

    public PdaScanWaveConfig getPdaScanWaveConfig() {
        return pdaScanWaveConfig;
    }

    public void setPdaScanWaveConfig(PdaScanWaveConfig pdaScanWaveConfig) {
        this.pdaScanWaveConfig = pdaScanWaveConfig;
    }

    /**
     * pda扫码生成波次配置
     */
    private PdaScanWaveConfig pdaScanWaveConfig;

    public Boolean getWebAccessPortalFlag() {
        return webAccessPortalFlag;
    }

    public void setWebAccessPortalFlag(Boolean webAccessPortalFlag) {
        this.webAccessPortalFlag = webAccessPortalFlag;
    }

    boolean groupByTrade;

    public boolean isAssembleBoxNum() {
        return assembleBoxNum;
    }

    public void setAssembleBoxNum(boolean assembleBoxNum) {
        this.assembleBoxNum = assembleBoxNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public WaveFilterParams setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public Long getExpressTemplateId() {
        return expressTemplateId;
    }

    public void setExpressTemplateId(Long expressTemplateId) {
        this.expressTemplateId = expressTemplateId;
    }

    public Integer getExpressTemplateType() {
        return expressTemplateType;
    }

    public void setExpressTemplateType(Integer expressTemplateType) {
        this.expressTemplateType = expressTemplateType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getItemKindNum() {
        return itemKindNum;
    }

    public void setItemKindNum(Integer itemKindNum) {
        this.itemKindNum = itemKindNum;
    }

    public String getGroupFields() {
        return groupFields;
    }

    public WaveFilterParams setGroupFields(String groupFields) {
        this.groupFields = groupFields;
        return this;
    }

    public Integer getSectionAreasSort() {
        return sectionAreasSort;
    }

    public void setSectionAreasSort(Integer sectionAreasSort) {
        this.sectionAreasSort = sectionAreasSort;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public WaveFilterParams setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
        return this;
    }

    public Boolean getPrinted() {
        return printed;
    }

    public WaveFilterParams setPrinted(Boolean printed) {
        this.printed = printed;
        return this;
    }

    public Boolean getExpressSpec() {
        return expressSpec;
    }

    public WaveFilterParams setExpressSpec(Boolean expressSpec) {
        this.expressSpec = expressSpec;
        return this;
    }

    public Integer getOpenExcepGenerateWave() {
        return openExcepGenerateWave;
    }

    public void setOpenExcepGenerateWave(Integer openExcepGenerateWave) {
        this.openExcepGenerateWave = openExcepGenerateWave;
    }

    public Long getWaveId() {
        return waveId;
    }

    public WaveFilterParams setWaveId(Long waveId) {
        this.waveId = waveId;
        return this;
    }

    public Long getFirstWaveId() {
        return firstWaveId;
    }

    public void setFirstWaveId(Long firstWaveId) {
        this.firstWaveId = firstWaveId;
    }

    public Long getEndWaveId() {
        return endWaveId;
    }

    public void setEndWaveId(Long endWaveId) {
        this.endWaveId = endWaveId;
    }

    public String getDistributionStatus() {
        return distributionStatus;
    }

    public void setDistributionStatus(String distributionStatus) {
        this.distributionStatus = distributionStatus;
    }

    public String getOutsidStatus() {
        return outsidStatus;
    }

    public void setOutsidStatus(String outsidStatus) {
        this.outsidStatus = outsidStatus;
    }

    public String getPickingTypes() {
        return pickingTypes;
    }

    public void setPickingTypes(String pickingTypes) {
        this.pickingTypes = pickingTypes;
    }

    public String getWaveTypes() {
        return waveTypes;
    }

    public void setWaveTypes(String waveTypes) {
        this.waveTypes = waveTypes;
    }

    public String getPickOrderConfigs() {
        return pickOrderConfigs;
    }

    public void setPickOrderConfigs(String pickOrderConfigs) {
        this.pickOrderConfigs = pickOrderConfigs;
    }

    public WaveFilterParams setStartRow(Integer startRow) {
        this.startRow = startRow;
        return this;
    }

    public Integer getStartRow() {
        return startRow;
    }

    public Boolean getHasTaxNo() {
        return hasTaxNo;
    }

    public void setHasTaxNo(Boolean hasTaxNo) {
        this.hasTaxNo = hasTaxNo;
    }

    public List<Long> getWaveIds() {
        return waveIds;
    }

    public WaveFilterParams setWaveIds(List<Long> waveIds) {
        this.waveIds = waveIds;
        return this;
    }

    public String getWaveIdStr() {
        return waveIdStr;
    }

    public void setWaveIdStr(String waveIdStr) {
        this.waveIdStr = waveIdStr;
    }

    public Boolean getWithExcep() {
        return withExcep;
    }

    public WaveFilterParams setWithExcep(Boolean withExcep) {
        this.withExcep = withExcep;
        return this;
    }

    public Boolean getIgnoreStatus() {
        return ignoreStatus;
    }

    public WaveFilterParams setIgnoreStatus(Boolean ignoreStatus) {
        this.ignoreStatus = ignoreStatus;
        return this;
    }

    public Boolean getOrderByItem() {
        return orderByItem;
    }

    public WaveFilterParams setOrderByItem(Boolean orderByItem) {
        this.orderByItem = orderByItem;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getStart() {
        return start;
    }

    public void setStart(Date start) {
        this.start = start;
    }

    public Date getEnd() {
        return end;
    }

    public void setEnd(Date end) {
        this.end = end;
    }

    public Integer getWaveSortRule() {
        return waveSortRule;
    }

    public void setWaveSortRule(Integer waveSortRule) {
        this.waveSortRule = waveSortRule;
    }

    public Integer getWaveSortTime() {
        return waveSortTime;
    }

    public void setWaveSortTime(Integer waveSortTime) {
        this.waveSortTime = waveSortTime;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public WaveFilterParams setUserIds(List<Long> userIds) {
        this.userIds = userIds;
        return this;
    }

    public String getKey() {
        return key;
    }

    public WaveFilterParams setKey(String key) {
        this.key = key;
        return this;
    }

    public Boolean getCheckGoodsFinished() {
        return checkGoodsFinished;
    }

    public void setCheckGoodsFinished(Boolean checkGoodsFinished) {
        this.checkGoodsFinished = checkGoodsFinished;
    }

    public String getPickingCode() {
        return pickingCode;
    }

    public void setPickingCode(String pickingCode) {
        this.pickingCode = pickingCode;
    }

    public String getPickerId() {
        return pickerId;
    }

    public void setPickerId(String pickerId) {
        this.pickerId = pickerId;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getWarehouseIds() {
        return warehouseIds;
    }

    public void setWarehouseIds(String warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    public String getRuleIds() {
        return ruleIds;
    }

    public void setRuleIds(String ruleIds) {
        this.ruleIds = ruleIds;
    }

    public Boolean getLogFilterTrades() {
        return logFilterTrades;
    }

    public void setLogFilterTrades(Boolean logFilterTrades) {
        this.logFilterTrades = logFilterTrades;
    }

    public String getSorterId() {
        return sorterId;
    }

    public void setSorterId(String sorterId) {
        this.sorterId = sorterId;
    }

    public String getAssignPickers() {
        return assignPickers;
    }

    public void setAssignPickers(String assignPickers) {
        this.assignPickers = assignPickers;
    }

    public String getAssignSorters() {
        return assignSorters;
    }

    public void setAssignSorters(String assignSorters) {
        this.assignSorters = assignSorters;
    }

    public Integer getAssignStatus() {
        return assignStatus;
    }

    public void setAssignStatus(Integer assignStatus) {
        this.assignStatus = assignStatus;
    }

    public Integer getShortageNumCompare() {
        return shortageNumCompare;
    }

    public void setShortageNumCompare(Integer shortageNumCompare) {
        this.shortageNumCompare = shortageNumCompare;
    }

    public Integer getUnPickNumCompare() {
        return unPickNumCompare;
    }

    public void setUnPickNumCompare(Integer unPickNumCompare) {
        this.unPickNumCompare = unPickNumCompare;
    }

    public Integer getItemNumDown() {
        return itemNumDown;
    }

    public void setItemNumDown(Integer itemNumDown) {
        this.itemNumDown = itemNumDown;
    }

    public Integer getItemNumUp() {
        return itemNumUp;
    }

    public void setItemNumUp(Integer itemNumUp) {
        this.itemNumUp = itemNumUp;
    }

    public Integer getTradeNumDown() {
        return tradeNumDown;
    }

    public void setTradeNumDown(Integer tradeNumDown) {
        this.tradeNumDown = tradeNumDown;
    }

    public Integer getTradeNumUp() {
        return tradeNumUp;
    }

    public void setTradeNumUp(Integer tradeNumUp) {
        this.tradeNumUp = tradeNumUp;
    }

    public Boolean getIgnoreNotPick() {
        return ignoreNotPick;
    }

    public void setIgnoreNotPick(Boolean ignoreNotPick) {
        this.ignoreNotPick = ignoreNotPick;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public Integer getOuterIdType() {
        return outerIdType;
    }

    public void setOuterIdType(Integer outerIdType) {
        this.outerIdType = outerIdType;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getReCalculationNum() {
        return reCalculationNum;
    }

    public void setReCalculationNum(Integer reCalculationNum) {
        this.reCalculationNum = reCalculationNum;
    }

	public Long getRuleGroupId() {
		return ruleGroupId;
	}

	public void setRuleGroupId(Long ruleGroupId) {
		this.ruleGroupId = ruleGroupId;
	}

	public String getRuleGroupIds() {
		return ruleGroupIds;
	}

	public void setRuleGroupIds(String ruleGroupIds) {
		this.ruleGroupIds = ruleGroupIds;
	}

    public Integer getOuterIdQueryType() {
        return outerIdQueryType;
    }

    public void setOuterIdQueryType(Integer outerIdQueryType) {
        this.outerIdQueryType = outerIdQueryType;
    }

    public Integer getWaveSortAssigner() {
        return waveSortAssigner;
    }

    public void setWaveSortAssigner(Integer waveSortAssigner) {
        this.waveSortAssigner = waveSortAssigner;
    }

    public Integer getWaveSortUrgent() {
        return waveSortUrgent;
    }

    public void setWaveSortUrgent(Integer waveSortUrgent) {
        this.waveSortUrgent = waveSortUrgent;
    }

    public Integer getWavePickAfter() {
        return wavePickAfter;
    }

    public void setWavePickAfter(Integer wavePickAfter) {
        this.wavePickAfter = wavePickAfter;
    }

    public Integer getExcludeRelayPicker() {
        return excludeRelayPicker;
    }

    public void setExcludeRelayPicker(Integer excludeRelayPicker) {
        this.excludeRelayPicker = excludeRelayPicker;
    }

    public Integer getWaveSortRandom() {
        return waveSortRandom;
    }

    public void setWaveSortRandom(Integer waveSortRandom) {
        this.waveSortRandom = waveSortRandom;
    }

    public String getSorters() {
		return sorters;
	}

	public void setSorters(String sorters) {
		this.sorters = sorters;
	}

    public Boolean getStatusAndDistributionStatus() {
        return statusAndDistributionStatus;
    }

    public void setStatusAndDistributionStatus(Boolean statusAndDistributionStatus) {
        this.statusAndDistributionStatus = statusAndDistributionStatus;
    }

	public String getExpress() {
		return express;
	}

	public void setExpress(String express) {
		this.express = express;
	}

	public Map<Integer, List<Long>> getExpressMap() {
		return expressMap;
	}

	public void setExpressMap(Map<Integer, List<Long>> expressMap) {
		this.expressMap = expressMap;
	}



    public String getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(String printStatus) {
        this.printStatus = printStatus;
    }

    public int getWaveTradeLimit() {
        return waveTradeLimit;
    }

    public void setWaveTradeLimit(int waveTradeLimit) {
        this.waveTradeLimit = waveTradeLimit;
    }

    public Integer getOpenWaveUniqueCode() {
        return openWaveUniqueCode;
    }

    public void setOpenWaveUniqueCode(Integer openWaveUniqueCode) {
        this.openWaveUniqueCode = openWaveUniqueCode;
    }


    public Long getLastMaxSid() {
        return lastMaxSid;
    }

    public void setLastMaxSid(Long lastMaxSid) {
        this.lastMaxSid = lastMaxSid;
    }

    public boolean isAssembleItemSupplier() {
        return assembleItemSupplier;
    }

    public void setAssembleItemSupplier(boolean assembleItemSupplier) {
        this.assembleItemSupplier = assembleItemSupplier;
    }

    public Date getFinishedStartTime() {
        if (finishedStartTime == null) {
            return null;
        }
        return new Date(finishedStartTime);
    }

    public void setFinishedStartTime(Long finishedStartTime) {
        this.finishedStartTime = finishedStartTime;
    }

    public Date getFinishedEndTime() {
        if (finishedEndTime == null) {
            return null;
        }
        return new Date(finishedEndTime);
    }

    public void setFinishedEndTime(Long finishedEndTime) {
        this.finishedEndTime = finishedEndTime;
    }

    public List<Long> getQueryWarehouseIds() {
        return queryWarehouseIds;
    }

    public void setQueryWarehouseIds(List<Long> queryWarehouseIds) {
        this.queryWarehouseIds = queryWarehouseIds;
    }

    public List<QueryParam> getQueryParams() {
        return queryParams;
    }

    public void setQueryParams(List<QueryParam> queryParams) {
        this.queryParams = queryParams;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getItemKeys() {
        return itemKeys;
    }

    public void setItemKeys(String itemKeys) {
        this.itemKeys = itemKeys;
    }

    public WaveCreateType getWaveCreateType() {
        return waveCreateType;
    }

    public void setWaveCreateType(WaveCreateType waveCreateType) {
        this.waveCreateType = waveCreateType;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public List<Integer> getIndexs() {
        return indexs;
    }

    public void setIndexs(List<Integer> indexs) {
        this.indexs = indexs;
    }

    public String getWaveRemark() {
        return waveRemark;
    }

    public void setWaveRemark(String waveRemark) {
        this.waveRemark = waveRemark;
    }

    public Boolean getBackRegionPickFirst() {
        return backRegionPickFirst;
    }

    public void setBackRegionPickFirst(Boolean backRegionPickFirst) {
        this.backRegionPickFirst = backRegionPickFirst;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }


    public List<Long> getNotInWaveRuleGroupIds() {
        return notInWaveRuleGroupIds;
    }

    public void setNotInWaveRuleGroupIds(List<Long> notInWaveRuleGroupIds) {
        this.notInWaveRuleGroupIds = notInWaveRuleGroupIds;
    }

    public Boolean getIsFillInfo() {
        return isFillInfo;
    }

    public void setIsFillInfo(Boolean isFillInfo) {
        this.isFillInfo = isFillInfo;
    }


    public Boolean getMulti() {
        return multi;
    }

    public void setMulti(Boolean multi) {
        this.multi = multi;
    }

    public List<Long> getSids() {
        return sids;
    }

    public WaveFilterParams setSids(List<Long> sids) {
        this.sids = sids;
        return this;
    }

    public List<Long> getShortIdsList() {
        return shortIdsList;
    }

    public WaveFilterParams setShortIdsList(List<Long> shortIdsList) {
        this.shortIdsList = shortIdsList;
        return this;
    }

    public List<String> getOutSids() {
        return outSids;
    }

    public WaveFilterParams setOutSids(List<String> outSids) {
        this.outSids = outSids;
        return this;
    }

    public Long getPickingPickerId() {
        return pickingPickerId;
    }

    public void setPickingPickerId(Long pickingPickerId) {
        this.pickingPickerId = pickingPickerId;
    }

    public String getRemarkKey() {
        return remarkKey;
    }

    public void setRemarkKey(String remarkKey) {
        this.remarkKey = remarkKey;
    }

    public List<Long> getNotWaveIds() {
        return notWaveIds;
    }

    public void setNotWaveIds(List<Long> notWaveIds) {
        this.notWaveIds = notWaveIds;
    }

    public List<Long> getIgnoreWaveIds() {
        return ignoreWaveIds;
    }

    public void setIgnoreWaveIds(List<Long> ignoreWaveIds) {
        this.ignoreWaveIds = ignoreWaveIds;
    }

    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public DateRange getPayTimeRange() {
        return payTimeRange;
    }

    public WaveFilterParams setPayTimeRange(DateRange payTimeRange) {
        this.payTimeRange = payTimeRange;
        return this;
    }

    public Integer getIsExcep() {
        return isExcep;
    }

    public WaveFilterParams setIsExcep(Integer isExcep) {
        this.isExcep = isExcep;
        return this;
    }
    public String getShortIdStr() {
        return shortIdStr;
    }

    public void setShortIdStr(String shortIdStr) {
        this.shortIdStr = shortIdStr;
    }

    public List<WaveRule> getRules() {
        return rules;
    }

    public WaveFilterParams setRules(List<WaveRule> rules) {
        this.rules = rules;
        return this;
    }

    public Boolean getNeedSort() {
        return needSort;
    }

    public WaveFilterParams setNeedSort(Boolean needSort) {
        this.needSort = needSort;
        return this;
    }

    public Long getWaveRuleGroupId() {
        return waveRuleGroupId;
    }

    public WaveFilterParams setWaveRuleGroupId(Long waveRuleGroupId) {
        this.waveRuleGroupId = waveRuleGroupId;
        return this;
    }

    public WaveRule getWaveRuleQuery() {
        return waveRuleQuery;
    }

    public WaveFilterParams setWaveRuleQuery(WaveRule waveRuleQuery) {
        this.waveRuleQuery = waveRuleQuery;
        return this;
    }

    public Integer getSubSectionPick() {
        return subSectionPick;
    }

    public void setSubSectionPick(Integer subSectionPick) {
        this.subSectionPick = subSectionPick;
    }

    public Long getSubSectionPickerId() {
        return subSectionPickerId;
    }

    public void setSubSectionPickerId(Long subSectionPickerId) {
        this.subSectionPickerId = subSectionPickerId;
    }
    public Integer getCountUnPackNum() {
        return countUnPackNum;
    }

    public void setCountUnPackNum(Integer countUnPackNum) {
        this.countUnPackNum = countUnPackNum;
    }

    public Integer getFailedLast() {
        return failedLast;
    }

    public void setFailedLast(Integer failedLast) {
        this.failedLast = failedLast;
    }

    public DateRange getModifiedRange() {
        return modifiedRange;
    }

    public void setModifiedRange(DateRange modifiedRange) {
        this.modifiedRange = modifiedRange;
    }

    public Integer getIsConsignInAdvance() {
        return isConsignInAdvance;
    }

    public void setIsConsignInAdvance(Integer isConsignInAdvance) {
        this.isConsignInAdvance = isConsignInAdvance;
    }

    public String getSectionAreas() {
        return sectionAreas;
    }

    public void setSectionAreas(String sectionAreas) {
        this.sectionAreas = sectionAreas;
    }

    public boolean isAssembleItemBrand() {
        return assembleItemBrand;
    }

    public void setAssembleItemBrand(boolean assembleItemBrand) {
        this.assembleItemBrand = assembleItemBrand;
    }

    public boolean isAssembleItemSellerCid() {
        return assembleItemSellerCid;
    }

    public void setAssembleItemSellerCid(boolean assembleItemSellerCid) {
        this.assembleItemSellerCid = assembleItemSellerCid;
    }
    public Integer getIsPackage() {
        return isPackage;
    }

    public WaveFilterParams setIsPackage(Integer isPackage) {
        this.isPackage = isPackage;
        return this;
    }

    public boolean isAssembleItemCat() {
        return assembleItemCat;
    }
    public List<Long> getNotTagIds() {
        return notTagIds;
    }

    public void setAssembleItemCat(boolean assembleItemCat) {
        this.assembleItemCat = assembleItemCat;
    }

    public boolean isAssembleItemTag() {
        return assembleItemTag;
    }

    public void setAssembleItemTag(boolean assembleItemTag) {
        this.assembleItemTag = assembleItemTag;
    }

    public void setNotTagIds(List<Long> notTagIds) {
        this.notTagIds = notTagIds;
    }

    public Map<Long, List<Order>> getSid2OrdersMap() {
        return sid2OrdersMap;
    }

    public void setSid2OrdersMap(Map<Long, List<Order>> sid2OrdersMap) {
        this.sid2OrdersMap = sid2OrdersMap;
    }

    public boolean isNeedGroupThenAllocate() {
        return needGroupThenAllocate;
    }

    public void setNeedGroupThenAllocate(boolean needGroupThenAllocate) {
        this.needGroupThenAllocate = needGroupThenAllocate;
    }

    public boolean isNeedRemoveTradeCount() {
        return needRemoveTradeCount;
    }

    public void setNeedRemoveTradeCount(boolean needRemoveTradeCount) {
        this.needRemoveTradeCount = needRemoveTradeCount;
    }

    public String getExpressCompanyId() {
        return expressCompanyId;
    }

    public void setExpressCompanyId(String expressCompanyId) {
        this.expressCompanyId = expressCompanyId;
    }

    public String getPickStyles() {
        return pickStyles;
    }

    public void setPickStyles(String pickStyles) {
        this.pickStyles = pickStyles;
    }

    public List<Long> getTemplateIds() {
        return templateIds;
    }

    public WaveFilterParams setTemplateIds(List<Long> templateIds) {
        this.templateIds = templateIds;
        return this;
    }

    public String getLogisticsCompanyIds() {
        return logisticsCompanyIds;
    }

    public void setLogisticsCompanyIds(String logisticsCompanyIds) {
        this.logisticsCompanyIds = logisticsCompanyIds;
    }

    public List<Long> getLogisticsCompanyIdList() {
        return logisticsCompanyIdList;
    }

    public WaveFilterParams setLogisticsCompanyIdList(List<Long> logisticsCompanyIdList) {
        this.logisticsCompanyIdList = logisticsCompanyIdList;
        return this;
    }

    public Integer getPickListPrintStatus() {
        return pickListPrintStatus;
    }

    public void setPickListPrintStatus(Integer pickListPrintStatus) {
        this.pickListPrintStatus = pickListPrintStatus;
    }

    public Map<Long, Integer> getSidIndexMap() {
        return sidIndexMap;
    }

    public void setSidIndexMap(Map<Long, Integer> sidIndexMap) {
        this.sidIndexMap = sidIndexMap;
    }

    public Integer getIncludeScalping() {
        return includeScalping;
    }

    public WaveFilterParams setIncludeScalping(Integer includeScalping) {
        this.includeScalping = includeScalping;
        return this;
    }

    public boolean isOpenPrintStatusV2() {
        return openPrintStatusV2;
    }

    public void setOpenPrintStatusV2(boolean openPrintStatusV2) {
        this.openPrintStatusV2 = openPrintStatusV2;
    }

    public Date getPickStartTime() {
        return pickStartTime;
    }

    public void setPickStartTime(Date pickStartTime) {
        this.pickStartTime = pickStartTime;
    }

    public Date getPickEndTime() {
        return pickEndTime;
    }

    public void setPickEndTime(Date pickEndTime) {
        this.pickEndTime = pickEndTime;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getPickingCodeStr() {
        return pickingCodeStr;
    }

    public void setPickingCodeStr(String pickingCodeStr) {
        this.pickingCodeStr = pickingCodeStr;
    }

    public TradeQueryParams getTradeQueryParams() {
        return tradeQueryParams;
    }

    public void setTradeQueryParams(TradeQueryParams tradeQueryParams) {
        this.tradeQueryParams = tradeQueryParams;
    }

    public Set<Long> getSysItemIds() {
        return sysItemIds;
    }

    public void setSysItemIds(Set<Long> sysItemIds) {
        this.sysItemIds = sysItemIds;
    }

    public Set<Long> getSysSkuIds() {
        return sysSkuIds;
    }

    public void setSysSkuIds(Set<Long> sysSkuIds) {
        this.sysSkuIds = sysSkuIds;
    }

    public Set<ItemKey> getItemKeyList() {
        return itemKeyList;
    }

    public void setItemKeyList(Set<ItemKey> itemKeyList) {
        this.itemKeyList = itemKeyList;
    }

    public List<Long> getWaveIdsByItemSearch() {
        return waveIdsByItemSearch;
    }

    public void setWaveIdsByItemSearch(List<Long> waveIdsByItemSearch) {
        this.waveIdsByItemSearch = waveIdsByItemSearch;
    }

    public Integer getQueryMultiPickingCode() {
        return queryMultiPickingCode;
    }

    public void setQueryMultiPickingCode(Integer queryMultiPickingCode) {
        this.queryMultiPickingCode = queryMultiPickingCode;
    }

    public List<String> getShipperIds() {
        return shipperIds;
    }

    public void setShipperIds(List<String> shipperIds) {
        this.shipperIds = shipperIds;
    }

    public List<String> getOuterIds() {
        return outerIds;
    }

    public void setOuterIds(List<String> outerIds) {
        this.outerIds = outerIds;
    }

    private List<String>outerIds;

    public boolean isUseTradeNotConsignTable() {
        return useTradeNotConsignTable;
    }

    public WaveFilterParams setUseTradeNotConsignTable(boolean useTradeNotConsignTable) {
        this.useTradeNotConsignTable = useTradeNotConsignTable;
        return this;
    }

    public Boolean getHasFilter() {
        return hasFilter;
    }

    public void setHasFilter(Boolean hasFilter) {
        this.hasFilter = hasFilter;
    }

    public boolean isAccurateStatWaveTradeNum() {
        return accurateStatWaveTradeNum;
    }

    public WaveRuleType getWaveRuleType() {
        return waveRuleType;
    }

    public void setWaveRuleType(WaveRuleType waveRuleType) {
        this.waveRuleType = waveRuleType;
    }

    public List<Long> getAssignPickerRoleIds() {
        return assignPickerRoleIds;
    }

    public void setAssignPickerRoleIds(List<Long> assignPickerRoleIds) {
        this.assignPickerRoleIds = assignPickerRoleIds;
    }


    public boolean isBackendWaveNotInReasonQuery() {
        return backendWaveNotInReasonQuery;
    }

    public void setBackendWaveNotInReasonQuery(boolean backendWaveNotInReasonQuery) {
        this.backendWaveNotInReasonQuery = backendWaveNotInReasonQuery;
    }

    public WaveFilterParams setAccurateStatWaveTradeNum(boolean accurateStatWaveTradeNum) {
        this.accurateStatWaveTradeNum = accurateStatWaveTradeNum;
        return this;
    }

    public boolean isGroupByTrade() {
        return groupByTrade;
    }

    public WaveFilterParams setGroupByTrade(boolean groupByTrade) {
        this.groupByTrade = groupByTrade;
        return this;
    }

    public Boolean getOnlyShowAssignSelf() {
        return onlyShowAssignSelf;
    }

    public void setOnlyShowAssignSelf(Boolean onlyShowAssignSelf) {
        this.onlyShowAssignSelf = onlyShowAssignSelf;
    }

    public String getCrossBorderWaveUnderstockedGroup() {
        return crossBorderWaveUnderstockedGroup;
    }

    public void setCrossBorderWaveUnderstockedGroup(String crossBorderWaveUnderstockedGroup) {
        this.crossBorderWaveUnderstockedGroup = crossBorderWaveUnderstockedGroup;
    }


    public List<Integer> getOrRuleTypes() {
        return orRuleTypes;
    }

    public void setOrRuleTypes(List<Integer> orRuleTypes) {
        this.orRuleTypes = orRuleTypes;
    }

    public List<Integer> getNotRuleTypes() {
        return notRuleTypes;
    }

    public void setNotRuleTypes(List<Integer> notRuleTypes) {
        this.notRuleTypes = notRuleTypes;
    }

    public List<Integer> getOrPickingTypes() {
        return orPickingTypes;
    }

    public void setOrPickingTypes(List<Integer> orPickingTypes) {
        this.orPickingTypes = orPickingTypes;
    }

    public boolean isNoRuleAllowUnderStocked() {
        return noRuleAllowUnderStocked;
    }

    public void setNoRuleAllowUnderStocked(boolean noRuleAllowUnderStocked) {
        this.noRuleAllowUnderStocked = noRuleAllowUnderStocked;
    }

    public Set<Long> getUnderStockedRemoveSids() {
        return underStockedRemoveSids;
    }

    public void setUnderStockedRemoveSids(Set<Long> underStockedRemoveSids) {
        this.underStockedRemoveSids = underStockedRemoveSids;
    }

    public Integer getTagStatus() {
        return tagStatus;
    }

    public void setTagStatus(Integer tagStatus) {
        this.tagStatus = tagStatus;
    }

    public Boolean getItemChangeFlagQuery() {
        return itemChangeFlagQuery;
    }

    public void setItemChangeFlagQuery(Boolean itemChangeFlagQuery) {
        this.itemChangeFlagQuery = itemChangeFlagQuery;
    }

    public List<String> getTids() {
        return tids;
    }

    public void setTids(List<String> tids) {
        this.tids = tids;
    }
    public String getNotInWaveReason() {
        return notInWaveReason;
    }

    public WaveFilterParams setNotInWaveReason(String notInWaveReason) {
        this.notInWaveReason = notInWaveReason;
        return this;
    }


    public boolean isAssembleShipper() {
        return assembleShipper;
    }

    public void setAssembleShipper(boolean assembleShipper) {
        this.assembleShipper = assembleShipper;
    }

    public Boolean getAiPackmaFlag() {
        return aiPackmaFlag;
    }

    public void setAiPackmaFlag(Boolean aiPackmaFlag) {
        this.aiPackmaFlag = aiPackmaFlag;
    }
}

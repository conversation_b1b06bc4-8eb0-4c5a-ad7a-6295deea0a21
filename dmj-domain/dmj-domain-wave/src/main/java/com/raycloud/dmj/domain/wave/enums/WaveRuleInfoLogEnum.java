package com.raycloud.dmj.domain.wave.enums;

import com.google.common.base.Objects;
import com.raycloud.dmj.domain.Domain;
import com.google.common.collect.Sets;
import com.raycloud.dmj.AbstractSpecifyCondition;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.SpecifyConditionTypeEnum;
import com.raycloud.dmj.domain.enums.TradeDeliveryTypeEnum;
import com.raycloud.dmj.domain.enums.TradeShipTypeEnum;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.WaveRule;
import com.raycloud.dmj.domain.wave.WaveRuleCondition;
import com.raycloud.dmj.domain.wave.WaveRuleOpLog;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author: men
 * @Date: 2022-06-16 10:57
 * 波次规则信息
 * 记录波次规则修改日志
 */
public enum WaveRuleInfoLogEnum {


    L1("波次名称", (title, oldValue, newValue) -> buildLog(title, oldValue.getName(), newValue.getName())),

    L2("参与批量生成波次", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getOpenBatchCreate(), newValue.getOpenBatchCreate())),

    L3("波次数量设置", (title, oldValue, newValue) -> {
        String oldSlicingType = WaveSlicingEnum.getNameByValue(oldValue.getRuleCondition().getSlicingType());
        String newSlicingType = WaveSlicingEnum.getNameByValue(newValue.getRuleCondition().getSlicingType());
        if (BooleanUtils.isTrue(newValue.getRuleCondition().getSharePosition())) { // 使用共享位置号时，实际为设置单个位置订单数
            title = "单个位置订单数设置";
        }
        return buildLog(title, oldSlicingType, newSlicingType, oldValue.getNumDown(), newValue.getNumDown(), oldValue.getNumUp(), newValue.getNumUp());
    }),

    L4("商品总数", (title, oldValue, newValue) ->
            buildLog(title, null, null,
                    isNull(oldValue.getRuleCondition().getItemTotalNumRange()) ? null : WaveUtils.toInteger(oldValue.getRuleCondition().getItemTotalNumRange().getMin()),
                    isNull(newValue.getRuleCondition().getItemTotalNumRange()) ? null : WaveUtils.toInteger(newValue.getRuleCondition().getItemTotalNumRange().getMin()),
                    isNull(oldValue.getRuleCondition().getItemTotalNumRange()) ? null : WaveUtils.toInteger(oldValue.getRuleCondition().getItemTotalNumRange().getMax()),
                    isNull(newValue.getRuleCondition().getItemTotalNumRange()) ? null : WaveUtils.toInteger(newValue.getRuleCondition().getItemTotalNumRange().getMax()))),

    L5("自动波次订单数", (title, oldValue, newValue) ->
            buildLog(title,
                    openOrClose(oldValue.getOpenAutoCreate()), openOrClose(newValue.getOpenAutoCreate()))),

    L106("自动波次订单数", (title, oldValue, newValue) ->
            buildLog(title,
                   null, null,
                    isNull(oldValue.getRuleCondition().getAutoWaveNumRange()) ? null : WaveUtils.toInteger(oldValue.getRuleCondition().getAutoWaveNumRange().getMin()),
                    isNull(newValue.getRuleCondition().getAutoWaveNumRange()) ? null : WaveUtils.toInteger(newValue.getRuleCondition().getAutoWaveNumRange().getMin()),
                    isNull(oldValue.getRuleCondition().getAutoWaveNumRange()) ? null : WaveUtils.toInteger(oldValue.getRuleCondition().getAutoWaveNumRange().getMax()),
                    isNull(newValue.getRuleCondition().getAutoWaveNumRange()) ? null : WaveUtils.toInteger(newValue.getRuleCondition().getAutoWaveNumRange().getMax()))),

    L72("波次商品种类", (title, oldValue, newValue) ->
            buildLog(title, null, null,
                    isNull(oldValue.getRuleCondition().getItemKindNumRange()) ? null : WaveUtils.toInteger(oldValue.getRuleCondition().getItemKindNumRange().getMin()),
                    isNull(newValue.getRuleCondition().getItemKindNumRange()) ? null : WaveUtils.toInteger(newValue.getRuleCondition().getItemKindNumRange().getMin()),
                    isNull(oldValue.getRuleCondition().getItemKindNumRange()) ? null : WaveUtils.toInteger(oldValue.getRuleCondition().getItemKindNumRange().getMax()),
                    isNull(newValue.getRuleCondition().getItemKindNumRange()) ? null : WaveUtils.toInteger(newValue.getRuleCondition().getItemKindNumRange().getMax()))),

    L6("位置号数量", (title, oldValue, newValue) ->
            buildLog(title, null, null,
                    isNull(oldValue.getRuleCondition().getPositionCodeNumRange()) ? null : WaveUtils.toInteger(oldValue.getRuleCondition().getPositionCodeNumRange().getMin()),
                    isNull(newValue.getRuleCondition().getPositionCodeNumRange()) ? null : WaveUtils.toInteger(newValue.getRuleCondition().getPositionCodeNumRange().getMin()),
                    isNull(oldValue.getRuleCondition().getPositionCodeNumRange()) ? null : toIntNull(oldValue.getRuleCondition().getPositionCodeNumRange().getMax()),
                    isNull(newValue.getRuleCondition().getPositionCodeNumRange()) ? null : toIntNull(newValue.getRuleCondition().getPositionCodeNumRange().getMax()))),


    L7("位置号循环播种", (title, oldValue, newValue) -> {
        String oldData = oldValue.getOpenLoopWave() == null || oldValue.getOpenLoopWave() == CommonConstants.JUDGE_NO ? "关闭" : oldValue.getOpenLoopWave() == 1 ? "单个波次" : "多个波次";
        String newData = newValue.getOpenLoopWave() == null || newValue.getOpenLoopWave() == CommonConstants.JUDGE_NO ? "关闭" : newValue.getOpenLoopWave() == 1 ? "单个波次" : "多个波次";
        return buildLog(title, oldData, newData);
    }),

    L79("虚拟位置号", (title, oldValue, newValue) -> {
        if (newValue.getOpenLoopWave() == null || newValue.getOpenLoopWave() == CommonConstants.JUDGE_NO) {
            return "";
        }
        return buildLog(title, toInt(oldValue.getVirtualNum()), toInt(newValue.getVirtualNum()));
    }),

    L78("混合位置号", (title, oldValue, newValue) -> {
        if (newValue.getOpenLoopWave() == null || newValue.getOpenLoopWave() == CommonConstants.JUDGE_NO) {
            return "";
        }
        return buildLog(title, toInt(oldValue.getMixPickCode()), toInt(newValue.getMixPickCode()));
    }),

    L8("优先级", (title, oldValue, newValue) -> buildLog(title, oldValue.getSort(), newValue.getSort())),

    L9("波次分组", (title, oldValue, newValue) -> buildLog(title, oldValue.getWaveRuleChCondition().getWaveRuleGroupName(), newValue.getWaveRuleChCondition().getWaveRuleGroupName())),

    L10("位置号属性", (title, oldValue, newValue) -> buildLog(title, getPositionCodeGenerateType(oldValue), getPositionCodeGenerateType(newValue))),

    L11("相同店铺", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getShopEq(), newValue.getRuleCondition().getShopEq())),

    L12("指定店铺", (title, oldValue, newValue) -> buildLog(title, oldValue.getWaveRuleChCondition().getUserNames(), newValue.getWaveRuleChCondition().getUserNames())),

    L13("相同商品", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getItemEq(), newValue.getRuleCondition().getItemEq())),

    L14("相同主商家编码", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getItemSysIdEq(), newValue.getRuleCondition().getItemSysIdEq())),

    L15("商品-类型", (title, oldValue, newValue) -> {
        String oldData = oldValue.getRuleCondition().getHasSuit() == null ? "无" : oldValue.getRuleCondition().getHasSuit() ? "套件商品" : "普通商品";
        String newData = newValue.getRuleCondition().getHasSuit() == null ? "无" : newValue.getRuleCondition().getHasSuit() ? "套件商品" : "普通商品";
        return buildLog(title, oldData, newData);
    }),

    L16("套件商品计算规则", (title, oldValue, newValue) -> {
        String oldData = Objects.equal(oldValue.getRuleCondition().getSuitBySingle(), CommonConstants.JUDGE_YES) ? "套件明细" : "套件";
        String newData = Objects.equal(newValue.getRuleCondition().getSuitBySingle(), CommonConstants.JUDGE_YES) ? "套件明细" : "套件";
        return buildLog(title, oldData, newData);
    }),

    L17("指定商品", (title, oldValue, newValue) -> buildLog(title,
            getTypeName(WaveRuleInfoLogTypeNameEnum.L17, oldValue.getRuleCondition().getSpecifyItemCondition()),
            getTypeName(WaveRuleInfoLogTypeNameEnum.L17, newValue.getRuleCondition().getSpecifyItemCondition()))),

    L18("指定商品", (title, oldValue, newValue) -> buildAddLog(title,
            Optional.ofNullable(oldValue.getRuleCondition().getSpecifyItemCondition()).map(WaveRuleCondition.SpecifyItemCondition::getItems).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.SimpleDmjItem::getOuterId).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList()),
            Optional.ofNullable(newValue.getRuleCondition().getSpecifyItemCondition()).map(WaveRuleCondition.SpecifyItemCondition::getItems).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.SimpleDmjItem::getOuterId).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList())
    )),

    L19("商品-排序", (title, oldValue, newValue) -> buildLog(title,
            Optional.ofNullable(oldValue.getRuleCondition().getSortRuleType()).map(WaveRuleCondition.SortRuleType::getName).orElse("无"),
            Optional.ofNullable(newValue.getRuleCondition().getSortRuleType()).map(WaveRuleCondition.SortRuleType::getName).orElse("无"))),

    L20("指定供应商", (title, oldValue, newValue) -> buildLog(title,
            isNull(oldValue.getRuleCondition().getSpecifySupplierCondition()) ? null : oldValue.getWaveRuleChCondition().getSupperNames(),
            isNull(newValue.getRuleCondition().getSpecifySupplierCondition()) ? null : newValue.getWaveRuleChCondition().getSupperNames())),

    L21("商品品牌", (title, oldValue, newValue) -> {
        if(oldValue.getRuleCondition().getSpecifyItemBrandCondition() != null && oldValue.getRuleCondition().getSpecifyItemBrandCondition().getType() == null){
            WaveRuleCondition.SpecifyItemBrandCondition specifyItemBrandCondition = oldValue.getRuleCondition().getSpecifyItemBrandCondition();
            specifyItemBrandCondition.setType(specifyItemBrandCondition.isNoBrand() ? SpecifyConditionTypeEnum.NONE : SpecifyConditionTypeEnum.INCLUDE);
        }
        return  buildLog(title, getTypeName(oldValue.getRuleCondition().getSpecifyItemBrandCondition()), getTypeName(newValue.getRuleCondition().getSpecifyItemBrandCondition()));
    }),

    L22("商品品牌", (title, oldValue, newValue) -> {
        List<String> oldData = Optional.ofNullable(oldValue.getRuleCondition().getSpecifyItemBrandCondition()).map(WaveRuleCondition.SpecifyItemBrandCondition::getItemBrands).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.ItemBrand::getName).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList());
        List<String> newData = Optional.ofNullable(newValue.getRuleCondition().getSpecifyItemBrandCondition()).map(WaveRuleCondition.SpecifyItemBrandCondition::getItemBrands).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.ItemBrand::getName).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList());
        return buildLog(title, oldData, newData);
    }),

    L23("商品分类", (title, oldValue, newValue) -> buildLog(title, getTypeName(oldValue.getRuleCondition().getSpecifyItemCategoryCondition()), getTypeName(newValue.getRuleCondition().getSpecifyItemCategoryCondition()))),
    // 需要前端传入
    L24("商品分类", (title, oldValue, newValue) -> buildLog(title,
            Optional.ofNullable(oldValue.getRuleCondition().getSpecifyItemCategoryCondition()).map(WaveRuleCondition.SpecifyItemCategoryCondition::getItemCategories).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.ItemCategory::getName).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList()),
            Optional.ofNullable(newValue.getRuleCondition().getSpecifyItemCategoryCondition()).map(WaveRuleCondition.SpecifyItemCategoryCondition::getItemCategories).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.ItemCategory::getName).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList()))),

    L25("商品类目", (title, oldValue, newValue) -> buildLog(title, getTypeName(oldValue.getRuleCondition().getSpecifyItemCatCondition()), getTypeName(newValue.getRuleCondition().getSpecifyItemCatCondition()))),
    // 需要前端传入
    L26("商品类目", (title, oldValue, newValue) -> buildLog(title,
            Optional.ofNullable(oldValue.getRuleCondition().getSpecifyItemCatCondition()).map(WaveRuleCondition.SpecifyItemCatCondition::getItemCats).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.ItemCat::getCatName).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList()),
            Optional.ofNullable(newValue.getRuleCondition().getSpecifyItemCatCondition()).map(WaveRuleCondition.SpecifyItemCatCondition::getItemCats).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.ItemCat::getCatName).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList()))),

    L27("相同快递", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getExpressEq(), newValue.getExpressEq())),

    L28("指定快递", (title, oldValue, newValue) -> buildLog(title, getTypeName(oldValue.getRuleCondition().getSpecifyExpressTemplateCondition()), getTypeName(newValue.getRuleCondition().getSpecifyExpressTemplateCondition()))),

    L29("指定快递", (title, oldValue, newValue) -> buildLog(title, oldValue.getWaveRuleChCondition().getTemplateNames(), newValue.getWaveRuleChCondition().getTemplateNames())),

    L30("快递运单号", (title, oldValue, newValue) -> {
        String oldData = oldValue.getRuleCondition().getOutSid() == null || oldValue.getRuleCondition().getOutSid() == CommonConstants.JUDGE_NO ? "无" : oldValue.getRuleCondition().getOutSid() == CommonConstants.JUDGE_YES ? "已分配" : "未分配";
        String newData = newValue.getRuleCondition().getOutSid() == null || newValue.getRuleCondition().getOutSid() == CommonConstants.JUDGE_NO ? "无" : newValue.getRuleCondition().getOutSid() == CommonConstants.JUDGE_YES ? "已分配" : "未分配";
        return buildLog(title, oldData, newData);
    }),

    L31("发票", (title, oldValue, newValue) -> {
        String oldData = oldValue.getRuleCondition().getHasTaxNo() == null ? "无" : oldValue.getRuleCondition().getHasTaxNo() ? "需要" : "不需要";
        String newData = newValue.getRuleCondition().getHasTaxNo() == null ? "无" : newValue.getRuleCondition().getHasTaxNo() ? "需要" : "不需要";
        return buildLog(title, oldData, newData);
    }),

    L32("订单标记", (title, oldValue, newValue) -> {
        String oldData = WaveRuleCondition.TRADE_MARK_URGENT.equals(oldValue.getRuleCondition().getTradeMark()) ? "加急" : WaveRuleCondition.TRADE_MARK_UN_URGENT.equals(oldValue.getRuleCondition().getTradeMark()) ? "不加急" : "无";
        String newData = WaveRuleCondition.TRADE_MARK_URGENT.equals(newValue.getRuleCondition().getTradeMark()) ? "加急" : WaveRuleCondition.TRADE_MARK_UN_URGENT.equals(newValue.getRuleCondition().getTradeMark()) ? "不加急" : "无";
        return buildLog(title, oldData, newData);
    }),

    L33("订单类型", (title, oldValue, newValue) -> {
        List<String> oldData = Optional.ofNullable(oldValue.getRuleCondition().getSpecifyTradeTypeCondition()).map(WaveRuleCondition.SpecifyTradeTypeCondition::getTradeTypeEnums).orElse(Sets.newHashSet()).stream().map(TradeTypeEnum::getName).collect(Collectors.toList());
        List<String> newData = Optional.ofNullable(newValue.getRuleCondition().getSpecifyTradeTypeCondition()).map(WaveRuleCondition.SpecifyTradeTypeCondition::getTradeTypeEnums).orElse(Sets.newHashSet()).stream().map(TradeTypeEnum::getName).collect(Collectors.toList());
        return buildLog(title, oldData, newData);
    }),

    L34("包含订单标签", (title, oldValue, newValue) -> buildLog(title,
            getTypeName(oldValue.getRuleCondition().getSpecifyIncludeTradeTagCondition()),
            getTypeName(newValue.getRuleCondition().getSpecifyIncludeTradeTagCondition()))),

    L35("包含订单标签", (title, oldValue, newValue) -> {
        List<String> oldData = Optional.ofNullable(oldValue.getRuleCondition().getSpecifyIncludeTradeTagCondition()).map(WaveRuleCondition.SpecifyTradeTagCondition::getTags).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.SimpleTradeTag::getTagName).collect(Collectors.toList());
        List<String> newData = Optional.ofNullable(newValue.getRuleCondition().getSpecifyIncludeTradeTagCondition()).map(WaveRuleCondition.SpecifyTradeTagCondition::getTags).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.SimpleTradeTag::getTagName).collect(Collectors.toList());
        return buildLog(title, oldData, newData);
    }),

    L36("排除订单标签", (title, oldValue, newValue) -> buildLog(title,
            getTypeName(oldValue.getRuleCondition().getSpecifyExcludeTradeTagCondition()),
            getTypeName(newValue.getRuleCondition().getSpecifyExcludeTradeTagCondition()))),

    L37("排除订单标签", (title, oldValue, newValue) -> {
        List<String> oldData = Optional.ofNullable(oldValue.getRuleCondition().getSpecifyExcludeTradeTagCondition()).map(WaveRuleCondition.SpecifyTradeTagCondition::getTags).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.SimpleTradeTag::getTagName).collect(Collectors.toList());
        List<String> newData = Optional.ofNullable(newValue.getRuleCondition().getSpecifyExcludeTradeTagCondition()).map(WaveRuleCondition.SpecifyTradeTagCondition::getTags).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.SimpleTradeTag::getTagName).collect(Collectors.toList());
        return buildLog(title, oldData, newData);
    }),

    L39("分销商", (title, oldValue, newValue) -> buildLog(title, oldValue.getWaveRuleChCondition().getSourceNames(), newValue.getWaveRuleChCondition().getSourceNames())),

    L391("分销商店铺", (title, oldValue, newValue) -> buildLog(title, oldValue.getWaveRuleChCondition().getFxUserNames(), newValue.getWaveRuleChCondition().getFxUserNames())),

    L40("bic送检方式", (title, oldValue, newValue) -> {
        String oldData = TradeDeliveryTypeEnum.getNameByValue(oldValue.getRuleCondition().getBicDeliveryType());
        String newData = TradeDeliveryTypeEnum.getNameByValue(newValue.getRuleCondition().getBicDeliveryType());
        return buildLog(title, oldData, newData);
    }),

    L41("bic出仓方式", (title, oldValue, newValue) -> {
        String oldData = TradeShipTypeEnum.getNameByValue(oldValue.getRuleCondition().getBicShipType());
        String newData = TradeShipTypeEnum.getNameByValue(newValue.getRuleCondition().getBicShipType());
        return buildLog(title, oldData, newData);
    }),

    L42("bic标记有赠品", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getBicIsGift(), newValue.getRuleCondition().getBicIsGift())),

    L43("留言/备注", (title, oldValue, newValue) -> {
        String oldData = BooleanUtils.isNotTrue(oldValue.getRuleCondition().getMessageMemoandOr()) ? "任意满足" : "同时满足";
        String newData = BooleanUtils.isNotTrue(newValue.getRuleCondition().getMessageMemoandOr()) ? "任意满足" : "同时满足";
        return buildLog(title, oldData, newData);
    }),

    L44("买家留言类型", (title, oldValue, newValue) -> buildLog(title,
            getTypeName(WaveRuleInfoLogTypeNameEnum.L44, oldValue.getRuleCondition().getSpecifyBuyerMessageCondition()),
            getTypeName(WaveRuleInfoLogTypeNameEnum.L44, newValue.getRuleCondition().getSpecifyBuyerMessageCondition()))),

    L45("买家留言", (title, oldValue, newValue) -> buildLog(title,
            isNull(oldValue.getRuleCondition().getSpecifyBuyerMessageCondition()) ? null : oldValue.getRuleCondition().getSpecifyBuyerMessageCondition().getMessages(),
            isNull(newValue.getRuleCondition().getSpecifyBuyerMessageCondition()) ? null : newValue.getRuleCondition().getSpecifyBuyerMessageCondition().getMessages())),

    L46("卖家备注类型", (title, oldValue, newValue) -> buildLog(title,
            getTypeName(WaveRuleInfoLogTypeNameEnum.L46, oldValue.getRuleCondition().getSpecifySellerMemoCondition()),
            getTypeName(WaveRuleInfoLogTypeNameEnum.L46, newValue.getRuleCondition().getSpecifySellerMemoCondition()))),

    L47("卖家备注", (title, oldValue, newValue) -> buildLog(title,
            isNull(oldValue.getRuleCondition().getSpecifySellerMemoCondition()) ? null : oldValue.getRuleCondition().getSpecifySellerMemoCondition().getMemos(),
            isNull(newValue.getRuleCondition().getSpecifySellerMemoCondition()) ? null : newValue.getRuleCondition().getSpecifySellerMemoCondition().getMemos())),

    L107("系统备注类型", (title, oldValue, newValue) -> buildLog(title,
            getTypeName(WaveRuleInfoLogTypeNameEnum.L46, oldValue.getRuleCondition().getSpecifySysMemoCondition()),
            getTypeName(WaveRuleInfoLogTypeNameEnum.L46, newValue.getRuleCondition().getSpecifySysMemoCondition()))),

    L108("系统备注", (title, oldValue, newValue) -> buildLog(title,
            isNull(oldValue.getRuleCondition().getSpecifySysMemoCondition()) ? null : oldValue.getRuleCondition().getSpecifySysMemoCondition().getMemos(),
            isNull(newValue.getRuleCondition().getSpecifySysMemoCondition()) ? null : newValue.getRuleCondition().getSpecifySysMemoCondition().getMemos())),

    L48("相同常态合作编码", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getCooperationNoEq(), newValue.getRuleCondition().getCooperationNoEq())),

    L49("相同送货仓库", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getSellSiteEq(), newValue.getRuleCondition().getSellSiteEq())),

    L50("商品数量", (title, oldValue, newValue) -> buildLog(title, null, null,
            oldValue.getRuleCondition().getItemNumDown(),
            newValue.getRuleCondition().getItemNumDown(),
            oldValue.getRuleCondition().getItemNumUp(),
            newValue.getRuleCondition().getItemNumUp())),

    L51("包裹重量", (title, oldValue, newValue) -> buildLog(title, null, null,
            oldValue.getRuleCondition().getWeightDown(),
            newValue.getRuleCondition().getWeightDown(),
            oldValue.getRuleCondition().getWeightUp(),
            newValue.getRuleCondition().getWeightUp())),

    L52("包装体积", (title, oldValue, newValue) -> buildLog(title, null, null,
            oldValue.getRuleCondition().getVolumeDown(),
            newValue.getRuleCondition().getVolumeDown(),
            oldValue.getRuleCondition().getVolumeUp(),
            newValue.getRuleCondition().getVolumeUp())),

    L53("订单商品种类", (title, oldValue, newValue) -> buildLog(title, null, null,
            oldValue.getRuleCondition().getItemKindsDown(),
            newValue.getRuleCondition().getItemKindsDown(),
            oldValue.getRuleCondition().getItemKindsUp(),
            newValue.getRuleCondition().getItemKindsUp())),

    L54("订单金额", (title, oldValue, newValue) -> buildLog(title, null, null,
            oldValue.getRuleCondition().getPaymentDown(),
            newValue.getRuleCondition().getPaymentDown(),
            oldValue.getRuleCondition().getPaymentUp(),
            newValue.getRuleCondition().getPaymentUp())),

    L55("订单毛利润", (title, oldValue, newValue) -> buildLog(title, null, null,
            isNull(oldValue.getRuleCondition().getGrossProfitRange()) ? null : WaveUtils.toDouble(oldValue.getRuleCondition().getGrossProfitRange().getMin()),
            isNull(newValue.getRuleCondition().getGrossProfitRange()) ? null : WaveUtils.toDouble(newValue.getRuleCondition().getGrossProfitRange().getMin()),
            isNull(oldValue.getRuleCondition().getGrossProfitRange()) ? null : WaveUtils.toDouble(oldValue.getRuleCondition().getGrossProfitRange().getMax()),
            isNull(newValue.getRuleCondition().getGrossProfitRange()) ? null : WaveUtils.toDouble(newValue.getRuleCondition().getGrossProfitRange().getMax()))),

    L56("相同货位", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().isSameGoodsSectionGroup(), newValue.getRuleCondition().isSameGoodsSectionGroup())),

    L57("库区", (title, oldValue, newValue) -> {

        String oldData = Objects.equal(oldValue.getRuleCondition().getSectionAreaType(), WaveRuleCondition.SECTION_AREA_TYPE_SAME) ? "同库区"
                : (Objects.equal(oldValue.getRuleCondition().getSectionAreaType(), WaveRuleCondition.SECTION_AREA_TYPE_MULTI) ? "跨区库"
                : (Objects.equal(oldValue.getRuleCondition().getSectionAreaType(), WaveRuleCondition.SECTION_AREA_TYPE_COMBINE) ? "指定库区组合" : "无"));
        String newData = Objects.equal(newValue.getRuleCondition().getSectionAreaType(), WaveRuleCondition.SECTION_AREA_TYPE_SAME) ? "同库区"
                : (Objects.equal(newValue.getRuleCondition().getSectionAreaType(), WaveRuleCondition.SECTION_AREA_TYPE_MULTI) ? "跨区库"
                : (Objects.equal(newValue.getRuleCondition().getSectionAreaType(), WaveRuleCondition.SECTION_AREA_TYPE_COMBINE) ? "指定库区组合" : "无"));
        return buildLog(title, oldData, newData);
    }),

    L58("指定库区组合", (title, oldValue, newValue) -> buildLog(title, oldValue.getRuleCondition().getSectionAreaCombineCodes(), newValue.getRuleCondition().getSectionAreaCombineCodes())),

    L59("指定货位", (title, oldValue, newValue) -> buildLog(title,
            getTypeName(oldValue.getRuleCondition().getSpecifyStockRegionCondition()),
            getTypeName(newValue.getRuleCondition().getSpecifyStockRegionCondition()))),

    L60("指定货位", (title, oldValue, newValue) -> buildLog(title, isNull(oldValue.getRuleCondition().getSpecifyStockRegionCondition()) ?
                    null : isNull(oldValue.getRuleCondition().getSpecifyStockRegionCondition().getRegions()) ?
                    null : oldValue.getRuleCondition().getSpecifyStockRegionCondition().getRegions().stream().map(WaveRuleCondition.SimpleStockRegion::getCode).collect(Collectors.toList()),
            isNull(newValue.getRuleCondition().getSpecifyStockRegionCondition()) ?
                    null : isNull(newValue.getRuleCondition().getSpecifyStockRegionCondition().getRegions()) ?
                    null : newValue.getRuleCondition().getSpecifyStockRegionCondition().getRegions().stream().map(WaveRuleCondition.SimpleStockRegion::getCode).collect(Collectors.toList()))),


    L61("货位可配库存不足时，仍允许生成波次", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().isAllowUnderstocked(), newValue.getRuleCondition().isAllowUnderstocked())),

    L611("货位可配库存不足时，仍允许生成波次", (title, oldValue, newValue) -> {
        String oldData = WaveRuleCondition.AllowUnderstockedRangeEnum.getNameByValue(oldValue.getRuleCondition().getAllowUnderstockedRange());
        String newData = WaveRuleCondition.AllowUnderstockedRangeEnum.getNameByValue(newValue.getRuleCondition().getAllowUnderstockedRange());
        return buildLog(title, oldData, newData);
    }),

    L612("含库存不足订单整个波次不配货", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().isInsufficientAllWaveNoAllocateRecord(), newValue.getRuleCondition().isInsufficientAllWaveNoAllocateRecord())),

    L62("箱库存参与拣货", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getBoxAllocate(), newValue.getRuleCondition().getBoxAllocate())),

    L63("按照货位编码排序生成波次", (title, oldValue, newValue) -> {
        String oldData = WaveRuleCondition.SORT_TYPE_SECTION.equals(oldValue.getRuleCondition().getSortType()) ? "是" : "否";
        String newData = WaveRuleCondition.SORT_TYPE_SECTION.equals(newValue.getRuleCondition().getSortType()) ? "是" : "否";
        return buildLog(title, oldData, newData);
    }),

    L64("波次拣选完成自动发货", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getPickEndAutoConsign(), newValue.getRuleCondition().getPickEndAutoConsign())),

    L65("指定拣选员", (title, oldValue, newValue) -> buildLog(title, oldValue.getWaveRuleChCondition().getPickerName(), newValue.getWaveRuleChCondition().getPickerName())),

    L66("包含商品标签", (title, oldValue, newValue) -> buildLog(title,
            getTypeName(WaveRuleInfoLogTypeNameEnum.L66, oldValue.getRuleCondition().getSpecifyItemTagCondition()),
            getTypeName(WaveRuleInfoLogTypeNameEnum.L66, newValue.getRuleCondition().getSpecifyItemTagCondition()))),
    //    需要前端传入
    L67("包含商品标签", (title, oldValue, newValue) -> buildLog(title,
            isNull(oldValue.getRuleCondition().getSpecifyItemTagCondition()) ? null : oldValue.getRuleCondition().getSpecifyItemTagCondition().getItemTagNames(),
            isNull(newValue.getRuleCondition().getSpecifyItemTagCondition()) ? null : newValue.getRuleCondition().getSpecifyItemTagCondition().getItemTagNames())),
    //    需要前端传入
    L68("排除商品标签", (title, oldValue, newValue) -> buildLog(title,
            isNull(oldValue.getRuleCondition().getSpecifyItemTagCondition()) ? null : oldValue.getRuleCondition().getSpecifyItemTagCondition().getExcludeItemTagNames(),
            isNull(newValue.getRuleCondition().getSpecifyItemTagCondition()) ? null : newValue.getRuleCondition().getSpecifyItemTagCondition().getExcludeItemTagNames())),

    L69("剩余订单合并波次数", (title, oldValue, newValue) -> buildLog(title, oldValue.getTradeMergeNum(), newValue.getTradeMergeNum())),

    L691("单品单件波次", (title, oldValue, newValue) -> buildLog(title, oldValue.getSameMergeNum(), newValue.getSameMergeNum())),

    L70("优先拣选", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getPriorityPick(), newValue.getRuleCondition().getPriorityPick())),

    L71("排除货位内容", (title, oldValue, newValue) -> buildLog(title,
            isNull(oldValue.getRuleCondition().getSpecifyStockRegionCondition())?
                    null:isNull(oldValue.getRuleCondition().getSpecifyStockRegionCondition().getSpareRegions())?
                    null:oldValue.getRuleCondition().getSpecifyStockRegionCondition().getSpareRegions().stream().map(WaveRuleCondition.SimpleStockRegion::getCode).collect(Collectors.toList()),
            isNull(newValue.getRuleCondition().getSpecifyStockRegionCondition())?
                    null:isNull(newValue.getRuleCondition().getSpecifyStockRegionCondition().getSpareRegions())?
                    null:newValue.getRuleCondition().getSpecifyStockRegionCondition().getSpareRegions().stream().map(WaveRuleCondition.SimpleStockRegion::getCode).collect(Collectors.toList()))),

    L77("主商家编码数量", (title, oldValue, newValue) -> buildLog(title,
            toString(oldValue.getRuleCondition().getMainItemKindsDown(),oldValue.getRuleCondition().getMainItemKindsUp()),
            toString(newValue.getRuleCondition().getMainItemKindsDown(),newValue.getRuleCondition().getMainItemKindsUp()),
            oldValue.getRuleCondition().getMainItemKindsDown(),
            newValue.getRuleCondition().getMainItemKindsDown(),
            oldValue.getRuleCondition().getMainItemKindsUp(),
            newValue.getRuleCondition().getMainItemKindsUp())),
    L73("快递分组-相同快递公司名称", (title, oldValue, newValue) -> buildLog(title, oldValue.getRuleCondition().getLogisticsCompanyEq(), newValue.getRuleCondition().getLogisticsCompanyEq())),
    L74("指定快递筛选-指定快递公司名称", (title, oldValue, newValue) -> buildLog(title,
            isNull(oldValue.getRuleCondition().getSpecifyLogisticsCompanyCondition())?null:oldValue.getRuleCondition().getSpecifyLogisticsCompanyCondition().getLogisticsCompanyIds(),
            isNull(newValue.getRuleCondition().getSpecifyLogisticsCompanyCondition())?null:newValue.getRuleCondition().getSpecifyLogisticsCompanyCondition().getLogisticsCompanyIds())),
    L75("指定快递筛选-指定快递公司", (title, oldValue, newValue) -> buildLog(title,isNull(oldValue.getRuleCondition().getSpecifyExpressCondition())?
                    null:isNull(oldValue.getRuleCondition().getSpecifyExpressCondition().getExpresses())?
                    null:oldValue.getRuleCondition().getSpecifyExpressCondition().getExpresses().stream().map(WaveRuleCondition.Express::getExpressName).collect(Collectors.toList()),
            isNull(newValue.getRuleCondition().getSpecifyExpressCondition())?
                    null:isNull(newValue.getRuleCondition().getSpecifyExpressCondition().getExpresses())?
                    null:newValue.getRuleCondition().getSpecifyExpressCondition().getExpresses().stream().map(WaveRuleCondition.Express::getExpressName).collect(Collectors.toList()))),
    L76("快递分组-相同快递公司", (title, oldValue, newValue) -> buildLog(title, oldValue.getRuleCondition().getExpressCompanyEq(), newValue.getRuleCondition().getExpressCompanyEq())),

    L80("波次箱数", (title, oldValue, newValue) -> buildLog(title,
            oldValue.getRuleCondition().getBigPick(), newValue.getRuleCondition().getBigPick(),
            oldValue.getRuleCondition().getBoxNumUp(), newValue.getRuleCondition().getBoxNumUp(),
            oldValue.getRuleCondition().getBoxNumDown(), newValue.getRuleCondition().getBoxNumDown())),

    L85("指定自动打印账号", (title, oldValue, newValue) -> buildLog(title, oldValue.getWaveRuleChCondition().getAutoPrinterName(), newValue.getWaveRuleChCondition().getAutoPrinterName())),

    L89("相同分类", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getItemCategoryEq(), newValue.getRuleCondition().getItemCategoryEq())),

    L101("纸质拣选", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getPaperPick(), newValue.getRuleCondition().getPaperPick())),

    L102("生产日期", (title, oldValue, newValue) -> {
        String oldData = Objects.equal(oldValue.getRuleCondition().getProductTimeRule(), WaveRuleCondition.RULE_MUST) ? "同订单必须同生产日期" : Objects.equal(oldValue.getRuleCondition().getProductTimeRule(), WaveRuleCondition.RULE_PRIORITY) ? "同订单优先同生产日期" : "按照出库规则";
        String newData = Objects.equal(newValue.getRuleCondition().getProductTimeRule(), WaveRuleCondition.RULE_MUST) ? "同订单必须同生产日期" : Objects.equal(newValue.getRuleCondition().getProductTimeRule(), WaveRuleCondition.RULE_PRIORITY) ? "同订单优先同生产日期" : "按照出库规则";
        return buildLog(title, oldData, newData);
    }),

    L103("批次规则", (title, oldValue, newValue) -> {
        String oldData = Objects.equal(oldValue.getRuleCondition().getBatchNoRule(), WaveRuleCondition.RULE_MUST) ? "同订单必须同批次" : Objects.equal(oldValue.getRuleCondition().getBatchNoRule(), WaveRuleCondition.RULE_PRIORITY) ? "同订单优先同批次" : "默认规则";
        String newData = Objects.equal(newValue.getRuleCondition().getBatchNoRule(), WaveRuleCondition.RULE_MUST) ? "同订单必须同批次" : Objects.equal(newValue.getRuleCondition().getBatchNoRule(), WaveRuleCondition.RULE_PRIORITY) ? "同订单优先同批次" : "默认规则";
        return buildLog(title, oldData, newData);

    }),

    L104("相同PO单", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getSamePoNo(), newValue.getRuleCondition().getSamePoNo())),

    L105("指定地区", (title, oldValue, newValue) -> buildLog(title, translateTradeArea(oldValue.getRuleCondition().getReceiverArea()), translateTradeArea(newValue.getRuleCondition().getReceiverArea()))),

    L891("规格分类类型", (title, oldValue, newValue) -> buildLog(title,
            isNull(oldValue.getRuleCondition().getSpecifySkuCategoryCondition())?null:oldValue.getRuleCondition().getSpecifySkuCategoryCondition().getType(),
            isNull(newValue.getRuleCondition().getSpecifySkuCategoryCondition())?null:newValue.getRuleCondition().getSpecifySkuCategoryCondition().getType())),
    L892("规格分类", (title, oldValue, newValue) -> buildLog(title,
            isNull(oldValue.getRuleCondition().getSpecifySkuCategoryCondition())?null:oldValue.getRuleCondition().getSpecifySkuCategoryCondition().getItemCategories(),
            isNull(newValue.getRuleCondition().getSpecifySkuCategoryCondition())?null:newValue.getRuleCondition().getSpecifySkuCategoryCondition().getItemCategories())),
    L893("相同规格分类", (title, oldValue, newValue) -> buildLog(title, oldValue.getRuleCondition().getSkuCategoryEq(), newValue.getRuleCondition().getSkuCategoryEq())),

    L109("波次订单数量设置", (title, oldValue, newValue) ->  buildLog(title, null, null,
            isNull(oldValue.getRuleCondition().getWaveTradeNumRange()) ? null : WaveUtils.toInteger(oldValue.getRuleCondition().getWaveTradeNumRange().getMin()),
            isNull(newValue.getRuleCondition().getWaveTradeNumRange()) ? null : WaveUtils.toInteger(newValue.getRuleCondition().getWaveTradeNumRange().getMin()),
            isNull(oldValue.getRuleCondition().getWaveTradeNumRange()) ? null : WaveUtils.toInteger(oldValue.getRuleCondition().getWaveTradeNumRange().getMax()),
            isNull(newValue.getRuleCondition().getWaveTradeNumRange()) ? null : WaveUtils.toInteger(newValue.getRuleCondition().getWaveTradeNumRange().getMax()))),

    L110("仅缺货订单生成波次", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getFilterNotUnderstocked(), newValue.getRuleCondition().getFilterNotUnderstocked())),


    L90("指定货主", (title, oldValue, newValue) -> buildLog(title,
            Optional.ofNullable(oldValue.getRuleCondition().getSpecifyShipperCondition()).map(WaveRuleCondition.SpecifyShipperCondition::getSimpleShippers).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.SimpleShipper::getShipperName).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList()),
            Optional.ofNullable(newValue.getRuleCondition().getSpecifyShipperCondition()).map(WaveRuleCondition.SpecifyShipperCondition::getSimpleShippers).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.SimpleShipper::getShipperName).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList())
    )),

    L91("相同货主", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getShipperEq(), newValue.getRuleCondition().getShipperEq())),
    L111("指定平台", (title, oldValue, newValue) -> buildLog(title, oldValue.getWaveRuleChCondition().getSources(), newValue.getWaveRuleChCondition().getSources())),


    L120("相同单供应商", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getSameSingleSupplier(), newValue.getRuleCondition().getSameSingleSupplier())),

    L121("排除商品", (title, oldValue, newValue) -> buildLog(title,
            getTypeName(WaveRuleInfoLogTypeNameEnum.L17, oldValue.getRuleCondition().getSpecifyExcludeItemCondition()),
            getTypeName(WaveRuleInfoLogTypeNameEnum.L17, newValue.getRuleCondition().getSpecifyExcludeItemCondition()))),

    L122("排除商品", (title, oldValue, newValue) -> buildAddLog(title,
            Optional.ofNullable(oldValue.getRuleCondition().getSpecifyExcludeItemCondition()).map(WaveRuleCondition.SpecifyItemCondition::getItems).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.SimpleDmjItem::getOuterId).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList()),
            Optional.ofNullable(newValue.getRuleCondition().getSpecifyExcludeItemCondition()).map(WaveRuleCondition.SpecifyItemCondition::getItems).orElse(Sets.newHashSet()).stream().map(WaveRuleCondition.SimpleDmjItem::getOuterId).filter(StringUtils::isNotEmpty).sorted().collect(Collectors.toList())
    )),
    L123("套件商品播种", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getSeedItemPositionSuit(), newValue.getRuleCondition().getSeedItemPositionSuit())),

    L113("相同单通道", (title, oldValue, newValue) -> openOrCloseLog(title, oldValue.getRuleCondition().getPathWayEq(), newValue.getRuleCondition().getPathWayEq())),

    L124("共享位置号-单个位置属性", (title, oldValue, newValue) -> buildLog(title,getSharePositionSingleType(oldValue),getSharePositionSingleType(newValue))),

    L125("共享位置号-单个位置体积", (title, oldValue, newValue) -> buildLog(title, null, null,
            oldValue.getRuleCondition().getPositionVolumeDown(),
            newValue.getRuleCondition().getPositionVolumeDown(),
            oldValue.getRuleCondition().getPositionVolumeUp(),
            newValue.getRuleCondition().getPositionVolumeUp())),
    ;

    /**
     * 波次规则属性名
     */
    @Getter
    @Setter
    private String title;

    /**
     * 构建日志
     */
    @Getter
    @Setter
    private Function3<String, WaveRule, WaveRule, String> getLog;


    WaveRuleInfoLogEnum(String title, Function3<String, WaveRule, WaveRule, String> function) {
        this.getLog = function;
        this.title = title;
    }


    public static void buildRuleLog(Staff staff, WaveRule oldValue, WaveRule newValue, String ip, ArrayList<OpLog> opLogs) {
        if (oldValue.getRuleCondition() == null) {
            oldValue.setRuleCondition(new WaveRuleCondition());
        }
        if (newValue.getRuleCondition() == null) {
            newValue.setRuleCondition(new WaveRuleCondition());
        }
        for (WaveRuleInfoLogEnum infoEnum : WaveRuleInfoLogEnum.values()) {
            if (infoEnum == WaveRuleInfoLogEnum.L3 && newValue.getRuleCondition().getSlicingType() != null && newValue.getRuleCondition().getSlicingType() != 0) {
                continue;
            }
            if (infoEnum == WaveRuleInfoLogEnum.L109 && BooleanUtils.isNotTrue(newValue.getRuleCondition().getSharePosition())) {
                continue;
            }
            // SlicingType 为0，或者为1，都可以填写 商品总数量
            if(infoEnum == WaveRuleInfoLogEnum.L4
                    && (newValue.getRuleCondition().getSlicingType() != null && newValue.getRuleCondition().getSlicingType() != 1)
                    && (newValue.getRuleCondition().getSlicingType() != null && newValue.getRuleCondition().getSlicingType() != 0)){
                continue;
            }
            if (infoEnum.getLog == null) {
                continue;
            }
            String log = infoEnum.getLog.apply(infoEnum.title, oldValue, newValue);
            if (StringUtils.isNotEmpty(log)) {
                opLogs.add(WaveRuleInfoLogEnum.buildOpLog(ip, staff, String.format("更新波次规则：%s", log), String.valueOf(newValue.getId())));
            }
        }
    }

    public static void buildWaveRuleOpLog(Staff staff, WaveRule oldValue, WaveRule newValue, String ip, ArrayList<WaveRuleOpLog> waveRuleOpLogs) {
        if (oldValue.getRuleCondition() == null) {
            oldValue.setRuleCondition(new WaveRuleCondition());
        }
        if (newValue.getRuleCondition() == null) {
            newValue.setRuleCondition(new WaveRuleCondition());
        }
        for (WaveRuleInfoLogEnum infoEnum : WaveRuleInfoLogEnum.values()) {
            if (infoEnum == WaveRuleInfoLogEnum.L3 && newValue.getRuleCondition().getSlicingType() != null && newValue.getRuleCondition().getSlicingType() != 0) {
                continue;
            }
            if (infoEnum == WaveRuleInfoLogEnum.L109 && BooleanUtils.isNotTrue(newValue.getRuleCondition().getSharePosition())) {
                continue;
            }
            // SlicingType 为0，或者为1，都可以填写 商品总数量
            if(infoEnum == WaveRuleInfoLogEnum.L4
                    && (newValue.getRuleCondition().getSlicingType() != null && newValue.getRuleCondition().getSlicingType() != 1)
                    && (newValue.getRuleCondition().getSlicingType() != null && newValue.getRuleCondition().getSlicingType() != 0)){
                continue;
            }
            if (infoEnum.getLog == null) {
                continue;
            }
            String log = infoEnum.getLog.apply(infoEnum.title, oldValue, newValue);
            if (StringUtils.isNotEmpty(log)) {
                waveRuleOpLogs.add(WaveRuleInfoLogEnum.buildWaveRuleOpLog(ip, staff, String.format("更新波次规则：%s", log), newValue.getId(),newValue.getName()));
            }
        }
    }

    private static <T> String buildLog(String title, T oldValue, T newValue) {
        return buildLog(title, oldValue, newValue, null, null, null, null);
    }

    private static <T> String buildLog(String title, List<String> oldValue, List<String> newValue) {
        return buildLog(title, toList(oldValue), toList(newValue));
    }

    /**
     * 记录增量修改日志
     */
    private static <T> String buildAddLog(String title, List<String> oldValue, List<String> newValue) {
        StringBuilder sb = new StringBuilder();
        List<String> added = newValue.stream().filter(item -> !oldValue.contains(item)).collect(Collectors.toList());
        List<String> removed = oldValue.stream().filter(item -> !newValue.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(added) || CollectionUtils.isNotEmpty(removed)) {
            sb.append("【").append(title).append("】").append("：");
        }
        if (CollectionUtils.isNotEmpty(added)) {
            sb.append("新增 ").append(toList(added)).append("；");
        }
        if (CollectionUtils.isNotEmpty(removed)) {
            sb.append("删除 ").append(toList(removed)).append("；");
        }
        return sb.toString();
    }


    private static <T> String buildLog(String title, T oldValue, T newValue, T oldKey1, T newKey1, T oldKey2, T newKey2) {
        boolean section = Objects.equal(toString(oldKey1, oldKey2), toString(newKey1, newKey2));
        StringBuilder sb = new StringBuilder();
        if (!Objects.equal(newValue, oldValue)) {
            sb.append("【").append(title).append("】").append("：").append(isNullString(oldValue)).append(" -> ").append(isNullString(newValue)).append("；");
        }
        if (!section) {
            sb.append("【").append(title).append("】");
            if (!Objects.equal(oldKey1, newKey1) || !Objects.equal(oldKey2, newKey2)) {
                sb.append(" 【").append(isNullString(oldKey1)).append(" - ").append(isNullString(oldKey2)).append("】：").append(" -> ")
                        .append(" 【").append(isNullString(newKey1)).append(" - ").append(isNullString(newKey2)).append("】");
            }
            sb.append("；");
        }
        return sb.toString();
    }

    public static OpLog buildOpLog(String ip, Staff staff, String content, String key) {
        //记录操作日志
        OpLog log = new OpLog();
        log.setCompanyId(staff.getCompanyId());
        log.setKey(key);
        log.setDomain(Domain.WAVE);
        log.setAction("waveRulesUpdate");
        log.setContent(content);
        log.setIp(ip);
        return log;
    }

    //构建波次规则操作日志
    public static WaveRuleOpLog buildWaveRuleOpLog(String ip, Staff staff, String content, Long ruleId,String ruleName) {
        //记录操作日志
        WaveRuleOpLog waveRuleOpLog = new WaveRuleOpLog();
        waveRuleOpLog.setCompanyId(staff.getCompanyId());
        waveRuleOpLog.setContent(content);
        waveRuleOpLog.setRuleId(ruleId);
        waveRuleOpLog.setRuleName(ruleName);
        waveRuleOpLog.setIp(ip);
        return waveRuleOpLog;
    }

    static <T> String openOrClose(T key) {
        if (key == null) {
            return "关闭";
        }
        if (key instanceof Integer) {
            Integer keyI = (Integer) key;
            return keyI > 0 ? "开启" : "关闭";
        } else if (key instanceof Boolean) {
            return (Boolean) key ? "开启" : "关闭";
        }
        return key.toString();
    }

    static <T> String openOrCloseLog(String title, T oldValue, T newValue) {
        StringBuilder sb = new StringBuilder();
        String oldValueStr = openOrClose(oldValue);
        String newValueStr = openOrClose(newValue);
        if (!Objects.equal(oldValueStr, newValueStr)) {
            sb.append("【").append(title).append("】").append("：").append(oldValueStr).append(" -> ").append(openOrClose(newValueStr)).append("；");
        }
        return sb.toString();
    }


    static String toString(Object v1, Object v2) {
        return isNullString(v1) + " - " + isNullString(v2);
    }

    static String toValue(Object v1, Object v2) {
        return v1 + " , " + v2;
    }

    static String toValue(Object v1, Object v2, Object v3) {
        return v1 + " , " + v2 + " , " + v3;
    }

    static boolean isNull(Object o) {
        return o == null;
    }

    static String isNullString(Object o) {
        return isNull(o) ? "无" : StringUtils.isNotEmpty(o.toString()) ? o.toString() : "无";
    }

    static Integer toInt(Integer v) {
        return v == null ? 0 : v;
    }

    static Integer toIntNull(Integer v) {
        return v != null && v != 0 ? v : null;
    }

    static String toList(List<String> data) {
        if (CollectionUtils.isEmpty(data)) {
            return "无";
        }
        return String.join(",", data);
    }

    private static String getPositionCodeGenerateType(WaveRule waveRule) {
        if (WaveRuleCondition.PositionCodeGenerateTypeEnum.ITEM_POSITION_CODE.equals(waveRule.getRuleCondition().getPositionCodeGenerateType())) {
            return "商品位置号";
        }
        if (Objects.equal(waveRule.getRuleCondition().getSharePosition(), Boolean.TRUE)) {
            return "共享位置号";
        }
        return "订单位置号";
    }

    private static String getSharePositionSingleType(WaveRule waveRule) {
        if (Objects.equal(waveRule.getRuleCondition().getSharePositionSingle(), Boolean.TRUE)) {
            return "单个位置体积";
        }
        if (Objects.equal(waveRule.getRuleCondition().getSharePositionSingle(), Boolean.FALSE)) {
            return "单个位置订单数";
        }
        return "";
    }

    private static String translateTradeArea(String area) {
        StringBuilder strBuf = new StringBuilder();
        if (StringUtils.isNotBlank(area)) {
            StringTokenizer t = new StringTokenizer(area, ",");
            while (t.hasMoreTokens()) {
                String pc = t.nextToken();
                String[] pcs = pc.split("@");
                if (strBuf.length() > 0) {
                    strBuf.append("，");
                }
                strBuf.append(pcs[0]);
                for (int i = 1; i < pcs.length; i++) {
                    String[] cds = pcs[i].trim().split("\\$");
                    strBuf.append(i == 1 ? "（" : "，").append(cds[0]);
                    for (int j = 1; j < cds.length; j++) {
                        strBuf.append(j == 1 ? "（" : "，").append(cds[j]);
                        if (j == cds.length - 1) {
                            strBuf.append("）");
                        }
                    }
                    if (i == pcs.length - 1) {
                        strBuf.append("）");
                    }
                }
            }
        }
        StringBuilder buf = new StringBuilder();
        if (strBuf.length() > 0) {
            buf.append("省份（例外地址）: ").append(strBuf);
        }
        return buf.toString();
    }

    private static String getTypeName(AbstractSpecifyCondition condition) {
        return WaveRuleInfoLogTypeNameEnum.getTypeName(null, condition);
    }

    private static String getTypeName(WaveRuleInfoLogTypeNameEnum waveRuleInfoLogTypeNameEnum, AbstractSpecifyCondition condition) {
        return WaveRuleInfoLogTypeNameEnum.getTypeName(waveRuleInfoLogTypeNameEnum, condition);
    }

    @FunctionalInterface
    interface Function3<A, B, C, R> {
        R apply(A a, B b, C c);
    }
}

package com.raycloud.dmj.domain.trade.config.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
public class SuitToSingleWhenSyncing extends SwitchConfig implements Serializable {
    private static final long serialVersionUID = -8896517826155563457L;

    public static String DEFAULT_JSON = JSONObject.toJSONString(new SuitToSingleWhenSyncing());

    private Set<String> outerIds = new HashSet<>();

    private String op = "include";

    private Set<Long> userIds = new HashSet<>();

    public boolean can(Long userId) {
        return opened() && (this.userIds.isEmpty() || this.userIds.contains(userId));
    }

    public boolean outerIdFilter(String outerId) {
        if ("include".equals(this.op)) {
            return outerId == null ||
                    outerId.isEmpty() ||
                    this.outerIds == null ||
                    this.outerIds.isEmpty() ||
                    this.outerIds.contains(outerId);
        }

        if ("exclude".equals(this.op)) {
            return outerId == null ||
                    outerId.isEmpty() ||
                    this.outerIds == null ||
                    this.outerIds.isEmpty() ||
                    !this.outerIds.contains(outerId);
        }

        return true;
    }
}

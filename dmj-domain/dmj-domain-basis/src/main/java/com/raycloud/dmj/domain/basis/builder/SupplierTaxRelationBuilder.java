package com.raycloud.dmj.domain.basis.builder;

import com.raycloud.dmj.domain.basis.SupplierTaxRelation;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/30
 * @since JDK1.8
 */
public final class SupplierTaxRelationBuilder {
    private Long id;
    private Long companyId;
    private Long supplierId;
    private Long supplierTaxId;
    private Integer enableStatus;
    private Long operateUserId;
    private String operateUserName;
    private Date created;
    private Date modified;

    private SupplierTaxRelationBuilder() {
    }

    public static SupplierTaxRelationBuilder builder() {
        return new SupplierTaxRelationBuilder();
    }

    public SupplierTaxRelationBuilder id(Long id) {
        this.id = id;
        return this;
    }

    public SupplierTaxRelationBuilder companyId(Long companyId) {
        this.companyId = companyId;
        return this;
    }

    public SupplierTaxRelationBuilder supplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierTaxRelationBuilder supplierTaxId(Long supplierTaxId) {
        this.supplierTaxId = supplierTaxId;
        return this;
    }

    public SupplierTaxRelationBuilder enableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
        return this;
    }

    public SupplierTaxRelationBuilder operateUserId(Long operateUserId) {
        this.operateUserId = operateUserId;
        return this;
    }

    public SupplierTaxRelationBuilder operateUserName(String operateUserName) {
        this.operateUserName = operateUserName;
        return this;
    }

    public SupplierTaxRelationBuilder created(Date created) {
        this.created = created;
        return this;
    }

    public SupplierTaxRelationBuilder modified(Date modified) {
        this.modified = modified;
        return this;
    }

    public SupplierTaxRelation build() {
        SupplierTaxRelation supplierTaxRelation = new SupplierTaxRelation();
        supplierTaxRelation.setId(id);
        supplierTaxRelation.setCompanyId(companyId);
        supplierTaxRelation.setSupplierId(supplierId);
        supplierTaxRelation.setSupplierTaxId(supplierTaxId);
        supplierTaxRelation.setEnableStatus(enableStatus);
        supplierTaxRelation.setOperateUserId(operateUserId);
        supplierTaxRelation.setOperateUserName(operateUserName);
        supplierTaxRelation.setCreated(created);
        supplierTaxRelation.setModified(modified);
        return supplierTaxRelation;
    }
}

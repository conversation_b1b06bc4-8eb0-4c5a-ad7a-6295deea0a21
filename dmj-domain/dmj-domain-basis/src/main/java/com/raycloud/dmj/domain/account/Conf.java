package com.raycloud.dmj.domain.account;

import com.alibaba.fastjson.annotation.JSONField;
import com.raycloud.dmj.domain.ConfigConst;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.user.SyncSettings;
import com.raycloud.dmj.domain.utils.CommonConstants;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 公司的配置信息
 *
 * <AUTHOR>
 */
public class Conf implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -2326209504731393685L;

    /**
     * 卖家的订单的快捷留言消息配置，使用','分割每个关键词
     */
    private String shortcutMessage;

    /**
     * 订单同步设置
     */
    private SyncSettings syncSettings;

    /**
     * 上一次点击同步订单或者同步订单的时间，这个和lastImportTradeTime时间不一样
     */
    private Date lastClickImportTradeTime;

    /**
     * 快递智能匹配优先级 0地区匹配 1运费匹配
     */
    private Integer expressMatchPriority;

    /**
     * 引导值，目前有5个步骤，只有guide到5时，引导才算完成
     */
    private int guide = 1;

    /**
     * 是否开启仓储，默认为true
     */
    private Boolean openWms;

    /**
     * 货位编码类型
     * 默认/0  固定编码配置
     * 1	自定义货位编码配置
     */
    private Integer sectionCodeType;

    /**
     * 是否开启复盘，默认false
     */
    private Boolean openRecheck;

    /**
     * 智能采购上下限取值
     */
    private String smartCgLimitType;

    /**
     * 智能采购库存补充方法
     */
    private String smartCgSupplyType;

    /**
     * 智能采购仓库
     */
    private Long smartCgWarehouseId;

    /**
     * 智能采购销售天数
     */
    private Integer smartCgSaleDays;

    /**
     * 智能采购是否考虑赠品
     */
    private Boolean smartCgContainsGift;

    /**
     * 参与库存上传的仓库
     */
    private String stockUploadWarehouses;

    /**
     * 是否开启波次打印
     */
    private Integer openWave;

    /**
     * 是否开启后置打印
     */
    private Integer openPrintDelay;

    /**
     * 是否开启返库，默认false
     */
    private Boolean openDepot;

    /**
     * 是否开启多品多件播种
     */
    private Integer openSeed;

    /**
     * 是否开启多品多件播种打印
     */
    private Integer openSeedPrint;

    /**
     * 播种位置号提示为排数
     */
    private Boolean openSeedRowPosition;

    /**
     * 播种每排位置数
     */
    private Integer seedRowPositionNum;

    /**
     * 自动生成的供应商编码
     */
    private String autoSupplierCode;

    /**
     * 商品匹配方案，A方案=0, B方案=1, C方案=2, D方案=3, Z方案=4
     */
    private Integer itemSpecial;

    /**
     * 是否开启智选物流
     */
    private Integer openIntelLigent;

    /**
     * 智选物流的店铺
     */
    private Long intelLigentUserId;

    /**
     * 是否使用菜鸟模板
     */
    private Integer useCainiao;

    /**
     * 是否使用京东快递模版tab
     *
     * 0：否，1：是
     */
    private Integer useJDTemplateTab;

    /**
     * 生成波次时是否自动获取单号
     */
    private Boolean waveAutoGetWaybillCode;

    /**
     * 是否是移动加权用户
     */
    private Integer movingWeighted;

    /**
     * 是否勾选 后续修复移动加权成本价。现在是0的商品，后续第一次修改成本价且期间无采购，收货等操作时，取当前成本价为移动加权成本价
     * 1 勾选 0 不勾选
     */
    private Integer movingWeightedFirstFill;

    /**
     * 云打印快递模板显示类型
     */
    private Integer cloudTemplateShowType;

    /**
     * 属性配置,后续的字段都加到该字段中
     * 如果放到外面的话，切环境保存配置就会导致配置丢失
     */
    private Map<String, Object> confAttrInfo;


	private Integer autoDownCopy;

	/**
	 * 是否使用菜鸟控件打印 0.不使用  1.使用
	 */
	private Integer printTaskSendCN;

    /**
     * 创建账号的时候是否需要默认开启单点登录 0.不需要  1.需要
     */
    private Integer needSingleLogin;

    /**
     * 单平台店铺授权
     * 1 开启 0 关闭
     */
    private Integer singlePlatformShopAuth;

    /**
     * 密码有效期设置,隔多少条需要修改密码
     */
    private Integer passwordExpiresDay;
    /**
     * 密码有效期设置，true-无限制，false是有限制
     * passwordExpiresDay是限制天数
     */
    private Boolean passwordExpiresFlag;


    /**
     * 换款开启的公司
     */
    private Integer replaceItemOuterIdEnable;

    public Boolean getPasswordExpiresFlag() {
        return passwordExpiresFlag;
    }

    public void setPasswordExpiresFlag(Boolean passwordExpiresFlag) {
        this.passwordExpiresFlag = passwordExpiresFlag;
    }

    public Integer getPasswordExpiresDay() {
        return passwordExpiresDay;
    }

    public void setPasswordExpiresDay(Integer passwordExpiresDay) {
        this.passwordExpiresDay = passwordExpiresDay;
    }

    private transient final static List<String> existConfFieldList = new ArrayList<String>();

    static {

        Class clazz = Conf.class;
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            existConfFieldList.add(field.getName());
        }
    }

    @JSONField(serialize = false)
    public List<String> getExistConfFields() {
        return existConfFieldList;
    }

    public Integer getItemSpecial() {
        return itemSpecial;
    }

    public void setItemSpecial(Integer itemSpecial) {
        this.itemSpecial = itemSpecial;
    }

    public Long getSmartCgWarehouseId() {
        return smartCgWarehouseId;
    }

    public void setSmartCgWarehouseId(Long smartCgWarehouseId) {
        this.smartCgWarehouseId = smartCgWarehouseId;
    }

    public Integer getOpenSeed() {
        return openSeed;
    }

    public void setOpenSeed(Integer openSeed) {
        this.openSeed = openSeed;
    }

    public Integer getOpenSeedPrint() {
        return openSeedPrint;
    }

    public void setOpenSeedPrint(Integer openSeedPrint) {
        this.openSeedPrint = openSeedPrint;
    }

    public Boolean getOpenDepot() {
        if (openDepot == null) {
            return false;
        }
        return openDepot;
    }

    public void setOpenDepot(Boolean openDepot) {
        this.openDepot = openDepot;
    }

    public String getShortcutMessage() {
        return shortcutMessage;
    }

    public void setShortcutMessage(String shortcutMessage) {
        this.shortcutMessage = shortcutMessage;
    }

    public SyncSettings getSyncSettings() {
        return syncSettings;
    }

    public void setSyncSettings(SyncSettings syncSettings) {
        this.syncSettings = syncSettings;
    }

    public Date getLastClickImportTradeTime() {
        return lastClickImportTradeTime;
    }

    public void setLastClickImportTradeTime(Date lastClickImportTradeTime) {
        this.lastClickImportTradeTime = lastClickImportTradeTime;
    }

    /**
     * 推荐使用{@link #getOpenWms()}
     */
    @Deprecated
    public Boolean isOpenWms() {
        if (openWms == null) {
            openWms = true;
        }
        return openWms;
    }

    public void setOpenWms(Boolean openWms) {
        this.openWms = openWms;
    }

    public Boolean getOpenWms() {
        if (openWms == null) {
            openWms = true;
        }
        return openWms;
    }

    public Integer getSectionCodeType() {
        if (sectionCodeType == null) {
            sectionCodeType = 0;
        }
        return sectionCodeType;
    }

    public void setSectionCodeType(Integer sectionCodeType) {
        this.sectionCodeType = sectionCodeType;
    }

    /**
     * 判断是否开启暂存区
     * @return
     */
    public boolean openWmsStorageSection() {
        if (confAttrInfo == null) {
            return false;
        }
        Object openWmsStorageSection = confAttrInfo.get("openStorageSection");
        return openWmsStorageSection != null && (Boolean) openWmsStorageSection;
    }

    public String includedInTotalStockWss() {
        if (confAttrInfo == null) {
            return null;
        }
        Object includedInTotalStockWss = confAttrInfo.get("includedInTotalStockWss");
        if (includedInTotalStockWss != null) {
            return (String)includedInTotalStockWss;
        }
        return null;
    }

    public Integer getStallType() {
        if (confAttrInfo == null) return 0;
        Object stallType = confAttrInfo.get("stallType");
        return stallType == null ? 0 : (Integer) stallType;
    }

    public Integer getGenericCodeOutLocation() {
        if (confAttrInfo == null) return 0;
        Object genericCodeOutLocation = confAttrInfo.get("genericCodeOutLocation");
        return genericCodeOutLocation == null ? 0 : (Integer) genericCodeOutLocation;
    }

    public Long getGenericCodeWarehouseId() {
        if (confAttrInfo == null) return 0L;
        Object getGenericCodeWarehouseId = confAttrInfo.get("genericCodeWarehouseId");
        if (getGenericCodeWarehouseId instanceof Integer){
            return getGenericCodeWarehouseId == null ? 0L : ((Integer) getGenericCodeWarehouseId).longValue();
        }else {
            return getGenericCodeWarehouseId == null ? 0L : (Long) getGenericCodeWarehouseId;
        }
    }

    public Integer getBlurTrade() {
        if (confAttrInfo == null) return 0;
        Object blurTrade = confAttrInfo.get("blurTrade");
        if ("all".equals(ConfigHolder.JD_HU_GRAY_CONFIG.getAll()) ){
            return 1;
        }
        return blurTrade == null ? 0 : (Integer) blurTrade;
    }

    public Integer getOpenWaitPayNotLockStock() {
        if (confAttrInfo == null) {
            return 0;
        }
        Object openWaitPayNotLockStock = confAttrInfo.get("openWaitPayNotLockStock");
        //没有设置默认为0 0：取平台商品属性默认值 1：付款减库存 2：下单减库存
        return openWaitPayNotLockStock == null ? 0 : (Integer) openWaitPayNotLockStock;
    }

    /**
     *  是否开启共享面单  1开启  0关闭
     */
    public boolean getHideShareOrder() {
        if (confAttrInfo == null) {
            return false;
        }
        Object hideShareOrder = confAttrInfo.get("hideShareOrder");
        return hideShareOrder != null && Integer.parseInt(String.valueOf(hideShareOrder)) == 1;
    }

    /**
     *  是否开启物流预警分析
     */
    public boolean getOpenLogisticsWarning() {
        if (confAttrInfo == null) {
            return false;
        }
        Object openLogisticsWarning = confAttrInfo.get("openLogisticsWarning");
        return openLogisticsWarning != null && Integer.parseInt(String.valueOf(openLogisticsWarning)) == 1;
    }

    /**
     *  物流预警分析开启时间
     */
    public Long getOpenLogisticsWarningDate() {
        if (confAttrInfo == null) {
            return null;
        }
        Object openLogisticsWarningDate = confAttrInfo.get("openLogisticsWarningDate");
        return openLogisticsWarningDate == null ? null : Long.parseLong(openLogisticsWarningDate.toString());
    }


    @JSONField(serialize = false)
    public Integer getAllowPrint() {
        if (confAttrInfo == null) {
            return 0;
        }
        Object allowPrint = confAttrInfo.get("allowPrint");
        //没有设置默认开启
        return allowPrint == null ? 1 : Integer.parseInt(String.valueOf(allowPrint));
    }

    public Integer getSeedRowNum() {
        if (confAttrInfo == null) {
            return null;
        }

        return (Integer) confAttrInfo.get("seedRowNum");
    }

    public boolean openOrderUniqueCode() {
        if (confAttrInfo == null) {
            return false;
        }
        Object orderUniqueCode = confAttrInfo.get("orderUniqueCode");
        return orderUniqueCode == null?false:(Integer.parseInt(String.valueOf(orderUniqueCode))==1?true:false);
    }

    public boolean openBlindScanIncludeSpecial() {
        if (confAttrInfo == null) {
            return false;
        }
        Object openBlindScanIncludeSpecial = confAttrInfo.get("openBlindScanIncludeSpecial");
        return openBlindScanIncludeSpecial == null?false:(Integer.parseInt(String.valueOf(openBlindScanIncludeSpecial))==1?true:false);
    }

    public boolean openJdWarehouseTradeAllowAudit() {
        if (confAttrInfo == null) {
            return false;
        }
        Object jdWarehouseTradeAllowAudit = confAttrInfo.get("jdWarehouseTradeAllowAudit");
        return jdWarehouseTradeAllowAudit == null?false:(Integer.parseInt(String.valueOf(jdWarehouseTradeAllowAudit))==1?true:false);
    }

    public boolean openSendGoodsRatio() {
        if(!openOrderUniqueCode()){
            return false;
        }
        Object sendGoodsRatio = confAttrInfo.get("sendGoodsRatio");
        return sendGoodsRatio == null?false:(Integer.parseInt(String.valueOf(sendGoodsRatio))==1?true:false);
    }

    public Boolean getOpenRecheck() {

        if (openRecheck == null) {
            openRecheck = false;
        }
        return openRecheck;
    }

    public void setOpenRecheck(Boolean openRecheck) {
        this.openRecheck = openRecheck;
    }

    public int getGuide() {
        return guide;
    }

    public void setGuide(int guide) {
        this.guide = guide;
    }

    public Integer getExpressMatchPriority() {
        if (expressMatchPriority == null) {
            return 0;
        }
        return expressMatchPriority;
    }

    public void setExpressMatchPriority(Integer expressMatchPriority) {
        this.expressMatchPriority = expressMatchPriority;
    }

    public String getSmartCgLimitType() {
        return smartCgLimitType;
    }

    public void setSmartCgLimitType(String smartCgLimitType) {
        this.smartCgLimitType = smartCgLimitType;
    }

    public String getSmartCgSupplyType() {
        return smartCgSupplyType;
    }

    public void setSmartCgSupplyType(String smartCgSupplyType) {
        this.smartCgSupplyType = smartCgSupplyType;
    }

    public String getStockUploadWarehouses() {
        return stockUploadWarehouses;
    }

    public void setStockUploadWarehouses(String stockUploadWarehouses) {
        this.stockUploadWarehouses = stockUploadWarehouses;
    }

    public Integer getOpenWave() {
        if (openWave == null) {
            return 0;
        }
        return openWave;
    }

    public void setOpenWave(Integer openWave) {
        this.openWave = openWave;
    }

    public Integer getOpenPrintDelay() {
        if (openPrintDelay == null) {
            return 0;
        }
        return openPrintDelay;
    }

    public void setOpenPrintDelay(Integer openPrintDelay) {
        this.openPrintDelay = openPrintDelay;
    }

    public String getAutoSupplierCode() {
        return autoSupplierCode;
    }

    public void setAutoSupplierCode(String autoSupplierCode) {
        this.autoSupplierCode = autoSupplierCode;
    }

    public Integer getSmartCgSaleDays() {
        return smartCgSaleDays;
    }

    public void setSmartCgSaleDays(Integer smartCgSaleDays) {
        this.smartCgSaleDays = smartCgSaleDays;
    }

    public Boolean getSmartCgContainsGift() {
        return smartCgContainsGift;
    }

    public void setSmartCgContainsGift(Boolean smartCgContainsGift) {
        this.smartCgContainsGift = smartCgContainsGift;
    }

    public Integer getSeedRowPositionNum() {
        return seedRowPositionNum;
    }

    public void setSeedRowPositionNum(Integer seedRowPositionNum) {
        this.seedRowPositionNum = seedRowPositionNum;
    }

    public Boolean getOpenSeedRowPosition() {
        return openSeedRowPosition;
    }

    public void setOpenSeedRowPosition(Boolean openSeedRowPosition) {
        this.openSeedRowPosition = openSeedRowPosition;
    }

    public Integer getOpenIntelLigent() {
        if (null == openIntelLigent) {
            openIntelLigent = 0;
        }
        return openIntelLigent;
    }

    public void setOpenIntelLigent(Integer openIntelLigent) {
        this.openIntelLigent = openIntelLigent;
    }

    public Long getIntelLigentUserId() {
        return intelLigentUserId;
    }

    public void setIntelLigentUserId(Long intellLigentUserId) {
        this.intelLigentUserId = intellLigentUserId;
    }

    public Integer getUseCainiao() {
        if (useCainiao == null) {
            return 1;
        }
        return useCainiao;
    }

    public Integer getUseJDTemplateTab() {
        if (useJDTemplateTab == null) {
            return 1;
        }
        return useJDTemplateTab;
    }

    public void setUseJDTemplateTab(Integer useJDTemplateTab) {
        this.useJDTemplateTab = useJDTemplateTab;
    }

    public void setUseCainiao(Integer useCainiao) {
        this.useCainiao = useCainiao;
    }

    public Boolean getWaveAutoGetWaybillCode() {
        return waveAutoGetWaybillCode;
    }

    public void setWaveAutoGetWaybillCode(Boolean waveAutoGetWaybillCode) {
        this.waveAutoGetWaybillCode = waveAutoGetWaybillCode;
    }

    public Integer getCloudTemplateShowType() {
        return cloudTemplateShowType;
    }

    public void setCloudTemplateShowType(Integer cloudTemplateShowType) {
        this.cloudTemplateShowType = cloudTemplateShowType;
    }

    public Map<String, Object> getConfAttrInfo() {
        return confAttrInfo;
    }

    @JSONField(serialize = false, deserialize = false)
    public Map<String, Object> getConfAttrInfoIfEmpty() {
        if (confAttrInfo == null) {
            return new HashMap<>();
        }

        return confAttrInfo;
    }





    public void setConfAttrInfo(Map<String, Object> confAttrInfo) {
        this.confAttrInfo = confAttrInfo;
    }

    public Integer getMovingWeighted() {
        if (null == movingWeighted)
            return 0;
        return movingWeighted;
    }

    public void setMovingWeighted(Integer movingWeighted) {
        this.movingWeighted = movingWeighted;
    }

    public Integer getMovingWeightedFirstFill() {
        if (null == movingWeightedFirstFill)
            return 0;
        return movingWeightedFirstFill;
    }

    public void setMovingWeightedFirstFill(Integer movingWeightedFirstFill) {
        this.movingWeightedFirstFill = movingWeightedFirstFill;
    }

	/**
	 * 审核后激活占用库存
	 */
	public boolean openAuditActiveStockRecord(){
		if(confAttrInfo == null){
			return false;
		}
		Object openAuditActiveStockRecord = confAttrInfo.get("openAuditActiveStockRecord");
		return openAuditActiveStockRecord != null && (Boolean) openAuditActiveStockRecord;
	}

	public Integer openPurchaseReturnStorage() {
	    if (confAttrInfo == null) {
	        return null;
        }
        Object openPurchaseReturnStorage = confAttrInfo.get("openPurchaseReturnStorage");
	    return openPurchaseReturnStorage != null ? Integer.parseInt(String.valueOf(openPurchaseReturnStorage)) : 0;
    }

	public Integer getAutoDownCopy() {
		return autoDownCopy;
	}

	public void setAutoDownCopy(Integer autoDownCopy) {
		this.autoDownCopy = autoDownCopy;
	}

	public Integer getPrintTaskSendCN() {
		return printTaskSendCN;
	}

	public void setPrintTaskSendCN(Integer printTaskSendCN) {
		this.printTaskSendCN = printTaskSendCN;
	}

	public Integer getMergeCheckDays() {
	    if (confAttrInfo == null) {
	        return null;
        }
        if (confAttrInfo.get("mergeCheckDays") == null) {
            return null;
        }
        return (Integer) confAttrInfo.get("mergeCheckDays");
    }

    public Integer getOpenGoodsSectionExport() {
        if (confAttrInfo == null) {
            return null;
        }
        if (confAttrInfo.get(ConfigConst.OPEN_GOODS_SECTION_EXPORT) == null) {
            return null;
        }
        return (Integer) confAttrInfo.get(ConfigConst.OPEN_GOODS_SECTION_EXPORT);
    }

    public Long getSmartLastCalTime() {
        if (confAttrInfo == null) {
            return null;
        }

        //智能采购最后一次计算时间
        Object smartLastCalTime = confAttrInfo.get("smartLastCalTime");
        return (Long) smartLastCalTime;
    }

    public Integer getNeedSingleLogin() {
        return needSingleLogin;
    }

    public void setNeedSingleLogin(Integer needSingleLogin) {
        this.needSingleLogin = needSingleLogin;
    }

    public Integer getSinglePlatformShopAuth() {
        return singlePlatformShopAuth;
    }

    public void setSinglePlatformShopAuth(Integer singlePlatformShopAuth) {
        this.singlePlatformShopAuth = singlePlatformShopAuth;
    }

    public String getUseItemOuterIdMatchOrderUserIds() {
	    if (confAttrInfo == null) {
	        return null;
        }

	    if (!confAttrInfo.containsKey("useItemOuterIdMatchOrderUserIds")) {
	        return null;
        }

	    return (String) confAttrInfo.get("useItemOuterIdMatchOrderUserIds");
    }

    public Integer getReplaceItemOuterIdEnable() {
        return replaceItemOuterIdEnable;
    }

    public void setReplaceItemOuterIdEnable(Integer replaceItemOuterIdEnable) {
        this.replaceItemOuterIdEnable = replaceItemOuterIdEnable;
    }

    public boolean replaceItemOuterIdEnable(){
        if (replaceItemOuterIdEnable == null){
            return false;
        }
        if (CommonConstants.VALUE_YES.equals(replaceItemOuterIdEnable)){
            return true;
        }
        return false;

    }
}

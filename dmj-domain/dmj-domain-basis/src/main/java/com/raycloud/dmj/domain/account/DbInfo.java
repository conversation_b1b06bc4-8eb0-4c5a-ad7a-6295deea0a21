package com.raycloud.dmj.domain.account;

import java.io.Serializable;

/**
 * 数据库分表信息
 *
 * <AUTHOR>
 */
public class DbInfo implements Serializable {

    /**
     * 主库编号
     */
    public static final int MASTER_DB_NO = 0;

    /**
     * 分两张表
     */
    public static final Integer DB_NO_TWO = 2;

    /**
     *
     */
    private static final long serialVersionUID = 8378250252039484038L;

    /**
     * Item数据库实例数量
     */
    public static final Integer COUNT_ITEM_INSTANCE_NUM = 20;

    /**
     * Sku数据库实例数量
     */
    public static final Integer COUNT_SKU_INSTANCE_NUM = 50;

    /**
     * item_supplier_bridge数据库实例数量
     */
    public static final Integer COUNT_ITEM_SUPPLIER_BRIDGE_NUM = 50;

    /**
     * 商品设置店铺分销价
     * item_sale_price_bridge 表数量
     */
    public static final Integer COUNT_ITEM_SALE_PRICE_BRIDGE_NUM = 50;

    /**
     * 订单表的数量
     */
    public static final Integer COUNT_TRADE_DB_NUM = 100;

    /**
     * 子订单表的数量
     */
    public static final Integer COUNT_ORDER_DB_NUM = 200;

    /**
     * 订单异常表的数量
     */
    public static final Integer COUNT_TRADE_EXCEPT_DB_NUM = 500;

    /**
     * order 异常表数量
     */
    public static final Integer COUNT_ORDER_EXCEPT_DB_NUM = 500;

    /**
     * 订单表优先级分表数量
     */
    public static final Integer COUNT_TRADE_PRIORITY_DB_NUM = 100;

    /**
     * 记录发送给三方仓数据表的数量
     */
    public static final Integer COUNT_TRADE_PARTY3_DATA_NUM = 100;


    /**
     * 子订单修改记录表的数量
     */
    public static final Integer COUNT_ORDER_MODIFY_LOG_DB_NUM = 200;

    /**
     * 订单操作记录分表数量
     */
    public static final Integer COUNT_TRADE_TRACE_NUM = 200;

    /**
     * 配置更新操作记录分表数量
     */
    public static final Integer COUNT_CONFIG_TRACE_NUM = 100;

    /**
     * 配置发货缓存分表数量
     */
    public static final Integer COUNT_CONSIGN_CACHE_NUM = 500;

    /**
     * 商品操作记录分表数量
     */
    public static final Integer COUNT_ITEM_TRACE_NUM = 200;

    public static final Integer COUNT_ITEMBRIGE_DB_NUM = 20;

    public static final Integer COUNT_SKUBRIGE_DB_NUM = 50;


    /**
     * 子订单表的数量
     */
    public static final Integer COUNT_STOCK_ADJUST_LOG_DB_NUM = 80;

    /**
     * 用户电子面单模板表的数量
     */
    public static final Integer COUNT_USER_WLB_EXPRESS_TEMPLATE_NUM = 2;

    /**
     * 用户电子面单自定义模板表的数量
     */
    public static final Integer COUNT_USER_WLB_CUSTOM_TEMPLATE_NUM = 2;

    /**
     * 用户拿货单模板表的数量
     */
    public static final Integer COUNT_USER_ASSEMBLY_TEMPLATE_NUM = 2;

    /**
     * 用户发货单模板表的数量
     */
    public static final Integer COUNT_USER_DELIVER_TEMPLATE_NUM = 2;

    /**
     * 用户拣货单模板表的数量
     */
    public static final Integer COUNT_USER_PICKER_TEMPLATE_NUM = 2;

    /**
     * 用户拣货单模板表的数量
     */
    public static final Integer COUNT_USER_GETTER_TEMPLATE_NUM = 2;

    /**
     * 用户快递模板表的数量
     */
    public static final Integer COUNT_USER_EXPRESS_TEMPLATE_NUM = 2;

    /**
     * 用户单据模板表的数量
     */
    public static final Integer COUNT_USER_INVOICES_TEMPLATE_NUM = 2;

    /**
     * 运费模版表的数量
     */
    public static final Integer COUNT_FREIGHT_TEMPLATE_NUM = 5;

    /**
     * 打印订单日志分表的数量
     */
    public static final Integer COUNT_PRINT_TRADE_LOG_NUM = 20;

    /**
     * 订单一单多包打印记录分表的数量
     */
    public static final Integer COUNT_MULTI_PACKS_PRINT_TRADE_LOG_NUM = 20;

    public static final Integer TRANSPOND_PRINT_LOG_COUNT_NUM = 5;

    /**
     * 打印订单统计日志分表的数量
     */
    public static final Integer COUNT_PRINT_ORDER_SHIP_COUNT_NUM = 5;

    /**
     * 自定义打印分表
     */
    public static final Integer COUNT_USER_CUSTOM_PRINT_NUM = 5;

    /**
     * 电子面单号记录的分表数量
     */
    public static final Integer COUNT_OUT_SID_POOL_NUM = 30;

    /**
     * 波次和订单送打状态表数量*
     */
    public static final Integer COUNT_WAVE_TRADE_PRINTING_STATUS_NUM = 30;

    /**
     * 电子面单号回收的分表数量
     */
    public static final Integer COUNT_OUT_SID_RECOVER_NUM = 2;

    /**
     * 电子面单的网点与订单的对应关系表数量
     */
    public static final Integer COUNT_WLB_ADDRESS_TRADE_LINK_NUM = 10;

    /**
     * 预售规则分表
     */
    public static final Integer COUNT_PRESELL_ITEM_TRADE = 20;

    /**
     * 短信交易的分表数量
     */
    public static final Integer COUNT_SMS_TXN_NUM = 6;

    /**
     * 短信通知日志的分表数量
     */
    public static final Integer COUNT_NOTIFICATION_LOG_NUM = 6;

    /**
     * 操作日志的分表数量
     */
    public static final Integer COUNT_ACTION_LOG_NUM = 10;

    /**
     * 短信的创建订单的分表号
     */
    public static final Integer COUNT_SMS_CREATED_TRADE_NUM = 5;

    /**
     * 快递智能匹配的分表号
     */
    public static final Integer COUNT_EXPRESS_SMARTMATCH_NUM = 2;
    /**
     * 运单号使用规则的分表号
     */
    public static final Integer COUNT_WAYBILL_USING_RULE_NUM = 10;
    /**
     * 运单号使用规则详情的分表号*
     */
    public static final Integer COUNT_WAYBILL_USING_RULE_DETAIL_NUM = 10;
    /**
     * 运单号使用规则记录的分表号*
     */
    public static final Integer COUNT_WAYBILL_USING_RULE_LOG_NUM = 100;
    /**
     * 运单号使用规则充值记录的分表号*
     */
    public static final Integer COUNT_WAYBILL_TOP_UP_LOG_NUM = 10;

    /**
     * 商品库存的分表数量
     */
    public static final Integer COUNT_ITEM_STOCK_NUM = 80;

    /**
     * 库存日志记录
     */
    public static final Integer COUNT_ITEM_STOCK_LOG_NUM = 80;

    /**
     * 操作日志的分表数量
     */
    public static final Integer COUNT_OP_LOG_NUM = 80;

    /**
     * 售后工单的分表数量
     */
    public static final Integer COUNT_WORK_ORDER_NUM = 5;

    /**
     * 售后工单对应的退货发货的分表数量
     */
    public static final Integer COUNT_AS_REISSUE_REFUND_NUM = 5;

    /**
     * 售后工单对应的商品快照的分表数量
     */
    public static final Integer COUNT_AS_ITEM_SNAPSHOT = 5;

    /**
     * 售后工单的留言板的分表数量
     */
    public static final Integer COUNT_AS_MSG_BOARD = 5;

    /**
     * 售后工单的操作日志的分表数量
     */
    public static final Integer COUNT_AS_OPERATE_LOG = 5;

    /**
     * 销退入库的分表数量
     */
    public static final Integer COUNT_AS_REFUND_WAREHOUSE = 5;

    /**
     * 售后工单的备注的分表数量
     */
    public static final Integer COUNT_AS_WO_REMAK = 5;

    /**
     * 售后工单的自定义售后原因的分表数量
     */
    public static final Integer COUNT_AS_REASON = 5;

    /**
     * 仓库绑定模版分表数量
     */
    public static final Integer COUNT_WAREHOUSE_TEMPLATE_NUM = 5;

    /**
     * 快递模版下网点分表数量
     */
    public static final Integer COUNT_TEMPLATE_BRANCH_NUM = 5;

    /**
     * 快递模版下网点的地址分表数量
     */
    public static final Integer COUNT_TEMPLATE_BRANCH_ADDRESS_NUM = 5;

    /**
     * 库存预售 分表数量
     */
    public static final Integer COUNT_STOCK_PRESELL_NUM = 5;
    /**
     * 库存预留 分表数量
     */
    public static final Integer COUNT_STOCK_RESERVE_NUM = 5;
    /**
     * 库存锁定详情 分表数量
     */
    public static final Integer COUNT_STOCK_LOCK_DETAIL_NUM = 80;

    /**
     * stock_upload_rule 规则表的 分表号
     */
    public static final Integer COUNT_STOCK_UPLOAD_RULE_NUM = 50;
    /**
     * 系统item的分表数量
     */
    public static final Integer COUNT_DMJ_ITEM_NUM = 20;
    /**
     * 系统sku的分表数量
     */
    public static final Integer COUNT_DMJ_SKU_NUM = 50;

    /**
     * 系统已删除item的分表数量
     */
    public static final Integer COUNT_DMJ_ITEM_DEL_NUM = 20;

    /**
     * 系统已删除sku的分表数量
     */
    public static final Integer COUNT_DMJ_SKU_DEL_NUM = 20;

    //商品警戒记录分表数量
    public static final Integer COUNT_ITEM_WARN = 40;


    /**
     * 订单报表分表数量
     */
    public static final Integer COUNT_REPORT_TRADE_NUM = 50;

    /**
     * 订单包装验货报表分表数量
     */
    public static final Integer COUNT_REPORT_TRADE_PACK_NUM = 50;

    /**
     * 商品报表分表数量
     */
    public static final Integer COUNT_REPORT_ORDER_NUM = 80;

    /**
     * 报表库stock_sum分表数
     */
    public static final Integer COUNT_DATA_STOCK_SUM_NUM = 10;

    /**
     * 报表库report_refund 商品退货报表分表数
     */
    public static final Integer COUNT_DATA_REPORT_REFUND = 50;

    /**
     * 报表库item_week_month_sales分表数
     */
    public static final Integer COUNT_ITEM_WEEK_MONTH_SALES_NUM = 20;

    /**
     * 报表relation_cids分表数
     */
    public static final Integer COUNT_DATA_RELATION_CIDS_NUM = 20;

    /**
     * 售后报表分表数量
     */
    public static final Integer COUNT_REPORT_AFTERSALE_NUM = 5;

    /**
     * 店铺销量报表分表数量
     */
    public static final Integer COUNT_REPORT_SHOP = 5;


    /**
     * stock_in_out_record 出入库记录表, 分表号
     */
    public static final Integer COUNT_STOCK_IN_OUT_RECORD_NUM = 80;

    /**
     * 订单库存申请 子订单在库存模块中的记录表 分表号
     */
    public static final Integer COUNT_STOCK_ORDER_RECORD_NUM = 80;

    public static final Integer COUNT_SELLER_CAT = 5;

    public static final Integer COUNT_SHOP_SELLER_CAT = 5;

    public static final Integer COUNT_SKU_ERP_DICT_OTHER = 20;

    //采购管理
    /**
     * 采购单的分表号
     */
    public static final Integer COUNT_PURCHASE_ORDER = 4;
    /**
     * 采购单详情的分表号
     */
    public static final Integer COUNT_PURCHASE_ORDER_DETAIL = 20;
    /**
     * 入库单的分表号
     */
    public static final Integer COUNT_WAREHOUSE_ENTRY = 100;
    /**
     * 入库单详情的分表号
     */
    public static final Integer COUNT_WAREHOUSE_ENTRY_DETAIL = 200;

    /**
     * 入库单关联唯一码的分表号
     */
    public static final Integer COUNT_WAREHOUSE_ENTRY_WAVE_UNIQUE_CODE = 20;
    /**
     * 业务实体关联唯一码的分表号
     */
    public static final Integer COUNT_TRADES_BUSINESS_WAVE_UNIQUE_CODE = 20;

    /**
     * 采退建议的分表号
     */
    public static final Integer COUNT_SMART_PURCHASE_RETURN = 20;

    /**
     * 上架单分表号
     */
    public static final Integer COUNT_SHELVE_ORDER = 8;

    /**
     * 上架单详情分表号
     */
    public static final Integer COUNT_SHELVE_ORDER_DETAIL = 40;

    /**
     * 上架单-入库单 关联表 数量
     */
    public static final Integer COUNT_ASSO_SHELVE_WAREHOUSE_ENTRY = 20;

    /**
     * 业务单据关联表 数量
     */
    public static final Integer COUNT_ASSO_BUSINESS_DOCUMENT = 20;

    /**
     * 文件存储日志的分表号
     */
    public static final Integer COUNT_STORAGE_LOG = 5;

    /**
     * 财务收款客户的分表号
     */
    public static final Integer COUNT_FINANCE_PAYEE = 2;

    /**
     * 财务科目的分表号
     */
    public static final Integer COUNT_FINANCE_SUBJECT = 2;

    /**
     * 财务收支账户的分表号
     */
    public static final Integer COUNT_FINANCE_PAYER = 2;

    /**
     * 财务资金流水的分表号
     */
    public static final Integer COUNT_FINANCE_CASH_JOURNAL = 10;

    /**
     * 财务运单信息导入的分表号
     */
    public static final Integer COUNT_FINANCE_IMPORT_EXPRESS = 200;

    /**
     * 财务订单同步的分表号
     */
    public static final Integer COUNT_FINANCE_TRADE_EXPRESS = 500;

    /**
     * 财务支付宝账单的分表号
     */
    public static final Integer COUNT_FINANCE_ALIPAY_BILL = 30;

    /**
     * 财务支付宝对账单的分表号
     */
    public static final Integer COUNT_FINANCE_ALIPAY_BILL_COMP = 30;


    /**
     * 货位商品关联表数量
     */
    public static final Integer COUNT_ASSO_GOODS_SECTION_SKU = 50;

    /**
     * 库区警戒关联表数量
     */
    public static final Integer COUNT_STOCK_REGION_ALERT = 50;

    /**
     * 商品盘点记录表数量
     */
    public static final Integer COUNT_SKU_CHECK_RECORD = 50;

    /**
     * 货位商品锁定记录表数量
     */
    public static final Integer COUNT_GOODS_SECTION_SKU_LOCK_RECORD = 100;

    /**
     * 货位子订单关联记录表数量
     */
    public static final Integer COUNT_GOODS_SECTION_ORDER_RECORD = 80;

    /**
     * 货位出入库记录表数量
     */
    public static final Integer COUNT_GOODS_SECTION_IN_OUT_RECORD = 80;

    /**
     * 货位库存操作日志分表数量
     */
    public static final Integer COUNT_GOODS_SECTION_INVENTORY_OP_LOG = 50;

    /**
     * 仓储标签列表分表号
     */
    public static final Integer WMS_LABEL = 10;

    /**
     * 仓储标签关联表分表号
     */
    public static final Integer WMS_LABEL_BRIDGE = 20;

    /**
     * 库存调拨任务分表数量
     */
    public static final Integer COUNT_SKU_ALLOCATE_TASK = 50;

    /**
     * 库存调拨出库记录分表数量
     */
    public static final Integer COUNT_SKU_ALLOCATE_FROM_RECORD = 50;

    /**
     * 库存调拨入库记录分表数量
     */
    public static final Integer COUNT_SKU_ALLOCATE_TO_RECORD = 50;

    /**
     * 京东店铺所关联的快递公司的分表数量
     */
    public static final Integer COUNT_USER_LOGISTICS_COMPANIES_JD_NUM = 2;

    /**
     * 货位库存导入记录分表
     */
    public static final Integer COUNT_GOODS_SECTION_INVENTORY_IMPORT = 50;

    /*
     * 交易赠品促销活动分表数量
     */
    public static final Integer COUNT_GIFT_PROMOTION_NUM = 2;

    /**
     * 交易赠品促销规则分表数量
     */
    public static final Integer COUNT_GIFT_PROMOTION_RULE_NUM = 5;

    /**
     * 交易赠品促销规则的对应商品分表数量
     */
    public static final Integer COUNT_GIFT_PROMOTION_RULE_SELLER_ITEM_NUM = 5;

    /**
     * 交易赠品促销规则的对应赠品分表数量
     */
    public static final Integer COUNT_GIFT_PROMOTION_RULE_GIVE_ITEM_NUM = 5;

    /**
     * 交易赠品促销规则的对应指定子单商品总数分表数量
     */
    public static final Integer COUNT_GIFT_PROMOTION_RULE_GIVE_RANGE_ITEM_NUM = 5;

    /**
     * 交易赠品与订单的匹配日志记录
     */
    public static final Integer COUNT_GIFT_PROMOTION_MATCH_TRADE_LOG_NUM = 10;

    /**
     * 赠品按标签赠送，标签配置的分表数量
     */
    public static final Integer COUNT_GIFT_PROMOTION_RULE_TAG_NUM = 5;

    /**
     * 交易赠品与订单的匹配日志记录
     */
    public static final Integer COUNT_ITEM_SUITE_BRIDGE_NUM = 50;

    /**
     * 订单临时表表的数量
     */
    public static final Integer COUNT_TRADE_TEMP_DB_NUM = 20;

    /**
     * 订单月销量报表数量
     */
    public static final Integer COUNT_REPORT_MONTH_DB_NUM = 50;

    /**
     * 库存出入库日报表数量
     */
    public static final Integer COUNT_STOCK_DAY_REPORT = 80;

    /**
     * 售后分析报表数量
     */
    public static final Integer COUNT_REPORT_AS_ANALYSE = 5;

    /**
     * 商品: 品牌的分表数量
     */
    public static final Integer COUNT_BRAND_NUM = 50;

    /**
     * 发货方式报表数量
     */
    public static final Integer COUNT_REPORT_SEND_GOODS_TYPE = 50;

    /**
     * pda模块的拣货订单表数量
     */
    public static final Integer COUNT_PDA_PICK_TRADE = 10;

    /**
     * pda模块的拣货订单表数量
     */
    public static final Integer COUNT_PDA_PICK_ITEM = 20;

    /**
     * 波次表数量
     */
    public static final Integer COUNT_WAVE = 10;

    /**
     * 波次拣选表数量
     */
    public static final Integer COUNT_WAVE_PICKING = 10;

    /**
     * 波次订单关联表数量
     */
    public static final Integer COUNT_ASSO_WAVE_TRADE = 30;

    /**
     * 审核绩效报表数量
     */
    public static final Integer COUNT_REPORT_TRADE_AUDIT = 50;

    /**
     * 称重绩效报表数量
     */
    public static final Integer COUNT_REPORT_TRADE_WEIGHT = 50;

    /**
     * 面单回收池表数量
     */
    public static final Integer COUNT_OUT_SID_RECYCLE_POOL_NUM = 50;

    /**
     * 商品采购报表数量
     */
    public static final Integer COUNT_REPORT_PURCHASE = 20;

    /**
     * 仓储返库任务分表
     */
    public static final Integer COUNT_DEPOT_TASK_NUM = 10;

    /**
     * 商品批次分表数
     */
    public static final Integer COUNT_ITEM_BATCH_DB_NUM = 50;

    /**
     * 仓储返库记录分表
     */
    public static final Integer COUNT_DEPOT_RECORD_NUM = 20;

    /**
     * 仓储返库订单明细分表
     */
    public static final Integer COUNT_DEPOT_ORDER_DETAIL_NUM = 20;

    /**
     * 商品上下架
     */
    public static final Integer COUNT_SHELF_TASK_NUM = 20;

    /**
     * 商品上下架日志
     */
    public static final Integer COUNT_SHELF_TASK_LOG_NUM = 20;

    /**
     * trade_solr分表数
     */
    public static final Integer COUNT_TRADE_SOLR_NUM = 10;

    /**
     * 波次拣选号分表数量
     */
    public static final Integer COUNT_WAVE_PICKING_CODE_NUM = 10;

    /**
     * 波次后置分拣表数量
     */
    public static final Integer COUNT_WAVE_SORTING_NUM = 10;

    /**
     * 波次后置分拣明细表数量
     */
    public static final Integer COUNT_WAVE_SORTING_DETAIL_NUM = 20;

    /**
     * 采购退货表数量
     */
    public static final Integer COUNT_PURCHASE_RETURN_NUM = 10;

    /**
     * 采购退货明细分表数
     */
    public static final Integer COUNT_PURCHASE_RETURN_DETAIL_NUM = 20;

    /**
     * 仓储锁定分表
     */
    public static final Integer COUNT_WMS_BUSI_LOCK = 30;

    //item 第三方关联
    public static final Integer COUNT_ASS_ITEM = 30;

    //sku 第三方关联
    public static final Integer COUNT_ASS_SKU = 60;

    //订单 第三方关联
    public static final Integer COUNT_ASS_ORDER = 80;

    /**
     * 库存业务锁定分表
     */
    public static final Integer COUNT_STOCK_BUSI_LOCK = 30;

    public static final Integer COUNT_PARTY3_WAREHOUSE_LOG = 80;

    /**
     * 第三方仓库商品同步结果
     */
    public static final Integer COUNT_WAREHOUSE_ITEM_SYNC_RESULT = 50;

    /**
     * 第三方仓库订单推送结果
     */
    public static final Integer COUNT_WAREHOUSE_ORDER_SYNC_RESULT = 80;

    /**
     * 商品商家明细
     */
    public static final Integer COUNT_ITEM_SHELVE_DETAIL_NUM = 20;

    /**
     * 订单黑名单表的数量
     */
    public static final Integer COUNT_TRADE_BLACK_DB_NUM = 50;

    /**
     * 订单黑名单表的数量
     */
    public static final Integer COUNT_TRADE_PACK_SPLIT_DB_NUM = 20;

    /**
     * 订单黑名单表的数量
     */
    public static final Integer COUNT_ORDER_PACK_SPLIT_DB_NUM = 40;

    public static final Integer COUNT_DATA_CUSTOM_PROFILE_DB_NUM = 20;

    public static final Integer COUNT_DATA_CUSTOM_CERTIFICATE_DB_NUM = 20;

    public static final Integer COUNT_DATA_CUSTOM_STOCK_DETAIL_DB_NUM = 200;

    /**
     * 跨境模板表数量
     */
    public static final Integer COUNT_USER_KJ_EXPRESS_TEMPLATE = 2;

    /**
     * 跨境模板地址表数量
     */
    public static final Integer COUNT_KJ_TEMPLATE_ADDRESS = 5;

    /**
     * 财务分销结账明细表数量
     */
    public static final Integer COUNT_DISTRIBUTION_TRADE = 5;

    /**
     * 包材修正表
     */
    private static final Integer COUNT_AI_PACKMA_MODIFIED_DATA = 20;

    /**
     * 公司id，用来取模获得分表号，不进行持久化
     */
    private Long companyId;

    /**
     * 开票方信息
     */
    public static final Integer COUNT_INVOICE_PAYEE_DB_NUM = 50;

    /**
     * 发票日志表
     */
    public static final Integer COUNT_INVOICE_LOG_DB_NUM = 100;

    /**
     * 发票信息
     */
    public static final Integer COUNT_INVOICE_INFO_NUM = 100;

    /**
     * 发票详情
     */
    public static final Integer COUNT_INVOICE_DETAIL_NUM = 200;

    /**
     * 税号规则
     */
    public static final Integer COUNT_TAX_CODE_RULE_NUM = 20;

    /**
     * 税号规则详情
     */
    public static final Integer COUNT_TAX_CODE_RULE_DETAIL_NUM = 100;

    /**
     * 物流跟踪运单号轮询池
     */
    public static final Integer COUNT_LOGISTICS_TRACKING_POLL_POOL_NUM = 50;

    /**
     * 标签规则表
     */
    public static final Integer COUNT_TRADE_TAG_RULE_NUM = 5;


    /**
     * 标签表
     */
    public static final Integer COUNT_TRADE_LABEL_NUM = 100;

    /**
     * 标签记录表
     */
    public static final Integer COUNT_TRADE_LABEL_RECORD_NUM = 100;


    /**
     * 补货任务分表数
     */
    public static final Integer COUNT_REPLENISH_TASK_DB_NUM = 20;

    /**
     * 补货任务明细分表数
     */
    public static final Integer COUNT_REPLENISH_TASK_DETAIL_DB_NUM = 50;

    /**
     * 上架任务分表数
     */
    public static final Integer COUNT_SHELVE_TASK_DB_NUM = 10;

    /**
     * 上架任务明细分表数
     */
    public static final Integer COUNT_SHELVE_TASK_DETAIL_DB_NUM = 20;

    /**
     * 客户管理线上分销商
     */
    public static final Integer COUNT_CM_ONLINE_DISTRIBUTOR_NUM = 5;

    /**
     * 客户管理发货订单记录
     */
    public static final Integer COUNT_CM_CONSIGN_TRADE_RECORD_NUM = 5;

    /**
     * 客户管理充值记录
     */
    public static final Integer COUNT_CM_RECHARGE_RECORD_NUM = 5;

    /**
     * 客户管理店铺关系
     */
    public static final Integer COUNT_CM_SHOP_DISTRIBUTOR_REL_NUM = 5;


    /**
     * 客户管理mysql lock
     */
    public static final Integer COUNT_CM_ID_LOCK_NUM = 5;

    /**
     * 商品包材进货分表数
     */
    public static final Integer COUNT_PACKMA_INGOODS = 3;

    /**
     * 商品包材消耗明细分表数
     */
    public static final Integer COUNT_PACKMA_CONSUME_LOG = 30;

    /**
     * 快速出库单分表数
     */
    public static final Integer COUNT_FAST_OUT_TRADE_NUM = 50;


    /**
     * 库存加工单
     */
    public static final Integer COUNT_STOCK_PROCESS_ORDER_NUM = 10;

    /**
     * 库存加工原料
     */
    public static final Integer COUNT_STOCK_PROCESS_MATERIAL_NUM = 15;

    /**
     * 库存加工单操作日志
     */
    public static final Integer COUNT_STOCK_PROCESS_OP_LOG_NUM = 15;

    /**
     * 库存加工单货位信息
     */
    public static final Integer COUNT_STOCK_PROCESS_GOODS_SECTION_NUM = 20;

    /**
     * 库存仓内加工单
     */
    public static final Integer COUNT_STOCK_PRODUCT_ORDER_NUM = 20;

    /**
     * 库存仓内加工原料
     */
    public static final Integer COUNT_STOCK_PRODUCT_MATERIAL_NUM = 20;

    /**
     * 库存仓内加工单操作日志
     */
    public static final Integer COUNT_STOCK_PRODUCT_GOOD_NUM = 20;

    /**
     * 单据下载关联表
     */
    public static final Integer ORDER_DOWNLOAD_ASSOCIATION = 20;

    /**
     * 售后lock表
     */
    public static final Integer COUNT_AS_LOCK_NUM = 80;

    /**
     * 商品客户关系表
     */
    public static final Integer COUNT_ITEM_CUSTOMER_BRIDGE_NUM = 50;

    /**
     * 商品箱码
     */
    public static final Integer COUNT_ITEM_BOX_NUM = 20;

    /**
     * 箱码单品
     */
    public static final Integer COUNT_ITEM_BOX_SINGLE_NUM = 30;

    /**
     * 智能理货
     */
    public static final Integer COUNT_SMART_TALLY_NUM = 10;

    /**
     * 配置拆分规则
     */
    public static final Integer COUNT_SPLIT_CONFIG_RULE_NUM = 10;

    /**
     * 配置拆分规则明细
     */
    public static final Integer COUNT_SPLIT_CONFIG_RULE_DETAIL_NUM = 20;

    /**
     * 配置拆分商品组合公式
     */
    public static final Integer COUNT_SPLIT_CONFIG_RULE_FORMULA_NUM = 20;
    /**
     * 财务清算单
     */
    public static final Integer COUNT_FINANCE_CLEAR_ORDER_NUM = 30;

    /**
     * 财务清算单明细
     */
    public static final Integer COUNT_FINANCE_CLEAR_ORDER_DETAIL_NUM = 30;

    /**
     * 财务锁
     */
    public static final Integer COUNT_FINANCE_LOCK_NUM = 30;

    /**
     * 财务操作日志
     */
    public static final Integer COUNT_FINANCE_OP_LOG_NUM = 50;

    /**
     * 财务结算单
     */
    public static final Integer COUNT_FINANCE_SETTLE_ORDER_NUM = 30;

    /**
     * 财务结算单明细
     */
    public static final Integer COUNT_FINANCE_SETTLE_ORDER_DETAIL_NUM = 30;

    /**
     * 水洗标
     */
    public static final Integer COUNT_WASH_LABEL_NUM = 30;

    /**
     * 商品关联水洗标
     */
    public static final Integer COUNT_ITEM_WASH_LABEL_BRIDGE_NUM = 30;

    // 暂存区进出记录
    public static final Integer COUNT_WORKING_STORAGE_SECTION_IN_OUT_RECORD_NUM = 500;

    // 配货记录
    public static final Integer COUNT_ALLOCATE_GOODS_RECORD_NUM = 500;


    /**
     * 波次拣选人员表数量
     */
    public static final Integer COUNT_WAVE_PICKER = 50;

    /**
     * 波次人员拣选表分表数
     */
    public static final Integer COUNT_WAVE_STAFF_PICK_REPORT = 50;

    /**
     * 订单人员拣选分表数
     */
    public static final Integer COUNT_ORDER_STAFF_PICK_REPORT = 100;

    public static final Integer COUNT_ITEM_CHANGE_RELATION_NUM = 20;

    public static final Integer COUNT_PRINT_VIEW_NUM = 20;

    /**
     * 波次操作记录分表数量
     */
    public static final Integer COUNT_WAVE_TRACE_NUM = 200;

    /**
     * 备货区拣货商品分表数量
     */
    public static final Integer COUNT_RESERVE_AREA_PICK_GOODS_NUM = 50;

    /**
     * 订单唯一码热销商品分表数量
     */
    public static final Integer COUNT_ORDER_UNIQUE_CODE_HOT_SALE_NUM = 10;

    /**
     * 商品换商品中间表
     */
    public static final Integer COUNT_TRADE_CHANGE_ITEM_MIDDLE_NUM = 20;

    /**
     * 唯一码分拣货位中间表
     */
    public static final Integer COUNT_POSITION_ORDER_UNIQUE_CODE_MIDDLE_NUM = 5;

    /**
     * 唯一码分拣货位详情中间表
     */
    public static final Integer COUNT_POSITION_ORDER_UNIQUE_CODE_DETAIL_MIDDLE_NUM = 10;

    /**
     * 唯一码分拣货位详情中间表
     */
    public static final Integer COUNT_WMS_PERFORMANCE_MIDDLE_NUM = 200;

    /**
     * 商品备忘录分表数量
     */
    public static final Integer COUNT_WAVE_GOODS_MEMORANDUM_NUM = 10;

    /**
     * 仓库位置号分表数量
     */
    public static final Integer COUNT_WAREHOUSE_POSITION_CONFIG = 50;
    /**
     * 未生成波次的配货记录数量
     */
    public static final Integer COUNT_UN_GENERATE_ALLOCATE_GOODS_RECORD = 100;

    /**
     * 热销商品分表数量
     */
    public static final Integer COUNT_TRADE_HOT_ITEM = 20;

    /**
     * 销货单分表数量
     */
    public static final Integer COUNT_TRADE_SALE_TRADE = 20;


    /**
     * 销货单下面的商品快照分表数量
     */
    public static final Integer COUNT_TRADE_SALE_ORDER = 20;


    /**
     * 无销量商品报表分表数量
     */
    public static final Integer COUNT_NO_SALES_REPORT = 16;

    /**
     * 商品销量分表数量
     */
    public static final Integer COUNT_ITEM_SALES = 16;

    /**
     * odps定销量快照分表数量
     */
    public static final Integer ODPS_ORDER_SNAP = 10;

    /**
     * 支付单分表数量
     */
    public static final Integer COUNT_TRADE_PAY = 500;

    /**
     * 售后包裹查询日志表
     */
    public static final Integer AS_PACKAGE_QUERY_LOG = 80;

    /**
     * 订单成本扩展表分表数量
     */
    public static final Integer REPORT_TRADE_EXTRA = 5;
    public static final Integer REPORT_ORDER_EXTRA = 10;

    /**
     * 拣货单打印日志分表数
     */
    public static final Integer PICKER_PRINT_LOG = 30;

    /**
     * 拣货单打印日志详情分表数
     */
    public static final Integer PICKER_PRINT_LOG_DETAIL = 30;


    /**
     * 销货单财务收款记录明细分表
     */
    public static final Integer COUNT_SALE_TRADE_FINANCE_DETAIL = 20;

    /**
     * 单据日志分表数
     */
    public static final Integer COUNT_RECEIPT_TRACE_DB_NO = 100;

    /**
     * 销货单无线打印记录表
     */
    public static final Integer COUNT_SALES_WIRELESS_PRINT = 10;
    /**
     * 商家编码无线打印记录表
     */
    public static final Integer COUNT_WIRELESS_PRINT_MERCHANT = 10;
    /**
     * 跨境无线打印记录表
     */
    public static final Integer COUNT_WIRELESS_PRINT_AUTO_ESCROW = 10;

    /**
     * 分单日志表分表数
     */
    public static final Integer COUNT_PRINT_DIVIDE_LOG = 5;

    /**
     * 打印任务数据分表
     */
    public static final Integer COUNT_PRINT_TASK_DATA = 10;

    /**
     * 打印任务商家编码详情数据分表
     */
    public static final Integer COUNT_MERCHANT_TASK_DETAIL = 50;

    /**
     * 客户销售价规则明细
     */
    public static final Integer COUNT_CUSTOMER_SALE_PRICE_RULE_DETAIL = 10;

    /**
     * 一品多码分表
     */
    public static final Integer COUNT_ONE_ITEM_MULTI_CODE = 20;

    /**
     * 扩展配置（员工级别）
     */
    public static final Integer COUNT_CUSTOMER_EXTEND = 10;

    /**
     * 代发商品记录表数
     */
    public static final Integer COUNT_DMS_ITEM_PUSH_RECORD_NUM = 10;

    /**
     * 支付单消费记录表
     */
    public static final Integer COUNT_TRADE_PAY_CONSUME_LOG_NUM = 10;

    /**
     * 订单自动标记规则匹配记录分表数
     */
    public static final Integer COUNT_TRADE_TAG_RULE_MATCH_LOG = 100;


    /**
     * 采购唯一码明细分表数
     */
    public static final Integer COUNT_PURCHASE_ORDER_UNIQUE_CODE_NUM = 50;
    /**
     * 商品标签分表数
     */
    public static final Integer COUNT_ITEM_TAG_NUM = 5;

    /**
     * 商品标签关联分表数
     */
    public static final Integer COUNT_ITEM_TAG_RELATION_NUM = 20;

    /**
     * 任务分表数
     */
    public static final Integer COUNT_TASK_NUM = 5;

    /**
     * 子任务分表数
     */
    public static final Integer COUNT_TASK_SUB_NUM = 20;

    /**
     * 用户物流商分表数
     */
    private static final Integer COUNT_USER_LOGISTICS_PROVIDER_NUM = 2;

    /**
     * 用户物流渠道分表数
     */
    private static final Integer COUNT_USER_LOGISTICS_CHANNEL_NUM = 2;

    /**
     * 组包分表数
     */
    private static final int COUNT_TRADE_COMBINE_PARCEL_NUM = 2;

    /**
     * 小包分表数
     */
    private static final int COUNT_TRADE_PARCEL_NUM = 2;

    /**
     * 唯一码解绑日志分表数
     */
    private static final int ORDER_UNIQUE_UNBOUND_LOG_NUM = 10;

    /**
     * 下架单分表数
     */
    public static final Integer COUNT_UNSHELVE_TAG_NUM = 50;
    /**
     * 类目属性商品桥接表分表数
     */
    public static final Integer COUNT_PROPERTY_SEGMENT_BRIDGE_NUM = 20;

    /**
     * 采购平台商品关联表分表数
     */
    public static final Integer COUNT_PURCHASE_PLATFORM_PRODUCT_NUM = 20;

    /**
     * 采购平台订单关联表分表数
     */
    public static final Integer COUNT_PURCHASE_PLATFORM_ORDER_NUM = 20;
    /**
     * 库存修改记录分表数
     */
    public static final Integer COUNT_STOCK_CHANGE_LOG = 50;

    /**
     * 快销库存分表数量
     */
    public static final Integer COUNT_FAST_IN_OUT_STOCK_NUM = 30;

    /**
     * 快销库存日志分表数量
     */
    public static final Integer COUNT_FAST_IN_OUT_STOCK_LOG_NUM = 100;

    public static final Integer CUSTOM_WORKLOAD_SUPPLEMENT_LOG_NUM = 10;

    /**
     * 唯一码打印日志分表数
     */
    public static final Integer UNIQUE_PRINT_LOG_DATA = 10;
    /**
     * 唯一码打印日志分表数
     */
    public static final Integer UNIQUE_PRINT_LOG_DETAIL_DATA = 50;

    /**
     * 唯一码标签分表数
     */
    public static final Integer UNIQUE_TAG_DATA = 5;

    /**
     * 审计日志分表数
     */
    public static final Integer COUNT_AUDIT_RECORD_NUM = 30;

    /**
     * 拣选改码分表数
     */
    public static final Integer COUNT_PICK_CHANGE_CODE_NUM = 5;


    /**
     * 自动标签规则商品信息表
     */
    public static final Integer TRADE_TAG_RULE_ITEM_NUM = 5;

    /**
     * 自动标签规则商品信息版本控制表
     */
    public static final Integer TRADE_TAG_RULE_CONTROL_NUM = 5;

    /**
     * 订单商品批次信息表
     */
    public static final Integer ORDER_PRODUCT_NUM = 200;


    /**
     * 装袋记录表
     */
    public static final Integer PACKING_GOODS_RECORD_NUM = 10;
    /**
     * 用户电子模板配置
     */
    public static final Integer USER_EXPRESS_TEMPLATE_CONFIG_NUM = 5;
    /**
     * 打印配置
     */
    public static final Integer PRINT_CONFIG_NEW_NUM = 20;

    /**
     * 统计同步记录表
     */
    public static final Integer TRADE_STAT_SYNC_NUM = 100;
    /**
     * 异常同步表
     */
    public static final Integer TRADE_EXCEPT_SYNC_NUM = 100;


    /**
     * 样式模板表
     */
    public static final Integer USER_STYLE_EXPRESS_TEMPLATE_NUM = 2;
    /**
     * 快递公司表
     */
    public static final Integer USER_LOGISTICS_COMPANY_NUM = 2;
    /**
     * 快递公司模板表
     */
    public static final Integer USER_LOGISTICS_COMPANY_TEMPLATE_NUM = 2;

    public static final Integer COUNT_TRADE_MERGE_ITEM_GROUP_NUM = 100;

    public static final Integer COUNT_TRADE_ITEM_GROUP_NUM = 100;

    /**
     * 周期购商品分表数
     */
    public static final Integer COUNT_PERIOD_ITEM_NUM = 10;

    /**
     * 周期购商品明细分表数
     */
    public static final Integer COUNT_PERIOD_ITEM_DETAIL_NUM = 20;

    /**
     * 操作日志分表数
     */
    public static final Integer COUNT_ITEM_OPERATE_LOG_NUM = 10;

    /**
     * 订单业务规则相关版本分表数
     */
    public static final Integer COUNT_TRADE_VERSION_NUM = 1000;

    /**
     * 订单规则基本信息分表数
     */
    public static final Integer COUNT_TRADE_RULE_NUM = 50;
    /**
     * 订单规则基本信息历史记录分表数
     */
    public static final Integer COUNT_TRADE_RULE_HISTORY_NUM = 100;
    /**
     * 订单规则条件分表数
     */
    public static final Integer COUNT_TRADE_RULE_CONDITION_NUM = 100;
    /**
     * 订单规则基本信息历史记录分表数
     */
    public static final Integer COUNT_TRADE_RULE_CONDITION_HISTORY_NUM = 200;
    /**
     * 订单规则过滤条件分表数
     */
    public static final Integer COUNT_TRADE_RULE_CONDITION_FILTER_NUM = 50;
    /**
     * 订单规则过滤条件历史记录分表数
     */
    public static final Integer COUNT_TRADE_RULE_CONDITION_FILTER_HISTORY_NUM = 100;

    /**
     * 订单规则条件明细分表数
     */
    public static final Integer COUNT_TRADE_RULE_CONDITION_DETAIL_NUM = 100;
    /**
     * 订单规则条件明细历史记录分表数
     */
    public static final Integer COUNT_TRADE_RULE_CONDITION_DETAIL_HISTORY_NUM = 200;

    /**
     * 规则匹配日志分表数
     */
    public static final Integer COUNT_TRADE_RULE_MATCH_LOG_NUM = 1000;

    /**
     * 唯一码扩展表分表数
     */
    public static final Integer UNIQUE_CODE_EXTEND_NUM = 100;

    /**
     * 打印商品日志分表的数量
     */
    public static final Integer COUNT_PRINT_ITEM_LOG_NUM = 20;

    /**
     * 赠品规则分表数
     */
    public static final Integer GIFT_RULE_NUM = 100;

    /**
     * 赠品规则扩展信息分表数
     */
    public static final Integer GIFT_RULE_EXTEND_NUM = 200;

    /**
     * 赠品规则商品分表数
     */
    public static final Integer GIFT_RULE_ITEM_NUM = 200;


    public static final Integer PRINT_MERCHANT_CODE_NUM_LOG_NUM = 20;


    /**
     * 赠品赠送记录分表数
     */
    public static final Integer GIFT_RECORD_LOG_NUM = 1000;
    /**
     * 原始打印数据分表数
     */
    public static final Integer PRINT_FIELD_VALUES_LOG_NUM = 50;


    /**
     * 采购与订单关联分表的数量
     */
    public static final Integer COUNT_ASSO_PURCHASE_ORDER_NUM = 10;

    /**
     * 标签对账关联表数量
     */
    public static final Integer COUNT_ASSO_LABEL_RECONCILIATION_NUM = 5;



    public static final Integer COUNT_LOGISTICS_WAVE_RELATION_NUM=20;

    /**
     * 机器人推送记录分表数量
     */
    public static final Integer COUNT_ROBOT_PUSH_RECORD_NUM = 4;


    /**
     * 机器人推送记录分表数量
     */
    public static final Integer COUNT_ROBOT_PUSH_RECORD_DETAIL_NUM = 4;



    /**
     * rds的库号，设置公司的数据存储在哪个rds上
     */
    private Integer rdsNo;

    /**
     * 订单的分表编号
     */
    private Integer tradeDbNo;

    /**
     * 子订单的分表编号
     */
    private Integer orderDbNo;

    /**
     * 订单异常的分表编号
     */
    private Integer tradeExceptDbNo;

    private Integer orderExceptDbNo;

    /**
     * 订单相关优先级分表号
     */
    private Integer tradePriorityDbNo;

    /**
     * 发货缓存分表号
     */
    private Integer consignCacheDbNo;

    /**
     * 记录发送给三方仓数据分表号
     */
    private Integer tradeParty3DataDbNo;

    /**
     * 子订单换商品日志表分表号
     */
    private Integer itemChangeLogDbNo;

    /**
     * order操作记录
     */
    private Integer orderModifyLogDbNo;

    /**
     * 换商品的记录
     */
    private Integer orderReplaceLogDbNo;

    /**
     * 订单地址变更记录表分表号
     */
    private Integer addressChangeLogDbNo;

    /**
     * 订单系统备注存储表分表号
     */
    private Integer sysMemoLogDbNo;

    /**
     * 订单操作记录分表号
     */
    private Integer tradeTraceDbNo;

    /**
     * 配置更新操作记录分表号
     */
    private Integer configTraceDbno;

    /**
     * 商品操作记录分表号
     */
    private Integer itemTraceDbNo;

    /**
     * 短信交易的分表号
     */
    private Integer smsTxnDbNo;

    /**
     * 库存调整的日志表
     */
    private Integer stockAdjustLogDbNo;

    /**
     * 商品item的分表编号
     */
    private Integer ItemDbNo;

    /**
     * 商品Sku的分表编号
     */
    private Integer SkuDbNo;

    /**
     * 系统商品item的分表编号
     */
    private Integer dmjItemDbNo;

    /**
     * 系统商品Sku的分表编号
     */
    private Integer dmjSkuDbNo;

    /**
     * 系统商品供应商商品关联表
     */
    private Integer itemSupplierBridgeDbNo;

    /**
     * 用户电子面单模板分表编号
     */
    private Integer userWlbExpressTemplateDbNo;

    /**
     * 用户电子面单自定义模板分表编号
     */
    private Integer userWlbCustomTemplateDbNo;

    /**
     * 样式电子面单模板分表编号
     */
    private Integer userStyleExpressTemplateDbNo;
    /**
     * 快递公司分表编号
     */
    private Integer userLogisticsCompanyDbNo;
    /**
     * 快递公司模板分表编号
     */
    private Integer userLogisticsCompanyTemplateDbNo;

    /**
     * 用户拿货单分表编号
     */
    private Integer userAssemblyTemplateDbNo;

    /**
     * 用户发货模板分表编号
     */
    private Integer userDeliverTemplateDbNo;

    /**
     * 用户拣货模板分表编号
     */
    private Integer userPickerTemplateDbNo;

    /**
     * 用户拿货模板分表编号
     */
    private Integer userGetterTemplateDbNo;

    /**
     * 用户快递模板分表编号
     */
    private Integer userExpressTemplateDbNo;

    /**
     * 用户单据模板分表编号
     */
    private Integer userInvoicesTemplateDbNo;

    /**
     * 用户运费设置模版分表编号
     */
    private Integer freightTemplateDbNo;

    /**
     * 用户打印订单日志分表编号
     */
    private Integer printTradeLogDbNo;

    private Integer transpondPrintLogDbNo;

    /**
     * 订单一单多包打印记录分表号
     */
    private Integer multiPacksPrintTradeLogDbNo;

    /**
     * 用户打印订单统计日志分表编号
     */
    private Integer printOrderShipCountDbNo;

    /**
     * 用户自定义打印分表编号
     */
    private Integer userCustomPrintCountDbNo;


    private Integer preSellItemDbNo;

    /**
     * 短信通知日志的分表号
     */
    private Integer notificationLogDbNo;

    /**
     * 电子面单号记录的分表号
     */
    private Integer outSidPoolDbNo;
    /**
     * 原始打印数据的分表号
     */
    private Integer printFieldValuesLogDbNo;

    /**
     * 电子面单号回收的分表号
     */
    private Integer outSidRecoverDbNo;

    /**
     * 电子面单的网点与订单的对应关系的分表号
     */
    private Integer wlbAddressTradeLinkDbNo;

    /**
     * 操作日志分表号
     */
    private Integer actionLogDbNo;

    /**
     * 短信的创建订单的分表号
     */
    private Integer smsCreatedTradeDbNo;

    /**
     * 快递智能匹配的分表号
     */
    private Integer expressSmartMatchDbNo;
    /**
     * 运单号使用规则的分表号*
     */
    private Integer waybillUsingRuleDbNo;
    /**
     * 运单号使用规则详情的分表号*
     */
    private Integer waybillUsingRuleDetailDbNo;
    /**
     * 运单号使用规则记录的分表号*
     */
    private Integer waybillUsingRuleLogDbNo;
    /**
     * 运单号使用规则充值记录的分表号*
     */
    private Integer waybillTopUpLogDbNo;

    /**
     * 商品库存的分表号
     */
    private Integer itemStockDbNo;

    /**
     * 库存出入库统计日志标表的分表号
     */
    private Integer itemStockLogDbNo;

    /**
     * 预售库存分表号
     */
    private Integer stockPresellDbNo;
    /**
     * 预留库存分表号
     */
    private Integer stockReserveDbNo;
    /**
     * stock_upload_rule 规则表的 分表号
     */
    private Integer stockUploadRuleDbNo;

    /**
     * 库存锁定详情 分表号
     */
    private Integer stockLockDetailDbNo;

    /**
     * 本地保存的和平台库存对应的镜像表分表号
     */
    private Integer stockInOutRecordDbNo;
    /**
     * 本地保存的和平台库存对应的镜像表分表号
     */
    private Integer stockOrderRecordDbNo;

    /**
     * 操作日志分表号
     */
    private Integer opLogDbNo;

    private Integer itemBridgeNo;

    private Integer SkuBridgeNo;

    /**
     * 售后工单的分表号
     */
    private Integer workOrderDbNo;

    /**
     * 售后工单对应的退货补发的分表号
     */
    private Integer asReissueOrRefundDbNo;

    /**
     * 售后工单对应的商品快照的分表号
     */
    private Integer asItemSnapshotDbNo;

    /**
     * 售后工单对应的销退入库分表号
     */
    private Integer asRefundWarehouseDbNo;

    /**
     * 售后工单的留言板的分表号
     */
    private Integer asMsgBoardDbNo;

    /**
     * 售后工单的操作日志的分表号
     */
    private Integer asOperateLogDbNo;

    /**
     * 售后工单的备注的分表号
     */
    private Integer asWoRemarkDbNo;

    /**
     * 售后工单的自定义售后原因的分表号
     */
    private Integer asReasonDbNo;

    /**
     * 仓库绑定模版分表号
     */
    private Integer warehouseTemplateDbNo;

    /**
     * 快递模版下网点分表号
     */
    private Integer templateBranchDbNo;

    /**
     * 快递模版下网点的地址分表号
     */
    private Integer templateBranchAddressDbNo;

    /**
     * 平台分类分表号
     */
    private Integer sellerCatDbNo;

    /**
     * 店铺分类分表号
     */
    private Integer shopSellerCatDbNo;

    /**
     * 属性表分类分表号
     */
    private Integer skuErpDictOtherDbNo;

    /**
     * 采购单分表号
     */
    private Integer purchaseOrderDbNo;

    /**
     * 采购单详情分表号
     */
    private Integer purchaseOrderDetailDbNo;

    /**
     * 采购单唯一码明细分表号
     */
    private Integer purchaseOrderUniqueCodeDbNo;

    /**
     * 机器人推送记录分表号
     */
    private Integer robotPushRecordDbNo;

    /**
     * 机器人推送记录明细分表号
     */
    private Integer robotPushRecordDetailDbNo;


    /**
     * 入库单分表号
     */
    private Integer warehouseEntryDbNo;

    /**
     * 采退建议分表号
     */
    private Integer smartPurchaseReturnDbNo;

    /**
     * 唯一码收货单关联表
     */
    private Integer warehouseEntryWaveUniqueCodeDbNo;

    /**
     * 唯一码业务关联表（交易库）
     */
    private Integer wmsBusinessUniqueCodeDbNo;


    /**
     * 入库单详情分表号
     */
    private Integer warehouseEntryDetailDbNo;

    /**
     * 上架单分表号
     */
    private Integer shelveOrderDbNo;

    /**
     * 上架单明细分表号
     */
    private Integer shelveOrderDetailDbNo;

    /**
     * 上架单-入库单关联表 表号
     */
    private Integer assoShelveWarehouseEntryDbNo;
    /**
     * 上架单-入库单关联表 表号
     */
    private Integer assoBusinessDocumentDbNO;

    /**
     * 文件存储信息的分表号
     */
    private Integer storageLogDbNo;

    /**
     * 财务收款客户的分表号
     */
    private Integer payeeFinanceDbNo;

    /**
     * 财务收支账户的分表号
     */
    private Integer payerFinanceDbNo;

    /**
     * 财务科目的分表号
     */
    private Integer subjectFinanceDbNo;

    /**
     * 财务资金流水的分表号
     */
    private Integer cashJournalFinanceDbNo;

    /**
     * 财务运单导入的分表号
     */
    private Integer importExpressFinanceDbNo;

    private Integer tradeExpressFinanceDbNo;

    /**
     * 财务支付宝账单的分表号
     */
    private Integer alipayBillFinanceDbNo;

    /**
     * 财务支付宝对账单的分表号
     */
    private Integer alipayBillCompFinanceDbNo;

    /**
     * 货位商品关联分表号
     */
    private Integer assoGoodsSectionSkuDbNo;

    /**
     * 库区警戒分表号
     */
    private Integer stockRegionAlertDbNo;

    /**
     * 商品盘点记录分表号
     */
    private Integer skuCheckRecordDbNo;

    /**
     * 货位商品锁定记录分表号
     */
    private Integer goodsSectionSkuLockRecordDbNo;

    /**
     * 货位子订单关联记录分表号
     */
    private Integer goodsSectionOrderRecordDbNo;

    /**
     * 货位商品出入记录分表号
     */
    private Integer goodsSectionInOutRecordDbNo;

    /**
     * 货位库存操作日志分表号
     */
    private Integer goodsSectionInventoryOpLogDbNo;


    /**
     * 移库任务分表号
     */
    private Integer skuAllocateTaskDbNo;

    /**
     * 移库出库记录分表号
     */
    private Integer skuAllocateFromRecordDbNo;

    /**
     * 移库入库记录分表号
     */
    private Integer skuAllocateToRecordDbNo;

    /**
     * 京东店铺所关联的快递公司的分表号
     */
    private Integer userLogisticsCompaniesJdDbNo;

    /**
     * 货位库存导入记录分表号
     */
    private Integer goodsSectionInventoryImportDbNo;

    /**
     * 交易赠品促销活动分表号
     */
    private Integer giftPromotionDbNo;

    /**
     * 交易赠品促销规则分表号
     */
    private Integer giftPromotionRuleDbNo;

    /**
     * 交易赠品促销规则的对应商品分表号
     */
    private Integer giftPromotionRuleSellerItemDbNo;

    /**
     * 交易赠品促销规则的对应赠品分表号
     */
    private Integer giftPromotionRuleGiveItemDbNo;

    /**
     * 交易赠品促销规则的对应指定商品分表号
     */
    private Integer giftPromotionRuleGiveItemRangeDbNo;

    /**
     * 交易赠品与订单的匹配记录日志分表号
     */
    private Integer giftPromotionMatchTradeLogDbNo;

    /**
     * 赠品按标签赠送，标签配置的分表号
     */
    private Integer giftPromotionRuleTagDbNo;

    /**
     * 组合商品关系表的分表号
     */
    private Integer itemSuiteBridgeDbNo;

    /**
     * 订单临时表的分表编号
     */
    private Integer tradeTempDbNo;

    /**
     * 智能理货的分表编号
     */
    private Integer smartTallyDbNo;

    /**
     * 订单报表分表号
     */
    private Integer reportTradeDbNo;

    /**
     * 订单包装验货报表分表号
     */
    private Integer reportTradePackDbNo;

    /**
     * 商品报表分表号
     */
    private Integer reportOrderDbNo;

    /**
     * 报表库交易分表
     */
    private Integer dataTradeDbNo;

    /**
     * 报表库order分表
     */
    private Integer dataOrderDbNo;

    /**
     * 报表库stock_sum分表
     */
    private Integer dataStockSumDbNo;

    /**
     * 报表库 商品退货 report_refund表
     */
    private Integer dataReportRefundDbNo;

    /**
     * 报表库item_week_month_sales分表
     */
    private Integer dataItemWeekMonthSalesDbNo;

    /**
     * 报表库relation_cids分表
     */
    private Integer dataRelationCidsDbNo;

    /**
     * 售后报表分表号
     */
    private Integer reportAftersaleDbNo;
    /**
     * 订单月销量报表分表号
     */
    private Integer reportMonthTradeDbNo;

    /**
     * 库存出入库日报表号
     */
    private Integer stockDayReportDbNo;

    /**
     * 售后分析报表分表号
     */
    private Integer reportAsAnalyseDbNo;

    /**
     * 店铺销量报表分表号
     */
    private Integer reportShopDbNo;

    /**
     * 商品: 品牌分表号
     */
    private Integer brandDbNo;

    /**
     * 发货方式报表分标号
     */
    private Integer reportSendGoodsTypeDbNo;

    /**
     * 商品警戒值记录表
     */
    private Integer itemWarnDbNo;

    /**
     * pda拣货订单分表号
     */
    private Integer pdaPickTradeDbNo;

    /**
     * pda拣货商品分表号
     */
    private Integer pdaPickItemDbNo;

    /**
     * 审核绩效的分标号
     */
    private Integer reportTradeAuditDbNo;

    /**
     * 称重绩效的分表好
     */
    private Integer reportTradeWeightDbNo;

    /**
     * 面单回收池的分表号
     */
    private Integer outSidRecyclePoolDbNo;

    /**
     * 返库任务分表号
     */
    private Integer depotTaskDbNo;

    /**
     * 返库记录分表号
     */
    private Integer depotRecordDbNo;

    /**
     * 返库订单明细分表
     */
    private Integer depotOrderDetailDbNo;

    private Integer reportPurchaseDbNo;

    private Integer tradeSolrDbNo;

    private Integer wavePickingCodeDbNo;

    /**
     * 波次后置分拣分表号
     */
    private Integer waveSortingDbNo;

    /**
     * 波次分拣明细分表号
     */
    private Integer waveSortingDetailDbNo;

    /**
     * 采购退货分表号
     */
    private Integer purchaseReturnDbNo;

    /**
     * 采购退货明细分表号
     */
    private Integer purchaseReturnDetailDbNo;

    /**
     * 仓储业务锁定分表号
     */
    private Integer wmsBusiLockDbNo;

    /**
     * 商品第三方关联表
     */
    private Integer assItemDbNo;

    /**
     * sku 第三方关联表
     */
    private Integer assSkuDbNo;

    /**
     * 订单第三方关联表
     */
    private Integer assOrderDbNo;

    /**
     * 第三方仓库日志表分表号
     */
    private Integer party3WarehouseLogDbNo;

    /**
     * 第三方仓库商品同步结果分表号
     */
    private Integer warehouseItemSyncResultDbNo;

    /**
     * 第三方仓库订单同步结果分表号
     */
    private Integer warehouseOrderSyncResultDbNo;


    /**
     * 商品上架明细分表号
     */
    private Integer itemShelveDetailDbNo;

    /**
     * 订单的分表编号
     */
    private Integer tradeBlackDbNo;

    /**
     * 打包拆分分表编号
     */
    private Integer tradePackSplitDbNo;

    /**
     * 打包拆分分表编号
     */
    private Integer orderPackSplitDbNo;

    private Integer dataCustomProfileDbNo;

    private Integer dataCustomCertificateDbNo;

    private Integer dataCustomStockDetailDbNo;

    /**
     * 波次分表号
     */
    public Integer waveDbNo;

    /**
     * 波次拣选分表号
     */
    public Integer wavePickingDbNo;

    /**
     * 波次订单关联表，分表号
     */
    public Integer assoWaveTradeDbNo;

    /**
     * 商品上下架
     */
    public Integer shelfTaskNo;

    /**
     * 商品上下架日志
     */
    public Integer shelfTaskLogNo;

    /**
     * 开票方信息分表号
     */
    private Integer invoicePayeeDbNo;

    /**
     * 开票方日志
     */
    private Integer invoiceLogDbNo;

    /**
     * 发票信息
     */
    private Integer invoiceInfoDbNo;

    /**
     * 发票详情
     */
    private Integer invoiceDetailDbNo;

    /**
     * 税号规则
     */
    private Integer taxCodeRuleDbNo;

    /**
     * 税号规则详情
     */
    private Integer taxCodeRuleDetailDbNo;

    /**
     * 运单号物流跟踪轮询池
     *
     * @return
     */
    private Integer logisticsTrackingPollPoolDbNo;

    /**
     * 标签规则分表
     */
    private Integer tradeTagRuleDbNo;

    /**
     * 标签表分表
     */
    private Integer tradeLabelDbNo;


    /**
     * 标签记录表分表
     */
    private Integer tradeLabelRecordDbNo;


    /**
     * 快速出库单分表
     */
    private Integer fastOutTradeDbNo;

    /**
     * 补货任务分表号
     */
    private Integer replenishTaskDbNo;

    /**
     * 补货任务明细分表号
     */
    private Integer replenishTaskDetailDbNo;

    /**
     * 上架任务分表号
     */
    private Integer shelveTaskDbNo;

    /**
     * 上架任务明细分表号
     */
    private Integer shelveTaskDetailDbNo;

    /**
     * 包材进货分表数
     */
    private Integer packmaIngoodsDbNo;

    /**
     * 包材消耗记录分表述
     */
    private Integer packmaConsumeLogDbNo;

    /**
     * 清算单
     */
    private Integer clearOrderDbNo;

    /**
     * 结算单
     */
    private Integer settleOrderDbNo;

    /**
     * 清算单明细
     */
    private Integer clearOrderDetailDbNo;

    /**
     * 结算单明细
     */
    private Integer settleOrderDetailDbNo;

    /**
     * 财务lock锁
     */
    private Integer financeLockDbNo;

    /**
     * 财务操作日志
     */
    private Integer financeOpLogDbNo;

    private Integer allocateGoodsRecordDbNo;

    private Integer printViewDbNo;

    /**
     * 波次操作记录分表号
     */
    private Integer waveTraceDbNo;

    /**
     * 备货区支持拣选商品分表号
     */
    private Integer reserveAreaPickGoodsDbNo;

    /**
     * 订单唯一码热销商品
     */
    private Integer orderUniqueCodeHotSaleDbNo;

    /**
     * 唯一码标签表
     */
    private Integer waveUniqueCodeTagDbNo;

    /**
     * 订单换商品中间表
     */
    private Integer tradeChangeItemMiddleDbNo;

    /**
     * 订单唯一码分拣货位中间表
     */
    private Integer positionOrderUniqueCodeMiddleDbNo;

    /**
     * 订单唯一码分拣货位详情中间表
     */
    private Integer positionOrderUniqueCodeDetailMiddleDbNo;

    /**
     * 包材修正表
     */
    private Integer aiPackmaModifiedDataDbNo;

    /**
     * 仓内绩效中间表
     */
    private Integer wmsPerformanceMiddleDbNo;

    /**
     * 商品备忘录
     */
    private Integer waveGoodsMemorandumDbNo;

    /**
     * 仓库位置号配置分表号
     */
    private Integer warehousePositionConfigDbNo;

    /**
     * 热销商品表号
     */
    private Integer hotItemDbNo;

    /**
     * 销货单表号
     */
    private Integer saleTradeDbNo;


    /**
     * 销货单商品
     */
    private Integer saleOrderDbNo;

    /**
     * odps定销量快照表号
     */
    private Integer odpsOrderSnapDbNo;

    /**
     * 订单成本扩展表分表号
     */
    private Integer reportTradeExtraDbNo;
    private Integer reportOrderExtraDbNo;

    private Integer saleTradeFinanceDetailDbNo;
    /**
     * 销货单无线打印记录表
     */
    private Integer salesWirelessPrintDbNo;
    /**
     * 商家编码无线打印记录表
     */
    private Integer wirelessPrintMerchantDbNo;
    /**
     * 全托管无线打印记录表
     */
    private Integer wirelessPrintAutoEscrowDbNo;

    /**
     * 客户销售价规则明细
     */
    private Integer customerSalePriceRuleDetailDbNo;

    /**
     * 代发商品记录表号
     */
    private Integer dmsItemPushRecordDbNo;


    private Integer tradePayConsumeLogDbNo;

    /**
     * 采购平台商品分表号
     */
    private Integer purchasePlatFormProductDbNo;

    /**
     * 采购平台订单分表号
     */
    private Integer purchasePlatFormOrderDbNo;

    /**
     * 库存改变日志表
     */
    private Integer stockChangeLog;

    /**
     * 自动标签规则商品信息表
     */
    private Integer tradeTagRuleItemDbNo;

    /**
     * 自动标签规则商品信息版本控制表
     */
    private Integer tradeTagRuleControlDbNo;

    /**
     * 商品批次分表号
     */
    private Integer itemBatchDbNo;

    /**
     * 装袋任务记录表
     */
    private Integer packingGoodsRecordDbNo;

    /**
     * 用户电子模板配置
     */
    private Integer userExpressTemplateConfigDbNo;
    /**
     * 新版打印配置类
     */
    private Integer printConfigNewDbNo;

    /**
     * 唯一码标签表
     */
    private Integer uniqueCodeExtendDbNo;


    /**
     * 云打印
     */
    private Integer userCloudPrinterDbNo;
    /**
     * 未生成波次的配货记录表
     */
    private Integer unGenerateAllocateGoodsRecordDbNo;

    /**
     * 合单商品分组
     */
    private Integer tradeMergeItemGroupDbNo;

    private Integer tradeItemGroupDbNo;

    /**
     * 用户打印商品日志分表编号
     */
    private Integer printItemLogDbNo;

    /**
     * 仓储标签列表分表号
     */
    private Integer WmsLabelDbNo;

    /**
     * 仓储标签关联表分表号
     */
    private Integer WmsLabelBridgeDbNo;

    private Integer periodItemDbNo;

    private Integer periodItemDetailDbNo;

    private Integer itemOperateLogDbNo;

    /**
     * 订单业务规则相关版本分表号
     */
    private Integer tradeVersionDbNo;

    /**
     * 订单规则基本信息分表号
     */
    private Integer tradeRuleDbNo;
    /**
     * 订单规则基本信息历史记录分表号
     */
    private Integer tradeRuleHistoryDbNo;
    /**
     * 订单规则条件分表号
     */
    private Integer tradeRuleConditionDbNo;
    /**
     * 订单规则条件历史记录分表号
     */
    private Integer tradeRuleConditionHistoryDbNo;
    /**
     * 订单规则过滤条件分表号
     */
    private Integer tradeRuleConditionFilterDbNo;
    /**
     *
     */
    private Integer tradeRuleConditionFilterHistoryDbNo;
    /**
     * 规则条件明细分表号
     */
    private Integer tradeRuleConditionDetailDbNo;
    /**
     * 规则条件明细分表号历史记录分表号
     */
    private Integer tradeRuleConditionDetailHistoryDbNo;
    /**
     * 规则匹配日志分表号
     */
    private Integer tradeRuleMatchLogDbNo;

    /**
     * 波次逻辑关系分表号
     */
    private Integer logisticsWaveRelationDbNo;

    /**
     * 拣选改码记录分表号
     * @param stockChangeLog
     */
    private Integer pickChangeCodeRecordDbNo;


    public void setStockChangeLog(Integer stockChangeLog) {
        this.stockChangeLog = stockChangeLog;
    }

    public Integer getStockChangeLog() {
        if (null == stockChangeLog) {
            stockChangeLog = generateDbNo(companyId, COUNT_STOCK_CHANGE_LOG);
        }
        return stockChangeLog;
    }

    private Integer auditRecordDbNo;

    public Integer getWarehouseOrderSyncResultDbNo() {
        if (warehouseOrderSyncResultDbNo == null) {
            return 0;
        }
        return warehouseOrderSyncResultDbNo;
    }

    public void setWarehouseOrderSyncResultDbNo(Integer warehouseOrderSyncResultDbNo) {
        this.warehouseOrderSyncResultDbNo = warehouseOrderSyncResultDbNo;
    }

    public Integer getInvoiceDetailDbNo() {
        if (this.invoiceDetailDbNo == null)
            return Integer.valueOf(0);

        return invoiceDetailDbNo;
    }

    public void setInvoiceDetailDbNo(Integer invoiceDetailDbNo) {
        this.invoiceDetailDbNo = invoiceDetailDbNo;
    }

    public Integer getInvoicePayeeDbNo() {
        if (this.invoicePayeeDbNo == null)
            return Integer.valueOf(0);

        return invoicePayeeDbNo;
    }

    public void setInvoicePayeeDbNo(Integer invoicePayeeDbNo) {
        this.invoicePayeeDbNo = invoicePayeeDbNo;
    }

    public Integer getInvoiceLogDbNo() {
        if (this.invoiceLogDbNo == null)
            return Integer.valueOf(0);

        return invoiceLogDbNo;
    }

    public void setInvoiceLogDbNo(Integer invoiceLogDbNo) {
        this.invoiceLogDbNo = invoiceLogDbNo;
    }

    public Integer getInvoiceInfoDbNo() {
        if (this.invoiceInfoDbNo == null)
            return Integer.valueOf(0);

        return invoiceInfoDbNo;
    }

    public void setInvoiceInfoDbNo(Integer invoiceInfoDbNo) {
        this.invoiceInfoDbNo = invoiceInfoDbNo;
    }

    public Integer getTaxCodeRuleDbNo() {
        if (this.taxCodeRuleDbNo == null)
            return Integer.valueOf(0);

        return taxCodeRuleDbNo;
    }

    public void setTaxCodeRuleDbNo(Integer taxCodeRuleDbNo) {
        this.taxCodeRuleDbNo = taxCodeRuleDbNo;
    }

    public Integer getTaxCodeRuleDetailDbNo() {
        if (this.taxCodeRuleDetailDbNo == null)
            return Integer.valueOf(0);

        return taxCodeRuleDetailDbNo;
    }

    public void setTaxCodeRuleDetailDbNo(Integer taxCodeRuleDetailDbNo) {
        this.taxCodeRuleDetailDbNo = taxCodeRuleDetailDbNo;
    }

    /**
     * 商品店铺分销售价关系表
     */
    private Integer itemSalePriceBrigeDbNo;

    /**
     * 跨境模板表
     */
    private Integer userKjExpressTemplateDbNo;

    /**
     * 跨境模板地址
     */
    private Integer kjTemplateAddressDbNo;

    /**
     * 财务分销结账
     */
    private Integer distributionTradeDbNo;

    /**
     * 客户管理线上分销商
     */
    private Integer cmOnlineDistributorDbNo;

    /**
     * 客户管理发货订单记录
     */
    private Integer cmConsignTradeRecordDbNo;

    /**
     * 客户管理充值记录
     */
    private Integer cmRechargeRecordDbNo;

    /**
     * 客户管理店铺关系
     */
    private Integer cmShopDistributorRelDbNo;


    /**
     * 客户管理mysql lock
     */
    private Integer cmIdLockDbNo;

    /**
     * 售后lock锁
     */
    private Integer asLockDbNo;

    /**
     * 库存加工单
     */
    private Integer stockProcessOrderDbNo;

    /**
     * 库存加工原料信息
     */
    private Integer stockProcessMaterialDbNo;

    /**
     * 库存加工货位信息
     */
    private Integer stockProcessGoodsSectionDbNo;

    /**
     * 库存加工操作日志
     */
    private Integer stockProcessOpLogDbNo;

    /**
     * 库存仓内加工单
     */
    private Integer stockProductOrderDbNo;

    /**
     * 库存仓内加工原料
     */
    private Integer stockProductMaterialDbNo;

    /**
     * 库存仓内加工成品
     */
    private Integer stockProductGoodDbNo;

    /**
     * 单据下载关联表
     */
    private Integer orderDownloadAssociationDbNo;

    /**
     * 商品客户关系
     */
    private Integer itemCustomerBridgeDbNo;

    /**
     * 商品箱dbno
     */
    private Integer itemBoxDbNo;

    /**
     * 商品箱单品dbno
     */
    private Integer itemBoxSingleDbNo;

    /**
     * 配置拆分规则
     */
    private Integer splitConfigRuleDbNo;

    /**
     * 配置拆分规则明细
     */
    private Integer splitConfigRuleDetailDbNo;


    /**
     * 配置拆分组和公公式
     */
    private Integer splitConfigRuleFormulaDbNo;

    /**
     * 商品水洗标
     */
    private Integer washLabelDbNo;

    /**
     * 商品关联水洗标
     */
    private Integer itemWashLabelBridgeDbNo;

    private Integer workingStorageSectionInOutRecordDbNo;


    /**
     * 波次拣选分表号
     */
    public Integer wavePickerDbNo;

    /**
     * 波次人员拣选分表号
     */
    public Integer wavePickStaffReportDbNo;

    /**
     * 订单人员拣选分表号
     */
    public Integer orderPickStaffReportDbNo;

    /**
     * 商品更换关系表的分表号
     */
    public Integer itemChangeRelationDbNo;

    /**
     * 无销量商品报表分表号
     */
    public Integer noSalesReportDbNo;

    /**
     * 商品销量分表号
     */
    public Integer itemSalesDbNo;
    /**
     * 支付单分表号
     */
    public Integer tradePayDbNo;

    /**
     * 售后包裹查询日志
     */
    public Integer asPackageQueryLogDbNo;


    /**
     * 拣货单打印日志分表号
     */
    public Integer pickerPrintLogDbNo;
    /**
     * 拣货单打印日志详情分表号
     */
    public Integer pickerPrintLogDetailDbNo;
    /**
     * 分单日志表分表号
     */
    public Integer printDivideLogDbNo;

    /**
     * 打印任务数据分表号
     */
    private Integer printTaskDataDbNo;

    /**
     * 唯一码打印任务商品详情分表号
     */
    private Integer merchantItemMsgDetailDbNo;
    /**
     * 商品打印数量统计表分表号
     */
    private Integer printMerchantCodeNumLogDbNo;

    /**
     * 打印任务其他信息详情分表号
     */
    private Integer merchantPlusMsgDetailDbNo;

    /**
     * 单据日志分表
     */
    public Integer receiptTraceDbNo;

    /**
     * 客户额外配置分表
     */
    public Integer customerExtendDbNo;

    /**
     * 一品多码分表号
     */
    private Integer oneItemMultiCodeDbNo;

    /**
     * 订单自动标记规则匹配记录分表
     */
    public Integer tradeTagRuleMatchLogDbNo;
    /**
     * 下架单分表号
     */
    private Integer unShelveEntryDbNo;

    /**
     * 下架单明细分表号
     */
    private Integer unShelveDetailEntryDbNo;

    /**
     * 商品标签分表号
     */
    private Integer itemTagDbNo;

    /**
     * 商品标签关联分表号
     */
    private Integer itemTagRelationDbNo;

    /**
     * 任务分表号
     */
    private Integer taskDbNo;

    /**
     * 子任务分表号
     */
    private Integer taskSubDbNo;

    /**
     * 用户物流商分表号
     */
    private Integer userLogisticsProviderDbNo;

    /**
     * 用户物流渠道分表号
     */
    private Integer userLogisticsChannelDbNo;

    /**
     * 组包分表号
     */
    private Integer tradeCombineParcelDbNo;

    /**
     * 小包分表号
     */
    private Integer tradeParcelDbNo;

    /**
     * 唯一码解绑日志表号
     */
    private Integer orderUniqueUnboundLogNo;
    /**
     * 唯一码打印日志分表号
     */
    private Integer uniquePrintLogDbNo;
    /**
     * 唯一码打印详情日志分表号
     */
    private Integer uniquePrintLogDetailDbNo;


    /**
     * 类目属性桥接表分表号
     */
    private Integer propertySegmentBridgeDbNo;

    /**
     * 售后错误重试日志分表号
     */
    private Integer errorSyncRefundLogDbNo;

    /**
     * 快销库存分表号
     */
    private Integer fastInOutStockDbNo;
    /**
     * 快销库存日志分表号
     */
    private Integer fastInOutStockLogDbNo;

    /**
     * 商品额外信息分表号
     */
    private Integer itemExtraInfoDbNo;

    /**
     * 订单商品批次
     */
    private Integer orderProductDbNo;

    /**
     * 自定义绩效日志分表号
     */
    private Integer customWorkloadSupplementLogDbNo;

    private Integer tradeStatSyncDbNo;
    /**
     * 本地消息分表号
     */
    private Integer localMessageDbNo;

    /**
     * 登录日志分表号
     */
    private Integer accountLoginLogDbNo;

    /**
     * 跨境订单打印状态记录
     */
    private Integer abroadTradePrintStatusDbNo;
    /**
     * 赠品规则分表号
     */
    private Integer giftRuleDbNo;

    /**
     * 赠品规则扩展信息分表号
     */
    private Integer giftRuleExtendDbNo;

    /**
     * 赠品规则商品分表号
     */
    private Integer giftRuleItemDbNo;

    /**
     * 赠品赠送记录分表号
     */
    private Integer giftRecordLogDbNo;


    /**
     * 采购与订单的关联分表号
     */
    private Integer assoPurchaseOrderDbNo;


    /**
     * 标签对账关联表
     */
    private Integer assoLabelReconciliationDbNo;


    public Integer getAccountLoginLogDbNo() {
        if (null == accountLoginLogDbNo) {
            accountLoginLogDbNo = generateDbNo(companyId, COUNT_ITEMBRIGE_DB_NUM);
        }
        return accountLoginLogDbNo;
    }

    public void setAccountLoginLogDbNo(Integer accountLoginLogDbNo) {
        this.accountLoginLogDbNo = accountLoginLogDbNo;
    }

    public Integer getLocalMessageDbNo() {
        if (null == localMessageDbNo) {
            localMessageDbNo = generateDbNo(companyId, COUNT_ITEMBRIGE_DB_NUM);
        }
        return localMessageDbNo;
    }

    public void setLocalMessageDbNo(Integer localMessageDbNo) {
        this.localMessageDbNo = localMessageDbNo;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getRdsNo() {
        if (rdsNo == null) {
            return 0;
        }
        return rdsNo;
    }

    public void setRdsNo(Integer rdsNo) {
        this.rdsNo = rdsNo;
    }

    public Integer getTradeDbNo() {
        if (null == tradeDbNo) {
            tradeDbNo = 0;
        }
        return tradeDbNo;
    }

    public DbInfo setTradeDbNo(Integer tradeDbNo) {
        this.tradeDbNo = tradeDbNo;
        return this;
    }

    public Integer getTradePriorityDbNo() {
        if (null == tradePriorityDbNo) {
            tradePriorityDbNo = generateDbNo(companyId, COUNT_TRADE_PRIORITY_DB_NUM);
        }
        return tradePriorityDbNo;
    }

    public void setTradePriorityDbNo(Integer tradePriorityDbNo) {
        this.tradePriorityDbNo = tradePriorityDbNo;
    }

    public Integer getConsignCacheDbNo() {
        if (null == consignCacheDbNo) {
            consignCacheDbNo = generateDbNo(companyId, COUNT_CONSIGN_CACHE_NUM);
        }
        return consignCacheDbNo;
    }

    public DbInfo setConsignCacheDbNo(Integer consignCacheDbNo) {
        this.consignCacheDbNo = consignCacheDbNo;
        return this;
    }

    public Integer getTradeParty3DataDbNo() {
        if (tradeParty3DataDbNo == null) {
            tradeParty3DataDbNo = getTradeDbNo();
        }
        return tradeParty3DataDbNo;
    }

    public DbInfo setTradeParty3DataDbNo(Integer tradeParty3DataDbNo) {
        this.tradeParty3DataDbNo = tradeParty3DataDbNo;
        return this;
    }

    public Integer getWarehouseItemSyncResultDbNo() {
        if (warehouseItemSyncResultDbNo == null) {
            return 0;
        }
        return warehouseItemSyncResultDbNo;
    }

    public void setWarehouseItemSyncResultDbNo(Integer warehouseItemSyncResultDbNo) {
        this.warehouseItemSyncResultDbNo = warehouseItemSyncResultDbNo;
    }

    public Integer getOrderDbNo() {
        if (null == orderDbNo) {
            orderDbNo = 0;
        }
        return orderDbNo;
    }

    public DbInfo setOrderDbNo(Integer orderDbNo) {
        this.orderDbNo = orderDbNo;
        return this;
    }

    public Integer getTradeExceptDbNo() {
       /* if (null == tradeExceptDbNo) {
            tradeExceptDbNo = generateDbNo(companyId, COUNT_TRADE_EXCEPT_DB_NUM);
        }*/
        return generateDbNo(companyId, COUNT_TRADE_EXCEPT_DB_NUM);
    }

    public DbInfo setTradeExceptDbNo(Integer tradeExceptDbNo) {
        this.tradeExceptDbNo = tradeExceptDbNo;
        return this;
    }

    public Integer getOrderExceptDbNo() {
      /*  if (null == orderExceptDbNo) {
            orderExceptDbNo = generateDbNo(companyId, COUNT_ORDER_EXCEPT_DB_NUM);
        }*/
        return generateDbNo(companyId, COUNT_ORDER_EXCEPT_DB_NUM);
    }

    public DbInfo setOrderExceptDbNo(Integer orderExceptDbNo) {
        this.orderExceptDbNo = orderExceptDbNo;
        return this;
    }


    public Integer getTradeTraceDbNo() {
        if (null == tradeTraceDbNo) {
            tradeTraceDbNo = 0;
        }
        return tradeTraceDbNo;
    }

    public DbInfo setTradeTraceDbNo(Integer tradeTraceDbNo) {
        this.tradeTraceDbNo = tradeTraceDbNo;
        return this;
    }

    public Integer getConfigTraceDbno() {
        if (null == configTraceDbno) {
            configTraceDbno = generateDbNo(companyId, COUNT_CONFIG_TRACE_NUM);
        }
        return configTraceDbno;
    }

    public DbInfo setConfigTraceDbno(Integer configTraceDbno) {
        this.configTraceDbno = configTraceDbno;
        return this;
    }

    public Integer getItemTraceDbNo() {
        if (null == itemTraceDbNo) {
            itemTraceDbNo = generateDbNo(companyId, COUNT_ITEM_TRACE_NUM);
        }
        return itemTraceDbNo;
    }

    public DbInfo setItemTraceDbNo(Integer itemTraceDbNo) {
        this.itemTraceDbNo = itemTraceDbNo;
        return this;
    }


    public Integer getItemChangeLogDbNo() {
        if (itemChangeLogDbNo == null) {
            itemChangeLogDbNo = getOrderDbNo();
        }
        return itemChangeLogDbNo;
    }

    public DbInfo setItemChangeLogDbNo(Integer itemChangeLogDbNo) {
        this.itemChangeLogDbNo = itemChangeLogDbNo;
        return this;
    }

    public Integer getOrderModifyLogDbNo() {
        if (orderModifyLogDbNo == null) {
            orderModifyLogDbNo = getOrderDbNo();
        }
        return orderModifyLogDbNo;
    }

    public DbInfo setOrderModifyLogDbNo(Integer orderModifyLogDbNo) {
        this.orderModifyLogDbNo = orderModifyLogDbNo;
        return this;
    }

    public Integer getOrderReplaceLogDbNo() {
        if (orderReplaceLogDbNo == null) {
            orderReplaceLogDbNo = getOrderDbNo();
        }
        return orderReplaceLogDbNo;
    }

    public DbInfo setOrderReplaceLogDbNo(Integer orderReplaceLogDbNo) {
        this.orderReplaceLogDbNo = orderReplaceLogDbNo;
        return this;
    }

    public Integer getAddressChangeLogDbNo() {
        if (addressChangeLogDbNo == null) {
            addressChangeLogDbNo = getTradeDbNo();
        }
        return addressChangeLogDbNo;
    }

    public DbInfo setAddressChangeLogDbNo(Integer addressChangeLogDbNo) {
        this.addressChangeLogDbNo = addressChangeLogDbNo;
        return this;
    }

    public Integer getSysMemoLogDbNo() {
        if (sysMemoLogDbNo == null) {
            sysMemoLogDbNo = getTradeDbNo();//以前的历史数据分表号为订单表的分表号
        }
        return sysMemoLogDbNo;
    }

    public DbInfo setSysMemoLogDbNo(Integer sysMemoLogDbNo) {
        this.sysMemoLogDbNo = sysMemoLogDbNo;
        return this;
    }

    public Integer getAssItemDbNo() {
        if (assItemDbNo == null) {
            return 0;
        }
        return assItemDbNo;
    }

    public void setAssItemDbNo(Integer assItemDbNo) {
        this.assItemDbNo = assItemDbNo;
    }

    public Integer getAssOrderDbNo() {
        if (assOrderDbNo == null) {
            return 0;
        }
        return assOrderDbNo;
    }

    public void setAssOrderDbNo(Integer assOrderDbNo) {
        this.assOrderDbNo = assOrderDbNo;
    }

    public Integer getAssSkuDbNo() {
        if (assSkuDbNo == null) {
            return 0;
        }
        return assSkuDbNo;
    }

    public void setAssSkuDbNo(Integer assSkuDbNo) {
        this.assSkuDbNo = assSkuDbNo;
    }

    /**
     * 库存业务锁定分表号
     */
    private Integer stockBusiLockDbNo;

    public Integer getItemSupplierBridgeDbNo() {
        if (itemSupplierBridgeDbNo == null) {
            itemSupplierBridgeDbNo = Integer.valueOf(0);
        }
        return itemSupplierBridgeDbNo;
    }


    public void setItemSupplierBridgeDbNo(Integer itemSupplierBridgeDbNo) {
        this.itemSupplierBridgeDbNo = itemSupplierBridgeDbNo;
    }

    public Integer getWavePickingCodeDbNo() {
        if (null == wavePickingCodeDbNo) {
            wavePickingCodeDbNo = generateDbNo(companyId, COUNT_WAVE_PICKING_CODE_NUM);
        }
        return wavePickingCodeDbNo;
    }

    public void setWavePickingCodeDbNo(Integer wavePickingCodeDbNo) {
        this.wavePickingCodeDbNo = wavePickingCodeDbNo;
    }

    public Integer getWaveSortingDbNo() {
        if (waveSortingDbNo == null) {
            return 0;
        }
        return waveSortingDbNo;
    }

    public DbInfo setWaveSortingDbNo(Integer waveSortingDbNo) {
        this.waveSortingDbNo = waveSortingDbNo;
        return this;
    }

    public Integer getParty3WarehouseLogDbNo() {
        if (party3WarehouseLogDbNo == null) {
            party3WarehouseLogDbNo = 0;
        }
        return party3WarehouseLogDbNo;
    }

    public void setParty3WarehouseLogDbNo(Integer party3WarehouseLogDbNo) {
        this.party3WarehouseLogDbNo = party3WarehouseLogDbNo;
    }

    public Integer getWaveSortingDetailDbNo() {
        if (waveSortingDetailDbNo == null) {
            return 0;
        }
        return waveSortingDetailDbNo;
    }

    public DbInfo setWaveSortingDetailDbNo(Integer waveSortingDetailDbNo) {
        this.waveSortingDetailDbNo = waveSortingDetailDbNo;
        return this;
    }

    public Integer getPreSellItemDbNo() {
        if (null == preSellItemDbNo) {
            return 0;
        }
        return preSellItemDbNo;
    }

    public void setPreSellItemDbNo(Integer preSellItemDbNo) {
        this.preSellItemDbNo = preSellItemDbNo;
    }

    public Integer getReportPurchaseDbNo() {
        if (null == reportPurchaseDbNo) {
            return 0;
        }
        return reportPurchaseDbNo;
    }

    public DbInfo setReportPurchaseDbNo(Integer reportPurchaseDbNo) {
        this.reportPurchaseDbNo = reportPurchaseDbNo;
        return this;
    }

    public Integer getReportTradeWeightDbNo() {
        if (null == reportTradeWeightDbNo) {
            return 0;
        }
        return reportTradeWeightDbNo;
    }

    public DbInfo setReportTradeWeightDbNo(Integer reportTradeWeightDbNo) {
        this.reportTradeWeightDbNo = reportTradeWeightDbNo;
        return this;
    }

    public Integer getReportTradeAuditDbNo() {
        if (null == reportTradeAuditDbNo) {
            return 0;
        }
        return reportTradeAuditDbNo;
    }

    public DbInfo setReportTradeAuditDbNo(Integer reportTradeAuditDbNo) {
        this.reportTradeAuditDbNo = reportTradeAuditDbNo;
        return this;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getShelfTaskNo() {
        return this.shelfTaskNo == null ? Integer.valueOf(0) : shelfTaskNo;
    }

    public void setShelfTaskNo(Integer shelfTaskNo) {
        this.shelfTaskNo = shelfTaskNo;
    }

    public Integer getShelfTaskLogNo() {
        return this.shelfTaskLogNo == null ? Integer.valueOf(0) : shelfTaskLogNo;
    }

    public void setShelfTaskLogNo(Integer shelfTaskLogNo) {
        this.shelfTaskLogNo = shelfTaskLogNo;
    }

    public Integer getBrandDbNo() {
        return this.brandDbNo == null ? Integer.valueOf(0) : brandDbNo;
    }

    public void setBrandDbNo(Integer brandDbNo) {
        this.brandDbNo = brandDbNo;
    }

    public Integer getReportSendGoodsTypeDbNo() {
        if (null == reportSendGoodsTypeDbNo) {
            return 0;
        }
        return reportSendGoodsTypeDbNo;
    }

    public DbInfo setReportSendGoodsTypeDbNo(Integer reportSendGoodsTypeDbNo) {
        this.reportSendGoodsTypeDbNo = reportSendGoodsTypeDbNo;
        return this;
    }

    public Integer getItemWarnDbNo() {
        if (itemWarnDbNo == null) {
            itemWarnDbNo = 0;
        }
        return itemWarnDbNo;
    }

    public void setItemWarnDbNo(Integer itemWarnDbNo) {
        this.itemWarnDbNo = itemWarnDbNo;
    }

    public Integer getDmjSkuDbNo() {
        return dmjSkuDbNo == null ? Integer.valueOf(0) : dmjSkuDbNo;
    }

    public void setDmjSkuDbNo(Integer dmjSkuDbNo) {
        this.dmjSkuDbNo = dmjSkuDbNo;
    }

    public Integer getDmjItemDbNo() {
        return dmjItemDbNo == null ? Integer.valueOf(0) : dmjItemDbNo;
    }

    public void setDmjItemDbNo(Integer dmjItemDbNo) {
        this.dmjItemDbNo = dmjItemDbNo;
    }

    public Integer getSellerCatDbNo() {
        if (null == sellerCatDbNo) {
            sellerCatDbNo = 0;
        }
        return sellerCatDbNo;
    }

    public DbInfo setSellerCatDbNo(Integer sellerCatDbNo) {
        this.sellerCatDbNo = sellerCatDbNo;
        return this;
    }

    public Integer getShopSellerCatDbNo() {
        if (shopSellerCatDbNo == null) {
            shopSellerCatDbNo = 0;
        }
        return shopSellerCatDbNo;
    }

    public DbInfo setShopSellerCatDbNo(Integer shopSellerCatDbNo) {
        this.shopSellerCatDbNo = shopSellerCatDbNo;
        return this;
    }

    public Integer getSkuErpDictOtherDbNo() {
        if (skuErpDictOtherDbNo == null) {
            skuErpDictOtherDbNo = 0;
        }
        return skuErpDictOtherDbNo;
    }

    public DbInfo setSkuErpDictOtherDbNo(Integer skuErpDictOtherDbNo) {
        this.skuErpDictOtherDbNo = skuErpDictOtherDbNo;
        return this;
    }

    public Integer getStockInOutRecordDbNo() {
        return stockInOutRecordDbNo == null ? Integer.valueOf(0) : stockInOutRecordDbNo;
    }

    public DbInfo setStockInOutRecordDbNo(Integer stockInOutRecordDbNo) {
        this.stockInOutRecordDbNo = stockInOutRecordDbNo;
        return this;
    }

    public Integer getStockUploadRuleDbNo() {
        return stockUploadRuleDbNo == null ? Integer.valueOf(0) : stockUploadRuleDbNo;
    }

    public DbInfo setStockUploadRuleDbNo(Integer stockUploadRuleDbNo) {
        this.stockUploadRuleDbNo = stockUploadRuleDbNo;
        return this;
    }

    public Integer getStockLockDetailDbNo() {
        return stockLockDetailDbNo == null ? Integer.valueOf(0) : stockLockDetailDbNo;
    }

    public DbInfo setStockLockDetailDbNo(Integer stockLockDetailDbNo) {
        this.stockLockDetailDbNo = stockLockDetailDbNo;
        return this;
    }

    public Integer getStockReserveDbNo() {
        return stockReserveDbNo == null ? Integer.valueOf(0) : stockReserveDbNo;
    }

    public DbInfo setStockReserveDbNo(Integer stockReserveDbNo) {
        this.stockReserveDbNo = stockReserveDbNo;
        return this;
    }

    public Integer getStockPresellDbNo() {
        return stockPresellDbNo == null ? Integer.valueOf(0) : stockPresellDbNo;
    }

    public DbInfo setStockPresellDbNo(Integer stockPresellDbNo) {
        this.stockPresellDbNo = stockPresellDbNo;
        return this;
    }

    public DbInfo setWorkOrderDbNo(Integer workOrderDbNo) {
        this.workOrderDbNo = workOrderDbNo;
        return this;
    }

    public DbInfo setAsReissueOrRefundDbNo(Integer asReissueOrRefundDbNo) {
        this.asReissueOrRefundDbNo = asReissueOrRefundDbNo;
        return this;
    }

    public Integer getAsReissueOrRefundDbNo() {
        if (asReissueOrRefundDbNo == null) {
            return 0;
        }
        return asReissueOrRefundDbNo;
    }

    public DbInfo setAsItemSnapshotDbNo(Integer asItemSnapshotDbNo) {
        this.asItemSnapshotDbNo = asItemSnapshotDbNo;
        return this;
    }

    public Integer getAsItemSnapshotDbNo() {
        if (asItemSnapshotDbNo == null) {
            return 0;
        }
        return asItemSnapshotDbNo;
    }

    public DbInfo setAsRefundWarehouseDbNo(Integer asRefundWarehouseDbNo) {
        this.asRefundWarehouseDbNo = asRefundWarehouseDbNo;
        return this;
    }

    public Integer getAsRefundWarehouseDbNo() {
        if (asRefundWarehouseDbNo == null) {
            return 0;
        }
        return asRefundWarehouseDbNo;
    }


    public DbInfo setAsMsgBoardDbNo(Integer asMsgBoardDbNo) {
        this.asMsgBoardDbNo = asMsgBoardDbNo;
        return this;
    }

    public Integer getAsMsgBoardDbNo() {
        if (asMsgBoardDbNo == null) {
            return 0;
        }
        return asMsgBoardDbNo;
    }

    public Integer getWaveDbNo() {
        if (waveDbNo == null) {
            return 0;
        }
        return waveDbNo;
    }

    public DbInfo setWaveDbNo(Integer waveDbNo) {
        this.waveDbNo = waveDbNo;
        return this;
    }

    public Integer getAssoWaveTradeDbNo() {
        if (assoWaveTradeDbNo == null) {
            return 0;
        }
        return assoWaveTradeDbNo;
    }

    public DbInfo setAssoWaveTradeDbNo(Integer assoWaveTradeDbNo) {
        this.assoWaveTradeDbNo = assoWaveTradeDbNo;
        return this;
    }

    public DbInfo setAsOperateLogDbNo(Integer asOperateLogDbNo) {
        this.asOperateLogDbNo = asOperateLogDbNo;
        return this;
    }

    public Integer getAsOperateLogDbNo() {
        if (asOperateLogDbNo == null) {
            return 0;
        }
        return asOperateLogDbNo;
    }

    public DbInfo setAsWoRemarkDbNo(Integer asWoRemarkDbNo) {
        this.asWoRemarkDbNo = asWoRemarkDbNo;
        return this;
    }

    public Integer getAsWoRemarkDbNo() {
        if (asWoRemarkDbNo == null) {
            return 0;
        }
        return asWoRemarkDbNo;
    }

    public DbInfo setAsReasonDbNo(Integer asReasonDbNo) {
        this.asReasonDbNo = asReasonDbNo;
        return this;
    }

    public Integer getAsReasonDbNo() {
        if (asReasonDbNo == null) {
            return 0;
        }
        return asReasonDbNo;
    }

    public Integer getItemBridgeNo() {
        if (itemBridgeNo == null) {
            return 0;
        }
        return itemBridgeNo;
    }

    public DbInfo setItemBridgeNo(Integer itemBridgeNo) {
        this.itemBridgeNo = itemBridgeNo;
        return this;
    }

    public Integer getSkuBridgeNo() {
        if (SkuBridgeNo == null) {
            return 0;
        }
        return SkuBridgeNo;
    }

    public DbInfo setSkuBridgeNo(Integer skuBridgeNo) {
        SkuBridgeNo = skuBridgeNo;
        return this;
    }

    public Integer getWorkOrderDbNo() {
        if (workOrderDbNo == null) {
            workOrderDbNo = 0;
        }
        return workOrderDbNo;
    }

    public DbInfo setItemStockDbNo(Integer itemStockDbNo) {
        this.itemStockDbNo = itemStockDbNo;
        return this;
    }

    public Integer getItemStockDbNo() {
        if (itemStockDbNo == null) {
            itemStockDbNo = 0;
        }
        return itemStockDbNo;
    }

    public DbInfo setItemStockLogDbNo(Integer itemStockLogDbNo) {
        this.itemStockLogDbNo = itemStockLogDbNo;
        return this;
    }

    public Integer getItemStockLogDbNo() {
        if (itemStockLogDbNo == null) {
            itemStockLogDbNo = 0;
        }
        return itemStockLogDbNo;
    }

    public Integer getStockOrderRecordDbNo() {
        if (stockOrderRecordDbNo == null) {
            stockOrderRecordDbNo = 0;
        }
        return stockOrderRecordDbNo;
    }

    public void setStockOrderRecordDbNo(Integer stockOrderRecordDbNo) {
        this.stockOrderRecordDbNo = stockOrderRecordDbNo;
    }

    public Integer getExpressSmartMatchDbNo() {
        if (expressSmartMatchDbNo == null) {
            expressSmartMatchDbNo = 0;
        }
        return expressSmartMatchDbNo;
    }

    public DbInfo setExpressSmartMatchDbNo(Integer expressSmartMatchDbNo) {
        this.expressSmartMatchDbNo = expressSmartMatchDbNo;
        return this;
    }

    public Integer getWaybillUsingRuleDbNo() {
        if (waybillUsingRuleDbNo == null) {
            waybillUsingRuleDbNo = generateDbNo(companyId, DbInfo.COUNT_WAYBILL_USING_RULE_NUM);
        }
        return waybillUsingRuleDbNo;
    }

    public DbInfo setWaybillUsingRuleDbNo(Integer waybillUsingRuleDbNo) {
        this.waybillUsingRuleDbNo = waybillUsingRuleDbNo;
        return this;
    }

    public Integer getWaybillUsingRuleDetailDbNo() {
        if (waybillUsingRuleDetailDbNo == null) {
            waybillUsingRuleDetailDbNo = generateDbNo(companyId, DbInfo.COUNT_WAYBILL_USING_RULE_DETAIL_NUM);
        }
        return waybillUsingRuleDetailDbNo;
    }

    public DbInfo setWaybillUsingRuleDetailDbNo(Integer waybillUsingRuleDetailDbNo) {
        this.waybillUsingRuleDetailDbNo = waybillUsingRuleDetailDbNo;
        return this;
    }

    public Integer getWaybillUsingRuleLogDbNo() {
        if (waybillUsingRuleLogDbNo == null) {
            waybillUsingRuleLogDbNo = generateDbNo(companyId, DbInfo.COUNT_WAYBILL_USING_RULE_LOG_NUM);
        }
        return waybillUsingRuleLogDbNo;
    }

    public DbInfo setWaybillUsingRuleLogDbNo(Integer waybillUsingRuleLogDbNo) {
        this.waybillUsingRuleLogDbNo = waybillUsingRuleLogDbNo;
        return this;
    }

    public Integer getWaybillTopUpLogDbNo() {
        if (waybillTopUpLogDbNo == null) {
            waybillTopUpLogDbNo = generateDbNo(companyId, DbInfo.COUNT_WAYBILL_TOP_UP_LOG_NUM);
        }
        return waybillTopUpLogDbNo;
    }

    public DbInfo setWaybillTopUpLogDbNo(Integer waybillTopUpLogDbNo) {
        this.waybillTopUpLogDbNo = waybillTopUpLogDbNo;
        return this;
    }

    public Integer getWlbAddressTradeLinkDbNo() {
        if (wlbAddressTradeLinkDbNo == null) {
            wlbAddressTradeLinkDbNo = 0;
        }
        return wlbAddressTradeLinkDbNo;
    }


    public Integer getActionLogDbNo() {
        if (actionLogDbNo == null) {
            actionLogDbNo = 0;
        }
        return actionLogDbNo;
    }

    public DbInfo setActionLogDbNo(Integer actionLogDbNo) {
        this.actionLogDbNo = actionLogDbNo;
        return this;
    }

    public Integer getSkuDbNo() {
        if (null == SkuDbNo) {
            SkuDbNo = 0;
        }
        return SkuDbNo;
    }

    public DbInfo setSkuDbNo(Integer skuDbNo) {
        SkuDbNo = skuDbNo;
        return this;
    }

    public Integer getItemDbNo() {
        if (null == ItemDbNo) {
            ItemDbNo = 0;
        }
        return ItemDbNo;
    }

    public DbInfo setItemDbNo(Integer itemDbNo) {
        ItemDbNo = itemDbNo;
        return this;
    }

    public DbInfo setWlbAddressTradeLinkDbNo(Integer wlbAddressTradeLinkDbNo) {
        this.wlbAddressTradeLinkDbNo = wlbAddressTradeLinkDbNo;
        return this;
    }

    public Integer getOutSidPoolDbNo() {
        if (outSidPoolDbNo == null) {
            outSidPoolDbNo = 0;
        }
        return outSidPoolDbNo;
    }

    public DbInfo setOutSidPoolDbNo(Integer outSidPoolDbNo) {
        this.outSidPoolDbNo = outSidPoolDbNo;
        return this;
    }

    public Integer getPrintFieldValuesLogDbNo() {
        if (null == printFieldValuesLogDbNo) {
            printFieldValuesLogDbNo = generateDbNo(companyId, PRINT_FIELD_VALUES_LOG_NUM);
        }
        return printFieldValuesLogDbNo;
    }

    public void setPrintFieldValuesLogDbNo(Integer printFieldValuesLogDbNo) {
        this.printFieldValuesLogDbNo = printFieldValuesLogDbNo;
    }

    public Integer getUserCustomPrintCountDbNo() {
        if (userCustomPrintCountDbNo == null) {
            userCustomPrintCountDbNo = 0;
        }
        return userCustomPrintCountDbNo;
    }

    public DbInfo setUserCustomPrintCountDbNo(Integer userCustomPrintCountDbNo) {
        this.userCustomPrintCountDbNo = userCustomPrintCountDbNo;
        return this;
    }


    public Integer getStockAdjustLogDbNo() {
        if (null == stockAdjustLogDbNo) {
            stockAdjustLogDbNo = getOrderDbNo();
        }
        return stockAdjustLogDbNo;
    }

    public DbInfo setStockAdjustLogDbNo(Integer stockAdjustLogDbNo) {
        this.stockAdjustLogDbNo = stockAdjustLogDbNo;
        return this;
    }

    public Integer getUserWlbExpressTemplateDbNo() {
        if (null == userWlbExpressTemplateDbNo) {
            userWlbExpressTemplateDbNo = 0;
        }
        return userWlbExpressTemplateDbNo;
    }

    public DbInfo setUserWlbExpressTemplateDbNo(Integer userWlbExpressTemplateDbNo) {
        this.userWlbExpressTemplateDbNo = userWlbExpressTemplateDbNo;
        return this;
    }

    public Integer getUserWlbCustomTemplateDbNo() {
        if (null == userWlbCustomTemplateDbNo) {
            userWlbCustomTemplateDbNo = generateDbNo(companyId, COUNT_USER_WLB_CUSTOM_TEMPLATE_NUM);
        }
        return userWlbCustomTemplateDbNo;

    }

    public DbInfo setUserWlbCustomTemplateDbNo(Integer userWlbCustomTemplateDbNo) {
        this.userWlbCustomTemplateDbNo = userWlbCustomTemplateDbNo;
        return this;
    }

    public Integer getUserAssemblyTemplateDbNo() {
        if (null == userAssemblyTemplateDbNo) {
            userAssemblyTemplateDbNo = 0;
        }
        return userAssemblyTemplateDbNo;
    }

    public DbInfo setUserAssemblyTemplateDbNo(Integer userAssemblyTemplateDbNo) {
        this.userAssemblyTemplateDbNo = userAssemblyTemplateDbNo;
        return this;
    }

    public Integer getUserDeliverTemplateDbNo() {
        if (null == userDeliverTemplateDbNo) {
            userDeliverTemplateDbNo = 0;
        }
        return userDeliverTemplateDbNo;
    }

    public DbInfo setUserDeliverTemplateDbNo(Integer userDeliverTemplateDbNo) {
        this.userDeliverTemplateDbNo = userDeliverTemplateDbNo;
        return this;
    }

    public Integer getUserPickerTemplateDbNo() {
        if (null == userPickerTemplateDbNo) {
            userPickerTemplateDbNo = 0;
        }
        return userPickerTemplateDbNo;
    }

    public void setUserPickerTemplateDbNo(Integer userPickerTemplateDbNo) {
        this.userPickerTemplateDbNo = userPickerTemplateDbNo;
    }

    public Integer getUserGetterTemplateDbNo() {
        if (null == userGetterTemplateDbNo)
            userGetterTemplateDbNo = 0;
        return userGetterTemplateDbNo;
    }

    public void setUserGetterTemplateDbNo(Integer userGetterTemplateDbNo) {
        this.userGetterTemplateDbNo = userGetterTemplateDbNo;
    }

    public Integer getUserExpressTemplateDbNo() {
        if (null == userExpressTemplateDbNo) {
            userExpressTemplateDbNo = 0;
        }
        return userExpressTemplateDbNo;
    }

    public DbInfo setUserExpressTemplateDbNo(Integer userExpressTemplateDbNo) {
        this.userExpressTemplateDbNo = userExpressTemplateDbNo;
        return this;
    }

    public Integer getFreightTemplateDbNo() {
        if (null == freightTemplateDbNo)
            freightTemplateDbNo = 0;
        return freightTemplateDbNo;
    }

    public void setFreightTemplateDbNo(Integer freightTemplateDbNo) {
        this.freightTemplateDbNo = freightTemplateDbNo;
    }

    public Integer getPrintTradeLogDbNo() {
        if (null == printTradeLogDbNo) {
            printTradeLogDbNo = 0;
        }
        return printTradeLogDbNo;
    }

    public DbInfo setPrintTradeLogDbNo(Integer printTradeLogDbNo) {
        this.printTradeLogDbNo = printTradeLogDbNo;
        return this;
    }

    public Integer getTranspondPrintLogDbNo() {
        if (null == transpondPrintLogDbNo) {
            transpondPrintLogDbNo = generateDbNo(companyId, TRANSPOND_PRINT_LOG_COUNT_NUM);
        }
        return transpondPrintLogDbNo;
    }

    public void setTranspondPrintLogDbNo(Integer transpondPrintLogDbNo) {
        this.transpondPrintLogDbNo = transpondPrintLogDbNo;
    }

    public Integer getMultiPacksPrintTradeLogDbNo() {
        if (null == multiPacksPrintTradeLogDbNo) {
            multiPacksPrintTradeLogDbNo = generateDbNo(companyId, COUNT_MULTI_PACKS_PRINT_TRADE_LOG_NUM);
        }
        return multiPacksPrintTradeLogDbNo;
    }

    public void setMultiPacksPrintTradeLogDbNo(Integer multiPacksPrintTradeLogDbNo) {
        this.multiPacksPrintTradeLogDbNo = multiPacksPrintTradeLogDbNo;
    }

    public Integer getPrintOrderShipCountDbNo() {
        if (null == printOrderShipCountDbNo) {
            printOrderShipCountDbNo = 0;
        }
        return printOrderShipCountDbNo;
    }

    public DbInfo setPrintOrderShipCountDbNo(Integer printOrderShipCountDbNo) {
        this.printOrderShipCountDbNo = printOrderShipCountDbNo;
        return this;
    }

    public Integer getNotificationLogDbNo() {
        if (null == notificationLogDbNo) {
            notificationLogDbNo = 0;
        }
        return notificationLogDbNo;
    }

    public DbInfo setNotificationLogDbNo(Integer notificationLogDbNo) {
        this.notificationLogDbNo = notificationLogDbNo;
        return this;
    }

    public Integer getSmsTxnDbNo() {
        if (null == smsTxnDbNo) {
            smsTxnDbNo = 0;
        }
        return smsTxnDbNo;
    }

    public void setSmsTxnDbNo(Integer smsTxnDbNo) {
        this.smsTxnDbNo = smsTxnDbNo;
    }

    public Integer getSmsCreatedTradeDbNo() {
        if (null == smsCreatedTradeDbNo) {
            smsCreatedTradeDbNo = 0;
        }
        return smsCreatedTradeDbNo;
    }

    public void setSmsCreatedTradeDbNo(Integer smsCreatedTradeDbNo) {
        this.smsCreatedTradeDbNo = smsCreatedTradeDbNo;
    }

    public Integer getOpLogDbNo() {
        if (null == opLogDbNo) {
            opLogDbNo = 0;
        }
        return opLogDbNo;
    }

    public void setOpLogDbNo(Integer opLogDbNo) {
        this.opLogDbNo = opLogDbNo;
    }

    public Integer getWarehouseTemplateDbNo() {
        if (warehouseTemplateDbNo == null) {
            return 0;
        }
        return warehouseTemplateDbNo;
    }

    public DbInfo setWarehouseTemplateDbNo(Integer warehouseTemplateDbNo) {
        this.warehouseTemplateDbNo = warehouseTemplateDbNo;
        return this;
    }

    public Integer getTemplateBranchDbNo() {
        if (templateBranchDbNo == null) {
            return 0;
        }
        return templateBranchDbNo;
    }

    public DbInfo setTemplateBranchDbNo(Integer templateBranchDbNo) {
        this.templateBranchDbNo = templateBranchDbNo;
        return this;
    }

    public Integer getTemplateBranchAddressDbNo() {
        if (templateBranchAddressDbNo == null) {
            return 0;
        }
        return templateBranchAddressDbNo;
    }

    public DbInfo setTemplateBranchAddressDbNo(Integer templateBranchAddressDbNo) {
        this.templateBranchAddressDbNo = templateBranchAddressDbNo;
        return this;
    }

    public Integer getOutSidRecoverDbNo() {
        if (null == outSidRecoverDbNo) {
            outSidRecoverDbNo = 0;
        }
        return outSidRecoverDbNo;
    }

    public DbInfo setOutSidRecoverDbNo(Integer outSidRecoverDbNo) {
        this.outSidRecoverDbNo = outSidRecoverDbNo;
        return this;
    }

    public Integer getWarehouseEntryDetailDbNo() {
        if (null == warehouseEntryDetailDbNo)
            warehouseEntryDetailDbNo = 0;
        return warehouseEntryDetailDbNo;
    }

    public void setWarehouseEntryDetailDbNo(Integer warehouseEntryDetailDbNo) {
        this.warehouseEntryDetailDbNo = warehouseEntryDetailDbNo;
    }

    public Integer getWarehouseEntryDbNo() {
        if (null == warehouseEntryDbNo)
            warehouseEntryDbNo = 0;
        return warehouseEntryDbNo;
    }

    public void setWarehouseEntryDbNo(Integer warehouseEntryDbNo) {
        this.warehouseEntryDbNo = warehouseEntryDbNo;
    }

    public Integer getSmartPurchaseReturnDbNo() {
        if (smartPurchaseReturnDbNo == null) {
            smartPurchaseReturnDbNo = generateDbNo(companyId, COUNT_SMART_PURCHASE_RETURN);
        }
        return smartPurchaseReturnDbNo;
    }

    public void setSmartPurchaseReturnDbNo(Integer smartPurchaseReturnDbNo) {
        this.smartPurchaseReturnDbNo = smartPurchaseReturnDbNo;
    }

    public Integer getWarehouseEntryWaveUniqueCodeDbNo() {
        if (warehouseEntryWaveUniqueCodeDbNo == null) {
            warehouseEntryWaveUniqueCodeDbNo = generateDbNo(companyId, COUNT_WAREHOUSE_ENTRY_WAVE_UNIQUE_CODE);
        }
        return warehouseEntryWaveUniqueCodeDbNo;
    }

    public Integer getWmsBusinessUniqueCodeDbNo() {
        if (wmsBusinessUniqueCodeDbNo == null) {
            wmsBusinessUniqueCodeDbNo = generateDbNo(companyId, COUNT_TRADES_BUSINESS_WAVE_UNIQUE_CODE);
        }
        return wmsBusinessUniqueCodeDbNo;
    }

    public void setWarehouseEntryWaveUniqueCodeDbNo(Integer warehouseEntryWaveUniqueCodeDbNo) {
        this.warehouseEntryWaveUniqueCodeDbNo = warehouseEntryWaveUniqueCodeDbNo;
    }

    public Integer getPurchaseOrderDetailDbNo() {
        if (null == purchaseOrderDetailDbNo)
            purchaseOrderDetailDbNo = 0;
        return purchaseOrderDetailDbNo;
    }

    public void setPurchaseOrderDetailDbNo(Integer purchaseOrderDetailDbNo) {
        this.purchaseOrderDetailDbNo = purchaseOrderDetailDbNo;
    }

    public Integer getPurchaseOrderUniqueCodeDbNo() {
        if (purchaseOrderUniqueCodeDbNo == null) {
            purchaseOrderUniqueCodeDbNo = generateDbNo(companyId, COUNT_PURCHASE_ORDER_UNIQUE_CODE_NUM);
        }
        return purchaseOrderUniqueCodeDbNo;
    }

    public void setPurchaseOrderUniqueCodeDbNo(Integer purchaseOrderUniqueCodeDbNo) {
        this.purchaseOrderUniqueCodeDbNo = purchaseOrderUniqueCodeDbNo;
    }

    public Integer getPurchaseOrderDbNo() {
        if (null == purchaseOrderDbNo)
            purchaseOrderDbNo = 0;
        return purchaseOrderDbNo;
    }

    public void setPurchaseOrderDbNo(Integer purchaseOrderDbNo) {
        this.purchaseOrderDbNo = purchaseOrderDbNo;
    }

    public Integer getStorageLogDbNo() {
        if (null == storageLogDbNo) {
            storageLogDbNo = 0;
        }
        return storageLogDbNo;
    }

    public void setStorageLogDbNo(Integer storageLogDbNo) {
        this.storageLogDbNo = storageLogDbNo;
    }

    public Integer getPayeeFinanceDbNo() {
        if (payeeFinanceDbNo == null) {
            payeeFinanceDbNo = 0;
        }
        return payeeFinanceDbNo;
    }

    public void setPayeeFinanceDbNo(Integer payeeFinanceDbNo) {
        this.payeeFinanceDbNo = payeeFinanceDbNo;
    }

    public Integer getPayerFinanceDbNo() {
        if (payerFinanceDbNo == null) {
            payerFinanceDbNo = 0;
        }
        return payerFinanceDbNo;
    }

    public void setPayerFinanceDbNo(Integer payerFinanceDbNo) {
        this.payerFinanceDbNo = payerFinanceDbNo;
    }

    public Integer getSubjectFinanceDbNo() {
        if (subjectFinanceDbNo == null) {
            subjectFinanceDbNo = 0;
        }
        return subjectFinanceDbNo;
    }

    public void setSubjectFinanceDbNo(Integer subjectFinanceDbNo) {
        this.subjectFinanceDbNo = subjectFinanceDbNo;
    }

    public Integer getCashJournalFinanceDbNo() {
        if (cashJournalFinanceDbNo == null) {
            cashJournalFinanceDbNo = 0;
        }
        return cashJournalFinanceDbNo;
    }

    public void setCashJournalFinanceDbNo(Integer cashJournalFinanceDbNo) {
        this.cashJournalFinanceDbNo = cashJournalFinanceDbNo;
    }

    public Integer getImportExpressFinanceDbNo() {
        if (importExpressFinanceDbNo == null) {
            importExpressFinanceDbNo = 0;
        }
        return importExpressFinanceDbNo;
    }

    public void setImportExpressFinanceDbNo(Integer importExpressFinanceDbNo) {
        this.importExpressFinanceDbNo = importExpressFinanceDbNo;
    }

    public Integer getTradeExpressFinanceDbNo() {
        if (tradeExpressFinanceDbNo == null) {
            tradeExpressFinanceDbNo = 0;
        }
        return tradeExpressFinanceDbNo;
    }

    public void setTradeExpressFinanceDbNo(Integer tradeExpressFinanceDbNo) {
        this.tradeExpressFinanceDbNo = tradeExpressFinanceDbNo;
    }

    public Integer getAssoGoodsSectionSkuDbNo() {
        return assoGoodsSectionSkuDbNo == null ? 0 : assoGoodsSectionSkuDbNo;
    }

    public DbInfo setAssoGoodsSectionSkuDbNo(Integer assoGoodsSectionSkuDbNo) {
        this.assoGoodsSectionSkuDbNo = assoGoodsSectionSkuDbNo;
        return this;
    }

    public Integer getStockRegionAlertDbNo() {
        return stockRegionAlertDbNo == null ? assoGoodsSectionSkuDbNo : stockRegionAlertDbNo;
    }

    public DbInfo setStockRegionAlertDbNo(Integer stockRegionAlertDbNo) {
        this.stockRegionAlertDbNo = stockRegionAlertDbNo;
        return this;
    }

    public Integer getSkuCheckRecordDbNo() {
        return skuCheckRecordDbNo == null ? 0 : skuCheckRecordDbNo;
    }

    public DbInfo setSkuCheckRecordDbNo(Integer skuCheckRecordDbNo) {
        this.skuCheckRecordDbNo = skuCheckRecordDbNo;
        return this;
    }

    public Integer getGoodsSectionSkuLockRecordDbNo() {
        return goodsSectionSkuLockRecordDbNo == null ? 0 : goodsSectionSkuLockRecordDbNo;
    }

    public void setGoodsSectionSkuLockRecordDbNo(Integer goodsSectionSkuLockRecordDbNo) {
        this.goodsSectionSkuLockRecordDbNo = goodsSectionSkuLockRecordDbNo;
    }

    public Integer getGoodsSectionOrderRecordDbNo() {
        return goodsSectionOrderRecordDbNo == null ? 0 : goodsSectionOrderRecordDbNo;
    }

    public DbInfo setGoodsSectionOrderRecordDbNo(Integer goodsSectionOrderRecordDbNo) {
        this.goodsSectionOrderRecordDbNo = goodsSectionOrderRecordDbNo;
        return this;
    }

    public Integer getGoodsSectionInOutRecordDbNo() {
        return goodsSectionInOutRecordDbNo == null ? 0 : goodsSectionInOutRecordDbNo;
    }

    public void setGoodsSectionInOutRecordDbNo(Integer goodsSectionInOutRecordDbNo) {
        this.goodsSectionInOutRecordDbNo = goodsSectionInOutRecordDbNo;
    }

    public Integer getGoodsSectionInventoryOpLogDbNo() {
        return goodsSectionInventoryOpLogDbNo == null ? 0 : goodsSectionInventoryOpLogDbNo;
    }

    public void setGoodsSectionInventoryOpLogDbNo(Integer goodsSectionInventoryOpLogDbNo) {
        this.goodsSectionInventoryOpLogDbNo = goodsSectionInventoryOpLogDbNo;
    }

    public Integer getSkuAllocateTaskDbNo() {
        if (skuAllocateTaskDbNo == null) {
            return 0;
        }
        return skuAllocateTaskDbNo;
    }

    public void setSkuAllocateTaskDbNo(Integer skuAllocateTaskDbNo) {
        this.skuAllocateTaskDbNo = skuAllocateTaskDbNo;
    }

    public Integer getSkuAllocateFromRecordDbNo() {
        if (skuAllocateFromRecordDbNo == null) {
            return 0;
        }
        return skuAllocateFromRecordDbNo;
    }

    public void setSkuAllocateFromRecordDbNo(Integer skuAllocateFromRecordDbNo) {
        this.skuAllocateFromRecordDbNo = skuAllocateFromRecordDbNo;
    }

    public Integer getSkuAllocateToRecordDbNo() {
        if (skuAllocateToRecordDbNo == null) {
            return 0;
        }
        return skuAllocateToRecordDbNo;
    }

    public void setSkuAllocateToRecordDbNo(Integer skuAllocateToRecordDbNo) {
        this.skuAllocateToRecordDbNo = skuAllocateToRecordDbNo;
    }

    public Integer getUserLogisticsCompaniesJdDbNo() {
        if (userLogisticsCompaniesJdDbNo == null) {
            return 0;
        }
        return userLogisticsCompaniesJdDbNo;
    }

    public void setUserLogisticsCompaniesJdDbNo(Integer userLogisticsCompaniesJdDbNo) {
        this.userLogisticsCompaniesJdDbNo = userLogisticsCompaniesJdDbNo;
    }

    public Integer getGoodsSectionInventoryImportDbNo() {
        if (goodsSectionInventoryImportDbNo == null) {
            return 0;
        }
        return goodsSectionInventoryImportDbNo;
    }

    public void setGoodsSectionInventoryImportDbNo(Integer goodsSectionInventoryImportDbNo) {
        this.goodsSectionInventoryImportDbNo = goodsSectionInventoryImportDbNo;
    }

    public Integer getGiftPromotionDbNo() {
        if (giftPromotionDbNo == null) {
            return 0;
        }
        return giftPromotionDbNo;
    }

    public void setGiftPromotionDbNo(Integer giftPromotionDbNo) {
        this.giftPromotionDbNo = giftPromotionDbNo;
    }

    public Integer getGiftPromotionRuleDbNo() {
        if (giftPromotionRuleDbNo == null) {
            return 0;
        }
        return giftPromotionRuleDbNo;
    }

    public void setGiftPromotionRuleDbNo(Integer giftPromotionRuleDbNo) {
        this.giftPromotionRuleDbNo = giftPromotionRuleDbNo;
    }

    public Integer getGiftPromotionRuleSellerItemDbNo() {
        if (giftPromotionRuleSellerItemDbNo == null) {
            return 0;
        }
        return giftPromotionRuleSellerItemDbNo;
    }

    public void setGiftPromotionRuleSellerItemDbNo(Integer giftPromotionRuleSellerItemDbNo) {
        this.giftPromotionRuleSellerItemDbNo = giftPromotionRuleSellerItemDbNo;
    }

    public Integer getGiftPromotionRuleGiveItemDbNo() {
        if (giftPromotionRuleGiveItemDbNo == null) {
            return 0;
        }
        return giftPromotionRuleGiveItemDbNo;
    }

    public void setGiftPromotionRuleGiveItemDbNo(Integer giftPromotionRuleGiveItemDbNo) {
        this.giftPromotionRuleGiveItemDbNo = giftPromotionRuleGiveItemDbNo;
    }

    public Integer getGiftPromotionRuleGiveItemRangeDbNo() {
        if (giftPromotionRuleGiveItemRangeDbNo == null) {
            giftPromotionRuleGiveItemRangeDbNo = generateDbNo(companyId, COUNT_GIFT_PROMOTION_RULE_GIVE_RANGE_ITEM_NUM);
        }
        return giftPromotionRuleGiveItemRangeDbNo;
    }

    public void setGiftPromotionRuleGiveItemRangeDbNo(Integer giftPromotionRuleGiveItemRangeDbNo) {
        this.giftPromotionRuleGiveItemRangeDbNo = giftPromotionRuleGiveItemRangeDbNo;
    }

    public Integer getShelveOrderDbNo() {
        if (shelveOrderDbNo == null) {
            return 0;
        }
        return shelveOrderDbNo;
    }

    public void setShelveOrderDbNo(Integer shelveOrderDbNo) {
        this.shelveOrderDbNo = shelveOrderDbNo;
    }

    public Integer getShelveOrderDetailDbNo() {
        if (shelveOrderDetailDbNo == null) {
            return 0;
        }
        return shelveOrderDetailDbNo;
    }

    public void setShelveOrderDetailDbNo(Integer shelveOrderDetailDbNo) {
        this.shelveOrderDetailDbNo = shelveOrderDetailDbNo;
    }


    public Integer getAssoShelveWarehouseEntryDbNo() {
        if (assoShelveWarehouseEntryDbNo == null) {
            return 0;
        }
        return assoShelveWarehouseEntryDbNo;
    }

    public void setAssoShelveWarehouseEntryDbNo(Integer assoShelveWarehouseEntryDbNo) {
        this.assoShelveWarehouseEntryDbNo = assoShelveWarehouseEntryDbNo;
    }

    public Integer getAssoBusinessDocumentDbNO() {
        if (assoBusinessDocumentDbNO == null) {
            assoBusinessDocumentDbNO = generateDbNo(companyId, COUNT_ASSO_BUSINESS_DOCUMENT);
        }
        return assoBusinessDocumentDbNO;
    }

    public void setAssoBusinessDocumentDbNO(Integer assoBusinessDocumentDbNO) {
        this.assoBusinessDocumentDbNO = assoBusinessDocumentDbNO;
    }

    public Integer getGiftPromotionMatchTradeLogDbNo() {
        if (giftPromotionMatchTradeLogDbNo == null) {
            return 0;
        }
        return giftPromotionMatchTradeLogDbNo;
    }

    public void setGiftPromotionMatchTradeLogDbNo(Integer giftPromotionMatchTradeLogDbNo) {
        this.giftPromotionMatchTradeLogDbNo = giftPromotionMatchTradeLogDbNo;
    }

    public Integer getGiftPromotionRuleTagDbNo() {
        if (giftPromotionRuleTagDbNo == null) {
            giftPromotionRuleTagDbNo = generateDbNo(companyId, COUNT_GIFT_PROMOTION_RULE_TAG_NUM);
        }
        return giftPromotionRuleTagDbNo;
    }

    public void setGiftPromotionRuleTagDbNo(Integer giftPromotionRuleTagDbNo) {
        this.giftPromotionRuleTagDbNo = giftPromotionRuleTagDbNo;
    }

    public Integer getItemSuiteBridgeDbNo() {
        if (itemSuiteBridgeDbNo == null) {
            return 0;
        }
        return itemSuiteBridgeDbNo;
    }

    public void setItemSuiteBridgeDbNo(Integer itemSuiteBridgeDbNo) {
        this.itemSuiteBridgeDbNo = itemSuiteBridgeDbNo;
    }

    public Integer getTradeTempDbNo() {
        if (null == tradeTempDbNo) {
            tradeTempDbNo = 0;
        }
        return tradeTempDbNo;
    }

    public DbInfo setTradeTempDbNo(Integer tradeTempDbNo) {
        this.tradeTempDbNo = tradeTempDbNo;
        return this;
    }

    public Integer getReportTradeDbNo() {
        if (reportTradeDbNo == null) {
            return 0;
        }
        return reportTradeDbNo;
    }

    public DbInfo setReportTradeDbNo(Integer reportTradeDbNo) {
        this.reportTradeDbNo = reportTradeDbNo;
        return this;
    }

    public Integer getReportTradePackDbNo() {
        if (reportTradePackDbNo == null) {
            return 0;
        }
        return reportTradePackDbNo;
    }

    public DbInfo setReportTradePackDbNo(Integer reportTradePackDbNo) {
        this.reportTradePackDbNo = reportTradePackDbNo;
        return this;
    }

    public Integer getReportOrderDbNo() {
        if (reportOrderDbNo == null) {
            return 0;
        }
        return reportOrderDbNo;
    }

    public DbInfo setReportOrderDbNo(Integer reportOrderDbNo) {
        this.reportOrderDbNo = reportOrderDbNo;
        return this;
    }

    public Integer getStockDayReportDbNo() {
        if (stockDayReportDbNo == null) {
            return 0;
        }
        return stockDayReportDbNo;
    }

    public DbInfo setStockDayReportDbNo(Integer stockDayReportDbNo) {
        this.stockDayReportDbNo = stockDayReportDbNo;
        return this;
    }

    public Integer getReportMonthTradeDbNo() {
        if (reportMonthTradeDbNo == null) {
            return 0;
        }
        return reportMonthTradeDbNo;
    }

    public DbInfo setReportMonthTradeDbNo(Integer reportMonthTradeDbNo) {
        this.reportMonthTradeDbNo = reportMonthTradeDbNo;
        return this;
    }

    public DbInfo setReportAftersaleDbNo(Integer reportAftersaleDbNo) {
        this.reportAftersaleDbNo = reportAftersaleDbNo;
        return this;
    }

    public Integer getReportAftersaleDbNo() {
        if (reportAftersaleDbNo == null) {
            return 0;
        }
        return reportAftersaleDbNo;
    }

    public Integer getReportAsAnalyseDbNo() {
        if (reportAsAnalyseDbNo == null) {
            return 0;
        }
        return reportAsAnalyseDbNo;
    }

    public DbInfo setReportAsAnalyseDbNo(Integer reportAsAnalyseDbNo) {
        this.reportAsAnalyseDbNo = reportAsAnalyseDbNo;
        return this;
    }

    public Integer getPdaPickTradeDbNo() {
        if (pdaPickTradeDbNo == null) {
            return 0;
        }
        return pdaPickTradeDbNo;
    }

    public DbInfo setPdaPickTradeDbNo(Integer pdaPickTradeDbNo) {
        this.pdaPickTradeDbNo = pdaPickTradeDbNo;
        return this;
    }

    public Integer getPdaPickItemDbNo() {
        if (pdaPickItemDbNo == null) {
            return 0;
        }
        return pdaPickItemDbNo;
    }

    public DbInfo setPdaPickItemDbNo(Integer pdaPickItemDbNo) {
        this.pdaPickItemDbNo = pdaPickItemDbNo;
        return this;
    }

    public Integer getUserInvoicesTemplateDbNo() {
        if (userInvoicesTemplateDbNo == null) {
            return 0;
        }
        return userInvoicesTemplateDbNo;
    }

    public DbInfo setUserInvoicesTemplateDbNo(Integer userInvoicesTemplateDbNo) {
        this.userInvoicesTemplateDbNo = userInvoicesTemplateDbNo;
        return this;
    }

    public Integer getWavePickingDbNo() {
        if (wavePickingDbNo == null) {
            return 0;
        }
        return wavePickingDbNo;
    }

    public DbInfo setWavePickingDbNo(Integer wavePickingDbNo) {
        this.wavePickingDbNo = wavePickingDbNo;
        return this;
    }

    public Integer getOutSidRecyclePoolDbNo() {
        if (null == outSidRecyclePoolDbNo) {
            return 0;
        }
        return outSidRecyclePoolDbNo;
    }

    public void setOutSidRecyclePoolDbNo(Integer outSidRecyclePoolDbNo) {
        this.outSidRecyclePoolDbNo = outSidRecyclePoolDbNo;
    }

    public Integer getDepotTaskDbNo() {
        if (depotTaskDbNo == null) {
            return 0;
        }
        return depotTaskDbNo;
    }

    public DbInfo setDepotTaskDbNo(Integer depotTaskDbNo) {
        this.depotTaskDbNo = depotTaskDbNo;
        return this;
    }

    public Integer getDepotRecordDbNo() {
        if (depotRecordDbNo == null) {
            return 0;
        }
        return depotRecordDbNo;
    }

    public DbInfo setDepotRecordDbNo(Integer depotRecordDbNo) {
        this.depotRecordDbNo = depotRecordDbNo;
        return this;
    }

    public Integer getDepotOrderDetailDbNo() {
        if (depotOrderDetailDbNo == null) {
            return 0;
        }
        return depotOrderDetailDbNo;
    }

    public DbInfo setDepotOrderDetailDbNo(Integer depotOrderDetailDbNo) {
        this.depotOrderDetailDbNo = depotOrderDetailDbNo;
        return this;
    }

    public Integer getReportShopDbNo() {
        if (reportShopDbNo == null) {
            return 0;
        }
        return reportShopDbNo;
    }

    public DbInfo setReportShopDbNo(Integer reportShopDbNo) {
        this.reportShopDbNo = reportShopDbNo;
        return this;
    }

    public Integer getTradeSolrDbNo() {
        if (null == tradeSolrDbNo) {
            return 0;
        }
        return tradeSolrDbNo;
    }

    public DbInfo setTradeSolrDbNo(Integer tradeSolrDbNo) {
        this.tradeSolrDbNo = tradeSolrDbNo;
        return this;
    }

    public Integer getPurchaseReturnDbNo() {
        if (purchaseReturnDbNo == null) {
            return 0;
        }
        return purchaseReturnDbNo;
    }

    public DbInfo setPurchaseReturnDbNo(Integer purchaseReturnDbNo) {
        this.purchaseReturnDbNo = purchaseReturnDbNo;
        return this;
    }

    public Integer getPurchaseReturnDetailDbNo() {
        if (purchaseReturnDetailDbNo == null) {
            return 0;
        }
        return purchaseReturnDetailDbNo;
    }

    public DbInfo setPurchaseReturnDetailDbNo(Integer purchaseReturnDetailDbNo) {
        this.purchaseReturnDetailDbNo = purchaseReturnDetailDbNo;
        return this;
    }

    public Integer getWmsBusiLockDbNo() {
        if (wmsBusiLockDbNo == null) {
            return 0;
        }
        return wmsBusiLockDbNo;
    }

    public DbInfo setWmsBusiLockDbNo(Integer wmsBusiLockDbNo) {
        this.wmsBusiLockDbNo = wmsBusiLockDbNo;
        return this;
    }

    public Integer getStockBusiLockDbNo() {
        if (stockBusiLockDbNo == null) {
            stockBusiLockDbNo = 0;
        }
        return stockBusiLockDbNo;
    }

    public DbInfo setStockBusiLockDbNo(Integer stockBusiLockDbNo) {
        this.stockBusiLockDbNo = stockBusiLockDbNo;
        return this;
    }

    public Integer getItemShelveDetailDbNo() {
        if (itemShelveDetailDbNo == null) {
            return 0;
        }
        return itemShelveDetailDbNo;
    }

    public DbInfo setItemShelveDetailDbNo(Integer itemShelveDetailDbNo) {
        this.itemShelveDetailDbNo = itemShelveDetailDbNo;
        return this;
    }

    public Integer getDataTradeDbNo() {
        if (dataTradeDbNo == null) {
            return getTradeDbNo();
        }
        return dataTradeDbNo;
    }

    public DbInfo setDataTradeDbNo(Integer dataTradeDbNo) {
        this.dataTradeDbNo = dataTradeDbNo;
        return this;
    }

    public Integer getDataOrderDbNo() {
        if (dataOrderDbNo == null) {
            return getOrderDbNo();
        }
        return dataOrderDbNo;
    }

    public DbInfo setDataOrderDbNo(Integer dataOrderDbNo) {
        this.dataOrderDbNo = dataOrderDbNo;
        return this;
    }

    public Integer getDataStockSumDbNo() {
        if (dataStockSumDbNo == null) {
            dataStockSumDbNo = 0;
        }
        return dataStockSumDbNo;
    }

    public DbInfo setDataStockSumDbNo(Integer dataStockSumDbNo) {
        this.dataStockSumDbNo = dataStockSumDbNo;
        return this;
    }

    public Integer getTradeBlackDbNo() {
        if (tradeBlackDbNo == null) {
            int dbNo = getTradeDbNo();
            tradeBlackDbNo = dbNo >= COUNT_TRADE_BLACK_DB_NUM ? 0 : dbNo;
        }
        return tradeBlackDbNo;
    }

    public DbInfo setTradeBlackDbNo(Integer tradeBlackDbNo) {
        this.tradeBlackDbNo = tradeBlackDbNo;
        return this;
    }

    public Integer getAlipayBillFinanceDbNo() {
        if (alipayBillFinanceDbNo == null) {
            return 0;
        }
        return alipayBillFinanceDbNo;
    }

    public DbInfo setAlipayBillFinanceDbNo(Integer alipayBillFinanceDbNo) {
        this.alipayBillFinanceDbNo = alipayBillFinanceDbNo;
        return this;
    }

    public Integer getAlipayBillCompFinanceDbNo() {
        if (alipayBillCompFinanceDbNo == null) {
            return 0;
        }
        return alipayBillCompFinanceDbNo;
    }

    public DbInfo setAlipayBillCompFinanceDbNo(Integer alipayBillCompFinanceDbNo) {
        this.alipayBillCompFinanceDbNo = alipayBillCompFinanceDbNo;
        return this;
    }

    public Integer getTradePackSplitDbNo() {
        if (tradePackSplitDbNo == null) {
            tradePackSplitDbNo = 0;
        }
        return tradePackSplitDbNo;
    }

    public DbInfo setTradePackSplitDbNo(Integer tradePackSplitDbNo) {
        this.tradePackSplitDbNo = tradePackSplitDbNo;
        return this;
    }

    public Integer getOrderPackSplitDbNo() {
        if (orderPackSplitDbNo == null) {
            orderPackSplitDbNo = 0;
        }
        return orderPackSplitDbNo;
    }

    public DbInfo setOrderPackSplitDbNo(Integer orderPackSplitDbNo) {
        this.orderPackSplitDbNo = orderPackSplitDbNo;
        return this;
    }

    public Integer getDataRelationCidsDbNo() {
        if (dataRelationCidsDbNo == null) {
            dataRelationCidsDbNo = 0;
        }
        return dataRelationCidsDbNo;
    }

    public DbInfo setDataRelationCidsDbNo(Integer dataRelationCidsDbNo) {
        this.dataRelationCidsDbNo = dataRelationCidsDbNo;
        return this;
    }

    public Integer getDataItemWeekMonthSalesDbNo() {
        if (dataItemWeekMonthSalesDbNo == null) {
            dataItemWeekMonthSalesDbNo = 0;
        }
        return dataItemWeekMonthSalesDbNo;
    }

    public DbInfo setDataItemWeekMonthSalesDbNo(Integer dataItemWeekMonthSalesDbNo) {
        this.dataItemWeekMonthSalesDbNo = dataItemWeekMonthSalesDbNo;
        return this;
    }

    public Integer getDataReportRefundDbNo() {
        if (dataReportRefundDbNo == null) {
            dataReportRefundDbNo = 0;
        }
        return dataReportRefundDbNo;
    }

    public DbInfo setDataReportRefundDbNo(Integer dataReportRefundDbNo) {
        this.dataReportRefundDbNo = dataReportRefundDbNo;
        return this;
    }

    public Integer getDataCustomProfileDbNo() {
        if (dataCustomProfileDbNo == null) {
            dataCustomProfileDbNo = 0;
        }
        return dataCustomProfileDbNo;
    }

    public DbInfo setDataCustomProfileDbNo(Integer dataCustomProfileDbNo) {
        this.dataCustomProfileDbNo = dataCustomProfileDbNo;
        return this;
    }

    public Integer getDataCustomCertificateDbNo() {
        if (dataCustomCertificateDbNo == null) {
            dataCustomCertificateDbNo = 0;
        }
        return dataCustomCertificateDbNo;
    }

    public DbInfo setDataCustomCertificateDbNo(Integer dataCustomCertificateDbNo) {
        this.dataCustomCertificateDbNo = dataCustomCertificateDbNo;
        return this;
    }

    public Integer getDataCustomStockDetailDbNo() {
        if (dataCustomStockDetailDbNo == null) {
            dataCustomStockDetailDbNo = 0;
        }
        return dataCustomStockDetailDbNo;
    }

    public DbInfo setDataCustomStockDetailDbNo(Integer dataCustomStockDetailDbNo) {
        this.dataCustomStockDetailDbNo = dataCustomStockDetailDbNo;
        return this;
    }

    public Integer getItemSalePriceBrigeDbNo() {
        if (itemSalePriceBrigeDbNo == null) {
            itemSalePriceBrigeDbNo = 0;
        }
        return itemSalePriceBrigeDbNo;
    }

    public DbInfo setItemSalePriceBrigeDbNo(Integer itemSalePriceBrigeDbNo) {
        this.itemSalePriceBrigeDbNo = itemSalePriceBrigeDbNo;
        return this;
    }

    public Integer getUserKjExpressTemplateDbNo() {
        if (null == userKjExpressTemplateDbNo) {
            userKjExpressTemplateDbNo = generateDbNo(companyId, COUNT_USER_KJ_EXPRESS_TEMPLATE);
        }
        return userKjExpressTemplateDbNo;
    }

    public DbInfo setUserKjExpressTemplateDbNo(Integer userKjExpressTemplateDbNo) {
        this.userKjExpressTemplateDbNo = userKjExpressTemplateDbNo;
        return this;
    }

    public Integer getKjTemplateAddressDbNo() {
        if (null == kjTemplateAddressDbNo) {
            kjTemplateAddressDbNo = generateDbNo(companyId, COUNT_KJ_TEMPLATE_ADDRESS);
        }
        return kjTemplateAddressDbNo;
    }

    public Integer getDistributionTradeDbNo() {
        if (null == distributionTradeDbNo) {
            distributionTradeDbNo = generateDbNo(companyId, COUNT_DISTRIBUTION_TRADE);
        }
        return distributionTradeDbNo;
    }

    public Integer getCmOnlineDistributorDbNo() {
        if (null == cmOnlineDistributorDbNo) {
            cmOnlineDistributorDbNo = generateDbNo(companyId, COUNT_CM_ONLINE_DISTRIBUTOR_NUM);
        }
        return cmOnlineDistributorDbNo;
    }

    public void setCmOnlineDistributorDbNo(Integer cmOnlineDistributorDbNo) {
        this.cmOnlineDistributorDbNo = cmOnlineDistributorDbNo;
    }

    public Integer getCmConsignTradeRecordDbNo() {
        if (null == cmConsignTradeRecordDbNo) {
            cmConsignTradeRecordDbNo = generateDbNo(companyId, COUNT_CM_CONSIGN_TRADE_RECORD_NUM);
        }
        return cmConsignTradeRecordDbNo;
    }

    public void setCmConsignTradeRecordDbNo(Integer cmConsignTradeRecordDbNo) {
        this.cmConsignTradeRecordDbNo = cmConsignTradeRecordDbNo;
    }

    public Integer getCmRechargeRecordDbNo() {
        if (null == cmRechargeRecordDbNo) {
            cmRechargeRecordDbNo = generateDbNo(companyId, COUNT_CM_RECHARGE_RECORD_NUM);
        }
        return cmRechargeRecordDbNo;
    }

    public void setCmRechargeRecordDbNo(Integer cmRechargeRecordDbNo) {
        this.cmRechargeRecordDbNo = cmRechargeRecordDbNo;
    }

    public Integer getCmShopDistributorRelDbNo() {
        if (null == cmShopDistributorRelDbNo) {
            cmShopDistributorRelDbNo = generateDbNo(companyId, COUNT_CM_SHOP_DISTRIBUTOR_REL_NUM);
        }
        return cmShopDistributorRelDbNo;
    }

    public void setCmShopDistributorRelDbNo(Integer cmShopDistributorRelDbNo) {
        this.cmShopDistributorRelDbNo = cmShopDistributorRelDbNo;
    }

    public Integer getCmIdLockDbNo() {
        if (null == cmIdLockDbNo) {
            cmIdLockDbNo = generateDbNo(companyId, COUNT_CM_ID_LOCK_NUM);
        }
        return cmIdLockDbNo;
    }

    public void setCmIdLockDbNo(Integer cmIdLockDbNo) {
        this.cmIdLockDbNo = cmIdLockDbNo;
    }

    public Integer getStockProcessOrderDbNo() {
        if (null == stockProcessOrderDbNo) {
            stockProcessOrderDbNo = generateDbNo(companyId, COUNT_STOCK_PROCESS_ORDER_NUM);
        }
        return stockProcessOrderDbNo;
    }

    public void setStockProcessOrderDbNo(Integer stockProcessOrderDbNo) {
        this.stockProcessOrderDbNo = stockProcessOrderDbNo;
    }

    public Integer getStockProcessMaterialDbNo() {
        if (null == stockProcessMaterialDbNo) {
            stockProcessMaterialDbNo = generateDbNo(companyId, COUNT_STOCK_PROCESS_MATERIAL_NUM);
        }
        return stockProcessMaterialDbNo;
    }

    public void setStockProcessMaterialDbNo(Integer stockProcessMaterialDbNo) {
        this.stockProcessMaterialDbNo = stockProcessMaterialDbNo;
    }

    public Integer getStockProcessGoodsSectionDbNo() {
        if (null == stockProcessGoodsSectionDbNo) {
            stockProcessGoodsSectionDbNo = generateDbNo(companyId, COUNT_STOCK_PROCESS_GOODS_SECTION_NUM);
        }
        return stockProcessGoodsSectionDbNo;
    }

    public void setStockProcessGoodsSectionDbNo(Integer stockProcessGoodsSectionDbNo) {
        this.stockProcessGoodsSectionDbNo = stockProcessGoodsSectionDbNo;
    }

    public Integer getStockProcessOpLogDbNo() {
        if (null == stockProcessOpLogDbNo) {
            stockProcessOpLogDbNo = generateDbNo(companyId, COUNT_STOCK_PROCESS_OP_LOG_NUM);
        }
        return stockProcessOpLogDbNo;
    }

    public void setStockProcessOpLogDbNo(Integer stockProcessOpLogDbNo) {
        this.stockProcessOpLogDbNo = stockProcessOpLogDbNo;
    }

    public Integer getStockProductOrderDbNo() {
        if (null == stockProductOrderDbNo) {
            stockProductOrderDbNo = generateDbNo(companyId, COUNT_STOCK_PRODUCT_ORDER_NUM);
        }
        return stockProductOrderDbNo;
    }

    public void setStockProductOrderDbNo(Integer stockProductOrderDbNo) {
        this.stockProductOrderDbNo = stockProductOrderDbNo;
    }

    public Integer getStockProductMaterialDbNo() {
        if (null == stockProductMaterialDbNo) {
            stockProductMaterialDbNo = generateDbNo(companyId, COUNT_STOCK_PRODUCT_MATERIAL_NUM);
        }
        return stockProductMaterialDbNo;
    }

    public void setStockProductMaterialDbNo(Integer stockProductMaterialDbNo) {
        this.stockProductMaterialDbNo = stockProductMaterialDbNo;
    }

    public Integer getStockProductGoodDbNo() {
        if (null == stockProductGoodDbNo) {
            stockProductGoodDbNo = generateDbNo(companyId, COUNT_STOCK_PRODUCT_GOOD_NUM);
        }
        return stockProductGoodDbNo;
    }

    public void setStockProductGoodDbNo(Integer stockProductGoodDbNo) {
        this.stockProductGoodDbNo = stockProductGoodDbNo;
    }

    public Integer getOrderDownloadAssociationDbNo() {
        if (null == orderDownloadAssociationDbNo) {
            orderDownloadAssociationDbNo = generateDbNo(companyId, ORDER_DOWNLOAD_ASSOCIATION);
        }
        return orderDownloadAssociationDbNo;
    }

    public void setOrderDownloadAssociationDbNo(Integer orderDownloadAssociationDbNo) {
        this.orderDownloadAssociationDbNo = orderDownloadAssociationDbNo;
    }

    public void setDistributionTradeDbNo(Integer distributionTradeDbNo) {
        this.distributionTradeDbNo = distributionTradeDbNo;
    }

    public Integer getAsLockDbNo() {
        if (null == asLockDbNo) {
            asLockDbNo = generateDbNo(companyId, COUNT_AS_LOCK_NUM);
        }
        return asLockDbNo;
    }

    public void setAsLockDbNo(Integer asLockDbNo) {
        this.asLockDbNo = asLockDbNo;
    }

    public DbInfo setKjTemplateAddressDbNo(Integer kjTemplateAddressDbNo) {
        this.kjTemplateAddressDbNo = kjTemplateAddressDbNo;
        return this;
    }

    // 适用于新加的表，老表不要通过该方法获取分表号
    private Integer generateDbNo(Long companyId, Integer num) {
        if (null != companyId) {
            return (int) (companyId % num);
        } else {
            return 0;
        }
    }

    public Integer getLogisticsTrackingPollPoolDbNo() {
        if (null == logisticsTrackingPollPoolDbNo) {
            logisticsTrackingPollPoolDbNo = generateDbNo(companyId, COUNT_LOGISTICS_TRACKING_POLL_POOL_NUM);
        }
        return logisticsTrackingPollPoolDbNo;
    }

    public void setLogisticsTrackingPollPoolDbNo(Integer logisticsTrackingPollPoolDbNo) {
        this.logisticsTrackingPollPoolDbNo = logisticsTrackingPollPoolDbNo;
    }

    public Integer getTradeTagRuleDbNo() {
        if (null == tradeTagRuleDbNo) {
            tradeTagRuleDbNo = generateDbNo(companyId, COUNT_TRADE_TAG_RULE_NUM);
        }
        return tradeTagRuleDbNo;
    }

    public void setTradeTagRuleDbNo(Integer tradeTagRuleDbNo) {
        this.tradeTagRuleDbNo = tradeTagRuleDbNo;
    }


    public Integer getTradeLabelDbNo() {
        if (null == tradeLabelDbNo) {
            tradeLabelDbNo = generateDbNo(companyId, COUNT_TRADE_LABEL_NUM);
        }
        return tradeLabelDbNo;
    }

    public void setTradeLabelDbNo(Integer tradeLabelDbNo) {
        this.tradeLabelDbNo = tradeLabelDbNo;
    }

    public Integer getTradeLabelRecordDbNo() {
        if (null == tradeLabelRecordDbNo) {
            tradeLabelRecordDbNo = generateDbNo(companyId, COUNT_TRADE_LABEL_RECORD_NUM);
        }
        return tradeLabelRecordDbNo;
    }

    public void setTradeLabelRecordDbNo(Integer tradeLabelRecordDbNo) {
        this.tradeLabelRecordDbNo = tradeLabelRecordDbNo;
    }


    public Integer getReplenishTaskDbNo() {
        if (replenishTaskDbNo == null) {
            replenishTaskDbNo = generateDbNo(companyId, COUNT_REPLENISH_TASK_DB_NUM);
        }
        return replenishTaskDbNo;
    }

    public Integer getShelveTaskDbNo() {
        if (shelveTaskDbNo == null) {
            shelveTaskDbNo = generateDbNo(companyId, COUNT_SHELVE_TASK_DB_NUM);
        }
        return shelveTaskDbNo;
    }

    public Integer getShelveTaskDetailDbNo() {
        if (shelveTaskDetailDbNo == null) {
            shelveTaskDetailDbNo = generateDbNo(companyId, COUNT_SHELVE_TASK_DETAIL_DB_NUM);
        }
        return shelveTaskDetailDbNo;
    }

    public void setShelveTaskDbNo(Integer shelveTaskDbNo) {
        this.shelveTaskDbNo = shelveTaskDbNo;
    }

    public void setShelveTaskDetailDbNo(Integer shelveTaskDetailDbNo) {
        this.shelveTaskDetailDbNo = shelveTaskDetailDbNo;
    }

    public Integer getItemBatchDbNo() {
        if (itemBatchDbNo == null) {
            itemBatchDbNo = generateDbNo(companyId, COUNT_ITEM_BATCH_DB_NUM);
        }
        return itemBatchDbNo;
    }

    public DbInfo setReplenishTaskDbNo(Integer replenishTaskDbNo) {
        this.replenishTaskDbNo = replenishTaskDbNo;
        return this;
    }

    public Integer getReplenishTaskDetailDbNo() {
        if (replenishTaskDetailDbNo == null) {
            replenishTaskDetailDbNo = generateDbNo(companyId, COUNT_REPLENISH_TASK_DETAIL_DB_NUM);
        }
        return replenishTaskDetailDbNo;
    }

    public DbInfo setReplenishTaskDetailDbNo(Integer replenishTaskDetailDbNo) {
        this.replenishTaskDetailDbNo = replenishTaskDetailDbNo;
        return this;
    }

    public Integer getFastOutTradeDbNo() {
        if (null == fastOutTradeDbNo) {
            fastOutTradeDbNo = generateDbNo(companyId, COUNT_FAST_OUT_TRADE_NUM);
        }
        return fastOutTradeDbNo;
    }

    public void setFastOutTradeDbNo(Integer fastOutTradeDbNo) {
        this.fastOutTradeDbNo = fastOutTradeDbNo;
    }

    public Integer getItemCustomerBridgeDbNo() {
        if (null == itemCustomerBridgeDbNo) {
            itemCustomerBridgeDbNo = generateDbNo(companyId, COUNT_ITEM_CUSTOMER_BRIDGE_NUM);
        }
        return itemCustomerBridgeDbNo;
    }

    public void setItemCustomerBridgeDbNo(Integer itemCustomerBridgeDbNo) {
        this.itemCustomerBridgeDbNo = itemCustomerBridgeDbNo;
    }

    public Integer getPackmaIngoodsDbNo() {
        if (packmaIngoodsDbNo == null) {
            packmaIngoodsDbNo = generateDbNo(companyId, COUNT_PACKMA_INGOODS);
        }
        return packmaIngoodsDbNo;
    }

    public void setPackmaIngoodsDbNo(Integer packmaIngoodsDbNo) {
        this.packmaIngoodsDbNo = packmaIngoodsDbNo;
    }

    public Integer getPackmaConsumeLogDbNo() {
        if (packmaConsumeLogDbNo == null) {
            packmaConsumeLogDbNo = generateDbNo(companyId, COUNT_PACKMA_CONSUME_LOG);
        }
        return packmaConsumeLogDbNo;
    }

    public void setPackmaConsumeLogDbNo(Integer packmaConsumeLogDbNo) {
        this.packmaConsumeLogDbNo = packmaConsumeLogDbNo;
    }

    public Integer getItemBoxDbNo() {
        if (itemBoxDbNo == null) {
            itemBoxDbNo = generateDbNo(companyId, COUNT_ITEM_BOX_NUM);
        }
        return itemBoxDbNo;
    }

    public void setItemBoxDbNo(Integer itemBoxDbNo) {
        this.itemBoxDbNo = itemBoxDbNo;
    }

    public Integer getItemBoxSingleDbNo() {
        if (itemBoxSingleDbNo == null) {
            itemBoxSingleDbNo = generateDbNo(companyId, COUNT_ITEM_BOX_SINGLE_NUM);
        }
        return itemBoxSingleDbNo;
    }

    public void setItemBoxSingleDbNo(Integer itemBoxSingleDbNo) {
        this.itemBoxSingleDbNo = itemBoxSingleDbNo;
    }

    public Integer getSplitConfigRuleDbNo() {
        if (splitConfigRuleDbNo == null) {
            splitConfigRuleDbNo = generateDbNo(companyId, COUNT_SPLIT_CONFIG_RULE_NUM);
        }
        return splitConfigRuleDbNo;
    }

    public Integer getSplitConfigRuleFormulaDbNo() {
        if (splitConfigRuleFormulaDbNo == null) {
            splitConfigRuleFormulaDbNo = generateDbNo(companyId, COUNT_SPLIT_CONFIG_RULE_FORMULA_NUM);
        }
        return splitConfigRuleFormulaDbNo;
    }

    public void setSplitConfigRuleFormulaDbNo(Integer splitConfigRuleFormulaDbNo) {
        this.splitConfigRuleFormulaDbNo = splitConfigRuleFormulaDbNo;
    }

    public void setSplitConfigRuleDbNo(Integer splitConfigRuleDbNo) {
        this.splitConfigRuleDbNo = splitConfigRuleDbNo;
    }

    public Integer getSplitConfigRuleDetailDbNo() {
        if (splitConfigRuleDetailDbNo == null) {
            splitConfigRuleDetailDbNo = generateDbNo(companyId, COUNT_SPLIT_CONFIG_RULE_DETAIL_NUM);
        }
        return splitConfigRuleDetailDbNo;
    }

    public void setSplitConfigRuleDetailDbNo(Integer splitConfigRuleDetailDbNo) {
        this.splitConfigRuleDetailDbNo = splitConfigRuleDetailDbNo;
    }

    public Integer getClearOrderDbNo() {
        if (clearOrderDbNo == null) {
            clearOrderDbNo = generateDbNo(companyId, COUNT_FINANCE_CLEAR_ORDER_NUM);
        }
        return clearOrderDbNo;
    }

    public void setClearOrderDbNo(Integer clearOrderDbNo) {
        this.clearOrderDbNo = clearOrderDbNo;
    }

    public Integer getSettleOrderDbNo() {
        if (settleOrderDbNo == null) {
            settleOrderDbNo = generateDbNo(companyId, COUNT_FINANCE_SETTLE_ORDER_NUM);
        }
        return settleOrderDbNo;
    }

    public void setSettleOrderDbNo(Integer settleOrderDbNo) {
        this.settleOrderDbNo = settleOrderDbNo;
    }

    public Integer getClearOrderDetailDbNo() {
        if (clearOrderDetailDbNo == null) {
            clearOrderDetailDbNo = generateDbNo(companyId, COUNT_FINANCE_CLEAR_ORDER_DETAIL_NUM);
        }
        return clearOrderDetailDbNo;
    }

    public void setClearOrderDetailDbNo(Integer clearOrderDetailDbNo) {
        this.clearOrderDetailDbNo = clearOrderDetailDbNo;
    }

    public Integer getSettleOrderDetailDbNo() {
        if (settleOrderDetailDbNo == null) {
            settleOrderDetailDbNo = generateDbNo(companyId, COUNT_FINANCE_SETTLE_ORDER_DETAIL_NUM);
        }
        return settleOrderDetailDbNo;
    }

    public void setSettleOrderDetailDbNo(Integer settleOrderDetailDbNo) {
        this.settleOrderDetailDbNo = settleOrderDetailDbNo;
    }

    public Integer getFinanceLockDbNo() {
        if (financeLockDbNo == null) {
            financeLockDbNo = generateDbNo(companyId, COUNT_FINANCE_LOCK_NUM);
        }
        return financeLockDbNo;
    }

    public void setFinanceLockDbNo(Integer financeLockDbNo) {
        this.financeLockDbNo = financeLockDbNo;
    }

    public Integer getFinanceOpLogDbNo() {
        if (financeOpLogDbNo == null) {
            financeOpLogDbNo = generateDbNo(companyId, COUNT_FINANCE_OP_LOG_NUM);
        }
        return financeOpLogDbNo;
    }

    public void setFinanceOpLogDbNo(Integer financeOpLogDbNo) {
        this.financeOpLogDbNo = financeOpLogDbNo;
    }

    public Integer getWashLabelDbNo() {
        if (washLabelDbNo == null) {
            washLabelDbNo = generateDbNo(companyId, COUNT_WASH_LABEL_NUM);
        }
        return washLabelDbNo;
    }

    public void setWashLabelDbNo(Integer washLabelDbNo) {
        this.washLabelDbNo = washLabelDbNo;
    }

    public Integer getItemWashLabelBridgeDbNo() {
        if (itemWashLabelBridgeDbNo == null) {
            itemWashLabelBridgeDbNo = generateDbNo(companyId, COUNT_ITEM_WASH_LABEL_BRIDGE_NUM);
        }
        return itemWashLabelBridgeDbNo;
    }

    public void setItemWashLabelBridgeDbNo(Integer itemWashLabelBridgeDbNo) {
        this.itemWashLabelBridgeDbNo = itemWashLabelBridgeDbNo;
    }

    public Integer getWorkingStorageSectionInOutRecordDbNo() {
        if (null == workingStorageSectionInOutRecordDbNo) {
            workingStorageSectionInOutRecordDbNo = generateDbNo(companyId, COUNT_WORKING_STORAGE_SECTION_IN_OUT_RECORD_NUM);
        }
        return workingStorageSectionInOutRecordDbNo;
    }

    public void setWorkingStorageSectionInOutRecordDbNo(Integer workingStorageSectionInOutRecordDbNo) {
        this.workingStorageSectionInOutRecordDbNo = workingStorageSectionInOutRecordDbNo;
    }

    public Integer getAllocateGoodsRecordDbNo() {
        if (null == allocateGoodsRecordDbNo) {
            allocateGoodsRecordDbNo = generateDbNo(companyId, COUNT_ALLOCATE_GOODS_RECORD_NUM);
        }
        return allocateGoodsRecordDbNo;
    }

    public void setAllocateGoodsRecordDbNo(Integer allocateGoodsRecordDbNo) {
        this.allocateGoodsRecordDbNo = allocateGoodsRecordDbNo;
    }

    public Integer getWavePickerDbNo() {
        if (null == wavePickerDbNo) {
            wavePickerDbNo = generateDbNo(companyId, COUNT_WAVE_PICKER);
        }
        return wavePickerDbNo;
    }

    public void setWavePickerDbNo(Integer wavePickerDbNo) {
        this.wavePickerDbNo = wavePickerDbNo;
    }

    public Integer getItemChangeRelationDbNo() {
        if (itemChangeRelationDbNo == null) {
            return getDmjItemDbNo();
        }
        return itemChangeRelationDbNo;
    }

    public void setItemChangeRelationDbNo(Integer itemChangeRelationDbNo) {
        this.itemChangeRelationDbNo = itemChangeRelationDbNo;
    }

    public Integer getPrintViewDbNo() {
        if (null == printViewDbNo) {
            printViewDbNo = generateDbNo(companyId, COUNT_PRINT_VIEW_NUM);
        }
        return printViewDbNo;
    }

    public void setPrintViewDbNo(Integer printViewDbNo) {
        this.printViewDbNo = printViewDbNo;
    }

    public Integer getWaveTraceDbNo() {
        if (null == waveTraceDbNo) {
            waveTraceDbNo = generateDbNo(companyId, COUNT_WAVE_TRACE_NUM);
        }
        return waveTraceDbNo;
    }

    public void setWaveTraceDbNo(Integer waveTraceDbNo) {
        this.waveTraceDbNo = waveTraceDbNo;
    }

    public Integer getReserveAreaPickGoodsDbNo() {
        if (null == reserveAreaPickGoodsDbNo) {
            reserveAreaPickGoodsDbNo = generateDbNo(companyId, COUNT_RESERVE_AREA_PICK_GOODS_NUM);
        }
        return reserveAreaPickGoodsDbNo;
    }

    public void setReserveAreaPickGoodsDbNo(Integer reserveAreaPickGoodsDbNo) {
        this.reserveAreaPickGoodsDbNo = reserveAreaPickGoodsDbNo;
    }

    public Integer getWaveGoodsMemorandumDbNo() {
        if (null == waveGoodsMemorandumDbNo) {
            waveGoodsMemorandumDbNo = generateDbNo(companyId, COUNT_WAVE_GOODS_MEMORANDUM_NUM);
        }
        return waveGoodsMemorandumDbNo;
    }

    public void setWaveGoodsMemorandumDbNo(Integer waveGoodsMemorandumDbNo) {
        this.waveGoodsMemorandumDbNo = waveGoodsMemorandumDbNo;
    }

    public Integer getTradeChangeItemMiddleDbNo() {
        if (null == tradeChangeItemMiddleDbNo) {
            tradeChangeItemMiddleDbNo = generateDbNo(companyId, COUNT_TRADE_CHANGE_ITEM_MIDDLE_NUM);
        }
        return tradeChangeItemMiddleDbNo;
    }

    public void setTradeChangeItemMiddleDbNo(Integer tradeChangeItemMiddleDbNo) {
        this.tradeChangeItemMiddleDbNo = tradeChangeItemMiddleDbNo;
    }

    public Integer getPositionOrderUniqueCodeMiddleDbNo() {
        if (null == positionOrderUniqueCodeMiddleDbNo) {
            positionOrderUniqueCodeMiddleDbNo = generateDbNo(companyId, COUNT_POSITION_ORDER_UNIQUE_CODE_MIDDLE_NUM);
        }
        return positionOrderUniqueCodeMiddleDbNo;
    }

    public void setPositionOrderUniqueCodeMiddleDbNo(Integer positionOrderUniqueCodeMiddleDbNo) {
        this.positionOrderUniqueCodeMiddleDbNo = positionOrderUniqueCodeMiddleDbNo;
    }

    public Integer getPositionOrderUniqueCodeDetailMiddleDbNo() {
        if (null == positionOrderUniqueCodeDetailMiddleDbNo) {
            positionOrderUniqueCodeDetailMiddleDbNo = generateDbNo(companyId, COUNT_POSITION_ORDER_UNIQUE_CODE_DETAIL_MIDDLE_NUM);
        }
        return positionOrderUniqueCodeDetailMiddleDbNo;
    }

    public void setPositionOrderUniqueCodeDetailMiddleDbNo(Integer positionOrderUniqueCodeDetailMiddleDbNo) {
        this.positionOrderUniqueCodeDetailMiddleDbNo = positionOrderUniqueCodeDetailMiddleDbNo;
    }

    public Integer getWmsPerformanceMiddleDbNo() {
        if (null == wmsPerformanceMiddleDbNo) {
            wmsPerformanceMiddleDbNo = generateDbNo(companyId, COUNT_WMS_PERFORMANCE_MIDDLE_NUM);
        }
        return wmsPerformanceMiddleDbNo;
    }

    public void setWmsPerformanceMiddleDbNo(Integer wmsPerformanceMiddleDbNo) {
        this.wmsPerformanceMiddleDbNo = wmsPerformanceMiddleDbNo;
    }

    public Integer getOrderUniqueCodeHotSaleDbNo() {
        if (null == orderUniqueCodeHotSaleDbNo) {
            orderUniqueCodeHotSaleDbNo = generateDbNo(companyId, COUNT_ORDER_UNIQUE_CODE_HOT_SALE_NUM);
        }
        return orderUniqueCodeHotSaleDbNo;
    }

    public void setOrderUniqueCodeHotSaleDbNo(Integer orderUniqueCodeHotSaleDbNo) {
        this.orderUniqueCodeHotSaleDbNo = orderUniqueCodeHotSaleDbNo;
    }

    public Integer getWaveUniqueCodeTagDbNo() {
        if (null == waveUniqueCodeTagDbNo) {
            waveUniqueCodeTagDbNo = generateDbNo(companyId, UNIQUE_TAG_DATA);
        }
        return waveUniqueCodeTagDbNo;
    }

    public void setWaveUniqueCodeTagDbNo(Integer waveUniqueCodeTagDbNo) {
        this.waveUniqueCodeTagDbNo = waveUniqueCodeTagDbNo;
    }

    public Integer getWarehousePositionConfigDbNo() {
        if (null == warehousePositionConfigDbNo) {
            warehousePositionConfigDbNo = generateDbNo(companyId, COUNT_WAREHOUSE_POSITION_CONFIG);
        }
        return warehousePositionConfigDbNo;
    }

    public void setWarehousePositionConfigDbNo(Integer warehousePositionConfigDbNo) {
        this.warehousePositionConfigDbNo = warehousePositionConfigDbNo;
    }

    public Integer getHotItemDbNo() {
        if (null == hotItemDbNo) {
            hotItemDbNo = generateDbNo(companyId, COUNT_TRADE_HOT_ITEM);
        }
        return hotItemDbNo;
    }

    public void setHotItemDbNo(Integer hotItemDbNo) {
        this.hotItemDbNo = hotItemDbNo;
    }

    public Integer getSaleTradeDbNo() {
        if (null == saleTradeDbNo) {
            saleTradeDbNo = generateDbNo(companyId, COUNT_TRADE_SALE_TRADE);
        }
        return saleTradeDbNo;
    }

    public void setSaleTradeDbNo(Integer saleTradeDbNo) {
        this.saleTradeDbNo = saleTradeDbNo;
    }

    public Integer getNoSalesReportDbNo() {
        if (null == noSalesReportDbNo) {
            noSalesReportDbNo = generateDbNo(companyId, COUNT_NO_SALES_REPORT);
        }
        return noSalesReportDbNo;
    }

    public void setNoSalesReportDbNo(Integer noSalesReportDbNo) {
        this.noSalesReportDbNo = noSalesReportDbNo;
    }

    public Integer getItemSalesDbNo() {
        if (null == itemSalesDbNo) {
            itemSalesDbNo = generateDbNo(companyId, COUNT_ITEM_SALES);
        }
        return itemSalesDbNo;
    }

    public void setItemSalesDbNo(Integer itemSalesDbNo) {
        this.itemSalesDbNo = itemSalesDbNo;
    }

    public Integer getSaleOrderDbNo() {
        if (null == saleOrderDbNo) {
            saleOrderDbNo = generateDbNo(companyId, COUNT_TRADE_SALE_ORDER);
        }
        return saleOrderDbNo;
    }

    public void setSaleOrderDbNo(Integer saleOrderDbNo) {
        this.saleOrderDbNo = saleOrderDbNo;
    }

    public Integer getOdpsOrderSnapDbNo() {
        if (null == odpsOrderSnapDbNo) {
            odpsOrderSnapDbNo = generateDbNo(companyId, ODPS_ORDER_SNAP);
        }
        return odpsOrderSnapDbNo;
    }

    public void setOdpsOrderSnapDbNo(Integer odpsOrderSnapDbNo) {
        this.odpsOrderSnapDbNo = odpsOrderSnapDbNo;
    }

    public Integer getReportTradeExtraDbNo() {
        if (null == reportTradeExtraDbNo) {
            reportTradeExtraDbNo = generateDbNo(companyId, REPORT_TRADE_EXTRA);
        }
        return reportTradeExtraDbNo;
    }

    public void setReportTradeExtraDbNo(Integer reportTradeExtraDbNo) {
        this.reportTradeExtraDbNo = reportTradeExtraDbNo;
    }

    public Integer getReportOrderExtraDbNo() {
        if (null == reportOrderExtraDbNo) {
            reportOrderExtraDbNo = generateDbNo(companyId, REPORT_ORDER_EXTRA);
        }
        return reportOrderExtraDbNo;
    }

    public void setReportOrderExtraDbNo(Integer reportOrderExtraDbNo) {
        this.reportOrderExtraDbNo = reportOrderExtraDbNo;
    }

    public Integer getWavePickStaffReportDbNo() {
        if (null == wavePickStaffReportDbNo) {
            wavePickStaffReportDbNo = generateDbNo(companyId, COUNT_WAVE_STAFF_PICK_REPORT);
        }
        return wavePickStaffReportDbNo;
    }

    public void setWavePickStaffReportDbNo(Integer wavePickStaffReportDbNo) {
        this.wavePickStaffReportDbNo = wavePickStaffReportDbNo;
    }

    public Integer getOrderPickStaffReportDbNo() {
        if (null == orderPickStaffReportDbNo) {
            orderPickStaffReportDbNo = generateDbNo(companyId, COUNT_ORDER_STAFF_PICK_REPORT);
        }
        return orderPickStaffReportDbNo;
    }

    public void setOrderPickStaffReportDbNo(Integer orderPickStaffReportDbNo) {
        this.orderPickStaffReportDbNo = orderPickStaffReportDbNo;
    }

    public Integer getTradePayDbNo() {
        if (null == tradePayDbNo) {
            tradePayDbNo = generateDbNo(companyId, COUNT_TRADE_PAY);
        }
        return tradePayDbNo;
    }

    public void setTradePayDbNo(Integer tradePayDbNo) {
        this.tradePayDbNo = tradePayDbNo;
    }


    public Integer getAsPackageQueryLogDbNo() {
        if (null == asPackageQueryLogDbNo) {
            asPackageQueryLogDbNo = generateDbNo(companyId, AS_PACKAGE_QUERY_LOG);
        }
        return asPackageQueryLogDbNo;
    }

    public void setAsPackageQueryLogDbNo(Integer asPackageQueryLogDbNo) {
        this.asPackageQueryLogDbNo = asPackageQueryLogDbNo;
    }

    public Integer getPickerPrintLogDbNo() {
        if (pickerPrintLogDbNo == null) {
            pickerPrintLogDbNo = generateDbNo(companyId, PICKER_PRINT_LOG);
        }
        return pickerPrintLogDbNo;
    }

    public void setPickerPrintLogDbNo(Integer pickerPrintLogDbNo) {
        this.pickerPrintLogDbNo = pickerPrintLogDbNo;
    }

    public Integer getPickerPrintLogDetailDbNo() {
        if (pickerPrintLogDetailDbNo == null) {
            pickerPrintLogDetailDbNo = generateDbNo(companyId, PICKER_PRINT_LOG_DETAIL);
        }
        return pickerPrintLogDetailDbNo;
    }

    public void setPickerPrintLogDetailDbNo(Integer pickerPrintLogDetailDbNo) {
        this.pickerPrintLogDetailDbNo = pickerPrintLogDetailDbNo;
    }

    public Integer getSaleTradeFinanceDetailDbNo() {
        if (null == saleTradeFinanceDetailDbNo) {
            saleTradeFinanceDetailDbNo = generateDbNo(companyId, COUNT_SALE_TRADE_FINANCE_DETAIL);
        }
        return saleTradeFinanceDetailDbNo;
    }

    public void setSaleTradeFinanceDetailDbNo(Integer saleTradeFinanceDetailDbNo) {
        this.saleTradeFinanceDetailDbNo = saleTradeFinanceDetailDbNo;
    }

    public Integer getReceiptTraceDbNo() {
        if (receiptTraceDbNo == null) {
            receiptTraceDbNo = generateDbNo(companyId, COUNT_RECEIPT_TRACE_DB_NO);
        }
        return receiptTraceDbNo;
    }

    public void setReceiptTraceDbNo(Integer receiptTraceDbNo) {
        this.receiptTraceDbNo = receiptTraceDbNo;
    }

    public Integer getSalesWirelessPrintDbNo() {
        if (null == salesWirelessPrintDbNo) {
            salesWirelessPrintDbNo = generateDbNo(companyId, COUNT_SALES_WIRELESS_PRINT);
        }
        return salesWirelessPrintDbNo;
    }

    public void setSalesWirelessPrintDbNo(Integer salesWirelessPrintDbNo) {
        this.salesWirelessPrintDbNo = salesWirelessPrintDbNo;
    }

    public Integer getPrintDivideLogDbNo() {
        if (printDivideLogDbNo == null) {
            printDivideLogDbNo = generateDbNo(companyId, COUNT_PRINT_DIVIDE_LOG);
        }
        return printDivideLogDbNo;
    }

    public void setPrintDivideLogDbNo(Integer printDivideLogDbNo) {
        this.printDivideLogDbNo = printDivideLogDbNo;
    }

    public Integer getPrintTaskDataDbNo() {
        if (null == printTaskDataDbNo) {
            printTaskDataDbNo = generateDbNo(companyId, COUNT_PRINT_TASK_DATA);
        }
        return printTaskDataDbNo;
    }

    public void setPrintTaskDataDbNo(Integer printTaskDataDbNo) {
        this.printTaskDataDbNo = printTaskDataDbNo;
    }

    public Integer getMerchantItemMsgDetailDbNo() {
        if (null == merchantItemMsgDetailDbNo) {
            merchantItemMsgDetailDbNo = generateDbNo(companyId, COUNT_MERCHANT_TASK_DETAIL);
        }
        return merchantItemMsgDetailDbNo;
    }

    public void setMerchantItemMsgDetailDbNo(Integer merchantItemMsgDetailDbNo) {
        this.merchantItemMsgDetailDbNo = merchantItemMsgDetailDbNo;
    }

    public Integer getMerchantPlusMsgDetailDbNo() {
        if (null == merchantPlusMsgDetailDbNo) {
            merchantPlusMsgDetailDbNo = generateDbNo(companyId, COUNT_MERCHANT_TASK_DETAIL);
        }
        return merchantPlusMsgDetailDbNo;
    }

    public void setMerchantPlusMsgDetailDbNo(Integer merchantPlusMsgDetailDbNo) {
        this.merchantPlusMsgDetailDbNo = merchantPlusMsgDetailDbNo;
    }

    public Integer getCustomerSalePriceRuleDetailDbNo() {
        if (null == customerSalePriceRuleDetailDbNo) {
            customerSalePriceRuleDetailDbNo = generateDbNo(companyId, COUNT_CUSTOMER_SALE_PRICE_RULE_DETAIL);
        }
        return customerSalePriceRuleDetailDbNo;
    }

    public void setCustomerSalePriceRuleDetailDbNo(Integer customerSalePriceRuleDetailDbNo) {
        this.customerSalePriceRuleDetailDbNo = customerSalePriceRuleDetailDbNo;
    }

    public Integer getCustomerExtendDbNo() {
        if (customerExtendDbNo == null) {
            customerExtendDbNo = generateDbNo(companyId, COUNT_CUSTOMER_EXTEND);
        }
        return customerExtendDbNo;
    }

    public void setCustomerExtendDbNo(Integer customerExtendDbNo) {
        this.customerExtendDbNo = customerExtendDbNo;
    }

    public Integer getDmsItemPushRecordDbNo() {
        if (null == dmsItemPushRecordDbNo) {
            dmsItemPushRecordDbNo = generateDbNo(companyId, COUNT_DMS_ITEM_PUSH_RECORD_NUM);
        }
        return dmsItemPushRecordDbNo;
    }

    public void setDmsItemPushRecordDbNo(Integer dmsItemPushRecordDbNo) {
        this.dmsItemPushRecordDbNo = dmsItemPushRecordDbNo;
    }

    /**
     * 前N有礼相关分表号
     */
    private Integer tradeOSActivityInfoDbNo;
    private Integer tradeOSActivityAwardOrderDbNo;
    private Integer tradeOSActivityRelateOrderDbNo;

    public static final Integer COUNT_TRADE_OS_ACTIVITY_INFO = 10;
    public static final Integer COUNT_TRADE_OS_ACTIVITY_AWARD = 10;
    public static final Integer COUNT_TRADE_OS_ACTIVITY_RELATE = 10;

    public Integer getTradeOSActivityInfoDbNo() {
        if (null == tradeOSActivityInfoDbNo) return generateDbNo(companyId, COUNT_TRADE_OS_ACTIVITY_INFO);
        return tradeOSActivityInfoDbNo;
    }

    public void setTradeOSActivityInfoDbNo(Integer tradeOSActivityInfoDbNo) {
        this.tradeOSActivityInfoDbNo = tradeOSActivityInfoDbNo;
    }

    public Integer getTradeOSActivityAwardOrderDbNo() {
        if (null == tradeOSActivityAwardOrderDbNo) return generateDbNo(companyId, COUNT_TRADE_OS_ACTIVITY_AWARD);
        return tradeOSActivityAwardOrderDbNo;
    }

    public void setTradeOSActivityAwardOrderDbNo(Integer tradeOSActivityAwardOrderDbNo) {
        this.tradeOSActivityAwardOrderDbNo = tradeOSActivityAwardOrderDbNo;
    }

    public Integer getOneItemMultiCodeDbNo() {
        if (oneItemMultiCodeDbNo == null) {
            oneItemMultiCodeDbNo = generateDbNo(companyId, COUNT_ONE_ITEM_MULTI_CODE);
        }
        return oneItemMultiCodeDbNo;
    }

    public void setOneItemMultiCodeDbNo(Integer oneItemMultiCodeDbNo) {
        this.oneItemMultiCodeDbNo = oneItemMultiCodeDbNo;
    }

    public Integer getTradeOSActivityRelateOrderDbNo() {
        if (null == tradeOSActivityRelateOrderDbNo) return generateDbNo(companyId, COUNT_TRADE_OS_ACTIVITY_RELATE);
        return tradeOSActivityRelateOrderDbNo;
    }

    public void setTradeOSActivityRelateOrderDbNo(Integer tradeOSActivityRelateOrderDbNo) {
        this.tradeOSActivityRelateOrderDbNo = tradeOSActivityRelateOrderDbNo;
    }

    public Integer getTradePayConsumeLogDbNo() {
        if (null == tradePayConsumeLogDbNo) return generateDbNo(companyId, COUNT_TRADE_PAY_CONSUME_LOG_NUM);
        return tradePayConsumeLogDbNo;
    }

    public void setTradePayConsumeLogDbNo(Integer tradePayConsumeLogDbNo) {
        this.tradePayConsumeLogDbNo = tradePayConsumeLogDbNo;
    }

    public Integer getTradeTagRuleMatchLogDbNo() {
        if (null == tradeTagRuleMatchLogDbNo) {
            return generateDbNo(companyId, COUNT_TRADE_TAG_RULE_MATCH_LOG);
        }
        return tradeTagRuleMatchLogDbNo;
    }

    public void setTradeTagRuleMatchLogDbNo(Integer tradeTagRuleMatchLogDbNo) {
        this.tradeTagRuleMatchLogDbNo = tradeTagRuleMatchLogDbNo;
    }

    public Integer getItemTagDbNo() {
        if (itemTagDbNo == null) {
            itemTagDbNo = generateDbNo(companyId, COUNT_ITEM_TAG_NUM);
        }
        return itemTagDbNo;
    }

    public void setItemTagDbNo(Integer itemTagDbNo) {
        this.itemTagDbNo = itemTagDbNo;
    }

    public Integer getItemTagRelationDbNo() {
        if (itemTagRelationDbNo == null) {
            itemTagRelationDbNo = generateDbNo(companyId, COUNT_ITEM_TAG_RELATION_NUM);
        }
        return itemTagRelationDbNo;
    }

    public void setItemTagRelationDbNo(Integer itemTagRelationDbNo) {
        this.itemTagRelationDbNo = itemTagRelationDbNo;
    }

    public Integer getTaskDbNo() {
        if (taskDbNo == null) {
            taskDbNo = generateDbNo(companyId, COUNT_TASK_NUM);
        }
        return taskDbNo;
    }

    public void setTaskDbNo(Integer taskDbNo) {
        this.taskDbNo = taskDbNo;
    }

    public Integer getTaskSubDbNo() {
        if (taskSubDbNo == null) {
            taskSubDbNo = generateDbNo(companyId, COUNT_TASK_SUB_NUM);
        }
        return taskSubDbNo;
    }

    public void setTaskSubDbNo(Integer taskSubDbNo) {
        this.taskSubDbNo = taskSubDbNo;
    }

    public Integer getUserLogisticsProviderDbNo() {
        if (null == userLogisticsProviderDbNo) {
            userLogisticsProviderDbNo = generateDbNo(companyId, COUNT_USER_LOGISTICS_PROVIDER_NUM);
        }
        return userLogisticsProviderDbNo;
    }

    public void setUserLogisticsProviderDbNo(Integer userLogisticsProviderDbNo) {
        this.userLogisticsProviderDbNo = userLogisticsProviderDbNo;
    }

    public Integer getUserLogisticsChannelDbNo() {
        if (null == userLogisticsChannelDbNo) {
            userLogisticsChannelDbNo = generateDbNo(companyId, COUNT_USER_LOGISTICS_CHANNEL_NUM);
        }
        return userLogisticsChannelDbNo;
    }

    public void setUserLogisticsChannelDbNo(Integer userLogisticsChannelDbNo) {
        this.userLogisticsChannelDbNo = userLogisticsChannelDbNo;
    }

    public Integer getTradeCombineParcelDbNo() {
        if (null == tradeCombineParcelDbNo) {
            return generateDbNo(companyId, COUNT_TRADE_COMBINE_PARCEL_NUM);
        }
        return tradeCombineParcelDbNo;
    }

    public void setTradeCombineParcelDbNo(Integer tradeCombineParcelDbNo) {
        this.tradeCombineParcelDbNo = tradeCombineParcelDbNo;
    }

    public Integer getTradeParcelDbNo() {
        if (null == tradeParcelDbNo) {
            return generateDbNo(companyId, COUNT_TRADE_PARCEL_NUM);
        }
        return tradeParcelDbNo;
    }

    public void setTradeParcelDbNo(Integer tradeParcelDbNo) {
        this.tradeParcelDbNo = tradeParcelDbNo;
    }

    public Integer getOrderUniqueUnboundLogNo() {
        if (null == orderUniqueUnboundLogNo) {
            return generateDbNo(companyId, ORDER_UNIQUE_UNBOUND_LOG_NUM);
        }
        return orderUniqueUnboundLogNo;
    }

    public void setOrderUniqueUnboundLogNo(Integer orderUniqueUnboundLogNo) {
        this.orderUniqueUnboundLogNo = orderUniqueUnboundLogNo;
    }

    public Integer getUnShelveEntryDbNo() {
        if (unShelveEntryDbNo == null) {
            unShelveEntryDbNo = generateDbNo(companyId, COUNT_UNSHELVE_TAG_NUM);
        }
        return unShelveEntryDbNo;
    }

    public void setUnShelveEntryDbNo(Integer unShelveEntryDbNo) {
        this.unShelveEntryDbNo = unShelveEntryDbNo;
    }

    public Integer getUnShelveDetailEntryDbNo() {
        if (unShelveDetailEntryDbNo == null) {
            unShelveDetailEntryDbNo = generateDbNo(companyId, COUNT_UNSHELVE_TAG_NUM);
        }
        return unShelveDetailEntryDbNo;
    }

    public void setUnShelveDetailEntryDbNo(Integer unShelveDetailEntryDbNo) {
        this.unShelveDetailEntryDbNo = unShelveDetailEntryDbNo;
    }

    public Integer getPropertySegmentBridgeDbNo() {
        if (propertySegmentBridgeDbNo == null) {
            propertySegmentBridgeDbNo = generateDbNo(companyId, COUNT_PROPERTY_SEGMENT_BRIDGE_NUM);
        }
        return propertySegmentBridgeDbNo;
    }

    public void setPropertySegmentBridgeDbNo(Integer propertySegmentBridgeDbNo) {
        this.propertySegmentBridgeDbNo = propertySegmentBridgeDbNo;
    }

    public Integer getPurchasePlatFormProductDbNo() {
        if (null == purchasePlatFormProductDbNo) {
            return generateDbNo(companyId, COUNT_PURCHASE_PLATFORM_PRODUCT_NUM);
        }
        return purchasePlatFormProductDbNo;
    }

    public void setPurchasePlatFormProductDbNo(Integer purchasePlatFormProductDbNo) {
        this.purchasePlatFormProductDbNo = purchasePlatFormProductDbNo;
    }

    public Integer getPurchasePlatFormOrderDbNo() {
        if (null == purchasePlatFormOrderDbNo) {
            return generateDbNo(companyId, COUNT_PURCHASE_PLATFORM_ORDER_NUM);
        }
        return purchasePlatFormOrderDbNo;
    }

    public void setPurchasePlatFormOrderDbNo(Integer purchasePlatFormOrderDbNo) {
        this.purchasePlatFormOrderDbNo = purchasePlatFormOrderDbNo;
    }

    public Integer getFastInOutStockDbNo() {
        if (null == fastInOutStockDbNo) {
            return generateDbNo(companyId, COUNT_FAST_IN_OUT_STOCK_NUM);
        }
        return fastInOutStockDbNo;
    }

    public void setFastInOutStockDbNo(Integer fastInOutStockDbNo) {
        this.fastInOutStockDbNo = fastInOutStockDbNo;
    }

    public Integer getFastInOutStockLogDbNo() {
        if (null == fastInOutStockLogDbNo) {
            return generateDbNo(companyId, COUNT_FAST_IN_OUT_STOCK_LOG_NUM);
        }
        return fastInOutStockLogDbNo;
    }

    public Integer getCustomWorkloadSupplementLogDbNo() {
        if (null == customWorkloadSupplementLogDbNo) {
            return generateDbNo(companyId, CUSTOM_WORKLOAD_SUPPLEMENT_LOG_NUM);
        }
        return customWorkloadSupplementLogDbNo;
    }

    public void setCustomWorkloadSupplementLogDbNo(Integer customWorkloadSupplementLogDbNo) {
        this.customWorkloadSupplementLogDbNo = customWorkloadSupplementLogDbNo;
    }

    public void setFastInOutStockLogDbNo(Integer fastInOutStockLogDbNo) {
        this.fastInOutStockLogDbNo = fastInOutStockLogDbNo;
    }

    public Integer getErrorSyncRefundLogDbNo() {
        return errorSyncRefundLogDbNo;
    }

    public void setErrorSyncRefundLogDbNo(Integer errorSyncRefundLogDbNo) {
        this.errorSyncRefundLogDbNo = errorSyncRefundLogDbNo;
    }

    public Integer getUniquePrintLogDbNo() {
        if (uniquePrintLogDbNo == null) {
            uniquePrintLogDbNo = generateDbNo(companyId, UNIQUE_PRINT_LOG_DATA);
        }
        return uniquePrintLogDbNo;
    }

    public void setUniquePrintLogDbNo(Integer uniquePrintLogDbNo) {
        this.uniquePrintLogDbNo = uniquePrintLogDbNo;
    }

    public Integer getUniquePrintLogDetailDbNo() {
        if (uniquePrintLogDetailDbNo == null) {
            uniquePrintLogDetailDbNo = generateDbNo(companyId, UNIQUE_PRINT_LOG_DETAIL_DATA);
        }
        return uniquePrintLogDetailDbNo;
    }

    public void setUniquePrintLogDetailDbNo(Integer uniquePrintLogDetailDbNo) {
        this.uniquePrintLogDetailDbNo = uniquePrintLogDetailDbNo;
    }


    public Integer getAuditRecordDbNo() {
        if (auditRecordDbNo == null) {
            auditRecordDbNo = generateDbNo(companyId, COUNT_AUDIT_RECORD_NUM);
        }
        return auditRecordDbNo;
    }

    public Integer getPickChangeCodeRecordDbNo() {
        if (pickChangeCodeRecordDbNo == null) {
            pickChangeCodeRecordDbNo = generateDbNo(companyId, COUNT_PICK_CHANGE_CODE_NUM);
        }
        return pickChangeCodeRecordDbNo;
    }

    public void setAuditRecordDbNo(Integer auditRecordDbNo) {
        this.auditRecordDbNo = auditRecordDbNo;
    }

    public Integer getItemExtraInfoDbNo() {
        if (itemExtraInfoDbNo == null) {
            itemExtraInfoDbNo = generateDbNo(companyId, COUNT_DMJ_ITEM_NUM);
        }
        return itemExtraInfoDbNo;
    }

    public void setItemExtraInfoDbNo(Integer itemExtraInfoDbNo) {
        this.itemExtraInfoDbNo = itemExtraInfoDbNo;
    }

    public Integer getTradeTagRuleItemDbNo() {
        if (tradeTagRuleItemDbNo == null) {
            tradeTagRuleItemDbNo = generateDbNo(companyId, TRADE_TAG_RULE_ITEM_NUM);
        }
        return tradeTagRuleItemDbNo;
    }

    public void setTradeTagRuleItemDbNo(Integer tradeTagRuleItemDbNo) {
        this.tradeTagRuleItemDbNo = tradeTagRuleItemDbNo;
    }

    public Integer getTradeTagRuleControlDbNo() {
        if (tradeTagRuleControlDbNo == null) {
            tradeTagRuleControlDbNo = generateDbNo(companyId, TRADE_TAG_RULE_CONTROL_NUM);
        }
        return tradeTagRuleControlDbNo;
    }

    public void setTradeTagRuleControlDbNo(Integer tradeTagRuleControlDbNo) {
        this.tradeTagRuleControlDbNo = tradeTagRuleControlDbNo;
    }

    public Integer getOrderProductDbNo() {
        if (orderProductDbNo == null) {
            orderProductDbNo = generateDbNo(companyId, ORDER_PRODUCT_NUM);
        }
        return orderProductDbNo;
    }

    public void setOrderProductDbNo(Integer orderProductDbNo) {
        this.orderProductDbNo = orderProductDbNo;
    }

    public Integer getPackingGoodsRecordDbNo() {
        if (packingGoodsRecordDbNo == null) {
            packingGoodsRecordDbNo = generateDbNo(companyId, PACKING_GOODS_RECORD_NUM);
        }
        return packingGoodsRecordDbNo;
    }

    public void setPackingGoodsRecordDbNo(Integer packingGoodsRecordDbNo) {
        this.packingGoodsRecordDbNo = packingGoodsRecordDbNo;
    }

    public Integer getTradeStatSyncDbNo() {
        if (tradeStatSyncDbNo == null) {
            tradeStatSyncDbNo = generateDbNo(companyId, TRADE_STAT_SYNC_NUM);
        }
        return tradeStatSyncDbNo;
    }

    public void setTradeStatSyncDbNo(Integer tradeStatSyncDbNo) {
        this.tradeStatSyncDbNo = tradeStatSyncDbNo;
    }

    public Integer getWirelessPrintMerchantDbNo() {
        if (wirelessPrintMerchantDbNo == null) {
            wirelessPrintMerchantDbNo = generateDbNo(companyId, COUNT_WIRELESS_PRINT_MERCHANT);
        }
        return wirelessPrintMerchantDbNo;
    }

    public void setWirelessPrintMerchantDbNo(Integer wirelessPrintMerchantDbNo) {
        this.wirelessPrintMerchantDbNo = wirelessPrintMerchantDbNo;
    }

    public Integer getUserExpressTemplateConfigDbNo() {
        if (userExpressTemplateConfigDbNo == null) {
            userExpressTemplateConfigDbNo = generateDbNo(companyId, USER_EXPRESS_TEMPLATE_CONFIG_NUM);
        }
        return userExpressTemplateConfigDbNo;
    }

    public void setUserExpressTemplateConfigDbNo(Integer userExpressTemplateConfigDbNo) {
        this.userExpressTemplateConfigDbNo = userExpressTemplateConfigDbNo;
    }

    public Integer getPrintConfigNewDbNo() {
        if (printConfigNewDbNo == null) {
            printConfigNewDbNo = generateDbNo(companyId, PRINT_CONFIG_NEW_NUM);
        }
        return printConfigNewDbNo;
    }

    public void setPrintConfigNewDbNo(Integer printConfigNewDbNo) {
        this.printConfigNewDbNo = printConfigNewDbNo;
    }

    public Integer getUserCloudPrinterDbNo() {
        if (userCloudPrinterDbNo == null) {
            userCloudPrinterDbNo = generateDbNo(companyId, DB_NO_TWO);
        }
        return userCloudPrinterDbNo;
    }

    public void setUserCloudPrinterDbNo(Integer userCloudPrinterDbNo) {
        this.userCloudPrinterDbNo = userCloudPrinterDbNo;
    }

    public Integer getTradeMergeItemGroupDbNo() {
        if (tradeMergeItemGroupDbNo == null) {
            tradeMergeItemGroupDbNo = generateDbNo(companyId, COUNT_TRADE_MERGE_ITEM_GROUP_NUM);
        }
        return tradeMergeItemGroupDbNo;
    }

    public void setTradeMergeItemGroupDbNo(Integer tradeMergeItemGroupDbNo) {
        this.tradeMergeItemGroupDbNo = tradeMergeItemGroupDbNo;
    }

    public Integer getTradeItemGroupDbNo() {
        if (tradeItemGroupDbNo == null) {
            tradeItemGroupDbNo = generateDbNo(companyId, COUNT_TRADE_ITEM_GROUP_NUM);
        }
        return tradeItemGroupDbNo;
    }

    public void setTradeItemGroupDbNo(Integer tradeItemGroupDbNo) {
        this.tradeItemGroupDbNo = tradeItemGroupDbNo;
    }

    public Integer getUserStyleExpressTemplateDbNo() {
        if (userStyleExpressTemplateDbNo == null) {
            userStyleExpressTemplateDbNo = generateDbNo(companyId, USER_STYLE_EXPRESS_TEMPLATE_NUM);
        }
        return userStyleExpressTemplateDbNo;
    }

    public void setTradeVersionDbNo(Integer tradeVersionDbNo) {
        this.tradeVersionDbNo = tradeVersionDbNo;
    }

    public Integer getTradeVersionDbNo() {
        if (tradeVersionDbNo == null) {
            tradeVersionDbNo = generateDbNo(companyId, COUNT_TRADE_VERSION_NUM);
        }
        return tradeVersionDbNo;
    }

    public void setTradeRuleDbNo(Integer tradeRuleDbNo) {
        this.tradeRuleDbNo = tradeRuleDbNo;
    }

    public Integer getTradeRuleDbNo() {
        if (tradeRuleDbNo == null) {
            tradeRuleDbNo = generateDbNo(companyId, COUNT_TRADE_RULE_NUM);
        }
        return tradeRuleDbNo;
    }

    public void setTradeRuleHistoryDbNo(Integer tradeRuleHistoryDbNo) {
        this.tradeRuleHistoryDbNo = tradeRuleHistoryDbNo;
    }

    public Integer getTradeRuleHistoryDbNo() {
        if (tradeRuleHistoryDbNo == null) {
            tradeRuleHistoryDbNo = generateDbNo(companyId, COUNT_TRADE_RULE_HISTORY_NUM);
        }
        return tradeRuleHistoryDbNo;
    }

    public void setTradeRuleConditionDbNo(Integer tradeRuleConditionDbNo) {
        this.tradeRuleConditionDbNo = tradeRuleConditionDbNo;
    }

    public Integer getTradeRuleConditionDbNo() {
        if (tradeRuleConditionDbNo == null) {
            tradeRuleConditionDbNo = generateDbNo(companyId, COUNT_TRADE_RULE_CONDITION_NUM);
        }
        return tradeRuleConditionDbNo;
    }

    public void setTradeRuleConditionHistoryDbNo(Integer tradeRuleConditionHistoryDbNo) {
        this.tradeRuleConditionHistoryDbNo = tradeRuleConditionHistoryDbNo;
    }

    public Integer getTradeRuleConditionHistoryDbNo() {
        if (tradeRuleConditionHistoryDbNo == null) {
            tradeRuleConditionHistoryDbNo = generateDbNo(companyId, COUNT_TRADE_RULE_CONDITION_HISTORY_NUM);
        }
        return tradeRuleConditionHistoryDbNo;
    }

    public void setTradeRuleConditionFilterDbNo(Integer tradeRuleConditionFilterDbNo) {
        this.tradeRuleConditionFilterDbNo = tradeRuleConditionFilterDbNo;
    }

    public Integer getTradeRuleConditionFilterDbNo() {
        if (tradeRuleConditionFilterDbNo == null) {
            tradeRuleConditionFilterDbNo = generateDbNo(companyId, COUNT_TRADE_RULE_CONDITION_FILTER_NUM);
        }
        return tradeRuleConditionFilterDbNo;
    }

    public void setTradeRuleConditionFilterHistoryDbNo(Integer tradeRuleConditionFilterHistoryDbNo) {
        this.tradeRuleConditionFilterHistoryDbNo = tradeRuleConditionFilterHistoryDbNo;
    }

    public Integer getTradeRuleConditionFilterHistoryDbNo() {
        if (tradeRuleConditionFilterHistoryDbNo == null) {
            tradeRuleConditionFilterHistoryDbNo = generateDbNo(companyId, COUNT_TRADE_RULE_CONDITION_FILTER_HISTORY_NUM);
        }
        return tradeRuleConditionFilterHistoryDbNo;
    }


    public void setTradeRuleConditionDetailDbNo(Integer tradeRuleConditionDetailDbNo) {
        this.tradeRuleConditionDetailDbNo = tradeRuleConditionDetailDbNo;
    }

    public Integer getTradeRuleConditionDetailDbNo() {
        if (tradeRuleConditionDetailDbNo == null) {
            tradeRuleConditionDetailDbNo = generateDbNo(companyId, COUNT_TRADE_RULE_CONDITION_DETAIL_NUM);
        }
        return tradeRuleConditionDetailDbNo;
    }

    public void setTradeRuleConditionDetailHistoryDbNo(Integer tradeRuleConditionDetailHistoryDbNo) {
        this.tradeRuleConditionDetailHistoryDbNo = tradeRuleConditionDetailHistoryDbNo;
    }

    public Integer getTradeRuleConditionDetailHistoryDbNo() {
        if (tradeRuleConditionDetailHistoryDbNo == null) {
            tradeRuleConditionDetailHistoryDbNo = generateDbNo(companyId, COUNT_TRADE_RULE_CONDITION_DETAIL_HISTORY_NUM);
        }
        return tradeRuleConditionDetailHistoryDbNo;
    }

    public void setTradeRuleMatchLogDbNo(Integer tradeRuleMatchLogDbNo) {
        this.tradeRuleMatchLogDbNo = tradeRuleMatchLogDbNo;
    }

    public Integer getTradeRuleMatchLogDbNo() {
        if (tradeRuleMatchLogDbNo == null) {
            tradeRuleMatchLogDbNo = generateDbNo(companyId, COUNT_TRADE_RULE_MATCH_LOG_NUM);
        }
        return tradeRuleMatchLogDbNo;
    }

    public void setUserStyleExpressTemplateDbNo(Integer userStyleExpressTemplateDbNo) {
        this.userStyleExpressTemplateDbNo = userStyleExpressTemplateDbNo;
    }

    public Integer getUserLogisticsCompanyDbNo() {
        if (userLogisticsCompanyDbNo == null) {
            userLogisticsCompanyDbNo = generateDbNo(companyId, USER_LOGISTICS_COMPANY_NUM);
        }
        return userLogisticsCompanyDbNo;
    }

    public void setUserLogisticsCompanyDbNo(Integer userLogisticsCompanyDbNo) {
        this.userLogisticsCompanyDbNo = userLogisticsCompanyDbNo;
    }

    public Integer getUserLogisticsCompanyTemplateDbNo() {
        if (userLogisticsCompanyTemplateDbNo == null) {
            userLogisticsCompanyTemplateDbNo = generateDbNo(companyId, USER_LOGISTICS_COMPANY_TEMPLATE_NUM);
        }
        return userLogisticsCompanyTemplateDbNo;
    }

    public void setUserLogisticsCompanyTemplateDbNo(Integer userLogisticsCompanyTemplateDbNo) {
        this.userLogisticsCompanyTemplateDbNo = userLogisticsCompanyTemplateDbNo;
    }

    public Integer getUniqueCodeExtendDbNo() {
        if (null == uniqueCodeExtendDbNo) {
            uniqueCodeExtendDbNo = generateDbNo(companyId, UNIQUE_CODE_EXTEND_NUM);
        }

        return uniqueCodeExtendDbNo;
    }

    public void setUniqueCodeExtendDbNo(Integer uniqueCodeExtendDbNo) {
        this.uniqueCodeExtendDbNo = uniqueCodeExtendDbNo;
    }

    public Integer getUnGenerateAllocateGoodsRecordDbNo() {
        if (unGenerateAllocateGoodsRecordDbNo == null) {
            unGenerateAllocateGoodsRecordDbNo = generateDbNo(companyId, COUNT_UN_GENERATE_ALLOCATE_GOODS_RECORD);
        }
        return unGenerateAllocateGoodsRecordDbNo;
    }

    public void setUnGenerateAllocateGoodsRecordDbNo(Integer unGenerateAllocateGoodsRecordDbNo) {
        this.unGenerateAllocateGoodsRecordDbNo = unGenerateAllocateGoodsRecordDbNo;
    }

    public Integer getPrintItemLogDbNo() {
        if (printItemLogDbNo == null) {
            printItemLogDbNo = generateDbNo(companyId, COUNT_PRINT_ITEM_LOG_NUM);
        }
        return printItemLogDbNo;
    }

    public void setPrintItemLogDbNo(Integer printItemLogDbNo) {
        this.printItemLogDbNo = printItemLogDbNo;
    }

    public Integer getAbroadTradePrintStatusDbNo() {
        if (abroadTradePrintStatusDbNo == null) {
            abroadTradePrintStatusDbNo = generateDbNo(companyId, DB_NO_TWO);
        }
        return abroadTradePrintStatusDbNo;
    }

    public void setAbroadTradePrintStatusDbNo(Integer abroadTradePrintStatusDbNo) {
        this.abroadTradePrintStatusDbNo = abroadTradePrintStatusDbNo;
    }

    public Integer getSmartTallyDbNo() {
        if (smartTallyDbNo == null) {
            smartTallyDbNo = generateDbNo(companyId, COUNT_SMART_TALLY_NUM);
        }
        return smartTallyDbNo;
    }

    public void setSmartTallyDbNo(Integer smartTallyDbNo) {
        this.smartTallyDbNo = smartTallyDbNo;
    }


    public Integer getPeriodItemDbNo() {
        if (periodItemDbNo == null) {
            periodItemDbNo = generateDbNo(companyId, COUNT_PERIOD_ITEM_NUM);
        }
        return periodItemDbNo;
    }

    public void setPeriodItemDbNo(Integer periodItemDbNo) {
        this.periodItemDbNo = periodItemDbNo;
    }

    public Integer getPeriodItemDetailDbNo() {
        if (periodItemDetailDbNo == null) {
            periodItemDetailDbNo = generateDbNo(companyId, COUNT_PERIOD_ITEM_DETAIL_NUM);
        }
        return periodItemDetailDbNo;
    }

    public void setPeriodItemDetailDbNo(Integer periodItemDetailDbNo) {
        this.periodItemDetailDbNo = periodItemDetailDbNo;
    }

    public Integer getItemOperateLogDbNo() {
        if (itemOperateLogDbNo == null) {
            itemOperateLogDbNo = generateDbNo(companyId, COUNT_ITEM_OPERATE_LOG_NUM);
        }
        return itemOperateLogDbNo;
    }

    public void setItemOperateLogDbNo(Integer itemOperateLogDbNo) {
        this.itemOperateLogDbNo = itemOperateLogDbNo;
    }


    public Integer getWirelessPrintAutoEscrowDbNo() {
        if (wirelessPrintAutoEscrowDbNo == null) {
            wirelessPrintAutoEscrowDbNo = generateDbNo(companyId, COUNT_WIRELESS_PRINT_AUTO_ESCROW);
        }
        return wirelessPrintAutoEscrowDbNo;
    }

    public void setWirelessPrintAutoEscrowDbNo(Integer wirelessPrintAutoEscrowDbNo) {
        this.wirelessPrintAutoEscrowDbNo = wirelessPrintAutoEscrowDbNo;
    }

    public Integer getWmsLabelDbNo() {
        if (WmsLabelDbNo == null) {
            WmsLabelDbNo = generateDbNo(companyId, WMS_LABEL);
        }
        return WmsLabelDbNo;
    }

    public void setWmsLabelDbNo(Integer wmsLabelDbNo) {
        WmsLabelDbNo = wmsLabelDbNo;
    }

    public Integer getWmsLabelBridgeDbNo() {
        if (WmsLabelBridgeDbNo == null) {
            WmsLabelBridgeDbNo = generateDbNo(companyId, WMS_LABEL_BRIDGE);
        }
        return WmsLabelBridgeDbNo;
    }

    public void setWmsLabelBridgeDbNo(Integer wmsLabelBridgeDbNo) {
        WmsLabelBridgeDbNo = wmsLabelBridgeDbNo;
    }

    public Integer getGiftRuleDbNo() {
        if (giftRuleDbNo == null) {
            giftRuleDbNo = generateDbNo(companyId, GIFT_RULE_NUM);
        }
        return giftRuleDbNo;
    }

    public void setGiftRuleDbNo(Integer giftRuleDbNo) {
        this.giftRuleDbNo = giftRuleDbNo;
    }

    public Integer getGiftRuleExtendDbNo() {
        if (giftRuleExtendDbNo == null) {
            giftRuleExtendDbNo = generateDbNo(companyId, GIFT_RULE_EXTEND_NUM);
        }
        return giftRuleExtendDbNo;
    }

    public void setGiftRuleExtendDbNo(Integer giftRuleExtendDbNo) {
        this.giftRuleExtendDbNo = giftRuleExtendDbNo;
    }

    public Integer getGiftRuleItemDbNo() {
        if (giftRuleItemDbNo == null) {
            giftRuleItemDbNo = generateDbNo(companyId, GIFT_RULE_ITEM_NUM);
        }
        return giftRuleItemDbNo;
    }

    public void setGiftRuleItemDbNo(Integer giftRuleItemDbNo) {
        this.giftRuleItemDbNo = giftRuleItemDbNo;
    }

    public Integer getGiftRecordLogDbNo() {
        if (giftRecordLogDbNo == null) {
            giftRecordLogDbNo = generateDbNo(companyId, GIFT_RECORD_LOG_NUM);
        }
        return giftRecordLogDbNo;
    }

    public void setGiftRecordLogDbNo(Integer giftRecordLogDbNo) {
        this.giftRecordLogDbNo = giftRecordLogDbNo;
    }


    public Integer getAssoPurchaseOrderDbNo() {
        if (assoPurchaseOrderDbNo == null) {
            assoPurchaseOrderDbNo = generateDbNo(companyId, COUNT_ASSO_PURCHASE_ORDER_NUM);
        }
        return assoPurchaseOrderDbNo;
    }

    public void setAssoPurchaseOrderDbNo(Integer assoPurchaseOrderDbNo) {
        this.assoPurchaseOrderDbNo = assoPurchaseOrderDbNo;
    }

    /**
     * 送货单分表数
     */
    public static final Integer PURCHASE_DELIVER_ORDER_NUM = 5;

    /**
     * 送货单明细分表数量
     */
    public static final Integer PURCHASE_DELIVER_ORDER_DETAIL_NUM = 20;

    /**
     * 采购送货单分表号
     */
    private Integer purchaseDeliverOrderDbNo;

    /**
     * 采购送货单明细分表号
     */
    private Integer purchaseDeliverOrderDetailDbNo;

    public Integer getPurchaseDeliverOrderDbNo() {
        if (purchaseDeliverOrderDbNo == null) {
            purchaseDeliverOrderDbNo = generateDbNo(companyId, PURCHASE_DELIVER_ORDER_NUM);
        }
        return purchaseDeliverOrderDbNo;
    }

    public void setPurchaseDeliverOrderDbNo(Integer purchaseDeliverOrderDbNo) {
        this.purchaseDeliverOrderDbNo = purchaseDeliverOrderDbNo;
    }

    public Integer getPurchaseDeliverOrderDetailDbNo() {
        if (purchaseDeliverOrderDetailDbNo == null) {
            purchaseDeliverOrderDetailDbNo = generateDbNo(companyId, PURCHASE_DELIVER_ORDER_DETAIL_NUM);
        }
        return purchaseDeliverOrderDetailDbNo;
    }

    public void setPurchaseDeliverOrderDetailDbNo(Integer purchaseDeliverOrderDetailDbNo) {
        this.purchaseDeliverOrderDetailDbNo = purchaseDeliverOrderDetailDbNo;
    }


    public void setLogisticsWaveRelationDbNo(Integer logisticsWaveRelationDbNo) {
        this.logisticsWaveRelationDbNo = logisticsWaveRelationDbNo;
    }

    public Integer getLogisticsWaveRelationDbNo() {
        if(logisticsWaveRelationDbNo == null){
            logisticsWaveRelationDbNo = generateDbNo(companyId,COUNT_LOGISTICS_WAVE_RELATION_NUM);
        }
        return logisticsWaveRelationDbNo;
    }

    public Integer getPrintMerchantCodeNumLogDbNo() {
        if (printMerchantCodeNumLogDbNo == null) {
            printMerchantCodeNumLogDbNo = generateDbNo(companyId, PRINT_MERCHANT_CODE_NUM_LOG_NUM);
        }
        return printMerchantCodeNumLogDbNo;
    }

    public void setPrintMerchantCodeNumLogDbNo(Integer printMerchantCodeNumLogDbNo) {
        this.printMerchantCodeNumLogDbNo = printMerchantCodeNumLogDbNo;
    }


    public Integer getRobotPushRecordDbNo() {
        if (robotPushRecordDbNo == null) {
            robotPushRecordDbNo = generateDbNo(companyId, COUNT_ROBOT_PUSH_RECORD_NUM);
        }
        return robotPushRecordDbNo;
    }

    public void setRobotPushRecordDbNo(Integer robotPushRecordDbNo) {
        this.robotPushRecordDbNo = robotPushRecordDbNo;
    }

    public Integer getRobotPushRecordDetailDbNo() {
        if (robotPushRecordDetailDbNo == null) {
            robotPushRecordDetailDbNo = generateDbNo(companyId, COUNT_ROBOT_PUSH_RECORD_DETAIL_NUM);
        }
        return robotPushRecordDetailDbNo;
    }

    public void setRobotPushRecordDetailDbNo(Integer robotPushRecordDetailDbNo) {
        this.robotPushRecordDetailDbNo = robotPushRecordDetailDbNo;
    }

    public Integer getAssoLabelReconciliationDbNo() {
        if (assoLabelReconciliationDbNo == null) {
            assoLabelReconciliationDbNo = generateDbNo(companyId,COUNT_ASSO_LABEL_RECONCILIATION_NUM);
        }
        return assoLabelReconciliationDbNo;
    }

    public void setAssoLabelReconciliationDbNo(Integer assoLabelReconciliationDbNo) {
        this.assoLabelReconciliationDbNo = assoLabelReconciliationDbNo;
    }


    public Integer getAiPackmaModifiedDataDbNo() {
        if (aiPackmaModifiedDataDbNo == null) {
            aiPackmaModifiedDataDbNo = generateDbNo(companyId, COUNT_AI_PACKMA_MODIFIED_DATA);
        }
        return aiPackmaModifiedDataDbNo;
    }

    public void setAiPackmaModifiedDataDbNo(Integer aiPackmaModifiedDataDbNo) {
        this.aiPackmaModifiedDataDbNo = aiPackmaModifiedDataDbNo;
    }
}

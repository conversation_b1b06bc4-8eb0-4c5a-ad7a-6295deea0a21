package com.raycloud.dmj.domain.basis;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购管理中的供应商
 * <AUTHOR>
 *
 */
public class Supplier implements Serializable {

	private static final long serialVersionUID = -7079757660115619325L;

	/**
	 * 供应商编号
	 */
	private Long id;

	/**
	 * 公司编号
	 */
	private Long companyId;

	/**
	 * 供应商编码
	 */
	private String code;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 地址
	 */
	private String address;

	/**
	 * 联系人名称
	 */
	private String contactName;

	/**
	 * 固话号码
	 */
	private String phone;

	/**
	 * 手机号码
	 */
	private String mobile;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 合作状态，1表示合作中，2表示停止合作
	 */
	private Integer status;

	/**
	 * 助记符
	 */
	private String memoni;

	/**
	 * 传真
	 */
	private String fax;

	/**
	 * QQ
	 */
	private String qq;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 网址
	 */
	private String webAddress;

	/**
	 * 邮编
	 */
	private String zip;

	/**
	 * 建档者，也就是员工的编号
	 */
	private Long createrId;

	/**
	 * 建档者名称
	 */
	private String createrName;

	/**
	 * 发票抬头
	 */
	private String invoiceName;

	/**
	 * 开户行
	 */
	private String accountBank;

	/**
	 * 银行账号
	 */
	private String bankNumber;

	/**
	 * 税号
	 */
	private String tax;

	/**
	 * 建档日期
	 */
	private Date created;

	/**
	 * 修改人编号
	 */
	private Long modifierId;

	/**
	 * 修改人名称
	 */
	private String modifierName;

	/**
	 * 修改日期
	 */
	private Date modified;

	/**
	 * 数据状态，0表示删除，1表示可用
	 */
	private Integer enableStatus;

	private String province;

	private String city;

	private String district;

	private String prefix;

	private Integer sort;

	/**
	 * 超收数值
	 * 注：供应商级别的超收数量
	 */
	private Long excessCount;

	/**
	 * 微信/支付宝/账期(现结、半月结、月结和其他，默认现结)
	 */
	private String wechat;
	private String alipay;
	private String billType;

	/**
	 * 供应商分类
	 */
	private String categoryName;

	/**
	 * 预计到货时长 默认0，为-1时则更新为null
	 */
	private Integer planReceiveDay;

	/**
	 * 供应商分类ID集合
	 */
	private String categoryId;

	/**
	 * 供应商税务ID(以逗号隔开)
	 */
	private String supplierTaxIds;
	private String taxType;
	/**
	 * 账期分类（1：现付、2：日付、3：月付、4：季付、5：年付）
	 */
	private Integer accountPeriodType;

	public Integer getAccountPeriodType() {
		return accountPeriodType;
	}

	public void setAccountPeriodType(Integer accountPeriodType) {
		this.accountPeriodType = accountPeriodType;
	}

	/**
	 * 渠道
	 */
	private String channel;

	/**
	 * 平台店铺名称
	 */
	private String platformShopName;

	/**
	 * 收款码图片url
	 */
	private String paymentCodeUrl;

	/**
	 * 支付方式（线下支付or余额or线下支付、余额）
	 */
	private String paymentType;

	private String paymentCodeRemark;


	/**
	 *  结算方式 0:付款单 1:余额 枚举PayWay
	 */
	private Integer payWay;

	/**
	 *  1688合作模式 0:批发模式 1:分销模式
	 */
	private Integer platformCooperateType;

	/**
	 * 	供应商等级
	 */
	private Integer supplierLevel;

	/**
	 * 奇门信息（渠道为"奇门"时展示）
	 * Customer id
	 * 第三方仓库编码
	 * 店铺名称
	 */
	private String customerId;
	private String thirdWarehouseCode;
	private String shopName;

	/**
	 * 支付信息，JSON数组
	 * 可参考 SupplierPayConf 所有的子类
	 */
	private String payConf;


	private String proofUrl;

	private String proofName;

	public String getProofUrl() {
		return proofUrl;
	}

	public void setProofUrl(String proofUrl) {
		this.proofUrl = proofUrl;
	}

	public String getProofName() {
		return proofName;
	}

	public void setProofName(String proofName) {
		this.proofName = proofName;
	}

	public List<ProofInfo> getProofInfoList() {
		if (CollectionUtils.isEmpty(proofInfoList) && StringUtils.isNotEmpty( proofUrl) && StringUtils.isNotEmpty(proofName)) {
			List<ProofInfo> addProofInfoList = Lists.newArrayList();
			if (proofUrl.contains(ProofInfo.SEPARATOR_BEFORE) && proofName.contains(ProofInfo.SEPARATOR_BEFORE)) {
				String[] proofUrlList = proofUrl.split(ProofInfo.SEPARATOR_AFTER);
				String[] proofNameList = proofName.split(ProofInfo.SEPARATOR_AFTER);
				for (int i = 0; i < proofUrlList.length; i++) {
					addProofInfoList.add(new ProofInfo(proofUrlList[i], proofNameList[i]));
				}
			} else {
				addProofInfoList.add(new ProofInfo(proofUrl, proofName));
			}
			this.proofInfoList = addProofInfoList;
			return proofInfoList;
		}
		return proofInfoList;
	}

	public void setProofInfoList(List<ProofInfo> proofInfoList) {
		this.proofInfoList = proofInfoList;
	}

	private List<ProofInfo> proofInfoList;


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getCreaterId() {
		return createrId;
	}

	public void setCreaterId(Long createrId) {
		this.createrId = createrId;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public Long getModifierId() {
		return modifierId;
	}

	public void setModifierId(Long modifierId) {
		this.modifierId = modifierId;
	}

	public String getModifierName() {
		return modifierName;
	}

	public void setModifierName(String modifierName) {
		this.modifierName = modifierName;
	}

	public Date getModified() {
		return modified;
	}

	public void setModified(Date modified) {
		this.modified = modified;
	}

	public Integer getEnableStatus() {
		return enableStatus;
	}

	public void setEnableStatus(Integer enableStatus) {
		this.enableStatus = enableStatus;
	}

	public String getMemoni() {
		return memoni;
	}

	public void setMemoni(String memoni) {
		this.memoni = memoni;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getWebAddress() {
		return webAddress;
	}

	public void setWebAddress(String webAddress) {
		this.webAddress = webAddress;
	}

	public String getZip() {
		return zip;
	}

	public void setZip(String zip) {
		this.zip = zip;
	}

	public String getInvoiceName() {
		return invoiceName;
	}

	public void setInvoiceName(String invoiceName) {
		this.invoiceName = invoiceName;
	}

	public String getAccountBank() {
		return accountBank;
	}

	public void setAccountBank(String accountBank) {
		this.accountBank = accountBank;
	}

	public String getBankNumber() {
		return bankNumber;
	}

	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}

	public String getTax() {
		return tax;
	}

	public void setTax(String tax) {
		this.tax = tax;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getDistrict() {
		return district;
	}

	public void setDistrict(String district) {
		this.district = district;
	}

	public String getPrefix() {
		return prefix;
	}

	public void setPrefix(String prefix) {
		this.prefix = prefix;
	}

	public Integer getSort() {
		if(null == sort){
			return 0;
		}
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public Long getExcessCount() {
		return excessCount;
	}

	public void setExcessCount(Long excessCount) {
		this.excessCount = excessCount;
	}

	public String getWechat() {
		return wechat;
	}

	public void setWechat(String wechat) {
		this.wechat = wechat;
	}

	public String getAlipay() {
		return alipay;
	}

	public void setAlipay(String alipay) {
		this.alipay = alipay;
	}

	public String getBillType() {
		return billType;
	}

	public void setBillType(String billType) {
		this.billType = billType;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public Integer getPlanReceiveDay() {
		return planReceiveDay;
	}

	public void setPlanReceiveDay(Integer planReceiveDay) {
		this.planReceiveDay = planReceiveDay;
	}

	public String getSupplierTaxIds() {
		return supplierTaxIds;
	}

	public void setSupplierTaxIds(String supplierTaxIds) {
		this.supplierTaxIds = supplierTaxIds;
	}

	public String getTaxType() {
		return taxType;
	}

	public void setTaxType(String taxType) {
		this.taxType = taxType;
	}

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getPlatformShopName() {
		return platformShopName;
	}

	public void setPlatformShopName(String platformShopName) {
		this.platformShopName = platformShopName;
	}

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public String getPaymentCodeUrl() {
		return paymentCodeUrl;
	}

	public void setPaymentCodeUrl(String paymentCodeUrl) {
		this.paymentCodeUrl = paymentCodeUrl;
	}

	public String getPaymentCodeRemark() {
		return paymentCodeRemark;
	}

	public void setPaymentCodeRemark(String paymentCodeRemark) {
		this.paymentCodeRemark = paymentCodeRemark;
	}

	public Integer getPayWay() {
		return payWay;
	}

	public void setPayWay(Integer payWay) {
		this.payWay = payWay;
	}

	public Integer getPlatformCooperateType() {
		return platformCooperateType;
	}

	public void setPlatformCooperateType(Integer platformCooperateType) {
		this.platformCooperateType = platformCooperateType;
	}

	public Integer getSupplierLevel() {
		return supplierLevel;
	}

	public void setSupplierLevel(Integer supplierLevel) {
		this.supplierLevel = supplierLevel;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public String getThirdWarehouseCode() {
		return thirdWarehouseCode;
	}

	public void setThirdWarehouseCode(String thirdWarehouseCode) {
		this.thirdWarehouseCode = thirdWarehouseCode;
	}

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public String getPayConf() {
		return payConf;
	}

	public void setPayConf(String payConf) {
		this.payConf = payConf;
	}

	public enum ChannelEnum {
		QIMEN("qimen", "奇门"),
		OFFLINE("offline", "线下"),
		ALI("1688", "1688"),
		;

		public static ChannelEnum getChannelEnum(String code) {
			for (ChannelEnum value : values()) {
				if (value.code.equals(code)) {
					return value;
				}
			}
			return null;
		}

		private String code;
		private String name;

		ChannelEnum(String code, String name) {
			this.code = code;
			this.name = name;
		}

		public String getCode() {
			return code;
		}

		public void setCode(String code) {
			this.code = code;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}
	}

	public class ProofInfo implements Serializable{
		/**
		 * 单据凭证url
		 */
		private String proofUrl;

		/**
		 * 单据凭证名称
		 */
		private String proofName;
		/**
		 * 自定义分隔符(转义前)
		 */
		public static final String SEPARATOR_BEFORE = "|--|";

		/**
		 * 自定义分隔符(转义后)
		 */
		public static final String SEPARATOR_AFTER = "\\|--\\|";

		public ProofInfo(String proofUrl, String proofName) {
			this.proofUrl = proofUrl;
			this.proofName = proofName;
		}

		public String getProofUrl() {
			return proofUrl;
		}

		public void setProofUrl(String proofUrl) {
			this.proofUrl = proofUrl;
		}

		public String getProofName() {
			return proofName;
		}

		public void setProofName(String proofName) {
			this.proofName = proofName;
		}
	}
}

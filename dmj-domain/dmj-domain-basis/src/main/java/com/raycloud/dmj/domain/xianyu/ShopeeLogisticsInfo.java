package com.raycloud.dmj.domain.xianyu;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * @Author: chen<PERSON><PERSON><PERSON>
 * @Date: 2021/7/16 3:33 下午
 */
public class ShopeeLogisticsInfo implements Serializable {
    private static final long serialVersionUID = 647834746889707257L;

    /**
     * 运单号
     */
    @JSONField(name = "tracking_no")
    private String trackingNo;


    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }
}

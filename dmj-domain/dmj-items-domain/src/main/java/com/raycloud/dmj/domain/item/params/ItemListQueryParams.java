package com.raycloud.dmj.domain.item.params;

import com.google.common.collect.Sets;
import com.raycloud.dmj.Tuple2;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.item.DefinedJsonParams;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class ItemListQueryParams implements Serializable {

    /**
     * 未设置-供应商
     */
    public static final String NOT_SET_SUPPLIER = "supplier";

    /**
     * 未设置-供销商
     */
    public static final String NOT_SET_SUPPLIER_COMPANY = "supplierCompany";

    /**
     * 支持的排序字段
     */
    private static final Set<SortField> ITEM_SUPPORT_SORT_FIELDS = Sets.newHashSet(SortField.CREATED, SortField.OUTER_ID, SortField.PURCHASE_PRICE, SortField.SELLING_PRICE, SortField.SUPPLIER, SortField.TITLE, SortField.WEIGHT, SortField.WHOLESALE_PRICE, SortField.LIST_TIME, SortField.DE_LIST_TIME,SortField.SHOT_TITLE,SortField.SKU_SHORT_TITLE,SortField.PROPERTIES_ALIAS);

    private static final Set<SortField> SKU_SUPPORT_SORT_FIELDS = Sets.newHashSet(SortField.CREATED, SortField.OUTER_ID, SortField.SKU_OUTER_ID, SortField.PROPERTIES_NAME, SortField.ALL_PROPERTIES_NAME, SortField.PURCHASE_PRICE, SortField.SELLING_PRICE, SortField.SUPPLIER, SortField.ITEM_OUTER_ID, SortField.TITLE, SortField.WEIGHT, SortField.WHOLESALE_PRICE, SortField.LIST_TIME,SortField.DE_LIST_TIME,SortField.SYS_SKU_ID,SortField.SHOT_TITLE,SortField.SKU_SHORT_TITLE,SortField.PROPERTIES_ALIAS,SortField.ITEM_SKU_OUTER_ID, SortField.ITEM_CREATED_SKU_OUTER_ID, SortField.SKU_CREATED);

    /**
     * 商品表分表号
     */
    private Integer dmjItemTableNo;

    /**
     * SKU商品表分表号
     */
    private Integer dmjSkuTableNo;

    /**
     * 商品对应表分表号
     */
    private Integer skuBridgeTableNo;

    /**
     * 供应商关联关系表分表号
     */
    private Integer itemSupplierBridgeTableNo;

    /**
     * 上传规则配置表分表号
     */
    private Integer uploadRuleTableNo;
    /**
     * 代发商品推送记录分表号
     */
    private Integer pushRecordDbNo;

    /**
     * 一品多码分表号
     */
    private Integer oneItemMultiCodeTableDbNo;

    /**
     * 周期购商品分表号
     */
    private Integer periodItemTableNo;

    /**
     * 周期购商品明细分表号
     */
    private Integer periodItemDetailTableNo;


    /**
     * 分页设置
     */
    private Page page;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 系统商品ID列表
     */
    private List<Long> sysItemIdList;

    /**
     * SKU商品ID列表
     */
    private List<Long> sysSkuIdList;

    /**
     * 主商家编码
     */
    private String outerId;

    /**
     * 规格商家编码
     */
    private String skuOuterId;

    /**
     * 没有空格且转为小写的规格商家编码
     */
    private String skuOuterIdPure;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品类型列表
     */
    private List<Integer> typeList;

    private List<String> typeStrList;

    /**
     * 商品类型标记列表
     */
    private List<Integer> typeTagList;

    /**
     * 是否是虚拟商品
     */
    private Boolean isVirtual;

    /**
     * 规格
     */
    private String propertiesName;

    /**
     * 商品备注
     */
    private String remark;

    /**
     * 规格备注
     */
    private String skuRemark;

    /**
     * 商品简称
     */
    private String shortTitle;


    /**
     * 规格简称
     */
    private String skuShortTitle;

    /**
     * 规格别名
     */
    private String propertiesAlias;

    /**
     * 供应商商家编码
     */
    private String supplierItemOuterId;

    /**
     * 类目ID列表
     */
    private List<Long> catIdList;
    private List<String> catIdStrList;

    /**
     * 分类ID列表
     */
    private List<Long> cIdList;
    private List<String> cIdStrList;

    /**
     * 店铺ID列表
     */
    private List<Long> userIdList;

    /**
     * 品牌名称列表
     */
    private List<String> brandNameList;

    /**
     * 品牌ID列表
     */
    private List<Long> brandIdList;

    /**
     * 规格品牌ID列表
     */
    private List<Long> skuBrandIdList;

    /**
     * 供应商ID列表
     */
    private List<Long> supplierIdList;

    /**
     * 供销商公司ID列表
     */
    private List<Long> supplierCompanyIdList;

    /**
     * 商品状态
     */
    private Integer activeStatus;

    /**
     * 未设置，支持的属性：商品简称、品牌、单位、重量、商品条形码、规格条形码、成本价、销售价、长宽高、保质期、产地、商品备注、规格备注、生产日期、供应商、供销商
     */
    private String notSet;

    private List<String> notSetList;

    /**
     * 纯商品排序字段
     */
    private String itemOrderColumn;

    /**
     * SKU商品排序字段
     */
    private String skuOrderColumn;

    /**
     * 是否倒序排序
     */
    private boolean orderDesc;

    /**
     * 是否自动上传
     */
    private Boolean autoUpload;

    private Integer hide;

    /**
     * 商品级别的商家编码
     */
    private List<String> inItemOuterIdList;

    /**
     * 商品级别的商家编码
     */
    private List<String> notInItemOuterIdList;

    /**
     * 包含的最小粒度商家编码列表
     */
    private List<String> skuOuterIdList;

    /**
     * 排除的最小粒度商家编码列表
     */
    private List<String> notInMiniOuterIdList;

    /**
     * 是否需要过滤供应商权限
     */
    private boolean filterSupplierAuth;

    /**
     * 是否包含未关联供应商的商品
     */
    private Boolean includeNoSupplier;

    /**
     * 开始以及结束时间
     */
    private Date startTime;

    private Date endTime;

    /**
     * 商品条形码
     */
    private String itemBarcode;

    /**
     * sku条形码
     */
    private String skuBarcode;

    private Integer isSkuItem;
    /**
     * 创建人
     */
    private String creator;

    /**
     * 是否是分销小店商品 1 是 0 否
     */
    private Integer distributorState;

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    /**
     * 子商品商家编码
     */
    private String suiteSingleOuterId;

    /**
     * 0 查询包含所有子商品的套件/加工/组合商品
     * 1 查询包含至少一个子商品的套件/加工/组合商品
     */
    private Integer suiteQueryType;

    /**
     * 标签查询类型
     * 0-包含任意一个选中标签 1-包含所有选中标签
     */
    private Integer tagQueryType;

    /**
     * 是否包含未关联标签的商品
     */
    private Boolean includeNoTag;

    /**
     * 查询标签ID集合
     */
    private List<Long> itemTagIdList;

    /**
     * 商品标签表号
     */
    private Integer itemTagTableNo;

    /**
     * 商品标签关联表号
     */
    private Integer itemTagRelationTableNo;

    /**
     * 关联码
     */
    private String relatingCode;

    /**
     * 货源 [全部,外采,工厂]
     */
    private String goodsSource;

    private Integer goodsStatus;
    /**
     * 唯一码类型
     * 0-弱唯一码
     * 1-强唯一码
     */
    private Integer uniqueCodeType;

    private List<String> shipperList;

    private List<String> shipperIdList;

    private List<String> shipperItemCodeList;

    /**
     * 自定义商品属性分表号
     */
    private Integer propertySegmentBridgeTableNo;

    /**
     * 用户定义直接展示的类目属性
     */
    private List<Tuple2<String, String>> propertySegmentList;

    /**
     * 供应商分类
     */
    private String supplierCategoryIds;


    /**
     * 标题列表
     */
    private List<String> titleList;

    /**
     * 新品字段
     * 0 不是新品 1是新品
     */
    private Integer isNew;

    private List<Long> warehouseIdList;

    private boolean filterWarehouseAuth;

    /**
     * 没有商品的标记,如果此字段为null,说明前置条件导致将全部商品已经过滤完,返回没有商品
     */
    private boolean nonItemCondition;

    /**
     * 是否推送三方仓
     * null 全部 0 不同步 1不同步
     */
    private Integer syncThreeWarehouse;


    /**
     * 是否只查询规格商品
     */
    private Boolean skuItem;

    /**
     * 商品修改开始时间
     */
    private Date startModifiedTime;

    /**
     * 商品修改结束时间
     */
    private Date endModifiedTime;

    /**
     * 1688映射管理表号
     */
    private Integer purchasePlatFormProductDbNo;

    /**
     * 筛选1688映射的商品，0-未映射，1-映射,空就不筛选
     */
    private Integer usePurchasePlatFormProduct;


    /**
     * 图片状态 1 有图片 0 无图片
     */
    private Integer picStatus;

    /**
     * 下架筛选开始时间
     */
    private Date deListStartTime;

    /**
     * 下架筛选结束时间
     */
    private Date deListEndTime;

    /**
     * 上一次查询的最大的sysItemId
     */
    private Long lastSysItemId;


    private List<Long> lastSysItemIdList;

    /**
     * 商品条码
     */
    private String itemMultiCode;

    /**
     * 是否精确搜索
     */
    private Integer isAccurate;

    /**
     * 是否精确搜索sku
     */
    private Integer isAccurateSku;

    /**
     * 是否为赠品
     * 1 赠品
     * 0 非赠品
     */
    private Integer makeGift;

    /**
     * 是否关联实体编码
     * 1 查询已绑定实体编码的商品
     * 0 查询未绑定实体编码的商品
     */
    private Integer hasEntityCode;

    private List<Long> skuCidList;

    /**
     * 实体编码
     */
    private String entityCode;



    /**
     * 成本价最小值
     */
    private Double importPriceMin;
    /**
     * 成本价最大值
     */
    private Double importPriceMax;
    /**
     * 销售价最小值
     */
    private Double sellPriceMin;
    /**
     * 销售价最大值
     */
    private Double sellPriceMax;
    /**
     * 重量最小值
     */
    private Double weightMin;
    /**
     * 重量最大值
     */
    private Double weightMax;
    /**
     * 供应商进价最小值
     */
    private Double supplierPurchasePriceMin;
    /**
     * 供应商进价最大值
     */
    private Double supplierPurchasePriceMax;

    private Integer onSale;

    /**
     * 0 查询品牌为空的商品
     * 1 查询品牌不为空的商品
     */
    private Integer queryEmptyBrand;

    /**
     * 勾选的商品
     */
    private String selectedItems;

    /**
     * 是否开启批次
     * 1- 开启
     * 0- 关闭
     */
    private Integer hasBatch;

    /**
     * 临保商品是否允许销售
     */
    private Integer allowSaleExpiring;

    private Integer oneSixEightEightMapStatus;

    private List<DefinedJsonParams> definedJsonParams;

    private List<DefinedJsonParams> skuDefinedJsonParams;

    private List<Long> deleteSupplierIdList;

    /**
     * 是否隐藏type_tag为5的商品
     */
    private Integer hideFlag;

    private  List<String> creators;

    /**
     * 0不排除  1排除创建人
     * 关联字段 creators
     */
    private Integer excludeCreator;
    /**
     * 0不排除  1排除品牌数据
     * 关联字段 brandIdList
     */
    private Integer excludeBrand;

    /**
     * 0不排除  1排除供应商
     * 关联字段 supplierIdList
     */
    private Integer excludeSupplier;
    /**
     * 0不排除  1排除 类目
     * 关联字段 catIdList
     */

    private Integer excludeCatIds;
    /**
     * 0不排除  1排除 分类
     * 关联字段 cIdList
     */
    private Integer excludeCIds;

    /**
     * 0不排除  1排除 分类
     * 关联字段 activeStatus
     */
    private Integer excludeItemStatus;

    /**
     *  0不排除  1排除 平台状态
     *
     */
    private Integer excludeItemPlatStatus;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     *  0在创建时间之前<=  1 在创建时候之后>=
     *  创建时间
     */
    private Integer excludeCreateTime;

    /**
     * 商品是否关联了供应商
     */
    private Boolean hasSupplier;

    /**
     * 0 不覆盖填充 1覆盖填充
     */
    private Integer coverFlag;

    /**
     * 是否作为实体编码
     * 1 是
     * 0 否
     */
    private Integer asEntityCode;

    /**
     * 是否维护采购链接
     * 1 是
     * 0 否
     */
    private Integer hasPurchaseLink;

    /**
     * 是否是分销商品
     * 1 是
     * 0 否
     */
    private Integer isFxItem;

    /**
     * 商品成分
     */
    private String component;

    /**
     * 规格成分
     */
    private String skuComponent;

    /**
     * 商品产地
     */
    private String place;

    /**
     * 规格产地
     */
    private String skuPlace;

    /**
     * 导入状态
     */
    private Integer importStatus;

    private Integer suiteSearchType;

    /**
     * 1 过滤空品牌
     * 0 不过滤空品牌
     */
    private Integer filterEmptyBrandByPrivilege;

    /**
     * true表示根据权限过滤
     * false 表示根据查询参数过滤
     */
    private boolean filterBySellerCidsPrivilege;

    private Integer invoiceMark;

    public Integer getInvoiceMark() {
        return invoiceMark;
    }

    public void setInvoiceMark(Integer invoiceMark) {
        this.invoiceMark = invoiceMark;
    }
    /**
     * 自动计算类型
     */
    private String calculateTypes;

    /**
     * 规格类目
     */
    private List<Long> skuCatIdList;

    /**
     * 是否是周期购商品
     * 1 是
     * 0 否
     */
    private Integer isPeriodItem;

    /**
     * 是否是周期购商品明细
     * 1 是
     * 0 否
     */
    private Integer isPeriodItemDetail;

    /**
     * 临时表表名,为了优化导出时的深度分页使用
     */
    private String tempTableName;

    /**
     * 导出时记录的LastId
     */
    private Long exportLastId;

    /**
     * 是否使用纯商品SKU
     */
    private boolean usePureItemSku;

    /**
     * 不再查询dmj_item
     * 用于商品导出时避免item重复查询
     */
    private boolean noQueryItem;

    private Long lastSkuId;

    private Integer pageSize;


    /**
     * 0 自建商品
     * 1 货主商品
     */
    private Integer shipperItemFlag;


    private Boolean enableLastSysItemId = false;


    private Boolean needStockInfo = true;

    public Boolean getNeedStockInfo() {
        return needStockInfo;
    }

    public void setNeedStockInfo(Boolean needStockInfo) {
        this.needStockInfo = needStockInfo;
    }

    /**
     * 是否是下载查询
     */
    private Boolean downLoadQuery = false;

    /**
     * 下载总数信息 商品 和 sku的
     */
    private List<Integer> downLoadCountList;

    private Map<String, Long> downLoadTimeInfo;

    public Map<String, Long> getDownLoadTimeInfo() {
        return downLoadTimeInfo;
    }

    public void setDownLoadTimeInfo(Map<String, Long> downLoadTimeInfo) {
        this.downLoadTimeInfo = downLoadTimeInfo;
    }

    public Boolean getDownLoadQuery() {
        return downLoadQuery;
    }

    public void setDownLoadQuery(Boolean downLoadQuery) {
        this.downLoadQuery = downLoadQuery;
    }

    public List<Integer> getDownLoadCountList() {
        return downLoadCountList;
    }

    public void setDownLoadCountList(List<Integer> downLoadCountList) {
        this.downLoadCountList = downLoadCountList;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getLastSkuId() {
        return lastSkuId;
    }

    public void setLastSkuId(Long lastSkuId) {
        this.lastSkuId = lastSkuId;
    }

    public String getItemMultiCode() {
        return itemMultiCode;
    }

    public void setItemMultiCode(String itemMultiCode) {
        this.itemMultiCode = itemMultiCode;
    }

    public Integer getIsAccurate() {
        return isAccurate;
    }

    public void setIsAccurate(Integer isAccurate) {
        this.isAccurate = isAccurate;
    }

    private Integer inShop=1;


    public Integer getInShop() {
        return inShop;
    }

    public void setInShop(Integer inShop) {
        this.inShop = inShop;
    }



    private Long startSysItemId;
    private Long startSysSkuId;

    public Long getStartSysItemId() {
        return startSysItemId;
    }

    public void setStartSysItemId(Long startSysItemId) {
        this.startSysItemId = startSysItemId;
    }

    public Long getStartSysSkuId() {
        return startSysSkuId;
    }

    public void setStartSysSkuId(Long startSysSkuId) {
        this.startSysSkuId = startSysSkuId;
    }


    public Boolean getVirtual() {
        return isVirtual;
    }

    public void setVirtual(Boolean virtual) {
        isVirtual = virtual;
    }

    public void setItemOrderColumn(String itemOrderColumn) {
        this.itemOrderColumn = itemOrderColumn;
    }

    public void setSkuOrderColumn(String skuOrderColumn) {
        this.skuOrderColumn = skuOrderColumn;
    }

    public boolean isOrderDesc() {
        return orderDesc;
    }

    public boolean isFilterSupplierAuth() {
        return filterSupplierAuth;
    }

    public void setFilterSupplierAuth(boolean filterSupplierAuth) {
        this.filterSupplierAuth = filterSupplierAuth;
    }

    public Boolean getIncludeNoSupplier() {
        return includeNoSupplier;
    }

    public void setIncludeNoSupplier(Boolean includeNoSupplier) {
        this.includeNoSupplier = includeNoSupplier;
    }

    public Integer getDmjItemTableNo() {
        return dmjItemTableNo;
    }

    public ItemListQueryParams setDmjItemTableNo(Integer dmjItemTableNo) {
        this.dmjItemTableNo = dmjItemTableNo;
        return this;
    }

    public Integer getDmjSkuTableNo() {
        return dmjSkuTableNo;
    }

    public ItemListQueryParams setDmjSkuTableNo(Integer dmjSkuTableNo) {
        this.dmjSkuTableNo = dmjSkuTableNo;
        return this;
    }

    public Integer getSkuBridgeTableNo() {
        return skuBridgeTableNo;
    }

    public ItemListQueryParams setSkuBridgeTableNo(Integer skuBridgeTableNo) {
        this.skuBridgeTableNo = skuBridgeTableNo;
        return this;
    }

    public Integer getItemSupplierBridgeTableNo() {
        return itemSupplierBridgeTableNo;
    }

    public ItemListQueryParams setItemSupplierBridgeTableNo(Integer itemSupplierBridgeTableNo) {
        this.itemSupplierBridgeTableNo = itemSupplierBridgeTableNo;
        return this;
    }

    public Integer getUploadRuleTableNo() {
        return uploadRuleTableNo;
    }

    public ItemListQueryParams setUploadRuleTableNo(Integer uploadRuleTableNo) {
        this.uploadRuleTableNo = uploadRuleTableNo;
        return this;
    }

    public Integer getPushRecordDbNo() {
        return pushRecordDbNo;
    }

    public ItemListQueryParams setPushRecordDbNo(Integer pushRecordDbNo) {
        this.pushRecordDbNo = pushRecordDbNo;
        return this;
    }

    public Page getPage() {
        return page;
    }

    public ItemListQueryParams setPage(Page page) {
        this.page = page;
        return this;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public ItemListQueryParams setCompanyId(Long companyId) {
        this.companyId = companyId;
        return this;
    }

    public List<Long> getSysItemIdList() {
        return sysItemIdList;
    }

    public ItemListQueryParams setSysItemIdList(List<Long> sysItemIdList) {
        this.sysItemIdList = sysItemIdList;
        return this;
    }

    public List<Long> getSysSkuIdList() {
        return sysSkuIdList;
    }

    public ItemListQueryParams setSysSkuIdList(List<Long> sysSkuIdList) {
        this.sysSkuIdList = sysSkuIdList;
        return this;
    }

    public String getOuterId() {
        return outerId;
    }

    public ItemListQueryParams setOuterId(String outerId) {
        this.outerId = outerId;
        return this;
    }

    public String getSkuOuterId() {
        return skuOuterId;
    }

    public ItemListQueryParams setSkuOuterId(String skuOuterId) {
        this.skuOuterId = skuOuterId;
        if (skuOuterId != null){
            skuOuterIdPure = StringUtils.deleteWhitespace(skuOuterId).replaceAll("\\u00A0","").replaceAll(",","").replaceAll(";","").toLowerCase();
        }
        return this;
    }

    public String getSkuOuterIdPure() {
        return skuOuterIdPure;
    }

    public void setSkuOuterIdPure(String skuOuterIdPure) {
        this.skuOuterIdPure = skuOuterIdPure;
    }

    public String getTitle() {
        return title;
    }

    public ItemListQueryParams setTitle(String title) {
        this.title = title;
        return this;
    }

    public List<Integer> getTypeList() {
        return typeList;
    }

    public ItemListQueryParams setTypeList(List<Integer> typeList) {
        this.typeList = typeList;
        return this;
    }

    public List<Integer> getTypeTagList() {
        return typeTagList;
    }

    public ItemListQueryParams setTypeTagList(List<Integer> typeTagList) {
        this.typeTagList = typeTagList;
        return this;
    }

    public Boolean getIsVirtual() {
        return isVirtual;
    }

    public ItemListQueryParams setIsVirtual(Boolean isVirtual) {
        this.isVirtual = isVirtual;
        return this;
    }

    public String getPropertiesName() {
        return propertiesName;
    }

    public ItemListQueryParams setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public ItemListQueryParams setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getSkuRemark() {
        return skuRemark;
    }

    public ItemListQueryParams setSkuRemark(String skuRemark) {
        this.skuRemark = skuRemark;
        return this;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public ItemListQueryParams setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
        return this;
    }

    public String getPropertiesAlias() {
        return propertiesAlias;
    }

    public ItemListQueryParams setPropertiesAlias(String propertiesAlias) {
        this.propertiesAlias = propertiesAlias;
        return this;
    }

    public String getSupplierItemOuterId() {
        return supplierItemOuterId;
    }

    public ItemListQueryParams setSupplierItemOuterId(String supplierItemOuterId) {
        this.supplierItemOuterId = supplierItemOuterId;
        return this;
    }

    public List<Long> getCatIdList() {
        return catIdList;
    }

    public ItemListQueryParams setCatIdList(List<Long> catIdList) {
        this.catIdList = catIdList;
        return this;
    }

    public List<Long> getcIdList() {
        return cIdList;
    }

    public ItemListQueryParams setcIdList(List<Long> cIdList) {
        this.cIdList = cIdList;
        return this;
    }

    public List<Long> getUserIdList() {
        return userIdList;
    }

    public ItemListQueryParams setUserIdList(List<Long> userIdList) {
        this.userIdList = userIdList;
        return this;
    }

    public List<String> getBrandNameList() {
        return brandNameList;
    }

    public void setBrandNameList(List<String> brandNameList) {
        this.brandNameList = brandNameList;
    }

    public List<Long> getBrandIdList() {
        return brandIdList;
    }

    public void setBrandIdList(List<Long> brandIdList) {
        this.brandIdList = brandIdList;
    }

    public List<Long> getSkuBrandIdList() {
        return skuBrandIdList;
    }

    public void setSkuBrandIdList(List<Long> skuBrandIdList) {
        this.skuBrandIdList = skuBrandIdList;
    }

    public List<Long> getSupplierIdList() {
        return supplierIdList;
    }

    public ItemListQueryParams setSupplierIdList(List<Long> supplierIdList) {
        this.supplierIdList = supplierIdList;
        return this;
    }

    public List<Long> getSupplierCompanyIdList() {
        return supplierCompanyIdList;
    }

    public void setSupplierCompanyIdList(List<Long> supplierCompanyIdList) {
        this.supplierCompanyIdList = supplierCompanyIdList;
    }

    public Integer getActiveStatus() {
        return activeStatus;
    }

    public ItemListQueryParams setActiveStatus(Integer activeStatus) {
        this.activeStatus = activeStatus;
        return this;
    }

    public String getNotSet() {
        return notSet;
    }

    public ItemListQueryParams setNotSet(String notSet) {
        this.notSet = notSet;
        return this;
    }

    public List<String> getNotSetList() {
        return notSetList;
    }

    public void setNotSetList(List<String> notSetList) {
        this.notSetList = notSetList;
    }

    public String getItemOrderColumn() {
        return itemOrderColumn;
    }

    public ItemListQueryParams setItemOrderColumn(SortField itemSortField) {
        if (itemSortField != null && ITEM_SUPPORT_SORT_FIELDS.contains(itemSortField)) {
            this.itemOrderColumn = itemSortField.getValue();
        }
        return this;
    }

    public String getSkuOrderColumn() {
        return skuOrderColumn;
    }

    public ItemListQueryParams setSkuOrderColumn(SortField skuSortField) {
        if (skuSortField != null && SKU_SUPPORT_SORT_FIELDS.contains(skuSortField)) {
            this.skuOrderColumn = skuSortField.getValue();
        }
        return this;
    }

    public boolean getOrderDesc() {
        return orderDesc;
    }

    public ItemListQueryParams setOrderDesc(boolean orderDesc) {
        this.orderDesc = orderDesc;
        return this;
    }

    public Boolean getAutoUpload() {
        return autoUpload;
    }

    public void setAutoUpload(Boolean autoUpload) {
        this.autoUpload = autoUpload;
    }

    /**
     * 是否需要Join供应商关联表
     */
    public boolean getNeedJoinItemSupplierBridge() {
        //没有按照供应商商家编码，指定供应商查询，没有设置选择的是供应商，没有按照供应商排序
        return StringUtils.isNotEmpty(supplierItemOuterId) || supplierIdList != null
                || NOT_SET_SUPPLIER.equals(notSet) || SortField.SUPPLIER.getValue().equals(itemOrderColumn)
                || (notSetList != null && notSetList.contains(NOT_SET_SUPPLIER))
                || Boolean.TRUE.equals(hasSupplier);
    }

    public boolean getNeedJoinUploadRule() {
        return autoUpload != null;
    }

    public boolean getNeedJoinItemPushRecord() {
        return CollectionUtils.isNotEmpty(supplierCompanyIdList) || NOT_SET_SUPPLIER_COMPANY.equals(notSet)
                || (notSetList != null && notSetList.contains(NOT_SET_SUPPLIER_COMPANY));
    }

    public List<String> getInItemOuterIdList() {
        return inItemOuterIdList;
    }

    public void setInItemOuterIdList(List<String> inItemOuterIdList) {
        this.inItemOuterIdList = inItemOuterIdList;
    }

    public Integer getHide() {
        return hide;
    }

    public void setHide(Integer hide) {
        this.hide = hide;
    }
    public List<String> getNotInItemOuterIdList() {
        return notInItemOuterIdList;
    }

    public void setNotInItemOuterIdList(List<String> notInItemOuterIdList) {
        this.notInItemOuterIdList = notInItemOuterIdList;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getItemBarcode() {
        return itemBarcode;
    }

    public void setItemBarcode(String itemBarcode) {
        this.itemBarcode = itemBarcode;
    }

    public String getSkuBarcode() {
        return skuBarcode;
    }

    public void setSkuBarcode(String skuBarcode) {
        this.skuBarcode = skuBarcode;
    }

    public Integer getIsSkuItem() {
        return isSkuItem;
    }

    public void setIsSkuItem(Integer isSkuItem) {
        this.isSkuItem = isSkuItem;
    }

    public List<String> getSkuOuterIdList() {
        return skuOuterIdList;
    }

    public void setSkuOuterIdList(List<String> skuOuterIdList) {
        this.skuOuterIdList = skuOuterIdList;
    }

    public List<String> getNotInMiniOuterIdList() {
        return notInMiniOuterIdList;
    }

    public void setNotInMiniOuterIdList(List<String> notInMiniOuterIdList) {
        this.notInMiniOuterIdList = notInMiniOuterIdList;
    }

    public String getSuiteSingleOuterId() {
        return suiteSingleOuterId;
    }

    public void setSuiteSingleOuterId(String suiteSingleOuterId) {
        this.suiteSingleOuterId = suiteSingleOuterId;
    }

    public Integer getSuiteQueryType() {
        return suiteQueryType;
    }

    public void setSuiteQueryType(Integer suiteQueryType) {
        this.suiteQueryType = suiteQueryType;
    }

    public Integer getTagQueryType() {
        return tagQueryType;
    }

    public void setTagQueryType(Integer tagQueryType) {
        this.tagQueryType = tagQueryType;
    }

    public List<Long> getItemTagIdList() {
        return itemTagIdList;
    }

    public void setItemTagIdList(List<Long> itemTagIdList) {
        this.itemTagIdList = itemTagIdList;
    }

    public Integer getItemTagTableNo() {
        return itemTagTableNo;
    }

    public ItemListQueryParams setItemTagTableNo(Integer itemTagTableNo) {
        this.itemTagTableNo = itemTagTableNo;
        return this;
    }

    public Integer getItemTagRelationTableNo() {
        return itemTagRelationTableNo;
    }

    public ItemListQueryParams setItemTagRelationTableNo(Integer itemTagRelationTableNo) {
        this.itemTagRelationTableNo = itemTagRelationTableNo;
        return this;
    }

    public Boolean getIncludeNoTag() {
        return includeNoTag;
    }

    public void setIncludeNoTag(Boolean includeNoTag) {
        this.includeNoTag = includeNoTag;
    }

    public String getRelatingCode() {
        return relatingCode;
    }

    public void setRelatingCode(String relatingCode) {
        this.relatingCode = relatingCode;
    }

    public String getGoodsSource() {
        return goodsSource;
    }

    public void setGoodsSource(String goodsSource) {
        this.goodsSource = goodsSource;
    }

    public void setUniqueCodeType(Integer uniqueCodeType) {
        this.uniqueCodeType = uniqueCodeType;
    }

    public List<String> getShipperList() {
        return shipperList;
    }

    public void setShipperList(List<String> shipperList) {
        this.shipperList = shipperList;
    }

    public List<String> getShipperIdList() {
        return shipperIdList;
    }

    public void setShipperIdList(List<String> shipperIdList) {
        this.shipperIdList = shipperIdList;
    }

    public List<String> getShipperItemCodeList() {
        return shipperItemCodeList;
    }

    public void setShipperItemCodeList(List<String> shipperItemCodeList) {
        this.shipperItemCodeList = shipperItemCodeList;
    }

    public List<Tuple2<String, String>> getPropertySegmentList() {
        return propertySegmentList;
    }

    public void setPropertySegmentList(List<Tuple2<String, String>> propertySegmentList) {
        this.propertySegmentList = propertySegmentList;
    }

    public Integer getPropertySegmentBridgeTableNo() {
        return propertySegmentBridgeTableNo;
    }

    public void setPropertySegmentBridgeTableNo(Integer propertySegmentBridgeTableNo) {
        this.propertySegmentBridgeTableNo = propertySegmentBridgeTableNo;
    }

    public String getSupplierCategoryIds() {
        return supplierCategoryIds;
    }

    public void setSupplierCategoryIds(String supplierCategoryIds) {
        this.supplierCategoryIds = supplierCategoryIds;
    }

    public Integer getUniqueCodeType() {
        return uniqueCodeType;
    }



    public List<String> getCatIdStrList() {
        return catIdStrList;
    }

    public void setCatIdStrList(List<String> catIdStrList) {
        this.catIdStrList = catIdStrList;
    }

    public List<String> getcIdStrList() {
        return cIdStrList;
    }

    public void setcIdStrList(List<String> cIdStrList) {
        this.cIdStrList = cIdStrList;
    }

    public List<String> getTypeStrList() {
        return typeStrList;
    }

    public void setTypeStrList(List<String> typeStrList) {
        this.typeStrList = typeStrList;
    }

    public List<String> getTitleList() {
        return titleList;
    }

    public void setTitleList(List<String> titleList) {
        this.titleList = titleList;
    }

    public List<Long> getWarehouseIdList() {
        return warehouseIdList;
    }

    public void setWarehouseIdList(List<Long> warehouseIdList) {
        this.warehouseIdList = warehouseIdList;
    }

    public boolean isFilterWarehouseAuth() {
        return filterWarehouseAuth;
    }

    public void setFilterWarehouseAuth(boolean filterWarehouseAuth) {
        this.filterWarehouseAuth = filterWarehouseAuth;
    }

    public boolean isNonItemCondition() {
        return nonItemCondition;
    }

    public void setNonItemCondition(boolean nonItemCondition) {
        this.nonItemCondition = nonItemCondition;
    }

    public Integer getSyncThreeWarehouse() {
        return syncThreeWarehouse;
    }

    public void setSyncThreeWarehouse(Integer syncThreeWarehouse) {
        this.syncThreeWarehouse = syncThreeWarehouse;
    }

    public Integer getGoodsStatus() {
        return goodsStatus;
    }

    public void setGoodsStatus(Integer goodsStatus) {
        this.goodsStatus = goodsStatus;
    }

    public Boolean getSkuItem() {
        return skuItem;
    }

    public void setSkuItem(Boolean skuItem) {
        this.skuItem = skuItem;
    }

    public Date getStartModifiedTime() {
        return startModifiedTime;
    }

    public void setStartModifiedTime(Date startModifiedTime) {
        this.startModifiedTime = startModifiedTime;
    }

    public Date getEndModifiedTime() {
        return endModifiedTime;
    }

    public void setEndModifiedTime(Date endModifiedTime) {
        this.endModifiedTime = endModifiedTime;
    }

    public Integer getPurchasePlatFormProductDbNo() {
        return purchasePlatFormProductDbNo;
    }

    public ItemListQueryParams setPurchasePlatFormProductDbNo(Integer purchasePlatFormProductDbNo) {
        this.purchasePlatFormProductDbNo = purchasePlatFormProductDbNo;
        return this;
    }

    public Integer getOneItemMultiCodeTableDbNo() {
        return oneItemMultiCodeTableDbNo;
    }

    public ItemListQueryParams setOneItemMultiCodeTableDbNo(Integer oneItemMultiCodeTableDbNo) {
        this.oneItemMultiCodeTableDbNo = oneItemMultiCodeTableDbNo;
        return this;
    }

    public Integer getPeriodItemTableNo() {
        return periodItemTableNo;
    }

    public void setPeriodItemTableNo(Integer periodItemTableNo) {
        this.periodItemTableNo = periodItemTableNo;
    }

    public Integer getPeriodItemDetailTableNo() {
        return periodItemDetailTableNo;
    }

    public void setPeriodItemDetailTableNo(Integer periodItemDetailTableNo) {
        this.periodItemDetailTableNo = periodItemDetailTableNo;
    }

    public Integer getUsePurchasePlatFormProduct() {
        return usePurchasePlatFormProduct;
    }

    public void setUsePurchasePlatFormProduct(Integer usePurchasePlatFormProduct) {
        this.usePurchasePlatFormProduct = usePurchasePlatFormProduct;
    }

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    public Integer getPicStatus() {
        return picStatus;
    }

    public void setPicStatus(Integer picStatus) {
        this.picStatus = picStatus;
    }

    public Date getDeListStartTime() {
        return deListStartTime;
    }

    public void setDeListStartTime(Date deListStartTime) {
        this.deListStartTime = deListStartTime;
    }

    public Date getDeListEndTime() {
        return deListEndTime;
    }

    public void setDeListEndTime(Date deListEndTime) {
        this.deListEndTime = deListEndTime;
    }


    /**
     * 强制使用dmj_sku某个索引进行查询
     */
    private String skuForceIndex;

    public String getSkuForceIndex() {
        return skuForceIndex;
    }

    public void setSkuForceIndex(String skuForceIndex) {
        this.skuForceIndex = skuForceIndex;
    }

    public Long getLastSysItemId() {
        return lastSysItemId;
    }

    public void setLastSysItemId(Long lastSysItemId) {
        this.lastSysItemId = lastSysItemId;
    }

    /**
     * 是否开启生产日期管理
     * 1- 开启
     * 0 -关闭
     */
    private Integer hasProduct;

    public Integer getHasProduct() {
        return hasProduct;
    }

    public void setHasProduct(Integer hasProduct) {
        this.hasProduct = hasProduct;
    }

    public Integer getMakeGift() {
        return makeGift;
    }

    public void setMakeGift(Integer makeGift) {
        this.makeGift = makeGift;
    }

    public Integer getHasEntityCode() {
        return hasEntityCode;
    }

    public void setHasEntityCode(Integer hasEntityCode) {
        this.hasEntityCode = hasEntityCode;
    }

    public List<Long> getSkuCidList() {
        return skuCidList;
    }

    public void setSkuCidList(List<Long> skuCidList) {
        this.skuCidList = skuCidList;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public Double getImportPriceMin() {
        return importPriceMin;
    }

    public void setImportPriceMin(Double importPriceMin) {
        this.importPriceMin = importPriceMin;
    }

    public Double getImportPriceMax() {
        return importPriceMax;
    }

    public void setImportPriceMax(Double importPriceMax) {
        this.importPriceMax = importPriceMax;
    }

    public Double getSellPriceMin() {
        return sellPriceMin;
    }

    public void setSellPriceMin(Double sellPriceMin) {
        this.sellPriceMin = sellPriceMin;
    }

    public Double getSellPriceMax() {
        return sellPriceMax;
    }

    public void setSellPriceMax(Double sellPriceMax) {
        this.sellPriceMax = sellPriceMax;
    }

    public Double getWeightMin() {
        return weightMin;
    }

    public void setWeightMin(Double weightMin) {
        this.weightMin = weightMin;
    }

    public Double getWeightMax() {
        return weightMax;
    }

    public void setWeightMax(Double weightMax) {
        this.weightMax = weightMax;
    }

    public Double getSupplierPurchasePriceMin() {
        return supplierPurchasePriceMin;
    }

    public void setSupplierPurchasePriceMin(Double supplierPurchasePriceMin) {
        this.supplierPurchasePriceMin = supplierPurchasePriceMin;
    }

    public Double getSupplierPurchasePriceMax() {
        return supplierPurchasePriceMax;
    }

    public void setSupplierPurchasePriceMax(Double supplierPurchasePriceMax) {
        this.supplierPurchasePriceMax = supplierPurchasePriceMax;
    }

    public Integer getOnSale() {
        return onSale;
    }

    public void setOnSale(Integer onSale) {
        this.onSale = onSale;
    }

    public Integer getHasBatch() {
        return hasBatch;
    }

    public void setHasBatch(Integer hasBatch) {
        this.hasBatch = hasBatch;
    }

    public Integer getAllowSaleExpiring() {
        return allowSaleExpiring;
    }

    public void setAllowSaleExpiring(Integer allowSaleExpiring) {
        this.allowSaleExpiring = allowSaleExpiring;
    }

    public Integer getQueryEmptyBrand() {
        return queryEmptyBrand;
    }

    public void setQueryEmptyBrand(Integer queryEmptyBrand) {
        this.queryEmptyBrand = queryEmptyBrand;
    }

    public String getSelectedItems() {
        return selectedItems;
    }

    public void setSelectedItems(String selectedItems) {
        this.selectedItems = selectedItems;
    }

    public List<DefinedJsonParams> getDefinedJsonParams() {
        return definedJsonParams;
    }

    public void setDefinedJsonParams(List<DefinedJsonParams> definedJsonParams) {
        this.definedJsonParams = definedJsonParams;
    }

    public List<DefinedJsonParams> getSkuDefinedJsonParams() {
        return skuDefinedJsonParams;
    }

    public void setSkuDefinedJsonParams(List<DefinedJsonParams> skuDefinedJsonParams) {
        this.skuDefinedJsonParams = skuDefinedJsonParams;
    }

    public List<Long> getDeleteSupplierIdList() {
        return deleteSupplierIdList;
    }

    public void setDeleteSupplierIdList(List<Long> deleteSupplierIdList) {
        this.deleteSupplierIdList = deleteSupplierIdList;
    }

    public Integer getHideFlag() {
        return hideFlag;
    }

    public void setHideFlag(Integer hideFlag) {
        this.hideFlag = hideFlag;
    }

    public Integer getExcludeCreator() {
        return excludeCreator;
    }

    public void setExcludeCreator(Integer excludeCreator) {
        this.excludeCreator = excludeCreator;
    }

    public Integer getExcludeBrand() {
        return excludeBrand;
    }

    public void setExcludeBrand(Integer excludeBrand) {
        this.excludeBrand = excludeBrand;
    }

    public Integer getExcludeSupplier() {
        return excludeSupplier;
    }

    public void setExcludeSupplier(Integer excludeSupplier) {
        this.excludeSupplier = excludeSupplier;
    }

    public Integer getExcludeCatIds() {
        return excludeCatIds;
    }

    public void setExcludeCatIds(Integer excludeCatIds) {
        this.excludeCatIds = excludeCatIds;
    }

    public Integer getExcludeCIds() {
        return excludeCIds;
    }

    public void setExcludeCIds(Integer excludeCIds) {
        this.excludeCIds = excludeCIds;
    }

    public Integer getExcludeItemStatus() {
        return excludeItemStatus;
    }

    public void setExcludeItemStatus(Integer excludeItemStatus) {
        this.excludeItemStatus = excludeItemStatus;
    }

    public Integer getExcludeItemPlatStatus() {
        return excludeItemPlatStatus;
    }

    public void setExcludeItemPlatStatus(Integer excludeItemPlatStatus) {
        this.excludeItemPlatStatus = excludeItemPlatStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getExcludeCreateTime() {
        return excludeCreateTime;
    }

    public void setExcludeCreateTime(Integer excludeCreateTime) {
        this.excludeCreateTime = excludeCreateTime;
    }

    public List<String> getCreators() {
        return creators;
    }

    public void setCreators(List<String> creators) {
        this.creators = creators;
    }

    public Boolean getHasSupplier() {
        return hasSupplier;
    }

    public void setHasSupplier(Boolean hasSupplier) {
        this.hasSupplier = hasSupplier;
    }

    public Integer getCoverFlag() {
        return coverFlag;
    }

    public void setCoverFlag(Integer coverFlag) {
        this.coverFlag = coverFlag;
    }

    public Integer getAsEntityCode() {
        return asEntityCode;
    }

    public void setAsEntityCode(Integer asEntityCode) {
        this.asEntityCode = asEntityCode;
    }

    public Integer getHasPurchaseLink() {
        return hasPurchaseLink;
    }

    public void setHasPurchaseLink(Integer hasPurchaseLink) {
        this.hasPurchaseLink = hasPurchaseLink;
    }

    public Integer getIsFxItem() {
        return isFxItem;
    }

    public void setIsFxItem(Integer isFxItem) {
        this.isFxItem = isFxItem;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getSkuComponent() {
        return skuComponent;
    }

    public void setSkuComponent(String skuComponent) {
        this.skuComponent = skuComponent;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getSkuPlace() {
        return skuPlace;
    }

    public void setSkuPlace(String skuPlace) {
        this.skuPlace = skuPlace;
    }

    public Integer getOneSixEightEightMapStatus() {
        return oneSixEightEightMapStatus;
    }

    public void setOneSixEightEightMapStatus(Integer oneSixEightEightMapStatus) {
        this.oneSixEightEightMapStatus = oneSixEightEightMapStatus;
    }

    public Integer getImportStatus() {
        return importStatus;
    }

    public void setImportStatus(Integer importStatus) {
        this.importStatus = importStatus;
    }

    public Integer getIsAccurateSku() {
        return isAccurateSku;
    }

    public void setIsAccurateSku(Integer isAccurateSku) {
        this.isAccurateSku = isAccurateSku;
    }

    public Integer getSuiteSearchType() {
        return suiteSearchType;
    }

    public void setSuiteSearchType(Integer suiteSearchType) {
        this.suiteSearchType = suiteSearchType;
    }

    public String getSkuShortTitle() {
        return skuShortTitle;
    }

    public void setSkuShortTitle(String skuShortTitle) {
        this.skuShortTitle = skuShortTitle;
    }

    public Integer getFilterEmptyBrandByPrivilege() {
        return filterEmptyBrandByPrivilege;
    }

    public void setFilterEmptyBrandByPrivilege(Integer filterEmptyBrandByPrivilege) {
        this.filterEmptyBrandByPrivilege = filterEmptyBrandByPrivilege;
    }

    public boolean getFilterBySellerCidsPrivilege() {
        return filterBySellerCidsPrivilege;
    }

    public void setFilterBySellerCidsPrivilege(boolean filterBySellerCidsPrivilege) {
        this.filterBySellerCidsPrivilege = filterBySellerCidsPrivilege;
    }

    public String getCalculateTypes() {
        return calculateTypes;
    }

    public void setCalculateTypes(String calculateTypes) {
        this.calculateTypes = calculateTypes;
    }

    public List<Long> getSkuCatIdList() {
        return skuCatIdList;
    }

    public void setSkuCatIdList(List<Long> skuCatIdList) {
        this.skuCatIdList = skuCatIdList;
    }

    public Integer getIsPeriodItem() {
        return isPeriodItem;
    }

    public void setIsPeriodItem(Integer isPeriodItem) {
        this.isPeriodItem = isPeriodItem;
    }

    public Integer getIsPeriodItemDetail() {
        return isPeriodItemDetail;
    }

    public void setIsPeriodItemDetail(Integer isPeriodItemDetail) {
        this.isPeriodItemDetail = isPeriodItemDetail;
    }

    public String getTempTableName() {
        return tempTableName;
    }

    public void setTempTableName(String tempTableName) {
        this.tempTableName = tempTableName;
    }

    public Long getExportLastId() {
        return exportLastId;
    }

    public void setExportLastId(Long exportLastId) {
        this.exportLastId = exportLastId;
    }

    public boolean isUsePureItemSku() {
        return usePureItemSku;
    }

    public void setUsePureItemSku(boolean usePureItemSku) {
        this.usePureItemSku = usePureItemSku;
    }

    public Integer getDistributorState() {
        return distributorState;
    }

    public void setDistributorState(Integer distributorState) {
        this.distributorState = distributorState;
    }

    public boolean isNoQueryItem() {
        return noQueryItem;
    }

    public void setNoQueryItem(boolean noQueryItem) {
        this.noQueryItem = noQueryItem;
    }

    public Integer getShipperItemFlag() {
        return shipperItemFlag;
    }

    public void setShipperItemFlag(Integer shipperItemFlag) {
        this.shipperItemFlag = shipperItemFlag;
    }


    public List<Long> getLastSysItemIdList() {
        return lastSysItemIdList;
    }

    public void setLastSysItemIdList(List<Long> lastSysItemIdList) {
        this.lastSysItemIdList = lastSysItemIdList;
    }


    public Boolean getEnableLastSysItemId() {
        return enableLastSysItemId;
    }

    public void setEnableLastSysItemId(Boolean enableLastSysItemId) {
        this.enableLastSysItemId = enableLastSysItemId;
    }

    private List<String> multiTitleList;

    public List<String> getMultiTitleList() {
        return multiTitleList;
    }

    public void setMultiTitleList(List<String> multiTitleList) {
        this.multiTitleList = multiTitleList;
    }

    private List<String> multiPropertiesNameList;

    public List<String> getMultiPropertiesNameList() {
        return multiPropertiesNameList;
    }

    public void setMultiPropertiesNameList(List<String> multiPropertiesNameList) {
        this.multiPropertiesNameList = multiPropertiesNameList;
    }


}

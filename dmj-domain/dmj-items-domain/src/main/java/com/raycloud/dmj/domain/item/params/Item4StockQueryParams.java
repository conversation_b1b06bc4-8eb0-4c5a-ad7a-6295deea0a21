package com.raycloud.dmj.domain.item.params;

import com.google.common.collect.*;
import com.raycloud.dmj.Tuple2;
import com.raycloud.dmj.domain.item.*;
import com.raycloud.dmj.domain.account.DbInfo;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;

import java.io.Serializable;
import java.util.*;

/**
 * Created by daixiaoming on 2019-06-28.
 */
public class Item4StockQueryParams extends AbstractPageQueryParams implements Serializable {

    /**
     * 模糊搜索
     */
    public static final Integer SEARCH_TYPE_LIKE = 0;

    /**
     * 精确搜索
     */
    public static final Integer SEARCH_TYPE_ACCURATE = 1;

    /**
     * 支持的商品类型Flag
     */
    private static final Set<Integer> SUPPORT_FLAG = Sets.newHashSet(0, 1, 6, 9, 11);

    /**
     * 支持的排序字段
     */
    private static final Set<SortField> ITEM_SUPPORT_SORT_FIELDS = Sets.newHashSet(SortField.OUTER_ID, SortField.CREATED, SortField.AVAILABLE_STOCK, SortField.AVAILABLE_STOCK_SUM, SortField.STOCK_DOWN, SortField.STOCK_UP, SortField.PURCHASE_NUM, SortField.LOCK_STOCK, SortField.ESTIMATED_STOCK, SortField.VIRTUAL_STOCK, SortField.SALE_ONE_DAYS, SortField.SALE_SEVEN_DAYS, SortField.SALE_FIFTEEN_DAYS, SortField.SALE_THIRTY_DAYS, SortField.TOTAL_AVAILABLE_STOCK, SortField.TOTAL_AVAILABLE_STOCK_SUM, SortField.TOTAL_AVAILABLE_STOCK_MONEY_SUM,SortField.TOTAL_PURCHASE_NUM,SortField.TOTAL_LOCK_NUM, SortField.TITLE, SortField.ON_THE_WAY_NUM, SortField.SALE_THREE_DAYS, SortField.PRODUCT_ON_WAY_QUANTITY);

    private static final Set<SortField> SKU_SUPPORT_SORT_FIELDS = Sets.newHashSet(SortField.OUTER_ID, SortField.CREATED, SortField.PROPERTIES_NAME, SortField.AVAILABLE_STOCK, SortField.AVAILABLE_STOCK_SUM, SortField.STOCK_DOWN, SortField.STOCK_UP, SortField.PURCHASE_NUM, SortField.LOCK_STOCK, SortField.ALL_PROPERTIES_NAME, SortField.ESTIMATED_STOCK, SortField.VIRTUAL_STOCK, SortField.SALE_ONE_DAYS, SortField.SALE_SEVEN_DAYS, SortField.SALE_FIFTEEN_DAYS, SortField.SALE_THIRTY_DAYS,SortField.TOTAL_AVAILABLE_STOCK, SortField.TOTAL_AVAILABLE_STOCK_SUM, SortField.ITEM_OUTER_ID, SortField.TOTAL_AVAILABLE_STOCK_MONEY_SUM,SortField.TOTAL_PURCHASE_NUM,SortField.TOTAL_LOCK_NUM, SortField.TITLE, SortField.SIZE_ORDER, SortField.SKU_REMARK, SortField.ON_THE_WAY_NUM, SortField.SALE_THREE_DAYS, SortField.PRODUCT_ON_WAY_QUANTITY, SortField.ITEM_SKU_OUTER_ID);

    /**
     * 库存状态-正常
     */
    public static final Integer STOCK_STATUS_NORMAL = 1;

    /**
     * 库存状态-预警
     */
    public static final Integer STOCK_STATUS_WARN = 2;


    private boolean useGoodStock;

    /**
     * 商品表分表号
     */
    private Integer itemTableNo;

    /**
     * SKU商品表分表号
     */
    private Integer skuTableNo;

    /**
     * 商品对应表分表号
     */
    private Integer skuBridgeTableNo;

    /**
     * 库存表分表号
     */
    private Integer stockTableNo;

    /**
     * 库存警戒表分表号
     */
    private Integer itemWarnTableNo;

    /**
     * 商品供应商关联表分表号
     */
    private Integer itemSupplierBridgeTableNo;

    /**
     * 上传规则表分表号
     */
    private Integer uploadRuleTableNo;

    /**
     * 商品-仓库关联表分表号
     */
    private Integer itemWarehouseBridgeTableNo;

    private Integer ItemExtraInfoTableNo;

    private Integer assoGoodsSectionNo;

    /**
     * 1688映射表分表号
     */
    private Integer purchasePlatFormProductDbNo;

    private Integer periodItemDbNo;

    /**
     * 组合商品关系表的分表号
     */
    private Integer itemSuiteBridgeDbNo;

    /**
     * 是否使用纯商品SKU
     */
    private boolean usePureItemSku;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private Long userId;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库IDs
     */
    private List<Long> warehouseIds;

    private List<Long> warehouseIdList;

    /**
     * 类目ID
     */
    private List<Long> cateIdList;

    private List<String> cateIdStrList;

    /**
     * 商品类型，0-普通(包括加工和组合)，1-套件，6-加工商品, 9-组合装，11-普通(不包括加工和组合),12-分销商品
     */
    private int flag;

    /**
     * 主商家编码
     */
    private String mainOuterId;

    /**
     * 规格商家编码
     */
    private String skuOuterId;

    /**
     * 主商家编码、规格商家编码是否进行模糊查询
     */
    private boolean outerIdLike = true;

    /**
     * 没有空格且转为小写的规格商家编码
     */
    private String skuOuterIdPure;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 规格
     */
    private String propertiesName;

    /**
     * 商品ID列表
     */
    private List<Long> sysItemIdList;


    /**
     *  商品ID列表
     */
    private List<Long> lastSysItemIdList;

    /**
     * skuId列表
     */
    private List<Long> sysSkuIdList;

    /**
     * 库存状态
     */
    private List<Integer> stockStatusList;

    /**
     * 商家编码(模糊查询)
     */
    private String outerId;

    /**
     * 商家编码列表（只支持精确查询）
     */
    private List<String> outerIdList;

    /**
     * 查询关键字
     */
    private String text;

    /**
     * 根据商家编码查询，0-模糊查询，1-精准查询
     */
    private int outIdType;

    /**
     * 搜索类型，0-模糊查询，1-精准查询
     */
    private int searchType;

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 纯商品排序字段
     */
    private String itemOrderColumn;

    /**
     * SKU商品排序字段
     */
    private String skuOrderColumn;

    /**
     * 是否倒序排序
     */
    private boolean orderDesc;

    /**
     * 用户定义直接展示的类目属性
     */
    private List<Tuple2<String, String>> propertySegmentList;

    /**
     * 自定义商品属性分表号
     */
    private Integer propertySegmentBridgeTableNo;

    //----------以下为库存警戒相关查询条件----------

    /**
     * 是否设置库存警戒
     */
    private Boolean hasAlarmSetting;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 产地
     */
    private String place;

    /**
     * 警戒库存下限最小值
     */
    private Integer minWarnStockDown;

    /**
     * 警戒库存下限最大值
     */
    private Integer maxWarnStockDown;

    /**
     * 警戒库存上限最小值
     */
    private Integer minWarnStockUp;

    /**
     * 警戒库存上限最大值
     */
    private Integer maxWarnStockUp;

    /**
     * 警戒天数下限最小值
     */
    private Integer minWarnDaysDown;

    /**
     * 警戒天数下限最大值
     */
    private Integer maxWarnDaysDown;

    /**
     * 警戒天数上限最小值
     */
    private Integer minWarnDaysUp;

    /**
     * 警戒天数上限最大值
     */
    private Integer maxWarnDaysUp;

    /**
     * 采购在途数
     */
    private Integer minPurchaseNum;
    private Integer maxPurchaseNum;

    //----------以下为库存状态相关查询条件----------

    /**
     * 供应商ID列表
     */
    private List<Long> supplierIdList;

    /**
     * 品牌列表
     */
    private List<String> brandList;

    /**
     * SKU品牌列表
     */
    private List<String> skuBrandList;

    /**
     * 库存标签列表。-1表示无标签
     */
    private List<Long> stockLabelList;

    /**
     * 是否开启返库
     */
    private boolean needDepotLock;

    /**
     * 加工在途数最小值
     */
    private Integer minProductNum;

    /**
     * 加工在途数最大值
     */
    private Integer maxProductNum;

    /**
     * 实际总库存最小值
     */
    private Integer minAvailableStockSum;

    /**
     * 实际总库存最大值
     */
    private Integer maxAvailableStockSum;

    /**
     * 实际可用库存最小值
     */
    private Integer minAvailableStock;

    /**
     * 实际可用库存最大值
     */
    private Integer maxAvailableStock;

    /**
     * 次品库存最小值
     */
    private Integer minDefectiveStock;

    /**
     * 次品库存最大值
     */
    private Integer maxDefectiveStock;

    /**
     * 同款实际总库存最小值
     */
    private Integer minTotalAvailableStockSum;

    /**
     * 同款实际总库存最大值
     */
    private Integer maxTotalAvailableStockSum;

    /**
     * 同款实际可用库存最小值
     */
    private Integer minTotalAvailableStock;

    /**
     * 同款实际可用库存最大值
     */
    private Integer maxTotalAvailableStock;

    /**
     * 按SKU级别计算库存警戒差
     */
    private boolean skuLevelStockWarnDiff;

    /**
     * 库存差最小值
     */
    private Integer minStockWarnDiff;

    /**
     * 库存差最大值
     */
    private Integer maxStockWarnDiff;

    /**
     * 虚拟库存最小最大值
     * @return
     */
    private Integer minVirtualStock;
    private Integer maxVirtualStock;

    /**
     * 类目ID列表
     */
    private List<Long> catIdList;
    private List<String> catIdStrList;

    /**
     * 销量查询最大最小值
     */
    private Integer maxSaleVolume;
    private Integer minSaleVolume;

    /**
     * 销量查询天数
     */
    private Integer saleVolumeSearchDays;

    /**
     * 调用报表任意天数销量数据库时所需的参数
     */
    private Long pt;

    /**
     * 警戒库存最大最小值
     */
    private Integer minLockStock;
    private Integer maxLockStock;

    //----------以下为库存上传相关查询条件----------

    /**
     * 是否自动上传
     */
    private Boolean autoUpload;

    /**
     * 针对商家编码指定查询
     */
    private String specialOuterId;

    /**
     * 针对商家编码指定查询: 0模糊查询, 1精准查询
     */
    private Integer specialOuterIdFlag = 0;

    //----------以下非页面查询条件----------

    /**
     * 是否需要过滤供应商权限
     */
    private boolean filterSupplierAuth;

    /**
     * 是否包含未绑定供应商权限
     */
    private Boolean includeNoSupplier;

    private String orderStatus;

    /**
     * 是否排除空单,(根据销量排序时候用)
     */
    private Integer excludeScalp;

    private List<String> specialOuterIds;

    private List<String> mainOuterIds;
    private List<String> skuOuterIds;

    private Integer filterItem;

    private Integer isSkuItem;

    /**
     * 子商品商家编码
     */
    private String suiteSingleOuterId;

    private List<Integer> warnStatusList;

    private Integer activeStatus;

    private boolean fastExport;

    private boolean filterWarehouseAuth;

    /**
     * 临时表表名,为了优化导出时的深度分页使用
     */
    private String tempTableName;

    /**
     * 导出时记录的LastId
     */
    private Long exportLastId;

    private List<Long> userIdList;

    private Integer oneSixEightEightMapStatus;

    /**
     * 7天销量范围查询最大、最小值
     */
    private Integer minSale7Days;
    private Integer maxSale7Days;

    /**
     * 15天销量范围查询最大、最小值
     */
    private Integer minSale14Days;
    private Integer maxSale14Days;

    /**
     * 30天销量范围查询最大、最小值
     */
    private Integer minSale30Days;
    private Integer maxSale30Days;

    /**
     * 7天实退范围查询最大、最小值
     */
    private Integer minTruthBack7Days;
    private Integer maxTruthBack7Days;

    /**
     * 15天实退范围查询最大、最小值
     */
    private Integer minTruthBack15Days;
    private Integer maxTruthBack15Days;

    /**
     * 30天实退范围查询最大、最小值
     */
    private Integer minTruthBack30Days;
    private Integer maxTruthBack30Days;

    public Integer getMinSale7Days() {
        return minSale7Days;
    }

    public void setMinSale7Days(Integer minSale7Days) {
        this.minSale7Days = minSale7Days;
    }

    public Integer getMaxSale7Days() {
        return maxSale7Days;
    }

    public void setMaxSale7Days(Integer maxSale7Days) {
        this.maxSale7Days = maxSale7Days;
    }

    public Integer getMinSale14Days() {
        return minSale14Days;
    }

    public void setMinSale14Days(Integer minSale14Days) {
        this.minSale14Days = minSale14Days;
    }

    public Integer getMaxSale14Days() {
        return maxSale14Days;
    }

    public void setMaxSale14Days(Integer maxSale14Days) {
        this.maxSale14Days = maxSale14Days;
    }

    public Integer getMinSale30Days() {
        return minSale30Days;
    }

    public void setMinSale30Days(Integer minSale30Days) {
        this.minSale30Days = minSale30Days;
    }

    public Integer getMaxSale30Days() {
        return maxSale30Days;
    }

    public void setMaxSale30Days(Integer maxSale30Days) {
        this.maxSale30Days = maxSale30Days;
    }

    public Integer getMinTruthBack7Days() {
        return minTruthBack7Days;
    }

    public void setMinTruthBack7Days(Integer minTruthBack7Days) {
        this.minTruthBack7Days = minTruthBack7Days;
    }

    public Integer getMaxTruthBack7Days() {
        return maxTruthBack7Days;
    }

    public void setMaxTruthBack7Days(Integer maxTruthBack7Days) {
        this.maxTruthBack7Days = maxTruthBack7Days;
    }

    public Integer getMinTruthBack15Days() {
        return minTruthBack15Days;
    }

    public void setMinTruthBack15Days(Integer minTruthBack15Days) {
        this.minTruthBack15Days = minTruthBack15Days;
    }

    public Integer getMaxTruthBack15Days() {
        return maxTruthBack15Days;
    }

    public void setMaxTruthBack15Days(Integer maxTruthBack15Days) {
        this.maxTruthBack15Days = maxTruthBack15Days;
    }

    public Integer getMinTruthBack30Days() {
        return minTruthBack30Days;
    }

    public void setMinTruthBack30Days(Integer minTruthBack30Days) {
        this.minTruthBack30Days = minTruthBack30Days;
    }

    public Integer getMaxTruthBack30Days() {
        return maxTruthBack30Days;
    }

    public void setMaxTruthBack30Days(Integer maxTruthBack30Days) {
        this.maxTruthBack30Days = maxTruthBack30Days;
    }

    private boolean enableLastSysItemId = false;

    public boolean isFastExport() {
        return fastExport;
    }

    public void setFastExport(boolean fastExport) {
        this.fastExport = fastExport;
    }

    public Integer getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Integer activeStatus) {
        this.activeStatus = activeStatus;
    }

    // ----------为了兼容统一---------

    /**
     * 主商家编码
     */
    private String tileOuterId;
    /**
     * 规则商家编码
     */
    private String tileSkuOuterId;
    /**
     * 商品名称
     */
    private String tileItemName;
    /**
     * 创建时间
     */
    private Long startTime;
    /**
     * 创建时间
     */
    private Date startDate;
    /**
     * 创建时间
     */
    private Long endTime;
    /**
     * 创建时间
     */
    private Date endDate;
    /**
     * 商品备注
     */
    private String tileRemark;
    /**
     * 商品简称
     */
    private String tileShortTitle;
    /**
     * 规格别名
     */
    private String tileSkuAlias;

    /**
     * 规格名称
     */
    private String tilePropertiesName;

    private List<String> tilePropertiesNames;

    /**
     * 供应商ID
     */
    private String tileSupplierId;

    /**
     * 供应商商家编码
     */
    private String tileSupplierItemOuterId;

    /**
     * 编码条码
     */
    private String multiCode;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date modified;

    /**
     * 库存修改开始时间
     */
    private Date startStockModified;

    /**
     * 库存修改结束时间
     */
    private Date endStockModified;

    private List<String> tileOuterIds;
    private List<String> tileSkuOuterIds;
    private List<Long> pureSysItemIdList;

    /**
     * 是否是多仓查询
     */
    private boolean multiWarehouseCondition;

    /**
     * 标签查询类型
     * 0-包含任意一个选中标签 1-包含所有选中标签
     */
    private Integer tagQueryType;

    /**
     * 查询标签ID集合以逗号隔开
     */
    private String itemTagIds;

    /**
     * 查询标签ID集合
     */
    private List<Long> itemTagIdList;

    /**
     * 是否包含未关联标签的商品
     */
    private Boolean includeNoTag;


    /**
     * 商品标签表号
     */
    private Integer itemTagTableNo;

    /**
     * 商品标签关联表号
     */
    private Integer itemTagRelationTableNo;

    /**
     * 上一次查询的最大的sysItemId
     */
    private Long lastSysItemId;

    private Boolean exportFromList = false;

    private boolean wareHouseEdition = false;

    /**
     * 实体编码集合
     */
    private List<String> entityCodeList;

    /**
     * 请求来源
     */
    private String requestSource;

    /**
     * 快麦通供应商名称
     */
    private String kmtSupplierName;

    private List<DefinedJsonParams> definedJsonParams;

    private List<String> shipperList;

    /**
     * 货主id
     */
    private List<String> shipperIdList;

    private List<String> shipperItemCodeList;

    private Integer isAccurateForShipperItemCode;

    private List<DefinedJsonParams> skuDefinedJsonParams;

    /**
     * 是否需要过滤虚拟商品
     */
    private Boolean needFilterVirtual;

    /**
     * 是否拥有权限（无供应商权限时，查看商品）,此优先级高于权限配置的
     * 主要用于其他模块查看商品是否需要根据此权限（无供应商权限时，查看商品）进行过滤
     * hasNoSupplier=true 员工配置此权限不生效
     * hasNoSupplier=false 员工配置此权限生效
     */
    private boolean hasNoSupplier = false;

    /**
     * 销退在途数分表号
     */
    private Integer asInventoryDbNo;

    /**
     * 列配置开启了销退在途数
     */
    private Boolean columnConfIncludeOnTheWayNum = false;

    /**
     * 是否需要返回总数
     */
    private boolean needTotal = true;

    /**
     * 警戒状态包括采购在途数
     */
    private Integer includePurchase;

    /**
     * 商品产地
     */
    private String itemPlace;

    /**
     * 规格产地
     */
    private String skuPlace;

    private Long itemId;

    private Long skuId;

    private Long availableStockSum;

    /**
     * 规格商品按照款维度查询
     */
    private Boolean queryItem;

    /**
     * 设置库存警戒状态是否包含入库暂存区库存
     * 0-不包含 1-包含
     */
    private Integer includePurchaseInStock;

    /**
     * 是否填充暂存区库存信息
     */
    private Integer fillSectionStockInfo;

    /**
     * 是否加入分销小店
     */
    private Integer distributorState;

    private List<String> skuCidList;

    private boolean quickQuery = false;

    private boolean fillSaleNumByGift = false;

    /**
     * 商品/规格条形码
     */
    private String barcode;

    /**
     * 排除周期购商品
     * 1 排除
     */
    private Integer excludePeriodItem;

    private Integer itemCount;

    private Integer skuCount;

    private List<Long> skuCatIdList;

    private Integer hide;

    /**
     * 规格简称
     */
    private String skuShortTitle;

    /**
     * 套件子商品比例模型
     */
    private List<SimpleSuiteBridge> simpleSuiteBridges = new ArrayList<>();

    /**
     * 套件子商品比例模型长度
     */
    private Integer simpleSuiteBridgeSize;

    private boolean useThreadPool = false;

    /**
     * 根据权限过滤无品牌
     * 0 不过滤
     * 1 过滤
     */
    private Integer filterEmptyBrandByPrivilege;

    private String orderType;

    private Integer orderVal;

    public Integer getFillSectionStockInfo() {
        return fillSectionStockInfo;
    }

    public void setFillSectionStockInfo(Integer fillSectionStockInfo) {
        this.fillSectionStockInfo = fillSectionStockInfo;
    }

    public Boolean getQueryItem() {
        return queryItem;
    }

    public void setQueryItem(Boolean queryItem) {
        this.queryItem = queryItem;
    }

    public boolean getNeedAddOtherDataToCalculate() {
        return CollectionUtils.isNotEmpty(warnStatusList) &&
                ((Objects.nonNull(includePurchase) && includePurchase == CommonConstants.JUDGE_YES) || (Objects.nonNull(includePurchaseInStock) && includePurchaseInStock == CommonConstants.JUDGE_YES));
    }

    public boolean getWarehouseNeedAddOtherDataToCalculate() {
        return CollectionUtils.isNotEmpty(warnStatusList) &&
                (Objects.nonNull(includePurchase) && includePurchase == CommonConstants.JUDGE_YES);
    }

    public Boolean getNeedFilterVirtual() {
        return needFilterVirtual;
    }

    public void setNeedFilterVirtual(Boolean needFilterVirtual) {
        this.needFilterVirtual = needFilterVirtual;
    }

    public String getRequestSource() {
        return requestSource;
    }

    public void setRequestSource(String requestSource) {
        this.requestSource = requestSource;
    }

    public Integer getFilterItem() {
        return filterItem;
    }

    public void setFilterItem(Integer filterItem) {
        this.filterItem = filterItem;
    }

    public Integer getMinPurchaseNum() {
        return minPurchaseNum;
    }

    public void setMinPurchaseNum(Integer minPurchaseNum) {
        this.minPurchaseNum = minPurchaseNum;
    }

    public Integer getMaxPurchaseNum() {
        return maxPurchaseNum;
    }

    public void setMaxPurchaseNum(Integer maxPurchaseNum) {
        this.maxPurchaseNum = maxPurchaseNum;
    }

    public boolean isUseGoodStock() {
        return useGoodStock;
    }

    public void setUseGoodStock(boolean useGoodStock) {
        this.useGoodStock = useGoodStock;
    }

    public Integer getItemTableNo() {
        return itemTableNo;
    }

    public Item4StockQueryParams setItemTableNo(Integer itemTableNo) {
        this.itemTableNo = itemTableNo;
        return this;
    }

    public Integer getSkuTableNo() {
        return skuTableNo;
    }

    public Item4StockQueryParams setSkuTableNo(Integer skuTableNo) {
        this.skuTableNo = skuTableNo;
        return this;
    }

    public Integer getSkuBridgeTableNo() {
        return skuBridgeTableNo;
    }

    public Item4StockQueryParams setSkuBridgeTableNo(Integer skuBridgeTableNo) {
        this.skuBridgeTableNo = skuBridgeTableNo;
        return this;
    }

    public Integer getStockTableNo() {
        return stockTableNo;
    }

    public Item4StockQueryParams setStockTableNo(Integer stockTableNo) {
        this.stockTableNo = stockTableNo;
        return this;
    }

    public Integer getItemWarnTableNo() {
        return itemWarnTableNo;
    }

    public Item4StockQueryParams setItemWarnTableNo(Integer itemWarnTableNo) {
        this.itemWarnTableNo = itemWarnTableNo;
        return this;
    }

    public Integer getItemSupplierBridgeTableNo() {
        return itemSupplierBridgeTableNo;
    }

    public Item4StockQueryParams setItemSupplierBridgeTableNo(Integer itemSupplierBridgeTableNo) {
        this.itemSupplierBridgeTableNo = itemSupplierBridgeTableNo;
        return this;
    }

    public Integer getUploadRuleTableNo() {
        return uploadRuleTableNo;
    }

    public Item4StockQueryParams setUploadRuleTableNo(Integer uploadRuleTableNo) {
        this.uploadRuleTableNo = uploadRuleTableNo;
        return this;
    }

    public Integer getItemWarehouseBridgeTableNo() {
        return itemWarehouseBridgeTableNo;
    }

    public Item4StockQueryParams setItemWarehouseBridgeTableNo(Integer itemWarehouseBridgeTableNo) {
        this.itemWarehouseBridgeTableNo = itemWarehouseBridgeTableNo;
        return this;
    }

    public Integer getItemExtraInfoTableNo() {
        return ItemExtraInfoTableNo;
    }

    public Item4StockQueryParams setItemExtraInfoTableNo(Integer itemExtraInfoTableNo) {
        this.ItemExtraInfoTableNo = itemExtraInfoTableNo;
        return this;
    }

    public Integer getAssoGoodsSectionNo() {
        return assoGoodsSectionNo;
    }

    public Item4StockQueryParams setAssoGoodsSectionNo(Integer assoGoodsSectionNo) {
        this.assoGoodsSectionNo = assoGoodsSectionNo;
        return this;
    }

    public boolean getUsePureItemSku() {
        return usePureItemSku;
    }

    public Item4StockQueryParams setUsePureItemSku(boolean usePureItemSku) {
        this.usePureItemSku = usePureItemSku;
        return this;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public Item4StockQueryParams setCompanyId(Long companyId) {
        this.companyId = companyId;
        return this;
    }

    public Long getUserId() {
        return userId;
    }

    public Item4StockQueryParams setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public Item4StockQueryParams setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
        return this;
    }

    public List<Long> getWarehouseIds() {
        return warehouseIds;
    }

    public Item4StockQueryParams setWarehouseIds(List<Long> warehouseIds) {
        this.warehouseIds = warehouseIds;
        return this;
    }

    public List<Long> getWarehouseIdList() {
        return warehouseIdList;
    }

    public void setWarehouseIdList(List<Long> warehouseIdList) {
        this.warehouseIdList = warehouseIdList;
    }

    public List<Long> getCateIdList() {
        return cateIdList;
    }

    public Item4StockQueryParams setCateIdList(List<Long> cateIdList) {
        this.cateIdList = cateIdList;
        return this;
    }

    public List<String> getCateIdStrList() {
        return cateIdStrList;
    }

    public void setCateIdStrList(List<String> cateIdStrList) {
        this.cateIdStrList = cateIdStrList;
    }

    public int getFlag() {
        return flag;
    }

    public Item4StockQueryParams setFlag(int flag) {
        if (SUPPORT_FLAG.contains(flag)) {
            this.flag = flag;
        }
        return this;
    }

    public Item4StockQueryParams setVirtualFlag(int flag) {
        this.flag = flag;
        return this;
    }

    public String getMainOuterId() {
        return mainOuterId;
    }

    public void setMainOuterId(String mainOuterId) {
        this.mainOuterId = mainOuterId;
    }

    public String getSkuOuterId() {
        return skuOuterId;
    }

    public Item4StockQueryParams setSkuOuterId(String skuOuterId) {
        this.skuOuterId = skuOuterId;
        if (skuOuterId != null){
            skuOuterIdPure = StringUtils.deleteWhitespace(skuOuterId).replaceAll("\\u00A0","").toLowerCase();
        }
        return this;
    }

    public String getSkuOuterIdPure() {
        return skuOuterIdPure;
    }

    public Item4StockQueryParams setSkuOuterIdPure(String skuOuterIdPure) {
        this.skuOuterIdPure = skuOuterIdPure;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public Item4StockQueryParams setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getPropertiesName() {
        return propertiesName;
    }

    public Item4StockQueryParams setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
        return this;
    }

    public Item4StockQueryParams setIsSuiteItem(boolean isSuiteItem) {
        return this;
    }

    public List<Long> getSysItemIdList() {
        return sysItemIdList;
    }

    public Item4StockQueryParams setSysItemIdList(List<Long> sysItemIdList) {
        this.sysItemIdList = sysItemIdList;
        return this;
    }

    public List<Long> getSysSkuIdList() {
        return sysSkuIdList;
    }

    public Item4StockQueryParams setSysSkuIdList(List<Long> sysSkuIdList) {
        this.sysSkuIdList = sysSkuIdList;
        return this;
    }

    public List<Integer> getStockStatusList() {
        return stockStatusList;
    }

    public Item4StockQueryParams setStockStatusList(List<Integer> stockStatusList) {
        this.stockStatusList = stockStatusList;
        return this;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public List<String> getOuterIdList() {
        return outerIdList;
    }

    public Item4StockQueryParams setOuterIdList(List<String> outerIdList) {
        this.outerIdList = outerIdList;
        return this;
    }

    public String getText() {
        return text;
    }

    public Item4StockQueryParams setText(String text) {
        this.text = text;
        return this;
    }

    public int getSearchType() {
        return searchType;
    }

    public Item4StockQueryParams setSearchType(int searchType) {
        if (searchType == SEARCH_TYPE_ACCURATE) {
            this.searchType = SEARCH_TYPE_ACCURATE;
        } else {
            this.searchType = SEARCH_TYPE_LIKE;
        }
        return this;
    }

    public String getQueryType() {
        return queryType;
    }

    public Item4StockQueryParams setQueryType(String queryType) {
        this.queryType = queryType;
        return this;
    }

    public String getItemOrderColumn() {
        return itemOrderColumn;
    }

    public Item4StockQueryParams setItemOrderColumn(SortField itemSortField) {
        if (itemSortField == null) {
            this.itemOrderColumn = null;
        } else if (ITEM_SUPPORT_SORT_FIELDS.contains(itemSortField)) {
            this.itemOrderColumn = itemSortField.getValue();
        }
        return this;
    }

    public String getSkuOrderColumn() {
        return skuOrderColumn;
    }

    public Item4StockQueryParams setSkuOrderColumn(SortField skuSortField) {
        if (skuSortField == null) {
            this.skuOrderColumn = null;
        } else if (SKU_SUPPORT_SORT_FIELDS.contains(skuSortField)) {
            this.skuOrderColumn = skuSortField.getValue();
        }
        return this;
    }

    public boolean getOrderDesc() {
        return orderDesc;
    }

    public Item4StockQueryParams setOrderDesc(boolean orderDesc) {
        this.orderDesc = orderDesc;
        return this;
    }

    public Boolean getHasAlarmSetting() {
        return hasAlarmSetting;
    }

    public Item4StockQueryParams setHasAlarmSetting(Boolean hasAlarmSetting) {
        this.hasAlarmSetting = hasAlarmSetting;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public Item4StockQueryParams setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public String getPlace() {
        return place;
    }

    public Item4StockQueryParams setPlace(String place) {
        this.place = place;
        return this;
    }

    public Integer getMinWarnStockDown() {
        return minWarnStockDown;
    }

    public Item4StockQueryParams setMinWarnStockDown(Integer minWarnStockDown) {
        this.minWarnStockDown = minWarnStockDown;
        return this;
    }

    public Integer getMaxWarnStockDown() {
        return maxWarnStockDown;
    }

    public Item4StockQueryParams setMaxWarnStockDown(Integer maxWarnStockDown) {
        this.maxWarnStockDown = maxWarnStockDown;
        return this;
    }

    public Integer getMinWarnStockUp() {
        return minWarnStockUp;
    }

    public Item4StockQueryParams setMinWarnStockUp(Integer minWarnStockUp) {
        this.minWarnStockUp = minWarnStockUp;
        return this;
    }

    public Integer getMaxWarnStockUp() {
        return maxWarnStockUp;
    }

    public Item4StockQueryParams setMaxWarnStockUp(Integer maxWarnStockUp) {
        this.maxWarnStockUp = maxWarnStockUp;
        return this;
    }

    public Integer getMinWarnDaysDown() {
        return minWarnDaysDown;
    }

    public Item4StockQueryParams setMinWarnDaysDown(Integer minWarnDaysDown) {
        this.minWarnDaysDown = minWarnDaysDown;
        return this;
    }

    public Integer getMaxWarnDaysDown() {
        return maxWarnDaysDown;
    }

    public Item4StockQueryParams setMaxWarnDaysDown(Integer maxWarnDaysDown) {
        this.maxWarnDaysDown = maxWarnDaysDown;
        return this;
    }

    public Integer getMinWarnDaysUp() {
        return minWarnDaysUp;
    }

    public Item4StockQueryParams setMinWarnDaysUp(Integer minWarnDaysUp) {
        this.minWarnDaysUp = minWarnDaysUp;
        return this;
    }

    public Integer getMaxWarnDaysUp() {
        return maxWarnDaysUp;
    }

    public Item4StockQueryParams setMaxWarnDaysUp(Integer maxWarnDaysUp) {
        this.maxWarnDaysUp = maxWarnDaysUp;
        return this;
    }

    public List<Long> getSupplierIdList() {
        return supplierIdList;
    }

    public Item4StockQueryParams setSupplierIdList(List<Long> supplierIdList) {
        this.supplierIdList = supplierIdList;
        return this;
    }

    public List<String> getBrandList() {
        return brandList;
    }

    public void setBrandList(List<String> brandList) {
        this.brandList = brandList;
    }

    public List<String> getSkuBrandList() {
        return skuBrandList;
    }

    public void setSkuBrandList(List<String> skuBrandList) {
        this.skuBrandList = skuBrandList;
    }

    public boolean getNeedDepotLock() {
        return needDepotLock;
    }

    public Item4StockQueryParams setNeedDepotLock(boolean needDepotLock) {
        this.needDepotLock = needDepotLock;
        return this;
    }

    public Integer getMinAvailableStockSum() {
        return minAvailableStockSum;
    }

    public Item4StockQueryParams setMinAvailableStockSum(Integer minAvailableStockSum) {
        this.minAvailableStockSum = minAvailableStockSum;
        return this;
    }

    public Integer getMaxAvailableStockSum() {
        return maxAvailableStockSum;
    }

    public Item4StockQueryParams setMaxAvailableStockSum(Integer maxAvailableStockSum) {
        this.maxAvailableStockSum = maxAvailableStockSum;
        return this;
    }

    public Integer getMinAvailableStock() {
        return minAvailableStock;
    }

    public Item4StockQueryParams setMinAvailableStock(Integer minAvailableStock) {
        this.minAvailableStock = minAvailableStock;
        return this;
    }

    public Integer getMaxAvailableStock() {
        return maxAvailableStock;
    }

    public Item4StockQueryParams setMaxAvailableStock(Integer maxAvailableStock) {
        this.maxAvailableStock = maxAvailableStock;
        return this;
    }

    public Integer getMinDefectiveStock() {
        return minDefectiveStock;
    }

    public Item4StockQueryParams setMinDefectiveStock(Integer minDefectiveStock) {
        this.minDefectiveStock = minDefectiveStock;
        return this;
    }

    public Integer getMaxDefectiveStock() {
        return maxDefectiveStock;
    }

    public Item4StockQueryParams setMaxDefectiveStock(Integer maxDefectiveStock) {
        this.maxDefectiveStock = maxDefectiveStock;
        return this;
    }

    public Integer getMinTotalAvailableStockSum() {
        return minTotalAvailableStockSum;
    }

    public Item4StockQueryParams setMinTotalAvailableStockSum(Integer minTotalAvailableStockSum) {
        this.minTotalAvailableStockSum = minTotalAvailableStockSum;
        return this;
    }

    public Integer getMaxTotalAvailableStockSum() {
        return maxTotalAvailableStockSum;
    }

    public Item4StockQueryParams setMaxTotalAvailableStockSum(Integer maxTotalAvailableStockSum) {
        this.maxTotalAvailableStockSum = maxTotalAvailableStockSum;
        return this;
    }

    public Integer getMinTotalAvailableStock() {
        return minTotalAvailableStock;
    }

    public Item4StockQueryParams setMinTotalAvailableStock(Integer minTotalAvailableStock) {
        this.minTotalAvailableStock = minTotalAvailableStock;
        return this;
    }

    public Integer getMaxTotalAvailableStock() {
        return maxTotalAvailableStock;
    }

    public Item4StockQueryParams setMaxTotalAvailableStock(Integer maxTotalAvailableStock) {
        this.maxTotalAvailableStock = maxTotalAvailableStock;
        return this;
    }

    public boolean getSkuLevelStockWarnDiff() {
        return skuLevelStockWarnDiff;
    }

    public Item4StockQueryParams setSkuLevelStockWarnDiff(boolean skuLevelStockWarnDiff) {
        this.skuLevelStockWarnDiff = skuLevelStockWarnDiff;
        return this;
    }

    public Integer getMinStockWarnDiff() {
        return minStockWarnDiff;
    }

    public Item4StockQueryParams setMinStockWarnDiff(Integer minStockWarnDiff) {
        this.minStockWarnDiff = minStockWarnDiff;
        return this;
    }

    public Integer getMaxStockWarnDiff() {
        return maxStockWarnDiff;
    }

    public Item4StockQueryParams setMaxStockWarnDiff(Integer maxStockWarnDiff) {
        this.maxStockWarnDiff = maxStockWarnDiff;
        return this;
    }

    public Boolean getAutoUpload() {
        return autoUpload;
    }

    public Item4StockQueryParams setAutoUpload(Boolean autoUpload) {
        this.autoUpload = autoUpload;
        return this;
    }

    public boolean getFilterSupplierAuth() {
        return filterSupplierAuth;
    }

    public void setFilterSupplierAuth(boolean filterSupplierAuth) {
        this.filterSupplierAuth = filterSupplierAuth;
    }

    public Boolean getIncludeNoSupplier() {
        return includeNoSupplier;
    }

    public void setIncludeNoSupplier(Boolean includeNoSupplier) {
        this.includeNoSupplier = includeNoSupplier;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getExcludeScalp() {
        return excludeScalp;
    }

    public void setExcludeScalp(Integer excludeScalp) {
        this.excludeScalp = excludeScalp;
    }

    public String getSpecialOuterId() {
        return specialOuterId;
    }

    public void setSpecialOuterId(String specialOuterId) {
        this.specialOuterId = specialOuterId;
    }

    public Integer getSpecialOuterIdFlag() {
        return specialOuterIdFlag;
    }

    public void setSpecialOuterIdFlag(Integer specialOuterIdFlag) {
        this.specialOuterIdFlag = specialOuterIdFlag;
    }

    public Integer getMinVirtualStock() {
        return minVirtualStock;
    }

    public void setMinVirtualStock(Integer minVirtualStock) {
        this.minVirtualStock = minVirtualStock;
    }

    public Integer getMaxVirtualStock() {
        return maxVirtualStock;
    }

    public void setMaxVirtualStock(Integer maxVirtualStock) {
        this.maxVirtualStock = maxVirtualStock;
    }

    public List<Long> getStockLabelList() {
        return stockLabelList;
    }

    public void setStockLabelList(List<Long> stockLabelList) {
        this.stockLabelList = stockLabelList;
    }

    public List<String> getSpecialOuterIds() {
        return specialOuterIds;
    }

    public void setSpecialOuterIds(List<String> specialOuterIds) {
        this.specialOuterIds = specialOuterIds;
    }

    public Integer getMaxSaleVolume() {
        return maxSaleVolume;
    }

    public void setMaxSaleVolume(Integer maxSaleVolume) {
        this.maxSaleVolume = maxSaleVolume;
    }

    public Integer getMinSaleVolume() {
        return minSaleVolume;
    }

    public void setMinSaleVolume(Integer minSaleVolume) {
        this.minSaleVolume = minSaleVolume;
    }

    public Integer getSaleVolumeSearchDays() {
        return saleVolumeSearchDays;
    }

    public void setSaleVolumeSearchDays(Integer saleVolumeSearchDays) {
        this.saleVolumeSearchDays = saleVolumeSearchDays;
    }

    public Integer getMinLockStock() {
        return minLockStock;
    }

    public void setMinLockStock(Integer minLockStock) {
        this.minLockStock = minLockStock;
    }

    public Integer getMaxLockStock() {
        return maxLockStock;
    }

    public void setMaxLockStock(Integer maxLockStock) {
        this.maxLockStock = maxLockStock;
    }

    public Long getPt() {
        return pt;
    }

    public Item4StockQueryParams setPt(Long pt) {
        this.pt = pt;
        return this;
    }
    public List<String> getMainOuterIds() {
        return mainOuterIds;
    }

    public void setMainOuterIds(List<String> mainOuterIds) {
        this.mainOuterIds = mainOuterIds;
    }

    public List<String> getSkuOuterIds() {
        return skuOuterIds;
    }

    public void setSkuOuterIds(List<String> skuOuterIds) {
        this.skuOuterIds = skuOuterIds;
    }

    public Integer getIsSkuItem() {
        return isSkuItem;
    }

    public void setIsSkuItem(Integer isSkuItem) {
        this.isSkuItem = isSkuItem;
    }

    public String getSuiteSingleOuterId() {
        return suiteSingleOuterId;
    }

    public void setSuiteSingleOuterId(String suiteSingleOuterId) {
        this.suiteSingleOuterId = suiteSingleOuterId;
    }

    public List<Integer> getWarnStatusList() {
        return warnStatusList;
    }

    public void setWarnStatusList(List<Integer> warnStatusList) {
        this.warnStatusList = warnStatusList;
    }

    public List<Tuple2<String, String>> getPropertySegmentList() {
        return propertySegmentList;
    }

    public void setPropertySegmentList(List<Tuple2<String, String>> propertySegmentList) {
        this.propertySegmentList = propertySegmentList;
    }

    public Integer getPropertySegmentBridgeTableNo() {
        return propertySegmentBridgeTableNo;
    }

    public Item4StockQueryParams setPropertySegmentBridgeTableNo(Integer propertySegmentBridgeTableNo) {
        this.propertySegmentBridgeTableNo = propertySegmentBridgeTableNo;
        return this;
    }

    public boolean isMultiWarehouseCondition() {
        return multiWarehouseCondition;
    }

    public void setMultiWarehouseCondition(boolean multiWarehouseCondition) {
        this.multiWarehouseCondition = multiWarehouseCondition;
    }

    public boolean isFilterWarehouseAuth() {
        return filterWarehouseAuth;
    }

    public void setFilterWarehouseAuth(boolean filterWarehouseAuth) {
        this.filterWarehouseAuth = filterWarehouseAuth;
    }

    //----------以下是特殊判断，不对应属性----------

    public boolean isSuiteItem() {
        return flag == 1;
    }

    /**
     * 是否需要同款总库存字段
     */
    public boolean getNeedTotalAvailableStockSumField() {
        //需要根据同款总库存过滤
        return minTotalAvailableStockSum != null
                || maxTotalAvailableStockSum != null
                || SortField.TOTAL_AVAILABLE_STOCK_SUM.getValue().equals(itemOrderColumn)
                || SortField.TOTAL_AVAILABLE_STOCK_MONEY_SUM.getValue().equals(itemOrderColumn)
                || SortField.TOTAL_PURCHASE_NUM.getValue().equals(itemOrderColumn)
                || SortField.TOTAL_LOCK_NUM.getValue().equals(itemOrderColumn);
    }

    /**
     * 是否需要同款可用数字段
     */
    public boolean getNeedTotalAvailableInStockField() {
        if (minTotalAvailableStock != null || maxTotalAvailableStock != null) {
            //需要根据同款可用数过滤
            return true;
        }

        if (!skuLevelStockWarnDiff && (minStockWarnDiff != null || maxStockWarnDiff != null)) {
            //需要根据款级别库存差过滤
            return true;
        }

        if(SortField.TOTAL_AVAILABLE_STOCK.getValue().equals(itemOrderColumn)){
            return true;
        }

        return false;
    }

    /**
     * 是否需要实际总库存字段
     */
    public boolean getNeedAvailableStockSumField() {
        //需要根据实际总库存过滤
        if (minAvailableStockSum != null || maxAvailableStockSum != null) {
            return true;
        }

        return SortField.AVAILABLE_STOCK_SUM.getValue().equals(itemOrderColumn);
    }

    /**
     * 是否需要实际可用数字段
     */
    public boolean getNeedAvailableInStockField() {
        //根据实际可用数过滤
        if (minAvailableStock != null || maxAvailableStock != null) {
            return true;
        }
        if (skuLevelStockWarnDiff && (minStockWarnDiff != null || maxStockWarnDiff != null)) {
            //根据SKU级别库存差过滤
            return true;
        }

        if (CollectionUtils.isNotEmpty(stockStatusList)){
            //根据库存状态过滤
            return true;
        }

        if (CollectionUtils.isNotEmpty(warnStatusList)) {
            return true;
        }

        return SortField.AVAILABLE_STOCK.getValue().equals(itemOrderColumn);
    }

    /**
     * 是否需要次品数字段
     */
    public boolean getNeedDefectiveStockField() {
        //需要根据次品数过滤
        return minDefectiveStock != null || maxDefectiveStock != null;
    }

    /**
     * 是否需要采购在途数字段
     */
    public boolean getNeedPurchaseNumField() {
        //需要根据采购在途数过滤
        if (minPurchaseNum != null || maxPurchaseNum != null) {
            return true;
        }
        return SortField.PURCHASE_NUM.getValue().equals(itemOrderColumn)
                || SortField.TOTAL_PURCHASE_NUM.getValue().equals(itemOrderColumn);
    }

    /**
     * 是否需要虚拟库存字段
     */
    public boolean getNeedVirtualStockField() {
        //需要根据虚拟库存过滤
        if (minVirtualStock != null || maxVirtualStock !=null) {
            return true;
        }

        return SortField.VIRTUAL_STOCK.getValue().equals(itemOrderColumn);
    }

    /**
     * 是否需要商品总库存字段(纯商品专用)
     */
    public boolean getNeedItemAvailableStockSumField() {
        if(getNeedTotalAvailableStockSumField()){
            return true;
        }

        return getNeedAvailableStockSumField();
    }

    /**
     * 是否需要商品可用数字段(纯商品专用)
     */
    public boolean getNeedItemAvailableInStockField() {
        if(getNeedTotalAvailableInStockField()){
            return true;
        }

        return getNeedAvailableInStockField();
    }

    public List<Long> getCatIdList() {
        return catIdList;
    }

    public void setCatIdList(List<Long> catIdList) {
        this.catIdList = catIdList;
    }

    public List<String> getCatIdStrList() {
        return catIdStrList;
    }

    public void setCatIdStrList(List<String> catIdStrList) {
        this.catIdStrList = catIdStrList;
    }

    /**
     * 是否根据供应商商家编码过滤
     */
    public boolean getFilterBySupplierItemOuterId() {
        return ("supplierItemOuterId".equals(queryType) && StringUtils.isNotEmpty(text)) || StringUtils.isNotEmpty(this.tileSupplierItemOuterId);
    }

    /**
     * 是否需要Join供应商商品关联表
     */
    public boolean getNeedJoinSupplierBridgeTable() {
        return filterSupplierAuth || getFilterBySupplierItemOuterId() || StringUtils.isNotEmpty(this.tileSupplierItemOuterId);
    }

    /**
     * 是否需要Join kmt供销商商品关联表
     */
    public boolean getNeedJoinKmtItemSupplierTable() {
        return flag == 12;
    }

    /**
     * 是否需要Join库存警戒表
     */
    public boolean getNeedJoinItemWarnTable() {
        if (CollectionUtils.isNotEmpty(stockStatusList) && (stockStatusList.contains(STOCK_STATUS_NORMAL) || stockStatusList.contains(STOCK_STATUS_WARN))) {
            //如果指定了库存状态筛选条件
            return true;
        }

        if (CollectionUtils.isNotEmpty(warnStatusList)) {
            return true;
        }

        if (hasAlarmSetting != null) {
            //如果指定了是否设置库存预警筛选条件
            return true;
        }

        if (minWarnStockDown != null || maxWarnStockDown != null || minWarnStockUp != null || maxWarnStockUp != null || minWarnDaysDown != null || maxWarnDaysDown != null || minWarnDaysUp != null || maxWarnDaysUp != null) {
            //如果指定了库存预警范围值筛选条件
            return true;
        }

        return false;
    }

    /**
     * 是否需要Join纯商品总库存临时表（纯商品款级别和SKU级别是一样的）
     */
    public boolean getNeedJoinItemTotalStockTemp() {
        if (getNeedItemAvailableStockSumField()) {
            return true;
        }

        if (getNeedItemAvailableInStockField()) {
            return true;
        }

        if (getNeedDefectiveStockField()) {
            return true;
        }

        if (getNeedPurchaseNumField()) {
            return true;
        }

        if (getNeedVirtualStockField()) {
            return true;
        }

        if (getNeedLockStock()) {
            return true;
        }

        if (getNeedProductOnWayQuantity()) {
            return true;
        }

        if (SortField.PURCHASE_NUM.getValue().equals(itemOrderColumn)
                || SortField.LOCK_STOCK.getValue().equals(itemOrderColumn)
                || SortField.ESTIMATED_STOCK.getValue().equals(itemOrderColumn)) {
            //如果指定了库存相关列排序
            return true;
        }

        return false;
    }

    /**
     * 按款导出是否需要前置查询规格条件
     * @return
     */
    public boolean getNeedHandleSkuQuery() {
        if (StringUtils.isNotEmpty(skuOuterId) || CollectionUtils.isNotEmpty(skuOuterIds) || CollectionUtils.isNotEmpty(tileSkuOuterIds) || StringUtils.isNotEmpty(propertiesName)
                || CollectionUtils.isNotEmpty(sysSkuIdList) || StringUtils.isNotEmpty(brand) || StringUtils.isNotEmpty(skuPlace) || CollectionUtils.isNotEmpty(skuCidList)
                || StringUtils.isNotEmpty(tileSkuAlias) || StringUtils.isNotEmpty(tilePropertiesName) || CollectionUtils.isNotEmpty(skuBrandList)) {
            return true;
        }

        if (StringUtils.isNotEmpty(text)
                && ("propertiesName".equals(queryType) || "skuAlias".equals(queryType) || "skuRemark".equals(queryType) || "skuBarcode".equals(queryType) || "skuShortTitle".equals(queryType))) {
            return true;
        }
        return false;
    }

    /**
     * count时是否需要Join纯商品总库存临时表（纯商品款级别和SKU级别是一样的）
     */
    public boolean getNeedJoinItemTotalStockTempByCount() {
        if (minAvailableStockSum != null || maxAvailableStockSum != null) {
            return true;
        }

        if (minTotalAvailableStockSum != null || maxTotalAvailableStockSum != null) {
            return true;
        }

        if (minTotalAvailableStock != null || maxTotalAvailableStock != null) {
            return true;
        }

        if (!skuLevelStockWarnDiff && (minStockWarnDiff != null || maxStockWarnDiff != null)) {
            return true;
        }

        if (minAvailableStock != null || maxAvailableStock != null) {
            return true;
        }

        if (skuLevelStockWarnDiff && (minStockWarnDiff != null || maxStockWarnDiff != null)) {
            return true;
        }

        if (CollectionUtils.isNotEmpty(stockStatusList)){
            return true;
        }

        if (CollectionUtils.isNotEmpty(warnStatusList)) {
            return true;
        }

        if (getNeedDefectiveStockField()) {
            return true;
        }

        if (getNeedProductOnWayQuantity()) {
            return true;
        }

        if (minPurchaseNum != null || maxPurchaseNum != null) {
            return true;
        }

        if (minVirtualStock != null || maxVirtualStock !=null) {
            return true;
        }

        return maxLockStock != null || minLockStock != null;
    }

    /**
     * 是否需要Join规格款级别总库存临时表
     */
    public boolean getNeedJoinSkuTotalStockTemp() {
        if (getNeedTotalAvailableStockSumField()) {
            return true;
        }

        if (getNeedTotalAvailableInStockField()) {
            return true;
        }

        return false;
    }

    /**
     * count时是否需要Join规格款级别总库存临时表
     */
    public boolean getNeedJoinSkuTotalStockTempByCount() {
        if (minTotalAvailableStockSum != null || maxTotalAvailableStockSum != null) {
            return true;
        }

        if (minTotalAvailableStock != null || maxTotalAvailableStock != null) {
            //需要根据同款可用数过滤
            return true;
        }

        if (!skuLevelStockWarnDiff && (minStockWarnDiff != null || maxStockWarnDiff != null)) {
            //需要根据款级别库存差过滤
            return true;
        }

        return false;
    }

    /**
     * 是否需要Join规格级别总库存临时表
     */
    public boolean getNeedJoinSkuStockTemp() {
        if (getNeedAvailableStockSumField()) {
            return true;
        }

        if (getNeedAvailableInStockField()) {
            return true;
        }

        if (getNeedDefectiveStockField()) {
            return true;
        }

        if (getNeedPurchaseNumField()) {
            return true;
        }

        if (getNeedVirtualStockField()) {
            return true;
        }

        if (getNeedLockStock()) {
            return true;
        }

        if (getNeedProductOnWayQuantity()) {
            return true;
        }

        if (SortField.PURCHASE_NUM.getValue().equals(skuOrderColumn)
                || SortField.LOCK_STOCK.getValue().equals(skuOrderColumn)
                || SortField.ESTIMATED_STOCK.getValue().equals(skuOrderColumn)) {
            //如果指定了库存相关列排序
            return true;
        }

        return false;
    }

    /**
     * count时是否需要Join规格级别总库存临时表
     */
    public boolean getNeedJoinSkuStockTempByCount() {
        if (minAvailableStockSum != null || maxAvailableStockSum != null) {
            return true;
        }

        if (minAvailableStock != null || maxAvailableStock != null) {
            return true;
        }
        if (skuLevelStockWarnDiff && (minStockWarnDiff != null || maxStockWarnDiff != null)) {
            return true;
        }

        if (CollectionUtils.isNotEmpty(stockStatusList)){
            return true;
        }

        if (CollectionUtils.isNotEmpty(warnStatusList)) {
            return true;
        }

        if (getNeedDefectiveStockField()) {
            return true;
        }

        if (getNeedProductOnWayQuantity()) {
            return true;
        }

        if (minPurchaseNum != null || maxPurchaseNum != null) {
            return true;
        }

        if (minVirtualStock != null || maxVirtualStock !=null) {
            return true;
        }

        return maxLockStock != null || minLockStock != null;
    }

    /**
     * 是否需要Join库存下限临时表
     */
    public boolean getNeedJoinStockDownTemp() {
        return minStockWarnDiff != null || maxStockWarnDiff != null;
    }

    /**
     * 是否需要Join上传规则表
     */
    public boolean getNeedJoinUploadRule() {
        return autoUpload != null;
    }

    public boolean getNeedJoinReportOrderTable(){
        return SortField.SALE_ONE_DAYS.getValue().equals(itemOrderColumn) || SortField.SALE_SEVEN_DAYS.getValue().equals(itemOrderColumn) || SortField.SALE_FIFTEEN_DAYS.getValue().equals(itemOrderColumn) || SortField.SALE_THIRTY_DAYS.getValue().equals(itemOrderColumn) || SortField.SALE_THREE_DAYS.getValue().equals(itemOrderColumn)
                || getBetweenSale()
                || SortField.TRUTH_BACK_1_DAYS.getValue().equals(itemOrderColumn) || SortField.TRUTH_BACK_7_DAYS.getValue().equals(itemOrderColumn) || SortField.TRUTH_BACK_15_DAYS.getValue().equals(itemOrderColumn) || SortField.TRUTH_BACK_30_DAYS.getValue().equals(itemOrderColumn);
    }

    public boolean getBetweenSale() {
        return minSale7Days != null || maxSale7Days != null || minSale14Days != null || maxSale14Days != null || minSale30Days!= null || maxSale30Days != null ||
                minTruthBack7Days != null || maxTruthBack7Days != null || minTruthBack15Days != null || maxTruthBack15Days != null || minTruthBack30Days != null || maxTruthBack30Days != null;
    }

    public boolean getNeedJoinReportSaleNumTable() {
        return minSaleVolume != null || maxSaleVolume != null;
    }

    public Date getSaleVolumeStartDate() {
        return new DateTime().minusDays(Optional.ofNullable(saleVolumeSearchDays).orElse(SmartPurchaseRule.DEFAULT_SALE_DAYS)).millisOfDay().withMinimumValue().toDate();
    }

    public Date getSaleVolumeEndDate() {
        return new DateTime().minusDays(1).millisOfDay().withMinimumValue().toDate();
    }

    public String getTileOuterId() {
        return tileOuterId;
    }

    public void setTileOuterId(String tileOuterId) {
        this.tileOuterId = tileOuterId;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getAvailableStockSum() {
        return availableStockSum;
    }

    public void setAvailableStockSum(Long availableStockSum) {
        this.availableStockSum = availableStockSum;
    }

    public boolean getNeedSkuAvailableStockSum() {
        return useThreadPool && "availableStockSum".equals(skuOrderColumn);
    }

    public boolean getNeedItemAvailableStockSum() {
        return useThreadPool && "availableStockSum".equals(itemOrderColumn);
    }

    public String getTileSkuOuterId() {
        return tileSkuOuterId;
    }

    public void setTileSkuOuterId(String tileSkuOuterId) {
        this.tileSkuOuterId = tileSkuOuterId;
    }

    public String getTileItemName() {
        return tileItemName;
    }

    public void setTileItemName(String tileItemName) {
        this.tileItemName = tileItemName;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public String getTileRemark() {
        return tileRemark;
    }

    public void setTileRemark(String tileRemark) {
        this.tileRemark = tileRemark;
    }

    public String getTileShortTitle() {
        return tileShortTitle;
    }

    public void setTileShortTitle(String tileShortTitle) {
        this.tileShortTitle = tileShortTitle;
    }

    public String getTileSkuAlias() {
        return tileSkuAlias;
    }

    public void setTileSkuAlias(String tileSkuAlias) {
        this.tileSkuAlias = tileSkuAlias;
    }

    public String getTilePropertiesName() {
        return tilePropertiesName;
    }

    public void setTilePropertiesName(String tilePropertiesName) {
        this.tilePropertiesName = tilePropertiesName;
    }

    public List<String> getTilePropertiesNames() {
        return tilePropertiesNames;
    }

    public void setTilePropertiesNames(List<String> tilePropertiesNames) {
        this.tilePropertiesNames = tilePropertiesNames;
    }

    public String getTileSupplierId() {
        return tileSupplierId;
    }

    public void setTileSupplierId(String tileSupplierId) {
        this.tileSupplierId = tileSupplierId;
    }

    public String getTileSupplierItemOuterId() {
        return tileSupplierItemOuterId;
    }

    public void setTileSupplierItemOuterId(String tileSupplierItemOuterId) {
        this.tileSupplierItemOuterId = tileSupplierItemOuterId;
    }

    public String getMultiCode() {
        return multiCode;
    }

    public void setMultiCode(String multiCode) {
        this.multiCode = multiCode;
    }

    public boolean getNeedLockStock(){
        return "lockStock".equals(itemOrderColumn) || "lockStock".equals(skuOrderColumn)
                || maxLockStock != null || minLockStock != null;
    }

    private boolean getNeedProductOnWayQuantity() {
        return "productOnWayQuantity".equals(itemOrderColumn) || "productOnWayQuantity".equals(skuOrderColumn)
                || minProductNum != null || maxProductNum != null;
    }

    public boolean getHasNoStockLabel() {
        return CollectionUtils.isNotEmpty(stockLabelList)
                && stockLabelList.stream().anyMatch(value -> value == -1L);
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Date getStartStockModified() {
        return startStockModified;
    }

    public void setStartStockModified(Date startStockModified) {
        this.startStockModified = startStockModified;
    }

    public Date getEndStockModified() {
        return endStockModified;
    }

    public void setEndStockModified(Date endStockModified) {
        this.endStockModified = endStockModified;
    }

    public List<String> getTileOuterIds() {
        return tileOuterIds;
    }

    public void setTileOuterIds(List<String> tileOuterIds) {
        this.tileOuterIds = tileOuterIds;
    }

    public List<String> getTileSkuOuterIds() {
        return tileSkuOuterIds;
    }

    public void setTileSkuOuterIds(List<String> tileSkuOuterIds) {
        this.tileSkuOuterIds = tileSkuOuterIds;
    }

    public List<Long> getPureSysItemIdList() {
        return pureSysItemIdList;
    }

    public void setPureSysItemIdList(List<Long> pureSysItemIdList) {
        this.pureSysItemIdList = pureSysItemIdList;
    }

    public Integer getTagQueryType() {
        return tagQueryType;
    }

    public void setTagQueryType(Integer tagQueryType) {
        this.tagQueryType = tagQueryType;
    }

    public String getItemTagIds() {
        return itemTagIds;
    }

    public void setItemTagIds(String itemTagIds) {
        this.itemTagIds = itemTagIds;
    }

    public List<Long> getItemTagIdList() {
        return itemTagIdList;
    }

    public void setItemTagIdList(List<Long> itemTagIdList) {
        this.itemTagIdList = itemTagIdList;
    }

    public Boolean getIncludeNoTag() {
        return includeNoTag;
    }

    public void setIncludeNoTag(Boolean includeNoTag) {
        this.includeNoTag = includeNoTag;
    }

    public Integer getItemTagTableNo() {
        return itemTagTableNo;
    }

    public Item4StockQueryParams setItemTagTableNo(Integer itemTagTableNo) {
        this.itemTagTableNo = itemTagTableNo;
        return this;
    }

    public String getKmtSupplierName() {
        return kmtSupplierName;
    }

    public void setKmtSupplierName(String kmtSupplierName) {
        this.kmtSupplierName = kmtSupplierName;
    }

    public Integer getItemTagRelationTableNo() {
        return itemTagRelationTableNo;
    }

    public Item4StockQueryParams setItemTagRelationTableNo(Integer itemTagRelationTableNo) {
        this.itemTagRelationTableNo = itemTagRelationTableNo;
        return this;
    }

    public String getTempTableName() {
        return tempTableName;
    }

    public void setTempTableName(String tempTableName) {
        this.tempTableName = tempTableName;
    }

    public Long getExportLastId() {
        return exportLastId;
    }

    public void setExportLastId(Long exportLastId) {
        this.exportLastId = exportLastId;
    }

    public Long getLastSysItemId() {
        return lastSysItemId;
    }

    public void setLastSysItemId(Long lastSysItemId) {
        this.lastSysItemId = lastSysItemId;
    }

    public int getOutIdType() {
        return outIdType;
    }

    public void setOutIdType(int outIdType) {
        this.outIdType = outIdType;
    }

    public List<String> getEntityCodeList() {
        return entityCodeList;
    }

    public void setEntityCodeList(List<String> entityCodeList) {
        this.entityCodeList = entityCodeList;
    }

    /**
     * 填充一个没有商品的条件, 在后续流程直接认为没有商品, 直接返回空
     */
    public void setNonItemCondition(){
        this.sysItemIdList = Lists.newArrayList();
        this.sysSkuIdList = Lists.newArrayList();
    }

    /**
     * 是否需要distinct进行去重
     */
    public boolean getNeedDistinct() {
        return getNeedJoinItemWarnTable() || this.stockLabelList != null;
    }

    public boolean isWareHouseEdition() {
        return wareHouseEdition;
    }

    public void setWareHouseEdition(boolean wareHouseEdition) {
        this.wareHouseEdition = wareHouseEdition;
    }

    public Boolean getExportFromList() {
        return exportFromList;
    }

    public void setExportFromList(Boolean exportFromList) {
        this.exportFromList = exportFromList;
    }

    public boolean isHasNoSupplier() {
        return hasNoSupplier;
    }

    public void setHasNoSupplier(boolean hasNoSupplier) {
        this.hasNoSupplier = hasNoSupplier;
    }

    public boolean isNeedTotal() {
        return needTotal;
    }

    public void setNeedTotal(boolean needTotal) {
        this.needTotal = needTotal;
    }

    /**
     * 是否需要Join销退在途数表
     */
    public boolean getNeedJoinAsInventory() {
        if (SortField.ON_THE_WAY_NUM.getValue().equals(itemOrderColumn)) {
            //如果指定了库存相关列排序
            return true;
        }
        return false;
    }

    public Integer getAsInventoryDbNo() {
        return asInventoryDbNo;
    }

    public Item4StockQueryParams setAsInventoryDbNo(Integer asInventoryDbNo) {
        this.asInventoryDbNo = asInventoryDbNo;
        return this;
    }

    public Boolean getColumnConfIncludeOnTheWayNum() {
        return columnConfIncludeOnTheWayNum;
    }

    public void setColumnConfIncludeOnTheWayNum(Boolean columnConfIncludeOnTheWayNum) {
        this.columnConfIncludeOnTheWayNum = columnConfIncludeOnTheWayNum;
    }

    public List<DefinedJsonParams> getDefinedJsonParams() {
        return definedJsonParams;
    }

    public void setDefinedJsonParams(List<DefinedJsonParams> definedJsonParams) {
        this.definedJsonParams = definedJsonParams;
    }

    public List<DefinedJsonParams> getSkuDefinedJsonParams() {
        return skuDefinedJsonParams;
    }

    public void setSkuDefinedJsonParams(List<DefinedJsonParams> skuDefinedJsonParams) {
        this.skuDefinedJsonParams = skuDefinedJsonParams;
    }

    public Integer getIncludePurchase() {
        return includePurchase;
    }

    public void setIncludePurchase(Integer includePurchase) {
        this.includePurchase = includePurchase;
    }

    public List<String> getShipperList() {
        return shipperList;
    }

    public void setShipperList(List<String> shipperList) {
        this.shipperList = shipperList;
    }

    public List<String> getShipperIdList() {
        return shipperIdList;
    }

    public void setShipperIdList(List<String> shipperIdList) {
        this.shipperIdList = shipperIdList;
    }

    public List<String> getShipperItemCodeList() {
        return shipperItemCodeList;
    }

    public void setShipperItemCodeList(List<String> shipperItemCodeList) {
        this.shipperItemCodeList = shipperItemCodeList;
    }

    public Integer getIsAccurateForShipperItemCode() {
        return isAccurateForShipperItemCode;
    }

    public void setIsAccurateForShipperItemCode(Integer isAccurateForShipperItemCode) {
        this.isAccurateForShipperItemCode = isAccurateForShipperItemCode;
    }

    public Integer getIncludePurchaseInStock() {
        return includePurchaseInStock;
    }

    public void setIncludePurchaseInStock(Integer includePurchaseInStock) {
        this.includePurchaseInStock = includePurchaseInStock;
    }

    public List<String> getSkuCidList() {
        return skuCidList;
    }

    public void setSkuCidList(List<String> skuCidList) {
        this.skuCidList = skuCidList;
    }

    public Integer getPurchasePlatFormProductDbNo() {
        return purchasePlatFormProductDbNo;
    }

    public Item4StockQueryParams setPurchasePlatFormProductDbNo(Integer purchasePlatFormProductDbNo) {
        this.purchasePlatFormProductDbNo = purchasePlatFormProductDbNo;
        return this;
    }

    public Integer getPeriodItemDbNo() {
        return periodItemDbNo;
    }

    public Item4StockQueryParams setPeriodItemDbNo(Integer periodItemDbNo) {
        this.periodItemDbNo = periodItemDbNo;
        return this;
    }

    public Integer getOneSixEightEightMapStatus() {
        return oneSixEightEightMapStatus;
    }

    public void setOneSixEightEightMapStatus(Integer oneSixEightEightMapStatus) {
        this.oneSixEightEightMapStatus = oneSixEightEightMapStatus;
    }

    public boolean isOuterIdLike() {
        return outerIdLike;
    }

    public void setOuterIdLike(boolean outerIdLike) {
        this.outerIdLike = outerIdLike;
    }

    public String getItemPlace() {
        return itemPlace;
    }

    public void setItemPlace(String itemPlace) {
        this.itemPlace = itemPlace;
    }

    public String getSkuPlace() {
        return skuPlace;
    }

    public void setSkuPlace(String skuPlace) {
        this.skuPlace = skuPlace;
    }

    public Integer getMinProductNum() {
        return minProductNum;
    }

    public void setMinProductNum(Integer minProductNum) {
        this.minProductNum = minProductNum;
    }

    public Integer getMaxProductNum() {
        return maxProductNum;
    }

    public void setMaxProductNum(Integer maxProductNum) {
        this.maxProductNum = maxProductNum;
    }

    public boolean getQueryNeedItem() {
        if (StringUtils.isNotEmpty(requestSource) && requestSource.equals("stock_uploadRule_queryList") && CollectionUtils.isNotEmpty(brandList)) {
            // 商品品牌列表查询
            return true;
        }

        if (Objects.nonNull(needFilterVirtual) && needFilterVirtual) {
            // 过滤虚拟商品
            return true;
        }

        if (StringUtils.isNotEmpty(itemPlace) || flag == 3 || StringUtils.isNotEmpty(mainOuterId) || CollectionUtils.isNotEmpty(mainOuterIds) || StringUtils.isNotEmpty(title)
                || StringUtils.isNotEmpty(outerId) || CollectionUtils.isNotEmpty(outerIdList) || StringUtils.isNotEmpty(specialOuterId) || CollectionUtils.isNotEmpty(specialOuterIds)
                || CollectionUtils.isNotEmpty(definedJsonParams) || CollectionUtils.isNotEmpty(catIdList)
                || StringUtils.isNotEmpty(tileItemName) || StringUtils.isNotEmpty(tileRemark) || StringUtils.isNotEmpty(tileShortTitle) || CollectionUtils.isNotEmpty(stockLabelList)) {
            // 商品产地查询、虚拟商品查询、主商家编码查询、商品名称查询
            return true;
        }

        if (CollectionUtils.isNotEmpty(cateIdList) && Objects.isNull(userId)) {
            return true;
        }

        if (StringUtils.isNotEmpty(text) && Objects.nonNull(queryType)
                && (queryType.equals("outerId") || queryType.equals("itemName") || queryType.equals("shortTitle") || queryType.equals("remark") || queryType.equals("record"))) {
            return true;
        }

        if ((StringUtils.isNotEmpty(skuOrderColumn) && (skuOrderColumn.equals("item_sku_outer_id") || skuOrderColumn.equals("properties_name") || skuOrderColumn.equals("title") || skuOrderColumn.equals("totalAvailableStockMoneySum")))
                || (StringUtils.isNotEmpty(itemOrderColumn) && itemOrderColumn.equals("title") && "title".equals(skuOrderColumn))) {
            // 排序
            return true;
        }

        return false;
    }

    public Integer getDistributorState() {
        return distributorState;
    }

    public void setDistributorState(Integer distributorState) {
        this.distributorState = distributorState;
    }

    public boolean isQuickQuery() {
        return quickQuery;
    }

    public void setQuickQuery(boolean quickQuery) {
        this.quickQuery = quickQuery;
    }

    public boolean isFillSaleNumByGift() {
        return fillSaleNumByGift;
    }

    public void setFillSaleNumByGift(boolean fillSaleNumByGift) {
        this.fillSaleNumByGift = fillSaleNumByGift;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public Integer getExcludePeriodItem() {
        return excludePeriodItem;
    }

    public void setExcludePeriodItem(Integer excludePeriodItem) {
        this.excludePeriodItem = excludePeriodItem;
    }

    public Integer getItemCount() {
        return itemCount;
    }

    public void setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public List<Long> getSkuCatIdList() {
        return skuCatIdList;
    }

    public void setSkuCatIdList(List<Long> skuCatIdList) {
        this.skuCatIdList = skuCatIdList;
    }

    public List<Long> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<Long> userIdList) {
        this.userIdList = userIdList;
    }

    public Integer getHide() {
        return hide;
    }

    public void setHide(Integer hide) {
        this.hide = hide;
    }


    public List<Long> getLastSysItemIdList() {
        return lastSysItemIdList;
    }

    public void setLastSysItemIdList(List<Long> lastSysItemIdList) {
        this.lastSysItemIdList = lastSysItemIdList;
    }

    public boolean isEnableLastSysItemId() {
        return enableLastSysItemId;
    }

    public void setEnableLastSysItemId(boolean enableLastSysItemId) {
        this.enableLastSysItemId = enableLastSysItemId;
    }

    public Integer getItemSuiteBridgeDbNo() {
        return itemSuiteBridgeDbNo;
    }

    public Item4StockQueryParams setItemSuiteBridgeDbNo(Integer itemSuiteBridgeDbNo) {
        this.itemSuiteBridgeDbNo = itemSuiteBridgeDbNo;
        return this;
    }

    public List<SimpleSuiteBridge> getSimpleSuiteBridges() {
        return simpleSuiteBridges;
    }

    public void setSimpleSuiteBridges(List<SimpleSuiteBridge> simpleSuiteBridges) {
        this.simpleSuiteBridgeSize = simpleSuiteBridges.size();
        this.simpleSuiteBridges = simpleSuiteBridges;
    }

    public Integer getSimpleSuiteBridgeSize() {
        return simpleSuiteBridgeSize;
    }

    public void setSimpleSuiteBridgeSize(Integer simpleSuiteBridgeSize) {
        this.simpleSuiteBridgeSize = simpleSuiteBridgeSize;
    }

    public String getSkuShortTitle() {
        return skuShortTitle;
    }

    public void setSkuShortTitle(String skuShortTitle) {
        this.skuShortTitle = skuShortTitle;
    }

    public boolean isUseThreadPool() {
        return useThreadPool;
    }

    public void setUseThreadPool(boolean useThreadPool) {
        this.useThreadPool = useThreadPool;
    }

    public Integer getFilterEmptyBrandByPrivilege() {
        return filterEmptyBrandByPrivilege;
    }

    public void setFilterEmptyBrandByPrivilege(Integer filterEmptyBrandByPrivilege) {
        this.filterEmptyBrandByPrivilege = filterEmptyBrandByPrivilege;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Integer getOrderVal() {
        return orderVal;
    }

    public void setOrderVal(Integer orderVal) {
        this.orderVal = orderVal;
    }
}

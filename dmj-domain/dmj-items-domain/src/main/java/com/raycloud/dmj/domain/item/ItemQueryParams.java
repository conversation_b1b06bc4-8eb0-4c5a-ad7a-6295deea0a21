package com.raycloud.dmj.domain.item;

import com.google.common.collect.Sets;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.params.SortField;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;

/**
 * 商品查询参数
 *
 * <AUTHOR>
 */
public class ItemQueryParams implements Serializable {

    private static final long serialVersionUID = -8466721904441261153L;

    private static final Set<SortField> ITEM_SUPPORT_SORT_FIELDS = Sets.newHashSet(SortField.CREATED, SortField.OUTER_ID, SortField.PURCHASE_PRICE, SortField.SELLING_PRICE, SortField.SUPPLIER);

    private static final Set<SortField> SKU_SUPPORT_SORT_FIELDS = Sets.newHashSet(SortField.CREATED, SortField.OUTER_ID, SortField.SKU_OUTER_ID, SortField.PROPERTIES_NAME, SortField.ALL_PROPERTIES_NAME, SortField.PURCHASE_PRICE, SortField.SELLING_PRICE, SortField.SUPPLIER);


    //精确搜索
    public static final Integer SEARCH_TYPE_ACCURATE = 1;

    //模糊搜索
    public static final Integer SEARCH_TYPE_LIKE = 0;

    //默认临保天数 30天
    public static final Integer NEAR_DATE = 30;

    //查询所有店铺 前端框架原因，传null或者-1 都代表查所有店铺的数据
    public static final String ALL_userId = "-1";

    private List<Long> sysItemIdList;

    private List<Long> sysSkuIdList;

    private List<Long> warnSysItemIdList;

    private List<Long> warnSysSkuIdList;

    private Long companyId;

    /**
     *  分销商id
     */
    private Long distributorCompanyId;

    /**
     * userId不为空，触发userType字段查询
     * itemSalePrice 分销价
     */
    private String userType;

    private Long userId;

    private String userIds;

    private List<Long> userIdList;

    private Long taobaoId;

    private String numIid;

    private String numIids;

    //平台skuid
    private String skuId;

    private String skuIds;

    // 分类id  未分类：-1
    private Long cId;
    // 所有分类id
    private List<Long> cIds;

    //规格分类id
    private List<Long> skuCids;

    private List<Long> brandIds;

    // 供应商分类
    private String supplierCategoryIds;

    private Long otherInId;

    /**
     * 0-普通(不含虚拟商品，含加工) 1-套件(含sku套件和纯套件) 2-全部 3-虚拟 4-普通和套件(包含0,1)
     * 5-纯套件 6-加工商品（sku加工和纯加工）7-普通商品（不含虚拟商品,不含加工） 8-全部（不含包材）
     * 9-组合装(sku组合装和纯组合装)
     * 10-普通商品(包含虚拟商品) 11-普通商品(双0，不包括组合商品和加工商品)
     */
    private String flag;

    private List<Long> sysItemIds;

    private String sysItemIdStrs;

    private String sysSkuIdStrs;

    private Long excludeSysItemId;

    /**
     * 纯商品id匹配成功或者商品下面的sku都已经匹配成功
     */
    private List<Long> excludedSysItemIds;

    private List<Long> sysSkuIds;

    private List<Long> excludedSysSkuIds;

    //已经绑定的skuId
    private List<Long> bindedSysSkuIds;

    //0-不包含，1-包含
    private Integer include;

    //平台商品numIid集合
    private List<String> numIidList;
    //平台商品skuId 集合
    private List<String> skuIdList;

    private Long sysItemId;

    private Long sysSkuId;

    private Long filterSysItemId; // 过滤掉加工商品自己

    /**
     * 库存状态
     * <pre>
     *  1：正常 2：缺货 3：无货
     * </pre>
     */
    private Integer stockStatus;

    /**
     * 库存状态，前端直接传来的字符串，用逗号分割
     */
    private String stockStatuses;

    /**
     *
     * 库存状态组合类型 0～15
     * 使用2的N次幂的N表达勾选状态，1 1 1 1，每一位代表不同状态（状态如下），如果勾选，将对应的位数置为1，否则置为0
     * eg：勾选正常和缺货，则为 0 0 1 1，结果为：3
     * 状态类型：
     *  1（正常） 2^0
     *  2（缺货） 2^1
     *  3（无货） 2^2
     *  4（超卖） 2^3
     *  逻辑:  1   1   1   1
     *
     *
     */
//    private Integer stockStatusGroup;

    /**
     * 库存状态多选检索使用
     */
    private List<Integer> stockStatusList;

    /**
     * 警戒状态多选检索使用
     */
    private List<Integer> warnStatusList;


    private Integer onSale;

    /**
     * 是否在结果集中返回商品库存，如果为1，那么必须要设置仓库
     */
    private Integer hasAvailableInStock;

    List<Long> warehouseIds;

    private Long warehouseId;

    /**
     * 0：查询结果中不包含sku，1：查询结果中包括sku，并组织到item中
     */
    private Integer hasSkus;

    /**
     * 是否设置 区分纯商品和sku商品
     * 1-sku商品  0-纯商品
     */
    private Integer pureSpecialField;

    /**
     * 特殊查询字段
     * <pre>
     *     库存数量(actualStock)
     *     可售库存警戒值(alarmStock)
     *     宝贝简称(shortTitle)
     *     成本价(referencePurchasePrice)
     *     重量(weight)
     *     市场(market)
     *     厂家(factory)
     *     货位(goodsAllocation)
     *     条形码(barcode)
     *     中文报关名(declareNameZh)
     *     英文报关名(declareNameEn)
     *     海关编码(hsCode)
     *     申报金额(declareAmount)
     *     申报重量(declareWeight)
     *     是否开启批次(hasBatch)
     *     保质期(consumptionCycle)
     *     供应商(supplier)
     * </pre>
     */
    private String specialField;

    /**
     * 特殊字段的状态
     * <pre>
     *     默认为全部
     *     0表示全部
     *     1表示已设置
     *     2表示未设置
     * </pre>
     */
    private Integer specialFieldStatus;

    /**
     * 针对 商品和规格有相同字段筛选，根据specialField获取sku的查询字段
     */
    private String skuSpecialField;

    /**
     * 过滤条件
     * <pre>
     *     宝贝名称
     *     宝贝简称
     *     单品规格（SKU属性）
     *     商家编码
     *     市场
     *     厂家和货位
     *     商品/规格备注
     *     规格别名
     * </pre>
     */
    private String text;

    private String skuText;

    private String text2;
    private String text3;

    /**
     * 分页
     */
    private Page page;

    /**
     * 排序
     */
    private Sort sort;

    /**
     * dmjitem是否需要排序 0-不排序，1-排序，默认排序,按照sys_item_id倒序
     */
    private Integer isSort = 1;

    /**
     * 按照sku的 XXS、XS、S、M、L、XL、2XL、3XL、4XL、5XL 进行排序
     * 1-按照从小到大排序，2-从大到小排序。null-不排序
     */
    private Integer skuSizeSort;

    /**
     * 商品编码
     */
    private String outerId;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 规格属性
     */
    private String propertiesName;

    /**
     * 商品是否启用 1-启用，-1 停用
     */
    private Integer activeStatus;


    /**
     * 校验商品是否停用
     */
    private Boolean checkActiveStatus;

    /**
     * 商品停用状态查询 0:全部 1:启用 2:停用
     */
    private Integer itemActiveStatus;

    /**
     * 设置预警过滤
     */
    private Integer alarmSetting;

    /**
     * 精确接口（0：模糊搜索 1：精确搜索）
     */
    private Integer searchType;
    /**
     * 是否精确搜索
     *
     * @return
     */
    private Integer isAccurate;

    /**
     * SKU是否精确搜索
     */
    private Integer isSkuAccurate;

    /**
     * 是否需要供应商信息
     */
    private Boolean needSupplier;

    /**
     * 是否需要销量信息
     */
    private Boolean needSaleReport;

    /**
     * 是否已设置供应商信息
     */
    private Integer hasSupplier;
    /**
     * 供应商权限  超级管理员权限为0
     */
    private String supplierIdPrivilege;
    /**
     * 供应商权限  转为集合形式，方便sql查询
     */
    private List<Long> supplierIdListPrivilege;
    /**
     * 有关联系供应商的商品id
     */
    private List<Long> sysItemIdListSupplier;
    /**
     * 有关联系供应商的skuid
     */
    private List<Long> sysSkuIdListSupplier;

    private Integer type;

    /**
     * 商品是否需要填充客户信息
     * 这里在批量更新销售价的时候有用到 不填充客户信息
     */
    private Boolean needFillCustomer = true;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 根据内容搜索：
     * title 商品名称
     * outerId 商家编码
     * shortTitle 商品简称
     * remark 商品备注
     * skuAlias 规格别名
     * skuRemark 规格备注
     * supplier 供应商名称
     * suiteOuterId 套件单品编码
     * suiteName 套件单品名称
     * propertiesName 规格属性
     * supplierOuterId 供应商商家编码
     * record 规格记录
     * created 商品创建时间
     * supplierCategoryIds 供应商分类
     */
    private String content;

    private String skuContent;

    private String content3;
    private String content2;
    /**
     * 根据sysIds查询商品
     */
    private String itemIds;
    /**
     * 标记请求信息的种类
     * "item"  商品导出
     * "supplier" 商品供应商导出
     * "avgPrice" 商品移动加权导出
     */
    private String mark;

    /**
     * 是否设置分销价,查询条件 1-是 0-否
     */
    private Integer hasSalePrice;

    /**
     * 是否需要组装分销价信息 0-不需要 1-需要
     */
    private Integer needSalePrice;

    private Long salePriceUserId;


    // 绑定商品 是否显示已经绑定的商品
    // 0-不过滤，1-过滤
    private Integer filterBind;

    /**
     * 是否开启批次设置  0-否 1-是
     */
    private Integer hasBatch;

    /**
     * 是否开启生产日期管理 0-否 1-是
     */
    private Integer hasProduct;

    /**
     * 临保商品是否允许销售 0- 关闭 1- 开启
     */
    private Integer allowSaleExpiring;

    /**
     * 批次出库规则  1-先进先出 2-先进后出
     */
    private Integer batchRule;

    /**
     * 临保天数
     */
    private Integer nearDate;


    /**
     * 是否需要客户信息 no 不需要 yes 需要客户信息
     */
    private String needCustomerFilter;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户id列表
     */
    private List<Long> customerIdList;

    private Integer hide;

    /**
     * 主商家编码，此时不去查询sku编码，仅查询商品编码
     */
    private String mainOuterId;


    private List<Long> lastSysItemIds;


    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getDistributorCompanyId() {
        return distributorCompanyId;
    }

    public void setDistributorCompanyId(Long distributorCompanyId) {
        this.distributorCompanyId = distributorCompanyId;
    }

    /**
     * 根据条件查询的商品，会显示下面所有的sku，这个参数用来过滤sku
     * 0-所有，1-过滤，只显示查询的sku。
     */
    private Integer filterSku;

    /**
     * 换绑时候，判断系统商品是否被当前平台商品绑定过
     * 0-未绑定。1-绑定
     */
    private Integer matchStatus;
    /**
     * 关键字匹配的字段:itemName 商品名称 outerId 商家编码
     * 不知道为什么会有这个字段，同 content
     */
    private String queryType;
    /**
     * 不要给默认值！！
     * 是否是虚拟商品，1：是虚拟商品，0:不是虚拟商品
     */
    private Integer isVirtual;


    //库存数量到一定值，触发上架操作
    private Integer putawayNum;

    // 多个店铺id,name
    private List<Shop> userList;

    //店铺taobaoIds
    private List<Long> taobaoIdList;

    /**
     * 是否是含有sku的商品，是：1，否：0
     */
    private Integer isSkuItem;

    /**
     * 平台商品标识 0-正常商品，1-套件商品
     */
    private Integer itemSign;
    /**
     * 平台商品名称
     */
    private String platTitle;
    /**
     * 平台规格名称
     */
    private String platSkuName;

    /**
     * 是否需要查询tbsku
     */
    private Boolean needTbSku;

    /**
     * 是否需要查询推荐货位
     */
    Boolean isGoodSection;

    public Boolean getGoodSection() {
        return isGoodSection;
    }

    public void setGoodSection(Boolean goodSection) {
        isGoodSection = goodSection;
    }

    /**
     * 批量填充编码方式：1-重复铺货填充编码 2-单链接重复规格填充编码
     */
    private Integer fillOuterIdType;

    /**
     * 是否高亮显示搜索结果
     */
    private Boolean highlight;

    /**
     * 是否参与上传 0：否 1：是
     */
    private Integer isUpload;
    private Integer nameType; // 排序字段 1:商品创建时间，2 商品名称

    /**
     * 自动计算类型
     */
    private String calculateTypes;
    /**
     * 套件自动计算后 1-覆盖 0-不覆盖
     */
    private Integer suiteCover;

    private Integer syncType;

    private Integer useOnWay;

    /**
     * 单件拣选积分最大与最小范围
     */
    private Double oneIntegralMin;
    private Double oneIntegralMax;
    /**
     * 多件拣选积分最大与最小范围
     */
    private Double moreIntegralMin;
    private Double moreIntegralMax;
    /**
     * 单件包装积分最大与最小范围
     */
    private Double onePackageIntegralMin;
    private Double onePackageIntegralMax;
    /**
     * 多件包装积分最大与最小范围
     */
    private Double morePackageIntegralMin;
    private Double morePackageIntegralMax;
    /**
     * 单件拣货积分最大与最小范围
     */
    private Double oneInspectionIntegralMin;
    private Double oneInspectionIntegralMax;
    /**
     * 多件拣货积分最大与最小范围
     */
    private Double moreInspectionIntegralMin;
    private Double moreInspectionIntegralMax;

    private Double oneWeightIntegralMin;
    private Double oneWeightIntegralMax;

    private Double moreWeightIntegralMin;
    private Double moreWeightIntegralMax;

    private Double receiveGoodsIntegralMin;
    private Double receiveGoodsIntegralMax;

    private Double qualityCheckIntegralMin;
    private Double qualityCheckIntegralMax;

    private Double  oneSendGoodsIntegralMin;
    private Double  oneSendGoodsIntegralMax;

    private Double  moreSendGoodsIntegralMin;

    private Double  moreSendGoodsIntegralMax;

    private Double  processIntegralMin;
    private Double  processIntegralMax;
    private Double  baggingIntegralMin;
    private Double  baggingIntegralMax;



    /**
     * 批量填充   (0拣选积分   1包装积分   2验货积分)
     */
    private String selectIntegralType;

    /**
     * 加载更多翻页
     */
    private Integer loadPageNo;
    /**
     * 加载等多商品id
     */
    private Long loadSysItemId;

    /**
     * 成本价筛选 2-全部、0-成本为0、1-成本大于0
     */
    private Long purchasePriceScope;

    /**
     * sku的商家编码
     */
    private String skuOuterId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 客户分销价模糊查询条件: 客户编码 or 客户名称 or 客户昵称
     */
    private String cmCodes;

    /**
     * 客户分销价模糊查询条件: 商家编码 or 商品名称 or 商品备注
     */
    private String itemCodes;

    /**
     * 客户分销价模糊查询条件: 规格名称 or 规格别名 or 规格备注
     */
    private String skuCodes;


    /**
     * 查询商品名称
     */
    private String tileItemName;

    /**
     * 查询商家编码
     */
    private String tileOuterId;
    private String tileSkuOuterId;
    /**
     * 查询商家编码（item和sku）集合
     */
    private List<String> tileOuterIdList;
    private List<String> tileSkuOuterIdList;


    /**
     * 匹配主商家编码和sku商家编码
     */
    private String tileAllOuterId;
    private List<String> tileAllOuterIdList;
    private String tileSupplierItemOuterId;

    /**
     * 查询商品简称
     */
    private String tileShortTitle;

    /**
     * 查询备注
     */
    private String tileRemark;

    /**
     * 查询属性名称
     */
    private String tilePropertiesName;

    /**
     * 查询规格别名
     */
    private String tileSkuAlias;

    /**
     * 查询规格备注
     */
    private String tileSkuRemark;

    /**
     * 查询供应商
     */
    private String tileSupplierId;

    /**
     * 查询品牌
     */
    private String tileBrand;

    /**
     * 查询供应商的结果列表集合
     */
    private List<Long> tileSupplierIdList;

    private List<String> tileBrandList;

    private List<String> skuBrandList;

    private Integer minAvailableStock;

    private Integer maxAvailableStock;

    private Integer minDefectiveStock;

    private Integer maxDefectiveStock;

    private List<Long> stockWarehouseIds;

    /**
     * 是否自动上传
     */
    private Boolean isAuto;

    /**
     * 根据时间搜索时的条件
     * @return
     */
    private Long startTime;

    private Long endTime;

    private Date startDate;

    private Date endDate;
    /**
     * 根据时间搜索时的条件
     * 起始的修改时间
     */
    private Date startModified;

    /**
     * 结束的修改时间
     */
    private Date endModified;

    private String multiCode;

    private List<String> multiCodeList;

    private List<Long> tileSysItemIds;

    private List<Long> tileSysSkuIds;

    /**
     * 是否快速查询
     * 这里是为了优化limit分页，使用id分页的做法
     * 所以如果使用id分页的话，那么排序只使用id，SQL当中不考虑Sort对象
     * 使用上配合下面的lastId属性
     *
     * 还有一点，在使用上需要注意
     * 由于根据id分页，那么查询的sql返回的id应该是唯一且有序
     *
     */
    private boolean fastQuery = false;

    /**
     * 上一次记录的ID
     */
    private Long lastId = 0L;

    /**
     * 平台商家编码集合
     */
    private List<String> platOuterIdList;

    /**
     * 查询来源
     */
    private String querySource;

    private Integer purchaseSourceType;

    /**
     * 是否需要排序（默认需要）
     */
    private Boolean needOrder;

    /**
     * 采购那边调用dubbo接口   是否需要排序 默认为true
     */
    private Boolean purchaseNeedOrder;

    /**
     * 0 范围查询 1.是否设置
     */
    private Integer queryTableType;

    /**
     * 标签查询类型
     * 0-包含任意一个选中标签 1-包含所有选中标签 2-排除包含任意一个选中标签 3-排除包含所有选中标签
     */
    private Integer tagQueryType;

    /**
     * 是否包含未关联标签的商品
     */
    private Boolean includeNoTag;

    private String itemTagIds;

    private List<Long> itemTagIdList;

    /**
     * 套件单品编码集合
     */
    private List<String> suiteOuterIds;

    /**
     * 套件单品编码集合数量
     */
    private Integer suiteOuterIdCount;

    private Long pageId;

    /**
     * 主表范围查询
     * 0 不需要 1 需要
     */
    private Integer mainScopeQuery;
    /**
     * 扩展表范围查询
     * 0 不需要 1 需要
     */
    private Integer extendsScopeQuery;

    /**
     * 导出excel格式
     */
    private Integer excelTypeCode;

    /**
     * 赠品标记
     * 1 赠品
     * 0 非赠品
     */
    private Integer makeGift;

    private String catIds;

    private List<String> catIdList;

    /**
     * 请求来源
     */
    private String requestSource;

    public String getRequestSource() {
        return requestSource;
    }

    public void setRequestSource(String requestSource) {
        this.requestSource = requestSource;
    }

    /**
     * 0 查询品牌为空的商品
     * 1 查询品牌不为空的商品
     */
    private Integer queryEmptyBrand;

    /**
     * 勾选的商品
     */
    private String selectedItems;

    /**
     * 商家编码模糊搜索Or精确搜索 0-模糊搜索 1-精确搜索 （参照新弹窗传参方式）
     */
    private Integer outIdType;

    private Integer skuOutIdType;

    private String skuOrderColumn;

    private String itemOrderColumn;

    private Boolean orderDesc;

    private Boolean itemPrintOrder;

    /**
     * 是否隐藏type_tag为5的商品
     */
    private Integer hideFlag;

    private boolean quickQuery = false;

    /**
     * 警戒状态包括采购在途数
     */
    private Integer includePurchase;

    /**
     * 警戒状态是否包含销退在途数
     */
    private Integer includePurchaseInStock;

    private boolean needMockCount = false;


    private Integer cover;
    /**
     * 0 下架 1 上架
     */
    private Integer filterOnsale;

    private Boolean enableLastSysItemId =  false;

    public Integer getIncludePurchase() {
        return includePurchase;
    }

    public void setIncludePurchase(Integer includePurchase) {
        this.includePurchase = includePurchase;
    }

    public Integer getIncludePurchaseInStock() {
        return includePurchaseInStock;
    }

    public void setIncludePurchaseInStock(Integer includePurchaseInStock) {
        this.includePurchaseInStock = includePurchaseInStock;
    }

    private boolean filterByDataPrivilege;

    private Integer filterEmptyBrandByPrivilege;

    private String apiName;

    public Integer getSuiteOuterIdCount() {
        return suiteOuterIdCount;
    }

    public void setSuiteOuterIdCount(Integer suiteOuterIdCount) {
        this.suiteOuterIdCount = suiteOuterIdCount;
    }

    public List<String> getSuiteOuterIds() {
        return suiteOuterIds;
    }

    public void setSuiteOuterIds(List<String> suiteOuterIds) {
        this.suiteOuterIds = suiteOuterIds;
    }

    public Integer getTagQueryType() {
        return tagQueryType;
    }

    public void setTagQueryType(Integer tagQueryType) {
        this.tagQueryType = tagQueryType;
    }

    public Boolean getIncludeNoTag() {
        return includeNoTag;
    }

    public void setIncludeNoTag(Boolean includeNoTag) {
        this.includeNoTag = includeNoTag;
    }

    public String getItemTagIds() {
        return itemTagIds;
    }

    public void setItemTagIds(String itemTagIds) {
        this.itemTagIds = itemTagIds;
    }

    public List<Long> getItemTagIdList() {
        return itemTagIdList;
    }

    public void setItemTagIdList(List<Long> itemTagIdList) {
        this.itemTagIdList = itemTagIdList;
    }

    /**
     * 是否忽略规格状态查询
     */
    private boolean ignoreActiveStatus = false;

    public Integer getQueryTableType() {
        return queryTableType;
    }

    public void setQueryTableType(Integer queryTableType) {
        this.queryTableType = queryTableType;
    }

    private Integer goodsStatus;

    public Integer getGoodsStatus() {
        return goodsStatus;
    }

    public void setGoodsStatus(Integer goodsStatus) {
        this.goodsStatus = goodsStatus;
    }

    public String getTileBrand() {
        return tileBrand;
    }

    public void setTileBrand(String tileBrand) {
        if (StringUtils.isNotEmpty(tileBrand)) {
            String[] tileSupplierIdArr = tileBrand.split(",");

            List<String> supplierIdList = new ArrayList<>();
            for (String supplierId : tileSupplierIdArr) {
                if (StringUtils.isEmpty(supplierId)) {
                    continue;
                }

                supplierIdList.add(supplierId);
            }

            if (CollectionUtils.isNotEmpty(supplierIdList)) {
                this.setTileBrandList(supplierIdList);
            }
        }

        this.tileBrand = tileBrand;
    }

    public List<String> getTileBrandList() {
        return tileBrandList;
    }

    public void setTileBrandList(List<String> tileBrandList) {
        this.tileBrandList = tileBrandList;
    }

    public List<String> getSkuBrandList() {
        return skuBrandList;
    }

    public void setSkuBrandList(List<String> skuBrandList) {
        this.skuBrandList = skuBrandList;
    }

    public List<String> getTileOuterIdList() {
        return tileOuterIdList;
    }

    public void setTileOuterIdList(List<String> tileOuterIdList) {
        this.tileOuterIdList = tileOuterIdList;
    }

    public List<String> getTileSkuOuterIdList() {
        return tileSkuOuterIdList;
    }

    public void setTileSkuOuterIdList(List<String> tileSkuOuterIdList) {
        this.tileSkuOuterIdList = tileSkuOuterIdList;
    }

    public String getTileSkuOuterId() {
        return tileSkuOuterId;
    }

    public void setTileSkuOuterId(String tileSkuOuterId) {
        this.tileSkuOuterId = tileSkuOuterId;
    }

    public Boolean getAuto() {
        return isAuto;
    }

    public void setAuto(Boolean auto) {
        isAuto = auto;
    }

    /**
     * 是否赠品
     */
    private Boolean gift;


    /**
     * 是否需要过滤供应商权限
     */
    private boolean filterSupplierAuth;

    /**
     * 是否包含未绑定供应商权限
     */
    private Boolean includeNoSupplier;

    /**
     * 是否需要填充总数
     */
    private boolean needTotal;

    /**
     * 是否返回总数
     */
    private boolean onlyTotal;

    private Integer picStatus;

    private Integer inShop=1;
    /**
     * sku规格未设置条件
     */
    private Integer skuSellerStatus;
    /**
     * 1688 映射 0 已映射 1 未映射
     */
    private Integer oneSixEightEightMapStatus;

    /**
     * 货主，多个以逗号隔开
     */
    private String shipper;

    /**
     * 货主id
     */
    private String shipperId;
    /**
     * 货主
     */
    private List<String> shipperList;

    /**
     * 货主id
     */
    private List<String> shipperIdList;

    /**
     * 货主编码
     */
    private List<String> shipperItemCodeList;

    /**
     * 商品级别的商家编码
     */
    private List<String> inItemOuterIdList;

    /**
     * 0 自建商品
     * 1 货主商品
     */
    private Integer shipperItemFlag;

    /**
     * 规格简称
     */
    private String skuShortTitle;

    public List<String> getShipperList() {
        return shipperList;
    }

    public void setShipperList(List<String> shipperList) {
        this.shipperList = shipperList;
    }

    public List<String> getShipperItemCodeList() {
        return shipperItemCodeList;
    }

    public void setShipperItemCodeList(List<String> shipperItemCodeList) {
        this.shipperItemCodeList = shipperItemCodeList;
    }

    public Integer getSkuSellerStatus() {
        return skuSellerStatus;
    }

    public void setSkuSellerStatus(Integer skuSellerStatus) {
        this.skuSellerStatus = skuSellerStatus;
    }

    /**
     * 订单类型 1:其他入库单 2:其他出库单
     */
    private Integer orderType;

    /**
     * 品牌名称，多个使用英文逗号隔开
     */
    private String brandNames;

    /**
     * 品牌名称集合
     */
    private List<String> brandNameList;

    /**
     * 分类ID集合
     */
    private String cIdStrs;

    /**
     * 商品条形码模糊搜索or精确搜索 0-模糊搜索 1-精确搜索，默认精确搜索
     */
    private Integer multiCodeType = 1;

    /**
     * 是否需要商品配置排序
     */
    private Boolean itemConfigSort = false;


    /**
     * 套件子商品比例模型
     */
    private List<SimpleSuiteBridge> simpleSuiteBridges = new ArrayList<>();

    /**
     * 套件子商品比例模型长度
     */
    private Integer simpleSuiteBridgeSize;


    public Integer getInShop() {
        return inShop;
    }

    public void setInShop(Integer inShop) {
        this.inShop = inShop;
    }

    public boolean isFilterSupplierAuth() {
        return filterSupplierAuth;
    }

    public void setFilterSupplierAuth(boolean filterSupplierAuth) {
        this.filterSupplierAuth = filterSupplierAuth;
    }

    public Boolean getIncludeNoSupplier() {
        return includeNoSupplier;
    }

    public void setIncludeNoSupplier(Boolean includeNoSupplier) {
        this.includeNoSupplier = includeNoSupplier;
    }

    public Boolean getGift() {
        return gift;
    }

    public void setGift(Boolean gift) {
        this.gift = gift;
    }

    public String getItemIds() {
        return itemIds;
    }

    public void setItemIds(String itemIds) {
        this.itemIds = itemIds;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getIsAccurate() {
        return isAccurate;
    }

    public void setIsAccurate(Integer isAccurate) {
        this.isAccurate = isAccurate;
    }

    public Integer getIsSkuAccurate() {
        return isSkuAccurate;
    }

    public void setIsSkuAccurate(Integer isSkuAccurate) {
        this.isSkuAccurate = isSkuAccurate;
    }

    public Integer getHasSupplier() {
        return hasSupplier;
    }

    public void setHasSupplier(Integer hasSupplier) {
        this.hasSupplier = hasSupplier;
    }

    public Integer getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(Integer isVirtual) {
        this.isVirtual = isVirtual;
    }

    public Boolean getNeedSupplier() {
        return needSupplier;
    }

    public void setNeedSupplier(Boolean needSupplier) {
        this.needSupplier = needSupplier;
    }

    public Boolean getNeedSaleReport() {
        return needSaleReport;
    }

    public void setNeedSaleReport(Boolean needSaleReport) {
        this.needSaleReport = needSaleReport;
    }

    public Long getcId() {
        return cId;
    }

    public void setcId(Long cId) {
        this.cId = cId;
    }

    public List<Long> getExcludedSysItemIds() {
        return excludedSysItemIds;
    }

    public void setExcludedSysItemIds(List<Long> excludedSysItemIds) {
        this.excludedSysItemIds = excludedSysItemIds;
    }

    public List<Long> getExcludedSysSkuIds() {
        return excludedSysSkuIds;
    }

    public void setExcludedSysSkuIds(List<Long> excludedSysSkuIds) {
        this.excludedSysSkuIds = excludedSysSkuIds;
    }

    public Integer getOnSale() {
        return onSale;
    }

    public void setOnSale(Integer onSale) {
        this.onSale = onSale;
    }

    public Long getSysItemId() {
        return sysItemId;
    }

    public String getQueryType() {
        return queryType;
    }

    public ItemQueryParams setQueryType(String queryType) {
        this.queryType = queryType;
        return this;
    }

    public ItemQueryParams setSysItemId(Long sysItemId) {
        this.sysItemId = sysItemId;
        return this;
    }

    public Long getSysSkuId() {
        return sysSkuId;
    }

    public void setSysSkuId(Long sysSkuId) {
        this.sysSkuId = sysSkuId;
    }

    public Integer getSearchType() {
        return searchType;
    }

    public void setSearchType(Integer searchType) {
        this.searchType = searchType;
    }

    public String getFlag() {
        return flag;
    }

    public ItemQueryParams setFlag(String flag) {
        this.flag = flag;
        return this;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public List<Long> getSysItemIds() {
        return sysItemIds;
    }

    public String getNumIid() {
        return numIid;
    }

    public ItemQueryParams setNumIid(String numIid) {
        this.numIid = numIid;
        return this;
    }

    public Integer getHasAvailableInStock() {
        return hasAvailableInStock;
    }

    public ItemQueryParams setHasAvailableInStock(Integer hasAvailableInStock) {
        this.hasAvailableInStock = hasAvailableInStock;
        return this;
    }


    public Integer getHasSkus() {
        return this.hasSkus;
    }

    public ItemQueryParams setHasSkus(Integer hasSkus) {
        this.hasSkus = hasSkus;
        return this;
    }

    public ItemQueryParams setSysItemIds(List<Long> sysItemIds) {
        this.sysItemIds = sysItemIds;
        return this;
    }

    public List<Long> getcIds() {
        return cIds;
    }

    public ItemQueryParams setcIds(List<Long> cIds) {
        this.cIds = cIds;
        return this;
    }

    public List<Long> getSkuCids() {
        return skuCids;
    }

    public ItemQueryParams setSkuCids(List<Long> skuCids) {
        this.skuCids = skuCids;
        return this;
    }

    public Long getOtherInId() {
        return otherInId;
    }

    public void setOtherInId(Long otherInId) {
        this.otherInId = otherInId;
    }

    public List<Long> getBrandIds() {
        return brandIds;
    }

    public void setBrandIds(List<Long> brandIds) {
        this.brandIds = brandIds;
    }

    public String getSupplierCategoryIds() {
        return supplierCategoryIds;
    }

    public void setSupplierCategoryIds(String supplierCategoryIds) {
        this.supplierCategoryIds = supplierCategoryIds;
    }

    public Long getUserId() {
        return userId;
    }


    public void setUserId(Long userId) {
        this.userId = userId;
    }


    public Sort getSort() {
        return sort;
    }

    public void setSort(Sort sort) {
        this.sort = sort;
    }

    public Integer getIsSort() {
        return isSort;
    }

    public void setIsSort(Integer isSort) {
        this.isSort = isSort;
    }

    public String getOuterId() {
        return outerId;
    }

    public ItemQueryParams setOuterId(String outerId) {
        this.outerId = outerId;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPropertiesName() {
        return propertiesName;
    }

    public void setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
    }

    public Integer getStockStatus() {
        return stockStatus;
    }

    public ItemQueryParams setStockStatus(Integer stockStatus) {
        this.stockStatus = stockStatus;
        return this;
    }

    public String getStockStatuses() {
        return stockStatuses;
    }

    public void setStockStatuses(String stockStatuses) {
        stockStatusList = ArrayUtils.toIntegerListPosition(stockStatuses);
        this.stockStatuses = stockStatuses;
    }

    public String getSpecialField() {
        return specialField;
    }

    public ItemQueryParams setSpecialField(String specialField) {
        this.specialField = StockConstants.FIELD_MAP.get(specialField);
        this.pureSpecialField = StockConstants.getPureSpecialField(this.specialField);
        this.skuSpecialField = StockConstants.SKU_FIELD_MAP.get(specialField);
        return this;
    }

    public void setSpecialFieldOri(String specialField) {
        this.specialField = specialField;
    }

    public Integer getSpecialFieldStatus() {
        return specialFieldStatus;
    }

    public ItemQueryParams setSpecialFieldStatus(Integer specialFieldStatus) {
        this.specialFieldStatus = specialFieldStatus;
        return this;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Page getPage() {
        return page;
    }

    public ItemQueryParams setPage(Page page) {
        this.page = page;
        return this;
    }

    public List<Long> getWarehouseIds() {
        return warehouseIds;
    }

    public void setWarehouseIds(List<Long> warehouseIds) {
        this.warehouseIds = warehouseIds;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public ItemQueryParams setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
        return this;
    }


    public ItemQueryParams setActiveStatus(Integer activeStatus) {
        this.activeStatus = activeStatus;
        return this;
    }

    public Integer getActiveStatus() {
        return activeStatus == null ? Integer.valueOf(CommonConstants.JUDGE_YES) : activeStatus;
    }

    public ItemQueryParams setAlarmSetting(Integer alarmSetting) {
        this.alarmSetting = alarmSetting;
        return this;
    }

    public Integer getAlarmSetting() {
        return alarmSetting;
    }

    public List<Long> getSysSkuIds() {
        return sysSkuIds;
    }

    public ItemQueryParams setSysSkuIds(List<Long> sysSkuIds) {
        this.sysSkuIds = sysSkuIds;
        return this;
    }

    public Long getCId() {
        return cId;
    }

    public void setCId(Long cId) {
        this.cId = cId;
    }

    private List<ItemSuiteBridge> itemModels;

    public List<ItemSuiteBridge> getItemModels() {
        return itemModels;
    }

    public void setItemModels(List<ItemSuiteBridge> itemModels) {
        this.itemModels = itemModels;
    }

    public Integer getNameType() {
        return nameType;
    }

    public void setNameType(Integer nameType) {
        this.nameType = nameType;
    }

    public List<Long> getBindedSysSkuIds() {
        return bindedSysSkuIds;
    }

    public void setBindedSysSkuIds(List<Long> bindedSysSkuIds) {
        this.bindedSysSkuIds = bindedSysSkuIds;
    }

    public Integer getFilterBind() {
        return filterBind;
    }

    public void setFilterBind(Integer filterBind) {
        this.filterBind = filterBind;
    }

    public Integer getHasBatch() {
        return hasBatch;
    }

    public void setHasBatch(Integer hasBatch) {
        this.hasBatch = hasBatch;
    }

    public Integer getHasProduct() {
        return hasProduct;
    }

    public void setHasProduct(Integer hasProduct) {
        this.hasProduct = hasProduct;
    }

    public Integer getAllowSaleExpiring() {
        return allowSaleExpiring;
    }

    public void setAllowSaleExpiring(Integer allowSaleExpiring) {
        this.allowSaleExpiring = allowSaleExpiring;
    }

    public Integer getFilterSku() {
        return filterSku;
    }

    public void setFilterSku(Integer filterSku) {
        this.filterSku = filterSku;
    }

    public String getSkuSpecialField() {
        return skuSpecialField;
    }

    public void setSkuSpecialField(String skuSpecialField) {
        this.skuSpecialField = skuSpecialField;
    }

    public Integer getNearDate() {
        return nearDate;
    }

    public void setNearDate(Integer nearDate) {
        this.nearDate = nearDate;
    }

    public Integer getBatchRule() {
        return batchRule;
    }

    public void setBatchRule(Integer batchRule) {
        this.batchRule = batchRule;
    }

    public Integer getMatchStatus() {
        return matchStatus;
    }

    public void setMatchStatus(Integer matchStatus) {
        this.matchStatus = matchStatus;
    }

    public Integer getPutawayNum() {
        return putawayNum;
    }

    public void setPutawayNum(Integer putawayNum) {
        this.putawayNum = putawayNum;
    }

    public List<Shop> getUserList() {
        return userList;
    }

    public void setUserList(List<Shop> userList) {
        this.userList = userList;
    }

    public List<Long> getTaobaoIdList() {
        return taobaoIdList;
    }

    public void setTaobaoIdList(List<Long> taobaoIdList) {
        this.taobaoIdList = taobaoIdList;
    }

    public Integer getIsSkuItem() {
        return isSkuItem;
    }

    public void setIsSkuItem(Integer isSkuItem) {
        this.isSkuItem = isSkuItem;
    }

    public Integer getItemSign() {
        return itemSign;
    }

    public void setItemSign(Integer itemSign) {
        this.itemSign = itemSign;
    }

    public String getPlatTitle() {
        return platTitle;
    }

    public void setPlatTitle(String platTitle) {
        this.platTitle = platTitle;
    }

    public String getPlatSkuName() {
        return platSkuName;
    }

    public void setPlatSkuName(String platSkuName) {
        this.platSkuName = platSkuName;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }


    public static String getTbItemSignName(Integer itemSign) {
        String name = "";
        if (itemSign == null) {
            return name;
        }
        if (itemSign == CommonConstants.JUDGE_YES) {
            name = "套件商品";
        } else if (itemSign == CommonConstants.JUDGE_NO) {
            name = "普通商品";
        }
        return name;
    }

    public String getNumIids() {
        return numIids;
    }

    public void setNumIids(String numIids) {
        this.numIids = numIids;
    }

    public String getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(String skuIds) {
        this.skuIds = skuIds;
    }

    public Boolean getNeedTbSku() {
        return needTbSku;
    }

    public void setNeedTbSku(Boolean needTbSku) {
        this.needTbSku = needTbSku;
    }

    public String getUserIds() {
        return userIds;
    }

    public void setUserIds(String userIds) {
        this.userIds = userIds;
    }

    public Integer getFillOuterIdType() {
        return fillOuterIdType;
    }

    public void setFillOuterIdType(Integer fillOuterIdType) {
        this.fillOuterIdType = fillOuterIdType;
    }

    public String getSysItemIdStrs() {
        return sysItemIdStrs;
    }

    public void setSysItemIdStrs(String sysItemIdStrs) {
        this.sysItemIdStrs = sysItemIdStrs;
    }

    public String getSysSkuIdStrs() {
        return sysSkuIdStrs;
    }

    public void setSysSkuIdStrs(String sysSkuIdStrs) {
        this.sysSkuIdStrs = sysSkuIdStrs;
    }

    public static List<Long> getTaobaoIdListByUserIds(Staff staff, String UserIds) {
        if (StringUtils.isEmpty(UserIds)) {
            return null;
        }
        List<Long> taobaoIdList = new ArrayList<Long>();
        for (String userId : UserIds.split(",")) {
            User user = staff.getUserByUserId(Long.valueOf(userId));
            if (user != null) {
                taobaoIdList.add(user.getTaobaoId());
            }
        }
        return taobaoIdList;
    }

    // 0表示不连接任何表，1表示连接bridge表，2表示连接tb_item表
    public static int getJoinMode(ItemQueryParams itemQueryParams) {
        int joinMode = 0;
        if (null != itemQueryParams.getUserId() && itemQueryParams.getUserId() > 0L) {
            joinMode = 1;
            if (CollectionUtils.isNotEmpty(itemQueryParams.getcIds())) {
                joinMode = 2;
            }
        }

        if (null != itemQueryParams.getNumIid()) {
            joinMode = 2;
        }

        return joinMode;
    }

    public Boolean getHighlight() {
        return highlight;
    }

    public void setHighlight(Boolean highlight) {
        this.highlight = highlight;
    }

    public Integer getIsUpload() {
        return isUpload;
    }

    public void setIsUpload(Integer isUpload) {
        this.isUpload = isUpload;
    }

    public List<String> getNumIidList() {
        return numIidList;
    }

    public void setNumIidList(List<String> numIidList) {
        this.numIidList = numIidList;
    }

    public List<String> getSkuIdList() {
        return skuIdList;
    }

    public void setSkuIdList(List<String> skuIdList) {
        this.skuIdList = skuIdList;
    }

    public Integer getInclude() {
        return include;
    }

    public void setInclude(Integer include) {
        this.include = include;
    }

    public Long getFilterSysItemId() {
        return filterSysItemId;
    }

    public void setFilterSysItemId(Long filterSysItemId) {
        this.filterSysItemId = filterSysItemId;
    }

    public List<Long> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<Long> userIdList) {
        this.userIdList = userIdList;
    }

    public Integer getSyncType() {
        return syncType;
    }

    public void setSyncType(Integer syncType) {
        this.syncType = syncType;
    }

    public Integer getUseOnWay() {
        return useOnWay;
    }

    public void setUseOnWay(Integer useOnWay) {
        this.useOnWay = useOnWay;
    }

    public String getNeedCustomerFilter() {
        return needCustomerFilter;
    }

    public void setNeedCustomerFilter(String needCustomerFilter) {
        this.needCustomerFilter = needCustomerFilter;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public List<Long> getCustomerIdList() {
        return customerIdList;
    }

    public void setCustomerIdList(List<Long> customerIdList) {
        this.customerIdList = customerIdList;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public Integer getHasSalePrice() {
        return hasSalePrice;
    }

    public void setHasSalePrice(Integer hasSalePrice) {
        this.hasSalePrice = hasSalePrice;
    }

    public String getCalculateTypes() {
        return calculateTypes;
    }

    public void setCalculateTypes(String calculateTypes) {
        this.calculateTypes = calculateTypes;
    }

    public Integer getNeedSalePrice() {
        return needSalePrice;
    }

    public void setNeedSalePrice(Integer needSalePrice) {
        this.needSalePrice = needSalePrice;
    }

    public Long getSalePriceUserId() {
        return salePriceUserId;
    }

    public void setSalePriceUserId(Long salePriceUserId) {
        this.salePriceUserId = salePriceUserId;
    }

    public Long getExcludeSysItemId() {
        return excludeSysItemId;
    }

    public void setExcludeSysItemId(Long excludeSysItemId) {
        this.excludeSysItemId = excludeSysItemId;
    }

    public Integer getSkuSizeSort() {
        return skuSizeSort;
    }

    public void setSkuSizeSort(Integer skuSizeSort) {
        this.skuSizeSort = skuSizeSort;
    }

    public Integer getSuiteCover() {
        return suiteCover;
    }

    public void setSuiteCover(Integer suiteCover) {
        this.suiteCover = suiteCover;
    }

    public Double getOneIntegralMin() {
        return oneIntegralMin;
    }

    public void setOneIntegralMin(Double oneIntegralMin) {
        this.oneIntegralMin = oneIntegralMin;
    }

    public Double getOneIntegralMax() {
        return oneIntegralMax;
    }

    public void setOneIntegralMax(Double oneIntegralMax) {
        this.oneIntegralMax = oneIntegralMax;
    }

    public Double getMoreIntegralMin() {
        return moreIntegralMin;
    }

    public void setMoreIntegralMin(Double moreIntegralMin) {
        this.moreIntegralMin = moreIntegralMin;
    }

    public Double getMoreIntegralMax() {
        return moreIntegralMax;
    }

    public void setMoreIntegralMax(Double moreIntegralMax) {
        this.moreIntegralMax = moreIntegralMax;
    }

    public List<Long> getSysItemIdList() {
        return sysItemIdList;
    }

    public void setSysItemIdList(List<Long> sysItemIdList) {
        this.sysItemIdList = sysItemIdList;
    }

    public List<Long> getSysSkuIdList() {
        return sysSkuIdList;
    }

    public void setSysSkuIdList(List<Long> sysSkuIdList) {
        this.sysSkuIdList = sysSkuIdList;
    }


    public Integer getPureSpecialField() {
        return pureSpecialField;
    }

    public void setPureSpecialField(Integer pureSpecialField) {
        this.pureSpecialField = pureSpecialField;
    }

    public Integer getLoadPageNo() {
        return loadPageNo;
    }

    public void setLoadPageNo(Integer loadPageNo) {
        this.loadPageNo = loadPageNo;
    }

    public Long getLoadSysItemId() {
        return loadSysItemId;
    }

    public void setLoadSysItemId(Long loadSysItemId) {
        this.loadSysItemId = loadSysItemId;
    }

    public String getSupplierIdPrivilege() {
        return supplierIdPrivilege;
    }

    public void setSupplierIdPrivilege(String supplierIdPrivilege) {
        this.supplierIdPrivilege = supplierIdPrivilege;
    }

    public List<Long> getSupplierIdListPrivilege() {
        return supplierIdListPrivilege;
    }

    public void setSupplierIdListPrivilege(List<Long> supplierIdListPrivilege) {
        this.supplierIdListPrivilege = supplierIdListPrivilege;
    }

    public List<Long> getSysItemIdListSupplier() {
        return sysItemIdListSupplier;
    }

    public void setSysItemIdListSupplier(List<Long> sysItemIdListSupplier) {
        this.sysItemIdListSupplier = sysItemIdListSupplier;
    }

    public List<Long> getSysSkuIdListSupplier() {
        return sysSkuIdListSupplier;
    }

    public void setSysSkuIdListSupplier(List<Long> sysSkuIdListSupplier) {
        this.sysSkuIdListSupplier = sysSkuIdListSupplier;
    }

    public String getText2() {
        return text2;
    }

    public void setText2(String text2) {
        this.text2 = text2;
    }

    public String getContent2() {
        return content2;
    }

    public void setContent2(String content2) {
        this.content2 = content2;
    }

    public String getText3() {
        return text3;
    }

    public void setText3(String text3) {
        this.text3 = text3;
    }

    public String getContent3() {
        return content3;
    }

    public void setContent3(String content3) {
        this.content3 = content3;
    }

    public Long getPurchasePriceScope() {
        return purchasePriceScope;
    }

    public void setPurchasePriceScope(Long purchasePriceScope) {
        this.purchasePriceScope = purchasePriceScope;
    }

    public String getSkuOuterId() {
        return skuOuterId;
    }

    public void setSkuOuterId(String skuOuterId) {
        this.skuOuterId = skuOuterId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getCmCodes() {
        return cmCodes;
    }

    public void setCmCodes(String cmCodes) {
        this.cmCodes = cmCodes;
    }

    public String getItemCodes() {
        return itemCodes;
    }

    public void setItemCodes(String itemCodes) {
        this.itemCodes = itemCodes;
    }

    public String getSkuCodes() {
        return skuCodes;
    }

    public void setSkuCodes(String skuCodes) {
        this.skuCodes = skuCodes;
    }

    public List<Long> getWarnSysItemIdList() {
        return warnSysItemIdList;
    }

    public void setWarnSysItemIdList(List<Long> warnSysItemIdList) {
        this.warnSysItemIdList = warnSysItemIdList;
    }

    public List<Long> getWarnSysSkuIdList() {
        return warnSysSkuIdList;
    }

    public void setWarnSysSkuIdList(List<Long> warnSysSkuIdList) {
        this.warnSysSkuIdList = warnSysSkuIdList;
    }

    public List<Integer> getStockStatusList() {
        return stockStatusList;
    }

    public void setStockStatusList(List<Integer> stockStatusList) {
        this.stockStatusList = stockStatusList;
    }

    public Double getOnePackageIntegralMin() {
        return onePackageIntegralMin;
    }

    public void setOnePackageIntegralMin(Double onePackageIntegralMin) {
        this.onePackageIntegralMin = onePackageIntegralMin;
    }

    public Double getOnePackageIntegralMax() {
        return onePackageIntegralMax;
    }

    public void setOnePackageIntegralMax(Double onePackageIntegralMax) {
        this.onePackageIntegralMax = onePackageIntegralMax;
    }

    public Double getMorePackageIntegralMin() {
        return morePackageIntegralMin;
    }

    public void setMorePackageIntegralMin(Double morePackageIntegralMin) {
        this.morePackageIntegralMin = morePackageIntegralMin;
    }

    public Double getMorePackageIntegralMax() {
        return morePackageIntegralMax;
    }

    public void setMorePackageIntegralMax(Double morePackageIntegralMax) {
        this.morePackageIntegralMax = morePackageIntegralMax;
    }

    public Double getOneInspectionIntegralMin() {
        return oneInspectionIntegralMin;
    }

    public void setOneInspectionIntegralMin(Double oneInspectionIntegralMin) {
        this.oneInspectionIntegralMin = oneInspectionIntegralMin;
    }

    public Double getOneInspectionIntegralMax() {
        return oneInspectionIntegralMax;
    }

    public void setOneInspectionIntegralMax(Double oneInspectionIntegralMax) {
        this.oneInspectionIntegralMax = oneInspectionIntegralMax;
    }

    public Double getMoreInspectionIntegralMin() {
        return moreInspectionIntegralMin;
    }

    public void setMoreInspectionIntegralMin(Double moreInspectionIntegralMin) {
        this.moreInspectionIntegralMin = moreInspectionIntegralMin;
    }

    public Double getMoreInspectionIntegralMax() {
        return moreInspectionIntegralMax;
    }

    public void setMoreInspectionIntegralMax(Double moreInspectionIntegralMax) {
        this.moreInspectionIntegralMax = moreInspectionIntegralMax;
    }

    public String getSelectIntegralType() {
        return selectIntegralType;
    }

    public void setSelectIntegralType(String selectIntegralType) {
        this.selectIntegralType = selectIntegralType;
    }

    public String getTileItemName() {
        return tileItemName;
    }

    public void setTileItemName(String tileItemName) {
        this.tileItemName = tileItemName;
    }

    public String getTileOuterId() {
        return tileOuterId;
    }

    public void setTileOuterId(String tileOuterId) {
        this.tileOuterId = tileOuterId;
    }

    public String getTileShortTitle() {
        return tileShortTitle;
    }

    public void setTileShortTitle(String tileShortTitle) {
        this.tileShortTitle = tileShortTitle;
    }

    public String getTileRemark() {
        return tileRemark;
    }

    public void setTileRemark(String tileRemark) {
        this.tileRemark = tileRemark;
    }

    public String getTileSkuAlias() {
        return tileSkuAlias;
    }

    public void setTileSkuAlias(String tileSkuAlias) {
        this.tileSkuAlias = tileSkuAlias;
    }

    public String getTileSkuRemark() {
        return tileSkuRemark;
    }

    public void setTileSkuRemark(String tileSkuRemark) {
        this.tileSkuRemark = tileSkuRemark;
    }

    public String getTilePropertiesName() {
        return tilePropertiesName;
    }

    public void setTilePropertiesName(String tilePropertiesName) {
        this.tilePropertiesName = tilePropertiesName;
    }

    public String getTileSupplierId() {
        return tileSupplierId;
    }

    public void setTileSupplierId(String tileSupplierId) {
        if (StringUtils.isNotEmpty(tileSupplierId)) {
            String[] tileSupplierIdArr = tileSupplierId.split(",");

            List<Long> supplierIdList = new ArrayList<>();
            for (String supplierId : tileSupplierIdArr) {
                if (StringUtils.isEmpty(supplierId)) {
                    continue;
                }

                supplierIdList.add(Long.parseLong(supplierId));
            }

            if (CollectionUtils.isNotEmpty(supplierIdList)) {
                this.setTileSupplierIdList(supplierIdList);
            }
        }
        this.tileSupplierId = tileSupplierId;
    }


    public void setTileSkuOuterIds(String tileSkuOuterIds) {
        if (StringUtils.isNotEmpty(tileSkuOuterIds)) {
            String[] tileSkuOuterIdArr = tileSkuOuterIds.split(",");
            if (tileSkuOuterIdArr.length > 100){
                throw new IllegalArgumentException("一次性输入的商家编码数量不能超过100个");
            }
            List<String> tileSkuOuterIdList = new ArrayList<>();
            for (String skuOuterId : tileSkuOuterIdArr) {
                if (StringUtils.isEmpty(skuOuterId)) {
                    continue;
                }
                tileSkuOuterIdList.add(skuOuterId);
            }

            if (CollectionUtils.isNotEmpty(tileSkuOuterIdList)) {
                this.setTileSkuOuterIdList(tileSkuOuterIdList);
            }
        }
    }

    public String getSkuText() {
        return skuText;
    }

    public void setSkuText(String skuText) {
        this.skuText = skuText;
    }

    public String getSkuContent() {
        return skuContent;
    }

    public void setSkuContent(String skuContent) {
        this.skuContent = skuContent;
    }

    public String getTileSupplierItemOuterId() {
        return tileSupplierItemOuterId;
    }

    public void setTileSupplierItemOuterId(String tileSupplierItemOuterId) {
        this.tileSupplierItemOuterId = tileSupplierItemOuterId;
    }


    public List<Long> getTileSupplierIdList() {
        return tileSupplierIdList;
    }

    public void setTileSupplierIdList(List<Long> tileSupplierIdList) {
        this.tileSupplierIdList = tileSupplierIdList;
    }

    public Integer getMinAvailableStock() {
        return minAvailableStock;
    }

    public void setMinAvailableStock(Integer minAvailableStock) {
        this.minAvailableStock = minAvailableStock;
    }

    public Integer getMaxAvailableStock() {
        return maxAvailableStock;
    }

    public void setMaxAvailableStock(Integer maxAvailableStock) {
        this.maxAvailableStock = maxAvailableStock;
    }

    public boolean getNeedFilterAvailableStock() {
        if (minAvailableStock != null || maxAvailableStock != null) {
            return true;
        }
        if (stockStatus != null) {
            return true;
        }
        return CollectionUtils.isNotEmpty(stockStatusList) || CollectionUtils.isNotEmpty(warnStatusList);
    }

    public List<Long> getStockWarehouseIds() {
        return stockWarehouseIds;
    }

    public void setStockWarehouseIds(List<Long> stockWarehouseIds) {
        this.stockWarehouseIds = stockWarehouseIds;
    }

    public Boolean getIsAuto() {
        return isAuto;
    }

    public void setIsAuto(Boolean isAuto) {
        this.isAuto = isAuto;
    }

    public String getTileAllOuterId() {
        return tileAllOuterId;
    }

    public void setTileAllOuterId(String tileAllOuterId) {
        this.tileAllOuterId = tileAllOuterId;
    }

    public List<String> getTileAllOuterIdList() {
        return tileAllOuterIdList;
    }

    public void setTileAllOuterIdList(List<String> tileAllOuterIdList) {
        this.tileAllOuterIdList = tileAllOuterIdList;
    }

    public Double getOneWeightIntegralMin() {
        return oneWeightIntegralMin;
    }

    public void setOneWeightIntegralMin(Double oneWeightIntegralMin) {
        this.oneWeightIntegralMin = oneWeightIntegralMin;
    }

    public Double getOneWeightIntegralMax() {
        return oneWeightIntegralMax;
    }

    public void setOneWeightIntegralMax(Double oneWeightIntegralMax) {
        this.oneWeightIntegralMax = oneWeightIntegralMax;
    }

    public Double getMoreWeightIntegralMin() {
        return moreWeightIntegralMin;
    }

    public void setMoreWeightIntegralMin(Double moreWeightIntegralMin) {
        this.moreWeightIntegralMin = moreWeightIntegralMin;
    }

    public Double getMoreWeightIntegralMax() {
        return moreWeightIntegralMax;
    }

    public void setMoreWeightIntegralMax(Double moreWeightIntegralMax) {
        this.moreWeightIntegralMax = moreWeightIntegralMax;
    }

    public Integer getHide() {
        return hide;
    }

    public void setHide(Integer hide) {
        this.hide = hide;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getStartModified() {
        return startModified;
    }

    public void setStartModified(Date startModified) {
        this.startModified = startModified;
    }

    public Date getEndModified() {
        return endModified;
    }

    public void setEndModified(Date endModified) {
        this.endModified = endModified;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getMultiCode() {
        return multiCode;
    }

    public void setMultiCode(String multiCode) {
        this.multiCode = multiCode;
    }

    public List<Long> getTileSysItemIds() {
        return tileSysItemIds;
    }

    public void setTileSysItemIds(List<Long> tileSysItemIds) {
        this.tileSysItemIds = tileSysItemIds;
    }

    public List<Long> getTileSysSkuIds() {
        return tileSysSkuIds;
    }

    public void setTileSysSkuIds(List<Long> tileSysSkuIds) {
        this.tileSysSkuIds = tileSysSkuIds;
    }

    public List<String> getPlatOuterIdList() {
        return platOuterIdList;
    }

    public void setPlatOuterIdList(List<String> platOuterIdList) {
        this.platOuterIdList = platOuterIdList;
    }

    /**
     * 是否需要Join供应商关联表
     */
    public boolean getNeedJoinItemSupplierBridge() {
        return filterSupplierAuth || StringUtils.isNotEmpty(tileSupplierItemOuterId) || CollectionUtils.isNotEmpty(tileSupplierIdList);
    }

    public Integer getMinDefectiveStock() {
        return minDefectiveStock;
    }

    public void setMinDefectiveStock(Integer minDefectiveStock) {
        this.minDefectiveStock = minDefectiveStock;
    }

    public Integer getMaxDefectiveStock() {
        return maxDefectiveStock;
    }

    public void setMaxDefectiveStock(Integer maxDefectiveStock) {
        this.maxDefectiveStock = maxDefectiveStock;
    }

    public boolean getNeedJoinSumStock() {
        if (minDefectiveStock != null || maxDefectiveStock != null) {
            return true;
        }
        if (minAvailableStock != null || maxAvailableStock != null) {
            return true;
        }
        return CollectionUtils.isNotEmpty(stockStatusList) || CollectionUtils.isNotEmpty(warnStatusList);
    }

    public boolean getNeedSumDefectiveField() {
        if (minDefectiveStock != null || maxDefectiveStock != null) {
            return true;
        }
        return false;
    }

    public boolean getNeedSumAvailableField() {
        if (minAvailableStock != null || maxAvailableStock != null) {
            return true;
        }
        return CollectionUtils.isNotEmpty(stockStatusList) || CollectionUtils.isNotEmpty(warnStatusList);
    }

    public boolean getNeedJoinItemWarn() {
        if (CollectionUtils.isEmpty(stockStatusList) && CollectionUtils.isEmpty(warnStatusList)) {
            return false;
        }
        return stockStatusList.contains(StockConstants.DMJ_ITEM_STOCK_STATUS_NORMAL) || stockStatusList.contains(StockConstants.DMJ_ITEM_STOCK_STATUS_ALARM)
                || warnStatusList.contains(StockConstants.DMJ_ITEM_STOCK_STATUS_NORMAL) || warnStatusList.contains(StockConstants.DMJ_ITEM_STOCK_STATUS_ALARM);
    }

    public boolean isFastQuery() {
        return fastQuery;
    }

    public void setFastQuery(boolean fastQuery) {
        this.fastQuery = fastQuery;
    }

    public Long getLastId() {
        return lastId;
    }

    public boolean getNeedJoinItemWarnTable() {
        if (stockStatus != null && (stockStatus == 1 || stockStatus == 2)) {
            return true;
        }
        //如果指定了库存状态筛选条件
        if (CollectionUtils.isNotEmpty(stockStatusList) && (stockStatusList.contains(1) || stockStatusList.contains(2))) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(warnStatusList)) {
            return true;
        }
        return false;
    }

    public void setLastId(Long lastId) {
        this.lastId = lastId;
    }

    public String getQuerySource() {
        return querySource;
    }

    public void setQuerySource(String querySource) {
        this.querySource = querySource;
    }

    public Integer getPurchaseSourceType() {
        return purchaseSourceType;
    }

    public void setPurchaseSourceType(Integer purchaseSourceType) {
        this.purchaseSourceType = purchaseSourceType;
    }

    public Boolean getNeedOrder() {
        return needOrder;
    }

    public void setNeedOrder(Boolean needOrder) {
        this.needOrder = needOrder;
    }

    public boolean isNeedTotal() {
        return needTotal;
    }

    public void setNeedTotal(boolean needTotal) {
        this.needTotal = needTotal;
    }

    public boolean isOnlyTotal() {
        return onlyTotal;
    }

    public void setOnlyTotal(boolean onlyTotal) {
        this.onlyTotal = onlyTotal;
    }

    public Double getReceiveGoodsIntegralMin() {
        return receiveGoodsIntegralMin;
    }

    public void setReceiveGoodsIntegralMin(Double receiveGoodsIntegralMin) {
        this.receiveGoodsIntegralMin = receiveGoodsIntegralMin;
    }

    public Double getReceiveGoodsIntegralMax() {
        return receiveGoodsIntegralMax;
    }

    public void setReceiveGoodsIntegralMax(Double receiveGoodsIntegralMax) {
        this.receiveGoodsIntegralMax = receiveGoodsIntegralMax;
    }

    public Double getQualityCheckIntegralMin() {
        return qualityCheckIntegralMin;
    }

    public void setQualityCheckIntegralMin(Double qualityCheckIntegralMin) {
        this.qualityCheckIntegralMin = qualityCheckIntegralMin;
    }

    public Double getQualityCheckIntegralMax() {
        return qualityCheckIntegralMax;
    }

    public void setQualityCheckIntegralMax(Double qualityCheckIntegralMax) {
        this.qualityCheckIntegralMax = qualityCheckIntegralMax;
    }

    public Double getOneSendGoodsIntegralMin() {
        return oneSendGoodsIntegralMin;
    }

    public void setOneSendGoodsIntegralMin(Double oneSendGoodsIntegralMin) {
        this.oneSendGoodsIntegralMin = oneSendGoodsIntegralMin;
    }

    public Double getOneSendGoodsIntegralMax() {
        return oneSendGoodsIntegralMax;
    }

    public void setOneSendGoodsIntegralMax(Double oneSendGoodsIntegralMax) {
        this.oneSendGoodsIntegralMax = oneSendGoodsIntegralMax;
    }

    public Double getMoreSendGoodsIntegralMin() {
        return moreSendGoodsIntegralMin;
    }

    public void setMoreSendGoodsIntegralMin(Double moreSendGoodsIntegralMin) {
        this.moreSendGoodsIntegralMin = moreSendGoodsIntegralMin;
    }

    public Double getMoreSendGoodsIntegralMax() {
        return moreSendGoodsIntegralMax;
    }

    public void setMoreSendGoodsIntegralMax(Double moreSendGoodsIntegralMax) {
        this.moreSendGoodsIntegralMax = moreSendGoodsIntegralMax;
    }

    public Boolean getNeedFillCustomer() {
        return needFillCustomer;
    }

    public void setNeedFillCustomer(Boolean needFillCustomer) {
        this.needFillCustomer = needFillCustomer;
    }

    public boolean getIgnoreActiveStatus() {
        return ignoreActiveStatus;
    }

    public void setIgnoreActiveStatus(boolean ignoreActiveStatus) {
        this.ignoreActiveStatus = ignoreActiveStatus;
    }

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public Integer getPicStatus() {
        return picStatus;
    }

    public void setPicStatus(Integer picStatus) {
        this.picStatus = picStatus;
    }

    public Double getProcessIntegralMin() {
        return processIntegralMin;
    }

    public void setProcessIntegralMin(Double processIntegralMin) {
        this.processIntegralMin = processIntegralMin;
    }

    public Double getProcessIntegralMax() {
        return processIntegralMax;
    }

    public void setProcessIntegralMax(Double processIntegralMax) {
        this.processIntegralMax = processIntegralMax;
    }

    public Double getBaggingIntegralMin() {
        return baggingIntegralMin;
    }

    public void setBaggingIntegralMin(Double baggingIntegralMin) {
        this.baggingIntegralMin = baggingIntegralMin;
    }

    public Double getBaggingIntegralMax() {
        return baggingIntegralMax;
    }

    public void setBaggingIntegralMax(Double baggingIntegralMax) {
        this.baggingIntegralMax = baggingIntegralMax;
    }

    public Integer getMainScopeQuery() {
        return mainScopeQuery;
    }

    public void setMainScopeQuery(Integer mainScopeQuery) {
        this.mainScopeQuery = mainScopeQuery;
    }

    public Integer getExtendsScopeQuery() {
        return extendsScopeQuery;
    }

    public void setExtendsScopeQuery(Integer extendsScopeQuery) {
        this.extendsScopeQuery = extendsScopeQuery;
    }

    public Integer getExcelTypeCode() {
        return excelTypeCode;
    }

    public void setExcelTypeCode(Integer excelTypeCode) {
        this.excelTypeCode = excelTypeCode;
    }

    public Integer getMakeGift() {
        return makeGift;
    }

    public void setMakeGift(Integer makeGift) {
        this.makeGift = makeGift;
    }

    public String getCatIds() {
        return catIds;
    }

    public void setCatIds(String catIds) {
        this.catIds = catIds;
    }

    public List<String> getCatIdList() {
        return catIdList;
    }

    public void setCatIdList(List<String> catIdList) {
        this.catIdList = catIdList;
    }

    public Boolean getPurchaseNeedOrder() {
        return purchaseNeedOrder;
    }

    public void setPurchaseNeedOrder(Boolean purchaseNeedOrder) {
        this.purchaseNeedOrder = purchaseNeedOrder;
    }

    public String getSelectedItems() {
        return selectedItems;
    }

    public void setSelectedItems(String selectedItems) {
        this.selectedItems = selectedItems;
    }

    public Integer getQueryEmptyBrand() {
        return queryEmptyBrand;
    }

    public void setQueryEmptyBrand(Integer queryEmptyBrand) {
        this.queryEmptyBrand = queryEmptyBrand;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getOutIdType() {
        return outIdType;
    }

    public void setOutIdType(Integer outIdType) {
        this.outIdType = outIdType;
    }

    public String getMainOuterId() {
        return mainOuterId;
    }

    public void setMainOuterId(String mainOuterId) {
        this.mainOuterId = mainOuterId;
    }

    public Integer getSkuOutIdType() {
        return skuOutIdType;
    }

    public void setSkuOutIdType(Integer skuOutIdType) {
        this.skuOutIdType = skuOutIdType;
    }

    public String getSkuOrderColumn() {
        return skuOrderColumn;
    }

    public void setSkuOrderColumn(SortField skuSortField) {
        if (skuSortField != null && SKU_SUPPORT_SORT_FIELDS.contains(skuSortField)) {
            this.skuOrderColumn = skuSortField.getValue();
        }
    }

    public String getItemOrderColumn() {
        return itemOrderColumn;
    }

    public void setItemOrderColumn(SortField itemSortField) {
        if (itemSortField != null && ITEM_SUPPORT_SORT_FIELDS.contains(itemSortField)) {
            this.itemOrderColumn = itemSortField.getValue();
        }
    }

    public Boolean getOrderDesc() {
        return orderDesc;
    }

    public void setOrderDesc(Boolean orderDesc) {
        this.orderDesc = orderDesc;
    }

    public Boolean getItemPrintOrder() {
        return itemPrintOrder;
    }

    public void setItemPrintOrder(Boolean itemPrintOrder) {
        this.itemPrintOrder = itemPrintOrder;
    }

    public Integer getHideFlag() {
        return hideFlag;
    }

    public void setHideFlag(Integer hideFlag) {
        this.hideFlag = hideFlag;
    }

    public Boolean getCheckActiveStatus() {
        return checkActiveStatus;
    }

    public void setCheckActiveStatus(Boolean checkActiveStatus) {
        this.checkActiveStatus = checkActiveStatus;
    }

    public List<Integer> getWarnStatusList() {
        return warnStatusList;
    }

    public void setWarnStatusList(List<Integer> warnStatusList) {
        this.warnStatusList = warnStatusList;
    }

    public Integer getOneSixEightEightMapStatus() {
        return oneSixEightEightMapStatus;
    }

    public void setOneSixEightEightMapStatus(Integer oneSixEightEightMapStatus) {
        this.oneSixEightEightMapStatus = oneSixEightEightMapStatus;
    }

    public boolean getNeedAddOtherDataToCalculate() {
        return CollectionUtils.isNotEmpty(this.warnStatusList) && (Objects.nonNull(this.includePurchase) && this.includePurchase == 1 || Objects.nonNull(this.includePurchaseInStock) && this.includePurchaseInStock == 1);
    }

    public boolean getFilterByDataPrivilege() {
        return filterByDataPrivilege;
    }

    public void setFilterByDataPrivilege(boolean filterByDataPrivilege) {
        this.filterByDataPrivilege = filterByDataPrivilege;
    }

    public Integer getItemActiveStatus() {
        return itemActiveStatus;
    }

    public void setItemActiveStatus(Integer itemActiveStatus) {
        this.itemActiveStatus = itemActiveStatus;
    }

    public boolean isQuickQuery() {
        return quickQuery;
    }

    public void setQuickQuery(boolean quickQuery) {
        this.quickQuery = quickQuery;
    }

    public List<String> getMultiCodeList() {
        return multiCodeList;
    }

    public void setMultiCodeList(List<String> multiCodeList) {
        this.multiCodeList = multiCodeList;
    }

    public boolean isNeedMockCount() {
        return needMockCount;
    }

    public void setNeedMockCount(boolean needMockCount) {
        this.needMockCount = needMockCount;
    }

    public Integer getCover() {
        return cover;
    }

    public void setCover(Integer cover) {
        this.cover = cover;
    }

    public Integer getFilterOnsale() {
        return filterOnsale;
    }

    public void setFilterOnsale(Integer filterOnsale) {
        this.filterOnsale = filterOnsale;
    }

    public Integer getFilterEmptyBrandByPrivilege() {
        return filterEmptyBrandByPrivilege;
    }

    public void setFilterEmptyBrandByPrivilege(Integer filterEmptyBrandByPrivilege) {
        this.filterEmptyBrandByPrivilege = filterEmptyBrandByPrivilege;
    }

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public List<String> getInItemOuterIdList() {
        return inItemOuterIdList;
    }

    public void setInItemOuterIdList(List<String> inItemOuterIdList) {
        this.inItemOuterIdList = inItemOuterIdList;
    }

    public String getShipper() {
        return shipper;
    }

    public void setShipper(String shipper) {
        this.shipper = shipper;
    }

    public String getShipperId() {
        return shipperId;
    }

    public void setShipperId(String shipperId) {
        this.shipperId = shipperId;
    }

    public List<String> getShipperIdList() {
        return shipperIdList;
    }

    public void setShipperIdList(List<String> shipperIdList) {
        this.shipperIdList = shipperIdList;
    }

    public Integer getShipperItemFlag() {
        return shipperItemFlag;
    }

    public void setShipperItemFlag(Integer shipperItemFlag) {
        this.shipperItemFlag = shipperItemFlag;
    }


    public List<Long> getLastSysItemIds() {
        return lastSysItemIds;
    }

    public void setLastSysItemIds(List<Long> lastSysItemIds) {
        this.lastSysItemIds = lastSysItemIds;
    }

    public Boolean getEnableLastSysItemId() {
        return enableLastSysItemId;
    }

    public void setEnableLastSysItemId(Boolean enableLastSysItemId) {
        this.enableLastSysItemId = enableLastSysItemId;
    }

    public String getBrandNames() {
        return brandNames;
    }

    public void setBrandNames(String brandNames) {
        this.brandNames = brandNames;
    }

    public List<String> getBrandNameList() {
        return brandNameList;
    }

    public void setBrandNameList(List<String> brandNameList) {
        this.brandNameList = brandNameList;
    }

    public String getcIdStrs() {
        return cIdStrs;
    }

    public void setcIdStrs(String cIdStrs) {
        this.cIdStrs = cIdStrs;
    }

    public String getSkuShortTitle() {
        return skuShortTitle;
    }

    public void setSkuShortTitle(String skuShortTitle) {
        this.skuShortTitle = skuShortTitle;
    }

    public Boolean getItemConfigSort() {
        return itemConfigSort;
    }

    public void setItemConfigSort(Boolean itemConfigSort) {
        this.itemConfigSort = itemConfigSort;
    }

    public Integer getMultiCodeType() {
        return multiCodeType;
    }

    public void setMultiCodeType(Integer multiCodeType) {
        this.multiCodeType = multiCodeType;
    }

    public List<SimpleSuiteBridge> getSimpleSuiteBridges() {
        return simpleSuiteBridges;
    }

    public void setSimpleSuiteBridges(List<SimpleSuiteBridge> simpleSuiteBridges) {
        this.simpleSuiteBridgeSize = simpleSuiteBridges.size();
        this.simpleSuiteBridges = simpleSuiteBridges;
    }

    public Integer getSimpleSuiteBridgeSize() {
        return simpleSuiteBridgeSize;
    }

    public void setSimpleSuiteBridgeSize(Integer simpleSuiteBridgeSize) {
        this.simpleSuiteBridgeSize = simpleSuiteBridgeSize;
    }
}

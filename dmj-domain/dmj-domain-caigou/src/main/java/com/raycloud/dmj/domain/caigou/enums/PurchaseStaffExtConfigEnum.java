package com.raycloud.dmj.domain.caigou.enums;

public enum PurchaseStaffExtConfigEnum {
    /**
     * 新建/修改采购单 自动备注
     */
    PURCHASE_CREATED_AUTO_REMARK("purchaseCreatedAutoRemark", 0, "自动备注"),

    /**
     * 新建/修改采购单 正序添加商品
     */
    PURCHASE_CREATED_ASC_ITEMS("purchaseCreateAscItems", 0, "正序添加商品"),

    /**
     * 新建/修改采购单 保存后按供应商自定拆分
     */
    PURCHASE_CREATED_SPLIT_BY_SUPPLIER("purchaseCreatedSplitBySupplier", 0, "保存后按供应商自定拆分"),

    /**
     * 新建/修改采购单 交货日期强制为空
     */
    PURCHASE_CREATED_FORCE_NULL("purchaseCreatedForceNull", 0, "交货日期强制为空"),

    /**
     * 新建/修改采购单 添加商品自动带入采购单供应商
     */
    PURCHASE_CREATED_AUTO_ADD_SUPPLIER("purchaseCreatedAutoAddSupplier", 0, "添加商品自动带入采购单供应商"),

    /**
     * 智能加工手动规则集合
     */
    SMART_PRODUCT_MANUAL_RULE_IDS("smartProductManualRuleIds", "", "智能加工手动规则集合"),

    /**
     * 快销语音播报
     */
    FAST_STOCK_VOICE("fastStockVoice", 0, "快销语音播报"),

    /**
     * 备货采购建议采购数减去原材料加工数
     */
    PURCHASE_MINUS_MATERIAL_STOCK_NUM("minusMaterialStockNum", false, "备货采购建议采购数减去原材料加工数"),

    /**
     * 缺货采购建议采购数减去原材料加工数
     */
    PURCHASE_MINUS_MATERIAL_OUT_STOCK_NUM("minusMaterialOutStockNum", false, "缺货采购建议采购数减去原材料加工数"),

    /**
     * 采购单管理-修改标签-新增勾选项“是否同步更新收货单标签”
     */
    PURCHASE_LABEL_SYNCHRONIZATION_FLAG("purchaseLabelSynchronizationFlag", false, "采购单标签是否同步更新收货单标签"),

    /**
     * 收货单管理-修改备注-新增勾选项“是否同步更新采购单标签”
     */
    PURCHASE_REMARK_SYNCHRONIZATION_FLAG("purchaseRemarkSynchronizationFlag", false, "采购单备注是否同步更新收货单标签"),

    /**
     * 收货单管理-修改备注-新增勾选项“是否同步更新采购单标签”
     */
    WAREHOUSEENTRY_REMARK_SYNCHRONIZATION_FLAG("warehouseEntryRemarkSynchronizationFlag", false, "收货单备注是否同步更新采购单标签"),

    /**
     * 其他出入库导出明细，支持自动填充单据信息到对应的商品 0、不支持 1、支持
     */
    OTHER_IN_OUT_ADD_ORDER_INFO_EXPORT("otherInOutAddOrderInfoExport",0,"默认填充单据信息至对应每个商品明细行导出"),

    /**
     * 展示≤0的建议采购数
     */
    SHOW_LE_TO_ZERO_PURCHASE_STOCK("showLeToZeroPurchaseStock", false, "展示≤0的建议采购数"),

    /**
     * 未匹配到外采的数量生成备货唯一码 0、不支持 1、支持
     */
    PRINT_WITH_CREATE_BACK_CODE("printWithCreateBackCode",0,"未匹配到外采的数量生成备货唯一码"),

    /**
     * （打印标签）订单唯一码---字段加密  字段配置 逗号分隔（1:平台  2:......）
     */
    ORDER_UNIQUE_ENCRYPT_FIELD("orderUniqueEncryptField","","订单唯一码-字段加密"),

    /**
     * 备货/缺货采购 --
     * 销退率计算纬度：12：sku/类目
     * 时长：天数
     * 缺货 -- 超出比例
     */
    REFUND_RATIO_STOCK_TYPE("refundRatioStockType",1,""),
    REFUND_RATIO_BACK_TYPE("refundRatioBackType",1,""),
    REFUND_RATIO_STOCK_DAY("refundRatioStockDay",0,""),
    REFUND_RATIO_BACK_DAY("refundRatioBackDay",0,""),
    EXCEED_RATIO("exceedRatio",0,""),

    SYNC_UPDATE_SUPPLIER("syncUpdateSupplier", "", "同步更新到供应商管理"),
    /**
     * 库区警戒手动规则集合
     */
    SMART_STOCK_REGION_RULES("SmartStockRegionRules", "", "库区警戒手动规则集合"),

    CHECK_SUPPLIER("checkSupplier", "", "校验当前供应商"),

    WE_ADD_DETAIL_CHECK_SUPPLIER("weAddDetailCheckSupplier", "", "收货添加明细校验当前供应商"),

    WE_FAST_RECEIVE_CHECK_SUPPLIER("weFastReceiveCheckSupplier", "", "快速收货扫描校验当前供应商"),

    PURCHASE_ON_WAY_QUANTITIES_ORDER("PurchaseStaffExtConfigEnum", "0", "库存状态列表采购在途数排序"),

    EXCLUDE_PURCHASE_PRICE_IS_ZERO("excludePurchasePriceIsZero", false, "排除采购单价为0的采购单"),


    TAX_RATE_CONTROLLER("taxRateController", "", "税率管控"),
    /**
     * 自动打印内容 1打印编码 2打印吊牌 3打印水洗唛，逗号拼接
     */
    AUTO_PRINT_CONTENT("autoPrintContent", "", "自动打印内容"),

    /**
     * 自动打印数量类型
     */
    AUTO_PRINT_QUANTITY_TYPE("autoPrintQuantityType", 0, "商品信息打印数量设置"),

    /**
     * 自动打印固定数量
     */
    AUTO_PRINT_FIXED_QUANTITY("autoPrintFixedQuantity", 1, "商品信息打印固定数量"),


    WE_ADD_DETAIL_SET_GOOD_QUANTITY("weAddDetailSetGoodQuantity", "", "收货添加明细设置扫入数量"),
    WE_ADD_DETAIL_GOOD_QUANTITY("weAddDetailGoodQuantity", 1, "收货添加明细扫入数量"),

    ;
    private String key;

    private Object defaultValue;

    private String remark;

    PurchaseStaffExtConfigEnum(String key, Object defaultValue, String remark) {
        this.key = key;
        this.defaultValue = defaultValue;
        this.remark = remark;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(Object defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public static PurchaseStaffExtConfigEnum parseByKey(String key) {
        if (key == null) {
            return null;
        }

        for (PurchaseStaffExtConfigEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value;
            }
        }
        return null;
    }
}

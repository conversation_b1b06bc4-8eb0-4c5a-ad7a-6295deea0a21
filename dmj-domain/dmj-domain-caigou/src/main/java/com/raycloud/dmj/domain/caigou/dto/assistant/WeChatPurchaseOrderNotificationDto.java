package com.raycloud.dmj.domain.caigou.dto.assistant;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @ClassName: WeChatPurchaseOrderNotificationDto
 * @Description: 采购推送消息体DTO
 * @Author: zl
 * @Date: 2025/3/26 17:25
 */

public class WeChatPurchaseOrderNotificationDto implements Serializable {

    public static final int MAX_REPORT_LENGTH = 20000;

    public WeChatPurchaseOrderNotificationDto(List<WeChatPurchaseOrderInfo> orders, EnvironmentEnum env) {
        this.orders = orders;
        this.env = env;
    }


    // 采购单列表
    private List<WeChatPurchaseOrderInfo> orders;

    private EnvironmentEnum env;


    public List<WeChatPurchaseOrderInfo> getOrders() {
        return orders;
    }

    public void setOrders(List<WeChatPurchaseOrderInfo> orders) {
        this.orders = orders;
    }

    public EnvironmentEnum getEnv() {
        return env;
    }

    public void setEnv(EnvironmentEnum env) {
        this.env = env;
    }

    // 核心转换方法
    public String generateReport() {
        StringBuilder sb = new StringBuilder();

        // 头部信息
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("M月d日"));
        String orderNumbers = String.join(",", orders.stream()
                .map(o -> o.getShortId())
                .toArray(String[]::new));


        sb.append(String.format("%s报单，采购单：%s；已发，请查收\n", date, orderNumbers));

        // 订单详情
        orders.forEach(order -> {
            sb.append(String.format("采购单：%s\n", order.getShortId()))
                    .append(String.format("商品种类：%d\n", order.getItemKindCount()))
                    .append(String.format("商品数量：%d\n", order.getItemQuantity()))
                    .append(buildShareLink(order))
                    .append("\n\n");
        });
        String report = sb.toString().trim();
        int length = Math.min(report.length(), MAX_REPORT_LENGTH);
        return report.substring(0, length);
    }


    private String buildShareLink(WeChatPurchaseOrderInfo order) {
        return String.format("https://%s.superboss.cc/purchaseShare.html?purchaseId=%s&%s", env.getSubdomain(), order.getShareId(), order.getUrl());
    }

    // 采购单对象消息体
    @Data
    public static class WeChatPurchaseOrderInfo {

        public WeChatPurchaseOrderInfo(String shortId, Long shareId, Integer itemKindCount, Long itemQuantity, String url) {
            this.shortId = shortId;
            this.shareId = shareId;
            this.itemKindCount = itemKindCount;
            this.itemQuantity = itemQuantity;
            this.url = url;
        }

        /**
         * 采购短号
         */
        private String shortId;
        private Long shareId;
        private Integer itemKindCount;
        private Long itemQuantity;
        private String url;

    }


    // 环境配置枚举
    public enum EnvironmentEnum {


        DEV("stage3erp", "dev"),
        GRAY("erp1", "gray"),
        GRAY2("erp2", "gray2"),
        GRAY3("erp3", "gray3"),
        GRAY4("erp4", "gray4"),
        GRAY5("erp5", "gray5"),
        GRAY6("erp6", "gray6"),
        PROD("erp", "prod"),
        PREISSUEA("erpa", "preissue"),
        PREISSUEB("erpb", "preissue"),

        ;
        private final String env;
        private final String subdomain;


        public static EnvironmentEnum getEnvironmentEnum(String env) {
            for (EnvironmentEnum value : values()) {
                if (value.getEnv().equals(env)) {
                    return value;
                }
            }
            return EnvironmentEnum.GRAY2;
        }

        EnvironmentEnum(String subdomain, String env) {
            this.subdomain = subdomain;
            this.env = env;
        }

        public String getSubdomain() {
            return subdomain;
        }

        public String getEnv() {
            return env;
        }
    }
}

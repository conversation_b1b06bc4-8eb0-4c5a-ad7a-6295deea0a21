package com.raycloud.dmj.web.model.caigou;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.caigou.*;
import com.raycloud.dmj.services.domain.ReceiptType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;

/**
 * 收货单vo
 * Created by guzy on 16/4/9.
 */
public class WarehouseEntryVo implements Serializable, Orderable<WarehouseEntryDetailVo> {

    private static final long serialVersionUID = -4695515074909132771L;

    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;


    /**
     * 审核人编号
     */
    private Long auditorId;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 审核日期
     */
    private Date auditorDate;

    /**
     * 完成人编号
     */
    private Long finisherId;
    /**
     * 完成时间
     */
    private Date finished;

    /**
     * 完成人姓名
     */
    private String finisherName;

    /**
     * 上架时间
     */
    private Date shelveTime;

    /**
     * 收货单编号
     */
    private String code;

    private String status;

    private String statusName;

    /**
     * 采购单编号
     */
    private String purchaseOrderCode;

    /**
     * 采购单id
     */
    private Long purchaseOrderId;

    /**
     * 供应商名
     */
    private String supplierName;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 货主ID 字符串拼接而成
     */
    private String shipperId;

    private String shipperName;
    /**
     * 仓库名
     */
    private String warehouseName;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 出库仓库id
     */
    private Long outWarehouseId;

    /**
     * 出库仓库名称
     */
    private String outWarehouseName;

    /**
     * 容器号
     */
    private String containerNo;

    /**
     * 数量
     */
    private Long quantity;

    private Long receiveQuantity;

    private Long cancelQuantity;

    /**
     * 商品种类数量
     */
    private Integer items;

    /**
     * 批打总价
     */
    private Double wholeSaleTotalAmount;

    /**
     * 收货总金额=商品总金额+运费+其他费用－其他优惠；
     */
    private Double totalDetailFee;

    /**
     * 商品总金额
     */
    private Double totalFee;

    /**
     * 作废金额
     */
    private Double cancelAmount;

    /**
     * 采购总数
     */
    private Long purchaseQuantity;

    /**
     * 待上架数量
     */
    private Long waitShelveQuantity;

    /**
     * 上架数
     */
    private Long shelvedQuantity;

    /**
     * 已接收良品数
     */
    private Long getGoodNum;

    /**
     * 已接收次品数
     */
    private Long getBadNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 旗帜
     */
    private Integer flag;

    /**
     * 制单人
     */
    private Long createrId;
    /**
     * 制单人名称
     */
    private String createrName;

    private Integer needCheck;

    private Integer hasCheck;

    private Integer quantityCheck;

    /**
     * pda收货状态，0：未进行pda收货 1：pda收货中
     */
    private Integer pdaWeTaskStatus;

    /**
     * 业务类型
     */
    private String busyType;

    /**
     * 业务类型desc
     */
    private String busyTypeDesc;

    /**
     * 业务id
     */
    private Long busyId;

    /**
     * 业务单据号
     */
    private String busyCode;

    /**
     * 良品上架金额
     */
    private Double goodShelveFee = 0D;

    /**
     * 次品上架金额
     */
    private Double badShelveFee = 0D;

    /**
     * 业务出库单id
     */
    private Long busyOutId;

    /**
     * 业务出库单单据号
     */
    private String busyOutCode;

    /**
     * 收货单详情
     */
    private List<WarehouseEntryDetailVo> details;


    /**
     * 收货单唯一码详情
     */
    private List<WarehouseEntryWaveCodeDetailVo> waveCodedetails;
    /**
     * 合并明细
     */
    private List<PurchaseItemMerge<WarehouseEntryDetailVo>> merges;

    /**
     * 其他金额
     */
    private Double othersFee;

    /**
     * 折扣金额
     */
    private Double discountAmount;


    /**
     * 物流方式
     */
    private Integer logisticsType;

    /**
     * 运费
     */
    private Double freight;

    /**
     * 金额分摊方式
     *
     * @see com.raycloud.dmj.domain.caigou.enums.FeeShareTypeEnum
     */
    private Integer feeShareType;

    /**
     * 收货短号
     */
    private Long shortId;

    /**
     * 关联短号(采购单短号or采退单短号)
     */
    private String associationShortId;

    /**
     * 票据日期
     */
    private Date billDate;

    /**
     * 标签
     */
    private List<Label> labels;

    /**
     * 供应商分类
     */
    private String categoryName;

    /**
     * 操作状态
     * 例如：财审之类的
     */
    private String operateStatus;

    /**
     * 操作状态
     * 例如：财审之类的
     */
    private String operateStatusName;

    /**
     * 入库位置,货位/入库暂存区/销退暂存区/次品暂存区/通用暂存区, 1/2/3/4/5
     * 可以参考：采退的位置选择，字段的选择是一样
     * {@link com.raycloud.dmj.domain.caigou.PurchaseReturn##outLocation}
     */
    private Integer inLocation;
    /**
     * 其他出入库自定义的出入库类型
     * {@link CustomTypeTeam##customType}
     */
    private String customType;

    /**
     * 收货调整 1是 0否
     */
    private Integer adjustType;


    /**
     * 上架状态 0-未上架，1-已上架
     */
    private Integer hasShelve;

    /**
     * 采购人，关联采购单的制单人
     */
    private String purchaser;

    /**
     * 凭证字段
     */
    private String proofUrl;

    /**
     * 单据凭证名称
     */
    private String proofName;

    /**
     * 单据凭证类型 pic,excel,pdf
     */
    private String proofType;

    /**
     * 附件集合
     */
    private List<ProofInfo> proofInfoList;

    /**
     * 外部采购编号
     */
    private String outerCode;

    /**
     * 对接平台 appKey
     */
    private Integer weSourceKey;
    /**
     * 收货单对接平台外部单号
     */
    private String weSourceId;

    /**
     * 收货单对接平台外部单号
     */
    private String externalSourceId;
    /**
     * 采退单id
     */
    private Long purchaseReturnId;

    private Date canceled;


    /** 一下字段收货v2 使用   兼容不同状态的收货单字段*/
    private Long quantity2NotFinish;
    private Long quantity2Finished;
    private Double totalDetailFee2NotFinish;
    private Double totalDetailFee2Finished;

    private Date purchaseOrderCreated;

    private Date purchaseOrderFinishDate;

    private Long realQuantity;

    public Long getRealQuantity() {
        return realQuantity;
    }

    public void setRealQuantity(Long realQuantity) {
        this.realQuantity = realQuantity;
    }

    private Integer totalDetailCount;

    /**
     * 是否快速收货  0:不是快速收货   1:唯一码收货  2:进仓装箱快速收货   3:按箱收货快速收货
     */
    private Integer isFastReceive;

    /**
     * 销售总价
     */
    private Double saleTotalAmount;

    /**
     * 预入库 1 是 0 否
     */
    private Integer isPreIn;

    private List<WeItemGoodsSection> weItemGoodsSections;


    private Integer warehouseType;
    /**
     * 收货单打印状态 0:未打印 1:已打印
     */
    private Integer printStatus;

    private String printStatusStr;


    /**
     * 交货日期
     */
    private Date deliveryDate;


    /**
     * 合同备注
     */
    private String contractRemark;

    /**
     * 套装状态 0、不含套装 1、含有套装 2、曾今含有套装
     */
    private Integer suitStatus;

    /**
     * 总工费（采购单明细“工费金额”的合计值）
     */
    private Double totalLaborCostAmount;

    /**
     * 总重量（采购单明细“已收重量”的合计值）
     */
    private Double totalReceiveWeight;

    /**
     * 财审时间
     */
    private Date financedDate;



    /**
     *  1688订单号
     */
    private List<String> platformOrderIdList;

    /**
     * 打印次数
     */
    private Integer printNum;

    public void setPrintNum (Integer printNum) {
        this.printNum = printNum;
    }

    public Integer getPrintNum () {
        return this.printNum = printNum;
    }


    /**
     * 预约入库单id
     */
    private Long preinOrderId;

    /**
     * 预约入库单编码
     */
    private String preinOrderCode;

    public List<WarehouseEntryDetailVo> getDetails() {
        if (details == null) {
            details = new ArrayList<>();
        }
        return details;
    }

    @Override
    public void setDetails(List<WarehouseEntryDetailVo> details) {
        this.details = details;
    }

    /**
     * 将可能的null值设置为默认值
     */
    public void setNullPropsToDefault() {
        if (getPurchaseOrderCode() == null) {
            setPurchaseOrderCode("");
        }
        if (getPurchaseQuantity() == null) {
            setPurchaseQuantity(0l);
        }
        if (getTotalDetailFee() == null) {
            setTotalDetailFee(0d);
        }
        if (getRemark() == null) {
            setRemark("");
        }
        if (getQuantity() == null) {
            setQuantity(0L);
        }
        if (getSupplierId() == null) {
            setSupplierId(0L);
        }
        if (getSupplierName() == null) {
            setSupplierName("");
        }
        if (getWarehouseId() == null) {
            setWarehouseId(0L);
        }
        if (getWarehouseName() == null) {
            setWarehouseName("");
        }
        if (getWaitShelveQuantity() == null) {
            setWaitShelveQuantity(0L);
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPurchaseOrderCode() {
        return purchaseOrderCode;
    }

    public void setPurchaseOrderCode(String purchaseOrderCode) {
        this.purchaseOrderCode = purchaseOrderCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public Double getTotalDetailFee() {
        return totalDetailFee;
    }

    public void setTotalDetailFee(Double totalDetailFee) {
        this.totalDetailFee = totalDetailFee;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getShipperId() {
        return shipperId;
    }

    public void setShipperId(String shipperId) {
        this.shipperId = shipperId;
    }

    public String getShipperName() {
        return shipperName;
    }

    public void setShipperName(String shipperName) {
        this.shipperName = shipperName;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Long getPurchaseQuantity() {
        return purchaseQuantity;
    }

    public void setPurchaseQuantity(Long purchaseQuantity) {
        this.purchaseQuantity = purchaseQuantity;
    }


    public Long getPurchaseOrderId() {
        return purchaseOrderId;
    }

    public void setPurchaseOrderId(Long purchaseOrderId) {
        this.purchaseOrderId = purchaseOrderId;
    }

    public Long getWaitShelveQuantity() {
        return waitShelveQuantity;
    }

    public void setWaitShelveQuantity(Long waitShelveQuantity) {
        this.waitShelveQuantity = waitShelveQuantity;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public Double getCancelAmount() {
        return cancelAmount;
    }

    public void setCancelAmount(Double cancelAmount) {
        this.cancelAmount = cancelAmount;
    }

    public Integer getItems() {
        return items;
    }

    public void setItems(Integer items) {
        this.items = items;
    }

    public Long getCancelQuantity() {
        return cancelQuantity;
    }

    public void setCancelQuantity(Long cancelQuantity) {
        this.cancelQuantity = cancelQuantity;
    }

    public Long getGetGoodNum() {
        return getGoodNum;
    }

    public void setGetGoodNum(Long getGoodNum) {
        this.getGoodNum = getGoodNum;
    }

    public Long getGetBadNum() {
        return getBadNum;
    }

    public void setGetBadNum(Long getBadNum) {
        this.getBadNum = getBadNum;
    }

    public Long getShelvedQuantity() {
        return shelvedQuantity;
    }

    public void setShelvedQuantity(Long shelvedQuantity) {
        this.shelvedQuantity = shelvedQuantity;
    }

    public Long getAuditorId() {
        return auditorId;
    }

    public void setAuditorId(Long auditorId) {
        this.auditorId = auditorId;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditorDate() {
        return auditorDate;
    }

    public void setAuditorDate(Date auditorDate) {
        this.auditorDate = auditorDate;
    }

    public Long getFinisherId() {
        return finisherId;
    }

    public void setFinisherId(Long finisherId) {
        this.finisherId = finisherId;
    }

    public Date getFinished() {
        return finished;
    }

    public void setFinished(Date finished) {
        this.finished = finished;
    }

    public Date getShelveTime() {
        return shelveTime;
    }

    public void setShelveTime(Date shelveTime) {
        this.shelveTime = shelveTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<PurchaseItemMerge<WarehouseEntryDetailVo>> getMerges() {
        return merges;
    }

    public void setMerges(List<PurchaseItemMerge<WarehouseEntryDetailVo>> merges) {
        this.merges = merges;
    }

    public Long getCreaterId() {
        return createrId;
    }

    public void setCreaterId(Long createrId) {
        this.createrId = createrId;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public Integer getNeedCheck() {
        return needCheck;
    }

    public void setNeedCheck(Integer needCheck) {
        this.needCheck = needCheck;
    }

    public Integer getHasCheck() {
        return hasCheck;
    }

    public void setHasCheck(Integer hasCheck) {
        this.hasCheck = hasCheck;
    }

    public Integer getQuantityCheck() {
        return quantityCheck;
    }

    public void setQuantityCheck(Integer quantityCheck) {
        this.quantityCheck = quantityCheck;
    }

    public Integer getPdaWeTaskStatus() {
        return pdaWeTaskStatus;
    }

    public void setPdaWeTaskStatus(Integer pdaWeTaskStatus) {
        this.pdaWeTaskStatus = pdaWeTaskStatus;
    }

    public Long getOutWarehouseId() {
        return outWarehouseId;
    }

    public void setOutWarehouseId(Long outWarehouseId) {
        this.outWarehouseId = outWarehouseId;
    }

    public String getOutWarehouseName() {
        return outWarehouseName;
    }

    public void setOutWarehouseName(String outWarehouseName) {
        this.outWarehouseName = outWarehouseName;
    }

    public String getBusyType() {
        return busyType;
    }

    public void setBusyType(String busyType) {
        this.busyType = busyType;
    }

    public Long getBusyId() {
        return busyId;
    }

    public void setBusyId(Long busyId) {
        this.busyId = busyId;
    }

    public String getBusyCode() {
        return busyCode;
    }

    public void setBusyCode(String busyCode) {
        this.busyCode = busyCode;
    }

    public String getBusyTypeDesc() {
        return busyTypeDesc;
    }

    public void setBusyTypeDesc(String busyTypeDesc) {
        this.busyTypeDesc = busyTypeDesc;
    }

    public String getFinisherName() {
        return finisherName;
    }

    public void setFinisherName(String finisherName) {
        this.finisherName = finisherName;
    }

    public void setGoodShelveFee(Double goodShelveFee) {
        this.goodShelveFee = goodShelveFee;
    }

    public void setBadShelveFee(Double badShelveFee) {
        this.badShelveFee = badShelveFee;
    }

    public Double getGoodShelveFee() {
        return goodShelveFee;
    }

    public Double getBadShelveFee() {
        return badShelveFee;
    }

    public Double getOthersFee() {
        return othersFee;
    }

    public void setOthersFee(Double othersFee) {
        this.othersFee = othersFee;
    }

    public Double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public Integer getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(Integer logisticsType) {
        this.logisticsType = logisticsType;
    }

    public Double getFreight() {
        return freight;
    }

    public void setFreight(Double freight) {
        this.freight = freight;
    }

    public Integer getFeeShareType() {
        return feeShareType;
    }

    public void setFeeShareType(Integer feeShareType) {
        this.feeShareType = feeShareType;
    }

    public Long getReceiveQuantity() {
        return quantity;
    }

    public Long getBusyOutId() {
        return busyOutId;
    }

    public void setBusyOutId(Long busyOutId) {
        this.busyOutId = busyOutId;
    }

    public String getBusyOutCode() {
        return busyOutCode;
    }

    public void setBusyOutCode(String busyOutCode) {
        this.busyOutCode = busyOutCode;
    }

    public Long getShortId() {
        return shortId;
    }

    public void setShortId(Long shortId) {
        this.shortId = shortId;
    }

    public String getAssociationShortId() {
        return associationShortId;
    }

    public void setAssociationShortId(String associationShortId) {
        this.associationShortId = associationShortId;
    }

    public Double getWholeSaleTotalAmount() {
        return wholeSaleTotalAmount;
    }

    public void setWholeSaleTotalAmount(Double wholeSaleTotalAmount) {
        this.wholeSaleTotalAmount = wholeSaleTotalAmount;
    }

    @Override
    public Long getReceiptId() {
        return getId();
    }

    @Override
    public String getReceiptNo() {
        return this.getCode();
    }

    @Override
    public List<Label> getLabels() {
        return labels;
    }

    @Override
    public void setLabels(List<Label> labels) {
        this.labels = labels;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Date getBillDate() {
        if (billDate == null) {
            return finished;
        }
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }


    public String getOperateStatus() {
        return operateStatus;
    }

    public void setOperateStatus(String operateStatus) {
        this.operateStatus = operateStatus;
    }

    public Integer getInLocation() {
        return inLocation;
    }

    public void setInLocation(Integer inLocation) {
        this.inLocation = inLocation;
    }

    public String getCustomType() {
        return customType;
    }

    public void setCustomType(String customType) {
        this.customType = customType;
    }

    public Integer getAdjustType() {
        return adjustType;
    }

    public void setAdjustType(Integer adjustType) {
        this.adjustType = adjustType;
    }
    @Override
    public Integer getReceiptType() {
        return ReceiptType.WAREHOUSE_ENTRY.getType();
    }

    public String getPurchaser() {
        return purchaser;
    }

    public void setPurchaser(String purchaser) {
        this.purchaser = purchaser;
    }

    public String getOuterCode() {
        return outerCode;
    }

    public void setOuterCode(String outerCode) {
        this.outerCode = outerCode;
    }

    public Integer getWeSourceKey() {
        return weSourceKey;
    }

    public void setWeSourceKey(Integer weSourceKey) {
        this.weSourceKey = weSourceKey;
    }

    public String getWeSourceId() {
        return weSourceId;
    }

    public void setWeSourceId(String weSourceId) {
        this.weSourceId = weSourceId;
    }

    public void setReceiveQuantity(Long receiveQuantity) {
        this.receiveQuantity = receiveQuantity;
    }

    public void setQuantity2NotFinish(Long quantity2NotFinish) {
        this.quantity2NotFinish = quantity2NotFinish;
    }

    public void setQuantity2Finished(Long quantity2Finished) {
        this.quantity2Finished = quantity2Finished;
    }

    public Integer getIsFastReceive() {
        return isFastReceive;
    }

    public void setIsFastReceive(Integer isFastReceive) {
        this.isFastReceive = isFastReceive;
    }

    public void setTotalDetailFee2NotFinish(Double totalDetailFee2NotFinish) {
        this.totalDetailFee2NotFinish = totalDetailFee2NotFinish;
    }

    public void setTotalDetailFee2Finished(Double totalDetailFee2Finished) {
        this.totalDetailFee2Finished = totalDetailFee2Finished;
    }

    public Long getQuantity2NotFinish() {
        return quantity2NotFinish;
    }

    public Long getQuantity2Finished() {
        return quantity2Finished;
    }

    public Double getTotalDetailFee2NotFinish() {
        return totalDetailFee2NotFinish;
    }

    public Double getTotalDetailFee2Finished() {
        return totalDetailFee2Finished;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Date getCanceled() {
        return canceled;
    }

    public void setCanceled(Date canceled) {
        this.canceled = canceled;
    }

    public Date getPurchaseOrderCreated() {
        return purchaseOrderCreated;
    }

    public void setPurchaseOrderCreated(Date purchaseOrderCreated) {
        this.purchaseOrderCreated = purchaseOrderCreated;
    }

    public Date getPurchaseOrderFinishDate() {
        return purchaseOrderFinishDate;
    }

    public void setPurchaseOrderFinishDate(Date purchaseOrderFinishDate) {
        this.purchaseOrderFinishDate = purchaseOrderFinishDate;
    }

    public Integer getTotalDetailCount() {
        return totalDetailCount;
    }

    public void setTotalDetailCount(Integer totalDetailCount) {
        this.totalDetailCount = totalDetailCount;
    }

    public String getProofUrl() {
        return proofUrl;
    }

    public void setProofUrl(String proofUrl) {
        this.proofUrl = proofUrl;
    }

    public String getProofName() {
        return proofName;
    }

    public void setProofName(String proofName) {
        this.proofName = proofName;
    }
    public Double getSaleTotalAmount() {
        return saleTotalAmount;
    }

    public void setSaleTotalAmount(Double saleTotalAmount) {
        this.saleTotalAmount = saleTotalAmount;
    }
    public Integer getIsPreIn() {
        return isPreIn;
    }

    public void setIsPreIn(Integer isPreIn) {
        this.isPreIn = isPreIn;
    }


    public Integer getHasShelve() {
        return hasShelve;
    }
    public String getOperateStatusName() {
        return operateStatusName;
    }

    public void setHasShelve(Integer hasShelve) {
        this.hasShelve = hasShelve;
    }

    public String getProofType() {
        return proofType;
    }

    public void setProofType(String proofType) {
        this.proofType = proofType;
    }

    /**
     * proofUrl、proofName   ---->   proofInfoList
     * @return
     */
    public List<ProofInfo> getProofInfoList() {
        if (CollectionUtils.isEmpty(proofInfoList) && StringUtils.isNotEmpty(proofUrl) && StringUtils.isNotEmpty(proofName)) {
            List<ProofInfo> addProofInfoList = Lists.newArrayList();
            if (proofUrl.contains(ProofInfo.SEPARATOR_BEFORE) && proofName.contains(ProofInfo.SEPARATOR_BEFORE)) {
                String[] proofUrlList = proofUrl.split(ProofInfo.SEPARATOR_AFTER);
                String[] proofNameList = proofName.split(ProofInfo.SEPARATOR_AFTER);
                for (int i = 0; i < proofUrlList.length; i++) {
                    addProofInfoList.add(new ProofInfo(proofUrlList[i], proofNameList[i], null));
                }
            } else {
                addProofInfoList.add(new ProofInfo(proofUrl, proofName, null));
            }
            this.proofInfoList = addProofInfoList;
            return proofInfoList;
        }
        return proofInfoList;
    }

    public void setProofInfoList(List<ProofInfo> proofInfoList) {
        this.proofInfoList = proofInfoList;
    }

    public Double getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Double totalFee) {
        this.totalFee = totalFee;
    }

    public Long getPurchaseReturnId() {
        return purchaseReturnId;
    }

    public void setPurchaseReturnId(Long purchaseReturnId) {
        this.purchaseReturnId = purchaseReturnId;
    }
    public void setOperateStatusName(String operateStatusName) {
        this.operateStatusName = operateStatusName;
    }

    public String getOperateStatusNameByStatus(String OperateStatus){
        switch (OperateStatus) {
            case WarehouseEntry.STATUS_WAIT_FINANCE:
                return "待财审";
            case WarehouseEntry.STATUS_FINANCED:
                return "已财审";
            default:
                return "";
        }
    }

    public List<WeItemGoodsSection> getWeItemGoodsSections() {
        return weItemGoodsSections;
    }

    public void setWeItemGoodsSections(List<WeItemGoodsSection> weItemGoodsSections) {
        this.weItemGoodsSections = weItemGoodsSections;
    }

    public Integer getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(Integer warehouseType) {
        this.warehouseType = warehouseType;
    }

    public List<WarehouseEntryWaveCodeDetailVo> getWaveCodedetails() {
        return waveCodedetails;
    }

    public void setWaveCodedetails(List<WarehouseEntryWaveCodeDetailVo> waveCodedetails) {
        this.waveCodedetails = waveCodedetails;
    }

    public Integer getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(Integer printStatus) {
        this.printStatus = printStatus;
    }

    public String getPrintStatusStr() {
        if (printStatus != null){
            return printStatus == 0 ? "未打印":"已打印";
        }
        return "未打印";
    }

    public void setPrintStatusStr(String printStatusStr) {
        this.printStatusStr = printStatusStr;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    /**
     * 获取合同备注
     * @return 合同备注信息
     */
    public String getContractRemark() {
        return contractRemark;
    }

    /**
     * 设置合同备注
     * @param contractRemark 合同备注
     */
    public void setContractRemark(String contractRemark) {
        this.contractRemark = contractRemark;
    }

    public Integer getSuitStatus() {
        return suitStatus;
    }

    public void setSuitStatus(Integer suitStatus) {
        this.suitStatus = suitStatus;
    }

    public Double getTotalLaborCostAmount() {
        return totalLaborCostAmount;
    }

    public void setTotalLaborCostAmount(Double totalLaborCostAmount) {
        this.totalLaborCostAmount = totalLaborCostAmount;
    }

    public Double getTotalReceiveWeight() {
        return totalReceiveWeight;
    }

    public void setTotalReceiveWeight(Double totalReceiveWeight) {
        this.totalReceiveWeight = totalReceiveWeight;
    }

    public String getExternalSourceId() {
        return externalSourceId;
    }

    public void setExternalSourceId(String externalSourceId) {
        this.externalSourceId = externalSourceId;
    }

    public Date getFinancedDate() {
        return financedDate;
    }

    public void setFinancedDate(Date financedDate) {
        this.financedDate = financedDate;
    }

    public List<String> getPlatformOrderIdList() {
        return platformOrderIdList;
    }

    public void setPlatformOrderIdList(List<String> platformOrderIdList) {
        this.platformOrderIdList = platformOrderIdList;
    }

    public Long getPreinOrderId() {
        return preinOrderId;
    }

    public void setPreinOrderId(Long preinOrderId) {
        this.preinOrderId = preinOrderId;
    }

    public String getPreinOrderCode() {
        return preinOrderCode;
    }

    public void setPreinOrderCode(String preinOrderCode) {
        this.preinOrderCode = preinOrderCode;
    }
}
package com.raycloud.dmj.domain.caigou;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.raycloud.dmj.data.domain.WarehouseSaleNumDetail;
import com.raycloud.dmj.domain.aware.ItemPriceAware;
import com.raycloud.dmj.domain.aware.ItemStockAware;
import com.raycloud.dmj.domain.aware.ItemSupplierAware;
import com.raycloud.dmj.domain.aware.ItemSysIdAware;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemStockSimple;
import com.raycloud.dmj.domain.item.ItemSupplierBridge;
import com.raycloud.dmj.domain.item.SuiteSingle;
import com.raycloud.dmj.domain.item.tag.ItemTag;
import com.raycloud.dmj.domain.sku.DmjSku;

import com.raycloud.dmj.domain.stock.StockLabel;
import com.raycloud.dmj.domain.stock.process.vo.ItemUnitVO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by weixin on 2017/4/24.
 */
public class ItemModelFull implements Serializable, ItemSupplierAware, ItemStockAware, ItemSysIdAware, ItemPriceAware {
    private static final long serialVersionUID = 5631506482209740677L;
    private Long userId;

    private Long id;

    private Integer status;

    private String shortTitle;

    private Double weight;

    private String barcode;

    private Date created;

    private Date modified;

    private String itemPicPath;

    private String picPath;

    private Integer enableStatus = 1;

    private String title;

    private String itemOuterId;

    private String outerId;

    private String cidName;
    /**
     * 生产日期
     */
    private Date productionDate;

    private Long num;

    private Long associateId;

    private Integer alarm;

    private String itemRemark;

    private String type;

    private Integer typeTag;

    /**
     * 套件状态 0:非套件  1：套件 2:加工
     */
    private Integer suitStatus;

    private String catId;

    private Double salesMoney;

    private Double salesCount;

    private String sellerCids;

    private String brand;

    private String unit;

    private Double purchasePrice;

    /**
     * 新建采购/采退单时，获取商品历史成本价用
     */
    private Double historyPrice;

    /**
     * 新建采退单时，获取移动加权成本价用
     */
    private Double weightAvgPrice;

    /**
     * 若用户配置勾选了供销价，获取供销价
     */
    private Double distributionPrice;

    private Double sellingPrice;

    /**
     * 批发价
     */
    private Double wholesalePrice;
    /**
     * 重复字段，与sellingPrice，在采购v1/v2中保持统一
     */
    private Double saleAmount;

    /**
     * 市场价
     */
    private Double marketPrice;

    private Long availableInStock;

    /**
     * 次品数
     */
    private Long defectiveStock;

    private Double priceInStock;

    private Double totalPriceInStock;

    private String place;

    private Integer age;

    private Long sysItemId;

    private Long sysSkuId;

    private String itemKey;

    private List<SuiteSingle> suiteSingleList;

    private List<SkuModelFull> skus = Lists.newLinkedList();

    private List<ItemSupplierBridge> itemSupplierBridgeList;

    private Double salePrice;

    /**
     * 根据最低进价选择最优供应商
     */
    private ItemSupplierBridge bestItemSupplierBridge;

    /**
     * 供应商名称组合，逗号分隔
     * @return
     */
    private String supplierNames;

    /**
     * 供应商ID
     */
    private List<Long> supplierIds;


    private Long quantity;

    private Long purchaseQuantity;

    private Long receiveQuantity;

    /**
     * 最大允许超收数
     */
    private Long overReceiveQuantity;

    private Long leftQuantity;

    /**
     * 采购链接
     */
    private String caigouUrl;

    /**
     * 是否开启批次设置  0-否 1-是
     */
    private Integer hasBatch;

    // 是否开启生产日期管理设置 0-否 1-是
    private Integer hasProduct;

    /**
     * 高亮的字段信息
     */
    private Map<String, Object> highlights;

    /**
     * 库存
     */
    private List<ItemStockSimple> stocks;

    /**
     * 库存类型类型
     */
    private Integer stockType;

    /**
     * 良次品
     * 只有有货位的情况下，才会有该值
     */
    private Boolean qualityType;

    /**
     * 成分
     */
    private String component;

    /**
     * 记录
     * @return
     */
    private String record;

    private String skuRecord;

    private String definedJson;

    /**
     * 自定义成分
     */
    private String cusComponent;

    private Boolean isGroup;

    private Boolean isProcess;

    private Integer boxNum;

    private List<StockLabel> stockLabels;

    /**
     * 商品标签
     */
    private List<ItemTag> itemTags;

    /**
     * 折扣率
     */
    private Double discountRate;

    /**
     * 成本价
     */
    private Double discountPrice;

    /**
     * 采退单明细信息
     */
    private PurchaseReturnDetail purchaseReturnDetail;

    /**
     * 收货单明细
     */
    private WarehouseEntryDetail warehouseEntryDetail;

    /**
     * 良品已收数
     */
    private Long goodReceiveNum;

    /**
     * 次品已收数
     */
    private Long badReceiveNum;

    private Double weReceiveAmount;
    /**
     * 0-弱唯一码
     * 1-强唯一码
     */
    private Integer uniqueCodeType;
    private Integer activeStatus;

    public Integer getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Integer activeStatus) {
        this.activeStatus = activeStatus;
    }
    private String itemCatName;

    private String itemCategoryNames;

    /**
     * 推荐货位
     */
    private String recommendGoodsSections;

    /**
     * 货位编码
     */
    private String goodsSectionCode;

    /**
     * 质量
     */
    private Boolean quality;

    /**
     * 采购在途数
     */
    private Long purchaseOnWayStock;

    /**
     * 采购单ids
     */
    private String purchaseOrderIds;

    public String getRecommendGoodsSections() {
        return recommendGoodsSections;
    }

    public void setRecommendGoodsSections(String recommendGoodsSections) {
        this.recommendGoodsSections = recommendGoodsSections;
    }

    /**
     * 体积 长 * 宽 * 高
     */
    private Double volume;

    /**
     * 是否自动计算 0-非自动计算 1-自动计算
     */
    private Integer isSysVolume;

    /**
     * 计价方式 0、按克 1、按件
     */
    private Integer calculationMode;

    /**
     * 采购单维护的工费(供应商+sku纬度)
     * 此字段区别于商品供应商维度的工费 仅用于值传递 请慎用！
     */
    private Double purchaseLaborCost;

    /**
     * 采退收货在途数
     */
    private Long prWeOnWayQuantity;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private Date productTime;

    /**
     *  1688映射状态
     */
    private String oneSixEightEightMapStatus;

    private Boolean canDistributable;

    /**
     * 货主
     */
    private String shipper;

    /**
     * 货主id
     */
    private String shipperId;

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Date getProductTime() {
        return productTime;
    }

    public void setProductTime(Date productTime) {
        this.productTime = productTime;
    }
/**
     * 商品库存单位对象
     */
    private ItemUnitVO itemUnitVO;

    /**
     * 单位名称
     */
    private String assistUnit;

    /**
     * 保质期
     */
    private Integer periodCast;

    public Integer getPeriodCast() {
        return periodCast;
    }

    public void setPeriodCast(Integer periodCast) {
        this.periodCast = periodCast;
    }

    /**
     * 单位ID
     */
    private Long assistUnitId;

    /**
     * 换算系数
     */
    private Double conversionFactor;

    /**
     * 是否是虚拟商品，是：1，否：0
     */
    private Integer isVirtual;


    public Boolean getQuality() {
        return quality;
    }

    public void setQuality(Boolean quality) {
        this.quality = quality;
    }

    public String getGoodsSectionCode() {
        return goodsSectionCode;
    }

    public void setGoodsSectionCode(String goodsSectionCode) {
        this.goodsSectionCode = goodsSectionCode;
    }

    public String getItemCatName() {
        return itemCatName;
    }

    public void setItemCatName(String itemCatName) {
        this.itemCatName = itemCatName;
    }

    public String getItemCategoryNames() {
        return itemCategoryNames;
    }

    public void setItemCategoryNames(String itemCategoryNames) {
        this.itemCategoryNames = itemCategoryNames;
    }

    public Integer getBoxNum() {
        return boxNum;
    }

    public void setBoxNum(Integer boxNum) {
        this.boxNum = boxNum;
    }

    private List<WarehouseSaleNumDetail> saleNumDetails;

    public List<WarehouseSaleNumDetail> getSaleNumDetails() {
        return saleNumDetails;
    }

    public void setSaleNumDetails(List<WarehouseSaleNumDetail> saleNumDetails) {
        this.saleNumDetails = saleNumDetails;
    }

    public String getSupplierNames() {
        return supplierNames;
    }

    public void setSupplierNames(String supplierNames) {
        this.supplierNames = supplierNames;
    }

    public List<Long> getSupplierIds() {
        return supplierIds;
    }

    public void setSupplierIds(List<Long> supplierIds) {
        this.supplierIds = supplierIds;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getPicPath() {
        return picPath;
    }

    public void setPicPath(String picPath) {
        this.picPath = picPath;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getOuterId() {
        return outerId;
    }

    public ItemModelFull setOuterId(String outerId) {
        this.outerId = outerId;
        return this;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Long getNum() {
        return num;
    }

    public void setNum(Long num) {
        this.num = num;
    }

    public Long getAssociateId() {
        return associateId;
    }

    public void setAssociateId(Long associateId) {
        this.associateId = associateId;
    }

    public Integer getAlarm() {
        return alarm;
    }

    public void setAlarm(Integer alarm) {
        this.alarm = alarm;
    }

    public String getItemRemark() {
        return itemRemark;
    }

    public void setItemRemark(String itemRemark) {
        this.itemRemark = itemRemark;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCatId() {
        return catId;
    }

    public void setCatId(String catId) {
        this.catId = catId;
    }

    public Double getSalesMoney() {
        return salesMoney;
    }

    public void setSalesMoney(Double salesMoney) {
        this.salesMoney = salesMoney;
    }

    public Double getSalesCount() {
        return salesCount;
    }

    public void setSalesCount(Double salesCount) {
        this.salesCount = salesCount;
    }

    public String getSellerCids() {
        return sellerCids;
    }

    public void setSellerCids(String sellerCids) {
        this.sellerCids = sellerCids;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(Double purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public Double getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(Double sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public Double getWholesalePrice() {
        return wholesalePrice;
    }

    public void setWholesalePrice(Double wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    public Double getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(Double saleAmount) {
        this.saleAmount = saleAmount;
    }

    public Double getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(Double marketPrice) {
        this.marketPrice = marketPrice;
    }

    public Long getAvailableInStock() {
        return availableInStock;
    }

    public void setAvailableInStock(Long availableInStock) {
        this.availableInStock = availableInStock;
    }

    public Double getPriceInStock() {
        return priceInStock;
    }

    public void setPriceInStock(Double priceInStock) {
        this.priceInStock = priceInStock;
    }

    public Double getTotalPriceInStock() {
        return totalPriceInStock;
    }

    public void setTotalPriceInStock(Double totalPriceInStock) {
        this.totalPriceInStock = totalPriceInStock;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    @Override
    public Long getSysItemId() {
        return sysItemId;
    }

    @Override
    public void setSysItemId(Long sysItemId) {
        this.sysItemId = sysItemId;
    }

    @Override
    public Long getSysSkuId() {
        return sysSkuId;
    }

    @Override
    public void setSysSkuId(Long sysSkuId) {
        this.sysSkuId = sysSkuId;
    }

    public List<SuiteSingle> getSuiteSingleList() {
        return suiteSingleList;
    }

    public void setSuiteSingleList(List<SuiteSingle> suiteSingleList) {
        this.suiteSingleList = suiteSingleList;
    }

    public List<SkuModelFull> getSkus() {
        return skus;
    }
    public void setSkus(List<SkuModelFull> skus) {
        this.skus = skus;
    }

    @Override
    public List<ItemSupplierBridge> getItemSupplierBridgeList() {
        return itemSupplierBridgeList;
    }

    @Override
    public void setItemSupplierBridgeList(List<ItemSupplierBridge> itemSupplierBridgeList) {
        this.itemSupplierBridgeList = itemSupplierBridgeList;
    }

    public Double getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(Double salePrice) {
        this.salePrice = salePrice;
    }

    @Override
    public ItemSupplierBridge getBestItemSupplierBridge() {
        return bestItemSupplierBridge;
    }

    @Override
    public void setBestItemSupplierBridge(ItemSupplierBridge bestItemSupplierBridge) {
        this.bestItemSupplierBridge = bestItemSupplierBridge;
    }

    public String getItemOuterId() {
        return itemOuterId;
    }

    public void setItemOuterId(String itemOuterId) {
        this.itemOuterId = itemOuterId;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public Long getPurchaseQuantity() {
        return purchaseQuantity;
    }

    public void setPurchaseQuantity(Long purchaseQuantity) {
        this.purchaseQuantity = purchaseQuantity;
    }

    public Long getLeftQuantity() {
        return leftQuantity;
    }

    public void setLeftQuantity(Long leftQuantity) {
        this.leftQuantity = leftQuantity;
    }

    public String getItemPicPath() {
        return itemPicPath;
    }

    public void setItemPicPath(String itemPicPath) {
        this.itemPicPath = itemPicPath;
    }

    public Integer getSuitStatus() {
        return suitStatus;
    }

    public void setSuitStatus(Integer suitStatus) {
        this.suitStatus = suitStatus;
    }

    public String getItemKey() {
        return sysItemId + "_0";
    }

    public void setItemKey(String itemKey) {
        this.itemKey = itemKey;
    }

    public String getCaigouUrl() {
        return caigouUrl;
    }

    public void setCaigouUrl(String caigouUrl) {
        this.caigouUrl = caigouUrl;
    }

    public Integer getHasBatch() {
        return hasBatch;
    }

    public void setHasBatch(Integer hasBatch) {
        this.hasBatch = hasBatch;
    }

    public Integer getHasProduct() {
        return hasProduct;
    }

    public void setHasProduct(Integer hasProduct) {
        this.hasProduct = hasProduct;
    }

    public Integer getTypeTag() {
        return typeTag;
    }

    public void setTypeTag(Integer typeTag) {
        this.typeTag = typeTag;
    }

    public Map<String, Object> getHighlights() {
        return highlights;
    }

    public void setHighlights(Map<String, Object> highlights) {
        this.highlights = highlights;
    }

    @Override
    public List<ItemStockSimple> getStocks() {
        return stocks;
    }

    @Override
    public void setStocks(List<ItemStockSimple> stocks) {
        this.stocks = stocks;
    }

    @Override
    public Integer getStockType() {
        return stockType;
    }

    @Override
    public void setStockType(Integer stockType) {
        this.stockType = stockType;
    }

    public Boolean getQualityType() {
        return qualityType;
    }

    public void setQualityType(Boolean qualityType) {
        this.qualityType = qualityType;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getRecord() {
        return record;
    }

    public void setRecord(String record) {
        this.record = record;
    }

    public String getSkuRecord() {
        return skuRecord;
    }

    public void setSkuRecord(String skuRecord) {
        this.skuRecord = skuRecord;
    }

    public String getDefinedJson() {
        return definedJson;
    }

    public void setDefinedJson(String definedJson) {
        this.definedJson = definedJson;
    }

    public Long getOverReceiveQuantity() {
        return overReceiveQuantity;
    }

    public void setOverReceiveQuantity(Long overReceiveQuantity) {
        this.overReceiveQuantity = overReceiveQuantity;
    }

    public String getCusComponent() {
        if(StringUtils.isNotEmpty(definedJson)) {
            List<Map<String, String>> definedList = JSON.parseObject(definedJson, new TypeReference<List<Map<String, String>>>() {});
            for (Map<String, String> stringStringMap : definedList) {
                String key = MapUtils.getString(stringStringMap, "definedKey");
                if(StringUtils.isNotEmpty(key) && "成份".equals(key)) {
                    return MapUtils.getString(stringStringMap, "definedValue");
                }
            }
        }
        return "";
    }

    public Boolean getGroup() {
        return isGroup;
    }

    public void setGroup(Boolean group) {
        isGroup = group;
    }

    public Boolean getProcess() {
        return isProcess;
    }

    public void setProcess(Boolean process) {
        isProcess = process;
    }
    
    public  String buildItemKey(DmjItem item) {
        return sysItemId + "_" + (item instanceof DmjSku ? ((DmjSku) item).getSysSkuId() : 0L);
    }

    public Long getDefectiveStock() {
        return defectiveStock;
    }

    public void setDefectiveStock(Long defectiveStock) {
        this.defectiveStock = defectiveStock;
    }

    public List<StockLabel> getStockLabels() {
        return stockLabels;
    }

    public void setStockLabels(List<StockLabel> stockLabels) {
        this.stockLabels = stockLabels;
    }

    public List<ItemTag> getItemTags() {
        return itemTags;
    }

    public void setItemTags(List<ItemTag> itemTags) {
        this.itemTags = itemTags;
    }

    public Long getReceiveQuantity() {
        return receiveQuantity;
    }

    public void setReceiveQuantity(Long receiveQuantity) {
        this.receiveQuantity = receiveQuantity;
    }

    public String getCidName() {
        return cidName;
    }

    public void setCidName(String cidName) {
        this.cidName = cidName;
    }

    public PurchaseReturnDetail getPurchaseReturnDetail() {
        return purchaseReturnDetail;
    }

    public void setPurchaseReturnDetail(PurchaseReturnDetail purchaseReturnDetail) {
        this.purchaseReturnDetail = purchaseReturnDetail;
    }

    public Double getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Double discountRate) {
        this.discountRate = discountRate;
    }

    public Double getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(Double discountPrice) {
        this.discountPrice = discountPrice;
    }

    public WarehouseEntryDetail getWarehouseEntryDetail() {
        return warehouseEntryDetail;
    }

    public void setWarehouseEntryDetail(WarehouseEntryDetail warehouseEntryDetail) {
        this.warehouseEntryDetail = warehouseEntryDetail;
    }
    public Long getGoodReceiveNum() {
        return goodReceiveNum;
    }

    public void setGoodReceiveNum(Long goodReceiveNum) {
        this.goodReceiveNum = goodReceiveNum;
    }

    public Long getBadReceiveNum() {
        return badReceiveNum;
    }

    public void setBadReceiveNum(Long badReceiveNum) {
        this.badReceiveNum = badReceiveNum;
    }

    public Integer getUniqueCodeType() {
        return uniqueCodeType;
    }

    public void setUniqueCodeType(Integer uniqueCodeType) {
        this.uniqueCodeType = uniqueCodeType;
    }

    public Double getWeReceiveAmount() {
        return weReceiveAmount;
    }

    public void setWeReceiveAmount(Double weReceiveAmount) {
        this.weReceiveAmount = weReceiveAmount;
    }

    public Long getPurchaseOnWayStock() {
        return purchaseOnWayStock;
    }

    public void setPurchaseOnWayStock(Long purchaseOnWayStock) {
        this.purchaseOnWayStock = purchaseOnWayStock;
    }

    public String getPurchaseOrderIds() {
        return purchaseOrderIds;
    }

    public void setPurchaseOrderIds(String purchaseOrderIds) {
        this.purchaseOrderIds = purchaseOrderIds;
    }

    @Override
    public Double getHistoryPrice() {
        return historyPrice;
    }

    @Override
    public void setHistoryPrice(Double historyPrice) {
        this.historyPrice = historyPrice;
    }

    @Override
    public Double getWeightAvgPrice() {
        return weightAvgPrice;
    }

    @Override
    public void setWeightAvgPrice(Double weightAvgPrice) {
        this.weightAvgPrice = weightAvgPrice;
    }

    public Double getDistributionPrice() {
        return distributionPrice;
    }

    @Override
    public void setDistributionPrice(Double distributionPrice) {
        this.distributionPrice = distributionPrice;
    }

    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public Integer getIsSysVolume() {
        return isSysVolume;
    }

    public void setIsSysVolume(Integer isSysVolume) {
        this.isSysVolume = isSysVolume;
    }

    public Integer getCalculationMode() {
        return calculationMode;
    }

    public void setCalculationMode(Integer calculationMode) {
        this.calculationMode = calculationMode;
    }

    public Double getPurchaseLaborCost() {
        return purchaseLaborCost;
    }

    public void setPurchaseLaborCost(Double purchaseLaborCost) {
        this.purchaseLaborCost = purchaseLaborCost;
    }

    public Long getPrWeOnWayQuantity() {
        return prWeOnWayQuantity;
    }

    public void setPrWeOnWayQuantity(Long prWeOnWayQuantity) {
        this.prWeOnWayQuantity = prWeOnWayQuantity;
    }

    @Override
    public Long getSupplierId() {
        return null;
    }

    public String getOneSixEightEightMapStatus() {
        return oneSixEightEightMapStatus;
    }

    public void setOneSixEightEightMapStatus(String oneSixEightEightMapStatus) {
        this.oneSixEightEightMapStatus = oneSixEightEightMapStatus;
    }

    public ItemUnitVO getItemUnitVO() {
        return itemUnitVO;
    }

    public void setItemUnitVO(ItemUnitVO itemUnitVO) {
        this.itemUnitVO = itemUnitVO;
    }

    public String getAssistUnit() {
        return assistUnit;
    }

    public void setAssistUnit(String assistUnit) {
        this.assistUnit = assistUnit;
    }

    public Long getAssistUnitId() {
        return assistUnitId;
    }

    public void setAssistUnitId(Long assistUnitId) {
        this.assistUnitId = assistUnitId;
    }

    public Double getConversionFactor() {
        return conversionFactor;
    }

    public void setConversionFactor(Double conversionFactor) {
        this.conversionFactor = conversionFactor;
    }

    public Boolean getCanDistributable() {
        return canDistributable;
    }

    public void setCanDistributable(Boolean canDistributable) {
        this.canDistributable = canDistributable;
    }

    public String getShipper() {
        return shipper;
    }

    public void setShipper(String shipper) {
        this.shipper = shipper;
    }

    public String getShipperId() {
        return shipperId;
    }

    public void setShipperId(String shipperId) {
        this.shipperId = shipperId;
    }

    public Integer getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(Integer isVirtual) {
        this.isVirtual = isVirtual;
    }

    public Integer isVirtual() {
        return isVirtual;
    }

    public void setVirtual(Integer virtual) {
        isVirtual = virtual;
    }

    @Override
    public String toString() {
        return "ItemModelFull{" +
                "userId=" + userId +
                ", id=" + id +
                ", status=" + status +
                ", shortTitle='" + shortTitle + '\'' +
                ", weight=" + weight +
                ", barcode='" + barcode + '\'' +
                ", created=" + created +
                ", modified=" + modified +
                ", itemPicPath='" + itemPicPath + '\'' +
                ", picPath='" + picPath + '\'' +
                ", enableStatus=" + enableStatus +
                ", title='" + title + '\'' +
                ", itemOuterId='" + itemOuterId + '\'' +
                ", outerId='" + outerId + '\'' +
                ", productionDate=" + productionDate +
                ", num=" + num +
                ", associateId=" + associateId +
                ", alarm=" + alarm +
                ", itemRemark='" + itemRemark + '\'' +
                ", type='" + type + '\'' +
                ", typeTag=" + typeTag +
                ", suitStatus=" + suitStatus +
                ", catId='" + catId + '\'' +
                ", salesMoney=" + salesMoney +
                ", salesCount=" + salesCount +
                ", sellerCids='" + sellerCids + '\'' +
                ", brand='" + brand + '\'' +
                ", unit='" + unit + '\'' +
                ", purchasePrice=" + purchasePrice +
                ", historyPrice=" + historyPrice +
                ", weightAvgPrice=" + weightAvgPrice +
                ", distributionPrice=" + distributionPrice +
                ", sellingPrice=" + sellingPrice +
                ", wholesalePrice=" + wholesalePrice +
                ", availableInStock=" + availableInStock +
                ", priceInStock=" + priceInStock +
                ", totalPriceInStock=" + totalPriceInStock +
                ", place='" + place + '\'' +
                ", age=" + age +
                ", sysItemId=" + sysItemId +
                ", sysSkuId=" + sysSkuId +
                ", itemKey='" + itemKey + '\'' +
                ", suiteSingleList=" + suiteSingleList +
                ", skus=" + skus +
                ", itemSupplierBridgeList=" + itemSupplierBridgeList +
                ", bestItemSupplierBridge=" + bestItemSupplierBridge +
                ", supplierNames='" + supplierNames + '\'' +
                ", quantity=" + quantity +
                ", purchaseQuantity=" + purchaseQuantity +
                ", leftQuantity=" + leftQuantity +
                ", caigouUrl='" + caigouUrl + '\'' +
                ", hasBatch=" + hasBatch +
                ", hasProduct=" + hasProduct +
                ", highlights=" + highlights +
                ", stocks=" + stocks +
                ", stockType=" + stockType +
                ", qualityType=" + qualityType +
                ", component='" + component + '\'' +
                ", definedJson='" + definedJson + '\'' +
                ", purchaseOrderIds=" + purchaseOrderIds +
                ", volume='" + volume + '\'' +
                ", isSysVolume='" + isSysVolume + '\'' +
                ", calculationMode='" + calculationMode + '\'' +
                ", purchaseLaborCost='" + purchaseLaborCost + '\'' +
                ", cusComponent='" + cusComponent + '\'' +
                ", oneSixEightEightMapStatus='" + oneSixEightEightMapStatus + '\'' +
                ", itemUnitVO=" + itemUnitVO +
                ", assistUnit='" + assistUnit + '\'' +
                ", assistUnitId=" + assistUnitId +
                ", conversionFactor=" + conversionFactor +
                ", saleNumDetails=" + saleNumDetails +
                ", canDistributable=" + canDistributable +
                '}';
    }
}

package com.raycloud.dmj.domain.trade.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.MDCKeys;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.trade.TradeFakeRule;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.common.FilterResult;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.MDC;

import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.enums.OrderModifyLogTypeEnum.*;

/**
 * @ClassName TradeUtils
 * @Description 订单工具类
 * <AUTHOR>
 * @Date 2024/1/29
 * @Version 1.0
 */
public abstract class TradeUtils {

    /**
     * 判断对应的订单状态是否为合单
     */
    public static boolean isMerge(Staff staff, Trade trade) {
        if (trade.getMergeSid() == null) {
//            Logs.warn(initBaseLog(staff) + String.format("mergeSid为空，合单判断不准,sid=%s", trade.getSid()));
            return false;
        }
        return trade.getMergeSid() > 0;
    }

    /**
     * 是否合单主单
     */
    public static boolean isMergeMain(Staff staff, Trade trade) {
        return isMerge(staff, trade) && trade.getSid() - trade.getMergeSid() == 0;
    }

    /**
     * 判断对应的订单状态是否为合单
     */
    public static boolean isSplit(Staff staff, Trade trade) {
        if (trade.getSplitSid() == null) {
            Logs.warn(initBaseLog(staff) + String.format("splitSid为空，拆单判断不准,sid=%s", trade.getSid()));
            return false;
        }
        return trade.getSplitSid() > 0;
    }

    /**
     * 是否拆单主单
     */
    public static boolean isSplitMain(Staff staff, Trade trade) {
        return isSplit(staff, trade) && trade.getSid() - trade.getSplitSid() == 0;
    }

    /**
     * 判断订单是否是刷单
     */
    public static FilterResult isFakeTrade(Staff staff, Trade trade, TradeFakeRule fakeRule) {
        if (fakeRule == null) {
            return FilterResult.builder().filter(false).build();
        }
        Set<String> sources = fakeRule.getSource();
        if (CollectionUtils.isEmpty(sources) || !sources.contains(trade.getSource())) {
            return FilterResult.builder().filter(false).build();
        }

        String receiverName = StringUtils.trim(trade.getReceiverName());
        if (StringUtils.isNotBlank(receiverName) && CollectionUtils.isNotEmpty(fakeRule.getReceiverNames()) && fakeRule.getReceiverNames().contains(receiverName)) {
            return FilterResult.builder().filter(true)
                    .msg(String.format("根据收件人判断为刷单，configReceiverNames=%s，receiverName=%s", fakeRule.getReceiverNames(), receiverName))
                    .build();
        }

        String sellerMemo = StringUtils.trim(trade.getSellerMemo());
        if (StringUtils.isNotBlank(sellerMemo) && CollectionUtils.isNotEmpty(fakeRule.getSellerMemos())) {
            for (String sellerMemoKey : fakeRule.getSellerMemos()) {
                if (sellerMemo.contains(sellerMemoKey)) {
                    return FilterResult.builder().filter(true)
                            .msg(String.format("根据卖家备注判断为刷单，configSellerMemo=%s，sellerMemo=%s", sellerMemoKey, sellerMemo))
                            .build();
                }
            }
        }

        String buyerMessage = StringUtils.trim(trade.getBuyerMessage());
        if (StringUtils.isNotBlank(buyerMessage) && CollectionUtils.isNotEmpty(fakeRule.getBuyerMessages())) {
            for (String buyerMessageKey : fakeRule.getBuyerMessages()) {
                if (buyerMessage.contains(buyerMessageKey)) {
                    return FilterResult.builder().filter(true)
                            .msg(String.format("根据买家留言判断为刷单，buyerMessageKey=%s，buyerMessage=%s", buyerMessageKey, buyerMessage))
                            .build();
                }
            }
        }

        List<Order> orders = com.raycloud.dmj.domain.trades.utils.TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            return FilterResult.builder().filter(false).build();
        }

        double minPayment = NumberUtils.str2Double(fakeRule.getOrderMinPayment());
        int maxNum = NumberUtils.nvlInteger(fakeRule.getOrderMaxNum(), 0);
        double divMinPayment = NumberUtils.str2Double(fakeRule.getOrderDivMinPayment());
        Set<String> picPaths = fakeRule.getOrderPicPaths(), titles = fakeRule.getOrderTitles();
        if (minPayment <= 0
                && maxNum <= 0
                && divMinPayment <= 0
                && CollectionUtils.isEmpty(picPaths)
                && CollectionUtils.isEmpty(titles)
        ) {
            return FilterResult.builder().filter(false).build();
        }
        int matchPaymentCount = 0, matchNumCount = 0, matchDivPaymentCount = 0, matchPicPathCount = 0, matchTitleCount = 0;
        for (Order order : orders) {
            double payment = NumberUtils.str2Double(order.getPayment(), 0.00D);
            if (minPayment > 0 && payment > 0 && minPayment >= payment) {
                matchPaymentCount++;
            }
            if (maxNum > 0 && maxNum <= order.getNum()) {
                matchNumCount++;
            }
            if (order.getNum() > 0) {
                double divPayment = NumberUtils.formatDouble(payment / order.getNum(), 5);
                if (divMinPayment > 0 && divPayment > 0 && divMinPayment >= divPayment) {
                    matchDivPaymentCount++;
                }
            }

            if (CollectionUtils.isNotEmpty(picPaths) && picPaths.contains(order.getPicPath())) {
                matchPicPathCount++;
            }
            if (CollectionUtils.isNotEmpty(titles) && titles.contains(order.getTitle())) {
                matchTitleCount++;
            }
        }
        if (matchPaymentCount == orders.size()) {
            return FilterResult.builder().filter(true)
                    .msg(String.format("根据订单商品金额判断为刷单，minPayment=%s,matchPaymentCount=%s", minPayment, matchPaymentCount))
                    .build();
        }
        if (matchNumCount == orders.size()) {
            return FilterResult.builder().filter(true)
                    .msg(String.format("根据订单商品数量判断为刷单，maxNum=%s,matchNumCount=%s", maxNum, matchNumCount))
                    .build();
        }
        if (matchDivPaymentCount == orders.size()) {
            return FilterResult.builder().filter(true)
                    .msg(String.format("根据订单商品实付金额除以数量判断为刷单，divMinPayment=%s,matchDivPaymentCount=%s", divMinPayment, matchDivPaymentCount))
                    .build();
        }
        if (matchPicPathCount == orders.size()) {
            return FilterResult.builder().filter(true)
                    .msg(String.format("根据订单商品图片链接判断为刷单，picPaths=%s,matchPicPathCount=%s", picPaths, matchPicPathCount))
                    .build();
        }
        // 存在任意配置的title
        if (matchTitleCount == orders.size()) {
            return FilterResult.builder().filter(true)
                    .msg(String.format("根据订单商品title判断为刷单，titles=%s,matchTitleCount=%s", titles, matchTitleCount))
                    .build();
        }
        return FilterResult.builder().filter(false).build();
    }


    /**
     * 判断订单是否平台单
     */
    public static boolean isPlatTrade(Staff staff, Trade trade) {
        if (StringUtils.isBlank(trade.getSource())) {
            Logs.warn(initBaseLog(staff) + String.format("判断订单是否是平台单，source为空,sid=%s", trade.getSid()));
            throw new IllegalArgumentException("判断订单是否是平台单，source为空");
        }
        if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            return true;
        }
        if (StringUtils.isNotBlank(trade.getSplitSource())) {
            return !CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource());
        }
        if (trade.getSplitSid() != null && trade.getSplitSid() > 0) {
            Logs.warn(initBaseLog(staff) + String.format("判断订单是否是平台单，splitSource为空,sid=%s,splitSid=%s", trade.getSid(), trade.getSplitSid()));
        }
        return false;
    }

    /**
     * 获取拆分订单的source
     *
     * @param splitTrades 所有的拆单
     */
    public static String getSplitSource(List<Trade> splitTrades) {
        for (Trade splitTrade : splitTrades) {
            if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(splitTrade.getSource())) {
                return splitTrade.getSource();
            }
        }
        return CommonConstants.PLAT_FORM_TYPE_SYS;
    }

    public static List<Trade> getMergeTrades(Staff staff, Trade
            trade, Map<Long, List<Trade>> mergeSidTradesMap) {
        return isMerge(staff, trade) && mergeSidTradesMap != null ? mergeSidTradesMap.get(trade.getMergeSid()) : null;
    }

    public static List<Trade> getMergeTrades(Staff staff, List<Trade> trades, Map<Long, List<Trade>> mergeSidTradesMap) {
        Map<Long, Trade> tradeMap = new HashMap<>();
        for (Trade trade : trades) {
            if (isMerge(staff, trade) && mergeSidTradesMap != null) {
                List<Trade> mergeTrades = mergeSidTradesMap.get(trade.getMergeSid());
                if (CollectionUtils.isEmpty(mergeTrades)) {
                    Logs.warn(initBaseLog(staff) + String.format("订单是合单，找不到隐藏,sid=%s,mergeSid=%s", trade.getSid(), trade.getMergeSid()));
                } else {
                    mergeTrades.forEach(mergeTrade -> tradeMap.put(mergeTrade.getSid(), mergeTrade));
                }
            }
            tradeMap.put(trade.getSid(), trade);
        }
        return new ArrayList<>(tradeMap.values());
    }


    public static List<Trade> getAllTrades(Staff staff, Trade trade, Map<Long, List<Trade>> mergeSidTradesMap) {
        ArrayList<Trade> trades = Lists.newArrayList(trade);
        if (!isMerge(staff, trade)) {
            return trades;
        }
        List<Trade> mergeTrades = Optional.ofNullable(mergeSidTradesMap).map(m -> m.get(trade.getMergeSid())).orElse(null);
        if (CollectionUtils.isEmpty(mergeTrades)) {
            Logs.warn(initBaseLog(staff) + String.format("订单是合单，找不到隐藏,sid=%s,mergeSid=%s, mergeSidTradesMapSize=%s", trade.getSid(), trade.getMergeSid(), Optional.ofNullable(mergeSidTradesMap).map(Map::size).orElse(null)));
            return trades;
        }
        for (Trade mergeTrade : mergeTrades) {
            if (trade.getSid() - mergeTrade.getSid() != 0) {
                trades.add(mergeTrade);
            }
        }
        return trades;
    }

    /**
     * 获取不能拆分的标签
     */
    public static TradeTag getNotSplitTag(Trade trade) {
        if (CollectionUtils.isNotEmpty(trade.getTags())) {
            for (TradeTag tag : trade.getTags()) {
                if (NumberUtils.isEquals(tag.getNotAllowSplit(), 1)) {
                    return tag;
                }
            }
        }
        return null;
    }


    public static Set<String> getTids(Staff staff, Trade trade, Map<Long, List<Trade>> mergeSidTradesMap) {
        Set<String> tids = Sets.newHashSet();
        tids.add(trade.getTid());
        if (isMerge(staff, trade)) {
            if (mergeSidTradesMap == null || !mergeSidTradesMap.containsKey(trade.getMergeSid())) {
                Logs.ifDebug(initBaseLog(staff) + String.format("获取合单隐藏单的tid找不到对应的隐藏单，mergeSid=%s", trade.getMergeSid()));
            } else {
                tids.addAll(mergeSidTradesMap.get(trade.getMergeSid()).stream().map(TradeBase::getTid).collect(Collectors.toList()));
            }
        }
        return tids;
    }

    public static List<Order> getOrders(Staff staff, Trade trade, Map<Long, List<Trade>> mergeSidTradesMap) {
        List<Trade> mergeTrades = isMerge(staff, trade) && mergeSidTradesMap != null ? mergeSidTradesMap.get(trade.getMergeSid()) : null;
        return getOrders(staff, trade, mergeTrades);
    }

    public static List<Order> getOrders(Staff staff, Trade trade, List<Trade> mergeTrades) {
        List<Order> orders = new ArrayList<>();
        for (Order order : com.raycloud.dmj.domain.trades.utils.TradeUtils.getOrders4Trade(trade)) {
            order.setPaymentDouble(NumberUtils.str2Double(order.getPayment()));
            orders.add(order);
        }
        if (isMerge(staff, trade)) {
            if (CollectionUtils.isEmpty(mergeTrades)) {
                Logs.ifDebug(initBaseLog(staff) + String.format("获取合单隐藏单的order找不到对应的隐藏单，mergeSid=%s", trade.getMergeSid()));
            } else {
                for (Trade mergeTrade : mergeTrades) {
                    if (mergeTrade.getSid() - trade.getSid() != 0) {
                        for (Order order : com.raycloud.dmj.domain.trades.utils.TradeUtils.getOrders4Trade(mergeTrade)) {
                            order.setPaymentDouble(NumberUtils.str2Double(order.getPayment()));
                            orders.add(order);
                        }
                    }
                }
            }
        }
        return orders;
    }

    /**
     * 获取一个订单真实的source：
     * 比如拆分的订单，需要知道对应的主单的source
     * 售后：
     */
    public static String getSource(Staff staff, Trade trade) {
        String source = null;
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && isSplit(staff, trade)) {
            source = trade.getSplitSource();
        }
        if (StringUtils.isBlank(source)) {
            source = trade.getSource();
        }
        return source;
    }

    /**
     * 生成日志模板
     */
    public static String initBaseLog(Staff staff) {
        return "[" + staff.getCompanyId() + "," + staff.getId() + "," + staff.getName() + "] ";
    }


    public static String buildSysItemKey(Staff staff, Order order) {
        return buildSysItemKey(staff, order.getItemSysId(), order.getSkuSysId());
    }

    public static String buildSysItemKey(Staff staff, Long sysItemId, Long sysSkuId) {
        return sysItemId + "_" + NumberUtils.negative2Zero(sysSkuId);
    }

    public static boolean getCoverWeight(TradeConfig tradeConfig) {
        String chatConfigs = tradeConfig.getChatConfigs();
        if (StringUtils.isNotBlank(chatConfigs)) {
            JSONObject jsonObject = JSONObject.parseObject(chatConfigs);
            if (jsonObject != null) {
                return TradeConstants.YES.equals(jsonObject.getString("isCoverWeight"));
            }
        }
        return false;
    }

    public static Long getClueId() {
        Object clueId = MDC.get(MDCKeys.CLUE_ID);
        if (clueId instanceof String) {
            return Long.valueOf((String) clueId);
        } else {
            return (Long) clueId;
        }
    }

}

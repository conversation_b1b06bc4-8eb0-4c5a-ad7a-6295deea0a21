package com.raycloud.dmj.domain.trade.label;

import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.tag.*;
import com.raycloud.dmj.domain.trade.common.TradeBusinessEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.tag.TradeTag.*;
import static com.raycloud.dmj.domain.trade.label.TradeLabelMatchKeyFrom.*;
import static com.raycloud.dmj.domain.trade.label.TradeLabelMatchOperator.*;
import static com.raycloud.dmj.domain.trade.label.TradeLabelPlatformEnum.*;

@Getter
public enum TradeSystemLabelEnum {

    /* 通用------------------------------------------------------------------------------------------------------------- */
    @TradeLabelProperties(initSupportManual = true)
    TAG_CANCEL_INSUFFICIENT(1000000000L, COMMON, "缺货发货", "1.订单缺货发货、缺货审核时，订单自动标记【缺货发货】标签<br/> 2.订单反审核、作废订单、撤销发货时，订单自动取消【缺货发货】标签"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_CANCEL_CONSIGN(1000000001L, COMMON, "撤销发货", "已发货订单撤销发货后，订单自动标记【撤销发货】标签"),
    TAG_MULTI_PACKS(1000000002L, COMMON, "一单多包", "订单使用一单多包打印了或者使用一单多包补打了，订单自动标记【一单多包】标签"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_TRADE_PAY_INCONSISTENT_AMOUNT(1000000003L, COMMON, "实付/实收不一致", "实付、实收已经出现不一致，订单自动标记【实付/实收不一致】标签"),
    @TradeLabelProperties(initSupportManual = true)
    @TradeLabelMatchCondition(key = "status", value = "XHS_10", operator = not, keyFrom = tradeField)
    @TradeLabelMatchCondition(key = "tid", value = "RP", operator = contain, keyFrom = tradeField)
    TAG_EXCHANGE(1000000005L, COMMON, "换货", "当售后工单创建换货订单时，自动给订单标记【换货】标签"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_REISSUE(1000000006L, COMMON, "补发", "当售后工单创建补发订单时，自动给订单标记【补发】标签"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_FORCE_PACK_SPLIT(1000000007L, COMMON, "强制验货拆分"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/tr42xzfcbgdskyk6")
    @TradeLabelMatchCondition(key = "homeDelivery", value = "1")
    TAG_1000000008(1000000008L, COMMON, "送货上门"),
    @TradeLabelProperties(couldManual = true, document = "https://gykj.yuque.com/entavv/xb9xi5/gw403qx1ye6okgu5")
    TAG_CLEARANCE_TRADE(1000000009L, COMMON, "清仓订单"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_IMPORT_DELIVER(1000000010L, COMMON, "导入发货", "导入成功后直接触发发货，并添加一个导入发货的标签"),
    @TradeLabelProperties(splitNotInherited = true)
    TAG_BEFORE_DELIVER_AFTER_SALE(1000000011L, COMMON, "发货前售后", "发货前的订单发生售后时，订单自动标记【发货前售后】标签"),
    @TradeLabelProperties(splitNotInherited = true)
    TAG_AFTER_DELIVER_AFTER_SALE(1000000012L, COMMON, "发货后售后", "发货后的订单发生售后时，订单自动标记【发货后售后】标签"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/dgnyi7twha2ger71")
    @TradeLabelMatchCondition(key = "delivery_schedule", value = "1")
    TAG_BATCH_DELIVERY(1000000014L, COMMON, "分批发货"),
    TAG_SALE_TRDE_PRE_ALLOCATE_GOODS(1000000015L, COMMON, "预配货", "批发收银预配货标签"),
    TAG_FAST_MOVING_ORDER(1000000016L, COMMON, "快销订单", "开启订单唯一码后缺货审核订单标记【快销订单】标签"),
    @TradeLabelProperties(initSupportManual = true, document = "https://gykj.yuque.com/entavv/xb9xi5/egac8d")
    TAG_OVERTIME(1000000017L, COMMON, "即将超时", "订单即将发货超时"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_ITEM_PRICE_EXCEPTION(1000000018L, COMMON, "单价异常", "订单商品价格出现异常时，商品折后价小于等于商品成本价时，标记【单价异常】标签"),
    @TradeLabelProperties(initSupportManual = true, document = "https://gykj.yuque.com/entavv/xb9xi5/mut0qo")
    TAG_PARTY3_TEMPLATE_CHANGED(1000000019L, COMMON, "三方仓换快递", "三方仓回传快递模板变更"),
    @TradeLabelMatchCondition(key = "takeType", value = "SELF")
    TAG_SELF_PICK(1000000020L, COMMON, "现场自提"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_TRADE_ITEM_REPLACE_EXCEPTION(1000000021L, COMMON, "商品搭配更换失败"),
    @TradeLabelProperties(couldManual = true)
    TAG_PRE_UPLOAD_CONSIGN(1000000022L, COMMON, "预发货"),
    @TradeLabelProperties(couldManual = true)
    TAG_CHANGE_ITEM(1000000023L, COMMON, "修改商品"),
    @TradeLabelProperties(couldManual = true)
    SUIT_TO_SINGLE(1000000024L, COMMON, "套件转单品"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_ORDER_NUM_EXCEPTION(1000000025L, COMMON, "数量异常", "根据关键字计算发货数量：得不到整数，自动标记为【数量异常】"),
    @TradeLabelProperties(couldManual = true)
    ITEM_REPLACE_SUCCESS(1000000026L, COMMON, "商品搭配替换成功"),
    @TradeLabelProperties(document = {"https://gykj.yuque.com/entavv/xb9xi5/yghfh7hr12ganyl7","https://gykj.yuque.com/entavv/xb9xi5/aabuov4ug9d13qu3"})
    TAG_TRADE_IN_STATE_SUBSIDIES(1000000027L, COMMON, "国家补贴"),
    @TradeLabelProperties(couldManual = true, document = "https://gykj.yuque.com/entavv/xb9xi5/gnc56lzv1ekem0rh")
    TAG_AUDIT_UNDO(1000000028L, COMMON, "重新审核"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/tnmkkugozf97zwaq#CCLJk")
    @TradeLabelMatchCondition(key = "gov_subsidy_3c", value = "1")
    TAG_STATE_SUBSIDY_3C(1000000029L, COMMON, "国补3C"),
    @TradeLabelProperties(initClose = true)
    TAG_OFFLINE_PAY(1000000030L, COMMON, "线下支付"),
//    @Deprecated
//    TAG_TB_EXPEDITED_SHIPMENTS(1000000031L, COMMON, "催发货(淘宝/天猫)", "买家催发货后，订单标记【催发货】标签"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/buiayuuo40mszpwz")
    TAG_EXPEDITED_SHIPMENTS(1000000032L, COMMON, "催发货", "统一后的催发货标签。消费者在平台对该单进行催单发货了，订单会自动标记【催发货】。后续不再根据平台区分标签，请商家朋友们直接使用统一后的标签，老标签将逐步下线。过渡期间，满足条件的订单会同时打上两个标签。"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/vklbwcyr9oqzcchd")
    TAG_RED_PACKET_PAY(1000000033L, COMMON, "红包支付"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/wyog1egk1af68ipn")
    @TradeLabelMatchCondition(key = "isConsolidateType", value = "1")
    TAG_1000000036(1000000036L, COMMON, "中转集运订单"),
    @TradeLabelProperties(document = {"https://gykj.yuque.com/entavv/xb9xi5/lc0gp087zfu3p81sSKIZy", "https://gykj.yuque.com/entavv/xb9xi5/vnqppilzomzozp4ySKIZy"})
    @TradeLabelMatchCondition(key = "o2oTrade", value = "1")
    TAG_1000000037(1000000037L, COMMON, "O2O订单"),
    @TradeLabelProperties(initClose = true, couldManual = true, document = {"https://gykj.yuque.com/entavv/xb9xi5/tnmkkugozf97zwaq"})
    @TradeLabelMatchCondition(key = "govSubsidyUnShare", value = "1")
    TAG_1000000040(1000000040L, COMMON, "未享受国补"),
    @TradeLabelProperties(initClose = true, document = "https://gykj.yuque.com/entavv/xb9xi5/eckt0b084mfrc19p")
    TAG_1000000041(1000000041L, COMMON, "指定物流"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/lmrxl5u5l61kl46q")
    @TradeLabelMatchCondition(key = "isConsult", value = "1")
    TAG_1000000042(1000000042L, COMMON, "协商发货"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/mq8k9kgwog6r66nn")
    TAG_1000000044(1000000044L, COMMON, "一单多包取号"),
    @TradeLabelProperties(couldManual = true, document = "https://gykj.yuque.com/entavv/xb9xi5/hfc1wlv8xgk0qd48?singleDoc#")
    @TradeLabelMatchCondition(key = "gov_vouchers", value = "1")
    TAG_1000000047(1000000047L, COMMON, "政府消费券补贴"),
    @TradeLabelProperties(couldManual = true, document = "https://gykj.yuque.com/entavv/xb9xi5/xcugfodl8frzt4e5?singleDoc#")
    @TradeLabelMatchCondition(key = "hasGreetingCard", value = "1", keyFrom = orderExt)
    TAG_1000000048(1000000048L, COMMON, "贺卡"),
    @TradeLabelProperties(couldManual = true, document = "https://gykj.yuque.com/entavv/xb9xi5/gxa4g17buxvcux6v?singleDoc#")
    @TradeLabelMatchCondition(key = "nextDayDelivery", value = "1")
    TAG_1000000053(1000000053L, COMMON, "次日达"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/wyog1egk1af68ipn")
    @TradeLabelMatchCondition(key = "consolidate_hmt", value = "1")
    TAG_1000000056(1000000056L, COMMON, "中转集运订单港澳台"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/wyog1egk1af68ipn")
    @TradeLabelMatchCondition(key = "consolidate_overseas", value = "1")
    TAG_1000000057(1000000057L, COMMON, "中转集运订单海外"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/zf3g9l/ndkp3taig9u4ipy2#Ino6S")
    TAG_YUN_ZHU_SHOU(1000000058L, COMMON, "云助手"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/dh6kf61cpxgbi8f0")
    @TradeLabelMatchCondition(key = "has_weipai_service", value = "1")
    TAG_1000000059(1000000059L, COMMON, "顺丰微派", "该订单已签署顺丰微派服务，请与物流服务商（如顺丰）确认获取现场拆封激活拍照的核验照片。"),
    @TradeLabelProperties(document = {"https://gykj.yuque.com/entavv/xb9xi5/gv89fltvhhdqhat1","https://gykj.yuque.com/entavv/xb9xi5/gk38uo18hg2awha0"})
    @TradeLabelMatchCondition(key = "isPresent", value = "1")
    TAG_WECHAT_VIDEO_GIFT_TRADE(1000000080L, COMMON, "礼物订单"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/bknpgwg3k84exu5d")
    @TradeLabelMatchCondition(key = "platform_cost_per_sale", value = "1")
    TAG_PLATFORM_DISTRIBUTION(1000000085L, COMMON, "平台分销"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_UNIQUE_OFF_SHELF(1000000098L, COMMON, "商品下架", "商品下架后，订单标记【商品下架】标签"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_OS_ACTIVITY(1000000099L, COMMON, "前N有礼", "订单参与前N有礼活动，自动给订单标记【前N有礼】标签"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_OS_ACTIVITY_GIFT(1000000100L, COMMON, "前N有礼赠品", "订单参与前N有礼活动，自动给赠品订单标记【前N有礼赠品】标签"),
    @TradeLabelProperties(initSupportManual = true)
    TAG_FAST_IN_FAST_OUT(1000000110L, COMMON, "即入即出", "仓储快销模式开启了即入即出功能后，商品入库时即匹配订单，若匹配上则订单打上即入即出标签"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/diy7cvg0z0buis9y")
    @TradeLabelMatchCondition(key = "subSource", value = "tb", keyFrom = tradeField)
    TAG_1000000130(1000000130L, COMMON, "淘宝分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "tm", keyFrom = tradeField)
    TAG_1000000131(1000000131L, COMMON, "天猫分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "fxg", keyFrom = tradeField)
    TAG_1000000132(1000000132L, COMMON, "抖音分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "kuaishou", keyFrom = tradeField)
    TAG_1000000133(1000000133L, COMMON, "快手分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "pdd", keyFrom = tradeField)
    TAG_1000000134(1000000134L, COMMON, "拼多多分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "xhs", keyFrom = tradeField)
    TAG_1000000135(1000000135L, COMMON, "小红书分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "jd", keyFrom = tradeField)
    TAG_1000000136(1000000136L, COMMON, "京东分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "wxsph", keyFrom = tradeField)
    TAG_1000000137(1000000137L, COMMON, "微信小店分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "duxd", keyFrom = tradeField)
    TAG_1000000138(1000000138L, COMMON, "度小店分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "tjb", keyFrom = tradeField)
    TAG_1000000139(1000000139L, COMMON, "淘特分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "yz", keyFrom = tradeField)
    TAG_1000000140(1000000140L, COMMON, "有赞分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "poison", keyFrom = tradeField)
    TAG_1000000141(1000000141L, COMMON, "得物分销订单"),
    @TradeLabelMatchCondition(key = "subSource", value = "fxg_gx", keyFrom = tradeField)
    TAG_1000000142(1000000142L, COMMON, "抖音供销分销订单"),
    TAG_SF_FREE_SHIPPING(1000000180L, COMMON, "顺丰包邮", "抖音：商家已选择本商品承诺顺丰包邮。为保障消费者体验，本商品请选择顺丰快递发货，否则会扣除10元货款，以无门槛优惠券的方式赔付给消费者。如遇顺丰不可达或消费者自行要求发其他快递，请保留相关信息截图，在抖店PC端后台-奖惩中心-违规管理处发起申诉。申诉通过后，平台会豁免相关扣罚。\n快手：快手顺丰包邮订单，若需使用非顺丰发货请到店铺后台操作发货。"),
    @TradeLabelMatchCondition(key = "lateDeliveryCompensation", value = "1")
    TAG_JD_WFP(1000000187L, COMMON, "晚发赔"),
    @TradeLabelProperties(couldManual = true)
    AFTER_SALE_CAN_CONSIGN(1000000210L, COMMON, "售后可直发", "售后开启匹配单sku的多件订单时，确认退货入仓时即匹配订单，若匹配上出单成功则订单打上【售后可直发】标签。"),
    @TradeLabelProperties(couldManual = true)
    TAG_HAS_CREATE_CAIGOU_ORDER(1000000300L, COMMON, "已生成采购单", "订单成功执行过按订单生成采购单"),
    @TradeLabelProperties(couldManual = true)
    TAG_PURCHASE_BASE_ON_SALES(1000000301L, COMMON, "以销定采", "订单成功执行过以销定采"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/td87q0xsgnlxfeu0")
    TAG_CROSS_BORDER_PRINT_WAVES(1000000400L, COMMON, "跨境印花波次", "订单生成跨境印花波次时,会自动标记该标签"),
    @TradeLabelProperties(splitNotInherited = true)
    SPLIT_THE_PARENT_ORDER(1000000503L, COMMON, "拆单子母单-母单"),
    @TradeLabelProperties(splitNotInherited = true)
    SPLIT_THE_CHILD_ORDER(1000000504L, COMMON, "拆单子母单-子单"),
    @TradeLabelProperties(splitNotInherited = true)
    SPLIT_THE_PARENT_ORDER_DIFF_NUM(1000000505L, COMMON, "拆单子母单-母单(不同平台单号)"),
    @TradeLabelProperties(splitNotInherited = true)
    SPLIT_THE_CHILD_ORDER_DIFF_NUM(1000000506L, COMMON, "拆单子母单-子单(不同平台单号)"),
    @TradeLabelProperties(couldManual = true)
    TAG_NEED_INVOICE(1000000701L, COMMON, "需要开票", "有发票抬头或者纳税人识别号的订单的标签"),
    @TradeLabelProperties(initClose = true, couldManual = true)
    TAG_ALREADY_INVOICE(1000000702L, COMMON, "已经开票", "已经新建发票的订单标签"),
    TAG_FX_FUND_NOT_ENOUGH(1000000786L, COMMON, "分销商资金不足"),
    TAG_FX_POSSIBLE_LOSS(1000000788L, COMMON, "可能亏损"),
    @TradeLabelProperties(couldManual = true)
    TAG_PARTY3_TMSCANCELED(1000000800L, COMMON, "物流拦截成功", "三方仓物流拦截成功"),
    @TradeLabelProperties(couldManual = true)
    TAG_PARTY3_WMSCANCELED(1000000801L, COMMON, "出仓拦截成功", "三方仓出仓拦截成功"),
    @TradeLabelProperties(couldManual = true)
    TAG_PARTY3_TMSCANCELFAILED(1000000802L, COMMON, "物流拦截失败", "三方仓物流拦截失败"),
    @TradeLabelProperties(couldManual = true)
    TAG_PARTY3_CANCELEDING(1000000803L, COMMON, "物流拦截中", "三方仓物流拦截中"),
    TAG_FX_FORCE_PUSH(1000000804L, COMMON, "分销强推"),
    TRANSIT_MERGE(1000000806L, COMMON, "中转合包订单"),
    TAG_UPDATE_TIMEOUT(1000000807L, COMMON, "手动修改订单承诺时间"),
    @TradeLabelMatchCondition(key = "subSource", value = "cainiao_zf", keyFrom = tradeField)
    CAINIAO_WAREHOUSE_ZF(1000000990L, COMMON, "菜鸟仓转自发"),
//    @SystemLabelMatchCondition(key = "subSource", value = "cainiao_warehouse", keyFrom = tradeField)
    CAINIAO_WAREHOUSE(1000000997L, COMMON, "菜鸟仓自流转"),
    @TradeLabelMatchCondition(key = "subSource", value = "fresh_half_day", keyFrom = tradeField)
    TAG_TB_SX_BRD(1000000999L, COMMON, "生鲜半日达"),
    @TradeLabelProperties(couldManual = true)
    TAG_TRADE_COPY(10000000211L, COMMON, "复制订单", "复制订单时添加的标签"),
    @TradeLabelProperties(couldManual = true)
    TAG_INVALID_CHANGE_ITEM(10000000295L, COMMON, "无效商品修改商品", "通过无效商品弹窗，批量修改商品成功的订单会打上该标签"),
    @TradeLabelProperties(couldManual = true, splitNotInherited = true)
    TAG_SYSTEM_AUTO_CREATE_ITEM(10000000299L, COMMON, "系统自动创建商品", "如果订单下载时包含系统自动创建的商品，则会增加【系统自动创建商品】的系统标签"),
    DISTRIBUTOR_URGING_SHIPMENT(10000001495L, COMMON, "分销商催发货", "分销商已催发货的订单，建议优先处理，防止分销商被处罚"),
    HAS_URGED_SUPPLIER_TO_SHIP(10000001497L, COMMON, "已催供销商发货", "您已催发货，供销商会在订单处理页面，筛选出【分销商催发货】的订单优先发货"),
    /* 拼多多------------------------------------------------------------------------------------------------------------- */
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "open_in_festival", value = "1")
    TAG_OPEN_IN_FESTIVAL(1000000105L, PDD, "不打烊", "只针对拼多多订单参与春节期间不打烊活动的订单"),
    TAG_DELAY_CONSIGN(1000000106L, PDD, "可延迟发货", "(1)受疫情等客观因素影响，快递可能无法正常配送；若该订单发货时间延迟，平台不做处罚。\n(2)当该订单收货区域解除上述影响时，解除当日订单将重启承诺发货时间倒计时，请您密切关注变化，按照承诺发货时间发货，否则将受平台处罚。"),
    TAG_NO_TRACE_DELIVERY(1000000107L, PDD, "无痕发货"),
    TAG_SAME_CITY_DISTRIBUTION(1000000108L, PDD, "同城配送"),
    TAG_HAS_SUBSIDY_POSTAGE(1000000109L, PDD, "补贴运费红包", "针对该订单的运费，平台会为您补贴广告红包，具体请至拼多多商家后台订单详情页查看"),
    TAG_HAS_SF_EXPRESS_SERVICE(1000000117L, PDD, "顺丰加价"),
    TAG_COMMUNITY_GROUP(1000000118L, PDD, "小区团购"),
    @TradeLabelMatchCondition(key = "shipHold", value = "1", keyFrom = tradeExtField)
    TAG_PDD_SHIP_HOLD(1000000126L, PDD, "暂停发货", "(1)该标签对应的订单，收件人信息将暂不给出，待收件地址支持配送后，再进行展示.\n(2)该订单因发货地疫情影响，暂不支持发货，当疫情限制解除时，订单将重启承诺发货时间倒计时并支持发货."),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "0")
    TAG_PDD_JY_HONGKONG(1000000200L, PDD, "香港集运(即将下线)"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "1")
    TAG_PDD_JY_XINJIANG(1000000201L, PDD, "新疆中转(即将下线)"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "2")
    TAG_PDD_JY_HSK(1000000203L, PDD, "哈萨克斯坦集运(即将下线)", "收件人地址及电话均为集运仓地址及联系电话，若您需要查看消费者信息，请您前往拼多多商家后台进行查看"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "3")
    TAG_PDD_JY_XIZANG(1000000204L, PDD, "西藏中转(即将下线)", "收件人地址及电话均为集运仓地址及联系电话，若您需要查看消费者信息，请您前往拼多多商家后台进行查看"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "duoduo_wholesale", value = "1")
    TAG_PINDUODUO_WHOLSALE(1000001100L, PDD, "多多批发"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "only_support_replace", value = "1")
    TAG_EXCHANGE_ONLY_NO_REPAIR(1000001101L, PDD, "只换不修"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "return_freight_payer", value = "1")
    TAG_FREE_RETURN_SHIPPING(1000001102L, PDD, "退货包运费"),
//    @Deprecated
//    @SystemLabelMatchCondition(key = "free_sf", value = "1")
//    TAG_PDD_SF_EXPRESS_FREE_SHIPPING(1000001103L, PDD, "顺丰包邮"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "support_nationwide_warranty", value = "1")
    TAG_NATIONWIDE_WARRANTY(1000001104L, PDD, "全国联保"),
    @TradeLabelMatchCondition(key = "self_contained", value = "1")
    TAG_IN_STORE_PICKUP(1000001105L, PDD, "门店自提"),
    @TradeLabelMatchCondition(key = "delivery_one_day", value = "1")
    TAG_SAME_DAY_SHIPPING(1000001106L, PDD, "当日发货"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "oversea_tracing", value = "1")
    TAG_GLOBAL_PURCHASE_TRACEABILITY(1000001107L, PDD, "全球购溯源"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "distributional_sale", value = "1")
    TAG_DISTRIBUTION_ORDER(1000001108L, PDD, "分销订单"),
    @TradeLabelMatchCondition(key = "has_ship_additional", value = "1")
    TAG_ADD_SHIPPING_FEE_FOR_SF_EXPRESS(1000001109L, PDD, "加运费发顺丰"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "ship_additional_order", value = "1")
    TAG_ADD_SHIPPING_FEE_TO_COVER_PRICE_DIFFERENCE_ORDER(1000001110L, PDD, "加运费补差价订单"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "allergy_refund", value = "1")
    TAG_ALLERGY_RETURN_PACKAGE(1000001111L, PDD, "过敏包退"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "professional_appraisal", value = "1")
    TAG_PROFESSIONAL_AUTHENTICATION(1000001112L, PDD, "专业鉴定"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "5")
    TAG_JAPAN_CONSOLIDATED_SHIPPING(1000001200L, PDD, "日本集运(即将下线)"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "6")
    TAG_TAIWAN_CONSOLIDATED_SHIPPING(1000001201L, PDD, "中国台湾集运(即将下线)"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "7")
    TAG_KOREA_CONSOLIDATED_SHIPPING(1000001202L, PDD, "韩国集运(即将下线)"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "8")
    TAG_SINGAPORE_CONSOLIDATED_SHIPPING(1000001203L, PDD, "新加坡集运(即将下线)"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "9")
    TAG_MALAYSIA_CONSOLIDATED_SHIPPING(1000001204L, PDD, "马来西亚集运(即将下线)"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "10")
    TAG_THAILAND_CONSOLIDATED_SHIPPING(1000001205L, PDD, "泰国集运(即将下线)"),
//    @Deprecated
//    @SystemLabelMatchCondition(key = "urge_shipping_time", value = "1", operator = exists)
//    TAG_URGE_SHIPPING(1000001600L, PDD, "催发货(拼多多)"),
    @TradeLabelMatchCondition(key = "direct_mail_activity", value = "1")
    TAG_DIRECT_MAIL_EVENT(1000001601L, PDD, "直邮活动", "针对该订单的运费，平台会为您补贴广告红包，具体请至拼多多商家后台订单详情页查看。"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "14")
    TAG_PINDUODUO_GANSU_TRANSIT(1000001602L, PDD, "拼多多甘肃中转(即将下线)", "收件人地址及电话均为集运仓地址及联系电话，若您需要查看消费者信息，请您前往拼多多商家后台进行查看。"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "15")
    TAG_PINDUODUO_INNER_MONGOLIA_TRANSIT(1000001603L, PDD, "拼多多内蒙古中转(即将下线)", "收件人地址及电话均为集运仓地址及联系电话，若您需要查看消费者信息，请您前往拼多多商家后台进行查看。"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "16")
    TAG_PINDUODUO_NINGXIA_TRANSIT(1000001604L, PDD, "拼多多宁夏中转(即将下线)", "收件人地址及电话均为集运仓地址及联系电话，若您需要查看消费者信息，请您前往拼多多商家后台进行查看。"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "17")
    TAG_PINDUODUO_QINGHAI_TRANSIT(1000001605L, PDD, "拼多多青海中转(即将下线)", "收件人地址及电话均为集运仓地址及联系电话，若您需要查看消费者信息，请您前往拼多多商家后台进行查看。"),
    @TradeLabelMatchCondition(key = "type", value = "pdd_local_warehouse", keyFrom = tradeField)
    TAG_LOCAL_WAREHOUSE_ORDER(1000001606L, PDD, "本地仓订单", "拼多多本地发货订单，无需商家打单发货，只需将货品派送至指定地点"),
    /* 抖音------------------------------------------------------------------------------------------------------------- */
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/mz8md8m2ag8capdg")
    @TradeLabelMatchCondition(key = "order_supply", value = "1")
    TAG_1000000038(1000000038L, FXG, "抖店供应链订单", "供应链订单标签，由供应商发货"),
//    @Deprecated
//    @SystemLabelMatchCondition(key = "urge_deliver", value = "", operator = exists)
//    TAG_LOGISTIC_URGENT(1000000097L, FXG, "催发货(抖音)", "买家催发货后，订单标记【催发货】标签"),
    TAG_HOME_DELIVERY(1000000101L, FXG, "音尊达送货上门", "抖店推荐送货上门的订单，订单自动标记【音尊达送货上门】标签"),
    TAG_YXD_HOME_DELIVERY(1000000102L, FXG, "音需达送货上门"),
//    @Deprecated
//    @SystemLabelMatchCondition(key = "sf_free_shipping", value = "1", keyFrom = orderExt)
//    TAG_FXG_SF_BY(1000000188L, FXG, "顺丰包邮", "商家已选择本商品承诺顺丰包邮。为保障消费者体验，本商品请选择顺丰快递发货，否则会扣除10元货款，以无门槛优惠券的方式赔付给消费者。如遇顺丰不可达或消费者自行要求发其他快递，请保留相关信息截图，在抖店PC端后台-奖惩中心-违规管理处发起申诉。申诉通过后，平台会豁免相关扣罚"),
    @TradeLabelMatchCondition(key = "priorityDelivery", value = "1")
    TAG_PRIORITY_SHIPPING(1000000189L, FXG, "优先发货"),
    @TradeLabelMatchCondition(key = "double_address", value = "1")
    TAG_DOUBLE_ADDRESS(1000000190L, FXG, "双地址", "用户选择的省、市、区/县、街道与填写的详细地址层级不一致，可能为双地址"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "user_profile_shop_customer_type_new", value = "1")
    TAG_NEW_STORE_CUSTOMER(1000000191L, FXG, "店铺新客"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "user_profile_shop_customer_type_old", value = "1")
    TAG_EXISTING_STORE_CUSTOMER(1000000192L, FXG, "店铺老客"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "user_profile_buy_frequency_high", value = "1")
    TAG_HIGH_FREQUENCY_PURCHASE(1000000193L, FXG, "高频购买"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "user_profile_buy_frequency_priority", value = "1")
    TAG_SERVICE_PRIORITY(1000000194L, FXG, "服务优先"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "c_biz_1", value = "1")
    TAG_LUBAN_ADVERTISING(1000000195L, FXG, "鲁班广告"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "c_biz_8", value = "1")
    TAG_SMALL_STORE_SELF_SELLING(1000000196L, FXG, "小店自卖"),
    @TradeLabelMatchCondition(key = "n_yuan_m_pieces", value = "1")
    TAG_FXG_N_ITEMS_M_PRODUCTS(1000000197L, FXG, "N元M件"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "c_biz", value = "2")
    TAG_FXG_SELECTED_ALLIANCE(1000000198L, FXG, "精选联盟"),
    @TradeLabelMatchCondition(key = "remote_derict", value = "1")
    TAG_FXGREMOTE_DIRECT_MAIL(1000000199L, FXG, "偏远直邮"),
    TAG_DY_AUTO_LZ(1000000202L, FXG, "自动流转"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "1")
    TAG_FXG_XINJIANG_TRANSIT(1000000205L, FXG, "新疆中转(抖音)", "收件人地址为中转地址，若您需要查看消费者信息，请您前往抖店商家后台进行查看"),
    @TradeLabelMatchCondition(key = "consolidate_type", value = "2")
    TAG_FXG_TIBET_TRANSIT(1000000206L, FXG, "西藏中转(抖音)", "收件人地址为中转地址，若您需要查看消费者信息，请您前往抖店商家后台进行查看"),
    @TradeLabelMatchCondition(key = "modify_address_user_confirm_open", value = "1")
    TAG_ADDRESS_MODIFICATION_IN_PROGRESS(1000000250L, FXG, "修改地址中", "可通过飞鸽联系消费者确认，超过6小时消费者未确认、 订单发货、订单取消，系统将自动取消本次地址修改"),
    @TradeLabelMatchCondition(key = "modify_address_user_confirm_reject", value = "1")
    TAG_ADDRESS_MODIFICATION_FAILED(1000000251L, FXG, "地址修改失败", "用户超时未确认任务拒绝"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "change_addr", value = "1")
    TAG_ADDRESS_MODIFIED(1000000252L, FXG, "已改地址"),
    @TradeLabelMatchCondition(key = "fxg_order_trade_type", value = "12")
    TAG_INFLUENCER_SAMPLE_ORDER(1000000253L, FXG, "达人买样(抖音)"),
    @TradeLabelMatchCondition(key = "fxg_order_trade_type", value = "10")
    TAG_SAMPLE_MAILING(1000000254L, FXG, "寄样(抖音)"),
    @TradeLabelProperties(supplyInherited = true)
    @TradeLabelMatchCondition(key = "user_appointment_ship_time", value = "1")
    TAG_SHIPPING_RESERVATION(1000000256L, FXG, "预约发货"),
    @TradeLabelProperties(supplyInherited = true)
    @TradeLabelMatchCondition(key = "user_appointment_receipt_time", value = "1")
    TAG_DELIVERY_RESERVATION(1000000257L, FXG, "预约送达"),
    @TradeLabelMatchCondition(key = "compass_source_not_ad_mark", value = "1")
    TAG_TRAFFIC_TYPE_NON_AD(1000000258L, FXG, "流量类型：非广告"),
    @TradeLabelMatchCondition(key = "compass_source_ad_mark", value = "1")
    TAG_TRAFFIC_TYPE_AD(1000000259L, FXG, "流量类型：广告"),
    @TradeLabelMatchCondition(key = "compass_source_content_type_live", value = "null", operator = exists)
    TAG_TRAFFIC_FORMAT_LIVE_STREAM(1000000260L, FXG, "流量体裁：直播间"),
    @TradeLabelMatchCondition(key = "compass_source_content_type_video", value = "null", operator = exists)
    TAG_TRAFFIC_FORMAT_SHORT_VIDEO(1000000261L, FXG, "流量体裁：短视频"),
    @TradeLabelMatchCondition(key = "compass_source_content_type_product_card", value = "null", operator = exists)
    TAG_TRAFFIC_FORMAT_PRODUCT_CARD(1000000262L, FXG, "流量体裁：商品卡"),
    @TradeLabelMatchCondition(key = "compass_source_content_type_other", value = "null", operator = exists)
    TAG_TRAFFIC_FORMAT_OTHER(1000000264L, FXG, "流量体裁：其他"),
    @TradeLabelMatchCondition(key = "logistics_transit", value = "1")
    TAG_REMOTE_TRANSIT(1000001007L, FXG, "偏远中转", "收件人地址为中转地址，若您需要查看消费者信息，请您前往抖店商家后台进行查看"),
    /* 得物------------------------------------------------------------------------------------------------------------- */
    @TradeLabelProperties(couldManual = true, document = "https://gykj.yuque.com/entavv/xb9xi5/tyqpup9yuyk9fcpa")
    @TradeLabelMatchCondition(key = "type", value = "12", keyFrom = tradeField)
    TAG_1000000034(1000000034L, POISON, "品牌专供现货"),
    @TradeLabelMatchCondition(key = "performance_type", value = "3")
    TAG_POISON_PERFORMANCE_TYPE_3(1000000263L, POISON, "一品多仓"),
    @TradeLabelMatchCondition(key = "serviceOrderList", value = "1", operator = exists, keyFrom = orderCustomization)
    TAG_POISON_CUSTOM_SERVICE(1000001705L, POISON, "定制服务"),
    @TradeLabelMatchCondition(key = "type", value = "39", keyFrom = tradeField)
    TAG_DEPOSIT_PRE_SALE_DIRECT_SHIP(1000001706L, POISON, "定金预售直发"),
    @TradeLabelMatchCondition(key = "type", value = "31", keyFrom = tradeField)
    TAG_BLIND_BOX_DIRECT_SHIP(1000001707L, POISON, "盲盒直发"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/kcyhadqv7fmxxviz")
    @TradeLabelMatchCondition(key = "deposit_pre_sell", value = "1")
    TAG_POISON_BRANDED_STRAIGHT_HAIR_PRE_SALE(1000002050L, POISON, "得物品牌直发(预售)"),
    @TradeLabelMatchCondition(key = "deposit_pre_sell", value = "0")
    TAG_POISON_BRANDED_STRAIGHT_HAIR_STOCK(1000002051L, POISON, "得物品牌直发(现货)"),
    /* 天猫国际------------------------------------------------------------------------------------------------------------- */
    TAG_TMGJZY_BUSINESS_WAREHOUSE_BUSINESS(1000000113L, TMGJZY, "商家仓商家配"),
    TAG_TMGJZY_BUSINESS_WAREHOUSE_SELF(1000000114L, TMGJZY, "商家仓自营配"),
    TAG_TMGJZY_BUSINESS_WAREHOUSE_CAINIAO(1000000115L, TMGJZY, "商家仓菜鸟配"),
    /* 1688------------------------------------------------------------------------------------------------------------- */
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/zo5g3s9aq80x8bxm")
    @TradeLabelMatchCondition(key = "alibaba_smt_no_consign", value = "1")
    TAG_1000000043(1000000043L, P_1688, "速卖通上门揽收", "速卖通上门揽订单请预约上门揽收，不要使用常规的自寄物流、平台官方物流等能力发货。速卖通订单包裹发货前请务必张贴箱唛和货品标签。"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/ptpizmgmegdfv3uq")
    @TradeLabelMatchCondition(key = "alibaba_smt_ae_self_send_order", value = "1")
    TAG_1000000054(1000000054L, P_1688, "速卖通自寄"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/cl3m5al6nqrk174v?singleDoc#")
    @TradeLabelMatchCondition(key = "alibabaOfficialPickUp", value = "1")
    TAG_1000000055(1000000055L, P_1688, "官方物流提货"),
    @TradeLabelMatchCondition(key = "subSource", value = "tb", keyFrom = tradeField)
    TAG_1688_TB_FX(1000000610L, P_1688, "1688淘宝分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "fxg", keyFrom = tradeField)
    TAG_1688_DY_FX(1000000611L, P_1688, "1688抖音分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "kuaishou", keyFrom = tradeField)
    TAG_1688_KS_FX(1000000612L, P_1688, "1688快手分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "pdd", keyFrom = tradeField)
    TAG_1688_PDD_FX(1000000613L, P_1688, "1688拼多多分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "xhs", keyFrom = tradeField)
    TAG_1688_XHS_FX(1000000614L, P_1688, "1688小红书分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "jd", keyFrom = tradeField)
    TAG_1688_JD_FX(1000000615L, P_1688, "1688京东分销订单(即将下线)"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/opvfbbb2cd20dage")
    @TradeLabelMatchCondition(key = "subSource", value = "wxsph", keyFrom = tradeField)
    TAG_1688_WXSPH_FX(1000000616L, P_1688, "1688微信小店/微信视频号分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "entryServiceInfo", operator = exists, keyFrom = orderCustomization)
    TAG_1688_CUSTOM_SERVICE(1000001000L, P_1688, "定制服务", "买家定制订单"),
    @TradeLabelMatchCondition(key = "dropshipping", value = "1")
    TAG_CROSS_BORDER_TRANSPORT_ORDER(1000001904L, P_1688, "1688跨境转运单"),
    @TradeLabelMatchCondition(key = "aeTrade", value = "Y")
    TAG_AE_SELF_OP_ORDER(1000001905L, P_1688, "AE自营订单", "AE自营订单只能通过1688商家后台发货，请前往1688后台发货，并注意贴货品标签和包装的箱唛"),
    @TradeLabelMatchCondition(key = "taodadian", value = "1")
    TAG_YANXUAN_ORDER(10000015108L, P_1688, "1688严选订单"),
    @TradeLabelMatchCondition(key = "alibaba_yanxuan", value = "1")
    TAG_YANXUAN_STORE_ORDER(10000015109L, P_1688, "1688严选店(站内)", "1688聚宝盆业务是全托管的模式帮助1688商家拓展站内销货渠道。商家在1688平台开店，商家将商品托管给1688严选店，商家接单发货就可以了。"),
    @TradeLabelMatchCondition(key = "subSource", value = "fxg_gx", keyFrom = tradeField)
    TAG_DOUYIN_SUPPLY_ORDER(10000015110L, P_1688, "1688抖音供销订单"),
    /* 速卖通------------------------------------------------------------------------------------------------------------- */
    TAG_SMT_ALREADY_TAXED(1000000123L, SMT, "已税", "速卖通已税标签"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/pb0otzxcg9hszf7o")
    @TradeLabelMatchCondition(key = "s_s", operator = exists)
    TAG_1000000035(1000000035L, SMT, "小额批发订单(速卖通)"),
    /* 快手------------------------------------------------------------------------------------------------------------- */
    TAG_KUAISHOU_DF(1000000129L, KUAI_SHOU, "代发"),
    @TradeLabelMatchCondition(key = "priorityDelivery", value = "true")
    TAG_KS_FAST_CONSIGN(1000000501L, KUAI_SHOU, "优先发货", "快手小店订单满足商家后台设置的优先发货条件时，会标记上【优先发货】标签"),
//    @Deprecated
//    @SystemLabelMatchCondition(key = "remindShipmentSign", value = "0", operator = greater)
//    TAG_KS_LOGISTIC_URGENT(1000000502L, KUAI_SHOU, "催发货(快手)"),
    @TradeLabelMatchCondition(key = "logistics_transit", value = "1")
    TAG_NORTHWEST_TRANSIT(1000001399L, KUAI_SHOU, "西北中转", "收货信息为中转仓信息，若需要查看消费者收货地址，请前往快手小店商家后台查看"),
    @TradeLabelMatchCondition(key = "logistics_transit", value = "15")
    TAG_INNER_MONGOLIA_TRANSIT(10000014000L, KUAI_SHOU, "内蒙古中转", "收货信息为中转仓信息，若需要查看消费者收货地址，请前往快手小店商家后台查看"),
    /* 微信小店(微信视频号)------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "paymentMethod", value = "3", keyFrom = orderCustomization)
    TAG_LUCKY_DRAW_PRODUCT(1000000556L, WXSPH, "抽奖商品"),
    @TradeLabelMatchCondition(key = "subSource", value = "wxsph_bic", keyFrom = tradeField)
    TAG_WX_BIC(1000000881L, WXSPH, "视频号质检"),
    @TradeLabelMatchCondition(key = "afterUsePayTag", value = "1")
    TAG_WX_XY_HF(1000000998L, WXSPH, "先用后付"),
    /* 淘系翱象------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "tradeAx", value = "送货上门", operator = contain, keyFrom = tradeAx)
    AX_DELIVERY(1000000777L, AOX, "送货上门"),
    @TradeLabelMatchCondition(key = "tradeAx", value = "次日达", operator = contain, keyFrom = tradeAx)
    AX_NEXT_DAY(1000000778L, AOX, "次日达"),
    @TradeLabelMatchCondition(key = "tradeAx", value = "24小时发", operator = contain, keyFrom = tradeAx)
    AX_24_HOURS(1000000779L, AOX, "24小时发"),
    @TradeLabelMatchCondition(key = "tradeAx", value = "今日发", operator = contain, keyFrom = tradeAx)
    AX_TODAY(1000000780L, AOX, "今日发"),
    @TradeLabelMatchCondition(key = "tradeAx", value = "官方物流", operator = contain, keyFrom = tradeAx)
    AX_OFFICIAL_LOGISTICS(1000000781L, AOX, "官方物流"),
    @TradeLabelMatchCondition(key = "tradeAx", value = "新疆集运", operator = contain, keyFrom = tradeAx)
    AX_XINJIANG_CONSOLIDATION(1000000782L, AOX, "新疆集运"),
    @TradeLabelMatchCondition(key = "tradeAx", value = "商家承诺送货上门", operator = contain, keyFrom = tradeAx)
    AX_PROMISES_HOME_DELIVERY(1000000783L, AOX, "商家承诺送货上门"),
    @TradeLabelMatchCondition(key = "tradeAx", value = "特惠集运", operator = contain, keyFrom = tradeAx)
    AX_SHIPPING_CONSOLIDATION(1000000784L, AOX, "特惠集运"),
    @TradeLabelMatchCondition(key = "tradeAx", value = "自选快递", operator = contain, keyFrom = tradeAx)
    AX_OPTIONAL_EXPRESS(1000000785L, AOX, "自选快递"),
    /* 花城农夫------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "type", value = "nfgood_help_sell", keyFrom = tradeField)
    NFGOOD_HELP_SELL(1000000789L, NFGOOD, "帮卖", "帮卖订单无需发货"),
    /* 1889------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "subSource", value = "tb", keyFrom = tradeField)
    TAG_1000002300(1000002300L, P_1889, "1889淘宝分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "fxg", keyFrom = tradeField)
    TAG_1000002301(1000002301L, P_1889, "1889抖音分销订单(即将下线)"),
    /* 震坤行------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "type", value = "zkh_direct", keyFrom = tradeField)
    TAG_10000015106(10000015106L, ZHENKUNHANG, "震坤行直发"),
    @TradeLabelMatchCondition(key = "type", value = "zkh_logistics", keyFrom = tradeField)
    TAG_10000015107(10000015107L, ZHENKUNHANG, "震坤行代管代发"),
    /* Tiktok全托管------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "is_first_order", value = "true")
    TAG_10000015100(10000015100L, TIKTOK_QTG, "首单(Tiktok)"),
    @TradeLabelMatchCondition(key = "is_delivery_completed", value = "false")
    TAG_10000015101(10000015101L, TIKTOK_QTG, "可送货(Tiktok)"),
    @TradeLabelMatchCondition(key = "emergency_level", value = "EXPEDITED")
    TAG_10000015102(10000015102L, TIKTOK_QTG, "加急(Tiktok)"),
    @TradeLabelMatchCondition(key = "emergency_level", value = "URGENT")
    TAG_10000015103(10000015103L, TIKTOK_QTG, "紧急(Tiktok)"),
    @TradeLabelMatchCondition(key = "emergency_level", value = "GENERAL")
    TAG_10000015105(10000015105L, TIKTOK_QTG, "普通(Tiktok)"),
    /* TIKTOK------------------------------------------------------------------------------------------------------------- */
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/tf5gpss47lk395ml")
    @TradeLabelMatchCondition(key = "warehouse_tag_flag", value = "local")
    TAG_1000000103(1000000103L, TIKTOK, "卖家本地仓发货(Tiktok)", "卖家从目标市场本地仓发货的订单。"),
    @TradeLabelMatchCondition(key = "warehouse_tag_flag", value = "crossBorder")
    TAG_1000000104(1000000104L, TIKTOK, "卖家跨境仓发货(Tiktok)", "卖家从国内跨境仓发货的订单。"),
    @TradeLabelMatchCondition(key = "delivery_option", value = "SEND_BY_SELLER")
    TAG_TIKTOK_SEND_BY_SELLER(1000000122L, TIKTOK, "商家自寄"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "status", value = "tiktok_AWAITING_COLLECTION", keyFrom = tradeField)
    TAG_1000100001(1000100001L, TIKTOK, "待揽收(Tik Tok)"),
    /* 希音------------------------------------------------------------------------------------------------------------- */
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/ggaq8qf0720fshk7")
    @TradeLabelMatchCondition(key = "customInfoId", operator = exists, keyFrom = orderCustomization)
    TAG_1000000046(1000000046L, SHEIN, "定制品订单(希音)"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/tpidhmx2gutyi3gv?singleDoc#")
    @TradeLabelMatchCondition(key = "isSecurityProduct", value = "1")
    TAG_1000000051(1000000051L, SHEIN, "安检(希音)"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/re9dbuad0bxy2d66?singleDoc#")
    @TradeLabelMatchCondition(key = "countryMarket", value = "1", keyFrom = tradeExt)
    TAG_1000000061(1000000061L, SHEIN, "南大区(希音)"),
    @TradeLabelMatchCondition(key = "countryMarket", value = "2", keyFrom = tradeExt)
    TAG_1000000062(1000000062L, SHEIN, "北大区(希音)"),
    @TradeLabelMatchCondition(key = "firstMark", value = "1")
    TAG_1000000207(1000000207L, SHEIN, "首单"),
    @TradeLabelProperties(document = {"https://gykj.yuque.com/entavv/xb9xi5/cqath02rbhr1y7ki", "https://gykj.yuque.com/entavv/xb9xi5/zobq0zikczzuggb1"})
    @TradeLabelMatchCondition(key = "refundTag", value = "1")
    TAG_1000000790(1000000790L, SHEIN, "已退货订单(希音)"),
    @TradeLabelMatchCondition(key = "urgencyType", value = "1")
    TAG_1000001001(1000001001L, SHEIN, "急采订单(希音)"),
    @TradeLabelMatchCondition(key = "urgencyType", value = "0")
    TAG_1000001002(1000001002L, SHEIN, "备货订单(希音)"),
    @TradeLabelMatchCondition(key = "urgencyType", value = "2")
    TAG_1000002108(1000002108L, SHEIN, "JIT(希音)"),
    @TradeLabelMatchCondition(key = "urgencyType", value = "3")
    TAG_1000002109(1000002109L, SHEIN, "VMI(希音)"),
    @TradeLabelMatchCondition(key = "spiltMark", value = "1")
    TAG_1000030007(1000030007L, SHEIN, "希音拆单"),
    /* TEMU------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "urgencyType", value = "1")
    TAG_1000001003(1000001003L, TEMU, "急采订单(TEMU)"),
    @TradeLabelMatchCondition(key = "urgencyType", value = "0")
    TAG_1000001004(1000001004L, TEMU, "备货订单(TEMU)"),
    @TradeLabelMatchCondition(key = "todayCanDeliver", value = "1")
    TAG_1000001500(1000001500L, TEMU, "今日可发"),
    @TradeLabelMatchCondition(key = "isCanJoinDeliverPlatform", value = "1")
    TAG_1000001503(1000001503L, TEMU, "可加入发货台"),
    @TradeLabelMatchCondition(key = "settlementType", value = "0")
    TAG_1000001504(1000001504L, TEMU, "JIT(TEMU)"),
    @TradeLabelMatchCondition(key = "settlementType", value = "1")
    TAG_1000001505(1000001505L, TEMU, "VMI(TEMU)"),
    @TradeLabelMatchCondition(key = "isFirst", value = "1")
    TAG_1000001506(1000001506L, TEMU, "首单(TEMU)"),
    @TradeLabelMatchCondition(key = "joinDeliveryDesk", value = "1")
    TAG_1000001507(1000001507L, TEMU, "发货台(TEMU)"),
    /* 京东------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "isJps", value = "1", keyFrom = tradeField)
    TAG_JD_JPS(1000000004L,JD, "京品试", "京东先试后付订单，订单自动标记【京品试】标签"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "subSource", value = "jd_identify_store", keyFrom = tradeField)
    TAG_1000000052(1000000052L, JD, "京东鉴定订单"),
    @TradeLabelMatchCondition(key = "afterUsePayTag", value = "1")
    TAG_1000001005(1000001005L, JD, "先用后付(京东)"),
    @TradeLabelMatchCondition(key = "subSource", value = "jd_jpzt", keyFrom = tradeField)
    TAG_JD_DELIVERY_SELF_PICKUP(1000001006L, JD, "京配自提"),
    @TradeLabelMatchCondition(key = "subSource", value = "fxg")
    TAG_1000001280(1000001280L, JD, "京东抖音分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "kuaishou")
    TAG_1000001281(1000001281L, JD, "京东快手分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "partialRefundSplitOrder", value = "2")
    TAG_1000001492(1000001492L, JD, "京东-部分退款拆单"),
    @TradeLabelProperties(supplyInherited = true)
    @TradeLabelMatchCondition(key = "isJdConsolidatedOrder", value = "1")
    TAG_1000001493(1000001493L, JD, "京东偏远集运", "京东集运订单真实收件人信息以及中转仓发往买家段的物流轨迹请前往京麦订单详情查看。"),
    @TradeLabelMatchCondition(key = "jdReservationOrder", value = "2")
    TAG_1000001494(1000001494L, JD, "京东预约订单", "订单可延迟发货买家预约了发货时间，请仔细核对好买家预约的发货时间"),
    @TradeLabelMatchCondition(key = "homeDelivery", value = "3")
    TAG_1000001495(1000001495L, JD, "京东修改为不支持送货上门"),
    @TradeLabelMatchCondition(key = "homeDelivery", value = "2")
    TAG_1000001496(1000001496L, JD, "京东中小件送货上门"),
    @TradeLabelMatchCondition(key = "homeDelivery", value = "1")
    TAG_1000001497(1000001497L, JD, "京东大件送货上门"),
//    @Deprecated
//    @SystemLabelProperties(supplyInherited = true)
//    @SystemLabelMatchCondition(key = "homeDelivery", value = "1")
//    TAG_DOOR_DELIVERY(1000001498L, JD, "送货上门"),
    @TradeLabelProperties(supplyInherited = true)
    @TradeLabelMatchCondition(key = "doorToDodrReplacement", value = "1")
    TAG_1000001499(1000001499L, JD, "上门换新"),
    /* 京东自营------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "subSource", value = "fxg", keyFrom = tradeField)
    TAG_1000002200(1000002200L, JD_VC, "京东自营抖店分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "kuaishou", keyFrom = tradeField)
    TAG_1000002201(1000002201L, JD_VC, "京东自营快手分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "gongXiaoDaOrder", value = "1")
    TAG_1000002202(1000002202L, JD_VC, "工小达"),
    @TradeLabelMatchCondition(key = "jdReservationOrder", value = "2")
    TAG_JD_VC_RESERVATION_ORDER(1000002203L, JD_VC, "京东预约订单", "订单可延迟发货买家预约了发货时间，请仔细核对好买家预约的发货时间"),
    @TradeLabelMatchCondition(key = "jdEnterpriseOrder", value = "1")
    TAG_1000002204(1000002204L, JD_VC, "企业订单", "京东自营B2B企业订单，需要专属的配送单。"),
    @TradeLabelMatchCondition(key = "jd_yip", value = "1")
    TAG_1000002205(1000002205L, JD_VC, "定制", "京东自营C2M定制订单"),
    @TradeLabelProperties(initSupportManual = true, splitNotInherited = true, document = "https://gykj.yuque.com/entavv/xb9xi5/of3tpttgpydvbmp8")
    TAG_JDCV_SELF_SETTLE(10000022010L, JD_VC, "自行结算"),
    /* 唯品会------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "subSource", value = "vipjitx", keyFrom = tradeField)
    @TradeLabelMatchCondition(key = "status", value = "vipjitx_NEW", operator = not, keyFrom = tradeField)
    TAG_TO_BE_SEARCHED(1000000013L, VIPJIT, "待寻仓"),
    @TradeLabelProperties(splitNotInherited = true)
    VIPSOV_UPDATE_ORDER(1000000515L, VIPJIT, "唯品会修改运单号"),
    @TradeLabelMatchCondition(key = "quality_check", value = "1")
    TAG_1000001010(1000001010L, VIPJIT, "重点检查", "发货前自检商品"),
    @TradeLabelMatchCondition(key = "priority_delivery", value = "1")
    TAG_1000001011(1000001011L, VIPJIT, "优先发货"),
    @TradeLabelMatchCondition(key = "qualityCheckType", value = "1")
    TAG_1000001012(1000001012L, VIPJIT, "(重点检查)一换质检", "JITX订单，发货前自检商品"),
    @TradeLabelMatchCondition(key = "qualityCheckType", value = "2")
    TAG_1000001013(1000001013L, VIPJIT, "(重点检查)二换质检", "JITX订单，发货前自检商品"),
    @TradeLabelMatchCondition(key = "qualityCheckType", value = "4")
    TAG_1000001014(1000001014L, VIPJIT, "(重点检查)退换货重拍同商品", "JITX订单，发货前自检商品"),
    @TradeLabelMatchCondition(key = "qualityCheckType", value = "8")
    TAG_1000001015(1000001015L, VIPJIT, "(重点检查)商品品相敏感", "JITX订单，发货前自检商品"),
    @TradeLabelMatchCondition(key = "actionCode", value = "newcarton_package")
    TAG_1000001016(1000001016L, VIPJIT, "全新纸箱包装发货", "请使用全新纸箱包装发货"),
    @TradeLabelMatchCondition(key = "actionCode", value = "gift_package")
    TAG_1000001017(1000001017L, VIPJIT, "礼盒包装发货", "请使用礼盒包装发货"),
    @TradeLabelMatchCondition(key = "actionCode", value = "carton_package")
    TAG_1000001018(1000001018L, VIPJIT, "纸箱包装发货", "请使用纸箱包装发货"),
    @TradeLabelMatchCondition(key = "orderLabel", value = "1")
    TAG_1000001019(1000001019L, VIPJIT, "普通订单"),
    @TradeLabelMatchCondition(key = "orderLabel", value = "2")
    TAG_1000001020(1000001020L, VIPJIT, "原订单"),
    @TradeLabelMatchCondition(key = "orderLabel", value = "3")
    TAG_1000001021(1000001021L, VIPJIT, "一换订单"),
    @TradeLabelMatchCondition(key = "orderLabel", value = "4")
    TAG_1000001022(1000001022L, VIPJIT, "二换订单"),
    @TradeLabelMatchCondition(key = "tid", value = "BHPICK", operator = contain, keyFrom = tradeField)
    TAG_VIPSHOP_RESTOCK_ORDER(1000001023L, VIPJIT, "唯品会补货单", "唯品会补货单-补货拣货订单"),
    @TradeLabelMatchCondition(key = "redeliver", value = "1")
    TAG_1000001025(1000001025L, VIPJIT, "唯品会补寄"),
    /* 快团团------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "subSource", operator = not, keyFrom = tradeField)
    TAG_1000000601(1000000601L, KTT, "团长业务"),
    @TradeLabelMatchCondition(key = "subSource", value = "ktt_supply", keyFrom = tradeField)
    TAG_1000000602(1000000602L, KTT, "供货商业务"),
    @TradeLabelMatchCondition(key = "type", value = "helpSale", keyFrom = tradeField)
    TAG_KTT_HELP_SALE(1000000603L, KTT, "帮卖订单"),
    @TradeLabelMatchCondition(key = "logisticsType", value = "20")
    TAG_1000030008(1000030008L, KTT, "自提", "物流方式为自提的快团团订单"),
    @TradeLabelMatchCondition(key = "logisticsType", value = "30")
    TAG_1000030009(1000030009L, KTT, "同城配送", "物流方式为自提的快团团订单"),
    @TradeLabelMatchCondition(key = "logisticsType", value = "0")
    TAG_1000030010(1000030010L, KTT, "无需物流", "物流方式为自提的快团团订单"),
    @TradeLabelMatchCondition(key = "type", value = "helpSale", keyFrom = tradeField)
    TAG_1000030011(1000030011L, KTT, "快团团帮卖订单", "快团团帮卖订单禁止发货"),
    /* 店加------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "type", value = "3", keyFrom = tradeField)
    TAG_1000001450(1000001450L, DIANPLUS, "发货单"),
    @TradeLabelMatchCondition(key = "type", value = "2", keyFrom = tradeField)
    TAG_1000001451(1000001451L, DIANPLUS, "调拨单"),
    /* 小红书------------------------------------------------------------------------------------------------------------- */
//    @Deprecated
//    @SystemLabelMatchCondition(key = "HOME_DELIVERY", value = "1")
//    TAG_XHS_DOOR_DELIVERY(1000001700L, XHS, "送货上门"),
//    @Deprecated
//    @SystemLabelMatchCondition(key = "URGENT_SHIP", value = "1")
//    TAG_XHS_URGE_SHIPPING(1000001701L, XHS, "催发货(小红书)"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "SAMPLE", value = "1")
    TAG_1000001702(1000001702L, XHS, "拿样"),
    @TradeLabelProperties(couldManual = true)
    @TradeLabelMatchCondition(key = "QIC", value = "1")
    TAG_1000001703(1000001703L, XHS, "质检"),
    @TradeLabelMatchCondition(key = "NEW_YEAR", value = "1")
    TAG_1000001704(1000001704L, XHS, "新年礼"),
    /* 苏宁------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "subSource", value = "fxg", keyFrom = tradeField)
    TAG_1000001800(1000001800L, SN, "苏宁抖音分销订单(即将下线)"),
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/wvusvx73wlds6gxt")
    @TradeLabelMatchCondition(key = "subSource", value = "tm", keyFrom = tradeField)
    TAG_1000001801(1000001801L, SN, "苏宁天猫分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "kuaishou", keyFrom = tradeField)
    TAG_1000001802(1000001802L, SN, "苏宁快手分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "duxd", keyFrom = tradeField)
    TAG_1000001803(1000001803L, SN, "苏宁百度小店分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "pdd", keyFrom = tradeField)
    TAG_1000001804(1000001804L, SN, "苏宁拼多多分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "tb", keyFrom = tradeField)
    TAG_1000001805(1000001805L, SN, "苏宁淘宝分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "tjb", keyFrom = tradeField)
    TAG_1000001806(1000001806L, SN, "苏宁淘特分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "wxsph", keyFrom = tradeField)
    TAG_1000001807(1000001807L, SN, "苏宁视频号分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "xhs", keyFrom = tradeField)
    TAG_1000001808(1000001808L, SN, "苏宁小红书分销订单(即将下线)"),
    /* 好食期------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "subSource", value = "tb", keyFrom = tradeField)
    TAG_1000001900(1000001900L, HAOSHIQI, "好食期淘宝分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "kuaishou", keyFrom = tradeField)
    TAG_1000001901(1000001901L, HAOSHIQI, "好食期快手分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "fxg", keyFrom = tradeField)
    TAG_1000001902(1000001902L, HAOSHIQI, "好食期抖店分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "pdd", keyFrom = tradeField)
    TAG_1000001903(1000001903L, HAOSHIQI, "好食期拼多多分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "wxsph", keyFrom = tradeField)
    TAG_1000001906(1000001906L, HAOSHIQI, "好食期视频号分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "jd", keyFrom = tradeField)
    TAG_1000001907(1000001907L, HAOSHIQI, "好食期京东分销订单(即将下线)"),
    /* 抖超------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "is_insure", value = "1")
    TAG_1000020001(1000020001L, FXG_CS, "保价订单"),
    @TradeLabelMatchCondition(key = "allow_merge", value = "0")
    TAG_1000020002(1000020002L, FXG_CS, "平台不允许合单"),
    /* 虾皮------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "fulfillmentFlag", value = "fulfilled_by_cb_seller")
    TAG_1000002000(1000002000L, SHOPEE, "卖家跨境仓发货(Shopee)", "卖家从国内跨境仓发货的订单。"),
    @TradeLabelMatchCondition(key = "fulfillmentFlag", value = "fulfilled_by_local_seller")
    TAG_1000002001(1000002001L, SHOPEE, "卖家本地仓发货(Shopee)", "卖家从目标市场本地仓发货的订单。"),
    @TradeLabelMatchCondition(key = "fulfillmentFlag", value = "fulfilled_by_shopee")
    TAG_SHOPEE_FUL_FILLED(576877679677952L, SHOPEE, "外仓发货", "外仓发货,虾皮官方海外仓发货的订单"),
    /* 小芒电商------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "subSource", value = "fxg", keyFrom = tradeField)
    TAG_1000002100(1000002100L, XIAOMANG, "小芒抖音分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "tm", keyFrom = tradeField)
    TAG_1000002101(1000002101L, XIAOMANG, "小芒天猫分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "pdd", keyFrom = tradeField)
    TAG_1000002102(1000002102L, XIAOMANG, "小芒拼多多分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "kuaishou", keyFrom = tradeField)
    TAG_1000002103(1000002103L, XIAOMANG, "小芒快手分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "yz", keyFrom = tradeField)
    TAG_1000002104(1000002104L, XIAOMANG, "小芒有赞分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "poison", keyFrom = tradeField)
    TAG_1000002105(1000002105L, XIAOMANG, "小芒得物分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "xhs", keyFrom = tradeField)
    TAG_1000002106(1000002106L, XIAOMANG, "小芒小红书分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "subSource", value = "jd", keyFrom = tradeField)
    TAG_1000002107(1000002107L, XIAOMANG, "小芒京东分销订单(即将下线)"),
    /* 速卖通全(半)托管------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "isOumengCountryTrade", value = "1")
    TAG_1000003000(1000003000L, SMTQTG, "速卖通欧盟订单"),
    @TradeLabelMatchCondition(key = "urgencyType", value = "1")
    TAG_1000003001(1000003001L, SMTQTG, "仓发备货单-紧急(速卖通)"),
    @TradeLabelMatchCondition(key = "urgencyType", value = "0")
    TAG_1000003002(1000003002L, SMTQTG, "仓发备货单-普通(速卖通)"),
    @TradeLabelMatchCondition(key = "deliveryFinish", value = "1")
    TAG_1000003003(1000003003L, SMTQTG, "可继续发货(速卖通)"),
    /* 有赞------------------------------------------------------------------------------------------------------------- */
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/oly8qmfn0vekbb6k")
    @TradeLabelMatchCondition(key = "entrance", value = "wx_video_xd")
    TAG_1000000039(1000000039L, YZ, "有赞视频号分销订单(即将下线)"),
    @TradeLabelMatchCondition(key = "type", value = "3")
    TAG_1000030001(1000030001L, YZ, "有赞分销供货"),
    @TradeLabelMatchCondition(key = "isRetailOrder", value = "true")
    TAG_1000030002(1000030002L, YZ, "零售"),
    @TradeLabelMatchCondition(key = "expressType", value = "9")
    TAG_1000030003(1000030003L, YZ, "无需发货"),
    @TradeLabelMatchCondition(key = "payType25", value = "25")
    TAG_1000030004(1000030004L, YZ, "储值卡支付25"),
    @TradeLabelMatchCondition(key = "payType35", value = "35")
    TAG_1000030005(1000030005L, YZ, "储值卡支付35"),
    @TradeLabelMatchCondition(key = "payType106", value = "106")
    TAG_1000030006(1000030006L, YZ, "储值卡支付106"),
    /* 淘工厂------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "title", value = "补寄", operator = contain, keyFrom = orderField)
    TAG_10000014100(10000014100L, P_1688_C2M, "补寄订单", "淘工厂补寄订单"),
    @TradeLabelMatchCondition(key = "title", value = "换货", operator = contain, keyFrom = orderField)
    TAG_10000014101(10000014101L, P_1688_C2M, "淘工厂换货单"),
    /* 网易严选------------------------------------------------------------------------------------------------------------- */
    @TradeLabelMatchCondition(key = "subSource", value = "fxg", keyFrom = tradeField)
    TAG_1000001960(1000001960L, NETEASE, "网易严选抖音分销订单(即将下线)"),
    /* JOOM------------------------------------------------------------------------------------------------------------- */
    @TradeLabelProperties(document = "https://gykj.yuque.com/entavv/xb9xi5/ew3thw49alrcw1pg")
    @TradeLabelMatchCondition(key = "isFbj", value = "1")
    TAG_1000000049(1000000049L, JOOM, "FBJ订单(Joom)"),


    ;

    /**
     * 定义 - 系统标签ID
     */
    private final Long id;
    /**
     * 定义 - 系统标签名称
     */
    private final String name;
    /**
     * 标签类型
     */
    private TradeLabelTypeEnum labelType;
    /**
     * 业务处理是否继承这个标签（比如拆单是否需要继承）(默认全继承)
     */
    private TradeBusinessEnum[] businessExtends;
    /**
     * 初始化是否关闭
     */
    private boolean initClose;
    /**
     * 初始化是否开启手动修改
     */
    private boolean initSupportManual;
    /**
     * 是否允许手动修改
     */
    private boolean allowManual;
    /**
     * 定义 - 系统标签备注(悬浮文案)
     */
    private final String remark;
    /**
     * 定义 - 系统标签所属平台
     */
    private final TradeLabelPlatformEnum platform;


    public final static Map<Long, TradeSystemLabelEnum> id2LabelMap = new HashMap<>();
    public final static Map<Long, TradeTag> id2SystemLabelMap = new HashMap<>();

    public final static Map<String, Set<TradeTag>> platform2SystemLabelMap = new HashMap<>();

    static {
        // 静态全量填充 定义-系统标签
        for (TradeSystemLabelEnum value : TradeSystemLabelEnum.values()) {
            id2LabelMap.put(value.id, value);
            TradeTag systemTag = value.getSystemTag();
            id2SystemLabelMap.put(systemTag.getId(), systemTag);
            platform2SystemLabelMap.computeIfAbsent(systemTag.getPlatformCode(), k -> new HashSet<>()).add(systemTag);
        }
        SystemTags.SYSTEM_TAG_MAP.putAll(id2SystemLabelMap);
    }


    /**
     * 基元构造函数, 仅攘括系统标签三要素[id, platform, name](系统标签备注默认为系统标签名称)
     */
    TradeSystemLabelEnum(Long id, TradeLabelPlatformEnum platform, String tagName) {
        this(id, platform, tagName, tagName);
    }

    /**
     * 补充构造函数, 基于基于构造函数, 扩展系统标签备注的定义
     */
    TradeSystemLabelEnum(Long id, TradeLabelPlatformEnum platform, String name, String remark) {
        this.id = id;
        this.platform = platform;
        this.name = name;
        this.remark = remark;
    }

    /**
     *  获取全局 - 系统标签实体
     */
    public TradeTag getSystemTag() {
        if (SystemTags.SYSTEM_TAG_MAP.containsKey(id)) {
            return SystemTags.SYSTEM_TAG_MAP.get(id);
        }
        TradeTag tradeTag = buildSystemTag(id, platform.getPlatformCode(), name, remark);
        fill(tradeTag);
        return tradeTag;
    }

    /**
     * 填充系统标签相关定义
     */
    private void fill(TradeTag tradeTag) {
        try {
            Field field = this.getClass().getField(this.name());
            fillProperties(tradeTag, field);
            fillMatchCondition(tradeTag, field);
        } catch (NoSuchFieldException e) {
            // 无需处理, 不存在此类情况
        }
    }

    /**
     * 填充基本属性
     */
    private void fillProperties(TradeTag tradeTag, Field field) {
        fillProperties(tradeTag, field.getAnnotation(TradeLabelProperties.class));
    }

    /**
     * 如果不存在属性配置,则取默认值即可
     */
    private static void fillProperties(TradeTag tradeTag, TradeLabelProperties properties) {
        if (Objects.nonNull(properties)) {
            tradeTag.setInitClose(properties.initClose());
            tradeTag.setOpen(!properties.initClose());
            tradeTag.setSupportManualUpdate(properties.initSupportManual());
            tradeTag.setCouldManual(properties.initSupportManual() || properties.couldManual());
            tradeTag.setSplitNotInherited(properties.splitNotInherited());
            tradeTag.setSupplyInherited(properties.supplyInherited());
        } else {
            tradeTag.setInitClose(TradeLabelDefaultProperties.initClose);
            tradeTag.setOpen(true);
            tradeTag.setSupportManualUpdate(TradeLabelDefaultProperties.initSupportManual);
            tradeTag.setCouldManual(TradeLabelDefaultProperties.couldManual);
            tradeTag.setSplitNotInherited(TradeLabelDefaultProperties.splitNotInherited);
            tradeTag.setSupplyInherited(TradeLabelDefaultProperties.supplyInherited);
        }
        tradeTag.setSupportReCalculate(false);
    }

    /**
     * 填充系统标签匹配条件
     */
    private void fillMatchCondition(TradeTag tradeTag, Field field) {
        if (field.isAnnotationPresent(TradeLabelMatchCondition.class)) {
            TradeLabelMatchCondition[] conditions = field.getAnnotationsByType(TradeLabelMatchCondition.class);
            for (TradeLabelMatchCondition condition : conditions) {
                fillMatchCondition(tradeTag, condition);
            }
        }else if (field.isAnnotationPresent(TradeLabelMatchConditions.class)) {
            TradeLabelMatchConditions conditions = field.getAnnotation(TradeLabelMatchConditions.class);
            for (TradeLabelMatchCondition condition : conditions.value()) {
                fillMatchCondition(tradeTag, condition);
            }
        }
    }

    private void fillMatchCondition(TradeTag tradeTag, TradeLabelMatchCondition condition) {
        tradeTag.getConditions().add(new TradeSystemLabelMatchCondition(condition.key(), condition.value(), condition.operator(), condition.keyFrom()));
    }

    public static void main(String[] args) throws NoSuchFieldException {
        printEnum();
    }


    /**
     * 效果等同于 [格式化]
     * 以代码样式打印完整整理后的枚举实现
     */
    public static void printEnum() throws NoSuchFieldException {
        TreeMap<TradeLabelPlatformEnum, List<TradeSystemLabelEnum>> collect = Arrays.stream(values())
                .collect(Collectors.groupingBy(TradeSystemLabelEnum::getPlatform, TreeMap::new, Collectors.toList()));
        for (Map.Entry<TradeLabelPlatformEnum, List<TradeSystemLabelEnum>> entry : collect.entrySet()) {
            TradeLabelPlatformEnum key = entry.getKey();
            List<TradeSystemLabelEnum> values = entry.getValue();
            System.out.println("/* " + key.getPlatformName() + "------------------------------------------------------------------------------------------------------------- */");
            for (TradeSystemLabelEnum value : values) {
                Annotation[] annotations = TradeSystemLabelEnum.class.getField(value.name()).getAnnotations();
                for (Annotation annotation : annotations) {
                    if (annotation instanceof Deprecated) {
                        System.out.println("@Deprecated");
                    }
                    if (annotation instanceof TradeLabelProperties) {
                        printAnnotationProperties((TradeLabelProperties) annotation);
                    }
                    if (annotation instanceof TradeLabelMatchCondition) {
                        printAnnotationCondition((TradeLabelMatchCondition) annotation);
                    }
                    if (annotation instanceof TradeLabelMatchConditions) {
                        printAnnotationCondition((TradeLabelMatchConditions) annotation);
                    }
                }
                System.out.println(value);
            }
        }
    }

    @Override
    public String toString() {
        StringJoiner joiner = new StringJoiner(", ", name() + "(", "),");
        joiner
                .add(id + "L")
                .add(platform.name())
                .add("\"" + name + "\"");
        if (!remark.equals(name)) {
            joiner.add("\"" + remark.replace("\n", "\\n") + "\"");
        }
        return joiner.toString();
    }

    public static void printAnnotationProperties(TradeLabelProperties properties) {
        StringJoiner joiner = new StringJoiner(", ", "@TradeLabelProperties(", ")");
        boolean needPrint = false;
        if (properties.initClose() != TradeLabelDefaultProperties.initClose) {
            joiner.add("initClose = " + properties.initClose());
            needPrint = true;
        }
        if (properties.initSupportManual() != TradeLabelDefaultProperties.initSupportManual) {
            joiner.add("initSupportManual = " + properties.initSupportManual());
            needPrint = true;
        }
        if (properties.couldManual() != TradeLabelDefaultProperties.couldManual) {
            joiner.add("couldManual = " + properties.couldManual());
            needPrint = true;
        }
        if (properties.splitNotInherited() != TradeLabelDefaultProperties.splitNotInherited) {
            joiner.add("splitNotInherited = " + properties.splitNotInherited());
            needPrint = true;
        }
        if (properties.supplyInherited() != TradeLabelDefaultProperties.supplyInherited) {
            joiner.add("supplyInherited = " + properties.supplyInherited());
            needPrint = true;
        }
        if (StringUtils.isNoneBlank(properties.document())) {
            if (properties.document().length > 1) {
                StringJoiner docJoin = new StringJoiner(",", "{", "}");
                for (String doc : properties.document()) {
                    docJoin.add("\"" + doc + "\"");
                }
                joiner.add("document = " + docJoin);
            }else {
                joiner.add("document = \"" + properties.document()[0] + "\"");
            }
            needPrint = true;
        }
        if (needPrint) {
            System.out.println(joiner);
        }
    }

    public static void printAnnotationCondition(TradeLabelMatchCondition condition) {
        StringJoiner joiner = new StringJoiner(", ", "@TradeLabelMatchCondition(", ")");
        joiner.add("key = \"" + condition.key() + "\"");
        joiner.add("value = \"" + condition.value() + "\"");
        if (!condition.operator().equals(equal)) {
            joiner.add("operator = " + condition.operator());
        }
        if (!condition.keyFrom().equals(tradeExt)) {
            joiner.add("keyFrom = " + condition.keyFrom());
        }
        System.out.println(joiner);
    }

    public static void printAnnotationCondition(TradeLabelMatchConditions conditions) {
        for (TradeLabelMatchCondition condition : conditions.value()) {
            printAnnotationCondition(condition);
        }
    }

}

package com.raycloud.dmj.domain.trade.config;

import com.raycloud.dmj.domain.trade.config.entity.*;
import lombok.Getter;

import java.util.Map;
import java.util.TreeMap;

/**
 * 订单配置枚举
 * 这里加入新的配置需要按照以下规则
 * 1. 中文名称必须和界面保持一致
 * 2. key 数据库默认不能超过64位
 * 3. 配置的值默认json，对于新加的开关配置定义为 {"enableStatus": 0}，tradeConfigNew.isOpen有做兼容, 复杂的json可继承SwitchConfig
 * 4. 不建议逗号隔开"123,1213", 直接使用["123", "1213"], 这样可以直接json转set或list
 * 5. 当前枚举有两个构造方法 oldParentPath属性是用来区分新老配置的，新加的配置不需要这个属性
 * 6. 配置的校验代码在单独的包里 com.raycloud.dmj.services.trades.config.inner.check，里面有例子
 * 7. 配置的迁移在单独的包里 com.raycloud.dmj.services.trades.config.inner.migrate 里面有例子
 * 8. 配置的转换 例如格式调整在单独的包里 com.raycloud.dmj.services.trades.config.inner.convert 里面有例子
 */
@Getter
public enum TradeConfigEnum {

    TRADE_PRINT_SUPPORT_ONLY_STOCK_INSUFFICIENT("tradePrintSupportOnlyStockInsufficient", "0", "订单打印/订单打印v2/打单发货(极速版)支持打印仅缺货异常订单"),
    /**
     * 重算配置开始
     */
    AUTO_RE_MATCH_GIFT_AFTER_CHANGE_ITEM("autoReMatchGiftAfterChangeItem", "0", "修改商品后重新匹配赠品"),
    AUTO_RE_MATCH_MARK_AFTER_CHANGE_ITEM("autoReMatchMarkAfterChangeItem", "0", "修改商品后重新匹配标签"),
    AUTO_RE_MATCH_WAREHOUSE_AFTER_CHANGE_ITEM("autoReMatchCkAfterChangeItem", "0", "修改商品后重新匹配仓库"),
    AUTO_RE_MATCH_EXPRESS_AFTER_CHANGE_ITEM("autoReMatchKdAfterChangeItem", "0", "修改商品后重新匹配快递"),
    AUTO_RE_MATCH_RULE_AFTER_CHANGE_ITEM("autoReMatchRuleAfterChangeItem", "0", "重算自动化匹配规则"),

    AUTO_EXCEPTION_NOT_RE_MATCH("exceptionNotReMatch", "-1", "指定异常不参与重算"),
    /**
     * 重算配置开始
     */
    ENTITY_CODE_CONVERT_DIMENSION("entityCodeConvertDimension", "0", "根据实体编码换商品时,计算实体商品库存的维度: 商品维度(0-order);订单维度(1-trade下所有可转order)"),
    ENTITY_CODE_CONVERT_AUTO("entityCodeConvertAuto", "0", "订单首次进入系统时，待审核状态的订单套件商品自动转换成对应的实体商品"),
    ENTITY_CODE_CONVERT_AUTO_TAG_ID_SET("entityCodeConvertAutoTagIdSet", "", "订单首次进入系统时，待审核状态的订单套件商品自动转换成对应的实体商品,增加标签条件过滤订单"),
    ENTITY_CODE_CONVERT_CHECK_STOCK("entityCodeConvertCheckStock", "1", "转换时考虑库存: 1-(有库存才转化)，2-(忽略库存转换（选择后订单首次下载默认转换成实体编码）)"),

    /**
     * 换货补发订单创建时同步执行智能分仓
     */
    REISSUE_OR_CHANGE_TRADE_WAREHOUSE_MATCH("reissueOrChangeTradeWarehouseMatch", "0", "换货补发订单创建时同步执行智能分仓 0-关闭，1-开启"),
    REISSUE_OR_CHANGE_TRADE_AUTO_RULE_MATCH("reissueOrChangeTradeAutoRuleMatch", "0", "换货补发订单创建时同步匹配自动化匹配规则 0-关闭，1-开启"),

    /**
     * 分仓后，异步执行智能分仓类型配置，默认1.实际不再执行，因为订单同步链路上已经执行过一次按规则分仓
     * 1 （按规则分仓
     * 0 （按库存分仓，整单库存不满足自动拆单
     * 2 （按库存分仓，整单库存不满足不拆单
     * 3 （按规则及库存分仓，整单库存不满足自动拆单
     * 4 （按规则及库存分仓，整单库存不满足不拆单
     */
    SPLIT_WAREHOUSE_AUTO_CONFIG_SPLIT_TYPE("splitWarehouseSplitType", "1", " 分仓后，异步执行智能分仓类型配置，默认1"),

    SPLIT_WAREHOUSE_AUTO_CONFIG_SPLIT_TYPE_SET_DEFAULT_WAREHOUSE("setDefaultWarehouse", "0", " 分仓后，异步执行智能分仓类型配置,未匹配时指定默认仓库"),

    WAREHOUSE_STOCK_SKIP_WARHOUSE("warehouseStockSkipWarehouse", "", "指定仓库不参与按库存分仓"),
    WAREHOUSE_STOCK_MATCH_FILTER("warehouseStockMatchFilter", "0", "按库存分仓，满足以下任意条件订单不执行按库存分仓((条件之间是或的关系)"),

    POISON_WAREHOUSE_TRADE_SUB_STOCK("poisonWarehouseTradeSubStock", "0", " 得物云仓订单扣减WMS库存，默认0"),

    JD_IDENTIFY_STORE_TRADE_SUB_STOCK("jdIdentifyStoreTradeSubStock", "0", " 京东鉴定订单默认扣减库存，默认0"),

    OPEN_DELIVER_PRINT("openDeliverPrint", "0", TradeConfigEnum.TRADE_CONFIG, "是否开启发货单"),

    FIRST_PRINT_EXPRESS("firstPrintExpress", "0", TradeConfigEnum.TRADE_CONFIG, "是否先打印快递单"),

    OPEN_IDENT_CODE("openIdentCode", "0", TradeConfigEnum.TRADE_CONFIG, "是否开启识别码输入"),

    POST_OPEN_IDENT_CODE("postOpenIdentCode", "0", TradeConfigEnum.TRADE_CONFIG, "是否开启后置打印识别码输入"),

    OPEN_PRINT_DELAY("openPrintDelay", "0", "是否开启后置打印"),

    OPEN_SEED("openSeed", "0", "是否开启多品多件播种"),

    OPEN_SEED_PRINT("openSeedPrint", "0", "是否开启多品多件播种打印"),

    SEND_GOODS_EXCEPT_UNAUDIT("sendGoodsExceptUnaudit", "0", "发货中的“快递异常”订单自动重新审核订单"),
    FINANCE_REJECT_EXCEPT("financeRejectExcept", "0", "订单财审被拒绝时，自动标记【财审拒绝】异常"),

    SPLIT_AFTER_MATCH_GIFT("splitAfterMatchGift", "0", "拆单后重新计算赠品"),

    SPLIT_AFTER_MATCH_WAREHOUSE("splitAfterMatchWarehouse", "0", "拆单后重新匹配仓库"),

    SPLIT_AFTER_MATCH_EXPRESS("splitAfterMatchExpress", "0", "拆单后自动匹配快递模板"),

    SPLIT_AFTER_MATCH_RULE("splitAfterMatchRule", "0", "重算自动化匹配规则"),

    SHOW_ITEM_TAG("showItemTag", "0", "订单列表中显示商品的商品标签"),

    /**
     * 订单列表中显示商品的平台补贴优惠 0.不展示 1.展示
     */
    SHOW_PLATFORM_DISCOUNT_FEE("showPlatformDiscountFee", "0", "订单列表中显示商品的平台补贴优惠"),

    FILL_SKU_WITH_ORDER_REMARK("fillSkuWithOrderRemark", "0", "填充平台属性名称到订单商品备注"),

    MATCH_WAREHOUSE_AFTER_RECALCULATION_EXPRESS("matchExpressAfterReWareHouse", "0", "修改仓库后重新匹配快递模版"),

    TRADE_COPY_REMATCH_MARK("tradeCopyRematchMark", "0", "复制新建时匹配标记规则"),
    TRADE_COPY_REMATCH_AUTO_RULE("tradeCopyRematchAutoRule","0","复制新建时匹配自动化匹配规则"),

    IMPORT_TRADE_DO_GIFT_MATCH("importTradeDoGifMatch", "0", "订单导入时执行赠品匹配"),

    CHANGE_GOODS_AFTER_MATCH_MERGE_RULE("changeGoodsAfterMatchMergeRule", "0", "换货、补发单创建时执行自动合单"),

    NEW_TRADE_AFTER_MATCH_MERGE_RULE("newTradeAfterMatchMergeRule", "0", "订单新建时执行自动合单"),

    IMPORT_TRADE_AFTER_MATCH_MERGE_RULE("importTradeAfterMatchMergeRule", "0", "订单导入时执行自动合单"),

    IMPORT_TRADE_DO_LABEL_MATCH("importTradeDoLabelMatch", "0", "订单导入时执行自动标记规则"),
    IMPORT_TRADE_DO_AUTO_RULE_MATCH("importTradeDoAutoRuleMatch", "0", "订单导入时执行自动化匹配规则"),

    IMPORT_TRADE_DO_WAREHOUSE_MATCH("importTradeAfterMatchWarehouseRule", "0", "订单导入时匹配分仓规则"),

    IMPORT_TRADE_DO_EXPRESS_MATCH("importTradeAfterMatchExpressSmartRule", "0", "订单导入时匹配快递规则"),

    IMPORT_TRADE_DO_SPILT_MATCH("importTradeAfterMatchSplitRule", "0", "订单导入时匹配拆单规则"),

    IMPORT_TRADE_DO_AUDIT_MATCH("importTradeAfterMatchAuditRule", "0", "订单导入时执行审核规则"),

    //================================================================ 整体设置 ======================================================================

    /**
     * 在订单列表中显示平台的商品图片，已勾选平台则显示商品的平台图片，未勾选的平台默认显示商品系统图片
     */
    ORDER_IMAGE_SOURCE("orderImageSource", "0", TradeConfigEnum.TRADE_CONFIG, "订单列表中显示商品的平台图片"),
    /**
     * [] 全部平台
     */
    PICTURE_FROM_PLATFORM("pictureFromPlatform", "all", TradeConfigEnum.CHAT_CONFIGS, "选择平台"),

    /**
     * 在订单列表中显示平台的商品规格。开启则显示商品的平台规格，关闭则显示系统的规格
     * 前端判断
     */
    SHOW_SKU_PROPERTIES("showSkuProperties", "0", TradeConfigEnum.TRADE_CONFIG, "订单列表中显示商品的平台规格"),

    /**
     * 勾选后，订单列表商品信息中显示平台商品ID，不勾选则不显示
     * 前端判断
     */
    SHOW_TRADE_NUM_IID("showTradeNumIid", "0", TradeConfigEnum.CHAT_CONFIGS, "订单列表中显示商品的平台ID/SKUID"),

    /**
     * 在订单列表中显示平台的商品名称。开启则显示商品的平台规格，关闭则不显示
     * 前端判断
     */
    SHOW_PLAT_TITLE("showPlatTitle", "0", TradeConfigEnum.CHAT_CONFIGS, "订单列表中显示商品的平台名称"),

    SHOW_MAIN_SYS_OUTER_ID("showMainsysouterId", "0", TradeConfigEnum.CHAT_CONFIGS, "订单列表中显示商品的主商家编码"),

    SHOW_SUPPLIER_NAME("showSupplierName", "0", TradeConfigEnum.CHAT_CONFIGS, "订单列表中显示商品的供应商名称"),

    /**
     * 前端判断
     */
    SHOW_GOODS_PURCHASE_LINK("showGoodsPurchaseLink", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "订单列表中显示商品的采购链接"),

    OPEN_TRADE_SALESMAN("openTradeSalesman", "0", "启用业务员业务"),

    OPEN_TRADE_PAYMENT_LINKAGE("openTradePaymentLinkage", "0", TradeConfigEnum.CHAT_CONFIGS, "订单金额联动时，自动创建新的支付单调整金额"),

    /**
     * 1:先执行赠品规则 0:先执行自动标记规则
     * 若先执行标记规则，则标记规则中与商品相关的条件（种类、重量等）均不会包含赠品
     */
    MATCH_TAG_AFTER_GIFT("matchTagAfterGift", "1", TradeConfigEnum.CHAT_CONFIGS, "设置订单下载时，执行赠品规则和自动标记规则的顺序"),

    /**
     * 开启后，只有拥有对应店铺的权限，才能查看并设置该店铺的赠品规则；赠品规则中没有设置店铺的则都能查看
     */
    GIFT_RULE_CHECK_PERMISSION("giftRuleCheckPermission", "0", TradeConfigEnum.CHAT_CONFIGS, "开启赠品规则权限"),

    //================================================================ 平台对接 ======================================================================

    OPEN_UPLOAD_CONSIGN("openUploadConsign", "0", TradeConfigEnum.TRADE_CONFIG, "是否开启上传发货节点"),
    /**
     * 自动上传发货的位置：1、打印 2、包装 3、称重 4、生成波次 5、审核
     * 注意：若开启审核后平台上传发货，则会在审核时自动获取订单的快递单号，获取成功的订单才可上传发货
     * uploadCheckItemNum   1上传单件，2上传多件，3都上传
     */
    UPLOAD_CONSIGN_NAME("uploadConsignName", "0", TradeConfigEnum.TRADE_CONFIG, "向平台上传发货的节点"),
    UPLOAD_CHECK_ITEM_NUM("uploadCheckItemNum", "3", TradeConfigEnum.CHAT_CONFIGS, "请选择自动上传发货的订单类型"),


    /**
     * 注意：① 如果关闭同步待付款订单后，拍下减库存的方式会无效。 ② 此设置对跨境订单无效。 ③除淘宝、天猫、京东平台,其他平台开启后可能导致超卖,需联系客服配置待付款订单库存锁定方式
     */
    SYNC_WAIT_PAY("syncWaitPay", "1", TradeConfigEnum.TRADE_CONFIG, "同步待付款的订单"),

    /**
     * a. 默认不勾选。
     * b. 注释文字：拼团中订单被同步到系统后，自动标记「风控」异常，待订单状态变更后自动取消异常；不可手动取消异常。
     * c. 选择平台：未勾选“同步拼团中订单”，“选择平台”置灰。若勾选，则可选择平台。目前平台仅支持选择“抖音”。
     */
    SYNC_GROUP_BUY_CONFIG("syncGroupBuyConfig", "{\"enable\": 0, \"platform\": \"\"}", "同步拼团中订单"),

    SYNC_1688_TAODADIAN_CONFIG("syn1688TaodadianConfig","{\"enableStatus\": 0, \"userIds\": []}", "同步1688严选订单"),

    /**
     * 注意：开启后，被删除的平台商品状态和信息将无法同步
     */
    ALLOW_REMOVE_ITEM("allowRemoveItem", "0", TradeConfigEnum.CHAT_CONFIGS, "覆盖模式下允许删除平台商品，但至少保留1种平台商品"),
    ALLOW_REMOVE_ALL_PLAT_ITEM("allowRemoveAllPlatItem", "0", TradeConfigEnum.CHAT_CONFIGS, "允许删除所有平台商品"),

    /**
     * openReUploadConsign  注意：开启后，针对平台已发货订单进行重新发货，目前仅支持淘宝/天猫/拼多多
     * openSplitReUploadConsign 开启后，预发货的拆分订单支持重新发货上传
     * openReUploadOtherConsigned   开启后，当有订单中的子单存在其他erp发货的订单时，其他erp发货的子单，上传平台发货
     */
    OPEN_RE_UPLOAD_CONSIGN("openReUploadConsign", "0", TradeConfigEnum.TRADE_CONFIG, "开启重新发货上传"),
    OPEN_SPLIT_RE_UPLOAD_CONSIGN("openSplitReUploadConsign", "0", TradeConfigEnum.TRADE_CONFIG, "预发货的拆分订单重新发货上传"),
    OPEN_RE_UPLOAD_OTHER_CONSIGNED("openReUploadOtherConsigned", "0", TradeConfigEnum.CHAT_CONFIGS, "其他erp发货的订单，支持重新发货配置"),
    OPEN_REISSUE_UPLOAD("openReissueUpload", "0", TradeConfigEnum.CHAT_CONFIGS, "补发单要上传快递单号给平台"),
    /**
     * 开启后，订单在上传平台发货时，如果系统变为交易关闭，则自动过滤不再上传发货
     */
    TRADE_UPLOAD_FILTER_CLOSED("tradeUploadFilterClosed", "0", TradeConfigEnum.CHAT_CONFIGS, "发货上传时，自动过滤交易关闭的订单"),

    /**
     * 界面是有勾选开启框和填写分钟数输入框 逻辑是没有勾选开启框,无论输入框是什么都保存了空字符串 开启则保存数字
     */
    OS_ACTIVITY_DELAY_MINUTE_CHECK("oSActivityDelayMinuteCheck", "0", TradeConfigEnum.CHAT_CONFIGS, "是否开启参与前N有礼活动订单延迟多少分钟后，提交发货"),
    OS_ACTIVITY_DELAY_MINUTE("oSActivityDelayMinute", "", TradeConfigEnum.CHAT_CONFIGS, "参与前N有礼活动订单延迟多少分钟后，提交发货"),

    OPEN_CHEXING_ADD_MEMO("openChexingAddMemo", "1", TradeConfigEnum.CHAT_CONFIGS, "订单同步时，商品车型信息自动填充到系统备注(目前仅支持天猫平台)"),

    DEWU_PLATFORM_DELAY_SHIP_TIME("dewuPlatformDelayShipTime", "", TradeConfigEnum.CHAT_CONFIGS, "毒平台预售订单，延迟多少小时后，提交发货"),
    DEWU_PLATFORM_DELAY_SHIP_TIME_TYPE("dewuPlatformDelayShipTimeType", "0", TradeConfigEnum.CHAT_CONFIGS, "开启毒平台预售订单，延迟时间类型"),

    COMMON_PLATFORM_DELAY_SHIP_CONFIG("commonPlatformDelayShipConfig", CommonPlatformDelayShipConfig.DEFAULT_JSON, "开启系统发货后，延迟上传发货(上传快递单号给平台)"),
    /**
     * 注意：目前仅支持淘宝/天猫/抖音
     */
    SYS_SOURCE_SPLIT_CONSIGN_UPLOAD_ALL("sysSourceSplitConsignUploadAll", "0", TradeConfigEnum.CHAT_CONFIGS, "平台订单拆分结果中有手工订单时，手工订单先上传发货，更新整个平台订单为已发货"),

    /**
     * 拆单订单第一个单（包括手工单）上传发货时对整单进行上传发货
     * 这个配置是针对1688一个平台的
     */
    @Deprecated
    SPLIT_TRADE_CONSIGN_UPLOAD_ALL("splitTradeConsignUploadAll", "0", "拆单订单第一个单（包括手工单）上传发货时对整单进行上传发货"),


    /**
     * 京东默认是这个逻辑
     */
    UPLOAD_ALL_WHEN_UPLOADING_SPLIT("uploadAllWhenUploadingSplit", UploadAllWhenUploadingSplit.DEFAULT_JSON, "拆单订单第一个单（包括手工单）上传发货时对整单进行上传发货"),

    /**
     * 抖音最后一个子订单发货上传时按照剩余购买数量发货
     * <a href="https://gykj.yuque.com/entavv/xb9xi5/okvxq9b6l7vmswcl#tz2lD">业务文档</a>
     */
    CONSIGN_UPLOAD_LAST_SKU_USE_ACTUAL_QUANTITY("consignUploadLastSkuUseActualQuantity", "0", "抖音最后一个子订单发货上传时按照剩余购买数量发货"),

    OPEN_COPY_TRADE_EXPRESS_MEMO("openCopyTradeExpressMemo", "0","复制新建的订单系统发货时，自动上传消息到平台备注"),
    GX_PLATFORM_AHEAD_SHIP_CONFIG("gxPlatformaheadShipConfig", "{\"userIdList\":[],\"aheadShipHour\":\"\",\"autoGainOutSid\":0,\"enableStatus\":0}", "供销订单距离承诺时间不足NH自动发货"),


    PAY_TIME_AHEAD_SHIP_CONFIG("payTimeAheadShipConfig", "{\"userIdList\":[],\"aheadShipHour\":\"\",\"autoGainOutSid\":0,\"enableStatus\":0}", "店铺管理，订单发货支持按照付款时间后N小时上传发货"),

    SUFFICIENT_TRADE_NOT_AUTO_UPLOAD("sufficientTradeNotAutoUpload", "0", "缺货订单不参与自动上传"),

    /**
     * 红旗/黄旗/绿旗/蓝旗   1/2/3/4
     */
    ALIBABA_DEFAULT_SELLER_FLAG("alibabaDefaultSellerFlag", "1", TradeConfigEnum.CHAT_CONFIGS, "1688订单自动更改备注时，原订单无旗帜，那么上传旗帜颜色为：红旗/黄旗/绿旗/蓝旗"),

    /**
     * 存的是逗号隔开的taobaoId
     */
    ADDRESS_CHANGE_APPLIED_USERS("addressChangeAppliedUsers", "", TradeConfigEnum.CHAT_CONFIGS, "买家自主修改地址，需要商家审核"),
    ADDRESS_CHANGE_APPLIED_USERS_TYPE("addressChangeAppliedUsersType", "0", TradeConfigEnum.CHAT_CONFIGS, "开启买家自主修改地址，需要商家审核"),

    JIT_ALLOW_UPDATE_PROD("jitAllowUpdateProd", "0", "唯品会订单允许修改商品"),

    JIT_ALLOW_SAME_COOPERATION_NO_FLAG("jitAllowSameCooperationNoFlag", "0", TradeConfigEnum.CHAT_CONFIGS, "唯品会JIT仅允许相同常态合作编码的PO单生成多PO拣货单"),

    /**
     * 0 手动获取
     * 1 自动获取
     */
    BIC_ACQUIRE_CODE_TYPE("bicAcquireCodeType", "0", TradeConfigEnum.CHAT_CONFIGS, "抖店bic质检订单的订单码获取方式（自动获取 or 手动获取）-- 获取方式"),
    /**
     * 0 一单一码
     * 1 混订单
     */
    BIC_ACQUIRE_CODE_MODE("bicAcquireCodeMode", "", TradeConfigEnum.CHAT_CONFIGS, "抖店bic质检订单的订单码获取方式（自动获取 or 手动获取）-- 获取模式"),

    FORCE_CONSIGN_UPLOAD("forceConsignUpload", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "抖音店铺退款中订单，支持上传发货"),

    /**
     * 注：1、仅支持拼多多和抖音平台；2、仅对平台赠品生效；3、设置项仅对新订单生效
     * 不参与拣选和不验货/参与拣选和不验货/参与拣选和验货/不参与拣选但验货   0/1/2/3
     */
    PLATFORM_GIFT_PICK_CONFIG("platformGiftPickConfig", "1", TradeConfigEnum.TRADE_EXTEND_CONFIG, "平台赠品下载后，是否参与拣选、验货配置"),

    /**
     * 注意：开启后，同步订单时识别天猫订单代发标志同步至系统，关闭后，将无视代发标志视为普通订单下载到系统
     */
    SYNC_PLATFORM_FX_CONFIG("syncPlatformFxConfig", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "同步天猫分销订单"),

    /**
     * 拆单订单系统发货后自动上传快递单号至平台
     * 仅支持淘宝和天猫
     */
    ALLOW_PACKAGES_NOTICE("allowPackagesNotice", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "多包裹物流上传"),

    /**
     * 微信小店（微信视频号）发货信息配置
     */
    WXSPH_CONSIGN_UPLOADER_TYPE("wxsphConsignUploaderType", "0", TradeConfigEnum.CHAT_CONFIGS, "发货上传时,按照从平台同步下来的订单商品信息进行上传"),

    /**
     * 开启得物直发库存发货信息上传（仅针对得物直发，需商家先在后台进行签约，开启后对开启后同步的订单生效）
     */
    @Deprecated
    POISON_TRADE_INFO_UPLOAD("poisonTradeInfoUpload", "0", "开启得物直发库存发货信息上传"),

    /**
     * poison 得物
     * kuaishou 快手
     */
    WAREHOUSE_OPERATION_SYNC_TO_PLATFORM("warehouseOperationSyncToPlatform", "{\"isOpen\":0,\"platformCodes\":[]}", "开启订单仓内作业同步至平台"),

    /**
     * 开启速卖通全托管自动接单
     */
    SMTQTG_TRADE_CONFIRM("smtqtgTradeConfirm", "0", "开启速卖通全托管自动接单"),

    TEMU_AUTO_JOIN_SHIPPING_DESK("temuAutoJoinShippingDesk", "0", "开启后, TEMU订单创建发货单时会把订单自动加入到发货台"),

    TB_XSD_UPLOAD_SET("tbXsdUploadSet", "0", "淘宝小时达O2O设置"),

    /**
     * O2O订单自动拣货完成节点
     */
    O2O_TRADE_AUTO_PICK_SET("o2oTradeAutoPickSet", "1", "O2O订单自动拣货完成节点"),

    /**
     * O2O订单自动接单节点  autoAcceptOrder
     */
    O2O_TRADE_AUTO_ACCEPT_SET("o2oTradeAutoAcceptSet", "1", "O2O订单自动接单节点"),

    /**
     * a. 默认不勾选。
     * b. 注释文字：开启该开关后，美团闪购、抖音小时达和淘宝小时达的订单线上发货不同不线下（自动给订单打"线上发货不同步ERP"，标签，只能由ERP操作发货和扣减库存）
     * c. 选择平台：未勾选“线上发货不同步ERP”，“选择店铺”置灰。若勾选，则可选择店铺。
     */
    O2O_ONLINE_UPLOAD_NO_SYNC_SET("o2oOnlineUploadNoSyncSet", "{\"enableStatus\": 0, \"userIds\": []}", "线上发货不同步ERP"),

    /**
     * 默认关闭
     * 麦芽田账号变更为商家级
     */
    O2O_MAIYATIAN_OPEN("o2oMaiyatianOpen", "0", TradeConfigEnum.CHAT_CONFIGS, "麦芽田账号变更为商家级"),

    //================================================================ 上传平台备注 ======================================================================

    /**
     * 注意：目前仅支持淘宝/天猫/京东/1688/抖音/快手/拼多多/速卖通/shopee
     */
    OPEN_MULTI_OUT_SID_UPLOAD("openMultiOutsidUpload", "0", TradeConfigEnum.TRADE_CONFIG, "一单多包运单号上传平台备注"),

    /**
     * 注意：上传消息内容：快递公司+快递单号+商家编码+数量
     */
    OPEN_UPLOAD_SELLER_MEMO("openUploadSellerMemo", "0", TradeConfigEnum.TRADE_CONFIG, "订单拆分后，拆分出去的订单获取单号时，自动上传消息到平台备注"),

    /**
     * 注意：上传消息内容：快递公司+快递单号
     */
    RESEND_FAIL_ADD_SELLER_MEMO("resendFailAddSellerMemo", "0", TradeConfigEnum.CHAT_CONFIGS, "重新发货上传失败后，自动上消息到备注"),

    @Deprecated
    SPLIT_CONSIGN_UPLOAD_SELLER_MEMO("splitConsignUploadSellerMemo", "0", TradeConfigEnum.CHAT_CONFIGS, "订单拆分后，拆分出去的订单系统发货时，自动上传消息到原订单平台备注"),
    @Deprecated
    SPLIT_CONSIGN_UPLOAD_SELLER_MEMO_CONTENT("splitConsignUploadSellerMemoContent", "", TradeConfigEnum.CHAT_CONFIGS, "备注信息"),
    @Deprecated
    SPLIT_CONSIGN_UPLOAD_SELLER_MEMO_CONTENT_CUSTOM("splitConsignUploadSellerMemoContentCustom", "", TradeConfigEnum.CHAT_CONFIGS, "自定义备注信息"),

    UPLOAD_SELLER_MEMO_WHEN_CONSIGNING_SPLIT("uploadSellerMemoWhenConsigningSplit", UploadSellerMemoWhenConsigningSplit.DEFAULT_JSON, "订单拆分后，拆分出去的订单系统发货时，自动上传消息到原订单平台备注"),

    /**
     * 开启后，当订单审核时，上传平台备注「订单已审核」
     */
    UPLOAD_SELLER_MEMO_AFTER_AUDIT("uploadSellerMemoAfterAudit", "0", TradeConfigEnum.CHAT_CONFIGS, "订单审核上传平台备注"),

    /**
     * 注意：上传消息内容：快递公司+快递单号
     */
    UPLOAD_FAIL_ADD_SELLER_MEMO("uploadFailAddSellerMemo", "", TradeConfigEnum.TRADE_EXTEND_CONFIG, "上传发货失败报错内容包含关键字自动上传消息到卖家备注"),
    IS_UPLOAD_FAIL_ADD_SELLER_MEMO("isuploadFailAddSellerMemo", "", TradeConfigEnum.TRADE_EXTEND_CONFIG, "上传发货失败报错内容包含关键字自动上传消息到卖家备注"),

    //================================================================ 订单处理 ======================================================================

    /**
     * 开启后，订单可以修改订单的商品单价
     */
    OPEN_PRICE_UPDATE("openPriceUpdate", "0", TradeConfigEnum.TRADE_CONFIG, "允许修改订单商品单价"),

    /**
     * 注意：开启该配置后，拼多多订单重新审核时不区分订单状态，都保留原单号
     * 0
     * 1/2/4/8/16 待打印/待包装/待称重/待发货/待财审
     */
    UN_AUDIT_WAYBILL_CANCEL("unAuditWaybillCancel", "0", TradeConfigEnum.TRADE_CONFIG, "发货中订单重新审核（自动重新审核+手工重新审核）时，继续保留原单号"),

    /**
     * 注意：开启后审核完成的订单平台修改备注后自动标记异常
     */
    OPEN_MEMO_EXP("openMemoExp", "0", TradeConfigEnum.TRADE_CONFIG, "平台更改备注的订单自动标记为异常订单"),

    /**
     * 发货中订单平台信息修改后自动反审核订单
     * 0 没有勾选, 1 待打印, 2 待包装, 4 待称重, 8 待发货, 16 待财审 多个取和
     */
    @Deprecated
    AUTO_CANCEL_AUDIT_STATUS("autoCancelAuditStatus", "0", TradeConfigEnum.CHAT_CONFIGS, ", 限定发货中订单状态"),
    /**
     * 0 没有开启自动反审核 | 1 平台修改地址 | 2 平台修改商品 | 4 平台修改备注 | 8 平台申请退款 | 16 平台部分关闭
     */
    @Deprecated
    AUTO_CANCEL_AUDIT("autoCancelAudit", "0", TradeConfigEnum.TRADE_CONFIG, "限定平台信息修改内容"),

    @Deprecated
    AUTO_CANCEL_AUDIT_SELLER_MEMO_KEYWORDS("autoCancelAuditSellerMemoKeywords", AutoCancelAuditSellerMemoKeywords.DEFAULT_JSON, TradeConfigEnum.CHAT_CONFIGS, "平台修改备注关键字设置"),

    AUTO_CANCEL_AUDIT_WHEN_CHANGING("autoCancelAuditWhenChanging", AutoCancelAuditWhenChanging.DEFAULT_JSON, "订单平台信息修改后自动反审核订单"),

    /**
     * 注意：开启后，手工单拆单情况下，平台修改备注时，会同步更新子单备注信息
     */
    COVER_SPLIT_SYS_TRADE_SELLER_MEMO("coverSplitSysTradeSellerMemo", "0", TradeConfigEnum.CHAT_CONFIGS, "平台修改备注信息同步更新子单备注信息"),

    /**
     * 开启后，拼多多订单发货后仍然可以修改快递模版
     */
    ALLOW_CHANGE_TEMPLATE("allowChangeTemplate", "1", TradeConfigEnum.TRADE_CONFIG, "拼多多订单发货后（已发货/交易成功），允许修改快递模版"),

    /**
     * 注意：开启后，系统状态为已发货的订单支持撤销发货，撤销后系统状态变为待审核
     */
    CANCEL_CONSIGN("cancelConsign", "0", TradeConfigEnum.TRADE_CONFIG, "订单支持撤销已发货订单"),

    OPEN_AUTO_AUDIT_MATCH_TEMPLATE("openAutoAuditMatchTemplate", "0", TradeConfigEnum.TRADE_CONFIG, "智能审核时，先执行快递智能匹配，然后执行智能审核"),

    OPEN_AUDIT_MATCH_TEMPLATE("openAuditMatchTemplate", "0", "订单审核时，先执行快递智能匹配，然后执行审核"),

    /**
     * 开启后，订单审核时将会自动获取单号
     */
    AUDIT_AUTO_GET_WAYBILL_CODE("auditAutoGetWaybillCode", "0", TradeConfigEnum.TRADE_CONFIG, "订单审核时是否自动获取单号"),
    /**
     * 订单审核时是否自动获取单号指定仓库
     */
    AUDIT_GET_WAYBILL_CODE_FILTER_WAREHOUSE("auditGetWaybillCodeFilterWarehouse", "", "订单审核时是否自动获取单号指定仓库"),
    /**
     * 开启后，跨境订单审核时将会自动获取单号
     */
    ABROAD_AUDIT_AUTO_GET_WAYBILL_CODE("abroadAuditAutoGetWaybillCode", "0", TradeConfigEnum.CHAT_CONFIGS, "跨境订单订单审核时是否自动获取单号"),
    /**
     * 勾选后，若订单使用了快运模板，则订单审核时会自动获取单号；否则不会自动获取运单号
     */
    KY_AUDIT_AUTO_GET_WAYBILL_CODE("kyAuditAutoGetWaybillCode", "0", TradeConfigEnum.CHAT_CONFIGS, "快运订单也自动获取单号"),

    IS_COVER_WEIGHT("isCoverWeight", "0", TradeConfigEnum.CHAT_CONFIGS, "包裹未称重时，包裹重量直接取商品净重，且发货时根据商品净重计算实际运费"),

    MATCH_FREIGHT_ZERO_WEIGHT("matchFreightZeroWeight", "0", TradeConfigEnum.CHAT_CONFIGS, "开启订单重量（商品净重/包裹重量）为0时，自动根据运费设置计算出运费（理论运费和实际运费）"),

    EXPRESS_MATCH_FILTER_EXIST("expressMatchFilterExist", "0", TradeConfigEnum.CHAT_CONFIGS, "快递智能匹配时，只处理未分配快递的订单，自动过滤已分配过模版的订单"),

    /**
     * 开启后，审核后的订单将自动把留言和备注标记为已处理
     */
    IS_HAND_MEMO_MESSAGE_AUDIT("isHandMemoMessageAudit", "0", TradeConfigEnum.CHAT_CONFIGS, "审核后是否自动标记留言备注为已处理"),

    OPEN_PART_REFUND("openPartRefund", "0", TradeConfigEnum.CHAT_CONFIGS, "订单已审核发货中，当部分商品退款成功后，订单自动标记【部分关闭】"),

    AUTO_WAIT_AUDIT_UNDO_MERGE("autoWaitAuditUndoMerge", "0", TradeConfigEnum.CHAT_CONFIGS, "平台修改地址后，对于待审核的合并订单自动取消合单"),

    /**
     * 注意：改配置前置锁库存和后置锁库存都适用
     */
    AUDIT_AUTO_INSUFFICIENT_CANCELED("auditAutoInsufficientCanceled", "0", TradeConfigEnum.CHAT_CONFIGS, "订单系统审核（自动/智能/手动审核/财务审核）时，忽略库存限制，库存不足仍可通过审核通过(该配置前置锁库存和后置锁库存都适用)"),

    /**
     * 注意：改配置仅适用后置锁库存都
     */
    AUDIT_INCLUED_INSUFFICIENT_STOCK("auditIncluedInsufficientStock", "0", TradeConfigEnum.CHAT_CONFIGS, "系统审核 （智能/手动/财务）时，对正常订单和仅【缺货】异常订单执行审核，符合条件即可通过审核，不符合仍审核不通过(该配置仅适用于后置锁库存)"),

    AUDIT_STOCK_ALLOW_APPLY_SINGLE("auditStockAllowApplySingle", "0", "后置审核时，组合装/加工商品库存不足时支持申请对应单品库存"),

    OPEN_SCALP_NOT_APPLY_STOCK("openScalpNotApplyStock", "0", TradeConfigEnum.CHAT_CONFIGS, "空包订单不锁库存"),

    OPEN_REFUND_NOT_APPLY_STOCK("openRefundpNotApplyStock", "0", TradeConfigEnum.CHAT_CONFIGS, "退款中商品不锁库存"),

    /**
     * 注意：只对保存后匹配/取消快递异常的订单生效
     */
    EXCEPTION_NOT_LOCK_STOCK("exceptionNotLockStock", "0", "通过自动标记规则匹配的快递异常不锁库存"),

    REPRINT_FILTER_CONSIGNED("reprintFilterConsigned", "1", TradeConfigEnum.CHAT_CONFIGS, "订单系统发货后再进行打印时，自动过滤【交易关闭】，【其他ERP发货】的商品不打印"),

    /**
     * 注意：部分退款的订单（含合单）也会自动过滤掉
     */
    FILTER_REFUND_ORDERS("filterRefundOrders", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "异常订单进行打印发货时自动过滤掉退款中的订单"),

    @Deprecated
    AUTO_SUIT_TRANSFORM_SINGLE("autoSuitTransformSingle", "0", TradeConfigEnum.CHAT_CONFIGS, "未发货订单首次同步系统时，套件商品自动转为单品"),
    @Deprecated
    INCLUDE_EXCLUDE_PRODUCT_OUTER_ID("includeExcludeProductOuterId", "", "指定/排除套件商品"),

    SUIT_TO_SINGLE_WHEN_SYNCING("suitToSingleWhenSyncing", SuitToSingleWhenSyncing.DEFAULT_JSON, "未发货订单首次同步系统时，套件商品自动转为单品"),

    AUDIT_SUIT_TRANSFORM_SINGLE("auditSuitTransformSingle", "0", TradeConfigEnum.CHAT_CONFIGS, "订单审核（自动/智能/手动/缺货审核）时，审核通过后套件自动转单品"),

    AUDIT_INCLUDE_EXCLUDE_PRODUCT_OUTER_ID("auditIncludeExcludeProductOuterId", "", "指定/排除套件商品"),

    /**
     * 0 未开启
     * WAIT_BUYER_PAY,WAIT_AUDIT,FINISHED_AUDIT,SELLER_SEND_GOODS
     * 未付款 待审核 已审核 已发货
     */
    CAN_MERGE_CHECK_SYS_STATUS("canMergeCheckSysStatus", "", TradeConfigEnum.CHAT_CONFIGS, "需要合并提示的订单状态"),

    MERGE_REMARK_CONTAIN_EXCEPTION("mergeRemarkContainException", "1", TradeConfigEnum.CHAT_CONFIGS, "异常订单是否参与计算"),

    AUTO_REMARK_MERGE_EXCEPTION("autoRemarkMergeException", "0", TradeConfigEnum.CHAT_CONFIGS, "有合并提示的订单自动标记【等待合并】异常"),

    /**
     * 待打印 WAIT_EXPRESS_PRINT | 待包装 WAIT_PACKAGE | 待称重 WAIT_WEIGHT | 待发货 WAIT_SEND_GOODS
     * ["WAIT_EXPRESS_PRINT", "WAIT_PACKAGE", "WAIT_WEIGHT", "WAIT_SEND_GOODS"]
     */
    CAN_AUDIT_UNDO_AUTO_SYS_STATUE("canAuditUndoAutoSysStatus", "0", TradeConfigEnum.CHAT_CONFIGS, "有合单提示需要自动反审核的订单状态"),

    /**
     * 注意：开启后，赠品会跟随订单中缺货商品，不会单独拆分成为一个订单
     */
    SPLIT_INSUFFICIENT_ALLOW_GIFT("splitInsufficientAllowGift", "0", TradeConfigEnum.CHAT_CONFIGS, "缺货拆分时，不允许单独把赠品拆出去"),


    /**
     * 勾选后，想通店铺，相同买家账号，相同收货人信息，订单商品信息完全一致的未发货货到付款订单，只保留一张为正常订单，其他都为重复货到付款订单
     */
    OPEN_CHECK_REPEAT_COD("openCheckRepeatCod", "0", TradeConfigEnum.CHAT_CONFIGS, "判断重复货到付款订单"),


    // -------------------------审核开始---------------------//
    INSUFFICIENT_ANALYZE_AUDIT("insufficientAnalyzeAudit", "0", TradeConfigEnum.CHAT_CONFIGS, "后置锁单：缺货分析时，分析成功的订单自动执行审核"),

    SPLIT_AUTO_AUDIT("splitAutoAudit", "0", TradeConfigEnum.CHAT_CONFIGS, "后置锁单：订单按商品库存/缺货拆分时，拆分出去的正常有货并且审核过的订单，自动执行审核"),

    // -------------------------审核结束---------------------//

    /**
     * 注意：只针对淘宝平台且已签约“翱象”服务的商家；勾选后，若平台有建议仓则不会执行系统“分配仓库”规则而直接用平台建议仓
     */
    WAREHOUSE_RECOMMENDATION("warehouseRecommendation", "0", "订单同步进系统时，发货仓取平台建议仓库"),

    CHANGE_WAREHOUSE_CHECK_PERMISSION("changeWarehouseCheckPermission", "0", TradeConfigEnum.CHAT_CONFIGS, "修改仓库时，允许修改成无权限的仓库"),

    CANCEL_EXCEPTION_IS_ALL("cancelExceptionIsAll", "0", TradeConfigEnum.CHAT_CONFIGS, "取消异常，默认取消所有异常"),

    AUTO_REMARK_ITEM_SHUT_OFF("autoRemarkItemShutoff", "0", TradeConfigEnum.CHAT_CONFIGS, "商品停用后，未发货商品的订单自动标记【商品停用】异常"),

    ITEM_SHUTOFF_ADD_MEMO("itemShutoffAddMemo", "0", TradeConfigEnum.CHAT_CONFIGS, "商品停用自动增加卖家备注"),

    OPEN_IMPORT_AUTO_SET_PAYMENT("openImportAutoSetPayment", "0", TradeConfigEnum.CHAT_CONFIGS, "订单导入时，商品付款金额设置为非必填"),

    QUERY_BY_OUTER_ID_EXCLUDE_CLOSED_TRADE("queryByOuterIdExcludeClosedTrade", "0", TradeConfigEnum.CHAT_CONFIGS, "订单管理/订单处理界面，根据商家编码查询订单时，排除交易关闭的商品所对应的订单"),

    QUERY_BY_OUTER_ID_EXCLUDE_CONSIGN_TRADE("queryByOuterIdExcludeConsignTrade", "0", "订单列表根据商品信息查询订单时，排除订单中已发货的订单商品"),

    AUTO_REMARK_PRICE_EXCEPTION_TAG("autoRemarkPriceExceptionTag", "0", TradeConfigEnum.CHAT_CONFIGS, "商品折后价小于等于商品成本价时，自动标记【单价异常】系统标签"),

    OPEN_SUIT_SINGLE_RATIO_SHARE("openSuitSingleRatioShare", "0", TradeConfigEnum.CHAT_CONFIGS, "套件/组合装/加工商品按金额占比进行分摊"),
    openWaitAuditSuitSingleRatioShare("openWaitAuditSuitSingleRatioShare", "0", TradeConfigEnum.CHAT_CONFIGS, "商品档案金额占比发生变化时，待审核订单金额重新分摊"),

    /**
     * 0 按子商品销售价 | 1 按子商品移动加权成本价 | 2 按子商品历史成本价
     */
    SUITS_SUB_ORDER_FEE_SHARE("suitsSubOrderFeeShare", "0", TradeConfigEnum.CHAT_CONFIGS, "套件/组合装/加工商品金额分摊逻辑"),

    GIFT_ORDER_SALE_PRICE_CONFIG("giftOrderSalePriceConfig", "0", TradeConfigEnum.CHAT_CONFIGS, "线上分销订单，匹配赠品时，赠品的分销价读取客户-分销价管理"),

    /**
     * 订单列表中, "分销"和"成本"字段的取值配置: (以下两个字段必须要有一个字段是取供销商的商品分销价, 不然分销订单无法扣减流水)
     * 订单SalePrice字段的取值源 0 客户模块分销价  1 供分销模块分销价(非分销订单展示0)
     */
    FX_SALE_PRICE_FIELD_SOURCE("fxSalePriceFieldSource", "0", TradeConfigEnum.CHAT_CONFIGS, "订单列表中，“分销”字段取值逻辑"),
    /**
     * 订单cost字段的取值源   0 供分销模块分销价(非分销订单展示商品成本价) 1 商品成本价
     */
    FX_COST_FIELD_SOURCE("fxCostFieldSource", "0", TradeConfigEnum.CHAT_CONFIGS, "订单列表中，“成本”字段取值逻辑"),

    /**
     * 当商品标记停用时，未审核订单会自动拆分停用的商品，已审核订单不处理
     */
    ITEM_INACTIVE_AUTO_SPLIT("itemInactiveAutoSplit", "0", TradeConfigEnum.CHAT_CONFIGS, "商品标记停用，订单自动执行拆分"),

    BUYER_SELF_UPDATE_SHIPPING_ADDRESS_FAIL("buyerSelfUpdateShippingAddressFail", "0", TradeConfigEnum.CHAT_CONFIGS, "卖家自助修改地址失败后，自动增加系统标签【自主改地址失败】"),

    OPEN_NEW_TRADE_IMPORT("openNewTradeImport", "0", TradeConfigEnum.CHAT_CONFIGS, "订单导入时，当收件人信息, 平台单号一致时，自动识别导入为同一笔订单"),

    /**
     * 1.仅支持待审核订单进行拆分
     * 2.开启平台申请退款/部分关闭自动反审核后，发货中订单先反审核，然后再执行拆分
     * 3.订单中全为平台商品时，可以进行拆分
     * 4.订单中包含系统商品时，必须至少有2种以上（含2种）待审核平台商品的订单可以进行拆分
     * 不能和rematchGift同时开启
     */
    REFUND_AUTO_SPLIT("refundAutoSplit", "0", TradeConfigEnum.CHAT_CONFIGS, "待审核多sku订单，当其中一个sku发起退款/同意退款后，自动将退款sku拆分出去"),

    HALF_AUTO_UNLOCK("halfAutoUnlock", "0", TradeConfigEnum.CHAT_CONFIGS, "手动挂起订单时，支持指定时长后自动解除挂起"),

    /**
     * 标记异常： 例如：挂起订单 EX_HALT 多个逗号隔开
     * 限定发货中订单状态: 1 待打印 | 2 待包装 | 4 待称重 | 8 待发货 多个取和
     */
    AUTO_RE_AUDIT_EXCEPTION("autoReAuditException", "", TradeConfigEnum.TRADE_EXTEND_CONFIG, "发货中订单标记异常时，自动重新审核订单"),
    AUTO_RE_AUDIT_STATUS("autoReAuditStatus", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "限定发货中订单状态"),

    /**
     * 注意：1.建议使用单一符号，如#、&等；2.解析时，匹配起始关键字与截止关键字间的字符串作为商品编码；3.若仅设置起始/截止关键字，则匹配起始关键字后/截止关键字前的字符串作为商品编码
     * 存储json 对应实体
     */
    ITEM_KEYWORD_PARSE("itemKeywordParse", "", TradeConfigEnum.CHAT_CONFIGS, "订单下载后通过关键字解析并匹配商品"),

    ALLOW_PARTY3_GET_WAYBILL("allowParty3GetWaybill", "0", TradeConfigEnum.CHAT_CONFIGS, "三方仓订单允许获取快递单号"),

    /**
     * json
     */
    PARTY3_WAREHOUSE_ORDER_SPECIL_EXCE_NOT_CANCEL("party3WarehouseOrderSpecilExceNotCancel", "", TradeConfigEnum.CHAT_CONFIGS, "已经成功推送三方仓订单新增对应关系改动异常不请求WMS侧取消发货单"),

    NO_AUTO_CHECK_SEND_INSUFFICIENT("noAutoCheckSendInsufficient", "0", TradeConfigEnum.TRADE_CONFIG, "缺货审核/缺货发货操作不需要标记缺货发货标签"),

    /**
     * 只支持天猫和淘宝
     */
    SHOP_PRESELL_MATCH_TAG("shopPresellMatchTag", "0", TradeConfigEnum.CHAT_CONFIGS, "店铺预售商品打上店铺预售标记"),

    /**
     * 赠送节点，1.下单送赠品；2.付款送赠品；3.全部付款送赠品
     */
    GIFT_MATCH_NODE_CONFIG("giftMatchNodeConfig", "2", TradeConfigEnum.TRADE_EXTEND_CONFIG, "赠送节点"),

    /**
     * 1 订单同步的时候执行 | 2 订单解锁的时候执行
     * 选择同步的时候执行则订单同步时执行赠品规则；选择解锁的时候执行则订单解锁时执行赠品规则。为了避免出现重复送和少送的情况，请不要随意切换选项
     */
    SYS_PRESELL_GIFT_MATCH_CONFIG("sysPresellGiftMatchConfig", "1", TradeConfigEnum.TRADE_EXTEND_CONFIG, "系统预售订单赠品规则执行逻辑"),

    AUTO_CREATE_VIP_PICK("autoCreateVipPick", "0", TradeConfigEnum.CHAT_CONFIGS, "唯品会JIT业务自动获取PO单并生成拣货单"),

    TRADE_JITX_FEEDBACK_PLATFORM_WAREHOUSE("tradeJitxFeedbackPlatformWarehouse", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "唯品会JITX自动反馈寻仓结果"),

    /**
     * 有异常订单同正常普通订单一样反馈寻仓成功
     */
    TRADE_JITX_EXCEP_FEEDBACK_SUCCESS("tradeJitxExcepFeedbackSuccess", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "有异常订单不自动反馈寻仓失败"),

    /**
     * 0 商品捡货数
     * 1 商品实际装箱数
     */
    VIPJIT_UPLOAD_BY_PRODUCT_ACTUAL_NUM("vipjitUploadByProductActualNum", "0", TradeConfigEnum.CHAT_CONFIGS, "唯品会出仓单发货上传时，商品数量按照选择维度进行上传"),

    /**
     * 注意：开启配置后，修改商品/换商品/明细换货，朱商家编码支持带入原商品主商家编码，仅支持平行结构
     */
    ORDER_PRODS_QUERY_TYPE("orderProdsQueryType", "0", TradeConfigEnum.CHAT_CONFIGS, "修改/换商品时，支持带入原商品主商家编码"),

    ORDER_NUM_RESET_BY_MERCHANT_CODE("orderNumResetByMerchantCode", "0", TradeConfigEnum.CHAT_CONFIGS, "根据系统商家编码中的除以自动计算商品的下单数量，如果除不尽，需要标记数量异常的系统标签"),

    /**
     * insufficientAnalyzeExcludeExcep 开关
     * 系统异常 自定义异常分开存储
     * "insufficientAnalyzeExcludeExcepSys": "EXCEP_HALT,EXCEP_REFUND,EX_CHANGE_ADDRESS",
     * "insufficientAnalyzeExcludeExcepSelf": "3000000001,2000000004,2000000003,2000000002",
     */
    INSUFFICIENT_ANALYZE_EXCLUDE_EXCEP("insufficientAnalyzeExcludeExcep", "0", TradeConfigEnum.CHAT_CONFIGS, "后置锁单：缺货分析时，支持排除指定异常"),
    INSUFFICIENT_ANALYZE_EXCLUDE_EXCEP_SYS("insufficientAnalyzeExcludeExcepSys", "", TradeConfigEnum.CHAT_CONFIGS, "系统异常"),
    INSUFFICIENT_ANALYZE_EXCLUDE_EXCEP_SELF("insufficientAnalyzeExcludeExcepSelf", "", TradeConfigEnum.CHAT_CONFIGS, "自定义异常"),

    /**
     * 开启后，订单发货会判断是否为代发订单且余额是否足够, 余额不够发货失败
     */
    TRADE_CONSIGN_VERIFY_CUSTOMER_BALANCE("tradeConsignVerifyCustomerBalance", "0", TradeConfigEnum.CHAT_CONFIGS, "订单发货时判断是否为代发类型订单"),

    LAZADA_FBL_AUTO_SPLIT("lazadaFblAutoSplit", "0", TradeConfigEnum.CHAT_CONFIGS, "拉取订单时，当订单中有FBL/二次销售的sku，自动将这些sku拆分出去；如果sku全部都是FBL/二次销售的则不执行（仅支持Lazada平台）"),

    AUTHOR_HIDE_OR_DISPLAY("authorHideOrDisplay", "1", TradeConfigEnum.TRADE_EXTEND_CONFIG, "展示达人信息"),

    DISTRIBUTOR_HIDE_OR_DISPLAY("distributorHideOrDisplay", "0", "展示团长信息"),

    /**
     * 0 订单实付金额-订单成本-实际运费-包材费用 | 1 订单平台实付金额+运费收入-订单成本-实际运费-包材费用
     */
    GROSS_CACULATE_TYPE("grossCaculateType", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "订单利润计算公式"),

    /**
     * 目前只针对使用菜鸟电子面单、抖音电子面单的订单进行收货地址是否可达判断
     */
    EXPRESS_REACHABLE("expressReachable", "1", TradeConfigEnum.CHAT_CONFIGS, "自动匹配快递模板或手工选择快递模板时判断收件地址是否可达（不可达会自动标记快递异常）"),

    /**
     * 计算单品数量
     */
    ITEM_QUANTITY_EXCLUDE_VIRTUAL("itemQuantityExcludeVirtual", "0", "计算单品数量时排除虚拟商品"),

    /**
     * 智能计算单品数量时剔除无需发货商品
     */
    ITEM_QUANTITY_EXCLUDE_NON_CONSIGN("itemQuantityExcludeNonConsign", "0", "计算单品数量时排除无需发货商品"),
    ITEM_QUANTITY_SUIT_SELF("itemQuantitySuitSelf", "0", "计算单品数量时计算套件本身"),

    TRADE_PLAT_STATUS_CHANGE_CANCEL_OR_HALT("platStatusChangeCancelOrHalt", "0", "平台单复制新建订单后平台单整单交易关闭,自动作废复制新建的订单,若部分交易关闭或部分退款中或整单退款中则自动挂起"),

    /**
     * 订单修改商品不自动更新供应商
     */
    MODIFY_ITEM_UN_SYNC_SUPPLIER("modifyItemUnSyncSupplier", "0", "订单修改商品不自动更新供应商"),

    DELIVERY_GT48H_MARK_FULL_PRE_SELL("deliveryGt48hMarkFullPreSell", "0", "抖音新现货+预售商品发货时效大于48小时标记全款预售"),

    /**
     * 修改仓库保留快递模板和单号
     * @see <a href="https://gykj.yuque.com/entavv/xb9xi5/flh10ovhkre1clgp">语雀文档</a>
     */
    PRE_UPLOAD_WAREHOUSE_CHANGE_KEEP_EXPRESS("preUploadWarehouseChangeKeepExpress", "0", "修改仓库保留快递模板和单号"),

    /**
     * 修改仓库保留快递模板和单号的订单类型
     * 1: 仅预发货订单
     * 2: 任何订单
     *
     * @see <a href="https://gykj.yuque.com/entavv/xb9xi5/flh10ovhkre1clgp">语雀文档</a>
     */
    WAREHOUSE_CHANGE_KEEP_EXPRESS_TRADE_TYPE("warehouseChangeKeepExpressTradeType", "1", "修改仓库保留快递模板和单号的订单类型"),



    FRONT_MODAL_TO_PROFESSIONAL_PRINT("frontModalToProfessionalPrint", "0", "前置打印弹窗支持多平台打印"),

    REISSUE_GX_CAL_FLOW("reissueGxCalFlow", "0", "分销商补发类型的订单，扣减流水（重算资金流水时也会扣减重算）"),

    FX_REISSUE_CAL_FLOW("fxReissueCalFlow", "", "分销商推送的补发订单扣减流水"),

    GX_REISSUE_CAL_FLOW("gxReissueCalFlow", "", "供销商创建的补发订单扣减流水"),

    FX_CHANGE_ITEM_CAL_FLOW("fxChangeItemCalFlow", "1", "分销商推送的换货订单扣波流水"),

    GX_CHANGE_ITEM_CAL_FLOW("gxChangeItemCalFlow", "1", "供销商创建的换货订单扣减流水"),

    //================================================================ 重算配置 ======================================================================

    /**
     * 开启后，订单交易关闭时会重新进行赠品规则的匹配
     * 注意：开启后只有待审核订单会重新匹配赠品；不能同时开启"待审核多SKU订单，当其中一个SKU发起退款/同意退款后，自动将退款SKU拆分出去"
     * 不能和refundAutoSplit 同时开启
     */
    REMATCH_GIFT("rematchGift", "0", TradeConfigEnum.TRADE_CONFIG, "交易关闭时重新匹配赠品规则"),

    /**
     * 开启后，待审核的单，平台修改商品后会自动清空当前已经匹配的赠品
     */
    @Deprecated
    PLATFORM_UPDATE_ITEM_REMATCH_GIVEAWAY("platformUpdateItemRematchGiveaway", "0", TradeConfigEnum.CHAT_CONFIGS, "平台修改商品后重新匹配赠品"),

    /**
     * /开启后，修改地址成功后会重新匹配赠品规则
     */
    ADDRESS_CHANGE_GIFT("addressChangeGift", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "平台修改地址后重新匹配赠品"),

    UN_AUDIT_REPLACE_INSUFFICIENT_CANCELED("unAuditReplaceInsufficientCanceled", "0", TradeConfigEnum.TRADE_CONFIG, "重新审核时重新计算缺货发货订单库存"),

    /**
     * json
     */
    TRADE_EXCEPTION_REMARK("tradeExceptionRemark", "{\"enableStatus\":false,\"changeMessage\":\"RECIVER_ADDRESS\",\"userIdList\":[],\"remarkType\":0,\"checkAllShop\":0}", TradeConfigEnum.CHAT_CONFIGS, "订单修改收件人信息时，重新匹配标签，便签及异常"),

    CHANGE_PDD_LOGISTICS_INFO_EXPRESS("changePddLogisticsInfoExpress", "0", TradeConfigEnum.CHAT_CONFIGS, "拼多多买家指定物流信息变更重新匹配快递"),

    TRADE_ASYNC_RE_MATCH_TAG_FROM_WAREHOUSE_MATCH("tradeAsyncReMatchTagFromWarehouseMatch", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "订单下载时商品按仓库拆分后也重新计算标记规则"),

    SPLIT_RECALCULATION_TAG("splitRecalculationTag", "0", TradeConfigEnum.CHAT_CONFIGS, "拆单后重新计算标记规则"),

    MATCH_TAG_EXCLUDE_MANUAL("matchTagExcludeManual", "0", TradeConfigEnum.CHAT_CONFIGS, "重新计算标记规则时，只重算自动标记的异常和标签，不重算手动打的异常和标签"),

    /**
     * 卖家备注变更后
     * 重新匹配赠品   开启后, 待审核的单, 卖家备注更新后会自动清空当前已经匹配到的赠品重新匹配一次
     * 重新匹配快递   只重算开启的规则，匹配成功后直接更改为新快递模板，匹配失败后自动清空原模板
     */
    MATCH_GIFT_AFTER_SELLER_MEMO_CHANGED("matchGiftAfterSellerMemoChanged", "0", TradeConfigEnum.CHAT_CONFIGS, "重新匹配赠品"),
    CHANGE_MEMO_RECALCULATION_EXPRESS("changeMemoRecalculationExpress", "0", TradeConfigEnum.CHAT_CONFIGS, "重新匹配快递"),

    /**
     * 卖家备注变更后，已有快递单号的订单也重算快递
     */
    CHANGE_MEMO_RE_EXPRESS_CONTAIN_OUT_SID("changeMemoReExpressContainOutSid", "0", TradeConfigEnum.CHAT_CONFIGS, "卖家备注变更后，已有快递单号的订单也重算快递"),

    /**
     * 卖家备注/订单旗帜变更后
     * 重新匹配仓库
     * 重新计算标记规则 只重算包含卖家备注和订单旗帜的规则
     * 重算执行自动拆单
     */
    CHANGE_MEMO_RECALCULATION_WAREHOUSE("changeMemoRecalculationWarehouse", "0", TradeConfigEnum.CHAT_CONFIGS, "重新匹配仓库"),
    CHANGE_MEMO_RECALCULATION_TAG("changeMemoRecalculationTag", "0", TradeConfigEnum.CHAT_CONFIGS, "重新计算标记规则"),

    CHANGE_MEMO_RECALCULATION_SPLIT("changeMemoRecalculationSplit", "0", "重算执行自动拆单"),

    CHANGE_MEMO_RECALCULATION_RULE("changeMemoRecalculationRule", "0","重算自动化匹配规则"),


    /**
     * 卖家备注/订单旗帜变更后，是否已审核订单也重算计算标记规则
     */
    CHANGE_MEMO_REMATCH_TAG_AUDITED("changeMemoRematchTagAudited", "0", TradeConfigEnum.CHAT_CONFIGS, "已审核订单重算标签"),

    /**
     * 卖家备注/订单旗帜变更后, 重算关键字配置
     */
    CHANGE_MEMO_REMATCH_KEYWORD_SETTINGS("changeMemoRematchKeywordSettings", "{\"enableStatus\":1,\"sellerFlags\":[],\"sellerMemos\":\"\"}", "重算关键字设置"),


    /**
     * 合单后
     * 重新计算赠品
     * 重新匹配仓库
     * 重新匹配快递模板
     * 重新计算标记规则
     */
    MERGE_AFTER_MATCH_GIFT("mergeAfterMatchGift", "0", TradeConfigEnum.CHAT_CONFIGS, "重新计算赠品"),
    MERGE_AFTER_MATCH_WAREHOUSE("mergeAfterMatchWarehouse", "0", TradeConfigEnum.CHAT_CONFIGS, "重新匹配仓库"),
    MERGE_AFTER_MATCH_EXPRESS("mergeAfterMatchExpress", "0", TradeConfigEnum.CHAT_CONFIGS, "重新匹配快递模板"),
    MERGE_RECALCULATION_TAG("mergeRecalculationTag", "0", TradeConfigEnum.CHAT_CONFIGS, "重新计算标记规则"),

    MERGE_AFTER_MATCH_RULE("mergeAfterMatchRule", "0", "重算自动化匹配规则"),

    /**
     * 取消合单后
     * 重新计算赠品
     * 重新匹配快递模板
     */
    UNDO_MERGE_AFTER_MATCH_GIFT("undoMergeAfterMatchGift", "0", TradeConfigEnum.CHAT_CONFIGS, "重新计算赠品"),
    UNDO_MERGE_AFTER_MATCH_EXPRESS("undoMergeAfterMatchExpress", "0", TradeConfigEnum.CHAT_CONFIGS, "重新匹配快递模板"),

    UNDO_MERGE_AFTER_MATCH_RULE("undoMergeAfterMatchRule", "0", "重算自动化匹配规则"),

    //================================================================ 包装验货 ======================================================================

    /**
     * openScanItemPackma   开启后，包装验货和包裹称重时支持扫描包材
     * packmaIndex  0 包装验货| 1 包裹称重
     */
    @Deprecated
    OPEN_SCAN_ITEM_PACKMA("openScanItemPackma", "0", TradeConfigEnum.TRADE_CONFIG, "开启包装验货/包裹称重支持扫包材"),
    @Deprecated
    PACKMA_INDEX("packmaIndex", "0", TradeConfigEnum.CHAT_CONFIGS, "包装验货|包裹称重"),
    @Deprecated
    OPEN_FORCE_PACKAGE_SCAN("openForcePackageScan", "0", TradeConfigEnum.CHAT_CONFIGS, "强制扫描包材"),
    @Deprecated
    OPEN_FORCE_PACKAGE_SCAN_TYPE("openForcePackageScanType", "0", TradeConfigEnum.CHAT_CONFIGS, "强制扫描包材类型"),
    @Deprecated
    OPEN_SINGLE_PACKAGE_SCAN_AUTO_SUBMIT("openSinglePackageScanAutoSubmit", "0", TradeConfigEnum.CHAT_CONFIGS, "扫单个包材自动确认"),

    SUPPORT_SCANNING_PACKAGING("supportScanningPackaging", SupportScanningPackaging.DEFAULT_JSON, "开启包装验货/包裹称重支持扫包材"),

    WEIGH_SUPPORT_SWITCH_ACCOUNT("weighSupportSwitchAccount", "0", TradeConfigEnum.CHAT_CONFIGS, "包裹称重（发货前/发货后），允许选账号进行称重"),

    REGISTER_SUPPORT_SWITCH_ACCOUNT("registerSupportSwitchAccount", "0", TradeConfigEnum.CHAT_CONFIGS, "验货登记，允许选账号进行称重"),

    PACK_SUPPORT_SWITCH_ACCOUNT("packSupportSwitchAccount", "0", TradeConfigEnum.CHAT_CONFIGS, "包装验货（包装验货/后置打印/播种打印/波次批打），允许选账号进行验货/播种"),

    /**
     * tradeConfig.seedSplitAutoGetWaybill tradeConfig.chatConfigs.seedSplitAutoGetWaybill 两个地方都有值
     */
    SEED_SPLIT_AUTO_GET_WAYBILL("seedSplitAutoGetWaybill", "0", TradeConfigEnum.TRADE_CONFIG, "播种拆分订单自动获取运单号"),

    /**
     * 配置包装验货及包装称重时的语音提示
     * openPackVoiceHints   是否开启
     */
    OPEN_PACK_VOICE_HINTS("openPackVoiceHints", "0", TradeConfigEnum.TRADE_CONFIG, "语音提示包配置"),
    OPEN_CLOSE_ORDER_VOICE("openCloseOrderVoice", "0", TradeConfigEnum.CHAT_CONFIGS, "包装验货-有退款"),
    OPEN_PACK_EXPRESS_VOICE_HINTS("openPackExpressVoiceHints", "0", TradeConfigEnum.CHAT_CONFIGS, "包装验货-快递公司"),
    OPEN_GIFT_VOICE_HINTS("openGiftVoiceHints", "0", TradeConfigEnum.TRADE_CONFIG, "包装验货-注意赠品"),
    OPEN_MESSAGE_VOICE_HINTS("openMessageVoiceHints", "0", TradeConfigEnum.TRADE_CONFIG, "包装验货-注意留言备注"),
    OPEN_REMARK_VOICE_HINTS("openRemarkVoiceHints", "0", TradeConfigEnum.TRADE_CONFIG, "包装验货-注意系统备注"),
    OPEN_INVOICE_VOICE_HINTS("openInvoiceVoiceHints", "0", TradeConfigEnum.TRADE_CONFIG, "包装验货-注意开票"),
    OPEN_PRINT_VOICE_HINTS("openPrintVoiceHints", "0", TradeConfigEnum.CHAT_CONFIGS, "包装验货-该订单已打印"),
    OPEN_SINGLE_TRADE_SOUND("openSingleTradeSound", "0", TradeConfigEnum.CHAT_CONFIGS, "包装验货-单件"),
    OPEN_COMPLETE_VOICE("openCompleteVoice", "0", TradeConfigEnum.CHAT_CONFIGS, "包装验货-完成"),
    OPEN_WAVE_DING_SOUND("openWaveDingSound", "0", TradeConfigEnum.CHAT_CONFIGS, "包装验货-叮"),
    OPEN_SHOP_NICK_NAME_VOICE("openShopNickNameVoice", "0", TradeConfigEnum.CHAT_CONFIGS, "包装验货-店铺简称"),
    OPEN_EXPRESS_COMPANY_VOICE_HINTS("openExpressCompanyVoiceHints", "0", TradeConfigEnum.TRADE_CONFIG, "包裹称重-快递公司"),
    OPEN_EXPRESS_WEIGHT_HINTS("openExpressWeightHints", "0", TradeConfigEnum.CHAT_CONFIGS, "包裹称重-重量(kg)"),
    OPEN_EXPRESS_COUNTRY_HINTS("openExpressCountryHints", "0", TradeConfigEnum.CHAT_CONFIGS, "包裹称重-收货国家"),
    OPEN_SEND_EXPRESS_COMPANY_VOICE_HINTS("openSendExpressCompanyVoiceHints", "0", TradeConfigEnum.CHAT_CONFIGS, "订单发货-快递公司"),
    OPEN_SYS_SET_TEMPLATE_NAME("openSysSetTemplateName", "0", TradeConfigEnum.CHAT_CONFIGS, "包裹称重-系统设置的模版名称"),

    /**
     * 强制验货功能配置
     * packForceType    0 直接验货整笔订单 | 1 直接拆分订单，将订单拆分为已验货商品和未验货商品
     * openPackUploadSellerMemo 0 不上传备注信息 | 1 上传备注信息，追加信息到原订单备注 | 2 上传备注信息，覆盖信息到原订单备注
     * packUploadInfo   skuSysId,sysSkuPropertiesName,forcePackNum,staffName,packDate,operType 规格商家编码，规格，数量，验货员，验货完成时间，操作类型
     * packSellerFlag   0 灰 | 1 红 | 2 橙 | 3 绿 | 4 蓝 | 5 紫 | -1 无旗帜
     */
    PACK_FORCE_TYPE("packForceType", "0", TradeConfigEnum.CHAT_CONFIGS, "订单处理"),
    OPEN_PACK_UPLOAD_SELLER_MEMO("openPackUploadSellerMemo", "0", TradeConfigEnum.CHAT_CONFIGS, "上传备注"),
    PACK_UPLOAD_INFO("packUploadInfo", "", TradeConfigEnum.CHAT_CONFIGS, "备注"),
    PACK_SELLER_FLAG("packSellerFlag", "-1", TradeConfigEnum.CHAT_CONFIGS, "旗帜"),
    PACK_FORCE_EXCLUDE_TAG("packForceExcludeTag", "", TradeConfigEnum.CHAT_CONFIGS, "标签"),

    /**
     * 开启后，会在选择的环节自动进行首公里预报，仅支持shopee平台
     * firstMilePushStep    0 包装称重后 | 1 系统发货后
     * shopeeGatherType 上门揽收 pickup
     */
    IS_FIRST_MILE_PUSH("isfirstMilePush", "0", TradeConfigEnum.CHAT_CONFIGS, "开启首公里自动预报功能"),
    FIRST_MILE_PUSH_STEP("firstMilePushStep", "0", TradeConfigEnum.CHAT_CONFIGS, "自动预报环节"),
    SHOPEE_SHOP_ID("shopeeShopId", "", TradeConfigEnum.CHAT_CONFIGS, "所属店铺"),
    SHOPEE_NAME("shopeeName", "", TradeConfigEnum.CHAT_CONFIGS, "所属店铺"),
    SHOPEE_GATHER_TYPE("shopeeGatherType", "pickup", TradeConfigEnum.CHAT_CONFIGS, "揽收方式"),

    OPEN_PACK_VIDEO_RECORD("openPackVideoRecord", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "开启包装验货视频监控"),

    OPEN_CHECK_REGISTER_VIDEO("openCheckRegisterVideo", "0", TradeConfigEnum.CHAT_CONFIGS, "开启验货登记视频监控"),

    OPEN_SUPPLEMENT_VIDEO("openSupplementVideo", "0", TradeConfigEnum.CHAT_CONFIGS, "开启工作量补入视频监控"),

    PACK_SUPPORT_SELLER_SEND("packSupportSellerSend", "0", TradeConfigEnum.CHAT_CONFIGS, "发货后验货"),

    /**
     * 0 优先系统图片
     * 1 优先平台图片
     */
    PACK_PIC_SHOW_TYPE("packPicShowType", "0", TradeConfigEnum.CHAT_CONFIGS, "包装验货商品图片展示"),

    SHORT_AGE_PICKED_ALLOW_PACK("shortagePickedAllowPack", "0", TradeConfigEnum.CHAT_CONFIGS, "缺拣订单允许包装验货"),

    //================================================================ TJ中 系统设置 交易设置 ======================================================================

    /**
     * 注：订单覆盖默认关闭，关闭后平台退款状态、交易关闭状态还是会继续同步到系统中
     */
    COVER_SYS_STATUS("coverSysStatus", "1", TradeConfigEnum.TRADE_CONFIG, "待发货订单发货状态是否同步"),

    /**
     * 注：默认关闭，不要频繁操作开启／关闭，10分钟后再操作
     */
    OPEN_LOGISTICS_TRACKING("openLogisticsTracking", "0", TradeConfigEnum.TRADE_CONFIG, "物流异常跟踪是否开启"),

    /**
     * 注：默认关闭，开启后同步订单自动分摊优惠金额
     */
    SHARE_DISCOUNT("shareDiscount", "0", TradeConfigEnum.TRADE_CONFIG, "交易金额自动分摊是否开启"),

    /**
     * 注：默认关闭，开启后创建手工单不分摊实付金额
     */
    CREATE_SYS_TRADE_NOT_SHARE_AC_PAYMENT("createSysTradeNotShareAcPayment", "0", TradeConfigEnum.CHAT_CONFIGS, "创建手工单不分摊订单实付金额"),

    /**
     * 注：默认关闭，关闭时去检查用户是否有未关闭的波次
     */
    OPEN_WAVE("openWave", "0", TradeConfigEnum.TRADE_CONFIG, "波次打印是否开启"),

    /**
     * 注：默认6小时，最大允许144小时
     */
    GIFT_DELAY_HOUR("giftDelayHour", "6", TradeConfigEnum.TRADE_CONFIG, "赠品活动有效期"),

    /**
     * 注：初始化默认12小时，最大允许144小时, 不填默认为0
     * 已经在新配置里面了
     */
    PRESALE_ACTIVITY_EXPIRY_DATE("presaleActivityExpiryDate", "12", "预售活动有效期设置"),

    /**
     * 注：默认关闭，开启后查所有的订单；关闭后查3天的订单
     */
    ORDER_QUERY_FULL_SWITCH("orderQueryFullSwitch", "0", TradeConfigEnum.CHAT_CONFIGS, "开启订单查询(三个月内所有订单)"),

    /**
     * 开启订单支付功能
     */
    OPEN_TRADE_PAY("openTradePay", "0", TradeConfigEnum.CHAT_CONFIGS, "开启订单支付功能"),

    /**
     * 订单是否包含ext信息
     */
    HAVE_ORDER_EXT_INFO("haveOrderExtInfo", "0", TradeConfigEnum.CHAT_CONFIGS, "订单是否包含ext信息"),

    /**
     * 开启抖店BIC质检订单流程
     * 如不录入，则默认没有店铺需要开启；录入店铺编号时，请以英文“，”隔开
     */
    OPEN_BIC_QUALITY_TESTING("openBicQualityTesting", "0", TradeConfigEnum.CHAT_CONFIGS, "开启抖店BIC质检订单流程"),
    OPEN_BIC_QUALITY_TESTING_SHOP("openBicQualityTestingShop", "", TradeConfigEnum.CHAT_CONFIGS, "录入需要开启抖店BIC质检订单流程的店铺编号"),

    /**
     * 如不录入，则默认没有店铺需要开启；录入多个店铺编号时，请以“，”隔开
     */
    OPEN_BTAS_QUALITY_TESTING("openBtasQualityTesting", "0", TradeConfigEnum.CHAT_CONFIGS, "开启抖店BTAS质检订单流程"),
    OPEN_BTAS_QUALITY_TESTING_SHOP("openBtasQualityTestingShop", "0", TradeConfigEnum.CHAT_CONFIGS, "录入需要开启的店铺编号"),

    /**
     * 京东直发订单进入系统锁定库存，订单平台状态为已发货时扣减库存
     */
    JD_WAREHOUSE_TRADE_SUB_STOCK("jdWarehouseTradeSubStock", "0", TradeConfigEnum.CHAT_CONFIGS, "京东直发订单扣减库存"),

    /**
     * 1688-AE直发订单进入系统锁定库存，订单平台状态为已发货时扣减库存
     */
    ALIBABA_AE_TRADE_SUB_STOCK("alibabaAETradeSubStock", "0", "1688-AE直发订单扣减库存"),

    O2O_TRADE_SUB_STOCK("o2oTradeSubStock", "0", "O2O订单扣减库存"),

    ONLINE_CONSIGN_NOT_SYNC_ERP("onlineConsignNotSyncErp", "0", "线上发货不同步ERP"),


    FXG_SUPPLIER_TRADE_SUB_STOCK("fxgSupplierTradeSubStock", "0", "抖店供应链订单扣减库存"),
    PREUPLOAD_NOT_ALLOW_SPLIT("preUploadNotAllowSplit", "0", "预发货订单不支持拆分"),

    /**
     * 开启放心购订单脱敏匹配标签
     */
    ALLOW_FXG_MASK_DATA_MATCH_TAG("allowFxgMaskDataMatchTag", "0", TradeConfigEnum.CHAT_CONFIGS, "开启放心购订单脱敏匹配标签"),

    /**
     * value值枚举 OpenLinkConfigEnum
     * 注意：如有变动请联系客服小伙伴进行操作
     */
    OPEN_LINK_PAYMENT("openLinkPayment", "4", TradeConfigEnum.TRADE_CONFIG, "订单商品金额是否设为联动"),

    /**
     * 交易平台金额映射
     * List<Map<String, String>>
     *     TRADE_PLATFORM_AMOUNT_MAPPING 老配置需一直维护
     *     TRADE_PLATFORM_AMOUNT_MAPPING_NEW 新配置需一直维护  区别（有些平台默认是取商家侧金额）
     */
    TRADE_PLATFORM_AMOUNT_MAPPING("tradePlatformAmountMapping", "[{\"yyjk\":\"1\",\"fxg\":\"1\",\"xhs\":\"1\",\"jd_vc\":\"1\",\"fx\":\"1\",\"elehealth\":\"1\",\"tb\":\"1\",\"tm\":\"1\",\"pdd\":\"1\",\"jd\":\"2\",\"kuaishou\":\"1\",\"poison\":\"1\",\"taocaicai\":\"1\",\"jingqizhimai\":\"2\"}]", TradeConfigEnum.CHAT_CONFIGS, "交易平台金额映射"),
    TRADE_PLATFORM_AMOUNT_MAPPING_NEW("tradePlatformAmountMappingNew", "[{\"yyjk\":\"2\",\"fxg\":\"2\",\"xhs\":\"2\",\"jd_vc\":\"2\",\"fx\":\"2\",\"elehealth\":\"2\",\"tb\":\"1\",\"tm\":\"1\",\"pdd\":\"1\",\"jd\":\"2\",\"kuaishou\":\"1\",\"poison\":\"1\",\"taocaicai\":\"1\",\"jingqizhimai\":\"2\"}]", TradeConfigEnum.CHAT_CONFIGS, "交易平台金额映射"),

    ITEM_NUM_EXCLUDE_AFTER_SEND_GOODS("itemNumExcludeAfterSendGoods", "0", "计算商品数量时排除已发货商品"),
    ITEM_NUM_EXCLUDE_VIRTUAL("itemNumExcludeVirtual", "0", "计算商品数量时排除虚拟商品"),
    /**
     * TJ配置:【无需发货商品不计入商品总数】
     * 0-关闭、1-开启(默认)
     */
    ITEM_NUM_EXCLUDE_NON_CONSIGN("itemNumExcludeNonConsign", "1", "计算商品数量时排除无需发货商品"),

    GET_WAYBILL_NOT_EX_UNATTAINABLE("getWaybillNotExUnattainable", "0", "获取运单号失败不新增快递异常"),

    /**
     * -1 未开启
     * 0 不解密模式
     * 1 解密模式
     */
    ORDER_CAIGOU_NEED_DECRYPT_INFO("orderCaigouNeedDecryptInfo", "-1", TradeConfigEnum.CHAT_CONFIGS, "开启按订单生成采购单"),
    //================================================================ 其他界面 比如自动化设置里 有操作更改配置 ======================================================================

    /**
     * 交易-自动化设置-分仓规则
     */
    RULE_AUTO_SEPARATE_WAREHOUSE("ruleAutoSeparateWarehouse", "0", TradeConfigEnum.CHAT_CONFIGS, "自动分仓"),

    warehouseAllocateAutoSplitByStock("warehouseAllocateAutoSplitByStock", "0", TradeConfigEnum.CHAT_CONFIGS, "按库存分仓"),

    /**
     * 交易-自动化设置-快递智能匹配
     * openExpressMatch 开关配置
     * openCloudSmartMatch  0 快递匹配规则 | 1 菜鸟智选物流规则 | 2 翱象匹配+快递匹配规则
     */
    OPEN_EXPRESS_MATCH("openExpressMatch", "0", TradeConfigEnum.TRADE_CONFIG, "订单自动匹配快递"),
    OPEN_CLOUD_SMART_MATCH("openCloudSmartMatch", "0", TradeConfigEnum.CHAT_CONFIGS, "订单自动匹配使用的规则"),
    OPEN_EXPRESS_FREIGHT("openExpressFreight", "0", TradeConfigEnum.CHAT_CONFIGS, "运费匹配规则"),
    OPEN_POINT_EXPRESS_FIRST("openPointExpressFirst", "0", TradeConfigEnum.CHAT_CONFIGS, "若存在低价运费优先规则拼多多买家指定物流订单优先匹配设置了买家指定物流的规则其次运费优先"),
    /**
     * 管理运费匹配规则
     */
    TB_FREIGHT_RULE("tbFreightRule", "", TradeConfigEnum.CHAT_CONFIGS, "淘系订单"),
    JD_FREIGHT_RULE("jdFreightRule", "", TradeConfigEnum.CHAT_CONFIGS, "京东订单"),
    PDD_FREIGHT_RULE("pddFreightRule", "", TradeConfigEnum.CHAT_CONFIGS, "拼多多订单"),
    FXG_FREIGHT_RULE("fxgFreightRule", "", TradeConfigEnum.CHAT_CONFIGS, "抖店订单"),
    KUAISHOU_FREIGHT_RULE("kuaishouFreightRule", "", TradeConfigEnum.CHAT_CONFIGS, "快手订单"),
    VIP_MP_FREIGHT_RULE("vipMpFreightRule", "", TradeConfigEnum.CHAT_CONFIGS, "唯品会MP订单"),
    OTHER_FREIGHT_RULE("otherFreightRule", "", TradeConfigEnum.CHAT_CONFIGS, "其他订单"),

    /**
     * 交易-自动化设置-赠品规则
     * 1、开启自动匹配后，订单会自动匹配赠品;
     * 2、开启自动匹配后，建议不要对平台订单执行手动匹配赠品，避免重复赠送
     */
    AUTO_GIFT_MATCH("autoGiftMatch", "1", TradeConfigEnum.TRADE_CONFIG, "自动匹配赠品"),

    MERGE_UNDO_AFTER_MATCH_TAG("mergeUndoAfterMatchTag", "0", "取消合单后重新匹配标签"),

    ADDRESS_CHANGE_AFTER_MATCH_WAREHOUSE("addressChangeAfterMatchWarehouse", "0", "修改地址后重新匹配仓库"),

    ADDRESS_CHANGE_AFTER_MATCH_EXPRESS("addressChangeAfterMatchExpress", "0", "修改地址后重新匹配快递"),
    SUPPORT_GIFTNOT_ALONE("supportGiftNotAlone", "0", "自动拆单/重量拆分/退款拆分/默认箱规拆分/批量拆分/分仓按供应商和指定商品拆单时，赠品不允许单独成单"),
    POST_PRINT_OPEN_ENTITY_CODE("postPrintOpenEntityCode", "0", "盲扫实体编码匹配套件订单"),

    POST_PRINT_BATCH_LIMIT_MAX("postPrintBatchLimitMax", "", "盲扫连打上限数"),

    PDD_EXPRESS_ONSITE_SERVICES("pddExpressOnsiteServices", "0", "拼多多跨境全托管快递上门揽件订单允许取号上传发货"),

    ALLOW_UNPRINT_PACK("allowUnprintPack", "0", "未打印进行包装验货"),

    AOXIANG_TEMPLATE_MATCH("aoxiangTemplateMatch", "0", "设置快递/按比例分配快递时，仍使用平台建议白名单快递"),
    SHOW_ORDER_PROD_BATCH("showOrderProdBatch", "0", "订单列表中线上商品批次、有效期数据"),

    /**
     * 交易-自动化设置-审单规则-自动审核
     */
    OPEN_AUTO_AUDIT("openAutoAudit", "0", TradeConfigEnum.TRADE_CONFIG, "自动审核"),

    /**
     * 交易-自动化设置-审单规则-设置订单自动/智能审核顺序
     * 订单排序
     * payTime 订单付款时间升序（默认配置）
     * beforePayTimeAndAfterTimeoutActionTime 现货订单按付款时间先排序，预售订单再按计划发货时间排序
     * payTimeAndTimeoutActionTime 现货订单按付款时间、预售订单按计划发货时间混合排序
     * incluedTagIds 包含某些标签订单优先
     * mutilItemNum 多件订单优先
     * urgent 加急订单优先
     * timeOutActionTime 订单承诺时间升序优先
     */
    AUDIT_ORDER("aiAuditOrder", "payTime", TradeConfigEnum.CHAT_CONFIGS, "订单排序"),
    INCLUDE_TAG_IDS("incluedTagIds", "", TradeConfigEnum.CHAT_CONFIGS, "订单排序-包含某些标签订单优先-标签id"),
    /**
     * 0 预留库存
     * 1 不预留库存
     */
    PRE_LOCK("preLock", "1", TradeConfigEnum.CHAT_CONFIGS, "库存预留方式"),
    AI_AUDIT_EXCEPTION_OPEN("aiAuditExceptionOpen", "0", TradeConfigEnum.CHAT_CONFIGS, "库存预留方式-预留库存-包含指定异常时，订单也参与预留库存"),
    /**
     * {"selfExceps": "2000554185495040,2000554185495041", "sysExceps": "2323232323232,232323"}
     */
    AI_AUDIT_EXCEPTION_MSG("aiAuditExceptionMsg", "", TradeConfigEnum.CHAT_CONFIGS, "库存预留方式-预留库存-包含指定异常时，订单也参与预留库存-自定义异常"),
    /**
     * [{"index":1,"userId":4364}]
     */
    AI_AUDIT_ORDER_SHOPS("aiAuditOrderShops", "[]", TradeConfigEnum.CHAT_CONFIGS, "店铺优先级"),


    /**
     * 自动化设置-财审规则
     */
    OPEN_FINANCE_AUDIT("openFinanceAudit", "0", TradeConfigEnum.TRADE_CONFIG, "开启财务审核流程"),

    /**
     * 自动化设置-承诺时间设置
     */
    OPEN_EST_CONSIGN_TIME("openEstConsignTime", "0", TradeConfigEnum.CHAT_CONFIGS, "自定义订单发货承诺时间"),

    /**
     * 自动化设置-超时预警设置
     */
    OPEN_OVERTIME_WARNING("openOvertimeWarning", "0", TradeConfigEnum.CHAT_CONFIGS, "订单即将发货超时预警开关"),

    /**
     * 自动化设置-自动标记规则
     * 修改模板自动标记
     * tagIds 逗号隔开
     */
    AUTO_UPDATE_TEMPLATE("autoUpdateTemplate", "", TradeConfigEnum.TRADE_CONFIG, "修改模板自动标记"),
    /**
     * 换货补发自动标记原单
     * tagIds 逗号隔开
     */
    AUTO_TAGS_AS_ORIGIN_TRADE("autoTagsAsOriginTrade", "", TradeConfigEnum.CHAT_CONFIGS, "换货补发自动标记原单"),

    /**
     * 换货订单自动标记标签
     * tagIds 逗号隔开
     */
    AUTO_TAGS_EXCHANGE("autoTagsExchange", "", TradeConfigEnum.CHAT_CONFIGS, ""),
    /**
     * 换货退款自动标记换货单
     * tagIds 逗号隔开
     */
    AUTO_TAGS_AS_EXCHANGE_TRADE("autoTagsAsExchangeTrade", "", TradeConfigEnum.CHAT_CONFIGS, "换货退款自动标记换货单"),

    /**
     * 自动化设置-商品搭配规则
     * 更换商品缺货匹配商品搭配规则
     */
    ITEM_REPLACE_INSUFFICIENT("itemReplaceInsufficient", "0", TradeConfigEnum.CHAT_CONFIGS, "更换商品缺货匹配商品搭配规则"),
    /**
     * 订单自动执行商品搭配规则
     */
    ITEM_REPLACE_MATCH_ALONE("itemReplaceMatchAlone", "0", TradeConfigEnum.CHAT_CONFIGS, "订单自动执行独立商品匹配规则"),

    /**
     * 交易-自动化设置-黑名单设置
     */
    OPEN_BLACK("openBlack", "0", TradeConfigEnum.TRADE_CONFIG, "黑名单设置"),

    /**
     * 交易-唯品会JIT-拣货单-剩余发货时间
     * 设置预警时间
     */
    VIP_JIT_LAST_TIME_HOUR_NUM("vipJitLastTimeHourNum", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "剩余发货时间"),
    /**
     * 请选择剩余发货时间显示格式
     * 1 按照小时显示
     * 2 按照天/小时/分显示
     */
    VIP_JIT_LAST_TIME_TYPE("vipJitLastTimeType", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "剩余发货时间格式"),

    /**
     * 订单处理-异常订单-订单异常状态
     * 数量显示气泡小红点
     */
    ITEM_EXCEP_OPEN("itemExcepOpen", "0", TradeConfigEnum.TRADE_CONFIG, "异常右上角数量显示气泡小红点"),

    //================================================================ 交易->快进快出配置 ======================================================================
    /**
     * "changeItemException": "EX_INSUFFICIENT,EX_ITEM_HALT,EX_BLACK"
     */
    CHANGE_ITEM_EXCEPTION("changeItemException", "", TradeConfigEnum.TRADE_EXTEND_CONFIG, "设置异常订单允许智能换商品的类型"),

    /**
     * 0： 默认不过滤 进行弹窗  1：过滤不弹窗
     */
    EXPRESS_WHETHER_FILTER_TRADE("expressWhetherFilterTrade", "0", "批量修改快递时直接过滤订单系统状态不为待审核，待财审，待打印的订单"),
    MERGE_TRADE_CLEAR_TEMPLATE("mergeTradeClearTemplate", "0", "合单匹配不到清空快递，默认0不开启，匹配不到时还是保留主单的快递模版,1 开启"),


    POISON_PRINT_CONTROL_NEW_STYLE("poisonPrintControlNewStyle", "", "得物品牌直发订单走得物打印控件打印支持新面单样式"),

    REISSUE_OR_CHANGE_TRADE_GIFT_MATCH("reissueOrChangeTradeGiftMatch", "0", "换货补发订单创建时匹配赠品规则"),

    /**
     * 换货补发订单创建时同步执行商品搭配规则
     */
    REISSUE_OR_CHANGE_TRADE_ITEM_REPLACE_MATCH("reissueOrChangeTradeItemReplaceMatch", "0", "换货补发订单创建时同步执行商品搭配规则 0-关闭，1-开启"),


    FXG_LOGISTICS_EXPLORATION("fxgLogisticsExploration", "0", "抖店物流探查功能"),
    VIPJIT_CREATE_DELIVERY_BY_SYS_WAREHOUSE("vipjitCreateDeliveryBySysWarehouse", "0", "唯品会JIT拣货单支持按照实体仓一键创建出仓单"),

    /**
     * 异常订单在第三方仓库出库后，ere也会自动出库
     * 1为开启，0为不开启，默认开启
     */
    EXCEP_TRADE_PARTY3_SEND_GOODS("excepTradeParty3SendGoods", "1", TradeConfigEnum.TRADE_CONFIG, "异常订单在第三方仓库出库后，ere也会自动出库"),

    /**
     * 三方仓订单 已发货订单交易关闭后拦截方式
     * 0为不处理 1为取消发货单 2为 拦截发货单
     */
    PARTY3_FINISH_TRADE_CALLBACK("party3FinishTradeCallBack", "1", "已发货订单交易关闭后是否发起方式"),

    /**
     * 交易关闭不取消三方仓取消发货单异常 默认 0 不开
     */
    PARTY3_TRADE_CLOSED_ADD_CANCEL_EXCEPTION("party3TradeClosedAddCancelException", "0", "交易关闭不取消三方仓取消发货单异常"),


    MERGE_MULTIL_PDD("mergeMultilPdd", "0", "允许拼多多订单跨店合单（注意：受平台返回数据限制跨店合单可能会有极端情况不准确，非必要请勿开启）"),

    MERGE_DIFF_MD5_PDD("mergeDiffMd5Pdd", "0", "允许拼多多同店铺不同买家相同收件地址订单合单"),

    /**
     * 撤销发货库存返还位置，0-拣选暂存区 1-出库货位
     */
    CANCEL_CONSIGN_ADD_STOCK_POSITION("cancelConsignAddStockPosition", "0", "撤销发货库存返还位置"),


    QIMEN_FX_TRADE_MATCH_GIFT("qimenFxTradeMatchGift", "0", "奇门分销版订单支持匹配赠品规则"),

    ADD_GIFT_RULE_NAME_TO_ITEM_REMARK("addGiftRuleNameToItemRemark", "0", "订单商品备注支持显示赠品策略名称"),

    UPLOAD_UNIQUECODE_OR_IDENTCODE("uploadUniqueCodeOrIdentCode", "0", "发货回传上传"),

    SALES_MAN_PRIVILEGE("salesManPrivilege", "0", "开启业务员订单控制权限"),

    /**
     * 发货装箱开启强制装箱
     */
    FORCE_BOX("forceBox", "0", "允许强制装箱"),


    OPEN_VIDEO_SHIP_BOX("openVideoShipBox","0","开启发货装箱视频监控"),

    /**
     * 分销订单匹配供销商的方式：0:不勾选-走老的模式-先走快麦通商品匹配在走分销策略匹配,1:通过快麦通的商品匹配供销商，2:通过ERP的分销策略规则匹配供销商
     */
    DMS_TRADE_MATCH_FX_ATTR_TYPE("dmsTradeMatchFxAttrType", "0", "分销订单匹配供销商的方式：0:不勾选,1:通过快麦通的商品匹配供销商，2:通过ERP的分销策略规则匹配供销商"),

    DMS_FX_TRADE_AS_NOT_DEDUCTION_CASH_FLOW("dmsFxTradeAsNotDeductionCashFlow", "0", "分销商推送的售后工单不退回流水"),

    DMS_GX_TRADE_FILL_SALESMAN("dmsGxTradeFillSalesman", "0", "供销订单的「业务员」字段，自动回填分销商管理中设置好的业务员 0:关闭 1:开启"),

    DMS_TRADE_MATCH_DO_ITEM_REPLACE("dmsTradeMatchDoItemReplace", "0", "供销订单开启执行商品搭配规则：0:不开启,1:开启  说明文案：1.执行商品搭配规则后，分销和供销的商品信息不会保持一致 2.资金流水会以搭配之前的商品来计算，且暂不支持重算"),

    DMS_TAG_POSSIBLE_LOSS("dmsTagPossibleLoss", "0", "设置分销订单的客户下单价和分销成本金额的差值小于一定值时，自动打上“可能亏损”的标签  0:关闭 1:开启"),

    DMS_TAG_POSSIBLE_LOSS_WAY("dmsTagPossibleLossWay", "0", "标记方式：0-差值,1-百分比"),

    DMS_LOSS_VALUE("dmsLossValue", "0", "具体的差值"),

    DMS_LOSS_PERCENT("dmsLossPercent", "0", "分销成本金额的百分比"),

    DMS_TRADE_GX_EDIT_NOT_REL_FX("dmsTradeGxEditNotRelFx", "0", "允许供销订单与分销订单的商品信息和订单信息不一致"),

    DMS_MATCH_LOW_PRICE_FREIGHT("dmsMatchLowPriceFreight", "0", "分销订单自动匹配供销商低价快递"),

    DMS_ALLOW_FX_APPOINT_TEMPLATE_ID("dmsAllowFxAppointTemplateId", "0", "允许分销商指定快递模板"),

    DMS_INTERCEPT_TAG_POSSIBLE_LOSS("dmsInterceptTagPossibleLoss", "0", "分销侧订单亏损拦截，不自动推单"),

    DMS_FX_APPOINT_TEMPLATE_CALC_POST_FEE("dmsFxAppointTemplateCalcPostFee", "0", "计算快递运费时，用分销商指定的快递公司计算的运费进行结算"),


    /**
     * 0： 后置智能审核顺序多条件组合排序
     */
    AUDIT_MULTIPLE_CONDITION("auditMultipleCondition", "0", "后置智能审核顺序多条件组合排序"),

    /**
     * 0： 后置智能审核顺序多条件组合排序(最优先排序条件)
     */
    AUDIT_MULTIPLE_CONDITION_FIRST("firstCondition", "0", "后置智能审核顺序多条件组合排序(最优先排序条件)"),

    /**
     * 0： 后置智能审核顺序多条件组合排序(次优先排序条件)
     */
    AUDIT_MULTIPLE_CONDITION_SECOND("secondCondition", "0", "后置智能审核顺序多条件组合排序(次优先排序条件)"),

    /**
     * 0： 后置智能审核顺序多条件组合排序(时间纬度排序条件)
     */
    AUDIT_MULTIPLE_CONDITION_TIME("timeCondition", "0", "后置智能审核顺序多条件组合排序(时间纬度排序条件)"),


    DMS_ALLOW_GX_EDIT_TRADE_ITEM("dmsAllowGxEditTradeItem", "0", "允许供销商编辑订单中的商品"),

    FX_AUTO_SELLER_MEMO_OPEN("fxAutoSellerMemoOpen", "0", "分销审核自动追加平台备注开关"),

    FX_SELLER_MEMO("fxSellerMemo", "", "分销追加的卖家备注内容"),

    FX_SELLER_MEMO_APPEND_SID("fxSellerMemoAppendSid", "0", "分销平台备注是否追加系统单号"),

    FX_AUTO_FLAG("fxAutoFlag", "", "分销审核自动追加平台备注开关"),

    DMS_SYNC_SUPPLIER_SELLER_MEMO("syncSupplierSellerMemo", "0", "允许供销商同步卖家备注到分销"),

    DMS_SYNC_SUPPLIER_SYS_MEMO("syncSupplierSysMemo", "0", "允许供销商同步系统备注到分销"),

    /**
     * 开启以销定采 默认0 0不开启 1开启
     */
    OPEN_PURCHASE_BASED_ON_SALES("openPurchaseBasedOnSales", "0", "开启以销定采"),

    SYNC_ONLINE_LOCK_CONFIG("syncOnlineLockConfig", "{\"enable\": 0, \"value\": \"\"}", "同步线上锁定订单"),

    NOT_WAYBILL_UNAUDIT("notWaybillUnaudit", "0", "审核自动获取快递单号失败则自动反审核订单"),

    OPEN_WAYBILL_USING_RULE("openWaybillUsingRule", "0", "开启货主单量管理"),

    SPLIT_PLAT_GIFT_FOLLOW_MAIN_ORDER("splitPlatGiftFollowMainOrder", "0", "拆分时，平台赠品默认跟随主品"),

    OPEN_HOT_ITEM_PRINT_V2("openHotItemPrintV2", "0", "开启升级版爆款打印"),

    SPLIT_THE_ORDER_SKIP_CHECK("splitTheOrderSkipCheck", "0", "拆分订单子母单取号的订单放开打印限制"),

    QIMEN_ORDER_FORBID_SPLIT("qimenForbidSplite", "0", "奇门订单不支持拆单"),

    TRADE_SHOW_MULTI_COUNT("tradeShowMultiCount", "0", "订单列表一单多包订单显示快递单号总数"),

    UPLOAD_SELLER_MEMO_AFTER_PRINT("uploadSellerMemoAfterPrint","0","订单打印上传平台备注"),


    DELIVER_NOT_REPEAT_PRINT("deliverNotRepeatPrint", "0", "发货单不允许重打"),

    /**
     * 拆单规则 自动拆单
     */
    TRADE_AUTO_SPLIT("tradeAutoSplit", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "自动拆单"),

    //================================================================ BTAS配置 ======================================================================
    /**
     * 数字
     * 0:顺丰标快
     * 1:顺丰特惠
     * ... 19
     */
    BTAS_EXPRESS_PRODUCT("btasExpressProduct", "0", TradeConfigEnum.CHAT_CONFIGS, "快递产品"),

    BTAS_INSURED_PRICE("btasInsuredPrice", "0", TradeConfigEnum.CHAT_CONFIGS, "订单保价"),

    BTAS_INSURED_SHOPIDS("btasInsuredShopIds", "[]", TradeConfigEnum.CHAT_CONFIGS, "保价店铺"),

    /**
     * 0:发货优先，1:售后优先
     */
    BTAS_GOODS_REJECTED_INTERCEPT("btasGoodsRejectedIntercept", "0", TradeConfigEnum.CHAT_CONFIGS, "退货发货拦截"),

    /**
     * 0:邮寄，1:线下自提
     */
    BTAS_QUALITY_CHECK_RETURN_TYPE("btasQualityCheckReturnType", "0", TradeConfigEnum.CHAT_CONFIGS, "取回方式"),

    //================================================================ 客服配置 ======================================================================
    OPEN_CHAT_CONFIG("openChatConfig", "0", TradeConfigEnum.CHAT_CONFIGS, "客服配置 Tab 权限"),

    OPEN_CHANGE_ITEM_CHAT("openChangeitemChat", "0", TradeConfigEnum.CHAT_CONFIGS, "客服推送配置 换货订单，系统发货后自动推送旺旺消息给买家"),

    OPEN_REISSUE_CHAT("openReissueChat", "0", TradeConfigEnum.CHAT_CONFIGS, "客服推送配置 补发订单，系统发货后自动推送旺旺消息给买家"),

    OPEN_DELAY_CHAT("openDelayChat", "0", TradeConfigEnum.CHAT_CONFIGS, "客服推送配置 订单付款时间超过一定时间仍未发货，自动推送旺旺消息给买家"),

    DAYS("days", "", TradeConfigEnum.CHAT_CONFIGS, "超过多少天未发货自动推送"),

    //================================================================ 交易导航 ======================================================================

    OPEN_PACKAGE_WEIGH("openPackageWeigh", "0", TradeConfigEnum.TRADE_CONFIG, "包裹称重"),

    OPEN_PACKAGE_EXAMINE("openPackageExamine", "0", TradeConfigEnum.TRADE_CONFIG, "包裹验货"),

    OPEN_CONSIGN("openConsign", "0", TradeConfigEnum.TRADE_CONFIG, "订单发货"),

    //================================================================ 老枚举中一些没有确定位置的配置 ======================================================================
    KY_WAVE_AUTO_GET_WAYBILL_CODE("kyWaveAutoGetWaybillCode", "0", TradeConfigEnum.CHAT_CONFIGS, "生成波次时自动获取快运单号"),

    /**
     * 逗号隔开的标签id
     * 12345,345556
     */
    TRADE_CHANGE_SIGN_TAGS("tradeChangeSignTags", "", TradeConfigEnum.CHAT_CONFIGS, "商品修改自动标签"),

    /**
     * 存的时间 2019-08-22 17:03:29
     */
    TRADE_DISCLAIMER_START("tradeDisclaimerStart", "", TradeConfigEnum.CHAT_CONFIGS, "免责申明有效期开始时间"),

    /**
     * 存的时间 2020-12-20 00:00:00
     */
    TRADE_DISCLAIMER_END("tradeDisclaimerEnd", "", TradeConfigEnum.CHAT_CONFIGS, "免责申明有效期结束时间"),

    /**
     * 存的ip ***************
     */
    TRADE_DISCLAIMER_IP("tradeDisclaimerIp", "", TradeConfigEnum.CHAT_CONFIGS, "同意免责申明的ip"),

    PT_LOCK_CLOSE("ptLockClose", "0", TradeConfigEnum.CHAT_CONFIGS, "关闭打印锁"),

    PDD_USE_CAI_NIAO("pddUseCaiNiao", "0", TradeConfigEnum.CHAT_CONFIGS, "拼多多订单是否允许菜鸟"),

    SPLIT_GET_NEW_WAYBILL_CODE("splitGetNewWaybillCode", "0", TradeConfigEnum.CHAT_CONFIGS, "拆单获取新单号"),

    /**
     * 逗号隔开的数字
     * 0,1,6,3,5,7,100
     */
    BATCH_WAVE_STATUS_CONFIG("batchWaveStatusConfig", "", TradeConfigEnum.CHAT_CONFIGS, "允许波次批打的波次状态 0/1/6/3/5/7/100 未拣选/拣选中/等待播种/播种中/等待验货/验货中/已完成"),

    MEMO_SORT_OPEN("memoSortOpen", "0", TradeConfigEnum.CHAT_CONFIGS, "按照备注做排序"),

    WAVE_SEED_FAST_FAIL("waveSeedFastFail", "0", TradeConfigEnum.CHAT_CONFIGS, " 播种打印时，扫描匹配到异常订单时，直接报错拦截"),

    QUERY_ORDER_EXT_INFO("queryOrderExtInfo", "0", TradeConfigEnum.CHAT_CONFIGS, "查询订单时，是否查询order_ext信息 0-不查询 1-查询 默认不查询"),

    SYNC3_MONTHS("sync3Months", "0", TradeConfigEnum.CHAT_CONFIGS, "是否同步三个月前的订单"),

    OUT_ID_LIKE("outIdLike", "0", TradeConfigEnum.CHAT_CONFIGS, "商家编码模糊查询"),

    OPEN_STALL_WAVE_RULE("openStallWaveRule", "0", TradeConfigEnum.CHAT_CONFIGS, "开启档口波次规则"),

    BILLING_INTO_WAVE_V2("billingIntoWaveV2", "0", TradeConfigEnum.CHAT_CONFIGS, "批发收银V2，开单后订单审核通过并自动生成波次"),

    WHOLE_SALE_CASHIER_OPEN_SYNC_CONTENT_TO_WAVE("wholeSaleCashierOpenSyncContentToWave", "0", TradeConfigEnum.CHAT_CONFIGS, "批发收银V2，同步以下内容到波次备注字段显示"),

    WHOLE_SALE_CASHIER_SYNC_DISTRIBUTOR_NAME_TO_WAVE("wholeSaleCashierSyncDistributorNameToWave", "0", TradeConfigEnum.CHAT_CONFIGS, "分销商名称"),

    WHOLE_SALE_CASHIER_SYNC_TRADE_REMARK_TO_WAVE("wholeSaleCashierSyncTradeRemarkToWave", "0", TradeConfigEnum.CHAT_CONFIGS, "备注（开单填的备注）"),

    WHOLE_SALE_CASHIER_SYNC_DISTRIBUTOR_REMARK_TO_WAVE("wholeSaleCashierSyncDistributorRemarkToWave", "0", TradeConfigEnum.CHAT_CONFIGS, "备注（分销商管理列表备注字段）"),

    DANGKOU_TRADE_CAN_MERGE_WITH_DANGKOU_TRADE("dangkouTradeCanMergeWithDangkouTrade", "0", TradeConfigEnum.CHAT_CONFIGS, "档口订单允许与档口订单合并"),

    MANUAL_CREATE_STALL_WAVE_SYNC_TRADE_SYS_MEMO("manualCreateStallWaveSyncTradeSysMemo", "0", TradeConfigEnum.CHAT_CONFIGS, "手动生成档口波次时同步对应订单备注"),

    OPEN_EXCEP_TRADE_GENERATE_WAVE("openExcepTradeGenerateWave", "0", TradeConfigEnum.CHAT_CONFIGS, "仅缺货异常订单，允许生成波次"),

    /**
     * 存的标签id 逗号隔开
     */
    SUIT_TO_SINGLE_MARK_TAG("suitToSingleMarkTag", "", TradeConfigEnum.CHAT_CONFIGS, "套件转单品自动标记"),

    OPEN_POST_PRINT_MATCH_SHOP_PRIORITY("openPostPrintMatchShopPriority", "0", TradeConfigEnum.CHAT_CONFIGS, "后置打印盲扫开启店铺优先发货 0-关闭 1-开启"),

    POST_PRINT_MATCH_PRIORITY_SHOPS("postPrintMatchPriorityShops", "", TradeConfigEnum.CHAT_CONFIGS, "后置打印盲扫优先发货指定店铺编号"),

    NEW_EXPORT("newExport", "0", TradeConfigEnum.CHAT_CONFIGS, "新版订单导出"),

    MOVE_STOCK("moveStock", "0", TradeConfigEnum.CHAT_CONFIGS, "暂存区平移"),

    WAVE_PICK_IGNORE_PRINTED_ERROR_TRADE("wavePickIgnorePrintedErrorTrade", "0", TradeConfigEnum.CHAT_CONFIGS, "波次拣选不过滤打印完成后出现异常的订单"),

    OPEN_POST_PRINT_VIDEO("openPostPrintVideo", "0", TradeConfigEnum.CHAT_CONFIGS, "开启后置打印视频监控"),

    PICKING_PROP_ONE_RULE_SORT("pickingPropOneRuleSort", "0", TradeConfigEnum.CHAT_CONFIGS, "波次拣选时相同货位的不同商品按照规格属性第一排序规则"),

    PICKING_PROP_TWO_RULE_SORT("pickingPropTwoRuleSort", "0", TradeConfigEnum.CHAT_CONFIGS, "波次拣选时相同货位的不同商品按照规格属性第二排序规则"),

    AUTO_SEND_FINANCE("autoSendFinance", "0", TradeConfigEnum.CHAT_CONFIGS, "批发收银结账后，自动生成财务应收单"),

    DISTRIBUTION_FLOW_CONTROL("distributionFlowControl", "0", TradeConfigEnum.CHAT_CONFIGS, "批发收银V2使用微信，支付宝现金不生成分销流水"),

    WAVE_AUTO_CREATE_TIME("waveAutoCreateTime", "00:00", TradeConfigEnum.TRADE_EXTEND_CONFIG, "固定时间点生成波次"),

    TRADE_ITEM_COST_CONFIG("tradeItemCostConfig", "0", TradeConfigEnum.CHAT_CONFIGS, "交易订单商品成本价取值 0.商品档案成本价 1.历史成本价"),

    OPEN_UPDATE_TRADE_COST("openUpdateTradeCost", "0", TradeConfigEnum.CHAT_CONFIGS, "交易订单商品成本价更新 默认开启 1，0.关闭 1.开启"),

    OPEN_PLATFORM_DIVIDE_ORDER_FEE("openPlatformDivideOrderFee", "0", TradeConfigEnum.CHAT_CONFIGS, "【天猫淘宝】平台订单商品金额分摊是否开启"),

    WAVE_TRADE_SORT("waveTradeSort", "0", TradeConfigEnum.CHAT_CONFIGS, "进入波次订单顺序"),

    CHANGE_ITEM_SUBS_STAGING_STOCK("changeItemSubsStagingStock", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "智能换商品时,扣除暂存区库存"),

    ALLOW_CHANGE_ITEM_WHEN_INSUFFICIENT("allowChangeItemWhenInsufficient", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "智能换商品时，工厂商品库存不足的时候也换成工厂商品"),

    SUPPORT_INSUFFICIENT_TRADE_WAVE_PRINT("supportInsufficientTradeWavePrint", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "支持仅缺货订单打印快递面单"),

    OPEN_WAVE_UNIQUE_CODE("openWaveUniqueCode", "0", TradeConfigEnum.TRADE_CONFIG, "开启波次唯一码"),

    /**
     * 消息设置 - 齿轮设置 - 发货提醒旁小问号
     */
    TIMEOUT_LIMIT("timeoutLimit", "", TradeConfigEnum.TRADE_CONFIG, "发货提醒时间"),

    /**
     * 系统设置-波次配置-生成波次-允许填写波次备注
     */
    OPEN_WAVE_REMARK("openWaveRemark", "0", TradeConfigEnum.TRADE_CONFIG, "允许填写波次备注"),

    /**
     * 波次打印-生成波次-是否自动生成波次
     */
    AUTO_CREATE_WAVE("autoCreateWave", "0", TradeConfigEnum.TRADE_CONFIG, "自动生成波次"),

    PDDTEMU_MERGE_CONSIGN("pddtemuMergeConsign", "{\"enableStatus\":\"0\",\"groups\":[]}", "开启Temu多店铺合并发货"),

    FX_GIFT_RECALCULATE_CASH_FLOW("fxGiftReCalculateCashFlow", "0", TradeConfigEnum.CHAT_CONFIGS, "赠品计算资金流水(包含重算资金流水)"),
    OPEN_WEIGHT_CLOSE_ORDER_VOICE("openWeightCloseOrderVoice", "0", TradeConfigEnum.CHAT_CONFIGS, "开启包裹称重退款订单语音播报 0-否 1-是"),
    /**
     * 速卖通同步半托管订单
     */
    SMT_SYNC_BTG_ORDER("smtSyncBtgOrder", "0", "速卖通同步半托管订单"),

    NEW_PAYMENT_SHARE("newPaymentShare", "0", TradeConfigEnum.CHAT_CONFIGS, "实付金额分摊"),

    ORDER_PRICE_USE_ITEM_PRICE("orderPriceUseItemPrice", "0", "平台商品单价取商品档案设置的销售价"),


    /**
     * 订单管理打印快递单后不自动刷新订单列表同时当前页面自动剔除已发货订单
     */
    IS_REMOVE_ORDER_BUT_NO_REFRESH("isRemoveOrderButNoRefresh", "0", "订单管理打印快递单后不自动刷新订单列表同时当前页面自动剔除已发货订单"),

    ALLOW_BATCH_CANCEL_UPLOADED("allowBatchCancelUploaded", "0", TradeConfigEnum.TRADE_EXTEND_CONFIG, "允许批量作废预发货订单"),

    OPEN_FORCE_TRADE_PACK("openForceTradePack", "0", TradeConfigEnum.TRADE_CONFIG, "强制包装验货"),

    //TODO
    USE_NEW_QUERY("useNewQuery", "0", ""),
    AUTO_CREATE_WAVE_TIME_INTERVAL("autoCreateWaveTimeInterval", "10", ""),
    RESERVE_EXCEPT_AFTER_CANCEL_AUDIT("reserveExceptAfterCancelAudit", "0", ""),
    SPEED_PRINT_SEND_OF_MULTI_COMPANY("speedPrintSendOfMultiCompany", "0", TradeConfigEnum.TRADE_CONFIG, "订单打印V2/波次打印/波次管理---极速打单发货，支持多快递同时打印"),
    //================================================================ 结尾分号 ======================================================================
    ;
    /**
     * 保存到数据库的key值，尽量简单
     * 数据库字段 varchar(64)
     */
    private final String configKey;

    /**
     * 默认值
     */
    private final String defaultValue;

    /**
     * 老配置迁移使用
     * 定位存在于老配置TradeConfig的位置
     */
    private String oldParentPath;

    public static final String TRADE_CONFIG = "tradeConfig";
    public static final String CHAT_CONFIGS = "tradeConfig.chatConfigs";
    public static final String TRADE_EXTEND_CONFIG = "tradeConfig.tradeExtendConfig";

    /**
     * 中文说明(这个值最好跟页面展示一致，这样前端就可以直接哪来使用)
     */
    private final String chinese;

    TradeConfigEnum(String configKey, String defaultValue, String chinese) {
        this.configKey = configKey;
        this.defaultValue = defaultValue;
        this.chinese = chinese;
    }

    TradeConfigEnum(String configKey, String defaultValue, String parentPath, String chinese) {
        this.configKey = configKey;
        this.defaultValue = defaultValue;
        this.oldParentPath = parentPath;
        this.chinese = chinese;
    }

    /**
     * 判断是否为老配置
     */
    public boolean isOld() {
        return this.oldParentPath != null && !this.oldParentPath.isEmpty();
    }

    public static final Map<String, TradeConfigEnum> KEY_TO_ENUM;

    static {
        TradeConfigEnum[] values = TradeConfigEnum.values();
        KEY_TO_ENUM = new TreeMap<>();
        for (TradeConfigEnum value : values) {
            KEY_TO_ENUM.put(value.getConfigKey(), value);
        }
    }
}

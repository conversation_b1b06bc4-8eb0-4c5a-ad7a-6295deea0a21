package com.raycloud.dmj.domain.trade.warehouse;

import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.pt.WarehouseAllocate;
import com.raycloud.dmj.domain.trade.config.TradeConfigContext;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trade.utils.TradeUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.diamond.TradeWarehouseConfigUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.ptException.PddCheckPtException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName TradeWarehouseUtils
 * @Description 订单仓库工具类
 * <AUTHOR>
 * @Date 2024/8/22
 * @Version 1.0
 */
public abstract class TradeWarehouseUtils {

    public static boolean needQueryRule(TradeWarehouseMatchTypeEnum matchTypeEnum) {
        return TradeWarehouseMatchTypeEnum.RULE == matchTypeEnum
                || TradeWarehouseMatchTypeEnum.RULE_STOCK_SPLIT == matchTypeEnum
                || TradeWarehouseMatchTypeEnum.RULE_STOCK_SPLIT_NOT == matchTypeEnum
                || TradeWarehouseMatchTypeEnum.STOCK_RULE_SPLIT_NOT == matchTypeEnum;
    }

    public static WarehouseAllocate fillQueryItemParams(Staff staff, TradeWarehouseContext context, List<Trade> trades) {
        WarehouseAllocate query = new WarehouseAllocate();
        query.setIsOpen(1);
        if (CollectionUtils.isNotEmpty(trades)) {
            Set<Long> pureItemIds = new HashSet<>(), sysItemIds = new HashSet<>(), sysSkuIds = new HashSet<>();
            for (Trade trade : trades) {
                List<Order> orders = TradeUtils.getOrders(staff, trade, context != null ? context.getMergeSidTradesMap() : null);
                if (CollectionUtils.isNotEmpty(orders)) {
                    for (Order order : orders) {
                        fillQueryItem(order, pureItemIds, sysItemIds, sysSkuIds);
                        if (order.isSuit(false) && CollectionUtils.isNotEmpty(order.getSuits())) {
                            order.getSuits().forEach(suit -> fillQueryItem(suit, pureItemIds, sysItemIds, sysSkuIds));
                        }
                    }
                }
            }

            query.setPureSysItemIds(pureItemIds);
            query.setSysItemIds(sysItemIds);
            query.setSysSkuIds(sysSkuIds);
        }
        return query;
    }

    private static void fillQueryItem(Order order, Set<Long> pureItemIds, Set<Long> sysItemIds, Set<Long> sysSkuIds) {
        long sysItemId = NumberUtils.nvlLong(order.getItemSysId(), 0L);
        if (sysItemId > 0) {
            long sysSkuId = NumberUtils.nvlLong(order.getSkuSysId(), 0L);
            if (sysSkuId <= 0) {//纯商品
                pureItemIds.add(sysItemId);
            } else {
                sysItemIds.add(sysItemId);
                sysSkuIds.add(sysSkuId);
            }
        }
    }

    /**
     * Trade能否拆分
     */
    public static boolean canSplitTrade(Staff staff, Trade trade) {
        return !com.raycloud.dmj.domain.trades.utils.TradeUtils.isFxSource(trade) //非分销
                && !com.raycloud.dmj.domain.trades.utils.TradeUtils.isPlatformFxTrade(trade)  //非平台分销
                && !com.raycloud.dmj.domain.trades.utils.TradeUtils.isQimenFxSource(trade) //非奇门分销
                && TradeUtils.getNotSplitTag(trade) == null //没有不允许拆分的标签
                && !TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RISK) //没有风控异常
                && !TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.HALT)
                && !TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.BLACK_NICK)
                && !TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UPLOAD_EXCEPT)
                ;
    }

    /**
     * Order能否按数量拆分
     */
    public static boolean canSplitOrderNum(Staff staff, Order order) {
        return TradeStatusUtils.isWaitAudit(order.getSysStatus())//待审核
                && !NumberUtils.isEquals(order.getIsVirtual(), 1)//非虚拟商品
                && !RefundUtils.isRefundOrder(order)//非退款
                && !OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.RELATION_CHANGED)//非对应关系改动
                ;
    }

    public static String getMatchMsg(TradeWarehouseContext context, Trade trade, Long newWarehouseId) {
        return getMatchMsg(context, trade, newWarehouseId, null);
    }

    public static String getMatchMsg(TradeWarehouseContext context, Trade trade, Long newWarehouseId, String reason) {
        return getMatchMsg(context, trade.getWarehouseId(), newWarehouseId, reason);
    }

    public static String getMatchMsg(TradeWarehouseContext context, Long oldWarehouseId, Long newWarehouseId, String reason) {
        // 原仓
        Warehouse oldWarehouse = NumberUtils.negative2Zero(oldWarehouseId) > 0
                ? context.getWarehouseMap().get(oldWarehouseId)
                : null;

        // 新仓
        Warehouse newWarehouse = context.getWarehouseMap().get(newWarehouseId);

        // 仓库变更信息
        String changeMsg = Objects.isNull(oldWarehouse)
                ? String.format("分配仓库[%s]", newWarehouse.getName())
                : String.format("修改仓库,原仓:[%s];新仓:[%s]", oldWarehouse.getName(), newWarehouse.getName());

        // 仓库分配类型
        String strategyMsg = StringUtils.isBlank(reason) ? context.getMatchTypeEnum().getMsg() : reason;

        // 配置标注信息
        String endMsg = context.isNoMatchSetDefaultWarehouse()
                ? "（勾选配置库存不满足匹配默认仓）"
                : "";

        switch (context.getOpEnum()) {
            case TRADE_SYNC: // 订单同步
                return String.format("自动化分仓执行:[%s]%s%s", strategyMsg, changeMsg, endMsg);
            case WAREHOUSE_AI_MATCH: // 智能分仓
                return String.format("手动智能分仓执行:[%s]%s%s", strategyMsg, changeMsg, endMsg);
            case AUTO_RE_MATCH_WAREHOUSE: // 重算分仓
                String reMatchEventMsg = Objects.isNull(context.getEvent()) ? "" : context.getEvent().getMsg();
                return Objects.nonNull(context.getEvent())
                        ? String.format("[%s]触发重算分仓执行:[%s]%s%s", reMatchEventMsg, strategyMsg, changeMsg, endMsg)
                        : String.format("触发重算分仓执行:[%s]%s%s", strategyMsg, changeMsg, endMsg);
            case WAREHOUSE_CHANGE: // 手动变更
                return String.format("手动更换仓库:%s", changeMsg);
            default:    // 默认处理
                return String.format("%s:%s", context.getOpEnum().getName(), changeMsg);
        }
    }

    /**
     * 未拆分的结果
     */
    public static void matchResultData(TradeWarehouseContext context, Trade trade, Long toWarehouseId, TradeWarehouseMatchResult result) {
        matchResultData(context, trade, toWarehouseId, result, null);
    }

    /**
     * 未拆分的结果
     */
    public static void matchResultData(TradeWarehouseContext context, Trade trade, Long toWarehouseId, TradeWarehouseMatchResult result, String otherStrategy) {
        if (!NumberUtils.isEquals(toWarehouseId, trade.getWarehouseId())) {
            String matchMsg = getMatchMsg(context, trade, toWarehouseId, otherStrategy);
            result.resultData = new TradeWarehouseMatchResult.ResultData(toWarehouseId, matchMsg);
        }
    }

    /**
     * 未拆分的结果
     */
    public static void matchResultDataWithStock(Staff staff,
                                                TradeWarehouseContext context,
                                                Trade trade,
                                                Long toWarehouseId,
                                                TradeWarehouseMatchResult result,
                                                List<Order> applyStockOrders) {
        matchResultDataWithStock(staff, context, trade, toWarehouseId, result, applyStockOrders, null);
    }

    /**
     * 未拆分的结果
     */
    public static void matchResultDataWithStock(Staff staff,
                                                TradeWarehouseContext context,
                                                Trade trade,
                                                Long toWarehouseId,
                                                TradeWarehouseMatchResult result,
                                                List<Order> applyStockOrders,
                                                String otherStrategy) {
        if (!NumberUtils.isEquals(toWarehouseId, trade.getWarehouseId())) {
            String matchMsg = getMatchMsg(context, trade, toWarehouseId, otherStrategy);
            result.resultData = new TradeWarehouseMatchResult.ResultData(toWarehouseId, matchMsg);
            addStockUse(staff, context.getConfigContext(), context.getWarehouseStockUseMap(), toWarehouseId, applyStockOrders);
        }
    }

    /**
     * 将 splitOrders分配到 toWarehouse
     *
     * @param context       仓库匹配上下问题
     * @param trade         原始订单
     * @param splitOrders   需要拆分的商品
     * @param toWarehouseId 目标仓库
     * @param result        分仓结果
     */
    public static void matchResultSplitData(TradeWarehouseContext context,
                                            Trade trade,
                                            List<Order> splitOrders,
                                            Long toWarehouseId,
                                            TradeWarehouseMatchResult result) {
        String matchMsg = getMatchMsg(context, trade, toWarehouseId);
        TradeWarehouseMatchResult.ResultSplitData resultSplitData = new TradeWarehouseMatchResult.ResultSplitData(toWarehouseId, matchMsg);
        resultSplitData.toWarehouseId = toWarehouseId;
        splitOrders.forEach(splitOrder -> resultSplitData.orderIdSplitNumMap.put(splitOrder.getId(), splitOrder.getNum()));
        result.toWarehouseResultSplitData.computeIfAbsent(toWarehouseId, k -> new ArrayList<>()).add(resultSplitData);
    }

    /**
     * 扣掉分配仓已使用库存
     */
    public static void addStockUse(Staff staff, TradeConfigContext context, Map<Long, Map<String, Long>> warehouseStockUseMap, Long warehouseId, List<Order> orders) {
        if (CollectionUtils.isNotEmpty(orders)) {
            Map<String, Long> stockUseMap = warehouseStockUseMap.computeIfAbsent(warehouseId, k -> new HashMap<>());
            for (Order order : orders) {
                if (!order.isSuit()) {
                    addStockUse(stockUseMap, order);
                } else {
                    for (Order suit : order.getSuits()) {
                        if (!TradeStockUtils.needNotApplyStock(staff, context, suit)) {
                            addStockUse(stockUseMap, suit);
                        }
                    }
                }
            }
        }

    }

    /**
     * 扣掉默认仓已使用库存
     */
    public static void addStockUse(Map<String, Long> stockUseMap, Order order) {
        String itemKey = TradeItemUtils.getItemKey(order.getItemSysId(), order.getSkuSysId());
        stockUseMap.put(itemKey, NumberUtils.nvlLong(stockUseMap.get(itemKey), 0L) + order.getNum());
    }

    /**
     * 开了拆分时平台赠品默认跟随主品配置时，要对拆分数据进行调整
     */
    public static void adjustSplitByPlatGift(Staff staff, TradeWarehouseMatchResult result, boolean splitPlatGiftFollowMainOrder) {
        if (result == null || !splitPlatGiftFollowMainOrder || result.originOrderMap.isEmpty() || result.toWarehouseResultSplitData.isEmpty()) {
            return;
        }
        Map<Order, Long> platGift2MasterOrderIdMap = new HashMap<>();
        for (Order order : result.originOrderMap.values()) {
            if (order.isPlatformGift() && order.getOrderExt() != null && CollectionUtils.isNotEmpty(order.getOrderExt().getMasterOrderIds())) {
                Order masterOrder = getGiftMasterOrder(staff, order, result.originOrderMap);
                if (masterOrder != null) {
                    platGift2MasterOrderIdMap.put(order, masterOrder.getId());
                }
            }
        }
        if (platGift2MasterOrderIdMap.isEmpty()) {
            return;
        }
        for (Map.Entry<Order, Long> platGift2MasterOrderId : platGift2MasterOrderIdMap.entrySet()) {
            Order platGiftOrder = platGift2MasterOrderId.getKey();
            Long masterOrderId = platGift2MasterOrderId.getValue();
            //赠品所在的拆分数据,主品所在的拆分数据
            Map<Long, List<Map<Long, Integer>>> giftToWarehouseOrderIdSplitNumMap = new HashMap<>(), masterToWarehouseOrderIdSplitNumMap = new HashMap<>();
            result.toWarehouseResultSplitData.forEach((warehouseId, resultSplitDatas) -> {
                for (TradeWarehouseMatchResult.ResultSplitData resultSplitData : resultSplitDatas) {
                    if (resultSplitData.orderIdSplitNumMap.containsKey(platGiftOrder.getId())) {
                        giftToWarehouseOrderIdSplitNumMap.computeIfAbsent(warehouseId, k -> new ArrayList<>()).add(resultSplitData.orderIdSplitNumMap);
                    }
                    if (resultSplitData.orderIdSplitNumMap.containsKey(masterOrderId)) {
                        masterToWarehouseOrderIdSplitNumMap.computeIfAbsent(warehouseId, k -> new ArrayList<>()).add(resultSplitData.orderIdSplitNumMap);
                    }
                }
            });
            //去掉赠品里面的拆分数据
            if (!giftToWarehouseOrderIdSplitNumMap.isEmpty()) {// 赠品跟随主品（赠品从拆分的数据里面移除）
                for (Map.Entry<Long, List<Map<Long, Integer>>> entry : giftToWarehouseOrderIdSplitNumMap.entrySet()) {
                    Iterator<Map<Long, Integer>> orderIdSplitNums = entry.getValue().iterator();
                    while (orderIdSplitNums.hasNext()) {
                        Map<Long, Integer> giftOrderIdSplitNum = orderIdSplitNums.next();
                        giftOrderIdSplitNum.remove(platGiftOrder.getId());
                        if (giftOrderIdSplitNum.isEmpty()) {
                            orderIdSplitNums.remove();
                        }
                    }
                    if (entry.getValue().isEmpty()) {
                        List<TradeWarehouseMatchResult.ResultSplitData> splitDatas = result.toWarehouseResultSplitData.get(entry.getKey());
                        if (CollectionUtils.isNotEmpty(splitDatas)) {
                            Iterator<TradeWarehouseMatchResult.ResultSplitData> iterator = splitDatas.iterator();
                            while (iterator.hasNext()) {
                                TradeWarehouseMatchResult.ResultSplitData splitData = iterator.next();
                                Map<Long, Integer> orderIdSplitNumMap = splitData.orderIdSplitNumMap;
                                orderIdSplitNumMap.remove(platGiftOrder.getId());
                                if (orderIdSplitNumMap.isEmpty()) {
                                    iterator.remove();
                                }
                            }
                            if (splitDatas.isEmpty()) {
                                result.toWarehouseResultSplitData.remove(entry.getKey());
                            }
                        }
                    }
                }
            }
            //将赠品数据添加到主品里面，主品有拆分，添加到拆分里面，主品未拆分不用处理
            if (!masterToWarehouseOrderIdSplitNumMap.isEmpty()) {
                // 赠品跟随主品（赠品添加到主品拆分的数据里面,放到第一个）
                for (Map.Entry<Long, List<Map<Long, Integer>>> entry : masterToWarehouseOrderIdSplitNumMap.entrySet()) {
                    entry.getValue().get(0).put(platGiftOrder.getId(), platGiftOrder.getNum());
                    break;
                }
            }
        }
    }

    private static Order getGiftMasterOrder(Staff staff, Order platGiftOrder, Map<Long, Order> originOrderMap) {
        List<Order> masterOrders = new ArrayList<>();
        for (Long masterOid : platGiftOrder.getOrderExt().getMasterOrderIds()) {
            for (Order order : originOrderMap.values()) {
                if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource()) && NumberUtils.isEquals(masterOid, order.getOid())) {
                    masterOrders.add(order);
                }
            }
        }
        if (masterOrders.isEmpty()) {
            Logs.ifDebug(TradeUtils.initBaseLog(staff) + String.format("平台赠品找不到对应的主品信息，giftOrderId=%s,masterOid=%s", platGiftOrder.getId(), platGiftOrder.getOrderExt().getMasterOrderIds()));
            return null;
        }
        //按承诺时间排序
        if (masterOrders.size() > 1) {
            masterOrders.sort((o1, o2) -> o2.getEstimateConTime() != null && o1.getEstimateConTime() != null ? o2.getEstimateConTime().compareTo(o1.getEstimateConTime()) : 0);
        }
        return masterOrders.get(0);
    }

    public static List<List<Long>> groupTrades(Staff staff, List<Trade> trades) {
        List<List<Long>> result = new ArrayList<>();
        int batchSize = TradeWarehouseConfigUtils.getBatchLimit(staff);
        if (trades.size() <= batchSize * 1.5) {
            result.add(com.raycloud.dmj.domain.trades.utils.TradeUtils.toSidList(trades));
            return result;
        }
        Map<Long, List<Trade>> warehouseMap = new HashMap<>();
        trades.forEach(trade -> warehouseMap.computeIfAbsent(NumberUtils.nvlLong(trade.getWarehouseId(), -1L), k -> new ArrayList<>()).add(trade));
        List<Long> groupSids = new ArrayList<>();
        for (Map.Entry<Long, List<Trade>> entry : warehouseMap.entrySet()) {
            if (entry.getValue().size() < batchSize) {
                result.add(com.raycloud.dmj.domain.trades.utils.TradeUtils.toSidList(entry.getValue()));
            } else {
                for (List<Trade> subTrades : Lists.partition(entry.getValue(), batchSize)) {
                    if (subTrades.size() < batchSize) {
                        groupSids.addAll(com.raycloud.dmj.domain.trades.utils.TradeUtils.toSidList(subTrades));
                    } else {
                        result.add(com.raycloud.dmj.domain.trades.utils.TradeUtils.toSidList(subTrades));
                    }
                }
            }
        }
        if (!groupSids.isEmpty()) {
            result.addAll(Lists.partition(groupSids, batchSize));
        }
        return result;
    }

    /**
     * 所以平台订单通用校验
     */
    public static void checkTemplateOutsid(List<Trade> tradeList, String content) {

        List<Trade> filterTrades = tradeList.stream()
                .filter(trade -> StringUtils.isNotBlank(trade.getOutSid()) && (NumberUtils.negative2Zero(trade.getIsUpload()) == 1 || TradeTagUtils.checkIfExistTag(trade, SystemTags.TAG_PRE_UPLOAD_CONSIGN)))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterTrades)) {
            return;
        }

        List<Trade> twentyPreTrades = filterTrades.subList(0, Math.min(filterTrades.size(), 20));

        String warnSidStr = Lists.partition(twentyPreTrades, 3)
                .stream()
                .map(com.raycloud.dmj.domain.trades.utils.TradeUtils::toSidsStr)
                .collect(Collectors.joining(",<br>"));

        String msg = "<div style=\"word-break:break-all;\">" +
                "<div style=\"font-size: 24px;color: red;\">风险提示！！！</div>" +
                "<span>预发货订单更换单号可能会被平台</span><span style=\"font-size:20px;color:red\">罚款!!!</span><br>" +
                "<span>已上传发货订单共<span style=\"font-size:20px;color:red\">" + filterTrades.size() + "</span>条" + (filterTrades.size() > 20 ? "，此处只展示前20条系统单号" : "") + ":</span><br>" +
                "<span>" + warnSidStr + "</span><br>" +
                "<span>确认要</span><span style=\"font-size:20px;color:red\">强制执行</span>" +
                "<span>:" + content + "操作?</span>" +
                "</div>";
        throw new PddCheckPtException(msg);
    }

    /**
     * 匹配的仓库是否达到上线
     *
     * @param warehouseMatchMaxMap 仓库配置上线
     * @param matchWarehouseId     匹配的仓库id
     */
    public static boolean arriveWarehouseMatchMax(Staff staff, Map<Long, TradeWarehouseMatchMax> warehouseMatchMaxMap, Long matchWarehouseId) {
        if (warehouseMatchMaxMap == null || !warehouseMatchMaxMap.containsKey(matchWarehouseId)) {
            return false;
        }
        TradeWarehouseMatchMax matchMax = warehouseMatchMaxMap.get(matchWarehouseId);
        //设置的上线
        long autoMatchMax = NumberUtils.nvlLong(matchMax.getAutoMatchMax(), 0L);
        //之前已匹配数
        long matchCount = NumberUtils.nvlLong(matchMax.getMatchCount(), 0L);
        //本次新增的匹配数
        long addMatchCount = NumberUtils.nvlLong(matchMax.getAddMatchCount(), 0L);
        if (addMatchCount + matchCount > autoMatchMax) {
            Logs.ifDebug(TradeUtils.initBaseLog(staff) + String.format("自动分仓已达到今日仓库上限，matchWarehouseId=%s,autoMatchMax=%s,matchCount=%s,addMatchCount=%s", matchWarehouseId, autoMatchMax, matchCount, addMatchCount));
            return true;
        }
        return false;
    }

    /**
     * 增加匹配仓库对应的已匹配数量
     *
     * @param warehouseMatchMaxMap 仓库配置上线
     * @param matchWarehouseId     匹配的仓库id
     */
    public static void addWarehouseMatchCount(Map<Long, TradeWarehouseMatchMax> warehouseMatchMaxMap, Long matchWarehouseId) {
        if (warehouseMatchMaxMap != null && warehouseMatchMaxMap.containsKey(matchWarehouseId)) {
            TradeWarehouseMatchMax matchMax = warehouseMatchMaxMap.get(matchWarehouseId);
            matchMax.setAddMatchCount(NumberUtils.nvlLong(matchMax.getAddMatchCount(), 0L) + 1);
        }
    }
}

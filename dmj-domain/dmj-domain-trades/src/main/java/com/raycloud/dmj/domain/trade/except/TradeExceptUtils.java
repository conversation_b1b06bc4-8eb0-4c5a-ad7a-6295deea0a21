package com.raycloud.dmj.domain.trade.except;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.ClueIdUtils;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.NewTradeExtendConfigEnum;
import com.raycloud.dmj.domain.enums.OrderOpeartEnum;
import com.raycloud.dmj.domain.enums.TradeDeliveringStatusEnum;
import com.raycloud.dmj.domain.trade.common.TradeBusinessFromEnum;
import com.raycloud.dmj.domain.trade.label.TradeLabel;
import com.raycloud.dmj.domain.trade.label.TradeLabelTypeEnum;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.domain.ExceptData;
import com.raycloud.dmj.except.domain.TradeExcept;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.except.enums.ExceptOldEnum;
import com.raycloud.dmj.except.utils.ExceptUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName TradeExceptUtils
 * @Description 订单异常工具类
 * <AUTHOR>
 * @Date 2023/2/16 10:47
 * @Version 1.0 其他模块暂时不要使用，用旧的方法，等稳定后在沟通, 该类下面的方法的使用最好先和于杨凡沟通一下
 */
public abstract class TradeExceptUtils {

    private final static Map<ExceptEnum, List<String>> exceptSourceMap = new HashMap<>();
    /**
     * 旧的异常trade上需要用到的字段
     */
    public static String OLD_EXCEPT_FILED="t.v,t.excep,t.item_excep,t.except_ids,t.sys_status,t.payment,t.receiver_name,t.address_changed,t.receiver_address,t.receiver_mobile,t.receiver_phone,t.receiver_name,t.type,t.source,t.sub_source,t.black_buyer_nick,t.seller_memo_update,t.unattainable,t.is_halt,t.is_refund";


    static {
        //风控异常
        exceptSourceMap.put(ExceptEnum.RISK, Arrays.asList(
                CommonConstants.PLAT_FORM_TYPE_PDD,
                CommonConstants.PLAT_FORM_TYPE_YYJK,
                CommonConstants.PLAT_FORM_TYPE_ELEHEALTH,
                CommonConstants.PLAT_FORM_TYPE_VIPJITX,
                CommonConstants.PLAT_FORM_TYPE_JD,
                CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU,
                CommonConstants.PLAT_FORM_TYPE_WXSPH,
                CommonConstants.PLAT_FORM_TYPE_XHS,
                CommonConstants.PLAT_FORM_TYPE_FXG,
                CommonConstants.PLAT_FORM_TYPE_BAIDU_HEALTH,
                CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU,
                CommonConstants.PLAT_FORM_TYPE_SUMAITONG
        ));
        //pdd缺货已处理异常
        exceptSourceMap.put(ExceptEnum.PDD_STOCK_OUT, Collections.singletonList(CommonConstants.PLAT_FORM_TYPE_PDD));
    }



    /**
     * 是否属于特定平台的异常
     *
     * @param source     平台source
     * @param exceptEnum 异常枚举
     * @return true：ExceptEnum是source这个平台才有的异常  false：ExceptEnum不是source这个平台的异常
     */
    @Deprecated
    public static boolean isPlatExcept(String source, ExceptEnum exceptEnum) {
        List<String> exceptSources = exceptSourceMap.get(exceptEnum);
        return CollectionUtils.isNotEmpty(exceptSources) && exceptSources.contains(source);
    }

    /**
     *  是否是平台异常
     * @param trade
     * @param exceptEnum
     * @return
     */
    public static boolean isPlatExcept(Trade trade, ExceptEnum exceptEnum) {
        return isPlatExcept(trade.getSource(),exceptEnum);
    }

    /**
     *  风控异常判断的平台
     * @param trade
     * @return
     */
    public static boolean isPlatRiskExcept(Trade trade){
        boolean b = CommonConstants.PLAT_FORM_TYPE_PDD.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_YYJK.equals(trade.getSubSource())
                || CommonConstants.PLAT_FORM_TYPE_ELEHEALTH.equals(trade.getSubSource())
                || CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource())
                || CommonConstants.PLAT_FORM_TYPE_FXG.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_SUMAITONG.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_JD.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_XHS.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_JD_VC.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_BAIDU_HEALTH.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_WXSPH.equals(trade.getSource())
                || TradeExceptWhiteUtils.openImitateExcept(trade.getCompanyId());
        return b;
    }

    /**
     * 解析子订单异常
     */
    @Deprecated
    public static Set<Integer> parseOrderExcept(Trade trade, Order order) {
        Set<Integer> result = new HashSet<>();
        //已删除的不计算
        if (order.getEnableStatus() == 0) {
            return result;
        }
        if (StringUtils.isBlank(order.getSysStatus())) {
            result.add((int) ExceptEnum.ORDER_LOST_SYS_STATUS.getOldExceptEnum().getOldIdx());
        }

        Set<Long> exceptIds = getTradeExceptData(trade).getOrderExceptIds().get(order.getId());
        if (CollectionUtils.isEmpty(exceptIds)) {
            return result;
        }

        //已发货的订单只考虑退款异常
        if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
            if (exceptIds.contains(ExceptEnum.REFUNDING.getId())) {
                result.add((int) ExceptEnum.REFUNDING.getOldExceptEnum().getOldIdx());
            }
            return result;
        }

        for (Long exceptId : exceptIds) {
            ExceptEnum exceptEnum = ExceptEnum.exceptEnumMap.get(exceptId);
            if (exceptEnum != null) {
                result.add((int) exceptEnum.getOldExceptEnum().getOldIdx());
            }
        }

        return result;
    }

    /**
     * 解析订单异常
     */
    @Deprecated
    public static Set<Integer> parseTradeExcept(Staff staff,Trade trade) {
        Set<Integer> result = new HashSet<>();

        Set<Long> exceptIds = getTradeExceptData(trade).getExceptIds();
        //没有商品异常
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            exceptIds.add(ExceptEnum.LOST_ITEM.getId());
            exceptIds.add(ExceptEnum.LOST_MSG.getId());
        }
        //信息缺失异常
        if (isLostMsg(staff,trade)) {
            exceptIds.add(ExceptEnum.LOST_MSG.getId());
        }

        if (CollectionUtils.isNotEmpty(exceptIds)) {
            for (Long exceptId : exceptIds) {
                ExceptEnum exceptEnum = ExceptEnum.exceptEnumMap.get(exceptId);
                if (exceptEnum != null) {
                    result.add((int) exceptEnum.getOldExceptEnum().getOldIdx());
                } else {//包含自定义异常的
                    result.add((int) ExceptOldEnum.OLD_CUSTOM.getOldIdx());
                }
            }
        }

        return result;
    }

    /**
     * 地址、手机/固话、收件人、商品信息缺失的订单
     */

    public static boolean isLostMsg(Staff staff,Trade trade) {
        return TradeExceptOldUtils.isLostMsg(staff,trade);
    }



    /**
     * 订单是否异常
     *
     * @param trade 订单
     * @return true：异常单 false：非异常单
     */
    public static boolean isExcept(Staff staff, Trade trade) {
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade != null && trade.getCompanyId() != null ? trade.getCompanyId() : -1L)) {
            int isExcep = parseIsExcep(staff, trade);
            Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
            if (MapUtils.isNotEmpty(subTradeExceptDatas)) {
                for (ExceptData exceptData : subTradeExceptDatas.values()) {
                    if (exceptData == null) {
                        continue;
                    }
                    if (ExceptUtils.calculateIsExcept(exceptData) == 1) {
                        isExcep = 1;
                        break;
                    }
                }
            }
            return isExcep == 1 || isUserUnActive(staff, trade);
        }
        return TradeExceptOldUtils.isExcept(staff, trade) || isUserUnActive(staff, trade);
    }

    /**
     * 判断合单子单是否存在异常
     * com.raycloud.dmj.domain.trade.except.TradeExceptUtils#isExcept(com.raycloud.dmj.domain.account.Staff, com.raycloud.dmj.domain.trades.Trade) 只是判断当前单
     * getSubTradeIsExcept 是判断合单整体
     * @param staff
     * @param trade
     * @return
     */
    public static Integer getSubTradeIsExcept(Staff staff, Trade trade) {
        if (!TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade != null && trade.getCompanyId() != null ? trade.getCompanyId() : -1L)) {
            return trade.getIsExcep();
        }
        if (!TradeUtils.isMerge(trade)) {
            return getIsExcep(staff,trade);
        }
        Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
        if (MapUtils.isNotEmpty(subTradeExceptDatas)) {
            boolean b = subTradeExceptDatas.values().stream().filter(Objects::nonNull).anyMatch(exceptData -> CollectionUtils.isNotEmpty(exceptData.getExceptIds())&&!ExceptUtils.isOnlyContainsExcept(staff,exceptData,ExceptEnum.UPLOAD_EXCEPT.getId()));
            return b ? 1 : 0;
        }
        return 0;
    }
    /**
     *
     * @param staff
     * @param trade  订单
     * @return 1异常,0异常
     */
    @Deprecated
    public static int getIsExcep(Staff staff, Trade trade) {
        return isExcept(staff, trade) ? 1 : 0;
    }



    /**
     * 订单是否变更了指定的异常
     *
     * @param trade      订单
     * @param ExceptEnum 异常枚举
     * @return true：有变化 false：无变化
     */
    @Deprecated
    public static boolean isChangExcept(Trade trade, ExceptEnum ExceptEnum) {
        return isChangExcept(trade, ExceptEnum.getId());
    }

    /**
     * 订单是否变更了指定的异常
     *
     * @param trade    订单
     * @param exceptId 异常id
     * @return true：有变化 false：无变化
     */
    @Deprecated
    public static boolean isChangExcept(Trade trade, long exceptId) {
        return ExceptUtils.isChangExcept(getTradeExceptData(trade), exceptId);
    }

    /**
     * 子订单是否变更了指定的异常
     *
     * @param trade      订单
     * @param order      子订单
     * @param ExceptEnum 异常枚举
     * @return true：有变化 false：无变化
     */
    @Deprecated
    public static boolean isChangOrderExcept(Trade trade, Order order, ExceptEnum ExceptEnum) {
        return isChangOrderExcept(trade, order, ExceptEnum.getId());
    }

    /**
     * 子订单是否变更了指定的异常
     *
     * @param trade    订单
     * @param order    子订单
     * @param exceptId 异常id
     * @return true：有变化 false：无变化
     */
    @Deprecated
    public static boolean isChangOrderExcept(Trade trade, Order order, long exceptId) {
        return ExceptUtils.isChangExcept(getTradeExceptData(trade), exceptId, order.getId());
    }

    /**
     * 是否有异常变更
     *
     * @param trade 订单
     * @return true：有变化 false：无变化
     */
    public static boolean hasExceptChange(Staff staff,Trade trade) {
        Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
        ExceptData exceptData = trade.getExceptData();
        if (subTradeExceptDatas == null && exceptData == null) {
            return false;
        }
        boolean change = false;
        if (subTradeExceptDatas != null) {
            // 子单变更也要判断
            change = subTradeExceptDatas.values().stream().anyMatch(ExceptUtils::hasExceptChange);
        }

        if (exceptData != null) {
            change = ExceptUtils.hasExceptChange(exceptData) || change;
        }
        return change;
    }

    /**
     * 订单是否包含某种异常
     *
     * @param trade    订单
     * @param exceptId 异常id
     * @return true：包含 false：不包含
     */
    @Deprecated
    public static boolean isContainsExcept(Staff staff,Trade trade, long exceptId) {
        return ExceptUtils.isContainsExcept(getTradeExceptData(trade), exceptId);
    }

    /**
     * 订单是否包含某种异常
     *
     * @param trade      订单
     * @param exceptEnum 异常枚举
     * @return true：包含 false：不包含
     */
    @Deprecated
    private static boolean isContainsExcept(Staff staff,Trade trade, ExceptEnum exceptEnum) {
        return isContainsExcept(staff,trade, exceptEnum.getId());
    }

    /**
     * 订单是否包含全部指定的异常
     *
     * @param trade       订单
     * @param ExceptEnums 多个异常枚举
     * @return true：包含 false：不包含
     */
    @Deprecated
    public static boolean isContainsAllExcept(Staff staff,Trade trade, ExceptEnum... ExceptEnums) {
        for (ExceptEnum ExceptEnum : ExceptEnums) {
            if (!isContainsExcept(staff,trade, ExceptEnum)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 子订单是否包含某种异常
     *
     * @param trade    订单
     * @param order    子订单订单
     * @param exceptId 异常id
     * @return true：包含 false：不包含
     */
    @Deprecated
    public static boolean isContainsExcept(Staff staff,Trade trade, Order order, long exceptId) {
        Map<Long, Set<Long>> orderExceptIdsMap = getTradeExceptData(trade).getOrderExceptIds();
        return orderExceptIdsMap != null && orderExceptIdsMap.size() != 0 && ExceptUtils.isContainsExcept(orderExceptIdsMap.get(order.getId()), exceptId);
    }

    /**
     * 子订单是否包含某种异常
     *
     * @param trade      订单
     * @param order      子订单订单
     * @param exceptEnum 异常枚举
     * @return true：包含 false：不包含
     */
    @Deprecated
    public static boolean isContainsExcept(Staff staff,Trade trade, Order order, ExceptEnum exceptEnum) {
        return isContainsExcept(staff,trade, order, exceptEnum.getId());
    }

    /**
     * 是否仅包含某种异常
     *
     * @param trade      订单
     * @param exceptEnum 异常枚举
     * @return true：包含 false：不包含
     */
    public static boolean isOnlyContainsExcept(Staff staff, Trade trade, ExceptEnum exceptEnum) {
        // 默认走旧的目前
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade != null && trade.getCompanyId() != null ? trade.getCompanyId() : -1L)) {
            Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
            Set<Long> exceptIds = new HashSet<>(getTradeExceptData(trade).getExceptIds());
            if (MapUtils.isNotEmpty(subTradeExceptDatas)) {
                for (ExceptData exceptData : subTradeExceptDatas.values()) {
                    if (exceptData != null && CollectionUtils.isNotEmpty(exceptData.getExceptIds())) {
                        exceptIds.addAll(exceptData.getExceptIds());
                    }
                }
            }
            if (exceptEnum == ExceptEnum.CUSTOM) {
                // 只包含自定义异常
                boolean b = ExceptEnum.allExceptEnums.stream().noneMatch(e -> exceptIds.contains(e.getId()));
                return b && CollectionUtils.isNotEmpty(exceptIds);
            }
            return ExceptUtils.isOnlyContainsExcept(staff, exceptIds, exceptEnum.getId());
        }
        return TradeExceptOldUtils.isOnlyContainsExcept(staff, trade, exceptEnum);
    }

    /**
     * 清除所有异常：系统异常+自定义异常
     *
     * @param trade 订单
     */
    @Deprecated
    public static void clearAllExcept(Staff staff,Trade trade) {
        removeAllSysExcept(staff,trade);
        removeAllCustomExcept(staff,trade);
        TradeExceptOldUtils.setTradeIsExcep(trade, 0);
    }

    /**
     * 删除所有系统异常
     *
     * @param trade 订单
     */
    @Deprecated
    public static void removeAllSysExcept(Staff staff,Trade trade) {
        // 清除tradeExcept
        Set<Long> exceptIds = getTradeExceptData(trade).getExceptIds();
        if (CollectionUtils.isNotEmpty(exceptIds)) {
            for (Long exceptId : exceptIds) {
                ExceptEnum exceptEnum = ExceptEnum.exceptEnumMap.get(exceptId);
                if (exceptEnum != null) {
                    updateExcept(staff,trade, exceptEnum.getId(), 0);
                }
            }
        } else {//兼容处理
            for (ExceptEnum exceptEnum : ExceptEnum.allExceptEnums) {
                updateExcept(staff,trade, exceptEnum.getId(), 0);
            }
        }
    }

    /**
     * 订单是否有自定义异常
     *
     * @param trade 订单
     * @return true：包含 false：不包含
     */
    @Deprecated
    public static boolean isCustomExcept(Trade trade) {
        Set<Long> exceptIds = getTradeExceptData(trade).getExceptIds();
        if (CollectionUtils.isNotEmpty(exceptIds)) {
            for (Long exceptId : exceptIds) {
                if (ExceptEnum.isCustomExcept(exceptId)) {
                    return true;
                }
            }
        }
        return false;
    }



    /**
     * 删除所有自定义异常
     *
     * @param trade 订单
     */
    @Deprecated
    public static void removeAllCustomExcept(Staff staff,Trade trade) {
        // 清除tradeExcept
        Set<Long> exceptIds = getTradeExceptData(trade).getExceptIds();
        if (CollectionUtils.isNotEmpty(exceptIds)) {
            for (Long exceptId : exceptIds) {
                if (!ExceptEnum.sysExceptIds.contains(exceptId)) {
                    updateExcept(staff,trade, exceptId, 0);
                }
            }
        }
    }

    /**
     * 删除所有自定义异常
     *
     * @param trade     订单
     * @param customIds 需要删除的自定义异常id
     */
    @Deprecated
    public static void removeCustomExcept(Staff staff,Trade trade, List<Long> customIds) {
        // 清除tradeExcept
        Set<Long> exceptIds = getTradeExceptData(trade).getExceptIds();
        if (CollectionUtils.isNotEmpty(exceptIds)) {
            for (Long exceptId : exceptIds) {
                if (customIds.contains(exceptId)) {
                    updateExcept(staff,trade, exceptId, 0);
                }
            }
        }
    }

    /**
     * 更新一个异常值
     * @param trade   订单
     * @param exceptEnum 异常枚举
     * @param add 异常值 false代表没有这个异常枚举，需要删除，true代表有这个值（老的异常有位运算）
     */
    public static void updateExcept(Staff staff,Trade trade, ExceptEnum exceptEnum, boolean add) {
        if (exceptEnum != null) {
            long exceptVal = add ? 1L : 0L;
            updateExcept(staff,trade, exceptEnum.getId(), exceptVal);
        }
    }

    /**
     *
     * @param staff
     * @param trade 订单
     * @param exceptEnum 异常枚举
     * @param exceptVal 异常值 0代表没有这个异常枚举，需要删除，非0代表有这个值（老的异常有位运算）
     */
    public static void updateExcept(Staff staff,Trade trade, ExceptEnum exceptEnum, Long exceptVal) {
        if (exceptEnum != null) {
            updateExcept(staff,trade, exceptEnum.getId(), exceptVal);
        }
    }


    /**
     * 更新一个异常值
     *
     * @param trade     订单
     * @param exceptId  异常id
     * @param exceptVal 异常值 0代表没有这个异常枚举，需要删除，非0代表有这个值（老的异常有位运算）
     */
    public static void updateExcept(Staff staff,Trade trade, Long exceptId, long exceptVal) {
        ExceptData exceptData = getTradeExceptData(trade);
        ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
        if (exceptEnum != null) {

            if (Objects.equals(exceptId, ExceptEnum.REFUNDING.getId())) {
                // 退款异常,trade 中合单中的order 在业务计算时会包含子单的order导致新版的计算不对，需要重新根据order计算
                List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
                if (CollectionUtils.isNotEmpty(orders4Trade) && trade.getMergeSid() != null && trade.getMergeSid() > 0) {
                    List<Order> collect = orders4Trade.stream().filter(order -> Objects.equals(order.getSid(), trade.getSid())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        boolean b = collect.stream().anyMatch(order -> OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.REFUNDING));
                        exceptVal = b || TradePlatExceptUtils.isContainExcept(staff, trade, exceptEnum) ? 1L : 0L;
                    }

                }
            }
            // 三方仓异常需要特殊处理 exceptId 为三方仓自己的id需要，兼容
            ExceptUtils.updateExcept(exceptData, exceptEnum.getId(), exceptVal);
        } else {
            ExceptUtils.updateExcept(exceptData, exceptId, exceptVal);
        }
        // 直接取消trade的异常时，先同步取消对应的order的异常
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        // 是否是order包含的异常，并且是取消异常
        if (CollectionUtils.isNotEmpty(orders4Trade) && ExceptEnum.orderExceptEnumMap.get(exceptId) != null && exceptVal == 0L) {
            List<Order> orderList = OrderUtils.toFullOrderList(orders4Trade, false);
            for (Order order : orderList) {
               // updateExcept(staff, trade, order, exceptId, exceptVal);
                OrderExceptUtils.updateExceptOrder(staff,order,exceptId,exceptVal);
            }
        }
        if (exceptEnum == null) {
            //自定义异常
            TradeExceptOldUtils.syncTradeExceptIdsNew2Old(staff,trade, exceptId, exceptVal);
        } else {//系统异常
            //老的方式 后续如果全量同步后  只需要去掉下面的代码执行
            TradeExceptOldUtils.syncTradeExceptNew2Old(staff,trade, exceptEnum, exceptVal);
        }
        Integer isExcep = null;
        boolean isWaitSellerSend = TradeStatusUtils.isWaitSellerSend(trade.getSysStatus());
        if(isContainExcept(staff,trade,ExceptEnum.REFUNDING)){
            // 包含退款异常时，不管是不是发货后isExcep都要设置为1
            isExcep=1;
        } else if ((!isWaitSellerSend && StringUtils.isNotBlank(trade.getSysStatus())) || ExceptUtils.isOnlyContainsExcept(staff, exceptData, ExceptEnum.UPLOAD_EXCEPT.getId())) {
            // 标记时仅包含上传异常时,上传异常不标记isExcep 为1, 非待发货的单 is_excep 默认为0
            isExcep = 0;
        } else if ((exceptVal == 1 || (exceptVal == 2 && ExceptEnum.SELLER_MEMO_UPDATE == exceptEnum)) && CollectionUtils.isNotEmpty(exceptData.getExceptIds())) {
            // 留言备注异常 exceptVal 是2
            isExcep = 1;
        } else if (exceptVal == 0) {
            if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, -1L)) {
                isExcep = parseIsExcep(staff,trade);
            } else {
                Integer itemExcep = Optional.ofNullable(trade.getItemExcep()).orElse(0);
                Long excep = Optional.ofNullable(trade.getExcep()).orElse(0L);
                Integer isLostMsg = Optional.ofNullable(trade.getIsLostMsg()).orElse(0);
                Long isRefund = Optional.ofNullable(trade.getIsRefund()).orElse(0L);
                Integer isHalt = Optional.ofNullable(trade.getIsHalt()).orElse(0);
                Integer unattainable = Optional.ofNullable(trade.getUnattainable()).orElse(0);
                Integer sellerMemoUpdate = Optional.ofNullable(trade.getSellerMemoUpdate()).orElse(0);
                Integer addressChanged = Optional.ofNullable(trade.getAddressChanged()).orElse(0);
                Integer blackBuyerNick = Optional.ofNullable(trade.getBlackBuyerNick()).orElse(0);
                String exceptIds = Optional.ofNullable(trade.getExceptIds()).orElse("");
                if (itemExcep != 0 || excep != 0L || isLostMsg != 0 || isRefund != 0L || isHalt != 0 || unattainable != 0
                        || sellerMemoUpdate == 2 || addressChanged != 0 || blackBuyerNick != 0 || StringUtils.isNotBlank(exceptIds)) {
                    isExcep = 1;
                }else {
                    if (exceptVal == 0) {
                        isExcep = parseIsExcep(staff,trade);
                    }
                }
            }
        }
        // 开了白名单直接拿异常算一次
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, -1L)) {
            isExcep = parseIsExcep(staff, trade);
        }
        if (isExcep != null) {
            TradeExceptOldUtils.setTradeIsExcep(trade, isExcep);
        }
    }


    /**
     * 更新一个异常值
     *
     * @param trade      订单
     * @param order      子订单
     * @param ExceptEnum 异常枚举
     * @param exceptVal  异常值 0代表没有这个异常枚举，需要删除，非0代表有这个值（老的异常有位运算）
     */

    @Deprecated
    public static void updateExcept(Staff staff, Trade trade, Order order, ExceptEnum ExceptEnum, Long exceptVal) {
        if (ExceptEnum != null) {
            updateExcept(staff, trade, order, ExceptEnum.getId(), exceptVal);
        }
    }


    /**
     * 更新一个异常值
     *
     * @param trade      订单
     * @param order      子订单
     * @param ExceptEnum 异常枚举
     * @param add        异常值 false 代表没有这个异常枚举，需要删除,true 代表有这个值（老的异常有位运算）
     */
    @Deprecated
    public static void updateExcept(Staff staff, Trade trade, Order order, ExceptEnum ExceptEnum, boolean add) {
        updateExcept(staff, trade, order, ExceptEnum, add ? 1L : 0L);
    }

    /**
     * 更新一个异常值
     *
     * @param trade     订单
     * @param trade     子订单
     * @param exceptId  异常id
     * @param exceptVal 异常值 0代表没有这个异常枚举，需要删除，非0代表有这个值（老的异常有位运算）
     *  com.raycloud.dmj.domain.trade.except.TradeExceptUtils 内部使用,避免报错不
     */
    @Deprecated
    public static void updateExcept(Staff staff,Trade trade, Order order, Long exceptId, Long exceptVal) {
        if (order == null) {
            updateExcept(staff,trade, exceptId, exceptVal);
            return;
        }

        ExceptData exceptData = getTradeExceptData(trade);
        ExceptUtils.updateExceptOrder(exceptData, exceptId, exceptVal, order.getId());
        ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
        if (exceptEnum == null) {
            //自定义异常
            Set<Long> exceptIdsSet = getCustomExceptIds(staff, trade);
            if(exceptVal==1){
                exceptIdsSet.add(exceptId);
            }else {
                Iterator<Long> iterator = exceptIdsSet.iterator();
                while (iterator.hasNext()) {
                    if (iterator.next() == exceptId) {
                        iterator.remove();
                    }
                }
            }
            trade.setExceptIds(StringUtils.join(exceptIdsSet,","));
        } else {//系统异常
            //老的方式 后续如果全量同步后  只需要去掉下面的代码执行
            TradeExceptOldUtils.syncOrderExceptNew2Old(staff,trade, order, exceptEnum, exceptVal);
        }
    }
    @Deprecated
    public static List<String> getExceptNames(Set<Long> exceptIds) {
        List<String> exceptNames = new ArrayList<>();
        if (CollectionUtils.isEmpty(exceptIds)) {
            return exceptNames;
        }
        List<Long> customExceptIds = new ArrayList<>();
        for (Long exceptId : exceptIds) {
            ExceptEnum exceptEnum = ExceptEnum.exceptEnumMap.get(exceptId);
            if (exceptEnum != null) {
                exceptNames.add(exceptEnum.getChinese());
            } else {
                customExceptIds.add(exceptId);
            }
        }
        if (CollectionUtils.isNotEmpty(customExceptIds)) {
            exceptNames.add("包含自定义异常");
        }
        return exceptNames;
    }

    /**
     * 筛选出满足系统配置中状态和异常都符合条件的订单
     * 发货中状态以值来区分：待打印 1 待包装 2  待称重 4  待发货 8
     * 如果页面上勾选了多个，则传入相加的值即可。如：传入的值为3，则表示待打印和待包装的订单
     *
     * @param tradeList   订单列表
     * @param tradeConfig 订单配置
     */
    @Deprecated
    public static List<Trade> satisfyStatusAndExcep(List<Trade> tradeList, TradeConfig tradeConfig) {
        List<Trade> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(tradeList) || StringUtils.isEmpty(tradeConfig.getTradeExtendConfig())) {
            return result;
        }
        //获取配置中设置的订单状态
        int configStatus = tradeConfig.getInteger(NewTradeExtendConfigEnum.AUTO_RE_AUDIT_STATUS.getKey());
        //如果没有勾选状态
        if (configStatus == 0) {
            return result;
        }
        //获取配置中设置的订单异常
        List<String> exceptions = ArrayUtils.toStringList(tradeConfig.getString(NewTradeExtendConfigEnum.AUTO_RE_AUDIT_EXCEPTION.getKey()));
        //如果没有勾选异常
        if (CollectionUtils.isEmpty(exceptions)) {
            return result;
        }
        for (Trade trade : tradeList) {
            int tradeStatusValue = TradeDeliveringStatusEnum.getValueByCode(TradeStatusUtils.convertSysStatus(trade, tradeConfig));
            //如果订单的状态或异常不在配置范围内，说明该订单不符合条件
            if (tradeStatusValue == 0 || (tradeStatusValue & configStatus) == 0) {
                continue;
            }
            Set<String> tradeExceptEnglishs = TradeExceptViewUtils.getTradeExceptEnglish(null, trade,true);
            //自定义异常
            Set<Long> exceptIds = getTradeExceptData(trade).getExceptIds();
            if (CollectionUtils.isEmpty(tradeExceptEnglishs) && CollectionUtils.isEmpty(exceptIds)) {
                continue;
            }
            for (String exception : exceptions) {
                if (tradeExceptEnglishs.contains(exception)) {
                    result.add(trade);
                    break;
                }
                for (Long exceptId : exceptIds) {
                    if (StringUtils.equals(exception, exceptId.toString())) {
                        result.add(trade);
                        break;
                    }
                }
            }
        }
        return result;
    }


    /**
     *  所有的异常包含判断都走这个方法，双写结束后，读阶段可以用这个方法开启白名单
     * @param staff
     * @param trade
     * @param exceptEnum
     * @return
     */
    public static boolean isContainExcept(Staff staff, Trade trade, ExceptEnum exceptEnum) {

        boolean containsExcept = false;
        // 默认都是走旧的
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade != null && trade.getCompanyId() != null ? trade.getCompanyId() : -1L)) {
            containsExcept = isContainsExcept(staff, trade, exceptEnum);
        } else {
            containsExcept = TradeExceptOldUtils.isContainExcept(staff,trade, exceptEnum);
        }
        // 双写结束后走白名单,新的
        // 新的判断 com.raycloud.dmj.domain.trade.except.TradeExceptUtils.isContainsExcept(com.raycloud.dmj.domain.trades.Trade, com.raycloud.dmj.except.enums.ExceptEnum)
        return containsExcept;
    }

    /**
     * 所有的异常包含判断都走这个方法，双写结束后，读阶段可以用这个方法开启白名单
     * @param staff
     * @param trade
     * @param exceptId
     * @return
     */
    public static boolean isContainExcept(Staff staff, Trade trade, Long exceptId){

        ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
        if (exceptEnum != null) {
            exceptId = exceptEnum.getId();
        }
        boolean containsExcept = false;
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade != null && trade.getCompanyId() != null ? trade.getCompanyId() : -1L)) {
            containsExcept = isContainsExcept(staff, trade, exceptId);
        } else {
            containsExcept = TradeExceptOldUtils.isContainExcept(staff,trade, exceptId);
        }
        // 双写结束后走白名单
        // 新的判断 com.raycloud.dmj.domain.trade.except.TradeExceptUtils.isContainsExcept(com.raycloud.dmj.domain.trades.Trade, long)
        return containsExcept;
    }

    /**
     *  对比trade1 ，trade2 是否都包含exceptEnum异常
     * @param staff
     * @param trade1
     * @param trade2
     * @param exceptEnum
     * @return
     */
    public static boolean isEqualsExcept(Staff staff, Trade trade1, Trade trade2, ExceptEnum exceptEnum) {
        return trade1 != null && trade2 != null && isContainExcept(staff, trade1, exceptEnum) == isContainExcept(staff, trade2, exceptEnum);
    }



    /**
     *  同步origin 的异常
     * @param staff
     * @param update 需要更新的trade
     * @param origin 原trade
     * @param exceptEnum 异常枚举
     */
    public static void syncTradeExcept(Staff staff,Trade update, Trade origin, ExceptEnum exceptEnum){
        TradeExceptUtils.updateExcept(staff,update,exceptEnum,TradeExceptUtils.isContainExcept(staff,origin,exceptEnum));
    }




    /**
     * 是否店铺停用异常(店铺停用的订单是异常订单，但isExcep不用设置为1)
     *
     * @param trade 订单数据
     * @return true：店铺停用 false：店铺没有停用
     */
    public static boolean isUserUnActive(Staff staff, Trade trade) {
        if (staff != null && !(trade.isOutstock() || TradeUtils.isGxOrMixTrade(trade))) {
            User user = null;
            if (staff.getUserIdMap() != null) {
                user = staff.getUserIdMap().get(trade.getUserId());
            }
            if (user == null) {
                user = staff.getUserByUserId(trade.getUserId());
            }
            if (user != null && user.getActive() != null && user.getActive() == 0) {
                return true;
            }
        }
        return false;
    }


    /**
     * 填充stockStatus 的值,计算缺货异常，和商品未匹配异常
     * @param staff
     * @param trade
     * @param stockStatus
     */
    public static void setStockStatus(Staff staff, Trade trade, String stockStatus) {
        updateStockStatus(staff, trade, stockStatus);
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders4Trade)) {
            return;
        }
        // 暂时只计算标记异常
        for (Order order : orders4Trade) {
            if (OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.INSUFFICIENT)) {
                OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.INSUFFICIENT, 1L);
            }
            if (OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED)) {
                OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.UNALLOCATED, 1L);
            }
        }
        ExceptData exceptData = getTradeExceptData(trade);
        Map<Long, Set<Long>> orderExceptIds = exceptData.getOrderExceptIds();
        boolean b = orderExceptIds.values().stream().anyMatch(exceptIds -> exceptIds.contains(ExceptEnum.INSUFFICIENT.getId()));
        if (!b) {
            ExceptUtils.updateExcept(exceptData, ExceptEnum.INSUFFICIENT.getId(), 0L);
        }

    }

    /**
     * 更新trade 的stockStatus 时使用,对应update
     */
    public static void updateStockStatus(Staff staff, Trade trade, String stockStatus){
        trade.setStockStatus(stockStatus);
        // 库存不足
        if (Objects.equals(stockStatus, Trade.STOCK_STATUS_INSUFFICIENT)
                || Objects.equals(stockStatus, Trade.STOCK_STATUS_EMPTY)
                || Objects.equals(stockStatus, Trade.STOCK_STATUS_EXCEP)) {
            updateExcept(staff, trade, ExceptEnum.INSUFFICIENT, 1L);
            updateExcept(staff, trade, ExceptEnum.UNALLOCATED, 0L);
        } else if (Objects.equals(stockStatus, Trade.STOCK_STATUS_UNALLOCATED)) {
            // 商品未匹配
            updateExcept(staff, trade, ExceptEnum.INSUFFICIENT, 0L);
            updateExcept(staff, trade, ExceptEnum.UNALLOCATED, 1L);
        } else if (Objects.equals(stockStatus, Trade.STOCK_STATUS_RELATION_MODIFIED)) {
            // trade 的stock_status 为商品对应关系改动时，不处理
        }else if(Objects.equals(stockStatus, Trade.STOCK_STATUS_NORMAL)){
            // 库存正常时，清除所有的库存不足异常,因为缺货审核时order 的stockStatus 还是缺货状态,以trade的为准
            // 这里不清除子单异常
            clearTradePointExcept(staff, trade, ExceptEnum.INSUFFICIENT, false);
            clearTradePointExcept(staff, trade, ExceptEnum.UNALLOCATED, false);
        } else {
            updateExcept(staff, trade, ExceptEnum.INSUFFICIENT, 0L);
            updateExcept(staff, trade, ExceptEnum.UNALLOCATED, 0L);
        }
        if (Objects.equals(stockStatus, Trade.STOCK_STATUS_EMPTY) || Objects.equals(stockStatus, Trade.STOCK_STATUS_EXCEP)) {
        }
    }

    public static void setStockStatus(Staff staff, Trade trade, String stockStatus,boolean calOrder) {
        setStockStatus(staff,trade,stockStatus);
        if(!calOrder){
            return;
        }
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade).stream().filter(order->Objects.equals(trade.getSid(),order.getSid())).collect(Collectors.toList());
        // 根据order计算新的商品未匹配或者库存不足
        boolean unallocated=false;
        boolean insufficient=false;
        for(Order order:orders4Trade){
            boolean containsUnallocated = OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.UNALLOCATED);
            OrderExceptUtils.updateExceptOrder(staff,order,ExceptEnum.UNALLOCATED,containsUnallocated);
            if(!unallocated){
                unallocated = containsUnallocated;
            }
            boolean containsInsufficient = OrderPlatExceptUtils.isContainsExcept(staff, order, ExceptEnum.INSUFFICIENT);
            OrderExceptUtils.updateExceptOrder(staff,order,ExceptEnum.UNALLOCATED,containsInsufficient);
            if(!insufficient){
                insufficient = containsInsufficient;
            }
        }
        TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.INSUFFICIENT,insufficient);
        TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.UNALLOCATED,unallocated);
        // 合单主单,非库存不足子单库存不足stockStatus也会缺货，根据stockStatus计算会不准,新老异常兼容
        if (trade.getMergeSid() != null && trade.getMergeSid() > 0 && Objects.equals(trade.getSid(), trade.getMergeSid())) {
            ExceptData exceptData = getTradeExceptData(trade);
            Map<Long, Set<Long>> orderExceptIds = exceptData.getOrderExceptIds();
            boolean b = orderExceptIds.values().stream().anyMatch(exceptIds -> exceptIds.contains(ExceptEnum.INSUFFICIENT.getId()));
            if (!b) {
                ExceptUtils.updateExcept(exceptData, ExceptEnum.INSUFFICIENT.getId(), 0L);
            }
        }
    }




    /************************************************************************自定义异常*************************************************************************/
    /**
     *  是否存在自定义异常
     * @param staff
     * @param trade
     * @return
     */
    public static boolean isContainCustomExcept(Staff staff, Trade trade) {
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff,trade!=null && trade.getCompanyId() != null?trade.getCompanyId():-1L)) {
            Set<Long> customerExcept = ExceptUtils.getCustomerExceptIds(trade.getExceptData());
            return CollectionUtils.isNotEmpty(customerExcept);
        }
        return TradeExceptOldUtils.isContainCustomExcept(staff, trade);
    }


    public static boolean isSubTradeContainCustomExcept(Staff staff, Trade trade) {
        if (!TradeUtils.isMerge(trade) || !TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return false;
        }
        // 未关闭白名单的异常或者起了新读
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade.getCompanyId())) {
            Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
            if (MapUtils.isEmpty(subTradeExceptDatas)) {
                return false;
            }
            Collection<ExceptData> values = subTradeExceptDatas.values();
            for (ExceptData exceptData : values) {
                if (exceptData == null) {
                    continue;
                }
                Set<Long> customerExcept = ExceptUtils.getCustomerExceptIds(trade.getExceptData());
                if (CollectionUtils.isNotEmpty(customerExcept)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取自定义异常
     * @param staff
     * @param trade
     * @return
     */
    public static Set<Long> getCustomExceptIds(Staff staff, Trade trade) {
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff,trade!=null && trade.getCompanyId() != null?trade.getCompanyId():-1L)) {
            Set<Long> customerExcept = ExceptUtils.getCustomerExceptIds(trade.getExceptData());
            return customerExcept;
        }
        return TradeExceptOldUtils.getCustomExceptIds(staff,trade);
    }

    /**
     * 获取合单的自定义异常
     * @param staff
     * @param trade
     * @return
     */
    public static Set<Long> getMergeCustomExceptIds(Staff staff, Trade trade) {
        if (TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)&&MapUtils.isNotEmpty(trade.getSubTradeExceptDatas())) {
            Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
            Collection<ExceptData> values = subTradeExceptDatas.values();
            Set<Long> customerExcepts = ExceptUtils.getCustomerExceptIds(trade.getExceptData());
            for(ExceptData exceptData:values){
                Set<Long> customerExcept = ExceptUtils.getCustomerExceptIds(exceptData);
                if(CollectionUtils.isNotEmpty(customerExcept)){
                    customerExcepts.addAll(customerExcept);
                }
            }
            return customerExcepts;
        }
        return new HashSet<>();
    }

    /**
     * 获取自定义异常
     * @param staff
     * @param trade
     * @return
     */
    public static Set<String> getCustomExceptIdsStr(Staff staff, Trade trade) {
        Set<String> customerExcept = getCustomExceptIds(staff, trade).stream().map(String::valueOf).collect(Collectors.toSet());
        return customerExcept;
    }

    public static Set<String> getOldCustomExceptIdsStr(Staff staff, Trade trade) {
        Set<String> customerExcept = getOldCustomExceptIds(staff, trade).stream().map(String::valueOf).collect(Collectors.toSet());
        return customerExcept;
    }

    /**
     * 获取旧的自定义异常
     * @param staff
     * @param trade
     * @return
     */
    public static Set<Long> getOldCustomExceptIds(Staff staff, Trade trade) {
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade != null && trade.getCompanyId() != null ? trade.getCompanyId() : -1L)) {
            return ExceptUtils.getCustomerOldExceptIds(trade.getExceptData());
        }
        return TradeExceptOldUtils.getOldCustomExceptIds(staff, trade);
    }

    /**
     * 清空自定义异常
     * @param staff
     * @param trade
     */
    public static void clearCustomExcept(Staff staff, Trade trade) {
        Set<Long> customExceptIds = getCustomExceptIds(staff, trade);
        // 清空自定义异常
        trade.setExceptIds("");
        if (CollectionUtils.isNotEmpty(customExceptIds)) {
            for (Long exceptId : customExceptIds) {
                updateExcept(staff, trade, exceptId, 0L);
            }
        } else {
            // 清除新的
            ExceptData tradeExceptData = getTradeExceptData(trade);
            if (tradeExceptData != null) {
                Set<Long> exceptIds = tradeExceptData.getExceptIds();
                Iterator<Long> iterator = exceptIds.iterator();
                while (iterator.hasNext()) {
                    Long next = iterator.next();
                    ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(next);
                    if (exceptEnum == null || ExceptEnum.oldPart3ExceptEnumMap.get(next) != null) {
                        iterator.remove();
                    }
                }
                if(CollectionUtils.isEmpty(exceptIds)){
                    trade.setIsExcep(0);
                }
            }
        }

    }


    /**
     * 覆盖一个多个自定义异常值
     * @param staff
     * @param trade
     * @param exceptIds 异常id
     */
    public static void updateCustomExcept(Staff staff, Trade trade, Set<Long> exceptIds) {
        // 先清空自定义异常,再更新自定义异常
        clearCustomExcept(staff, trade);
        for (Long exceptId : exceptIds) {
            // 可能是三方仓异常,所以需要获取一下
            ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
            // 三方仓的异常属于系统异常，但是标记在exceptIds 上，需要特殊处理
            if (exceptEnum != null) {
                updateExcept(staff, trade, exceptEnum.getId(), 1L);
            } else {
                updateExcept(staff, trade, exceptId, 1L);
            }
        }
    }
    /**
     * 覆盖一个多个自定义异常值
     * @param staff
     * @param trade
     * @param exceptIds 异常id
     */
    public static void updateCustomExcept(Staff staff, Trade trade, String exceptIds) {
        // 先清空自定义异常,再更新自定义异常
        Set<Long> collect = Arrays.stream(exceptIds.split(",")).filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toSet());
        updateCustomExcept(staff,trade,collect);
    }


    public static void syncExceptIds(Staff staff,Trade update,Trade origin){
        updateCustomExcept(staff,update, getCustomExceptIds(staff,origin));
    }


    public static List<String> getSaveExceptErrorLogs(Staff staff, List<Trade> trades) {
        List<String> logs = new ArrayList<>();
        for (Trade trade : trades) {
            ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(trade);
            String param = JSONObject.toJSONString(tradeExceptData);
            logs.add(param);
        }
        return logs;
    }

    /**
     * 清除itemExcep上的异常，例如：作废订单时
     * @param staff
     * @param trade
     */
    public static void clearItemExcep(Staff staff,Trade trade){
        List<ExceptOldEnum> tradeExcepExceptEnums = ExceptOldEnum.tradeItemExcepExceptEnums;
        trade.setItemExcep(0);
        for(ExceptEnum exceptEnum:ExceptEnum.allExceptEnums){
            if(tradeExcepExceptEnums.contains(exceptEnum.getOldExceptEnum())){
                updateExcept(staff,trade,exceptEnum,0L);
            }
        }
    }
    /**
     * 清除excep上的异常，例如：作废订单时
     * @param staff
     * @param trade
     */
    public static void clearExcep(Staff staff,Trade trade){
        List<ExceptOldEnum> tradeExcepExceptEnums = ExceptOldEnum.tradeExcepExceptEnums;
        trade.setExcep(0L);
        for(ExceptEnum exceptEnum:ExceptEnum.allExceptEnums){
            if(tradeExcepExceptEnums.contains(exceptEnum.getOldExceptEnum())){
                updateExcept(staff,trade,exceptEnum,0L);
            }
        }
    }

    /**
     * 清除V上的异常
     * @param staff
     * @param trade
     */
    public static void clearV(Staff staff,Trade trade){
        List<ExceptOldEnum> tradeExcepExceptEnums = ExceptOldEnum.tradeVExceptEnums;
        trade.setV(0L);
        for(ExceptEnum exceptEnum:ExceptEnum.allExceptEnums){
            if(tradeExcepExceptEnums.contains(exceptEnum.getOldExceptEnum())){
                updateExcept(staff,trade,exceptEnum,0L);
            }
        }
    }


    /**
     * 获取手动标记的异常
     * @param staff
     * @param trade
     * @return
     */
    public static Set<Long> getManualExceptIds(Staff staff,Trade trade){
        ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(trade);
        return tradeExceptData.getManualExceptIds();
    }


/*********************************异常初始化********************************************/

    /**
     *  复制订单时需要重新初始化exceptData的sid和mergeSid,清空order的异常，新增的订单可能需要重新初始化ExceptData 时
     * @param staff
     * @param trade
     */
    public static void initExceptData(Staff staff, Trade trade){
        ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(trade);
        tradeExceptData.setSid(trade.getSid());
        tradeExceptData.setMergeSid(trade.getMergeSid());
        tradeExceptData.setOldMergeSid(trade.getMergeSid());
        tradeExceptData.setOldExceptIds(ConcurrentHashMap.newKeySet());
        tradeExceptData.setOldOrderExceptIds(new ConcurrentHashMap<>());
        tradeExceptData.setOrderExceptIds(new ConcurrentHashMap<>());

    }
    /**
     * 获取订单的异常数据
     */
    public static ExceptData getTradeExceptData(Trade trade) {
        if (trade == null) {
            throw new IllegalArgumentException("trade对象不能为null!");
        } else {
            ExceptData exceptData = trade.getExceptData();
            if (exceptData == null) {
                exceptData = ExceptData.init();
                // 第一次初始化时填充OldMergeSid
                exceptData.setOldMergeSid(trade.getMergeSid());
            }
            if (trade.getMergeSid() != null) {
                exceptData.setMergeSid(trade.getMergeSid());
            }
            exceptData.setSid(trade.getSid());
            trade.setExceptData(exceptData);
            return exceptData;
        }
    }


    /**
     *  合单时的 异常处理
     * @param main
     * @param mergeTrades
     */
    public static  <T extends Trade> void handleMergeExcept(Trade main, List<T> mergeTrades) {
        if (main == null) {
            return;
        }
        Map<Long, ExceptData> subTradeExceptDatas = new ConcurrentHashMap<>();
        for (Trade trade : mergeTrades) {
            // 此处不需要新建ExceptData 对象，移除异常主单也移除
            subTradeExceptDatas.put(trade.getSid(), trade.getExceptData());
            // 每个trade都包含合单的所有子单的exceptData
            trade.setSubTradeExceptDatas(subTradeExceptDatas);
        }
    }


    /**
     * order上的异常可能变动了，要根据order上的异常重新计算对应的trade上的异常
     * @param staff
     * @param trade
     * @param exceptEnums order上存在的异常
     */
    public static void resetTradeExcept(Staff staff, Trade trade, List<ExceptEnum> exceptEnums) {
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        resetTradeExcept(staff, trade, TradeUtils.getOrders4Trade(trade), exceptEnums);
    }
    /**
     * order上的异常可能变动了，要根据order上的异常重新计算对应的trade上的异常
     * @param staff
     * @param trade
     * @param exceptEnum order上存在的异常
     */
    public static void resetTradeExcept(Staff staff, Trade trade, ExceptEnum exceptEnum) {
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        resetTradeExcept(staff, trade, TradeUtils.getOrders4Trade(trade), exceptEnum);
    }

    /**
     * order上的异常可能变动了，要根据order上的异常重新计算对应的trade上的异常
     *
     * @param staff
     * @param trade
     * @param orders
     * @param exceptEnums order上存在的异常
     */
    public static void resetTradeExcept(Staff staff, Trade trade, List<Order> orders, List<ExceptEnum> exceptEnums) {
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        for (ExceptEnum exceptEnum : exceptEnums) {
            resetTradeExcept(staff, trade, orders, exceptEnum);
        }
    }
    /**
     * order上的异常可能变动了，要根据order上的异常重新计算对应的trade上的异常
     *
     * @param staff
     * @param trade
     * @param orders
     * @param exceptEnum order上存在的异常
     */
    public static void resetTradeExcept(Staff staff, Trade trade, List<Order> orders, ExceptEnum exceptEnum) {
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        boolean b = orders.stream().anyMatch(order -> OrderExceptUtils.isContainsExcept(staff, order, exceptEnum));
        TradeExceptUtils.updateExcept(staff, trade, exceptEnum, b);
    }


    /**
     *   清除trade指定的异常,与updateExcept 不同的是，在某些场景下，例如缺货发货:order的stock_status 是缺货状态，但是trade是库存正常的，这种情况是没有缺货异常的，需要直接清除
     *   强制删除指定异常
     * @param staff
     * @param trade
     * @param exceptEnum
     * @param clearSubTrade 子单的异常是否也要清除
     */
    public static void clearTradePointExcept(Staff staff, Trade trade, ExceptEnum exceptEnum,boolean clearSubTrade) {
        updateExcept(staff,trade,exceptEnum,0L);
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(trade);
        Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
        ExceptUtils.clearTradePointExcept(staff,tradeExceptData,exceptEnum.getId());
        if (MapUtils.isEmpty(subTradeExceptDatas)) {
            return;
        }
        if(clearSubTrade){
            Set<Map.Entry<Long, ExceptData>> entries = subTradeExceptDatas.entrySet();
            for(Map.Entry<Long, ExceptData> entry:entries){
                ExceptUtils.clearTradePointExcept(staff,entry.getValue(),exceptEnum.getId());
            }
        }


    }
    public static void clearTradePointExcept(Staff staff, Trade trade, List<ExceptEnum> exceptEnums,boolean clearSubTrade) {
        exceptEnums.forEach(exceptEnum->clearTradePointExcept(staff,trade,exceptEnum,clearSubTrade));
    }

    /**
     * order 被删除时对应的异常也要被删除
     * @param staff
     * @param trade
     * @param order
     */
    public static void delOrderExcept(Staff staff, Trade trade,Order order){
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        // 暂时兼容一下,批量换商品时的trade 传入的对象并不是和order对应的，优先取order自己的
        ExceptData exceptData = order.getExceptData() == null ? getTradeExceptData(trade) : order.getExceptData();
        if(order.getSubTradeExceptDatas()==null){
            order.setSubTradeExceptDatas(trade.getSubTradeExceptDatas());
        }
        OrderExceptUtils.delOrderExcept(staff, exceptData, order);
    }


    public static <T extends Trade> List<ExceptData> getTradeExceptDatas(Staff staff, List<T> trades) {
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return new ArrayList<>();
        }
        List<ExceptData> exceptDatas = new ArrayList<>();
        for (T trade : trades) {
            exceptDatas.add(getTradeExceptData(trade));
        }
        return exceptDatas;
    }


    /**
     * 合并order的异常到目标异常，目前只归并order的异常，例如取消拆单时需要用到
     * @param staff
     * @param targetExcept
     * @param exceptDatas
     */
    public static void joinOrderExcept(Staff staff, ExceptData targetExcept, List<ExceptData> exceptDatas) {
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        for (ExceptData exceptData : exceptDatas) {
            if (Objects.equals(targetExcept.getSid(), exceptData.getSid())) {
                // 与目标单相同，不需要
                continue;
            }
            Set<Long> tradeExceptIds = exceptData.getExceptIds();
            if (CollectionUtils.isNotEmpty(tradeExceptIds)) {
                for (Long exceptId : tradeExceptIds) {
                    ExceptUtils.updateExcept(targetExcept, exceptId, 1L);
                }
            }
            Map<Long, Set<Long>> orderExceptIds = exceptData.getOrderExceptIds();
            if (MapUtils.isEmpty(orderExceptIds)) {
                continue;
            }
            // 归并order的异常
            Set<Map.Entry<Long, Set<Long>>> entries = orderExceptIds.entrySet();
            for (Map.Entry<Long, Set<Long>> entry : entries) {
                Set<Long> exceptIds = entry.getValue();
                Long orderId = entry.getKey();
                for (Long exceptId : exceptIds) {
                    ExceptUtils.updateExceptOrder(targetExcept, exceptId, 1L, orderId);
                }
            }

        }
    }

    /**
     * 取消拆单时异常处理
     * @param staff
     * @param originTrades
     * @param mainTrade
     * @param <T>
     */
    public static <T extends Trade>  void splitUndoExcept(Staff staff, List<TbTrade> originTrades, Trade mainTrade){
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        // 原单的异常
        List<ExceptData> exceptDatas = TradeExceptUtils.getTradeExceptDatas(staff, originTrades);
        // 取消拆单时对应的order的sid会变，order的异常需要并入主单
        ExceptData exceptData = TradeExceptUtils.getTradeExceptData(mainTrade);
        TradeExceptUtils.joinOrderExcept(staff,exceptData,exceptDatas);
    }

    /**
     * 重制拆单的异常对象,操作except对象必须开启了填充的白名单
     * @param staff
     * @param splitTrade
     * @param orders
     */
    public static void resetSplitTradeExceptData(Staff staff, Trade splitTrade, List<Order> orders) {
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        ExceptData exceptData = TradeExceptUtils.getTradeExceptData(splitTrade);
        List<Order> orderList = OrderUtils.toFullOrderList(orders, false);
        orderList.forEach(order -> {
            order.setExceptData(exceptData);
            // order.setSubTradeExceptDatas(splitTrade.getSubTradeExceptDatas());
        });
        for (Order order : orderList) {
            Set<Long> orderExceptIds = order.getOrderExceptIds();
            for (Long exceptId : orderExceptIds) {
                OrderExceptUtils.updateExceptOrder(staff, exceptData, order, exceptId, 1L);
            }
        }
    }

    public static void resetSplitTradeExceptData(Staff staff, Trade trade) {
        resetSplitTradeExceptData(staff,trade,TradeUtils.getOrders4Trade(trade));
    }


    public static <T extends Order> void fillOrderExceptData(Staff staff, List<Trade> trades, List<T> orders) {
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        if (CollectionUtils.isEmpty(orders) || CollectionUtils.isEmpty(trades)) {
            return;
        }
        Map<Long, Trade> collect = trades.stream().collect(Collectors.toMap(Trade::getSid, Function.identity(), (k1, k2) -> k1));
        for (Order order : orders) {
            Trade trade = collect.get(order.getSid());
            if (trade != null) {
                ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(trade);
                OrderOpeartEnum orderOpeart = order.getOrderOpeart();
                if (Objects.equals(order.getEnableStatus(), 0) || OrderOpeartEnum.DELETE == orderOpeart) {
                    // 删除的order的异常一起删除
                    OrderExceptUtils.delOrderExcept(staff, tradeExceptData, order);
                } else {
                    order.setExceptData(tradeExceptData);
                    order.setSubTradeExceptDatas(trade.getSubTradeExceptDatas());
                }
            }
        }
    }

    /**
     * 合单子单包含异常,订单查询时没有查询出子单，通过子单的exceptData判断
     * @param staff
     * @param trade
     * @param exceptEnum
     * @return
     */
    public static boolean isSubTradeContainExcept(Staff staff, Trade trade, ExceptEnum exceptEnum) {
        if (!TradeUtils.isMerge(trade)) {
            return false;
        }
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return false;
        }
        // 未关闭白名单的异常或者起了新读
        if (!TradeExceptWhiteUtils.openCloseViewExceptIds(staff, exceptEnum.getId())
                || TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade.getCompanyId())) {
            Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
            if (MapUtils.isEmpty(subTradeExceptDatas)) {
                return false;
            }

            Collection<ExceptData> values = subTradeExceptDatas.values();
            for (ExceptData exceptData : values) {
                if (exceptData == null) {
                    continue;
                }
                if (ExceptUtils.isContainsExcept(exceptData, exceptEnum.getId())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     *  order 存在异常，如果拆单，可能要重新计算一下trade的异常，因为拆出去的order可能不包含这个异常
     * @param staff
     * @param trade
     * @param exceptEnum
     */
    public static void resetTradeExceptByOrder(Staff staff, Trade trade, ExceptEnum exceptEnum) {
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        boolean b = orders4Trade.stream().filter(order -> !Objects.equals(order.getEnableStatus(), 0)).anyMatch(order -> OrderExceptUtils.isContainsExcept(staff, order, exceptEnum));
        TradeExceptUtils.updateExcept(staff, trade, exceptEnum, b);
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            trade.setIsExcep(getIsExcep(staff, trade));
        }
    }

    public static void resetTradeExceptByOrder(Staff staff, Trade trade, ExceptEnum ... exceptEnums) {
        for (ExceptEnum exceptEnum : exceptEnums) {
            resetTradeExceptByOrder(staff, trade, exceptEnum);
        }
    }

    /**
     * 旧的异常适配
     * @param staff
     * @param trades
     * @param <T>
     */
    public static <T extends Trade> void resetExceptDataOld(Staff staff, List<T> trades) {
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff)) {
            return;
        }
        trades.forEach(trade->getTradeExceptData(trade).initOld());
    }

    /**
     * 复制trade上的异常，这里不包含order的异常
     * @param staff
     * @param target
     * @param origin
     * @param <T>
     */
    public static <T extends  Trade> void copyTradeExcept(Staff staff,Trade target,Trade origin){
        ExceptData tradeExceptData = getTradeExceptData(origin);
        Set<Long> exceptIds = tradeExceptData.getExceptIds();
        for(Long exceptId:exceptIds){
             updateExcept(staff,target,exceptId,1L);
        }
    }


    /**
     * 获取指定异常反审核配置的异常ID
     * @param staff
     * @param tradeConfig
     * @return
     */
    private static Set<Long> getUnAuditPointExceptIds(Staff staff,TradeConfig tradeConfig){
        String tradeExtendConfig = tradeConfig.getTradeExtendConfig();
        if (StringUtils.isEmpty(tradeExtendConfig)) {
            return new HashSet<>();
        }
        //获取配置中设置的订单状态
        Object tradeStatus = tradeConfig.get(NewTradeExtendConfigEnum.AUTO_RE_AUDIT_STATUS.getKey());
        Integer status = (Integer) Optional.ofNullable(tradeStatus).orElse(0);
        if(Objects.equals(status,0)){
            return new HashSet<>(0);
        }
        String tradeExceptions = (String) tradeConfig.get(NewTradeExtendConfigEnum.AUTO_RE_AUDIT_EXCEPTION.getKey());
        if(StringUtils.isBlank(tradeExceptions)){
            return new HashSet<>(0);
        }
        List<String> exceptions = Arrays.stream(tradeExceptions.split(",")).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(exceptions)){
            return new HashSet<>(0);
        }
        Set<Long> exceptIds=new HashSet<>();
        for(String exception:exceptions){
            List<ExceptEnum> exceptEnums = ExceptEnum.getEnumByOldEnglish(Lists.newArrayList(exception));
            if(CollectionUtils.isNotEmpty(exceptEnums)){
                exceptIds.add(exceptEnums.get(0).getId());
            }else{
                // 说明是自定义异常
                exceptIds.add(Long.valueOf(exception));
            }
        }
        return exceptIds;
    }

    /**
     * 指定异常反审核
     */
    public static boolean analysisTradeExceptIdsUnAudit(Staff staff, Trade trade, TradeConfig tradeConfig) {
        // 获取配置的异常
        Set<Long> unAuditPointExceptIds = getUnAuditPointExceptIds(staff, tradeConfig);
        if (CollectionUtils.isEmpty(unAuditPointExceptIds)) {
            return false;
        }
        // 存在任意包含的异常
        boolean containsExcept = unAuditPointExceptIds.stream().anyMatch(exceptId -> TradeExceptUtils.isContainExcept(staff, trade, exceptId));
        if (!containsExcept) {
            return false;
        }
        String tempStatus = TradeStatusUtils.convertSysStatus(trade, tradeConfig);
        TradeDeliveringStatusEnum tradeDeliveringStatusEnum = TradeDeliveringStatusEnum.tradeDeliveringStatusEnumMap.get(tempStatus);
        if (tradeDeliveringStatusEnum == null) {
            return false;
        }
        Object tradeStatus = tradeConfig.get(NewTradeExtendConfigEnum.AUTO_RE_AUDIT_STATUS.getKey());
        Integer status = (Integer) Optional.ofNullable(tradeStatus).orElse(0);
        if ((tradeDeliveringStatusEnum.getValue() & status) == 0) {
            return false;
        }
        return true;
    }

    /**
     * 获取异常的中文翻译
     * @param staff
     * @param trade
     * @param tagNameMap 可以通过  com.raycloud.dmj.services.tag.TradeTagService#getTradeTagMap(com.raycloud.dmj.domain.account.Staff) 获取
     * @return
     */
    public static List<String> getExceptChineseNames(Staff staff, Trade trade, Map<Long, String> tagNameMap) {
        if (TradeExceptWhiteUtils.openBizNewReadExceptConfig(staff, trade != null && trade.getCompanyId() != null ? trade.getCompanyId() : -1L)) {
            Set<Long> exceptIds = ExceptUtils.getExceptIds(getTradeExceptData(trade));
            List<String> exceptChineseNames = new ArrayList<>();
            for (Long exceptId : exceptIds) {
                ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
                if (exceptEnum != null) {
                    exceptChineseNames.add(exceptEnum.getChinese());
                } else {
                    if (tagNameMap != null) {
                        String exceptName = tagNameMap.get(exceptId);
                        if (StringUtils.isNotBlank(exceptName)) {
                            exceptChineseNames.add(exceptName);
                        }
                    }
                }
            }
            return exceptChineseNames;
        }
        return TradeExceptOldUtils.getExceptChineseNames(staff, trade, tagNameMap);
    }


    public static void clearCustomExcept(Staff staff, Trade trade, boolean isClearSubTrade) {
        clearCustomExcept(staff, trade);
        if (!TradeExceptWhiteUtils.openExceptFillCompanyIds(staff) || !isClearSubTrade) {
            return;
        }
        // 是否要清除子单的异常
        Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
        if (subTradeExceptDatas != null) {
            Collection<ExceptData> exceptDatas = subTradeExceptDatas.values();
            for (ExceptData exceptData : exceptDatas) {
                Set<Long> customExceptIds = ExceptUtils.getCustomExceptIds(exceptData);
                customExceptIds.forEach(exceptId -> ExceptUtils.updateExcept(exceptData, exceptId, 0L));

            }
        }
    }

    public static void syncTradeExcepts(Staff staff, Trade update, Trade origin, ExceptEnum... exceptEnums) {
        for (ExceptEnum exceptEnum : exceptEnums) {
            syncTradeExcept(staff, update, origin, exceptEnum);
        }
    }

    /***
     * 获取异常的中文名
     */
    public static String getExceptChineseName(Staff staff,Long exceptId,Map<Long, String> exceptNameMap){
        String exceptName = "";
        ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
        if (exceptEnum != null) {
            exceptName = exceptEnum.getChinese();
        } else {
            exceptName = exceptNameMap.get(exceptId);
        }
        return exceptName;
    }
    /***
     * trade 如果是合单，trade的order可能包含子单的order，order直接塞主单tradeExceptData 不对
     */
    public static void fillOrderExceptData(Trade trade,Order order,boolean fillExcept){
        if(!fillExcept){
            return;
        }
        ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(trade);
        Map<Long, ExceptData> subTradeExceptDatas = trade.getSubTradeExceptDatas();
        fillOrderExceptData(trade.getSid(), tradeExceptData, subTradeExceptDatas, order);

    }

    private static void fillOrderExceptData(Long sid, ExceptData tradeExceptData, Map<Long, ExceptData> subTradeExceptDatas, Order order) {
        if (Objects.equals(order.getSid(), sid)) {
            order.setExceptData(tradeExceptData);
        } else {
            if (MapUtils.isNotEmpty(subTradeExceptDatas) && order.getSid() != null) {
                ExceptData exceptData = subTradeExceptDatas.get(order.getSid());
                order.setExceptData(exceptData == null ? tradeExceptData : exceptData);
            } else {
                order.setExceptData(tradeExceptData);
            }
        }
        order.setSubTradeExceptDatas(subTradeExceptDatas);
        List<Order> suits = order.getSuits();
        if (CollectionUtils.isEmpty(suits)) {
            return;
        }
        for (Order suit : suits) {
            fillOrderExceptData(sid, tradeExceptData, subTradeExceptDatas, suit);
        }
    }


    public static int parseIsExcep(Staff staff, Trade trade) {
        int isExcep = 0;
        ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(trade);
        if (isContainExcept(staff, trade, ExceptEnum.REFUNDING)) {
            // 包含退款异常时，不管是不是发货后isExcep都要设置为1
            isExcep = 1;
        } else if (StringUtils.isNotBlank(trade.getSysStatus()) && TradeUtils.isAfterSendGoods(trade)) {
            isExcep = 0;
        } else {
            isExcep = ExceptUtils.calculateIsExcept(tradeExceptData);
        }
        return isExcep;
    }







}

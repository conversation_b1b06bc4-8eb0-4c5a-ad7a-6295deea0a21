package com.raycloud.dmj.domain.trade.except;

import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.RefundUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.constant.ExceptConstantOld;
import com.raycloud.dmj.except.domain.ExceptData;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.except.enums.ExceptOldEnum;
import com.raycloud.dmj.except.utils.ExceptUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.TradeConstants.*;

/**
 * @ClassName TradeExceptOldUtils
 * @Description 老的异常处理
 * <AUTHOR>
 * @Date 2023/2/16 14:39
 * @Version 1.0 其他模块暂时不要使用，用旧的方法，等稳定后在沟通
 */
@Deprecated
public abstract class TradeExceptOldUtils {
    /**
     * 同一个异常对应多个异常code
     */
    public static final Map<Long,ExceptOldEnum> exceptId2ExceptOldEnumMap  = Maps.newHashMapWithExpectedSize(8);;

    static {
        exceptId2ExceptOldEnumMap.put(ExceptEnum.RELATION_CHANGED.getId(),ExceptOldEnum.OLD_ITEM_RELATION_MODIFIED);
        exceptId2ExceptOldEnumMap.put(ExceptEnum.UNALLOCATED.getId(),ExceptOldEnum.OLD_ITEM_UNALLOCATED);
        exceptId2ExceptOldEnumMap.put(ExceptEnum.INSUFFICIENT.getId(),ExceptOldEnum.OLD_STOCK_INSUFFICIENT);
        exceptId2ExceptOldEnumMap.put(ExceptEnum.PLATFORM_WAREHOUSE_MATCH.getId(),ExceptOldEnum.OLD_PLATFORM_WAREHOUSE_MAPPING);
        exceptId2ExceptOldEnumMap.put(ExceptEnum.HALT.getId(),ExceptOldEnum.OLD_EXCEP_HALT);
        exceptId2ExceptOldEnumMap.put(ExceptEnum.REFUNDING.getId(),ExceptOldEnum.OLD_EXCEP_REFUND);
    }

    /**
     * 将老的异常数据转换成新的方式
     * 后期从数据库获取
     *
     * @param trade 订单
     */
    public static void syncSysExceptOld2New(Staff staff, Trade trade) {
        ExceptData exceptData = TradeExceptUtils.getTradeExceptData(trade);
        //挂起异常
        ExceptUtils.updateExcept(exceptData, ExceptEnum.HALT.getId(), trade.getIsHalt() == null ? 0L : trade.getIsHalt().longValue());
        //退款异常
        ExceptUtils.updateExcept(exceptData, ExceptEnum.REFUNDING.getId(), trade.getIsRefund() == null ? 0L : trade.getIsRefund());
        //平台更换地址异常
        ExceptUtils.updateExcept(exceptData, ExceptEnum.ADDRESS_CHANGED.getId(), trade.getAddressChanged() == null ? 0L : trade.getAddressChanged().longValue());
        //卖家 旗帜，备注变更
        ExceptUtils.updateExcept(exceptData, ExceptEnum.SELLER_MEMO_UPDATE.getId(), trade.getSellerMemoUpdate() == null ? 0L : trade.getSellerMemoUpdate().longValue());
        //黑名单
        ExceptUtils.updateExcept(exceptData, ExceptEnum.BLACK_NICK.getId(), trade.getBlackBuyerNick() == null ? 0L : trade.getBlackBuyerNick().longValue());
        //快递不可达
        ExceptUtils.updateExcept(exceptData, ExceptEnum.UNATTAINABLE.getId(), trade.getUnattainable() == null ? 0L : trade.getUnattainable().longValue());
        //信息缺失
        ExceptUtils.updateExcept(exceptData, ExceptEnum.LOST_MSG.getId(), trade.getIsLostMsg() == null ? 0L : trade.getIsLostMsg().longValue());

        //处理 trade.item_excep、trade.excep、trade.v的数据
        for (ExceptEnum exceptEnum : ExceptEnum.allExceptEnums) {
            ExceptOldEnum oldExceptEnum = exceptEnum.getOldExceptEnum();
            if (oldExceptEnum == null) {
                continue;
            }
            long exceptVal = 0;
            //trade.item_excep
            if (ExceptOldEnum.tradeItemExcepExceptEnums.contains(oldExceptEnum)) {
                if (trade.getItemExcep() != null && (trade.getItemExcep() & oldExceptEnum.getItemExcep()) > 0) {
                    exceptVal = 1;
                }
            }
            //trade.excep
            else if (ExceptOldEnum.tradeExcepExceptEnums.contains(oldExceptEnum)) {//trade.excep
                if (getTradeExcep(trade) != null && (getTradeExcep(trade) & oldExceptEnum.getTradeExcep()) > 0) {
                    exceptVal = 1;
                }
            }
            //trade.v
            else if (ExceptOldEnum.tradeVExceptEnums.contains(oldExceptEnum)) {
                if (getTradeV(trade) != null && (getTradeV(trade) & oldExceptEnum.getTradeV()) > 0) {
                    exceptVal = 1;
                }
            }
            // 其他的就是自定义异常
            else {
                continue;
            }
            ExceptUtils.updateExcept(exceptData, exceptEnum.getId(), exceptVal);
        }
        syncOrderExceptOld2New(staff, trade,exceptData);
        //处理自定义异常
        Set<Long> exceptIds = ArrayUtils.toLongSet(getTradeExceptIds(trade));
        if (CollectionUtils.isNotEmpty(exceptIds)) {
            exceptIds.forEach(exceptId -> ExceptUtils.updateExcept(exceptData, exceptId, 1L));
        }
    }

    /**
     * 同步oder异常
     *
     * @param trade
     */
    public static void syncOrderExceptOld2New(Staff staff, Trade trade, ExceptData exceptData) {
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders4Trade) {
            // 平台换商品
            ExceptUtils.updateExceptOrder(exceptData, ExceptEnum.ITEM_CHANGED.getId(), order.getItemChanged() == null ? 0L : order.getItemChanged().longValue(), order.getId());
            // 商品对应关系改动
            ExceptUtils.updateExceptOrder(exceptData, ExceptEnum.RELATION_CHANGED.getId(), order.getRelationChanged() == null ? 0L : order.getRelationChanged().longValue(), order.getId());
            // 商品未匹配
            boolean isUnallocated = (order.getItemSysId() != null && order.getItemSysId() <= 0) || Trade.STOCK_STATUS_UNALLOCATED.equals(order.getStockStatus());
            ExceptUtils.updateExceptOrder(exceptData, ExceptEnum.UNALLOCATED.getId(), !isUnallocated ? 0L : 1L, order.getId());
            // 缺货异常
            boolean insufficient = Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus()) || Trade.STOCK_STATUS_EXCEP.equals(order.getStockStatus()) ||
                    (Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus()) && (order.getInsufficientCanceled() == null || order.getInsufficientCanceled() == 0));
            ExceptUtils.updateExceptOrder(exceptData, ExceptEnum.INSUFFICIENT.getId(), !insufficient ? 0L : 1L, order.getId());
            //套件修改异常
            ExceptUtils.updateExceptOrder(exceptData, ExceptEnum.SUITE_CHANGE.getId(), !NumberUtils.has(order.getV(), 1 << 3) ? 0L : 1L, order.getId());
            //商品停用
            ExceptUtils.updateExceptOrder(exceptData, ExceptEnum.ITEM_SHUTOFF.getId(), !(order.getV() != null && (order.getV() & Order.ITEM_SHUTOFF) == Order.ITEM_SHUTOFF) ? 0L : 1L, order.getId());
            //唯一码下架
            boolean b = order.getV() != null && (order.getV() & Order.ITEM_UNIQUE_CODE_OFFSHELF) == Order.ITEM_UNIQUE_CODE_OFFSHELF;
            ExceptUtils.updateExceptOrder(exceptData, ExceptEnum.UNIQUE_CODE_OFFSHELF.getId(), !b ? 0L : 1L, order.getId());
            //普通商品转加工商品异常
            boolean itemProcess = order.getV() != null && (order.getV() & Order.ITEM_PROCESS) == Order.ITEM_PROCESS;
            ExceptUtils.updateExceptOrder(exceptData, ExceptEnum.ITEM_PROCESS.getId(), !itemProcess ? 0L : 1L, order.getId());
            // 退款异常
            ExceptUtils.updateExceptOrder(exceptData, ExceptEnum.REFUNDING.getId(), !RefundUtils.isRefundOrder(order) ? 0L : 1L, order.getId());
        }

    }

    /**
     * 获取itemExcep值
     */
    public static int getItemExcep(Trade trade) {
        int itemExcep = 0;
        Set<Long> exceptIds = TradeExceptUtils.getTradeExceptData(trade).getExceptIds();
        if (CollectionUtils.isNotEmpty(exceptIds)) {
            for (ExceptEnum exceptEnum : ExceptEnum.allExceptEnums) {
                ExceptOldEnum oldExceptEnum;
                if (exceptIds.contains(exceptEnum.getId())
                        && (oldExceptEnum = exceptEnum.getOldExceptEnum()) != null
                        && ExceptOldEnum.tradeItemExcepExceptEnums.contains(oldExceptEnum)) {
                    itemExcep = itemExcep | (int) oldExceptEnum.getItemExcep();
                }

            }
        }
        return itemExcep;
    }

    /**
     * 同步订单系统异常值到老的字段,不处理stockStatus 字段 stockStatus 包含商品未匹配，缺货，对应关系改动，作为第三方平台数据处理，这里只处理V,excep,itemExcep 等字段
     *
     * @param trade      订单
     * @param exceptEnum 异常枚举
     * @param exceptVal  异常值
     */
    public static void syncTradeExceptNew2Old(Staff staff,Trade trade, ExceptEnum exceptEnum, Long exceptVal) {
        List<Order> orders4Trade=null;
        List<Long> afterSendIngoreOrderExceptIds = new ArrayList<>(TradeExceptWhiteUtils.getAfterSendIngoreOrderExceptIds(staff));
        if (CollectionUtils.isEmpty(afterSendIngoreOrderExceptIds)) {
            afterSendIngoreOrderExceptIds.add(ExceptEnum.INSUFFICIENT.getId());
            afterSendIngoreOrderExceptIds.add(ExceptEnum.UNALLOCATED.getId());
            // 发货后order订单，默认参与商品未匹配和库存不足计算
        }
        switch (exceptEnum) {
            case LOST_ITEM://缺少商品异常
            case TRADE_LOST_SYS_STATUS://trade缺少系统状态异常
            case ORDER_LOST_SYS_STATUS://order缺少系统状态异常
            case LOST_MSG://订单缺少必要信息
                trade.setIsLostMsg(exceptVal == 0 ? 0 : 1);
                syncTradeItemExcepNew2Old(trade, exceptEnum, exceptVal);
                break;
            case HALT://订单挂起
                trade.setIsHalt(exceptVal == 0 ? 0 : 1);
                break;
            case REFUNDING://退款中
                trade.setIsRefund(exceptVal == 0 ? 0L : 1L);
                break;
            case UNATTAINABLE://快递异常
                trade.setUnattainable(exceptVal == 0 ? 0 : 1);
                break;
            case ADDRESS_CHANGED://平台更换地址
                trade.setAddressChanged(exceptVal == 0 ? 0 : 1);
                syncTradeItemExcepNew2Old(trade, exceptEnum, exceptVal);
                break;
            case BLACK_NICK://黑名单
                trade.setBlackBuyerNick(exceptVal == 0 ? 0 : 1);
                syncTradeItemExcepNew2Old(trade, exceptEnum, exceptVal);
                break;
            case SELLER_MEMO_UPDATE://平台修改备注
                trade.setSellerMemoUpdate(exceptVal.intValue());
                // 平台改备注 1 不是标记异常
                if (exceptVal == 2L) {
                    syncTradeItemExcepNew2Old(trade, exceptEnum, 1L);
                } else {
                    syncTradeItemExcepNew2Old(trade, exceptEnum, 0L);
                }
                break;
            case RISK://风控订单
                trade.setRiskExcep(exceptVal == 0 ? 0 : 1);
                syncTradeItemExcepNew2Old(trade, exceptEnum, exceptVal);
                break;
            case PDD_STOCK_OUT://pdd缺货已处理异常
                trade.setPddStockOut(exceptVal == 0 ? 0 : 1);
                syncTradeItemExcepNew2Old(trade, exceptEnum, exceptVal);
                break;
            //trade.stockStatus
            case RELATION_CHANGED://商品对应关系修改
            case UNALLOCATED://商品未匹配
            case INSUFFICIENT://库存不足
                orders4Trade = TradeUtils.getOrders4Trade(trade);
                if (CollectionUtils.isNotEmpty(orders4Trade)) {
                    boolean orderContainExcept = isOrderContainsExcept(staff, orders4Trade, exceptEnum, afterSendIngoreOrderExceptIds);
                    syncTradeItemExcepNew2Old(trade, exceptEnum, orderContainExcept ? 1L : 0L);
                } else {
                    syncTradeItemExcepNew2Old(trade, exceptEnum, exceptVal);
                }
                if (exceptVal == 1L && exceptEnum == ExceptEnum.UNALLOCATED) {
                    trade.setStockStatus(Trade.STOCK_STATUS_UNALLOCATED);
                }
                // 库存不足 的stockStatus 以业务外部为准
                break;
            //trade.item_excep
            case PART_REFUND://部分退款
            case COD_REPEAT://重复货到付款订单
            case WAITING_RETURN_WMS://等待退货入仓
            case DELIVER_EXCEPT://发货异常
            case WAIT_MERGE://等待合并
                syncTradeItemExcepNew2Old(trade, exceptEnum, exceptVal);
                break;
            // 含有order的异常
            case ITEM_PROCESS://普通商品转加工商品异常
            case ITEM_CHANGED://平台更换商品
            case ITEM_SHUTOFF://商品停用
            case UNIQUE_CODE_OFFSHELF://唯一码下架
            case SUITE_CHANGE://套件修改异常
                orders4Trade = TradeUtils.getOrders4Trade(trade);
                if (CollectionUtils.isNotEmpty(orders4Trade)) {
                    boolean orderContainExcept = isOrderContainsExcept(staff, orders4Trade, exceptEnum, afterSendIngoreOrderExceptIds);
                    syncTradeItemExcepNew2Old(trade, exceptEnum, orderContainExcept ? 1L : 0L);
                } else {
                    syncTradeItemExcepNew2Old(trade, exceptEnum, exceptVal);
                }
                break;
            //trade.excep异常处理
            case PLATFORM_FX_SPLIT_EXCEPT://平台分销待拆分
            case FX_AMBIGUITY://不明确供应商
            case FX_WAITPAY://分销商未付款
            case FX_UNAUDIT://分销商反审核
            case FX_REPULSE://供销商打回
            case TMCS_STOCK_OUT://天猫超市缺货回告
            case TMGJZY_STOCK_OUT://天猫国际直营缺货回告异常
            case POISON_NOT_MATCH_EXPRESS_TEMPLATE://得物直发物流模板匹配
            case PLATFORM_WAREHOUSE_MATCH://平台仓未匹配
            case VIP_COOPERATION_CODE_NOT_MATCH://未匹配常态合作编码
            case OUTSID_RECOVERY_FAIL://单号回收失败
            case PART_PAY_EXCEPT:// 部分付款异常
            case SMTQTG_UN_CONFIRM:
            case ONLINE_LOCK:// 线上锁定
            case CAI_GOU_TRADE_EXCEPT:
            case PLAT_MODIFY_ITEM_NUM_EXCEPT: // 平台修改数量异常
            case PO_JIA_EXCEPT:
            case ONLINE_STATUS_EXCEPT:
            case SMALL_REFUND_EXCEPT:
                syncTradeExcepNew2Old(trade, exceptEnum, exceptVal);
                break;
           // 保存在exceptIds上的系统异常
            case PARTY3_WAREHOUSE://三方仓操作失败
            case PARTY3_UNCREATE://三方仓取消发货单失败
            case PARTY3_RECREATE://三方仓重建发货单失败
            case PARTY3_CREATE://三方仓创建发货单失败
            case PARTY3_UNATTAINABLE:
            case UNIQUE_CODE_CLOSE://唯一码扫描关闭
            case QIMEN_TRADE_INTERCEPT_SUCCESS://奇门订单拦截成功
            case QIMEN_TRADE_INTERCEPT_FAILED://奇门订单拦截失败
            case SHIP_BOX_SPLIT_ORDER:
            case FX_STOP_DELIVER_EXCEPT:
                syncTradeExceptIdsNew2Old(staff,trade, exceptEnum.getOldExceptEnum().getOldIdx(), exceptVal);
                break;
            case USER_UNACTIVE://店铺停用
                break;

            case UPLOAD_EXCEPT://上传发货异常
                syncTradeVNew2Old(trade, exceptEnum, exceptVal);
                break;

            case FINANCE_REJECT_EXCEPT://财审拒绝
                break;
            case REFUND_ITEM_NUM_EXCEPT: // 退款商品数量异常
                case GX_ITEM_CHANGE_EXCEPT:
            case EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT:
                //退款商品数量异常 ,trade 异常是依据order来算的，同步时以order为准
                orders4Trade = TradeUtils.getOrders4Trade(trade);
                if (CollectionUtils.isNotEmpty(orders4Trade)) {
                    boolean orderContainExcept = isOrderContainsExcept(staff, orders4Trade, exceptEnum, afterSendIngoreOrderExceptIds);
                    syncTradeExcepNew2Old(trade, exceptEnum, orderContainExcept ? 1L : 0L);
                } else {
                    syncTradeExcepNew2Old(trade, exceptEnum, exceptVal);
                }
                break;
            case CUSTOM:
                //自定义异常
                break;
            default:
               // throw new RuntimeException(String.format("新的异常还不支持[%s]异常，请用旧的异常方法!", exceptEnum.getChinese()));
                Logs.debug(String.format(String.format("1.新的异常还不支持[%s]异常，请用旧的异常方法!", exceptEnum.getChinese())));

        }
    }

    /**
     * 同步订单自定义异常值到老的字段
     *
     * @param trade     订单
     * @param exceptId  自定义异常id
     * @param exceptVal 异常值 0：删除 1：新增
     */
    public static void syncTradeExceptIdsNew2Old(Staff staff,Trade trade, Long exceptId, long exceptVal) {
        Set<Long> exceptIds = ArrayUtils.toLongSet(getTradeExceptIds(trade));
        if (TradeExceptWhiteUtils.openNewMethodExceptLogConfig(staff, trade.getCompanyId())) {
            Logs.debug(String.format("同步自定义异常 %s oldExceptIds=%s exceptId =%s exceptVal=%s", trade.getSid(), exceptIds, exceptId, exceptVal));
        }
        if (exceptVal == 0) {//删除
            exceptIds.remove(exceptId);
        } else {//新增
            exceptIds.add(exceptId);
        }
        StringBuilder exceptIdStr = new StringBuilder();
        if (CollectionUtils.isNotEmpty(exceptIds)) {
            for (Long id : exceptIds) {
                if (exceptIdStr.length() > 0) {
                    exceptIdStr.append(",");
                }
                exceptIdStr.append(id);
            }
        }
        setTradeExceptIds(trade, exceptIdStr.toString());
    }

    /**
     * 同步子订单系统异常值到老的字段,同步到order
     *
     * @param trade      订单
     * @param order      子订单
     * @param exceptEnum 异常枚举
     * @param exceptVal  异常值
     */
    @Deprecated
    public static void syncOrderExceptNew2Old(Staff staff, Trade trade, Order order, ExceptEnum exceptEnum, Long exceptVal) {

        //先处理order的异常
        OrderExceptOldUtils.syncOrderExceptNew2Old(staff,order, exceptEnum,  exceptVal);
        //删除当前order的异常，订单依然还有这个异常（说明其他order有这个异常），此时不用处理这个trade的异常信息
        if (exceptVal == 0 && isContainExcept(staff,trade, exceptEnum)) {
            return;
        }
        // 同步完order，再处理trade
        syncTradeExceptNew2Old(staff,trade, exceptEnum, exceptVal);
    }

    private static void syncTradeItemExcepNew2Old(Trade trade, ExceptEnum exceptEnum, Long exceptVal) {
        Integer oldItemExcep = getTradeItemExcep(trade);
        if (oldItemExcep == null) {
            Trade origin = trade.getOrigin();
            if (origin != null) {
                oldItemExcep = getTradeItemExcep(origin);
            }
        }
        if (exceptEnum.getOldExceptEnum() == null) {
            return;
        }
        if (oldItemExcep == null) {
            oldItemExcep = 0;
        }
        int itemExcep = (int) exceptEnum.getOldExceptEnum().getItemExcep();
        if (trade != null && TradeExceptWhiteUtils.openUpdateOldExceptLogCompanyIds(Optional.ofNullable(trade.getCompanyId()).orElse(-1L))) {
            Logs.debug(String.format("sid=%s 更新旧的异常数据 exceptVal=%s  exceptEnum=%s itemExcep=%s oldItemExcep=%s ", trade.getSid(), exceptVal, exceptEnum.getChinese(), itemExcep, oldItemExcep));
        }
        if (exceptVal == 0) {//删除这个异常
            if ((oldItemExcep & itemExcep) > 0) {
                setTradeItemExcep(trade, oldItemExcep - itemExcep);
            }
        } else {//增加这个异常
            setTradeItemExcep(trade, oldItemExcep | itemExcep);
        }
    }

    private static void syncTradeExcepNew2Old(Trade trade, ExceptEnum exceptEnum, Long exceptVal) {
        Long oldTradeExcep = getTradeExcep(trade);
        if (oldTradeExcep == null) {
            Trade origin = trade.getOrigin();
            if (origin != null) {
                oldTradeExcep = getTradeExcep(origin);
            }
        }
        if (oldTradeExcep == null) {
            oldTradeExcep = 0L;
        }
        if(exceptEnum.getOldExceptEnum()==null){
            return;
        }
        long tradeExcep = exceptEnum.getOldExceptEnum().getTradeExcep();
        if (trade != null && TradeExceptWhiteUtils.openUpdateOldExceptLogCompanyIds(Optional.ofNullable(trade.getCompanyId()).orElse(-1L))) {
            Logs.debug(String.format("sid=%s 更新旧的异常数据 exceptVal=%s  exceptEnum=%s tradeExcep=%s oldTradeExcep=%s ", trade.getSid(), exceptVal, exceptEnum.getChinese(), tradeExcep, oldTradeExcep));
        }
        if (exceptVal == 0) {//删除这个异常
            if ((oldTradeExcep & tradeExcep) > 0) {
                setTradeExcep(trade, oldTradeExcep - tradeExcep);
            }
        } else {//增加这个异常
            setTradeExcep(trade, oldTradeExcep | tradeExcep);
        }
    }

    private static void syncTradeVNew2Old(Trade trade, ExceptEnum exceptEnum, Long exceptVal) {
        Long oldTradeV = getTradeV(trade);
        if (oldTradeV == null) {
            Trade origin = trade.getOrigin();
            if (origin != null) {
                oldTradeV = getTradeV(origin);
            }
        }
        if (oldTradeV == null) {
            oldTradeV = 0L;
        }
        if (exceptEnum.getOldExceptEnum() == null) {
            return;
        }
        long tradeV = exceptEnum.getOldExceptEnum().getTradeV();
        if (trade != null && TradeExceptWhiteUtils.openUpdateOldExceptLogCompanyIds(Optional.ofNullable(trade.getCompanyId()).orElse(-1L))) {
            Logs.debug(String.format("sid=%s 更新旧的异常数据 exceptVal=%s  exceptEnum=%s tradeV=%s oldTradeV=%s ", trade.getSid(), exceptVal, exceptEnum.getChinese(), tradeV, oldTradeV));
        }
        if (exceptVal == 0) {//删除这个异常
            if ((oldTradeV & tradeV) > 0) {
                setTradeV(trade, oldTradeV - tradeV);
            }
        } else {//增加这个异常
            setTradeV(trade, oldTradeV | tradeV);
        }
    }

    private static void syncOrderVNew2Old(Order order, ExceptEnum exceptEnum, Long exceptVal) {
        Long oldV = getOrderV(order);
        if (oldV == null) {
            Order origin = order.getOrigin();
            if (origin != null) {
                oldV = getOrderV(origin);
            }
        }
        if (oldV == null) {
            oldV = 0L;
        }
        if(exceptEnum.getOldExceptEnum()==null){
            return;
        }
        int orderV = (int) exceptEnum.getOldExceptEnum().getOrderV();

        if (exceptVal == 0) {//删除这个异常
            if ((oldV & orderV) == orderV) {
                setOrderV(order, oldV - orderV);
            }
        } else {//增加这个异常
            setOrderV(order, oldV | orderV);
        }
    }

    /**
     * 获取订单的异常原先保存到数据库的值
     *
     * @param trade      订单
     * @param exceptEnum 异常枚举
     * @return 0 代表没有这个异常枚举 其他代表返回了这个异常枚举对应的数据库存在的值
     */
    public static Long getTradeExceptDbValue(Trade trade, ExceptEnum exceptEnum) {
        //存在这个异常
        if (ExceptUtils.isContainsExcept(TradeExceptUtils.getTradeExceptData(trade), exceptEnum.getId())) {
            return exceptEnum.getOldExceptEnum().getDbValue();
        }
        //留言备注特殊性，0、代表没有变更，不属于异常，1代表已处理，不属于异常
        return ExceptEnum.SELLER_MEMO_UPDATE == exceptEnum ? trade.getSellerMemoUpdate() : 0L;
    }

    /**
     * 获取子订单的一个异常值
     *
     * @param trade      订单
     * @param order      子订单
     * @param exceptEnum 异常枚举
     * @return 1 代表有这个异常枚举  0 代码没有这个异常枚举
     */
    public static Long getOrderExceptDbValue(Trade trade, Order order, ExceptEnum exceptEnum) {
        Map<Long, Set<Long>> orderExceptIdsMap = TradeExceptUtils.getTradeExceptData(trade).getOrderExceptIds();
        return orderExceptIdsMap != null && orderExceptIdsMap.size() > 0 && ExceptUtils.isContainsExcept(orderExceptIdsMap.get(order.getId()), exceptEnum.getId()) ? exceptEnum.getOldExceptEnum().getDbValue() : 0L;
    }

    /**
     * 获取订单的异常原先保存到数据库的值
     *
     * @param trade      订单
     * @param exceptEnum 异常枚举
     * @return 0 代表没有这个异常枚举 其他代表返回了这个异常枚举对应的老的idx
     */
    public static Long getTradeExceptOldIdx(Trade trade, ExceptEnum exceptEnum) {
        //存在这个异常
        if (ExceptUtils.isContainsExcept(TradeExceptUtils.getTradeExceptData(trade), exceptEnum.getId())) {
            return exceptEnum.getOldExceptEnum().getOldIdx();
        }
        //留言备注特殊性，0、代表没有变更，不属于异常，1代表已处理，不属于异常
        return ExceptEnum.SELLER_MEMO_UPDATE == exceptEnum ? trade.getSellerMemoUpdate() : 0L;
    }

    /**
     * 同步自定义异常值到老的字段
     *
     * @param trade      订单
     * @param exceptEnum 异常枚举
     * @param exceptVal  异常值
     */
    public static void syncCustomExceptNew2Old(Trade trade, ExceptEnum exceptEnum, Long exceptVal) {

    }


    /**
     * 第一个元素为异常状态值,大于0表示异常订单，第二个元素为退款状态值，大于0表示退款订单
     */
    public static int getItemExcep(Trade trade, List<Trade> mergeList) {
        int itemExcep = 0;
        Set<Long> exceptIds = new HashSet<>(TradeExceptUtils.getTradeExceptData(trade).getExceptIds());
        if (CollectionUtils.isNotEmpty(mergeList)) {
            mergeList.forEach(mergeTrade -> exceptIds.addAll(TradeExceptUtils.getTradeExceptData(mergeTrade).getExceptIds()));
        }

        if (CollectionUtils.isNotEmpty(exceptIds)) {
            for (Long exceptId : exceptIds) {
                ExceptEnum exceptEnum = ExceptEnum.exceptEnumMap.get(exceptId);
                if (exceptEnum != null && exceptEnum.getOldExceptEnum().getItemExcep() != 0) {
                    itemExcep |= exceptEnum.getOldExceptEnum().getItemExcep();
                }
            }
        }
        return itemExcep;
    }


    /**
     * 设置订单的是否异常
     *
     * @param trade 订单数据
     * @return 1：异常单 0：非异常单
     */
    public static int setTradeExcep(Staff staff,Trade trade) {
        if (TradeStatusUtils.isWaitSellerSend(trade.getSysStatus())) {
            Set<Long> exceptIds = TradeExceptUtils.getTradeExceptData(trade).getExceptIds();
            //没有商品 || 信息缺失
            if (CollectionUtils.isEmpty(TradeUtils.getOrders4Trade(trade)) || TradeExceptUtils.isLostMsg(staff,trade)) {
                exceptIds.add(ExceptEnum.LOST_MSG.getId());
                //同步到老的字段
                syncTradeExceptNew2Old(staff,trade, ExceptEnum.LOST_MSG, ExceptEnum.LOST_MSG.getOldExceptEnum().getDbValue());
            }
            trade.setIsExcep(CollectionUtils.isNotEmpty(exceptIds) ? 1 : 0);
        } else {
            trade.setIsExcep(0);
        }
        return trade.getIsExcep();
    }

    /**
     * 计算trade的异常，将变化更新到toUpdate
     *
     * @param trade    原订单
     * @param toUpdate 更新的订单
     * @return 大于0，说明异常有变更
     */
    public static int setTradeExcep(Staff staff, Trade trade, Trade toUpdate) {
        int originIsExcep = trade.getIsExcep();

        int oldIsLostMsg = getTradeExceptDbValue(trade, ExceptEnum.LOST_MSG).intValue();
        setTradeExcep(staff,trade);
        int c = 0;
        if (originIsExcep - trade.getIsExcep() != 0) {
            toUpdate.setIsExcep(trade.getIsExcep());
            c++;
        }
        if (oldIsLostMsg - TradeExceptOldUtils.getTradeExceptDbValue(trade, ExceptEnum.LOST_MSG) != 0) {
            TradeExceptUtils.updateExcept(staff, toUpdate, ExceptEnum.LOST_MSG, getTradeExceptDbValue(trade, ExceptEnum.LOST_MSG));
            c++;
        }
        return c;
    }

    @Deprecated
    public static void setTradeIsExcep(Trade trade, Integer isExcep) {
        trade.setIsExcep(isExcep);
    }

    @Deprecated
    public static Integer getTradeIsExcep(Trade trade) {
        return trade.getIsExcep();
    }

    @Deprecated
    public static void setTradeExceptIds(Trade trade, String exceptIds) {
        trade.setExceptIds(exceptIds);
    }

    @Deprecated
    public static String getTradeExceptIds(Trade trade) {
        return trade.getExceptIds();
    }

    /**
     * 设置trade.itemExcep值
     */
    @Deprecated
    public static void setTradeItemExcep(Trade trade, Integer itemExcep) {
        trade.setItemExcep(itemExcep);
    }

    /**
     * 设置trade.itemExcep值
     */
    @Deprecated
    public static Integer getTradeItemExcep(Trade trade) {
        return trade.getItemExcep();
    }

    /**
     * 设置trade.excep值
     */
    @Deprecated
    public static void setTradeExcep(Trade trade, Long excep) {
        trade.setExcep(excep);
    }

    /**
     * 获取trade.excep值
     */
    @Deprecated
    public static Long getTradeExcep(Trade trade) {
        return trade.getExcep();
    }

    /**
     * 设置trade.v值
     */
    @Deprecated
    public static void setTradeV(Trade trade, Long v) {
        trade.setV(v);
    }

    /**
     * 获取trade.v值
     */
    @Deprecated
    public static Long getTradeV(Trade trade) {
        return trade.getV();
    }

    @Deprecated
    public static void addTradeV(Trade trade, int v) {
        setTradeV(trade, getTradeV(trade) != null ? (getTradeV(trade) | v) : (long) v);
    }

    @Deprecated
    public static void removeTradeV(Trade trade, int v) {
        if (getTradeV(trade) != null && (getTradeV(trade) | v) - getTradeV(trade) == 0) {
            setTradeV(trade, getTradeV(trade) - v);
        }
    }

    /**
     * 2^1 = 2. 地址已处理
     */
    @Deprecated
    public static boolean isAddressHandled(Trade trade) {
        return getTradeV(trade) != null && (getTradeV(trade) | 2) - getTradeV(trade) == 0;
    }

    /**
     * 2^0 = 1. 已取消平台换地址异常（反审核时会取消该标记）
     */
    @Deprecated
    public static boolean isAddressChangeCancelled(Trade trade) {
        return getTradeV(trade) != null && (getTradeV(trade) | 1) - getTradeV(trade) == 0;
    }

    /**
     * 设置order.v值
     */
    @Deprecated
    public static void setOrderV(Order order, Long v) {
        order.setV(v);
    }

    /**
     * 获取order.v值
     */
    @Deprecated
    public static Long getOrderV(Order order) {
        return order.getV();
    }

    @Deprecated
    public static boolean addOrderV(Order order, int v) {
        if (getOrderV(order) != null) {
            if ((getOrderV(order) | v) != getOrderV(order)) {
                setOrderV(order, getOrderV(order) | v);
                return true;
            }
            return false;
        }
        order.setV((long) v);
        return true;
    }

    @Deprecated
    public static boolean removeOrderV(Order order, int v) {
        if (getOrderV(order) != null && (getOrderV(order) | v) - getOrderV(order) == 0) {
            setOrderV(order, getOrderV(order) - v);
            return true;
        }
        return false;
    }

    @Deprecated
    public static boolean isItemChangeCancelled(Order order) {
        return getOrderV(order) != null && (getOrderV(order) | 1) - getOrderV(order) == 0;
    }

    /**
     *  旧的异常判断方法，双写稳定后全部删除; 异常判断属于系统内部的判断，不使用多平台字段，例如：风控异常RISK 是多平台自己的标记，交易的业务链路不应该使用，应该用交易自己的
     * @param trade
     * @param exceptEnum
     * @return
     */
    @Deprecated
    public static boolean isContainExcept(Staff staff, Trade trade, ExceptEnum exceptEnum) {
        boolean isContain = false;
        if (trade == null) {
            return false;
        }
        switch (exceptEnum) {
            case USER_UNACTIVE:
                //店铺停用不支持
                break;
            case FINANCE_REJECT_EXCEPT:
                break;
            case REFUNDING:
                // 退款
                isContain = trade.getIsRefund() != null && trade.getIsRefund() == 1L;
                break;
            case HALT:
                // 挂起
                isContain = trade.getIsHalt() != null && trade.getIsHalt() == 1;
                break;
            case ADDRESS_CHANGED:
                // 地址异常
                isContain = trade.getAddressChanged() != null && trade.getAddressChanged() == 1;
                break;
            case BLACK_NICK:
                // 黑名单
                isContain = trade.getBlackBuyerNick() != null && trade.getBlackBuyerNick() == 1;
                break;
            case SELLER_MEMO_UPDATE:
                // 留言备注
                isContain = trade.getSellerMemoUpdate() != null && trade.getSellerMemoUpdate() == 2;
                break;
            case LOST_MSG:
            case ORDER_LOST_SYS_STATUS: // 信息缺失
                isContain = trade.getIsLostMsg() != null && trade.getIsLostMsg() == 1 || (trade.getItemExcep() != null && (trade.getItemExcep() & ExceptConstantOld.TRADE_ITEM_LOST_MESSAGE) > 0);
                break;
            case TRADE_LOST_SYS_STATUS:
                isContain=StringUtils.isBlank(trade.getSysStatus());
                break;
            case LOST_ITEM:
                isContain = CollectionUtils.isEmpty(TradeUtils.getOrders4Trade(trade));
                break;
            case UNATTAINABLE:
                // 快递异常
                isContain = trade.getUnattainable() != null && trade.getUnattainable() == 1;
                break;
            case RISK:
                // 风控
                isContain = (trade.getItemExcep() != null && (trade.getItemExcep() & ExceptConstantOld.TRADE_ITEM_PDD_RISKEXCEP) == ExceptConstantOld.TRADE_ITEM_PDD_RISKEXCEP);
                break;
            case PDD_STOCK_OUT:
                // 拼多多缺货处理
                isContain = (trade.getItemExcep() != null && (trade.getItemExcep() & ExceptConstantOld.TRADE_ITEM_PDD_STOCK_OUT) == ExceptConstantOld.TRADE_ITEM_PDD_STOCK_OUT);
                break;
            case PART_REFUND:
                // 部分退款异常
                isContain = trade.getItemExcep() != null && ((trade.getItemExcep() | ExceptConstantOld.TRADE_ITEM_PART_REFUND) - trade.getItemExcep() == 0);
                break;
            case WAITING_RETURN_WMS:
                // 等待退货入仓
                isContain = trade.getItemExcep() != null && (trade.getItemExcep() | ExceptConstantOld.TRADE_ITEM_WATING_RETURN_WMS_EXCEPT) - trade.getItemExcep() == 0;
                break;
            case COD_REPEAT:
                // 货到付款订单重复
                isContain = trade.getItemExcep() != null && ((trade.getItemExcep()) | ExceptConstantOld.TRADE_ITEM_COD_REPEAT) - trade.getItemExcep() == 0;
                break;
            case WAIT_MERGE:
                //等待合并
                isContain = trade.getItemExcep() != null && (trade.getItemExcep() | ExceptConstantOld.TRADE_ITEM_WAIT_MERGE) - trade.getItemExcep() == 0;
                break;
            case PLATFORM_FX_SPLIT_EXCEPT:
                //平台分销待拆分
                isContain = trade.getExcep() != null && (trade.getExcep() | ExceptConstantOld.TRADE_ITEM_PLATFORM_FX_SPLIT_EXCEPT) - trade.getExcep() == 0;
                break;
            case FX_AMBIGUITY:
                //不明确供应商
                isContain = trade.getExcep() != null && (trade.getExcep() | ExceptConstantOld.TRADE_ITEM_FX_AMBIGUITY) - trade.getExcep() == 0;
                break;
            case FX_WAITPAY:
                //分销待付款异常
                isContain = trade.getExcep() != null && (trade.getExcep() | ExceptConstantOld.TRADE_ITEM_FX_WAITPAY) - trade.getExcep() == 0;
                break;
            case FX_UNAUDIT:
                //分销反审核异常
                isContain = trade.getExcep() != null && ((trade.getExcep()) | ExceptConstantOld.TRADE_ITEM_FX_UNAUDIT) - trade.getExcep() == 0;
                break;
            case FX_REPULSE:
                //供销商打回异常
                isContain = trade.getExcep() != null && ((trade.getExcep()) | ExceptConstantOld.TRADE_ITEM_FX_REPULSE) - trade.getExcep() == 0;
                break;
            case TMCS_STOCK_OUT:
                //天猫超市缺货回告
                isContain = trade.getExcep() != null && ((trade.getExcep()) | ExceptConstantOld.TMCS_STOCK_OUT) - trade.getExcep() == 0;
                break;
            case TMGJZY_STOCK_OUT:
                //天猫国际直营缺货异常
                isContain = trade.getExcep() != null && ((trade.getExcep()) | ExceptConstantOld.TMGJZY_STOCK_OUT) - trade.getExcep() == 0;
                break;
            case POISON_NOT_MATCH_EXPRESS_TEMPLATE:
                //得物直发订单物流模板匹配失败异常
                isContain = trade.getExcep() != null && ((trade.getExcep()) | ExceptConstantOld.POISON_NOT_MATCH_EXPRESS_TEMPLATE) - trade.getExcep() == 0;
                break;
            case PLATFORM_WAREHOUSE_MATCH:
                //天猫物流升级平台仓异常
                isContain = trade.getExcep() != null && (trade.getExcep() | ExceptConstantOld.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION) - trade.getExcep() == 0;
                break;
            case VIP_COOPERATION_CODE_NOT_MATCH:
                //未匹配常态合作编码
                isContain = trade.getExcep() != null && (trade.getExcep() | ExceptConstantOld.VIP_COOPERATION_CODE_NOT_MATCH) - trade.getExcep() == 0;
                break;
            case OUTSID_RECOVERY_FAIL:
                // 单号回收失败
                isContain=trade.getExcep() != null && (trade.getExcep() | ExceptConstantOld.TRADE_ITEM_OUTSID_RECOVERY_FAIL) - trade.getExcep() == 0;
                break;
            case UPLOAD_EXCEPT:
                //上传发货异常
                isContain = trade.getV() != null && (trade.getV() | 4) - trade.getV() == 0;
                break;
            case RELATION_CHANGED:
                isContain = (trade.getItemExcep() != null && (trade.getItemExcep() & ExceptConstantOld.TRADE_ITEM_RELATION_CHANGED) > 0) || Trade.STOCK_STATUS_RELATION_MODIFIED.equals(trade.getStockStatus());
                break;
            case REFUND_ITEM_NUM_EXCEPT:
            case GX_ITEM_CHANGE_EXCEPT:
            case EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT:
            case CAI_GOU_TRADE_EXCEPT:
            case PO_JIA_EXCEPT:
            case ONLINE_STATUS_EXCEPT:
            case SMALL_REFUND_EXCEPT:
                isContain = trade.getExcep() != null && exceptEnum.getOldExceptEnum() != null && (trade.getExcep() & exceptEnum.getOldExceptEnum().getTradeExcep()) > 0;
                break;
            case PLAT_MODIFY_ITEM_NUM_EXCEPT:
                isContain = trade.getExcep() != null && (trade.getExcep() | ExceptConstantOld.PLAT_MODIFY_ITEM_NUM_TRADE_EXCEPT) - trade.getExcep() == 0;
                break;
            case PART_PAY_EXCEPT:
                isContain = trade.getExcep() != null && (trade.getExcep() & ExceptConstantOld.PART_PAY_EXCEPT) > 0;
                break;
            case UNALLOCATED:
                // 商品未匹配
                isContain = trade.getItemExcep() != null && (trade.getItemExcep() & ExceptConstantOld.TRADE_ITEM_UNALLOCATED) > 0 || Trade.STOCK_STATUS_UNALLOCATED.equals(trade.getStockStatus());
                break;
            case ITEM_CHANGED:
                // 平台换商品
                isContain = trade.getItemExcep() != null && (trade.getItemExcep() & ExceptConstantOld.TRADE_ITEM_CHANGED) > 0;
                break;
            case DELIVER_EXCEPT:
                // 发货异常
                isContain =  trade.getItemExcep() != null && (trade.getItemExcep() | ExceptConstantOld.TRADE_ITEM_DELIVER_EXCEPT) - trade.getItemExcep() == 0;
                break;
            case SUITE_CHANGE:
                //套件修改异常
                isContain =  trade.getItemExcep() != null && (trade.getItemExcep() | ExceptConstantOld.TRADE_ITEM_SUITE_CHANGE) - trade.getItemExcep() == 0;
                break;
            case UNIQUE_CODE_OFFSHELF:
                // 唯一码下架
                isContain = trade.getItemExcep() != null && (trade.getItemExcep() | ExceptConstantOld.TRADE_ITEM_UNIQUE_CODE_OFFSHELF) - trade.getItemExcep() == 0;
                break;
            case ITEM_SHUTOFF:
                //商品停用异常
                isContain = trade.getItemExcep() != null && (trade.getItemExcep() | ExceptConstantOld.TRADE_ITEM_SHUTOFF) - trade.getItemExcep() == 0;
                break;
            case ITEM_PROCESS:
                // 加工商品转换异常
                isContain = trade.getItemExcep() != null && (trade.getItemExcep() | ExceptConstantOld.TRADE_ITEM_PROCESS) - trade.getItemExcep() == 0;
                break;
            case INSUFFICIENT:
                // 缺货异常
                isContain = (trade.getItemExcep() != null && (trade.getItemExcep() | ExceptConstantOld.TRADE_ITEM_INSUFFICIENT) - trade.getItemExcep() == 0) || Objects.equals(trade.getStockStatus(), Trade.STOCK_STATUS_INSUFFICIENT);
                break;
            case UNIQUE_CODE_CLOSE:
            case PARTY3_WAREHOUSE:
            case PARTY3_UNCREATE:
            case PARTY3_CREATE:
            case PARTY3_RECREATE:
            case QIMEN_TRADE_INTERCEPT_SUCCESS:
            case QIMEN_TRADE_INTERCEPT_FAILED:
            case PARTY3_UNATTAINABLE:
            case SHIP_BOX_SPLIT_ORDER:
            case FX_STOP_DELIVER_EXCEPT:
                Set<Long> customExceptIds = getCustomExceptIds(null, trade);
                isContain = customExceptIds.contains(exceptEnum.getOldExceptEnum().getOldIdx());
                break;
            case SMTQTG_UN_CONFIRM:
                isContain =trade.getExcep() != null && (trade.getExcep() | ExceptConstantOld.SMTQTG_UN_CONFIRM_EXCEPTION_EXCEPT) - trade.getExcep() == 0;
                break;
            case CUSTOM:
                // 自定义异常
                isContain = isContainCustomExcept(null, trade);
                break;
            case ONLINE_LOCK:
                // 线上锁定
                isContain = trade.getExcep() != null && (trade.getExcep() & ExceptConstantOld.ONLINE_LOCK_EXCEPT) > 0;
                break;
            default:
               // throw new RuntimeException(String.format("新的异常还不支持[%s]异常，请用旧的异常方法!", exceptEnum.getChinese()));
                Logs.debug(String.format(String.format("3.新的异常还不支持[%s]异常，请用旧的异常方法!", exceptEnum.getChinese())));
        }
        return isContain;
    }

    /**
     * 仅包含
     *
     * @param staff
     * @param trade
     * @param exceptEnum
     * @return
     */
    public static boolean isOnlyContainsExcept(Staff staff, Trade trade, ExceptEnum exceptEnum) {
        boolean isContain = false;
        Set<Integer> excepts = TradeUtils.parseExcept(staff, trade);
        Set<Long> customExceptIds = getCustomExceptIds(staff, trade);
        switch (exceptEnum) {
            case WAIT_MERGE:  //等待合并
            case FX_AMBIGUITY: // 不明确供应商
            case INSUFFICIENT: // 缺货异常
                isContain = excepts.contains(new Long(exceptEnum.getOldExceptEnum().getOldIdx()).intValue()) && excepts.size() == 1 && CollectionUtils.isEmpty(customExceptIds);
                break;
            case QIMEN_TRADE_INTERCEPT_FAILED:
                // 奇门拦截失败异常
                isContain = customExceptIds.contains(exceptEnum.getOldExceptEnum().getOldIdx()) && customExceptIds.size() == 1 && CollectionUtils.isEmpty(excepts);
                break;
            case CUSTOM:
                // 自定义异常
                isContain = CollectionUtils.isNotEmpty(excepts) && excepts.size() == 1 && excepts.contains(new Long(exceptEnum.getOldExceptEnum().getOldIdx()).intValue());
                break;
            default:
               // throw new RuntimeException(String.format("新的异常还不支持[%s]异常，请用旧的异常方法!", exceptEnum.getChinese()));
                Logs.debug(String.format(String.format("4.新的异常还不支持[%s]异常，请用旧的异常方法!", exceptEnum.getChinese())));

        }
        return isContain;
    }


    public static boolean isContainExcept(Staff staff,Trade trade, Long exceptId) {
        ExceptEnum exceptEnum = ExceptEnum.getExceptEnumById(exceptId);
        if (exceptEnum != null) {
            return isContainExcept(staff,trade, exceptEnum);
        }
        // 自定义异常
        String exceptIds = trade.getExceptIds();
        if (StringUtils.isBlank(exceptIds)) {
            return false;
        }
        Set<Long> exceptIdList = ArrayUtils.toLongSet(exceptIds);
        return exceptIdList.contains(exceptId);
    }



    /*********************************************************自定义异常****************************************************************************/
    @Deprecated
    public static boolean isContainOldCustomExcept(Staff staff,Trade trade){
        return trade != null && StringUtils.isNotBlank(trade.getOldExceptIds());
    }

    @Deprecated
    public static boolean isContainCustomExcept(Staff staff,Trade trade){
        return trade != null && StringUtils.isNotBlank(trade.getExceptIds());
    }
    @Deprecated
    public static Set<Long> getCustomExceptIds(Staff staff, Trade trade) {
        Set<Long> exceptIds = new HashSet<>();
        if (!isContainCustomExcept(staff, trade)) {
            return exceptIds;
        }
        exceptIds = Arrays.stream(trade.getExceptIds().split(",")).filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toSet());
        return exceptIds;
    }

    @Deprecated
    public static Set<Long> getOldCustomExceptIds(Staff staff, Trade trade){
        if (StringUtils.isBlank(trade.getOldExceptIds())) {
            return new HashSet<>();
        }
        Set<Long> exceptIds = Arrays.stream(trade.getOldExceptIds().split(",")).filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toSet());
        return exceptIds;
    }

    /**
     * 是否是异常订单
     * @param staff
     * @param trade
     * @return
     */
    public static boolean isExcept(Staff staff, Trade trade) {
        Set<Integer> sysExcepts = TradeUtils.parseExcept(staff, trade);
        return CollectionUtils.isNotEmpty(sysExcepts) || (trade!=null&&StringUtils.isNotBlank(trade.getExceptIds()));
    }


    /**
     * 信息缺失的信息
     * @param trade
     * @return
     */
    public static boolean isLostMsg(Staff staff,Trade trade) {
        if (!TradeStatusUtils.isWaitSellerSend(trade.getSysStatus())) {
          return false;
        }
        boolean lostMsg = StringUtils.isBlank(trade.getPayment()) || // 实付金额缺失
                StringUtils.isBlank(trade.getReceiverName()) || // 收件人缺失
                StringUtils.isBlank(trade.getReceiverAddress()) || //收货地址缺失
                (StringUtils.isBlank(trade.getReceiverMobile()) && StringUtils.isBlank(trade.getReceiverPhone()));  //手机/固话缺失
        if (lostMsg && (trade.getType() == null || (!trade.getType().startsWith(TYPE_TRADE_OUT)
                && !trade.getType().startsWith(TYPE_TRADE_DANGKOU)))
                && !CommonConstants.PLAT_FORM_TYPE_TEMU.equals(trade.getSource())
                && !("fds".equals(trade.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource()))) {
            return true;
        }
        return false;
    }

    /**
     * 在itemExcep上的异常
     * @param trade
     * @param exceptEnum
     * @return
     */
    private static boolean containItemExcep(Trade trade, ExceptEnum exceptEnum) {
        return trade.getItemExcep() != null && (trade.getItemExcep() | exceptEnum.getOldExceptEnum().getItemExcep()) - trade.getItemExcep() == 0;
    }
    /**
     * 在excep上的异常
     * @param trade
     * @param exceptEnum
     * @return
     */
    private static boolean containExcep(Trade trade, ExceptEnum exceptEnum) {
        return trade.getExcep() != null && (trade.getExcep() | exceptEnum.getOldExceptEnum().getTradeExcep()) - trade.getExcep() == 0;
    }
    /**
     * 在v上的异常
     * @param trade
     * @param exceptEnum
     * @return
     */
    private static boolean containV(Trade trade, ExceptEnum exceptEnum) {
        return trade.getV() != null && (trade.getV() | exceptEnum.getOldExceptEnum().getTradeV()) - trade.getV() == 0;
    }




    public static List<String> getExceptChineseNames(Staff staff,Trade trade,Map<Long,String> tagNameMap){
        List<String> exceptChineseNames = new ArrayList<>();
        Set<Integer> excepts = TradeUtils.parseExcept(staff, trade);
        for (Integer except : excepts) {
            ExceptEnum exceptEnum = ExceptEnum.getExceptEnumByOldId(new Long(except));
            if (exceptEnum != null) {
                exceptChineseNames.add(exceptEnum.getChinese());
            }
        }
        if (tagNameMap != null) {
            Set<Long> customExceptIds = getCustomExceptIds(staff, trade);
            for (Long customExceptId : customExceptIds) {
                String tagName = tagNameMap.get(customExceptId);
                if (StringUtils.isNotBlank(tagName)) {
                    exceptChineseNames.add(tagName);
                }
            }
        }
        return exceptChineseNames;
    }


    private static boolean isOrderContainsExcept(Staff staff, List<Order> orders4Trade, ExceptEnum exceptEnum, List<Long> afterSendIngoreOrderExceptIds) {
        boolean orderContainExcept = false;
        for (Order order : orders4Trade) {
            if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) && afterSendIngoreOrderExceptIds.contains(exceptEnum.getId())) {
                continue;
            }
            if (OrderExceptOldUtils.isContainsExcept(staff, order, exceptEnum)) {
                orderContainExcept = true;
                break;
            }
        }
        return orderContainExcept;
    }
}

package com.raycloud.dmj.junit.utils.wms;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.wms.AllocateGoodsRecord;
import com.raycloud.dmj.domain.wms.AllocateType;
import com.raycloud.dmj.domain.wms.GoodsSection;
import com.raycloud.dmj.services.wms.allocategoods.AllocateGoodsService;
import com.raycloud.junit4mock.MockObject;
import com.raycloud.junit4mock.MockUtilsBase;
import org.apache.commons.beanutils.BeanUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * 配货mock类
 * <AUTHOR>
 * @date 2019/5/12
 **/
@MockObject
public class AllocateGoodsMockUtils extends MockUtilsBase {

    public static AllocateGoodsService mockAllocateGoodsService(){
        return new AllocateGoodsService();
    }

    /**
     * 创建sysItemId和sysSkuId都为1，未拣，waveId为0，分配数为1，未拣数为0的配货记录
     * @param staff
     * @param goodsSection
     * @param sid
     * @return
     */
    public static AllocateGoodsRecord buildAllocateGoodsRecord(Staff staff, GoodsSection goodsSection, Long sid){
        AllocateGoodsRecord record = new AllocateGoodsRecord();
        record.setCompanyId(staff.getCompanyId());
        record.setWaveId(0L);
        record.setPickedNum(0);
        record.setAllocatedNum(1);
        record.setGoodsSectionCode(goodsSection.getCode());
        record.setGoodsSectionId(goodsSection.getId());
        record.setStatus(AllocateGoodsRecord.AllocateGoodsStatusEnum.UNPICKED.getValue());
        record.setAllocateType(AllocateType.TRADE.getValue());
        record.setSid(sid);
        record.setSysItemId(1L);
        record.setSysSkuId(1L);
        record.setWarehouseId(1L);
        return record;
    }

    public static List<AllocateGoodsRecord> buildAllocateGoodsRecords(Staff staff, GoodsSection goodsSection, Long sid){
        List<AllocateGoodsRecord> records = Lists.newArrayList();
        AllocateGoodsRecord record = buildAllocateGoodsRecord(staff, goodsSection, sid);
        try {
            for (int i = 1; i<=5; i++) {
                AllocateGoodsRecord temp = new AllocateGoodsRecord();
                BeanUtils.copyProperties(temp,record);
                temp.setGoodsSectionId(2L);
                temp.setAllocatedNum(i);
                records.add(temp);
            }
            for (int i = 1; i<=5; i++) {
                AllocateGoodsRecord temp = new AllocateGoodsRecord();
                BeanUtils.copyProperties(temp,record);
                temp.setGoodsSectionId(3L);
                temp.setAllocatedNum(i);
                records.add(temp);
            }
            // for (int i = 1; i<=5; i++) {
            //     AllocateGoodsRecord temp = new AllocateGoodsRecord();
            //     BeanUtils.copyProperties(temp,record);
            //     temp.setGoodsSectionId(2L);
            //     temp.setAllocatedNum(4);
            //     temp.setStatus(AllocateGoodsRecord.AllocateGoodsStatusEnum.PICKED.getValue());
            //     records.add(temp);
            // }
            for (int i = 1; i<=5; i++) {
                AllocateGoodsRecord temp = new AllocateGoodsRecord();
                BeanUtils.copyProperties(temp,record);
                temp.setGoodsSectionId(2L);
                temp.setAllocatedNum(i);
                temp.setAllocateType(AllocateType.PICK_INVENTORY.getValue());
                records.add(temp);
            }
            AllocateGoodsRecord temp = new AllocateGoodsRecord();
            BeanUtils.copyProperties(temp,record);
            temp.setGoodsSectionId(2L);
            temp.setAllocatedNum(16);
            temp.setAllocateType(AllocateType.PICK_INVENTORY.getValue());
            records.add(temp);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return records;
    }
}

package com.raycloud.dmj.wx;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.wx.dto.WxPurchaseOrderMsgDto;
import com.raycloud.dmj.wx.dto.WxPurchaseReturnMsgDto;

import java.util.List;
import java.util.Map;

/**
 * @Author: lit
 * @Date: 2021/12/7 4:26 下午
 */
public interface SupplierUserService {

    /**
     * 根据unionId查询 供应商用户信息
     *
     * @param unionId
     * @return
     */
    List<SupplierUser> getSupplierUserByUnionId(String unionId);

    /**
     * 取消绑定
     *
     * @param sign
     * @param unionId
     */

    void unBind(Long companyId,Long supplierId, String unionId);

    SupplierUser createSupplierUser(Long companyId, Long supplierId, String unionId);

    /**
     * 根据签名信息换取公司及供应商信息
     *
     * @param sign
     * @return
     */
    JSONObject getComInfo(String sign, String unionId);

    /**
     * 绑定
     *
     * @param sign
     * @param unionId
     * @return
     */
    String bind(Long companyId,Long supplierId, String unionId);

    /**
     * 根据公司ID及供应商ID获取绑定的微信用户列表
     *
     * @param companyId
     * @param supplierId
     * @return
     */
    List<JSONObject> getListByCompanyIdAndSupplierId(Long companyId, Long supplierId);

    /**
     *
     * @param companyId
     * @param supplierId
     * @return
     */
    Map<Long, Integer> getCountByCompanyIdAndSupplierIds(Long companyId, List<Long> supplierId);

    /**
     * 返回采购用户列表
     * @param companyId
     * @param supplierId
     * @return
     */
    List<SupplierUser> getByCompanyIdAndSupplierIds(Long companyId, List<Long> supplierIds);

    void sendPurchaseOrderWxMsg(List<WxPurchaseOrderMsgDto> wxPurchaseOrderMsgDtos);

    /**
     * 推送采购单至微信（捕获微信返回的异常）
     * @param wxPurchaseOrderMsgDtos
     * @param errorMap
     */
    void sendPurchaseOrderWxMsg(List<WxPurchaseOrderMsgDto> wxPurchaseOrderMsgDtos, Map<String, String> errorMap);

    /**
     *  推送采退单到vx
     */
    void sendPurchaseReturnWxMsg(List<WxPurchaseReturnMsgDto> wxPurchasereturnMsgDtos);
}

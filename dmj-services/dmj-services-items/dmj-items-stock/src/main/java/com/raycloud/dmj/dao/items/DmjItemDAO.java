package com.raycloud.dmj.dao.items;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.business.item.ItemMultiCodeBusiness;
import com.ibatis.sqlmap.engine.execution.SqlExecutor;
import com.raycloud.dmj.business.item.PureItemSkuBusiness;
import com.raycloud.dmj.dao.stock.process.BaseDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.Sort;
import com.raycloud.dmj.domain.account.DbInfo;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemQueryParams;
import com.raycloud.dmj.domain.item.ItemSkuBatch;
import com.raycloud.dmj.domain.item.SimpleItem;
import com.raycloud.dmj.domain.contants.BaseConstants;
import com.raycloud.dmj.domain.item.*;
import com.raycloud.dmj.domain.item.params.*;
import com.raycloud.dmj.domain.item.tb.MappingQueryParams;
import com.raycloud.dmj.domain.sku.ParamsKit;
import com.raycloud.dmj.domain.stock.FarERPStock;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.WaveSortingDetail;
import com.raycloud.dmj.web.model.items.ItemSkuReportModel;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Repository
@SuppressWarnings({"unchecked"})
public class DmjItemDAO extends BaseDao {

    @Resource
    private PureItemSkuBusiness pureItemSkuBusiness;

    @Resource
    private DmjItemDelDao dmjItemDelDao;

    @Resource
    private ItemMultiCodeBusiness itemMultiCodeBusiness;

    private void init(Staff staff, DmjItem obj) {
        obj.setCompanyId(staff.getCompanyId());
        obj.setDbNo(staff.getDbInfo().getDmjItemDbNo());
        obj.setDbKey(staff.getDbNo());
    }

    private Map<String, Object> initParams(Staff staff) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        //这里做表扩展，如果用户不存在这个字段，或者为0则分到默认表，否则水平切分表
        params.put("dbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("dbInfo", staff.getDbInfo());
        return params;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long insert(Staff staff, DmjItem dmjItem) {
        return insert(staff, dmjItem, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long insert(Staff staff, DmjItem dmjItem, boolean needDealMultiCode) {
        init(staff, dmjItem);
        pureItemSkuBusiness.processNewPureItemSku(staff, dmjItem);
        if (needDealMultiCode) {
            itemMultiCodeBusiness.processUpdateItemCodeInfo(staff, dmjItem);
        }
        getSqlMapClientTemplate(staff).insert("DmjItem.insert", dmjItem);
        return dmjItem.getSysItemId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(Staff staff, List<DmjItem> dmjItems) {
        batchInsert(staff, dmjItems, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(Staff staff, List<DmjItem> dmjItems, boolean needDealMultiCode) {
        if (CollectionUtils.isEmpty(dmjItems)) {
            return;
        }
        for (DmjItem dmjItem : dmjItems) {
            init(staff, dmjItem);
            if (needDealMultiCode) {
                itemMultiCodeBusiness.processUpdateItemCodeInfo(staff, dmjItem);
            }
        }
        pureItemSkuBusiness.processNewPureItemSku(staff, dmjItems);
        batchInsert("DmjItem.insert", dmjItems);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer deleteBySysItemId(Staff staff, Long sysItemId) {
        return deleteBySysItemId(staff, sysItemId, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer deleteBySysItemId(Staff staff, Long sysItemId, boolean needDealMultiCode) {
        Map<String, Object> params = initParams(staff);
        params.put("sysItemId", sysItemId);
        //删除前先进行备份
        dmjItemDelDao.insertBySysItemId(staff, params);
        pureItemSkuBusiness.deletePureItemSku(staff, sysItemId);
        if (needDealMultiCode) {
            itemMultiCodeBusiness.deleteItem(staff, sysItemId);
        }
        return getSqlMapClientTemplate(staff).delete("DmjItem.deleteBySysItemId", params);
    }

    /**
     * 批量删除系统商品
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteBySysItemIds(Staff staff, List<Long> sysItemIds) {
        return batchDeleteBySysItemIds(staff, sysItemIds, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteBySysItemIds(Staff staff, List<Long> sysItemIds, boolean needDealMultiCode) {
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIds", sysItemIds);
        //删除前先进行备份
        dmjItemDelDao.batchDeleteBySysItemIds(staff, params);
        pureItemSkuBusiness.deletePureItemSku(staff, sysItemIds);
        if (needDealMultiCode) {
            itemMultiCodeBusiness.batchDeleteItem(staff, sysItemIds);
        }
        return getSqlMapClientTemplate(staff).delete("DmjItem.batchDeleteBySysItemIds", params);
    }

    /**
     * 根据主键id更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBySysItemId(Staff staff, DmjItem dmjItem) {
        updateBySysItemId(staff, dmjItem, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBySysItemId(Staff staff, DmjItem dmjItem, boolean needDealMultiCode) {
        if (dmjItem.getSysItemId() == null) {
            throw new IllegalArgumentException("缺少SysItemId参数");
        }
        init(staff, dmjItem);
        pureItemSkuBusiness.updatePureItemSkuFullInfo(staff, dmjItem);
        if (needDealMultiCode) {
            itemMultiCodeBusiness.processUpdateItemCodeInfo(staff, dmjItem);
        }
        getSqlMapClientTemplate(staff).update("DmjItem.update", dmjItem);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update4Save(Staff staff, DmjItem dmjItem) {
        update4Save(staff, dmjItem, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update4Save(Staff staff, DmjItem dmjItem, boolean needDealMultiCode) {
        init(staff, dmjItem);
        pureItemSkuBusiness.updatePureItemSkuFullInfo(staff, dmjItem);
        if (needDealMultiCode) {
            itemMultiCodeBusiness.processUpdateItemCodeInfo(staff, dmjItem);
        }
        getSqlMapClientTemplate(staff).update("DmjItem.update4Save", dmjItem);
    }

    /**
     * 为excel导入时需要批量更新而写 by wangfl
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(Staff staff, List<DmjItem> dmjItems) {
        batchUpdate(staff, dmjItems, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(Staff staff, List<DmjItem> dmjItems, boolean needDealMultiCode) {
        if (CollectionUtils.isEmpty(dmjItems)) {
            return;
        }
        for (DmjItem dmjItem : dmjItems) {
            init(staff, dmjItem);
            if (needDealMultiCode) {
                itemMultiCodeBusiness.processUpdateItemCodeInfo(staff, dmjItem);
            }
        }
        //判断当前用户是否支持纯商品SKU逻辑，如果有更新时也要更新纯商品的SKU记录
//        pureItemSkuBusiness.updatePureItemSkuFullInfo(staff, dmjItems);
        super.batchUpdate("DmjItem.update", dmjItems);
    }

    public List<DmjItem> getDmjItemBySysItemId(Staff staff, List<Long> sysItemIdList) {

        return getDmjItemBySysItemIdList(staff, sysItemIdList, null);
    }

    /**
     * 根据sysItemIdList 查询
     *
     * @param staff
     * @param sysItemIdList
     * @return
     */
    public List<DmjItem> getDmjItemBySysItemIdList(Staff staff, List<Long> sysItemIdList, ItemQueryParams itemQueryParams) {
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return Collections.emptyList();
        }
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIdList", sysItemIdList);
        params.put("params", itemQueryParams);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.getDmjItemBySysItemIdList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public List<DmjItem> queryItemsBySysItemIdList(Staff staff, List<Long> sysItemIdList) {
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return Collections.emptyList();
        }
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIdList", sysItemIdList);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemsBySysItemIdList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 查询简单的商品信息
     */
    public List<DmjItem> querySimpleDmjItemBySysItemIds(Staff staff, List<Long> sysItemIdList) {
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return Collections.emptyList();
        }
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIdList", sysItemIdList);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.querySimpleDmjItemBySysItemIds", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public DmjItem queryDmjItemBySysItemId(Staff staff, long sysItemId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("dmjItemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("sysItemId", sysItemId);
        return (DmjItem) getSqlMapClientTemplate(staff).queryForObject("DmjItem.queryDmjItemBySysItemId", params);
    }

    public DmjItem getDmjItemBySysItemId(Staff staff, Long sysItemId, Integer enableStatus, Integer activeStatus) {
        if (sysItemId == null || sysItemId == -1) {
            return null;
        }

        Map<String, Object> params = initParams(staff);
        params.put("sysItemId", sysItemId);
        params.put("enableStatus", enableStatus);
        params.put("activeStatus", activeStatus);
        return (DmjItem) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getDmjItemBySysItemId", params);
    }

    /**
     * 根据sysItemId 查询
     *
     * @param staff
     * @param sysItemId
     * @return
     */
    public DmjItem getDmjItemBySysItemId(Staff staff, Long sysItemId) {
        if (sysItemId == null || sysItemId == -1) {
            return null;
        }
        return getDmjItemBySysItemId(staff, sysItemId, CommonConstants.JUDGE_YES, null);
    }

    public List<DmjItem> getDmjItemByOuterIds(Staff staff, List<String> outerIds) {
        if (CollectionUtils.isEmpty(outerIds)) {
            return Collections.emptyList();
        }
        Map<String, Object> params = initParams(staff);
        params.put("outerIds", outerIds);
        params.put("enableStatus", 1);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.getDmjItemByOuterIds", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public List<DmjItem> getImportSimpleDmjItemByOuterIds(Staff staff, List<String> outerIds) {
        if (CollectionUtils.isEmpty(outerIds)) {
            return Collections.emptyList();
        }
        Map<String, Object> params = initParams(staff);
        params.put("outerIds", outerIds);
        params.put("enableStatus", 1);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.getImportSimpleDmjItemByOuterIds", params);
    }

    public List<DmjItem> getPureDmjItemByOuterIds(Staff staff, List<String> outerIds) {
        if (CollectionUtils.isEmpty(outerIds)) {
            return Collections.emptyList();
        }
        Map<String, Object> params = initParams(staff);
        params.put("outerIds", outerIds);
        params.put("enableStatus", 1);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.getPureDmjItemByOuterIds", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 根据D方案的商家编码查询DmjItem
     * @param staff
     * @param outerIds
     * @return
     */
    public List<DmjItem> getDmjItemByOuterIdPures(Staff staff, List<String> outerIds) {
        if (CollectionUtils.isEmpty(outerIds)) {
            return Collections.emptyList();
        }
        Map<String, Object> params = initParams(staff);
        params.put("outerIdPures", outerIds);
        params.put("enableStatus", 1);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.getDmjItemByOuterIdPures", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 根据outerId取值
     *
     * @param staff
     * @param outerId
     * @return
     */
    public List<DmjItem> queryByOuterId(Staff staff, String outerId, String type) {
        if (StringUtils.isBlank(outerId)) {
            return Lists.newArrayList();
        }
        Map<String, Object> map = initParams(staff);
        map.put("outerId", outerId);
        map.put("type", type);
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.queryByOuterId", map, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }


    /**
     * 根据outerIds查询dmj_item和dmj_sku是否存在相同的，并排除掉sys_item_id的excludeSysIds的值
     *
     * @param staff
     * @param excludeSysIds
     * @param outerIds
     * @return key 为outerId,value为这个商家编码所对应的重复数量
     */
    public Map<String, Object> groupOfOuterIdsByExcludeItemSysIds(Staff staff, Long[] excludeSysIds, String[] outerIds) {
        if (null == excludeSysIds || outerIds == null)
            return new HashMap<String, Object>();
        Map<String, Object> params = initParams(staff);
        params.put("outerIds", outerIds);
        params.put("excludeSysIds", excludeSysIds);
        return getSqlMapClientTemplate(staff).queryForMap("DmjItem.groupOfOuterIdsByExcludeItemSysIds", params, "outer_id", "c");
    }

    /**
     * 根据outerIds查询dmj_item和dmj_sku是否存在相同的，并排除掉sys_sku_id的excludeSysIds的值
     *
     * @param staff
     * @param excludeSysIds
     * @param outerIds
     * @return key 为outerId,value为这个商家编码所对应的重复数量
     */
    public Map<String, Object> groupOfOuterIdsByExcludeSkuSysIds(Staff staff, Long[] excludeSysIds, String[] outerIds) {
        if (null == excludeSysIds || outerIds == null)
            return new HashMap<String, Object>();
        Map<String, Object> params = initParams(staff);
        params.put("outerIds", outerIds);
        params.put("excludeSysIds", excludeSysIds);
        return getSqlMapClientTemplate(staff).queryForMap("DmjItem.groupOfOuterIdsByExcludeSkuSysIds", params, "outer_id", "c");
    }

    /**
     * 获取商品库存数量
     *
     * @param staff
     * @param itemQueryParams
     * @return
     */
    public Long getStockItemCount(Staff staff, ItemQueryParams itemQueryParams) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(itemQueryParams);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        params.setLevel(0);
        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getStockItemCount", params);
    }

    public Long getSmallProItemCount(Staff staff, Map<String, Object> params) {
        Map<String, Object> map = initParams(staff);
        params.putAll(map);
        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getSmallProItemCount", params);
    }


    public List<DmjItem> getSmallProItemList(Staff staff, Map<String, Object> params) {
        Map<String, Object> map = initParams(staff);
        params.putAll(map);
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.getSmallProItemList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public List<DmjItem> getStockItemList(Staff staff, ItemQueryParams itemQueryParams) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(itemQueryParams);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        params.setLevel(0);

        return getSqlMapClientTemplate(staff).queryForList("DmjItem.getStockItemList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 获取sku库存数量
     *
     * @param staff
     * @param itemQueryParams
     * @return
     */
    public Long getStockSkuCount(Staff staff, ItemQueryParams itemQueryParams) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(itemQueryParams);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        params.setLevel(1);

        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getStockSkuCount", params);
    }

    public List<DmjItem> getStockSkuList(Staff staff, ItemQueryParams itemQueryParams) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(itemQueryParams);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        params.setLevel(1);

        return getSqlMapClientTemplate(staff).queryForList("DmjItem.getStockSkuList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 根据条件，查询所有商品数量，可以查库存
     */
    public int getDmjItemListCount(Staff staff, ItemQueryParams dmjItemQuery) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(dmjItemQuery);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        params.setLevel(0);

        return (int) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getDmjItemListCount", params);
    }

    /**
     * 查询所有商品信息，可以查库存
     *
     * @param staff
     * @param dmjItemQuery
     * @return
     */
    public List<DmjItem> getDmjItemListWithPageConcise(Staff staff, ItemQueryParams dmjItemQuery) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(dmjItemQuery);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        params.setLevel(0);

        return getSqlMapClientTemplate(staff).queryForList("DmjItem.getDmjItemListWithPage", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public Long getDmjItemListCountJoin(Staff staff, ItemQueryParams itemQueryParams) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(itemQueryParams);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());

        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getDmjItemListCountJoin", params);
    }

    public List<DmjItem> getDmjItemListWithPageJoin(Staff staff, ItemQueryParams dmjItemQuery) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(dmjItemQuery);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());

        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.getDmjItemListWithPageJoin", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public Long getDmjSkuItemCount(Staff staff, ItemQueryParams dmjItemQuery) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(dmjItemQuery);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        params.setLevel(1);

        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getDmjSkuItemCount", params);
    }

    public List<DmjItem> getDmjSkuItemListWithPage(Staff staff, ItemQueryParams dmjItemQuery) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(dmjItemQuery);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        params.setLevel(1);

        return getSqlMapClientTemplate(staff).queryForList("DmjItem.getDmjSkuItemListWithPage", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 库存状态查询
     * 根据条件查询所有sku数量
     */
    public int getDmjSkuCount(Staff staff, ItemQueryParams dmjItemQuery) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(dmjItemQuery);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        params.setLevel(1);

        return (int) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getDmjSkuCount", params);
    }


    /**
     * 库存状态查询
     * 根据条件查询所有sku列表
     */
    public List<FarERPStock> getDmjSkuListWithPage(Staff staff, ItemQueryParams dmjItemQuery) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(dmjItemQuery);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        params.setLevel(1);

        return getSqlMapClientTemplate(staff).queryForList("DmjItem.getDmjSkuListWithPage", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }


    /**
     * 批量更新预警值 xy.cao
     */
    public int updateStockAlarm(Staff staff, List<DmjItem> sysItemList) {
        Collections.sort(sysItemList, new Comparator<DmjItem>() {
            @Override
            public int compare(DmjItem o1, DmjItem o2) {
                return o1.getSysItemId().compareTo(o2.getSysItemId());
            }
        });
        for (DmjItem dmjItem : sysItemList) {
            init(staff, dmjItem);
        }

        return super.batchUpdate("DmjItem.updateStockAlarm", sysItemList);
    }

    /**
     * 更新销售价以及成本价
     *
     * @param staff
     * @param dmjItemList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int updatePrice4Supplier(Staff staff, List<DmjItem> dmjItemList) {
        for (DmjItem dmjItem : dmjItemList) {
            init(staff, dmjItem);
        }
        pureItemSkuBusiness.updatePureItemSkuFullInfo(staff, dmjItemList);
        return super.batchUpdate("DmjItem.updatePrice4Supplier", dmjItemList);
    }


    /**
     * 查询可以绑定的商品列表
     *
     * @param staff
     * @return
     */
    public List<DmjItem> queryCanBindItemWithPage(Staff staff, ItemQueryParams itemQueryParams) {
        Map<String, Object> params = initParams(staff);
        params.put("itemQueryParams", itemQueryParams);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryCanBindItemWithPage", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public Long queryCanBindItemWithPageTotal(Staff staff, ItemQueryParams itemQueryParams) {
        Map<String, Object> params = initParams(staff);
        params.put("itemQueryParams", itemQueryParams);
        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.queryCanBindItemWithPageTotal", params);
    }

    /**
     * 没有绑定平台商品的系统商品
     *
     * @param staff
     * @param _params
     * @return
     */
    public List<DmjItem> getEmptyItemERPBridge(Staff staff, MappingQueryParams _params) {
        Map<String, Object> params = initParams(staff);
        params.put("onSale", _params.getOnSale());//onsale是前端使用的字段
        params.put("queryType", _params.getQueryType());
        params.put("queryText", _params.getQueryText());
        params.put("page", _params.getPage());
        params.put("SkuBridgeNo", staff.getDbInfo().getSkuBridgeNo());
        params.put("dmjSkuDbNo", staff.getDbInfo().getDmjSkuDbNo());
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.getEmptyItemERPBridge", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public Long getEmptyItemERPBridgeTotal(Staff staff, MappingQueryParams _params) {
        Map<String, Object> params = initParams(staff);
        params.put("enableStatus", 1);
        params.put("onSale", _params.getOnSale());//onsale是前端使用的字段
        params.put("text", _params.getText());
        params.put("SkuBridgeNo", staff.getDbInfo().getSkuBridgeNo());
        params.put("dmjSkuDbNo", staff.getDbInfo().getDmjSkuDbNo());
        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getEmptyItemERPBridgeTotal", params);
    }

    /**
     * 选择性更新商品信息，is_sku_item, alarmed
     *
     * @param staff
     * @param item
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateSection(Staff staff, DmjItem item) {
        init(staff, item);
        pureItemSkuBusiness.updatePureItemSkuFullInfo(staff, item);
        return getSqlMapClientTemplate(staff).update("DmjItem.updateSection", item);
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateBase(Staff staff, List<DmjItem> dmjItemList) {
        return batchUpdateBase(staff, dmjItemList, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateBase(Staff staff, List<DmjItem> dmjItemList, boolean needDealMultiCode) {
        if (CollectionUtils.isEmpty(dmjItemList)) {
            return 0;
        }
        for (DmjItem dmjItem : dmjItemList) {
            init(staff, dmjItem);
            if (needDealMultiCode) {
                itemMultiCodeBusiness.processUpdateItemCodeInfo(staff, dmjItem);
            }
        }
        pureItemSkuBusiness.updatePureItemSkuFullInfo(staff, dmjItemList);
        return batchUpdate("DmjItem.updateBase", dmjItemList);
    }

    /**
     * 查询过滤出在数据库中已经存在在outerId (查询dmj_item表和dmj_sku表)
     *
     * @param staff
     * @param outerIdList
     * @return
     */
    public List<String> checkSysOuterIdListExists(Staff staff, List<String> outerIdList) {
        if (CollectionUtils.isEmpty(outerIdList)) {
            return Collections.EMPTY_LIST;
        }
        Map<String, Object> params = initParams(staff);
        params.put("outerIdList", outerIdList);
        params.put("dmjSkuDbNo", staff.getDbInfo().getDmjSkuDbNo());
        return (List<String>) getSqlMapClientTemplate(staff).queryForList("DmjItem.checkSysOuterIdListExists", params);
    }

    public List<DmjItem> queryAllItemByCompanyId(Staff staff, Integer isSkuItem) {
        Map<String, Object> params = initParams(staff);
        params.put("isSkuItem", isSkuItem);
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.queryAllItemByCompanyId", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 查询所有可用的纯商品
     *
     * @param staff
     * @return
     */
    public List<DmjItem> selectAllPure(Staff staff, Page page) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("startRow", page.getStartRow());
        params.put("offsetRow", page.getOffsetRow());
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.selectAllPure", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }


    /**
     * 检查给出的outerId 在已经绑定的系统商品和 所有的系统sku中都没有出现过，返回重复的商家编码
     * 新版本中，由于一个店铺中多个平台商品对应一个系统商品，所以更加商家编码能查询到系统商品，可以直接绑定，新建时就要考虑是否和系统sku的商品相同了
     *
     * @param staff
     * @param outerIdList 商家编码
     * @return
     */
    public List<String> checkSysOuterIdExists4CopyBindItem(Staff staff, User user, List<String> outerIdList) {
        if (CollectionUtils.isEmpty(outerIdList)) {
            return Collections.EMPTY_LIST;
        }
        Map<String, Object> params = initParams(staff);
        params.put("outerIdList", outerIdList);
        params.put("skuErpBridgeDBNO", staff.getDbInfo().getSkuBridgeNo());
        params.put("dmjSkuDbNo", staff.getDbInfo().getDmjSkuDbNo());
        params.put("userId", user.getId());
        Set<String> resultSet = Sets.newHashSetWithExpectedSize(outerIdList.size());
        List<String> outerIdListAfterQuery = (List<String>) getSqlMapClientTemplate(staff).queryForList("DmjItem.checkSysOuterIdExists4CopyBindItem4Sku", params);
        for (String outerId : outerIdListAfterQuery) {
            resultSet.add(outerId.toLowerCase());
        }
        return Lists.newArrayList(resultSet);
    }

    public List<DmjItem> querySysItemList(Staff staff, ItemQueryParams queryParams) {
        Map<String, Object> params = initParams(staff);
        params.put("params", queryParams);
        params.put("needLikedSkuTable", StockConstants.getNeedSkuTable(queryParams));

        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.querySysItemList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public Long querySysItemListCount(Staff staff, ItemQueryParams queryParams) {
        Map<String, Object> params = initParams(staff);
        Sort sort = queryParams.getSort();
        String sortField = null;
        if (sort != null) {
            sortField = queryParams.getSort().getField();
            queryParams.getSort().setField(null);
        }
        Page page = queryParams.getPage();
        //统计时，sku中的sku需要全部处理
        queryParams.setPage(null);
        params.put("params", queryParams);
        params.put("needLikedSkuTable", StockConstants.getNeedSkuTable(queryParams));
        Long result = (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.querySysItemListCount", params);
        queryParams.setPage(page);
        if (sort != null) {
            queryParams.getSort().setField(sortField);
        }
        return result;
    }

    public List<DmjItem> querySysItemPage(Staff staff, ItemQueryParams queryParams) {
        Map<String, Object> params = initParams(staff);
        params.put("itemQueryParams", queryParams);
        params.put("params", queryParams);

        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.querySysItemPage", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public Long querySysItemPageCount(Staff staff, ItemQueryParams queryParams) {
        Map<String, Object> params = initParams(staff);
        params.put("itemQueryParams", queryParams);
        params.put("params", queryParams);
        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.querySysItemPageCount", params);
    }

    /**
     * 停用商品
     *
     * @param staff
     * @param sysItemId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateActiveStatusBySysItemId(Staff staff, long sysItemId, int activeStatus) {
        Map<String, Object> params = initParams(staff);
        params.put("sysItemId", sysItemId);
        params.put("activeStatus", activeStatus);
        DmjItem dmjItem = new DmjItem();
        dmjItem.setSysItemId(sysItemId);
        dmjItem.setActiveStatus(activeStatus);
        pureItemSkuBusiness.updatePureItemSkuSimpleInfo(staff, dmjItem);
        return getSqlMapClientTemplate(staff).update("DmjItem.updateActiveStatusBySysItemId", params);
    }

    /**
     * 更新商品库存预警
     *
     * @param staff
     * @param sysItemIdList
     */
    public void updateStockStatus4Sandbox(Staff staff, ArrayList<Long> sysItemIdList) {
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return;
        }
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIdList", sysItemIdList);
        getSqlMapClientTemplate(staff).update("DmjItem.updateStockStatus4Sandbox", params);
    }

    public List<DmjItem> query4StockInit(Staff staff, Long warehouseId, Page page) {
        Map<String, Object> params = new HashMap<String, Object>(5);
        params.put("dbInfo", staff.getDbInfo());
        params.put("companyId", staff.getCompanyId());
        params.put("warehouseId", warehouseId);
        params.put("page", page);
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.query4StockInit", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 删除分类后，清除系统商品的分类为未分类
     *
     * @param staff
     * @param cidList
     */
    public void update2ResetSellerCat(Staff staff, List<Long> cidList, long nonClassifiedId) {
        Map<String, Object> params = initParams(staff);
        params.put("cidList", cidList);
        params.put("nonClassifiedId", nonClassifiedId);
        getSqlMapClientTemplate(staff).update("DmjItem.update2ResetSellerCat", params);
    }


    /**
     * 根据公司Id统计商品及其商品下规格的总数量
     *
     * @param staff
     * @return
     */
    public Integer countItemSkuByCompanyId(Staff staff, String key, String text) {
        Map<String, Object> params = new HashMap<String, Object>(2);
        params.put("itemDbNo", staff.getDbInfo().getItemDbNo());
        params.put("skuDbNo", staff.getDbInfo().getSkuDbNo());
        params.put("companyId", staff.getCompanyId());
        if (text != null) {
            params.put("text", text);
            params.put("key", key);
        }
        return Integer.valueOf(getSqlMapClientTemplate(staff).queryForObject("DmjItem.countItemSkuByCompanyId", params).toString());
    }

    /**
     * 查询商品及其所属规格商品信息
     *
     * @param staff
     * @param page
     * @return
     */
    public List<ItemSkuReportModel> query4ItemSkuList(Staff staff, Page page, String key, String text) {
        Map<String, Object> params = new HashMap<String, Object>(3);
        params.put("dbInfo", staff.getDbInfo());
        params.put("companyId", staff.getCompanyId());
        params.put("page", page);
        params.put("key", key);
        if (StringUtils.isNotBlank(text)) {
            params.put("text", text);
        }
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.query4ItemSkuList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 根据商品编码查询商品是否存在
     *
     * @param staff
     * @param outerId
     * @return
     */
    public List<Map<String, Object>> queryItemInfoExists(Staff staff, String outerId) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("itemDbNo", staff.getDbInfo().getItemDbNo());
        params.put("skuDbNo", staff.getDbInfo().getSkuDbNo());
        params.put("outerId", outerId);
        params.put("companyId", staff.getCompanyId());
        return this.getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemInfoExists", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }


    public Map<Long, DmjItem> queryBySysItemIdsMap(String sysItemIds, Staff staff) {
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("sysItemIds", sysItemIds);
        condition.put("dbNo", staff.getDbInfo().getDmjItemDbNo());
        condition.put("companyId", staff.getCompanyId());
        return getSqlMapClientTemplate(staff).queryForMap("DmjItem.queryBySysItemIds", condition, "sysItemId");
    }

    public List<Long> checkIsSkuItemDataOk(Staff staff, Long companyId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", companyId == null ? staff.getCompanyId() : companyId);
        params.put("skuNo", staff.getDbInfo().getDmjSkuDbNo());
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.checkIsSkuItemDataOk", params);
    }

    public int fixIsSkuItem(Staff staff, Long companyId, List<Long> sysItemIds, Integer isSkuItem) throws Exception {
        if (CollectionUtils.isEmpty(sysItemIds)) {
            return 0;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", companyId == null ? staff.getCompanyId() : companyId);
        params.put("sysItemIds", sysItemIds);
        params.put("isSkuItem", isSkuItem);
        return getSqlMapClientTemplate(staff).update("DmjItem.fixIsSkuItem", params);
    }

    public List<Long> checkSuiteList(Staff staff, ArrayList<Long> sysItemIdList, String type) {
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return Lists.newArrayList();
        }
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("sysItemIdList", sysItemIdList);
        condition.put("dbNo", staff.getDbInfo().getDmjItemDbNo());
        condition.put("companyId", staff.getCompanyId());
        condition.put("type", type);
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.checkSuiteList", condition);
    }

    /**
     * 根据平台商品id查询 关联的系统纯商品
     *
     * @param staff
     * @param numIid
     * @return
     */
    public DmjItem queryItemByNumIid(Staff staff, String numIid) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("numIid", numIid);
        params.put("itemNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("skuBridgeNo", staff.getDbInfo().getSkuBridgeNo());
        return (DmjItem) getSqlMapClientTemplate(staff).queryForObject("DmjItem.queryItemByNumIid", params);
    }

    public List<DmjItem> getDmjItemList4Search(Staff staff, ItemQueryParams itemQueryParams) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(itemQueryParams);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.getDmjItemList4Search", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public Long getSearchOfTotal(Staff staff, ItemQueryParams itemQueryParams) {
        ParamsKit params = new ParamsKit();
//        itemQueryParams.setSysItemIds(null);
        params.setItemQueryParams(itemQueryParams);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getSearchOfTotal", params);

    }

    /**
     * 按照款式打印
     */
    public List<DmjItem> getSearchPrintStyleList(Staff staff, ItemQueryParams itemQueryParams) {
        ParamsKit params = new ParamsKit();
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setItemQueryParams(itemQueryParams);
        params.setDbInfo(staff.getDbInfo());
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.getSearchPrintStyleList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 按照款式打印
     */
    public Long getSearchPrintStyleCount(Staff staff, ItemQueryParams itemQueryParams) {
        ParamsKit params = new ParamsKit();
        params.setItemQueryParams(itemQueryParams);
        Map<String, Object> map = initParams(staff);
        params.setSplit(map);
        params.setDbInfo(staff.getDbInfo());
        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getSearchPrintStyleCount", params);

    }

    public int batchUpdateCate(Staff staff, List<Long> sysItemIds, String sellerCids) {
        if (CollectionUtils.isEmpty(sysItemIds)) {
            return 0;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        params.put("sellerCids", sellerCids);
        return getSqlMapClientTemplate(staff).update("DmjItem.batchUpdateCate", params);
    }

    public Integer check4brand(Staff staff, Long brandId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("brandId", brandId);
        return (Integer) getSqlMapClientTemplate(staff).queryForObject("DmjItem.check4brand", params);

    }

    public List<DmjItem> getDmjItemListWithPage4SkuBind(Staff staff, ItemQueryParams queryParams) {
        Map<String, Object> params = initParams(staff);
        params.put("text", queryParams.getText());
        params.put("excludeSysItemIdList", queryParams.getExcludedSysItemIds());
        params.put("excludeSysSkuIdList", queryParams.getExcludedSysSkuIds());
        params.put("page", queryParams.getPage());
        params.put("content", queryParams.getContent());
        params.put("searchType", queryParams.getSearchType());

        if(StringUtil.isNotBlank(queryParams.getTilePropertiesName())){
            params.put("tilePropertiesName", queryParams.getTilePropertiesName());
        }
        if(StringUtil.isNotBlank(queryParams.getTileSkuAlias())){
            params.put("tileSkuAlias", queryParams.getTileSkuAlias());
        }

        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.getDmjItemListWithPage4SkuBind", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public Long getDmjItemListTotal4SkuBind(Staff staff, ItemQueryParams queryParams) {
        Map<String, Object> params = initParams(staff);
        params.put("text", queryParams.getText());
        params.put("excludeSysItemIdList", queryParams.getExcludedSysItemIds());
        params.put("excludeSysSkuIdList", queryParams.getExcludedSysSkuIds());
        params.put("page", queryParams.getPage());
        params.put("content", queryParams.getContent());

        if(StringUtil.isNotBlank(queryParams.getTilePropertiesName())){
            params.put("tilePropertiesName", queryParams.getTilePropertiesName());
        }
        if(StringUtil.isNotBlank(queryParams.getTileSkuAlias())){
            params.put("tileSkuAlias", queryParams.getTileSkuAlias());
        }

        return (Long) getSqlMapClientTemplate(staff).queryForObject("DmjItem.getDmjItemListTotal4SkuBind", params);
    }

    /**
     * 根据平台skuId获得dmjItem(pure)
     *
     * @param staff
     * @param skuId platform.skuId
     * @return
     */
    public DmjItem queryItemByPlatSkuId(Staff staff, String skuId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("skuId", skuId);
        params.put("itemNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("skuBridgeNo", staff.getDbInfo().getSkuBridgeNo());
        return (DmjItem) getSqlMapClientTemplate(staff).queryForObject("DmjItem.queryItemByPlatSkuId", params);
    }

    public List<DmjItem> queryItemInfoWithIdList(Staff staff, List<Long> sysItemIdList, Integer isSkuItem) {
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return Collections.EMPTY_LIST;
        }
        Map<String, Object> params = initParams(staff);
        params.put("enableStatus", 1);
        params.put("sysItemIdList", sysItemIdList);
        if (isSkuItem != null) {
            params.put("isSkuItem", isSkuItem);
        }
        params.put("DB", staff.getDbInfo().getItemBridgeNo());
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemInfoWithIdList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public List<DmjItem> getSpecifyFieldOfDmjItemInfo(Staff staff, String fields, List<Long> sysItemIds) {
        if (CollectionUtils.isEmpty(sysItemIds) || fields == null) {
            return Collections.EMPTY_LIST;
        }
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIds", sysItemIds);
        params.put("fields", fields);
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.getSpecifyFieldOfDmjItemInfo", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateUnit(Staff staff, List<Long> sysItemIds, String unit) {
        if (CollectionUtils.isEmpty(sysItemIds)) {
            return 0;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        params.put("unit", unit);
        pureItemSkuBusiness.batchUpdateUnit(staff, sysItemIds, unit);
        return getSqlMapClientTemplate(staff).update("DmjItem.batchUpdateUnit", params);
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateShipper(Staff staff, List<Long> sysItemIdList, String shipper) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("dmjItemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("sysItemIdList", sysItemIdList);
        params.put("shipper", shipper);
        params.put("companyId", staff.getCompanyId());
        pureItemSkuBusiness.batchUpdateShipper(staff, sysItemIdList, shipper);
        return getSqlMapClientTemplate(staff).update("DmjItem.batchUpdateShipper", params);
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateHasSupplier(Staff staff, List<Long> sysItemIdList, int hasSupplier) {
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return 0;
        }

        Map<String, Object> params = Maps.newHashMap();
        params.put("dmjItemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("sysItemIdList", sysItemIdList);
        params.put("hasSupplier", hasSupplier);
        params.put("companyId", staff.getCompanyId());
        pureItemSkuBusiness.batchUpdateHasSupplier(staff, sysItemIdList, hasSupplier);
        return getSqlMapClientTemplate(staff).update("DmjItem.batchUpdateHasSupplier", params);
    }

    public List<Long> querySysItemIdsByCIds(Staff staff, List<Long> cIds, String type) {
        Map<String, Object> params = initParams(staff);
        params.put("cIds", cIds);
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.querySysItemIdsByCIds", params);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer batchUpdateField(Staff staff, List<Long> sysItemIdList, DmjItem dmjItem) {
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return 0;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("dmjItemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("sysItemIdList", sysItemIdList);
        params.put("dmjItem", dmjItem);
        params.put("companyId", staff.getCompanyId());
        pureItemSkuBusiness.batchUpdateField(staff, sysItemIdList, dmjItem);
        return getSqlMapClientTemplate(staff).update("DmjItem.batchUpdateField", params);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer batchUpdateBatchManage(Staff staff, List<Long> sysItemIdList, ItemQueryParams itemQueryParams) {
        DmjItem dmjItem = new DmjItem();
        dmjItem.setHasBatch(itemQueryParams.getHasBatch());
        dmjItem.setBatchRule(itemQueryParams.getBatchRule());
        dmjItem.setNearDate(itemQueryParams.getNearDate());

        return batchUpdateField(staff, sysItemIdList, dmjItem);
    }

    public Map<String, ItemSkuBatch> queryItemSkuBatchs(Staff staff, List<ItemSkuBatch> itemSkuBatchs) {
        return queryItemSku(staff, 1, itemSkuBatchs);
    }

    public Map<String, ItemSkuBatch> queryItemSku(Staff staff, Integer hasBatch, List<ItemSkuBatch> itemSkuBatchs) {
        Map<String, ItemSkuBatch> filter = new HashMap<String, ItemSkuBatch>();

        Map<String, Object> params = initParams(staff);
        params.put("sysItemIds", getSysItemIds(itemSkuBatchs));
        params.put("sysSkuIds", getSysSkuIds(itemSkuBatchs));
        if (hasBatch != null) {
            params.put("hasBatch", hasBatch);
        }
        Boolean checkActiveStatus = Optional.ofNullable(itemSkuBatchs).orElse(Lists.newArrayList()).stream().anyMatch(batch -> batch.getCheckActiveStatus() == null || batch.getCheckActiveStatus() == true);
        params.put("checkActiveStatus", checkActiveStatus);
        List<ItemSkuBatch> result = getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemSkuBatchs", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
        if (result == null || result.size() == 0) {
            return filter;
        }
        Set<String> keys = getBatchKey(itemSkuBatchs);
        for (ItemSkuBatch itemSkuBatch : result) {
            if (keys.contains(itemSkuBatch.getKey())) {
                filter.put(itemSkuBatch.getKey(), itemSkuBatch);
            }
        }
        return filter;
    }

    /**
     * key:itemId_sku_id
     * @param staff
     * @param hasBatch
     * @param sysItemIds
     * @param sysSkuIds
     * @return
     */
    public Map<String, ItemSkuBatch> queryItemSkuByItemSkuIds(Staff staff, Integer hasBatch, List<Long> sysItemIds,List<Long> sysSkuIds) {
        Map<String, ItemSkuBatch> filter = new HashMap<String, ItemSkuBatch>();

        Map<String, Object> params = initParams(staff);
        params.put("sysItemIds", sysItemIds);
        params.put("sysSkuIds", sysSkuIds);
        if (hasBatch != null) {
            params.put("hasBatch", hasBatch);
        }
        List<ItemSkuBatch> result = getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemSkuBatchs", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
        if (result == null || result.size() == 0) {
            return filter;
        }
        return result.stream().collect(Collectors.toMap(ItemSkuBatch::getKey, Function.identity(), (key1, key2) -> key1));
    }


    private List<Long> getSysItemIds(List<ItemSkuBatch> itemSkuBatchs) {
        List<Long> sysItemIds = new ArrayList<Long>();
        for (ItemSkuBatch itemSkuBatch : itemSkuBatchs) {
            if (!sysItemIds.contains(itemSkuBatch.getSysItemId())) {
                sysItemIds.add(itemSkuBatch.getSysItemId());
            }
        }
        return sysItemIds;
    }

    private List<Long> getSysSkuIds(List<ItemSkuBatch> itemSkuBatchs) {
        List<Long> sysSkuIds = new ArrayList<Long>();
        for (ItemSkuBatch itemSkuBatch : itemSkuBatchs) {
            if (!sysSkuIds.contains(itemSkuBatch.getSysSkuId())) {
                sysSkuIds.add(itemSkuBatch.getSysSkuId());
            }
        }
        return sysSkuIds;
    }

    private Set<String> getBatchKey(List<ItemSkuBatch> itemSkuBatchs) {
        Set<String> keys = new HashSet<String>();
        for (ItemSkuBatch itemSkuBatch : itemSkuBatchs) {
            keys.add(itemSkuBatch.getKey());
        }
        return keys;
    }

    public List<DmjItem> queryItemPlatformIdExist(Staff staff, List<String> platformIdList) {
        if (CollectionUtils.isEmpty(platformIdList)) {
            return Collections.emptyList();
        }

        Map<String, Object> params = Maps.newHashMap();
        params.put("dmjItemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("platformIdList", platformIdList);
        params.put("companyId", staff.getCompanyId());

        return getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemPlatformIdExist", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    /**
     * 根据枚举类型来修改自动计算的字段
     *
     * @param staff
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCalculateColumnsBySysItemId(Staff staff, List<ItemUpdateParams> itemUpdateParams) {
        if (CollectionUtils.isEmpty(itemUpdateParams)) {
            return;
        }
        //批量修改因为找不到DBKey 需要重新构造dmjItem来修改数据
        List<DmjItem> updateItems = Lists.newArrayListWithCapacity(itemUpdateParams.size());
        for (ItemUpdateParams updateParams : itemUpdateParams) {
            DmjItem dmjItem = new DmjItem();
            init(staff, dmjItem);
            dmjItem.setSysItemId(updateParams.getSysItemId());
            buildCalculateColumns(dmjItem, updateParams);
            updateItems.add(dmjItem);
        }
        pureItemSkuBusiness.updateCalculateColumns(staff, updateItems);
        this.batchUpdate("DmjItem.batchUpdateCalculateColumn", updateItems);
    }

    private void buildCalculateColumns(DmjItem dmjItem, ItemUpdateParams updateParams) {
        Preconditions.checkNotNull(dmjItem);
        Preconditions.checkNotNull(updateParams);
        dmjItem.setIsSysWholesalePrice(updateParams.getIsSysWholesalePrice());
        dmjItem.setIsSysWeight(updateParams.getIsSysWeight());
        dmjItem.setIsSysPriceImport(updateParams.getIsSysPurchasePrice());
        dmjItem.setIsSysPriceOutput(updateParams.getIsSysSellingPrice());
    }

    public List<DmjItem> queryDefinedItem(Staff staff) {
        Map<String, Object> params = initParams(staff);
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.queryDefinedItem", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    @Transactional(rollbackFor = Exception.class)
    public void clearItemIntegral(Staff staff, List<Long> sysItemIdList) {
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return;
        }
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIdList", sysItemIdList);
        pureItemSkuBusiness.clearItemIntegral(staff, sysItemIdList);
        getSqlMapClientTemplate(staff).update("DmjItem.clearItemIntegral", params);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRecord(Staff staff, DmjItem dmjItem) {
        init(staff, dmjItem);
        pureItemSkuBusiness.updatePureItemSkuFullInfo(staff, dmjItem);
        getSqlMapClientTemplate(staff).update("DmjItem.updateRecord", dmjItem);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updatePurchasePrice(Staff staff, List<Long> sysItemIds, Double purchasePrice) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemTableNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        params.put("purchasePrice", purchasePrice);
        pureItemSkuBusiness.updatePurchasePrice(staff, sysItemIds, purchasePrice);
        getSqlMapClientTemplate(staff).update("DmjItem.updatePurchasePrice", params);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchFillMoreIntegral(Staff staff, List<DmjItem> dmjItemList, Integer selectIntegralType) {
        if (CollectionUtils.isEmpty(dmjItemList)) {
            return;
        }
        for (DmjItem dmjItem : dmjItemList) {
            init(staff, dmjItem);
        }
        switch (selectIntegralType) {
            case 0:
                pureItemSkuBusiness.batchFillMoreIntegral(staff, dmjItemList);
                batchUpdate("DmjItem.batchFillMoreIntegral", dmjItemList);
                break;
            case 1:
                pureItemSkuBusiness.batchFillMorePackageIntegral(staff, dmjItemList);
                batchUpdate("DmjItem.batchFillMorePackageIntegral", dmjItemList);
                break;
            case 2:
                pureItemSkuBusiness.batchFillMoreInspectionIntegral(staff, dmjItemList);
                batchUpdate("DmjItem.batchFillMoreInspectionIntegral", dmjItemList);
                break;
            case 3:
                pureItemSkuBusiness.batchFillMoreWeightIntegral(staff, dmjItemList);
                batchUpdate("DmjItem.batchFillMoreWeightIntegral", dmjItemList);
                break;
            default:
                break;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRemark(Staff staff, long sysItemId, String remark) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("sysItemId", sysItemId);
        params.put("remark", remark);
        DmjItem dmjItem = new DmjItem();
        dmjItem.setSysItemId(sysItemId);
        dmjItem.setRemark(remark);
        pureItemSkuBusiness.updatePureItemSkuSimpleInfo(staff, dmjItem);
        getSqlMapClientTemplate(staff).update("DmjItem.updateRemark", params);
    }

    public void updateShortTitle(Staff staff, long sysItemId, String shortTitle) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("sysItemId", sysItemId);
        params.put("shortTitle", shortTitle);
        getSqlMapClientTemplate(staff).update("DmjItem.updateShortTitle", params);
    }

    public int queryItemCount4Stock(Staff staff, Item4StockQueryParams params) {
        DbInfo dbInfo = staff.getDbInfo();
        params.setCompanyId(staff.getCompanyId())
                .setItemTableNo(dbInfo.getDmjItemDbNo())
                .setSkuTableNo(dbInfo.getSkuDbNo())
                .setSkuBridgeTableNo(dbInfo.getSkuBridgeNo())
                .setStockTableNo(dbInfo.getItemStockDbNo())
                .setItemWarnTableNo(dbInfo.getItemWarnDbNo())
                .setItemSupplierBridgeTableNo(dbInfo.getItemSupplierBridgeDbNo())
                .setUploadRuleTableNo(dbInfo.getStockUploadRuleDbNo());
        return (int) getSqlMapClientTemplate(staff).queryForObject("DmjItem.queryItemCount4Stock", params);
    }

    public List<DmjItem> queryItemList4Stock(Staff staff, Item4StockQueryParams params) {
        DbInfo dbInfo = staff.getDbInfo();
        params.setCompanyId(staff.getCompanyId())
                .setItemTableNo(dbInfo.getDmjItemDbNo())
                .setSkuTableNo(dbInfo.getSkuDbNo())
                .setSkuBridgeTableNo(dbInfo.getSkuBridgeNo())
                .setStockTableNo(dbInfo.getItemStockDbNo())
                .setItemWarnTableNo(dbInfo.getItemWarnDbNo())
                .setItemSupplierBridgeTableNo(dbInfo.getItemSupplierBridgeDbNo())
                .setUploadRuleTableNo(dbInfo.getStockUploadRuleDbNo());
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemList4Stock", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public List<ItemIdInfo> queryItemIdInfoList4Stock(Staff staff, Item4StockQueryParams params) {
        DbInfo dbInfo = staff.getDbInfo();
        params.setCompanyId(staff.getCompanyId())
                .setItemTableNo(dbInfo.getDmjItemDbNo())
                .setSkuTableNo(dbInfo.getSkuDbNo())
                .setSkuBridgeTableNo(dbInfo.getSkuBridgeNo())
                .setStockTableNo(dbInfo.getItemStockDbNo())
                .setItemWarnTableNo(dbInfo.getItemWarnDbNo())
                .setItemSupplierBridgeTableNo(dbInfo.getItemSupplierBridgeDbNo())
                .setUploadRuleTableNo(dbInfo.getStockUploadRuleDbNo());
        return (List<ItemIdInfo>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemIdInfoList4Stock", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateComponent(Staff staff, List<Long> sysItemIds, String component) {
        if (CollectionUtils.isEmpty(sysItemIds)) {
            return;
        }

        Map<String, Object> condition = initParams(staff);
        condition.put("sysItemIds", sysItemIds);
        condition.put("component", component);
        pureItemSkuBusiness.updateComponent(staff,sysItemIds,component);
        getSqlMapClientTemplate(staff).update("DmjItem.updateComponent", condition);
    }

    public List<Long> querySysItemIdsByParams(Staff staff, SysItemQueryParams params) {
        DbInfo dbInfo = staff.getDbInfo();
        params.setCompanyId(staff.getCompanyId())
                .setDmjItemTableNo(dbInfo.getDmjItemDbNo())
                .setSkuBridgeTableNo(dbInfo.getSkuBridgeNo())
                .setItemTableNo(dbInfo.getItemDbNo())
                .setSkuTableNo(dbInfo.getSkuDbNo());
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.querySysItemIdsByParams", params);
    }

    /**
     * 标记商品为赠品状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMakeGiftBySysItemIds(Staff staff, List<Long> sysItemIdList, int makeGift) {
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return;
        }
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIdList", sysItemIdList);
        params.put("makeGift", makeGift);
        pureItemSkuBusiness.makeSku2Gift(staff, sysItemIdList);
        getSqlMapClientTemplate(staff).update("DmjItem.updateMakeGiftBySysItemIds", params);
    }

    public void updateBrandBatch(
            Staff staff, List<Long> sysItemIdList,String brandId,String brandName){
        if (CollectionUtils.isEmpty(sysItemIdList)) {
            return;
        }

        Map<String, Object> params = initParams(staff);
        params.put("sysItemIdList", sysItemIdList);
        params.put("brandId", brandId);
        params.put("brand", brandName);
        getSqlMapClientTemplate(staff).update("DmjItem.updateBrandBatch", params);
    }

    public void clearBrand(Staff staff, Long sysItemId) {
        if (sysItemId == null) {
            return;
        }
        Map<String, Object> params = initParams(staff);
        params.put("sysItemId", sysItemId);
        getSqlMapClientTemplate(staff).update("DmjItem.clearBrand", params);
    }

    public int queryItemCount(Staff staff) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("itemDbNo", staff.getDbInfo().getDmjItemDbNo());
        return (int) getSqlMapClientTemplate(staff).queryForObject("DmjItem.queryItemCount", params);
    }

    public List<SimpleItem> querySimpleItemList(Staff staff, Page page) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("itemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("page", page);
        return (List<SimpleItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.querySimpleItemList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public List<DmjItem> queryitemMagnifier(Staff staff, String outerId) {
        Map<String, Object> condition = initParams(staff);
        condition.put("outerId", outerId);
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.queryitemMagnifier", condition, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public void updateItemCatId(Staff staff, long sysItemId, String catId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("itemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("sysItemId", sysItemId);
        params.put("catId", catId);

        getSqlMapClientTemplate(staff).update("DmjItem.updateItemCatId", params);
    }

    public List<String> checkOuterIdInItemAndSku(Staff staff, List<String> outerIdList) {
        if (CollectionUtils.isEmpty(outerIdList)) {
            return Lists.newArrayList();
        }
        Map<String, Object> params = initParams(staff);
        params.put("outerIdList", outerIdList);
        params.put("dmjSkuDbNo", staff.getDbInfo().getDmjSkuDbNo());
        return (List<String>) getSqlMapClientTemplate(staff).queryForList("DmjItem.checkOuterIdInItemAndSku", params);
    }

    public int queryItemListCount(Staff staff, ItemListQueryParams params) {
        DbInfo dbInfo = staff.getDbInfo();
        params.setCompanyId(staff.getCompanyId())
                .setDmjItemTableNo(dbInfo.getDmjItemDbNo())
                .setSkuBridgeTableNo(dbInfo.getSkuBridgeNo())
                .setItemSupplierBridgeTableNo(dbInfo.getItemSupplierBridgeDbNo())
                .setUploadRuleTableNo(dbInfo.getStockUploadRuleDbNo())
                .setPushRecordDbNo(dbInfo.getDmsItemPushRecordDbNo());
        return (int) getSqlMapClientTemplate(staff).queryForObject("DmjItem.queryItemListCount", params);
    }

    public List<DmjItem> queryAllItemList(Staff staff, QueryAllItemListParams params) {
        DbInfo dbInfo = staff.getDbInfo();
        params.setCompanyId(staff.getCompanyId())
                .setDmjItemTableNo(dbInfo.getDmjItemDbNo());
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryAllItemList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public List<DmjItem> queryItemList(Staff staff, ItemListQueryParams params) {
        DbInfo dbInfo = staff.getDbInfo();
        params.setCompanyId(staff.getCompanyId())
                .setDmjItemTableNo(dbInfo.getDmjItemDbNo())
                .setSkuBridgeTableNo(dbInfo.getSkuBridgeNo())
                .setItemSupplierBridgeTableNo(dbInfo.getItemSupplierBridgeDbNo())
                .setUploadRuleTableNo(dbInfo.getStockUploadRuleDbNo())
                .setPushRecordDbNo(dbInfo.getDmsItemPushRecordDbNo());
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public Map<String, Long> queryOuterIdSysIdMapByOuterIds(Staff staff, List<String> outerIds) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("dmjItemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("dmjSkuDbNo", staff.getDbInfo().getDmjSkuDbNo());
        params.put("outerIds", outerIds);

        return getSqlMapClientTemplate(staff).queryForMap("DmjItem.queryOuterIdSysIdMapByOuterIds", params,"outerId","sysId");
    }

    public void updateSalePrice(Staff staff, List<Long> sysItemIds, Double salePrice) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemTableNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        params.put("salePrice", salePrice);
        getSqlMapClientTemplate(staff).update("DmjItem.updateSalePrice", params);
    }

    public void updateItem4ListV2(Staff staff, List<Long> sysItemIds, DmjItem dmjItem) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemTableNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        params.put("dmjItem", dmjItem);
        getSqlMapClientTemplate(staff).update("DmjItem.updateItem4ListV2", params);
    }


    public void updateSysItemWeight(Staff staff, List<Long> sysItemIds, Double weight) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemTableNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        params.put("weight", weight);
        getSqlMapClientTemplate(staff).update("DmjItem.updateSysItemWeight", params);
    }


    public Map<Long, String> queryOuterIdMapBySysItemIds(Staff staff, List<Long> sysItemIds) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("itemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("sysItemIds", sysItemIds);

        return getSqlMapClientTemplate(staff).queryForMap("DmjItem.queryOuterIdMapBySysItemIds", params,"sysItemId","outerId");
    }

    public List<DmjItem> queryPureItemList(Staff staff, Page page) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("itemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("page", page);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryPureItemList", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }
    /**
     *
     * @param staff
     * @param sysItemId
     * @param historyPriceImport
     */
    public void updateHistoryPriceImport(Staff staff,Long sysItemId,String historyPriceImport){
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemTableNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemId", sysItemId);
        params.put("historyPriceImport", historyPriceImport);
        getSqlMapClientTemplate(staff).update("DmjItem.updateHistoryPriceImport", params);
    }

    public List<Long> queryValidSysItemIds(Staff staff, List<Long> sysItemIds) {
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIds", sysItemIds);
        return (List<Long>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryValidSysItemIds", params);
    }

    public void batchUpdateRemark(Staff staff, List<Long> sysItemIds, String remark) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemTableNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        params.put("remark", remark);
        getSqlMapClientTemplate(staff).update("DmjItem.batchUpdateRemark", params);
    }

    public List<Long> stockInOutRecordQueryCount(Staff staff, StockInOutRecordV2QueryParams stockInOutRecordQueryParams) {
        DbInfo dbInfo = staff.getDbInfo();
        stockInOutRecordQueryParams.setCompanyId(staff.getCompanyId())
                .setRecordTableNo(dbInfo.getStockInOutRecordDbNo())
                .setItemTableNo(dbInfo.getDmjItemDbNo())
                .setSkuTableNo(dbInfo.getDmjSkuDbNo())
                .setItemSupplierBridgeTableNo(dbInfo.getItemSupplierBridgeDbNo());
        return (List<Long>) getSqlMapClientTemplate(staff).queryForList("DmjItem.stockInOutRecordQueryCount", stockInOutRecordQueryParams);
    }

    public int updateIntegral(Staff staff, DmjItem dmjItem) {
        init(staff, dmjItem);
        return getSqlMapClientTemplate(staff).update("DmjItem.update", dmjItem);
    }

    /**
     * 查询和积分相关的商品信息
     * @param staff
     * @param sysItemIds
     * @return
     */
    public List<DmjItem> queryIntegralDataBySysItemIds(Staff staff,List<Long> sysItemIds){
        if (CollectionUtils.isEmpty(sysItemIds)) {
            return Collections.emptyList();
        }
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIds", sysItemIds);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryIntegralDataBySysItemIds", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public int countNotSkuItemByItemIds(Staff staff, List<Long> sysItemIds) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("dbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        return (Integer) getSqlMapClientTemplate(staff).queryForObject("DmjItem.countNotSkuItemByItemIds", params);
    }

    public List<DmjItem> queryPureItemByOuterIds4Fms(Staff staff, List<String> outerIds) {
        Map<String, Object> params = initParams(staff);
        params.put("outerIds", outerIds);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryPureItemByOuterIds4Fms", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public List<DmjItem> queryItemBySysItemIds4Fms(Staff staff, List<Long> sysItemIds) {
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIds", sysItemIds);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemBySysItemIds4Fms", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public Map<Long, String> queryItemTypeByIds(Staff staff, List<Long> sysItemIds) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("dbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        return getSqlMapClientTemplate(staff).queryForMap("DmjItem.queryItemTypeByIds", params, "sys_item_id", "type");
    }

    public List<Long> querySysItemIds4CheckExist(Staff staff, List<Long> sysItemIds) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("dbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        return  getSqlMapClientTemplate(staff).queryForList("DmjItem.querySysItemIds4CheckExist", params);
    }

    public void updateWholesalePrice(Staff staff, List<Long> sysItemIds, Double wholesalePrice) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemTableNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        params.put("wholesalePrice", wholesalePrice);
        getSqlMapClientTemplate(staff).update("DmjItem.updateWholesalePrice", params);
    }

    public List<Long> queryIsAutoCalculateSysItemIds(Staff staff, List<Long> sysItemIds, String autoCalculateColumn) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemTableNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemIds", sysItemIds);
        params.put("autoCalculateColumn", autoCalculateColumn);
        return (List<Long>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryIsAutoCalculateSysItemIds", params);
    }

    public List<DmjItem> queryPureItemCatIdAndCidsByOuterIds(Staff staff, List<String> outerIds) {
        Map<String, Object> params = initParams(staff);
        params.put("outerIds", outerIds);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryPureItemCatIdAndCidsByOuterIds", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public String queryOuterIdBySysItemId(Staff staff, long sysItemId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemTableNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("sysItemId", sysItemId);
        return (String) getSqlMapClientTemplate(staff).queryForObject("DmjItem.queryOuterIdBySysItemId", params);
    }

    public Map<Long, String> queryOuterIdByPage(Staff staff, Page page) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("itemTableNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("page", page);
        return getSqlMapClientTemplate(staff).queryForMap("DmjItem.queryOuterIdByPage", params, "sys_item_id", "outer_id");
    }

    public List<Long> querySysItemIds4Stock(Staff staff, Item4StockQueryParams params) {
        DbInfo dbInfo = staff.getDbInfo();
        params.setCompanyId(staff.getCompanyId())
                .setItemTableNo(dbInfo.getDmjItemDbNo());
        return (List<Long>) getSqlMapClientTemplate(staff).queryForList("DmjItem.querySysItemIds4Stock", params);
    }

    public List<DmjItem> queryItemList4ProcessData(Staff staff,long minSysItemId, int pageSize){
        Map<String, Object> params = initParams(staff);
        params.put("minSysItemId", minSysItemId);
        params.put("pageSize", pageSize);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryItemList4ProcessData", params);
    }

    public List<DmjItem> queryPureItemBySysItemIds(Staff staff, List<Long> sysItemIds) {
        Map<String, Object> params = initParams(staff);
        params.put("sysItemIds", sysItemIds);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryPureItemBySysItemIds", params, SqlExecutor.NO_SKIPPED_RESULTS, BaseConstants.ITEM_MAX_RESULTS);
    }

    public List<Long> querySysItemIdList(Staff staff, ItemListQueryParams params) {
        DbInfo dbInfo = staff.getDbInfo();
        params.setCompanyId(staff.getCompanyId())
                .setDmjItemTableNo(dbInfo.getDmjItemDbNo())
                .setSkuBridgeTableNo(dbInfo.getSkuBridgeNo())
                .setItemSupplierBridgeTableNo(dbInfo.getItemSupplierBridgeDbNo())
                .setUploadRuleTableNo(dbInfo.getStockUploadRuleDbNo())
                .setPushRecordDbNo(dbInfo.getDmsItemPushRecordDbNo());
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.querySysItemIdList", params);
    }

    public List<DmjItem> queryDmjItemListWithOuterIdAndExtendFieldValues(Staff staff, List<Long> sysItemList){
        if(CollectionUtils.isEmpty(sysItemList)){
            return Collections.emptyList();
        }
        Map<String, Object> params = initParams(staff);
        params.put("sysItemList",sysItemList);
        return (List<DmjItem>) getSqlMapClientTemplate(staff).queryForList("DmjItem.queryDmjItemListWithOuterIdAndExtendFieldValues", params);
    }

    public List<DmjItem> query4TestMockLimit5(Staff staff) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("dbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("companyId", staff.getCompanyId());
        return getSqlMapClientTemplate(staff).queryForList("DmjItem.query4TestMockLimit5", params);
    }

    //根据商家编码查询纯套件
    public DmjItem getPureSuiteDmjItemByOuterId(Staff staff, String outerId) {
        if (StringUtils.isEmpty(outerId)) {
            return null;
        }
        Map<String, Object> params = initParams(staff);
        params.put("outerId", outerId);
        List<DmjItem> dmjItems = getSqlMapClientTemplate(staff).queryForList("DmjItem.getPureSuiteDmjItemByOuterId", params);
        if (CollectionUtils.isEmpty(dmjItems)) {
            return null;
        }
        return dmjItems.get(0);
    }
    public int queryItemCountByCatId(Staff staff, List<String> catIds) {
        Map<String, Object> params = initParams(staff);
        params.put("companyId", staff.getCompanyId());
        params.put("itemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("catIds", catIds);
        return (Integer) getSqlMapClientTemplate(staff).queryForObject("DmjItem.queryItemCountByCatId", params);
    }
}
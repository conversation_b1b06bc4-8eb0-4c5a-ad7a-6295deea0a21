<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="DmjItem">
    <!-- Alias Map Defined -->
    <typeAlias alias="DmjItemQuery" type="com.raycloud.dmj.domain.item.ItemQueryParams"/>
    <typeAlias alias="QueryAllItemListParams" type="com.raycloud.dmj.domain.item.params.QueryAllItemListParams"/>
    <typeAlias alias="Item4StockQueryParams"
               type="com.raycloud.dmj.domain.item.params.Item4StockQueryParams"/>
    <typeAlias alias="SysItemQueryParams"
               type="com.raycloud.dmj.domain.item.params.SysItemQueryParams"/>
    <typeAlias alias="ItemListQueryParams"
               type="com.raycloud.dmj.domain.item.params.ItemListQueryParams"/>
    <typeAlias alias="ParamsKitDmjItem" type="com.raycloud.dmj.domain.sku.ParamsKit"/>
    <typeAlias alias="DmjItem" type="com.raycloud.dmj.domain.item.DmjItem"/>
    <typeAlias alias="ItemStockMixVo" type="com.raycloud.dmj.domain.stock.FarERPStock"/>
    <typeAlias alias="ItemSkuReportModel" type="com.raycloud.dmj.web.model.items.ItemSkuReportModel"/>
    <typeAlias alias="DmjSku" type="com.raycloud.dmj.domain.sku.DmjSku"/>
    <typeAlias alias="ItemSkuBatch" type="com.raycloud.dmj.domain.item.ItemSkuBatch"/>
    <typeAlias alias="SimpleItem" type="com.raycloud.dmj.domain.item.SimpleItem"/>
    <typeAlias alias="ItemIdInfo" type="com.raycloud.dmj.domain.item.ItemIdInfo"/>

    <resultMap id="itemSkuReportModel" class="ItemSkuReportModel">
        <result property="outerId" column="outer_id"/>
        <result property="title" column="title"/>
        <result property="picPath" column="pic_path"/>
        <result property="sysItemId" column="sys_item_id"/>
        <result property="sysSkuId" column="sys_sku_id"/>
        <result property="propertiesName" column="properties_name"/>
        <result property="remark" column="remark"/>
        <result property="propertiesAlias" column="properties_alias"/>
    </resultMap>

    <resultMap id="itemStockMixVo" class="ItemStockMixVo">
        <result property="sysItemId" column="sys_item_id"/>
        <result property="sysSkuId" column="sys_sku_id"/>
        <result property="title" column="title"/>
        <result property="shortTitle" column="short_title"/>
        <result property="outerId" column="outer_id"/>
        <result property="skuOuterId" column="sku_outer_id"/>
        <result property="propertiesName" column="properties_name"/>
        <result property="propertiesAlias" column="properties_alias"/>
        <result property="remark" column="remark"/>
        <result property="skuRemark" column="sku_remark"/>
        <result property="picPath" column="pic_path"/>
        <result property="skuPicPath" column="sku_pic_path"/>
        <result property="propColor" column="prop_color"/>
        <result property="propOther" column="prop_other"/>
    </resultMap>
    <resultMap id="skuStockMixVo" class="ItemStockMixVo">
        <result property="sysItemId" column="sys_item_id"/>
        <result property="sysSkuId" column="sys_sku_id"/>
        <result property="skuOuterId" column="sku_outer_id"/>
        <result property="propertiesName" column="properties_name"/>
        <result property="propertiesAlias" column="properties_alias"/>
        <result property="skuRemark" column="sku_remark"/>
        <result property="skuPicPath" column="sku_pic_path"/>
        <result property="propColor" column="prop_color"/>
        <result property="propOther" column="prop_other"/>
        <result property="brandName" column="brand_name"/>
        <result property="place" column="place"/>
        <result property="record" column="record"/>
        <result property="component" column="component"/>
        <result property="safekind" column="safekind"/>
        <result property="boxnum" column="boxnum"/>
        <result property="purchasePrice" column="purchase_price"/>
        <result property="unit" column="unit"/>

        <result property="bigPic" column="big_pic"/>
    </resultMap>

    <resultMap id="dmjItemMap" class="DmjItem">
        <result property="onSale" column="on_sale"/>
        <result property="companyId" column="company_id"/>
        <result property="shortTitle" column="short_title"/>
        <result property="market" column="market"/>
        <result property="costPrice" column="cost_price"/>
        <result property="weight" column="weight"/>
        <result property="barcode" column="barcode"/>
        <result property="brand" column="brand"/>
        <result property="brandId" column="brand_id"/>
        <result property="status" column="status"/>
        <result property="created" column="created"/>
        <result property="modified" column="modified"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="picPath" column="pic_path"/>
        <result property="title" column="title"/>
        <result property="outerId" column="outer_id"/>
        <result property="num" column="num"/>
        <result property="stockAlarmNum" column="stock_alarm_num"/>
        <result property="packmaAlarmNum" column="packma_alarm_num"/>
        <result property="packmaSurplus" column="packma_surplus"/>
        <result property="remark" column="remark"/>
        <result property="type" column="type"/>
        <result property="goodAllocation" column="goods_allocation"/>
        <result property="catId" column="cat_id"/>
        <result property="sellerCids" column="seller_cids"/>
        <result property="propsName" column="props_name"/>
        <result property="unit" column="unit"/>
        <result property="priceImport" column="purchase_price"/>
        <result property="priceOutput" column="selling_price"/>
        <result property="wholesalePrice" column="wholesale_price"/>
        <result property="x" column="x"/>
        <result property="y" column="y"/>
        <result property="z" column="z"/>
        <result property="periodCast" column="period_cast"/>
        <result property="place" column="place"/>
        <result property="age" column="age"/>
        <result property="sysItemId" column="sys_item_id"/>
        <result property="defined1" column="defined_1"/>
        <result property="defined1Key" column="defined_1_key"/>
        <result property="defined2" column="defined_2"/>
        <result property="defined2Key" column="defined_2_key"/>
        <result property="defined3" column="defined_3"/>
        <result property="defined3Key" column="defined_3_key"/>
        <result property="defined4" column="defined_4"/>
        <result property="defined4Key" column="defined_4_key"/>
        <result property="defined5" column="defined_5"/>
        <result property="defined5Key" column="defined_5_key"/>
        <result property="defined6" column="defined_6"/>
        <result property="defined6Key" column="defined_6_key"/>
        <result property="mergeType" column="merge_type"/>
        <result property="activeStatus" column="active_status"/>
        <result property="hasInventory" column="has_inventory"/>
        <result property="overSell" column="over_sell"/>
        <result property="alarmed" column="alarmed"/>
        <result property="hasSetAlarm" column="has_set_alarm"/>
        <result property="hasNotSetAlarm" column="has_not_set_alarm"/>
        <result property="isStockNormal" column="is_stock_normal"/>
        <result property="productionDate" column="production_date"/>
        <result property="isSkuItem" column="is_sku_item"/>
        <result property="shipper" column="shipper"/>
        <result property="isVirtual" column="is_virtual"/>
        <result property="hasSupplier" column="has_supplier"/>
        <result property="isSysWeight" column="is_sys_weight"/>
        <result property="declareNameZh" column="declare_name_zh"/>
        <result property="declareNameEn" column="declare_name_en"/>
        <result property="hsCode" column="hs_code"/>
        <result property="declareAmount" column="declare_amount"/>
        <result property="declareWeight" column="declare_weight"/>
        <result property="hasBatch" column="has_batch"/>
        <result property="batchRule" column="batch_rule"/>
        <result property="isSysPriceImport" column="is_sys_purchase_price"/>
        <result property="isSysPriceOutput" column="is_sys_selling_price"/>
        <result property="isSysWholesalePrice" column="is_sys_wholesale_price"/>
        <result property="weightAvgPrice" column="weight_avg_price"/>
        <result property="nearDate" column="near_date"/>
        <result property="typeTag" column="type_tag"/>
        <result property="definedJson" column="defined_json"/>
        <result property="component" column="component"/>
        <result property="standard" column="standard"/>
        <result property="platformId" column="platform_id"/>
        <result property="oneIntegral" column="one_integral"/>
        <result property="moreIntegral" column="more_integral"/>
        <result property="onePackageIntegral" column="one_package_integral"/>
        <result property="morePackageIntegral" column="more_package_integral"/>
        <result property="oneInspectionIntegral" column="one_inspection_integral"/>
        <result property="moreInspectionIntegral" column="more_inspection_integral"/>
        <result property="oneWeightIntegral" column="one_weight_integral"/>
        <result property="moreWeightIntegral" column="more_weight_integral"/>
        <result property="record" column="record"/>
        <result property="safekind" column="safekind"/>
        <result property="boxnum" column="boxnum"/>
        <result property="invoice" column="invoice"/>
        <result property="makeGift" column="make_gift"/>
        <result property="outerIdPure" column="outer_id_pure"/>
        <result property="bigPic" column="big_pic"/>
        <result property="extendFieldValues" column="extend_field_values"/>
        <result property="extendRelationInfo" column="extend_relation_Info" />
        <result property="listTime" column="list_time"/>
        <result property="specificationJson" column="specification_json"/>
    </resultMap>

    <resultMap id="dmjItemStock" extends="dmjItemMap" class="DmjItem">
        <result property="availableInStock" column="available_in_stock"/>
    </resultMap>

    <resultMap id="simpleMap" class="DmjItem">
        <result property="sysItemId" column="sys_item_id"/>
        <result property="companyId" column="company_id"/>
        <result property="status" column="status"/>
        <result property="outerId" column="outer_id"/>
        <result property="type" column="type"/>
        <result property="activeStatus" column="active_status"/>
        <result property="isSkuItem" column="is_sku_item"/>
        <result property="title" column="title"/>
        <result property="picPath" column="pic_path"/>
        <result property="remark" column="remark"/>
        <result property="shortTitle" column="short_title"/>
    </resultMap>

    <resultMap id="simpleMapForMatchD" class="DmjItem">
        <result property="sysItemId" column="sys_item_id"/>
        <result property="companyId" column="company_id"/>
        <result property="status" column="status"/>
        <result property="outerId" column="outer_id"/>
        <result property="type" column="type"/>
        <result property="activeStatus" column="active_status"/>
        <result property="isSkuItem" column="is_sku_item"/>
        <result property="outerIdPure" column="outer_id_pure"/>
    </resultMap>

    <resultMap id="dmjSkuMap" class="DmjSku">
        <result property="sysSkuId" column="sys_sku_id"/>
        <result property="propertiesName" column="properties_name"/>
        <result property="propertiesAlias" column="properties_alias"/>
        <result property="companyId" column="company_id"/>
        <result property="sysItemId" column="sys_item_id"/>
        <result property="shortTitle" column="short_title"/>
        <result property="costPrice" column="cost_price"/>
        <result property="weight" column="weight"/>
        <result property="barcode" column="barcode"/>
        <result property="picPath" column="pic_path"/>
        <result property="skuPicPath" column="sku_pic_path"/>
        <result property="remark" column="remark"/>
        <result property="unit" column="unit"/>
        <result property="outerId" column="outer_id"/>
        <result property="skuOuterId" column="sku_outer_id"/>
        <result property="hasBatch" column="has_batch"/>
        <result property="batchRule" column="batch_rule"/>
        <result property="title" column="title"/>
        <result property="nearDate" column="near_date"/>
        <result property="priceOutput" column="selling_price"/>
        <result property="record" column="record"/>
        <result property="safekind" column="safekind"/>
        <result property="boxnum" column="boxnum"/>
        <result property="bigPic" column="big_pic"/>
    </resultMap>

    <resultMap id="itemSkuBatch" class="ItemSkuBatch">
        <result property="key" column="key"/>
        <result property="sysItemId" column="sys_item_id"/>
        <result property="sysSkuId" column="sys_sku_id"/>
        <result property="hasBatch" column="has_batch"/>
        <result property="periodCast" column="period_cast"/>
        <result property="batchRule" column="batch_rule"/>
        <result property="nearDate" column="near_date"/>
        <result property="hasProduct" column="has_product"/>
        <result property="allowSaleExpiring" column="allow_sale_expiring"/>
    </resultMap>

    <resultMap id="platformIdMap" class="DmjItem">
        <result property="sysItemId" column="sys_item_id"/>
        <result property="platformId" column="platform_id"/>
    </resultMap>

    <resultMap id="importSimpleItem" class="DmjItem" >
        <result property="sysItemId" column="sys_item_id"/>
        <result property="outerId" column="outer_id"/>
        <result property="type" column="type"/>
        <result property="typeTag" column="type_tag"/>
    </resultMap>

    <resultMap id="importSimpleItem" class="DmjItem" >
        <result property="sysItemId" column="sys_item_id"/>
        <result property="outerId" column="outer_id"/>
        <result property="type" column="type"/>
        <result property="typeTag" column="type_tag"/>
    </resultMap>

    <sql id="joinSumStock">
        <isEqual property="itemQueryParams.needJoinSumStock" compareValue="true">
            left join (
                select sys_item_id
                <isEqual prepend="," property="itemQueryParams.needSumDefectiveField" compareValue="true">
                    sum(defective_stock) defectiveStock
                </isEqual>
                <isEqual prepend="," property="itemQueryParams.needSumAvailableField" compareValue="true">
                    sum(available_in_stock) availableInStock
                </isEqual>
                from stock_$dbInfo.itemStockDbNo$
                where enable_status = 1 and company_id = #companyId# and sys_sku_id = 0
                <isNotEmpty property="itemQueryParams.warehouseId">
                    and ware_house_id = #itemQueryParams.warehouseId#
                </isNotEmpty>
                group by sys_item_id
            ) sumStock on item.sys_item_id = sumStock.sys_item_id
        </isEqual>
    </sql>

    <sql id="joinItemWarn">
        <isEqual property="itemQueryParams.needJoinItemWarn" compareValue="true">
            LEFT JOIN item_warn_#dbInfo.itemWarnDbNo# warn
            ON warn.company_id = #companyId#
            AND warn.sys_item_id = item.sys_item_id
            AND warn.sys_sku_id = 0
            <isNotNull property="itemQueryParams.warehouseId">
                AND warn.warehouse_id = #itemQueryParams.warehouseId#
            </isNotNull>
            <isNull property="itemQueryParams.warehouseId">
                AND warn.warehouse_id = 0
            </isNull>
        </isEqual>
    </sql>

    <sql id="joinSumStockWhere">
        <isEqual property="itemQueryParams.needJoinSumStock" compareValue="true">
            <isNotEmpty prepend="and" property="itemQueryParams.minDefectiveStock">
                sumStock.defectiveStock >= #itemQueryParams.minDefectiveStock#
            </isNotEmpty>
            <isNotEmpty prepend="and" property="itemQueryParams.maxDefectiveStock">
                sumStock.defectiveStock &lt;= #itemQueryParams.maxDefectiveStock#
            </isNotEmpty>
            <isNotEmpty prepend="and" property="itemQueryParams.minAvailableStock">
                sumStock.availableInStock >= #itemQueryParams.minAvailableStock#
            </isNotEmpty>
            <isNotEmpty prepend="and" property="itemQueryParams.maxAvailableStock">
                sumStock.availableInStock &lt;= #itemQueryParams.maxAvailableStock#
            </isNotEmpty>

            <isNotEmpty property="itemQueryParams.stockStatusList" prepend=" AND ">
                <iterate property="itemQueryParams.stockStatusList" open="(" close=")" conjunction="OR">
                    <!--正常-->
                    <isEqual property="itemQueryParams.stockStatusList[]" compareValue="1">
                        (sumStock.availableInStock > 0 AND (warn.stock_down IS NULL OR sumStock.availableInStock >= warn.stock_down))
                    </isEqual>

                    <!--缺货（警戒） 设置预警 且 0<实际可用数<预警值 -->
                    <isEqual property="itemQueryParams.stockStatusList[]" compareValue="2">
                        (sumStock.availableInStock > 0 AND warn.stock_down IS NOT NULL AND sumStock.availableInStock &lt; warn.stock_down)
                    </isEqual>

                    <!--无库存-->
                    <isEqual property="itemQueryParams.stockStatusList[]" compareValue="3">
                        (sumStock.availableInStock IS NULL OR sumStock.availableInStock = 0)
                    </isEqual>

                    <!--超卖-->
                    <isEqual property="itemQueryParams.stockStatusList[]" compareValue="4">
                        sumStock.availableInStock &lt; 0
                    </isEqual>
                </iterate>
            </isNotEmpty>
        </isEqual>
    </sql>

    <sql id="paramsFlag">
        <isNotEmpty property="params.flag">
            <isEqual property="params.flag" compareValue="0">
                and item.type = '0' and item.is_virtual = 0
            </isEqual>
            <isEqual property="params.flag" compareValue="1">
                and item.type IN ('1', '2')
            </isEqual>
            <isEqual property="params.flag" compareValue="3">
                and item.type = '0' and item.is_virtual = 1
            </isEqual>
            <isEqual property="params.flag" compareValue="6">
                and item.type = '0' and item.type_tag in (1,2)
            </isEqual>
            <isEqual property="params.flag" compareValue="7">
                and item.type = '0' and item.is_virtual = 0 and item.type_tag = 0
            </isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="8">
                and item.type != '3'
            </isEqual>

            <isEqual property="params.flag" compareValue="9">
                and item.type = '0' and item.type_tag in (3,4)
            </isEqual>
            <isEqual property="params.flag" compareValue="10">
                and item.type = '0'
            </isEqual>
        </isNotEmpty>
    </sql>
    <!-- 参数不一致，这里写两份，更新也两份一起更新。后续改一下参数，可以去掉-->
    <sql id="itemQueryParamsFlag">
        <isNotEmpty property="itemQueryParams.flag">
            <isEqual property="itemQueryParams.flag" compareValue="1">
                and item.type IN('1' ,'2') and item.is_virtual = 0
            </isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="0">
                and item.type = '0'
            </isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="4">
                and item.type IN('0' ,'1', '2')
            </isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="5">
                and item.type = '2'
            </isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="6">
                and item.type = '0' and item.type_tag in (1,2)
            </isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="7">
                and item.type= '0' and item.is_virtual = 0 and item.type_tag = 0
            </isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="8">
                and item.type != '3'
            </isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="9">
                and item.type = '0' and item.type_tag in (3,4)
            </isEqual>
        </isNotEmpty>
    </sql>

    <sql id="simpleMapColumn">
        di.company_id,
        di.sys_item_id,
        di.outer_id,
        di.title,
        di.is_sku_item,
        di.pic_path,
        di.remark,
        di.short_title,
        di.type,
        di.status,
        di.active_status
    </sql>

    <!-- 根据商品名称、编码、备注 全模糊查询 -->
    <sql id="cmPageQuery.ItemCodes">
        <isNotEmpty property="itemQueryParams.itemCodes" prepend="AND" open="(" close=")">
            item.outer_id like CONCAT('%', #itemQueryParams.itemCodes#, '%')
            or item.title like CONCAT('%', #itemQueryParams.itemCodes#, '%')
            or item.remark like CONCAT('%', #itemQueryParams.itemCodes#, '%')
        </isNotEmpty>
    </sql>

    <!-- 平铺的组合查询条件 -->
    <sql id="querySysItemTilePageWhere">
        <isNotEmpty property="itemQueryParams.tileItemName" prepend="AND">
            item.title like CONCAT('%', #itemQueryParams.tileItemName#, '%')
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tileOuterId" prepend="AND">
            item.outer_id like CONCAT('%', #itemQueryParams.tileOuterId#, '%')
        </isNotEmpty>
        <isNotNull property="itemQueryParams.tileOuterIdList" prepend="AND">
            item.outer_id IN
            <iterate property="itemQueryParams.tileOuterIdList" open="(" conjunction="," close=")">
            #itemQueryParams.tileOuterIdList[]#
            </iterate>
        </isNotNull>
        <isNotEmpty property="itemQueryParams.tileSkuOuterId" prepend="AND">
            item.is_sku_item = 0 and item.outer_id like CONCAT('%', #itemQueryParams.tileSkuOuterId#, '%')
        </isNotEmpty>
        <isNotNull property="itemQueryParams.tileSkuOuterIdList" prepend="AND">
            item.is_sku_item = 0 and item.outer_id IN
            <iterate property="itemQueryParams.tileSkuOuterIdList" open="(" conjunction="," close=")">
                #itemQueryParams.tileSkuOuterIdList[]#
            </iterate>
        </isNotNull>
        <isNotEmpty property="itemQueryParams.tileAllOuterId" prepend="AND">
            item.outer_id like CONCAT('%', #itemQueryParams.tileAllOuterId#, '%')
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tileShortTitle" prepend="AND">
            item.short_title like CONCAT('%',#itemQueryParams.tileShortTitle#,'%')
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tileRemark" prepend="AND">
            item.remark like CONCAT('%', #itemQueryParams.tileRemark#, '%')
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tileSkuAlias" prepend="AND">
            1=2
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tileSkuRemark" prepend="AND">
            1=2
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tilePropertiesName" prepend="AND">
            1=2
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tileSkuOuterIdList" prepend="AND">
            item.is_sku_item = 0 and item.outer_id in
            <iterate property="itemQueryParams.tileSkuOuterIdList" open="(" conjunction="," close=")">
                #itemQueryParams.tileSkuOuterIdList[]#
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="itemQueryParams.multiCode" prepend="AND">
            (item.barcode like CONCAT('%',#itemQueryParams.multiCode#,'%') OR item.outer_id like CONCAT('%',#itemQueryParams.multiCode#,'%'))
        </isNotEmpty>

        <!-- 是否赠品 -->
        <isNotNull property="itemQueryParams.gift">
            <isEqual property="itemQueryParams.gift" compareValue="true">
                and item.make_gift = 1
            </isEqual>
            <isEqual property="itemQueryParams.gift" compareValue="false">
                and item.make_gift = 0
            </isEqual>
        </isNotNull>

        <isNotEmpty property="itemQueryParams.tileBrandList" prepend="AND">
            item.brand in
            <iterate property="itemQueryParams.tileBrandList" open="(" conjunction="," close=")">
                #itemQueryParams.tileBrandList[]#
            </iterate>
        </isNotEmpty>

        <isNotNull property="itemQueryParams.startDate">
            <isNotNull property="itemQueryParams.endDate" prepend=" AND ">
                item.created between #itemQueryParams.startDate# and #itemQueryParams.endDate#
            </isNotNull>
        </isNotNull>

        <!--以下非页面查询条件，是采购那边单独调接口使用的条件-->
        <isNotNull property="itemQueryParams.tileSysItemIds" prepend="AND">
            item.is_sku_item = 0 and item.sys_item_id in
            <iterate property="itemQueryParams.tileSysItemIds" open="(" conjunction="," close=")">
                #itemQueryParams.tileSysItemIds[]#
            </iterate>
        </isNotNull>
    </sql>

    <!-- 平铺的组合查询条件 -->
    <sql id="where4SearchTile">
        <isNotEmpty property="itemQueryParams.tileItemName" prepend="AND">
            item.title like CONCAT('%', #itemQueryParams.tileItemName#, '%')
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tileOuterId" prepend="AND">
            (item.outer_id like CONCAT('%', #itemQueryParams.tileOuterId#, '%') OR item.sys_item_id in ( select distinct
            sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
            where sku.enable_status=1 and sku.company_id=#split.companyId# and sku.outer_id like CONCAT('%',
            #itemQueryParams.tileOuterId#, '%') ))
        </isNotEmpty>
        <isNotNull property="itemQueryParams.tileOuterIdList" prepend="AND">
            ((item.outer_id IN
            <iterate property="itemQueryParams.tileOuterIdList" open="(" conjunction="," close=")">
                #itemQueryParams.tileOuterIdList[]#
            </iterate>) OR item.sys_item_id in ( select distinct
            sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
            where sku.enable_status=1 and sku.company_id=#split.companyId# and sku.outer_id IN
            <iterate property="itemQueryParams.tileOuterIdList" open="(" conjunction="," close=")">
                #itemQueryParams.tileOuterIdList[]#
            </iterate> ))
        </isNotNull>

        <isNotEmpty property="itemQueryParams.tileBrandList" prepend="AND">
            (
            item.brand in
            <iterate property="itemQueryParams.tileBrandList" open="(" conjunction="," close=")">
                #itemQueryParams.tileBrandList[]#
            </iterate>
            OR
            item.sys_item_id in (
            select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
            where sku.enable_status=1 and sku.company_id=#companyId# and sku.brand in
            <iterate property="itemQueryParams.tileBrandList" open="(" conjunction="," close=")">
                #itemQueryParams.tileBrandList[]#
            </iterate>
            )
            )
        </isNotEmpty>


        <isNotEmpty property="itemQueryParams.tileShortTitle" prepend="AND">
            item.short_title like CONCAT('%',#itemQueryParams.tileShortTitle#,'%')
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tileRemark" prepend="AND">
            item.remark like CONCAT('%', #itemQueryParams.tileRemark#, '%')
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tileSkuAlias" prepend="AND">
            exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
            sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.properties_alias like CONCAT('%',
            #itemQueryParams.tileSkuAlias#, '%'))
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tileSkuRemark" prepend="AND">
            exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
            sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.remark like CONCAT('%',
            #itemQueryParams.tileSkuRemark#, '%'))
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.tilePropertiesName" prepend="AND">
            exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
            sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.properties_name like CONCAT('%',
            #itemQueryParams.tilePropertiesName#, '%'))
        </isNotEmpty>

        <isNotEmpty property="itemQueryParams.tileSkuOuterId" prepend="AND">
            exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
            sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.outer_id like CONCAT('%',
            #itemQueryParams.tileSkuOuterId#, '%'))
        </isNotEmpty>

        <isEqual property="itemQueryParams.filterSupplierAuth" compareValue="true" open="(" close=")" prepend=" AND ">
            <isNotEmpty property="itemQueryParams.tileSupplierIdList">
                item.sys_item_id in (select supplier.sys_item_id from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier
                where supplier.company_id=#split.companyId# and supplier.sys_item_id=item.sys_item_id
                and supplier.supplier_id in
                <iterate property="itemQueryParams.tileSupplierIdList" open="(" conjunction="," close=")">
                    #itemQueryParams.tileSupplierIdList[]#
                </iterate>
                )
            </isNotEmpty>

            <isEqual property="itemQueryParams.includeNoSupplier" compareValue="true">
                <isNotEmpty property="itemQueryParams.tileSupplierIdList">
                    OR
                </isNotEmpty>
                item.has_supplier = 0
            </isEqual>
        </isEqual>

        <isNotEmpty property="itemQueryParams.tileSupplierItemOuterId" prepend="AND">
            item.sys_item_id in (
            select supplier.sys_item_id from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier
            where supplier.company_id=#split.companyId#
            and supplier.sys_item_id=item.sys_item_id
            and supplier.supplier_item_outer_id like CONCAT('%', #itemQueryParams.tileSupplierItemOuterId#, '%')
            )
        </isNotEmpty>

        <isNotEmpty property="itemQueryParams.multiCode" prepend="AND">
            (item.barcode like CONCAT('%',#itemQueryParams.multiCode#,'%') OR item.outer_id like CONCAT('%',#itemQueryParams.multiCode#,'%'))
        </isNotEmpty>
    </sql>

    <sql id="querySysItemPageWhere">
        where item.company_id = #companyId#
        <isNotEmpty property="itemQueryParams.hasSalePrice">
            <isEqual property="itemQueryParams.hasSalePrice" prepend="and" compareValue="1">
                exists (select 1 from item_sale_price_bridge_#dbInfo.itemSalePriceBrigeDbNo# isp where
                isp.company_id=#companyId# and isp.sys_item_id=item.sys_item_id)
            </isEqual>
            <isEqual property="itemQueryParams.hasSalePrice" prepend="and" compareValue="0">
                not exists (select 1 from item_sale_price_bridge_#dbInfo.itemSalePriceBrigeDbNo# isp where
                isp.company_id=#companyId# and isp.sys_item_id=item.sys_item_id)
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.text" prepend="AND" open="(" close=")">
            <isEqual property="itemQueryParams.searchType" compareValue="0">
                <!-- 商家编码 -->
                <isEqual property="itemQueryParams.queryType" compareValue="outerId">item.outer_id like CONCAT('%',
                    #itemQueryParams.text#, '%')
                </isEqual>
                <!-- 主商家编码 -->
                <isEqual property="itemQueryParams.queryType" compareValue="mainOuterId">item.outer_id like CONCAT('%',
                    #itemQueryParams.text#, '%')
                </isEqual>
                <!-- 商品名称 -->
                <isEqual property="itemQueryParams.queryType" compareValue="itemName">item.title like CONCAT('%',
                    #itemQueryParams.text#, '%')
                </isEqual>
                <isEqual property="itemQueryParams.queryType" compareValue="title">item.title like CONCAT('%',
                    #itemQueryParams.text#, '%')
                </isEqual>
                <!-- 商品简称 -->
                <isEqual property="itemQueryParams.queryType" compareValue="shortTitle">item.short_title like
                    CONCAT('%',#itemQueryParams.text#,'%')
                </isEqual>
                <!-- 商品备注 -->
                <isEqual property="itemQueryParams.queryType" compareValue="remark">item.remark like CONCAT('%',
                    #itemQueryParams.text#, '%')
                </isEqual>
                <isEqual property="itemQueryParams.queryType" compareValue="supplier">item.sys_item_id in (select
                    supplier.sys_item_id from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier where
                    supplier.company_id=#companyId# and supplier.sys_item_id=item.sys_item_id and supplier.sys_sku_id=0
                    and supplier.supplier_name like CONCAT('%', #itemQueryParams.text#, '%'))
                </isEqual>
            </isEqual>
            <isEqual property="itemQueryParams.searchType" compareValue="1">
                <!-- 商家编码 -->
                <isEqual property="itemQueryParams.queryType" compareValue="outerId">item.outer_id
                    =#itemQueryParams.text#
                </isEqual>
                <!-- 主商家编码 -->
                <isEqual property="itemQueryParams.queryType" compareValue="mainOuterId">item.outer_id
                    =#itemQueryParams.text#
                </isEqual>
                <!-- 商品名称 -->
                <isEqual property="itemQueryParams.queryType" compareValue="itemName">item.title
                    =#itemQueryParams.text#
                </isEqual>
                <isEqual property="itemQueryParams.queryType" compareValue="title">item.title =#itemQueryParams.text#
                </isEqual>
                <!-- 商品简称 -->
                <isEqual property="itemQueryParams.queryType" compareValue="shortTitle">
                    item.short_title=#itemQueryParams.text#
                </isEqual>
                <!-- 商品备注 -->
                <isEqual property="itemQueryParams.queryType" compareValue="remark">item.remark like CONCAT('%',
                    #itemQueryParams.text#, '%')
                </isEqual>
                <isEqual property="itemQueryParams.queryType" compareValue="supplier">item.sys_item_id in (select
                    supplier.sys_item_id from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier where
                    supplier.company_id=#companyId# and supplier.sys_item_id=item.sys_item_id and supplier.sys_sku_id=0
                    and supplier.supplier_name = #itemQueryParams.text#)
                </isEqual>
            </isEqual>
            <!-- 规格字段 不返回数据-->
            <!-- 规格属性 -->
            <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">1=2</isEqual>
            <!-- 规格别名 -->
            <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">1=2</isEqual>
            <!-- 规格备注 -->
            <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">1=2</isEqual>
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.isSkuItem" prepend="and">
            item.is_sku_item = #itemQueryParams.isSkuItem#
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.userId" prepend="and">
            <isEqual property="itemQueryParams.userType" compareValue="itemSalePrice">
                exists (select 1 from item_sale_price_bridge_#dbInfo.itemSalePriceBrigeDbNo# isp where
                isp.company_id=#companyId# and isp.sys_item_id=item.sys_item_id and isp.uesr_id =
                #itemQueryParams.userId#)
            </isEqual>
        </isNotEmpty>
        <isNotNull property="itemQueryParams.cIds" prepend=" AND ">
            <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">item.seller_cids like
                CONCAT('%', #itemQueryParams.cIds[]#, '%')
            </iterate>
        </isNotNull>

        <isNotNull property="itemQueryParams.brandIds" prepend=" AND ">
            <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.brandIds">item.brand_id IN
                (#itemQueryParams.brandIds[]#)
            </iterate>
        </isNotNull>
        <isNotNull property="itemQueryParams.sysItemIds" prepend="AND">
            item.sys_item_id in
            <iterate open="(" close=")" conjunction="," property="itemQueryParams.sysItemIds">
                #itemQueryParams.sysItemIds[]#
            </iterate>
        </isNotNull>

        <isNotEmpty property="itemQueryParams.sysItemIdList" prepend="and">
            item.sys_item_id in
            <iterate open="(" close=")" conjunction="," property="itemQueryParams.sysItemIdList">
                #itemQueryParams.sysItemIdList[]#
            </iterate>
        </isNotEmpty>

        <include refid="querySysItemTilePageWhere"/>
    </sql>

    <!-- Select SQL -->
    <sql id="dmjItem.selector">
        SELECT
        *
        FROM dmj_item_#split.dbNo#
    </sql>

    <sql id="dmjItemList.limit">
        <dynamic prepend=" LIMIT  ">
            <isNotEmpty property="itemQueryParams.page.startRow">
                #itemQueryParams.page.startRow#,#itemQueryParams.page.offsetRow#
            </isNotEmpty>
        </dynamic>
    </sql>
    <sql id="dmjItemList.limit2">
        <dynamic prepend=" LIMIT  ">
            <isNotEmpty property="page.startRow">
                #page.startRow#,#page.pageSize#
            </isNotEmpty>
        </dynamic>
    </sql>

    <sql id="dmjItemList.sortJoin">
        <isNotEmpty property="itemQueryParams.sort">
            <isNotEmpty property="itemQueryParams.sort.field">
                <isNotEmpty property="itemQueryParams.sort.order">
                    order by
                    <encode property="itemQueryParams.sort.field"></encode>
                    <encode property="itemQueryParams.sort.order"></encode>
                </isNotEmpty>
            </isNotEmpty>
        </isNotEmpty>
        <isEqual property="itemQueryParams.isSort" compareValue="1">
            <isEmpty property="itemQueryParams.sort">
                order by item.sys_item_id desc
            </isEmpty>
        </isEqual>

    </sql>

    <sql id="dmjItemList.joinSql">
        dmj_item_#dbInfo.dmjItemDbNo# AS item
        <isEqual property="joinMode" compareValue="1">
            LEFT JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# AS sku_bridge
            ON sku_bridge.company_id = #split.companyId#
            AND sku_bridge.enable_status = 1
            AND item.sys_item_id = sku_bridge.sys_item_id
        </isEqual>
        <isEqual property="joinMode" compareValue="2">
            LEFT JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# AS sku_bridge
            ON sku_bridge.company_id = #split.companyId#
            AND sku_bridge.enable_status = 1
            AND item.sys_item_id = sku_bridge.sys_item_id
            JOIN tb_item_#dbInfo.ItemDbNo# AS tb_item
            ON tb_item.company_id = #split.companyId#
            AND tb_item.num_iid = sku_bridge.num_iid
        </isEqual>
        <isEqual property="itemQueryParams.needFilterAvailableStock" compareValue="true">
            LEFT JOIN (
            SELECT sys_item_id,
            SUM(available_in_stock) sum_available_in_stock
            FROM stock_#dbInfo.itemStockDbNo#
            WHERE company_id = #split.companyId# AND enable_status = 1
            <isNotNull property="itemQueryParams.stockWarehouseIds">
                AND ware_house_id IN ( <iterate property="itemQueryParams.stockWarehouseIds" conjunction=",">
                #itemQueryParams.stockWarehouseIds[]#
            </iterate> )
            </isNotNull>
            GROUP BY sys_item_id
            ) stock
            ON item.sys_item_id = stock.sys_item_id
        </isEqual>
        <isNotEmpty property="itemQueryParams.text">
            <isEqual property="itemQueryParams.queryType" compareValue="supplierItemOuterId">
                LEFT JOIN (
                SELECT
                item_supplier_bridge.sys_item_id,
                GROUP_CONCAT(distinct item_supplier_bridge.supplier_item_outer_id) supplier_item_outer_id
                FROM item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# item_supplier_bridge
                WHERE item_supplier_bridge.company_id = #split.companyId#
                AND item_supplier_bridge.sys_sku_id &lt; 1
                GROUP BY item_supplier_bridge.sys_item_id
                ) AS supplier_bridge
                ON supplier_bridge.sys_item_id = item.sys_item_id
            </isEqual>
        </isNotEmpty>
    </sql>

    <sql id="itemStockAll.where">
        <dynamic prepend=" WHERE ">
            <!--只能查询当前公司的-->
            <isNotNull property="split.companyId" prepend=" AND ">
                item.company_id=#split.companyId#
                <isEqual property="level" compareValue="1">AND sku.company_id=#split.companyId#</isEqual>
            </isNotNull>
            <isNotNull property="itemQueryParams.numIid" prepend=" AND ">tb_item.num_iid=#itemQueryParams.numIid#
            </isNotNull>
            <!--设置预警-->
            <isEqual property="itemQueryParams.alarmSetting" compareValue="0" prepend=" AND ">item.has_not_set_alarm=1
            </isEqual>
            <isEqual property="itemQueryParams.alarmSetting" compareValue="1" prepend=" AND ">item.has_set_alarm=1
            </isEqual>
            <isNotNull property="itemQueryParams.activeStatus" prepend=" AND ">item.active_status =
                #itemQueryParams.activeStatus#
            </isNotNull>
            and item.is_virtual=0
            <isNotEmpty property="itemQueryParams.hasBatch" prepend="and">
                <isEqual property="level" compareValue="0">item.has_batch=#itemQueryParams.hasBatch#</isEqual>
                <isEqual property="level" compareValue="1">sku.has_batch=#itemQueryParams.hasBatch#</isEqual>
            </isNotEmpty>
            <!--1. 只查询指定商品的-->
            <isNotNull property="itemQueryParams.sysItemIds" prepend="AND">
                item.sys_item_id in
                <iterate open="(" close=")" conjunction="," property="itemQueryParams.sysItemIds">
                    #itemQueryParams.sysItemIds[]#
                </iterate>
            </isNotNull>

            <isNotNull property="itemQueryParams.sysItemId" prepend="AND">item.sys_item_id =
                #itemQueryParams.sysItemId#
            </isNotNull>
            <!--2. 指定字段是否设置-->
            <isNotNull property="itemQueryParams.specialField" prepend="AND" open="(" close=")">
                <isEqual property="itemQueryParams.specialFieldStatus" compareValue="1">
                    <encode property="itemQueryParams.specialField"></encode>
                    IS NOT NULL and <encode property="itemQueryParams.specialField"></encode> !=""
                </isEqual>
                <isEqual property="itemQueryParams.specialFieldStatus" compareValue="2">
                    <encode property="itemQueryParams.specialField"></encode>
                    IS NULL or <encode property="itemQueryParams.specialField"></encode> =""
                    <isNotEmpty property="params.skuSpecialField">
                        or item.sys_item_id in (select sku1.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku1
                        where sku1.enable_status=1 and sku1.company_id=#companyId# and
                        (sku1.<encode property="params.skuSpecialField"></encode> IS NULL or sku1.<encode
                            property="params.skuSpecialField"></encode>=""))
                    </isNotEmpty>
                </isEqual>
            </isNotNull>

            <!--4.库存状态（修改的逻辑）-->
            <isNotNull property="itemQueryParams.stockStatus">
                <isNotNull property="itemQueryParams.warehouseId">
                    <isEqual property="level" compareValue="0">
                        AND EXISTS (select 1 from stock_#dbInfo.itemStockDbNo# stock where stock.company_id =
                        #split.companyId# and
                        item.sys_item_id=stock.sys_item_id AND stock.ware_house_id=#itemQueryParams.warehouseId#
                    </isEqual>
                    <isNotEqual property="level" compareValue="0">
                        AND EXISTS (select 1 from stock_#dbInfo.itemStockDbNo# stock where stock.company_id =
                        #split.companyId# and
                        sku.sys_item_id=stock.sys_item_id AND sku.sys_sku_id = stock.sys_sku_id AND
                        stock.ware_house_id=#itemQueryParams.warehouseId#
                    </isNotEqual>
                    <!--正常-->
                    <isEqual property="itemQueryParams.stockStatus" compareValue="1" prepend="AND">
                        stock.available_in_stock &gt; 0
                        <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                            stock.sys_item_id not in
                            <iterate open="(" close=")" conjunction="," property="itemQueryParams.warnSysItemIdList">
                                #itemQueryParams.warnSysItemIdList[]#
                            </iterate>
                        </isNotEmpty>
                    </isEqual>
                    <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                    <isEqual property="itemQueryParams.stockStatus" compareValue="2" prepend="AND">
                        stock.available_in_stock &gt; 0
                        <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                            stock.sys_item_id in
                            <iterate open="(" close=")" conjunction="," property="itemQueryParams.warnSysItemIdList">
                                #itemQueryParams.warnSysItemIdList[]#
                            </iterate>
                        </isNotEmpty>
                        <isEmpty property="itemQueryParams.warnSysItemIdList" prepend="AND">
                            stock.sys_item_id = -1
                        </isEmpty>
                    </isEqual>
                    <!--无库存-->
                    <isEqual property="itemQueryParams.stockStatus" compareValue="3" prepend="AND">
                        stock.available_in_stock = 0
                        <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                            stock.sys_item_id not in
                            <iterate open="(" close=")" conjunction="," property="itemQueryParams.warnSysItemIdList">
                                #itemQueryParams.warnSysItemIdList[]#
                            </iterate>
                        </isNotEmpty>
                    </isEqual>
                    <!--超卖-->
                    <isEqual property="itemQueryParams.stockStatus" compareValue="4" prepend="AND">
                        stock.available_in_stock &lt; 0
                    </isEqual>
                    )
                </isNotNull>
                <isNull property="itemQueryParams.warehouseId">
                    <isEqual property="level" compareValue="1">
                        <!--正常 1-->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="1" prepend="AND">sku.stock_status
                            = 1
                        </isEqual>
                        <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 2-->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="2" prepend="AND">sku.stock_status=
                            2
                        </isEqual>
                        <!--无库存 3-->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="3" prepend="AND">sku.stock_status
                            = 3
                        </isEqual>
                        <!--超卖 4-->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="4" prepend="AND">sku.stock_status
                            = 4
                        </isEqual>
                    </isEqual>
                    <isNotEqual property="level" compareValue="1">
                        <!--正常-->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="1" prepend="AND">
                            item.is_stock_normal = 1
                        </isEqual>
                        <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="2" prepend="AND">item.alarmed =
                            1
                        </isEqual>
                        <!--无库存-->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="3" prepend="AND">
                            item.has_inventory = 0
                        </isEqual>
                        <!--超卖-->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="4" prepend="AND">item.over_sell =
                            1
                        </isEqual>
                    </isNotEqual>
                </isNull>
            </isNotNull>

            <!--库存状态（多选情况下使用）-->
            <isNotNull property="itemQueryParams.stockStatusList">
                <isNotNull property="itemQueryParams.warehouseId">
                    <isEqual property="level" compareValue="0">
                        AND EXISTS (select 1 from stock_#dbInfo.itemStockDbNo# stock where stock.company_id =
                        #split.companyId# and
                        item.sys_item_id=stock.sys_item_id AND stock.ware_house_id=#itemQueryParams.warehouseId#
                    </isEqual>
                    <isNotEqual property="level" compareValue="0">
                        AND EXISTS (select 1 from stock_#dbInfo.itemStockDbNo# stock where stock.company_id =
                        #split.companyId# and
                        sku.sys_item_id=stock.sys_item_id AND sku.sys_sku_id = stock.sys_sku_id AND
                        stock.ware_house_id=#itemQueryParams.warehouseId#
                    </isNotEqual>

                    <isNotEmpty property="itemQueryParams.stockStatusList" prepend="and">
                        <iterate property="itemQueryParams.stockStatusList" open="(" close=")" conjunction="OR">
                            <!--正常-->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="1">
                                stock.available_in_stock &gt; 0
                                <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                                    stock.sys_item_id not in
                                    <iterate open="(" close=")" conjunction=","
                                             property="itemQueryParams.warnSysItemIdList">
                                        #itemQueryParams.warnSysItemIdList[]#
                                    </iterate>
                                </isNotEmpty>
                            </isEqual>
                            <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="2">
                                stock.available_in_stock &gt; 0
                                <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                                    stock.sys_item_id in
                                    <iterate open="(" close=")" conjunction=","
                                             property="itemQueryParams.warnSysItemIdList">
                                        #itemQueryParams.warnSysItemIdList[]#
                                    </iterate>
                                </isNotEmpty>
                                <isEmpty property="itemQueryParams.warnSysItemIdList" prepend="AND">
                                    stock.sys_item_id = -1
                                </isEmpty>
                            </isEqual>
                            <!--无库存-->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="3">
                                stock.available_in_stock = 0
                                <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                                    stock.sys_item_id not in
                                    <iterate open="(" close=")" conjunction=","
                                             property="itemQueryParams.warnSysItemIdList">
                                        #itemQueryParams.warnSysItemIdList[]#
                                    </iterate>
                                </isNotEmpty>
                            </isEqual>
                            <!--超卖-->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="4">
                                stock.available_in_stock &lt; 0
                            </isEqual>
                        </iterate>
                    </isNotEmpty>
                    )
                </isNotNull>
                <isNull property="itemQueryParams.warehouseId">
                    <isEqual property="level" compareValue="1">
                        <isNotEmpty property="itemQueryParams.stockStatusList" prepend="and">
                            <iterate property="itemQueryParams.stockStatusList" open="(" close=")" conjunction="OR">
                                <!--正常-->
                                <isEqual property="itemQueryParams.stockStatusList[]" compareValue="1">sku.stock_status
                                    = 1
                                </isEqual>
                                <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 2-->
                                <isEqual property="itemQueryParams.stockStatusList[]" compareValue="2">sku.stock_status=
                                    2
                                </isEqual>
                                <!--无库存 3-->
                                <isEqual property="itemQueryParams.stockStatusList[]" compareValue="3">sku.stock_status
                                    = 3
                                </isEqual>
                                <!--超卖 4-->
                                <isEqual property="itemQueryParams.stockStatusList[]" compareValue="4">sku.stock_status
                                    = 4
                                </isEqual>
                            </iterate>
                        </isNotEmpty>
                    </isEqual>
                    <isNotEqual property="level" compareValue="1">
                        <isNotEmpty property="itemQueryParams.stockStatusList" prepend="and">
                            <iterate property="itemQueryParams.stockStatusList" open="(" close=")" conjunction="OR">
                                <!--正常-->
                                <isEqual property="itemQueryParams.stockStatusList[]" compareValue="1">
                                    item.is_stock_normal = 1
                                </isEqual>
                                <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                                <isEqual property="itemQueryParams.stockStatusList[]" compareValue="2">item.alarmed =
                                    1
                                </isEqual>
                                <!--无库存-->
                                <isEqual property="itemQueryParams.stockStatusList[]" compareValue="3">
                                    item.has_inventory = 0
                                </isEqual>
                                <!--超卖-->
                                <isEqual property="itemQueryParams.stockStatusList[]" compareValue="4">item.over_sell =
                                    1
                                </isEqual>
                            </iterate>
                        </isNotEmpty>
                    </isNotEqual>
                </isNull>
            </isNotNull>

            <isNotNull property="itemQueryParams.onSale" prepend="AND">
                <!--出售中-->
                <isEqual property="itemQueryParams.onSale" compareValue="1">item.`status`=0</isEqual>
                <!--橱窗中-->
                <isEqual property="itemQueryParams.onSale" compareValue="2">item.`status`=1</isEqual>
                <!--仓库中-->
                <isEqual property="itemQueryParams.onSale" compareValue="3">item.`status`=2</isEqual>
                <!--停用-->
                <isEqual property="itemQueryParams.onSale" compareValue="4">item.`status`=4</isEqual>
                <isEqual property="itemQueryParams.onSale" compareValue="5">sku_bridge.id is null</isEqual>
            </isNotNull>
            <!--5.模糊匹配-->
            <isNotNull property="itemQueryParams.text" prepend="AND" open="(" close=")">
                <isEqual property="itemQueryParams.searchType" compareValue="0">
                    <!-- 商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">item.outer_id like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 商品名称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="itemName">item.title like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 商品简称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="shortTitle">item.short_title like
                        CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 商品备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="remark">item.remark like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 规格属性 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">item.is_sku_item = 1
                    </isEqual>
                    <!-- 规格别名 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">item.is_sku_item = 1</isEqual>
                    <!-- 规格备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">item.is_sku_item = 1
                    </isEqual>

                    <isEqual property="level" compareValue="1">
                        <!-- 商家编码 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="outerId">or sku.outer_id like
                            CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                        <!-- 规格属性 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">and
                            sku.properties_name like CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                        <!-- 规格别名 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">and sku.properties_alias
                            like CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                        <!-- 规格备注 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">and sku.remark like
                            CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                    </isEqual>
                    <isEqual property="level" compareValue="2">
                        item.sys_item_id in
                        (select sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where enable_status=1 and company_id=#split.companyId#
                        and (
                        <isEqual property="itemQueryParams.queryType" compareValue="outerId">sku.outer_id like
                            CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                        <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">sku.properties_name
                            like CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                        <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">sku.properties_alias like
                            CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                        <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">sku.remark like
                            CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                        )
                        )
                    </isEqual>
                </isEqual>
                <isEqual property="itemQueryParams.searchType" compareValue="1">
                    <!-- 商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">item.outer_id =
                        #itemQueryParams.text#
                    </isEqual>
                    <!-- 商品名称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="itemName">
                        item.title=#itemQueryParams.text#
                    </isEqual>
                    <!-- 商品简称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="shortTitle">
                        item.short_title=#itemQueryParams.text#
                    </isEqual>
                    <!-- 商品备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="remark">
                        item.remark=#itemQueryParams.text#
                    </isEqual>
                    <!-- 规格属性 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">item.is_sku_item = 1
                    </isEqual>
                    <!-- 规格别名 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">item.is_sku_item = 1</isEqual>
                    <!-- 规格备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">item.is_sku_item = 1
                    </isEqual>

                    <isEqual property="level" compareValue="1">
                        <!-- 商家编码 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="outerId">or
                            sku.outer_id=#itemQueryParams.text#
                        </isEqual>
                        <!-- 规格属性 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">and
                            sku.properties_name=#itemQueryParams.text#
                        </isEqual>
                        <!-- 规格别名 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">and
                            sku.properties_alias=#itemQueryParams.text#
                        </isEqual>
                        <!-- 规格备注 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">and
                            sku.remark=#itemQueryParams.text#
                        </isEqual>
                    </isEqual>
                    <isEqual property="level" compareValue="2">
                        item.sys_item_id in
                        (select sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where enable_status=1 and company_id=#split.companyId#
                        and (
                        <isEqual property="itemQueryParams.queryType" compareValue="outerId">sku.outer_id
                            =#itemQueryParams.text#
                        </isEqual>
                        <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">
                            sku.properties_name=#itemQueryParams.text#
                        </isEqual>
                        <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">
                            sku.properties_alias=#itemQueryParams.text#
                        </isEqual>
                        <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">
                            sku.remark=#itemQueryParams.text#
                        </isEqual>
                        )
                        )
                    </isEqual>
                </isEqual>
            </isNotNull>
            <!--6. 平台信息-->
            <isNotNull property="itemQueryParams.userId">
                <!--不等于-1 查询指定平台-->
                <isNotEqual property="itemQueryParams.userId" compareValue="-1" prepend=" AND ">
                    (sku_bridge.user_id=#itemQueryParams.userId# )
                    <isNotNull property="itemQueryParams.cIds" prepend=" AND ">
                        <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">
                            tb_item.seller_cids like CONCAT('%', #itemQueryParams.cIds[]#, '%')
                        </iterate>
                    </isNotNull>
                </isNotEqual>
                <!--不等于-1 查询指定平台-->
                <isEqual property="itemQueryParams.userId" compareValue="-1">
                    <isNotNull property="itemQueryParams.cIds" prepend=" AND ">
                        <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">item.seller_cids
                            like CONCAT('%', #itemQueryParams.cIds[]#, '%')
                        </iterate>
                    </isNotNull>
                </isEqual>
            </isNotNull>
            <!-- 套件和单品的区分字段 -->
            <isNotEmpty property="itemQueryParams.flag">
                <isEqual property="itemQueryParams.flag" compareValue="1">and item.type IN ('1', '2')</isEqual>
                <isEqual property="itemQueryParams.flag" compareValue="0">and item.type = '0'</isEqual>
                <isEqual property="itemQueryParams.flag" compareValue="6">and item.type_tag In (1, 2)</isEqual>
                <isEqual property="itemQueryParams.flag" compareValue="9">and item.type_tag In (3, 4)</isEqual>
            </isNotEmpty>
            <isEqual property="level" compareValue="1">and sku.enable_status =1</isEqual>
            <isEqual property="level" compareValue="0">and item.is_sku_item=0</isEqual>
        </dynamic>
    </sql>

    <sql id="itemStockStatusCondition">
        <isNotNull property="itemQueryParams.minAvailableStock">
            AND stock.sum_available_in_stock >= #itemQueryParams.minAvailableStock#
        </isNotNull>
        <isNotNull property="itemQueryParams.maxAvailableStock">
            <![CDATA[ AND stock.sum_available_in_stock <= #itemQueryParams.maxAvailableStock# ]]>
        </isNotNull>
        <!--库存状态（修改的逻辑）-->
        <isNotNull property="itemQueryParams.stockStatus">
            <isNotNull property="itemQueryParams.warehouseId">
                AND EXISTS (select 1 from stock_#dbInfo.itemStockDbNo# stock where stock.company_id = #split.companyId#
                and
                item.sys_item_id=stock.sys_item_id AND stock.ware_house_id=#itemQueryParams.warehouseId#
                <!--正常-->
                <isEqual property="itemQueryParams.stockStatus" compareValue="1" prepend="AND">
                    stock.available_in_stock &gt; 0
                    <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                        stock.sys_item_id not in
                        <iterate open="(" close=")" conjunction="," property="itemQueryParams.warnSysItemIdList">
                            #itemQueryParams.warnSysItemIdList[]#
                        </iterate>
                    </isNotEmpty>
                </isEqual>
                <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                <isEqual property="itemQueryParams.stockStatus" compareValue="2" prepend="AND">
                    stock.available_in_stock &gt; 0
                    <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                        stock.sys_item_id in
                        <iterate open="(" close=")" conjunction="," property="itemQueryParams.warnSysItemIdList">
                            #itemQueryParams.warnSysItemIdList[]#
                        </iterate>
                    </isNotEmpty>
                    <isEmpty property="itemQueryParams.warnSysItemIdList" prepend="AND">
                        stock.sys_item_id = -1
                    </isEmpty>
                </isEqual>
                <!--无库存-->
                <isEqual property="itemQueryParams.stockStatus" compareValue="3" prepend="AND">
                    stock.available_in_stock = 0
                    <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                        stock.sys_item_id not in
                        <iterate open="(" close=")" conjunction="," property="itemQueryParams.warnSysItemIdList">
                            #itemQueryParams.warnSysItemIdList[]#
                        </iterate>
                    </isNotEmpty>
                </isEqual>
                <!--超卖-->
                <isEqual property="itemQueryParams.stockStatus" compareValue="4" prepend="AND">stock.available_in_stock
                    &lt; 0
                </isEqual>
                )
            </isNotNull>
            <isNull property="itemQueryParams.warehouseId">
                <!--正常-->
                <isEqual property="itemQueryParams.stockStatus" compareValue="1" prepend="AND">item.is_stock_normal =
                    1
                </isEqual>
                <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                <isEqual property="itemQueryParams.stockStatus" compareValue="2" prepend="AND">item.alarmed = 1
                </isEqual>
                <!--无库存-->
                <isEqual property="itemQueryParams.stockStatus" compareValue="3" prepend="AND">item.has_inventory = 0
                </isEqual>
                <!--超卖-->
                <isEqual property="itemQueryParams.stockStatus" compareValue="4" prepend="AND">item.over_sell = 1
                </isEqual>
            </isNull>
        </isNotNull>
        <!--库存状态（多选情况下使用）-->
        <isNotNull property="itemQueryParams.stockStatusList">
            <isNotNull property="itemQueryParams.warehouseId">
                AND EXISTS (select 1 from stock_#dbInfo.itemStockDbNo# stock where stock.company_id = #split.companyId#
                and
                item.sys_item_id=stock.sys_item_id AND stock.ware_house_id=#itemQueryParams.warehouseId#

                <isNotEmpty property="itemQueryParams.stockStatusList" prepend="and">
                    <iterate property="itemQueryParams.stockStatusList" open="(" close=")" conjunction="OR">
                        <!--正常-->
                        <isEqual property="itemQueryParams.stockStatusList[]" compareValue="1">
                            stock.available_in_stock &gt; 0
                            <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                                stock.sys_item_id not in
                                <iterate open="(" close=")" conjunction=","
                                         property="itemQueryParams.warnSysItemIdList">
                                    #itemQueryParams.warnSysItemIdList[]#
                                </iterate>
                            </isNotEmpty>
                        </isEqual>
                        <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                        <isEqual property="itemQueryParams.stockStatusList[]" compareValue="2">
                            stock.available_in_stock &gt; 0
                            <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                                stock.sys_item_id in
                                <iterate open="(" close=")" conjunction=","
                                         property="itemQueryParams.warnSysItemIdList">
                                    #itemQueryParams.warnSysItemIdList[]#
                                </iterate>
                            </isNotEmpty>
                            <isEmpty property="itemQueryParams.warnSysItemIdList" prepend="AND">
                                stock.sys_item_id = -1
                            </isEmpty>
                        </isEqual>
                        <!--无库存-->
                        <isEqual property="itemQueryParams.stockStatusList[]" compareValue="3">
                            stock.available_in_stock = 0
                            <isNotEmpty property="itemQueryParams.warnSysItemIdList" prepend="and">
                                stock.sys_item_id not in
                                <iterate open="(" close=")" conjunction=","
                                         property="itemQueryParams.warnSysItemIdList">
                                    #itemQueryParams.warnSysItemIdList[]#
                                </iterate>
                            </isNotEmpty>
                        </isEqual>
                        <!--超卖-->
                        <isEqual property="itemQueryParams.stockStatusList[]" compareValue="4">
                            stock.available_in_stock &lt; 0
                        </isEqual>
                    </iterate>
                </isNotEmpty>
                )
            </isNotNull>
            <isNull property="itemQueryParams.warehouseId">
                <isNotEmpty property="itemQueryParams.stockStatusList" prepend="and">
                    <iterate property="itemQueryParams.stockStatusList" open="(" close=")" conjunction="OR">
                        <!--正常-->
                        <isEqual property="itemQueryParams.stockStatusList[]" compareValue="1">item.is_stock_normal =
                            1
                        </isEqual>
                        <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                        <isEqual property="itemQueryParams.stockStatusList[]" compareValue="2">item.alarmed = 1
                        </isEqual>
                        <!--无库存-->
                        <isEqual property="itemQueryParams.stockStatusList[]" compareValue="3">item.has_inventory = 0
                        </isEqual>
                        <!--超卖-->
                        <isEqual property="itemQueryParams.stockStatusList[]" compareValue="4">item.over_sell = 1
                        </isEqual>
                    </iterate>
                </isNotEmpty>
            </isNull>
        </isNotNull>
    </sql>

    <sql id="newItemStockAll.where">
        <dynamic prepend=" WHERE ">
            <!--只能查询当前公司的-->
            <isNotNull property="split.companyId" prepend=" AND ">
                item.company_id=#split.companyId#
            </isNotNull>
            <!--设置预警-->
            <isEqual property="itemQueryParams.alarmSetting" compareValue="0" prepend=" AND ">item.has_not_set_alarm=1
            </isEqual>
            <isEqual property="itemQueryParams.alarmSetting" compareValue="1" prepend=" AND ">item.has_set_alarm=1
            </isEqual>
            <isNotNull property="itemQueryParams.activeStatus" prepend=" AND ">item.active_status =
                #itemQueryParams.activeStatus#
            </isNotNull>
            and item.is_virtual=0
            <!-- 只查询指定商品的-->
            <isNotNull property="itemQueryParams.sysItemIds" prepend="AND">
                item.sys_item_id in
                <iterate open="(" close=")" conjunction="," property="itemQueryParams.sysItemIds">
                    #itemQueryParams.sysItemIds[]#
                </iterate>
            </isNotNull>
            <isNotNull property="itemQueryParams.sysItemId" prepend="AND">item.sys_item_id =
                #itemQueryParams.sysItemId#
            </isNotNull>

            <include refid="itemStockStatusCondition"/>

            <!--模糊匹配-->
            <isNotNull property="itemQueryParams.text" prepend="AND" open="(" close=")">
                <isEqual property="itemQueryParams.searchType" compareValue="0">
                    <!-- 商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">item.outer_id like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 商品名称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="itemName">item.title like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 商品简称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="shortTitle">item.short_title like
                        CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 商品备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="remark">item.remark like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>

                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">or item.sys_item_id in (select
                        sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where company_id=#split.companyId# and
                        sku.outer_id like CONCAT('%', #itemQueryParams.text#, '%'))
                    </isEqual>
                    <!-- 规格属性 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">item.sys_item_id in
                        (select sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where company_id=#split.companyId# and
                        sku.properties_name like CONCAT('%', #itemQueryParams.text#, '%'))
                    </isEqual>
                    <!-- 规格别名 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">item.sys_item_id in (select
                        sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where company_id=#split.companyId# and
                        sku.properties_alias like CONCAT('%', #itemQueryParams.text#, '%'))
                    </isEqual>
                    <!-- 规格备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">item.sys_item_id in (select
                        sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where company_id=#split.companyId# and
                        sku.remark like CONCAT('%', #itemQueryParams.text#, '%'))
                    </isEqual>
                    <!-- 供应商商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="supplierItemOuterId">
                        supplier_bridge.supplier_item_outer_id LIKE CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                </isEqual>
                <isEqual property="itemQueryParams.searchType" compareValue="1">
                    <!-- 商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">item.outer_id =
                        #itemQueryParams.text#
                    </isEqual>
                    <!-- 商品名称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="itemName">
                        item.title=#itemQueryParams.text#
                    </isEqual>
                    <!-- 商品简称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="shortTitle">
                        item.short_title=#itemQueryParams.text#
                    </isEqual>
                    <!-- 商品备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="remark">
                        item.remark=#itemQueryParams.text#
                    </isEqual>

                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">or item.sys_item_id in (select
                        sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where company_id=#split.companyId# and
                        sku.outer_id =#itemQueryParams.text#)
                    </isEqual>
                    <!-- 规格属性 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">item.sys_item_id in
                        (select sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where company_id=#split.companyId# and
                        sku.properties_name=#itemQueryParams.text#)
                    </isEqual>
                    <!-- 规格别名 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">item.sys_item_id in (select
                        sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where company_id=#split.companyId# and
                        sku.properties_alias=#itemQueryParams.text#)
                    </isEqual>
                    <!-- 规格备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">item.sys_item_id in (select
                        sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where company_id=#split.companyId# and
                        sku.remark=#itemQueryParams.text#)
                    </isEqual>
                    <!-- 供应商商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="supplierItemOuterId">
                        FIND_IN_SET(#itemQueryParams.text#, supplier_bridge.supplier_item_outer_id) > 0
                    </isEqual>
                </isEqual>
            </isNotNull>

            <!--类目信息-->
            <isNotNull property="itemQueryParams.cIds" prepend=" AND ">
                <isEqual property="itemQueryParams.userId" compareValue="-1">
                    <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">
                        FIND_IN_SET(#itemQueryParams.cIds[]#, item.seller_cids) > 0
                    </iterate>
                </isEqual>

                <isNotEqual property="itemQueryParams.userId" compareValue="-1">
                    <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">
                        FIND_IN_SET(#itemQueryParams.cIds[]#, tb_item.seller_cids) > 0
                    </iterate>
                </isNotEqual>
            </isNotNull>

            <!--平台信息-->
            <isNotNull property="itemQueryParams.userId">
                <isNotEqual property="itemQueryParams.userId" compareValue="-1" prepend=" AND ">
                    sku_bridge.user_id = #itemQueryParams.userId#
                </isNotEqual>
            </isNotNull>

            <!-- 套件和单品的区分字段 -->
            <isNotEmpty property="itemQueryParams.flag">
                <isEqual property="itemQueryParams.flag" compareValue="1">and item.type IN ('1', '2')</isEqual>
                <isEqual property="itemQueryParams.flag" compareValue="0">and item.type = '0'</isEqual>
                <isEqual property="itemQueryParams.flag" compareValue="6">and item.type_tag In (1, 2)</isEqual>
                <isEqual property="itemQueryParams.flag" compareValue="9">and item.type_tag In (3, 4)</isEqual>
            </isNotEmpty>
            <isEqual property="level" compareValue="1">and item.is_sku_item=1</isEqual>
            <isEqual property="level" compareValue="0">and item.is_sku_item=0</isEqual>
        </dynamic>
    </sql>

    <sql id="skuStockAll.where">
        <dynamic prepend=" WHERE ">
            <!--只能查询当前公司的-->
            <isNotNull property="split.companyId" prepend=" AND ">
                sku.company_id=#split.companyId#
            </isNotNull>
            <isNotNull property="itemQueryParams.numIid" prepend=" AND ">tb_item.num_iid=#itemQueryParams.numIid#
            </isNotNull>
            <!--设置预警-->
            <isNotEmpty property="itemQueryParams.alarmSetting">
                <isEqual property="itemQueryParams.alarmSetting" compareValue="0" prepend=" AND ">
                    not exists(select 1 from item_warn_#dbInfo.itemWarnDbNo# iw where iw.enable_status=1 and
                    iw.company_id=#split.companyId#
                    <isNotNull property="itemQueryParams.warehouseId">
                        and iw.warehouse_id = #itemQueryParams.warehouseId#
                    </isNotNull>
                    and iw.sys_item_id=sku.sys_item_id and iw.sys_sku_id = sku.sys_sku_id and iw.stock_down>0
                    )
                </isEqual>
                <isEqual property="itemQueryParams.alarmSetting" compareValue="1" prepend=" AND ">
                    exists(select 1 from item_warn_#dbInfo.itemWarnDbNo# iw where iw.enable_status=1 and
                    iw.company_id=#split.companyId#
                    <isNotNull property="itemQueryParams.warehouseId">
                        and iw.warehouse_id = #itemQueryParams.warehouseId#
                    </isNotNull>
                    and iw.sys_item_id=sku.sys_item_id and iw.sys_sku_id = sku.sys_sku_id and iw.stock_down>0
                    )
                </isEqual>
            </isNotEmpty>

            <isNotNull property="itemQueryParams.activeStatus" prepend=" AND ">sku.active_status =
                #itemQueryParams.activeStatus#
            </isNotNull>
            and sku.is_virtual=0
            <isNotEmpty property="itemQueryParams.hasBatch" prepend="and">
                <isEqual property="level" compareValue="1">sku.has_batch=#itemQueryParams.hasBatch#</isEqual>
            </isNotEmpty>
            <!-- 只查询指定商品的-->
            <isNotNull property="itemQueryParams.sysItemIds" prepend="AND">
                sku.sys_item_id in
                <iterate open="(" close=")" conjunction="," property="itemQueryParams.sysItemIds">
                    #itemQueryParams.sysItemIds[]#
                </iterate>
            </isNotNull>
            <isNotNull property="itemQueryParams.sysItemId" prepend="AND">sku.sys_item_id =
                #itemQueryParams.sysItemId#
            </isNotNull>
            <!--增加sku的过滤条件-->
            <isNotNull property="itemQueryParams.sysSkuIds" prepend="AND">
                sku.sys_sku_id in
                <iterate open="(" close=")" conjunction="," property="itemQueryParams.sysSkuIds">
                    #itemQueryParams.sysSkuIds[]#
                </iterate>
            </isNotNull>

            <!--库存状态（修改的逻辑）-->
            <isNotNull property="itemQueryParams.minAvailableStock">
                AND stock.sum_available_in_stock >= #itemQueryParams.minAvailableStock#
            </isNotNull>
            <isNotNull property="itemQueryParams.maxAvailableStock">
                <![CDATA[ AND stock.sum_available_in_stock <= #itemQueryParams.maxAvailableStock# ]]>
            </isNotNull>
            <isNotNull property="itemQueryParams.stockStatus">
                <isNotNull property="itemQueryParams.warehouseId">
                    AND EXISTS (select 1 from stock_#dbInfo.itemStockDbNo# stock where stock.company_id =
                    #split.companyId# and
                    sku.sys_item_id=stock.sys_item_id AND sku.sys_sku_id = stock.sys_sku_id AND
                    stock.ware_house_id=#itemQueryParams.warehouseId#

                    <!--正常-->
                    <isEqual property="itemQueryParams.stockStatus" compareValue="1" prepend="AND">
                        stock.available_in_stock &gt; 0
                        <isNotEmpty property="itemQueryParams.warnSysSkuIdList" prepend="and">
                            stock.sys_sku_id not in
                            <iterate open="(" close=")" conjunction="," property="itemQueryParams.warnSysSkuIdList">
                                #itemQueryParams.warnSysSkuIdList[]#
                            </iterate>
                        </isNotEmpty>
                    </isEqual>
                    <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                    <isEqual property="itemQueryParams.stockStatus" compareValue="2" prepend="AND">
                        stock.available_in_stock &gt; 0
                        <isNotEmpty property="itemQueryParams.warnSysSkuIdList" prepend="and">
                            stock.sys_sku_id in
                            <iterate open="(" close=")" conjunction="," property="itemQueryParams.warnSysSkuIdList">
                                #itemQueryParams.warnSysSkuIdList[]#
                            </iterate>
                        </isNotEmpty>
                        <isEmpty property="itemQueryParams.warnSysSkuIdList" prepend="AND">
                            stock.sys_sku_id = -1
                        </isEmpty>
                    </isEqual>
                    <!--无库存-->
                    <isEqual property="itemQueryParams.stockStatus" compareValue="3" prepend="AND">
                        stock.available_in_stock = 0
                        <isNotEmpty property="itemQueryParams.warnSysSkuIdList" prepend="and">
                            stock.sys_sku_id not in
                            <iterate open="(" close=")" conjunction="," property="itemQueryParams.warnSysSkuIdList">
                                #itemQueryParams.warnSysSkuIdList[]#
                            </iterate>
                        </isNotEmpty>
                    </isEqual>
                    <!--超卖-->
                    <isEqual property="itemQueryParams.stockStatus" compareValue="4" prepend="AND">
                        stock.available_in_stock &lt; 0
                    </isEqual>
                    )
                </isNotNull>
                <isNull property="itemQueryParams.warehouseId">
                    <isEqual property="level" compareValue="1">
                        <!--正常-->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="1" prepend="AND">sku.stock_status
                            = 1
                        </isEqual>
                        <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="2" prepend="AND">sku.stock_status=
                            2
                        </isEqual>
                        <!--无库存-->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="3" prepend="AND">sku.stock_status
                            = 3
                        </isEqual>
                        <!--超卖-->
                        <isEqual property="itemQueryParams.stockStatus" compareValue="4" prepend="AND">sku.stock_status
                            = 4
                        </isEqual>
                    </isEqual>
                </isNull>
            </isNotNull>

            <!--库存状态（多选情况下使用）-->
            <isNotNull property="itemQueryParams.stockStatusList">
                <isNotNull property="itemQueryParams.warehouseId">
                    AND EXISTS (select 1 from stock_#dbInfo.itemStockDbNo# stock where stock.company_id =
                    #split.companyId# and
                    sku.sys_item_id=stock.sys_item_id AND sku.sys_sku_id = stock.sys_sku_id AND
                    stock.ware_house_id=#itemQueryParams.warehouseId#

                    <isNotEmpty property="itemQueryParams.stockStatusList" prepend="and">
                        <iterate property="itemQueryParams.stockStatusList" open="(" close=")" conjunction="OR">
                            <!--正常-->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="1">
                                stock.available_in_stock &gt; 0
                                <isNotEmpty property="itemQueryParams.warnSysSkuIdList" prepend="and">
                                    stock.sys_sku_id not in
                                    <iterate open="(" close=")" conjunction=","
                                             property="itemQueryParams.warnSysSkuIdList">
                                        #itemQueryParams.warnSysSkuIdList[]#
                                    </iterate>
                                </isNotEmpty>
                            </isEqual>
                            <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="2">
                                stock.available_in_stock &gt; 0
                                <isNotEmpty property="itemQueryParams.warnSysSkuIdList" prepend="and">
                                    stock.sys_sku_id in
                                    <iterate open="(" close=")" conjunction=","
                                             property="itemQueryParams.warnSysSkuIdList">
                                        #itemQueryParams.warnSysSkuIdList[]#
                                    </iterate>
                                </isNotEmpty>
                                <isEmpty property="itemQueryParams.warnSysSkuIdList" prepend="AND">
                                    stock.sys_sku_id = -1
                                </isEmpty>
                            </isEqual>
                            <!--无库存-->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="3">
                                stock.available_in_stock = 0
                                <isNotEmpty property="itemQueryParams.warnSysSkuIdList" prepend="and">
                                    stock.sys_sku_id not in
                                    <iterate open="(" close=")" conjunction=","
                                             property="itemQueryParams.warnSysSkuIdList">
                                        #itemQueryParams.warnSysSkuIdList[]#
                                    </iterate>
                                </isNotEmpty>
                            </isEqual>
                            <!--超卖-->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="4">
                                stock.available_in_stock &lt; 0
                            </isEqual>
                        </iterate>
                    </isNotEmpty>
                    )
                </isNotNull>
                <isNull property="itemQueryParams.warehouseId">
                    <isNotEmpty property="itemQueryParams.stockStatusList" prepend="and">
                        <iterate property="itemQueryParams.stockStatusList" open="(" close=")" conjunction="OR">
                            <!--正常-->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="1">sku.stock_status =
                                1
                            </isEqual>
                            <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="2">sku.stock_status= 2
                            </isEqual>
                            <!--无库存-->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="3">sku.stock_status =
                                3
                            </isEqual>
                            <!--超卖-->
                            <isEqual property="itemQueryParams.stockStatusList[]" compareValue="4">sku.stock_status =
                                4
                            </isEqual>
                        </iterate>
                    </isNotEmpty>
                </isNull>
            </isNotNull>

            <!--模糊匹配-->
            <isNotNull property="itemQueryParams.text" prepend="AND" open="(" close=")">
                <isEqual property="itemQueryParams.searchType" compareValue="0">
                    <!-- 商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">sku.sys_item_id in (select
                        sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# where company_id=#split.companyId# and outer_id
                        like CONCAT('%', #itemQueryParams.text#, '%'))
                    </isEqual>
                    <!-- 商品名称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="itemName">sku.sys_item_id in (select
                        sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# where company_id=#split.companyId# and title like
                        CONCAT('%', #itemQueryParams.text#, '%'))
                    </isEqual>
                    <!-- 商品简称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="shortTitle">sku.sys_item_id in (select
                        sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# where company_id=#split.companyId# and
                        short_title like CONCAT('%', #itemQueryParams.text#, '%') )
                    </isEqual>
                    <!-- 商品备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="remark">sku.sys_item_id in (select
                        sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# where company_id=#split.companyId# and remark
                        like CONCAT('%', #itemQueryParams.text#, '%'))
                    </isEqual>

                    <!-- 商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">or sku.outer_id like
                        CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 规格属性 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">sku.properties_name like
                        CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 规格别名 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">sku.properties_alias like
                        CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 规格备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">sku.remark like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 供应商商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="supplierItemOuterId">
                        supplier_bridge.supplier_item_outer_id LIKE CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                </isEqual>
                <isEqual property="itemQueryParams.searchType" compareValue="1">
                    <!-- 商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">sku.sys_item_id in (select
                        sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# where company_id=#split.companyId# and outer_id =
                        #itemQueryParams.text#)
                    </isEqual>
                    <!-- 商品名称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="itemName">sku.sys_item_id in (select
                        sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# where company_id=#split.companyId# and
                        title=#itemQueryParams.text#)
                    </isEqual>
                    <!-- 商品简称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="shortTitle">sku.sys_item_id in (select
                        sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# where company_id=#split.companyId# and
                        short_title=#itemQueryParams.text#)
                    </isEqual>
                    <!-- 商品备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="remark">sku.sys_item_id in (select
                        sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# where company_id=#split.companyId# and
                        remark=#itemQueryParams.text#)
                    </isEqual>

                    <!-- 商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">or
                        sku.outer_id=#itemQueryParams.text#
                    </isEqual>
                    <!-- 规格属性 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">
                        sku.properties_name=#itemQueryParams.text#
                    </isEqual>
                    <!-- 规格别名 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">
                        sku.properties_alias=#itemQueryParams.text#
                    </isEqual>
                    <!-- 规格备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">
                        sku.remark=#itemQueryParams.text#
                    </isEqual>
                    <!-- 供应商商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="supplierItemOuterId">
                        FIND_IN_SET(#itemQueryParams.text#, supplier_bridge.supplier_item_outer_id) > 0
                    </isEqual>
                </isEqual>
            </isNotNull>

            <!--类目信息-->
            <isNotNull property="itemQueryParams.cIds" prepend=" AND ">
                <isEqual property="itemQueryParams.userId" compareValue="-1">
                    <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">
                        FIND_IN_SET(#itemQueryParams.cIds[]#, sku.seller_cids) > 0
                        OR FIND_IN_SET(#itemQueryParams.cIds[]#, item.seller_cids) > 0
                    </iterate>
                </isEqual>

                <isNotEqual property="itemQueryParams.userId" compareValue="-1">
                    <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">
                        FIND_IN_SET(#itemQueryParams.cIds[]#, tb_item.seller_cids) > 0
                    </iterate>
                </isNotEqual>
            </isNotNull>

            <!--平台信息-->
            <isNotNull property="itemQueryParams.userId">
                <isNotEqual property="itemQueryParams.userId" compareValue="-1" prepend=" AND ">
                    sku_bridge.user_id = #itemQueryParams.userId#
                </isNotEqual>
            </isNotNull>

            <!-- 套件和单品的区分字段 -->
            <isNotEmpty property="itemQueryParams.flag">
                <isEqual property="itemQueryParams.flag" compareValue="1">and sku.type IN ('1', '2')</isEqual>
                <isEqual property="itemQueryParams.flag" compareValue="0">and sku.type = '0'</isEqual>
                <isEqual property="itemQueryParams.flag" compareValue="6">and item.type_tag = 1</isEqual>
                <isEqual property="itemQueryParams.flag" compareValue="9">and item.type_tag = 3</isEqual>
            </isNotEmpty>
            <isEqual property="level" compareValue="1">and sku.enable_status =1</isEqual>
        </dynamic>
    </sql>

    <sql id="filterItemSupplierSql">
        <!-- 根据供应商权限过滤商品-->
        <isNotEmpty property="itemQueryParams.supplierIdPrivilege">
            <isNotEqual property="itemQueryParams.supplierIdPrivilege" compareValue="0">
                <isNotEmpty property="itemQueryParams.sysItemIdListSupplier" prepend="and">
                    (item.has_supplier = 0 or
                    item.sys_item_id in
                    <iterate open="(" close=")" conjunction="," property="itemQueryParams.sysItemIdListSupplier">
                        #itemQueryParams.sysItemIdListSupplier[]#
                    </iterate>
                    )
                </isNotEmpty>
            </isNotEqual>
        </isNotEmpty>
    </sql>

    <!-- 根据供应商权限过滤sku -->
    <sql id="filterSkuSupplierSql">
        <isNotEmpty property="itemQueryParams.supplierIdPrivilege">
            <isNotEqual property="itemQueryParams.supplierIdPrivilege" compareValue="0">
                <isNotEmpty property="itemQueryParams.sysSkuIdListSupplier" prepend="and">
                    (sku.has_supplier = 0 or
                    sku.sys_sku_id in
                    <iterate open="(" close=")" conjunction="," property="itemQueryParams.sysSkuIdListSupplier">
                        #itemQueryParams.sysSkuIdListSupplier[]#
                    </iterate>
                    )
                </isNotEmpty>
            </isNotEqual>
        </isNotEmpty>
        <isEmpty property="itemQueryParams.supplierIdPrivilege" prepend="and">
            sku.has_supplier = 0
        </isEmpty>
    </sql>


    <sql id="itemStockList.joinSql">
        stock_#dbInfo.itemStockDbNo# as stock
        join dmj_item_#dbInfo.dmjItemDbNo# AS item ON item.sys_item_id=stock.sys_item_id and item.is_sku_item=0
        <isEqual property="joinMode" compareValue="1">
            LEFT JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# as sku_bridge on sku_bridge.enable_status=1 and
            item.sys_item_id=sku_bridge.sys_item_id
        </isEqual>
        <isEqual property="joinMode" compareValue="2">
            LEFT JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# as sku_bridge on sku_bridge.enable_status=1 and
            item.sys_item_id=sku_bridge.sys_item_id
            JOIN tb_item_#dbInfo.ItemDbNo# AS tb_item ON tb_item.num_iid=sku_bridge.num_iid
        </isEqual>
    </sql>

    <sql id="skuStockList.joinSql">
        stock_#dbInfo.itemStockDbNo# as stock
        join dmj_sku_#dbInfo.dmjSkuDbNo# AS sku ON sku.sys_item_id=stock.sys_item_id and sku.sys_sku_id=stock.sys_sku_id
        and sku.type = #itemQueryParams.flag#
        and sku.company_id=#split.companyId# and sku.enable_status=1
        join dmj_item_#dbInfo.dmjItemDbNo# as item ON item.sys_item_id=sku.sys_item_id
        <isEqual property="joinMode" compareValue="1">
            left JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# AS sku_bridge ON sku_bridge.enable_status=1 and
            sku.sys_item_id =
            sku_bridge.sys_item_id and sku.sys_sku_id=sku_bridge.sys_id
        </isEqual>
        <isEqual property="joinMode" compareValue="2">
            left JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# as sku_bridge ON sku_bridge.enable_status=1 and
            sku.sys_item_id =
            sku_bridge.sys_item_id and sku.sys_sku_id=sku_bridge.sys_id
            JOIN tb_item_#dbInfo.ItemDbNo# AS tb_item ON tb_item.num_iid=sku_bridge.num_iid
        </isEqual>
    </sql>

    <sql id="smallProCondition">
        <isNotEmpty property="outerId" prepend="and">
            (item.outer_id like CONCAT('%', #outerId#, '%')
            OR
            item.sys_item_id in (select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where
            sku.company_id = #companyId# and sku.outer_id like CONCAT('%', #outerId#, '%')))
        </isNotEmpty>
        <isNotEmpty property="title" prepend="and">
            (item.title like CONCAT('%', #title#, '%')
            OR
            item.sys_item_id in (select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where
            sku.company_id = #companyId# and sku.title like CONCAT('%', #title#, '%')))
        </isNotEmpty>
        <isNotEmpty property="shortTitle" prepend="and">
            (item.short_title like CONCAT('%', #shortTitle#, '%')
            OR
            item.sys_item_id in (select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where
            sku.company_id = #companyId# and sku.short_title like CONCAT('%', #shortTitle#, '%')))
        </isNotEmpty>
        <isNotNull property="supplierId" prepend="and">
            (item.sys_item_id in (select distinct sys_item_id from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo#
            isb where supplier_item_outer_id like CONCAT('%', #supplierId# ,'%')))
        </isNotNull>
        <isNotEmpty property="brandName" prepend="and">
            (item.brand like CONCAT('%', #brandName#, '%')
            OR
            item.sys_item_id in (select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where
            sku.brand like CONCAT('%', #brandName#, '%')))
        </isNotEmpty>
        <isNotEmpty property="minAvailableStockSum" prepend="and">
            <![CDATA[ availableStockSum.totalStock >= #minAvailableStockSum# ]]>
        </isNotEmpty>
        <isNotEmpty property="maxAvailableStockSum" prepend="and">
            <![CDATA[ availableStockSum.totalStock <= #maxAvailableStockSum# ]]>
        </isNotEmpty>
        <isNotEmpty property="maxTotalAvailableStockSum" prepend="and">
            <![CDATA[ totalAvailableStockSum.totalStock <= #maxTotalAvailableStockSum# ]]>
        </isNotEmpty>
        <isNotEmpty property="minTotalAvailableStockSum" prepend="and">
            <![CDATA[ totalAvailableStockSum.totalStock >= #minTotalAvailableStockSum# ]]>
        </isNotEmpty>
    </sql>

    <sql id="itemTypeFlag">
        <isNotEmpty property="flag">
            <isEqual property="flag" compareValue="1">and item.type IN ('1', '2')</isEqual>
            <isEqual property="flag" compareValue="0">and item.type = '0'</isEqual>
        </isNotEmpty>
    </sql>


    <sql id="leftTotalAvailableStockSum">
        LEFT JOIN (
        SELECT
        stock.sys_item_id sys_item_id,
        SUM(available_in_stock) + SUM(lock_stock) AS totalStock
        FROM stock_#dbInfo.itemStockDbNo# stock
        WHERE stock.company_id = #companyId#
        AND stock.enable_status = 1
        GROUP BY stock.sys_item_id
        ) AS totalAvailableStockSum
        ON totalAvailableStockSum.sys_item_id = item.sys_item_id
    </sql>


    <sql id="leftAvailableStockSum">
        LEFT JOIN (
        SELECT
        stock.sys_item_id sys_item_id,
        SUM(available_in_stock) AS totalStock
        FROM stock_#dbInfo.itemStockDbNo# stock
        WHERE stock.company_id = #companyId#
        AND stock.enable_status = 1
        GROUP BY stock.sys_item_id
        ) AS availableStockSum
        ON availableStockSum.sys_item_id = item.sys_item_id
    </sql>


    <sql id="leftLockStockSum">
        LEFT JOIN (
        SELECT
        stock.sys_item_id sys_item_id,
        SUM(lock_stock) AS totalStock
        FROM stock_#dbInfo.itemStockDbNo# stock
        WHERE stock.company_id = #companyId#
        AND stock.enable_status = 1
        GROUP BY stock.sys_item_id
        ) AS lockStockSum
        ON lockStockSum.sys_item_id = item.sys_item_id
    </sql>

    <sql id="leftOnWayStockSum">
        LEFT JOIN (
        SELECT
        stock.sys_item_id sys_item_id,
        SUM(on_way_quantity) AS totalStock
        FROM stock_#dbInfo.itemStockDbNo# stock
        WHERE stock.company_id = #companyId#
        AND stock.enable_status = 1
        GROUP BY stock.sys_item_id
        ) AS onWayStockSum
        ON onWayStockSum.sys_item_id = item.sys_item_id
    </sql>

    <sql id="leftStockDownSum">
        LEFT JOIN (
        SELECT
        stock.sys_item_id sys_item_id,
        SUM(stock_down) AS totalStock
        FROM item_warn_#dbInfo.itemWarnDbNo# stock
        WHERE stock.company_id = #companyId#
        AND stock.enable_status = 1
        GROUP BY stock.sys_item_id
        ) AS stockDownSum
        ON stockDownSum.sys_item_id = item.sys_item_id
    </sql>

    <sql id="joinTables4SmallPro">
        <isNotNull property="orderType">
            <isEqual property="orderType" compareValue="totalAvailableStockSum">
                <include refid="leftTotalAvailableStockSum"/>
            </isEqual>
            <isEqual property="orderType" compareValue="availableStockSum">
                <include refid="leftAvailableStockSum"/>
            </isEqual>
            <isEqual property="orderType" compareValue="lockStockSum">
                <include refid="leftLockStockSum"/>
            </isEqual>
            <isEqual property="orderType" compareValue="onWayStockSum">
                <include refid="leftOnWayStockSum"/>
            </isEqual>
            <isEqual property="orderType" compareValue="stockDownSum">
                <include refid="leftStockDownSum"/>
            </isEqual>
            <isEqual property="orderType" compareValue="totalAvailableStockMoneySum">
                <include refid="leftTotalAvailableStockSum"/>
            </isEqual>
        </isNotNull>

        <isNotNull property="stockType">
            <isNotNull property="orderType">
                <isNotEqual property="orderType" compareValue="totalAvailableStockSum">
                    <isEqual property="stockType" compareValue="totalAvailableStockSum">
                        <include refid="leftTotalAvailableStockSum"/>
                    </isEqual>
                </isNotEqual>
            </isNotNull>

            <isNotNull property="orderType">
                <isNotEqual property="orderType" compareValue="availableStockSum">
                    <isEqual property="stockType" compareValue="availableStockSum">
                        <include refid="leftAvailableStockSum"/>
                    </isEqual>
                </isNotEqual>
            </isNotNull>

            <isNull property="orderType">
                <isEqual property="stockType" compareValue="availableStockSum">
                    <include refid="leftAvailableStockSum"/>
                </isEqual>
            </isNull>

        </isNotNull>
        <isNull property="orderType">
            <include refid="leftTotalAvailableStockSum"/>
        </isNull>
    </sql>

    <sql id="orderBy4SmallPro">
        ORDER BY
        <isNotNull property="orderType">
            <isEqual property="orderType" compareValue="totalAvailableStockSum">
                totalAvailableStockSum.totalStock
            </isEqual>
            <isEqual property="orderType" compareValue="availableStockSum">
                availableStockSum.totalStock
            </isEqual>
            <isEqual property="orderType" compareValue="lockStockSum">
                lockStockSum.totalStock
            </isEqual>
            <isEqual property="orderType" compareValue="onWayStockSum">
                onWayStockSum.totalStock
            </isEqual>
            <isEqual property="orderType" compareValue="stockDownSum">
                stockDownSum.totalStock
            </isEqual>
            <isEqual property="orderType" compareValue="totalAvailableStockMoneySum">
                (totalAvailableStockSum.totalStock * item.purchase_price)
            </isEqual>
        </isNotNull>
        <isNull property="orderType">
            totalAvailableStockSum.totalStock
        </isNull>
        <isEqual property="orderDesc" compareValue="1">
             DESC
        </isEqual>
    </sql>

    <sql id="itemStockList.Where">
        <dynamic prepend=" WHERE ">
            <!--只能查询当前公司的-->
            <isNotNull property="split.companyId" prepend=" AND ">stock.company_id=#split.companyId#</isNotNull>
            and stock.ware_house_id=#itemQueryParams.warehouseId#
            <isNotNull property="itemQueryParams.activeStatus" prepend=" AND ">item.active_status =
                #itemQueryParams.activeStatus#
            </isNotNull>
            and item.is_virtual=0
            <isNotEqual property="level" compareValue="1">and stock.sys_sku_id=0</isNotEqual>
            <!--2. 指定字段是否设置-->
            <isNotNull property="itemQueryParams.specialField" prepend="AND" open="(" close=")">
                <isEqual property="itemQueryParams.specialFieldStatus" compareValue="1">
                    <encode property="itemQueryParams.specialField"></encode>
                    IS NOT NULL and <encode
                        property="itemQueryParams.specialField"></encode> !=""
                </isEqual>
                <isEqual property="itemQueryParams.specialFieldStatus" compareValue="2">
                    <encode property="itemQueryParams.specialField"></encode>
                    IS NULL or <encode property="itemQueryParams.specialField"></encode> =""
                    <isNotEmpty property="params.skuSpecialField">
                        or item.sys_item_id in (select sku1.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku1
                        where sku1.enable_status=1 and sku1.company_id=#companyId# and
                        (sku1.<encode property="params.skuSpecialField"></encode> IS NULL or sku1.<encode
                            property="params.skuSpecialField"></encode>=""))
                    </isNotEmpty>
                </isEqual>
            </isNotNull>
            <!--1. 只查询指定商品-->
            <isNotNull property="itemQueryParams.sysItemIds" prepend="AND">
                item.sys_item_id in
                <iterate open="(" close=")" conjunction="," property="itemQueryParams.sysItemIds">
                    #itemQueryParams.sysItemIds[]#
                </iterate>
            </isNotNull>

            <!--4.库存状态（修改的逻辑）-->
            <isNotNull property="itemQueryParams.stockStatus">
                <!--正常-->
                <isEqual property="itemQueryParams.stockStatus" compareValue="1" prepend="AND">
                    stock.available_in_stock &gt; 0
                    and not exists (select 1 from item_warn_#dbInfo.itemWarnDbNo# iw where iw.company_id =
                    #split.companyId#
                    and iw.warehouse_id = stock.ware_house_id and iw.sys_item_id = stock.sys_item_id
                    and iw.sys_sku_id = stock.sys_sku_id and iw.stock_down &gt; stock.available_in_stock)
                </isEqual>
                <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                <isEqual property="itemQueryParams.stockStatus" compareValue="2" prepend="AND">
                    stock.available_in_stock &gt; 0
                    and exists (select 1 from item_warn_#dbInfo.itemWarnDbNo# iw where iw.company_id = #split.companyId#
                    and iw.warehouse_id = stock.ware_house_id and iw.sys_item_id = stock.sys_item_id
                    and iw.sys_sku_id = stock.sys_sku_id and iw.stock_down &gt; stock.available_in_stock)
                </isEqual>
                <!--无库存-->
                <isEqual property="itemQueryParams.stockStatus" compareValue="3" prepend="AND">
                    stock.available_in_stock = 0
                    and not exists (select 1 from item_warn_#dbInfo.itemWarnDbNo# iw where iw.company_id =
                    #split.companyId#
                    and iw.warehouse_id = stock.ware_house_id and iw.sys_item_id = stock.sys_item_id
                    and iw.sys_sku_id = stock.sys_sku_id and iw.stock_down &gt; stock.available_in_stock)
                </isEqual>
                <!--超卖-->
                <isEqual property="itemQueryParams.stockStatus" compareValue="4" prepend="AND">stock.available_in_stock
                    &lt; 0
                </isEqual>
            </isNotNull>

            <!--4.库存状态（多选情况下使用）-->
            <isNotNull property="itemQueryParams.stockStatusList" prepend="and">
                <iterate property="itemQueryParams.stockStatusList" open="(" close=")" conjunction="OR">
                    <!--正常-->
                    <isEqual property="itemQueryParams.stockStatusList[]" compareValue="1">
                        stock.available_in_stock &gt; 0
                        and not exists (select 1 from item_warn_#dbInfo.itemWarnDbNo# iw where iw.company_id =
                        #split.companyId#
                        and iw.warehouse_id = stock.ware_house_id and iw.sys_item_id = stock.sys_item_id
                        and iw.sys_sku_id = stock.sys_sku_id and iw.stock_down &gt; stock.available_in_stock)
                    </isEqual>
                    <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
                    <isEqual property="itemQueryParams.stockStatusList[]" compareValue="2">
                        stock.available_in_stock &gt; 0
                        and exists (select 1 from item_warn_#dbInfo.itemWarnDbNo# iw where iw.company_id =
                        #split.companyId#
                        and iw.warehouse_id = stock.ware_house_id and iw.sys_item_id = stock.sys_item_id
                        and iw.sys_sku_id = stock.sys_sku_id and iw.stock_down &gt; stock.available_in_stock)
                    </isEqual>
                    <!--无库存-->
                    <isEqual property="itemQueryParams.stockStatusList[]" compareValue="3">
                        stock.available_in_stock = 0
                        and not exists (select 1 from item_warn_#dbInfo.itemWarnDbNo# iw where iw.company_id =
                        #split.companyId#
                        and iw.warehouse_id = stock.ware_house_id and iw.sys_item_id = stock.sys_item_id
                        and iw.sys_sku_id = stock.sys_sku_id and iw.stock_down &gt; stock.available_in_stock)
                    </isEqual>
                    <!--超卖-->
                    <isEqual property="itemQueryParams.stockStatusList[]" compareValue="4">stock.available_in_stock &lt;
                        0
                    </isEqual>
                </iterate>
            </isNotNull>

            <!--5.模糊匹配-->
            <isNotNull property="itemQueryParams.text" prepend="AND" open="(" close=")">
                <isEqual property="itemQueryParams.searchType" compareValue="0">
                    <!-- 商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">item.outer_id like CONCAT('%',
                        #itemQueryParams.text#, '%') or item.remark like concat('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 商品名称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="itemName">item.title like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>
                    <!-- 商品简称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="shortTitle">item.short_title like
                        CONCAT('%',#itemQueryParams.text#,'%')
                    </isEqual>
                    <!-- 商品备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="remark">item.remark like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>

                    <!-- 规格属性 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">item.is_sku_item = 1
                    </isEqual>
                    <!-- 规格别名 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">item.is_sku_item = 1</isEqual>
                    <!-- 规格备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">item.is_sku_item = 1
                    </isEqual>

                    <isEqual property="level" compareValue="1">
                        <!-- 商家编码 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="outerId">or sku.outer_id like
                            CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                        <!-- 规格属性 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">and
                            sku.properties_name like CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                        <!-- 规格别名 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">and sku.properties_alias
                            like CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                        <!-- 规格备注 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">and sku.remark like
                            CONCAT('%', #itemQueryParams.text#, '%')
                        </isEqual>
                    </isEqual>
                </isEqual>
                <isEqual property="itemQueryParams.searchType" compareValue="1">
                    <!-- 商家编码 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="outerId">item.outer_id
                        =#itemQueryParams.text# or item.remark =#itemQueryParams.text#
                    </isEqual>
                    <!-- 商品名称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="itemName">item.title
                        =#itemQueryParams.text#
                    </isEqual>
                    <!-- 商品简称 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="shortTitle">
                        item.short_title=#itemQueryParams.text#
                    </isEqual>
                    <!-- 商品备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="remark">item.remark like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>

                    <!-- 规格属性 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">item.is_sku_item = 1
                    </isEqual>
                    <!-- 规格别名 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">item.is_sku_item = 1</isEqual>
                    <!-- 规格备注 -->
                    <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">item.is_sku_item = 1
                    </isEqual>

                    <isEqual property="level" compareValue="1">
                        <!-- 商家编码 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="outerId">or sku.outer_id
                            =#itemQueryParams.text#
                        </isEqual>
                        <!-- 规格属性 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="propertiesName">and
                            sku.properties_name=#itemQueryParams.text#
                        </isEqual>
                        <!-- 规格别名 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="skuAlias">and
                            sku.properties_alias=#itemQueryParams.text#
                        </isEqual>
                        <!-- 规格备注 -->
                        <isEqual property="itemQueryParams.queryType" compareValue="skuRemark">and
                            sku.remark=#itemQueryParams.text#
                        </isEqual>
                    </isEqual>
                </isEqual>
            </isNotNull>
            <!--6. 平台信息-->
            <isNotNull property="itemQueryParams.userId">
                <!--不等于-1 查询指定平台-->
                <isNotEqual property="itemQueryParams.userId" compareValue="-1" prepend=" AND ">
                    sku_bridge.user_id=#itemQueryParams.userId#
                    <isNotNull property="itemQueryParams.cIds" prepend=" AND ">
                        <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">
                            tb_item.seller_cids like CONCAT('%', #itemQueryParams.cIds[]#, '%')
                        </iterate>
                    </isNotNull>
                </isNotEqual>
                <isEqual property="itemQueryParams.userId" compareValue="-1">
                    <isNotNull property="itemQueryParams.cIds" prepend=" AND ">
                        <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">item.seller_cids
                            like CONCAT('%', #itemQueryParams.cIds[]#, '%')
                        </iterate>
                    </isNotNull>
                </isEqual>
            </isNotNull>
            <!--设置预警-->
            <isEqual property="itemQueryParams.alarmSetting" compareValue="0" prepend=" AND ">item.has_not_set_alarm=1
            </isEqual>
            <isEqual property="itemQueryParams.alarmSetting" compareValue="1" prepend=" AND ">item.has_set_alarm=1
            </isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="0">and item.type = '0'</isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="1">and item.type IN ('1', '2')</isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="6">and item.type_tag In (1, 2)</isEqual>
            <isEqual property="itemQueryParams.flag" compareValue="9">and item.type_tag In (3, 4)</isEqual>
            and item.company_id=#split.companyId#
        </dynamic>
    </sql>

    <sql id="baseQueryType.customerQuery">
        <isEqual property="itemQueryParams.queryType" compareValue="cmName">
            <isNotEmpty property="itemQueryParams.text" prepend="AND">
                customer.cm_name like CONCAT('%', #itemQueryParams.text#, '%')
            </isNotEmpty>
        </isEqual>
        <isEqual property="itemQueryParams.queryType" compareValue="cmCode">
            <isNotEmpty property="itemQueryParams.text" prepend="AND">
                customer.cm_code like CONCAT('%', #itemQueryParams.text#, '%')
            </isNotEmpty>
        </isEqual>
        <isEqual property="itemQueryParams.queryType" compareValue="cmNick">
            <isNotEmpty property="itemQueryParams.text" prepend="AND">
                customer.cm_nick like CONCAT('%', #itemQueryParams.text#, '%')
            </isNotEmpty>
        </isEqual>
        <!-- 按客户名称、编码、昵称全模糊查询 -->
        <isNotEmpty property="itemQueryParams.cmCodes" prepend="AND" open="(" close=")">
            customer.cm_name like CONCAT('%', #itemQueryParams.cmCodes#, '%')
            or customer.cm_code like CONCAT('%', #itemQueryParams.cmCodes#, '%')
            or customer.cm_nick like CONCAT('%', #itemQueryParams.cmCodes#, '%')
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.customerIdList" prepend="AND">
            customer.customer_id IN
            <iterate property="itemQueryParams.customerIdList" open="(" close=")" conjunction=",">
                #itemQueryParams.customerIdList[]#
            </iterate>
        </isNotEmpty>
        and customer.enable_status = 1
    </sql>

    <sql id="dmjItemFilter.customerFilter">
        <isNotEmpty property="itemQueryParams.needCustomerFilter">
            <!-- 客户销售价信息-->
            <isEqual property="itemQueryParams.needCustomerFilter" compareValue="no">
                AND (customer.sys_item_id IS NULL)
            </isEqual>
            <isEqual property="itemQueryParams.needCustomerFilter" compareValue="yes">
                AND (customer.sys_item_id IS NOT NULL)
                <include refid="baseQueryType.customerQuery"/>
            </isEqual>
        </isNotEmpty>
    </sql>

    <sql id="dmjSkuFilter.customerFilter">
        <!-- 客户销售价信息-->
        <isEqual prepend="and" property="itemQueryParams.needCustomerFilter" compareValue="no">
            not exists (select 1 from item_customer_bridge_#dbInfo.itemCustomerBridgeDbNo# customer where
            customer.company_id = sku.company_id and customer.sys_item_id = sku.sys_item_id and customer.sys_sku_id = sku.sys_sku_id
            <include refid="baseQueryType.customerQuery"/>
            )
        </isEqual>
        <isEqual prepend="and" property="itemQueryParams.needCustomerFilter" compareValue="yes">
            exists (select 1 from item_customer_bridge_#dbInfo.itemCustomerBridgeDbNo# customer where
            customer.company_id = sku.company_id and customer.sys_item_id = sku.sys_item_id and customer.sys_sku_id = sku.sys_sku_id
            <include refid="baseQueryType.customerQuery"/>
            )
        </isEqual>
    </sql>


    <select id="queryAllItemByCompanyId" parameterClass="hashMap" resultMap="dmjItemMap" timeout="30">
        select * from dmj_item_#dbNo# where company_id=#companyId#
        <isEqual property="isSkuItem" prepend="and" compareValue="1">
            is_sku_item = 1
        </isEqual>
    </select>


    <!-- 根据主键删除 -->
    <delete id="deleteBySysItemId" parameterClass="hashMap">
        delete from dmj_item_#dbNo#
        WHERE
        sys_item_id=#sysItemId# and company_id=#companyId#
    </delete>

    <!-- 根据主键批量删除 -->
    <delete id="batchDeleteBySysItemIds" parameterClass="hashMap">
        delete from dmj_item_#dbNo#
        WHERE
        company_id=#companyId#
        and
        sys_item_id in
        <iterate conjunction="," open="(" close=")" property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </delete>

    <!-- 根据主键更新 -->
    <update id="update" parameterClass="DmjItem">
        UPDATE dmj_item_#dbNo#
        set modified = now()
        <isNotEmpty prepend="," property="productionDate">production_date= #productionDate#</isNotEmpty>
        <isNotNull prepend="," property="mergeType">merge_type = #mergeType#</isNotNull>
        <isNotNull prepend="," property="onSale">on_sale = #onSale#</isNotNull>
        <isNotNull prepend="," property="sysItemId">sys_item_id = #sysItemId#</isNotNull>
        <isNotNull prepend="," property="goodAllocation">goods_allocation = #goodAllocation#</isNotNull>
        <isNotNull prepend="," property="shortTitle">short_title = #shortTitle#</isNotNull>
        <isNotNull prepend="," property="market">market = substring(#market#,1,64)</isNotNull>
        <isNotNull prepend="," property="costPrice">cost_price = #costPrice#</isNotNull>
        <isNotNull prepend="," property="weight">weight = #weight#</isNotNull>
        <isNotNull prepend="," property="barcode">barcode = #barcode#</isNotNull>
        <isNotNull prepend="," property="created">created = #created#</isNotNull>
        <isNotNull prepend="," property="status">status = #status#</isNotNull>
        <isNotNull prepend="," property="picPath">pic_path = substring(#picPath#,1,1024)</isNotNull>
        <isNotNull prepend="," property="title">title = substring(#title#,1,64)</isNotNull>
        <isNotNull prepend="," property="outerId">outer_id = #outerId#</isNotNull>
        <isNotNull prepend="," property="num">num = #num#</isNotNull>
        <isNotNull prepend="," property="remark">remark = #remark#</isNotNull>
        <isNotNull prepend="," property="type">type = #type#</isNotNull>
        <isNotNull prepend="," property="catId">cat_id = #catId#</isNotNull>
        <isNotNull prepend="," property="brand">brand= #brand#</isNotNull>
        <isNotNull prepend="," property="brandId">brand_id= #brandId#</isNotNull>
        <isNotNull prepend="," property="sellerCids">seller_cids= #sellerCids#</isNotNull>
        <isNotNull prepend="," property="propsName">props_name= #propsName#</isNotNull>
        <isNotNull prepend="," property="unit">unit= #unit#</isNotNull>
        <isNotNull prepend="," property="priceImport">purchase_price= #priceImport#</isNotNull>
        <isNotNull prepend="," property="priceOutput">selling_price= #priceOutput#</isNotNull>
        <isNotNull prepend="," property="wholesalePrice">wholesale_price= #wholesalePrice#</isNotNull>
        <isNotNull prepend="," property="x">x= #x#</isNotNull>
        <isNotNull prepend="," property="y">y= #y#</isNotNull>
        <isNotNull prepend="," property="z">z= #z#</isNotNull>
        <isNotNull prepend="," property="periodCast">period_cast= #periodCast#</isNotNull>
        <isNotNull prepend="," property="place">place= #place#</isNotNull>
        <isNotNull prepend="," property="age">age= #age#</isNotNull>
        <isNotNull prepend="," property="defined1">defined_1= #defined1#</isNotNull>
        <isNotNull prepend="," property="defined1Key">defined_1_key= #defined1Key#</isNotNull>
        <isNotNull prepend="," property="defined2">defined_2= #defined2#</isNotNull>
        <isNotNull prepend="," property="defined2Key">defined_2_key= #defined2Key#</isNotNull>
        <isNotNull prepend="," property="defined3">defined_3= #defined3#</isNotNull>
        <isNotNull prepend="," property="defined3Key">defined_3_key= #defined3Key#</isNotNull>
        <isNotNull prepend="," property="defined4">defined_4= #defined4#</isNotNull>
        <isNotNull prepend="," property="defined4Key">defined_4_key= #defined4Key#</isNotNull>
        <isNotNull prepend="," property="defined5">defined_5= #defined5#</isNotNull>
        <isNotNull prepend="," property="defined5Key">defined_5_key= #defined5Key#</isNotNull>
        <isNotNull prepend="," property="defined6">defined_6= #defined6#</isNotNull>
        <isNotNull prepend="," property="defined6Key">defined_6_key= #defined6Key#</isNotNull>
        <isNotNull prepend="," property="activeStatus">active_status= #activeStatus#</isNotNull>
        <isNotNull prepend="," property="stockAlarmNum">stock_alarm_num= #stockAlarmNum#</isNotNull>
        <isNotNull prepend="," property="packmaAlarmNum">packma_alarm_num= #packmaAlarmNum#</isNotNull>
        <isNotNull prepend="," property="hasSetAlarm">has_set_alarm= #hasSetAlarm#</isNotNull>
        <isNotNull prepend="," property="hasNotSetAlarm">has_not_set_alarm= #hasNotSetAlarm#</isNotNull>
        <isNotNull prepend="," property="alarmed">alarmed= #alarmed#</isNotNull>
        <isNotNull prepend="," property="isStockNormal">is_stock_normal= #isStockNormal#</isNotNull>
        <isNotNull prepend="," property="hasInventory">has_inventory= #hasInventory#</isNotNull>
        <isNotNull prepend="," property="isSkuItem">is_sku_item = #isSkuItem#</isNotNull>
        <isNotNull prepend="," property="shipper">shipper = #shipper#</isNotNull>
        <isNotNull prepend="," property="hasSupplier">has_supplier = #hasSupplier#</isNotNull>
        <isNotNull prepend="," property="isSysWeight">is_sys_weight = #isSysWeight#</isNotNull>
        <isNotNull prepend="," property="declareNameZh">declare_name_zh = #declareNameZh#</isNotNull>
        <isNotNull prepend="," property="declareNameEn">declare_name_en = #declareNameEn#</isNotNull>
        <isNotNull prepend="," property="hsCode">hs_code = #hsCode#</isNotNull>
        <isNotNull prepend="," property="declareAmount">declare_amount = #declareAmount#</isNotNull>
        <isNotNull prepend="," property="declareWeight">declare_weight = #declareWeight#</isNotNull>
        <isNotNull prepend="," property="isSysPriceImport">is_sys_purchase_price = #isSysPriceImport#</isNotNull>
        <isNotNull prepend="," property="isSysPriceOutput">is_sys_selling_price = #isSysPriceOutput#</isNotNull>
        <isNotNull prepend="," property="isSysWholesalePrice">is_sys_wholesale_price = #isSysWholesalePrice#</isNotNull>
        <isNotNull prepend="," property="weightAvgPrice">weight_avg_price = #weightAvgPrice#</isNotNull>
        <isNotNull prepend="," property="nearDate">near_date = #nearDate#</isNotNull>
        <isNotNull prepend="," property="typeTag">type_tag = #typeTag#</isNotNull>
        <isNotNull prepend="," property="definedJson">defined_json = #definedJson#</isNotNull>
        <isNotNull prepend="," property="component">component = #component#</isNotNull>
        <isNotNull prepend="," property="standard">standard = #standard#</isNotNull>
        <isNotEmpty prepend="," property="platformId">platform_id = #platformId#</isNotEmpty>
        <isNotNull prepend="," property="oneIntegral">one_integral = #oneIntegral#</isNotNull>
        <isNotNull prepend="," property="moreIntegral">more_integral = #moreIntegral#</isNotNull>
        <isNotNull prepend="," property="onePackageIntegral">one_package_integral = #onePackageIntegral#</isNotNull>
        <isNotNull prepend="," property="morePackageIntegral">more_package_integral = #morePackageIntegral#</isNotNull>
        <isNotNull prepend="," property="oneInspectionIntegral">one_inspection_integral = #oneInspectionIntegral#</isNotNull>
        <isNotNull prepend="," property="moreInspectionIntegral">more_inspection_integral = #moreInspectionIntegral#</isNotNull>
        <isNotNull prepend="," property="oneWeightIntegral">one_weight_integral = #oneWeightIntegral#</isNotNull>
        <isNotNull prepend="," property="moreWeightIntegral">more_weight_integral = #moreWeightIntegral#</isNotNull>
        <isNotNull prepend="," property="record">record = #record#</isNotNull>
        <isNotNull prepend="," property="safekind">safekind = #safekind#</isNotNull>
        <isNotNull prepend="," property="boxnum">boxnum = #boxnum#</isNotNull>
        <isNotNull prepend="," property="invoice">invoice = #invoice#</isNotNull>
        <isNotNull prepend="," property="makeGift">make_gift = #makeGift#</isNotNull>
        <isNotNull prepend="," property="outerIdPure">outer_id_pure = #outerIdPure#</isNotNull>
        <isNotNull prepend="," property="bigPic">big_pic = #bigPic#</isNotNull>
        <isNotNull prepend="," property="extendFieldValues">extend_field_values = #extendFieldValues#</isNotNull>
        <isNotNull prepend="," property="specificationJson">specification_json = #specificationJson#</isNotNull>
        WHERE sys_item_id = #sysItemId# and company_id = #companyId# and enable_status = 1
    </update>

    <update id="update4Save" parameterClass="DmjItem">
        UPDATE dmj_item_#dbNo#
        SET modified = NOW(),
        list_time = #listTime#
        <isNotNull prepend="," property="outerId">
            outer_id = #outerId#
        </isNotNull>
        <isNotNull prepend="," property="outerIdPure">
            outer_id_pure = #outerIdPure#
        </isNotNull>
        <isNotNull prepend="," property="title">
            title = #title#
        </isNotNull>
        <isNotNull prepend="," property="shortTitle">
            short_title = #shortTitle#
        </isNotNull>
        <isNotNull prepend="," property="barcode">
            barcode = #barcode#
        </isNotNull>
        <isNotNull prepend="," property="catId">
            cat_id = #catId#
        </isNotNull>
        <isNotNull prepend="," property="sellerCids">
            seller_cids = #sellerCids#
        </isNotNull>
        <isNotNull prepend="," property="brand">
            brand = #brand#
        </isNotNull>
        <isNotNull prepend="," property="brandId">
            brand_id = #brandId#
        </isNotNull>
        <isNotNull prepend="," property="picPath">
            pic_path = #picPath#
        </isNotNull>
        <isNotNull prepend="," property="priceImport">
            purchase_price = #priceImport#
        </isNotNull>
        <isNotNull prepend="," property="priceOutput">
            selling_price = #priceOutput#
        </isNotNull>
        <isNotNull prepend="," property="weight">
            weight = #weight#
        </isNotNull>
        <isNotNull prepend="," property="remark">
            remark = #remark#
        </isNotNull>
        <isNotNull prepend="," property="makeGift">
            make_gift = #makeGift#
        </isNotNull>
        <isNotNull prepend="," property="periodCast">
            period_cast = #periodCast#
        </isNotNull>
        <isNotNull prepend="," property="shipper">
            shipper = #shipper#
        </isNotNull>
        <isNotNull prepend="," property="unit">
            unit = #unit#
        </isNotNull>
        <isNotNull prepend="," property="x">
            x = #x#
        </isNotNull>
        <isNotNull prepend="," property="y">
            y = #y#
        </isNotNull>
        <isNotNull prepend="," property="z">
            z = #z#
        </isNotNull>
        <isNotNull prepend="," property="place">
            place = #place#
        </isNotNull>
        <isNotNull prepend="," property="component">
            component = #component#
        </isNotNull>
        <isNotNull prepend="," property="standard">
            standard = #standard#
        </isNotNull>
        <isNotNull prepend="," property="safekind">
            safekind = #safekind#
        </isNotNull>
        <isNotNull prepend="," property="record">
            record = #record#
        </isNotNull>
        <isNotNull prepend="," property="wholesalePrice">
            wholesale_price = #wholesalePrice#
        </isNotNull>
        <isNotNull prepend="," property="definedJson">
            defined_json = #definedJson#
        </isNotNull>
        <isNotNull prepend="," property="isSkuItem">
            is_sku_item = #isSkuItem#
        </isNotNull>
        <isNotNull prepend="," property="stockAlarmNum">
            stock_alarm_num = #stockAlarmNum#
        </isNotNull>
        <isNotNull prepend="," property="hasSetAlarm">
            has_set_alarm = #hasSetAlarm#
        </isNotNull>
        <isNotNull prepend="," property="hasNotSetAlarm">
            has_not_set_alarm = #hasNotSetAlarm#
        </isNotNull>
        <isNotNull prepend="," property="alarmed">
            alarmed = #alarmed#
        </isNotNull>
        <isNotNull prepend="," property="isStockNormal">
            is_stock_normal = #isStockNormal#
        </isNotNull>
        <isNotNull prepend="," property="hasInventory">
            has_inventory = #hasInventory#
        </isNotNull>
        <isNotNull prepend="," property="hasBatch">
            has_batch = #hasBatch#
        </isNotNull>
        <isNotNull prepend="," property="bigPic">
            big_pic = #bigPic#
        </isNotNull>
        <isNotNull prepend="," property="boxnum">
            boxnum = #boxnum#
        </isNotNull>
        <isNotNull prepend="," property="invoice">
            invoice = #invoice#
        </isNotNull>
        <isNotNull prepend="," property="extendFieldValues">
            extend_field_values = #extendFieldValues#
        </isNotNull>
        <isNotNull prepend="," property="extendRelationInfo">
            extend_relation_info = #extendRelationInfo#
        </isNotNull>
        <isNotNull prepend="," property="isSysWholesalePrice">
            is_sys_wholesale_price = #isSysWholesalePrice#
        </isNotNull>
        <isNotNull prepend="," property="isSysWeight">
            is_sys_weight = #isSysWeight#
        </isNotNull>
        <isNotNull prepend="," property="isSysPriceImport">
            is_sys_purchase_price = #isSysPriceImport#
        </isNotNull>
        <isNotNull prepend="," property="isSysPriceOutput">
            is_sys_selling_price = #isSysPriceOutput#
        </isNotNull>
        <isNotNull prepend="," property="listTime">
            list_time = #listTime#
        </isNotNull>
        <isNotNull prepend="," property="specificationJson">
            specification_json = #specificationJson#
        </isNotNull>

        WHERE company_id = #companyId# AND sys_item_id = #sysItemId#
    </update>

    <select id="getDmjItemBySysItemIdList" resultMap="dmjItemMap" parameterClass="hashMap" timeout="30">
        SELECT * FROM dmj_item_#dbNo# item
        WHERE item.company_id=#companyId# and item.sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIdList">
            #sysItemIdList[]#
        </iterate>
        <include refid="paramsFlag"/>
    </select>

    <select id="queryItemsBySysItemIdList" resultMap="dmjItemMap" parameterClass="map" timeout="30">
        SELECT * FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate open="(" close=")" conjunction="," property="sysItemIdList">
            #sysItemIdList[]#
        </iterate>
    </select>

    <!-- 根据主键查询 -->
    <select id="getDmjItemBySysItemId" resultMap="dmjItemMap" parameterClass="hashMap" timeout="30">
        SELECT * FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND sys_item_id = #sysItemId#
        <isNotNull property="enableStatus" prepend="and">
            enable_status = #enableStatus#
        </isNotNull>
        <isNotNull property="activeStatus" prepend="and">
            active_status = #activeStatus#
        </isNotNull>
    </select>

    <select id="getDmjItemByOuterIds" resultMap="dmjItemMap" parameterClass="hashMap" timeout="30">
        SELECT * FROM dmj_item_#dbNo#
        WHERE
        company_id=#companyId# and enable_status = 1 and outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIds">
            #outerIds[]#
        </iterate>
    </select>

    <select id="getImportSimpleDmjItemByOuterIds" resultMap="importSimpleItem" parameterClass="hashMap" timeout="30">
        SELECT sys_item_id, outer_id, type, type_tag FROM dmj_item_#dbNo#
        WHERE
        company_id=#companyId# and enable_status = 1 and outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIds">
            #outerIds[]#
        </iterate>
    </select>

    <select id="getPureDmjItemByOuterIds" resultMap="dmjItemMap" parameterClass="hashMap" timeout="30">
        SELECT * FROM dmj_item_#dbNo#
        WHERE
        company_id=#companyId# and enable_status = 1 and is_sku_item = 0 and outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIds">
            #outerIds[]#
        </iterate>
    </select>

    <select id="getDmjItemByOuterIdPures" resultMap="simpleMapForMatchD" parameterClass="hashMap" timeout="30">
        SELECT * FROM dmj_item_#dbNo#
        WHERE
        company_id=#companyId# and enable_status = 1 and outer_id_pure in
        <iterate open="(" close=")" conjunction="," property="outerIdPures">
            #outerIdPures[]#
        </iterate>
    </select>

    <select id="DmjItem.queryByOuterId" resultMap="dmjItemMap" timeout="30">
        select *
        from dmj_item_#dbNo#
        where outer_id=#outerId# and company_id=#companyId#
        <isNotEmpty property="type" prepend="and">
            type = #type#
        </isNotEmpty>
    </select>

    <select id="DmjItem.groupOfOuterIdsByExcludeItemSysIds" resultClass="hashMap" parameterClass="hashMap"
            timeout="30">
        select count(outer_id) c,outer_id from
        ((SELECT sys_item_id,outer_id FROM dmj_item_#dbNo#
        WHERE company_id=#companyId# AND outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIds">
            #outerIds[]#
        </iterate>
        AND sys_item_id not in
        <iterate open="(" close=")" conjunction="," property="excludeSysIds">
            #excludeSysIds[]#
        </iterate>
        )
        UNION ALL
        (SELECT sys_item_id,outer_id FROM dmj_sku_#dbInfo.dmjSkuDbNo#
        WHERE company_id=#companyId# AND enable_status = 1 AND outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIds">
            #outerIds[]#
        </iterate>
        AND sys_item_id not in
        <iterate open="(" close=")" conjunction="," property="excludeSysIds">
            #excludeSysIds[]#
        </iterate>
        )) tmp
        group by outer_id
    </select>

    <select id="DmjItem.groupOfOuterIdsByExcludeSkuSysIds" resultClass="hashMap" parameterClass="hashMap"
            timeout="30">
        select count(outer_id) c,outer_id from
        ((SELECT outer_id FROM dmj_item_#dbNo#
        WHERE company_id=#companyId# AND outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIds">
            #outerIds[]#
        </iterate>
        )
        UNION ALL
        (SELECT outer_id FROM dmj_sku_#dbInfo.dmjSkuDbNo#
        WHERE company_id=#companyId# AND enable_status = 1 AND outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIds">
            #outerIds[]#
        </iterate>
        AND sys_sku_id not in
        <iterate open="(" close=")" conjunction="," property="excludeSysIds">
            #excludeSysIds[]#
        </iterate>
        )) tmp
        group by outer_id
    </select>


    <!-- 根据主键更新，更新基础信息，例如商家编码、商品简称等等 -->
    <update id="updateBase" parameterClass="DmjItem">
        UPDATE dmj_item_#dbNo#
        set modified = now()
        <isNotNull prepend="," property="shortTitle">short_title = #shortTitle#</isNotNull>
        <isNotNull prepend="," property="outerId">outer_id = #outerId#</isNotNull>
        <isNotNull prepend="," property="outerIdPure">outer_id_pure = #outerIdPure#</isNotNull>
        <isNotNull prepend="," property="num">num = #num#</isNotNull>
        <isNotNull prepend="," property="picPath">pic_path = #picPath#</isNotNull>
        <isNotNull prepend="," property="weight">weight = #weight#</isNotNull>
        <isNotNull prepend="," property="component">component = #component#</isNotNull>
        WHERE sys_item_id = #sysItemId# and company_id = #companyId#
    </update>

    <update id="updateSection" parameterClass="DmjItem">
        UPDATE dmj_item_#dbNo#
        set modified = now()
        <isNotNull prepend="," property="isSkuItem">is_sku_item = #isSkuItem#</isNotNull>
        <isNotNull prepend="," property="alarmed">has_set_alarm = 0, has_not_set_alarm = 1, alarmed = 0, has_inventory =
            0
        </isNotNull>
        WHERE sys_item_id = #sysItemId# and company_id = #companyId#
    </update>

    <!--商品库存相关查询 begin-->
    <!--纯商品查询-->
    <select id="getDmjItemListCount" parameterClass="ParamsKitDmjItem" resultClass="int" timeout="30">
        SELECT
        count(distinct item.sys_item_id)
        FROM
        <include refid="dmjItemList.joinSql"/>
        <include refid="newItemStockAll.where"/>
        <include refid="filterItemSupplierSql"/>
    </select>

    <!-- 分页 -->
    <select id="getDmjItemListWithPage" parameterClass="ParamsKitDmjItem" resultMap="dmjItemMap" timeout="30">
        SELECT
        <isNotEqual property="joinMode" compareValue="0">distinct</isNotEqual>
        item.sys_item_id as id, item.*
        FROM
        <include refid="dmjItemList.joinSql"/>
        <include refid="newItemStockAll.where"/>
        <include refid="filterItemSupplierSql"/>
        <include refid="dmjItemList.sortJoin"/>
        <include refid="dmjItemList.limit"/>
    </select>

    <!-- 分页 -->
    <!--含sku商品查询-->
    <select id="getDmjSkuItemListWithPage" parameterClass="ParamsKitDmjItem" resultMap="dmjItemMap" timeout="30">
        SELECT <isNotEqual property="joinMode" compareValue="0">distinct</isNotEqual> item.sys_item_id as id, item.*
        FROM
        <include refid="dmjItemList.joinSql"/>
        <include refid="newItemStockAll.where"/>
        <include refid="filterItemSupplierSql"/>
        <include refid="dmjItemList.sortJoin"/>
        <include refid="dmjItemList.limit"/>
    </select>

    <select id="getDmjSkuItemCount" parameterClass="ParamsKitDmjItem" resultClass="long" timeout="30">
        SELECT count(distinct item.sys_item_id)
        FROM
        <include refid="dmjItemList.joinSql"/>
        <include refid="newItemStockAll.where"/>
        <include refid="filterItemSupplierSql"/>
    </select>

    <select id="getDmjItemListWithPageJoin" parameterClass="ParamsKitDmjItem" resultMap="dmjItemMap" timeout="30">
        SELECT
        distinct item.sys_item_id as id,
        item.*
        FROM
        dmj_item_#dbInfo.dmjItemDbNo# AS item
        LEFT JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# AS sku_bridge ON sku_bridge.enable_status=1 and item.sys_item_id =
        sku_bridge.sys_item_id
        LEFT JOIN tb_item_#dbInfo.ItemDbNo# AS tb_item ON tb_item.num_iid = sku_bridge.num_iid
        LEFT join dmj_sku_#dbInfo.dmjSkuDbNo# as sku on item.sys_item_id=sku.sys_item_id
        <include refid="itemStockAll.where"/>
        <include refid="filterItemSupplierSql"/>
        group by item.sys_item_id
        <include refid="dmjItemList.sortJoin"/>
        <include refid="dmjItemList.limit"/>
    </select>

    <select id="getDmjItemListCountJoin" parameterClass="ParamsKitDmjItem" resultClass="long" timeout="30">
        SELECT
        count(DISTINCT(item.sys_item_id))
        FROM
        dmj_item_#dbInfo.dmjItemDbNo# AS item
        LEFT JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# AS sku_bridge ON sku_bridge.enable_status=1 and item.sys_item_id =
        sku_bridge.sys_item_id
        LEFT JOIN tb_item_#dbInfo.ItemDbNo# AS tb_item ON tb_item.num_iid = sku_bridge.num_iid
        LEFT join dmj_sku_#dbInfo.dmjSkuDbNo# as sku on item.sys_item_id=sku.sys_item_id
        <include refid="itemStockAll.where"/>
        <include refid="filterItemSupplierSql"/>
    </select>

    <select id="getStockItemList" resultMap="dmjItemStock" timeout="30">
        SELECT item.* ,sum(stock.available_in_stock) as available_in_stock
        from
        <include refid="itemStockList.joinSql"/>
        <include refid="itemStockList.Where"/>
        <include refid="filterItemSupplierSql"/>
        <isNotEmpty property="itemQueryParams.sort">
            <isNotEmpty property="itemQueryParams.sort.field">
                <isNotEmpty property="itemQueryParams.sort.order">
                    order by
                    <encode property="itemQueryParams.sort.field"></encode>
                    <encode property="itemQueryParams.sort.order"></encode>
                </isNotEmpty>
            </isNotEmpty>
        </isNotEmpty>
        group by item.sys_item_id
        <isEmpty property="itemQueryParams.sort">
            order by item.sys_item_id desc
        </isEmpty>
        <include refid="dmjItemList.limit"/>
    </select>

    <select id="getStockSkuList" resultMap="dmjItemStock" timeout="30">
        SELECT item.* ,sum(stock.available_in_stock) as available_in_stock
        from
        <include refid="skuStockList.joinSql"/>
        <include refid="itemStockList.Where"/>
        <include refid="filterSkuSupplierSql"/>
        <isNotEmpty property="itemQueryParams.sort">
            <isNotEmpty property="itemQueryParams.sort.field">
                <isNotEmpty property="itemQueryParams.sort.order">
                    order by
                    <encode property="itemQueryParams.sort.field"></encode>
                    <encode property="itemQueryParams.sort.order"></encode>
                </isNotEmpty>
            </isNotEmpty>
        </isNotEmpty>
        group by item.sys_item_id
        <isEmpty property="itemQueryParams.sort">
            order by item.sys_item_id desc
        </isEmpty>
        <include refid="dmjItemList.limit"/>
    </select>

    <select id="DmjItem.getStockItemCount" resultClass="Long" timeout="30">
        SELECT
        count(DISTINCT(stock.sys_item_id))
        FROM
        <include refid="itemStockList.joinSql"/>
        <include refid="itemStockList.Where"/>
        <include refid="filterItemSupplierSql"/>
    </select>

    <select id="DmjItem.getSmallProItemCount" resultClass="Long" timeout="30">
        SELECT
        COUNT(*)
        FROM dmj_item_#dbInfo.dmjItemDbNo# AS item
        <include refid="joinTables4SmallPro" />
        WHERE item.company_id = #companyId#
        <include refid="itemTypeFlag" />
        AND item.is_virtual = 0
        <include refid="smallProCondition"/>
    </select>

    <select id="DmjItem.getSmallProItemList" resultMap="dmjItemMap" timeout="30">
        SELECT *
        FROM dmj_item_#dbInfo.dmjItemDbNo# AS item
        <include refid="joinTables4SmallPro" />
        WHERE item.company_id = #companyId#
        <include refid="itemTypeFlag" />
        AND item.is_virtual = 0
        <include refid="smallProCondition"/>
        <isNotEmpty property="offset">
            <isNotEmpty property="pageSize">
                <include refid="orderBy4SmallPro" />
                limit #offset#, #pageSize#
            </isNotEmpty>
        </isNotEmpty>
    </select>

    <select id="DmjItem.getStockSkuCount" resultClass="Long" timeout="30">
        SELECT
        count(DISTINCT(stock.sys_item_id))
        FROM
        <include refid="skuStockList.joinSql"/>
        <include refid="itemStockList.Where"/>
        <include refid="filterSkuSupplierSql"/>
    </select>

    <select id="getDmjSkuCount" parameterClass="ParamsKitDmjItem" resultClass="int" timeout="30">
        SELECT
        count(distinct sku.sys_sku_id)
        FROM
        dmj_sku_#dbInfo.dmjSkuDbNo# as sku
        <isNotNull property="itemQueryParams.cIds">
            <isEqual property="itemQueryParams.userId" compareValue="-1">
                LEFT JOIN dmj_item_#dbInfo.ItemDbNo# item
                ON item.company_id = #split.companyId#
                AND item.sys_item_id = sku.sys_item_id
            </isEqual>
        </isNotNull>
        <isEqual property="joinMode" compareValue="1">
            JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# as sku_bridge on sku_bridge.enable_status=1 and
            sku.sys_item_id=sku_bridge.sys_item_id and sku_bridge.sys_id=sku.sys_sku_id
        </isEqual>
        <isEqual property="joinMode" compareValue="2">
            JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# as sku_bridge on sku_bridge.enable_status=1 and
            sku.sys_item_id=sku_bridge.sys_item_id and sku_bridge.sys_id=sku.sys_sku_id
            JOIN tb_item_#dbInfo.ItemDbNo# AS tb_item ON tb_item.num_iid=sku_bridge.num_iid
        </isEqual>
        <isEqual property="itemQueryParams.needFilterAvailableStock" compareValue="true">
            LEFT JOIN (
            SELECT sys_item_id,
            sys_sku_id,
            SUM(available_in_stock) sum_available_in_stock
            FROM stock_#dbInfo.itemStockDbNo#
            WHERE company_id = #split.companyId# and enable_status = 1
            <isNotNull property="itemQueryParams.stockWarehouseIds">
                AND ware_house_id IN ( <iterate property="itemQueryParams.stockWarehouseIds" conjunction=",">
                #itemQueryParams.stockWarehouseIds[]#
            </iterate> )
            </isNotNull>
            GROUP BY sys_sku_id
            ) stock
            ON sku.sys_sku_id = stock.sys_sku_id
        </isEqual>
        <isNotEmpty property="itemQueryParams.text">
            <isEqual property="itemQueryParams.queryType" compareValue="supplierItemOuterId">
                LEFT JOIN (
                SELECT
                item_supplier_bridge.sys_item_id,
                item_supplier_bridge.sys_sku_id,
                GROUP_CONCAT(distinct item_supplier_bridge.supplier_item_outer_id) supplier_item_outer_id
                FROM item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# item_supplier_bridge
                WHERE item_supplier_bridge.company_id = #split.companyId#
                AND item_supplier_bridge.sys_sku_id > 0
                GROUP BY item_supplier_bridge.sys_item_id,item_supplier_bridge.sys_sku_id
                ) AS supplier_bridge
                ON supplier_bridge.sys_item_id = sku.sys_item_id
                AND supplier_bridge.sys_sku_id = sku.sys_sku_id
            </isEqual>
        </isNotEmpty>
        <include refid="skuStockAll.where"/>
        <!--sku 客户销售价信息过滤-->
        <include refid="dmjSkuFilter.customerFilter"/>
        <include refid="filterSkuSupplierSql"/>
    </select>


    <select id="getDmjSkuListWithPage" parameterClass="ParamsKitDmjItem" resultMap="skuStockMixVo" timeout="30">
        SELECT
        <isNotEqual property="joinMode" compareValue="0">distinct</isNotEqual>
        sku.sys_item_id,
        sku.sys_sku_id,
        sku.outer_id sku_outer_id,
        sku.properties_name,
        sku.properties_alias,
        sku.remark AS sku_remark,
        sku.pic_path AS sku_pic_path,
        sku.prop_color AS prop_color,
        sku.prop_other AS prop_other,
        sku.brand AS brand_name,
        sku.place AS place,
        sku.record AS record,
        sku.safekind AS safekind,
        sku.boxnum AS boxnum,
        sku.big_pic AS big_pic,
        sku.sku_component AS component,
        sku.purchase_price,
        sku.unit
        FROM
        dmj_sku_#dbInfo.dmjSkuDbNo# as sku
        <isNotNull property="itemQueryParams.cIds">
            <isEqual property="itemQueryParams.userId" compareValue="-1">
                LEFT JOIN dmj_item_#dbInfo.ItemDbNo# item
                ON item.company_id = #split.companyId#
                AND item.sys_item_id = sku.sys_item_id
            </isEqual>
        </isNotNull>
        <isEqual property="joinMode" compareValue="1">
            JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# as sku_bridge on sku_bridge.enable_status=1 and
            sku.sys_item_id=sku_bridge.sys_item_id and sku_bridge.sys_id=sku.sys_sku_id
        </isEqual>
        <isEqual property="joinMode" compareValue="2">
            JOIN sku_erp_bridge_#dbInfo.skuBridgeNo# as sku_bridge on sku_bridge.enable_status=1 and
            sku.sys_item_id=sku_bridge.sys_item_id and sku_bridge.sys_id=sku.sys_sku_id
            JOIN tb_item_#dbInfo.ItemDbNo# AS tb_item ON tb_item.num_iid=sku_bridge.num_iid
        </isEqual>
        <isEqual property="itemQueryParams.needFilterAvailableStock" compareValue="true">
            LEFT JOIN (
            SELECT sys_item_id,
            sys_sku_id,
            SUM(available_in_stock) sum_available_in_stock
            FROM stock_#dbInfo.itemStockDbNo#
            WHERE company_id = #split.companyId# and enable_status = 1
            <isNotNull property="itemQueryParams.stockWarehouseIds">
                AND ware_house_id IN ( <iterate property="itemQueryParams.stockWarehouseIds" conjunction=",">
                #itemQueryParams.stockWarehouseIds[]#
            </iterate> )
            </isNotNull>
            GROUP BY sys_sku_id
            ) stock
            ON sku.sys_sku_id = stock.sys_sku_id
        </isEqual>
        <isNotEmpty property="itemQueryParams.text">
            <isEqual property="itemQueryParams.queryType" compareValue="supplierItemOuterId">
                LEFT JOIN (
                SELECT
                item_supplier_bridge.sys_item_id,
                item_supplier_bridge.sys_sku_id,
                GROUP_CONCAT(distinct item_supplier_bridge.supplier_item_outer_id) supplier_item_outer_id
                FROM item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# item_supplier_bridge
                WHERE item_supplier_bridge.company_id = #split.companyId#
                AND item_supplier_bridge.sys_sku_id > 0
                GROUP BY item_supplier_bridge.sys_item_id,item_supplier_bridge.sys_sku_id
                ) AS supplier_bridge
                ON supplier_bridge.sys_item_id = sku.sys_item_id
                AND supplier_bridge.sys_sku_id = sku.sys_sku_id
            </isEqual>
        </isNotEmpty>
        <include refid="skuStockAll.where"/>
        <include refid="dmjSkuFilter.customerFilter"/>
        <include refid="filterSkuSupplierSql"/>
        <isEqual compareValue="1" property="itemQueryParams.isSort">
            order by sku.sys_item_id desc,sku.prop_color_l,sku.prop_other_l
        </isEqual>
        <include refid="dmjItemList.limit"/>
    </select>

    <!--商品库存相关查询 end-->

    <insert id="insert" parameterClass="DmjItem">
        INSERT INTO dmj_item_#dbNo#
        <dynamic prepend="(" close=")">
            <isNotEmpty prepend="," property="onSale">on_sale</isNotEmpty>
            <isNotEmpty prepend="," property="x">x</isNotEmpty>
            <isNotEmpty prepend="," property="y">y</isNotEmpty>
            <isNotEmpty prepend="," property="z">z</isNotEmpty>
            <isNotEmpty prepend="," property="periodCast">period_cast</isNotEmpty>
            <isNotEmpty prepend="," property="place">place</isNotEmpty>
            <isNotEmpty prepend="," property="age">age</isNotEmpty>
            <isNotEmpty prepend="," property="unit">unit</isNotEmpty>
            <isNotEmpty prepend="," property="brand">brand</isNotEmpty>
            <isNotEmpty prepend="," property="brandId">brand_id</isNotEmpty>
            <isNotEmpty prepend="," property="status">status</isNotEmpty>
            <isNotEmpty prepend="," property="companyId">company_id</isNotEmpty>
            <isNotEmpty prepend="," property="shortTitle">short_title</isNotEmpty>
            <isNotEmpty prepend="," property="market">market</isNotEmpty>
            <isNotEmpty prepend="," property="costPrice">cost_price</isNotEmpty>
            <isNotEmpty prepend="," property="weight">weight</isNotEmpty>
            <isNotNull prepend="," property="barcode">barcode</isNotNull>
            <isNotEmpty prepend="," property="picPath">pic_path</isNotEmpty>
            <isNotEmpty prepend="," property="title">title</isNotEmpty>
            <isNotEmpty prepend="," property="outerId">outer_id</isNotEmpty>
            <isNotEmpty prepend="," property="num">num</isNotEmpty>
            <isNotEmpty prepend="," property="remark">remark</isNotEmpty>
            <isNotEmpty prepend="," property="type">type</isNotEmpty>
            <isNotEmpty prepend="," property="catId">cat_id</isNotEmpty>
            <isNotEmpty prepend="," property="defined1">defined_1</isNotEmpty>
            <isNotEmpty prepend="," property="defined1Key">defined_1_key</isNotEmpty>
            <isNotEmpty prepend="," property="defined2">defined_2</isNotEmpty>
            <isNotEmpty prepend="," property="defined2Key">defined_2_key</isNotEmpty>
            <isNotEmpty prepend="," property="defined3">defined_3</isNotEmpty>
            <isNotEmpty prepend="," property="defined3Key">defined_3_key</isNotEmpty>
            <isNotEmpty prepend="," property="defined4">defined_4</isNotEmpty>
            <isNotEmpty prepend="," property="defined4Key">defined_4_key</isNotEmpty>
            <isNotEmpty prepend="," property="defined5">defined_5</isNotEmpty>
            <isNotEmpty prepend="," property="defined5Key">defined_5_key</isNotEmpty>
            <isNotEmpty prepend="," property="defined6">defined_6</isNotEmpty>
            <isNotEmpty prepend="," property="defined6Key">defined_6_key</isNotEmpty>
            <isNotEmpty prepend="," property="sysItemId">sys_item_id</isNotEmpty>
            <isNotEmpty prepend="," property="goodAllocation">goods_allocation</isNotEmpty>
            <isNotEmpty prepend="," property="priceOutput">selling_price</isNotEmpty>
            <isNotEmpty prepend="," property="priceImport">purchase_price</isNotEmpty>
            <isNotEmpty prepend="," property="wholesalePrice">wholesale_price</isNotEmpty>
            <isNotEmpty prepend="," property="productionDate">production_date</isNotEmpty>
            <isNotEmpty prepend="," property="sellerCids">seller_cids</isNotEmpty>
            <isNotEmpty prepend="," property="isSkuItem">is_sku_item</isNotEmpty>
            <isNotEmpty prepend="," property="isVirtual">is_virtual</isNotEmpty>
            <isNotEmpty prepend="," property="shipper">shipper</isNotEmpty>
            <isNotEmpty prepend="," property="hasSupplier">has_supplier</isNotEmpty>
            <isNotEmpty prepend="," property="isSysWeight">is_sys_weight</isNotEmpty>
            <isNotEmpty prepend="," property="declareNameZh">declare_name_zh</isNotEmpty>
            <isNotEmpty prepend="," property="declareNameEn">declare_name_en</isNotEmpty>
            <isNotEmpty prepend="," property="hsCode">hs_code</isNotEmpty>
            <isNotEmpty prepend="," property="declareAmount">declare_amount</isNotEmpty>
            <isNotEmpty prepend="," property="declareWeight">declare_weight</isNotEmpty>
            <isNotEmpty prepend="," property="isSysPriceImport">is_sys_purchase_price</isNotEmpty>
            <isNotEmpty prepend="," property="isSysPriceOutput">is_sys_selling_price</isNotEmpty>
            <isNotEmpty prepend="," property="isSysWholesalePrice">is_sys_wholesale_price</isNotEmpty>
            <isNotEmpty prepend="," property="weightAvgPrice">weight_avg_price</isNotEmpty>
            <isNotEmpty prepend="," property="nearDate">near_date</isNotEmpty>
            <isNotEmpty prepend="," property="typeTag">type_tag</isNotEmpty>
            <isNotEmpty prepend="," property="platformId">platform_id</isNotEmpty>
            <isNotEmpty prepend="," property="packmaAlarmNum">packma_alarm_num</isNotEmpty>
            <isNotEmpty prepend="," property="definedJson">defined_json</isNotEmpty>
            <isNotEmpty prepend="," property="component">component</isNotEmpty>
            <isNotEmpty prepend="," property="standard">standard</isNotEmpty>
            <isNotEmpty prepend="," property="oneIntegral">one_integral</isNotEmpty>
            <isNotEmpty prepend="," property="moreIntegral">more_integral</isNotEmpty>
            <isNotEmpty prepend="," property="onePackageIntegral">one_package_integral</isNotEmpty>
            <isNotEmpty prepend="," property="morePackageIntegral">more_package_integral</isNotEmpty>
            <isNotEmpty prepend="," property="oneInspectionIntegral">one_inspection_integral</isNotEmpty>
            <isNotEmpty prepend="," property="moreInspectionIntegral">more_inspection_integral</isNotEmpty>
            <isNotEmpty prepend="," property="oneWeightIntegral">one_weight_integral</isNotEmpty>
            <isNotEmpty prepend="," property="moreWeightIntegral">more_weight_integral</isNotEmpty>
            <isNotNull prepend="," property="record">record</isNotNull>
            <isNotNull prepend="," property="safekind">safekind</isNotNull>
            <isNotNull prepend="," property="boxnum">boxnum</isNotNull>
            <isNotNull prepend="," property="invoice">invoice</isNotNull>
            <isNotNull prepend="," property="makeGift">make_gift</isNotNull>
            <isNotNull prepend="," property="outerIdPure">outer_id_pure</isNotNull>
            <isNotNull prepend="," property="hide">hide</isNotNull>
            <isNotNull prepend="," property="bigPic">big_pic</isNotNull>
            <isNotNull prepend="," property="extendFieldValues">extend_field_values</isNotNull>
            <isNotNull prepend="," property="extendRelationInfo">extend_relation_info</isNotNull>
            <isNotNull prepend="," property="listTime">list_time</isNotNull>
            <isNotNull prepend="," property="specificationJson">specification_json</isNotNull>
            ,created
            ,modified
        </dynamic>
        VALUES
        <dynamic prepend="(" close=")">
            <isNotEmpty prepend="," property="onSale">#onSale#</isNotEmpty>
            <isNotEmpty prepend="," property="x">#x#</isNotEmpty>
            <isNotEmpty prepend="," property="y">#y#</isNotEmpty>
            <isNotEmpty prepend="," property="z">#z#</isNotEmpty>
            <isNotEmpty prepend="," property="periodCast">#periodCast#</isNotEmpty>
            <isNotEmpty prepend="," property="place">#place#</isNotEmpty>
            <isNotEmpty prepend="," property="age">#age#</isNotEmpty>
            <isNotEmpty prepend="," property="unit">#unit#</isNotEmpty>
            <isNotEmpty prepend="," property="brand">#brand#</isNotEmpty>
            <isNotEmpty prepend="," property="brandId">#brandId#</isNotEmpty>
            <isNotEmpty prepend="," property="status">#status#</isNotEmpty>
            <isNotEmpty prepend="," property="companyId">#companyId#</isNotEmpty>
            <isNotEmpty prepend="," property="shortTitle">#shortTitle#</isNotEmpty>
            <isNotEmpty prepend="," property="market">#market#</isNotEmpty>
            <isNotEmpty prepend="," property="costPrice">#costPrice#</isNotEmpty>
            <isNotEmpty prepend="," property="weight">#weight#</isNotEmpty>
            <isNotNull prepend="," property="barcode">#barcode#</isNotNull>
            <isNotEmpty prepend="," property="picPath"><!--substring(#picPath#,1,1024)-->#picPath#</isNotEmpty>
            <isNotEmpty prepend="," property="title"><!--substring(#title#,1,64)-->#title#</isNotEmpty>
            <isNotEmpty prepend="," property="outerId"><!--substring(#outerId#,1,64)-->#outerId#</isNotEmpty>
            <isNotEmpty prepend="," property="num">#num#</isNotEmpty>
            <isNotEmpty prepend="," property="remark">#remark#</isNotEmpty>
            <isNotEmpty prepend="," property="type">#type#</isNotEmpty>
            <isNotEmpty prepend="," property="catId">#catId#</isNotEmpty>
            <isNotEmpty prepend="," property="defined1">#defined1#</isNotEmpty>
            <isNotEmpty prepend="," property="defined1Key">#defined1Key#</isNotEmpty>
            <isNotEmpty prepend="," property="defined2">#defined2#</isNotEmpty>
            <isNotEmpty prepend="," property="defined2Key">#defined2Key#</isNotEmpty>
            <isNotEmpty prepend="," property="defined3">#defined3#</isNotEmpty>
            <isNotEmpty prepend="," property="defined3Key">#defined3Key#</isNotEmpty>
            <isNotEmpty prepend="," property="defined4">#defined4#</isNotEmpty>
            <isNotEmpty prepend="," property="defined4Key">#defined4Key#</isNotEmpty>
            <isNotEmpty prepend="," property="defined5">#defined5#</isNotEmpty>
            <isNotEmpty prepend="," property="defined5Key">#defined5Key#</isNotEmpty>
            <isNotEmpty prepend="," property="defined6">#defined6#</isNotEmpty>
            <isNotEmpty prepend="," property="defined6Key">#defined6Key#</isNotEmpty>
            <isNotEmpty prepend="," property="sysItemId">#sysItemId#</isNotEmpty>
            <isNotEmpty prepend="," property="goodAllocation">#goodAllocation#</isNotEmpty>
            <isNotEmpty prepend="," property="priceOutput">#priceOutput#</isNotEmpty>
            <isNotEmpty prepend="," property="priceImport">#priceImport#</isNotEmpty>
            <isNotEmpty prepend="," property="wholesalePrice">#wholesalePrice#</isNotEmpty>
            <isNotEmpty prepend="," property="productionDate">#productionDate#</isNotEmpty>
            <isNotEmpty prepend="," property="sellerCids">#sellerCids#</isNotEmpty>
            <isNotEmpty prepend="," property="isSkuItem">#isSkuItem#</isNotEmpty>
            <isNotEmpty prepend="," property="isVirtual">#isVirtual#</isNotEmpty>
            <isNotEmpty prepend="," property="shipper">#shipper#</isNotEmpty>
            <isNotEmpty prepend="," property="hasSupplier">#hasSupplier#</isNotEmpty>
            <isNotEmpty prepend="," property="isSysWeight">#isSysWeight#</isNotEmpty>
            <isNotEmpty prepend="," property="declareNameZh">#declareNameZh#</isNotEmpty>
            <isNotEmpty prepend="," property="declareNameEn">#declareNameEn#</isNotEmpty>
            <isNotEmpty prepend="," property="hsCode">#hsCode#</isNotEmpty>
            <isNotEmpty prepend="," property="declareAmount">#declareAmount#</isNotEmpty>
            <isNotEmpty prepend="," property="declareWeight">#declareWeight#</isNotEmpty>
            <isNotEmpty prepend="," property="isSysPriceImport">#isSysPriceImport#</isNotEmpty>
            <isNotEmpty prepend="," property="isSysPriceOutput">#isSysPriceOutput#</isNotEmpty>
            <isNotEmpty prepend="," property="isSysWholesalePrice">#isSysWholesalePrice#</isNotEmpty>
            <isNotEmpty prepend="," property="weightAvgPrice">#weightAvgPrice#</isNotEmpty>
            <isNotEmpty prepend="," property="nearDate">#nearDate#</isNotEmpty>
            <isNotEmpty prepend="," property="typeTag">#typeTag#</isNotEmpty>
            <isNotEmpty prepend="," property="platformId">#platformId#</isNotEmpty>
            <isNotEmpty prepend="," property="packmaAlarmNum">#packmaAlarmNum#</isNotEmpty>
            <isNotEmpty prepend="," property="definedJson">#definedJson#</isNotEmpty>
            <isNotEmpty prepend="," property="component">#component#</isNotEmpty>
            <isNotEmpty prepend="," property="standard">#standard#</isNotEmpty>
            <isNotEmpty prepend="," property="oneIntegral">#oneIntegral#</isNotEmpty>
            <isNotEmpty prepend="," property="moreIntegral">#moreIntegral#</isNotEmpty>
            <isNotEmpty prepend="," property="onePackageIntegral">#onePackageIntegral#</isNotEmpty>
            <isNotEmpty prepend="," property="morePackageIntegral">#morePackageIntegral#</isNotEmpty>
            <isNotEmpty prepend="," property="oneInspectionIntegral">#oneInspectionIntegral#</isNotEmpty>
            <isNotEmpty prepend="," property="moreInspectionIntegral">#moreInspectionIntegral#</isNotEmpty>
            <isNotEmpty prepend="," property="oneWeightIntegral">#oneWeightIntegral#</isNotEmpty>
            <isNotEmpty prepend="," property="moreWeightIntegral">#moreWeightIntegral#</isNotEmpty>
            <isNotNull prepend="," property="record">#record#</isNotNull>
            <isNotNull prepend="," property="safekind">#safekind#</isNotNull>
            <isNotNull prepend="," property="boxnum">#boxnum#</isNotNull>
            <isNotNull prepend="," property="invoice">#invoice#</isNotNull>
            <isNotNull prepend="," property="makeGift">#makeGift#</isNotNull>
            <isNotNull prepend="," property="outerIdPure">#outerIdPure#</isNotNull>
            <isNotNull prepend="," property="hide">#hide#</isNotNull>
            <isNotNull prepend="," property="bigPic">#bigPic#</isNotNull>
            <isNotNull prepend="," property="extendFieldValues">#extendFieldValues#</isNotNull>
            <isNotNull prepend="," property="extendRelationInfo">#extendRelationInfo#</isNotNull>
            <isNotNull prepend="," property="listTime">#listTime#</isNotNull>
            <isNotNull prepend="," property="specificationJson">#specificationJson#</isNotNull>
            ,now()
            ,now()
        </dynamic>
    </insert>

    <sql id="query_text">
        <isNotEmpty property="queryText" prepend="and">
            <isEqual property="queryType" compareValue="outerId">
                (di.outer_id like CONCAT('%', #queryText#, '%')
                OR
                di.sys_item_id in (select distinct sku.sys_item_id from dmj_sku_#dmjSkuDbNo# sku where sku.outer_id like
                CONCAT('%', #queryText#, '%')
                )
                )
            </isEqual>
            <isEqual property="queryType" compareValue="title">
                di.title like CONCAT('%', #queryText#, '%')
            </isEqual>
            <isEqual property="queryType" compareValue="propertiesName">
                (di.sys_item_id in (
                select distinct sku.sys_item_id from dmj_sku_#dmjSkuDbNo# sku where sku.company_id=#companyId#
                and sku.properties_name like
                CONCAT('%', #queryText#, '%')
                )
                )
            </isEqual>
            <isEqual property="queryType" compareValue="propertiesAlias">
                (di.sys_item_id in (
                select distinct sku.sys_item_id from dmj_sku_#dmjSkuDbNo# sku where sku.company_id=#companyId#
                and sku.properties_alias like
                CONCAT('%', #queryText#, '%')
                )
                )
            </isEqual>
        </isNotEmpty>
    </sql>

    <select id="DmjItem.getEmptyItemERPBridge" resultMap="dmjItemMap" parameterClass="hashMap" timeout="30">
        SELECT * FROM dmj_item_#dbNo# di
        WHERE di.company_id=#companyId#
        <!--停用-->
        <isEqual property="onSale" compareValue="4" prepend="AND">
            di.active_status = 0
        </isEqual>
        <!--正常-->
        <isEqual property="onSale" compareValue="6" prepend="AND">
            di.active_status=1
        </isEqual>
        <include refid="query_text"/>
        AND NOT exists(SELECT 1 FROM sku_erp_bridge_#SkuBridgeNo# ieb WHERE ieb.enable_status=1 and
        ieb.company_id=#companyId#
        and ieb.sys_item_id > 0 and di.sys_item_id =ieb.sys_item_id)
        <include refid="dmjItemList.limit2"/>
    </select>

    <select id="DmjItem.getEmptyItemERPBridgeTotal" resultClass="long" parameterClass="hashMap" timeout="30">
        SELECT count(1) FROM dmj_item_#dbNo# di
        WHERE di.company_id=#companyId#
        <!--停用-->
        <isEqual property="onSale" compareValue="4" prepend="AND">
            di.active_status = 0
        </isEqual>
        <!--正常-->
        <isEqual property="onSale" compareValue="6" prepend="AND">
            di.active_status=1
        </isEqual>
        <include refid="query_text"/>
        AND NOT exists(SELECT 1 FROM sku_erp_bridge_#SkuBridgeNo# ieb WHERE ieb.enable_status=1 and
        ieb.company_id=#companyId#
        and ieb.sys_item_id > 0 and di.sys_item_id =ieb.sys_item_id)
    </select>

    <sql id="bindItemWhere">
        <isNotEmpty property="itemQueryParams.isSkuItem">
            and di.is_sku_item = #itemQueryParams.isSkuItem#
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.excludeSysItemId">
            and di.sys_item_id not in(#itemQueryParams.excludeSysItemId#)
        </isNotEmpty>
        <isEqual property="itemQueryParams.searchType" compareValue="0">
            <isNotEmpty property="itemQueryParams.content">
                <isNotEmpty property="itemQueryParams.text">
                    <isEqual property="itemQueryParams.content" compareValue="title">
                        and di.title like CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="outerId">
                        and di.outer_id like CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="shortTitle">
                        and di.short_title like CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="remark">
                        and di.remark like CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                </isNotEmpty>
            </isNotEmpty>
        </isEqual>
        <isEqual property="itemQueryParams.searchType" compareValue="1">
            <isNotEmpty property="itemQueryParams.content">
                <isNotEmpty property="itemQueryParams.text">
                    <isEqual property="itemQueryParams.content" compareValue="title">
                        and di.title = #itemQueryParams.text#
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="outerId">
                        and di.outer_id = #itemQueryParams.text#
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="shortTitle">
                        and di.short_title = #itemQueryParams.text#
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="remark">
                        and di.remark = #itemQueryParams.text#
                    </isEqual>
                </isNotEmpty>
            </isNotEmpty>
        </isEqual>
    </sql>

    <select id="DmjItem.queryCanBindItemWithPage" resultMap="simpleMap" parameterClass="hashMap" timeout="30">
        SELECT
        <include refid="simpleMapColumn"/>
        FROM dmj_item_#dbInfo.dmjItemDbNo# di
        WHERE di.active_status = 1
        and di.company_id = #companyId#
        <include refid="bindItemWhere"/>
        <include refid="dmjItemList.limit"/>
    </select>
    <select id="DmjItem.queryCanBindItemWithPageTotal" resultClass="long" parameterClass="hashMap" timeout="30">
        SELECT count(1)
        FROM dmj_item_#dbInfo.dmjItemDbNo# di
        WHERE di.active_status = 1
        and di.company_id = #companyId#
        <include refid="bindItemWhere"/>
    </select>

    <!-- 更新商品库存预警 -->
    <update id="DmjItem.updateStockAlarm" parameterClass="DmjItem">
        update dmj_item_#dbNo# set
        stock_alarm_num = #stockAlarmNum# ,
        alarmed =#alarmed#,
        has_inventory =#hasInventory#,
        has_set_alarm =#hasSetAlarm#,
        has_not_set_alarm =#hasNotSetAlarm#,
        is_stock_normal =#isStockNormal#,
        over_sell =#overSell#
        WHERE active_status = 1
        and company_id=#companyId#
        and sys_item_id =#sysItemId#
    </update>

    <update id="DmjItem.updatePrice4Supplier" parameterClass="DmjItem">
        update dmj_item_#dbNo#
        <dynamic prepend="SET">
            <isNotNull property="priceImport" prepend=",">
                purchase_price=#priceImport#
            </isNotNull>
            <isNotNull property="priceOutput" prepend=",">
                selling_price=#priceOutput#
            </isNotNull>

            <isNotNull property="place" prepend=",">
                place=#place#
            </isNotNull>
            <isNotNull property="remark" prepend=",">
                remark=#remark#
            </isNotNull>
        </dynamic>
        WHERE company_id=#companyId# and sys_item_id =#sysItemId#
    </update>

    <!-- 更新商品库存预警 -->
    <update id="DmjItem.updateStockStatus4Sandbox" parameterClass="hashMap">
        update dmj_item_#dbNo#
        set
        stock_alarm_num = 0 , alarmed =0, has_inventory =1, has_set_alarm =0, has_not_set_alarm =1,
        is_stock_normal =1, over_sell =0
        WHERE active_status = 1 and company_id=#companyId# and sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIdList">
            #sysItemIdList[]#
        </iterate>
    </update>

    <select id="DmjItem.checkSysOuterIdListExists" resultClass="String" parameterClass="hashMap" timeout="30">
        SELECT i.outer_id
        FROM dmj_item_#dbNo# i
        WHERE i.company_id = #companyId# and i.outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIdList">
            #outerIdList[]#
        </iterate>
        UNION all
        SELECT s.outer_id
        FROM dmj_sku_#dmjSkuDbNo# s
        WHERE s.enable_status = 1 AND s.company_id = #companyId# and s.outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIdList">
            #outerIdList[]#
        </iterate>
    </select>

    <select id="DmjItem.checkOuterIdInItemAndSku" resultClass="String" parameterClass="hashMap" timeout="30">
        SELECT i.outer_id
        FROM dmj_item_#dbNo# i
        WHERE i.company_id = #companyId# and i.outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIdList">
            #outerIdList[]#
        </iterate>
        UNION all
        SELECT distinct i.outer_id
        FROM dmj_sku_#dmjSkuDbNo# s, dmj_item_#dbNo# i
        WHERE s.sys_item_id = i.sys_item_id
        AND s.enable_status = 1 AND s.company_id = #companyId# and s.outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIdList">
            #outerIdList[]#
        </iterate>
    </select>

    <select id="checkItemSysOuterIdExists" resultClass="String" parameterClass="hashMap" timeout="30">
        SELECT i.outer_id
        FROM dmj_item_#dbNo# i
        WHERE i.company_id = #companyId# and i.outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIdList">
            #outerIdList[]#
        </iterate>
    </select>

    <select id="DmjItem.checkSysOuterIdExists4CopyBindItem4Sku" resultClass="String" parameterClass="hashMap"
            timeout="30">
        SELECT s.outer_id
        FROM dmj_sku_#dmjSkuDbNo# s
        WHERE s.enable_status = 1
        AND s.company_id = #companyId#
        <!--AND exists(SELECT 1 FROM sku_erp_bridge_#skuErpBridgedbInfoNO# seb WHERE  seb.sys_id = s.sys_sku_id)-->
        and s.outer_id in
        <iterate open="(" close=")" conjunction="," property="outerIdList">
            #outerIdList[]#
        </iterate>
    </select>

    <sql id="querySysItemListWhere">
        AND item.type != '3'
        <!--1. 只查询指定商品的-->
        <isNotEmpty property="params.sysItemIds" prepend="AND">
            item.sys_item_id in
            <iterate open="(" close=")" conjunction="," property="params.sysItemIds">
                #params.sysItemIds[]#
            </iterate>
        </isNotEmpty>

        <isNotNull property="params.sysItemId" prepend="AND">
            item.sys_item_id = #params.sysItemId#
        </isNotNull>
        <isNotNull property="params.isSkuItem" prepend="AND">
            item.is_sku_item = #params.isSkuItem#
        </isNotNull>

        <isNotEmpty property="params.tileOuterIdList" prepend="AND">
            (
            item.outer_id in
            <iterate property="params.tileOuterIdList" open="(" close=")" conjunction=",">
                #params.tileOuterIdList[]#
            </iterate>
            OR
            item.sys_item_id in
            (
            select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
            where sku.enable_status=1
            and sku.company_id=#companyId#
            and sku.outer_id in
            <iterate property="params.tileOuterIdList" open="(" close=")" conjunction=",">
                #params.tileOuterIdList[]#
            </iterate>
            )
            )
        </isNotEmpty>

        <!--3. 查询库存状态-->
        <!--正常-->
        <isEqual property="params.stockStatus" compareValue="1" prepend="AND">
            item.is_stock_normal = 1
        </isEqual>
        <!--缺货（警戒） 设置预警 且 0<=实际可用数<预警值 -->
        <isEqual property="params.stockStatus" compareValue="2" prepend="AND">
            item.alarmed = 1
        </isEqual>
        <!--无库存-->
        <isEqual property="params.stockStatus" compareValue="3" prepend="AND">
            item.has_inventory = 0
        </isEqual>
        <!--超卖-->
        <isEqual property="params.stockStatus" compareValue="4" prepend="AND">
            item.over_sell = 1
        </isEqual>
        <isEqual property="params.onSale" compareValue="4" prepend="AND">
            item.active_status = 0
        </isEqual>
        <!--正常-->
        <isEqual property="params.onSale" compareValue="6" prepend="AND">
            item.active_status=1
            <isEqual property="needLikedSkuTable" compareValue="true">
                and sku.active_status = 1
            </isEqual>
        </isEqual>
        <isNotNull property="params.hasSupplier" prepend="AND">
            item.has_supplier = #params.hasSupplier#
        </isNotNull>

        <!-- 简称/编码-->
        <isNotEmpty property="params.text" prepend="and">
            <isNotEmpty property="params.isAccurate">
                <isNotEqual property="params.isAccurate" compareValue="1">
                    <isEqual property="params.content" compareValue="record">
                        (
                        item.record like CONCAT('%', #params.text#, '%')
                        OR
                        item.sys_item_id in
                        (
                        select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where sku.enable_status=1
                        and sku.company_id=#companyId#
                        and sku.record like CONCAT('%', #params.text#, '%')
                        )
                        )
                    </isEqual>
                    <isEqual property="params.content" compareValue="title">
                        item.title like CONCAT('%', #params.text#, '%')
                    </isEqual>
                    <isEqual property="params.content" compareValue="outerId">
                        (
                        item.outer_id like CONCAT('%', #params.text#, '%')
                        OR
                        item.sys_item_id in
                        (
                        select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where sku.enable_status=1
                        and sku.company_id=#companyId#
                        and sku.outer_id like CONCAT('%', #params.text#, '%')
                        )
                        )
                    </isEqual>

                    <isEqual property="params.content" compareValue="tileBrand">
                        (
                        item.brand in
                        <iterate property="params.tileBrandList" open="(" conjunction="," close=")">
                            #params.tileBrandList[]#
                        </iterate>
                        OR
                        item.sys_item_id in (
                            select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                            where sku.enable_status=1
                            and sku.company_id=#companyId# and sku.brand in
                            <iterate property="params.tileBrandList" open="(" conjunction="," close=")">
                                #params.tileBrandList[]#
                            </iterate>
                        )
                        )
                    </isEqual>

                    <isEqual property="params.content" compareValue="supplierOuterId">
                        (
                        item.sys_item_id in
                        (
                        select distinct sys_item_id from (
                        select distinct _item.sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# _item,
                        item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# _supplier
                        where _item.sys_item_id = _supplier.sys_item_id and _item.is_sku_item = 0 and
                        _supplier.supplier_item_outer_id like CONCAT('%', #params.text#, '%')
                        union all
                        select distinct _sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# _sku,
                        item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# _supplier
                        where _sku.sys_item_id = _supplier.sys_item_id and _sku.sys_sku_id = _supplier.sys_sku_id and
                        _supplier.supplier_item_outer_id like CONCAT('%', #params.text#, '%')
                        ) as _t
                        )
                        )
                    </isEqual>
                    <isEqual property="params.content" compareValue="shortTitle">
                        item.short_title like CONCAT('%', #params.text#, '%')
                    </isEqual>
                    <isEqual property="params.content" compareValue="remark">
                        item.remark like CONCAT('%', #params.text#, '%')
                    </isEqual>
                    <isEqual property="params.content" compareValue="skuAlias">
                        item.sys_item_id in
                        (
                        select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where sku.enable_status=1
                        and sku.company_id=#companyId#
                        and sku.properties_alias like CONCAT('%', #params.text#, '%')
                        )
                    </isEqual>
                    <isEqual property="params.content" compareValue="skuRemark">
                        item.sys_item_id in
                        (
                        select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where sku.enable_status=1
                        and sku.company_id=#companyId#
                        and sku.remark like CONCAT('%', #params.text#, '%')
                        )
                    </isEqual>
                    <isEqual property="params.content" compareValue="suiteOuterId">
                        (item.sys_item_id in
                        (
                        select isb.sys_item_id from item_suite_bridge_#dbInfo.itemSuiteBridgeDbNo# isb where isb.status
                        = 1 and isb.company_id=#companyId#
                        and
                        (isb.sub_item_id in (select item1.sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# item1
                        where item1.company_id=#companyId# and item1.outer_id like CONCAT('%',
                        #params.text#, '%'))
                        or isb.sub_sku_id in (select sku1.sys_sku_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku1
                        where sku1.enable_status=1 and sku1.company_id=#companyId# and sku1.outer_id like CONCAT('%',
                        #params.text#, '%'))
                        )
                        )
                        )
                    </isEqual>
                    <isEqual property="params.content" compareValue="suiteName">
                        (item.sys_item_id in
                        (
                        select isb.sys_item_id from item_suite_bridge_#dbInfo.itemSuiteBridgeDbNo# isb where isb.status
                        = 1 and isb.company_id=#companyId#
                        and
                        (isb.sub_item_id in (select item1.sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# item1
                        where item1.company_id=#companyId# and item1.title like CONCAT('%',
                        #params.text#, '%'))
                        or isb.sub_sku_id in (select sku1.sys_sku_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku1
                        where sku1.enable_status=1 and sku1.company_id=#companyId# and sku1.properties_name like
                        CONCAT('%', #params.text#, '%'))
                        )
                        )
                        )
                    </isEqual>
                </isNotEqual>
                <isEqual property="params.isAccurate" compareValue="1">
                    <isEqual property="params.content" compareValue="record">
                        (
                        item.record = #params.text#
                        OR
                        item.sys_item_id in
                        (
                        select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where sku.enable_status=1
                        and sku.company_id=#companyId#
                        and sku.record = #params.text#
                        )
                        )
                    </isEqual>
                    <isEqual property="params.content" compareValue="title">
                        item.title = #params.text#
                    </isEqual>
                    <isEqual property="params.content" compareValue="outerId">
                        (
                        item.outer_id = #params.text#
                        OR
                        item.sys_item_id in
                        (
                        select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where sku.enable_status=1
                        and sku.company_id=#companyId#
                        and sku.outer_id = #params.text#
                        )
                        )
                    </isEqual>

                    <isEqual property="params.content" compareValue="tileBrand">
                        (
                        item.brand in
                        <iterate property="params.tileBrandList" open="(" conjunction="," close=")">
                            #params.tileBrandList[]#
                        </iterate>
                        OR
                        item.sys_item_id in (
                        select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where sku.enable_status=1
                        and sku.company_id=#companyId# and sku.brand in
                        <iterate property="params.tileBrandList" open="(" conjunction="," close=")">
                            #params.tileBrandList[]#
                        </iterate>
                        )
                        )
                    </isEqual>


                    <isEqual property="params.content" compareValue="supplierOuterId">
                        (
                        item.sys_item_id in
                        (
                        select distinct sys_item_id from (
                        select distinct _item.sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# _item,
                        item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# _supplier
                        where _item.sys_item_id = _supplier.sys_item_id and _item.is_sku_item = 0 and
                        _supplier.supplier_item_outer_id = #params.text#
                        union all
                        select distinct _sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# _sku,
                        item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# _supplier
                        where _sku.sys_item_id = _supplier.sys_item_id and _sku.sys_sku_id = _supplier.sys_sku_id and
                        _supplier.supplier_item_outer_id = #params.text#
                        ) as _t
                        )
                        )
                    </isEqual>
                    <isEqual property="params.content" compareValue="shortTitle">
                        item.short_title = #params.text#
                    </isEqual>
                    <isEqual property="params.content" compareValue="remark">
                        item.remark = #params.text#
                    </isEqual>
                    <isEqual property="params.content" compareValue="skuAlias">
                        item.sys_item_id in
                        (
                        select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where sku.enable_status=1
                        and sku.company_id=#companyId#
                        and sku.properties_alias = #params.text#
                        )
                    </isEqual>
                    <isEqual property="params.content" compareValue="skuRemark">
                        item.sys_item_id in
                        (
                        select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku
                        where sku.enable_status=1
                        and sku.company_id=#companyId#
                        and sku.remark = #params.text#
                        )
                    </isEqual>
                    <isEqual property="params.content" compareValue="suiteOuterId">
                        (item.sys_item_id in
                        (
                        select isb.sys_item_id from item_suite_bridge_#dbInfo.itemSuiteBridgeDbNo# isb where isb.status
                        = 1 and isb.company_id=#companyId#
                        and
                        (isb.sub_item_id in (select item1.sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# item1
                        where item1.company_id=#companyId# and item1.outer_id = #params.text#)
                        or isb.sub_sku_id in (select sku1.sys_sku_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku1
                        where sku1.enable_status=1 and sku1.company_id=#companyId# and sku1.outer_id = #params.text#)
                        )
                        )
                        )
                    </isEqual>
                    <isEqual property="params.content" compareValue="suiteName">
                        (item.sys_item_id in
                        (
                        select isb.sys_item_id from item_suite_bridge_#dbInfo.itemSuiteBridgeDbNo# isb where isb.status
                        = 1 and isb.company_id=#companyId#
                        and
                        (isb.sub_item_id in (select item1.sys_item_id from dmj_item_#dbInfo.dmjItemDbNo# item1
                        where item1.company_id=#companyId# and item1.title = #params.text#)
                        or isb.sub_sku_id in (select sku1.sys_sku_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku1
                        where sku1.enable_status=1 and sku1.company_id=#companyId# and sku1.properties_name =
                        #params.text#)
                        )
                        )
                        )
                    </isEqual>
                </isEqual>
            </isNotEmpty>
            <!--不区分字段查询-->
            <isEmpty property="params.content">
                (item.title like CONCAT('%', #params.text#, '%')
                or item.short_title like CONCAT('%', #params.text#, '%')
                or item.outer_id like CONCAT('%', #params.text#, '%')
                or item.remark like CONCAT('%', #params.text#, '%')
                )
            </isEmpty>
        </isNotEmpty>
        <isNotEmpty property="params.content">
            <!--根据商品创建时间查询-->
            <isEqual property="params.content" compareValue="created" prepend="and">
                item.created between #params.startDate# and #params.endDate#
            </isEqual>
        </isNotEmpty>

        <isNotEmpty property="params.startModified" prepend="and">
            <!--根据商品创建时间查询-->
            <![CDATA[ item.modified >= #params.startModified# ]]>
        </isNotEmpty>
        <isNotEmpty property="params.endModified" prepend="and">
            <!--根据商品创建时间查询-->
            <![CDATA[ item.modified <= #params.endModified# ]]>
        </isNotEmpty>

        <isNotEmpty property="params.skuText" prepend="and">
            <isNotEmpty property="params.isAccurate">
                <isNotEqual property="params.isAccurate" compareValue="1">
                    <isEqual property="params.skuContent" compareValue="title">
                        sku.properties_name like CONCAT('%', #params.skuText#, '%')
                    </isEqual>
                </isNotEqual>
                <isEqual property="params.isAccurate" compareValue="1">
                    <isEqual property="params.skuContent" compareValue="title">
                        sku.properties_name = #params.skuText#
                    </isEqual>
                </isEqual>
            </isNotEmpty>
        </isNotEmpty>

        <!-- 积分筛选 -->
        <dynamic prepend="and">
            <isNotNull property="params.needIntegral">
                ((
                <isNotEmpty prepend="and" property="params.oneIntegralMin">
                    <![CDATA[ item.one_integral >= #params.oneIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.oneIntegralMax">
                    <![CDATA[ item.one_integral <= #params.oneIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreIntegralMin">
                    <![CDATA[ item.more_integral >= #params.moreIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreIntegralMax">
                    <![CDATA[ item.more_integral <= #params.moreIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.onePackageIntegralMin">
                    <![CDATA[ item.one_package_integral >= #params.onePackageIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.onePackageIntegralMax">
                    <![CDATA[ item.one_package_integral <= #params.onePackageIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.morePackageIntegralMin">
                    <![CDATA[ item.more_package_integral >= #params.morePackageIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.morePackageIntegralMax">
                    <![CDATA[ item.more_package_integral <= #params.morePackageIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.oneInspectionIntegralMin">
                    <![CDATA[ item.one_inspection_integral >= #params.oneInspectionIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.oneInspectionIntegralMax">
                    <![CDATA[ item.one_inspection_integral <= #params.oneInspectionIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreInspectionIntegralMin">
                    <![CDATA[ item.more_inspection_integral >= #params.moreInspectionIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreInspectionIntegralMax">
                    <![CDATA[ item.more_inspection_integral <= #params.moreInspectionIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.oneWeightIntegralMin">
                    <![CDATA[ item.one_weight_integral >= #params.oneWeightIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.oneWeightIntegralMax">
                    <![CDATA[ item.one_weight_integral <= #params.oneWeightIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreWeightIntegralMin">
                    <![CDATA[ item.more_weight_integral >= #params.moreWeightIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreWeightIntegralMax">
                    <![CDATA[ item.more_weight_integral <= #params.moreWeightIntegralMax# ]]>
                </isNotEmpty>
                )
                or
                item.sys_item_id in (select sku1.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku1
                where sku1.enable_status=1 and sku1.company_id=#companyId#
                <isNotEmpty prepend="and" property="params.oneIntegralMin">
                    <![CDATA[ sku1.sku_one_integral >= #params.oneIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.oneIntegralMax">
                    <![CDATA[ sku1.sku_one_integral <= #params.oneIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreIntegralMin">
                    <![CDATA[ sku1.sku_more_integral >= #params.moreIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreIntegralMax">
                    <![CDATA[ sku1.sku_more_integral <= #params.moreIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.onePackageIntegralMin">
                    <![CDATA[ sku1.sku_one_package_integral >= #params.onePackageIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.onePackageIntegralMax">
                    <![CDATA[ sku1.sku_one_package_integral <= #params.onePackageIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.morePackageIntegralMin">
                    <![CDATA[ sku1.sku_more_package_integral >= #params.morePackageIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.morePackageIntegralMax">
                    <![CDATA[ sku1.sku_more_package_integral <= #params.morePackageIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.oneInspectionIntegralMin">
                    <![CDATA[ sku1.sku_one_inspection_integral >= #params.oneInspectionIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.oneInspectionIntegralMax">
                    <![CDATA[ sku1.sku_one_inspection_integral <= #params.oneInspectionIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreInspectionIntegralMin">
                    <![CDATA[ sku1.sku_more_inspection_integral >= #params.moreInspectionIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreInspectionIntegralMax">
                    <![CDATA[ sku1.sku_more_inspection_integral <= #params.moreInspectionIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.oneWeightIntegralMin">
                    <![CDATA[ sku1.sku_one_weight_integral >= #params.oneWeightIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.oneWeightIntegralMax">
                    <![CDATA[ sku1.sku_one_weight_integral <= #params.oneWeightIntegralMax# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreWeightIntegralMin">
                    <![CDATA[ sku1.sku_more_weight_integral >= #params.moreWeightIntegralMin# ]]>
                </isNotEmpty>
                <isNotEmpty prepend="and" property="params.moreWeightIntegralMax">
                    <![CDATA[ sku1.sku_more_weight_integral <= #params.moreWeightIntegralMax# ]]>
                </isNotEmpty>
                ))
            </isNotNull>
        </dynamic>

        <!--2. 指定字段是否设置-->
        <isNotNull property="params.specialField" prepend="AND" open="(" close=")">
            <isEqual property="params.specialFieldStatus" compareValue="1">
                <isEqual property="params.pureSpecialField" compareValue="0">
                    ( <encode property="params.specialField"></encode> IS NOT NULL and <encode
                        property="params.specialField"></encode> !="" and item.is_sku_item=0)
                </isEqual>
                <isNotEqual property="params.pureSpecialField" compareValue="0">
                    ( <encode property="params.specialField"></encode> IS NOT NULL and <encode
                        property="params.specialField"></encode> !="" )
                </isNotEqual>
                <isNotEqual property="params.specialField" compareValue="supplier.supplier_name">
                    <isNotEmpty property="params.skuSpecialField">
                        or item.sys_item_id in (select sku1.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku1
                        where sku1.enable_status=1 and sku1.company_id=#companyId# and
                        (sku1.<encode property="params.skuSpecialField"></encode> IS NOT NULL or sku1.<encode
                            property="params.skuSpecialField"></encode> !=""
                        ))
                    </isNotEmpty>
                </isNotEqual>
            </isEqual>
            <isEqual property="params.specialFieldStatus" compareValue="2">
                <isEqual property="params.pureSpecialField" compareValue="0">
                    ((<encode property="params.specialField"></encode> IS NULL or <encode
                        property="params.specialField"></encode> ="") and item.is_sku_item=0 )
                </isEqual>
                <isNotEqual property="params.pureSpecialField" compareValue="0">
                    <encode property="params.specialField"></encode>
                    IS NULL or <encode property="params.specialField"></encode> =""
                </isNotEqual>
                <isEqual property="params.specialField" compareValue="item.x">
                    or item.y is null or item.y = "" or item.z is null or item.z = ""
                </isEqual>

                <isNotEqual property="params.specialField" compareValue="supplier.supplier_name">
                    <isNotEmpty property="params.skuSpecialField">
                        or item.sys_item_id in (select sku1.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku1
                        where sku1.enable_status=1 and sku1.company_id=#companyId# and
                        (sku1.<encode property="params.skuSpecialField"></encode> IS NULL or sku1.<encode
                            property="params.skuSpecialField"></encode>=""
                        <isEqual property="params.skuSpecialField" compareValue="x">
                            or sku1.y is null or sku1.y = "" or sku1.z is null or sku1.z = ""
                        </isEqual>
                        ))
                    </isNotEmpty>
                </isNotEqual>
            </isEqual>
        </isNotNull>

        <isNotEmpty property="params.purchasePriceScope">
            <isEqual property="params.purchasePriceScope" prepend="and" compareValue="0">
                ( item.purchase_price = 0 or item.sys_item_id in (select ds.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo#
                ds where ds.company_id=#companyId# and ds.enable_status=1 and ds.purchase_price = 0))
            </isEqual>
            <isEqual property="params.purchasePriceScope" prepend="and" compareValue="1">
                ( item.purchase_price > 0 or item.sys_item_id in (select ds.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo#
                ds where ds.company_id=#companyId# and ds.enable_status=1 and ds.purchase_price > 0))
            </isEqual>
        </isNotEmpty>
    </sql>

    <sql id="sellerCidCondition">
        <isNotNull property="params.cIds" prepend=" AND ">
            <iterate open="(" close=")" conjunction=" OR " property="params.cIds">
                item.seller_cids like CONCAT('%', #params.cIds[]#, '%')
            </iterate>
        </isNotNull>
    </sql>

    <sql id="skuWhereCondition">
        item.sys_item_id = sku.sys_item_id and
    </sql>

    <select id="DmjItem.selectAllPure" resultMap="dmjItemMap" timeout="30">
        select *
        from dmj_item_#dbNo#
        where company_id=#companyId# and is_sku_item=0
        limit #startRow#,#offsetRow#
    </select>

    <select id="queryitemMagnifier" resultMap="dmjItemMap" timeout="30">
        SELECT *
        FROM dmj_item_#dbNo#
        WHERE company_id=#companyId#
        AND is_sku_item=0
        AND outer_id = #outerId#
    </select>

    <sql id="querySysItemListJoinTable">
        <isEqual property="needLikedSkuTable" compareValue="true">
            , dmj_sku_#dbInfo.dmjSkuDbNo# sku
        </isEqual>

        <isEqual property="params.specialField" compareValue="supplier.supplier_name">
            LEFT JOIN item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier
            ON item.company_id = supplier.company_id AND item.sys_item_id = supplier.sys_item_id
        </isEqual>
    </sql>

    <sql id="querySysItemListCondition">
        <!--表示系统商品-->
        <isEqual property="params.userId" compareValue="-1">
            where
            <isEqual property="needLikedSkuTable" compareValue="true">
                <include refid="skuWhereCondition"/>
            </isEqual>
            item.company_id = #companyId#
            <include refid="querySysItemListWhere"/>
            <include refid="sellerCidCondition"/>
        </isEqual>

        <!--表示平台商品-->
        <isNotEqual property="params.userId" compareValue="-1">
            ,sku_erp_bridge_#dbInfo.skuBridgeNo# ieb
            ,tb_item_#dbInfo.itemDbNo# ti
            where
            <isEqual property="needLikedSkuTable" compareValue="true">
                <include refid="skuWhereCondition"/>
            </isEqual>
            item.company_id = #companyId#
            and ti.company_id =#companyId#
            and ieb.company_id =#companyId#
            and ieb.user_id = #params.userId#
            and ti.taobao_id = #params.taobaoId#
            and item.sys_item_id = ieb.sys_item_id
            and ieb.num_iid = ti.num_iid

            <include refid="querySysItemListWhere"/>
            <isNotNull property="params.cIds" prepend=" AND ">
                <iterate open="(" close=")" conjunction=" OR " property="params.cIds">
                    ti.seller_cids like CONCAT('%', #params.cIds[]#, '%')
                </iterate>
            </isNotNull>
        </isNotEqual>

        <!-- 需要过滤供应商权限 -->
        <isEqual property="params.filterSupplierAuth" compareValue="true" open="(" close=")" prepend="AND">
            item.sys_item_id IN (SELECT supplier.sys_item_id
            FROM item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier
            WHERE supplier.company_id=#companyId# AND supplier.sys_item_id=item.sys_item_id
            AND supplier.supplier_id IN
            <iterate property="params.tileSupplierIdList" open="(" conjunction="," close=")">
                #params.tileSupplierIdList[]#
            </iterate>
            )

            <isEqual property="params.includeNoSupplier" compareValue="true" prepend=" OR ">
                item.has_supplier = 0
            </isEqual>
        </isEqual>

        <isNotNull property="params.hide" prepend="and">
            item.hide = #params.hide#
        </isNotNull>

        <isNotEmpty property="params.isSkuItem">
            and item.is_sku_item = #params.isSkuItem#
        </isNotEmpty>

        <include refid="paramsFlag"/>
    </sql>

    <select id="querySysItemListCount" parameterClass="hashMap" resultClass="long" timeout="30">
        SELECT
        count(distinct(item.sys_item_id))
        FROM dmj_item_#dbInfo.dmjItemDbNo# AS item
        <include refid="querySysItemListJoinTable"/>
        <include refid="querySysItemListCondition"/>
    </select>

    <select id="querySysItemList" parameterClass="hashMap" resultMap="dmjItemMap" timeout="30">
        SELECT
        <isNotEqual property="params.userId" compareValue="-1"><!--表示平台商品-->
            distinct item.sys_item_id as id,
        </isNotEqual>
        <isEqual property="params.userId" compareValue="-1"><!--表示系统商品-->
            <isEqual property="needLikedSkuTable" compareValue="true">
                distinct
            </isEqual>
            item.sys_item_id as id,
        </isEqual>
        item.* FROM dmj_item_#dbInfo.dmjItemDbNo# AS item

        <include refid="querySysItemListJoinTable"/>
        <include refid="querySysItemListCondition"/>

        <isNotEmpty property="params.sort.field">
            order by
            <encode property="params.sort.field"/>
            <encode property="params.sort.order"/>
        </isNotEmpty>

        <isNotEmpty property="params.page.startRow">
            limit #params.page.startRow#,#params.page.pageSize#
        </isNotEmpty>
    </select>

    <sql id="joinTable4QuerySysItem">
        <isNotNull property="itemQueryParams.needCustomerFilter">
            LEFT JOIN item_customer_bridge_#dbInfo.itemCustomerBridgeDbNo# customer ON customer.company_id = item.company_id and customer.sys_item_id = item.sys_item_id and customer.enable_status = 1
        </isNotNull>

        <isEqual property="itemQueryParams.needJoinItemSupplierBridge" compareValue="true">
            LEFT JOIN (
            SELECT
            sys_item_id,
            GROUP_CONCAT(distinct supplier_id) supplierIds,
            GROUP_CONCAT(distinct supplier_item_outer_id) supplierItemOuterIds
            FROM item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo#
            WHERE company_id = #companyId#
            AND sys_sku_id &lt; 1
            GROUP BY sys_item_id
            ) AS supplier_bridge
            ON supplier_bridge.sys_item_id = item.sys_item_id
        </isEqual>
        <include refid="joinSumStock"/>

        <include refid="joinItemWarn"/>
    </sql>

    <sql id="querySysItemCondition">
        <include refid="querySysItemPageWhere"/>
        <include refid="paramsFlag"/>
        <include refid="cmPageQuery.ItemCodes"/>
        <include refid="dmjItemFilter.customerFilter"/>
        <include refid="filterItemSupplier"/>
        <include refid="joinSumStockWhere"/>
    </sql>

    <sql id="filterItemSupplier">
        <isEqual property="itemQueryParams.filterSupplierAuth" compareValue="true" open="(" close=")" prepend=" AND ">
            <!-- 需要过滤供应商权限 -->
            <isNotEmpty property="itemQueryParams.tileSupplierIdList">
                <iterate property="itemQueryParams.tileSupplierIdList" open="(" close=")" conjunction=" OR ">
                    FIND_IN_SET(#itemQueryParams.tileSupplierIdList[]#, supplier_bridge.supplierIds) > 0
                </iterate>
            </isNotEmpty>

            <isEqual property="itemQueryParams.includeNoSupplier" compareValue="true">
                <isNotEmpty property="itemQueryParams.tileSupplierIdList">
                    OR
                </isNotEmpty>
                supplier_bridge.supplierIds IS NULL
            </isEqual>

        </isEqual>

        <isNotEmpty property="itemQueryParams.tileSupplierItemOuterId" prepend=" AND ">
            supplier_bridge.supplierItemOuterIds LIKE CONCAT('%', #itemQueryParams.tileSupplierItemOuterId#, '%')
        </isNotEmpty>
    </sql>

    <!-- 只查系统商品的相关分页-->
    <select id="querySysItemPage" parameterClass="hashMap" resultMap="dmjItemMap" timeout="30">
        SELECT item.* FROM dmj_item_#dbInfo.dmjItemDbNo# AS item
        <include refid="joinTable4QuerySysItem"/>
        <include refid="querySysItemCondition"/>
        <include refid="dmjItemList.sortJoin"/>
        <isNotEmpty property="itemQueryParams.page.startRow">
            LIMIT #itemQueryParams.page.startRow#,#itemQueryParams.page.offsetRow#
        </isNotEmpty>
    </select>

    <select id="querySysItemPageCount" parameterClass="map" resultClass="long" timeout="30">
        SELECT COUNT(*) FROM dmj_item_#dbInfo.dmjItemDbNo# AS item
        <include refid="joinTable4QuerySysItem"/>
        <include refid="querySysItemCondition"/>
    </select>

    <!-- 更新商品库存预警 -->
    <update id="DmjItem.updateActiveStatusBySysItemId" parameterClass="hashMap">
        update dmj_item_#dbNo#
        set active_status = #activeStatus#
        WHERE company_id=#companyId# and sys_item_id =#sysItemId#
    </update>

    <!-- 更新商品分类 -->
    <update id="DmjItem.update2ResetSellerCat" parameterClass="hashMap">
        update dmj_item_#dbNo#
        set seller_cids =#nonClassifiedId#
        where company_id = #companyId#
        <isNotEmpty property="cidList">
            and
            <iterate open="(" close=")" conjunction=" OR " property="cidList">
                seller_cids like CONCAT('%', #cidList[]#, '%')
            </iterate>
        </isNotEmpty>
    </update>

    <select id="DmjItem.query4StockInit" resultMap="simpleMap" parameterClass="hashMap" timeout="30">
        select
        <include refid="simpleMapColumn"/>
        from dmj_item_#dbInfo.dmjItemDbNo# di
        where di.company_id = #companyId# and not exists(select * from
        stock_#dbInfo.itemStockDbNo#
        where sys_item_id = t.sys_item_id and sys_sku_id = 0 and ware_house_id = #warehouseId# and enable_status = 1)
        and di.active_status=1
        limit #page.startRow#, #page.pageSize#
    </select>

    <!--统计当前的所有的商品及其规格商品总数量-->
    <select id="DmjItem.countItemSkuByCompanyId" resultClass="Long" parameterClass="hashmap" timeout="30">
        select count(1) from dmj_item_#itemDbNo# as di LEFT JOIN dmj_sku_#skuDbNo# as ds on
        di.sys_item_id=ds.sys_item_id and di.company_id=ds.company_id
        where ds.enable_status=1
        <isEqual property="key" compareValue="1" prepend="and" open="(" close=")">
            di.outer_id like #text# or ds.outer_id like #text# or di.remark like #text# or ds.remark like #text#
        </isEqual>
        <isEqual property="key" compareValue="2" prepend="and" open="(" close=")">
            di.title like #text# or ds.properties_alias like #text#
        </isEqual>
    </select>
    <!--查询商品和产品所属sku，分页查询-->
    <select id="DmjItem.query4ItemSkuList" parameterClass="hashMap" resultMap="itemSkuReportModel" timeout="30">
        select di.company_id,di.sys_item_id,ds.sys_sku_id,di.pic_path,di.title, ifnull(ds.remark, di.remark) remark,
        if(ds.sys_sku_id is null,di.outer_id,ds.outer_id) as outer_id,ds.properties_name,ds.properties_alias
        from dmj_item_#dbInfo.ItemDbNo# as di
        LEFT JOIN dmj_sku_#dbInfo.SkuDbNo# as ds
        on di.sys_item_id=ds.sys_item_id and ds.company_id=#companyId# and ds.enable_status=1
        where di.company_id=#companyId#
        <isEqual property="key" compareValue="1" prepend="and" open="(" close=")">
            di.outer_id like #text# or ds.outer_id like #text# or di.remark like #text# or ds.remark like #text#
        </isEqual>
        <isEqual property="key" compareValue="2" prepend="and" open="(" close=")">
            di.title like #text# or ds.properties_alias like #text#
        </isEqual>
        ORDER BY di.modified desc
        limit #page.startRow#,#page.pageSize#
    </select>

    <select id="DmjItem.checkSuiteList" resultClass="Long" parameterClass="map" timeout="30">
        select sys_item_id from dmj_item_#dbNo#
        where company_id=#companyId#
        and type = #type#
        and sys_item_id in
        <iterate open="(" close=")" property="sysItemIdList" conjunction=",">
            #sysItemIdList[]#
        </iterate>
    </select>

    <!--根据商家编码和仓库id来查询是否存在对应的商品-->
    <select id="DmjItem.queryItemInfoExists" parameterClass="hashMap" resultClass="hashMap" timeout="30">
        select sys_item_id,REPLACE(outer_id,outer_id,"null") as sys_sku_id from dmj_item_#itemDbNo# where
        outer_id=#outerId# and company_id=#companyId# UNION select sys_item_id ,sys_sku_id from
        dmj_sku_#skuDbNo# where outer_id=#outerId# and company_id=#companyId# and enable_status=1
    </select>


    <select id="DmjItem.queryItemByNumIid" parameterClass="hashmap" resultMap="dmjItemMap" timeout="30">
        select *
        from dmj_item_#itemNo# item
        left join sku_erp_bridge_#skuBridgeNo# bridge on bridge.sku_id = '0' and item.sys_item_id = bridge.sys_id
        where
        item.company_id = #companyId#
        and
        bridge.num_iid = #numIid#
    </select>

    <sql id="itemQueryParamsTextAndContent">
        <isNotEmpty property="itemQueryParams.text" prepend="AND">
            <isEmpty property="itemQueryParams.content">
                <!--5.模糊匹配-->
                <isEqual property="itemQueryParams.searchType" compareValue="0">
                    (
                    item.outer_id like CONCAT('%', #itemQueryParams.text#, '%')
                    OR
                    item.title like CONCAT('%', #itemQueryParams.text#, '%')
                    OR
                    item.remark like CONCAT('%', #itemQueryParams.text#, '%')
                    OR
                    item.short_title like CONCAT('%', #itemQueryParams.text#, '%')
                    OR
                    item.sys_item_id in
                    (select sys_item_id from (
                    select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo#
                    where enable_status = 1
                    and company_id = #split.companyId#
                    and (outer_id like CONCAT('%', #itemQueryParams.text#, '%')
                    or remark like CONCAT('%', #itemQueryParams.text#, '%')
                    or properties_alias like CONCAT('%', #itemQueryParams.text#, '%')
                    )
                    ) p
                    )
                    )
                </isEqual>
                <isEqual property="itemQueryParams.searchType" compareValue="1">
                    (
                    item.outer_id =#itemQueryParams.text#
                    OR
                    item.title = #itemQueryParams.text#
                    OR
                    item.remark = #itemQueryParams.text#
                    OR
                    item.short_title = #itemQueryParams.text#
                    OR
                    item.sys_item_id in
                    (
                    select sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo#
                    where enable_status=1
                    and company_id=#split.companyId#
                    and outer_id = #itemQueryParams.text#
                    )
                    )
                </isEqual>
            </isEmpty>
            <isNotEmpty property="itemQueryParams.content" open="(" close=")">
                <isEqual property="itemQueryParams.searchType" compareValue="0">
                    <isEqual property="itemQueryParams.content" compareValue="itemName">item.title like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="title">item.title like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="outerId">item.outer_id like CONCAT('%',
                        #itemQueryParams.text#, '%') OR item.sys_item_id in ( select distinct sku.sys_item_id from
                        dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.enable_status=1 and sku.company_id=#split.companyId#
                        and sku.outer_id like CONCAT('%', #itemQueryParams.text#, '%') )
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="shortTitle">item.short_title like
                        CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="remark">item.remark like CONCAT('%',
                        #itemQueryParams.text#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="skuAlias">exists (select 1 from
                        dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and sku.enable_status=1
                        and sku.sys_item_id=item.sys_item_id and sku.properties_alias like CONCAT('%',
                        #itemQueryParams.text#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="skuRemark">exists (select 1 from
                        dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and sku.enable_status=1
                        and sku.sys_item_id=item.sys_item_id and sku.remark like CONCAT('%', #itemQueryParams.text#,
                        '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="supplier">item.sys_item_id in (select
                        supplier.sys_item_id from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier where
                        supplier.company_id=#split.companyId# and supplier.sys_item_id=item.sys_item_id and
                        supplier.supplier_name like CONCAT('%', #itemQueryParams.text#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="propertiesName">exists (select 1 from
                        dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and sku.enable_status=1
                        and sku.sys_item_id=item.sys_item_id and sku.properties_name like CONCAT('%',
                        #itemQueryParams.text#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="multiCode">
                        (item.barcode like CONCAT('%',#itemQueryParams.text#,'%') OR item.outer_id like CONCAT('%',#itemQueryParams.text#,'%')
                        OR item.sys_item_id in (select distinct sku.sys_item_id from
                        dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.enable_status=1 and sku.company_id=#split.companyId#
                        and (sku.outer_id like CONCAT('%',#itemQueryParams.text#,'%') OR sku.barcode like CONCAT('%',#itemQueryParams.text#,'%'))))
                    </isEqual>
                </isEqual>
                <isEqual property="itemQueryParams.searchType" compareValue="1">
                    <isEqual property="itemQueryParams.content" compareValue="itemName">item.title
                        =#itemQueryParams.text#
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="title">item.title=#itemQueryParams.text#
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="outerId">item.outer_id
                        =#itemQueryParams.text# OR item.sys_item_id in ( select distinct sku.sys_item_id from
                        dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.enable_status=1 and sku.company_id=#split.companyId#
                        and sku.outer_id=#itemQueryParams.text# )
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="shortTitle">
                        item.short_title=#itemQueryParams.text#
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="remark">
                        item.remark=#itemQueryParams.text#
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="skuAlias">exists (select 1 from
                        dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and sku.enable_status=1
                        and sku.sys_item_id=item.sys_item_id and sku.properties_alias=#itemQueryParams.text#)
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="skuRemark">exists (select 1 from
                        dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and sku.enable_status=1
                        and sku.sys_item_id=item.sys_item_id and sku.remark=#itemQueryParams.text#)
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="supplier">item.sys_item_id in (select
                        supplier.sys_item_id from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier where
                        supplier.company_id=#split.companyId# and supplier.sys_item_id=item.sys_item_id and
                        supplier.supplier_name=#itemQueryParams.text#)
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="propertiesName">exists (select 1 from
                        dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and sku.enable_status=1
                        and sku.sys_item_id=item.sys_item_id and sku.properties_name=#itemQueryParams.text#)
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="multiCode">
                        (item.barcode = #itemQueryParams.text# OR item.outer_id = #itemQueryParams.text#
                        OR item.sys_item_id in (select distinct sku.sys_item_id from
                        dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.enable_status=1 and sku.company_id=#split.companyId#
                        and (sku.outer_id= #itemQueryParams.text# OR sku.barcode = #itemQueryParams.text#)))
                    </isEqual>
                </isEqual>
            </isNotEmpty>
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.text2" prepend="AND">
            <isNotEmpty property="itemQueryParams.content2">
                <!-- 商品名称 -->
                <isEqual property="itemQueryParams.content2" compareValue="title">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">item.title like CONCAT('%',
                        #itemQueryParams.text2#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">item.title=#itemQueryParams.text2#
                    </isEqual>
                </isEqual>
                <!-- 商家编码 -->
                <isEqual property="itemQueryParams.content2" compareValue="outerId">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">
                        item.outer_id like CONCAT('%', #itemQueryParams.text2#, '%') OR item.sys_item_id in ( select
                        distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.enable_status=1 and
                        sku.company_id=#split.companyId# and sku.outer_id like CONCAT('%', #itemQueryParams.text2#, '%')
                        )
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        item.outer_id =#itemQueryParams.text2# OR item.sys_item_id in ( select distinct sku.sys_item_id
                        from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.enable_status=1 and
                        sku.company_id=#split.companyId# and sku.outer_id=#itemQueryParams.text2# )
                    </isEqual>
                </isEqual>
                <!-- 商品简称 -->
                <isEqual property="itemQueryParams.content2" compareValue="shortTitle">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">item.short_title like CONCAT('%',
                        #itemQueryParams.text2#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        item.short_title=#itemQueryParams.text2#
                    </isEqual>
                </isEqual>
                <!-- 商品备注 -->
                <isEqual property="itemQueryParams.content2" compareValue="remark">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">item.remark like CONCAT('%',
                        #itemQueryParams.text2#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        item.remark=#itemQueryParams.text2#
                    </isEqual>
                </isEqual>
                <!-- 规格别名 -->
                <isEqual property="itemQueryParams.content2" compareValue="skuAlias">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.properties_alias like
                        CONCAT('%', #itemQueryParams.text2#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and
                        sku.properties_alias=#itemQueryParams.text2#)
                    </isEqual>
                </isEqual>
                <!-- 规格备注 -->
                <isEqual property="itemQueryParams.content2" compareValue="skuRemark">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.remark like CONCAT('%',
                        #itemQueryParams.text2#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.remark=#itemQueryParams.text2#)
                    </isEqual>
                </isEqual>
                <isEqual property="itemQueryParams.content2" compareValue="supplier">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">
                        exists (select 1 from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier where
                        supplier.company_id=#split.companyId# and supplier.sys_item_id=item.sys_item_id and
                        supplier.supplier_name like CONCAT('%', #itemQueryParams.text2#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        exists (select 1 from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier where
                        supplier.company_id=#split.companyId# and supplier.sys_item_id=item.sys_item_id and
                        supplier.supplier_name=#itemQueryParams.text2#)
                    </isEqual>
                </isEqual>
                <!-- 规格属性 -->
                <isEqual property="itemQueryParams.content2" compareValue="propertiesName">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.properties_name like
                        CONCAT('%', #itemQueryParams.text2#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and
                        sku.properties_name=#itemQueryParams.text2#)
                    </isEqual>
                </isEqual>
            </isNotEmpty>
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.text3" prepend="AND">
            <isNotEmpty property="itemQueryParams.content3">
                <!-- 商品名称 -->
                <isEqual property="itemQueryParams.content3" compareValue="title">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">item.title like CONCAT('%',
                        #itemQueryParams.text3#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">item.title=#itemQueryParams.text3#
                    </isEqual>
                </isEqual>
                <!-- 商家编码 -->
                <isEqual property="itemQueryParams.content3" compareValue="outerId">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">
                        item.outer_id like CONCAT('%', #itemQueryParams.text3#, '%') OR item.sys_item_id in ( select
                        distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.enable_status=1 and
                        sku.company_id=#split.companyId# and sku.outer_id like CONCAT('%', #itemQueryParams.text3#, '%')
                        )
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        item.outer_id =#itemQueryParams.text3# OR item.sys_item_id in ( select distinct sku.sys_item_id
                        from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.enable_status=1 and
                        sku.company_id=#split.companyId# and sku.outer_id=#itemQueryParams.text3# )
                    </isEqual>
                </isEqual>
                <!-- 商品简称 -->
                <isEqual property="itemQueryParams.content3" compareValue="shortTitle">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">item.short_title like CONCAT('%',
                        #itemQueryParams.text3#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        item.short_title=#itemQueryParams.text#
                    </isEqual>
                </isEqual>
                <!-- 商品备注 -->
                <isEqual property="itemQueryParams.content3" compareValue="remark">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">item.remark like CONCAT('%',
                        #itemQueryParams.text3#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">item.remark=#itemQueryParams.text#
                    </isEqual>
                </isEqual>
                <!-- 规格别名 -->
                <isEqual property="itemQueryParams.content3" compareValue="skuAlias">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.properties_alias like
                        CONCAT('%', #itemQueryParams.text3#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and
                        sku.properties_alias=#itemQueryParams.text3#)
                    </isEqual>
                </isEqual>
                <!-- 规格备注 -->
                <isEqual property="itemQueryParams.content3" compareValue="skuRemark">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.remark like CONCAT('%',
                        #itemQueryParams.text3#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.remark=#itemQueryParams.text3#)
                    </isEqual>
                </isEqual>
                <isEqual property="itemQueryParams.content3" compareValue="supplier">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">
                        exists (select 1 from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier where
                        supplier.company_id=#split.companyId# and supplier.sys_item_id=item.sys_item_id and
                        supplier.supplier_name like CONCAT('%', #itemQueryParams.text3#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        exists (select 1 from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier where
                        supplier.company_id=#split.companyId# and supplier.sys_item_id=item.sys_item_id and
                        supplier.supplier_name=#itemQueryParams.text3#)
                    </isEqual>
                </isEqual>
                <!-- 规格属性 -->
                <isEqual property="itemQueryParams.content3" compareValue="propertiesName">
                    <isEqual property="itemQueryParams.searchType" compareValue="0">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and sku.properties_name like
                        CONCAT('%', #itemQueryParams.text3#, '%'))
                    </isEqual>
                    <isEqual property="itemQueryParams.searchType" compareValue="1">
                        exists (select 1 from dmj_sku_#dbInfo.dmjSkuDbNo# sku where sku.company_id=#split.companyId# and
                        sku.enable_status=1 and sku.sys_item_id=item.sys_item_id and
                        sku.properties_name=#itemQueryParams.text3#)
                    </isEqual>
                </isEqual>
            </isNotEmpty>
        </isNotEmpty>

    </sql>

    <sql id="where4Search">
        where item.company_id=#split.companyId#
        <isNotNull property="itemQueryParams.numIid" prepend=" AND ">tb_item.num_iid=#itemQueryParams.numIid#
        </isNotNull>
        <isNotNull property="itemQueryParams.isSkuItem" prepend=" AND ">item.is_sku_item = #itemQueryParams.isSkuItem#
        </isNotNull>

        <!--设置预警-->
        <isEqual property="itemQueryParams.alarmSetting" compareValue="0" prepend="AND">
            (
            (item.is_sku_item = 0
            AND NOT EXISTS(SELECT 1
            FROM item_warn_#dbInfo.itemWarnDbNo# warn
            WHERE item.sys_item_id = warn.sys_item_id AND item.company_id = warn.company_id AND warn.enable_status = 1
            <isNotNull property="itemQueryParams.warehouseId">
                and warn.warehouse_id = #itemQueryParams.warehouseId#
            </isNotNull>
            AND warn.stock_down > 0))
            OR (item.is_sku_item = 1
            AND EXISTS(SELECT 1
            FROM item_warn_#dbInfo.itemWarnDbNo# warn
            WHERE item.sys_item_id = warn.sys_item_id AND item.company_id = warn.company_id AND warn.enable_status = 1
            <isNotNull property="itemQueryParams.warehouseId">
                and warn.warehouse_id = #itemQueryParams.warehouseId#
            </isNotNull>
            AND warn.stock_down = 0))
            OR (item.is_sku_item = 1
            AND (SELECT count(1)
            FROM item_warn_#dbInfo.itemWarnDbNo# warn
            WHERE item.sys_item_id = warn.sys_item_id AND item.company_id = warn.company_id AND
            warn.enable_status = 1
            <isNotNull property="itemQueryParams.warehouseId">
                and warn.warehouse_id = #itemQueryParams.warehouseId#
            </isNotNull>
            AND warn.stock_down > 0)
            &lt; (SELECT count(1)
            FROM dmj_sku_#dbInfo.dmjSkuDbNo# sku
            WHERE sku.company_id = item.company_id AND sku.sys_item_id = item.sys_item_id AND
            sku.enable_status = 1))
            )
        </isEqual>

        <isEqual property="itemQueryParams.alarmSetting" compareValue="1" prepend="AND">
            exists(select 1 from item_warn_#dbInfo.itemWarnDbNo# iw where iw.enable_status=1 and
            iw.company_id=#split.companyId#
            <isNotNull property="itemQueryParams.warehouseId">
                and iw.warehouse_id = #itemQueryParams.warehouseId#
            </isNotNull>
            and iw.sys_item_id=item.sys_item_id and iw.stock_down>0
            )
        </isEqual>

        <isNotNull property="itemQueryParams.activeStatus" prepend=" AND ">item.active_status =
            #itemQueryParams.activeStatus#
        </isNotNull>

        <!--1. 只查询指定商品的-->
        <isNotEmpty property="itemQueryParams.sysItemIds" prepend="AND">
            item.sys_item_id in
            <iterate open="(" close=")" conjunction="," property="itemQueryParams.sysItemIds">
                #itemQueryParams.sysItemIds[]#
            </iterate>
        </isNotEmpty>

        <isNotNull property="itemQueryParams.sysItemId" prepend="AND">
            item.sys_item_id = #itemQueryParams.sysItemId#
        </isNotNull>
        <isNotNull property="itemQueryParams.filterSysItemId" prepend="AND">
            item.sys_item_id != #itemQueryParams.filterSysItemId#
        </isNotNull>

        <!--6. 平台信息-->
        <isNotNull property="itemQueryParams.userId">
            <!--不等于-1 查询指定平台-->
            <isNotEqual property="itemQueryParams.userId" compareValue="-1" prepend=" AND ">
                sku_bridge.user_id=#itemQueryParams.userId#
                <isNotNull property="itemQueryParams.cIds" prepend=" AND ">
                    <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">
                        tb_item.seller_cids like CONCAT('%', #itemQueryParams.cIds[]#, '%')
                    </iterate>
                </isNotNull>
            </isNotEqual>
            <isEqual property="itemQueryParams.userId" compareValue="-1">
                <isNotNull property="itemQueryParams.cIds" prepend=" AND ">
                    <iterate open="(" close=")" conjunction=" OR " property="itemQueryParams.cIds">
                        item.seller_cids like CONCAT('%', #itemQueryParams.cIds[]#, '%')
                    </iterate>
                </isNotNull>
            </isEqual>
        </isNotNull>

        <isNotNull property="itemQueryParams.isVirtual" prepend=" AND ">
            item.is_virtual=#itemQueryParams.isVirtual#
        </isNotNull>

        <!-- 是否已绑供应商 -->
        <isNotNull property="itemQueryParams.hasSupplier">
            <isEqual property="itemQueryParams.hasSupplier" compareValue="0">
                and item.has_supplier = 0
            </isEqual>
        </isNotNull>

        <!-- 是否自动 -->
        <isNotNull property="itemQueryParams.isAuto">
            AND EXISTS (
            SELECT 1 FROM
            stock_upload_rule_#dbInfo.dmjSkuDbNo# upload_rule LEFT JOIN
            dmj_sku_#dbInfo.dmjSkuDbNo# sku
            ON sku.company_id = #split.companyId#
            AND sku.sys_item_id = upload_rule.sys_item_id
            AND sku.sys_sku_id = upload_rule.sys_sku_id
            WHERE upload_rule.company_id = #split.companyId#
            AND upload_rule.enable_status = 1
            AND upload_rule.sys_item_id = item.sys_item_id
            AND ((item.is_sku_item = 0 AND upload_rule.sys_sku_id = 0 AND upload_rule.is_auto =
            <isEqual property="itemQueryParams.isAuto" compareValue="true">
                1
            </isEqual>
            <isEqual property="itemQueryParams.isAuto" compareValue="false">
                0
            </isEqual>
            ) OR (item.is_sku_item = 1 AND upload_rule.sys_sku_id != 0 AND upload_rule.is_auto =
            <isEqual property="itemQueryParams.isAuto" compareValue="true">
                1
            </isEqual>
            <isEqual property="itemQueryParams.isAuto" compareValue="false">
                0
            </isEqual>
            ))
            <isNotEmpty property="itemQueryParams.text">
                <isNotEqual property="itemQueryParams.searchType" compareValue="1" prepend=" AND ">
                    <isEqual property="itemQueryParams.content" compareValue="skuRemark">
                        sku.remark like CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="skuAlias">
                        sku.properties_alias like CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="propertiesName">
                        sku.properties_name like CONCAT('%', #itemQueryParams.text#, '%')
                    </isEqual>
                </isNotEqual>

                <isEqual property="itemQueryParams.searchType" compareValue="1" prepend=" AND ">
                    <isEqual property="itemQueryParams.content" compareValue="skuRemark">
                        sku.remark =#itemQueryParams.text#
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="skuAlias">
                        sku.properties_alias = #itemQueryParams.text#
                    </isEqual>
                    <isEqual property="itemQueryParams.content" compareValue="propertiesName">
                        sku.properties_name = #itemQueryParams.text#
                    </isEqual>
                </isEqual>
            </isNotEmpty>
            )
        </isNotNull>

        <!-- 是否赠品 -->
        <isNotNull property="itemQueryParams.gift">
            <isEqual property="itemQueryParams.gift" compareValue="true">
                and item.make_gift = 1
            </isEqual>
            <isEqual property="itemQueryParams.gift" compareValue="false">
                and item.make_gift = 0
            </isEqual>
        </isNotNull>

        <include refid="itemStockStatusCondition"/>
        <include refid="itemQueryParamsTextAndContent"/>
        <include refid="where4SearchTile"/>
    </sql>

    <sql id="whereSearchPrintStyle">
        where item.company_id=#split.companyId#
        <isNotEmpty property="itemQueryParams.outerId" prepend=" AND ">
            item.outer_id like CONCAT('%', #itemQueryParams.outerId#, '%')
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.skuOuterId" prepend=" AND ">
            item.sys_item_id in ( select distinct sku.sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# sku where
            sku.enable_status=1 and sku.company_id=#split.companyId# and sku.outer_id like CONCAT('%',
            #itemQueryParams.skuOuterId#, '%') )
        </isNotEmpty>
        <isNotEmpty property="itemQueryParams.supplierName" prepend=" AND ">
            exists (select 1 from item_supplier_bridge_#dbInfo.itemSupplierBridgeDbNo# supplier where
            supplier.company_id=#split.companyId# and supplier.sys_item_id=item.sys_item_id and supplier.supplier_name
            like CONCAT('%', #itemQueryParams.supplierName#, '%'))
        </isNotEmpty>
    </sql>

    <select id="getDmjItemList4Search" parameterClass="ParamsKitDmjItem" resultMap="dmjItemMap" timeout="30">
        SELECT
        <isNotEqual property="joinMode" compareValue="0">distinct</isNotEqual>
        item.sys_item_id as id ,
        item.*
        FROM
        <include refid="dmjItemList.joinSql"/>
        <include refid="where4Search"/>
        <include refid="itemQueryParamsFlag"/>
        <include refid="dmjItemList.sortJoin"/>
        <include refid="dmjItemList.limit"/>
    </select>

    <select id="getSearchOfTotal" parameterClass="ParamsKitDmjItem" resultClass="Long" timeout="30">
        SELECT
        count(distinct item.sys_item_id)
        FROM
        <include refid="dmjItemList.joinSql"/>
        <include refid="where4Search"/>
        <include refid="itemQueryParamsFlag"/>
    </select>


    <select id="DmjItem.getSearchPrintStyleList" parameterClass="ParamsKitDmjItem" resultMap="dmjItemMap"
            timeout="30">
        SELECT item.*
        FROM dmj_item_#dbInfo.dmjItemDbNo# AS item
        <include refid="whereSearchPrintStyle"/>
        <include refid="dmjItemList.sortJoin"/>
        <include refid="dmjItemList.limit"/>
    </select>

    <select id="DmjItem.getSearchPrintStyleCount" parameterClass="ParamsKitDmjItem" resultClass="Long" timeout="30">
        SELECT
        count(item.sys_item_id)
        FROM dmj_item_#dbInfo.dmjItemDbNo# AS item
        <include refid="whereSearchPrintStyle"/>
    </select>

    <!-- 批量更新商品的分类信息 -->
    <update id="DmjItem.batchUpdateCate" parameterClass="hashMap">
        update dmj_item_#itemNo#
        set seller_cids = #sellerCids#
        where
        company_id = #companyId#
        and
        sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </update>

    <select id="DmjItem.check4brand" parameterClass="hashMap" resultClass="Integer" timeout="30">
        SELECT count(1) from dmj_item_#itemNo# where company_id = #companyId# and brand_id = #brandId#
    </select>


    <sql id="sql4skuBind">
        <isNotEmpty prepend="filterBind">
            <isEqual property="filterBind" compareValue="1">
                <isNotEmpty property="excludeSysItemIdList">
                    di.sys_item_id not in
                    <iterate property="excludeSysItemIdList" conjunction="," open="(" close=")" prepend=" AND">
                        #excludeSysItemIdList[]#
                    </iterate>
                </isNotEmpty>
                <isNotEmpty property="excludeSysSkuIdList">
                    ds.sys_sku_id not in
                    <iterate property="excludeSysSkuIdList" conjunction="," open="(" close=")" prepend=" AND">
                        #excludeSysSkuIdList[]#
                    </iterate>
                </isNotEmpty>
            </isEqual>
        </isNotEmpty>

        <isEqual property="searchType" compareValue="0">
            <isNotEmpty property="text" prepend="and" open="(" close=")">
                <isEqual property="content" compareValue="title">
                    di.title like CONCAT('%', #text#, '%')
                </isEqual>
                <isEqual property="content" compareValue="outerId">
                    di.outer_id like CONCAT('%', #text#, '%') OR di.sys_item_id in (
                        select sys_item_id from (
                            select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# ds
                            where ds.enable_status=1 and ds.company_id=#companyId# and ( ds.outer_id like CONCAT('%', #text#, '%'))
                        ) p
                    )
                </isEqual>
                <isEqual property="content" compareValue="shortTitle">
                    di.short_title like CONCAT('%', #text#, '%')
                </isEqual>
                <isEqual property="content" compareValue="remark">
                    di.remark like CONCAT('%', #text#, '%')
                </isEqual>
                <isEqual property="content" compareValue="skuAlias">
                    di.sys_item_id in
                    (select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# ds
                    where ds.enable_status=1
                    and ds.company_id=#companyId#
                    and ds.properties_alias like CONCAT('%', #text#, '%')
                    )
                </isEqual>
                <isEqual property="content" compareValue="skuRemark">
                    di.sys_item_id in
                    (select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# ds
                    where ds.enable_status=1
                    and ds.company_id=#companyId#
                    and ds.remark like CONCAT('%', #text#, '%')
                    )
                </isEqual>
            </isNotEmpty>


            <isNotEmpty property="tilePropertiesName" prepend="and">
                di.sys_item_id in (
                    select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# ds
                    where ds.enable_status=1 and ds.company_id=#companyId# and ds.properties_name like CONCAT('%', #tilePropertiesName#, '%')
                )
            </isNotEmpty>
            <isNotEmpty property="tileSkuAlias" prepend="and">
                di.sys_item_id in (
                    select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# ds
                    where ds.enable_status=1 and ds.company_id=#companyId# and ds.properties_alias like CONCAT('%', #tileSkuAlias#, '%')
                )
            </isNotEmpty>
        </isEqual>
        <isEqual property="searchType" compareValue="1">
            <isNotEmpty property="text" prepend="and" open="(" close=")">
                <isEqual property="content" compareValue="title">
                    di.title = #text#
                </isEqual>
                <isEqual property="content" compareValue="outerId">
                    di.outer_id = #text# OR di.sys_item_id in (
                        select sys_item_id from (
                            select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# ds
                            where ds.enable_status=1 and ds.company_id=#companyId# and ( ds.outer_id = #text# )
                        ) p
                    )
                </isEqual>
                <isEqual property="content" compareValue="shortTitle">
                    di.short_title = #text#
                </isEqual>
                <isEqual property="content" compareValue="remark">
                    di.remark = #text#
                </isEqual>
                <isEqual property="content" compareValue="skuAlias">
                    di.sys_item_id in
                    (select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# ds
                    where ds.enable_status=1
                    and ds.company_id=#companyId#
                    and ds.properties_alias = #text#
                    )
                </isEqual>
                <isEqual property="content" compareValue="skuRemark">
                    di.sys_item_id in
                    (select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# ds
                    where ds.enable_status=1
                    and ds.company_id=#companyId#
                    and ds.remark = #text#
                    )
                </isEqual>
            </isNotEmpty>


            <isNotEmpty property="tilePropertiesName" prepend="and">
                di.sys_item_id in (
                    select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# ds
                    where ds.enable_status=1 and ds.company_id=#companyId# and ds.properties_name = #tilePropertiesName#
                )
            </isNotEmpty>
            <isNotEmpty property="tileSkuAlias" prepend="and">
                di.sys_item_id in (
                    select distinct sys_item_id from dmj_sku_#dbInfo.dmjSkuDbNo# ds
                    where ds.enable_status=1 and ds.company_id=#companyId# and ds.properties_alias = #tileSkuAlias#
                )
            </isNotEmpty>
        </isEqual>
        order by di.sys_item_id desc
    </sql>

    <select id="DmjItem.getDmjItemListWithPage4SkuBind" parameterClass="hashMap" resultMap="simpleMap" timeout="30">
        SELECT
        <include refid="simpleMapColumn"/>
        FROM dmj_item_#dbInfo.dmjItemDbNo# di
        WHERE di.active_status = 1
        and di.company_id = #companyId#
        <include refid="sql4skuBind"/>
        <include refid="dmjItemList.limit2"/>
    </select>

    <select id="DmjItem.getDmjItemListTotal4SkuBind" parameterClass="hashMap" resultClass="Long" timeout="30">
        SELECT count(1) FROM dmj_item_#dbInfo.dmjItemDbNo# di
        WHERE di.active_status = 1
        and di.company_id = #companyId#
        <include refid="sql4skuBind"/>
    </select>

    <select id="DmjItem.queryItemByPlatSkuId" parameterClass="hashmap" resultMap="dmjItemMap" timeout="30">
        select *
        from dmj_item_#itemNo# item
        left join sku_erp_bridge_#skuBridgeNo# bridge on item.sys_item_id = bridge.sys_item_id
        where
        item.company_id = #companyId#
        and
        bridge.sys_id = -1
        and
        bridge.sku_id = #skuId#
    </select>

    <select id="DmjItem.queryItemIdsByCond" parameterClass="hashMap" resultClass="Long" timeout="30">
        select sys_item_id
        from dmj_item_#dbNo#
        where company_id = #companyId#
        <isEqual property="cond" compareValue="outer">
            and outer_id = ''
        </isEqual>
        <isEqual property="cond" compareValue="status">
            and enable_status = 0
        </isEqual>
    </select>

    <select id="DmjItem.queryItemInfoWithIdList" resultMap="simpleMap" parameterClass="hashMap" timeout="30">
        SELECT
        <include refid="simpleMapColumn"/>
        FROM dmj_item_#dbInfo.dmjItemDbNo# di
        WHERE di.company_id = #companyId#
        <isNotEmpty property="isSkuItem">
            and di.is_sku_item = #isSkuItem#
        </isNotEmpty>
        and di.sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIdList">
            #sysItemIdList[]#
        </iterate>
    </select>

    <select id="DmjItem.getSpecifyFieldOfDmjItemInfo" resultClass="DmjItem" parameterClass="hashMap"
            remapResults="true" timeout="30">
        SELECT
        $fields$
        FROM dmj_item_#dbInfo.dmjItemDbNo#
        WHERE company_id=#companyId#
        and sys_item_id in
        <iterate conjunction="," open="(" close=")" property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </select>

    <select id="queryItemCountByCatId" parameterClass="hashMap" resultClass="java.lang.Integer">
        select count(*) from dmj_item_#itemDbNo#
        WHERE company_id = #companyId#
        and cat_id in
        <iterate conjunction="," open="(" close=")" property="catIds">
            #catIds[]#
        </iterate>
    </select>



    <!-- 批量更新商品的单位信息 -->
    <update id="batchUpdateUnit" parameterClass="hashMap">
        update dmj_item_#itemNo# set unit = #unit#
        where company_id = #companyId# and sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </update>

    <update id="DmjItem.batchUpdateShipper" parameterClass="hashMap">
        update dmj_item_#dmjItemDbNo#
        set shipper=#shipper#
        where company_id = #companyId#
        and sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIdList">
            #sysItemIdList[]#
        </iterate>
    </update>

    <select id="DmjItem.querySysItemIdsByCIds" parameterClass="hashMap" resultClass="Long" timeout="30">
        select sys_item_id
        from dmj_item_#dbNo#
        where company_id = #companyId#
        and is_virtual = 0
        <isNotEmpty prepend="and" property="type">
            type = #type#
        </isNotEmpty>
        <isNotEmpty property="cIds" prepend=" and ">
            <iterate open="(" close=")" conjunction=" OR " property="cIds">
                seller_cids like CONCAT('%', #cIds[]#, '%')
            </iterate>
        </isNotEmpty>
        <isEmpty property="cIds" prepend="and">
            (seller_cids is null or seller_cids = '')
        </isEmpty>
    </select>

    <update id="batchUpdateHasSupplier" parameterClass="hashMap">
        UPDATE dmj_item_#dmjItemDbNo#
        SET modified = NOW(),
        has_supplier = #hasSupplier#
        WHERE company_id = #companyId#
        AND sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIdList">
            #sysItemIdList[]#
        </iterate>
    </update>

    <update id="DmjItem.updateErrorPic" parameterClass="hashMap">
        update dmj_item_#dbNo#
        set pic_path = '/resources/css/build/images/no_pic.png'
        where pic_path not LIKE concat('%','http','%') AND company_id = #companyId#
    </update>

    <update id="batchUpdateField" parameterClass="hashMap">
        update dmj_item_#dmjItemDbNo#
        set modified = now()
        <isNotEmpty prepend="," property="dmjItem.periodCast">
            period_cast = #dmjItem.periodCast#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.oneIntegral">
            one_integral = #dmjItem.oneIntegral#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.moreIntegral">
            more_integral = #dmjItem.moreIntegral#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.onePackageIntegral">
            one_package_integral = #dmjItem.onePackageIntegral#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.morePackageIntegral">
            more_package_integral = #dmjItem.morePackageIntegral#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.oneInspectionIntegral">
            one_inspection_integral = #dmjItem.oneInspectionIntegral#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.moreInspectionIntegral">
            more_inspection_integral = #dmjItem.moreInspectionIntegral#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.oneWeightIntegral">
            one_weight_integral = #dmjItem.oneWeightIntegral#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.moreWeightIntegral">
            more_weight_integral = #dmjItem.moreWeightIntegral#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.hasBatch">
            has_batch = #dmjItem.hasBatch#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.batchRule">
            batch_rule = #dmjItem.batchRule#
        </isNotEmpty>
        <isNotEmpty prepend="," property="dmjItem.nearDate">
            near_date = #dmjItem.nearDate#
        </isNotEmpty>
        <isNotNull prepend="," property="dmjItem.standard">
            standard = #dmjItem.standard#
        </isNotNull>
        where company_id = #companyId#
        and sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIdList">
            #sysItemIdList[]#
        </iterate>
    </update>

    <select id="DmjItem.queryItemSkuBatchs" parameterClass="hashMap" resultMap="itemSkuBatch" timeout="30">
        select
        concat(a.sys_item_id,"_",ifnull(b.sys_sku_id,0)) `key`,
        a.sys_item_id,
        ifnull(b.sys_sku_id,0) sys_sku_id,
        ifnull(b.has_batch,a.has_batch) has_batch,
        ifnull(b.has_product,a.has_product) has_product,
        ifnull(b.allow_sale_expiring,a.allow_sale_expiring) allow_sale_expiring,
        ifnull(b.period_cast,a.period_cast) period_cast,
        ifnull(b.batch_rule,a.batch_rule) batch_rule,
        ifnull(b.near_date,a.near_date) near_date
        from dmj_item_#dbInfo.dmjItemDbNo# a
        left join dmj_sku_#dbInfo.dmjSkuDbNo# b on a.company_id=b.company_id and a.sys_item_id=b.sys_item_id and
        b.sys_sku_id in  <iterate open="(" close=")" conjunction="," property="sysSkuIds">#sysSkuIds[]#</iterate> and
        b.enable_status=1 and b.active_status=1
        where a.active_status = 1
        and a.company_id = #companyId#
        and a.sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIds">#sysItemIds[]#</iterate>
        <isNotEmpty property="hasBatch" prepend="and">ifnull(b.has_batch,a.has_batch) = #hasBatch#</isNotEmpty>
    </select>

    <select id="queryItemPlatformIdExist" parameterClass="hashMap" resultMap="platformIdMap" timeout="30">
        select platform_id, sys_item_id
        from dmj_item_#dmjItemDbNo#
        where company_id = #companyId# and
        platform_id in
        <iterate property="platformIdList" open="(" close=")" conjunction=",">
            #platformIdList[]#
        </iterate>
    </select>

    <!--修改自动计算字段-->
    <update id="batchUpdateCalculateColumn" parameterClass="DmjItem">
        update dmj_item_#dbNo#
        set modified = now()
        <isNotEmpty property="isSysWeight" prepend=",">
            is_sys_weight = #isSysWeight#
        </isNotEmpty>
        <isNotEmpty property="isSysPriceImport" prepend=",">
            is_sys_purchase_price = #isSysPriceImport#
        </isNotEmpty>
        <isNotEmpty property="isSysPriceOutput" prepend=",">
            is_sys_selling_price = #isSysPriceOutput#
        </isNotEmpty>
        <isNotEmpty property="isSysWholesalePrice" prepend=",">
            is_sys_wholesale_price = #isSysWholesalePrice#
        </isNotEmpty>
        where company_id = #companyId#
        and sys_item_id = #sysItemId#
    </update>

    <select id="queryDefinedItem" parameterClass="hashMap" resultMap="dmjItemMap" timeout="30">
        select * from dmj_item_#dbNo# where company_id = #companyId#
        and (defined_1_key!="" or defined_2_key!="" or defined_3_key!="" or defined_4_key!="" or defined_5_key!="" or
        defined_6_key!="")
    </select>

    <update id="clearItemIntegral" parameterClass="hashMap">
        update dmj_item_#dbInfo.dmjItemDbNo#
        set modified = now(),one_integral = null,more_integral = null,
        one_package_integral = null,more_package_integral = null,
        one_inspection_integral = null,more_inspection_integral = null,
        one_weight_integral = null, more_weight_integral = null,
        receive_goods_integral = null, quality_check_integral = null
        where company_id = #companyId#
        and sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIdList">
            #sysItemIdList[]#
        </iterate>
    </update>

    <update id="batchFillMoreIntegral" parameterClass="DmjItem">
        update dmj_item_#dbNo#
        set modified = now(),more_integral = #oneIntegral#
        where company_id = #companyId#
        and sys_item_id = #sysItemId#
    </update>

    <update id="batchFillMorePackageIntegral" parameterClass="DmjItem">
        update dmj_item_#dbNo#
        set modified = now(),more_package_integral = #onePackageIntegral#
        where company_id = #companyId#
        and sys_item_id = #sysItemId#
    </update>

    <update id="batchFillMoreInspectionIntegral" parameterClass="DmjItem">
        update dmj_item_#dbNo#
        set modified = now(),more_inspection_integral = #oneInspectionIntegral#
        where company_id = #companyId#
        and sys_item_id = #sysItemId#
    </update>

    <update id="batchFillMoreWeightIntegral" parameterClass="DmjItem">
        update dmj_item_#dbNo#
        set modified = now(),more_weight_integral = #oneWeightIntegral#
        where company_id = #companyId#
        and sys_item_id = #sysItemId#
    </update>

    <update id="updateRecord" parameterClass="DmjItem">
        UPDATE dmj_item_#dbNo#
        SET modified = now(),
        record = #record#
        WHERE company_id = #companyId#
        AND sys_item_id = #sysItemId#
    </update>

    <update id="updatePurchasePrice" parameterClass="map">
        UPDATE dmj_item_#itemTableNo#
        SET modified = NOW(),
        purchase_price = #purchasePrice#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate property="sysItemIds" open="(" close=")" conjunction=",">
            #sysItemIds[]#
        </iterate>
    </update>

    <select id="querySimpleDmjItemBySysItemIds" parameterClass="map" resultClass="DmjItem" timeout="30">
        SELECT
        sys_item_id sysItemId,
        outer_id outerId,
        title,
        brand_id brandId,
        seller_cids sellerCids
        FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        <iterate property="sysItemIdList" prepend=" AND sys_item_id IN " open="(" close=")" conjunction=",">
            #sysItemIdList[]#
        </iterate>
    </select>


    <update id="updateRemark" parameterClass="map">
        UPDATE dmj_item_#dbNo#
        SET modified = NOW(),
        remark = #remark#
        WHERE company_id = #companyId#
        AND sys_item_id = #sysItemId#
    </update>

    <update id="updateShortTitle" parameterClass="map">
        UPDATE dmj_item_#dbNo#
        SET modified = NOW(),
        short_title = #shortTitle#
        WHERE company_id = #companyId#
        AND sys_item_id = #sysItemId#
    </update>

    <sql id="joinTable4Stock">
        <include refid="joinSkuBridgeTable"/>

        <include refid="joinSupplierBridgeTable"/>

        <include refid="joinStockTable"/>

        <include refid="joinStockDownTable"/>

        <include refid="joinUploadRuleTable"/>

        <include refid="joinItemWarnTable"/>

        <include refid="joinStockLableTable"/>
    </sql>

    <!-- 添加平台商品关联表 -->
    <sql id="joinSkuBridgeTable">
        <isNotNull property="userId">
            LEFT JOIN (
            SELECT
            sku_erp_bridge.sys_item_id,
            GROUP_CONCAT(sku_erp_bridge.user_id) user_id
            <isNotNull property="cateIdList">
                ,GROUP_CONCAT(tb_item.seller_cids) AS seller_cids
            </isNotNull>
            FROM sku_erp_bridge_#skuBridgeTableNo# sku_erp_bridge
            <isNotNull property="cateIdList">
                LEFT JOIN tb_item_#itemTableNo# tb_item
                ON tb_item.company_id = #companyId#
                AND tb_item.num_iid = sku_erp_bridge.num_iid
            </isNotNull>
            WHERE sku_erp_bridge.company_id = #companyId#
            AND sku_erp_bridge.user_id = #userId#
            AND sku_erp_bridge.sys_item_id > 0
            AND sku_erp_bridge.sys_id &lt; 1
            <isNotEmpty property="sysItemIdList" prepend="AND">
                sku_erp_bridge.sys_item_id in
                <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                    #sysItemIdList[]#
                </iterate>
            </isNotEmpty>
            GROUP BY sku_erp_bridge.sys_item_id
            ) AS sku_bridge
            ON sku_bridge.sys_item_id = item.sys_item_id
        </isNotNull>
    </sql>

    <!-- 添加供应商商品关联表 -->
    <sql id="joinSupplierBridgeTable">
        <isEqual property="needJoinSupplierBridgeTable" compareValue="true">
            LEFT JOIN (
            SELECT
            item_supplier_bridge.sys_item_id
            <isEqual property="filterBySupplierItemOuterId" compareValue="true">
                ,GROUP_CONCAT(distinct item_supplier_bridge.supplier_item_outer_id) supplier_item_outer_id
            </isEqual>
            <isEqual property="filterSupplierAuth" compareValue="true">
                ,GROUP_CONCAT(distinct item_supplier_bridge.supplier_id) concat_supplier_id
            </isEqual>
            FROM item_supplier_bridge_#itemSupplierBridgeTableNo# item_supplier_bridge
            WHERE item_supplier_bridge.company_id = #companyId#
            AND item_supplier_bridge.sys_sku_id &lt; 1
            <isNotEmpty property="sysItemIdList" prepend="AND">
                item_supplier_bridge.sys_item_id in
                <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                    #sysItemIdList[]#
                </iterate>
            </isNotEmpty>
            GROUP BY item_supplier_bridge.sys_item_id
            ) AS supplier_bridge
            ON supplier_bridge.sys_item_id = item.sys_item_id
        </isEqual>
    </sql>

    <!-- 添加库存表 -->
    <sql id="joinStockTable">
        <!-- 总库存临时表 -->
        <isEqual property="needJoinItemTotalStockTemp" compareValue="true">
            LEFT JOIN (
            SELECT
            sys_item_id
            <isEqual property="needItemAvailableStockSumField" compareValue="true" prepend=", ">
                SUM(available_in_stock) + SUM(lock_stock) + SUM(defective_stock)
                <isEqual property="needDepotLock" compareValue="true">
                    + SUM(depot_stock)
                </isEqual>
                AS availableStockSum
            </isEqual>
            <isEqual property="needItemAvailableInStockField" compareValue="true" prepend=", ">
                SUM(available_in_stock) AS availableInStock
            </isEqual>
            <isEqual property="needDefectiveStockField" compareValue="true" prepend=", ">
                SUM(defective_stock) AS defectiveStock
            </isEqual>
            <isEqual property="needPurchaseNumField" compareValue="true" prepend=", ">
                SUM(on_way_quantity) AS purchaseNum
            </isEqual>
            <isEqual property="needVirtualStockField" compareValue="true" prepend=", ">
                SUM(virtual_stock) AS virtualStock
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="lockStock" prepend=", ">
                SUM(lock_stock) AS lockStock
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="estimatedStock" prepend=", ">
                SUM(estimated_stock) AS estimatedStock
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="totalLockStock" prepend=", ">
                SUM(lock_stock) AS totalLockStock
            </isEqual>
            FROM stock_$stockTableNo$
            WHERE company_id = #companyId#
            AND enable_status = 1
            AND sys_sku_id = 0
            <isNotNull property="warehouseId">
                AND ware_house_id = #warehouseId#
            </isNotNull>
            <isNotEmpty property="sysItemIdList" prepend="AND">
                sys_item_id in
                <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                    #sysItemIdList[]#
                </iterate>
            </isNotEmpty>
            GROUP BY sys_item_id
            ) AS totalStockTemp
            ON totalStockTemp.sys_item_id = item.sys_item_id
        </isEqual>
    </sql>

    <!-- 添加库存下限表 -->
    <sql id="joinStockDownTable">
        <isEqual property="needJoinStockDownTemp" compareValue="true">
            LEFT JOIN (
            SELECT
            item_warn.sys_item_id sys_item_id,
            SUM(item_warn.stock_down) totalStockDown
            FROM item_warn_#itemWarnTableNo# item_warn
            WHERE item_warn.company_id = #companyId#
            AND item_warn.sys_sku_id = 0
            <isNull property="warehouseId">
                AND item_warn.warehouse_id = 0
            </isNull>
            <isNotNull property="warehouseId">
                AND item_warn.warehouse_id = #warehouseId#
            </isNotNull>
            <isNotEmpty property="sysItemIdList" prepend="AND">
                item_warn.sys_item_id in
                <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                    #sysItemIdList[]#
                </iterate>
            </isNotEmpty>
            GROUP BY item_warn.sys_item_id
            ) AS tempStockDown
            ON tempStockDown.sys_item_id = item.sys_item_id
        </isEqual>
    </sql>

    <!-- 添加上传规则表 -->
    <sql id="joinUploadRuleTable">
        <isEqual property="needJoinUploadRule" compareValue="true">
            LEFT JOIN stock_upload_rule_#uploadRuleTableNo# upload_rule
            ON upload_rule.company_id = #companyId#
            AND upload_rule.sys_item_id = item.sys_item_id
            AND upload_rule.sys_sku_id = 0
            AND upload_rule.enable_status = 1
        </isEqual>
    </sql>

    <sql id="joinStockLableTable">
        <isNotNull property="stockLabelList">
            LEFT JOIN stock_label_bridge_#stockTableNo# slb
            ON slb.company_id = #companyId#
            AND slb.sys_item_id = item.sys_item_id
            AND slb.sys_sku_id = 0
            <isNotNull property="warehouseId">
                <isNotEqual property="warehouseId" compareValue="-1">
                    AND slb.warehouse_id = #warehouseId#
                </isNotEqual>
            </isNotNull>
        </isNotNull>
    </sql>

    <!-- 添加库存预警表 -->
    <sql id="joinItemWarnTable">
        <isEqual property="needJoinItemWarnTable" compareValue="true">
            LEFT JOIN item_warn_#itemWarnTableNo# item_warn
            ON item_warn.company_id = #companyId#
            AND item_warn.sys_item_id = item.sys_item_id
            AND item_warn.sys_sku_id = 0
            <isNotNull property="warehouseId">
                AND item_warn.warehouse_id = #warehouseId#
            </isNotNull>
            <isNull property="warehouseId">
                AND item_warn.warehouse_id = 0
            </isNull>
        </isEqual>
    </sql>

    <!-- 添加排序表,仅用于排序使用 -->
    <sql id="joinOrderTable">
        <isNotEmpty property="itemOrderColumn">
            <!-- 下限安全库存 -->
            <isEqual property="itemOrderColumn" compareValue="stockDown">
                LEFT JOIN (
                SELECT
                item_warn.sys_item_id sys_item_id,
                SUM(item_warn.stock_down) totalStockDown
                FROM item_warn_#itemWarnTableNo# item_warn
                WHERE item_warn.company_id = #companyId#
                AND item_warn.sys_sku_id = 0
                <isNull property="warehouseId">
                    AND item_warn.warehouse_id = 0
                </isNull>
                <isNotNull property="warehouseId">
                    AND item_warn.warehouse_id = #warehouseId#
                </isNotNull>
                <isNotEmpty property="sysItemIdList" prepend="AND">
                    item_warn.sys_item_id in
                    <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                        #sysItemIdList[]#
                    </iterate>
                </isNotEmpty>
                GROUP BY item_warn.sys_item_id
                ) AS stockDownSortTemp
                ON stockDownSortTemp.sys_item_id = item.sys_item_id
            </isEqual>

            <!-- 上限安全库存 -->
            <isEqual property="itemOrderColumn" compareValue="stockUp">
                LEFT JOIN (
                SELECT
                item_warn.sys_item_id sys_item_id,
                SUM(item_warn.stock_up) totalStockUp
                FROM item_warn_#itemWarnTableNo# item_warn
                WHERE item_warn.company_id = #companyId#
                AND item_warn.sys_sku_id = 0
                <isNull property="warehouseId">
                    AND item_warn.warehouse_id = 0
                </isNull>
                <isNotNull property="warehouseId">
                    AND item_warn.warehouse_id = #warehouseId#
                </isNotNull>
                <isNotEmpty property="sysItemIdList" prepend="AND">
                    item_warn.sys_item_id in
                    <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                        #sysItemIdList[]#
                    </iterate>
                </isNotEmpty>
                GROUP BY item_warn.sys_item_id
                ) AS stockUpSortTemp
                ON stockUpSortTemp.sys_item_id = item.sys_item_id
            </isEqual>
        </isNotEmpty>
    </sql>

    <sql id="queryItem4StockCondition">

        <include refid="queryItem4StockSimpleCondition"/>
        AND item.is_sku_item = 0

        <!-- 指定商家编码查询 -->
        <isNotNull property="outerId" prepend=" AND ">
            item.outer_id LIKE CONCAT('%', #outerId#, '%')
        </isNotNull>
        <isNotEmpty property="outerIdList" prepend=" AND ">
            <iterate property="outerIdList" open="(" close=")" conjunction=" OR ">
                item.outer_id = #outerIdList[]#
            </iterate>
        </isNotEmpty>

        <!-- 普通商品 -->
        <isEqual property="flag" compareValue="0" prepend=" AND ">
            item.type = '0'
        </isEqual>
        <!-- 套件商品 -->
        <isEqual property="flag" compareValue="1" prepend=" AND ">
            item.type IN ('1', '2')
        </isEqual>
        <!-- 加工商品 -->
        <isEqual property="flag" compareValue="6" prepend=" AND ">
            item.type = '0' AND item.type_tag IN (1, 2)
        </isEqual>
        <!-- 组合装商品 -->
        <isEqual property="flag" compareValue="9" prepend=" AND ">
            item.type = '0' AND item.type_tag IN (3, 4)
        </isEqual>
        <!-- 普通商品(不含组合、加工) -->
        <isEqual property="flag" compareValue="11" prepend=" AND ">
            item.type = '0' AND item.type_tag = 0
        </isEqual>

        <isNotEmpty property="title" prepend=" AND ">
            item.title LIKE CONCAT('%',#title#,'%')
        </isNotEmpty>

        <isNotEmpty property="propertiesName" prepend=" AND ">
            <!-- 纯商品不支持该条件，给定一个不可能的条件 -->
            item.sys_item_id = 0
        </isNotEmpty>

        <!-- 品牌 -->
        <isNotEmpty property="brand" prepend=" AND ">
            item.brand = #brand#
        </isNotEmpty>

        <!-- 品牌列表 -->
        <isNotEmpty property="brandList" prepend=" AND ">
            item.brand IN
            <iterate property="brandList" open="(" close=")" conjunction=",">
                #brandList[]#
            </iterate>
        </isNotEmpty>

        <!-- 产地 -->
        <isNotEmpty property="place" prepend=" AND ">
            item.place LIKE CONCAT('%', #place#, '%')
        </isNotEmpty>

        <!-- 商家编码 -->
        <isNotEmpty property="specialOuterId" prepend=" AND ">
            <isEqual property="specialOuterIdFlag" compareValue="0">
                item.outer_id LIKE CONCAT('%', #specialOuterId#, '%')
            </isEqual>
            <isEqual property="specialOuterIdFlag" compareValue="1">
                item.outer_id = #specialOuterId#
            </isEqual>
        </isNotEmpty>

        <isNotEmpty property="specialOuterIds" prepend=" AND ">
            item.outer_id in
            <iterate property="specialOuterIds" open="(" close=")" conjunction=",">
                #specialOuterIds[]#
            </iterate>
        </isNotEmpty>

        <isNotNull property="cateIdList" prepend=" AND ">
            <isNull property="userId">
                <iterate property="cateIdList" open="(" close=")" conjunction=" OR ">
                    FIND_IN_SET(#cateIdList[]#, item.seller_cids) > 0
                </iterate>
            </isNull>

            <isNotNull property="userId">
                <iterate property="cateIdList" open="(" close=")" conjunction=" OR ">
                    FIND_IN_SET(#cateIdList[]#, sku_bridge.seller_cids) > 0
                </iterate>
            </isNotNull>
        </isNotNull>

        <isNotNull property="userId" prepend=" AND ">
            sku_bridge.user_id IS NOT NULL
        </isNotNull>

        <!--库存状态-->
        <isNotEmpty property="stockStatusList" prepend=" AND ">
            <iterate property="stockStatusList" open="(" close=")" conjunction="OR">
                <!--正常-->
                <isEqual property="stockStatusList[]" compareValue="1">
                    (totalStockTemp.availableInStock > 0 AND (item_warn.stock_down IS NULL OR totalStockTemp.availableInStock >= item_warn.stock_down))
                </isEqual>

                <!--缺货（警戒） 设置预警 且 0<实际可用数<预警值 -->
                <isEqual property="stockStatusList[]" compareValue="2">
                    (totalStockTemp.availableInStock > 0 AND item_warn.stock_down IS NOT NULL AND totalStockTemp.availableInStock &lt; item_warn.stock_down)
                </isEqual>

                <!--无库存-->
                <isEqual property="stockStatusList[]" compareValue="3">
                    (totalStockTemp.availableInStock IS NULL OR totalStockTemp.availableInStock = 0)
                </isEqual>

                <!--超卖-->
                <isEqual property="stockStatusList[]" compareValue="4">
                    totalStockTemp.availableInStock &lt; 0
                </isEqual>
            </iterate>
        </isNotEmpty>

        <!--模糊匹配-->
        <isNotEmpty property="text" prepend="AND" open="(" close=")">
            <isEqual property="searchType" compareValue="0">
                <!-- 商家编码 -->
                <isEqual property="queryType" compareValue="outerId">
                    item.outer_id LIKE CONCAT('%', #text#, '%')
                    OR item.sys_item_id IN (
                    SELECT sys_item_id FROM dmj_sku_#skuTableNo#
                    WHERE company_id = #companyId#
                    AND outer_id LIKE CONCAT('%', #text#, '%')
                    )
                </isEqual>

                <!-- 商品名称 -->
                <isEqual property="queryType" compareValue="itemName">
                    item.title LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 商品简称 -->
                <isEqual property="queryType" compareValue="shortTitle">
                    item.short_title LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 商品备注 -->
                <isEqual property="queryType" compareValue="remark">
                    item.remark LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 规格属性 -->
                <isEqual property="queryType" compareValue="propertiesName">
                    item.sys_item_id IN (
                    SELECT sys_item_id FROM dmj_sku_#skuTableNo#
                    WHERE company_id = #companyId#
                    AND properties_name LIKE CONCAT('%', #text#, '%')
                    )
                </isEqual>

                <!-- 规格别名 -->
                <isEqual property="queryType" compareValue="skuAlias">
                    item.sys_item_id IN (
                    SELECT sys_item_id FROM dmj_sku_#skuTableNo#
                    WHERE company_id = #companyId#
                    AND properties_alias LIKE CONCAT('%', #text#, '%')
                    )
                </isEqual>

                <!-- 规格备注 -->
                <isEqual property="queryType" compareValue="skuRemark">
                    item.sys_item_id IN (
                    select sys_item_id FROM dmj_sku_#skuTableNo#
                    WHERE company_id = #companyId#
                    AND remark LIKE CONCAT('%', #text#, '%')
                    )
                </isEqual>

                <!-- 供应商商家编码 -->
                <isEqual property="queryType" compareValue="supplierItemOuterId">
                    supplier_bridge.supplier_item_outer_id LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 商品记录 -->
                <isEqual property="queryType" compareValue="record">
                    item.record LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 商品条形码 -->
                <isEqual property="queryType" compareValue="itemBarcode">
                    item.barcode LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 规格条形码 -->
                <isEqual property="queryType" compareValue="skuBarcode">
                    item.sys_item_id IN (
                    SELECT sys_item_id FROM dmj_sku_#skuTableNo#
                    WHERE company_id = #companyId#
                    AND barcode LIKE CONCAT('%', #text#, '%')
                    )
                </isEqual>
            </isEqual>
            <isEqual property="searchType" compareValue="1">
                <!-- 商家编码 -->
                <isEqual property="queryType" compareValue="outerId">
                    item.outer_id = #text#
                    OR item.sys_item_id IN (
                    SELECT sys_item_id FROM dmj_sku_#skuTableNo#
                    WHERE company_id = #companyId#
                    AND outer_id = #text#
                    )
                </isEqual>

                <!-- 商品名称 -->
                <isEqual property="queryType" compareValue="itemName">
                    item.title = #text#
                </isEqual>

                <!-- 商品简称 -->
                <isEqual property="queryType" compareValue="shortTitle">
                    item.short_title = #text#
                </isEqual>

                <!-- 商品备注 -->
                <isEqual property="queryType" compareValue="remark">
                    item.remark = #text#
                </isEqual>

                <!-- 规格属性 -->
                <isEqual property="queryType" compareValue="propertiesName">
                    item.sys_item_id IN (
                    SELECT sys_item_id FROM dmj_sku_#skuTableNo#
                    WHERE company_id = #companyId#
                    AND properties_name = #text#
                    )
                </isEqual>

                <!-- 规格别名 -->
                <isEqual property="queryType" compareValue="skuAlias">
                    item.sys_item_id IN (
                    SELECT sys_item_id FROM dmj_sku_#skuTableNo#
                    WHERE company_id = #companyId#
                    AND properties_alias = #text#
                    )
                </isEqual>

                <!-- 规格备注 -->
                <isEqual property="queryType" compareValue="skuRemark">
                    item.sys_item_id IN (
                    SELECT sys_item_id FROM dmj_sku_#skuTableNo#
                    WHERE company_id = #companyId#
                    AND remark = #text#
                    )
                </isEqual>

                <!-- 供应商商家编码 -->
                <isEqual property="queryType" compareValue="supplierItemOuterId">
                    FIND_IN_SET(#text#, supplier_bridge.supplier_item_outer_id) > 0
                </isEqual>

                <!-- 商品记录 -->
                <isEqual property="queryType" compareValue="record">
                    item.record = #text#
                </isEqual>

                <!-- 商品条形码 -->
                <isEqual property="queryType" compareValue="itemBarcode">
                    item.barcode = #text#
                </isEqual>

                <!-- 规格条形码 -->
                <isEqual property="queryType" compareValue="skuBarcode">
                    item.sys_item_id IN (
                    SELECT sys_item_id FROM dmj_sku_$skuTableNo$
                    WHERE company_id = #companyId#
                    AND barcode = #text#
                    )
                </isEqual>
            </isEqual>
        </isNotEmpty>

        <isNotNull property="autoUpload" prepend=" AND ">
            <isEqual property="autoUpload" compareValue="true">
                upload_rule.is_auto = 1
            </isEqual>
            <isEqual property="autoUpload" compareValue="false">
                upload_rule.is_auto = 0
            </isEqual>
        </isNotNull>

        <isNotNull property="stockLabelList" prepend="AND">
            slb.label_id IN
            <iterate open="(" close=")" conjunction="," property="stockLabelList">
                #stockLabelList[]#
            </iterate>
        </isNotNull>

        <isNotNull property="catIdList" prepend=" AND ">
            item.cat_id IN
            <iterate property="catIdList" open="(" close=")" conjunction=",">
                #catIdList[]#
            </iterate>
        </isNotNull>

        <include refid="itemWarnCondition"/>

        <include refid="itemStockCondition"/>

        <include refid="filterSupplierCondition"/>
    </sql>

    <!--库存查询简单的过滤条件(不需要Join其他表的一些条件)-->
    <sql id="queryItem4StockSimpleCondition">
        WHERE item.company_id = #companyId#
        AND item.active_status = 1

        <isNotEmpty property="mainOuterId" prepend=" AND ">
            item.outer_id LIKE CONCAT('%',#mainOuterId#,'%')
        </isNotEmpty>

        <isNotEmpty property="mainOuterIds" prepend=" AND ">
            item.outer_id IN
            <iterate property="mainOuterIds" open="(" close=")" conjunction=",">
                #mainOuterIds[]#
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="skuOuterId" prepend=" AND ">
            item.outer_id LIKE CONCAT('%',#skuOuterId#,'%')
        </isNotEmpty>

        <isNotEmpty property="skuOuterIds" prepend=" AND ">
            item.outer_id IN
            <iterate property="skuOuterIds" open="(" close=")" conjunction=",">
                #skuOuterIds[]#
            </iterate>
        </isNotEmpty>

        <!-- 指定商品查询 -->
        <isNotEmpty property="sysItemIdList" prepend=" AND ">
            item.sys_item_id IN
            <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                #sysItemIdList[]#
            </iterate>
        </isNotEmpty>
    </sql>

    <sql id="filterSupplierCondition">
        <isEqual property="filterSupplierAuth" compareValue="true" prepend=" AND " open="(" close=")">
            <isNotEmpty property="supplierIdList">
                <iterate property="supplierIdList" conjunction=" OR ">
                    FIND_IN_SET(#supplierIdList[]#, supplier_bridge.concat_supplier_id) > 0
                </iterate>
            </isNotEmpty>

            <isNotNull property="includeNoSupplier">
                <isEqual property="includeNoSupplier" compareValue="true">
                    <isNotEmpty property="supplierIdList">
                        OR
                    </isNotEmpty>
                    supplier_bridge.concat_supplier_id IS NULL
                </isEqual>
            </isNotNull>
        </isEqual>
    </sql>

    <sql id="itemWarnCondition">
        <!--预警设置-->
        <isNotNull property="hasAlarmSetting" prepend=" AND ">
            <isEqual property="hasAlarmSetting" compareValue="true">
                item_warn.stock_down > 0
            </isEqual>
            <isEqual property="hasAlarmSetting" compareValue="false">
                (item_warn.stock_down = 0 OR item_warn.stock_down IS NULL)
            </isEqual>
        </isNotNull>

        <!-- 警戒库存过滤 -->
        <isNotNull property="minWarnStockDown" prepend=" AND ">
            (item_warn.stock_down >= #minWarnStockDown#
            <isEqual property="minWarnStockDown" compareValue="0">
                OR item_warn.stock_down IS NULL
            </isEqual>
            )
        </isNotNull>

        <isNotNull property="maxWarnStockDown" prepend=" AND ">
            (<![CDATA[
                item_warn.stock_down <= #maxWarnStockDown#
            ]]>
            <isEqual property="maxWarnStockDown" compareValue="0">
                OR item_warn.stock_down IS NULL
            </isEqual>
            )
        </isNotNull>

        <isNotNull property="minWarnStockUp" prepend=" AND ">
            (item_warn.stock_up >= #minWarnStockUp#
            <isEqual property="minWarnStockUp" compareValue="0">
                OR item_warn.stock_up IS NULL
            </isEqual>
            )
        </isNotNull>

        <isNotNull property="maxWarnStockUp" prepend=" AND ">
            (<![CDATA[
                item_warn.stock_up <= #maxWarnStockUp#
            ]]>
            <isEqual property="maxWarnStockUp" compareValue="0">
                OR item_warn.stock_up IS NULL
            </isEqual>
            )
        </isNotNull>

        <isNotNull property="minWarnDaysDown" prepend=" AND ">
            (item_warn.days_down >= #minWarnDaysDown#
            <isEqual property="minWarnDaysDown" compareValue="0">
                OR item_warn.days_down IS NULL
            </isEqual>
            )
        </isNotNull>

        <isNotNull property="maxWarnDaysDown" prepend=" AND ">
            (<![CDATA[
                item_warn.days_down <= #maxWarnDaysDown#
            ]]>
            <isEqual property="maxWarnDaysDown" compareValue="0">
                OR item_warn.days_down IS NULL
            </isEqual>
            )
        </isNotNull>

        <isNotNull property="minWarnDaysUp" prepend=" AND ">
            (item_warn.days_up >= #minWarnDaysUp#
            <isEqual property="minWarnDaysUp" compareValue="0">
                OR item_warn.days_up IS NULL
            </isEqual>
            )
        </isNotNull>

        <isNotNull property="maxWarnDaysUp" prepend=" AND ">
            (<![CDATA[
                item_warn.days_up <= #maxWarnDaysUp#
            ]]>
            <isEqual property="maxWarnDaysUp" compareValue="0">
                OR item_warn.days_up IS NULL
            </isEqual>
            )
        </isNotNull>
    </sql>

    <!-- 库存过滤条件 -->
    <sql id="itemStockCondition">

        <isNotNull property="minAvailableStockSum" prepend=" AND ">
            totalStockTemp.availableStockSum >= #minAvailableStockSum#
        </isNotNull>

        <isNotNull property="maxAvailableStockSum" prepend=" AND ">
            totalStockTemp.availableStockSum &lt;= #maxAvailableStockSum#
        </isNotNull>

        <isNotNull property="minAvailableStock" prepend=" AND ">
            totalStockTemp.availableInStock >= #minAvailableStock#
        </isNotNull>

        <isNotNull property="maxAvailableStock" prepend=" AND ">
            totalStockTemp.availableInStock &lt;= #maxAvailableStock#
        </isNotNull>

        <isNotNull property="minDefectiveStock" prepend=" AND ">
            totalStockTemp.defectiveStock >= #minDefectiveStock#
        </isNotNull>
        <isNotNull property="maxDefectiveStock" prepend=" AND ">
            totalStockTemp.defectiveStock &lt;= #maxDefectiveStock#
        </isNotNull>

        <isNotNull property="minPurchaseNum" prepend=" AND ">
            totalStockTemp.purchaseNum >= #minPurchaseNum#
        </isNotNull>
        <isNotNull property="maxPurchaseNum" prepend=" AND ">
            totalStockTemp.purchaseNum &lt;= #maxPurchaseNum#
        </isNotNull>

        <isNotNull property="minTotalAvailableStockSum" prepend=" AND ">
            totalStockTemp.availableStockSum >= #minTotalAvailableStockSum#
        </isNotNull>

        <isNotNull property="maxTotalAvailableStockSum" prepend=" AND ">
            totalStockTemp.availableStockSum &lt;= #maxTotalAvailableStockSum#
        </isNotNull>

        <isNotNull property="minTotalAvailableStock" prepend=" AND ">
            totalStockTemp.availableInStock >= #minTotalAvailableStock#
        </isNotNull>

        <isNotNull property="maxTotalAvailableStock" prepend=" AND ">
            totalStockTemp.availableInStock &lt;= #maxTotalAvailableStock#
        </isNotNull>

        <isNotNull property="minStockWarnDiff" prepend=" AND ">
            totalStockTemp.availableInStock - tempStockDown.totalStockDown >= #minStockWarnDiff#
        </isNotNull>

        <isNotNull property="maxStockWarnDiff" prepend=" AND ">
            totalStockTemp.availableInStock - tempStockDown.totalStockDown &lt;= #maxStockWarnDiff#
        </isNotNull>

        <isNotNull property="minVirtualStock" prepend=" AND ">
            totalStockTemp.virtualStock >= #minVirtualStock#
        </isNotNull>
        <isNotNull property="maxVirtualStock" prepend=" AND ">
            totalStockTemp.virtualStock &lt;= #maxVirtualStock#
        </isNotNull>

    </sql>

    <select id="queryItemCount4Stock" parameterClass="Item4StockQueryParams" resultClass="int" timeout="30">
        SELECT COUNT(distinct item.sys_item_id) FROM dmj_item_#itemTableNo# item
        <include refid="joinTable4Stock"/>
        <include refid="queryItem4StockCondition"/>
    </select>

    <select id="queryItemList4Stock" parameterClass="Item4StockQueryParams" resultMap="dmjItemMap" timeout="30">
        SELECT distinct item.sys_item_id id, item.*
        FROM dmj_item_#itemTableNo# item
        <include refid="joinTable4Stock"/>
        <include refid="joinOrderTable"/>
        <include refid="queryItem4StockCondition"/>
        ORDER BY
        <isNotEmpty property="itemOrderColumn">
            <isEqual property="itemOrderColumn" compareValue="outer_id">
                item.outer_id <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="created">
                item.sys_item_id DESC
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="availableStock">
                totalStockTemp.availableInStock <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="availableStockSum">
                totalStockTemp.availableStockSum <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>


            <isEqual property="itemOrderColumn" compareValue="totalAvailableStockSum">
                totalStockTemp.availableStockSum <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="totalAvailableStock">
                totalStockTemp.availableInStock <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="stockDown">
                stockDownSortTemp.totalStockDown <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="stockUp">
                stockUpSortTemp.totalStockUp <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="purchaseNum">
                totalStockTemp.purchaseNum <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="lockStock">
                totalStockTemp.lockStock <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="estimatedStock">
                totalStockTemp.estimatedStock <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="virtualStock">
                totalStockTemp.virtualStock <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="totalAvailableStockMoneySum">
                (totalStockTemp.availableStockSum * item.purchase_price) <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="totalPurchaseNum">
                totalStockTemp.purchaseNum <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="totalLockStock">
                totalStockTemp.totalLockStock <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>

            <isEqual property="itemOrderColumn" compareValue="saleOneDays">
                item.sys_item_id DESC
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="saleSevenDays">
                item.sys_item_id DESC
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="saleFifteenDays">
                item.sys_item_id DESC
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="saleThirtyDays">
                item.sys_item_id DESC
            </isEqual>
        </isNotEmpty>
        <isEmpty property="itemOrderColumn">
            item.sys_item_id DESC
        </isEmpty>
        LIMIT #page.startRow#,#page.offsetRow#
    </select>

    <select id="queryItemIdInfoList4Stock" parameterClass="Item4StockQueryParams" resultClass="ItemIdInfo">
        SELECT
        item.sys_item_id AS sysItemId,
        0 AS sysSkuId
        FROM dmj_item_#itemTableNo# item
        <include refid="joinTable4Stock"/>
        <include refid="joinOrderTable"/>
        <include refid="queryItem4StockCondition"/>
        ORDER BY item.sys_item_id
        LIMIT #page.startRow#,#page.offsetRow#
    </select>

    <select id="queryItemCount" parameterClass="map" resultClass="int">
        SELECT COUNT(*) FROM dmj_item_#itemDbNo# WHERE company_id = #companyId#
    </select>

    <select id="querySimpleItemList" parameterClass="map" resultClass="SimpleItem">
        SELECT
        sys_item_id sysItemId,
        is_sku_item isSkuItem
        FROM dmj_item_#itemDbNo#
        WHERE company_id = #companyId#
        ORDER BY sys_item_id ASC
        LIMIT #page.startRow#,#page.pageSize#
    </select>

    <select id="querySysItemIdsByParams" parameterClass="SysItemQueryParams" resultClass="long" timeout="30">
        SELECT dmj_item.sys_item_id FROM dmj_item_#dmjItemTableNo# dmj_item

        <isEqual property="needSkuBridgeTable" compareValue="true">
            LEFT JOIN sku_erp_bridge_#skuBridgeTableNo# sku_bridge
            ON sku_bridge.company_id = #companyId#
            AND sku_bridge.sys_item_id = dmj_item.sys_item_id
            AND sku_bridge.sys_id = 0
        </isEqual>

        <isEqual property="needTbItemTable" compareValue="true">
            LEFT JOIN tb_item_#itemTableNo# tb_item
            ON tb_item.company_id = #companyId#
            AND tb_item.num_iid = sku_bridge.num_iid
        </isEqual>

        <isEqual property="needTbSkuTable" compareValue="true">
            LEFT JOIN tb_sku_#itemTableNo# tb_sku
            ON tb_sku.company_id = #companyId#
            AND tb_sku.num_iid = sku_bridge.num_iid
            AND tb_sku.sku_id = sku_bridge.sku_id
        </isEqual>

        WHERE dmj_item.company_id = #companyId#
        AND dmj_item.is_sku_item = 0

        <isNotNull property="userId" prepend=" AND ">
            sku_bridge.user_id = #userId#
        </isNotNull>

        <isNotEmpty property="outerIdList" prepend="AND">
            dmj_item.outer_id in
            <iterate property="outerIdList" open="(" close=")" conjunction=",">
                #outerIdList[]#
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="platOuterIdList" prepend="AND">
            (
            tb_item.outer_id in
            <iterate property="platOuterIdList" open="(" close=")" conjunction=",">
                #platOuterIdList[]#
            </iterate>
            or
            tb_sku.outer_id in
            <iterate property="platOuterIdList" open="(" close=")" conjunction=",">
                #platOuterIdList[]#
            </iterate>
            )
        </isNotEmpty>

        <isNotEmpty property="text" prepend="AND" open="(" close=")">
            <!--模糊匹配-->
            <isEqual property="searchType" compareValue="0">
                <!-- 商家编码 -->
                <isEqual property="queryType" compareValue="outerId">
                    dmj_item.outer_id LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 商品名称 -->
                <isEqual property="queryType" compareValue="itemName">
                    dmj_item.title LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 商品简称 -->
                <isEqual property="queryType" compareValue="shortTitle">
                    dmj_item.short_title LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 商品备注 -->
                <isEqual property="queryType" compareValue="remark">
                    dmj_item.remark LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 规格属性 -->
                <isEqual property="queryType" compareValue="propertiesName">
                    <!-- 纯商品不支持该查询类型,所以给定一个不存在的条件，过滤掉所有商品 -->
                    <![CDATA[ dmj_item.sys_item_id < 0 ]]>
                </isEqual>

                <!-- 规格别名 -->
                <isEqual property="queryType" compareValue="skuAlias">
                    <!-- 纯商品不支持该查询类型,所以给定一个不存在的条件，过滤掉所有商品 -->
                    <![CDATA[ dmj_item.sys_item_id < 0 ]]>
                </isEqual>

                <!-- 规格备注 -->
                <isEqual property="queryType" compareValue="skuRemark">
                    <!-- 纯商品不支持该查询类型,所以给定一个不存在的条件，过滤掉所有商品 -->
                    <![CDATA[ dmj_item.sys_item_id < 0 ]]>
                </isEqual>

                <!-- 平台商家编码 -->
                <isEqual property="queryType" compareValue="platOuterId">
                    tb_item.outer_id LIKE CONCAT('%', #text#, '%') OR tb_sku.outer_id LIKE CONCAT('%', #text#, '%')
                </isEqual>

                <!-- 平台商品名称 -->
                <isEqual property="queryType" compareValue="platTitle">
                    tb_item.title LIKE CONCAT('%', #text#, '%')
                </isEqual>
            </isEqual>

            <!--精准匹配-->
            <isEqual property="searchType" compareValue="1">
                <!-- 商家编码 -->
                <isEqual property="queryType" compareValue="outerId">
                    dmj_item.outer_id = #text#
                </isEqual>

                <!-- 商品名称 -->
                <isEqual property="queryType" compareValue="itemName">
                    dmj_item.title = #text#
                </isEqual>

                <!-- 商品简称 -->
                <isEqual property="queryType" compareValue="shortTitle">
                    dmj_item.short_title = #text#
                </isEqual>

                <!-- 商品备注 -->
                <isEqual property="queryType" compareValue="remark">
                    dmj_item.remark = #text#
                </isEqual>

                <!-- 规格属性 -->
                <isEqual property="queryType" compareValue="propertiesName">
                    <!-- 纯商品不支持该查询类型,所以给定一个不存在的条件，过滤掉所有商品 -->
                    <![CDATA[ dmj_item.sys_item_id < 0 ]]>
                </isEqual>

                <!-- 规格别名 -->
                <isEqual property="queryType" compareValue="skuAlias">
                    <!-- 纯商品不支持该查询类型,所以给定一个不存在的条件，过滤掉所有商品 -->
                    <![CDATA[ dmj_item.sys_item_id < 0 ]]>
                </isEqual>

                <!-- 规格备注 -->
                <isEqual property="queryType" compareValue="skuRemark">
                    <!-- 纯商品不支持该查询类型,所以给定一个不存在的条件，过滤掉所有商品 -->
                    <![CDATA[ dmj_item.sys_item_id < 0 ]]>
                </isEqual>

                <!-- 平台商家编码 -->
                <isEqual property="queryType" compareValue="platOuterId">
                    tb_item.outer_id = #text# OR tb_sku.outer_id = #text#
                </isEqual>

                <!-- 平台商品名称 -->
                <isEqual property="queryType" compareValue="platTitle">
                    tb_item.title = #text#
                </isEqual>
            </isEqual>
        </isNotEmpty>
    </select>

    <!-- 修改成份 -->
    <update id="updateComponent" parameterClass="hashMap">
        UPDATE dmj_item_#dbNo#
        SET modified = NOW(),
        component = #component#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate open="(" close=")" conjunction="," property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </update>

    <select id="queryAllItemList" parameterClass="QueryAllItemListParams" resultMap="dmjItemMap" timeout="30">
        SELECT * FROM dmj_item_#dmjItemTableNo#
        WHERE company_id = #companyId#
        AND enable_status = 1
        <isNotEmpty property="typeList">
            AND type IN
            <iterate property="typeList" open="(" close=")" conjunction=",">
                #typeList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="typeTagList">
            AND type_tag IN
            <iterate property="typeTagList" open="(" close=")" conjunction=",">
                #typeTagList[]#
            </iterate>
        </isNotEmpty>
        ORDER BY sys_item_id
        LIMIT #page.startRow#,#page.offsetRow#
    </select>

    <sql id="joinTable4ItemList">
        <isNotNull property="userIdList">
            LEFT JOIN (
            SELECT
            sys_item_id,
            GROUP_CONCAT(user_id) userIds
            FROM sku_erp_bridge_#skuBridgeTableNo#
            WHERE company_id = #companyId#
            <isNotEmpty property="sysItemIdList">
                AND sys_item_id IN
                <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                    #sysItemIdList[]#
                </iterate>
            </isNotEmpty>
            AND sys_item_id > 0
            AND sys_id &lt; 1
            GROUP BY sys_item_id
            ) AS sku_bridge
            ON sku_bridge.sys_item_id = dmj_item.sys_item_id
        </isNotNull>
        <isEqual property="needJoinItemSupplierBridge" compareValue="true">
            LEFT JOIN (
            SELECT
            sys_item_id,
            GROUP_CONCAT(distinct supplier_id) supplierIds,
            GROUP_CONCAT(distinct supplier_item_outer_id) supplierItemOuterIds
            FROM item_supplier_bridge_#itemSupplierBridgeTableNo#
            WHERE company_id = #companyId#
            <isNotEmpty property="sysItemIdList">
                AND sys_item_id IN
                <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                    #sysItemIdList[]#
                </iterate>
            </isNotEmpty>
            AND sys_sku_id &lt; 1
            GROUP BY sys_item_id
            ) AS supplier_bridge
            ON supplier_bridge.sys_item_id = dmj_item.sys_item_id
        </isEqual>
        <isEqual property="needJoinUploadRule" compareValue="true">
            LEFT JOIN stock_upload_rule_#uploadRuleTableNo# upload_rule
            ON upload_rule.company_id = #companyId#
            AND upload_rule.sys_item_id = dmj_item.sys_item_id
            AND upload_rule.sys_sku_id = 0
            AND upload_rule.enable_status = 1
        </isEqual>
        <isEqual property="needJoinItemPushRecord" compareValue="true">
            LEFT JOIN (
            SELECT
                sys_item_id,
                GROUP_CONCAT(supplier_company_id) supplierCompanyIds
            FROM
                dms_item_push_record_#pushRecordDbNo#
            WHERE
                distributor_company_id = #companyId#
                AND enable_status = 1
                AND sys_sku_id = 0
                <isNotEmpty property="sysItemIdList">
                    AND sys_item_id IN
                    <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                        #sysItemIdList[]#
                    </iterate>
                </isNotEmpty>
            GROUP BY sys_item_id
            ) item_push_record
            ON item_push_record.sys_item_id = dmj_item.sys_item_id
        </isEqual>
    </sql>

    <sql id="queryItemListCondition">

        <include refid="queryItemListSimpleCondition"/>

        AND dmj_item.is_sku_item = 0

        <isEqual property="filterSupplierAuth" compareValue="true" open="(" close=")" prepend=" AND ">
            <!-- 需要过滤供应商权限 -->
            <iterate property="supplierIdList" open="(" close=")" conjunction=" OR ">
                FIND_IN_SET(#supplierIdList[]#, supplier_bridge.supplierIds) > 0
            </iterate>

            <isEqual property="includeNoSupplier" compareValue="true">
                <isNotEmpty property="supplierIdList">
                    OR
                </isNotEmpty>
                supplier_bridge.supplierIds IS NULL
            </isEqual>
        </isEqual>

        <isNotEmpty property="supplierItemOuterId" prepend=" AND ">
            supplier_bridge.supplierItemOuterIds LIKE CONCAT('%',#supplierItemOuterId#,'%')
        </isNotEmpty>

        <isNotEmpty property="supplierCompanyIdList" prepend=" AND ">
            <iterate property="supplierCompanyIdList" open="(" close=")" conjunction=" OR ">
                FIND_IN_SET(#supplierCompanyIdList[]#, item_push_record.supplierCompanyIds) > 0
            </iterate>
        </isNotEmpty>

        <!-- 未设置 -->
        <isNotEmpty property="notSet">
            <!-- 商品简称 -->
            <isEqual property="notSet" compareValue="shortTitle" prepend=" AND ">
                (LENGTH(dmj_item.short_title) = 0 OR dmj_item.short_title IS NULL)
            </isEqual>
            <!-- 品牌 -->
            <isEqual property="notSet" compareValue="brand" prepend=" AND ">
                (LENGTH(dmj_item.brand) = 0 OR dmj_item.brand IS NULL)
            </isEqual>
            <!-- 单位 -->
            <isEqual property="notSet" compareValue="unit" prepend=" AND ">
                (LENGTH(dmj_item.unit) = 0 OR dmj_item.unit IS NULL)
            </isEqual>
            <!-- 重量 -->
            <isEqual property="notSet" compareValue="weight" prepend=" AND ">
                dmj_item.weight = 0
            </isEqual>
            <!-- 商品条形码 -->
            <isEqual property="notSet" compareValue="barcode" prepend=" AND ">
                (LENGTH(dmj_item.barcode) = 0 OR dmj_item.barcode IS NULL)
            </isEqual>
            <!-- 规格条形码 -->
            <isEqual property="notSet" compareValue="skuBarcode" prepend=" AND ">
                <!-- 纯商品不支持该条件，给定一个不可能的条件 -->
                dmj_item.sys_item_id = 0
            </isEqual>
            <!-- 成本价 -->
            <isEqual property="notSet" compareValue="purchasePrice" prepend=" AND ">
                dmj_item.purchase_price = 0
            </isEqual>
            <!-- 销售价 -->
            <isEqual property="notSet" compareValue="sellingPrice" prepend=" AND ">
                dmj_item.selling_price = 0
            </isEqual>
            <!-- 长宽高 -->
            <isEqual property="notSet" compareValue="lengthWidthHeight" prepend=" AND ">
                (dmj_item.x = 0 OR dmj_item.y = 0 OR dmj_item.z = 0)
            </isEqual>
            <!-- 保质期 -->
            <isEqual property="notSet" compareValue="periodCast" prepend=" AND ">
                dmj_item.period_cast = 0
            </isEqual>
            <!-- 产地 -->
            <isEqual property="notSet" compareValue="place" prepend=" AND ">
                LENGTH(dmj_item.place) = 0
            </isEqual>
            <!-- 商品备注 -->
            <isEqual property="notSet" compareValue="remark" prepend=" AND ">
                LENGTH(dmj_item.remark) = 0
            </isEqual>
            <!-- 规格备注 -->
            <isEqual property="notSet" compareValue="skuRemark" prepend=" AND ">
                <!-- 纯商品不支持该条件，给定一个不可能的条件 -->
                dmj_item.sys_item_id = 0
            </isEqual>
            <!-- 生产日期 -->
            <isEqual property="notSet" compareValue="productionDate" prepend=" AND ">
                (LENGTH(dmj_item.production_date) = 0 OR dmj_item.production_date IS NULL)
            </isEqual>
            <!-- 供应商 -->
            <isEqual property="notSet" compareValue="supplier" prepend=" AND ">
                supplier_bridge.supplierIds IS NULL
            </isEqual>
            <!-- 供销商 -->
            <isEqual property="notSet" compareValue="supplierCompany" prepend=" AND ">
                item_push_record.supplierCompanyIds IS NULL
            </isEqual>
        </isNotEmpty>
        <isNotNull property="autoUpload" prepend=" AND ">
            <isEqual property="autoUpload" compareValue="true">
                upload_rule.is_auto = 1
            </isEqual>
            <isEqual property="autoUpload" compareValue="false">
                upload_rule.is_auto = 0
            </isEqual>
        </isNotNull>
    </sql>

    <sql id="queryItemListSimpleCondition">
        WHERE dmj_item.company_id = #companyId#
        AND dmj_item.enable_status = 1

        <isNotEmpty property="sysItemIdList" prepend=" AND ">
            dmj_item.sys_item_id IN
            <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
                #sysItemIdList[]#
            </iterate>
        </isNotEmpty>

        <isNotNull property="userIdList" prepend=" AND ">
            <iterate property="userIdList" open="(" close=")" conjunction=" OR ">
                FIND_IN_SET(#userIdList[]#, sku_bridge.userIds) > 0
            </iterate>
        </isNotNull>

        <isNotNull property="brandNameList" prepend=" AND ">
            dmj_item.brand IN
            <iterate property="brandNameList" open="(" close=")" conjunction=",">
                #brandNameList[]#
            </iterate>
        </isNotNull>

        <isNotNull property="catIdList" prepend=" AND ">
            dmj_item.cat_id IN
            <iterate property="catIdList" open="(" close=")" conjunction=",">
                #catIdList[]#
            </iterate>
        </isNotNull>

        <isNotNull property="cIdList" prepend=" AND ">
            <iterate property="cIdList" open="(" close=")" conjunction=" OR ">
                FIND_IN_SET(#cIdList[]#, dmj_item.seller_cids) > 0
            </iterate>
        </isNotNull>

        <isNotNull property="typeList" prepend=" AND ">
            dmj_item.type IN
            <iterate property="typeList" open="(" close=")" conjunction=",">
                #typeList[]#
            </iterate>
        </isNotNull>

        <isNotNull property="typeTagList" prepend=" AND ">
            dmj_item.type_tag IN
            <iterate property="typeTagList" open="(" close=")" conjunction=",">
                #typeTagList[]#
            </iterate>
        </isNotNull>

        <isNotNull property="isVirtual" prepend=" AND ">
            <isEqual property="isVirtual" compareValue="true">
                dmj_item.is_virtual = 1
            </isEqual>

            <isEqual property="isVirtual" compareValue="false">
                dmj_item.is_virtual = 0
            </isEqual>
        </isNotNull>

        <isNotEmpty property="outerId" prepend=" AND ">
            dmj_item.outer_id LIKE CONCAT('%',#outerId#,'%')
        </isNotEmpty>

        <isNotEmpty property="skuOuterId" prepend=" AND ">
            dmj_item.outer_id LIKE CONCAT('%',#skuOuterId#,'%')
        </isNotEmpty>

        <isNotEmpty property="inItemOuterIdList" prepend=" AND ">
            dmj_item.outer_id in
            <iterate property="inItemOuterIdList" open="(" close=")" conjunction=",">
                #inItemOuterIdList[]#
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="notInItemOuterIdList" prepend=" AND ">
            dmj_item.outer_id not in
            <iterate property="notInItemOuterIdList" open="(" close=")" conjunction=",">
                #notInItemOuterIdList[]#
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="skuOuterIdList" prepend=" AND ">
            dmj_item.outer_id in
            <iterate property="skuOuterIdList" open="(" close=")" conjunction=",">
                #skuOuterIdList[]#
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="title" prepend=" AND ">
            dmj_item.title LIKE CONCAT('%',#title#,'%')
        </isNotEmpty>

        <isNotEmpty property="propertiesName" prepend=" AND ">
            <!-- 纯商品不支持该条件，给定一个不可能的条件 -->
            dmj_item.sys_item_id = 0
        </isNotEmpty>

        <isNotEmpty property="remark" prepend=" AND ">
            dmj_item.remark LIKE CONCAT('%',#remark#,'%')
        </isNotEmpty>

        <isNotEmpty property="skuRemark" prepend=" AND ">
            <!-- 纯商品不支持该条件，给定一个不可能的条件 -->
            dmj_item.sys_item_id = 0
        </isNotEmpty>

        <isNotEmpty property="shortTitle" prepend=" AND ">
            dmj_item.short_title LIKE CONCAT('%',#shortTitle#,'%')
        </isNotEmpty>

        <isNotEmpty property="propertiesAlias" prepend=" AND ">
            <!-- 纯商品不支持该条件，给定一个不可能的条件 -->
            dmj_item.sys_item_id = 0
        </isNotEmpty>

        <isNotNull property="activeStatus" prepend=" AND ">
            dmj_item.active_status = #activeStatus#
        </isNotNull>

        <isNotNull property="startTime">
            <isNotNull property="endTime" prepend=" AND ">
                dmj_item.created between #startTime# and #endTime#
            </isNotNull>
        </isNotNull>

        <isNotEmpty property="hide" prepend=" AND ">
            dmj_item.hide = #hide#
        </isNotEmpty>

        <isNotEmpty property="skuBarcode" prepend=" AND ">
            <!-- 纯商品不支持该条件，给定一个不可能的条件 -->
            dmj_item.sys_item_id = 0
        </isNotEmpty>

        <isNotEmpty property="itemBarcode" prepend=" AND ">
            dmj_item.barcode LIKE CONCAT('%',#itemBarcode#,'%')
        </isNotEmpty>
    </sql>

    <select id="queryItemListCount" parameterClass="ItemListQueryParams" resultClass="int" timeout="30">
        SELECT COUNT(*) FROM dmj_item_#dmjItemTableNo# dmj_item
        <include refid="joinTable4ItemList"/>
        <include refid="queryItemListCondition"/>
    </select>

    <select id="queryItemList" parameterClass="ItemListQueryParams" resultMap="dmjItemMap" timeout="30">
        SELECT dmj_item.* FROM dmj_item_#dmjItemTableNo# dmj_item
        <include refid="joinTable4ItemList"/>
        <include refid="queryItemListCondition"/>
        ORDER BY
        <isNotEmpty property="itemOrderColumn">
            <isEqual property="itemOrderColumn" compareValue="created">
                dmj_item.sys_item_id
                <isEmpty property="orderDesc">DESC</isEmpty>
                <isNotEmpty property="orderDesc">
                    <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
                </isNotEmpty>
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="outer_id">
                dmj_item.outer_id <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="purchase_price">
                dmj_item.purchase_price <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="selling_price">
                dmj_item.selling_price <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="supplier">
                supplier_bridge.supplierIds <isEqual property="orderDesc" compareValue="true">DESC</isEqual>
            </isEqual>
            <isEqual property="itemOrderColumn" compareValue="title">
                CONVERT(dmj_item.title USING gbk)
            </isEqual>
        </isNotEmpty>
        <isEmpty property="itemOrderColumn">
            dmj_item.sys_item_id DESC
        </isEmpty>
        LIMIT #page.startRow#,#page.offsetRow#
    </select>

    <select id="queryOuterIdSysIdMapByOuterIds" parameterClass="map" resultClass="hashMap">
        SELECT outer_id outerId,sys_item_id sysId FROM dmj_item_#dmjItemDbNo#
        WHERE company_id = #companyId#
        AND outer_id IN
        <iterate property="outerIds" open="(" close=")" conjunction=",">
            #outerIds[]#
        </iterate>
        UNION ALL SELECT outer_id outerId,sys_sku_id sysId FROM dmj_sku_#dmjSkuDbNo#
        WHERE company_id = #companyId#
        AND outer_id IN
        <iterate property="outerIds" open="(" close=")" conjunction=",">
            #outerIds[]#
        </iterate>
    </select>

    <!-- 批量更新商品的指定字段 -->
    <update id="batchUpdateCustomField" parameterClass="hashMap">
        update dmj_item_#dbNo# set $updateField$ = #updateValue#
        where company_id = #companyId# and sys_item_id in
        <iterate open="(" close=")" conjunction="," property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </update>

    <!-- 修改商品标记赠品状态 -->
    <update id="updateMakeGiftBySysItemIds" parameterClass="map">
        UPDATE dmj_item_#dbNo#
        SET make_gift = #makeGift#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
            #sysItemIdList[]#
        </iterate>
    </update>


    <update id="updateBrandBatch" parameterClass="map">
        UPDATE dmj_item_#dbNo#
        SET brand = #brand#,brand_id= #brandId#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate property="sysItemIdList" open="(" close=")" conjunction=",">
            #sysItemIdList[]#
        </iterate>
    </update>

    <update id="clearBrand">
        UPDATE dmj_item_#dbNo# SET brand = '', brand_id = null WHERE company_id = #companyId# AND sys_item_id = #sysItemId#
    </update>

    <!--批量修改销售价格-->
    <update id="updateSalePrice" parameterClass="map">
        UPDATE dmj_item_#itemTableNo#
        SET modified = NOW(),
        selling_price = #salePrice#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate property="sysItemIds" open="(" close=")" conjunction=",">
            #sysItemIds[]#
        </iterate>
    </update>

    <!--  -->
    <update id="updateItem4ListV2" parameterClass="map">
        UPDATE dmj_item_#itemTableNo#
        SET modified = NOW()
        <isNotNull prepend="," property="dmjItem.shipper">shipper = #dmjItem.shipper#</isNotNull>
        <isNotNull prepend="," property="dmjItem.unit">unit= #dmjItem.unit#</isNotNull>
        <isNotNull prepend="," property="dmjItem.x">x= #dmjItem.x#</isNotNull>
        <isNotNull prepend="," property="dmjItem.y">y= #dmjItem.y#</isNotNull>
        <isNotNull prepend="," property="dmjItem.z">z= #dmjItem.z#</isNotNull>
        <isNotNull prepend="," property="dmjItem.wholesalePrice">wholesale_price= #dmjItem.wholesalePrice#</isNotNull>
        <isNotNull prepend="," property="dmjItem.boxnum">boxnum = #dmjItem.boxnum#</isNotNull>
        <isNotNull prepend="," property="dmjItem.invoice">invoice = #dmjItem.invoice#</isNotNull>
        <isNotNull prepend="," property="dmjItem.place">place = #dmjItem.place#</isNotNull>
        <isNotNull prepend="," property="dmjItem.component">component = #dmjItem.component#</isNotNull>
        <isNotNull prepend="," property="dmjItem.standard">standard = #dmjItem.standard#</isNotNull>
        <isNotNull prepend="," property="dmjItem.safekind">safekind = #dmjItem.safekind#</isNotNull>
        <isNotNull prepend="," property="dmjItem.record">record = #dmjItem.record#</isNotNull>
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate property="sysItemIds" open="(" close=")" conjunction=",">
            #sysItemIds[]#
        </iterate>
    </update>

    <!--批量修改商品重量-->
    <update id="updateSysItemWeight" parameterClass="map">
        UPDATE dmj_item_#itemTableNo#
        SET modified = NOW(),
        weight = #weight#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate property="sysItemIds" open="(" close=")" conjunction=",">
            #sysItemIds[]#
        </iterate>
    </update>

    <!--批量修改备注-->
    <update id="batchUpdateRemark" parameterClass="map">
        UPDATE dmj_item_#itemTableNo#
        SET modified = NOW(),
        remark = #remark#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate property="sysItemIds" open="(" close=")" conjunction=",">
            #sysItemIds[]#
        </iterate>
    </update>

    <update id="updateItemCatId" parameterClass="map">
        UPDATE dmj_item_#itemDbNo#
        SET modified = NOW(),
        cat_id = #catId#
        WHERE company_id = #companyId#
        AND sys_item_id = #sysItemId#
    </update>

    <select id="queryOuterIdMapBySysItemIds" parameterClass="map" resultClass="hashMap">
        SELECT
        sys_item_id AS sysItemId,
        outer_id AS outerId
        FROM dmj_item_#itemDbNo#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate property="sysItemIds" open="(" close=")" conjunction=",">
            #sysItemIds[]#
        </iterate>
    </select>

    <select id="queryDmjItemBySysItemId" parameterClass="map" resultMap="dmjItemMap">
        SELECT * FROM dmj_item_#dmjItemDbNo#
        WHERE company_id = #companyId#
        AND sys_item_id = #sysItemId#
    </select>

    <select id="queryPureItemList" parameterClass="map" resultMap="dmjItemMap">
        SELECT * FROM dmj_item_#itemDbNo#
        WHERE company_id = #companyId#
        AND is_sku_item = 0
        ORDER BY sys_item_id
        LIMIT #page.startRow#,#page.pageSize#
    </select>

    <update id="updateHistoryPriceImport" parameterClass="map">
        UPDATE dmj_item_#itemTableNo#
        SET modified = NOW(), history_price_import = #historyPriceImport#
        WHERE company_id = #companyId# and sys_item_id=#sysItemId#
    </update>

    <select id="queryValidSysItemIds" parameterClass="map" resultClass="long">
        SELECT sys_item_id FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate open="(" close=")" conjunction="," property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </select>

    <select id="stockInOutRecordQueryCount" resultClass="long">
        SELECT distinct item.sys_item_id FROM dmj_item_#itemTableNo# item
        LEFT JOIN item_supplier_bridge_#itemSupplierBridgeTableNo# supplier on (item.company_id = supplier.company_id AND item.sys_item_id = supplier.sys_item_id AND supplier.sys_sku_id = 0)
        WHERE item.company_id = #companyId#
        AND item.is_sku_item = 0
        <!-- 是否是套件 -->
        <isEqual property="isSuiteItem" compareValue="true" prepend=" AND ">item.type IN ('1', '2')</isEqual>
        <isEqual property="isSuiteItem" compareValue="false" prepend=" AND ">item.type = '0'</isEqual>
        <isNotNull property="outerId" prepend="AND">
            item.outer_id LIKE CONCAT('%', #outerId#, '%')
        </isNotNull>
        <isNotNull property="outerIds" prepend="AND">
            item.outer_id IN <iterate open="(" close=")" conjunction="," property="outerIds">#outerIds[]#</iterate>
        </isNotNull>
        <isNotNull property="title" prepend="AND">
            item.title LIKE CONCAT('%', #title#, '%')
        </isNotNull>
        <isNotNull property="brand" prepend="AND">
            item.brand_id = #brand#
        </isNotNull>
        <isNotNull property="cId" prepend="AND">
            item.seller_cids LIKE CONCAT('%', #cId#, '%')
        </isNotNull>
        <isNotNull property="supplierIds" prepend="AND">
            (supplier.supplier_id in <iterate property="supplierIds" open="(" conjunction="," close=")">#supplierIds[]#</iterate> OR supplier.supplier_id IS NULL)
        </isNotNull>
        <!-- 特殊搜索 -->
        <isNotEmpty property="text" prepend=" AND ">
            <!-- 商品简称 -->
            <isEqual property="queryType" compareValue="shortTitle">
                item.short_title LIKE CONCAT('%', #text#, '%')
            </isEqual>

            <!-- 商品备注 -->
            <isEqual property="queryType" compareValue="remark">
                item.remark LIKE CONCAT('%', #text#, '%')
            </isEqual>

        </isNotEmpty>
        LIMIT 2000
    </select>

    <select id="queryIntegralDataBySysItemIds" parameterClass="map" resultClass="DmjItem" timeout="30">
        SELECT
        sys_item_id sysItemId,
        title,
        outer_id outerId,
        IFNULL(one_integral,0) oneIntegral,
        IFNULL(more_integral,0) moreIntegral,
        IFNULL(one_package_integral,0) onePackageIntegral,
        IFNULL(more_package_integral,0) morePackageIntegral,
        IFNULL(one_inspection_integral,0) oneInspectionIntegral,
        IFNULL(more_inspection_integral,0) moreInspectionIntegral,
        IFNULL(one_weight_integral,0) oneWeightIntegral,
        IFNULL(more_weight_integral,0) moreWeightIntegral
        FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND is_sku_item = 0
        <iterate property="sysItemIds" prepend=" AND sys_item_id IN " open="(" close=")" conjunction=",">
            #sysItemIds[]#
        </iterate>
    </select>

    <select id="countNotSkuItemByItemIds" parameterClass="map" resultClass="int">
        select count(*) from dmj_item_#dbNo#
        where company_id = #companyId#
        and is_sku_item = 0
        <iterate prepend="and sys_item_id in" open="(" conjunction="," close=")" property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </select>

    <select id="queryPureItemByOuterIds4Fms" parameterClass="map" resultClass="DmjItem">
        SELECT
        title,
        outer_id AS outerId,
        pic_path AS picPath
        FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND is_sku_item = 0
        AND outer_id IN
        <iterate open="(" close=")" conjunction="," property="outerIds">
            #outerIds[]#
        </iterate>
    </select>

    <select id="queryItemBySysItemIds4Fms" parameterClass="map" resultClass="DmjItem">
        SELECT
        sys_item_id AS sysItemId,
        title,
        outer_id AS outerId,
        is_sku_item AS isSkuItem,
        pic_path AS picPath
        FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate open="(" close=")" conjunction="," property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </select>

    <select id="queryItemTypeByIds" parameterClass="map" resultClass="hashMap">
        SELECT sys_item_id,type FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate open="(" conjunction="," close=")" property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </select>

    <select id="querySysItemIds4CheckExist" parameterClass="map" resultClass="long">
        SELECT sys_item_id FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate open="(" conjunction="," close=")" property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </select>

    <!--批量修改批发价格-->
    <update id="updateWholesalePrice" parameterClass="map">
        UPDATE dmj_item_#itemTableNo#
        SET modified = NOW(),
        wholesale_price = #wholesalePrice#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate property="sysItemIds" open="(" close=")" conjunction=",">
            #sysItemIds[]#
        </iterate>
    </update>

    <select id="queryIsAutoCalculateSysItemIds" resultClass="long" parameterClass="map">
        SELECT sys_item_id FROM dmj_item_#itemTableNo#
        where company_id = #companyId#
        AND (type in ('1', '2') OR type_tag in (1, 2, 3, 4))
        AND sys_item_id IN
        <iterate property="sysItemIds" open="(" close=")" conjunction=",">
            #sysItemIds[]#
        </iterate>

        <isEqual property="autoCalculateColumn" compareValue="isSysPriceImport">
            AND is_sys_purchase_price = 1
        </isEqual>
        <isEqual property="autoCalculateColumn" compareValue="isSysPriceOutput">
            AND is_sys_selling_price = 1
        </isEqual>
        <isEqual property="autoCalculateColumn" compareValue="isSysWholesalePrice">
            AND is_sys_wholesale_price = 1
        </isEqual>
        <isEqual property="autoCalculateColumn" compareValue="isSysWeight">
            AND is_sys_weight = 1
        </isEqual>
    </select>

    <select id="queryPureItemCatIdAndCidsByOuterIds" parameterClass="map" resultClass="DmjItem">
        SELECT
        outer_id AS outerId,
        cat_id AS catId,
        seller_cids as sellerCids
        FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND is_sku_item = 0
        AND outer_id IN
        <iterate open="(" close=")" conjunction="," property="outerIds">
            #outerIds[]#
        </iterate>
    </select>

    <select id="queryOuterIdBySysItemId" resultClass="String">
        SELECT outer_id FROM dmj_item_#itemTableNo#
        WHERE company_id = #companyId#
        AND sys_item_id = #sysItemId#
    </select>

    <select id="queryOuterIdByPage" resultClass="hashMap">
        SELECT sys_item_id,outer_id FROM dmj_item_#itemTableNo#
        WHERE company_id = #companyId#
        LIMIT #page.startRow#,#page.offsetRow#
    </select>

    <select id="querySysItemIds4Stock" parameterClass="Item4StockQueryParams" resultClass="long">
        SELECT item.sys_item_id FROM dmj_item_#itemTableNo# item
        <include refid="queryItem4StockSimpleCondition"/>
        AND item.is_sku_item = #isSkuItem#
    </select>

    <select id="queryPureItemBySysItemIds" parameterClass="map" resultMap="dmjItemMap">
        SELECT * FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND enable_status = 1
        AND is_sku_item = 0
        AND sys_item_id IN
        <iterate open="(" close=")" conjunction="," property="sysItemIds">
            #sysItemIds[]#
        </iterate>
    </select>

    <select id="querySysItemIdList" parameterClass="ItemListQueryParams" resultClass="long" timeout="30">
        SELECT dmj_item.sys_item_id FROM dmj_item_#dmjItemTableNo# dmj_item
        <include refid="queryItemListSimpleCondition"/>
        AND dmj_item.is_sku_item = #isSkuItem#
    </select>

    <select id="queryItemList4ProcessData" parameterClass="map" resultMap="dmjItemMap">
        SELECT * FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND sys_item_id > #minSysItemId#
        ORDER BY sys_item_id
        LIMIT #pageSize#
    </select>

    <select id="queryDmjItemListWithOuterIdAndExtendFieldValues" parameterClass="map" resultClass="DmjItem">
        SELECT
        sys_item_id AS sysItemId,
        outer_id AS outerId,
        extend_field_values AS extendFieldValues
        FROM dmj_item_#dbNo#
        WHERE company_id = #companyId#
        AND sys_item_id IN
        <iterate open="(" close=")" conjunction="," property="sysItemList">
            #sysItemList[]#
        </iterate>
    </select>

    <select id="query4TestMockLimit5" resultClass="DmjItem">
        SELECT
        sys_item_id AS sysItemId,
        outer_id AS outerId,
        is_sku_item AS isSkuItem
        FROM dmj_item_#dbNo#
        WHERE company_id = #companyId# LIMIT 5
    </select>

    <select id="getPureSuiteDmjItemByOuterId" resultMap="dmjItemMap" parameterClass="hashMap" timeout="30">
        SELECT * FROM dmj_item_#dbNo#
        WHERE company_id= #companyId# and enable_status = 1 and outer_id = #outerId#
        AND type = '2'
        AND is_sku_item = 0
    </select>
</sqlMap>

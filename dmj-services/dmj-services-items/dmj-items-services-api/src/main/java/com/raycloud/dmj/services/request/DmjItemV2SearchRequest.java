package com.raycloud.dmj.services.request;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by ChangJie on 2019-12-27.
 */
public class DmjItemV2SearchRequest implements Serializable {

    private int pageSize;

    private int pageNo;

    /**
     * 主商家编码
     */
    private String outerId;

    /**
     * 规格商家编码
     */
    private String skuOuterId;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品类型，0-全部，1-普通商品，2-虚拟商品，3-套件商品，4-组合装商品，5-加工商品
     */
    private Integer itemType;

    /**
     * 规格
     */
    private String propertiesName;

    /**
     * 商品备注
     */
    private String remark;

    /**
     * 规格备注
     */
    private String skuRemark;

    /**
     * 商品简称
     */
    private String shortTitle;

    /**
     * 规格别名
     */
    private String propertiesAlias;

    /**
     * 供应商商家编码
     */
    private String supplierItemOuterId;

    /**
     * 类目ID，多个使用英文逗号隔开
     */
    private String catIds;

    /**
     * 分类ID，多个使用英文逗号隔开
     */
    private String cIds;

    /**
     * 店铺ID，多个使用英文逗号隔开
     */
    private String userIds;

    /**
     * 品牌名称，多个使用英文逗号隔开
     */
    private String brandNames;

    /**
     * 品牌id，多个使用英文逗号隔开
     */
    private String brandIds;

    /**
     * 供应商ID，多个使用英文逗号隔开
     */
    private String supplierIds;

    /**
     * 商品状态，true-启用，false-停用
     */
    private Boolean activeStatus;

    /**
     * 未设置，支持的属性：商品简称、品牌、单位、重量、商品条形码、规格条形码、成本价、销售价、长宽高、保质期、产地、商品备注、规格备注、生产日期、供应商
     */
    private String notSet;

    /**
     * 排序字段
     */
    private String orderColumn;

    /**
     * 是否倒序排序
     */
    private boolean orderDesc;

    /**
     * 是否自动上传
     */
    private Boolean autoUpload;

    /**
     * 是否只查询规格商品
     */
    private Boolean skuItem;

    /**
     * 是否是分销小店商品 1 是 0 否
     */
    private Integer distributorState;

    /**
     * 商品修改开始时间
     */
    private Date startModifiedTime;

    /**
     * 商品修改结束时间
     */
    private Date endModifiedTime;

    /**
     * 商品级别的商家编码
     */
    private List<String> inItemOuterIdList;

    /**
     * 商品级别的商家编码
     */
    private List<String> notInItemOuterIdList;

    /**
     * 包含的最小粒度商家编码列表
     */
    private List<String> skuOuterIdList;

    /**
     * 排除的最小粒度商家编码列表
     */
    private List<String> notInMiniOuterIdList;

    /**
     * 是否需要填充历史成本价
     */
    private boolean fillHistoryImportPrice;

    /**
     * 商品id集合
     */
    private List<Long> sysItemIdList;

    /**
     * 规格id集合
     */
    private List<Long> sysSkuIdList;

    private Long lastSkuId;

    private Boolean needStockInfo = true;

    public Boolean getNeedStockInfo() {
        return needStockInfo;
    }

    public void setNeedStockInfo(Boolean needStockInfo) {
        this.needStockInfo = needStockInfo;
    }

    public boolean isFillHistoryImportPrice() {
        return fillHistoryImportPrice;
    }

    public void setFillHistoryImportPrice(boolean fillHistoryImportPrice) {
        this.fillHistoryImportPrice = fillHistoryImportPrice;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getSkuOuterId() {
        return skuOuterId;
    }

    public void setSkuOuterId(String skuOuterId) {
        this.skuOuterId = skuOuterId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getItemType() {
        return itemType;
    }

    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    public String getPropertiesName() {
        return propertiesName;
    }

    public void setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSkuRemark() {
        return skuRemark;
    }

    public void setSkuRemark(String skuRemark) {
        this.skuRemark = skuRemark;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public String getPropertiesAlias() {
        return propertiesAlias;
    }

    public void setPropertiesAlias(String propertiesAlias) {
        this.propertiesAlias = propertiesAlias;
    }

    public String getSupplierItemOuterId() {
        return supplierItemOuterId;
    }

    public void setSupplierItemOuterId(String supplierItemOuterId) {
        this.supplierItemOuterId = supplierItemOuterId;
    }

    public String getCatIds() {
        return catIds;
    }

    public void setCatIds(String catIds) {
        this.catIds = catIds;
    }

    public String getcIds() {
        return cIds;
    }

    public void setcIds(String cIds) {
        this.cIds = cIds;
    }

    public String getUserIds() {
        return userIds;
    }

    public void setUserIds(String userIds) {
        this.userIds = userIds;
    }

    public String getBrandNames() {
        return brandNames;
    }

    public void setBrandNames(String brandNames) {
        this.brandNames = brandNames;
    }

    public String getBrandIds() {
        return brandIds;
    }

    public void setBrandIds(String brandIds) {
        this.brandIds = brandIds;
    }

    public String getSupplierIds() {
        return supplierIds;
    }

    public void setSupplierIds(String supplierIds) {
        this.supplierIds = supplierIds;
    }

    public Boolean getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Boolean activeStatus) {
        this.activeStatus = activeStatus;
    }

    public String getNotSet() {
        return notSet;
    }

    public void setNotSet(String notSet) {
        this.notSet = notSet;
    }

    public String getOrderColumn() {
        return orderColumn;
    }

    public void setOrderColumn(String orderColumn) {
        this.orderColumn = orderColumn;
    }

    public boolean isOrderDesc() {
        return orderDesc;
    }

    public void setOrderDesc(boolean orderDesc) {
        this.orderDesc = orderDesc;
    }

    public Boolean getAutoUpload() {
        return autoUpload;
    }

    public void setAutoUpload(Boolean autoUpload) {
        this.autoUpload = autoUpload;
    }

    public Boolean getSkuItem() {
        return skuItem;
    }

    public void setSkuItem(Boolean skuItem) {
        this.skuItem = skuItem;
    }

    public Date getStartModifiedTime() {
        return startModifiedTime;
    }

    public void setStartModifiedTime(Date startModifiedTime) {
        this.startModifiedTime = startModifiedTime;
    }

    public Date getEndModifiedTime() {
        return endModifiedTime;
    }

    public void setEndModifiedTime(Date endModifiedTime) {
        this.endModifiedTime = endModifiedTime;
    }

    public Integer getDistributorState() {
        return distributorState;
    }

    public void setDistributorState(Integer distributorState) {
        this.distributorState = distributorState;
    }

    public List<String> getInItemOuterIdList() {
        return inItemOuterIdList;
    }

    public void setInItemOuterIdList(List<String> inItemOuterIdList) {
        this.inItemOuterIdList = inItemOuterIdList;
    }

    public List<String> getNotInItemOuterIdList() {
        return notInItemOuterIdList;
    }

    public void setNotInItemOuterIdList(List<String> notInItemOuterIdList) {
        this.notInItemOuterIdList = notInItemOuterIdList;
    }

    public List<String> getSkuOuterIdList() {
        return skuOuterIdList;
    }

    public void setSkuOuterIdList(List<String> skuOuterIdList) {
        this.skuOuterIdList = skuOuterIdList;
    }

    public List<String> getNotInMiniOuterIdList() {
        return notInMiniOuterIdList;
    }

    public void setNotInMiniOuterIdList(List<String> notInMiniOuterIdList) {
        this.notInMiniOuterIdList = notInMiniOuterIdList;
    }


    public List<Long> getSysItemIdList() {
        return sysItemIdList;
    }

    public void setSysItemIdList(List<Long> sysItemIdList) {
        this.sysItemIdList = sysItemIdList;
    }

    public List<Long> getSysSkuIdList() {
        return sysSkuIdList;
    }

    public void setSysSkuIdList(List<Long> sysSkuIdList) {
        this.sysSkuIdList = sysSkuIdList;
    }

    public Long getLastSkuId() {
        return lastSkuId;
    }

    public void setLastSkuId(Long lastSkuId) {
        this.lastSkuId = lastSkuId;
    }
}

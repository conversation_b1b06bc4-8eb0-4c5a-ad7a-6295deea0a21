<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="reportSaleV2">

    <typeAlias alias="ReportSaleDimensions" type="com.raycloud.dmj.data.ReportThemeSale"/>


    <sql id="dmjItem">
        <isEqual property="dmjItemDbNo" compareValue="0">dmj_item_0</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="1">dmj_item_1</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="2">dmj_item_2</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="3">dmj_item_3</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="4">dmj_item_4</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="5">dmj_item_5</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="6">dmj_item_6</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="7">dmj_item_7</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="8">dmj_item_8</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="9">dmj_item_9</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="10">dmj_item_10</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="11">dmj_item_11</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="12">dmj_item_12</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="13">dmj_item_13</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="14">dmj_item_14</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="15">dmj_item_15</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="16">dmj_item_16</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="17">dmj_item_17</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="18">dmj_item_18</isEqual>
        <isEqual property="dmjItemDbNo" compareValue="19">dmj_item_19</isEqual>
    </sql>


    <sql id="sellerCat">
        <isEqual property="sellerCatDbNo" compareValue="0">seller_cat_0</isEqual>
        <isEqual property="sellerCatDbNo" compareValue="1">seller_cat_1</isEqual>
        <isEqual property="sellerCatDbNo" compareValue="2">seller_cat_2</isEqual>
        <isEqual property="sellerCatDbNo" compareValue="3">seller_cat_3</isEqual>
        <isEqual property="sellerCatDbNo" compareValue="4">seller_cat_4</isEqual>
    </sql>

    <sql id="dmjSku">
        <isEqual property="dmjSkuDbNo" compareValue="0">dmj_sku_0</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="1">dmj_sku_1</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="2">dmj_sku_2</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="3">dmj_sku_3</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="4">dmj_sku_4</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="5">dmj_sku_5</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="6">dmj_sku_6</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="7">dmj_sku_7</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="8">dmj_sku_8</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="9">dmj_sku_9</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="10">dmj_sku_10</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="11">dmj_sku_11</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="12">dmj_sku_12</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="13">dmj_sku_13</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="14">dmj_sku_14</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="15">dmj_sku_15</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="16">dmj_sku_16</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="17">dmj_sku_17</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="18">dmj_sku_18</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="19">dmj_sku_19</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="20">dmj_sku_20</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="21">dmj_sku_21</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="22">dmj_sku_22</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="23">dmj_sku_23</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="24">dmj_sku_24</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="25">dmj_sku_25</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="26">dmj_sku_26</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="27">dmj_sku_27</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="28">dmj_sku_28</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="29">dmj_sku_29</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="30">dmj_sku_30</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="31">dmj_sku_31</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="32">dmj_sku_32</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="33">dmj_sku_33</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="34">dmj_sku_34</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="35">dmj_sku_35</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="36">dmj_sku_36</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="37">dmj_sku_37</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="38">dmj_sku_38</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="39">dmj_sku_39</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="40">dmj_sku_40</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="41">dmj_sku_41</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="42">dmj_sku_42</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="43">dmj_sku_43</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="44">dmj_sku_44</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="45">dmj_sku_45</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="46">dmj_sku_46</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="47">dmj_sku_47</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="48">dmj_sku_48</isEqual>
        <isEqual property="dmjSkuDbNo" compareValue="49">dmj_sku_49</isEqual>
    </sql>


    <sql id="itemSupplierBridge">
        <isEqual property="itemSupplierBridgeDbNo" compareValue="0">item_supplier_bridge_0</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="1">item_supplier_bridge_1</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="2">item_supplier_bridge_2</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="3">item_supplier_bridge_3</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="4">item_supplier_bridge_4</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="5">item_supplier_bridge_5</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="6">item_supplier_bridge_6</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="7">item_supplier_bridge_7</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="8">item_supplier_bridge_8</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="9">item_supplier_bridge_9</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="10">item_supplier_bridge_10</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="11">item_supplier_bridge_11</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="12">item_supplier_bridge_12</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="13">item_supplier_bridge_13</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="14">item_supplier_bridge_14</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="15">item_supplier_bridge_15</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="16">item_supplier_bridge_16</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="17">item_supplier_bridge_17</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="18">item_supplier_bridge_18</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="19">item_supplier_bridge_19</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="20">item_supplier_bridge_20</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="21">item_supplier_bridge_21</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="22">item_supplier_bridge_22</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="23">item_supplier_bridge_23</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="24">item_supplier_bridge_24</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="25">item_supplier_bridge_25</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="26">item_supplier_bridge_26</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="27">item_supplier_bridge_27</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="28">item_supplier_bridge_28</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="29">item_supplier_bridge_29</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="30">item_supplier_bridge_30</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="31">item_supplier_bridge_31</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="32">item_supplier_bridge_32</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="33">item_supplier_bridge_33</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="34">item_supplier_bridge_34</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="35">item_supplier_bridge_35</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="36">item_supplier_bridge_36</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="37">item_supplier_bridge_37</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="38">item_supplier_bridge_38</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="39">item_supplier_bridge_39</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="40">item_supplier_bridge_40</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="41">item_supplier_bridge_41</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="42">item_supplier_bridge_42</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="43">item_supplier_bridge_43</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="44">item_supplier_bridge_44</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="45">item_supplier_bridge_45</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="46">item_supplier_bridge_46</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="47">item_supplier_bridge_47</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="48">item_supplier_bridge_48</isEqual>
        <isEqual property="itemSupplierBridgeDbNo" compareValue="49">item_supplier_bridge_49</isEqual>
    </sql>


    <sql id="multiPacksPrintTradeLog">
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="0">multi_packs_print_trade_log_0</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="1">multi_packs_print_trade_log_1</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="2">multi_packs_print_trade_log_2</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="3">multi_packs_print_trade_log_3</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="4">multi_packs_print_trade_log_4</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="5">multi_packs_print_trade_log_5</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="6">multi_packs_print_trade_log_6</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="7">multi_packs_print_trade_log_7</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="8">multi_packs_print_trade_log_8</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="9">multi_packs_print_trade_log_9</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="10">multi_packs_print_trade_log_10</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="11">multi_packs_print_trade_log_11</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="12">multi_packs_print_trade_log_12</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="13">multi_packs_print_trade_log_13</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="14">multi_packs_print_trade_log_14</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="15">multi_packs_print_trade_log_15</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="16">multi_packs_print_trade_log_16</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="17">multi_packs_print_trade_log_17</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="18">multi_packs_print_trade_log_18</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="19">multi_packs_print_trade_log_19</isEqual>
        <isEqual property="multiPacksPrintTradeLogDbNo" compareValue="20">multi_packs_print_trade_log_20</isEqual>
    </sql>
    <sql id="multiPacksPrintTradeLogDetail">
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="0">multi_packs_print_trade_log_detail_0</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="1">multi_packs_print_trade_log_detail_1</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="2">multi_packs_print_trade_log_detail_2</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="3">multi_packs_print_trade_log_detail_3</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="4">multi_packs_print_trade_log_detail_4</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="5">multi_packs_print_trade_log_detail_5</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="6">multi_packs_print_trade_log_detail_6</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="7">multi_packs_print_trade_log_detail_7</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="8">multi_packs_print_trade_log_detail_8</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="9">multi_packs_print_trade_log_detail_9</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="10">multi_packs_print_trade_log_detail_10</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="11">multi_packs_print_trade_log_detail_11</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="12">multi_packs_print_trade_log_detail_12</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="13">multi_packs_print_trade_log_detail_13</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="14">multi_packs_print_trade_log_detail_14</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="15">multi_packs_print_trade_log_detail_15</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="16">multi_packs_print_trade_log_detail_16</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="17">multi_packs_print_trade_log_detail_17</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="18">multi_packs_print_trade_log_detail_18</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="19">multi_packs_print_trade_log_detail_19</isEqual>
        <isEqual property="multiPacksPrintTradeLogDetailDbNo" compareValue="20">multi_packs_print_trade_log_detail_20</isEqual>
    </sql>

    <sql id="trade_table">
        <isEqual property="tradeDbNo" compareValue="0"> trade_0 </isEqual>
        <isEqual property="tradeDbNo" compareValue="1"> trade_1 </isEqual>
        <isEqual property="tradeDbNo" compareValue="2"> trade_2 </isEqual>
        <isEqual property="tradeDbNo" compareValue="3"> trade_3 </isEqual>
        <isEqual property="tradeDbNo" compareValue="4"> trade_4 </isEqual>
        <isEqual property="tradeDbNo" compareValue="5"> trade_5 </isEqual>
        <isEqual property="tradeDbNo" compareValue="6"> trade_6 </isEqual>
        <isEqual property="tradeDbNo" compareValue="7"> trade_7 </isEqual>
        <isEqual property="tradeDbNo" compareValue="8"> trade_8 </isEqual>
        <isEqual property="tradeDbNo" compareValue="9"> trade_9 </isEqual>
        <isEqual property="tradeDbNo" compareValue="10"> trade_10 </isEqual>
        <isEqual property="tradeDbNo" compareValue="11"> trade_11 </isEqual>
        <isEqual property="tradeDbNo" compareValue="12"> trade_12 </isEqual>
        <isEqual property="tradeDbNo" compareValue="13"> trade_13 </isEqual>
        <isEqual property="tradeDbNo" compareValue="14"> trade_14 </isEqual>
        <isEqual property="tradeDbNo" compareValue="15"> trade_15 </isEqual>
        <isEqual property="tradeDbNo" compareValue="16"> trade_16 </isEqual>
        <isEqual property="tradeDbNo" compareValue="17"> trade_17 </isEqual>
        <isEqual property="tradeDbNo" compareValue="18"> trade_18 </isEqual>
        <isEqual property="tradeDbNo" compareValue="19"> trade_19 </isEqual>
        <isEqual property="tradeDbNo" compareValue="20"> trade_20 </isEqual>
        <isEqual property="tradeDbNo" compareValue="21"> trade_21 </isEqual>
        <isEqual property="tradeDbNo" compareValue="22"> trade_22 </isEqual>
        <isEqual property="tradeDbNo" compareValue="23"> trade_23 </isEqual>
        <isEqual property="tradeDbNo" compareValue="24"> trade_24 </isEqual>
        <isEqual property="tradeDbNo" compareValue="25"> trade_25 </isEqual>
        <isEqual property="tradeDbNo" compareValue="26"> trade_26 </isEqual>
        <isEqual property="tradeDbNo" compareValue="27"> trade_27 </isEqual>
        <isEqual property="tradeDbNo" compareValue="28"> trade_28 </isEqual>
        <isEqual property="tradeDbNo" compareValue="29"> trade_29 </isEqual>
        <isEqual property="tradeDbNo" compareValue="30"> trade_30 </isEqual>
        <isEqual property="tradeDbNo" compareValue="31"> trade_31 </isEqual>
        <isEqual property="tradeDbNo" compareValue="32"> trade_32 </isEqual>
        <isEqual property="tradeDbNo" compareValue="33"> trade_33 </isEqual>
        <isEqual property="tradeDbNo" compareValue="34"> trade_34 </isEqual>
        <isEqual property="tradeDbNo" compareValue="35"> trade_35 </isEqual>
        <isEqual property="tradeDbNo" compareValue="36"> trade_36 </isEqual>
        <isEqual property="tradeDbNo" compareValue="37"> trade_37 </isEqual>
        <isEqual property="tradeDbNo" compareValue="38"> trade_38 </isEqual>
        <isEqual property="tradeDbNo" compareValue="39"> trade_39 </isEqual>
        <isEqual property="tradeDbNo" compareValue="40"> trade_40 </isEqual>
        <isEqual property="tradeDbNo" compareValue="41"> trade_41 </isEqual>
        <isEqual property="tradeDbNo" compareValue="42"> trade_42 </isEqual>
        <isEqual property="tradeDbNo" compareValue="43"> trade_43 </isEqual>
        <isEqual property="tradeDbNo" compareValue="44"> trade_44 </isEqual>
        <isEqual property="tradeDbNo" compareValue="45"> trade_45 </isEqual>
        <isEqual property="tradeDbNo" compareValue="46"> trade_46 </isEqual>
        <isEqual property="tradeDbNo" compareValue="47"> trade_47 </isEqual>
        <isEqual property="tradeDbNo" compareValue="48"> trade_48 </isEqual>
        <isEqual property="tradeDbNo" compareValue="49"> trade_49 </isEqual>
        <isEqual property="tradeDbNo" compareValue="50"> trade_50 </isEqual>
        <isEqual property="tradeDbNo" compareValue="51"> trade_51 </isEqual>
        <isEqual property="tradeDbNo" compareValue="52"> trade_52 </isEqual>
        <isEqual property="tradeDbNo" compareValue="53"> trade_53 </isEqual>
        <isEqual property="tradeDbNo" compareValue="54"> trade_54 </isEqual>
        <isEqual property="tradeDbNo" compareValue="55"> trade_55 </isEqual>
        <isEqual property="tradeDbNo" compareValue="56"> trade_56 </isEqual>
        <isEqual property="tradeDbNo" compareValue="57"> trade_57 </isEqual>
        <isEqual property="tradeDbNo" compareValue="58"> trade_58 </isEqual>
        <isEqual property="tradeDbNo" compareValue="59"> trade_59 </isEqual>
        <isEqual property="tradeDbNo" compareValue="60"> trade_60 </isEqual>
        <isEqual property="tradeDbNo" compareValue="61"> trade_61 </isEqual>
        <isEqual property="tradeDbNo" compareValue="62"> trade_62 </isEqual>
        <isEqual property="tradeDbNo" compareValue="63"> trade_63 </isEqual>
        <isEqual property="tradeDbNo" compareValue="64"> trade_64 </isEqual>
        <isEqual property="tradeDbNo" compareValue="65"> trade_65 </isEqual>
        <isEqual property="tradeDbNo" compareValue="66"> trade_66 </isEqual>
        <isEqual property="tradeDbNo" compareValue="67"> trade_67 </isEqual>
        <isEqual property="tradeDbNo" compareValue="68"> trade_68 </isEqual>
        <isEqual property="tradeDbNo" compareValue="69"> trade_69 </isEqual>
        <isEqual property="tradeDbNo" compareValue="70"> trade_70 </isEqual>
        <isEqual property="tradeDbNo" compareValue="71"> trade_71 </isEqual>
        <isEqual property="tradeDbNo" compareValue="72"> trade_72 </isEqual>
        <isEqual property="tradeDbNo" compareValue="73"> trade_73 </isEqual>
        <isEqual property="tradeDbNo" compareValue="74"> trade_74 </isEqual>
        <isEqual property="tradeDbNo" compareValue="75"> trade_75 </isEqual>
        <isEqual property="tradeDbNo" compareValue="76"> trade_76 </isEqual>
        <isEqual property="tradeDbNo" compareValue="77"> trade_77 </isEqual>
        <isEqual property="tradeDbNo" compareValue="78"> trade_78 </isEqual>
        <isEqual property="tradeDbNo" compareValue="79"> trade_79 </isEqual>
        <isEqual property="tradeDbNo" compareValue="80"> trade_80 </isEqual>
        <isEqual property="tradeDbNo" compareValue="81"> trade_81 </isEqual>
        <isEqual property="tradeDbNo" compareValue="82"> trade_82 </isEqual>
        <isEqual property="tradeDbNo" compareValue="83"> trade_83 </isEqual>
        <isEqual property="tradeDbNo" compareValue="84"> trade_84 </isEqual>
        <isEqual property="tradeDbNo" compareValue="85"> trade_85 </isEqual>
        <isEqual property="tradeDbNo" compareValue="86"> trade_86 </isEqual>
        <isEqual property="tradeDbNo" compareValue="87"> trade_87 </isEqual>
        <isEqual property="tradeDbNo" compareValue="88"> trade_88 </isEqual>
        <isEqual property="tradeDbNo" compareValue="89"> trade_89 </isEqual>
        <isEqual property="tradeDbNo" compareValue="90"> trade_90 </isEqual>
        <isEqual property="tradeDbNo" compareValue="91"> trade_91 </isEqual>
        <isEqual property="tradeDbNo" compareValue="92"> trade_92 </isEqual>
        <isEqual property="tradeDbNo" compareValue="93"> trade_93 </isEqual>
        <isEqual property="tradeDbNo" compareValue="94"> trade_94 </isEqual>
        <isEqual property="tradeDbNo" compareValue="95"> trade_95 </isEqual>
        <isEqual property="tradeDbNo" compareValue="96"> trade_96 </isEqual>
        <isEqual property="tradeDbNo" compareValue="97"> trade_97 </isEqual>
        <isEqual property="tradeDbNo" compareValue="98"> trade_98 </isEqual>
        <isEqual property="tradeDbNo" compareValue="99"> trade_99 </isEqual>
    </sql>

    <sql id="order_table">
        <isEqual property="orderDbNo" compareValue="0"> order_0 </isEqual>
        <isEqual property="orderDbNo" compareValue="1"> order_1 </isEqual>
        <isEqual property="orderDbNo" compareValue="2"> order_2 </isEqual>
        <isEqual property="orderDbNo" compareValue="3"> order_3 </isEqual>
        <isEqual property="orderDbNo" compareValue="4"> order_4 </isEqual>
        <isEqual property="orderDbNo" compareValue="5"> order_5 </isEqual>
        <isEqual property="orderDbNo" compareValue="6"> order_6 </isEqual>
        <isEqual property="orderDbNo" compareValue="7"> order_7 </isEqual>
        <isEqual property="orderDbNo" compareValue="8"> order_8 </isEqual>
        <isEqual property="orderDbNo" compareValue="9"> order_9 </isEqual>
        <isEqual property="orderDbNo" compareValue="10"> order_10 </isEqual>
        <isEqual property="orderDbNo" compareValue="11"> order_11 </isEqual>
        <isEqual property="orderDbNo" compareValue="12"> order_12 </isEqual>
        <isEqual property="orderDbNo" compareValue="13"> order_13 </isEqual>
        <isEqual property="orderDbNo" compareValue="14"> order_14 </isEqual>
        <isEqual property="orderDbNo" compareValue="15"> order_15 </isEqual>
        <isEqual property="orderDbNo" compareValue="16"> order_16 </isEqual>
        <isEqual property="orderDbNo" compareValue="17"> order_17 </isEqual>
        <isEqual property="orderDbNo" compareValue="18"> order_18 </isEqual>
        <isEqual property="orderDbNo" compareValue="19"> order_19 </isEqual>
        <isEqual property="orderDbNo" compareValue="20"> order_20 </isEqual>
        <isEqual property="orderDbNo" compareValue="21"> order_21 </isEqual>
        <isEqual property="orderDbNo" compareValue="22"> order_22 </isEqual>
        <isEqual property="orderDbNo" compareValue="23"> order_23 </isEqual>
        <isEqual property="orderDbNo" compareValue="24"> order_24 </isEqual>
        <isEqual property="orderDbNo" compareValue="25"> order_25 </isEqual>
        <isEqual property="orderDbNo" compareValue="26"> order_26 </isEqual>
        <isEqual property="orderDbNo" compareValue="27"> order_27 </isEqual>
        <isEqual property="orderDbNo" compareValue="28"> order_28 </isEqual>
        <isEqual property="orderDbNo" compareValue="29"> order_29 </isEqual>
        <isEqual property="orderDbNo" compareValue="30"> order_30 </isEqual>
        <isEqual property="orderDbNo" compareValue="31"> order_31 </isEqual>
        <isEqual property="orderDbNo" compareValue="32"> order_32 </isEqual>
        <isEqual property="orderDbNo" compareValue="33"> order_33 </isEqual>
        <isEqual property="orderDbNo" compareValue="34"> order_34 </isEqual>
        <isEqual property="orderDbNo" compareValue="35"> order_35 </isEqual>
        <isEqual property="orderDbNo" compareValue="36"> order_36 </isEqual>
        <isEqual property="orderDbNo" compareValue="37"> order_37 </isEqual>
        <isEqual property="orderDbNo" compareValue="38"> order_38 </isEqual>
        <isEqual property="orderDbNo" compareValue="39"> order_39 </isEqual>
        <isEqual property="orderDbNo" compareValue="40"> order_40 </isEqual>
        <isEqual property="orderDbNo" compareValue="41"> order_41 </isEqual>
        <isEqual property="orderDbNo" compareValue="42"> order_42 </isEqual>
        <isEqual property="orderDbNo" compareValue="43"> order_43 </isEqual>
        <isEqual property="orderDbNo" compareValue="44"> order_44 </isEqual>
        <isEqual property="orderDbNo" compareValue="45"> order_45 </isEqual>
        <isEqual property="orderDbNo" compareValue="46"> order_46 </isEqual>
        <isEqual property="orderDbNo" compareValue="47"> order_47 </isEqual>
        <isEqual property="orderDbNo" compareValue="48"> order_48 </isEqual>
        <isEqual property="orderDbNo" compareValue="49"> order_49 </isEqual>
        <isEqual property="orderDbNo" compareValue="50"> order_50 </isEqual>
        <isEqual property="orderDbNo" compareValue="51"> order_51 </isEqual>
        <isEqual property="orderDbNo" compareValue="52"> order_52 </isEqual>
        <isEqual property="orderDbNo" compareValue="53"> order_53 </isEqual>
        <isEqual property="orderDbNo" compareValue="54"> order_54 </isEqual>
        <isEqual property="orderDbNo" compareValue="55"> order_55 </isEqual>
        <isEqual property="orderDbNo" compareValue="56"> order_56 </isEqual>
        <isEqual property="orderDbNo" compareValue="57"> order_57 </isEqual>
        <isEqual property="orderDbNo" compareValue="58"> order_58 </isEqual>
        <isEqual property="orderDbNo" compareValue="59"> order_59 </isEqual>
        <isEqual property="orderDbNo" compareValue="60"> order_60 </isEqual>
        <isEqual property="orderDbNo" compareValue="61"> order_61 </isEqual>
        <isEqual property="orderDbNo" compareValue="62"> order_62 </isEqual>
        <isEqual property="orderDbNo" compareValue="63"> order_63 </isEqual>
        <isEqual property="orderDbNo" compareValue="64"> order_64 </isEqual>
        <isEqual property="orderDbNo" compareValue="65"> order_65 </isEqual>
        <isEqual property="orderDbNo" compareValue="66"> order_66 </isEqual>
        <isEqual property="orderDbNo" compareValue="67"> order_67 </isEqual>
        <isEqual property="orderDbNo" compareValue="68"> order_68 </isEqual>
        <isEqual property="orderDbNo" compareValue="69"> order_69 </isEqual>
        <isEqual property="orderDbNo" compareValue="70"> order_70 </isEqual>
        <isEqual property="orderDbNo" compareValue="71"> order_71 </isEqual>
        <isEqual property="orderDbNo" compareValue="72"> order_72 </isEqual>
        <isEqual property="orderDbNo" compareValue="73"> order_73 </isEqual>
        <isEqual property="orderDbNo" compareValue="74"> order_74 </isEqual>
        <isEqual property="orderDbNo" compareValue="75"> order_75 </isEqual>
        <isEqual property="orderDbNo" compareValue="76"> order_76 </isEqual>
        <isEqual property="orderDbNo" compareValue="77"> order_77 </isEqual>
        <isEqual property="orderDbNo" compareValue="78"> order_78 </isEqual>
        <isEqual property="orderDbNo" compareValue="79"> order_79 </isEqual>
        <isEqual property="orderDbNo" compareValue="80"> order_80 </isEqual>
        <isEqual property="orderDbNo" compareValue="81"> order_81 </isEqual>
        <isEqual property="orderDbNo" compareValue="82"> order_82 </isEqual>
        <isEqual property="orderDbNo" compareValue="83"> order_83 </isEqual>
        <isEqual property="orderDbNo" compareValue="84"> order_84 </isEqual>
        <isEqual property="orderDbNo" compareValue="85"> order_85 </isEqual>
        <isEqual property="orderDbNo" compareValue="86"> order_86 </isEqual>
        <isEqual property="orderDbNo" compareValue="87"> order_87 </isEqual>
        <isEqual property="orderDbNo" compareValue="88"> order_88 </isEqual>
        <isEqual property="orderDbNo" compareValue="89"> order_89 </isEqual>
        <isEqual property="orderDbNo" compareValue="90"> order_90 </isEqual>
        <isEqual property="orderDbNo" compareValue="91"> order_91 </isEqual>
        <isEqual property="orderDbNo" compareValue="92"> order_92 </isEqual>
        <isEqual property="orderDbNo" compareValue="93"> order_93 </isEqual>
        <isEqual property="orderDbNo" compareValue="94"> order_94 </isEqual>
        <isEqual property="orderDbNo" compareValue="95"> order_95 </isEqual>
        <isEqual property="orderDbNo" compareValue="96"> order_96 </isEqual>
        <isEqual property="orderDbNo" compareValue="97"> order_97 </isEqual>
        <isEqual property="orderDbNo" compareValue="98"> order_98 </isEqual>
        <isEqual property="orderDbNo" compareValue="99"> order_99 </isEqual>
        <isEqual property="orderDbNo" compareValue="100"> order_100 </isEqual>
        <isEqual property="orderDbNo" compareValue="101"> order_101 </isEqual>
        <isEqual property="orderDbNo" compareValue="102"> order_102 </isEqual>
        <isEqual property="orderDbNo" compareValue="103"> order_103 </isEqual>
        <isEqual property="orderDbNo" compareValue="104"> order_104 </isEqual>
        <isEqual property="orderDbNo" compareValue="105"> order_105 </isEqual>
        <isEqual property="orderDbNo" compareValue="106"> order_106 </isEqual>
        <isEqual property="orderDbNo" compareValue="107"> order_107 </isEqual>
        <isEqual property="orderDbNo" compareValue="108"> order_108 </isEqual>
        <isEqual property="orderDbNo" compareValue="109"> order_109 </isEqual>
        <isEqual property="orderDbNo" compareValue="110"> order_110 </isEqual>
        <isEqual property="orderDbNo" compareValue="111"> order_111 </isEqual>
        <isEqual property="orderDbNo" compareValue="112"> order_112 </isEqual>
        <isEqual property="orderDbNo" compareValue="113"> order_113 </isEqual>
        <isEqual property="orderDbNo" compareValue="114"> order_114 </isEqual>
        <isEqual property="orderDbNo" compareValue="115"> order_115 </isEqual>
        <isEqual property="orderDbNo" compareValue="116"> order_116 </isEqual>
        <isEqual property="orderDbNo" compareValue="117"> order_117 </isEqual>
        <isEqual property="orderDbNo" compareValue="118"> order_118 </isEqual>
        <isEqual property="orderDbNo" compareValue="119"> order_119 </isEqual>
        <isEqual property="orderDbNo" compareValue="120"> order_120 </isEqual>
        <isEqual property="orderDbNo" compareValue="121"> order_121 </isEqual>
        <isEqual property="orderDbNo" compareValue="122"> order_122 </isEqual>
        <isEqual property="orderDbNo" compareValue="123"> order_123 </isEqual>
        <isEqual property="orderDbNo" compareValue="124"> order_124 </isEqual>
        <isEqual property="orderDbNo" compareValue="125"> order_125 </isEqual>
        <isEqual property="orderDbNo" compareValue="126"> order_126 </isEqual>
        <isEqual property="orderDbNo" compareValue="127"> order_127 </isEqual>
        <isEqual property="orderDbNo" compareValue="128"> order_128 </isEqual>
        <isEqual property="orderDbNo" compareValue="129"> order_129 </isEqual>
        <isEqual property="orderDbNo" compareValue="130"> order_130 </isEqual>
        <isEqual property="orderDbNo" compareValue="131"> order_131 </isEqual>
        <isEqual property="orderDbNo" compareValue="132"> order_132 </isEqual>
        <isEqual property="orderDbNo" compareValue="133"> order_133 </isEqual>
        <isEqual property="orderDbNo" compareValue="134"> order_134 </isEqual>
        <isEqual property="orderDbNo" compareValue="135"> order_135 </isEqual>
        <isEqual property="orderDbNo" compareValue="136"> order_136 </isEqual>
        <isEqual property="orderDbNo" compareValue="137"> order_137 </isEqual>
        <isEqual property="orderDbNo" compareValue="138"> order_138 </isEqual>
        <isEqual property="orderDbNo" compareValue="139"> order_139 </isEqual>
        <isEqual property="orderDbNo" compareValue="140"> order_140 </isEqual>
        <isEqual property="orderDbNo" compareValue="141"> order_141 </isEqual>
        <isEqual property="orderDbNo" compareValue="142"> order_142 </isEqual>
        <isEqual property="orderDbNo" compareValue="143"> order_143 </isEqual>
        <isEqual property="orderDbNo" compareValue="144"> order_144 </isEqual>
        <isEqual property="orderDbNo" compareValue="145"> order_145 </isEqual>
        <isEqual property="orderDbNo" compareValue="146"> order_146 </isEqual>
        <isEqual property="orderDbNo" compareValue="147"> order_147 </isEqual>
        <isEqual property="orderDbNo" compareValue="148"> order_148 </isEqual>
        <isEqual property="orderDbNo" compareValue="149"> order_149 </isEqual>
        <isEqual property="orderDbNo" compareValue="150"> order_150 </isEqual>
        <isEqual property="orderDbNo" compareValue="151"> order_151 </isEqual>
        <isEqual property="orderDbNo" compareValue="152"> order_152 </isEqual>
        <isEqual property="orderDbNo" compareValue="153"> order_153 </isEqual>
        <isEqual property="orderDbNo" compareValue="154"> order_154 </isEqual>
        <isEqual property="orderDbNo" compareValue="155"> order_155 </isEqual>
        <isEqual property="orderDbNo" compareValue="156"> order_156 </isEqual>
        <isEqual property="orderDbNo" compareValue="157"> order_157 </isEqual>
        <isEqual property="orderDbNo" compareValue="158"> order_158 </isEqual>
        <isEqual property="orderDbNo" compareValue="159"> order_159 </isEqual>
        <isEqual property="orderDbNo" compareValue="160"> order_160 </isEqual>
        <isEqual property="orderDbNo" compareValue="161"> order_161 </isEqual>
        <isEqual property="orderDbNo" compareValue="162"> order_162 </isEqual>
        <isEqual property="orderDbNo" compareValue="163"> order_163 </isEqual>
        <isEqual property="orderDbNo" compareValue="164"> order_164 </isEqual>
        <isEqual property="orderDbNo" compareValue="165"> order_165 </isEqual>
        <isEqual property="orderDbNo" compareValue="166"> order_166 </isEqual>
        <isEqual property="orderDbNo" compareValue="167"> order_167 </isEqual>
        <isEqual property="orderDbNo" compareValue="168"> order_168 </isEqual>
        <isEqual property="orderDbNo" compareValue="169"> order_169 </isEqual>
        <isEqual property="orderDbNo" compareValue="170"> order_170 </isEqual>
        <isEqual property="orderDbNo" compareValue="171"> order_171 </isEqual>
        <isEqual property="orderDbNo" compareValue="172"> order_172 </isEqual>
        <isEqual property="orderDbNo" compareValue="173"> order_173 </isEqual>
        <isEqual property="orderDbNo" compareValue="174"> order_174 </isEqual>
        <isEqual property="orderDbNo" compareValue="175"> order_175 </isEqual>
        <isEqual property="orderDbNo" compareValue="176"> order_176 </isEqual>
        <isEqual property="orderDbNo" compareValue="177"> order_177 </isEqual>
        <isEqual property="orderDbNo" compareValue="178"> order_178 </isEqual>
        <isEqual property="orderDbNo" compareValue="179"> order_179 </isEqual>
        <isEqual property="orderDbNo" compareValue="180"> order_180 </isEqual>
        <isEqual property="orderDbNo" compareValue="181"> order_181 </isEqual>
        <isEqual property="orderDbNo" compareValue="182"> order_182 </isEqual>
        <isEqual property="orderDbNo" compareValue="183"> order_183 </isEqual>
        <isEqual property="orderDbNo" compareValue="184"> order_184 </isEqual>
        <isEqual property="orderDbNo" compareValue="185"> order_185 </isEqual>
        <isEqual property="orderDbNo" compareValue="186"> order_186 </isEqual>
        <isEqual property="orderDbNo" compareValue="187"> order_187 </isEqual>
        <isEqual property="orderDbNo" compareValue="188"> order_188 </isEqual>
        <isEqual property="orderDbNo" compareValue="189"> order_189 </isEqual>
        <isEqual property="orderDbNo" compareValue="190"> order_190 </isEqual>
        <isEqual property="orderDbNo" compareValue="191"> order_191 </isEqual>
        <isEqual property="orderDbNo" compareValue="192"> order_192 </isEqual>
        <isEqual property="orderDbNo" compareValue="193"> order_193 </isEqual>
        <isEqual property="orderDbNo" compareValue="194"> order_194 </isEqual>
        <isEqual property="orderDbNo" compareValue="195"> order_195 </isEqual>
        <isEqual property="orderDbNo" compareValue="196"> order_196 </isEqual>
        <isEqual property="orderDbNo" compareValue="197"> order_197 </isEqual>
        <isEqual property="orderDbNo" compareValue="198"> order_198 </isEqual>
        <isEqual property="orderDbNo" compareValue="199"> order_199 </isEqual>
    </sql>


    <sql id="work_order">
        <isEqual property="workOrderDbNo" compareValue="0"> work_order_0 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="1"> work_order_1 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="2"> work_order_2 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="3"> work_order_3 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="4"> work_order_4 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="5"> work_order_5 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="6"> work_order_6 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="7"> work_order_7 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="8"> work_order_8 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="9"> work_order_9 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="10"> work_order_10 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="11"> work_order_11 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="12"> work_order_12 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="13"> work_order_13 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="14"> work_order_14 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="15"> work_order_15 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="16"> work_order_16 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="17"> work_order_17 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="18"> work_order_18 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="19"> work_order_19 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="20"> work_order_20 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="21"> work_order_21 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="22"> work_order_22 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="23"> work_order_23 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="24"> work_order_24 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="25"> work_order_25 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="26"> work_order_26 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="27"> work_order_27 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="28"> work_order_28 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="29"> work_order_29 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="30"> work_order_30 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="31"> work_order_31 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="32"> work_order_32 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="33"> work_order_33 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="34"> work_order_34 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="35"> work_order_35 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="36"> work_order_36 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="37"> work_order_37 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="38"> work_order_38 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="39"> work_order_39 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="40"> work_order_40 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="41"> work_order_41 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="42"> work_order_42 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="43"> work_order_43 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="44"> work_order_44 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="45"> work_order_45 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="46"> work_order_46 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="47"> work_order_47 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="48"> work_order_48 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="49"> work_order_49 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="50"> work_order_50 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="51"> work_order_51 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="52"> work_order_52 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="53"> work_order_53 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="54"> work_order_54 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="55"> work_order_55 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="56"> work_order_56 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="57"> work_order_57 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="58"> work_order_58 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="59"> work_order_59 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="60"> work_order_60 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="61"> work_order_61 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="62"> work_order_62 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="63"> work_order_63 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="64"> work_order_64 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="65"> work_order_65 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="66"> work_order_66 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="67"> work_order_67 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="68"> work_order_68 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="69"> work_order_69 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="70"> work_order_70 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="71"> work_order_71 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="72"> work_order_72 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="73"> work_order_73 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="74"> work_order_74 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="75"> work_order_75 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="76"> work_order_76 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="77"> work_order_77 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="78"> work_order_78 </isEqual>
        <isEqual property="workOrderDbNo" compareValue="79"> work_order_79 </isEqual>
    </sql>

    <sql id="item_snapshot">
        <isEqual property="itemSnapShotDbNo" compareValue="0"> item_snapshot_0 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="1"> item_snapshot_1 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="2"> item_snapshot_2 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="3"> item_snapshot_3 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="4"> item_snapshot_4 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="5"> item_snapshot_5 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="6"> item_snapshot_6 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="7"> item_snapshot_7 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="8"> item_snapshot_8 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="9"> item_snapshot_9 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="10"> item_snapshot_10 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="11"> item_snapshot_11 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="12"> item_snapshot_12 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="13"> item_snapshot_13 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="14"> item_snapshot_14 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="15"> item_snapshot_15 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="16"> item_snapshot_16 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="17"> item_snapshot_17 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="18"> item_snapshot_18 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="19"> item_snapshot_19 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="20"> item_snapshot_20 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="21"> item_snapshot_21 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="22"> item_snapshot_22 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="23"> item_snapshot_23 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="24"> item_snapshot_24 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="25"> item_snapshot_25 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="26"> item_snapshot_26 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="27"> item_snapshot_27 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="28"> item_snapshot_28 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="29"> item_snapshot_29 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="30"> item_snapshot_30 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="31"> item_snapshot_31 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="32"> item_snapshot_32 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="33"> item_snapshot_33 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="34"> item_snapshot_34 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="35"> item_snapshot_35 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="36"> item_snapshot_36 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="37"> item_snapshot_37 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="38"> item_snapshot_38 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="39"> item_snapshot_39 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="40"> item_snapshot_40 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="41"> item_snapshot_41 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="42"> item_snapshot_42 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="43"> item_snapshot_43 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="44"> item_snapshot_44 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="45"> item_snapshot_45 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="46"> item_snapshot_46 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="47"> item_snapshot_47 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="48"> item_snapshot_48 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="49"> item_snapshot_49 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="50"> item_snapshot_50 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="51"> item_snapshot_51 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="52"> item_snapshot_52 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="53"> item_snapshot_53 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="54"> item_snapshot_54 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="55"> item_snapshot_55 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="56"> item_snapshot_56 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="57"> item_snapshot_57 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="58"> item_snapshot_58 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="59"> item_snapshot_59 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="60"> item_snapshot_60 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="61"> item_snapshot_61 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="62"> item_snapshot_62 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="63"> item_snapshot_63 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="64"> item_snapshot_64 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="65"> item_snapshot_65 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="66"> item_snapshot_66 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="67"> item_snapshot_67 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="68"> item_snapshot_68 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="69"> item_snapshot_69 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="70"> item_snapshot_70 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="71"> item_snapshot_71 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="72"> item_snapshot_72 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="73"> item_snapshot_73 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="74"> item_snapshot_74 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="75"> item_snapshot_75 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="76"> item_snapshot_76 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="77"> item_snapshot_77 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="78"> item_snapshot_78 </isEqual>
        <isEqual property="itemSnapShotDbNo" compareValue="79"> item_snapshot_79 </isEqual>
    </sql>

    <!--订单宽表时间过滤场景-->
    <sql id="order_wide_time_fliter">
        <!-- 付款时间用trade的时间查询，原因是因为，一个订单的付款时间是统一支付的，并且不同平台逻辑上会保证trade有这个值 -->
        <isEqual property="sysStatus" compareValue="pay">
            and t_pay_time &gt;= #startTime#
            and t_pay_time &lt;= #endTime#
        </isEqual>
        <isEqual property="sysStatus" compareValue="consign">
            and o_consign_time &gt;= #startTime#
            and o_consign_time &lt;= #endTime#
        </isEqual>
        <isEqual property="sysStatus" compareValue="sys_consign">
            and o_consign_time &gt;= #startTime#
            and o_consign_time &lt;= #endTime#
            and o_sys_consigned =1
        </isEqual>
        <isEqual property="sysStatus" compareValue="end">
            and o_end_time &gt;= #startTime#
            and o_end_time &lt;= #endTime#
            and o_sys_status != 'CLOSED'
        </isEqual>
        <isEqual property="sysStatus" compareValue="finish">
            and o_end_time  &gt;= #startTime#
            and o_end_time  &lt;= #endTime#
            and t_pay_time > '2001-01-01'
        </isEqual>
        <isEqual property="sysStatus" compareValue="created">
            and o_created &gt;= #startTime#
            and o_created &lt;= #endTime#
            and t_pay_time>'2001-01-01'
        </isEqual>
        <isEqual property="sysStatus" compareValue="print">
            and t_express_print_time &gt;= #startTime#
            and t_express_print_time &lt;= #endTime#
            and t_pay_time>'2001-01-01'
        </isEqual>
    </sql>



    <sql id="brandCidMatch">
        <isNotEmpty property="brandIds">
            inner join (select itema.sys_item_id  as brand_sys_item_id from  <include refid="dmjItem"/> itema
            WHERE company_id = #companyId#
            AND enable_status = 1
            AND brand_id in
            <iterate property="brandIds" open="(" conjunction="," close=")">
                #brandIds[]#
            </iterate>
            ) itembrand
            on o_item_sys_id = itembrand.brand_sys_item_id
        </isNotEmpty>

        <isNotEmpty property="classifyIds">

            INNER JOIN

            (
                ( SELECT i.sys_item_id AS k_sys_item_id, string_agg(s.name, ',') AS item_kind,cat_id FROM
                    <include refid="dmjItem"/> i INNER JOIN
                    <include refid="sellerCat"/> s ON i.company_id = s.company_id
                    WHERE i.company_id = #companyId# AND s.company_id = #companyId#  AND

                    position(s.cid in i.seller_cids) > 0
                    <isNotEmpty property="classifyIds" prepend="AND">
                        s.cid
                        <isEqual property="classifyContain" compareValue="0">
                            NOT IN
                        </isEqual>
                        <isEqual property="classifyContain" compareValue="1">
                            IN
                        </isEqual>
                        <iterate property="classifyIds" open="(" conjunction="," close=")">
                            #classifyIds[]#
                        </iterate>
                    </isNotEmpty>
                )
                <isEqual property="cidFlag" compareValue="1">
                union
                (
                    select dmjitem2.sys_item_id AS k_sys_item_id,'未分类' as item_kind from
                    <include refid="dmjItem"/> dmjitem2 where dmjitem2.company_id = #companyId#  AND (dmjitem2.seller_cids='' or dmjitem2.seller_cids='-1')
                )
                </isEqual>
            ) kind on o_item_sys_id = kind.k_sys_item_id

        </isNotEmpty>

        <!--商品类目-->
        <isNotEmpty property="categoryIds" >
            inner join (select itemcatin.sys_item_id  as cat_sys_item_id from  <include refid="dmjItem"/> itemcatin
            WHERE company_id = #companyId#
            AND enable_status = 1
            AND itemcatin.cat_id
            <isEqual prepend="categoryContain" compareValue="0">
                NOT IN
            </isEqual>
            <isEqual prepend="categoryContain" compareValue="1">
                IN
            </isEqual>
            <iterate property="categoryIds" open="(" conjunction="," close=")">
                #categoryIds[]#
            </iterate>
        </isNotEmpty>
            ) itemcat
            on o_item_sys_id = itemcat.cat_sys_item_id

    </sql>

    <sql id="common_time_filter">
        <isEqual property="sysStatus" compareValue="pay">
            t_pay_time
        </isEqual>
        <isEqual property="sysStatus" compareValue="consign">
            o_consign_time
        </isEqual>
        <isEqual property="sysStatus" compareValue="sys_consign">
            o_consign_time
        </isEqual>
        <isEqual property="sysStatus" compareValue="end">
            o_end_time
        </isEqual>
        <isEqual property="sysStatus" compareValue="finish">
            o_end_time
        </isEqual>
        <isEqual property="sysStatus" compareValue="created">
            o_created
        </isEqual>
        <isEqual property="sysStatus" compareValue="print">
            t_express_print_time
        </isEqual>
    </sql>

    <sql id="order_where">
        WHERE
        ow.o_company_id = #companyId#
        and ow.t_enable_status in(1,2) and ow.t_is_cancel=0
        and ow.o_enable_status =1 and ow.o_is_cancel=0
        AND ow.pt = #pt#
        and ow.o_item_sys_id > 1
        <!--剔除换货 补发-->
        and ow.t_type not in('changeitem','reissue')
        <include refid="order_wide_time_fliter"/>
        <isEqual property="showSuitFlag" compareValue="1">
            and ow.o_combine_id = 0
        </isEqual>
        <isEqual property="showSuitFlag" compareValue="0">
            <!-- 普通可以扣减库存的商品，如套件下的子商品 -->
            and (
                (ow.o_type in (0,1,3,4) and ow.o_combine_id = 0) OR
                (ow.o_type = 2 AND ow.o_combine_id > 0)
            )
        </isEqual>
        <!-- 只查询可以扣减库存的单品，排除第三层数据 -->
        AND (o_level is null OR o_level not in ('230', '240'))
        <isEqual property="scalping" compareValue="0">
            and COALESCE(ow.m_scalping,ow.t_scalping) = #scalping#
        </isEqual>
        <isEqual property="nonConsign" compareValue="0">
            and (ow.o_non_consign is null or ow.o_non_consign = '0')
        </isEqual>
        <isEqual property="nonConsign" compareValue="1">
            and ow.o_non_consign = '1'
        </isEqual>
        <isNotEmpty property="userIds" prepend="and">
            COALESCE(ow.m_user_id,ow.t_user_id) in
            <iterate property="userIds" conjunction="," open="(" close=")">
                #userIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="warehouseIds" prepend="and">
            COALESCE(ow.m_warehouse_id,ow.t_warehouse_id) in
            <iterate property="warehouseIds" conjunction="," open="(" close=")">
                #warehouseIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="templateId" prepend="and">
            COALESCE(ow.m_template_id,ow.t_template_id) = #templateId#
        </isNotEmpty>
        <isNotEmpty property="templateIds" prepend="and">
            COALESCE(ow.m_template_id,ow.t_template_id) in
            <iterate property="templateIds" conjunction="," open="(" close=")">
                #templateIds[]#
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="outerIdList" prepend="and">
            <isEqual property="isOuterIdFuzzy" compareValue="false">
                ow.o_sys_outer_id in
                <iterate property="outerIdList" conjunction="," open="(" close=")">
                    #outerIdList[]#
                </iterate>
            </isEqual>
            <isNotEqual property="isOuterIdFuzzy" compareValue="false">
                <iterate property="outerIdList" conjunction="OR" open="(" close=")">
                    ow.o_sys_outer_id ILIKE  '%' || #outerIdList[]# || '%'
                </iterate>
            </isNotEqual>
        </isNotEmpty>

        <isNotEmpty property="sellerFlags" prepend="AND">
            (1=1 AND COALESCE(ow.m_seller_flag,ow.t_seller_flag) in
            <iterate property="sellerFlags" conjunction="," open="(" close=")">
                #sellerFlags[]#
            </iterate>
            <isEqual property="nullSellerFlag" compareValue="1" prepend="or">
                COALESCE(ow.m_seller_flag,ow.t_seller_flag) is NULL
            </isEqual>
            )
        </isNotEmpty>

        <isNotEmpty property="orderTags" prepend="and">
            <isEqual property="orderTagsContain" compareValue="0">
                <iterate property="orderTags" conjunction=" AND " open="(" close=")">
                    position(#orderTags[]# in ow.tag_ids) &lt;= 0
                </iterate>
            </isEqual>
            <isEqual property="orderTagsContain" compareValue="1">
                <iterate property="orderTags" conjunction=" OR " open="(" close=")">
                    position(#orderTags[]# in ow.tag_ids) &gt; 0
                </iterate>
            </isEqual>

        </isNotEmpty>

        <isEmpty property="virtualFlag" prepend="and">
            <!--默认过滤虚拟商品-->
            ow.o_is_virtual &lt;= 0
        </isEmpty>


        <!--备注过滤 主要过滤vip刷单-->
        <isNotEmpty property="sysMemo">
            AND ow.t_sys_memo
            <isEqual property="sysMemoCotain" compareValue="1">
                ILIKE
            </isEqual>
            <isEqual property="sysMemoCotain" compareValue="0">
                NOT ILIKE
            </isEqual>
            '%' || #sysMemo# || '%'
        </isNotEmpty>

        <!--卖家备注过滤 主要过滤vip刷单-->
        <isNotEmpty property="sellerMemo">
            AND ow.t_seller_memo
            <isEqual property="sellerMemoContain" compareValue="1">
                ILIKE
            </isEqual>
            <isEqual property="sellerMemoContain" compareValue="0">
                NOT ILIKE
            </isEqual>
            '%' || #sellerMemo# || '%'
        </isNotEmpty>

        <!--商品过滤-->
        <isNotEmpty property="itemQuery">
            <isNotEmpty property="sysItemIds" prepend="and">
                ow.o_item_sys_id IN
                <iterate conjunction="," open="(" close=")" property="sysItemIds">
                    #sysItemIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty property="sysSkuIds" prepend="and">
                ow.o_sku_sys_id IN
                <iterate conjunction="," open="(" close=")" property="sysSkuIds">
                    #sysSkuIds[]#
                </iterate>
            </isNotEmpty>
        </isNotEmpty>

    </sql>

    <sql id="afterSaleWhere">
        WHERE
        o_company_id = #companyId#
        AND w_status = 9
        AND pt = #pt#
        AND w_enable_status = 1
        AND i_enable_status = 1
        AND w_after_sale_type IN (1, 2, 4, 5)
        and i_sys_item_id > 0
        <include refid="order_wide_time_fliter"/>
        and (
            <!--普通商品和转单品的-->
            i_suite = '0' or ( i_suite = '1' and i_suite_single = '2')
            <!--套件装商品显示明细-->
            OR ( i_suite = '1' and i_suite_single = '1')
            <!--组合装商品不显示明细-->
            OR (i_suite = '2' and i_suite_single = '0')
            <!--加工装商品不显示明细-->
            OR (i_suite = '3' and i_suite_single = '0')
        )
        <!-- 只查询可以扣减库存的单品，排除第三层数据 -->
        and (i_level is null OR i_level not in ('120', '130'))
        <!--商品过滤-->
        <isNotEmpty property="itemQuery">
            <isNotEmpty property="sysItemIds" prepend="and">
                i_sys_item_id IN
                <iterate conjunction="," open="(" close=")" property="sysItemIds">
                    #sysItemIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty property="sysSkuIds" prepend="and">
                i_sys_sku_id IN
                <iterate conjunction="," open="(" close=")" property="sysSkuIds">
                    #sysSkuIds[]#
                </iterate>
            </isNotEmpty>
        </isNotEmpty>
        <isNotEmpty property="userIds" prepend="and">
            user_id in
            <iterate property="userIds" conjunction="," open="(" close=")">
                #userIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="warehouseIds" prepend="and">
            r_ware_house_id in
            <iterate property="warehouseIds" conjunction="," open="(" close=")">
                #warehouseIds[]#
            </iterate>
        </isNotEmpty>
    </sql>

    <sql id="order_dts_where">
        where
        t.company_id = #companyId#
        and io.company_id = #companyId#
        and t.scalping != 1
        and t.is_cancel = 0
        and io.enable_status = 1
        and io.item_sys_id > 0
        <!-- 普通可以扣减库存的商品，如套件下的子商品 -->
        and (
            (io.type in (0,1,3,4) and io.combine_id = 0) OR
            (io.type = 2 AND io.combine_id > 0)
        )
        and t.type != 'trade_out'
        and t.type not in('changeitem','reissue')
        <isEqual property="sysStatus" compareValue="pay">
            and io.pay_time &gt;= #startTime#
            and io.pay_time &lt;= #endTime#
        </isEqual>
        <isEqual property="sysStatus" compareValue="consign">
            and io.consign_time &gt;= #startTime#
            and io.consign_time &lt;= #endTime#
        </isEqual>
        <isEqual property="sysStatus" compareValue="sys_consign">
            and io.consign_time &gt;= #startTime#
            and io.consign_time &lt;= #endTime#
            and io.sys_consigned =1
        </isEqual>
        <isEqual property="sysStatus" compareValue="end">
            and case when io.end_time &lt;= '2001-01-01' and io.sys_status != 'CLOSED' then io.end_time else io.end_time end &gt;= #startTime#
            and case when io.end_time &lt;= '2001-01-01' and io.sys_status != 'CLOSED' then io.end_time else io.end_time end &lt;= #endTime#
            and io.sys_status != 'CLOSED'
        </isEqual>
        <isEqual property="sysStatus" compareValue="finish">
            and case when io.end_time &lt;= '2001-01-01' and io.sys_status != 'CLOSED' then io.end_time else io.end_time end &gt;= #startTime#
            and case when io.end_time &lt;= '2001-01-01' and io.sys_status != 'CLOSED' then io.end_time else io.end_time end &lt;= #endTime#
            and t.pay_time>'2001-01-01'
        </isEqual>
        <isEqual property="sysStatus" compareValue="created">
            and io.created &gt;= #startTime#
            and io.created &lt;= #endTime#
            and t.pay_time>'2001-01-01'
        </isEqual>
        <isEqual property="sysStatus" compareValue="print">
            and t.express_print_time &gt;= #startTime#
            and t.express_print_time &lt;= #endTime#
            and t.pay_time>'2001-01-01'
        </isEqual>

        <isNotEmpty property="userIds" prepend="and">
            t.user_id in
            <iterate property="userIds" conjunction="," open="(" close=")">
                #userIds[]#
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="warehouseIds" prepend="and">
            t.warehouse_id in
            <iterate property="warehouseIds" conjunction="," open="(" close=")">
                #warehouseIds[]#
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="sellerFlags" prepend="AND">
            (1=1 AND t.seller_flag in
            <iterate property="sellerFlags" conjunction="," open="(" close=")">
                #sellerFlags[]#
            </iterate>
            <isEqual property="sellerFlagContain" compareValue="0">
                OR t.seller_flag is NULL
            </isEqual>
            )
        </isNotEmpty>

        <isEmpty property="virtualFlag" prepend="and">
            <!--默认过滤虚拟商品-->
            io.is_virtual &lt;= 0
        </isEmpty>

        <!--备注过滤 主要过滤vip刷单-->
        <isNotEmpty property="sysMemo">
            AND t.sys_memo
            <isEqual property="sysMemoCotain" compareValue="1">
                ILIKE
            </isEqual>
            <isEqual property="sysMemoCotain" compareValue="0">
                NOT ILIKE
            </isEqual>
            '%' || #sysMemo# || '%'
        </isNotEmpty>

        <!--卖家备注过滤 主要过滤vip刷单-->
        <isNotEmpty property="sellerMemo">
            AND t.seller_memo
            <isEqual property="sellerMemoContain" compareValue="1">
                ILIKE
            </isEqual>
            <isEqual property="sellerMemoContain" compareValue="0">
                NOT ILIKE
            </isEqual>
            '%' || #sellerMemo# || '%'
        </isNotEmpty>

        <!--商品过滤-->
        <isNotEmpty property="itemQuery">
            <isNotEmpty property="sysItemIds" prepend="and">
                io.item_sys_id IN
                <iterate conjunction="," open="(" close=")" property="sysItemIds">
                    #sysItemIds[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty property="sysSkuIds" prepend="and">
                io.sku_sys_id IN
                <iterate conjunction="," open="(" close=")" property="sysSkuIds">
                    #sysSkuIds[]#
                </iterate>
            </isNotEmpty>
        </isNotEmpty>

        <isNotEmpty property="outerIdList" prepend="and">
            <iterate property="outerIdList" conjunction="OR" open="(" close=")">
                io.sys_outer_id ILIKE  '%' || #outerIdList[]# || '%'
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="orderTags" prepend="and">
            <isEqual property="orderTagsContain" compareValue="0">
                <iterate property="orderTags" conjunction=" AND " open="(" close=")">
                    position(#orderTags[]# in t.tag_ids) &lt;= 0
                </iterate>
            </isEqual>
            <isEqual property="orderTagsContain" compareValue="1">
                <iterate property="orderTags" conjunction=" OR " open="(" close=")">
                    position(#orderTags[]# in t.tag_ids) &gt; 0
                </iterate>
            </isEqual>
        </isNotEmpty>

    </sql>

    <sql id="query_dts_base">
        (
        select
        o.*,
        ROW_NUMBER() OVER(PARTITION BY o.id order by o.upd_time ) AS seq1,
        ROW_NUMBER() OVER(PARTITION BY o.id order by o.upd_time) AS seq2,
        COALESCE(COALESCE(tb.actual_refund_num,sys.actual_refund_num),0.00) actual_refund_num
        from
        (
            select
                t.company_id,
                io.id ,
                t.sid ,
                t.tid,
                io.num as o_num,
                io.item_sys_id,
                io.sku_sys_id,
                io.upd_time ,
                io.pay_time
            FROM
            (
                select * from <include refid="order_table"/> where company_id = #companyId#
            ) io
            inner join
            (
                select * from <include refid="trade_table"/> where company_id = #companyId#
            ) t
            on io.sid=t.sid and io.company_id = t.company_id
                <include refid="order_dts_where"/>
        ) o
        left join
        (
        select
        item.id,
        work.tid,
        item.sys_item_id,
        case when item.sys_sku_id=0 then -1 else item.sys_sku_id end as sys_sku_id ,
        item.company_id,
        CASE WHEN item.type = 1 THEN COALESCE(item.receivable_count,0.00) ELSE 0.00 END as actual_refund_num
        from <include refid="work_order"/> work inner join <include refid="item_snapshot"/> item
        on work.id = item.work_order_id and work.company_id=item.company_id
        where
        work.company_id= #companyId# and work.enable_status = 1  and  work.after_sale_type in(1,2,4,5) and work.source !='sys' and work.status=9 and
        item.company_id= #companyId# and (item.suite = '0' OR item.suite_single = '1')

        <isEqual property="tradePgDb" compareValue="polardb">
            and "is_match" = 0
        </isEqual>
        <isNotEqual property="tradePgDb" compareValue="polardb">
            and "is_matCh" = 0
        </isNotEqual>


        and (item.enable_status = 1 or item.enable_status is null) and item.type != 2
        <isNotEmpty property="userIds" prepend="and">
            work.user_id in
            <iterate property="userIds" conjunction="," open="(" close=")">
                #userIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="warehouseIds" prepend="and">
            work.refund_warehouse_id in
            <iterate property="warehouseIds" conjunction="," open="(" close=")">
                #warehouseIds[]#
            </iterate>
        </isNotEmpty>
        ) tb
        on tb.tid=o.tid and tb.sys_item_id=o.item_sys_id and tb.sys_sku_id =o.sku_sys_id and tb.company_id=o.company_id
        left join
        (
        select
        item.id,
        work.sid,
        item.sys_item_id,
        case when item.sys_sku_id=0 then -1 else item.sys_sku_id end as sys_sku_id ,
        item.company_id,
        CASE WHEN item.type = 1 THEN COALESCE(item.receivable_count,0.00) ELSE 0.00 END as actual_refund_num
        from <include refid="work_order"/> work inner join <include refid="item_snapshot"/> item
        on work.id = item.work_order_id and work.company_id=item.company_id
        where
        work.company_id= #companyId# and work.enable_status = 1  and  work.after_sale_type in(1,2,4,5) and work.source ='sys' and work.status=9 and
        item.company_id= #companyId# and (item.suite = '0' OR item.suite_single = '1')

        <isEqual property="tradePgDb" compareValue="polardb">
            and "is_match" = 0
        </isEqual>
        <isNotEqual property="tradePgDb" compareValue="polardb">
            and "is_matCh" = 0
        </isNotEqual>

        and (item.enable_status = 1 or item.enable_status is null) and item.type != 2
        <isNotEmpty property="userIds" prepend="and">
            work.user_id in
            <iterate property="userIds" conjunction="," open="(" close=")">
                #userIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="warehouseIds" prepend="and">
            work.refund_warehouse_id in
            <iterate property="warehouseIds" conjunction="," open="(" close=")">
                #warehouseIds[]#
            </iterate>
        </isNotEmpty>
        )sys
        on  sys.sid=o.sid and sys.company_id=o.company_id and sys.sys_item_id=o.item_sys_id  and sys.sys_sku_id=o.sku_sys_id
        )tab

    </sql>

    <!--销量权重-->
    <sql id="fixedOrderWeight">
        <isNotEmpty property="weightList">
            <iterate open="CASE WHEN " close=" ELSE o_num END" conjunction= "WHEN" property="weightList">
                to_char(to_date(<include refid="common_time_filter"/>,'yyyy-MM-dd'),'YYYY-MM-DD') = split_part(#weightList[]#,'_',1) THEN o_num * cast(split_part(#weightList[]#,'_',2) as NUMERIC )
            </iterate>
        </isNotEmpty>
        <isEmpty property="weightList">
            o_num
        </isEmpty>
    </sql>

    <!--销退权重-->
    <sql id="fixedRefundWeight">
        <isNotEmpty property="weightList">
            <iterate open=" CASE WHEN " close=" ELSE CASE WHEN i_type = 1 THEN COALESCE(i_receivable_count,0.00) ELSE 0.00 END END" conjunction="WHEN" property="weightList">
                to_char(to_date(<include refid="common_time_filter"/>,'yyyy-MM-dd'),'YYYY-MM-DD') = split_part(#weightList[]#,'_',1) THEN CASE WHEN i_type = 1 THEN COALESCE(i_receivable_count,0) ELSE 0 END * cast(split_part(#weightList[]#,'_',2) as NUMERIC )
            </iterate>
        </isNotEmpty>
        <isEmpty property="weightList">
            CASE WHEN i_type = 1 THEN COALESCE(i_receivable_count,0.00) ELSE 0.00 END
        </isEmpty>
    </sql>

    <!--权重-->
    <sql id="fixedWeightDts">
        <isNotEmpty property="weightList">
            <iterate open=" CASE WHEN " close=" ELSE o_num -  COALESCE(actual_refund_num,0.00) END" conjunction = "WHEN" property="weightList">
                to_char(pay_time,'YYYY-MM-DD') = split_part(#weightList[]#,'_',1) THEN (o_num -  COALESCE(actual_refund_num,0.00)) * cast(split_part(#weightList[]#,'_',2) as NUMERIC )
            </iterate>
        </isNotEmpty>
        <isEmpty property="weightList">
            o_num -  COALESCE(actual_refund_num,0.00)
        </isEmpty>
    </sql>

    <select id="list4SkuFixed" resultClass="ReportSaleDimensions">

        select
        o.sysItemId,
        o.sysSkuId,
        o.firstSubSaleNum  - COALESCE(refund.firstSubReturnNum,0.00) as firstSubSaleNum,
        o.secondSubSaleNum - COALESCE(refund.secondSubReturnNum,0.00) as secondSubSaleNum,
        o.thirdSubSaleNum - COALESCE(refund.thirdSubReturnNum,0.00) as thirdSubSaleNum,
        o.threeSaleNum - COALESCE(refund.threeReturnNum,0.00) as threeSaleNum,
        o.fiveSaleNum - COALESCE(refund.fiveReturnNum,0.00) as fiveSaleNum,
        o.sevenSaleNum -  COALESCE(refund.sevenReturnNum,0.00) as sevenSaleNum,
        o.customSaleNum -  COALESCE(refund.customReturnNum,0.00) as customSaleNum
        FROM
        (
            SELECT
            o_item_sys_id as sysItemId,
            o_sku_sys_id as sysSkuId,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -1 THEN <include refid="fixedOrderWeight"/> ELSE 0 END) as firstSubSaleNum,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE-1 AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -2 THEN <include refid="fixedOrderWeight"/> ELSE 0 END) as secondSubSaleNum,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE-2 AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -3 THEN <include refid="fixedOrderWeight"/> ELSE 0 END) as thirdSubSaleNum,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -3 THEN <include refid="fixedOrderWeight"/> ELSE 0 END) as threeSaleNum,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE - 5 THEN <include refid="fixedOrderWeight"/> ELSE 0 END) as fiveSaleNum,
            SUM( CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE - 7 THEN <include refid="fixedOrderWeight"/> ELSE 0 END) as sevenSaleNum
            <isNotEmpty property="customSaleNumDay">
                , SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE - #customSaleNumDay# THEN <include refid="fixedOrderWeight"/> ELSE 0 END) as customSaleNum
            </isNotEmpty>
            <isEmpty property="customSaleNumDay">
                , 0 as customSaleNum
            </isEmpty>

            from
            order_wide ow
            <include refid="order_where"/>

            GROUP BY t_company_id, o_item_sys_id , o_sku_sys_id
        ) o
        left join
        (
            SELECT
            i_sys_item_id, i_sys_sku_id,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -1 THEN <include refid="fixedRefundWeight"/> ELSE 0 END) as firstSubReturnNum,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE-1 AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -2 THEN <include refid="fixedRefundWeight"/> ELSE 0 END) as secondSubReturnNum,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE-2 AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -3 THEN <include refid="fixedRefundWeight"/> ELSE 0 END) as thirdSubReturnNum,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -3 THEN <include refid="fixedRefundWeight"/> ELSE 0 END) as threeReturnNum,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE - 5 THEN <include refid="fixedRefundWeight"/> ELSE 0 END) as fiveReturnNum,
            SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE - 7 THEN <include refid="fixedRefundWeight"/> ELSE 0 END) as sevenReturnNum
            <isNotEmpty property="customSaleNumDay">
                , SUM(CASE WHEN to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(<include refid="common_time_filter"/>,'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE - #customSaleNumDay# THEN <include refid="fixedRefundWeight"/> ELSE 0 END) as  customReturnNum
            </isNotEmpty>
            <isEmpty property="customSaleNumDay">
                , 0 as customReturnNum
            </isEmpty>
            FROM worder_manual_wide
            <include refid="afterSaleWhere"/>
            GROUP BY o_company_id, i_sys_item_id, i_sys_sku_id
        )refund
        ON o.sysItemId = refund.i_sys_item_id AND o.sysSkuId = refund.i_sys_sku_id
    </select>

    <select id="list4SkuByDate" resultClass="ReportSaleDimensions">
        <isEqual property="filterByItem" compareValue="true">
            WITH dmj_item_table AS (
                select outer_id, sys_item_id
                from dmj_item_supple
                where company_id = #companyId#
                    <isNotNull property="isItemOuterIdFuzzy">
                        <isEqual property="isItemOuterIdFuzzy" compareValue="false" prepend="and">
                            outer_id in
                            <iterate property="itemOuterIdList" conjunction="," open="(" close=")">
                                #itemOuterIdList[]#
                            </iterate>
                        </isEqual>
                        <isNotEqual property="isItemOuterIdFuzzy" compareValue="false" prepend="and">
                            <iterate property="itemOuterIdList" conjunction="OR" open="(" close=")">
                                outer_id ILIKE  '%' || #itemOuterIdList[]# || '%'
                            </iterate>
                        </isNotEqual>
                    </isNotNull>
                    <isNotEmpty property="catIds" prepend="AND">
                        cat_id IN
                        <iterate property="catIds" open="(" conjunction="," close=")">
                            #catIds[]#
                        </iterate>
                    </isNotEmpty>
                    <isNotEmpty property="brandIds" prepend="AND">
                        brand_id IN
                        <iterate property="brandIds" open="(" conjunction="," close=")">
                            #brandIds[]#
                        </iterate>
                    </isNotEmpty>
            )
        </isEqual>
        select
            o.sysItemId,
            o.sysSkuId,
            o.saleNum -  COALESCE(refund.returnNum,0) as saleNum
        FROM
        (
            SELECT
                o_item_sys_id as sysItemId,
                o_sku_sys_id as sysSkuId,
                SUM( <include refid="fixedOrderWeight"/> ) saleNum
            from
            order_wide ow
            <isEqual property="filterByItem" compareValue="true">
                inner join dmj_item_table item on ow.o_item_sys_id = item.sys_item_id
            </isEqual>
            <include refid="order_where"/>
            GROUP BY t_company_id, o_item_sys_id , o_sku_sys_id
        ) o
        left join
        (
            SELECT
                i_sys_item_id, i_sys_sku_id,
                SUM( <include refid="fixedRefundWeight"/> ) returnNum
            FROM worder_manual_wide
            <include refid="afterSaleWhere"/>
            GROUP BY o_company_id, i_sys_item_id, i_sys_sku_id
        )refund
        ON o.sysItemId = refund.i_sys_item_id AND o.sysSkuId = refund.i_sys_sku_id
        order by o.sysItemId, o.sysSkuId
        <isNotEmpty property="page">
            limit #page.pageSize# OFFSET #page.startRow#
        </isNotEmpty>
    </select>


    <select id="list4SkuFixedDts" resultClass="ReportSaleDimensions">
        select
        item_sys_id sysItemId,
        sku_sys_id sysSkuId,
        SUM(CASE WHEN to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -1 THEN <include refid="fixedWeightDts"/> ELSE 0.00 END) as firstSubSaleNum,
        SUM(CASE WHEN to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE-1 AND to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -2 THEN <include refid="fixedWeightDts"/> ELSE 0.00 END) as secondSubSaleNum,
        SUM(CASE WHEN to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE-2 AND to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -3 THEN <include refid="fixedWeightDts"/> ELSE 0.00 END) as thirdSubSaleNum,
        SUM(CASE WHEN to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE -3 THEN  <include refid="fixedWeightDts"/> ELSE 0.00 END) as threeSaleNum,
        SUM(CASE WHEN to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE - 5 THEN  <include refid="fixedWeightDts"/> ELSE 0.00 END) as fiveSaleNum,
        SUM(CASE WHEN to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE - 7 THEN  <include refid="fixedWeightDts"/> ELSE 0.00 END) as sevenSaleNum
        <isNotEmpty property="customSaleNumDay">
            , SUM(CASE WHEN to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &lt; CURRENT_DATE AND to_timestamp(cast(pay_time as VARCHAR),'yyyy-MM-dd hh24:mi:ss') &gt;= CURRENT_DATE - #customSaleNumDay# THEN  <include refid="fixedWeightDts"/> ELSE 0.00 END) as customSaleNum
        </isNotEmpty>
        <isEmpty property="customSaleNumDay">
            , 0 as customSaleNum
        </isEmpty>
        from
        <include refid="query_dts_base"/>
        where (tab.seq1=1 or tab.seq2 = 1)
        group by tab.item_sys_id,tab.sku_sys_id
    </select>


    <select id="list4SkuByDateDts" resultClass="ReportSaleDimensions">
        select
        item_sys_id sysItemId,
        sku_sys_id sysSkuId,
        SUM( <include refid="fixedWeightDts"/> ) as saleNum,
        MAX(id) as o_id
        from
        <include refid="query_dts_base"/>
        where (tab.seq1=1 or tab.seq2 = 1)
        group by tab.item_sys_id,tab.sku_sys_id
        order by o_id desc
        <isNotEmpty property="page">
            limit #page.pageSize# OFFSET #page.startRow#
        </isNotEmpty>
    </select>


</sqlMap>

package com.raycloud.dmj.services.caigou;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.aware.ItemPriceAware;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.caigou.*;
import com.raycloud.dmj.domain.caigou.result.BatchResult;
import com.raycloud.dmj.domain.caigou.result.SuccessItem;
import com.raycloud.dmj.domain.caigou.vo.PurchaseBillVo;
import com.raycloud.dmj.domain.caigou.vo.ThirdPurchaseQueryRequestVO;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.wave.WaveUniqueCode;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.web.model.caigou.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 采购订单服务接口
 *
 * <AUTHOR>
 */
public interface IPurchaseOrderService {

    /**
     * 更新插入采购单
     *
     * @param staff
     * @param updateOrders
     * @param updateDetails
     * @param insertOrders
     * @param insertDetails
     */
    void updatePurchaseOrders(Staff staff, List<PurchaseOrder> updateOrders, List<PurchaseOrderDetail> updateDetails, List<PurchaseOrder> insertOrders, List<PurchaseOrderDetail> insertDetails);

    /**
     * 更新采购单
     *
     * @param staff
     * @param updateOrders
     * @param updateDetails
     */
    void updatePurchaseOrders(Staff staff, List<PurchaseOrder> updateOrders, List<PurchaseOrderDetail> updateDetails);

    /**
     * 创建收货单，第三方接口根据采购单创建
     *
     * @param staff
     * @param we
     * @return
     */
    WarehouseEntry createWeFromPo(Staff staff, WarehouseEntry we) throws Throwable;

    WarehouseEntry createWe4NoWms(Staff staff, WarehouseEntry we);

    /**
     * 创建收货单
     *
     * @param staff
     * @param purchaseOrder
     */
    void createWarehouseEntry(Staff staff, PurchaseOrder purchaseOrder);

    /**
     * 更新采购在途数
     *
     * @param staff
     */
    void updateOnWayQuantities(Staff staff, List<Long> poIds);
    void newUpdateOnWayQuantities(Staff staff, List<Long> poIds, Boolean update);

    /**
     * 采购单完成时候作废未完成的收货单和上架单
     *
     * @param staff
     * @param purchaseOrderIds
     */
    void cancelAssoWesAndSos(Staff staff, List<Long> purchaseOrderIds);
    /**
     * 查询采购单下的商品
     *
     * @param staff
     * @param purchaseOrderId 采购单id
     * @param queryType       查询关键字
     * @param queryText
     * @param outerIds
     */
    List<PurchaseOrderDetail> queryDetailItems(Staff staff, Long purchaseOrderId, String queryType, String queryText, Boolean isAccurate, List<String> outerIds);

    /**
     * 查询集合
     *
     * @param purchaseOrderQueryParams
     * @param page                     @return
     */
    PurchaseOrders list(Staff staff, PurchaseOrderQueryParams purchaseOrderQueryParams, Page page, Sort sort);

    /**
     * 查询采购单列表
     *
     * @param staff
     * @param purchaseOrderQueryParams
     * @param page
     * @param sort
     * @return
     */
    List<PurchaseOrder> listSimple(Staff staff, PurchaseOrderQueryParams purchaseOrderQueryParams, Page page, Sort sort);

    /**
     * 根据采购订单的系统编号查询
     *
     * @param staff
     * @param id
     * @param includeDetail 是否包含详情数据
     * @return
     */
    PurchaseOrder queryById(Staff staff, Long id, boolean includeDetail);

    /**
     * 根据平台订单号查询采购单
     *
     * @param staff
     * @param ptOrderId
     * @param includeDetail 是否包含详情数据
     * @return
     */
    PurchaseOrder queryByPtOrderId(Staff staff, String ptOrderId, boolean includeDetail);

    /**
     * 根据采购单id列出所有商品，相同需要合并
     *
     * @param staff
     * @param poIds
     * @return
     */
    List<PurchaseOrderDetail> listItemsByPoIds(Staff staff, List<Long> poIds, String text);

    /**
     * 根据编码查询采购单
     *
     * @param staff
     * @param codes      采购单编码
     * @param withDetail
     * @return
     */
    List<PurchaseOrder> queryByCodes(Staff staff, List<String> codes, boolean withDetail);

    /**
     * 查询包含某个商品的采购单
     *
     * @param staff
     * @param page
     * @return
     */
    PageList<PurchaseOrderDetailOnWay> queryOnWayQuantitiesPurchaseOrder(Staff staff, List<Long> warehouseIds, Long sysItemId, Long sysSkuId, Integer purchaseOnWayQuantitiesOrder, Page page);

    /**
     * @description: 查询库存状态-计划采购数弹窗数据
     * @author: HZY
     * @date: 2023/12/26 16:20
     */
    PageList<PurchaseOrderDetailOnWay> queryPurchaseNumInfo(Staff staff, PurchaseOrderQueryParams params);

    /**
     * 根据采购单ids查询采购单
     *
     * @param staff
     * @param idList
     * @return
     */
    List<PurchaseOrder> queryByIds(Staff staff, List<Long> idList, boolean isDetail);

    /**
     * 新增一条采购订单数据
     *
     * @param staff
     * @param po
     * @return
     */
    PurchaseOrder insert(Staff staff, PurchaseOrder po);
    /**
     * 新增历史采购订单数据
     *
     * @param staff
     * @param pos
     * @return
     */
    void batchInsertHistory(Staff staff, List<PurchaseOrder> pos, List<PurchaseOrderDetail> poDeatils, Set<Long> receiptIds, Map<Long, Set<Label>> labels);
    /**
     * 新增一条采购
     *
     * @param staff
     * @param po
     * @return
     */
    PurchaseOrder insertV2(Staff staff, PurchaseOrder po);

    /**
     * 批量新增采购单数据
     *
     * @param staff
     * @param orders
     * @return
     */
    List<PurchaseOrder> batchInsert(Staff staff, List<PurchaseOrder> orders);


    /**
     * 真正走批量新增采购单逻辑
     * 暂时只有以销定采调用，未处理套件相关逻辑
     * @param staff
     * @param orders
     * @return
     */
    List<PurchaseOrder> realBatchInsert(Staff staff, List<PurchaseOrder> orders);

    /**
     * 更新采购订单
     *
     * @param staff
     * @param po
     */
    PurchaseOrder update(Staff staff, PurchaseOrder po);

    /**
     * 更新一些特殊字段，不校验状态
     *
     * @param staff
     * @param po
     */
    PurchaseOrder updateIgnoreStatus(Staff staff, PurchaseOrder po);

    /**
     * 修改预付款
     *
     * @param staff
     * @param po
     * @return
     */
    PurchaseOrder updateAdvanceCharge(Staff staff, PurchaseOrder po);

    /**
     * 修改制单时间
     *
     * @param staff
     * @param po
     * @return
     */
    PurchaseOrder updateCreateTime(Staff staff, PurchaseOrder po);

    /**
     * 更新收货单凭证
     *
     * @param staff
     * @param po
     * @return
     */
    void updateProofUrl(Staff staff, PurchaseOrder po);

    /**
     * 更新采购订单
     *
     * @param staff
     * @param po
     */
    PurchaseOrder updateV2(Staff staff, PurchaseOrder po);

    /**
     * 更新备注旗帜
     *
     * @param staff
     * @param ids    采购单id
     * @param remark 备注
     * @param flag   旗帜
     * @param cover  是否覆盖 0：不覆盖 1：覆盖
     * @param associate  是否同步修改收货单
     * @return
     */
    Map<PurchaseOrder, PurchaseOrder> updateRemark(Staff staff, List<Long> ids, String remark, Integer flag, Integer cover,Integer associate);

    /**
     * 同步修改收货单备注
     */
    Boolean associatePurchaseReceipt(Map<Long, String> purchaseMap, Staff staff, Integer flag);

    /**
     * 修改采购单金额
     *
     * @param staff
     * @param id
     * @return
     */
    PurchaseOrder updatePurchaseOrderAmount(Staff staff, Long id);

    /**
     * 修改备注
     *
     * @param staff
     * @param purchaseOrder
     */
    PurchaseOrder updateRemark(Staff staff, PurchaseOrder purchaseOrder);

    /**
     * 修改明细备注
     *
     * @param staff
     * @param details
     */
    void updateDetailRemark(Staff staff, List<PurchaseOrderDetail> details);

    /**
     * 关闭采购订单
     *
     * @param staff
     * @param po
     */
    void close(Staff staff, Long po);

    /**
     * @Description 采购单关闭
     * @param staff
     * @param po
     * @param closePurchaseForce 是否强制关闭采购单，不调用关闭三方仓单据
     * @param purchaseOrderCloseResult 是否需要确认，返回前端三方仓单据关闭报错时候使用，确认是否强制关闭
     */
    void close(Staff staff, Long po, boolean closePurchaseForce, PurchaseOrderCloseResult purchaseOrderCloseResult);

    /**
     * 批量关闭
     *
     * @param staff
     * @param ids
     * @return
     */
    BatchResult<SuccessItem> batchClose(Staff staff, List<Long> ids, boolean force);

    BatchResult<SuccessItem> batchClose(Staff staff, List<Long> ids, boolean force,BatchResult<SuccessItem> result, boolean closePurchaseForce);

    /**
     * 取消订单，无需检查和错误
     */
    void cancelPoWithOutCheckAndError(Staff staff, Long id);

    /**
     * 批量打回草稿
     *
     * @param staff
     * @param ids   purchaseOrder'id
     * @return
     */
    BatchResult<SuccessItem> batchUnAudit(Staff staff, List<Long> ids);

    /**
     * 根据采购订单系统单号，查询此采购订单的所有详情列表
     *
     * @param staff
     * @param oid
     * @return
     */
    List<PurchaseOrderDetail> queryDetailsByOid(Staff staff, Long oid);

    /**
     * 进行提审操作
     *
     * @param staff
     * @param param
     */
    void verify(Staff staff, PurchaseVerifyParam param);

    /**
     * 批量提审采购单前查看
     *
     * @param staff
     * @param longList
     * @return
     * @Title: batchVerifyView
     */
    PageList<PurchaseOrder> batchVerifyView(Staff staff, List<Long> longList);

    /**
     * 批量复审采购单
     *
     * @param staff
     * @param ids:采购单IDs
     * @param isPass:是否通过审核
     * @param auditorRemark:审核备注
     */
    void batchReviewPurchase(Staff staff, List<Long> ids, boolean isPass, String auditorRemark, Integer type,boolean createWarehouseEntry, Integer thirdPickWay);

    /**
     * 批量提审
     *
     * @param staff
     * @param ids
     * @return
     */
    BatchResult<SuccessItem> batchVerify(Staff staff, List<Long> ids);

    /**
     * 审核订单，通过与否由pass字段判断，order参数必须要传入id参数
     *
     * @param staff
     * @param purchaseId
     * @param isPass:是否通过审核
     * @param auditorRemark：审核备注
     */
    void auditPurchase(Staff staff, Long purchaseId, boolean isPass, String auditorRemark, Integer type, boolean isFastAudit, Integer thirdPickWay);

    /**
     * 批量审核采购单
     *
     * @param staff
     * @param ids:采购单IDs
     * @param isPass:是否通过审核
     * @param auditorRemark:审核备注
     */
    void batchAuditPurchase(Staff staff, List<Long> ids, boolean isPass, String auditorRemark, Integer type, boolean isFastAudit, Integer thirdPickWay);

    /**
     * 批量审核采购单
     *
     * @param staff
     * @param ids:采购单IDs
     * @param isPass:是否通过审核
     * @param auditorRemark:审核备注
     */
    void batchAuditPurchase(Staff staff, List<Long> ids, boolean isPass, String auditorRemark, Integer type, boolean isFastAudit,boolean createWarehouseEntry, Integer thirdPickWay,boolean isReview);

    /**
     * 财审订单，通过与否由pass字段判断，order参数必须要传入id参数
     *
     * @param staff
     * @param order
     * @param pass
     * @param financeAuditorRemark
     */
    void verifyOrderFinance(Staff staff, PurchaseOrder order, boolean pass, String financeAuditorRemark);

    /**
     * 批量财审订单，通过与否由pass字段判断，必须要传入ids参数
     *
     * @param staff
     * @param purchaseVerifyParam
     */
    void batchVerifyFinance(Staff staff, PurchaseVerifyParam purchaseVerifyParam);

    /**
     * 批量清算、批量取消清算
     *
     * @param staff
     * @param param
     */
    void batchClearing(Staff staff, PurchaseClearingParam param);

    /**
     * 根据采购单查询关联未完成的收货单、上架单
     *
     * @param staff
     * @param purchaseOrderId
     * @return
     */
    PurchaseAsso getUnFinishedAssoMap(Staff staff, Long purchaseOrderId);

    /**
     * 部分到货状态提前完成
     *
     * @param staff
     * @param purchaseOrderId
     */
    PurchaseOrder complete(Staff staff, Long purchaseOrderId, boolean updateOnWayQuantity);


    /**
     * 批量完成采购单
     *
     * @param staff
     * @param ids
     * @return
     */
    BatchResult<SuccessItem> batchComplete(Staff staff, List<Long> ids, boolean force);

    /**
     * 批量完成采购单
     * @param staff
     * @param ids
     * @param force
     * @param result
     * @return
     */
    BatchResult<SuccessItem> batchComplete(Staff staff, List<Long> ids, boolean force,BatchResult<SuccessItem> result);

    /**
     * 按商品供应商id拆分采购单
     *
     * @param id
     */
    void splitPurchaseOrder(Staff staff, Long id, Long splitRuleId);

    /**
     * 查询未完成和未关闭的采购单数量
     *
     * @param staff
     * @return
     */
    Long countUnFinishOrClosed(Staff staff);

    /**
     * 修改采购单的剩余数，当sysItemId为null则更新该采购单下所有明细
     *
     * @param staff
     * @param purchaseOrderId 采购单Id
     * @param sysItemId       商品Id
     * @param sysSkuId        skuId
     */
    void updateRemainQuantity(Staff staff, Long purchaseOrderId, Long sysItemId, Long sysSkuId);

    /**
     * 检查是否可以超收
     *
     * @param staff
     */
    void checkOverReceive(Staff staff, WarehouseEntry entry, boolean isUpdate, boolean changePurchasePrice);

    /**
     * 获取采购单的套件明细
     *
     * @param staff
     * @param poIds
     * @return
     */
    List<PurchaseOrderDetail> getSuitDetails(Staff staff, List<Long> poIds);

    /**
     * 套件,加工 转单品
     *
     * @param order
     * @return
     */
    PurchaseOrder suiteToSingle(Staff staff, PurchaseOrder order);

    /**
     *  采购单全选套件转单品
     * @param purchaseOrderId 采购单id
     * @param processToSingle 加工商品按单品采购
     * @param groupToSingle 组合商品按照单品采购
     * @return java.lang.Object
     */
    PurchaseOrder allSuiteToSingle(Staff staff, Long purchaseOrderId, boolean processToSingle, boolean groupToSingle);

    /**
     * @Description 转单品并更新详情
     * @param staff
     * @param param
     * @return com.raycloud.dmj.domain.caigou.PurchaseOrder
     */
    PurchaseOrder suiteToSingleAndUpdate(Staff staff, PurchaseOrderSuiteToSingleParam param);

    /**
     * 查询未完成的单据
     *
     * @param staff
     * @return
     */
    int[] queryNotFinishReceipts(Staff staff);

    /**
     * 筛选采购单详情接口（old）
     * 参数字段维护性差 后续慢慢废弃
     *
     */
    @Deprecated
    PurchaseOrder queryByIdAndOuterId(Staff staff, Long id, String outerId, Integer lessAndMore, Integer priceType, QueryParams params, Page page, String remark);

    /**
     * 筛选采购单详情接口（new）
     * @param staff
     * @param purchaseDetailQueryParams
     * @return
     */
    PurchaseOrder queryOrderByParams(Staff staff, PurchaseDetailQueryParams purchaseDetailQueryParams, QueryParams params);

    /**
     *  获取唯一码采购单唯一码明细
     * @param staff 员工信息
     * @param id    采购单ID
     * @param outerId   商家编码
     * @param page  分页信息
     * @return  唯一码采购单明细
     */
    List<PurchaseUniqueCode> getPurchaseUniqueCodeDetail(Staff staff, Long id, String outerId, Page page);

    /**
     *  获取唯一码采购单唯一码明细总数
     * @param staff 员工信息
     * @param id    采购单ID
     * @param outerId   商家编码
     * @return  唯一码采购单明细
     */
    Long countPurchaseUniqueCodeDetail(Staff staff, Long id, String outerId);

    /**
     * 删除唯一码明细
     * @param staff 员工信息
     * @param id 采购单ID
     * @param uniqueCodeDetailIds 唯一码明细ID
     * @return
     */
    int deletePurchaseUniqueCodeDetail(Staff staff, Long id, List<Long> uniqueCodeDetailIds);

    /**
     * 更新采购单供应商排序
     *
     * @param staff
     * @param suppliers
     */
    void updateSupplierSort(Staff staff, List<Supplier> suppliers);

    PurchaseOrder addOrUpdateDetail(Staff staff, List<PurchaseOrderDetail> detail, Boolean checkStatus);

    void coverQuantityByShipNum(Staff staff, List<Long> poIds);

    PurchaseOrder deleteDetail(Staff staff, List<Long> idLists);

    /**
     * 1688一键下单删除缺货商品可以删除多个单据详情，因此需要分组删除
     * @param staff
     * @param idList
     * @return java.util.List<com.raycloud.dmj.domain.caigou.PurchaseOrder>
     */
    List<PurchaseOrder> deleteDetailGroupOrderId(Staff staff, List<Long> idList);

    /**
     * 查询采购单明细到货时间和在途数
     */
    List<OnWayInfo> queryPoDetailOnWayInfo(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds, String categoryNameStr);

    /**
     * 查询采购单明细到货时间和在途数(支持多仓库查询)
     */
    List<OnWayInfo> queryPoDetailOnWayInfo(Staff staff, List<Long> warehouseIds, List<Long> sysItemIds, List<Long> sysSkuIds, String categoryNameStr);
    /**
     *
     * 采购合单
     *
     * @param staff
     * @param params
     */
    PurchaseMergeInfo mergeInfo(Staff staff, PurchaseOrderQueryParams params);

    /**
     * 采购合单
     *
     * @param staff
     * @param
     */
    PurchaseOrder merge(Staff staff, List<PurchaseOrder> list, Long warehouseId, String warehouseName, Date deliveryDate, String remark, boolean autoRemark);

    /**
     * 采购单导出 - 报表使用
     *
     * @param staff
     * @param params
     * @return
     */
    Map<String, Object> export(Staff staff, PurchaseDataExportParams params);


    List<PurchaseOrderDetail> queryDetailsByIds(Staff staff, List<Long> ids);

    /**
     * 更新出库快递单号等数据
     *
     * @param staff
     * @param orderId
     * @param purchaseOrderExpresses
     * @return
     * @Title: updateOutTemplate
     */
    PurchaseOrder updateOutTemplate(Staff staff, Long orderId, List<PurchaseOrderExpress> purchaseOrderExpresses);


    List<PurchaseOrderDetail> queryEarliestNotFinishDetailsByItems(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds);

    Map<String, ItemLastReceivePrice> queryLastPrice(Staff staff, ItemLastReceivePriceQueryParam param);

    void fillLastPrice(Staff staff, List<Long> ids);

    /**
     * 置空到货日期
     *
     * @param staff
     * @param id
     */
    void updateDeliveryDate(Staff staff, Long id);

    /**
     * 采购单复制
     *
     * @param staff
     * @param
     */
    void copy(Staff staff, List<Long> purchaseOrderIds);

    void printRecord(Staff staff, List<Long> ids);

    List<Long> updatePurchaseOrder(Staff staff, Date start, Date end, Map<String, Double> priceMap, Map<String, Long> supplierMap, Set<Long> itemIdSet, Set<Long> skuIdSet, Page page);


    List<PurchaseOrderDetail> queryDetailsByPo(Staff staff, PurchaseOrder po, String outerId, Integer lessAndMore, Page page);

    void batchAuditPurchase(Staff staff, List<Long> ids, boolean isPass, String auditorRemark, Integer pickWay, boolean isFastAudit, boolean createWarehouseEntry);

    /**
     * 推送到三方仓并返回推送失败的采购单
     * @param staff
     * @param orders
     * @return
     */
    List<Long> filterAfterThirdHandled(Staff staff, List<PurchaseOrder> orders);

    /**
     * 推送到三方仓并返回推送失败的采购单
     * @param staff
     * @param orders
     * @param throwError
     * @return
     */
    List<Long> filterAfterThirdHandled(Staff staff, List<PurchaseOrder> orders,Boolean throwError);

    /**
     * 获取带有外部单号的采购单个数
     * @param staff
     * @param sourceId 外部单号
     * @param sourceKey 外部appKey
     * @return
     */
    Integer getCountBySourceId(Staff staff, String sourceId, Integer sourceKey);

    PurchaseOrder getBySourceId(Staff staff, String sourceId, Integer appKey);

    /**
     * 修改明细交货日期和截止交货日期
     *
     * @param staff 账号信息
     * @param request 请求参数
     */
    void updateDetailDeliveryDate(Staff staff, UpdateDeliveryDateRequest request);

    /**
     * 明细置空到货日期
     *
     * @param staff
     * @param id
     */
    void updateDetailDeliveryDate(Staff staff, Long id);

    /**
     * 查询关联单据
     */
    List<PurchaseBillVo> billList4SupplierId(Staff staff, Long poId);

    /**
     * 修改供应商
     */
    void updateSupplier(Staff staff, PurchaseOrder order);
    /**
     * 批量刷新采购单价
     * @param staff
     * @param ids
     * @return
     */
    BatchResult<SuccessItem> batchUpdatePrice(Staff staff, List<Long> ids);

    /**
     * 采购单
     */
    PurchaseOrders listPurchaseOrders(Staff staff, PurchaseOrderQueryParams purchaseOrderQueryParams, Page page);

    /**
     * 查询采购单code（外链使用）
     */
    List<String> listCodePurchaseOrders4Share(Staff staff, Long supplierId, Page page);

    /**
     * 采购单详情
     */
    PurchaseOrder listPurchaseOrderDetail(Staff staff, Long id, String outerId , Page page);
    /**
     * 发货情况
     */
    PurchaseOrder listPurchaseOrderShipInfo(Staff staff, PurchaseOrderQueryParams params, Page page);

    List<PurchaseOrderDetail> fillAndFilterOrderDetailDeliveryStatus(PurchaseOrder purchaseOrder, List<PurchaseOrderDetail> purchaseOrderDetailList, QueryParams params);

    /**
     * 更新采购单
     * @param staff
     * @param po
     */
    void updatePurchaseOrder(Staff staff, PurchaseOrder po);

    List<PurchaseOrderDetail> queryDetailsByPos(Staff staff, List<PurchaseOrder> pos, Boolean showSuitsParam, Page page);

    /**
     * 生成仓内加工单
     * @param o
     * @param ids
     */
    Map<String, String> generateStockProductOrder(Staff staff, List<Long> ids);

    void calculatePriceAndAmount(Staff staff, PurchaseOrder po, boolean dataPrivilege);

    /**
     * 创建1688订单
     *
     * @param staff 配置信息
     * @param request 请求参数
     * @return 结果
     */
    BatchResult createAliOrder(Staff staff,CreateOrderRequest request);

    /**
     * 创建1688一件采购预检查
     * @param staff
     * @param params 采购单ID列表
     * @return
     */
    JSONObject preCheckOrder (Staff staff, CreateOrderRequest request);

    ThirdPurchaseOrderDetails getThirdPurchaseOrderDetails(Staff staff, List<Long> orderIds, Page page);


    /**
     * 查询三方采购单列表
     *
     * @param staff 账号信息
     * @param requestVO 采购单信息
     * @return 采购单列表
     */
    List<ThirdPurchaseOrder> queryThirdPurchaseOrderList(Staff staff, ThirdPurchaseQueryRequestVO requestVO);

    JSONObject getPurchaseProductInfos(Staff staff, PurchaseProductQueryParam params, Page page);

    String setDefaultProduct(Staff staff, PurchaseProductQueryParam params);

    /**
     * 创建1688一件采购订单级别检查
     * @param staff
     * @param orderIds 采购单ID列表
     * @param addressType 收货地址类型 0：1688收货地址 1：采购单仓库收货地址
     * @param receiveAddressId 1688收货地址主键id 选择1688收货地址时传值
     * @param flag 是否有勾选回填价格、回填运费、自动拆分采购单
     * @return
     */
    JSONObject checkOrder (Staff staff, List<Long> orderIds, Integer addressType, Long receiveAddressId, boolean flag);

    /**
     * 1688 一件采购 检查 每个商品是否满足下单情况
     * @param staff
     * @param request
     * @return
     */
    JSONObject afterCheckOrder(Staff staff, CreateOrderRequest request);

    /**
     * 查询采购单常量供应商
     * @param staff
     * @param config
     * @return
     */
    List<PurchaseOrder> queryCommonlyUsedSupplierByOrderCreated(Staff staff,PurchaseConfig config);

    /**
     * 批量修改采购单详情的发货数
     * @param staff
     * @param purchaseOrderDetails
     */
    void batchUpdateDetailShip(Staff staff, List<PurchaseOrderDetail> purchaseOrderDetails);

    /**
     * 查询商品在采购单数量
     */
    Map<String, Long> queryPurchaseOrderItemNum(Staff staff, PurchaseOrderQueryParams params);

    /**
     * 批量添加优惠折扣率
     * @param staff
     * @param toLongList
     * @param discountRate
     * @return
     */
    Map<PurchaseOrder, PurchaseOrder> batchAddDiscountRate(Staff staff, List<Long> toLongList, Double discountRate);

    /**
     * 修改优惠折扣率
     * @param staff
     * @param purchaseOrder
     * @return
     */
    PurchaseOrder updateDiscountRate(Staff staff, PurchaseOrder purchaseOrder);


    /**
     * 修改价格
     * @param staff
     * @param detailId
     * @param code
     * @param purchasePrice
     * @param rebatePrice
     */
    void updatePurchaseOrderAndItemPrice(Staff staff, Long detailId, String code, Double purchasePrice, BigDecimal rebatePrice, Boolean checkBox, Boolean syncItem, List<String> errors);

    /**
     * 按商品sku ids结单
     * @param staff
     * @param ids
     * @param result
     * @return
     */
    void statementDetail(Staff staff, List<Long> ids,BatchResult<SuccessItem> result);

    /**
     * 填充准时交货总数、按时交货总数
     */
    void fillDeliveryInfo(Staff staff, List<PurchaseOrder> list, Integer type, List<PurchaseOrderDetail> purchaseOrderDetailList);

    /**
     * 计算并填充超收总数
     */
    void fillOverQuantity(Staff staff, List<PurchaseOrder> list, PurchaseOrders purchaseOrders, Integer type, List<PurchaseOrderDetail> purchaseOrderDetailList);

    /**
     * 封装采购金额、采退收货金额、实收金额
     * @param purchaseOrder
     * @param staff
     * @return
     */
    void addDetailAmount(Staff staff, List<PurchaseOrder> purchaseOrderList);

    /**
     * 采购单，自动、手动推送外链链接到微信公众号
     * @param staff
     * @param ids
     * @param type  0:自动  1:手动
     */
    void pushPurchaseOrder2Wx(Staff staff, List<Long> ids, Integer type);

    /**
     * 采购创建预付款单据
     */
    void batchSavePurchasePrePaymentOrder(Staff staff, PurchasePrePaymentDTO dto);

    /**
     * 查询存在付款单的采购单
     */
    List<String> checkExistsPrePaymentOrder(Staff staff, List<String> codes);

    /**
     * 查询采购单对应的预付单
     */
    PageList<PurchasePrePaymentDTO> queryPurchasePrePaymentOrder(Staff staff, PurchasePrePaymentParam param);

    /**
     * 财务更新采购单预付款单金额合计
     */
    void updatePurchaseOrderAdvanceCharge(Staff staff, List<PurchaseOrder> purchaseOrders);

    /**
     * 根据参数查询采购单
     * @param staff
     * @param params
     * @return
     */
    PurchaseOrder queryPurchaseOrderByParams(Staff staff,PurchaseOrderQueryParams params);
    /**
     * @description: 根据采退单ids查询采购单列表
     * @author: HZY
     * @date: 2022/5/5 16:26
     */
    List<PurchaseOrder> listByReturnIds(Staff staff, List<Long> returnIds);

    /**
     * @description: 根据采退单ids创建采购单
     * @author: HZY
     * @date: 2022/5/5 16:26
     */
    BatchResult<SuccessItem> generatePurchaseOrder(Staff staff, List<Long> returnIds);

    /**
     * @description: 创建采购单接口
     * 暂时只提供给 订单测调用、APP端调用
     * @author: HZY
     * @date: 2022/9/30 10:32
     */
    List<PurchaseOrder> batchCreatePurchaseOrderByType(Staff staff, List<PurchaseOrder> purchaseOrderList, Integer type);


    /**
     * @description: 创建采购单接口
     * 暂时只提供给 订单测调用、APP端调用
     * @author: HZY
     * @date: 2022/9/30 10:32
     */
    List<PurchaseOrder> batchCreatePurchaseOrderByType(Staff staff, List<PurchaseOrder> purchaseOrderList, Integer type,Integer orderType);


    void setSupplierBridge(List<PurchaseOrderDetail> list);
    /**
     * 查询采购单明细
     *
     * @param staff
     * @param params
     * @return
     */
    PageList<PurchaseOrderDetail> queryPurchaseOrderDetail(Staff staff, PurchaseOrderQueryParams params);

    /**
     * <AUTHOR>
     * @Description 1688一键下单预检
     * @param staff
     * @param request
     * @param purchaseOrderMap 采购单map
     * @param orderList 采购单list
     * @param fillPassInfo BatchResult中是否填充通过单据信息
     * @return com.raycloud.dmj.domain.caigou.result.BatchResult
     */
    BatchResult thirdPurchasePrecheck(Staff staff, CreateOrderRequest request, Map<String, PurchaseOrder> purchaseOrderMap, List<PurchaseOrder> orderList, Boolean fillPassInfo);

    /**
     * <AUTHOR>
     * @Description 查询平台可售数
     * @param staff
     * @param thirdPurchaseOrderList
     * @return void
     */
    void queryThirdPurchaseOrderDetailStock(Staff staff, List<ThirdPurchaseOrder> thirdPurchaseOrderList);
    /**
     * 填充收货相关信息
     * @param staff
     * @param orders
     */
    void fillReceiveInfo(Staff staff, List<PurchaseOrder> orders);

    /**
     *  开放平台修改采购单,需跳过采购单状态校验修改
     */
    PurchaseOrder openApiUpdate(Staff staff, PurchaseOrder po);


    /**
     * 获取采购单明细的交货日期
     *
     * @param staff   账号信息
     * @param request 请求对象
     * @return 交货日期对象
     */
    PurchaseOrderDetailDeliveryDate getPurchaseOrderDetailDeliveryDate(Staff staff, PurchaseOrderDetailDeliveryDateRequest request);

    /**
     * 批量更新采购明细的到货日期和截止到货日期
     *
     * @param staff   账号信息
     * @param request 请求对象
     */
    void batchUpdateDeliveryDateAndDeadlineDeliveryDate(Staff staff, BatchUpdateDeliveryDateRequest request);

    /**
     * 根据仓库进行混合拆分
     * @param staff
     * @param purchaseOrder
     */
    void splitByWarehouse(Staff staff, PurchaseOrder purchaseOrder);

    /**
     * @description: 批量刷新工费
     * @author: HZY
     * @date: 2023/2/6 16:05
     */
    void batchRefreshLaborCost(Staff staff, String ids);

    /**
     * <AUTHOR>
     * @Description 更新采购单仓库
     * @param staff
     * @param ids 采购单ids
     * @param warehouseId 仓库id
     * @return com.raycloud.dmj.domain.caigou.result.BatchResult<com.raycloud.dmj.domain.caigou.result.SuccessItem>
     */
    BatchResult<SuccessItem> batchUpdateWarehouse(Staff staff, List<Long> ids, Long warehouseId);

    /**
     * 批量更新采购单列表
     *
     * @param staff 配置信息
     * @param purchaseOrderList 采购单列表
     */
    void batchUpdatePurchaseOrder(Staff staff,List<PurchaseOrder> purchaseOrderList);

    /**
     * 查询款维度明细
     */
    PageList<PurchaseOrderDetail> queryProductDetailsByIdAndOuterId(Staff staff, Long orderId, String itemOuterId, Page page);


    /**
     * @description: 更新发货数
     * @author: HZY
     * @date: 2023/6/8 14:04
     */
    PurchaseOrder updateShipNum(Staff staff, List<PurchaseOrderDetail> purchaseOrderDetailList);



    /**
     * 填充物流信息
     * @param staff 员工信息
     * @param orders 采购单列表
     */
    void repairPurchaseOrderPrice(Staff staff, String code);

    /**
     * @description: 更新采购单外部单号
     * @author: HZY
     * @date: 2023/8/14 13:49
     */
    void updateSourceOrderId(Staff staff, PurchaseOrder purchaseOrder);

    /**
     * 批量修改1688订单类型（更新单据供应商1688合作模式）
     * @param staff
     * @param poIds 采购单id
     * @param platformCooperateType 1688合作模式
     * @return java.util.List<com.raycloud.dmj.web.model.caigou.ThirdPurchaseOrder>
     */
    List<ThirdPurchaseOrder> batchUpdateThirdPurchaseOrderType(Staff staff, List<Long> poIds, Integer platformCooperateType);

    /**
     * <AUTHOR>
     * @Description 填充起批数校验处理
     */
    List<PurchaseOrderDetail> checkUpdateQuantityByMinOrderQuantity(Staff staff, List<PurchaseOrderDetail> details);


    /**
     * 刷新采购单明细供应商信息
     *
     * @param staff    员工ID
     * @param orderIds 采购单ID列表
     */
    void refreshDetailSupplier(Staff staff, List<Long> orderIds,List<PurchaseOrderDetail> orderDetails,Integer source);

    /**
     * 获取供应商平台loginId供阿里旺旺跳转用
     * @date 2023/11/16
     * @param staff
     * @param supplierId 供应商id
     * @return java.lang.String
     */
    String queryPlatformSupplierLoginId(Staff staff,Long supplierId);

    /**
     * @Description 1688回填采购价及运费
     * @param staff
     * @param purchaseOrders 采购单
     * @param purchaseUserId 1688账号id
     * @param fillPlatformPrice 同步1688商品单价
     * @param fillPlatformFreight 1688订单运费
     * @return void
     */
    void fillPlatformPurchasePriceAndFreight(Staff staff, List<PurchaseOrder> purchaseOrders, Long purchaseUserId, Boolean fillPlatformPrice, Boolean fillPlatformFreight);

    /**
     *  更新采购单1688订单状态
     */
    void updatePlatformStatus(Staff staff, List<PurchaseOrder> purchaseOrderList);

    /**
     * 填充物流信息
     * @param staff 员工信息
     * @param orders 采购单列表
     */
    void fillOrderExpress(Staff staff, List<PurchaseOrder> orders);


    /**
     * 根据ID查询采购单列表，带排序
     */
    List<PurchaseOrder> queryByIdsWithSort(Staff staff, List<Long> ids, Sort sort);

    /**
     * 推送采购单给供应商（通过奇门对接）
     * @param staff
     * @param orderIds
     * @return
     */
    BatchResult<SuccessItem> pushOrderToSupplier(Staff staff, List<Long> orderIds);

    /**
     * 奇门发货回调（pushOrderToSupplier 推  这个接口回调）
     * @param staff
     * @param purchaseOrder
     */
    void updateShippingInfo(Staff staff, PurchaseOrder purchaseOrder);



    /**
     * 填充同款汇总数
     * @param detailList
     */
    void fillItemQuantity(List<PurchaseOrderDetail> detailList);

    /**
     *  查询物流信息
     */
    Object purchaseSearchLogisticsTrace(Staff staff, PurchaseOrderExpressRequest purchaseOrderExpress);

    /**
     * <AUTHOR>
     * @Description 外链待发货商品汇总
     */
    PageList<ShareQueryPurchaseDetailSummaryVo> shareQueryPurchaseDetailSummary(Staff staff, PurchaseOrderQueryParams purchaseOrderQueryParams, Page page);

    /**
     * 修改交货日期
     * @param staff
     * @param purchaseOrderId 采购单ID
     * @param deliveryDate 交货日期
     * @param syncDetail 是否同步修改明细的交货日期
     * @param source 1:从采购单列表修改 2:导入更新采购单修改
     */
    void updateDeliveryDate(Staff staff,Long purchaseOrderId,Date deliveryDate,Boolean syncDetail,Integer source);
    /**
     * 采购单商品唯一码
     */
    Long itemUniqueCodeCount(Staff staff, String codes);


    /**
     * 根据仅缺货订单创建采购单
     * @param staff
     * @param purchaseOrderList
     * @return
     */
    List<AssoPurchaseOrder> batchCreatePurchaseOrderByOrderLack(Staff staff, List<PurchaseOrder> purchaseOrderList);


    /**
     * 订单反审
     * @param staff
     * @param sids
     */
    void tradeUnauditSuccess(Staff staff,List<Long> sids);

    /**
     * @param staff
     * @param orderIds 采购单号
     * @return java.lang.String
     * <AUTHOR>
     * @Description 获取1688支付链接
     */
    List<ThirdPurchaseOrderPayUrlVO> getPlatformOrderPayUrl(Staff staff, String orderIds);

    /**
     * 通过合并信息获取列表
     */
    List<PurchaseOrder> getListByMergeInfo(Staff staff, PurchaseMergeInfo purchaseMergeInfo);

    /**
     * 填充收货单1688订单信息
     * @param staff
     * @param
     */
    void fillWarehouseEntryPlatformOrderInfo(Staff staff, List<WarehouseEntryVo> entryVoList);

    /**
     * 根据查询条件查询采购单商品商家编码
     * @param staff
     * @param purchaseOrderQueryParams
     * @return
     */
    List<PurchaseOrderDetail> listPurchaseOrderDetailByParams(Staff staff, PurchaseOrderQueryParams purchaseOrderQueryParams);

    /**
     * 采购快速收货 商品搜索后-填充商品价格相关信息
     * @param staff 登录信息
     * @param purchaseOrderId 如果是在收货单中添加商品时，可能会有采购单ID
     * @param warehouseId 仓库ID
     * @param isMatchPoReceive 是否匹配采购收货记录，true表示查询时需考虑与采购收货的匹配情况
     * @param config 采购配置
     * @param source  1:采购类型、2:采退类型、3:收货类型、4:其他出入类型
     * @param pageList 商品信息
     * @param receiveType 1:只匹配采购单、2:只匹配采退单、3:同时匹配采购、采退单
     * @return
     */
    PageList<ItemModelFull> fillPoReceiveDataBroad(Staff staff, Long purchaseOrderId, Long warehouseId, Boolean isMatchPoReceive, PurchaseConfig config, Integer source, PageList<ItemModelFull> pageList, Integer receiveType,Long supplierId);



    /**
     * 校验是否需要预警
     * @param purchaseConfig
     * @return
     */
    boolean checkNeedEarlyWarning(PurchaseConfig purchaseConfig);

    /**
     * 校验采购单价是否满足条件
     * @param purchasePrice
     * @param dmjItem
     * @param purchaseConfig
     * @return
     */
    boolean purchasePriceCheck(Double purchasePrice, DmjItem dmjItem, PurchaseConfig purchaseConfig);

    /**
     *  微信查询采购单信息
     */
    List<PurchaseOrder> wxQueryOrder(Staff staff, Long supplierId);

    /**
     * 更新明细供应商
     * @param staff
     * @param purchaseOrder
     */
    void updateDetailSupplier(Staff staff,PurchaseOrder purchaseOrder);
}
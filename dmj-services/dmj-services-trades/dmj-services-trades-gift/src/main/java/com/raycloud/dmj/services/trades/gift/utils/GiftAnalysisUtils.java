package com.raycloud.dmj.services.trades.gift.utils;

import com.raycloud.dmj.gift.domain.GiftRuleExtend;
import com.raycloud.dmj.gift.domain.GiftRuleItem;
import com.raycloud.dmj.gift.domain.GiftRule;
import com.raycloud.dmj.gift.domain.GiftV2Item;
import com.raycloud.dmj.gift.enums.GiveTypeEnum;
import com.raycloud.dmj.gift.enums.PropertyNameEnum;
import com.raycloud.dmj.gift.enums.RuleItemTypeEnum;
import com.raycloud.dmj.gift.enums.RuleItemMatchTypeEnum;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.NumberUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 赠品公式解析类
 */
public class GiftAnalysisUtils {

    private static final int DEFAULT_GROUP_PREFIX = 10000;

    /**
     * 解析原始商品
     *
     * @param formulas 公式
     */
    public static List<GiftRuleItem> analysisOriginItem(Long companyId, Long ruleId, Long version, Integer itemType, Integer matchType, String formulas) {
        if (!StringUtils.hasText(formulas)) {
            return null;
        }
        List<GiftRuleItem> giftItems = new ArrayList<>();
        AtomicInteger group = new AtomicInteger(0);
        Arrays.stream(formulas.split("[,，]")).map(String::trim).filter(StringUtils::hasText).distinct().forEach(formula -> {
            List<GiftRuleItem> giftItemsList = analysisOriginItem(companyId, ruleId, version, itemType, matchType, formula, group.addAndGet(1));
            if (CollectionUtils.isEmpty(giftItemsList)) {
                return;
            }
            giftItems.addAll(giftItemsList);
        });

        return giftItems;
    }

    /**
     * 还原原商品公式
     */
    public static String restoreOriginItemFormula(List<GiftRuleItem> giftItems) {
        if (CollectionUtils.isEmpty(giftItems)) {
            return "";
        }
        StringBuilder formula = new StringBuilder();
        giftItems.stream().collect(Collectors.groupingBy(GiftRuleItem::getGroup)).forEach((group, items) -> {

            if (items.size() == 1) {
                GiftRuleItem giftRuleItem = items.get(0);
                if (giftRuleItem.getNum() == 1 && giftRuleItem.getNumMax() ==0) {
                    formula.append(giftRuleItem.getKeyword()).append(",");
                    return;
                }
            }

            formula.append("@");
            int index = 0;
            for (GiftRuleItem item : items) {
                formula.append(item.getKeyword());
                // 或关系的时候，只有在最后一个商品的时候拼接数量,
                if (item.getRelation().equals("OR") && index != items.size() - 1 && "OR".equals(items.get(index + 1).getRelation())) {
                    formula.append("|");
                    index++;
                    continue;
                }
                if (item.getNum() == 0 && item.getNumMax() > 0) {
                    formula.append("<").append(item.getNumMax());
                }
                if (Objects.equals(item.getNumMax(), item.getNum())) {
                    formula.append("=").append(item.getNum());
                }
                if (item.getNum() > 0 && item.getNumMax() == Integer.MAX_VALUE) {
                    formula.append(">").append(item.getNum());
                }
                if (++index < items.size()) {
                    formula.append("+");
                }
            }
            formula.append(",");
        });
        // 去除最后一个，
        String returnStr = formula.toString();
        if (returnStr.endsWith("，") || returnStr.endsWith(",")) {
            returnStr = returnStr.substring(0, returnStr.length() - 1);
        }
        return returnStr;
    }

    /**
     * 解析买A送B公式
     * 例子：
     *      组1：A+B*3=C*5,D=E*2
     *      组2：D+Q*3=P*5,C=W*2
     *      数据库中保存个商品组号为： 原组号（前端传入的group） * DEFAULT_GROUP_PREFIX + 内部序号（interiorGroup）
     *      返回页面组号解析 数据库内组号（group） / DEFAULT_GROUP_PREFIX
     */
    public static List<GiftRuleItem> analysisGiveItem(Long companyId, Long ruleId, Long version, GiftV2Item giftV2Item) {
        if (!StringUtils.hasText(giftV2Item.getFormula())) {
            return null;
        }
        AtomicInteger interiorGroup = new AtomicInteger(0);
        List<GiftRuleItem> giftItems = new ArrayList<>();
        Arrays.stream(giftV2Item.getFormula().split("[,，]")).map(String::trim).filter(StringUtils::hasText).forEach(formula -> {
            List<GiftRuleItem> giftItemsList = analysisGiveItem(companyId, ruleId, version, formula, giftV2Item.getGroup() * DEFAULT_GROUP_PREFIX + interiorGroup.addAndGet(1));
            if (CollectionUtils.isEmpty(giftItemsList)) {
                return;
            }
            giftItems.addAll(giftItemsList);
        });
        return giftItems;
    }

    /**
     * 还原买A送B公式
     */
    public static List<GiftV2Item> restoreGiveItemFormula(List<GiftRuleItem> giftItems) {
        if (CollectionUtils.isEmpty(giftItems)) {
            return null;
        }

        List<GiftV2Item> giftV2Items = new ArrayList<>();
        Map<Integer, List<GiftRuleItem>> giftRuleItemMap = giftItems.stream().collect(Collectors.groupingBy(item -> item.getGroup() / DEFAULT_GROUP_PREFIX));
        giftRuleItemMap.forEach((group, giftRuleItems) -> {
            StringBuilder formula = new StringBuilder();
            giftRuleItems.stream().collect(Collectors.groupingBy(GiftRuleItem::getGroup)).values().forEach(items -> formula.append(restoreFormula(items)).append(","));
            GiftV2Item giftV2Item = new GiftV2Item();
            giftV2Item.setFormula(formula.toString());
            giftV2Item.setGroup(group);
            giftV2Items.add(giftV2Item);
        });
        return giftV2Items;
    }

    /**
     * 赠品前端入参转换为后端存库对象
     *
     * @param giftV2Items 入参赠品商品信息
     */
    public static List<GiftRuleItem> analysisGiveItem(Long companyId, Long ruleId, Long version, List<GiftV2Item> giftV2Items) {
        if (CollectionUtils.isEmpty(giftV2Items)) {
            return null;
        }

        return giftV2Items.stream().map(giftV2Item -> {
            GiftRuleItem giftItem = new GiftRuleItem();
            giftItem.setRuleId(ruleId);
            giftItem.setItemType(RuleItemTypeEnum.RULE_ITEM_GROUP_GIFT.getType());
            giftItem.setGroup(giftV2Item.getGroup());
            if (Objects.isNull(giftV2Item.getGroup()) || giftV2Item.getGroup() <= 0) {
                giftItem.setGroup(1);
            }
            giftItem.setGroup(giftV2Item.getGroup());
            giftItem.setCompanyId(companyId);
            giftItem.setMatchType(RuleItemMatchTypeEnum.SKU.getType());
            giftItem.setKeyword(giftV2Item.getSysOuterId());
            if (!StringUtils.hasText(giftV2Item.getSysOuterId())) {
                giftItem.setKeyword("0");
            }
            giftItem.setNum(giftV2Item.getNum());
            if (Objects.isNull(giftV2Item.getNum()) || giftV2Item.getNum() <= 0) {
                giftItem.setNum(1);
            }
            giftItem.setNumMax(giftV2Item.getUpperLimit());
            if (Objects.isNull(giftV2Item.getUpperLimit()) || giftV2Item.getUpperLimit() <= 0) {
                giftItem.setNumMax(0);
            }

            giftItem.setVersion(version);
            return giftItem;
        }).collect(Collectors.toList());
    }

    /**
     * 赠品后端存库对象转为前端对象
     *
     * @param giftRuleItems 后端存库对象
     */
    public static List<GiftV2Item> restoreGiveItem(List<GiftRuleItem> giftRuleItems) {
        if (CollectionUtils.isEmpty(giftRuleItems)) {
            return null;
        }
        return giftRuleItems.stream().map(ruleItem -> {
            GiftV2Item giftV2Item = new GiftV2Item();
            giftV2Item.setId(ruleItem.getId());
            giftV2Item.setGroup(ruleItem.getGroup());
            giftV2Item.setNum(ruleItem.getNum());
            giftV2Item.setUpperLimit(ruleItem.getNumMax());
            giftV2Item.setSysOuterId(ruleItem.getKeyword());
            return giftV2Item;
        }).collect(Collectors.toList());
    }

    /**
     * 解析字符串参数
     */

    public static List<GiftRuleExtend> analysisGiveExtend(Long companyId, Long ruleId, Long version, GiftRule giftRule) {
        if (Objects.isNull(giftRule)) {
            return null;
        }
        List<GiftRuleExtend> giftExtendAll = new ArrayList<>();
        Field[] fields = GiftRule.class.getDeclaredFields();
        for (Field field : fields) {
            String s = Modifier.toString(field.getModifiers());
            // 排除final修饰的属性
            if (s.contains("final")) {
                continue;
            }

            PropertyNameEnum propertyNameEnum = PropertyNameEnum.typeOf(field.getName());
            if (Objects.isNull(propertyNameEnum)) {
                continue;
            }

            try {
                field.setAccessible(true);
                Object value = field.get(giftRule);
                if (Objects.isNull(value)) {
                    continue;
                }
                List<GiftRuleExtend> giftExtends = analysisGiveExtend(companyId, ruleId, version, propertyNameEnum.getType(), value.toString());
                if (!CollectionUtils.isEmpty(giftExtends)) {
                    giftExtendAll.addAll(giftExtends);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }

        return giftExtendAll;
    }

    /**
     * 还原字符串
     */
    public static void restoreGiveExtend(List<GiftRuleExtend> giftExtends, GiftRule target) {
        if (CollectionUtils.isEmpty(giftExtends)) {
            return;
        }
        if (Objects.isNull(target)) {
            return;
        }
        Class<GiftRule> giftRuleClass = GiftRule.class;
        giftExtends.stream().collect(Collectors.groupingBy(GiftRuleExtend::getKey)).forEach((key, value) -> {

            try {
                Field nameField = giftRuleClass.getDeclaredField(key);
                nameField.setAccessible(true);
                nameField.set(target, value.stream().map(GiftRuleExtend::getValue).collect(Collectors.joining(",")));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

    }

    private static List<GiftRuleExtend> analysisGiveExtend(Long companyId, Long ruleId, Long version, String key, String contents) {
        if (!StringUtils.hasText(contents)) {
            return null;
        }
        return Arrays.stream(contents.split("[,，]")).map(String::trim).distinct().map(content -> {
            GiftRuleExtend giftExtend = new GiftRuleExtend();
            giftExtend.setCompanyId(companyId);
            giftExtend.setRuleId(ruleId);
            giftExtend.setKey(key);
            giftExtend.setValue(content);
            giftExtend.setVersion(version);
            return giftExtend;
        }).collect(Collectors.toList());
    }

    private static List<GiftRuleItem> analysisOriginItem(Long companyId, Long ruleId, Long version, Integer itemType, Integer matchType, String formula, Integer group) {
        if (!StringUtils.hasText(formula)) {
            return null;
        }

        List<GiftRuleItem> giftItems = new ArrayList<>();
        if (formula.indexOf("@") != 0) {
            giftItems.add(analysisOriginItem(formula, companyId, ruleId, version, itemType, matchType, group));
            return giftItems;
        }

        Arrays.stream(formula.split("\\+")).forEach(itemFormula -> {
            try {
                String[] split = itemFormula.split("[<>=]");
                String orItemFormula = split[0];
                int num = split.length > 1 ? NumberUtils.parseNumber(split[1], Integer.class) : 1;
                String separator = itemFormula.contains("<") ? "<" : itemFormula.contains(">") ? ">" : itemFormula.contains("=") ? "=" : "";
                giftItems.addAll(analysisOriginItem(orItemFormula, separator, companyId, ruleId, version, itemType, matchType, num, group));
            } catch (Exception e) {
                throw new IllegalArgumentException("解析商品失败，无法解析的公式：" + itemFormula);
            }
        });

        return giftItems;
    }

    private static GiftRuleItem analysisOriginItem(String itemFormula, Long companyId, Long ruleId, Long version, Integer itemType, Integer matchType, Integer group) {
        GiftRuleItem giftItem = new GiftRuleItem();
        giftItem.setCompanyId(companyId);
        giftItem.setRuleId(ruleId);
        giftItem.setGroup(group);
        giftItem.setItemType(itemType);
        giftItem.setMatchType(matchType);
        if (RuleItemMatchTypeEnum.PLAT_SKU_ID.getType().equals(matchType)) {
            giftItem.setSkuId(itemFormula);
        }
        giftItem.setKeyword(itemFormula);
        giftItem.setRelation("AND");
        giftItem.setNum(1);
        giftItem.setNumMax(0);
        giftItem.setVersion(version);
        return giftItem;
    }

    private static List<GiftRuleItem> analysisOriginItem(String itemFormula, String separator, Long companyId, Long ruleId, Long version, Integer itemType, Integer matchType, Integer num, Integer group) {
        List<String> itemFormulaList = Arrays.stream(itemFormula.split("\\|")).collect(Collectors.toList());
        String relation = itemFormulaList.size() > 1 ? "OR" : "AND";
        List<GiftRuleItem> giftRuleItems = new ArrayList<>();
        itemFormulaList.stream().map(item -> item.replace("@", "").trim()).filter(StringUtils::hasText).forEach(item -> {

            GiftRuleItem giftItem = new GiftRuleItem();
            giftItem.setCompanyId(companyId);
            giftItem.setRuleId(ruleId);
            giftItem.setGroup(group);
            giftItem.setItemType(itemType);
            giftItem.setMatchType(matchType);
            if (RuleItemMatchTypeEnum.PLAT_SKU_ID.getType().equals(matchType)) {
                giftItem.setSkuId(item);
            }
            giftItem.setKeyword(item);
            giftItem.setRelation(relation);
            giftItem.setNum(1);
            giftItem.setNumMax(0);
            if (separator.equals(">")) {
                giftItem.setNum(num);
                giftItem.setNumMax(Integer.MAX_VALUE);
            }
            if (separator.contains("<")) {
                giftItem.setNum(0);
                giftItem.setNumMax(num);
            }
            if (separator.contains("=")) {
                giftItem.setNum(num);
                giftItem.setNumMax(num);
            }
            giftItem.setVersion(version);
            giftRuleItems.add(giftItem);
        });
        return giftRuleItems;
    }

    private static List<GiftRuleItem> analysisGiveItem(Long companyId, Long ruleId, Long version, String formula, Integer group) {
        if (!StringUtils.hasText(formula)) {
            return null;
        }
        String[] split = formula.split("=");
        Assert.isTrue(split.length >= 2, "解析商品失败，无法解析的公式：" + formula);
        String originItemStr = split[0];
        String giftItemStr = split[1];

        List<GiftRuleItem> originItems = analysisGiveItem(companyId, ruleId, version, originItemStr, RuleItemTypeEnum.RULE_ITEM_GROUP_A2B_PARTICIPANT_SYS, group);
        Assert.notEmpty(originItems, "解析商品失败，无法解析的公式：" + formula);
        List<GiftRuleItem> giftItems = analysisGiveItem(companyId, ruleId, version, giftItemStr, RuleItemTypeEnum.RULE_ITEM_GROUP_A2B_GIFT, group);
        Assert.notEmpty(giftItems, "解析商品失败，无法解析的公式：" + formula);

        if (!CollectionUtils.isEmpty(originItems) && !CollectionUtils.isEmpty(giftItems)) {
            originItems.addAll(giftItems);
        }
        return originItems;
    }


    private static List<GiftRuleItem> analysisGiveItem(Long companyId, Long ruleId, Long version, String itemsStr, RuleItemTypeEnum itemType, Integer group) {
        Assert.hasText(itemsStr, "公式异常！！！");
        if (!StringUtils.hasText(itemsStr)) {
            return null;
        }
        List<GiftRuleItem> giftItems = new ArrayList<>();
        Arrays.stream(itemsStr.split("\\+")).map(String::trim).filter(StringUtils::hasText).forEach(itemStr -> {
            GiftRuleItem giftItem = new GiftRuleItem();
            giftItem.setCompanyId(companyId);
            giftItem.setRuleId(ruleId);
            giftItem.setMatchType(RuleItemMatchTypeEnum.SKU.getType());
            String[] split = itemStr.split("\\*");
            String outerId = split[0];
            int num = split.length > 1 ? NumberUtils.parseNumber(split[1], Integer.class) : 1;
            giftItem.setItemType(itemType.getType());
            giftItem.setKeyword(outerId);
            giftItem.setGroup(group);
            giftItem.setNum(num);
            giftItem.setNumMax(0);
            giftItem.setRelation("AND");
            giftItem.setVersion(version);
            giftItems.add(giftItem);
        });
        return giftItems;
    }

    private static String restoreFormula(List<GiftRuleItem> giftItems) {
        if (CollectionUtils.isEmpty(giftItems)) {
            return "";
        }
        StringBuilder formula = new StringBuilder();
        StringBuilder origin = new StringBuilder();
        StringBuilder gift = new StringBuilder();
        giftItems.stream().collect(Collectors.groupingBy(GiftRuleItem::getItemType)).forEach((itemGroup, items) -> {
            int index = 0;
            boolean originItem = Objects.equals(RuleItemTypeEnum.RULE_ITEM_GROUP_A2B_PARTICIPANT_SYS.getType(), itemGroup);
            for (GiftRuleItem item : items) {
                (originItem ? origin : gift).append(item.getKeyword());
                if (item.getNum() > 1) {
                    (originItem ? origin : gift).append("*").append(item.getNum());
                }
                if (++index < items.size()) {
                    (originItem ? origin : gift).append("+");
                }
            }
        });
        formula.append(origin).append("=").append(gift);
        return formula.toString();
    }


    public static void main(String[] args) {
        String s1 = "@A+B=1,C,@A=2+B,@C>2,@F=3+A|B|C=5,";
        List<GiftRuleItem> giftItems1 = analysisOriginItem(1L, 1L, 1L, 1, 1, s1);

//        List<GiftV2Item> giftV2Items = new ArrayList<>();
//        String s2 = "A+B*3=C*5,D=E*2,";
//        GiftV2Item giftV2Item1 = new GiftV2Item();
//        giftV2Item1.setGroup(1);
//        giftV2Item1.setFormula(s2);
//        giftV2Items.add(giftV2Item1);
//
//        String s3 = "D+Q*3=P*5,C=W*2,";
//        GiftV2Item giftV2Item2 = new GiftV2Item();
//        giftV2Item2.setGroup(2);
//        giftV2Item2.setFormula(s3);
//        giftV2Items.add(giftV2Item2);
//
//        List<GiftRuleItem> giftItems = new ArrayList<>();
//        giftV2Items.forEach(giftV2Item -> giftItems.addAll(Objects.requireNonNull(GiftAnalysisUtils.analysisGiveItem(1L, 1L, 1L, giftV2Item))));
//
        String s3 = restoreOriginItemFormula(giftItems1);
//        List<GiftV2Item>  restoreGiftV2Items = restoreGiveItemFormula(giftItems);
//
//        GiftRule giftRule = new GiftRule();
//        giftRule.setShop("1,2,4,5");
//        giftRule.setBuyerRemark("牛马，老6，77，QQ，😘");
//        List<GiftRuleExtend> giftExtends = analysisGiveExtend(1L, 1L, 1L, giftRule);
//        GiftRule giftRule1 = new GiftRule();
//        restoreGiveExtend(giftExtends, giftRule1);
//
        System.out.println("------------------------------");
        System.out.println(s1 + "----" + s3);
        System.out.println(s1.equals(s3));
//        System.out.println(s2 + "----" + restoreGiftV2Items.get(0).getFormula());
//        System.out.println(s2.equals(restoreGiftV2Items.get(0).getFormula()));
//        System.out.println(s3 + "----" + restoreGiftV2Items.get(1).getFormula());
//        System.out.println(s3.equals(restoreGiftV2Items.get(1).getFormula()));
//        Assert.isTrue(!CollectionUtils.isEmpty(new ArrayList<>()) || Objects.equals(GiveTypeEnum.GIVE_TYPE_SELLER_REMARK.getType(), 1), "未选择赠品信息！！！");

        List<String> orderItemSize = Arrays.asList("M", "L", "A");
        List<String> ruleItemSize = Arrays.asList("L,M,XL,S,XS".split("[,，]"));
        /*
            赠送尺码在已设置的指定尺码中，那么排序逻销：赠送尺码(按规则：排前面>排后面)>指定尺码(按规则：相邻前面尺码>相邻后面尺码）＞非指定尺码(按ascii排序）＞无尺码（排最后)
                举例：指定排序为XL >M>S>xS,订单中购买的是M,L，那么排序：L>M>XL>S>×S>无尺码
            赠送尺码不在已设置的指定尺码中，那么排序逻辑：赠送尺码 (ASCII码排序） ＞指定尺码(按规则排序） ＞非指定尺码(按ascii排序）>无尺码
                举例：指定排序为XL L>M>S>xS,订单中购买的是xXL，xxS，那么排序：xXL>XXS>XL L>M>S>XS>无尺码
         */

        //这个是规则和订单中都存在的尺码，按规则的顺序取值
//        List<String> allHave = ruleItemSize.stream().filter(orderItemSize::contains).distinct().collect(Collectors.toList());
//        //这个是 规则中没有但是订单中有的尺码
//        List<String> ruleNonexistent = orderItemSize.stream().filter(size -> !ruleItemSize.contains(size)).distinct().collect(Collectors.toList());
//        ruleNonexistent.addAll(allHave);
//        ruleNonexistent.addAll(ruleItemSize);
//        List<String> sortItemSize = ruleNonexistent.stream().distinct().collect(Collectors.toList());
//        System.out.println(sortItemSize);
    }
}

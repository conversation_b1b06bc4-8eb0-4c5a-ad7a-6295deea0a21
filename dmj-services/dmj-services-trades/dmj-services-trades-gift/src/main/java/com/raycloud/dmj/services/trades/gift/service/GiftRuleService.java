package com.raycloud.dmj.services.trades.gift.service;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.StaffDataPrivilege;
import com.raycloud.dmj.domain.stalls.common.Pagination;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.gift.domain.*;
import com.raycloud.dmj.gift.enums.*;
import com.raycloud.dmj.gift.param.GiftSearchParam;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.gift.IGiftRecordLogService;
import com.raycloud.dmj.services.trades.gift.dao.GiftRuleDao;
import com.raycloud.dmj.services.trades.gift.utils.GiftAnalysisUtils;
import com.raycloud.dmj.services.trades.gift.IGiftRuleService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.trades.gift.utils.GiftRuleUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class GiftRuleService implements IGiftRuleService {

    @Resource
    private GiftRuleDao giftRuleDao;

    @Resource
    private GiftRuleExtendService giftRuleExtendService;

    @Resource
    private GiftRuleItemService giftRuleItemService;

    @Resource
    private GiftRuleService giftRuleService;

    @Resource
    private IGiftRecordLogService giftRecordLogService;

    @Resource
    private GiftRuleGroupService giftRuleGroupService;

    @Resource
    private IStaffService staffService;

    @Resource
    private ITradeConfigNewService tradeConfigNewService;

    /**
     * 保存赠品规则信息
     *
     * @param staff    员工信息
     * @param giftRule 规则信息
     */
    @Transactional
    public boolean save(Staff staff, GiftRule giftRule) {

        checkRule(giftRule);

        if (Objects.isNull(giftRule.getId())) {
            giftRuleService.saveRule(staff, giftRule);
        } else {
            giftRuleService.updateRule(staff, giftRule);
        }

        GiftRule byName = giftRuleDao.getByName(staff, giftRule.getName());
        Assert.notNull(byName, "规则保存失败！！！");

        //解析扩展信息为后端所用格式
        List<GiftRuleExtend> giftExtends = GiftAnalysisUtils.analysisGiveExtend(byName.getCompanyId(), byName.getId(), byName.getVersion(), giftRule);
        // 保存赠品规则扩展信息
        giftRuleExtendService.save(staff, byName.getId(), giftExtends);

        //解析赠品商品信息为后端所用格式
        List<GiftRuleItem> giftItems = analysisAllItem(byName.getCompanyId(), byName.getId(), byName.getVersion(), giftRule);
        // 保存赠品商品信息
        giftRuleItemService.save(staff, byName, byName.getId(), giftItems);

        addLog(staff, byName, giftExtends, giftItems);

        return true;
    }

    /**
     * 查询基本规则信息
     *
     * @param staff 员工信息
     * @param param 参数
     */
    public Pagination<GiftRule> getByParam(Staff staff, GiftSearchParam param, boolean fillGiveNum) {
        List<Long> ruleIdByParam = getRuleIdByParam(staff, param);
        // 上一步返回null根据店铺、或者商品条件，查询查空了，返回空list是没有相对应的条件
        if (ruleIdByParam == null) {
            return null;
        }

        if (!CollectionUtils.isEmpty(ruleIdByParam)) {
            param.setIds(ruleIdByParam);
        }

        Long total = giftRuleDao.countByParam(staff, param);
        List<GiftRule> byParam = giftRuleDao.getByParam(staff, param);
        if (CollectionUtils.isEmpty(byParam)) {
            return null;
        }

        TradeConfigNew giftRuleCheckPermission = TradeConfigGetUtil.get(staff, TradeConfigEnum.GIFT_RULE_CHECK_PERMISSION);
        StaffDataPrivilege staffDataPrivilege = staffService.queryStaffPrivilege(staff);
        if (giftRuleCheckPermission.isOpen() && !staff.isDefaultStaff()
                && org.apache.commons.collections.CollectionUtils.isNotEmpty(byParam) && !staffDataPrivilege.getAllUserFlag()) {
            List<Long> permissionIds = staff.getUsers().stream().map(User::getId).collect(Collectors.toList());
            List<GiftRule> needDelete = new ArrayList<>();
            for (GiftRule giftRule : byParam) {
                String shop = queryShop(staff, giftRule);
                giftRule.setShop(shop);
                List<Long> tempIds = Strings.getAsLongList(giftRule.getShop(), ",", true);
                if (tempIds.isEmpty() || shop.equals("-1")) {
                    continue;
                }
                if (org.apache.commons.collections.CollectionUtils.intersection(tempIds, permissionIds).isEmpty()) {
                    needDelete.add(giftRule);
                }
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(needDelete)) {
                byParam.removeAll(needDelete);
                total = total - needDelete.size();
            }
        }

        if (!fillGiveNum) {
            return new Pagination<>(byParam, total, param.getPage());
        }

        fillGiveTradeNum(staff, byParam);
        fillRuleGiveNum(staff, byParam);
        fillOtherField(staff, byParam);
        fillShop(staff, byParam);

        return new Pagination<>(byParam, total, param.getPage());
    }

    /**
     *
     * @param staff
     * @param giftRules
     */
    private void fillOtherField(Staff staff, List<GiftRule> giftRules) {
        giftRules.forEach(giftRule -> {
            giftRule.setIsOpen(giftRule.getStatus());
        });
    }

    private void fillShop(Staff staff, List<GiftRule> giftRules) {
        if (CollectionUtils.isEmpty(giftRules)) {
            return;
        }
        for (GiftRule giftRule : giftRules) {
            String shop = queryShop(staff, giftRule);
            giftRule.setShop(shop);
        }
    }

    private String queryShop(Staff staff, GiftRule giftRule) {
        List<GiftRuleExtend> byKey = giftRuleExtendService.getByKey(staff, "shop", giftRule.getId());
        List<String> collect = byKey.stream().map(GiftRuleExtend::getValue).collect(Collectors.toList());
        return StringUtils.join(collect, ",");
    }

    public Pagination<GiftRule> getByParam(Staff staff, GiftSearchParam param) {
        return getByParam(staff, param, Boolean.TRUE);
    }

    public Long countByParam(Staff staff, GiftSearchParam param) {
        List<Long> ruleIdByParam = getRuleIdByParam(staff, param);
        // 上一步返回null根据店铺、或者商品条件，查询查空了，返回空list是没有相对应的条件
        if (ruleIdByParam == null) {
            return null;
        }

        if (!CollectionUtils.isEmpty(ruleIdByParam)) {
            param.setIds(ruleIdByParam);
        }

        return giftRuleDao.countByParam(staff, param);
    }

    /**
     * 查询详细规则信息
     *
     * @param staff 员工信息
     * @param id    规则id
     */
    public GiftRule getDetailsById(Staff staff, Long id) {
        if (Objects.isNull(id) || id <= 0) {
            return null;
        }

        GiftRule byId = giftRuleDao.getById(staff, id);
        if (Objects.isNull(byId)) {
            return null;
        }

        //将规则扩展信息还原为前端所用格式
        List<GiftRuleExtend> ruleExtends = giftRuleExtendService.getByRuleId(staff, id);
        if (!CollectionUtils.isEmpty(ruleExtends)) {
            GiftAnalysisUtils.restoreGiveExtend(ruleExtends, byId);
        }

        //将规则商品还原为前端所用格式
        List<GiftRuleItem> ruleItems = giftRuleItemService.getByRuleId(staff, id);
        if (!CollectionUtils.isEmpty(ruleItems)) {
            // 填充商品基本信息
            restoreAllItem(ruleItems, byId);
        }
        giftRuleItemService.fillGiveItems(staff, byId.getGiftItems());
        giftRecordLogService.fillGiveNum(staff, byId);
        fillOtherField(staff, Collections.singletonList(byId));

        return byId;
    }

    /**
     * 填充规则详情
     */
    public void fillRule(Staff staff, List<GiftRule> giftRules) {
        if (CollectionUtils.isEmpty(giftRules)) {
            return;
        }
        List<Long> ruleIds = giftRules.stream().map(GiftRule::getId).collect(Collectors.toList());
        Map<Long, List<GiftRuleExtend>> giftRuleExtendMap = giftRuleExtendService.getByRuleIds(staff, ruleIds);
        Map<Long, List<GiftRuleItem>> giftRuleItemMap = giftRuleItemService.getByRuleIds(staff, ruleIds);

        giftRules.forEach(giftRule -> {
            //将规则扩展信息还原为前端所用格式
            List<GiftRuleExtend> ruleExtends = CollectionUtils.isEmpty(giftRuleExtendMap) ? null : giftRuleExtendMap.get(giftRule.getId());
            if (!CollectionUtils.isEmpty(ruleExtends)) {
                GiftAnalysisUtils.restoreGiveExtend(ruleExtends, giftRule);
            }

            //将规则商品还原为前端所用格式
            List<GiftRuleItem> ruleItems = CollectionUtils.isEmpty(giftRuleItemMap) ? null : giftRuleItemMap.get(giftRule.getId());
            if (!CollectionUtils.isEmpty(ruleItems)) {
                restoreAllItem(ruleItems, giftRule);
            }
        });
    }

    public List<GiftRuleExtend> getGiftRuleExtend(Staff staff, Long ruleId) {
        if (Objects.isNull(ruleId)) {
            return null;
        }
        return giftRuleExtendService.getByRuleId(staff, ruleId);
    }

    public Map<Long, List<GiftRuleExtend>> getGiftRuleExtend(Staff staff, List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return null;
        }
        return giftRuleExtendService.getByRuleIds(staff, ruleIds);
    }

    public Map<Long, List<GiftRuleItem>> getGiftRuleItem(Staff staff, List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return null;
        }
        return giftRuleItemService.getByRuleIds(staff, ruleIds);
    }

    /**
     * 填充赠品赠送订单数量
     */
    public void fillGiveTradeNum(Staff staff, List<GiftRule> giftRules) {
        if (CollectionUtils.isEmpty(giftRules)) {
            return;
        }
        List<Long> ruleIds = giftRules.stream().map(GiftRule::getId).collect(Collectors.toList());
        List<GiftRecordLog> countByRuleIds = giftRecordLogService.countGiveTradeNumByRuleIds(staff, ruleIds);
        if (CollectionUtils.isEmpty(countByRuleIds)) {
            return;
        }
        Map<Long, GiftRecordLog> giftRecordLogMap = countByRuleIds.stream().collect(Collectors.toMap(GiftRecordLog::getRuleId, v -> v));
        giftRules.forEach(giftRule -> {
            GiftRecordLog giftRecordLog = giftRecordLogMap.get(giftRule.getId());
            if (Objects.isNull(giftRecordLog)) {
                return;
            }
            giftRule.setGiveTradeNum(giftRecordLog.getGiveNum());
        });
    }

    /**
     * 填充赠品赠送数量
     */
    public void fillRuleGiveNum(Staff staff, List<GiftRule> giftRules) {
        if (CollectionUtils.isEmpty(giftRules)) {
            return;
        }
        List<Long> ruleIds = giftRules.stream().map(GiftRule::getId).collect(Collectors.toList());
        List<GiftRecordLog> countByRuleIds = giftRecordLogService.sumGiveGiftNumByRuleIds(staff, ruleIds);
        if (CollectionUtils.isEmpty(countByRuleIds)) {
            return;
        }
        Map<Long, GiftRecordLog> giftRecordLogMap = countByRuleIds.stream().collect(Collectors.toMap(GiftRecordLog::getRuleId, v -> v));
        giftRules.forEach(giftRule -> {
            GiftRecordLog giftRecordLog = giftRecordLogMap.get(giftRule.getId());
            if (Objects.isNull(giftRecordLog)) {
                return;
            }
            giftRule.setGiveGiftNum(giftRecordLog.getGiveNum());
        });
    }

    /**
     * 启用、关闭规则
     *
     * @param staff  员工信息
     * @param ruleId 规则id
     * @param isOpen 0：关闭 1：启用
     */
    @Transactional
    public void enableRule(Staff staff, Long ruleId, Integer isOpen) {
        Assert.notNull(ruleId, "操作规则异常！！！");

        GiftRule byId = giftRuleDao.getById(staff, ruleId);
        Assert.notNull(byId, "操作规则异常！！！");

        giftRuleDao.enableRule(staff, ruleId, isOpen);

        List<GiftRuleExtend> giftRuleExtends = giftRuleExtendService.getByRuleId(staff, ruleId);
        // 保存赠品规则扩展信息
        giftRuleExtendService.save(staff, byId.getId(), giftRuleExtends);

        List<GiftRuleItem> giftRuleItems = giftRuleItemService.getByRuleId(staff, ruleId);
        // 保存赠品商品信息
        giftRuleItemService.save(staff, byId, byId.getId(), giftRuleItems);

        GiftRule newGiftRule = giftRuleDao.getById(staff, ruleId);
        List<GiftRuleExtend> newGiftRuleExtends = giftRuleExtendService.getByRuleId(staff, ruleId);
        List<GiftRuleItem> newGiftRuleItems = giftRuleItemService.getByRuleId(staff, ruleId);
        newGiftRuleItems.forEach(item -> item.setVersion(newGiftRule.getVersion()));
        newGiftRuleExtends.forEach(item -> item.setVersion(newGiftRule.getVersion()));
        newGiftRule.setRuleId(byId.getId());
        newGiftRule.setLog(true);
        newGiftRule.setOperationId(String.valueOf(staff.getClueId()));
        newGiftRule.setOperator(staff.getName());
        giftRuleDao.saveLog(staff, newGiftRule);
        giftRuleExtendService.saveLog(staff, newGiftRule.getId(), newGiftRuleExtends);
        giftRuleItemService.saveLog(staff, newGiftRule.getId(), newGiftRuleItems);
    }

    /**
     * 删除规则
     *
     * @param staff  员工信息
     * @param ruleId 规则id
     */
    @Transactional
    public void deleteRule(Staff staff, Long ruleId) {
        Assert.notNull(ruleId, "操作规则异常！！！");
        GiftRule byId = giftRuleDao.getById(staff, ruleId);
        Assert.notNull(byId, "操作规则不存在，请刷新页面再试！！！");

        byId.setRuleId(ruleId);
        byId.setLog(true);
        byId.setOperationId(String.valueOf(staff.getClueId()));
        byId.setOperator(staff.getName());
        giftRuleDao.saveLog(staff, byId);
        giftRuleDao.deleteRule(staff, ruleId);

        giftRuleExtendService.deleteByRuleId(staff, ruleId);
        giftRuleItemService.deleteByRuleId(staff, ruleId);

    }

    @Override
    public List<GiftRuleGroup> getGroupList(Staff staff) {
        return giftRuleGroupService.getAll(staff);
    }

    @Override
    public void saveGroup(Staff staff, GiftRuleGroup giftRuleGroup) {
        if (Objects.isNull(giftRuleGroup)) {
            return;
        }

        if(Objects.isNull(giftRuleGroup.getId())){
            giftRuleGroupService.save(staff, giftRuleGroup);
            return;
        }
        giftRuleGroupService.update(staff, giftRuleGroup);
    }

    public void deleteGroup(Staff staff, Long id) {
        if (Objects.isNull(id)) {
            return;
        }
        giftRuleGroupService.delete(staff, id);
    }

    @Override
    @Transactional
    public void batchUpdateShops(Staff staff, List<Long> ruleIds, String shops, Integer operatorType) {
        if (CollectionUtils.isEmpty(ruleIds) || StringUtils.isEmpty(shops) || Objects.isNull(operatorType)) {
            return;
        }

        List<GiftRule> giftRules = giftRuleDao.getByIds(staff, ruleIds);

        Set<String> shopSet = new HashSet<>(Arrays.asList(shops.split(",")));
        for (GiftRule giftRule : giftRules) {
            List<GiftRuleExtend> giftExtendList = giftRuleExtendService.getByKey(staff, "shop", giftRule.getId());
            List<GiftRuleExtend> giftRuleExtends = giftExtendList.stream().filter(giftRuleExtend -> hasPermission(staff, giftRuleExtend.getValue())).collect(Collectors.toList());
            if (!giftRuleExtends.isEmpty()) {
                List<Long> deleteIds = giftRuleExtends.stream().map(GiftRuleExtend::getId).collect(Collectors.toList());
                if (!deleteIds.isEmpty()) {
                    giftRuleExtendService.deleteByIds(staff, deleteIds);
                }
            }

            Set<String> existShops = handleShop(shops, operatorType, giftRuleExtends, shopSet);

            List<GiftRuleExtend> saveGiftRuleExtendList = existShops.stream().map(shopId -> {
                GiftRuleExtend giftRuleExtend = new GiftRuleExtend();
                giftRuleExtend.setCompanyId(staff.getCompanyId());
                giftRuleExtend.setRuleId(giftRule.getId());
                giftRuleExtend.setKey("shop");
                giftRuleExtend.setValue(shopId);
                giftRuleExtend.setVersion(giftRule.getVersion());
                return giftRuleExtend;
            }).collect(Collectors.toList());
            giftRuleExtendService.saveNew(staff, giftRule.getId(), saveGiftRuleExtendList);

            giftRuleService.updateRule(staff, giftRule);
            GiftRule byName = giftRuleDao.getByName(staff, giftRule.getName());
            Assert.notNull(byName, "规则保存失败！！！");

            List<GiftRuleExtend> giftRuleExtendList = giftRuleExtendService.getByRuleId(staff, giftRule.getId());
            List<GiftRuleItem> giftRuleItemList = giftRuleItemService.getByRuleId(staff, giftRule.getId());
            List<GiftRuleExtend> logGiftRuleExtendList = GiftRuleUtils.copyGiftRuleExtend(giftRuleExtendList, byName.getVersion());
            List<GiftRuleItem> logGiftRuleItemList = GiftRuleUtils.copyGiftRuleItem(giftRuleItemList, byName.getVersion());

            addLog(staff, byName, logGiftRuleExtendList, logGiftRuleItemList);
        }
    }

    private Set<String> handleShop(String shops, Integer operatorType, List<GiftRuleExtend> giftRuleExtends, Set<String> shopSet) {
        Set<String> existShops = giftRuleExtends.stream().map(GiftRuleExtend::getValue).collect(Collectors.toSet());
        if (operatorType == 1) {
            if (!Objects.equals("-1", shops)) {
                existShops.addAll(shopSet);
                existShops.remove("-1");
            }
        } else if (operatorType == 2) {
            existShops.clear();
            existShops.addAll(shopSet);
        } else if (operatorType == 3 ){
            if (!Objects.equals("-1", shops)) {
                existShops.removeAll(shopSet);
            }
            if (existShops.isEmpty()) {
                existShops.add("-1");
            }
        }
        return existShops;
    }

    private void addLog(Staff staff, GiftRule byName, List<GiftRuleExtend> giftRuleExtends, List<GiftRuleItem> giftRuleItemList) {
        byName.setRuleId(byName.getId());
        byName.setLog(true);
        byName.setOperationId(String.valueOf(staff.getClueId()));
        byName.setOperator(staff.getName());
        giftRuleDao.saveLog(staff, byName);

        giftRuleExtendService.saveLog(staff, byName.getId(), giftRuleExtends);
        giftRuleItemService.saveLog(staff, byName.getId(), giftRuleItemList);
    }

    public List<GiftRule> getLogByRuleId(Staff staff, Long ruleId) {
        if (Objects.isNull(ruleId) || ruleId <= 0) {
            return null;
        }
        return giftRuleDao.getLogByRuleId(staff, ruleId);
    }

    /**
     * 查询详细规则信息
     *
     * @param staff 员工信息
     * @param id    规则id
     */
    public GiftRule getLogDetailsById(Staff staff, Long id) {
        if (Objects.isNull(id) || id <= 0) {
            return null;
        }

        GiftRule giftRuleLog = giftRuleDao.getLogById(staff, id);
        if (Objects.isNull(giftRuleLog)) {
            return null;
        }

        //将规则扩展信息还原为前端所用格式
        List<GiftRuleExtend> ruleExtends = giftRuleExtendService.getLogByRuleId(staff, giftRuleLog.getRuleId(), giftRuleLog.getVersion());
        if (!CollectionUtils.isEmpty(ruleExtends)) {
            GiftAnalysisUtils.restoreGiveExtend(ruleExtends, giftRuleLog);
        }

        //将规则商品还原为前端所用格式
        List<GiftRuleItem> ruleItems = giftRuleItemService.getLogByRuleId(staff, giftRuleLog.getRuleId(), giftRuleLog.getVersion());
        if (!CollectionUtils.isEmpty(ruleItems)) {
            // 填充商品基本信息
            restoreAllItem(ruleItems, giftRuleLog);
        }
        giftRuleItemService.fillGiveItems(staff, giftRuleLog.getGiftItems());
        giftRecordLogService.fillGiveNum(staff, giftRuleLog);
        return giftRuleLog;
    }

    @Override
    public Map<Long, List<GiftRule>> getAllValidRule(Staff staff) {
        return giftRuleDao.getAllValidRule(staff);
    }

    @Transactional
    public void saveRule(Staff staff, GiftRule giftRule) {
        checkSaveRule(staff, giftRule);
        checkSaveRuleGroup(staff, giftRule);
        giftRuleDao.save(staff, giftRule);
    }

    @Transactional
    public void updateRule(Staff staff, GiftRule giftRule) {
        checkUpdateRule(staff, giftRule);
        giftRuleDao.update(staff, giftRule);
    }

    public List<GiftRuleExtend> getGiftRuleExtendByKey(Staff staff, String key, Long ruleId) {
        return giftRuleExtendService.getByKey(staff, key, ruleId);
    }

    @Override
    @Transactional
    public List<String> batchModifyItem(Staff staff, List<GiftRule> giftRules, List<GiftRuleItem> giftRuleItems, Integer operatorType) {
        if (CollectionUtils.isEmpty(giftRules) || CollectionUtils.isEmpty(giftRuleItems)) {
            return new ArrayList<>();
        }
        List<String> errorMsg = new ArrayList<>();
        for (GiftRule giftRule : giftRules) {
            if (!giftRule.getSysItemMatchType().equals(RuleItemMatchTypeEnum.SKU.getType())) {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("赠品规则[%s]系统商品匹配类型为%s，跳过执行！", giftRule.getName(), RuleItemMatchTypeEnum.valueOf(giftRule.getSysItemMatchType()).getName())));
                continue;
            }
            List<GiftRuleItem> giftRuleItemList = giftRuleItemService.getByRuleId(staff, giftRule.getId());
            // 需要保留的内容
            List<GiftRuleItem> retainGiftRuleItemList = giftRuleItemList.stream().filter(item -> !RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_SYS.getType().equals(item.getItemType())
                    || !RuleItemMatchTypeEnum.SKU.getType().equals(item.getMatchType())).collect(Collectors.toList());
            giftRuleItemList = giftRuleItemList.stream().filter(item -> RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_SYS.getType().equals(item.getItemType())
                    && RuleItemMatchTypeEnum.SKU.getType().equals(item.getMatchType())).collect(Collectors.toList());
            Set<String> existSet = giftRuleItemList.stream().map(GiftRuleItem::getKeyword).collect(Collectors.toSet());
            Set<String> newSet = giftRuleItems.stream().map(GiftRuleItem::getKeyword).collect(Collectors.toSet());
            retainGiftRuleItemList.forEach(item -> item.setVersion(giftRule.getVersion()+1));
            boolean logFlag = false;
            switch (operatorType) {
                case 1:
                    for (GiftRuleItem giftRuleItem : giftRuleItems) {
                        if (existSet.contains(giftRuleItem.getKeyword())) {
                            Logs.ifDebug(LogHelper.buildLog(staff, String.format("指定商品[%s]存在，不添加！", giftRuleItem.getKeyword())));
                            continue;
                        }
                        giftRuleItem.setCompanyId(staff.getCompanyId());
                        giftRuleItem.setRuleId(giftRule.getId());
                        giftRuleItem.setVersion(giftRule.getVersion()+1);
                        retainGiftRuleItemList.add(giftRuleItem);
                    }
                    giftRuleItemList.forEach(item -> item.setVersion(giftRule.getVersion()+1));
                    retainGiftRuleItemList.addAll(giftRuleItemList);
                    logFlag = true;
                    break;
                case 2:
                    for (GiftRuleItem giftRuleItem : giftRuleItems) {
                        giftRuleItem.setCompanyId(staff.getCompanyId());
                        giftRuleItem.setRuleId(giftRule.getId());
                        giftRuleItem.setVersion(giftRule.getVersion()+1);
                        retainGiftRuleItemList.add(giftRuleItem);
                    }
                    logFlag = true;
                    break;
                case 3:
                    if (existSet.containsAll(newSet)) {
                        logFlag = true;
                    }
                    giftRuleItemList.removeIf(giftRuleItem -> newSet.contains(giftRuleItem.getKeyword()));
                    for (GiftRuleItem giftRuleItem : giftRuleItemList) {
                        GiftRuleItem copy = new GiftRuleItem();
                        copy.setCompanyId(giftRuleItem.getCompanyId());
                        copy.setRuleId(giftRuleItem.getRuleId());
                        copy.setGroup(giftRuleItem.getGroup());
                        copy.setItemType(giftRuleItem.getItemType());
                        copy.setMatchType(giftRuleItem.getMatchType());
                        copy.setKeyword(giftRuleItem.getKeyword());
                        copy.setRelation("AND");
                        copy.setNum(1);
                        copy.setNumMax(0);
                        copy.setVersion(giftRule.getVersion()+1);
                        retainGiftRuleItemList.add(copy);
                    }
                    break;
                default:
                    throw new IllegalStateException("不支持的操作类型: " + operatorType);
            }
            if (operatorType == 3 && CollectionUtils.isEmpty(giftRuleItemList)) {
                if (GiveTypeEnum.GIVE_TYPE_COPY.getType().equals(giftRule.getGiveType())) {
                    errorMsg.add(String.format("规则[%s]在买A送A模式下必须存在指定参与商品，不支持全部删除！", giftRule.getName()));
                    break;
                }
            }
            // 重新赋值group字段
            AtomicInteger group = new AtomicInteger(1);
            retainGiftRuleItemList.stream().filter(item -> RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_SYS.getType().equals(item.getItemType())
                    && RuleItemMatchTypeEnum.SKU.getType().equals(item.getMatchType())).forEach(item -> item.setGroup(group.getAndIncrement()));
            giftRuleItemService.save(staff, giftRule, giftRule.getId(), retainGiftRuleItemList);

            giftRuleService.updateRule(staff, giftRule);
            GiftRule byName = giftRuleDao.getByName(staff, giftRule.getName());
            Assert.notNull(byName, "规则保存失败！！！");

            if (logFlag) {
                List<GiftRuleExtend> giftRuleExtendLogList = giftRuleExtendService.getByRuleId(staff, giftRule.getId());
                List<GiftRuleItem> giftRuleItemLogList = giftRuleItemService.getByRuleId(staff, giftRule.getId());
                List<GiftRuleExtend> logGiftRuleExtendList = GiftRuleUtils.copyGiftRuleExtend(giftRuleExtendLogList, byName.getVersion());
                List<GiftRuleItem> logGiftRuleItemList = GiftRuleUtils.copyGiftRuleItem(giftRuleItemLogList, byName.getVersion());

                addLog(staff, byName, logGiftRuleExtendList, logGiftRuleItemList);
            }
        }
        return errorMsg;
    }

    private void checkSaveRule(Staff staff, GiftRule giftRule) {
        GiftRule byName = giftRuleDao.getByName(staff, giftRule.getName());
        Assert.isNull(byName, "规则名称不能重复！！！");
    }

    private void checkSaveRuleGroup(Staff staff, GiftRule giftRule) {
        GiftRuleGroup group = null;
        Assert.notNull(giftRule.getGroupId(), "系统分组不能为空");
        if (giftRule.getGroupId() == 0L) {
            group = giftRuleGroupService.getByName(staff, "系统默认分组");
        }

        if (giftRule.getGroupId() == 0L && Objects.isNull(group)) {
            GiftRuleGroup newGroup = new GiftRuleGroup();
            newGroup.setName("系统默认分组");
            giftRuleGroupService.save(staff, newGroup);
            group = giftRuleGroupService.getByName(staff, "系统默认分组");
        }

        if (Objects.isNull(group)) {
            group = giftRuleGroupService.getById(staff, giftRule.getGroupId());
        }
        Assert.notNull(group, "未知规则分组！！！");
        giftRule.setGroupId(group.getId());
    }

    private boolean getSearchRuleIds(Staff staff, List<Long> ruleIds, Integer itemType, List<String> items, Integer itemSearchRelation) {
        List<GiftRuleItem> giftItems = giftRuleItemService.getByItemTypeAndKeyword(staff, itemType, items, itemSearchRelation);
        if (CollectionUtils.isEmpty(giftItems)) {
            return false;
        }

        List<Long> ruleIdList = giftItems.stream().map(GiftRuleItem::getRuleId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ruleIds)) {
            ruleIds.addAll(ruleIdList);
        } else {
            ruleIds.retainAll(ruleIdList);
        }

        return true;
    }

    private void checkUpdateRule(Staff staff, GiftRule giftRule) {
        GiftRule byId = giftRuleDao.getById(staff, giftRule.getId());
        Assert.notNull(byId, "原规则不存在！！！");

        if (!byId.getName().equals(giftRule.getName())) {
            GiftRule byName = giftRuleDao.getByName(staff, giftRule.getName());
            Assert.isNull(byName, "规则名称不能重复！！！");
        }
    }

    private List<GiftRuleItem> analysisAllItem(Long companyId, Long ruleId, Long version, GiftRule parameter) {
        List<GiftRuleItem> allItems = new ArrayList<>();
        //解析系统商品信息
        List<GiftRuleItem> sysItems = GiftAnalysisUtils.analysisOriginItem(companyId, ruleId, version, RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_SYS.getType(), parameter.getSysItemMatchType(), parameter.getSysItems());
        if (!CollectionUtils.isEmpty(sysItems)) {
            allItems.addAll(sysItems);
        }

        //解析平台规格商家编码信息
        List<GiftRuleItem> platOuterSkuIdItems = GiftAnalysisUtils.analysisOriginItem(companyId, ruleId, version, RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_PLAT_OUTER_SKU_ID.getType(), parameter.getPlatItemMatchType(), parameter.getPlatOuterSkuIdItems());
        if (!CollectionUtils.isEmpty(platOuterSkuIdItems)) {
            allItems.addAll(platOuterSkuIdItems);
        }

        //解析平台规则ID信息
        List<GiftRuleItem> platSkuIdItems = GiftAnalysisUtils.analysisOriginItem(companyId, ruleId, version, RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_PLAT_SKU_ID.getType(), parameter.getPlatItemMatchType(), parameter.getPlatSkuIdItems());
        if (!CollectionUtils.isEmpty(platSkuIdItems)) {
            allItems.addAll(platSkuIdItems);
        }

        //解析排除商品信息（包含即不送）
        List<GiftRuleItem> excludeItems = GiftAnalysisUtils.analysisOriginItem(companyId, ruleId, version, RuleItemTypeEnum.RULE_ITEM_GROUP_EXCLUDE_SYS.getType(), parameter.getExcludeItemMatchType(), parameter.getExcludeItems());
        if (!CollectionUtils.isEmpty(excludeItems)) {
            allItems.addAll(excludeItems);
        }

        //解析排除商品信息（排除后送赠）
        List<GiftRuleItem> excludeAfterGiveItems = GiftAnalysisUtils.analysisOriginItem(companyId, ruleId, version, RuleItemTypeEnum.RULE_ITEM_GROUP_EXCLUDE_AFTER_GIVE_SYS.getType(), parameter.getExcludeItemAfterGiveMatchType(), parameter.getExcludeAfterGiveItems());
        if (!CollectionUtils.isEmpty(excludeAfterGiveItems)) {
            allItems.addAll(excludeAfterGiveItems);
        }

        Assert.isTrue(!CollectionUtils.isEmpty(parameter.getGiftItems()) || Objects.equals(GiveTypeEnum.GIVE_TYPE_SELLER_REMARK.getType(), parameter.getGiveType()), "未选择赠品信息！！！");

        //解析买A送B
        if (Objects.equals(GiveTypeEnum.GIVE_TYPE_A2B.getType(), parameter.getGiveType())) {
            parameter.getGiftItems().forEach(giftV2Item -> {
                List<GiftRuleItem> giftItems = GiftAnalysisUtils.analysisGiveItem(companyId, ruleId, version, giftV2Item);
                if (!CollectionUtils.isEmpty(giftItems)) {
                    allItems.addAll(giftItems);
                }
            });
            return allItems;
        }

        //解析其他
        List<GiftRuleItem> giftItems = GiftAnalysisUtils.analysisGiveItem(companyId, ruleId, version, parameter.getGiftItems());
        if (!CollectionUtils.isEmpty(giftItems)) {
            allItems.addAll(giftItems);
        }

        return allItems;
    }

    private void restoreAllItem(List<GiftRuleItem> ruleItems, GiftRule target) {
        if (CollectionUtils.isEmpty(ruleItems)) {
            return;
        }
        List<GiftRuleItem> formula = new ArrayList<>();

        ruleItems.stream().collect(Collectors.groupingBy(GiftRuleItem::getItemType)).forEach((itemGroup, giftRuleItems) -> {
            if (Objects.equals(itemGroup, RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_SYS.getType())) {
                target.setSysItems(GiftAnalysisUtils.restoreOriginItemFormula(giftRuleItems));
            }
            if (Objects.equals(itemGroup, RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_PLAT_OUTER_SKU_ID.getType())) {
                target.setPlatOuterSkuIdItems(GiftAnalysisUtils.restoreOriginItemFormula(giftRuleItems));
            }
            if (Objects.equals(itemGroup, RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_PLAT_SKU_ID.getType())) {
                target.setPlatSkuIdItems(GiftAnalysisUtils.restoreOriginItemFormula(giftRuleItems));
            }
            if (Objects.equals(itemGroup, RuleItemTypeEnum.RULE_ITEM_GROUP_EXCLUDE_SYS.getType())) {
                target.setExcludeItems(GiftAnalysisUtils.restoreOriginItemFormula(giftRuleItems));
            }
            if (Objects.equals(itemGroup, RuleItemTypeEnum.RULE_ITEM_GROUP_EXCLUDE_AFTER_GIVE_SYS.getType())) {
                target.setExcludeAfterGiveItems(GiftAnalysisUtils.restoreOriginItemFormula(giftRuleItems));
            }
            if (Objects.equals(itemGroup, RuleItemTypeEnum.RULE_ITEM_GROUP_GIFT.getType())) {
                target.setGiftItems(GiftAnalysisUtils.restoreGiveItem(giftRuleItems));
            }
            if (Objects.equals(itemGroup, RuleItemTypeEnum.RULE_ITEM_GROUP_A2B_PARTICIPANT_SYS.getType())) {
                formula.addAll(giftRuleItems);
            }
            if (Objects.equals(itemGroup, RuleItemTypeEnum.RULE_ITEM_GROUP_A2B_GIFT.getType())) {
                formula.addAll(giftRuleItems);
            }
        });
        if (CollectionUtils.isEmpty(formula)) {
            return;
        }

        List<GiftV2Item> giftV2Items = GiftAnalysisUtils.restoreGiveItemFormula(formula);
        if (CollectionUtils.isEmpty(giftV2Items)) {
            return;
        }

        if (CollectionUtils.isEmpty(target.getGiftItems())) {
            target.setGiftItems(giftV2Items);
        } else {
            target.getGiftItems().addAll(giftV2Items);
        }
    }

    public List<Long> getRuleIdByParam(Staff staff, GiftSearchParam param) {
        List<Long> ruleIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(param.getShopIds())) {
            List<String> shopIds = param.getShopIds().stream().map(String::valueOf).collect(Collectors.toList());
            shopIds.add("-1");
            List<GiftRuleExtend> byShop = giftRuleExtendService.getByKeyAndValue(staff, "shop", shopIds);
            if (CollectionUtils.isEmpty(byShop)) {
                return null;
            }

            ruleIds.addAll(byShop.stream().map(GiftRuleExtend::getRuleId).distinct().collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(param.getSysItems())) {
            boolean searchRuleIds = getSearchRuleIds(staff, ruleIds, RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_SYS.getType(), param.getSysItems(), param.getItemSearchRelation());
            if (!searchRuleIds) {
                return null;
            }
        }

        if (!CollectionUtils.isEmpty(param.getPlatOuterSkuIdItems())) {
            boolean searchRuleIds = getSearchRuleIds(staff, ruleIds, RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_PLAT_OUTER_SKU_ID.getType(), param.getPlatOuterSkuIdItems(), null);
            if (!searchRuleIds) {
                return null;
            }
        }

        if (!CollectionUtils.isEmpty(param.getPlatSkuIdItems())) {
            boolean searchRuleIds = getSearchRuleIds(staff, ruleIds, RuleItemTypeEnum.RULE_ITEM_GROUP_PARTICIPANT_PLAT_SKU_ID.getType(), param.getPlatSkuIdItems(), null);
            if (!searchRuleIds) {
                return null;
            }
        }

        if (!CollectionUtils.isEmpty(param.getGiveItems())) {
            boolean searchRuleIds = getSearchRuleIds(staff, ruleIds, RuleItemTypeEnum.RULE_ITEM_GROUP_GIFT.getType(), param.getGiveItems(), 1);
            if (!searchRuleIds) {
                return null;
            }
        }
        return ruleIds;
    }

    private void checkRule(GiftRule giftRule) {
        Assert.notNull(giftRule, "规则信息异常！！！");
        Assert.hasText(giftRule.getName(), "规则名称不能为空！！！");
        Assert.notNull(giftRule.getStartTime(), "活动起始时间不能为空！！！");
        Assert.notNull(giftRule.getEndTime(), "活动结束时间不能为空！！！");
        Assert.isTrue(giftRule.getEndTime().getTime() > giftRule.getStartTime().getTime(), "活动起始时间不能大于结束时间！！！");
        Assert.isTrue(new Date().getTime() < giftRule.getEndTime().getTime() , "活动结束时间不能小于当前时间！！！");
        Assert.notNull(giftRule.getPriority(), "活动优先级不能为空！！！");
        Assert.notNull(giftRule.getRuleType(), "促销类型不能为空！！！");
        Assert.isTrue(giftRule.getSysItems().split(",").length <= 5000, "指定参与商品不能超过5000条！");
        if (Objects.nonNull(giftRule.getPlatOuterSkuIdItems())) {
            Assert.isTrue(giftRule.getPlatOuterSkuIdItems().split(",").length <= 5000, "指定平台规格商家编码不能超过5000条！");
        }
        if (Objects.nonNull(giftRule.getPlatSkuIdItems())) {
            Assert.isTrue(giftRule.getPlatSkuIdItems().split(",").length <= 5000, "指定平台规格ID不能超过5000条！");
        }
        if (Objects.nonNull(giftRule.getExcludeItems())) {
            Assert.isTrue(giftRule.getExcludeItems().split(",").length <= 5000, "排除指定商品不能超过5000条！");
        }
        if (Objects.nonNull(giftRule.getExcludeAfterGiveItems())) {
            Assert.isTrue(giftRule.getExcludeAfterGiveItems().split(",").length <= 5000, "排除指定商品不能超过5000条！");
        }
        if (Objects.isNull(giftRule.getRepetitionGive())) {
            giftRule.setRepetitionGive(0);
        }

        Assert.isTrue(giftRule.getRepetitionGive() >= -2, "赠品多组值异常！！！");
        if (Objects.isNull(giftRule.getSellerMemoMatchType())) {
            giftRule.setSellerMemoMatchType(ConditionEnum.AND.getType());
        }

        if (Objects.isNull(giftRule.getBuyerLeaveWordInclude())) {
            giftRule.setBuyerLeaveWordInclude(ConditionEnum.OR.getType());
        }

        if (Objects.isNull(giftRule.getSellerMemoInclude())) {
            giftRule.setBuyerLeaveWordInclude(ConditionEnum.OR.getType());
        }

        if (Objects.isNull(giftRule.getTagMatchType())) {
            giftRule.setTagMatchType(ConditionEnum.UNCHECK.getType());
        }

        if (Objects.isNull(giftRule.getSysItemMatchType())) {
            giftRule.setSysItemMatchType(RuleItemMatchTypeEnum.SKU.getType());
        }

        if (Objects.isNull(giftRule.getPlatItemMatchType())) {
            giftRule.setPlatItemMatchType(RuleItemMatchTypeEnum.PLAT_OUTER_SKU_ID.getType());
        }

        if (Objects.isNull(giftRule.getExcludeItemMatchType())) {
            giftRule.setExcludeItemMatchType(RuleItemMatchTypeEnum.SKU.getType());
        }

        if (Objects.isNull(giftRule.getExcludeItemAfterGiveMatchType())) {
            giftRule.setExcludeItemAfterGiveMatchType(RuleItemMatchTypeEnum.SKU.getType());
        }

        if (Objects.isNull(giftRule.getMoneyNumMatchType())) {
            giftRule.setMoneyNumMatchType(MoneyNumMatchTypeEnum.AND.getType());
        }

        if (Objects.isNull(giftRule.getMoneyMatchType())) {
            giftRule.setMoneyMatchType(MoneyMatchTypeEnum.MONEY_MATCH_TYPE_TRADE_AMOUNT_PAYABLE.getType());
        }

        if (Objects.isNull(giftRule.getNumMatchType())) {
            giftRule.setNumMatchType(NumMatchTypeEnum.NUM_MATCH_TYPE_SCOPE_TRADE_NUM.getType());
        }

        if (Objects.isNull(giftRule.getMoneyStart())) {
            giftRule.setMoneyStart(0D);
        }

        if (Objects.isNull(giftRule.getMoneyEnd())) {
            giftRule.setMoneyEnd(0D);
        }

        if (Objects.isNull(giftRule.getNumStart())) {
            giftRule.setNumStart(0);
        }

        if (Objects.isNull(giftRule.getNumEnd())) {
            giftRule.setNumEnd(0);
        }

        if (Objects.isNull(giftRule.getNickUpperLimit())) {
            giftRule.setNickUpperLimit(0);
        }

        if (Objects.isNull(giftRule.getTradeUpperLimit())) {
            giftRule.setTradeUpperLimit(0);
        }

        Assert.notNull(giftRule.getGiveType(), "赠送方式不能为空！！！");

        if (Objects.isNull(giftRule.getIsSuiteSingle())) {
            giftRule.setIsSuiteSingle(0);
        }

        if (giftRule.getNumStart() > 0 && giftRule.getNumEnd() > 0) {
            Assert.isTrue(!(giftRule.getNumStart() > giftRule.getNumEnd()), "数量区间左边界不允许大于右边界");
        }

        if (giftRule.getMoneyStart() > 0D && giftRule.getMoneyEnd() > 0D) {
            Assert.isTrue(!(giftRule.getMoneyStart() > giftRule.getMoneyEnd()), "金额区间左边界不允许大于右边界");
        }

        if (Objects.isNull(giftRule.getVersion())) {
            giftRule.setVersion(0L);
        }

        if (Objects.isNull(giftRule.getIsOpen())) {
            giftRule.setIsOpen(1);
        }
        giftRule.setStatus(giftRule.getIsOpen());

        if (GiveTypeEnum.GIVE_TYPE_COPY.getType().equals(giftRule.getGiveType())) {
            boolean match = StringUtils.isNotEmpty(giftRule.getSysItems()) || StringUtils.isNotEmpty(giftRule.getPlatOuterSkuIdItems()) || StringUtils.isNotEmpty(giftRule.getPlatSkuIdItems()) || StringUtils.isNotEmpty(giftRule.getItemTag());
            Assert.isTrue(match, "买A送A时，必须填写【指定参与商品】、【指定平台规格商家编码】、【指定商品标签】中的任意一个");
            match = !RepetitionGiveTypeEnum.REPETITION_GIVE.getType().equals(giftRule.getRepetitionGive()) && !RepetitionGiveTypeEnum.DIPLOID_GIVE.getType().equals(giftRule.getRepetitionGive());
            Assert.isTrue(match, "买A送A时，赠送多组只能选择每xx件送一组");
        }
        if (GiveTypeEnum.GIVE_TYPE_A2B.getType().equals(giftRule.getGiveType())) {
            boolean match = !RepetitionGiveTypeEnum.REPETITION_GIVE.getType().equals(giftRule.getRepetitionGive()) && !RepetitionGiveTypeEnum.DIPLOID_GIVE.getType().equals(giftRule.getRepetitionGive());
            Assert.isTrue(match, "买A送B时，赠送多组只能选择每xx件送一组");
        }

    }

    private boolean hasPermission(Staff staff, String userIds) {
        // -1 代表所有店铺 不用校验
        if (StringUtils.isEmpty(userIds) || "-1".equals(userIds)) {
            return true;
        }
        // 拥有所有店铺权限,无需校验
        if (hasAllShopPermission(staff)) {
            return true;
        }
        TradeConfigNew giftRuleCheckPermission = tradeConfigNewService.get(staff, TradeConfigEnum.GIFT_RULE_CHECK_PERMISSION);
        if (giftRuleCheckPermission.isOpen()) {
            List<Long> originIds = Strings.getAsLongList(userIds, ",", true);
            List<Long> ownIds = staff.getUsers().stream().map(User::getId).collect(Collectors.toList());
            return org.apache.commons.collections.CollectionUtils.isEmpty(originIds.stream().filter(k -> !ownIds.contains(k)).collect(Collectors.toList()));
        }
        return true;
    }

    /**
     * 是否拥有所有店铺权限
     */
    private boolean hasAllShopPermission(Staff staff) {
        StaffDataPrivilege staffDataPrivilege = staffService.queryStaffPrivilege(staff.getId());
        return staffDataPrivilege.getAllUserFlag();
    }

}

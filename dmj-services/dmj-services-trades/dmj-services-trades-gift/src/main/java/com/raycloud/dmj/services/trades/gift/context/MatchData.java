package com.raycloud.dmj.services.trades.gift.context;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.gift.domain.GiftRule;
import com.raycloud.dmj.gift.domain.GiftRuleItem;
import com.raycloud.dmj.services.trades.gift.utils.GiftRuleMatchUtils;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.NumberUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Data
public class MatchData {

    public MatchData() {
    }

    public MatchData(GiftRule giftRule) {
        this.giftRule = giftRule;
    }

    public MatchData(Trade trade, GiftRule giftRule) {
        this.giftRule = giftRule;
        availableOrder = GiftRuleMatchUtils.getOrder(trade, giftRule);
    }

    /**
     * 规则id
     */
    public GiftRule giftRule;

    private List<Order> availableOrder;

    private AtomicInteger MATCH_INDEX = new AtomicInteger(0);

    public int getMatchIndex() {
        return MATCH_INDEX.addAndGet(1);
    }

    /**
     * 原始订单id
     * key: 匹配序号            对应  matchCountGroup  giftItemGroup  matchItemGroup
     * value: 匹配次数
     * 例：
     * 订单T1： A*3 B*2 C*3
     * 规则1： A=2+C,C>2,A+B=2
     * 叠加促销：送三次 A=2+C C>2 A+B=2
     * 组合促销：送两次 A=2+C A+B=2
     * 独立促销：送一次 A=2+C
     */
    private Map<Integer, List<Order>> matchOrderGroup = new ConcurrentHashMap<>();

    /**
     * 匹配次数
     * key: 匹配序号            对应  matchOrderGroup  giftItemGroup  matchItemGroup
     * value: 匹配次数
     * 例：
     * 订单T1：A*3 B*2 C*3
     * 规则1： A=2+C,C>2,A+B=2
     * 叠加促销：送三次 A=2+C C>2 A+B=2
     * 例子 结果就是{1:1, 2:1, 3:1}
     */
    private Map<Integer, Integer> matchCountGroup = new ConcurrentHashMap<>();

    /**
     * 匹配到的赠品商品
     * key: 匹配序号            对应  matchCountGroup  matchOrderGroup  giftItemGroup
     * value: 匹配到的规则中的原始商品
     * 例子 A+B=C,A+D=E+C,....,C+D=F结果就是{1:["A","B"],2:["A","D"],...,n:["C","D"]}
     */
    private Map<Integer, List<GiftRuleItem>> matchItemGroup = new ConcurrentHashMap<>();

    /**
     * 匹配到的赠品商品
     * key: 匹配序号            对应  matchCountGroup  matchOrderGroup  matchItemGroup
     * value: 匹配次数
     * 例子 A+B=C,A+D=E+C,....,C+D=F结果就是{1:["C"],2:["E","C"],...,n:["F"]}
     */
    private Map<Integer, List<GiftRuleItem>> giftItemGroup = new ConcurrentHashMap<>();

    /**
     * 规则未匹配日志
     */
    private List<String> noMatchLog = new CopyOnWriteArrayList<>();

    /**
     * 买A送B匹配到的组
     */
    private List<Integer> buyAGiveBMatchGroup = new CopyOnWriteArrayList<>();

    public boolean isMatch() {
        return !CollectionUtils.isEmpty(giftItemGroup);
    }

    public MatchData buildNoMatchLog(int group, int matchCount, List<Order> originItem, List<GiftRuleItem> giftItems, String message) {
        noMatchLog.add("匹配规则赠品商品第[" + group
                + "]批失败, 规则商品：" + originItem.stream().map(o -> "{商品:" + o.getSysOuterId() + ", 数量:" + o.getNum() + "} ").collect(Collectors.toList())
                + ", 赠品商品：" + giftItems.stream().map(item -> "{商品:" + item.getKeyword() + ", 每次赠送" + item.getNum() + "个, 赠送上限" + item.getNumMax() + "} ").collect(Collectors.toList())
                + ", 匹配次数：[" + matchCount
                + "], 原因：[" + message + "]");
        return this;
    }

    public MatchData buildNoMatchLog(int group, List<GiftRuleItem> giftRuleItems, String message) {
        noMatchLog.add("匹配规则第[" + group
                + "]批失败, 规则商品：" + giftRuleItems.stream().map(o -> "{商品:" + o.getKeyword() + ", 数量" + o.getNum() + "} ").collect(Collectors.toList())
                + ", 原因：[" + message + "]");
        return this;
    }

    public MatchData buildNoMatchLog(int group, List<Order> orders, GiftMatchContext context, String message) {
        noMatchLog.add("匹配规则第[" + group
                + "]批失败, 订单商品：" + orders.stream().map(o -> "{商品:" + o.getSysOuterId() + ", 数量:" + o.getNum() + ", 已使用数:" + context.getUseNum(o.getId()) + "} ").collect(Collectors.toList())
                + ", 原因：[" + message + "]");
        return this;
    }

    public MatchData buildNoMatchLog(Integer group, String message) {
        noMatchLog.add("匹配规则第[" + group + "]批失败, 原因：[" + message + "]");
        return this;
    }

    public MatchData buildNoMatchLog(List<Order> orders, String message) {
        noMatchLog.add("订单商品：" + orders.stream().map(order -> "{商品:" + order.getSysOuterId() + ", 数量:" + order.getNum() + ", 应付金额:" + order.getTotalFee() + ", 销售价:" + order.getPrice() + "} ").collect(Collectors.toList())
                + "总计：{数量[" + orders.stream().mapToDouble(Order::getNum).sum()
                + "], 应付金额[" + orders.stream().mapToDouble(order -> NumberUtils.parseNumber(Objects.isNull(order.getTotalFee()) ? "0" : order.getTotalFee(), Double.class)).sum()
                + "], 原因：[" + message + "]");
        return this;
    }

    public MatchData buildNoMatchLog(Order order, String message) {
        noMatchLog.add("订单商品:" + order.getSysOuterId() + ", 数量:" + order.getNum() + ", 应付金额:" + order.getTotalFee() + ", 销售价:" + order.getPrice() + ", "
                + ", 原因：[" + message + "]");
        return this;
    }

    /**
     * 编辑规则未匹配日志
     */
    public MatchData buildNoMatchLog(List<String> ruleValues, String tradeValue, String message) {
        noMatchLog.add("规则条件:[" + String.join(",", ruleValues) + "], 订单值:[" + tradeValue + "], message：[" + message + "]");
        return this;
    }

    /**
     * 编辑规则未匹配日志
     */
    public MatchData buildNoMatchLog(String message) {
        noMatchLog.add("原因：[" + message + "]");
        return this;
    }

    public void addMatchCountGroup(Integer group, Integer count) {
        this.matchCountGroup.put(group, count);
    }

    public void addMatchOrderGroup(Integer group, Order order) {
        this.matchOrderGroup.computeIfAbsent(group, v -> new CopyOnWriteArrayList<>()).add(order);
    }

    public void addMatchOrderGroup(Integer group, List<Order> orders) {
        this.matchOrderGroup.computeIfAbsent(group, v -> new CopyOnWriteArrayList<>()).addAll(orders);
    }

    public void addGiftItemGroup(Integer group, GiftRuleItem giftItem) {
        this.giftItemGroup.computeIfAbsent(group, v -> new CopyOnWriteArrayList<>()).add(giftItem);
    }

    public void addGiftItemGroup(Integer group, List<GiftRuleItem> giftItems) {
        this.giftItemGroup.computeIfAbsent(group, v -> new CopyOnWriteArrayList<>()).addAll(giftItems);
    }

    public void addMatchItemGroup(Integer group, GiftRuleItem giftItem) {
        this.matchItemGroup.computeIfAbsent(group, v -> new CopyOnWriteArrayList<>()).add(giftItem);
    }

    public void addMatchItemGroup(Integer group, List<GiftRuleItem> giftItems) {
        this.matchItemGroup.computeIfAbsent(group, v -> new CopyOnWriteArrayList<>()).addAll(giftItems);
    }

    public void clear() {
        this.MATCH_INDEX.set(0);
        this.matchOrderGroup.clear();
        this.matchCountGroup.clear();
        this.giftItemGroup.clear();
        this.matchItemGroup.clear();
    }
}

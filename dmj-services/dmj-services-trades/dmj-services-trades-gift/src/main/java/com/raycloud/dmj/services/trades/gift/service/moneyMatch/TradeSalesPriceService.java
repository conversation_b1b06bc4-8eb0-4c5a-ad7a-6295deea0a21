package com.raycloud.dmj.services.trades.gift.service.moneyMatch;

import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.gift.domain.GiftRuleItem;
import com.raycloud.dmj.gift.enums.MoneyMatchTypeEnum;
import com.raycloud.dmj.services.trades.gift.context.GiftMatchContext;
import com.raycloud.dmj.services.trades.gift.context.MatchData;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单商品销售价满足金额范围（销售单价，不含优惠、税费、远费，且默认排除交易关闭的商品)
 */
@Service("tradeSalesPriceService")
public class TradeSalesPriceService extends AGiftRuleMoneyMatchDefaultService {
    @Override
    public MoneyMatchTypeEnum getType() {
        return MoneyMatchTypeEnum.MONEY_MATCH_TYPE_TRADE_SALES_PRICE;
    }

    @Override
    public boolean giftRuleMoneyMatch(List<GiftRuleItem> giftRuleItems, List<Order> participationOrder, MatchData matchData, GiftMatchContext context) {
        return super.giftRuleMoneySumTradeMatch(participationOrder, matchData, context);
    }

    @Override
    public double getCompareValue(Order order) {
        return order.getPriceDouble();
    }

    @Override
    public double getOrderMoney(Order order) {
        return order.getPriceDouble();
    }
}

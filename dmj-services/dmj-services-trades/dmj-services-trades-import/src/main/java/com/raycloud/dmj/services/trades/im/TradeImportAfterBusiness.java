package com.raycloud.dmj.services.trades.im;

import com.alibaba.fastjson.*;
import com.google.api.client.util.Sets;
import com.google.common.collect.*;
import com.raycloud.dmj.*;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.except.TradePlatModifyItemNumExceptionBusiness;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.gift.GiftDeductCountBusiness;
import com.raycloud.dmj.business.logistics.*;
import com.raycloud.dmj.business.modify.TradeAddBusiness;
import com.raycloud.dmj.business.operate.*;
import com.raycloud.dmj.business.rematch.business.ReMatchBusiness;
import com.raycloud.dmj.business.split.*;
import com.raycloud.dmj.business.split.warehouse.ai.help.WarehouseAiClient;
import com.raycloud.dmj.business.stock.TradeConfigStockBusiness;
import com.raycloud.dmj.business.tag.TradeTagBusiness;
import com.raycloud.dmj.business.trade.TradeFilterBusiness;
import com.raycloud.dmj.dao.trade.*;
import com.raycloud.dmj.dms.domain.dto.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.*;
import com.raycloud.dmj.domain.contants.BaseConstants;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.FxGlobalConfig;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.fx.Constants;
import com.raycloud.dmj.domain.item.ItemActiveStatusDto;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.trade.config.*;
import com.raycloud.dmj.domain.trade.item.*;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.fx.refreshPrice.TradeRefreshParams;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.params.UpdateOutsidPoolParams;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.utils.UserUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.ec.except.*;
import com.raycloud.dmj.services.ec.fx.FxTradeDownloadNotifyListener;
import com.raycloud.dmj.services.ec.trace.TrackingBatchOrderListener;
import com.raycloud.dmj.services.ec.wave.TradeWaveChangeListener;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.trade.audit.ITradeAuditService;
import com.raycloud.dmj.services.trade.audit.auto.AuditAutoBusiness;
import com.raycloud.dmj.services.trade.item.entity.EntityCodeConvertBusiness;
import com.raycloud.dmj.services.trade.notify.*;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trade.warehouse.ITradeWarehouseMatchService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.im.business.LogisticsChangeBusiness;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.utils.UpdateOutsidPoolUItils;
import com.raycloud.ec.api.*;
import org.apache.commons.collections.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.enums.OpEnum.*;
import static com.raycloud.dmj.domain.rematch.enums.EventEnum.*;

/**
 * @description: 订单同步后处理
 * @author: 贾凯乐
 * @date: 2021-09-18 16:24
 **/
@Service
public class TradeImportAfterBusiness {
    @Resource
    IEventCenter eventCenter;
    @Resource
    IUserService userService;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    SplitRefundAutoBusiness splitRefundAutoBusiness;
    @Resource
    FxTradeDownloadNotifyListener fxTradeDownloadNotifyListener;
    @Resource
    IUserWlbExpressTemplateService userWlbExpressTemplateService;
    @Resource
    TbTradeDao tbTradeDao;

    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;

    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    TrackingBatchOrderListener trackingBatchOrderListener;
    @Resource
    ITradeWaveService tradeWaveService;
    @Resource
    LogisticsChangeBusiness logisticsChangeBusiness;
    @Resource
    MoneyChangeBusiness moneyChangeBusiness;
    @Resource
    LogisticsWarningBusiness logisticsWarningBusiness;
    @Resource
    TradeExtDao tradeExtDao;
    @Resource
    ITradeAuditService tradeAuditService;

    @Resource
    WarehouseAiClient warehouseAiClient;

    @Resource
    FxBusiness fxBusiness;

    @Resource
    EntityCodeConvertBusiness entityCodeConvertBusiness;

    @Resource
    ReMatchBusiness reMatchBusiness;

    @Resource
    TradeAddBusiness tradeAddBusiness;

    @Resource
    InfoChangeLogBusiness infoChangeLogBusiness;

    @Resource
    AutoSplitBusiness autoSplitBusiness;

    @Resource
    AuditAutoBusiness auditAutoBusiness;
    @Resource
    TradeFilterBusiness tradeFilterBusiness;

    @Resource
    UploadAheadBusiness uploadAheadBusiness;

    @Resource
    ILockService lockService;

    @Resource
    TradeLockBusiness tradeLockBusiness;


    @Resource
    ITradeWarehouseMatchService tradeWarehouseMatchService;

    @Resource
    TradeTagBusiness tradeTagBusiness;

    @Resource
    GiftDeductCountBusiness giftDeductCountBusiness;

    @Resource
    TradeNotifyScanLock tradeNotifyScanLock;

    @Resource
    TradePlatModifyItemNumExceptionBusiness tradePlatModifyItemNumExceptionBusiness;
    @Resource
    SplitOtherERPConsignRuleBusiness splitOtherERPConsignRuleBusiness;

    /**
     * 需要同步处理的订单同步after业务
     */
    protected void importAfter(User user, TradeImportData tradeImportData, TradeImportResult result, List<Trade> originCurrentTrades) {
        long start = System.currentTimeMillis();
        Staff staff = user.getStaff();
        //淘宝店铺脱敏之后设置一个标识
        checkUserOpenDesensitization(user, originCurrentTrades);
        // 运单号变化保存，放在拆单之前
        long temp = System.currentTimeMillis();
        infoChangeLogBusiness.batchInsertOutSidLog(staff, result.outSidsInfoChangeLogs);
        long outSidLogTook = System.currentTimeMillis() - temp;
        //地址修改
        if (!tradeImportData.addressChangeTrades.isEmpty()) {
            eventCenter.fireEvent(this, new EventInfo("wlb.waybillCode.update").setArgs(new Object[]{staff, TradeConvertUtils.buildSimpleTrades(tradeImportData.addressChangeTrades)}), null);
        }
        // 交易关闭的order 通知采购
        closeOrderNoticeCaiGou(user, result);

        // 交易关闭的order 归还赠品赠送数
        closeOrderDeleteGiftMatchLog(user, tradeImportData);

        //如果开启了订单存在指定异常时重新审核的配置，则需要筛选出符合配置的订单进行重新审核
        unautidForExcep(user, tradeImportData);
        if (!result.opEnumAuditUndoSidMap.isEmpty()) {
            tradeImportData.opEnumAuditUndoSidMap.putAll(result.opEnumAuditUndoSidMap);
        }
        //自动反审核
        temp = System.currentTimeMillis();
        if (!tradeImportData.opEnumAuditUndoSidMap.isEmpty()) {
            List<Trade> unAuditTrades = new ArrayList<>();
            for (Map.Entry<OpEnum, Set<Long>> entry : tradeImportData.opEnumAuditUndoSidMap.entrySet()) {
                List<Trade> updateTrades = tradeAuditService.unaudit(staff, entry.getKey(), entry.getValue().toArray(new Long[0])).getSuccessTrades();
                if (CollectionUtils.isNotEmpty(updateTrades)) {
                    unAuditTrades.addAll(updateTrades);
                }
            }
            if (CollectionUtils.isNotEmpty(unAuditTrades) && tradeImportData.reconsumeRemoveWaveSids != null) {
                Set<Long> sids = unAuditTrades.stream().map(Trade::getSid).collect(Collectors.toSet());
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("补偿踢出波次:记录反审核数据，sid：%s", sids)));
                tradeImportData.reconsumeRemoveWaveSids.addAll(sids);
            }
        }
        long unauditTook = System.currentTimeMillis() - temp;

        //退款自动拆单
        temp = System.currentTimeMillis();
        splitRefundAutoBusiness.autoRefundSplit(staff, tradeImportData);
        long refundSplitTook = System.currentTimeMillis() - temp;

        // 订单部分其他erp发货 查询是否有其他erp发货规则 做拆分
        splitOtherERPConsignRuleBusiness.autoSplitAfterOrderSynchronization(staff, result);

        //重新匹配仓库
        temp = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(result.needRematchWarehouseSidSet)) {
            tradeWarehouseMatchService.matchRedo(user.getStaff(), result.needRematchWarehouseSidSet.toArray(new Long[0]), EVENT_TRADE_CHANGE_SELLER_MEMO_FLAG);
        }
        long rematchWarehouseTook = System.currentTimeMillis() - temp;

        //重新匹配快递
        temp = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(result.needRematchExpressSidSet)) {
            reMatchBusiness.reMatch(staff, new ArrayList<>(result.needRematchExpressSidSet), EVENT_TRADE_CHANGE_SELLER_MEMO_FLAG, Boolean.FALSE);
        }
        long rematchExpressTook = System.currentTimeMillis() - temp;

        //重新匹配标签
        temp = System.currentTimeMillis();
        tradeImportData.sids2SysRecalculationTag.addAll(result.needRematchTagSidSet);
        tradeTagBusiness.tagReculation(user.getStaff(), tradeImportData.sids2SysRecalculationTag, 0, 2, true);
        long rematchTradeLabelTook = System.currentTimeMillis() - temp;

        if (!tradeImportData.refundChangeSids.isEmpty()) {
            eventCenter.fireEvent(this, new EventInfo(TradeConfigStockBusiness.EVENT_NAME).setArgs(new Object[]{user.getStaff(), tradeImportData.refundChangeSids}), null);
        }
        if (!tradeImportData.needDeleteOrders.isEmpty()) {
            Map<Long, Long> orderIdMap = tradeImportData.needDeleteOrders.stream().collect(Collectors.toMap(OrderBase::getId, OrderBase::getSid, (k1, k2) -> k2));
            eventCenter.fireEvent(this, new EventInfo("trade.delete.item").setArgs(new Object[]{user.getStaff(), orderIdMap}), null);
        }

        if (CollectionUtils.isNotEmpty(result.itemShelfOffOrders)) {
            List<Long> orderIds = OrderUtils.toIdList(result.itemShelfOffOrders);
            Logs.ifDebug(LogHelper.buildLog(user.getStaff(), String.format("发送商品下架事件,orderIds=%s", orderIds)));
            eventCenter.fireEvent(this, new EventInfo(BaseConstants.EVENT_TRADE_UNICODE_OFF_SHELF).setArgs(new Object[]{user.getStaff(), new HashSet<>(orderIds)}), null);
            // 上传商品下架的备注
            eventCenter.fireEvent(this, new EventInfo(BaseConstants.EVENT_TRADE_ITEM_OFF_SHELF).setArgs(new Object[]{user.getStaff(), new HashSet<>(orderIds)}), null);
        }
        checkTradePartClose(user, tradeImportData.updateAllTrades, result);

        if (CollectionUtils.isNotEmpty(result.needRematchGiftSidSet)) {
            eventCenter.fireEvent(this, new EventInfo("trade.close.gift.rematch").setArgs(new Object[]{user.getStaff(), result.needRematchGiftSidSet, 1}), null);
        }

        if (CollectionUtils.isNotEmpty(result.addressChangeNeedRematchSids)) {
            reMatchBusiness.reMatch(user.getStaff(), new ArrayList<>(result.addressChangeNeedRematchSids), EventEnum.EVENT_TRADE_CHANGE_SHIPPING_ADDRESS, Boolean.TRUE);
        }

        temp = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(result.logisticsChangeSids)) {
            logisticsChangeBusiness.pddDesignatedLogisticsRematchExpress(user.getStaff(), result.logisticsChangeSids);
        }
        long pddRematchExpressTook = System.currentTimeMillis() - temp;

        temp = System.currentTimeMillis();
        if (user.getSource().equals(CommonConstants.PLAT_FORM_TYPE_PDD)) {
            TradeConfig tradeConfig = tradeConfigService.get(user.getStaff());
            logisticsChangeBusiness.handlePddChangeLogisticsExpressForTrace(user.getStaff(), originCurrentTrades, tradeConfig, result);
        }
        long pddExpressForTraceTook = System.currentTimeMillis() - temp;

        if (CollectionUtils.isNotEmpty(result.platformUpdateItemRematchGiftSidSet) || CollectionUtils.isNotEmpty(tradeImportData.platformUpdateItemRematchGiftSidSet)) {
            TradeQueryParams tradeQueryParams = new TradeQueryParams();
            result.platformUpdateItemRematchGiftSidSet.addAll(tradeImportData.platformUpdateItemRematchGiftSidSet);
            tradeQueryParams.setSid(result.platformUpdateItemRematchGiftSidSet.toArray(new Long[0]));
            eventCenter.fireEvent(this, new EventInfo("trade.gift.match.manual").setArgs(new Object[]{user.getStaff(), tradeQueryParams, 1}), null);
        }
        if (CollectionUtils.isNotEmpty(tradeImportData.waitPayToWaitAuditMatchWarehouseSids)) {
            TradeControllerParams params = new TradeControllerParams();
            params.setSid(Strings.join(",", tradeImportData.waitPayToWaitAuditMatchWarehouseSids));
            eventCenter.fireEvent(this, new EventInfo("trade.split.warehouse.stock.sync").setArgs(new Object[]{user.getStaff(), params}), null);
        }

        temp = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(tradeImportData.insertTrades)) {
            Map<String, Long> fxSid2GxSidMap = tradeImportData.insertTrades.stream().filter(TradeUtils::isGxTrade).collect(Collectors.toMap(Trade::getTid, Trade::getSid, (a, b) -> a));
            fxBusiness.consumeDelaySyncCache(user.getStaff(), fxSid2GxSidMap);
        }
        long fxconsumeTook = System.currentTimeMillis() - temp;

        if (CollectionUtils.isNotEmpty(tradeImportData.insertTrades)) {
            //商品未匹配 && 强推单 && （供销单 || 供销且分销 || 奇门供销单）
            List<Long> needBindGxSidList = tradeImportData.insertTrades.stream()
                    .filter(t -> (TradeUtils.isGxOrMixTrade(t) || TradeUtils.isQimenFxSource(t)) && t.getIfFxForcePushTrade())
                    .filter(t -> CollectionUtils.isNotEmpty(TradeUtils.getOrders4Trade(t)))
                    .filter(t -> TradeUtils.getOrders4Trade(t).stream().anyMatch(o -> o.getItemSysId() == null || o.getItemSysId() <= 0))
                    .map(Trade::getSid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needBindGxSidList)) {
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_GX_IMPORT_FORCEPUSH).setArgs(new Object[]{Optional.ofNullable(user.getStaff()).map(Staff::getId).orElse(null), null, needBindGxSidList}), null);
            }
        }

        if (CollectionUtils.isNotEmpty(tradeImportData.updateTrades)) {
            List<Trade> gxCancelTrades = tradeImportData.updateTrades.stream().filter(t -> TradeUtils.isGxOrMixTrade(t) && t.hasOpV(OpVEnum.TRADE_GX_EDIT_ITEM_PART_REFUND)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(gxCancelTrades)) {
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_GX_CANCEL).setArgs(new Object[]{user.getStaff(), TradeUtils.toSidList(gxCancelTrades), "供销执行搭配规则，分销发生退款时，找不到对应的商品，所以供销单作废，重新生成供销订单，避免发错货"}), null);
            }
        }
        if (CollectionUtils.isNotEmpty(result.recyclingOutSidTrades) || CollectionUtils.isNotEmpty(result.changeStatusCancelOutSidTrades)) {
            Set<Trade> cancelOutSidTrades = new HashSet<>(result.recyclingOutSidTrades.size() + result.changeStatusCancelOutSidTrades.size());
            List<Trade> recyclingOutSidTrades = tradeFilterBusiness.filterSubOuterSid(user.getStaff(), result.recyclingOutSidTrades);
            cancelOutSidTrades.addAll(recyclingOutSidTrades);
//            cancelOutSidTrades.addAll(result.changeStatusCancelOutSidTrades);
            eventCenter.fireEvent(this, new EventInfo("wlb.waybillCode.cancel").setArgs(new Object[]{user.getStaff(), new ArrayList<>(cancelOutSidTrades), "交易关闭"}), null);
        }

        if (CollectionUtils.isNotEmpty(tradeImportData.insertTrades)) {
            // 供销 && 强推 && 有商品已匹配的订单需要回传分销价和运费给对应的分销订单
            List<Trade> gxForcePushInsertTrades = tradeImportData.insertTrades.stream()
                    .filter(t -> BooleanUtils.isTrue(t.getIfFxForcePushTrade()))
                    .filter(TradeUtils::isGxOrMixTrade)
                    .filter(t -> CollectionUtils.isNotEmpty(TradeUtils.getOrders4Trade(t)))
                    .filter(t -> TradeUtils.getOrders4Trade(t).stream().anyMatch(o -> o.getItemSysId() != null & o.getItemSysId() > 0))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(gxForcePushInsertTrades)) {
                eventCenter.fireEvent(this, new EventInfo("trade.fx.sync.price").setArgs(new Object[]{user.getStaff(), TradeUtils.toSidList(gxForcePushInsertTrades), true, true}), null);
            }
        }

        if (CollectionUtils.isNotEmpty(tradeImportData.insertTrades) || CollectionUtils.isNotEmpty(tradeImportData.updateTrades)) {
            List<Trade> shopeeInsertTrades = tradeImportData.insertTrades.stream().filter(x -> CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(x.getSource()) && org.apache.commons.lang3.StringUtils.isNotBlank(x.getOutSid())).collect(Collectors.toList());
            List<Trade> shopeeUpdateTrades = tradeImportData.updateTrades.stream().filter(x -> CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(x.getSource()) && org.apache.commons.lang3.StringUtils.isNotBlank(x.getOutSid())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(shopeeInsertTrades) || CollectionUtils.isNotEmpty(shopeeUpdateTrades)) {
                eventCenter.fireEvent(this, new EventInfo("shopee.insert.outsid.pool").setArgs(new Object[]{user.getStaff(), shopeeInsertTrades, shopeeUpdateTrades}), null);
            }

            TradeConfig tradeConfig = tradeImportData.tradeConfig;
            if (tradeConfig.allowJitxFeedbackPlatformWarehouse()) {
                // 唯品会jitx 订单寻仓处理
                List<Trade> jitxTrades = tradeImportData.insertTrades.stream().filter(t -> CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(t.getSource()) && CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(t.getSubSource()) &&
                        "vipjitx_NEW".equals(t.getStatus())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(jitxTrades)) {
                    eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_JITX_FEEDBACK_PLATFORM_WAREHOUSE).setArgs(new Object[]{user.getStaff(), TradeUtils.toSids(jitxTrades)}), null);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(tradeImportData.shopeeSyncTids)) {
            eventCenter.fireEvent(this, new EventInfo("shopee.trade.split").setArgs(new Object[]{user, tradeImportData.shopeeSyncTids, tradeImportData.shopeeSplitTids}), null);
        }
        if (MapUtils.isNotEmpty(result.platStatusChangetidMap)) {
            eventCenter.fireEvent(this, new EventInfo(TradePlatStatusChangeListener.EVENT_NAME).setArgs(new Object[]{user.getStaff(), new HashMap<>(result.platStatusChangetidMap)}), null);
        }

        // 商品停用订单，发送商品停用事件，进行停用拆单、标记等。
        if (CollectionUtils.isNotEmpty(result.itemInactiveOrders)) {
            List<ItemActiveStatusDto.ItemAndSkuId> itemList = Lists.newArrayList();
            Set<Long> itemInactiveSidsSet = Sets.newHashSet();
            Map<Long, Set<Long>> sysItemAndSkuSetMap = Maps.newHashMap();
            for (Order itemInactiveOrder : result.itemInactiveOrders) {
                Long sysItemId = itemInactiveOrder.getItemSysId();
                Set<Long> skuSet = sysItemAndSkuSetMap.get(sysItemId);
                if (Objects.isNull(skuSet)) {
                    skuSet = new HashSet<>();
                    sysItemAndSkuSetMap.put(sysItemId, skuSet);
                }
                skuSet.add(itemInactiveOrder.getSkuSysId());
                itemInactiveSidsSet.add(itemInactiveOrder.getSid());
            }

            if (CollectionUtils.isNotEmpty(sysItemAndSkuSetMap.keySet())) {
                sysItemAndSkuSetMap.forEach((sysItemId, skuSet) -> {
                    ItemActiveStatusDto.ItemAndSkuId itemAndSkuId = new ItemActiveStatusDto.ItemAndSkuId(sysItemId, "", Lists.newArrayList(skuSet), Lists.newArrayList());
                    itemList.add(itemAndSkuId);
                });
            }

            ItemActiveStatusDto itemActiveStatusDto = new ItemActiveStatusDto();
            itemActiveStatusDto.setStaff(user.getStaff());
            itemActiveStatusDto.setItems(itemList);
            eventCenter.fireEvent(this, new EventInfo("trade.items.inactive.after.import").setArgs(new Object[]{itemActiveStatusDto, new ArrayList<>(itemInactiveSidsSet)}), false);
        }

        //lazada FBL和二次销售的子订单需要拆分出去
        if (CollectionUtils.isNotEmpty(tradeImportData.trades) && CommonConstants.PLAT_FORM_TYPE_LAZADA.equals(user.getSource())) {
            Set<String> splitTid = new HashSet<>();
            tradeImportData.trades.forEach(trade -> {
                boolean anyMatch = TradeUtils.getOrders4Trade(trade).stream().anyMatch(OrderUtils::hasFblOrder);
                //包含fbl商品的订单需要自动拆分
                if (anyMatch) {
                    splitTid.add(trade.getTid());
                }
            });
            eventCenter.fireEvent(this, new EventInfo("lazada.trade.items.fbl.split").setArgs(new Object[]{user.getStaff(), splitTid}), null);
        }

        // 订单同步按库存智能分仓
        warehouseAiClient.warehouseAiProcessSync(user.getStaff(), tradeImportData.tradeConfig, TradeUtils.toSidList(tradeImportData.insertTrades), TRADE_SYNC);

        //平台信息修改重新匹配赠品
        Set<Long> changeSellerMemoFlagNeedRematch = new HashSet<>();
        if (CollectionUtils.isNotEmpty(result.event2needRematchSidSet.get(EVENT_TRADE_CHANGE_SELLER_MEMO_FLAG))) {
            changeSellerMemoFlagNeedRematch.addAll(result.event2needRematchSidSet.get(EVENT_TRADE_CHANGE_SELLER_MEMO_FLAG));
        }
        if (CollectionUtils.isNotEmpty(result.needRematchWarehouseSidNormalSet)) {
            changeSellerMemoFlagNeedRematch.addAll(result.needRematchWarehouseSidNormalSet);
            changeSellerMemoFlagNeedRematch.addAll(result.needRematchExpressNormalSet);
        }
        //更新订单修改备注后重算
        if (CollectionUtils.isNotEmpty(tradeImportData.updateAllTrades)) {
            List<Trade> updateSellerMemoTrades = tradeImportData.updateAllTrades.stream().filter(TradeUtils::isUpdateSellerMemo).collect(Collectors.toList());
            // 平台单拆分出来的手工单，平台改备注后也需要去重算快递
            if (CollectionUtils.isNotEmpty(tradeImportData.split2RematchTrades)) {
                updateSellerMemoTrades.addAll(tradeImportData.split2RematchTrades.stream().filter(TradeUtils::isUpdateSellerMemo).collect(Collectors.toList()));
            }
            changeSellerMemoFlagNeedRematch.addAll(TradeUtils.toSidList(updateSellerMemoTrades));
        }
        if (CollectionUtils.isNotEmpty(tradeImportData.needAutoReMatchGiftSids)) {
            changeSellerMemoFlagNeedRematch.addAll(tradeImportData.needAutoReMatchGiftSids);
        }

        // 不在重算备注旗帜，只是订单同步链路需要处理的都可以用这个。
        if (MapUtils.isNotEmpty(result.rematchBusinessEnumListMap)) {
            Set<Long> rematchSids = result.rematchBusinessEnumListMap.get(EventEnum.EVENT_TRADE_CHANGE_SELLER_MEMO_FLAG);
            if (CollectionUtils.isNotEmpty(rematchSids)) {
                changeSellerMemoFlagNeedRematch.addAll(rematchSids);
            }
            Set<Long> rematchSyncSids = result.rematchBusinessEnumListMap.get(EventEnum.EVENT_TRADE_SYNC);
            if (CollectionUtils.isNotEmpty(rematchSyncSids)) {
                Set<Long> needSyncRematchSids = rematchSyncSids.stream().filter(sid -> !changeSellerMemoFlagNeedRematch.contains(sid)).collect(Collectors.toSet());
                reMatchBusiness.reMatch(user.getStaff(), new ArrayList<>(needSyncRematchSids), EventEnum.EVENT_TRADE_SYNC, Boolean.TRUE);
            }
        }

        temp = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(result.event2needRematchSidSet.get(EVENT_TRADE_BE_WAIT_AUDIT))) {
            tradeWarehouseMatchService.matchRedo(user.getStaff(), result.event2needRematchSidSet.get(EVENT_TRADE_BE_WAIT_AUDIT).toArray(new Long[0]), EVENT_TRADE_BE_WAIT_AUDIT);
        }
        long toWaitAuditMatchWarehouseTook = System.currentTimeMillis() - temp;

        temp = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(result.event2needRematchSidSet.get(EVENT_TRADE_CANCEL_RISK))) {
            tradeWarehouseMatchService.matchRedo(user.getStaff(), result.event2needRematchSidSet.get(EVENT_TRADE_CANCEL_RISK).toArray(new Long[0]), EVENT_TRADE_CANCEL_RISK);
        }
        long cancelRiskMatchWarehouseTook = System.currentTimeMillis() - temp;

        if (CollectionUtils.isNotEmpty(changeSellerMemoFlagNeedRematch)) {
            reMatchBusiness.reMatch(user.getStaff(), new ArrayList<>(changeSellerMemoFlagNeedRematch), EVENT_TRADE_CHANGE_SELLER_MEMO_FLAG, TRADE_SYNC, Boolean.TRUE);
        }

        if (CollectionUtils.isNotEmpty(result.event2needRematchSidSet.get(EVENT_TRADE_SPLIT_AFTER))) {
            reMatchBusiness.reMatch(user.getStaff(), new ArrayList<>(result.event2needRematchSidSet.get(EVENT_TRADE_SPLIT_AFTER)), EVENT_TRADE_SPLIT_AFTER, TRADE_SYNC, Boolean.TRUE);
        }

        //订单同步修改地址重新匹配赠品仓库快递
        if (CollectionUtils.isNotEmpty(tradeImportData.waitAuditAddressChangeSids)) {
            eventCenter.fireEvent(this, new EventInfo("trade.updateShippingAddress.after").setArgs(new Object[]{user.getStaff()}), new ArrayList<>(tradeImportData.waitAuditAddressChangeSids));
        }
        // 物流预警分析同步订单关闭状态
        if (CollectionUtils.isNotEmpty(tradeImportData.updateAllTrades)) {
            logisticsWarningBusiness.syncTradeClosedStatus(user.getStaff(), tradeImportData.updateAllTrades);
        }
        // 订单交易关闭通知wms
        if (CollectionUtils.isNotEmpty(tradeImportData.updateAllTrades)) {
            // 非交易关闭变成交易关闭
            List<Long> closeSids = tradeImportData.updateAllTrades.stream()
                    .filter(t -> Objects.equals(t.getSysStatus(), Trade.SYS_STATUS_CLOSED)
                                    && Objects.nonNull(t.getOrigin())
                                    && !Objects.equals(t.getOrigin().getSysStatus(), Trade.SYS_STATUS_CLOSED))
                    .map(TradeBase::getSid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(closeSids)) {
                eventCenter.fireEvent(this, new EventInfo("wms.trade.close").setArgs(new Object[] {staff, closeSids}), null);
            }
        }

        temp = System.currentTimeMillis();
        List<Trade> allTrades = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tradeImportData.insertTrades) || CollectionUtils.isNotEmpty(tradeImportData.updateTrades)) {
            if (CollectionUtils.isNotEmpty(tradeImportData.insertTrades)) {
                allTrades.addAll(tradeImportData.insertTrades);
            }
            if (CollectionUtils.isNotEmpty(tradeImportData.updateTrades)) {
                allTrades.addAll(tradeImportData.updateTrades);
            }
            if (CollectionUtils.isNotEmpty(tradeImportData.insertTrades)) {
                // 供销换货/补发单触发售后事件
                List<Trade> reissueOrChangeNeedNotifyAsTrades = tradeImportData.insertTrades.stream().filter(trade -> TradeUtils.isReissueOrChangeitem(trade) && (TradeUtils.isGxOrMixTrade(trade) || TradeUtils.isQimenFxSource(trade))).collect(Collectors.toList());

                // 带上原始的workOrderId
                Map<Long, Long> workOrderIds = new HashMap<>();
                for (Trade gxOrMix : reissueOrChangeNeedNotifyAsTrades) {
                    getWorkOrderId(gxOrMix, workOrderIds);
                }
                // 补发换货 && 分销 && 待审核 && 奇门 && 助手
                List<Trade> tradesFxOrMix;
                if (UserUtils.isKdzs(user)) {
                    tradesFxOrMix = tradeImportData.insertTrades.stream().filter(trade -> TradeUtils.isReissueOrChangeitem(trade) && TradeUtils.isQimenFxSource(trade) && TradeStatusUtils.isWaitAudit(trade.getSysStatus()) && TradeUtils.isFxOrMixTrade(trade)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(tradesFxOrMix)) {
                        List<Long> list = tradesFxOrMix.stream().filter(x -> x.getTradeExt() == null || x.getTradeExt().getExtraFields() == null || !x.getTradeExt().getExtraFields().contains("workOrderId")).map(TradeBase::getSid).collect(Collectors.toList());
                        Map<Long, TradeExt> tradeExtMap = new HashMap<>();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<TradeExt> tradeExts = tradeExtDao.tradeExtsGetBySids(user.getStaff(), list);
                            if (CollectionUtils.isNotEmpty(tradeExts)) {
                                tradeExtMap = tradeExts.stream().collect(Collectors.toMap(TradeExt::getSid, x -> x));
                            }
                        }

                        Map<Long, TradeExt> finalTradeExtMap = tradeExtMap;
                        tradesFxOrMix.forEach(t -> {
                            if (t.getTradeExt() == null) {
                                t.setTradeExt(finalTradeExtMap.get(t.getSid()));
                            }

                            if (!getWorkOrderId(t, workOrderIds)) {
                                Logs.ifDebug(new FxLogBuilder(user.getStaff(), FxLogBuilder.ROLE_FX).appendTrade(t).append("快递助手奇门补发订单 workOrderId 为空 TradeExt:").append(t.getTradeExt() == null ? null : JSON.toJSONString(t.getTradeExt())).toString());
                            }

                            t.addOpV(OpVEnum.TRADE_NEED_AUTO_AUDIT);
                            t.addOpV(OpVEnum.TRADE_NOT_NEED_MATCH_DMS_ATTR);
                            tradeAddBusiness.afterSaveTrade(user.getStaff(), t);
                        });

                        for (Trade fxOrMix : tradesFxOrMix) {
                            if (!reissueOrChangeNeedNotifyAsTrades.contains(fxOrMix)) {
                                reissueOrChangeNeedNotifyAsTrades.add(fxOrMix);
                            }
                        }
                    }
                }
                if (!reissueOrChangeNeedNotifyAsTrades.isEmpty()) {
                    Logs.ifDebug(LogHelper.buildLog(user.getStaff(), String.format("供销换货/补发单触发售后事件，sids:%s", TradeUtils.toSidList(reissueOrChangeNeedNotifyAsTrades))));
                    List<Trade> simpleTrades = TradeUtils.buildSimpleTrade(reissueOrChangeNeedNotifyAsTrades);
                    Map<Trade, Long> tradeMap = new HashMap<>();
                    for (Trade simpleTrade : simpleTrades) {
                        tradeMap.put(simpleTrade, workOrderIds.get(simpleTrade.getSid()));
                    }
                    eventCenter.fireEvent(this, new EventInfo("as.gx.trade.reissue.created.v2").setArgs(new Object[]{user.getStaff(), tradeMap}), null);
                }
            }
        }
        long gxTook = System.currentTimeMillis() - temp;
        // 实体编码换商品支持自动换
        if (CollectionUtils.isNotEmpty(allTrades)) {
            if (entityCodeConvertBusiness.canConvertAuto(user.getStaff()) && !allTrades.isEmpty()) {
                Set<String> tagIdSet = entityCodeConvertBusiness.canConvertAutoTagIdSet(user.getStaff());
                List<Trade> trades = entityCodeConvertBusiness.tradeTagInSet(allTrades, tagIdSet);
                if (CollectionUtils.isNotEmpty(trades)) {
                    TradeControllerParams params = new TradeControllerParams();
                    List<Long> list = TradeUtils.toSidList(trades);
                    params.setSids(org.apache.commons.lang3.StringUtils.join(list, ","));
                    EntityCodeConvertParams suitConvertParams = new EntityCodeConvertParams();
                    suitConvertParams.setSuitsConvertType(1);
                    suitConvertParams.setAllowableStockOut(0);
                    suitConvertParams.setIgnoreProcess(1);
                    if (user.getStaff().openAuditActiveStockRecord()) {
                        suitConvertParams.setAllowableStockOut(1);
                    }
                    eventCenter.fireEvent(this, new EventInfo("trade.suits.convert").setArgs(new Object[]{user.getStaff(), params, suitConvertParams}), null);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(tradeImportData.insertTrades)) {
            List<Trade> gxTrades = tradeImportData.insertTrades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(gxTrades)) {
                fxBusiness.recordSupplierSrcInfo(user, gxTrades);
                if(ConfigHolder.FX_GLOBAL_CONFIG.isUploadFxCommission(staff.getCompanyId())) {
                    eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_COMMISSION_UPLOAD).setArgs(new Object[]{user.getStaff(), TradeUtils.toSidList(gxTrades)}), null);
                }
            }
            List<Trade> fxOrMixTrades = tradeImportData.insertTrades.stream().filter(TradeUtils::isFxOrMixTrade).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(fxOrMixTrades)) {
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_SYNC).setArgs(new Object[]{user.getStaff(), TradeUtils.toSidList(fxOrMixTrades)}), null);
            }
            List<Trade> gxOrMixTrades = tradeImportData.insertTrades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(gxOrMixTrades)) {
                fxBusiness.handleGxItemStockCache(gxOrMixTrades.get(0).getSourceId());
                // 已作废的供销订单如果运单号没有继承下来需要回收掉
                Set<String> outSids = gxOrMixTrades.stream().filter(t->StringUtils.isNotBlank(t.getOutSid())).map(Trade::getOutSid).collect(Collectors.toSet());
                List<Trade> cancelGxTrades = Lists.newArrayList();
                tradeImportData.existTradeMap.forEach((k,v)->{
                    if(CollectionUtils.isNotEmpty(v)){
                        v.stream().filter(t->t!=null && TradeUtils.isGxOrMixTrade(t) && TradeUtils.isCancel(t) && StringUtils.isNotBlank(t.getOutSid())).forEach(cancelGxTrades::add);
                    }
                });
                if(CollectionUtils.isNotEmpty(cancelGxTrades)){
                    List<Trade> gxCancelOutSidTrades = cancelGxTrades.stream().filter(t->!outSids.contains(t.getOutSid())).map(TradeUtils::buildHaveOutsidTrade)
                            .collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(gxCancelOutSidTrades)){
                        eventCenter.fireEvent(this, new EventInfo("wlb.waybillCode.cancel").setArgs(new Object[]{user.getStaff(), gxCancelOutSidTrades, "分销重新指定供销快递，运单号未继承"}), null);
                    }
                }
                List<Trade> updateOutSidPoolTrades = gxOrMixTrades.stream().filter(t->t.hasOpV(OpVEnum.TRADE_GX_EXTEND_OUTSID) && StringUtils.isNotBlank(t.getOutSid())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(updateOutSidPoolTrades)){
                    UpdateOutsidPoolParams params = UpdateOutsidPoolUItils.buildUpdateOutsidPoolParams(user.getStaff(), updateOutSidPoolTrades);
                    eventCenter.fireEvent(this, new EventInfo("update.outsid.pool").setArgs(new Object[]{params}), null);
                }
            }
            Optional.ofNullable(tradeImportData.tradeConfig)
                    .filter(config -> Objects.equals(1, config.getInteger("isCoverWeight")))
                    .ifPresent(config -> {
                        List<Trade> insertTrades = tradeImportData.insertTrades.stream()
                                .filter(trade -> TradeWeightUtils.isNeedCoverActualVolume(trade)
                                        && !MathUtils.equalsZero(trade.getVolume()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(insertTrades)) {
                            eventCenter.fireEvent(
                                    this,
                                    new EventInfo(TradeEvents.TRADE_UPDATE_ACTUAL_VOLUME)
                                            .setArgs(new Object[]{user.getStaff(), insertTrades}),
                                    null
                            );
                        }
                    });
        }

        temp = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(tradeImportData.trades)) {
            //平台已发或者系统已发均不需要发送提前上传事件
            List<Trade> toUploadTrades = tradeImportData.trades.stream().filter(t -> !(UploadUtils.platformIsConsigned(user.getStaff(), t) || TradeUtils.isAfterSendGoods(t))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(toUploadTrades)) {
                this.sendUploadAheadEvent(user, TradeUtils.toTidList(toUploadTrades), toUploadTrades, eventCenter, this);
            }
        }
        long uploadAheadTook = System.currentTimeMillis() - temp;

        if (CollectionUtils.isNotEmpty(tradeImportData.updateTrades)) {
            List<Trade> btas = tradeImportData.updateTrades.stream().filter(TradeTypeUtils::isFxgBtasTrade).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(btas)) {
                eventCenter.fireEvent(this, new EventInfo("trade.btas.notify.wms").setArgs(new Object[]{user.getStaff(), TradeUtils.toSidList(btas)}), null);
            }
        }

        temp = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(tradeImportData.updateAllTrades)) {
            List<Trade> gxCancelAuditUndoTagTrades = tradeImportData.updateAllTrades.stream()
                    .filter(t -> TradeUtils.isGxOrMixTrade(t) && t.isFxReAudit() && TradeTagUtils.checkIfExistTag(t, SystemTags.TAG_AUDIT_UNDO))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(gxCancelAuditUndoTagTrades)) {
                tradeSysLabelBusiness.removeTags(user.getStaff(), gxCancelAuditUndoTagTrades, OpEnum.TRADE_SYNC, Collections.singletonList(SystemTags.TAG_AUDIT_UNDO));
            }

            List<Trade> alibabaFxCancelTrades = tradeImportData.updateAllTrades.stream()
                    .filter(t->TradeUtils.isAlibabaFxRoleTrade(t) && t.getOrigin()!=null && !Objects.equals(t.getOrigin().getSysStatus(), Trade.SYS_STATUS_CLOSED) && Objects.equals(t.getSysStatus(), Trade.SYS_STATUS_CLOSED))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(alibabaFxCancelTrades)){
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_ALIBABA_FX_CANCEL).setArgs(new Object[]{user.getStaff(), TradeAlibabaEcDto.builder().sids(TradeUtils.toSidList(alibabaFxCancelTrades)).opTypeEnum(TradeAlibabaOpTypeEnum.TRADE_CLOSE).build()}), null);
            }
            // 订单同步流程分销重新指定快递 && 有取号的 --> 运单号回收
            List<Trade> gxCancelOutSidTrades = tradeImportData.updateAllTrades.stream()
                    .filter(t -> TradeUtils.isGxOrMixTrade(t) && t.hasOpV(OpVEnum.TRADE_FX_REAPPOINT_TEMPLATE_ID) && t.getOrigin()!=null && StringUtils.isNotBlank(t.getOrigin().getOutSid()))
                    .map(t->TradeUtils.buildHaveOutsidTrade(t.getOrigin()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(gxCancelOutSidTrades)){
                eventCenter.fireEvent(this, new EventInfo("wlb.waybillCode.cancel").setArgs(new Object[]{user.getStaff(), gxCancelOutSidTrades, "分销重新指定供销快递"}), null);
            }
            // 供销订单需要重算流水的订单集合
            List<Trade> gxReCalcCashFlowTrades = tradeImportData.updateAllTrades.stream()
                    .filter(t -> TradeUtils.isGxOrMixTrade(t) && t.hasOpV(OpVEnum.TRADE_GX_RECALC_CASHFLOW))
                    .collect(Collectors.toList());


            if(CollectionUtils.isNotEmpty(gxReCalcCashFlowTrades)){
                TradeControllerParams params = new TradeControllerParams();
                params.setQueryId(8L);
                params.setSid(gxReCalcCashFlowTrades.stream().map(t->String.valueOf(t.getSid())).collect(Collectors.joining(",")));

                TradeRefreshParams refreshParams = new TradeRefreshParams();
                // 不重算分销价
                refreshParams.setReFreshFxPrice(false);
                refreshParams.setReCalGxPostFee("1");
                // 不重算佣金
                refreshParams.setReFreshTradeCommission(false);
                // 内部流水， 走作废 + 消费处理
                refreshParams.setFlowRefreshMode("2");
                refreshParams.setAddFlowTrace(true);
                // 后台流水处理日志记录 -- 建议打，方便排查
                refreshParams.setPrintFlowLog(true);
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.GX_REFRESH_PRICE_BATCH).setArgs(new Object[]{user.getStaff(), params, refreshParams}), null);
            }
        }
        if(CollectionUtils.isNotEmpty(tradeImportData.insertTrades)){
            for(Trade trade : tradeImportData.insertTrades){
                if(PlatformUtils.isTradeO2o(trade)){//o2o订单 开启ERP发货配置 同步即发送事件
                    TradeConfigNew o2oSubStockConfig = result.getTradeConfigMap().get(TradeConfigEnum.O2O_TRADE_SUB_STOCK.getConfigKey());
                    if(o2oSubStockConfig != null&&o2oSubStockConfig.isOpen()){
                        eventCenter.fireEvent(this, new EventInfo("o2o.trade.auto.accept").setArgs(new Object[]{user,trade.getTid(), trade}), null);
                        EventInfo eventInfo = new EventInfo("o2o.trade.auto.pick").setArgs(new Object[]{user,trade.getTid(), trade});
                        eventInfo.setDelayTimeLevel(5);
                        eventCenter.fireEvent(this, eventInfo, null);
                    }else{
                        TradeConfigNew autoAcceptConfig = result.getTradeConfigMap().get(TradeConfigEnum.O2O_TRADE_AUTO_ACCEPT_SET.getConfigKey());
                        if(autoAcceptConfig != null&&"2".equals(autoAcceptConfig.getConfigValue())){
                            //设置订单下载自动接单
                            eventCenter.fireEvent(this, new EventInfo("o2o.trade.auto.accept").setArgs(new Object[]{user,trade.getTid(), trade}), null);
                        }
                        TradeConfigNew autoPickConfig = result.getTradeConfigMap().get(TradeConfigEnum.O2O_TRADE_AUTO_PICK_SET.getConfigKey());
                        if(autoPickConfig != null&&"2".equals(autoPickConfig.getConfigValue())){
                            EventInfo eventInfo = new EventInfo("o2o.trade.auto.pick").setArgs(new Object[]{user,trade.getTid(), trade});
                            eventInfo.setDelayTimeLevel(5);
                            eventCenter.fireEvent(this, eventInfo, null);
                        }
                    }
                }
            }
        }

        Set<Long> recalculatePostFeeSids = new HashSet<>(result.closeOrders.size());
        if (CollectionUtils.isNotEmpty(result.closeOrders)) {
            recalculatePostFeeSids.addAll(result.closeOrders.stream().map(Order::getSid).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(result.sidsOfPartOrderOtherERPConsigned)) {
            recalculatePostFeeSids.addAll(result.sidsOfPartOrderOtherERPConsigned);
        }
        if (CollectionUtils.isNotEmpty(recalculatePostFeeSids)) {
            Logs.info(LogHelper.buildLog(user.getStaff(), String.format("订单同步后重算理论运费, sids: %s", recalculatePostFeeSids)));
            eventCenter.fireEvent(this, new EventInfo("modify.template.calculate.theoryPostFee").setArgs(new Object[]{user.getStaff(), new ArrayList<>(recalculatePostFeeSids), BusinessNodeEnum.TRADE_MODIFY_TEMPLATE}), null);
        }

        long removeTagTook = System.currentTimeMillis() - temp;

        long took = System.currentTimeMillis() - start;
        if (took > 3000) {
            Logs.ifDebug(LogHelper.buildLogHead(user.getStaff()).append(String.format("慢业务，TradeImportAfterBusiness.importAfter,took=%s,outSidLogTook=%s,unauditTook=%s,refundSplitTook=%s,rematchWarehouseTook=%s,rematchExpressTook=%s,rematchTradeLabelTook=%s,pddRematchExpressTook=%s,pddExpressForTraceTook=%s,fxconsumeTook=%s,toWaitAuditMatchWarehouseTook=%s,cancelRiskMatchWarehouseTook=%s,gxTook=%s,uploadAheadTook=%s,removeTagTook=%s", took, outSidLogTook, unauditTook, refundSplitTook, rematchWarehouseTook, rematchExpressTook, rematchTradeLabelTook, pddRematchExpressTook, pddExpressForTraceTook, fxconsumeTook, toWaitAuditMatchWarehouseTook, cancelRiskMatchWarehouseTook, gxTook, uploadAheadTook, removeTagTook)));
        }
    }

    private boolean getWorkOrderId(Trade trade, Map<Long, Long> workOrderIds) {
        TradeExt tradeExt = trade.getTradeExt();
        if (tradeExt == null) {
            return false;
        }
        String extraFields = tradeExt.getExtraFields();
        if (extraFields == null) {
            return false;
        }
        JSONObject extraJson = JSON.parseObject(extraFields);
        String workOrderId = extraJson.getString("workOrderId");
        if (org.apache.commons.lang3.StringUtils.isBlank(workOrderId)) {
            return false;
        }
        workOrderIds.put(trade.getSid(), Long.valueOf(workOrderId));
        return true;
    }

    private void checkUserOpenDesensitization(User user, List<Trade> originCurrentTrades) {
        if (TradeDecryptUtils.supportPageSearchUsers(user) && CollectionUtils.isNotEmpty(originCurrentTrades) && org.apache.commons.lang3.StringUtils.isNotBlank(originCurrentTrades.get(0).getAddressMd5())) {
            try {
                if (null == user.getUserConf().getConfAttrInfoIfEmpty().get("openOaid")) {
                    Map<String, Object> confMap = new HashMap<>();
                    confMap.put("openOaid", "1");
                    userService.updateUserConf(user.getStaff(), user.getId(), confMap);
                }
            } catch (Exception e) {
                Logs.error(LogHelper.buildLogHead(user).append("更新user表的openOaid参数失败"), e);
            }
        }
    }


    public void notifyGxBusisess(User user, TradeImportAfterData tradeImportAfterData) {
        if (CollectionUtils.isEmpty(tradeImportAfterData.notifyGxTradeSids)) {
            return;
        }
        Logs.ifDebug(LogHelper.buildLog(user.getStaff(), String.format("分销订单自动通知供销下载%s", tradeImportAfterData.notifyGxTradeSids)));
        try {
            fxTradeDownloadNotifyListener.handleGxTrade(user.getStaff(), tradeImportAfterData.notifyGxTradeSids.toArray(new Long[0]), null);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLogHead(user).append(String.format("分销订单自动通知供销下载出错，sids=%s", tradeImportAfterData.notifyGxTradeSids)), e);
        }
    }


    public void bicTradeInitExpressBusiness(User user, TradeImportAfterData tradeImportAfterData) {
        if (CollectionUtils.isEmpty(tradeImportAfterData.bicTradeInitExpressSidSet)) {
            return;
        }
        List<TbTrade> bicTbTrade = null;
        try {
            // 查询订单
            Long[] sidsArray = tradeImportAfterData.bicTradeInitExpressSidSet.toArray(new Long[0]);
            List<TbTrade> trades = tbTradeDao.queryBySids(user.getStaff(), sidsArray);
            bicTbTrade = trades.stream()
                    .filter(e -> e.getTemplateId() == null || e.getTemplateId() <= 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(bicTbTrade)) {
                Long[] sids = TradeUtils.toSids(bicTbTrade);
                List<Trade> bicTrade = TradeUtils.toTrades(bicTbTrade);
                lockService.locks(tradeLockBusiness.getERPLocks(user.getStaff(), sids),
                        () -> userWlbExpressTemplateService.getBicWlbExpressTemplate(user.getStaff(), bicTrade));
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLogHead(user).append(String.format("bic快递模版出错，sids=%s", TradeUtils.toSidList(bicTbTrade))), e);
        }
    }

    public void trackingBatchBusiness(User user, TradeImportAfterData tradeImportAfterData) {
        if (CommonConstants.PLAT_FORM_TYPE_PDD.equals(user.getSource()) && CollectionUtils.isNotEmpty(tradeImportAfterData.trackingBatchOrderTids)) {
            try {
                trackingBatchOrderListener.handle(user, new ArrayList<>(tradeImportAfterData.trackingBatchOrderTids));
            } catch (Exception e) {
                Logs.error(LogHelper.buildLogHead(user).append(String.format("trackingBatch处理出错，trackingBatchOrderTids=%s", tradeImportAfterData.trackingBatchOrderTids)), e);
            }
        }
    }

    public void unmatchRemindBusiness(User user, TradeImportAfterData tradeImportAfterData) {
        if (CollectionUtils.isNotEmpty(tradeImportAfterData.insertSids)) {
            try {
                List<Trade> insertTrades = tradeSearchService.queryBySids(user.getStaff(), true, tradeImportAfterData.insertSids.toArray(new Long[0]));
                List<Order> unMatchOrder = TradeUtils.getOrders4Trade(insertTrades).stream().filter(t -> (t.getItemSysId() == null || t.getItemSysId() <= 0) && !OrderUtils.isGxOrder(t)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(unMatchOrder)) {
                    eventCenter.fireEvent(this, new EventInfo("trade.unmatch.remind").setArgs(new Object[]{user, unMatchOrder}), null);
                }
            } catch (Exception e) {
                Logs.error(LogHelper.buildLogHead(user).append(String.format("unmatchRemind处理出错，insertSids=%s", tradeImportAfterData.insertSids)), e);
            }
        }
    }

    /**
     * 踢出波次重新补偿
     */
    public void waveRemoveBusiness(User user, TradeImportAfterData tradeImportAfterData) {
        Set<Long> reconsumeRemoveWaveSids = tradeImportAfterData.reconsumeRemoveWaveSids;
        if (CollectionUtils.isEmpty(reconsumeRemoveWaveSids)) {
            return;
        }
        Logs.ifDebug(LogHelper.buildLog(user.getStaff(), String.format("补偿踢出事件:%s", reconsumeRemoveWaveSids)));
        try {
            removeWaveSid(user.getStaff(), reconsumeRemoveWaveSids.toArray(new Long[0]), user.getStaff().getConf().getOpenWave() == 1, user.getStaff().getConf().openOrderUniqueCode(), "");
        } catch (Exception e) {
            Logs.error(LogHelper.buildLogHead(user).append(String.format("waveRemove处理出错，reconsumeRemoveWaveSids=%s", reconsumeRemoveWaveSids)), e);
        }
    }

    public void removeWaveSid(Staff staff, Long[] sids, boolean openWave, boolean openOrderUniqueCode, String opName) {
        if (sids == null || sids.length == 0) {
            return;
        }
        if (!openWave && !openOrderUniqueCode) {
            return;
        }
        // 踢出波次
        tradeWaveService.removeSids(staff, sids, opName);
    }


    /**
     * 自动拆单
     *
     * @param user
     * @param tradeImportAfterData
     */
    public void splitAuto(User user, TradeImportAfterData tradeImportAfterData) {
        Set<Long> optSids = new HashSet<>();
        if (CollectionUtils.isNotEmpty(tradeImportAfterData.autoSplitSids)) {
            optSids.addAll(tradeImportAfterData.autoSplitSids);
        }
        if (CollectionUtils.isNotEmpty(tradeImportAfterData.waitPayToWaitAuditSids)) {
            optSids.addAll(tradeImportAfterData.waitPayToWaitAuditSids);
        }
        if (MapUtils.isNotEmpty(tradeImportAfterData.exceptChangeMap)) {
            Map<Long, Set<Long>> exceptMap = tradeImportAfterData.exceptChangeMap.get(ExceptEnum.RISK);
            if (MapUtils.isNotEmpty(exceptMap) && CollectionUtils.isNotEmpty(exceptMap.get(0L))) {
                // 取消风控异常的订单
                optSids.addAll(exceptMap.get(0L));
            }
        }
        //自动拆单
        if (CollectionUtils.isEmpty(optSids)) {
            return;
        }
        TradeConfig tradeConfig = tradeImportAfterData.tradeConfig;
        if (!TradeConfigUtils.isOpenTradeAutoSplit(tradeConfig)) {
            return;
        }
        try {
            Logs.ifDebug(LogHelper.buildLog(user.getStaff(), String.format("执行自动拆单:%s", optSids)));
            List<Trade> insertTrades = tradeSearchService.queryBySids(user.getStaff(), true, tradeImportAfterData.insertSids.toArray(new Long[0]));
            moneyChangeBusiness.tradeUpdateMoneyLog(user.getStaff(), MONEY_CHANGE_BEFORE, TRADE_SYNC, insertTrades);
            autoSplitBusiness.splitAuto(user.getStaff(), optSids.toArray(new Long[0]));
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(user.getStaff(), e, String.format("自动拆单处理出错,sids=%s", optSids)), e);
        }
    }


    /**
     * 抖音自动流转处理
     */
    public void fxgAutoLzHandle(User user, TradeImportAfterData tradeImportAfterData) {
        //抖音自动流转的订单
        if (CollectionUtils.isNotEmpty(tradeImportAfterData.fxgAutoLzSids)) {
            eventCenter.fireEvent(this, new EventInfo("fxg.auto.lz").setArgs(new Object[]{user.getStaff(), tradeImportAfterData.fxgAutoLzSids.toArray(new Long[0])}), null);
        }
    }

    public void poisonFeedBackPlatWarehouseHandle(User user, TradeImportAfterData tradeImportAfterData) {
        if (CollectionUtils.isEmpty(tradeImportAfterData.poisonFeedbackSids)) {
            return;
        }
        eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_POISON_FEEDBACK_PLATFORM_WAREHOUSE).setArgs(new Object[]{user.getStaff(), tradeImportAfterData.poisonFeedbackSids.toArray(new Long[0])}), null);
    }


    public void smtQtgFeedbackHandle(User user, TradeImportAfterData tradeImportAfterData) {
        if (CollectionUtils.isEmpty(tradeImportAfterData.smtqtgSids)) {
            return;
        }
        eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_SMTQTG_FEEDBACK_CONFIRM).setArgs(new Object[]{user.getStaff(), tradeImportAfterData.smtqtgSids.toArray(new Long[0])}), null);
    }

    /**
     * 商家代发店铺处理
     */
    public void dfMallHandler(User user, TradeImportAfterData tradeImportAfterData) {
        try {
            //放心购商家代发店铺的处理
            boolean ifSourceMatch = CommonConstants.PLAT_FORM_TYPE_FXG_DF.equals(user.getSource()) || CommonConstants.PLAT_FORM_TYPE_PDD.equals(user.getSource());
            if ((CollectionUtils.isNotEmpty(tradeImportAfterData.insertSids) || CollectionUtils.isNotEmpty(tradeImportAfterData.updateSids)) && ifSourceMatch) {
                List<Trade> insertTrades = tradeSearchService.queryBySids(user.getStaff(), true, tradeImportAfterData.insertSids.toArray(new Long[0]));
                List<Trade> updateTrades = tradeSearchService.queryBySids(user.getStaff(), true, tradeImportAfterData.updateSids.toArray(new Long[0]));
                eventCenter.fireEvent(this, new EventInfo("fxgDf.mall.insertOrUpdate").setArgs(new Object[]{user.getStaff(), insertTrades, updateTrades}), null);
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(user.getStaff(), e, String.format("dfMallHandler处理出错,insertSids=%s,updateSids=%s", tradeImportAfterData.insertSids, tradeImportAfterData.updateSids)), e);
        }
    }

    /**
     * tiktok根据平台标识自动拆/合单
     *
     * @param user
     * @param tradeImportAfterData
     */
    public void tiktokAutoHandler(User user, TradeImportAfterData tradeImportAfterData) {
        if (tradeImportAfterData.tiktokTidList.isEmpty()) {
            return;
        }
        try {
            List<TbTrade> trades = tradeSearchService.queryByTids(user.getStaff(), false, tradeImportAfterData.tiktokTidList.toArray(new String[0]));
            if (trades.isEmpty()) {
                return;
            }
            List<TradeExt> tradeExts = tradeExtDao.tradeExtsGetBySids(user.getStaff(), trades.stream().map(TbTrade::getSid).collect(Collectors.toList()));
            TradeUtils.assemblyBySidForExt(trades, tradeExts);
            //判断是否需要拆/合单
            Set<String> combinedTidList = new HashSet<>();
            Set<String> splitTidList = new HashSet<>();
            Set<String> upSplitTidList = new HashSet<>();
            Set<String> unCombinedTidList = new HashSet<>();
            for (Trade trade : trades) {
                TradeExt tradeExt = trade.getTradeExt();
                if (Objects.isNull(tradeExt)) {
                    continue;
                }
                String extraFields = tradeExt.getExtraFields();
                JSONObject extraJson = JSON.parseObject(extraFields);
                String splitOrCombineTag = extraJson.getString("split_or_combine_tag");
                //平台标识为合单，且系统订单未合并 则发送合单事件
                if (Objects.equals("combined", splitOrCombineTag) && !com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(user.getStaff(), trade)) {
                    combinedTidList.add(trade.getTid());
                }
                //平台标识为拆单，且系统订单未拆单 则发送拆单事件
                if (Objects.equals("split", splitOrCombineTag) && !TradeUtils.isSplit(trade)) {
                    splitTidList.add(trade.getTid());
                }
                if ((Objects.isNull(splitOrCombineTag) || Objects.equals("none", splitOrCombineTag))) {
                    if (TradeUtils.isSplit(trade)) {
                        //平台标识为正常单，且系统订单已拆单，则需要取消系统单的拆单
                        upSplitTidList.add(trade.getTid());
                    }
                    if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(user.getStaff(), trade)) {
                        //平台标识为正常单，且系统订单已合单，则需要取消系统单的合单
                        unCombinedTidList.add(trade.getTid());
                    }
                }
            }
            if (!combinedTidList.isEmpty()) {
                eventCenter.fireEvent(this, new EventInfo("tiktok.auto.handler").setArgs(new Object[]{user, combinedTidList, "combined"}), null);
            }
            if (!splitTidList.isEmpty()) {
                eventCenter.fireEvent(this, new EventInfo("tiktok.auto.handler").setArgs(new Object[]{user, splitTidList, "split"}), null);
            }
            if (!upSplitTidList.isEmpty()) {
                eventCenter.fireEvent(this, new EventInfo("tiktok.auto.handler").setArgs(new Object[]{user, upSplitTidList, "upSplit"}), null);
            }
            if (!unCombinedTidList.isEmpty()) {
                eventCenter.fireEvent(this, new EventInfo("tiktok.auto.handler").setArgs(new Object[]{user, unCombinedTidList, "unCombined"}), null);
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(user.getStaff(), e, String.format("tiktokAutoHandler处理出错,tiktokTidList=%s", tradeImportAfterData.tiktokTidList)), e);
        }
    }

    public void auditConsign(Staff staff, Long[] sids) {
        if (sids != null && sids.length > 0) {
            try {
                tradeAuditService.auditAndConsign(staff, sids);
            } catch (Exception e) {
                Logs.error(LogHelper.buildErrorLog(staff, e, String.format("auditConsign处理出错,sids=%s", Arrays.asList(sids))), e);
            }
        }
    }

    // public void auditAuto(User user) {
    //     if (auditAutoBusiness.needAudit(user.getStaff(), user)) {
    //         eventCenter.fireEvent(this, new EventInfo("trade.auto.audit").setArgs(new Object[]{user.getStaff(), user}), null);
    //     }
    // }

    private void unautidForExcep(User user, TradeImportData tradeImportData) {
        if (TradeConfigUtils.openExistExcepAutoUndoAudit(tradeImportData.tradeConfig)) {
            List<Trade> tempList = new ArrayList<>();
            List<Trade> trades = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(tradeImportData.updateTrades)) {
                trades.addAll(tradeImportData.updateTrades);
            }
            if (CollectionUtils.isNotEmpty(tradeImportData.insertTrades)) {
                trades.addAll(tradeImportData.insertTrades);
            }
            for (Trade trade : trades) {
                if (Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus())) {
                    continue;
                }
                if (!Objects.equals(trade.getIsExcep(), 1)) {
                    continue;
                }
                tempList.add(trade);
            }
            tempList = TradeUtils.satisfyStatusAndExcep(user.getStaff(), tempList, tradeImportData.tradeConfig);
            tradeImportData.opEnumAuditUndoSidMap.put(OpEnum.AUDIT_UNDO_AUTO_EXCEPT_SYNC, TradeUtils.toSidSet(tempList));
        }
    }

    /**
     * 订单获取单号后根据配置需要发送定时预发货的事件
     */
    public void sendUploadAheadEvent(User user, List<String> tids, List<Trade> trades, IEventCenter eventCenter, Object target) {
        if (CollectionUtils.isEmpty(tids) || CollectionUtils.isEmpty(trades)) {
            return;
        }
        Map<Long, List<Trade>> tradeMap = trades.stream().filter(t -> tids.contains(t.getTid()) && t.getUserId() != null).collect(Collectors.groupingBy(Trade::getUserId));
        if (user.getId().equals(100000000L)) {//供销订单特殊处理
            tradeMap = trades.stream().filter(t -> tids.contains(t.getTid()) && t.getTaobaoId() != null).collect(Collectors.groupingBy(Trade::getTaobaoId));
        }
        List<String> tidList = new ArrayList<>();
        Set<String> userIds = uploadAheadBusiness.getGxAheadSendGoodsUserIdsFromSysSetting(user);
        Set<String> payTimeAheadUserIds = uploadAheadBusiness.getPayTimeAheadUserIds(user);
        for (Map.Entry<Long, List<Trade>> entry : tradeMap.entrySet()) {
            Integer gxAheadHours = 0;
            boolean isFilterPresell = false;
            if (userIds.contains(entry.getKey().toString())) {
                gxAheadHours = uploadAheadBusiness.getGxAheadSendGoodsHourFromSysSetting(user);
            }
            if (payTimeAheadUserIds.contains(entry.getKey().toString())) {
                gxAheadHours = uploadAheadBusiness.getPayTimeAheadSendGoodsHourFromSysSetting(user.getStaff(), entry.getKey());
                if (gxAheadHours > 0) {
                    isFilterPresell = true;
                }
            }
            if (!UploadUtils.isUploadAhead(user) && gxAheadHours <= 0) {
                Logs.ifDebug(LogHelper.buildLogHead(user.getStaff()).append(String.format("执行定时预发货失败，用户未开启配置[userId=%s]", entry.getKey())));
                continue;
            }
            List<Trade> tradeList = entry.getValue();
            if (isFilterPresell) {//按付款配置预发货 过滤预售订单
                tidList.addAll(tradeList.stream().filter(t -> !(t.isPresellTrade() || "step".equals(t.getType()) || "shopPresell".equals(t.getType()) || "fullPresell".equals(t.getType())) || "sys".equals(t.getSource())).map(Trade::getTid).collect(Collectors.toList()));
            } else {
                tidList.addAll(tradeList.stream().map(Trade::getTid).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isNotEmpty(tidList)) {
            eventCenter.fireEvent(target, new EventInfo("trade.upload.ahead").setArgs(new Object[]{user.getStaff(), tidList.toArray(new String[0]), user}), null);
        }
    }


    private void closeOrderNoticeCaiGou(User user, TradeImportResult result) {
        // 交易关闭的order 通知采购
        if (CollectionUtils.isEmpty(result.closeOrders)) {
            return;
        }
        List<String> logs = new ArrayList<>();
        Map<Integer, List<Order>> orderMap = new HashMap<>();
        Map<Integer, List<Order>> orderReFundMap = new HashMap<>();
        for (Order order : result.closeOrders) {
            Order tbOrder = new TbOrder();
            tbOrder.setId(order.getId());
            tbOrder.setSid(order.getSid());
            tbOrder.setItemSysId(order.getItemSysId());
            tbOrder.setSkuSysId(order.getSkuSysId());
            Order origin = order.getOrigin();
            if (origin == null) {
                // 理论上是不存在origin为null的
                Logs.debug(LogHelper.buildLog(user.getStaff(), String.format("origin 对象不存在 id=%s  sid=%s", order.getId(), order.getSid())));
                continue;
            }
            // 申请退款
            boolean refund = RefundUtils.isRefundOrder(order) && !RefundUtils.isRefundOrder(origin) && !Objects.equals(order.getSysStatus(), Trade.SYS_STATUS_CLOSED);
            // 取消退款
            boolean cancelRefund = !RefundUtils.isRefundOrder(order) && RefundUtils.isRefundOrder(origin) && !Objects.equals(order.getSysStatus(), Trade.SYS_STATUS_CLOSED);
            // 交易关闭
            boolean close = !Objects.equals(origin.getSysStatus(), Trade.SYS_STATUS_CLOSED) && Objects.equals(order.getSysStatus(), Trade.SYS_STATUS_CLOSED);
            if (refund) {
                // 申请退款
                orderReFundMap.computeIfAbsent(0, k -> new ArrayList<>()).add(tbOrder);
            } else if (cancelRefund) {
                // 取消退款
                orderReFundMap.computeIfAbsent(1, k -> new ArrayList<>()).add(tbOrder);
            } else if (close) {
                // 原始状态为发货后
                if (TradeStatusUtils.isAfterSendGoods(origin.getSysStatus())) {
                    orderMap.computeIfAbsent(0, k -> new ArrayList<>()).add(tbOrder);
                } else {
                    orderMap.computeIfAbsent(1, k -> new ArrayList<>()).add(tbOrder);
                }
            }
            logs.add(String.format(" order id=%s,sid=%s,sysStatus=%s,refundStatus=%s", order.getId(), order.getSid(), order.getSysStatus(), order.getRefundStatus()));
        }
        Logs.debug(LogHelper.buildLog(user.getStaff(), String.format("交易关闭的单通知采购：%s", logs)));
        if (MapUtils.isNotEmpty(orderMap)) {
            eventCenter.fireEvent(this, new EventInfo("trade.waicai.close").setArgs(new Object[]{user.getStaff(), orderMap}), null);
        }
        if (MapUtils.isNotEmpty(orderReFundMap)) {
            eventCenter.fireEvent(this, new EventInfo("trade.waicai.refund").setArgs(new Object[]{user.getStaff(), orderReFundMap}), null);
        }
    }

    private void checkTradePartClose(User user, List<Trade> updateTrades, TradeImportResult result) {
        //针对合单，处理退款部分关闭异常
        TradeConfig tradeConfig = result.getTradeConfig();
        int openPartRefund = tradeConfig.getOpenPartRefund(user.getStaff());
        //开启了配置
        if (openPartRefund != 1) {
            return;
        }
        if (CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        List<Long> checkPartCloseSids = new ArrayList<>();
        for (Trade trade : updateTrades) {
            Trade origin = trade.getOrigin();
            if (origin == null) {
                Logs.debug(LogHelper.buildLog(user.getStaff(), String.format("订单同步 origin 为null sid=%s", trade.getSid())));
                continue;
            }
            if (TradeUtils.isAfterSendGoods(trade) && !TradeUtils.isAfterSendGoods(origin)) {
                checkPartCloseSids.add(trade.getSid());
            }
        }
        if (CollectionUtils.isNotEmpty(checkPartCloseSids)) {
            eventCenter.fireEvent(this, new EventInfo("merge.part.refund").setArgs(new Object[]{user, checkPartCloseSids}), null);
        }
    }

    private void closeOrderDeleteGiftMatchLog(User user, TradeImportData tradeImportData) {
        if (CollectionUtils.isEmpty(tradeImportData.updateAllTrades)) {
            return;
        }
        List<Order> giftOrders = TradeUtils.getGiftOrders4Trade(tradeImportData.updateAllTrades);
        List<Order> closeGiftOrders = giftOrders.stream().filter(order -> Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()) && order.isGift()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(closeGiftOrders)) {
            giftDeductCountBusiness.returnGiftCountByOrderIds(user.getStaff(),  closeGiftOrders.stream().map(Order::getId).collect(Collectors.toList()));
        }
    }

    public void tradeNotifyScan(User user) {
        if (user.getId() - Constants.FxDefaultUserId != 0 && !tradeNotifyScanLock.scaning(user.getStaff(), user)) {
            eventCenter.fireEvent(this, new EventInfo("trade.notify.scan").setArgs(new Object[]{user.getStaff(), user}), null);
        }
    }

    private Staff getLightStaff(User user) {
        Map<Long, User> userIdMap = new HashMap<>();
        userIdMap.put(user.getId(), user);
        Staff lightStaff = user.getStaff();
        lightStaff.setUserIdMap(userIdMap);
        return lightStaff;
    }


    public void handlerPlatNumModifyExcept(User user, TradeImportAfterData tradeImportAfterData){
        if(CollectionUtils.isEmpty(tradeImportAfterData.platModifyItemNumExceptSids)){
            return;
        }
        eventCenter.fireEvent(this, new EventInfo(TradePlatModifyItemNumExceptListener.EVENT_NAME).setArgs(new Object[]{user.getStaff(), tradeImportAfterData.platModifyItemNumExceptSids}), null);
    }
}

package com.raycloud.dmj.business.sync.strategy;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.raycloud.data.api.SyncRDSTradeGetRequest;
import com.raycloud.dmj.business.sync.TbTradeQueryBusiness;
import com.raycloud.dmj.business.sync.utils.DubboRequstUtil;
import com.raycloud.dmj.business.sync.utils.RdsServerUtils;
import com.raycloud.dmj.business.sync.utils.TbTradeUtils;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeStatus;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.UserUtils;
import com.raycloud.dmj.domain.trades.TradeImportResult;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.tb.trade.TbTradeSearchRetry;
import com.raycloud.domain.RdsTradeJson;
import com.raycloud.domain.UserSynInfo;
import com.raycloud.sync.api.DubboRequest;
import com.raycloud.sync.api.DubboResponse;
import com.taobao.api.response.TradeFullinfoGetResponse;
import com.taobao.api.response.TradesSoldIncrementGetResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @desc
 * @date 2018-04-27-15:23
 */
@Service
public class MixIncrSyncStrategy extends RdsIncrSyncStrategy {

    @Resource
    TbTradeSearchRetry tbTradeSearchRetry;

    @Resource
    TbTradeQueryBusiness tbTradeQueryBusiness;

    @Resource
    ApiFullSyncStrategy apiFullSyncStrategy;

    @Override
    public Integer getSyncType() {
        return TradeConstants.SYNC_MIX;
    }

    @Override
    public TradeImportResult importTrades(User user, Date startTime, Date endTime, String status) {
        boolean firstUseSync = user.getUserConf().isFirstUseTradeSync();
        if (firstUseSync) {//首次同步不能用MIX,用full api，并且同步前三个月所有状态订单
            return apiFullSyncStrategy.importTrades(user, null, null, null);
        }
        long t0 = System.currentTimeMillis();
        TradeImportResult result = new TradeImportResult();
        Date start = (startTime != null ? startTime : user.getLastImportTradeTime());
        Date end = (endTime != null ? endTime : getTaobaoTime(user));
        //使用API增量量方式下载订单，起始时间与截止时间差不能超过1天
        if (start == null || start.after(end)) {//没有起始时间或者起始时间在截止时间之后，设置起始时间为截止时间向前推一天
            start = DateUtils.addDays(end, -1);
        } else {//起始时间在截止时间之前，若截止时间与起始时间之差超过一天，设置截止时间为起始时间向后推一天，以防止漏单
            start = DateUtils.addSeconds(start, -getSyncAheadTime(user));
            Date dayEnd = DateUtils.addDays(start, 1);
            if (end.after(dayEnd)) {
                end = dayEnd;
            }
        }
        Page page = new Page().setPageNo(2).setPageSize(DEFAULT_IMPORT_PAGE_SIZE);
        TradesSoldIncrementGetResponse rsp;

        UserSynInfo userSyncInfo = null;
        try {
            registerUser(user);
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(user).append(String.format("店铺[%s, %s, %s]注册到RDS同步模块出错", user.getSource(), user.getId(), user.getNick())), e);
        }

        Integer failCount = 0;
        Boolean hasError = false;
        Date lastModified = null;
        do {
            rsp = null;
            page.setPageNo(page.getPageNo() - 1);
            long current = System.currentTimeMillis();
            try {
                //增量接口   没返回tradeAttr字段
                if (page.getPageNo() - 1 == 0) {//第一页的时候设置useHasNext=false,用于拉取订单总数,计算分页数
                    rsp = tbTradeSearchRetry.queryByModified(user, page, start, end, "tid,modified,status,seller_flag,receiver_address,trade_attr", false);
                    if (rsp != null && rsp.getTotalResults() != null) {
                        int totalPageNo = (int) (rsp.getTotalResults() / page.getPageSize()) + (rsp.getTotalResults() % page.getPageSize() == 0 ? 0 : 1);
                        page.setPageNo(totalPageNo + 1);
                    }
                } else {//其他页不需要拉订单总数,设置useHasNext=true
                    rsp = tbTradeSearchRetry.queryByModified(user, page, start, end, "tid,modified,status,seller_flag,receiver_address,trade_attr", true);
                }
                if (rsp != null) {
                    if (rsp.getTrades() == null || rsp.getTrades().isEmpty()) {
                        if (!rsp.isSuccess()) {
                            result.setError(true).addErrMsg(StringUtils.isNotBlank(rsp.getSubMsg()) ? rsp.getSubMsg() : rsp.getMsg());
                        }
                        StringBuilder buf = new StringBuilder().append("code=").append(rsp.getErrorCode()).append(",errMsg=").append(rsp.getMsg()).append("; subCode=").append(rsp.getSubCode()).append(", subMsg=").append(rsp.getSubMsg());
                        logger.debug(LogHelper.buildLogHead(user).append(String.format("[MIX同步][API增量]店铺[%s, %s, %s]第%s页没有同步到订单%s,耗时%sms", user.getSource(), user.getId(), user.getNick(), page.getPageNo(), (buf.length() > 0 ? "[" + buf + "]" : ""), (System.currentTimeMillis() - current))));
                    } else {
                        if (userSyncInfo == null) {
                            userSyncInfo = getUserSyncInfo(user);
                        }
                        if (userSyncInfo == null) {
                            result.setError(true).addErrMsg("获取UserSyncInfo出错,忽略订单同步");
                            return result;
                        }
                        if (UserUtils.isTaoBaoTjb(user)) {
                            resetTjbOuterIid(rsp.getTrades());
                        }
                        List<Trade> tradesByApi = toCommonTrade(user, rsp.getTrades());
                        if (logger.isDebugEnabled()) {
                            logger.debug(LogHelper.buildLogHead(user).append(String.format("[MIX同步][API增量]店铺[%s, %s, %s]第%s页同步%s笔订单，耗时%sms,%s", user.getSource(), user.getId(), user.getNick(), page.getPageNo(), tradesByApi.size(), (System.currentTimeMillis() - current), TradeUtils.toTidList(tradesByApi))));
                        }
                        if (tradesByApi.size() == 0) {
                            logger.warn(LogHelper.buildLogHead(user).append(String.format("[MIX同步][API增量]店铺[%s, %s, %s]第%s页无订单,耗时%sms", user.getSource(), user.getId(), user.getNick(), page.getPageNo(), (System.currentTimeMillis() - current))));
                            continue;
                        }

                        Integer pageFailCount = SyncFromRdsThenFullInfo(user, result, tradesByApi, userSyncInfo);

                        failCount += pageFailCount;
                        if (pageFailCount.equals(0)) {
                            logger.debug(LogHelper.buildLogHead(user).append(String.format("[MIX同步][API增量]店铺[%s, %s, %s]第%s页订单顺利同步完成,该页最晚modified时间%s,耗时%sms", user.getSource(), user.getId(), user.getNick(), page.getPageNo(), DateFormatUtils.format(findLastModified(tradesByApi), "yyyy-MM-dd HH:mm:ss"), (System.currentTimeMillis() - current))));
                            if (hasError) {
                                lastModified = findLastModified(tradesByApi);
                                hasError = false;
                            }
                        } else {
                            hasError = true;
                        }
                    }
                }
            } catch (Exception e) {
                //一旦某一页同步失败 ，不继续同步直接返回，游标前进至已成功同步页中最末尾modified
                String errorMsg = LogHelper.buildLogHead(user).append(String.format("[MIX]店铺[%s, %s, %s]第%s页订单同步出错", user.getSource(), user.getId(), user.getNick(), page.getPageNo())).toString();
                logger.error(errorMsg, e);
                hasError = true;
                result.addErrMsg(errorMsg);
            }
        } while (rsp != null && page.getPageNo() > 2);

        if (failCount > 0) {
            result.setError(true).addErrMsg(String.format("[MIX][API]%s笔延迟或信息不完整订单getFullInfo失败", failCount));
        }

        if (!hasError && lastModified == null) {
            //没有一页发生错误
            forwardTradeImportTime(user, end, result, (startTime != null || endTime != null));
        } else if (!hasError && lastModified != null) {
            //有分页发生错误，但随后有分页全部同步成功，把游标移至成功同步页的最晚modified,因为增量api用hasnext拉倒的订单按modified倒序排序
            forwardTradeImportTime(user, lastModified, result, (startTime != null || endTime != null));
            result.setError(true);
        } else {
            //有分页发生错误之后每页都有错误，不前进游标
            result.setError(true);
        }
        //没有报错，不是自定义同步，一整天又没有同步到订单，则游标前进
        if (!result.getError() && !(startTime != null || endTime != null) && result.getTotal() == 0 && end.compareTo(DateUtils.addDays(start, 1)) == 0) {
            result.setForceUpdate(true);
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(user).append(String.format("[MIX]店铺[%s, %s]订单同步结束[error=%s],同步范围:%s ~ %s,耗时%sms", user.getId(), user.getNick(), result.getError(), start, end, System.currentTimeMillis() - t0)));
        }
        return result;
    }

    private Integer SyncFromRdsThenFullInfo(User user, TradeImportResult result, List<Trade> tradesByApi, UserSynInfo userSyncInfo) {
        List<Long> errorTidList;
        try {
            errorTidList = RdsSyncByTids(user, userSyncInfo, tradesByApi, result, RdsServerUtils.getServices(user, rdsTradeGetRequest, rdsTradeGetRequestNewAppKey));
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(user).append(String.format("[MIX同步][RDS]店铺[%s, %s, %s]根据tid同步rds出错", user.getSource(), user.getId(), user.getNick())), e);
            errorTidList = toTids(tradesByApi);
        }
        if (errorTidList.size() > 0) {
            logger.warn(LogHelper.buildLogHead(user).append(String.format("[MIX同步]店铺[%s, %s, %s]%s笔延迟订单进行getFullInfo:%s", user.getSource(), user.getId(), user.getNick(), errorTidList.size(), StringUtils.join(errorTidList, ","))));
        } else {
            return 0;
        }
        List<Trade> tradeList = batchGetFulInfoTrades(user, errorTidList, getSyncType());
        importToDB(user, tradeList, result);
        return errorTidList.size() - tradeList.size();//是否存在没有拉取到的订单
    }

    private List<Trade> toCommonTrade(User user, List<com.taobao.api.domain.Trade> tbTrades) {
        List<Trade> trades = new ArrayList<Trade>(tbTrades.size());
        for (com.taobao.api.domain.Trade tbTrade : tbTrades) {
            if(TbTradeUtils.isTbXsdTrade(tbTrade)) {
                User xsdUser = getXsdUserIfXsdTrade(user, tbTrade);
                if(xsdUser == null){
                    logger.warn(LogHelper.buildUserLog(user, String.format("淘宝小时达订单同步，未找到该门店信息，tid:%s,tradeAttr:%s", tbTrade.getTid(), tbTrade.getTradeAttr())));
                    continue;
                }
            }
            TbTrade trade = new TbTrade();
            trade.setModified(tbTrade.getModified());
            trade.setTid(tbTrade.getTid() + "");
            trade.setSellerFlag(tbTrade.getSellerFlag());
            trade.setReceiverAddress(tbTrade.getReceiverAddress());
            trade.setStatus(tbTrade.getStatus());
            trades.add(trade);
        }
        return trades;
    }

    private List<Long> toTids(List<Trade> trades) {
        return Lists.transform(trades, new Function<Trade, Long>() {
            @Nullable
            @Override
            public Long apply(Trade input) {
                return Long.valueOf(input.getTid());
            }
        });
    }

    private List<Long> RdsSyncByTids(User user, UserSynInfo userSyncInfo, List<Trade> pageTrades, TradeImportResult result, SyncRDSTradeGetRequest<RdsTradeJson> rdsTradeGetRequest) throws Exception {
        List<Long> pageTids = Lists.transform(pageTrades, new Function<Trade, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable Trade input) {
                return Long.valueOf(input.getTid());
            }
        });

        beforeInvoke(user);
        DubboRequest request = tbTradeQueryBusiness.buildTradeGetRequest(user, userSyncInfo, pageTids);
        long current = System.currentTimeMillis();
        DubboResponse<List<RdsTradeJson>> rsp = rdsTradeGetRequest.getDataListByTidList(request);
        if (rsp.getBody() == null || rsp.getBody().isEmpty()) {
            boolean error = (rsp.getSuccess() != null && !rsp.getSuccess()) || rsp.getCode() != 0;
            if (error) {
                result.setError(true).addErrMsg(String.format("errCode:%s,errMsg:%s", rsp.getCode(), rsp.getErrorMsg()));//接口调用异常
            }
            StringBuilder buf = new StringBuilder().append("code=").append(rsp.getCode()).append(", errMsg=").append(rsp.getErrorMsg());
            logger.warn(LogHelper.buildLogHead(user).append(String.format("[MIX同步][RDS同步]店铺[%s, %s, %s]没有同步到订单%s,耗时%sms", user.getSource(), user.getId(), user.getNick(), (buf.length() > 0 ? "[" + buf + "]" : ""), (System.currentTimeMillis() - current))));
            return pageTids;//需要getFullInfo
        } else {
            List<RdsTradeJson> rdsTrades = rsp.getBody();
            List<Trade> trades = rdsTrades2Common(user, rdsTrades);
            if (!trades.isEmpty()) {
                List<Trade> importRdsTrades = Lists.newArrayList();
                List<Long> delayTrades = removeNoDelayTrades(user, pageTrades, trades, rdsTrades, importRdsTrades);
                if (importRdsTrades.size() > 0) {
                    importToDB(user, importRdsTrades, result);
                }
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLogHead(user).append(String.format("[MIX]店铺[%s, %s, %s]共拉取到%s笔rds订单,同步%s笔及时的rds订单,%s笔rds订单延迟需要调API,耗时%sms", user.getSource(), user.getId(), user.getNick(), rdsTrades.size(), importRdsTrades.size(), delayTrades.size(), (System.currentTimeMillis() - current))));
                }
                return delayTrades;
            }

            return pageTids;
        }
    }

    private List<Long> removeNoDelayTrades(User user, List<Trade> pageTrades, List<Trade> trades, List<RdsTradeJson> rdsTrades, List<Trade> importRdsTrades) {
        Map<String, Trade> tid2RdsTrade = TradeUtils.toMapByTid(trades);
        List<String> rdsDelayTids = Lists.newArrayList();
        List<String> apiDelayTids = Lists.newArrayList();

        Map<Long, RdsTradeJson> rdsTradeMap = new HashMap<>();
        rdsTrades.forEach(t -> rdsTradeMap.put(t.getTid(), t));

        Iterator<Trade> it = pageTrades.iterator();
        while (it.hasNext()) {
            Trade apiTrade = it.next();
            Long tidLong = Long.parseLong(apiTrade.getTid());
            Trade rdsTrade = tid2RdsTrade.get(apiTrade.getTid());
            if (TradeStatus.PAID_FORBID_CONSIGN.equals(apiTrade.getStatus()) && !tradeLocalConfig.isTbSyncPaidForbidConsign(user.getCompanyId())) {
                logger.error(LogHelper.buildLog(user.getStaff(), String.format("[MIX]拼团失败订单[%s]不下载", apiTrade.getTid())));
                continue;
            } else if (rdsTrade == null) {
                if(isXsdTradeAndNotExistUser(user, rdsTradeMap, tidLong)){
                    continue;
                }
                //rds还不存在
                rdsDelayTids.add(apiTrade.getTid());
                continue;
            }
            Date rdsModify = rdsTrade.getModified();
            Date apiModify = apiTrade.getModified();

            if (isRdsDelay(rdsTrade, apiTrade, rdsModify, apiModify)) {
                if(isXsdTradeAndNotExistUser(user, rdsTradeMap, tidLong)){
                    continue;
                }
                //rds延迟超6分钟
                rdsDelayTids.add(apiTrade.getTid());
            } else if (DateUtils.addMinutes(apiModify, 1).before(rdsModify)){
                //api滞后超10分钟
                apiDelayTids.add(apiTrade.getTid());
                importRdsTrades.add(rdsTrade);
            } else {
                //rds同步到的订单信息及时
                importRdsTrades.add(rdsTrade);
            }
        }

        if (apiDelayTids.size() > 0) {
            logger.warn(LogHelper.buildLogHead(user).append(String.format("[MIX][RDS]店铺[%s, %s, %s]%s笔api订单滞后:%s", user.getSource(), user.getId(), user.getNick(), apiDelayTids.size(), StringUtils.join(apiDelayTids, ","))));
        }
        if (rdsDelayTids.size() > 0) {
            logger.warn(LogHelper.buildLogHead(user).append(String.format("[MIX][RDS]店铺[%s, %s, %s]%s笔rds订单延迟:%s", user.getSource(), user.getId(), user.getNick(), rdsDelayTids.size(), StringUtils.join(rdsDelayTids, ","))));
        }
        return Lists.transform(rdsDelayTids, new Function<String, Long>() {
            @Nullable
            @Override
            public Long apply(String input) {
                return Long.valueOf(input);
            }
        });
    }

    private boolean isXsdTradeAndNotExistUser(User user, Map<Long, RdsTradeJson> rdsTradeMap, Long tid){
        RdsTradeJson rdsTradeJson = rdsTradeMap.get(tid);
        if(rdsTradeJson != null){
            try {
                TradeFullinfoGetResponse response = DubboRequstUtil.parseResponse(rdsTradeJson.getJdpResponse(), TradeFullinfoGetResponse.class);
                if(TbTradeUtils.isTbXsdTrade(response.getTrade())){
                    User xsdUser = getXsdUserIfXsdTrade(user, response.getTrade());
                    if(xsdUser == null){
                        return true;
                    }
                }
            } catch (Exception e){
                logger.error(LogHelper.buildLogHead(user, String.format("判断是否是小时达订单且没有绑定门店店铺的订单发生异常，tid:%s, rsp:%s", tid, rdsTradeJson.getJdpResponse())), e);
            }
        }
        return false;
    }

    private boolean isRdsDelay(Trade trade, Trade apiTrade, Date rdsModify, Date apiModify) {
        //不是拼团中订单
        return  !StringUtils.equals(TradeStatus.PAID_FORBID_CONSIGN, apiTrade.getStatus()) &&
                (!StringUtils.equals(trade.getStatus(), apiTrade.getStatus()) ||
                !isAddressEqual(trade.getReceiverAddress(), apiTrade.getReceiverAddress()) ||
//                !StringUtils.equals(trade.getSellerMemo(), apiTrade.getSellerMemo()) ||
                !trade.getSellerFlag().equals(apiTrade.getSellerFlag()) ||
                DateUtils.addMinutes(rdsModify, 6).before(apiModify));
    }


    private boolean isAddressEqual(String rds, String api) {
        if (StringUtils.isEmpty(rds) || StringUtils.isEmpty(api)) {
            return false;
        } else if (rds.length() != api.length()) {
            return false;
        }
        char[] rdsCharArray = rds.toCharArray();
        char[] apiCharArray = api.toCharArray();
        for (int i = 0; i < apiCharArray.length; i++) {
            if (apiCharArray[i] == '*') {
                continue;
            } else if (apiCharArray[i] != rdsCharArray[i]) {
                return false;
            }
        }
        return true;
    }

}

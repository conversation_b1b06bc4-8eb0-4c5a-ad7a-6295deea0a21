package com.raycloud.dmj.services.trades.im.ec;

import com.raycloud.dmj.business.trade.SmtqtgConfirmBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeUpdateService;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.CommonEventSource;
import com.raycloud.ec.api.EventSourceBase;
import com.raycloud.ec.api.IEventListener;
import com.raycloud.ec.api.annotation.ListenerBind;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
@ListenerBind(TradeEvents.TRADE_SMTQTG_FEEDBACK_CONFIRM)
public class SmtqtgFeedConfirmListener implements IEventListener {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource(name = "tbTradeSearchService")
    public ITradeSearchService tradeSearchService;
    @Resource
    public SmtqtgConfirmBusiness smtqtgConfirmBusiness;
    @Resource
    private ITradeUpdateService tradeUpdateService;

    @Override
    public void onObserved(EventSourceBase source) {
        CommonEventSource evt = (CommonEventSource) source;
        Staff staff = evt.getArg(0, Staff.class);
        try {
            Long[] sids = evt.getArg(1, Long[].class);
            List<Long> sidList = Arrays.stream(sids).collect(Collectors.toList());
            TradeConfigNew configNew = TradeConfigGetUtil.get(staff, TradeConfigEnum.SMTQTG_TRADE_CONFIRM);
            if(Objects.isNull(configNew) || !configNew.isOpen()){
                logger.debug(LogHelper.buildLog(staff, String.format("未开启速卖通全托管自动接单 trades=%s", sidList)));
                return;
            }
            List<List<Long>> lists = ListUtils.splitList(sidList, 100);
            for (List<Long> list : lists) {
                smtqtgConfirmBusiness.batchfeedBackSmtqtgConfirm(staff, list.toArray(new Long[0]));
            }

           /* List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, sids);
            if (CollectionUtils.isEmpty(trades)) {
                return;
            }
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("速卖通全托管 订单反馈确认结果 trades=%s", TradeUtils.toSidList(trades))));
            }

            for (Trade trade:trades){
                smtqtgConfirmBusiness.feedBackSmtqtgConfirm(staff,trade);
            }*/
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "速卖通全托管 订单反馈确认结果:" + e.getMessage()), e);
        }
    }

}

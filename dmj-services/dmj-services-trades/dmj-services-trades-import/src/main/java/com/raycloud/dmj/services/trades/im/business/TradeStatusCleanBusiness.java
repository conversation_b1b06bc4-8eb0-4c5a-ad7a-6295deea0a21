package com.raycloud.dmj.services.trades.im.business;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.sync.TradeSyncContext;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.trades.ITradeBusiness;
import com.raycloud.dmj.domain.trades.TradeImportResult;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.raycloud.dmj.domain.trade.utils.TradeUtils.*;

/**
 *
 */
@Component
public class TradeStatusCleanBusiness implements ITradeBusiness {

    /**
     * 插入操作,如果订单状态属于已发货之后的订单，则取消订单的异常状态以及挂起状态
     */
    @Override
    public List<Trade> handleInsert(Staff staff, User user, TradeSyncContext syncContext, List<Trade> trades, TradeImportResult tir) {
        return trades;
    }

    /**
     * 对更新订单时，如果订单状态属于已发货之后的订单，则取消订单的异常状态以及挂起状态
     */
    @Override
    public List<Trade> handleUpdate(Staff staff, User user, TradeSyncContext syncContext, List<Trade> trades, TradeImportResult tir) {
        for (Trade trade : trades) {
            if (TradeUtils.isAfterSendGoods(trade)) {
                TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.ADDRESS_CHANGED, 0L);
                TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.HALT, 0L);
                // 交易关闭不清空发货异常
                if (!Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus()) && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.DELIVER_EXCEPT)) {
                    TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.DELIVER_EXCEPT, 0L);
                }
                if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.SUITE_CHANGE)) {
                    TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.SUITE_CHANGE,0L);
                }
                if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.ITEM_PROCESS)) {
                    TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.ITEM_PROCESS,0L);
                }
                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.COD_REPEAT)) {
                    TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.COD_REPEAT, 0L);
                }
                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.WAIT_MERGE)) {
                    TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.WAIT_MERGE, 0L);
                }
                TradeExceptUtils.updateExcept(user.getStaff(),trade,ExceptEnum.PART_PAY_EXCEPT,0L);
                TradeExceptUtils.clearTradePointExcept(user.getStaff(),trade,ExceptEnum.INSUFFICIENT,false);
            }
            if (! (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) || TradeStatusUtils.isWaitFinanceAudit(trade.getSysStatus()) || isMerge(staff, trade) || isSplit(staff,trade))) {
                TradeExceptUtils.updateExcept(staff,trade, ExceptEnum.ADDRESS_CHANGED,0L);
            }
        }
        return trades;
    }
}

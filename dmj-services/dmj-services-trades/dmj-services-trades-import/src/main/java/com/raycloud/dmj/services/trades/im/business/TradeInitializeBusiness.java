package com.raycloud.dmj.services.trades.im.business;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.except.TradePlatModifyItemNumExceptionBusiness;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.operate.*;
import com.raycloud.dmj.business.payment.TradeSuitCalculateBusiness;
import com.raycloud.dmj.business.payment.support.PaymentCalculateSupports;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.channel.OrderChannel;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.presell.PresellConstant;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.trade.sync.TradeSyncConstant;
import com.raycloud.dmj.domain.trade.sync.TradeSyncContext;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.UserUtils;
import com.raycloud.dmj.except.domain.ExceptData;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.item.TradeItemContext;
import com.raycloud.ec.api.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trade.common.TradeTimeUtils.*;
import static com.raycloud.dmj.domain.trade.config.TradeConfigEnum.*;
import static com.raycloud.dmj.domain.utils.CommonConstants.*;

/**
 * 订单相关信息初始化业务类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2017-03-12 20:46
 */
@Component
public class TradeInitializeBusiness extends AbstractTradeBusiness {

    @Resource
    IdWorkerService guidService;

    @Resource
    PaymentCalculateBusiness paymentCalculateBusiness;

    @Resource
    PayAmountCalculateBusiness payAmountCalculateBusiness;

    @Resource
    IEventCenter eventCenter;

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    TradeTraceBusiness tradeTraceBusiness;

    @Resource
    FxBusiness fxBusiness;

    @Resource
    TradeSuitCalculateBusiness tradeSuitCalculateBusiness;

    @Resource
    TradeTimeoutContextBuilder tradeTimeoutContextBuilder;
    @Resource
    PaymentCalculateSupports paymentCalculateSupports;
    @Resource
    ITradeBlackService tradeBlackService;

    @Override
    public List<Trade> handleInsert(Staff staff, User user, TradeSyncContext syncContext, List<Trade> trades, TradeImportResult tir) {
        TradeConfig tradeConfig = getTradeConfig(staff, tir);
        for (Trade trade : trades) {
            if (TradeUtils.isGxOrMixTrade(trade)) {
                if (trade.hasOpV(OpVEnum.TRADE_FX_MATCH_LOW_PRICE_FREIGHT) && !syncContext.getConfigContext().isDmsAllowFxAppointTemplateId()) {
                    trade.removeOpV(OpVEnum.TRADE_FX_MATCH_LOW_PRICE_FREIGHT);
                }
                if (!syncContext.getConfigContext().isDmsAllowFxAppointTemplateId()) {
                    trade.removeV(TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID);
                }
            }
            initTrade(staff, syncContext, trade);
            if (UserUtils.isKdzs(user)) {
                trade.addV(TradeConstants.V_IF_KDZS);
            }
            initShopPreSell(syncContext.getConfigContext().isShopPresellMatchTag(), trade);
            TradeUtils.setTimeoutActionTime(trade, tradeConfig, tir.getTradeUserConfig());//设置承诺发货时间
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                initOrder(staff, trade, order, syncContext.getConfigContext().isFillSkuWithOrderRemark());
                initPlatformGiftOrder(user, order, syncContext.getConfigContext().getPlatformGiftPickConfig());
            }
            TradeUtils.resetAddressType(trade);//计算地址类型
            handleStore(user, trade);
            String sysStatus = TradeStatus.getSysStatus(trade.getStatus(), null);
            if (!Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus)) {
                payAmountCalculateBusiness.handleInsert(user.getStaff(), trade, null);
                paymentCalculateSupports.calculatePlatformDiscountFee(user.getStaff(), trade);
            }
            carAttrMemoHandle(staff, trade, syncContext.getConfigContext().isOpenChexingAddMemo());

            PaymentUtils.init(trade, orders, syncContext.getConfigContext().isOpenPlatformDivideOrderFee(), true);
            initOrderExtCustomization(staff, trade);
            initO2oTradeOutSid(trade);
        }
        handleBicTrade(trades, tir);
        handleTimeoutActionTime(staff, trades);
        //标记黑名单
        tradeBlackService.check(user.getStaff(), trades);
        return trades;
    }

    void initPlatformGiftOrder(User user, Order order, Integer configValue) {
        if (TradeSyncConstant.GIFT_ORDER_PICK_PLATFORMS.contains(user.getSource())) {
            if (order.isPlatformGift()) {
                if (Objects.nonNull(configValue)) {
                    order.setIsPick(configValue);
                } else {
                    // https://gykj.yuque.com/docs/share/420fc6a2-4374-4622-8ed5-4ea371eaf913
                    // 如果是平台赠品，没有配置参与拣选与验货的值，默认就设置为 参与拣选和不验货（isPick = 1）
                    order.setIsPick(1);
                }
            }
        }
    }

    @Override
    public List<Trade> handleUpdate(Staff staff, User user, TradeSyncContext syncContext, List<Trade> trades, TradeImportResult tir) {
        TradeConfig tradeConfig = getTradeConfig(staff, tir);
        for (Trade trade : trades) {
            if (trade.hasOpV(OpVEnum.TRADE_FX_MATCH_LOW_PRICE_FREIGHT) && !syncContext.getConfigContext().isDmsAllowFxAppointTemplateId()) {
                trade.removeOpV(OpVEnum.TRADE_FX_MATCH_LOW_PRICE_FREIGHT);
            }
            TradeUtils.setTimeoutActionTime(trade, tir.getTradeConfig(), tir.getTradeUserConfig());//设置承诺发货时间
            // 之前为待付款，状态更新
            if (TradeStatusUtils.isWaitPay(trade.getOldSysStatus()) &&
                    TradeStatus.getStatusWeight(trade.getStatus()) - TradeStatus.getStatusWeight(trade.getOrigin().getStatus()) > 0) {
                initShopPreSell(syncContext.getConfigContext().isShopPresellMatchTag(), trade);
                PaymentUtils.init(trade, TradeUtils.getOrders4Trade(trade), syncContext.getConfigContext().isOpenPlatformDivideOrderFee(), false);
                for (Order order : TradeUtils.getOrders4Trade(trade)) {
                    if (order.getEstimateConTime() == null) {
                        order.setEstimateConTime(trade.getTimeoutActionTime());
                        if (CollectionUtils.isNotEmpty(order.getSuits())) {
                            order.getSuits().forEach(suit -> suit.setEstimateConTime(trade.getTimeoutActionTime()));
                        }
                    }
                }
            }
        }
        TradeItemContext tradeItemContext = new TradeItemContext();
        tradeItemContext.setTradeConfig(tradeConfig);
        for (Trade trade : trades) {
            Trade originTrade = trade.getOrigin();
            if (originTrade != null) {
                //拆分订单取消作废不执行分摊逻辑
                if ((TradeUtils.isSplit(trade) || TradeUtils.isSplit(originTrade)) && tir.getTradeOperateEnum() != null && tir.getTradeOperateEnum() == TradeOperateEnum.TRADE_OPERATE_CANCEL_UNDO) {
                    continue;
                }
                String sysStatus = TradeStatus.getSysStatus(trade.getStatus(), null);
                if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(originTrade.getSysStatus()) &&
                        !Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus)) {//待付款变已付款实付金额分摊
                    trade.setAcPayment(trade.getPayment());
                    PaymentUtils.init(trade, TradeUtils.getOrders4Trade(trade), syncContext.getConfigContext().isOpenPlatformDivideOrderFee(), false);
                    paymentCalculateBusiness.sharePaymentAssemble(staff, tir.getTradeConfig(), trade);
                    Logs.debug(String.format("再次指定下载订单金额逻辑处理 sid:[{%s}], payment:[{%s}], payamount:[{%s}]", trade.getSid(), trade.getPayment(), trade.getPayAmount()));
                    payAmountCalculateBusiness.handleInsert(staff, trade, null);
                    paymentCalculateSupports.calculatePlatformDiscountFee(staff, trade);
                    tradeSuitCalculateBusiness.calculate(staff, TradeUtils.getOrders4Trade(trade), tradeItemContext);
                }
                //快团团sysMemo处理
                handleKTTSysMemo(user, trade);
            }
            initO2oTradeOutSid(trade);
        }
        handleBTASTradeExt(staff, syncContext, trades);
        handleBicTrade(trades, tir);

        handleTimeoutActionTime(staff, trades);
        return trades;
    }

    /**
     * https://gykj.yuque.com/entavv/xb9xi5/ek5gg86m35srwu66
     * 团号-订单初次同步后将团号放入系统备注中；防止被误操作删掉，订单再次同步的时候判断系统备注是否有值，有的话则不再同步团号到系统备注；系统备注为空同步团号到系统备注中
     */
    private void handleKTTSysMemo(User user, Trade trade) {
        if (!PLAT_FORM_TYPE_KTT.equals(user.getSource())) {
            return;
        }
        Trade originTrade = trade.getOrigin();
        if (PLAT_FORM_TYPE_KTT.equals(trade.getSource()) && Objects.nonNull(originTrade) && StringUtils.isNotBlank(originTrade.getSysMemo())) {
            trade.setSysMemo(originTrade.getSysMemo());
        }
    }

    private void handleStore(User user, Trade trade) {
        //开启星盘分单的前提下判断是否门店分单
        if (user.getUseXingpan() != null && user.getUseXingpan() - 1 == 0 && trade.getChannels() != null && !trade.getChannels().isEmpty()) {
            //目前根据第一个OrderChannel判断是否为门店分单
            OrderChannel channel = trade.getChannels().get(0);
            trade.setIsStore("STORE".equals(channel.getTargetType()) ? 1 : 0);
        }
    }

    private void initTrade(Staff staff, TradeSyncContext syncContext, Trade trade) {
        if (PLAT_FORM_TYPE_QIMEN.equals(trade.getSource()) && trade.getSid() != null) {
            Logs.debug(LogHelper.buildLog(staff, "奇门订单同步指定系统单号,sid:" + trade.getSid()));
        } else {
            trade.setSid(guidService.nextId());
        }
        trade.setMergeSid(-1L);
        trade.setSplitSid(-1L);
        ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(trade);
        trade.setExceptData(tradeExceptData);

        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.HALT, 0L);
        trade.setIsCancel(0);
        trade.setIsUrgent(0);
        trade.setAuditTime(INIT_DATE);
        trade.setMergeType(TradeMergeEnum.MERGE_NORMAL.getDbType());
        trade.setSplitType(TradeSplitEnum.SPLIT_NORMAL.getDbType());
        trade.setExpressPrintTime(INIT_DATE);
        trade.setDeliverPrintTime(INIT_DATE);
        trade.setAssemblyPrintTime(INIT_DATE);
        trade.setScalping(0);
        trade.setCanDelivered(9);
        trade.setIsPackage(0);
        trade.setIsWeigh(0);
        if (trade.getIsPresell() == null || PresellConstant.SYSTEM_PRESELL_ORDER != trade.getIsPresell()) {
            trade.setIsPresell(0);
        }
        trade.setScalping(0);
        String sysStatus = TradeStatus.getSysStatus(trade.getStatus(), null);
        trade.setIsUpload(TradeConstants.CONSIGN_UPLOAD_NO);
        if (trade.getPayTime() == null) {
            trade.setPayTime(INIT_DATE);
        }
        if (trade.getConsignTime() == null) {
            trade.setConsignTime(INIT_DATE);
        }
        if (TradeUtils.isGxOrMixTrade(trade)) {
            trade.setCreated(new Date());
            Trade sourceTrade = trade.getSourceTrade();
            // 分销订单已经选择了供销的快递模板
            if (TradeUtils.ifContainV(trade, TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID) && sourceTrade != null) {
                trade.setTemplateType(sourceTrade.getTemplateType());
                trade.setTemplateId(sourceTrade.getTemplateId());
                trade.setTemplateName(sourceTrade.getTemplateName());
                trade.setExpressCompanyId(sourceTrade.getExpressCompanyId());
                trade.setLogisticsCompanyName(sourceTrade.getLogisticsCompanyName());
                trade.setLogisticsCompanyId(sourceTrade.getLogisticsCompanyId());
                fxBusiness.saveFxAppointTemplate(staff, trade);
                trade.addOpV(OpVEnum.TRADE_FX_APPOINT_TEMPLATE_ID);
            }
        }
        trade.addOpV(OpVEnum.TRADE_IMPORT_INSERT_CHAIN);
        if (trade.getEndTime() == null) {
            if (Trade.SYS_STATUS_FINISHED.equals(sysStatus) || Trade.SYS_STATUS_CLOSED.equals(sysStatus)) {
                trade.setEndTime(trade.getModified());
            } else {
                trade.setEndTime(INIT_DATE);
            }
        }
        if (TradeUtils.isGxOrMixTrade(trade) && Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getFxSysStatus())) {
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_UNAUDIT, 1L);
            Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s标记[%s]异常", trade.getSid(), ExceptEnum.FX_UNAUDIT.getChinese())));
        }
        trade.setGrossProfitRate("0");
        TradeUtils.setMobileTail(trade);
        //BTAS订单的配置信息处理
        initBtasTradeExt(syncContext, trade);
    }

    private void initBtasTradeExt(TradeSyncContext syncContext, Trade trade) {
        if (!TradeTypeUtils.isFxgBtasTrade(trade)) {
            return;
        }
        TradeExt tradeExt = trade.getTradeExt();
        if (tradeExt == null) {
            tradeExt = new TradeExt();
            tradeExt.setCompanyId(trade.getCompanyId());
            tradeExt.setUserId(trade.getUserId());
            tradeExt.setSid(trade.getSid());
            tradeExt.setTid(trade.getTid());
            trade.setTradeExt(tradeExt);
        }
        //BTAS订单在下载时，保存BTAS配置的快照信息
        TradeExtUtils.setExtraFieldValue(tradeExt, BTAS_EXPRESS_PRODUCT.getConfigKey(), syncContext.getConfigContext().getBtasExpressProduct());
        TradeExtUtils.setExtraFieldValue(tradeExt, BTAS_INSURED_PRICE.getConfigKey(), syncContext.getConfigContext().getBtasInsuredPrice());
        TradeExtUtils.setExtraFieldValue(tradeExt, BTAS_GOODS_REJECTED_INTERCEPT.getConfigKey(), syncContext.getConfigContext().getBtasGoodsRejectedIntercept());
        TradeExtUtils.setExtraFieldValue(tradeExt, BTAS_QUALITY_CHECK_RETURN_TYPE.getConfigKey(), syncContext.getConfigContext().getBtasQualityCheckReturnType());

        int insuranceCostType = 0;
        if (StringUtils.equals("1", syncContext.getConfigContext().getBtasInsuredPrice())) {
            Set<Long> btasInsuredShopIds = syncContext.getConfigContext().getBtasInsuredShopIds();
            if (CollectionUtils.isEmpty(btasInsuredShopIds) || !btasInsuredShopIds.contains(trade.getUserId())) {
                insuranceCostType = 2;
            } else {
                insuranceCostType = 1;
            }
        }

        TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.INSURANCE_COST_TYPE.getField(), insuranceCostType);
        if (insuranceCostType != 2) {
            TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.INSURED_PRICE.getField(), 0.00D);
        }
    }

    void initOrder(Staff staff, Trade trade, Order order, boolean fillSkuWithOrderRemark) {
        order.setId(guidService.nextId());
        order.setExceptData(trade.getExceptData());
        if (order.getOid() == null) {//京东的子订单没有oid，所以直接使用order.id
            order.setOid(order.getId());
        }
        order.setSid(trade.getSid());

        //非平台预售 更新orderIsPresell
        if (order.isCoverTradePresell()) {
            order.setIsPresell(trade.getIsPresell());
        }
        order.setItemSysId(-1L);
        order.setSkuSysId(-1L);

        if (order.getNumIid() != null && order.getNumIid().length() == 1 && NumberUtils.isNumber(order.getNumIid())) {
            //numiid为个位数的做虚拟商品处理
            order.setIsVirtual(1);
            order.setItemSysId(1L);
            order.setSkuSysId(1L);
            OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_NORMAL);
            order.setSysPicPath(order.getPicPath());
            order.setSysTitle(order.getTitle());
        } else {
            order.setIsVirtual(0);
        }
        order.setType(Order.TypeOfNormal);
        order.setCombineId(0L);
        OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.RELATION_CHANGED, 0L);
        order.setInsufficientCanceled(0);
        if (order.getPayTime() == null) {
            order.setPayTime(trade.getPayTime());
        }
        if (order.getConsignTime() == null) {
            order.setConsignTime(INIT_DATE);
        }
        if (order.getEndTime() == null) {
            order.setEndTime(trade.getEndTime());
        }
        //设置order 创建时间内,如果没有取trade
        order.setCreated(order.getCreated() != null ? order.getCreated() : trade.getCreated());
        // 微信视频号这里不用做填充
        if (order.getEstimateConTime() == null && !PLAT_FORM_TYPE_WXSPH.equals(order.getSource())) {
            order.setEstimateConTime(trade.getTimeoutActionTime());
        }

        if (fillSkuWithOrderRemark) {
            fillSkuWithOrderRemark(order);
        }
    }

    private void carAttrMemoHandle(Staff staff, Trade toUpdate, boolean openChexingAddMemo) {
        if (openChexingAddMemo && StringUtils.isNotBlank(toUpdate.getCarAttrMemo())) {
            toUpdate.setSysMemo(StringUtils.isNotBlank(toUpdate.getSysMemo()) ? (toUpdate.getSysMemo() + toUpdate.getCarAttrMemo()) : toUpdate.getCarAttrMemo());
            if (null != toUpdate.getSysMemo() && toUpdate.getSysMemo().length() > 256) {
                toUpdate.setSysMemo(toUpdate.getSysMemo().substring(0, 256));
            }
            String content = "自动匹配系统备注: " + toUpdate.getSysMemo();
            List<Trade> tradeList = Collections.singletonList(TradeTraceUtils.simplifyWithTrace(toUpdate, content));
            eventCenter.fireEvent(this, new EventInfo("trade.custom.trace.save").setArgs(new Object[]{staff, "自动匹配系统备注"}), tradeList);
        }
    }

    private void handleBicTrade(List<Trade> trades, TradeImportResult tir) {
        for (Trade trade : trades) {
            if (TradeUtils.isFxgBicTrade(trade, tir.getTradeConfig())) {
                tir.bicTradeInitExpressSidSet.add(trade.getSid());
            }
        }
    }

    private void handleBTASTradeExt(Staff staff, TradeSyncContext syncContext, List<Trade> trades) {
        try {
            List<TradeExt> insertTradeExt = new ArrayList<>();
            for (Trade trade : trades) {
                Trade originTrade = trade.getOrigin();
                if (originTrade == null) {
                    continue;
                }
                if (TradeTypeUtils.isFxgBtasTrade(trade) && !TradeTypeUtils.isFxgBtasTrade(originTrade)) {
                    TradeExt tradeExt = trade.getTradeExt();
                    if (tradeExt == null) {
                        if (null == originTrade.getTradeExt()) {
                            tradeExt = new TradeExt();
                            tradeExt.setCompanyId(trade.getCompanyId());
                            tradeExt.setUserId(trade.getUserId());
                            tradeExt.setSid(trade.getSid());
                            tradeExt.setTid(trade.getTid());
                            insertTradeExt.add(tradeExt);
                        } else {
                            tradeExt = originTrade.getTradeExt();
                        }
                        trade.setTradeExt(tradeExt);
                    }
                    //BTAS订单在初次打上标志时，保存BTAS配置的快照信息
                    initBtasTradeExt(syncContext, trade);
                    //系统日志构建
                    tradeTraceBusiness.btasConfigMatchLog(trade);
                }
            }
            if (!CollectionUtils.isEmpty(insertTradeExt)) {
                tradeExtDao.batchInsert(staff, insertTradeExt);
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(staff, e, "BTAS订单配置匹配失败"), e);
        }
    }

    /**
     * @description: 店铺预售初始化
     */
    private void initShopPreSell(boolean configFlag, Trade trade) {
        boolean sourceFlag = PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource()) || PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource());
        //未开启配置；source不是是天猫、淘宝；或者是预售订单
        if (!configFlag || !sourceFlag || "step".equals(trade.getType())) {
            return;
        }
        // 更新时判断该字段是否为true，多平台赋值
        if (trade.isExistEstimateConTime()) {
            trade.setType(SHOP_PRESELL_TAG);
            TradeUtils.getOrders4Trade(trade)
                    .stream()
                    .filter(Order::isExistEstimateConTime)
                    .forEach(order -> {
                        order.setIsPresell(4);
                    });
        }
    }

    /**
     * 填充商品规格到订单商品备注
     */
    private void fillSkuWithOrderRemark(Order order) {
        OrderExt orderExt = order.getOrderExt();
        if (orderExt == null) {
            orderExt = new OrderExt();
            order.setOrderExt(orderExt);
        }
        if (StringUtils.isNotBlank(order.getSkuPropertiesName())) {
            orderExt.setOrderRemark(order.getSkuPropertiesName());
        }
    }

    /**
     * 订单初始化处理承诺时间
     */
    private void handleTimeoutActionTime(Staff staff, List<Trade> trades) {
        for (Trade trade : trades) {
            // 这里先对系统order同步一次对应平台order的承诺时间
            updateSysOrderEstimateConTime(staff, trade);
            Date oldTimeoutActionTime = trade.getTimeoutActionTime();
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                // 合单要用trade的承诺时间对比
                TradeEsConTimeUtils.handleTimeoutActionTime(tradeTimeoutContextBuilder.build(staff), trade, true);
            } else {
                TradeEsConTimeUtils.handleTimeoutActionTime(tradeTimeoutContextBuilder.build(staff), trade);
            }
            Date nowTimeoutActionTime = trade.getTimeoutActionTime();
            if (oldTimeoutActionTime != null && nowTimeoutActionTime != null) {
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) && nowTimeoutActionTime.after(oldTimeoutActionTime)) {
                    Logs.debug(LogHelper.buildLog(staff, String.format(" handleTimeoutActionTime () 合单承诺时间变动 sid:%s,mergeSid:%s,oldTimeoutActionTime:%s,nowTimeoutActionTime:%s 新的时间大于旧的承诺时间不覆盖", trade.getSid(), trade.getMergeSid(), oldTimeoutActionTime, nowTimeoutActionTime)));
                    trade.setTimeoutActionTime(oldTimeoutActionTime);
                } else {
                    String nowTimeoutActionTimeStr = time2Str(nowTimeoutActionTime);
                    String oldTimeoutActionTimeStr = time2Str(oldTimeoutActionTime);
                    if (!StringUtils.equals(nowTimeoutActionTimeStr, oldTimeoutActionTimeStr)) {
                        Logs.debug(LogHelper.buildLog(staff, String.format(" handleTimeoutActionTime () 承诺时间变动 sid:%s,oldTimeoutActionTime:%s,nowTimeoutActionTime:%s", trade.getSid(), oldTimeoutActionTimeStr, nowTimeoutActionTimeStr)));
                    }
                }

            }
        }
    }


    private void initOrderExtCustomization(Staff staff, Trade trade) {
        if (!TradePlatModifyItemNumExceptionBusiness.SUPPORT_PLATFORMS.contains(trade.getSource())) {
            return;
        }
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders4Trade) {
            OrderExt orderExt = Optional.ofNullable(order.getOrderExt()).orElse(new OrderExt());
            String customization = orderExt.getCustomization();
            Integer syncPlatOrderNum = orderExt.getSyncPlatOrderNum();
            if (syncPlatOrderNum == null) {
                Logs.debug(LogHelper.buildLog(staff, String.format("oid=%s source=%s customization=%s 初始化时,不存在平台同步orderNum ", order.getOid(), trade.getSource(), customization)));
                continue;
            }
            orderExt.setFirstRecordOrderNum(syncPlatOrderNum);
        }
    }


    /**
     * 平台承诺时间变动，系统order跟随平台order变动
     */
    private void updateSysOrderEstimateConTime(Staff staff, Trade trade) {
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        Map<Long, Order> orderOidMap = orders4Trade.stream().filter(order -> !Objects.equals(order.getSource(), PLAT_FORM_TYPE_SYS)).collect(Collectors.toMap(Order::getOid, Function.identity(), (a, b) -> a));
        if (MapUtils.isEmpty(orderOidMap)) {
            return;
        }
        StringBuilder log = new StringBuilder();
        for (Order order : orders4Trade) {
            Order platOrder = orderOidMap.get(order.getOid());
            if (platOrder == null || !PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
                continue;
            }
            String platEstimateConTime = time2Str(platOrder.getEstimateConTime());
            if (StringUtils.isBlank(platEstimateConTime)) {
                Logs.debug(LogHelper.buildLog(staff, String.format("id=%s 平台order的承诺时间不存在!", platOrder.getId())));
                continue;
            }
            String sysEstimateConTime = time2Str(order.getEstimateConTime());
            if (!StringUtils.equals(platEstimateConTime, sysEstimateConTime)) {
                order.setEstimateConTime(platOrder.getEstimateConTime());
                log.append("[sysOrderId=").append(order.getId()).append(",estimateConTime=").append(sysEstimateConTime).append(",platOrderId=").append(platOrder.getId()).append(",platEstimateConTime=").append(platEstimateConTime).append("];");
            }
        }
        if (log.length() > 0) {
            Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s order 的承诺时间变动，系统order跟平台order同步 %s", trade.getSid(), log)));
        }
    }

    /**
     * o2o 订单会把 sid 设置为运单号
     */
    private void initO2oTradeOutSid(Trade trade) {
        if (!PlatformUtils.isTradeO2o(trade)) {
            return;
        }
        trade.setOutSid(String.valueOf(trade.getSid()));
    }

}

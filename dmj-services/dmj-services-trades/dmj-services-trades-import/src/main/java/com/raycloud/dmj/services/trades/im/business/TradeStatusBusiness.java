package com.raycloud.dmj.services.trades.im.business;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.raycloud.dmj.*;
import com.raycloud.dmj.business.common.InfoChangeLogBusiness;
import com.raycloud.dmj.business.fx.*;
import com.raycloud.dmj.business.logistics.*;
import com.raycloud.dmj.business.operate.PaymentCalculateBusiness;
import com.raycloud.dmj.business.trade.TradePlatStatusChangeBusiness;
import com.raycloud.dmj.dao.trade.*;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.*;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.pt.OutSidPool;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.config.*;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.sync.TradeSyncContext;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.AbsLogBuilder;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.utils.diamond.TradeConsignConfigDiamondUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.pt.*;
import com.raycloud.dmj.services.trade.audit.old.AuditConsignBusiness;
import com.raycloud.dmj.services.trade.except.plat.PlatExceptSyncBusiness;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.support.TbTradeSearchService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.utils.except.TradeSyncExceptHandlerUtils;
import com.raycloud.ec.api.*;
import org.apache.commons.collections.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trade.common.TradeTimeUtils.INIT_DATE;
import static com.raycloud.dmj.services.trade.except.plat.PlatModifyExchangeItemExceptSyncService.*;

/**
 * 设置订单的sysStatus
 *
 * <AUTHOR>
 */
@Component
public class TradeStatusBusiness extends AbstractTradeBusiness {

    private final Logger logger = Logger.getLogger(getClass());

    @Resource
    PaymentCalculateBusiness paymentCalculateBusiness;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    FxBusiness fxBusiness;

    @Resource
    SysOrderStatusMappingBusiness sysOrderStatusMappingBusiness;

    @Resource
    UploadRecordBusiness uploadRecordBusiness;
    @Resource
    FxCashFlowBusiness fxCashFlowBusiness;

    @Resource
    TbTradeSearchService tbTradeSearchService;

    @Resource
    TradeSysLabelBusiness tradeTagUpdateBusiness;

    @Resource
    IEventCenter eventCenter;

    @Resource
    TradeDelayProcessRecordDao delayProcessRecordDao;

    @Resource
    PlatExceptSyncBusiness platExceptSyncBusiness;

    @Resource
    LogisticsWarningBusiness logisticsWarningBusiness;

    @Resource
    IOutSidPoolService outSidPoolService;
    @Resource
    TradePlatStatusChangeBusiness tradePlatStatusChangeBusiness;

    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Override
    public List<Trade> handleInsert(Staff staff, User user, TradeSyncContext syncContext, List<Trade> trades, TradeImportResult tir) {

        TradeConfig tradeConfig = getTradeConfig(staff, tir);
        boolean shareDiscount = TradeConfig.needAutoShare(tradeConfig);

        Integer autoUnHookDelayShipTime = getDelayShipTime(staff, tradeConfig);
        for (Trade trade : trades) {
            if (AuditConsignBusiness.consignConsumeStockSource(staff, user)) {
                // 快团团订单只有核销订单才直接发货
                if (Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_KTT)) {
                    if (TradeUtils.isKttSelfAndCancelledTrade(trade)) {
                        tir.auditConsignSids.add(trade.getSid());
                    }
                } else {
                    if(Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_YYJK)){
                    }else{
                        tir.auditConsignSids.add(trade.getSid());
                    }
                }
            }
            if (TradeUtils.isTbXsdTrade(trade)&&!TradeUtils.isTbXsdB2cTrade(trade)) {
                if (tradeConfigService.getTbXsdUploadSet(tradeConfig) == 1) {
                    tir.auditConsignSids.add(trade.getSid());
                }
            }


            boolean isStore = trade.getIsStore() != null && trade.getIsStore() - 1 == 0;
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                //如果是供销订单并且对应的分销订单已经上传发货过了
                if (OrderUtils.isGxOrMixOrder(order) && ((trade.getFxIsUpload() != null && trade.getFxIsUpload() == 1) || order.isFxSysTriggerUpload()) && StatusUpdateUtils.isSellerSendGoodsOrFinished(TradeStatus.getSysStatus(order.getStatus(), null))) {
                    order.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
                } else {
                    order.setSysStatus(getSysStatus(order.getStatus(), isStore));
                }
                String stockStatus = TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) ? Trade.STOCK_STATUS_NORMAL : Trade.STOCK_STATUS_UNALLOCATED;
                OrderExceptUtils.setStockStatus(staff, order, stockStatus);
            }
            String sysStatus = TradeStatusUtils.getTradeSysStatus(trade);
            if (isStore) {
                trade.setSysStatus(Trade.SYS_STATUS_CLOSED.equals(sysStatus) ? sysStatus : Trade.SYS_STATUS_FINISHED);
            } else {
                trade.setSysStatus(sysStatus);
            }
            //设置实付金额
            trade.setAcPayment(Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(sysStatus) ? "0": trade.getPayment());

            trade.setEndTime(Trade.SYS_STATUS_FINISHED.equals(sysStatus) || Trade.SYS_STATUS_CLOSED.equals(sysStatus) ? trade.getModified() : INIT_DATE);

            TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);//根据子订单库存状态计算订单库存状态
            if (isStore) {
                Logs.ifDebug(LogHelper.buildLogHead(user).append(String.format("同步到门店订单[sid=%s,tid=%s,status=%s],系统状态自动变为[%s]", trade.getSid(), trade.getTid(), trade.getStatus(), trade.getSysStatus())));
            }
            dewuPlatformAutoHook(staff, autoUnHookDelayShipTime, trade, true);
            if (shareDiscount) {
                paymentCalculateBusiness.shareDiscount(staff, trade);
            }
        }
        //检查是否要同步待付款订单，如果不需要则删除待付款订单
        checkIfSyncWaitPay(trades, tir.getTradeConfig());
        TradeSysConsignUtils.handleSysConsigned(trades);
        // 其他erp发货的需要保存运单号的关系
        saveTradeOutSidMapping(staff, trades, tir);
        handlePtConsignTime(user, trades);
        handerPlatExcept(staff, user, trades, tir, true);
        return trades;
    }

    /**
     * @param tradeConfig
     * @return java.lang.Integer
     * <AUTHOR>
     * @description 获取毒平台自动解挂时间
     * @date 2021/5/12 4:47 下午
     */
    private Integer getDelayShipTime(Staff staff, TradeConfig tradeConfig) {
        String dewuDelayShipTimeStr = tradeConfig.getString(TradeExtendConfigsEnum.DEWU_PLATFORM_DELAY_SHIP_TIME.getKey());
        int dewuDelayShipTime = 0;
        if (StringUtils.isNotBlank(dewuDelayShipTimeStr)) {
            try {
                dewuDelayShipTime = Integer.parseInt(dewuDelayShipTimeStr.trim());
            } catch (NumberFormatException e) {
                Logs.error(LogHelper.buildErrorLog(staff, e, String.format("毒平台自动挂起时长解析失败 analysisValue=[%s]", dewuDelayShipTimeStr)));
            }
        }
        return dewuDelayShipTime;
    }

    /**
     * @param delayShipTime
     * @param trade
     * @param isInsert      是否新增trade
     * <AUTHOR>
     * @description 得物平台自动挂起
     * @date 2021/5/12 3:52 下午
     */
    private void dewuPlatformAutoHook(Staff staff, Integer delayShipTime, Trade trade, Boolean isInsert) {
        boolean flag = isInsert || TradeStatusUtils.waitPay2WaitAudit(trade);
        if (!flag) {
            return;
        }
        //毒（得物、云仓）平台开启延迟发货，设置订单挂起状态，trade拓展表设置挂起时间
        flag = Objects.nonNull(delayShipTime)
                && (CommonConstants.PLAT_FORM_TYPE_YUNCANG.equals(trade.getSource()) || CommonConstants.PLAT_FORM_TYPE_POISON.equals(trade.getSource()))
                && delayShipTime > 0;
        if (!flag) {
            return;
        }
        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.HALT, 1L);
        Date autoUnHookTime = DateUtils.addHours(trade.getPayTime(), delayShipTime);
        //新增或者由待支付变为已支付
        TradeDelayProcessRecord delayProcessRecord = new TradeDelayProcessRecord();
        delayProcessRecord.setCompanyId(trade.getCompanyId());
        delayProcessRecord.setUserId(trade.getUserId());
        delayProcessRecord.setSource(trade.getSource());
        delayProcessRecord.setSid(trade.getSid());
        delayProcessRecord.setBusinessType(TradeConstants.STATUS_EXCEP_HALT);
        delayProcessRecord.setOccurTime(new Date());
        delayProcessRecord.setOccurTimeUnit(2);
        delayProcessRecord.setOccurTimeDelayNum(delayShipTime);
        delayProcessRecord.setExpTime(autoUnHookTime);
        delayProcessRecord.setEnableStatus(1);
        delayProcessRecordDao.insert(staff, delayProcessRecord);
    }

    private void checkIfSyncWaitPay(List<Trade> trades, TradeConfig tradeConfig) {
        if (tradeConfig.getSyncWaitPay() != null && tradeConfig.getSyncWaitPay() == 0) {
            trades.removeIf(trade -> Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getSysStatus()));
        }
    }

    String getSysStatus(String status, boolean isStore) {
        String sysStatus = TradeStatus.getSysStatus(status, null);
        //非关闭的门店订单，自动将系统状态设置为已完成
        return isStore ? (Trade.SYS_STATUS_CLOSED.equals(sysStatus) ? sysStatus : Trade.SYS_STATUS_FINISHED) : sysStatus;
    }

    @Override
    public List<Trade> handleUpdate(Staff staff, User user, TradeSyncContext syncContext, List<Trade> trades, TradeImportResult tir) {

        TradeConfig tradeConfig = tir.getTradeConfig();
        boolean shareDiscount = TradeConfig.needAutoShare(tir.getTradeConfig());
        boolean isCover = StatusUpdateUtils.isCover(user, tradeConfig);
        Set<Trade> notCloseTrades = new HashSet<>(trades.size()),
                notOtherERPConsignedTrades = new HashSet<>(trades.size());
        Set<Long> zeroSysConsignedOrderIds = new HashSet<>(trades.size());
        fillBefore(trades, notCloseTrades, notOtherERPConsignedTrades, zeroSysConsignedOrderIds);

        sysOrderStatusMappingBusiness.mapSysOrderPlatStatus(staff, trades);
        // 获取系统中已经被使用的运单号
        Map<String, List<Long>> sysExistOutSidsMap = getSysExistOutSid(staff, trades);
        // 获取系统获取的运单号
        List<String> sysOutSids = getSysOutSids(staff, trades);
        //通过发货记录来这设置sysTriggerUpload,这个值在后面的状态更新需要用来进行断言判断
        uploadRecordBusiness.fillUploadRecord(staff, trades);
        //同步订单码失败时，BTAS订单状态处理
        modifyBtasTradeStatus(trades);
        Integer autoUnHookDelayShipTime = getDelayShipTime(staff, tradeConfig);
        List<Trade> needRemoveLabelTrades = new ArrayList<>();
        for (Trade trade : trades) {
            TradeTimeUtils.setEndTime(staff, trade, trade.getOrigin());
            //店加的isCover 跟订单类型有关 type = 2 缺货 KMERP-203446
            if (Objects.equals(CommonConstants.PLAT_FORM_TYPE_DIANPLUS, trade.getSource()) && Objects.equals("2", trade.getType())) {
                isCover = true;
            }

            StatusUpdateUtils.handleUpdate(staff, trade, tir, isCover, sysOutSids);

            handleV(staff, trade);
            checkPartRefund(staff, trade, tir);
            checkFxPtStatusChange(staff, trade, tir);
            undoMerge(staff, trade, tir);
            fxCashFlowBusiness.checkGxTradeForCashFlowUpdate(staff, trade, tir);
            if (shareDiscount && Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getOldSysStatus()) && Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus())) {
                paymentCalculateBusiness.shareDiscount(staff, trade);
            }
            //如果订单已打印且未发货，同步为交易关闭，则标记上传异常
            processDeliverException(staff, trade);

            //如果订单的平台状态由未发货变为已发货 要取消即将超时标签
            if (trade.getOrigin() != null && !UploadUtils.platformIsConsigned(null, trade.getOrigin()) && UploadUtils.platformIsConsigned(null, trade)) {
                needRemoveLabelTrades.add(trade);
                if (TradeUtils.isPoisonBrandDeliverPerformanceTrade(trade)) {//得物直发订单已打印已审核如果单号和平台一致  不同步为其他ERP发货
                    if (trade.getOrigin() != null && TradeStatusUtils.getPrintStatus(trade.getOrigin().getExpressPrintTime()) > 0 && Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getOrigin().getSysStatus())) {
                        if (Objects.nonNull(trade.getOutSid()) && trade.getOutSid().equals(trade.getOrigin().getOutSid())) {
                            Map<Long, String> infoMap = Maps.newHashMap();
                            infoMap.put(trade.getSid(), "得物品牌直发订单，快递揽收后触发平台发货");
                            List<Trade> infoTrades = new ArrayList<>();
                            infoTrades.add(trade);
                            eventCenter.fireEvent(this, new EventInfo("trade.upload.info").setArgs(new Object[]{staff, infoMap}), infoTrades);
                        }
                    }
                }
            }
            dewuPlatformAutoHook(staff, autoUnHookDelayShipTime, trade, false);
            handleParentAndChildOrder(trade, tir, staff);
            orderSysStatus2Close(staff, trade, tir);
        }
        tradeTagUpdateBusiness.removeTags(staff, needRemoveLabelTrades, OpEnum.TAG_UPDATE, Lists.newArrayList(SystemTags.TAG_OVERTIME));
        TradeSysConsignUtils.handleSysConsigned(trades);
        fillTradeImportResult(tir, zeroSysConsignedOrderIds, notCloseTrades, notOtherERPConsignedTrades, trades);
        handlePtConsignTime(user, trades);
        // 放在handleSysConsigned 之后，判断是否是其他erp发货
        handleOutSid(staff, trades, tradeConfig, tir, sysExistOutSidsMap, sysOutSids);
        handerPlatExcept(staff, user, trades, tir, false);
        handleOtherErpLogisticsSubscribe(staff, user, trades);
        handleSysConsignedLogisticsSubscribe(staff, user, trades);
        handlerHandlerMemoAndMessage(staff, trades);
        tradePlatStatusChangeBusiness.addPlatStatusChange(staff, trades, tir);
        return trades;
    }

    private void handleOtherErpLogisticsSubscribe(Staff staff, User user, List<Trade> trades) {
        Conf conf = staff.getCompany().getProfile().getConf();
        boolean openLogisticsWarning = conf.getOpenLogisticsWarning();
        if (TradeConsignConfigDiamondUtils.openOtherErpLogisticSubscribe(user.getCompanyId()) && openLogisticsWarning) {
            List<Trade> otherErpConsignedTrades = trades.stream().filter(trade -> Objects.nonNull(trade.getSysConsigned()) && trade.getSysConsigned() == 2).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(otherErpConsignedTrades)) {
                for (Trade trade : otherErpConsignedTrades) {
                    trade.setTemplateId(null);
                }
                logger.info(LogHelper.buildLog(staff, String.format("其他ERP发货物流订阅 sids=[%s]", JSONObject.toJSONString(TradeUtils.toSids(otherErpConsignedTrades)))));
                eventCenter.fireEvent(this, new EventInfo("trade.logistics.warning.sync.consign").setArgs(new Object[]{staff, otherErpConsignedTrades}), null);
            }
        }

    }

    private void handleSysConsignedLogisticsSubscribe(Staff staff, User user, List<Trade> trades) {
        List<Trade> logisticTrades = trades.stream().filter(trade -> Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getUnifiedStatus())&&Objects.nonNull(trade.getSysConsigned()) && trade.getSysConsigned() == 1).collect(Collectors.toList());
        // 更新物流推送信息
        if(CollectionUtils.isNotEmpty(logisticTrades)){
            logisticsWarningBusiness.syncConsignStatusEvent(staff, logisticTrades);
        }
    }

    private void handleV(Staff staff, Trade trade) {
        if (trade.getOrigin() == null || trade.getOrigin().getV() == null || trade.getOrigin().getV().equals(0L)) {
            return;
        }
        trade.addV(Math.toIntExact(trade.getOrigin().getV()));
    }

    private void processDeliverException(Staff staff, Trade trade) {
        //如果订单已打印且未发货，同步为交易关闭，则标记上传异常
        if (trade.getOrigin() != null && TradeStatusUtils.getPrintStatus(trade.getOrigin().getExpressPrintTime()) > 0 && TradeStatusUtils.isWaitSellerSend(trade.getOrigin().getSysStatus()) && StringUtils.equals(Trade.SYS_STATUS_CLOSED, trade.getSysStatus())) {
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.UPLOAD_EXCEPT, 1L);
            Logs.debug(LogHelper.buildLog(staff, String.format("订单[sid=%s,tid=%s,sysStatus=%s,status=%s,v=%s]标记上传异常", trade.getSid(), trade.getTid(), trade.getSysStatus(), trade.getStatus(), trade.getV())));
        }
    }

    private void handleOutSid(Staff staff, List<Trade> trades, TradeConfig tradeConfig, TradeImportResult tir, Map<String, List<Long>> sysExistOutSidsMap, List<String> sysOutSids) {
        FxLogBuilder logBuilder = FxLogBuilder.fx(staff).append("分销订单不覆盖运单号:").startWatch();
        for (Trade trade : trades) {
            handleOutSid(staff, trade, tradeConfig, tir, sysExistOutSidsMap, sysOutSids, logBuilder);
        }
        logBuilder.printDebug(logger);
    }

    private void handleOutSid(Staff staff, Trade trade, TradeConfig tradeConfig, TradeImportResult tir, Map<String, List<Long>> sysExistOutSidsMap, List<String> sysOutSids, AbsLogBuilder logBuilder) {
        if (logBuilder == null) {
            logBuilder = AbsLogBuilder.getNvlInstance();
        }
        Trade origin = trade.getOrigin();
        if (Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_SYS)) {
            //系统单不用处理
            return;
        }
        // 先保存映射关系，再处理是否覆盖
        saveOutSidMapping(staff, trade, tir);
        if (origin == null) {
            return;
        }
        // 运单号处理之前先根据多平台传过来的order上的运单号先算一遍当前运单号
        reCalculateSyncPlatOutSid(staff, trade, sysOutSids);
        //如果系统内有模板没有运单号，平台新同步订单有运单号且在发货后状态，则清除系统内订单模板
        if ("null".equalsIgnoreCase(trade.getOutSid())) {
            // null 字符串设置为null
            trade.setOutSid(null);
        }
        String curOutSid = trade.getOutSid();
        String originOutSid = origin.getOutSid();
        if (!Objects.equals(Optional.ofNullable(curOutSid).orElse(""), Optional.ofNullable(originOutSid).orElse(""))) {
            Logs.debug(LogHelper.buildLog(staff, String.format("平台同步运单号与系统不同 sid=%s,tid=%s source:%s type:%s sysStatus:%s 平台同步运单号:%s 原运单号：%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getType(), trade.getSysStatus(), curOutSid, originOutSid)));
        }
        if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus()) &&
                StringUtils.isNotBlank(curOutSid) && StringUtils.isEmpty(originOutSid) && origin.getTemplateId() != -1) {

            trade.setTemplateId(-1L);
            trade.setLogisticsCompanyId(0L);
            trade.setTemplateType(0);
        }

        if (TradeUtils.isFxOrMixTrade(trade)) {
            //平台运单号可能存在延迟情况,为空时，不覆盖运单号
            if (StringUtils.isEmpty(curOutSid)) {
                logBuilder.group("运单号为空", trade.getSid());
                trade.setOutSid(null);
            }
            //系统状态已发货，不覆盖运单号
            if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
                logBuilder.group("已发货", trade.getSid());
                trade.setOutSid(null);
            }
            //已经获得运单号，不覆盖运单号
            if (StringUtils.isNotBlank(originOutSid)) {
                logBuilder.group("已有运单号", trade.getSid());
                trade.setOutSid(null);
            }
            return;
        }
        //以下情形放弃平台快递单号
        //订单在ERP已有快递单号
        //订单在ERP没有快递单号且本次同步后仍是未发货状态
        //非vipjitx订单那
        //非得物品牌直发类型订单
        //非虾皮

        // 当前运单号在系统中对应的系统单号,existSids 不为空 说明该运单号已经被使用,不能覆盖
        // 其他erp发货的单运单号要覆盖
        boolean otherErpConsigned = TradeSysConsignUtils.isOtherErpConsigned(staff, trade);

        // 小红书其他erp发货，order的平台状态映射成系统状态后都是已发货的订单,运单号非系统获取的
        //https://tb.raycloud.com/task/66011c407613466bb9c34c63
        // 小红书其他erp发货的不能覆盖运单号 https://tb.raycloud.com/task/6662db631ed729400990c0f5
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        boolean b = orders4Trade.stream().allMatch(order -> TradeStatusUtils.isAfterSendGoods(TradeStatus.getSysStatus(order.getStatus(), order.getSysStatus())));
        boolean xhsOtherErpConsigned = Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_XHS) && b && StringUtils.isNotBlank(trade.getOutSid()) && !sysOutSids.contains(trade.getOutSid());
        List<Long> existSids = sysExistOutSidsMap.get(curOutSid);
        boolean isJdOpenCutStockConfig = isJdOpenCutStockConfig(trade, tir);
        if ((StringUtils.isNotBlank(originOutSid) || !TradeStatusUtils.isAfterSendGoods(trade.getSysStatus()) && (!isJdOpenCutStockConfig) || CollectionUtils.isNotEmpty(existSids))
                && !TradeUtils.isFxgBicTrade(trade, tradeConfig)
                && !StringUtils.equals(CommonConstants.PLAT_FORM_TYPE_VIPJITX, trade.getSubSource())
                && !(CommonConstants.PLAT_FORM_TYPE_POISON.equals(trade.getSource()) && TradeTypeConstants.POISON_BRAND_DELIVER_TRADE_TYPE.equals(trade.getType()))
                && (!otherErpConsigned || xhsOtherErpConsigned)// 小红书其他erp发货或者非其他erp发货的单不允许覆盖运单号
        ) {// 不覆盖
            trade.setOutSid(originOutSid);
        } else {
            if (!Objects.equals(StringUtils.trimToEmpty(curOutSid), StringUtils.trimToEmpty(originOutSid)) && curOutSid != null) {
                Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s,tid=%s ,unattainable =%s,source:%s type:%s sysStatus:%s otherErpConsigned:%s 平台同步运单号:%s 覆盖原运单号：%s, existSids=%s,xhsOtherErpConsigned=%s sysOutSids=%s", trade.getSid(), trade.getTid(), trade.getUnattainable(), trade.getSource(), trade.getType(), trade.getSysStatus(), otherErpConsigned, curOutSid, originOutSid, existSids, xhsOtherErpConsigned, sysOutSids)));
                trade.getOperations().put(OpEnum.OUT_SID_SYNC_UPDATE, String.format("订单同步回订单非系统内取号的快递单号：%s", curOutSid));
            }
        }
        //在ERP没有发货的拆单，平台上关闭了，不覆盖平台的发货时间
        if (TradeUtils.isSplit(trade) && Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus()) && origin.getConsignTime().compareTo(TradeTimeUtils.INIT_DATE) == 0) {
            trade.setConsignTime(origin.getConsignTime());
        }

    }

    public static boolean isJdOpenCutStockConfig(Trade trade, TradeImportResult tir) {
        if (tir != null && tir.getTradeConfigMap() != null) {
            //开启京东自营京东直发订单扣减库存
            if ("jd_warehouse".equals(trade.getSubSource()) || PlatformUtils.isJdWarehouseOrder(trade)) {
                TradeConfigNew tradeConfig = tir.getTradeConfigMap().get(TradeConfigEnum.JD_WAREHOUSE_TRADE_SUB_STOCK.getConfigKey());
                if (tradeConfig != null) {
                    return tradeConfig.isOpen();
                }
            }
        }
        return false;
    }

    /**
     * 检查是否需要标识订单部分退款异常
     */
    private void checkPartRefund(Staff staff, Trade trade, TradeImportResult tir) {
        TradeConfig tradeConfig = tir.getTradeConfig();
        // 订单发货前，当部分商品退款成功后，订单自动标记【部分关闭】开关，只有这个开关开启才需要进一步判断
        boolean openPartRefund = true;
        if (tradeConfig != null) {
            openPartRefund = tradeConfig.getOpenPartRefund(staff) == 1;
        }
        boolean isGxWaitSendGoods = TradeUtils.isGxTrade(trade.getOrigin()) && (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getOrigin().getSysStatus()) || Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getOrigin().getSysStatus()));
        // 修改原来的逻辑，去掉待审核，待财审状态标识部分关闭异常，只保留已审核状态
        if ((TradeUtils.isFxOrMixTrade(trade.getOrigin()) || openPartRefund || TradeUtils.isAlibabaFxRoleTrade(trade.getOrigin()))
                && (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()) ||
                (Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getOrigin().getSysStatus()) && com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) ||
                (Objects.nonNull(tradeConfig) && TradeStatusUtils.ifAutoCancelAudit(trade, tradeConfig) && tradeConfig.isCancelAuditIfPartRefund()))    // KMERP-105669: 待财审订单，支持平台信息发生修改自动反审核
                || isGxWaitSendGoods) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            int refundSuccessCount = 0;
            int platformTradeCount = 0;
            for (Order order : orders) {
                if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
                    platformTradeCount++;
                    if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()) && Order.REFUND_SUCCESS.equals(order.getRefundStatus()) && !Order.REFUND_SUCCESS.equals(order.getOldRefundStatus())) {
                        refundSuccessCount++;
                    }
                }
            }
            if (refundSuccessCount > 0 && ((refundSuccessCount < platformTradeCount) || (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) && refundSuccessCount == platformTradeCount))) {
                //KMERP-155780 供销未发货交易关闭取消部分关闭异常
                if (TradeUtils.isGxTrade(trade.getOrigin()) && Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
                    //取消部分退款异常
                    clearPartRefund(staff, trade, tir, openPartRefund);
                } else {
                    //部分退款成功
                    TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.PART_REFUND, 1L);
                    Logs.ifDebug(LogHelper.buildLog(staff, String.format("订单[sid=%s,tid=%s,sysStatus=%s,status=%s,itemExcep=%s,openPartRefund=%s]添加部分退款异常", trade.getSid(), trade.getTid(), trade.getSysStatus(), trade.getStatus(), trade.getItemExcep(), openPartRefund)));
                }
                dealWithAutoUnAudit(staff, trade, tir);
            }
        } else {
            //取消部分退款异常
            clearPartRefund(staff, trade, tir, openPartRefund);
        }
    }

    /**
     * 取消部分退款异常
     */
    private void clearPartRefund(Staff staff, Trade trade, TradeImportResult tir, boolean openPartRefund) {
        if (TradeExceptUtils.isContainExcept(staff, trade.getOrigin(), ExceptEnum.PART_REFUND)) {
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.PART_REFUND, 0L);
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("订单[sid=%s,tid=%s,sysStatus=%s,status=%s,itemExcep=%s,openPartRefund=%s]取消部分退款异常", trade.getSid(), trade.getTid(), trade.getSysStatus(), trade.getStatus(), trade.getItemExcep(), openPartRefund)));
            tir.getCancelPartRefund().add(trade);
        }
    }


    /**
     * 检查分销订单状态是否变为关闭，成功或者已发货
     */
    private void checkFxPtStatusChange(Staff staff, Trade trade, TradeImportResult tir) {
        if (TradeUtils.isFxOrMixTrade(trade.getOrigin())) {
            if ((Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus()) && !Trade.SYS_STATUS_CLOSED.equals(trade.getOrigin().getSysStatus()))
                    || (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus()) && !Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getOrigin().getSysStatus()))
                    || (Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus()) && !Trade.SYS_STATUS_FINISHED.equals(trade.getOrigin().getSysStatus()))
                    || (Trade.SYS_STATUS_CLOSED.equals(TradeStatus.getSysStatus(trade.getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)) && !Trade.SYS_STATUS_CLOSED.equals(TradeStatus.getSysStatus(trade.getOrigin().getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)))
                    || (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(TradeStatus.getSysStatus(trade.getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)) && !Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(TradeStatus.getSysStatus(trade.getOrigin().getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)))
                    || (Trade.SYS_STATUS_FINISHED.equals(TradeStatus.getSysStatus(trade.getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)) && !Trade.SYS_STATUS_FINISHED.equals(TradeStatus.getSysStatus(trade.getOrigin().getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)))
            ) {
                FxLogBuilder.fx(staff).format("分销订单[sid=%s,tid=%s,newSysStatus=%s,oldSysStatus=%s,newStatus=%s,oldStatus=%s]状态有变化,加入到ptInfoChangeSidSet", trade.getSid(), trade.getTid(), trade.getSysStatus(), trade.getOrigin().getSysStatus(),trade.getStatus(),trade.getOrigin().getStatus()).printDebug(logger);
                List<Trade> gxTradesByTid = fxBusiness.filterFxTradesIfGxTradeExist(Collections.singletonList(trade.getOrigin()), true);
                if (CollectionUtils.isNotEmpty(gxTradesByTid)) {
                    tir.notifyGxTradeMap.put(trade.getOrigin().getSid(), trade.getOrigin());
                }
                tir.fxSidDelaySyncSet.add(trade.getOrigin().getSid());
            } else {
                List<Order> orderList = TradeUtils.getOrders4Trade(trade);
                for (Order order : orderList) {
                    Order originOrder = order.getOrigin();
                    if (originOrder != null &&
                            ((Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(order.getSysStatus()) && !Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(originOrder.getSysStatus()))
                                    || (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(TradeStatus.getSysStatus(order.getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)) && !Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(TradeStatus.getSysStatus(order.getOrigin().getStatus(), Trade.SYS_STATUS_WAIT_AUDIT))))) {
                        FxLogBuilder.fx(staff).format("分销订单order部分发货[sid=%s,tid=%s,id=%s,newSysStatus=%s,oldSysStatus=%s,newSysStatus=%s,oldSysStatus=%s],加入到ptInfoChangeSidSet", trade.getSid(), trade.getTid(), originOrder.getId(), order.getSysStatus(), originOrder.getSysStatus(),
                                order.getStatus(), originOrder.getStatus()).printDebug(logger);
                        List<Trade> gxTradesByTid = fxBusiness.filterFxTradesIfGxTradeExist(Collections.singletonList(trade.getOrigin()), true);
                        if (CollectionUtils.isNotEmpty(gxTradesByTid)) {
                            tir.notifyGxTradeMap.put(trade.getOrigin().getSid(), trade.getOrigin());
                        }
                        tir.fxSidDelaySyncSet.add(trade.getOrigin().getSid());
                    }
                }
            }
        }
        Trade originTrade = trade.getOrigin();
        if (originTrade == null) return;
        boolean isGxTrade = TradeUtils.isGxOrMixTrade(originTrade);
        boolean isCurrentAfterSendGoods = TradeStatusUtils.isAfterSendGoods(trade.getSysStatus());
        if (isGxTrade && isCurrentAfterSendGoods
                && (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus()) && !Trade.SYS_STATUS_CLOSED.equals(trade.getOrigin().getSysStatus()))
                || (Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus()) && !Trade.SYS_STATUS_FINISHED.equals(trade.getOrigin().getSysStatus()))
                || (Trade.SYS_STATUS_CLOSED.equals(TradeStatus.getSysStatus(trade.getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)) && !Trade.SYS_STATUS_CLOSED.equals(TradeStatus.getSysStatus(trade.getOrigin().getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)))
                || (Trade.SYS_STATUS_FINISHED.equals(TradeStatus.getSysStatus(trade.getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)) && !Trade.SYS_STATUS_FINISHED.equals(TradeStatus.getSysStatus(trade.getOrigin().getStatus(), Trade.SYS_STATUS_WAIT_AUDIT)))
        ) {
            tir.gxTradeStatusChangeSidSet.add(originTrade.getSid());
            FxLogBuilder.gx(staff).format("供销订单[sid=%s,tid=%s,newSysStatus=%s,oldSysStatus=%s,newStatus=%s,oldStatus=%s]状态有变化,加入到gxTradeStatusChangeSidSet", trade.getSid(), trade.getTid(), trade.getSysStatus(), trade.getOrigin().getSysStatus(), trade.getStatus(), trade.getOrigin().getStatus()).printDebug(logger);
        }
}

    /**
     * 根据自动反审核订单的订单勾选状态做自动反审核的逻辑
     */
    private void dealWithAutoUnAudit(Staff staff, Trade trade, TradeImportResult tir) {
        Trade originTrade = trade.getOrigin();
        boolean isGxWaitSendGoods = TradeUtils.isGxTrade(originTrade) && (Trade.SYS_STATUS_FINISHED_AUDIT.equals(originTrade.getSysStatus()) || Trade.SYS_STATUS_WAIT_AUDIT.equals(originTrade.getSysStatus()));

        if (isGxWaitSendGoods) {
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("订单[sid=%s,tid=%s，因为部分交易关闭触发供销订单同步", trade.getSid(), trade.getTid())));
        } else if (TradeUtils.isFxOrMixTrade(originTrade)) {
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("订单[sid=%s,tid=%s，因为部分交易关闭触发供销订单同步", trade.getSid(), trade.getTid())));
            tir.notifyGxTradeMap.put(originTrade.getSid(), originTrade);
        } else {
            TradeConfig tradeConfig = tir.getTradeConfig();
            // 由于平台下载的订单同步没有同步isPackage,isWeigh,故使用数据库订单数据进行业务逻辑操作
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("订单[sid=%s,tid=%s，其中平台单isPackage=%s,isWeigh=%s,数据库订单isPackage=%s,isWeigh=%s，其中autoCancelAudit=%s", trade.getSid(), trade.getTid(), trade.getIsPackage(), trade.getIsWeigh(), originTrade.getIsPackage(), originTrade.getIsWeigh(), tradeConfig.getAutoCancelAudit())));
            // autoCancelAuditStatus字段标识限定发货中订单状态，包含勾选的状态 1, 2, 4, 8, 16 才说明有针对自动反审核状态的勾选
            // autoCancelAudit标识限定平台信息修改内容，目前使用16标识平台部分关闭，包含才进行自动反审核订单
            if (!TradeUtils.isFxSource(originTrade)) {
                if (TradeStatusUtils.ifAutoCancelAudit(originTrade, tradeConfig) && tradeConfig.isCancelAuditIfPartRefund()) {
                    tir.opEnumAuditUndoSidMap.computeIfAbsent(OpEnum.AUDIT_UNDO_AUTO_PLAT_ITEM_REFUND, k -> new HashSet<>()).add(trade.getSid());

                    // reserveExceptAfterCancelAudit字段标识开启发货中订单平台信息修改后自动反审核时，仍标记对应异常
                    // 如果开启部分关闭异常标识，并且开启平台部分关闭的情况下，如果为0就需要取消部分关闭异常，如果为1需要继续标识异常
                    boolean reserveExceptAfterCancelAudit = tradeConfig.getInteger("reserveExceptAfterCancelAudit") == 0;
                    if (reserveExceptAfterCancelAudit) {
                        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PART_REFUND)) {
                            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.PART_REFUND, 0L);
                            Logs.ifDebug(LogHelper.buildLog(staff, String.format("订单[sid=%s,tid=%s,sysStatus=%s,status=%s,itemExcep=%s,reserveExceptAfterCancelAudit=%s]取消部分退款异常", trade.getSid(), trade.getTid(), trade.getSysStatus(), trade.getStatus(), trade.getItemExcep(), tradeConfig.getInteger("reserveExceptAfterCancelAudit"))));
                        }
                    }
                } else if(TradeUtils.isAlibabaFxRoleTrade(trade)){
                    tir.opEnumAuditUndoSidMap.computeIfAbsent(OpEnum.AUDIT_UNDO_AUTO_PLAT_ITEM_REFUND, k -> new HashSet<>()).add(trade.getSid());
                }
            }
        }

    }

    private void handlePtConsignTime(User user, List<Trade> trades) {
        try {
            for (Trade trade : trades) {
                if (!TradeSysConsignUtils.isEmptyTime(trade.getPtConsignTime())) {
                    continue;
                }
                String currentTradePlatStatus = TradeStatus.getSysStatus(trade.getStatus(), null);
                if (Trade.SYS_STATUS_CLOSED.equals(currentTradePlatStatus)) {
                    continue;
                }
                if ((trade.getOrigin() == null && TradeStatusUtils.isAfterSendGoods(currentTradePlatStatus))
                        || (trade.getOrigin() != null && !TradeStatusUtils.isAfterSendGoods(TradeStatus.getSysStatus(trade.getOldStatus(), null)) && TradeStatusUtils.isAfterSendGoods(currentTradePlatStatus))) {
                    //其他erp发货处理逻辑
                    if (Objects.equals(TradeSysConsignUtils.OTHER_ERP_CONSIGNED, trade.getSysConsigned())) {
                        trade.setPtConsignTime(getConsignTime(trade));
                    } else {
                        Date currentDate = Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(currentTradePlatStatus) ? new Date() : DateUtils.addHours(trade.getPayTime(), 48);
                        trade.setPtConsignTime(currentDate);
                    }
                    handleOrderPtConsignTime(trade);
                } else if (TradeStatus.TB_SELLER_CONSIGNED_PART.equals(currentTradePlatStatus)) {
                    handleOrderPtConsignTime(trade);
                }
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLogHead(user).append("handle ptConsignTime error, errMsg:").append(e.getMessage()), e);
        }
    }

    /**
     * 针对没有平台发货时间的  如果当前时间>付款时间+48小时  则取付款时间+48小时  否则取当前时间
     *
     * @param trade
     * @return
     */
    private Date getConsignTime(Trade trade) {
        Date currentDate = new Date();
        Date payTime = trade.getPayTime() != null && trade.getPayTime().after(TradeTimeUtils.INIT_DATE) ? trade.getPayTime() : null;
        if (payTime == null && trade.getCreated() != null && trade.getCreated().after(TradeTimeUtils.INIT_DATE)) {
            payTime = trade.getCreated();
        }
        if (payTime != null) {
            Date newDate = org.apache.commons.lang3.time.DateUtils.addDays(payTime, 48);
            if (currentDate.after(newDate)) {
                currentDate = newDate;
            }
        }
        return currentDate;
    }

    private void handleOrderPtConsignTime(Trade trade) {
        for (Order order : TradeUtils.getOrders4Trade(trade)) {
            if (!TradeSysConsignUtils.isEmptyTime(order.getPtConsignTime())) {
                continue;
            }
            String currentOrderPlatStatus = TradeStatus.getSysStatus(order.getStatus(), null);
            if (Trade.SYS_STATUS_CLOSED.equals(currentOrderPlatStatus)) {
                continue;
            }
            if ((order.getOrigin() == null && TradeStatusUtils.isAfterSendGoods(currentOrderPlatStatus))
                    || (order.getOrigin() != null && !TradeStatusUtils.isAfterSendGoods(TradeStatus.getSysStatus(order.getOldStatus(), null)) && TradeStatusUtils.isAfterSendGoods(currentOrderPlatStatus))) {
                Date currentDate = null;
                //其他erp发货处理逻辑
                if (Objects.equals(TradeSysConsignUtils.OTHER_ERP_CONSIGNED, trade.getSysConsigned())) {
                    currentDate = getConsignTime(trade);
                } else {
                    currentDate = Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(currentOrderPlatStatus) ? new Date() : DateUtils.addHours(trade.getPayTime(), 48);
                }
                Date consignTime = currentDate;
                order.setPtConsignTime(consignTime);
                if (order.getSuits() != null) {
                    order.getSuits().forEach(suit -> suit.setPtConsignTime(consignTime));
                }
            }
        }
    }

    /**
     * 针对BTAS订单映射平台状态的特殊处理 -- 基本场景在FangxingouBtasBusiness#modifyTradeStatus内已经实现
     * 修复场景：BTAS更新同步时，订单码拉取失败时，平台状态的处理
     * 放心购抖店平台--系统状态：fxg_2 ：备货中 --> 待审核
     *
     * @param trades
     */
    private void modifyBtasTradeStatus(List<Trade> trades) {
        trades.forEach(t -> {
            if (null != t.getSourceTrade() && TradeTypeUtils.isFxgBtasTrade(t.getSourceTrade()) && !TradeTypeUtils.isFxgBtasTrade(t)) {
                boolean existOutSid = StringUtils.isNotBlank(t.getOutSid());
                Map<Long, Order> sourceOrderMap = Optional.ofNullable(TradeUtils.getOrders4Trade(t.getSourceTrade())).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(Order::getOid, k -> k, (v1, v2) -> v1));
                for (Order order : TradeUtils.getOrders4Trade(t)) {
                    String status = order.getStatus();
                    //没有运单号、不是质检成功，平台状态为 待发货
                    if (!existOutSid) {
                        status = "fxg_2";
                    }
                    Order sourceOrder = sourceOrderMap.get(order.getOid());
                    if (!(null != sourceOrder && null != sourceOrder.getOrderExt() && StringUtils.isNotBlank(sourceOrder.getOrderExt().getCustomization())
                            && sourceOrder.getOrderExt().getCustomization().contains(OrderUtils.BtasPictureQualityResultEnum.SUCCESS.descAndCode)
                            && !sourceOrder.getOrderExt().getCustomization().contains(OrderUtils.BtasPictureQualityResultEnum.UNDEFINED.descAndCode)
                            && !sourceOrder.getOrderExt().getCustomization().contains(OrderUtils.BtasPictureQualityResultEnum.FAIL.descAndCode)
                            && !sourceOrder.getOrderExt().getCustomization().contains(OrderUtils.BtasPictureQualityResultEnum.SELLER_SEND.descAndCode)
                    )) {
                        status = "fxg_2";
                    }
                    order.setStatus(status);
                }
            }
        });
    }

    private void saveTradeOutSidMapping(Staff staff, List<Trade> trades, TradeImportResult tir) {
        for (Trade trade : trades) {
            saveOutSidMapping(staff, trade, tir);
        }
    }

    private void saveOutSidMapping(Staff staff, Trade trade, TradeImportResult tir) {
        try {
            boolean hasOtherErpConsigned = TradeUtils.getOrders4Trade(trade).stream().anyMatch(TradeSysConsignUtils::isOtherErpConsigned);
            Trade origin = trade.getOrigin();
            if (!hasOtherErpConsigned || StringUtils.isBlank(trade.getOutSid()) || (origin != null && Objects.equals(origin.getOutSid(), trade.getOutSid()))) {
                // 系统状态为已发货的单不处理；不存在运单号的单不处理；有运单号，并且和原运单号一样的不处理
                return;
            }
            String originOutSid = origin == null ? null : origin.getOutSid();
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("sid:%s, 平台同步下来运单号：%s , 当前系统运单号：%s ", trade.getSid(), trade.getOutSid(), originOutSid)));
            InfoChangeLog infoChangeLog = new InfoChangeLog(trade.getCompanyId(), trade.getSid(), originOutSid, trade.getOutSid(), InfoChangeLogBusiness.INFO_TYPE_OUTSID_CHANGE, trade.getTid());
            tir.outSidsInfoChangeLogs.add(infoChangeLog);
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(staff, e, String.format("sid=%s 平台同步运单号处理出错", trade.getSid())));
        }

    }

    /**
     * 合单退款交易关闭取消合单
     */
    private void undoMerge(Staff staff, Trade trade, TradeImportResult tir) {
        Trade origin = trade.getOrigin();
        if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) && CommonConstants.PLAT_FORM_TYPE_POISON.equals(trade.getSource())
                && TradeTypeConstants.POISON_BRAND_DELIVER_TRADE_TYPE.equals(trade.getType()) && Objects.equals(trade.getSysStatus(), Trade.SYS_STATUS_CLOSED)) {
            // 得物直发的合单订单，交易关闭取消合单,部分取消合单,已审核状态不会取消合单，但是反审核后会自动走一次指定下载
            tir.undoMergeSids.computeIfAbsent(TradeMergeEnum.MERGE_UNDO_PART_AUTO, k -> new HashMap<>())
                    .computeIfAbsent(trade.getMergeSid(), k -> new HashSet<>())
                    .add(trade.getSid());
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("得物直发合并订单 sid:%s mergeSid:%s sysStatus:%s,originSysStatus:%s 交易关闭取消合单", trade.getSid(), trade.getMergeSid(), trade.getSysStatus(), trade.getOrigin().getSysStatus())));
            // 得物直发的合单订单，交易关闭取消合单,待审核订单，部分取消合单在
            if (origin != null && Objects.equals(origin.getSysStatus(), Trade.SYS_STATUS_FINISHED_AUDIT)) {
                tir.opEnumAuditUndoSidMap.computeIfAbsent(OpEnum.AUDIT_UNDO_AUTO_MERGE_PART_REFUND, k -> new HashSet<>()).add(trade.getMergeSid());
                Logs.debug(LogHelper.buildLog(staff, String.format("已审核订单 sid:%s mergeSid:%s sysStatus:%s ,originSysStatus:%s 自动反审核", trade.getSid(), trade.getMergeSid(), trade.getSysStatus(), trade.getOrigin().getSysStatus())));
            }
        }
        //https://gykj.yuque.com/entavv/xb9xi5/uwz1bgv4sgxyadir
        if (CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSource())
                && CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource())
                && Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())
                && origin != null && com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, origin)
        ) {
            tir.undoMergeSids.computeIfAbsent(TradeMergeEnum.MERGE_UNDO_PART_AUTO, k -> new HashMap<>())
                    .computeIfAbsent(trade.getMergeSid(), k -> new HashSet<>())
                    .add(trade.getSid());
            if (Trade.SYS_STATUS_FINISHED_AUDIT.equals(origin.getSysStatus())) {
                tir.opEnumAuditUndoSidMap.computeIfAbsent(OpEnum.AUDIT_UNDO_AUTO_MERGE_PART_REFUND, k -> new HashSet<>()).add(trade.getMergeSid());
            }
        }
    }

    /**
     * 获取系统中已经存在的运单号
     */
    private Map<String, List<Long>> getSysExistOutSid(Staff staff, List<Trade> trades) {
        Set<String> outSids = trades.stream().map(Trade::getOutSid).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trades);
        for (Order order : orders4Trade) {
            Set<String> platOutSids = order.getPlatOutSids();
            if (CollectionUtils.isNotEmpty(platOutSids)) {
                outSids.addAll(platOutSids);
            }
        }
        Map<String, List<Long>> sysExistOutSidsMap = new HashMap<>();
        outSids = outSids.stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(outSids)) {
            // 获取系统中已经被使用的运单号
            List<TbTrade> outSidTrades = tbTradeSearchService.queryByKeys(staff, "sid,out_sid", "out_sid", outSids.toArray(new String[0]));
            for (TbTrade trade : outSidTrades) {
                if (StringUtils.isBlank(trade.getOutSid())) {
                    continue;
                }
                sysExistOutSidsMap.computeIfAbsent(trade.getOutSid(), k -> new ArrayList<>()).add(trade.getSid());
            }
        }
        return sysExistOutSidsMap;
    }

    /**
     * 获取系统获取的运单号
     */
    public List<String> getSysOutSids(Staff staff, List<Trade> trades) {
        List<String> collect = trades.stream().map(Trade::getOutSid).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return Lists.newArrayList();
        }
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trades);
        for (Order order : orders4Trade) {
            Set<String> platOutSids = order.getPlatOutSids();
            if (CollectionUtils.isNotEmpty(platOutSids)) {
                collect.addAll(platOutSids);
            }
        }
        List<OutSidPool> outSidPools = outSidPoolService.queryAllByOutSids(staff, new ArrayList<>(collect));
        if (CollectionUtils.isEmpty(outSidPools)) {
            return Lists.newArrayList();
        }
        return outSidPools.stream().map(OutSidPool::getOutSid).collect(Collectors.toList());
    }


    /**
     * 订单同步中的平台异常统一在这处理
     * 放在系统状态映射之后,内部的状态都是以系统状态为准
     */
    private void handerPlatExcept(Staff staff, User user, List<Trade> trades, TradeImportResult tir, boolean insert) { TradeSyncExceptHandlerUtils.handlerExcept(user, trades, tir, tradeLocalConfigurable, insert, ExceptEnum.ONLINE_STATUS_EXCEPT, null);

        TradeSyncExceptHandlerUtils.handlerExcept(user, trades, tir, tradeLocalConfigurable, insert, ExceptEnum.SMALL_REFUND_EXCEPT, null);
        Set<ExceptEnum> exceptEnums = platExceptSyncBusiness.getSyncExceptEnums();
        platExceptSyncBusiness.handlerSyncPlatExcept(user, trades, tir, insert, exceptEnums);
        for (Trade trade : trades) {
            reMatchOrderExcept(staff, trade, ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
            reMatchOrderExcept(staff, trade, ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT);
            reMatchOrderExcept(user.getStaff(), trade, ExceptEnum.SMALL_REFUND_EXCEPT);
            reMatchOrderExcept(user.getStaff(), trade, ExceptEnum.ONLINE_STATUS_EXCEPT);
        }

        closeExchangeItemPlatModifyExcept(staff, trades); // 交易关闭的订单取消“换货前商品平台信息变更”异常
    }

    private void reMatchOrderExcept(Staff staff, Trade trade, ExceptEnum orderExcept) {
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        boolean b = orders4Trade.stream().anyMatch(order -> OrderExceptUtils.isContainsExcept(staff, order, orderExcept));
        if (TradeExceptUtils.isContainExcept(staff, trade, orderExcept) != b) {
            TradeExceptUtils.updateExcept(staff, trade, orderExcept, b);
        }
    }


    /**
     * 拆分订单子母单号交易关闭回收处理
     */
    private void handleParentAndChildOrder(Trade trade, TradeImportResult tir, Staff staff) {
        boolean isParentAndChildOrder = TradeTagUtils.checkIfExistTag(trade, SystemTags.SPLIT_THE_PARENT_ORDER) ||
                TradeTagUtils.checkIfExistTag(trade, SystemTags.SPLIT_THE_CHILD_ORDER) ||
                TradeTagUtils.checkIfExistTag(trade, SystemTags.SPLIT_THE_PARENT_ORDER_DIFF_NUM) ||
                TradeTagUtils.checkIfExistTag(trade, SystemTags.SPLIT_THE_CHILD_ORDER_DIFF_NUM);
        boolean isClose = StringUtils.equals(Trade.SYS_STATUS_CLOSED, trade.getSysStatus()) && !StringUtils.equals(Trade.SYS_STATUS_CLOSED, trade.getOrigin().getSysStatus());
        boolean hasOutSid = StringUtils.isNotBlank(trade.getOutSid());

        if (!isParentAndChildOrder || !isClose || !hasOutSid || TradeUtils.isAfterSendGoods(trade)) {//【交易】【优化】【奥丰农药——粳米（张迪）】子母单取号订单同步到交易关闭时要判断订单在系统内是否已发货，已发货则不回收单号。
            return;
        }
        List<String> subOuterSid = new ArrayList<>();
        subOuterSid.add(trade.getOutSid());
        Map<String, Long> outerSidToSid = multiPacksPrintTradeLogService.getMainSidMapByOutSids(staff, subOuterSid);
        if (outerSidToSid != null && !outerSidToSid.isEmpty()) {
            Long[] parentSids = outerSidToSid.values().toArray(new Long[0]);
            Logs.info(LogHelper.buildLog(staff, String.format("母单取代子单上传，查询到的主单sids: %s", Arrays.toString(parentSids))));
            List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, parentSids);
            if (CollectionUtils.isNotEmpty(trades)) {
                for (Trade trade1 : trades) {
                    if (TradeUtils.isAfterSendGoods(trade1)) {
                        return;
                    }
                }
            }
        }
        tir.recyclingOutSidTrades.add(trade);
    }

    /**
     * 订单商品的系统状态变为交易关闭
     */
    private void orderSysStatus2Close(Staff staff, Trade trade, TradeImportResult tir) {
        boolean checkIfExistTag = TradeTagUtils.checkIfExistTag(trade, SystemTags.TAG_HAS_CREATE_CAIGOU_ORDER);
        if (!checkIfExistTag) {
            // 不包含已经生成采购单标签
            return;
        }
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders4Trade) {
            Order origin = order.getOrigin();
            if (origin == null) {
                // 理论上是不存在origin为null的
                Logs.debug(LogHelper.buildLog(staff, String.format("origin 对象不存在 id=%s  sid=%s sysStatus=%s,refundStatus=%s ", order.getId(), order.getSid(), order.getSysStatus(), order.getRefundStatus())));
                continue;
            }
            // 灰度三日志
            Logs.debug(LogHelper.buildLog(staff, String.format("交易关闭order id=%s sid=%s sysStatus=%s refundStatus=%s originSysStatus=%s originRefundStatus=%s ", order.getId(), order.getSid(), order.getSysStatus(), order.getRefundStatus(), origin.getSysStatus(), origin.getRefundStatus())));
            // 申请退款
            boolean refund = RefundUtils.isRefundOrder(order) && !RefundUtils.isRefundOrder(origin) && !Objects.equals(order.getSysStatus(), Trade.SYS_STATUS_CLOSED);
            // 取消退款
            boolean cancelRefund = !RefundUtils.isRefundOrder(order) && RefundUtils.isRefundOrder(origin) && !Objects.equals(order.getSysStatus(), Trade.SYS_STATUS_CLOSED);
            // 交易关闭
            boolean close = !Objects.equals(origin.getSysStatus(), Trade.SYS_STATUS_CLOSED) && Objects.equals(order.getSysStatus(), Trade.SYS_STATUS_CLOSED);
            if (refund || cancelRefund || close) {
                tir.closeOrders.add(order);
            }
        }
    }

    /**
     * 重新计算同步下来的运单号
     */
    private void reCalculateSyncPlatOutSid(Staff staff, Trade trade, List<String> sysOutSids) {
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        String syncPlatOutSid = trade.getOutSid();
        Set<String> platOutSids = new HashSet<>();
        Map<Long, Set<String>> orderOutSidMap = new HashMap<>();
        // 获取当前trade中的order的所有的运单号
        for (Order order : orders4Trade) {
            Set<String> platOutSid = order.getPlatOutSids();
            if (CollectionUtils.isNotEmpty(platOutSid)) {
                platOutSids.addAll(platOutSid);
                orderOutSidMap.put(order.getId(), platOutSid);
            }
        }
        if (CollectionUtils.isNotEmpty(platOutSids)) {
            // 优先取系统获取的运单号赋值给trade
            Set<String> outSids = platOutSids.stream().filter(sysOutSids::contains).collect(Collectors.toSet());
            //outSids size 大于1 存在多个系统获取的，不处理直接取多平台给的
            if (outSids.size() == 1) {
                syncPlatOutSid = new ArrayList<>(outSids).get(0);
            } else if (outSids.size() > 1) {
                Logs.debug(LogHelper.buildLog(staff, String.format("订单同步运单号重新计算处理outSids大小大于1 保持多平台同步不变 sid=%s trade_outSid=%s,outSids=%s,syncPlatOutSid=%s orderOutSidMap=%s sysOutSids=%s", trade.getSid(), trade.getOutSid(), outSids, syncPlatOutSid, orderOutSidMap, sysOutSids)));
            } else {
                //不存在系统获取的单号
                if (MapUtils.isNotEmpty(orderOutSidMap)) {
                    // 非系统获取的运单号，如果trade上的运单号不属于order上的,优先取order上的随机取一个
                    List<String> orderOutSids = orderOutSidMap.values().stream().flatMap(Set::stream).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(orderOutSids) && !orderOutSids.contains(syncPlatOutSid) && !Objects.equals(syncPlatOutSid, orderOutSids.get(0))) {
                        syncPlatOutSid = orderOutSids.get(0);
                        Logs.debug(LogHelper.buildLog(staff, String.format("非系统获取的运单号，如果trade上的运单号不属于order上的,优先取order上的随机取一个 sid=%s trade_outSid=%s,outSids=%s,syncPlatOutSid=%s orderOutSidMap=%s sysOutSids=%s ", trade.getSid(), trade.getOutSid(), outSids, syncPlatOutSid, orderOutSidMap, sysOutSids)));
                    }
                }
            }
        }
        if (!Objects.equals(syncPlatOutSid, trade.getOutSid())) {
            Logs.debug(LogHelper.buildLog(staff, String.format("订单同步运单号重新计算处理sid=%s trade_outSid=%s,syncPlatOutSid=%s orderOutSidMap=%s sysOutSids=%s", trade.getSid(), trade.getOutSid(), syncPlatOutSid, orderOutSidMap, sysOutSids)));
            // 计算后的运单号与当前同步下来的运单号不相同 用order上的运单号 覆盖当前运单号
            trade.setOutSid(syncPlatOutSid);
        }
    }


    private void handlerHandlerMemoAndMessage(Staff staff, List<Trade> trades) {
        // https://gykj.yuque.com/entavv/xb9xi5/dcww12dadtqg49ap 订单同步回来其他ERP发货或者交易关闭留言和备注默认标记为已处理。
        for (Trade trade : trades) {
            boolean otherErpConsigned = TradeSysConsignUtils.isOtherErpConsigned(staff, trade);
            if (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus()) || otherErpConsigned) {
                trade.setIsHandlerMemo(1);
                trade.setIsHandlerMessage(1);
            }
        }

    }

    private void fillBefore(List<Trade> trades, Set<Trade> notCloseTrades, Set<Trade> notOtherERPConsignedTrades, Set<Long> zeroSysConsignedOrderIds) {
        for (Trade trade : trades) {
            if (!Objects.equals(trade.getSysStatus(), Trade.SYS_STATUS_CLOSED)) {
                notCloseTrades.add(trade);
            }
            if (!Objects.equals(trade.getSysConsigned(), TradeSysConsignUtils.OTHER_ERP_CONSIGNED)) {
                notOtherERPConsignedTrades.add(trade);
            }
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            if (orders == null || orders.isEmpty()) {
                continue;
            }

            for (Order order : orders) {
                Integer sysConsigned = order.getSysConsigned();
                if (sysConsigned == null || sysConsigned.equals(TradeSysConsignUtils.NO_CONSIGNED)) {
                    zeroSysConsignedOrderIds.add(order.getId());
                }
            }
        }
    }

    private void fillTradeImportResult(TradeImportResult tir, Set<Long> zeroSysConsignedOrderIds, Set<Trade> notCloseTrades, Set<Trade> notOtherERPConsignedTrades, List<Trade> trades) {
        if (trades == null || trades.isEmpty()) {
            return;
        }

        for (Trade trade : trades) {
            List<Order> os = TradeUtils.getOrders4Trade(trade);
            if (os == null || os.isEmpty()) {
                continue;
            }

            String outSid = trade.getOutSid();
            boolean existOutSid = outSid != null && !outSid.isEmpty();
            // 整单变更为交易关闭
            if (notCloseTrades.contains(trade) &&
                    Objects.equals(trade.getSysStatus(), Trade.SYS_STATUS_CLOSED) &&
                    existOutSid
            ) {
                tir.changeStatusCancelOutSidTrades.add(trade);
            }
            // 整单变更为其他ERP发货
            if (notOtherERPConsignedTrades.contains(trade) &&
                    Objects.equals(trade.getSysConsigned(), TradeSysConsignUtils.OTHER_ERP_CONSIGNED) &&
                    existOutSid
            ) {
                tir.changeStatusCancelOutSidTrades.add(trade);
            }

            boolean existZeroSysConsigned = false, existOtherErpConsigned = false;
            for (Order o : os) {
                if (Objects.equals(o.getSysConsigned(), TradeSysConsignUtils.NO_CONSIGNED)) {
                    existZeroSysConsigned = true;
                }
                if (zeroSysConsignedOrderIds.contains(o.getId()) &&
                        Objects.equals(o.getSysConsigned(), TradeSysConsignUtils.OTHER_ERP_CONSIGNED)
                ) {
                    existOtherErpConsigned = true;
                }
                if (existZeroSysConsigned && existOtherErpConsigned) {
                    tir.sidsOfPartOrderOtherERPConsigned.add(trade.getSid());
                    break;
                }
            }
        }
    }
}

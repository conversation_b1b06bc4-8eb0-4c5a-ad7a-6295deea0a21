<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="UserWlbExpressTemplate">
    <!-- Alias Map Defined -->
    <typeAlias alias="UserWlbExpressTemplate" type="com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate"/>
    <typeAlias alias="UserWlbExpressTemplateName" type="com.raycloud.dmj.domain.pt.model.UserWlbExpressTemplateName"/>
    <typeAlias alias="UserExpressTemplate" type="com.raycloud.dmj.domain.pt.UserExpressTemplate"/>

    <resultMap id="UserWlbExpressTemplateResultMap" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="name" column="name"/>
        <result property="nameDesc" column="name_desc"/>
        <result property="expressId" column="express_id"/>
        <result property="bgPathStandard" column="bg_path_standard"/>
        <result property="bgPathCustom" column="bg_path_custom"/>
        <result property="fontSizeCustom" column="font_size_custom"/>
        <result property="fontCustom" column="font_custom"/>
        <result property="boldCustom" column="bold_custom"/>
        <result property="widthStandard" column="width_standard"/>
        <result property="heightStandard" column="height_standard"/>
        <result property="widthCustom" column="width_custom"/>
        <result property="heightCustom" column="height_custom"/>
        <result property="horizontalOffsetStandard" column="horizontal_offset_standard"/>
        <result property="verticalOffsetStandard" column="vertical_offset_standard"/>
        <result property="verticalOffsetDefaultStandard" column="vertical_offset_default_standard"/>
        <result property="horizontalOffsetCustom" column="horizontal_offset_custom"/>
        <result property="verticalOffsetCustom" column="vertical_offset_custom"/>
        <result property="verticalOffsetDefaultCustom" column="vertical_offset_default_custom"/>
        <result property="fieldSettingsStrStandard" column="field_settings_str_standard"/>
        <result property="fieldSettingsStrCustom" column="field_settings_str_custom"/>
        <result property="templatePathStandard" column="template_path_standard"/>
        <result property="templatePathCustom" column="template_path_custom"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="templateType" column="template_type"/>
        <result property="cpType" column="cp_type"/>
        <result property="cpCode" column="cp_code"/>
        <result property="branchAccountsStr" column="branch_accounts_str"/>
        <result property="sysTemplateId" column="sys_template_id"/>
        <result property="title" column="title"/>
        <result property="fieldValuesStrStandard" column="field_values_str_standard"/>
        <result property="fieldValuesStrCustom" column="field_values_str_custom"/>
        <result property="printerName" column="printer_name"/>
        <result property="created" column="created"/>
        <result property="modified" column="modified"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="shopUse" column="shop_use"/>
        <result property="wlbType" column="wlb_type"/>
        <result property="wlbTemplateType" column="wlb_template_type"/>
        <result property="cainiaoEncryptedFields" column="cainiao_encrypted_fields"/>
        <result property="cloudTemplateId" column="cloud_template_id"/>
        <result property="cloudTemplateUrl" column="cloud_template_url"/>
        <result property="standardCloudTemplateUrl" column="standard_cloud_template_url"/>
        <result property="cod" column="cod"/>
        <result property="isManulfill" column="is_manulfill"/>
        <result property="serviceInfoChoiceStr" column="service_info_choice_str"/>
        <result property="modeInputs" column="mode_inputs"/>
        <result property="obtainWay" column="obtain_way"/>
        <result property="cloudTemplatePaperType" column="cloud_template_paper_type"/>
        <result property="cloudTemplateXml" column="cloud_template_xml"/>
        <result property="fieldBoxList" column="field_box_list"/>
        <result property="serviceValueStr" column="service_value_str"/>
        <result property="liveStatus" column="live_status"/>
        <result property="ydConfigStr" column="yd_config_str"/>
        <result property="cloudCustomAreaConfig" column="cloud_custom_area_config"/>
        <result property="fieldValuesCustomMap" column="field_values_custom_map" />
        <result property="updateStatus" column="update_status" />
        <result property="templateHtml" column="template_html" />
    </resultMap>
    <!--电子面单的列表resultMap-->
    <resultMap id="UserWlbExpressTemplateListResultMap" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="expressId" column="express_id"/>
        <result property="companyId" column="company_id"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="sysTemplateId" column="sys_template_id"/>
        <result property="name" column="name"/>
        <result property="cpType" column="cp_type"/>
        <result property="cpCode" column="cp_code"/>
        <result property="branchAccountsStr" column="branch_accounts_str"/>
        <result property="templatePathStandard" column="template_path_standard"/>
        <result property="templatePathCustom" column="template_path_custom"/>
        <result property="templateType" column="template_type"/>
        <result property="printerName" column="printer_name"/>
        <result property="created" column="created"/>
        <result property="modified" column="modified"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="cod" column="cod"/>
        <result property="liveStatus" column="live_status"/>
        <result property="ydConfigStr" column="yd_config_str"/>
        <result property="cloudCustomAreaConfig" column="cloud_custom_area_config"/>
    </resultMap>
    <!--电子面单的列表resultMap-->
    <resultMap id="listByConditionResultMap" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="expressId" column="express_id"/>
        <result property="sysTemplateId" column="sys_template_id"/>
        <result property="cloudTemplateId" column="cloud_template_id"/>
        <result property="branchAccountsStr" column="branch_accounts_str"/>
        <result property="cloudTemplateUrl" column="cloud_template_url"/>
        <result property="cloudTemplatePaperType" column="cloud_template_paper_type"/>
        <result property="fieldBoxList" column="field_box_list"/>
        <result property="cpCode" column="cp_code"/>
        <result property="cpType" column="cp_type"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="name" column="name"/>
        <result property="obtainWay" column="obtain_way"/>
    </resultMap>

    <resultMap id="ExpressIdResultMap" class="UserWlbExpressTemplate">
        <result property="expressId" column="express_id"/>
    </resultMap>

    <!--电子面单的列表resultMap-->
    <resultMap id="UserWlbExpressTemplateResultMap1" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="expressId" column="express_id"/>
        <result property="branchAccountsStr" column="branch_accounts_str"/>
        <result property="liveStatus" column="live_status"/>
    </resultMap>
    <!--电子面单的列表需要快递公司名resultMap-->
    <resultMap id="UserWlbExpressTemplateResultExpressNameMap" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="expressId" column="express_id"/>
        <result property="sysTemplateId" column="sys_template_id"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="wlbTemplateType" column="wlb_template_type"/>
        <result property="liveStatus" column="live_status"/>
        <result property="printerName" column="printer_name"/>
    </resultMap>
    <!--    ExpressIdAndCpCode-->
    <resultMap id="UserWlbExpressTemplateResultExpressIdAndCpCode" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="expressId" column="express_id"/>
        <result property="cpCode" column="cp_code"/>
        <result property="cpType" column="cp_type"/>
        <result property="wlbTemplateType" column="wlb_template_type"/>
        <result property="wlbType" column="wlb_type"/>
        <result property="liveStatus" column="live_status"/>
        <result property="branchAccountsStr" column="branch_accounts_str"/>
        <result property="obtainWay" column="obtain_way"/>
    </resultMap>

    <resultMap id="UserWlbExpressTemplateResultMap2" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="expressId" column="express_id"/>
    </resultMap>

    <resultMap id="UserWlbExpressTemplateResultMap3" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="cpCode" column="cp_code"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="wlbTemplateType" column="wlb_template_type"/>
        <result property="wlbType" column="wlb_type"/>
        <result property="name" column="name"/>
        <result property="expressId" column="express_id"/>
        <result property="templateType" column="template_type"/>
        <result property="fieldValuesStrStandard" column="field_values_str_standard"/>
        <result property="fieldValuesStrCustom" column="field_values_str_custom"/>
    </resultMap>

    <resultMap id="UserWlbExpressTemplateResultMap4" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="isManulfill" column="is_manulfill"/>
        <result property="branchAccountsStr" column="branch_accounts_str"/>
    </resultMap>

    <resultMap id="UserWlbExpressTemplateResultMap5" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="branchAccountsStr" column="branch_accounts_str"/>
    </resultMap>

    <resultMap id="UserWlbExpressTemplateResultMap6" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="branchAccountsStr" column="branch_accounts_str"/>
        <result property="wlbTemplateType" column="wlb_template_type"/>
    </resultMap>

    <resultMap id="UserWlbExpressTemplateResultMap7" class="UserWlbExpressTemplate">
        <result property="expressId" column="express_id"/>
    </resultMap>

    <resultMap id="UserWlbExpressTemplateResultMap8" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="expressId" column="express_id"/>
        <result property="wlbTemplateType" column="wlb_template_type"/>
    </resultMap>

    <!--查询电子面单的名称列表-->
    <resultMap id="UserWlbExpressTemplateNameResultMap" class="UserWlbExpressTemplateName">
        <result property="taobaoId" column="taobao_id"/>
        <result property="templateId" column="id"/>
        <result property="templateName" column="name"/>
        <result property="cpCode" column="cp_code"/>
        <result property="id" column="id"/>
        <result property="cloudTemplateId" column="cloud_template_id"/>
        <result property="obtainWay" column="obtain_way"/>
        <result property="sysTemplateId" column="sys_template_id"/>
        <result property="wlbTemplateType" column="wlb_template_type"/>
    </resultMap>

    <!--查询普通面单和电子混合的模块列表-->
    <resultMap id="UserExpressWlbTemplateResultMap" class="UserExpressTemplate">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="expressId" column="express_id"/>
        <result property="isWlb" column="is_wlb"/>
        <result property="groupId" column="group_id"/>
        <result property="wlbTemplateType" column="wlb_template_type"/>
        <result property="cpType" column="cp_type"/>
        <result property="sysTemplateId" column="sys_template_id"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="cod" column="cod"/>
        <result property="obtainWay" column="obtain_way"/>
        <result property="templateType" column="template_type"/>
        <result property="liveStatus" column="live_status"/>
        <result property="printerName" column="printer_name"/>
    </resultMap>

    <!--电子面单的列表需要快递公司名 -->
    <resultMap id="SomeFieldsUserWlbExpressTemplateMap" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="expressId" column="express_id"/>
        <result property="wlbTemplateType" column="wlb_template_type"/>
        <result property="sysTemplateId" column="sys_template_id"/>
        <result property="cod" column="cod"/>
        <result property="templateType" column="template_type"/>
        <result property="liveStatus" column="live_status"/>
        <result property="printerName" column="printer_name"/>
        <result property="cloudTemplateId" column="cloud_template_id"/>
        <result property="cloudTemplateUrl" column="cloud_template_url"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="cpCode" column="cp_code"/>
        <result property="cpType" column="cp_type"/>
    </resultMap>

    <!--用来绑定的模板-->
    <resultMap id="CanBindTemplate" class="UserWlbExpressTemplate">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="name" column="name"/>
        <result property="nameDesc" column="name_desc"/>
        <result property="expressId" column="express_id"/>
        <result property="templateType" column="template_type"/>
        <result property="cpType" column="cp_type"/>
        <result property="cpCode" column="cp_code"/>
        <result property="branchAccountsStr" column="branch_accounts_str"/>
        <result property="sysTemplateId" column="sys_template_id"/>
        <result property="title" column="title"/>
        <result property="wlbType" column="wlb_type"/>
        <result property="wlbTemplateType" column="wlb_template_type"/>
        <result property="cloudTemplateId" column="cloud_template_id"/>
        <result property="obtainWay" column="obtain_way"/>
        <result property="liveStatus" column="live_status"/>
    </resultMap>

    <!-- 插入 -->
    <insert id="insert" parameterClass="UserWlbExpressTemplate">
        INSERT INTO user_wlb_express_template_#dbNo#
        <dynamic prepend="(" close=")">
            <isNotNull prepend="," property="id">id</isNotNull>
            <isNotNull prepend="," property="companyId">company_id</isNotNull>
            <isNotNull prepend="," property="taobaoId">taobao_id</isNotNull>
            <isNotNull prepend="," property="name">name</isNotNull>
            <isNotNull prepend="," property="nameDesc">name_desc</isNotNull>
            <isNotNull prepend="," property="expressId">express_id</isNotNull>
            <isNotNull prepend="," property="bgPathStandard">bg_path_standard</isNotNull>
            <isNotNull prepend="," property="bgPathCustom">bg_path_custom</isNotNull>
            <isNotNull prepend="," property="fontSizeCustom">font_size_custom</isNotNull>
            <isNotNull prepend="," property="fontCustom">font_custom</isNotNull>
            <isNotNull prepend="," property="boldCustom">bold_custom</isNotNull>
            <isNotNull prepend="," property="widthStandard">width_standard</isNotNull>
            <isNotNull prepend="," property="heightStandard">height_standard</isNotNull>
            <isNotNull prepend="," property="widthCustom">width_custom</isNotNull>
            <isNotNull prepend="," property="heightCustom">height_custom</isNotNull>
            <isNotNull prepend="," property="horizontalOffsetStandard">horizontal_offset_standard</isNotNull>
            <isNotNull prepend="," property="verticalOffsetStandard">vertical_offset_standard</isNotNull>
            <isNotNull prepend="," property="verticalOffsetDefaultStandard">vertical_offset_default_standard</isNotNull>
            <isNotNull prepend="," property="horizontalOffsetCustom">horizontal_offset_custom</isNotNull>
            <isNotNull prepend="," property="verticalOffsetCustom">vertical_offset_custom</isNotNull>
            <isNotNull prepend="," property="verticalOffsetDefaultCustom">vertical_offset_default_custom</isNotNull>
            <isNotNull prepend="," property="fieldSettingsStrStandard">field_settings_str_standard</isNotNull>
            <isNotNull prepend="," property="fieldSettingsStrCustom">field_settings_str_custom</isNotNull>
            <isNotNull prepend="," property="templatePathStandard">template_path_standard</isNotNull>
            <isNotNull prepend="," property="templatePathCustom">template_path_custom</isNotNull>
            <isNotNull prepend="," property="sortOrder">sort_order</isNotNull>
            <isNotNull prepend="," property="status">status</isNotNull>
            <isNotNull prepend="," property="templateType">template_type</isNotNull>
            <isNotNull prepend="," property="cpType">cp_type</isNotNull>
            <isNotNull prepend="," property="cpCode">cp_code</isNotNull>
            <isNotNull prepend="," property="branchAccountsStr">branch_accounts_str</isNotNull>
            <isNotNull prepend="," property="sysTemplateId">sys_template_id</isNotNull>
            <isNotNull prepend="," property="title">title</isNotNull>
            <isNotNull prepend="," property="fieldValuesStrStandard">field_values_str_standard</isNotNull>
            <isNotNull prepend="," property="fieldValuesStrCustom">field_values_str_custom</isNotNull>
            <isNotNull prepend="," property="printerName">printer_name</isNotNull>
            <isNotNull prepend="," property="shopUse">shop_use</isNotNull>
            <isNotNull prepend="," property="wlbType">wlb_type</isNotNull>
            <isNotNull prepend="," property="wlbTemplateType">wlb_template_type</isNotNull>
            <isNotNull prepend="," property="cainiaoEncryptedFields">cainiao_encrypted_fields</isNotNull>
            <isNotNull prepend="," property="cloudTemplateId">cloud_template_id</isNotNull>
            <isNotNull prepend="," property="cloudTemplateUrl">cloud_template_url</isNotNull>
            <isNotNull prepend="," property="standardCloudTemplateUrl">standard_cloud_template_url</isNotNull>
            <isNotNull prepend="," property="cod">cod</isNotNull>
            <isNotNull prepend="," property="isManulfill">is_manulfill</isNotNull>
            <isNotNull prepend="," property="serviceInfoChoiceStr">service_info_choice_str</isNotNull>
            <isNotNull prepend="," property="obtainWay">obtain_way</isNotNull>
            <isNotNull prepend="," property="cloudTemplatePaperType">cloud_template_paper_type</isNotNull>
            <isNotNull prepend="," property="modeInputs">mode_inputs</isNotNull>
            <isNotNull prepend="," property="cloudTemplateXml">cloud_template_xml</isNotNull>
            <isNotNull prepend="," property="fieldBoxList">field_box_list</isNotNull>
            <isNotNull prepend="," property="serviceValueStr">service_value_str</isNotNull>
            <isNotNull prepend="," property="ydConfigStr">yd_config_str</isNotNull>
            <isNotNull prepend="," property="cloudCustomAreaConfig">cloud_custom_area_config</isNotNull>
            <isNotNull prepend="," property="fieldValuesCustomMap">field_values_custom_map</isNotNull>
            <isNotNull prepend="," property="templateHtml">template_html</isNotNull>
            ,created
        </dynamic>
        VALUES
        <dynamic prepend="(" close=")">
            <isNotNull prepend="," property="id">#id#</isNotNull>
            <isNotNull prepend="," property="companyId">#companyId#</isNotNull>
            <isNotNull prepend="," property="taobaoId">#taobaoId#</isNotNull>
            <isNotNull prepend="," property="name">#name#</isNotNull>
            <isNotNull prepend="," property="nameDesc">#nameDesc#</isNotNull>
            <isNotNull prepend="," property="expressId">#expressId#</isNotNull>
            <isNotNull prepend="," property="bgPathStandard">#bgPathStandard#</isNotNull>
            <isNotNull prepend="," property="bgPathCustom">#bgPathCustom#</isNotNull>
            <isNotNull prepend="," property="fontSizeCustom">#fontSizeCustom#</isNotNull>
            <isNotNull prepend="," property="fontCustom">#fontCustom#</isNotNull>
            <isNotNull prepend="," property="boldCustom">#boldCustom#</isNotNull>
            <isNotNull prepend="," property="widthStandard">#widthStandard#</isNotNull>
            <isNotNull prepend="," property="heightStandard">#heightStandard#</isNotNull>
            <isNotNull prepend="," property="widthCustom">#widthCustom#</isNotNull>
            <isNotNull prepend="," property="heightCustom">#heightCustom#</isNotNull>
            <isNotNull prepend="," property="horizontalOffsetStandard">#horizontalOffsetStandard#</isNotNull>
            <isNotNull prepend="," property="verticalOffsetStandard">#verticalOffsetStandard#</isNotNull>
            <isNotNull prepend="," property="verticalOffsetDefaultStandard">#verticalOffsetDefaultStandard#</isNotNull>
            <isNotNull prepend="," property="horizontalOffsetCustom">#horizontalOffsetCustom#</isNotNull>
            <isNotNull prepend="," property="verticalOffsetCustom">#verticalOffsetCustom#</isNotNull>
            <isNotNull prepend="," property="verticalOffsetDefaultCustom">#verticalOffsetDefaultCustom#</isNotNull>
            <isNotNull prepend="," property="fieldSettingsStrStandard">#fieldSettingsStrStandard#</isNotNull>
            <isNotNull prepend="," property="fieldSettingsStrCustom">#fieldSettingsStrCustom#</isNotNull>
            <isNotNull prepend="," property="templatePathStandard">#templatePathStandard#</isNotNull>
            <isNotNull prepend="," property="templatePathCustom">#templatePathCustom#</isNotNull>
            <isNotNull prepend="," property="sortOrder">#sortOrder#</isNotNull>
            <isNotNull prepend="," property="status">#status#</isNotNull>
            <isNotNull prepend="," property="templateType">#templateType#</isNotNull>
            <isNotNull prepend="," property="cpType">#cpType#</isNotNull>
            <isNotNull prepend="," property="cpCode">#cpCode#</isNotNull>
            <isNotNull prepend="," property="branchAccountsStr">#branchAccountsStr#</isNotNull>
            <isNotNull prepend="," property="sysTemplateId">#sysTemplateId#</isNotNull>
            <isNotNull prepend="," property="title">#title#</isNotNull>
            <isNotNull prepend="," property="fieldValuesStrStandard">#fieldValuesStrStandard#</isNotNull>
            <isNotNull prepend="," property="fieldValuesStrCustom">#fieldValuesStrCustom#</isNotNull>
            <isNotNull prepend="," property="printerName">#printerName#</isNotNull>
            <isNotNull prepend="," property="shopUse">#shopUse#</isNotNull>
            <isNotNull prepend="," property="wlbType">#wlbType#</isNotNull>
            <isNotNull prepend="," property="wlbTemplateType">#wlbTemplateType#</isNotNull>
            <isNotNull prepend="," property="cainiaoEncryptedFields">#cainiaoEncryptedFields#</isNotNull>
            <isNotNull prepend="," property="cloudTemplateId">#cloudTemplateId#</isNotNull>
            <isNotNull prepend="," property="cloudTemplateUrl">#cloudTemplateUrl#</isNotNull>
            <isNotNull prepend="," property="standardCloudTemplateUrl">#standardCloudTemplateUrl#</isNotNull>
            <isNotNull prepend="," property="cod">#cod#</isNotNull>
            <isNotNull prepend="," property="isManulfill">#isManulfill#</isNotNull>
            <isNotNull prepend="," property="serviceInfoChoiceStr">#serviceInfoChoiceStr#</isNotNull>
            <isNotNull prepend="," property="obtainWay">#obtainWay#</isNotNull>
            <isNotNull prepend="," property="cloudTemplatePaperType">#cloudTemplatePaperType#</isNotNull>
            <isNotNull prepend="," property="modeInputs">#modeInputs#</isNotNull>
            <isNotNull prepend="," property="cloudTemplateXml">#cloudTemplateXml#</isNotNull>
            <isNotNull prepend="," property="fieldBoxList">#fieldBoxList#</isNotNull>
            <isNotNull prepend="," property="serviceValueStr">#serviceValueStr#</isNotNull>
            <isNotNull prepend="," property="ydConfigStr">#ydConfigStr#</isNotNull>
            <isNotNull prepend="," property="cloudCustomAreaConfig">#cloudCustomAreaConfig#</isNotNull>
            <isNotNull prepend="," property="fieldValuesCustomMap">#fieldValuesCustomMap#</isNotNull>
            <isNotNull prepend="," property="templateHtml">#templateHtml#</isNotNull>
            ,now()
        </dynamic>
        <selectKey resultClass="long" keyProperty="id">
            SELECT LAST_INSERT_ID() AS ID
        </selectKey>
    </insert>

    <!-- 根据主键查询 -->
    <select id="queryById" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo# WHERE id=#id# and company_id = #companyId# and enable_status = 1 and status = 1
    </select>

    <!-- 仅根据主键查询 -->
    <select id="queryOnlyById" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo# WHERE id=#id# and company_id = #companyId#
    </select>

    <!-- 根据主键查询 -->
    <select id="queryByIdAndWlbTemplateType" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo#
        WHERE id = #id# and company_id = #companyId# and enable_status = 1
        <isNotEmpty property="wlbTemplateType" prepend="and">wlb_template_type = #wlbTemplateType#</isNotEmpty>
    </select>

    <!-- 根据主键删除 -->
    <update id="delete" parameterClass="UserWlbExpressTemplate">
        UPDATE user_wlb_express_template_#dbNo# SET status = 0 WHERE id = #id# and company_id = #companyId#
    </update>

    <!-- 根据主键删除V2 -->
    <update id="deleteV2" parameterClass="UserWlbExpressTemplate">
        UPDATE user_wlb_express_template_#dbNo# SET enable_status = 0 WHERE id = #id# and company_id = #companyId#
    </update>

    <!-- 根据taobaoId删除 -->
    <update id="deleteByTaobaoId" parameterClass="hashMap">
        UPDATE user_wlb_express_template_#dbNo# SET status = 0 WHERE taobao_id = #taobaoId# and company_id =
        #companyId# and obtain_way = #obtainWay#
    </update>

    <!-- 根据taobaoId，但不包括cpCode -->
    <update id="deleteByTaobaoIdNotCpCode" parameterClass="hashMap">
        UPDATE user_wlb_express_template_#dbNo# SET status = 0 WHERE taobao_id = #taobaoId# and company_id =
        #companyId# and obtain_way = #obtainWay# and cp_code != #cpCode#
    </update>

    <!-- 根据taobaoId和wlbTemplateType删除 -->
    <update id="deleteByTaobaoIdV2" parameterClass="hashMap">
        UPDATE user_wlb_express_template_#dbNo# SET enable_status = 0 WHERE taobao_id = #taobaoId# and company_id =
        #companyId# and obtain_way = #obtainWay# and wlb_template_type = #wlbTemplateType#
    </update>

    <!-- 根据主键更新 -->
    <update id="update" parameterClass="UserWlbExpressTemplate">
        UPDATE user_wlb_express_template_#dbNo#
        <dynamic prepend="SET">
            <isNotNull prepend="," property="taobaoId">taobao_id = #taobaoId#</isNotNull>
            <isNotNull prepend="," property="name">name = #name#</isNotNull>
            <isNotNull prepend="," property="nameDesc">name_desc = #nameDesc#</isNotNull>
            <isNotNull prepend="," property="expressId">express_id = #expressId#</isNotNull>
            <isNotNull prepend="," property="bgPathStandard">bg_path_standard = #bgPathStandard#</isNotNull>
            <isNotNull prepend="," property="bgPathCustom">bg_path_custom = #bgPathCustom#</isNotNull>
            <isNotNull prepend="," property="fontSizeCustom">font_size_custom = #fontSizeCustom#</isNotNull>
            <isNotNull prepend="," property="fontCustom">font_custom = #fontCustom#</isNotNull>
            <isNotNull prepend="," property="boldCustom">bold_custom = #boldCustom#</isNotNull>
            <isNotNull prepend="," property="widthStandard">width_standard = #widthStandard#</isNotNull>
            <isNotNull prepend="," property="heightStandard">height_standard = #heightStandard#</isNotNull>
            <isNotNull prepend="," property="widthCustom">width_custom = #widthCustom#</isNotNull>
            <isNotNull prepend="," property="heightCustom">height_custom = #heightCustom#</isNotNull>
            <isNotNull prepend="," property="horizontalOffsetStandard">horizontal_offset_standard =
                #horizontalOffsetStandard#
            </isNotNull>
            <isNotNull prepend="," property="verticalOffsetStandard">vertical_offset_standard =
                #verticalOffsetStandard#
            </isNotNull>
            <isNotNull prepend="," property="verticalOffsetDefaultStandard">vertical_offset_default_standard =
                #verticalOffsetDefaultStandard#
            </isNotNull>
            <isNotNull prepend="," property="horizontalOffsetCustom">horizontal_offset_custom =
                #horizontalOffsetCustom#
            </isNotNull>
            <isNotNull prepend="," property="verticalOffsetCustom">vertical_offset_custom = #verticalOffsetCustom#
            </isNotNull>
            <isNotNull prepend="," property="verticalOffsetDefaultCustom">vertical_offset_default_custom =
                #verticalOffsetDefaultCustom#
            </isNotNull>
            <isNotNull prepend="," property="fieldSettingsStrStandard">field_settings_str_standard =
                #fieldSettingsStrStandard#
            </isNotNull>
            <isNotNull prepend="," property="fieldSettingsStrCustom">field_settings_str_custom =
                #fieldSettingsStrCustom#
            </isNotNull>
            <isNotNull prepend="," property="templatePathStandard">template_path_standard = #templatePathStandard#
            </isNotNull>
            <isNotNull prepend="," property="templatePathCustom">template_path_custom = #templatePathCustom#</isNotNull>
            <isNotNull prepend="," property="sortOrder">sort_order = #sortOrder#</isNotNull>
            <isNotNull prepend="," property="status">status = #status#</isNotNull>
            <isNotNull prepend="," property="enableStatus">enable_status = #enableStatus#</isNotNull>
            <isNotNull prepend="," property="templateType">template_type = #templateType#</isNotNull>
            <isNotNull prepend="," property="cpType">cp_type = #cpType#</isNotNull>
            <isNotNull prepend="," property="cpCode">cp_code = #cpCode#</isNotNull>
            <isNotNull prepend="," property="branchAccountsStr">branch_accounts_str = #branchAccountsStr#</isNotNull>
            <isNotNull prepend="," property="sysTemplateId">sys_template_id = #sysTemplateId#</isNotNull>
            <isNotNull prepend="," property="title">title = #title#</isNotNull>
            <isNotNull prepend="," property="fieldValuesStrStandard">field_values_str_standard =
                #fieldValuesStrStandard#
            </isNotNull>
            <isNotNull prepend="," property="fieldValuesStrCustom">field_values_str_custom = #fieldValuesStrCustom#
            </isNotNull>
            <isNotNull prepend="," property="printerName">printer_name = #printerName#</isNotNull>
            <isNotNull prepend="," property="shopUse">shop_use = #shopUse#</isNotNull>
            <isNotNull prepend="," property="wlbType">wlb_type = #wlbType#</isNotNull>
            <isNotNull prepend="," property="wlbTemplateType">wlb_template_type = #wlbTemplateType#</isNotNull>
            <isNotNull prepend="," property="cainiaoEncryptedFields">cainiao_encrypted_fields =
                #cainiaoEncryptedFields#
            </isNotNull>
            <isNotNull prepend="," property="cod">cod = #cod#</isNotNull>
            <isNotNull prepend="," property="cloudTemplateUrl">cloud_template_url = #cloudTemplateUrl#</isNotNull>
            <isNotNull prepend="," property="standardCloudTemplateUrl">standard_cloud_template_url =
                #standardCloudTemplateUrl#
            </isNotNull>
            <isNotNull prepend="," property="serviceInfoChoiceStr">service_info_choice_str = #serviceInfoChoiceStr#
            </isNotNull>
            <isNotNull prepend="," property="modeInputs">mode_inputs = #modeInputs#</isNotNull>
            <isNotNull prepend="," property="cloudTemplateXml">cloud_template_xml = #cloudTemplateXml#</isNotNull>
            <isNotNull prepend="," property="fieldBoxList">field_box_list = #fieldBoxList#</isNotNull>
            <isNotNull prepend="," property="cloudTemplateId">cloud_template_id = #cloudTemplateId#</isNotNull>
            <isNotNull prepend="," property="cloudTemplatePaperType">cloud_template_paper_type =
                #cloudTemplatePaperType#
            </isNotNull>
            <isNotNull prepend="," property="obtainWay">obtain_way = #obtainWay#</isNotNull>
            <isNotNull prepend="," property="serviceValueStr">service_value_str = #serviceValueStr#</isNotNull>
            <isNotEmpty prepend="," property="liveStatus">live_status = #liveStatus#</isNotEmpty>
            <isNotEmpty prepend="," property="ydConfigStr">yd_config_str = #ydConfigStr#</isNotEmpty>
            <isNotEmpty prepend="," property="cloudCustomAreaConfig">cloud_custom_area_config = #cloudCustomAreaConfig#</isNotEmpty>
            <isNotNull prepend="," property="fieldValuesCustomMap">field_values_custom_map = #fieldValuesCustomMap#</isNotNull>
            <isNotNull prepend="," property="updateStatus">update_status = #updateStatus#</isNotNull>
            <isNotNull prepend="," property="templateHtml">template_html = #templateHtml#</isNotNull>
        </dynamic>
        WHERE id = #id# and company_id = #companyId#
    </update>

    <!-- 分页查询 -->
    <select id="getUserWlbExpressTemplatePage" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT *
        FROM user_wlb_express_template_#dbNo#
        WHERE enable_status = 1 and shop_use = 1 and status = 1 and wlb_template_type in (0, 1, 2, 3, 5, 6, 12, 17, 18,21, 19, 9, 27,30, 180,29, 31, 36, 37, 39, 40,  43, 44, 45,46)
        <isNotEmpty property="id" prepend="and">
            id = #id#
        </isNotEmpty>
        <isNotEmpty property="companyId" prepend="and">
            company_id = #companyId#
        </isNotEmpty>
        <isNotEmpty property="taobaoId" prepend="and">
            taobao_id = #taobaoId#
        </isNotEmpty>
        <isNotEmpty property="expressId" prepend="and">
            express_id = #expressId#
        </isNotEmpty>
        <isNotEmpty property="expressIds" prepend="and">
            express_id in
            <iterate property="expressIds" conjunction="," open="(" close=")">
                #expressIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysTemplateId" prepend="and">
            sys_template_id = #sysTemplateId#
        </isNotEmpty>
        <isNotEmpty property="name" prepend="and">
            name = #name#
        </isNotEmpty>
        <isNotEmpty property="cpType" prepend="and">
            cp_type = #cpType#
        </isNotEmpty>
        <isNotEmpty property="cpCode" prepend="and">
            cp_code = #cpCode#
        </isNotEmpty>
        <isNotEmpty property="wlbType" prepend="and">
            wlb_type = #wlbType#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateType" prepend="and">
            wlb_template_type = #wlbTemplateType#
        </isNotEmpty>
        <isNotEmpty property="wlbTypeNotIn" prepend="and">
            wlb_type not in
            <iterate property="wlbTypeNotIn" conjunction="," open="(" close=")">
                #wlbTypeNotIn[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="obtainWay" prepend="and">
            obtain_way = #obtainWay#
        </isNotEmpty>
        <isNotEmpty property="isManulfill" prepend="and">
            is_manulfill = #isManulfill#
        </isNotEmpty>
        <isNotEmpty property="liveStatus" prepend="and">
            live_status = #liveStatus#
        </isNotEmpty>
        order by sort_order
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>
    <!-- 分页总数 -->
    <select id="getUserWlbExpressTemplateCount" resultClass="long" parameterClass="hashMap">
        SELECT count(1) FROM user_wlb_express_template_#dbNo#
        WHERE enable_status = 1 and shop_use = 1 and status = 1
        <isNotEmpty property="id" prepend="and">
            id = #id#
        </isNotEmpty>
        <isNotEmpty property="expressId" prepend="and">
            express_id = #expressId#
        </isNotEmpty>
        <isNotEmpty property="sysTemplateId" prepend="and">
            sys_template_id = #sysTemplateId#
        </isNotEmpty>
        <isNotEmpty property="companyId" prepend="and">
            company_id = #companyId#
        </isNotEmpty>
        <isNotEmpty property="taobaoId" prepend="and">
            taobao_id = #taobaoId#
        </isNotEmpty>
        <isNotEmpty property="name" prepend="and">
            name = #name#
        </isNotEmpty>
        <isNotEmpty property="cpType" prepend="and">
            cp_type = #cpType#
        </isNotEmpty>
        <isNotEmpty property="cpCode" prepend="and">
            cp_code = #cpCode#
        </isNotEmpty>
        <isNotEmpty property="wlbType" prepend="and">
            wlb_type = #wlbType#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateType" prepend="and">
            wlb_template_type = #wlbTemplateType#
        </isNotEmpty>
        <isNotEmpty property="liveStatus" prepend="and">
            live_status = #liveStatus#
        </isNotEmpty>
    </select>

    <!--得到该用户的所有expressId-->
    <select id="getExpressAndIdMap" resultMap="UserWlbExpressTemplateResultMap1" parameterClass="hashMap">
        select express_id,id,is_default,branch_accounts_str,live_status from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and shop_use = 1 and status = 1 and wlb_template_type in
        (0, 1)
        and obtain_way = 1
        <isNotEmpty property="taobaoId">
            and taobao_id = #taobaoId#
        </isNotEmpty>
    </select>

    <!--得到所有加盟型电子面单-->
    <select id="queryAllJoinSaleType" resultMap="UserWlbExpressTemplateResultMap6" parameterClass="hashMap">
        select id,branch_accounts_str,company_id,wlb_template_type from user_wlb_express_template_#dbNo#
        where enable_status = 1 and cp_type = 2 and wlb_template_type in (0,2)
    </select>

    <!--得到该用户的所有京东快递模板的isManulfill-->
    <select id="getJdIsManulfillIdMap" resultMap="UserWlbExpressTemplateResultMap4" parameterClass="hashMap">
        select is_manulfill,id,is_default,branch_accounts_str from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and shop_use = 1 and status = 1
        and wlb_template_type = 3
        <isNotEmpty property="taobaoId">
            and taobao_id = #taobaoId#
        </isNotEmpty>
    </select>

    <!--根据id集合得到快递模版名称-->
    <select id="queryNameByIds" parameterClass="hashMap" resultMap="UserWlbExpressTemplateResultMap2">
        select id, name, express_id from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and shop_use = 1 and status = 1 and id in
        <iterate property="ids" conjunction="," open="(" close=")">
            #ids[]#
        </iterate>
    </select>

    <!--根据id集合得到cpCode-->
    <select id="queryCpCodeByIds" parameterClass="hashMap" resultMap="UserWlbExpressTemplateResultMap3">
        select id, cp_code, taobao_id, wlb_template_type, wlb_type, name, express_id, template_type, field_values_str_standard, field_values_str_custom from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and shop_use = 1 and status = 1 and id in
        <iterate property="ids" conjunction="," open="(" close=")">
            #ids[]#
        </iterate>
    </select>

    <!--得到该用户的所有模版名称-->
    <select id="getWlbTemplateName" parameterClass="hashMap" resultMap="UserWlbExpressTemplateNameResultMap">
        select taobao_id, id, name, cloud_template_id, obtain_way, cp_code, sys_template_id, wlb_template_type from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and shop_use = 1 and status = 1
        <isNotEmpty property="cpType" prepend="and">
            cp_type = #cpType#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateType" prepend="and">
            wlb_template_type = #wlbTemplateType#
        </isNotEmpty>
        <isNotEmpty property="wlbType" prepend="and">
            wlb_type = #wlbType#
        </isNotEmpty>
    </select>

    <!--查询电子快递单模版列表-->
    <select id="getUserWlbExpressNoMix" parameterClass="hashMap" resultMap="UserExpressWlbTemplateResultMap">
        select id, name, express_id, 1 as is_wlb, express_id as group_id, wlb_template_type, cp_type,
        sys_template_id, taobao_id,cod,obtain_way,template_type,live_status,printer_name
        from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and shop_use = 1 and status = 1 and wlb_template_type in
        (0, 1, 3)
        order by sort_order
    </select>

    <!--得到该用户的最小排序号-->
    <select id="getMinSortOrderByCompany" parameterClass="hashMap" resultClass="int">
        select min(sort_order) from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and status = 1 and wlb_template_type = #wlbTemplateType#
    </select>


    <!--得到该用户的最大排序号-->
    <select id="getMaxSortOrderByCompany" parameterClass="hashMap" resultClass="int">
        select max(sort_order) from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and status = 1 and wlb_template_type = #wlbTemplateType#
    </select>

    <!--根据排序方式得到对应模版-->
    <select id="getTemplateBySortOrder" parameterClass="hashMap" resultMap="UserWlbExpressTemplateResultMap">
        SELECT * FROM user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and status = 1 and wlb_template_type = #wlbTemplateType#
        <isEqual property="isUp" compareValue="1" prepend="and">
            sort_order &lt; (select sort_order from user_wlb_express_template_#dbNo# where enable_status = 1 and status
            = 1 and id = #id# and wlb_template_type = #wlbTemplateType#)
            order by sort_order desc
        </isEqual>
        <isEqual property="isUp" compareValue="0" prepend="and">
            sort_order > (select sort_order from user_wlb_express_template_#dbNo# where enable_status = 1 and status = 1
            and id = #id# and wlb_template_type = #wlbTemplateType#)
            order by sort_order
        </isEqual>
        limit 0, 1
    </select>

    <!--根据id得到排序号-->
    <select id="querySortOrderById" parameterClass="hashMap" resultClass="int">
        select sort_order from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and status = 1 and id = #id#
    </select>

    <update id="updateByTaobaoId" parameterClass="hashMap">
        UPDATE user_wlb_express_template_#dbNo#
        <dynamic prepend="SET">
            <isNotNull prepend="," property="shopUse">
                shop_use = #shopUse#
            </isNotNull>
        </dynamic>
        WHERE
        company_id = #companyId# and taobao_id = #taobaoId# and enable_status = 1
        <isNotNull prepend="and" property="obtainWay">
            obtain_way = #obtainWay#
        </isNotNull>
    </update>

    <select id="queryTemplateWithWlbTemplateTypes" parameterClass="hashMap" resultMap="UserExpressWlbTemplateResultMap">
        select id, name, express_id, 1 as is_wlb, express_id as group_id, wlb_template_type, cp_type,
        sys_template_id, taobao_id,cod,obtain_way,template_type,live_status,printer_name
        from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and shop_use = 1 and status = 1 and wlb_template_type in
        <iterate open="(" close=")" property="wlbTemplateTypes" conjunction=",">
            #wlbTemplateTypes[]#
        </iterate>
        order by wlb_template_type, sort_order
    </select>

    <!-- 分页查询 -->
    <select id="getUserJdExpressTemplatePage" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo#
        WHERE enable_status = 1 and shop_use = 1 and status = 1 and wlb_type = 500 and wlb_template_type = 3
        <isNotEmpty property="id" prepend="and">
            id = #id#
        </isNotEmpty>
        <isNotEmpty property="companyId" prepend="and">
            company_id = #companyId#
        </isNotEmpty>
        <isNotEmpty property="taobaoId" prepend="and">
            taobao_id = #taobaoId#
        </isNotEmpty>
        <isNotEmpty property="expressId" prepend="and">
            express_id = #expressId#
        </isNotEmpty>
        <isNotEmpty property="sysTemplateId" prepend="and">
            sys_template_id = #sysTemplateId#
        </isNotEmpty>
        <isNotEmpty property="name" prepend="and">
            name = #name#
        </isNotEmpty>
        <isNotEmpty property="liveStatus" prepend="and">
            live_status = #liveStatus#
        </isNotEmpty>
        order by sort_order
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="queryBranchAccountsByExpressId" resultMap="UserWlbExpressTemplateResultMap5" parameterClass="hashMap">
        select id, branch_accounts_str from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and express_id = #expressId# and enable_status = 1 and shop_use = 1 and status =
        1
        <isNotEmpty property="wlbTemplateType" prepend="and">
            wlb_template_type = #wlbTemplateType#
        </isNotEmpty>
    </select>

    <select id="getNotDeleteExpressIds" resultMap="UserWlbExpressTemplateResultMap7" parameterClass="hashMap">
        SELECT distinct(express_id)
        FROM user_wlb_express_template_#dbNo#
        WHERE enable_status = 1 and shop_use = 1 and status = 1 and wlb_template_type = 0 and obtain_way = 1
        <isNotEmpty property="companyId" prepend="and">
            company_id = #companyId#
        </isNotEmpty>
        <isNotEmpty property="expressIds" prepend="and">
            express_id in
            <iterate property="expressIds" conjunction="," open="(" close=")">
                #expressIds[]#
            </iterate>
        </isNotEmpty>
    </select>


    <!--得到该公司所有加盟型电子面单-->
    <select id="queryAllJoinSaleTypeByCompanyId" resultMap="UserWlbExpressTemplateResultMap6" parameterClass="hashMap">
        select id,branch_accounts_str,company_id,wlb_template_type from user_wlb_express_template_#dbNo#
        where enable_status = 1 and cp_type = 2 and wlb_template_type in (0,2,6,12,10,5,17,18,19,8,9,21,30,180,29, 31, 36, 37, 39, 40, 43, 44, 45,46) and company_id = #companyId#
    </select>

    <!--得到该公司所有分单电子面单-->
    <select id="queryAllDivideTemplate" resultMap="UserWlbExpressTemplateResultMap6" parameterClass="hashMap">
        select id,branch_accounts_str,company_id,wlb_template_type from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1
        <isNotEmpty property="wlbTemplateTypes" prepend="and">
            wlb_template_type in
            <iterate property="wlbTemplateTypes" conjunction="," open="(" close=")">
                #wlbTemplateTypes[]#
            </iterate>
        </isNotEmpty>
    </select>

    <!--查询部分字段-->
    <select id="querySomeFields" parameterClass="hashMap" resultMap="UserWlbExpressTemplateResultExpressNameMap">
        SELECT
        id,name,express_id,sys_template_id,taobao_id,wlb_template_type,live_status,printer_name
        FROM user_wlb_express_template_#dbNo#
        WHERE
        company_id = #companyId# and enable_status = 1 and status = 1
        <isNotEmpty property="ids" prepend="and">
            id in
            <iterate property="ids" open="(" conjunction="," close=")">#ids[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateType" prepend="and">
            wlb_template_type = #wlbTemplateType#
        </isNotEmpty>
        <isNotNull property="expressId" prepend="and">
            express_id = #expressId#
        </isNotNull>
        <isNotEmpty property="expressIds" prepend=" and ">
            express_id in
            <iterate property="expressIds" open="(" conjunction="," close=")">
                #expressIds[]#
            </iterate>
        </isNotEmpty>
    </select>

    <!--查询部分字段-->
    <select id="queryExpressIdAndCpCodeByIds" parameterClass="hashMap" resultMap="UserWlbExpressTemplateResultExpressIdAndCpCode">
        SELECT
        id, taobao_id, express_id, cp_code, branch_accounts_str, cp_type, wlb_template_type, wlb_type, obtain_way, live_status
        FROM user_wlb_express_template_#dbNo#
        WHERE
        company_id = #companyId# and enable_status = 1 and status = 1
        <isNotEmpty property="ids" prepend="and">
            id in
            <iterate property="ids" open="(" conjunction="," close=")">#ids[]#</iterate>
        </isNotEmpty>
    </select>

    <!-- 根据cpcode 查询 jitx 模板 -->
    <select id="getJitxTemplateByCpCode" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo#
        WHERE
        company_id = #companyId# and cp_code = #cpCode# and enable_status = 1 and wlb_template_type =7
        limit 0, 1
    </select>

    <!-- 根据cpcode 查询 jitx 模板 -->
    <select id="getJitxTemplateByExpressId" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo#
        WHERE
        company_id = #companyId# and express_id = #expressId# and enable_status = 1 and wlb_template_type =7
        limit 0, 1
    </select>

    <!-- 根据模版id查询jitx 模板 -->
    <select id="getJitxTemplateById" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo#
        WHERE
        company_id = #companyId# and id = #id# and enable_status = 1
        limit 0, 1
    </select>

    <!-- 查询公司下所有可用模版 -->
    <select id="queryAll" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo# WHERE company_id = #companyId# and enable_status = 1
    </select>

    <!-- 查询公司下所有可用模版 -->
    <select id="queryAllExpressId" resultMap="ExpressIdResultMap" parameterClass="hashMap">
        SELECT distinct(express_id) as express_id FROM user_Wlb_Express_Template_#dbNo# WHERE company_id = #companyId# and enable_status = 1
    </select>

    <!-- 查询所有字段 -->
    <select id="getAllFields" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo#
        WHERE
        company_id = #companyId# and enable_status = 1
        <isNotEmpty property="ids" prepend=" and ">
            id in
            <iterate property="ids" open="(" conjunction="," close=")">
                #ids[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="expressId" prepend=" and ">
            express_id = #expressId#
        </isNotEmpty>
        <isNotEmpty property="expressIds" prepend=" and ">
            express_id in
            <iterate property="expressIds" open="(" conjunction="," close=")">
                #expressIds[]#
            </iterate>
        </isNotEmpty>
    </select>

    <select id="getGroupTemplateIdsById" resultClass="long" parameterClass="hashMap">
        SELECT id
        FROM user_wlb_express_template_#dbNo# a
        WHERE EXISTS (
        SELECT 1
        FROM (
        SELECT a.company_id AS company_id, a.express_id AS express_id, a.wlb_type AS wlb_type, a.wlb_template_type AS wlb_template_type, a.cloud_template_paper_type AS cloud_template_paper_type
        FROM user_wlb_express_template_#dbNo# a
        WHERE a.company_id = #companyId#
        AND a.id =  #id#
        AND a.obtain_way = 1
        ) t
        WHERE (a.company_id = t.company_id
        AND a.express_id = t.express_id
        AND a.wlb_type = t.wlb_type
        AND a.wlb_template_type = t.wlb_template_type
        AND a.cloud_template_paper_type = t.cloud_template_paper_type
        AND a.enable_status = 1
        AND a.status = 1
        AND a.obtain_way = 1)
        );
    </select>

    <!--根据面单类型查询用户模板列表-->
    <select id="listTemplateByCondition" resultMap="listByConditionResultMap" parameterClass="hashMap">
        select * from user_wlb_express_template_#dbNo#
        where company_id = #companyId# and enable_status = 1 and shop_use = 1 and status = 1
        <isNotEmpty property="taobaoId" prepend="and">
            taobao_id = #taobaoId#
        </isNotEmpty>
        <isNotEmpty property="obtainWay" prepend="and">
            obtain_way = #obtainWay#
        </isNotEmpty>
        <isNotEmpty property="cloudTemplatePaperType" prepend="and">
            cloud_template_paper_type = #cloudTemplatePaperType#
        </isNotEmpty>
        <isNotEmpty property="expressId" prepend="and">
            express_id = #expressId#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateTypes" prepend="and">
            wlb_template_type in
            <iterate open="(" close=")" property="wlbTemplateTypes" conjunction=",">
                #wlbTemplateTypes[]#
            </iterate>
        </isNotEmpty>
    </select>

    <select id="queryByIds" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo#
        WHERE id in
        <iterate property="ids" open="(" conjunction="," close=")">
            #ids[]#
        </iterate>
    </select>
    <!-- 无条件查询 -->
    <select id="getWlbExpressTemplates" resultMap="UserWlbExpressTemplateResultMap" parameterClass="hashMap">
        SELECT * FROM user_wlb_express_template_#dbNo#
        WHERE 1 = 1
        <isNotEmpty property="enableStatus" prepend="and">
            enable_status = #enableStatus#
        </isNotEmpty>
        <isNotEmpty property="id" prepend="and">
            id = #id#
        </isNotEmpty>
        <isNotEmpty property="companyId" prepend="and">
            company_id = #companyId#
        </isNotEmpty>
        <isNotEmpty property="taobaoId" prepend="and">
            taobao_id = #taobaoId#
        </isNotEmpty>
        <isNotEmpty property="expressId" prepend="and">
            express_id = #expressId#
        </isNotEmpty>
        <isNotEmpty property="sysTemplateId" prepend="and">
            sys_template_id = #sysTemplateId#
        </isNotEmpty>
        <isNotEmpty property="name" prepend="and">
            name = #name#
        </isNotEmpty>
        <isNotEmpty property="liveStatus" prepend="and">
            live_status = #liveStatus#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateType" prepend="and">
            wlb_template_type = #wlbTemplateType#
        </isNotEmpty>
        <isNotEmpty property="wlbType" prepend="and">
            wlb_type = #wlbType#
        </isNotEmpty>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>
    <select id="queryByParams" resultMap="UserWlbExpressTemplateResultMap" parameterClass="com.raycloud.dmj.template.wlb.SearchWlbTemplateParams">
        SELECT * FROM user_wlb_express_template_#dbNo#
        WHERE enable_status = 1 and status = 1 and shop_use = 1
        <isNotEmpty property="companyId" prepend="and">
            company_id = #companyId#
        </isNotEmpty>
        <isNotEmpty property="id" prepend="and">
            id = #id#
        </isNotEmpty>
        <isNotEmpty property="ids" prepend=" and ">
            id in
            <iterate property="ids" open="(" conjunction="," close=")">
                #ids[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="name" prepend="and">
            name = #name#
        </isNotEmpty>
        <isNotEmpty property="expressId" prepend="and">
            express_id = #expressId#
        </isNotEmpty>
        <isNotEmpty property="expressIds" prepend=" and ">
            express_id in
            <iterate property="expressIds" open="(" conjunction="," close=")">
                #expressIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysTemplateId" prepend="and">
            sys_template_id = #sysTemplateId#
        </isNotEmpty>
        <isNotEmpty property="groupId" prepend="and">
            group_id = #groupId#
        </isNotEmpty>
        <isNotEmpty property="cod" prepend="and">
            cod = #cod#
        </isNotEmpty>
        <isNotEmpty property="status" prepend="and">
            status = #status#
        </isNotEmpty>
        <isNotEmpty property="liveStatus" prepend="and">
            live_status = #liveStatus#
        </isNotEmpty>
        <isNotEmpty property="taobaoId" prepend="and">
            taobao_id = #taobaoId#
        </isNotEmpty>
        <isNotEmpty property="taobaoIdList" prepend=" and ">
            taobao_id in
            <iterate open="(" close=")" property="taobaoIdList" conjunction=",">
                #taobaoIdList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="nameDesc" prepend="and">
            name_desc = #nameDesc#
        </isNotEmpty>
        <isNotEmpty property="templateType" prepend="and">
            template_type = #templateType#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateType" prepend="and">
            wlb_template_type = #wlbTemplateType#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateTypes" prepend=" and ">
            wlb_template_type in
            <iterate open="(" close=")" property="wlbTemplateTypes" conjunction=",">
                #wlbTemplateTypes[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="title" prepend="and">
            title = #title#
        </isNotEmpty>
        <isNotEmpty property="cpType" prepend="and">
            cp_type = #cpType#
        </isNotEmpty>
        <isNotEmpty property="cpCode" prepend="and">
            cp_code = #cpCode#
        </isNotEmpty>
        <isNotEmpty property="wlbType" prepend="and">
            wlb_type = #wlbType#
        </isNotEmpty>
        <isNotEmpty property="cloudTemplateId" prepend="and">
            cloud_template_id = #cloudTemplateId#
        </isNotEmpty>
        <isNotEmpty property="obtainWay" prepend="and">
            obtain_way = #obtainWay#
        </isNotEmpty>
        <isNotEmpty property="cloudTemplatePaperType" prepend="and">
            cloud_template_paper_type = #cloudTemplatePaperType#
        </isNotEmpty>
        <isNotEmpty property="cloudTemplatePaperTypes" prepend=" and ">
            cloud_template_paper_type in
            <iterate open="(" close=")" property="cloudTemplatePaperTypes" conjunction=",">
                #cloudTemplatePaperTypes[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="wlbTypes" prepend="and">
            wlb_type in
            <iterate open="(" close=")" property="wlbTypes" conjunction=",">
                #wlbTypes[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="sortField">
            ORDER BY $sortField$
            <isNotEmpty property="sortOrder">
                $sortOrder$
            </isNotEmpty>
        </isNotEmpty>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="querySomeFieldsByParams" resultMap="SomeFieldsUserWlbExpressTemplateMap" parameterClass="com.raycloud.dmj.template.wlb.SearchWlbTemplateParams">
        SELECT id,name,express_id,wlb_template_type,sys_template_id,cod,template_type,live_status,printer_name,cloud_template_id,cloud_template_url,cp_type,cp_code,taobao_id
        FROM user_wlb_express_template_#dbNo#
        WHERE company_id = #companyId#
        <isNotEqual property="queryAll" compareValue="1" prepend="and">
            enable_status = 1 and status = 1
        </isNotEqual>
        <isNotEmpty property="id" prepend="and">
            id = #id#
        </isNotEmpty>
        <isNotEmpty property="ids" prepend=" and ">
            id in
            <iterate property="ids" open="(" conjunction="," close=")">
                #ids[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="name" prepend="and">
            name = #name#
        </isNotEmpty>
        <isNotEmpty property="expressId" prepend="and">
            express_id = #expressId#
        </isNotEmpty>
        <isNotEmpty property="expressIds" prepend=" and ">
            express_id in
            <iterate property="expressIds" open="(" conjunction="," close=")">
                #expressIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysTemplateId" prepend="and">
            sys_template_id = #sysTemplateId#
        </isNotEmpty>
        <isNotEmpty property="groupId" prepend="and">
            group_id = #groupId#
        </isNotEmpty>
        <isNotEmpty property="cod" prepend="and">
            cod = #cod#
        </isNotEmpty>
        <isNotEmpty property="status" prepend="and">
            status = #status#
        </isNotEmpty>
        <isNotEmpty property="liveStatus" prepend="and">
            live_status = #liveStatus#
        </isNotEmpty>
        <isNotEmpty property="shopUse" prepend="and">
            shop_use = #shopUse#
        </isNotEmpty>
        <isNotEmpty property="taobaoId" prepend="and">
            taobao_id = #taobaoId#
        </isNotEmpty>
        <isNotEmpty property="nameDesc" prepend="and">
            name_desc = #nameDesc#
        </isNotEmpty>
        <isNotEmpty property="templateType" prepend="and">
            template_type = #templateType#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateType" prepend="and">
            wlb_template_type = #wlbTemplateType#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateTypes" prepend=" and ">
            wlb_template_type in
            <iterate open="(" close=")" property="wlbTemplateTypes" conjunction=",">
                #wlbTemplateTypes[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="title" prepend="and">
            title = #title#
        </isNotEmpty>
        <isNotEmpty property="cpType" prepend="and">
            cp_type = #cpType#
        </isNotEmpty>
        <isNotEmpty property="cpCode" prepend="and">
            cp_code = #cpCode#
        </isNotEmpty>
        <isNotEmpty property="wlbType" prepend="and">
            wlb_type = #wlbType#
        </isNotEmpty>
        <isNotEmpty property="cloudTemplateId" prepend="and">
            cloud_template_id = #cloudTemplateId#
        </isNotEmpty>
        <isNotEmpty property="obtainWay" prepend="and">
            obtain_way = #obtainWay#
        </isNotEmpty>
        <isNotEmpty property="cloudTemplatePaperType" prepend="and">
            cloud_template_paper_type = #cloudTemplatePaperType#
        </isNotEmpty>
        <isNotEmpty property="sortField">
            ORDER BY $sortField$
            <isNotEmpty property="sortOrder">
                $sortOrder$
            </isNotEmpty>
        </isNotEmpty>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="queryExpressIds" resultClass="long" parameterClass="com.raycloud.dmj.template.wlb.SearchWlbTemplateParams">
        SELECT distinct(express_id) FROM user_wlb_express_template_#dbNo#
        WHERE enable_status = 1 and status = 1 and shop_use = 1
        <isNotEmpty property="companyId" prepend="and">
            company_id = #companyId#
        </isNotEmpty>
        <isNotEmpty property="id" prepend="and">
            id = #id#
        </isNotEmpty>
        <isNotEmpty property="ids" prepend=" and ">
            id in
            <iterate property="ids" open="(" conjunction="," close=")">
                #ids[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="name" prepend="and">
            name = #name#
        </isNotEmpty>
        <isNotEmpty property="expressId" prepend="and">
            express_id = #expressId#
        </isNotEmpty>
        <isNotEmpty property="expressIds" prepend=" and ">
            express_id in
            <iterate property="expressIds" open="(" conjunction="," close=")">
                #expressIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysTemplateId" prepend="and">
            sys_template_id = #sysTemplateId#
        </isNotEmpty>
        <isNotEmpty property="groupId" prepend="and">
            group_id = #groupId#
        </isNotEmpty>
        <isNotEmpty property="cod" prepend="and">
            cod = #cod#
        </isNotEmpty>
        <isNotEmpty property="status" prepend="and">
            status = #status#
        </isNotEmpty>
        <isNotEmpty property="liveStatus" prepend="and">
            live_status = #liveStatus#
        </isNotEmpty>
        <isNotEmpty property="taobaoId" prepend="and">
            taobao_id = #taobaoId#
        </isNotEmpty>
        <isNotEmpty property="nameDesc" prepend="and">
            name_desc = #nameDesc#
        </isNotEmpty>
        <isNotEmpty property="templateType" prepend="and">
            template_type = #templateType#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateType" prepend="and">
            wlb_template_type = #wlbTemplateType#
        </isNotEmpty>
        <isNotEmpty property="wlbTemplateTypes" prepend=" and ">
            wlb_template_type in
            <iterate open="(" close=")" property="wlbTemplateTypes" conjunction=",">
                #wlbTemplateTypes[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="title" prepend="and">
            title = #title#
        </isNotEmpty>
        <isNotEmpty property="cpType" prepend="and">
            cp_type = #cpType#
        </isNotEmpty>
        <isNotEmpty property="cpCode" prepend="and">
            cp_code = #cpCode#
        </isNotEmpty>
        <isNotEmpty property="wlbType" prepend="and">
            wlb_type = #wlbType#
        </isNotEmpty>
        <isNotEmpty property="cloudTemplateId" prepend="and">
            cloud_template_id = #cloudTemplateId#
        </isNotEmpty>
        <isNotEmpty property="obtainWay" prepend="and">
            obtain_way = #obtainWay#
        </isNotEmpty>
        <isNotEmpty property="cloudTemplatePaperType" prepend="and">
            cloud_template_paper_type = #cloudTemplatePaperType#
        </isNotEmpty>
        <isNotEmpty property="wlbTypes" prepend="and">
            wlb_type in
            <iterate open="(" close=")" property="wlbTypes" conjunction=",">
                #wlbTypes[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>


    <select id="queryAllTemplates" resultMap="UserWlbExpressTemplateResultMap8"  parameterClass="com.raycloud.dmj.template.wlb.SearchWlbTemplateParams">
        SELECT id,express_id,wlb_template_type FROM user_wlb_express_template_#dbNo#
        WHERE enable_status = 1 and status = 1 and shop_use = 1
        <isNotEmpty property="companyId" prepend="and">
            company_id = #companyId#
        </isNotEmpty>
    </select>

    <!-- 根据taobaoId删除 -->
    <update id="deleteByTemplateIds" parameterClass="hashMap">
        UPDATE user_wlb_express_template_#dbNo# SET status = 0 WHERE company_id = #companyId#
        <isNotEmpty property="templateIds" prepend="and">
            id in
            <iterate open="(" close=")" property="templateIds" conjunction=",">
                #templateIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="obtainWay" prepend="and">
            obtain_way = #obtainWay#
        </isNotEmpty>
    </update>

    <select id="queryUsedWlbTemplateTypeList" parameterClass="hashMap" resultClass="int">
        SELECT wlb_template_type FROM user_wlb_express_template_#dbNo#
        WHERE enable_status = 1 and status = 1 and shop_use = 1 and live_status = 1 and company_id = #companyId#
        group by wlb_template_type order by wlb_template_type
    </select>
</sqlMap>

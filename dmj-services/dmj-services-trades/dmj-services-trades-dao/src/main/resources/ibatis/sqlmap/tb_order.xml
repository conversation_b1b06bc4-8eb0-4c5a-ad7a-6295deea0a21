<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="TbOrder">
    <!-- Alias Map Defined -->
    <typeAlias alias="TbOrder" type="com.raycloud.dmj.domain.trades.TbOrder"/>
    <resultMap id="orderBase" class="TbOrder">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="oid" column="oid"/>
        <result property="numIid" column="num_iid"/>
        <result property="skuId" column="sku_id"/>
        <result property="status" column="status"/>
        <result property="title" column="title"/>
        <result property="cost" column="cost"/>
        <result property="price" column="price"/>
        <result property="itemSysId" column="item_sys_id"/>
        <result property="skuSysId" column="sku_sys_id"/>
        <result property="num" column="num"/>
        <result property="outerSkuId" column="outer_sku_id"/>
        <result property="totalFee" column="total_fee"/>
        <result property="payment" column="payment"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="acPayment" column="ac_payment" />
        <result property="saleFee" column="sale_fee"/>
        <result property="salePrice" column="sale_price"/>
        <result property="discountFee" column="discount_fee"/>
        <result property="adjustFee" column="adjust_fee"/>
        <result property="modified" column="modified"/>
        <result property="skuPropertiesName" column="sku_properties_name"/>
        <result property="refundId" column="refund_id"/>
        <result property="picPath" column="pic_path"/>
        <result property="sysPicPath" column="sys_pic_path"/>
        <result property="refundStatus" column="refund_status"/>
        <result property="outerIid" column="outer_iid"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="companyId" column="company_id"/>
        <result property="source" column="source"/>
        <result property="tid" column="tid"/>
        <result property="created" column="created"/>
        <result property="payTime" column="pay_time"/>
        <result property="endTime" column="end_time"/>
        <result property="consignTime" column="consign_time"/>
        <result property="sysConsigned" column="sys_consigned" />
        <result property="updTime" column="upd_time"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="sysStatus" column="sys_status"/>
        <result property="insufficientCanceled" column="insufficient_canceled"/>
        <result property="relationChanged" column="relation_changed"/>
        <result property="stockStatus" column="stock_status"/>
        <result property="stockNum" column="stock_num"/>
        <result property="isPresell" column="is_presell"/>
        <result property="subStock" column="sub_stock"/>
        <result property="sysOuterId" column="sys_outer_id"/>
        <result property="sysSkuPropertiesName" column="sys_sku_properties_name"/>
        <result property="sysSkuPropertiesAlias" column="sys_sku_properties_alias"/>
        <result property="sysItemRemark" column="sys_item_remark"/>
        <result property="sysSkuRemark" column="sys_sku_remark"/>
        <result property="sysTitle" column="sys_title"/>
        <result property="shortTitle" column="short_title"/>
        <result property="cids" column="cids"/>
        <result property="combineId" column="combine_id"/>
        <result property="netWeight" column="net_weight"/>
        <result property="volume" column="volume"/>
        <result property="giftNum" column="gift_num"/>
        <result property="type" column="type"/>
        <result property="identCode" column="ident_code"/>
        <result property="unit" column="unit"/>
        <result property="skuUnit" column="sku_unit"/>
        <result property="sysItemChanged" column="sys_item_changed"/>
        <result property="itemChanged" column="item_changed"/>
        <result property="soid" column="soid"/>
        <result property="isVirtual" column="is_virtual"/>
        <result property="nonConsign" column="non_consign"/>
        <result property="isCancel" column="is_cancel"/>
        <result property="discountRate" column="discount_rate"/>
        <result property="customGiftType" column="custom_gift_type"/>
        <result property="isPick" column="is_pick"/>
        <result property="ptConsignTime" column="pt_consign_time"/>
        <result property="belongSid" column="belong_sid" />
        <result property="convertType" column="convert_type" />
        <result property="destId" column="dest_id" />
        <result property="belongType" column="belong_type" />
        <result property="v" column="v"/>
        <result property="forcePackNum" column="force_pack_num"/>
        <result property="divideOrderFee" column="divide_order_fee"/>
        <result property="estimateConTime" column="estimate_con_time"/>
        <result property="platformDiscountFee" column="platform_discount_fee"/>
    </resultMap>
    <resultMap id="orderSimple" class="TbOrder">
        <result property="id" column="id"/>
        <result property="tid" column="tid"/>
        <result property="oid" column="oid"/>
        <result property="sid" column="sid"/>
        <result property="status" column="status"/>
        <result property="numIid" column="num_iid"/>
        <result property="skuId" column="sku_id"/>
        <result property="itemSysId" column="item_sys_id"/>
        <result property="skuSysId" column="sku_sys_id"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="companyId" column="company_id"/>
        <result property="source" column="source"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="sysStatus" column="sys_status"/>
        <result property="stockStatus" column="stock_status"/>
        <result property="stockNum" column="stock_num"/>
        <result property="isPresell" column="is_presell"/>
        <result property="subStock" column="sub_stock"/>
    </resultMap>
    <resultMap id="tbOrder" class="TbOrder">
        <result property="id" column="id"/>
        <result property="itemMealId" column="item_meal_id"/>
        <result property="isOversold" column="is_oversold"/>
        <result property="isServiceOrder" column="is_service_order"/>
        <result property="itemMealName" column="item_meal_name"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="buyerNick" column="buyer_nick"/>
        <result property="timeoutActionTime" column="timeout_action_time"/>
        <result property="buyerRate" column="buyer_rate"/>
        <result property="sellerRate" column="seller_rate"/>
        <result property="sellerType" column="seller_type"/>
        <result property="cid" column="cid"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="companyId" column="company_id"/>
        <result property="source" column="source"/>
        <result property="ydId" column="yd_id"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="identCode" column="ident_code"/>
    </resultMap>

    <resultMap id="orderLock" class="TbOrder">
        <result property="id" column="id"/>
        <result property="tid" column="tid"/>
        <result property="sid" column="sid"/>
    </resultMap>

    <resultMap id="swapStockOrder" class="TbOrder">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="tid" column="tid"/>
        <result property="mergeSid" column="merge_sid"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="itemSysId" column="item_sys_id"/>
        <result property="skuSysId" column="sku_sys_id"/>
        <result property="num" column="num"/>
        <result property="stockNum" column="stock_num"/>
        <result property="insufficientCanceled" column="insufficient_canceled"/>
        <result property="type" column="type"/>
        <result property="payTime" column="pay_time"/>
        <result property="combineId" column="combine_id"/>
    </resultMap>

    <resultMap id="refundOrScalpingOrder" class="TbOrder">
        <result property="itemSysId" column="item_sys_id"/>
        <result property="skuSysId" column="sku_sys_id"/>
        <result property="stockNum" column="stock_num"/>
        <result property="num" column="num"/>
        <result property="scalping" column="scalping"/>
        <result property="refundStatus" column="refund_status"/>
    </resultMap>

    <resultMap id="splitPlatformOrder" class="TbOrder">
        <result property="id" column="id"/>
        <result property="tid" column="tid"/>
        <result property="oid" column="oid"/>
        <result property="sid" column="sid"/>
        <result property="soid" column="soid"/>
        <result property="companyId" column="company_id" />
    </resultMap>

    <select id="TbOrder.queryOrderLock" resultClass="long" parameterClass="hashMap">
        select distinct sid from order_#dbNo# where company_id = #companyId# and enable_status &gt; 0 and id in  <iterate conjunction="," open="(" close=")" property="ids"> #ids[]# </iterate>
    </select>

    <select id="TbOrder.querySuits" resultMap="orderBase" parameterClass="hashMap">
        SELECT * FROM order_#dbNo# WHERE company_id = #companyId# AND sid = #sid# AND combine_id in  <iterate conjunction="," open="(" close=")" property="orderIds"> #orderIds[]# </iterate> AND enable_status = 1
    </select>

    <!-- 根据主键删除 -->
    <update id="TbOrder.deleteByIds" parameterClass="hashMap">
        update order_#dbNo# set enable_status = 0 WHERE company_id = #companyId# and id in
        <iterate open="(" close=")" conjunction="," property="orders">
            #orders[].id#
        </iterate>
    </update>

    <select id="TbOrder.queryRefundOrders" resultClass="TbOrder" parameterClass="hashMap">
        select sid,id,tid,oid,sys_status sysStatus from order_#dbNo# where company_id = #companyId#
        <isNotNull property="fromUpdTime" prepend="and"> upd_time &gt;= #fromUpdTime# </isNotNull>
        <isNotNull property="toUpdTime" prepend="and"> upd_time &lt;= #toUpdTime# </isNotNull>
        <isNotEmpty property="sysStatuss" prepend="and">
            sys_status in <iterate property="sysStatuss" open="(" conjunction="," close=")"> #sysStatuss[]# </iterate>
        </isNotEmpty>
          and (refund_id is not null and refund_id != '') and enable_status &gt; 0
        limit #page.startRow#, #page.pageSize#
    </select>

    <!-- 根据主键更新 -->
    <update id="TbOrder.update" parameterClass="TbOrder">
        UPDATE <encode property="table"/>
            SET company_id = #companyId#
            <isNotEmpty prepend="," property="oid"> oid = #oid# </isNotEmpty>
            <isNotNull prepend="," property="status"> status = #status# </isNotNull>
            <isNotNull prepend="," property="title"> title = substring(#title#,1,128) </isNotNull>
            <isNotNull prepend="," property="cost"> cost = #cost# </isNotNull>
            <isNotNull prepend="," property="salePrice"> sale_price = #salePrice# </isNotNull>
            <isNotNull prepend="," property="saleFee"> sale_fee = #saleFee# </isNotNull>
            <isNotNull prepend="," property="price"> price = #price# </isNotNull>
            <isNotNull prepend="," property="numIid"> num_iid = #numIid# </isNotNull>
            <isNotNull prepend="," property="skuId"> sku_id = #skuId# </isNotNull>
            <isNotNull prepend="," property="num"> num = #num# </isNotNull>
            <isNotNull prepend="," property="outerSkuId"> outer_sku_id = #outerSkuId# </isNotNull>
            <isNotNull prepend="," property="totalFee"> total_fee = #totalFee# </isNotNull>
            <isNotNull prepend="," property="payment"> payment = #payment# </isNotNull>
            <isNotNull prepend="," property="payAmount"> pay_amount = #payAmount# </isNotNull>
            <isNotNull prepend="," property="acPayment"> ac_payment = #acPayment# </isNotNull>
            <isNotNull prepend="," property="discountFee"> discount_fee = #discountFee# </isNotNull>
            <isNotNull prepend="," property="adjustFee"> adjust_fee = #adjustFee# </isNotNull>
            <isNotNull prepend="," property="modified"> modified = #modified# </isNotNull>
            <isNotNull prepend="," property="skuPropertiesName"> sku_properties_name = #skuPropertiesName# </isNotNull>
            <isNotNull prepend="," property="refundId"> refund_id = #refundId# </isNotNull>
            <isNotNull prepend="," property="picPath"> pic_path = #picPath# </isNotNull>
            <isNotNull prepend="," property="sysPicPath"> sys_pic_path = #sysPicPath# </isNotNull>
            <isNotNull prepend="," property="refundStatus"> refund_status = #refundStatus# </isNotNull>
            <isNotNull prepend="," property="outerIid"> outer_iid = #outerIid# </isNotNull>
            <isNotNull prepend="," property="taobaoId"> taobao_id = #taobaoId# </isNotNull>
            <isNotNull prepend="," property="companyId"> company_id = #companyId# </isNotNull>
            <isNotNull prepend="," property="source"> source = #source# </isNotNull>
            <isNotNull prepend="," property="tid"> tid = #tid# </isNotNull>
            <isNotNull prepend="," property="created"> created = #created# </isNotNull>
            <isNotNull prepend="," property="payTime"> pay_time = #payTime# </isNotNull>
            <isNotNull prepend="," property="endTime"> end_time = #endTime# </isNotNull>
            <isNotNull prepend="," property="consignTime"> consign_time = #consignTime# </isNotNull>
            <isNotNull prepend="," property="sysConsigned"> sys_consigned = #sysConsigned# </isNotNull>
            <isNotNull prepend="," property="enableStatus"> enable_status = #enableStatus# </isNotNull>
            <isNotNull prepend="," property="sysStatus"> sys_status = #sysStatus# </isNotNull>
            <isNotNull prepend="," property="insufficientCanceled"> insufficient_canceled = #insufficientCanceled# </isNotNull>
            <isNotNull prepend="," property="relationChanged"> relation_changed = #relationChanged# </isNotNull>
            <isNotNull prepend="," property="stockStatus"> stock_status = #stockStatus# </isNotNull>
            <isNotNull prepend="," property="stockNum"> stock_num = #stockNum# </isNotNull>
            <isNotNull prepend="," property="sid"> sid = #sid# </isNotNull>
            <isNotNull prepend="," property="isPresell"> is_presell = #isPresell# </isNotNull>
            <isNotNull prepend="," property="subStock"> sub_stock = #subStock# </isNotNull>
            <isNotNull prepend="," property="sysOuterId"> sys_outer_id = #sysOuterId# </isNotNull>
            <isNotNull prepend="," property="sysSkuPropertiesName"> sys_sku_properties_name = #sysSkuPropertiesName# </isNotNull>
            <isNotNull prepend="," property="sysSkuPropertiesAlias"> sys_sku_properties_alias = #sysSkuPropertiesAlias# </isNotNull>
            <isNotNull prepend="," property="sysSkuRemark"> sys_sku_remark = #sysSkuRemark# </isNotNull>
            <isNotNull prepend="," property="sysItemRemark"> sys_item_remark = #sysItemRemark# </isNotNull>
            <isNotNull prepend="," property="sysTitle"> sys_title = #sysTitle# </isNotNull>
            <isNotNull prepend="," property="shortTitle"> short_title = #shortTitle# </isNotNull>
            <isNotNull prepend="," property="itemSysId"> item_sys_id = #itemSysId# </isNotNull>
            <isNotNull prepend="," property="skuSysId"> sku_sys_id = #skuSysId# </isNotNull>
            <isNotNull prepend="," property="combineId"> combine_id = #combineId# </isNotNull>
            <isNotNull prepend="," property="giftNum"> gift_num = #giftNum# </isNotNull>
            <isNotNull prepend="," property="type"> type = #type# </isNotNull>
            <isNotNull prepend="," property="netWeight"> net_weight = #netWeight# </isNotNull>
            <isNotNull prepend="," property="volume"> volume = #volume# </isNotNull>
            <isNotNull prepend="," property="identCode"> ident_code = #identCode# </isNotNull>
            <isNotNull prepend="," property="unit"> unit = #unit# </isNotNull>
            <isNotNull prepend="," property="skuUnit"> sku_unit = #skuUnit# </isNotNull>
            <isNotNull prepend="," property="sysItemChanged"> sys_item_changed = #sysItemChanged# </isNotNull>
            <isNotNull prepend="," property="itemChanged"> item_changed = #itemChanged# </isNotNull>
            <isNotNull prepend="," property="soid"> soid = #soid# </isNotNull>
            <isNotNull prepend="," property="isVirtual"> is_virtual = #isVirtual# </isNotNull>
            <isNotNull prepend="," property="nonConsign"> non_consign = #nonConsign# </isNotNull>
            <isNotNull prepend="," property="isCancel"> is_cancel = #isCancel# </isNotNull>
            <isNotNull prepend="," property="cids"> cids = #cids# </isNotNull>
            <isNotNull prepend="," property="discountRate"> discount_rate = #discountRate# </isNotNull>
            <isNotNull prepend="," property="customGiftType"> custom_gift_type = #customGiftType# </isNotNull>
            <isNotNull prepend="," property="isPick"> is_pick = #isPick# </isNotNull>
            <isNotNull prepend="," property="ptConsignTime"> pt_consign_time = #ptConsignTime# </isNotNull>
            <isNotNull prepend="," property="belongSid"> belong_sid = #belongSid# </isNotNull>
            <isNotNull prepend="," property="convertType"> convert_type = #convertType# </isNotNull>
            <isNotNull prepend="," property="destId"> dest_id = #destId# </isNotNull>
            <isNotNull prepend="," property="belongType"> belong_type = #belongType# </isNotNull>
            <isNotNull prepend="," property="v"> v = #v# </isNotNull>
            <isNotNull prepend="," property="forcePackNum"> force_pack_num = #forcePackNum# </isNotNull>
            <isNotNull prepend="," property="divideOrderFee"> divide_order_fee = #divideOrderFee# </isNotNull>
            <isNotNull prepend="," property="estimateConTime"> estimate_con_time = #estimateConTime# </isNotNull>
            <isNotNull prepend="," property="platformDiscountFee"> platform_discount_fee = #platformDiscountFee# </isNotNull>
        WHERE company_id = #companyId# and id = #id# and enable_status &gt; 0
    </update>

    <update id="TbOrder.batchUpdate">
        <iterate property="orders" conjunction=";">
            UPDATE <encode property="table"/>
            SET company_id = #companyId#
            <isNotEmpty prepend="," property="orders[].oid"> oid = #orders[].oid# </isNotEmpty>
            <isNotNull prepend="," property="orders[].status"> status = #orders[].status# </isNotNull>
            <isNotNull prepend="," property="orders[].title"> title = substring(#orders[].title#,1,128) </isNotNull>
            <isNotNull prepend="," property="orders[].cost"> cost = #orders[].cost# </isNotNull>
            <isNotNull prepend="," property="orders[].salePrice"> sale_price = #orders[].salePrice# </isNotNull>
            <isNotNull prepend="," property="orders[].saleFee"> sale_fee = #orders[].saleFee# </isNotNull>
            <isNotNull prepend="," property="orders[].price"> price = #orders[].price# </isNotNull>
            <isNotNull prepend="," property="orders[].numIid"> num_iid = #orders[].numIid# </isNotNull>
            <isNotNull prepend="," property="orders[].skuId"> sku_id = #orders[].skuId# </isNotNull>
            <isNotNull prepend="," property="orders[].num"> num = #orders[].num# </isNotNull>
            <isNotNull prepend="," property="orders[].outerSkuId"> outer_sku_id = #orders[].outerSkuId# </isNotNull>
            <isNotNull prepend="," property="orders[].totalFee"> total_fee = #orders[].totalFee# </isNotNull>
            <isNotNull prepend="," property="orders[].payment"> payment = #orders[].payment# </isNotNull>
            <isNotNull prepend="," property="orders[].payAmount"> pay_amount = #orders[].payAmount# </isNotNull>
            <isNotNull prepend="," property="orders[].acPayment"> ac_payment = #orders[].acPayment# </isNotNull>
            <isNotNull prepend="," property="orders[].discountFee"> discount_fee = #orders[].discountFee# </isNotNull>
            <isNotNull prepend="," property="orders[].adjustFee"> adjust_fee = #orders[].adjustFee# </isNotNull>
            <isNotNull prepend="," property="orders[].modified"> modified = #orders[].modified# </isNotNull>
            <isNotNull prepend="," property="orders[].skuPropertiesName"> sku_properties_name = #orders[].skuPropertiesName# </isNotNull>
            <isNotNull prepend="," property="orders[].refundId"> refund_id = #orders[].refundId# </isNotNull>
            <isNotNull prepend="," property="orders[].picPath"> pic_path = #orders[].picPath# </isNotNull>
            <isNotNull prepend="," property="orders[].sysPicPath"> sys_pic_path = #orders[].sysPicPath# </isNotNull>
            <isNotNull prepend="," property="orders[].refundStatus"> refund_status = #orders[].refundStatus# </isNotNull>
            <isNotNull prepend="," property="orders[].outerIid"> outer_iid = #orders[].outerIid# </isNotNull>
            <isNotNull prepend="," property="orders[].taobaoId"> taobao_id = #orders[].taobaoId# </isNotNull>
            <isNotNull prepend="," property="orders[].companyId"> company_id = #orders[].companyId# </isNotNull>
            <isNotNull prepend="," property="orders[].source"> source = #orders[].source# </isNotNull>
            <isNotNull prepend="," property="orders[].tid"> tid = #orders[].tid# </isNotNull>
            <isNotNull prepend="," property="orders[].created"> created = #orders[].created# </isNotNull>
            <isNotNull prepend="," property="orders[].payTime"> pay_time = #orders[].payTime# </isNotNull>
            <isNotNull prepend="," property="orders[].endTime"> end_time = #orders[].endTime# </isNotNull>
            <isNotNull prepend="," property="orders[].consignTime"> consign_time = #orders[].consignTime# </isNotNull>
            <isNotNull prepend="," property="orders[].sysConsigned"> sys_consigned = #orders[].sysConsigned# </isNotNull>
            <isNotNull prepend="," property="orders[].enableStatus"> enable_status = #orders[].enableStatus# </isNotNull>
            <isNotNull prepend="," property="orders[].sysStatus"> sys_status = #orders[].sysStatus# </isNotNull>
            <isNotNull prepend="," property="orders[].insufficientCanceled"> insufficient_canceled = #orders[].insufficientCanceled# </isNotNull>
            <isNotNull prepend="," property="orders[].relationChanged"> relation_changed = #orders[].relationChanged# </isNotNull>
            <isNotNull prepend="," property="orders[].stockStatus"> stock_status = #orders[].stockStatus# </isNotNull>
            <isNotNull prepend="," property="orders[].stockNum"> stock_num = #orders[].stockNum# </isNotNull>
            <isNotNull prepend="," property="orders[].sid"> sid = #orders[].sid# </isNotNull>
            <isNotNull prepend="," property="orders[].isPresell"> is_presell = #orders[].isPresell# </isNotNull>
            <isNotNull prepend="," property="orders[].subStock"> sub_stock = #orders[].subStock# </isNotNull>
            <isNotNull prepend="," property="orders[].sysOuterId"> sys_outer_id = #orders[].sysOuterId# </isNotNull>
            <isNotNull prepend="," property="orders[].sysSkuPropertiesName"> sys_sku_properties_name = #orders[].sysSkuPropertiesName# </isNotNull>
            <isNotNull prepend="," property="orders[].sysSkuPropertiesAlias"> sys_sku_properties_alias = #orders[].sysSkuPropertiesAlias# </isNotNull>
            <isNotNull prepend="," property="orders[].sysSkuRemark"> sys_sku_remark = #orders[].sysSkuRemark# </isNotNull>
            <isNotNull prepend="," property="orders[].sysItemRemark"> sys_item_remark = #orders[].sysItemRemark# </isNotNull>
            <isNotNull prepend="," property="orders[].sysTitle"> sys_title = #orders[].sysTitle# </isNotNull>
            <isNotNull prepend="," property="orders[].shortTitle"> short_title = #orders[].shortTitle# </isNotNull>
            <isNotNull prepend="," property="orders[].itemSysId"> item_sys_id = #orders[].itemSysId# </isNotNull>
            <isNotNull prepend="," property="orders[].skuSysId"> sku_sys_id = #orders[].skuSysId# </isNotNull>
            <isNotNull prepend="," property="orders[].combineId"> combine_id = #orders[].combineId# </isNotNull>
            <isNotNull prepend="," property="orders[].giftNum"> gift_num = #orders[].giftNum# </isNotNull>
            <isNotNull prepend="," property="orders[].type"> type = #orders[].type# </isNotNull>
            <isNotNull prepend="," property="orders[].netWeight"> net_weight = #orders[].netWeight# </isNotNull>
            <isNotNull prepend="," property="orders[].volume"> volume = #orders[].volume# </isNotNull>
            <isNotNull prepend="," property="orders[].identCode"> ident_code = #orders[].identCode# </isNotNull>
            <isNotNull prepend="," property="orders[].unit"> unit = #orders[].unit# </isNotNull>
            <isNotNull prepend="," property="orders[].skuUnit"> sku_unit = #orders[].skuUnit# </isNotNull>
            <isNotNull prepend="," property="orders[].sysItemChanged"> sys_item_changed = #orders[].sysItemChanged# </isNotNull>
            <isNotNull prepend="," property="orders[].itemChanged"> item_changed = #orders[].itemChanged# </isNotNull>
            <isNotNull prepend="," property="orders[].soid"> soid = #orders[].soid# </isNotNull>
            <isNotNull prepend="," property="orders[].isVirtual"> is_virtual = #orders[].isVirtual# </isNotNull>
            <isNotNull prepend="," property="orders[].nonConsign"> non_consign = #orders[].nonConsign# </isNotNull>
            <isNotNull prepend="," property="orders[].isCancel"> is_cancel = #orders[].isCancel# </isNotNull>
            <isNotNull prepend="," property="orders[].cids"> cids = #orders[].cids# </isNotNull>
            <isNotNull prepend="," property="orders[].discountRate"> discount_rate = #orders[].discountRate# </isNotNull>
            <isNotNull prepend="," property="orders[].customGiftType"> custom_gift_type = #orders[].customGiftType# </isNotNull>
            <isNotNull prepend="," property="orders[].isPick"> is_pick = #orders[].isPick# </isNotNull>
            <isNotNull prepend="," property="orders[].ptConsignTime"> pt_consign_time = #orders[].ptConsignTime# </isNotNull>
            <isNotNull prepend="," property="orders[].belongSid"> belong_sid = #orders[].belongSid# </isNotNull>
            <isNotNull prepend="," property="orders[].convertType"> convert_type = #orders[].convertType# </isNotNull>
            <isNotNull prepend="," property="orders[].destId"> dest_id = #orders[].destId# </isNotNull>
            <isNotNull prepend="," property="orders[].belongType"> belong_type = #orders[].belongType# </isNotNull>
            <isNotNull prepend="," property="orders[].v"> v = #orders[].v# </isNotNull>
            <isNotNull prepend="," property="orders[].forcePackNum"> force_pack_num = #orders[].forcePackNum# </isNotNull>
            <isNotNull prepend="," property="orders[].divideOrderFee"> divide_order_fee = #orders[].divideOrderFee# </isNotNull>
            <isNotNull prepend="," property="orders[].estimateConTime"> estimate_con_time = #orders[].estimateConTime# </isNotNull>
            <isNotNull prepend="," property="orders[].platformDiscountFee"> platform_discount_fee = #orders[].platformDiscountFee# </isNotNull>
            WHERE company_id = #companyId# and id = #orders[].id# and enable_status &gt; 0
        </iterate>
    </update>

    <insert id="TbOrder.insert" parameterClass="TbOrder">
        INSERT INTO <encode property="table"/>
        <dynamic prepend="(" close=")">
            <isNotEmpty prepend="," property="id"> id </isNotEmpty>
            <isNotEmpty prepend="," property="oid"> oid </isNotEmpty>
            <isNotEmpty prepend="," property="status"> status </isNotEmpty>
            <isNotEmpty prepend="," property="title"> title </isNotEmpty>
            <isNotEmpty prepend="," property="cost"> cost </isNotEmpty>
            <isNotEmpty prepend="," property="salePrice"> sale_price </isNotEmpty>
            <isNotEmpty prepend="," property="saleFee"> sale_fee </isNotEmpty>
            <isNotEmpty prepend="," property="price"> price </isNotEmpty>
            <isNotEmpty prepend="," property="numIid"> num_iid </isNotEmpty>
            <isNotEmpty prepend="," property="skuId"> sku_id </isNotEmpty>
            <isNotEmpty prepend="," property="itemSysId"> item_sys_id </isNotEmpty>
            <isNotEmpty prepend="," property="skuSysId"> sku_sys_id </isNotEmpty>
            <isNotEmpty prepend="," property="num"> num </isNotEmpty>
            <isNotEmpty prepend="," property="outerSkuId"> outer_sku_id </isNotEmpty>
            <isNotEmpty prepend="," property="totalFee"> total_fee </isNotEmpty>
            <isNotEmpty prepend="," property="payment"> payment </isNotEmpty>
            <isNotEmpty prepend="," property="payAmount"> pay_amount </isNotEmpty>
            <isNotEmpty prepend="," property="acPayment"> ac_payment </isNotEmpty>
            <isNotEmpty prepend="," property="discountFee"> discount_fee </isNotEmpty>
            <isNotEmpty prepend="," property="adjustFee"> adjust_fee </isNotEmpty>
            <isNotEmpty prepend="," property="modified"> modified </isNotEmpty>
            <isNotEmpty prepend="," property="skuPropertiesName"> sku_properties_name </isNotEmpty>
            <isNotEmpty prepend="," property="refundId"> refund_id </isNotEmpty>
            <isNotEmpty prepend="," property="picPath"> pic_path </isNotEmpty>
            <isNotEmpty prepend="," property="sysPicPath"> sys_pic_path </isNotEmpty>
            <isNotEmpty prepend="," property="refundStatus"> refund_status </isNotEmpty>
            <isNotEmpty prepend="," property="outerIid"> outer_iid </isNotEmpty>
            <isNotEmpty prepend="," property="taobaoId"> taobao_id </isNotEmpty>
            <isNotEmpty prepend="," property="companyId"> company_id </isNotEmpty>
            <isNotEmpty prepend="," property="source"> source</isNotEmpty>
            <isNotEmpty prepend="," property="tid"> tid </isNotEmpty>
            <isNotEmpty prepend="," property="created"> created </isNotEmpty>
            <isNotEmpty prepend="," property="payTime"> pay_time </isNotEmpty>
            <isNotEmpty prepend="," property="endTime"> end_time </isNotEmpty>
            <isNotEmpty prepend="," property="consignTime"> consign_time </isNotEmpty>
            <isNotEmpty prepend="," property="enableStatus"> enable_status </isNotEmpty>
            <isNotEmpty prepend="," property="sysStatus"> sys_status </isNotEmpty>
            <isNotNull prepend="," property="insufficientCanceled"> insufficient_canceled </isNotNull>
            <isNotNull prepend="," property="relationChanged"> relation_changed </isNotNull>
            <isNotEmpty prepend="," property="stockStatus"> stock_status </isNotEmpty>
            <isNotEmpty prepend="," property="stockNum"> stock_num </isNotEmpty>
            <isNotEmpty prepend="," property="sid"> sid </isNotEmpty>
            <isNotEmpty prepend="," property="isPresell"> is_presell </isNotEmpty>
            <isNotEmpty prepend="," property="subStock"> sub_stock </isNotEmpty>
            <isNotNull prepend="," property="sysOuterId"> sys_outer_id </isNotNull>
            <isNotNull prepend="," property="sysSkuPropertiesName"> sys_sku_properties_name </isNotNull>
            <isNotNull prepend="," property="sysSkuPropertiesAlias"> sys_sku_properties_alias </isNotNull>
            <isNotNull prepend="," property="sysSkuRemark"> sys_sku_remark </isNotNull>
            <isNotNull prepend="," property="sysItemRemark"> sys_item_remark </isNotNull>
            <isNotNull prepend="," property="sysTitle"> sys_title </isNotNull>
            <isNotNull prepend="," property="shortTitle"> short_title </isNotNull>
            <isNotEmpty prepend="," property="combineId"> combine_id </isNotEmpty>
            <isNotEmpty prepend="," property="giftNum"> gift_num </isNotEmpty>
            <isNotEmpty prepend="," property="type"> type </isNotEmpty>
            <isNotEmpty prepend="," property="netWeight"> net_weight </isNotEmpty>
            <isNotNull prepend="," property="volume"> volume </isNotNull>
            <isNotEmpty prepend="," property="identCode"> ident_code </isNotEmpty>
            <isNotNull prepend="," property="unit"> unit </isNotNull>
            <isNotNull prepend="," property="skuUnit"> sku_unit </isNotNull>
            <isNotNull prepend="," property="soid"> soid </isNotNull>
            <isNotNull prepend="," property="isVirtual"> is_virtual </isNotNull>
            <isNotNull prepend="," property="nonConsign"> non_consign </isNotNull>
            <isNotNull prepend="," property="cids"> cids </isNotNull>
            <isNotNull prepend="," property="discountRate"> discount_rate </isNotNull>
            <isNotNull prepend="," property="customGiftType"> custom_gift_type </isNotNull>
            <isNotNull prepend="," property="isPick"> is_pick </isNotNull>
            <isNotNull prepend="," property="ptConsignTime"> pt_consign_time </isNotNull>
            <isNotNull prepend="," property="belongSid"> belong_sid </isNotNull>
            <isNotNull prepend="," property="sysItemChanged"> sys_item_changed </isNotNull>
            <isNotNull prepend="," property="convertType"> convert_type </isNotNull>
            <isNotNull prepend="," property="destId"> dest_id </isNotNull>
            <isNotNull prepend="," property="belongType"> belong_type </isNotNull>

            <isNotNull prepend="," property="v"> v </isNotNull>
            <isNotNull prepend="," property="forcePackNum"> force_pack_num </isNotNull>
            <isNotNull prepend="," property="divideOrderFee"> divide_order_fee </isNotNull>
            <isNotNull prepend="," property="estimateConTime"> estimate_con_time </isNotNull>
            <isNotNull prepend="," property="sysConsigned"> sys_consigned </isNotNull>
            <isNotNull prepend="," property="isCancel"> is_cancel </isNotNull>
            <isNotNull prepend="," property="itemChanged"> item_changed </isNotNull>
            <isNotNull prepend="," property="platformDiscountFee"> platform_discount_fee </isNotNull>
        </dynamic>
        VALUES
        <dynamic prepend="(" close=")">
            <isNotEmpty prepend="," property="id"> #id# </isNotEmpty>
            <isNotEmpty prepend="," property="oid"> #oid# </isNotEmpty>
            <isNotEmpty prepend="," property="status"> #status# </isNotEmpty>
            <isNotEmpty prepend="," property="title"> substring(#title#,1,128) </isNotEmpty>
            <isNotEmpty prepend="," property="cost"> #cost# </isNotEmpty>
            <isNotEmpty prepend="," property="salePrice"> #salePrice# </isNotEmpty>
            <isNotEmpty prepend="," property="saleFee"> #saleFee# </isNotEmpty>
            <isNotEmpty prepend="," property="price"> #price# </isNotEmpty>
            <isNotEmpty prepend="," property="numIid"> #numIid# </isNotEmpty>
            <isNotEmpty prepend="," property="skuId"> #skuId# </isNotEmpty>
            <isNotEmpty prepend="," property="itemSysId"> #itemSysId# </isNotEmpty>
            <isNotEmpty prepend="," property="skuSysId"> #skuSysId# </isNotEmpty>
            <isNotEmpty prepend="," property="num"> #num# </isNotEmpty>
            <isNotEmpty prepend="," property="outerSkuId"> #outerSkuId# </isNotEmpty>
            <isNotEmpty prepend="," property="totalFee"> #totalFee# </isNotEmpty>
            <isNotEmpty prepend="," property="payment"> #payment# </isNotEmpty>
            <isNotEmpty prepend="," property="payAmount"> #payAmount# </isNotEmpty>
            <isNotEmpty prepend="," property="acPayment"> #acPayment# </isNotEmpty>
            <isNotEmpty prepend="," property="discountFee"> #discountFee# </isNotEmpty>
            <isNotEmpty prepend="," property="adjustFee"> #adjustFee# </isNotEmpty>
            <isNotEmpty prepend="," property="modified"> #modified# </isNotEmpty>
            <isNotEmpty prepend="," property="skuPropertiesName"> #skuPropertiesName# </isNotEmpty>
            <isNotEmpty prepend="," property="refundId"> #refundId# </isNotEmpty>
            <isNotEmpty prepend="," property="picPath"> #picPath# </isNotEmpty>
            <isNotEmpty prepend="," property="sysPicPath"> #sysPicPath# </isNotEmpty>
            <isNotEmpty prepend="," property="refundStatus"> #refundStatus# </isNotEmpty>
            <isNotEmpty prepend="," property="outerIid"> #outerIid# </isNotEmpty>
            <isNotEmpty prepend="," property="taobaoId"> #taobaoId# </isNotEmpty>
            <isNotEmpty prepend="," property="companyId"> #companyId# </isNotEmpty>
            <isNotEmpty prepend="," property="source"> #source# </isNotEmpty>
            <isNotEmpty prepend="," property="tid"> #tid# </isNotEmpty>
            <isNotEmpty prepend="," property="created"> #created# </isNotEmpty>
            <isNotEmpty prepend="," property="payTime"> #payTime# </isNotEmpty>
            <isNotEmpty prepend="," property="endTime"> #endTime# </isNotEmpty>
            <isNotEmpty prepend="," property="consignTime"> #consignTime# </isNotEmpty>
            <isNotEmpty prepend="," property="enableStatus"> #enableStatus# </isNotEmpty>
            <isNotEmpty prepend="," property="sysStatus"> #sysStatus# </isNotEmpty>
            <isNotNull prepend="," property="insufficientCanceled"> #insufficientCanceled# </isNotNull>
            <isNotNull prepend="," property="relationChanged"> #relationChanged# </isNotNull>
            <isNotEmpty prepend="," property="stockStatus"> #stockStatus# </isNotEmpty>
            <isNotEmpty prepend="," property="stockNum"> #stockNum# </isNotEmpty>
            <isNotEmpty prepend="," property="sid"> #sid# </isNotEmpty>
            <isNotEmpty prepend="," property="isPresell"> #isPresell# </isNotEmpty>
            <isNotEmpty prepend="," property="subStock"> #subStock# </isNotEmpty>
            <isNotNull prepend="," property="sysOuterId"> #sysOuterId# </isNotNull>
            <isNotNull prepend="," property="sysSkuPropertiesName"> #sysSkuPropertiesName# </isNotNull>
            <isNotNull prepend="," property="sysSkuPropertiesAlias"> #sysSkuPropertiesAlias# </isNotNull>
            <isNotNull prepend="," property="sysSkuRemark"> #sysSkuRemark# </isNotNull>
            <isNotNull prepend="," property="sysItemRemark"> #sysItemRemark# </isNotNull>
            <isNotNull prepend="," property="sysTitle"> #sysTitle# </isNotNull>
            <isNotNull prepend="," property="shortTitle"> #shortTitle# </isNotNull>
            <isNotEmpty prepend="," property="combineId"> #combineId# </isNotEmpty>
            <isNotEmpty prepend="," property="giftNum"> #giftNum# </isNotEmpty>
            <isNotEmpty prepend="," property="type"> #type# </isNotEmpty>
            <isNotEmpty prepend="," property="netWeight"> #netWeight# </isNotEmpty>
            <isNotNull prepend="," property="volume"> #volume# </isNotNull>
            <isNotEmpty prepend="," property="identCode"> #identCode# </isNotEmpty>
            <isNotNull prepend="," property="unit"> #unit# </isNotNull>
            <isNotNull prepend="," property="skuUnit"> #skuUnit# </isNotNull>
            <isNotNull prepend="," property="soid"> #soid# </isNotNull>
            <isNotNull prepend="," property="isVirtual"> #isVirtual# </isNotNull>
            <isNotNull prepend="," property="nonConsign"> #nonConsign# </isNotNull>
            <isNotNull prepend="," property="cids"> #cids# </isNotNull>
            <isNotNull prepend="," property="discountRate"> #discountRate# </isNotNull>
            <isNotNull prepend="," property="customGiftType"> #customGiftType# </isNotNull>
            <isNotNull prepend="," property="isPick"> #isPick# </isNotNull>
            <isNotNull prepend="," property="ptConsignTime"> #ptConsignTime# </isNotNull>
            <isNotNull prepend="," property="belongSid"> #belongSid# </isNotNull>
            <isNotNull prepend="," property="sysItemChanged"> #sysItemChanged# </isNotNull>
            <isNotNull prepend="," property="convertType"> #convertType# </isNotNull>
            <isNotNull prepend="," property="destId"> #destId# </isNotNull>
            <isNotNull prepend="," property="belongType"> #belongType# </isNotNull>
            <isNotNull prepend="," property="v"> #v# </isNotNull>
            <isNotNull prepend="," property="forcePackNum"> #forcePackNum# </isNotNull>
            <isNotNull prepend="," property="divideOrderFee"> #divideOrderFee# </isNotNull>
            <isNotNull prepend="," property="estimateConTime"> #estimateConTime# </isNotNull>
            <isNotNull prepend="," property="sysConsigned"> #sysConsigned# </isNotNull>
            <isNotNull prepend="," property="isCancel"> #isCancel# </isNotNull>
            <isNotNull prepend="," property="itemChanged"> #itemChanged# </isNotNull>
            <isNotNull prepend="," property="platformDiscountFee"> #platformDiscountFee# </isNotNull>
        </dynamic>
        ON DUPLICATE KEY UPDATE
        modified = NOW()
    </insert>

    <select id="TbOrder.listByItemSku" parameterClass="hashMap" resultMap="orderBase">
        SELECT * FROM order_#dbNo# WHERE enable_status &gt; 0 AND company_id = #companyId#
        <isEqual property="isFx" compareValue="0">
            AND belong_type != 1
        </isEqual>
        AND item_sys_id IN
        <iterate property="sysItemIds" open="(" conjunction="," close=")">#sysItemIds[]#</iterate>
        <isNotEmpty property="sysSkuIds">
            AND sku_sys_id IN<iterate property="sysSkuIds" open="(" conjunction="," close=")">#sysSkuIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="sysStatusList">
            AND sys_status IN<iterate property="sysStatusList" open="(" conjunction="," close=")">#sysStatusList[]#</iterate>
        </isNotEmpty>
        <isNotNull property="page"> LIMIT #page.startRow#, #page.pageSize# </isNotNull>
    </select>

    <select id="TbOrder.listAuditedByItemSku" parameterClass="hashMap" resultClass="hashMap">
        SELECT distinct item_sys_id,sku_sys_id FROM order_#dbNo# o
        WHERE enable_status &gt; 0 AND company_id = #companyId# AND sys_status='FINISHED_AUDIT' AND stock_status='NORMAL'
        AND item_sys_id IN
        <iterate property="sysItemIds" open="(" conjunction="," close=")">#sysItemIds[]#</iterate>
        <isNotEmpty property="sysSkuIds">
            AND sku_sys_id IN<iterate property="sysSkuIds" open="(" conjunction="," close=")">#sysSkuIds[]#</iterate>
        </isNotEmpty>
        AND exists(select 1 from trade_$tradeDbNo$ t where t.enable_status &gt; 0 AND t.company_id=#companyId# AND t.is_cancel=0 and t.is_excep=0 and t.warehouse_id=#warehouseId#
        AND t.sid=o.sid)
    </select>

    <select id="TbOrder.listStockSwapableByItemSku" parameterClass="hashMap" resultClass="TbOrder">
        SELECT o.id,IF(t.merge_sid > 0, t.merge_sid, t.sid) sid,o.item_sys_id itemSysId,o.sku_sys_id skuSysId,o.num,o.stock_num stockNum,o.sys_status sysStatus,o.stock_status stockStatus,o.created FROM order_not_consign_#dbNo# o
        join trade_not_consign_$tradeDbNo$ t on t.enable_status &gt; 0 AND t.company_id=#companyId# AND t.is_cancel=0  and t.warehouse_id = #warehouseId# AND t.sid=o.sid
        WHERE o.enable_status &gt; 0 AND o.company_id = #companyId# AND o.sys_status in ('WAIT_AUDIT' ,'WAIT_BUYER_PAY') and o.is_cancel = 0  and o.convert_type in (0, 3)
        <isEqual property="openScaleNotApplyStock"  compareValue="1">
            and t.scalping = 0
        </isEqual>
        <isEqual property="exceptionNotLockStock" compareValue="1">
            and t.unattainable = 0
        </isEqual>
        and t.sid = o.sid
        and o.stock_num > 0
        AND o.item_sys_id IN
        <iterate property="sysItemIds" open="(" conjunction="," close=")">#sysItemIds[]#</iterate>
        <isNotEmpty property="sysSkuIds">
            AND o.sku_sys_id IN<iterate property="sysSkuIds" open="(" conjunction="," close=")">#sysSkuIds[]#</iterate>
        </isNotEmpty>
        order by o.stock_status ,o.created desc
        <isNotNull property="page"> LIMIT #page.startRow#, #page.pageSize# </isNotNull>
    </select>

    <!-- 库存调剂（根据商家编码调剂） -->
    <sql id="querySwapStockOrderCommon">
        where company_id = #companyId#
            and sys_status in ('WAIT_AUDIT','WAIT_BUYER_PAY')
            and enable_status = 1
    </sql>

    <select id="TbOrder.querySwapStockOrderByOuterIds" resultMap="swapStockOrder" parameterClass="hashMap">
        select a.id,a.sid,a.tid,b.merge_sid,b.warehouse_id,item_sys_id,sku_sys_id,a.num,a.stock_num,a.insufficient_canceled,a.type,b.pay_time,a.combine_id
        from order_not_consign_#dbNo# a
        inner join trade_not_consign_#tradeDbNo# b on a.company_id=b.company_id and a.sid=b.sid and b.enable_status &gt;=1
        where a.company_id = #companyId#
            and b.company_id = #companyId#
            and b.is_cancel = 0
            and a.convert_type  = 0
            and a.sys_status in ('WAIT_AUDIT','WAIT_BUYER_PAY')
            and a.enable_status = 1
            and a.is_virtual = 0
            and a.non_consign = 0
            <isNotEmpty property="sysOuterIds" prepend="and">
                a.sys_outer_id in <iterate property="sysOuterIds" open="(" conjunction="," close=")">#sysOuterIds[]#</iterate>
            </isNotEmpty>
        order by
        <!-- 按商品缺货数量优先 -->
        <isEqual property="orderField" compareValue="num"> b.is_upload DESC, (a.num-a.stock_num=0),(a.num-a.stock_num),</isEqual>
        <!-- 按单品单件优先 -->
        <isEqual property="orderField" compareValue="kind"> b.is_upload DESC, (a.num-a.stock_num=0),(a.num-a.stock_num),b.item_kind_num,</isEqual>
        <!-- 按加急优先 -->
        <isEqual property="orderField" compareValue="urgent"> b.is_upload DESC, b.is_urgent DESC, </isEqual>
        <!-- 按订单付款时间优先 -->
         b.is_upload DESC, b.pay_time='2000-01-01 00:00:00',b.pay_time
    </select>

    <select id="TbOrder.querySwapStockSuitByCombineIds" resultMap="swapStockOrder" parameterClass="hashMap">
        select id,sid,tid,0 merge_sid,0 warehouse_id,item_sys_id,sku_sys_id,item_sys_id,sku_sys_id,num,stock_num,insufficient_canceled,type,pay_time,combine_id from order_not_consign_#dbNo#
        where company_id = #companyId#
            and enable_status = 1
            and is_virtual = 0
            and non_consign = 0
            and sys_status in ('WAIT_AUDIT','WAIT_BUYER_PAY')
            and combine_id in <iterate property="combineIds" open="(" conjunction="," close=")">#combineIds[]#</iterate>
    </select>

    <select id="queryNotFinishedRefundOrScalpingOrder" resultMap="refundOrScalpingOrder" timeout="30">
        SELECT t.scalping, o.stock_num, o.num, o.item_sys_id, o.sku_sys_id, o.refund_status
        FROM trade_#tradeDbNo# t
            JOIN order_#dbNo# o ON t.sid = o.sid and t.company_id = o.company_id
            JOIN stock_order_record_#stockOrderRecordDbNo# s on t.company_id = s.company_id and o.id = s.order_id and s.enable_status = 1
        WHERE t.company_id = #companyId#
            AND t.enable_status > 0
            AND t.is_cancel = 0
            AND t.sys_status IN ('WAIT_BUYER_PAY', 'WAIT_AUDIT', 'WAIT_FINANCE_AUDIT', 'FINISHED_AUDIT')
            AND (t.scalping = 1 OR t.is_refund = 1)
            <isNotEmpty property="warehouseIds">
                and t.warehouse_id in
                <iterate property="warehouseIds" open="(" conjunction="," close=")"> #warehouseIds[]# </iterate>
            </isNotEmpty>
            AND o.enable_status = 1
            AND o.sys_status IN ('WAIT_BUYER_PAY', 'WAIT_AUDIT', 'WAIT_FINANCE_AUDIT', 'FINISHED_AUDIT')
            AND t.stock_status != 'EMPTY'
    </select>

    <select id="TbOrder.isPlatformOrder" resultClass="long" parameterClass="hashMap">
        SELECT count(*) FROM order_#dbNo# WHERE company_id = #companyId# AND oid = #oid# AND tid=#tid# AND  source != 'sys'
    </select>

    <select id="TbOrder.queryPlatformOrderInfo" resultMap="splitPlatformOrder" parameterClass="hashMap">
        SELECT id,oid,sid,tid,soid,company_id FROM order_#dbNo# WHERE company_id = #companyId# AND oid = #oid# AND tid=#tid# AND soid != '1'
    </select>


    <select id="TbOrder.hasPlatformOrderInfo" resultClass="long" parameterClass="hashMap">
        SELECT count(*) FROM order_#dbNo# WHERE company_id = #companyId# AND oid = #oid# AND tid=#tid# AND  source IN <iterate property="sources" open="(" conjunction="," close=")"> #sources[]# </iterate>
    </select>

    <select id="TbOrder.queryPlatOrder" resultMap="orderBase" parameterClass="hashMap">
        /*FORCE_MASTER*/ select * from order_#dbNo# where company_id = #companyId# and enable_status=1 and combine_id=0 and source!='sys'
        and tid in <iterate property="tids" open="(" conjunction="," close=")">#tids[]#</iterate>
        and oid in <iterate property="oids" open="(" conjunction="," close=")">#oids[]#</iterate>
    </select>

    <select id="TbOrder.findByOid" resultMap="orderBase" parameterClass="hashMap">
        select * from order_#dbNo# where company_id = #companyId# and enable_status=1
        AND tid=#tid#
        and oid in <iterate property="oids" open="(" conjunction="," close=")">#oids[]#</iterate>
    </select>

    <select id="TbOrder.queryByTidAndOid" resultMap="orderBase" parameterClass="hashMap">
        select * from order_#dbNo# where company_id = #companyId# and enable_status=1
        and tid in <iterate property="tids" open="(" conjunction="," close=")">#tids[]#</iterate>
        and oid in <iterate property="oids" open="(" conjunction="," close=")">#oids[]#</iterate>
    </select>

    <select id="TbOrder.getByNeedRefreshIsPick" resultClass="TbOrder" parameterClass="hashMap">
        select id, company_id, sid, item_sys_id, sku_sys_id, sys_status, custom_gift_type, gift_num, is_pick from order_#dbNo#
        where company_id = #companyId#
        and enable_status=1
        and sys_status in ('WAIT_BUYER_PAY', 'WAIT_AUDIT', 'WAIT_FINANCE_AUDIT', 'WAIT_FINANCE_AUDIT', 'SYS_STATUS_WAIT_MANUAL_AUDIT', 'SYS_STATUS_WAIT_MANUAL_AUDIT', 'FINISHED_AUDIT' )
        and (gift_num is null or gift_num &lt;= 0)
        and is_pick != 2
        and pay_time &lt;= #endTime#
        limit 0, 200
    </select>

    <select id="TbOrder.listByPlatItemSku" parameterClass="hashMap" resultMap="orderBase">
        SELECT * FROM order_not_consign_#dbNo# WHERE company_id = #companyId# AND enable_status = 1 and is_cancel=0 and source != 'sys'
        AND sys_status IN <iterate property="sysStatusList" open="(" conjunction="," close=")">#sysStatusList[]#</iterate>

        <isEqual property="excludeMatching" compareValue="true">
            AND (item_sys_id is null OR item_sys_id &lt;= 0)
        </isEqual>

        <isNotEmpty property="numIIds"> AND num_iid IN <iterate property="numIIds" open="(" conjunction="," close=")">#numIIds[]#</iterate>  </isNotEmpty>
        <isNotEmpty property="skuIds"> AND sku_id IN <iterate property="skuIds" open="(" conjunction="," close=")">#skuIds[]#</iterate> </isNotEmpty>
    </select>

</sqlMap>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="GiftPromotionRuleGiveItemRange">
    <!-- Alias Map Defined -->
    <typeAlias alias="GiftPromotionRuleGiveItemRange"
               type="com.raycloud.dmj.domain.gift.GiftPromotionRuleGiveItemRange"/>
    <resultMap id="GiftPromotionRuleGiveItemRangeResultMap" class="GiftPromotionRuleGiveItemRange">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="giftPromotionId" column="gift_promotion_id"/>
        <result property="giftPromotionRuleId" column="gift_promotion_rule_id"/>
        <result property="itemId" column="item_id"/>
        <result property="skuIds" column="sku_ids"/>
        <result property="minNum" column="min_num"/>
        <result property="maxNum" column="max_num"/>
        <result property="created" column="created"/>
        <result property="modified" column="modified"/>
        <result property="enableStatus" column="enable_status"/>
    </resultMap>

    <!-- 插入 -->
    <insert id="insert" parameterClass="GiftPromotionRuleGiveItemRange">
        insert into gift_promotion_rule_give_item_range_#dbNo#
        <dynamic prepend="(" close=")">
            <isNotNull prepend="," property="companyId">company_id</isNotNull>
            <isNotNull prepend="," property="giftPromotionId">gift_promotion_id</isNotNull>
            <isNotNull prepend="," property="giftPromotionRuleId">gift_promotion_rule_id</isNotNull>
            <isNotNull prepend="," property="itemId">item_id</isNotNull>
            <isNotNull prepend="," property="skuIds">sku_ids</isNotNull>
            <isNotNull prepend="," property="minNum">min_num</isNotNull>
            <isNotNull prepend="," property="maxNum">max_num</isNotNull>
            ,created
        </dynamic>
        VALUES
        <dynamic prepend="(" close=")">
            <isNotNull prepend="," property="companyId">#companyId#</isNotNull>
            <isNotNull prepend="," property="giftPromotionId">#giftPromotionId#</isNotNull>
            <isNotNull prepend="," property="giftPromotionRuleId">#giftPromotionRuleId#</isNotNull>
            <isNotNull prepend="," property="itemId">#itemId#</isNotNull>
            <isNotNull prepend="," property="skuIds">#skuIds#</isNotNull>
            <isNotNull prepend="," property="minNum">#minNum#</isNotNull>
            <isNotNull prepend="," property="maxNum">#maxNum#</isNotNull>
            ,now()
        </dynamic>
        <selectKey resultClass="long" keyProperty="id">select last_insert_id() as id</selectKey>
    </insert>

    <!-- 根据主键查询 -->
    <select id="queryById" resultMap="GiftPromotionRuleGiveItemRangeResultMap" parameterClass="hashMap">
        select * from gift_promotion_rule_give_item_range_#dbNo# where id=#id# and enable_status = 1
    </select>

    <select id="queryByIds" resultMap="GiftPromotionRuleGiveItemRangeResultMap" parameterClass="hashMap">
        select * from gift_promotion_rule_give_item_range_#dbNo# where id in <iterate property="ids" open="(" close=")"
                                                                                      conjunction=",">#ids[]#
    </iterate> and enable_status = 1
    </select>

    <select id="queryByGiftPromotionRuleIds" resultMap="GiftPromotionRuleGiveItemRangeResultMap"
            parameterClass="hashMap">
        select * from gift_promotion_rule_give_item_range_#dbNo# where gift_promotion_rule_id in <iterate
            property="giftPromotionRuleIds" open="(" close=")" conjunction=",">#giftPromotionRuleIds[]#
    </iterate> and enable_status = 1
    </select>

    <select id="queryByGiftPromotionRuleId" resultMap="GiftPromotionRuleGiveItemRangeResultMap"
            parameterClass="hashMap">
        select * from gift_promotion_rule_give_item_range_#dbNo# where gift_promotion_rule_id =
        #giftPromotionRuleId# and enable_status = 1
    </select>

    <update id="deleteByPromotionId" parameterClass="hashMap">
        update gift_promotion_rule_give_item_range_#dbNo# set enable_status = 0 where company_id = #companyId# and
        gift_promotion_id = #giftPromotionId#
    </update>

    <update id="deleteByPromotionRuleId" parameterClass="hashMap">
        update gift_promotion_rule_give_item_range_#dbNo# set enable_status = 0 where company_id = #companyId# and
        gift_promotion_rule_id = #giftPromotionRuleId#
    </update>

    <!-- 分页查询 -->
    <select id="getGiftPromotionRuleGiveItemRangePage" resultMap="GiftPromotionRuleGiveItemRangeResultMap"
            parameterClass="hashMap">
        select * from gift_promotion_rule_give_item_range_#dbNo# where enable_status = 1
        <isNotEmpty property="id" prepend="and">id = #id#</isNotEmpty>
        <isNotEmpty property="companyId" prepend="and">company_id = #companyId#</isNotEmpty>
        <isNotEmpty property="giftPromotionId" prepend="and">gift_promotion_id = #giftPromotionId#</isNotEmpty>
        <isNotEmpty property="giftPromotionRuleId" prepend="and">gift_promotion_rule_id = #giftPromotionRuleId#
        </isNotEmpty>
        <isNotEmpty property="itemId" prepend="and">item_id = #itemId#</isNotEmpty>
        <isNotEmpty property="skuIds" prepend="and">sku_ids = #skuIds#</isNotEmpty>
        <isNotEmpty property="minNum" prepend="and">min_num = #minNum#</isNotEmpty>
        <isNotEmpty property="maxNum" prepend="and">max_num = #maxNum#</isNotEmpty>
        order by created
        <isNotEmpty property="startRow">limit #startRow#, #pageSize#</isNotEmpty>
    </select>

    <select id="getItemSkuInActiveCount" parameterClass="hashMap" resultClass="long">
        select count(*) from gift_promotion_rule_give_item_range_#dbNo#
        where company_id = #companyId# and enable_status = 1
        <isNotEmpty property="itemId" prepend="and">item_id = #itemId#</isNotEmpty>
        <isNotEmpty property="skuIds" prepend="and">sku_ids = #skuIds#</isNotEmpty>
        and gift_promotion_id in
        <iterate property="promotionIdList" open="(" close=")" conjunction=",">#promotionIdList[]#</iterate>
    </select>

    <select id="getItemSkuInActive" parameterClass="hashMap" resultMap="GiftPromotionRuleGiveItemRangeResultMap">
        select * from gift_promotion_rule_give_item_range_#dbNo#
        where company_id = #companyId# and enable_status = 1
        <isNotEmpty property="itemId" prepend="and">item_id = #itemId#</isNotEmpty>
        <isNotEmpty property="skuIds" prepend="and">sku_ids = #skuIds#</isNotEmpty>
        and gift_promotion_id in
        <iterate property="promotionIdList" open="(" close=")" conjunction=",">#promotionIdList[]#</iterate>
    </select>

    <select id="getItemByItemIds" parameterClass="hashMap" resultMap="GiftPromotionRuleGiveItemRangeResultMap">
        select * from gift_promotion_rule_give_item_range_#dbNo#
        where company_id = #companyId# and enable_status = 1
        <isNotEmpty property="itemIds">
            and item_id in <iterate property="itemIds" open="(" close=")" conjunction=",">#itemIds[]#</iterate>
        </isNotEmpty>
        <isNotNull property="giftPromotionId" prepend=" and "> gift_promotion_id = #giftPromotionId#</isNotNull>
    </select>

    <select id="getByPromotionIds" parameterClass="hashMap" resultMap="GiftPromotionRuleGiveItemRangeResultMap">
        select * from gift_promotion_rule_give_item_range_#dbNo#
        where company_id = #companyId# and enable_status = 1
        and gift_promotion_id in <iterate property="promotionIdList" open="(" close=")" conjunction=",">#promotionIdList[]#</iterate>
    </select>

</sqlMap>

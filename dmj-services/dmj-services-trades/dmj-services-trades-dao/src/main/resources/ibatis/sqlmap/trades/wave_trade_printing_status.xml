<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="WaveTradePrintingStatus">
    <!-- Alias Map Defined -->
    <typeAlias alias="WaveTradePrintingStatus" type="com.raycloud.dmj.domain.pt.WaveTradePrintingStatus"/>

    <resultMap id="WaveTradePrintingStatusResultMap" class="WaveTradePrintingStatus">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="sid" column="sid"/>
        <result property="waveId" column="wave_id"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="created" column="created"/>
        <result property="modified" column="modified"/>
        <result property="enableStatus" column="enable_status"/>
    </resultMap>

    <!-- 插入 -->
    <insert id="insert" parameterClass="WaveTradePrintingStatus">
        INSERT INTO wave_trade_printing_status_#dbNo#
        <dynamic prepend="(" close=")">
            <isNotNull prepend="," property="id">id</isNotNull>
            <isNotNull prepend="," property="companyId">company_id</isNotNull>
            <isNotNull prepend="," property="type">type</isNotNull>
            <isNotNull prepend="," property="sid">sid</isNotNull>
            <isNotNull prepend="," property="waveId">wave_id</isNotNull>
            <isNotNull prepend="," property="status">status</isNotNull>
            <isNotNull prepend="," property="enableStatus">enable_status</isNotNull>
            ,created
        </dynamic>
        VALUES
        <dynamic prepend="(" close=")">
            <isNotNull prepend="," property="id">#id#</isNotNull>
            <isNotNull prepend="," property="companyId">#companyId#</isNotNull>
            <isNotNull prepend="," property="type">#type#</isNotNull>
            <isNotNull prepend="," property="sid">#sid#</isNotNull>
            <isNotNull prepend="," property="waveId">#waveId#</isNotNull>
            <isNotNull prepend="," property="status">#status#</isNotNull>
            <isNotNull prepend="," property="enableStatus">#enableStatus#</isNotNull>
            ,now()
        </dynamic>
        <selectKey resultClass="long" keyProperty="id">
            SELECT LAST_INSERT_ID() AS ID
        </selectKey>
    </insert>

    <select id="queryByWaveId" resultMap="WaveTradePrintingStatusResultMap" parameterClass="hashMap">
        SELECT *
        FROM wave_trade_printing_status_#dbNo# WHERE company_id = #companyId# and wave_id = #waveId# and enable_status = 1
    </select>

    <select id="queryAllByWaveIds" resultMap="WaveTradePrintingStatusResultMap" parameterClass="hashMap">
        SELECT *
        FROM wave_trade_printing_status_#dbNo# WHERE company_id = #companyId# and enable_status = 1
        <isNotEmpty property="waveIdList">
            AND wave_id in
            <iterate property="waveIdList" conjunction="," open="(" close=")">
                #waveIdList[]#
            </iterate>
        </isNotEmpty>
    </select>

    <select id="queryAllBySids" resultMap="WaveTradePrintingStatusResultMap" parameterClass="hashMap">
        SELECT *
        FROM wave_trade_printing_status_#dbNo# WHERE company_id = #companyId# and enable_status = 1
        <isNotEmpty property="sidList">
            AND sid in
            <iterate property="sidList" conjunction="," open="(" close=")">
                #sidList[]#
            </iterate>
        </isNotEmpty>
    </select>

</sqlMap>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="SplitConfigRule">
    <!-- Alias Map Defined -->
    <typeAlias alias="SplitConfigRule" type="com.raycloud.dmj.domain.trade.split.SplitConfigRule"/>

    <resultMap id="SplitConfigRuleMap" class="SplitConfigRule">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="name" column="name"/>
        <result property="splitType" column="split_type"/>
        <result property="minPayment" column="min_payment"/>
        <result property="leaveMinPayment" column="leave_min_payment"/>
        <result property="splitMaxNum" column="split_max_num"/>
        <result property="splitMinNum" column="split_min_num"/>
        <result property="supportSplit" column="support_split"/>
        <result property="supportMerge" column="support_merge"/>
        <result property="created" column="created"/>
        <result property="modified" column="modified"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="splitStockRatio" column="split_stock_ratio"/>
        <result property="priority" column="priority"/>
        <result property="volumeLimit" column="volume_limit"/>
        <result property="userIds" column="user_ids"/>
        <result property="fxUserIds" column="fx_user_ids"/>
        <result property="express" column="express"/>
        <result property="warehouseIds" column="warehouse_ids"/>
        <result property="conditionDesc" column="condition_desc"/>
        <result property="supportContinueSplit" column="support_continue_split"/>
        <result property="supportGiftNotAlone" column="support_gift_not_alone"/>
        <result property="splitWeight" column="split_weight"/>
        <result property="ruleType" column="rule_type"/>
        <result property="isOpen" column="is_open"/>
        <result property="autoContinueSplit" column="auto_continue_split"/>
    </resultMap>

    <!-- 插入 -->
    <insert id="insert" parameterClass="SplitConfigRule">
        INSERT INTO split_config_rule_#dbNo#
        <dynamic prepend="(" close=")">
            <isNotNull prepend="," property="companyId"> company_id </isNotNull>
            <isNotNull prepend="," property="name"> name </isNotNull>
            <isNotNull prepend="," property="splitType"> split_type </isNotNull>
            <isNotNull prepend="," property="minPayment"> min_payment </isNotNull>
            <isNotNull prepend="," property="leaveMinPayment"> leave_min_payment </isNotNull>
            <isNotNull prepend="," property="splitMaxNum"> split_max_num </isNotNull>
            <isNotNull prepend="," property="splitMinNum"> split_min_num </isNotNull>
            <isNotNull prepend="," property="supportSplit"> support_split </isNotNull>
            <isNotNull prepend="," property="supportMerge"> support_merge </isNotNull>
            <isNotNull prepend="," property="splitStockRatio"> split_stock_ratio </isNotNull>
            <isNotNull prepend="," property="priority"> priority </isNotNull>
            <isNotNull prepend="," property="volumeLimit"> volume_limit </isNotNull>
            <isNotNull prepend="," property="userIds"> user_ids </isNotNull>
            <isNotNull prepend="," property="fxUserIds"> fx_user_ids </isNotNull>
            <isNotNull prepend="," property="express"> express </isNotNull>
            <isNotNull prepend="," property="warehouseIds"> warehouse_ids </isNotNull>
            <isNotNull prepend="," property="conditionDesc"> condition_desc </isNotNull>
            <isNotNull prepend="," property="supportContinueSplit"> support_continue_split </isNotNull>
            <isNotNull prepend="," property="supportGiftNotAlone"> support_gift_not_alone </isNotNull>
            <isNotNull prepend="," property="splitWeight"> split_weight </isNotNull>
            <isNotNull prepend="," property="ruleType"> rule_type </isNotNull>
            <isNotNull prepend="," property="isOpen"> is_open </isNotNull>
            <isNotNull prepend="," property="autoContinueSplit"> auto_continue_split </isNotNull>
            ,created
        </dynamic>
        VALUES
        <dynamic prepend="(" close=")">
            <isNotNull prepend="," property="companyId"> #companyId# </isNotNull>
            <isNotNull prepend="," property="name"> #name# </isNotNull>
            <isNotNull prepend="," property="splitType"> #splitType# </isNotNull>
            <isNotNull prepend="," property="minPayment"> #minPayment# </isNotNull>
            <isNotNull prepend="," property="leaveMinPayment"> #leaveMinPayment# </isNotNull>
            <isNotNull prepend="," property="splitMaxNum"> #splitMaxNum# </isNotNull>
            <isNotNull prepend="," property="splitMinNum"> #splitMinNum# </isNotNull>
            <isNotNull prepend="," property="supportSplit"> #supportSplit# </isNotNull>
            <isNotNull prepend="," property="supportMerge"> #supportMerge# </isNotNull>
            <isNotNull prepend="," property="splitStockRatio"> #splitStockRatio# </isNotNull>
            <isNotNull prepend="," property="priority"> #priority# </isNotNull>
            <isNotNull prepend="," property="volumeLimit"> #volumeLimit# </isNotNull>
            <isNotNull prepend="," property="userIds"> #userIds# </isNotNull>
            <isNotNull prepend="," property="fxUserIds"> #fxUserIds# </isNotNull>
            <isNotNull prepend="," property="express"> #express# </isNotNull>
            <isNotNull prepend="," property="warehouseIds"> #warehouseIds# </isNotNull>
            <isNotNull prepend="," property="conditionDesc"> #conditionDesc# </isNotNull>
            <isNotNull prepend="," property="supportContinueSplit"> #supportContinueSplit# </isNotNull>
            <isNotNull prepend="," property="supportGiftNotAlone"> #supportGiftNotAlone# </isNotNull>
            <isNotNull prepend="," property="splitWeight"> #splitWeight# </isNotNull>
            <isNotNull prepend="," property="ruleType"> #ruleType# </isNotNull>
            <isNotNull prepend="," property="isOpen"> #isOpen# </isNotNull>
            <isNotNull prepend="," property="autoContinueSplit"> #autoContinueSplit# </isNotNull>
            ,now()
        </dynamic>
        <selectKey resultClass="long" keyProperty="id">
            SELECT last_insert_id() AS id
        </selectKey>
    </insert>

    <!-- 根据主键删除 -->
    <update id="delete" parameterClass="SplitConfigRule">
        UPDATE split_config_rule_#dbNo# SET enable_status = 0 WHERE company_id = #companyId# AND id = #id#
    </update>

    <!-- 根据主键更新 -->
    <update id="update" parameterClass="SplitConfigRule">
        UPDATE split_config_rule_#dbNo#
        <dynamic prepend="SET">
            <isNotNull prepend="," property="name"> name = #name# </isNotNull>
            <isNotNull prepend="," property="splitType"> split_type = #splitType# </isNotNull>
            <isNotNull prepend="," property="minPayment"> min_payment = #minPayment# </isNotNull>
            <isNotNull prepend="," property="leaveMinPayment"> leave_min_payment = #leaveMinPayment# </isNotNull>
            <isNotNull prepend="," property="splitMaxNum"> split_max_num = #splitMaxNum# </isNotNull>
            <isNotNull prepend="," property="splitMinNum"> split_min_num = #splitMinNum# </isNotNull>
            <isNotNull prepend="," property="supportSplit"> support_split =  #supportSplit# </isNotNull>
            <isNotNull prepend="," property="supportMerge"> support_merge = #supportMerge# </isNotNull>
            <isNotNull prepend="," property="splitStockRatio"> split_stock_ratio = #splitStockRatio# </isNotNull>
            <isNotNull prepend="," property="priority"> priority = #priority# </isNotNull>
            <isNotNull prepend="," property="volumeLimit"> volume_limit = #volumeLimit# </isNotNull>
            <isNotNull prepend="," property="userIds"> user_ids = #userIds# </isNotNull>
            <isNotNull prepend="," property="fxUserIds"> fx_user_ids = #fxUserIds# </isNotNull>
            <isNotNull prepend="," property="express"> express = #express# </isNotNull>
            <isNotNull prepend="," property="warehouseIds"> warehouse_ids = #warehouseIds# </isNotNull>
            <isNotNull prepend="," property="conditionDesc"> condition_desc = #conditionDesc# </isNotNull>
            <isNotNull prepend="," property="supportContinueSplit"> support_continue_split = #supportContinueSplit# </isNotNull>
            <isNotNull prepend="," property="supportGiftNotAlone"> support_gift_not_alone = #supportGiftNotAlone# </isNotNull>
            <isNotNull prepend="," property="splitWeight"> split_weight = #splitWeight# </isNotNull>
            <isNotNull prepend="," property="isOpen"> is_open = #isOpen# </isNotNull>
            <isNotNull prepend="," property="autoContinueSplit"> auto_continue_split=#autoContinueSplit# </isNotNull>
        </dynamic>
        WHERE company_id = #companyId# AND enable_status = 1 AND id = #id#
    </update>

    <update id="updateOne" parameterClass="SplitConfigRule">
        UPDATE split_config_rule_#dbNo#
        <dynamic prepend="SET">
            <isNotNull prepend="," property="name"> name = #name# </isNotNull>
            <isNotNull prepend="," property="splitType"> split_type = #splitType# </isNotNull>
            <isNotNull prepend="," property="supportSplit"> support_split =  #supportSplit# </isNotNull>
            <isNotNull prepend="," property="supportMerge"> support_merge = #supportMerge# </isNotNull>
            <isNotNull prepend="," property="splitStockRatio"> split_stock_ratio = #splitStockRatio# </isNotNull>
            <isNotNull prepend="," property="priority"> priority = #priority# </isNotNull>
            <isNotNull prepend="," property="userIds"> user_ids = #userIds# </isNotNull>
            <isNotNull prepend="," property="fxUserIds"> fx_user_ids = #fxUserIds# </isNotNull>
            <isNotNull prepend="," property="express"> express = #express# </isNotNull>
            <isNotNull prepend="," property="warehouseIds"> warehouse_ids = #warehouseIds# </isNotNull>
            <isNotNull prepend="," property="conditionDesc"> condition_desc = #conditionDesc# </isNotNull>
            <isNotNull prepend="," property="supportContinueSplit"> support_continue_split = #supportContinueSplit# </isNotNull>
            <isNotNull prepend="," property="supportGiftNotAlone"> support_gift_not_alone = #supportGiftNotAlone# </isNotNull>
            <isNotNull prepend="," property="splitWeight"> split_weight = #splitWeight# </isNotNull>
            <isNotNull prepend="," property="isOpen"> is_open = #isOpen# </isNotNull>
            <isNotNull prepend="," property="volumeLimit"> volume_limit = #volumeLimit# </isNotNull>
            <isNotNull prepend="," property="autoContinueSplit"> auto_continue_split=#autoContinueSplit# </isNotNull>
            ,min_payment = #minPayment#,
            leave_min_payment = #leaveMinPayment#,
            split_max_num = #splitMaxNum#,
            split_min_num = #splitMinNum#
        </dynamic>
        WHERE company_id = #companyId# AND enable_status = 1 AND id = #id#
    </update>

    <update id="reducePriority" parameterClass="hashMap">
        UPDATE split_config_rule_#dbNo# SET priority = priority - 1 WHERE company_id = #companyId# AND enable_status = 1 AND priority &gt; #p#
        <isNotNull property="ruleType" prepend="AND"> rule_type = #ruleType# </isNotNull>
        <isNotNull prepend="AND" property="p2"> priority &lt;= #p2# </isNotNull>
    </update>

    <update id="raisePriority" parameterClass="hashMap">
        UPDATE split_config_rule_#dbNo# SET priority = priority + 1 WHERE company_id = #companyId# AND enable_status = 1 AND priority &gt;= #p#
        <isNotNull property="ruleType" prepend="AND"> rule_type = #ruleType# </isNotNull>
        <isNotNull prepend="AND" property="p2"> priority &lt; #p2# </isNotNull>
    </update>

    <!-- 查询 -->
    <select id="queryList" resultMap="SplitConfigRuleMap" parameterClass="hashMap">
        SELECT * FROM split_config_rule_#dbNo# WHERE company_id = #companyId#
            AND enable_status = 1
            <isNotEmpty property="ids" prepend="AND"> id in <iterate property="ids" open="(" close=")" conjunction=",">  #ids[]#</iterate> </isNotEmpty>
            <isNotEmpty property="names" prepend="AND"> name in <iterate property="names" open="(" close=")" conjunction=",">  #names[]#</iterate> </isNotEmpty>
            <isNotNull property="ruleType" prepend="AND"> rule_type = #ruleType# </isNotNull>
            <isNotNull property="minPriority" prepend=" and "> priority &gt; #minPriority# </isNotNull>
            <isNotNull property="isOpen" prepend=" and "> is_open = #isOpen# </isNotNull>

            order by priority,created
    </select>

    <select id="queryBySplitType" resultMap="SplitConfigRuleMap" parameterClass="hashMap">
        SELECT *
        FROM split_config_rule_#dbNo#
        WHERE company_id = #companyId#
        AND enable_status = 1
        AND is_open = 1
        AND rule_type = #ruleType#
        AND split_type = #splitType#
        ORDER BY priority,created,id
    </select>

</sqlMap>

package com.raycloud.dmj.dao.trade.audit;

import com.raycloud.dmj.dao.trade.update.DaoBatchData;
import com.raycloud.dmj.dao.trade.update.builder.BatchSqlBuilder;
import com.raycloud.dmj.dao.trade.update.UpdateFieldEnum;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Order;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.raycloud.dmj.dao.trade.update.DaoBatchUtils.*;
import static com.raycloud.dmj.dao.trade.update.UpdateFieldEnum.*;

/**
 * @ClassName AuditTradeSqlBuilder
 * @Description 审核批量更新sql
 * <AUTHOR>
 * @Date 2023/12/11
 * @Version 1.0
 */
@Service
public class AuditOrderSqlBuilder implements BatchSqlBuilder<Order> {

    private static final UpdateFieldEnum[] UPDATE_FIELDS = {orderSysStatus, orderStockStatus, orderStockNum, orderInsufficientCanceled, orderDestId, orderSourceId, orderBelongType, orderConvertType, orderCost, orderV};

    @Override
    public void buildBatchUpdateSql(Staff staff, DaoBatchData<Order> batchData) {
        StringBuilder updateSql = new StringBuilder();

        Map<UpdateFieldEnum, StringBuilder> updateFieldMap = initUpdateField(UPDATE_FIELDS);
        List<Long> ids = new ArrayList<>();
        for (Order order : batchData.getDatas()) {
            ids.add(order.getId());
            appendWhenThen(orderSysStatus, updateFieldMap.get(orderSysStatus), order.getId(), order.getSysStatus());
            appendWhenThen(orderStockStatus, updateFieldMap.get(orderStockStatus), order.getId(), order.getStockStatus());
            appendWhenThen(orderStockNum, updateFieldMap.get(orderStockNum), order.getId(), order.getStockNum());
            appendWhenThen(orderInsufficientCanceled, updateFieldMap.get(orderInsufficientCanceled), order.getId(), order.getInsufficientCanceled());
            appendWhenThen(orderDestId, updateFieldMap.get(orderDestId), order.getId(), order.getDestId());
            appendWhenThen(orderSourceId, updateFieldMap.get(orderSourceId), order.getId(), order.getSourceId());
            appendWhenThen(orderBelongType, updateFieldMap.get(orderBelongType), order.getId(), order.getBelongType());
            appendWhenThen(orderConvertType, updateFieldMap.get(orderConvertType), order.getId(), order.getConvertType());
            appendWhenThen(orderCost, updateFieldMap.get(orderCost), order.getId(), order.getCost());
            appendWhenThen(orderV, updateFieldMap.get(orderV), order.getId(), order.getV());
        }

        String whenThen = appendWhenThen("id", updateFieldMap);
        //审核时间
        if (StringUtils.isNotBlank(whenThen)) {
            updateSql.append(whenThen)
                    .append(" where enable_status > 0 ")
                    .append(" and company_id=").append(staff.getCompanyId())
                    .append(" and id in (").append(StringUtils.join(ids, ",")).append(")");
            batchData.setBatchSql(updateSql.toString());
        }
    }

}

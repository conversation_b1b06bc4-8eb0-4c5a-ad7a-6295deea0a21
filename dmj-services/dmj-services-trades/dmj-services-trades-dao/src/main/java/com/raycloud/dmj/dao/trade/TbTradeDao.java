package com.raycloud.dmj.dao.trade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.dao.DaoLogUtils;
import com.raycloud.dmj.dao.TradeQuerySomeFieldsUtils;
import com.raycloud.dmj.dao.TradeSidSnapshotInsertExecutor;
import com.raycloud.dmj.dao.TradeSidSnapshotInsertWorker;
import com.raycloud.dmj.dao.rowmapper.InvaildItemRowMapper;
import com.raycloud.dmj.dao.rowmapper.InvaildItemTradeRowMapper;
import com.raycloud.dmj.dao.rowmapper.RowMapperContext;
import com.raycloud.dmj.dao.trade.export.TradeExcelExportTaskDao;
import com.raycloud.dmj.data.DataAccessException;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.DbInfo;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeMergeEnum;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import com.raycloud.dmj.domain.trade.merge.TradeMergeConf;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.export.TradeExcelExportTask;
import com.raycloud.dmj.domain.trades.params.DangkouSidsGetRequest;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.trades.search.QueryKeyEnum;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.diamond.TradeExportDiamondUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.erp.db.router.ibatis.SqlMapClientBaseDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.core.ArgumentPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by ZuoYun on 2014/7/4.
 * Time: 11:10
 * Information:
 */
//@Repository
@SuppressWarnings({"deprecation", "unchecked"})
public class TbTradeDao extends SqlMapClientBaseDao {

    private final Logger logger = Logger.getLogger(this.getClass());

    public TbTradeDao() {
        domain = "TbTrade";
    }

    @Resource
    TradeSeparationDao tradeSeparationDao;
    @Resource
    TradeExtDao tradeExtDao;
    @Resource
    TradeDistributorDao tradeDistributorDao;
    @Resource
    RuntimeIdSnapshotDao runtimeIdSnapshotDao;
    @Resource
    TradeExcelExportTaskDao tradeExcelExportTaskDao;
    @Resource
    TradeO2oDao tradeO2oDao;

    void init(Staff staff, Trade obj) {
        obj.setCompanyId(staff.getCompanyId());
        obj.setDbNo(staff.getDbInfo().getTradeDbNo());
        obj.setDbKey(staff.getDbNo());
        obj.setTable("trade_" + obj.getDbNo());
    }

    public <T extends Trade> int batchInsert(Staff staff, List<T> trades) {
        if (trades == null || trades.isEmpty()) {
            return 0;
        }
        //判断是否停止了旧的写
        if (TradeDiamondUtils.stopOldLabelWrite(staff)) {
            trades.forEach(item -> item.setTagIds(null));
        }
        tradeSeparationDao.batchInsert(staff, trades);
        List<TradeExt> tradeExtList = Lists.newArrayList();
        List<TradeDistributor> tradeDistributorList = Lists.newArrayList();
        List<TradeO2o> tradeO2oList = Lists.newArrayList();
        for (Trade trade : trades) {
            if (trade.getEnableStatus() == null) {
                trade.setEnableStatus(1);
            }
            init(staff, trade);
            TradeExt tradeExt = trade.getTradeExt();
            if (tradeExt != null) {
                initTradeExtByTrade(trade, tradeExt);
                tradeExtList.add(trade.getTradeExt());
            }
            TradeDistributor tradeDistributor = trade.getTradeDistributor();
            if (tradeDistributor != null) {
                initTradeDistributorByTrade(staff, trade, tradeDistributor);
                tradeDistributorList.add(tradeDistributor);
            }
            TradeO2o tradeO2o = trade.getTradeO2o();
            if (tradeO2o != null) {
                initTradeO2oByTrade(trade, tradeO2o);
                tradeO2oList.add(trade.getTradeO2o());
            }
        }
        int r = super.batchInsert("TbTrade.insert", trades);
        if (CollectionUtils.isNotEmpty(tradeExtList)) {
            tradeExtDao.batchInsert(staff, tradeExtList);
        }
        if (CollectionUtils.isNotEmpty(tradeDistributorList)) {
            tradeDistributorDao.batchInsert(staff, tradeDistributorList);
        }
        if (CollectionUtils.isNotEmpty(tradeO2oList)) {
            tradeO2oDao.batchInsert(staff, tradeO2oList);
        }
        return r;
    }

    @Transactional
    public <T extends Trade> int batchUpdate(Staff staff, List<T> trades) {
        if (trades == null || trades.isEmpty()) {
            return 0;
        }
        //判断是否停止了旧的写
        if (TradeDiamondUtils.stopOldLabelWrite(staff)) {
            trades.forEach(item -> item.setTagIds(null));
        }
        tradeSeparationDao.batchUpdate(staff, trades);
        List<TradeExt> tradeExtList = Lists.newArrayList();
        List<TradeDistributor> tradeDistributorList = Lists.newArrayList();
        List<TradeO2o> tradeO2oList = Lists.newArrayList();
//        printLogs(staff, trades);
        for (Trade trade : trades) {
            init(staff, trade);
            TradeExt tradeExt = trade.getTradeExt();
            if (tradeExt != null) {
                initTradeExtByTrade(trade, tradeExt);
                tradeExtList.add(trade.getTradeExt());
            }
            TradeDistributor tradeDistributor = trade.getTradeDistributor();
            if (tradeDistributor != null) {
                initTradeDistributorByTrade(staff, trade, tradeDistributor);
                tradeDistributorList.add(tradeDistributor);
            }
            TradeO2o tradeO2o = trade.getTradeO2o();
            if (tradeO2o != null) {
                initTradeO2oByTrade(trade, tradeO2o);
                tradeO2oList.add(trade.getTradeO2o());
            }
        }
        int r = super.batchUpdate("TbTrade.update", trades);
        if (CollectionUtils.isNotEmpty(tradeExtList)) {
            List<Long> sidList = tradeExtList.stream().map(t -> t.getSid()).collect(Collectors.toList());
            List<TradeExt> existTradeExts = tradeExtDao.tradeExtsGetBySids(staff, sidList);
           final Set<Long> existTradeExtSids = new HashSet<>();
            if(CollectionUtils.isNotEmpty(existTradeExts)){
                Set<Long> sidSet = existTradeExts.stream().map(t -> t.getSid()).collect(Collectors.toSet());
                existTradeExtSids.addAll(sidSet);
            }
            List<TradeExt> needInsertTradeExts = tradeExtList.stream().filter(t -> !existTradeExtSids.contains(t.getSid())).collect(Collectors.toList());
            List<TradeExt> needUpdateTradeExts = tradeExtList.stream().filter(t -> existTradeExtSids.contains(t.getSid())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(needUpdateTradeExts)){
                tradeExtDao.batchUpdate(staff, needUpdateTradeExts);
            }
            if(CollectionUtils.isNotEmpty(needInsertTradeExts)){
                tradeExtDao.batchInsert(staff, needInsertTradeExts);
            }
        }
        if (CollectionUtils.isNotEmpty(tradeDistributorList)) {
            //保存或更新
            tradeDistributorDao.batchInsert(staff, tradeDistributorList);
        }
        if (CollectionUtils.isNotEmpty(tradeO2oList)) {
            List<Long> sidList = tradeO2oList.stream().map(TradeO2o::getSid).collect(Collectors.toList());
            List<TradeO2o> existTradeO2os = tradeO2oDao.queryTradeO2oBySids(staff, sidList);
            final Set<Long> existTradeO2oSids = new HashSet<>();
            if(CollectionUtils.isNotEmpty(existTradeO2os)){
                Set<Long> sidSet = existTradeO2os.stream().map(TradeO2o::getSid).collect(Collectors.toSet());
                existTradeO2oSids.addAll(sidSet);
                setTradeOriginTradeO2o((List<Trade>) trades, existTradeO2os);
            }
            List<TradeO2o> needInsertTradeO2os = tradeO2oList.stream().filter(t -> !existTradeO2oSids.contains(t.getSid())).collect(Collectors.toList());
            List<TradeO2o> needUpdateTradeO2os = tradeO2oList.stream().filter(t -> existTradeO2oSids.contains(t.getSid())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(needUpdateTradeO2os)){
                tradeO2oDao.batchUpdate(staff, needUpdateTradeO2os);
            }
            if(CollectionUtils.isNotEmpty(needInsertTradeO2os)){
                tradeO2oDao.batchInsert(staff, needInsertTradeO2os);
            }
        }
        return r;
    }
    private <T extends Trade> void printLogs(Staff staff, List<T> trades) {
        try {
            List<String> ignorePrintLogs = TradeDiamondUtils.getIgnorePrintLogs(staff);
            for (Trade trade : trades) {
                if (Objects.isNull(trade.getOutSid()) && Objects.isNull(trade.getTemplateId()) && Objects.isNull(trade.getTemplateType()) && Objects.isNull(trade.getLogisticsCompanyId())) {
                    continue;
                }
                StackTraceElement[] elements = Thread.currentThread().getStackTrace();
                StringBuilder stackInfoBuilder = new StringBuilder();
                for (StackTraceElement element : elements) {
                    // fileName=lineNumber 堆栈定位到具体行数
                    if (ignorePrintLogs.contains(element.getFileName() + "#" + element.getMethodName())) {
                        return;
                    }
                    if (Objects.isNull(element.getFileName())) {
                        continue;
                    }
                    if (!element.getClassName().startsWith("com.raycloud.dmj") || element.getFileName().contains("generated") || element.getFileName().contains("Aspect")) {
                        continue;
                    }
                    if (stackInfoBuilder.length() > 0) {
                        stackInfoBuilder.append("-- ");
                    }
                    stackInfoBuilder.append(element.getFileName()).append("#").append(element.getMethodName());
                }
                Logs.warn(LogHelper.buildLog(staff, String.format("存在更新打印字段的情况堆栈%s", stackInfoBuilder)));
                return;
            }
        } catch (Exception e) {
            Logs.warn(LogHelper.buildLog(staff, String.format("存在更新打印字段的情况堆栈错误 %s", e.getMessage())));
            e.printStackTrace();
        }
    }

    private void setTradeOriginTradeO2o(List<Trade> trades, List<TradeO2o> existTradeO2os) {
        //维护已经存在的 Trade 和 TradeO2O 的关系
        Map<String, Trade> tidMap = trades.stream()
                .collect(Collectors.toMap(Trade::getTid, Function.identity(), (v1, v2) -> v1));
        existTradeO2os.stream()
                .filter(tradeO2o -> tidMap.containsKey(tradeO2o.getTid()))
                .forEach(tradeO2o -> tidMap.get(tradeO2o.getTid()).setOriginTradeO2o(tradeO2o));
    }

    private void initTradeDistributorByTrade(Staff staff, Trade trade, TradeDistributor tradeDistributor) {
        tradeDistributor.setUserId(trade.getUserId());
        tradeDistributor.setCompanyId(trade.getCompanyId());
        tradeDistributor.setSid(trade.getSid());
        tradeDistributor.setTid(trade.getTid());
    }

    private void initTradeExtByTrade(Trade trade, TradeExt tradeExt) {
        if(tradeExt == null){
            return;
        }
        tradeExt.setCompanyId(trade.getCompanyId());
        tradeExt.setUserId(trade.getUserId());
        tradeExt.setSid(trade.getSid());
        tradeExt.setTid(trade.getTid());
    }

    private void initTradeO2oByTrade(Trade trade, TradeO2o tradeO2o) {
        if(tradeO2o == null){
            return;
        }
        tradeO2o.setCompanyId(trade.getCompanyId());
        tradeO2o.setUserId(trade.getUserId());
        tradeO2o.setSid(trade.getSid());
        tradeO2o.setTid(trade.getTid());
    }

    @Transactional
    public <T extends Trade> int realBatchUpdate(Staff staff, List<T> trades) {
        if (trades == null || trades.isEmpty()) {
            return 0;
        }
        if (TradeDiamondUtils.stopOldLabelWrite(staff)) {
            trades.forEach(item -> item.setTagIds(null));
        }
        tradeSeparationDao.realBatchUpdate(staff, trades);

        for (Trade trade : trades) {
            init(staff, trade);
        }

        return realBatchUpdateOnlyTrade(staff, trades);
    }

    public <T extends Trade> int realBatchUpdateOnlyTrade(Staff staff, List<T> trades) {
        if (TradeDiamondUtils.stopOldLabelWrite(staff)) {
            trades.forEach(item -> item.setTagIds(null));
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("dbKey", staff.getDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("table", "trade_" + staff.getDbInfo().getTradeDbNo());
        List<List<T>> partitionList = Lists.partition(trades, 50);
        int count = 0;
        for (List<T> subList : partitionList) {
            params.put("trades", subList);
            super.update("TbTrade.batchUpdate", params);
            count += subList.size();
        }

        return count;
    }

    private Map<String, Object> initParams(Staff staff) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("companyId", staff.getCompanyId());
        params.put("tradeDbNo", staff.getDbInfo().getTradeDbNo());
        params.put("orderDbNo", staff.getDbInfo().getOrderDbNo());
        return params;
    }

    @Transactional
    public int update(Staff staff, Trade trade) throws DataAccessException {
        tradeSeparationDao.update(staff, trade);
        init(staff, trade);
        return update("TbTrade.update", trade);
    }

    private <T> void validateKeys(Staff staff,QueryKeyEnum field,T[] ids){
        //为空后面有处理 这里先放过
        if (ids == null || ids.length == 0) {
            return;
        }
        for (T value : ids) {
            if (field.getForbiddenValues().contains(value)) {
                throw new IllegalArgumentException("key:"+field.getField()+ " 不支持的查询值:"+value);
            }
        }
        if (ids.length > 20000) {
            new QueryLogBuilder(staff).append("超大数据集查询 ").append("key",field.getField()).append("size",ids.length).printStackWarn(logger);
        }
    }

    public List<TbTrade> queryByTids(Staff staff, String... tids) {
        validateKeys(staff,QueryKeyEnum.TID,tids);
        return queryByKeys(staff, null, "tid", tids);
    }

    public List<TbTrade> queryBySids(Staff staff, Long... sids) {
        validateKeys(staff,QueryKeyEnum.SID,sids);
        return queryByKeys(staff, null, "sid", sids);
    }

    public List<TbTrade> queryByMergeSids(Staff staff, Long... mergeSids) {
        validateKeys(staff,QueryKeyEnum.MERGE_SID,mergeSids);
        return queryByKeys(staff, null, "merge_sid", mergeSids);
    }

    public List<TbTrade> queryBySplitSids(Staff staff, Long... splitSids) {
        validateKeys(staff,QueryKeyEnum.SPLIT_SID,splitSids);
        return queryByKeys(staff, null, "split_sid", splitSids);
    }

    public TbTrade queryBySid(Staff staff, Long sid) {
        List<TbTrade> tbTrades = queryBySids(staff, sid);
        return CollectionUtils.isNotEmpty(tbTrades) ? tbTrades.get(0) : null;
    }

    public <T> List<TbTrade>  queryByKeys(Staff staff, String fields, String key, T... values) {
        DbInfo dbInfo = staff.getDbInfo();
        Assert.notNull(dbInfo,"dbInfo 不能为空");
        return queryByKeys(staff, "trade_" + dbInfo.getTradeDbNo(), fields, false, key, values);
    }

    public <T> List<TbTrade> queryByKeys(Staff staff, String table, String fields, boolean includeDelete, String key, T... values) {
        if (values == null || values.length == 0) {
            return new ArrayList<TbTrade>(0);
        }
        QueryKeyEnum keyEnum = QueryKeyEnum.getByField(key);
        if (keyEnum != null) {
            validateKeys(staff,keyEnum,values);
        }

        fields = (fields != null && !(fields = fields.trim()).isEmpty()) ? fields : "*";
        Query q = new Query().append(" /* FORCE_MASTER */ ").append("SELECT ").append(fields);
        q.append(" FROM ").append(table).append(" t ");
        q.append(" WHERE company_id = ?").add(staff.getCompanyId());
        if (!includeDelete) {
            q.append(" AND enable_status > 0");
        }
        q.append(" AND ").append(key).append(" IN (");
        for (int i = 0; i < values.length; i++) {
            q.append(i > 0 ? ", ?" : "?").add(values[i]);
        }
        q.append(")");
        List<TbTrade> query = getJdbcTemplate(staff).query(q.getQ().toString(), q.getArgs().toArray(), RowMapperContext.tbTradeRowMapper);

        if (query !=null && query.size() > 20000) {
            new QueryLogBuilder(staff).append("超大数据集返回 ").append("key",key).append("keySize",values.length).append("returnSize",query.size()).printStackWarn(logger);
        }

        return query;
    }

    public List<TbTrade> queryByWaveIds(Staff staff, Page page, boolean containDelete, String fields, Long warehouseId, Long waveId) {
        return queryByWaveIds(staff, "trade_" + staff.getDbInfo().getTradeDbNo(), fields, page, containDelete, warehouseId, waveId);
    }

    public List<TbTrade> queryByWaveIds(Staff staff, String table, String fields, Page page, boolean containDelete, Long warehouseId, Long waveId) {
        if (null == waveId) {
            return new ArrayList<>();
        }
        fields = (StringUtils.isBlank(fields) ? "*" : fields);
        Query q = new Query().append("SELECT ").append(fields).append(" FROM ").append(table).append(" WHERE company_id = ?").add(staff.getCompanyId());
        if (!containDelete) {
            q.append(" AND enable_status > 0");
        }
        if (null != warehouseId) {
            q.append(" AND warehouse_id = ?").add(warehouseId);
        }
        if (null != waveId) {
            q.append(" AND wave_id = ?").add(waveId);
        }
        q.append(" order by sid ASC");
        if (null != page) {
            q.append(" LIMIT ").append(page.getStartRow()).append(", ").append(page.getOffsetRow());
        }

        return getJdbcTemplate(staff).query(q.getQ().toString(), q.getArgs().toArray(), RowMapperContext.tbTradeRowMapper);
    }

    public List<String> queryTids(Staff staff, Long... sids) {
        validateKeys(staff,QueryKeyEnum.SID,sids);
        return fillMergeTids(staff, queryByKeys(staff, "sid, tid, merge_sid", "sid", sids), null);
    }

    public List<String> queryTidsByTid(Staff staff, String... tidArr) {
        validateKeys(staff,QueryKeyEnum.TID,tidArr);
        return fillMergeTids(staff, queryByKeys(staff, "sid, tid, merge_sid", "tid", tidArr), tidArr);
    }

    public List<String> fillMergeTids(Staff staff, List<TbTrade> tbTrades, String... tidArr) {
        Set<String> tids = new HashSet<>(tbTrades.size());
        Set<Long> mergeSids = new HashSet<>();
        for (Trade trade : tbTrades) {
            tids.add(trade.getTid());
            if (trade.getMergeSid() > 0) {
                mergeSids.add(trade.getMergeSid());
            }
        }
        if(tidArr != null && tidArr.length > 0){
            tids.addAll(Arrays.asList(tidArr));
        }
        if (!mergeSids.isEmpty()) {
            tbTrades = queryByKeys(staff, "sid, tid", "merge_sid", mergeSids.toArray(new Long[0]));
            if (CollectionUtils.isNotEmpty(tbTrades)) {
                tbTrades.forEach(trade -> tids.add(trade.getTid()));
            }
        }
        return new ArrayList<>(tids);
    }

    public List<Trade> searchDbTradeByDate(Staff staff, Page page, List<String> statusList, Date startDate, Date endDate) {
        Map<String, Object> params = initParams(staff);
        params.put("page", page);
        params.put("sysStatusList", statusList);
        params.put("startTime", startDate);
        params.put("endTime", endDate);
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.searchDbTradeByDate", params);
    }

    public Long searchDbTradeByDateOfCount(Staff staff, List<String> statusList, Date startDate, Date endDate) {
        Map<String, Object> params = initParams(staff);
        params.put("sysStatusList", statusList);
        params.put("startTime", startDate);
        params.put("endTime", endDate);
        return (Long) getSqlMapClientTemplate(staff).queryForObject("TbTrade.searchDbTradeByDateOfCount", params);
    }

    public List<Trade> searchTradeByQueryParams(Staff staff, TradeQueryParams tradeQueryParams) {
        Map<String, Object> params = initParams(staff);
        params.put("page", tradeQueryParams.getPage());
        params.put("startTime", tradeQueryParams.getStartTime());
        params.put("endTime", tradeQueryParams.getEndTime());
        if (null != tradeQueryParams.getUserIds()) {
            params.put("userIds", Lists.newArrayList(tradeQueryParams.getUserIds()));
        }
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.searchTradeByQueryParams", params);
    }

    public List<TbTrade> queryTrades(Staff staff, String fields, Query q, Page page, boolean hasItemQuery, boolean useNewQuery) {
        return queryWithJdbcTemplate(staff, fields, q, page, hasItemQuery, useNewQuery, false, false);
    }

    public List<TbTrade> queryTrades(Staff staff, String fields, Query q, Page page, boolean hasItemQuery, boolean useNewQuery, boolean hasTradeExtSearch, boolean hasConsignRecordQuery) {
        //执行SQL查询数据库
        return queryWithJdbcTemplate(staff, fields, q, page, hasItemQuery, useNewQuery, hasTradeExtSearch, hasConsignRecordQuery);
    }

    protected List<TbTrade> queryWithJdbcTemplate(Staff staff, String fields, Query q, Page page, boolean hasItemQuery, boolean useNewQuery, boolean hasTradeExtSearch, boolean hasConsignRecordQuery) {
        StringBuilder sql = buildTradeQuerySql(staff, fields, q, page, hasItemQuery, useNewQuery, hasTradeExtSearch, hasConsignRecordQuery);
        //添加分页参数
        if (page != null) {
            q.add(page.getStartRow()).add(page.getPageSize());
        }
        String sqlStr = sql.toString();
        if (q.isNeedSaveSql()) {
            Query wholeQuery = new Query();
            wholeQuery.setQ(sql).setArgs(q.getArgs());
            q.setWholeSql(wholeQuery.toString());
        }
        List<TbTrade> trades = getJdbcTemplate(staff).query(sqlStr, RowMapperContext.tbTradeRowMapper, q.getArgs().toArray());
        //删除分页参数
        if (page != null) {
            q.getArgs().remove(q.getArgs().size() - 1);
            q.getArgs().remove(q.getArgs().size() - 1);
        }
        return trades;
    }

    public List<TbTrade> queryWithJdbcTemplate(Staff staff,String sql,Object[] args){
        List<TbTrade> trades = getJdbcTemplate(staff).query(sql, RowMapperContext.tbTradeRowMapper, args);
        return trades;
    }

    public Long queryTradesCount(Staff staff, Query q, boolean hasItemQuery) {
        return queryTradesCount(staff, q, hasItemQuery, false);
    }

    //limit 指定返回数量范围
    public Long queryTradesCount(Staff staff, Query q, int limit) {
        StringBuilder sql = buildTradeQuerySql(staff, q, limit);
        //Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append(String.format("[订单查询count table=%s] sql: %s; sort: %s;", q.getTradeTable(), sql.toString(), q.getSort())));
        return getJdbcTemplate(staff).queryForObject(sql.toString(), q.getArgs().toArray(), Long.class);
    }

    public Long queryTradesCount(Staff staff, Query q, boolean hasItemQuery, boolean isUnique) {
        return queryTradesCount(staff, q, hasItemQuery, false, false);
    }

    public Long queryTradesCount(Staff staff, Query q, boolean hasItemQuery, boolean isUnique, boolean hasTradeExtSearch) {
        return queryTradesCount(staff, q, hasItemQuery, isUnique, hasTradeExtSearch, false);
    }

    public Long queryTradesCount(Staff staff, Query q, boolean hasItemQuery, boolean isUnique, boolean hasTradeExtSearch, boolean hasConsignRecordQuery) {
        StringBuilder sql = buildTradeQuerySql(staff, null, q, null, hasItemQuery, isUnique, hasTradeExtSearch, hasConsignRecordQuery);
        String sqlStr = sql.toString();
        if (q.isNeedSaveSql()) {
            Query wholeQuery = new Query();
            wholeQuery.setQ(sql).setArgs(q.getArgs());
            q.setWholeSql(wholeQuery.toString());
        }
        //Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append(String.format("[订单查询count table=%s] sql: %s; sort: %s;", q.getTradeTable(), sql.toString(), q.getSort())));
        return getJdbcTemplate(staff).queryForObject(sql.toString(), q.getArgs().toArray(), Long.class);
    }

    public Long queryTradeSidInsertRuntime(Staff staff, Query q, boolean hasItemQuery, boolean isUnique, boolean hasTradeExtSearch, RuntimeIdSnapshot runtime) {
        Long tradeCount = queryTradesCount(staff, q, hasItemQuery, isUnique, hasTradeExtSearch);
        runtime.setTradeCount(tradeCount);
        long startTime = System.currentTimeMillis();
        Page page = new Page();
        page.setPageSize(1);

        StringBuilder sql = buildTradeQuerySql(staff, hasItemQuery ? "DISTINCT(t.sid)" : "t.sid", q, page, hasItemQuery, isUnique, hasTradeExtSearch);
        sql.delete(sql.length() - " LIMIT ?, ?".length(), sql.length());
        Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append(String.format("[sid快照建立查询模版 table=%s] sql: %s; sort: %s;", q.getTradeTable(), sql, q.getSort())));
        TradeSidSnapshotInsertWorker worker = null;
        if (runtime.isBatch()) {
            if (runtime.getSnapshotWorker() == null) {
                runtime.setSnapShotWorker(new TradeSidSnapshotInsertWorker(staff, runtime, runtimeIdSnapshotDao, staff.getDbNo()));
                TradeSidSnapshotInsertExecutor.getInstance().execute(((TradeSidSnapshotInsertWorker) runtime.getSnapshotWorker()));
            }
            worker = (TradeSidSnapshotInsertWorker) runtime.getSnapshotWorker();
        } else {
            worker = new TradeSidSnapshotInsertWorker(staff, runtime, runtimeIdSnapshotDao, staff.getDbNo());
            TradeSidSnapshotInsertExecutor.getInstance().execute(worker);
        }
        if (worker == null) {
            throw new IllegalArgumentException("worker 不可以为空！！");
        }
        Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append("申请建立快照：isReject:" + worker.isReject()));

        if (worker.isReject()) {
            return TradeSidSnapshotInsertWorker.REJECT;
        }
        boolean exception = false;
        try {
            TradeSidSnapshotInsertWorker finalWorker = worker;
            getJdbcTemplate(staff).query(con -> {
                ArgumentPreparedStatementSetter argumentPreparedStatementSetter = new ArgumentPreparedStatementSetter(q.getArgs().toArray());
                PreparedStatement preparedStatement =
                        con.prepareStatement(sql.toString(),
                                ResultSet.TYPE_FORWARD_ONLY,
                                ResultSet.CONCUR_READ_ONLY);
                argumentPreparedStatementSetter.setValues(preparedStatement);
                preparedStatement.setFetchSize(Integer.MIN_VALUE);
                preparedStatement.setFetchDirection(ResultSet.FETCH_FORWARD);
                return preparedStatement;
            }, rs -> {
                finalWorker.addId(rs.getLong(1));
            });
        } catch (Throwable e) {
            exception = true;
            throw e;
        } finally {
            if (exception
                    || (runtime.isBatch() && runtime.finalBatch())
                    || !runtime.isBatch()) {
                worker.queryEnd();
            }
        }
        Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append(String.format("[订单查询结果插入运行时 共耗时：%s ms ,查询出来的sid总数为 %s.", System.currentTimeMillis() - startTime, worker.getCount())));
        return tradeCount;
    }

    private String getFields(Query q, String fields, boolean hasItemQuery, Staff staff) {
        Sort sort = q.getSortTrade();
        if (fields == null || fields.trim().isEmpty()) {
            if (hasItemQuery) {
                if (sort != null && CollectionUtils.isNotEmpty(sort.getSortExcepLevel())) {
                    String frontSortExcep = buildFrontSortExcep(sort.getSortExcepLevel());
                    if (StringUtils.isBlank(frontSortExcep)) {
                        return "DISTINCT(t.sid)";
                    }
                    return "DISTINCT(t.sid)," + frontSortExcep;
                }
                return "DISTINCT(t.sid)";
            } else {
                if (sort != null) {
                    if ("payment".equals(sort.getField())) {
                        return "  CAST(ifnull(tp.payment,t.payment) AS DECIMAL(15, 2)) payment_order,t.* ";
                    }
                    if (CollectionUtils.isNotEmpty(sort.getSortExcepLevel())) {
                        String frontSortExcep = buildFrontSortExcep(sort.getSortExcepLevel());
                        if (StringUtils.isBlank(frontSortExcep)) {
                            return "t.*";
                        }
                        return frontSortExcep + ",t.*";
                    }
                }
                return "t.*";
            }
        } else {
            return fields;
        }
    }

    private String buildFrontSortExcep(List<SortExcepLevel> sortExcepLevels) {
        //升序排序 level:顺序,excepFile 异常名称
        StringBuffer sb = new StringBuffer("  ");
        for (int i = 0; i < sortExcepLevels.size(); i++) {
            SortExcepLevel excepLevel = sortExcepLevels.get(i);
            if (excepLevel.getType() == 0) {
                //0 系统异常
                if (TradeQueryParams.STATUS_EXCEP_HALT.equals(excepLevel.getExcepName())) {
                    sb.append("t.is_halt AS  ").append(TradeQueryParams.STATUS_EXCEP_HALT).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_REFUND.equals(excepLevel.getExcepName())) {
                    sb.append("t.is_refund  AS  ").append(TradeQueryParams.STATUS_EXCEP_REFUND).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_BLACK.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.BLACK_NICK).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_BLACK).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.UNALLOCATED).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_ITEM_RELATION_MODIFIED.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.RELATION_CHANGED).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_ITEM_RELATION_MODIFIED).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_STOCK_INSUFFICIENT.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.INSUFFICIENT).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_STOCK_INSUFFICIENT).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_ADDRESS_CHANGED.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.ADDRESS_CHANGED).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_ADDRESS_CHANGED).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_ITEM_CHANGED.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.ITEM_CHANGED).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_ITEM_CHANGED).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_SELLERMEMO_UPDATED.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.SELLER_MEMO_UPDATE).append(" >0) THEN '1' ELSE '0' END  AS ").append(TradeQueryParams.STATUS_EXCEP_SELLERMEMO_UPDATED).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_PART_REFUND.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.PART_REFUND).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_PART_REFUND).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_UNATTAINABLE.equals(excepLevel.getExcepName())) {
                    sb.append("t.unattainable AS  ").append(TradeQueryParams.STATUS_EXCEP_UNATTAINABLE).append(",");
                    ;
                } else if (TradeQueryParams.STATUS_EXCEP_RISK_ORDER.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.PDD_RISKEXCEP).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_RISK_ORDER).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_AMBIGUITY_FX.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.excep & ").append(TradeConstants.FX_AMBIGUITY).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_AMBIGUITY_FX).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_UNAUDIT_FX.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.excep & ").append(TradeConstants.FX_UNAUDIT).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_UNAUDIT_FX).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_WAITEPAY_FX.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.excep & ").append(TradeConstants.FX_WAITPAY).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_WAITEPAY_FX).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_REPULSE_FX.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.excep & ").append(TradeConstants.FX_REPULSE).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_REPULSE_FX).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_PLATFORM_FX_SPLIT.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.excep & ").append(TradeConstants.PLATFORM_FX_SPLIT_EXCEPT).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_PLATFORM_FX_SPLIT).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_POISON_NOT_MATCH_EXPRESS_TEMPLATE.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.excep & ").append(TradeConstants.POISON_NOT_MATCH_EXPRESS_TEMPLATE).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_POISON_NOT_MATCH_EXPRESS_TEMPLATE).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_LOST_MSG.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.LOST_MESSAGE).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_LOST_MSG).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_DELIVER.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.DELIVER_EXCEPT).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_DELIVER).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_UPLOAD_DELIVER.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.v & 4 >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_UPLOAD_DELIVER).append(",");
                } else if (TradeQueryParams.STATUS_WAITING_RETURN_WMS.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.WATING_RETURN_WMS_EXCEPT).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_WAITING_RETURN_WMS).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_SUITE_QUANTITY_CHANGE.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.SUITE_QUANTITY_CHANGE).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_SUITE_QUANTITY_CHANGE).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_PROCESS_CHANGE.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.ITEM_PROCESS_EXCEP).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_PROCESS_CHANGE).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_COD_REPEAT.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.COD_REPEAT).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_COD_REPEAT).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_STOCK_OUT.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.PDD_STOCK_OUT).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_STOCK_OUT).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_WAIT_MERGE.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.WAIT_MERGE).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_WAIT_MERGE).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_ITEM_SHUTOFF.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.ITEM_SHUTOFF).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_ITEM_SHUTOFF).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_UNIQUE_CODE_OFFSHELF.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.UNIQUE_CODE_OFFSHELF).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_UNIQUE_CODE_OFFSHELF).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_TMCS_STOCK_OUT.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.TMCS_STOCK_OUT).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_TMCS_STOCK_OUT).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_TMGJZY_STOCK_OUT.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.TMGJZY_STOCK_OUT).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_TMGJZY_STOCK_OUT).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_PLATFORM_WAREHOUSE_MAPPING.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.item_excep & ").append(TradeConstants.PLATFORM_WAREHOUSE_MAPPING_EXCEPTION).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_PLATFORM_WAREHOUSE_MAPPING).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_VIP_COOPERATION_CODE_NOT_MATCH.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.excep & ").append(TradeConstants.VIP_COOPERATION_CODE_NOT_MATCH).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_VIP_COOPERATION_CODE_NOT_MATCH).append(",");
                } else if (TradeQueryParams.STATUS_EXCEP_SMTQTG_UN_CONFIRM_EXCEPTION.equals(excepLevel.getExcepName())) {
                    sb.append("case WHEN (t.excep & ").append(TradeConstants.SMTQTG_UN_CONFIRM_EXCEPTION).append(" >0) THEN '1' ELSE '0' END  AS  ").append(TradeQueryParams.STATUS_EXCEP_SMTQTG_UN_CONFIRM_EXCEPTION).append(",");
                } else if (String.valueOf(ExceptEnum.FINANCE_REJECT_EXCEPT.getId()).equals(excepLevel.getExcepName())) {
                    // 42: 财审异常，查trade_except 表 过滤掉
                } else {
                    // 不要报错
                    Logs.warn("订单查询暂时不支持当前系统异常排序[" + excepLevel.getExcepName() + "]，需要单独实现");
                }
            } else if (excepLevel.getType() == 1 && StringUtils.isNumeric(excepLevel.getExcepTagId())) {
                //1 自定义异常
                sb.append("case WHEN (LOCATE('").append(excepLevel.getExcepTagId()).append("', `except_ids`) > 0) THEN '1' ELSE '0' END  AS  ").append("自定义标签").append(i).append(",");
            }
        }
        return sb.toString().substring(0, sb.length() - 1);
    }

    public StringBuilder buildTradeQuerySql(Staff staff, String fields, Query q, Page page, boolean hasItemQuery, boolean useNewQuery, boolean hasTradeExtSearch) {
        return buildTradeQuerySql(staff, fields, q, page, hasItemQuery, useNewQuery, hasTradeExtSearch, false);
    }

    public StringBuilder buildTradeQuerySql(Staff staff, String fields, Query q, Page page, boolean hasItemQuery, boolean useNewQuery, boolean hasTradeExtSearch, boolean hasConsignRecordQuery) {
        return buildTradeQuerySql(staff, fields, q, page, hasItemQuery, useNewQuery, hasTradeExtSearch, hasConsignRecordQuery, false);
    }

    public StringBuilder buildTradeQuerySql(Staff staff, String fields, Query q, Page page, boolean hasItemQuery, boolean useNewQuery, boolean hasTradeExtSearch, boolean hasConsignRecordQuery, boolean exportTrade) {
        StringBuilder buf = new StringBuilder("SELECT ");
        if (page == null) {
            if (StringUtils.isNotBlank(q.getCountField())) {
                buf.append(q.getCountField());
            } else {
                buf.append(hasItemQuery ? "COUNT(DISTINCT(t.sid))" : "COUNT(*)");
            }
        } else {
            buf.append(getFields(q, fields, hasItemQuery, staff));
            //导出流程，构建SQL，不用返回额外字段
            if (hasTradeExtSearch && !exportTrade) {
                buf.append(",CASE WHEN e.extra_fields is NOT NULL THEN json_extract(e.extra_fields,'$.itemQuantity') ELSE 0 END as itemQuantity");
            }
        }

        buf.append(" FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t");
        if (StringUtils.isNotBlank(q.getTradeIndex())) {
            // 查订单表时需要强制引用索引的走强制引用索引
            buf.append(q.getTradeIndex());
        }else {
            if(page == null && StringUtils.isNotBlank(q.getTradeCountIndex()) && q.isAllowTradeCountIndex()){
                buf.append(q.getTradeCountIndex());
            }
        }
        if (hasItemQuery) {
            if (!useNewQuery) {
                buf.append(" JOIN ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" o ON t.sid = o.sid AND o.enable_status > 0");
            } else {
                buf.append(" JOIN ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" o ON t.sid = o.belong_sid AND o.enable_status > 0");
            }
        }

        if (hasTradeExtSearch) {
            buf.append(" JOIN ").append(q.getTradeExtTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" e ON t.sid = e.sid AND t.company_id = e.company_id");
        }

        if (hasConsignRecordQuery) {
            buf.append(" LEFT JOIN consign_record_").append(staff.getDbInfo().getTradeDbNo()).append(" cr ON t.sid = cr.sid AND t.company_id = cr.company_id");
        }

        //payment排序
        if (q.getSortTrade() != null && "payment".equals(q.getSortTrade().getField())) {
            buf.append(" LEFT JOIN (select merge_sid AS msid,sum(payment) payment from  ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
            buf.append(" where company_id = ").append(staff.getCompanyId()).append("  and merge_sid>0 and enable_status>=1 group by merge_sid order by null) tp on t.sid=tp.msid");
        }

        if (q.isOnlyContains()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("onlyContains"))) {
                buf.append(q.getParamBeforeWhere().get("onlyContains"));
            }
        }
        if (q.isTradeAddress()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("tradeAddress"))) {
                buf.append(q.getParamBeforeWhere().get("tradeAddress"));
            }
        }
        if (q.isOrderSupplier()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("orderSupplier"))) {
                buf.append(q.getParamBeforeWhere().get("orderSupplier"));
            }
        }
        if (q.isTradeSalesman()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("tradeSalesman"))) {
                buf.append(q.getParamBeforeWhere().get("tradeSalesman"));
            }
        }
        if (q.isOrderCaigou()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("orderCaigou"))) {
                buf.append(q.getParamBeforeWhere().get("orderCaigou"));
            }
        }
        buf.append(" WHERE ").append(q.getQ());
        if (page != null && StringUtils.isNotBlank(q.getGroupByPart())) {
            buf.append(" group by  ").append(q.getGroupByPart());
        }
        if (page != null && StringUtils.isNotBlank(q.getSort())) {
            buf.append(" ORDER BY ").append(q.getSort());
        }
        if (page != null) {
            buf.append(" LIMIT ?, ?");
        }
        return buf;
    }

    public StringBuilder buildTaskTradeQuery3MonthSql(Staff staff, Query q) {
        StringBuilder buf = new StringBuilder("SELECT DISTINCT(t.sid)").append(" FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t");
        if (StringUtils.isNotBlank(q.getTradeIndex())) {
            // 查订单表时需要强制引用索引的走强制引用索引
            buf.append(q.getTradeIndex());
        }

        //payment排序
        if (q.getSortTrade() != null && "payment".equals(q.getSortTrade().getField())) {
            buf.append(" LEFT JOIN (select merge_sid AS msid,sum(payment) payment from  ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
            buf.append(" where company_id = ").append(staff.getCompanyId()).append("  and merge_sid>0 and enable_status>=1 group by merge_sid order by null) tp on t.sid=tp.msid");
        }

        if (q.isOnlyContains()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("onlyContains"))) {
                buf.append(q.getParamBeforeWhere().get("onlyContains"));
            }
        }
        if (q.isTradeAddress()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("tradeAddress"))) {
                buf.append(q.getParamBeforeWhere().get("tradeAddress"));
            }
        }
        if (q.isOrderSupplier()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("orderSupplier"))) {
                buf.append(q.getParamBeforeWhere().get("orderSupplier"));
            }
        }
        if (q.isTradeSalesman()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("tradeSalesman"))) {
                buf.append(q.getParamBeforeWhere().get("tradeSalesman"));
            }
        }
        if (q.isOrderCaigou()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("orderCaigou"))) {
                buf.append(q.getParamBeforeWhere().get("orderCaigou"));
            }
        }
        buf.append(" WHERE ").append(q);
        if (q.getSort() != null) {
            buf.append(" ORDER BY ").append(q.getSort());
        }
        buf.append(" LIMIT 0, 300000");
        return buf;
    }

    public StringBuilder buildTradeQuerySql(Staff staff, Query q, int limit) {
        StringBuilder buf = new StringBuilder("SELECT COUNT(*) FROM (")
                .append(" SELECT 1 FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t")
                .append(Optional.ofNullable(q.getTradeIndex()).filter(s -> Objects.equals(q.getTradeTable(), "trade")).orElse(""))
                .append(" WHERE ").append(q.getQ()).append(" LIMIT ").append(limit)
                .append(" ) t");
        return buf;
    }

    public StringBuilder buildTaskTradeQuerySql(Staff staff, Query q, boolean hasItemQuery, boolean useNewQuery, boolean hasTradeExtSearch, boolean hasConsignRecordQuery) {
        StringBuilder buf = new StringBuilder("SELECT ");
        buf.append("DISTINCT(t.sid)");
        buf.append(" FROM ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t");
        if (StringUtils.isNotBlank(q.getTradeIndex())) {
            // 查订单表时需要强制引用索引的走强制引用索引
            buf.append(q.getTradeIndex());
        }
        if (hasItemQuery) {
            if (!useNewQuery) {
                buf.append(" JOIN ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" o ON t.sid = o.sid AND o.enable_status > 0");
            } else {
                buf.append(" JOIN ").append(q.getOrderTable()).append("_").append(staff.getDbInfo().getOrderDbNo()).append(" o ON t.sid = o.belong_sid AND o.enable_status > 0");
            }
        }
        if (hasTradeExtSearch) {
            buf.append(" JOIN ").append(q.getTradeExtTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" e ON t.sid = e.sid AND t.company_id = e.company_id");
        }

        if (hasConsignRecordQuery) {
            buf.append(" LEFT JOIN consign_record_").append(staff.getDbInfo().getTradeDbNo()).append(" cr ON t.sid = cr.sid AND t.company_id = cr.company_id");
        }

        //payment排序
        if (q.getSortTrade() != null && "payment".equals(q.getSortTrade().getField())) {
            buf.append(" LEFT JOIN (select merge_sid AS msid,sum(payment) payment from  ").append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo());
            buf.append(" where company_id = ").append(staff.getCompanyId()).append("  and merge_sid>0 and enable_status>=1 group by merge_sid order by null) tp on t.sid=tp.msid");
        }

        if (q.isOnlyContains()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("onlyContains"))) {
                buf.append(q.getParamBeforeWhere().get("onlyContains"));
            }
        }
        if (q.isTradeAddress()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("tradeAddress"))) {
                buf.append(q.getParamBeforeWhere().get("tradeAddress"));
            }
        }
        if (q.isOrderSupplier()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("orderSupplier"))) {
                buf.append(q.getParamBeforeWhere().get("orderSupplier"));
            }
        }
        if (q.isTradeSalesman()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("tradeSalesman"))) {
                buf.append(q.getParamBeforeWhere().get("tradeSalesman"));
            }
        }
        if (q.isOrderCaigou()) {
            if (!q.getParamBeforeWhere().isEmpty() && StringUtils.isNotBlank(q.getParamBeforeWhere().get("orderCaigou"))) {
                buf.append(q.getParamBeforeWhere().get("orderCaigou"));
            }
        }
        buf.append(" WHERE ").append(q.toString());
        return buf;
    }

    @Transactional
    public int batchUpdateParty3(Staff staff, List<Trade> trades) {
        tradeSeparationDao.batchUpdateParty3(staff, trades);
        for (Trade trade : trades) {
            init(staff, trade);
        }
        return batchUpdate("TbTrade.updateParty3", trades);
    }

    public List<TbTrade> queryCheckMerge(Staff staff, Long userId, Date created, boolean isOpenSeparation, Integer belongType, List<String> buyerNicks) {
        Map<String, Object> params = initParams(staff);
        params.put("isOpenSeparation", isOpenSeparation);
        params.put("buyerNicks", buyerNicks);
        params.put("belongType", belongType);
        if (created != null) {
            params.put("created", created);
        }
        List<TbTrade> result = new ArrayList<>();
        List<TbTrade> tbTrades = queryCheckMergeFromDb(staff, params);
        if (tbTrades != null) {
            for (TbTrade tbTrade : tbTrades) {
                if (tbTrade.getUserId() == userId.longValue()) {
                    result.add(tbTrade);
                }
            }
        }
        return result;
    }

    public List<TbTrade> queryCheckMergePdd(Staff staff, List<Long> sids, Date created) {
        Map<String, Object> params = initParams(staff);
        params.put("sids", sids);
        params.put("created", created);
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryCheckMergePdd", params);
    }

    protected List<TbTrade> queryCheckMergeFromDb(Staff staff, Map<String, Object> params) {
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryCheckMerge", params);
    }

    /**
     * 根据buyerNicks查询订单锁
     *
     * @param staff
     * @param buyerNicks
     * @return
     */
    public List<TbTrade> queryMergeAuto(Staff staff, List<String> buyerNicks, TradeConfig tradeConfig, TradeMergeConf tradeMergeConf) {
        if (CollectionUtils.isEmpty(buyerNicks)) {
            return new ArrayList<>();
        }
        Map<String, Object> params = getParamsQueryMergeAuto(staff, tradeConfig, tradeMergeConf);
        params.put("buyerNicks", buyerNicks);
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryMergeAuto", params);
    }

    public List<TbTrade> queryMergeAutoPdd(Staff staff, List<Long> sids, TradeConfig tradeConfig, TradeMergeConf tradeMergeConf) {
        Map<String, Object> params = getParamsQueryMergeAuto(staff, tradeConfig, tradeMergeConf);
        params.put("sids", sids);
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryMergeAutoPdd", params);
    }

    private Map<String, Object> getParamsQueryMergeAuto(Staff staff, TradeConfig tradeConfig, TradeMergeConf tradeMergeConf) {
        Map<String, Object> params = initParams(staff);
        params.put("userIds", Collections.singletonList(tradeMergeConf.getUserId()));
        //允许已审核的订单参与合单
        if (tradeMergeConf.getMamFilterFinishAudit() != null && tradeMergeConf.getMamFilterFinishAudit() == 1) {
            mergeSysStatus(tradeConfig, tradeMergeConf, params);
        }
        params.put("hasSysTrade", Objects.equals(1, tradeMergeConf.getHasSysTrade()) ? 1 : 0);
        return params;
    }

    protected List<TbTrade> queryMergeAutoFromDb(Staff staff, Map<String, Object> params) {
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryMergeAuto", params);
    }

    /**
     * 根据收件地址receiverAddress查询订单
     *
     * @param staff
     * @return
     */
    public List<TbTrade> queryMergeAutoManual(Staff staff, TradeMergeEnum mergeEnum, TradeMergeConf tradeMergeConf, List<String> receiverAddresss, TradeConfig tradeConfig) {
        if (CollectionUtils.isEmpty(receiverAddresss)) {
            return new ArrayList<>();
        }
        Map<String, Object> params = getParamsQueryMergeAutoManual(staff, mergeEnum, tradeMergeConf, tradeConfig);
        params.put("buyerNicks", receiverAddresss);
        Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append("queryMergeAutoManual：" + JSON.toJSONString(params)));
        return queryMergeAutoManualFromDb(staff, params);
    }

    public List<TbTrade> queryMergeAutoManualPdd(Staff staff, TradeMergeEnum mergeEnum, TradeMergeConf tradeMergeConf, List<Long> sids, TradeConfig tradeConfig) {
        if (CollectionUtils.isEmpty(sids)) {
            return new ArrayList<>();
        }
        Map<String, Object> params = getParamsQueryMergeAutoManual(staff, mergeEnum, tradeMergeConf, tradeConfig);
        params.put("sids", sids);
        Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append("queryMergeAutoManualPdd：" + JSON.toJSONString(params)));
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryMergeAutoManualPdd", params);
    }

    private Map<String, Object> getParamsQueryMergeAutoManual(Staff staff, TradeMergeEnum mergeEnum, TradeMergeConf tradeMergeConf, TradeConfig tradeConfig) {
        Map<String, Object> params = initParams(staff);
        List<Long> userIds = tradeMergeConf.getUserIds();
        if (userIds == null || userIds.size() == 0) {
            userIds = Collections.singletonList(tradeMergeConf.getUserId());
        }
        params.put("userIds", userIds);

        List<Integer> mergeTypes = new ArrayList<>();
        mergeTypes.add(-1);
        mergeTypes.add(1);
        mergeTypes.add(2);
        if (mergeEnum == TradeMergeEnum.MERGE_AUTO_NULTIL) {//跨店智能合单
            mergeTypes.add(4);
        }
        //允许合并已经取消合单的订单
        if (tradeMergeConf.getMamFilterUndo() != null && tradeMergeConf.getMamFilterUndo() == 1) {
            mergeTypes.add(3);
        }
        params.put("mergeTypes", mergeTypes);
        //异常单不合并
        if (tradeMergeConf.getMamFilterExcep() != null && tradeMergeConf.getMamFilterExcep() == 1 && (tradeMergeConf.getSpeExcep() == null || tradeMergeConf.getSpeExcep() == 0)) {
            params.put("isExcep", 0);
        }
        //拆单是否可以合并
        params.put("isSplit", tradeMergeConf.getMamFilterSplit() != null && tradeMergeConf.getMamFilterSplit() == 1 ? 1 : 0);
        //允许已审核的订单参与合单
        if (tradeMergeConf.getMamFilterFinishAudit() != null && tradeMergeConf.getMamFilterFinishAudit() == 1) {
            mergeSysStatus(tradeConfig, tradeMergeConf, params);
        }
        return params;
    }

    protected List<TbTrade> queryMergeAutoManualFromDb(Staff staff, Map<String, Object> params) {
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryMergeAutoManual", params);
    }

    private void mergeSysStatus(TradeConfig tradeConfig, TradeMergeConf tradeMergeConf, Map<String, Object> params) {
        Integer mergeWaitPrint = tradeMergeConf.getMergeWaitPrint();
        Integer mergeWaitPack = tradeMergeConf.getMergeWaitPack();
        Integer mergeWaitWeight = tradeMergeConf.getMergeWaitWeight();
        Integer mergeWaitConsign = tradeMergeConf.getMergeWaitConsign();
        //这里是为了兼容老数据
        if ((mergeWaitPrint == null || mergeWaitPrint == 0) && (mergeWaitPack == null || mergeWaitPack == 0) && (mergeWaitWeight == null || mergeWaitWeight == 0) && (mergeWaitConsign == null || mergeWaitConsign == 0)) {
            params.put("isFinishAudit", 1);
        } else {
            Integer openPackageExamine = tradeConfig.getOpenPackageExamine();
            Integer openPackageWeigh = tradeConfig.getOpenPackageWeigh();

            params.put("openPackageExamine", openPackageExamine);
            params.put("openPackageWeigh", openPackageWeigh);

            params.put("isFinishAudit", 2);
            if (mergeWaitPrint != null && mergeWaitPrint == 1) {
                params.put("mergeWaitPrint", 1);
            }
            if (mergeWaitPack != null && mergeWaitPack == 1 && openPackageExamine != null && openPackageExamine == 1) {
                params.put("mergeWaitPack", 1);
            }
            if (mergeWaitWeight != null && mergeWaitWeight == 1 && openPackageWeigh != null && openPackageWeigh == 1) {
                params.put("mergeWaitWeight", 1);
            }
            if (mergeWaitConsign != null && mergeWaitConsign == 1) {
                params.put("mergeWaitConsign", 1);
            }
        }
    }

    /**
     * 根据 buyerNick 查询可合单的订单的sid
     */
    public List<Long> queryMergeSids(Staff staff, Long userId, Integer belongType, List<String> buyerNicks) {
        Map<String, Object> params = initParams(staff);
        params.put("buyerNicks", buyerNicks);
        params.put("userId", userId);
        params.put("belongType", belongType);
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryMergeSids", params);
    }

    /**
     * 根据sids查询需要合单的订单
     */
    public List<TbTrade> queryMergeTrades(Staff staff, TradeMergeConf tradeMergeConf, Integer mergeType, Long... sids) {
        Map<String, Object> params = initParams(staff);
        params.put("sids", sids);
        params.put("mergeType", mergeType);
        params.put("isSplit", 0);
        if (mergeType == 1 || mergeType == 3 || mergeType == 5) {
            if (tradeMergeConf != null) {
                if (tradeMergeConf.getMamFilterUndo() != null && tradeMergeConf.getMamFilterUndo() == 1) {
                    params.put("isMergeUndo", tradeMergeConf.getMamFilterUndo());
                }
                //如果勾选了异常类型内存还需要过滤一遍
                if (tradeMergeConf.getMamFilterExcep() != null && tradeMergeConf.getMamFilterExcep() == 1 && (tradeMergeConf.getSpeExcep() == null || tradeMergeConf.getSpeExcep() == 0)) {//未勾选异常类型
                    params.put("isExcep", 0);
                }
                if (tradeMergeConf.getMamFilterSplit() != null) {
                    params.put("isSplit", tradeMergeConf.getMamFilterSplit());
                }
                //允许已审核的订单参与合单
                if (tradeMergeConf.getMamFilterFinishAudit() != null && tradeMergeConf.getMamFilterFinishAudit() == 1) {
                    params.put("isFinishAudit", 1);
                }
            }
        }
        Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append(String.format("queryMergeTrades[companyId=%s,params=%s]", staff.getCompanyId(), JSON.toJSON(params))));
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryMergeTrades", params);
    }

    public List<Map<Long, Long>> queryUserIdsHavingTimeoutActionTrade(Integer dbNo, Integer tbNo, Date timeoutDate, Long companyId) {
        Map<String, Object> params = new HashMap<String, Object>(3, 1);
        params.put("timeoutDate", timeoutDate);
        params.put("tradeDbNo", tbNo);
        params.put("companyId", companyId);
        return getSqlMapClientTemplate(dbNo).queryForList("TbTrade.queryUserIdsHavingTimeoutActionTrade", params);
    }

    public void updatePackmaItemCost(Staff staff, Long sid, Double packmaItemCost) {
        Map<String, Object> params = new HashMap<String, Object>(6, 1);
        params.put("sid", sid);
        params.put("packmaCost", packmaItemCost);
        params.put("companyId", staff.getCompanyId());
        params.put("tradeDbNo", staff.getDbInfo().getTradeDbNo());
        params.put("table", "trade_" + staff.getDbInfo().getTradeDbNo());
        getSqlMapClientTemplate(staff).update("TbTrade.updatePackmaItemCost", params);
        //重新设置table，更新相同结构的trade_not_consign表
        params.put("table", "trade_not_consign_" + staff.getDbInfo().getTradeDbNo());
        tradeSeparationDao.updatePackmaItemCost(staff, params);
    }


    @VisibleForTesting
    protected JdbcTemplate getJdbcTemplate(Staff staff) {
        JdbcTemplate jdbcTemplate = super.getJdbcTemplate(staff);
        jdbcTemplate.setQueryTimeout(38);
        return jdbcTemplate;
    }

    public List<String> queryTidsExist(Staff staff, List<String> tidList) {
        if (CollectionUtils.isEmpty(tidList)) {
            return Collections.emptyList();
        }

        Map<String, Object> params = new HashMap<String, Object>(3, 1);
        params.put("tradeDbNo", staff.getDbInfo().getTradeDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("tidList", tidList);

        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryTidsExist", params);
    }

    public List<Trade> queryNotChatAndDelayTime(Staff staff, Page page, Double days, String... taobaoIds) {
        Map<String, Object> params = initParams(staff);
        params.put("days", days + "");
        params.put("taobaoIds", taobaoIds);
        params.put("page", page);
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryNotChatAndDelayTime", params);
    }


    /**
     * 根据companyId+订单类型+系统状态+userId+买家昵称查询订单
     * 有索引应该不会特别慢吧。。。。
     *
     * @param staff
     * @param userId
     * @param buyerNicks
     * @return
     */
    public List<TbTrade> queryCodRepeat(Staff staff, Long userId, List<String> buyerNicks) {
        List<Long> userIds = new ArrayList<>();
        userIds.add(userId);
        return queryCodRepeat(staff, userIds, buyerNicks);
    }

    public List<TbTrade> queryCodRepeat(Staff staff, List<Long> userIds, List<String> buyerNicks) {
        Map<String, Object> params = initParams(staff);
        params.put("buyerNicks", buyerNicks);
        params.put("userIds", userIds);
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryCodRepeat", params);
    }

    public List<Long> queryDangkouTradeSid(DangkouSidsGetRequest request) {
        Map<String, Object> params = initParams(request.getStaff());
        params.put("startDate", request.getStartDate());
        params.put("endDate", request.getEndDate());
        params.put("page", request.getPage());
        return getSqlMapClientTemplate(request.getStaff()).queryForList("TbTrade.queryDangkouTradeSid", params);
    }

    public Long countDangkouTradeSid(DangkouSidsGetRequest request) {
        Map<String, Object> params = initParams(request.getStaff());
        params.put("startDate", request.getStartDate());
        params.put("endDate", request.getEndDate());
        return (Long) getSqlMapClientTemplate(request.getStaff()).queryForObject("TbTrade.countDangkouTradeSid", params);
    }

    public List<TbTrade> queryTradeByWaveId(Staff staff, Long warehouseId, Long waveId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("tradeDbNo", staff.getDbInfo().getTradeDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("warehouseId", warehouseId);
        params.put("waveId", waveId);
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryTradeByWaveId", params);
    }

    /**
     * 查询部分字段
     */
    public List<Trade> querySomeFieldsByWaveIds(Staff staff, String fields, List<Long> waveIds) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return new ArrayList<>();
        }
        fields = TradeQuerySomeFieldsUtils.getQueryFields(fields, TradeQuerySomeFieldsUtils.TRADE_TABLE_KEY);
        if (StringUtils.isBlank(fields)) {
            return new ArrayList<>();
        }
        Map<String, Object> condistion = initParams(staff);
        condistion.put("waveIds", waveIds);
        condistion.put("fields", fields);
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.querySomeFields", condistion);
    }


    public List<TbTrade> queryTradeByWaveIds(Staff staff, Long[] warehouseIds, Long[] waveIds) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("tradeDbNo", staff.getDbInfo().getTradeDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("warehouseIds", warehouseIds);
        params.put("waveIds", waveIds);
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryTradeByWaveIds", params);
    }

    public List<TbTrade> queryTradesFromRuntime(Staff staff, Query q, RuntimeIdSnapshot runtime, Page page) {
        StringBuilder buf = buildSnapshotQuerySql(staff, q, runtime, page);
        List<TbTrade> result = getJdbcTemplate(staff).query(buf.toString(), RowMapperContext.tbTradeRowMapper);
        if (result.isEmpty()) {
            return new ArrayList<>();
        }
        return result;
    }

    public StringBuilder buildSnapshotQuerySql(Staff staff, Query q, RuntimeIdSnapshot runtime, Page page) {
        StringBuilder buf = new StringBuilder();
        buf.append("select t.* from ");
        buf.append(q.getTradeTable()).append("_").append(staff.getDbInfo().getTradeDbNo()).append(" t");
        buf.append(" right join ");
        buf.append("(select id from runtime_id_snapshot where company_id=");
        buf.append(runtime.getCompanyId());
        buf.append(" and staff_id=").append(runtime.getStaffId());
        buf.append(" and type=").append(runtime.getType());
        buf.append(" and manualSeq >= ").append(page.getStartRow())
                .append(" order by manualSeq limit ").append(page.getPageSize());
        buf.append(") r on t.sid = r.id");
        return buf;
    }

    /**
     * 查询部分字段
     */
    public List<Trade> querySomeFieldsBySids(Staff staff, String fields, List<Long> sids, List<Long> mergeSids) {
        if (CollectionUtils.isEmpty(sids) && CollectionUtils.isEmpty(mergeSids)) {
            return new ArrayList<>();
        }
        Map<String, Object> condistion = initParams(staff);
        condistion.put("sids", sids);
        condistion.put("mergeSids", mergeSids);
        return querySomeFieldsByCondistion(staff, fields, condistion);
    }

    /**
     * 查询部分字段
     */
    public List<Trade> querySomeFieldsByCondition(Staff staff, String fields, Map<String, Object> condition) {
        Map<String, Object> params = initParams(staff);
        params.putAll(condition);
        return querySomeFieldsByCondistion(staff, fields, params);
    }

    /**
     * 根据tid查非系统订单
     * 只查部分字段
     */
    public List<Trade> queryNotSysTradeByTids(Staff staff, List<String> tids) {
        if (CollectionUtils.isEmpty(tids)) {
            return new ArrayList<>();
        }
        Map<String, Object> condistion = initParams(staff);
        condistion.put("tids", tids);
        condistion.put("excludeSource", CommonConstants.PLAT_FORM_TYPE_SYS);
        return querySomeFieldsByCondistion(staff, "sid,tid,source", condistion);
    }

    /**
     * 查询部分字段
     */
    private List<Trade> querySomeFieldsByCondistion(Staff staff, String fields, Map<String, Object> condistion) {
        if (StringUtils.isBlank(fields)) {
            return new ArrayList<>();
        }
        condistion.put("fields", fields);
        Query q = buildSomeFieldSql(staff,condistion);
        if(condistion.containsKey("ifDebug")){
            Logs.debug("querySomeFieldsByCondistion,sql:" + q);
        }

        List<TbTrade> trades = getJdbcTemplate(staff).query(q.getQ().toString(), q.getArgs().toArray(), RowMapperContext.tbTradeRowMapper);
        return TradeUtils.toTrades(trades);
    }


    private Query buildSomeFieldSql(Staff staff, Map<String, Object> condition) {

        Integer tradeDbNo = staff.getDbInfo().getTradeDbNo();
        Query q = new Query().append(" SELECT ").append(condition.getOrDefault("fields","*"));
        q.append("  FROM ").append("  trade_").append(tradeDbNo).append(" t ");
        q.append(" WHERE t.company_id = ?").add(staff.getCompanyId());

        buildListQuery(q,"t.sid", (List<Object>) condition.get("sids"));
        buildListQuery(q,"t.merge_sid", (List<Object>) condition.get("mergeSids"));
        buildListQuery(q,"t.tid", (List<Object>) condition.get("tids"));
        q.conjunct("AND t.source!=?",condition.get("excludeSource")!=null).add(condition.get("excludeSource"),condition.get("excludeSource")!=null);
        buildListQuery(q,"t.wave_id", (List<Object>) condition.get("waveIds"));
        q.conjunct(" AND t.sys_status=?",condition.get("sysStatus")!=null).add(condition.get("sysStatus"),condition.get("sysStatus")!=null);
        q.conjunct(" AND t.upd_time>=?",condition.get("updTimeStart")!=null).add(condition.get("updTimeStart"),condition.get("updTimeStart")!=null);
        q.conjunct(" AND t.upd_time<=?",condition.get("updTimeEnd")!=null).add(condition.get("updTimeEnd"),condition.get("updTimeEnd")!=null);
        q.conjunct(" AND t.created >=?",condition.get("createdStart")!=null).add(condition.get("createdStart"),condition.get("createdStart")!=null);
        q.conjunct(" AND t.created <=?",condition.get("createdEnd")!=null).add(condition.get("createdEnd"),condition.get("createdEnd")!=null);
        q.conjunct(" AND t.consign_time>=?",condition.get("consignTimeStart")!=null).add(condition.get("consignTimeStart"),condition.get("consignTimeStart")!=null);
        q.conjunct(" AND t.consign_time<=?",condition.get("consignTimeEnd")!=null).add(condition.get("consignTimeEnd"),condition.get("consignTimeEnd")!=null);
        buildListQuery(q,"t.sys_status", (List<Object>) condition.get("sysStatusList"));
        buildNotListQuery(q,"t.sys_status", (List<Object>) condition.get("excludeSysStatusList"));
        q.conjunct(" AND t.template_id=?",condition.get("templateId")!=null).add(condition.get("templateId"),condition.get("templateId")!=null);
        q.conjunct(" AND t.warehouse_id=?",condition.get("warehouseId")!=null).add(condition.get("warehouseId"),condition.get("warehouseId")!=null);
        q.conjunct(" AND t.template_type=?",condition.get("templateType")!=null).add(condition.get("templateType"),condition.get("templateType")!=null);
        q.conjunct(" AND t.out_sid != '' AND t.out_sid IS NOT NULL",condition.get("existOutSid")!=null);
        q.conjunct(" AND t.enable_status=?",condition.get("enableStatus")!=null).add(condition.get("enableStatus"),condition.get("enableStatus")!=null);
        q.conjunct(" AND t.is_cancel=?",condition.get("isCancel")!=null).add(condition.get("isCancel"),condition.get("isCancel")!=null);

        return q;
    }

    private void buildListQuery(Query q,String field,List<Object> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        String inStr = list.stream().map(t -> "?").collect(Collectors.joining(",", "(", ")"));
        q.and().append(field).append(" IN ").append(inStr).add(list);
    }

    private void buildNotListQuery(Query q,String field,List<Object> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        String inStr = list.stream().map(t -> "?").collect(Collectors.joining(",", "(", ")"));
        q.and().append(field).append(" NOT IN ").append(inStr).add(list);
    }

    public List<InvaildItem> queryExceptionItem(Staff staff, Page page,Sort sort, String key, String value, Long[] userIds, boolean isAccurate,List<Long> sids) {
        Long[] taobaoId = fliterItemMatchPlanAandBTaobaoId(staff, userIds);
        StringBuilder sql = new StringBuilder();
        String[] args = buildQueryExceptionItem(staff, page,sort, key, value, taobaoId,userIds, isAccurate, sql, false,sids);
        List<InvaildItem> list = getJdbcTemplate(staff).query(sql.toString(), InvaildItemRowMapper.instance, args);
        if (list.isEmpty()) {
            return list;
        }
        list = fillUserInfo(staff, list);
        return list;
    }

    public Long queryExceptionItemCount(Staff staff, String key, String value, Long[] userIds, boolean isAccurate,List<Long> sids) {
        Long[] taobaoId = fliterItemMatchPlanAandBTaobaoId(staff, userIds);
        StringBuilder sql = new StringBuilder();
        String[] args = buildQueryExceptionItem(staff, null,null ,key, value, taobaoId,userIds, isAccurate, sql, true,sids);
        return getJdbcTemplate(staff).queryForObject(sql.toString(), Long.class, args);
    }

    private Long[] fliterItemMatchPlanAandBTaobaoId(Staff staff, Long[] userIds) {
        Long[] itemSpecialFilterUserIds;
        Map<Long, User> userIdMap = staff.getUserIdMap();
        if (userIds != null) {
            itemSpecialFilterUserIds = Arrays.stream(userIds).filter(x -> {
                User userTemp = staff.getUserByUserId(x);
                if (userTemp == null) {
                    return false;
                }
                return userTemp.getItemSpecial() == 2 || userTemp.getItemSpecial() == 3;
            }).toArray(Long[]::new);
        } else {
            itemSpecialFilterUserIds = userIdMap.values().stream().filter(user -> {
                if (user == null) {
                    return false;
                }
                return user.getItemSpecial() == 2 || user.getItemSpecial() == 3;
            }).map(User::getId).toArray(Long[]::new);
        }
        if (itemSpecialFilterUserIds.length == 0) {
            throw new IllegalArgumentException("无效商品匹配暂不支持A，B方案");
        }

        ArrayList<Long> result = new ArrayList<>();
        for (Long userId : itemSpecialFilterUserIds) {
            result.add(userIdMap.get(userId).getTaobaoId());
        }
        return result.toArray(new Long[]{});
    }

    @VisibleForTesting
    protected List<InvaildItem> fillUserInfo(Staff staff, List<InvaildItem> list) {
        Map<Long, User> userIdMap = staff.getUserIdMap();
        List<InvaildItem> result = new ArrayList<>();
        QueryLogBuilder builder = new QueryLogBuilder(staff).append("填充无效商品对应店铺信息 无效店铺:").startArray().startWatch();
        for (InvaildItem invaildItem : list) {
            Assert.notNull(invaildItem.getUserId(), "sid:" + invaildItem.getSid() + ",order表的userId为空！");
            User tempUser = userIdMap.get(invaildItem.getUserId());
            if (tempUser == null) {
                builder.startObject().append("skuOuterId",invaildItem.getSkuOuterId()).append("userId",invaildItem.getUserId()).endObject();
            }else {
                invaildItem.setUserId(tempUser.getId());
                invaildItem.setSubSource(tempUser.getSubSource());
                result.add(invaildItem);
            }
        }
        if (builder.isChanged()) {
            builder.endArray().printDebug(logger);
        }
        return result;
    }

    @VisibleForTesting
    protected String[] buildQueryExceptionItem(Staff staff, Page page,Sort sort, String key, String value, Long[] taobaoIds,Long[] userIds,boolean isAccurate, StringBuilder sql, boolean queryCount,List<Long> sids) {
        if (queryCount) {
            sql.append("SELECT count(*) count from (SELECT 1 FROM order_not_consign_");
        } else {
            sql.append("SELECT o.sid, o.title, o.sys_title, o.taobao_id, o.source, o.belong_sid, o.num_iid, o.sku_id, o.outer_iid, o.pic_path, o.sku_properties_name, o.sys_sku_properties_name, o.sku_sys_id, o.outer_sku_id,sum(o.num) as num,t.user_id  FROM order_not_consign_");
        }
        sql.append(staff.getDbInfo().getOrderDbNo()).append(" o join trade_not_consign_").append(staff.getDbInfo().getTradeDbNo()).append(" t on t.sid = o.sid");

        if (userIds != null) {
            sql.append(" and t.user_id in (").append(StringUtils.join(userIds, ",")).append(") ");
        }

        sql.append(" WHERE o.company_id = ").append(staff.getCompanyId()).append(" and (o.stock_status = 'UNALLOCATED') AND o.sys_status in ('WAIT_BUYER_PAY', 'WAIT_AUDIT', 'WAIT_FINANCE_AUDIT', 'WAIT_MANUAL_AUDIT') and o.item_sys_id < 0 ")
                .append(" and o.is_cancel =0").append(" and o.enable_status != 0");
        String[] args = null;
        if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
            if ("sku_id".equals(key)) {
                args = new String[]{value.trim(), value.trim()};
                if (isAccurate) {
                    sql.append(" and (o.sku_id = ? OR o.num_iid = ?)");
                } else {
                    sql.append(" and (o.sku_id LIKE CONCAT('%', ?, '%') OR o.num_iid LIKE CONCAT('%', ?, '%'))");
                }
            } else if ("outer_iid".equals(key)) {
                args = new String[]{value.trim(), value.trim()};
                if (isAccurate) {
                    sql.append(" and (o.outer_iid = ? OR o.outer_sku_id = ?)");
                } else {
                    sql.append(" and (o.outer_iid LIKE CONCAT('%', ?, '%') OR o.outer_sku_id LIKE CONCAT('%', ?, '%'))");
                }
            } else {
                args = new String[]{value.trim()};
                sql.append(" and o.").append(key);
                if (isAccurate) {
                    sql.append(" = ?");
                } else {
                    sql.append(" LIKE CONCAT('%', ?, '%')");
                }
            }
        }
        sql.append(" and o.taobao_id in (").append(StringUtils.join(taobaoIds, ",")).append(") ");
        if (sids != null) {
            sql.append(" and o.belong_sid in (").append(StringUtils.join(sids, ",")).append(") ");
        }

        sql.append(" GROUP BY o.source, t.user_id, o.num_iid, o.sku_id");
        if (queryCount) {
            sql.append(" )a ");
            return args;
        }

        if (sort != null && StringUtils.isNotBlank(sort.getField())) {
            sql.append(" order by ").append(sort.getField()).append(" ").append(StringUtils.isBlank(sort.getOrder())?"desc":sort.getOrder());
        }

        if (page == null) {
            page = new Page();
        }
        sql.append(" limit ").append((page.getPageNo() - 1) * page.getPageSize()).append(",").append(page.getPageSize());
        return args;
    }


    public List<InvaildItemTrade> queryInvaildItemTrade(Staff staff, Page page, String source, String numIid, String skuId) {
        StringBuilder sql = new StringBuilder();

        buildQueryInvaildItemSid(staff, page, source, numIid, skuId, sql, false);
        ArrayList<Long> sidArray = new ArrayList<>();
        ArrayList<Long> numArray = new ArrayList<>();
        getJdbcTemplate(staff).query(sql.toString(), resultSet -> {
            sidArray.add(resultSet.getLong("sid"));
            numArray.add(resultSet.getLong("num"));
        });
        if (sidArray.isEmpty()) {
            return Lists.newArrayList();
        }
        // 去重
        List<InvaildItemTrade> list = getJdbcTemplate(staff).query(buildQueryInvalidItemTradeSql(staff, new HashSet<>(sidArray)), InvaildItemTradeRowMapper.instance);

        Map<Long, InvaildItemTrade> sidToInvaildItem = list.stream().collect(Collectors.toMap(InvaildItemTrade::getSid, x -> x));
        LinkedList<InvaildItemTrade> result = new LinkedList<>();
        for (int i = 0; i < sidArray.size(); i++) {
            InvaildItemTrade invaildItemTrade = sidToInvaildItem.get(sidArray.get(i));
            if (invaildItemTrade == null) {
                Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append(String.format("queryInvaildItemTrade[companyId=%s,sid=%s]", staff.getCompanyId(), sidArray.get(i))));
                continue;
            }
            result.add(invaildItemTrade.copyIfItemNumExist(numArray.get(i)));
        }

        return result;
    }

    public Long queryInvaildItemTradeCount(Staff staff, String source, String numIid, String skuId) {
        StringBuilder sql = new StringBuilder();
        buildQueryInvaildItemSid(staff, null, source, numIid, skuId, sql, true);

        return getJdbcTemplate(staff).queryForObject(sql.toString(), Long.class);
    }

    @VisibleForTesting
    protected String buildQueryInvalidItemTradeSql(Staff staff, Set<Long> querySid) {
        String sids = StringUtils.join(querySid, ",");
        StringBuilder sb = new StringBuilder();
        sb.append("select t.sid,t.short_id,t.tid,t.sys_status,t.insufficient_num,t.insufficient_rate,t.item_kind_num from trade_not_consign_").append(staff.getDbInfo().getTradeDbNo()).append(" t where t.sid in (").append(sids).append(")");
        return sb.toString();
    }

    @VisibleForTesting
    protected void buildQueryInvaildItemSid(Staff staff, Page page, String source, String numIid, String skuId, StringBuilder sql, boolean queryCount) {
        if (queryCount) {
            sql.append("select count(1) from order_not_consign_");
        } else {
            sql.append("select o.sid,o.num from order_not_consign_");
        }
        sql.append(staff.getDbInfo().getOrderDbNo()).append(" o where o.stock_status ='UNALLOCATED' and o.sys_status in ('WAIT_BUYER_PAY','WAIT_AUDIT','WAIT_FINANCE_AUDIT','WAIT_MANUAL_AUDIT') and o.item_sys_id < 0 and o.company_id = ").append(staff.getCompanyId())
                .append(" and o.is_cancel =0").append(" and o.enable_status != 0");
        //条件
        if (StringUtils.isNotBlank(source)) {
            sql.append(" and o.source= '").append(source).append("'");
        }
        if (StringUtils.isNotBlank(numIid)) {
            sql.append(" and o.num_iid= '").append(numIid).append("'");
        }
        if (StringUtils.isNotBlank(skuId)) {
            sql.append(" and o.sku_id= '").append(skuId).append("'");
        }
        if (queryCount) {
            return;
        }
        sql.append(" order by o.num desc");
        if (page != null) {
            sql.append(" limit ").append((page.getPageNo() - 1) * page.getPageSize()).append(",").append(page.getPageSize());
        }
    }


    public Long queryTradeSqlInsertTask(Staff staff, Query q, TradeQueryParams params) {
        TradeExcelExportTask tradeExcelExportTask = tradeExcelExportTaskDao.queryByTaskId(staff, params.getContext().getTaskId());
        tradeExcelExportTask.setExportStatus(TradeExcelExportTask.TASK_READY);
        String taskParams = tradeExcelExportTask.getTaskParams();
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isNotBlank(taskParams)){
            jsonObject = JSONObject.parseObject(taskParams);
        }
        jsonObject.put(TradeExcelExportTask.EXPORT_OPEN_PGL, q.allowPgl() && !params.getContext().hasTradeExtSearch());
        tradeExcelExportTask.setTaskParams(jsonObject.toJSONString());
        StringBuilder sql;
        if (jsonObject.containsKey(TradeExcelExportTask.OPEN_EXPORT_NEW_SQL)){
            JSONObject sqlObject = new JSONObject();
            Page page = new Page(1, TradeExportDiamondUtils.getExportLimit(staff).intValue());
            sql = buildTradeQuerySql(staff, "DISTINCT(t.sid)", q, page, (params.getContext().hasItemQuery() || params.getContext().hasOrderCaculateQuery()), params.isUseNewQuery() != null && params.isUseNewQuery() && !params.getContext().isUniqueQuery(), params.getContext().hasTradeExtSearch(), params.getContext().hasConsignRecordQuery(), true);
            q.add(page.getStartRow()).add(page.getPageSize());
            sqlObject.put("sql", sql.toString());
            List<Object> args = new ArrayList<>();
            for (Object arg : q.getArgs()){
                if (arg instanceof Date){
                    args.add(DateUtils.datetime2Str((Date) arg));
                }else {
                    args.add(arg);
                }
            }
            sqlObject.put("params", args);
            tradeExcelExportTask.setFileDownloadSql(sqlObject.toJSONString());
        }else {
            sql = buildTaskTradeQuerySql(staff, q, (params.getContext().hasItemQuery() || params.getContext().hasOrderCaculateQuery()), params.isUseNewQuery() != null && params.isUseNewQuery() && !params.getContext().isUniqueQuery(), params.getContext().hasTradeExtSearch(), params.getContext().hasConsignRecordQuery());
            tradeExcelExportTask.setFileDownloadSql(sql.toString());
        }
        tradeExcelExportTaskDao.update(staff, tradeExcelExportTask);
        Logs.ifDebug(DaoLogUtils.buildLogHead(staff).append(String.format("[sid快照建立查询模版 table=%s] sql: %s; sort: %s;", q.getTradeTable(), sql, q.getSort())));
        return 0L;
    }


    public List<Long> queryTradeSidList(Staff staff, String sql, TradeExcelExportTask task) {
        JdbcTemplate template;
        if (null != task && task.export3Month()) {
            template = getJdbcTemplate(staff.getReportDbNo());
        } else {
            template = getJdbcTemplate(staff.getDbNo());
        }
        int timeout = template.getQueryTimeout();
        template.setQueryTimeout(600);
        try {
            if (null != task && task.openExportNewSQL()){
                JSONObject sqlObject = JSONObject.parseObject(task.getFileDownloadSql());
                return template.queryForList((String) sqlObject.get("sql"), sqlObject.getJSONArray("params").toArray(), Long.class);
            }else {
                return template.queryForList(sql, Long.class);
            }
        } catch (Exception e) {
            Logs.error(DaoLogUtils.buildLogHead(staff).append(String.format("查询数据出错, companyId=%s, sql=%s", staff.getCompanyId(), sql)));
            return new ArrayList<>();
        } finally {
            template.setQueryTimeout(timeout);
        }

    }

    public List<TbTrade> queryTradeByAfterSales(Staff staff, TradeTypeEnum tradeType, String... tids) {
        if (null == tids || tids.length <= 0) {
            return null;
        }

        StringBuilder sb = new StringBuilder("select t.sid, t.tid, t.sys_status, t.type from trade_")
                .append(staff.getDbInfo().getTradeDbNo()).append(" t where t.company_id = ").append(staff.getCompanyId())
                .append(" AND (t.enable_status = 1 OR t.enable_status = 2)").append(" and (");

        for (int i = 0; i < tids.length; i++) {
            sb.append("t.tid like '").append(tids[i]).append("-%'");
            if (i == tids.length - 1) {
                sb.append(")");
                break;
            }
            sb.append(" or ");
        }

        if (null == tradeType) {
            sb.append(" and (t.type = 'changeitem' or t.type = 'reissue')");
        }
        //换货订单
        if (TradeTypeEnum.NEED_CHANGE_TRADE.equals(tradeType)) {
            sb.append(" and t.type = 'changeitem'");
        }
        //补发订单
        if (TradeTypeEnum.REISSUE_TRADE.equals(tradeType)) {
            sb.append(" and t.type = 'reissue'");
        }

        sb.append(" LIMIT 1000");
        try {
            return getJdbcTemplate(staff).query(sb.toString(), RowMapperContext.tbTradeRowMapper);
        } catch (Exception e) {
            Logs.error(DaoLogUtils.buildLogHead(staff).append(String.format("查询数据出错, companyId=%s, sql=%s", staff.getCompanyId(), sb)), e);
        }
        return null;
    }

    public List<Long> queryTradesByWarningSubscribe(Staff staff, String startDate, String endDate, Integer pageNo, Integer pageSize) {
        Map<String, Object> params = initParams(staff);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        if (pageNo != null && pageSize != null) {
            params.put("page", new Page(pageNo, pageSize));
        }
        return getSqlMapClientTemplate(staff).queryForList("TbTrade.queryTradesByWarningSubscribe", params);
    }

    public Long countTradesByWarningSubscribe(Staff staff, String startDate, String endDate) {
        Map<String, Object> params = initParams(staff);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        return (Long) getSqlMapClientTemplate(staff).queryForObject("TbTrade.countTradesByWarningSubscribe", params);
    }

    /**
     * 查询部分字段 走JDBC
     */
    public List<TbTrade> querySomeFieldsJDBC(Staff staff, String fields, List<Long> sids, List<Long> mergeSids) {
        if (CollectionUtils.isEmpty(sids) && CollectionUtils.isEmpty(mergeSids)) {
            return new ArrayList<>();
        }
        if (StringUtils.isBlank(fields)) {
            return new ArrayList<>();
        }
        StringBuilder sqlStr = buildSql(staff, fields, sids, mergeSids);

        List<TbTrade> trades = getJdbcTemplate(staff).query(sqlStr.toString(), RowMapperContext.tbTradeRowMapper);
        Logs.debug("查询到订单:" + trades.size());
        return trades;
    }


    public StringBuilder buildSql(Staff staff, String fields, List<Long> sids, List<Long> mergeSids) {
        Long companyId = staff.getCompanyId();
        Integer tradeDbNo = staff.getDbInfo().getTradeDbNo();
        StringBuilder sqlStr = new StringBuilder(" /* FORCE_MASTER */ select ");
        sqlStr.append(fields).append(" from trade_").append(tradeDbNo).append(" where company_id = ").append(companyId).append(" and enable_status != 0");
        if (CollectionUtils.isNotEmpty(sids)) {
            fillArgs(sqlStr, sids, "sid");
        } else {
            fillArgs(sqlStr, mergeSids, "merge_sid");
        }
        Logs.debug("完整sql:" + sqlStr);
        return sqlStr;
    }

    public void fillArgs(StringBuilder sqlStr, List<Long> ids, String key) {
        sqlStr.append(" and ").append(key).append(" in (");
        int i = 0;
        for (Long id : ids) {
            if (++i < ids.size()) {
                sqlStr.append(id).append(",");
            } else {
                sqlStr.append(id).append(")");
            }
        }
    }


    /**
     * 此方法只会返回sid
     */
    public List<TbTrade> queryByLikeTids(Staff staff, List<String> tids) {
        if (CollectionUtils.isEmpty(tids)) {
            return new ArrayList<>();
        }
        StringBuilder sqlStr = new StringBuilder(" /* FORCE_MASTER */ select t.sid from trade_").append(staff.getDbInfo().getTradeDbNo()).append(" t where t.company_id = ").append(staff.getCompanyId());
        StringBuilder tempSql = new StringBuilder();
        for (int i = 0; i < tids.size(); i++) {
            String tid = tids.get(i);
            tempSql.append(" t.tid like '").append(tid).append("%'");
            if (i != tids.size() - 1) {
                tempSql.append(" OR ");
            }
        }
        sqlStr.append(" and (").append(tempSql).append(")");
        return getJdbcTemplate(staff).query(sqlStr.toString(), RowMapperContext.tbTradeRowMapper);
    }
}



package com.raycloud.dmj.dao.trade.audit;

import com.raycloud.dmj.dao.trade.update.UpdateFieldEnum;
import com.raycloud.dmj.dao.trade.update.builder.AbstractTradeSqlBuilder;

import static com.raycloud.dmj.dao.trade.update.UpdateFieldEnum.*;

/**
 * UnAuditTradeSqlBuilder
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class UnAuditTradeSqlBuilder extends AbstractTradeSqlBuilder {

    private static final UpdateFieldEnum[] FIELDS = {
            tradeSysStatus,
            tradeAuditTime,
            tradeIsAutoAudit,
            tradeSysOuterId,
            tradeIsExcep,
            tradeExcep,
            tradeExceptIds,
            tradeWarehouseId,
            tradeInsufficientNum,
            tradeInsufficientRate,
            tradeMergeSid,
            tradeBelongType,
            tradeStockStatus,
            tradeConvertType,
            tradeSourceId,
            tradeDestId,
            tradeCost,
            tradeTagIds,
            tradeV,
            tradeOutSid,
            tradeDeliverPrintTime,
            tradeExpressPrintTime,
            tradeAssemblyPrintTime,
            tradeIsPackage,
            tradeIsWeigh,
    };

    @Override
    protected UpdateFieldEnum[] getUpdateFields() {
        return FIELDS;
    }
}

package com.raycloud.dmj.dao.trade.update;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.dao.DaoLogUtils;
import com.raycloud.dmj.dao.trade.update.builder.BatchSqlBuilder;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.erp.db.router.jdbc.JdbcTemplateAdapter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName AuditBatchDao
 * @Description 审核dao批量操作
 * <AUTHOR>
 * @Date 2023/12/11
 * @Version 1.0
 */

@Service
public class BatchUpdateDao {

    private final Logger LOGGER = Logger.getLogger(BatchUpdateDao.class);

    private static final int BATCH_UPDATE_SIZE = 1000;

    @Resource
    private JdbcTemplateAdapter jdbcTemplateAdapter;


    public void batchUpdateOrder(Staff staff, List<Order> orders, BatchSqlBuilder<Order> builder) {
        batchUpdate(orders, data -> {
            // 大表
            batchUpdate0(staff, data, "order_" + staff.getDbInfo().getOrderDbNo(), builder);
            // 小表
            batchUpdate0(staff, data, "order_not_consign_" + staff.getDbInfo().getOrderDbNo(), builder);
        });
    }

    public void batchUpdateTrade(Staff staff, List<Trade> trades, BatchSqlBuilder<Trade> builder) {
        batchUpdate(trades, data -> {
            // 大表
            batchUpdate0(staff, data, "trade_" + staff.getDbInfo().getTradeDbNo(), builder);
            // 小表
            batchUpdate0(staff, data, "trade_not_consign_" + staff.getDbInfo().getTradeDbNo(), builder);
        });
    }

    public void batchUpdateTradeExt(Staff staff, List<TradeExt> tradeExtList, BatchSqlBuilder<TradeExt> builder) {
        batchUpdate(staff, tradeExtList, "trade_ext_" + staff.getDbInfo().getTradeDbNo(), builder);
    }

    public <T> void batchUpdate(Staff staff, List<T> data, String tableName, BatchSqlBuilder<T> builder) {
        batchUpdate(data, d -> batchUpdate0(staff, d, tableName, builder));
    }


    private <T> void batchUpdate(List<T> data, Consumer<List<T>> consumer) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        if (data.size() <= BATCH_UPDATE_SIZE * 1.5) {
            consumer.accept(data);
            return;
        }
        for (List<T> partData : Lists.partition(data, BATCH_UPDATE_SIZE)) {
            consumer.accept(partData);
        }
    }

    private <T> void batchUpdate0(Staff staff, List<T> data, String tableName, BatchSqlBuilder<T> builder) {
        DaoBatchData<T> batchData = new DaoBatchData<>(data);
        batchData.setTableName(tableName);
        builder.buildBatchUpdateSql(staff, batchData);
        if (StringUtils.isEmpty(batchData.getBatchSql())) {
            LOGGER.warn(DaoLogUtils.buildLogHead(staff).append(String.format("更新内容为空. table:%s, builder:%s, content:%s", tableName, builder.getClass().getSimpleName(), JSONObject.toJSONString(Optional.ofNullable(data).orElse(Collections.emptyList())))));
            return;
        }
        // 走这里理论上都仅能通过id进行更新
        //noinspection SqlSourceToSinkFlow
        jdbcTemplateAdapter.get(staff).update(batchData.getBatchSql());
    }
}

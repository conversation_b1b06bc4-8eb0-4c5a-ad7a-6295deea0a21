<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="Wave">
    <typeAlias alias="Wave" type="com.raycloud.dmj.domain.wave.Wave"/>
    <typeAlias alias="WavePickerInfo" type="com.raycloud.dmj.domain.wave.WavePickerInfo"/>

    <resultMap id="baseWaveMap" class="Wave">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="code" column="id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="condition" column="condition"/>
        <result property="expressTemplateId" column="express_template_id"/>
        <result property="expressTemplateType" column="express_template_type"/>
        <result property="status" column="status"/>
        <result property="distributionStatus" column="distribution_status"/>
        <result property="pickingType" column="picking_type"/>
        <result property="created" column="created"/>
        <result property="outsidStatus" column="outsid_status"/>
        <result property="tradesCount" column="trades_count"/>
        <result property="itemCount" column="item_count"/>
        <result property="sectionAreas" column="section_areas"/>
        <result property="isConsignInAdvance" column="is_consign_in_advance"/>
        <result property="positionNoType" column="position_no_type"/>
        <result property="assignPicker" column="assign_picker"/>
        <result property="assignPickerName" column="assign_picker_name"/>
        <result property="assignSorter" column="assign_sorter"/>
        <result property="assignSorterName" column="assign_sorter_name"/>
        <result property="itemKindNum" column="item_kind_num"/>
        <result property="pickingRoute" column="picking_route"/>
        <result property="customerName" column="customer_name"/>
        <result property="finished" column="finished"/>
        <result property="fixedItem" column="fixed_item"/>
        <result property="appointStockRegionIds" column="appoint_stock_region_ids"/>
        <result property="tagIds" column="tag_ids"/>
        <result property="subSectionPick" column="sub_section_pick"/>
        <result property="shortId" column="short_id"/>
        <result property="removedCount" column="removed_count"/>
        <result property="pickedRemovedCount" column="picked_removed_count"/>
        <result property="boxAllocate" column="box_allocate"/>
        <result property="stockRegionZoneId" column="stock_region_zone_id"/>
        <result property="pickEndAutoConsign" column="pick_end_auto_consign"/>
        <result property="ruleType" column="rule_type"/>
        <result property="combineItemSingle" column="combine_item_single"/>
        <result property="expressCompanyId" column="express_company_id"/>
        <result property="pickStyle" column="pick_style"/>
        <result property="pickListPrintStatus" column="pick_list_print_status"/>
        <result property="logisticsCompanyId" column="logistics_company_id"/>
        <result property="seedStartTime" column="seed_start_time"/>
        <result property="seedEndTime" column="seed_end_time"/>
        <result property="pickOrderConfig" column="pick_order_config"/>
        <result property="understockedGroup" column="understocked_group"/>
        <result property="shipperIds" column="shipper_ids"/>
        <result property="pickingCartTypeId" column="picking_cart_type_id"/>
    </resultMap>

    <resultMap id="waveMap" class="Wave">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="code" column="id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="condition" column="condition"/>
        <result property="expressTemplateId" column="express_template_id"/>
        <result property="expressTemplateType" column="express_template_type"/>
        <result property="status" column="status"/>
        <result property="distributionStatus" column="distribution_status"/>
        <result property="pickingType" column="picking_type"/>
        <result property="pickingCode" column="picking_code"/>
        <result property="pickingId" column="picking_id"/>
        <result property="created" column="created"/>
        <result property="outsidStatus" column="outsid_status"/>
        <result property="tradesCount" column="trades_count"/>
        <result property="itemCount" column="item_count"/>
        <result property="pickerName" column="picker_name"/>
        <result property="pickStartTime" column="pick_start_time"/>
        <result property="pickEndTime" column="pick_end_time"/>
        <result property="checkGoodsFinished" column="check_goods_finished"/>
        <result property="sectionAreas" column="section_areas"/>
        <result property="isConsignInAdvance" column="is_consign_in_advance"/>
        <result property="positionNoType" column="position_no_type"/>
        <result property="sorterName" column="sorter_name"/>
        <result property="assignPicker" column="assign_picker"/>
        <result property="assignPickerName" column="assign_picker_name"/>
        <result property="assignSorter" column="assign_sorter"/>
        <result property="assignSorterName" column="assign_sorter_name"/>
        <result property="itemKindNum" column="item_kind_num"/>
        <result property="pickingRoute" column="picking_route"/>
        <result property="finished" column="finished"/>
        <result property="fixedItem" column="fixed_item"/>
        <result property="tagIds" column="tag_ids"/>
        <result property="shortId" column="short_id"/>
        <result property="removedCount" column="removed_count"/>
        <result property="pickedRemovedCount" column="picked_removed_count"/>
        <result property="boxAllocate" column="box_allocate"/>
        <result property="stockRegionZoneId" column="stock_region_zone_id"/>
        <result property="combineItemSingle" column="combine_item_single"/>
        <result property="expressCompanyId" column="express_company_id"/>

    </resultMap>

    <resultMap id="waveStatisticsMap" class="Wave">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="code" column="id"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="ruleType" column="rule_type"/>
        <result property="condition" column="condition"/>
        <result property="expressTemplateId" column="express_template_id"/>
        <result property="expressTemplateType" column="express_template_type"/>
        <result property="status" column="status"/>
        <result property="distributionStatus" column="distribution_status"/>
        <result property="pickingType" column="picking_type"/>
        <result property="pickingCode" column="picking_code"/>
        <result property="pickingId" column="picking_id"/>
        <result property="created" column="created"/>
        <result property="outsidStatus" column="outsid_status"/>
        <result property="tradesCount" column="trades_count"/>
        <result property="itemCount" column="item_count"/>
        <result property="pickerName" column="picker_name"/>
        <result property="pickerId" column="picker_id"/>
        <result property="pickStartTime" column="pick_start_time"/>
        <result property="pickEndTime" column="pick_end_time"/>
        <result property="checkGoodsFinished" column="check_goods_finished"/>
        <result property="sectionAreas" column="section_areas"/>
        <result property="isConsignInAdvance" column="is_consign_in_advance"/>
        <result property="positionNoType" column="position_no_type"/>
        <result property="sorterName" column="sorter_name"/>
        <result property="assignPicker" column="assign_picker"/>
        <result property="assignPickerName" column="assign_picker_name"/>
        <result property="assignSorter" column="assign_sorter"/>
        <result property="assignSorterName" column="assign_sorter_name"/>
        <result property="itemKindNum" column="item_kind_num"/>
        <result property="planPickNum" column="plan_pick_num"/>
        <result property="pickedNum" column="picked_num"/>
        <result property="unPickNum" column="unpick_num"/>
        <result property="shortageNum" column="shortage_num"/>
        <result property="pickingRoute" column="picking_route"/>
        <result property="ruleGroupId" column="wave_rule_group_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="finished" column="finished"/>
        <result property="tagIds" column="tag_ids"/>
        <result property="pickStyle" column="pick_style"/>
        <result property="pickListPrintStatus" column="pick_list_print_status"/>
        <result property="subSectionPick" column="sub_section_pick"/>
        <result property="subSectionPickerIds" column="sub_section_picker_ids"/>
        <result property="shortId" column="short_id"/>
        <result property="removedCount" column="removed_count"/>
        <result property="pickedRemovedCount" column="picked_removed_count"/>
        <result property="boxAllocate" column="box_allocate"/>
        <result property="stockRegionZoneId" column="stock_region_zone_id"/>
        <result property="shopUserId" column="shop_user_id"/>
        <result property="unPackNum" column="un_pack_num"/>
        <result property="combineItemSingle" column="combine_item_single"/>
        <result property="splitFlag" column="split_flag"/>
        <result property="cooperationNo" column="cooperation_no"/>
        <result property="sellSite" column="sell_site"/>
        <result property="expressCompanyId" column="express_company_id"/>
        <result property="logisticsCompanyId" column="logistics_company_id"/>
        <result property="creatorId" column="creator_id"/>
        <result property="creatorName" column="creator_name"/>
        <result property="seedStartTime" column="seed_start_time"/>
        <result property="seedEndTime" column="seed_end_time"/>
        <result property="pickOrderConfig" column="pick_order_config"/>
        <result property="understockedGroup" column="understocked_group"/>
        <result property="shipperIds" column="shipper_ids"/>
        <result property="assignPickerRoles" column="assign_picker_roles"/>
        <result property="pickAfterStatus" column="pick_after_status"/>
        <result property="pickingCartTypeId" column="picking_cart_type_id"/>
    </resultMap>

    <resultMap id="WavePickerInfo" class="WavePickerInfo">
        <result property="id" column="id"></result>
        <result property="pickerId" column="picker_id"></result>
        <result property="assignPicker" column="assign_picker"></result>
    </resultMap>

    <resultMap id="WaveTagIds" class="Wave">
        <result property="id" column="id"></result>
        <result property="tagIds" column="tag_ids"></result>
    </resultMap>

    <insert id="insert" >
        insert into wave_#dbNo#(company_id,warehouse_id,rule_id,`condition`
        <isNotNull property="code" prepend=",">code</isNotNull>
        <isNotNull property="pickingType" prepend=",">picking_type</isNotNull>
        <isNotNull property="waveType" prepend=",">wave_type</isNotNull>
        <isNotNull property="expressTemplateId" prepend=",">express_template_id</isNotNull>
        <isNotNull property="expressTemplateType" prepend=",">express_template_type</isNotNull>
        <isNotNull property="outsidStatus" prepend=",">outsid_status</isNotNull>
        <isNotNull property="tradesCount" prepend=",">trades_count</isNotNull>
        <isNotNull property="itemCount" prepend=",">item_count</isNotNull>
        <isNotNull property="sectionAreas" prepend=",">section_areas</isNotNull>
        <isNotNull property="positionNoType" prepend=",">position_no_type</isNotNull>
        <isNotNull property="assignPicker" prepend=",">assign_picker</isNotNull>
        <isNotNull property="assignPickerName" prepend=",">assign_picker_name</isNotNull>
        <isNotNull property="assignSorter" prepend=",">assign_sorter</isNotNull>
        <isNotNull property="assignSorterName" prepend=",">assign_sorter_name</isNotNull>
        <isNotNull property="itemKindNum" prepend=",">item_kind_num</isNotNull>
        <isNotNull property="customerId" prepend=",">customer_id</isNotNull>
        <isNotNull property="customerName" prepend=",">customer_name</isNotNull>
        <isNotNull property="pickingRoute" prepend=",">picking_route</isNotNull>
        <isNotNull property="finished" prepend=",">finished</isNotNull>
        <isNotNull property="fixedItem" prepend=",">fixed_item</isNotNull>
        <isNotNull property="appointStockRegionIds" prepend=",">appoint_stock_region_ids</isNotNull>
        <isNotNull property="tagIds" prepend=",">tag_ids</isNotNull>
        <isNotNull property="shortId" prepend=",">short_id</isNotNull>
        <isNotNull property="pickEndAutoConsign" prepend=",">pick_end_auto_consign</isNotNull>
        <isNotNull property="shopUserId" prepend=",">shop_user_id</isNotNull>
        <isNotNull property="subSectionPick" prepend=",">sub_section_pick</isNotNull>
        <isNotNull property="boxAllocate" prepend=",">box_allocate</isNotNull>
        <isNotNull property="stockRegionZoneId" prepend=",">stock_region_zone_id</isNotNull>
        <isNotNull property="combineItemSingle" prepend=",">combine_item_single</isNotNull>
        <isNotNull property="cooperationNo" prepend=",">cooperation_no</isNotNull>
        <isNotNull property="sellSite" prepend=",">sell_site</isNotNull>
        <isNotNull property="itemInfo" prepend=",">item_info</isNotNull>
        <isNotNull property="expressCompanyId" prepend=",">express_company_id</isNotNull>
        <isNotNull property="pickStyle" prepend=","> pick_style </isNotNull>
        <isNotNull property="logisticsCompanyId" prepend=",">logistics_company_id</isNotNull>
        <isNotNull property="pickListPrintStatus" prepend=","> pick_list_print_status </isNotNull>
        <isNotNull property="shipperIds" prepend=","> shipper_ids </isNotNull>
        <isNotNull property="creatorId" prepend=","> creator_id </isNotNull>
        <isNotNull property="creatorName" prepend=","> creator_name </isNotNull>
        <isNotNull property="pickOrderConfig" prepend=","> pick_order_config </isNotNull>
        <isNotNull property="understockedGroup" prepend=","> understocked_group </isNotNull>
        <isNotNull property="assignPickerRoles" prepend=","> assign_picker_roles </isNotNull>
        <isNotNull property="pickingCartTypeId" prepend=","> picking_cart_type_id </isNotNull>
        ,status,created)
        values(#companyId#,#warehouseId#,#ruleId#,#condition#
        <isNotNull property="code" prepend=",">#code#</isNotNull>
        <isNotNull property="pickingType" prepend=",">#pickingType#</isNotNull>
        <isNotNull property="waveType" prepend=",">#waveType#</isNotNull>
        <isNotNull property="expressTemplateId" prepend=",">#expressTemplateId#</isNotNull>
        <isNotNull property="expressTemplateType" prepend=",">#expressTemplateType#</isNotNull>
        <isNotNull property="outsidStatus" prepend=",">#outsidStatus#</isNotNull>
        <isNotNull property="tradesCount" prepend=",">#tradesCount#</isNotNull>
        <isNotNull property="itemCount" prepend=",">#itemCount#</isNotNull>
        <isNotNull property="sectionAreas" prepend=",">#sectionAreas#</isNotNull>
        <isNotNull property="positionNoType" prepend=",">#positionNoType#</isNotNull>
        <isNotNull property="assignPicker" prepend=",">#assignPicker#</isNotNull>
        <isNotNull property="assignPickerName" prepend=",">#assignPickerName#</isNotNull>
        <isNotNull property="assignSorter" prepend=",">#assignSorter#</isNotNull>
        <isNotNull property="assignSorterName" prepend=",">#assignSorterName#</isNotNull>
        <isNotNull property="itemKindNum" prepend=",">#itemKindNum#</isNotNull>
        <isNotNull property="customerId" prepend=",">#customerId#</isNotNull>
        <isNotNull property="customerName" prepend=",">#customerName#</isNotNull>
        <isNotNull property="pickingRoute" prepend=",">#pickingRoute#</isNotNull>
        <isNotNull property="finished" prepend=",">#finished#</isNotNull>
        <isNotNull property="fixedItem" prepend=",">#fixedItem#</isNotNull>
        <isNotNull property="appointStockRegionIds" prepend=",">#appointStockRegionIds#</isNotNull>
        <isNotNull property="tagIds" prepend=",">#tagIds#</isNotNull>
        <isNotNull property="shortId" prepend=",">#shortId#</isNotNull>
        <isNotNull property="pickEndAutoConsign" prepend=",">#pickEndAutoConsign#</isNotNull>
        <isNotNull property="shopUserId" prepend=",">#shopUserId#</isNotNull>
        <isNotNull property="subSectionPick" prepend=",">#subSectionPick#</isNotNull>
        <isNotNull property="boxAllocate" prepend=",">#boxAllocate#</isNotNull>
        <isNotNull property="stockRegionZoneId" prepend=",">#stockRegionZoneId#</isNotNull>
        <isNotNull property="combineItemSingle" prepend=",">#combineItemSingle#</isNotNull>
        <isNotNull property="cooperationNo" prepend=",">#cooperationNo#</isNotNull>
        <isNotNull property="sellSite" prepend=",">#sellSite#</isNotNull>
        <isNotNull property="itemInfo" prepend=",">#itemInfo#</isNotNull>
        <isNotNull property="expressCompanyId" prepend=",">#expressCompanyId#</isNotNull>
        <isNotNull property="pickStyle" prepend=","> #pickStyle# </isNotNull>
        <isNotNull property="logisticsCompanyId" prepend=",">#logisticsCompanyId#</isNotNull>
        <isNotNull property="pickListPrintStatus" prepend=","> #pickListPrintStatus# </isNotNull>
        <isNotNull property="shipperIds" prepend=",">#shipperIds#</isNotNull>
        <isNotNull property="creatorId" prepend=","> #creatorId# </isNotNull>
        <isNotNull property="creatorName" prepend=","> #creatorName# </isNotNull>
        <isNotNull property="pickOrderConfig" prepend=","> #pickOrderConfig# </isNotNull>
        <isNotNull property="understockedGroup" prepend=","> #understockedGroup# </isNotNull>
        <isNotNull property="assignPickerRoles" prepend=","> #assignPickerRoles# </isNotNull>
        <isNotNull property="pickingCartTypeId" prepend=","> #pickingCartTypeId# </isNotNull>
        ,#status#,now());
        <selectKey resultClass="Long" keyProperty="id">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>

    <update id="update">
        update wave_#dbNo#
        <dynamic prepend="set">
            <isNotEmpty property="status" prepend=",">status = #status#</isNotEmpty>
            <isNotEmpty property="pickingType" prepend=",">picking_type = #pickingType#</isNotEmpty>
            <isNotEmpty property="distributionStatus" prepend=",">distribution_status = #distributionStatus#</isNotEmpty>
            <isNotEmpty property="outsidStatus" prepend=",">outsid_status = #outsidStatus#</isNotEmpty>
            <isNotEmpty property="tradesCount" prepend=",">trades_count = #tradesCount#</isNotEmpty>
            <isNotEmpty property="itemCount" prepend=",">item_count = #itemCount#</isNotEmpty>
            <isNotEmpty property="checkGoodsFinished" prepend=",">check_goods_finished = #checkGoodsFinished#</isNotEmpty>
            <isNotNull property="sectionAreas" prepend=",">section_areas = #sectionAreas#</isNotNull>
            <isNotNull property="isConsignInAdvance" prepend=",">is_consign_in_advance = #isConsignInAdvance#</isNotNull>
            <isNotNull property="assignPicker" prepend=",">assign_picker = #assignPicker#</isNotNull>
            <isNotNull property="assignPickerName" prepend=",">assign_picker_name = #assignPickerName#</isNotNull>
            <isNotNull property="assignSorter" prepend=",">assign_sorter = #assignSorter#</isNotNull>
            <isNotNull property="assignSorterName" prepend=",">assign_sorter_name = #assignSorterName#</isNotNull>
            <isNotNull property="itemKindNum" prepend=",">item_kind_num = #itemKindNum#</isNotNull>
            <isNotNull property="customerId" prepend=",">customer_id = #customerId#</isNotNull>
            <isNotNull property="customerName" prepend=",">customer_name = #customerName#</isNotNull>
            <isNotNull property="pickingRoute" prepend=",">picking_route = #pickingRoute#</isNotNull>
            <isNotNull property="finished" prepend=",">finished = #finished#</isNotNull>
            <isNotNull property="fixedItem" prepend=",">fixed_item = #fixedItem#</isNotNull>
            <isNotNull property="tagIds" prepend=",">tag_ids = #tagIds#</isNotNull>
            <isNotNull property="subSectionPick" prepend=",">sub_section_pick = #subSectionPick#</isNotNull>
            <isNotNull property="shortId" prepend=",">short_id = #shortId#</isNotNull>
            <isNotNull property="removedCount" prepend=",">removed_count = #removedCount#</isNotNull>
            <isNotNull property="boxAllocate" prepend=",">box_allocate = #boxAllocate#</isNotNull>
            <isNotNull property="stockRegionZoneId" prepend=",">stock_region_zone_id = #stockRegionZoneId#</isNotNull>
            <isNotNull property="pickEndAutoConsign" prepend=",">pick_end_auto_consign = #pickEndAutoConsign#</isNotNull>
            <isNotNull property="failedLast" prepend=",">failed_last = #failedLast#</isNotNull>
            <isNotNull property="combineItemSingle" prepend=",">combine_item_single = #combineItemSingle#</isNotNull>
            <isNotNull property="splitFlag" prepend=",">split_flag = #splitFlag#</isNotNull>
            <isNotNull property="pickedRemovedCount" prepend=",">picked_removed_count = #pickedRemovedCount#</isNotNull>
            <isNotNull property="expressCompanyId" prepend=",">express_company_id = #expressCompanyId#</isNotNull>
            <isNotNull property="pickStyle" prepend=","> pick_style = #pickStyle# </isNotNull>
            <isNotNull property="logisticsCompanyId" prepend=",">logistics_company_id = #logisticsCompanyId#</isNotNull>
            <isNotNull property="pickListPrintStatus" prepend=","> pick_list_print_status = #pickListPrintStatus# </isNotNull>
            <isNotNull property="seedStartTime" prepend=",">seed_start_time = #seedStartTime#</isNotNull>
            <isNotNull property="seedEndTime" prepend=",">seed_end_time = #seedEndTime#</isNotNull>
            <isNotNull property="clearSeedStartTime" prepend=",">seed_start_time = null</isNotNull>
            <isNotNull property="clearSeedEndTime" prepend=",">seed_end_time = null</isNotNull>
            <isNotNull property="understockedGroup" prepend=",">understocked_group = #understockedGroup#</isNotNull>
            <isNotNull property="clearAssignPickerRoles" prepend=",">assign_picker_roles = null</isNotNull>
            <isNotNull property="pickingCartTypeId" prepend=","> picking_cart_type_id = #pickingCartTypeId# </isNotNull>
        </dynamic>
        where company_id = #companyId# and id = #id#
    </update>

    <sql id="queryWaveItemSql">
        select 1 from trade_#tradeDbNo# t join order_#orderDbNo# o on t.sid = o.sid and t.company_id = o.company_id
        <isEqual property="openWaveUniqueCode" compareValue="true">
            left join wave_unique_code_#uniqueCodeDbNo# c on c.company_id = o.company_id and c.order_id = o.id and c.enable_status = 1 and c.wave_id = t.wave_id
        </isEqual>
        where t.company_id = #companyId#  and t.wave_id = w.id
        and t.enable_status > 0 and o.enable_status > 0
        and t.is_cancel = 0 and o.is_cancel = 0
    </sql>

    <sql id="queryWaveItemSqlCompatible">
        select 1 from wave_sorting_detail_#sortingDetailDbNo# o
        left join wave_sorting_#sortingDbNo# ws on o.sorting_id = ws.id
        <isEqual property="outerIdType" compareValue="1">
            left join dmj_item_#itemDbNo# item on o.sys_item_id = item.sys_item_id
        </isEqual>
        where o.company_id = #companyId# and ws.company_id = #companyId# and ws.picking_id = p.id
    </sql>

    <sql id="queryListCondition">
        <isNotEmpty property="subSectionPick">
            and w.sub_section_pick = #subSectionPick#
        </isNotEmpty>
        <isNotEmpty property="pickListPrintStatus">
            and w.pick_list_print_status = #pickListPrintStatus#
        </isNotEmpty>
        <isNotEmpty property="subSectionPickerId">
            and (p.sub_section_picker_ids is null or p.sub_section_picker_ids = '' or find_in_set(#subSectionPickerId#, p.sub_section_picker_ids) > 0)
        </isNotEmpty>
        <isNotEmpty property="warehouseId">
            and w.warehouse_id = #warehouseId#
        </isNotEmpty>
        <isNotEmpty property="warehouseIds">
            and w.warehouse_id in
            <iterate property="warehouseIds" open="(" conjunction="," close=")">
                #warehouseIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="isConsignInAdvance"> and w.is_consign_in_advance = #isConsignInAdvance# </isNotEmpty>
        <isNotEmpty property="notInWaveRuleGroupIds">
            and (wr.wave_rule_group_id not in
            <iterate property="notInWaveRuleGroupIds" open="(" conjunction="," close=")">
                #notInWaveRuleGroupIds[]#
            </iterate>
            or wr.wave_rule_group_id is null)
            <isNotEmpty property="containNotTradeWave">
                <isEqual property="containNotTradeWave" compareValue="false"> and wr.wave_rule_group_id is not null </isEqual>
            </isNotEmpty>
        </isNotEmpty>
        <isNotEmpty property="pickingPickerId" prepend="and">
            (p.picker_id is null or (p.picker_id = 0 <isEqual property="excludeRelayPicker" compareValue="1"> and p.picker_name != '接力拣选' </isEqual>) or p.picker_id = #pickingPickerId# or w.sub_section_pick = 1)
            <isEqual property="wavePickAfter" compareValue="1">
                and p.picker_id = #pickingPickerId#
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="notWaveIds" prepend="and">
            w.id not in
            <iterate property="notWaveIds" open="(" conjunction="," close=")">
                #notWaveIds[]#
            </iterate>
        </isNotEmpty>

        <isEqual property="statusAndDistributionStatus" compareValue="true">
            <isNotEmpty property="distributionStatus">
                and (
                (w.status in(1) and w.distribution_status in
                <iterate property="distributionStatus" open="(" conjunction="," close=")">
                    #distributionStatus[]#
                </iterate>
                ) or w.status in
                <iterate property="status" open="(" conjunction="," close=")">
                    #status[]#
                </iterate>
                )
            </isNotEmpty>
        </isEqual>
        <isEqual property="statusAndDistributionStatus" compareValue="false">
            <isNotNull property="status">
                and w.status in
                <iterate property="status" open="(" conjunction="," close=")">
                    #status[]#
                </iterate>
            </isNotNull>
            <isNotEmpty property="distributionStatus">
                and w.distribution_status in
                <iterate property="distributionStatus" open="(" conjunction="," close=")">
                    #distributionStatus[]#
                </iterate>
            </isNotEmpty>
        </isEqual>

        <isNotNull property="ruleId">
            and w.rule_id=#ruleId#
        </isNotNull>
        <isNotEmpty property="ruleIds">
            and w.rule_id in
            <iterate property="ruleIds" open="(" conjunction="," close=")">
                #ruleIds[]#
            </iterate>
        </isNotEmpty>
        <isNotNull property="waveId">
            and w.id=#waveId#
        </isNotNull>
        <isNotEmpty property="waveIds">
            and w.id in
            <iterate property="waveIds" open="(" conjunction="," close=")">
                #waveIds[]#
            </iterate>
        </isNotEmpty>
        <isNotNull property="firstWaveId">
            <isNotNull property="endWaveId">
                and <![CDATA[ w.id <= #endWaveId# ]]> and <![CDATA[ w.id >= #firstWaveId# ]]>
            </isNotNull>
        </isNotNull>
        <isNotEmpty property="shortIds">
            and w.short_id in
            <iterate property="shortIds" open="(" conjunction="," close=")">
                #shortIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="pickingCodes">
            and (p.picking_code in
            <iterate property="pickingCodes" open="(" conjunction="," close=")">
                #pickingCodes[]#
            </iterate>
            <isNotEmpty property="queryMultiPickingCode">
                <isEqual property="queryMultiPickingCode" compareValue="1">
                    or p.id in (select picking_id from wave_picking_code_#pickingCodeDbNo# where company_id = #companyId# and picking_code in
                    <iterate property="pickingCodes" open="(" conjunction="," close=")">
                        #pickingCodes[]#
                    </iterate>
                    )
                </isEqual>
            </isNotEmpty>
            )
        </isNotEmpty>
        <isNotEmpty property="shopUserIds">
            <isEqual property="shopUserIdInclude" compareValue="-1">
                and w.shop_user_id is not null
            </isEqual>
            <isEqual property="shopUserIdInclude" compareValue="-1001">
                and w.shop_user_id is null
            </isEqual>
            <isEqual property="shopUserIdInclude" compareValue="0">
                and w.shop_user_id in
                <iterate property="shopUserIds" open="(" conjunction="," close=")">
                    #shopUserIds[]#
                </iterate>
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="cooperationNo">
            and w.cooperation_no=#cooperationNo#
        </isNotEmpty>
        <isNotEmpty property="sellSite">
            and w.sell_site like concat('%', #sellSite#, '%')
        </isNotEmpty>
        <isEqual property="ignoreNotPick" compareValue="true">
            w.distribution_status != 0
        </isEqual>
        <isNotEmpty property="pickingTypes">
            and w.picking_type in
            <iterate property="pickingTypes" open="(" conjunction="," close=")">
                #pickingTypes[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="waveTypes">
            and w.wave_type in
            <iterate property="waveTypes" open="(" conjunction="," close=")">
                #waveTypes[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="notRuleTypes">
            and (wr.rule_type not in <iterate property="notRuleTypes" open="(" conjunction="," close=")"> #notRuleTypes[]# </iterate> OR wr.rule_type is null)
        </isNotEmpty>
        <isNotEmpty property="tagStatus">
            <isEqual property="tagStatus" compareValue="0">
                and FIND_IN_SET(1, w.tag_ids) = 0
                <isNotEmpty property="orPickingTypes">
                    and (
                    w.picking_type in <iterate property="orPickingTypes" open="(" conjunction="," close=")"> #orPickingTypes[]# </iterate>
                    <isNotEmpty property="orRuleTypes">
                        or wr.rule_type in <iterate property="orRuleTypes" open="(" conjunction="," close=")"> #orRuleTypes[]# </iterate>
                    </isNotEmpty>
                    )
                </isNotEmpty>
            </isEqual>
            <isEqual property="tagStatus" compareValue="1">
                <isNotEmpty property="orPickingTypes">
                    and (
                    w.picking_type in <iterate property="orPickingTypes" open="(" conjunction="," close=")"> #orPickingTypes[]# </iterate>
                    or
                    FIND_IN_SET(1, w.tag_ids) > 0
                    <isNotEmpty property="orRuleTypes">
                        or wr.rule_type in <iterate property="orRuleTypes" open="(" conjunction="," close=")"> #orRuleTypes[]# </iterate>
                    </isNotEmpty>
                    )
                </isNotEmpty>
                <isEmpty property="orPickingTypes">
                    and FIND_IN_SET(1, w.tag_ids) > 0
                </isEmpty>
            </isEqual>
        </isNotEmpty>

        <isNotEmpty property="pickOrderConfigs">
            and w.pick_order_config in
            <iterate property="pickOrderConfigs" open="(" conjunction="," close=")">
                #pickOrderConfigs[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="sectionAreas" prepend="and" open="(" close=")">
            <iterate property="sectionAreas" open="(" close=")" conjunction="or">
                find_in_set(#sectionAreas[]#,section_areas)
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="startTime">
            <![CDATA[ and w.created >= #startTime# ]]>
        </isNotEmpty>
        <isNotEmpty property="endTime">
            <![CDATA[ and w.created <= #endTime# ]]>
        </isNotEmpty>
        <isNotEmpty property="pickStartTime">
            <![CDATA[ and p.start_time >= #pickStartTime# ]]>
        </isNotEmpty>
        <isNotEmpty property="pickEndTime">
            <![CDATA[ and p.end_time <= #pickEndTime# ]]>
        </isNotEmpty>
        <isNotEmpty property="finishedStartTime">
            <![CDATA[ and w.finished >= #finishedStartTime# ]]>
        </isNotEmpty>
        <isNotEmpty property="finishedEndTime">
            <![CDATA[ and w.finished <= #finishedEndTime# ]]>
        </isNotEmpty>
        <isNotEmpty property="modifiedStart"> <![CDATA[ and w.modified >= #modifiedStart# ]]> </isNotEmpty>
        <isNotEmpty property="modifiedEnd"> <![CDATA[ and w.modified <= #modifiedEnd# ]]> </isNotEmpty>
        <isNotEmpty property="pickingCode" prepend="and">
            p.picking_code = #pickingCode#
        </isNotEmpty>
        <isNotEmpty property="checkGoodsFinished" prepend="and">
            w.check_goods_finished = #checkGoodsFinished#
        </isNotEmpty>
        <isNotEmpty property="assignStatus">
            <isEqual property="assignStatus" compareValue="0">
                and p.id is null and w.assign_picker = 0 and w.assign_sorter = 0
            </isEqual>
            <isEqual property="assignStatus" compareValue="1">
                and (w.assign_picker > 0 or w.assign_sorter > 0 or p.picker_id > 0 or p.sorter_id > 0)
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="shortageNumCompare">
            and w.distribution_status > 0
            <isEqual property="shortageNumCompare" compareValue="0">
                and p.shortage_num = 0
            </isEqual>
            <isEqual property="shortageNumCompare" compareValue="1">
                and p.plan_pick_num > 0 and p.shortage_num > 0
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="unPickNumCompare">
            and p.plan_pick_num > 0
            <isEqual property="unPickNumCompare" compareValue="0">
                and ((w.distribution_status = 1 and p.unpick_num = 0) or (w.distribution_status > 1))
            </isEqual>
            <isEqual property="unPickNumCompare" compareValue="1">
                and (w.distribution_status = 1 and p.unpick_num > 0)
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="remarkKey" prepend="and">
            w.customer_name like #remarkKey#
        </isNotEmpty>
        <isNotEmpty property="expressCompanyIds" prepend="and">
            w.express_company_id in
            <iterate property="expressCompanyIds" open="(" conjunction="," close=")">
                #expressCompanyIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="logisticsCompanyIds" prepend="and">
            w.logistics_company_id in
            <iterate property="logisticsCompanyIds" open="(" conjunction="," close=")">
                #logisticsCompanyIds[]#
            </iterate>
        </isNotEmpty>
        <!-- 订单数/商品数筛选 -->
        <isNotEmpty property="itemNumDown" prepend="and">
            w.item_count >= #itemNumDown#
        </isNotEmpty>
        <isNotEmpty property="itemNumUp" prepend="and">
            <![CDATA[ w.item_count <= #itemNumUp# ]]>
        </isNotEmpty>
        <isNotEmpty property="tradeNumDown" prepend="and">
            w.trades_count >= #tradeNumDown#
        </isNotEmpty>
        <isNotEmpty property="tradeNumUp" prepend="and">
            <![CDATA[ w.trades_count <= #tradeNumUp# ]]>
        </isNotEmpty>
        <isNotEmpty property="pickStyles" prepend="and" open="(" close=")">
            w.pick_style in <iterate property="pickStyles" open="(" conjunction="," close=")"> #pickStyles[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="pickerIds" prepend="and" open="(" close=")">
            w.assign_picker in <iterate property="pickerIds" open="(" conjunction="," close=")"> #pickerIds[]# </iterate>
            or p.picker_id in <iterate property="pickerIds" open="(" conjunction="," close=")"> #pickerIds[]# </iterate>
            or exists (select 1 from wave_picker_#pickerDbNo# picker where picker.wave_id = w.id
            and picker.company_id =#companyId# and picker.participate_pick = 1 and picker.picker_id in
            <iterate property="pickerIds" open="(" conjunction="," close=")">#pickerIds[]#</iterate>
            )
        </isNotEmpty>
        <isNotEmpty property="sorterIds" prepend="and" open="(" close=")">
            w.assign_sorter in <iterate property="sorterIds" open="(" conjunction="," close=")"> #sorterIds[]# </iterate>
            or p.sorter_id in <iterate property="sorterIds" open="(" conjunction="," close=")"> #sorterIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="assignPickers">
            and w.assign_picker in
            <iterate property="assignPickers" open="(" conjunction="," close=")"> #assignPickers[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="assignSorters">
            and w.assign_sorter in
            <iterate property="assignSorters" open="(" conjunction="," close=")"> #assignSorters[]# </iterate>
        </isNotEmpty>

        <isNotEmpty property="pickerRoleMatch">
            <isNotEmpty property="assignPickerRoleIds">
                and (w.assign_picker_roles is null or
                <iterate property="assignPickerRoleIds" open="(" conjunction=" or " close=")">
                    FIND_IN_SET(#assignPickerRoleIds[]#, w.assign_picker_roles) > 0
                </iterate>
                )
            </isNotEmpty>
            <isEmpty property="assignPickerRoleIds">
                and w.assign_picker_roles is null
            </isEmpty>
        </isNotEmpty>
        <isEmpty property="pickerRoleMatch">
            <isNotEmpty property="assignPickerRoleIds">
                and w.assign_picker_roles is not null and
                <iterate property="assignPickerRoleIds" open="(" conjunction=" or " close=")">
                    FIND_IN_SET(#assignPickerRoleIds[]#, w.assign_picker_roles) > 0
                </iterate>
            </isNotEmpty>
        </isEmpty>
        <isNotEmpty property="creatorIds">
            and w.creator_id in
            <iterate property="creatorIds" open="(" conjunction="," close=")"> #creatorIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="sorters">
            and IF(p.sorter_id = 0 || p.sorter_id is null,w.assign_sorter,p.sorter_id) in
            <iterate property="sorters" open="(" conjunction="," close=")"> #sorters[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysItemIds">
            and
            <isEqual property="outerIdQueryType" compareValue="0">
                <isNotEmpty property="waveIdsByItemSearch">
                    w.id in <iterate property="waveIdsByItemSearch" open="(" conjunction="," close=")"> #waveIdsByItemSearch[]# </iterate>
                </isNotEmpty>
                <isEmpty property="waveIdsByItemSearch">
                    1 = 2
                </isEmpty>
            </isEqual>
            <isNotEqual property="outerIdQueryType" compareValue="0">
                <iterate property="itemKeyList" open="(" close=")" conjunction="and">
                    exists (
                    select 1 from wave_trade_#waveTradeDbNo# t join order_#orderDbNo# o on t.sid = o.sid and t.company_id = o.company_id
                    where t.company_id = #companyId#  and t.wave_id = w.id and o.enable_status > 0 and o.is_cancel = 0 and o.is_virtual = 0 and o.non_consign = 0
                    and o.item_sys_id = #itemKeyList[].sysItemId# and o.sku_sys_id = #itemKeyList[].sysSkuId#
                    union
                    SELECT 1 FROM wave_sorting_detail_#sortingDetailDbNo# wsd
                    LEFT JOIN wave_sorting_#sortingDbNo# ws ON wsd.sorting_id = ws.id AND wsd.company_id = ws.company_id
                    JOIN wave_picking_#pickingDbNo# wp ON wp.id = ws.picking_id AND wp.company_id = ws.company_id
                    WHERE
                    wsd.company_id = #companyId# AND wsd.enable_status > 0 AND ws.enable_status > 0 and wp.wave_id = w.id
                    AND wsd.outer_id = #itemKeyList[].outerId#
                    <isEqual property="openWaveUniqueCode" compareValue="true" prepend="UNION">
                        SELECT 1 FROM WAVE_UNIQUE_CODE_#uniqueCodeDbNo# C
                        WHERE C.COMPANY_ID = #companyId#
                        AND c.enable_status = 1 and c.wave_id = w.id
                        AND C.UNIQUE_CODE = #itemKeyList[].outerId#
                    </isEqual>
                    )
                </iterate>
            </isNotEqual>
        </isNotEmpty>
        <isNotEmpty property="expressEntry" prepend="and">
            <iterate property="expressEntry" open="(" conjunction="or" close=")">
                <isEqual compareValue="-1" property="expressEntry[].key"> w.express_template_id = -1 </isEqual>
                <isEqual compareValue="-2" property="expressEntry[].key"> w.express_template_id > 0 </isEqual>
                <isEqual compareValue="-3" property="expressEntry[].key"> w.express_template_id = 0 </isEqual>
                <isGreaterEqual compareValue="0" property="expressEntry[].key">
                    <isNotEmpty property="expressEntry[].value">
                        w.express_template_type = #expressEntry[].key# and w.express_template_id in <iterate property="expressEntry[].value" open="(" conjunction="," close=")"> #expressEntry[].value[]# </iterate>
                    </isNotEmpty>
                </isGreaterEqual>
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="outsidStatus">
            <iterate property="outsidStatus" prepend=" AND " open="(" close=")" conjunction=" OR ">
                <isEqual property="outsidStatus[]" compareValue="0">
                    <![CDATA[
                    exists (
                    select 1 from ( select count(case when wt.trade_wave_status != 2 and t.out_sid is not null and t.out_sid != '' then wt.sid end) as tradePrintCount, count(case when wt.trade_wave_status = 2 then wt.sid end) as untradePrintCount
                    from wave_trade_#waveTradeDbNo# wt
                    left join trade_#tradeDbNo# t on wt.company_id = t.company_id and wt.sid = t.sid
                    where t.company_id = #companyId#
                    and wt.company_id = #companyId#
                    and t.enable_status = 1
                    and wt.wave_id = w.id
                    and t.warehouse_id = w.warehouse_id
                    ) as subquery
                    where subquery.tradePrintCount = 0
                    and subquery.tradePrintCount != (select count(*) from trade_#tradeDbNo# where company_id = #companyId# and enable_status != 2 and sid in (select sid from wave_trade_#waveTradeDbNo# where wave_id = w.id)) - subquery.untradePrintCount) and w.status < 4 and w.picking_type < 4
                    ]]>
                </isEqual>
                <isEqual property="outsidStatus[]" compareValue="1" open="(" close=")">
                    <![CDATA[
                    (select count(wt.sid) as tradePrintCount from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t on wt.company_id = t.company_id AND wt.sid = t.sid
                    where t.company_id=#companyId# and wt.company_id = #companyId# and wt.trade_wave_status != 2 and t.enable_status = 1 and t.out_sid is not null and t.out_sid != '' and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id) > 0
                    AND
                    (select count(wt.sid) as tradePrintCount from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t on wt.company_id = t.company_id AND wt.sid = t.sid
                    where t.company_id=#companyId# and wt.company_id = #companyId# and wt.trade_wave_status != 2 and t.enable_status = 1 and t.out_sid is not null and t.out_sid != '' and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id) < (select count(*) from trade_#tradeDbNo# where company_id = #companyId# and enable_status != 2 and sid in (select sid from wave_trade_#waveTradeDbNo# where wave_id = w.id)) - (select count(wt.sid) as tradePrintCount from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t on wt.company_id = t.company_id AND wt.sid = t.sid
                    where t.company_id=#companyId# and wt.company_id = #companyId# and wt.trade_wave_status = 2 and t.enable_status = 1 and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id)
                    ]]>
                </isEqual>
                <isEqual property="outsidStatus[]" compareValue="2">
                    <![CDATA[
                    exists (
                    select 1 from ( select count(case when wt.trade_wave_status != 2 and t.out_sid is not null and t.out_sid != '' then wt.sid end) as tradePrintCount, count(case when wt.trade_wave_status = 2 then wt.sid end) as untradePrintCount
                    from wave_trade_#waveTradeDbNo# wt
                    left join trade_#tradeDbNo# t on wt.company_id = t.company_id and wt.sid = t.sid
                    where t.company_id = #companyId#
                    and wt.company_id = #companyId#
                    and t.enable_status = 1
                    and wt.wave_id = w.id
                    and t.warehouse_id = w.warehouse_id
                    ) as subquery
                    where subquery.tradePrintCount > 0
                    and subquery.tradePrintCount = (select count(*) from trade_#tradeDbNo# where company_id = #companyId# and enable_status != 2 and sid in (select sid from wave_trade_#waveTradeDbNo# where wave_id = w.id)) - subquery.untradePrintCount) and w.status < 4 and w.picking_type < 4
                    ]]>
                </isEqual>
                <isEqual property="outsidStatus[]" compareValue="3">
                    <![CDATA[
                    w.status > 4 and w.picking_type >= 4 or ((select count(*) from trade_#tradeDbNo# where company_id = #companyId# and enable_status != 2 and sid in (select sid from wave_trade_#waveTradeDbNo# where wave_id = w.id)) = (select count(wt.sid) as tradePrintCount from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t on wt.company_id = t.company_id AND wt.sid = t.sid
                    where t.company_id=#companyId# and wt.company_id = #companyId# and wt.trade_wave_status = 2 and t.enable_status = 1 and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id))
                    ]]>
                </isEqual>
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="printStatus">
            <iterate property="printStatus" prepend=" AND " open="(" close=")" conjunction=" AND ">
                <isLessThan property="printStatus[]" compareValue="3">
                    <![CDATA[ w.picking_type <= 3 ]]>
                </isLessThan>
            </iterate>
            <iterate property="printStatus" prepend=" AND " open="(" close=")" conjunction=" OR ">
                <isEqual property="printStatus[]" compareValue="0">
                    (select count(wt.sid) as tradePrintCount from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t on wt.company_id = t.company_id AND wt.sid = t.sid
                    where t.company_id=#companyId# and wt.company_id = #companyId# and t.enable_status = 1 and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id and t.express_print_time != '2000-01-01 00:00:00') = 0
                </isEqual>
                <isEqual property="printStatus[]" compareValue="1">
                    (select count(wt.sid) as tradePrintCount from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t on wt.company_id = t.company_id AND wt.sid = t.sid
                    where t.company_id=#companyId# and wt.company_id = #companyId# and t.enable_status = 1 and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id and t.express_print_time = '2000-01-01 00:00:00') = 0
                </isEqual>
                <isEqual property="printStatus[]" compareValue="2" open="(" close=")">
                    <![CDATA[
                    (select count(wt.sid) as tradePrintCount from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t on wt.company_id = t.company_id AND wt.sid = t.sid
                    where t.company_id=#companyId# and wt.company_id = #companyId# and t.enable_status = 1 and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id and t.express_print_time != '2000-01-01 00:00:00') > 0
                    AND
                    (select count(wt.sid) as tradePrintCount from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t on wt.company_id = t.company_id AND wt.sid = t.sid
                    where t.company_id=#companyId# and wt.company_id = #companyId# and t.enable_status = 1 and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id and t.express_print_time != '2000-01-01 00:00:00') < w.trades_count
                    ]]>
                </isEqual>
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="unRemoveWavePrintStatus">
            <iterate property="unRemoveWavePrintStatus" prepend=" AND " open="(" close=")" conjunction=" AND ">
                <isNotEqual property="unRemoveWavePrintStatus[]" compareValue="3">
                    <![CDATA[ w.picking_type <= 3 ]]>
                </isNotEqual>
            </iterate>
            <iterate property="unRemoveWavePrintStatus" prepend=" AND " open="(" close=")" conjunction=" OR ">
                <isEqual property="unRemoveWavePrintStatus[]" compareValue="4">
                    <![CDATA[
                        not exists (select 1 tradePrintCount
                                from wave_trade_#waveTradeDbNo# wt
                                where wt.company_id = #companyId# and wt.wave_id = w.id and wt.trade_wave_status in (0, 1)
                        )
                 ]]>
                </isEqual>
                <isEqual property="unRemoveWavePrintStatus[]" compareValue="0">
                    <![CDATA[
                        exists (select 1 tradePrintCount
                                from wave_trade_#waveTradeDbNo# wt
                                where wt.company_id = #companyId# and wt.wave_id = w.id and wt.trade_wave_status in (0, 1)
                        ) AND
                        not exists (select 1 tradePrintCount
                                from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t FORCE INDEX (PRIMARY )  on wt.company_id = t.company_id AND wt.sid = t.sid
                                where wt.company_id = #companyId# and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id and t.express_print_time != '2000-01-01 00:00:00'
                                and wt.trade_wave_status in (0, 1)
                        )
                    ]]>
                </isEqual>
                <isEqual property="unRemoveWavePrintStatus[]" compareValue="1">
                    <![CDATA[
                        exists (select 1 tradePrintCount
                                from wave_trade_#waveTradeDbNo# wt
                                where wt.company_id = #companyId# and wt.wave_id = w.id and wt.trade_wave_status in (0, 1)
                        ) AND
                        not exists (select 1 tradePrintCount
                                from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo#  t FORCE INDEX (PRIMARY )  on wt.company_id = t.company_id AND wt.sid = t.sid
                                where wt.company_id = #companyId# and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id and t.express_print_time = '2000-01-01 00:00:00'
                                and wt.trade_wave_status in (0, 1)
                        )
                     ]]>
                </isEqual>
                <isEqual property="unRemoveWavePrintStatus[]" compareValue="2">
                    <![CDATA[
                        exists (select 1 tradePrintCount
                                from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t FORCE INDEX (PRIMARY )  on wt.company_id = t.company_id AND wt.sid = t.sid
                                where wt.company_id = #companyId# and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id and t.express_print_time = '2000-01-01 00:00:00'
                                and wt.trade_wave_status in (0, 1)
                        ) AND
                        exists (select 1 tradePrintCount
                                from wave_trade_#waveTradeDbNo# wt left join trade_#tradeDbNo# t FORCE INDEX (PRIMARY )  on wt.company_id = t.company_id AND wt.sid = t.sid
                                where wt.company_id = #companyId# and wt.wave_id = w.id and t.warehouse_id = w.warehouse_id and t.express_print_time != '2000-01-01 00:00:00'
                                and wt.trade_wave_status in (0, 1)
                        )
                    ]]>
                </isEqual>
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="tagIds"  prepend=" and ">
            <iterate property="tagIds" open="(" conjunction=" or " close=")">
                FIND_IN_SET(#tagIds[]#, w.tag_ids) > 0
            </iterate>
        </isNotEmpty>
        <!--波次送打状态-->
        <isEqual property="waveSendPrintStatus" compareValue="1">
            <![CDATA[
                   AND  exists (select 1
                                FROM wave_trade_printing_status_#wtpDbNo#  wtp
                                where wtp.company_id = #companyId# and wtp.wave_id = w.id  and wtp.type = 1)
                     ]]>
        </isEqual>
        <isEqual property="waveSendPrintStatus" compareValue="0">
            <![CDATA[
                   AND NOT exists (select 1
                                FROM wave_trade_printing_status_#wtpDbNo#  wtp
                                where wtp.company_id = #companyId# and wtp.wave_id = w.id and wtp.type = 1)
                     ]]>
        </isEqual>
        <!-- 货主ids集合 -->
        <isNotEmpty property="shipperIds" prepend=" and ">
            <iterate property="shipperIds" open="(" conjunction=" or " close=")">
                FIND_IN_SET(#shipperIds[]#, w.shipper_ids) > 0
            </iterate>
        </isNotEmpty>
    </sql>

    <sql id="queryWaveIdByItemIdsTimeWhere">
        <isNotEmpty property="startTime"> <![CDATA[ and w.created >= #startTime# ]]> </isNotEmpty>
        <isNotEmpty property="endTime"> <![CDATA[ and w.created <= #endTime# ]]> </isNotEmpty>
        <isNotEmpty property="finishedStartTime"> <![CDATA[ and w.finished >= #finishedStartTime# ]]> </isNotEmpty>
        <isNotEmpty property="finishedEndTime"> <![CDATA[ and w.finished <= #finishedEndTime# ]]> </isNotEmpty>
    </sql>

    <select id="queryWaveIdByItemIds" resultClass="long" timeout="60">
        select t.wave_id from wave_trade_#waveTradeDbNo# t
        join order_#orderDbNo# o on t.sid = o.sid and t.company_id = o.company_id
        join wave_#waveDbNo# w on t.wave_id = w.id and t.company_id = w.company_id
        where t.company_id = #companyId# and o.is_cancel = 0 and o.enable_status > 0 and o.is_virtual = 0 and o.non_consign = 0
        and o.item_sys_id in <iterate property="sysItemIds" open="(" conjunction="," close=")"> #sysItemIds[]# </iterate>
        and o.sku_sys_id in <iterate property="sysSkuIds" open="(" conjunction="," close=")"> #sysSkuIds[]# </iterate>
        <isNotEmpty property="warehouseId">
            and w.warehouse_id = #warehouseId#
        </isNotEmpty>
        <isNotEmpty property="warehouseIds">
            and w.warehouse_id in
            <iterate property="warehouseIds" open="(" conjunction="," close=")">
                #warehouseIds[]#
            </iterate>
        </isNotEmpty>
        <include refid="queryWaveIdByItemIdsTimeWhere"/>

        union

        SELECT w.id FROM wave_#waveDbNo# w
        left join wave_picking_#pickingDbNo# wp on wp.wave_id = w.id and wp.company_id = w.company_id
        left join wave_sorting_#sortingDbNo# ws on ws.picking_id = wp.id and ws.company_id = wp.company_id
        left join wave_sorting_detail_#sortingDetailDbNo# wsd on wsd.sorting_id = ws.id and wsd.company_id = ws.company_id
        WHERE
        wsd.company_id = #companyId# AND wsd.enable_status > 0 AND ws.enable_status > 0
        <!-- 模糊查询取itemKey里查询到的精确outerId -->
        <isEqual property="outerIdsVagueType" compareValue="1">
            AND wsd.outer_id IN <iterate property="itemKeyList" open="(" close=")" conjunction=","> #itemKeyList[].outerId# </iterate>
        </isEqual>
        <!-- 精确查询取输入的原始outerId -->
        <isNotEqual property="outerIdsVagueType" compareValue="1">
            AND wsd.outer_id IN <iterate property="outerIds" open="(" close=")" conjunction=","> #outerIds[]# </iterate>
        </isNotEqual>
        <isNotEmpty property="warehouseId">
            and w.warehouse_id = #warehouseId#
        </isNotEmpty>
        <isNotEmpty property="warehouseIds">
            and w.warehouse_id in
            <iterate property="warehouseIds" open="(" conjunction="," close=")">
                #warehouseIds[]#
            </iterate>
        </isNotEmpty>
        <include refid="queryWaveIdByItemIdsTimeWhere"/>
        <isEqual property="openWaveUniqueCode" compareValue="true" prepend="UNION">
            SELECT c.wave_id FROM WAVE_UNIQUE_CODE_#uniqueCodeDbNo# C
            WHERE C.COMPANY_ID = #companyId#
            AND c.enable_status = 1
            AND C.UNIQUE_CODE IN <iterate property="outerIds" open="(" conjunction="," close=")"> #outerIds[]# </iterate>
            AND c.wave_id is not null
        </isEqual>
    </select>


    <select id="queryWaitPickWaveIds" resultClass="long" timeout="60">
        select id from wave_#waveDbNo#
        where company_id=#companyId#
        and warehouse_id=#warehouseId#
        and distribution_status=#distributionStatus#
        and assign_picker=0
        and status=1
        and rule_id in <iterate property="ruleIds" open="(" conjunction="," close=")">#ruleIds[]#</iterate>
    </select>

    <select id="queryWaitPickWave" resultMap="WaveTagIds" timeout="60">
        select id,tag_ids from wave_#waveDbNo#
        where company_id=#companyId#
        and warehouse_id=#warehouseId#
        and distribution_status=#distributionStatus#
        and assign_picker=0
        and status=1
        and rule_id in <iterate property="ruleIds" open="(" conjunction="," close=")">#ruleIds[]#</iterate>
    </select>

    <select id="queryOperatorWaitPickWave" resultClass="long" timeout="60">
        select id from wave_#waveDbNo#
        where company_id=#companyId#
        and warehouse_id=#warehouseId#
        and distribution_status in (0,1)
        and assign_picker=#assignPicker#
        and status=1
        and rule_id in <iterate property="ruleIds" open="(" conjunction="," close=")">#ruleIds[]#</iterate>
    </select>


    <select id="queryPickerInfo" resultMap="WavePickerInfo">
        select w.id,w.assign_picker,p.picker_id from wave_#waveDbNo# w
        LEFT JOIN wave_picking_#pickingDbNo# p on w.id = p.wave_id and  enable_status > 0
        where w.company_id=#companyId#
        <include refid="queryListCondition"/>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>


    <sql id="orderByField">
        <isEqual property="sortField" compareValue="canSortDistributionStatus">
            <isEqual property="sortOrder" compareValue="desc">
                w.status desc, FIELD (w.distribution_status, 8, 7, 5, 4, 3, 6, 2, 1, 0)
            </isEqual>
            <isNotEqual property="sortOrder" compareValue="desc">
                w.status asc, FIELD (w.distribution_status, 0, 1, 2, 6, 3, 4, 5, 7, 8)
            </isNotEqual>
        </isEqual>

        <isNotEqual property="sortField" compareValue="canSortDistributionStatus">
            <encode property="sortField"></encode> <encode property="sortOrder"></encode>
        </isNotEqual>
    </sql>

    <select id="queryPageList" resultMap="waveStatisticsMap" timeout="60">
        select w.pick_list_print_status, w.pick_style,w.sub_section_pick,w.short_id,wr.rule_type,w.finished,w.customer_name,w.id,w.company_id,w.code,w.warehouse_id,w.rule_id,w.condition,w.express_template_id,w.express_template_type,w.status,w.created,w.modified,w.distribution_status,
        w.picking_type,w.outsid_status,w.check_goods_finished,w.section_areas,w.is_consign_in_advance,w.position_no_type,w.assign_picker,w.express_company_id,w.creator_id,w.creator_name,w.seed_start_time,w.seed_end_time,w.pick_order_config,
        w.assign_picker_name,w.assign_sorter,w.assign_sorter_name, w.item_kind_num, w.picking_route, w.tag_ids,w.removed_count, w.picked_removed_count, w.box_allocate, w.stock_region_zone_id,w.shop_user_id, w.combine_item_single,w.split_flag,
        w.cooperation_no, w.sell_site, w.logistics_company_id, w.understocked_group, w.shipper_ids,w.assign_picker_roles,w.picking_cart_type_id,
        <isNotEmpty property="reCalculationNum">
            <isEqual property="reCalculationNum" compareValue="1">
                IF(w.picking_type>3,w.trades_count,
                IFNUll((select count(sid) as tradesCount from trade_<isNotEqual property="accurateStatWaveTradeNum" compareValue="true">not_consign_</isNotEqual>#tradeDbNo# t force index(idx_company_warehouse_wave) where t.company_id=#companyId# and t.enable_status = 1 and t.is_cancel = 0 and t.type not like 'trade_out%'
                and t.scalping = 0 and t.wave_id = w.id and t.warehouse_id = w.warehouse_id),0)) as trades_count,
                IF(w.picking_type>3,w.item_count,
                IFNUll((select sum(item_num) as itemCount from trade_<isNotEqual property="accurateStatWaveTradeNum" compareValue="true">not_consign_</isNotEqual>#tradeDbNo# t force index(idx_company_warehouse_wave) where t.company_id=#companyId# and t.enable_status = 1 and t.is_cancel = 0 and t.type not like 'trade_out%'
                and t.scalping = 0 and t.wave_id = w.id and t.warehouse_id = w.warehouse_id),0)) as item_count,
            </isEqual>
            <isEqual property="reCalculationNum" compareValue="0">
                w.trades_count,w.item_count,
            </isEqual>
        </isNotEmpty>
        <isEmpty property="reCalculationNum">
            w.trades_count,w.item_count,
        </isEmpty>
        <isNotEmpty property="countUnPackNum">
            <isEqual property="countUnPackNum" compareValue="1">
                IF(w.picking_type>3,0,
                IFNUll((select count(sid) from trade_#tradeDbNo# t where t.company_id=#companyId# and t.enable_status = 1 and t.is_cancel = 0 and t.type not like 'trade_out%'
                and t.scalping = 0 and t.wave_id = w.id and t.warehouse_id = w.warehouse_id and t.is_package = 0),0)) as un_pack_num,
            </isEqual>
            <isEqual property="countUnPackNum" compareValue="0">
                0 as un_pack_num,
            </isEqual>
        </isNotEmpty>
        <isEmpty property="countUnPackNum">
            0 as un_pack_num,
        </isEmpty>
        wr.name as rule_name, wr.wave_rule_group_id , p.sub_section_picker_ids, p.picking_code, p.id picking_id, p.picker_name, p.picker_id, p.start_time pick_start_time, p.end_time pick_end_time, p.sorter_name,
        p.plan_pick_num, p.picked_num, p.unpick_num, p.shortage_num, p.pick_after_status from wave_#waveDbNo# w
        left join wave_rule wr on w.rule_id=wr.id and wr.company_id=#companyId#
        LEFT JOIN wave_picking_#pickingDbNo# p on w.id = p.wave_id and p.company_id = #companyId# and p.enable_status > 0
        where w.company_id=#companyId#
        <include refid="queryListCondition" />

        <isNotEmpty property="waveSortTime">
            order by
            <isEqual property="wavePickAfter" compareValue="1">
                if(p.unpick_num > 0 or p.shortage_num > 0, 0, 1),
                if(p.pick_after_status = 1, 0, 1),
            </isEqual>
            <isEqual property="waveSortRandom" compareValue="1"> rand(), </isEqual>
            <isEqual property="waveSortUrgent" compareValue="1"> find_in_set(2, w.tag_ids) desc, </isEqual>
            <isEqual property="waveSortAssigner" compareValue="0"> w.assign_picker = #currentStaff# desc, </isEqual>
            <isEqual property="waveSortAssigner" compareValue="1"> w.assign_sorter = #currentStaff# desc, </isEqual>
            <isNotNull property="sectionAreasSort">
                w.section_areas <isEqual property="sectionAreasSort" compareValue="1">desc</isEqual>,
            </isNotNull>
            <isNotEmpty property="sortField"> <include refid="orderByField"/> , </isNotEmpty>
            <isEqual property="waveSortRule" compareValue="1"> w.rule_id, </isEqual>
            <isEqual property="failedLast" compareValue="1"> w.failed_last, </isEqual>
            <isEqual property="fromPage" compareValue="true"> w.created </isEqual>
            <isNotEqual property="fromPage" compareValue="true"> w.id </isNotEqual>
            <isEqual property="waveSortTime" compareValue="1"> desc </isEqual>
        </isNotEmpty>
        <isEmpty property="waveSortTime">
            order by
            <isEqual property="wavePickAfter" compareValue="1">
                if(p.unpick_num > 0 or p.shortage_num > 0, 0, 1),
                if(p.pick_after_status = 1, 0, 1),
            </isEqual>
            <isEqual property="waveSortUrgent" compareValue="1"> find_in_set(2, w.tag_ids) desc, </isEqual>
            <isEqual property="waveSortAssigner" compareValue="0"> w.assign_picker = #currentStaff# desc, </isEqual>
            <isEqual property="waveSortAssigner" compareValue="1"> w.assign_sorter = #currentStaff# desc, </isEqual>
            <isNotNull property="sectionAreasSort">
                w.section_areas <isEqual property="sectionAreasSort" compareValue="1">desc</isEqual>,
            </isNotNull>
            <isNotEmpty property="sortField"> <include refid="orderByField"/> </isNotEmpty>
        </isEmpty>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="count" resultClass="long" timeout="30">
        select count(1) from wave_#waveDbNo# w
        left join wave_rule wr on w.rule_id=wr.id and wr.company_id=#companyId#
        LEFT JOIN wave_picking_#pickingDbNo# p on w.id = p.wave_id and p.company_id = #companyId# and p.enable_status > 0
        where w.company_id=#companyId#
        <include refid="queryListCondition" />
    </select>

    <select id="queryById" resultMap="baseWaveMap">
        /*FORCE_MASTER*/ select
        w.*,r.name rule_name,r.rule_type from wave_#dbNo# w left join wave_rule r on w.rule_id = r.id
        where w.id = #id# and w.company_id = #companyId#
    </select>

    <select id="queryByIds" resultMap="baseWaveMap">
        /*FORCE_MASTER*/ select
        w.*,r.name rule_name,r.rule_type from wave_#dbNo# w left join wave_rule r on w.rule_id = r.id
        where w.company_id = #companyId#
        and w.id in
        <iterate property="ids" open="(" close=")" conjunction=",">
            #ids[]#
        </iterate>
    </select>

    <select id="getDistributionWaveCount" resultClass="long">
        select count(1) from wave_#dbNo# where company_id = #companyId# and status = 1
        and distribution_status in
        <iterate property="distributionStatus" open="(" conjunction="," close=")">
            #distributionStatus[]#
        </iterate>
    </select>

    <select id="queryByCondition" resultMap="waveMap" timeout="30">
        select w.*,wr.name as rule_name, p.picking_code, p.id picking_id, p.picker_name, p.start_time pick_start_time, p.end_time pick_end_time, p.sorter_name from wave_#waveDbNo# w
        left join wave_rule wr on w.rule_id=wr.id and wr.company_id=#companyId#
        LEFT JOIN wave_picking_#pickingDbNo# p on w.id = p.wave_id and p.company_id = #companyId# and p.enable_status > 0
        where w.company_id=#companyId#
        <include refid="queryListCondition" />
        limit 1
    </select>

    <select id="checkNotFinishByRuleIds" resultClass="long">
        select count(*) from wave_#dbNo#
        where company_id = #companyId#
        and status = 1
        and rule_id in <iterate property="ruleIds" open="(" conjunction="," close=")">#ruleIds[]#</iterate>
        <isNotEmpty property="warehouseIds">
            and warehouse_id in <iterate property="warehouseIds" open="(" conjunction="," close=")"> #warehouseIds[]# </iterate>
        </isNotEmpty>
    </select>

    <select id="queryPickedSingleWave" resultMap="waveMap">
        select w.*,'' as rule_name, p.picking_code, p.id picking_id, p.picker_name, p.start_time pick_start_time, p.end_time pick_end_time, p.sorter_name
        from wave_#waveDbNo# w
        LEFT JOIN wave_picking_#pickingDbNo# p on w.id = p.wave_id and p.company_id = #companyId#
        <!-- -2盲扫时候要拣选中的波次 -->
        <isEmpty property="enableStatus">
            and p.enable_status = 3
        </isEmpty>
        <isNotEmpty property="enableStatus">
            and p.enable_status in
            <iterate property="enableStatus" open="(" conjunction="," close=")"> #enableStatus[]# </iterate>
        </isNotEmpty>
        where w.company_id = #companyId#
        and w.picking_type = 1
        and w.warehouse_id = #warehouseId#
        and w.status = 1 order by w.id
    </select>

    <select id="queryWaveIdAndShortId" resultClass="hashMap">
        select id, short_id
        from wave_#waveDbNo#
        where company_id = #companyId#
        <isNotEmpty property="shortIds">
            and short_id in
            <iterate property="shortIds" open="(" conjunction="," close=")"> #shortIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="waveIds">
            and id in
            <iterate property="waveIds" open="(" conjunction="," close=")"> #waveIds[]# </iterate>
        </isNotEmpty>
    </select>

    <update id="updateNumByWaveId">
        update wave_$dbNo$
        set
        removed_count = removed_count + #removedCount#,
        picked_removed_count = picked_removed_count + #pickedRemovedCount#
        where company_id = #companyId#
        and id = #id#
    </update>
</sqlMap>
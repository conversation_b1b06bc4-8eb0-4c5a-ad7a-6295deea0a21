<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="WaveTrade">
    <typeAlias alias="WaveTrade" type="com.raycloud.dmj.domain.wave.WaveTrade"/>
    <typeAlias alias="Wave" type="com.raycloud.dmj.domain.wave.Wave"/>

    <resultMap id="WaveTradeMap" class="WaveTrade">
        <result property="id" column="id"/>
        <result property="waveId" column="wave_id"/>
        <result property="sid" column="sid"/>
        <result property="code" column="code"/>
        <result property="positionNo" column="position_no"/>
        <result property="tradeWaveStatus" column="trade_wave_status"/>
        <result property="printTimes" column="print_times"/>
        <result property="matchedStatus" column="matched_status"/>
        <result property="packTime" column="pack_time"/>
        <result property="packStaffId" column="pack_staff_id"/>
        <result property="outBusinessType" column="out_business_type"/>
    </resultMap>

    <resultMap id="WaveCountMap" class="Wave">
        <result property="id" column="wave_id"/>
        <result property="unRemoveHasPrintCount" column="un_remove_print_count"/>
        <result property="hasPrintCount" column="print_count"/>
        <result property="unRemoveCount" column="un_remove_count"/>
    </resultMap>

    <resultMap id="WaveTradeCountMap" class="Wave">
        <result property="id" column="wave_id"/>
        <result property="tradesCount" column="trades_count"/>
        <result property="removeTradeCount" column="remove_trade_count"/>
        <result property="hasOutIdTradeCount" column="has_out_id_trade_count"/>
        <result property="unOutIdTradeCount" column="un_out_id_trade_count"/>
    </resultMap>

    <resultMap id="WaveTradeOrderMap" class="WaveTrade">
        <result property="id" column="id"/>
        <result property="waveId" column="wave_id"/>
        <result property="sid" column="sid"/>
        <result property="positionNo" column="position_no"/>
        <result property="tradeWaveStatus" column="trade_wave_status"/>
    </resultMap>

    <resultMap id="WaveTradeOutSidMap" class="WaveTrade">
        <result property="waveId" column="wave_id"/>
        <result property="outSid" column="out_sid"/>
    </resultMap>


    <insert id="insertMerge" parameterClass="java.util.HashMap">
        insert into wave_trade_#column.dbNo#
        <dynamic prepend="(" close=")">
            <isNotNull property="column.companyId" prepend=","> company_id </isNotNull>
            <isNotNull property="column.waveId" prepend=","> wave_id </isNotNull>
            <isNotNull property="column.sid" prepend=","> sid </isNotNull>
            <isNotNull property="column.positionNo" prepend=","> position_no </isNotNull>
            <isNotNull property="column.tradeWaveStatus" prepend=","> trade_wave_status </isNotNull>
            <isNotNull property="column.code" prepend=","> code </isNotNull>
            <isNotNull property="column.matchedStatus" prepend=","> matched_status </isNotNull>
            <isNotNull property="column.packTime" prepend=","> pack_time </isNotNull>
            <isNotNull property="column.packStaffId" prepend=","> pack_staff_id </isNotNull>
            <isNotNull property="column.outBusinessType" prepend=","> out_business_type </isNotNull>
        </dynamic>
        VALUES
        <iterate property="values" conjunction=",">
            (#values[].companyId#, #values[].waveId#
            <isNotNull property="column.sid" prepend=","> #values[].sid# </isNotNull>
            <isNotNull property="column.positionNo" prepend=","> #values[].positionNo# </isNotNull>
            <isNotNull property="column.tradeWaveStatus" prepend=","> #values[].tradeWaveStatus# </isNotNull>
            <isNotNull property="column.code" prepend=","> #values[].code# </isNotNull>
            <isNotNull property="column.matchedStatus" prepend=","> #values[].matchedStatus# </isNotNull>
            <isNotNull property="column.packTime" prepend=","> #values[].packTime# </isNotNull>
            <isNotNull property="column.packStaffId" prepend=","> #values[].packStaffId# </isNotNull>
            <isNotNull property="column.outBusinessType" prepend=","> #values[].outBusinessType# </isNotNull>
            )
        </iterate>
    </insert>


    <insert id="insert" >
        insert into wave_trade_#dbNo#(company_id
        <isNotNull property="waveId" prepend=",">
            wave_id
        </isNotNull>
        <isNotNull property="sid" prepend=",">
            sid
        </isNotNull>
        <isNotNull property="positionNo" prepend=",">
            position_no
        </isNotNull>
        <isNotNull property="tradeWaveStatus" prepend=",">
            trade_wave_status
        </isNotNull>
        <isNotNull property="code" prepend=",">
            code
        </isNotNull>
        <isNotNull property="matchedStatus" prepend=",">
            matched_status
        </isNotNull>
        <isNotNull property="packTime" prepend=",">
            pack_time
        </isNotNull>
        <isNotNull property="packStaffId" prepend=",">
            pack_staff_id
        </isNotNull>
        <isNotNull property="outBusinessType" prepend=",">
            out_business_type
        </isNotNull>
        )
        values(#companyId#
        <isNotNull property="waveId" prepend=",">
            #waveId#
        </isNotNull>
        <isNotNull property="sid" prepend=",">
            #sid#
        </isNotNull>
        <isNotNull property="positionNo" prepend=",">
            #positionNo#
        </isNotNull>
        <isNotNull property="tradeWaveStatus" prepend=",">
            #tradeWaveStatus#
        </isNotNull>
        <isNotNull property="code" prepend=",">
            #code#
        </isNotNull>
        <isNotNull property="matchedStatus" prepend=",">
            #matchedStatus#
        </isNotNull>
        <isNotNull property="packTime" prepend=",">
            #packTime#
        </isNotNull>
        <isNotNull property="packStaffId" prepend=",">
            #packStaffId#
        </isNotNull>
        <isNotNull property="outBusinessType" prepend=",">
            #outBusinessType#
        </isNotNull>
        )
    </insert>
    
    <sql id="likeOrEquals">
        <isEqual property="queryMethod" compareValue="like">like</isEqual>
        <isEqual property="queryMethod" compareValue="=">=</isEqual>
    </sql>

    <sql id="existOrderQuerySql">
        select 1 from order_#orderDbNo# o
        where t.sid = o.belong_sid and o.enable_status > 0 and o.combine_id = 0
    </sql>

    <sql id="queryOrderSql">
        <isEqual property="queryType" compareValue="itemTitle">
            and exists (<include refid="existOrderQuerySql"/>
            and ((o.item_sys_id > 0 and o.sys_title <include refid="likeOrEquals"/> #queryText#) or (<![CDATA[ o.item_sys_id <= 0 ]]> and o.title <include refid="likeOrEquals"/> #queryText#)) )
        </isEqual>
        <isEqual property="queryType" compareValue="outerId">
            and exists (<include refid="existOrderQuerySql"/>
            and ((o.item_sys_id > 0 and o.sys_outer_id <include refid="likeOrEquals"/> #queryText#)
                or (<![CDATA[ o.item_sys_id <= 0 ]]> and (o.outer_iid <include refid="likeOrEquals"/> #queryText# or o.outer_sku_id <include refid="likeOrEquals"/> #queryText#))) )
        </isEqual>
        <isEqual property="queryType" compareValue="skuProps">
            and exists (<include refid="existOrderQuerySql"/> and o.item_sys_id > 0 and o.sys_sku_properties_name <include refid="likeOrEquals"/> #queryText# )
        </isEqual>
        <isEqual property="queryType" compareValue="itemRemark">
            <isEmpty property="itemChangeFlagQuery">
                and exists (<include refid="existOrderQuerySql"/> and o.item_sys_id > 0 and o.sys_item_remark <include refid="likeOrEquals"/> #queryText# )
            </isEmpty>
            <isNotEmpty property="itemChangeFlagQuery">
                and exists(
                    select belong_sid from order_$orderDbNo$ oo
                        left join dmj_sku_#skuDbNo# c on c.company_id = oo.company_id and if(oo.sku_sys_id = -1,0,oo.sku_sys_id) = c.sys_sku_id and c.enable_status = 1 and c.active_status = 1 and c.last_sys_item_id != c.sys_item_id
                        LEFT JOIN dmj_item_#itemDbNo# item ON item.company_id = c.company_id and c.last_sys_item_id = item.sys_item_id
                    where oo.belong_sid = t.sid and oo.combine_id = 0  and oo.enable_status > 0 and oo.item_sys_id > 0
                        and IF(item.remark is not null, item.remark, oo.sys_item_remark) <include refid="likeOrEquals"/> #queryText#
                )
            </isNotEmpty>
        </isEqual>
        <isEqual property="queryType" compareValue="skuRemark">
            and exists (<include refid="existOrderQuerySql"/> and o.item_sys_id > 0 and o.sys_sku_remark <include refid="likeOrEquals"/> #queryText# )
        </isEqual>
        <isEqual property="queryType" compareValue="skuPropAlias">
            and exists (<include refid="existOrderQuerySql"/> and o.item_sys_id > 0 and o.sys_sku_properties_alias <include refid="likeOrEquals"/> #queryText# )
        </isEqual>
        <isEqual property="queryType" compareValue="platSkuProps">
            and exists (<include refid="existOrderQuerySql"/> and o.sku_properties_name <include refid="likeOrEquals"/> #queryText# )
        </isEqual>
        <isEqual property="queryType" compareValue="shortTitle">
            and exists (<include refid="existOrderQuerySql"/> and o.item_sys_id > 0 and o.short_title <include refid="likeOrEquals"/> #queryText# )
        </isEqual>
        <isEqual property="queryType" compareValue="identCode">
            and exists (<include refid="existOrderQuerySql"/> and o.item_sys_id > 0 and o.ident_code <include refid="likeOrEquals"/> #queryText# )
        </isEqual>
        <isEqual property="queryType" compareValue="batchNo">

        </isEqual>
    </sql>
    


    <sql id="fromQueryWaveTradeSql">
        from wave_trade_#dbNo# w join trade_#tradeDbNo# t on w.sid = t.sid
        <isEqual property="postStatus" compareValue="1">
            left join wave_sorting_#sortingDbNo# ws on w.company_id = ws.company_id and w.sid = ws.sid and ws.enable_status = 1
        </isEqual>
        where w.company_id = #companyId#
        and t.enable_status = 1
        <isNotEmpty property="waveId">
            and w.wave_id = #waveId#
        </isNotEmpty>
        <isNotEmpty property="waveIds">
            and w.wave_id in
            <iterate open="(" close=")" conjunction="," property="waveIds">#waveIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="sid">
            and w.sid = #sid#
        </isNotEmpty>
        <isNotEmpty property="tradeSids">
            and w.sid in
            <iterate open="(" close=")" conjunction="," property="tradeSids">#tradeSids[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="positionNo">
            and w.position_no = #positionNo#
        </isNotEmpty>
        <isNotEmpty property="outSid">
            and t.out_sid = #outSid#
        </isNotEmpty>
        <isNotEmpty  property="expressOrderType"  >
            <isEqual property="expressOrderType" compareValue="1">
                AND t.out_sid != '' AND t.out_sid IS NOT NULL
            </isEqual>
            <isEqual property="expressOrderType" compareValue="2">
                AND (t.out_sid = '' OR t.out_sid IS NULL)
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="tid">
            and t.tid = #tid#
        </isNotEmpty>
        <isNotEmpty property="shortId">
            and t.short_id = #shortId#
        </isNotEmpty>
        <isNotEmpty property="matchedStatus">
            <isEqual property="matchedStatus" compareValue="0"> and w.matched_status in (0, 1) </isEqual>
            <isEqual property="matchedStatus" compareValue="1"> and w.matched_status = 2 </isEqual>
        </isNotEmpty>
        <isNotEmpty property="tradeWaveStatus">
            <isEqual property="tradeWaveStatus" compareValue="10">
                and w.trade_wave_status in (0, 1)
            </isEqual>
            <isNotEqual property="tradeWaveStatus" compareValue="10">
                and w.trade_wave_status = #tradeWaveStatus#
            </isNotEqual>
        </isNotEmpty>
        <isNotEmpty property="printStatus">
            <isEqual property="printStatus" compareValue="0">
                and t.express_print_time = '2000-01-01 00:00:00'
            </isEqual>
            <isEqual property="printStatus" compareValue="1">
                and t.express_print_time > '2000-01-01 00:00:00'
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="timeType">
            <isEqual property="timeType" compareValue="printTime">
                <isNotEmpty property="startTime">
                    and t.express_print_time >= #startTime#
                </isNotEmpty>
                <isNotEmpty property="endTime">
                    <![CDATA[ and t.express_print_time <= #endTime# ]]>
                </isNotEmpty>
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="userIds">
            and t.user_id in <iterate property="userIds" open="(" conjunction="," close=")"> #userIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="logisticsCompanyIds">
            and t.logistics_company_id in <iterate property="logisticsCompanyIds" open="(" conjunction="," close=")"> #logisticsCompanyIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="expressTemplateIds">
            and t.template_id in <iterate property="expressTemplateIds" open="(" conjunction="," close=")"> #expressTemplateIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="tradeSourceList">
            and t.source in <iterate property="tradeSourceList" open="(" conjunction="," close=")"> #tradeSourceList[]# </iterate>
        </isNotEmpty>
        <isEqual property="showPrintTimes" compareValue="true">
            <isNotEmpty property="printTimes">
                <isEqual property="printTimes" compareValue="0">
                    and not exists (select 1 from print_trade_log_detail_#printTradeLogDbNo# l where l.sid = t.sid)
                </isEqual>
                <isNotEqual property="printTimes" compareValue="0">
                    and exists (select 1 from print_trade_log_detail_#printTradeLogDbNo# l where l.sid = t.sid group by sid having count(1) = #printTimes#)
                </isNotEqual>
            </isNotEmpty>
            <isNotEmpty property="printNumType">
                and <isEqual property="printNumType" compareValue="1" open="(" close="or">
                        not exists (select 1 from print_trade_log_detail_#printTradeLogDbNo# l where l.sid = t.sid and l.enable_status = 1)
                    </isEqual>
                exists (select 1 from print_trade_log_detail_#printTradeLogDbNo# l where l.sid = t.sid and l.enable_status = 1 group by sid having count(1)
                <isEqual property="printNumType" compareValue="1"> <![CDATA[ < 2 ]]> </isEqual>
                <isEqual property="printNumType" compareValue="2"> <![CDATA[ >=2 ]]> </isEqual>
                <isEqual property="printNumType" compareValue="3"> <![CDATA[ >=3 ]]> </isEqual>
                )
                <isEqual property="printNumType" compareValue="1">)</isEqual>
            </isNotEmpty>
        </isEqual>
        <isNotEmpty property="expressEntry" prepend="and">
            <iterate property="expressEntry" open="(" conjunction="or" close=")">
                <isEqual compareValue="-1" property="expressEntry[].key"> t.template_id = -1 </isEqual>
                <isEqual compareValue="-2" property="expressEntry[].key"> t.template_id > 0 </isEqual>
                <isEqual compareValue="-3" property="expressEntry[].key"> t.template_id = 0 </isEqual>
                <isGreaterEqual compareValue="0" property="expressEntry[].key">
                    <isNotEmpty property="expressEntry[].value">
                        t.template_type = #expressEntry[].key# and t.template_id in <iterate property="expressEntry[].value" open="(" conjunction="," close=")"> #expressEntry[].value[]# </iterate>
                    </isNotEmpty>
                </isGreaterEqual>
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="tagIds" prepend="and" open="(" close=")">
            EXISTS ( SELECT 1 FROM trade_label_#tradeLabelDbNo# tl WHERE tl.company_id = t.company_id AND t.sid = tl.sid AND tl.enable_status = 1 and tl.label_id in <iterate property="tagIds" open="(" conjunction="," close=")"> #tagIds[]# </iterate>)
            or EXISTS ( SELECT 1 FROM trade_label_#tradeLabelDbNo# tl join trade_#tradeDbNo# t1 on t1.company_id = tl.company_id AND t1.sid = tl.sid where tl.enable_status = 1
            and t1.merge_sid = t.sid and t1.enable_status != 0 and t1.company_id = t.company_id and tl.label_id in <iterate property="tagIds" open="(" conjunction="," close=")"> #tagIds[]# </iterate>)
        </isNotEmpty>
        <isNotEmpty property="sellerFlags" prepend="and" open="(" close=")">
            (t.merge_sid = -1 and find_in_set(t.seller_flag, #sellerFlags#))
            or (t.merge_sid > 0 and exists(select 1 from trade_#tradeDbNo# m where m.company_id = t.company_id and m.merge_sid = t.sid and find_in_set(m.seller_flag, #sellerFlags#)))
        </isNotEmpty>
        <isNotEmpty property="queryType">
            <isEqual property="withOrder" compareValue="true">
                <include refid="queryOrderSql"/>
            </isEqual>
            <isEqual property="queryType" compareValue="buyerMessage">
                and t.buyer_message <include refid="likeOrEquals"/> #queryText#
            </isEqual>
            <isEqual property="queryType" compareValue="sellerMemo">
                and t.seller_memo <include refid="likeOrEquals"/> #queryText#
            </isEqual>
            <isEqual property="queryType" compareValue="receiverName">
                and (t.receiver_name = #queryText# or t.receiver_name = #originQueryText#)
            </isEqual>
            <isEqual property="queryType" compareValue="receiverMobile">
                and (t.receiver_mobile = #queryText# or t.receiver_mobile = #originQueryText#)
            </isEqual>
            <isEqual property="queryType" compareValue="mobileTail">
                and (t.mobile_tail = #queryText# or t.mobile_tail = #originQueryText#)
            </isEqual>
            <isEqual property="queryType" compareValue="receiverAddress">
                and t.receiver_address <include refid="likeOrEquals"/> #queryText#
            </isEqual>
            <isEqual property="queryType" compareValue="sysMemo">
                and t.sys_memo <include refid="likeOrEquals"/> #queryText#
            </isEqual>
            <isEqual property="queryType" compareValue="waveUniqueCode">
                <isEqual property="openWaveUniqueCode" compareValue="true">
                    and t.sid in ( select c.sid from wave_unique_code_#uniqueCodeDbNo# c
                    where c.enable_status = 1 and c.company_id = #companyId#
                    and c.unique_code = #queryText# )
                </isEqual>
            </isEqual>
            <isEqual property="queryType" compareValue="itemMultiCode">
                and exists (<include refid="existOrderQuerySql"/> and exists (select 1 from item_multi_code_#itemMultiCodeDbNo# c where c.company_id=#companyId# and c.code=#queryText# and c.sys_item_id=o.item_sys_id and (c.sys_sku_id=o.sku_sys_id or (c.sys_sku_id=0 and o.sku_sys_id=-1))))
            </isEqual>
        </isNotEmpty>
        <isEqual property="postStatus" compareValue="1">
            and ws.post_status = 1
        </isEqual>

        <isNotEmpty property="paymentStart">
            and <![CDATA[ t.payment >= #paymentStart# ]]>
        </isNotEmpty>
        <isNotEmpty property="paymentEnd">
            and <![CDATA[ t.payment <= #paymentEnd# ]]>
        </isNotEmpty>
        <isNotEmpty property="netWeightStart">
            and <![CDATA[ t.net_weight >= #netWeightStart# ]]>
        </isNotEmpty>
        <isNotEmpty property="netWeightEnd">
            and <![CDATA[ t.net_weight <= #netWeightEnd# ]]>
        </isNotEmpty>
        <isNotEmpty property="weightStart">
            and <![CDATA[ t.weight >= #weightStart# ]]>
        </isNotEmpty>
        <isNotEmpty property="weightEnd">
            and <![CDATA[ t.weight <= #weightEnd# ]]>
        </isNotEmpty>
        <isNotEmpty property="volumeStart">
            and <![CDATA[ t.volume >= #volumeStart# ]]>
        </isNotEmpty>
        <isNotEmpty property="volumeEnd">
            and <![CDATA[ t.volume <= #volumeEnd# ]]>
        </isNotEmpty>
        <isNotEmpty property="isExcep">
            and t.is_excep = #isExcep#
        </isNotEmpty>
        <isNotEmpty property="exceptionStatus">
            <iterate property="exceptionStatus" prepend="AND" conjunction="OR" open="(" close=")">
                <isEqual property="exceptionStatus[]" compareValue="EXCEP_HALT"> t.is_halt = 1 </isEqual>
                <isEqual property="exceptionStatus[]" compareValue="EXCEP_REFUND"> t.is_refund = 1 </isEqual>
                <isEqual property="exceptionStatus[]" compareValue="EXCEP_ITEM_UNALLOCATED"> t.stock_status = 'UNALLOCATED' </isEqual>
                <isEqual property="exceptionStatus[]" compareValue="EXCEP_ITEM_RELATION_MODIFIED"> t.stock_status = 'RELATION_MODIFIED' </isEqual>
                <isEqual property="exceptionStatus[]" compareValue="EXCEP_STOCK_INSUFFICIENT"> t.stock_status = 'INSUFFICIENT' </isEqual>
                <isEqual property="exceptionStatus[]" compareValue="EX_CHANGE_ADDRESS"> t.address_changed = 1 </isEqual>
                <isEqual property="exceptionStatus[]" compareValue="EX_CHANGE_ITEM">
                    exists (select 1 from order_#orderDbNo# o where o.company_id = t.company_id and o.sid = t.sid and o.item_changed = 1)
                </isEqual>
                <isEqual property="exceptionStatus[]" compareValue="EX_UNATTAINABLE">
                    ((t.merge_sid = -1 and t.unattainable = 1) or (t.merge_sid > 0 and t.merge_sid in (select merge_sid from trade_not_consign_#tradeDbNo# t1 where t1.merge_sid = t.merge_sid and t1.company_id = #companyId# and t1.merge_sid > 0 and t1.unattainable = 1)))
                </isEqual>
                <isEqual property="exceptionStatus[]" compareValue="EX_UPDATED_SELLERMEMO"> t.seller_memo_update = 2 </isEqual>
                <isEqual property="exceptionStatus[]" compareValue="EX_BLACK"> t.black_buyer_nick = 1 </isEqual>
                <isEqual property="exceptionStatus[]" compareValue="EX_CUSTOM"> t.except_ids != '' </isEqual>
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="exceptIds">
            <iterate property="exceptIds" prepend="AND" conjunction="OR" open="(" close=")">
                LOCATE(#exceptIds[]#,t.except_ids) > 0
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysStatusList">
            <iterate  prepend=" AND " open="(" close=")"  conjunction=" OR " property="sysStatusList">
                <isEqual property="sysStatusList[]" compareValue="WAIT_BUYER_PAY" > t.sys_status = #sysStatusList[]#</isEqual>
                <isEqual property="sysStatusList[]" compareValue="WAIT_AUDIT" > t.sys_status = #sysStatusList[]#</isEqual>
                <isEqual property="sysStatusList[]" compareValue="WAIT_FINANCE_AUDIT" > t.sys_status = #sysStatusList[]#</isEqual>
                <isEqual property="sysStatusList[]" compareValue="WAIT_MANUAL_AUDIT" > t.sys_status = #sysStatusList[]#</isEqual>
                <isEqual property="sysStatusList[]" compareValue="FINISHED_AUDIT" > t.sys_status = #sysStatusList[]#</isEqual>
                <isEqual property="sysStatusList[]" compareValue="SELLER_SEND_GOODS" > t.sys_status = #sysStatusList[]#</isEqual>
                <isEqual property="sysStatusList[]" compareValue="FINISHED" > t.sys_status = #sysStatusList[]#</isEqual>
                <isEqual property="sysStatusList[]" compareValue="CLOSED" > t.sys_status = #sysStatusList[]#</isEqual>
                <isEqual property="sysStatusList[]" compareValue="CANCEL" > t.is_cancel = 1 </isEqual>
                <isEqual property="sysStatusList[]" compareValue="WAIT_EXPRESS_PRINT" > (t.sys_status = 'FINISHED_AUDIT' AND t.express_print_time &lt;= STR_TO_DATE('2000-01-01 00:00:00','%Y-%m-%d %H:%i:%s'))</isEqual>
                <isEqual property="sysStatusList[]" compareValue="WAIT_DELIVERY_PRINT" > (t.sys_status = 'FINISHED_AUDIT' AND t.express_print_time &lt;= STR_TO_DATE('2000-01-01 00:00:00','%Y-%m-%d %H:%i:%s'))</isEqual>
                <isEqual property="sysStatusList[]" compareValue="WAIT_PACKAGE" >
                    (
                        <isNotNull property="tradeConfig.openPackageExamine" >
                            <isEqual property="tradeConfig.openPackageExamine"  compareValue="1"> t.sys_status = 'FINISHED_AUDIT'  </isEqual>
                            <isNotEqual  property="tradeConfig.openPackageExamine" compareValue="1" > t.sys_status is null </isNotEqual>
                        </isNotNull>
                        <isNull property="tradeConfig.openPackageExamine"> t.sys_status is null </isNull>
                        AND t.is_package = 0
                        AND (
                            t.express_print_time &gt; STR_TO_DATE('2000-01-01 00:00:00','%Y-%m-%d %H:%i:%s')
                            <isNotNull property="tradeConfig.openDeliverPrint" >
                                <isEqual property="tradeConfig.openDeliverPrint"  compareValue="1"> AND t.deliver_print_time &gt; STR_TO_DATE('2000-01-01 00:00:00','%Y-%m-%d %H:%i:%s') </isEqual>
                            </isNotNull>
                        )
                    )
                </isEqual>
                <isEqual property="sysStatusList[]" compareValue="WAIT_WEIGHT" >
                    (
                        <isNotNull property="tradeConfig.openPackageWeigh" >
                            <isEqual property="tradeConfig.openPackageWeigh"  compareValue="1"> t.sys_status = 'FINISHED_AUDIT'  </isEqual>
                            <isNotEqual  property="tradeConfig.openPackageWeigh" compareValue="1" > t.sys_status is null </isNotEqual>
                        </isNotNull>
                        <isNull property="tradeConfig.openPackageWeigh"> t.sys_status is null </isNull>
                        AND t.is_weigh = 0
                        <isNotNull property="tradeConfig.openPackageExamine" >
                            <isEqual property="tradeConfig.openPackageExamine"  compareValue="1"> AND t.is_package = 1 </isEqual>
                        </isNotNull>
                        AND (
                            t.express_print_time &gt; STR_TO_DATE('2000-01-01 00:00:00','%Y-%m-%d %H:%i:%s')
                            <isNotNull property="tradeConfig.openDeliverPrint" >
                                <isEqual property="tradeConfig.openDeliverPrint"  compareValue="1"> AND t.deliver_print_time &gt; STR_TO_DATE('2000-01-01 00:00:00','%Y-%m-%d %H:%i:%s') </isEqual>
                            </isNotNull>
                        )
                    )
                </isEqual>
                <isEqual property="sysStatusList[]" compareValue="WAIT_SEND_GOODS" >
                    (
                        t.sys_status = 'FINISHED_AUDIT'
                        AND (
                                (
                                    t.template_id > 0
                                    <isNotNull property="tradeConfig.openPackageWeigh" >
                                        <isEqual property="tradeConfig.openPackageWeigh"  compareValue="1"> AND t.is_weigh = 1 </isEqual>
                                    </isNotNull>
                                    <isNotNull property="tradeConfig.openPackageExamine" >
                                        <isEqual property="tradeConfig.openPackageExamine"  compareValue="1"> AND t.is_package = 1 </isEqual>
                                    </isNotNull>
                                    AND (
                                        t.express_print_time &gt; STR_TO_DATE('2000-01-01 00:00:00','%Y-%m-%d %H:%i:%s')
                                        <isNotNull property="tradeConfig.openDeliverPrint" >
                                            <isEqual property="tradeConfig.openDeliverPrint"  compareValue="1"> AND t.deliver_print_time &gt; STR_TO_DATE('2000-01-01 00:00:00','%Y-%m-%d %H:%i:%s') </isEqual>
                                        </isNotNull>
                                      )
                                )
                                OR t.template_id = 0 OR t.type like 'trade_out%'
                         )
                    )
                </isEqual>
            </iterate>
        </isNotEmpty>
    </sql>

    <select id="queryWaveTradePage" resultMap="WaveTradeMap" timeout="30">
        select w.*,
        <isEqual property="showPrintTimes" compareValue="true"> if(t.print_count = 0, (select count(1) from print_trade_log_detail_#printTradeLogDbNo# l where l.sid = t.sid and l.print_status not in (1, 3)), t.print_count) </isEqual>
        <isNotEqual property="showPrintTimes" compareValue="true">''</isNotEqual>
        print_times
        <include refid="fromQueryWaveTradeSql"/>
        order by
        <isNotEmpty property="field">
            <isEqual property="field" compareValue="printTime">
                t.express_print_time <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="buyerMessage">
                t.buyer_message <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="sellerMemo">
                t.seller_memo <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="sysMemo">
                t.sys_memo <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="modified">
                w.modified <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="netWeight">
                t.net_weight <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="weight">
                t.weight <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="sid">
                w.sid <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="positionNo">
                w.position_no <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="shortId">
                t.short_id <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="itemNum">
                t.item_num <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
            <isEqual property="field" compareValue="itemKindNum">
                t.item_kind_num <isEqual property="order" compareValue="desc">desc</isEqual> ,
            </isEqual>
        </isNotEmpty>
        w.position_no, w.id
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="queryWaveTradeCount" resultClass="Long" timeout="30">
        select count(1)
        <include refid="fromQueryWaveTradeSql"/>
    </select>

    <select id="queryWaveTradeByWaveIds" resultMap="WaveTradeMap">
        select w.*, '' print_times from wave_trade_#dbNo# w
        where company_id = #companyId#
        and wave_id in <iterate property="waveIds" open="(" conjunction="," close=")"> #waveIds[]# </iterate>
        <isNotEmpty property="positionNos" prepend="and">
            position_no in <iterate property="positionNos" open="(" conjunction="," close=")"> #positionNos[]# </iterate>
        </isNotEmpty>
    </select>

    <select id="queryWaveTradeByWaveId4Print" resultMap="WaveTradeMap">
        select w.*, '' print_times from wave_trade_#dbNo# w
        left join trade_#tradeDbNo# t on w.company_id = t.company_id and w.sid = t.sid
        where w.company_id = #companyId#
        and w.wave_id = #waveId#
        and w.trade_wave_status != 2
        <isNotEmpty property="positionNos" prepend="and">
            w.position_no in <iterate property="positionNos" open="(" conjunction="," close=")"> #positionNos[]# </iterate>
        </isNotEmpty>
        and (t.enable_status = 1 or t.enable_status is null)
    </select>

    <select id="queryNotRemoveCount" resultClass="Long">
        select count(1) from wave_trade_#dbNo#
        where company_id = #companyId#
        and wave_id = #waveId#
        and trade_wave_status != 2
    </select>

    <select id="queryNotRemoveCountJoinTrade" resultClass="Long">
        select count(1) from wave_trade_#dbNo# w join trade_#tradeDbNo# t on w.sid = t.sid
        where w.company_id = #companyId#
        and t.enable_status = 1
        and w.wave_id = #waveId#
        and w.trade_wave_status != 2
    </select>

    <select id="queryRemovedSidCount" resultClass="hashMap">
        select
            w.wave_id, count(*) num
        from wave_trade_#dbNo# w
        join trade_#tradeDbNo# t on w.company_id = t.company_id and w.sid = t.sid
        where
            w.company_id = #companyId#
            and w.wave_id in <iterate property="waveIds" open="(" conjunction="," close=")"> #waveIds[]# </iterate>
            and w.trade_wave_status = 2
            and (t.merge_sid = -1 or t.sid = t.merge_sid)
        group by w.company_id,w.wave_id
    </select>

    <select id="queryRemoveWaveSids" resultClass="Long">
        select sid from wave_trade_#dbNo#
        where company_id = #companyId#
        and trade_wave_status = 2
        and wave_id in <iterate property="waveIds" open="(" conjunction="," close=")"> #waveIds[]# </iterate>
    </select>

    <update id="update">
        update wave_trade_#dbNo#
        <dynamic prepend="set">
            <isNotNull property="positionNo" prepend=",">
                position_no = #positionNo#
            </isNotNull>
            <isNotNull property="tradeWaveStatus" prepend=",">
                trade_wave_status = #tradeWaveStatus#
            </isNotNull>
            <isNotNull property="newSid" prepend=",">
                sid = #newSid#
            </isNotNull>
            <isNotNull property="matchedStatus" prepend=",">
                matched_status = #matchedStatus#
            </isNotNull>
            <isNotNull property="packTime" prepend=",">
                pack_time = #packTime#
            </isNotNull>
            <isNotNull property="packStaffId" prepend=",">
                pack_staff_id = #packStaffId#
            </isNotNull>
            <isNotNull property="outBusinessType" prepend=",">
                out_business_type = #outBusinessType#
            </isNotNull>
        </dynamic>
        where company_id = #companyId#
        <isNotEmpty property="id">
            and id = #id#
        </isNotEmpty>
        <isEmpty property="id">
            and sid = #sid# and wave_id = #waveId#
        </isEmpty>
    </update>

    <select id="queryByWaveIdAndSid" resultMap="WaveTradeMap">
        select w.*, '' print_times from wave_trade_#dbNo# w where company_id = #companyId#
        and wave_id = #waveId#
        <isNotEmpty property="sid">
            and sid = #sid#
        </isNotEmpty>
        <isNotEmpty property="sids">
            and sid in <iterate property="sids" open="(" conjunction="," close=")"> #sids[]# </iterate>
        </isNotEmpty>
    </select>

    <select id="queryWavePositionNum" resultClass="com.raycloud.dmj.domain.wave.model.WavePosition">
        select wave_id waveId,position_no positionNo,count(1)  positionSidNum
        from  wave_trade_#dbNo#  where company_id = #companyId#
        <isNotEmpty property="wavePositions">
            <iterate property="wavePositions" open="(" conjunction=" or " close=")"  prepend=" and ">
                (wave_id = #wavePositions[].waveId#   and  position_no =  #wavePositions[].positionNo# )
            </iterate>
        </isNotEmpty>
        group by    wave_id,position_no order by null
    </select>

    <select id="queryMaxPositionNoByWaveId" resultClass="Integer">
        select max(position_no) FROM wave_trade_#dbNo# where company_id = #companyId# AND wave_id = #waveId#
    </select>

    <update id="updateTradeWaveId">
        update trade_#dbNo# set wave_id = #waveId#
        where company_id = #companyId# and sid = #sid#
    </update>

    <update id="updateNotConsignTradeWaveId">
        update trade_not_consign_#dbNo# set wave_id = #waveId#
        where company_id = #companyId# and sid = #sid#
    </update>

    <select id="statWavePack" resultClass="com.raycloud.dmj.domain.wave.WaveStat">
        select waveId, packBeginTime, packEndTime, packTradeCount, (select pack_staff_id from wave_trade_#dbNo# where id = m_id) packStaffId from (
            select wave_id waveId, min(pack_time) packBeginTime, max(pack_time) packEndTime, count(1) packTradeCount, max(id) m_id
            from wave_trade_#dbNo#
            where company_id = #companyId# and wave_id = #waveId#
            and trade_wave_status in (0, 1)
            and pack_time > '2000-01-01 00:00:00'
        ) a
    </select>

    <select id="queryClosedWaveTradeBySysItemSkuId" resultClass="Long">
        select w.sid from wave_trade_#dbNo# w join order_#orderDbNo# o
        on w.sid = o.sid and w.company_id = o.company_id
        where w.company_id = #companyId#
            and w.wave_id = #waveId#
            and o.enable_status = 1
            and o.item_sys_id = #sysItemId#
            and o.sku_sys_id = #sysSkuId#
            and o.sys_status = 'CLOSED'
        limit 1
    </select>

    <select id="queryRemovedWaveTradeBySysItemSkuId" resultMap="WaveTradeOrderMap">
        select w.* from wave_trade_#dbNo# w
        join order_#orderDbNo# o
        on w.sid = o.belong_sid and w.company_id = o.company_id
        where w.company_id = #companyId#
        and w.wave_id = #waveId#
        and w.trade_wave_status = 2
        and o.enable_status = 1
        and o.item_sys_id = #sysItemId#
        and o.sku_sys_id = #sysSkuId#
    </select>

    <select id="queryRemovedWaveTrade" resultMap="WaveTradeOrderMap">
        select w.* from wave_trade_#dbNo# w
        where w.company_id = #companyId#
        and w.wave_id = #waveId#
        and w.trade_wave_status = 2
    </select>


    <select id="querySidsBySectionAndWaveId" resultClass="Long">
        select sid from wave_trade_#dbNo#
        where company_id = #companyId#
        and trade_wave_status in (0,1)
        <isNotEmpty property="waveId">
            and wave_id =  #waveId#
        </isNotEmpty>
        <isNotEmpty property="firstSid">
            <isNotEmpty property="lastSid">
                and position_no between (select position_no from wave_trade_#dbNo# where company_id = #companyId# and sid = #firstSid# and wave_id =  #waveId#) and (select position_no from wave_trade_#dbNo# where company_id = #companyId# and sid = #lastSid# and wave_id =  #waveId#)
            </isNotEmpty>
        </isNotEmpty>
    </select>

    <select id="queryAllStatusSidsBySectionAndWaveId" resultClass="Long">
        select sid from wave_trade_#dbNo#
        where company_id = #companyId#
        <isNotEmpty property="statusList">
            and trade_wave_status in
            <iterate property="statusList" open="(" conjunction="," close=")">
                #statusList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="waveId">
            and wave_id =  #waveId#
        </isNotEmpty>
        <isNotEmpty property="firstSid">
            <isNotEmpty property="lastSid">
                and position_no between (select position_no from wave_trade_#dbNo# where company_id = #companyId# and sid = #firstSid# and wave_id =  #waveId#) and (select position_no from wave_trade_#dbNo# where company_id = #companyId# and sid = #lastSid# and wave_id =  #waveId#)
            </isNotEmpty>
        </isNotEmpty>
    </select>

    <select id="queryExcepAndRemoveWaveTradeCount" resultClass="Long" timeout="30">
        select count(*)
        from wave_trade_#dbNo# w join trade_#tradeDbNo# t on w.sid = t.sid
        where w.company_id = #companyId# and w.wave_id = #waveId#
        and t.enable_status = 1 and (t.is_excep = 1 or w.trade_wave_status = 2)
    </select>



    <select id="queryExcepAndRemoveWaveTrade" resultMap="WaveTradeMap" timeout="30">
        select w.*, '' print_times
        from wave_trade_#dbNo# w join trade_#tradeDbNo# t on w.sid = t.sid
        where w.company_id = #companyId# and w.wave_id = #waveId#
        and t.enable_status = 1 and (t.is_excep = 1 or w.trade_wave_status = 2)
        order by
        w.position_no, w.id
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="queryCanceledUnRemoveSids" resultClass="Long" timeout="30">
        select t.sid from trade_#tradeDbNo# t
        join wave_#waveDbNo# w ON w.company_id = t.company_id and t.wave_id = w.id
        where t.company_id = #companyId#
        and t.enable_status = 1
        and t.wave_id !=0
        and w.status =4
    </select>
    <select id="queryPrintedTradeNum" resultClass="hashMap">
        select w.wave_id, count(*) num
        from wave_trade_#dbNo# w join trade_#tradeDbNo# t on w.company_id = t.company_id and w.sid = t.sid
        where w.company_id = #companyId#
        <isNotEmpty property="waveIds">
            and w.wave_id in <iterate property="waveIds" open="(" conjunction="," close=")"> #waveIds[]# </iterate>
        </isNotEmpty>
        and w.trade_wave_status != 2
        and t.enable_status = 1
        and t.express_print_time > '2000-01-01 00:00:00'
        and (t.merge_sid = -1 or t.sid = t.merge_sid)
        group by w.company_id, w.wave_id
    </select>

    <select id="queryPrintPickWaveTradesByWaveIds" resultClass="WaveTrade">
        select
        t.sid sid,
        t.sys_status sysStatus,
        t.sub_source subSource,
        t.merge_sid mergeSid,
        t.is_excep isExcep,
        t.tag_ids tagIds,
        w.trade_wave_status tradeWaveStatus,
        w.wave_id waveId
        from wave_trade_#dbNo# w
        join trade_#tradeDbNo# t  on w.sid = t.sid and w.company_id = t.company_id
        where w.company_id = #companyId#
        <isEqual property="pickBillGetDataFromTrade" compareValue="true">
            and t.wave_id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
        </isEqual>
        <isEqual property="pickBillGetDataFromTrade" compareValue="false">
            and w.wave_id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
        </isEqual>
    </select>

    <select id="queryWaveTradesByWaveIds" resultClass="WaveTrade" timeout="30">
        select
        t.sid sid,
        t.express_print_time expressPrintTime,
        w.trade_wave_status tradeWaveStatus,
        w.wave_id waveId
        from wave_trade_#dbNo# w
        join trade_#tradeDbNo# t  on w.sid = t.sid and w.company_id = t.company_id
        where w.company_id = #companyId# and w.wave_id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
    </select>

    <select id="queryWaveStatCountByWaveIds" resultMap="WaveCountMap">
        select
        w.wave_id wave_id,
        sum(case when w.trade_wave_status !=2 then 1 else 0 end) un_remove_count,
        sum(case when t.express_print_time > '2000-01-01 00:00:00' then 1 else 0 end) print_count,
        sum(case when w.trade_wave_status !=2 and t.express_print_time > '2000-01-01 00:00:00' then 1 else 0 end) un_remove_print_count
        from wave_trade_#dbNo# w
        join trade_#tradeDbNo# t  on w.sid = t.sid and w.company_id = t.company_id
        where w.company_id = #companyId# and w.wave_id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
        group by w.wave_id
    </select>

    <select id="queryWaveOutSidStatus" resultMap="WaveTradeCountMap">
        select
        w.wave_id wave_id,
        count(*) trades_count,
        sum(case when w.trade_wave_status =2 then 1 else 0 end) remove_trade_count,
        sum(case when w.trade_wave_status !=2 and t.out_sid is not null and t.out_sid != '' then 1 else 0 end) has_out_id_trade_count,
        sum(case when w.trade_wave_status !=2 and t.out_sid is null or t.out_sid = '' then 1 else 0 end) un_out_id_trade_count
        from wave_trade_#dbNo# w join trade_#tradeDbNo# t  on w.sid = t.sid and w.company_id = t.company_id
        where w.company_id = #companyId#  and t.enable_status = 1 and w.wave_id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
        group by w.wave_id
    </select>

</sqlMap>
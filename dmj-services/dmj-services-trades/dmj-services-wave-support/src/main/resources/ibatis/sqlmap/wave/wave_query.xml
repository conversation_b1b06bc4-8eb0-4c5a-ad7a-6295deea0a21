<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="WaveQuery">
    <typeAlias alias="TbTrade" type="com.raycloud.dmj.domain.trades.TbTrade"/>
    <typeAlias alias="TbOrder" type="com.raycloud.dmj.domain.trades.TbOrder"/>
    <typeAlias alias="GoodsSectionOrderRecord" type="com.raycloud.dmj.domain.wms.GoodsSectionOrderRecord"/>
    <typeAlias alias="HotSaleTrade" type="com.raycloud.dmj.domain.trades.bo.HotSaleTradeInfo"/>
    <typeAlias alias="TradeExt" type="com.raycloud.dmj.domain.trades.TradeExt"/>
    <typeAlias alias="ItemSupplierBridge" type="com.raycloud.dmj.domain.item.ItemSupplierBridge"/>
    <typeAlias alias="Wave" type="com.raycloud.dmj.domain.wave.Wave"/>

    <resultMap id="waveQueryTradeMap" class="TbTrade">
        <result property="sid" column="sid"/>
        <result property="shortId" column="short_id"/>
        <result property="mergeSid" column="merge_sid"/>
        <result property="waveId" column="wave_id"/>
        <result property="userId" column="user_id"/>
        <result property="payTime" column="pay_time"/>
        <result property="outSid" column="out_sid"/>
        <result property="payment" column="payment"/>
        <result property="postFee" column="post_fee"/>
        <result property="taxFee" column="tax_fee"/>
        <result property="discountFee" column="discount_fee"/>
        <result property="templateId" column="template_id"/>
        <result property="templateType" column="template_type"/>
        <result property="isExcep" column="is_excep"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="sysStatus" column="sys_status"/>
        <result property="needInvoice" column="need_invoice"/>
        <result property="netWeight" column="net_weight" />
        <result property="mergeType" column="merge_type" />
        <result property="mergeSid" column="merge_sid" />
        <result property="isUrgent" column="is_urgent" />
        <result property="buyerMessage" column="buyer_message" />
        <result property="sellerMemo" column="seller_memo" />
        <result property="tagIds" column="tag_ids" />
        <result property="type" column="type" />
        <result property="printCount" column="print_count" />
        <result property="stockRegionType" column="stock_region_type"/>
        <result property="sourceId" column="source_id"/>
        <result property="taobaoId" column="taobao_id"/>
        <result property="sellerFlag" column="seller_flag" />
        <result property="volume" column="volume"/>
        <result property="isPackage" column="is_package"/>
        <result property="source" column="source"/>
        <result property="subSource" column="sub_source"/>
        <result property="sysMemo" column="sys_memo" />
        <result property="grossProfit" column="gross_profit" />
        <result property="stockStatus" column="stock_status" />
        <result property="receiverState" column="receiver_state" />
        <result property="receiverCity" column="receiver_city" />
        <result property="receiverDistrict" column="receiver_district" />
        <result property="logisticsCompanyId" column="logistics_company_id" />
        <result property="timeoutActionTime" column="timeout_action_time" />
        <result property="sellerNick" column="seller_nick" />
        <result property="poNos" column="po_nos" />
        <result property="splitType" column="split_type"/>
        <result property="expressPrintTime" column="express_print_time"/>
        <result property="isWeigh" column="is_weigh"/>
    </resultMap>

    <resultMap id="waveQueryOrderMap" class="TbOrder">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="itemSysId" column="item_sys_id"/>
        <result property="skuSysId" column="sku_sys_id"/>
        <result property="sysStatus" column="sys_status"/>
        <result property="num" column="num"/>
        <result property="giftNum" column="gift_num"/>
        <result property="payment" column="payment"/>
        <result property="price" column="price"/>
        <result property="type" column="type"/>
        <result property="isVirtual" column="is_virtual"/>
        <result property="sysOuterId" column="sys_outer_id"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="combineId" column="combine_id"/>
        <result property="netWeight" column="net_weight" />
        <result property="isPick" column="is_pick" />
        <result property="stockNum" column="stock_num" />
        <result property="source" column="source"/>
        <result property="belongSid" column="belong_sid"/>
        <result property="sysSkuPropertiesName" column="sys_sku_properties_name"/>
        <result property="shortTitle" column="short_title"/>
        <result property="sysTitle" column="sys_title"/>
        <result property="companyId" column="company_id"/>
        <result property="nonConsign" column="non_consign"/>
        <result property="volume" column="volume"/>
    </resultMap>

    <resultMap id="goodsSectionOrderRecordMap" class="GoodsSectionOrderRecord">
        <result property="orderId" column="order_id"/>
        <result property="goodsSectionId" column="goods_section_id"/>
        <result property="goodsSectionCode" column="goods_section_code"/>
        <result property="applyNum" column="apply_num"/>
    </resultMap>

    <resultMap id="TradeExtResultMap" class="TradeExt">
        <result property="sid" column="sid"/>
        <result property="deliveryType" column="delivery_type"/>
        <result property="shipType" column="ship_type"/>
        <result property="cooperationNo" column="cooperation_no"/>
        <result property="sellSite" column="sell_site"></result>
    </resultMap>

    <resultMap id="hotSaleTradeMap" class="HotSaleTrade">
        <result property="sid" column="sid"/>
        <result property="outSid" column="out_sid"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="userId" column="user_id"/>
        <result property="payTime" column="pay_time"/>
        <result property="templateId" column="template_id"/>
        <result property="itemSysId" column="item_sys_id"/>
        <result property="skuSysId" column="sku_sys_id"/>
        <result property="sysOuterId" column="sys_outer_id"/>
        <result property="num" column="num"/>
        <result property="isUrgent" column="is_urgent"/>
        <result property="timeoutActionTime" column="timeout_action_time"/>
        <result property="logisticsCompanyId" column="logistics_company_id"/>
    </resultMap>

    <resultMap id="itemSupplierBridgeMap" class="ItemSupplierBridge">
        <result property="id" column="id"/>
        <result property="sysItemId" column="sys_item_id"/>
        <result property="sysSkuId" column="sys_sku_id"/>
        <result property="companyId" column="company_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="supplierPurchasePrice" column="supplier_purchase_price"/>
    </resultMap>

    <resultMap id="waveQuerySimpleTradeMap" class="TbTrade">
        <result property="sid" column="sid"/>
        <result property="shortId" column="short_id"/>
        <result property="templateId" column="template_id"/>
        <result property="templateType" column="template_type"/>
        <result property="itemNum" column="item_num"/>
        <result property="logisticsCompanyId" column="logistics_company_id" />
        <result property="netWeight" column="net_weight" />
        <result property="buyerMessage" column="buyer_message" />
        <result property="sellerMemo" column="seller_memo" />
        <result property="mergeSid" column="merge_sid" />
        <result property="mergeType" column="merge_type" />
        <result property="userId" column="user_id"/>
        <result property="tid" column = "tid"/>
        <result property="outSid" column = "out_sid"/>
    </resultMap>


    <resultMap id="waveQueryExcepOrderMap" class="TbOrder">
        <result property="sid" column="sid"/>
        <result property="sysStatus" column="sys_status"/>
        <result property="sysOuterId" column="sys_outer_id"/>
        <result property="sysSkuPropertiesName" column="sys_sku_properties_name"/>
        <result property="sysSkuPropertiesAlias" column="sys_sku_properties_alias"/>
        <result property="sysTitle" column="sys_title"/>
        <result property="sysPicPath" column="sys_pic_path"/>

    </resultMap>
    <resultMap id="waveStockOrderRecord" class="com.raycloud.dmj.domain.stock.StockOrderRecord">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="sid" column="sid"/>
        <result property="sysItemId" column="sys_item_id"/>
        <result property="sysSkuId" column="sys_sku_id"/>
        <result property="outerId" column="outer_id"/>
        <result property="num" column="num"/>
        <result property="stockNum" column="stock_num"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="type" column="type"/>
    </resultMap>



    <resultMap id="tradeWarehouseMap" class="TbTrade">
        <result property="sid" column="sid"/>
        <result property="warehouseId" column="warehouse_id"/>
    </resultMap>


    <select id="queryEffectWaveIds" resultClass="Long">
        select distinct wave_id
        from trade_$dbNo$
        where company_id=#companyId# and sys_status='FINISHED_AUDIT' AND is_excep=0 and is_cancel=0 and type not like 'trade_out%' and enable_status=1 and wave_id!=0
    </select>

    <sql id="queryFields">
        t.sid, t.short_id, t.merge_sid, t.wave_id, t.user_id, t.pay_time, t.out_sid, t.payment, t.post_fee, t.tax_fee, t.discount_fee, t.template_id, t.template_type, t.is_excep, t.warehouse_id, t.sys_status, t.need_invoice, t.net_weight, t.merge_type, t.merge_sid, t.is_urgent, t.buyer_message, t.seller_memo, t.tag_ids, t.type, t.print_count , t.stock_region_type, source_id, t.taobao_id, t.seller_flag, t.volume , t.is_package, t.source, t.sub_source, t.sys_memo, t.gross_profit, t.stock_status, t.receiver_state, t.receiver_city, t.receiver_district, t.logistics_company_id, t.timeout_action_time, t.seller_nick,t.po_nos,t.split_type,t.express_print_time,t.is_weigh
    </sql>

    <sql id="dateRangeCondition">
        <isNotEmpty property="params.payTimeRange">
            <isEqual property="params.payTimeRange.type" compareValue="1">
                <isNotEmpty property="params.payTimeRange.start">
                    and <![CDATA[ t.pay_time >= #params.payTimeRange.start#]]>
                </isNotEmpty>
                <isNotEmpty property="params.payTimeRange.end">
                    and <![CDATA[ t.pay_time <= #params.payTimeRange.end#]]>
                </isNotEmpty>
            </isEqual>
            <isEqual property="params.payTimeRange.type" compareValue="2">
                <isNotEmpty property="params.payTimeRange.start">
                    and <![CDATA[ t.audit_time >= #params.payTimeRange.start#]]>
                </isNotEmpty>
                <isNotEmpty property="params.payTimeRange.end">
                    and <![CDATA[ t.audit_time <= #params.payTimeRange.end#]]>
                </isNotEmpty>
            </isEqual>
        </isNotEmpty>
    </sql>

    <sql id="waveCon">
        t.company_id = #companyId# and t.enable_status = 1 and t.is_cancel = 0 and t.type not like 'trade_out%'
        <isNull property="params.includeScalping">
            and t.scalping = 0
        </isNull>
        <isNotEqual property="params.withExcep" compareValue="true">
            and (is_excep = 0
            <isEqual property="params.openExcepGenerateWave" compareValue="1">
                or (t.item_excep =2 and t.is_halt = 0 AND t.unattainable = 0 AND t.is_refund = 0  AND t.excep=0 and t.except_ids = '')
            </isEqual>)
        </isNotEqual>
        <isNotEqual property="params.ignoreStatus" compareValue="true">
            and t.sys_status = 'FINISHED_AUDIT'
        </isNotEqual>
        <isNotNull property="params.waveId">
            <isEqual property="params.waveId" compareValue="-1">
                and t.wave_id > 0
            </isEqual>
            <isNotEqual property="params.waveId" compareValue="-1">
                and t.wave_id = #params.waveId#
            </isNotEqual>
        </isNotNull>
        <isNotEmpty property="params.sids">
            and t.sid in
            <iterate property="params.sids" open="(" conjunction="," close=")">
                #params.sids[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="params.shortIdsList">
            and t.short_id in
            <iterate property="params.shortIdsList" open="(" conjunction="," close=")">
                #params.shortIdsList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="params.outSids">
            and t.out_sid in
            <iterate property="params.outSids" open="(" conjunction="," close=")">
                #params.outSids[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="params.waveIds">
            and t.wave_id in
            <iterate property="params.waveIds" open="(" conjunction="," close=")">
                #params.waveIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="params.notTagIds" prepend="and">
            not exists (select 1 from trade_label_#tradeLabelDbNo# tl  where tl.company_id = t.company_id and t.sid = tl.sid and tl.enable_status = 1
            and tl.label_id in  <iterate property="params.notTagIds" open="(" conjunction="," close=")"> #params.notTagIds[]# </iterate>
            )
        </isNotEmpty>
        <isNotEmpty property="params.notInWaveReason" prepend="and">
            exists (select 1 from wave_not_in_reason_#waveNotInReasonDb# r where r.company_id = t.company_id and r.type = 0 and r.sid = t.sid
            and r.reason like CONCAT('%', #params.notInWaveReason#, '%')
            )
        </isNotEmpty>
        <isNotEmpty property="params.ignoreWaveIds">
            and t.wave_id not in
            <iterate property="params.ignoreWaveIds" open="(" conjunction="," close=")">
                #params.ignoreWaveIds[]#
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="params.expressSpec">
            and (t.template_id > 0 or t.type = 'dangkou')
        </isNotEmpty>
        <isNotEmpty property="params.warehouseId">
            and t.warehouse_id = #params.warehouseId#
        </isNotEmpty>
        <isNotEmpty property="params.queryWarehouseIds">
            and t.warehouse_id in
            <iterate property="params.queryWarehouseIds" open="(" conjunction="," close=")">
                #params.queryWarehouseIds[]#
            </iterate>
        </isNotEmpty>
        <isEqual property="params.printed" compareValue="true">
            and express_print_time > '2000-01-01 00:00:00'
        </isEqual>
        <isEqual property="params.printed" compareValue="false">
            and express_print_time = '2000-01-01 00:00:00'
        </isEqual>
        <isNotNull property="params.userId">
            and t.user_id=#params.userId#
        </isNotNull>
        <isNotEmpty property="params.userIds">
            and t.user_id in
            <iterate property="params.userIds" open="(" conjunction="," close=")">
                #params.userIds[]#
            </iterate>
        </isNotEmpty>
        <isNotNull property="params.key" prepend="and">
            (t.sid = #params.key# or t.out_sid = #params.key#)
        </isNotNull>
        <isEqual property="params.multi" compareValue="true">
            and (t.item_num > 1 or exists (select 1 from order_not_consign_#orderDbNo# o where o.sid = t.sid and o.type = 2 ))
        </isEqual>
        <include refid="dateRangeCondition"/>
        <isNotEmpty property="params.isExcep">
            and t.is_excep = #params.isExcep#
        </isNotEmpty>
        <isNotEmpty property="params.expressTemplateType">
            and t.template_type = #params.expressTemplateType#
        </isNotEmpty>
        <isNotEmpty property="params.isPackage">
            and t.is_package = #params.isPackage#
        </isNotEmpty>
        <isNotEmpty property="params.templateIds">
            and t.template_id in
            <iterate property="params.templateIds" open="(" conjunction="," close=")">
                #params.templateIds[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="params.logisticsCompanyIdList">
            and t.logistics_company_id in
            <iterate property="params.logisticsCompanyIdList" open="(" conjunction="," close=")">
                #params.logisticsCompanyIdList[]#
            </iterate>
        </isNotEmpty>
    </sql>

    <select id="queryTradesBySids" resultMap="waveQueryTradeMap">
        select <include refid="queryFields"/>
        from trade_$tradeDbNo$ t
        WHERE company_id = #companyId# AND sid in
        <iterate conjunction="," open="(" close=")" property="sids"> #sids[]# </iterate>
        AND enable_status &gt; 0
    </select>

    <sql id="checkSkuWaveItemNum">
        and (
            select
                sum(onc.num)
            from
                order_not_consign_#orderDbNo# onc
            where
                onc.company_id = t.company_id
                and onc.belong_sid = t.sid
                and onc.enable_status = 1
                and onc.is_pick in (1, 2)
                and onc.is_virtual = 0
        <isEqual property="checkIsPickWhiteList" compareValue="true">
            and not exists (
                select
                    1
                from
                    order_item_tag_#orderDbNo# it
                where
                    it.company_id = onc.company_id
                    and it.order_id = onc.`id`
                    and it.item_tag_id = 1000000004
                    and it.enable_status = 1
            )
        </isEqual>
        )
        <isNotEqual property="params.pickingTypes" compareValue="2"> = 1 </isNotEqual>
        <isEqual property="params.pickingTypes" compareValue="2"> &gt; 1 </isEqual>
    </sql>

    <select id="queryTradesWarehouseBySids" resultMap="tradeWarehouseMap">
        select t.sid,t.warehouse_id
        from trade_$tradeDbNo$ t
        WHERE company_id = #companyId# AND sid in
        <iterate conjunction="," open="(" close=")" property="sids"> #sids[]# </iterate>
        AND enable_status &gt; 0
    </select>



    <sql id="queryWaveTradesSql">
        <isEqual property="isOpenSeparation" compareValue="true">
            from trade_not_consign_$tradeDbNo$ t
        </isEqual>
        <isEqual property="isOpenSeparation" compareValue="false">
            from trade_$tradeDbNo$ t
        </isEqual>
        where <include refid="waveCon"/>
        <isEqual property="params.pickingTypes" compareValue="2">
            <include refid="checkSkuWaveItemNum"/>
        </isEqual>
        <isNotEmpty property="params.lastMaxSid">
            and sid > #params.lastMaxSid#
        </isNotEmpty>
        <isNotNull property="params.startRow">
            order by sid
            limit #params.startRow#,#params.pageSize#
        </isNotNull>
    </sql>

    <select id="queryWaveTrades" resultMap="waveQueryTradeMap">
        select <include refid="queryFields"/>
        <include refid="queryWaveTradesSql"/>
    </select>

    <select id="queryWaveSidsV2" resultClass="Long">
        /*FORCE_MASTER*/ select sid
        <include refid="queryWaveTradesSql"/>
    </select>

    <select id="queryWaveTradesV2" resultMap="waveQueryTradeMap">
        /*FORCE_MASTER*/ select <include refid="queryFields"/>
        from trade_not_consign_$tradeDbNo$ t
        where t.sid in
        <iterate property="sids" open="(" conjunction="," close=")">
            #sids[]#
        </iterate>
    </select>

    <select id="queryWaveSids" resultClass="Long">
        select sid from trade_<isEqual property="params.useTradeNotConsignTable" compareValue="true">not_consign_</isEqual>$tradeDbNo$ t
        where <include refid="waveCon"/>
        <isEqual property="params.groupByTrade" compareValue="true">
            group by template_id,template_type,user_id,logistics_company_id
        </isEqual>
        <isNotNull property="params.startRow">
            order by
            <isNotNull property="params.itemNumSort">
                <isEqual property="params.itemNumSort" compareValue="desc"> t.item_num desc, </isEqual>
                <isNotEqual property="params.itemNumSort" compareValue="desc"> t.item_num asc, </isNotEqual>
            </isNotNull>
            t.pay_time, t.sid
            limit #params.startRow#,#params.pageSize#
        </isNotNull>
        <isNull property="params.startRow">
            order by
            <isNotNull property="params.itemNumSort">
                <isEqual property="params.itemNumSort" compareValue="desc"> t.item_num desc, </isEqual>
                <isNotEqual property="params.itemNumSort" compareValue="desc"> t.item_num asc, </isNotEqual>
            </isNotNull>
            t.pay_time, t.sid
        </isNull>
    </select>

    <select id="querySidsByWaveIds" resultClass="Long">
        select sid FROM trade_#tradeDbNo#
        WHERE company_id = #companyId#
        AND warehouse_id = #warehouseId#
        AND wave_id in
        <iterate property="waveIds" open="(" conjunction="," close=")">
            #waveIds[]#
        </iterate>
    </select>

    <select id="queryItemNumByWaveIds" resultClass="Integer">
        select sum(item_num) FROM trade_#tradeDbNo#
        WHERE company_id = #companyId#
        AND warehouse_id = #warehouseId#
        AND wave_id in
        <iterate property="waveIds" open="(" conjunction="," close=")">
            #waveIds[]#
        </iterate>
    </select>

    <select id="queryByMergeSids" resultMap="waveQueryTradeMap">
        select <include refid="queryFields"/>
        FROM trade_#tradeDbNo# t WHERE company_id = #companyId# AND merge_sid in
        <iterate conjunction="," open="(" close=")" property="mergeSids"> #mergeSids[]# </iterate>
        AND enable_status &gt; 0 and sys_status = 'FINISHED_AUDIT'
    </select>

    <select id="queryFullByMergeSids" resultClass="com.raycloud.dmj.domain.trades.TbTrade">
        select sid, merge_sid mergeSid, sys_status sysStatus
        FROM trade_#tradeDbNo# WHERE company_id = #companyId# AND merge_sid in
        <iterate conjunction="," open="(" close=")" property="mergeSids"> #mergeSids[]# </iterate>
        AND enable_status &gt; 0
    </select>

    <select id="queryWaveTradeCount" resultClass="Long">
        select count(1) from trade_$tradeDbNo$ t
        where <include refid="waveCon"/>
    </select>

    <select id="statWave" resultClass="com.raycloud.dmj.domain.wave.Wave">
        select count(sid) as tradesCount,sum(item_num) as itemCount from trade_<isNotEqual property="params.accurateStatWaveTradeNum" compareValue="true">not_consign_</isNotEqual>$tradeDbNo$ t
        where <include refid="waveCon"/>
    </select>

    <select id="countStatWave" resultClass="com.raycloud.dmj.domain.wave.TradeWaveResult">
        select
        count(sid) as tradesCount,
        sum(case when wave_id > 0 then 1 else 0 end) as tradesWaveCount
        from trade_not_consign_$tradeDbNo$ t
        where <include refid="waveCon"/>
    </select>

    <select id="queryNotFinishTradeCount" resultClass="Integer">
        select count(sid) from trade_#tradeDbNo# t
        where company_id = #companyId#
        and warehouse_id = #warehouseId#
        and wave_id = #waveId#
        <isEqual property="openPackageExamine" compareValue="true">
            <isEqual property="finishStatus" compareValue="0">
                and is_package = 0
                and sys_status in ('FINISHED_AUDIT')
            </isEqual>
            <isEqual property="finishStatus" compareValue="1">
                and is_package = 1
            </isEqual>
        </isEqual>
        <isEqual property="openPackageExamine" compareValue="false">
            <isEqual property="finishStatus" compareValue="0">
                and express_print_time = '2000-01-01 00:00:00'
                and sys_status = 'FINISHED_AUDIT'
            </isEqual>
            <isEqual property="finishStatus" compareValue="1">
                and express_print_time > '2000-01-01 00:00:00'
            </isEqual>
        </isEqual>
        and enable_status > 0
        and is_cancel = 0
    </select>

    <select id="queryNeedRemoveSids" resultClass="com.raycloud.dmj.domain.wave.AssoWaveTrade">
        select t.sid, t.wave_id waveId, t.enable_status enableStatus from trade_not_consign_#tradeDbNo# t
        join wave_trade_#waveTradeDbNo# w on t.company_id = w.company_id and t.wave_id = w.wave_id and t.sid = w.sid
        where t.company_id = #companyId#
        and t.wave_id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
        and t.enable_status in (1,2)
        and (
                (t.sys_status in ('WAIT_BUYER_PAY', 'WAIT_AUDIT', 'WAIT_FINANCE_AUDIT', 'SELLER_SEND_GOODS', 'FINISHED', 'CLOSED', 'CANCEL') and t.is_cancel = 0)
                or t.is_cancel = 1
            )
    </select>

    <select id="queryTradeShouldRemoveWaveButNotSids" resultClass="Long">
        select
            sid
        from trade_not_consign_#tradeDbNo#
        where company_id = #companyId#
            and wave_id != 0
            and enable_status = 1
            and sys_status in ('WAIT_BUYER_PAY', 'WAIT_AUDIT', 'WAIT_FINANCE_AUDIT')
            and is_cancel = 0

        union all

        select
            t.sid
        from wave_#waveDbNo# w
        left join trade_not_consign_#tradeDbNo# t
            on w.company_id = t.company_id and w.warehouse_id = t.warehouse_id and w.id = t.wave_id
        left join wave_rule wr on wr.company_id = w.company_id and w.rule_id = wr.id
        where w.company_id = #companyId#
            and wr.rule_type != 3
            and w.created > DATE_ADD(SYSDATE(), INTERVAL -3 DAY)
            and (w.status = 4 or (w.status = 3 and <![CDATA[ w.finished < DATE_SUB(NOW(), INTERVAL 2 HOUR) ]]> and t.express_print_time ='2000-01-01 00:00:00.000'))
            and t.sys_status in ('FINISHED_AUDIT')
            and t.enable_status = 1
    </select>

    <select id="queryWaveTradesGroupByTemplate" resultClass="com.raycloud.dmj.domain.trades.TbTrade">
        select template_id templateId, template_type templateType from trade_#tradeDbNo# t
        where company_id = #companyId#
        and warehouse_id in <iterate property="warehouseIds" open="(" conjunction="," close=")">#warehouseIds[]#</iterate>
        and wave_id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
        and is_cancel = 0
        group by template_id, template_type order by null
    </select>

    <select id="queryWaveTradesGroupByUserId" resultClass="Long">
        select user_id from trade_#tradeDbNo# t
        where company_id = #companyId#
        and warehouse_id in <iterate property="warehouseIds" open="(" conjunction="," close=")">#warehouseIds[]#</iterate>
        and wave_id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
        and is_cancel = 0
        group by user_id order by null
    </select>

    <!--子订单相关查询-->

    <select id="queryWaveOrdersBySids" resultMap="waveQueryOrderMap">
        /*FORCE_MASTER*/ SELECT id, sid, item_sys_id, sku_sys_id, sys_status, num, gift_num, payment, price, type, is_virtual, sys_outer_id, enable_status, combine_id, net_weight, is_pick, stock_num, source, belong_sid, sys_sku_properties_name, sys_title,short_title, company_id, non_consign,volume
        FROM order_#dbNo#
        WHERE company_id = #companyId# AND sid IN
        <iterate conjunction="," open="(" close=")" property="sids"> #sids[]# </iterate>
        <isNotNull prepend="AND" property="combineId"> combine_id = #combineId# </isNotNull>
        and enable_status &gt; 0 order by sid
    </select>

    <select id="queryGoodsSectionOrderRecords" resultMap="goodsSectionOrderRecordMap">
        select order_id, goods_section_id, goods_section_code, apply_num FROM goods_section_order_record_#dbNo#
        where company_id = #companyId# and enable_status = 1 AND order_id in
        <iterate property="orderIds" open="(" conjunction="," close=")">
            #orderIds[]#
        </iterate>
    </select>

    <select id="queryWaveIdsByOrderIds" resultClass="Long">
        SELECT DISTINCT t.wave_id
        FROM trade_#tradeDbNo# t
        JOIN order_#orderDbNo# o ON t.sid = o.sid
        WHERE t.company_id = #companyId#
        AND o.id IN
        <iterate property="orderIds" open="(" conjunction="," close=")">
            #orderIds[]#
        </iterate>
        AND t.wave_id > 0
        AND t.enable_status > 0
    </select>

    <!-- 查询备货区 -->
    <select id="queryBackRegions" resultClass="String">
        select code from stock_region where company_id = #companyId# and status = 1
        and warehouse_id = #warehouseId#
        and stock_region_type = 2
    </select>

    <select id="queryBackRegionGsIds" resultClass="Long">
        select id from goods_section where company_id = #companyId# and status = 1
        and warehouse_id = #warehouseId#
        and stock_region_type = 2 and active_status = 1
    </select>

    <select id="queryIgnorePickGoodsSections" resultClass="String">
        select code from goods_section where company_id = #companyId# and status = 1 and warehouse_id = #warehouseId#
        and ignore_pick = 1 and active_status = 1
    </select>

    <select id="queryGoodsSectionsByWaveId" resultClass="String">
        SELECT DISTINCT g.goods_section_code
        FROM trade_#tradeDbNo# t
        JOIN order_#orderDbNo# o ON t.sid = o.sid
        <isEmpty property="isWms">
            JOIN goods_section_order_record_#gsOrderRecordDbNo# g
        </isEmpty>
        <isNotEmpty property="isWms">
            JOIN allocate_goods_record_#allocateGoodsRecordDbNo# g
        </isNotEmpty>
        ON o.company_id = g.company_id
        AND o.id = g.order_id
        WHERE t.company_id = #companyId#
        AND g.company_id = #companyId#
        AND t.warehouse_id = #warehouseId#
        AND t.wave_id = #waveId#
        AND g.enable_status = 1
    </select>

    <sql id="suitModeSqlIgnoreVirtualGift">
        <isEqual property="suitMode" compareValue="1"> and ((type = 2 and combine_id = 0) or (type != 2 and combine_id = 0)) </isEqual>
        <isNotEqual property="suitMode" compareValue="1"> and ((type = 0 or type = 1) or (type = 2 and combine_id > 0) or (type > 2 and combine_id = 0)) </isNotEqual>
    </sql>

    <select id="querySingleTradeIgnoreVirtualGift" resultMap="waveQueryTradeMap">
        select DISTINCT <include refid="queryFields"/>
        from
            (select o.company_id, o.sid, o.belong_sid from order_not_consign_#orderDbNo# o
                where o.company_id = #companyId#
                and o.sys_status = 'FINISHED_AUDIT'
                <!-- 聚合订单商品数量 如果扫的是套件本身，就按本身来算数量 -->
                <include refid="suitModeSqlIgnoreVirtualGift"/>
                and o.is_virtual = 0
                and o.non_consign = 0
                and o.is_pick != 0
                and o.enable_status = 1
                and o.belong_sid in (
                    select belong_sid from order_not_consign_#orderDbNo#
                    where company_id = #companyId#
                    and item_sys_id = #itemSysId#
                    and sku_sys_id = #skuSysId#
                    <!-- 扫的码只支持匹配普通商品、加工本身、套件明细（or 套件本身）-->
                    <include refid="suitModeSqlIgnoreVirtualGift"/>
                    and is_pick != 0
                    and is_virtual = 0
                    and non_consign = 0
                    and sys_status = 'FINISHED_AUDIT'
                    and is_cancel = 0
                    and enable_status = 1
                )
            group by o.company_id, o.belong_sid having sum(o.num) = 1 order by null) oo
        left join trade_not_consign_#tradeDbNo# t on oo.company_id = t.company_id and oo.belong_sid = t.sid
        where t.company_id = #companyId#
        and t.warehouse_id = #warehouseId#
        and t.sys_status = 'FINISHED_AUDIT'
        and t.template_type = 1
        and t.wave_id = 0
        <isNotEmpty property="sid"> and t.sid = #sid# </isNotEmpty>
        <isEqual property="noExcept" compareValue="0"> and (t.is_excep = 0 or (t.is_excep = 1 and t.stock_status = 'INSUFFICIENT' and t.insufficient_num > 0)) </isEqual>
        <isEqual property="noExcept" compareValue="1">and t.is_excep = 1 and t.stock_status != 'INSUFFICIENT' and t.insufficient_num = 0 </isEqual>
        <isEmpty property="needQueryPrint"> and t.express_print_time = '2000-01-01 00:00:00' </isEmpty> and t.enable_status = 1 and t.scalping = 0
        <isNotEmpty property="templateList">
            and concat(t.template_id,'_',t.template_type) in <iterate property="templateList" open="(" conjunction="," close=")"> #templateList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="notInTemplateList">
            and concat(t.template_id, '_', t.template_type) not in <iterate property="notInTemplateList" open="(" conjunction="," close=")"> #notInTemplateList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="appointUserIdList">
            and t.user_id in <iterate property="appointUserIdList" open="(" conjunction="," close=")"> #appointUserIdList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="excludeUserIdList">
            and t.user_id not in <iterate property="excludeUserIdList" open="(" conjunction="," close=")"> #excludeUserIdList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="logisticsCompanyIdList">
            and t.logistics_company_id in <iterate property="logisticsCompanyIdList" open="(" conjunction="," close=")"> #logisticsCompanyIdList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="notInLogisticsCompanyIdList">
            and t.logistics_company_id not in <iterate property="notInLogisticsCompanyIdList" open="(" conjunction="," close=")"> #notInLogisticsCompanyIdList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="tagIds" prepend="and">
            <isEqual property="includeTradeTag" compareValue="1"> not </isEqual>
            exists (select 1 from trade_label_#tradeLabelDbNo# tl where tl.company_id = t.company_id and tl.sid = t.sid and tl.enable_status = 1
            and tl.label_id in <iterate property="tagIds" open="(" conjunction="," close=")"> #tagIds[]# </iterate>
            )
        </isNotEmpty>
        <isNotEmpty property="sellerFlags">
            and (t.seller_flag in <iterate property="sellerFlags" open="(" conjunction="," close=")"> #sellerFlags[]# </iterate>
            <isEqual property="containEmptySellerFlag" compareValue="true">
                or t.seller_flag is null
            </isEqual>
            )
        </isNotEmpty>
        <isEqual property="filterBindUniqueCodeTrade" compareValue="true">
            and not exists (select 1 from wave_unique_code_#waveUniqueCodeDbNo# wuc force index(idx_sid) where wuc.company_id = t.company_id and wuc.sid = t.sid and wuc.type in (1, 2) and wuc.status not in (7, 12, 15) and wuc.enable_status = 1)
        </isEqual>
        order by
        <isNotEmpty property="postPrintMatchPriorityShopList">
            FIELD(t.user_id,
            <iterate property="postPrintMatchPriorityShopList" conjunction=",">
                #postPrintMatchPriorityShopList[]#
            </iterate>
            ) desc,
        </isNotEmpty>
        if(is_excep=0 or ( t.item_excep =2 and	t.is_halt = 0 AND t.unattainable = 0 AND t.is_refund = 0  AND t.excep=0 and t.except_ids = ''), 0, 1),
        <include refid="WaveSorting.orderByUploadConsignFirst"/>
        t.is_urgent desc,
        <isNotEmpty property="matchFastInOutTrade">
            CASE
            WHEN EXISTS (
                SELECT 1
                FROM trade_label_#tradeLabelDbNo# tl  where tl.company_id = t.company_id and t.sid = tl.sid and tl.enable_status = 1
                and tl.label_id = 1000000110
            ) THEN 0
            ELSE 1
            END,
        </isNotEmpty>
        <include refid="WaveSorting.orderByTimeoutActionTime"/>
        t.pay_time
        limit #printNum#
    </select>

    <select id="queryUnPrintTradeList" resultClass="Long">
        select sid
        from trade_#tradeDbNo# t
        where t.company_id = #companyId# and t.wave_id = #waveId# and t.express_print_time = '2000-01-01 00:00:00' and t.enable_status = 1
    </select>


    <!-- 根据指定商品查询已审核订单，订单未打印、未进入异常、单个商品的订单，未进入波次的订单，查询订单小表 -->
    <select id="queryMultiItemTrade" resultMap="waveQueryTradeMap">
        select <include refid="queryFields"/>
        from trade_not_consign_#tradeDbNo# t left join order_not_consign_#orderDbNo# o on t.sid = o.belong_sid
        where t.company_id = #companyId# and o.company_id = #companyId# and o.item_sys_id = #itemSysId# and o.sku_sys_id = #skuSysId# and o.enable_status = 1 and t.warehouse_id = #warehouseId#
        and o.sys_status = 'FINISHED_AUDIT' and t.sys_status = 'FINISHED_AUDIT' and t.item_kind_num = 1 and t.template_type = 1
        <isNotEqual property="suitMode" compareValue="1"> and o.type != 2 and o.combine_id = 0 </isNotEqual>
        <isEqual property="suitMode" compareValue="1"> and o.type = 2 and o.combine_id = 0 </isEqual>
        <isNull property="totalNum"> and t.item_num = 1 </isNull>
        <isNotNull property="totalNum"> and t.item_num = #totalNum# </isNotNull>
        <isEqual property="noExcept" compareValue="0"> and (t.is_excep = 0 or (t.is_excep = 1 and t.stock_status = 'INSUFFICIENT' and t.insufficient_num > 0)) </isEqual>
        <isEqual property="noExcept" compareValue="1">and t.is_excep = 1 and t.stock_status != 'INSUFFICIENT' and t.insufficient_num = 0 </isEqual>
        <isEmpty property="needQueryPrint"> and t.express_print_time = '2000-01-01 00:00:00' </isEmpty> and t.enable_status = 1 and t.scalping = 0
        <!-- 唯一码复用逻辑-->
        <isNotNull property="reuseRange">
            <isEqual property="reuseRange" compareValue="0">
                and t.wave_id = 0
            </isEqual>
            <isEqual property="reuseRange" compareValue="1">
                AND (
                t.wave_id = 0
                OR (
                t.wave_id = - 5
                AND EXISTS (
                SELECT
                1
                FROM
                wave_unique_code_#waveUniqueCodeDbNo# wuc
                WHERE
                wuc.type = 1
                AND wuc.enable_status = 1 AND
                wuc.status IN
                <iterate property="reuseStatusList" open="(" conjunction="," close=")">
                    #reuseStatusList[]#
                </iterate>
                AND wuc.sid = t.sid
                AND wuc.company_id = t.company_id
                )
                )
                )
            </isEqual>
        </isNotNull>
        <isNull property="reuseRange">
            and t.wave_id = 0
        </isNull>
        <isNotEmpty property="sid">
            and t.sid = #sid#
        </isNotEmpty>
        <isNotEmpty property="templateList">
            and concat(t.template_id,'_',t.template_type) in <iterate property="templateList" open="(" conjunction="," close=")"> #templateList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="notInTemplateList">
            and concat(t.template_id, '_', t.template_type) not in <iterate property="notInTemplateList" open="(" conjunction="," close=")"> #notInTemplateList[]# </iterate>
        </isNotEmpty>

        <isNotEmpty property="appointUserIdList">
            and t.user_id in <iterate property="appointUserIdList" open="(" conjunction="," close=")"> #appointUserIdList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="excludeUserIdList">
            and t.user_id not in <iterate property="excludeUserIdList" open="(" conjunction="," close=")"> #excludeUserIdList[]# </iterate>
        </isNotEmpty>

        <isNotEmpty property="logisticsCompanyIdList">
            and t.logistics_company_id in <iterate property="logisticsCompanyIdList" open="(" conjunction="," close=")"> #logisticsCompanyIdList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="notInLogisticsCompanyIdList">
            and t.logistics_company_id not in <iterate property="notInLogisticsCompanyIdList" open="(" conjunction="," close=")"> #notInLogisticsCompanyIdList[]# </iterate>
        </isNotEmpty>
        <isEqual property="filterBindUniqueCodeTrade" compareValue="true">
            and not exists (select 1 from wave_unique_code_#waveUniqueCodeDbNo# wuc force index(idx_sid) where wuc.company_id = t.company_id and wuc.sid = t.sid and wuc.type in (1, 2) and wuc.status not in (7, 12, 15) and wuc.enable_status = 1)
        </isEqual>
        order by
        <isNotEmpty property="postPrintMatchPriorityShopList">
            FIELD(t.user_id,
            <iterate property="postPrintMatchPriorityShopList" conjunction=",">
                #postPrintMatchPriorityShopList[]#
            </iterate>
            ) desc,
        </isNotEmpty>
        if(is_excep=0 or ( t.item_excep =2 and	t.is_halt = 0 AND t.unattainable = 0 AND t.is_refund = 0  AND t.excep=0 and t.except_ids = ''), 0, 1),
        <include refid="WaveSorting.orderByUploadConsignFirst"/>
        t.is_urgent desc,
        <isNotEmpty property="matchFastInOutTrade">
            CASE
            WHEN EXISTS (
                SELECT 1
                FROM trade_label_#tradeLabelDbNo# tl  where tl.company_id = t.company_id and t.sid = tl.sid and tl.enable_status = 1
                and tl.label_id = 1000000110
            ) THEN 0
            ELSE 1
            END,
        </isNotEmpty>
        if( <isEqual property="checkIsPickWhiteList" compareValue="false"> o.gift_num > 0 and </isEqual> o.is_pick = 0, 0, 1)  desc,
        <include refid="WaveSorting.orderByTimeoutActionTime"/>
        t.pay_time
        limit #printNum#
    </select>

    <!-- 根据sid查询order基本信息 -->
    <select id="querySingleItemOrder" resultMap="waveQueryOrderMap">
        select id, sid, item_sys_id, sku_sys_id, sys_status, num, gift_num, payment, price, type, is_virtual, sys_outer_id, enable_status, combine_id, net_weight, is_pick, stock_num, source, belong_sid, sys_sku_properties_name, sys_title, company_id
        from order_not_consign_#orderDbNo#
        where company_id = #companyId# AND sid = #sid#
    </select>

    <select id="queryItemSuppliers" resultClass="com.raycloud.dmj.domain.item.ItemSupplierBridge">
        SELECT sys_item_id sysItemId, sys_sku_id sysSkuId, supplier_id supplierId, supplier_purchase_price supplierPurchasePrice, supplier_name supplierName, main_state mainState FROM item_supplier_bridge_#itemSupplierBridgeDbNo#
        WHERE company_id = #companyId#
        AND enable_status = 1
        <isNotEmpty property="sysItemIds">
            and sys_item_id in <iterate property="sysItemIds" open="(" conjunction="," close=")">#sysItemIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="sysSkuIds">
            and sys_sku_id in <iterate property="sysSkuIds" open="(" conjunction="," close=")">#sysSkuIds[]#</iterate>
        </isNotEmpty>
    </select>

    <sql id="singleEffectiveOrderSql">
        and o.enable_status > 0
        and o.sys_status = 'FINISHED_AUDIT'
        and o.is_virtual = 0
        and o.non_consign = 0
        and o.num = 1
        <isNotEmpty property="params.outerId">
            and o.sys_outer_id like concat('%', #params.outerId#, '%')
        </isNotEmpty>

        <isNotEmpty property="params.outerIds" prepend="and">
            <iterate property="params.outerIds" open="(" close=")" conjunction=" or ">
                o.sys_outer_id like concat('%', #params.outerIds[]#, '%')
            </iterate>
        </isNotEmpty>

        <isNotEmpty property="params.queryParams">
            <iterate property="params.queryParams" prepend="and" open="(" conjunction="and" close=")">
                <isEqual property="params.queryParams[].queryType" compareValue="title">
                    <isEmpty property="params.itemChangeFlagQuery">
                        o.sys_title like concat('%', #params.queryParams[].queryText#, '%')
                    </isEmpty>
                    <isNotEmpty property="params.itemChangeFlagQuery">
                        IF(item.title is not null, item.title, o.sys_title) like concat('%', #params.queryParams[].queryText#, '%')
                    </isNotEmpty>
                </isEqual>
                <isEqual property="params.queryParams[].queryType" compareValue="shortTitle">
                    <isEmpty property="params.itemChangeFlagQuery">
                        o.short_title like concat('%', #params.queryParams[].queryText#, '%')
                    </isEmpty>
                    <isNotEmpty property="params.itemChangeFlagQuery">
                        IF(item.short_title is not null, item.short_title, o.short_title) like concat('%', #params.queryParams[].queryText#, '%')
                    </isNotEmpty>
                </isEqual>
                <isEqual property="params.queryParams[].queryType" compareValue="propertiesName">
                    o.sys_sku_properties_name like concat('%', #params.queryParams[].queryText#, '%')
                </isEqual>
                <isEqual property="params.queryParams[].queryType" compareValue="propertiesAlias">
                    o.sys_sku_properties_alias like concat('%', #params.queryParams[].queryText#, '%')
                </isEqual>
            </iterate>
        </isNotEmpty>
    </sql>

    <select id="queryOrderItem4UniqueCode" resultClass="com.raycloud.dmj.domain.wave.WaveItem" timeout="30">
        select c2.belong_sid sid,
               c2.sys_outer_id outerId,
               c2.item_sys_id sysItemId,
               c2.sku_sys_id sysSkuId,
               c2.id orderId,
               c2.num,
               c1.pay_time payTime,
               c1.warehouse_id warehouseId,
               c1.template_id templateId,
               c1.template_type templateType,
               c1.logistics_company_id logisticsCompanyId,
               c1.user_id userId,
               c1.item_num tradeNum,
               c1.tag_ids tradeTagIds,
               c1.out_sid outSid,
               c1.source source
        from (
            select t.sid, t.pay_time, t.warehouse_id, t.template_id, t.template_type, t.logistics_company_id, t.company_id, t.user_id, t.item_num, t.tag_ids, t.out_sid, t.source
            from trade_not_consign_#tradeDbNo# t
            left join trade_except_#tradeExceptDbNo# e on t.company_id = e.company_id and t.sid = e.sid and e.enable_status = 1
            where t.company_id = #companyId#
                and t.sys_status = 'FINISHED_AUDIT'
                and t.enable_status = 1 and t.is_cancel = 0 and t.type not like 'trade_out%' and t.scalping = 0
                and (t.is_excep = 0 or (e.except_id in (6) and not exists (select 1 from trade_except_#tradeExceptDbNo# e1 where e1.company_id = #companyId# and e1.sid = e.sid and e1.except_id not in (6))))
                and t.wave_id = 0
                and t.express_print_time = '2000-01-01 00:00:00'
                <isNotEmpty property="warehouseIdList">
                    and t.warehouse_id in <iterate property="warehouseIdList" open="(" conjunction="," close=")"> #warehouseIdList[]# </iterate>
                </isNotEmpty>
                <isNotEmpty property="sids">
                    and t.sid in <iterate property="sids" open="(" conjunction="," close=")"> #sids[]# </iterate>
                </isNotEmpty>
                <isNotEmpty property="tradeTag">
                    <isEqual property="tradeTag" compareValue="1">
                        AND (
                            EXISTS ( SELECT 1 FROM trade_label_#tradeLabelDbNo# tl WHERE tl.company_id = t.company_id AND t.sid = tl.sid AND tl.enable_status = 1 and tl.label_id = 1000000016)
                            or EXISTS ( SELECT 1 FROM trade_label_#tradeLabelDbNo# tl join trade_not_consign_7 t1 on t1.company_id = tl.company_id AND t1.sid = tl.sid where tl.enable_status = 1
                            and t1.merge_sid = t.sid and t1.enable_status != 0 and t1.company_id = t.company_id and tl.label_id = 1000000016 and tl.company_id = #companyId#)
                        )
                    </isEqual>
                </isNotEmpty>
                <isNotEmpty property="interceptTagIds" prepend="and">
                    not exists (select 1 from trade_label_#tradeLabelDbNo# tl  where tl.company_id = t.company_id and t.sid = tl.sid and tl.enable_status = 1
                    and tl.label_id in  <iterate property="interceptTagIds" open="(" conjunction="," close=")"> #interceptTagIds[]# </iterate>
                    )
                </isNotEmpty>
                <isNotEmpty property="multi">
                    <isEqual property="multi" compareValue="1">
                        and t.item_num = 1 and t.item_kind_num = 1 and
                        not exists (select 1 from order_not_consign_#orderDbNo# o where o.belong_sid = t.sid and type = 2 and combine_id != 0 group by id having count(1) > 1)
                    </isEqual>
                    <isEqual property="multi" compareValue="2">
                        and (t.item_num > 1 or (t.item_num = 1 and
                        exists(select 1 from order_not_consign_#orderDbNo# o where o.belong_sid = t.sid and combine_id != 0 and type =2 group by id having count(1) > 1)))
                    </isEqual>
                </isNotEmpty>
                <isNotEmpty property="outerIds">
                    and exists(select 1 from order_not_consign_#orderDbNo# o
                    where o.belong_sid = t.sid and ((o.type = 0 or o.type = 1) or (o.type = 2 and o.combine_id > 0) or (o.type > 2 and o.combine_id = 0))
                    and o.sys_outer_id in <iterate conjunction="," open="(" close=")" property="outerIds"> #outerIds[]# </iterate>)
                </isNotEmpty>
                <isNotEmpty property="userIds">
                    and t.user_id in <iterate conjunction="," open="(" close=")" property="userIds"> #userIds[]# </iterate>
                </isNotEmpty>
                <isNotEmpty property="currentMaxSid">
                    and t.sid > #currentMaxSid#
                </isNotEmpty>
            order by t.sid
            <isNotEmpty property="queryNum"> limit 0, #queryNum# </isNotEmpty>
        ) c1
        join order_not_consign_#orderDbNo# c2
             on c1.company_id = c2.company_id and c1.sid = c2.belong_sid
        where c2.company_id = #companyId#
        and (c2.type <![CDATA[ < ]]> 2 or (c2.type > 2 and combine_id = 0) or (c2.type = 2 and c2.combine_id > 0))
        and c2.enable_status > 0
        and c2.sys_status = 'FINISHED_AUDIT'
        and c2.is_virtual = 0
        and c2.non_consign = 0
    </select>

    <select id="queryOrderItemForSingleWave" resultClass="com.raycloud.dmj.domain.wave.WaveItem" timeout="30">
        select o.item_sys_id sysItemId,
               o.sku_sys_id sysSkuId,
               o.item_sys_id sysItemIds,
               o.sku_sys_id sysSkuIds,
               o.sys_outer_id outerId,
               o.sys_sku_properties_name propertiesName,
               <isEmpty property="params.itemChangeFlagQuery">
                    o.sys_title title,
               </isEmpty>
               <isNotEmpty property="params.itemChangeFlagQuery">
                   IF(item.title is not null, item.title, o.sys_title) title,
               </isNotEmpty>
               <isEmpty property="params.itemChangeFlagQuery">
                   o.short_title shortTitle,
               </isEmpty>
               <isNotEmpty property="params.itemChangeFlagQuery">
                   IF(item.short_title is not null, item.short_title, o.short_title) shortTitle,
               </isNotEmpty>
               o.sys_sku_properties_alias propertiesAlias,
               o.sys_pic_path picPath,
               group_concat(t.user_id) userIds,
               t.sid sid,
               sum(o.num) num,
               1 nums
        from trade_not_consign_#tradeDbNo# t
        join order_not_consign_#orderDbNo# o on t.sid = o.belong_sid
        <isNotEmpty property="params.itemChangeFlagQuery">
            left join dmj_sku_#skuDbNo# c on c.company_id = o.company_id and if(o.sku_sys_id = -1,0,o.sku_sys_id) = c.sys_sku_id and c.enable_status = 1 and c.active_status = 1 and c.last_sys_item_id != c.sys_item_id
            LEFT JOIN dmj_item_#itemDbNo# item ON item.company_id = c.company_id and c.last_sys_item_id = item.sys_item_id
        </isNotEmpty>
        where o.company_id = #companyId#
            and t.sys_status = 'FINISHED_AUDIT'
            and t.merge_type in (-1, 3)
        <include refid="checkSkuWaveItemNum"/>
            and o.is_pick in (1, 2)
            <isEqual property="checkIsPickWhiteList" compareValue="true">
                and not exists (select 1 from order_item_tag_#orderDbNo# oit where oit.company_id = o.company_id and oit.order_id = o.id and oit.item_tag_id = 1000000004 AND oit.enable_status = 1)
            </isEqual>
            and o.combine_id = 0
            and <include refid="waveCon"/>
            <include refid="singleEffectiveOrderSql"/>
        group by o.item_sys_id, o.sku_sys_id
        <dynamic prepend="having">
            <isNotEmpty property="params.itemNumDown" prepend="and">
                count(1) >= #params.itemNumDown#
            </isNotEmpty>
            <isNotEmpty property="params.itemNumUp" prepend="and">
                count(1) &lt;= #params.itemNumUp#
            </isNotEmpty>
        </dynamic>
        order by num desc
    </select>

    <select id="queryOrderItemForWave" resultClass="com.raycloud.dmj.domain.wave.WaveItem" timeout="30">
        SELECT
            outerId outerId,
            sysTitle title,
            propertiesName,
            sysItemId sysItemIds,
            sysSkuId sysSkuIds,
            nums nums,
            group_concat(DISTINCT userId) userIds,
            count(*) num
        FROM
        (
            SELECT
                group_concat(
                concat( o.sys_outer_id, '*', o.num ) ORDER BY o.sys_outer_id separator '\r\n' ) outerId,
                <isEmpty property="params.itemChangeFlagQuery">
                    group_concat( sys_title ORDER BY o.sys_outer_id separator '\r\n' ) sysTitle,
                </isEmpty>
                <isNotEmpty property="params.itemChangeFlagQuery">
                    group_concat( IF(item.title is not null, item.title, o.sys_title) ORDER BY o.sys_outer_id separator '\r\n' ) sysTitle,
                </isNotEmpty>
                    group_concat( sys_sku_properties_name  ORDER BY o.sys_outer_id separator '\r\n' ) propertiesName,
                <isEmpty property="params.itemChangeFlagQuery">
                    group_concat( short_title  ORDER BY o.sys_outer_id separator '\r\n' ) shortTitle,
                </isEmpty>
                <isNotEmpty property="params.itemChangeFlagQuery">
                    group_concat( IF(item.short_title is not null, item.short_title, o.short_title)  ORDER BY o.sys_outer_id separator '\r\n' ) shortTitle,
                </isNotEmpty>
                group_concat( item_sys_id ORDER BY o.sys_outer_id) sysItemId,
                group_concat( sku_sys_id ORDER BY o.sys_outer_id) sysSkuId,
                group_concat(t.user_id ORDER BY o.sys_outer_id) userId,
                group_concat( o.num ORDER BY o.sys_outer_id) nums
            FROM
            trade_not_consign_#tradeDbNo# t
            JOIN order_not_consign_#orderDbNo# o ON t.sid = o.belong_sid AND o.company_id = t.company_id
            <isNotEmpty property="params.itemChangeFlagQuery">
                left join dmj_sku_#skuDbNo# c on c.company_id = o.company_id and if(o.sku_sys_id = -1,0,o.sku_sys_id) = c.sys_sku_id and c.enable_status = 1 and c.active_status = 1 and c.last_sys_item_id != c.sys_item_id
                LEFT JOIN dmj_item_#itemDbNo# item ON item.company_id = c.company_id and c.last_sys_item_id = item.sys_item_id
            </isNotEmpty>
            WHERE
                <include refid="waveCon"/>
                AND t.sys_status = 'FINISHED_AUDIT'
                <isEqual property="params.pickingTypes" compareValue="2">
                    <include refid="checkSkuWaveItemNum"/>
                </isEqual>
                AND o.is_pick IN (1, 2)
                <isEqual property="checkIsPickWhiteList" compareValue="true">
                    AND NOT EXISTS (SELECT 1 FROM order_item_tag_#orderDbNo# oit WHERE oit.company_id = o.company_id AND oit.order_id = o.id AND oit.item_tag_id = 1000000004 AND oit.enable_status = 1)
                </isEqual>
                AND o.company_id = #companyId#
                AND o.combine_id = 0
                AND o.enable_status > 0
                AND o.sys_status = 'FINISHED_AUDIT'
                AND o.is_virtual = 0
                AND o.non_consign = 0
            GROUP BY
            t.company_id,
            t.sid
            ORDER BY
            o.sys_outer_id
        ) a
        <dynamic prepend="where">
            <isNotEmpty property="params.outerId" prepend="and">
                outerId like concat('%', #params.outerId#, '%')
            </isNotEmpty>

            <isNotEmpty property="params.outerIds" prepend="and">
                <iterate property="params.outerIds" open="(" close=")" conjunction=" or ">
                    outerId like concat('%', #params.outerIds[]#, '%')
                </iterate>
            </isNotEmpty>

            <isNotEmpty property="params.queryParams">
                <iterate property="params.queryParams" prepend="and" open="(" conjunction="and" close=")">
                    <isEqual property="params.queryParams[].queryType" compareValue="title">
                        sysTitle like concat('%', #params.queryParams[].queryText#, '%')
                    </isEqual>
                    <isEqual property="params.queryParams[].queryType" compareValue="shortTitle">
                        shortTitle like concat('%', #params.queryParams[].queryText#, '%')
                    </isEqual>
                    <isEqual property="params.queryParams[].queryType" compareValue="propertiesName">
                        propertiesName like concat('%', #params.queryParams[].queryText#, '%')
                    </isEqual>
                </iterate>
            </isNotEmpty>
        </dynamic>
        GROUP BY
            outerId,
            sysTitle,
            propertiesName,
            shortTitle,
            sysItemId,
            sysSkuId
        <dynamic prepend="having">
            <isNotEmpty property="params.itemNumDown" prepend="and">
                num >= #params.itemNumDown#
            </isNotEmpty>
            <isNotEmpty property="params.itemNumUp" prepend="and">
                num &lt;= #params.itemNumUp#
            </isNotEmpty>
        </dynamic>
        ORDER BY num desc
        limit 1000
    </select>

    <select id="queryTradeForSingleWave" resultMap="waveQueryTradeMap">
        select <include refid="queryFields"/>
        from trade_not_consign_#tradeDbNo# t
        where <include refid="waveCon"/>
              and t.sys_status = 'FINISHED_AUDIT'
        <include refid="checkSkuWaveItemNum"/>
    </select>

    <select id="queryOpenAutoCreateWave" resultClass="Long">
        select company_id from trade_config where auto_create_wave in (1,2,3)
    </select>

    <select id="queryNoOutSidWave" resultClass="Long">
        select distinct w.id from wave_#dbNo# w join trade_not_consign_#tradeDbNo# t on w.company_id = t.company_id and w.id = t.wave_id and w.warehouse_id = t.warehouse_id
        where w.company_id = #companyId#
        and w.id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
        and t.enable_status = 1 and (t.out_sid is null or t.out_sid = '');
    </select>

    <select id="queryMergeSidBySid" resultClass="Long">
        select
            distinct t.merge_sid
        from trade_#tradeDbNo# t
        where
            t.company_id = #companyId#
            and t.sid in <iterate property="sids" open="(" conjunction="," close=")">#sids[]#</iterate>
            and t.merge_sid > 0
    </select>


    <select id="queryWavesItems" resultClass="com.raycloud.dmj.domain.wave.WaveItem" timeout="30">
        select * from (
            select
                oo.item_sys_id sysItemId,
                oo.sku_sys_id sysSkuId,
                sum(oo.num) num,
                oo.sys_outer_id outerId,
                oo.sys_sku_properties_name propertiesName,
                wtt.wave_id waveId,
                (
                SELECT
                    count(distinct oo2.sys_outer_id) + 1
                FROM
                order_#orderDbNo# oo2
                JOIN wave_trade_#waveTradeDbNo# t ON oo2.sid = t.sid AND oo2.company_id = t.company_id
                WHERE
                    t.wave_id = wtt.wave_id
                    AND oo2.sys_outer_id > oo.sys_outer_id
                    <!-- 排除 赠品不参与拣选，套件本身，组合明细，虚拟商品, 已取消商品-->
                    AND oo2.enable_status > 0 and oo2.company_id = #companyId# and oo2.is_cancel = 0 and oo2.is_virtual = 0 and oo2.non_consign = 0 and ( oo2.is_pick IN ( 1, 2 ) OR oo2.gift_num = 0 )  and ((oo2.type=2 and oo2.combine_id > 0) or (oo2.type != 2 AND oo2.combine_id =0))
                ) `rank`
        from order_#orderDbNo# oo join wave_trade_#waveTradeDbNo# wtt on  oo.sid = wtt.sid
            where
            <!-- 排除 赠品不参与拣选，套件本身，组合明细，虚拟商品, 已取消商品-->
             oo.enable_status > 0 and oo.company_id = #companyId# and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 )  and ((oo.type=2 and oo.combine_id > 0) or (oo.type != 2 AND oo.combine_id =0))
             and wtt.wave_id in <iterate property="waveIdList" open="(" conjunction="," close=")">#waveIdList[]#</iterate> and wtt.company_id = #companyId#
            group by oo.item_sys_id,oo.sku_sys_id,wtt.wave_id
        )  TEMP
        <!-- 取前6个-->
        WHERE TEMP.`rank` &lt; 7 order by TEMP.`rank` DESC
    </select>

    <select id="queryWavesUnCombineItems" resultClass="com.raycloud.dmj.domain.wave.WaveItem" timeout="30">
        select * from (
            select
                oo.item_sys_id sysItemId,
                oo.sku_sys_id sysSkuId,
                sum(oo.num) num,
                oo.sys_outer_id outerId,
                oo.sys_sku_properties_name propertiesName,
                wtt.wave_id waveId,
                (
                SELECT
                    count(distinct oo2.sys_outer_id) + 1
                FROM
                order_#orderDbNo# oo2
                JOIN wave_trade_#waveTradeDbNo# t ON oo2.sid = t.sid
                WHERE
                    t.wave_id = wtt.wave_id
                    AND oo2.sys_outer_id > oo.sys_outer_id
                    <!-- 排除 赠品不参与拣选，套件明细，组合明细，虚拟商品, 已取消商品-->
                    AND oo2.enable_status > 0 and oo2.company_id = #companyId# and oo2.is_cancel = 0 and oo2.is_virtual = 0 and oo2.non_consign = 0 and oo2.combine_id = 0 and ( oo2.is_pick IN ( 1, 2 ) OR oo2.gift_num = 0 )
                ) `rank`
        from order_#orderDbNo# oo join wave_trade_#waveTradeDbNo# wtt on  oo.sid = wtt.sid
            where
             <!-- 排除 赠品不参与拣选, 套件明细, 虚拟商品, 已取消商品-->
             oo.enable_status > 0 and oo.company_id = #companyId# and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and oo.combine_id = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 )
             and wtt.wave_id in <iterate property="waveIdList" open="(" conjunction="," close=")">#waveIdList[]#</iterate> and wtt.company_id = #companyId#
            group by oo.item_sys_id,oo.sku_sys_id,wtt.wave_id
        )  TEMP
        <!-- 取前6个-->
        WHERE TEMP.`rank` &lt; 7 order by TEMP.`rank` DESC
    </select>


    <select id="queryItemInfoBySysItemIdsAndSkuIds" resultClass="com.raycloud.dmj.domain.wave.WaveItem" timeout="30">
        select di.sys_item_id sysItemId,
            ds.sys_sku_id sysSkuId,
            di.seller_cids sellerCids,
            ds.seller_cids skuSellerCids,
            ifnull(ds.brand,di.brand) brand,
            ifnull(ds.boxnum,di.boxnum) boxNum,
            di.cat_id catId
        from dmj_item_#itemDbNo# di
        left join dmj_sku_#skuDbNo# ds
        on di.sys_item_id = ds.sys_item_id and di.company_id = ds.company_id and ds.enable_status = 1
        where di.company_id = #companyId#
        <isNotEmpty property="sysItemIds">
            and di.sys_item_id in <iterate property="sysItemIds" open="(" conjunction="," close=")">#sysItemIds[]#</iterate>
            and ds.sys_sku_id is null
        </isNotEmpty>
        <isNotEmpty property="sysSkuIds">
            and ds.sys_sku_id in <iterate property="sysSkuIds" open="(" conjunction="," close=")">#sysSkuIds[]#</iterate>
        </isNotEmpty>
    </select>


    <!-- 根据主键查询 -->
    <select id="getTradeExt" resultMap="TradeExtResultMap" parameterClass="java.util.HashMap">
        select sid, delivery_type, ship_type, cooperation_no, sell_site from trade_ext_#dbNo#
        <dynamic prepend="where">
            <isNotEmpty prepend="and" property="companyId">
                company_id = #companyId#
            </isNotEmpty>
            <isNotEmpty prepend="and" property="sids">
                sid in
                <iterate property="sids" open="(" close=")" conjunction=",">
                    #sids[]#
                </iterate>
            </isNotEmpty>
        </dynamic>
    </select>


    <select id="queryHotSaleTrades" resultMap="hotSaleTradeMap">
        SELECT o.belong_sid as sid,t.out_sid,t.warehouse_id,t.user_id,t.pay_time,t.template_id,t.logistics_company_id,o.item_sys_id,if(o.sku_sys_id &lt; 0,0,o.sku_sys_id) sku_sys_id,o.sys_outer_id,o.num,t.is_urgent,t.timeout_action_time
        FROM trade_not_consign_#tradeDbNo# t
        JOIN order_#orderDbNo# o ON t.company_id= o.company_id AND t.sid = o.belong_sid AND o.enable_status>0
        STRAIGHT_JOIN order_unique_code_hot_sale_$hotSaleDbNo$ hot ON o.company_id =  hot.company_id AND o.item_sys_id = hot.sys_item_id AND o.sku_sys_id = IF(hot.sys_sku_id=0,-1,hot.sys_sku_id) AND hot.enable_status = 1
        <isNotEmpty property="hotSaleType">
            AND hot.type = #hotSaleType#
        </isNotEmpty>
        <isEmpty property="hotSaleType">
            AND hot.type = 1
        </isEmpty>
        WHERE t.company_id = #companyId#
        AND t.enable_status = 1
        AND o.enable_status = 1
        AND t.is_cancel = 0
        AND o.is_cancel = 0
        AND t.sys_status = 'FINISHED_AUDIT'
        AND o.sys_status = 'FINISHED_AUDIT'
        AND t.type NOT LIKE 'trade_out%'
        AND (o.type = 0 or o.type = 1 or o.type = 3 or o.type = 4)
        AND (t.template_id > 0 OR t.type = 'dangkou')
        AND t.wave_id = 0
        AND t.scalping = 0
        AND t.is_excep = 0
        AND t.template_type = 1
        AND t.express_print_time = '2000-01-01 00:00:00'
        AND o.combine_id = 0
        AND o.num = 1
        AND (
            t.item_num = 1
            OR EXISTS (
                SELECT 1 FROM order_not_consign_#orderDbNo# o
                WHERE o.company_id = #companyId#
                <isNotEmpty property="excludeNotCheck">
                    <isEqual property="excludeNotCheck" compareValue="1">
                        AND (o.is_pick in (2,3)
                    <isEqual property="checkIsPickWhiteList" compareValue="false">
                        OR o.gift_num = 0
                    </isEqual>
                        )
                    </isEqual>
                </isNotEmpty>
                AND o.belong_sid = t.sid
                AND o.is_cancel = 0
                AND o.enable_status = 1
                AND o.is_virtual = 0
                AND o.non_consign = 0
                AND o.combine_id = 0
                AND o.sys_status not in ('FINISHED','CLOSED','SELLER_SEND_GOODS')
                GROUP BY o.belong_sid HAVING sum( o.num ) = 1
            )
        )
        <isNotEmpty property="warehouseIds">
            AND t.warehouse_id in
            <iterate property="warehouseIds" open="(" conjunction="," close=")">#warehouseIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="userIds">
            AND t.user_id in
            <iterate property="userIds" open="(" conjunction="," close=")">#userIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="expressTemplateIds">
            AND t.template_id in
            <iterate property="expressTemplateIds" open="(" conjunction="," close=")">#expressTemplateIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="startPayTime">
            AND t.pay_time &gt;=#startPayTime#
        </isNotEmpty>
        <isNotEmpty property="endPayTime">
            AND t.pay_time &lt;=#endPayTime#
        </isNotEmpty>
        <isNotEmpty property="interceptIds" prepend="and">
            not exists (select 1 from trade_label_#tradeLabelDbNo# tl  where tl.company_id = t.company_id and t.sid = tl.sid and tl.enable_status = 1
            and tl.label_id in  <iterate property="interceptIds" open="(" conjunction="," close=")"> #interceptIds[]# </iterate>
            )
        </isNotEmpty>
    </select>

    <select id="queryItemSupplierBridges" resultMap="itemSupplierBridgeMap" parameterClass="hashMap">
        SELECT * FROM item_supplier_bridge_#itemSupplierBridgeDbNo#
        WHERE company_id = #companyId# and enable_status = 1
        <isNotEmpty property="sysItemIdList" prepend=" AND ">
            ( sys_item_id IN
            <iterate open="(" close=")" conjunction="," property="sysItemIdList">
                #sysItemIdList[]#
            </iterate>
            )
            <isNotEmpty property="sysSkuIdList">
                UNION
                SELECT * FROM item_supplier_bridge_#itemSupplierBridgeDbNo#
                WHERE company_id = #companyId# and enable_status = 1
                AND sys_sku_id IN
                <iterate open="(" close=")" conjunction="," property="sysSkuIdList">
                    #sysSkuIdList[]#
                </iterate>
            </isNotEmpty>
        </isNotEmpty>

        <isEmpty property="sysItemIdList">
            <isNotEmpty property="sysSkuIdList" prepend=" AND ">
                sys_sku_id IN
                <iterate open="(" close=")" conjunction="," property="sysSkuIdList">
                    #sysSkuIdList[]#
                </iterate>
            </isNotEmpty>
        </isEmpty>
    </select>

    <sql id="excludeTradeExceptSql">
        and ( 1=1
        <isNotEmpty property="systemExceptStatus">
            <iterate property="systemExceptStatus" prepend="and" open="(" conjunction="and" close=")">
                <isEqual property="systemExceptStatus[]" compareValue="EX_HALT"> t.is_halt = 0 </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_REFUND"> t.is_refund = 0 </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_CHANGE_ADDRESS"> t.address_changed = 0 </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_UPDATED_SELLERMEMO"> t.seller_memo_update = 0 </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_BLACK"> t.black_buyer_nick = 0 </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_CUSTOM"> t.except_ids = '' </isEqual>

                <isEqual property="systemExceptStatus[]" compareValue="EX_UNATTAINABLE">
                    ((t.merge_sid = -1 and t.unattainable  != 1) or (t.merge_sid > 0 and t.merge_sid not in (select merge_sid from trade_not_consign_#tradeDbNo# t1 where t1.merge_sid = t.merge_sid and t1.company_id = #companyId# and t1.merge_sid > 0 and t1.unattainable = 1)))
                </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_UPLOAD_DELIVER"> <![CDATA[ t.v & 4 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_PART_REFUND"> <![CDATA[ t.item_excep & 16 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_LOST_MSG"> <![CDATA[ t.item_excep & 256 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_RISK_ORDER"> <![CDATA[ t.item_excep & 512 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_DELIVER"> <![CDATA[ t.item_excep & 1024 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_SUITE_QUANTITY_CHANGE"> <![CDATA[ t.item_excep & 4096 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_COD_REPEAT"> <![CDATA[ t.item_excep & 1 << 20 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="PLATFORM_WAREHOUSE_MAPPING_EXCEPTION"> <![CDATA[ t.item_excep & 1 << 28 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_WAIT_MERGE"> <![CDATA[ t.item_excep & 1 << 22 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_WAITING_RETURN_WMS"> <![CDATA[ t.item_excep & 1 << 21 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_STOCK_OUT"> <![CDATA[ t.item_excep & 1 << 11 = 0 ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_UNIQUE_CODE_OFFSHELF"> <![CDATA[ t.item_excep & 1 << 27 = 0 ]]> </isEqual>
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="customExceptIds">
            and (
                merge_sid = -1 and not(
                    <iterate property="customExceptIds" conjunction=" or ">
                        find_in_set(#customExceptIds[]#, IF(t.except_ids is null, '', t.except_ids))
                    </iterate>
                )
            )
            or (
                merge_sid > 0 and merge_sid not in (
                    select merge_sid from trade_not_consign_#tradeDbNo# t1 where t1.merge_sid = t.merge_sid and company_id = #companyId# and merge_sid > 0
                        and (
                                <iterate property="customExceptIds" conjunction=" or ">
                                    find_in_set(#customExceptIds[]#, IF(except_ids is null, '', except_ids))
                                </iterate>
                             )
                )
            )
        </isNotEmpty>
        )
    </sql>

    <sql id="excludeOrderExceptSql">
        and ( 1=1
        <isNotEmpty property="systemExceptStatus">
            <iterate property="systemExceptStatus" prepend="and" open="(" conjunction="and" close=")">
                <isEqual property="systemExceptStatus[]" compareValue="EX_UNALLOCATED"> o.item_sys_id > -1 </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_RELATION_MODIFIED"> o.relation_changed = 0 </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_INSUFFICIENT"> <![CDATA[ o.stock_num = o.num ]]> </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_CHANGE_ITEM"> (o.sys_item_changed = 0 and o.item_changed = 0) </isEqual>
                <isEqual property="systemExceptStatus[]" compareValue="EX_ITEM_SHUTOFF"> <![CDATA[ o.v is null or (o.v & 16 = 0) ]]> </isEqual>
            </iterate>
        </isNotEmpty>
        )
    </sql>

    <sql id="excludeMultiOrderExceptSql">
        <isNotEmpty property="systemOrderExceptStatus">
            and ( 1=1
            <iterate property="systemOrderExceptStatus" prepend="and" open="(" conjunction="and" close=")">
                <isEqual property="systemOrderExceptStatus[]" compareValue="EX_UNALLOCATED">
                    SUM(CASE WHEN o.item_sys_id > -1 THEN 1 ELSE 0 END) = count(1)
                </isEqual>
                <isEqual property="systemOrderExceptStatus[]" compareValue="EX_RELATION_MODIFIED">
                    SUM(CASE WHEN o.relation_changed = 0 THEN 1 ELSE 0 END) = count(1)
                </isEqual>
                <isEqual property="systemOrderExceptStatus[]" compareValue="EX_INSUFFICIENT">
                    SUM(CASE WHEN <![CDATA[ o.stock_num = o.num ]]>  THEN 1 ELSE 0 END) = count(1)
                </isEqual>
                <isEqual property="systemOrderExceptStatus[]" compareValue="EX_CHANGE_ITEM">
                    SUM(CASE WHEN (o.sys_item_changed = 0 and o.item_changed = 0) THEN 1 ELSE 0 END) = count(1)
                </isEqual>
                <isEqual property="systemOrderExceptStatus[]" compareValue="EX_ITEM_SHUTOFF">
                    SUM(CASE WHEN <![CDATA[ o.v is null or (o.v & 16 = 0) ]]>  THEN 1 ELSE 0 END) = count(1)
                </isEqual>
            </iterate>
            )
        </isNotEmpty>
    </sql>

    <sql id="excludeShopSql">
        <isNotEmpty property="fastTradeExcludeUserIds">
            AND t.user_id not in
            <iterate property="fastTradeExcludeUserIds" open="(" conjunction="," close=")">#fastTradeExcludeUserIds[]#</iterate>
        </isNotEmpty>
    </sql>

    <sql id="excludeTagSql">
        <isNotEmpty property="fastTradeExcludeTagIds">
            AND not exists (select 1 from trade_label_$tradeLabelDbNo$ tl  where tl.company_id = t.company_id and t.sid = tl.sid and tl.enable_status = 1
            and tl.label_id in  <iterate property="fastTradeExcludeTagIds" open="(" conjunction="," close=")"> #fastTradeExcludeTagIds[]# </iterate>
            )
        </isNotEmpty>
        <isEqual property="fastTradeExcludeNullTag" compareValue="true">
            AND exists (select 1 from trade_label_$tradeLabelDbNo$ tl  where tl.company_id = t.company_id and t.sid = tl.sid and tl.enable_status = 1
            )
        </isEqual>
    </sql>

    <sql id="matchCrossWarehouseSql">
        <isNotEqual property="matchCrossWarehouse" compareValue="1">
            and t.warehouse_id = #warehouseId#
            <isEqual property="tradeStatus" compareValue="1"> and t.sys_status = 'FINISHED_AUDIT' </isEqual>
            <isEqual property="tradeStatus" compareValue="2">
                and t.sys_status in ('FINISHED_AUDIT', 'WAIT_AUDIT', 'WAIT_FINANCE_AUDIT')
            </isEqual>
        </isNotEqual>
        <isEqual property="matchCrossWarehouse" compareValue="1">
            <isEqual property="tradeStatus" compareValue="1"> and t.sys_status = 'FINISHED_AUDIT' and t.warehouse_id = #warehouseId# </isEqual>
            <isEqual property="tradeStatus" compareValue="2">
                and ((t.sys_status in ('FINISHED_AUDIT', 'WAIT_AUDIT', 'WAIT_FINANCE_AUDIT') and t.warehouse_id = #warehouseId#) or
                (t.sys_status = 'WAIT_AUDIT' and t.warehouse_id != #warehouseId#))
            </isEqual>
        </isEqual>
    </sql>



    <sql id="mergeSidSqlConditionInWaveCon2">
        select
        case t1.merge_sid when -1 then t1.sid else t1.merge_sid end
        from trade_not_consign_#tradeDbNo# t1
        where t1.company_id = #companyId#
        <isNotEmpty property="params.warehouseId">
            and t1.warehouse_id = #params.warehouseId#
        </isNotEmpty>
        and t1.wave_id = 0
    </sql>

    <sql id="waveCon2">
        <isNotNull property="params.itemNumUp">
            and t.item_num &lt;= #params.itemNumUp#
        </isNotNull>
        <isNotNull property="params.itemNumDown">
            and t.item_num &gt;= #params.itemNumDown#
        </isNotNull>
        <isNotEmpty property="params.shortIdList" prepend="and">
            t.short_id in
            <iterate property="params.shortIdList" open="(" conjunction="," close=")">#params.shortIdList[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="params.userIdList" prepend="and">
            t.user_id in
            <iterate property="params.userIdList" open="(" conjunction="," close=")">#params.userIdList[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="params.titleList" prepend="and">
            <isNotEmpty property="params.changeItemFlag">
                <iterate property="params.titleList" open="(" close=")" conjunction=" or ">
                    exists (
                        select belong_sid from
                            order_not_consign_$orderDbNo$ oo
                            left join dmj_sku_#skuDbNo# c on c.company_id = oo.company_id and if(oo.sku_sys_id = -1,0,oo.sku_sys_id) = c.sys_sku_id and c.enable_status = 1 and c.active_status = 1 and c.last_sys_item_id != c.sys_item_id
                            LEFT JOIN dmj_item_#itemDbNo# item ON item.company_id = c.company_id and c.last_sys_item_id = item.sys_item_id
                        where oo.belong_sid = t.sid and oo.combine_id = 0 and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 ) and oo.enable_status > 0
                            and IF(item.title is not null, item.title, oo.sys_title) like concat('%', #params.titleList[]#, '%')
                    )
                </iterate>
            </isNotEmpty>
            <isEmpty property="params.changeItemFlag">
                <iterate property="params.titleList" open="(" close=")" conjunction=" or ">
                    exists (select belong_sid from order_not_consign_$orderDbNo$ oo where oo.belong_sid = t.sid and oo.combine_id = 0 and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 ) and oo.enable_status > 0
                    and (oo.sys_title like concat('%', #params.titleList[]#, '%')))
                </iterate>
            </isEmpty>
        </isNotEmpty>
        <isNotEmpty property="params.skuPropertiesNameList" prepend="and">
            <iterate property="params.skuPropertiesNameList" open="(" close=")" conjunction=" or ">
                exists (select belong_sid from order_not_consign_$orderDbNo$ oo where oo.belong_sid = t.sid and oo.combine_id = 0 and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 ) and oo.enable_status > 0
                and (oo.sys_sku_properties_name like concat('%', #params.skuPropertiesNameList[]#, '%')))
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="params.bindGoodsSectionCode" prepend="and">
            exists (select 1 from asso_goods_section_sku_$assoDbNo$ asso where asso.company_id = t.company_id and asso.warehouse_id = t.warehouse_id and asso.sys_item_id = o.item_sys_id and ((asso.sys_sku_id = o.sku_sys_id) or (asso.sys_sku_id = 0 and o.sku_sys_id = -1))
            and asso.goods_section_code like concat('%', #params.bindGoodsSectionCode#, '%') and asso.container_type = 0)
        </isNotEmpty>
        <isNotEmpty property="params.sysItemRemark">
            <isEmpty property="params.changeItemFlag">
                and t.sid
                in (select belong_sid from order_not_consign_$orderDbNo$ oo where oo.belong_sid = t.sid
                and oo.combine_id = 0 and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 ) and oo.enable_status > 0
                and oo.sys_item_remark like concat('%', #params.sysItemRemark#, '%'))
            </isEmpty>
            <isNotEmpty property="params.changeItemFlag">
                and exists(
                select belong_sid from
                    order_not_consign_$orderDbNo$ oo
                    left join dmj_sku_#skuDbNo# c on c.company_id = oo.company_id and if(oo.sku_sys_id = -1,0,oo.sku_sys_id) = c.sys_sku_id and c.enable_status = 1 and c.active_status = 1 and c.last_sys_item_id != c.sys_item_id
                    LEFT JOIN dmj_item_#itemDbNo# item ON item.company_id = c.company_id and c.last_sys_item_id = item.sys_item_id
                where oo.belong_sid = t.sid
                    and oo.combine_id = 0 and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 ) and oo.enable_status > 0
                    and IF(item.remark is not null, item.remark, oo.sys_item_remark) like concat('%', #params.sysItemRemark#, '%')
                )
            </isNotEmpty>
        </isNotEmpty>
        <isNotEmpty property="params.sysSkuRemark">
            and t.sid
            in (select belong_sid from order_not_consign_$orderDbNo$ oo where oo.belong_sid = t.sid
            and oo.combine_id = 0 and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 ) and oo.enable_status > 0
            and oo.sys_sku_remark like concat('%', #params.sysSkuRemark#, '%'))
        </isNotEmpty>
        <isNotNull property="params.hasSuit">
            and t.sid <isNotEqual property="params.hasSuit" compareValue="true"> not </isNotEqual>
            in (select belong_sid from order_not_consign_$orderDbNo$ oo where oo.belong_sid = t.sid
            and oo.combine_id = 0 and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 ) and oo.enable_status > 0
            and oo.type =2)
        </isNotNull>
        <isNotNull property="params.specifyItemCondition">
            <isNotEmpty property="params.specifyItemCondition.collectionList">
                <isEqual property="params.specifyItemCondition.type" compareValue="INCLUDE">
                    and t.sid in (select belong_sid from order_not_consign_$orderDbNo$ oo where oo.belong_sid = t.sid
                    and oo.combine_id = 0 and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 ) and oo.enable_status > 0
                    and (oo.item_sys_id,oo.sku_sys_id) in
                    <iterate property="params.specifyItemCondition.collectionList" open="(" conjunction="," close=")">
                        (#params.specifyItemCondition.collectionList[].key#,
                        #params.specifyItemCondition.collectionList[].key2#)
                    </iterate>
                    )
                </isEqual>
                <isEqual property="params.specifyItemCondition.type" compareValue="INCLUDE_ONLY">
                    and t.sid in (select belong_sid from order_not_consign_$orderDbNo$ oo where oo.belong_sid = t.sid
                    and oo.combine_id = 0 and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 ) and oo.enable_status > 0
                    and (oo.item_sys_id,oo.sku_sys_id) in
                    <iterate property="params.specifyItemCondition.collectionList" open="(" conjunction="," close=")">
                        (#params.specifyItemCondition.collectionList[].key#,
                        #params.specifyItemCondition.collectionList[].key2#)
                    </iterate>
                    )
                    and t.item_kind_num = #params.specifyItemCondition.length#
                </isEqual>
                <isEqual property="params.specifyItemCondition.type" compareValue="EXCLUDE">
                    and t.sid not in (select belong_sid from order_not_consign_$orderDbNo$ oo where oo.belong_sid = t.sid
                    and oo.combine_id = 0 and oo.is_cancel = 0 and oo.is_virtual = 0 and oo.non_consign = 0 and ( oo.is_pick IN ( 1, 2 ) OR oo.gift_num = 0 ) and oo.enable_status > 0
                    and (oo.item_sys_id,oo.sku_sys_id) in
                    <iterate property="params.specifyItemCondition.collectionList" open="(" conjunction="," close=")">
                        (#params.specifyItemCondition.collectionList[].key#,
                        #params.specifyItemCondition.collectionList[].key2#)
                    </iterate>
                    )
                </isEqual>
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.specifyExpressTemplateCondition">
            <isNotEmpty property="params.specifyExpressTemplateCondition.collectionList">
                <isEqual property="params.specifyExpressTemplateCondition.type" compareValue="INCLUDE">
                    and (t.template_id,t.template_type) in
                    <iterate property="params.specifyExpressTemplateCondition.collectionList" open="(" conjunction="," close=")">
                        (#params.specifyExpressTemplateCondition.collectionList[].key#,
                        #params.specifyExpressTemplateCondition.collectionList[].key2#)
                    </iterate>
                </isEqual>
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.hasOutSid">
            <isNotEqual property="params.hasOutSid" compareValue="true">
                and (t.out_sid is null or t.out_sid = '')
            </isNotEqual>
            <isEqual property="params.hasOutSid" compareValue="true">
                and (t.out_sid is not null and t.out_sid != '')
            </isEqual>
        </isNotNull>
        <isNotNull property="params.hasTaxNo">
            <isNotEqual property="params.hasTaxNo" compareValue="true">
                and (t.need_invoice is null or t.need_invoice = 0)
            </isNotEqual>
            <isEqual property="params.hasTaxNo" compareValue="true">
                and t.need_invoice = 1
            </isEqual>
        </isNotNull>
        <isNotNull property="params.hasUrgent">
            <isNotEqual property="params.hasUrgent" compareValue="true">
                and (t.is_urgent is null or t.is_urgent = 0)
            </isNotEqual>
            <isEqual property="params.hasUrgent" compareValue="true">
                and t.is_urgent = 1
            </isEqual>
        </isNotNull>
        <isNotNull property="params.specifyTradeTypeCondition">
            <isNotEmpty property="params.specifyTradeTypeCondition.collectionList">
                and
                <iterate property="params.specifyTradeTypeCondition.collectionList" open="(" conjunction="or" close=")">
                    <isEqual property="params.specifyTradeTypeCondition.collectionList[].value" compareValue="NORMAL">
                        t.type not in ('changeitem', 'reissue')
                    </isEqual>
                    <isEqual property="params.specifyTradeTypeCondition.collectionList[].value" compareValue="EXCHANGE">
                        t.type = 'changeitem'
                    </isEqual>
                    <isEqual property="params.specifyTradeTypeCondition.collectionList[].value" compareValue="REISSUE">
                        t.type = 'reissue'
                    </isEqual>
                    <isEqual property="params.specifyTradeTypeCondition.collectionList[].value" compareValue="OFFLINE">
                        t.source = 'sys'
                    </isEqual>
                </iterate>
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.specifyIncludeTradeTagCondition">
            <isNotEmpty property="params.specifyIncludeTradeTagCondition.collectionList" prepend="and" open="(" close=")">
                EXISTS ( SELECT 1 FROM trade_label_#tradeLabelDbNo# tl WHERE tl.company_id = t.company_id AND t.sid = tl.sid AND tl.enable_status = 1 and tl.label_id in <iterate property="params.specifyIncludeTradeTagCondition.collectionList" open="(" conjunction="," close=")"> #params.specifyIncludeTradeTagCondition.collectionList[].key# </iterate>)
                or EXISTS ( SELECT 1 FROM trade_label_#tradeLabelDbNo# tl join trade_not_consign_#tradeDbNo# t1 on t1.company_id = tl.company_id AND t1.sid = tl.sid where tl.enable_status = 1
                and t1.merge_sid = t.sid and t1.enable_status != 0 and t1.company_id = t.company_id and tl.label_id in <iterate property="params.specifyIncludeTradeTagCondition.collectionList" open="(" conjunction="," close=")"> #params.specifyIncludeTradeTagCondition.collectionList[].key# </iterate>)
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.specifyExcludeTradeTagCondition">
            <isNotEmpty property="params.specifyExcludeTradeTagCondition.collectionList" prepend="and" open="(" close=")">
                NOT EXISTS ( SELECT 1 FROM trade_label_#tradeLabelDbNo# tl WHERE tl.company_id = t.company_id AND t.sid = tl.sid AND tl.enable_status = 1 and tl.label_id in <iterate property="params.specifyExcludeTradeTagCondition.collectionList" open="(" conjunction="," close=")"> #params.specifyExcludeTradeTagCondition.collectionList[].key# </iterate>)
                AND NOT EXISTS ( SELECT 1 FROM trade_label_#tradeLabelDbNo# tl join trade_not_consign_#tradeDbNo# t1 on t1.company_id = tl.company_id AND t1.sid = tl.sid where tl.enable_status = 1
                and t1.merge_sid = t.sid and t1.enable_status != 0 and t1.company_id = t.company_id and tl.label_id in <iterate property="params.specifyExcludeTradeTagCondition.collectionList" open="(" conjunction="," close=")"> #params.specifyExcludeTradeTagCondition.collectionList[].key# </iterate>)
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.specifyDistributorCondition">
            <isNotEmpty property="params.specifyDistributorCondition.collectionList">
                and
                <iterate property="params.specifyDistributorCondition.collectionList" open="(" conjunction="or" close=")">
                    t.source_id = #params.specifyDistributorCondition.collectionList[].key#
                    <isNotEmpty property="params.specifyDistributorCondition.collectionList[].key2List">
                        and t.taobao_id in
                        <iterate property="params.specifyDistributorCondition.collectionList[].key2List" open="(" conjunction="," close=")">
                            #params.specifyDistributorCondition.collectionList[].key2List[]#
                        </iterate>
                    </isNotEmpty>
                </iterate>
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.specifyBuyerMessageCondition">
            and
            <isNotNull property="params.specifySellerMemoCondition"><isNotEqual property="params.messageMemoAndOr" compareValue="true"> ( </isNotEqual></isNotNull>
            <isEqual property="params.specifyBuyerMessageCondition.type" compareValue="INCLUDE">
                <iterate property="params.specifyBuyerMessageCondition.collectionList" open="(" close=")"
                         conjunction=" AND ">
                    t.sid in(
                        <include refid="mergeSidSqlConditionInWaveCon2"/>
                        and t1.buyer_message like concat('%', #params.specifyBuyerMessageCondition.collectionList[].value#, '%')
                    )
                </iterate>
            </isEqual>
            <isEqual property="params.specifyBuyerMessageCondition.type" compareValue="EXCLUDE">

                <iterate property="params.specifyBuyerMessageCondition.collectionList" open="(" close=")"
                         conjunction=" AND ">
                    t.sid in(
                         <include refid="mergeSidSqlConditionInWaveCon2"/>
                        and t1.buyer_message not like concat('%', #params.specifyBuyerMessageCondition.collectionList[].value#, '%')
                        and (t1.buyer_message is not null and t1.buyer_message != '')
                    )
                </iterate>
            </isEqual>
            <isEqual property="params.specifyBuyerMessageCondition.type" compareValue="NONE">
                t.sid in(
                    <include refid="mergeSidSqlConditionInWaveCon2"/>
                    and (t1.buyer_message is null or t1.buyer_message = '')
                )
            </isEqual>
            <isEqual property="params.specifyBuyerMessageCondition.type" compareValue="EXIST">
                t.sid in(
                    <include refid="mergeSidSqlConditionInWaveCon2"/>
                    and t1.wave_id = 0
                    and (t1.buyer_message is not null and t1.buyer_message != '')
                )
            </isEqual>
        </isNotNull>
        <isNotNull property="params.specifySellerMemoCondition">
            <isNotNull property="params.specifyBuyerMessageCondition"><isNotEqual property="params.messageMemoAndOr" compareValue="true">or</isNotEqual></isNotNull>
            <isNotNull property="params.specifyBuyerMessageCondition"><isEqual property="params.messageMemoAndOr" compareValue="true">and</isEqual></isNotNull>
            <isNull property="params.specifyBuyerMessageCondition"> and</isNull>
            <isEqual property="params.specifySellerMemoCondition.type" compareValue="INCLUDE">
                <iterate property="params.specifySellerMemoCondition.collectionList" open="(" close=")"
                         conjunction=" AND ">
                    t.sid in(
                        <include refid="mergeSidSqlConditionInWaveCon2"/>
                        and t1.seller_memo like concat('%', #params.specifySellerMemoCondition.collectionList[].value#, '%')
                    )
                </iterate>
            </isEqual>
            <isEqual property="params.specifySellerMemoCondition.type" compareValue="EXCLUDE">
                <iterate property="params.specifySellerMemoCondition.collectionList" open="(" close=")"
                         conjunction=" AND ">
                    t.sid in(
                        <include refid="mergeSidSqlConditionInWaveCon2"/>
                        and t1.seller_memo not like concat('%', #params.specifySellerMemoCondition.collectionList[].value#, '%')
                        and (t1.seller_memo is not null and t1.seller_memo != '')
                    )
                </iterate>
            </isEqual>
            <isEqual property="params.specifySellerMemoCondition.type" compareValue="NONE">
                t.sid in (
                    <include refid="mergeSidSqlConditionInWaveCon2"/>
                    and (t1.seller_memo is null or t1.seller_memo = '')
                )
            </isEqual>
            <isEqual property="params.specifySellerMemoCondition.type" compareValue="EXIST">
                t.sid in (
                    <include refid="mergeSidSqlConditionInWaveCon2"/>
                    and  t1.seller_memo is not null and t1.seller_memo != ''
                )
            </isEqual>
            <isNotNull property="params.specifyBuyerMessageCondition"><isNotEqual property="params.messageMemoAndOr" compareValue="true">)</isNotEqual></isNotNull>
        </isNotNull>
        <isNotNull property="params.itemNumRange">
            <isNotEmpty property="params.itemNumRange.max">
                and t.item_num &lt;= #params.itemNumRange.max#
            </isNotEmpty>
            <isNotEmpty property="params.itemNumRange.min">
                and t.item_num &gt;= #params.itemNumRange.min#
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.weightRange">
            <isNotEmpty property="params.weightRange.max">
                and t.net_weight &lt;= #params.weightRange.max#
            </isNotEmpty>
            <isNotEmpty property="params.weightRange.min">
                and t.net_weight &gt;= #params.weightRange.min#
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.volumeRange">
            <isNotEmpty property="params.volumeRange.max">
                and t.volume &lt;= #params.volumeRange.max#
            </isNotEmpty>
            <isNotEmpty property="params.volumeRange.min">
                and t.volume &gt;= #params.volumeRange.min#
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.itemKindRange">
            <isNotEmpty property="params.itemKindRange.max">
                and t.item_kind_num &lt;= #params.itemKindRange.max#
            </isNotEmpty>
            <isNotEmpty property="params.itemKindRange.min">
                and t.item_kind_num &gt;= #params.itemKindRange.min#
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.paymentRange">
            <isNotEmpty property="params.paymentRange.max">
                and t.payment &lt;= #params.paymentRange.max#
            </isNotEmpty>
            <isNotEmpty property="params.paymentRange.min">
                and t.payment &gt;= #params.paymentRange.min#
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.grossProfitRange">
            <isNotEmpty property="params.grossProfitRange.max">
                and t.gross_profit &lt;= #params.grossProfitRange.max#
            </isNotEmpty>
            <isNotEmpty property="params.grossProfitRange.min">
                and t.gross_profit &gt;= #params.grossProfitRange.min#
            </isNotEmpty>
        </isNotNull>
        <isNotNull property="params.specifyLogisticsCompanyCondition">
            <isNotEmpty property="params.specifyLogisticsCompanyCondition.collectionList">
                <isEqual property="params.specifyLogisticsCompanyCondition.type" compareValue="INCLUDE">
                    and t.logistics_company_id in
                    <iterate property="params.specifyLogisticsCompanyCondition.collectionList" open="(" conjunction="," close=")">
                        #params.specifyLogisticsCompanyCondition.collectionList[].key#
                    </iterate>
                </isEqual>
            </isNotEmpty>
        </isNotNull>
        <isNotEmpty property="params.tids">
            and t.tid in
            <iterate property="params.tids" open="(" conjunction="," close=")">
                #params.tids[]#
            </iterate>
        </isNotEmpty>
    </sql>

    <sql id="queryWaveTradeSortSql">
        <isNotNull property="params.sortField">
            order by
            <isEqual property="params.sortField" compareValue="shortId">
                t.short_id
            </isEqual>
            <isEqual property="params.sortField" compareValue="itemInfo">
                itemInfo
            </isEqual>
            <isEqual property="params.sortField" compareValue="templateName">
                t.template_id
            </isEqual>
            <isEqual property="params.sortField" compareValue="logisticsCompanyName">
                t.logistics_company_id
            </isEqual>
            <isEqual property="params.sortOrder" compareValue="desc">
                desc
            </isEqual>
        </isNotNull>
        <isNull property="params.sortField">
            order by t.pay_time
        </isNull>
    </sql>

    <select id="queryWaveTradeByCondition" resultMap="waveQuerySimpleTradeMap">
        select t.sid,
        t.short_id,
        t.template_id,
        t.template_type,
        t.item_num,
        t.logistics_company_id,
        t.net_weight,
        t.buyer_message,
        t.seller_memo,
        t.merge_sid,
        t.merge_type,
        t.user_id,
        concat(t.item_num, '.', GROUP_CONCAT(o.sys_outer_id, '*',o.num separator ' ')) itemInfo,
        t.tid,
        t.out_sid
        from trade_not_consign_$tradeDbNo$ t
        left join order_not_consign_$orderDbNo$ o on o.belong_sid = t.sid and o.combine_id = 0 and o.is_cancel = 0 and o.is_virtual = 0 and o.non_consign = 0 and ( o.is_pick IN ( 1, 2 ) OR o.gift_num = 0 ) and o.enable_status > 0
        where
        <include refid="waveCon"/>
        <include refid="waveCon2"/>
        group by t.sid
        <include refid="queryWaveTradeSortSql"/>
        limit 600
    </select>

    <select id="queryWaveTradeIncludeConsign" resultMap="waveQuerySimpleTradeMap">
        select t.sid,
        t.short_id,
        t.template_id,
        t.template_type,
        t.item_num,
        t.logistics_company_id,
        t.net_weight,
        t.buyer_message,
        t.seller_memo,
        t.merge_sid,
        t.merge_type,
        t.user_id,
        concat(t.item_num, '.', GROUP_CONCAT(o.sys_outer_id, '*',o.num separator ' ')) itemInfo,
        t.tid,
        t.out_sid
        from trade_$tradeDbNo$ t
        left join order_$orderDbNo$ o on o.belong_sid = t.sid and o.combine_id = 0 and o.is_cancel = 0 and o.is_virtual = 0 and o.non_consign = 0 and ( o.is_pick IN ( 1, 2 ) OR o.gift_num = 0 ) and o.enable_status > 0
        where
        <include refid="waveCon"/>
        <include refid="waveCon2"/>
        group by t.sid
        <include refid="queryWaveTradeSortSql"/>
        limit 600
    </select>


    <select id="queryWaveTradeOrderByCondition" resultClass="TbOrder">
        select
        o.belong_sid belongSid,
        o.sys_outer_id sysOuterId,
        o.item_sys_id itemSysId,
        o.sku_sys_id skuSysId,
        o.num num,
        o.sys_item_remark sysItemRemark,
        o.sys_sku_remark sysSkuRemark,
        o.sys_title sysTitle,
        o.sys_sku_properties_name sysSkuPropertiesName,
        o.sys_pic_path sysPicPath
        from order_not_consign_$orderDbNo$ o
        where
        o.company_id = #companyId# AND o.belong_sid in
        <iterate property="sidList" open="(" conjunction="," close=")">#sidList[]#</iterate>
        and o.combine_id = 0 and o.is_cancel = 0 and o.enable_status > 0
    </select>

    <select id="queryWaveItemInfoByWaveIds" resultClass="Wave">
        select w.id id,
        w.item_info itemInfo,
        w.picking_type pickingType
        from wave_#dbNo# w
        where w.company_id = #companyId#
        and w.id in <iterate property="waveIdList" open="(" conjunction="," close=")">#waveIdList[]#</iterate>
    </select>



    <select id="queryExcepAndRemoveWaveOrder" resultMap="waveQueryExcepOrderMap" timeout="40">
        select * from order_#orderDbNo# where company_id = #companyId#
        and sid in (
        select t.sid
        from wave_trade_#waveTradeDbNo# w join trade_#tradeDbNo# t on w.sid = t.sid
        where w.company_id = #companyId# and w.wave_id = #waveId#
        and t.enable_status = 1 and (t.is_excep = 1 or w.trade_wave_status = 2)
        )
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>


    <select id="queryExcepAndRemoveWaveOrderCount"  resultClass="Long"  timeout="40">
        select count(*) from order_#orderDbNo# where company_id = #companyId#
        and sid in (
        select t.sid
        from wave_trade_#waveTradeDbNo# w join trade_#tradeDbNo# t on w.sid = t.sid
        where w.company_id = #companyId# and w.wave_id = #waveId#
        and t.enable_status = 1 and (t.is_excep = 1 or w.trade_wave_status = 2)
        )
    </select>

    <select id="queryFastInOutTradeCount" resultClass="com.raycloud.dmj.domain.wave.WaveItem" timeout="30">
        select o.item_sys_id sysItemId, o.sku_sys_id sysSkuId, o.stock_status stockStatus,
        <isEqual property="needGroupByItem" compareValue="true"> count(*) as num, </isEqual>
        <isEqual property="needGroupByItem" compareValue="false"> 0 as num, </isEqual> t.sid
        from trade_not_consign_#tradeDbNo# t
        join order_not_consign_#orderDbNo# o <isEqual property="fastInOutForIndex" compareValue="true"> force index(idx_company_item_sku_sys)</isEqual>
        on t.sid = o.belong_sid and t.company_id=o.company_id
        <isEqual property="fastTradeWaveStatusRange" compareValue="2">
            left join wave_#waveDb# w on t.wave_id = w.id and t.company_id = w.company_id
        </isEqual>
        <isNotEmpty property="fastTradeUniqueCodeStatusRange">
            <isGreaterEqual property="fastTradeUniqueCodeStatusRange" compareValue="2">
                left join wave_unique_code_#uniqueCodeDbNo# c on c.company_id = o.company_id and c.order_id = o.id and c.enable_status = 1 and c.status != 7
            </isGreaterEqual>
        </isNotEmpty>
        where t.company_id = #companyId#
        and t.item_num = 1
        and o.enable_status = 1
        and t.enable_status = 1 and t.is_cancel = 0 and t.type not like 'trade_out%' and t.scalping = 0
        and o.num = 1
        <isNotEqual property="fastTradeWaveStatusRange" compareValue="2">
            and (t.wave_id = 0
            <isNotEmpty property="fastTradeUniqueCodeStatusRange">
                <isGreaterEqual property="fastTradeUniqueCodeStatusRange" compareValue="2">
                    or t.wave_id = -5
                </isGreaterEqual>
            </isNotEmpty>
            )
        </isNotEqual>
        <isEqual property="fastTradeWaveStatusRange" compareValue="2">
            and (w.distribution_status is null or w.distribution_status = 0) and (t.wave_id >= 0
            <isNotEmpty property="fastTradeUniqueCodeStatusRange">
                <isGreaterEqual property="fastTradeUniqueCodeStatusRange" compareValue="2">
                    or t.wave_id = -5
                </isGreaterEqual>
            </isNotEmpty>
            )
        </isEqual>
        <isEqual property="fastTradeUniqueCodeStatusRange" compareValue="2">
            and (c.status is null or c.status in (1,2,4))
        </isEqual>
        <isEqual property="fastTradeUniqueCodeStatusRange" compareValue="3">
            and (c.status is null or c.status in (1,2))
        </isEqual>
        <isNotEqual property="suitModel" compareValue="1">
            AND NOT EXISTS(SELECT 1 FROM order_not_consign_#orderDbNo# WHERE sid = t.sid and company_id = #companyId# AND (enable_status = 1 or  enable_status = 2)  AND type = '2')
        </isNotEqual>
        <isNotEmpty property="sysItemIds" prepend="and">
            o.item_sys_id in <iterate property="sysItemIds" open="(" conjunction="," close=")"> #sysItemIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysSkuIds" prepend="and">
            o.sku_sys_id in <iterate property="sysSkuIds" open="(" conjunction="," close=")">#sysSkuIds[]#</iterate>
        </isNotEmpty>
        <include refid="matchCrossWarehouseSql"/>
        <include refid="excludeTradeExceptSql"/>
        <include refid="excludeOrderExceptSql"/>
        <include refid="excludeShopSql"/>
        <include refid="excludeTagSql"/>
        <isEqual property="needGroupByItem" compareValue="true"> group by o.item_sys_id, o.sku_sys_id, o.stock_status order by null</isEqual>
        <isEqual property="needGroupByItem" compareValue="false">
            order by
            o.stock_status, if(t.wave_id = -5, 0, t.wave_id), if(t.sys_status = 'FINISHED_AUDIT', 0, 1),
            t.is_urgent desc,
             <isNotEqual property="fastInOutMatchTradeOrder" compareValue="1">
                t.pay_time
             </isNotEqual>
            <isEqual property="fastInOutMatchTradeOrder" compareValue="1">
                t.timeout_action_time
            </isEqual>
        </isEqual>
    </select>

    <select id="queryFastInOutMultiMatchTrade" resultClass="com.raycloud.dmj.domain.wave.WaveItem" timeout="30">
        select t.sid, COUNT(CASE WHEN o.stock_status = 'INSUFFICIENT' THEN 1 END) as insufficientCount
        from trade_not_consign_#tradeDbNo# t
        left join order_not_consign_#orderDbNo# o on t.sid = o.belong_sid and t.company_id=o.company_id
        <isEqual property="fastTradeWaveStatusRange" compareValue="2">
            left join wave_#waveDb# w on t.wave_id = w.id and t.company_id = w.company_id
        </isEqual>
        where t.company_id = #companyId#
        and t.enable_status = 1 and t.is_cancel = 0 and t.type not like 'trade_out%' and t.scalping = 0
        <isEmpty property="totalNumUp">
            and t.item_num = #totalNum#
            and t.item_kind_num = #totalKindNum#
        </isEmpty>
        <isNotEmpty property="totalNumUp">
            and <![CDATA[ t.item_num <= #totalNumUp# ]]>
            and <![CDATA[ t.item_num >= #totalNumDown# ]]>
            and <![CDATA[ t.item_kind_num <= #totalKindNumUp# ]]>
            and <![CDATA[ t.item_kind_num >= #totalKindNumDown# ]]>
        </isNotEmpty>
        <isNotEqual property="fastTradeWaveStatusRange" compareValue="2">
            and (t.wave_id = 0
            <isNotEmpty property="fastTradeUniqueCodeStatusRange">
                <isGreaterEqual property="fastTradeUniqueCodeStatusRange" compareValue="2">
                    or t.wave_id = -5
                </isGreaterEqual>
            </isNotEmpty>
            )
        </isNotEqual>
        <isEqual property="fastTradeWaveStatusRange" compareValue="2">
            and (w.distribution_status is null or w.distribution_status = 0) and (t.wave_id >= 0
            <isNotEmpty property="fastTradeUniqueCodeStatusRange">
                <isGreaterEqual property="fastTradeUniqueCodeStatusRange" compareValue="2">
                    or t.wave_id = -5
                </isGreaterEqual>
            </isNotEmpty>
            )
        </isEqual>
        and o.is_cancel = 0
        and o.enable_status = 1
        and o.combine_id = 0
        <include refid="matchCrossWarehouseSql"/>
        <include refid="excludeTradeExceptSql"/>
        <include refid="excludeShopSql"/>
        <include refid="excludeTagSql"/>
        <isNotEmpty property="fastTradeUniqueCodeStatusRange">
            <isGreaterEqual property="fastTradeUniqueCodeStatusRange" compareValue="2">
                and (t.wave_id=-5 and
                exists (select 1 from wave_unique_code_#uniqueCodeDbNo# c where c.company_id=o.company_id and c.order_id = o.id and c.enable_status = 1
                group by c.sid having
                <isEqual property="fastTradeUniqueCodeStatusRange" compareValue="2">
                    SUM(CASE WHEN c.status IN (1, 2, 4) THEN 1 ELSE 0 END) = COUNT(*)
                </isEqual>
                <isNotEqual property="fastTradeUniqueCodeStatusRange" compareValue="2">
                    SUM(CASE WHEN c.status IN (1, 2) THEN 1 ELSE 0 END) = COUNT(*)
                </isNotEqual>
                )
                or wave_id != -5
                )
            </isGreaterEqual>
        </isNotEmpty>
        group by t.sid
        having sum(o.num) = #totalNum#
        <isNotEmpty property="matchItems">
            <iterate property="matchItems" prepend="and" conjunction="and">
                SUM(CASE WHEN CONCAT(o.item_sys_id, '_', o.sku_sys_id, '_', o.is_virtual, '_', o.non_consign) = #matchItems[].itemIdStr# THEN o.num ELSE 0 END) = #matchItems[].num#
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="matchSuitItems">
            <iterate property="matchSuitItems" prepend="and" conjunction="and">
                (SUM(CASE WHEN CONCAT(o.item_sys_id, '_', o.sku_sys_id, '_', o.is_virtual, '_', o.non_consign) = #matchSuitItems[].itemIdStr# THEN o.num ELSE 0 END) = #matchSuitItems[].num#
                or SUM(CASE WHEN CONCAT(o.item_sys_id, '_', o.sku_sys_id, '_', o.is_virtual, '_', o.non_consign) = #matchSuitItems[].suitItemIdStr# THEN o.num ELSE 0 END) = #matchSuitItems[].num#
                )
            </iterate>
        </isNotEmpty>
        <isEqual property="matchStockStatusMulti" compareValue="INSUFFICIENT">
            and SUM(CASE WHEN o.stock_status = 'INSUFFICIENT' THEN 1 ELSE 0 END) > 0
        </isEqual>
        <isEqual property="matchStockStatusMulti" compareValue="NORMAL">
            and SUM(CASE WHEN o.stock_status = 'NORMAL' THEN 1 ELSE 0 END) = count(1)
        </isEqual>
        <include refid="excludeMultiOrderExceptSql"/>
        order by
        insufficientCount, if(t.wave_id = -5, 0, t.wave_id), if(t.sys_status = 'FINISHED_AUDIT', 0, 1),
        t.is_urgent desc,
        <isNotEqual property="fastInOutMatchTradeOrder" compareValue="1">
            t.pay_time
        </isNotEqual>
        <isEqual property="fastInOutMatchTradeOrder" compareValue="1">
            t.timeout_action_time
        </isEqual>
        limit 1
    </select>

    <select id="queryItemTags" resultClass="com.raycloud.dmj.domain.item.tag.ItemTagRelation">
        SELECT company_id companyId, sys_item_id sysItemId, sys_sku_id sysSkuId, item_tag_id itemTagId FROM item_tag_relation_#itemTagRelationDbNo#
        WHERE company_id = #companyId#
        <isNotEmpty property="sysItemIds">
            and sys_item_id in <iterate property="sysItemIds" open="(" conjunction="," close=")">#sysItemIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="sysSkuIds">
            and sys_sku_id in <iterate property="sysSkuIds" open="(" conjunction="," close=")">#sysSkuIds[]#</iterate>
        </isNotEmpty>
        AND enable_status = 1
    </select>

    <select id="queryTradeLabel" resultClass="com.raycloud.dmj.domain.trades.Trade">
        SELECT
            GROUP_CONCAT(label_id) tagIds,
            sid
        FROM trade_label_#tradeLabelDbNo#
        WHERE company_id = #companyId#
        AND sid IN <iterate conjunction=", " open="(" close=")" property="sids">#sids[]#</iterate>
        AND enable_status = 1
        group by company_id,sid
    </select>

    <select id="queryLastUnderstockedGroup" resultClass="String">
        select understocked_group from wave_#waveDbNo#
        where company_id = #companyId#
        and warehouse_id = #warehouseId#
        and understocked_group is not null
        and created >= #beginTime# and created &lt;= #endTime#
        order by created desc,id desc
        limit 1
    </select>

    <select id="queryStockOrderRecordBySids" resultMap="waveStockOrderRecord">
        SELECT
            id,sid,order_id,sys_item_id,sys_sku_id,outer_id,num,stock_num,warehouse_id,type
        FROM stock_order_record_#dbNo#
        WHERE company_id = #companyId#
        AND sid IN <iterate conjunction=", " open="(" close=")" property="sids">#sids[]#</iterate>
        AND enable_status = 1
    </select>

    <select id="queryWaveIdBySidOrShortIdOrTidOrOutSid" resultClass="Long">
        select distinct wave_id
        from trade_#tradeDbNo#
        where company_id=#companyId# and enable_status=1 and wave_id != 0
        <isNotEmpty property="querySids" prepend="and">
            sid in <iterate property="querySids" open="(" conjunction="," close=")"> #querySids[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="queryShortIds" prepend="and">
            short_id in <iterate property="queryShortIds" open="(" conjunction="," close=")"> #queryShortIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="queryTids" prepend="and">
            tid in <iterate property="queryTids" open="(" conjunction="," close=")"> #queryTids[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="queryOutSids" prepend="and">
            out_sid in <iterate property="queryOutSids" open="(" conjunction="," close=")"> #queryOutSids[]# </iterate>
        </isNotEmpty>
    </select>

    <select id="queryWaveIdByQueryKey" resultClass="Long">
        select distinct wave_id
        from trade_#tradeDbNo#
        where company_id=#companyId# and enable_status= 1 and wave_id != 0
        <isNotEmpty property="queryKey" >
            <isEqual property="keyIsLong" compareValue="true">
                and ( sid = #queryKey#  or short_id = #queryKey# or tid = #queryKey# or out_sid = #queryKey# )
            </isEqual>
            <isEqual property="keyIsLong" compareValue="false">
                and (tid = #queryKey# or out_sid = #queryKey# )
            </isEqual>
        </isNotEmpty>
    </select>


    <select id="queryChangeItemSku" resultClass="com.raycloud.dmj.domain.wave.WaveItem" timeout="60">
        select
            du.sys_sku_id sysSkuId,
            du.sys_item_id sysItemId,
            du.last_sys_item_id lastSysItemId,
            di.title title,
            di.short_title shortTitle,
            IF(du.pic_path is not null and du.pic_path!='/resources/css/build/images/no_pic.png', du.pic_path, di.pic_path) picPath,
            di.outer_id itemOuterId,
            di.remark itemRemark
        from
            dmj_sku_#skuDbNo# du
        join dmj_item_#itemDbNo# di on  du.last_sys_item_id=di.sys_item_id
        where
        du.company_id = #companyId#
        <isNotEmpty property="sysSkuIds">
            and du.sys_sku_id in <iterate property="sysSkuIds" open="(" conjunction="," close=")">#sysSkuIds[]#</iterate>
        </isNotEmpty>
        and du.sys_item_id != du.last_sys_item_id
    </select>

</sqlMap>
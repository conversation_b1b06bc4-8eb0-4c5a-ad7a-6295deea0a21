<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="WaveUniqueCode">
    <typeAlias alias="WaveUniqueCode" type="com.raycloud.dmj.domain.wave.WaveUniqueCode"/>
    <typeAlias alias="UniqueCodeArrivedCheckSummary" type="com.raycloud.dmj.domain.wave.vo.UniqueCodeArrivedCheckSummary"/>
    <typeAlias alias="UniqueCodeGroupSummaryVO" type="com.raycloud.dmj.domain.wave.vo.UniqueCodeGroupSummaryVO"/>

    <resultMap id="GroupSummaryUniqueCodeMap" class="UniqueCodeGroupSummaryVO">
        <result property="supplierCategory" column="supplierCategory"/>
        <result property="payTime" column="payTime"/>
        <result property="sid" column="sid"/>
        <result property="platformId" column="platformId"/>
        <result property="warehouseId" column="warehouseId"/>
        <result property="supplierId" column="supplierId"/>
        <result property="supplierName" column="supplierName"/>
        <result property="sysItemId" column="sysItemId"/>
        <result property="sysSkuId" column="sysSkuId"/>
        <result property="outerId" column="outerId"/>
        <result property="itemOuterId" column="itemOuterId"/>
        <result property="shortTitle" column="shortTitle"/>
        <result property="mainTitle" column="mainTitle"/>
        <result property="picPath" column="picPath"/>
        <result property="skuOuterId" column="skuOuterId"/>
        <result property="skuRemark" column="skuRemark"/>
        <result property="skuTitle" column="skuTitle"/>
        <result property="propertiesName" column="propertiesName"/>
        <result property="skuPicPath" column="skuPicPath"/>
        <result property="purchaseNum" column="purchaseNum"/>
        <result property="price" column="price"/>
    </resultMap>

    <resultMap id="WaveUniqueCodeMap" class="WaveUniqueCode">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="uniqueCode" column="unique_code"/>
        <result property="sid" column="sid"/>
        <result property="orderId" column="order_id"/>
        <result property="waveId" column="wave_id"/>
        <result property="sysItemId" column="sys_item_id"/>
        <result property="sysSkuId" column="sys_sku_id"/>
        <result property="outerId" column="outer_id"/>
        <result property="created" column="created"/>
        <result property="modified" column="modified"/>
        <result property="matchedStatus" column="matched_status"/>
        <result property="postStatus" column="post_status"/>
        <result property="receiveStatus" column="receive_status"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="stockStatus" column="stock_status"/>
        <result property="type" column="type"/>
        <result property="codeType" column="code_type"/>
        <result property="goodsSectionId" column="goods_section_id"/>
        <result property="goodsSectionCode" column="goods_section_code"/>
        <result property="status" column="status"/>
    </resultMap>

    <resultMap id="unboundUniqueCodeMap" class="WaveUniqueCode" extends="WaveUniqueCodeMap">
        <result property="reasonType" column="reason_type"/>
    </resultMap>

    <resultMap id="OrderUniqueCodeMap" class="WaveUniqueCode" extends="WaveUniqueCodeMap">
        <result property="type" column="type"/>
        <result property="codeType" column="code_type"/>
        <result property="positionNo" column="position_no"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="status" column="status"/>
        <result property="printNum" column="print_num"/>
        <result property="printTime" column="print_time"/>
        <result property="customPrintTime" column="custom_print_time"/>
        <result property="outTime" column="out_time"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="supplierCategory" column="supplier_category"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="dateNo" column="date_no"/>
        <result property="source" column="source"/>
        <result property="batchNo" column="batch_no"/>
        <result property="shortSid" column="short_id"/>
        <result property="userId" column="user_id"/>
        <result property="templateId" column="template_id"/>
        <result property="templateType" column="template_type"/>
        <result property="mainOuterId" column="main_outer_id"/>
        <result property="skuOuterId" column="sku_outer_id"/>
        <result property="propertiesAlias" column="properties_alias"/>
        <result property="propertiesName" column="properties_name"/>
        <result property="itemNum" column="item_num"/>
        <result property="picPath" column="pic_path"/>
        <result property="skuPicPath" column="sku_pic_path"/>
        <result property="shortTitle" column="short_title"/>
        <result property="mainRemark" column="main_remark"/>
        <result property="skuRemark" column="sku_remark"/>
        <result property="positionNoId" column="position_no_id"/>
        <result property="buyerNick" column="buyer_nick"/>
        <result property="mainTitle" column="main_title"/>
        <result property="skuTitle" column="sku_title"/>
        <result property="shareSource" column="share_source"/>
        <result property="positionNoSort" column="position_no_sort"/>
        <result property="businessId" column="business_id"/>
        <result property="businessCode" column="business_code"/>
        <result property="tagIds" column="tag_ids"/>
        <result property="returnOrderStatus" column="return_order_status"/>
        <result property="skuShortTitle" column="sku_short_title"/>
        <result property="processStatus" column="process_status"/>
        <result property="hotSaleCode" column="hot_sale_code"/>
    </resultMap>

    <resultMap id="WaveUniqueCodeForOpenMap" class="WaveUniqueCode" extends="WaveUniqueCodeMap">
        <result property="positionNo" column="position_no"/>
        <result property="positionNoId" column="position_no_id"/>
        <result property="printTime" column="print_time"/>
        <result property="customPrintTime" column="custom_print_time"/>
        <result property="printNum" column="print_num"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="itemBatchNo" column="item_batch_no"/>
        <result property="productionDate" column="production_date"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="hotSaleCode" column="hot_sale_code"/>
        <result property="processStatus" column="process_status"/>
        <result property="businessCode" column="business_code"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="stockPosition" column="stock_position"/>
    </resultMap>

    <resultMap id="WaveUniqueCodeJoinItem" class="WaveUniqueCode" extends="WaveUniqueCodeForOpenMap">
        <result property="mainOuterId" column="main_outer_id"/>
        <result property="skuOuterId" column="sku_outer_id"/>
        <result property="tagIds" column="tag_ids"/>
    </resultMap>

    <resultMap id="OrderUniqueCodeUsedMap" class="WaveUniqueCode">
        <result property="positionNoId" column="position_no_id"/>
        <result property="sid" column="sid"/>
        <result property="itemNum" column="item_num"/>
    </resultMap>

    <resultMap id="OrderUniqueSortUsedMap" class="WaveUniqueCode">
        <result property="positionNoId" column="position_no_id"/>
        <result property="positionNoSort" column="position_no_sort"/>
    </resultMap>

    <resultMap id="OrderUniqueCodeExtMap" class="WaveUniqueCode" extends="OrderUniqueCodeMap">
        <result property="goodsStatus" column="goods_status"/>
        <result property="skuGoodsStatus" column="sku_goods_status"/>
        <result property="newSplit" column="new_split"/>
        <result property="refundStatus" column="refund_status"/>
        <result property="payTime" column="pay_time"/>
        <result property="consignTime" column="consign_time"/>
        <result property="tid" column="tid"/>
        <result property="offTime" column="off_time"/>
        <result property="stockPosition" column="stock_position"/>
        <result property="purchasePrice" column="purchase_price"/>
        <result property="skuPurchasePrice" column="sku_purchase_price"/>
        <result property="timeoutActionTime" column="timeout_action_time"/>
        <result property="logisticsCompanyId" column="logistics_company_id"/>
        <result property="sellingPrice" column="item_selling_price"/>
        <result property="skuSellingPrice" column="sku_selling_price"/>
        <result property="relationChangedException" column="relation_changed_Exception" javaType="boolean" jdbcType="TINYINT"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="platformSkuPropertiesName" column="sku_properties_name"/>
    </resultMap>

    <resultMap id="WaveUniqueCodeQueryMap" class="WaveUniqueCode" extends="WaveUniqueCodeMap">
        <result property="warehouseId" column="warehouse_id"/>
        <result property="createSource" column="create_source"/>
    </resultMap>

    <resultMap id="UniqueCodeTagIdsMap" class="WaveUniqueCode">
        <result property="id" column="id"/>
        <result property="uniqueCode" column="unique_code"/>
        <result property="tagIds" column="tag_ids"/>
        <result property="sysItemId" column="sys_item_id"/>
        <result property="sysSkuId" column="sys_sku_id"/>
        <result property="supplierCategory" column="supplier_category"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="supplierId" column="supplier_id"/>
    </resultMap>

    <insert id="insert">
        insert into wave_unique_code_#dbNo#(company_id
        <isNotNull prepend="," property="uniqueCode"> unique_code </isNotNull>
        <isNotNull prepend="," property="sid"> sid </isNotNull>
        <isNotNull prepend="," property="orderId"> order_id </isNotNull>
        <isNotNull prepend="," property="waveId"> wave_id </isNotNull>
        <isNotNull prepend="," property="sysItemId"> sys_item_id </isNotNull>
        <isNotNull prepend="," property="sysSkuId"> sys_sku_id </isNotNull>
        <isNotNull prepend="," property="outerId"> outer_id </isNotNull>
        <isNotNull prepend="," property="created"> created </isNotNull>
        <isNotNull prepend="," property="modified"> modified </isNotNull>
        <isNotNull prepend="," property="matchedStatus"> matched_status </isNotNull>
        <isNotNull prepend="," property="postStatus"> post_status </isNotNull>
        <isNotNull prepend="," property="receiveStatus"> receive_status </isNotNull>
        <isNotNull prepend="," property="enableStatus"> enable_status </isNotNull>
        <isNotNull prepend="," property="stockStatus"> stock_status </isNotNull>
        <isNotNull prepend="," property="type"> type </isNotNull>
        <isNotNull prepend="," property="codeType"> code_type </isNotNull>
        <isNotNull prepend="," property="positionNo"> position_no </isNotNull>
        <isNotNull prepend="," property="warehouseId"> warehouse_id </isNotNull>
        <isNotNull prepend="," property="status"> status </isNotNull>
        <isNotNull prepend="," property="printNum"> print_num </isNotNull>
        <isNotNull prepend="," property="printTime"> print_time </isNotNull>
        <isNotNull prepend="," property="customPrintTime"> custom_print_time </isNotNull>
        <isNotNull prepend="," property="outTime"> out_time </isNotNull>
        <isNotNull prepend="," property="supplierId"> supplier_id </isNotNull>
        <isNotNull prepend="," property="supplierName"> supplier_name </isNotNull>
        <isNotNull prepend="," property="supplierCategory"> supplier_category </isNotNull>
        <isNotNull prepend="," property="goodsSectionId"> goods_section_id </isNotNull>
        <isNotNull prepend="," property="goodsSectionCode"> goods_section_code </isNotNull>
        <isNotNull prepend="," property="serialNumber"> serial_number </isNotNull>
        <isNotNull prepend="," property="source"> source </isNotNull>
        <isNotNull prepend="," property="batchNo"> batch_no </isNotNull>
        <isNotNull prepend="," property="dateNo"> date_no </isNotNull>
        <isNotNull prepend="," property="itemNum"> item_num </isNotNull>
        <isNotNull prepend="," property="positionNoId"> position_no_id </isNotNull>
        <isNotNull prepend="," property="shareSource"> share_source </isNotNull>
        <isNotNull prepend="," property="positionNoSort"> position_no_sort </isNotNull>
        <isNotNull prepend="," property="tagIds"> tag_ids </isNotNull>
        <isNotNull prepend="," property="createSource"> create_source </isNotNull>
        <isNotNull prepend="," property="intensity"> intensity </isNotNull>
        <isNotNull prepend="," property="costPrice"> cost_price </isNotNull>
        <isNotNull prepend="," property="sellingPrice"> selling_price </isNotNull>
        <isNotNull prepend="," property="weight"> weight </isNotNull>
        <isNotNull prepend="," property="productionDate"> production_date </isNotNull>
        <isNotNull prepend="," property="receiveTime"> receive_time </isNotNull>
        <isNotNull prepend="," property="newSplit"> new_split </isNotNull>
        <isNotNull prepend="," property="offTime"> off_time </isNotNull>
        <isNotNull prepend="," property="stockPosition"> stock_position </isNotNull>
        <isNotNull prepend="," property="itemBatchNo"> item_batch_no </isNotNull>
        <isNotNull prepend="," property="businessId"> business_id </isNotNull>
        <isNotNull prepend="," property="businessCode"> business_code </isNotNull>
        <isNotNull prepend="," property="hotSaleCode"> hot_sale_code </isNotNull>
        ) values (#companyId#
        <isNotNull prepend="," property="uniqueCode"> #uniqueCode# </isNotNull>
        <isNotNull prepend="," property="sid"> #sid# </isNotNull>
        <isNotNull prepend="," property="orderId"> #orderId# </isNotNull>
        <isNotNull prepend="," property="waveId"> #waveId# </isNotNull>
        <isNotNull prepend="," property="sysItemId"> #sysItemId# </isNotNull>
        <isNotNull prepend="," property="sysSkuId"> #sysSkuId# </isNotNull>
        <isNotNull prepend="," property="outerId"> #outerId# </isNotNull>
        <isNotNull prepend="," property="created"> #created# </isNotNull>
        <isNotNull prepend="," property="modified"> #modified# </isNotNull>
        <isNotNull prepend="," property="matchedStatus"> #matchedStatus# </isNotNull>
        <isNotNull prepend="," property="postStatus"> #postStatus# </isNotNull>
        <isNotNull prepend="," property="receiveStatus"> #receiveStatus# </isNotNull>
        <isNotNull prepend="," property="enableStatus"> #enableStatus# </isNotNull>
        <isNotNull prepend="," property="stockStatus"> #stockStatus# </isNotNull>
        <isNotNull prepend="," property="type"> #type# </isNotNull>
        <isNotNull prepend="," property="codeType"> #codeType# </isNotNull>
        <isNotNull prepend="," property="positionNo"> #positionNo# </isNotNull>
        <isNotNull prepend="," property="warehouseId"> #warehouseId# </isNotNull>
        <isNotNull prepend="," property="status"> #status# </isNotNull>
        <isNotNull prepend="," property="printNum"> #printNum# </isNotNull>
        <isNotNull prepend="," property="printTime"> #printTime# </isNotNull>
        <isNotNull prepend="," property="customPrintTime"> #customPrintTime# </isNotNull>
        <isNotNull prepend="," property="outTime"> #outTime# </isNotNull>
        <isNotNull prepend="," property="supplierId"> #supplierId# </isNotNull>
        <isNotNull prepend="," property="supplierName"> #supplierName# </isNotNull>
        <isNotNull prepend="," property="supplierCategory"> #supplierCategory# </isNotNull>
        <isNotNull prepend="," property="goodsSectionId"> #goodsSectionId# </isNotNull>
        <isNotNull prepend="," property="goodsSectionCode"> #goodsSectionCode# </isNotNull>
        <isNotNull prepend="," property="serialNumber"> #serialNumber# </isNotNull>
        <isNotNull prepend="," property="source"> #source# </isNotNull>
        <isNotNull prepend="," property="batchNo"> #batchNo# </isNotNull>
        <isNotNull prepend="," property="dateNo"> #dateNo# </isNotNull>
        <isNotNull prepend="," property="itemNum"> #itemNum# </isNotNull>
        <isNotNull prepend="," property="positionNoId"> #positionNoId# </isNotNull>
        <isNotNull prepend="," property="shareSource"> #shareSource# </isNotNull>
        <isNotNull prepend="," property="positionNoSort"> #positionNoSort# </isNotNull>
        <isNotNull prepend="," property="tagIds"> #tagIds# </isNotNull>
        <isNotNull prepend="," property="createSource"> #createSource# </isNotNull>
        <isNotNull prepend="," property="intensity"> #intensity# </isNotNull>
        <isNotNull prepend="," property="costPrice"> #costPrice# </isNotNull>
        <isNotNull prepend="," property="sellingPrice"> #sellingPrice# </isNotNull>
        <isNotNull prepend="," property="weight"> #weight# </isNotNull>
        <isNotNull prepend="," property="productionDate"> #productionDate# </isNotNull>
        <isNotNull prepend="," property="receiveTime"> #receiveTime# </isNotNull>
        <isNotNull prepend="," property="newSplit"> #newSplit# </isNotNull>
        <isNotNull prepend="," property="offTime"> #offTime# </isNotNull>
        <isNotNull prepend="," property="stockPosition"> #stockPosition# </isNotNull>
        <isNotNull prepend="," property="itemBatchNo"> #itemBatchNo# </isNotNull>
        <isNotNull prepend="," property="businessId"> #businessId# </isNotNull>
        <isNotNull prepend="," property="businessCode"> #businessCode# </isNotNull>
        <isNotNull prepend="," property="hotSaleCode"> #hotSaleCode# </isNotNull>
        )
    </insert>

    <insert id="insertMerge" parameterClass="java.util.HashMap">
        INSERT INTO wave_unique_code_#column.dbNo#
        <dynamic prepend="(" close=")">
            <isNotNull prepend="," property="column.companyId"> company_id </isNotNull>
            <isNotNull prepend="," property="column.id"> id </isNotNull>
            <isNotNull prepend="," property="column.uniqueCode"> unique_code </isNotNull>
            <isNotNull prepend="," property="column.sid"> sid </isNotNull>
            <isNotNull prepend="," property="column.orderId"> order_id </isNotNull>
            <isNotNull prepend="," property="column.waveId"> wave_id </isNotNull>
            <isNotNull prepend="," property="column.sysItemId"> sys_item_id </isNotNull>
            <isNotNull prepend="," property="column.sysSkuId"> sys_sku_id </isNotNull>
            <isNotNull prepend="," property="column.outerId"> outer_id </isNotNull>
            <isNotNull prepend="," property="column.created"> created </isNotNull>
            <isNotNull prepend="," property="column.modified"> modified </isNotNull>
            <isNotNull prepend="," property="column.matchedStatus"> matched_status </isNotNull>
            <isNotNull prepend="," property="column.postStatus"> post_status </isNotNull>
            <isNotNull prepend="," property="column.receiveStatus"> receive_status </isNotNull>
            <isNotNull prepend="," property="column.enableStatus"> enable_status </isNotNull>
            <isNotNull prepend="," property="column.stockStatus"> stock_status </isNotNull>
            <isNotNull prepend="," property="column.type"> type </isNotNull>
            <isNotNull prepend="," property="column.codeType"> code_type </isNotNull>
            <isNotNull prepend="," property="column.positionNo"> position_no </isNotNull>
            <isNotNull prepend="," property="column.warehouseId"> warehouse_id </isNotNull>
            <isNotNull prepend="," property="column.status"> status </isNotNull>
            <isNotNull prepend="," property="column.printNum"> print_num </isNotNull>
            <isNotNull prepend="," property="column.printTime"> print_time </isNotNull>
            <isNotNull prepend="," property="column.customPrintTime"> custom_print_time </isNotNull>
            <isNotNull prepend="," property="column.outTime"> out_time </isNotNull>
            <isNotNull prepend="," property="column.supplierId"> supplier_id </isNotNull>
            <isNotNull prepend="," property="column.supplierName"> supplier_name </isNotNull>
            <isNotNull prepend="," property="column.supplierCategory"> supplier_category </isNotNull>
            <isNotNull prepend="," property="column.goodsSectionId"> goods_section_id </isNotNull>
            <isNotNull prepend="," property="column.goodsSectionCode"> goods_section_code </isNotNull>
            <isNotNull prepend="," property="column.serialNumber"> serial_number </isNotNull>
            <isNotNull prepend="," property="column.source"> source </isNotNull>
            <isNotNull prepend="," property="column.batchNo"> batch_no </isNotNull>
            <isNotNull prepend="," property="column.dateNo"> date_no </isNotNull>
            <isNotNull prepend="," property="column.itemNum"> item_num </isNotNull>
            <isNotNull prepend="," property="column.positionNoId"> position_no_id </isNotNull>
            <isNotNull prepend="," property="column.shareSource"> share_source </isNotNull>
            <isNotNull prepend="," property="column.positionNoSort"> position_no_sort </isNotNull>
            <isNotNull prepend="," property="column.tagIds"> tag_ids </isNotNull>
            <isNotNull prepend="," property="column.createSource"> create_source </isNotNull>
            <isNotNull prepend="," property="column.intensity"> intensity </isNotNull>
            <isNotNull prepend="," property="column.costPrice"> cost_price </isNotNull>
            <isNotNull prepend="," property="column.sellingPrice"> selling_price </isNotNull>
            <isNotNull prepend="," property="column.weight"> weight </isNotNull>
            <isNotNull prepend="," property="column.productionDate"> production_date </isNotNull>
            <isNotNull prepend="," property="column.receiveTime"> receive_time </isNotNull>
            <isNotNull prepend="," property="column.newSplit"> new_split </isNotNull>
            <isNotNull prepend="," property="column.offTime"> off_time </isNotNull>
            <isNotNull prepend="," property="column.stockPosition"> stock_position </isNotNull>
            <isNotNull prepend="," property="column.itemBatchNo"> item_batch_no </isNotNull>
            <isNotNull prepend="," property="column.hotSaleCode"> hot_sale_code </isNotNull>
        </dynamic>
        VALUES
        <iterate property="values" conjunction=",">
            (#values[].companyId#
            <isNotNull prepend="," property="column.id"> #values[].id# </isNotNull>
            <isNotNull prepend="," property="column.uniqueCode"> #values[].uniqueCode# </isNotNull>
            <isNotNull prepend="," property="column.sid"> #values[].sid# </isNotNull>
            <isNotNull prepend="," property="column.orderId"> #values[].orderId# </isNotNull>
            <isNotNull prepend="," property="column.waveId"> #values[].waveId# </isNotNull>
            <isNotNull prepend="," property="column.sysItemId"> #values[].sysItemId# </isNotNull>
            <isNotNull prepend="," property="column.sysSkuId"> #values[].sysSkuId# </isNotNull>
            <isNotNull prepend="," property="column.outerId"> #values[].outerId# </isNotNull>
            <isNotNull prepend="," property="column.created"> #values[].created# </isNotNull>
            <isNotNull prepend="," property="column.modified"> #values[].modified# </isNotNull>
            <isNotNull prepend="," property="column.matchedStatus"> #values[].matchedStatus# </isNotNull>
            <isNotNull prepend="," property="column.postStatus"> #values[].postStatus# </isNotNull>
            <isNotNull prepend="," property="column.receiveStatus"> #values[].receiveStatus# </isNotNull>
            <isNotNull prepend="," property="column.enableStatus"> #values[].enableStatus# </isNotNull>
            <isNotNull prepend="," property="column.stockStatus"> #values[].stockStatus# </isNotNull>
            <isNotNull prepend="," property="column.type"> #values[].type# </isNotNull>
            <isNotNull prepend="," property="column.codeType"> #values[].codeType# </isNotNull>
            <isNotNull prepend="," property="column.positionNo"> #values[].positionNo# </isNotNull>
            <isNotNull prepend="," property="column.warehouseId"> #values[].warehouseId# </isNotNull>
            <isNotNull prepend="," property="column.status"> #values[].status# </isNotNull>
            <isNotNull prepend="," property="column.printNum"> #values[].printNum# </isNotNull>
            <isNotNull prepend="," property="column.printTime"> #values[].printTime# </isNotNull>
            <isNotNull prepend="," property="column.customPrintTime"> #values[].customPrintTime# </isNotNull>
            <isNotNull prepend="," property="column.outTime"> #values[].outTime# </isNotNull>
            <isNotNull prepend="," property="column.supplierId"> #values[].supplierId# </isNotNull>
            <isNotNull prepend="," property="column.supplierName"> #values[].supplierName# </isNotNull>
            <isNotNull prepend="," property="column.supplierCategory"> #values[].supplierCategory# </isNotNull>
            <isNotNull prepend="," property="column.goodsSectionId"> #values[].goodsSectionId# </isNotNull>
            <isNotNull prepend="," property="column.goodsSectionCode"> #values[].goodsSectionCode# </isNotNull>
            <isNotNull prepend="," property="column.serialNumber"> #values[].serialNumber# </isNotNull>
            <isNotNull prepend="," property="column.source"> #values[].source# </isNotNull>
            <isNotNull prepend="," property="column.batchNo"> #values[].batchNo# </isNotNull>
            <isNotNull prepend="," property="column.dateNo"> #values[].dateNo# </isNotNull>
            <isNotNull prepend="," property="column.itemNum"> #values[].itemNum# </isNotNull>
            <isNotNull prepend="," property="column.positionNoId"> #values[].positionNoId# </isNotNull>
            <isNotNull prepend="," property="column.shareSource"> #values[].shareSource# </isNotNull>
            <isNotNull prepend="," property="column.positionNoSort"> #values[].positionNoSort# </isNotNull>
            <isNotNull prepend="," property="column.tagIds"> #values[].tagIds# </isNotNull>
            <isNotNull prepend="," property="column.createSource"> #values[].createSource# </isNotNull>
            <isNotNull prepend="," property="column.intensity"> #values[].intensity# </isNotNull>
            <isNotNull prepend="," property="column.costPrice"> #values[].costPrice# </isNotNull>
            <isNotNull prepend="," property="column.sellingPrice"> #values[].sellingPrice# </isNotNull>
            <isNotNull prepend="," property="column.weight"> #values[].weight# </isNotNull>
            <isNotNull prepend="," property="column.productionDate"> #values[].productionDate# </isNotNull>
            <isNotNull prepend="," property="column.receiveTime"> #values[].receiveTime# </isNotNull>
            <isNotNull prepend="," property="column.newSplit"> #values[].newSplit# </isNotNull>
            <isNotNull prepend="," property="column.offTime"> #values[].offTime# </isNotNull>
            <isNotNull prepend="," property="column.stockPosition"> #values[].stockPosition# </isNotNull>
            <isNotNull prepend="," property="column.itemBatchNo"> #values[].itemBatchNo# </isNotNull>
            <isNotNull prepend="," property="column.hotSaleCode"> #values[].hotSaleCode# </isNotNull>
            )
        </iterate>
    </insert>

    <update id="update">
        update wave_unique_code_#dbNo#
        <dynamic prepend="set">
            <isNotNull prepend="," property="companyId"> company_id = #companyId# </isNotNull>
            <isNotNull prepend="," property="uniqueCode"> unique_code = #uniqueCode# </isNotNull>
            <isNotNull prepend="," property="sid"> sid = #sid# </isNotNull>
            <isNotNull prepend="," property="orderId"> order_id = #orderId# </isNotNull>
            <isNotNull prepend="," property="waveId"> wave_id = #waveId# </isNotNull>
            <isNotNull prepend="," property="sysItemId"> sys_item_id = #sysItemId# </isNotNull>
            <isNotNull prepend="," property="sysSkuId"> sys_sku_id = #sysSkuId# </isNotNull>
            <isNotNull prepend="," property="outerId"> outer_id = #outerId# </isNotNull>
            <isNotNull prepend="," property="modified"> modified = #modified# </isNotNull>
            <isNotNull prepend="," property="matchedStatus"> matched_status = #matchedStatus# </isNotNull>
            <isNotNull prepend="," property="postStatus"> post_status = #postStatus# </isNotNull>
            <isNotNull prepend="," property="receiveStatus"> receive_status = #receiveStatus# </isNotNull>
            <isNotNull prepend="," property="enableStatus"> enable_status = #enableStatus# </isNotNull>
            <isNotNull prepend="," property="stockStatus"> stock_status = #stockStatus# </isNotNull>
            <isNotNull prepend="," property="type"> type = #type# </isNotNull>
            <isNotNull prepend="," property="codeType"> code_type = #codeType# </isNotNull>
            <isNotNull prepend="," property="positionNo"> position_no = #positionNo# </isNotNull>
            <isNotNull prepend="," property="warehouseId"> warehouse_id = #warehouseId# </isNotNull>
            <isNotNull prepend="," property="status"> status = #status# </isNotNull>
            <isNotNull prepend="," property="printNum"> print_num = #printNum# </isNotNull>
            <isNotNull prepend="," property="printTime"> print_time = #printTime# </isNotNull>
            <isNotNull prepend="," property="customPrintTime"> custom_print_time = #customPrintTime# </isNotNull>
            <isNotNull prepend="," property="outTime"> out_time = #outTime# </isNotNull>
            <isNotNull prepend="," property="supplierId"> supplier_id = #supplierId# </isNotNull>
            <isNotNull prepend="," property="supplierName"> supplier_name = #supplierName# </isNotNull>
            <isNotNull prepend="," property="supplierCategory"> supplier_category = #supplierCategory# </isNotNull>
            <isNotNull prepend="," property="goodsSectionId"> goods_section_id = #goodsSectionId# </isNotNull>
            <isNotNull prepend="," property="goodsSectionCode"> goods_section_code = #goodsSectionCode# </isNotNull>
            <isNotNull prepend="," property="serialNumber"> serial_number = #serialNumber# </isNotNull>
            <isNotNull prepend="," property="source"> source = #source# </isNotNull>
            <isNotNull prepend="," property="batchNo"> batch_no = #batchNo# </isNotNull>
            <isNotNull prepend="," property="dateNo"> date_no = #dateNo# </isNotNull>
            <isNotNull prepend="," property="itemNum"> item_num = #itemNum# </isNotNull>
            <isNotNull prepend="," property="positionNoId"> position_no_id = #positionNoId# </isNotNull>
            <isNotNull prepend="," property="businessId"> business_id = #businessId# </isNotNull>
            <isNotNull prepend="," property="businessCode"> business_code = #businessCode# </isNotNull>
            <isNotNull prepend="," property="tagIds"> tag_ids = #tagIds# </isNotNull>
            <isNotNull prepend="," property="createSource"> create_source = #createSource# </isNotNull>
            <isNotNull prepend="," property="intensity"> intensity = #intensity# </isNotNull>
            <isNotNull prepend="," property="costPrice"> cost_price = #costPrice# </isNotNull>
            <isNotNull prepend="," property="sellingPrice"> selling_price = #sellingPrice# </isNotNull>
            <isNotNull prepend="," property="weight"> weight = #weight# </isNotNull>
            <isNotNull prepend="," property="productionDate"> production_date = #productionDate# </isNotNull>
            <isNotNull prepend="," property="returnOrderStatus"> return_order_status = #returnOrderStatus# </isNotNull>
            <isNotNull prepend="," property="receiveTime"> receive_time = #receiveTime# </isNotNull>
            <isNotNull prepend="," property="positionNoSort"> position_no_sort = #positionNoSort# </isNotNull>
            <isNotNull prepend="," property="created"> created = #created# </isNotNull>
            <isNotNull prepend="," property="newSplit"> new_split = #newSplit# </isNotNull>
            <isNotNull prepend="," property="offTime"> off_time = #offTime# </isNotNull>
            <isNotNull prepend="," property="stockPosition"> stock_position = #stockPosition# </isNotNull>
            <isNotNull prepend="," property="unboundLogId"> unbound_log_id = #unboundLogId#</isNotNull>
            <isNotNull prepend="," property="processStatus"> process_status = #processStatus#</isNotNull>
            <isNotNull prepend="," property="itemBatchNo"> item_batch_no = #itemBatchNo#</isNotNull>
        </dynamic>
        where company_id = #companyId# and id = #id#
    </update>

    <update id="updateByUniqueCode">
        update wave_unique_code_#dbNo#
        <dynamic prepend="set">
            <isNotNull prepend="," property="printNum"> print_num = #printNum# </isNotNull>
            <isNotNull prepend="," property="printTime"> print_time = #printTime# </isNotNull>
            <isNotNull prepend="," property="customPrintTime"> custom_print_time = #customPrintTime# </isNotNull>
            <isNotNull prepend="," property="modified"> modified = #modified# </isNotNull>
            <isNotNull prepend="," property="source"> source = #source# </isNotNull>
            <isNotNull prepend="," property="status"> status = #status# </isNotNull>
            <isNotNull prepend="," property="returnOrderStatus"> return_order_status = #returnOrderStatus# </isNotNull>
            <isNotNull prepend="," property="stockPosition"> stock_position = #stockPosition# </isNotNull>
            <isNotNull prepend="," property="itemBatchNo"> item_batch_no = #itemBatchNo# </isNotNull>
        </dynamic>
        where company_id = #companyId# and unique_code = #uniqueCode#
    </update>

    <update id="batchUpdateByIds">
        update wave_unique_code_#dbNo#
        set
            modified = now()
            <isNotNull property="waveUniqueCode.returnOrderStatus"> ,return_order_status = #waveUniqueCode.returnOrderStatus# </isNotNull>
        where
            company_id = #companyId#
            and id in <iterate property="ids" open="(" conjunction="," close=")">#ids[]#</iterate>
    </update>


    <sql id="andMultiIdsSql">
        <isNotEmpty property="waveIds">
            and wave_id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="sids">
            and sid in <iterate property="sids" open="(" conjunction="," close=")">#sids[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="orderIds">
            and order_id in <iterate property="orderIds" open="(" conjunction="," close=")">#orderIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="uniqueCodes">
            and unique_code in <iterate property="uniqueCodes" open="(" conjunction="," close=")">#uniqueCodes[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="matchedStatus">
            and matched_status = #matchedStatus#
        </isNotEmpty>
        <isNotEmpty property="stockStatus">
            and stock_status = #stockStatus#
        </isNotEmpty>
        <isNotEmpty property="matchedStatus">
            and matched_status = #matchedStatus#
        </isNotEmpty>
        <isNotEmpty property="stockStatus">
            and stock_status = #stockStatus#
        </isNotEmpty>
        <isNotEmpty property="outerId">
            and outer_id = #outerId#
        </isNotEmpty>
        <!-- 默认查询波次唯一码 -->
        <isEmpty property="type"> and type = 0 </isEmpty>
        <isNotEmpty property="uniqueCode">
            and unique_code = #uniqueCode#
        </isNotEmpty>
        <isNotEmpty property="type">
            and type = #type#
        </isNotEmpty>
    </sql>

    <select id="queryByKeys" resultMap="WaveUniqueCodeQueryMap">
        select * from wave_unique_code_#dbNo#
        where company_id = #companyId# and enable_status = 1
        <include refid="andMultiIdsSql"/>
    </select>

    <sql id="joinItemSku">
        LEFT JOIN dmj_item_#itemDbNo# b force index(PRIMARY) ON w.company_id = b.company_id AND w.sys_item_id = b.sys_item_id AND b.enable_status = 1
        LEFT JOIN dmj_sku_#skuDbNo# c ON w.company_id = c.company_id AND w.sys_item_id = c.sys_item_id AND w.sys_sku_id = c.sys_sku_id AND c.enable_status = 1
    </sql>

    <select id="queryItemUniqueCodesByCodes" resultMap="WaveUniqueCodeJoinItem">
        select w.*
        <isEqual property="joinItem" compareValue="true">
            , b.outer_id main_outer_id, c.outer_id sku_outer_id
        </isEqual>
        <isEqual property="joinItem" compareValue="false">
            , null as main_outer_id, null as sku_outer_id
        </isEqual>
        from wave_unique_code_#dbNo# w
        <isEqual property="joinItem" compareValue="true">
            <include refid="joinItemSku"/>
        </isEqual>
        where w.company_id = #companyId# and w.enable_status = 1 and w.type in (1, 2)
        and w.unique_code in <iterate property="uniqueCodes" open="(" conjunction="," close=")">#uniqueCodes[]#</iterate>
    </select>


    <select id="queryItemByKeys" resultClass="com.raycloud.dmj.domain.wave.WaveItem">
        select id, sid, order_id orderId, unique_code uniqueCode, wave_id waveId, outer_id outerId, sys_item_id sysItemId, sys_sku_id sysSkuId, goods_section_code goodsSectionCode, code_type codeType
        from wave_unique_code_#dbNo#
        where company_id = #companyId# and enable_status = 1
        <include refid="andMultiIdsSql"/>
    </select>

    <select id="count" resultClass="Long">
        select count(*) from wave_unique_code_#dbNo#
        where company_id = #companyId# and enable_status = 1
        <include refid="andMultiIdsSql"/>
    </select>

    <update id="deleteByIds">
        update wave_unique_code_#dbNo# set enable_status = 0
        where company_id = #companyId#
        <include refid="andMultiIdsSql"/>
    </update>

    <delete id="migrateDelete">
        delete from
        <isEqual property="toOld" compareValue="true">
            wave_unique_code_#dbNo#
        </isEqual>
        <isEqual property="toOld" compareValue="false">
            wave_unique_code_old_#oldDbNo#
        </isEqual>
        where company_id = #companyId# and type = 1 and id >= #startId# and <![CDATA[ id <= #endId# ]]>
        <isEqual property="toOld" compareValue="true">
            <isNotEmpty property="before"> <![CDATA[ and created <= #before# ]]> </isNotEmpty>
        </isEqual>
        <isNotEmpty property="statusList">
            and status in <iterate property="statusList" open="(" conjunction="," close=")"> #statusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="unbind">
            <isEqual property="unbind" compareValue="1"> and (order_id = 0 or order_id is null) </isEqual>
        </isNotEmpty>
    </delete>

    <sql id="migrateColumn">
        id, company_id, unique_code, sid, order_id, wave_id, sys_item_id, sys_sku_id, outer_id, created, modified, matched_status
        ,post_status,receive_status,enable_status,stock_status,`type`,code_type,position_no,warehouse_id,`status`,print_num,
        print_time,out_time,supplier_id,supplier_name,supplier_category,goods_section_id,goods_section_code,serial_number,source,
        batch_no,date_no,item_num,position_no_id,share_source,position_no_sort,tag_ids
    </sql>

    <insert id="migrateInsert" parameterClass="map">
        insert into
        <isEqual property="toOld" compareValue="true">
            wave_unique_code_old_#oldDbNo#
        </isEqual>
        <isEqual property="toOld" compareValue="false">
            wave_unique_code_#dbNo#
        </isEqual>
        (<include refid="migrateColumn"/>)
        select <include refid="migrateColumn"/>
        from
        <isEqual property="toOld" compareValue="true">
            wave_unique_code_#dbNo#
        </isEqual>
        <isEqual property="toOld" compareValue="false">
            wave_unique_code_old_#oldDbNo#
        </isEqual>
        where company_id = #companyId# and type = 1 and id >= #startId# and <![CDATA[ id <= #endId# ]]>
        <isEqual property="toOld" compareValue="true">
            <isNotEmpty property="before"> <![CDATA[ and created <= #before# ]]> </isNotEmpty>
        </isEqual>
        <isNotEmpty property="statusList">
            and status in <iterate property="statusList" open="(" conjunction="," close=")"> #statusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="unbind">
            <isEqual property="unbind" compareValue="1"> and (order_id = 0 or order_id is null) </isEqual>
        </isNotEmpty>
    </insert>

    <update id="resetMatchStatus">
        update wave_unique_code_#dbNo# set matched_status = 0
        where company_id = #companyId#
        and wave_id in <iterate property="waveIds" open="(" conjunction="," close=")"> #waveIds[]# </iterate>
    </update>

    <!-- 下面是订单唯一码使用sql -->

    <sql id="orderUniqueCodeSkuOuterIdsInclude">
        <isEqual property="skuOuterIdsVagueType" compareValue="0"> concat(#outerIds[]#, '%') </isEqual>
        <isEqual property="skuOuterIdsVagueType" compareValue="1"> concat('%', #outerIds[]#, '%') </isEqual>
    </sql>

    <sql id="orderUniqueCodeItemOuterIdsInclude">
        <isEqual property="itemOuterIdsVagueType" compareValue="0"> concat(#itemOuterIds[]#, '%') </isEqual>
        <isEqual property="itemOuterIdsVagueType" compareValue="1"> concat('%', #itemOuterIds[]#, '%') </isEqual>
    </sql>

    <sql id="orderUniqueCodeQuerySql">
        and w.type = 1
        <isNotEmpty property="sids">
            and w.sid in <iterate property="sids" open="(" conjunction="," close=")"> #sids[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="tids">
            and t.tid in <iterate property="tids" open="(" conjunction="," close=")"> #tids[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="platformSkuPropertiesName">
            and o.sku_properties_name like concat('%', #platformSkuPropertiesName#, '%')
        </isNotEmpty>
        <isNotEmpty property="outerIds">
            <isEqual property="skuOuterIdsInclude" compareValue="0">
                <iterate open="(" close=")" conjunction=" or " property="outerIds" prepend=" and "> c.outer_id like <include refid="orderUniqueCodeSkuOuterIdsInclude"/> </iterate>
            </isEqual>
            <isEqual property="skuOuterIdsInclude" compareValue="1">
                and (c.outer_id is null or <iterate open="(" close=")" conjunction=" and " property="outerIds"> c.outer_id not like <include refid="orderUniqueCodeSkuOuterIdsInclude"/> </iterate>)
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="sellerCids" prepend="and">
            <iterate property="sellerCids" open="(" conjunction="or" close=")">
                b.seller_cids <isEqual property="includeItemSeller" compareValue="1"> not </isEqual>  like concat('%', #sellerCids[]#, '%')
            </iterate>
        </isNotEmpty>
        <isNotEmpty property="itemTypes" prepend="and">
            <isEqual property="includeItemType" compareValue="1"> NOT </isEqual> EXISTS (
            SELECT 1 FROM dmj_item_#itemDbNo# item WHERE  item.sys_item_id = b.sys_item_id and
            <iterate property="itemTypes" open="(" conjunction="or" close=")">
                CASE WHEN #itemTypes[]# = 1
                THEN b.type = 1 OR b.type = 2
                END OR
                CASE WHEN #itemTypes[]# = 2
                THEN b.type = 0 AND ( b.type_tag = 1 OR b.type_tag = 2 )
                END OR
                CASE WHEN #itemTypes[]# = 3
                THEN b.type = 0 AND ( b.type_tag = 3 OR b.type_tag = 4 )
                END OR
                CASE WHEN #itemTypes[]# = 0
                THEN b.type = 0 AND b.type_tag = 0
                END
            </iterate>
            )
        </isNotEmpty>
        <isNotEmpty property="warehouseIds">
            and w.warehouse_id in <iterate property="warehouseIds" open="(" conjunction="," close=")"> #warehouseIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="positionNoIds">
            and w.position_no_id in <iterate property="positionNoIds" open="(" conjunction="," close=")"> #positionNoIds[]# </iterate>
        </isNotEmpty>

        <isNotEmpty property="refundStatusList">
            and o.refund_status <isEqual property="includeRefundStatus" compareValue="false"> not </isEqual> in <iterate property="refundStatusList" open="(" conjunction="," close=")"> #refundStatusList[]# </iterate>
        </isNotEmpty>


        <isNotEmpty property="userIds">
            and t.user_id <isEqual property="includeUserIds" compareValue="1"> not </isEqual> in <iterate property="userIds" open="(" conjunction="," close=")"> #userIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="unboundOrUserIds">
            and ( t.user_id <isEqual property="includeUserIds" compareValue="1"> not </isEqual> in <iterate property="unboundOrUserIds" open="(" conjunction="," close=")"> #unboundOrUserIds[]# </iterate> or ( w.order_id = 0 or w.order_id is null or t.sid is null))
        </isNotEmpty>
        <isNotEmpty property="expressIdList">
            and concat(IF(ifnull(t.template_type, 0) = 1,'w',''), ifnull(t.template_id, 0)) <isEqual property="includeExpressIds" compareValue="1"> not </isEqual> in <iterate property="expressIdList" open="(" conjunction="," close=")"> #expressIdList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="logisticsCompanyIdList">
            and t.logistics_company_id <isEqual property="includeLogisticsCompanyIds" compareValue="1"> not </isEqual> in <iterate property="logisticsCompanyIdList" open="(" conjunction="," close=")"> #logisticsCompanyIdList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="supplierIds">
            and w.supplier_id <isEqual property="includeSupplier" compareValue="1"> not </isEqual>
            in <iterate property="supplierIds" open="(" conjunction="," close=")"> #supplierIds[]# </iterate>
        </isNotEmpty>
        <isEmpty property="codeTypes">
            <isNotEmpty property="codeType">
                and w.code_type = #codeType#
            </isNotEmpty>
        </isEmpty>
        <isNotEmpty property="codeTypes">
            and w.code_type in <iterate property="codeTypes" open="(" conjunction="," close=")"> #codeTypes[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="supplierCategoryNames">
            and w.supplier_category <isEqual property="includeSupplierCategory" compareValue="1"> not </isEqual>
            in <iterate property="supplierCategoryNames" open="(" conjunction="," close=")"> #supplierCategoryNames[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="categorySupplierIds">
            and w.supplier_id <isEqual property="includeSupplierCategory" compareValue="1"> not </isEqual>
            in <iterate property="categorySupplierIds" open="(" conjunction="," close=")"> #categorySupplierIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="tagIds" prepend="and">
            <isEqual property="includeTradeTag" compareValue="1"> not </isEqual>
            exists (select 1 from trade_label_#tradeLabelDbNo# tl where tl.company_id = t.company_id and tl.sid = t.sid and tl.enable_status = 1
            and tl.label_id in <iterate property="tagIds" open="(" conjunction="," close=")"> #tagIds[]# </iterate>
            )
        </isNotEmpty>
        <isNotEmpty property="uniqueCodeTagIds" prepend="and">
            <isEqual property="includeUniqueCodeTagIds" compareValue="1"> not </isEqual>
            <iterate property="uniqueCodeTagIds" open="(" close=")" conjunction=" OR "> find_in_set(#uniqueCodeTagIds[]#, w.tag_ids) > 0 </iterate>
        </isNotEmpty>
        <isNotEmpty property="printed">
            <isEqual property="printed" compareValue="0"> and w.print_num = 0 </isEqual>
            <isGreaterThan property="printed" compareValue="0"> and w.print_num > 0 </isGreaterThan>
        </isNotEmpty>
        <isEqual property="includeTime" compareValue="0">
            <isNotEmpty property="createBegin"> <![CDATA[ and w.created >= #createBegin# ]]> </isNotEmpty>
            <isNotEmpty property="createEnd"> <![CDATA[ and w.created <= #createEnd# ]]> </isNotEmpty>
            <isNotEmpty property="printTimeBegin"> <![CDATA[ and w.print_time >= #printTimeBegin# ]]> </isNotEmpty>
            <isNotEmpty property="printTimeEnd"> <![CDATA[ and w.print_time <= #printTimeEnd# ]]> </isNotEmpty>
        </isEqual>
        <isEqual property="includeTime" compareValue="1">
            <isNotEmpty property="createBegin"> <![CDATA[ and w.created <= #createBegin# ]]> </isNotEmpty>
            <isNotEmpty property="createEnd"> <![CDATA[ and w.created >= #createEnd# ]]> </isNotEmpty>
            <isNotEmpty property="printTimeBegin"> <![CDATA[ and w.print_time <= #printTimeBegin# ]]> </isNotEmpty>
            <isNotEmpty property="printTimeEnd"> <![CDATA[ and w.print_time >= #printTimeEnd# ]]> </isNotEmpty>
        </isEqual>
        <isNotEmpty property="customPrintTimeBegin"> <![CDATA[ and w.custom_print_time >= #customPrintTimeBegin# ]]> </isNotEmpty>
        <isNotEmpty property="customPrintTimeEnd"> <![CDATA[ and w.custom_print_time <= #customPrintTimeEnd# ]]> </isNotEmpty>
        <isNotEmpty property="payTimeBegin"> <![CDATA[ and t.pay_time >= #payTimeBegin# ]]> </isNotEmpty>
        <isNotEmpty property="payTimeEnd"> <![CDATA[ and t.pay_time <= #payTimeEnd# ]]> </isNotEmpty>
        <isNotEmpty property="consignTimeBegin"> <![CDATA[ and t.consign_time >= #consignTimeBegin# ]]> </isNotEmpty>
        <isNotEmpty property="consignTimeEnd"> <![CDATA[ and t.consign_time <= #consignTimeEnd# ]]> </isNotEmpty>
        <isNotEmpty property="offTimeBegin"> <![CDATA[ and w.off_time >= #offTimeBegin# ]]> </isNotEmpty>
        <isNotEmpty property="offTimeEnd"> <![CDATA[ and w.off_time <= #offTimeEnd# ]]> </isNotEmpty>
        <isNotEmpty property="receiveTimeBegin"> <![CDATA[ and w.receive_time >= #receiveTimeBegin# ]]> </isNotEmpty>
        <isNotEmpty property="receiveTimeEnd"> <![CDATA[ and w.receive_time <= #receiveTimeEnd# ]]> </isNotEmpty>
        <isNotEmpty property="sysItemIds">
            and w.sys_item_id in <iterate property="sysItemIds" open="(" conjunction="," close=")"> #sysItemIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysSkuIds">
            and w.sys_sku_id in <iterate property="sysSkuIds" open="(" conjunction="," close=")"> #sysSkuIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="ids">
            and w.id in <iterate property="ids" open="(" conjunction="," close=")"> #ids[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="source">
            and w.source = #source#
        </isNotEmpty>
        <isNotEmpty property="dateNoBegin"> <![CDATA[ and w.date_no >= #dateNoBegin# ]]> </isNotEmpty>
        <isNotEmpty property="dateNoEnd"> <![CDATA[ and w.date_no <= #dateNoEnd# ]]> </isNotEmpty>
        <isNotEmpty property="serialNumberBegin"> <![CDATA[ and w.serial_number >= #serialNumberBegin# ]]> </isNotEmpty>
        <isNotEmpty property="serialNumberEnd"> <![CDATA[ and w.serial_number <= #serialNumberEnd# ]]> </isNotEmpty>
        <isNotEmpty property="statusList">
            and w.status in <iterate property="statusList" open="(" conjunction="," close=")"> #statusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="notStatusList">
            and w.status not in <iterate property="notStatusList" open="(" conjunction="," close=")"> #notStatusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="uniqueCodes">
            and w.unique_code in <iterate property="uniqueCodes" open="(" conjunction="," close=")"> #uniqueCodes[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="shortIds">
            and t.short_id in <iterate property="shortIds" open="(" conjunction="," close=")"> #shortIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="stockStatus"> and w.stock_status = #stockStatus# </isNotEmpty>
        <isNotEmpty property="propertiesNames">
            <iterate open="(" close=")" conjunction=" or " property="propertiesNames" prepend=" and "> c.properties_name like concat(#propertiesNames[]#, '%') </iterate>
        </isNotEmpty>
        <isNotEmpty property="itemOuterIds">
            <isEqual property="itemOuterIdsInclude" compareValue="0">
                <iterate open="(" close=")" conjunction=" or " property="itemOuterIds" prepend=" and "> b.outer_id like <include refid="orderUniqueCodeItemOuterIdsInclude"/> </iterate>
            </isEqual>
            <isEqual property="itemOuterIdsInclude" compareValue="1">
                and (b.outer_id is null or <iterate open="(" close=")" conjunction=" and " property="itemOuterIds"> b.outer_id not like <include refid="orderUniqueCodeItemOuterIdsInclude"/> </iterate>)
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="orderIds">
            and w.order_id in <iterate property="orderIds" open="(" conjunction="," close=")"> #orderIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="dateNo">
            and w.date_no = #dateNo#
        </isNotEmpty>
        <isNotEmpty property="batchNo">
            and w.batch_no = #batchNo#
        </isNotEmpty>
        <isNotEmpty property="outerId">
            and (c.outer_id = #outerId# or b.outer_id = #outerId#)
        </isNotEmpty>
        <isNotEmpty property="outerIdLike">
            and (
                c.outer_id like concat('%', #outerIdLike#, '%')
                or b.outer_id like concat('%', #outerIdLike#, '%')
            )
        </isNotEmpty>
        <isNotEmpty property="title">
           and b.title like concat(#title#, '%')
        </isNotEmpty>

        <isNotEmpty property="outerIdOrItemOuterIds">
            and (
                c.outer_id in <iterate property="outerIdOrItemOuterIds" open="(" conjunction="," close=")">#outerIdOrItemOuterIds[]#</iterate>
                or b.outer_id in <iterate property="outerIdOrItemOuterIds" open="(" conjunction="," close=")">#outerIdOrItemOuterIds[]#</iterate>
            )
        </isNotEmpty>
        <isNotEmpty property="shareSource">
            and w.share_source = #shareSource#
        </isNotEmpty>
        <isNotEmpty property="uniqueCodeIds">
            and w.id in
            <iterate property="uniqueCodeIds" open="(" conjunction="," close=")">#uniqueCodeIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="notInIdList">
            and w.id not in
            <iterate property="notInIdList" open="(" conjunction="," close=")">#notInIdList[]#</iterate>
        </isNotEmpty>
        <isEqual property="withOutBusinessId" compareValue="true">
            and (w.business_id is null or w.business_id = -1 or w.business_id = 0)
            and (w.business_code is null or w.business_code = '')
            and ( not exists (select 1 from unique_code_relation_#dbNo# ucr where ucr.company_id = w.company_id and ucr.unique_code = w.unique_code and ucr.enable_status = 1 and ucr.business_type = 1))
        </isEqual>
        <isEqual property="withOutBusinessId" compareValue="false">
            and ((w.business_id is not null and w.business_id != -1 and w.business_id != 0)
            or (w.business_code is not null and w.business_code != '')
            or ( exists (select 1 from unique_code_relation_#dbNo# ucr where ucr.company_id = w.company_id and ucr.unique_code = w.unique_code and ucr.enable_status = 1 and ucr.business_type = 1)))
        </isEqual>
        <isNotEmpty property="unbind">
            <isEqual property="unbind" compareValue="1">and w.order_id != 0 and w.order_id is not null</isEqual>
            <isEqual property="unbind" compareValue="2">and (w.order_id = 0 or w.order_id is null)</isEqual>
        </isNotEmpty>
        <isEqual property="shareFilter" compareValue="true">
            and  ((w.order_id != 0 and w.order_id is not null) or w.code_type = 3)
        </isEqual>
        <isNotEmpty property="businessCodes">
            and w.business_code in
            <iterate property="businessCodes" open="(" conjunction="," close=")">#businessCodes[]#</iterate>
        </isNotEmpty>
        <isEqual property="isMigrateData" compareValue="true">
            <isNotEmpty property="createBegin"> <![CDATA[ and w.created <= #createBegin# ]]> </isNotEmpty>
        </isEqual>
        <isNotEmpty property="relationShareUrl">
            <isEqual property="relationShareUrl" compareValue="true">
                and (exists(select 1 from share_url where company_id = #companyId# and enable_status = 1 and supplier_id = w.supplier_id) and w.stock_status = 0)
            </isEqual>
            <isEqual property="relationShareUrl" compareValue="false">
                and (not exists(select 1 from share_url where company_id = #companyId# and enable_status = 1 and supplier_id = w.supplier_id) or w.stock_status = 1)
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="relationSidStatusList">
            and w.sid in (select wuc.sid from wave_unique_code_#dbNo# wuc force index(company_type_status_index)
                        where wuc.company_id = #companyId# and wuc.enable_status = 1 and wuc.type = 1
                        and wuc.status in <iterate property="relationSidStatusList" open="(" conjunction="," close=")"> #relationSidStatusList[]# </iterate>
                    )
        </isNotEmpty>
        <isNotEmpty property="positionGroups">
            <iterate open="(" close=")" conjunction=" or " property="positionGroups" prepend=" and "> w.position_no like concat(#positionGroups[]#, '%') </iterate>
        </isNotEmpty>
        <isNotEmpty property="positionNo">
            and replace(position_no,'-','') = #positionNo#
        </isNotEmpty>
        <isNotEmpty property="positionNoSort">
            and position_no_sort = #positionNoSort#
        </isNotEmpty>
        <isNotEmpty property="newSplit">
            and new_split = #newSplit#
        </isNotEmpty>
        <isNotEmpty property="operateSource">
            <isEqual property="operateSource" compareValue="4">
                and ( (code_type = 2 and position_no_id > 0) or code_type in (1,3) )
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="itemTagIds">
            and exists (select 1 from item_tag_relation_#itemTagRelationDbNo# itr where  w.company_id = itr.company_id and w.sys_item_id = itr.sys_item_id  AND  if(w.sys_sku_id >0,w.sys_sku_id,0) = itr.sys_sku_id
            and itr.item_tag_id in <iterate property="itemTagIds" open="(" conjunction="," close=")"> #itemTagIds[]# </iterate>
            AND itr.enable_status = 1
            )
        </isNotEmpty>
        <isNotNull property="reAllocateType">
            <!-- 固定供应商生成家里 唯一码状态是【等待拣选】、创建时间是3个月内、未解绑、标签为【缺货】、不在正在进行的加工计划中的 -->
            <isEqual property="reAllocateType" compareValue="1">
                and not exists (select 1 from process_plan_order_#ppoDbNo# ppo join process_plan_#ppDbNo# pp on ppo.plan_id = pp.id and ppo.company_id  = pp.company_id
                            where pp.status in (1,2) and ppo.unique_code = w.unique_code and ppo.company_id  = w.company_id )
                and find_in_set('4', w.tag_ids) > 0 and w.status = 2
            </isEqual>
            <!-- 非固定供应商生成家里 唯一码状态是【等待拣选】【等待采购】【等待收货】，未解绑，创建时间是3个月内-->
            <isEqual property="reAllocateType" compareValue="2">
                and w.status in (1,2,4)
            </isEqual>
            and w.order_id != 0 and w.order_id is not null
            <isNotEmpty property="forceSupplierIds">
                and w.supplier_id <isEqual property="reAllocateType" compareValue="2"> not </isEqual>
                in <iterate property="forceSupplierIds" open="(" conjunction="," close=")"> #forceSupplierIds[]# </iterate>
            </isNotEmpty>
        </isNotNull>
        <isEqual property="suit" compareValue="true">
            and o.type = 2 and o.combine_id > 0 and exists (select 1 from order_#orderDbNo# oo where oo.id = o.combine_id and w.company_id = oo.company_id
                                                    and oo.item_sys_id in <iterate property="suitSysItemIds" open="(" conjunction="," close=")"> #suitSysItemIds[]# </iterate>
                                                    and oo.sku_sys_id in <iterate property="suitSysSkuIds" open="(" conjunction="," close=")"> #suitSysSkuIds[]# </iterate>
                                                    )
        </isEqual>
        <isNotEmpty property="stockRegionIds">
            and exists (select 1 from warehouse_position_config_#warehousePositionConfigNo# p
            where p.company_id = w.company_id and w.position_no_id = p.id and p.enable_status = 1
            and p.stock_region_id in <iterate property="stockRegionIds" open="(" conjunction="," close=")"> #stockRegionIds[]# </iterate>)
        </isNotEmpty>
        <isEqual property="filterHotSaleCode" compareValue="true">
            and w.hot_sale_code = ''
        </isEqual>
        <isNotEmpty property="sellerFlags">
            and (t.seller_flag in <iterate property="sellerFlags" open="(" conjunction="," close=")"> #sellerFlags[]# </iterate>
            <isEqual property="containEmptySellerFlag" compareValue="true">
                or t.seller_flag is null
            </isEqual>
            )
        </isNotEmpty>
        <isNotEmpty property="takenOrderStatus">
            <isEqual property="takenOrderStatus" compareValue="0">
                and not exists (select 1 from unique_code_relation_#dbNo# ucr
                where ucr.company_id = w.company_id and ucr.unique_code = w.unique_code and ucr.enable_status = 1 and ucr.business_type = 22 and ucr.status != 20 )
            </isEqual>
            <isEqual property="takenOrderStatus" compareValue="1">
                and exists (select 1 from unique_code_relation_#dbNo# ucr
                where ucr.company_id = w.company_id and ucr.unique_code = w.unique_code and ucr.enable_status = 1 and ucr.business_type = 22 and ucr.status != 20 )
            </isEqual>
        </isNotEmpty>
    </sql>

    <sql id="sortSql">
        <isNotEmpty property="sortField">
            order by
            <isEqual property="sortField" compareValue="mainOuterId"> b.outer_id </isEqual>
            <isNotEqual property="sortOrder" compareValue="2">
                <isEqual property="sortField" compareValue="skuOuterId"> c.outer_id </isEqual>
                <isEqual property="sortField" compareValue="supplierName"> w.supplier_name = '', w.supplier_name </isEqual>
            </isNotEqual>
            <isEqual property="sortField" compareValue="payTime"> t.pay_time </isEqual>
            <isEqual property="sortField" compareValue="consignTime"> t.consign_time </isEqual>
            <isEqual property="sortField" compareValue="id"> w.id </isEqual>
            <isEqual property="sortField" compareValue="positionNoId"> w.position_no_id </isEqual>
            <isEqual property="sortField" compareValue="shareSort"> w.outer_id, field(w.code_type, 2,1,3)</isEqual>
            <isEqual property="sortField" compareValue="timeoutActionTime"> (t.timeout_action_time is null or t.timeout_action_time = '2000-01-01 00:00:00'), ifnull(t.timeout_action_time, '2000-01-01 00:00:00') </isEqual>
            <isEqual property="sortField" compareValue="created"> w.created </isEqual>
            <isEqual property="sortOrder" compareValue="0"> asc </isEqual>
            <isEqual property="sortOrder" compareValue="1"> desc </isEqual>
            <isEqual property="sortOrder" compareValue="2">
                <isEqual property="sortField" compareValue="skuOuterId"> uce.live_property IS NULL, uce.live_property + 0 </isEqual>
                <isEqual property="sortField" compareValue="supplierName">
                    <isNotEmpty property="sortSupplierIds">
                        <isEqual property="sortOrder" compareValue="2">
                            field(w.supplier_id,
                            <iterate property="sortSupplierIds" conjunction=","> #sortSupplierIds[]# </iterate>
                            , 0)
                        </isEqual>
                    </isNotEmpty>
                </isEqual>
            </isEqual>
            <isEqual property="sortField" compareValue="payTime"> , w.unique_code </isEqual>
            <isNotEqual property="sortField" compareValue="payTime"> , w.id </isNotEqual>
        </isNotEmpty>
        <isEqual property="isMigrateData" compareValue="true">
            <isNotEmpty property="createBegin">w.created desc </isNotEmpty>
        </isEqual>
    </sql>

    <sql id="sourceData">
        <isEqual property="queryMigrateData" compareValue="true"> from wave_unique_code_old_#dbNo# w </isEqual>
        <isEqual property="queryMigrateData" compareValue="false"> from wave_unique_code_#dbNo# w </isEqual>
    </sql>

    <sql id="orderUniqueCodeSortSql">
        <isEqual property="sortType" compareValue="2">
            order by code_type, id
        </isEqual>
        <isEqual property="sortType" compareValue="3">
            order by FIELD(code_type, 2, 1, 3), id
        </isEqual>
    </sql>

    <sql id="orderUniqueCodeReceiveStatusSql">
        <isEqual property="receiveStatus" compareValue="0">
            and exists (SELECT sid FROM wave_unique_code_#dbNo# WHERE company_id=#companyId# AND type=1 AND STATUS IN (1,2,4) AND sid=w.sid)
            and exists (SELECT sid FROM wave_unique_code_#dbNo# WHERE company_id=#companyId# AND type=1 AND STATUS IN (3,5) AND sid=w.sid)
        </isEqual>
        <isEqual property="receiveStatus" compareValue="1">
            and not exists (SELECT sid FROM wave_unique_code_#dbNo# WHERE company_id=#companyId# AND type=1 AND STATUS IN (1,2,4,6,9,10,11,13) AND sid=w.sid) and w.status in (3,5)
        </isEqual>
        <isEqual property="receiveStatus" compareValue="2">
            and not exists (SELECT sid FROM wave_unique_code_#dbNo# WHERE company_id=#companyId# AND type=1 AND STATUS IN (3,5,9,10,11,13) AND sid=w.sid) and w.status in (1,2,4,8)
        </isEqual>
    </sql>

    <sql id="joinExtendSQL">
        LEFT JOIN unique_code_extend_#uniqueCodeExtendDbNo# uce ON w.company_id = uce.company_id AND w.id = uce.unique_code_id AND uce.enable_status = 1
    </sql>

    <sql id="joinExtend">
        <isEqual property="sortField" compareValue="skuOuterId">
            <isEqual property="sortOrder" compareValue="2">
                <include refid="joinExtendSQL"/>
            </isEqual>
        </isEqual>
    </sql>

    <select id="queryOrderUniqueCodeByCondition" resultMap="OrderUniqueCodeExtMap" timeout="30">
        /*FORCE_MASTER*/ select w.*, o.refund_status, t.pay_time, t.consign_time, t.tid, t.short_id, t.user_id, t.template_id, t.template_type,t.logistics_company_id,
        b.outer_id main_outer_id,b.remark main_remark,c.remark sku_remark,b.short_title, c.outer_id sku_outer_id, c.properties_name, c.properties_alias,
        b.pic_path, c.pic_path sku_pic_path, t.buyer_nick, b.title main_title, c.title sku_title,b.goods_status,c.goods_status sku_goods_status,
        b.purchase_price, c.purchase_price sku_purchase_price, c.short_title AS sku_short_title, t.timeout_action_time, b.selling_price AS item_selling_price,
        c.selling_price AS sku_selling_price, o.sku_properties_name,
        if(o.relation_changed = 1 or exists (select 1 from order_#orderDbNo# oo where oo.company_id = #companyId# and oo.id = o.combine_id and oo.relation_changed = 1), 1, 0) AS relation_changed_exception
        <include refid="sourceData"/>
        <isEqual property="forceCreatedIndex" compareValue="true"> force index(company_created_index) </isEqual>
        <isEqual property="forceStatusIndex" compareValue="true">
            <isEqual property="queryMigrateData" compareValue="false"> force index(company_type_status_index) </isEqual>
        </isEqual>
        <isEqual property="forceTypeStatusIndex" compareValue="true"> force index(company_type_status_index) </isEqual>
        <include refid="joinExtend"/>
        <include refid="joinItemSku"/>
        left join trade_#tradeDbNo# t on w.sid = t.sid and w.company_id = t.company_id
        left join order_#orderDbNo# o on w.order_id = o.id
        where
        <isEqual property="needTodayBack" compareValue="true"> ( </isEqual>
        w.company_id = #companyId# and w.enable_status = 1
        <include refid="orderUniqueCodeQuerySql"/>
        <include refid="orderUniqueCodeReceiveStatusSql"/>
        <isEqual property="relationChanged" compareValue="1">
            and (o.relation_changed = 1 or exists (select 1 from order_#orderDbNo# oo where oo.company_id = #companyId# and oo.id = o.combine_id and oo.relation_changed = 1))
        </isEqual>
        <isEqual property="needTodayBack" compareValue="true">
           ) or (w.company_id = #companyId# AND w.type = 1 AND w.created >= CURRENT_DATE AND w.created &lt; CURRENT_DATE + INTERVAL 1 DAY AND w.code_type = 3 AND w.enable_status = 1 AND w.print_num = 0)
        </isEqual>
        <include refid="sortSql"/>
        <include refid="orderUniqueCodeSortSql"/>
        <isEqual property="needGroupByItem" compareValue="1">
            group by w.sys_item_id, w.sys_sku_id
        </isEqual>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="countOrderUniqueCode" resultClass="long" timeout="30">
        select count(*)
        <include refid="sourceData"/>
        <isEqual property="forceStatusIndex" compareValue="true">
            <isEqual property="queryMigrateData" compareValue="false"> force index(company_type_status_index) </isEqual>
        </isEqual>
        <isEqual property="forceTypeStatusIndex" compareValue="true"> force index(company_type_status_index) </isEqual>
        <isEqual property="needJoinItemInfo" compareValue="true">
            <include refid="joinItemSku"/>
        </isEqual>
        <isEqual property="needJoinTradeInfo" compareValue="true">
            left join trade_#tradeDbNo# t on w.sid = t.sid and w.company_id = t.company_id
            left join order_#orderDbNo# o on w.order_id = o.id
        </isEqual>
        where
        <isEqual property="needTodayBack" compareValue="true"> ( </isEqual>
        w.company_id = #companyId# and w.enable_status = 1
        <include refid="orderUniqueCodeQuerySql"/>
        <include refid="orderUniqueCodeReceiveStatusSql"/>
        <isEqual property="relationChanged" compareValue="1">
            and (o.relation_changed = 1 or exists (select 1 from order_#orderDbNo# oo where oo.company_id = #companyId# and oo.id = o.combine_id and oo.relation_changed = 1))
        </isEqual>
        <isEqual property="needTodayBack" compareValue="true">
            ) or (w.company_id = #companyId# AND w.type = 1 AND w.created >= CURRENT_DATE AND w.created &lt; CURRENT_DATE + INTERVAL 1 DAY AND w.code_type = 3 AND w.enable_status = 1 AND w.print_num = 0)
        </isEqual>
    </select>

    <select id="queryUniqueCodeMaxIdSimple" resultClass="long">
        SELECT max(id) FROM wave_unique_code_#dbNo# WHERE company_id = #companyId# AND type = 1 <![CDATA[ AND created <= #createBegin# ]]>
        <isNotEmpty property="statusList">
            and status in <iterate property="statusList" open="(" conjunction="," close=")"> #statusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="unbind">
            <isEqual property="unbind" compareValue="1"> and (order_id = 0 or order_id is null) </isEqual>
        </isNotEmpty>
    </select>

    <select id="queryOrderUniqueCodeUsed" resultMap="OrderUniqueCodeUsedMap">
        select position_no_id, sid, item_num from wave_unique_code_#dbNo#
        force index(company_type_status_index)
        where company_id = #companyId#
        and warehouse_id = #warehouseId#
        <isEqual property="containItemUniqueCode" compareValue="true">
            and type in (1, 2)
            and `status` not in (6, 7, 9, 12)
        </isEqual>
        <isEqual property="containItemUniqueCode" compareValue="false">
            and type = 1
            and status not in (6, 7, 9)
        </isEqual>
        and code_type = 2
        and matched_status = 0
        and position_no_id > 0
        and enable_status = 1
        and order_id > 0
        <isNotEmpty property="created">
            and (<![CDATA[ created >= #created# ]]> or new_split = 1)
        </isNotEmpty>
        group by sid
    </select>

    <select id="queryMatchUniqueCodes" resultClass="String">
        select unique_code from wave_unique_code_#dbNo#
        where company_id = #companyId#
        <isNotEmpty property="warehouseId">
            and warehouse_id = #warehouseId#
        </isNotEmpty>
        <isNotEmpty property="sysItemIds">
            and sys_item_id in <iterate property="sysItemIds" open="(" conjunction="," close=")"> #sysItemIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysSkuIds">
            and sys_sku_id in <iterate property="sysSkuIds" open="(" conjunction="," close=")"> #sysSkuIds[]# </iterate>
        </isNotEmpty>
        and enable_status = 1
        and status in (1, 4, 5)
        and print_num = 0
        and type = 1
    </select>

    <select id="queryOrderUniqueSortUsed" resultMap="OrderUniqueSortUsedMap">
        select position_no_id, position_no_sort from wave_unique_code_#dbNo#
        force index(company_type_status_index)
        where company_id = #companyId#
        and warehouse_id = #warehouseId#
        <isEqual property="containItemUniqueCode" compareValue="true">
            and type in (1, 2)
            and status not in (6, 7, 9, 12)
        </isEqual>
        <isEqual property="containItemUniqueCode" compareValue="false">
            and type = 1
            and status not in (6, 7, 9)
        </isEqual>
        <!-- 多件订单唯一码 -->
        and code_type = 2 and position_no_id > 0
        <!-- 未释放的分拣货位 -->
        and matched_status = 0
        <!-- 非出库、取消状态 -->
        and enable_status = 1
        and order_id > 0
        and position_no_id in
        <iterate property="positionNoIds" open="(" conjunction="," close=")"> #positionNoIds[]# </iterate>
        <isNotEmpty property="created">
            and (<![CDATA[ created >= #created# ]]> or new_split = 1)
        </isNotEmpty>
        group by sid
    </select>

    <select id="queryPositionNoBySid" resultClass="hashMap">
        select * from (select DISTINCT(sid), position_no from wave_unique_code_#dbNo#
        where company_id = #companyId#
        and sid in <iterate property="sids" open="(" conjunction="," close=")"> #sids[]# </iterate>
        and enable_status = 1 order by id desc) c group by sid
    </select>

    <select id="queryActiveUniqueCodeMap" resultClass="hashMap">
        select sid, count(*) active_num from wave_unique_code_#dbNo#
        where company_id = #companyId#
        and sid in <iterate property="sids" open="(" conjunction="," close=")"> #sids[]# </iterate>
        and enable_status = 1 and `status` not in (6, 7, 9) group by sid
    </select>

    <select id="queryCancelOrderIds" resultClass="long">
        select wuc.id
        from wave_unique_code_#dbNo# wuc force index(company_dateno_batchno_index)
        join trade_not_consign_#tradeDbNo# t on t.sid = wuc.sid and t.company_id = wuc.company_id
        where wuc.company_id = #companyId#
        and t.enable_status = 1
        and t.sys_status in ('WAIT_BUYER_PAY', 'WAIT_AUDIT', 'WAIT_FINANCE_AUDIT', 'SELLER_SEND_GOODS', 'FINISHED', 'CLOSED', 'CANCEL')
        and t.is_cancel = 0
        and wuc.batch_no = #batchNo#
        and wuc.date_no = #dateNo#
        and wuc.type = 1
    </select>

    <update id="updateOuterIdByOrderId">
        update wave_unique_code_#dbNo#
        <dynamic prepend="set">
            <isNotNull prepend="," property="outerId"> outer_id = #outerId# </isNotNull>
        </dynamic>
        where company_id = #companyId# and order_id = #orderId#
    </update>

    <select id="queryUniqueCodeId4RefreshSupplier" resultClass="Long">
        select id from wave_unique_code_#dbNo#
        where company_id = #companyId#
        and enable_status = 1
        and type = 1
        and status in (1, 4)
        and order_id > 0
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="queryTodayUsePostionIds" resultClass="Long">
        select position_no_id from wave_unique_code_#dbNo#
        force index(company_type_status_index)
        where company_id = #companyId#
        and <![CDATA[ created >= #todayBegin# ]]>
        and enable_status = 1
        <isEqual property="containItemUniqueCode" compareValue="true">
            and type in (1, 2)
            and `status` not in (7, 12)
        </isEqual>
        <isEqual property="containItemUniqueCode" compareValue="false">
            and type = 1
            and `status` not in (7)
        </isEqual>
        and code_type = 2
        and position_no_id is not null
        and position_no_id > 0
        and order_id > 0
        group by position_no_id
    </select>


    <delete id="deleteByParams">
        delete from wave_unique_code_#dbNo#
        where company_id = #companyId#
        <isNotEmpty property="sids">
            and sid in <iterate property="sids" open="(" conjunction="," close=")"> #sids[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="uniqueCodes">
            and unique_code in <iterate property="uniqueCodes" open="(" conjunction="," close=")"> #uniqueCodes[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="createBegin"> <![CDATA[ and created >= #createBegin# ]]> </isNotEmpty>
        <isNotEmpty property="createEnd"> <![CDATA[ and created <= #createEnd# ]]> </isNotEmpty>
    </delete>

    <!-- 下面是商品唯一码使用sql -->

    <resultMap id="ItemUniqueCodeMap" class="WaveUniqueCode" extends="OrderUniqueCodeMap">
        <result property="createSource" column="create_source"/>
        <result property="intensity" column="intensity"/>
        <result property="costPrice" column="cost_price"/>
        <result property="sellingPrice" column="selling_price"/>
        <result property="weight" column="weight"/>
        <result property="productionDate" column="production_date"/>
        <result property="itemBarcode" column="item_barcode"/>
        <result property="skuBarcode" column="sku_barcode"/>
        <result property="itemBrand" column="item_brand"/>
        <result property="skuBrand" column="sku_brand"/>
        <result property="catId" column="cat_id"/>
        <result property="sellerCids" column="seller_cids"/>
        <result property="itemPeriodCast" column="item_period_cast"/>
        <result property="skuPeriodCast" column="sku_period_cast"/>
        <result property="itemUniqueCodeType" column="item_unique_code_type"/>
        <result property="itemActiveStatus" column="item_active_status"/>
        <result property="stockPosition" column="stock_position"/>
        <result property="itemBatchNo" column="item_batch_no"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="ICCID" column="ICCID"/>
        <result property="tradeWaveId" column="trade_wave_id"/>
        <result property="payTime" column="pay_time"/>
        <result property="extendField2" column="extend_field2"/>
        <result property="extendField3" column="extend_field3"/>
        <result property="extendField4" column="extend_field4"/>
        <result property="extendField5" column="extend_field5"/>
        <result property="suitPositionNo" column="suit_position_no"/>
        <result property="logisticsCompanyId" column="logistics_company_id"/>
    </resultMap>

    <sql id="itemUniqueCodeColumn">
        w.*,
        <isEqual property="needExtendMsg" compareValue="true">
            uce.ICCID, uce.extend_field2, uce.extend_field3, uce.extend_field4, uce.extend_field5, uce.suit_position_no,
        </isEqual>
        <isEqual property="needExtendMsg" compareValue="false">
            null as ICCID, null as extend_field2, null as extend_field3, null as extend_field4, null as extend_field5, null as suit_position_no,
        </isEqual>
        t.short_id, t.user_id, t.template_id, t.template_type, t.wave_id as trade_wave_id, t.pay_time,
        b.outer_id main_outer_id, c.outer_id sku_outer_id, c.properties_name, c.properties_alias,
        b.pic_path, c.pic_path sku_pic_path, t.buyer_nick, b.title main_title, c.title sku_title,
        b.barcode item_barcode, c.barcode sku_barcode, b.brand item_brand, c.brand sku_brand,
        b.cat_id, b.seller_cids, b.period_cast item_period_cast, c.period_cast sku_period_cast,
        b.remark main_remark, c.remark sku_remark, b.short_title, ifnull(c.unique_code_type, b.unique_code_type) item_unique_code_type,
        c.short_title AS sku_short_title,ifnull(c.active_status, b.active_status) item_active_status,
        t.logistics_company_id
    </sql>

    <sql id="itemUniqueCodeQuerySql">
        and w.type in (1, 2)
        <isEmpty property="enableStatus">
            and w.`enable_status` = 1
        </isEmpty>
        <isNotEmpty property="enableStatus">
            and w.`enable_status` = #enableStatus#
        </isNotEmpty>
        <isNotEmpty property="warehouseIds">
            and w.warehouse_id in <iterate property="warehouseIds" open="(" conjunction="," close=")"> #warehouseIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="type">
            and w.`type` = #type#
        </isNotEmpty>
        <isNotEmpty property="codeTypes">
            and w.code_type in <iterate property="codeTypes" open="(" conjunction="," close=")">#codeTypes[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="tradeWaveIds">
            and t.wave_id in <iterate property="tradeWaveIds" open="(" conjunction="," close=")">#tradeWaveIds[]#</iterate>
        </isNotEmpty>
        <isNotEmpty property="notStatusList">
            and w.status not in <iterate property="notStatusList" open="(" conjunction="," close=")"> #notStatusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="orderIds">
            and w.order_id in <iterate property="orderIds" open="(" conjunction="," close=")"> #orderIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="supplierIds">
            and w.supplier_id in <iterate property="supplierIds" open="(" conjunction="," close=")"> #supplierIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="createSources">
            and w.create_source in <iterate property="createSources" open="(" conjunction="," close=")"> #createSources[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="intensity">
            and w.intensity = #intensity#
        </isNotEmpty>
        <isNotEmpty property="statusList">
            and w.status in <iterate property="statusList" open="(" conjunction="," close=")"> #statusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="stockStatus">
            and w.stock_status = #stockStatus#
        </isNotEmpty>
        <isNotEmpty property="uniqueCodes">
            and w.unique_code in <iterate property="uniqueCodes" open="(" conjunction="," close=")"> #uniqueCodes[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="stockPositions">
            and w.stock_position in <iterate property="stockPositions" open="(" conjunction="," close=")"> #stockPositions[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="serialNumbers">
            and concat(w.date_no, '-', w.serial_number) in <iterate property="serialNumbers" open="(" conjunction="," close=")"> #serialNumbers[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="printed">
            <isEqual property="printed" compareValue="0"> and w.print_num = 0 </isEqual>
            <isGreaterThan property="printed" compareValue="0"> and w.print_num > 0 </isGreaterThan>
        </isNotEmpty>
        <isNotEmpty property="uniqueCodeTagIds" prepend="and">
            <isEqual property="includeUniqueCodeTagIds" compareValue="1"> not </isEqual>
            <iterate property="uniqueCodeTagIds" open="(" close=")" conjunction=" OR "> find_in_set(#uniqueCodeTagIds[]#, w.tag_ids) > 0 </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysItemIds">
            and w.sys_item_id in <iterate property="sysItemIds" open="(" conjunction="," close=")"> #sysItemIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="sysSkuIds">
            and w.sys_sku_id in <iterate property="sysSkuIds" open="(" conjunction="," close=")"> #sysSkuIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="createBegin"> <![CDATA[ and w.created >= #createBegin# ]]> </isNotEmpty>
        <isNotEmpty property="createEnd"> <![CDATA[ and w.created <= #createEnd# ]]> </isNotEmpty>
        <isNotEmpty property="printTimeBegin"> <![CDATA[ and w.print_time >= #printTimeBegin# ]]> </isNotEmpty>
        <isNotEmpty property="printTimeEnd"> <![CDATA[ and w.print_time <= #printTimeEnd# ]]> </isNotEmpty>
        <isNotEmpty property="customPrintTimeBegin"> <![CDATA[ and w.custom_print_time >= #customPrintTimeBegin# ]]> </isNotEmpty>
        <isNotEmpty property="customPrintTimeEnd"> <![CDATA[ and w.custom_print_time <= #customPrintTimeEnd# ]]> </isNotEmpty>
        <isNotEmpty property="ids">
            and w.id in <iterate property="ids" open="(" conjunction="," close=")"> #ids[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="itemOuterIds">
            <isEqual property="itemOuterIdsVagueType" compareValue="1">
                <iterate open="(" close=")" conjunction=" or " property="itemOuterIds" prepend=" and "> b.outer_id like concat('%', #itemOuterIds[]#, '%') </iterate>
            </isEqual>
            <isEqual property="itemOuterIdsVagueType" compareValue="2">
                and b.outer_id in <iterate property="itemOuterIds" open="(" conjunction="," close=")"> #itemOuterIds[]# </iterate>
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="skuOuterIds">
            and c.outer_id in <iterate property="skuOuterIds" open="(" conjunction="," close=")"> #skuOuterIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="outerIds">
            <isEqual property="outerIdsVagueType" compareValue="1">
                and (<iterate open="(" close=")" conjunction=" or " property="outerIds"> c.outer_id like concat('%', #outerIds[]#, '%') </iterate> or <iterate open="(" close=")" conjunction=" or " property="outerIds"> b.outer_id like concat('%', #outerIds[]#, '%') </iterate>)
            </isEqual>
            <isEqual property="outerIdsVagueType" compareValue="2">
                and (c.outer_id in <iterate property="outerIds" open="(" conjunction="," close=")"> #outerIds[]# </iterate> or b.outer_id in <iterate property="outerIds" open="(" conjunction="," close=")"> #outerIds[]# </iterate>)
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="outerId">
            and ((b.is_sku_item = 0 and b.outer_id = #outerId#) or ( b.is_sku_item = 1 and c.outer_id = #outerId#))
        </isNotEmpty>
        <isNotEmpty property="outerIdLike">
            and ((b.is_sku_item = 0 and b.outer_id like concat('%', #outerIdLike#, '%')) or ( b.is_sku_item = 1 and c.outer_id like concat('%', #outerIdLike#, '%')))
        </isNotEmpty>
        <isEqual property="withBusinessId" compareValue="true">
            and ((w.business_id is not null and w.business_id != -1 and w.business_id != 0)
            or (w.business_code is not null and w.business_code != '')
            or ( exists (select 1 from unique_code_relation_#dbNo# ucr where ucr.company_id = w.company_id and ucr.unique_code = w.unique_code and ucr.enable_status = 1 and ucr.business_type = 1)))
        </isEqual>
        <isEqual property="printFilter" compareValue="true">
            and  ((w.sid != 0 and w.sid is not null) or w.code_type = 3)
        </isEqual>
        <isNotEmpty property="shipperId">
            and ((b.is_sku_item = 0 and b.shipper_id = #shipperId#) or (b.is_sku_item = 1 and c.shipper_id = #shipperId#))
        </isNotEmpty>
        <isNotEmpty property="sids">
            and w.sid in <iterate property="sids" open="(" conjunction="," close=")"> #sids[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="tradeBindStatus">
            <isEqual property="tradeBindStatus" compareValue="0"> and w.sid = 0</isEqual>
            <isEqual property="tradeBindStatus" compareValue="1"> and w.sid > 0</isEqual>
        </isNotEmpty>
        <isNotEmpty property="shortIds">
            and t.short_id in <iterate property="shortIds" open="(" conjunction="," close=")"> #shortIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="takenOrderStatus">
            <isEqual property="takenOrderStatus" compareValue="0">
                and not exists (select 1 from unique_code_relation_#dbNo# ucr
                where ucr.company_id = w.company_id and ucr.unique_code = w.unique_code and ucr.enable_status = 1 and ucr.business_type = 22 and ucr.status != 20 )
            </isEqual>
            <isEqual property="takenOrderStatus" compareValue="1">
                and exists (select 1 from unique_code_relation_#dbNo# ucr
                where ucr.company_id = w.company_id and ucr.unique_code = w.unique_code and ucr.enable_status = 1 and ucr.business_type = 22 and ucr.status != 20 )
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="dateNo">
            and w.`date_no` = #dateNo#
        </isNotEmpty>
        <isNotEmpty property="batchNo">
            and w.`batch_no` = #batchNo#
        </isNotEmpty>
        <isNotEmpty property="itemBatchNo">
            and w.`item_batch_no` = #itemBatchNo#
        </isNotEmpty>
        <isNotNull property="updateStart"> <![CDATA[ and w.modified >= #updateStart# ]]> </isNotNull>
        <isNotNull property="updateEnd"> <![CDATA[ and w.modified <= #updateEnd# ]]> </isNotNull>
        <isNotNull property="consignStart"> <![CDATA[ and t.consign_time >= #consignStart# ]]> </isNotNull>
        <isNotNull property="consignEnd"> <![CDATA[ and t.consign_time <= #consignEnd# ]]> </isNotNull>
        <isNotEmpty property="exportLastMaxId"> <![CDATA[ and w.id < #exportLastMaxId# ]]> </isNotEmpty>
    </sql>

    <select id="getOuterIdByUniqueCode" resultClass="String">
        select ifnull(c.outer_id,b.outer_id) outerId
        from wave_unique_code_#dbNo# w
        <include refid="joinItemSku"/>
        where w.company_id = #companyId# and w.enable_status = 1
        and w.type in (1, 2)
        and w.unique_code = #uniqueCode#
    </select>

    <sql id="joinExtend4Item">
        <isEqual property="needExtendMsg" compareValue="true">
            <include refid="joinExtendSQL"/>
        </isEqual>
    </sql>

    <select id="queryItemUniqueCodeByCondition" resultMap="ItemUniqueCodeMap" timeout="30">
        /*FORCE_MASTER*/ select <include refid="itemUniqueCodeColumn"/>
        <include refid="sourceData"/>
        <isEqual property="forceOrderIdIndex" compareValue="true">
            <isEqual property="queryMigrateData" compareValue="false"> force index(idx_comp_order_id) </isEqual>
        </isEqual>
        <include refid="joinExtend4Item"/>
        <include refid="joinItemSku"/>
        left join trade_#tradeDbNo# t on w.sid = t.sid
        <isNotEmpty property="tradeWaveIds">
            and t.wave_id != 0
        </isNotEmpty>
        where w.company_id = #companyId#
        <include refid="itemUniqueCodeQuerySql"/>
        <isEmpty property="sortField">
            order by w.id desc
            <isNotEmpty property="tradeBindStatus">
                , w.created desc
            </isNotEmpty>
        </isEmpty>
        <isNotEmpty property="sortField">
            order by
            <isEqual property="sortField" compareValue="skuOuterId">
                (c.outer_id is null), c.outer_id
                <isEqual property="sortOrder" compareValue="0"> asc </isEqual>
                <isEqual property="sortOrder" compareValue="1"> desc </isEqual>
            </isEqual>
            <isEqual property="sortField" compareValue="outerId"> w.sys_item_id, w.sys_sku_id </isEqual>
            <isEqual property="sortField" compareValue="payTime">
                (t.pay_time is null or t.pay_time = '2000-01-01 00:00:00'), ifnull(t.pay_time, '2000-01-01 00:00:00')
                <isEqual property="sortOrder" compareValue="0"> asc </isEqual>
                <isEqual property="sortOrder" compareValue="1"> desc </isEqual>
                , w.created desc
            </isEqual>
        </isNotEmpty>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="queryOnlyUniqueCodeByCondition" resultMap="UniqueCodeTagIdsMap">
        select w.id, w.unique_code, w.tag_ids, w.sys_item_id, w.sys_sku_id, w.supplier_category, w.supplier_name, w.supplier_id
        from wave_unique_code_#dbNo# w
        where w.company_id = #companyId# and w.enable_status = 1
        <include refid="orderUniqueCodeQuerySql"/>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="queryCodeByCondition" resultClass="WaveUniqueCode">
        /*FORCE_MASTER*/ select unique_code uniqueCode
        from wave_unique_code_#dbNo# w
        where w.company_id = #companyId#
        <include refid="itemUniqueCodeQuerySql"/>
    </select>

    <select id="queryWaveUniqueCodeBySids" resultMap="WaveUniqueCodeMap">
        /*FORCE_MASTER*/ select * from wave_unique_code_#dbNo# w
        where w.company_id = #companyId#
        and type = 0
        and w.sid in <iterate property="sids" open="(" conjunction="," close=")"> #sids[]# </iterate>
    </select>

    <select id="countItemUniqueCode" resultClass="long">
        select count(*) from wave_unique_code_#dbNo# w
        <isEqual property="needJoinItemInfo" compareValue="true">
            <include refid="joinItemSku"/>
        </isEqual>
        <isEqual property="needJoinTradeInfo" compareValue="true">
            left join trade_#tradeDbNo# t on w.sid = t.sid
            <isNotEmpty property="tradeWaveIds">
                and t.wave_id != 0
            </isNotEmpty>
        </isEqual>
        where w.company_id = #companyId#
        <include refid="itemUniqueCodeQuerySql"/>
    </select>

    <update id="updateItemUniqueCodeCustom">
        update wave_unique_code_#dbNo#
        set cost_price = #costPrice#,selling_price = #sellingPrice#,weight = #weight#
        <isNotEmpty prepend="," property="productionDate"> production_date = #productionDate# </isNotEmpty>
        <isNotEmpty prepend="," property="uniqueCode"> unique_code = #uniqueCode# </isNotEmpty>
        <isNotEmpty prepend="," property="sysItemId"> sys_item_id = #sysItemId# </isNotEmpty>
        <isNotEmpty prepend="," property="sysSkuId"> sys_sku_id = #sysSkuId# </isNotEmpty>
        <isNotEmpty prepend="," property="outerId"> outer_id = #outerId# </isNotEmpty>
        <isNotEmpty prepend="," property="supplierId"> supplier_id = #supplierId# </isNotEmpty>
        <isNotEmpty prepend="," property="supplierName"> supplier_name = #supplierName# </isNotEmpty>
        <isNotEmpty prepend="," property="itemBatchNo"> item_batch_no = #itemBatchNo# </isNotEmpty>
        <isNotEmpty prepend="," property="supplierCategory"> supplier_category = #supplierCategory# </isNotEmpty>
        <isNotEmpty prepend="," property="enableStatus"> enable_status = #enableStatus# </isNotEmpty>
        <isNotEmpty prepend="," property="status"> status = #status# </isNotEmpty>
        <isNotEmpty prepend="," property="receiveStatus"> receive_status = #receiveStatus# </isNotEmpty>
        where id = #id#
    </update>

    <sql id="itemUniqueCodeListSql">
        where w.company_id = #companyId# and w.enable_status = 1
        <isNull property="type">
        and w.type in (1, 2)
        </isNull>
        <isNotNull property="type">
            and w.type = #type#
        </isNotNull>
        <isNotNull property="waveId">
            and w.wave_id = #waveId#
        </isNotNull>
        <isNotEmpty property="uniqueCodes">
            and w.unique_code in <iterate property="uniqueCodes" open="(" conjunction="," close=")"> #uniqueCodes[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="sids">
            and w.sid in <iterate property="sids" open="(" conjunction="," close=")"> #sids[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="afterSaleOrderCodes">
            and exists (select 1 from unique_code_relation_#dbNo# ucr
            where ucr.unique_code_id = w.id and ucr.enable_status = 1 and ucr.business_type = 2
            and ucr.business_code in <iterate property="afterSaleOrderCodes" open="(" conjunction="," close=")"> #afterSaleOrderCodes[]# </iterate>)
        </isNotEmpty>
        <isNotEmpty property="shortIds">
            and t.short_id in <iterate property="shortIds" open="(" conjunction="," close=")"> #shortIds[]# </iterate>
        </isNotEmpty>
        <isNotNull property="updateStart"> <![CDATA[ and w.modified >= #updateStart# ]]> </isNotNull>
        <isNotNull property="updateEnd"> <![CDATA[ and w.modified <= #updateEnd# ]]> </isNotNull>
        <isNotNull property="consignStart"> <![CDATA[ and t.consign_time >= #consignStart# ]]> </isNotNull>
        <isNotNull property="consignEnd"> <![CDATA[ and t.consign_time <= #consignEnd# ]]> </isNotNull>
        <isNotNull property="codeType"> and w.code_type = #codeType# </isNotNull>
        <isNotNull property="createStart"> <![CDATA[ and w.created >= #createStart# ]]> </isNotNull>
        <isNotNull property="createEnd"> <![CDATA[ and w.created <= #createEnd# ]]> </isNotNull>
    </sql>

    <sql id="forceIndexForOpen">
        <isNull property="ignoreIndex">
            <isNotNull property="createStart">
                force index(company_created_index)
            </isNotNull>
            <isNull property="createStart">
                force index(company_modified_index)
            </isNull>
        </isNull>
    </sql>

    <select id="queryItemUniqueCodeList" resultMap="ItemUniqueCodeMap">
        select
        <include refid="itemUniqueCodeColumn"/>
        from wave_unique_code_#dbNo# w
        <include refid="forceIndexForOpen"/>
        <include refid="joinExtend4Item"/>
        <include refid="joinItemSku"/>
        left join trade_#tradeDbNo# t on w.sid = t.sid
        <include refid="itemUniqueCodeListSql"/>
        <isNotNull property="createStart">
            order by w.created desc
        </isNotNull>
        <isNull property="createStart">
            order by w.modified desc
        </isNull>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="queryItemUniqueCodeCount" resultClass="long">
        select count(*)
        from wave_unique_code_#dbNo# w
        left join trade_#tradeDbNo# t on w.sid = t.sid
        <include refid="itemUniqueCodeListSql"/>
    </select>

    <select id="queryUnBindErrorCodes" resultClass="WaveUniqueCode">
        SELECT w.id id FROM wave_unique_code_#dbNo# w
        LEFT JOIN order_#orderDbNo# o ON w.company_id = o.company_id AND w.order_id = o.id
        WHERE w.company_id = #companyId#
        <isNotEmpty property="uniqueCodes">
            AND w.unique_code in <iterate property="uniqueCodes" open="(" conjunction="," close=")"> #uniqueCodes[]# </iterate>
        </isNotEmpty>
        AND w.type = 1
        AND w.status IN (1, 2, 3, 4, 5)
        AND w.enable_status = 1
        AND o.sys_status != 'FINISHED_AUDIT'
        AND w.order_id > 0
    </select>

    <select id="queryNeedUnboundUniqueCodes" resultClass="String">
        SELECT unique_code FROM wave_unique_code_#orderDbNo# w
        LEFT JOIN order_#orderDbNo# o ON w.company_id = o.company_id AND w.order_id = o.id and o.enable_status > 0
        WHERE w.company_id = #companyId#
        AND w.type = 1 AND w.status IN (1, 2, 3, 4, 5)
        AND w.enable_status = 1
        AND (o.sys_status != 'FINISHED_AUDIT' or o.id is null)
        AND w.order_id > 0
        <isNotEmpty property="uniqueCodes">
            and w.unique_code in <iterate property="uniqueCodes" open="(" conjunction="," close=")"> #uniqueCodes[]# </iterate>
        </isNotEmpty>
    </select>

    <sql id="groupSummarySql">
        <isNotEmpty property="statusList">
            AND w.status in <iterate property="statusList" open="(" conjunction="," close=")"> #statusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="warehouseId">
            AND w.warehouse_id = #warehouseId#
        </isNotEmpty>
        <isNotEmpty property="supplierId">
            and w.supplier_id = #supplierId#
        </isNotEmpty>
        <isNotEmpty property="supplierName">
            and w.supplier_name like concat('%', #supplierName#, '%')
        </isNotEmpty>
        <isNotEmpty property="supplierIds">
            and w.supplier_id in <iterate property="supplierIds" open="(" conjunction="," close=")"> #supplierIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="privilegesSupplierIds">
            and w.supplier_id in <iterate property="privilegesSupplierIds" open="(" conjunction="," close=")"> #privilegesSupplierIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="outerId">
            AND w.outer_id like concat('%', #outerId#, '%')
        </isNotEmpty>
        <isNotEmpty property="sysItemId">
            and w.sys_item_id = #sysItemId#
        </isNotEmpty>
        <isNotEmpty property="sysSkuId">
            and w.sys_sku_id = #sysSkuId#
        </isNotEmpty>
        <isNotEmpty property="itemOuterId">
            AND b.outer_id like concat('%', #itemOuterId#, '%')
        </isNotEmpty>
        <isNotEmpty property="skuOuterId">
            AND c.outer_id like concat('%', #skuOuterId#, '%')
        </isNotEmpty>
        <isNotEmpty property="title">
            AND b.title like concat('%', #title#, '%')
        </isNotEmpty>
        <isNotEmpty property="shortTitle">
            AND b.short_title like concat('%', #shortTitle#, '%')
        </isNotEmpty>
        <isNotEmpty property="skuRemark">
            AND c.remark like concat('%', #skuRemark#, '%')
        </isNotEmpty>
        <isNotEmpty property="sellerFlags">
            and w.sid > 0 and w.order_id > 0
            and (t.seller_flag
            <isEqual property="filterFlag" compareValue="0">
                not
            </isEqual>
            in <iterate property="sellerFlags" open="(" conjunction="," close=")"> #sellerFlags[]# </iterate>
            <isEqual property="containEmptySellerFlag" compareValue="true">
                <isEqual property="filterFlag" compareValue="0">
                    and t.seller_flag is not null
                </isEqual>
                <isEqual property="filterFlag" compareValue="1">
                    or t.seller_flag is null
                </isEqual>
            </isEqual>
            )
        </isNotEmpty>
    </sql>

    <sql id="sortType">
        <isNotEmpty property="sortType">
            order by
            <isEqual property="sortType" compareValue="1"> outerId </isEqual>
            <isEqual property="sortType" compareValue="2"> outerId desc</isEqual>
            <isEqual property="sortType" compareValue="3"> purchaseNum </isEqual>
            <isEqual property="sortType" compareValue="4"> purchaseNum desc </isEqual>
            <isEqual property="sortType" compareValue="5"> supplierName</isEqual>
            <isEqual property="sortType" compareValue="6"> supplierName desc </isEqual>
        </isNotEmpty>
        <isEqual property="source" compareValue="0"> order by CONVERT(w.supplier_category USING GBK), CONVERT(w.supplier_name USING GBK), CONVERT(b.outer_id USING GBK), CONVERT(c.outer_id USING GBK), w.id </isEqual>
    </sql>

    <select id="groupSummaryCount" resultClass="long">
        SELECT count(*)
        FROM wave_unique_code_#dbNo# w
        LEFT JOIN dmj_item_#itemDbNo# b ON w.company_id = b.company_id AND w.sys_item_id = b.sys_item_id AND b.enable_status = 1
        LEFT JOIN dmj_sku_#skuDbNo# c ON w.company_id = c.company_id AND w.sys_item_id = c.sys_item_id AND w.sys_sku_id = c.sys_sku_id AND c.enable_status = 1
        WHERE w.company_id = #companyId#
        AND w.type = 1
        AND w.enable_status = 1
        AND w.order_id > 0
        <include refid="groupSummarySql"/>
    </select>

    <select id="groupSummaryCountBySku" resultClass="com.raycloud.dmj.domain.wave.vo.UniqueCodeGroupSummaryVO">
        select temp.supplier_id supplierId, temp.sys_item_id sysItemId, count(*) purchaseNum
        from (SELECT w.supplier_category, w.supplier_id, w.sys_item_id, w.sys_sku_id, count(1) skuPurchaseNum
        FROM wave_unique_code_#dbNo# w
        <isNotEmpty property="sellerFlags">
            left join trade_#tradeDbNo# t on w.sid = t.sid and w.company_id = t.company_id
        </isNotEmpty>
        LEFT JOIN dmj_item_#itemDbNo# b ON w.company_id = b.company_id AND w.sys_item_id = b.sys_item_id AND b.enable_status = 1
        LEFT JOIN dmj_sku_#skuDbNo# c ON w.company_id = c.company_id AND w.sys_item_id = c.sys_item_id AND w.sys_sku_id = c.sys_sku_id AND c.enable_status = 1
        WHERE w.company_id = #companyId#
        AND w.type = 1
        AND w.enable_status = 1
        AND w.order_id > 0
        <include refid="groupSummarySql"/>
        group by w.supplier_category, w.supplier_id, w.sys_item_id, if(w.sys_sku_id >= 0, w.sys_sku_id, 0)
        <include refid="sortType"/>) temp
        group by temp.supplier_category, temp.supplier_id, temp.sys_item_id
        <isNotEmpty property="purchaseNumStart">
            having
            <![CDATA[ sum(temp.skuPurchaseNum) >= #purchaseNumStart# ]]>
        </isNotEmpty>
        <isNotEmpty property="purchaseNumStart">
            <isNotEmpty property="purchaseNumEnd">
                and
            </isNotEmpty>
        </isNotEmpty>
        <isNotEmpty property="purchaseNumEnd">
            <isEmpty property="purchaseNumStart">
                having
            </isEmpty>
            <![CDATA[ sum(temp.skuPurchaseNum) <= #purchaseNumEnd# ]]>
        </isNotEmpty>
    </select>

    <select id="queryListByPurchase" resultMap="WaveUniqueCodeMap">
        SELECT w.*
        FROM wave_unique_code_#dbNo# w
        <isNotEmpty property="sellerFlags">
            left join trade_#tradeDbNo# t on w.sid = t.sid
        </isNotEmpty>
        LEFT JOIN dmj_item_#itemDbNo# b ON w.company_id = b.company_id AND w.sys_item_id = b.sys_item_id AND b.enable_status = 1
        LEFT JOIN dmj_sku_#skuDbNo# c ON w.company_id = c.company_id AND w.sys_item_id = c.sys_item_id AND w.sys_sku_id = c.sys_sku_id AND c.enable_status = 1
        WHERE w.company_id = #companyId#
        AND w.type = 1
        AND w.enable_status = 1
        AND w.order_id > 0
        <include refid="groupSummarySql"/>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="countByPurchaseUniqueCode" resultClass="long">
        SELECT count(*)
        FROM wave_unique_code_#dbNo# w
        <isNotEmpty property="sellerFlags">
            left join trade_#tradeDbNo# t on w.sid = t.sid
        </isNotEmpty>
        LEFT JOIN dmj_item_#itemDbNo# b ON w.company_id = b.company_id AND w.sys_item_id = b.sys_item_id AND b.enable_status = 1
        LEFT JOIN dmj_sku_#skuDbNo# c ON w.company_id = c.company_id AND w.sys_item_id = c.sys_item_id AND w.sys_sku_id = c.sys_sku_id AND c.enable_status = 1
        WHERE w.company_id = #companyId#
        AND w.type = 1
        AND w.enable_status = 1
        AND w.order_id > 0
        <include refid="groupSummarySql"/>
    </select>

    <select id="groupSummaryUniqueCode" resultMap="GroupSummaryUniqueCodeMap">
        SELECT
        <isEqual property="source" compareValue="0"> w.supplier_category as supplierCategory, t.pay_time as payTime, w.sid as sid, if(o.sku_id is null or o.sku_id = '', o.num_iid, o.sku_id) as platformId, </isEqual>
        <isEqual property="source" compareValue="1"> null as supplierCategory, null as payTime, null as sid, null as platformId, </isEqual>
        w.warehouse_id as warehouseId, w.supplier_id as supplierId, w.supplier_name as supplierName, w.sys_item_id as sysItemId, w.sys_sku_id as sysSkuId, w.outer_id as outerId,
        b.outer_id as itemOuterId, b.short_title as shortTitle, b.title as mainTitle, b.pic_path as picPath,
        c.outer_id as skuOuterId, c.remark as skuRemark, c.title as skuTitle, c.properties_name as propertiesName, c.pic_path as skuPicPath,
        count(*) as purchaseNum, if(c.purchase_price is null or c.purchase_price = 0, b.purchase_price, c.purchase_price) as price
        FROM wave_unique_code_#dbNo# w
        LEFT JOIN dmj_item_#itemDbNo# b ON w.company_id = b.company_id AND w.sys_item_id = b.sys_item_id AND b.enable_status = 1
        LEFT JOIN dmj_sku_#skuDbNo# c ON w.company_id = c.company_id AND w.sys_item_id = c.sys_item_id AND w.sys_sku_id = c.sys_sku_id AND c.enable_status = 1
        <isEqual property="source" compareValue="0">
            LEFT JOIN trade_#tradeDbNo# t on w.sid = t.sid and w.company_id = t.company_id
            LEFT JOIN order_#dbNo# o on w.order_id = o.id
        </isEqual>
        WHERE w.company_id = #companyId#
        AND w.type = 1
        AND w.enable_status = 1
        AND w.order_id > 0
        <isNotEmpty property="supplierIdAndSysItemIds">
            and concat(w.supplier_id, '_', w.sys_item_id) in <iterate property="supplierIdAndSysItemIds" open="(" conjunction="," close=")"> #supplierIdAndSysItemIds[]# </iterate>
        </isNotEmpty>
        <include refid="groupSummarySql"/>
        group by <isEqual property="source" compareValue="0"> w.supplier_category, </isEqual> w.supplier_id, w.sys_item_id, if(w.sys_sku_id >= 0, w.sys_sku_id, 0)
        <include refid="sortType"/>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="countUniqueCodeWithSupplier" resultClass="long">
        SELECT count(*) from (select w.supplier_id FROM wave_unique_code_#dbNo# w
        force index(company_type_status_index)
        WHERE w.company_id = #companyId#
        AND w.type = 1
        AND w.enable_status = 1
        AND w.order_id > 0
        <isNotEmpty property="statusList">
            AND w.status in <iterate property="statusList" open="(" conjunction="," close=")"> #statusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="warehouseId">
            AND w.warehouse_id = #warehouseId#
        </isNotEmpty>
        <isNotEmpty property="supplierIds">
            and w.supplier_id in <iterate property="supplierIds" open="(" conjunction="," close=")"> #supplierIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="supplierId">
            and w.supplier_id = #supplierId#
        </isNotEmpty>
        <isNotEmpty property="supplierName">
            and w.supplier_name like concat('%', #supplierName#, '%')
        </isNotEmpty>
        <isNotEmpty property="outerId">
            AND w.outer_id like concat('%', #outerId#, '%')
        </isNotEmpty>
        group by w.supplier_id) temp
    </select>

    <select id="groupSummaryUniqueCodeWithSupplier" resultClass="com.raycloud.dmj.domain.wave.vo.UniqueCodeGroupSummaryVO">
        SELECT w.supplier_id supplierId, w.supplier_name supplierName, count(*) purchaseNum, w.outer_id outerId
        FROM wave_unique_code_#dbNo# w force index(company_type_status_index)
        WHERE w.company_id = #companyId#
        AND w.type = 1
        AND w.enable_status = 1
        AND w.order_id > 0
        <isNotEmpty property="statusList">
            AND w.status in <iterate property="statusList" open="(" conjunction="," close=")"> #statusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="warehouseId">
            AND w.warehouse_id = #warehouseId#
        </isNotEmpty>
        <isNotEmpty property="supplierId">
            and w.supplier_id = #supplierId#
        </isNotEmpty>
        <isNotEmpty property="supplierIds">
            and w.supplier_id in <iterate property="supplierIds" open="(" conjunction="," close=")"> #supplierIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="supplierName">
            and w.supplier_name like concat('%', #supplierName#, '%')
        </isNotEmpty>
        <isNotEmpty property="outerId">
            AND w.outer_id like concat('%', #outerId#, '%')
        </isNotEmpty>
        group by w.supplier_id
        <include refid="sortType"/>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="queryUsePositionOrderUniqueCode" resultMap="WaveUniqueCodeForOpenMap">
        select * from wave_unique_code_#dbNo#
        force index(company_type_status_index)
        where company_id = #companyId#
        and type = 1
        and code_type in (1,2)
        and status not in (6, 7, 9)
        and enable_status = 1
        and order_id > 0
        and position_no_id in
        <iterate property="positionNoIds" open="(" conjunction="," close=")"> #positionNoIds[]# </iterate>
        <isNotEmpty property="created">
            and (<![CDATA[ created >= #created# ]]> or new_split = 1)
        </isNotEmpty>
    </select>

    <select id="queryUsePositionNoIdList" resultClass="long">
        select distinct position_no_id from wave_unique_code_#dbNo#
        force index(company_type_status_index)
        where company_id = #companyId#
        and type = 1
        and code_type in (1,2)
        and status not in (6, 7, 9)
        and position_no_id > 0
        and enable_status = 1
        and order_id > 0
        <isNotEmpty property="created">
            and (<![CDATA[ created >= #created# ]]> or new_split = 1)
        </isNotEmpty>
    </select>

    <select id="queryHasUniqueCodesWaveCount" resultClass="Integer">
        select count(distinct wave_id) from wave_unique_code_#dbNo#
        where company_id = #companyId# and enable_status = 1 and type = 0
        and wave_id in <iterate property="waveIds" open="(" conjunction="," close=")">#waveIds[]#</iterate>
    </select>

    <select id="getUniqueCodeSupplierIds" resultClass="long">
        select distinct w.supplier_id FROM wave_unique_code_#dbNo# w
        WHERE w.company_id = #companyId#
        AND w.type = 1
        AND w.enable_status = 1
        AND w.order_id > 0
        <isNotEmpty property="statusList">
            AND w.status in <iterate property="statusList" open="(" conjunction="," close=")"> #statusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="warehouseId">
            AND w.warehouse_id = #warehouseId#
        </isNotEmpty>
        <isNotEmpty property="supplierIds">
            and w.supplier_id in <iterate property="supplierIds" open="(" conjunction="," close=")"> #supplierIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="supplierId">
            and w.supplier_id = #supplierId#
        </isNotEmpty>
        <isNotEmpty property="supplierName">
            and w.supplier_name like concat('%', #supplierName#, '%')
        </isNotEmpty>
        <isNotEmpty property="outerId">
            AND w.outer_id like concat('%', #outerId#, '%')
        </isNotEmpty>
    </select>


    <!-- 到货清点 -->
    <resultMap id="arrivedCheckSummaryMap" class="UniqueCodeArrivedCheckSummary">
        <result property="mainTitle" column="main_title"/>
        <result property="skuTitle" column="sku_title"/>
        <result property="picPath" column="pic_path"/>
        <result property="skuPicPath" column="sku_pic_path"/>
        <result property="propertiesName" column="properties_name"/>
        <result property="purchaseTotalNum" column="purchase_total_num"/>
        <result property="arrivedNum" column="arrived_num"/>
        <result property="printedNotArrivedNum" column="printed_not_arrived_num"/>
        <result property="cancelNum" column="cancel_num"/>
        <result property="canArrivedNum" column="can_arrived_num"/>
        <result property="uniqueCodeId" column="unique_code_id"/>
        <result property="uniqueCode" column="unique_code"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="sid" column="sid"/>
        <result property="codeType" column="code_type"/>
        <result property="expressPrintTime" column="express_print_time"/>
        <result property="isExcep" column="is_excep"/>
        <result property="sysItemId" column="sys_item_id"/>
        <result property="sysSkuId" column="sys_sku_id"/>
        <result property="itemOuterId" column="item_outer_id"/>
        <result property="skuOuterId" column="sku_outer_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="purchaseOrderCodes" column="purchase_order_codes"/>
        <result property="printedNum" column="printed_num"/>
        <result property="hotSaleCode" column="hot_sale_code"/>
    </resultMap>

    <sql id="arrivedCheckSummarySql">
        <isNotEmpty property="warehouseId">
            AND w.warehouse_id = #warehouseId#
        </isNotEmpty>
        <isNotEmpty property="codeTypeList">
            and w.code_type in <iterate property="codeTypeList" open="(" conjunction="," close=")"> #codeTypeList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="itemOuterIdList">
            and b.outer_id in <iterate property="itemOuterIdList" open="(" conjunction="," close=")"> #itemOuterIdList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="skuOuterIdList">
            and c.outer_id in <iterate property="skuOuterIdList" open="(" conjunction="," close=")"> #skuOuterIdList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="itemOuterId">
            AND b.outer_id like concat('%', #itemOuterId#, '%')
        </isNotEmpty>
        <isNotEmpty property="skuOuterId">
            AND c.outer_id like concat('%', #skuOuterId#, '%')
        </isNotEmpty>
        <isNotEmpty property="sysItemId">
            AND b.sys_item_id = #sysItemId#
        </isNotEmpty>
        <isNotEmpty property="sysSkuId">
            AND c.sys_sku_id = #sysSkuId#
        </isNotEmpty>
        <isNotEmpty property="title">
            AND b.title like concat('%', #title#, '%')
        </isNotEmpty>
        <isNotEmpty property="propertiesName">
            AND c.properties_name like concat('%', #propertiesName#, '%')
        </isNotEmpty>
        <isNotEmpty property="supplierIds">
            and w.supplier_id in <iterate property="supplierIds" open="(" conjunction="," close=")"> #supplierIds[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="supplierCategoryNames">
            and w.supplier_category in <iterate property="supplierCategoryNames" open="(" conjunction="," close=")"> #supplierCategoryNames[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="createBegin"> <![CDATA[ and w.created >= #createBegin# ]]> </isNotEmpty>
        <isNotEmpty property="createEnd"> <![CDATA[ and w.created <= #createEnd# ]]> </isNotEmpty>
        <isNotEmpty property="statusList">
            and w.status in <iterate property="statusList" open="(" conjunction="," close=")"> #statusList[]# </iterate>
        </isNotEmpty>
        <isNotEmpty property="printed">
            <isEqual property="printed" compareValue="0"> and w.print_num = 0 </isEqual>
            <isGreaterThan property="printed" compareValue="0"> and w.print_num > 0 </isGreaterThan>
        </isNotEmpty>
    </sql>

    <sql id="arrivedCheckSummaryFieldSql">
        <isNotEmpty property="queryType">
            <isEqual property="queryType" compareValue="1">
                b.title as main_title, c.title as sku_title, c.properties_name,
                b.pic_path, c.pic_path sku_pic_path,
                count(*) as purchase_total_num,
                sum( CASE WHEN w.status in (5,6,14,15) THEN 1 ELSE 0 END ) as arrived_num,
                sum( CASE WHEN w.status = 4 and w.print_num > 0 THEN 1 ELSE 0 END ) as printed_not_arrived_num,
                sum( CASE WHEN w.status = 7 THEN 1 ELSE 0 END ) as cancel_num,
                sum( CASE WHEN w.status = 1 and w.print_num = 0 THEN 1 ELSE 0 END ) as can_arrived_num,
                0 as unique_code_id, 0 as sid, 0 as code_type, null as hot_sale_code,
                '2000-01-01 00:00:00' as express_print_time, 0 as is_excep,
                <isEqual property="fillReceiveNumType" compareValue="1">
                    GROUP_CONCAT(distinct if(w.business_code is not null and w.business_code != '', w.business_code, null)) as purchase_order_codes,
                    null as printed_num,
                </isEqual>
                <isEqual property="fillReceiveNumType" compareValue="2">
                    GROUP_CONCAT( distinct if( w.business_code is not null and w.business_code != '', w.business_code, null ) ) as purchase_order_codes,
                    sum( CASE WHEN w.print_num > 0 THEN 1 ELSE 0 END ) as printed_num,
                </isEqual>
                <isEmpty property="fillReceiveNumType">
                    null as purchase_order_codes,
                    null as printed_num,
                </isEmpty>
            </isEqual>
            <isEqual property="queryType" compareValue="2">
                w.id as unique_code_id, w.sid, w.code_type, w.hot_sale_code,
                null as pic_path, null as sku_pic_path,
                t.express_print_time, t.is_excep,
                '' as main_title, '' as sku_title, '' as properties_name,
                0 as purchase_total_num, 0 as arrived_num,
                0 as printed_not_arrived_num, 0 as cancel_num, 0 as can_arrived_num,
                null as purchase_order_codes,
                null as printed_num,
            </isEqual>
        </isNotEmpty>
        b.sys_item_id, c.sys_sku_id,
        b.outer_id as item_outer_id, c.outer_id as sku_outer_id,
        w.supplier_id, w.supplier_name, w.unique_code
    </sql>

    <sql id="arrivedCheckSummaryCommonSql">
        FROM wave_unique_code_#dbNo# w
        LEFT JOIN dmj_item_#itemDbNo# b ON w.company_id = b.company_id AND w.sys_item_id = b.sys_item_id AND b.enable_status = 1
        LEFT JOIN dmj_sku_#skuDbNo# c ON w.company_id = c.company_id AND w.sys_item_id = c.sys_item_id AND w.sys_sku_id = c.sys_sku_id AND c.enable_status = 1
        <isEqual property="queryType" compareValue="2">
            left join trade_#tradeDbNo# t on w.sid = t.sid
        </isEqual>
        WHERE w.company_id = #companyId#
        AND w.type IN (1,2)
        AND w.enable_status = 1
        AND w.stock_status = 0
        <include refid="arrivedCheckSummarySql"/>
        <isEqual property="queryType" compareValue="1">
            group by w.supplier_id, w.sys_item_id, if(w.sys_sku_id >= 0, w.sys_sku_id, 0)
        </isEqual>
    </sql>

    <sql id="arrivedCheckCompleteOrder">
        (select count(*) from wave_unique_code_#dbNo# w1
        where w1.company_id = #companyId# and w1.enable_status = 1 and w1.status not in (3,5) and w1.sid = w.sid group by w1.sid)
    </sql>

    <sql id="arrivedCheckMatchTradeOrderSortSql">
        <isEqual property="priorityUrgent" compareValue="1">
            t.is_urgent desc,
        </isEqual>
        <isEqual property="arrivedCheckMatchTradeOrder" compareValue="1">
            t.pay_time
        </isEqual>
        <isEqual property="arrivedCheckMatchTradeOrder" compareValue="2">
            w.code_type
        </isEqual>
        <isEqual property="arrivedCheckMatchTradeOrder" compareValue="3">
            w.code_type desc
        </isEqual>
        <isEqual property="arrivedCheckMatchTradeOrder" compareValue="4">
            <include refid="arrivedCheckCompleteOrder"/>, t.pay_time
        </isEqual>
        <isEqual property="arrivedCheckMatchTradeOrder" compareValue="5">
            <include refid="arrivedCheckCompleteOrder"/>, w.code_type
        </isEqual>
        <isEqual property="arrivedCheckMatchTradeOrder" compareValue="6">
            <include refid="arrivedCheckCompleteOrder"/>, w.code_type desc
        </isEqual>
    </sql>

    <sql id="arrivedCheckSummarySortSql">
        <isNotEmpty property="queryType">
            <isEqual property="queryType" compareValue="1">
                <isEqual property="sortType" compareValue="1">
                    w.supplier_id = 0, CONVERT(w.supplier_name USING GBK), c.outer_id IS NULL, CONVERT(c.outer_id USING GBK), CONVERT(b.outer_id USING GBK)
                </isEqual>
                <isEqual property="sortType" compareValue="2">
                    w.supplier_id = 0, CONVERT(w.supplier_name USING GBK), CONVERT(b.outer_id USING GBK),
                    c.prop_color IS NULL,
                    <isNotEmpty property="sortDetails1">
                        FIELD(c.prop_color,
                        <iterate property="sortDetails1" conjunction=",">
                            #sortDetails1[]#
                        </iterate>
                        ) desc,
                    </isNotEmpty>
                    CONVERT(c.prop_color USING GBK),
                    c.prop_other = '',
                    <isNotEmpty property="sortDetails2">
                        FIELD(c.prop_other,
                        <iterate property="sortDetails2" conjunction=",">
                            #sortDetails2[]#
                        </iterate>
                        ) desc,
                    </isNotEmpty>
                    CONVERT(c.prop_other USING GBK)
                </isEqual>
            </isEqual>
            <isEqual property="queryType" compareValue="2">
                <include refid="arrivedCheckMatchTradeOrderSortSql"/>
            </isEqual>
        </isNotEmpty>
        <isEmpty property="queryType">
            count(*) desc, w.id
        </isEmpty>
    </sql>

    <select id="arrivedCheckSummary" resultMap="arrivedCheckSummaryMap">
        SELECT
        <include refid="arrivedCheckSummaryFieldSql"/>
        <include refid="arrivedCheckSummaryCommonSql"/>
        order by
        <include refid="arrivedCheckSummarySortSql"/>
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize#
        </isNotEmpty>
    </select>

    <select id="countArrivedCheckSummary" resultClass="long">
        SELECT count(*) from ( SELECT count(*) <include refid="arrivedCheckSummaryCommonSql"/> ) temp
    </select>

    <select id="matchOneByOuterId4FastConsign" resultMap="ItemUniqueCodeMap">
        /*FORCE_MASTER*/ select <include refid="itemUniqueCodeColumn"/>
        <include refid="sourceData"/>
        <include refid="joinExtend4Item"/>
        <include refid="joinItemSku"/>
        left join trade_#tradeDbNo# t on w.sid = t.sid
        <isNotEmpty property="tradeWaveIds">
            and t.wave_id != 0
        </isNotEmpty>
        where w.company_id = #companyId#
        and w.matched_status != 2
        and w.order_id > 0
        and t.sys_status = 'FINISHED_AUDIT'
        and t.express_print_time &lt;= '2000-01-01 00:00:00'
        and (t.is_excep = 0 OR (t.is_excep = 1 AND w.matched_status != 4))
        <include refid="itemUniqueCodeQuerySql"/>
        order by
            t.is_excep,
        <isEqual property="sortField" compareValue="payTime">
            (t.pay_time is null or t.pay_time = '2000-01-01 00:00:00'), ifnull(t.pay_time, '2000-01-01 00:00:00'),
        </isEqual>
        <isEqual property="sortField" compareValue="timeOutActionTime">
            (t.timeout_action_time is null or t.timeout_action_time = '2000-01-01 00:00:00'), ifnull(t.timeout_action_time, '2000-01-01 00:00:00'),
        </isEqual>
            t.sid
        <isNotNull property="scanNum">
            limit #scanNum#
        </isNotNull>
        <isNull property="scanNum">
            limit 1
        </isNull>
    </select>

    <select id="matchBatchByOuterId4FastConsign" resultMap="ItemUniqueCodeMap">
        /*FORCE_MASTER*/ select <include refid="itemUniqueCodeColumn"/>
        <include refid="sourceData"/>
        <include refid="joinExtend4Item"/>
        <include refid="joinItemSku"/>
        left join trade_#tradeDbNo# t on w.sid = t.sid
        <isNotEmpty property="tradeWaveIds">
            and t.wave_id != 0
        </isNotEmpty>
        where w.company_id = #companyId#
        and w.matched_status != 2
        and w.order_id > 0
        and t.sys_status = 'FINISHED_AUDIT'
        and t.express_print_time &lt;= '2000-01-01 00:00:00'
        and t.item_num = 1
        and t.is_excep = 0
        <include refid="itemUniqueCodeQuerySql"/>
        order by
        <isEqual property="sortField" compareValue="payTime">
            (t.pay_time is null or t.pay_time = '2000-01-01 00:00:00'), ifnull(t.pay_time, '2000-01-01 00:00:00'),
        </isEqual>
        <isEqual property="sortField" compareValue="timeOutActionTime">
            (t.timeout_action_time is null or t.timeout_action_time = '2000-01-01 00:00:00'), ifnull(t.timeout_action_time, '2000-01-01 00:00:00'),
        </isEqual>
        t.sid
        <isNotEmpty property="startRow">
            limit #startRow#, #pageSize# <!-- 指定数量 -->
        </isNotEmpty>
    </select>

    <select id="repairRepeatUniqueCode" resultClass="long">
        SELECT DISTINCT a.sid FROM
             ( SELECT * FROM wave_unique_code_#orderDbNo#
                    WHERE company_id = #companyId#
                    AND created >= '2025-01-07 00:00:00'
                    AND status IN (1,2,3,4,5)
                    AND order_id > 0 AND type = 1 GROUP BY order_id, batch_no, date_no )
        a GROUP BY a.order_id HAVING count(1) > 1
    </select>

    <select id="querySids4MatchMutli" resultClass="long">
        /*FORCE_MASTER*/ select w.sid
        <include refid="sourceData"/>
        left join trade_#tradeDbNo# t on w.sid = t.sid
        <isNotEmpty property="tradeWaveIds">
            and t.wave_id != 0
        </isNotEmpty>
        where w.company_id = #companyId#
        and w.matched_status != 2
        and w.order_id > 0
        and w.type = 2
        and w.status in (10, 11)
        and w.outer_id = #outerId#
        and t.sys_status = 'FINISHED_AUDIT'
        and t.express_print_time &lt;= '2000-01-01 00:00:00'
        and (t.is_excep = 0 OR (t.is_excep = 1 AND w.matched_status != 4))
        group by w.sid
        having count(w.sid) >= #scanNum#
    </select>

    <select id="matchUnboundUniqueCode4FastConsign" resultMap="unboundUniqueCodeMap">
        /*FORCE_MASTER*/ select w.*,l.reason_type
        <include refid="sourceData"/>
        left JOIN order_unique_code_unbound_log_#orderUniqueUnboundLogDbNo# l
        ON w.company_id = l.company_id AND w.unique_code = l.unique_code
        where w.company_id = #companyId#
        and w.matched_status not in (2, 4)
        and w.order_id = 0
        and w.type = 2
        and w.status = 10
        and w.enable_status = 1
        and w.outer_id = #outerId#
        and l.reason_type > 0
        order by w.created, l.created desc
        limit 1
    </select>


</sqlMap>
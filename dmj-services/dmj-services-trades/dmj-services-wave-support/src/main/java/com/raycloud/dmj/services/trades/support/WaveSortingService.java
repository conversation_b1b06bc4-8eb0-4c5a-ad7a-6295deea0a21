package com.raycloud.dmj.services.trades.support;

import com.google.common.base.Function;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.wave.WaveHelpBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.wave.*;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.SuiteSingle;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbOrder;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.RefundUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.CompanyUtils;
import com.raycloud.dmj.domain.utils.MapUtils;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.model.ItemUniqueCodeQueryParams;
import com.raycloud.dmj.domain.wave.model.OrderUniqueCodeQueryParams;
import com.raycloud.dmj.domain.wave.utils.OrderUniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.UniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.WmsChangeAffect;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.domain.wms.WorkingStorageSection;
import com.raycloud.dmj.services.domain.ItemTraceActionEnum;
import com.raycloud.dmj.services.domain.ItemTraceBillTypeEnum;
import com.raycloud.dmj.services.domain.WaveItemTraceActionModulePathEnum;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.trace.IItemTraceService;
import com.raycloud.dmj.services.trades.ITradePostPrintService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.IWaveSortingService;
import com.raycloud.dmj.services.trades.support.wave.WaveItemTraceLogHelper;
import com.raycloud.dmj.services.trades.support.wave.business.PostPrintUniqueCodeBusiness;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.trades.support.wave.proxy.WaveUseTradeServiceProxy;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsKeyUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 波次分拣实现
 * Created by shaoxianchang on 2017/2/13.
 */
@Service
public class WaveSortingService implements IWaveSortingService, ApplicationContextAware {

    private Logger logger = Logger.getLogger(this.getClass());

    @Autowired
    WaveSortingDao waveSortingDao;

    @Autowired
    private WavePickingDao wavePickingDao;

    @Resource
    private TbOrderDAO tbOrderDAO;

    @Autowired
    private IWavePositionService wavePositionService;

    @Autowired
    private WaveRuleDao waveRuleDao;
    @Resource
    private WaveDao waveDao;

    @Autowired
    private WaveSeedLogDao waveSeedLogDao;

    @Resource
    private WaveTradeDao waveTradeDao;

    @Resource
    private WaveUniqueCodeDao waveUniqueCodeDao;

    @Resource
    private IEventCenter eventCenter;

    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;

    @Resource
    private IWaveConfigService waveConfigService;

    @Resource(name = "tbTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private WaveUseTradeServiceProxy waveUseTradeServiceProxy;

    @Resource
    private IWaveUniqueCodeService waveUniqueCodeService;

    @Resource
    private WavePickerDao wavePickerDao;

    @Resource
    private IItemTraceService itemTraceService;

    private ApplicationContext applicationContext;
    private WaveSortingService beanProxy;
    @Resource
    private IWmsService wmsService;
    @Resource
    private WaveUniqueCodeLogDao waveUniqueCodeLogDao;
    @Resource
    private PostPrintUniqueCodeBusiness postPrintUniqueCodeBusiness;
    @Resource
    private IUniqueCodeBaseService uniqueCodeBaseService;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void startup() {
        beanProxy = applicationContext.getBean(WaveSortingService.class);
    }

    @Resource
    private ITradePostPrintService tradePostPrintService;
    @Resource
    private WaveHelpBusiness waveHelpBusiness;

    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;

    @Resource
    private IUniqueCodeGenericService genericService;

    @Override
    @Transactional
    public void batchInsert(Staff staff, List<WaveSorting> sortings,Boolean withPositionNo) {
        if (withPositionNo) {
            waveSortingDao.batchInsertOld(staff, sortings);
        } else {
            waveSortingDao.batchInsert(staff, sortings, withPositionNo);
        }
        List<WaveSortingDetail> allWaveSortingDetailList = Lists.newArrayList();
        for (WaveSorting sorting : sortings) {
            List<WaveSortingDetail> details = sorting.getDetails();
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            for (WaveSortingDetail detail : details) {
                detail.setSortingId(sorting.getId());
                if (detail.getSysSkuId() == null || detail.getSysSkuId() <= 0L) {
                    detail.setSysSkuId(-1L);
                }
            }
            allWaveSortingDetailList.addAll(details);
        }
        waveSortingDao.batchInsertDetails(staff, allWaveSortingDetailList);
    }

    @Override
    @Transactional
    public List<WaveSorting> matchAllByWaveId(Staff staff, Long pickingId) {
        List<WaveSorting> waveSortings = waveSortingDao.queryByPickingId(staff, pickingId, null, null);
        if (CollectionUtils.isEmpty(waveSortings)) {
            throw new IllegalArgumentException(String.format("根据拣选号[%s]未找到拣选订单记录!", pickingId));
        }

        List<WaveSortingDetail> allDetails = waveSortingDao.queryDetailsBySortingIds(staff, waveSortings.stream().map(WaveSorting::getId).collect(Collectors.toList()));
        Map<Long, List<WaveSortingDetail>> sortingIdDetailsMap = allDetails.stream().collect(Collectors.groupingBy(WaveSortingDetail::getSortingId));
        List<WaveSortingDetail> updateDetails = Lists.newArrayListWithCapacity(allDetails.size());
        List<WaveSorting> updates = Lists.newArrayListWithCapacity(waveSortings.size());
        for (WaveSorting waveSorting : waveSortings) {
            List<WaveSortingDetail> details = sortingIdDetailsMap.get(waveSorting.getId());
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            boolean matchStatusOver = true;
            for (WaveSortingDetail detail : details) {
                detail.setMatchedNum(detail.getPickedNum());
                detail.setChangeMatchedNum(detail.getPickedNum());
                detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
                if (detail.getMatchedNum().intValue() != detail.getItemNum() && WaveUtils.needPick(detail.getGiftPickAndCheck())) {
                    detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_ING);
                    matchStatusOver = false;
                }
                updateDetails.add(detail);
            }
            waveSorting.setMatchedStatus(matchStatusOver ? WaveSorting.MATCHED_STATUS_OVER : WaveSorting.MATCHED_STATUS_ING);
            updates.add(waveSorting);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("直接播种时，分拣订单详细已匹配完成,分拣号:%s,分拣状态:%s", waveSorting.getId(), waveSorting.getMatchedStatus())));
            }
        }

        if (!updates.isEmpty()) {
            waveSortingDao.batchUpdateStatus(staff, updates);
            updateWaveTradeMatchedStatus(staff, updates);
        }
        if (!updateDetails.isEmpty()) {
            waveSortingDao.batchUpdateMatchedDetails(staff, updateDetails);
            //记录播种日志
            waveSeedLogDao.batchInsert(staff, WaveUtils.buildSeedLogs(updates.get(0).getWaveId(), updateDetails));
        }
        return waveSortings;
    }

    @Override
    public List<WaveSorting> getUnSeedOverSorting(Staff staff, Long waveId) {
        WavePicking wavePicking = wavePickingDao.getByWaveId(staff, waveId);
        Assert.notNull(wavePicking, String.format("根据波次号[%s]找不到拣选记录!", waveId));
        Long pickingId = wavePicking.getId();
        List<WaveSorting> waveSortings = waveSortingDao.queryByPickingId(staff, pickingId, null, null);
        if (CollectionUtils.isEmpty(waveSortings)) {
            throw new IllegalArgumentException(String.format("根据拣选号[%s]未找到拣选订单记录!", pickingId));
        }
        List<WaveSortingDetail> allDetails = waveSortingDao.queryDetailsBySortingIds(staff, waveSortings.stream().map(WaveSorting::getId).collect(Collectors.toList()));
        Map<Long, List<WaveSortingDetail>> sortingIdDetailsMap = allDetails.stream().collect(Collectors.groupingBy(WaveSortingDetail::getSortingId));
        for (WaveSorting waveSorting : waveSortings) {
            List<WaveSortingDetail> details = sortingIdDetailsMap.get(waveSorting.getId());
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            boolean matchStatusOver = true;
            for (WaveSortingDetail detail : details) {
                detail.setMatchedNum(detail.getPickedNum());
                detail.setChangeMatchedNum(detail.getPickedNum());
                detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
                if (detail.getMatchedNum().intValue() != detail.getItemNum() && WaveUtils.needPick(detail.getGiftPickAndCheck())) {
                    detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_ING);
                    matchStatusOver = false;
                }
            }
            waveSorting.setMatchedStatus(matchStatusOver ? WaveSorting.MATCHED_STATUS_OVER : WaveSorting.MATCHED_STATUS_ING);
        }
        return waveSortings;
    }

    @Override
    @Transactional
    public List<WaveSorting> matchAllByWaveIdForOpen(Staff staff, Long pickingId, WavePickingParam param, TradeConfig tradeConfig, WaveSeedForOpenResult result, WaveConfig waveConfig) {
        List<WaveSeedForOpenResult.WaveSeedForOpenErrorPosition> errorPositions = new ArrayList<>();
        result.setPositionErrorList(errorPositions);
        List<WaveSorting> waveSortings = waveSortingDao.querySortingByPickIdsForOpen(staff, Lists.newArrayList(pickingId), null, 0);
        if (CollectionUtils.isEmpty(waveSortings)) {
            throw new IllegalArgumentException(String.format("根据拣选号[%s]未找到拣选订单记录!", pickingId));
        }
        List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, true, waveSortings.stream().map(WaveSorting::getSid).toArray(Long[]::new));
        Map<Long, Trade> tradeMap = trades.stream().collect(Collectors.toMap(Trade::getSid, t -> t, (v1, v2) -> v1));
        //需要播种的同位置号商品合并下数量
        List<WaveSortingDetail> seedDetails = param.getDetails();
        Map<String, WaveSortingDetail> seedDetailPositionMap = new HashMap<>();
        for (WaveSortingDetail seedDetail : seedDetails) {
            String key = seedDetail.getPositionNo() + "_" + seedDetail.getOuterId();
            if (seedDetailPositionMap.get(key) == null) {
                seedDetailPositionMap.put(key, seedDetail);
            } else {
                WaveSortingDetail detail = seedDetailPositionMap.get(key);
                detail.setMatchedNum(detail.getMatchedNum() + seedDetail.getMatchedNum());
            }
        }
        Map<Long, List<WaveSorting>> positionSortingMap = waveSortings.stream().collect(Collectors.groupingBy(t -> (long) t.getPositionNo()));
        Map<Long, List<WaveSortingDetail>> positionSeedMap = seedDetailPositionMap.values().stream().collect(Collectors.groupingBy(WaveSortingDetail::getPositionNo));
        List<WaveSortingDetail> allDetails = waveSortingDao.queryDetailsBySortingIdsForOpen(staff, waveSortings.stream().map(WaveSorting::getId).collect(Collectors.toList()), 0);
        Map<Long, List<WaveSortingDetail>> sortingIdDetailsMap = allDetails.stream().collect(Collectors.groupingBy(WaveSortingDetail::getSortingId));
        List<WaveSortingDetail> updateDetails = Lists.newArrayListWithCapacity(allDetails.size());
        List<WaveSorting> updates = Lists.newArrayListWithCapacity(waveSortings.size());

        for (Map.Entry<Long, List<WaveSortingDetail>> entry : positionSeedMap.entrySet()) {
            Long positionNo = entry.getKey();
            List<WaveSortingDetail> seedDetailList = entry.getValue();
            Map<String, WaveSortingDetail> outerIdSeedMap = seedDetailList.stream().collect(Collectors.toMap(WaveSortingDetail::getOuterId, t -> t, (v1, v2) -> v1));
            List<WaveSorting> sortings = positionSortingMap.get(positionNo);
            //找不到播种的sorting
            if (CollectionUtils.isEmpty(sortings)) {
                WaveSeedForOpenResult.WaveSeedForOpenErrorPosition errorPosition = new WaveSeedForOpenResult.WaveSeedForOpenErrorPosition();
                errorPosition.setPositionNo(positionNo);
                errorPosition.setErrorMsg("位置号" + positionNo + "踢出波次!");
                errorPosition.setDetailList(WaveSeedForOpenResult.convertSortingDetailToModel(seedDetailList));
                errorPositions.add(errorPosition);
                continue;
            }
            String errorMsg = null;
            List<WaveSorting> templateUpdates = new ArrayList<>();
            List<WaveSortingDetail> templateUpdateDetails = new ArrayList<>();
            for (WaveSorting sorting : sortings) {
                //对应订单异常
                Trade trade = tradeMap.get(sorting.getSid());
                if (trade == null || CollectionUtils.isEmpty(TradeUtils.getOrders4Trade(trade))) {
                    errorMsg = "订单sid:" + sorting.getSid() + " 未查询到订单或子单";
                    continue;
                }
                List<WaveSortingDetail> details = sortingIdDetailsMap.get(sorting.getId());
                if (CollectionUtils.isEmpty(details)) {
                    continue;
                }
                boolean matchStatusOver = true;
                for (WaveSortingDetail detail : details) {
                    List<Order> orders = TradeUtils.getOrders4Trade(trade).stream()
                            .filter(o -> Objects.equal(o.getId(), detail.getOrderId()) && RefundUtils.isRefundOrder(o)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(orders)) {
                        if (logger.isDebugEnabled()) {
                            logger.debug(LogHelper.buildLog(staff, "开放平台播种过滤退款，orderId：" + detail.getOrderId()));
                        }
                        errorMsg = "订单部分关闭";
                        matchStatusOver = false;
                        continue;
                    }
                    WaveSortingDetail seedDetail = outerIdSeedMap.get(detail.getOuterId());
                    if (seedDetail == null || seedDetail.getMatchedNum() == 0) {
                        matchStatusOver = false;
                        continue;
                    }
                    Integer oldMatchedNum = detail.getMatchedNum();
                    if (detail.getMatchedNum() + seedDetail.getMatchedNum() > detail.getItemNum()) {
                        detail.setMatchedNum(detail.getItemNum());
                        //剩余数量
                        seedDetail.setMatchedNum(oldMatchedNum + seedDetail.getMatchedNum() - detail.getItemNum());
                    } else {
                        detail.setMatchedNum(detail.getMatchedNum() + seedDetail.getMatchedNum());
                        seedDetail.setMatchedNum(0);
                    }
                    detail.setChangeMatchedNum(detail.getMatchedNum() - oldMatchedNum);
                    detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
                    if (detail.getMatchedNum().intValue() != detail.getItemNum()) {
                        detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_ING);
                        matchStatusOver = false;
                    }
                    templateUpdateDetails.add(detail);
                }
                sorting.setMatchedStatus(matchStatusOver ? WaveSorting.MATCHED_STATUS_OVER : WaveSorting.MATCHED_STATUS_ING);
                templateUpdates.add(sorting);
            }
            List<WaveSortingDetail> errorSeedDetails = outerIdSeedMap.values().stream().filter(a -> a.getMatchedNum() > 0).collect(Collectors.toList());
            //传参播种数量大于能播种的数量 则不去更新
            if (CollectionUtils.isNotEmpty(errorSeedDetails)) {
                WaveSeedForOpenResult.WaveSeedForOpenErrorPosition errorPosition = new WaveSeedForOpenResult.WaveSeedForOpenErrorPosition();
                errorPosition.setPositionNo(positionNo);
                errorPosition.setErrorMsg(StringUtils.isNotEmpty(errorMsg) ? errorMsg : "位置号" + positionNo + "播种数量大于可播数量!");
                errorPosition.setDetailList(WaveSeedForOpenResult.convertSortingDetailToModel(errorSeedDetails));
                errorPositions.add(errorPosition);
                //大于播种数量的情况 把播种状态改成播种中 不落库 后续判断不会为播种完成
                templateUpdates.forEach(s -> s.setMatchedStatus(WaveSorting.MATCHED_STATUS_ING));
            } else {
                updateDetails.addAll(templateUpdateDetails);
                updates.addAll(templateUpdates);
            }
        }

        if (!updates.isEmpty()) {
            waveSortingDao.batchUpdateStatus(staff, updates);
            updateWaveTradeMatchedStatus(staff, updates);
        }
        if (!updateDetails.isEmpty()) {
            waveSortingDao.batchUpdateMatchedDetails(staff, updateDetails);
            //记录播种日志
            waveSeedLogDao.batchInsert(staff, WaveUtils.buildSeedLogs(updates.get(0).getWaveId(), updateDetails));
        }
        //更新唯一码
        if (Objects.equal(waveConfig.getOpenWaveUniqueCode(), 1)) {
            waveUniqueCodeService.updateByWaveSortingDetails(staff, updateDetails, param.getWaveId());
        }
        return waveSortings;
    }


    @Override
    public void updateStatus(Staff staff, WaveSorting waveSorting) {
        waveSortingDao.updateStatus(staff, waveSorting);
    }

    @Override
    public List<WaveSortingDetail> queryDetailsBySortingId(Staff staff, Long sortingId) {
        return queryDetailsBySortingId(staff, sortingId, false);
    }

    @Override
    public List<WaveSortingDetail> queryDetailsBySortingId(Staff staff, Long sortingId, boolean containSuitSelf) {
        return waveSortingDao.queryDetailsBySortingId(staff, sortingId, containSuitSelf);
    }

    @Override
    @Transactional
    public WaveSorting matchOneByOuterId(Staff staff, WavePickingParam param, WaveUniqueCode waveUniqueCode) {
        return matchOneByOuterId(staff, param, waveUniqueCode, null);
    }

    private WaveSortingDetail queryOneMatchedDetailByOuterId(Staff staff, WavePickingParam param, Boolean seed, WaveConfig waveConfig) {
        WaveSortingParam sortingParam = new WaveSortingParam();
        sortingParam.setPickingId(param.getPickingId());
        sortingParam.setOuterId(param.getOuterId());
        sortingParam.setPositionNo(param.getPositionNo());
        sortingParam.setOrderId(param.getOrderId());
        sortingParam.setPrintStatus(param.getPrintStatus());
        sortingParam.setSeed(seed);
        sortingParam.setLockSid(param.getLockSid());
        sortingParam.setShipperSpeSysItemId(param.getShipperSpeSysItemId());
        sortingParam.setMatchTradePickedFirst(WaveHelpBusiness.isOpenPostPrintPickFirst(waveConfig) || WmsUtils.isWorkStorageSectionAllocate(staff, waveConfig, wmsService.getConfig(staff)));
        return waveSortingDao.queryOneMatchedDetailByOuterId(staff, sortingParam, waveConfig);
    }

    @Override
    @Transactional
    public WaveSorting matchOneByOuterId(Staff staff, WavePickingParam param, WaveUniqueCode waveUniqueCode, WaveSortingDetail detail) {
        long t1, t2, t3, t4, t5, t6, t7, t8 = 0, t9, t10, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0, t16 = 0;
        long start = System.currentTimeMillis();
        // 判断是否已经绑定过唯一码
        WaveUniqueCode bindWaveUniqueCode = postPrintUniqueCodeBusiness.checkBindUniqueCode(staff, param);
        boolean uniqueCodeBindTrade = bindWaveUniqueCode != null && DataUtils.checkLongNotEmpty(bindWaveUniqueCode.getSid());
        t1 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        WaveConfig waveConfig = waveConfigService.get(staff);
        // 简洁模式判断是否分配位置号和订单绑定的外采商品唯一码是否到齐
        boolean needPosition = needGeneratePosition(staff, bindWaveUniqueCode, param);
        if (needPosition) {
            if (BooleanUtils.isTrue(param.isAllReceive())) { // 全部到齐分配位置号，扫描唯一码需要匹配到关联订单
                param.setOrderId(bindWaveUniqueCode.getOrderId());
            }
            // 混合拣选号轮播参数detail不等于null
            if (detail == null) {
                detail = wavePositionService.needClearPosition(staff, param.getPickingId()) ?
                        wavePositionService.generatePosition(staff
                                , waveRuleDao.queryById(staff, param.getWaveRuleId())
                                , waveSortingDao.queryNeedWavePrintByPickingIds(staff, Collections.singletonList(param.getPickingId()), param.getPrintStatus(), waveConfigService.get(staff))
                                , param.getOuterId(), param.getOrderId()
                                , Collections.singletonList(param.getPickingId())
                        , param.getShipperSpeSysItemId()
                            ): queryOneMatchedDetailByOuterId(staff, param, true, waveConfig);
                if (detail != null && BooleanUtils.isTrue(param.isAllReceive())) { // 全部到齐清空虚拟位置号
                    clearVirtualPosition(staff, detail, param.getWaveId());
                }
            }
        } else {
            // 外采商品唯一码未到齐时播种直接匹配对应的订单，如果订单没分配位置号则分配虚拟位置号
            param.setOrderId(bindWaveUniqueCode.getOrderId());
            detail = queryOneMatchedDetailByOuterId(staff, param, true, waveConfig);
            if (detail != null) {
                logger.debug(String.format("播种打印简洁模式唯一码: [%s] 直接匹配关联订单: [%s]", param.getBindUniqueCode().getUniqueCode(), detail.getSid()));
                dealVirtualPosition(staff, detail, param.getWaveId());
            }
        }
        t2 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        if (detail == null) {
            //验证该商品是否已播种完成
            param.setPrintStatus(CommonConstants.JUDGE_IGNORE);
            WaveSortingDetail exist = queryOneMatchedDetailByOuterId(staff, param, false, waveConfig);
            if (exist != null) {
                //存在位置号，重复扫描了，验证是否成单
                if (StringUtils.isNotBlank(param.getOuterId()) && (param.getPositionNo() != null || param.getOrderId() != null)) {
                    List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySortingId(staff, exist.getSortingId());
                    if (CollectionUtils.isNotEmpty(details) && details.stream().allMatch(d -> WaveSorting.MATCHED_STATUS_OVER == d.getMatchedStatus())) {
                        Trade trade = tradeSearchService.queryBySid(staff, true, exist.getSid());
                        TradeValidator validator = new TradeValidator();
                        validator.setThrowExceptionIfError(false);
                        validator.check(staff, trade);
                        if (validator.hasError()) {
                            throw new TradeValidatorException(validator.getCode(), validator.getMessage());
                        }
                    }
                }
                // 校验播种是否已打印
                checkSeedPrinted(exist);
                throw new WaveScanException(WaveScanException.ERROR_CODE_ITEM_SEED_OVER, "该波次商品已播种完成！");
            }

            //存在位置号，验证该商品是否存在以前的剔出波次当中
            if (StringUtils.isNotBlank(param.getOuterId()) && (param.getPositionNo() != null || param.getOrderId() != null)) {
                Map<Long, String> sidStatusMap = waveSortingDao.queryRemoveWaveSidSysStatusByOuterId(staff, param.getPickingId(), param.getOuterId(), param.getPositionNo(), param.getOrderId());
                if (!sidStatusMap.isEmpty()) {
                    TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
                    for (Map.Entry<Long, String> entry : sidStatusMap.entrySet()) {
                        Trade trade = tradeSearchService.queryBySid(staff, true, entry.getKey());
                        if (trade == null) {
                            continue;
                        }
                        String sysStatus = TradeStatusUtils.convertSysStatus(trade, tradeConfig);
                        if (Trade.SYS_STATUS_CLOSED.equals(sysStatus)) {
                            throw new WaveScanException(WaveScanException.ERROR_CODE_SEED_TRADE_CLOSED_NOT_EXIST, "订单[" + trade.getSid() + "]关闭踢出波次！");
                        } else if (Trade.SYS_STATUS_CANCEL.equals(sysStatus)) {
                            throw new WaveScanException(WaveScanException.ERROR_CODE_SEED_TRADE_CANCEL_NOT_EXIST, "订单[" + trade.getSid() + "]作废踢出波次！");
                        } else if (Trade.SYS_STATUS_WAIT_AUDIT.equals(sysStatus)) {
                            throw new WaveScanException(WaveScanException.ERROR_CODE_SEED_TRADE_WAIT_AUDIT_NOT_EXIST, "订单[" + trade.getSid() + "]重新审核踢出波次！");
                        } else if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus)) {
                            throw new WaveScanException(WaveScanException.ERROR_CODE_SEED_TRADE_SELLER_SEND_GOODS_NOT_EXIST, "订单[" + trade.getSid() + "]发货踢出波次！");
                        }
                    }
                    String sids = sidStatusMap.keySet().stream().map(String::valueOf).collect(Collectors.joining(",", "[", "]"));
                    throw new WaveScanException(WaveScanException.ERROR_CODE_SEED_TRADE_NOT_EXIST, "订单" + sids + "踢出波次!");
                }
            }
            //否则商品不在波次中
            throw new WaveScanException(WaveScanException.ERROR_CODE_ITEM_NOT_IN_WAVE, "该波次订单没有这种商品！");
        }
        t3 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        List<WaveSortingDetail> mergeDetailList = null;
        if (param.isSeedAllPosition() && detail.getSortingId() != null && StringUtils.isNotEmpty(detail.getOuterId()) && detail.getId() != null) {
            mergeDetailList = waveSortingDao.queryNotMatchedMergeDetails(staff, detail, true);
            param.setMergeDetailList(mergeDetailList);
        }
        t4 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        // 判断对应sortingDetail已全部绑定唯一码
        param.setBindUniqueCode(bindWaveUniqueCode);
        postPrintUniqueCodeBusiness.checkAllBindUniqueCode(staff, param, detail, false, false, null);
        t5 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        // 判断是否需要过滤退款商品
        WaveScanException waveScanException = beanProxy.seedFilterRefund(staff, detail, param);
        if (waveScanException != null) {
            throw waveScanException;
        }
        t6 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        checkTradeException(staff, detail.getSid(), param);
        if (BooleanUtils.isNotFalse(param.isAllReceive())) { // 简洁模式外采商品唯一码未到齐时不处理sortingDetail
            detail.setChangeMatchedNum(param.isSeedAllPosition() ? (detail.getItemNum() - detail.getMatchedNum().intValue()) : 1);
            //质检登记
            detail.setChecker(param.getChecker());
            detail.setMatchedNum(detail.getMatchedNum() + detail.getChangeMatchedNum());
            detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_ING);
        }
        t7 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        WaveSorting waveSorting = buildMatchWaveSorting(detail, param.getWaveId());
        if (BooleanUtils.isNotFalse(param.isAllReceive())) { // // 简洁模式外采商品唯一码未到齐时不处理sortingDetail、sorting、播种日志
            if (detail.getMatchedNum().intValue() == detail.getItemNum()) {
                if (param.isSeedAllPosition() && CollectionUtils.isNotEmpty(mergeDetailList)) {
                    handleAllSeedPosition(staff, mergeDetailList, detail, param);
                    mergeDetailList.add(detail);
                } else {
                    detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
                    waveSortingDao.updateMatchedDetail(staff, detail);
                }
                Integer count = waveSortingDao.queryNotMatchedDetailsCount(staff, detail.getSortingId());
                if (count == 0) {
                    waveSorting.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
                    updateWaveTradeMatchedStatus(staff, Collections.singletonList(waveSorting));
                    logger.debug(LogHelper.buildLog(staff, String.format("所有分拣明细都已匹配完成,分拣号:%s,商家编码：%s,sid:%s,posNo:%s", detail.getSortingId(), param.getOuterId(), detail.getSid(), detail.getPositionNo())));
                }
            } else {
                waveSortingDao.updateMatchedDetail(staff, detail);
            }
            t8 = System.currentTimeMillis() - start;

            start = System.currentTimeMillis();
            waveSortingDao.updateStatus(staff, waveSorting);
            //记录播种日志
            waveSeedLogDao.batchInsert(staff, WaveUtils.buildSeedLogs(param.getWaveId(), CollectionUtils.isNotEmpty(mergeDetailList) ? mergeDetailList : Lists.newArrayList(detail), param.getScanCode(), WaveUtils.getScanCodeType(detail.getOuterId(), param.getScanCode(), param.getScanCodeType())));
        }
        t9 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        //pda波次播种 商品日志
        if (WavePickingParam.SEED_SOURCE_PDA.equals(param.getSeedSource())) {
            try {
                WaveItemTraceLogHelper.recodeItemTraceLogByWaveSortingDetails(itemTraceService, staff, CollectionUtils.isNotEmpty(mergeDetailList) ? mergeDetailList : Lists.newArrayList(detail), ItemTraceActionEnum.WAVE_PC_SEED.getCode(),
                        ItemTraceBillTypeEnum.WAVE.getCode(), String.valueOf(param.getWaveId()), WaveItemTraceActionModulePathEnum.PRINT_SEED.getName(), param.getOperatePath());
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "PDA播种商品日志输出失败"), e);
            }
        }
        t10 = System.currentTimeMillis() - start;

        if (waveUniqueCode != null) {
            start = System.currentTimeMillis();
            updateUniqueCodeStatus(staff, Lists.newArrayList(waveUniqueCode), WaveSorting.MATCHED_STATUS_OVER, null, CommonConstants.JUDGE_YES);
            t11 = System.currentTimeMillis() - start;
        } else if (bindWaveUniqueCode != null) {
            start = System.currentTimeMillis();
            // 绑定唯一码
            bindWaveUniqueCode.setSid(detail.getSid());
            bindWaveUniqueCode.setOrderId(detail.getOrderId());
            int bindType = 1;
            if (WavePickingParam.SEED_SOURCE_PDA.equals(param.getSeedSource())) {
                //4 表示是 pda触发的波次播种
                bindType = 4;
                //波次号和 位置号进行传参，后续日志中需要使用
                bindWaveUniqueCode.setTempPositionNo(Long.valueOf(waveSorting.getPositionNo()));
                bindWaveUniqueCode.setTempWaveId(waveSorting.getWaveId());
            }
            postPrintUniqueCodeBusiness.bindUniqueCodeNew(staff, Collections.singletonList(bindWaveUniqueCode), bindType, null, param);
            t12 = System.currentTimeMillis() - start;
        } else {
            start = System.currentTimeMillis();
            waveUniqueCodeService.updateByWaveSortingDetails(staff, CollectionUtils.isNotEmpty(mergeDetailList) ? mergeDetailList : Lists.newArrayList(detail), param.getWaveId());
            t13 = System.currentTimeMillis() - start;
        }

        if (Objects.equal(WaveSorting.MATCHED_STATUS_OVER, waveSorting.getMatchedStatus())) {
            if (!param.isOpenSeedSimpleStyle() || bindWaveUniqueCode == null || !uniqueCodeBindTrade) {
                // 非简洁模式成单后要查一遍要平移的唯一码/简洁模式最后扫的是商家编码成单后要再查一遍要平移的唯一码/简洁模式pda放弃放弃拣选后要再查一遍要平移的唯一码
                WaveUniqueCode virtualCode = new WaveUniqueCode();
                virtualCode.setSid(detail.getSid());
                virtualCode.setUniqueCode(bindWaveUniqueCode == null ? null : bindWaveUniqueCode.getUniqueCode());
                fillMoveStockCodes4Seed(staff, virtualCode, param, false);
            }
            if (CollectionUtils.isNotEmpty(param.getMoveStockUniqueCodes())) {
                // 播种打印成单平移库存
                start = System.currentTimeMillis();
                postPrintUniqueCodeBusiness.moveWmsStock4UniqueCode(staff, param.getMoveStockUniqueCodes(), WorkingStorageSection.MoveStockEnum.PURCHASE);
                t14 = System.currentTimeMillis() - start;
                // 唯一码修改库存位置
                start = System.currentTimeMillis();
                postPrintUniqueCodeBusiness.moveStock4UniqueCode(staff, param.getMoveStockUniqueCodes(), 3);
                t15 = System.currentTimeMillis() - start;
            }
        }

        if (BooleanUtils.isNotFalse(param.isAllReceive())) { // 简洁模式外采商品唯一码未到齐时不配货
            // 暂存区配货
            start = System.currentTimeMillis();
            Trade tradeIncludeOrdersCache = param.getTradeIncludeOrdersCache();
            // 有种情况，先打印发货然后再播种，如果订单已经发货了再配货就有问题
            if (tradeIncludeOrdersCache == null || !TradeStatusUtils.isAfterSendGoods(tradeIncludeOrdersCache.getSysStatus())) {
                workingStorageSectionAllocateGoods(staff, param, CollectionUtils.isNotEmpty(mergeDetailList) ? mergeDetailList : Lists.newArrayList(detail));
            }
            t16 = System.currentTimeMillis() - start;
        }

        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, "matchOneByOuterId耗时：t1 = " + t1 + ", t2 = " + t2 + ", t3 = " + t3 + ", t4 = " + t4 + ", t5 = " + t5 + ", t6 = " + t6 + ", t7 = " + t7 + ", t8 = " + t8 + ", t9 = " + t9 + ", t10 = " + t10 + ", t11 = " + t11 + ", t12 = " + t12 + ", t13 = " + t13 + ", t14 = " + t14 + ", t15 = " + t15 + ", t16 = " + t16));
        }

        return waveSorting;
    }

    private void handleAllSeedPosition(Staff staff, List<WaveSortingDetail> mergeDetailList, WaveSortingDetail sortingDetail, WavePickingParam param) {
        List<WaveSortingDetail> updates = new ArrayList<>();
        for (WaveSortingDetail mergeDetail : mergeDetailList) {
            mergeDetail.setSid(sortingDetail.getSid());
            mergeDetail.setPrintStatus(sortingDetail.getPrintStatus());
            mergeDetail.setPositionNo(sortingDetail.getPositionNo());
            mergeDetail.setStockNum(sortingDetail.getStockNum());
            mergeDetail.setChangeMatchedNum(mergeDetail.getItemNum() - mergeDetail.getMatchedNum());
            mergeDetail.setChecker(param.getChecker());
            mergeDetail.setMatchedNum(mergeDetail.getItemNum());
            mergeDetail.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
            updates.add(mergeDetail);
        }
        sortingDetail.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
        updates.add(sortingDetail);
        waveSortingDao.batchUpdateMatchedDetails(staff, updates);
    }

    /**
     * 暂存区配货
     */
    private void workingStorageSectionAllocateGoods(Staff staff, WavePickingParam param, WaveSortingDetail detail) {
        if (!WmsUtils.isWorkStorageSectionAllocate(staff, waveConfigService.get(staff), wmsService.getConfig(staff))) {
            return;
        }
        if (detail.getPickedNum() != null && detail.getMatchedNum() > detail.getPickedNum()) {
            logger.debug(LogHelper.buildLog(staff, String.format("当前分拣明细id:%s, 匹配数:%s, 已拣数:%s", detail.getId(), detail.getMatchedNum(), detail.getPickedNum())));
            throw new WaveScanException(WaveScanException.ERROR_CODE_WAVE_PICK_NUM_LACK_NOT_SEED, "该商品未拣选，不支持播种！");
        }

        WaveSorting waveSorting = waveSortingDao.queryById(staff, detail.getSortingId());
        // 已经后置打印不校验
        if (waveSorting != null && Objects.equal(waveSorting.getPostStatus(), CommonConstants.JUDGE_YES)) {
            return;
        }
        List<WmsChangeAffect> affects = Lists.newArrayList();
        WmsChangeAffect affect = new WmsChangeAffect();
        affect.setBusiId(detail.getSid());
        affect.setSubBusiId(detail.getOrderId());
        affect.setSysItemId(detail.getSysItemId());
        affect.setSysSkuId(DataUtils.getZeroIfDefault(detail.getSysSkuId()));
        affect.setOuterId(detail.getOuterId());
        affect.setWarehouseId(param.getWarehouseId());
        affect.setTotalNum(detail.getChangeMatchedNum());
        affect.setWaveId(param.getWaveId());
        affects.add(affect);
        // 暂存区配货(配货失败不影响平移）
        wmsService.workingStorageSectionAllocateGoods(staff, affects, WorkingStorageSection.TypeEnum.PICK, WorkingStorageSection.MoveStockSourceEnum.SEED);
    }

    private void workingStorageSectionAllocateGoods(Staff staff, WavePickingParam param, List<WaveSortingDetail> detailList) {
        if (!WmsUtils.isWorkStorageSectionAllocate(staff, waveConfigService.get(staff), wmsService.getConfig(staff))) {
            return;
        }
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        for (WaveSortingDetail detail : detailList) {
            if (detail.getPickedNum() != null && detail.getMatchedNum() > detail.getPickedNum()) {
                logger.debug(LogHelper.buildLog(staff, String.format("当前分拣明细id:%s, 匹配数:%s, 已拣数:%s", detail.getId(), detail.getMatchedNum(), detail.getPickedNum())));
                throw new WaveScanException(WaveScanException.ERROR_CODE_WAVE_PICK_NUM_LACK_NOT_SEED, "该商品未拣选，不支持播种！");
            }
        }

        WaveSorting waveSorting = waveSortingDao.queryById(staff, detailList.get(0).getSortingId());
        // 已经后置打印不校验
        if (waveSorting != null && Objects.equal(waveSorting.getPostStatus(), CommonConstants.JUDGE_YES)) {
            return;
        }
        List<WmsChangeAffect> affects = Lists.newArrayList();
        for (WaveSortingDetail detail : detailList) {
            WmsChangeAffect affect = new WmsChangeAffect();
            affect.setBusiId(detail.getSid());
            affect.setSubBusiId(detail.getOrderId());
            affect.setSysItemId(detail.getSysItemId());
            affect.setSysSkuId(DataUtils.getZeroIfDefault(detail.getSysSkuId()));
            affect.setOuterId(detail.getOuterId());
            affect.setWarehouseId(param.getWarehouseId());
            affect.setTotalNum(detail.getChangeMatchedNum());
            affect.setWaveId(param.getWaveId());
            affects.add(affect);
        }

        // 暂存区配货(配货失败不影响平移）
        wmsService.workingStorageSectionAllocateGoods(staff, affects, WorkingStorageSection.TypeEnum.PICK, WorkingStorageSection.MoveStockSourceEnum.SEED);
    }

    /**
     * 校验播种已打印
     */
    private void checkSeedPrinted(WaveSortingDetail detail) {
        if (Objects.equal(detail.getMatchedStatus(), WaveSorting.MATCHED_STATUS_OVER) || Objects.equal(detail.getPrintStatus(), CommonConstants.JUDGE_NO)) {
            return;
        }
        throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_PRINT_EXPRESS_ORDER, "快递面单已打印！");
    }

    @Override
    public TradeConfig getTradeConfigCache(Staff staff, WavePickingParam param) {
        if (param.getTradeConfigCache() != null) {
            return param.getTradeConfigCache();
        }

        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        param.setTradeConfigCache(tradeConfig);
        return tradeConfig;
    }

    @Override
    public WaveConfig getWaveConfigCache(Staff staff, WavePickingParam param) {
        if (param.getWaveConfig() != null) {
            return param.getWaveConfig();
        }

        WaveConfig waveConfig = waveConfigService.get(staff);
        param.setWaveConfig(waveConfig);
        return waveConfig;
    }

    @Override
    public Trade getTradeCache(Staff staff, Long sid, WavePickingParam param, boolean noFilter) {
        if (param.getTradeIncludeOrdersCache() != null) {
            return param.getTradeIncludeOrdersCache();
        }

        if (noFilter) {
            List<Trade> trades = tradeSearchService.queryBySidsNoFilter(staff, true, sid);
            if (CollectionUtils.isNotEmpty(trades)) {
                param.setTradeIncludeOrdersCache(trades.get(0));
                return trades.get(0);
            }
            {
                return null;
            }
        } else {
            Trade trade = tradeSearchService.queryBySid(staff, true, sid);
            param.setTradeIncludeOrdersCache(trade);
            return trade;
        }
    }

    /**
     * 播种过滤退款的商品
     *
     * @param staff
     * @param detail
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public WaveScanException seedFilterRefund(Staff staff, WaveSortingDetail detail, WavePickingParam param) {
        List<WaveSortingDetail> sortingDetails = new ArrayList<>();
        sortingDetails.add(detail);
        if (CollectionUtils.isNotEmpty(param.getMergeDetailList())) {
            sortingDetails.addAll(param.getMergeDetailList());
        }
        WaveConfig waveConfig = getWaveConfigCache(staff, param);
        if (waveConfig.getSeedNotAllowRefund() == null || waveConfig.getSeedNotAllowRefund() == 0
                || !DataUtils.checkLongNotEmpty(detail.getOrderId()) || !DataUtils.checkLongNotEmpty(detail.getSid())) {
            return null;
        }
        Trade trade = getTradeCache(staff, detail.getSid(), param, true);
        if (trade == null || CollectionUtils.isEmpty(TradeUtils.getOrders4Trade(trade))) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "播种过滤退款，sid：" + detail.getSid() + "，未查询到订单或子单！"));
            }
            return null;
        }
        List<Long> refundOrderIds = TradeUtils.getOrders4Trade(trade).stream()
                .filter(RefundUtils::isRefundOrder).map(Order::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refundOrderIds)) {
            return null;
        }
        List<WaveSortingDetail> updates = new ArrayList<>();
        List<Long> refundDetailOrderIds = new ArrayList<>();
        for (WaveSortingDetail waveSortingDetail : sortingDetails) {
            if (!refundOrderIds.contains(waveSortingDetail.getOrderId())) {
                continue;
            }
            refundDetailOrderIds.add(waveSortingDetail.getOrderId());
            WaveSortingDetail update = new WaveSortingDetail();
            update.setId(waveSortingDetail.getId());
            update.setNotSeedReason(1);
            updates.add(update);
        }
        if (CollectionUtils.isNotEmpty(updates)) {
            waveSortingDao.batchUpdateDetails(staff, updates);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "播种过滤退款，orderId：" + refundDetailOrderIds));
            }
            return new WaveScanException(WaveScanException.ERROR_CODE_ITEM_REFUND, "商品已退款，无法播种！");
        }
        return null;
    }

    private void checkTradeException(Staff staff, Long sid, WavePickingParam param) {
        if (!DataUtils.checkLongNotEmpty(sid)) {
            return;
        }
        WaveConfig waveConfig = waveConfigService.get(staff);
        Trade trade = getTradeCache(staff, sid, param, true);
        try {
            WaveUtils.tradeValidator(staff, trade, waveConfig, Boolean.FALSE);
        } catch (TradeValidatorException e) {
            e.setErrMsg("[" + sid + "]" + e.getErrMsg());
            throw e;
        }
    }

    private WaveSorting buildMatchWaveSorting(WaveSortingDetail detail, Long waveId) {
        WaveSorting waveSorting = new WaveSorting();
        waveSorting.setId(detail.getSortingId());
        waveSorting.setMatchedStatus(WaveSorting.MATCHED_STATUS_ING);
        waveSorting.setSid(detail.getSid());
        waveSorting.setWaveId(waveId);
        waveSorting.setPositionNo(detail.getPositionNo().intValue());
        return waveSorting;
    }

    @Override
    @Transactional
    public WaveSorting matchMultiByOuterCodes(Staff staff, WavePickingParam param, WaveUniqueCode waveUniqueCode) {
        return matchMultiByOuterCodes(staff, param, waveUniqueCode, null);
    }

    private List<WaveSortingDetail> queryMultiMatchedByOuterCodes(Staff staff, WavePickingParam param) {
        WaveConfig waveConfig = waveConfigService.get(staff);
        WmsConfig wmsConfig = wmsService.getConfig(staff);
        param.setPostPrintMatchTradeSort(waveConfig.getPostPrintMatchTradeSort());
        boolean workStorageSectionAllocate = WmsUtils.isWorkStorageSectionAllocate(staff, waveConfig, wmsConfig);
        param.setMatchTradePickedFirst(WaveHelpBusiness.isOpenPostPrintPickFirst(waveConfig) || workStorageSectionAllocate);
        List<WaveSortingDetail> waveSortingDetails = waveSortingDao.queryMultiMatchedByOuterCodes(staff, param);
        if(CompanyUtils.openMultiShipper(staff)&&CollectionUtils.isNotEmpty(waveSortingDetails)&&param.getShipperId()!=null){
            waveSortingDetails=waveHelpBusiness.filterWaveSortingDetailByShipper(staff,waveSortingDetails,param.getShipperId());
        }
        return waveSortingDetails;
    }

    @Override
    @Transactional
    public WaveSorting matchMultiByOuterCodes(Staff staff, WavePickingParam param, WaveUniqueCode waveUniqueCode, List<WaveSortingDetail> details) {
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(staff).append("套件播种，商家编码：").append(param.getOuterId()).append(",明细：")
                    .append(param.getDetails().stream().map(d -> d.getOuterId() + "=" + d.getItemNum()).collect(Collectors.joining(","))));
        }
        // 混合拣选号轮播details不为空
        if (CollectionUtils.isEmpty(details)) {
            details = wavePositionService.needClearPosition(staff, param.getPickingId()) ?
                    wavePositionService.generatePositionSuit(staff
                            , waveRuleDao.queryById(staff, param.getWaveRuleId())
                            , waveSortingDao.queryNeedWavePrintByPickingIds(staff, Collections.singletonList(param.getPickingId()), param.getPrintStatus(), waveConfigService.get(staff))
                            , param.getOuterId()
                            , param.getDetails()
                            , Collections.singletonList(param.getPickingId())
                            , param.getShipperId())
                    : (waveUniqueCode != null ? waveSortingDao.queryMultiMatchedBySuitOrderId(staff, param, waveUniqueCode) : queryMultiMatchedByOuterCodes(staff, param));
        }
        if (CollectionUtils.isEmpty(details)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_ITEM_NOT_IN_WAVE, "该波次订单没有这种商品！");
        }
        //查出来的不一定都要播种，需要筛选
        List<WaveSortingDetail> outerCodes = param.getDetails();
        List<WaveSortingDetail> updates = Lists.newArrayListWithCapacity(details.size());
        boolean hasNotFinish = false;
        for (WaveSortingDetail outerCode : outerCodes) {
            for (WaveSortingDetail detail : details) {
                if (detail.getOuterId().equalsIgnoreCase(outerCode.getOuterId())) {
                    if (detail.getMatchedNum() >= detail.getItemNum() || outerCode.getItemNum() <= 0) {
                        continue;
                    }
                    int getNum = Math.min(detail.getItemNum() - detail.getMatchedNum(), outerCode.getItemNum());
                    detail.setChangeMatchedNum(getNum);
                    detail.setMatchedNum(detail.getMatchedNum() + getNum);
                    WaveConfig waveConfig = waveConfigService.get(staff);
                    //如果开启波次唯一码,不校验数量 KMERP-193064
                    if (!java.util.Objects.equals(waveConfig.getOpenWaveUniqueCode(), 1) ) {
                        if (detail.getMatchedNum() > detail.getPickedNum()) {
                            throw new WaveScanException(WaveScanException.ERROR_CODE_WAVE_PICK_NUM_LACK_NOT_SEED, String.format("播种数量[%s]大于拣选数量[%s]，请确认波次状态！", detail.getMatchedNum(), detail.getPickedNum()));
                        }
                    }
                    if (detail.getMatchedNum().equals(detail.getItemNum())) {
                        detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
                    } else {
                        hasNotFinish = true;
                        detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_ING);
                    }
                    outerCode.setItemNum(outerCode.getItemNum() - getNum);
                    //由于outerCode的itemNum是大于detail的，所以根据商家编码匹配到的明细只会有一条
                    updates.add(detail);
                }
            }
        }

        //更新唯一码播种状态
        if (waveUniqueCode == null) {
            waveUniqueCodeService.updateByWaveSortingDetails(staff, updates, param.getWaveId());
        } else {
            updateSuiteAndSingleUniqueCodes(staff, details, waveUniqueCode);
        }

        // 判断是否需要过滤退款商品，套件每个明细上面都会有退款标记
        WaveScanException waveScanException = beanProxy.seedFilterRefund(staff, outerCodes.get(0), param);
        if (waveScanException != null) {
            throw waveScanException;
        }

        //不能有剩余
        for (WaveSortingDetail outerCode : outerCodes) {
            Assert.isTrue(outerCode.getItemNum() == 0, "播种数量异常！");
        }
        Assert.notEmpty(updates, "无可播种记录！");
        waveSortingDao.batchUpdateMatchedDetails(staff, updates);

        WaveSortingDetail detail = updates.get(0);
        WaveSorting waveSorting = buildMatchWaveSorting(detail, param.getWaveId());
        //查询分拣是否完成
        if (!hasNotFinish) {
            Integer count = waveSortingDao.queryNotMatchedDetailsCount(staff, detail.getSortingId());
            if (count == 0) {
                waveSorting.setMatchedStatus(WaveSorting.MATCHED_STATUS_OVER);
                updateWaveTradeMatchedStatus(staff, Collections.singletonList(waveSorting));
            }
            waveSortingDao.updateStatus(staff, waveSorting);
        }

        //记录播种日志
        waveSeedLogDao.batchInsert(staff, WaveUtils.buildSeedLogs(param.getWaveId(), updates, param.getScanCode(), param.getScanCodeType()));
        return waveSorting;
    }

    private void updateSuiteAndSingleUniqueCodes(Staff staff, List<WaveSortingDetail> details, WaveUniqueCode waveUniqueCode) {
        List<WaveUniqueCode> singleCodes = waveUniqueCodeDao.queryByKeys(staff, Lists.newArrayList(waveUniqueCode.getWaveId()), null, details.stream().map(WaveSortingDetail::getOrderId).collect(Collectors.toList()));
        Assert.notEmpty(singleCodes, "找不到唯一码对应的套件单品唯一码！");

        List<WaveUniqueCode> resultList = Lists.newArrayList();
        Map<Long, List<WaveUniqueCode>> orderIdUniqueCodesMap = singleCodes.stream().filter(code -> WaveSorting.MATCHED_STATUS_NOT == code.getMatchedStatus()).collect(Collectors.groupingBy(WaveUniqueCode::getOrderId));
        for (WaveSortingDetail detail : details) {
            List<WaveUniqueCode> uniqueCodes = orderIdUniqueCodesMap.get(detail.getOrderId());
            Assert.notEmpty(uniqueCodes, "找不到唯一码对应的套件单品唯一码！");
            int needNum = DataUtils.getZeroIfDefaultI(detail.getChangeMatchedNum());
            Assert.isTrue(uniqueCodes.size() >= needNum, String.format("套件唯一码对应的单品[%s]唯一码数量不足！", detail.getOuterId()));
            resultList.addAll(uniqueCodes.subList(0, needNum));
        }

        resultList.add(waveUniqueCode);
        updateUniqueCodeStatus(staff, resultList, WaveSorting.MATCHED_STATUS_OVER, null, CommonConstants.JUDGE_YES);
    }

    private void updateUniqueCodeStatus(Staff staff, List<WaveUniqueCode> waveUniqueCodes, Integer matchedStatus, Integer postStatus, Integer receiveStatus) {
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            return;
        }

        waveUniqueCodeDao.batchUpdate(staff, waveUniqueCodes.stream().map(code -> {
            WaveUniqueCode codeUpdate = new WaveUniqueCode();
            codeUpdate.setId(code.getId());
            codeUpdate.setMatchedStatus(matchedStatus);
            codeUpdate.setPostStatus(postStatus);
            return codeUpdate;
        }).collect(Collectors.toList()));
    }

    private void updateWaveTradeMatchedStatus(Staff staff, List<WaveSorting> waveSortings) {
        if (CollectionUtils.isEmpty(waveSortings)) {
            return;
        }

        List<WaveTrade> updates = waveSortings.stream().map(waveSorting -> {
            WaveTrade update = new WaveTrade();
            update.setSid(waveSorting.getSid());
            update.setWaveId(waveSorting.getWaveId());
            update.setMatchedStatus(waveSorting.getMatchedStatus());
            return update;
        }).collect(Collectors.toList());

        waveTradeDao.batchUpdate(staff, updates);
    }

    /**
     * @return 完全匹配到返回订单，不完全匹配到返回null，没有组合抛出异常
     */
    @Override
    @Transactional
    public WaveSorting queryOneByOuterCodes(Staff staff, WavePickingParam param, List<WaveUniqueCode> waveUniqueCodes) {
        if (CollectionUtils.isEmpty(param.getDetails())) {
            throw new IllegalArgumentException("请扫描商品！");
        }
        // 后置打印 位置号顺序匹配订单
        if (Objects.equal(CommonConstants.JUDGE_YES, param.getPostPrintPositionFirst())) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("后置打印开启位置号顺序打印，跳过唯一码绑定流程")));
            }
            return postPrintPositionFirst(staff, param);
        }
        //先根据商品组合完全匹配是否有订单，如果没有则校验波次订单是否有这个组合，没有的话则提示
        WaveSorting waveSorting = waveSortingDao.queryOneUnPrintedByOuterCodes(staff, param, false);
        if (waveSorting == null) {
            //如果没有完全匹配到则进行商家编码组合校验检查，如果没有这个组合提示出来，如果商品组合不能完整匹配到订单，返回null
            WaveSorting check = waveSortingDao.queryOneUnPrintedByOuterCodes(staff, param, true);
            tradePostPrintService.checkPostPrintItem(staff, param, null, check == null);
            param.setPartMatchSorting(check);
        } else {
            // 后置打印判断唯一码绑定有没有超过最大数量
            List<WaveUniqueCode> bindCodes = postPrintUniqueCodeBusiness.checkAllBindUniqueCodeForPost(staff, param, waveSorting);
            //匹配到设置匹配状态
            updatePostStatusOver(staff, waveSorting);
            waveSorting.setUniqueCodes(waveUniqueCodes);
            // 绑定唯一码
            long uniqueCodeBind = System.currentTimeMillis();
            postPrintUniqueCodeBusiness.bindUniqueCodeNew(staff, bindCodes, 2, param.getWarehouseId());
            logger.debug(LogHelper.buildLog(staff, String.format("后置打印绑定唯一码，costTime：%s", System.currentTimeMillis() - uniqueCodeBind)));
        }

        return waveSorting;
    }

    private void updatePostStatusOver(Staff staff, WaveSorting waveSorting) {
        WaveSorting update = new WaveSorting();
        update.setPostStatus(WaveSorting.POST_STATUS_OVER);
        update.setId(waveSorting.getId());
        waveSortingDao.updateStatus(staff, update);
    }

    /**
     * 位置号优先：要先排序再判断是否成单，而不是先成单再排序 KMERP-64840
     */
    private WaveSorting postPrintPositionFirst(Staff staff, WavePickingParam param) {
        WaveSorting waveSorting = waveSortingDao.queryOneUnPrintedByOuterCodes(staff, param, true);
        if (waveSorting == null) {
            // 没有商品抛出异常
            tradePostPrintService.checkPostPrintItem(staff, param, null, true);
        } else {
            // 排除套件本身，排除赠品不参与拣选
            Map<String, Integer> allOid2NumMap = waveSortingDao.queryDetailBySortingId(staff, Lists.newArrayList(waveSorting.getId()))
                    .stream()
                    .filter(detail -> WaveUtils.needPick(detail.getGiftPickAndCheck()))
                    .filter(detail -> !(Objects.equal(detail.getSuitType(), 1) && Objects.equal(detail.getCombineId(), 0L)))
                    .collect(Collectors.toMap(data -> StringUtils.defaultString(data.getOuterId().toLowerCase()), WaveSortingDetail::getItemNum, Integer::sum));

            Map<String, Integer> oid2NumMap = Optional.ofNullable(param.getDetails()).orElse(Lists.newArrayList()).stream()
                    .collect(Collectors.toMap(data -> StringUtils.defaultString(data.getOuterId().toLowerCase()), WaveSortingDetail::getItemNum, Integer::sum));
            boolean allMatch = true;
            for (Map.Entry<String, Integer> entrySet : allOid2NumMap.entrySet()) {
                Integer itemNum = oid2NumMap.get(StringUtils.defaultString(entrySet.getKey().toLowerCase()));
                if (itemNum == null || !Objects.equal(entrySet.getValue(), itemNum)) {
                    allMatch = false;
                    break;
                }
            }
            if (allMatch) {
                //匹配到设置匹配状态
                updatePostStatusOver(staff, waveSorting);
                return waveSorting;
            }
        }
        return null;
    }

    @Override
    @Transactional
    public List<WaveSorting> queryBatchByOuterId(Staff staff, WavePickingParam param, DmjItem dmjItem) {
        Assert.hasText(param.getOuterId(), "请扫描商品！");
        Integer printNum = param.getPrintNum();
        Assert.isTrue(printNum != null && printNum > 0, "扫入数量格式不正确！");
        if (StringUtils.isNotEmpty(param.getFixedItemOuterId())) {
            Wave wave = waveDao.queryById(staff, param.getWaveId());
            Assert.isTrue(Objects.equal(wave.getRuleType(), WaveRule.RULE_TYPE_A_N), "该波次非A+N波次");
            if (dmjItem != null && dmjItem.isSuite() && CollectionUtils.isNotEmpty(dmjItem.getSuiteSingleList())) {
                param.setOuterId(dmjItem.getSuiteSingleList().get(0).getOuterId());
                param.setSuitMode(CommonConstants.JUDGE_NO);
            }
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("后置打印连扫，waveId：%s，pickingId：%s，outerId：%s，printNum：%s，fixItemOuterId：%s", param.getWaveId(), param.getPickingId(), param.getOuterId(), printNum, param.getFixedItemOuterId())));
        }
        if (Objects.equal(param.getValidatePrintNum(), CommonConstants.JUDGE_YES)) {
            Integer pickedNum = Objects.equal(param.getSuitMode(), CommonConstants.JUDGE_YES) ? getPickedNumBySuitMode(staff, param, dmjItem) : getPickedNum(staff, param);
            if (pickedNum != null) {
                if (pickedNum == 0) {
                    throw new WaveScanException(WaveScanException.ERROR_CODE_COMB_NOT_FOUND, "该波次订单没有这种商品组合！");
                }
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("开启按商品实际拣选数量进行匹配,当前pickedNum=%s,printNum=%s", pickedNum, param.getPrintNum())));
                }
                param.setPrintNum(Math.min(param.getPrintNum(), pickedNum));
            }
        }
        // 如果FixedItemOuterId不为空则为a+n连打
        List<WaveSorting> waveSortings = waveSortingDao.queryBatchUnPrintedByOuterId(staff, param);
        tradePostPrintService.checkPostPrintItem(staff, param, null, CollectionUtils.isEmpty(waveSortings));

        //校验实际拣选数量是否超出待打印数量
        if (WmsUtils.isNewWms(staff) && BooleanUtils.isNotTrue(param.getForce()) && printNum > waveSortings.size()) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_BATCH_PRINT_OVER_NUM, String.format("实际拣选数量%s超出待打印数量%s！", waveSortings.size(), printNum));
        }

        //匹配到设置匹配状态
        updateWaveSortingOver(staff, waveSortings);
        return waveSortings;
    }

    /**
     * 按实际拣选数匹配，套件模式
     */
    @Override
    public Integer getPickedNumBySuitMode(Staff staff, WavePickingParam param, DmjItem dmjItem) {
        if (dmjItem != null && dmjItem.isSuite() && CollectionUtils.isNotEmpty(dmjItem.getSuiteSingleList())) {
            List<SuiteSingle> suiteSingleList = dmjItem.getSuiteSingleList();
            List<Long> sysItemIds = suiteSingleList.stream().map(SuiteSingle::getSubItemId).collect(Collectors.toList());
            List<Long> sysSkuIds = suiteSingleList.stream().map(s -> Objects.equal(s.getSubSkuId(), 0L) ? -1 : s.getSubSkuId()).collect(Collectors.toList());
            List<WaveSortingDetail> suitSingleDetails = waveSortingDao.queryDetailsByItemIds(staff, param.getPickingId(), sysItemIds, sysSkuIds);
            Map<Long, List<WaveSortingDetail>> sumMap = suitSingleDetails.stream().filter(d -> WaveUtils.needPick(d.getGiftPickAndCheck()))
                    .collect(Collectors.groupingBy(WaveSortingDetail::getCombineId));
            Integer num = 0;
            for (Map.Entry<Long, List<WaveSortingDetail>> longListEntry : sumMap.entrySet()) {
                // 套件内单品全部拣选，才匹配套件
                boolean allPicked = longListEntry.getValue().stream().allMatch(d -> d.getPickedNum() != null && d.getPickedNum() > 0 && Objects.equal(d.getPickedNum(), d.getTotalNum()));
                if (allPicked) {
                    num++;
                }
            }
            return num;
        }
        return 0;
    }

    private Integer getPickedNum(Staff staff, WavePickingParam param) {
        if (StringUtils.isNotEmpty(param.getFixedItemOuterId())) {
            return waveSortingDao.queryBatchUnPrintPickedNumAPlusN(staff, param);
        } else {
            return waveSortingDao.queryBatchUnPrintPickedNum(staff, param);
        }
    }

    @Override
    public List<WaveSortingDetail> queryDetailBySids(Staff staff, List<Long> sids) {
        return WaveUtils.partitionToQuery(WaveUtils.WAVE_PARTITION_SIZE, sids, new java.util.function.Function<List<Long>, List<WaveSortingDetail>>() {
            @Override
            public List<WaveSortingDetail> apply(List<Long> subSids) {
                return waveSortingDao.queryDetailBySids(staff, subSids);
            }
        });
    }

    @Override
    public List<WaveSorting> queryWaveSortingsByWaveId(Staff staff, Long waveId, boolean withDetail) {
        return queryWaveSortingsByWaveId(staff, waveId, withDetail, null, false);
    }

    @Override
    public List<WaveSorting> queryWaveSortingsByWaveIds(Staff staff, List<Long> waveIds, boolean withDetail) {
        List<WavePicking> pickingList = wavePickingDao.getByWaveIds(staff, waveIds);
        if (CollectionUtils.isEmpty(pickingList)) {
            return null;
        }
        List<Long> pickingIdList = pickingList.stream().map(WavePicking::getId).collect(Collectors.toList());
        List<WaveSorting> sortings = waveSortingDao.queryByPickingIds(staff, pickingIdList, null, null, false);
        if (withDetail) {
            List<Long> sortingIds = Lists.newArrayListWithCapacity(sortings.size());
            for (WaveSorting sorting : sortings) {
                sortingIds.add(sorting.getId());
            }
            List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySortingIds(staff, sortingIds);
            Map<Long, List<WaveSortingDetail>> sortingIdDetailsMap = MapUtils.toMapList(details, new Function<WaveSortingDetail, Long>() {
                @Override
                public Long apply(WaveSortingDetail detail) {
                    return detail.getSortingId();
                }
            });
            for (WaveSorting sorting : sortings) {
                List<WaveSortingDetail> sortingDetails = sortingIdDetailsMap.get(sorting.getId());
                if (CollectionUtils.isNotEmpty(sortingDetails)) {
                    for (WaveSortingDetail waveSortingDetail : details) {
                        waveSortingDetail.setSid(sorting.getSid());
                    }
                }
                sorting.setDetails(sortingDetails);
            }
        }
        return sortings;
    }

    @Override
    public List<WaveSorting> queryWaveSortingsByWaveIdWithReport(Staff staff, Long waveId, boolean withDetail, Page page, boolean complete, boolean isReport) {
        WavePicking picking = wavePickingDao.getByWaveIdForceMaster(staff, waveId);
        Assert.notNull(picking, "该波次未拣选");

        List<WaveSorting> sortings = waveSortingDao.queryByPickingIds(staff, Lists.newArrayList(picking.getId()), null, page, complete);
        //组装货主
        if(CompanyUtils.openMultiShipper(staff)){
            assembleShipper(staff, sortings);
        }
        if (withDetail) {
            List<Long> sortingIds = Lists.newArrayListWithCapacity(sortings.size());
            for (WaveSorting sorting : sortings) {
                sortingIds.add(sorting.getId());
            }
            List<WaveSortingDetail> details = isReport ? waveSortingDao.queryDetailsBySortingIdsWithReport(staff, sortingIds) : waveSortingDao.queryDetailsBySortingIds(staff, sortingIds);
            Map<Long, List<WaveSortingDetail>> sortingIdDetailsMap = MapUtils.toMapList(details, new Function<WaveSortingDetail, Long>() {
                @Override
                public Long apply(WaveSortingDetail detail) {
                    return detail.getSortingId();
                }
            });
            for (WaveSorting sorting : sortings) {
                List<WaveSortingDetail> sortingDetails = sortingIdDetailsMap.get(sorting.getId());
                if (CollectionUtils.isNotEmpty(sortingDetails)) {
                    for (WaveSortingDetail waveSortingDetail : sortingDetails) {
                        waveSortingDetail.setSid(sorting.getSid());
                    }
                }
                sorting.setDetails(sortingDetails);
            }
        }
        return sortings;
    }

    private void assembleShipper(Staff staff, List<WaveSorting> sortings) {
        Long[] sidArray = sortings.stream().map(WaveSorting::getSid).filter(java.util.Objects::nonNull).toArray(Long[]::new);
        if(sidArray.length>0){
            List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, false, sidArray);
            Map<Long, Trade> tradeMap = trades.stream().collect(Collectors.toMap(Trade::getSid, t -> t, (v1, v2) -> v1));
            Map<Long, Pair<String, String>>  shipperInfoMap= waveHelpBusiness.queryShipByUserIds(staff, trades.stream().map(Trade::getUserId).collect(Collectors.toSet()));
            sortings.forEach(waveSorting -> Optional.ofNullable(tradeMap.get(waveSorting.getSid()))
                    .flatMap(trade -> Optional.ofNullable(shipperInfoMap.get(trade.getUserId()))).
                    ifPresent(shipperInfo -> {
                        waveSorting.setShipperId(shipperInfo.getKey());
                        waveSorting.setShipperName(shipperInfo.getValue());
                    }));
        }
    }

    @Override
    public List<WaveSorting> queryWaveSortingsByWaveId(Staff staff, Long waveId, boolean withDetail, Page page, boolean complete) {
        return queryWaveSortingsByWaveIdWithReport(staff, waveId, withDetail, page, complete, false);
    }

    @Override
    public PageListBase<WaveSorting> queryDetailByWaveId(Staff staff, Long waveId, boolean withDetail, Page page, boolean complete) {
        WavePicking picking = wavePickingDao.getByWaveId(staff, waveId);
        Assert.notNull(picking, "请先进行波次拣选！");
        PageListBase<WaveSorting> pageList = new PageList<>();
        if (page == null) {
            page = new Page().setPageSize(20);
        }
        Long count = waveSortingDao.queryCountByPickingId(staff, picking.getId(), complete);
        pageList.setTotal(count);
        pageList.setPage(page);
        if (DataUtils.checkLongNotEmpty(count)) {
            List<WaveSorting> waveSortings = queryWaveSortingsByWaveId(staff, waveId, withDetail, page, complete);
            filterDetailsByMatchStatus(waveSortings, complete);
            pageList.setList(waveSortings);
        } else {
            pageList.setList(new ArrayList<>());
        }
        return pageList;
    }

    public static List<WaveSorting> filterDetailsByMatchStatus(List<WaveSorting> sortings, boolean complete) {
        if (CollectionUtils.isEmpty(sortings)) {
            return new ArrayList<>();
        }

        if (complete) {
            sortings = sortings.stream().filter(waveSorting -> !Objects.equal(waveSorting.getMatchedStatus(), WaveSorting.MATCHED_STATUS_OVER)).collect(Collectors.toList());
        }
        for (WaveSorting sorting : sortings) {
            List<WaveSortingDetail> details = sorting.getDetails();
            if (CollectionUtils.isNotEmpty(details)) {
                if (complete) {
                    details = details.stream().filter(waveSorting -> !Objects.equal(waveSorting.getMatchedStatus(), WaveSorting.MATCHED_STATUS_OVER)).collect(Collectors.toList());
                    sorting.setDetails(details);
                }
            }
        }
        return sortings;
    }

    @Override
    public List<WaveSortingDetail> queryShortageWaveSortingDetailBySids(Staff staff, List<Long> sids) {
        List<WaveSorting> sortings = waveSortingDao.queryBySids(staff, sids).stream().sorted(Comparator.comparing(WaveSorting::getId).reversed()).collect(Collectors.toList());
        List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySortingIds(staff, sortings.stream().map(WaveSorting::getId).collect(Collectors.toList()));
        details = details.stream().filter(detail -> detail.getShortageNum() > 0).collect(Collectors.toList());
        Map<Long, Long> sortingsMap = sortings.stream().collect(Collectors.toMap(WaveSorting::getId, WaveSorting::getSid));
        details.forEach(detail -> detail.setSid(sortingsMap.get(detail.getSortingId())));
        if (CollectionUtils.isNotEmpty(sids)) {
            details = details.stream().filter(detail -> sids.contains(detail.getSid())).collect(Collectors.toList());
        }
        return details;
    }

    @Override
    @Transactional
    public void printSortingSids(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        List<WaveSorting> sortings = waveSortingDao.queryBySids(staff, sids);
        List<WaveSorting> modifyList = Lists.newArrayListWithCapacity(sortings.size());
        for (WaveSorting sorting : sortings) {
            WaveSorting temp = new WaveSorting();
            temp.setId(sorting.getId());
            temp.setPrintStatus(WaveSorting.PRINT_STATUS_OVER);
            modifyList.add(temp);
        }
        if (modifyList.size() > 0) {
            waveSortingDao.batchUpdateStatus(staff, modifyList);
        }
        logger.debug(LogHelper.buildLog(staff, String.format("打印分拣订单，sid：%s", sids)));
    }

    @Override
    public Map<Long, Long> querySidPositionNoMapByPickingId(Staff staff, Long pickingId) {
        return waveSortingDao.querySidPositionNoMapByPickingId(staff, pickingId);
    }

    @Override
    public List<WaveSortingDetail> queryDetailByOuterId(Staff staff, Long sortingId, String outerId) {
        return waveSortingDao.queryDetailByOuterId(staff, sortingId, outerId);
    }

    @Override
    public Integer queryNotMatchedOverCount(Staff staff, Long pickingId) {
        return waveSortingDao.queryNotMatchedOverCount(staff, pickingId);
    }

    @Override
    public Integer queryNotMatchedOverCount(Staff staff, List<Long> pickingIds) {
        return waveSortingDao.queryNotMatchedOverCount(staff, pickingIds);
    }

    @Override
    public WavePicking queryTradeAndItemNumByPickingId(Staff staff, Long pickingId) {
        return waveSortingDao.queryTradeAndItemNumByPickingId(staff, pickingId);
    }

    @Override
    public WavePicking queryTradeAndItemNumByPickingId(Staff staff, List<Long> pickingIds) {
        return waveSortingDao.queryTradeAndItemNumByPickingId(staff, pickingIds);
    }

    @Override
    public List<WaveSorting> queryItemAndMatchedNumGroupForSid(Staff staff, Long pickingId) {
        return waveSortingDao.queryItemAndMatchedNumGroupForSid(staff, pickingId);
    }

    @Override
    @Transactional
    public void resetMatchedByPickingId(Staff staff, Long pickingId, Integer printedStatus) {
        logger.info(LogHelper.buildLog(staff, "重置已匹配的分拣数量和状态，拣选Id：" + pickingId));
        waveSortingDao.resetMatchedSortingsByPickingId(staff, pickingId, printedStatus, wavePositionService.needClearPosition(staff, pickingId));
    }

    @Override
    @Transactional
    public void deleteSortingBySids(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        logger.debug(LogHelper.buildLog(staff, "根据订单编号删除分拣记录，sids：" + sids));

        List<TbOrder> orders = tbOrderDAO.queryBySids(staff, sids.toArray(new Long[0]));
        deleteSortingByOrderIds(staff, OrderUtils.toIdList(orders), sids);
    }

    @Override
    @Transactional
    public void deleteSortingByOrderIds(Staff staff, List<Long> orderIds) {
        deleteSortingByOrderIds(staff, orderIds, Lists.newArrayList());
    }

    @Override
    @Transactional
    public void deleteSortingByOrderIds(Staff staff, List<Long> orderIds, List<Long> sids) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }

        logger.debug(LogHelper.buildLog(staff, "根据orderIds移出分拣明细记录,orderIds:" + orderIds));
        List<WaveSortingDetail> details = waveSortingDao.queryDetailsByOrderIds(staff, orderIds);
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        List<Long> ids = Lists.newArrayListWithCapacity(details.size());
        Set<Long> sortingIdSet = Sets.newHashSet();
        for (WaveSortingDetail detail : details) {
            ids.add(detail.getId());
            sortingIdSet.add(detail.getSortingId());
        }

        if (CollectionUtils.isNotEmpty(sids)) {
            List<Long> notExistOrderIds = waveSortingDao.queryNotExistDetailOrderIds(staff, sids);
            ids.addAll(notExistOrderIds);
        }

        waveSortingDao.deleteDetailByIds(staff, ids);

        List<Long> sortingIds = new ArrayList<>(sortingIdSet);
        List<WaveSorting> sortings = waveSortingDao.queryByIds(staff, sortingIds);
        List<WaveSortingDetail> currDetails = waveSortingDao.queryDetailsBySortingIds(staff, sortingIds);
        Map<Long, List<WaveSortingDetail>> sortingIdDetailsMap = MapUtils.toMapList(currDetails, WaveSortingDetail::getSortingId);

        List<WaveSorting> updates = Lists.newArrayListWithExpectedSize(sortingIds.size());
        List<WaveSortingDetail> updateDetails = Lists.newArrayListWithExpectedSize(currDetails.size());
        for (WaveSorting sorting : sortings) {
            List<WaveSortingDetail> sortingDetails = sortingIdDetailsMap.get(sorting.getId());
            if (sortingDetails == null) {
                //分拣明细不存在，删除所有分拣信息
                updates.add(buildUpdateSorting(sorting, null, 0));
            } else {
                //重新计算分拣播种状态以及分拣明细的totalNum
                int matchedNum = 0, itemNum = 0;
                Map<String, Integer> itemNumMap = sortingDetails.stream()
                        .collect(Collectors.toMap(WmsKeyUtils::buildItemKey, WaveSortingDetail::getItemNum, Integer::sum));
                for (WaveSortingDetail detail : sortingDetails) {
                    matchedNum += detail.getMatchedNum();
                    itemNum += detail.getItemNum();
                    Integer totalNum = itemNumMap.remove(WmsKeyUtils.buildItemKey(detail));
                    if (totalNum != null) {
                        if (!detail.getTotalNum().equals(totalNum)) {
                            updateDetails.add(buildUpdateDetail(detail, totalNum));
                        }
                    } else if (detail.getTotalNum() > 0) {
                        updateDetails.add(buildUpdateDetail(detail, 0));
                    }
                }

                int matchedStatus = matchedNum > 0 ? (matchedNum >= itemNum ? WaveSorting.MATCHED_STATUS_OVER : WaveSorting.MATCHED_STATUS_ING) : WaveSorting.MATCHED_STATUS_NOT;
                if (!Objects.equal(matchedStatus, sorting.getMatchedStatus())) {
                    updates.add(buildUpdateSorting(sorting, matchedStatus, null));
                }
            }
        }

        if (!updates.isEmpty()) {
            waveSortingDao.batchUpdateStatus(staff, updates);
        }
        if (!updateDetails.isEmpty()) {
            waveSortingDao.batchUpdateDetails(staff, updateDetails);
        }
    }

    private WaveSortingDetail buildUpdateDetail(WaveSortingDetail detail, Integer totalNum) {
        WaveSortingDetail update = new WaveSortingDetail();
        update.setId(detail.getId());
        update.setTotalNum(totalNum);
        return update;
    }

    private WaveSorting buildUpdateSorting(WaveSorting sorting, Integer matchStatus, Integer enableStatus) {
        WaveSorting update = new WaveSorting();
        update.setId(sorting.getId());
        update.setMatchedStatus(matchStatus);
        update.setEnableStatus(enableStatus);
        return update;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int deleteDetailsAfterSendGoods(Staff staff, Long pickingId) {
        return deleteDetailsAfterSendGoods(staff, Lists.newArrayList(pickingId));
    }

    @Override
    @Transactional
    public int deleteDetailsAfterSendGoodsDefaultTx(Staff staff, List<Long> pickingIds) {
        return deleteDetailsAfterSendGoods(staff, pickingIds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int deleteDetailsAfterSendGoods(Staff staff, List<Long> pickingIds) {
        if (CollectionUtils.isEmpty(pickingIds)) {
            return 0;
        }
        List<Long> orderIds = waveSortingDao.queryAfterSendOrderIds(staff, pickingIds);
        deleteSortingByOrderIds(staff, orderIds);
        return orderIds.size();
    }

    @Override
    public int fixSortingDetails(Staff staff, Long sortingId) {
        return waveSortingDao.fixSortingDetails(staff, sortingId);
    }

    @Override
    public void fixTotalNum(Staff staff, List<Long> pickingIds) {
        if (CollectionUtils.isEmpty(pickingIds)) {
            return;
        }
        List<Long> sortingIds = waveSortingDao.queryNeedFixSortingIds(staff, pickingIds);
        if (CollectionUtils.isEmpty(sortingIds)) {
            return;
        }

        List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySortingIds(staff, sortingIds);
        Map<String, List<WaveSortingDetail>> keyDetailsMap = MapUtils.toMapList(details, new Function<WaveSortingDetail, String>() {
            @Override
            public String apply(WaveSortingDetail detail) {
                return detail.getSortingId() + "_" + detail.getSysItemId() + "_" + detail.getSysSkuId();
            }
        });

        List<WaveSortingDetail> updates = Lists.newArrayListWithCapacity(details.size());
        for (List<WaveSortingDetail> keyDetails : keyDetailsMap.values()) {
            if (keyDetails.size() <= 1) {
                continue;
            }
            int totalNum = 0;
            for (WaveSortingDetail keyDetail : keyDetails) {
                keyDetail.setTotalNum(0);
                totalNum += keyDetail.getItemNum();
            }
            keyDetails.get(0).setTotalNum(totalNum);

            for (WaveSortingDetail keyDetail : keyDetails) {
                WaveSortingDetail update = new WaveSortingDetail();
                update.setId(keyDetail.getId());
                update.setTotalNum(keyDetail.getTotalNum());
                updates.add(update);
            }
        }

        if (!updates.isEmpty()) {
            logger.debug(LogHelper.buildLog(staff, String.format("pickingIds:%s,需要修改的sortings：%s，修改明细数量：%s", pickingIds, sortingIds, updates.size())));
            waveSortingDao.batchUpdateDetails(staff, updates);
        }
    }

    @Override
    @Transactional
    public void subSectionPickClearSortingDetail(Staff staff, Long waveId, Map<Long, Integer> waveSortingDetailUpdateMaps) {
        if (!DataUtils.checkLongNotEmpty(waveId)
                || org.apache.commons.collections.MapUtils.isEmpty(waveSortingDetailUpdateMaps)) {
            return;
        }
        Set<Long> orderIds = waveSortingDetailUpdateMaps.keySet();
        List<WaveSortingDetail> sortingDetails = waveSortingDao.queryDetailsByOrderIds(staff, Lists.newArrayList(orderIds));
        List<WaveSortingDetail> updates = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(sortingDetails)) {
            for (WaveSortingDetail detail : sortingDetails) {
                Integer reduceNum = waveSortingDetailUpdateMaps.get(detail.getOrderId());
                if (reduceNum != null) {
                    WaveSortingDetail update = new WaveSortingDetail();
                    update.setId(detail.getId());
                    update.setPickedNum(DataUtils.getZeroIfDefaultI(detail.getPickedNum() - reduceNum));
                    updates.add(update);
                }
            }
            if (CollectionUtils.isNotEmpty(updates)) {
                waveSortingDao.batchUpdateDetails(staff, updates);
            }
        }

        // 删除拣选员
        Map<String, Object> params = Maps.newHashMap();
        params.put("waveIds", Lists.newArrayList(waveId));
        params.put("pickerIds", Lists.newArrayList(staff.getId()));
        List<WavePicker> wavePickers = wavePickerDao.queryByCondition(staff, params);
        List<WavePicker> updateWavePickers = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(wavePickers)) {
            for (WavePicker wavePicker : wavePickers) {
                WavePicker update = new WavePicker();
                update.setId(wavePicker.getId());
                update.setWaveId(wavePicker.getWaveId());
                update.setEnableStatus(0);
                updateWavePickers.add(update);
            }
            if (CollectionUtils.isNotEmpty(updateWavePickers)) {
                wavePickerDao.batchUpdate(staff, updateWavePickers);
            }
        }
        deleteSubSectionPicker(staff, waveId);

    }

    @Override
    public void deleteSubSectionPicker(Staff staff, Long waveId) {
        if (!DataUtils.checkLongNotEmpty(waveId)) {
            return;
        }

        // 删除正在拣选的分段拣选人
        WavePicking wavePicking = wavePickingDao.getByWaveId(staff, waveId);
        String subSectionPickerIds = wavePicking.getSubSectionPickerIds();
        if (StringUtils.isEmpty(subSectionPickerIds)) {
            return;
        }
        List<String> pickerIdList = new ArrayList<>(Arrays.asList(subSectionPickerIds.split(",")));
        pickerIdList.remove(staff.getId().toString());
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "删除分段拣选人：" + Strings.join(",", pickerIdList)));
        }
        WavePicking update = new WavePicking();
        update.setId(wavePicking.getId());
        update.setSubSectionPickerIds(Strings.join(",", pickerIdList));
        wavePickingDao.update(staff, update);
    }

    @Override
    public void clearSortingDetails(Staff staff, Long pickingId) {
        if (DataUtils.checkLongNotEmpty(pickingId)) {
            List<WaveSorting> waveSortings = waveSortingDao.queryByPickingId(staff, pickingId, null, null);
            if (CollectionUtils.isEmpty(waveSortings)) return;

            List<Long> sortingIds = waveSortings.stream().map(WaveSorting::getId).collect(Collectors.toList());
            List<WaveSortingDetail> sortingDetails = waveSortingDao.queryDetailsBySortingIds(staff, sortingIds);
            List<WaveSortingDetail> updates = Lists.newArrayList();
            for (WaveSortingDetail detail : sortingDetails) {
                WaveSortingDetail update = new WaveSortingDetail();
                update.setId(detail.getId());
                update.setPickedNum(0);
                update.setGoodsSectionStr("");
                update.setShortageNum(0);
                updates.add(update);
            }

            if (CollectionUtils.isNotEmpty(updates)) {
                waveSortingDao.batchUpdateDetails(staff, updates);
            }
        }
    }

    @Override
    public void updateDetails(Staff staff, List<WaveSortingDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        waveSortingDao.batchUpdateDetails(staff, details);
    }

    @Override
    @Transactional
    public void changeSortingSids(Staff staff, Map<Long, List<Long>> newMergeSidOldSidsMap) {
        List<WaveSorting> updates = Lists.newArrayList();
        List<WaveUniqueCode> codeUpdates = Lists.newArrayList();
        WaveConfig waveConfig = waveConfigService.get(staff);
        boolean openWaveUniqueCode = Objects.equal(waveConfig.getOpenWaveUniqueCode(), 1);
        boolean openOrderUniqueCode = WaveUtils.openOrderUniqueCode(staff);
        boolean openItemUniqueCode = uniqueCodeBaseService.judgeOpenItemUniqueCode(staff);
        List<WaveUniqueCode> changeCodeTypeCodes = Lists.newArrayList();

        for (Map.Entry<Long, List<Long>> entry : newMergeSidOldSidsMap.entrySet()) {
            List<WaveSorting> waveSortings = waveSortingDao.queryBySids(staff, entry.getValue());
            if (CollectionUtils.isNotEmpty(waveSortings)) {
                for (WaveSorting waveSorting : waveSortings) {
                    if (!waveSorting.getSid().equals(entry.getKey())) {
                        WaveSorting update = new WaveSorting();
                        update.setId(waveSorting.getId());
                        update.setSid(entry.getKey());
                        updates.add(update);
                    }
                }
            }

            List<WaveUniqueCode> waveUniqueCodes = openWaveUniqueCode ? waveUniqueCodeDao.queryByKeys(staff, null, entry.getValue(), null) : null;
            List<WaveUniqueCode> codes = mergeCodes(waveUniqueCodes, queryUniqueCodes(staff, entry.getValue(), openOrderUniqueCode, openItemUniqueCode));
            if (CollectionUtils.isNotEmpty(codes)) {
                for (WaveUniqueCode waveUniqueCode : codes) {
                    if (!waveUniqueCode.getSid().equals(entry.getKey())) {
                        WaveUniqueCode update = new WaveUniqueCode();
                        update.setId(waveUniqueCode.getId());
                        update.setSid(entry.getKey());
                        codeUpdates.add(update);

                        WaveUniqueCode changeCodeTypeCode = new WaveUniqueCode();
                        changeCodeTypeCode.setUniqueCode(waveUniqueCode.getUniqueCode());
                        changeCodeTypeCodes.add(changeCodeTypeCode);
                    }
                }
            }
        }

        if (!updates.isEmpty()) {
            waveSortingDao.batchUpdateStatus(staff, updates);
        }
        if (!codeUpdates.isEmpty()) {
            waveUniqueCodeDao.batchUpdate(staff, codeUpdates);
        }

        // 异步更新单多类型
        eventCenter.fireEvent(this, new EventInfo("unique.code.update.code.type").setArgs(new Object[]{staff, changeCodeTypeCodes}), null);
    }

    private List<WaveUniqueCode> mergeCodes(List<WaveUniqueCode> waveUniqueCodes, List<WaveUniqueCode> orderUniqueCodes) {
        if (CollectionUtils.isEmpty(waveUniqueCodes)
                && CollectionUtils.isEmpty(orderUniqueCodes)) {
            return null;
        }
        List<WaveUniqueCode> codes = new ArrayList<>();
        if (!CollectionUtils.isEmpty(waveUniqueCodes)) {
            codes.addAll(waveUniqueCodes);
        }
        if (!CollectionUtils.isEmpty(orderUniqueCodes)) {
            codes.addAll(orderUniqueCodes);
        }
        return codes;
    }

    private List<WaveUniqueCode> queryUniqueCodes(Staff staff, List<Long> sids, boolean openOrderUniqueCode, boolean openItemUniqueCode) {
        if ((!openOrderUniqueCode && !openItemUniqueCode) || CollectionUtils.isEmpty(sids)) {
            return new ArrayList<>();
        }
        return waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, new ItemUniqueCodeQueryParams().setSids(sids.stream().distinct().map(String::valueOf).collect(Collectors.toList())));
    }

    @Override
    public WavePicking queryWavePickingBySid(Staff staff, Long sid) {
        List<WaveSorting> waveSortings = waveSortingDao.queryBySids(staff, Lists.newArrayList(sid));
        if (CollectionUtils.isNotEmpty(waveSortings)) {
            //防止有多条数据,取最新的
            waveSortings.sort((t1, t2) -> t2.getId().compareTo(t1.getId()));
            WavePicking wavePicking = wavePickingDao.queryById(staff, waveSortings.get(0).getPickingId());
            return wavePicking;
        }
        return null;
    }

    @Override
    public List<WaveSortingDetail> queryGroupItemSortingDetails(Staff staff, WaveSortingParam param) {
        Assert.notNull(param.getWarehouseId(), "仓库id不能为空！");
        if (CollectionUtils.isEmpty(param.getSysItemIds())) {
            return Collections.emptyList();
        }

        return waveSortingDao.queryGroupItemSortingDetails(staff, param);
    }

    /**
     * 根据pickingId查询
     *
     * @param staff     公司
     * @param pickingId 波次拣货Id
     * @param status    状态
     * @param page      分页
     */
    @Override
    public List<WaveSorting> queryByPickingId(Staff staff, Long pickingId, Integer status, Page page) {
        return waveSortingDao.queryByPickingId(staff, pickingId, status, page);
    }

    /**
     * 根据pickingIds查询
     */
    @Override
    public List<WaveSorting> queryByPickingIds(Staff staff, List<Long> pickingIds) {
        return waveSortingDao.queryByPickingIds(staff, pickingIds);
    }

    @Override
    public List<WaveSorting> querySortingBySids(Staff staff, List<Long> sids) {
        return waveSortingDao.queryBySids(staff, sids);
    }

    @Override
    public List<WaveSortingDetail> queryDetailsWithWaveId(Staff staff, List<Long> sids) {
        return waveSortingDao.queryDetailsWithWaveId(staff, sids);
    }

    @Override
    public void deleteWaveSortingByPickingId(Staff staff, Long pickingId) {
        waveSortingDao.deleteDetailsBySids(staff, null, pickingId);
        waveSortingDao.deleteSortingBySid(staff, null, pickingId);
    }

    @Override
    public void batchUpdateOuterId(Staff staff, List<? extends Order> orders) {
        List<WaveSortingDetail> updateDetails = Lists.newArrayListWithCapacity(orders.size());
        List<WaveUniqueCode> updateUniqueCodes = Lists.newArrayListWithCapacity(orders.size());
        for (Order order : orders) {
            if (StringUtils.isEmpty(order.getSysOuterId())) {
                continue;
            }
            WaveSortingDetail detail = new WaveSortingDetail();
            detail.setOuterId(order.getSysOuterId());
            detail.setOrderId(order.getId());
            updateDetails.add(detail);

            WaveUniqueCode updateUniqueCode = new WaveUniqueCode();
            updateUniqueCode.setOrderId(order.getId());
            updateUniqueCode.setOuterId(order.getSysOuterId());
            updateUniqueCodes.add(updateUniqueCode);
        }

        waveSortingDao.batchUpdateOuterId(staff, updateDetails);
        waveUniqueCodeDao.batchUpdateOuterIdByOrderId(staff, updateUniqueCodes);
    }

    @Override
    public void updateWaveSortingOver(Staff staff, List<WaveSorting> waveSortings) {
        List<WaveSorting> updates = Lists.newArrayListWithCapacity(waveSortings.size());
        for (WaveSorting waveSorting : waveSortings) {
            WaveSorting update = new WaveSorting();
            update.setPostStatus(WaveSorting.POST_STATUS_OVER);
            update.setId(waveSorting.getId());
            updates.add(update);
        }

        waveSortingDao.batchUpdateStatus(staff, updates);
    }

    @Override
    public List<WaveSorting> queryWaveSortingByIds(Staff staff, List<Long> ids) {
        return waveSortingDao.queryByIds(staff, ids);
    }

    @Override
    public PageListBase<WaveSeedLog> queryPageList(Staff staff, WaveSeedLogParam param, Page page) {
        PageList<WaveSeedLog> pageList = new PageList<>();
        long count = waveSeedLogDao.queryCount(staff, param);
        pageList.setTotal(count);
        pageList.setPage(page);
        if (count > page.getStartRow()) {
            List<WaveSeedLog> waveSeedLogs = waveSeedLogDao.queryPageList(staff, param, page);
            pageList.setList(waveSeedLogs);
        } else {
            pageList.setList(new ArrayList<>());
        }
        return pageList;
    }

    @Override
    public List<WaveSorting> queryAllStatusWaveSortingsByWaveId(Staff staff, Long waveId) {
        WavePicking picking = wavePickingDao.getByWaveId(staff, waveId);
        Assert.notNull(picking, "该波次未拣选");

        List<WaveSorting> sortings = waveSortingDao.queryAllStatusByPickingIds(staff, Lists.newArrayList(picking.getId()));
        if (CollectionUtils.isEmpty(sortings)) {
            return new ArrayList<>();
        }
        List<Long> sortingIds = sortings.stream().map(WaveSorting::getId).collect(Collectors.toList());

        List<WaveSortingDetail> details = waveSortingDao.queryDetailsAllStatusBySortingId(staff, sortingIds);
        Map<Long, List<WaveSortingDetail>> sortingIdDetailsMap = MapUtils.toMapList(details, new Function<WaveSortingDetail, Long>() {
            @Override
            public Long apply(WaveSortingDetail detail) {
                return detail.getSortingId();
            }
        });
        for (WaveSorting sorting : sortings) {
            List<WaveSortingDetail> sortingDetails = sortingIdDetailsMap.get(sorting.getId());
            if (CollectionUtils.isNotEmpty(sortingDetails)) {
                for (WaveSortingDetail waveSortingDetail : details) {
                    waveSortingDetail.setSid(sorting.getSid());
                }
            }
            sorting.setDetails(sortingDetails);
        }
        return sortings;
    }

    @Override
    public List<WaveSorting> queryWaveSortingsByWaveIdForOpen(Staff staff, Long waveId) {
        WavePicking picking = wavePickingDao.getByWaveId(staff, waveId);
        if (picking == null) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("波次%s未拣选", waveId)));
            }
            return new ArrayList<>();
        }

        List<WaveSorting> sortings = waveSortingDao.querySortingByPickIdsForOpen(staff, Lists.newArrayList(picking.getId()), null, null);
        List<Long> sortingIds = Lists.newArrayListWithCapacity(sortings.size());
        for (WaveSorting sorting : sortings) {
            sortingIds.add(sorting.getId());
        }
        if (CollectionUtils.isEmpty(sortingIds)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("波次%s,sortingIds为空", waveId)));
            }
            return new ArrayList<>();
        }
        List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySortingIdsForOpen(staff, sortingIds, null);
        Map<String, DmjItem> itemKeyMap = waveHelpBusiness.getDetailsItemSkuMap(staff, details);
        waveHelpBusiness.fillSortingDetailMultiCodes(staff, details);
        Map<Long, List<WaveSortingDetail>> sortingIdDetailsMap = MapUtils.toMapList(details, new Function<WaveSortingDetail, Long>() {
            @Override
            public Long apply(WaveSortingDetail detail) {
                return detail.getSortingId();
            }
        });
        for (WaveSorting sorting : sortings) {
            List<WaveSortingDetail> sortingDetails = sortingIdDetailsMap.get(sorting.getId());
            if (CollectionUtils.isNotEmpty(sortingDetails)) {
                for (WaveSortingDetail waveSortingDetail : sortingDetails) {
                    waveSortingDetail.setSid(sorting.getSid());
                    DmjItem item = itemKeyMap.get(WaveHelpBusiness.buildItemKey(waveSortingDetail));
                    if (item == null) {
                        continue;
                    }
                    waveSortingDetail.setTitle(item.getTitle());
                    if (item instanceof DmjSku) {
                        DmjSku sku = (DmjSku) item;
                        waveSortingDetail.setPropertiesName(sku.getPropertiesName());
                    }
                }
            }
            sorting.setDetails(sortingDetails);
        }
        return sortings;
    }

    /**
     * 简洁模式判断绑定订单的唯一码是否全部到齐
     * @param staff
     * @param bindWaveUniqueCode
     * @return
     */
    private boolean needGeneratePosition(Staff staff, WaveUniqueCode bindWaveUniqueCode, WavePickingParam param) {
        if (param.isOpenSeedSimpleStyle() && bindWaveUniqueCode != null && DataUtils.checkLongNotEmpty(bindWaveUniqueCode.getSid())) {
            fillMoveStockCodes4Seed(staff, bindWaveUniqueCode, param, true);
            return param.isAllReceive();
        }
        return true;
    }

    private void fillMoveStockCodes4Seed(Staff staff, WaveUniqueCode bindWaveUniqueCode, WavePickingParam param, boolean fillAllReceive) {
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("播种填充移动库存唯一码, sid:【%s】, fillAllReceive:【%s】", bindWaveUniqueCode.getSid(), fillAllReceive)));
        }
        if (!DataUtils.checkLongNotEmpty(bindWaveUniqueCode.getSid())) {
            return;
        }
        List<WaveUniqueCode> waveUniqueCodes = itemUniqueCodeService.queryItemUniqueCodeByCondition(staff,
                new ItemUniqueCodeQueryParams().setSids(Lists.newArrayList(bindWaveUniqueCode.getSid().toString())).setType(2)
                        .setNeedCategroyAndCatName(false)
                        .setNeedRelationMsg(false));
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            if (fillAllReceive) {
                param.setAllReceive(true);
            }
            return;
        }
        boolean allReceive = waveUniqueCodes.stream()
                .filter(code -> OrderUniqueCodeUtils.isItemUniqueCode(code) && Objects.equal(0, code.getStockStatus()) && !Objects.equal(bindWaveUniqueCode.getUniqueCode(), code.getUniqueCode()))
                // 缺库存的商品唯一码都收货了(不包含当前扫的唯一码，当前扫的后面收货)
                .allMatch(code -> Objects.equal(OrderUniqueCodeStatusEnum.IN.getType(), code.getStatus()));
        if (fillAllReceive) {
            param.setAllReceive(allReceive);
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("播种填充移动库存唯一码, allReceive:【%s】", allReceive)));
        }
        if (!allReceive && fillAllReceive) {
            return;
        }
        param.getMoveStockUniqueCodes().addAll(waveUniqueCodes.stream().filter(w -> Objects.equal(w.getStockPosition(), UniqueCodeStockPositionEnum.WAREHOUSING_AREA.getType()) || (param.isOpenSeedSimpleStyle() && Objects.equal(bindWaveUniqueCode.getUniqueCode(), w.getUniqueCode()))).collect(Collectors.toList()));
        param.getMoveStockUniqueCodes().forEach(c -> c.setWaveId(param.getWaveId()));
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("播种填充移动库存唯一码, 唯一码数量:【%s】", param.getMoveStockUniqueCodes().size())));
        }
    }

    /**
     * 简洁模式轮播外采商品唯一码未到齐时分配虚拟位置号
     * @param staff
     * @param detail
     */
    private void dealVirtualPosition(Staff staff, WaveSortingDetail detail, Long waveId) {
        List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradeByWaveId(staff, waveId);
        if (CollectionUtils.isEmpty(waveTrades)) {
            return;
        }
        try {
            // 只获取未完结的订单，过滤掉已踢出和已经完结
            waveTrades = waveTrades.stream().filter(waveTrade -> Objects.equal(WaveTrade.TRADE_WAVE_STATUS_IN, waveTrade.getTradeWaveStatus())).collect(Collectors.toList());
            Map<Long, WaveTrade> tradeMap = waveTrades.stream().collect(Collectors.toMap(WaveTrade::getSid, waveTrade -> waveTrade, (a, b) -> a));
            WaveTrade pointTrade = tradeMap.get(detail.getSid());
            if (pointTrade == null) {
                return;
            }
            if (DataUtils.checkLongNotEmpty(pointTrade.getPositionNo())) {
                detail.setPositionNo(pointTrade.getPositionNo());
                return;
            }
            List<Long> positions = waveTrades.stream().filter(waveTrade -> !Objects.equal(WaveSorting.MATCHED_STATUS_OVER, waveTrade.getMatchedStatus())).map(WaveTrade::getPositionNo).filter(positionNo -> positionNo != null && positionNo != -1).sorted(Long::compare).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(positions)) {
                detail.setPositionNo(1L);
                pointTrade.setPositionNo(1L);
                waveTradeDao.update(staff, pointTrade);
            } else {
                Long maxPosition = positions.get(positions.size() - 1);
                for (long i = 1L; i <= maxPosition; i++) {
                    if (!positions.contains(i)) {
                        detail.setPositionNo(i);
                        pointTrade.setPositionNo(i);
                        waveTradeDao.update(staff, pointTrade);
                        return;
                    }
                }
                detail.setPositionNo(maxPosition + 1);
                WaveTrade waveTrade = tradeMap.get(detail.getSid());
                waveTrade.setPositionNo(maxPosition + 1);
                waveTradeDao.update(staff, waveTrade);
            }
        } catch (Exception ex) {
            logger.debug("播种打印简洁模式分配虚拟位置号失败！");
        }
    }

    /**
     * 波次打印简洁模式外采商品唯一码全部到齐时清空虚拟位置号
     * @param staff
     * @param detail
     */
    private void clearVirtualPosition(Staff staff, WaveSortingDetail detail, Long waveId) {
        WaveTrade waveTrade = waveTradeDao.queryByWaveIdAndSid(staff, waveId, detail.getSid());
        if (waveTrade == null) {
            return;
        }
        waveTrade.setPositionNo(-1L);
        waveTradeDao.update(staff, waveTrade);
    }



    @Override
    public List<WaveSortingDetail> queryDetailsByWaveId(Staff staff, List<Long> waveIds, List<Long> pickingIds, List<Integer> pickAndCheckList) {
        return waveSortingDao.queryDetailsByWaveId(staff, waveIds, pickingIds, pickAndCheckList);
    }
}

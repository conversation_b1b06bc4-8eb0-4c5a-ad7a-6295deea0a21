package com.raycloud.dmj.business.wave.split;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.*;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.wave.*;
import com.raycloud.dmj.domain.ERPLock;
import com.raycloud.dmj.business.wave.LogisticsWaveRelationBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.enums.WaveTypeEnum;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.params.UpdateOutsidPoolParams;
import com.raycloud.dmj.domain.trades.split.*;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.params.OrderUniqueCodeSplitParams;
import com.raycloud.dmj.domain.wave.utils.*;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.domain.wave.utils.OrderUniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.UniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.enums.WaveIdRecordEnum;
import com.raycloud.dmj.services.WaveCommonConstants;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.pt.IOutSidPoolDuubo;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.utils.*;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.ec.api.*;
import lombok.Data;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.beans.BeansException;
import org.springframework.context.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单唯一码拆分业务
 */
@Service
public class OrderUniqueCodeNewSplitBusiness implements ApplicationContextAware {

    private Logger logger = Logger.getLogger(this.getClass());

    public static final String UNIQUE_CODE_ITEM_OUT_PRINT_LOCKPREFIX = "unique_code_item_out_print_";

    @Resource
    TradeLocalConfigurable tradeLocalConfig;
    @Resource
    private IOutSidPoolDuubo outSidPoolDuubo;
    @Resource(name = "tbTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    private IWaveUniqueCodeService waveUniqueCodeService;
    @Resource
    private IOrderUniqueCodeService orderUniqueCodeService;
    private OrderUniqueCodeNewSplitBusiness beanProxy;
    private ApplicationContext applicationContext;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    ILockService lockService;
    @Resource
    private WaveProgressBusiness waveProgressBusiness;
    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;
    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;


    private ITradeConfigService tradeConfigService;
    @Resource
    private LogisticsWaveRelationBusiness logisticsWaveRelationBusiness;

    @PostConstruct
    public void startup() {
        beanProxy = applicationContext.getBean(OrderUniqueCodeNewSplitBusiness.class);
    }

    @Transactional
    public WaveTradeSplitResult doSingleSplit(Staff staff, OrderUniqueCodeSplitParams params) {
        long t1, t2, t3;
        long start = System.currentTimeMillis();
        validate(staff, params);
        t1 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        UniqueCodeUtils.debugLog(staff, orderNumLogs(params), logger, "套件数据", 2);
        SplitAuditedNumResponse response = tradeServiceDubbo.splitAuditedNum(staff, buildRequest(staff, params));
        t2 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        SplitTradeRelation relation = handleAfter(staff, params, response);
        t3 = System.currentTimeMillis() - start;
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, "singleSplit耗时：t1= " + t1 + ",t2= " + t2 + ",t3 = " + t3));
        }
        WaveTradeSplitResult result= getResult(staff, relation, response);
        handleOutSid4UniqueCodeNewSplit(staff, Lists.newArrayList(result), params.isNeedChangeOutSid());
        handleTradeTags4UniqueCodeNewSplit(staff, result, params);
        return result;
    }

    private void handleTradeTags4UniqueCodeNewSplit(Staff staff, WaveTradeSplitResult result, OrderUniqueCodeSplitParams params) {
        if (!params.isNeedRecalculationTradeTags() && !params.isNeedChangeOutSid()) {
            return;
        }
        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        if (tradeConfig == null || StringUtils.isEmpty(tradeConfig.getChatConfigs())) {
            return;
        }
        Map<String, Object> chatConfigMap;
        try {
            chatConfigMap = JSONObject.parseObject(tradeConfig.getChatConfigs());
        } catch (Exception e) {
            if (logger.isInfoEnabled()) {
                logger.info(LogHelper.buildLog(staff, String.format("handleTradeTags4UniqueCodeNewSplit Json转换失败, message:[%s]", e.getMessage())));
            }
            return;
        }
        String splitRecalculationTag = chatConfigMap.getOrDefault(TradeConfigEnum.SPLIT_RECALCULATION_TAG.getConfigKey(), 0).toString();
        if (!Objects.equals(splitRecalculationTag, "1")) {
            return;
        }
        Long sid = params.isNeedChangeOutSid() ? result.getLeaveMainTrade().getSid() : result.getSplitSid();
        if (!DataUtils.checkLongNotEmpty(sid)) {
            return;
        }
        String handleSids = sid + "";
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, String.format("handleTradeTags4UniqueCodeNewSplit 开始重算订单标签,重算sid:[%s]", handleSids)));
        }
        TradeControllerParams tradeControllerParams = new TradeControllerParams();
        tradeControllerParams.setSids(handleSids);
        eventCenter.fireEvent(this, new EventInfo("trade.tag.manual.match").setArgs(new Object[]{staff, tradeControllerParams, 0}), null);
    }

    private void handleOutSid4UniqueCodeNewSplit(Staff staff, List<WaveTradeSplitResult> results, Boolean needChangeOutSid) {
        if (BooleanUtils.isNotTrue(needChangeOutSid) || CollectionUtils.isEmpty(results)) {
            return;
        }
        List<Pair<Long, Long>> handleSids = Lists.newArrayList();
        for (WaveTradeSplitResult result : results) {
            if (BooleanUtils.isNotTrue(result.getSuccess()) || result.getLeaveMainTrade() == null || !DataUtils.checkLongNotEmpty(result.getLeaveMainTrade().getSid()) || !DataUtils.checkLongNotEmpty(result.getSplitSid())) {
                continue;
            }
            handleSids.add(Pair.of(result.getLeaveMainTrade().getSid(), result.getSplitSid()));
        }
        handleOutSid4UniqueCodeBatchSplit(staff, handleSids);
    }

    private void handleOutSid4UniqueCodeBatchSplit(Staff staff, List<Pair<Long, Long>> handleSids) {
        if (CollectionUtils.isEmpty(handleSids)) {
            return;
        }
        Long[] sids = new Long[handleSids.size() << 1];
        int i = 0;
        for (Pair<Long, Long> handleSid : handleSids) {
            sids[i++] = handleSid.getLeft();
            sids[i++] = handleSid.getRight();
        }
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, false, true, sids);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        Map<Long, List<Trade>> tradeMap = trades.stream().collect(Collectors.groupingBy(t -> DataUtils.checkLongNotEmpty(t.getMergeSid()) ? t.getMergeSid() : t.getSid()));
        List<Trade> updateTrades = Lists.newArrayList();
        List<Trade> keepOutSidTrades = Lists.newArrayList();
        for (Pair<Long, Long> handleSid : handleSids) {
            // 拆出来的原单，有运单号
            List<Trade> mains = tradeMap.get(handleSid.getLeft());
            String outSid;
            if (CollectionUtils.isEmpty(mains) || StringUtils.isEmpty(outSid = getOutSid4UniqueCodeBatchSplit(mains))) {
                continue;
            }
            // 新单，没有运单号
            List<Trade> splits = tradeMap.get(handleSid.getRight());
            if (CollectionUtils.isEmpty(splits) || StringUtils.isNotEmpty(getOutSid4UniqueCodeBatchSplit(splits))) {
                continue;
            }

            for (Trade main : mains) {
                Trade updateMainTrade = new TbTrade();
                updateMainTrade.setSid(main.getSid());
                // 没有运单号
                updateMainTrade.setOutSid("");
                updateTrades.add(updateMainTrade);
            }

            for (Trade split : splits) {
                Trade updateSplitTrade = new TbTrade();
                updateSplitTrade.setSid(split.getSid());
                // 有运单号
                updateSplitTrade.setOutSid(outSid);
                updateTrades.add(updateSplitTrade);

                if (DataUtils.checkLongNotEmpty(split.getMergeSid()) && !com.google.common.base.Objects.equal(split.getSid(), split.getMergeSid())) {
                    continue;
                }

                // 只取主单
                split.setOutSid(outSid);
                keepOutSidTrades.add(split);
            }

            if (logger.isInfoEnabled()) {
                logger.info(LogHelper.buildLog(staff, String.format("handleOutSid4UniqueCodeBatchSplit 原单号:【%s】, 原单运单号:【%s】, 新单号:【%s】", handleSid.getLeft(), outSid, handleSid.getRight())));
            }
        }

        // 发送修改面单池的事件
        if (CollectionUtils.isNotEmpty(keepOutSidTrades)) {
            UpdateOutsidPoolParams params = WaveSplitUtils.buildUpdateOutsidPoolParams(staff, keepOutSidTrades);
            if (logger.isInfoEnabled()) {
                logger.info(LogHelper.buildLog(staff, String.format("修改面单池的事件参数:【%s】", JSON.toJSONString(params.getSidOutsidMap()))));
            }
            eventCenter.fireEvent(this, new EventInfo("update.outsid.pool").setArgs(new Object[]{params}), null);
        }

        if (CollectionUtils.isNotEmpty(updateTrades)) {
            tradeServiceDubbo.updateTrades4Wave(staff, updateTrades);
        }
    }

    private String getOutSid4UniqueCodeBatchSplit(List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return null;
        }
        List<Trade> subTrades = trades.stream().filter(t -> !DataUtils.checkLongNotEmpty(t.getMergeSid()) || com.google.common.base.Objects.equal(t.getSid(), t.getMergeSid())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subTrades)) {
            return null;
        }
        return subTrades.get(0).getOutSid();
    }

    private List<String> orderNumLogs(OrderUniqueCodeSplitParams params) {
        List<String> logs = new ArrayList<>();
        for (Order order : TradeUtils.getOrders4Trade(params.getSingleSplitParams().getTrade())) {
            if (order.isSuit()) {
                logs.add("suit本身" + order.getId() + "_" + order.getNum());
                for (Order detail : order.getSuits()) {
                    logs.add("明细" + detail.getId() + "_" + detail.getNum() + "_" + detail.getCombineId());
                }
            }
        }
        return logs;
    }

    @Transactional
    public WaveTradeSplitResult singleSplit(Staff staff, OrderUniqueCodeSplitParams params) {
        Assert.notNull(params.getSingleSplitParams().getTrade(), "订单不能为空！");
        return lockService.locks(
                Collections.singletonList(key2ERPLock(staff,UNIQUE_CODE_ITEM_OUT_PRINT_LOCKPREFIX + params.getSingleSplitParams().getTrade().getSid())),
                () -> doSingleSplit(staff, params));
    }

    public static ERPLock key2ERPLock(Staff staff, String lockKey) {
        return ERPLockUtils.createERPLock(staff.getDbNo(), staff.getCompanyId(), lockKey);
    }

    public List<WaveTradeSplitResult> multiSplit(Staff staff, OrderUniqueCodeSplitParams params) {
        OrderUniqueCodeSplitParams.multiSplitParams multiSplitParams = params.getMultiSplitParams();
        Map<Long, List<String>> paramsUniqueCodeMap = multiSplitParams.getParamsSplitUniqueCodeMap();
        Long[] sids = multiSplitParams.getSids();
        List<WaveTradeSplitResult> results = new ArrayList<>();
        for (Long sid : sids) {
            List<String> paramsCodes = paramsUniqueCodeMap.get(sid);
            if (CollectionUtils.isEmpty(paramsCodes)) {
                continue;
            }

            try {
                List<Trade> trades = tradeSearchService.queryBySidsNoFilter(staff, true, sid);
                List<WaveUniqueCode> allUniqueCodes = OrderUniqueCodeUtils.filterFinalGenerates(orderUniqueCodeService.queryBySid(staff, sid));
                OrderUniqueCodeSplitParams.SingleSplitParams splitParams = new OrderUniqueCodeSplitParams.SingleSplitParams();
                splitParams.setTrade(trades.get(0));
                splitParams.setAllUniqueCodes(allUniqueCodes);
                splitParams.setParamsUniqueCodes(allUniqueCodes.stream().filter(a -> paramsCodes.contains(a.getUniqueCode())).collect(Collectors.toList()));
                params.setSingleSplitParams(splitParams);
                results.add(beanProxy.singleSplit(staff, params));
                waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_WAVE_SPLIT_TRADE, params.getProgressData(), 1);
            } catch (Exception e) {
                results.add(WaveTradeSplitResult.buildFailure(sid, e.getMessage()));
            }
        }
        return results;
    }

    private WaveTradeSplitResult getResult(Staff staff, SplitTradeRelation relation, SplitAuditedNumResponse response) {
        WaveTradeSplitResult result = new WaveTradeSplitResult();
        if (relation != null) {
            WaveSorting originTrade = WaveUtils.fillWaveSortingWithTrade(staff, new WaveSorting(), relation.leaveMainTrade);
            result.setOriginTrade(originTrade);
            // 拆分出去的不需要打印的订单
            result.setSplitSid(relation.getSplitMainTrade().getSid());
            // 留下来的要打印的订单
            result.setOriginSid(originTrade.getSid());
            // 留下来的要打印的订单
            result.setLeaveMainTrade(relation.leaveMainTrade);
            result.setSuccess(true);
        } else {
            result.setSuccess(false);
            result.setMessage(response.getErrorMsg());
        }
        return result;
    }

    private SplitTradeRelation handleAfter(Staff staff, OrderUniqueCodeSplitParams params, SplitAuditedNumResponse response) {
        if (response.getSuccess() == null || !response.getSuccess()) {
            return null;
        }

        long t1, t2, t3, t4, t5, t6;
        long start = System.currentTimeMillis();
        if (logger.isInfoEnabled()) {
            UniqueCodeUtils.debugLog(staff, response, logger, "拆分返回数据", 2);
        }
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, response.getResultSids());
        t1 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        SplitTradeRelation relation = getRelation(trades, params, response);
        t2 = System.currentTimeMillis() - start;

        if (logger.isInfoEnabled()) {
            UniqueCodeUtils.debugLog(staff, "原单的主单：" + params.getSingleSplitParams().getParamsUniqueCodes().get(0).getSid() + " -> " + relation.getLeaveMainTrade().getSid() + " 拆分出的订单：" + relation.getSplitMainTrade().getSid() + ",queryBySidsContainMergeTradeResult:" + OrderUniqueCodeUtils.buildQueryBySidsContainMergeTradeResult(trades), logger, "handleAfter拆分成功:", 2);
        }

        // 处理运单号
        start = System.currentTimeMillis();
        handleOutSid(staff, params, relation);
        t3 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        // 处理唯一码
        handleOrderUniqueCode(staff, params, response, relation);
        t4 = System.currentTimeMillis() - start;

        // 处理唯一码其他
        start = System.currentTimeMillis();
        setTradeUnShelfTag(staff, params, response);
        t5 = System.currentTimeMillis() - start;
        //处理波次业务逻辑表
        createLogisticsWaveRelations(staff, relation);

        // 处理反审核：卡比例换商品和批量拆分根据配置来判断是否要反审核，反审核的应该是留下来的主单
        start = System.currentTimeMillis();
        if (params.isNeedUnAudit()) {
            // 验货出库页面的拆分
            if (Objects.equals(0, params.getPageSource())) {
                eventCenter.fireEvent(this, new EventInfo("wave.unaudit.trade").setArgs(new Object[]{staff, relation.getSplitMainTrade().getSid()}), null);
            } else {
                tradeServiceDubbo.cancelAudit(staff, OpEnum.AUDIT_UNDO_AUTO_UNIQUE_SPLIT, new Long[]{relation.getLeaveMainTrade().getSid()});
            }
        }
        t6 = System.currentTimeMillis() - start;
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, "handleAfter耗时：t1= " + t1 + ",t2= " + t2 + ",t3 = " + t3 + ",t4 = " + t4 + ",t5 = " + t5 + ",t6 = " + t6));
        }
        return relation;
    }

    private void createLogisticsWaveRelations(Staff staff, SplitTradeRelation relation) {
        List<Trade> trades = new ArrayList<>();
        trades.add(relation.splitMainTrade);
        trades.add(relation.leaveMainTrade);
        trades.addAll(relation.leaveTrades);
        trades.addAll(relation.splitTrades);
        if(CollectionUtils.isEmpty(trades)){
            logger.error(LogHelper.buildLog(staff,WaveCommonConstants.LOGISTICS_WAVE_RELATION_LOG_HEADER+"OrderUniqueCodeNewSplitBusiness createLogisticsWaveRelations relation invalid"));
            return;
        }
        Set<Long> sids = trades.stream().map(Trade::getSid).collect(Collectors.toSet());
        try{
            List<LogisticsWaveRelation> logisticsWaveRelations = logisticsWaveRelationBusiness.queryLogisticsWaveRelations(staff, sids, LogisticsWaveRelation.LogisticsType.TRADE, LogisticsWaveRelation.WaveTypeEnum.UNIQUE_CODE, LogisticsWaveRelation.WaveSubTypeEnum.ORDER_UNIQUE_CODE);
            if(CollectionUtils.isEmpty(logisticsWaveRelations)){
                logger.error(LogHelper.buildLog(staff,WaveCommonConstants.LOGISTICS_WAVE_RELATION_LOG_HEADER
                        +String.format("OrderUniqueCodeNewSplitBusiness invalid sids:%s", JSON.toJSONString(sids))));
                //补偿先插入，防止影响后续数据
                List<LogisticsWaveRelation> waveRelations=sids.stream().map(sid->{
                    LogisticsWaveRelation insert = new LogisticsWaveRelation();
                    insert.setCompanyId(staff.getCompanyId());
                    insert.setLogisticsType(LogisticsWaveRelation.LogisticsType.TRADE.getLogisticsType());
                    insert.setLogisticsId(sid);
                    insert.setWaveType( LogisticsWaveRelation.WaveTypeEnum.UNIQUE_CODE.getWaveType());
                    insert.setWaveSubType(LogisticsWaveRelation.WaveSubTypeEnum.ORDER_UNIQUE_CODE.getWaveSubType());
                    insert.setWaveId(WaveTypeEnum.ORDER_UNIQUE_CODE.getWaveId());
                    return insert;
                }).collect(Collectors.toList());
                logisticsWaveRelationBusiness.batchInsertRelations(staff,waveRelations);
            }else {
                Set<Long> exist = logisticsWaveRelations.stream().map(LogisticsWaveRelation::getLogisticsId).collect(Collectors.toSet());
                if(exist.size()!=logisticsWaveRelations.size()){
                    logger.error(LogHelper.buildLog(staff,WaveCommonConstants.LOGISTICS_WAVE_RELATION_LOG_HEADER+"logisticsWaveRelations 重复"));
                }
                Set<Long> insert = sids.stream().filter(sid -> !exist.contains(sid)).collect(Collectors.toSet());
                LogisticsWaveRelation logisticsWaveRelation = logisticsWaveRelations.get(0);
                logger.info(LogHelper.buildLog(staff,String.format("OrderUniqueCodeNewSplitBusiness logisticsWaveRelations:%s insert:%s exist:%s",JSON.toJSONString(logisticsWaveRelations),JSON.toJSONString(insert),JSON.toJSONString(exist))));
                if(insert.isEmpty()){
                    logger.info(LogHelper.buildLog(staff,"OrderUniqueCodeNewSplitBusiness insert is empty"));
                    return;
                }
                List<LogisticsWaveRelation> waveRelations=insert.stream().map(sid->{
                    LogisticsWaveRelation copyRelation = new LogisticsWaveRelation();
                    copyRelation.setCompanyId(logisticsWaveRelation.getCompanyId());
                    copyRelation.setLogisticsType(logisticsWaveRelation.getLogisticsType());
                    copyRelation.setLogisticsId(sid);
                    copyRelation.setWaveType(logisticsWaveRelation.getWaveType());
                    copyRelation.setWaveSubType(logisticsWaveRelation.getWaveSubType());
                    copyRelation.setWaveId(logisticsWaveRelation.getWaveId());
                    return copyRelation;
                }).collect(Collectors.toList());
                logisticsWaveRelationBusiness.batchInsertRelations(staff,waveRelations);
            }
        }catch (Exception e){
            logger.error(LogHelper.buildErrorLog(staff, e,WaveCommonConstants.LOGISTICS_WAVE_RELATION_LOG_HEADER +
                    "OrderUniqueCodeNewSplitBusiness createLogisticsWaveRelations fail. sids:"+JSON.toJSONString(sids)+"stackTrace:"+ Throwables.getStackTraceAsString(e)));
        }
    }

    private void validate(Staff staff, OrderUniqueCodeSplitParams params) {
        Assert.notEmpty(params.getSingleSplitParams().getAllUniqueCodes(), "allUniqueCodes不能为空！");
        Assert.notEmpty(params.getSingleSplitParams().getParamsUniqueCodes(), "已验唯一码不能为空！");

        List<WaveUniqueCode> cancelOrUnbindCodes = params.getSingleSplitParams().getParamsUniqueCodes().stream().filter(code -> Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.CANCEL.getType()) || !DataUtils.checkLongNotEmpty(code.getSid())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cancelOrUnbindCodes)) {
            throw new IllegalArgumentException("拆分失败：已验货的唯一码" + cancelOrUnbindCodes.get(0).getUniqueCode() + "已取消或者已解绑");
        }
        Map<Long, List<WaveUniqueCode>> orderIdGroup = params.getSingleSplitParams().getAllUniqueCodes().stream().collect(Collectors.groupingBy(WaveUniqueCode::getOrderId));
        for (Map.Entry<Long, List<WaveUniqueCode>> entry : orderIdGroup.entrySet()) {
            List<WaveUniqueCode> value = entry.getValue();
            if (value.stream().anyMatch(v -> Objects.equals(v.getStatus(), OrderUniqueCodeStatusEnum.CANCEL.getType()))
                    && value.stream().anyMatch(v -> !Objects.equals(v.getStatus(), OrderUniqueCodeStatusEnum.CANCEL.getType()))) {
                throw new IllegalArgumentException("拆分失败：相同商品的唯一码存在已取消和其他状态！" + entry.getKey());
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Data
    static class SplitOrderMethodParam {
        // 套件本身 + 套件明细
        Map<Long, Order> fullOrderMap;
        Map<Long, Long> fullOrderIdSidMap;
        // 生成了订单唯一码的orderId，包含套件本身的id
        List<Long> generatedCodeOrderIds;
        Map<Long, Long> paramsCodeGroupByOrderMap;
    }

    private SplitOrderMethodParam build(Staff staff, OrderUniqueCodeSplitParams params) {
        SplitOrderMethodParam param = new SplitOrderMethodParam();
        param.fullOrderMap = OrderUniqueCodeUtils.getFullTradeOrders(params.getSingleSplitParams().getTrade());
        param.fullOrderIdSidMap = param.fullOrderMap.values().stream().collect(Collectors.toMap(Order::getId, Order::getSid, (a, b) -> a));
        // 生成了唯一码的order,套件本身orderId也会在里面
        param.generatedCodeOrderIds = getGeneratedCodeOrderIds(params, param.fullOrderMap);
        param.paramsCodeGroupByOrderMap = params.getSingleSplitParams().getParamsUniqueCodes().stream().collect(Collectors.groupingBy(WaveUniqueCode::getOrderId, Collectors.counting()));

        if (logger.isInfoEnabled()) {
            UniqueCodeUtils.debugLog(staff, "fullOrderId：" + Strings.join(",", OrderUniqueCodeUtils.getFullOrderAndCombineId(param.fullOrderMap))
                    + ", paramsUniqueCodes：" + Strings.join(",", OrderUniqueCodeUtils.getUniqueCodeAndOrderIdsList(params.getSingleSplitParams().getParamsUniqueCodes()))
                    + ", sid：" + params.getSingleSplitParams().getTrade().getSid(), logger, "build数据", 2);
        }
        return param;
    }

    private List<Order> buildSplitOrders(Staff staff, OrderUniqueCodeSplitParams params) {
        SplitOrderMethodParam methodParam = build(staff, params);
        moveCancel2Params(staff, params, methodParam.fullOrderMap, methodParam.fullOrderIdSidMap, methodParam.generatedCodeOrderIds);
        List<Order> splitOrders = new ArrayList<>();
        for (Order order : TradeUtils.getOrders4Trade(params.getSingleSplitParams().getTrade())) {
            // 未生成唯一码的order处理，这里循环的是套件本身，不是套件明细
            if (!methodParam.generatedCodeOrderIds.contains(order.getId())) {
                if (noGenerateNeedOut(order, params.getSingleSplitParams().getTrade(), params.getSingleSplitParams().getParamsUniqueCodes(), methodParam.fullOrderIdSidMap)) {
                    // 套件本身
                    splitOrders.add(buildSplitOrder(order));
                    if (logger.isInfoEnabled()) {
                        logger.info(LogHelper.buildLog(staff, "buildSplitOrders：未生成唯一码需要拆分出 orderId:" + order.getId()));
                    }
                } else {
                    if (logger.isInfoEnabled()) {
                        logger.info(LogHelper.buildLog(staff, "buildSplitOrders：未生成唯一码需要保留在原单 orderId:" + order.getId()));
                    }
                }
            } else if (!order.isSuit()){
                splitOrders.add(buildSplitOrder(order, methodParam.paramsCodeGroupByOrderMap.containsKey(order.getId())
                        ? (order.getNum() - methodParam.paramsCodeGroupByOrderMap.get(order.getId()).intValue())
                        : order.getNum()));
                if (logger.isInfoEnabled()) {
                    logger.info(LogHelper.buildLog(staff, "buildSplitOrders：非套件生成了唯一码 orderId:" + order.getId()));
                }
            } else {
                // 套件明细和套件本身的比例，例如套件A，由1个b,2个c组成
                Map<Long, Integer> ratioMap = UniqueCodeUtils.getRatioMap(order);
                // 计算能留下来几个套件
                Map<Long, Integer> suitNumRatioMap = getCanLeaveSuitNum(methodParam, order, ratioMap);
                // 套件本身留下来的数量
                Integer suitNum = Collections.min(suitNumRatioMap.values());
                dealSuitMoreParams(staff, params, suitNum, ratioMap, order);
                // 套件本身拆分, 套件明细的信息不用传给交易
                splitOrders.add(buildSplitOrder(order, (order.getNum() - suitNum)));
            }
        }
        return splitOrders;
    }

    /**
     * 如果 com.raycloud.dmj.domain.wave.params.OrderUniqueCodeSplitParams.SingleSplitParams#paramsUniqueCodes
     * 没有成套，多出来的部分，应该要拆分出去，也就是从paramsUniqueCodes中删除
     * @param staff
     * @param params
     * @param suitNum 成套的数量
     */
    private void dealSuitMoreParams(Staff staff, OrderUniqueCodeSplitParams params, Integer suitNum, Map<Long, Integer> ratioMap, Order suit) {
        if (suitNum == null || suitNum <= 0) {
            return;
        }

        List<String> splitCodes = new ArrayList<>();
        for (Order suitDetail : suit.getSuits()) {
            // 应该要留下的数量
            Integer paramsCodeNum = ratioMap.get(suitDetail.getId()) * suitNum;
            List<String> paramsCodes = params.getSingleSplitParams().getParamsUniqueCodes().stream().filter(code -> Objects.equals(code.getOrderId(), suitDetail.getId())).map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
            if (logger.isInfoEnabled()) {
                logger.info(LogHelper.buildLog(staff,
                        "dealSuitMoreParams，成套数量：" + suitNum
                                + "，套件orderId：" + suit.getId()
                                + "，套件明细orderId："+ suitDetail.getId()
                                + "，套件明细比例：" + ratioMap.get(suitDetail.getId())
                                + "，应该要留下来的数量：" + paramsCodeNum
                                + "，该套件明细paramsCodes数量：" + paramsCodes.size()));
            }
            if (paramsCodes.size() <= paramsCodeNum) {
                continue;
            }
            splitCodes.addAll(paramsCodes.subList(paramsCodeNum, paramsCodes.size()));
        }

        if (CollectionUtils.isNotEmpty(splitCodes)) {
            params.getSingleSplitParams().getParamsUniqueCodes().removeIf(paramCode -> splitCodes.contains(paramCode.getUniqueCode()));
        }
    }

    private Map<Long, Integer> getCanLeaveSuitNum(SplitOrderMethodParam methodParam, Order order, Map<Long, Integer> ratioMap) {
        Map<Long, Integer> suitNumRatioMap = Maps.newHashMap();
        for (Order suitDetail : order.getSuits()) {
            Long intSummaryStatistics = methodParam.paramsCodeGroupByOrderMap.get(suitDetail.getId());
            if (intSummaryStatistics != null) {
                suitNumRatioMap.put(suitDetail.getId(), (methodParam.paramsCodeGroupByOrderMap.get(suitDetail.getId()).intValue() / ratioMap.get(suitDetail.getId())));
            } else if (methodParam.generatedCodeOrderIds.contains(suitDetail.getId())) {
                // 生成了唯一码，但是一件都没有验货，那么全部都留不下来
                suitNumRatioMap.put(suitDetail.getId(), 0);
            }
            // 未生成唯一码，说明都可以留下来，不用参与计算
        }
        return suitNumRatioMap;
    }

    private void moveCancel2Params(Staff staff, OrderUniqueCodeSplitParams params, Map<Long, Order> fullOrderMap, Map<Long, Long> fullOrderIdSidMap, List<Long> generatedCodeOrderIds) {
        List<Long> needSplitCombineId = getNeedSplitCombineId(params, fullOrderMap);
        // 需要留下来的已取消的唯一码
        List<WaveUniqueCode> leaveCancelUniqueCodes = new ArrayList<>();
        Set<Long> splitSids = getSplitSids(params.getSingleSplitParams().getParamsUniqueCodes(), fullOrderIdSidMap);
        List<String> paramsCodes = params.getSingleSplitParams().getParamsUniqueCodes().stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());

        for (WaveUniqueCode code : params.getSingleSplitParams().getAllUniqueCodes()) {
            if (!Objects.equals(OrderUniqueCodeStatusEnum.CANCEL.getType(), code.getStatus())) {
                continue;
            }
            Order order = fullOrderMap.get(code.getOrderId());
            // 如果order没有，说明这个唯一码是换商品之前的已取消唯一码，保留在原单好了
            if (order == null) {
                logger.info(LogHelper.buildLog(staff, "moveCancel2Params：已取消的唯一码：" + code.getUniqueCode() + " order不存在 留在原单，orderId:" + code.getOrderId()));
                leaveCancelUniqueCodes.add(code);
                continue;
            }
            // 如果是套件中的唯一码，并且套件中有唯一码是已验货，那么把这个已取消的唯一码带着一起
            if (order.isSuitSingle()) {
                if (needSplitCombineId.contains(order.getCombineId())) {
                    logger.info(LogHelper.buildLog(staff, "moveCancel2Params：已取消的唯一码：" + code.getUniqueCode() + " 跟随套件中已验货的唯一码，combineId:" + order.getCombineId()));
                    leaveCancelUniqueCodes.add(code);
                }
                continue;
            }
            // 如果是合单，本单中有唯一码是已验货，那么把这个已取消的唯一码带着一起
            if (TradeUtils.isMerge(params.getSingleSplitParams().getTrade())) {
                if (splitSids.contains(order.getSid())) {
                    logger.info(LogHelper.buildLog(staff, "moveCancel2Params：已取消的唯一码：" + code.getUniqueCode() + " 跟随合单中已验货的唯一码，sid:" + order.getSid()));
                    leaveCancelUniqueCodes.add(code);
                }
                continue;
            }
            // 非套件明细、也非合单，直接带着一起
            leaveCancelUniqueCodes.add(code);
            if (logger.isInfoEnabled()) {
                logger.info(LogHelper.buildLog(staff, "moveCancel2Params：已取消的唯一码：" + code.getUniqueCode() + " 跟已验货的唯一码"));
            }
        }

        if (CollectionUtils.isNotEmpty(leaveCancelUniqueCodes)) {
            params.getSingleSplitParams().getLeaveCancelUniqueCodes().addAll(leaveCancelUniqueCodes);
            List<String> leaveCancelCodes = leaveCancelUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
            params.getSingleSplitParams().getAllUniqueCodes().removeIf(c -> leaveCancelCodes.contains(c.getUniqueCode()));
        }
    }

    private List<Long> getNeedSplitCombineId(OrderUniqueCodeSplitParams params, Map<Long, Order> fullOrderMap) {
        List<Long> needSplitCombineId = new ArrayList<>();
        for (WaveUniqueCode code : params.getSingleSplitParams().getParamsUniqueCodes()) {
            Order order = fullOrderMap.get(code.getOrderId());
            if (order.isSuitSingle()) {
                needSplitCombineId.add(order.getCombineId());
            }
        }
        return needSplitCombineId;
    }

    private List<Long> getGeneratedCodeOrderIds(OrderUniqueCodeSplitParams params, Map<Long, Order> fullOrderMap) {
        List<Long> generatedCodeOrderIds = params.getSingleSplitParams().getAllUniqueCodes().stream()
                .filter(p -> !Objects.equals(p.getStatus(), OrderUniqueCodeStatusEnum.CANCEL.getType()))
                .map(WaveUniqueCode::getOrderId).distinct().collect(Collectors.toList());
        List<Long> generatedCodeCombineIds = new ArrayList<>();
        for (Long generatedCodeOrderId : generatedCodeOrderIds) {
            Order order = fullOrderMap.get(generatedCodeOrderId);
            if (order.isSuitSingle()) {
                generatedCodeCombineIds.add(order.getCombineId());
            }
        }
        generatedCodeOrderIds.addAll(generatedCodeCombineIds);
        return generatedCodeOrderIds;
    }

    private static Order buildSplitOrder(Order order) {
        return buildSplitOrder(order, order.getNum());
    }

    private static Order buildSplitOrder(Order order, Integer splitNum) {
        Order outOrder = new Order();
        outOrder.setId(order.getId());
        outOrder.setSourceId(order.getId());
        outOrder.setNum(order.getNum());
        outOrder.setType(order.getType());
        outOrder.setSplitNum(splitNum);
        outOrder.setCombineId(order.getCombineId());
        return outOrder;
    }

    private static boolean noGenerateNeedOut(Order currentOrder, Trade trade, List<WaveUniqueCode> paramsUniqueCodes, Map<Long, Long> orderIdSidMap) {
        // 非合单的话应该要保留在原单
        if (!TradeUtils.isMerge(trade)) {
            return false;
        }

        // 这里不能取唯一码中的订单号，应该唯一码中记录的是主单的
        Set<Long> splitSids = getSplitSids(paramsUniqueCodes, orderIdSidMap);
        // 当前子单所属的订单有唯一码要拆分，当前子单要保留在原单
        if (splitSids.contains(currentOrder.getSid())) {
            return false;
        }
        return true;
    }

    private static Set<Long> getSplitSids(List<WaveUniqueCode> paramsUniqueCodes, Map<Long, Long> orderIdSidMap) {
        Set<Long> splitSids = Sets.newHashSet();
        for (WaveUniqueCode code : paramsUniqueCodes) {
            if (orderIdSidMap.get(code.getOrderId()) != null) {
                splitSids.add(orderIdSidMap.get(code.getOrderId()));
            }
        }
        return splitSids;
    }

    private void handleOrderUniqueCode(Staff staff, OrderUniqueCodeSplitParams params, SplitAuditedNumResponse response, SplitTradeRelation relation) {
        // 保留在原单中的唯一码
        List<WaveUniqueCode> paramsUniqueCodes = params.getSingleSplitParams().getParamsUniqueCodes();
        List<String> leaveCodes = paramsUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
        // 拆分出的唯一码
        List<WaveUniqueCode> splitCodes = params.getSingleSplitParams().getAllUniqueCodes().stream().filter(waveUniqueCode -> !leaveCodes.contains(waveUniqueCode.getUniqueCode())).collect(Collectors.toList());

        Long leaveMainTradeSid = relation.leaveMainTrade.getSid();
        Long splitMainTradeSid = relation.splitMainTrade.getSid();
        params.getSingleSplitParams().setLeaveMainSid(leaveMainTradeSid);
        params.getSingleSplitParams().setSplitMainSid(splitMainTradeSid);

        List<WaveUniqueCode> updates = new ArrayList<>();
        for (WaveUniqueCode code : paramsUniqueCodes) {
            if (Objects.equals(code.getSid(), leaveMainTradeSid)) {
                continue;
            }
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setSid(leaveMainTradeSid);
            updates.add(update);
        }

        if (CollectionUtils.isNotEmpty(params.getSingleSplitParams().getLeaveCancelUniqueCodes())) {
            for (WaveUniqueCode leave : params.getSingleSplitParams().getLeaveCancelUniqueCodes()) {
                if (Objects.equals(leave.getSid(), leaveMainTradeSid)) {
                    continue;
                }
                WaveUniqueCode update = new WaveUniqueCode();
                update.setId(leave.getId());
                update.setSid(leaveMainTradeSid);
                updates.add(update);
            }
        }

        Map<Long, Long> sourceToSplitOrderId = response.getSourceToSplitOrderId();
        for (WaveUniqueCode splitCode : splitCodes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(splitCode.getId());
            update.setSid(splitMainTradeSid);
            if (sourceToSplitOrderId.containsKey(splitCode.getOrderId())) {
                update.setOrderId(sourceToSplitOrderId.get(splitCode.getOrderId()));
            }
            updates.add(update);
        }

        if (CollectionUtils.isNotEmpty(updates)) {
            waveUniqueCodeService.batchUpdate(staff, updates);
        }
    }

    protected void handleOutSid(Staff staff, OrderUniqueCodeSplitParams params, SplitTradeRelation relation) {
        // 非合单不需要处理运单号，保留在原单中
        if (!TradeUtils.isMerge(params.getSingleSplitParams().getTrade())) {
            return;
        }
        String leaveMainOutSid = getOutSid(relation.leaveMainTrade, relation.leaveTrades);
        String splitMainOutSid = getOutSid(relation.splitMainTrade, relation.splitTrades);
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, String.format("唯一码拆分运单号处理，splitMainTrade=%s, splitMainOutSid=%s, leaveMainTrade%s, leaveMainOutSid=%s", relation.splitMainTrade.getSid(), splitMainOutSid, relation.leaveMainTrade.getSid(), leaveMainOutSid)));
        }

        List<Trade> updateTrades = new ArrayList<>();
        WaveSplitUtils.buildUpdateTrade(staff, updateTrades, relation.splitTrades, "");
        if (StringUtils.isEmpty(leaveMainOutSid)) {
            leaveMainOutSid = splitMainOutSid;
        }

        WaveSplitUtils.buildUpdateTrade(staff,updateTrades, relation.leaveTrades, leaveMainOutSid);
        UpdateOutsidPoolParams poolParams = WaveSplitUtils.buildUpdateOutsidPoolParams(staff, relation.leaveTrades);
        Map<Long, String> sidOutSidMap = poolParams.getSidOutsidMap();
        if (MapUtils.isNotEmpty(sidOutSidMap)) {
            outSidPoolDuubo.updateOutSidPool(staff, sidOutSidMap);
        }
        tradeServiceDubbo.updateTrades4Wave(staff, updateTrades);
    }

    @Data
    static class SplitTradeRelation {
        // 拆分出的订单（如果是合单，就是主单）
        Trade splitMainTrade;
        //拆分出来的订单，拆分出来的订单不一定是新增的
        List<Trade> splitTrades = new ArrayList<>();

        //留下来的订单（如果是合单，就是主单）
        Trade leaveMainTrade;
        //留下来的订单
        List<Trade> leaveTrades = new ArrayList<>();
    }

    private SplitTradeRelation getRelation(List<Trade> trades, OrderUniqueCodeSplitParams params, SplitAuditedNumResponse response) {
        SplitTradeRelation relation = new SplitTradeRelation();
        // 留下来的主单号
        Long leaveMainTradeSid = getLeaveMainTradeSid(params.getSingleSplitParams().getParamsUniqueCodes().get(0).getOrderId(), trades);
        // 留下来的主单
        relation.leaveMainTrade = trades.stream().filter(t -> Objects.equals(t.getSid(), leaveMainTradeSid)).collect(Collectors.toList()).get(0);
        relation.leaveTrades = trades.stream().filter(t -> Objects.equals(t.getSid(), leaveMainTradeSid) || Objects.equals(t.getMergeSid(), leaveMainTradeSid)).collect(Collectors.toList());

        // 拆分出去的主单号
        Long splitMainTradeSid = getSplitMainTradeSid(trades, leaveMainTradeSid);
        relation.splitTrades = trades.stream().filter(t -> Objects.equals(t.getSid(), splitMainTradeSid) || Objects.equals(t.getMergeSid(), splitMainTradeSid)).collect(Collectors.toList());
        relation.splitMainTrade = trades.stream().filter(t -> Objects.equals(t.getSid(), splitMainTradeSid)).collect(Collectors.toList()).get(0);
        return relation;
    }

    private Long getLeaveMainTradeSid(Long leaveOrderId, List<Trade> trades) {
        for (Trade trade : trades) {
            Order order = OrderUniqueCodeUtils.getFullTradeOrders(trade).get(leaveOrderId);
            if (order != null) {
                return DataUtils.checkLongNotEmpty(trade.getMergeSid()) ? trade.getMergeSid() : trade.getSid();
            }
        }
        throw new IllegalArgumentException("根据leaveOrderId：" + leaveOrderId + " 未查询到主单！");
    }

    private Long getSplitMainTradeSid(List<Trade> trades, Long leaveMainTradeSid) {
        Long splitMainTradeSid = 0L;
        for (Trade trade : trades) {
            if (!Objects.equals(trade.getSid(), leaveMainTradeSid) && !Objects.equals(trade.getMergeSid(), leaveMainTradeSid)) {
                return DataUtils.checkLongNotEmpty(trade.getMergeSid()) ? trade.getMergeSid() : trade.getSid();
            }
        }
        return splitMainTradeSid;
    }

    private String getOutSid(Trade mainTrade, List<Trade> trades) {
        if (StringUtils.isNotEmpty(mainTrade.getOutSid())) {
            return mainTrade.getOutSid();
        }
        for (Trade trade : trades) {
            if (!TradeUtils.isAfterSendGoods(trade) && StringUtils.isNotEmpty(trade.getOutSid())) {
                return trade.getOutSid();
            }
        }
        return "";
    }

    protected void setTradeUnShelfTag(Staff staff, OrderUniqueCodeSplitParams params, SplitAuditedNumResponse response) {
        Long splitMainSid = params.getSingleSplitParams().getSplitMainSid();
        if (splitMainSid == null) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "新拆分出的订单：" + splitMainSid + "开始计算下架标签！"));
        }
        List<WaveUniqueCode> codes = OrderUniqueCodeUtils.filterFinalGenerates(orderUniqueCodeService.queryBySid(staff, splitMainSid));
        // 重新计算订单下架标签
        if (CollectionUtils.isNotEmpty(codes) && codes.stream().allMatch(c -> !Objects.equals(OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), c.getStatus()))) {
            tradeServiceDubbo.setTags(staff, new Long[]{splitMainSid}, new Long[]{SystemTags.TAG_UNIQUE_OFF_SHELF.getId()}, 2);
        }

        // 重新计算唯一码单/多
        if (CollectionUtils.isNotEmpty(codes) && codes.size() == 1) {
            // 如果拆分出的唯一码是已收货/已拣选 则不清空分拣货位信息
            boolean notClearPositionNo = (Objects.equals(OrderUniqueCodeStatusEnum.RECIVED.getType(), codes.get(0).getStatus()) || Objects.equals(OrderUniqueCodeStatusEnum.PICKED.getType(), codes.get(0).getStatus()));
            orderUniqueCodeService.batchUpdate(staff, Lists.newArrayList(OrderUniqueCodeUtils.buildMulti2Single(codes.get(0), !notClearPositionNo)));
        }
        // 拆分新标处理
        dealNewSplit(staff, codes);
    }

    private void dealNewSplit(Staff staff, List<WaveUniqueCode> codes) {
        OrderUniqueCodeConfig config = orderUniqueCodeService.queryConfig(staff);
        if (config == null || !OrderUniqueCodeUtils.isTrue(config.getSplitNewTag())) {
            return;
        }

        List<WaveUniqueCode> updates = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setUniqueCode(code.getUniqueCode());
            update.setNewSplit(CommonConstants.JUDGE_YES);
            updates.add(update);
        }

        orderUniqueCodeService.batchUpdate(staff, updates);
    }

    private SplitAuditedNumRequest buildRequest(Staff staff, OrderUniqueCodeSplitParams params) {
        List<Order> orders = buildSplitOrders(staff, params);
        if (logger.isInfoEnabled()) {
            UniqueCodeUtils.debugLog(staff, OrderUniqueCodeUtils.buildSplitOrderLog(orders), logger, "拆分请求数据", 2);
        }
        orders = orders.stream().filter(o -> o.getSplitNum() > 0).collect(Collectors.toList());
        Assert.notEmpty(orders, "无可拆分商品！");

        return SplitAuditedNumRequest.builder()
                .splitEnum(TradeSplitEnum.SPLIT_WAVE)
                .sid(params.getSingleSplitParams().getTrade().getSid())
                .splitOrders(orders)
                .needUnAudit(params.isNeedUnAudit())
                .splitFx2GxNew(tradeLocalConfig.isTradeSplitFx2GxCompanyIds(staff.getCompanyId()))
                .build();
    }
}

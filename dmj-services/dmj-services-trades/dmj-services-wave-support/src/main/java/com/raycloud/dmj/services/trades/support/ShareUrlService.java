package com.raycloud.dmj.services.trades.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.dao.trade.ShareUrlDao;
import com.raycloud.dmj.dao.wave.WaveQueryConditionDao;
import com.raycloud.dmj.dao.wave.WaveUniqueCodeDao;
import com.raycloud.dmj.dms.request.DmsSupplierBridgeRequest;
import com.raycloud.dmj.dms.response.DmsSupplierBridgeInfoResponse;
import com.raycloud.dmj.dms.domain.dto.DmsCgSupplierCompanyRelationDto;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.PageListBase;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.basis.SupplierQueryParams;
import com.raycloud.dmj.domain.trades.ShareUrl;
import com.raycloud.dmj.domain.trades.params.ShareUrlDTO;
import com.raycloud.dmj.domain.trades.utils.LocalDateUtils;
import com.raycloud.dmj.domain.trades.utils.ShareSignUtils;
import com.raycloud.dmj.domain.trades.vo.ShareUrlVO;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.WaveQueryCondition;
import com.raycloud.dmj.domain.wave.model.OrderUniqueCodeQueryParams;
import com.raycloud.dmj.services.basis.ISupplierService;
import com.raycloud.dmj.services.basis.Suppliers;
import com.raycloud.dmj.services.trades.IShareUrlService;
import com.raycloud.dmj.domain.wave.OrderUniqueCodeStatusEnum;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.web.utils.PwdUtils;
import com.raycloud.dmj.wx.SupplierUserService;
import com.raycloud.secret_api.api.SecretRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @description: 外部链接管理
 * @author: chunri
 * @create: 2021-03-10 14:12
 **/
@Service
public class ShareUrlService implements IShareUrlService {

    @Resource
    private ShareUrlDao shareUrlDao;
    @Resource
    private ISupplierService supplierService;
    @Resource
    private SecretRequest secretRequest;
    @Resource
    private IShopService shopService;
    @Resource
    private IDmsTradeService dmsTradeService;
    @Resource
    private WaveQueryConditionDao waveQueryConditionDao;
    @Resource
    private WaveUniqueCodeDao waveUniqueCodeDao;
    @Resource
    protected ICache cache;

    @Resource
    private SupplierUserService supplierUserService;
    // 缺货状态-外采
    private static final Integer SOURCE_OUT_PURCHASE = 0;

    private static final String EMPTY_JSON = "{}";

    // 供销商接口查询字段
    private static final String SEARCH_FIELD = "supplier_id";
    // 外链查询条件类型
    private static final Integer WAVE_QUERY_CONDITION_TYPE = 2;

    private Logger logger = Logger.getLogger(this.getClass());
    /**
     * 供应商合作状态改变自动禁用链接
     */
    @Override
    public void supplierChangeAutoDisableUrl(Staff staff, Long supplierId) {
        ShareUrlVO shareUrlVO = queryBySupplierId(staff, supplierId);
        if (shareUrlVO != null && Objects.equals(shareUrlVO.getSupplierAutoDisable(), 1) && Objects.equals(shareUrlVO.getEnableStatus(), 1)) {
            batchEnableOrDisable(staff, String.valueOf(shareUrlVO.getId()), 0);
        }
    }

    /**
     * 列表
     */
    @Override
    public PageListBase<ShareUrlVO> list(Staff staff, ShareUrlDTO shareUrlDTO) {
        PageListBase<ShareUrlVO> pageListBase = new PageList<>();
        // 过滤列表
        boolean isEmpty = filterShareUrlList(staff, shareUrlDTO);
        if (isEmpty) {
            return pageListBase.setTotal(0L).setList(Lists.newArrayList());
        }
        List<ShareUrlVO> list = shareUrlDao.list(staff, shareUrlDTO);
        // 填充列表
        fillSupplierInfo(list);
        // 填充+过滤供销商信息
        list = fillDistributorInfo(staff, list, shareUrlDTO);
        encodePassword(list);
        list.forEach(this::fillDetailInfo);
        // 填充绑定数
        fillBindNum(staff,list);
        pageListBase.setTotal((long) list.size());
        // 排序分页
        pageListBase.setList(orderAndPage(shareUrlDTO, list));
        return pageListBase;
    }

    /**
     * @description: 填充+过滤供销商信息
     * @author: HZY
     * @date: 2023/5/18 17:37
     */
    private List<ShareUrlVO> fillDistributorInfo(Staff staff, List<ShareUrlVO> list, ShareUrlDTO shareUrlDTO) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        DmsCgSupplierCompanyRelationDto dmsCgSupplierCompanyRelationDto = new DmsCgSupplierCompanyRelationDto();
        // 根据供应商id集合 批量查询供应商对应的供销商数据
        try {
            dmsCgSupplierCompanyRelationDto = dmsTradeService.getCgSupplierCompanyRelation(list.stream().map(ShareUrlVO::getSupplierId).collect(toList()), staff.getCompanyId(), null, null);
        } catch (Exception e) {
            logger.debug(LogHelper.buildLog(staff, e.getMessage()));
        }
        if (null != dmsCgSupplierCompanyRelationDto && CollectionUtils.isNotEmpty(dmsCgSupplierCompanyRelationDto.getList())) {
            Map<Long, List<DmsCgSupplierCompanyRelationDto.SingleResponse>> singleResponseMap = dmsCgSupplierCompanyRelationDto.getList().stream()
                    .collect(Collectors.groupingBy(DmsCgSupplierCompanyRelationDto.SingleResponse::getCgSupplierId));
            // 填充供销商信息 原则上 供应商和供销商的关联关系为  1:1
            list.forEach(shareUrlVO -> {
                if (null != singleResponseMap && singleResponseMap.containsKey(shareUrlVO.getSupplierId())) {
                    shareUrlVO.setDistributorId(singleResponseMap.get(shareUrlVO.getSupplierId()).get(0).getSupplierCompanyId());
                    shareUrlVO.setDistributorName(singleResponseMap.get(shareUrlVO.getSupplierId()).get(0).getSupplierCompanyName());
                }
            });
        }
        // 若前端有供销商筛选条件 则进行过滤
        if (null != shareUrlDTO.getDistributorName() && StringUtils.isNotEmpty(shareUrlDTO.getDistributorName())) {
            list = list.stream().filter(shareUrlVO -> shareUrlDTO.getDistributorName().equals(shareUrlVO.getDistributorName())).collect(toList());
        }
        return list;
    }

    private void encodePassword(List<ShareUrlVO> list) {
        list.forEach(vo -> vo.setPassword("******"));
    }
    private void fillBindNum(Staff staff, List<ShareUrlVO> list) {
        Set<Long> supplierSet = list.stream().map(ShareUrl::getSupplierId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(supplierSet)){
            Map<Long, Integer> supplierNumMap = supplierUserService.getCountByCompanyIdAndSupplierIds(staff.getCompanyId(), new ArrayList<>(supplierSet));
            list.forEach(
                    v -> v.setBindNum(supplierNumMap.getOrDefault(v.getSupplierId(), 0))
            );
        }
    }

    private List<ShareUrlVO> orderAndPage(ShareUrlDTO shareUrlDTO, List<ShareUrlVO> list) {
        Function<ShareUrlVO, String> sortField = null;
        if ("supplierName".equals(shareUrlDTO.getSortField())) {
            sortField = ShareUrlVO::getSupplierName;
        }
        if ("supplierCode".equals(shareUrlDTO.getSortField())) {
            sortField = ShareUrlVO::getSupplierCode;
        }
        if (sortField != null) {
            list = list.stream().sorted(Comparator.comparing(sortField, "desc".equals(shareUrlDTO.getSortOrder()) ? Comparator.reverseOrder() : Comparator.naturalOrder())).collect(Collectors.toList());
        }
        return list.subList(Math.min(shareUrlDTO.getStartRow(), list.size()), Math.min(list.size(), shareUrlDTO.getStartRow() + shareUrlDTO.getPageSize()));
    }

    /**
     * 新建
     */
    @Override
    public void insert(Staff staff, ShareUrlDTO shareUrlDTO) {
        Map<Long, Supplier> id2SupplierMap = getId2SupplierMap(ArrayUtils.toLongList(shareUrlDTO.getSupplierIdStr()));
        List<Long> supplierIds = ArrayUtils.toLongList(shareUrlDTO.getSupplierIdStr());
        Long count = shareUrlDao.count(staff, new ShareUrlDTO().setSupplierIds(supplierIds));
        if (count != null && count > 0) {
            throw new IllegalArgumentException("供应商不能重复");
        }
        // 校验是否在供销中存在的
        validDmsExistSupplier(staff, id2SupplierMap, supplierIds);
        List<ShareUrl> list = Lists.newArrayList();
        id2SupplierMap.forEach((k, v) -> {
            String sign = ShareSignUtils.encodeSign(ShareSignUtils.ShareSignInfo.builder().key(String.valueOf(k)).companyId(staff.getCompanyId()).build(), secretRequest);
            list.add(ShareUrl.builder()
                    .supplierId(k)
                    .supplierName(v.getName())
                    .companyId(staff.getCompanyId())
                    .dataPrivilege(shareUrlDTO.getDataPrivilege())
                    .dayLimit(shareUrlDTO.getDayLimit())
                    .shareType(shareUrlDTO.getShareType())
                    .sign(sign)
                    .url(getUrl(shareUrlDTO.getUrlPrefix(), sign))
                    .created(new Date())
                    .urlRefreshTime(new Date())
                    .password(ShareSignUtils.encodePassword(PwdUtils.createRandomPwd(6), secretRequest))
                    .purchaseOrderEncryptField(shareUrlDTO.getPurchaseOrderEncryptField())
                    .purchaseOrderDayLimit(shareUrlDTO.getPurchaseOrderDayLimit())
                    .purchaseReturnDayLimit(shareUrlDTO.getPurchaseReturnDayLimit())
                    .purchaseReturnEncryptField(shareUrlDTO.getPurchaseReturnEncryptField())
                    .orderUniqueExtConfig(shareUrlDTO.getOrderUniqueExtConfig())
                    .orderUniqueExtConf(JSON.toJSONString(Optional.ofNullable(shareUrlDTO.getOrderUniqueExtConfig()).orElse(new ShareUrl.OrderUniqueExtConf())))
                    .viewReceiveStatus(shareUrlDTO.getViewReceiveStatus())
                    .build());
        });
        shareUrlDao.batchInsert(staff, list);
    }

    /**
     * 供应商外链管理增加限制，供销、外链二选一
     */
    private void validDmsExistSupplier(Staff staff, Map<Long, Supplier> id2SupplierMap, List<Long> supplierIds) {
        Map<Long, Supplier> supplierMap = MapUtils.isEmpty(id2SupplierMap) ? getId2SupplierMap(supplierIds) : id2SupplierMap;
        DmsSupplierBridgeRequest request = new DmsSupplierBridgeRequest();
        request.setSupplierIds(supplierIds);
        request.setStaff(staff);
        request.setSearchField(SEARCH_FIELD);
        request.setPinchHitItemLabel(true);
        DmsSupplierBridgeInfoResponse dmsSupplierBridgeInfoResponse = dmsTradeService.querySupplierBridgeInfo(request);
        if (dmsSupplierBridgeInfoResponse == null || CollectionUtils.isEmpty(dmsSupplierBridgeInfoResponse.getDmsSupplierBridgeDomainList())) {
            return;
        }
        List<String> existSupplierNames = dmsSupplierBridgeInfoResponse.getDmsSupplierBridgeDomainList()
                .stream().map(data -> supplierMap.getOrDefault(data.getSupplierId(), new Supplier()).getName())
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(toList());
        if (CollectionUtils.isNotEmpty(existSupplierNames)) {
            throw new IllegalArgumentException(existSupplierNames + "供应商，开启采购外链打标前请先关闭相应供应商的档口打标！");
        }
    }

    /**
     * 打开采购单分享
     * @param staff
     * @param shareUrlDTO
     */
    @Override
    public void openPurchaseShareUrl(Staff staff, ShareUrlDTO shareUrlDTO) {
        shareUrlDTO.setDataPrivilege(1);
        shareUrlDTO.setDayLimit(3);
        shareUrlDTO.setShareType(ShareUrl.ShareType.purchaseShareType.getType().toString());
        shareUrlDTO.setPurchaseShareType(true);
        shareUrlDTO.setPurchaseOrderDayLimit(3);
        ShareUrl.OrderUniqueExtConf extConf = new ShareUrl.OrderUniqueExtConf();
        extConf.setPurchaseOrderUnitConf(0);
        extConf.setStatusLimit(ShareUrl.INIT_STATUS);
        extConf.setFilterBind(true);
        extConf.setUnitConf(0);
        shareUrlDTO.setOrderUniqueExtConfig(extConf);
        ShareUrlVO shareUrlVO = shareUrlDao.queryBySupplierId(staff, shareUrlDTO.getSupplierIds().get(0));
        if (null == shareUrlVO) {
            insert(staff, shareUrlDTO);
        } else {
            if (!shareUrlVO.getShareType().contains(ShareUrl.ShareType.purchaseShareType.getType().toString())) {
                shareUrlDTO.setShareType(new StringBuilder(shareUrlVO.getShareType()).append(",").append(ShareUrl.ShareType.purchaseShareType.getType()).toString());
                shareUrlDTO.setId(shareUrlVO.getId());
                update(staff, shareUrlDTO);
            }
        }
    }

    /**
     * 打开采退单分享
     * @param staff
     * @param shareUrlDTO
     */
    @Override
    public void openPurchaseReturnShareUrl(Staff staff, ShareUrlDTO shareUrlDTO) {
        shareUrlDTO.setDataPrivilege(1);
        shareUrlDTO.setDayLimit(3);
        shareUrlDTO.setShareType(ShareUrl.ShareType.purchaseReturnShareType.getType().toString());
        shareUrlDTO.setPurchaseReturnShareType(true);
        shareUrlDTO.setPurchaseReturnDayLimit(3);
        ShareUrl.OrderUniqueExtConf extConf = new ShareUrl.OrderUniqueExtConf();
        extConf.setPurchaseOrderUnitConf(0);
        extConf.setStatusLimit(ShareUrl.INIT_STATUS);
        extConf.setFilterBind(true);
        extConf.setUnitConf(0);
        shareUrlDTO.setOrderUniqueExtConfig(extConf);
        ShareUrlVO shareUrlVO = shareUrlDao.queryBySupplierId(staff, shareUrlDTO.getSupplierIds().get(0));
        if (null == shareUrlVO){
            insert(staff, shareUrlDTO);
        } else {
            if (!shareUrlVO.getShareType().contains(ShareUrl.ShareType.purchaseReturnShareType.getType().toString())){
                shareUrlDTO.setShareType(new StringBuilder(shareUrlVO.getShareType()).append(",").append(ShareUrl.ShareType.purchaseReturnShareType.getType()).toString());
                shareUrlDTO.setId(shareUrlVO.getId());
                update(staff, shareUrlDTO);
            }
        }
    }
    /**
     * 更新
     */
    @Override
    public List<String> update(Staff staff, ShareUrlDTO shareUrlDTO) {
        Assert.notNull(shareUrlDTO.getId(), "id不能为空");
        List<ShareUrlVO> shareUrlVOList = shareUrlDao.queryListByIds(staff, Collections.singletonList(shareUrlDTO.getId()));
        if (CollectionUtils.isEmpty(shareUrlVOList)) {
            throw new IllegalArgumentException("分享链接不存在！");
        }
        ShareUrlVO shareUrlVO = shareUrlVOList.get(0);
        // 关闭采购单分享功能
        if (StringUtils.isNotEmpty(shareUrlVO.getShareType()) && StringUtils.isNotEmpty(shareUrlDTO.getShareType())) {
            if ((shareUrlVO.getShareType().contains(ShareUrl.ShareType.purchaseShareType.getType().toString()) && !shareUrlDTO.getShareType().contains(ShareUrl.ShareType.purchaseShareType.getType().toString()))
                    || shareUrlVO.getShareType().contains(ShareUrl.ShareType.purchaseReturnShareType.getType().toString()) && !shareUrlDTO.getShareType().contains(ShareUrl.ShareType.purchaseReturnShareType.getType().toString())) {
                Map<Long, ShareUrlVO> shareUrlVOMap = shareUrlVOList.stream().collect(Collectors.toMap(ShareUrlVO::getSupplierId, Function.identity()));
                Set<Long> supplierSet = shareUrlVOList.stream().map(ShareUrl::getSupplierId).collect(Collectors.toSet());
                Map<Long, Integer> supplierNumMap = supplierUserService.getCountByCompanyIdAndSupplierIds(staff.getCompanyId(), new ArrayList<>(supplierSet));
                HashMap<Long, String> errors = new HashMap<>();
                supplierNumMap.forEach(
                        (k, v) -> {
                            if (v > 0) {
                                ShareUrlVO shareUrl = shareUrlVOMap.get(k);
                                if (null != shareUrl) {
                                    errors.put(shareUrl.getId(), "供应商" + shareUrl.getSupplierName() + ",微信账号数大于0，不允许删除");
                                }
                            }
                        }
                );
                if (errors.size() > 0) {
                    return new ArrayList<>(errors.values());
                }
            }
        }
        shareUrlDao.update(staff,
                ShareUrl.builder()
                        .id(shareUrlDTO.getId())
                        .dataPrivilege(shareUrlDTO.getDataPrivilege())
                        .dayLimit(shareUrlDTO.getDayLimit())
                        .shareType(shareUrlDTO.getShareType())
                        .supplierAutoDisable(shareUrlDTO.getSupplierAutoDisable())
                        .purchaseOrderEncryptField(shareUrlDTO.getPurchaseOrderEncryptField())
                        .purchaseReturnEncryptField(shareUrlDTO.getPurchaseReturnEncryptField())
                        .purchaseOrderDayLimit(shareUrlDTO.getPurchaseOrderDayLimit())
                        .purchaseReturnDayLimit(shareUrlDTO.getPurchaseReturnDayLimit())
                        .password(shareUrlDTO.getPassword())
                        .orderUniqueExtConfig(shareUrlDTO.getOrderUniqueExtConfig())
                        .modified(new Date())
                        .viewReceiveStatus(shareUrlDTO.getViewReceiveStatus())
                        .build());
        return new ArrayList<>();
    }

    private int PurchaseReturnDayLimit (ShareUrlDTO shareUrlDTO) {
        if (shareUrlDTO.getPurchaseReturnDayLimit() == CommonConstants.JUDGE_IGNORE ){
            if (shareUrlDTO.getOrderUniqueExtConfig().getPurchaseReturnCustomDayLimit() == null || shareUrlDTO.getOrderUniqueExtConfig().getPurchaseReturnCustomDayLimit() <= 0){
                throw new IllegalArgumentException("自定义时间要大于0");
            }
            return shareUrlDTO.getOrderUniqueExtConfig().getPurchaseReturnCustomDayLimit();
        }
        return shareUrlDTO.getPurchaseReturnDayLimit();
    }

    /**
     * 批量刷新链接时间
     */
    @Override
    public int batchRefreshUrl(Staff staff, String ids, String urlPrefix) {
        List<ShareUrlVO> list = shareUrlDao.queryListByIds(staff, ArrayUtils.toLongList(ids));
        List<ShareUrl> updateList = list.stream()
                .map(vo -> {
                    String sign = ShareSignUtils.encodeSign(ShareSignUtils.ShareSignInfo.builder().key(String.valueOf(vo.getSupplierId())).companyId(staff.getCompanyId()).build(), secretRequest);
                    return ShareUrl.builder()
                            .id(vo.getId())
                            .sign(sign) // 更新签名
                            .url(getUrl(urlPrefix, sign))
                            .urlRefreshTime(new Date()) // 更新时间
                            .modified(new Date())
                            .build();
                }).collect(Collectors.toList());
        return shareUrlDao.batchUpdate(staff, updateList);
    }

    private String getUrl(String urlPrefix, String sign) {
        return urlPrefix + "/outerLink/dist/index.html?sign=" + sign;
    }

    /**
     * 启用禁用
     */
    @Override
    public int batchEnableOrDisable(Staff staff, String ids, Integer status) {
        List<Long> idList = ArrayUtils.toLongList(ids);
        // 校验是否在供销中存在的
        if (Objects.equals(status, CommonConstants.JUDGE_YES)) {
            List<ShareUrlVO> shareUrlVOS = shareUrlDao.queryListByIds(staff, idList);
            validDmsExistSupplier(staff, null, Optional.ofNullable(shareUrlVOS).orElse(Lists.newArrayList()).stream().map(ShareUrlVO::getSupplierId).collect(toList()));
        }
        return shareUrlDao.updateByIds(staff, ShareUrl.builder()
                .idList(idList)
                .enableStatus(status)
                .modified(new Date())
                .build());
    }

    /**
     * 获取详情，根据供应商id
     */
    @Override
    public ShareUrlVO queryBySupplierId(Staff staff, Long supplierId) {
        ShareUrlVO shareUrlVO = shareUrlDao.queryBySupplierId(staff, supplierId);
        if (shareUrlVO == null) {
            return null;
        }
        fillDetailInfo(shareUrlVO);
        // 外链tab, 查询条件
        WaveQueryCondition condition = new WaveQueryCondition();
        condition.setType(WAVE_QUERY_CONDITION_TYPE);
        List<WaveQueryCondition> conditions = waveQueryConditionDao.queryListPage(staff, condition, null);
        if (CollectionUtils.isEmpty(conditions)) {
            initCondition(staff);
            condition.setEnableStatus(CommonConstants.JUDGE_YES);
            conditions = waveQueryConditionDao.queryListPage(staff, condition, null);
        }
        conditions = conditions.stream().filter(data -> Objects.equals(CommonConstants.JUDGE_YES, data.getEnableStatus())).collect(toList());
        shareUrlVO.setWaveQueryConditionList(conditions);
        return shareUrlVO;
    }

    /**
     * 获取详情，根据id
     */
    @Override
    public ShareUrlVO queryById(Staff staff, Long id) {
        Assert.notNull(id, "id is not null");
        List<ShareUrlVO> shareUrlVOList= shareUrlDao.queryListByIds(staff, Lists.newArrayList(id));
        Assert.notEmpty(shareUrlVOList, "外部链接不存在");
        ShareUrlVO vo = shareUrlVOList.get(0);
        fillDetailInfo(vo);
        return vo;
    }

    @Override
    public List<ShareUrlVO> queryByIds(Staff staff, List<Long> ids) {
        Assert.notEmpty(ids, "ids is not empty");
        List<ShareUrlVO> shareUrlVOList= shareUrlDao.queryListByIds(staff, ids);
        Assert.notEmpty(shareUrlVOList, "外部链接不存在");
        shareUrlVOList.forEach(this::fillDetailInfo);
        return shareUrlVOList;
    }

    private void fillDetailInfo(ShareUrlVO vo) {
        fillPurchaseOrderEncryptField(vo);
        fillOrderUniqueExtConf(vo);
    }

    private void fillOrderUniqueExtConf(ShareUrlVO shareUrlVO) {
        if (StringUtils.isEmpty(shareUrlVO.getOrderUniqueExtConf()) || Objects.equals(shareUrlVO.getOrderUniqueExtConf(), EMPTY_JSON)) {
            shareUrlVO.setOrderUniqueExtConfig(new ShareUrl.OrderUniqueExtConf().init());
            shareUrlVO.setOrderUniqueExtConf(JSON.toJSONString(shareUrlVO.getOrderUniqueExtConfig()));
        } else {
            shareUrlVO.setOrderUniqueExtConfig(JSON.parseObject(shareUrlVO.getOrderUniqueExtConf(), ShareUrl.OrderUniqueExtConf.class));
        }
        JSONObject jsonObject = JSONObject.parseObject(shareUrlVO.getOrderUniqueExtConf());
        if (!jsonObject.containsKey("orderUniqueDisplayFile")) {
            shareUrlVO.getOrderUniqueExtConfig().setOrderUniqueDisplayFile(ShareUrl.OrderUniqueExtConf.DEFAULT_ORDER_UNIQUE_DISPLAY_FILE);
        }
    }






    private void fillPurchaseOrderEncryptField(ShareUrlVO shareUrlVO) {
        if (StringUtils.isEmpty(shareUrlVO.getPurchaseOrderEncryptField())) {
            return;
        }
        List<Integer> types = ArrayUtils.toIntegerListPosition(shareUrlVO.getPurchaseOrderEncryptField());
        if (CollectionUtils.isEmpty(types)) {
            return;
        }
        for (Integer type : types) {
            switch (type) {
                case 1:
                    shareUrlVO.setEncryptPurchasePrice(true);
                    break;
                case 2:
                    shareUrlVO.setEncryptReceiveAddress(true);
                    break;
                case 3:
                    shareUrlVO.setEncryptReceiveContact(true);
                    break;
                case 5:
                    shareUrlVO.setShowPic(true);
                    break;
            }
        }
    }

    @Override
    public List<ShareUrlVO> queryBySupplierIds(Staff staff, List<Long> supplierIds) {
        List<ShareUrlVO> shareUrlVOList = shareUrlDao.queryBySupplierIds(staff, supplierIds);
        shareUrlVOList.forEach(shareUrlVO -> {
            if (null != shareUrlVO.getPassword()) {
                shareUrlVO.setPassword(ShareSignUtils.decodePassword(shareUrlVO.getPassword(), secretRequest));
            }
        });
        return shareUrlDao.queryBySupplierIds(staff, supplierIds);
    }

    /**
     * 过滤列表 供应商分类
     */
    private boolean filterShareUrlList(Staff staff, ShareUrlDTO shareUrlDTO) {
        if (StringUtils.isEmpty(shareUrlDTO.getCategoryNameStr()) && shareUrlDTO.getSupplierStatus() == null) {
            return false;
        }
        SupplierQueryParams queryParams = new SupplierQueryParams();
        queryParams.setStatus(shareUrlDTO.getSupplierStatus());
        queryParams.setCategoryName(shareUrlDTO.getCategoryNameStr());
        queryParams.setCategoryId(shareUrlDTO.getCategoryIdStr());
        Suppliers suppliers = supplierService.list(staff,queryParams);

        if (CollectionUtils.isEmpty(suppliers.getList())) {
            return true;
        }
        List<Long> supplierIds = suppliers.getList().stream().map(Supplier::getId).collect(Collectors.toList());
        shareUrlDTO.setSupplierIds(supplierIds);
        return false;
    }

    /**
     * 填充供应商信息
     */
    private void fillSupplierInfo(List<ShareUrlVO> list) {
        Map<Long, Supplier> id2SupplierMap = getId2SupplierMap(list.stream().map(ShareUrl::getSupplierId).distinct().collect(Collectors.toList()));
        for (ShareUrlVO vo : list) {
            Supplier supplier = Optional.ofNullable(id2SupplierMap.get(vo.getSupplierId())).orElse(new Supplier());
            vo.setSupplierCategoryNames(supplier.getCategoryName())
                    .setSupplierCode(Objects.isNull(supplier.getCode()) ? "" : supplier.getCode())
                    .setSupplierStatus(supplier.getStatus())
                    .setSupplierName(Objects.isNull(supplier.getName()) ? "" : supplier.getName());
        }

    }

    /**
     * 获取供应商id为key的map
     */
    private Map<Long, Supplier> getId2SupplierMap(List<Long> ids) {
        return supplierService
                .queryByIds(ids)
                .stream().collect(Collectors.toMap(Supplier::getId, Function.identity()));
    }

    /**
     * 过滤唯一码数据
     */
    @Override
    public void buildParams(Staff staff, OrderUniqueCodeQueryParams params) {
        buildParams(staff, params, queryBySupplierId(staff, params.getSupplierIds().get(0)));
    }

    public static boolean isForceTypeStatusIndex(List<Integer> notStatusList) {
        if (CollectionUtils.isEmpty(notStatusList)) {
            return false;
        }

        List<Integer> notSupplierStatus = Lists.newArrayList(OrderUniqueCodeStatusEnum.RECIVED.getType(), OrderUniqueCodeStatusEnum.OUT.getType(),
                OrderUniqueCodeStatusEnum.CANCEL.getType(), OrderUniqueCodeStatusEnum.OFF_SHELF.getType(), OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType());
        for (Integer status : notSupplierStatus) {
            if (!notStatusList.contains(status)) {
                return false;
            }
        }

        return true;
    }

    public void buildParams(Staff staff, OrderUniqueCodeQueryParams params, ShareUrlVO vo) {
        if (CollectionUtils.isEmpty(params.getSupplierIds())) {
            throw new IllegalArgumentException("参数不正确");
        }
        if (vo == null || Objects.equals(vo.getEnableStatus(), 0)) {
            throw new IllegalArgumentException("链接已失效");
        }
        // 过滤已取消和已下架
        List<Integer> notStatusList = StringUtils.isNotEmpty(vo.getOrderUniqueExtConfig().getStatusLimit())
                ? ArrayUtils.toIntegerListPosition(vo.getOrderUniqueExtConfig().getStatusLimit())
                : Lists.newArrayList(OrderUniqueCodeStatusEnum.CANCEL.getType(), OrderUniqueCodeStatusEnum.OFF_SHELF.getType());
        // 过滤已退回和已采退
        notStatusList.add(OrderUniqueCodeStatusEnum.RETURN.getType());
        notStatusList.add(OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType());
        // 过滤【在库】【已作废】【待入库】
        notStatusList.add(OrderUniqueCodeStatusEnum.WAIT_IN.getType());
        notStatusList.add(OrderUniqueCodeStatusEnum.IN.getType());
        notStatusList.add(OrderUniqueCodeStatusEnum.DELETE.getType());
        // 过滤【已取消】【已下架】
        notStatusList.add(OrderUniqueCodeStatusEnum.CANCEL.getType());
        notStatusList.add(OrderUniqueCodeStatusEnum.OFF_SHELF.getType());
        params.setNotStatusList(notStatusList);
        if (Objects.equals(26083L, staff.getCompanyId())) {
            params.getNotStatusList().add(OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType());
        }
        params.setForceTypeStatusIndex(isForceTypeStatusIndex(params.getNotStatusList()));
        // 只查外采
        params.setStockStatus(SOURCE_OUT_PURCHASE);
        // 外链过滤解绑
        params.setShareFilter(Optional.ofNullable(vo.getOrderUniqueExtConfig()).map(ShareUrl.OrderUniqueExtConf::getFilterBind).orElse(Boolean.TRUE));
        // 转换店铺来源为店铺userId
        convertShopSources(staff, params);

        Integer dayLimit = vo.getDayLimit();
        if (dayLimit == null || dayLimit == 0) {
            return;
        }
        // 当前 2021-03-12
        LocalDateTime currentLocalDate = LocalDateUtils.getTodayEndTime(); // 2021-03-12 23:59:59
        LocalDateTime minusLocalDate = currentLocalDate.minusDays(dayLimit); // 2021-03-11 23:59:59

        if (params.getCreateBegin() == null || minusLocalDate.isAfter(LocalDateUtils.convertDateToLDT(params.getCreateBegin()))) {
            params.setCreateBegin(LocalDateUtils.convertLDTToDate(minusLocalDate));
        }
        if (params.getCreateEnd() == null || currentLocalDate.isBefore(LocalDateUtils.convertDateToLDT(params.getCreateEnd()))) {
            params.setCreateEnd(LocalDateUtils.convertLDTToDate(currentLocalDate));
        }
    }

    @Override
    public List<Long> querySupplierFromShareUrl(Staff staff) {
        return shareUrlDao.querySupplierFromShareUrl(staff);
    }

    @Override
    public List<String> batchDelete(Staff staff, String ids) {
        List<Long> idArr = ArrayUtils.toLongList(ids);
        if (null == idArr || idArr.size() <= 0) {
            return new ArrayList<>();
        }
        HashMap<Long, String> errors = checkWxPurchase(staff, idArr);
        idArr.removeAll(errors.keySet());
        if (CollectionUtils.isNotEmpty(idArr)){
            shareUrlDao.batchDelete(staff, idArr);
        }
        return new ArrayList<>(errors.values());
    }

    public HashMap<Long, String> checkWxPurchase(Staff staff, List<Long> ids) {
        List<ShareUrlVO> shareUrlVOList = shareUrlDao.queryListByIds(staff, ids);
        Map<Long, ShareUrlVO> shareUrlVOMap = shareUrlVOList.stream().collect(Collectors.toMap(ShareUrlVO::getSupplierId, Function.identity()));
        Set<Long> supplierSet = shareUrlVOList.stream().map(ShareUrl::getSupplierId).collect(Collectors.toSet());
        Map<Long, Integer> supplierNumMap = supplierUserService.getCountByCompanyIdAndSupplierIds(staff.getCompanyId(), new ArrayList<>(supplierSet));
        HashMap<Long, String> errors = new HashMap<>();
        supplierNumMap.forEach(
                (k, v) -> {
                    if (v > 0) {
                        ShareUrlVO shareUrlVO = shareUrlVOMap.get(k);
                        if (null != shareUrlVO) {
                            errors.put(shareUrlVO.getId(), "供应商" + shareUrlVO.getSupplierName() + ",微信账号数大于0，不允许删除或停用采购订单分享");
                        }
                    }
                }
        );
        return errors;
    }

    /**
     * 批量更新
     */
    @Override
    public int batchUpdate(Staff staff, ShareUrlDTO shareUrlDTO) {
        Assert.notNull(shareUrlDTO.getIds(), "ids is not null");
        List<Long> idList = ArrayUtils.toLongList(shareUrlDTO.getIds());
        List<ShareUrlVO> list = shareUrlDao.queryListByIds(staff, idList);
        list.forEach(this::fillDetailInfo);
        List<ShareUrl> updateList = list.stream()
                .map(vo -> ShareUrl.builder()
                        .id(vo.getId())
                        .enableStatus(shareUrlDTO.getEnableStatus())
                        .dataPrivilege(shareUrlDTO.getDataPrivilege())
                        .shareType(getShareType(shareUrlDTO, vo))
                        .dayLimit(shareUrlDTO.getDayLimit())
                        .orderUniqueExtConfig(shareUrlDTO.getOrderUniqueExtConfig())
                        .purchaseOrderDayLimit(shareUrlDTO.getPurchaseOrderDayLimit())
                        .purchaseOrderEncryptField(shareUrlDTO.getPurchaseOrderEncryptField())
                        .purchaseReturnDayLimit(shareUrlDTO.getPurchaseReturnDayLimit())
                        .purchaseReturnEncryptField(shareUrlDTO.getPurchaseReturnEncryptField())
                        .supplierAutoDisable(shareUrlDTO.getSupplierAutoDisable())
                        .modified(new Date())
                        .viewReceiveStatus(shareUrlDTO.getViewReceiveStatus())
                        .build()).collect(Collectors.toList());
        // 校验是否在供销中存在的
        validDmsExistSupplier(staff, null, updateList.stream()
                .filter(data -> Objects.equals(data.getEnableStatus(), CommonConstants.JUDGE_YES))
                .map(ShareUrl::getSupplierId).collect(toList()));

        return shareUrlDao.batchUpdate(staff, updateList);

    }

    @Override
    public List<ShareUrlVO> queryShareUrlsByCondition(Staff staff, ShareUrlDTO condition) {
        condition.setEnableStatus(CommonConstants.JUDGE_YES);
        return shareUrlDao.list(staff, condition);
    }

    @Override
    public int batchResetPassword(Staff staff, String ids) {
        List<Long> idList = ArrayUtils.toLongList(ids);
        List<ShareUrlVO> vos = shareUrlDao.queryListByIds(staff, idList);
        if(CollectionUtils.isEmpty(vos)){
            throw new IllegalArgumentException("该外链不存在");
        }
        List<ShareUrl> updateList = vos.stream()
                .map(vo -> ShareUrl.builder()
                        .id(vo.getId())
                        .companyId(vo.getCompanyId())
                        .password(ShareSignUtils.encodePassword(PwdUtils.createRandomPwd(6), secretRequest))
                        .build()).collect(Collectors.toList());
        int updateNum = shareUrlDao.batchUpdate(staff, updateList);
        clearWrongPwdAccountCache(staff,vos);
        return updateNum;
    }

    @Override
    public void updatePassword(Staff staff, ShareUrlDTO shareUrlDTO) {
        String reNewPwd = shareUrlDTO.getReNewPwd();
        String newPwd = shareUrlDTO.getNewPwd();
        String oldPwd = shareUrlDTO.getOldPwd();
        Assert.hasText(newPwd, "新密码不能为空");
        Assert.hasText(reNewPwd, "确认新密码不能为空");

        if (!newPwd.equals(reNewPwd)) {
            throw new IllegalArgumentException("两次输入密码不同！");
        }
        if (!PwdUtils.checkSharePass(newPwd)) {
            throw new IllegalArgumentException(CommonConstants.SHARE_URL_PWD_VALIDATION);
        }

        ShareUrlVO shareUrlVO = queryBySupplierId(staff, shareUrlDTO.getSupplierId());

        if(shareUrlVO == null){
            throw new IllegalArgumentException("该外链不存在");
        }
        String decodePassword = ShareSignUtils.encodePassword(oldPwd, secretRequest);
        if (!shareUrlVO.getPassword().equals(decodePassword)){
            throw new IllegalArgumentException("旧密码不正确");
        }
        ShareUrlDTO update = new ShareUrlDTO();
        update.setId(shareUrlVO.getId());
        update.setCompanyId(shareUrlVO.getCompanyId());
        update.setPassword(ShareSignUtils.encodePassword(newPwd, secretRequest));
        update(staff, update);
        clearWrongPwdAccountCache(staff,Lists.newArrayList(shareUrlVO));
    }

    @Override
    public String decodePassword(Staff staff, Long id) {
        List<ShareUrlVO> shareUrlVOList= shareUrlDao.queryListByIds(staff, Lists.newArrayList(id));
        Assert.notEmpty(shareUrlVOList, "外部链接不存在");
        ShareUrlVO vo = shareUrlVOList.get(0);
        return ShareSignUtils.decodePassword(vo.getPassword(), secretRequest);
    }

    @Override
    public void clearWrongPwdAccountCache(Staff staff,List<ShareUrlVO> vos){
        List<Long> idList = vos.stream().map(ShareUrlVO::getId).collect(Collectors.toList());
        if(logger.isDebugEnabled()){
            logger.debug(LogHelper.buildLog(staff, String.format("清空外链缓存，shareUrlIds:%s", idList)));
        }
        vos.forEach(vo -> {
            try {
                cache.delete(CommonConstants.SHARE_URL_WRONG_PWD_ACCOUNT_COUNT + vo.getCompanyId() + "_" + vo.getSupplierId());
                cache.delete(CommonConstants.SHARE_URL_LOGIN + vo.getCompanyId() + "_" + vo.getSupplierId());
            } catch (CacheException e) {
                logger.debug(LogHelper.buildLog(staff, String.format("清空外链缓存失败，id:%s", vo.getId())));
            }
        });
    }

    public String getShareType(ShareUrlDTO shareUrlDTO, ShareUrl vo) {
        Set<Integer> shareTypeSet = StringUtils.isEmpty(vo.getShareType()) ? Sets.newHashSet() : Sets.newHashSet(ArrayUtils.toIntegerListPosition(vo.getShareType()));
        if (shareUrlDTO.getPrintShareType() != null) {
            if (Objects.equals(shareUrlDTO.getPrintShareType(), Boolean.TRUE)) {
                shareTypeSet.add(ShareUrl.ShareType.printShareType.getType());
            } else {
                shareTypeSet.removeIf(data -> Objects.equals(ShareUrl.ShareType.printShareType.getType(), data));
            }
        }
        if (shareUrlDTO.getPurchaseShareType() != null) {
            if (Objects.equals(shareUrlDTO.getPurchaseShareType(), Boolean.TRUE)) {
                shareTypeSet.add(ShareUrl.ShareType.purchaseShareType.getType());
            } else {
                shareTypeSet.removeIf(data -> Objects.equals(ShareUrl.ShareType.purchaseShareType.getType(), data));
            }
        }
        if (shareUrlDTO.getPurchaseReturnShareType() != null) {
            if (Objects.equals(shareUrlDTO.getPurchaseReturnShareType(), Boolean.TRUE)) {
                shareTypeSet.add(ShareUrl.ShareType.purchaseReturnShareType.getType());
            } else {
                shareTypeSet.removeIf(data -> Objects.equals(ShareUrl.ShareType.purchaseReturnShareType.getType(), data));
            }
        }
        return shareTypeSet.stream().map(String::valueOf).collect(Collectors.joining(","));
    }


    /**
     * 转换店铺来源为店铺userId
     */
    private void convertShopSources(Staff staff, OrderUniqueCodeQueryParams params) {
        if (CollectionUtils.isEmpty(params.getShopSources())) {
            return;
        }
        List<Long> userIds = Optional.ofNullable(shopService.queryShopByCompanyId(staff.getCompanyId(), params.getShopSources().toArray(new String[0])))
                .orElse(Lists.newArrayList())
                .stream().map(Shop::getUserId).collect(toList());
        if (CollectionUtils.isEmpty(userIds)) {
            // 查不到店铺,userId设置成-1
            params.setUserIds(Lists.newArrayList((long) CommonConstants.JUDGE_IGNORE));
            return;
        }
        params.setUserIds(userIds);
    }

    /**
     * 查询外链查询条件配置
     */
    @Override
    public PageListBase<WaveQueryCondition> queryCondition(Staff staff, Page page) {
        PageListBase<WaveQueryCondition> pageListBase = new PageList<>();
        WaveQueryCondition waveQueryCondition = new WaveQueryCondition();
        waveQueryCondition.setType(WAVE_QUERY_CONDITION_TYPE);
        Long total = waveQueryConditionDao.queryListCount(staff, waveQueryCondition);
        if (total == null || total == 0) {
            initCondition(staff);
        }
        waveQueryCondition.setEnableStatus(CommonConstants.JUDGE_YES);
        pageListBase.setTotal(waveQueryConditionDao.queryListCount(staff, waveQueryCondition));
        pageListBase.setList(waveQueryConditionDao.queryListPage(staff, waveQueryCondition, page));
        return pageListBase;
    }

    /**
     * 初始化数据
     */
    private void initCondition(Staff staff) {
        OrderUniqueCodeQueryParams unPrintParams = initParamsCondition();
        unPrintParams.setName("待打印");
        unPrintParams.setPrinted(CommonConstants.JUDGE_NO);
        addCondition(staff, unPrintParams);

        OrderUniqueCodeQueryParams printParams = initParamsCondition();
        printParams.setName("已打印");
        printParams.setPrinted(CommonConstants.JUDGE_YES);
        addCondition(staff, printParams);

        OrderUniqueCodeQueryParams allParams = initParamsCondition();
        allParams.setName("全部");
        addCondition(staff, allParams);
    }

    private OrderUniqueCodeQueryParams initParamsCondition() {
        OrderUniqueCodeQueryParams printParams = new OrderUniqueCodeQueryParams();
        printParams.setDateType(CommonConstants.JUDGE_NO);
        printParams.setDateCondition(CommonConstants.JUDGE_NO);
        return printParams;
    }

    /**
     * 移动外链查询条件配置
     */
    @Override
    @Transactional
    public void moveCondition(Staff staff, boolean up, Long id) {
        WaveQueryCondition condition = new WaveQueryCondition();
        condition.setId(id);
        condition.setType(WAVE_QUERY_CONDITION_TYPE);
        condition.setEnableStatus(CommonConstants.JUDGE_YES);
        condition = waveQueryConditionDao.queryOne(staff, condition);
        Assert.notNull(condition, "当前数据不存在,刷新后重新");
        Integer orderNo = condition.getOrderNo();
        // 获取相邻数据
        WaveQueryCondition nextQueryCondition = waveQueryConditionDao.queryNextOneByOrderNo(staff, orderNo, WAVE_QUERY_CONDITION_TYPE, up);
        if (nextQueryCondition == null) {
            return;
        }
        // 顺序号交换
        List<WaveQueryCondition> conditions = Lists.newArrayList();
        WaveQueryCondition updateCondition = new WaveQueryCondition();
        updateCondition.setId(id);
        updateCondition.setOrderNo(nextQueryCondition.getOrderNo());
        conditions.add(updateCondition);

        WaveQueryCondition nextCondition = new WaveQueryCondition();
        nextCondition.setId(nextQueryCondition.getId());
        nextCondition.setOrderNo(orderNo);
        conditions.add(nextCondition);

        waveQueryConditionDao.batchUpdate(staff, conditions);
    }

    /**
     * 新增外链查询条件配置
     */
    @Override
    @Transactional
    public void addCondition(Staff staff, OrderUniqueCodeQueryParams params) {
        Assert.isTrue(StringUtils.isNotEmpty(params.getName()), "名称不能为空");
        WaveQueryCondition condition = new WaveQueryCondition();
        condition.setCompanyId(staff.getCompanyId());
        condition.setName(params.getName());
        condition.setType(WAVE_QUERY_CONDITION_TYPE);
        condition.setEnableStatus(CommonConstants.JUDGE_YES);
        // 判断名称是否重复
        WaveQueryCondition one = waveQueryConditionDao.queryOne(staff, condition);
        Assert.isTrue(one == null, "名称不能重复");
        // 新增
        condition.setContent(JSON.toJSONString(params));
        condition.setCreated(new Date());
        Long id = waveQueryConditionDao.insert(staff, condition);
        // 更新顺序号
        WaveQueryCondition update = new WaveQueryCondition();
        update.setId(id);
        update.setOrderNo(id.intValue());
        waveQueryConditionDao.update(staff, update);
    }

    /**
     * 修改外链查询条件配置
     */
    @Override
    @Transactional
    public void saveCondition(Staff staff, OrderUniqueCodeQueryParams params) {
        Assert.notNull(params.getQueryId(), "id 不能为空");
        Assert.isTrue(StringUtils.isNotEmpty(params.getName()), "名称不能为空");
        // 判断名称是否重复
        WaveQueryCondition condition = new WaveQueryCondition();
        condition.setName(params.getName());
        condition.setType(WAVE_QUERY_CONDITION_TYPE);
        condition.setEnableStatus(CommonConstants.JUDGE_YES);
        WaveQueryCondition one = waveQueryConditionDao.queryOne(staff, condition);
        Assert.isTrue(one == null || Objects.equals(params.getQueryId(), one.getId()), "名称不能重复");
        // 更新
        condition.setId(params.getQueryId());
        condition.setContent(JSON.toJSONString(params));
        params.setQueryId(null);
        waveQueryConditionDao.update(staff, condition);
    }

    @Override
    public ShareUrlVO queryShareTabTotal(Staff staff, Long supplierId) {
        ShareUrlVO shareUrlVO = new ShareUrlVO();
        // 外链tab, 查询条件
        WaveQueryCondition condition = new WaveQueryCondition();
        condition.setType(WAVE_QUERY_CONDITION_TYPE);
        condition.setEnableStatus(CommonConstants.JUDGE_YES);
        List<WaveQueryCondition> conditions = waveQueryConditionDao.queryListPage(staff, condition, null);
        shareUrlVO.setWaveQueryConditionList(conditions);
        Date startDate = LocalDateUtils.convertLDTToDate(LocalDateUtils.getTodayStartTime());
        Date endDate = LocalDateUtils.convertLDTToDate(LocalDateUtils.getTodayEndTime());
        if (CollectionUtils.isEmpty(shareUrlVO.getWaveQueryConditionList())) {
            return shareUrlVO;
        }
        for (WaveQueryCondition waveQueryCondition : conditions) {
            OrderUniqueCodeQueryParams queryParams = JSON.parseObject(waveQueryCondition.getContent(), OrderUniqueCodeQueryParams.class);
            queryParams.setSupplierIds(Lists.newArrayList(supplierId));
            queryParams.setShareFilter(true);
            if (DateCondition.CURRENT_DAY.getType().equals(queryParams.getDateCondition())) {
                if (DateType.PRINT_TIME.getType().equals(queryParams.getDateType())) {
                    queryParams.setPrintTimeBegin(startDate);
                    queryParams.setPrintTimeEnd(endDate);
                } else {
                    queryParams.setCreateBegin(startDate);
                    queryParams.setCreateEnd(endDate);
                }
            } else if (DateCondition.EXCLUDE_CURRENT_DAY.getType().equals(queryParams.getDateCondition())) {
                if (DateType.PRINT_TIME.getType().equals(queryParams.getDateType())) {
                    queryParams.setPrintTimeEnd(startDate);
                } else {
                    queryParams.setCreateEnd(startDate);
                }
            }
            buildParams(staff, queryParams);
            waveQueryCondition.setTotal(waveUniqueCodeDao.countOrderUniqueCode(staff, queryParams));
        }
        return shareUrlVO;
    }

    @AllArgsConstructor
    enum DateType {
        PRINT_TIME(0),
        CREATED(1),
        ;
        @Getter
        private Integer type;
    }

    @AllArgsConstructor
    enum DateCondition {
        CURRENT_DAY(1),
        EXCLUDE_CURRENT_DAY(2),
        ;
        @Getter
        private Integer type;
    }

    @Override
    public List<JSONObject> getListByCompanyIdAndSupplierId(Staff staff, Long supplierId) {
        return supplierUserService.getListByCompanyIdAndSupplierId(staff.getCompanyId(), supplierId);
    }

    @Override
    public void unBind(Staff staff, Long supplierId, String unionId) {
        supplierUserService.unBind(staff.getCompanyId(), supplierId, unionId);
    }
}

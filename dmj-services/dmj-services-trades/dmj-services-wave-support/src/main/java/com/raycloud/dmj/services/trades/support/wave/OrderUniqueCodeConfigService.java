package com.raycloud.dmj.services.trades.support.wave;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.dao.wave.OrderUniqueCodeConfigDao;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.SellerFlagEnum;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.OrderUniqueCodeConfig;
import com.raycloud.dmj.domain.wave.OrderUniqueCodeOffShelfConfig;
import com.raycloud.dmj.domain.wave.enums.*;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.trades.wave.IOrderUniqueCodeConfigService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.utils.wms.DataUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: qingfeng
 * @Description: 订单唯一码配置服务
 * @Date: 2021-01-28 14:28
 */
@Service
public class OrderUniqueCodeConfigService implements IOrderUniqueCodeConfigService {

    private Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private OrderUniqueCodeConfigDao orderUniqueCodeConfigDao;
    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;
    @Resource
    IOpLogService opLogService;
    @Resource
    private ICompanyService companyService;

    @Override
    @Cacheable(value = "defaultCache#600", key = "'unique_code_config_cache_' + #staff.companyId")
    public OrderUniqueCodeConfig get(Staff staff) {
        return getOrCreate(staff);
    }

    @Override
    public OrderUniqueCodeConfig getWithoutCache(Staff staff) {
        return getOrCreate(staff);
    }

    private OrderUniqueCodeConfig getOrCreate(Staff staff) {
        OrderUniqueCodeConfig config = orderUniqueCodeConfigDao.get(staff);
        if (config == null) {
            config = new OrderUniqueCodeConfig();
            config.setCompanyId(staff.getCompanyId());
            // 唯一码复用新公司默认开启
            config.setExtConfigMap(getExtConfigMap4Create());
            orderUniqueCodeConfigDao.insert(staff, config);
        }
        config.setOffShelfConfig(StringUtils.isNotEmpty(config.getOffShelfConfigJson()) ? JSONObject.parseObject(config.getOffShelfConfigJson(), OrderUniqueCodeOffShelfConfig.class) : null);
        // 唯一码复用新公司默认开启
        boolean generateReuse = config.getExtConfigMap() != null && config.getExtConfigMap().get("generateReuse") == null && isNewCompany4GenerateReuse(staff);
        if (generateReuse) {
            config.getExtConfigMap().put("generateReuse", 1);
        }
        // "指定异常订单，允许强制发货"配置格式转换
        transferIgnoreExceptNew(config);
        return config;
    }

    /**
     * 旧指定异常订单配置转新指定异常订单配置
     *
     * @param config
     */
    private void transferIgnoreExceptNew(OrderUniqueCodeConfig config) {
        String ignoreExceptInfo = config.getIgnoreExceptInfo();
        if (StringUtils.isEmpty(ignoreExceptInfo)) {
            config.setIgnoreExceptAutoCancel("");
            config.setIgnoreExceptAllowConsign("");
            return;
        }
        // 指定异常订单，允许强制发货
        StringBuilder ignoreExceptAllowConsign = new StringBuilder();
        // 指定异常订单，验货自动取消异常
        StringBuilder ignoreExceptAutoCancel = new StringBuilder();
        for (String ignoreExcept : ignoreExceptInfo.split(",")) {
            if (StringUtils.isEmpty(ignoreExcept) || !ignoreExcept.contains(":")) {
                continue;
            }
            String[] s = ignoreExcept.split(":");
            if (Objects.equal(CommonConstants.JUDGE_NO, Integer.valueOf(s[1]))) {
                ignoreExceptAutoCancel.append(s[0]).append(",");
                continue;
            }
            ignoreExceptAllowConsign.append(s[0]).append(",");
        }
        if (ignoreExceptAutoCancel.length() != 0) {
            ignoreExceptAutoCancel.deleteCharAt(ignoreExceptAutoCancel.length() - 1);
        }
        if (ignoreExceptAllowConsign.length() != 0) {
            ignoreExceptAllowConsign.deleteCharAt(ignoreExceptAllowConsign.length() - 1);
        }
        config.setIgnoreExceptAutoCancel(ignoreExceptAutoCancel.toString());
        config.setIgnoreExceptAllowConsign(ignoreExceptAllowConsign.toString());
    }

    private Map<String, Object> getExtConfigMap4Create() {
        Map<String, Object> extConfigMap = Maps.newHashMap();
        extConfigMap.put("generateReuse", 1);
        // 后面新公司配置默认开启的往这里加
        return extConfigMap;
    }

    private boolean isNewCompany4GenerateReuse(Staff staff) {
        Long companyId = staff.getCompanyId();
        if (!DataUtils.checkLongNotEmpty(companyId)) {
            return false;
        }
        try {
            Company company = companyService.queryById(companyId);
            if (company != null && company.getCreated() != null && company.getCreated().after(new SimpleDateFormat("yyyy-MM-dd").parse("2024-10-20"))) {
                return true;
            }
        } catch (Exception e) {
            logger.error("唯一码复用判断新公司报错", e);
        }
        return false;
    }

    @Override
    @CacheEvict(value = "defaultCache", key = "'unique_code_config_cache_' + #staff.companyId")
    @Transactional
    public void save(Staff staff, OrderUniqueCodeConfig config) {
        saveConfig(staff, config);
    }

    @Override
    public List<Long> queryAllCompany(Integer dbKey) {
        return orderUniqueCodeConfigDao.queryAllCompany(dbKey);
    }

    private void saveConfig(Staff staff, OrderUniqueCodeConfig config) {
        Assert.notNull(config, "要保存的配置不能为空！");
        // 获取已有配置，没有就新建一个新的返回
        OrderUniqueCodeConfig oldConfig = getOrCreate(staff);
        // 获取交易额外配置
        TradeConfig oldTradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        logger.debug(LogHelper.buildLog(staff, String.format("保存唯一码配置, before: %s, after: %s", JSONObject.toJSONString(oldConfig), JSONObject.toJSONString(config))));
        // 校验下架模式配置
        saveOffShelfConfig(config);
        // 对比扩展配置，并赋值
        incrementSaveExtConfigs(config, oldConfig);
        // "指定异常订单，允许强制发货"配置格式转换
        transferIgnoreExceptOld(config, oldConfig);
        // 更新入库
        orderUniqueCodeConfigDao.update(staff, config);
        if (config.getNoSaveConfigLog() == null || !config.getNoSaveConfigLog()) {
            // 保存修改日志
            saveUpdateLog(staff, config, oldConfig, oldTradeConfig);
            // 供应商分类不参与生成标签修改
            saveSupplierCategoryUpdateLog(staff, config.getExtConfigMap(), oldConfig.getExtConfigMap());
        }
    }

    /**
     * 新指定异常订单配置转旧指定异常订单配置
     *
     * @param config
     */
    private void transferIgnoreExceptOld(OrderUniqueCodeConfig config, OrderUniqueCodeConfig oldConfig) {
        // EX_SUITE_QUANTITY_CHANGE:1,EXCEP_HALT:0,EXCEP_ITEM_RELATION_MODIFIED:1
        StringBuilder newIgnoreExceptInfo = new StringBuilder();
        // EX_SUITE_QUANTITY_CHANGE,EXCEP_HALT,EXCEP_ITEM_RELATION_MODIFIED
        StringBuilder newIgnoreExceptIds = new StringBuilder();
        // 就配置转换新格式
        transferIgnoreExceptNew(oldConfig);
        // 指定异常订单，允许强制发货
        String[] ignoreExceptAllowConsign;
        // 为空不更新
        if (config.getIgnoreExceptAllowConsign() == null && StringUtils.isNotEmpty(oldConfig.getIgnoreExceptAllowConsign())) {
            config.setIgnoreExceptAllowConsign(oldConfig.getIgnoreExceptAllowConsign());
        }
        if (StringUtils.isNotEmpty(config.getIgnoreExceptAllowConsign())) {
            ignoreExceptAllowConsign = config.getIgnoreExceptAllowConsign().split(",");
            for (String str : ignoreExceptAllowConsign) {
                if (StringUtils.isEmpty(str)) {
                    continue;
                }
                newIgnoreExceptInfo.append(str).append(":1,");
                newIgnoreExceptIds.append(str).append(",");
            }
        }
        // 指定异常订单，验货自动取消异常
        String[] ignoreExceptAutoCancel;
        // 为空不更新
        if (config.getIgnoreExceptAutoCancel() == null && StringUtils.isNotEmpty(oldConfig.getIgnoreExceptAutoCancel())) {
            config.setIgnoreExceptAutoCancel(oldConfig.getIgnoreExceptAutoCancel());
        }
        if (StringUtils.isNotEmpty(config.getIgnoreExceptAutoCancel())) {
            ignoreExceptAutoCancel = config.getIgnoreExceptAutoCancel().split(",");
            for (String str : ignoreExceptAutoCancel) {
                if (StringUtils.isEmpty(str)) {
                    continue;
                }
                newIgnoreExceptInfo.append(str).append(":0,");
                newIgnoreExceptIds.append(str).append(",");
            }
        }
        if (newIgnoreExceptInfo.length() != 0) {
            newIgnoreExceptInfo.deleteCharAt(newIgnoreExceptInfo.length() - 1);
        }
        if (newIgnoreExceptIds.length() != 0) {
            newIgnoreExceptIds.deleteCharAt(newIgnoreExceptIds.length() - 1);
        }
        config.setIgnoreExceptInfo(newIgnoreExceptInfo.toString());
        config.setIgnoreExceptIds(newIgnoreExceptIds.toString());
    }

    private void saveSupplierCategoryUpdateLog(Staff staff, Map<String, Object> newExtConfigMap, Map<String, Object> oldExtConfigMap) {
        if (newExtConfigMap == null || oldExtConfigMap == null) {
            return;
        }
        boolean flag = Objects.equal(newExtConfigMap.get("supplierCategoryIds"), oldExtConfigMap.get("supplierCategoryIds")) &&
                Objects.equal(newExtConfigMap.get("notGenerateCodeSupplierCategoryType"), oldExtConfigMap.get("notGenerateCodeSupplierCategoryType"));
        if (flag) {
            return;
        }
        addUpdateLog(staff, "保存唯一码配置，指定供应商分类，不参与生成标签修改");
    }

    private boolean preJudgeParams(Staff staff, OrderUniqueCodeConfig newConf, OrderUniqueCodeConfig oldConf, TradeConfig oldTradeConfig) {
        if (newConf.getOffShelfConfig() != null &&
                !java.util.Objects.equals(oldConf.getOffShelfConfig(), newConf.getOffShelfConfig())) {
            opLogService.record(staff, fillUpdateLog(staff, "保存唯一码配置，下架模式修改"));
        }
        if (newConf.getGetOutSidPlatformTypes() != null &&
                !java.util.Objects.equals(oldConf.getGetOutSidPlatformTypes(), newConf.getGetOutSidPlatformTypes())) {
            opLogService.record(staff, fillUpdateLog(staff, "保存唯一码配置，指定平台，自动获取快递单号修改"));
        }
        if (newConf.getInterceptTagIds() != null &&
                !java.util.Objects.equals(oldConf.getInterceptTagIds(), newConf.getInterceptTagIds())) {
            opLogService.record(staff, fillUpdateLog(staff, "保存唯一码配置，指定标签的订单，不允许发货修改"));
        }
        if (newConf.getItemOutShortcutKey() != null &&
                !java.util.Objects.equals(oldConf.getItemOutShortcutKey(), newConf.getItemOutShortcutKey())) {
            opLogService.record(staff, fillUpdateLog(staff, "保存唯一码配置，验货出库设置快捷键修改"));
        }
        if (newConf.getErrorMsgClear() == null) {
            if (newConf.getExtConfigMap() != null) {
                if (newConf.getExtConfigMap().get("splitInterceptTagIds") != null &&
                        !java.util.Objects.equals(oldConf.getExtConfigMap().get("splitInterceptTagIds"), newConf.getExtConfigMap().get("splitInterceptTagIds"))) {
                    opLogService.record(staff, fillUpdateLog(staff, "保存唯一码配置，指定标签的订单，不允许拆分修改"));
                }
                boolean voiceFlag = (newConf.getExtConfigMap().get("multiVoice") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("multiVoice"), newConf.getExtConfigMap().get("multiVoice"))) ||
                        (newConf.getExtConfigMap().get("suitVoice") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("suitVoice"), newConf.getExtConfigMap().get("suitVoice"))) ||
                        (newConf.getExtConfigMap().get("positionNoVoice") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("positionNoVoice"), newConf.getExtConfigMap().get("positionNoVoice"))) ||
                        (newConf.getExtConfigMap().get("positionStockRegionVoice") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("positionStockRegionVoice"), newConf.getExtConfigMap().get("positionStockRegionVoice"))) ||
                        (newConf.getExtConfigMap().get("openPackExpressVoiceHints") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("openPackExpressVoiceHints"), newConf.getExtConfigMap().get("openPackExpressVoiceHints"))) ||
                        (newConf.getExtConfigMap().get("openGiftVoiceHints") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("openGiftVoiceHints"), newConf.getExtConfigMap().get("openGiftVoiceHints"))) ||
                        (newConf.getExtConfigMap().get("openMessageVoiceHints") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("openMessageVoiceHints"), newConf.getExtConfigMap().get("openMessageVoiceHints"))) ||
                        (newConf.getExtConfigMap().get("openRemarkVoiceHints") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("openRemarkVoiceHints"), newConf.getExtConfigMap().get("openRemarkVoiceHints"))) ||
                        (newConf.getExtConfigMap().get("openInvoiceVoiceHints") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("openInvoiceVoiceHints"), newConf.getExtConfigMap().get("openInvoiceVoiceHints"))) ||
                        (newConf.getExtConfigMap().get("openCompleteVoice") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("openCompleteVoice"), newConf.getExtConfigMap().get("openCompleteVoice"))) ||
                        (newConf.getExtConfigMap().get("openShopNickNameVoice") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("openShopNickNameVoice"), newConf.getExtConfigMap().get("openShopNickNameVoice"))) ||
                        (newConf.getExtConfigMap().get("itemNumVoice") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("itemNumVoice"), newConf.getExtConfigMap().get("itemNumVoice"))) ||
                        (newConf.getExtConfigMap().get("haveOrderVoice") != null && !java.util.Objects.equals(oldConf.getExtConfigMap().get("haveOrderVoice"), newConf.getExtConfigMap().get("haveOrderVoice")));
                if (voiceFlag) {
                    opLogService.record(staff, fillUpdateLog(staff, "保存唯一码配置，语音播报高级配置修改"));
                }
            }
            if (newConf.getTradeExtendConfig() != null && !java.util.Objects.equals(getTradeExtendConfigValue(oldTradeConfig, "changeItemException"), getTradeExtendConfigValue(newConf.getTradeExtendConfig(), "changeItemException"))) {
                opLogService.record(staff, fillUpdateLog(staff, "保存唯一码配置，指定异常的订单，允许智能换商品修改"));
            }
            return true;
        }
        return false;
    }

    private void fillLog4IgnoreExcept(Staff staff, OrderUniqueCodeConfig newConf, OrderUniqueCodeConfig oldConf, List<OpLog> opLogs) {
        String newIgnoreExceptAutoCancel = newConf.getIgnoreExceptAutoCancel();
        String newIgnoreExceptAllowConsign = newConf.getIgnoreExceptAllowConsign();

        String oldIgnoreExceptAutoCancel = oldConf.getIgnoreExceptAutoCancel();
        String oldIgnoreExceptAllowConsign = oldConf.getIgnoreExceptAllowConsign();

        fillOpLogs(newIgnoreExceptAutoCancel, oldIgnoreExceptAutoCancel,
                opLogs, "保存唯一码配置，指定异常订单，验货自动取消异常修改为【%s】",
                newIgnoreExceptAutoCancel, staff);
        fillOpLogs(newIgnoreExceptAllowConsign, oldIgnoreExceptAllowConsign,
                opLogs, "保存唯一码配置，指定异常订单，允许强制发货修改为【%s】",
                newIgnoreExceptAllowConsign, staff);
    }

    private void saveUpdateLog(Staff staff, OrderUniqueCodeConfig newConf, OrderUniqueCodeConfig oldConf, TradeConfig oldTradeConfig) {
        preJudgeParams(staff, newConf, oldConf, oldTradeConfig);
        List<OpLog> opLogs = Lists.newArrayList();
        // 指定异常订单日志填充
        fillLog4IgnoreExcept(staff, newConf, oldConf, opLogs);
        for (OrderUniqueCodeConfigEnum configEnum : OrderUniqueCodeConfigEnum.values()) {
            try {
                switch (configEnum.getId()) {
                    case 1:
                        // 分拣货位订单顺序
                        fillOpLogs(newConf.getTradeSortRule(), oldConf.getTradeSortRule(),
                                opLogs, "保存唯一码配置，分拣货位订单顺序修改为【%s】",
                                TradeSortRuleEnum.getRemarkByKey(newConf.getTradeSortRule()), staff);
                        break;
                    case 2:
                        // 分拣货位显示样式
                        fillOpLogs(newConf.getPositionShowType(), oldConf.getPositionShowType(),
                                opLogs, "保存唯一码配置，分拣货位显示样式修改为【%s】",
                                PositionShowTypeEnum.getRemarkByKey(newConf.getPositionShowType()), staff);
                        break;
                    case 3:
                        // 分拣货位显示订单序号
                        fillOpLogs(newConf.getPositionShowSort(), oldConf.getPositionShowSort(),
                                opLogs, "保存唯一码配置，分拣货位显示订单序号：%s",
                                PositionShowSortEnum.getRemarkByKey(newConf.getPositionShowSort()), staff);
                        break;
                    case 4:
                        // 开启“隔天重新分配”

                        fillOpLogs(newConf.getPositionOccupyCalculateType(), oldConf.getPositionOccupyCalculateType(),
                                opLogs, "保存唯一码配置，开启“隔天重新分配” 修改为【%s】",
                                PositionOccupyCalculateTypeEnum.getRemarkByKey(newConf.getPositionOccupyCalculateType()), staff);
                        break;
                    case 5:
                        // 分拣货位货位顺序
                        fillOpLogs(newConf.getPositionAllocateType(), oldConf.getPositionAllocateType(),
                                opLogs, "保存唯一码配置，分拣货位货位顺序修改为【%s】",
                                PositionAllocateTypeEnum.getRemarkByKey(newConf.getPositionAllocateType()), staff);
                        break;
                    case 6:
                        // 下架成功后，订单自动标记旗帜
                        Integer openOffShelfFlagNew = (Integer) newConf.getExtConfigMap().get("openOffShelfFlag");
                        Integer openOffShelfFlagOld = (Integer) oldConf.getExtConfigMap().get("openOffShelfFlag");
                        Integer offShelfFlagNew = newConf.getOffShelfFlag();
                        Integer offShelfFlagOld = oldConf.getOffShelfFlag();
                        boolean checkBox = openOffShelfFlagNew == null || openOffShelfFlagNew.equals(openOffShelfFlagOld);
                        boolean offShelfFlag= offShelfFlagNew == null || offShelfFlagNew.equals(offShelfFlagOld);
                        String logStr = "";
                        String content = "";
                        if (checkBox && offShelfFlag) {
                            break;
                        }
                        if (!checkBox && Objects.equal(openOffShelfFlagNew, 0)) {
                            logStr = "保存唯一码配置，下架成功后，订单自动标记旗帜: %s";
                            content = "取消勾选";
                        } else if (!checkBox && Objects.equal(openOffShelfFlagNew, 1)) {
                            logStr = "保存唯一码配置，下架成功后，订单自动标记旗帜: %s";
                            content = "勾选";
                            if (!offShelfFlag) {
                                content = content +"，旗帜修改为: " + SellerFlagEnum.getMsg(newConf.getOffShelfFlag().longValue());
                            }
                        } else if (!offShelfFlag) {
                            logStr = "保存唯一码配置，下架成功后，订单自动标记旗帜，旗帜修改为: %s";
                            content = SellerFlagEnum.getMsg(newConf.getOffShelfFlag().longValue());
                        }
                        opLogs.add(fillUpdateLog(staff, String.format(logStr, content)));
                        break;
                    case 7:
                        // 下架模式
                        fillOpLogs(newConf.getOffShelfMode(), oldConf.getOffShelfMode(),
                                opLogs, "保存唯一码配置，下架模式修改为【%s】",
                                OffShelfModeEnum.getRemarkByKey(newConf.getOffShelfMode()), staff);
                        break;
                    case 8:
                        // 家里标签，自动拣选下架
                        fillOpLogs(newConf.getPrintChangeStatus(), oldConf.getPrintChangeStatus(),
                                opLogs, "保存唯一码配置，家里标签，自动拣选下架：%s",
                                PrintChangeStatusEnum.getRemarkByKey(newConf.getPrintChangeStatus()), staff);
                        break;
                    case 9:
                        // 拆分后，未到货的订单，自动反审核
                        fillOpLogs(newConf.getSplitTradeNeedUnAudit(), oldConf.getSplitTradeNeedUnAudit(),
                                opLogs, "保存唯一码配置，拆分后，未到货的订单，自动反审核：%s",
                                SplitTradeNeedUnAuditEnum.getRemarkByKey(newConf.getSplitTradeNeedUnAudit()), staff);
                        break;
                    case 11:
                        // 已生成采购单的标签，允许更新供应商
                        fillOpLogs(newConf.getRefreshSupplierWithBusinessNo(), oldConf.getRefreshSupplierWithBusinessNo(),
                                opLogs, "保存唯一码配置，已生成采购单的标签，允许更新供应商：%s",
                                RefreshSupplierWithBusinessNoEnum.getRemarkByKey(newConf.getRefreshSupplierWithBusinessNo()), staff);
                        break;
                    case 12:
                        // 分拣货位分配节点
                        fillOpLogs(newConf.getPositionNoWay(), oldConf.getPositionNoWay(),
                                opLogs, "保存唯一码配置，分拣货位分配节点修改为【%s】",
                                PositionNoWayEnum.getRemarkByKey(newConf.getPositionNoWay()), staff);
                        break;
                    case 14:
                        // 未绑定订单的标签，允许匹配新的单件订单
                        fillOpLogs(newConf.getReuseRange(), oldConf.getReuseRange(),
                                opLogs, "保存唯一码配置，解绑标签复用范围修改为【%s】",
                                ReuseRangeEnum.getRemarkByKey(newConf.getReuseRange()), staff);
                        break;
                    case 15:
                        // 反审核/已作废/交易关闭的商品唯一码标签可以继续匹配新的单件订单（已审核未打印，未加入波次且未生成唯一码的订单)
                        fillOpLogs(newConf.getUniqueCodeMultiplex(), oldConf.getUniqueCodeMultiplex(),
                                opLogs, "保存唯一码配置，反审核/已作废/交易关闭的商品唯一码标签可以继续匹配新的单件订单（已审核未打印，未加入波次且未生成唯一码的订单)：%s",
                                UniqueCodeMultiplexEnum.getRemarkByKey(newConf.getUniqueCodeMultiplex()), staff);
                        break;
                    case 18:
                        // 订单打印后，清除错误信息弹窗
                        fillOpLogs(newConf.getErrorMsgClear(), oldConf.getErrorMsgClear(),
                                opLogs, "保存唯一码配置，订单打印后，清除错误信息弹窗：%s",
                                ErrorMsgClearEnum.getRemarkByKey(newConf.getErrorMsgClear()), staff);
                        break;
                    case 20:
                        // 验货出库拆分的订单，先取消标签，再生成新标签
                        fillOpLogs(newConf.getSplitNewTag(), oldConf.getSplitNewTag(),
                                opLogs, "保存唯一码配置，验货出库拆分的订单，先取消标签，再生成新标签：%s",
                                SplitNewTagEnum.getRemarkByKey(newConf.getSplitNewTag()), staff);
                        break;
                    case 22:
                        // 同商品多件订单，允许拆分
                        fillOpLogs(newConf.getSameSkuAllowSplit(), oldConf.getSameSkuAllowSplit(),
                                opLogs, "保存唯一码配置，同商品多件订单，允许拆分：%s",
                                SameSkuAllowSplitEnum.getRemarkByKey(newConf.getSameSkuAllowSplit()), staff);
                        break;
                    case 23:
                        // 开启“套件拆分”，允许套件转为单品后拆分
                        fillOpLogs(newConf.getSuitSplit(), oldConf.getSuitSplit(),
                                opLogs, "保存唯一码配置，开启“套件拆分”，允许套件转为单品后拆分：%s",
                                SuitSplitEnum.getRemarkByKey(newConf.getSuitSplit()), staff);
                        break;
                    case 24:
                        // 开启“单多混扫”模式
                        fillOpLogs(newConf.getExtConfigMap().get("hybridScanType"),
                                oldConf.getExtConfigMap().get("hybridScanType"),
                                opLogs, "保存唯一码配置，开启“单多混扫”模式：%s",
                                HybridScanTypeEnum.getRemarkByKey(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("hybridScanType")))), staff);
                        break;
                    case 30:
                        // 工厂商品，暂存区不参与更换
                        fillOpLogs(getTradeExtendConfigValue(newConf.getTradeExtendConfig(), "changeItemSubsStagingStock"),
                                getTradeExtendConfigValue(oldTradeConfig, "changeItemSubsStagingStock"),
                                opLogs, "保存唯一码配置，工厂商品，暂存区不参与更换：%s",
                                ChangeItemSubsStagingStockEnum.getRemarkByKey(Integer.valueOf(String.valueOf(getTradeExtendConfigValue(newConf.getTradeExtendConfig(), "changeItemSubsStagingStock")))), staff);
                        break;
                    case 31:
                        // 工厂商品，库存不足时，也换成工厂商品
                        fillOpLogs(getTradeExtendConfigValue(newConf.getTradeExtendConfig(), "allowChangeItemWhenInsufficient"),
                                getTradeExtendConfigValue(oldTradeConfig, "allowChangeItemWhenInsufficient"),
                                opLogs, "保存唯一码配置，工厂商品，库存不足时，也换成工厂商品：%s",
                                AllowChangeItemWhenInsufficientEnum.getRemarkByKey(Integer.valueOf(String.valueOf(getTradeExtendConfigValue(newConf.getTradeExtendConfig(), "allowChangeItemWhenInsufficient")))), staff);
                        break;
                    case 32:
                        // 唯一码收货语音播报(唯一码类型(单/多/备))
                        Integer oldPlayCodeType = Integer.valueOf(String.valueOf(oldConf.getExtConfigMap().get("playCodeType")));
                        Integer newPlayCodeType = Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("playCodeType")));
                        // 唯一码收货语音播报(到货到齐)
                        Integer oldPlayReceiveStatus = Integer.valueOf(String.valueOf(oldConf.getExtConfigMap().get("playReceiveStatus")));
                        Integer newPlayReceiveStatus = Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("playReceiveStatus")));
                        // 唯一码收货语音播报(快递公司)
                        Integer oldPlayExpressName = Integer.valueOf(String.valueOf(oldConf.getExtConfigMap().get("playExpressName")));
                        Integer newPlayExpressName = Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("playExpressName")));
                        // 唯一码收货语音播报(退款中)
                        Integer oldPlayUniqueCodeItemRefund = Integer.valueOf(String.valueOf(oldConf.getExtConfigMap().get("playUniqueCodeItemRefund")));
                        Integer newPlayUniqueCodeItemRefund = Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("playUniqueCodeItemRefund")));
                        // 唯一码收货语音播报(交易关闭)
                        boolean playFlag = !newPlayCodeType.equals(oldPlayCodeType) ||
                                !newPlayReceiveStatus.equals(oldPlayReceiveStatus) ||
                                !newPlayExpressName.equals(oldPlayExpressName) ||
                                !newPlayUniqueCodeItemRefund.equals(oldPlayUniqueCodeItemRefund);
                        if (playFlag) {
                            StringBuilder playBuffer = new StringBuilder();
                            if (newPlayCodeType == 1) {
                                playBuffer.append("唯一码类型(单/多/备),");
                            }
                            if (newPlayReceiveStatus == 1) {
                                playBuffer.append("到货到齐,");
                            }
                            if (newPlayExpressName == 1) {
                                playBuffer.append("快递公司,");
                            }
                            if (newPlayUniqueCodeItemRefund == 1) {
                                playBuffer.append("退款中,");
                            }
                            String playStr = playBuffer.toString();
                            if (StringUtils.isNotBlank(playStr)) {
                                playStr = playStr.substring(0, playStr.length() - 1);
                            }
                            opLogs.add(fillUpdateLog(staff, String.format("保存唯一码配置，语音播报修改为【%s】", playStr)));
                        }
                        break;
                    case 35:
                        // 售后转发
                        fillOpLogs(newConf.getExtConfigMap().get("asTransmitPrintUcStatus"),
                                oldConf.getExtConfigMap().get("asTransmitPrintUcStatus"),
                                opLogs, "保存唯一码配置，售后转发新标签状态：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("asTransmitPrintUcStatus"))), 1) ? "已拣选" : "已退回", staff);
                        break;
                    case 36:
                        // 到货清点打印配置
                        fillOpLogs(newConf.getExtConfigMap().get("arrivedCheckPrintOption"),
                                oldConf.getExtConfigMap().get("arrivedCheckPrintOption"),
                                opLogs, "保存唯一码配置，到货清点打印配置：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("arrivedCheckPrintOption"))), 1) ? "单件仅打印标签" : "单件仅打印快递单", staff);
                        break;
                    case 37:
                        // 打印标签后，自动收货
                        fillOpLogs(newConf.getExtConfigMap().get("arrivedCheckAutoReceive"),
                                oldConf.getExtConfigMap().get("arrivedCheckAutoReceive"),
                                opLogs, "保存唯一码配置，打印标签后，自动收货：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("arrivedCheckAutoReceive"))), 1) ? "勾选" : "取消", staff);
                        break;
                    case 38:
                        // 到货清点列表排序类型
                        fillOpLogs(newConf.getExtConfigMap().get("arrivedCheckListSortType"),
                                oldConf.getExtConfigMap().get("arrivedCheckListSortType"),
                                opLogs, "保存唯一码配置，列表排序修改为%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("arrivedCheckListSortType"))), 1) ? "【供应商-规格商家编码】" : "【供应商-主商品-颜色尺码】", staff);
                        break;
                    case 39:
                        // 爆款打印订单顺序
                        Integer hotSalePrintTradeSort = Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("hotSalePrintTradeSort")));
                        fillOpLogs(newConf.getExtConfigMap().get("hotSalePrintTradeSort"),
                                oldConf.getExtConfigMap().get("hotSalePrintTradeSort"),
                                opLogs, "保存唯一码配置，爆款打印订单顺序修改为：【%s】",
                                Objects.equal(hotSalePrintTradeSort, 0) ? "优先店铺，再按付款时间升序" : Objects.equal(hotSalePrintTradeSort, 1) ? "优先加急，再按付款时间升序" : "优先加急，再按剩余时间升序", staff);
                        break;
                    case 40:
                        // 排除“免验”商品的单件爆款订单，不参与生成标签
                        fillOpLogs(newConf.getExtConfigMap().get("hotSalePrintExcludeNotCheck"),
                                oldConf.getExtConfigMap().get("hotSalePrintExcludeNotCheck"),
                                opLogs, "保存唯一码配置，排除“免验”商品的单件爆款订单，不参与生成标签：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("hotSalePrintExcludeNotCheck"))), 1) ? "勾选" : "取消勾选", staff);
                        break;
                    case 41:
                        // 生成唯一码提取直播属性
                        fillOpLogs(newConf.getExtConfigMap().get("generateLiveProperty"),
                                oldConf.getExtConfigMap().get("generateLiveProperty"),
                                opLogs, "保存唯一码配置，生成唯一码提取直播属性：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("generateLiveProperty"))), 1) ? "勾选" : "取消勾选", staff);
                        break;
                    case 42:
                        // 后置绑定，开启视频监控
                        fillOpLogs(newConf.getExtConfigMap().get("postBindOpenVideo"),
                                oldConf.getExtConfigMap().get("postBindOpenVideo"),
                                opLogs, "保存唯一码配置，后置绑定，开启视频监控：%s",
                                Boolean.parseBoolean(String.valueOf(newConf.getExtConfigMap().get("postBindOpenVideo"))) ? "勾选" : "取消勾选", staff);
                        break;
                    case 43:
                        // PDA混放上架解绑外采唯一码范围
                        Object singleOldValue = oldConf.getExtConfigMap().get("singleOnShelfUnbindUniqueCode");
                        Object singleNewValue = newConf.getExtConfigMap().get("singleOnShelfUnbindUniqueCode");
                        Object multiOldValue = oldConf.getExtConfigMap().get("multiOnShelfUnbindUniqueCode");
                        Object multiNewValue = newConf.getExtConfigMap().get("multiOnShelfUnbindUniqueCode");
                        boolean flag = (singleNewValue != null && !singleNewValue.equals(singleOldValue)) || (multiNewValue != null && !multiNewValue.equals(multiOldValue));
                        if (flag) {
                            StringBuilder buffer = new StringBuilder();
                            if (Objects.equal(Integer.valueOf(String.valueOf(singleNewValue)), 1)) {
                                buffer.append("单件订单,");
                            }
                            if (Objects.equal(Integer.valueOf(String.valueOf(multiNewValue)), 1)) {
                                buffer.append("多件订单,");
                            }
                            String str = buffer.toString();
                            if (StringUtils.isNotBlank(str)) {
                                str = str.substring(0, str.length() - 1);
                            }
                            opLogs.add(fillUpdateLog(staff, String.format("保存唯一码配置，PDA混放上架解绑外采唯一码范围修改为:【%s】", str)));
                        }
                        break;
                    case 44:
                        // 后置打印，开启视频监控
                        fillOpLogs(newConf.getExtConfigMap().get("postPrintOpenVideo"),
                                oldConf.getExtConfigMap().get("postPrintOpenVideo"),
                                opLogs, "保存唯一码配置，后置打印，开启视频监控：%s",
                                Boolean.parseBoolean(String.valueOf(newConf.getExtConfigMap().get("postPrintOpenVideo"))) ? "勾选" : "取消勾选", staff);
                        break;
                    case 47:
                        // 自动复用原码
                        fillOpLogs(newConf.getExtConfigMap().get("generateReuse"),
                                oldConf.getExtConfigMap().get("generateReuse"),
                                opLogs, "保存唯一码配置，自动复用原码：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("generateReuse"))), 1) ? "勾选" : "取消勾选", staff);
                        break;
                    case 48:
                        // 收货超时提醒
                        fillOpLogs(newConf.getExtConfigMap().get("receiveUniqueCodeOverTimeConfig"),
                                oldConf.getExtConfigMap().get("receiveUniqueCodeOverTimeConfig"),
                                opLogs, "保存唯一码配置，收货超时提醒：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("receiveUniqueCodeOverTimeConfig"))), 1) ? "勾选" : "取消勾选", staff);
                        break;
                    case 49:
                        // 收货超时处理配置-基准时间类型
                        fillOpLogs(newConf.getExtConfigMap().get("receiveUniqueCodeOverTimeType"),
                                oldConf.getExtConfigMap().get("receiveUniqueCodeOverTimeType"),
                                opLogs, "保存唯一码配置，收货超时处理配置-基准时间类型修改为：【%s】",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("receiveUniqueCodeOverTimeType"))), 1) ? "唯一码打印时间" : "唯一码生成时间", staff);
                        break;
                    case 50:
                        // 收货超时处理配置-超时时长
                        fillOpLogs(newConf.getExtConfigMap().get("receiveUniqueCodeOverTimeLength"),
                                oldConf.getExtConfigMap().get("receiveUniqueCodeOverTimeLength"),
                                opLogs, "保存唯一码配置，收货超时处理配置-超时时长修改为：【%s】",
                                String.valueOf(newConf.getExtConfigMap().get("receiveUniqueCodeOverTimeLength")), staff);
                        break;
                    case 51:
                        // 收货超时处理配置-超时处理类型
                        fillOpLogs(newConf.getExtConfigMap().get("receiveUniqueCodeOverTimeHandleType"),
                                oldConf.getExtConfigMap().get("receiveUniqueCodeOverTimeHandleType"),
                                opLogs, "保存唯一码配置，收货超时处理配置-超时处理类型修改为：【%s】",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("receiveUniqueCodeOverTimeHandleType"))), 1) ? "拦截并播报" : "仅播报", staff);
                        break;
                    case 53:
                        // 拆分后，未验货的订单，自动反审核
                        fillOpLogs(newConf.getExtConfigMap().get("examineSplitUnAuditTrade"),
                                oldConf.getExtConfigMap().get("examineSplitUnAuditTrade"),
                                opLogs, "保存唯一码配置，拆分后，未验货的订单，自动反审核：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("examineSplitUnAuditTrade"))), 1) ? "勾选" : "取消勾选", staff);
                        break;
                    case 54:
                        // 拆分订单后，存在退款中商品再次拆分并反审核
                        fillOpLogs(newConf.getExtConfigMap().get("examineSplitContinueSplitRefunding"),
                                oldConf.getExtConfigMap().get("examineSplitContinueSplitRefunding"),
                                opLogs, "保存唯一码配置，拆分订单后，存在退款中商品再次拆分并反审核：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("examineSplitContinueSplitRefunding"))), 1) ? "勾选" : "取消勾选", staff);
                        break;
                    case 52:
                        // 售后转发 匹配订单范围（指定旗帜）
                        Object asTransmitSellerFlag = newConf.getExtConfigMap().get("asTransmitSellerFlag");
                        if (asTransmitSellerFlag == null) {
                            break;
                        }
                        String[] asTransmitSellerFlags = asTransmitSellerFlag.toString().split(",");
                        fillOpLogs(newConf.getExtConfigMap().get("asTransmitSellerFlag"),
                                oldConf.getExtConfigMap().get("asTransmitSellerFlag"),
                                opLogs, "保存唯一码配置，匹配订单范围（指定旗帜）修改为【%s】",
                                getLog4AsTransmitSellerFlag(asTransmitSellerFlags), staff);
                        break;
                    case 57:
                        // 自动作废标签
                        fillOpLogs(newConf.getExtConfigMap().get("uniqueCodeAutoCancel"),
                                oldConf.getExtConfigMap().get("uniqueCodeAutoCancel"),
                                opLogs, "保存唯一码配置，取消复制新增修改为：【%s】",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("uniqueCodeAutoCancel"))), 1) ? "开启" : "关闭", staff);
                        break;
                    case 58:
                        // 自动作废标签定时时间
                        fillOpLogs(newConf.getExtConfigMap().get("uniqueCodeAutoCancelTime"),
                                oldConf.getExtConfigMap().get("uniqueCodeAutoCancelTime"),
                                opLogs, "保存唯一码配置，取消复制新增执行时间修改为：【%s】",
                                String.valueOf(newConf.getExtConfigMap().get("uniqueCodeAutoCancelTime")), staff);
                        break;
                    case 55:
                        // 外采唯一码自动生成采购单
                        fillOpLogs(newConf.getExtConfigMap().get("autoCreatePurchaseOrder"),
                                oldConf.getExtConfigMap().get("autoCreatePurchaseOrder"),
                                opLogs, "保存唯一码配置，外采唯一码自动生成采购单：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("autoCreatePurchaseOrder"))), 1) ? "勾选" : "取消勾选", staff);
                        break;
                    case 56:
                        // 相同供应商自动合并
                        fillOpLogs(newConf.getExtConfigMap().get("sameSupplierNeedMerge"),
                                oldConf.getExtConfigMap().get("sameSupplierNeedMerge"),
                                opLogs, "保存唯一码配置，相同供应商自动合并：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("sameSupplierNeedMerge"))), 1) ? "勾选" : "取消勾选", staff);
                        break;
                    case 62:
                        fillOpLogs(newConf.getExtConfigMap().get("uniqueReceiveShowBigPic"),
                                oldConf.getExtConfigMap().get("uniqueReceiveShowBigPic"),
                                opLogs, "保存唯一码配置，到货扫描大图模式：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("uniqueReceiveShowBigPic"))), 1) ? "勾选" : "取消勾选", staff);
                        break;
                    case 63:
                        fillOpLogs(newConf.getExtConfigMap().get("uniqueReceiveShowBigPicType"),
                                oldConf.getExtConfigMap().get("uniqueReceiveShowBigPicType"),
                                opLogs, "保存唯一码配置，到货扫描图片显示：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("uniqueReceiveShowBigPicType"))), 1) ? "平台图片" : "系统图片", staff);
                        break;
                    case 59:
                        // 收货超时处理配置-记录商品备忘录
                        fillOpLogs(newConf.getExtConfigMap().get("logGoodsMemorandum"),
                                oldConf.getExtConfigMap().get("logGoodsMemorandum"),
                                opLogs, "保存唯一码配置，收货超时处理配置-记录商品备忘录修改为：【%s】",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("logGoodsMemorandum"))), 1) ? "开启" : "关闭", staff);
                        break;
                    case 60:
                        // 质检次品自动执行退档换货
                        fillOpLogs(newConf.getExtConfigMap().get("defectiveUniqueCodeAutoReturnExchange"),
                                oldConf.getExtConfigMap().get("defectiveUniqueCodeAutoReturnExchange"),
                                opLogs, "保存唯一码配置，质检次品自动执行退档换货修改为：【%s】",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("defectiveUniqueCodeAutoReturnExchange"))), 1) ? "开启" : "关闭", staff);
                        break;
                    case 61:
                        // 生成唯一码时，复用原码
                        fillOpLogs(newConf.getExtConfigMap().get("warehousePositionAutoFillZero"),
                                oldConf.getExtConfigMap().get("warehousePositionAutoFillZero"),
                                opLogs, "保存唯一码配置，新建分拣货位支持自动补0：%s",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("warehousePositionAutoFillZero"))), 1) ? "勾选" : "取消勾选", staff);
                        break;
                    case 65:
                        // 自定义首位数字开关
                        fillOpLogs(newConf.getExtConfigMap().get("customFirstDigitSwitch"),
                                oldConf.getExtConfigMap().get("customFirstDigitSwitch"),
                                opLogs, "保存唯一码配置，自定义首位数字开关修改为：【%s】",
                                Objects.equal(Integer.valueOf(String.valueOf(newConf.getExtConfigMap().get("customFirstDigitSwitch"))), 1) ? "开启" : "关闭", staff);
                        break;
                    case 64:
                        // 自定义首位数字
                        fillOpLogs(newConf.getExtConfigMap().get("customFirstDigitNum"),
                                oldConf.getExtConfigMap().get("customFirstDigitNum"),
                                opLogs, "保存唯一码配置，自定义首位数字修改为：【%s】",
                                String.valueOf(newConf.getExtConfigMap().get("customFirstDigitNum")), staff);
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                logger.debug(String.format("OrderUniqueCodeConfigService.saveUpdateLog error, msg:【%s】", e.getMessage()));
            }
        }
        if (CollectionUtils.isEmpty(opLogs)) {
            return;
        }
        opLogService.batchRecord(staff, opLogs);
    }

    private String getLog4AsTransmitSellerFlag(String[] asTransmitSellerFlags) {
        StringBuilder content = new StringBuilder();
        for (String asTransmitSellerFlag : asTransmitSellerFlags) {
            if (StringUtils.isEmpty(asTransmitSellerFlag)) {
                content.append(",");
                continue;
            }
            if (Objects.equal(asTransmitSellerFlag, "-1")) {
                content.append("无旗帜,");
                continue;
            }
            content.append(SellerFlagEnum.getMsg(Long.parseLong(asTransmitSellerFlag))).append(",");
        }
        return content.substring(0, content.length() - 1);
    }

    private void fillOpLogs(Object newConfValue,
                            Object oldConfValue,
                            List<OpLog> opLogs,
                            String logStr,
                            String fillStr,
                            Staff staff) {
        boolean flag = newConfValue == null || newConfValue.equals(oldConfValue);
        if (flag) {
            return;
        }
        opLogs.add(fillUpdateLog(staff, String.format(logStr, fillStr)));
    }

    private Object getTradeExtendConfigValue(TradeConfig tradeConfig, String key) {
        if (ObjectUtils.isEmpty(tradeConfig) || StringUtils.isBlank(tradeConfig.getTradeExtendConfig())) {
            return null;
        }
        return getTradeExtendConfigValue(tradeConfig.getTradeExtendConfig(), key);
    }

    private Object getTradeExtendConfigValue(String tradeExtendConfig, String key) {
        logger.info(String.format("保存唯一码配置,config:%s,key:%s", tradeExtendConfig, key));
        if (StringUtils.isBlank(tradeExtendConfig)) {
            return null;
        }
        Map<String, Object> map = JSON.parseObject(tradeExtendConfig, Map.class);
        return map.get(key);
    }

    private void saveOffShelfConfig(OrderUniqueCodeConfig config) {
        if (config.getOffShelfConfig() == null) {
            return;
        }
        List<OrderUniqueCodeOffShelfConfig.OffShelfShop> offShelfShopList = config.getOffShelfConfig().getOffShelfShopList();
        if (CollectionUtils.isNotEmpty(offShelfShopList)) {
            Set<Long> userIdSet = Sets.newHashSet();
            for (OrderUniqueCodeOffShelfConfig.OffShelfShop shop : offShelfShopList) {
                if (userIdSet.contains(shop.getUserId())) {
                    throw new IllegalArgumentException(String.format("店铺%s重复添加", shop.getShopName()));
                }
                userIdSet.add(shop.getUserId());
            }
        }
        config.setOffShelfConfigJson(JSONObject.toJSONString(config.getOffShelfConfig()));
    }

    private void buildUpdateLog(Staff staff, OrderUniqueCodeConfig newConf, OrderUniqueCodeConfig oldConf) {
        StringBuilder logContent = new StringBuilder("保存唯一码配置\n");
        addLogContent(logContent, "最后一次的流水号", newConf.getLastSerialNumber(), oldConf.getLastSerialNumber());
        addLogContent(logContent, "最后生成流水号的时间", DateUtils.datetime2Str(newConf.getLastTime()), DateUtils.datetime2Str(oldConf.getLastTime()));
        addLogContent(logContent, "反审核/已作废/交易关闭的商品唯一码标签可以继续匹配新的单件订单", newConf.getUniqueCodeMultiplex(), oldConf.getUniqueCodeMultiplex());
        addLogContent(logContent, "分配分拣货位规则", newConf.getTradeSortRule(), oldConf.getTradeSortRule());
        addLogContent(logContent, "设置异常订单允许强制发货类型", newConf.getIgnoreExceptIds(), oldConf.getIgnoreExceptIds());
        addLogContent(logContent, "最后的批次号", newConf.getBatchNo(), oldConf.getBatchNo());


        if (logContent.length() > 0) {
            addUpdateLog(staff, logContent.toString());
        }
    }

    private OpLog fillUpdateLog(Staff staff, String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        OpLog log = new OpLog();
        log.setDomain(OpLog.DOMAIN_TRADE);
        log.setAction("order.unique.code.config.update");
        log.setKey(staff.getCompanyId().toString());
        log.setCompanyId(staff.getCompanyId());
        log.setStaffName(staff.getName());
        log.setAccountName(staff.getAccountName());
        log.setContent(content);
        log.setStaffId(staff.getId());
        return log;
    }

    private void addUpdateLog(Staff staff, String content) {
        OpLog log = fillUpdateLog(staff, content);
        if (log == null) {
            return;
        }
        opLogService.record(staff, log);
    }

    private <T> void addLogContent(StringBuilder buf, String title, T newValue, T oldValue) {
        if (newValue == null) {
            return;
        }
        if (!Objects.equal(newValue, oldValue)) {
            if (oldValue == null) {
                buf.append(title).append(":").append(newValue);
            } else {
                buf.append(title).append(":").append(oldValue).append("->").append(newValue);
            }
        }
    }

    /**
     * 增量修改扩展配置
     */
    public void incrementSaveExtConfigs(OrderUniqueCodeConfig config, OrderUniqueCodeConfig oldConfig) {
        if (config == null || oldConfig == null) {
            return;
        }
        if (config.getExtConfigMap() == null || config.getExtConfigMap().isEmpty()) {
            config.setExtConfigMap(oldConfig.getExtConfigMap());
            return;
        }

        Map<String, Object> map = config.getExtConfigMap();
        Map<String, Object> oldMap = oldConfig.getExtConfigMap();
        HashMap<String, Object> result = new HashMap<>();
        for (OrderUniqueCodeExtConfigEnum extInfoEnum : OrderUniqueCodeExtConfigEnum.values()) {
            String key = extInfoEnum.getKey();
            // 前端传了值,如果为空字符串,则根据默认值类型判断是更新为空字符串还是默认值
            if (map.get(key) != null) {
                if (StringUtils.isEmpty(map.get(key).toString()) || Objects.equal("{}", map.get(key).toString())) {
                    if (Objects.equal(extInfoEnum.getDefaultValue(), map.get(key))) {
                        result.put(key, map.get(key));
                    } else {
                        result.put(key, extInfoEnum.getDefaultValue());
                    }
                } else {
                    result.put(key, map.get(key));
                }
            } else {
                // 前端未传值,则保留数据库原有配置,未配置则设置成默认值
                if (oldMap != null && oldMap.get(key) != null) {
                    result.put(key, oldMap.get(key));
                } else {
                    // 设置为默认值
                    result.put(key, extInfoEnum.getDefaultValue());
                }
            }
        }
        config.setExtConfigMap(result);
    }
}

package com.raycloud.dmj.services.trades.support.wave.business;

import com.google.common.collect.Lists;
import com.raycloud.dmj.dao.wave.WaveQueryDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.wms.AllocateGoodsRecord;
import com.raycloud.dmj.domain.wms.AssoGoodsSectionSku;
import com.raycloud.dmj.domain.wms.LogisticsOrder;
import com.raycloud.dmj.domain.wms.params.AssoGoodsSectionSkuParams;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeWaveService;
import com.raycloud.dmj.services.trades.support.wave.WaveLogisticsOrderService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.web.aspect.annotation.AutoBatch;
import com.raycloud.dmj.web.model.wms.LogisticsOrderVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 自动批处理业务
 */
@Service
public class AutoBatchBusiness {

    private Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private IWmsService wmsService;
    @Resource
    private WaveQueryDao waveQueryDao;
    @Resource
    private WaveLogisticsOrderService waveLogisticsOrderService;
    @Resource
    private ITradeWaveService tradeWaveService;

    @AutoBatch(listFieldPathOrIndex = "1", batchSize = 500)
    public void getAssoGoodsSectionSkuList(Staff staff, List<AllocateGoodsRecord> records, List<AssoGoodsSectionSku> assos) {
        AssoGoodsSectionSkuParams params = new AssoGoodsSectionSkuParams();
        params.setSysItemIds(records.stream().map(AllocateGoodsRecord::getSysItemId).distinct().collect(Collectors.toList()));
        params.setSysSkuIds(records.stream().map(AllocateGoodsRecord::getSysSkuId).distinct().collect(Collectors.toList()));
        params.setWarehouseIds(Lists.newArrayList(records.get(0).getWarehouseId()));
        List<Long> goodsSectionIds = records.stream().map(AllocateGoodsRecord::getGoodsSectionId).distinct().collect(Collectors.toList());
        params.setGoodsSectionIds(goodsSectionIds.toArray(new Long[goodsSectionIds.size()]));
        assos.addAll(wmsService.queryAssoGoodsSectionSkuList(staff, params)) ;
    }

    @AutoBatch(listFieldPathOrIndex = "1", batchSize = 500)
    public void setTradeTagIds(Staff staff, List<? extends Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Long> sids = trades.stream().map(Trade::getSid).distinct().collect(toList());
        List<Trade> tradeLabels = waveQueryDao.queryTradeLabel(staff, sids);
        Map<Long, Trade> sidLabelsMap = TradeUtils.toMapBySid(tradeLabels);
        for (Trade trade : trades) {
            Trade tradeLabel = sidLabelsMap.get(trade.getSid());
            if (tradeLabel == null) {
                continue;
            }
            trade.setTagIds(tradeLabel.getTagIds());
        }
    }

    @AutoBatch(listFieldPathOrIndex = "1", batchSize = 3000)
    public void handleEnterWave(Staff staff, List<Long> sids) {
        List<LogisticsOrder> logisticsOrderList = waveLogisticsOrderService.queryLogisticsOrdersByRelatedBillId(staff, sids);
        List<LogisticsOrder> logisticsOrderList4Update = buildUpdateList(logisticsOrderList);
        logisticsOrderList4Update.forEach(LogisticsOrder::processing);
        waveLogisticsOrderService.batchUpdate(staff, logisticsOrderList4Update);
        waveLogisticsOrderService.addLog(logisticsOrderList, "生成波次", LogisticsOrderVO.StatusEnum.PROCESSING, staff);
    }

    private List<LogisticsOrder> buildUpdateList(List<LogisticsOrder> logisticsOrderList) {
        List<LogisticsOrder> logisticsOrderList4Update = Lists.newArrayListWithCapacity(logisticsOrderList.size());
        for (LogisticsOrder logisticsOrder : logisticsOrderList) {
            LogisticsOrder logisticsOrder4Update = new LogisticsOrder();
            logisticsOrder4Update.setId(logisticsOrder.getId());
            logisticsOrder4Update.setCompanyId(logisticsOrder.getCompanyId());
            logisticsOrderList4Update.add(logisticsOrder4Update);
        }
        return logisticsOrderList4Update;
    }
}

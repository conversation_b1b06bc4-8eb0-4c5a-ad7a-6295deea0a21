package com.raycloud.dmj.services.trades.support.wave.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.dao.wave.WaveSeedLogDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.PackmaConsume;
import com.raycloud.dmj.domain.enums.AiPackmaOpEnum;
import com.raycloud.dmj.domain.pt.log.WavePrintType;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradePackScanInfo;
import com.raycloud.dmj.domain.trades.TradeTrace;
import com.raycloud.dmj.domain.trades.params.TradePackParams;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.domain.wave.WavePackmaItemParam;
import com.raycloud.dmj.domain.wave.WaveSeedLog;
import com.raycloud.dmj.domain.wave.WaveSortingDetail;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.trades.IWaveSortingService;
import com.raycloud.dmj.services.trades.wave.IWaveWriteTradeTraceService;
import com.raycloud.dmj.services.utils.BeanCopierUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 波次自动包装业务
 * @author: chunri
 * @create: 2021-03-01 17:37
 **/
@Service
public class TradeWavePackBusiness {
    private final Logger logger = Logger.getLogger(this.getClass());
    @Resource
    private WaveSeedLogDao waveSeedLogDao;
    @Resource
    private IWaveSortingService waveSortingService;
    // 批量订单数量
    private static final int BATCH_SIZE = 100;
    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;
    @Resource
    private IWaveWriteTradeTraceService waveWriteTradeTraceService;
    @Resource
    private IEventCenter eventCenter;


    /**
     * 封装TradePackScanInfo
     */
    public List<TradePackScanInfo> buildTradePackScanInfos(Staff staff, List<Trade> trades, WavePrintType wavePrintType, Long waveId, List<TradePackScanInfo> tradePackScanInfoList) {
        List<Long> sids = trades.stream().map(Trade::getSid).distinct().collect(Collectors.toList());
        logger.debug(LogHelper.buildLog(staff, String.format("波次自动包装, WavePrintType: %s, sid: %s", wavePrintType, sids)));
        switch (wavePrintType) {
            case SEED: // 播种每次播都会有播种记录，包装时从配货记录中取
                List<WaveSeedLog> waveSeedLogs = waveSeedLogDao.queryWaveSeedLogWithSortingDetailList(staff, sids, waveId);
                tradePackScanInfoList = Lists.newArrayList();
                Map<Long, List<WaveSeedLog>> sid2WaveSeedLogsMap = waveSeedLogs.stream().collect(Collectors.groupingBy(WaveSeedLog::getSid));
                for (Map.Entry<Long, List<WaveSeedLog>> entry : sid2WaveSeedLogsMap.entrySet()) {
                    List<TradePackScanInfo> sidTradePackScanInfoList = Lists.newArrayList();
                    for (WaveSeedLog detail : waveSeedLogs) {
                        sidTradePackScanInfoList.add(TradePackScanInfo.buildTradePackScanInfo(detail.getOuterId(), detail.getOrderId(), detail.getItemNum(), Optional.ofNullable(detail.getScanCodeType()).orElse(TradePackScanInfo.ScanCodeType.NORMAL.getType()), detail.getScanCode()));
                    }
                    // 商家编码去重合并
                    sidTradePackScanInfoList = mergeTradePackScanInfo(sidTradePackScanInfoList);
                    tradePackScanInfoList.addAll(sidTradePackScanInfoList);
                }
                break;
            case POST:
            case CHECK:
                // case POST_BATCH: // 后置只有成单才会触发
                if (CollectionUtils.isEmpty(tradePackScanInfoList)) {
                    return tradePackScanInfoList;
                }
                Map<String, List<Order>> outerId2OrderIdMap =
                        trades.stream().flatMap(trade -> WaveUtils.getCanSortingOrders(trade).stream()).collect(Collectors.groupingBy(Order::getSysOuterId));
                tradePackScanInfoList = splitTradePackScanInfos(tradePackScanInfoList, outerId2OrderIdMap);
                break;
            default:
                tradePackScanInfoList = Lists.newArrayList();
                List<WaveSortingDetail> waveSortingDetails = waveSortingService.queryDetailBySids(staff, sids);
                addTradePackScanInfoListByWaveSorting(waveSortingDetails, tradePackScanInfoList);
                break;
        }
        return tradePackScanInfoList;
    }

    private List<TradePackScanInfo> splitTradePackScanInfos(List<TradePackScanInfo> tradePackScanInfoList, Map<String, List<Order>> outerId2OrderIdMap) {
        List<TradePackScanInfo> scanInfos = Lists.newArrayList();
        for (TradePackScanInfo originTradePackScanInfo : tradePackScanInfoList) {
            List<Order> orders = outerId2OrderIdMap.get(originTradePackScanInfo.getOuterId().toLowerCase());
            //为了防止相同商品订单合单导致出现多个子订单outerId相同，对已合并的TradePackScanInfo做拆分处理
            if (CollectionUtils.isNotEmpty(orders)) {
                orders.forEach(order -> {
                    TradePackScanInfo tradePackScanInfo = new TradePackScanInfo();
                    BeanCopierUtil.copy(originTradePackScanInfo, tradePackScanInfo);
                    tradePackScanInfo.setNum(order.getNum());
                    tradePackScanInfo.setOrderId(order.getId());
                    scanInfos.add(tradePackScanInfo);
                });
            }
        }
        return scanInfos;
    }

    /**
     * 播种打印需要合并相同的商品
     */
    private List<TradePackScanInfo> mergeTradePackScanInfo(List<TradePackScanInfo> tradePackScanInfoList) {
        Map<String, TradePackScanInfo> mergeTradePackScanInfoList = Maps.newHashMap();
        for (TradePackScanInfo tradePackScanInfo : tradePackScanInfoList) {
            String key = StringUtils.defaultString(tradePackScanInfo.getScanCode()).toLowerCase();
            TradePackScanInfo oldTradePackScanInfo = mergeTradePackScanInfoList.get(key);
            if (oldTradePackScanInfo != null) {
                Integer oldNum = DataUtils.getZeroIfDefaultI(oldTradePackScanInfo.getNum());
                Integer newNum = DataUtils.getZeroIfDefaultI(tradePackScanInfo.getNum());
                oldTradePackScanInfo.setNum(oldNum + newNum);
            } else {
                mergeTradePackScanInfoList.put(key, tradePackScanInfo);
            }
        }
        tradePackScanInfoList = Lists.newArrayList(mergeTradePackScanInfoList.values());
        return tradePackScanInfoList;
    }

    public void addTradePackScanInfoListByWaveSorting(List<WaveSortingDetail> waveSortingDetails, List<TradePackScanInfo> tradePackScanInfoList) {
        for (WaveSortingDetail detail : waveSortingDetails) {
            if (detail.getPickedNum() == null || detail.getPickedNum() <= 0) {
                continue;
            }
            tradePackScanInfoList.add(TradePackScanInfo.buildTradePackScanInfo(detail.getOuterId(), detail.getOrderId(), detail.getPickedNum(), TradePackScanInfo.ScanCodeType.NORMAL.getType(), detail.getOuterId()));
        }
    }

    public void packTrades(Staff staff, List<Long> sids, List<TradePackScanInfo> tradePackScanInfoList, String printType) {
        List<TradeTrace> traces = new ArrayList<>();
        // 100个批量消费，如果出现失败，再一个个处理
        for (List<Long> subSids : Lists.partition(sids, BATCH_SIZE)) {
            try {
                TradePackParams params = new TradePackParams.Builder().sids(subSids.toArray(new Long[0])).orders(null).packScanInfos(tradePackScanInfoList).isBind(false).clientIp(printType).autoPack(true).builder();
                params.setSkipValidStaffUser(true);
                tradeServiceDubbo.packTradesBatchByParams(staff, params);
            } catch (Exception e) {
                for (Long sid : subSids) {
                    try {
                        TradePackParams params = new TradePackParams.Builder().sids(new Long[]{sid}).orders(null).packScanInfos(tradePackScanInfoList).isBind(false).clientIp(null).autoPack(true).builder();
                        params.setSkipValidStaffUser(true);
                        tradeServiceDubbo.packTradesBatchByParams(staff, params);
                    } catch (Exception exception) {
                        logger.error(LogHelper.buildLogHead(staff).append("订单拣选自动验货失败,原因:").append(e.getMessage()), e);
                        TradeTrace trace = TradeTrace.builder()
                                .created(new Date())
                                .operateTime(new Date())
                                .operator(staff.getName())
                                .companyId(staff.getCompanyId())
                                .sid(sid)
                                .content("自动验货失败：" + e.getMessage())
                                .build();
                        traces.add(trace);
                    }
                }

            }
        }
        waveWriteTradeTraceService.batchAddTradeTrace(staff, traces);
    }

    public void packTrades(Staff staff, List<Long> sids, List<TradePackScanInfo> tradePackScanInfoList) {
        packTrades(staff, sids, tradePackScanInfoList, null);
    }

    public void consumePackma(Staff staff, WavePackmaItemParam wavePackmaItemParam) {
        try {
            List<Long> sidList = wavePackmaItemParam.getSidList();
            String packmaOuterIds = wavePackmaItemParam.getPackmaOuterIds();
            String action = wavePackmaItemParam.getAction();
            String ip = wavePackmaItemParam.getIp();
            Integer optSource = wavePackmaItemParam.getPackMaOptSource();
            if (StringUtils.isEmpty(packmaOuterIds) || CollectionUtils.isEmpty(sidList)) {
                return;
            }
            logger.debug(LogHelper.buildLog(staff, String.format("包材传参====》%s",packmaOuterIds)));
            JSONArray jsonArray = null;
            try {
                jsonArray = JSON.parseArray(packmaOuterIds);
            } catch (Exception e) {
                throw new IllegalArgumentException("包材商品格式有问题.");
            }
            List<PackmaConsume> packmaConsumeList = new ArrayList<>();
            List<String> traceLogs = new ArrayList<>();
            boolean newPackma = false;
            for (Object packmaOuterId : jsonArray) {
                if (!(packmaOuterId instanceof JSONObject)) {
                    continue;
                }
                JSONObject jsonObject = (JSONObject) packmaOuterId;
                if (Objects.equals("new", jsonObject.getString("v"))) {
                    newPackma = true;
                }
                for (Long sid : sidList) {
                    PackmaConsume packmaConsume = new PackmaConsume();
                    packmaConsume.setSid(sid);
                    packmaConsume.setOuterId(jsonObject.getString("outerId"));
                    packmaConsume.setAmount(jsonObject.getInteger("amount"));
                    packmaConsumeList.add(packmaConsume);
                }
                traceLogs.add(jsonObject.getString("outerId") + " *" + jsonObject.getInteger("amount"));
            }
            Assert.isTrue(CollectionUtils.isNotEmpty(packmaConsumeList), "请输入正确传参");
            List<TradeTrace> tradeTraceList = new ArrayList<>();
            String content = (StringUtils.isNotEmpty(action) ? action : "") + "验货完成，包材：" + StringUtils.join(traceLogs, ",");
            for (Long sid : sidList) {
                tradeTraceList.add(TradeTraceUtils.createTradeTrace(staff, sid, null, "packConsumePackma", content));
            }
            if (newPackma) {
                List<Map<String, Object>> packmaOuterIdArr = JSON.parseObject(packmaOuterIds, new TypeReference<List<Map<String, Object>>>() {});
                eventCenter.fireEvent(this, new EventInfo("wave.new.packma.consume").setArgs(new Object[]{staff, sidList, packmaOuterIdArr,null, optSource}), null);
            } else {
                eventCenter.fireEvent(this, new EventInfo("trade.pack.packma.item.batch").setArgs(new Object[]{staff, packmaConsumeList, ip}), null);
            }
            for (List<TradeTrace> subList : Lists.partition(tradeTraceList, 500)) {
                tradeServiceDubbo.batchAddTradeTrace(staff, Lists.newArrayList(subList));
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "处理包材失败"), e);
            if (wavePackmaItemParam.isThrowExcept()) {
                throw new IllegalArgumentException(e);
            }
        }
    }
}

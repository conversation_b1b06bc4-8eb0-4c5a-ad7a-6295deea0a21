package com.raycloud.dmj.services.trades.support.wave;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.business.wave.WaveHelpBusiness;
import com.raycloud.dmj.business.wave.WaveLockBusiness;
import com.raycloud.dmj.constant.ELockConstant;
import com.raycloud.dmj.dao.items.DmjItemDAO;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.stock.StockOrderRecordDAO;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.dao.wave.*;
import com.raycloud.dmj.domain.ERPLock;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemSkuBatch;
import com.raycloud.dmj.domain.item.ItemSupplierBridge;
import com.raycloud.dmj.domain.item.SuiteSingle;
import com.raycloud.dmj.domain.item.box.ItemBox;
import com.raycloud.dmj.domain.item.box.ItemBoxSingle;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.log.WavePrintType;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockOrderRecord;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.audit.TradePostCheckItemResult;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.CompanyUtils;
import com.raycloud.dmj.domain.utils.MapUtils;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.enums.WaveChatConfigsEnum;
import com.raycloud.dmj.domain.wave.model.ItemUniqueCodeQueryParams;
import com.raycloud.dmj.domain.wave.model.WaveFilterParams;
import com.raycloud.dmj.domain.wave.params.TradeSplitWaveGenericParams;
import com.raycloud.dmj.domain.wave.utils.TradePostPrintContextUtils;
import com.raycloud.dmj.domain.wave.utils.UniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.WaveTraceUtils;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.enums.ContainerTypeEnum;
import com.raycloud.dmj.domain.wms.product.params.StockProductOrderParams;
import com.raycloud.dmj.domain.wms.product.vo.StockProductOrderVo;
import com.raycloud.dmj.index.request.CheckHasFeatureRequest;
import com.raycloud.dmj.index.response.CheckHasFeatureResponse;
import com.raycloud.dmj.item.search.api.DmjItemCommonSearchApi;
import com.raycloud.dmj.item.search.dto.DmjItemDto;
import com.raycloud.dmj.item.search.request.QueryMiniItemByEntityCodeRequest;
import com.raycloud.dmj.item.search.request.StaffRequest;
import com.raycloud.dmj.item.search.response.QueryMiniItemByEntityCodeResponse;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.WaveCommonConstants;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import com.raycloud.dmj.services.domain.ItemTraceActionEnum;
import com.raycloud.dmj.services.domain.ItemTraceBillTypeEnum;
import com.raycloud.dmj.services.domain.WaveItemTraceActionModulePathEnum;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.response.ItemCatIdAndSellerCidsResponse;
import com.raycloud.dmj.services.trace.IItemTraceService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.filter.TradeFilterException;
import com.raycloud.dmj.services.trades.support.wave.business.PostPrintUniqueCodeBusiness;
import com.raycloud.dmj.services.trades.support.wave.business.WaveCodePrintBusiness;
import com.raycloud.dmj.services.trades.support.wave.efficient.EfficientEnum;
import com.raycloud.dmj.services.trades.support.wave.efficient.EfficientWaveHandler;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.trades.support.wave.proxy.WaveUseTradeServiceProxy;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.PdaRpcException;
import com.raycloud.dmj.services.utils.WavePickUtils;
import com.raycloud.dmj.services.wms.IWmsPurchaseService;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.utils.ERPLockUtils;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.FastInOutStockUtils;
import com.raycloud.dmj.utils.wms.WmsKeyUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import com.raycloud.erp.db.router.TransactionTemplateAdapter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.util.Assert;
import com.raycloud.dmj.domain.item.PackmaConsume;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trades.utils.TradeUtils.printIsMerge;
import static com.raycloud.dmj.domain.utils.DataPrivilegeIds.ITEM_SELLING_PRICE;
import static com.raycloud.dmj.domain.wave.WaveRuleType.*;

/**
 * 后置打印实现类
 * Created by shaoxianchang on 2016/11/30.
 */
@Service
public class TradePostPrintService implements ITradePostPrintService {
    private Logger logger = Logger.getLogger(this.getClass());

    private static final int BATCH_SIZE = 500;

    private static Pattern codePattern = Pattern.compile("^([a-zA-Z]|\\d|-|#|_)+$");
    private static final String TRADE_FIELDS = "sid,merge_sid,merge_type";
    public static final String RELAY_PICKER_NAME = "接力拣选";
    public static final String POST_ITEM_LOCK_TRADE = "postItemLockTrade";
    public static final String STOCK_PRODUCT="STOCK_PRODUCT";


    @Autowired
    private WavePickingDao wavePickingDao;
    @Resource
    private WavePickingCodeDao wavePickingCodeDao;
    @Resource
    private WaveDao waveDao;
    @Resource
    private TbTradeDao tbTradeDao;
    @Resource
    private TbOrderDAO tbOrderDao;
    @Autowired
    private ITradeWaveService tradeWaveService;
    @Autowired
    private IWaveSortingService waveSortingService;
    @Resource
    private IItemServiceDubbo itemService;
    @Resource(name = "tbTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private WaveUseTradeServiceProxy waveUseTradeServiceProxy;

    @Resource
    private WaveTradeDao waveTradeDao;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private ILockService lockService;
    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;
    @Resource
    private WaveQueryDao waveQueryDao;
    @Autowired(required = false)
    private IWaveSplitGenericService waveSplitGenericService;
    @Resource
    private IOpLogService opLogService;
    @Resource
    private PickBackOrderDao pickBackOrderDao;
    @Resource
    private IWmsService wmsService;
    @Resource
    private WavePickerDao wavePickerDao;
    @Resource
    private IWavePositionService wavePositionService;
    @Resource
    private WaveSortingDao waveSortingDao;
    @Resource
    private TransactionTemplateAdapter transactionTemplateAdapter;
    @Resource
    private IShopService shopService;
    @Autowired
    private WaveSeedLogDao waveSeedLogDao;
    @Resource
    private IWaveTraceService waveTraceService;
    @Resource
    private EfficientWaveHandler efficientWaveHandler;

    @Resource
    private IWaveUniqueCodeService waveUniqueCodeService;

    @Resource
    private WaveUniqueCodeDao waveUniqueCodeDao;

    @Resource
    private IItemServiceDubbo itemServiceDubbo;

    @Resource
    private WaveHelpBusiness waveHelpBusiness;

    @Resource
    private IItemTraceService itemTraceService;

    @Resource
    private IIndexDubboService indexDubboService;

    @Resource
    private StockOrderRecordDAO stockOrderRecordDAO;

    @Resource
    private WaveCodePrintBusiness waveCodePrintBusiness;

    @Resource(name = "waveSeed4UnTradeWaveService")
    private ITradePostPrintService waveSeed4UnTradeWaveService;
    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;
    @Resource
    private DmjItemDAO dmjItemDAO;
    @Resource
    private IWaveConfigService waveConfigService;
    @Resource
    private DmjItemCommonSearchApi dmjItemCommonSearchApi;
    @Resource
    private HttpServletRequest request;
    @Resource
    private PostPrintUniqueCodeBusiness postPrintUniqueCodeBusiness;
    @Resource
    private IWmsPurchaseService wmsPurchaseService;

    @Resource
    private FeatureService featureService;
    @Resource
    private IWaveWriteTradeTraceService waveWriteTradeTraceService;


    @Override
    public Boolean validatePickingCode(Staff staff, Long waveId, String pickingCode) {
        logger.debug(LogHelper.buildLog(staff, String.format("校验拣选号，波次Id：%s，拣选号：%s", waveId, pickingCode)));
        WavePicking wavePicking = new WavePicking();
        wavePicking.setPickingCode(pickingCode);
        wavePicking.setWaveId(waveId);
        checkPickingCode(staff, wavePicking, false);

        return true;
    }

    /**
     * 检查波次拣选号
     *
     * @param wavePicking 波次拣选
     * @param byHand 是否手动拣选
     */
    private Wave checkPickingCode(Staff staff, WavePicking wavePicking, boolean byHand) {
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (waveConfig != null) {
            wavePicking.setOneWaveMultiPickingCode(waveConfig.getInteger(WaveChatConfigsEnum.ONE_WAVE_MULTI_PICKING_CODE.getKey()));
        }
        Integer openPrintDelay = staff.getConf().getOpenPrintDelay();
        if (openPrintDelay == null || openPrintDelay == 0) {
            throw new IllegalArgumentException("请先开启后置打印！");
        }
        String pickingCode = wavePicking.getPickingCode();
        Assert.hasText(pickingCode, "波次拣选号不能为空！");
        Assert.isTrue(!efficientWaveHandler.isEfficientEngine(pickingCode), String.format("波次拣选号【%s】已存在!", pickingCode));

        boolean matches = codePattern.matcher(pickingCode).matches();
        if (!matches) {
            throw new IllegalArgumentException(String.format("波次拣选号[%s]只能为字母，数字，-（横线），_(下划线)，#（井号）！", pickingCode));
        }

        Long waveId = wavePicking.getWaveId();
        Wave wave = tradeWaveService.queryWaveById(staff, waveId);
        if (wave == null) {
            throw new IllegalArgumentException(String.format("波次拣选号[%s]不存在！", pickingCode));
        }

        if (wave.getStatus() != Wave.STATUS_CREATED) {
            throw new IllegalArgumentException("该波次状态不正确！");
        }

        wavePicking.setPickingType(wave.getPickingType());
        if (Wave.DISTRIBUTION_STATUS_NONE != wave.getDistributionStatus()) {
            throw new IllegalArgumentException("只有未拣选的波次才能进行该操作！");
        }

        if (StringUtils.isNumeric(pickingCode) && !waveId.equals(Long.valueOf(pickingCode))) {
            Integer count = wavePickingDao.checkCodeExistsWaveId(staff, Long.valueOf(pickingCode));
            if (count != null && count > 0) {
                throw new IllegalArgumentException(String.format("波次拣选号[%s]为其他波次号！", pickingCode));
            }
        }
        boolean understockedWave = WaveUtils.containsWaveTag(wave.getTagIds(), WaveTagEnum.UNDERSTOCKED.getId()) ||
                Objects.equal(wave.getRuleId(), -28L) || Objects.equal(wave.getRuleId(), -29L);
        if (!understockedWave) {
            if (Objects.equal(wavePicking.getOneWaveMultiPickingCode(), 1)) {
                if (BooleanUtils.isNotTrue(wavePicking.getRepeatPickingCodeForce())) {
                    Integer multiCodeCount = wavePickingCodeDao.checkCodeExists(staff, pickingCode);
                    Integer count = wavePickingDao.checkCodeExists(staff, pickingCode);
                    if ((multiCodeCount != null && multiCodeCount > 0) || (count != null && count > 0)) {
                        throw new PdaRpcException(PdaRpcException.ERROR_CODE_PICKING_CODE_EXIST, String.format("波次拣选号[%s]已存在！", pickingCode));
                    }
                }
            } else if (Objects.equal(wavePicking.getOneWaveMultiPickingCode(), 2)) {
                Integer multiCodeCount = wavePickingCodeDao.checkCodeExists(staff, pickingCode);
                Integer count = wavePickingDao.checkCodeExists(staff, pickingCode);
                if ((multiCodeCount != null && multiCodeCount > 0) || (count != null && count > 0)) {
                    throw new IllegalArgumentException(String.format("波次拣选号[%s]已存在！", pickingCode));
                }
            }else {
                Integer count = wavePickingDao.checkCodeExists(staff, pickingCode);
                if (count != null && count > 0) {
                    throw new IllegalArgumentException(String.format("波次拣选号[%s]已存在！", pickingCode));
                }
            }
        }
        WavePicking temp = wavePickingDao.getByWaveId(staff, waveId);
        if (temp != null) {
            if (WavePickUtils.isTradeWave(wave) && !understockedWave) {
                throw new IllegalArgumentException(String.format("该波次已被[%s]拣选！", temp.getPickerName()));
            }
            //非交易类型的波次会在生成波次的时候生成对应的拣选和分拣信息
            wave.setWavePicking(temp);
        }

        Long assignPicker = wave.getAssignPicker();
        if (BooleanUtils.isNotTrue(byHand) && assignPicker != null && assignPicker > 0L && !assignPicker.equals(staff.getId())) {
            throw new IllegalArgumentException(String.format("该波次已安排给[%s]拣选！", wave.getAssignPickerName()));
        }

        return wave;
    }

    @Override
    @Transactional
    public void subSectionBeginWavePicking(Staff staff, WavePicking wavePicking) {
        Assert.notNull(wavePicking.getWaveId(), "波次号不能为空！");

        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "开始分段，正在分段拣选人：" + wavePicking.getSubSectionPickerIds()));
        }

        WavePicking queryPicking = wavePickingDao.getByWaveId(staff, wavePicking.getWaveId());
        Assert.notNull(queryPicking, "未查询到分拣信息！");
        WavePicking update = new WavePicking();
        update.setId(queryPicking.getId());
        update.setSubSectionPickerIds(wavePicking.getSubSectionPickerIds());
        wavePickingDao.update(staff, update);

        Set<Long> wavePickerIds = wavePickerDao.getByWaveId(staff, wavePicking.getWaveId())
                .stream().map(WavePicker::getPickerId).collect(Collectors.toSet());
        // 防止重复插入
        if (wavePickerIds.contains(staff.getId())) {
            return;
        }
        WavePicker wavePicker = new WavePicker();
        wavePicker.setPickerId(staff.getId());
        wavePicker.setPickerName(staff.getName());
        wavePicker.setWaveId(wavePicking.getWaveId());
        wavePicker.setParticipatePick(1);
        wavePickerDao.insert(staff, wavePicker);
    }

    @Override
    @Transactional
    public WavePicking beginWavePicking(Staff staff, WavePicking wavePicking) {
        Assert.notNull(wavePicking.getWaveId(), "波次号不能为空！");
        Assert.hasText(wavePicking.getPickingCode(), "波次拣选号不能为空！");
        // 如果是一键随机，超时时间设置为2秒
        boolean isRandom = (wavePicking.getPickWaveEncryption() != null
                && (wavePicking.getPickWaveEncryption() == 2 || wavePicking.getPickWaveEncryption() == 4));
        return lockService.locks(WaveLockBusiness.getBeginWavePickingLock(staff, wavePicking), () -> {
            Wave wave = checkPickingCode(staff, wavePicking, false);
            Integer subSectionPick = wave.getSubSectionPick();
            WavePicking existPicking = wave.getWavePicking();
            //如果已经有了拣选信息直接修改，否则新增一个拣选信息
            if (existPicking != null) {
                existPicking.setPickingCode(wavePicking.getPickingCode());
                existPicking.setPickerId(staff.getId());
                existPicking.setPickerName(staff.getName());
                existPicking.setEnableStatus(WavePicking.STATUS_PICKING);
                existPicking.setStartTime(new Date());
                //加工印花需要改变拣选方式
                if(WaveRuleType.NORMAL_STOCK_PRODUCT.getRuleId().equals(wave.getRuleId())){
                    existPicking.setPickingWay(wavePicking.getPickingWay());
                    existPicking.setUpdatePickingWay(true);
                }
                if (Objects.equal(subSectionPick, 1)) {
                    existPicking.setSubSectionPickerIds(staff.getId().toString());
                }
                setWavePickingSorter(staff, wavePicking.getPickSeedConsistent(), existPicking, wave.getPickingType());
                if (Objects.equal(wavePicking.getOneWaveMultiPickingCode(), 1)) {
                    handleWavePickingCode(staff, existPicking, false, null);
                } else {
                    wavePickingDao.update(staff, existPicking);
                }
                beginWavePickingUpdateWave(staff, wavePicking.getPickSeedConsistent(), wavePicking.getWaveId());
            } else {
                wavePicking.setEnableStatus(WavePicking.STATUS_PICKING);
                wavePicking.setStartTime(new Date());
                wavePicking.setPickerId(staff.getId());
                wavePicking.setPickerName(staff.getName());
                if (Objects.equal(subSectionPick, 1)) {
                    wavePicking.setSubSectionPickerIds(staff.getId().toString());
                }
                setWavePickingSorter(staff, wavePicking.getPickSeedConsistent(), wavePicking, wave.getPickingType());
                if (Objects.equal(wavePicking.getOneWaveMultiPickingCode(), 1)) {
                    handleWavePickingCode(staff, wavePicking, true, null);
                } else {
                    wavePickingDao.insert(staff, wavePicking);
                }
                beginWavePickingUpdateWave(staff, wavePicking.getPickSeedConsistent(), wavePicking.getWaveId());
                createWaveSortings(staff, wavePicking);
            }

            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "开始波次拣货，波次号:" + wavePicking.getWaveId() + ",拣货号:" + wavePicking.getId()));
            }
            eventCenter.fireEvent(this, new EventInfo("wave.picking.begin").setArgs(new Object[]{staff, Lists.newArrayList(wave.getId())}), null);
            return existPicking != null ? existPicking : wavePicking;
        }, isRandom ? 2L : ELockConstant.DEFAULT_TIMEOUT, ELockConstant.DEFAULT_TIMEUNIT);
    }

    @Override
    @Transactional
    public WavePicking addPickingCode(Staff staff, Long pickingId, String pickingCode, boolean repeatPickingCodeForce) {
        WavePicking wavePicking = wavePickingDao.queryById(staff, pickingId);
        String oldCode = wavePicking.getPickingCode();
        wavePicking.setPickingCode(pickingCode);
        wavePicking.setRepeatPickingCodeForce(repeatPickingCodeForce);
        checkPickingCodeAddPickingCode(staff, wavePicking);
        WavePicking update = new WavePicking();
        update.setId(wavePicking.getId());
        update.setCompanyId(wavePicking.getCompanyId());
        update.setWaveId(wavePicking.getWaveId());
        update.setPickingCode(wavePicking.getPickingCode());
        update.setRepeatPickingCodeForce(wavePicking.getRepeatPickingCodeForce());
        handleWavePickingCode(staff, update, false, oldCode);
        return update;
    }

    private void checkPickingCodeAddPickingCode(Staff staff, WavePicking wavePicking) {
        String pickingCode = wavePicking.getPickingCode();
        Assert.hasText(pickingCode, "波次拣选号不能为空！");
        Assert.isTrue(!efficientWaveHandler.isEfficientEngine(pickingCode), String.format("波次拣选号【%s】已存在!", pickingCode));

        boolean matches = codePattern.matcher(pickingCode).matches();
        if (!matches) {
            throw new IllegalArgumentException(String.format("波次拣选号[%s]只能为字母，数字，-（横线），_(下划线)，#（井号）！", pickingCode));
        }
        if (StringUtils.isNumeric(pickingCode) && !wavePicking.getWaveId().equals(Long.valueOf(pickingCode))) {
            Integer count = wavePickingDao.checkCodeExistsWaveId(staff, Long.valueOf(pickingCode));
            if (count != null && count > 0) {
                throw new IllegalArgumentException(String.format("波次拣选号[%s]为其他波次号！", pickingCode));
            }
        }
        if (BooleanUtils.isNotTrue(wavePicking.getRepeatPickingCodeForce())) {
            Integer multiCodeCount = wavePickingCodeDao.checkCodeExists(staff, pickingCode);
            Integer count = wavePickingDao.checkCodeExists(staff, pickingCode);
            if ((multiCodeCount != null && multiCodeCount > 0) || (count != null && count > 0)) {
                throw new PdaRpcException(PdaRpcException.ERROR_CODE_PICKING_CODE_EXIST, String.format("波次拣选号[%s]已存在！", pickingCode));
            }
        }
    }

    private void handleWavePickingCode(Staff staff, WavePicking wavePicking, boolean isInsert, String oldCode) {
        String pickingCode = wavePicking.getPickingCode();
        List<WavePickingCode> wavePickingCodes = new ArrayList<>();
        List<WavePicking> updatePickings = new ArrayList<>();
        WavePickingCode existPickingCode = wavePickingCodeDao.queryByPickingCode(staff, pickingCode);
        if (existPickingCode != null) {
            existPickingCode.setEnableStatus(0);
            wavePickingCodeDao.update(staff, existPickingCode);
        }
        WavePicking existPicking = wavePickingDao.getSameCodePicking(staff, pickingCode, existPickingCode == null ? null : existPickingCode.getPickingId());
        if (existPicking != null) {
            //不同波次被占用需要设置code为波次号 如果被占用的还有其他拣选号则设置其他拣选号
            if (!existPicking.getWaveId().equals(wavePicking.getWaveId())) {
                WavePicking existPickingUpdate = new WavePicking();
                WavePickingCode otherCode = wavePickingCodeDao.queryOtherUsedCode(staff, existPicking.getId());
                existPicking.setPickingCode(existPicking.getWaveId().toString());
                if (otherCode != null) {
                    existPicking.setPickingCode(otherCode.getPickingCode());
                }
                existPickingUpdate.setId(existPicking.getId());
                existPickingUpdate.setPickingCode(existPicking.getPickingCode());
                updatePickings.add(existPickingUpdate);
                wavePicking.setOccupiedPicking(existPicking);
            }
            //重复的pickingCode已更新 不用再新增
            if (existPickingCode == null){
                wavePickingCodes.add(buildWavePickingCode(existPicking, 0, pickingCode));
            }
        }
        if (isInsert) {
            wavePickingDao.insert(staff, wavePicking);
        } else {
            updatePickings.add(wavePicking);
        }
        if (CollectionUtils.isNotEmpty(updatePickings)) {
            wavePickingDao.batchUpdate(staff, updatePickings);
        }
        //新增拣选号 老的拣选号没有pickingCode数据 则新增一个
        if(StringUtils.isNotEmpty(oldCode) && !oldCode.equals(pickingCode)) {
            List<WavePickingCode> codes = wavePickingCodeDao.queryByPickingIds(staff, Lists.newArrayList(wavePicking.getId()), null);
            if (CollectionUtils.isEmpty(codes)) {
                wavePickingCodes.add(buildWavePickingCode(wavePicking, 1, oldCode));
            }
        }
        wavePickingCodes.add(buildWavePickingCode(wavePicking, 1, pickingCode));
        wavePickingCodeDao.insertBatch(staff, wavePickingCodes);
    }

    private WavePickingCode buildWavePickingCode(WavePicking wavePicking, int enableStatus, String pickingCode) {
        WavePickingCode wavePickingCode = new WavePickingCode();
        wavePickingCode.setEnableStatus(enableStatus);
        wavePickingCode.setWaveId(wavePicking.getWaveId());
        wavePickingCode.setPickingId(wavePicking.getId());
        wavePickingCode.setPickingCode(pickingCode);
        return wavePickingCode;
    }

    private void beginWavePickingUpdateWave(Staff staff, Integer openPickSeedConsistent,Long waveId) {
        Wave wave = waveDao.queryById(staff, waveId);
        if (wave != null) {
            Wave update = new Wave();
            update.setId(wave.getId());
            update.setDistributionStatus(WaveDistributionStatus.PICKING.getValue());
            if (Objects.equal(openPickSeedConsistent, 1)) {
                update.setAssignSorter(0L);
                update.setAssignSorterName("");
            }
            waveDao.update(staff, update);
        } else {
            throw new IllegalArgumentException("该波次不存在！");
        }
    }

    /**
     * 设置播种人
     *
     * @param staff
     * @param openPickSeedConsistent：开启谁拣选谁播种
     */
    private void setWavePickingSorter(Staff staff, Integer openPickSeedConsistent, WavePicking wavePicking, Integer pickingType) {

        if (openPickSeedConsistent != null && openPickSeedConsistent == 1 && WavePickUtils.isTradeWave(pickingType)) {
            wavePicking.setSorterId(staff.getId());
            wavePicking.setSorterName(staff.getName());
        }
    }

    @Override
    @Transactional
    public void reAllocateUpdateWaveSorting(Staff staff, WavePickingParam param, List<WaveSortingDetail> details) {
        if (DataUtils.checkLongNotEmpty(param.getWaveId()) && CollectionUtils.isNotEmpty(details)) {
            Wave wave = tradeWaveService.queryWaveById(staff, param.getWaveId());
            Assert.notNull(wave, "根据波次Id未查询到波次！");

            // 1. 查询分拣信息
            List<Long> orderIds = details.stream().map(WaveSortingDetail::getOrderId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderIds)) return;

            List<WaveSortingDetail> sortingDetails = waveSortingDao.queryDetailsByOrderIds(staff, orderIds);
            // 2. 把重新配货的商品缺货数减掉
            List<WaveSortingDetail> updateDetails = Lists.newArrayList();
            Map<Long, WaveSortingDetail> orderIdPickDetailMap = details.stream().collect(Collectors.toMap(WaveSortingDetail::getOrderId, a -> a, (k1, k2) -> k1));
            for (WaveSortingDetail sortingDetail : sortingDetails) {
                WaveSortingDetail pickDetail = orderIdPickDetailMap.get(sortingDetail.getOrderId());
                Integer reduceNum = sortingDetail.getShortageNum() - pickDetail.getTotalNum();
                if (pickDetail != null && reduceNum >= 0) {
                    WaveSortingDetail updateDetail = new WaveSortingDetail();
                    updateDetail.setShortageNum(reduceNum);
                    updateDetail.setId(sortingDetail.getId());
                    updateDetails.add(updateDetail);
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, "PDA拣选完成重新配货/补拣开始重新配货，反向更新子单缺货数，orderId：" + sortingDetail.getOrderId() + "，reduceNum：" + reduceNum));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(updateDetails)) {
                waveSortingDao.batchUpdateDetails(staff, updateDetails);
            }

            if(BooleanUtils.isTrue(param.getUpdatePickingNum())) {
                WavePicking wavePicking = wavePickingDao.getByWaveId(staff, param.getWaveId());
                if(wavePicking == null) {
                    return;
                }
                WavePicking update = new WavePicking();
                update.setId(wavePicking.getId());
                update.setPlanPickNum(wavePicking.getPlanPickNum());
                update.setPickedNum(wavePicking.getPickedNum());
                update.setShortageNum(wavePicking.getShortageNum() - details.stream().mapToInt(d -> d.getTotalNum()).sum());
                update.setUnPickNum(update.getPlanPickNum() - update.getPickedNum() - update.getShortageNum());
                if (BooleanUtils.isTrue(param.getUpdatePickAfterStatus())) {
                    update.setPickAfterStatus(1);
                }
                wavePickingDao.update(staff, update);
                logger.debug(LogHelper.buildLog(staff, "修改波次拣选统计数据，[PlanPickNum,PickedNum,UnPickNum,ShortageNum] "
                        + update.getPlanPickNum() + " " + update.getPickedNum() + " " + update.getUnPickNum() + " " + update.getShortageNum()));
                tradeWaveService.addWavePickingStatLogOnlyEs(staff, wavePicking, update, "reAllocateUpdateWaveSorting");
            }
        }
    }

    @Override
    @Transactional
    public void clearTaskSortingDetails(Staff staff, WavePickingParam param, List<WaveSortingDetail> details) {
        Assert.notNull(param.getPickingId(), "请传入拣选号!");
        Assert.notEmpty(details, "请传入拣选回滚详情!");
        WavePicking wavePicking = wavePickingDao.queryById(staff, param.getPickingId());
        Assert.notNull(wavePicking, "拣选号[" + param.getPickingId() + "]已经不存在!");
        List<WaveSortingDetail> sortingDetails = waveSortingDao.queryDetailsBySid(staff, wavePicking.getId(), null, true);
        if (CollectionUtils.isEmpty(sortingDetails)) {
            logger.warn(LogHelper.buildLog(staff, "找不到该拣选任务的分拣信息！"));
            return;
        }
        Map<Long, WaveSortingDetail> orderIdPickDetailMap = details.stream().collect(Collectors.toMap(WaveSortingDetail::getOrderId, Function.identity(), (v1, v2) -> v1));
        List<WaveSortingDetail> updateDetails = Lists.newArrayListWithCapacity(sortingDetails.size());
        for (WaveSortingDetail sortingDetail : sortingDetails) {
            WaveSortingDetail pickDetail = orderIdPickDetailMap.get(sortingDetail.getOrderId());
            if (pickDetail == null) {
                continue;
            }
            WaveSortingDetail updateDetail = new WaveSortingDetail();
            updateDetail.setId(sortingDetail.getId());
            updateDetail.setPickedNum(sortingDetail.getPickedNum() - pickDetail.getPickedNum());
            updateDetail.setShortageNum(sortingDetail.getItemNum() - updateDetail.getPickedNum());
            updateDetails.add(updateDetail);
        }
        if (CollectionUtils.isNotEmpty(updateDetails)) {
            waveSortingDao.batchUpdateDetails(staff, updateDetails);
        }
        if(BooleanUtils.isTrue(param.getUpdatePickingNum())) {
            WavePicking update = new WavePicking();
            update.setId(wavePicking.getId());
            update.setPlanPickNum(wavePicking.getPlanPickNum());
            update.setPickedNum(wavePicking.getPickedNum() - details.stream().mapToInt(d -> d.getPickedNum()).sum());
            update.setShortageNum(update.getPlanPickNum() - update.getPickedNum());
            update.setUnPickNum(0);
            if (BooleanUtils.isTrue(param.getUpdatePickAfterStatus())) {
                update.setPickAfterStatus(0);
            }
            wavePickingDao.update(staff, update);
            logger.debug(LogHelper.buildLog(staff, "修改波次拣选统计数据，[PlanPickNum,PickedNum,UnPickNum,ShortageNum] "
                    + update.getPlanPickNum() + " " + update.getPickedNum() + " " + update.getUnPickNum() + " " + update.getShortageNum()));
            tradeWaveService.addWavePickingStatLogOnlyEs(staff, wavePicking, update, "clearTaskSortingDetails");
        }
    }

    @Override
    @Transactional
    public WavePicking updateWavePicking(Staff staff, WavePickingParam param, List<WaveSortingDetail> details) {
        Long waveId = param.getWaveId();
        Long pickingId = param.getPickingId();
        Long goodsSectionId = param.getGoodsSectionId();
        if (waveId == null || waveId <= 0) {
            throw new IllegalArgumentException(String.format("波次id[%s]异常!", waveId));
        }

        WavePicking wavePicking = wavePickingDao.queryById(staff, pickingId);
        if (wavePicking == null) {
            throw new IllegalArgumentException("拣选号[" + pickingId + "]已经不存在!");
        }
        if (CollectionUtils.isEmpty(details)) return wavePicking;

        List<Long> sids = Lists.newArrayListWithCapacity(details.size());
        List<Long> orderIds = new ArrayList<>();
        Map<Long, WaveSortingDetail> orderIdPickDetailMap = Maps.newHashMapWithExpectedSize(details.size());
        if (logger.isDebugEnabled()) {
            StringBuilder message = new StringBuilder(String.format("处理pda波次子任务拣选完成事件，波次：%s，拣选：%s, sysItemId: %s, sysSkuId: %s, sid: %s", waveId, pickingId, param.getSysItemId(), param.getSysSkuId(), param.getSid()));
            for (WaveSortingDetail detail : details) {
                sids.add(detail.getSid());
                orderIdPickDetailMap.put(detail.getOrderId(), detail);
                orderIds.add(detail.getOrderId());
                message.append(",orderId:").append(detail.getOrderId()).append(",pickedNum:").append(detail.getPickedNum()).append(",shortageNum:").append(detail.getShortageNum());
            }
            logger.debug(LogHelper.buildLog(staff, message.toString()));
        }
        List<WaveSortingDetail> sortingDetails;
        if (BooleanUtils.isTrue(param.getUpdateByBatchItem())) {
            sortingDetails = waveSortingDao.queryDetailsByOrderIds(staff, pickingId, orderIds);
        } else {
            sortingDetails = waveSortingDao.queryDetailsBySidsAndItem(staff, pickingId, param.getSysItemId(), param.getSysSkuId() == null || param.getSysSkuId() <= 0L ? -1L : param.getSysSkuId(), sids, false);
        }
        if (sortingDetails.isEmpty()) {
            logger.warn(LogHelper.buildLog(staff, "找不到该拣选任务的分拣信息！"));
            return wavePicking;
        }

        WmsConfig wmsConfig = wmsService.getConfig(staff);
        boolean openAutoReallocate = WmsUtils.isOldWms(staff) && Objects.equal(wmsConfig.getOpenAutoReallocate(), CommonConstants.JUDGE_YES);

        List<WaveSortingDetail> updateDetails = Lists.newArrayListWithCapacity(sortingDetails.size());
        Set<Long> sortingIdSet = Sets.newHashSet();
        for (WaveSortingDetail sortingDetail : sortingDetails) {
            sortingDetail.setGoodsSections(StringUtils.isEmpty(sortingDetail.getGoodsSectionStr()) ? Lists.newArrayList() : JSONArray.parseArray(sortingDetail.getGoodsSectionStr(), PickBackGoodsSection.class));
            WaveSortingDetail pickDetail = orderIdPickDetailMap.get(sortingDetail.getOrderId());
            if (pickDetail != null) {
                int pickedNum = sortingDetail.getPickedNum() + pickDetail.getPickedNum();
                if (pickedNum > sortingDetail.getItemNum()) {
                    logger.info(LogHelper.buildLog(staff, String.format("分拣id：%s的已捡数量总和%s超过商品数%s", sortingDetail.getId(), pickedNum, sortingDetail.getItemNum())));
                }
                // 商品全拣才记录拣选时间
                if (pickedNum == sortingDetail.getItemNum()) {
                    sortingIdSet.add(sortingDetail.getSortingId());
                }
                WaveSortingDetail updateDetail = new WaveSortingDetail();
                updateDetail.setId(sortingDetail.getId());
                updateDetail.setPickedNum(pickedNum);
                updateDetail.setShortageNum(sortingDetail.getShortageNum() + pickDetail.getShortageNum());
                fillSortingDetailPicker(staff, updateDetail, false, wavePicking);
                List<PickBackGoodsSection> goodsSections = sortingDetail.getGoodsSections();
                if (BooleanUtils.isTrue(param.getUpdateByBatchItem())) {
                    if (CollectionUtils.isNotEmpty(pickDetail.getGoodsSectionsIncr())) {
                        goodsSections.addAll(pickDetail.getGoodsSectionsIncr());
                    }
                } else {
                    PickBackGoodsSection goodsSection = new PickBackGoodsSection();
                    goodsSection.setId(goodsSectionId);
                    goodsSection.setNum(pickDetail.getPickedNum());
                    goodsSection.setBackNum(0);
                    goodsSections.add(goodsSection);
                }
                if (openAutoReallocate && CollectionUtils.isNotEmpty(goodsSections)) {
                    updateDetail.setGoodsSectionStr(JSON.toJSONString(goodsSections));
                }
                updateDetails.add(updateDetail);
            } else {
                logger.warn(LogHelper.buildLog(staff, String.format("找不到分拣信息，sid:%s, orderId:%s", sortingDetail.getSid(), sortingDetail.getOrderId())));
            }
        }
        updateWavePicker(staff, wavePicking.getWaveId(), true, false);
        if (!updateDetails.isEmpty()) {
            waveSortingDao.batchUpdateDetails(staff, updateDetails);
        }
        Date pickTime = new Date();
        List<WaveSorting> waveSortings = Lists.newArrayListWithCapacity(sortingIdSet.size());
        for (Long sortingId : sortingIdSet) {
            WaveSorting waveSorting = new WaveSorting();
            waveSorting.setId(sortingId);
            waveSorting.setPickEndTime(pickTime);
            waveSortings.add(waveSorting);
        }
        waveSortingDao.batchUpdateStatus(staff, waveSortings);
        if(BooleanUtils.isTrue(param.getUpdatePickingNum())) {
            WavePicking update = new WavePicking();
            update.setId(wavePicking.getId());
            update.setPlanPickNum(wavePicking.getPlanPickNum());
            update.setPickedNum(wavePicking.getPickedNum() + details.stream().mapToInt(d -> d.getPickedNum()).sum());
            update.setShortageNum(wavePicking.getShortageNum() + details.stream().mapToInt(d -> d.getShortageNum()).sum());
            update.setUnPickNum(update.getPlanPickNum() - update.getPickedNum() - update.getShortageNum());
            wavePickingDao.update(staff, update);
            logger.debug(LogHelper.buildLog(staff, "修改波次拣选统计数据，[PlanPickNum,PickedNum,UnPickNum,ShortageNum] "
                    + update.getPlanPickNum() + " " + update.getPickedNum() + " " + update.getUnPickNum() + " " + update.getShortageNum()));
            tradeWaveService.addWavePickingStatLogOnlyEs(staff, wavePicking, update, "updateWavePicking");
        }
        return wavePicking;
    }

    private String orderPositionKey(WaveSortingDetail waveSortingDetail){
        return waveSortingDetail.getOrderId() + "_" + waveSortingDetail.getPositionNo();
    }



    /**
     * 设置分拣明细的拣选员
     * @param staff
     * @param sortingDetail
     * @param byHand
     * @param wavePicking
     */
    private void fillSortingDetailPicker(Staff staff, WaveSortingDetail sortingDetail, boolean byHand, WavePicking wavePicking) {
        if (staff == null || sortingDetail == null || wavePicking == null) {
            return;
        }
        String beforePickerIds = sortingDetail.getPickerIds();
        String beforePickNames = sortingDetail.getPickerNames();
        String pickerId = String.valueOf(WavePickUtils.getAssignPicker(staff, wavePicking.getPickerId(), byHand));
        String pickerName = WavePickUtils.getAssignPickerName(staff, wavePicking.getPickerName(), byHand);
        if (StringUtils.isBlank(beforePickerIds) || "null".equals(beforePickerIds)) {
            sortingDetail.setPickerIds(pickerId);
            sortingDetail.setPickerNames(pickerName);
        } else {
            sortingDetail.setPickerIds(beforePickerIds + "," + pickerId);
            sortingDetail.setPickerNames(beforePickNames + "," + pickerName);
        }
    }

    /**
     * 更新波次拣选员
     *
     * @param staff
     * @param waveId
     * @param participatePick 是否参与拣选
     * @param updateAll       是否更新全部
     */
    private void updateWavePicker(Staff staff, Long waveId, Boolean participatePick, Boolean updateAll) {
        List<WavePicker> wavePickers = wavePickerDao.getByWaveIds(staff, Lists.newArrayList(waveId), participatePick ? 0 : 1);
        List<WavePicker> updates = Lists.newArrayList();
        if (wavePickers != null && wavePickers.size() > 0) {
            for (WavePicker wavePicker : wavePickers) {
                if (updateAll || (wavePicker.getPickerId().equals(staff.getId()))) {
                    WavePicker update = new WavePicker();
                    update.setId(wavePicker.getId());
                    update.setParticipatePick(participatePick ? 1 : 0);
                    update.setWaveId(waveId);
                    updates.add(update);
                }
            }
        }
        if (!updates.isEmpty()) {
            wavePickerDao.batchUpdate(staff, updates);
        }
    }

    @Override
    public WavePicking updateWavePickingStatistics(Staff staff, WavePicking wavePicking) {
        Assert.notNull(wavePicking.getId(), "请选择拣选任务！");
        WavePicking update = new WavePicking();
        update.setId(wavePicking.getId());
        update.setPlanPickNum(wavePicking.getPlanPickNum());
        update.setPickedNum(wavePicking.getPickedNum());
        update.setUnPickNum(wavePicking.getUnPickNum());
        update.setShortageNum(wavePicking.getShortageNum());
        wavePickingDao.update(staff, update);
        return wavePicking;
    }

    @Override
    @Transactional
    public WavePicking relayWavePicking(Staff staff, WavePicking wavePicking) {
        Assert.notNull(wavePicking.getId(), "请选择拣选任务！");
        Assert.notNull(wavePicking.getRelayType(), "请选择接力类型！");
        Wave wave = waveDao.queryById(staff, wavePicking.getWaveId());
        Assert.notNull(wave, "该波次不存在！");
        Assert.isTrue(wave.getStatus() == Wave.STATUS_CREATED, "该波次已完成！");

        WavePicking originPicking = wavePickingDao.queryById(staff, wavePicking.getId());
        Assert.notNull(originPicking, "波次拣选不存在！");
        Assert.isTrue(originPicking.getEnableStatus() != WavePicking.STATUS_OVER, "该波次已拣选完成！");

        WavePicking update = new WavePicking();
        update.setId(wavePicking.getId());

        Wave updateWave = new Wave();
        updateWave.setId(wave.getId());
        if (wavePicking.getRelayType() == WavePicking.RELAY_TYPE_OUT) {
            update.setPickerId(0L);
            update.setPickerName(RELAY_PICKER_NAME);
            updateWave.setAssignPicker(0L);
            updateWave.setAssignPickerName(RELAY_PICKER_NAME);
        } else {
            update.setPickerId(staff.getId());
            update.setPickerName(staff.getName());
            updateWave.setAssignPicker(staff.getId());
            updateWave.setAssignPickerName(staff.getName());
            wavePickerDao.insert(staff, wavePicking.getWaveId());
        }
        // 清空或安排拣选人
        waveDao.update(staff, updateWave);
        wavePickingDao.updateOperator(staff, update);
        logger.debug(LogHelper.buildLog(staff, String.format("波次号：%s, 接力拣选类型：%s", wavePicking.getWaveId(), wavePicking.getRelayType())));
        return wavePicking;
    }

    @Override
    @Transactional
    public WavePicking endWavePicking(Staff staff, WavePicking wavePicking) {
        Wave wave = waveDao.queryById(staff, wavePicking.getWaveId());
        Assert.notNull(wave, "该波次不存在！");
        logger.debug(LogHelper.buildLog(staff, String.format("波次%s拣选的类型为%s", wavePicking.getWaveId(), wavePicking.getPickingType())));
        wavePicking.setEnableStatus(WavePicking.STATUS_OVER);
        wavePicking.setEndTime(new Date());
        wavePickingDao.update(staff, wavePicking);

        updatePickedWave(staff, wave, false);
        updateWaveSortingDetails(staff, wave, false, null);
        return wavePicking;
    }

    @Override
    @Transactional
    public WavePicking endWavePickingByHand(Staff staff, WavePicking wavePicking) {
        Wave wave = waveDao.queryById(staff, wavePicking.getWaveId());
        Assert.notNull(wave, "该波次不存在！");
        logger.debug(LogHelper.buildLog(staff, String.format("波次%s拣选的类型为%s", wavePicking.getWaveId(), wavePicking.getPickingType())));
        wavePicking.setEnableStatus(WavePicking.STATUS_OVER);
        wavePicking.setEndTime(new Date());
        wavePickingDao.update(staff, wavePicking);

        updateWaveSortingDetails(staff, wave, false, null);
        return wavePicking;
    }

    @Override
    @Transactional
    public WavePicking endWavePickingPickAfter(Staff staff, WavePicking wavePicking, List<Long> sids) {
        Wave wave = waveDao.queryById(staff, wavePicking.getWaveId());
        Assert.notNull(wave, "该波次不存在！");
        logger.debug(LogHelper.buildLog(staff, String.format("波次%s拣选的类型为%s", wavePicking.getWaveId(), wavePicking.getPickingType())));
        updatePickedWavePickAfter(staff, sids, wave);
        WavePickingParam param = new WavePickingParam();
        param.setUpdatePickAfterStatus(true);
        updateWaveSortingDetails(staff, wave, true, param);
        return wavePicking;
    }

    private void updatePickedWavePickAfter(Staff staff, List<Long> sids, Wave wave) {
        //暂存区:单件波次开启了自动包装验货，发送事件自动包装
        if (WavePickUtils.isTradeWave(wave) && WavePickUtils.isSingleWave(wave.getPickingType()) && !isStallRule(staff, wave.getRuleId())) {
            TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
            WaveConfig waveConfig = waveConfigService.get(staff);
            if (Objects.equal(tradeConfig.getOpenPackageExamine(), CommonConstants.JUDGE_YES)
                    && BooleanUtils.isTrue(waveConfig.getSingleWaveAutoPackage())) {
                eventCenter.fireEvent(this, new EventInfo("trade.wave.auto.package").setArgs(new Object[]{staff, wave.getId(), false, sids}), null);
            }
        }
        // PDA/PC拣选完成，波次内订单直接发货
        if (WavePickUtils.isTradeWave(wave) && Objects.equal(wave.getPickEndAutoConsign(), 1)) {
            eventCenter.fireEvent(this, new EventInfo("trade.wave.consign.direct").setArgs(new Object[]{staff, Lists.newArrayList(wave.getId()), null, true, null, sids}), null);
        }
        // 档口波次--PDA/PC拣选完成，若完全拣选则直接发货
        if (isStallRule(staff, wave.getRuleId())) {
            eventCenter.fireEvent(this, new EventInfo("stall.wave.split.consign").setArgs(new Object[]{staff, Lists.newArrayList(wave.getId()), true}), null);
        }
    }

    private void updateWaveSortingDetails(Staff staff, Wave wave, Boolean updatePickingNum, WavePickingParam param) {
        WavePicking wavePicking = wavePickingDao.getByWaveId(staff, wave.getId());
        if (wavePicking != null && DataUtils.checkLongNotEmpty(wavePicking.getId())) {
            List<WaveSorting> sortings = waveSortingDao.queryByPickingIds(staff, Lists.newArrayList(wavePicking.getId()));
            if (CollectionUtils.isNotEmpty(sortings)) {
                List<Long> sortingIds = sortings.stream().map(WaveSorting::getId).collect(Collectors.toList());
                List<WaveSortingDetail> sortingDetails = waveSortingDao.queryDetailsBySortingIds(staff, sortingIds);
                if (CollectionUtils.isNotEmpty(sortingDetails)) {
                    List<WaveSortingDetail> updates = Lists.newArrayList();
                    for (WaveSortingDetail detail : sortingDetails) {
                        if (detail.getItemNum() > detail.getPickedNum()) {
                            WaveSortingDetail update = new WaveSortingDetail();
                            update.setId(detail.getId());
                            update.setShortageNum(detail.getItemNum() - detail.getPickedNum());
                            updates.add(update);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(updates)) {
                        waveSortingDao.batchMergeUpdateDetails(staff, updates);
                    }
                }
            }
            if(BooleanUtils.isTrue(updatePickingNum)) {
                WavePicking update = new WavePicking();
                update.setId(wavePicking.getId());
                update.setPlanPickNum(wavePicking.getPlanPickNum());
                update.setPickedNum(wavePicking.getPickedNum());
                update.setShortageNum(update.getPlanPickNum() - update.getPickedNum());
                update.setUnPickNum(0);
                if (param != null && BooleanUtils.isTrue(param.getUpdatePickAfterStatus())) {
                    update.setPickAfterStatus(2);
                }
                wavePickingDao.update(staff, update);
                logger.debug(LogHelper.buildLog(staff, "修改波次拣选统计数据，[PlanPickNum,PickedNum,UnPickNum,ShortageNum] "
                        + update.getPlanPickNum() + " " + update.getPickedNum() + " " + update.getUnPickNum() + " " + update.getShortageNum()));
                tradeWaveService.addWavePickingStatLogOnlyEs(staff, wavePicking, update, "updateWaveSortingDetails");
            }
        }

    }

    private void updatePickedWave(Staff staff, Wave wave, Boolean byHand) {
        Wave update = new Wave();
        update.setId(wave.getId());
        if (WavePickUtils.isTradeWave(wave) && !isStallRule(staff, wave.getRuleId())) {
            //多件波次拣选完成状态是等待播种；单件波次拣选完成，无需验货，状态是等待验货
            WaveDistributionStatus distributionStatus = WavePickUtils.isMultiWave(wave.getPickingType()) ? WaveDistributionStatus.WAIT_SEED : WaveDistributionStatus.WAIT_EXAMINE;
            //由于边检边播，可能波次状态领先于流程中的状态
            if (WaveDistributionStatus.WAIT_EXAMINE.getValue().equals(wave.getDistributionStatus())) {
                distributionStatus = WaveDistributionStatus.WAIT_EXAMINE;
            }
            update.setDistributionStatus(distributionStatus.getValue());
            //手动拣选 且 拣选状态>=2 且 未安排拣选人, 则安排拣选人
            if (Boolean.TRUE.equals(byHand) && distributionStatus.getValue() >= WaveDistributionStatus.PICKED.getValue() && (wave.getAssignPicker() == null || wave.getAssignPicker() == 0 )) {
                update.setAssignPicker(staff.getId());
                update.setAssignPickerName(staff.getName());
                logger.debug(LogHelper.buildLog(staff, String.format("波次手动拣选完成自动安排拣选员为当前账户，waveId：%s", wave.getId())));
            }
        } else if (WavePickUtils.isNeedSeed4NoTradeWave(wave)
                // 广州和成电子商务有限公司 印花加工波次拣选完成波次就直接完成
                && !(WaveRuleType.NORMAL_STOCK_PRODUCT.getRuleId().equals(wave.getRuleId()) && Objects.equal(staff.getCompanyId(), 325494L))
                && !(WaveRuleType.NORMAL_STOCK_PRODUCT.getRuleId().equals(wave.getRuleId()) && Objects.equal(staff.getCompanyId(), 20107L))
                && !(WaveRuleType.NORMAL_STOCK_PRODUCT.getRuleId().equals(wave.getRuleId()) && Objects.equal(staff.getCompanyId(), 23836L))) {
            update.setDistributionStatus(WaveDistributionStatus.WAIT_SEED.getValue());
        } else {
            //非交易波次拣选完成就完成波次
            update.setDistributionStatus(WaveDistributionStatus.PICKED.getValue());
            update.setStatus(Wave.STATUS_FINISHED);
            update.setFinished(new Date());
        }

        if (byHand != null) {
            update.setPickingRoute(BooleanUtils.toIntegerObject(byHand));
        }
        waveDao.update(staff, update);

        //开启波次唯一码需要生成对应关系
        waveUniqueCodeService.createWaveOrderUniqueCodes(staff, wave);
        eventCenter.fireEvent(this, new EventInfo("wave.picking.end").setArgs(new Object[]{staff, Lists.newArrayList(wave.getId()), byHand}), null);
        logger.debug(LogHelper.buildLog(staff, String.format("波次拣选完成，配货状态修改%s->%s 波次状态=%s", wave.getDistributionStatus(), update.getDistributionStatus(), update.getStatus())));

        //暂存区:单件波次开启了自动包装验货，发送事件自动包装
        if (WavePickUtils.isTradeWave(wave) && WavePickUtils.isSingleWave(wave.getPickingType()) && !isStallRule(staff, wave.getRuleId())) {
            TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
            WaveConfig waveConfig = waveConfigService.get(staff);
            if (Objects.equal(tradeConfig.getOpenPackageExamine(), CommonConstants.JUDGE_YES)
                    && BooleanUtils.isTrue(waveConfig.getSingleWaveAutoPackage())) {
                eventCenter.fireEvent(this, new EventInfo("trade.wave.auto.package").setArgs(new Object[]{staff, wave.getId(), byHand}), null);
            }
        }
        // PDA/PC拣选完成，波次内订单直接发货
        if (WavePickUtils.isTradeWave(wave) && Objects.equal(wave.getPickEndAutoConsign(), 1)) {
            eventCenter.fireEvent(this, new EventInfo("trade.wave.consign.direct").setArgs(new Object[]{staff, Lists.newArrayList(wave.getId()), null, true}), null);
        }
        // 档口波次--PDA/PC拣选完成，若完全拣选则直接发货
        if (isStallRule(staff, wave.getRuleId())) {
            eventCenter.fireEvent(this, new EventInfo("stall.wave.split.consign").setArgs(new Object[]{staff, Lists.newArrayList(wave.getId()), true}), null);
        }
    }

    private boolean isStallRule(Staff staff, Long ruleId) {
        if (!DataUtils.checkLongNotEmpty(ruleId)) {
            return false;
        }
        List<WaveRule> rules = tradeWaveService.queryRulesByIds(staff, Lists.newArrayList(ruleId));
        if (CollectionUtils.isEmpty(rules)) {
            return false;
        }
        return WaveUtils.isStallRule(rules.get(0));
    }

    /**
     * 手动拣选
     * 用于pc端，直接对波次号和拣选号进行绑定，然后完成拣选
     * 1. 校验拣选号是否可用，可用则进行分配
     * 2. 如果开启了多品播种配置，则多品波次配货状态改为播种完成，单品单件为拣选完成
     * 3. 没有开启多品播种，单品单件和多品都为拣选完成
     *
     * @param wavePicking 拣选
     */
    @Override
    @Transactional
    public WavePicking pickingByHand(Staff staff, WavePicking wavePicking) throws Exception {
        Assert.notNull(wavePicking.getWaveId(), "波次号不能为空！");
        Assert.hasText(wavePicking.getPickingCode(), "波次拣选号不能为空！");
        boolean byHand = true;
        List<ERPLock> locks = WaveLockBusiness.getBeginWavePickingLock(staff, wavePicking);
        return lockService.locks(locks, () -> {
            Wave wave = checkPickingCode(staff, wavePicking, byHand);
            WavePicking existPicking = wave.getWavePicking();
            Long pickerId = WavePickUtils.getAssignPicker(staff, wave.getAssignPicker(), byHand);
            String pickerName = WavePickUtils.getAssignPickerName(staff, wave.getAssignPickerName(), byHand);
            //如果已经有了拣选信息直接修改，否则新增一个拣选信息
            if (existPicking != null) {
                existPicking.setPickingCode(wavePicking.getPickingCode());
                existPicking.setPickerId(pickerId);
                existPicking.setPickerName(pickerName);
                existPicking.setEnableStatus(WavePicking.STATUS_OVER);
                existPicking.setStartTime(new Date());
                existPicking.setEndTime(existPicking.getStartTime());
                wavePicking.setId(existPicking.getId());
                wavePickingDao.update(staff, existPicking);
            } else {
                wavePicking.setEnableStatus(WavePicking.STATUS_OVER);
                wavePicking.setStartTime(new Date());
                wavePicking.setPickerId(pickerId);
                wavePicking.setPickerName(pickerName);
                wavePicking.setEndTime(wavePicking.getStartTime());
                wavePickingDao.insert(staff, wavePicking);
                createWaveSortings(staff, wavePicking, null, true, byHand);
            }

            updatePickedWave(staff, wave, true);
            handleWavePicked(staff, wave);
            logger.debug(LogHelper.buildLog(staff, String.format("手动拣选，波次Id：%s，拣选号：%s", wavePicking.getWaveId(), wavePicking.getPickingCode())));
            List<Trade> tradeTraces = getSimpleTradesByWaveIdForTrace(staff, wavePicking.getWaveId());
            for (List<Trade> subTrades : Lists.partition(tradeTraces, 2000)) {
                eventCenter.fireEvent(this, new EventInfo("trade.wave.picking.end").setArgs(new Object[]{staff, "" + wavePicking.getWaveId()}), Lists.newArrayList(subTrades));
            }
            return wavePicking;
        });
    }

    private void handleWavePicked(Staff staff, Wave wave) {
        if (!WmsUtils.isNewWms(staff)) {
            return;
        }
        WaveConfig waveConfig = waveConfigService.get(staff);
        //批发收银v1、v2、补货、订单、纸质 根据波次Id 采退 其他出库 调拨出库单
        if (isStallRule(staff,wave.getRuleId())
                || WaveRuleType.STALL.getRuleId().equals(wave.getRuleId())
                || PickingType.REPLENISH.getValue().equals(wave.getPickingType())
                || WaveUtils.directPickIntoWss(wave, waveConfig)
                || Objects.equal(PickStyle.PAPER.getStyleId(), wave.getPickStyle())
                ||PURCHASE_RETURN.getRuleId().equals(wave.getRuleId())
                ||OTHER_CUSTOM_OUT.getRuleId().equals(wave.getRuleId())
                ||ALLOCATE_OUT.getRuleId().equals(wave.getRuleId())) {
            if(ALLOCATE_OUT.getRuleId().equals(wave.getRuleId())){
                wmsPurchaseService.updateALlocateOutDetailByWaveId(staff,Lists.newArrayList(wave.getId()));
            }
            wmsService.pickWaveGoods(staff, Lists.newArrayList(wave.getId()));
            updateSortingDetailPickedNumByAllocateNum(staff, wave);
        }
        //加工单、根据配货记录拣选
        if (WaveRuleType.STOCK_PRODUCT.getRuleId().equals(wave.getRuleId())
                || WaveRuleType.NORMAL_STOCK_PRODUCT.getRuleId().equals(wave.getRuleId())) {
            updateSortingDetailPickedNumByAllocateNum(staff, wave);
            handleStockProductWaveByAllocateRecord(staff, wave);
        }
        //下架单波次 根据配货记录拣选
        if (PickingType.DOWN_SHELF.getValue().equals(wave.getPickingType()) && (wave.getRuleId().equals(WaveRuleType.DOWN_SHELF.getRuleId()) || wave.getRuleId().equals(WaveRuleType.APPOINT_DOWN_SHELF.getRuleId()))) {
            updateSortingDetailPickedNumByAllocateNum(staff, wave);
            handleDownShelfWaveByAllocateRecord(staff,wave);
        }
    }

    private List<AllocateGoodsRecord> buildStockProductAllocateGoodsRecords(Staff staff, Long waveId) {
        if (waveId == null || waveId <= 0) {
            return Collections.emptyList();
        }
        QueryAllocateGoodsRecordParams params = new QueryAllocateGoodsRecordParams();
        params.setWaveIds(Collections.singletonList(waveId));
        List<AllocateGoodsRecord> recordList = wmsService.queryAllocateGoodsRecords(staff, params);
        setDownShelfSectionAndPickedNumWithStockProduct(staff,waveId,recordList);
        return recordList;
    }

    private void setDownShelfSectionAndPickedNumWithStockProduct(Staff staff, Long waveId, List<AllocateGoodsRecord> records) {
        if (waveId == null || waveId <= 0 || CollectionUtils.isEmpty(records)) {
            return;
        }
        StockProductOrderParams params = new StockProductOrderParams();
        params.setWaveId(waveId);
        List<StockProductOrderVo> productOrderList = wmsService.getStockProductOrderVoList(staff, params);

        if (CollectionUtils.isEmpty(productOrderList)) {
            return;
        }

        StockProductOrderVo stockProductOrderVo = productOrderList.get(0);
        final WorkingStorageSection.TypeEnum downShelfSection = WorkingStorageSection.TypeEnum.parseType(stockProductOrderVo.getTargetWaveStockType(), false);
        if (downShelfSection != null) {
            for (AllocateGoodsRecord record : records) {
                record.setDownShelfSection(downShelfSection);
                record.setPickedNumInc(record.getAllocatedNum());
            }
            return;
        }

        for (AllocateGoodsRecord record : records) {
            record.setDownShelfSection(WorkingStorageSection.TypeEnum.COMMON);
            record.setPickedNumInc(record.getAllocatedNum());
        }
    }
    private void handleDownShelfWaveByAllocateRecord(Staff staff, Wave wave) {
        if (wave == null ||  wave.getId() <= 0) {
            return;
        }
        List<AllocateGoodsRecord> allocateGoodsRecords  =  buildUnShelfAllocateGoodsRecords(staff, wave.getId());
        if (CollectionUtils.isEmpty(allocateGoodsRecords)) {
            return;
        }
        wmsService.pickGoods(staff, allocateGoodsRecords);
        List<WavePicking> wavePickings = wavePickingDao.getByWaveIds(staff, Collections.singletonList(wave.getId()));
        wavePickings.forEach(a -> endWavePickingByHand(staff, a));
        //下架单 拣选完成后 需要删除配货记录
        wmsService.deleteByAllocateGoodsStatusAndWaveId(staff, wave.getId(), AllocateGoodsRecord.AllocateGoodsStatusEnum.PICKED.getValue(), PickingType.DOWN_SHELF.getAllocateType().getValue());
    }

    private void handleStockProductWaveByAllocateRecord(Staff staff, Wave wave) {
        if (wave == null ||  wave.getId() <= 0) {
            return;
        }
        List<AllocateGoodsRecord> allocateGoodsRecords  = buildStockProductAllocateGoodsRecords(staff,wave.getId());
        if (CollectionUtils.isEmpty(allocateGoodsRecords)) {
            return;
        }
        wmsService.pickGoods(staff, allocateGoodsRecords);
        wmsService.makeUpProductGood2WorkingStorageSection(staff,null,wave.getId());
        List<WavePicking> wavePickings = wavePickingDao.getByWaveIds(staff, Collections.singletonList(wave.getId()));
        wavePickings.forEach(a -> endWavePickingByHand(staff, a));
    }

    private List<AllocateGoodsRecord> buildUnShelfAllocateGoodsRecords(Staff staff,Long waveId){
        List<Wave> waveList = tradeWaveService.queryWaveInfoByIds(staff,  Lists.newArrayList(waveId), true);
        List<Wave> filterWave = waveList.stream().filter(wave -> wave.getRuleId().equals(WaveRuleType.DOWN_SHELF.getRuleId()) || wave.getRuleId().equals(WaveRuleType.APPOINT_DOWN_SHELF.getRuleId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterWave)) {
            return Collections.emptyList();
        }

        QueryAllocateGoodsRecordParams params = new QueryAllocateGoodsRecordParams();
        params.setWaveIds(filterWave.stream().map(Wave::getId).collect(Collectors.toList()));
        List<AllocateGoodsRecord> recordList = wmsService.queryAllocateGoodsRecords(staff, params);

        if (CollectionUtils.isEmpty(recordList)) {
            return Collections.emptyList();
        }
        Map<Long, AllocateGoodsRecord> map = recordList.stream().collect(Collectors.toMap(AllocateGoodsRecord::getSid, Function.identity(), (v1, v2) -> v1));
        Map<Long, UnShelveOrder> unShelveOrderMap = wmsService.queryUnShelveOrderByIds(staff, new ArrayList<>(map.keySet())).stream().collect(Collectors.toMap(UnShelveOrder::getId, Function.identity(), (v1, v2) -> v1));
        for (AllocateGoodsRecord allocateGoodsRecord : recordList) {
            allocateGoodsRecord.setDownShelfSection(WorkingStorageSection.TypeEnum.valueOf(unShelveOrderMap.get(allocateGoodsRecord.getSid()).getTargetStockType()));
            allocateGoodsRecord.setPickedNumInc(allocateGoodsRecord.getAllocatedNum());
        }
        return recordList;
    }

    private void updateSortingDetailPickedNumByAllocateNum(Staff staff, Wave wave) {
        if (wave == null || !DataUtils.checkLongNotEmpty(wave.getId())) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "updateSortingDetailPickedNumByAllocateNum波次不存在！"));
            }
            return;
        }
        List<WaveSorting> sortings = waveSortingService.queryWaveSortingsByWaveId(staff, wave.getId(), true);
        if (CollectionUtils.isEmpty(sortings)) {
            return;
        }

        QueryAllocateGoodsRecordParams params = new QueryAllocateGoodsRecordParams();
        params.setWaveId(wave.getId());
        List<AllocateGoodsRecord> records = wmsService.queryAllocateGoodsRecords(staff, params);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        List<WaveSortingDetail> updates = new ArrayList<>();
        Map<String, Integer> outerIdAllocateNumMap = records.stream().collect(Collectors.groupingBy(r -> WmsKeyUtils.buildAllocateGoodsRecordKey(r), Collectors.summingInt(AllocateGoodsRecord::getAllocatedNum)));
        for (WaveSorting sorting : sortings) {
            List<WaveSortingDetail> details = sorting.getDetails();
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }

            for (WaveSortingDetail detail : details) {
                Integer allocateNum = outerIdAllocateNumMap.getOrDefault(WmsKeyUtils.buildOrderItemKey(detail), 0);
                if (allocateNum != null) {
                    WaveSortingDetail update = new WaveSortingDetail();
                    update.setId(detail.getId());
                    update.setPickedNum(allocateNum);
                    update.setCompanyId(staff.getCompanyId());
                    updates.add(update);
                }

            }

        }
        waveSortingDao.batchUpdateDetails(staff, updates);
    }

    @Override
    public void finishWave(final Staff staff, final Long waveId) {
        finishWave(staff, waveId, true);
    }

    /**
     * 完结波次
     * 波次也可以操作完结，当操作完结时，该波次内，状态为未打印的订单踢出该波次。只留下已完成的订单。同时该波次的状态为处理完成。（只有拣选完成状态的波次才可以点击完结）
     */
    @Override
    public void finishWave(final Staff staff, final Long waveId, boolean check) {
        List<Long> unauditSids = new ArrayList<>();
        Set<Long> removedSidSet = new HashSet<>();

        transactionTemplateAdapter.execute(staff, new TransactionCallback<Wave>() {
            @Override
            public Wave doInTransaction(TransactionStatus status) {
                Wave wave = tradeWaveService.queryWaveById(staff, waveId);
                Assert.notNull(wave, "该波次不存在");

                //波次管理页面整合了 已完成+已取消+拣选中的所有波次, 所以加上已完成/取消两种状态的校验
                if (wave.getStatus() == Wave.STATUS_FINISHED) {
                    throw new IllegalArgumentException("该波次已结束,不能完结波次");
                }
                if (wave.getStatus() == Wave.STATUS_DELETED) {
                    throw new IllegalArgumentException("该波次已取消,不能完结波次");
                }

                if ((wave.getDistributionStatus() == Wave.DISTRIBUTION_STATUS_NONE || wave.getDistributionStatus() == Wave.DISTRIBUTION_STATUS_ON) && check) {
                    throw new IllegalArgumentException("该波次订单未完成拣选，不能完结该波次");
                }

                if (WavePickUtils.isTradeWave(wave)) {
                    List<Long> sids = tradeWaveService.querySidsByWaveId(staff, waveId);
                    if (CollectionUtils.isEmpty(sids)) {
                        return null;
                    }

                    List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, false, sids.toArray(new Long[0]));
                    if (CollectionUtils.isEmpty(trades)) {
                        return null;
                    }

                    Predicate<Trade> unauditTradePredicate = getUnauditTradePredicate(staff);
                    List<Trade> removeTrades = Lists.newArrayList();
                    List<Trade> tradeLogs = Lists.newArrayList();
                    for (Trade trade : trades) {
                        if (trade.getExpressPrintTime() == null || !trade.getExpressPrintTime().after(TradeTimeUtils.INIT_DATE)) {
                            removeTrades.add(trade);
                            if (unauditTradePredicate.test(trade)) {
                                unauditSids.add(trade.getSid());
                            }
                        } else {
                            tradeLogs.add(trade);
                        }
                    }

                    List<Long> removeSids = TradeUtils.toSidList(removeTrades);
                    if (removeTrades.size() > 0) {
                        generateWaveBackOrders(staff, wave, removeTrades);
                        List<Trade> removeTrades0 = tradeWaveService.removeSids(staff, removeSids.toArray(new Long[0]), WaveOutBusinessEnum.FINISH_WAVE.getOpName());
                        if (null != removeTrades0) {
                            removedSidSet.addAll(TradeUtils.toSidList(removeTrades));
                        }
                        // 新仓储订单踢出波次需要先删除原先配货记录后生成新的配货记录
                        if (WmsUtils.isNewWms(staff)) {
                            List<AllocateGoodsRecord> records = wmsService.queryAllocateGoodsRecords(staff, new QueryAllocateGoodsRecordParams.Builder().allocateType(AllocateType.TRADE.getValue()).containerTypes(Lists.newArrayList(ContainerTypeEnum.GOODS_SECTION.getValue(), ContainerTypeEnum.BOX.getValue(), ContainerTypeEnum.WORKING_STORAGE_SECTION.getValue())).wssType(WorkingStorageSection.TypeEnum.PICK.getCode()).waveId(waveId).sids(removeSids).build());
                            if (CollectionUtils.isNotEmpty(records)) {
                                wmsService.deleteAllocateGoodsRecords(staff, records.stream().map(AllocateGoodsRecord::getId).collect(Collectors.toList()));
                            }
                        }
                    } else {
                        eventCenter.fireEvent(this, new EventInfo("wave.finish.auto").setArgs(new Object[]{staff, Lists.newArrayList(wave)}), null);
                    }

                    wave.setStatus(Wave.STATUS_FINISHED);
                    Wave update = new Wave();
                    update.setId(wave.getId());
                    update.setFinished(new Date());
                    update.setStatus(wave.getStatus());
                    waveDao.update(staff, update);
                    logger.info(LogHelper.buildLog(staff, String.format("波次[%s]完结, 未打印订单:[%s]", waveId, removeSids)));

                    for (List<Trade> subTrades : Lists.partition(tradeLogs, BATCH_SIZE)) {
                        eventCenter.fireEvent(this, new EventInfo("trade.wave.finish").setArgs(new Object[]{staff}), WaveUtils.simplifyTrades(subTrades));
                    }
                } else {
                    throw new IllegalArgumentException("该类型波次暂不支持完结操作！");
                }

                eventCenter.fireEvent(this, new EventInfo("wave.finish").setArgs(new Object[]{staff, Lists.newArrayList(wave)}), null);
                waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.PRINT_FINISH, "手工完结波次"));
                logger.debug(LogHelper.buildLog(staff, "波次完结，处理波次数据结束"));
                return null;
            }
        });

        unauditWaveSids(staff, waveId, unauditSids.stream().filter(removedSidSet::contains).collect(Collectors.toList()));
    }

    @Override
    public void batchFinishWave(Staff staff, List<Long> waveIdList, boolean check) {
        batchFinishWave(staff, waveIdList, check,null);
    }

    @Override
    public void batchFinishWave(Staff staff, List<Long> waveIdList, boolean check,String source) {

        //波次分类，订单波次，非订单波次逻辑不一样
        List<Wave> waveList= tradeWaveService.queryWaveByIds(staff, waveIdList.toArray(new Long[0]));
        checkStatus(waveList,check);
        //订单波次
        List<Wave> tradeWaves = waveList.stream().filter(WavePickUtils::isTradeWave).collect(Collectors.toList());
        List<Wave> otherWaves = waveList.stream().filter(wave -> !WavePickUtils.isTradeWave(wave)&&WavePickUtils.otherWaveSupportFinishWave(wave)).collect(Collectors.toList());
        //非订单波次
        List<Long> otherWaveIds = otherWaves.stream().map(Wave::getId).collect(Collectors.toList());
        List<Long> tradeWaveIds = waveIdList.stream().filter(waveId->!otherWaveIds.contains(waveId)).collect(Collectors.toList());

        long start = System.currentTimeMillis();
        Map<Long,List<Long>> unauditSidsToMap = new HashMap<>(tradeWaveIds.size());
        Map<Long,Set<Long>> removedSidSetToMap = new HashMap<>(tradeWaveIds.size());
        //初始化数据
        for (Long waveId : tradeWaveIds) {
            unauditSidsToMap.put(waveId,new ArrayList<>());
            removedSidSetToMap.put(waveId,new HashSet<>());
        }
        List<Long> alreadyDealWaveIds = new ArrayList(tradeWaveIds.size());
        List<Trade> removeTrades = Lists.newArrayList();
        transactionTemplateAdapter.execute(staff, new TransactionCallback<Wave>() {
            @Override
            public Wave doInTransaction(TransactionStatus status) {
                //处理非订单波次
                finishOtherWaves(otherWaveIds,staff,source);

                Map<Long, Wave> waveMap = tradeWaves.stream().collect(Collectors.toMap(Wave::getId, Function.identity()));
                List<Long> waitDealWaveIds = new ArrayList<Long>(waveMap.keySet());
                if (waitDealWaveIds.size() != tradeWaveIds.size()) {
                    waveIdList.removeAll(waitDealWaveIds);
                    logger.info("存在无效的波次号，waveIds:" + JSON.toJSONString(waveIdList));
                }
                if(waitDealWaveIds.isEmpty()){
                    return null;
                }
                long startQuerySidsByWaveIds = System.currentTimeMillis();
                List<Long> sids = tradeWaveService.querySidsByWaveIds(staff, waitDealWaveIds);
                if (CollectionUtils.isEmpty(sids)) {
                    return null;
                }
                logger.info(LogHelper.buildLog(staff, String.format("【1】【querySidsByWaveIds】查询:took=%s ms, 波次ID:%s ",
                        (System.currentTimeMillis() - startQuerySidsByWaveIds), StringUtils.join(waitDealWaveIds, ","))));

                long startQueryBySids = System.currentTimeMillis();
                List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, false, sids.toArray(new Long[0]));
                if (CollectionUtils.isEmpty(trades)) {
                    return null;
                }
                logger.info(LogHelper.buildLog(staff, String.format("【2】【queryBySids】查询:took=%s ms, 查询订单数:%s ", (System.currentTimeMillis() - startQueryBySids),
                        sids.size())));

                // 过滤掉已经踢出波次的订单(【1】【2】步两次查询之间可能会有订单踢出波次）
                trades = trades.stream().filter(trade -> DataUtils.checkLongNotEmpty(trade.getWaveId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(trades)) {
                    return null;
                }

                Predicate<Trade> unauditTradePredicate = getUnauditTradePredicate(staff);
                List<Trade> tradeLogs = Lists.newArrayList();
                for (Trade trade : trades) {
                    if (trade.getExpressPrintTime() == null || !trade.getExpressPrintTime().after(TradeTimeUtils.INIT_DATE)) {
                        removeTrades.add(trade);
                        if (unauditTradePredicate.test(trade)) {
                            unauditSidsToMap.get(trade.getWaveId()).add(trade.getSid());
                        }
                    } else {
                        tradeLogs.add(trade);
                    }
                }
                List<Long> removeSids = TradeUtils.toSidList(removeTrades);
                //将 removeTrades 按照WaveId进行分组，value为每个waveId对应的 removeTrades
                Map<Long, List<Trade>> removeWaveTradeMap = removeTrades.stream().collect(Collectors.groupingBy(Trade::getWaveId));
                long startBatchGenerateWaveBackOrders = System.currentTimeMillis();
                batchGenerateWaveBackOrders(staff, tradeWaves, removeWaveTradeMap);
                logger.info(LogHelper.buildLog(staff, String.format("【3】【batchGenerateWaveBackOrders】处理:took=%s ms", (System.currentTimeMillis() - startBatchGenerateWaveBackOrders))));

                // 新仓储订单踢出波次需要先删除原先配货记录后生成新的配货记录
                if (WmsUtils.isNewWms(staff)) {
                    if (CollectionUtils.isNotEmpty(removeSids)) {
                        long startQueryAllocateGoodsRecords= System.currentTimeMillis();
                        //此处的查询条件删除了之前的waveId
                        List<AllocateGoodsRecord> records = wmsService.queryAllocateGoodsRecords(staff,
                                new QueryAllocateGoodsRecordParams.Builder().allocateType(AllocateType.TRADE.getValue())
                                        .containerTypes(Lists.newArrayList(ContainerTypeEnum.GOODS_SECTION.getValue(),
                                                ContainerTypeEnum.BOX.getValue(), ContainerTypeEnum.WORKING_STORAGE_SECTION
                                                        .getValue())).wssType(WorkingStorageSection.TypeEnum.PICK.getCode())
                                        .sids(removeSids).build());
                        logger.info(LogHelper.buildLog(staff, String.format("【5】【queryAllocateGoodsRecords】查询:took=%s ms, 订单个数:%s ",
                                (System.currentTimeMillis() - startQueryAllocateGoodsRecords), removeSids.size())));
                        if (CollectionUtils.isNotEmpty(records)) {
                            long startDeleteAllocateGoodsRecords= System.currentTimeMillis();
                            wmsService.deleteAllocateGoodsRecords(staff, records.stream().map(AllocateGoodsRecord::getId).collect(Collectors.toList()));
                            logger.info(LogHelper.buildLog(staff, String.format("【6】【deleteAllocateGoodsRecords】处理:took=%s ms, 订单个数:%s ",
                                    (System.currentTimeMillis() - startDeleteAllocateGoodsRecords), records.size())));
                        }
                    }
                }

                //日志打印
                logger.info(String.format("存在未打印订单的波次号: %s", removeWaveTradeMap.keySet()));
                for (Map.Entry<Long, List<Trade>> entry: removeWaveTradeMap.entrySet()){
                    Wave tempWave = waveMap.get(entry.getKey());
                    if (tempWave != null && !entry.getValue().isEmpty()){
                        List<Long> sonRemoveSids = TradeUtils.toSidList(entry.getValue());
                        //处理日志打印
                        logger.info(LogHelper.buildLog(staff, String.format("波次[%s]完结, 未打印订单:[%s]", tempWave.getId(), sonRemoveSids)));
                    }
                }
                //对于自动完成的波次，发送消息
                sendAutoFinishWaveMessage(staff, removeWaveTradeMap, waitDealWaveIds, waveMap);
                List<Wave> updateWaveList = waitDealWaveIds.stream().map(waveId -> {
                    Wave update = new Wave();
                    update.setId(waveId);
                    update.setFinished(new Date());
                    update.setStatus(Wave.STATUS_FINISHED);
                    alreadyDealWaveIds.add(waveId);
                    return update;
                }).collect(Collectors.toList());
                waveDao.batchUpdate(staff, updateWaveList);
                //消息发送
                for (List<Trade> subTrades : Lists.partition(tradeLogs, BATCH_SIZE)) {
                    eventCenter.fireEvent(this, new EventInfo("trade.wave.finish").setArgs(new Object[]{staff}), WaveUtils.simplifyTrades(subTrades));
                }
                if (!CollectionUtils.isEmpty(tradeWaves))
                {
                    eventCenter.fireEvent(this, new EventInfo("wave.finish").setArgs(new Object[]{staff, Lists.newArrayList(tradeWaves)}), null);
                }
                //添加轨迹信息
                addWaveTrace(staff, alreadyDealWaveIds,null);
                logger.debug(LogHelper.buildLog(staff, "波次完结，处理波次数据结束"));
                return null;
            }
        });

        waveFinishRemoveSids(staff, removedSidSetToMap, removeTrades);
        for (Long waveId:alreadyDealWaveIds){
            unauditWaveSids(staff, waveId, unauditSidsToMap.get(waveId).stream().filter(removeId->removedSidSetToMap
                    .get(waveId).contains(removeId)).collect(Collectors.toList()));
        }
        logger.info(LogHelper.buildLog(staff, String.format("本批次【异步完成波次结束】:took=%s ms, 波次ID:%s ", (System.currentTimeMillis() - start),
                StringUtils.join(waveIdList, ","))));
    }

    private void finishOtherWaves(List<Long>purchaseReturnWaveIds,Staff staff,String source) {
        try{
            //波次状态变更为【已完成】
            List<Wave> purchaseReWaveUpdate = purchaseReturnWaveIds.stream().map(purchaseReturnWaveId -> {
                Wave update = new Wave();
                update.setId(purchaseReturnWaveId);
                update.setFinished(new Date());
                update.setStatus(Wave.STATUS_FINISHED);
                return update;
            }).collect(Collectors.toList());
            waveDao.batchUpdate(staff,purchaseReWaveUpdate);
            //波次日志记录“手工完结波次”
            addWaveTrace(staff,purchaseReturnWaveIds,StringUtils.isNotEmpty(source)&&STOCK_PRODUCT.equals(source)?"加工单完结，波次自动结束":null);
        }catch (Exception e){
            logger.error(LogHelper.buildErrorLog(staff,e,"finishPurchaseReturnWave fail."));
        }
    }

    private void waveFinishRemoveSids(Staff staff, Map<Long, Set<Long>> removedSidSetToMap, List<Trade> removeTrades) {
        long startRemoveSids= System.currentTimeMillis();
        List<Long> removeSids = TradeUtils.toSidList(removeTrades);
        //将 removeTrades 按照WaveId进行分组，value为每个waveId对应的 removeTrades
        Map<Long, List<Trade>> removeWaveTradeMap = removeTrades.stream().collect(Collectors.groupingBy(Trade::getWaveId));

        for (List<Long> subRemoveSids : Lists.partition(removeSids, 5000)) {
            try {
                List<Trade> removeTrades0 = tradeWaveService.removeSids(staff, subRemoveSids.toArray(new Long[0]), WaveOutBusinessEnum.FINISH_WAVE.getOpName());
                // 将 removeTrades0 按照WaveId进行分组，value为每个waveId对应的 removeTrades0
                if (CollectionUtils.isNotEmpty(removeTrades0)) {
                    //removeTrades0 这里应该是多个 波次Id的，将 removeTrades0 中的 waveId提取出来，然后放到set集合中
                    Set<Long> removeWaveIds = removeTrades0.stream().map(Trade::getWaveId).collect(Collectors.toSet());
                    for (Long removeWaveId : removeWaveIds) {
                        removedSidSetToMap.get(removeWaveId).addAll(TradeUtils.toSidList(removeWaveTradeMap.get(removeWaveId)));
                    }
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "波次完成踢出订单失败！"), e);
            }
        }
        logger.info(LogHelper.buildLog(staff, String.format("【4】【removeSids】处理:took=%s ms, 订单个数:%s ", (System.currentTimeMillis() - startRemoveSids), removeSids.size())));
    }

    private void sendAutoFinishWaveMessage(Staff staff, Map<Long, List<Trade>> removeWaveTradeMap, List<Long> waitDealWaveIds, Map<Long, Wave> waveMap) {
        List<Long> needRemoveTradeWaveIdList = new ArrayList(removeWaveTradeMap.keySet());
        List<Long> autoFinishWaveIdList = new ArrayList(waitDealWaveIds);
        autoFinishWaveIdList.removeAll(needRemoveTradeWaveIdList);
        List<Wave> autoFinishWaveList = new ArrayList<>();
        autoFinishWaveIdList.forEach(waveId -> autoFinishWaveList.add(waveMap.get(waveId)));

        if (!CollectionUtils.isEmpty(autoFinishWaveList)){
            eventCenter.fireEvent(  this, new EventInfo("wave.finish.auto").setArgs(new Object[]{staff, Lists.newArrayList(autoFinishWaveList)}), null);
        }
    }

    private void checkStatus(List<Wave> wavelist, boolean check)
    {
        if (CollectionUtils.isEmpty(wavelist))
        {
            throw new IllegalArgumentException("该该波次不存在");
        }
        //使用增强for 来处理 wavelist
        for (Wave wave : wavelist) {
            //波次管理页面整合了 已完成+已取消+拣选中的所有波次, 所以加上已完成/取消两种状态的校验
            if (wave.getStatus() == Wave.STATUS_FINISHED) {
                throw new IllegalArgumentException("该波次已结束,不能完结波次");
            }
            if (wave.getStatus() == Wave.STATUS_DELETED) {
                throw new IllegalArgumentException("该波次已取消,不能完结波次");
            }

            if ((wave.getDistributionStatus() == Wave.DISTRIBUTION_STATUS_NONE || wave.getDistributionStatus() == Wave.DISTRIBUTION_STATUS_ON) && check) {
                throw new IllegalArgumentException("该波次订单未完成拣选，不能完结该波次");
            }
            if (!WavePickUtils.isTradeWave(wave)
                                    &&!PickingType.PURCHASE_RETURN.getValue().equals(wave.getPickingType())
                    &&!PickingType.STOCK_PRODUCT.getValue().equals(wave.getPickingType())){
                throw new IllegalArgumentException("该类型波次暂不支持完结操作！");
            }
            if(WavePickUtils.isStockProductWave(wave)
                    &&(!(WaveDistributionStatus.WAIT_SEED.getValue().equals(wave.getDistributionStatus())||WaveDistributionStatus.SEEDING.getValue().equals(wave.getDistributionStatus())))){
                throw new IllegalArgumentException("该类型波次只支持等待播种和播种中的完结操作！");
            }

        }
    }

    private void addWaveTrace(Staff staff, List<Long> waveIdList,String log) {
        if (CollectionUtils.isEmpty(waveIdList)) {
            return;
        }
        List<WaveTrace> waveTraces = new ArrayList<>(waveIdList.size());
        for (Long waveId : waveIdList){
            waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.PRINT_FINISH, StringUtils.isEmpty(log)?"手工完结波次":log));
        }
        waveTraceService.batchAddWaveTrace(staff,waveTraces);
    }

    private void unauditWaveSids(Staff staff, Long waveId, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        long startUnauditWaveSids= System.currentTimeMillis();
        for (List<Long> subSids : Lists.partition(sids, BATCH_SIZE)) {
            List<Trade> unauditedTrades = tradeServiceDubbo.cancelAudit(staff, OpEnum.AUDIT_UNDO_AUTO_WAVE_END, subSids.toArray(new Long[0]));
            if (CollectionUtils.isNotEmpty(unauditedTrades)) {
                List<Long> successSids = unauditedTrades.stream().map(Trade::getSid).collect(Collectors.toList());
                opLogService.record(staff, new OpLog.Builder().companyId(staff.getCompanyId()).staffId(staff.getId()).accountName(staff.getAccountName())
                        .staffName(staff.getName()).domain(OpLog.DOMAIN_TRADE).action("unauditExceptWaveTrades").key(String.valueOf(waveId))
                        .content(String.format("结束波次订单自动重审，波次号：%s，系统单号：%s", waveId, successSids)).created(new Date()).build());
            }
        }
        logger.info(LogHelper.buildLog(staff, String.format("【7】【unauditWaveSids】处理:took=%s ms, 订单个数:%s ",
                (System.currentTimeMillis() - startUnauditWaveSids), sids.size())));
        logger.debug(LogHelper.buildLog(staff, "波次完结，处理反审核数据结束"));
    }

    private Predicate<Trade> getUnauditTradePredicate(Staff staff) {
        Integer finishWaveTradeAutoUnaudit = waveConfigService.get(staff).getFinishWaveTradeAutoUnaudit();
        return trade -> {
            if (TradeConfig.FinishWaveTradeAutoUnauditTypeEnum.ALL.getValue().equals(finishWaveTradeAutoUnaudit)) {
                return true;
            } else if (TradeConfig.FinishWaveTradeAutoUnauditTypeEnum.ONLY_NORMAL.getValue().equals(finishWaveTradeAutoUnaudit)) {
                return !YesNoEnum.YES.getValue().equals(trade.getIsExcep());
            } else if (TradeConfig.FinishWaveTradeAutoUnauditTypeEnum.ONLY_EXCEPT.getValue().equals(finishWaveTradeAutoUnaudit)) {
                return YesNoEnum.YES.getValue().equals(trade.getIsExcep());
            }
            return false;
        };
    }

    private void generateWaveBackOrders(Staff staff, Wave wave, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades) || !Objects.equal(wmsService.getConfig(staff).getOpenAutoReallocate(), CommonConstants.JUDGE_NO)) {
            return;
        }

        WavePicking picking = wavePickingDao.getByWaveId(staff, wave.getId());
        Assert.notNull(picking, "波次拣选不存在！");

        List<WaveSortingDetail> details = waveSortingDao.queryUnSeedDetailsByPickingId(staff, picking.getId());
        Map<Long, Trade> sidTradeMap = TradeUtils.toMapBySid(trades);
        List<PickBackOrder> pickBackOrders = Lists.newArrayListWithCapacity(details.size());
        for (WaveSortingDetail detail : details) {
            if (detail.getPickedNum() == null || detail.getPickedNum() <= 0 || StringUtils.isEmpty(detail.getGoodsSectionStr())) {
                continue;
            }
            Trade trade = sidTradeMap.get(detail.getSid());
            if (trade != null) {
                pickBackOrders.add(buildWaveBackOrder(detail, trade));
            }
        }

        if (pickBackOrders.isEmpty()) {
            return;
        }
        pickBackOrderDao.batchInsert(staff, pickBackOrders);
    }

    /**
     * 批量处理
     * 注意：trades的订单必须和waves有对应关系
     * @param staff
     * @param waves
     * @param waveTradeMap
     */
    private void batchGenerateWaveBackOrders(Staff staff, List<Wave> waves, Map<Long, List<Trade>> waveTradeMap) {
        if (waveTradeMap == null  || waveTradeMap.isEmpty() || !Objects.equal(wmsService.getConfig(staff).getOpenAutoReallocate(), CommonConstants.JUDGE_NO)) {
            return;
        }
        for (Wave wave : waves) {
            List<Trade> waveTrades = waveTradeMap.get(wave.getId());
            if (CollectionUtils.isEmpty(waveTrades)) {
                continue;
            }
            WavePicking picking = wavePickingDao.getByWaveId(staff, wave.getId());
            Assert.notNull(picking, "波次拣选不存在！");
            List<WaveSortingDetail> details = waveSortingDao.queryUnSeedDetailsByPickingId(staff, picking.getId());
            Map<Long, Trade> sidTradeMap = TradeUtils.toMapBySid(waveTrades);
            List<PickBackOrder> pickBackOrders = Lists.newArrayListWithCapacity(details.size());
            for (WaveSortingDetail detail : details) {
                if (detail.getPickedNum() == null || detail.getPickedNum() <= 0 || StringUtils.isEmpty(detail.getGoodsSectionStr())) {
                    continue;
                }
                Trade trade = sidTradeMap.get(detail.getSid());
                if (trade != null) {
                    pickBackOrders.add(buildWaveBackOrder(detail, trade));
                }
            }
            if (!pickBackOrders.isEmpty()) {
                pickBackOrderDao.batchInsert(staff, pickBackOrders);
            }
        }
    }

    private PickBackOrder buildWaveBackOrder(WaveSortingDetail detail, Trade trade) {
        PickBackOrder pickBackOrder = new PickBackOrder();
        pickBackOrder.setSid(detail.getSid());
        pickBackOrder.setWaveId(detail.getWaveId());
        pickBackOrder.setOrderId(detail.getOrderId());
        pickBackOrder.setOuterId(detail.getOuterId());
        pickBackOrder.setSysItemId(detail.getSysItemId());
        pickBackOrder.setSysSkuId(detail.getSysSkuId());
        pickBackOrder.setItemNum(detail.getItemNum());
        pickBackOrder.setPickedNum(detail.getPickedNum());
        pickBackOrder.setMatchedNum(detail.getMatchedNum());
        pickBackOrder.setWarehouseId(trade.getWarehouseId());
        pickBackOrder.setGoodsSectionStr(detail.getGoodsSectionStr());
        return pickBackOrder;
    }

    /**
     * 根据拣选编号查询拣选信息
     * 多品多件开启播种后置打印扫描拣选号需要判断波次状态是否播种完成
     *
     * @param pickingCode 拣选号
     * @param scanType    扫描类型 1：后置打印   2：pc播种打印  3：pda播种
     */
    @Override
    @Transactional
    public WavePicking scanByPickingCode(Staff staff, String pickingCode, Integer scanType, Boolean showPositions) throws Exception {
        WavePickingScanParam param = new WavePickingScanParam();
        param.setPickingCode(pickingCode);
        param.setScanType(scanType);
        param.setShowPositions(showPositions);
        setMixPickingCode(param);
        return scanPickingInfo(staff, param);
    }

    @Override
    public WavePicking scanByPickingCode(Staff staff, WavePickingScanParam param) throws Exception {
        WavePickingScanParam queryParam = new WavePickingScanParam();
        queryParam.setPickingCode(param.getPickingCode());
        queryParam.setScanType(param.getScanType());
        queryParam.setShowPositions(param.getShowPositions());
        queryParam.setOnlyPrintTrade(param.getOnlyPrintTrade());
        setMixPickingCode(queryParam);
        return scanPickingInfo(staff, queryParam);
    }

    @Override
    public boolean asyncFinishWave(Staff staff, List<Long> waveIds) {
        Assert.notEmpty(waveIds, "波次Ids不能为空！");
        if (!waveHelpBusiness.lockBefore(staff, ProgressEnum.PROGRESS_WAVE_FINISH, waveIds.size())) {
            return false;
        }
        eventCenter.fireEvent(this, new EventInfo("wave.finish.handler").setArgs(new Object[]{staff, waveIds}), null);
        return true;
    }

    @Override
    public boolean asyncFinishWave(Staff staff, List<Long> waveIds, String source) {
        Assert.notEmpty(waveIds, "波次Ids不能为空！");
        if (!waveHelpBusiness.lockBefore(staff, ProgressEnum.PROGRESS_WAVE_FINISH, waveIds.size())) {
            return false;
        }
        eventCenter.fireEvent(this, new EventInfo("wave.finish.handler").setArgs(new Object[]{staff, waveIds,source}), null);
        return true;
    }

    public static void setMixPickingCode(WavePickingScanParam param) {
        String prePickingCode = param.getPickingCode();
        if (StringUtils.isNotEmpty(prePickingCode)
                && prePickingCode.startsWith(EfficientEnum.EFFICIENT_MULTI_WAVES.getWaveCode())) {
            param.setPickingCode(EfficientEnum.EFFICIENT_MULTI_WAVES.getWaveCode());
            param.setMixPickingCode(prePickingCode);
        }
    }

    @Override
    public WavePicking scanPickingInfo(Staff staff, WavePickingScanParam param) {
        return efficientWaveHandler.scanPickingInfo(staff, param);
    }

    @Override
    public WavePageList<WaveSorting> queryUnSeedSortings(Staff staff, Long pickingId, Page page) {
        WavePageList<WaveSorting> pageList = new WavePageList<>();
        Long count = waveSortingDao.queryUnSeedSortingsByPickingIdCount(staff, pickingId, null);
        pageList.setTotal(count);

        if (count != null && count > 0L) {
            WaveSortingSeedParam seedParam = new WaveSortingSeedParam();
            seedParam.setPickingIds(Lists.newArrayList(pickingId));
            seedParam.setPage(page);
            List<WaveSorting> waveSortings = waveSortingDao.queryUnSeedSortingsByPickingId(staff, seedParam);
            pageList.setList(fillWaveSortingTradeInfo(staff, waveSortings, null));
        }

        return pageList;
    }

    @Override
    public List<WaveSorting> fillWaveSortingTradeInfo(Staff staff, List<WaveSorting> waveSortings, Map<Long, Trade> sidTradeMap) {
        long t1, t2, t3, t4, t5;
        long start = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(waveSortings)) {
            return waveSortings;
        }

        List<Long> sids = Lists.newArrayListWithCapacity(waveSortings.size());
        List<Long> sortingIds = Lists.newArrayListWithCapacity(waveSortings.size());
        for (WaveSorting waveSorting : waveSortings) {
            sids.add(waveSorting.getSid());
            sortingIds.add(waveSorting.getId());
        }

        if (sidTradeMap == null) {
            sidTradeMap = Optional.ofNullable(waveUseTradeServiceProxy.queryBySids(staff, false, sids.toArray(new Long[0]))).orElse(Collections.emptyList())
                    .stream().collect(Collectors.toMap(Trade::getSid, java.util.function.Function.identity(), (v1, v2) -> v1));
        }
        t1 = System.currentTimeMillis() - start;
        Map<Long, Order> orderMap = buildOrderMap(sidTradeMap);

        start = System.currentTimeMillis();
        fillMergeTradeInfo(staff, sidTradeMap.values());
        List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySortingIds(staff, sortingIds);
        t2 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        Map<Long, List<WaveSortingDetail>> sortingIdDetailsMap = details.stream().collect(Collectors.groupingBy(WaveSortingDetail::getSortingId));
        List<WaveSeedStat> waveSeedStatList = waveSeedLogDao.statSortingSeedInfo(staff, null, sortingIds);
        Map<Long, Date> waveSeedStatMap = Optional.ofNullable(waveSeedStatList)
                .orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(WaveSeedStat::getSortingId, WaveSeedStat::getSeedEndTime));
        t3 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        Map<String, DmjItem> itemKeyMap = getDetailsItemSkuMap(staff, details);
        t4 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        Map<Long, String> userIdShopNameMap = queryUserIdShopNameMap(staff);
        t5 = System.currentTimeMillis() - start;

        if (logger.isInfoEnabled() && (staff.getCompanyId() == 90223L || staff.getCompanyId() == 257636L || staff.getCompanyId() == 11920L)) {
            logger.info(LogHelper.buildLog(staff, "fillWaveSortingTradeInfo耗时：t1 = " + t1 + ", t2 = " + t2 + ", t3 = " + t3 + ", t4 = " + t4 + ", t5 = " + t5));
        }

        Set<Long>userIds=new HashSet<>();
        Map<Long, Pair<String, String>> shipperInfoMap=new HashMap<>();
        if(CompanyUtils.openMultiShipper(staff)){
            shipperInfoMap= waveHelpBusiness.queryShipByUserIds(staff, sidTradeMap.values().stream().map(Trade::getUserId).collect(Collectors.toSet()));
        }
        for (WaveSorting waveSorting : waveSortings) {
            waveSorting.setPositionCode(WaveUtils.generatePositionCode(staff, waveSorting.getPositionNo().longValue()));
            List<WaveSortingDetail> subDetails = sortingIdDetailsMap.get(waveSorting.getId());
            if (subDetails != null) {
                for (WaveSortingDetail detail : subDetails) {
                    WavePickUtils.fillItemInfo(detail, itemKeyMap.get(WaveHelpBusiness.buildItemKey(detail)));
                    Order order = orderMap.get(detail.getOrderId());
                    if (order != null) {
                        detail.setPlatformPicPath(order.getPicPath());
                    }
                }
                waveSorting.setDetails(subDetails);
            }

            Trade trade = sidTradeMap.get(waveSorting.getSid());
            if (trade != null) {
                WaveUtils.fillWaveSortingWithTrade(staff,waveSorting, trade);
                waveSorting.setShopName(userIdShopNameMap.get(trade.getUserId()));
                waveSorting.setSeedEndTime(waveSeedStatMap.get(waveSorting.getId()));
                userIds.add(trade.getUserId());
                //设置货主信息
                if(CompanyUtils.openMultiShipper(staff)){
                    Pair<String, String> pair = shipperInfoMap.get(trade.getUserId());
                    if(pair!=null){
                        waveSorting.setShipperId(pair.getKey());
                        waveSorting.setShipperName(pair.getValue());
                    }
                }
            }
        }
        return waveSortings;
    }

    /**
     * pc播种显示生产日期批次
     */
    @Override
    public void fillItemBatchAndProductInfo(Staff staff, List<? extends Order> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        List<Long> orderIds = details.stream().map(Order::getId).collect(Collectors.toList());
        // 获取配货记录
        Map<Long, List<AllocateGoodsRecord>> recordMap = wmsService.queryAllocateGoodsRecords(staff, new QueryAllocateGoodsRecordParams.Builder().orderIds(orderIds).build()).stream().collect(Collectors.groupingBy(AllocateGoodsRecord::getOrderId));
        // 查询商品是否开启批次/生产日期
        Map<String, ItemSkuBatch> batchMap = dmjItemDAO.queryItemSku(staff, null,
                details.stream().map(d -> ItemSkuBatch.itemKey2ItemSkuBatch(d.getItemSysId(), d.getSkuSysId())).collect(Collectors.toList()));
        for (Order d : details) {
            ItemSkuBatch batch = batchMap.get(WmsKeyUtils.buildItemKey(d.getItemSysId(), d.getSkuSysId()));
            if (batch == null) {
                continue;
            }
            List<AllocateGoodsRecord> records = recordMap.getOrDefault(d.getId(), Lists.newArrayList());
            if (Objects.equal(batch.getHasProduct(), CommonConstants.JUDGE_YES)) {
                d.setProductDateInfo(WmsUtils.getProductDateByAllocateRecord(records, false));
            }
            if (Objects.equal(batch.getHasBatch(), CommonConstants.JUDGE_YES)) {
                d.setBatchNoInfo(WmsUtils.getBatchNoByAllocateRecord(records, false));
            }
        }
    }



    private Map<Long, Order> buildOrderMap(Map<Long, Trade> sidTradeMap) {
        Map<Long, Order> orderMap = Maps.newHashMap();
        if (org.apache.commons.collections.MapUtils.isEmpty(sidTradeMap)) {
            return orderMap;
        }

        Set<Map.Entry<Long, Trade>> entries = sidTradeMap.entrySet();
        for (Map.Entry<Long, Trade> entry : entries) {
            List<Order> orders = WaveUtils.getOrdersForWave(new WaveUtils.GetOrdersForWaveParams.Builder()
                    .orders(TradeUtils.getOrders4Trade(entry.getValue())).splitSuit(true).build());
            if (CollectionUtils.isEmpty(orders)) {
                continue;
            }
            orderMap.putAll(orders.stream().collect(Collectors.toMap(Order::getId, a -> a, (b1, b2) -> b1)));
        }

        return orderMap;
    }

    @Override
    public void fillMergeTradeInfo(Staff staff, Collection<Trade> trades) {
        Long[] mergeSids = trades.stream().filter(TradeUtils::printIsMerge).map(Trade::getSid).toArray(Long[]::new);
        if (mergeSids.length > 0) {
            List<TbTrade> mergeTrades = tbTradeDao.queryByKeys(staff, "sid,merge_sid,seller_memo,sys_memo,tag_ids,buyer_message", "merge_sid", mergeSids);
            try {
                mergeTrades = waveUseTradeServiceProxy.fillTradeTagName(staff, mergeTrades);
            } catch (TradeFilterException e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "标签过滤处理失败！"), e);
            }
            if (CollectionUtils.isNotEmpty(mergeTrades)) {
                Map<Long, String> sid2TagIdsMap = waveQueryDao.queryTradeLabel(staff, mergeTrades.stream().map(TbTrade::getSid).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(Trade::getSid, Trade::getTagIds, (t1, t2) -> t1));
                for (TbTrade mergeTrade : mergeTrades) {
                    mergeTrade.setTagIds(sid2TagIdsMap.get(mergeTrade.getSid()));
                }
                Map<Long, List<TbTrade>> mergeSidTradesMap = mergeTrades.stream().collect(Collectors.groupingBy(Trade::getMergeSid));
                for (Trade trade : trades) {
                    if (mergeSidTradesMap.containsKey(trade.getSid())) {
                        List<TbTrade> subTrades = mergeSidTradesMap.get(trade.getSid());
                        trade.setSellerMemo(Joiner.on(",").skipNulls().join(subTrades.stream().map(t -> StringUtils.trimToNull(t.getSellerMemo())).collect(Collectors.toList())));
                        trade.setSysMemo(Joiner.on(",").skipNulls().join(subTrades.stream().map(t -> StringUtils.trimToNull(t.getSysMemo())).collect(Collectors.toList())));
                        trade.setBuyerMessage(Joiner.on(",").skipNulls().join(subTrades.stream().map(t -> StringUtils.trimToNull(t.getBuyerMessage())).collect(Collectors.toList())));
                        trade.setTags(getMergeTagNameList(subTrades));
                    }
                }
            }
        }
    }

    /**
     * 获取合单标签
     */
    private List<TradeTag> getMergeTagNameList(List<TbTrade> subTrades) {
        List<TradeTag> mergeTagNames = Lists.newArrayList();
        subTrades.forEach(data -> {
            if (CollectionUtils.isNotEmpty(data.getTags())) {
                mergeTagNames.addAll(data.getTags());
            }
        });
        return mergeTagNames;
    }

    @Override
    public Map<Long, String> queryUserIdShopNameMap(Staff staff) {
        List<Shop> shops = shopService.queryByCompanyId(staff);
        return Optional.ofNullable(shops).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(Shop::getUserId, WaveUtils::getShopName));
    }

    @Override
    public List<Long> queryUnPrintSidsByPickStatus(Staff staff, Long waveId, Long pickingId, Integer pickedStatus, Boolean isUnPickedPrint, Integer printStatus) {
        Assert.notNull(pickingId, "请扫描拣选号！");
        List<Long> sids = null;
        //未拣选,没有开启配置
        if (Long.valueOf(-1).equals(pickingId) && pickedStatus == 0 && BooleanUtils.isTrue(isUnPickedPrint)) {
            sids = gainWaveSidsNoPicked(staff, waveId);
        } else {
            WavePicking wavePicking = wavePickingDao.queryById(staff, pickingId);
            Assert.notNull(wavePicking, "未找到拣选记录信息！");
            WaveConfig waveConfig = waveConfigService.get(staff);
            Integer sortType = 0;
            if (Integer.valueOf(1).equals(waveConfig.getPostPrintSortType())) {
                sortType = 1;
            }
            List<WaveSorting> waveSortings = waveSortingDao.queryUnSeedSortingsByPickingIdPickStatus(staff, pickingId, pickedStatus, printStatus, null, sortType);
            sids = waveSortings.stream().map(WaveSorting::getSid).collect(Collectors.toList());
        }
        return sids;
    }

    List<Long> gainWaveSidsNoPicked(Staff staff, Long waveId) {
        List<Long> tradeSids = tradeWaveService.querySidsByWaveId(staff, waveId);
        List<Long> sids = null;
        if (CollectionUtils.isNotEmpty(tradeSids)) {
            List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradeByWaveId(staff, waveId);
            sids = Optional.ofNullable(waveTrades).orElse(Collections.emptyList()).stream().filter(w -> (WaveTrade.TRADE_WAVE_STATUS_IN == w.getTradeWaveStatus()
                    || WaveTrade.TRADE_WAVE_STATUS_OVER == w.getTradeWaveStatus()) && tradeSids.contains(w.getSid())).map(WaveTrade::getSid).collect(Collectors.toList());
        }
        return sids;
    }

    @Override
    public List<Long> queryUnPrintSids(Staff staff, List<Long> waveIds, List<Long> pickingIds, Integer matchedStatus, Boolean isUnPickedPrint, Integer printType, Integer printStatus) {
        if (CollectionUtils.isEmpty(pickingIds)) {
            throw new IllegalArgumentException("请扫描拣选号！");
        }
        List<Long> sids = null;
        //未拣选,没有开启配置
        if (Long.valueOf(-1).equals(pickingIds.get(0)) && matchedStatus == null && BooleanUtils.isTrue(isUnPickedPrint)) {
            //未拣选打印,目前只有波次批打使用,一个波次
            sids = gainWaveSidsNoPicked(staff, waveIds.get(0));
        } else {
            Integer sortType = 0;
            WaveConfig waveConfig = waveConfigService.get(staff);
            WavePrintType wavePrintType = WavePrintType.parseValue(printType);
            if (WavePrintType.POST.equals(wavePrintType)
                    || WavePrintType.POST_ALL.equals(wavePrintType)
                    || WavePrintType.POST_BATCH.equals(wavePrintType)
                    || WavePrintType.POST_REFRESH.equals(wavePrintType)
                    || WavePrintType.POST_PART.equals(wavePrintType)) {
                if (Integer.valueOf(1).equals(waveConfig.getPostPrintSortType())) {
                    sortType = 1;
                }
            } else if (WavePrintType.BATCH_WAVE.equals(wavePrintType) || WavePrintType.BATCH_WAVE_REFRESH.equals(wavePrintType)) {
                // 后置全部打印也会走这里,过滤做特殊处理
                List<Wave> waves = tradeWaveService.queryWaveByIds(staff, waveIds.toArray(new Long[0]));
                List<Wave> seedWaves = Optional.ofNullable(waves).orElse(Collections.emptyList()).stream().filter(w -> w.getPickingType() == 2).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(seedWaves) && Integer.valueOf(1).equals(waveConfig.getPostPrintSortType())) {
                    sortType = 1;
                }
            }
            WaveSortingSeedParam seedParam = new WaveSortingSeedParam();
            seedParam.setPickingIds(pickingIds);
            seedParam.setPrintStatus(printStatus);
            seedParam.setMatchedStatus(matchedStatus);
            seedParam.setSortType(sortType);
            List<WaveSorting> waveSortings = waveSortingDao.queryUnSeedSortingsByPickingId(staff, seedParam);
            sids = Lists.newArrayListWithCapacity(waveSortings.size());
            for (WaveSorting waveSorting : waveSortings) {
                sids.add(waveSorting.getSid());
            }
        }
        return sids;
    }

    @Override
    public List<Long> queryUnPrintSids(Staff staff, Long waveId, Long pickingId, Integer matchedStatus) {
        return queryUnPrintSids(staff, Lists.newArrayList(waveId), Lists.newArrayList(pickingId), matchedStatus, false, null, null);
    }

    @Override
    public WaveSorting getSortingBySid(Staff staff, Long pickingId, Long sid) {
        WaveSorting waveSorting = new WaveSorting();
        WaveConfig waveConfig = waveConfigService.get(staff);
        WavePicking wavePicking = wavePickingDao.queryById(staff, pickingId);
        Assert.notNull(wavePicking, "该波次不存在！");

        List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySid(staff, pickingId, sid, false);

        if (Objects.equal(waveConfig.getOpenWaveUniqueCode(), 1)) {
            List<WaveUniqueCode> uniqueCodes = waveUniqueCodeDao.queryByKeys(staff, Collections.singletonList(wavePicking.getWaveId()), Collections.singletonList(sid), null);
            Map<Long, List<WaveUniqueCode>> orderIdUniqueCodesMap = uniqueCodes.stream().collect(Collectors.groupingBy(WaveUniqueCode::getOrderId));
            for (WaveSortingDetail detail : details) {
                detail.setUniqueCodes(orderIdUniqueCodesMap.get(detail.getOrderId()));
            }
        }

        details = mergeSortingDetails(details);
        Map<String, DmjItem> itemMap = getDetailsItemSkuMap(staff, details);
        long itemCount = 0L;
        List<Pair<Long, Long>> itemIds = details.stream().map(w -> Pair.of(w.getSysItemId(), w.getSysSkuId())).collect(Collectors.toList());
        Map<Pair<Long, Long>, List<String>> pairListMap = tradeWaveService.queryMultiCodeByItemInfo(staff, itemIds);

        for (WaveSortingDetail detail : details) {
            WavePickUtils.fillItemInfo(detail, itemMap.get(WaveHelpBusiness.buildItemKey(detail)));
            itemCount += detail.getItemNum();

            // 填充商品条形码
            if (org.apache.commons.collections.MapUtils.isNotEmpty(pairListMap)) {
                List<String> multiCodes = pairListMap.get(Pair.of(detail.getSysItemId(), DataUtils.getZeroIfDefault(detail.getSysSkuId())));
                if (CollectionUtils.isNotEmpty(multiCodes)) {
                    detail.setMultiCode(multiCodes.stream().filter(m -> !Objects.equal(m, detail.getOuterId())).findFirst().orElse(null));
                }
            }
        }
        waveSorting.setDetails(details);
        waveSorting.setItemNum(itemCount);
        if (!details.isEmpty()) {
            Long positionNo = details.get(0).getPositionNo();
            waveSorting.setPositionNo(positionNo.intValue());
            waveSorting.setPositionCode(WaveUtils.generatePositionCode(staff, positionNo));
        }

        waveSorting.setSeedScanType(waveConfig.getSeedScanType());
        return waveSorting;
    }

    private List<WaveSortingDetail> mergeSortingDetails(List<WaveSortingDetail> details) {
        Map<String, WaveSortingDetail> itemKeyDetailMap = Maps.newLinkedHashMap();
        for (WaveSortingDetail detail : details) {
            String key = WaveHelpBusiness.buildItemKey(detail);
            WaveSortingDetail tempDetail = itemKeyDetailMap.get(key);
            if (tempDetail == null) {
                itemKeyDetailMap.put(key, detail);
            } else {
                tempDetail.setItemNum(detail.getItemNum() + tempDetail.getItemNum());
                tempDetail.setMatchedNum(detail.getMatchedNum() + tempDetail.getMatchedNum());
                if (detail.getUniqueCodes() != null) {
                    Optional.ofNullable(tempDetail.getUniqueCodes()).orElse(Lists.newArrayList()).addAll(detail.getUniqueCodes());
                }
            }
        }
        return Lists.newArrayList(itemKeyDetailMap.values());
    }


    /**
     * 开始播种
     *
     * @param wave        wave
     * @param wavePicking wavePicking
     */
    public void seeding(Staff staff, Wave wave, WavePicking wavePicking) {
        //set sorter name
        Long sorterId = wavePicking.getSorterId();
        if (sorterId == null || sorterId == 0L) {
            WavePicking update = new WavePicking();
            update.setId(wavePicking.getId());
            update.setSorterId(staff.getId());
            update.setSorterName(staff.getName());
            wavePickingDao.updateOperator(staff, update);
            logger.debug(LogHelper.buildLog(staff, String.format("波次%s播种，播种人：%s(%s)", wavePicking.getWaveId(), wavePicking.getSorterId(), wavePicking.getSorterName())));
        }
        //update wave status
        if (WaveDistributionStatus.PICKED.getValue().equals(wave.getDistributionStatus())
                || WaveDistributionStatus.WAIT_EXAMINE.getValue().equals(wave.getDistributionStatus())
                || WaveDistributionStatus.WAIT_SEED.getValue().equals(wave.getDistributionStatus())
                || WaveDistributionStatus.EXAMINING.getValue().equals(wave.getDistributionStatus())) {
            tradeWaveService.updateDistributionStatus(staff, wavePicking.getWaveId(), WaveDistributionStatus.SEEDING.getValue());
        }
    }

    @Override
    @Transactional
    public WavePickingScanResult scanByOuterId(final Staff staff, final WavePickingParam param) throws Exception {
        WavePickingScanResult result = new WavePickingScanResult();
        long getTradeConfigTimeStart = System.currentTimeMillis();
        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        TradePostPrintContextUtils.getWithInit().setGetTradeConfigTime(System.currentTimeMillis() - getTradeConfigTimeStart);

        long fillResultWithParamStart = System.currentTimeMillis();
        fillResultWithParam(staff, tradeConfig, param, result);
        TradePostPrintContextUtils.getWithInit().setFillResultWithParamTime(System.currentTimeMillis() - fillResultWithParamStart);

        //填充扫描的商品类目名称
        long fillItemCatNameStart = System.currentTimeMillis();
        fillItemCatName(staff, result, param);
        TradePostPrintContextUtils.getWithInit().setFillItemCatNameTime(System.currentTimeMillis() - fillItemCatNameStart);
        logger.debug(LogHelper.buildLog(staff, "波次：" + param.getWaveId() + " 后置打印扫描如下商品：" + param));
        // 关闭了称重和发货需要记录商品操作日志
        if (tradeConfig.getOpenPackageWeigh() - 1 != 0 && tradeConfig.getOpenConsign() - 1 != 0) {
            WaveItemTraceLogHelper.batchRecodeItemTraceLogByBlindScanning(param);
        }

        WavePickingScanResult scanResult = lockService.lock(getPostLock(staff, param), () -> efficientWaveHandler.scanByOuterId(staff, param, result));
        checkPostOrderBySort(staff, scanResult, param);
        // 包材处理
        dealPack(staff, param, result, false);
        return scanResult;
    }

    private void dealPack(Staff staff, WavePickingParam param, WavePickingScanResult result, boolean isBatch) {
        List<Trade> trades;
        if (isBatch) {
            trades = result.getTrades();
        } else {
            trades = result.getTrade() == null ? null : Collections.singletonList(result.getTrade());
        }
        boolean packFlag = result.getTradeMatched() != null && result.getTradeMatched()
                && CollectionUtils.isNotEmpty(trades) && StringUtils.isNotBlank(param.getPackmaOuterIds());
        if (!packFlag) {
            return;
        }
        List<Map<String, Object>> packmaOuterIdArr = null;
        try {
            packmaOuterIdArr = JSON.parseObject(param.getPackmaOuterIds(), new TypeReference<List<Map<String, Object>>>() {
            });
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("后置打印，包材商品格式有误，packmaOuterIds：【%s】", param.getPackmaOuterIds())), e);
            throw new IllegalArgumentException("包材商品格式有误！");
        }
        // 记录包材日志，消耗包材
        if (CollectionUtils.isNotEmpty(packmaOuterIdArr)) {
            if (WaveUtils.checkNewPackma(param.getPackmaOuterIds())) {
                eventCenter.fireEvent(this, new EventInfo("wave.new.packma.consume").setArgs(new Object[]{staff, trades.stream().map(Trade::getSid).distinct().collect(Collectors.toList()), packmaOuterIdArr,null, AiPackmaOpEnum.POST_PRINT.getOpType()}), null);
                saveTradePackTraceLogs(staff, trades, packmaOuterIdArr, param, isBatch);
                return;
            }
            if (trades.size() == 1) {
                eventCenter.fireEvent(this, new EventInfo("trade.pack.packma.item").setArgs(new Object[]{staff, trades.get(0).getSid(), packmaOuterIdArr, IpUtils.getClientIP(request)}), null);
            } else {
                batchConsumePack(staff, packmaOuterIdArr, trades.stream().map(Trade::getSid).distinct().collect(Collectors.toList()));
            }
            saveTradePackTraceLogs(staff, trades, packmaOuterIdArr, param, isBatch);
        }
    }

    private void batchConsumePack(Staff staff, List<Map<String, Object>> packmaOuterIdArr, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        List<PackmaConsume> packmaConsumeList = Lists.newArrayList();
        for (Map<String, Object> map : packmaOuterIdArr) {
            if (map.get("outerId") == null || map.get("amount") == null) {
                continue;
            }
            for (Long sid : sids) {
                PackmaConsume packmaConsume = new PackmaConsume();
                packmaConsume.setSid(sid);
                packmaConsume.setOuterId(map.get("outerId").toString());
                packmaConsume.setAmount(Integer.parseInt(map.get("amount").toString()));
                packmaConsumeList.add(packmaConsume);
            }
        }
        eventCenter.fireEvent(this, new EventInfo("trade.pack.packma.item.batch").setArgs(new Object[]{staff, packmaConsumeList, IpUtils.getClientIP(request)}), null);
    }

    private void saveTradePackTraceLogs(Staff staff, List<Trade> trades, List<Map<String, Object>> packmaOuterIdArr, WavePickingParam param, boolean isBatch) {
        StringBuffer content = new StringBuffer();
        content.append("后置打印，验货商品：");
        if (isBatch) {
            content.append(param.getOuterId()).append("*").append(param.getPrintNum()).append(",");
        } else {
            JSONObject outerIdJson = JSONObject.parseObject(param.getOuterCodes());
            for (Map.Entry<String, Object> entry : outerIdJson.entrySet()) {
                Map<String, Object> map = (Map<String, Object>) entry.getValue();
                content.append(entry.getKey()).append("*").append(String.valueOf(map.get("num"))).append(",");
            }
        }
        content.deleteCharAt(content.length() - 1).append("；包材：");
        for (Map<String, Object> map : packmaOuterIdArr) {
            content.append(map.get("outerId")).append("*").append(String.valueOf(map.get("amount"))).append(",");
        }
        content.deleteCharAt(content.length() - 1).append("；");
        List<TradeTrace> tradeTraces = Lists.newArrayList();
        for (Trade trade : trades) {
            tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff, trade, "后置打印", staff.getName(), new Date(), content.toString()));
        }
        waveWriteTradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }

    private void checkPostOrderBySort(Staff staff, WavePickingScanResult scanResult, WavePickingParam param) {
        if (scanResult.getTrade() == null || !DataUtils.checkLongNotEmpty(param.getWaveId()) || param.getOrderBySort() == null || Objects.equal(CommonConstants.JUDGE_NO, param.getOrderBySort())) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "checkPostOrderBySort，waveId:" + param.getWaveId() + ", orderBySort：" + param.getOrderBySort()));
        }

        // 已按照位置号排序
        List<WaveSorting> waveSortings = waveSortingService.queryWaveSortingsByWaveId(staff, param.getWaveId(), false);
        if (CollectionUtils.isEmpty(waveSortings)) {
            return;
        }

        Map<Long, Integer> sortingMap = waveSortings.stream().collect(Collectors.toMap(WaveSorting::getSid, WaveSorting::getPositionNo, (a, b) -> a));
        Integer currentPositionNo = sortingMap.get(scanResult.getTrade().getSid());
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "checkPostOrderBySort, currentPositionNo:" + currentPositionNo));
        }
        if (currentPositionNo == null) {
            return;
        }
        List<WaveSorting> filters = waveSortings.stream().filter(s -> (s.getPositionNo() > currentPositionNo && Objects.equal(s.getPrintStatus(), 0))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filters)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_COMMON, "扫描不符合订单顺序，请检查商品顺序！");
        }
    }

    private void fillResultWithParam(Staff staff, TradeConfig tradeConfig, WavePickingParam param, WavePickingScanResult result) {
        WaveConfig waveConfig = waveConfigService.get(staff);
        // 去重相同的编码
        mergeSameOuterCodes(param);

        fillRealOuterIds(staff, param);
        // 处理待绑定唯一码
        long uniqueCodeHandle = System.currentTimeMillis();
        postPrintUniqueCodeBusiness.fillBindUniqueCode(staff, param);
        logger.debug(LogHelper.buildLog(staff, String.format("后置打印处理唯一码，costTime：%s", System.currentTimeMillis() - uniqueCodeHandle)));
        //处理扫描的条码
        handleScanOuterCodes(staff, param, waveConfig);
        //设置扫描的商品
        result.setItem(scanItem(staff, param));
        result.setEntitySuitItem(scanEntityItem2Suit(staff, param, tradeConfig));
        //处理套件情况
        handleSuitOuterCodes(param, result.getItem());
        // 后置打印盲扫开启店铺优先发货
        param.setPostPrintMatchPriorityShopList(waveConfig.getPostPrintMatchPriorityShopList());
        // 后置打印匹配订单顺序
        param.setPostPrintMatchTradeSort(waveConfig.getPostPrintMatchTradeSort());
        param.setSuitMode(result.getItem() != null && result.getItem().isSuite() ? CommonConstants.JUDGE_YES : CommonConstants.JUDGE_NO);
        param.setTradeConfigCache(tradeConfig);
        // 预发货订单优先
        param.setOpenPostPrintUploadConsignFirst(Objects.equal(waveConfig.getInteger(WaveChatConfigsEnum.OPEN_POST_PRINT_UPLOAD_CONSIGN_FIRST.getKey()), 1));
        // 拣选优先
        param.setMatchTradePickedFirst(WaveHelpBusiness.isOpenPostPrintPickFirst(waveConfig));

        // 大图锁单模式参数
        handleItemLockTradeMode(staff, param);
        //设置附加返回值
        result.setMatchedItemNum(ObjectUtils.defaultIfNull(param.getTotalNum(), 1));
        result.setPickingCode(param.getPickingCode());
        result.setPositionNo(param.getPositionNo() != null ? param.getPositionNo().intValue() : null);
        result.setSid(param.getSid());
        result.setTradePackScanInfoList(param.getTradePackScanInfoList());
        fillBindUniqueCodes(param, result);
    }

    private void fillBindUniqueCodes(WavePickingParam param, WavePickingScanResult result) {
        // 后置打印返回给前端绑定的唯一码
        if (CollectionUtils.isNotEmpty(param.getDetails())) {
            List<WaveUniqueCode> bindUniqueCodes = Lists.newArrayList();
            for (WaveSortingDetail detail : param.getDetails()) {
                if (detail.getBindUniqueCode() == null) {
                    continue;
                }
                bindUniqueCodes.add(detail.getBindUniqueCode());
            }
            result.setBindUniqueCodes(bindUniqueCodes);
        }
    }

    /**
     * 大图锁单模式参数
     */
    private void handleItemLockTradeMode(Staff staff, WavePickingParam param) {
        if (!param.isPostItemLockTrade()) {
            return;
        }
        param.setPostPrintPositionFirst(CommonConstants.JUDGE_NO);
        param.setOrderBySort(CommonConstants.JUDGE_NO);
    }

    /**
     * 填充扫描商品的类目名称
     *
     * @param staff
     * @param result
     * @param param
     */
    public void fillItemCatName(Staff staff, WavePickingScanResult result, WavePickingParam param) {
        if (result == null || param == null || StringUtils.isEmpty(param.getOuterId())) {
            return;
        }

        if (Objects.equal(param.getCategoryVoice(), CommonConstants.VALUE_YES)) {
            List<String> sysOuterIds = ArrayUtils.toStringList(param.getOuterId());
            ItemCatIdAndSellerCidsResponse response = itemServiceDubbo.queryItemCatIdAndSellerCids(staff, sysOuterIds);
            if (response == null || CollectionUtils.isEmpty(response.getItemList())) {
                return;
            }
            List<ItemCatIdAndSellerCidsResponse.SimpleItem> items = response.getItemList();
            result.setItemCatName(items.get(0).getItemCatName());
        }
    }

    /**
     * 转换一品多码成商家编码
     *
     * @param staff
     * @param param
     */
    private void fillRealOuterIds(Staff staff, WavePickingParam param) {
        if (param == null || StringUtils.isEmpty(param.getOuterId())) {
            return;
        }
        List<String> outerIds = Lists.newArrayList(param.getOuterId());
        if (StringUtils.isNotEmpty(param.getOuterCodes())) {
            JSONObject outerIdJson = JSONObject.parseObject(param.getOuterCodes());
            outerIds.addAll(outerIdJson.keySet());
        }

        Map<String, String> realOuterIds = tradeWaveService.queryOuterIdByMultiCodes(staff, outerIds);
        if (org.apache.commons.collections.MapUtils.isNotEmpty(realOuterIds)) {
            String realOuterId = realOuterIds.get(StringUtils.defaultString(param.getOuterId()).toLowerCase());
            param.setOuterId(StringUtils.isNotEmpty(realOuterId) ? realOuterId : param.getOuterId());
            if (StringUtils.isNotEmpty(param.getOuterCodes())) {
                JSONObject newOuterIdJson = new JSONObject();
                for (Map.Entry<String, Object> entry : JSONObject.parseObject(param.getOuterCodes()).entrySet()) {
                    String temptOuterId = realOuterIds.get(StringUtils.defaultString(entry.getKey()).toLowerCase());
                    if (StringUtils.isNotEmpty(temptOuterId)) {
                        mergeRealOuterOuterJson(staff, newOuterIdJson, temptOuterId, entry);
                        ((JSONObject) entry.getValue()).put("multiCode", entry.getKey());
                        newOuterIdJson.put(temptOuterId, entry.getValue());
                    } else {
                        mergeRealOuterOuterJson(staff, newOuterIdJson, entry.getKey(), entry);
                        newOuterIdJson.put(entry.getKey(), entry.getValue());
                    }
                }
                param.setOuterCodes(newOuterIdJson.toJSONString());
            }
        }
    }

    private void mergeRealOuterOuterJson(Staff staff, JSONObject newOuterIdJson, String realOuterId, Map.Entry<String, Object> entry) {
        if (!newOuterIdJson.containsKey(realOuterId)) {
            return;
        }
        try {
            JSONObject mapData = (JSONObject) newOuterIdJson.get(realOuterId);
            Integer oldNum = DataUtils.getZeroIfDefaultI(mapData.getInteger("num"));
            Integer newNum = DataUtils.getZeroIfDefaultI(((JSONObject) entry.getValue()).getInteger("num"));
            ((JSONObject) entry.getValue()).put("num", oldNum + newNum);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "合并商品多码商品数量报错"), e);
        }
    }

    private void mergeSameOuterCodes(WavePickingParam param) {
        if (StringUtils.isEmpty(param.getOuterCodes())) {
            return;
        }
        // 商家编码去重合并
        Map<String, Map.Entry<String, Object>> outerId2JsonMap = Maps.newHashMap();
        boolean needMerge = false;
        for (Map.Entry<String, Object> entry : JSONObject.parseObject(param.getOuterCodes()).entrySet()) {
            String lowerOuterId = StringUtils.defaultString(entry.getKey()).toLowerCase();
            Map.Entry<String, Object> mapData = outerId2JsonMap.get(lowerOuterId);
            if (mapData != null) {
                needMerge = true;
                Integer oldNum = DataUtils.getZeroIfDefaultI(((JSONObject) mapData.getValue()).getInteger("num"));
                Integer newNum = DataUtils.getZeroIfDefaultI(((JSONObject) entry.getValue()).getInteger("num"));
                ((JSONObject) mapData.getValue()).put("num", oldNum + newNum);
            } else {
                outerId2JsonMap.put(lowerOuterId, entry);
            }
        }
        if (!needMerge) {
            return;
        }
        Map<String, Object> newJsonMap = Maps.newHashMap();
        for (Map.Entry<String, Object> entry : outerId2JsonMap.values()) {
            newJsonMap.put(entry.getKey(), entry.getValue());
        }
        param.setOuterCodes(JSONObject.toJSONString(newJsonMap));
    }

    public List<WaveItem> findExtraItems(Trade trade, boolean postPrintGiftNotCheck) {
        List<Order> orders = WaveUtils.getCanSortingOrders(trade);
        List<WaveItem> items = Lists.newArrayList();
        for (Order order : orders) {
            //去除套件的添加
            if (order.getType() == 2 && order.getCombineId() == 0) {
                continue;
            }
            if (giftNotCheck(order) || (postPrintGiftNotCheck && DataUtils.checkIntegerNotEmpty(order.getGiftNum()))) {
                items.add(WaveUtils.convertToWaveItem(order));
            }
        }

        if (items.isEmpty()) {
            return null;
        }

        Map<String, WaveItem> keyItemMap = Maps.newHashMapWithExpectedSize(items.size());
        for (WaveItem item : items) {
            String key = item.getSysItemId() + "_" + item.getSysSkuId();
            WaveItem waveItem = keyItemMap.get(key);
            if (waveItem == null) {
                keyItemMap.put(key, item);
            } else {
                waveItem.setNum(item.getNum() + waveItem.getNum());
            }
        }

        return Lists.newArrayList(keyItemMap.values());
    }

    /**
     * 检测商品在系统中是否存在
     */
    private DmjItem scanItem(Staff staff, WavePickingParam param) {
        if (BooleanUtils.isNotTrue(param.getShowDetail())) {
            return null;
        }
        if (Objects.equal(param.getScanType(), WavePickingScanParam.SCAN_TYPE_POST_MATCH_FAST_OUT_IN) && CollectionUtils.isNotEmpty(param.getSids())) {
            return null;
        }
        long t1, t2, t3, t4, t5, t6;
        long start = System.currentTimeMillis();
        DmjItem dmjItem = waveHelpBusiness.queryByOuterId(staff, param.getOuterId(),param.getShipperSpeSysItemId());
        t1 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        // 如果是播种，再查一次一品多码
        if (dmjItem == null && DataUtils.checkIntegerNotEmpty(param.getSupportMultiCode())) {
            Map<String, String> realOuterIdMaps = tradeWaveService.queryOuterIdByMultiCodes(staff, Lists.newArrayList(param.getOuterId()));
            if (org.apache.commons.collections.MapUtils.isNotEmpty(realOuterIdMaps)
                    && realOuterIdMaps.get(StringUtils.defaultString(param.getOuterId()).toLowerCase()) != null) {
                param.setOuterId(realOuterIdMaps.get(StringUtils.defaultString(param.getOuterId()).toLowerCase()));
                dmjItem = waveHelpBusiness.queryByOuterId(staff, param.getOuterId(),param.getShipperSpeSysItemId());
            }
        }
        t2 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        dmjItem = scanBoxCode(staff, param, dmjItem);
        t3 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        dmjItem = scanItemUniqueCode(staff, param, dmjItem);
        t4 = System.currentTimeMillis() - start;

        if (dmjItem == null) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_ITEM_NOT_IN_SYSTEM, "商品不存在！");
        }

        start = System.currentTimeMillis();
        //如果是套件需要找到单品处理
        if (dmjItem.isSuite()) {
            List<SuiteSingle> suits = waveHelpBusiness.queryItemSkuSuitesSingles(staff, dmjItem.getSysItemId(), dmjItem instanceof DmjSku ? ((DmjSku) dmjItem).getSysSkuId() : null);
            if (CollectionUtils.isEmpty(suits)) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_ITEM_NOT_IN_SYSTEM, "该套件单品不存在！");
            }
            dmjItem.setSuiteSingleList(suits);
            // 多波次后置 将套件明细的唯一码全部返回给前端
            fillSuitSingleWaveUniqueCodes(staff, param, suits);
        }
        t5 = System.currentTimeMillis() - start;
        DmjSku dmjSku = WavePickUtils.simplifyItem(dmjItem);

        //设置查询出来的sysItemId和sysSkuId
        param.setSysItemId(dmjSku.getSysItemId());
        param.setSysSkuId(dmjSku.getSysSkuId());

        start = System.currentTimeMillis();
        fillProductBatchNotInfo(staff, param, dmjItem, dmjSku);
        t6 = System.currentTimeMillis() - start;
        if (logger.isInfoEnabled() && (staff.getCompanyId() == 90223L || staff.getCompanyId() == 257636L || staff.getCompanyId() == 11920L)) {
            logger.info(LogHelper.buildLog(staff, "scanItem耗时：t1 = " + t1 + ", t2 = " + t2 + ", t3 = " + t3 + ", t4 = " + t4 + ", t5 = " + t5 + ", t6 = " + t6));
        }
        return dmjSku;
    }

    public static QueryMiniItemByEntityCodeRequest buildEntityCodeRequest(Staff staff, List<String> barcodes) {
        QueryMiniItemByEntityCodeRequest request = new QueryMiniItemByEntityCodeRequest();
        request.setStaffRequest(StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build());
        request.setEntityCodes(barcodes);
        return request;
    }

    private void fillSuitSingleWaveUniqueCodes(Staff staff, WavePickingParam param, List<SuiteSingle> suits) {
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, "多波次后置将套件明细的唯一码全部返回给前端，waveId:" + param.getWaveId() + ",sid:" + param.getSid()));
        }

        if (param.getMultiWaves() == null || !param.getMultiWaves() || !DataUtils.checkLongNotEmpty(param.getWaveId()) || !DataUtils.checkLongNotEmpty(param.getSid())) {
            return;
        }

        long start = System.currentTimeMillis();
        WaveUniqueCodeParams waveUniqueCodeParams = new WaveUniqueCodeParams();
        waveUniqueCodeParams.setWaveIds(Lists.newArrayList(param.getWaveId()));
        waveUniqueCodeParams.setSids(Lists.newArrayList(param.getSid()));
        List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeDao.queryByCondition(staff, waveUniqueCodeParams);
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            return;
        }
        Map<String, List<WaveUniqueCode>> waveUniqueCodesMap = waveUniqueCodes.stream().collect(Collectors.groupingBy(w -> WmsKeyUtils.buildItemKey(w.getSysItemId(), w.getSysSkuId())));
        for (SuiteSingle suiteSingle : suits) {
            List<WaveUniqueCode> waveUniqueCodesSuitSingle = waveUniqueCodesMap.get(WmsKeyUtils.buildItemKey(suiteSingle.getSubItemId(), suiteSingle.getSubSkuId()));
            if (CollectionUtils.isEmpty(waveUniqueCodesSuitSingle)) {
                continue;
            }
            suiteSingle.setWaveUniqueCodes(waveUniqueCodesSuitSingle.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList()));
        }
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, "fillSuitSingleWaveUniqueCodes耗时：" + (System.currentTimeMillis() - start)));
        }
    }

    /**
     * 实体编码找套件
     */
    private DmjItem scanEntityItem2Suit(Staff staff, WavePickingParam param, TradeConfig tradeConfig) {
        if (Objects.equal(param.getScanType(), WavePickingScanParam.SCAN_TYPE_POST_MATCH_FAST_OUT_IN)) {
            logger.debug(LogHelper.buildLog(staff, "即入即出匹配后后置打印不重复执行实体编码转化"));
            return null;
        }
        boolean openPostPrintOpenEntityCode = waveHelpBusiness.judgeIsOpenByTradeConfigNew(staff);
        if (!openPostPrintOpenEntityCode) {
            return null;
        }
        logger.debug(LogHelper.buildLog(staff, "开启实体编码找套件模式"));
        QueryMiniItemByEntityCodeResponse queryMiniItemByEntityCodeResponse = dmjItemCommonSearchApi.queryMiniItemByEntityCodes(buildEntityCodeRequest(staff, Lists.newArrayList(param.getOuterId())));
        if (queryMiniItemByEntityCodeResponse == null || CollectionUtils.isEmpty(queryMiniItemByEntityCodeResponse.getList())) {
            logger.debug(LogHelper.buildLog(staff, "根据实体编码未找到套件, outerId:" + param.getOuterId()));
            return null;
        }
        // 套件商品
        DmjItemDto dmjItemDto = queryMiniItemByEntityCodeResponse.getList().get(0);
        String suiOuterId = StringUtils.isEmpty(dmjItemDto.getSkuOuterId()) ? dmjItemDto.getOuterId() : dmjItemDto.getSkuOuterId();
        logger.debug(LogHelper.buildLog(staff, "找到套件编码：" + suiOuterId));
        DmjItem dmjItem = waveHelpBusiness.queryByOuterId(staff, suiOuterId,param.getShipperSpeSysItemId());
        if (dmjItem == null) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_ITEM_NOT_IN_SYSTEM, "套件商品不存在！");
        }
        DmjSku dmjSku = WavePickUtils.simplifyItem(dmjItem);
        WavePickingParam.WavePickingEntitySuitParam wavePickingEntitySuitParam = new WavePickingParam.WavePickingEntitySuitParam();
        wavePickingEntitySuitParam.setSysItemId(dmjSku.getSysItemId());
        wavePickingEntitySuitParam.setSysSkuId(dmjSku.getSysSkuId());
        param.setWavePickingEntitySuitParam(wavePickingEntitySuitParam);
        return dmjSku;
    }

    protected void fillProductBatchNotInfo(Staff staff, WavePickingParam param, DmjItem item, DmjSku dmjSku) {
        dmjSku.setHasProduct(item.getHasProduct());
        dmjSku.setHasBatch(item.getHasBatch());
        if (!WmsUtils.isFillBatchNoProductDate(item) || param.getWaveId() <= 0) {
            return;
        }
        // 获取配货记录
        List<AllocateGoodsRecord> allocateGoodsRecords = wmsService.queryAllocateGoodsRecords(staff, new QueryAllocateGoodsRecordParams.Builder()
                .sysItemId(param.getSysItemId())
                .sysSkuId(param.getSysSkuId())
                .waveId(param.getWaveId())
                .build());
        if (Objects.equal(item.getHasProduct(), CommonConstants.JUDGE_YES)) {
            dmjSku.setProductDateInfo(WmsUtils.getProductDateByAllocateRecord(allocateGoodsRecords, false));
        }
        if (Objects.equal(item.getHasBatch(), CommonConstants.JUDGE_YES)) {
            dmjSku.setBatchNoInfo(WmsUtils.getBatchNoByAllocateRecord(allocateGoodsRecords, false));
        }
    }


    /**
     * 扫箱规
     */
    private DmjItem scanBoxCode(Staff staff, WavePickingParam param, DmjItem dmjItem) {
        if (dmjItem != null || !Objects.equal(tradeServiceDubbo.queryTradeStaffConfig(staff).getOpenBoxCodeScan(), CommonConstants.JUDGE_YES)) {
            return dmjItem;
        }
        String sourKey = param.getOuterId();
        ItemBox itemBox = itemServiceDubbo.queryItemBoxByCode(staff, param.getOuterId(), CommonConstants.JUDGE_YES);
        if (itemBox == null) {
            return dmjItem;
        }
        List<ItemBoxSingle> itemBoxSingles = itemBox.getItemBoxSingles();
        if (CollectionUtils.isEmpty(itemBoxSingles) || itemBoxSingles.size() > 1) {
            throw new WaveScanException("箱规只支持一箱一品类型！");
        }
        param.setOpenBoxCodeScan(Boolean.TRUE);
        String outerId = StringUtils.lowerCase(itemBoxSingles.get(0).getOuterId());
        Integer nums = itemBoxSingles.get(0).getNums() == null ? 0 : itemBoxSingles.get(0).getNums().intValue();
        param.setOuterId(outerId);
        dmjItem = waveHelpBusiness.queryByOuterId(staff, param.getOuterId(),param.getShipperSpeSysItemId());

        if (dmjItem == null) {
            return dmjItem;
        }
        resetMatchDetails(param, nums, outerId, sourKey);
        return dmjItem;
    }

    /**
     * 扫商品唯一码
     */
    private DmjItem scanItemUniqueCode(Staff staff, WavePickingParam param, DmjItem dmjItem) {
        if (dmjItem != null) {
            return dmjItem;
        }
        String sourKey = param.getOuterId();
        String outerId = itemUniqueCodeService.getOuterIdByUniqueCode(staff, param.getOuterId());
        param.setOuterId(outerId);
        dmjItem = waveHelpBusiness.queryByOuterId(staff, param.getOuterId(),param.getShipperSpeSysItemId());

        if (dmjItem == null) {
            return dmjItem;
        }
        resetMatchDetails(param, 1, outerId, sourKey);
        return dmjItem;
    }

    /**
     * 设置WaveSortingDetail匹配参数
     */
    private void resetMatchDetails(WavePickingParam param, Integer num, String outerId, String sourKey) {
        if (CollectionUtils.isNotEmpty(param.getDetails())) {
            param.getDetails().removeIf(detail -> StringUtils.endsWithIgnoreCase(detail.getOuterId(), sourKey));
            WaveSortingDetail waveSortingDetail = param.getDetails().stream()
                    .filter(detail -> StringUtils.endsWithIgnoreCase(detail.getOuterId(), outerId))
                    .findFirst().orElseGet(() -> {
                        WaveSortingDetail detail = new WaveSortingDetail();
                        detail.setOuterId(outerId);
                        param.getDetails().add(detail);
                        return detail;
                    });
            waveSortingDetail.setItemNum(DataUtils.add(waveSortingDetail.getItemNum(), num));
        } else {
            WaveSortingDetail detail = new WaveSortingDetail();
            detail.setOuterId(outerId);
            detail.setItemNum(num);
            param.setDetails(Lists.newArrayList(detail));
        }
        // totalNum播种使用
        param.setTotalNum(num);
    }

    private ERPLock getPostLock(Staff staff, WavePickingParam param) {
        Long waveId = param.getWaveId();
        if (waveId != null && waveId > 0L) {
            return ERPLockUtils.createERPLock(staff.getDbNo(), staff.getCompanyId(), "wave_post_" + staff.getCompanyId() + "_" + waveId);
        }

        return ERPLockUtils.createERPLock(staff.getDbNo(), staff.getCompanyId(), "wave_post_" + staff.getCompanyId() + "_" + waveId + "_" + StringUtils.substring(param.getOuterId(), 0, 64));
    }

    @Override
    @Transactional
    public WavePickingScanResult scanBatchByOuterId(Staff staff, WavePickingParam param) {
        WavePickingScanResult result = new WavePickingScanResult();
        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        WaveConfig waveConfig = waveConfigService.get(staff);
        String configValueStr = waveConfig.getString(WaveChatConfigsEnum.POST_PRINT_BATCH_LIMIT_MAX.getKey());
        int limitMax = StringUtils.isEmpty(configValueStr) ? 0 : Integer.parseInt(configValueStr);
        if (param.getPrintNum() != null && limitMax != 0 && param.getPrintNum() > limitMax) {
            throw new IllegalArgumentException("数量超过上限值，请减少打印数量");
        }
        param.setPostPrintMatchPriorityShopList(waveConfig.getPostPrintMatchPriorityShopList());
        param.setPostPrintMatchTradeSort(waveConfig.getPostPrintMatchTradeSort());
        param.setTradeConfigCache(tradeConfig);
        param.setMatchTradePickedFirst(WaveHelpBusiness.isOpenPostPrintPickFirst(waveConfig));
        // 预发货订单优先
        param.setOpenPostPrintUploadConsignFirst(Objects.equal(waveConfig.getInteger(WaveChatConfigsEnum.OPEN_POST_PRINT_UPLOAD_CONSIGN_FIRST.getKey()), 1));
        param.setMemoRemarkTradePrintFirst(Objects.equal(waveConfig.getInteger(WaveChatConfigsEnum.MEMO_REMARK_TRADE_PRINT_FIRST.getKey()), 1));
        param.setSupportMultiCode(1);
        result.setItem(scanItem(staff, param));
        result.setEntitySuitItem(scanEntityItem2Suit(staff, param, tradeConfig));
        result.setPickingCode(param.getPickingCode());
        param.setSuitMode(result.getItem() != null && result.getItem().isSuite() ? CommonConstants.JUDGE_YES : CommonConstants.JUDGE_NO);
        WavePickingScanResult wavePickingScanResult = lockService.lock(getPostLock(staff, param), () -> efficientWaveHandler.scanBatchByOuterId(staff, param, result));
        // 包材处理
        dealPack(staff, param, result, true);
        return wavePickingScanResult;
    }

    @Override
    public WavePickingScanResult getItemInfoForBatchPrint(Staff staff, WavePickingParam param) {
        WavePickingScanResult result = new WavePickingScanResult();
        try {
            TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
            WaveConfig waveConfig = waveConfigService.get(staff);
            param.setPostPrintMatchPriorityShopList(waveConfig.getPostPrintMatchPriorityShopList());
            param.setPostPrintMatchTradeSort(waveConfig.getPostPrintMatchTradeSort());
            param.setSupportMultiCode(1);
            result.setPickingCode(param.getPickingCode());
            result.setItem(scanItem(staff, param));
            return efficientWaveHandler.getItemInfoForBatchPrint(staff, param, result);
        } catch (Exception exception) {
            // 盲扫异步不抛出异常，记录debug日志方便排查
            logger.debug(LogHelper.buildLog(staff, exception.getMessage()));
        }
        return result;
    }

    private List<String> setSortingShopName(Staff staff, List<WaveSorting> sortings, List<Trade> trades) {
        if (CollectionUtils.isEmpty(sortings) || CollectionUtils.isEmpty(trades)) {
            return null;
        }

        Map<Long, String> userIdShopNameMap = queryUserIdShopNameMap(staff);
        Map<Long, Long> sidUserIdMap = trades.stream().collect(Collectors.toMap(Trade::getSid, Trade::getUserId));
        Map<Long, String> waveUserIdShopNameMap = Maps.newTreeMap(Long::compare);
        for (WaveSorting sorting : sortings) {
            Long userId = sidUserIdMap.get(sorting.getSid());
            if (userId != null) {
                sorting.setShopName(userIdShopNameMap.get(userId));
                waveUserIdShopNameMap.put(userId, sorting.getShopName());
            }
        }

        return Lists.newArrayList(waveUserIdShopNameMap.values());
    }

    private void handleScanOuterCodes(Staff staff, WavePickingParam param, WaveConfig waveConfig) {
        if (StringUtils.isEmpty(param.getOuterCodes())) {
            return;
        }
        JSONObject outerIdJson = JSONObject.parseObject(param.getOuterCodes());
        boolean scanWithPosNo = Objects.equal(waveConfig.getSeedScanType(), 1);
        boolean multiWaves = Boolean.TRUE.equals(param.getMultiWaves());
        boolean seedCheck = Objects.equal(param.getScanType(), WavePickingScanParam.SCAN_TYPE_SEED_CHECK);

        //当前扫描的条码
        WaveUtils.MultiWavesSeedKey currentSeedKey = null;

        //解析所有有效的条码
        List<WaveUtils.MultiWavesSeedKey> subKeys = Lists.newArrayListWithCapacity(outerIdJson.size());
        List<TradePackScanInfo> tradePackScanInfoList = Lists.newArrayListWithCapacity(outerIdJson.size());
        for (Map.Entry<String, Object> entry : outerIdJson.entrySet()) {
            String outerIdKey = entry.getKey();
            WaveUtils.MultiWavesSeedKey subSeedKey;
            if (multiWaves) {
                subSeedKey = WaveUtils.splitMultiWavesBarcode(outerIdKey, waveConfig);
                Assert.notNull(subSeedKey, String.format("多波次后置打印扫描的条码数据不符合格式，条码数据为：%s", WaveUtils.formatMultiWaveSeedBarcode(outerIdKey)));
            } else if (scanWithPosNo) {
                subSeedKey = WaveUtils.splitWaveItemPositionBarcode(outerIdKey);
                Assert.notNull(subSeedKey, String.format("后置打印扫描的条码数据不符合格式，条码数据为：%s", WaveUtils.formatMultiWaveSeedBarcode(outerIdKey)));
            } else if (seedCheck) {
                // 播种复验唯一码绑定，商品唯一码和订单唯一码
                WaveUniqueCode bindUniqueCode = new WaveUniqueCode();
                bindUniqueCode.setUniqueCode(outerIdKey);
                param.setBindUniqueCode(bindUniqueCode);
                WaveUniqueCode bindWaveUniqueCode = postPrintUniqueCodeBusiness.checkBindUniqueCode(staff, param);
                if (bindWaveUniqueCode != null) {
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, String.format("播种复验，uniqueCode：【%s】，outerId：【%s】", bindWaveUniqueCode.getUniqueCode(), bindWaveUniqueCode.getOuterId())));
                    }
                    subSeedKey = new WaveUtils.MultiWavesSeedKey(StringUtils.isNotEmpty(bindWaveUniqueCode.getSkuOuterId()) ? bindWaveUniqueCode.getSkuOuterId() : bindWaveUniqueCode.getMainOuterId(), bindWaveUniqueCode);
                } else if (WaveUtils.isWaveUniqueCodeBarcode(outerIdKey)) {
                    // 波次唯一码
                    subSeedKey = WaveUtils.splitMultiWavesBarcode(outerIdKey, waveConfig);
                    Assert.notNull(subSeedKey, String.format("播种复验扫描条码数据不符合格式，条码数据为：%s", WaveUtils.formatMultiWaveSeedBarcode(outerIdKey)));
                } else {
                    subSeedKey = new WaveUtils.MultiWavesSeedKey(outerIdKey, null, null);
                }
            } else {
                WaveUniqueCode bindWaveUniqueCode = JSONObject.parseObject(JSON.toJSONString(((JSONObject) entry.getValue()).get("bindWaveUniqueCode")), WaveUniqueCode.class);
                if (bindWaveUniqueCode != null) {
                    // 唯一码仓库和波次仓库不一样 && 是强唯一码
                    boolean flag = !Objects.equal(bindWaveUniqueCode.getWarehouseId(), param.getWarehouseId()) && !postPrintUniqueCodeBusiness.skipWarehouseJudge(staff, bindWaveUniqueCode);
                    if (flag) {
                        throw new WaveScanException(WaveScanException.UNIQUE_CODE_WAREHOUSE_NOT_MATCH, "订单和唯一码仓库不匹配，请重新扫描！");
                    }
                    // 后置打印唯一码绑定
                    subSeedKey = new WaveUtils.MultiWavesSeedKey(bindWaveUniqueCode.getOuterId(), bindWaveUniqueCode);
                    param.setBindUniqueCode(bindWaveUniqueCode);
                } else {
                    subSeedKey = new WaveUtils.MultiWavesSeedKey(outerIdKey, null, null);
                }
            }
            subSeedKey.setNum(((JSONObject) entry.getValue()).getInteger("num"));
            Assert.notNull(subSeedKey.getNum(), String.format("条码%s对应的数量不能为空！", outerIdKey));
            subKeys.add(subSeedKey);
            String multiCode = ((JSONObject) entry.getValue()).getString("multiCode");
            String scanCode = StringUtils.isNotEmpty(multiCode) ? multiCode : outerIdKey;
            tradePackScanInfoList.add(TradePackScanInfo.buildTradePackScanInfo(subSeedKey.getOuterId(), null, subSeedKey.getNum(), WaveUtils.getScanCodeType(subSeedKey.getOuterId(), scanCode, param.getScanCodeType()), scanCode));

            if (outerIdKey.equals(param.getOuterId())) {
                currentSeedKey = subSeedKey;
            }
        }

        WaveUtils.MultiWavesSeedKey current = currentSeedKey;
        if (multiWaves && current != null) {
            //填充唯一码中缺少的信息，便于过滤和分组
            waveHelpBusiness.fillMultiWaveSeedKey(staff, subKeys);
            Assert.isTrue(current.getPositionNo() != null || current.getSid() != null, "多波次条码没有找到关联的位置号或sid！" + param.getOuterId());
            //多波次需要过滤出拣选号和位置号一致的
            subKeys = subKeys.stream().filter(subKey -> current.getPickingCode().equals(subKey.getPickingCode()) && (subKey.getSid() != null ? subKey.getSid().equals(current.getSid()) : current.getPositionNo().equals(subKey.getPositionNo()))).collect(Collectors.toList());
        }
        if (scanWithPosNo && current != null && current.getPositionNo() != null) {
            subKeys.forEach(subKey -> Assert.isTrue(current.getPositionNo().equals(subKey.getPositionNo()), "请不要扫描多个商品位置号！"));
        }

        if (current != null) {
            param.setOuterId(current.getOuterId());
        }

        List<WaveSortingDetail> details = subKeys.stream().map(this::convertSeedKey).collect(Collectors.toList());
        if (Objects.equal(waveConfig.getOpenWaveUniqueCode(), 1)
                && ((currentSeedKey != null && StringUtils.isNotEmpty(currentSeedKey.getUniqueCode()))
                || (seedCheck && !details.isEmpty() && StringUtils.isNotEmpty(details.get(0).getUniqueCode())))) {
            details = mergeOuterCodes(details);
        }
        param.setDetails(details);
        param.setTradePackScanInfoList(tradePackScanInfoList);
    }

    private WaveSortingDetail convertSeedKey(WaveUtils.MultiWavesSeedKey seedKey) {
        WaveSortingDetail detail = new WaveSortingDetail();
        detail.setOuterId(seedKey.getOuterId());
        detail.setPositionNo(seedKey.getPositionNo());
        detail.setUniqueCode(seedKey.getUniqueCode());
        detail.setItemNum(seedKey.getNum());
        detail.setBindUniqueCode(seedKey.getBindUniqueCode());
        return detail;
    }

    private List<WaveSortingDetail> mergeOuterCodes(List<WaveSortingDetail> details) {
        Map<String, List<WaveSortingDetail>> outerIdDetailsMap = details.stream().collect(Collectors.groupingBy(WaveSortingDetail::getOuterId));
        List<WaveSortingDetail> resultList = Lists.newArrayListWithCapacity(outerIdDetailsMap.size());
        Set<String> uniqueCodes = Sets.newHashSetWithExpectedSize(details.size());
        for (Map.Entry<String, List<WaveSortingDetail>> entry : outerIdDetailsMap.entrySet()) {
            WaveSortingDetail merge = new WaveSortingDetail();
            merge.setOuterId(entry.getKey());
            List<WaveSortingDetail> subDetails = entry.getValue();
            List<WaveUniqueCode> subUniqueCodes = Lists.newArrayListWithCapacity(subDetails.size());

            int totalNum = 0;
            for (WaveSortingDetail subDetail : subDetails) {
                if (subDetail.getItemNum() != null && subDetail.getItemNum() > 0) {
                    WaveUniqueCode subUniqueCode = new WaveUniqueCode();
                    subUniqueCode.setUniqueCode(subDetail.getUniqueCode());
                    Assert.hasText(subDetail.getUniqueCode(), "唯一码不能为空！");
                    Assert.isTrue(subDetail.getItemNum() == 1, "唯一码重复扫描：" + subDetail.getUniqueCode());
                    Assert.isTrue(!uniqueCodes.contains(subDetail.getUniqueCode()), "唯一码重复扫描：" + subDetail.getUniqueCode());

                    uniqueCodes.add(subDetail.getUniqueCode());
                    subUniqueCodes.add(subUniqueCode);
                    totalNum += subDetail.getItemNum();
                }
            }
            merge.setItemNum(totalNum);
            merge.setUniqueCodes(subUniqueCodes);
            resultList.add(merge);
        }
        return resultList;
    }

    /**
     * 将套件转成对应的单品明细和数量
     */
    private void handleSuitOuterCodes(WavePickingParam param, DmjItem item) {
        if (item == null || !item.isSuite()) {
            return;
        }

        List<SuiteSingle> suits = item.getSuiteSingleList();
        Assert.notEmpty(suits, "找不到套件对应的单品！");

        List<WaveSortingDetail> details = param.getDetails();
        WaveSortingDetail suitSelf = null;
        List<WaveSortingDetail> inserts = Lists.newArrayListWithCapacity(suits.size());
        int totalNum = 0;
        for (WaveSortingDetail detail : details) {
            if (item.getOuterId().equalsIgnoreCase(detail.getOuterId())) {
                suitSelf = detail;
                for (SuiteSingle suit : suits) {
                    WaveSortingDetail suitDetail = new WaveSortingDetail();
                    suitDetail.setOuterId(StringUtils.isNotEmpty(suit.getSkuOuterId()) ? suit.getSkuOuterId() : suit.getOuterId());
                    suitDetail.setItemNum(detail.getItemNum() * suit.getRatio());
                    totalNum += suitDetail.getItemNum();
                    inserts.add(suitDetail);
                }
                break;
            }
        }

        Assert.notNull(suitSelf, "找不到该套件！");

        Map<String, WaveSortingDetail> outerIdDetailMap = Maps.newHashMapWithExpectedSize(details.size());
        details.addAll(inserts);

        for (WaveSortingDetail detail : details) {
            if (suitSelf == detail) {
                continue;
            }
            WaveSortingDetail mergeDetail = outerIdDetailMap.get(detail.getOuterId().toLowerCase());

            if (mergeDetail == null) {
                outerIdDetailMap.put(detail.getOuterId().toLowerCase(), detail);
            } else {
                mergeDetail.setItemNum(mergeDetail.getItemNum() + detail.getItemNum());
            }
        }
        param.setTotalNum(totalNum);
        param.setDetails(Lists.newArrayList(outerIdDetailMap.values()));
    }


    @Override
    @Transactional
    public WavePicking coverSeedCheckResult(Staff staff, WavePickingParam param) {
        param.setScanType(WavePickingScanParam.SCAN_TYPE_SEED_CHECK);
        WavePicking wavePicking = wavePickingDao.queryById(staff, param.getPickingId());
        List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySid(staff, param.getPickingId(), param.getSid(), false);
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (wavePicking != null && DataUtils.checkLongNotEmpty(wavePicking.getWaveId())) {
            Wave wave = waveDao.queryById(staff, wavePicking.getWaveId());
            param.setWarehouseId(wave.getWarehouseId());
        }

        handleScanOuterCodes(staff, param, waveConfig);

        List<WaveSortingDetail> outerCodes = param.getDetails();
        Map<String, Integer> outerIdNumMap = Maps.newHashMapWithExpectedSize(outerCodes.size());
        // 唯一码和商家编码混合，需要聚合
        for (WaveSortingDetail detail : outerCodes) {
            outerIdNumMap.merge(detail.getOuterId(), detail.getItemNum(), Integer::sum);
        }

        List<WaveSortingDetail> modifyDetails = Lists.newArrayList();
        int itemNum = 0, matchedNum = 0, coverNum = 0;
        Long sortingId = 0L;
        Map<String, List<WaveSortingDetail>> outerIdDetailsMap = toOuterIdDetailsMap(details);
        for (Map.Entry<String, List<WaveSortingDetail>> entry : outerIdDetailsMap.entrySet()) {
            int num = ObjectUtils.defaultIfNull(outerIdNumMap.get(entry.getKey()), 0);
            for (WaveSortingDetail detail : entry.getValue()) {
                sortingId = detail.getSortingId();
                itemNum += detail.getItemNum();
                matchedNum += detail.getMatchedNum();
                int detailCoverNum = Math.min(detail.getItemNum(), Math.max(num, 0));
                modifyDetails.add(buildModifyDetail(detail, detailCoverNum, wavePicking.getWaveId()));
                coverNum += detailCoverNum;
                num -= detailCoverNum;
            }
        }

        if (itemNum == matchedNum) {
            throw new IllegalArgumentException("该位置号已完成播种，不能进行复验！");
        }

        if (Objects.equal(waveConfig.getOpenWaveUniqueCode(), 1)) {
            handleCoverUniqueCodes(staff, wavePicking, param.getSid(), outerCodes);
        }

        if (modifyDetails.size() > 0) {
            waveSeedLogDao.deleteBySortingId(staff, sortingId);
            waveSortingDao.batchUpdateDetails(staff, modifyDetails);
            WaveSorting waveSorting = new WaveSorting();
            waveSorting.setId(sortingId);
            waveSorting.setMatchedStatus(coverNum == 0 ? WaveSorting.MATCHED_STATUS_NOT : (coverNum < itemNum ? WaveSorting.MATCHED_STATUS_ING : WaveSorting.MATCHED_STATUS_OVER));
            waveSortingDao.updateStatus(staff, waveSorting);
            logger.debug(LogHelper.buildLog(staff, String.format("波次播种复验覆盖，拣选号：%s，位置号：%s，商品组合：%s", param.getPickingId(), param.getPositionNo(), param.getOuterCodes())));
            List<WaveSortingDetail> filterList = modifyDetails.stream().filter(waveSortingDetail -> waveSortingDetail.getChangeMatchedNum() > 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)) {
                waveSeedLogDao.batchInsert(staff, WaveUtils.buildSeedLogs(wavePicking.getWaveId(), filterList));
            }

            // 唯一码解绑和绑定
            postPrintUniqueCodeBusiness.handleBindCodes(staff, outerCodes, modifyDetails, param.getSid());
        }
        return wavePicking;
    }

    @Override
    @Transactional
    public WaveSeedForOpenResult seedForOpen(Staff staff, WavePickingParam param) {
        Long waveId = param.getWaveId();
        Assert.notNull(waveId, "请选择波次！");
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("开放平台进行播种匹配，波次Id：%s", waveId)));
        }

        Wave wave = tradeWaveService.queryWaveById(staff, waveId);
        if (wave == null) {
            throw new IllegalArgumentException(String.format("该波次[%s]不存在！", waveId));
        }
        if (Wave.STATUS_FINISHED == wave.getStatus() || WaveDistributionStatus.EXAMINING.getValue().equals(wave.getDistributionStatus())) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("开放平台进行播种匹配，该波次[%s]已完成或者验货中！", waveId)));
            }
            throw new IllegalArgumentException(String.format("开放平台进行播种匹配，该波次[%s]已完成或者验货中！", waveId));
        }

        checkAssignSorter(staff, wave);
        Assert.isTrue(WaveDistributionStatus.PICKED.getValue().equals(wave.getDistributionStatus())
                || WaveDistributionStatus.WAIT_SEED.getValue().equals(wave.getDistributionStatus())
                || WaveDistributionStatus.SEEDING.getValue().equals(wave.getDistributionStatus())
                || WaveDistributionStatus.WAIT_EXAMINE.getValue().equals(wave.getDistributionStatus()), "该波次配货状态无法直接播种！");
        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        fillRealOuterId(staff, param);
        return lockService.lock(getSeedLock(staff, waveId, waveId.toString()), () -> {
            WavePicking wavePicking = wavePickingDao.getByWaveId(staff, waveId);
            Assert.notNull(wavePicking, String.format("根据波次号[%s]找不到拣选记录!", waveId));
            seeding(staff, wave, wavePicking);
            return seedForOpen(staff, waveId, wavePicking, tradeConfig, param);
        });
    }

    private void fillRealOuterId(Staff staff, WavePickingParam param) {
        List<WaveSortingDetail> details = param.getDetails();
        List<String> outerIds = details.stream().map(WaveSortingDetail::getOuterId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outerIds)) {
            return;
        }
        Map<String, String> realOuterIdMaps = tradeWaveService.queryOuterIdByMultiCodes(staff, outerIds);
        if (org.apache.commons.collections.MapUtils.isEmpty(realOuterIdMaps)) {
            return;
        }
        for (WaveSortingDetail detail : details) {
            String outerId = StringUtils.defaultString(detail.getOuterId()).toLowerCase();
            if (StringUtils.isEmpty(outerId)) {
                continue;
            }
            if (StringUtils.isEmpty(realOuterIdMaps.get(outerId))) {
                continue;
            }
            detail.setOuterId(realOuterIdMaps.get(outerId));
        }
    }

    private WaveSeedForOpenResult seedForOpen(Staff staff, Long waveId, WavePicking wavePicking, TradeConfig tradeConfig, WavePickingParam param) {
        Long picking = wavePicking.getId();
        WaveSeedForOpenResult result = new WaveSeedForOpenResult();
        WaveConfig waveConfig = waveConfigService.get(staff);
        List<WaveSorting> waveSortings = waveSortingService.matchAllByWaveIdForOpen(staff, picking, param, tradeConfig, result, waveConfig);

        List<WaveSorting> seeded = waveSortings.stream().filter(waveSorting -> waveSorting.getMatchedStatus() == WaveSorting.MATCHED_STATUS_OVER).collect(Collectors.toList());
        if (!seeded.isEmpty()) {
            eventCenter.fireEvent(this, new EventInfo("trade.wave.seed.over").setArgs(new Object[]{staff}), seeded.stream().map(waveSorting -> createTradeByWaveSortingForTrace(staff, waveSorting)).collect(Collectors.toList()));
        }

        if (waveSortings.size() == seeded.size()) {
            handleSeedOver(staff, waveId, picking, tradeConfig, waveConfig);
        } else {
            Wave update = new Wave();
            update.setId(waveId);
            update.setDistributionStatus(WaveDistributionStatus.SEEDING.getValue());
            waveDao.update(staff, update);

            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("开放平台播种波次订单，修改配货状态:%s，波次id：%s", WaveDistributionStatus.SEEDING.getValue(), waveId)));
            }
        }

        // 多件波次订单自动包装验货
        //eventCenter.fireEvent(this, new EventInfo("trade.wave.auto.package").setArgs(new Object[]{staff, waveId, false, false}), null);
        //日志区分
        WaveTraceOperateEnum waveSeedEnum = WaveTraceOperateEnum.WAVE_SEED_OPEN;
        waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, waveId, waveSeedEnum, waveSeedEnum.getOperate()));
        return result;
    }

    private void handleCoverUniqueCodes(Staff staff, WavePicking wavePicking, Long sid, List<WaveSortingDetail> outerCodes) {
        List<String> uniqueCodes = outerCodes.stream().filter(detail -> CollectionUtils.isNotEmpty(detail.getUniqueCodes()) && ObjectUtils.defaultIfNull(detail.getItemNum(), 0) > 0).flatMap(detail -> detail.getUniqueCodes().stream()).map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
        List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeDao.queryByKeys(staff, Collections.singletonList(wavePicking.getWaveId()), Collections.singletonList(sid), null);
        List<WaveUniqueCode> updateUniqueCodes = Lists.newArrayListWithCapacity(waveUniqueCodes.size());
        for (WaveUniqueCode waveUniqueCode : waveUniqueCodes) {
            WaveUniqueCode updateUniqueCode = new WaveUniqueCode();
            updateUniqueCode.setId(waveUniqueCode.getId());
            updateUniqueCode.setMatchedStatus(uniqueCodes.contains(waveUniqueCode.getUniqueCode()) ? WaveSorting.MATCHED_STATUS_OVER : WaveSorting.MATCHED_STATUS_NOT);
            updateUniqueCodes.add(updateUniqueCode);
        }

        waveUniqueCodeDao.batchUpdate(staff, updateUniqueCodes);
    }

    private WaveSortingDetail buildModifyDetail(WaveSortingDetail detail, int coverNum, Long waveId) {
        WaveSortingDetail modifyDetail = new WaveSortingDetail();
        modifyDetail.setId(detail.getId());
        modifyDetail.setMatchedNum(coverNum);
        modifyDetail.setChangeMatchedNum(coverNum);
        modifyDetail.setSortingId(detail.getSortingId());
        modifyDetail.setMatchedStatus(coverNum == 0 ? WaveSorting.MATCHED_STATUS_NOT : (coverNum < detail.getItemNum() ? WaveSorting.MATCHED_STATUS_ING : WaveSorting.MATCHED_STATUS_OVER));
        modifyDetail.setOuterId(detail.getOuterId());
        modifyDetail.setItemNum(detail.getItemNum());
        modifyDetail.setSid(detail.getSid());
        modifyDetail.setOrderId(detail.getOrderId());
        modifyDetail.setWaveId(waveId);
        return modifyDetail;
    }


    private Map<String, List<WaveSortingDetail>> toOuterIdDetailsMap(List<WaveSortingDetail> details) {
        Map<String, List<WaveSortingDetail>> outerIdDetailsMap = Maps.newHashMap();
        for (WaveSortingDetail detail : details) {
            List<WaveSortingDetail> outerIdDetails = outerIdDetailsMap.get(detail.getOuterId());
            if (outerIdDetails == null) {
                outerIdDetailsMap.put(detail.getOuterId(), Lists.newArrayList(detail));
            } else {
                outerIdDetails.add(detail);
            }
        }

        return outerIdDetailsMap;
    }

    @Override
    @Transactional
    public void printSortingSids(Staff staff, List<Long> sids, WavePrintType printType) {
        if (CollectionUtils.isEmpty(sids)) {
            logger.error(LogHelper.buildLog(staff, "打印分拣的订单，sid为空！"));
            return;
        }

        waveSortingService.printSortingSids(staff, sids);
        eventCenter.fireEvent(this, new EventInfo("wave.scan.trades").setArgs(new Object[]{staff, sids, printType}), null);
    }

    private void createWaveSortings(Staff staff, WavePicking wavePicking) {
        createWaveSortings(staff, wavePicking, null, true, false);
    }

    /**
     * 根据波次号拣选id查找对应的订单，生成波次打印相关信息
     *  @param wavePicking 波次拣选
     * @param querySids
     * @param byHand 是否手动拣选
     */
    @Override
    public void createWaveSortings(Staff staff, WavePicking wavePicking, List<Long> querySids, boolean careGift, boolean byHand) {
        List<Long> sids = CollectionUtils.isEmpty(querySids)
                ? tradeWaveService.querySidsByWaveId(staff, wavePicking.getWaveId())
                : querySids;
        Assert.notEmpty(sids, "波次订单不存在！");
        long start = System.currentTimeMillis();
        boolean isOver = wavePicking.getEnableStatus() == WavePicking.STATUS_OVER;
        List<List<Long>> subList = Lists.partition(sids, 2000);
        List<Trade> trades = new ArrayList<>();
        for (List<Long> sidList : subList) {
            List<Trade> subTrades = waveUseTradeServiceProxy.queryBySidsNoFilter(staff, true, Lists.newArrayList(sidList).toArray(new Long[0]));
            if (CollectionUtils.isNotEmpty(subTrades)){
                trades.addAll(subTrades);
            }
        }
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        List<WaveSorting> waveSortings = Lists.newArrayListWithExpectedSize(trades.size());
        String freeScanLog = null;
        for (Trade trade : trades) {
            Map<String, Integer> itemNumMap = Maps.newHashMap();
            WaveSorting waveSorting = createWaveSorting(wavePicking);
            boolean giftCheck = true;
            waveSorting.setSid(trade.getSid());
            List<Order> orders = WaveUtils.getCanSortingOrders(trade);
            if (orders.isEmpty()) {
                continue;
            }

            List<WaveSortingDetail> details = Lists.newArrayListWithCapacity(orders.size());
            for (Order order : orders) {
                boolean orderGiftCheck = true;
                // 如果关心赠品
                if (careGift) {
                    if (giftNotCheck(order)) {
                        giftCheck = false;
                        orderGiftCheck = false;
                    }
                    // 赠品不参与拣选也不参与验货，不生成waveSortingDetail
                    if (giftNotPickNotCheck(order)) {
                        continue;
                    }
                }

                String itemKey = order.getItemSysId() + "_" + order.getSkuSysId();
                itemNumMap.merge(itemKey, orderGiftCheck ? order.getNum() : 0, (a, b) -> a + b);

                WaveSortingDetail detail = createSortingDetailByOrder(order);
                if (detail.getGiftPickAndCheck() != null && !detail.getGiftPickAndCheck().equals(2)) {
                    freeScanLog = "免拣免验统计";
                }
                detail.setPickedNum(isOver ? detail.getItemNum() : 0);
                detail.setPickingId(waveSorting.getPickingId());
                // 填充拣选员
                if (isOver) {
                    fillSortingDetailPicker(staff, detail, byHand, wavePicking);
                }
                if (!careGift) {
                    detail.setGiftPickAndCheck(2);
                }
                details.add(detail);
            }
            if (itemNumMap.size() == 0) {
                continue;
            }
            for (Map.Entry<String, Integer> itemNumEntry : itemNumMap.entrySet()) {
                for (WaveSortingDetail detail : details) {
                    String itemKey = detail.getSysItemId() + "_" + detail.getSysSkuId();
                    if (itemKey.equals(itemNumEntry.getKey())) {
                        detail.setTotalNum(itemNumEntry.getValue());
                        break;
                    }
                }
            }
            waveSorting.setGiftCheck(BooleanUtils.toInteger(giftCheck));
            waveSorting.setDetails(details);
            if (trade.getExpressPrintTime() != null && trade.getExpressPrintTime().after(TradeTimeUtils.INIT_DATE)) {
                waveSorting.setPrintStatus(1);
            } else {
                waveSorting.setPrintStatus(0);
            }
            waveSortings.add(waveSorting);
        }

        Assert.notEmpty(waveSortings, "当前波次无可拣选商品！");
        //如果有波次订单记录中分配的位置号，则优先使用该位置号
        allocatePositionNo(staff, wavePicking.getWaveId(), waveSortings);
        waveSortingService.batchInsert(staff, waveSortings,false);
        logger.debug(LogHelper.buildLog(staff, String.format("生成波次%s分拣信息完成，耗时=%s", wavePicking.getWaveId(), (System.currentTimeMillis() - start))));
        if (StringUtils.isNotEmpty(freeScanLog)) {
            logger.debug(LogHelper.buildLog(staff, freeScanLog));
        }
    }


    private WaveSorting createWaveSorting(WavePicking picking) {
        WaveSorting waveSorting = new WaveSorting();
        waveSorting.setPickingId(picking.getId());
        waveSorting.setWaveId(picking.getWaveId());
        waveSorting.setCreated(new Date());
        waveSorting.setMatchedStatus(WaveSorting.MATCHED_STATUS_NOT);
        return waveSorting;
    }

    private WaveSortingDetail createSortingDetailByOrder(Order order) {
        WaveSortingDetail detail = new WaveSortingDetail();
        detail.setSid(order.getSid());
        detail.setOrderId(order.getId());
        //0 是普通 1 是套件  （赠品已经被提前过来了，简单当做0处理）
        detail.setSuitType(WaveUtils.getWaveSortingDetailSuitType(order.getType()));
        detail.setCombineId(order.getCombineId());
        detail.setSysItemId(order.getItemSysId());
        detail.setSysSkuId(order.getSkuSysId());
        detail.setOuterId(order.getSysOuterId());
        detail.setItemNum(order.getNum());
        detail.setMatchedNum(0);
        detail.setTotalNum(0);
        detail.setMatchedStatus(WaveSorting.MATCHED_STATUS_NOT);
        detail.setGiftPickAndCheck(OrderUtils.buildPickCheckFlag(order));
        return detail;
    }

    /**
     * 赠品不参与拣选也不参与验货
     *
     * @param order
     * @return
     */
    private boolean giftNotPickNotCheck(Order order) {
        return OrderUtils.notPickNotCheck(order);
    }

    /**
     * 赠品不参与验货
     *
     * @param order
     * @return
     */
    private boolean giftNotCheck(Order order) {
        return OrderUtils.notCheck(order);
    }

    public void allocatePositionNo(Staff staff, Long waveId, List<WaveSorting> waveSortings) {
        List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradeByWaveId(staff, waveId);
        if (CollectionUtils.isNotEmpty(waveTrades)) {
            Map<Long, WaveTrade> sidPositionNoMap = Maps.newHashMapWithExpectedSize(waveTrades.size());
            for (WaveTrade waveTrade : waveTrades) {
                sidPositionNoMap.put(waveTrade.getSid(), waveTrade);
            }
            for (WaveSorting waveSorting : waveSortings) {
                WaveTrade waveTrade = sidPositionNoMap.get(waveSorting.getSid());
                Assert.notNull(waveTrade, String.format("找不到订单%s的位置号信息！", waveSorting.getSid()));
                waveSorting.setPositionCode(WaveUtils.generatePositionCode(staff, waveTrade.getPositionNo()));
                waveSorting.setPositionNo(waveTrade.getPositionNo().intValue());
                waveSorting.setWaveTradeId(waveTrade.getId());
            }

            waveSortings.sort(Comparator.comparing(WaveSorting::getWaveTradeId));
        }
    }

    @Override
    public WavePicking getPickingByWaveId(Staff staff, Long waveId, Integer enableStatus) {
        return wavePickingDao.getPickingByWaveId(staff, waveId, enableStatus);
    }

    @Override
    public WavePickContext getWaveAndPickingByWaveId(Staff staff, Long waveId) {
        WavePickContext wavePickContext = new WavePickContext();
        WavePicking wavePicking = wavePickingDao.getPickingByWaveId(staff, waveId, null);
        Wave wave = tradeWaveService.queryWaveById(staff, waveId, false);
        wavePickContext.setWavePicking(wavePicking);
        wavePickContext.setWave(wave);
        return wavePickContext;
    }

    @Override
    @Transactional
    public int cancelWavePicking(Staff staff, Long waveId) throws Exception {
        Wave wave = tradeWaveService.queryWaveById(staff, waveId);
        Assert.notNull(wave, "该波次不存在！");

        WavePicking picking = wavePickingDao.getByWaveId(staff, waveId);
        Assert.notNull(picking, "拣选不存在！");
        WavePicking update = new WavePicking();
        update.setId(picking.getId());
        update.setWaveId(waveId);
        if (WavePickUtils.isTradeWave(wave)) {
            wavePickingDao.deleteByWave(staff, update);
            waveSortingService.deleteWaveSortingByPickingId(staff, picking.getId());
        } else {
            //非交易波次重置拣选号
            update.setPickingCode("");
            //重置拣选员
            update.setPickerId(0L);
            update.setPickerName("");
            update.setSubSectionPickerIds("");
            wavePickingDao.update(staff, update);
            deleteMultiPickingCode(staff, update.getId());
        }

        updateWavePicker(staff, wave.getId(), false, true);
        tradeWaveService.updateDistributionStatus(staff, waveId, Wave.DISTRIBUTION_STATUS_NONE);
        tradeWaveService.updateWaveFailedLast(staff, Lists.newArrayList(waveId), CommonConstants.JUDGE_NO);
        tradeWaveService.addItemTraceLogByCancelPick(staff, Lists.newArrayList(wave));
        eventCenter.fireEvent(this, new EventInfo("wave.picking.cancel").setArgs(new Object[]{staff, Lists.newArrayList(wave.getId())}), null);
        logger.debug(LogHelper.buildLog(staff, String.format("取消波次[%s]拣选，回收拣选号，重置波次拣选状态", waveId)));
        return 1;
    }

    private void deleteMultiPickingCode(Staff staff, Long pickingId) {
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (waveConfig == null || waveConfig.getInteger(WaveChatConfigsEnum.ONE_WAVE_MULTI_PICKING_CODE.getKey()) == 0) {
            return;
        }
        wavePickingCodeDao.deleteByPickingId(staff, pickingId);
    }

    @Override
    @Transactional
    public void cancelSubWavePicking(Staff staff, WavePicking wavePicking) {
        Assert.notNull(wavePicking.getWaveId(), "波次号不存在！");
        WavePicking picking = wavePickingDao.getByWaveId(staff, wavePicking.getWaveId());
        Assert.notNull(picking, "拣选不存在！");
        Map<Long, String> staffId2NameMap = wavePickerDao.getByWaveId(staff, wavePicking.getWaveId()).stream().collect(Collectors.toMap(WavePicker::getPickerId, WavePicker::getPickerName));
        Long pickerId = wavePicking.getPickerId();
        WavePicking update = new WavePicking();
        update.setId(picking.getId());
        update.setPickerId(pickerId);
        update.setPickerName(staffId2NameMap.getOrDefault(pickerId, ""));
        update.setSubSectionPickerIds(wavePicking.getSubSectionPickerIds());
        wavePickingDao.update(staff, update);
    }

    @Override
    public Map<Long, Long> querySidPositionNoMapByPickingId(Staff staff, Long pickingId) {
        Map<Long, Long> sidPositionNoMap = waveSortingService.querySidPositionNoMapByPickingId(staff, pickingId);
        if (wavePositionService.needClearPosition(staff, pickingId)) {
            Map<Long, Long> sidPositionNoMapNew = Maps.newHashMapWithExpectedSize(sidPositionNoMap.size());
            for (Map.Entry<Long, Long> entry : sidPositionNoMap.entrySet()) {
                sidPositionNoMapNew.put(entry.getKey(), -1L);
            }
            return sidPositionNoMapNew;
        }

        return sidPositionNoMap;
    }

    @Override
    public Map<Long, Long> querySidPositionNoMapByWaveId(Staff staff, Long waveId) {
        List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradeByWaveId(staff, waveId);
        Map<Long, Long> sidPositionMap = Maps.newLinkedHashMap();
        for (WaveTrade waveTrade : waveTrades) {
            sidPositionMap.put(waveTrade.getSid(), waveTrade.getPositionNo());
        }
        return sidPositionMap;
    }

    @Override
    public Map<Long, String> querySidPositionCodeMapByPickingId(Staff staff, Long pickingId) {
        Map<Long, Long> sidPositionNoMap = waveSortingService.querySidPositionNoMapByPickingId(staff, pickingId);
        Map<Long, String> sidPositionCodeMap = Maps.newHashMapWithExpectedSize(sidPositionNoMap.size());
        for (Map.Entry<Long, Long> entry : sidPositionNoMap.entrySet()) {
            sidPositionCodeMap.put(entry.getKey(), WaveUtils.generatePositionCode(staff, entry.getValue()));
        }
        return sidPositionCodeMap;
    }

    @Override
    public WaveSorting queryWaveSortingBySid(Staff staff, Long pickingId, Long sid) {
        WaveSorting sorting = waveSortingDao.queryWaveSortingBySid(staff, pickingId, sid);
        if (sorting != null) {
            sorting.setPositionCode(WaveUtils.generatePositionCode(staff, sorting.getPositionNo().longValue()));
            List<WaveSeedStat> waveSeedStatList = waveSeedLogDao.statSortingSeedInfo(staff, null, Lists.newArrayList(sorting.getId()));
            if (CollectionUtils.isNotEmpty(waveSeedStatList)) {
                sorting.setSeedEndTime(waveSeedStatList.get(0).getSeedEndTime());
            }
        }
        return sorting;
    }

    @Override
    public List<WaveTrade> queryWaveTradeByWaveIdAndSids(Staff staff, Long waveId, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return Lists.newArrayList();
        }
        logger.info(LogHelper.buildLog(staff, String.format("queryWaveTradeByWaveIdAndSids传参，sids：%s", sids)));
        Wave wave = waveDao.queryById(staff, waveId);
        boolean openLoopWave = wavePositionService.openLoopWave(staff, wave);
        WavePicking picking = wavePickingDao.getPickingByWaveId(staff, waveId, null);
        List<Long> exceptSids = getExceptSids(staff, waveId, wave);

        if (picking == null) {
            return getNoPickingWaveTrades(staff, waveId, sids, openLoopWave, exceptSids);
        } else {
            List<WaveTrade> waveTrades = getWaveTrades(staff, waveId, sids, openLoopWave, picking, exceptSids);
            //搜不到sorting的情况
            List<Long> existSids = waveTrades.stream().map(WaveTrade::getSid).collect(Collectors.toList());
            List<Long> noSortingSids = sids.stream().filter(sid -> !existSids.contains(sid)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(noSortingSids)) {
                return waveTrades;
            }
            List<WaveTrade> noSortingWaveTrades = getNoPickingWaveTrades(staff, waveId, noSortingSids, openLoopWave, exceptSids);
            noSortingWaveTrades.forEach(noSorting -> noSorting.setPickingCode(picking.getPickingCode()));
            waveTrades.addAll(noSortingWaveTrades);
            return waveTrades;
        }
    }

    private List<WaveTrade> getWaveTrades(Staff staff, Long waveId, List<Long> sids, boolean openLoopWave, WavePicking picking, List<Long> exceptSids) {
        List<WaveSorting> sortings = waveSortingDao.queryByPickingAndSids(staff, picking.getId(), sids);
        List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradeByWaveId4Print(staff, waveId, sortings.stream().map(w -> w.getPositionNo().longValue()).collect(Collectors.toList()));
        Map<Long, List<WaveTrade>> positionNoTradesMap = waveTrades.stream().collect(Collectors.groupingBy(WaveTrade::getPositionNo));
        for (List<WaveTrade> trades : positionNoTradesMap.values()) {
            trades.removeIf(t -> filterTradesByPrintPosition(exceptSids, t));
            trades.sort(Comparator.comparing(WaveTrade::getId));
        }
        logger.info(LogHelper.buildLog(staff, String.format("getWaveTrades，过滤后sids：%s",
                positionNoTradesMap.values().stream().flatMap(Collection::stream).map(WaveTrade::getSid).collect(Collectors.toList()))));

        return sortings.stream().map(sorting -> {
            WaveTrade waveTrade = new WaveTrade();
            waveTrade.setSid(sorting.getSid());
            waveTrade.setWaveId(waveId);
            waveTrade.setPositionNo(sorting.getPositionNo().longValue());
            //轮播时不会同时共享位置号,轮播时相同位置号不可以合并
            if (!openLoopWave) {
                List<WaveTrade> posTrades = positionNoTradesMap.get(waveTrade.getPositionNo());
                if (CollectionUtils.isNotEmpty(posTrades)) {
                    waveTrade.setPositionSidNum(posTrades.size());
                    for (int i = 0; i < posTrades.size(); i++) {
                        if (posTrades.get(i).getSid().equals(waveTrade.getSid())) {
                            waveTrade.setPositionSidIndex(i + 1);
                            break;
                        }
                    }
                }
            }
            waveTrade.setPickingCode(picking.getPickingCode());
            waveTrade.setPositionCode(WaveUtils.generatePositionCode(staff, sorting.getPositionNo().longValue()));
            return waveTrade;
        }).collect(Collectors.toList());
    }

    private List<WaveTrade> getNoPickingWaveTrades(Staff staff, Long waveId, List<Long> sids, boolean openLoopWave, List<Long> exceptSids) {
        List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradeByWaveId4Print(staff, waveId, Lists.newArrayList());
        Map<Long, List<WaveTrade>> positionNoTradesMap = waveTrades.stream().collect(Collectors.groupingBy(WaveTrade::getPositionNo));
        for (List<WaveTrade> trades : positionNoTradesMap.values()) {
            trades.removeIf(t -> filterTradesByPrintPosition(exceptSids, t));
            trades.sort(Comparator.comparing(WaveTrade::getId));
        }
        logger.info(LogHelper.buildLog(staff, String.format("getNoPickingWaveTrades，过滤后sids：%s",
                positionNoTradesMap.values().stream().flatMap(Collection::stream).map(WaveTrade::getSid).collect(Collectors.toList()))));
        return waveTrades.stream()
                .filter(waveTrade -> sids.contains(waveTrade.getSid()))
                .peek(waveTrade -> {
                    if (waveTrade.getTradeWaveStatus() != WaveTrade.TRADE_WAVE_STATUS_NOT) {
                        //如果未拣选且开启轮播位置号返回待分配
                        if (openLoopWave) {
                            waveTrade.setPositionNo(-1L);
                        } else {
                            List<WaveTrade> posTrades = positionNoTradesMap.get(waveTrade.getPositionNo());
                            waveTrade.setPositionSidNum(posTrades.size());
                            waveTrade.setPositionSidIndex(posTrades.indexOf(waveTrade) + 1);
                        }
                        waveTrade.setPositionCode(WaveUtils.generatePositionCode(staff, waveTrade.getPositionNo()));
                    }
                }).collect(Collectors.toList());
    }

    /**
     * 获取异常订单
     */
    private List<Long> getExceptSids(Staff staff, Long waveId, Wave wave) {
        boolean isSharePositionNo = Wave.PositionNoTypeEnum.SHARE.getValue().equals(wave.getPositionNoType());
        WaveFilterParams params = new WaveFilterParams();
        params.setIsExcep(CommonConstants.JUDGE_YES);
        params.setWithExcep(true);
        params.setWaveId(waveId);
        params.setWarehouseId(wave.getWarehouseId());
        return isSharePositionNo ? waveQueryDao.queryWaveSids(staff, params) : Lists.newArrayList();
    }

    /**
     * 打印同位置号总数,排除踢出波次订单和存在异常订单
     */
    private boolean filterTradesByPrintPosition(List<Long> exceptSids, WaveTrade t) {
        boolean isException = CollectionUtils.isNotEmpty(exceptSids) && exceptSids.contains(t.getSid());
        return Objects.equal(t.getTradeWaveStatus(), WaveTrade.TRADE_WAVE_STATUS_NOT) || isException;
    }

    @Override
    public WaveTrade queryWaveTradeByWaveIdAndSid(Staff staff, Long waveId, Long sid) {
        List<WaveTrade> waveTrades = queryWaveTradeByWaveIdAndSids(staff, waveId, Collections.singletonList(sid));
        return CollectionUtils.isNotEmpty(waveTrades) ? waveTrades.get(0) : null;
    }

    /**
     * 播种扫描适配
     */
    @Override
    @Transactional
    public WavePickingScanResult seedByOuterIdAdaptive(Staff staff, WavePickingParam param) {
        Wave wave = waveDao.queryById(staff, param.getWaveId());
        if (WavePickUtils.isNeedSeed4NoTradeWave(wave)) {
            return waveSeed4UnTradeWaveService.seedByOuterId(staff, param);
        }
        return this.seedByOuterId(staff, param);
    }

    @Override
    @Transactional
    public WavePickingScanResult seedByOuterId(Staff staff, WavePickingParam param) {
        long t1, t2, t3, t4, t5;
        long start = System.currentTimeMillis();
        TradeConfig tradeConfig = waveSortingService.getTradeConfigCache(staff, param);
        WaveConfig waveConfig = waveSortingService.getWaveConfigCache(staff, param);
        checkSeedParam(staff, waveConfig, param);
        Long waveId = param.getWaveId();
        t1 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        DmjItem item = handleSeedOuterId(staff, param);
        t2 = System.currentTimeMillis() - start;
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("进行播种匹配，波次Id：%s，商家编码：%s，大图锁单：%s", waveId, param.getOuterId(), param.getLockSid())));
        }
        Integer waveSeedType = waveConfig.getWaveSeedType();
        if (Integer.valueOf(1).equals(waveSeedType)) {
            param.setPrintStatus(-1);
        }
        start = System.currentTimeMillis();
        // 大图锁定模式，如果锁定的订单已被提出波次，解除锁定
        handleLockSid(staff, waveId, param);
        t3 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        assembleShipperCondition(staff,param);
        WavePickingScanResult result = lockService.lock(getSeedLock(staff, waveId, param.getOuterId()), () -> {
            Wave wave = checkWaveBeforeSeed(staff, waveId);
            param.setWaveRuleId(wave.getRuleId());
            param.setWarehouseId(wave.getWarehouseId());

            WavePicking wavePicking = wavePickingDao.getByWaveId(staff, waveId);
            Assert.notNull(wavePicking, String.format("根据波次号[%s]找不到拣选记录!", waveId));
            param.setPickingCode(wavePicking.getPickingCode());
            Boolean multi = BooleanUtils.isTrue(param.getOpenBoxCodeScan()) || item.isSuite();

            WaveUniqueCode waveUniqueCode = handleSeedUniqueCode(staff, param, wavePicking, item);
            seeding(staff, wave, wavePicking);
            WavePickingScanResult data = seed(staff, param, multi, tradeConfig, waveUniqueCode);
            data.setRuleName(wave.getRuleName());
            return data;
        });
        t4 = System.currentTimeMillis() - start;

        result.setItem(item);
        start = System.currentTimeMillis();
        if (BooleanUtils.isTrue(param.getShowTradeInfo())) {
            setTradeInfo(staff, result, param.getMultiWaves(), param);
        }
        t5 = System.currentTimeMillis() - start;
        if (logger.isInfoEnabled() && (staff.getCompanyId() == 90223L || staff.getCompanyId() == 257636L || staff.getCompanyId() == 11920L)) {
            logger.info(LogHelper.buildLog(staff, "seedByOuterId耗时：t1 = " + t1 + ", t2 = " + t2 + ", t3 = " + t3 + ", t4 = " + t4 + ", t5 = " + t5));
        }
        return result;
    }

    public void assembleShipperCondition(Staff staff,WavePickingParam param){
        if(CompanyUtils.openMultiShipper(staff)&&param.getShipperSpeSysItemId()!=null){
            //设置货主
            //设置货主对应的userId
            DmjItem item= itemService.queryItemWithSysItemId(staff, param.getShipperSpeSysItemId());
            if(item!=null){
                String shipperId = item.getShipperId();
                if(StringUtils.isNotEmpty(shipperId)){
                    param.setShipperId(shipperId);
                }else {
                    param.setShipperId(WaveCommonConstants.DEFAULT_SHIPPER_ID);
                }
                Map<String, List<Long>> userIdMap = waveHelpBusiness.queryUserByShipperIds(staff, Lists.newArrayList(param.getShipperId()));
                param.setUserIds(userIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
            }
        }
    }

    private void handleLockSid(Staff staff, Long waveId, WavePickingParam param) {
        if (!DataUtils.checkLongNotEmpty(param.getLockSid()) || !DataUtils.checkLongNotEmpty(waveId)) {
            return;
        }

        WaveTrade waveTrade = waveTradeDao.queryByWaveIdAndSid(staff, waveId, param.getLockSid());
        if (waveTrade != null && Objects.equal(waveTrade.getTradeWaveStatus(), 2)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "大图锁定订单：" + param.getLockSid() + " 已踢出波次，解除锁定！"));
            }

            param.setLockSid(null);
        }
    }

    public void setTradeInfo(Staff staff, WavePickingScanResult result, Boolean multiWaves, WavePickingParam param) {
        long t1, t2, t3;
        long start = System.currentTimeMillis();
        Trade trade = result.getTrade();
        if (trade == null) {
            trade = waveSortingService.getTradeCache(staff, result.getSorting().getSid(), param, true);
            result.setTrade(trade);
        }
        t1 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        if (trade != null) {
            dealTradeInfoIfSimpleStyle(staff, trade, param.isOpenSeedSimpleStyle());
            WaveUtils.calculateWaveTradeItemCount(Lists.newArrayList(trade));
            trade.setExceptions(TradeExceptionUtils.analyze(staff,trade));
            Optional.ofNullable(result.getSorting()).ifPresent(sorting -> sorting.setShopName(WaveUtils.getShopName(shopService.queryByUserId(staff, result.getTrade().getUserId()))));
            try {
                trade = tradeServiceDubbo.fillTradeTagName(staff, trade);
            } catch (TradeFilterException e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "标签过滤处理失败！"), e);
            }
        }
        t2 = System.currentTimeMillis() - start;
        result.setTrade(trade);

        start = System.currentTimeMillis();
        if (BooleanUtils.isTrue(multiWaves)) {
            Map<Long, Trade> sidTradeMap = Maps.newHashMap();
            sidTradeMap.put(trade.getSid(), trade);
            fillWaveSortingTradeInfo(staff, Collections.singletonList(result.getSorting()), sidTradeMap);
        }
        t3 = System.currentTimeMillis() - start;

        if (logger.isInfoEnabled() && (staff.getCompanyId() == 90223L || staff.getCompanyId() == 257636L || staff.getCompanyId() == 11920L)) {
            logger.info(LogHelper.buildLog(staff, "setTradeInfo耗时：t1 = " + t1 + ", t2 = " + t2 + ", t3 = " + t3));
        }
    }

    /**
     * 播种打印简洁模式需要处理的订单信息
     * @param staff
     * @param trade
     * @param simpleStyle
     */
    private void dealTradeInfoIfSimpleStyle(Staff staff, Trade trade, boolean simpleStyle) {
        if (!simpleStyle) {
            return;
        }
        fillMergeTradeInfo(staff, Lists.newArrayList(trade)); // 处理合单订单信息
        boolean hasPrintTemplateIntegrate = featureService.checkHasFeature(staff.getCompanyId(), Feature.PRINT_TEMPLATE_INTEGRATE);
        if (hasPrintTemplateIntegrate) { // 填充快递公司
            tradeWaveService.fillTradeLogisticsCompanyName(staff, Lists.newArrayList(trade));
        } else { // 填充快递模版
            tradeWaveService.fillTradeTemplateName(staff, Lists.newArrayList(trade));
        }
    }

    @Override
    @Transactional
    public void seedByDirect(Staff staff, WavePickingParam param) {
        Long waveId = param.getWaveId();
        Assert.notNull(waveId, "请选择波次！");
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("直接进行播种匹配，波次Id：%s", waveId)));
        }
        Integer openSeed = staff.getConf().getOpenSeed();
        //请先开启多品多件启用播种！
        if (openSeed == null || openSeed != 1) {
            return;
        }
        Wave wave = tradeWaveService.queryWaveById(staff, waveId);
        if (wave == null) {
            return;
        }
        if (Wave.STATUS_FINISHED == wave.getStatus() || WaveDistributionStatus.EXAMINING.getValue().equals(wave.getDistributionStatus())) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("直接进行播种匹配，该波次[%s]已完成或者验货中！", waveId)));
            }
            return;
        }

        checkAssignSorter(staff, wave);
        Assert.isTrue(WaveDistributionStatus.PICKED.getValue().equals(wave.getDistributionStatus())
                || WaveDistributionStatus.WAIT_SEED.getValue().equals(wave.getDistributionStatus())
                || WaveDistributionStatus.WAIT_EXAMINE.getValue().equals(wave.getDistributionStatus()), "该波次未拣选完成，无法直接播种！");

        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        lockService.lock(getSeedLock(staff, waveId, waveId.toString()), () -> {
            WavePicking wavePicking = wavePickingDao.getByWaveId(staff, waveId);
            Assert.notNull(wavePicking, String.format("根据波次号[%s]找不到拣选记录!", waveId));
            seeding(staff, wave, wavePicking);
            seed(staff, waveId, wavePicking, tradeConfig);
            return null;
        });
    }

    public WavePickingScanResult seed(Staff staff, WavePickingParam param, boolean multi, TradeConfig tradeConfig, WaveUniqueCode waveUniqueCode) {
        return seed(staff, param, multi, tradeConfig, waveUniqueCode, null);
    }

    public WavePickingScanResult seed(Staff staff, WavePickingParam param, boolean multi, TradeConfig tradeConfig, WaveUniqueCode waveUniqueCode, WaveSorting waveSorting) {
        long t1, t2, t3 = 0, t4 = 0, t5 = 0, t6 = 0, t7 = 0, t8 = 0;
        long start = System.currentTimeMillis();
        final Long waveId = param.getWaveId();
        // 混合拣选号轮播waveSorting不为空
        if (waveSorting == null) {
            waveSorting = multi ? waveSortingService.matchMultiByOuterCodes(staff, param, waveUniqueCode) : waveSortingService.matchOneByOuterId(staff, param, waveUniqueCode);
        }
        t1 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        if (waveSorting != null) {
            List<WaveSeedStat> waveSeedStatList = waveSeedLogDao.statSortingSeedInfo(staff, null, Lists.newArrayList(waveSorting.getId()));
            if (CollectionUtils.isNotEmpty(waveSeedStatList)) {
                waveSorting.setSeedEndTime(waveSeedStatList.get(0).getSeedEndTime());
            }
        }
        t2 = System.currentTimeMillis() - start;

        waveSorting.setSeedEndTime(new Date());
        WavePickingScanResult result = buildSeedResult(staff, waveSorting);
        if (param.isOpenSeedSimpleStyle()) {
            fillUnderstockedGroup(staff, result, param.isAllReceive());
            if(param.getBindUniqueCode() != null) { // 绑定的唯一码返回给前端
                result.setUniqueCode(param.getBindUniqueCode().getUniqueCode());
            }
        }
        if (BooleanUtils.isTrue(result.getTradeMatched())) {
            start = System.currentTimeMillis();
            Trade trade = waveSortingService.getTradeCache(staff, waveSorting.getSid(), param, true);
            Assert.notNull(trade, String.format("订单[%s]不存在!", waveSorting.getSid()));
            result.setTrade(trade);
            result.setTradeMatched(true);
            t3 = System.currentTimeMillis() - start;

            start = System.currentTimeMillis();
            Trade tradeForTrace = createTradeByWaveSortingForTrace(staff, waveSorting);
            t4 = System.currentTimeMillis() - start;

            start = System.currentTimeMillis();
            //pda波次播种 自动打印
            if (WavePickingParam.SEED_SOURCE_PDA.equals(param.getSeedSource())) {
                fillCheckContentBySeedLog(staff, tradeForTrace);
            }
            t5 = System.currentTimeMillis() - start;

            start = System.currentTimeMillis();
            eventCenter.fireEvent(this, new EventInfo("trade.wave.seed.over").setArgs(new Object[]{staff}), Lists.newArrayList(tradeForTrace));
            waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.WAVE_SEED_TRADE, ("播种完成：订单号：" + waveSorting.getSid() + "; 内部单号:" + trade.getShortId())));
            WaveConfig waveConfig = waveConfigService.get(staff);
            t6 = System.currentTimeMillis() - start;

            start = System.currentTimeMillis();
            Integer count = waveSortingService.queryNotMatchedOverCount(staff, param.getPickingId());
            t7 = System.currentTimeMillis() - start;

            if (count == null || count == 0) {
                start = System.currentTimeMillis();
                handleSeedOver(staff, waveId, param.getPickingId(), tradeConfig, waveConfig);
                result.setTradeAllMatched(true);
                waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.WAVE_SEED_ALL, "全部播种完成"));
                t8 = System.currentTimeMillis() - start;
            }
        }

        if (logger.isInfoEnabled() && (staff.getCompanyId() == 90223L || staff.getCompanyId() == 257636L || staff.getCompanyId() == 11920L)) {
            logger.info(LogHelper.buildLog(staff, "seed耗时：t1 = " + t1 + ", t2 = " + t2 + ", t3 = " + t3 + ", t4 = " + t4 + ", t5 = " + t5 + ", t6 = " + t6 + ", t7 = " + t7 + ", t8 = " + t8));
        }

        result.setPickingCode(param.getPickingCode());
        result.setPickingId(param.getPickingId());
        result.setWaveId(param.getWaveId());
        result.setMatchedItemNum(param.getTotalNum());
        return result;
    }

    /**
     * 填充缺货分组
     * @param result
     * @param isAllReceive
     */
    private void fillUnderstockedGroup(Staff staff, WavePickingScanResult result, Boolean isAllReceive) {
        if (isAllReceive != null && !isAllReceive) {
            result.setUnderstockedGroup("未配齐");
        } else {
            Wave wave = waveDao.queryById(staff, result.getSorting().getWaveId());
            result.setUnderstockedGroup(wave.getUnderstockedGroup());
        }
    }

    @Override
    public void fillCheckContentBySeedLog(Staff staff, Trade trade) {
        if (trade.getSid() == null || trade.getWaveId() == null) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("播种登记质检未记录订单号或波次号，sid: %s, waveId: %s", trade.getSid(), trade.getWaveId())));
            }
            return;
        }
        //播种打印质检登记
        List<WaveSeedLog> waveSeedLogs = waveSeedLogDao.queryWaveSeedLogWithSortingDetailList(staff, Collections.singletonList(trade.getSid()), trade.getWaveId());
        //该订单所有播种记录质检人信息为空则不处理
        int checkerNotBlankNum = waveSeedLogs.stream().filter(log -> StringUtils.isNotBlank(log.getChecker())).collect(Collectors.toList()).size();
        if (checkerNotBlankNum > 0) {
            StringBuilder seedCheckContent = new StringBuilder("【质检登记】");
            //根据商家编码进行分组
            Map<String, List<WaveSeedLog>> outerIdSeedLogMap = waveSeedLogs.stream().collect(Collectors.groupingBy(WaveSeedLog::getOuterId));
            Set<Map.Entry<String, List<WaveSeedLog>>> entries = outerIdSeedLogMap.entrySet();
            for (Map.Entry<String, List<WaveSeedLog>> entry : entries) {
                String outerId = entry.getKey();
                List<WaveSeedLog> seedLogs = entry.getValue();
                Integer itemNumCount = seedLogs.stream().collect(Collectors.summingInt(WaveSeedLog::getItemNum));
                //过滤掉质检人为空的记录，根据质检人分组统计
                Map<String, Integer> checkerCountMap = seedLogs.stream().filter(log -> StringUtils.isNotBlank(log.getChecker()))
                        .collect(Collectors.groupingBy(WaveSeedLog::getChecker, Collectors.summingInt(WaveSeedLog::getItemNum)));
                if (checkerCountMap.size() > 0) {
                    seedCheckContent.append(String.format("商品：%s*%s", outerId, itemNumCount)).append("，质检员：");
                    for (Map.Entry<String, Integer> checkerCountEntry : checkerCountMap.entrySet()) {
                        seedCheckContent.append(String.format("%s*%s", checkerCountEntry.getKey(), checkerCountEntry.getValue())).append("、");
                    }
                    seedCheckContent.deleteCharAt(seedCheckContent.length() - 1);
                    seedCheckContent.append("；");
                }
            }
            trade.setCheckContent(seedCheckContent.toString());
        } else {
            logger.debug(LogHelper.buildLog(staff, String.format("所有播种日志都未记录质检员，sid：%s", trade.getSid())));
        }
    }

    private void handleSeedOver(Staff staff, Long waveId, Long pickingId, TradeConfig tradeConfig, WaveConfig waveConfig) {
        Wave updateWave = new Wave();
        updateWave.setId(waveId);
        //1、播种完成未打印-》等待验货 2、需要播种包装验货-》等待验货 3、不要包装验货-》验货完成
        int notPrintCount = waveSortingDao.queryNotPrintOverCount(staff, pickingId);
        Wave wave = waveDao.queryById(staff, waveId);
        if (WavePickUtils.isNeedSeed4NoTradeWave(wave)) {
            updateWave.setDistributionStatus(WaveDistributionStatus.EXAMINED.getValue());
            updateWave.setStatus(Wave.STATUS_FINISHED);
            updateWave.setFinished(new Date());
        } else if (notPrintCount > 0 || WaveUtils.needWaveExamine(tradeConfig, waveConfig)) {
            updateWave.setDistributionStatus(WaveDistributionStatus.WAIT_EXAMINE.getValue());
        } else {
            updateWave.setDistributionStatus(WaveDistributionStatus.EXAMINED.getValue());
            updateWave.setStatus(Wave.STATUS_FINISHED);
            updateWave.setFinished(new Date());
        }
        if (wave != null && wave.getSeedEndTime() == null) {
            updateWave.setSeedEndTime(new Date());
        }
        waveDao.update(staff, updateWave);
        if (Objects.equal(updateWave.getStatus(), Wave.STATUS_FINISHED)) {
            wave.setStatus(Wave.STATUS_FINISHED);
            eventCenter.fireEvent(this, new EventInfo("wave.finish.auto").setArgs(new Object[]{staff, Lists.newArrayList(wave)}), null);
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("播种完成，修改配货状态:%s，波次id：%s", updateWave.getDistributionStatus(), waveId)));
        }
    }

    private void seed(Staff staff, Long waveId, WavePicking wavePicking, TradeConfig tradeConfig) {
        Long picking = wavePicking.getId();
        List<WaveSorting> waveSortings = waveSortingService.matchAllByWaveId(staff, picking);
        WaveConfig waveConfig = waveConfigService.get(staff);
        List<WaveSorting> seeded = waveSortings.stream().filter(waveSorting -> waveSorting.getMatchedStatus() == WaveSorting.MATCHED_STATUS_OVER).collect(Collectors.toList());
        if (!seeded.isEmpty()) {
            eventCenter.fireEvent(this, new EventInfo("trade.wave.seed.over").setArgs(new Object[]{staff}), seeded.stream().map(waveSorting -> createTradeByWaveSortingForTrace(staff, waveSorting)).collect(Collectors.toList()));
        }

        if (waveSortings.size() == seeded.size()) {
            handleSeedOver(staff, waveId, picking, tradeConfig, waveConfig);
        } else {
            Wave update = new Wave();
            update.setId(waveId);
            update.setDistributionStatus(WaveDistributionStatus.SEEDING.getValue());
            waveDao.update(staff, update);

            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("直接播种波次订单，修改配货状态:%s，波次id：%s", WaveDistributionStatus.SEEDING.getValue(), waveId)));
            }
        }
        // 多件波次订单自动包装验货
        eventCenter.fireEvent(this, new EventInfo("trade.wave.auto.package").setArgs(new Object[]{staff, waveId, false, false}), null);
        //日志区分
        WaveTraceOperateEnum waveSeedEnum = WaveTraceOperateEnum.WAVE_SEED_DIRECT;
        if (wavePicking.getPickingWay() != null && wavePicking.getPickingWay() == 2) {
            waveSeedEnum = WaveTraceOperateEnum.WAVE_SEED_DIRECT_TASK_LIST;
        }
        waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, waveId, waveSeedEnum, waveSeedEnum.getOperate()));
    }

    protected void checkSeedParam(Staff staff, WaveConfig waveConfig, WavePickingParam param) {
        Assert.notNull(param.getWaveId(), "请选择波次！");
        Assert.notNull(param.getPickingId(), "拣选id不能为空！");
        Assert.hasText(param.getOuterId(), "商家编码不能为空！");

        String outerId = param.getOuterId();
        param.setScanCode(outerId); // 扫描的编码
        if (Boolean.TRUE.equals(param.getMultiWaves())) {
            WaveUtils.MultiWavesSeedKey currentSeedKey = WaveUtils.splitMultiWavesBarcode(outerId, waveConfig);
            Assert.notNull(currentSeedKey, String.format("多波次打印扫描的条码数据不符合格式，条码数据为：%s", WaveUtils.formatMultiWaveSeedBarcode(outerId)));

            waveHelpBusiness.fillMultiWaveSeedKey(staff, currentSeedKey);
            WaveUtils.setCurrentPickingParam(param, currentSeedKey);
            param.setOuterId(currentSeedKey.getOuterId());
        } else if (Objects.equal(waveConfig.getSeedScanType(), 1)) {
            WaveUtils.MultiWavesSeedKey currentSeedKey = WaveUtils.splitWaveItemPositionBarcode(outerId);
            Assert.notNull(currentSeedKey, "播种开启扫描商家编码+位置号，扫描的条码数据不符合格式，条码数据为：" + WaveUtils.formatMultiWaveSeedBarcode(outerId));

            param.setOuterId(currentSeedKey.getOuterId());
            param.setPositionNo(currentSeedKey.getPositionNo());
        }
    }

    @Override
    @Cacheable(value = "defaultCache#600", key = "'wave_multi_code_cache_' + #staff.companyId")
    public boolean checkHasFeature(Staff staff, Feature feature) {
        CheckHasFeatureRequest request = new CheckHasFeatureRequest();
        request.setCompanyId(staff.getCompanyId());
        request.setFeature(feature);
        CheckHasFeatureResponse response = indexDubboService.checkHasFeature(request);
        Assert.isTrue(response.isSuccess(), "查询一品多码失败:" + response.getErrorMsg());
        return response.isHasFeature();
    }

    public DmjItem handleSeedOuterId(Staff staff, WavePickingParam param) {
        long t1, t2;
        long start = System.currentTimeMillis();
        param.setShowDetail(true);
        param.setSupportMultiCode(1);
        param.setTotalNum(1);
        DmjItem item = scanItem(staff, param);
        t1 = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        // pda播种返回批次信息
        fillBatchInfoFromPda(staff, param, item);
        t2 = System.currentTimeMillis() - start;

        if (logger.isInfoEnabled() && (staff.getCompanyId() == 90223L || staff.getCompanyId() == 257636L || staff.getCompanyId() == 11920L)) {
            logger.info(LogHelper.buildLog(staff, "handleSeedOuterId耗时：t1 = " + t1 + ", t2 = " + t2));
        }

        Assert.notNull(item, String.format("商品%s不存在！", param.getOuterId()));
        if (!item.isSuite()) {
            return item;
        }

        List<SuiteSingle> suits = item.getSuiteSingleList();
        Assert.notEmpty(suits, String.format("商品%s套件明细不存在！", item.getOuterId()));
        List<WaveSortingDetail> details = Lists.newArrayListWithCapacity(suits.size());
        int totalNum = 0;
        for (SuiteSingle suit : suits) {
            WaveSortingDetail detail = new WaveSortingDetail();
            detail.setOuterId(StringUtils.isNotEmpty(suit.getSkuOuterId()) ? suit.getSkuOuterId() : suit.getOuterId());
            detail.setItemNum(suit.getRatio());
            totalNum += detail.getItemNum();
            details.add(detail);
        }
        param.setTotalNum(totalNum);
        param.setDetails(details);

        return item;
    }

    /**
     * pda播种返回批次信息
     */
    private void fillBatchInfoFromPda(Staff staff, WavePickingParam param, DmjItem item) {
        if (!Objects.equal(param.getSeedSource(), WavePickingParam.SEED_SOURCE_PDA) || !WmsUtils.isFillBatchNoProductDate(item)) {
            return;
        }
        List<AllocateGoodsRecord> allocateGoodsRecords = wmsService.queryAllocateGoodsRecords(staff, new QueryAllocateGoodsRecordParams.Builder()
                .sysItemId(item.getSysItemId())
                .sysSkuId(item.getSysItemId())
                .waveId(param.getWaveId())
                .build());
        // 产品回馈取第一条
        if (Objects.equal(item.getHasBatch(), CommonConstants.JUDGE_YES)) {
            item.setProductDateInfo(WmsUtils.getProductDateByAllocateRecord(allocateGoodsRecords, true));
        }
        if (Objects.equal(item.getHasProduct(), CommonConstants.JUDGE_YES)) {
            item.setProductDateInfo(WmsUtils.getBatchNoByAllocateRecord(allocateGoodsRecords, true));
        }
    }

    private WaveUniqueCode handleSeedUniqueCode(Staff staff, WavePickingParam param, WavePicking wavePicking, DmjItem item) {
        //处理唯一码
        if (StringUtils.isNotEmpty(param.getUniqueCode())) {
            WaveUniqueCode waveUniqueCode = waveUniqueCodeService.queryByUniqueCode(staff, param.getUniqueCode());
            Assert.notNull(waveUniqueCode, "该唯一码不存在");
            Assert.isTrue(WaveSorting.MATCHED_STATUS_NOT == waveUniqueCode.getMatchedStatus(), "该唯一码已使用");
//            Assert.isTrue(CommonConstants.JUDGE_NO == waveUniqueCode.getReceiveStatus(), "该唯一码已扫描");
            Assert.isTrue(wavePicking.getPickingCode().equals(param.getPickingCode()), "该唯一码和拣选号不对应！");
            Assert.isTrue(waveUniqueCode.getWaveId().equals(wavePicking.getWaveId()), "该唯一码与波次不对应！");
            Assert.isTrue(waveUniqueCode.getSysItemId().equals(item.getSysItemId()), "该唯一码与商品不对应！");
            if (item instanceof DmjSku && ((DmjSku) item).getSysSkuId() > 0L) {
                Assert.isTrue(waveUniqueCode.getSysSkuId().equals(((DmjSku) item).getSysSkuId()), "该唯一码与商品规格不对应！");
            }

            param.setOrderId(waveUniqueCode.getOrderId());
            return waveUniqueCode;
        }

        return null;
    }

    protected ERPLock getSeedLock(Staff staff, Long waveId, String outerId) {
        return wavePositionService.openLoopWave(staff, waveId) ?
                ERPLockUtils.createERPLock(staff.getDbNo(), staff.getCompanyId(), "wave_seed_" + staff.getCompanyId() + "_" + waveId)
                : ERPLockUtils.createERPLock(staff.getDbNo(), staff.getCompanyId(), "wave_seed_" + staff.getCompanyId() + "_" + waveId + "_" + outerId);
    }

    protected WavePickingScanResult buildSeedResult(Staff staff, WaveSorting waveSorting) {
        WavePickingScanResult result = new WavePickingScanResult();
        result.setTradeMatched(WaveSorting.MATCHED_STATUS_OVER == waveSorting.getMatchedStatus());
        waveSorting.setPositionCode(WaveUtils.generatePositionCode(staff, waveSorting.getPositionNo().longValue()));
        result.setSorting(waveSorting);
        result.setSid(waveSorting.getSid());
        result.setPositionNo(waveSorting.getPositionNo());
        result.setPositionCode(waveSorting.getPositionCode());
        return result;
    }

    private Trade createTradeByWaveSortingForTrace(Staff staff, WaveSorting sorting) {
        Trade trade = new Trade();
        trade.setSid(sorting.getSid());
        trade.setCompanyId(staff.getCompanyId());
        trade.setWaveId(sorting.getWaveId());
        trade.setTaobaoId(0L);
        return trade;
    }

    @Override
    public boolean handleExceptionTrade(Staff staff, Trade trade, Long pickingId, Long sortingId) {
        if (Objects.equal(trade.getIsExcep(), CommonConstants.JUDGE_YES)) {
            WaveSorting temp = new WaveSorting();
            temp.setPickingId(pickingId);
            temp.setId(sortingId);
            temp.setDelayStatus(WaveSorting.DELAY_STATUS_EXCEP);
            temp.setPostStatus(WaveSorting.POST_STATUS_NOT);
            // add by 决明 2019-05-12 pickingId为0,表示盲扫批次，不能保存到waveSorting中
            if (pickingId > 0) {
                waveSortingDao.updateStatus(staff, temp);
                logger.debug(LogHelper.buildLog(staff, String.format("%s的订单系统状态为异常不能打印", trade.getSid())));
            }
            return true;
        }
        return false;
    }

    /**
     * 处理后置异常订单
     *
     * @param staff
     * @param trades
     */
    @Override
    public List<WavePickingScanResult.ErrorItem> handleExceptionTrades(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return Collections.emptyList();
        }
        if (WmsUtils.isOpenWms(staff)) {
            //开启仓储版本的缺货订单后置打印自动取消缺货
            List<Trade> insufficientTrades = Lists.newArrayListWithCapacity(trades.size());
            List<Long> insufficientSids = Lists.newArrayList();
            for (Trade trade : trades) {
                if (Objects.equal(trade.getIsExcep(), CommonConstants.JUDGE_YES) && Trade.STOCK_STATUS_INSUFFICIENT.equals(trade.getStockStatus())) {
                    insufficientSids.add(trade.getSid());
                    insufficientTrades.add(trade);
                }
            }

            if (!insufficientSids.isEmpty()) {
                logger.debug(LogHelper.buildLog(staff, String.format("仓储版本，后置打印缺货订单[%s]自动取消缺货", insufficientSids)));
                try {
                    Map<String, String> errMap = tradeServiceDubbo.cancelInsufficient(staff, null, insufficientSids.toArray(new Long[0]));
                    if (errMap != null && !errMap.isEmpty()) {
                        logger.warn(LogHelper.buildLog(staff, String.format("取消缺货异常存在异常返回，订单号：%s，错误map：%s", insufficientSids, errMap)));
                    }
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "取消缺货异常失败！"), e);
                }

                //重新查询取消缺货的订单，并放到otherTrades中进行后面的异常计算
                trades.removeAll(insufficientTrades);
                trades.addAll(waveUseTradeServiceProxy.queryBySids(staff, true, insufficientSids.toArray(new Long[0])));
            }
        }

        //如果订单是异常单，重置后置打印匹配的状态
        List<WavePickingScanResult.ErrorItem> errorItems = Lists.newArrayList();
        List<Trade> excepTrades = Lists.newArrayList();
        for (Trade trade : trades) {
            if (Objects.equal(trade.getIsExcep(), CommonConstants.JUDGE_YES)) {
                excepTrades.add(trade);
                errorItems.add(new WavePickingScanResult.ErrorItem(trade.getSid(), "异常订单！"));
            } else if (Objects.equal(trade.getTemplateType(), 0) && !CommonConstants.PLAT_FORM_TYPE_POISON.equals(trade.getSource()) ) {
                errorItems.add(new WavePickingScanResult.ErrorItem(trade.getSid(), "普通面单！"));
            }
        }

        if (!excepTrades.isEmpty()) {
            updateDelayStatus(staff, excepTrades);
        }

        if (!errorItems.isEmpty()) {
            logger.debug(LogHelper.buildLog(staff, "后置打印处理异常订单，" + errorItems));
        }
        return errorItems;
    }

    private void updateDelayStatus(Staff staff, List<Trade> trades) {
        List<WaveSorting> updates = trades.stream().map(trade -> {
            WaveSorting update = new WaveSorting();
            update.setSid(trade.getSid());
            update.setDelayStatus(WaveSorting.DELAY_STATUS_EXCEP);
            update.setPostStatus(WaveSorting.POST_STATUS_NOT);
            return update;
        }).collect(Collectors.toList());

        waveSortingDao.batchUpdateStatus(staff, updates);
    }

    @Override
    @Deprecated
    public void cancelPostPrintStatus(Staff staff, List<WaveSorting> sortings) {
        if (CollectionUtils.isEmpty(sortings)) {
            return;
        }
        throw new IllegalArgumentException("取消失败，请重新尝试!");
    }

    @Override
    public void cancelPostPrint(Staff staff, WavePickingParam param, List<WaveSorting> sortings) {
        sortings = CollectionUtils.isEmpty(sortings) ? TradePostPrintContextUtils.getWithInit().getWaveSortings() : sortings;
        if (CollectionUtils.isEmpty(sortings)) {
            return;
        }
        efficientWaveHandler.cancelPostPrint(staff, param, sortings);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(staff).append("波次[").append(param.getPickingCode()).append("]取消后置打印，取消详情:")
                    .append(sortings.stream().map(s -> "{id:" + s.getId() + ",sid:" + s.getSid() + "}").collect(Collectors.joining(","))));
        }

        WaveConfig waveConfig = waveConfigService.get(staff);
        if (Objects.equal(waveConfig.getOpenWaveUniqueCode(), 1)) {
            waveUniqueCodeDao.batchUpdate(staff, sortings.stream().filter(sorting -> CollectionUtils.isNotEmpty(sorting.getUniqueCodes()))
                    .flatMap(sorting -> sorting.getUniqueCodes().stream())
                    .map(uniqueCode -> {
                        WaveUniqueCode update = new WaveUniqueCode();
                        update.setId(uniqueCode.getId());
                        update.setPostStatus(WaveSorting.POST_STATUS_NOT);
                        return update;
                    }).collect(Collectors.toList()));
            ;
        }
    }

    /**
     * 后置打印回调
     * @param staff
     * @param paramJson 后置打印参数
     * @param successSids 成功sid
     * @param errorSids 失败sid
     */
    @Override
    public void postPrintCallback(Staff staff, String paramJson, List<Long> successSids, List<Long> errorSids) {
        WavePickingParam param = JSONObject.parseObject(paramJson, WavePickingParam.class);
        successSids = Optional.ofNullable(successSids).orElse(Lists.newArrayList());
        errorSids = Optional.ofNullable(errorSids).orElse(Lists.newArrayList());
        logger.debug(LogHelper.buildLog(staff, String.format("后置打印批量打印回调, successSids:%s, errorSids:%s", successSids, errorSids)));
        if (CollectionUtils.isNotEmpty(successSids)) {
            List<Trade> successTrades = waveUseTradeServiceProxy.queryBySids(staff, false, successSids.toArray(new Long[0]));
            List<WaveSorting> waveSortings = (param.getWaveId() > 0 || Objects.equal(param.getWaveId(), -2L)) ?
                    waveSortingDao.queryBySids(staff, successSids) :
                    successSids.stream().map(sid -> {
                        WaveSorting waveSorting = new WaveSorting();
                        waveSorting.setSid(sid);
                        return waveSorting;
                    }).collect(Collectors.toList());
            Set<Integer> postionNos = waveSortings.stream().map(WaveSorting::getPositionNo).collect(Collectors.toSet());
            waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, param.getWaveId(), WaveTraceOperateEnum.PRINT_POST_BATCH, ("后置连打出单：位置号：" + postionNos)));
            fastInOutConsignAfter(staff, param, successTrades);
        }
        if (CollectionUtils.isNotEmpty(errorSids)) {
            List<WaveSorting> waveSortings = param.getWaveId() > 0 ?
                    waveSortingDao.queryBySids(staff, errorSids) :
                    errorSids.stream().map(sid -> {
                        WaveSorting waveSorting = new WaveSorting();
                        waveSorting.setSid(sid);
                        return waveSorting;
                    }).collect(Collectors.toList());
            eventCenter.fireEvent(this, new EventInfo("wave.trace.add").setArgs(new Object[]{staff, Collections.singletonList(param.getWaveId()), WaveTraceOperateEnum.PRINT_POST_BATCH, ("后置连打出单失败"), 1}), null);
            cancelPostPrint(staff, param, waveSortings);
        }

    }

    @Override
    public void fastInOutConsignAfter(Staff staff, WavePickingParam param, List<Trade> trades) {
        if (param == null || param.getMoveStockType() == null || (!Objects.equal(WorkingStorageSection.MoveStockEnum.PURCHASE.getType(), param.getMoveStockType()) && !Objects.equal(WorkingStorageSection.MoveStockEnum.REFUND.getType(), param.getMoveStockType()))) {
            return;
        }

        if (!FastInOutStockUtils.isOpenFastInOut(wmsService.getConfig(staff))) {
            return;
        }
        List<Long> sids = trades.stream().map(Trade::getSid).collect(Collectors.toList());
        wmsService.consignAfter(staff, sids, trades.get(0).getWarehouseId(), Objects.equal(WorkingStorageSection.MoveStockEnum.PURCHASE.getType(), param.getMoveStockType()) ? FastInOutStockUtils.FAST_IN_OUT_PURCHASE_WAVE_ID : FastInOutStockUtils.FAST_IN_OUT_REFUND_WAVE_ID);
    }

    protected Wave checkWaveBeforeSeed(Staff staff, Long waveId) {
        return checkWaveBeforeSeed(staff, waveId, true);
    }

    /**
     * 播种操作前检查波次状态
     *
     * @param waveId 波次Id
     */
    private Wave checkWaveBeforeSeed(Staff staff, Long waveId, boolean checkSorter) {
        Integer openSeed = staff.getConf().getOpenSeed();
        Assert.isTrue(openSeed != null && openSeed == 1, "请先开启多品多件启用播种！");

        Wave wave = tradeWaveService.queryWaveById(staff, waveId);
        Assert.notNull(wave, "该波次不存在！");

        Assert.isTrue(WavePickUtils.isTradeWave(wave) || WavePickUtils.isNeedSeed4NoTradeWave(wave), "非订单波次无法进行该操作！");

        if (Wave.STATUS_FINISHED == wave.getStatus()) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_WAVE_OVER, "该波次已完成！");
        }

        if (checkSorter) {
            checkAssignSorter(staff, wave);
        }
        return wave;
    }

    public void checkAssignSorter(Staff staff, Wave wave) {
        Long assignSorter = wave.getAssignSorter();
        if (assignSorter != null && assignSorter > 0L && !assignSorter.equals(staff.getId())) {
            throw new IllegalArgumentException(String.format("该波次已安排[%s]播种！", wave.getAssignSorterName()));
        }
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (waveConfig.getOpenTradeWaveSeedSorter() == null || waveConfig.getOpenTradeWaveSeedSorter() == 0) {
            return;
        }
        WavePicking wavePicking = wavePickingDao.getByWaveId(staff, wave.getId());
        if (wavePicking == null) {
            return;
        }
        Long sorterId = wavePicking.getSorterId();
        if (sorterId != null && sorterId > 0L && !sorterId.equals(staff.getId())) {
            throw new IllegalArgumentException("该播种任务已被[" + wavePicking.getSorterName() + "]领取，不能操作！");
        }
    }

    private void checkBeforeCancelSeeds(Staff staff, List<Long> waveIds) {
        checkBeforeCancelSeeds(staff, waveIds, true);
    }

    private void checkBeforeCancelSeeds(Staff staff, List<Long> waveIds, boolean checkSorter) {
        if (waveIds.size() == 1) {
            checkWaveBeforeSeed(staff, waveIds.get(0), checkSorter);
            return;
        }

        Integer openSeed = staff.getConf().getOpenSeed();
        Assert.isTrue(openSeed != null && openSeed == 1, "请先开启多品多件启用播种！");
        // 1. 验证波次是否存在
        List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
        Assert.notEmpty(waves, "通过波次Ids未查询到波次！");
        // 2. 验证是否是订单波次
        List<Wave> unTradeWaves = waves.stream().filter(w -> !WavePickUtils.isTradeWave(w)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unTradeWaves)) {
            throw new IllegalArgumentException("该波次： " + unTradeWaves.get(0).getId() + "不是订单波次!");
        }
        // 3. 验证波次是否完成
        List<Wave> finishedWaves = waves.stream().filter(w -> Wave.STATUS_FINISHED == w.getStatus()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(finishedWaves)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_WAVE_OVER, "该波次： " + finishedWaves.get(0) + "已完成！");
        }
        if (checkSorter) {
            // 4. 验证安排的播种员
            List<Wave> unAssignSorterWaves = waves.stream().filter(w -> w.getAssignSorter() != null && w.getAssignSorter() > 0L && !w.getAssignSorter().equals(staff.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(unAssignSorterWaves)) {
                throw new IllegalArgumentException("该波次" + unAssignSorterWaves.get(0) + "已安排" + unAssignSorterWaves.get(0).getAssignSorterName() + "播种！");
            }
            // 5. 验证领取的播种员
            WaveConfig waveConfig = waveConfigService.get(staff);
            if (waveConfig.getOpenTradeWaveSeedSorter() == null || waveConfig.getOpenTradeWaveSeedSorter() == 0) {
                return;
            }
            List<WavePicking> wavePickings = wavePickingDao.getByWaveIds(staff, waves.stream().map(Wave::getId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(wavePickings)) {
                return;
            }
            List<WavePicking> getPickings = wavePickings.stream().filter(p -> p.getSorterId() != null && p.getSorterId() > 0L && !p.getSorterId().equals(staff.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(getPickings)) {
                throw new IllegalArgumentException("该播种任务已被[" + getPickings.get(0).getSorterName() + "]领取，不能操作！");
            }
        }
    }

    @Override
    @Transactional
    public void cancelSeeds(Staff staff, List<Long> waveIds, List<Long> pickingIds) {
        cancelSeeds(staff, waveIds, pickingIds, true);
    }

    @Override
    @Transactional
    public void cancelSeeds(Staff staff, List<Long> waveIds, List<Long> pickingIds, boolean checkSorter) {
        checkBeforeCancelSeeds(staff, waveIds, checkSorter);
        List<WavePicking> wavePickings = Lists.newArrayList();
        final Integer printedStatus;
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (Integer.valueOf(1).equals(waveConfig.getWaveSeedType())) {
            printedStatus = -1;
        } else {
            printedStatus = null;
        }
        pickingIds.forEach(p -> {
            waveSortingService.resetMatchedByPickingId(staff, p, printedStatus);
            WavePicking temp = new WavePicking();
            temp.setId(p);
            temp.setSorterId(0L);
            temp.setSorterName("");
            wavePickings.add(temp);
        });
        waveUniqueCodeService.resetMatchStatus(staff, waveIds);
        wavePickingDao.batchUpdate(staff, wavePickings);
        seedCancelUpdateWaveStatus(staff, waveIds);
        List<WaveSorting> sortings = waveSortingDao.queryByPickingIds(staff, pickingIds);
        List<String> releaseBindSids = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(sortings)) {
            List<Long> sortingIds = Lists.newArrayListWithCapacity(sortings.size());
            List<WaveTrade> updates = Lists.newArrayListWithCapacity(sortingIds.size());
            for (WaveSorting sorting : sortings) {
                if (sorting.getPrintStatus() != WaveSorting.PRINT_STATUS_NOT && Integer.valueOf(0).equals(waveConfig.getWaveSeedType())) {
                    continue;
                }
                sortingIds.add(sorting.getId());
                releaseBindSids.add(String.valueOf(DataUtils.getZeroIfDefault(sorting.getSid())));
                WaveTrade update = new WaveTrade();
                update.setWaveId(sorting.getWaveId());
                update.setSid(sorting.getSid());
                update.setMatchedStatus(WaveSorting.MATCHED_STATUS_NOT);
                updates.add(update);

            }
            waveSeedLogDao.deleteBySortingIds(staff, sortingIds);
            waveTradeDao.batchUpdate(staff, updates);
        }
        //重新播种商品日志输出
        try {
            if (CollectionUtils.isNotEmpty(sortings)) {
                sortings.forEach(waveSorting -> {
                    List<WaveSortingDetail> waveSortingDetails = waveSortingDao.queryDetailsBySortingId(staff, waveSorting.getId());
                    if (!CollectionUtils.isEmpty(waveSortingDetails)) {
                        WaveItemTraceLogHelper.batchRecodeItemTraceLogByWaveSortingDetail(itemTraceService, staff, waveSortingDetails,
                                ItemTraceActionEnum.WAVE_PC_SEED.getCode(), ItemTraceBillTypeEnum.WAVE.getCode(), String.valueOf(waveSorting.getWaveId()),
                                WaveItemTraceActionModulePathEnum.POST_PRINT_SEED_CANCEL.getName());
                    }
                });
            }
        } catch (Exception e) {
            logger.error("重新播种商品日志输出失败", e);
        }
        // 清空暂存区配货记录
        wmsService.deleteByAllocateGoodsByParams(staff, new QueryAllocateGoodsRecordParams.Builder().waveIds(waveIds).containerType(ContainerTypeEnum.WORKING_STORAGE_SECTION.getValue()).build());

        // 解绑唯一码
        if (CollectionUtils.isNotEmpty(releaseBindSids)) {
            ItemUniqueCodeQueryParams uniqueCodeParams = new ItemUniqueCodeQueryParams();
            uniqueCodeParams.setSids(releaseBindSids);
            List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, uniqueCodeParams);
            postPrintUniqueCodeBusiness.releaseBindUniqueCode(staff, waveUniqueCodes, 1);
        }

        logger.info(LogHelper.buildLog(staff, String.format("取消波次播种，重置播种人，播种日志，配货状态，波次号：%s", JSON.toJSONString(waveIds))));
    }

    /**
     * 取消播种更新波次状态
     * 1. 如果波次未拣选完成，更新状态为拣选中
     * 2. 如果波次已拣选完成，更新状态为等待播种
     *
     * @param staff
     * @param waveIds
     */
    private void seedCancelUpdateWaveStatus(Staff staff, List<Long> waveIds) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return;
        }
        List<WavePicking> pickings = wavePickingDao.getByWaveIds(staff, waveIds);
        List<Long> waitSeedIds = Lists.newArrayList();
        List<Long> pickingIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(pickings)) {
            logger.debug(LogHelper.buildLog(staff, String.format("取消播种未查询到波次拣选记录不处理，波次号：%s", waveIds)));
        } else {
            Map<Long, Integer> pickingMaps = pickings.stream().collect(Collectors.toMap(WavePicking::getWaveId, WavePicking::getEnableStatus, (a, b) -> a));
            for (Long waveId : waveIds) {
                Integer status = pickingMaps.get(waveId);
                if (status != null && status == 3) {
                    waitSeedIds.add(waveId);
                } else {
                    pickingIds.add(waveId);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(waitSeedIds)) {
            tradeWaveService.batchUpdateDistributionStatus(staff, waitSeedIds, WaveDistributionStatus.WAIT_SEED.getValue());
        }
        if (CollectionUtils.isNotEmpty(pickingIds)) {
            tradeWaveService.batchUpdateDistributionStatus(staff, pickingIds, WaveDistributionStatus.PICKING.getValue());
        }
    }

    /**
     * 取消播种
     * 1. 将已经匹配分拣数量和状态重置
     * 2. 修改播种人和波次配货状态
     *
     * @param waveId    波次Id
     * @param pickingId 拣选Id
     */
    @Override
    @Transactional
    public void cancelSeed(Staff staff, Long waveId, Long pickingId) {
        cancelSeeds(staff, Lists.newArrayList(waveId), Lists.newArrayList(pickingId));
    }

    @Override
    @Transactional
    public void cancelSeed(Staff staff, Long waveId, Long pickingId, boolean checkSorter) {
        cancelSeeds(staff, Lists.newArrayList(waveId), Lists.newArrayList(pickingId), checkSorter);
    }

    /**
     * 根据波次Id生成简单的trades用于记录日志
     *
     * @param waveId 波次号
     */
    private List<Trade> getSimpleTradesByWaveIdForTrace(Staff staff, Long waveId) {
        List<Long> sids = tradeWaveService.querySidsByWaveId(staff, waveId);

        if (CollectionUtils.isEmpty(sids)) {
            return Collections.emptyList();
        }

        List<Trade> trades = new ArrayList<Trade>(sids.size());
        for (Long sid : sids) {
            Trade trade = new Trade();
            trade.setSid(sid);
            trade.setCompanyId(staff.getCompanyId());
            trade.setTaobaoId(0L);
            trades.add(trade);
        }
        return trades;
    }

    @Override
    public WavePageList<WaveSorting> queryWavesSeedTrades(Staff staff, List<Long> waveIds, List<Long> pickingIds, Integer status, Page page) {
        status = (status == null ? 0 : status);
        WavePageList<WaveSorting> pageList = new WavePageList<WaveSorting>(page);
        Long count = waveSortingDao.queryCountByPickingIds(staff, pickingIds, status);
        pageList.setTotal(count);
        if (count == null || count <= 0L) {
            pageList.setTotal(0L);
            pageList.setList(Collections.<WaveSorting>emptyList());
            return pageList;
        }

        List<WaveSorting> sortings = waveSortingDao.queryByPickingIds(staff, pickingIds, status, page);
        Map<Long, WaveSorting> proportionSortingMaps = getProportionSorting(staff, sortings);
        List<Long> sids = Lists.newArrayListWithCapacity(sortings.size());
        if (CollectionUtils.isNotEmpty(sortings)) {
            sortings.forEach(sorting -> sorting.setPositionCode(WaveUtils.generatePositionCode(staff, sorting.getPositionNo().longValue())));
            sortings.forEach(sorting -> sids.add(sorting.getSid()));
            List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, false, sids.toArray(new Long[0]));
            for (WaveSorting waveSorting : sortings) {
                for (Trade trade : trades) {
                    if (waveSorting.getSid().equals(trade.getSid())) {
                        waveSorting.setIsExcep(trade.getIsExcep());
                    }
                }
                WaveSorting proportionSorting = proportionSortingMaps.get(waveSorting.getSid());
                if (proportionSorting != null) {
                    waveSorting.setItemNum(proportionSorting.getItemNum());
                    waveSorting.setMatchedNum(proportionSorting.getMatchedNum());
                }
            }
        }
        pageList.setList(sortings);
        return pageList;
    }

    private Map<Long, WaveSorting> getProportionSorting(Staff staff, List<WaveSorting> sortings) {
        Map<Long, WaveSorting> proportionSortingMaps = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(sortings)) {
            List<WaveSorting> proportionSortings = waveSortingDao.querySeedProportionBySortingId(staff, sortings.stream().map(WaveSorting::getId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(proportionSortings)) {
                proportionSortingMaps = proportionSortings.stream().collect(Collectors.toMap(WaveSorting::getSid, a -> a, (k1, k2) -> k1));
            }
        }
        return proportionSortingMaps;
    }

    @Override
    public WavePageList<WaveSorting> queryWaveSeedTrades(Staff staff, Long waveId, Long pickingId, Integer status, Page page) {
        return queryWavesSeedTrades(staff, Lists.newArrayList(waveId), Lists.newArrayList(pickingId), status, page);
    }

    @Override
    public WavePageList<WaveSortingDetail> queryWaveSeedItems(Staff staff, WavePickingParam param, Page page) {
        Long pickingId = param.getPickingId();
        Assert.isTrue(pickingId != null || CollectionUtils.isNotEmpty(param.getPickingIds()), "拣选id不能为空！");
        int status = ObjectUtils.defaultIfNull(param.getMatchedStatus(), 0);
        WavePageList<WaveSortingDetail> pageList = new WavePageList<WaveSortingDetail>(page);
        Long count = waveSortingDao.queryItemMapByPickingIdCount(staff, param);
        pageList.setTotal(count);
        if (count == null || count <= 0L) {
            pageList.setTotal(0L);
            pageList.setList(new ArrayList<>());
            return pageList;
        }

        List<WaveSortingDetail> sortingDetails = waveSortingDao.queryItemMapByPickingId(staff, param, page);
        if (BooleanUtils.isTrue(param.getShowItemInfo())) {
            fillItemInfo(staff, sortingDetails);
        }

        for (WaveSortingDetail detail : sortingDetails) {
            detail.setTotalNum(detail.getItemNum());
            detail.setItemNum(status == 1 ? detail.getMatchedNum() : detail.getItemNum() - detail.getMatchedNum());
            detail.setMatchedNum(detail.getMatchedNum());
        }

        if (BooleanUtils.isTrue(param.getShowPosition())) {
            assemblePositions(staff, param, sortingDetails);
        }

        //设置缺货数
        List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySidsAndItem(staff, null != param.getPickingId() ? param.getPickingId() : param.getPickingIds().get(0), null, null, null, false);
        Map<String, Integer> map = details.stream().collect(Collectors.toMap(this::buildKey, WaveSortingDetail::getShortageNum, (key1, key2) -> key1 + key2));
        for (WaveSortingDetail detail : sortingDetails) {
            List<WaveItemPosition> positions = detail.getPositions();
            if (CollectionUtils.isNotEmpty(positions)) {
                for (WaveItemPosition position : positions) {
                    Integer shortageNum = map.get(detail.getSysItemId() + "_" + detail.getSysSkuId() + "_" + position.getPositionNo());
                    position.setShortageNum(null == shortageNum ? 0 : shortageNum);
                }
            }
        }
        //设置货主
        assembleShipperInfo(staff,sortingDetails);
        pageList.setList(sortingDetails);
        return pageList;
    }

    private void assembleShipperInfo(Staff staff,List<WaveSortingDetail> waveSortingDetails){
        if(!CompanyUtils.openMultiShipper(staff)||CollectionUtils.isEmpty(waveSortingDetails)){
            return;
        }
        List<Long> sysItemIds = waveSortingDetails.stream().map(WaveSortingDetail::getSysItemId).filter(java.util.Objects::nonNull).collect(Collectors.toList());

        //根据商品获取对应的货主
        Map<Long, DmjItem> itemIdMap = Maps.newHashMapWithExpectedSize(sysItemIds.size());
        try{
            for (List<Long> subItemIds : Lists.partition(Lists.newArrayList(sysItemIds), 200)) {
                if(CollectionUtils.isNotEmpty(subItemIds)){
                    List<DmjItem> dmjItems = itemService.queryItemWithSysItemId(staff, subItemIds);
                    itemIdMap.putAll(dmjItems.stream().collect(Collectors.toMap(DmjItem::getSysItemId, java.util.function.Function.identity(), (v1, v2) -> v2)));
                }
            }
            waveSortingDetails.forEach(sortingDetail -> {
                DmjItem dmjItem = itemIdMap.get(sortingDetail.getSysItemId());
                if(dmjItem!=null){
                    sortingDetail.setShipperId(dmjItem.getShipperId());
                    sortingDetail.setShipperName(dmjItem.getShipper());
                    sortingDetail.setShipper(dmjItem.getShipper());
                }
            });
        }catch (Exception e){
            logger.error("assembleShipperInfo failed", e);
        }
    }



    @Override
    public WavePageList<WaveSortingDetail> queryWaveSeedItemsSimple(Staff staff, WavePickingParam param, Page page) {
        Long pickingId = param.getPickingId();
        Assert.isTrue(pickingId != null || CollectionUtils.isNotEmpty(param.getPickingIds()), "拣选id不能为空！");
        WavePageList<WaveSortingDetail> pageList = new WavePageList<>(page);
        Long count = waveSortingDao.queryItemPositionByPickingIdCount(staff, param);
        pageList.setTotal(count);
        if (count == null || count <= 0L) {
            pageList.setTotal(0L);
            pageList.setList(new ArrayList<>());
            return pageList;
        }
        List<WaveSortingDetail> sortingDetails = waveSortingDao.queryItemPositionByPickingId(staff, param, page);
        fillItemInfo(staff, sortingDetails);
        fillPositionInfo(staff, sortingDetails);
        assembleShipperInfo(staff,sortingDetails);
        pageList.setList(sortingDetails);
        return pageList;
    }

    private String buildKey(WaveSortingDetail detail) {
        return detail.getSysItemId() + "_" + detail.getSysSkuId() + "_" + detail.getPositionNo();
    }

    private void fillPositionInfo(final Staff staff, List<WaveSortingDetail> sortingDetails) {
        if (CollectionUtils.isEmpty(sortingDetails)) {
            return;
        }
        //get goodsSectionCode
        List<Long> orderIds = sortingDetails.stream().map(WaveSortingDetail::getOrderId).distinct().collect(Collectors.toList());
        Map<Long, String> sectionCodeMap;
        if (WmsUtils.isNewWms(staff)) {
            List<AllocateGoodsRecord> records = wmsService.queryAllocateGoodsRecords(staff, new QueryAllocateGoodsRecordParams.Builder().allocateType(AllocateType.TRADE.getValue()).orderIds(orderIds).build());
            WmsConfig wmsConfig = wmsService.getConfig(staff);
            if (records == null) {
                records = new ArrayList<>();
            }
            List<Long> sectionIds = records.stream().map(AllocateGoodsRecord::getGoodsSectionId).collect(Collectors.toList());
            Map<Long, GoodsSection> encodeGsCodeMap = wmsService.queryEncodeGsCodeMap(staff, sectionIds);
            for (AllocateGoodsRecord record : records) {
                record.setGoodsSectionCode(WmsUtils.encodeGsCode(wmsConfig, record.getGoodsSectionCode(), encodeGsCodeMap.get(record.getGoodsSectionId())));
            }
            sectionCodeMap = records.stream().collect(
                    Collectors.toMap(AllocateGoodsRecord::getOrderId, AllocateGoodsRecord::getGoodsSectionCode, (k1, k2) -> k1));
        } else {
            List<GoodsSectionOrderRecord> records = wmsService.queryGoodsSectionInfoByOrderIds(staff, orderIds);
            sectionCodeMap = Optional.ofNullable(records).orElse(Lists.newArrayList()).stream().collect(
                    Collectors.toMap(GoodsSectionOrderRecord::getOrderId, GoodsSectionOrderRecord::getGoodsSectionCode, (k1, k2) -> k1));
        }
        for (WaveSortingDetail detail : sortingDetails) {
            detail.setPositionCode(WaveUtils.generatePositionCode(staff, detail.getPositionNo()));
            Optional.ofNullable(sectionCodeMap.get(detail.getOrderId())).ifPresent(detail::setGoodsSectionCode);
        }
    }

    private void assemblePositions(final Staff staff, WavePickingParam param, List<WaveSortingDetail> sortingDetails) {
        if (CollectionUtils.isEmpty(sortingDetails)) {
            return;
        }
        Integer matchedStatus = param.getMatchedStatus();
        //这里要查出未播和已播的明细
        param.setMatchedStatus(null);
        //get positions
        List<WaveItemPosition> itemPositions = waveSortingDao.queryItemPositionMapByPickingId(staff, param, null);
        if (CollectionUtils.isEmpty(itemPositions)) {
            return;
        }
        Map<String, List<WaveItemPosition>> itemPositionsMap = MapUtils.toMapList(itemPositions, position -> {
            position.setPositionCode(WaveUtils.generatePositionCode(staff, position.getPositionNo()));
            String itemKey = position.getSysItemId() + "_" + position.getSysSkuId();
            position.setSysItemId(null);
            position.setSysSkuId(null);
            return itemKey;
        });

        //get goodsSectionCode
        List<Long> orderIds = sortingDetails.stream().map(WaveSortingDetail::getOrderId).distinct().collect(Collectors.toList());
        Map<Long, String> sectionCodeMap;
        if (WmsUtils.isNewWms(staff)) {
            List<AllocateGoodsRecord> records = wmsService.queryAllocateGoodsRecords(staff, new QueryAllocateGoodsRecordParams.Builder().allocateType(AllocateType.TRADE.getValue()).orderIds(orderIds).build());
            WmsConfig wmsConfig = wmsService.getConfig(staff);
            if (records == null) {
                records = new ArrayList<>();
            }
            List<Long> sectionIds = records.stream().map(AllocateGoodsRecord::getGoodsSectionId).collect(Collectors.toList());
            Map<Long, GoodsSection> encodeGsCodeMap = wmsService.queryEncodeGsCodeMap(staff, sectionIds);
            for (AllocateGoodsRecord record : records) {
                record.setGoodsSectionCode(WmsUtils.encodeGsCode(wmsConfig, record.getGoodsSectionCode(), encodeGsCodeMap.get(record.getGoodsSectionId())));
            }
            sectionCodeMap = records.stream().collect(
                    Collectors.toMap(AllocateGoodsRecord::getOrderId, AllocateGoodsRecord::getGoodsSectionCode, (k1, k2) -> k1));
        } else {
            List<GoodsSectionOrderRecord> records = wmsService.queryGoodsSectionInfoByOrderIds(staff, orderIds);
            sectionCodeMap = Optional.ofNullable(records).orElse(Lists.newArrayList()).stream().collect(
                    Collectors.toMap(GoodsSectionOrderRecord::getOrderId, GoodsSectionOrderRecord::getGoodsSectionCode, (k1, k2) -> k1));
        }

        //assemble data
        for (WaveSortingDetail sortingDetail : sortingDetails) {
            List<WaveItemPosition> positions = itemPositionsMap.get(sortingDetail.getSysItemId() + "_" + sortingDetail.getSysSkuId());
            if (CollectionUtils.isEmpty(positions)) {
                continue;
            }
            for (WaveItemPosition position : positions) {
                position.setOuterId(sortingDetail.getOuterId());
                Optional.ofNullable(sectionCodeMap.get(sortingDetail.getOrderId())).ifPresent(position::setGoodsSectionCode);
            }
            sortingDetail.setPositions(positions.stream().sorted(Comparator.comparing(WaveItemPosition::getPositionNo)).collect(Collectors.toList()));
        }
        param.setMatchedStatus(matchedStatus);
    }
    @Override
    public void fillItemInfo(Staff staff, List<WaveSortingDetail> sortingDetails) {
        Map<String, DmjItem> itemKeyMap = getDetailsItemSkuMap(staff, sortingDetails);
        for (WaveSortingDetail detail : sortingDetails) {
            WavePickUtils.fillItemInfo(detail, itemKeyMap.get(WaveHelpBusiness.buildItemKey(detail)));
        }
    }

    @Override
    public WavePageList<WaveItemPosition> queryWaveSeedItemPositionNos(Staff staff, List<Long> waveIds, List<Long> pickingIds, Integer status, Long sysItemId, Long sysSkuId, Page page) {
        if (sysSkuId == null || sysSkuId <= 0L) {
            sysSkuId = -1L;
        }
        if (status == null) {
            status = 0;
        }

        WavePageList<WaveItemPosition> pageList = new WavePageList<>(page);
        WavePickingParam param = new WavePickingParam();
        param.setPickingIds(pickingIds);
        param.setWaveIds(waveIds);
        param.setMatchedStatus(status);
        param.setSysItemId(sysItemId);
        param.setSysSkuId(sysSkuId);
        param.setStockStatus(0);
        List<WaveItemPosition> itemPositions = waveSortingDao.queryItemPositionMapByPickingId(staff, param, page);
        for (WaveItemPosition itemPosition : itemPositions) {
            if (status == 1) {
                itemPosition.setItemNum(itemPosition.getMatchedNum());
            } else {
                itemPosition.setItemNum(itemPosition.getItemNum() - itemPosition.getMatchedNum());
            }
            itemPosition.setPositionCode(WaveUtils.generatePositionCode(staff, itemPosition.getPositionNo()));
        }
        pageList.setPage(page);
        pageList.setList(itemPositions);
        if (page != null) {
            if (itemPositions.size() < page.getPageSize()) {
                pageList.setTotal((long) (page.getStartRow() + itemPositions.size()));
            } else {
                pageList.setTotal(waveSortingDao.queryItemPositionCountByPickingId(staff, param));
            }
        }

        return pageList;
    }

    @Override
    public WavePageList<WaveItemPosition> queryWaveSeedItemPositionNos(Staff staff, Long waveId, Long pickingId, Integer status, Long sysItemId, Long sysSkuId, Page page) {
        return queryWaveSeedItemPositionNos(staff, Lists.newArrayList(waveId), Lists.newArrayList(pickingId), status, sysItemId, sysSkuId, page);
    }

    private void logWaveItemParams(Staff staff, List<WaveItem> waveItems) {
        for (List<WaveItem> items : Lists.partition(waveItems, 50)) {
            StringBuilder log = new StringBuilder("params:");
            for (WaveItem waveItem : items) {
                log.append("sysItemId=").append(waveItem.getSysItemId()).append(",sysSkuId=").append(waveItem.getSysSkuId()).append(",num=").append(waveItem.getNum()).append(";");
            }
            if (log.length() > 0) {
                log.deleteCharAt(log.length() - 1);
            }
            logger.debug(LogHelper.buildLog(staff, log.toString()));
        }
    }


    @Override
    public List<WaveItemPosition> queryWaveItemPositionWithNum(Staff staff, List<Long> waveIds, List<Long> sids, List<WaveItem> waveItems, Integer stockStatus, Integer suitType, List<String> uniqueCodes) {
        logger.debug(LogHelper.buildLog(staff, String.format("查询波次%s商家编码位置号数量,stockStatus:%s", waveIds, stockStatus)));
        logWaveItemParams(staff, waveItems);
        long start = System.currentTimeMillis();
        WaveConfig waveConfig = waveConfigService.get(staff);
        Integer printStatus = Integer.valueOf(1).equals(waveConfig.getWaveSeedType()) ? -1 : WaveSorting.PRINT_STATUS_NOT;
        if (Objects.equal(waveConfig.getOpenWaveCodePrintNew(), 1)) {
            List<WaveItemPosition> resultList = tradeWaveService.queryWaveItemPositionWithNum(staff, waveIds, sids, waveItems, stockStatus, suitType, uniqueCodes);
            logger.debug(LogHelper.buildLog(staff, String.format("完成波次%s商品位置号信息组装，耗时：%s", waveIds, (System.currentTimeMillis() - start))));
            return resultList;
        }
        List<WaveItemPosition> resultList = new ArrayList<>();
        BeanCopier copier = BeanCopier.create(WaveItemPosition.class, WaveItemPosition.class, false);
        List<WavePicking> pickings = wavePickingDao.getByWaveIds(staff, waveIds);
        Map<Long, Map<Long, WaveSorting>> pickingPosSortingMap = queryUnSeedPosSortingByPickingIds(staff, pickings, sids, printStatus, suitType);//获取波次订单位置号
        Map<String, List<WaveItemPosition>> waveItemPositionsMap = queryWaveItemPositionsMap(staff, pickings, sids, stockStatus, printStatus, suitType);//获取波次商品位置号
        logger.debug(LogHelper.buildLog(staff, "数据加载完成"));
        for (WaveItem waveItem : waveItems) {
            Integer num = waveItem.getNum();
            if (num == null || num <= 0) {
                continue;
            }
            //多个波次的数据或单个波次的多个数据都要返回 后面再通过唯一码去筛选
            int index = 0;
            for (WavePicking picking : pickings) {
                String key = picking.getId() + "_" + waveItem.getSysItemId() + "_" + waveItem.getSysSkuId();
                List<WaveItemPosition> itemPositions = waveItemPositionsMap.get(key);
                if (CollectionUtils.isNotEmpty(itemPositions)) {
                    List<WaveItemPosition> itemPosList = Lists.newArrayListWithCapacity(num);
                    Map<Long, WaveSorting> posWaveSortingMap = pickingPosSortingMap.get(picking.getId());
                    if (posWaveSortingMap != null) {
                        fillTradeItemNum(staff, picking.getId(), posWaveSortingMap, itemPositions);
                    }
                    for (WaveItemPosition itemPosition : itemPositions) {
                        itemPosition.setPositionCode(WaveUtils.generatePositionCode(staff, itemPosition.getPositionNo()));
                        itemPosition.setPickingId(picking.getId());
                        itemPosition.setWaveId(picking.getWaveId());
                        itemPosition.setPickingCode(picking.getPickingCode());
                        for (int i = 0; i < itemPosition.getItemNum(); i++) {
                            WaveItemPosition temp = new WaveItemPosition();
                            copier.copy(itemPosition, temp, null);
                            itemPosList.add(temp);
                            index++;
                        }
                    }
                    resultList.addAll(itemPosList);
                }
            }
            //当打印数量大于位置号数量，需要生成默认的
            int diff = num - index;
            if (diff > 0) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("商品%s-%s需要打印数量%s超过波次商品数量，超出%s", waveItem.getSysItemId(), waveItem.getSysSkuId(), num, diff)));
                }
                for (int i = 0; i < diff; i++) {
                    WaveItemPosition temp = new WaveItemPosition();
                    temp.setSysItemId(waveItem.getSysItemId());
                    temp.setSysSkuId(waveItem.getSysSkuId());
                    resultList.add(temp);
                }
            }
        }

        logger.debug(LogHelper.buildLog(staff, "完成组装商品位置号数据"));
        //填充唯一码 和 根据唯一码过滤
        resultList = assembleItemPositionUniqueCode(staff, waveConfigService.get(staff), resultList, stockStatus, uniqueCodes);

        //如果开启仓储需要填充货位信息
        if (staff.getConf().isOpenWms()) {
            //填充货位信息
            fillWaveItemWithGoodsSectionCode(staff, resultList);
            logger.debug(LogHelper.buildLog(staff, "完成组装货位信息数据"));
        }
        logger.debug(LogHelper.buildLog(staff, String.format("完成波次%s商品位置号信息组装，耗时：%s", waveIds, (System.currentTimeMillis() - start))));
        return resultList;
    }

    private List<WaveItemPosition> assembleItemPositionUniqueCode(Staff staff, WaveConfig waveConfig, List<WaveItemPosition> itemPositions, Integer stockStatus, List<String> uniqueCodeList) {
        Set<Long> waveIds = Sets.newHashSet();
        Set<Long> orderIds = Sets.newHashSet();
        for (WaveItemPosition itemPosition : itemPositions) {
            if (itemPosition.getWaveId() != null) {
                waveIds.add(itemPosition.getWaveId());
            }
            if (itemPosition.getOrderId() != null) {
                orderIds.add(itemPosition.getOrderId());
            }
        }

        List<WaveUniqueCode> waveUniqueCodes = querySortedWaveUniqueCodes(staff, waveConfig, Lists.newArrayList(waveIds), null, Lists.newArrayList(orderIds), stockStatus);
        Map<String, List<WaveUniqueCode>> orderIdCodesMap = waveUniqueCodes.stream().collect(Collectors.groupingBy(code -> code.getWaveId() + "_" + code.getOrderId()));
        if (orderIdCodesMap == null || orderIdCodesMap.isEmpty()) {
            return itemPositions;
        }

        for (WaveItemPosition itemPosition : itemPositions) {
            if (itemPosition.getOrderId() == null) {
                continue;
            }
            List<WaveUniqueCode> uniqueCodes = orderIdCodesMap.get(itemPosition.getWaveId() + "_" + itemPosition.getOrderId());
            if (CollectionUtils.isNotEmpty(uniqueCodes)) {
                WaveUniqueCode uniqueCode = uniqueCodes.remove(0);
                itemPosition.setUniqueCode(uniqueCode.getUniqueCode());
                itemPosition.setStockStatus(uniqueCode.getStockStatus());
            } else {
                logger.debug(LogHelper.buildLog(staff, String.format("商品%s-%s(%s)没有找到唯一码记录", itemPosition.getSysItemId(), itemPosition.getSysSkuId(), itemPosition.getOrderId())));
            }
        }

        if (CollectionUtils.isNotEmpty(uniqueCodeList)) {
            logger.debug(LogHelper.buildLog(staff, "需要组装的唯一码:" + uniqueCodeList));
            return itemPositions.stream().filter(itemPosition -> StringUtils.isNotEmpty(itemPosition.getUniqueCode()) && uniqueCodeList.contains(itemPosition.getUniqueCode())).collect(Collectors.toList());
        }
        return itemPositions;
    }

    private void fillWaveItemWithGoodsSectionCode(Staff staff, List<WaveItemPosition> positions) {
        List<Long> orderIds = positions.stream().filter(position -> position.getOrderId() != null).map(WaveItemPosition::getOrderId).collect(Collectors.toList());
        if (orderIds.isEmpty()) {
            return;
        }

        List<GoodsSectionOrderRecord> gsRecords = WmsUtils.isNewWms(staff)
                ? wmsService.queryGoodsSectionInfoByOrderIdsOfNewWms(staff, orderIds)
                : waveQueryDao.queryGoodsSectionOrderRecords(staff, orderIds);
        if (CollectionUtils.isEmpty(gsRecords)) {
            return;
        }
        if (!WmsUtils.isNewWms(staff)) {
            gsRecords = (List<GoodsSectionOrderRecord>) wmsService.getGoodsSectionDisplay(staff, gsRecords);
        }

        Map<Long, List<GoodsSectionOrderRecord>> orderIdRecordsMap = Maps.newHashMapWithExpectedSize(orderIds.size());
        for (GoodsSectionOrderRecord gsRecord : gsRecords) {
            List<GoodsSectionOrderRecord> records = orderIdRecordsMap.get(gsRecord.getOrderId());
            if (records == null) {
                records = Lists.newArrayList();
            }
            records.add(gsRecord);
            orderIdRecordsMap.put(gsRecord.getOrderId(), records);
        }

        for (WaveItemPosition itemPosition : positions) {
            if (itemPosition.getOrderId() != null && orderIdRecordsMap.containsKey(itemPosition.getOrderId())) {
                List<GoodsSectionOrderRecord> records = orderIdRecordsMap.get(itemPosition.getOrderId());
                for (GoodsSectionOrderRecord record : records) {
                    //itemPosition每一条记录就是1个需要返回的数据，不考虑itemNum
                    if (record.getApplyNum() <= 0) {
                        continue;
                    }
                    record.setApplyNum(record.getApplyNum() - 1);
                    itemPosition.setGoodsSectionCode(record.getGoodsSectionCode());
                    itemPosition.setGoodsSectionId(record.getGoodsSectionId());
                    itemPosition.setGoodsSectionDisplay(record.getGoodsSectionDisplay());
                    break;
                }
            }
        }
    }

    /**
     * 获取wavesorting 同一个位置号的会吧item num 求和
     */
    private Map<Long, Map<Long, WaveSorting>> queryUnSeedPosSortingByPickingIds(Staff staff, List<WavePicking> pickings, List<Long> sids, Integer printStatus,Integer suitType) {
        Map<Long, Map<Long, WaveSorting>> pickingPosSortingMap = Maps.newHashMapWithExpectedSize(pickings.size());
        for (WavePicking picking : pickings) {
            WaveSortingSeedParam seedParam = new WaveSortingSeedParam();
            seedParam.setPickingIds(Lists.newArrayList(picking.getId()));
            seedParam.setSids(sids);
            seedParam.setPrintStatus(printStatus);
            seedParam.setSuitType(suitType);
            List<WaveSorting> waveSortings = waveSortingDao.queryUnSeedSortingsByPickingId(staff, seedParam);
            Map<Long, WaveSorting> posWaveSortingMap = Maps.newHashMapWithExpectedSize(waveSortings.size());
            for (WaveSorting waveSorting : waveSortings) {
                WaveSorting tempSorting = posWaveSortingMap.get(waveSorting.getPositionNo().longValue());
                if (tempSorting == null) {
                    posWaveSortingMap.put(waveSorting.getPositionNo().longValue(), waveSorting);
                } else {
                    tempSorting.setItemNum(tempSorting.getItemNum() + waveSorting.getItemNum());
                }
            }
            pickingPosSortingMap.put(picking.getId(), posWaveSortingMap);
        }
        return pickingPosSortingMap;
    }

    /**
     * 获取 波次商品位置号 按 拣选号_商品id_规格id 分组
     */
    private Map<String, List<WaveItemPosition>> queryWaveItemPositionsMap(Staff staff, List<WavePicking> pickings, List<Long> sids, Integer stockStatus, Integer printStatus, Integer suitType) {
        Map<String, List<WaveItemPosition>> waveItemPositionsMap = Maps.newHashMapWithExpectedSize(pickings.size());
        for (WavePicking picking : pickings) {
            List<WaveItemPosition> itemPositions = waveSortingDao.queryPositionAndOrderId(staff, picking.getId(), sids, printStatus, 0, stockStatus, suitType);
            for (WaveItemPosition itemPosition : itemPositions) {
                String key = picking.getId() + "_" + itemPosition.getSysItemId() + "_" + itemPosition.getSysSkuId();
                List<WaveItemPosition> positions = waveItemPositionsMap.get(key);
                if (positions == null) {
                    waveItemPositionsMap.put(key, Lists.newArrayList(itemPosition));
                } else {
                    positions.add(itemPosition);
                }
            }
        }
        return waveItemPositionsMap;
    }

    private void fillTradeItemNum(Staff staff, Long pickingId, Map<Long, WaveSorting> posWaveSortingMap, List<WaveItemPosition> itemPositions) {
        for (WaveItemPosition itemPosition : itemPositions) {
            WaveSorting waveSorting = posWaveSortingMap.get(itemPosition.getPositionNo());
            if (waveSorting != null) {
                itemPosition.setTradeItemNum(waveSorting.getItemNum().intValue());
            } else {
                itemPosition.setTradeItemNum(itemPosition.getItemNum());
                logger.warn(LogHelper.buildLog(staff, String.format("拣选%s订单%s商品%s分配总数异常", pickingId, itemPosition.getSid(), itemPosition.getSysItemId() + "_" + itemPosition.getSysSkuId())));
            }
        }
    }


    @Override
    public Map<Long, List<WaveItemPosition>> querySidOrderIdMapByWaveIds(Staff staff, List<Long> waveIds, Integer matchedStatus, Integer stockStatus) {
        Map<Long, List<WaveItemPosition>> sidOrdersMap = Maps.newHashMap();
        for (Long waveId : waveIds) {
            WavePicking picking = wavePickingDao.getByWaveId(staff, waveId);
            if (picking == null) {
                logger.warn(LogHelper.buildLog(staff, "波次未完成拣选:" + waveId));
                continue;
            }

            List<WaveItemPosition> itemPositions = waveSortingDao.queryPositionAndOrderIdByItem(staff, picking.getId(), null, null, null, null, matchedStatus, stockStatus, null);
            for (WaveItemPosition itemPosition : itemPositions) {
                List<WaveItemPosition> orderIds = sidOrdersMap.get(itemPosition.getSid());
                if (orderIds == null) {
                    sidOrdersMap.put(itemPosition.getSid(), Lists.newArrayList(itemPosition));
                } else {
                    orderIds.add(itemPosition);
                }
            }
        }

        return sidOrdersMap;
    }

    @Override
    public WavePageList<WaveItem> queryWaveUnSeedItemPositionPageList(Staff staff, List<Long> waveIds, Integer stockStatus, Page page) {
        return queryWaveUnSeedItemPositionPageList(staff, new QueryWaveItemPageParams().setWaveIds(waveIds).setStockStatus(stockStatus).setPage(page));
    }

    /**
     * 打印商家编码查询 非 唯一码 的 打印数据
     */
    @Override
    public WavePageList<WaveItem> queryNotUniqueItems4Print(Staff staff, QueryWaveItemPageParams params) {
        UniqueCodeUtils.debugLog(staff, params, logger, "queryNotUniqueItems4PrintBegin.params");
        List<Long> waveIds = params.getWaveIds();
        if (params.getStockStatus() == null) {
            params.setStockStatus(0);
        }
        Page page = params.getPage();
        WaveConfig waveConfig = waveConfigService.get(staff);
        List<WavePicking> pickings = wavePickingDao.getByWaveIds(staff, waveIds);
        Assert.isTrue(waveIds.size() == pickings.size(), "存在未拣选的波次！");
        List<Wave> waves = tradeWaveService.queryWaveByIds(staff, waveIds.toArray(new Long[0]));
        // 判断是否只查询未发货
        waveCodePrintBusiness.judgeUnConsign(params, waves);
        if (params.getSuitType() != null && 1 == params.getSuitType()) {
            for (Wave wave : waves) {
                if (WaveUtils.isWaveSuitCanPrint(wave)) {
                    throw new IllegalArgumentException(String.format("波次号[%s]状态非拣选中/拣选完成，不能打印套件，请去除后打印!", wave.getId()));
                }
            }
        }
        WavePickingParam pickingParam = buildUnSeedQueryParams(params, pickings.stream().map(WavePicking::getId).collect(Collectors.toList()), waveConfig);
        Long count = waveSortingDao.queryItemMapByPickingIdCount(staff, pickingParam);
        WavePageList<WaveItem> pageList = new WavePageList<>(page);
        pageList.setTotal(count);
        if (count == null || count <= 0L) {
            pageList.setTotal(0L);
            pageList.setList(Collections.emptyList());
            return pageList;
        }
        UniqueCodeUtils.debugLog(staff, pickingParam, logger, "queryNotUniqueItems4Print.params");
        List<WaveSortingDetail> details = waveSortingDao.queryNotUniqueItems4Print(staff, pickingParam);
        List<WaveItem> itemPositions = Lists.newArrayListWithCapacity(details.size());
        Set<String> supplierIds = CollectionUtils.isNotEmpty(params.getSupplierIds()) ? Sets.newHashSet(params.getSupplierIds()) : null;
        Map<String, List<ItemSupplierBridge>> itemBridgesMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(params.getSupplierIds()) || CollectionUtils.isNotEmpty(params.getCategoryNameList())) {
            itemBridgesMap = getSupplierInfo(staff, details);
        }

        boolean excludeSuppplier = Objects.equal(params.getExcludeSuppplier(), CommonConstants.JUDGE_YES);
        long itemCount = 0L;
        for (WaveSortingDetail detail : details) {
            WaveItem itemPosition = new WaveItem();
            itemPosition.setWaveId(detail.getWaveId());
            itemPosition.setSysItemId(detail.getSysItemId());
            itemPosition.setSysSkuId(detail.getSysSkuId());
            itemPosition.setOuterId(detail.getOuterId());
            itemPosition.setOrderId(detail.getOrderId());
            itemPosition.setSid(detail.getSid());
            itemPosition.setTitle(detail.getTitle());
            String key = WaveHelpBusiness.buildItemKey(detail);
            List<ItemSupplierBridge> bridges = itemBridgesMap.get(key);
            boolean match = WavePickUtils.matchSupplier(bridges, supplierIds, excludeSuppplier) && WavePickUtils.matchCategoryName(bridges, params.getCategoryNameList());
            if (!match) {
                continue;
            }
            if (params.getStockStatus() == 2) {
                itemPosition.setNum(detail.getItemNum() - Math.max(detail.getMatchedNum() - detail.getStockNum(), 0));
            } else {
                itemPosition.setNum(detail.getItemNum() - detail.getMatchedNum());
            }
            itemPositions.add(itemPosition);
            itemCount += itemPosition.getNum();
        }
        pageList.setItemCount(itemCount);
        pageList.setList(itemPositions);

        if (params.isNeedUniqueCode()) {
            assembleWaveItemUniqueCode(staff, waveConfig, pageList, params);
        }
//        assembleGoodsSectionCode(staff, pageList);
        return pageList;
    }

    @Override
    public WavePageList<WaveItem> queryWaveUnSeedItemPositionPageList(Staff staff, QueryWaveItemPageParams params) {
        List<Long> waveIds = params.getWaveIds();
        Integer stockStatus = params.getStockStatus();
        Page page = params.getPage();

        if (stockStatus == null) {
            stockStatus = 0;
            params.setStockStatus(stockStatus);
        }

        WaveConfig waveConfig = waveConfigService.get(staff);
        List<WavePicking> pickings = wavePickingDao.getByWaveIds(staff, waveIds);
        Assert.isTrue(waveIds.size() == pickings.size(), "存在未拣选的波次！");
        List<Wave> waves = tradeWaveService.queryWaveByIds(staff, waveIds.toArray(new Long[0]));
        // 判断是否只查询未发货
        waveCodePrintBusiness.judgeUnConsign(params, waves);
        if (params.getSuitType() != null && 1 == params.getSuitType()) {
            for (Wave wave : waves) {
                if (WaveUtils.isWaveSuitCanPrint(wave)) {
                    throw new IllegalArgumentException(String.format("波次号[%s]状态非拣选中/拣选完成，不能打印套件，请去除后打印!", wave.getId()));
                }
            }
        }

        WavePickingParam pickingParam = buildUnSeedQueryParams(params, pickings.stream().map(WavePicking::getId).collect(Collectors.toList()), waveConfig);
        Long count = waveSortingDao.queryItemMapByPickingIdCount(staff, pickingParam);
        WavePageList<WaveItem> pageList = new WavePageList<>(page);
        pageList.setTotal(count);
        if (count == null || count <= 0L) {
            pageList.setTotal(0L);
            pageList.setList(Collections.emptyList());
            return pageList;
        }

        List<WaveSortingDetail> details = waveSortingDao.queryItemMapByPickingId(staff, pickingParam, page);
        Map<String, List<ItemSupplierBridge>> itemBridgesMap = getSupplierInfo(staff, details);
        Map<String, DmjItem> itemMap = getDetailsItemSkuMap(staff, details);
        List<WaveItem> itemPositions = Lists.newArrayListWithCapacity(details.size());
        long itemCount = 0L;
        Set<String> supplierIds = CollectionUtils.isEmpty(params.getSupplierIds()) ? null : Sets.newHashSet(params.getSupplierIds());
        boolean excludeSuppplier = Objects.equal(params.getExcludeSuppplier(), CommonConstants.JUDGE_YES);
        for (WaveSortingDetail detail : details) {
            DmjItem item = itemMap.get(WaveHelpBusiness.buildItemKey(detail));
            WaveItem itemPosition = buildWaveItem(staff, detail, item);
            String key = WmsKeyUtils.buildItemKey(itemPosition);
            List<ItemSupplierBridge> bridges = itemBridgesMap.get(key);
            boolean match = WavePickUtils.matchSupplier(bridges, supplierIds, excludeSuppplier) && WavePickUtils.matchCategoryName(bridges, params.getCategoryNameList());
            if (!match) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(bridges)) {
                List<String> itemSupplierIds = Lists.newArrayListWithExpectedSize(bridges.size());
                List<String> itemSupplierNames = Lists.newArrayListWithExpectedSize(bridges.size());
                for (ItemSupplierBridge bridge : bridges) {
                    itemSupplierIds.add(bridge.getSupplierId().toString());
                    itemSupplierNames.add(bridge.getSupplierName());
                }
                itemPosition.setSupplierId(itemSupplierIds);
                itemPosition.setSupplierName(itemSupplierNames);
            } else {
                itemPosition.setSupplierId(Collections.emptyList());
                itemPosition.setSupplierName(Collections.emptyList());
            }

            if (stockStatus == 2) {
                itemPosition.setNum(detail.getItemNum() - Math.max(detail.getMatchedNum() - detail.getStockNum(), 0));
            } else {
                itemPosition.setNum(detail.getItemNum() - detail.getMatchedNum());
            }
            itemCount += itemPosition.getNum();
            itemPositions.add(itemPosition);
        }
        pageList.setItemCount(itemCount);
        pageList.setList(itemPositions);

        assembleWaveItemUniqueCode(staff, waveConfig, pageList, params);
        assembleGoodsSectionCode(staff, pageList);
        return pageList;
    }

    private WaveItem buildWaveItem(Staff staff, WaveSortingDetail detail, DmjItem item) {
        WaveItem itemPosition = new WaveItem();
        itemPosition.setWaveId(detail.getWaveId());
        itemPosition.setSysItemId(detail.getSysItemId());
        itemPosition.setSysSkuId(detail.getSysSkuId());
        itemPosition.setOuterId(detail.getOuterId());
        itemPosition.setOrderId(detail.getOrderId());
        itemPosition.setSid(detail.getSid());

        if (item != null) {
            itemPosition.setTitle(item.getTitle());
            itemPosition.setOuterId(item.getOuterId());
            if (item instanceof DmjSku) {
                DmjSku sku = (DmjSku) item;
                itemPosition.setPropertiesName(sku.getPropertiesName());
                itemPosition.setPropertiesAlias(sku.getPropertiesAlias());
            }
            itemPosition.setPrice(checkSalePower(staff) ? String.valueOf(item.getPriceOutput()) : "***");
        }
        return itemPosition;
    }

    private WavePickingParam buildUnSeedQueryParams(QueryWaveItemPageParams params, List<Long> pickingIds, WaveConfig waveConfig) {
        WavePickingParam pickingParam = new WavePickingParam();
        pickingParam.setPickingIds(pickingIds);
        pickingParam.setSuiteType(params.getSuitType());

        pickingParam.setMatchedStatus(0);
        pickingParam.setStockStatus(params.getStockStatus());
        pickingParam.setUniqueCodeStockStatus(params.getUniqueCodeStockStatus());
        pickingParam.setSearchKeyword(params.getSearchKeyword());
        pickingParam.setOpenWaveUniqueCode(waveConfig.getOpenWaveUniqueCode());
        pickingParam.setSids(params.getSids());
        pickingParam.setPrintStatus(WaveSorting.PRINT_STATUS_NOT);
        pickingParam.setNeedUnConsign(params.getNeedUnConsign());
        return pickingParam;
    }

    private void assembleWaveItemUniqueCode(Staff staff, WaveConfig waveConfig, WavePageList<WaveItem> pageList, QueryWaveItemPageParams params) {
        List<WaveItem> waveItems = pageList.getList();

        List<WaveUniqueCode> waveUniqueCodes = querySortedWaveUniqueCodes(staff, waveConfig, params.getWaveIds(), params.getSids(), null, params.getStockStatus());
        if (CollectionUtils.isNotEmpty(waveUniqueCodes) && DataUtils.checkIntegerNotEmpty(params.getUniqueCodeStockStatus())) {
            waveUniqueCodes.removeIf(w -> !Objects.equal(params.getUniqueCodeStockStatus(), w.getStockStatus()));
        }
        Map<String, List<WaveUniqueCode>> orderIdCodesMap = waveUniqueCodes.stream().collect(Collectors.groupingBy(code -> code.getWaveId() + "_" + code.getOrderId()));

        if (orderIdCodesMap == null || orderIdCodesMap.isEmpty()) {
            return;
        }

        List<WaveItem> resultList = Lists.newArrayListWithCapacity(waveItems.size());
        for (WaveItem waveItem : waveItems) {
            List<WaveUniqueCode> uniqueCodes = orderIdCodesMap.get(waveItem.getWaveId() + "_" + waveItem.getOrderId());
            if (CollectionUtils.isNotEmpty(uniqueCodes)) {
                resultList.addAll(copyUniqueWaveItems(waveItem, uniqueCodes.subList(0, Math.min(uniqueCodes.size(), waveItem.getNum()))));
            }
        }

        if (StringUtils.isNotEmpty(params.getSearchKeyword())) {
            resultList = resultList.stream().filter(waveItem -> WavePickUtils.matchSearchKey(waveItem, params.getSearchKeyword())).collect(Collectors.toList());
        }

        pageList.setTotal((long) resultList.size());
        if (pageList.getPage() != null) {
            pageList.setList(subListByPage(resultList, pageList.getPage()));
        } else {
            pageList.setList(resultList);
        }
    }

    public static <T> List<T> subListByPage(List<T> list, Page page) {
        int start = page.getStartRow();
        if (start >= list.size()) {
            return Collections.emptyList();
        }
        int end = Math.min(start + page.getPageSize(), list.size());
        return Lists.newArrayList(list.subList(start, end));
    }

    private List<WaveItem> copyUniqueWaveItems(WaveItem waveItem, List<WaveUniqueCode> uniqueCodes) {
        List<WaveUniqueCode> sorted = uniqueCodes.stream().sorted(Comparator.comparing(WaveUniqueCode::getId)).collect(Collectors.toList());
        List<WaveItem> copies = Lists.newArrayListWithCapacity(uniqueCodes.size());
        for (WaveUniqueCode uniqueCode : sorted) {
            WaveItem copy = new WaveItem();
            BeanUtils.copyProperties(waveItem, copy);
            copy.setUniqueCode(uniqueCode.getUniqueCode());
            copy.setNum(1);
            copies.add(copy);
        }

        return copies;
    }

    private List<WaveUniqueCode> querySortedWaveUniqueCodes(Staff staff, WaveConfig waveConfig, List<Long> waveIds, List<Long> sids, List<Long> orderIds, Integer stockStatus) {
        if (!Objects.equal(waveConfig.getOpenWaveUniqueCode(), 1)) {
            return Collections.emptyList();
        }

        List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeService.queryByOrderIds(staff, Lists.newArrayList(waveIds), sids, orderIds);
        //波次编码打印都是未播的，所以要过滤下下已播的唯一码
        waveUniqueCodes = waveUniqueCodes.stream().filter(code -> code.getMatchedStatus() == WaveSorting.MATCHED_STATUS_NOT).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            return Collections.emptyList();
        }

        //有货无货需要进行一次排序
        if (stockStatus == null || stockStatus == 1 || stockStatus == 0) {
            waveUniqueCodes.sort(Comparator.comparing(WaveUniqueCode::getId));
        } else if (stockStatus == 2) {
            waveUniqueCodes.sort(Comparator.comparing(WaveUniqueCode::getId).reversed());
        }

        return waveUniqueCodes;
    }

    @Override
    public void printCallUnSeedItemPositions(Staff staff, List<Long> waveIds, List<String> outerIds, Integer isError) {
        List<WavePicking> pickings = wavePickingDao.getByWaveIds(staff, waveIds);
        if (pickings == null || pickings.size() == 0) {
            logger.warn(LogHelper.buildLog(staff, "根据waveIds找不到WavePicking，waveIds=%s" + waveIds));
            return;
        }
        List<Long> pickingIds = pickings.stream().map(WavePicking::getId).collect(Collectors.toList());
        List<WaveSorting> waveSortings = waveSortingDao.queryByPickingIds(staff, pickingIds);
        if (waveSortings == null || waveSortings.size() == 0) {
            logger.warn(LogHelper.buildLog(staff, "根据拣选pickingIds找不到WaveSorting，pickingIds=%s" + pickingIds));
            return;
        }
        Map<Long, Long> waveSortingIdWaveIdMap = new HashMap<>();
        List<Long> waveSortingIds = new ArrayList<>();
        for (WaveSorting waveSorting : waveSortings) {
            if (waveSorting.getWaveId() != null && waveSorting.getWaveId() > 0) {
                waveSortingIdWaveIdMap.put(waveSorting.getId(), waveSorting.getWaveId());
                waveSortingIds.add(waveSorting.getId());
            }
        }
        if (waveSortingIdWaveIdMap.size() == 0){
            return;
        }
        List<WaveSortingDetail> waveSortingDetails = waveSortingDao.queryDetailsBySortingIds(staff, waveSortingIds);
        if (waveSortingDetails == null || waveSortingDetails.size() == 0) {
            logger.warn(LogHelper.buildLog(staff, "根据waveSortingIds找不到WaveSortingDetail，waveSortingIds=%s" + waveSortingIds));
            return;
        }
        Map<Long, Set<String>> waveIdOurderIdsMap = new HashMap<>();
        for (WaveSortingDetail waveSortingDetail : waveSortingDetails) {
            String outerId = waveSortingDetail.getOuterId();
            if (StringUtils.isEmpty(outerId) || !outerIds.contains(outerId)) {
                continue;
            }
            Long waveId = waveSortingIdWaveIdMap.get(waveSortingDetail.getSortingId());
            if (waveId == null) {
                continue;
            }
            Set<String> outerIdSet = waveIdOurderIdsMap.computeIfAbsent(waveId, k -> new HashSet<>());
            outerIdSet.add(outerId);
        }
        List<WaveTrace> waveTraces = new ArrayList<>();
        for (Long waveId : waveIds) {
            Set<String> outerIdSet = waveIdOurderIdsMap.get(waveId);
            if (outerIdSet != null) {
                waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.POST_PRINT_UNSEED_ITEMS, ("打印商家编码，编码：" + outerIdSet), isError));
            }
        }
        waveTraceService.batchAddWaveTrace(staff, waveTraces);
    }

    /**
     * 根据分拣明细的商品id获取商品明细信息
     */
    private Map<String, DmjItem> getDetailsItemSkuMap(Staff staff, List<WaveSortingDetail> details) {
        return waveHelpBusiness.getDetailsItemSkuMap(staff, details);
    }

    public void updateOrderIdentCode(Staff staff, WavePickingParam param, Trade trade) {
        JSONObject outerIdJson = JSONObject.parseObject(param.getOuterCodes());
        HashMap<String, String> outerId2IdentCode = new HashMap<String, String>();
        for (String outerId : outerIdJson.keySet()) {
            JSONObject numAndIdentcode = outerIdJson.getJSONObject(outerId);
            String orderIdIdentCode = numAndIdentcode.getString("orderIdIdentCode");
            if (StringUtils.isNotEmpty(orderIdIdentCode)) {
                outerId2IdentCode.put(outerId, orderIdIdentCode);
            }
        }

        if (outerId2IdentCode.isEmpty()) {
            return;
        }

        logger.debug(LogHelper.buildLog(staff, "识别码信息：" + outerId2IdentCode));

        List<Order> orders = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(trade));
        List<Order> updates = Lists.newArrayListWithCapacity(orders.size());
        for (Order order : orders) {
            String orderIdIdentCode = outerId2IdentCode.get(order.getSysOuterId());
            if (StringUtils.isNotEmpty(orderIdIdentCode)) {
                Integer num = order.getNum();
                StringBuilder suitIdentCodeSb = new StringBuilder();
                String[] splitIdIdentCodes = StringUtils.split(orderIdIdentCode, ",");
                for (int i = 0; i < Math.min(splitIdIdentCodes.length, num); i++) {
                    suitIdentCodeSb.append(",").append(StringUtils.trimToEmpty(splitIdIdentCodes[i]));
                }
                order.setIdentCode(suitIdentCodeSb.substring(1));
                updates.add(buildUpdateOrder(order));
            }
        }
        if (!updates.isEmpty()) {
            tradeServiceDubbo.updateOrders4Wave(staff, null, updates);
        }
    }

    private Order buildUpdateOrder(Order order) {
        Order update = new TbOrder();
        update.setId(order.getId());
        update.setIdentCode(order.getIdentCode());
        return update;
    }

    @Override
    public WaveTradeSplitResult splitTrade(Staff staff, Long sid) {
        return waveSplitGenericService.splitTrade(staff, sid);
    }

    @Override
    public List<WaveTradeSplitResult> splitTrades(Staff staff, Long[] sids, ProgressData progressData) {
        return waveSplitGenericService.splitTrades(staff, sids, progressData);
    }

    @Override
    public List<WaveTradeSplitResult> splitMixTrades(Staff staff, Long[] sids, ProgressData progressData) {
        return waveSplitGenericService.splitMixTrades(staff, sids, progressData);
    }

    @Override
    public List<WaveTradeSplitResult> orderUniqueCodeSplitMixTrades(Staff staff, Long[] sids, ProgressData progressData, Map<Long, List<Order>> splitMap, boolean needUnAudit) {
        return waveSplitGenericService.orderUniqueCodeSplitMixTrades(staff, sids, progressData, splitMap, needUnAudit);
    }

    @Override
    public List<WaveTradeSplitResult> splitTradeByPost(Staff staff, Long[] sids, ProgressData progressData, Map<Long, List<Order>> splitMap) {
        return waveSplitGenericService.splitTradeByPost(staff, sids, progressData, splitMap);
    }

    @Override
    public WaveTradeSplitResult splitTrade(Staff staff, Long sid, List<Long> orderIds) {
        return waveSplitGenericService.splitTrade(staff, sid, orderIds);
    }

    public List<WaveSeedStat> statDetailSeedInfo(Staff staff, Long sortingId, List<Long> detailIds) {
        return waveSeedLogDao.statDetailSeedInfo(staff, sortingId, detailIds);
    }

    @Override
    public List<WaveSeedLog> queryWaveSeedLogList(Staff staff, WaveSeedLog waveSeedLog) {
        return waveSeedLogDao.queryWaveSeedLogList(staff, waveSeedLog);
    }

    @Override
    public Map<Long, List<Order>> queryDetailsBySidsAndPickingIds(Staff staff, List<Long> pickingIds, List<Long> sids) {
        Assert.notEmpty(pickingIds, "拣选Ids不能为空！");
        Assert.notEmpty(sids, "sid不能为空！");
        List<WaveSortingDetail> sortingDetails = waveSortingDao.queryDetailsBySidsAndPickingIds(staff, pickingIds, sids);
        if (CollectionUtils.isEmpty(sortingDetails)) {
            return Maps.newHashMap();
        }
        List<Order> orders = sortingDetails.stream().map(detail -> {
            Order order = new Order();
            order.setSkuSysId(detail.getSysSkuId());
            order.setItemSysId(detail.getSysItemId());
            order.setNum(detail.getItemNum());
            order.setSid(detail.getSid());
            return order;
        }).collect(Collectors.toList());

        return orders.stream().collect(Collectors.groupingBy(Order::getSid));
    }

    private Map<String, List<ItemSupplierBridge>> getSupplierInfo(Staff staff, List<WaveSortingDetail> waveSortingDetails) {
        if (CollectionUtils.isEmpty(waveSortingDetails)) {
            return Maps.newHashMap();
        }
        Map<String, List<ItemSupplierBridge>> map = Maps.newHashMap();

        for (List<WaveSortingDetail> details : Lists.partition(waveSortingDetails, 500)) {
            List<Long> itemIdList = new ArrayList<>();
            List<Long> skuIdList = new ArrayList<>();
            for (WaveSortingDetail wave : details) {
                if (wave.getSysSkuId() <= 0) {
                    itemIdList.add(wave.getSysItemId());
                } else {
                    skuIdList.add(wave.getSysSkuId());
                }
            }
            List<ItemSupplierBridge> itemSupplierBridges = itemServiceDubbo.queryItemSuppliers(staff, itemIdList, skuIdList);
            waveCodePrintBusiness.fillSupperInfo(itemSupplierBridges);
            map.putAll(itemSupplierBridges.stream().collect(Collectors.groupingBy(WmsKeyUtils::buildItemKey)));
        }
        return map;
    }

    @Override
    public Wave queryWaveById(Staff staff, Long waveId) {
        return waveDao.queryById(staff, waveId);
    }

    @Override
    public List<Wave> queryWaveByIds(Staff staff, Long... waveId) {
        return waveDao.queryByIds(staff, waveId);
    }

    @Override
    public List<StockOrderRecord> getStockOrderRecordList(Staff staff, Long... orderIds) {
        return stockOrderRecordDAO.queryByOrderIds(staff, orderIds);
    }

    /**
     * 判断staff是否有销售价权限
     */
    public static Boolean checkSalePower(Staff staff) {
        String powerDataPrivilegeSettings = staff.getPowerDataPrivilegeSettings();
        return powerDataPrivilegeSettings.contains(ITEM_SELLING_PRICE);
    }

    /**
     * 波次打印：商家编码打印，填充货位
     *
     * @param staff
     * @param pageList
     */
    private void assembleGoodsSectionCode(Staff staff, WavePageList<WaveItem> pageList) {
        if (pageList == null || CollectionUtils.isEmpty(pageList.getList()) || !WmsUtils.isOpenWms(staff)) {
            return;
        }
        List<Long> orderIds = pageList.getList().stream().map(WaveItem::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "波次商家编码打印：未获取到orderIds！"));
            }
            return;
        }
        List<GoodsSectionOrderRecord> gsCodeRecords = WmsUtils.isNewWms(staff)
                ? wmsService.queryGoodsSectionInfoByOrderIdsOfNewWms(staff, orderIds)
                : wmsService.queryGoodsSectionInfoByOrderIds(staff, orderIds);
        if (CollectionUtils.isEmpty(gsCodeRecords)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "波次商家编码打印：未获取货位锁定或配货记录！"));
            }
            return;
        }
        List<WaveItem> resultList = Lists.newArrayList();
        gsCodeRecords.sort(Comparator.comparing(GoodsSectionOrderRecord::getGoodsSectionCode));
        Map<String, List<GoodsSectionOrderRecord>> itemGsCodeMaps = gsCodeRecords.stream().collect(Collectors.groupingBy(g -> WmsKeyUtils.buildGoodsItemKey(g.getOrderId(), g.getSysItemId(), g.getSysSkuId())));
        for (WaveItem waveItem : pageList.getList()) {
            String waveItemKey = WmsKeyUtils.buildGoodsItemKey(waveItem.getOrderId(), waveItem.getSysItemId(), waveItem.getSysSkuId());
            resultList.addAll(buildWaveItemByGsCodes(waveItem, itemGsCodeMaps.get(waveItemKey)));
        }
        pageList.setTotal((long) resultList.size());
        if (pageList.getPage() != null) {
            pageList.setList(subListByPage(resultList, pageList.getPage()));
        } else {
            pageList.setList(resultList);
        }
    }

    private List<WaveItem> buildWaveItemByGsCodes(WaveItem waveItem, List<GoodsSectionOrderRecord> goodsSectionOrderRecords) {
        List<WaveItem> waveItems = Lists.newArrayList();
        if (CollectionUtils.isEmpty(goodsSectionOrderRecords)) {
            waveItems.add(waveItem);
            return waveItems;
        } else {
            for (GoodsSectionOrderRecord g : goodsSectionOrderRecords) {
                if (g.getApplyNum() == null || g.getApplyNum() <= 0) {
                    continue;
                }
                WaveItem copy = new WaveItem();
                BeanUtils.copyProperties(waveItem, copy);
                copy.setGoodsSectionCode(g.getGoodsSectionCode());
                copy.setGoodsSectionDisplay(g.getGoodsSectionDisplay());
                g.setApplyNum(g.getApplyNum() - 1);
                waveItems.add(copy);
                break;
            }
            if (CollectionUtils.isEmpty(waveItems)) {
                waveItems.add(waveItem);
            }
            return waveItems;
        }
    }

    /**
     * 查询合单主单订单
     */
    @Override
    public List<TbTrade> queryTradeBySidsForPrint(Staff staff, List<Long> sids, String fields) {
        List<String> fieldList = new ArrayList<String>();
        fieldList = (fields == null || fields.equals("")) ? new ArrayList<String>() : new ArrayList<>(Arrays.asList(fields.split(",")));
        fieldList.add("sid");
        fieldList.add("merge_sid");
        fieldList.add("merge_type");
        fieldList = new ArrayList<>(new LinkedHashSet<>(fieldList));
        List<TbTrade> trades = tbTradeDao.queryByKeys(staff, StringUtils.join(fieldList.toArray(), ","), "sid", sids.toArray(new Long[sids.size()]));
        sids = (trades.stream().filter(t -> printIsMerge(t)).map(Trade::getMergeSid).collect(Collectors.toList()));
        sids.addAll(trades.stream().filter(t -> !printIsMerge(t)).map(Trade::getSid).collect(Collectors.toList()));
        sids = new ArrayList<Long>(new LinkedHashSet<Long>(sids));
        return tbTradeDao.queryByKeys(staff, fields, "sid", sids.toArray(new Long[sids.size()]));
    }

    /**
     * 查询全部order信息
     * 主单+子单
     */
    @Override
    public List<Order> queryOrderBySidsForPrint(Staff staff, List<Long> sids, String fields) {
        List<TbTrade> trades = queryTradeBySidsForPrint(staff, sids, TRADE_FIELDS);
        List<TbTrade> mergeTrades = tbTradeDao.queryByKeys(staff, TRADE_FIELDS, "merge_sid", sids.toArray(new Long[sids.size()]));
        Map<Long, Long> mergeTradeMap = mergeTrades.stream().filter(t -> printIsMerge(t)).collect(Collectors.toMap(Trade::getSid, Trade::getMergeSid, (k1, k2) -> k2));
        trades.addAll(mergeTrades);
        List<Long> tSids = new ArrayList<Long>(new LinkedHashSet<Long>(trades.stream().map(Trade::getSid).collect(Collectors.toList())));
        List<Order> orders = OrderUtils.toTree(tbOrderDao.queryByKeys(staff, true, fields, "sid", tSids.toArray(new Long[tSids.size()])));
        for (Order order : orders) {
            order.setMergeSid(mergeTradeMap.containsKey(order.getSid()) ? mergeTradeMap.get(order.getSid()) : null);
            List<Order> orderSuits = order.getSuits() != null ? order.getSuits() : new ArrayList<>();
            for (Order suit : orderSuits) {
                suit.setMergeSid(mergeTradeMap.containsKey(suit.getSid()) ? mergeTradeMap.get(suit.getSid()) : null);
            }
        }
        return orders;
    }

    /**
     * 校验后置打印商品
     *
     * @param staff
     * @param param
     * @param isNull 商品是否为空
     */
    @Override
    public void checkPostPrintItem(Staff staff, WavePickingParam param, WavePickingScanResult result, boolean isNull) {
        if (!isNull) {
            return;
        }
        TradePostCheckItemResult checkItemResult = efficientWaveHandler.checkExistExceptTrade(staff, param, result);
        boolean efficientEngine = efficientWaveHandler.isEfficientEngine(param.getPickingCode());
        // 当出现商品不存在，匹配下波次是否有异常的订单，如果有给出订单的异常提示
        if (CollectionUtils.isNotEmpty(checkItemResult.getExceptTradeSidList())) {
            List<Long> sids = checkItemResult.getExceptTradeSidList();
            List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, true, sids.toArray(new Long[0]));
            checkTrades(staff, param, efficientEngine, trades);
        }
        // 校验交易关闭
        if (checkExistClosedTrade(staff, param, efficientEngine)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_SEED_TRADE_CLOSED_NOT_EXIST,
                    String.format("波次【%s】中没有该商品的订单交易关闭!",
                            WaveItemTraceLogHelper.getPickingCodeByMoveStockType(param)));
        }
        if (checkExistUnConsignedTrade(staff, param)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_COMB_NOT_FOUND,
                    String.format("%s【%s】中没有该商品的订单!",
                            efficientEngine ? "盲扫 " : "波次 ",
                            WaveItemTraceLogHelper.getPickingCodeByMoveStockType(param)));
        }
        throw new WaveScanException(WaveScanException.ERROR_CODE_ITEM_NOT_IN_TRADE, "系统中没有该商品订单！");
    }

    @Override
    public void checkTrades(Staff staff, WavePickingParam param, boolean efficientEngine, List<Trade> trades) {
        // 校验异常单
        doCheckExceptTrades(staff, param, efficientEngine, trades);
        // 校验已打印
        checkPrintTrades(trades);
        // 校验快递模板
        checkTemplate(param, trades);
        // 校验快递公司名称
        checkLogisticsCompanyId(param, trades);
        // 校验店铺
        checkUserId(param, trades);
    }

    private void checkUserId(WavePickingParam param, List<Trade> trades) {
        if (StringUtils.isNotEmpty(param.getAppointUserIds())) {
            Set<String> appointUserIdList = ArrayUtils.toStringSet(param.getAppointUserIds());
            if (trades.stream().anyMatch(trade -> !appointUserIdList.contains(trade.getUserId()))) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_COMMON, "订单店铺未匹配！");
            }
        }

        if (StringUtils.isNotEmpty(param.getExcludeUserIds())) {
            Set<String> excludeUserIdList = ArrayUtils.toStringSet(param.getExcludeUserIds());
            if (trades.stream().anyMatch(trade -> excludeUserIdList.contains(trade.getUserId()))) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_COMMON, "订单店铺未匹配！");
            }
        }
    }

    /**
     * 校验快递模板
     */
    private void checkTemplate(WavePickingParam param, List<Trade> trades) {
        if (StringUtils.isNotEmpty(param.getTemplateStr())) {
            Set<String> templateList = ArrayUtils.toStringSet(param.getTemplateStr());
            if (trades.stream().anyMatch(trade -> !templateList.contains(String.format("%s_%s", trade.getTemplateId(), trade.getTemplateType())))) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_PRINT_TEMPLATE, "订单快递模板未匹配");
            }
        }
        if (StringUtils.isNotEmpty(param.getNotInTemplateStr())) {
            Set<String> templateList = ArrayUtils.toStringSet(param.getNotInTemplateStr());
            if (trades.stream().anyMatch(trade -> templateList.contains(String.format("%s_%s", trade.getTemplateId(), trade.getTemplateType())))) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_PRINT_TEMPLATE, "订单快递模板未匹配");
            }
        }
    }

    private void checkLogisticsCompanyId(WavePickingParam param, List<Trade> trades) {
        if (StringUtils.isNotEmpty(param.getLogisticsCompanyIdStr())) {
            Set<Long> logisticsCompanyIdSet = ArrayUtils.toLongSet(param.getLogisticsCompanyIdStr());
            if (trades.stream().anyMatch(trade -> !logisticsCompanyIdSet.contains(trade.getLogisticsCompanyId()))) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_POST_PRINT_LOGISTICS_COMPANY, "订单快递公司名称未匹配");
            }
        }
        if (StringUtils.isNotEmpty(param.getNotInLogisticsCompanyIdStr())) {
            Set<Long> logisticsCompanyIdSet = ArrayUtils.toLongSet(param.getNotInLogisticsCompanyIdStr());
            if (trades.stream().anyMatch(trade -> logisticsCompanyIdSet.contains(trade.getLogisticsCompanyId()))) {
                throw new WaveScanException(WaveScanException.ERROR_CODE_POST_PRINT_LOGISTICS_COMPANY, "订单快递公司名称未匹配");
            }
        }
    }

    /**
     * 校验已打印
     */
    private void checkPrintTrades(List<Trade> trades) {
        if (trades.stream().anyMatch(trade -> TradeStatusUtils.getPrintStatus(trade.getExpressPrintTime()).equals(CommonConstants.JUDGE_YES))) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_PRINT_EXPRESS_ORDER, "快递面单已打印！");
        }
    }

    @Override
    public List<WaveTradeSplitResult> splitScanItem(Staff staff, WavePickingParam param) {
        Assert.isTrue(!efficientWaveHandler.isEfficientEngine(param.getPickingCode()), "盲扫不支持拆分");
        // 根据前端传入的参数，找到最优先匹配的订单
        fillResultWithParam(staff, tradeServiceDubbo.queryTradeConfig(staff), param, new WavePickingScanResult());
        WaveSorting waveSorting = waveSortingDao.queryOneUnPrintedByOuterCodes(staff, param, true);
        Assert.notNull(waveSorting, "未找到适合拆分的订单");
        waveHelpBusiness.checkTradeWaveSplit(staff,Collections.singletonList(waveSorting.getSid()));
        // 组装splitMap
        Map<Long, List<Order>> splitMap = buildSplitMap(staff, waveSorting, param);

        // 拆分，调用唯一码验货拆分
        TradeSplitWaveGenericParams genericParams = new TradeSplitWaveGenericParams();
        genericParams.setSidArr(new Long[]{waveSorting.getSid()});
        genericParams.setSplitType(4);
        genericParams.setProgressData(null);
        genericParams.setSplitMap(splitMap);
        genericParams.setPageSource(1);
        return waveSplitGenericService.tradeSplitWave(staff, genericParams);
    }

    private Map<Long, List<Order>> buildSplitMap(Staff staff, WaveSorting waveSorting, WavePickingParam param) {
        Assert.notNull(param.getOriginOuterCodes(), "拆分商品信息为空");
        Map<Long, List<Order>> splitMap = Maps.newHashMap();
        Map<String, Integer> outerIdNumMap = param.getOuterIdNumMap();
        List<WaveSortingDetail> waveSortingDetails = waveSortingDao.queryOrderIdsByOuterIds(staff, waveSorting.getId(), Lists.newArrayList(outerIdNumMap.keySet()));
        Map<String, List<WaveSortingDetail>> outerId2WaveSortingDetailMap = waveSortingDetails.stream().collect(Collectors.groupingBy(WaveSortingDetail::getOuterId));
        Assert.notEmpty(outerId2WaveSortingDetailMap, "拆分商品信息为空");
        List<Order> orderList = Lists.newArrayList();
        Long sid = waveSorting.getSid();
        for (Map.Entry<String, Integer> outerIdNumMapEntry : outerIdNumMap.entrySet()) {
            List<WaveSortingDetail> orderDetails = outerId2WaveSortingDetailMap.get(outerIdNumMapEntry.getKey());
            if(CollectionUtils.isEmpty(orderDetails)){
                logger.error(LogHelper.buildLog(staff,"拆分商品信息为空 outerId2WaveSortingDetailMap:"+ JSON.toJSONString(outerId2WaveSortingDetailMap
                                +" param:"+ JSON.toJSONString(param)
                        +" waveSorting:"+JSON.toJSONString(waveSorting))));
            }
            Assert.notNull(orderDetails, "拆分商品信息为空");
            int num = DataUtils.getZeroIfDefaultI(outerIdNumMapEntry.getValue());
            for (WaveSortingDetail detail : orderDetails) {
                if (num <= 0) {
                    break;
                }
                int divideNum = Math.min(DataUtils.getZeroIfDefaultI(detail.getItemNum()), num);
                Order order = new Order();
                order.setId(detail.getOrderId());
                order.setSplitNum(divideNum);
                orderList.add(order);
                num -= divideNum;
            }
        }
        splitMap.put(sid, orderList);
        return splitMap;
    }

    /**
     * 校验异常单
     */
    @Override
    public void checkExceptTrades(Staff staff, WavePickingParam param, boolean efficientEngine, List<Long> sids) {
        List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, true, sids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(trades)) {
            throw new WaveScanException(WaveScanException.ERROR_TRADE_EXCEPT, String.format("订单%s不存在", sids));
        }
        doCheckExceptTrades(staff, param, efficientEngine, trades);
    }

    private void doCheckExceptTrades(Staff staff, WavePickingParam param, boolean efficientEngine, List<Trade> trades) {
        List<Trade> exceptTrades = trades.stream()
                // 非缺货异常
                .filter(trade -> Objects.equal(trade.getIsExcep(), CommonConstants.JUDGE_YES)
                        && !Trade.STOCK_STATUS_INSUFFICIENT.equals(trade.getStockStatus()) && Objects.equal(trade.getInsufficientNum(), 0))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(exceptTrades)) {
            return;
        }
        Trade trade = exceptTrades.get(0);
        TradeExceptionUtils.analyze(staff, trade);
        String exceptionName = TradeExceptionStatusUtils.getExceptionToString(trade.getExceptions()) +
                (CollectionUtils.isNotEmpty(trade.getExceptNames()) ? " " + String.join(" ", trade.getExceptNames()) : "");
        throw new WaveScanException(WaveScanException.ERROR_TRADE_EXCEPT,
                String.format("%s【%s】中该商品的订单【%s】存在异常!【%s】",
                        efficientEngine ? "盲扫 " : "波次 ",
                        WaveItemTraceLogHelper.getPickingCodeByMoveStockType(param),
                        trade.getSid(),
                        exceptionName));
    }

    /**
     * 再次查看系统中是否存在包含这个商品的订单
     */
    private boolean checkExistUnConsignedTrade(Staff staff, WavePickingParam param) {
        if (param.getSysItemId() == null || param.getSysSkuId() == null) {
            return false;
        }
        List<Long> sids = tradeSearchService.querySidsBySysItemSkuId(staff, param.getSysItemId(), param.getSysSkuId(), new Page(1, 5), null);
        if (CollectionUtils.isNotEmpty(sids)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append("后置商品[").append(param.getOuterId()).append("]匹配到多个待发货订单,订单:").append(sids).append(",拣选号:").append(param.getPickingCode()));
            }
        }
        return CollectionUtils.isNotEmpty(sids);
    }

    /**
     * 校验波次中是否有交易关闭的订单
     */
    private boolean checkExistClosedTrade(Staff staff, WavePickingParam param, boolean efficientEngine) {
        return !efficientEngine && DataUtils.checkLongNotEmpty(waveTradeDao.queryClosedWaveTradeBySysItemSkuId(staff, param));
    }

    /**
     * 后置打印设置打印序号
     * @param trades
     * @param printData
     */
    public void setPrintNum(List<Trade> trades, List<Object> printData) {
        if (CollectionUtils.isEmpty(printData)) {
            return;
        }
        // 按订单顺序获取打印序号map
        AtomicInteger printNum = new AtomicInteger(1);
        // k -> outSid; v -> printNum
        Map<String, String> outSidAndPrintNumMap = trades.stream().collect(Collectors.toMap(Trade::getOutSid, t -> printNum.getAndIncrement() + "/" + trades.size()));

        // 根据运单号填充到打印JSON数据中
        for (Object printDatum : printData) {
            JSONObject printDataJson = (JSONObject) printDatum;
            if (printDataJson != null && printDataJson.get("fieldValues") != null) {
                JSONArray jsonArray = JSONArray.parseArray(JSONArray.toJSONString(printDataJson.get("fieldValues")));
                for (Object fieldValue : jsonArray) {
                    JSONObject jsonObject = (JSONObject) fieldValue;
                    if (jsonObject.get("outSid") != null) {
                        jsonObject.put("trade_print_num", outSidAndPrintNumMap.get(jsonObject.get("outSid").toString()));
                    }
                }
                printDataJson.put("fieldValues", jsonArray);
            }
        }
    }

    @Override
    public WavePickingScanResult postPrintCheck(Staff staff, WavePickingParam param) {
        WavePickingScanResult result = new WavePickingScanResult();
        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        fillResultWithParam(staff, tradeConfig, param, result);
        param.setPostPrintCheck(true);
        logger.debug(LogHelper.buildLog(staff, "波次：" + param.getWaveId() + " 后置打印扫描如下商品：" + param));
        return efficientWaveHandler.postPrintCheck(staff, param, result);
    }

    @Override
    public void fillSplitResult(Staff staff, List<WaveTradeSplitResult> results) {
        if (CollectionUtils.isEmpty(results)) {
            return;
        }

        List<WaveSorting> waveSortings = new ArrayList<>();
        for (WaveTradeSplitResult result : results) {
            if (BooleanUtils.isNotTrue(result.getSuccess())) {
                continue;
            }
            waveSortings.add(result.getOriginTrade());
            if (CollectionUtils.isNotEmpty(result.getSplitTrades())) {
                waveSortings.addAll(result.getSplitTrades());
            }
        }
        fillWaveSortingTradeInfo(staff, waveSortings, null);
    }
}

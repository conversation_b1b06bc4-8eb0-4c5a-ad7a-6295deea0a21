package com.raycloud.dmj.services.ec.backend;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.wave.*;
import com.raycloud.dmj.business.wave.strategy.WaveAutoCreateStrategyHelper;
import com.raycloud.dmj.dao.wave.WaveDao;
import com.raycloud.dmj.dao.wave.WaveQueryDao;
import com.raycloud.dmj.dao.wave.WaveRuleDao;
import com.raycloud.dmj.dao.wave.WaveRuleGroupDao;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.diamond.*;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.enums.WaybillGetOperationEnum;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.process.suggest.params.ProcessGenerateOrderWaveParam;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.LocalDateUtils;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.dto.WaveAutoCreateRuleDTO;
import com.raycloud.dmj.domain.wave.enums.AutoWaveTypeEnum;
import com.raycloud.dmj.domain.wave.model.WaveFilterParams;
import com.raycloud.dmj.domain.wave.model.WaveProgressContext;
import com.raycloud.dmj.domain.wave.model.*;
import com.raycloud.dmj.domain.wave.model.WaveProgressContext;
import com.raycloud.dmj.domain.wave.utils.WaveTraceUtils;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.exception.CustomException;
import com.raycloud.dmj.domain.wms.AllocateGoodsRecord;
import com.raycloud.dmj.domain.wms.QueryAllocateGoodsRecordParams;
import com.raycloud.dmj.domain.wms.WmsChangeAffect;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.dubbo.IUniqueCodeServiceDubbo;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.trades.ITradeWaveQueryService;
import com.raycloud.dmj.services.trades.IWaveTraceService;
import com.raycloud.dmj.services.trades.wave.IWaveServiceDubbo;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.services.wms.allocategoods.IAllocateGoodsService;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.web.utils.DateUtil;
import com.raycloud.ec.api.*;
import com.raycloud.ec.api.annotation.ListenerBind;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.raycloud.dmj.domain.wave.WaveCreateType.CHECKED_CROSS_TRADE;
import static com.raycloud.dmj.domain.wave.model.CrossBorderWaveConfig.*;

/**
 * Created by guzy on 17/6/13.
 */
@Component
@ListenerBind("trade.wave.create,trade.wave.save")
public class TradeWaveCreateListener implements IEventListener {

    private final Logger logger = Logger.getLogger(TradeWaveCreateListener.class);

    @Resource
    private IWarehouseService warehouseService;

    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;

    @Resource
    private IWaveServiceDubbo waveServiceDubbo;

    @Resource
    private WaveProgressBusiness waveProgressBusiness;

    @Resource
    private IOpLogService opLogService;

    @Resource
    private IWaveTraceService waveTraceService;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private WaveRuleDao waveRuleDao;

    @Resource
    private IStaffService staffService;

    @Resource
    private IUserService userService;

    @Resource
    private WaveGenerateBusiness waveGenerateBusiness;

    @Resource
    private WaveAllocateGoodsBusiness waveAllocateGoodsBusiness;

    @Resource
    private CheckedSkuWaveGenerateBusiness checkedSkuWaveGenerateBusiness;

    @Resource
    private WaveHelpBusiness waveHelpBusiness;

    @Resource
    private Configurable config;

    @Resource
    private WaveNotInReasonBusiness waveNotInReasonBusiness;
    @Resource
    private CheckedTradeWaveBusiness checkedTradeWaveBusiness;

    @Resource
    private ITradeWaveQueryService iTradeWaveQueryService;

    @Resource
    private WaveRuleGroupDao waveRuleGroupDao;

    @Resource
    private WaveAutoCreateStrategyHelper waveAutoCreateStrategyHelper;

    @Resource
    private WaveTradesQueryBusiness waveTradesQueryBusiness;

    @Resource
    private IUniqueCodeServiceDubbo uniqueCodeServiceDubbo;

    @Resource
    private IWmsService wmsService;

    @Resource
    private WaveDao waveDao;

    @Resource
    private WaveQueryDao waveQueryDao;

    @Resource
    ICache cache;

    /**
     * 默认过期时间  5分钟
     */
    private static final int DEFAULT_EXPIRE = 5 * 60;

    private static final String PRINT_STOCK_PROCESS_MATERIAL_STOCK_NOT_ENOUGH="PRINT_STOCK_PROCESS_MATERIAL_STOCK_NOT_ENOUGH";

    @Override
    public void onObserved(EventSourceBase eventSourceBase) {
        CommonEventSource source = (CommonEventSource) eventSourceBase;
        Staff staff = source.getArg(0, Staff.class);
        String eventId = eventSourceBase.getEventId();
        if (eventId != null) {
            String cacheKey = getKey(eventId);
            //使用缓存，防止重复消费  如果为false 报错
            try {
                Assert.isTrue(cache.add(cacheKey, eventId, DEFAULT_EXPIRE), "正在生成波次，请稍候操作！");
            } catch (CacheException e) {
                throw new RuntimeException(e);
            }
        }
        execute(staff, source);
    }

    public void execute(Staff staff, CommonEventSource source) {
        boolean isAuto = source.getEventName().contains("trade.wave.create");
        int autoCreateType = -1;
        Long manualCreateWarehouseId = null;
        if (isAuto) {
            //自动生成波次的时候，计划任务那边发过来的staff可能由于缺少字段引起异常，需要重新查询下staff
            staff = getNewStaff(staff);
            autoCreateType = waveAutoCreateStrategyHelper.canAutoCreate(staff);
            if (autoCreateType == AutoWaveTypeEnum.CloseAuto.getType()) {
                logger.debug(LogHelper.buildLog(staff, "不自动生成波次"));
                return;
            }
        }

        long start = System.currentTimeMillis();
        List<String> errorMsg = null;
        List<Long> checkedSids = new ArrayList<>();
        WaveFilterParams filterParams = source.getResult(WaveFilterParams.class);
        PdaScanWaveConfig pdaScanFlag=null;
        try {
            if (isAuto) {
                List<WaveRule> rules = queryRulesFilterWaveGroup(staff, null);
                if (autoCreateType == AutoWaveTypeEnum.TimedAuto.getType() || AutoWaveTypeEnum.HourMinuteAuto.getType() == autoCreateType) {
                    autoCreateWaves(staff, rules, autoCreateType);
                } else if (autoCreateType == AutoWaveTypeEnum.LaborAuto.getType()) {
                    autoCreateByLabor(staff, rules);
                }
            } else if (filterParams != null && filterParams.getWaveCreateType() == WaveCreateType.CHECKED_SKU) {
                checkedSkuGenerateWave(staff, filterParams);
            } else if (filterParams != null && (Objects.equal(filterParams.getWaveCreateType(), WaveCreateType.CHECKED_TRADE)
                            ||Objects.equal(filterParams.getWaveCreateType(), CHECKED_CROSS_TRADE))) {
                checkedTradeGenerateWave(staff, filterParams);
                checkedSids.addAll(filterParams.getSids());
            }else {
                Long ruleId = source.getArg(1, Long.class);
                Long warehouseId = source.getArg(2, Long.class);
                List<Integer> indexes = source.getArgList(3, Integer.class);
                String ip = source.getArg(4, String.class);
                Long ruleGroupId = source.getArg(5, Long.class);
                String waveRemark = source.getArg(6, String.class);
                DateRange payTimeRange = source.getArg(7, DateRange.class);
                List<Long>ruleIds=source.getArgList(8,Long.class);
                PdaScanWaveConfig pdaScanWaveConfig = source.getArg(9, PdaScanWaveConfig.class);
                pdaScanFlag=pdaScanWaveConfig;
                manualCreateWarehouseId = warehouseId;
                manualCreateWaves(staff, ruleId, ruleGroupId, warehouseId, indexes, ip, waveRemark, payTimeRange,ruleIds,pdaScanWaveConfig);
                eventCenter.fireEvent(this, new EventInfo("wave.reason.supplement").setArgs(new Object[]{staff, warehouseId}), null);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, isAuto + "生成波次失败！"), e);
            errorMsg = Lists.newArrayList(e.getMessage());
        } finally {
            // 生成波次控制的维度为仓库级别，自动生成波次是全仓执行，不在这里处理进度
            if (!isAuto) {
                try{
                ProgressData progressData = waveProgressBusiness.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_WAVE_GENERATE, WaveProgressContext.build(manualCreateWarehouseId == null ? filterParams.getWarehouseId() : manualCreateWarehouseId));
                progressData.setProgress(2);
                progressData.setCountCurrent(progressData.getCountAll());
                if (errorMsg != null) {
                    progressData.setErrorMsg(errorMsg);
                }
                fillCheckedResult(staff, checkedSids, progressData, filterParams,errorMsg,pdaScanFlag);
                waveProgressBusiness.setProgress(staff, ProgressEnum.PROGRESS_TRADE_WAVE_GENERATE, progressData, WaveProgressContext.build(manualCreateWarehouseId == null ? filterParams.getWarehouseId() : manualCreateWarehouseId));
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("异步生成波次结束：took=%s ms", (System.currentTimeMillis() - start))));
                }
                }catch (Exception e){
                    logger.error(LogHelper.buildErrorLog(staff,e,"异步生成波次 处理finally 失败"));
                }finally {
                    WaveUtils.PDA_SCAN_WAVE_ASSIGN_WAVE.remove();
                }
            }
        }
    }

    private void fillCheckedResult(Staff staff, List<Long> checkedSids, ProgressData progressData, WaveFilterParams filterParams, List<String> errorMsg, PdaScanWaveConfig pdaScanFlag) {
        if (CollectionUtils.isNotEmpty(checkedSids)) {
            List<Trade> trades = tradeServiceDubbo.queryTradeBySids(staff, false, checkedSids.toArray(new Long[0]));
            if (CollectionUtils.isEmpty(trades)) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, "勾选订单生成波次填充结果：未查询到订单！"));
                }
            } else {
                Boolean generatePickWave = filterParams.getCrossBorderGeneratePickWave();
                List<Trade> unGenerates = trades.stream().filter(t -> !DataUtils.checkLongNotEmpty(t.getWaveId())).collect(Collectors.toList());
                Map<String, String> checkedResult = Maps.newHashMap();
                checkedResult.put("warehouseId", String.valueOf(filterParams.getWarehouseId()));
                checkedResult.put("generatedNum", Boolean.FALSE.equals(generatePickWave)?"0":String.valueOf(checkedSids.size() - unGenerates.size()));
                checkedResult.put("unGenerateNum", Boolean.FALSE.equals(generatePickWave)?"0":String.valueOf(unGenerates.size()));
                if(Objects.equal(filterParams.getWaveCreateType(), CHECKED_CROSS_TRADE)&&CollectionUtils.isNotEmpty(errorMsg)){
                    checkedResult.put("errorMsg",StringUtils.join(errorMsg, ","));
                }
                if (CollectionUtils.isNotEmpty(unGenerates)) {
                    checkedResult.put("unGenerateSids", StringUtils.substring(Strings.join(",", unGenerates.stream().map(Trade::getSid).collect(Collectors.toList())), 0, 30000));
                }
                progressData.setExecutResult(checkedResult);
            }
        }
        //如果pda扫描生成波次，需要返回生成的波次数量，判断是否成功
        if(pdaScanFlag!=null){
            Map<String, Object> checkedResult = Maps.newHashMap();
            Wave wave = WaveUtils.PDA_SCAN_WAVE_ASSIGN_WAVE.get();
            if(wave!=null){
                checkedResult.put("wave", wave);
                checkedResult.put("pdaScanWaveGenerateSuccess", true);
            }else {
                checkedResult.put("pdaScanWaveGenerateSuccess", false);
            }
            progressData.setExecutResult(checkedResult);
        }

    }

    private void updateLastAutoCreateWaveTime(Staff staff) {
        TradeConfig update = new TradeConfig();
        update.setCompanyId(staff.getCompanyId());
        update.setLastAutoCreateWaveTime(new Date());
        tradeServiceDubbo.updateTradeConfig(staff, update);
    }

    private Staff getNewStaff(Staff staff) {
        Staff staffNew = staffService.queryDefaultStaffByCompanyId(staff.getCompanyId());
        if (staffNew != null) {
            List<User> userList = userService.queryByCompanyId(staff.getCompanyId());
            Map<Long, User> userMap = new HashMap<>(userList.size(), 1);
            for (User user : userList) {
                user.setStaff(staffNew);
                staffNew.getUsers().add(user);
                userMap.put(user.getId(), user);
            }
            staffNew.setUserIdMap(userMap);
        }

        if (staffNew == null) {
            logger.error(LogHelper.buildLog(staff, String.format("根据companyId=%s没有查询到staff,忽略该公司波次生成", staff.getCompanyId())));
            return staff;
        }

        staffNew.setClueId(staff.getClueId());
        return staffNew;
    }

    /**
     * 自动生成波次需要进度控制并发
     */
    public void autoCreateWaves(Staff staff, List<WaveRule> rules, int autoCreateType) {
        // TRACE参数组装
        String[] params = new String[]{
                JSONObject.toJSONString(rules.stream().map(WaveRule::getId).collect(Collectors.toList()))
        };

        List<Warehouse> warehouses = warehouseService.queryAll(staff, 1);
        rules = CollectionUtils.isEmpty(rules) ? rules : rules.stream().filter(rule -> BooleanUtils.isTrue(rule.getOpenAutoCreate())).collect(Collectors.toList());
        if (warehouses == null || warehouses.size() == 0 || rules == null || rules.size() == 0) {
            return;
        }
        rules = getAutoWaveRulesByAutoWaveType(staff, rules, autoCreateType);
        if (CollectionUtils.isEmpty(rules)) {
            logger.debug(LogHelper.buildLog(staff, "没有符合自动生成波次的规则"));
            return;
        }
        rules.forEach(rule -> rule.setCreateType(WaveCreateType.AUTO));
        Set<Long> warehouseIdList = new HashSet<>();
        for (Warehouse warehouse : warehouses) {
            if (!Objects.equal(Warehouse.TYPE_OWN, warehouse.getType()) || !waveHelpBusiness.lockBeforeGenerate(staff, warehouse.getId())) {
                continue;
            }
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("autoCreateWaves[warehouseId:%d]", warehouse.getId())));
            }
            try {
                generateWave(staff, warehouse.getId(), rules, null, WaveCreateType.AUTO, null, null, null, autoCreateType,null);
                warehouseIdList.add(warehouse.getId());
            } finally {
                autoCreateFinishProgress(staff, warehouse);
            }
        }
        waveHelpBusiness.deleteWaveRuleCache(staff, null, Lists.newArrayList(warehouseIdList),null);
    }

    private void autoCreateFinishProgress(Staff staff, Warehouse warehouse) {
        ProgressData progressData = waveProgressBusiness.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_WAVE_GENERATE, WaveProgressContext.build(warehouse.getId()));
        progressData.setProgress(2);
        progressData.setCountCurrent(progressData.getCountAll());
        waveProgressBusiness.setProgress(staff, ProgressEnum.PROGRESS_TRADE_WAVE_GENERATE, progressData, WaveProgressContext.build(warehouse.getId()));
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "仓库：" + warehouse.getName() + " 自动生成波次结束"));
        }
    }

    private List<WaveRule> getAutoWaveRulesByAutoWaveType(Staff staff, List<WaveRule> rules, int autoCreateType) {
        Map<Long, List<WaveRule>> ruleGroupId2RuleListMap = rules.stream().collect(Collectors.groupingBy(WaveRule::getWaveRuleGroupId));
        List<Long> waveRuleGroupId = Lists.newArrayList(ruleGroupId2RuleListMap.keySet());
        List<WaveAutoCreateRuleDTO> dtos = waveServiceDubbo.getWaveAutoCreateRuleByGroupIds(staff, waveRuleGroupId, autoCreateType);
        Map<Long, WaveAutoCreateRuleDTO> groupId2RuleMap = dtos.stream().collect(Collectors.toMap(WaveAutoCreateRuleDTO::getSourceWaveGroupId, Function.identity()));
        List<WaveRule> filterRules = Lists.newArrayList();
        List<Long> logRuleGroupIds = Lists.newArrayList();
        for (Map.Entry<Long, List<WaveRule>> ruleGroupId2Rule : ruleGroupId2RuleListMap.entrySet()) {
            Long ruleGroupId = ruleGroupId2Rule.getKey();
            WaveAutoCreateRuleDTO waveAutoCreateRuleDTO = groupId2RuleMap.getOrDefault(ruleGroupId, new WaveAutoCreateRuleDTO());
            WaveAutoCreateRule autoCreateRule = waveAutoCreateRuleDTO.getRule();
            if (waveAutoCreateRuleDTO.getId() == null || autoCreateRule == null) {
                logger.debug(LogHelper.buildLog(staff, String.format("未找到该波次分组【%s】的自动生成波次规则信息", ruleGroupId)));
                continue;
            }
            // 定时
            if (autoCreateType == AutoWaveTypeEnum.TimedAuto.getType()) {
                if (!waveHelpBusiness.greaterThanInterval(staff, autoCreateRule)) {
                    logRuleGroupIds.add(ruleGroupId);
                    continue;
                }
                filterRules.addAll(ruleGroupId2Rule.getValue());
                // 更新最后一次自动生成波次时间
                autoCreateRule.setLastAutoCreateWaveTime(new Date());
                waveServiceDubbo.saveWaveAutoCreateRule(staff, waveAutoCreateRuleDTO);
            } else if (autoCreateType == AutoWaveTypeEnum.HourMinuteAuto.getType()) {
                // 定点
                String waveAutoCreateTime = autoCreateRule.getWaveAutoCreateTime();
                if (StringUtils.isNotEmpty(waveAutoCreateTime)) {
                    List<String> times = ArrayUtils.toStringList(waveAutoCreateTime);
                    Date now = new Date();
                    int hour = DateUtil.getHour(now);
                    int minute = DateUtil.getMinute(now);
                    for (String time : times) {
                        if (StringUtils.isEmpty(time) || !time.contains(":")) {
                            continue;
                        }
                        String[] hourMinute = time.split(":");
                        if (hour == Integer.parseInt(hourMinute[0]) && minute == Integer.parseInt(hourMinute[1])) {
                            filterRules.addAll(ruleGroupId2Rule.getValue());
                            logger.debug(LogHelper.buildLog(staff, String.format("波次分组【%s】符合定点自动生成波次时间，自动生成波次", ruleGroupId)));
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(logRuleGroupIds)) {
            logger.debug(LogHelper.buildLog(staff, String.format("波次分组%s上次自动生成波次时间小于间隔！", logRuleGroupIds)));
        }
        return filterRules.stream().sorted(Comparator.comparing(WaveRule::getSort, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
    }

    /**
     * 自动生成波次需要进度控制并发
     */
    public void autoCreateWavesByLabor(Staff staff, Long warehouseId, List<WaveRule> rules) {
        // TRACE参数组装
        String[] params = new String[]{
                JSONObject.toJSONString(rules.stream().map(WaveRule::getId).collect(Collectors.toList()))
        };

        rules.forEach(rule -> rule.setCreateType(WaveCreateType.AUTO));
        Set<Long> warehouseIdList = new HashSet<>();
        warehouseIdList.add(warehouseId);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("autoCreateWavesByLabor[warehouseId:%d]", warehouseId)));
        }
        generateWave(staff, warehouseId, rules, null, WaveCreateType.AUTO, null, null, null, AutoWaveTypeEnum.LaborAuto.getType(),null);
        warehouseIdList.add(warehouseId);
        waveHelpBusiness.deleteWaveRuleCache(staff, null, Lists.newArrayList(warehouseIdList),null);
    }

    private List<WaveRule> queryRules(Staff staff, WaveRule waveRuleQuery) {
        List<WaveRule> waveRules = waveRuleDao.queryList(staff, waveRuleQuery);
        int i = 1;
        for (WaveRule waveRule : waveRules) {
            waveRule.setSort(i++);
            if (waveRule.getRuleCondition() != null) {
                waveRule.getRuleCondition().setWaveRuleId(waveRule.getId());
            }
        }
        return waveRules;
    }

    public List<WaveRule> queryRulesFilterWaveGroup(Staff staff, WaveRule waveRuleQuery) {
        List<WaveRule> waveRules = queryRules(staff,waveRuleQuery);
        List<Long> filterWaveGroupId = staffService.queryWaveGroupByStaff(staff);
        if(filterWaveGroupId == null){
            return waveRules;
        }
        if (logger.isDebugEnabled()&&CollectionUtils.isNotEmpty(filterWaveGroupId)) {
            logger.debug(LogHelper.buildLog(staff, String.format("员工【%s】,过滤的WaveGroup【%s】", staff.getId(),filterWaveGroupId)));
        }
        return waveRules.stream().filter(waveRule -> !filterWaveGroupId.contains(waveRule.getWaveRuleGroupId())).collect(Collectors.toList());
    }

    /**
     * 手动生成波次
     */
    private void manualCreateWaves(Staff staff, Long ruleId, Long ruleGroupId, Long warehouseId, List<Integer> indexes, String ip, String waveRemark, DateRange payTimeRange,List<Long> ruleIds,PdaScanWaveConfig pdaScanWaveConfig) {
        WaveCreateType waveCreateType = WaveCreateType.MANUAL;
        List<WaveRule> rules = null;
        List<Long>ruleGroupIds=Lists.newArrayList();
        if (ruleId != null && ruleId == 0L) {
            WaveRule waveRuleQuery = new WaveRule();
            waveRuleQuery.setWaveRuleGroupId(ruleGroupId);
            rules = queryRulesFilterWaveGroup(staff, waveRuleQuery);
            if (CollectionUtils.isNotEmpty(rules)) {
                rules = rules.stream().filter(rule -> BooleanUtils.isTrue(rule.getOpenBatchCreate())).peek(rule -> rule.setCreateType(WaveCreateType.BATCH)).collect(Collectors.toList());
            }
            waveCreateType = WaveCreateType.BATCH;
            // 标记最后一个波次规则
            if (CollectionUtils.isNotEmpty(rules)) {
                WaveRule lastWaveRule = rules.get(rules.size() - 1);
                lastWaveRule.setBatchGenerateLast(true);
            }
        } else if(CollectionUtils.isNotEmpty(ruleIds)){
            //有一种case，只有传ruleIds 不传ruleId
            List<WaveRule> waveRules=waveRuleDao.queryByIds(staff,ruleIds);
            waveRules=waveRules.stream().peek(waveRule->{
                if(waveRule.getSort()==null){
                    waveRule.setSort(Integer.MAX_VALUE);
                }
            }).sorted(Comparator.comparingInt(WaveRule::getSort)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(waveRules)){
                rules = waveRules.stream().filter(rule -> BooleanUtils.isTrue(rule.getOpenBatchCreate())).peek(rule -> rule.setCreateType(WaveCreateType.BATCH)).collect(Collectors.toList());
                waveCreateType = WaveCreateType.BATCH;
                // 标记最后一个波次规则
                if (CollectionUtils.isNotEmpty(rules)) {
                    WaveRule lastWaveRule = rules.get(rules.size() - 1);
                    lastWaveRule.setBatchGenerateLast(true);
                }
                ruleGroupIds=rules.stream().map(WaveRule::getWaveRuleGroupId).filter(java.util.Objects::nonNull).distinct().collect(Collectors.toList());
            }
            //扫码生成波次场景
            if(pdaScanWaveConfig!=null){
                waveCreateType = WaveCreateType.PDA_SCAN;
                //查询当前规则下所有待拣货状态的波次
                List<Wave> waveList = Optional.ofNullable(waveDao.queryWaitPickWaves(staff,ruleIds,warehouseId)).orElse(new ArrayList<>());
                Integer minGenerateWaveNum = pdaScanWaveConfig.getMinGenerateWaveNum();
                //当最小触发数设置为0的时候。只领取波次，不生成波次
                if(minGenerateWaveNum==0){
                    if(!waveList.isEmpty()){
                        //随机分配一个任务
                        pdaScanAssignPickerByWave(waveList,staff,ruleIds,warehouseId);
                        return;
                    }else {
                        logger.info(LogHelper.buildLog(staff,"pda扫码生成波次，最小触发数为0，并且任务数为0,ruleGroupId"+ruleGroupId));
                        return;
                    }
                }
                int waitAssignSize = waveList.size();
                if(waitAssignSize==0){
                    //走生成波次,波次生成后分配任务
                    pdaScanWaveConfig.setAssignPickerAfterGenerateWave(true);
                } else if(waitAssignSize-1<minGenerateWaveNum) {
                    //分配任务
                    pdaScanAssignPickerByWave(waveList,staff,ruleIds,warehouseId);
                    //走生成波次
                }else if(waitAssignSize-1>=minGenerateWaveNum){
                    //分配任务
                    pdaScanAssignPickerByWave(waveList,staff,ruleIds,warehouseId);
                    //无需生成波次
                    return;
                }
            }
        }else{
            WaveRule rule = waveRuleDao.queryById(staff, ruleId);
            if (rule != null) {
                rule.setCreateType(WaveCreateType.MANUAL);
                rules = new ArrayList<>();
                rules.add(rule);
            }
        }
        if (CollectionUtils.isEmpty(rules)) {
            logger.warn(LogHelper.buildLog(staff, String.format("波次规则不存在，ruleId=%s,ruleIds", ruleId)));
            return;
        }
        generateWave(staff, warehouseId, rules, indexes, waveCreateType, ip, waveRemark, payTimeRange,pdaScanWaveConfig);
        //按照ruleGroupId 清理缓存，2中case 1.只有一个gruop 2.前端传ruleIds 要计算出对应的groupIds，有可能>=1 个
        waveHelpBusiness.deleteWaveRuleCache(staff, ruleGroupId, Lists.newArrayList(warehouseId),ruleGroupIds);
    }


    private void pdaScanAssignPicker(List<Long> waveIds, Staff operator,List<Long>ruleIds,Long warehouseId) {
        if(operator==null){
            throw new IllegalArgumentException("当前登陆人为空");
        }
        if(CollectionUtils.isEmpty(waveIds)){
            return;
        }
        Long assignWaveId;
        List<Long> waitWaveIds = waveDao.queryOperatorWaitPickWave(operator, ruleIds, warehouseId);
        if(CollectionUtils.isNotEmpty(waitWaveIds)){
            //优先分配上个自己未完成的任务
            logger.info(LogHelper.buildLog(operator,"pda扫码领取波次，还有未完成拣选的任务，不重新领取,waveIds="+ JSONObject.toJSONString(waitWaveIds)));
            assignWaveId=waitWaveIds.get(0);
        }else {
            int random = RandomUtils.nextInt(0, waveIds.size());
            assignWaveId = waveIds.get(random);
            waveHelpBusiness.pdaScanAssignPicker(operator,assignWaveId);
        }
        //设置领取的波次号，返回给调用方
        Wave wave = waveDao.queryById(operator, assignWaveId);
        WaveUtils.PDA_SCAN_WAVE_ASSIGN_WAVE.set(WaveUtils.getSimplePdaScanWave(wave,operator));
    }

    private void pdaScanAssignPickerByWave(List<Wave> waveList, Staff operator,List<Long> ruleIds,Long warehouseId) {
        if(operator==null){
            throw new IllegalArgumentException("当前登陆人为空");
        }
        if(CollectionUtils.isEmpty(waveList)){
            return;
        }
        //加急波次id信息列表
        List<Long> urgentWaveIdList = waveList.stream().filter(wave -> ArrayUtils.toLongList(wave.getTagIds()).contains(WaveTagEnum.URGENT.getId())).map(Wave::getId).collect(Collectors.toList());
        Long assignWaveId;
        List<Long> waitWaveIds = waveDao.queryOperatorWaitPickWave(operator, ruleIds, warehouseId);
        if(CollectionUtils.isNotEmpty(waitWaveIds)){
            //优先分配上个自己未完成的任务
            logger.info(LogHelper.buildLog(operator,"pda扫码领取波次，还有未完成拣选的任务，不重新领取,waveIds="+ JSONObject.toJSONString(waitWaveIds)));
            assignWaveId=waitWaveIds.get(0);
        }else {
            if (CollectionUtils.isNotEmpty(urgentWaveIdList)){}

            if (CollectionUtils.isNotEmpty(urgentWaveIdList)){
                int random = RandomUtils.nextInt(0, urgentWaveIdList.size());
                assignWaveId = urgentWaveIdList.get(random);
                waveHelpBusiness.pdaScanAssignPicker(operator,assignWaveId);
            }else {
                int random = RandomUtils.nextInt(0, waveList.size());
                assignWaveId = waveList.get(random).getId();
                waveHelpBusiness.pdaScanAssignPicker(operator,assignWaveId);
            }
        }
        //设置领取的波次号，返回给调用方
        Wave wave = waveDao.queryById(operator, assignWaveId);
        WaveUtils.PDA_SCAN_WAVE_ASSIGN_WAVE.set(WaveUtils.getSimplePdaScanWave(wave,operator));
    }




    private void checkedSkuGenerateWave(Staff staff, WaveFilterParams params) {
        WaveRule rule = checkedSkuWaveGenerateBusiness.buildWaveRule(staff, params, tradeServiceDubbo.queryTradeStaffConfig(staff));
        WaveGenerateBusiness.GenerateWaveResult result = waveGenerateBusiness.generateWave(staff, Collections.singletonList(rule), params);
        addWaveTraceAndGetWaybillCode(staff, result.getWaves(), params.getWaveCreateType(), params.getIp());
        waveHelpBusiness.deleteWaveRuleCache(staff, null, Lists.newArrayList(params.getWarehouseId()),null);
    }


    private void checkedTradeGenerateWave(Staff staff, WaveFilterParams params) {
        WaveRule rule = new WaveRule();
        WaveRuleCondition condition = new WaveRuleCondition();
        String ruleName = WaveRuleType.CHECKED_TRADE.getName();
        if (params.getRuleId() != null) {
            WaveTradeCondition waveTradeCondition = checkedTradeWaveBusiness.queryWaveQueryConditionById(staff, params.getRuleId());
            condition.setPickEndAutoConsign(waveTradeCondition.getPickEndAutoConsign());
            condition.setPaperPick(waveTradeCondition.getPaperPick());
            condition.setAllowUnderstocked(waveTradeCondition.isAllowUnderstocked());
            condition.setAllowUnderstockedRange(waveTradeCondition.getAllowUnderstockedRange());
            condition.setSortRuleType(waveTradeCondition.getSortRuleType());
            rule.setPickerId(waveTradeCondition.getPickerId());
            ruleName = BooleanUtils.isTrue(params.getHasFilter()) ? ruleName + "(" + waveTradeCondition.getConditionName() + ")" : ruleName;
        }
        Long ruleId = WaveRuleType.CHECKED_TRADE.getRuleId();
        rule.setId(ruleId);
        rule.setName(ruleName);
        rule.setRuleCondition(condition);
        rule.setNumUp(params.getTradeNumUp());
        // 订单管理生成波次填充sid
        if (params.getTradeQueryParams() != null) {
            fillSidsByTradeQueryParams(staff, params);
        }
        if(CHECKED_CROSS_TRADE ==params.getWaveCreateType()){
            //判断订单来源
            rule.setName(params.getWaveRuleType().getName());
            rule.setId(params.getWaveRuleType().getRuleId());
            rule.getRuleCondition().setInsufficientAllWaveNoAllocateRecord(false);
            CrossBorderWaveConfig crossborderWaveConfig = params.getCrossborderWaveConfig();
            if(CROSS_WAVE_TYPE.equals(crossborderWaveConfig.getWaveType())){
                //允许缺货生成波次
                rule.getRuleCondition().setAllowUnderstocked(true);
                checkCrossWaveTradeGenerateWaveV2(staff, params, crossborderWaveConfig, rule);
            }else {
                //正常波次不允许缺货生成
                doCheckedTradeGenerateWave(staff, params, rule);
            }
        }else {
            doCheckedTradeGenerateWave(staff, params, rule);
        }
    }

    private void addTradeCrossTag(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        try {
            for (List<Long> subSids : Lists.partition(sids, 500)) {
                logger.info(LogHelper.buildLog(staff, "生成跨界波次，给订单打标签，sid：" + subSids));
                tradeServiceDubbo.saveLabels(staff, subSids, Lists.newArrayList(SystemTags.TAG_CROSS_BORDER_PRINT_WAVES), true);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("生成跨界波次，给订单打标签失败，原因:").append(e.getMessage()), e);
        }
    }

    private void checkCrossWaveTradeGenerateWaveV2(Staff staff, WaveFilterParams params, CrossBorderWaveConfig crossborderWaveConfig, WaveRule rule) {
        Integer stockOutType = crossborderWaveConfig.getStockOutType();
        List<Long> sids = params.getSids();

        //设置波次批次分组号
        String lastUnderstockedGroup = waveQueryDao.queryLastUnderstockedGroup(staff, params.getWarehouseId(), new Date()); // 查询获取当天最新的缺货分组
        lastUnderstockedGroup = waveHelpBusiness.getUnderstockedGroup(lastUnderstockedGroup);
        params.setCrossBorderWaveUnderstockedGroup(lastUnderstockedGroup);

        CrossBoardTradeWaveGroup crossBoardTradeWaveGroup=new CrossBoardTradeWaveGroup();
        //按照缺货情况才生成拣货波次
        Set<Long>normalWaveIds=new HashSet<>();
        Set<Long> waveIds = new HashSet<>();
        if(SITUATION_STOCK_TOUT_TYPE.equals(stockOutType)){
            crossBoardTradeWaveGroup = dealAcrossLackCondition(staff, params, crossborderWaveConfig, rule);
            waveIds = crossBoardTradeWaveGroup.getWaveIds();
            normalWaveIds.addAll(waveIds);
        }else {
            logger.debug(LogHelper.buildLog(staff,"跨境订单生成波次，全部生成加工单,sids:"+ com.github.ltsopensource.core.json.JSON.toJSONString(params.getSids())));
            crossBoardTradeWaveGroup=waveTradesQueryBusiness.calculateAcrossBorderTradeStock(staff, params,crossborderWaveConfig);
            crossBoardTradeWaveGroup.setGenerateStockProductOrder(true);
        }
        //生成唯一码
        UniqueCodeGenericParams uniqueCodeGenericParams = crossBoardTradeWaveGroup.getUniqueCodeGenericParams();
        uniqueCodeGenericParams.setGenerateType(crossborderWaveConfig.getGenerateType());
        UniqueCodeResult uniqueCodeResult = uniqueCodeServiceDubbo.generateCode(staff, uniqueCodeGenericParams);
        boolean generateStockProductOrderFlag = crossBoardTradeWaveGroup.isGenerateStockProductOrder();
        if(uniqueCodeResult==null||!BooleanUtils.isTrue(uniqueCodeResult.isSuccess())){
            String errorMsg = "跨境生成唯一码失败";
            if(uniqueCodeResult!=null){
                List<UniqueCodeResult.FailCodeInfo> failCodeInfos = uniqueCodeResult.getFailCodeInfos();
                if(CollectionUtils.isNotEmpty(failCodeInfos)){
                    errorMsg=failCodeInfos.get(0).getFailMsg();
                }else if(StringUtils.isNotEmpty(uniqueCodeResult.getErrorMsg())){
                    errorMsg=uniqueCodeResult.getErrorMsg();
                }
            }
            logger.error(LogHelper.buildLog(staff,"跨境生成唯一码失败 uniqueCodeResult:"+ JSON.toJSONString(uniqueCodeResult)));
            throw new RuntimeException(errorMsg);
        }

        if (CollectionUtils.isNotEmpty(uniqueCodeResult.getExtMsgInfos()) && CollectionUtils.isNotEmpty(waveIds)) {
            Iterator<Long> iterator = waveIds.iterator();
            Map<Long, String> msgMap = uniqueCodeResult.getExtMsgInfos().stream().collect(Collectors.toMap(UniqueCodeResult.ExtMsgInfo::getBusinessId, UniqueCodeResult.ExtMsgInfo::getMsg, (a, b) -> a));
            List<Wave> waves = Lists.newArrayList();
            while (iterator.hasNext()) {
                Long waveId = iterator.next();
                String customerName;
                if ((customerName = msgMap.get(waveId)) == null) {
                    continue;
                }
                Wave wave = new Wave();
                wave.setId(waveId);
                wave.setCustomerName(customerName);
                waves.add(wave);
            }
            if (CollectionUtils.isNotEmpty(waves)) {
                waveDao.batchUpdate(staff, waves);
            }
        }
        // 给订单打上"跨境印花" 标签
        addTradeCrossTag(staff, sids);
        //生成加工单
        if(generateStockProductOrderFlag&&BooleanUtils.isTrue(uniqueCodeResult.isSuccess())){
            ProcessGenerateOrderWaveParam processPlanGenerateOrderParam = crossborderWaveConfig.getProcessPlanGenerateOrderParam();
            if(uniqueCodeResult.getDateNo()==null||uniqueCodeResult.getBatchNo()==null){
                logger.error(LogHelper.buildLog(staff,"跨境生成唯一码失败 uniqueCodeResult:"+
                        JSON.toJSONString(uniqueCodeResult)+"uniqueCodeGenericParams:"+ JSON.toJSONString(uniqueCodeGenericParams)));
                throw new RuntimeException("跨境生成唯一码失败");
            }
            processPlanGenerateOrderParam.setDateNo(uniqueCodeResult.getDateNo());
            processPlanGenerateOrderParam.setBatchNo(uniqueCodeResult.getBatchNo());
            processPlanGenerateOrderParam.setUnderstockedGroup(lastUnderstockedGroup);
            try{
                BaseResult baseResult = wmsService.generateStockProductOrderAndWave(staff, processPlanGenerateOrderParam);
                if(baseResult==null||!baseResult.isSuccess()){
                    logger.info(LogHelper.buildLog(staff,String.format("跨境波次生成加工单失败,param:%s result:%s",JSON.toJSONString(processPlanGenerateOrderParam),JSON.toJSONString(baseResult))));
                    String errorMessage="";
                    if(baseResult!=null){
                        errorMessage=baseResult.getMessage();
                        revertCrossBorderWave(staff, baseResult, normalWaveIds, uniqueCodeResult);
                    }
                    throw new RuntimeException(errorMessage);
                }
            }
            catch (Exception e){
                logger.error(LogHelper.buildErrorLog(staff,e,"跨境波次生成加工单失败,param:"+JSON.toJSONString(processPlanGenerateOrderParam)));
                throw e;
            }
        }
    }

    private CrossBoardTradeWaveGroup dealAcrossLackCondition(Staff staff, WaveFilterParams params, CrossBorderWaveConfig crossborderWaveConfig, WaveRule rule) {
        if (BooleanUtils.isTrue(crossborderWaveConfig.getCheckMaterialStockProduct())) {
            Set<Long> crossProductMaterialLackSids = getCrossProductMaterialLackSids(staff, params, crossborderWaveConfig);
            logger.debug(LogHelper.buildLog(staff, String.format("校验印花原料，存在缺货的sids:%s", crossProductMaterialLackSids)));
            if (CollectionUtils.isEmpty(crossProductMaterialLackSids)) {
                logger.info(LogHelper.buildLog(staff, "加工原料充足"));
                return generateCrossNormalWave(staff, params, crossborderWaveConfig, rule);
            }
            if (SUMMARY_GENERATE_TYPE.equals(crossborderWaveConfig.getGenerateType())) {
                logger.debug(LogHelper.buildLog(staff,"汇总生成，存在缺货，全部生成加工单,sids:"+ params.getSids()));
                CrossBoardTradeWaveGroup crossBoardTradeWaveGroup = waveTradesQueryBusiness.calculateAcrossBorderTradeStock(staff, params, crossborderWaveConfig);
                crossBoardTradeWaveGroup.setGenerateStockProductOrder(true);
                crossBoardTradeWaveGroup.setWaveIds(new HashSet<>());
                return crossBoardTradeWaveGroup;
            } else {
                List<Long> allSids = params.getSids();
                List<Long> fullSids = params.getSids().stream().filter(s -> !crossProductMaterialLackSids.contains(s)).collect(Collectors.toList());
                logger.debug(LogHelper.buildLog(staff,"单个生成，存在缺货，全部生成加工单,sids:"+ crossProductMaterialLackSids));
                params.setSids(new ArrayList<>(crossProductMaterialLackSids));
                CrossBoardTradeWaveGroup crossBoardTradeWaveGroup = waveTradesQueryBusiness.calculateAcrossBorderTradeStock(staff, params, crossborderWaveConfig);
                crossBoardTradeWaveGroup.setGenerateStockProductOrder(true);
                crossBoardTradeWaveGroup.setWaveIds(new HashSet<>());
                if (CollectionUtils.isNotEmpty(fullSids)) {
                    logger.debug(LogHelper.buildLog(staff, String.format("单个生成，存在缺货，不缺货生成波次的sids:%s", fullSids)));
                    params.setSids(fullSids);
                    CrossBoardTradeWaveGroup partCrossGroup = generateCrossNormalWave(staff, params, crossborderWaveConfig, rule);
                    crossBoardTradeWaveGroup.setWaveIds(partCrossGroup.getWaveIds());
                    crossBoardTradeWaveGroup.getUniqueCodeGenericParams().getItemParams().addAll(partCrossGroup.getUniqueCodeGenericParams().getItemParams());
                }
                //把值塞回去
                params.setSids(allSids);
                return crossBoardTradeWaveGroup;
            }
        }
        return generateCrossNormalWave(staff, params, crossborderWaveConfig, rule);
    }

    private CrossBoardTradeWaveGroup generateCrossNormalWave(Staff staff, WaveFilterParams params, CrossBorderWaveConfig crossborderWaveConfig, WaveRule rule) {
        //生成拣货波次
        Set<Long> waveIds = crossBorderWaveGenerateNormalWave(staff, params, rule);
        //根据配货结果生成唯一码
        QueryAllocateGoodsRecordParams queryAllocateGoodsRecordParams = new QueryAllocateGoodsRecordParams();
        queryAllocateGoodsRecordParams.setSids(params.getSids());
        queryAllocateGoodsRecordParams.setWaveIds(new ArrayList<>(waveIds));
        List<AllocateGoodsRecord> allocateGoodsRecords = wmsService.queryAllocateGoodsRecords(staff, queryAllocateGoodsRecordParams);
        CrossBoardTradeWaveGroup crossBoardTradeWaveGroup = waveTradesQueryBusiness.calculateAcrossBorderTradeStockV2(staff, params, crossborderWaveConfig, allocateGoodsRecords);
        crossBoardTradeWaveGroup.setWaveIds(waveIds);
        return crossBoardTradeWaveGroup;
    }

    private Set<Long> getCrossProductMaterialLackSids(Staff staff, WaveFilterParams params, CrossBorderWaveConfig crossborderWaveConfig) {
        Set<Long> lackMaterialSids = new HashSet<>();
        ITradeWaveQueryService.QueryWaveResult queryWaveResult = waveHelpBusiness.initQueryWaveResult(staff, params, null);
        //前配货
        queryWaveResult.getWaveConfig().setOpenRuleFirstThenAllocate(0);
        queryWaveResult.setFromQuery(Boolean.FALSE);
        waveAllocateGoodsBusiness.allocateGoodsStock(staff, queryWaveResult, null, null, Boolean.TRUE, null, params);
        List<AllocateGoodsRecord> allocateGoodsRecords = queryWaveResult.getAlreadyAllocateRecord();
        CrossBoardTradeWaveGroup crossBoardTradeWaveGroup = waveTradesQueryBusiness.calculateAcrossBorderTradeStockV2(staff, params, crossborderWaveConfig, allocateGoodsRecords);
        List<CrossBoardTradeWaveGroup.OrderStockInfo> lackOrderList = crossBoardTradeWaveGroup.getLackAllocateOrderStockInfo();
        List<Order> productOrders = crossBoardTradeWaveGroup.getProductOrders();
        if (CollectionUtils.isEmpty(lackOrderList) || CollectionUtils.isEmpty(productOrders)) {
            return lackMaterialSids;
        }
        Map<Long, Order> orderMap = productOrders.stream().collect(Collectors.toMap(Order::getId, o -> o, (k1, k2) -> k1));
        List<WmsChangeAffect> affects = Lists.newArrayList();
        for (CrossBoardTradeWaveGroup.OrderStockInfo lackOrderStock : lackOrderList) {
            Order productOrder = orderMap.get(lackOrderStock.getOrderId());
            if (productOrder == null || CollectionUtils.isEmpty(productOrder.getSuits())) {
                continue;
            }
            for (Order suit : productOrder.getSuits()) {
                WmsChangeAffect affect = new WmsChangeAffect();
                affect.setSysItemId(suit.getItemSysId());
                affect.setSysSkuId(suit.getSkuSysId());
                affect.setQualityType(Boolean.TRUE);
                affect.setOuterId(suit.getOuterSkuId());
                affect.setBusiId(suit.getSid());
                affect.setSubBusiId(suit.getId());
                affect.setTotalNum((int) (lackOrderStock.getInsufficientNum() * (suit.getNum() / productOrder.getNum())));
                affect.setWarehouseId(params.getWarehouseId());
                affects.add(affect);
            }
        }
        if (CollectionUtils.isEmpty(affects)) {
            return lackMaterialSids;
        }
        //原料配货
        List<AllocateGoodsRecord> records = wmsService.preAllocateGoods(staff, affects);
        Map<Long, List<WmsChangeAffect>> affectSidMap = affects.stream().collect(Collectors.groupingBy(WmsChangeAffect::getBusiId));
        if (CollectionUtils.isEmpty(records)) {
            return affects.stream().map(WmsChangeAffect::getBusiId).collect(Collectors.toSet());
        }
        Map<String, List<AllocateGoodsRecord>> itemAllocateMap = records.stream()
                .collect(Collectors.groupingBy(record ->  record.getOrderId() + "_" + record.getSysItemId() + "_" + (record.getSysSkuId() == null || record.getSysSkuId() < 0 ? 0L : record.getSysSkuId())));
        for (Map.Entry<Long, List<WmsChangeAffect>> entry : affectSidMap.entrySet()) {
            Long sid = entry.getKey();
            boolean lackMaterial = false;
            for (WmsChangeAffect affect : entry.getValue()) {
                String key = affect.getSubBusiId() + "_" + affect.getSysItemId() + "_" + (affect.getSysSkuId() == null || affect.getSysSkuId() < 0 ? 0L : affect.getSysSkuId());
                List<AllocateGoodsRecord> allocateGoodsRecordList = itemAllocateMap.get(key);
                if (CollectionUtils.isEmpty(allocateGoodsRecordList)) {
                    lackMaterial = true;
                    break;
                }
                int stockNum = allocateGoodsRecordList.stream().mapToInt(allocateGoodsRecord -> Optional.ofNullable(allocateGoodsRecord.getAllocatedNum()).orElse(0)).sum();
                if (stockNum < affect.getTotalNum()) {
                    lackMaterial = true;
                    break;
                }
            }
            if (lackMaterial) {
                lackMaterialSids.add(sid);
            }
        }
        return lackMaterialSids;
    }

    private void checkCrossWaveTradeGenerateWaveV1(Staff staff, WaveFilterParams params, CrossBorderWaveConfig crossborderWaveConfig, WaveRule rule) {
        CrossBoardTradeWaveGroup crossBoardTradeWaveGroup=waveTradesQueryBusiness.calculateAcrossBorderTradeStock(staff, params,crossborderWaveConfig);
        List<CrossBoardTradeWaveGroup.TradeStockInfo> normalTrades = crossBoardTradeWaveGroup.getNormalTrades();
        Integer stockOutType = crossborderWaveConfig.getStockOutType();
        List<Long> sids = params.getSids();

        //设置波次批次分组号
        String lastUnderstockedGroup = waveQueryDao.queryLastUnderstockedGroup(staff, params.getWarehouseId(), new Date()); // 查询获取当天最新的缺货分组
        lastUnderstockedGroup = waveHelpBusiness.getUnderstockedGroup(lastUnderstockedGroup);
        params.setCrossBorderWaveUnderstockedGroup(lastUnderstockedGroup);

        //按照缺货情况才生成拣货波次
        Set<Long>normalWaveIds=new HashSet<>();
        Set<Long> waveIds = new HashSet<>();
        if(CollectionUtils.isNotEmpty(normalTrades)&&SITUATION_STOCK_TOUT_TYPE.equals(stockOutType)){
            //无需配货的子订单，占用数量为0
            Set<Long> allocateOrderIds= normalTrades.stream().flatMap(trade -> trade.getOrderStockInfos().stream())
                    .map(CrossBoardTradeWaveGroup.OrderStockInfo::getOrderId)
                    .collect(Collectors.toSet());
            params.setAllocateOrderIds(allocateOrderIds);
            //生成拣货波次
            waveIds = crossBorderWaveGenerateNormalWave(staff, params, rule);
            normalWaveIds.addAll(waveIds);
            //过滤无法加入波次的订单，这种订单无法创建唯一码
            filterUngenerateWaveTradeUnicode(staff, sids, normalTrades, crossBoardTradeWaveGroup);
            params.setCrossBorderGeneratePickWave(true);
        }else {
            logger.debug(LogHelper.buildLog(staff,"跨境订单生成波次，全部生成加工单,sids:"+JSON.toJSONString(params.getSids())));
            params.setCrossBorderGeneratePickWave(false);
            crossBoardTradeWaveGroup.setGenerateStockProductOrder(true);
        }
        //生成唯一码
        UniqueCodeGenericParams uniqueCodeGenericParams = crossBoardTradeWaveGroup.getUniqueCodeGenericParams();
        uniqueCodeGenericParams.setGenerateType(crossborderWaveConfig.getGenerateType());
        UniqueCodeResult uniqueCodeResult = uniqueCodeServiceDubbo.generateCode(staff, uniqueCodeGenericParams);
        boolean generateStockProductOrderFlag = crossBoardTradeWaveGroup.isGenerateStockProductOrder();
        if(uniqueCodeResult==null||!BooleanUtils.isTrue(uniqueCodeResult.isSuccess())){
            String errorMsg = "跨境生成唯一码失败";
            if(uniqueCodeResult!=null){
                List<UniqueCodeResult.FailCodeInfo> failCodeInfos = uniqueCodeResult.getFailCodeInfos();
                if(CollectionUtils.isNotEmpty(failCodeInfos)){
                    errorMsg=failCodeInfos.get(0).getFailMsg();
                }else if(StringUtils.isNotEmpty(uniqueCodeResult.getErrorMsg())){
                    errorMsg=uniqueCodeResult.getErrorMsg();
                }
            }
            logger.error(LogHelper.buildLog(staff,"跨境生成唯一码失败 uniqueCodeResult:"+ JSON.toJSONString(uniqueCodeResult)));
            throw new RuntimeException(errorMsg);
        }

        if (uniqueCodeResult != null && CollectionUtils.isNotEmpty(uniqueCodeResult.getExtMsgInfos()) && CollectionUtils.isNotEmpty(waveIds)) {
            Iterator<Long> iterator = waveIds.iterator();
            Map<Long, String> msgMap = uniqueCodeResult.getExtMsgInfos().stream().collect(Collectors.toMap(UniqueCodeResult.ExtMsgInfo::getBusinessId, UniqueCodeResult.ExtMsgInfo::getMsg, (a, b) -> a));
            List<Wave> waves = Lists.newArrayList();
            while (iterator.hasNext()) {
                Long waveId = iterator.next();
                String customerName;
                if ((customerName = msgMap.get(waveId)) == null) {
                    continue;
                }
                Wave wave = new Wave();
                wave.setId(waveId);
                wave.setCustomerName(customerName);
                waves.add(wave);
            }
            if (CollectionUtils.isNotEmpty(waves)) {
                waveDao.batchUpdate(staff, waves);
            }
        }
        // 给订单打上"跨境印花" 标签
        addTradeCrossTag(staff, sids);
        //生成加工单
        if(generateStockProductOrderFlag&&BooleanUtils.isTrue(uniqueCodeResult.isSuccess())){
            ProcessGenerateOrderWaveParam processPlanGenerateOrderParam = crossborderWaveConfig.getProcessPlanGenerateOrderParam();
            if(uniqueCodeResult.getDateNo()==null||uniqueCodeResult.getBatchNo()==null){
                logger.error(LogHelper.buildLog(staff,"跨境生成唯一码失败 uniqueCodeResult:"+
                        JSON.toJSONString(uniqueCodeResult)+"uniqueCodeGenericParams:"+ JSON.toJSONString(uniqueCodeGenericParams)));
                throw new RuntimeException("跨境生成唯一码失败");
            }
            processPlanGenerateOrderParam.setDateNo(uniqueCodeResult.getDateNo());
            processPlanGenerateOrderParam.setBatchNo(uniqueCodeResult.getBatchNo());
            processPlanGenerateOrderParam.setUnderstockedGroup(lastUnderstockedGroup);
            try{
                BaseResult baseResult = wmsService.generateStockProductOrderAndWave(staff, processPlanGenerateOrderParam);
                if(baseResult==null||!baseResult.isSuccess()){
                    logger.info(LogHelper.buildLog(staff,String.format("跨境波次生成加工单失败,param:%s result:%s",JSON.toJSONString(processPlanGenerateOrderParam),JSON.toJSONString(baseResult))));
                    String errorMessage="";
                    if(baseResult!=null){
                        errorMessage=baseResult.getMessage();
                        revertCrossBorderWave(staff, baseResult, normalWaveIds, uniqueCodeResult);
                    }
                    throw new RuntimeException(errorMessage);
                }
            }
            catch (Exception e){
                logger.error(LogHelper.buildErrorLog(staff,e,"跨境波次生成加工单失败,param:"+JSON.toJSONString(processPlanGenerateOrderParam)));
                throw e;
            }
        }
    }

    private void revertCrossBorderWave(Staff staff, BaseResult baseResult, Set<Long> normalWaveIds, UniqueCodeResult uniqueCodeResult) {
        if(baseResult==null){
            return;
        }
        String code = baseResult.getCode();
        if(PRINT_STOCK_PROCESS_MATERIAL_STOCK_NOT_ENOUGH.equals(code)){
            //光板库存不足，需要回滚数据，其他异常目前没有回滚
            //回滚生成的有货和无货的唯一码
            logger.info(LogHelper.buildLog(staff,String.format("跨境波次生成加工单失败，回滚数据 waveIds:%s dateNo:%s batchNo%s",
                    JSON.toJSONString(normalWaveIds), uniqueCodeResult.getData(), uniqueCodeResult.getBatchNo())));
            UniqueCodeGenericParams revertUniqueCodeParams = new UniqueCodeGenericParams();
            revertUniqueCodeParams.setBusinessType(UniqueCodeRelation.BusinessType.TRADE.getType());
            revertUniqueCodeParams.setUnboundType(UnboundReasonTypeEnum.MATERIALS_LACK_RELEASE_BIND_TRADE);
            revertUniqueCodeParams.setDateNo(uniqueCodeResult.getDateNo());
            revertUniqueCodeParams.setBatchNo(uniqueCodeResult.getBatchNo());
            uniqueCodeServiceDubbo.releaseRelation(staff,revertUniqueCodeParams);
            //回滚生成的订单波次
            if(CollectionUtils.isNotEmpty(normalWaveIds)){
                Assert.isTrue(!waveProgressBusiness.hasProgress(staff, ProgressEnum.PROGRESS_TRADE_WAVE_CANCEL), "正在进行取消波次，请稍等！");
                if (waveProgressBusiness.addProgress(staff, ProgressEnum.PROGRESS_TRADE_WAVE_CANCEL)) {
                    eventCenter.fireEvent(this, new EventInfo("trade.wave.cancel.progress").setArgs(new Object[]{staff, normalWaveIds}), null);
                }
            }
        }
    }

    private void filterUngenerateWaveTradeUnicode(Staff staff, List<Long>sids, List<CrossBoardTradeWaveGroup.TradeStockInfo> normalTrades, CrossBoardTradeWaveGroup crossBoardTradeWaveGroup) {
        List<Trade> trades = tradeServiceDubbo.queryTradeBySids(staff, false, sids.toArray(new Long[0]));
        List<Trade> generates = trades.stream().filter(t -> DataUtils.checkLongNotEmpty(t.getWaveId())&&!t.getWaveId().equals(0L)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(generates)){
            Set<Long> generatesSids = generates.stream().map(Trade::getSid).collect(Collectors.toSet());
            List<Long> removeSids = normalTrades.stream().map(CrossBoardTradeWaveGroup.TradeStockInfo::getSid).filter(stockSid -> !generatesSids.contains(stockSid)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(removeSids)){
                logger.info(LogHelper.buildLog(staff,"checkCrossWaveTradeGenerateWave removeSids:"+JSON.toJSONString(removeSids)));
            }
            UniqueCodeGenericParams uniqueCodeGenericParams = crossBoardTradeWaveGroup.getUniqueCodeGenericParams();
            List<UniqueCodeGenericParams.ItemGenericParams> itemParams = uniqueCodeGenericParams.getItemParams();
            List<UniqueCodeGenericParams.ItemGenericParams> genericParams = itemParams.stream().filter(itemGenericParams -> !removeSids.contains(itemGenericParams.getBusinessId())).collect(Collectors.toList());
            uniqueCodeGenericParams.setItemParams(genericParams);
        }
    }


    private Set<Long> crossBorderWaveGenerateNormalWave(Staff staff, WaveFilterParams params, WaveRule rule){

        CrossBorderWaveConfig crossborderWaveConfig = params.getCrossborderWaveConfig();
        //重新设置sids
        List<Long> sids = params.getSids();
        Set<Long>waveIds=new HashSet<>();
        if(SINGLE_GENERATE_TYPE.equals(crossborderWaveConfig.getGenerateType())){
            //一个订单1个波次
            sids.forEach(sid->{
                params.setSids(Collections.singletonList(sid));
                assembleCrossBorderWaveResult(staff, params, rule, waveIds);
            });
            params.setSids(sids);
        }
        if(SUMMARY_GENERATE_TYPE.equals(crossborderWaveConfig.getGenerateType())){
            params.setSids(sids);
            assembleCrossBorderWaveResult(staff, params, rule, waveIds);
        }
        return waveIds;
    }

    private void assembleCrossBorderWaveResult(Staff staff, WaveFilterParams params, WaveRule rule, Set<Long> waveIds) {
        WaveGenerateBusiness.GenerateWaveResult generateWaveResult = doCheckedTradeGenerateWave(staff, params, rule);
        List<Wave> waves = generateWaveResult.getWaves();
        if(CollectionUtils.isNotEmpty(waves)){
            Set<Long> ids = waves.stream().map(Wave::getId).filter(java.util.Objects::nonNull).collect(Collectors.toSet());
            waveIds.addAll(ids);
        }
    }


    private WaveGenerateBusiness.GenerateWaveResult doCheckedTradeGenerateWave(Staff staff, WaveFilterParams params, WaveRule rule) {
        WaveGenerateBusiness.GenerateWaveResult result = waveGenerateBusiness.generateWave(staff, Collections.singletonList(rule), params);
        addWaveTraceAndGetWaybillCode(staff, result.getWaves(), params.getWaveCreateType(), params.getIp(),params.getWaveRuleType());
        // 生成波次时生成拣选人
        assignWaveOperator(staff, Lists.newArrayList(rule), result.getWaves());
        dealPaperPick(staff, result);
        waveHelpBusiness.deleteWaveRuleCache(staff, null, Lists.newArrayList(params.getWarehouseId()),null);
        return result;
    }


    /**
     * 订单管理生成波次填充sid
     */
    private void fillSidsByTradeQueryParams(Staff staff, WaveFilterParams waveFilterParams) {
        if (waveFilterParams == null || waveFilterParams.getTradeQueryParams() == null) {
            return;
        }
        TradeQueryParams tradeQueryParams = waveFilterParams.getTradeQueryParams();
        List<Long> sids = StringUtils.isEmpty(tradeQueryParams.getSids()) ?
                waveHelpBusiness.getSidsByTradeQueryParams(staff, tradeQueryParams) : ArrayUtils.toLongList(tradeQueryParams.getSids());

        Assert.notEmpty(sids, "未查询到能生成波次的订单");
        List<Trade> trades = tradeServiceDubbo.queryTradeBySids(staff, false, sids.toArray(new Long[0]));
        Assert.notEmpty(trades, "未查询到能生成波次的订单");
        List<Long> warehouseIdList = trades.stream().map(Trade::getWarehouseId).distinct().collect(Collectors.toList());
        logger.debug(LogHelper.buildLog(staff, "仓库id" + warehouseIdList));
        Assert.isTrue(warehouseIdList.size() == 1, "处理的订单不在同一仓库！");

        waveFilterParams.setWarehouseId(warehouseIdList.get(0));
        waveFilterParams.setSids(trades.stream().map(Trade::getSid).collect(Collectors.toList()));
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "订单管理生成，查询到的sid数量：" + (CollectionUtils.isEmpty(waveFilterParams.getSids()) ? "0" : waveFilterParams.getSids().size()) + "，sids：" + waveFilterParams.getSids()));
        }
    }

    private void generateWave(Staff staff, Long warehouseId, List<WaveRule> rules, List<Integer> indexs, WaveCreateType waveCreateType, String ip, String waveRemark, DateRange payTimeRange,PdaScanWaveConfig pdaScanWaveConfig) {
        generateWave(staff, warehouseId, rules, indexs, waveCreateType, ip, waveRemark, payTimeRange, AutoWaveTypeEnum.CloseAuto.getType(),pdaScanWaveConfig);
    }

    private void generateWave(Staff staff, Long warehouseId, List<WaveRule> rules, List<Integer> indexs, WaveCreateType waveCreateType, String ip, String waveRemark, DateRange payTimeRange, int autoCreateType,PdaScanWaveConfig pdaScanWaveConfig) {
        List<Long> ruleIdList = rules.stream().map(WaveRule::getId).collect(Collectors.toList());
        //清理上一次原因
        waveNotInReasonBusiness.resetReasons(staff, warehouseId);

        int waveTradeLimit = getGenerateTradeLimit(staff);
        if (waveTradeLimit <= 0 || CollectionUtils.isNotEmpty(indexs)) {
            WaveGenerateBusiness.GenerateWaveResult result = waveGenerateBusiness.generateWave(staff, warehouseId, rules, indexs, null, waveTradeLimit, waveRemark, payTimeRange,pdaScanWaveConfig);

            addWaveTraceAndGetWaybillCode(staff, result.getWaves(), waveCreateType, ip, autoCreateType);
            // 生成波次时生成拣选人
            assignWaveOperator(staff, rules, result.getWaves());
            dealPaperPick(staff, result);
            waveAutoPrint(staff, waveCreateType, result);
        } else {
            if (logger.isInfoEnabled()) {
                logger.info(LogHelper.buildLog(staff, "开启波次生成订单数量限制，limit:" + waveTradeLimit));
            }

            Long lastMaxSid = null;
            boolean wavePageLimit = getGeneratePageLimit(staff);
            for (int i = 0; i < 50; i++) {
                WaveGenerateBusiness.GenerateWaveResult result = waveGenerateBusiness.generateWave(staff, warehouseId, rules, indexs, lastMaxSid, waveTradeLimit, waveRemark, payTimeRange,pdaScanWaveConfig);
                if (logger.isInfoEnabled()) {
                    logger.info(LogHelper.buildLog(staff, "循环生成波次结束，i = " + i
                            + "，wave.size = " + (CollectionUtils.isEmpty(result.getWaves()) ? "0" : result.getWaves().size())
                            + "，currentMaxSid:" + result.getCurrentMaxSid()));
                }
                if (wavePageLimit) {
                    lastMaxSid = result.getCurrentMaxSid();
                }
                if (CollectionUtils.isNotEmpty(result.getWaves())) {
                    addWaveTraceAndGetWaybillCode(staff, result.getWaves(), waveCreateType, ip, autoCreateType);
                    // 生成波次时生成拣选人
                    assignWaveOperator(staff, rules, result.getWaves());
                    waveAutoPrint(staff, waveCreateType, result);
                } else {
                    break;
                }
            }
        }
    }

    private int getGenerateTradeLimit(Staff staff) {
        WaveStatsSaveLimit companyLimit = waveHelpBusiness.getCompanyWaveStatsSaveLimit(staff);
        if (companyLimit == null || companyLimit.getWaveTradeLimit() == 0) {
            return waveHelpBusiness.getWaveTradeLimit(staff);
        } else {
            return companyLimit.getWaveTradeLimit();
        }
    }

    private boolean getGeneratePageLimit(Staff staff) {
        WaveStatsSaveLimit companyLimit = waveHelpBusiness.getCompanyWaveStatsSaveLimit(staff);
        if (companyLimit == null || StringUtils.isEmpty(companyLimit.getWavePageLimit())) {
            return waveHelpBusiness.isOpenWavePageLimit();
        } else {
            return DiamondConfig.SWITCH_ON.equalsIgnoreCase(companyLimit.getWavePageLimit());
        }
    }

    private void dealPaperPick(Staff staff, WaveGenerateBusiness.GenerateWaveResult result) {
        if (result == null || CollectionUtils.isEmpty(result.getWaves())) {
            return;
        }
        eventCenter.fireEvent(this, new EventInfo("wave.create").setArgs(new Object[]{staff, null, result.getWaves().stream().map(Wave::getId).collect(Collectors.toList()), true}), null);
    }

    private void waveAutoPrint(Staff staff, WaveCreateType waveCreateType, WaveGenerateBusiness.GenerateWaveResult result) {
        List<Wave> waves = result.getWaves();
        if (!WaveCreateType.AUTO.equals(waveCreateType) || CollectionUtils.isEmpty(waves)) {
            return;
        }
        // 精简传输对象
        List<Wave> transWaves = waves.stream().map(wave -> {
            Wave transWave = new Wave();
            transWave.setWarehouseId(wave.getWarehouseId());
            transWave.setId(wave.getId());
            transWave.setAutoPrinterId(wave.getAutoPrinterId());
            return transWave;
        }).collect(Collectors.toList());
        eventCenter.fireEvent(this, new EventInfo("wave.auto.print.save").setArgs(new Object[]{staff}), transWaves);
    }

    private void addWaveTraceAndGetWaybillCode(Staff staff, List<Wave> waves, WaveCreateType waveCreateType, String ip) {
        addWaveTraceAndGetWaybillCode(staff, waves, waveCreateType, ip, AutoWaveTypeEnum.CloseAuto.getType());
    }
    private void addWaveTraceAndGetWaybillCode(Staff staff, List<Wave> waves, WaveCreateType waveCreateType, String ip,WaveRuleType waveRuleType) {
        addWaveTraceAndGetWaybillCode(staff, waves, waveCreateType, ip, AutoWaveTypeEnum.CloseAuto.getType(),waveRuleType);
    }

    private void addWaveTraceAndGetWaybillCode(Staff staff, List<Wave> waves, WaveCreateType waveCreateType, String ip, int autoCreateType) {
        addWaveTraceAndGetWaybillCode(staff, waves, waveCreateType, ip, autoCreateType,null);
    }

    private void addWaveTraceAndGetWaybillCode(Staff staff, List<Wave> waves, WaveCreateType waveCreateType, String ip, int autoCreateType,WaveRuleType waveRuleType) {
        if (CollectionUtils.isNotEmpty(waves)) {
            try {
                addWaveTrace(staff, waves, waveCreateType, ip, autoCreateType,waveRuleType);
                autoGetWaveWaybill(staff, waves, ip);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "记录波次日志失败"), e);
            }
        }
    }


    private void addWaveTrace(Staff staff, List<Wave> waves, WaveCreateType waveCreateType, String ip, int autoCreateType,WaveRuleType waveRuleType) {
        List<WaveTrace> waveTraces = new ArrayList<>();
        String action = waveCreateType != null ? waveCreateType.getName() : WaveCreateType.MANUAL.getName();
        if (WaveCreateType.AUTO == waveCreateType){
            action = getAutoAction(action, autoCreateType);
        }
        if(CHECKED_CROSS_TRADE == waveCreateType&&waveRuleType!=null){
            action= waveRuleType.getName();
        }
        for (Wave wave : waves) {
            List<AssoWaveTrade> assos = wave.getAssos();
            List<Long> sids = Lists.newArrayListWithCapacity(assos.size());
            for (AssoWaveTrade asso : assos) {
                sids.add(asso.getSid());
            }
            WaveTrace waveTrace = WaveTraceUtils.buildWaveTrace(staff, wave.getId(), WaveTraceOperateEnum.CREATE_TRADE, action + "生成订单波次");
            if (waveCreateType == WaveCreateType.AUTO) {
                waveTrace.setStaffName("系统");
            }
            waveTraces.add(waveTrace);
            writeOpLog(staff, waveCreateType, String.format("%s生成波次，波次号：%s，生成波次订单：%s", action, wave.getId(), sids), String.format("ruleId:%d,warehouseId:%d", wave.getRuleId(), wave.getWarehouseId()), ip);
        }
        waveTraceService.batchAddWaveTrace(staff, waveTraces);
    }

    private String getAutoAction(String action, int autoCreateType) {
        Map<Integer, String> autoActions = new HashMap<>();
        autoActions.put(AutoWaveTypeEnum.TimedAuto.getType(), "定时");
        autoActions.put(AutoWaveTypeEnum.LaborAuto.getType(), "按劳动力");
        autoActions.put(AutoWaveTypeEnum.HourMinuteAuto.getType(), "定点");
        if (autoActions.containsKey(autoCreateType)) {
            action = autoActions.get(autoCreateType);
        }
        return action;
    }

    private void autoGetWaveWaybill(Staff staff, List<Wave> waves, String ip) {
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }

        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        WaveConfig waveConfig = waveServiceDubbo.queryWaveConfig(staff);
        boolean openCreateWaveUpload = openCreateWaveUpload(tradeConfig);
        Boolean waveAutoGetWaybill = waveConfig.getWaveAutoGetWaybillCode();
        if (!openCreateWaveUpload && (waveAutoGetWaybill == null || !waveAutoGetWaybill)) {
            return;
        }

        if (waveHelpBusiness.isOpenWaveWaybillGetLimit() && Objects.equal(tradeConfig.getAuditAutoGetWaybillCode(), 1)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "已开启审核获取单号，不进行波次生成获取单号操作"));
            }
            return;
        }
        List<Long> waveIds = waves.stream().map(Wave::getId).collect(Collectors.toList());
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("开启波次自动获取单号配置，异步获取单号,waveIds=%s", waveIds)));
        }
        /**
         * 获取单号
         */
        waveHelpBusiness.getWaybillCode(staff, waveIds, WaybillGetOperationEnum.GENERATE_WAVE, openCreateWaveUpload);
    }

    private void writeOpLog(Staff staff, WaveCreateType waveCreateType, String content, String args, String ip) {
        //记录操作日志
        OpLog log = new OpLog();
        if (waveCreateType == WaveCreateType.AUTO) {
            log.setStaffName("系统");
        }
        log.setDomain(Domain.TRADE);
        log.setAction("saveWaves");
        log.setContent(content);
        log.setArgs(args);
        log.setIp(ip);
        opLogService.record(staff, log);
    }

    private boolean openCreateWaveUpload(TradeConfig tradeConfig) {
        return tradeConfig != null && tradeConfig.getOpenUploadConsign() != null && tradeConfig.getOpenUploadConsign() == 1 && tradeConfig.getUploadConsignName() != null && tradeConfig.getUploadConsignName() == 4;
    }

    /**
     * 生成波次时生成拣选人
     */
    private void assignWaveOperator(Staff staff, List<WaveRule> rules, List<Wave> waves) {
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }
        //拣货角色
        List<WaveTrace> waveTraces=new ArrayList<>();

        List<Wave> pickerRoleWaves = waves.stream().filter(wave -> StringUtils.isNotEmpty(wave.getAssignPickerRoles()) && (wave.getAssignPicker() == null || wave.getAssignPicker() == 0)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(pickerRoleWaves)){
            Map<Long, String> pickerRoleName = waveHelpBusiness.getPickerRoleName(staff, true);
            pickerRoleWaves.forEach(wave -> {
                String assignPickerRoles = wave.getAssignPickerRoles();
                List<Long> roleIds = Strings.getAsLongList(assignPickerRoles, ",", true);
                List<String> roleNames=new ArrayList<>();
                for (Long roleId : roleIds) {
                    String roleName = Optional.ofNullable(pickerRoleName.get(roleId)).orElse("");
                    if(StringUtils.isNotEmpty(roleName)){
                        roleNames.add(roleName);
                    }
                }
                WaveTrace waveTrace = WaveTraceUtils.buildWaveTrace(staff, wave.getId(), WaveTraceOperateEnum.WAVE_ASSIGN,
                        String.format("安排拣选员:%s", roleNames.stream().filter(StringUtils::isNotEmpty).collect(Collectors.joining(","))));
                waveTraces.add(waveTrace);
            });
        }
        if(CollectionUtils.isNotEmpty(waveTraces)){
            waveTraceService.batchAddWaveTrace(staff,waveTraces);
        }
        //拣货人
        Map<String, List<Long>> name2IdsMap = waves.stream().filter(wave -> StringUtils.isNotEmpty(wave.getAssignPickerName()))
                .collect(Collectors.groupingBy(Wave::getAssignPickerName, Collectors.mapping(Wave::getId, Collectors.toList())));
        for (Map.Entry<String, List<Long>> name2IdsMapEntry : name2IdsMap.entrySet()) {
            // 记录波次日志
            waveTraceService.batchAddWaveTrace(staff,
                    WaveTraceUtils.buildBatchWaveTrace(staff, name2IdsMapEntry.getValue(), WaveTraceOperateEnum.WAVE_ASSIGN, String.format("安排拣选员:%s", name2IdsMapEntry.getKey())));
        }
    }


    /**
     *
     * @param staff
     * @param rules
     */
    private void autoCreateByLabor(Staff staff, List<WaveRule> rules) {
        if(CollectionUtils.isEmpty(rules)){
            return;
        }
        // 按劳动力 2min1次
        if (!LocalDateUtils.isMinuteMultipleOfTen()) {
            return;
        }

        Long companyId = staff.getCompanyId();
        List<WaveRule> openAutoCreateList = rules.stream().filter(WaveRule::getOpenAutoCreate).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(openAutoCreateList)){
            logger.debug(LogHelper.buildLog(staff,
                    "autoCreateByLabor_openAutoCreateList_empty:" + companyId + "_" + JSONObject.toJSONString(openAutoCreateList)));
            return;
        }

        logger.debug(LogHelper.buildLog(staff,
                "autoCreateByLabor_rules:" + companyId + "_" + JSONObject.toJSONString(openAutoCreateList)));

        // 规则分组后, 轮询进行波次计算
        //
        Map<Long, List<WaveRule>> ruleGroupIdAndRuleListMap = openAutoCreateList.stream().collect(Collectors.groupingBy(WaveRule::getWaveRuleGroupId));

        List<WaveRuleGroup> groupList = waveRuleGroupDao.queryByIds(staff, Lists.newArrayList(ruleGroupIdAndRuleListMap.keySet()));
        Map<Long, WaveRuleGroup> ruleGroupMap = groupList.stream().collect(Collectors.toMap(WaveRuleGroup::getId, v -> v));

        Staff finalStaff = staff;
        ruleGroupIdAndRuleListMap.forEach((groupId, ruleList) -> {
            logger.debug(LogHelper.buildLog(finalStaff,
                    "autoCreateByLabor_groupIdAndRuleList:" + groupId + ":" + ruleList));
        });
        ruleGroupMap.forEach((groupId, ruleGroup)->{
            logger.debug(LogHelper.buildLog(finalStaff,
                    "autoCreateByLabor_groupIdAndRuleGroup:" + groupId + ":" + ruleGroup));
        });

        List<Warehouse> warehouses = warehouseService.queryAll(staff, 1);
        if (warehouses == null || warehouses.size() == 0 || rules == null || rules.size() == 0) {
            return;
        }
        rules.forEach(rule -> rule.setCreateType(WaveCreateType.AUTO));
        Set<Long> warehouseIdList = new HashSet<>();
        for (Warehouse warehouse : warehouses) {
            if (!warehouse.getType().equals(Warehouse.TYPE_OWN) || !waveHelpBusiness.lockBeforeGenerate(staff, warehouse.getId())) {
                continue;
            }
            Long warehouseId = warehouse.getId();
            logger.debug(LogHelper.buildLog(staff, String.format("autoCreateByLabor[warehouseId:%d]", warehouseId)));

            try {
                ruleGroupIdAndRuleListMap.forEach((groupId, ruleList) -> {
                    executeAutoCreateByRuleGroup(finalStaff, warehouse.getId(), ruleGroupMap.get(groupId), ruleList);
                });
            } finally {
                autoCreateFinishProgress(staff, warehouse);
            }
        }

    }

    /**
     * 根据WaveGroupId来进行波次创建;
     * 返回创建的列表;
     * @param staff
     * @param ruleGroup
     * @param ruleList
     */
    private void executeAutoCreateByRuleGroup(Staff staff, Long warehouseId, WaveRuleGroup ruleGroup, List<WaveRule> ruleList) {
        String method = "TradeWaveCreateListener.executeAutoCreateByRuleGroup:";

        // Step1: 分组配置的前置检查 和 TRACE参数组装
        if (java.util.Objects.isNull(ruleGroup)) {
            logger.debug(LogHelper.buildLog(staff, method + "ruleGroup_IS_NULL"));
            return;
        }
        if (CollectionUtils.isEmpty(ruleList)) {
            logger.debug(LogHelper.buildLog(staff, method + "ruleList_IS_EMPTY," + ruleGroup.getId()));
            return;
        }
        List<WaveAutoCreateRuleDTO> dtos = waveServiceDubbo.getWaveAutoCreateRuleByGroupIds(staff, Lists.newArrayList(ruleGroup.getId()), AutoWaveTypeEnum.LaborAuto.getType());
        WaveAutoCreateRule waveAutoCreateRule = CollectionUtils.isEmpty(dtos) ? new WaveAutoCreateRule() : Optional.ofNullable(dtos.get(0).getRule()).orElse(new WaveAutoCreateRule());

        JSONObject params = new JSONObject() {{
            put("groupId", ruleGroup.getId());
            put("warehouseId", warehouseId);
            put("groupName", ruleGroup.getName());
            put("list", ruleList.stream().map(WaveRule::getId).collect(Collectors.toList()));
        }};
        if (java.util.Objects.isNull(waveAutoCreateRule.getWavePoolCountMax())) {
            logger.debug(LogHelper.buildLog(staff, method + "WAVE_POOL_COUNT_MAX_NULL," + params));
            return;
        }

        logger.debug(LogHelper.buildLog(staff, "条件判断完成进入数量计算," + params));


        // Step2: 计算当前规则分组的剩余容量
        Integer demandNumber = calculateDemandNum(staff, warehouseId, ruleGroup, ruleList, waveAutoCreateRule);
        if (demandNumber == null || demandNumber <= 0) {
            //log something
            logger.debug(LogHelper.buildLog(staff, ":demandNumber<=0, 不再进行波次创建:" + params));
            return;
        }

        // Step3: 基于剩余容量, 进行波次创建
        WaveUtils.DEMAND_NUMBER.set(demandNumber);
        params.put("demandNumber", WaveUtils.DEMAND_NUMBER.get());
        logger.debug(LogHelper.buildLog(staff, "autoCreateWaves_开始调用wave生成" + params));
        try {
            autoCreateWavesByLabor(staff, warehouseId, ruleList);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "开始调用wave生成_EXCEPTION" + params), e);
        }finally {
            WaveUtils.DEMAND_NUMBER.set(null);
        }
    }

    private Integer calculateDemandNum(Staff staff, Long warehouseId, WaveRuleGroup ruleGroup, List<WaveRule> ruleList, WaveAutoCreateRule waveAutoCreateRule) {
        JSONObject params = new JSONObject() {{
            put("groupId", ruleGroup.getId());
            put("warehouseId", warehouseId);
            put("groupName", ruleGroup.getName());
            put("list", ruleList.stream().map(WaveRule::getId).collect(Collectors.toList()));
        }};

        Integer demandNumber = 0;
        try {

            Integer wavePoolCountMax = waveAutoCreateRule.getWavePoolCountMax();
            Integer wavePoolCountMin = waveAutoCreateRule.getWavePoolCountMin();
            params.put(WaveCreateConstants.WAVE_POOL_COUNT_MAX, wavePoolCountMax);
            params.put(WaveCreateConstants.WAVE_POOL_COUNT_MIN, wavePoolCountMin);

            demandNumber = wavePoolCountMax;
            // 查询当前规则分组生成, 但是未作业的波次列表
            WaveFilterParams filter = new WaveFilterParams();
            filter.setWaveRuleGroupId(ruleGroup.getId());
            filter.setRules(ruleList);
            filter.setStatus(String.valueOf(Wave.STATUS_CREATED));
            filter.setWarehouseId(warehouseId);
            Page page = new Page();
            page.setPageNo(1);
            page.setPageSize(300);
            PageListBase<Wave> result = waveServiceDubbo.queryWaves(staff, filter, page);
            if (result != null && CollectionUtils.isNotEmpty(result.getList())) {

//                result.getList().forEach(sub->{
//                    JSONObject subParams = new JSONObject();
//                    subParams.put("id", sub.getId());
//                    subParams.put("warehouseId", sub.getWarehouseId());
//                    subParams.put("groupId", sub.getRuleGroupId());
//                    subParams.put("status", sub.getStatus());
//                    subParams.put("dStatus", sub.getDistributionStatus());
//
//                    logger.debug(LogHelper.buildLog(staff, "[DOING]查询存量波次列表:" + params + subParams));
//                });

                List<Wave> filterList = result.getList().stream().filter(sub -> {
                    return Objects.equal(sub.getRuleGroupId(), ruleGroup.getId())
                            && Objects.equal(warehouseId, (sub.getWarehouseId()));
                }).collect(Collectors.toList());

                List<Long> waveIdList = filterList.stream().map(Wave::getId).collect(Collectors.toList());
                params.put("waveIdList", waveIdList);

                logger.debug(LogHelper.buildLog(staff, "[DOING]查询存量波次列表_QueryWaveResult_SIZE:" + params));
                params.remove("waveIdList");

                List<Wave> waitPickList = filterList.stream()
                        .filter(wave -> (wave.getDistributionStatus() == 0 && wave.getStatus() == 1))
                        .collect(Collectors.toList());
                List<Wave> inPickingList = filterList.stream()
                        .filter(wave -> (wave.getDistributionStatus() == 1 && wave.getStatus() == 1))
                        .collect(Collectors.toList());

                List<Long> waitPickIdList = waitPickList.stream().map(Wave::getId).collect(Collectors.toList());
                List<Long> inPickingIdList = inPickingList.stream().map(Wave::getId).collect(Collectors.toList());
                params.put("waitPickIdList", waitPickIdList);
                params.put("inPickingIdList", inPickingIdList);

                logger.debug(LogHelper.buildLog(staff, "[DOING]重新设置需求数量:" + waitPickList.size() + "_" + inPickingIdList.size() + params));

                if(waitPickList.size() >= wavePoolCountMax){
                    logger.debug(LogHelper.buildLog(staff,
                            "[FINAL]重新设置需求数量:超限返回0,未拣选数量大于劳动力,"
                                    + waitPickList.size() + "_" + inPickingIdList.size() + params));
                    return 0;
                }

                if(waitPickList.size() + inPickingList.size() >= wavePoolCountMax){
                    if(waitPickList.size() < wavePoolCountMin){
                        logger.debug(LogHelper.buildLog(staff, "[DOING]重新设置需求数量:使用预留值" + waitPickList.size() + "_" + params));
                        demandNumber = wavePoolCountMin - waitPickList.size();
                    }else {
                        logger.debug(LogHelper.buildLog(staff, "[DOING]重新设置需求数量:超限返回0,未拣选数量大于预留值" + waitPickList.size() + "_" + params));
                        demandNumber = 0;
                    }
                }else{
                    logger.debug(LogHelper.buildLog(staff, "[DOING]重新设置需求数量:使用预留值和劳动力判断" + waitPickList.size() + "_" + params));
                    int laborCount = wavePoolCountMax - waitPickList.size() - inPickingList.size();
                    demandNumber = waitPickList.size() > 0 ? laborCount : Math.max(laborCount, wavePoolCountMin);
                }
            } else {
                logger.debug(LogHelper.buildLog(staff, "[DOING]查询存量波次列表_QueryWaveResult_NULL" + params));
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "[DOING]查询存量波次列表_QueryWaveResult_EXCEPTION_异常" + params), e);
        }
        logger.debug(LogHelper.buildLog(staff, "[FINAL]重新设置需求数量:" + demandNumber + params));

        return demandNumber;
    }

    private String getKey(String eventId)
    {
        return "wave_create_" + eventId;
    }
}

//FIXME 单独拆出来;
class WaveCreateConstants{
    public final static String WAVE_POOL_COUNT_MAX = "wavePoolCountMax";
    public final static String WAVE_POOL_COUNT_MIN = "wavePoolCountMin";
}
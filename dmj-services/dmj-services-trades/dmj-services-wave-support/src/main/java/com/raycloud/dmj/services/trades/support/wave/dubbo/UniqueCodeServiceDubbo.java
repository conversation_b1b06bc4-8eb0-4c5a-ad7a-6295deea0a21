package com.raycloud.dmj.services.trades.support.wave.dubbo;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.wave.ItemUniqueCodeGenerateBusiness;
import com.raycloud.dmj.commoncode.commonquery.UniqueCodeCommonQueryBusiness;
import com.raycloud.dmj.dao.wave.OrderUniqueCodeUnboundLogDao;
import com.raycloud.dmj.dao.wave.UniqueCodeRelationDao;
import com.raycloud.dmj.dao.wave.WaveUniqueCodeLogDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.PageListBase;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.caigou.PdaFastScanReturnBO;
import com.raycloud.dmj.domain.caigou.result.BatchResult;
import com.raycloud.dmj.domain.caigou.result.SuccessItem;
import com.raycloud.dmj.domain.trades.PageColumnConf;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.model.*;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wave.vo.BeforeArrivedCheckVO;
import com.raycloud.dmj.domain.wave.vo.UniqueCodeInfoQueryVO;
import com.raycloud.dmj.domain.wave.vo.AnalysisOrderUniqueCodesResult;
import com.raycloud.dmj.services.dubbo.IUniqueCodeServiceDubbo;
import com.raycloud.dmj.services.trades.support.wave.OrderUniqueCodeService;
import com.raycloud.dmj.services.trades.support.business.OrderUniqueCodeAnalysisBusiness;
import com.raycloud.dmj.services.trades.support.wave.business.UniqueCodeCrossBorderBusiness;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.trades.wave.IItemUniqueCodeService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> xubin
 * @desc:  唯一码对外提供dubbo服务接口
 * @date : 9/2/21  7:16 PM
 */
@Service
public class UniqueCodeServiceDubbo implements IUniqueCodeServiceDubbo {

    private static Logger logger = Logger.getLogger(UniqueCodeServiceDubbo.class);

    @Resource
    private OrderUniqueCodeUnboundLogDao orderUniqueCodeUnboundLogDao;
    @Resource
    private OrderUniqueCodeService orderUniqueCodeService;
    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;
    @Resource
    private IThirdWarehouseUniqueCodeService thirdWarehouseUniqueCodeService;
    @Resource
    private IUniqueCodeForceAllocateGoodsSupplierService uniqueCodeForceAllocateGoodsSupplierService;

    @Resource
    private IOrderUniqueCodeConfigService orderUniqueCodeConfigService;
    @Resource
    private IWaveUniqueCodeService waveUniqueCodeService;

    @Resource
    private WaveUniqueCodeLogDao waveUniqueCodeLogDao;
    @Resource
    private OrderUniqueCodeAnalysisBusiness orderUniqueCodeAnalysisBusiness;
    @Resource
    private UniqueCodeRelationDao uniqueCodeRelationDao;
    @Resource
    private ItemUniqueCodeGenerateBusiness itemUniqueCodeGenerateBusiness;
    @Resource
    private IOrderUniqueCodeTagService orderUniqueCodeTagService;
    @Resource
    private IUniqueCodeGenericService uniqueCodeGenericService;
    @Resource
    private IUniqueCodeExtendService uniqueCodeExtendService;
    @Resource
    private IUniqueCodeBaseService uniqueCodeBaseService;
    @Resource
    private IOrderUniqueCodeBackendService orderUniqueCodeBackendService;
    @Resource
    private UniqueCodeCrossBorderBusiness uniqueCodeCrossBorderBusiness;
    @Resource
    private IWaveColumnConfService waveColumnConfService;
    @Resource
    private UniqueCodeCommonQueryBusiness uniqueCodeCommonQueryBusiness;
    @Resource
    private IItemUniqueCodeExtendService itemUniqueCodeExtendService;
    @Resource
    private IItemUniqueCodeGenerateRuleService itemUniqueCodeGenerateRuleService;


    @Override
    public OrderUniqueCodeUnboundLog queryUnboundLogByUniqueCode(Staff staff, String uniqueCode) {
        return orderUniqueCodeUnboundLogDao.queryByLastOne(staff, uniqueCode);
    }

    @Override
    public Map<String, Object> orderUniqueCodePick(Staff staff, String uniqueCode) {
        return orderUniqueCodeService.orderUniqueCodePick(staff, uniqueCode);
    }

    @Override
    public List<WaveUniqueCode> createAndQueryUniqueCodes(Staff staff, List<WaveUniqueCode> codes) {
        return itemUniqueCodeService.createAndQueryUniqueCodes(staff, codes);
    }

    @Override
    public List<UniqueCodeForceAllocateGoodsSupplier> queryAllocateGoodsSupplier(Staff staff) {
        return uniqueCodeForceAllocateGoodsSupplierService.queryList(staff, null);
    }
    @Override
    public OrderUniqueCodeConfig getUniqueCodeConfig(Staff staff) {
        return orderUniqueCodeConfigService.get(staff);
    }


    @Override
    public WaveUniqueCode queryUniqueCodeInfo(Staff staff, String uniqueCode) {
        return itemUniqueCodeService.packScanItemUniqueCodeInfo(staff, Lists.newArrayList(uniqueCode)).get(0);
    }

    @Override
    public WaveUniqueCode queryUniqueCodeInfoNew(Staff staff, String uniqueCode, Long sid) {
        return itemUniqueCodeService.packScanItemUniqueCodeInfoNew(staff, Lists.newArrayList(uniqueCode), sid).get(0);
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodeByCondition(Staff staff, ItemUniqueCodeQueryParams itemUniqueCodeQueryParams) {
        return itemUniqueCodeService.queryItemUniqueCodeByCondition(staff, itemUniqueCodeQueryParams);
    }

    @Override
    public void generateSplitTag(Staff staff, List<Long> warehouseIdList, List<Long> userIdList) {
        orderUniqueCodeService.generateSplitTag(staff, warehouseIdList, userIdList);
    }

    @Override
    public List<String> purchaseMatchUniqueCode(Staff staff, List<PurchaseMatchUniqueCodeParams> queryParams) {
        return orderUniqueCodeService.purchaseMatchUniqueCode(staff, queryParams);
    }

    @Override
    public List<WaveUniqueCodeLog> queryLogListAdaptive(Staff staff, WaveUniqueCodeLogParams params, Page page) {
        return waveUniqueCodeService.queryListAdaptive(staff, params, page);
    }

    @Override
    public Long queryLogCountAdaptive(Staff staff, WaveUniqueCodeLogParams params) {
        return waveUniqueCodeService.queryCountAdaptive(staff, params);
    }

    @Override
    public List<String> queryByCodesAndType(Staff staff, List<String> codes, String opType) {
        return waveUniqueCodeService.queryByCodesAndType(staff, codes, opType);
    }

    @Override
    public void updateUniqueCodeStockPosition(Staff staff, List<String> uniqueCodes, boolean recordLog) {
        orderUniqueCodeService.updateUniqueCodeStockPosition(staff, uniqueCodes, recordLog);
    }

    @Override
    public List<WaveUniqueCode> afterSaleGenerate(Staff staff, AfterSaleGenerateUniqueCodeParams generateUniqueCodeParams) {
        return itemUniqueCodeService.afterSaleGenerate(staff, generateUniqueCodeParams);
    }

    @Override
    public void cancelAfterSaleUniqueCode(Staff staff, List<String> uniqueCodes) {
        itemUniqueCodeService.cancelAfterSaleUniqueCode(staff, uniqueCodes);
    }

    @Override
    public List<WaveUniqueCode> queryByCondition4AfterSale(Staff staff, ItemUniqueCodeQueryParams params) {
        return itemUniqueCodeService.queryByCondition4AfterSale(staff, params);
    }

    @Override
    @Deprecated
    public List<WaveUniqueCode> returnOrderScan(Staff staff, ReturnOrderUniqueCodeReceiveParams params)  throws WaveScanException{
        return orderUniqueCodeService.returnOrderScanPda(staff, params);
    }

    @Override
    public void recordWaveUniqueCodeLog(Staff staff, List<WaveUniqueCodeLog> logs) {
        waveUniqueCodeLogDao.batchInsert(staff, logs);
    }

    @Override
    public String analysisOrderUniqueCode(Staff staff, String scanCode) {
        return orderUniqueCodeAnalysisBusiness.analysisOrderUniqueCode(staff,scanCode);
    }


    @Override
    public void batchUpdateProcessStatus(Staff staff, List<String> uniqueCodes, Integer processStatus) {
        orderUniqueCodeService.batchUpdateProcessStatus(staff, uniqueCodes, processStatus);
    }

    @Override
    public void updateAfterSaleUniqueCode(Staff staff, List<WaveUniqueCode> codes, Boolean qualityType) {
        itemUniqueCodeService.updateAfterSaleUniqueCode(staff, codes, qualityType);
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodeByCodes(Staff staff, List<String> uniqueCodes) {
        return itemUniqueCodeService.queryItemUniqueCodeByCodes(staff, uniqueCodes);
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodeByCodes(Staff staff, List<String> uniqueCodes, Boolean convertSkuId) {
        return itemUniqueCodeService.queryItemUniqueCodeByCodes(staff, uniqueCodes, convertSkuId);
    }

    @Override
    public List<WaveUniqueCode> checkAndFilterScanUniqueCodes(Staff staff, List<String> uniqueCodes) {
        return itemUniqueCodeService.checkAndFilterScanUniqueCodes(staff, uniqueCodes);
    }

    @Override
    public List<WaveUniqueCode> returnToWarehouse(Staff staff, ReturnToWarehouseParams params) {
        return itemUniqueCodeService.returnToWarehouse(staff, params);
    }

    @Override
    public void cancelReturnToWarehouse(Staff staff, CancelReturnToWarehouseParams params) {
        itemUniqueCodeService.cancelReturnToWarehouse(staff, params);
    }

    @Override
    public void afterSaleOnShelf(Staff staff, AfterSaleOnShelfParams param) {
        itemUniqueCodeService.afterSaleOnShelf(staff, param);
    }

    @Override
    public void batchInsertUniqueCodeRelation(Staff staff, List<UniqueCodeRelation> uniqueCodeRelations) {
        uniqueCodeBaseService.batchInsertUniqueCodeRelation(staff, uniqueCodeRelations);
    }

    @Override
    public List<WaveUniqueCode> postBindGenerate(Staff staff, ItemUniqueCodeGenerateParams generateParams) {
        itemUniqueCodeGenerateBusiness.doGenerate(staff, generateParams, null);
        return generateParams.getInsertCodes();
    }

    @Override
    public UniqueCodeInfoQueryVO queryUniqueCodeInfo(Staff staff, UniqueCodeInfoQueryParams params) {
        return itemUniqueCodeService.queryUniqueCodeInfo(staff, params);
    }

    @Override
    public UniqueCodeInfoQueryVO queryUniqueCodeInfoNew(Staff staff, UniqueCodeInfoQueryParams params) {
        return uniqueCodeCommonQueryBusiness.queryUniqueCodeInfo(staff, params);
    }


    @Override
    public WaveUniqueCodeTag insert(Staff staff, WaveUniqueCodeTag tag) {
        return orderUniqueCodeTagService.insert(staff, tag);
    }

    @Override
    public void update(Staff staff, WaveUniqueCodeTag tag) {
        orderUniqueCodeTagService.update(staff, tag);
    }

    @Override
    public void batchDelete(Staff staff, List<Long> ids) {
        orderUniqueCodeTagService.batchDelete(staff, ids);
    }

    @Override
    public void updateUniqueCodeTag(Staff staff, WaveUniqueCodeTagParams params) {
        orderUniqueCodeTagService.updateUniqueCodeTag(staff, params);
    }

    @Override
    public PageListBase<WaveUniqueCodeTag> queryList(Staff staff, WaveUniqueCodeTagParams params) {
        return orderUniqueCodeTagService.queryList(staff, params);
    }

    @Override
    public List<WaveUniqueCode> thirdWarehouseGenerate(Staff staff, ItemUniqueCodeGenerateParams generateParams) {
        return thirdWarehouseUniqueCodeService.thirdWarehouseGenerate(staff, generateParams);
    }

    @Override
    public UniqueCodeResult thirdWarehouseModify(Staff staff, ItemUniqueCodeGenerateParams generateParams) {
        return thirdWarehouseUniqueCodeService.thirdWarehouseModify(staff, generateParams);
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodesByCodes(Staff staff, List<String> uniqueCodes) {
        return itemUniqueCodeService.queryItemUniqueCodesByCodes(staff, uniqueCodes);
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodesByCodes(Staff staff, List<String> uniqueCodes, Boolean fillRelation) {
        return itemUniqueCodeService.queryItemUniqueCodesByCodes(staff, uniqueCodes, fillRelation);
    }

    @Override
    public List<WaveUniqueCode> query4PrintHotSaleCode(Staff staff, List<String> hotSaleCodes) {
        return uniqueCodeExtendService.query4PrintHotSaleCode(staff, hotSaleCodes);
    }

    @Override
    public UniqueCodeResult establishRelationAndOut(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.establishRelationAndOut(staff, params);
    }

    @Override
    public UniqueCodeResult establishRelation(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.establishRelation(staff, params);
    }

    @Override
    public UniqueCodeResult establishRelationAndImport(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.establishRelationAndImport(staff, params);
    }

    @Override
    public UniqueCodeResult out(Staff staff, UniqueCodeGenericParams params) {
        params.setDirectOut(true);
        return uniqueCodeGenericService.out(staff, params);
    }

    @Override
    public UniqueCodeResult importCode(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.importCode(staff, params);
    }

    @Override
    public UniqueCodeResult generateCode(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.generateCode(staff, params);
    }

    @Override
    public UniqueCodeResult revert(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.revert(staff, params);
    }

    @Override
    public UniqueCodeResult receiveAndImport(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.receiveAndImport(staff, params);
    }

    @Override
    public UniqueCodeResult inWarehouse(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.inWarehouse(staff, params);
    }

    @Override
    public UniqueCodeResult moveStock(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.moveStock(staff, params);
    }

    @Override
    public UniqueCodeResult queryUniqueCodeByRelation(Staff staff, UniqueCodeGenericQueryParams params) {
        return uniqueCodeGenericService.queryUniqueCodeByRelation(staff, params);
    }

    @Override
    public List<WaveUniqueCode> updateReturnOrderReceiveCode(Staff staff, ReturnOrderUniqueCodeReceiveParams params, BatchResult<SuccessItem> result, List<WaveUniqueCode> uniqueCodeLis) {
        return orderUniqueCodeService.updateReturnOrderReceiveCode(staff, params, result, uniqueCodeLis);
    }

    @Override
    public void recordScanLog(Staff staff, ReturnOrderUniqueCodeReceiveParams params) {
        orderUniqueCodeService.recordScanLog(staff, params);
    }

    @Override
    public void batchInsert(Staff staff, List<WaveUniqueCodeLog> logs) {
        waveUniqueCodeLogDao.batchInsert(staff, logs);
    }

    @Override
    public List<WaveUniqueCode> getByUniqueOrGenericCode(Staff staff, ReturnOrderUniqueCodeReceiveParams params) throws WaveScanException {
        return orderUniqueCodeService.getByUniqueOrGenericCode(staff, params);
    }

    @Override
    public PdaFastScanReturnBO getByUniqueOrGenericCode(Staff staff,List<String> uniqueCodes) {
        return orderUniqueCodeService.getByUniqueOrGenericCode(staff,uniqueCodes);
    }

    @Override
    public AnalysisOrderUniqueCodesResult analysisOrderUniqueCodes(Staff staff, List<String> scanCodes) throws WaveScanException {
        return orderUniqueCodeAnalysisBusiness.analysisOrderUniqueCodes(staff, scanCodes, "-1", staff.getCompanyName());
    }

    @Override
    public AnalysisOrderUniqueCodesResult analysisOrderUniqueCodes(Staff staff, ReturnOrderUniqueCodeReceiveParams params) throws WaveScanException {
        return orderUniqueCodeAnalysisBusiness.analysisOrderUniqueCodes(staff, params.getUniqueCodes(), params.getShipperId(), params.getShipperName());
    }
    @Override
    public AnalysisOrderUniqueCodesResult fastAnalysisOrderUniqueCodes(Staff staff, List<String> scanCodes) {
        return orderUniqueCodeAnalysisBusiness.fastAnalysisOrderUniqueCodes(staff,scanCodes);
    }

    @Override
    public Boolean judgeOpenItemUniqueCode(Staff staff) {
        return uniqueCodeBaseService.judgeOpenItemUniqueCode(staff);
    }

    @Override
    public UniqueCodeResult releaseRelation(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.releaseRelation(staff, params);
    }

    @Override
    public PageListBase<WaveUniqueCode> queryItemUniqueCodeList(Staff staff, ItemUniqueCodeParams params) {
        return itemUniqueCodeService.queryItemUniqueCodeList(staff, params);
    }

    @Override
    public boolean openItemUniqueCodeOrOrderUniqueCode(Staff staff) {
        return (WaveUtils.openOrderUniqueCode(staff) || judgeOpenItemUniqueCode(staff));
    }

    @Override
    public WaveUniqueCodeValidateResult validateWaveUniqueCode(Staff staff, List<String> uniqueCodeList, Date startTime, Date endTime, Page page) {
        return itemUniqueCodeService.validateWaveUniqueCode(staff, uniqueCodeList, startTime, endTime, page);
    }


    @Override
    public void cancel4Trade(Staff staff, List<Long> sids, boolean includeMerge, UnboundReasonTypeEnum type) {
        orderUniqueCodeService.cancel4Trade(staff, sids, includeMerge, type);
    }
    @Override
    public UniqueCodeResult batchSupplierModify(Staff staff, UniqueCodeGenericParams params) {
        return uniqueCodeGenericService.batchSupplierModify(staff, params);
    }

    @Override
    public List<UniqueCodeRelation> queryRelationByUniqueCondition(Staff staff, OrderUniqueCodeQueryParams params) {
        return uniqueCodeRelationDao.queryByUniqueCondition(staff, params);
    }
    @Override
    public void handleOrderUniqueCodeGenerateRepeat(Staff staff, String sids) {
        orderUniqueCodeBackendService.handleOrderUniqueCodeGenerateRepeat(staff, sids);
    }
    @Override
    public BeforeArrivedCheckVO allocatePositionNo(Staff staff, UniqueCodeArrivedCheckParams param) {
        return uniqueCodeCrossBorderBusiness.allocatePositionNo(staff, param);
    }

    @Override
    public void updatePageColumnConf4UniqueCode(Staff staff, List<PageColumnConf> columnConfs) {
        waveColumnConfService.updatePageColumnConf4UniqueCode(staff, columnConfs);
    }

    @Override
    public void batchReturnExchange4PurchaseReturn(Staff staff, OrderUniqueCodeQueryParams params) {
        orderUniqueCodeService.batchReturnExchange4PurchaseReturn(staff, params);
    }
    @Override
    public void defectiveUniqueCodesAutoReturnExchange(Staff staff, List<WaveUniqueCode> defectiveUniqueCodes) {
        orderUniqueCodeService.defectiveUniqueCodesAutoReturnExchange(staff, defectiveUniqueCodes);
    }
    @Override
    public void cancelFastConsign(Staff staff, List<WaveUniqueCode> codes) {
        itemUniqueCodeExtendService.cancelFastConsign(staff, codes);
    }

    @Override
    public void moveStock4FastConsign(Staff staff, List<WaveUniqueCode> codes) {
        itemUniqueCodeExtendService.moveStock4FastConsign(staff, codes);
    }
    @Override
    public void cancelConsign(Staff staff, List<Long> orders) {
        orderUniqueCodeService.cancelConsign(staff, orders);
    }
    @Override
    public void generateDefaultRule(Staff staff) {
        itemUniqueCodeGenerateRuleService.generateDefaultRule(staff);
    }

}

package com.raycloud.dmj.services.trades.support.wave;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.dao.wave.OrderUniqueCodeUnboundLogDao;
import com.raycloud.dmj.business.wave.WaveHelpBusiness;
import com.raycloud.dmj.dao.stock.StockOrderRecordDAO;
import com.raycloud.dmj.dao.wave.UniqueCodeRelationDao;
import com.raycloud.dmj.dao.wave.WavePickingDao;
import com.raycloud.dmj.dao.wave.WaveUniqueCodeDao;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.stock.StockOrderRecord;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.trades.utils.WaveUniqueCodeLogUtils;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.model.*;
import com.raycloud.dmj.domain.wave.utils.UniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.GoodsSection;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.domain.wms.enums.WmsConfigExtInfoEnum;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.basis.ISupplierService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.trades.support.wave.proxy.WaveUseTradeServiceProxy;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.web.model.wms.GoodsSectionVo;
import com.raycloud.dmj.web.source.OperateSourceContext;
import com.raycloud.dmj.utils.wms.WmsKeyUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UniqueCodeBaseService implements IUniqueCodeBaseService {

    private Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private WaveUniqueCodeDao waveUniqueCodeDao;
    @Resource
    private UniqueCodeRelationDao uniqueCodeRelationDao;
    @Resource
    private ISupplierService supplierService;
    @Resource
    private IWarehouseService warehouseService;
    @Resource
    private IWmsService wmsService;
    @Resource
    private IItemUniqueCodeGenerateRuleService itemUniqueCodeGenerateRuleService;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private WavePickingDao wavePickingDao;
    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;
    @Resource
    private OrderUniqueCodeUnboundLogDao unboundLogDao;

    @Resource
    private WaveUseTradeServiceProxy waveUseTradeServiceProxy;
    @Resource
    private IItemUniqueCodeExtendService itemUniqueCodeExtendService;
    @Resource
    private IOrderUniqueCodeExtendService orderUniqueCodeExtendService;
    @Resource
    private ICompanyService companyService;
    @Resource
    private IOrderUniqueCodeService orderUniqueCodeService;

    @Resource
    private StockOrderRecordDAO stockOrderRecordDao;
    @Resource
    private WaveHelpBusiness waveHelpBusiness;

    @Override
    @Transactional
    public UniqueCodeResult establishRelation(Staff staff, UniqueCodeGenericParams params) {
        UniqueCodeResult result = new UniqueCodeResult();
        UniqueCodeUtils.baseGenericParamsCheck(staff, params, result);
        establishRelationCheck(staff, params, result);
        uniqueCodeExistCheck(staff, params, result);
        relationUniqueCodeCheck(staff, params, result);
        releaseRelation4TakenOrder(staff, params, result);
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return result;
        }
        insertRelation(staff, result, result.getData(), params);
        return result;
    }

    @Transactional
    public void releaseRelation4TakenOrder(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result) {
        if (!Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.TAKEN_ORDER.getType()) ||
                CollectionUtils.isEmpty(result.getData())) {
            return;
        }
        OrderUniqueCodeQueryParams relationParams = new OrderUniqueCodeQueryParams();
        relationParams.setUniqueCodes(result.getData().stream().map(WaveUniqueCode::getUniqueCode).distinct().collect(Collectors.toList()));
        relationParams.setBusinessType(UniqueCodeRelation.BusinessType.TAKEN_ORDER.getType());
        List<UniqueCodeRelation> relations = uniqueCodeRelationDao.queryByUniqueCondition(staff, relationParams);
        if (CollectionUtils.isEmpty(relations)) {
            return;
        }

        List<UniqueCodeRelation> uniqueCodeRelations = Lists.newArrayList();
        for (UniqueCodeRelation relation : relations) {
            UniqueCodeRelation updateRelation = new UniqueCodeRelation();
            updateRelation.setUniqueCode(relation.getUniqueCode());
            updateRelation.setBusinessCode(relation.getBusinessCode());
            updateRelation.setEnableStatus(0);
            uniqueCodeRelations.add(updateRelation);
        }

        uniqueCodeRelationDao.batchUpdateByCode(staff, uniqueCodeRelations);
    }

    @Override
    public void changeStockPosition(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result) {
        UniqueCodeUtils.baseGenericParamsCheck(staff, params, result);
        changeStockPositionCheck(params, result);
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return;
        }
        updateUniqueCode4MoveStock(staff, params, result);
    }

    @Override
    public void logOnShelf(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result) {
        if (!Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.ON_SHELF.getType()) ||
                StringUtils.isEmpty(params.getSubLogTemplate())) {
            return;
        }
        UniqueCodeUtils.baseGenericParamsCheck(staff, params, result);
        changeStockPositionCheck(params, result);
        Map<String, UniqueCodeGenericParams.ItemGenericParams> codeMap = getStringItemGenericParamsMap(params, result);
        if (codeMap == null) {
            return;
        }
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> updateCodeLogs = Lists.newArrayList();
        for (WaveUniqueCode exist : result.getData()) {
            UniqueCodeGenericParams.ItemGenericParams codeParam = codeMap.get(exist.getUniqueCode());
            if (codeParam == null) {
                continue;
            }
            updateCodeLogs.add(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO()
                    .setUniqueCode(exist.getUniqueCode())
                    .setGoodsSectionCode(codeParam.getGoodsSectionCode()));
        }
        if (CollectionUtils.isNotEmpty(updateCodeLogs)) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, updateCodeLogs, WaveUniqueOpType.PDA_ON_SHELF, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(params.getSubLogTemplate())}), null);
        }
    }

    private void relationUniqueCodeCheck(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result) {
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return;
        }
        //订单类型 校验唯一码是否绑定过订单
        if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.TRADE.getType())) {
            List<WaveUniqueCode> uniqueCodes = result.getData();
            for (WaveUniqueCode waveUniqueCode : uniqueCodes) {
                if (waveUniqueCode.getSid() != null && waveUniqueCode.getSid() > 0) {
                    result.getFailCodeInfos().add(new UniqueCodeResult.FailCodeInfo(waveUniqueCode.getUniqueCode(), "唯一码已绑定订单，sid:" + waveUniqueCode.getSid()));
                }
            }
            if (CollectionUtils.isNotEmpty(result.getFailCodeInfos())) {
                UniqueCodeResult.setFailInfo("500", "存在已绑定订单的唯一码", result);
            }
        }
    }

    private static void establishRelationCheck(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result) {
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return;
        }

        if (params.getBusinessType() == null) {
            UniqueCodeResult.setFailInfo("500", "单据类型不能为空！", result);
            return;
        }

        for (UniqueCodeGenericParams.ItemGenericParams item : params.getItemParams()) {
            if (StringUtils.isEmpty(item.getUniqueCode())) {
                result.getFailCodeInfos().add(new UniqueCodeResult.FailCodeInfo("", "唯一码不能为空！"));
            }
            if (StringUtils.isEmpty(item.getBusinessCode())) {
                result.getFailCodeInfos().add(new UniqueCodeResult.FailCodeInfo(item.getUniqueCode(), "单据号不能为空！"));
            }
            if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.TRADE.getType())) {
                if (!DataUtils.checkLongNotEmpty(item.getBusinessId()) || !DataUtils.checkLongNotEmpty(item.getSubBusinessId())) {
                    result.getFailCodeInfos().add(new UniqueCodeResult.FailCodeInfo(item.getUniqueCode(), "订单号不能为空！"));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(result.getFailCodeInfos())) {
            UniqueCodeResult.setFailInfo("500", "参数不正确", result);
        }
    }

    private void changeStockPositionCheck(UniqueCodeGenericParams params, UniqueCodeResult result) {
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return;
        }

        // 判断库存位置
        boolean judgeStockPosition = Objects.equals(UniqueCodeRelation.BusinessType.WAVE_PICKING_ID.getType(), params.getBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.OFF_SHELF.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.ALLOCATE_IN.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.OTHER_IN.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_EMPTY_BOX.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.PACK_MOVE_STOCK.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.PRODUCT_ORDER_IN_WAREHOUSE.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.REPAIR_ORDER_RETURN.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.MOVE_STOCK_IN.getType(), params.getSubBusinessType());
        // 判断货位
        boolean judgeGoodsSectionCode = !judgeStockPosition;
        // 不需要判断位置, 针对既可以到货位又可以到暂存区的动作
        boolean needNotJudgePosition = Objects.equals(UniqueCodeGenericParams.SubBusinessType.SPLIT_BOX.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.STOCK_PRODUCT_FINISH.getType(), params.getSubBusinessType());

        for (UniqueCodeGenericParams.ItemGenericParams item : params.getItemParams()) {
            if (StringUtils.isEmpty(item.getUniqueCode())) {
                result.getFailCodeInfos().add(new UniqueCodeResult.FailCodeInfo("", "唯一码不能为空！"));
            }
            if (needNotJudgePosition) {
                continue;
            }
            if (judgeGoodsSectionCode && StringUtils.isEmpty(item.getGoodsSectionCode())) {
                result.getFailCodeInfos().add(new UniqueCodeResult.FailCodeInfo("", "移动货位不能为空！"));
            }
            if (judgeStockPosition && !DataUtils.checkIntegerNotEmpty(item.getStockPosition())) {
                result.getFailCodeInfos().add(new UniqueCodeResult.FailCodeInfo("", "库存位置不能为空！"));
            }
        }

        if (CollectionUtils.isNotEmpty(result.getFailCodeInfos())) {
            UniqueCodeResult.setFailInfo("500", "参数不正确", result);
        }
    }

    private void updateUniqueCode4MoveStock(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result) {
        Map<String, UniqueCodeGenericParams.ItemGenericParams> codeMap = getStringItemGenericParamsMap(params, result);
        if (codeMap == null || codeMap.isEmpty()) {
            return;
        }
        Map<String, String> boxCodeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(params.getRelations())) {
            boxCodeMap = params.getRelations().stream().collect(Collectors.toMap(UniqueCodeRelation::getUniqueCode, UniqueCodeRelation::getBusinessCode, (a, b) -> a));
        }
        List<WaveUniqueCode> updates = Lists.newArrayList();
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> updateCodeLogs = Lists.newArrayList();
        boolean moveStockPosition = Objects.equals(UniqueCodeRelation.BusinessType.WAVE_PICKING_ID.getType(), params.getBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.OFF_SHELF.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.ALLOCATE_IN.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.OTHER_IN.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.PACK_MOVE_STOCK.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_EMPTY_BOX.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.PRODUCT_ORDER_IN_WAREHOUSE.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.REPAIR_ORDER_RETURN.getType(), params.getSubBusinessType()) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.MOVE_STOCK_IN.getType(), params.getSubBusinessType());
        Map<Long, String> warehouseNameMap = getWarehouseNameMap(params, result.getData());
        for (WaveUniqueCode exist : result.getData()) {
            UniqueCodeGenericParams.ItemGenericParams codeParam = codeMap.get(exist.getUniqueCode());
            if (codeParam == null) {
                continue;
            }
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(exist.getId());
            update.setStatus(getStatus4MoveStock(params.getSubBusinessType()));
            update.setWarehouseId(codeParam.getWarehouseId());
            if (Objects.equals(UniqueCodeGenericParams.SubBusinessType.SPLIT_BOX.getType(), params.getSubBusinessType()) ||
                    Objects.equals(UniqueCodeGenericParams.SubBusinessType.STOCK_PRODUCT_FINISH.getType(), params.getSubBusinessType())) {
                // 拆箱根据有无传库存位置字段判断移动位置
                moveStockPosition = DataUtils.checkIntegerNotEmpty(codeParam.getStockPosition());
            }
            update.setStockPosition(moveStockPosition ? codeParam.getStockPosition() : UniqueCodeStockPositionEnum.SHELVES.getType());
            update.setGoodsSectionCode(moveStockPosition ? "" : codeParam.getGoodsSectionCode());
            update.setGoodsSectionId(moveStockPosition ? Long.valueOf(0L) : codeParam.getGoodsSectionId());
            if (StringUtils.isNotEmpty(codeParam.getItemBatchNo())) {
                update.setItemBatchNo(codeParam.getItemBatchNo());
            }
            update.setProductionDate(codeParam.getProductionDate());
            updates.add(update);

            updateCodeLogs.add(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO()
                    .setUniqueCode(exist.getUniqueCode())
                    .setGoodsSectionCode(getGoodsSectionCode4Log(params.getSubBusinessType(), codeParam))
                    .setStockPosition(codeParam.getStockPosition())
                    .setBeforeStockPosition(exist.getStockPosition())
                    .setBeforeBoxCode(boxCodeMap.get(exist.getUniqueCode()) == null ? "" : boxCodeMap.get(exist.getUniqueCode()))
                    .setBoxCode(codeParam.getBusinessCode())
                    .setBusinessCode(codeParam.getBusinessCode())
                    .setBeforeWarehouseName(getBeforeWarehouseName4MoveStock(warehouseNameMap, codeParam.getFromWarehouseId(), exist.getWarehouseId()))
                    .setWarehouseName(warehouseNameMap.get(codeParam.getWarehouseId()))
                    .setOldStatus(exist.getStatus()));
        }
        if (CollectionUtils.isNotEmpty(updates)) {
            waveUniqueCodeDao.batchUpdate(staff, updates);
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, updateCodeLogs, getWaveUniqueOpType(params.getBusinessType(), params.getSubBusinessType()), null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(params.getLogTemplate())}), null);
        }
    }

    private Integer getStatus4MoveStock(Integer subBusinessType) {
        // 验货出库库存平移，唯一码状态不改变为在库
        // 包装验货库存平移，唯一码状态不改变为在库
        if (Objects.equals(UniqueCodeGenericParams.SubBusinessType.MOVE_STOCK_IN.getType(), subBusinessType) ||
                Objects.equals(UniqueCodeGenericParams.SubBusinessType.PACK_MOVE_STOCK.getType(), subBusinessType)) {
            return null;
        }
        // 维修单退回入库
        if (Objects.equals(UniqueCodeGenericParams.SubBusinessType.REPAIR_ORDER_RETURN.getType(), subBusinessType)) {
            return OrderUniqueCodeStatusEnum.RETURN.getType();
        }
        return OrderUniqueCodeStatusEnum.IN.getType();
    }

    private String getGoodsSectionCode4Log(Integer subBusinessType, UniqueCodeGenericParams.ItemGenericParams codeParam) {
        if (Objects.equals(UniqueCodeGenericParams.SubBusinessType.OFF_SHELF.getType(), subBusinessType)) {
            return codeParam.getFromGoodsSectionCode();
        }
        return codeParam.getGoodsSectionCode();
    }

    private String getBeforeWarehouseName4MoveStock(Map<Long, String> warehouseNameMap, Long fromWarehouseId, Long existWarehouseId) {
        if (DataUtils.checkLongNotEmpty(fromWarehouseId)) {
            return warehouseNameMap.get(fromWarehouseId);
        }
        return warehouseNameMap.get(existWarehouseId);
    }

    public Map<Long, String> getWarehouseNameMap(UniqueCodeGenericParams params, List<WaveUniqueCode> exists) {
        if (!Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_AND_OUT.getType()) &&
                !Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.OFF_SHELF.getType()) &&
                !Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.MOVE_STOCK.getType()) &&
                !Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.OTHER_IN.getType()) &&
                !Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.OTHER_IN_ON_SHELF.getType()) &&
                !Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_ESTABLISH_RELATION_AND_OUT.getType())) {
            return Maps.newHashMap();
        }
        List<Long> paramWarehouseIds = params.getItemParams().stream().filter(p -> DataUtils.checkLongNotEmpty(p.getWarehouseId())).map(UniqueCodeGenericParams.ItemGenericParams::getWarehouseId).distinct().collect(Collectors.toList());
        List<Long> fromParamWarehouseIds = params.getItemParams().stream().filter(p -> DataUtils.checkLongNotEmpty(p.getFromWarehouseId())).map(UniqueCodeGenericParams.ItemGenericParams::getFromWarehouseId).distinct().collect(Collectors.toList());
        List<Long> existWarehouseIds = exists.stream().filter(e -> DataUtils.checkLongNotEmpty(e.getWarehouseId())).map(WaveUniqueCode::getWarehouseId).distinct().collect(Collectors.toList());
        paramWarehouseIds.addAll(fromParamWarehouseIds);
        paramWarehouseIds.addAll(existWarehouseIds);
        if (CollectionUtils.isEmpty(paramWarehouseIds)) {
            return Maps.newHashMap();
        }
        List<Warehouse> warehouses = warehouseService.queryByIds(paramWarehouseIds);
        if (CollectionUtils.isEmpty(warehouses)) {
            return Maps.newHashMap();
        }
        return warehouses.stream().collect(Collectors.toMap(Warehouse::getId, Warehouse::getName, (a, b) -> a));
    }

    private WaveUniqueOpType getWaveUniqueOpType(Integer businessType, Integer subBusinessType) {
        if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.MOVE_BOX.getType()) ||
                Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.PART_MOVE_BOX.getType())) {
            return WaveUniqueOpType.MOVE_BOX;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.SPLIT_BOX.getType())) {
            return WaveUniqueOpType.SPLIT_BOX;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.CHANGE_BOX.getType())) {
            return WaveUniqueOpType.CHANGE_BOX;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.WAVE_PICK_ITEM.getType())) {
            return WaveUniqueOpType.WAVE_PICK_ITEM;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.WAVE_PICK_ITEM_BOX.getType()) ||
                Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.PART_WAVE_PICK_ITEM_BOX.getType())) {
            return WaveUniqueOpType.WAVE_PICK_ITEM_BOX;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.ON_SHELF_IN_WAREHOUSE.getType()) ||
                Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.ON_SHELF_EMPTY_BOX.getType())) {
            return WaveUniqueOpType.PDA_ON_SHELF;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_EMPTY_BOX.getType())) {
            return WaveUniqueOpType.PDA_OFF_SHELF;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.ALLOCATE_IN.getType())) {
            return WaveUniqueOpType.ALLOCATE_IN;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.ALLOCATE_IN_GOODS_SECTION.getType())) {
            return WaveUniqueOpType.ALLOCATE_IN_GOODS_SECTION;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF.getType())) {
            if (Objects.equals(businessType, UniqueCodeRelation.BusinessType.UNSHELVE_ORDER.getType())) {
                return WaveUniqueOpType.UNSHELVE_ORDER_OFF_SHELF;
            }
            return WaveUniqueOpType.OFF_SHELF_AND_OUT;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.MOVE_STOCK.getType())) {
            return WaveUniqueOpType.ITEM_MOVE_STOCK;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.OTHER_IN.getType())) {
            return WaveUniqueOpType.OTHER_WAREHOUSING_IN;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.OTHER_IN_ON_SHELF.getType())) {
            return WaveUniqueOpType.OTHER_WAREHOUSING_IN_ON_SHELF;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.PACK_MOVE_STOCK.getType())) {
            return WaveUniqueOpType.PACK_MOVE_STOCK;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.PRODUCT_ORDER_IN_WAREHOUSE.getType())) {
            return WaveUniqueOpType.PRODUCT_ORDER_IN_WAREHOUSE;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.REPAIR_ORDER_RETURN.getType())) {
            return WaveUniqueOpType.REPAIR_ORDER_RETURN;
        } else if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.STOCK_PRODUCT_FINISH.getType())) {
            return WaveUniqueOpType.STOCK_PRODUCT_FINISH;
        } else {
            return WaveUniqueOpType.MOVE_STOCK;
        }
    }

    private Map<String, UniqueCodeGenericParams.ItemGenericParams> getStringItemGenericParamsMap(UniqueCodeGenericParams params, UniqueCodeResult result) {
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return null;
        }
        if (CollectionUtils.isEmpty(result.getData())) {
            UniqueCodeResult.setFailInfo("500", "唯一码不存在, 无可更新数据！", result);
            return null;
        }

        return params.getItemParams().stream().collect(Collectors.toMap(UniqueCodeGenericParams.ItemGenericParams::getUniqueCode, a -> a, (a1, a2) -> a1));
    }

    @Override
    public void uniqueCodeExistCheck(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result) {
        uniqueCodeExistCheckNew(staff, params, result, null);
    }

    public void uniqueCodeExistCheckNew(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result, UniqueCodeResult resultOrigin) {
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return;
        }

        if (DataUtils.checkIntegerNotEmpty(params.getBatchNo()) && DataUtils.checkIntegerNotEmpty(params.getDateNo())) {
            List<WaveUniqueCode> uniqueCodes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, new ItemUniqueCodeQueryParams().setNeedCategroyAndCatName(false).setNeedRelationMsg(false).setBatchNo(params.getBatchNo()).setDateNo(params.getDateNo()));
            result.setData(uniqueCodes);
            return;
        }

        List<String> uniqueCodes = new ArrayList<>();
        List<UniqueCodeResult.FailCodeInfo> failCodeInfos = new ArrayList<>();
        result.setFailCodeInfos(failCodeInfos);
        // 临时的唯一码, 避免和真实的重复从-1往后减
        int tempId = -1;
        for (UniqueCodeGenericParams.ItemGenericParams item : params.getItemParams()) {
            if (StringUtils.isNotEmpty(item.getUniqueCode())) {
                if (uniqueCodes.contains(item.getUniqueCode())) {
                    failCodeInfos.add(new UniqueCodeResult.FailCodeInfo(item.getPurchaseOrderCode(), item.getUniqueCode(), "唯一码重复！"));
                    continue;
                }
                uniqueCodes.add(item.getUniqueCode());
                continue;
            }
            if (params.isPurchaseGenerateUniqueCode()) {
                // 采购单审核生成唯一码不会传唯一码，附个值躲过导入的唯一码参数校验
                item.setUniqueCode((tempId--) + "").setVirtualUniqueCode(true);
            }
        }
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            if(params.isPurchaseGenerateUniqueCode()){
                for (UniqueCodeGenericParams.ItemGenericParams item : params.getItemParams()) {
                    failCodeInfos.add(new UniqueCodeResult.FailCodeInfo(item.getPurchaseOrderCode(), item.getUniqueCode(), "唯一码不存在！"));
                }
            }
            UniqueCodeResult.setFailInfo("500", "唯一码全部不存在, 无可更新数据", result);
            return;
        }
        if (CollectionUtils.isNotEmpty(result.getFailCodeInfos())) {
            // 唯一码重复报错
            UniqueCodeResult.setFailInfo("500", "唯一码重复!", result);
            UniqueCodeResult.setFailInfo("500", "唯一码重复!", resultOrigin);
            if (resultOrigin != null) {
                resultOrigin.setFailCodeInfos(result.getFailCodeInfos());
            }
            return;
        }

        List<WaveUniqueCode> exists = itemUniqueCodeService.queryItemUniqueCodesByCodes(staff, uniqueCodes, params.isFillRelation());
        if (CollectionUtils.isEmpty(exists)) {
            for (UniqueCodeGenericParams.ItemGenericParams item : params.getItemParams()) {
                failCodeInfos.add(new UniqueCodeResult.FailCodeInfo(item.getPurchaseOrderCode(), item.getUniqueCode(), "唯一码不存在！"));
            }
            UniqueCodeResult.setFailInfo("500", "唯一码全部不存在, 无可更新数据", result);
            return;
        }

        Map<String, WaveUniqueCode> existMap = exists.stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, a -> a, (a1, a2) -> a1));
        List<WaveUniqueCode> filters = new ArrayList<>();
        for (UniqueCodeGenericParams.ItemGenericParams item : params.getItemParams()) {
            WaveUniqueCode code = existMap.get(item.getUniqueCode());
            if (code == null) {
                failCodeInfos.add(new UniqueCodeResult.FailCodeInfo(item.getPurchaseOrderCode(), item.getUniqueCode(), "唯一码不存在！"));
                continue;
            }
            filters.add(code);
        }
        result.setData(filters);
        if (Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.PROCESS_ORDER_CANCEL.getType())) {
            params.setReleaseSids(exists.stream().filter(e -> DataUtils.checkLongNotEmpty(e.getSid())).map(WaveUniqueCode::getSid).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(result.getFailCodeInfos())) {
            UniqueCodeResult.setFailInfo("500", "唯一码不存在", result);
        }
    }

    @Override
    public UniqueCodeUtils.TradeNumResult getTradeNumResult(Staff staff, List<Long> sids) {
        return getTradeNumResult(staff, sids, false);
    }

    @Override
    public UniqueCodeUtils.TradeNumResult getTradeNumResult(Staff staff, List<Long> sids, boolean returnTrades) {
        UniqueCodeUtils.TradeNumResult result = new UniqueCodeUtils.TradeNumResult();
        if (CollectionUtils.isEmpty(sids)) {
            return result;
        }
        List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, true, sids.toArray(new Long[0]));
        if (CollectionUtils.isNotEmpty(trades)) {
            result = UniqueCodeUtils.getTradeNumResult(trades);
            if (returnTrades) {
                result.setTrades(trades);
            }
        }
        return result;
    }

    private void fillParam4PurchaseOrder(Staff staff, List<Long> sids, UniqueCodeGenericParams params) {
        if (!params.isPurchaseGenerateUniqueCode() || CollectionUtils.isEmpty(sids)) {
            return;
        }
        // 查合单隐藏单
        List<Trade> trades = waveUseTradeServiceProxy.queryBySidsContainMergeTrade(staff, true, false, sids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        // 订单状态映射
        Map<Long, String> sysStatusMap = trades.stream().collect(Collectors.toMap(Trade::getSid, Trade::getSysStatus, (a, b) -> a));
        // 子单到主单的映射
        Map<Long, Long> sidMap = trades.stream().filter(t -> DataUtils.checkLongNotEmpty(t.getMergeSid()) && !Objects.equals(t.getSid(), t.getMergeSid())).collect(Collectors.toList()).stream().collect(Collectors.toMap(Trade::getSid, Trade::getMergeSid, (a, b) -> a));
        // 合单的主单或非合单订单
        List<Trade> normalTrades = waveUseTradeServiceProxy.queryBySids(staff, true, trades.stream().filter(t -> !DataUtils.checkLongNotEmpty(t.getMergeSid()) || (DataUtils.checkLongNotEmpty(t.getMergeSid()) && Objects.equals(t.getSid(), t.getMergeSid()))).map(Trade::getSid).distinct().collect(Collectors.toList()).toArray(new Long[0]));

        // 订单商品有效数量
        Map<Long, Integer> tradeNumMap = Maps.newHashMap();
        Map<Long, Integer> orderNumMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(normalTrades)) {
            UniqueCodeUtils.TradeNumResult result = UniqueCodeUtils.getTradeNumResult(normalTrades, true, false);
            tradeNumMap = result.getTradeNumMap();
            orderNumMap = result.getOrderNumMap();
        }
        List<UniqueCodeGenericParams.ItemGenericParams> itemParamsCopy = Lists.newArrayList();
        for (UniqueCodeGenericParams.ItemGenericParams item : params.getItemParams()) {
            if (Objects.equals(item.getCodeType(), 3)) {
                // 备货类型不过滤
                itemParamsCopy.add(item);
                continue;
            }
            if (orderNumMap.get(item.getSubBusinessId()) == null) {
                if (Objects.equals(sysStatusMap.get(item.getBusinessId()), Trade.SYS_STATUS_WAIT_AUDIT)) {
                    // 订单反审核后改为生成备货唯一码
                    itemParamsCopy.add(item.setCodeType(3).setBusinessId(null).setSubBusinessId(null));
                }
                // 不符合条件的订单商品跳过生成
                continue;
            }
            Long mergeSid = sidMap.get(item.getBusinessId());
            if (mergeSid != null) {
                // 子单取主单的id
                item.setBusinessId(mergeSid);
            }
            // 填充非备货唯一码单多类型
            if (StringUtils.isEmpty(item.getUniqueCode()) && item.getCodeType() == null) {
                Integer itemNum = tradeNumMap.get(item.getBusinessId());
                // 填充单多类型
                item.setCodeType(itemNum != null && itemNum > 1 ? 2 : 1);
            }
            itemParamsCopy.add(item);
        }
        params.setItemParams(itemParamsCopy);
        params.setTradeNumMap(tradeNumMap);

        // 订单有货部分根据库存锁定记录生成在库唯一码
        buildParamsByStockOrderRecord(staff, params, trades);
    }

    @Transactional
    public void insertRelation(Staff staff, UniqueCodeResult result, List<WaveUniqueCode> exists, UniqueCodeGenericParams params) {
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return;
        }

        Map<String, UniqueCodeGenericParams.ItemGenericParams> codeMap = params.getItemParams().stream().collect(Collectors.toMap(UniqueCodeGenericParams.ItemGenericParams::getUniqueCode, a -> a, (a1, a2) -> a1));
        //订单类型
        if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.TRADE.getType())) {
            relationTrade(staff, exists, params, codeMap);
            return;
        }
        List<UniqueCodeRelation> relations = Lists.newArrayList();
        List<WaveUniqueCode> updates4PurchaseOrder = Lists.newArrayList();
        for (WaveUniqueCode exist : exists) {
            UniqueCodeGenericParams.ItemGenericParams relation = codeMap.get(exist.getUniqueCode());
            if (relation == null) {
                continue;
            }
            if (handlePurchaseOrder(relations, params, exist, relation, updates4PurchaseOrder)) {
                continue;
            }
            if (handleWarehouseEntryOrder(relations, params, exist, relation, updates4PurchaseOrder)) {
                continue;
            }
            if (Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.PURCHASE_ORDER_REUSE.getType())) {
                continue;
            }
            handleCommon(relations, params, exist, relation);
        }
        if (params.isPurchaseGenerateUniqueCode()) {
            // 绑定采购单日志
            log4BindPurchaseOrder(staff, updates4PurchaseOrder, params.getLogTemplate());
        }
        uniqueCodeRelationDao.batchInsert(staff, relations);
        waveUniqueCodeDao.batchUpdate(staff, updates4PurchaseOrder);
        log4BindRelation(staff, params, relations);
    }

    private void log4BindRelation(Staff staff, UniqueCodeGenericParams params, List<UniqueCodeRelation> relations) {
        boolean skip = (!Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.BOX_CODE.getType()) && !Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.TAKEN_ORDER.getType())) || CollectionUtils.isEmpty(relations);
        if (skip) {
            return;
        }
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> dtos = Lists.newArrayList();
        for (UniqueCodeRelation relation : relations) {
            dtos.add(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO()
                    .setUniqueCode(relation.getUniqueCode())
                    .setBusinessCode(relation.getBusinessCode()));
        }

        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace")
                .setArgs(new Object[]{staff, dtos, WaveUniqueOpType.ITEM_UNIQUE_CODE_PURCHASE_BIND, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(params.getLogTemplate())}), null);
    }

    private void relationTrade(Staff staff, List<WaveUniqueCode> exists, UniqueCodeGenericParams params, Map<String, UniqueCodeGenericParams.ItemGenericParams> codeMap) {
        if (CollectionUtils.isEmpty(exists) || MapUtils.isEmpty(codeMap)) {
            return;
        }
        String logTemplate = params.getLogTemplate();
        Map<Long, Integer> tradeNumMap = params.getTradeNumMap();
        if (tradeNumMap == null) {
            tradeNumMap = getTradeNumResult(staff, params.getItemParams().stream().filter(i -> DataUtils.checkLongNotEmpty(i.getBusinessId())).map(UniqueCodeGenericParams.ItemGenericParams::getBusinessId).distinct().collect(Collectors.toList())).getTradeNumMap();
        }
        List<WaveUniqueCode> updates = new ArrayList<>();
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> codeTypeChangeLogs = Lists.newArrayList();
        for (WaveUniqueCode code : exists) {
            UniqueCodeGenericParams.ItemGenericParams relation = codeMap.get(code.getUniqueCode());
            if (relation == null) {
                continue;
            }
            WaveUniqueCode update = new  WaveUniqueCode();
            update.setId(code.getId());
            update.setSid(relation.getBusinessId());
            update.setOrderId(relation.getSubBusinessId());
            Integer tradeNum, codeType;
            if ((tradeNum = tradeNumMap.get(relation.getBusinessId())) != null && !Objects.equals(code.getCodeType(), codeType = tradeNum > 1 ? 2 : 1)) {
                update.setCodeType(codeType);
                codeTypeChangeLogs.add(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO().setUniqueCode(code.getUniqueCode()).setOldCodeType(code.getCodeType()).setCodeType(codeType));
            }
            updates.add(update);
            code.setSid(relation.getBusinessId());
            code.setOrderId(relation.getSubBusinessId());
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> waveUniqueCodeLogDTOS = WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(exists);

        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace")
                .setArgs(new Object[]{staff, waveUniqueCodeLogDTOS, WaveUniqueOpType.ITEM_UNIQUE_CODE_TRADE_BIND, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(logTemplate)}), null);

        // 唯一码修改单多类型：唯一码类型修改：单/多/备->单/多/备
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, codeTypeChangeLogs, WaveUniqueOpType.UNIQUE_CODE_UPDATE_CODE_TYPE}), null);
    }

    private boolean handlePurchaseOrder(List<UniqueCodeRelation> relations, UniqueCodeGenericParams params, WaveUniqueCode exist, UniqueCodeGenericParams.ItemGenericParams relation, List<WaveUniqueCode> updates4PurchaseOrder) {
        boolean skip = !Objects.equals(UniqueCodeRelation.BusinessType.PURCHASE_ORDER.getType(), params.getBusinessType()) || StringUtils.isEmpty(relation.getPurchaseOrderCode());
        if (skip) {
            return false;
        }
        fill4PurchaseOrder(relations, exist.getId(), exist.getUniqueCode(), relation.getPurchaseOrderId(), relation.getPurchaseOrderCode(), UniqueCodeRelation.BusinessType.PURCHASE_ORDER.getType(), updates4PurchaseOrder);
        return true;
    }

    private void log4BindPurchaseOrder(Staff staff, List<WaveUniqueCode> updates4PurchaseOrder, String logTemplate) {
        if (CollectionUtils.isEmpty(updates4PurchaseOrder)) {
            return;
        }
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> waveUniqueCodeLogDTOS = WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(updates4PurchaseOrder);

        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace")
                .setArgs(new Object[]{staff, waveUniqueCodeLogDTOS, WaveUniqueOpType.ITEM_UNIQUE_CODE_PURCHASE_BIND, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(logTemplate)}), null);
    }

    private boolean handleWarehouseEntryOrder(List<UniqueCodeRelation> relations, UniqueCodeGenericParams params, WaveUniqueCode exist, UniqueCodeGenericParams.ItemGenericParams relation, List<WaveUniqueCode> updates4PurchaseOrder) {
        boolean skip = !Objects.equals(UniqueCodeRelation.BusinessType.WAREHOUSE_ENTRY_ORDER.getType(), params.getBusinessType()) || StringUtils.isEmpty(relation.getWarehouseEntryCode());
        if (skip) {
            return false;
        }
        fillRelation4WarehouseEntry(relations, exist.getId(), exist.getUniqueCode(), relation.getWarehouseEntryCode(), params.getBusinessType());
        fill4PurchaseOrder(relations, exist.getId(), exist.getUniqueCode(), relation.getPurchaseOrderId(), relation.getPurchaseOrderCode(), UniqueCodeRelation.BusinessType.PURCHASE_ORDER.getType(), updates4PurchaseOrder);
        fillRelation4WarehouseEntry(relations, exist.getId(), exist.getUniqueCode(), relation.getPurchaseReturnCode(), UniqueCodeRelation.BusinessType.PURCHASE_RETURN_ORDER.getType());
        return true;
    }

    private void fill4PurchaseOrder(List<UniqueCodeRelation> relations, Long id, String uniqueCode, Long purchaseOrderId, String purchaseOrderCode, Integer businessType, List<WaveUniqueCode> updates4PurchaseOrder) {
        if (StringUtils.isEmpty(purchaseOrderCode) || !DataUtils.checkIntegerNotEmpty(businessType)) {
            return;
        }
        WaveUniqueCode update = new WaveUniqueCode();
        update.setId(id);
        update.setUniqueCode(uniqueCode);
        update.setBusinessCode(purchaseOrderCode);
        update.setBusinessId(DataUtils.getZeroIfDefault(purchaseOrderId));
        updates4PurchaseOrder.add(update);

        relations.add(new UniqueCodeRelation()
                .setUniqueCodeId(id)
                .setUniqueCode(uniqueCode)
                .setBusinessId(DataUtils.getZeroIfDefault(purchaseOrderId))
                .setBusinessCode(purchaseOrderCode)
                .setBusinessType(UniqueCodeRelation.BusinessType.PURCHASE_ORDER.getType())
                .setCreated(new Date()));
    }

    private void fillRelation4WarehouseEntry(List<UniqueCodeRelation> relations, Long id, String uniqueCode, String businessCode, Integer businessType) {
        if (StringUtils.isEmpty(businessCode) || !DataUtils.checkIntegerNotEmpty(businessType)) {
            return;
        }
        relations.add(new UniqueCodeRelation()
                .setUniqueCodeId(id)
                .setUniqueCode(uniqueCode)
                .setBusinessId(0L)
                .setBusinessCode(businessCode)
                .setSubBusinessId(0L)
                .setBusinessType(businessType)
                .setCreated(new Date()));
    }

    private void handleCommon(List<UniqueCodeRelation> relations, UniqueCodeGenericParams params, WaveUniqueCode exist, UniqueCodeGenericParams.ItemGenericParams relation) {
        if (params.isPurchaseGenerateUniqueCode()) {
            // 以销定采家里货唯一码复用会因businessCode为null报错
            return;
        }
        boolean skip = !DataUtils.checkLongNotEmpty(relation.getBusinessId()) && StringUtils.isEmpty(relation.getBusinessCode()) && !DataUtils.checkLongNotEmpty(relation.getSubBusinessId());
        if (skip) {
            return;
        }
        relations.add(new UniqueCodeRelation()
                .setUniqueCodeId(exist.getId())
                .setUniqueCode(exist.getUniqueCode())
                .setBusinessId(relation.getBusinessId() == null ? 0L : relation.getBusinessId())
                .setBusinessCode(relation.getBusinessCode())
                .setSubBusinessId(relation.getSubBusinessId())
                .setBusinessType(params.getBusinessType())
                .setStatus(exist.getStatus())
                .setCreated(new Date()));
    }

    @Override
    public void updateUniqueCodeAfterReceive(Staff staff, UniqueCodeResult result, List<WaveUniqueCode> exists, UniqueCodeGenericParams params) {
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return;
        }

        Map<String, UniqueCodeGenericParams.ItemGenericParams> codeMap = params.getItemParams().stream().collect(Collectors.toMap(UniqueCodeGenericParams.ItemGenericParams::getUniqueCode, a -> a, (a1, a2) -> a1));
        List<WaveUniqueCode> updates = Lists.newArrayList();
        List<Long> supplierIds = params.getItemParams().stream().map(UniqueCodeGenericParams.ItemGenericParams::getSupplierId).collect(Collectors.toList());
        Map<Long, Supplier> supplierMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(supplierIds)) {
            supplierMap = Optional.ofNullable(supplierService.queryByIds(staff, supplierIds)).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(Supplier::getId, a -> a, (a1, a2) -> a1));
        }
        for (WaveUniqueCode exist : exists) {
            UniqueCodeGenericParams.ItemGenericParams codeParam = codeMap.get(exist.getUniqueCode());
            boolean warehouseEntryReceive = Objects.equals(UniqueCodeRelation.BusinessType.WAREHOUSE_ENTRY_ORDER.getType(), params.getBusinessType()) && codeParam != null;
            if (warehouseEntryReceive) {
                WaveUniqueCode update = new WaveUniqueCode();
                update.setId(exist.getId());
                update.setBusinessCode(codeParam.getPurchaseOrderCode());
                if (Objects.equals(codeParam.getStockPosition(), UniqueCodeStockPositionEnum.SHELVES.getType())) {
                    update.setStockPosition(codeParam.getStockPosition());
                    update.setGoodsSectionCode(codeParam.getGoodsSectionCode());
                } else {
                    update.setStockPosition(codeParam.getStockPosition());
                    update.setGoodsSectionCode("");
                }
                update.setProductionDate(codeParam.getProductionDate());
                if (StringUtils.isNotEmpty(codeParam.getItemBatchNo())) {
                    update.setItemBatchNo(codeParam.getItemBatchNo());
                }
                if (DataUtils.checkLongNotEmpty(codeParam.getWarehouseId())) {
                    update.setWarehouseId(codeParam.getWarehouseId());
                }
                Supplier supplier;
                if ((supplier = supplierMap.get(codeParam.getSupplierId())) != null) {
                    update.setSupplierId(supplier.getId());
                    update.setSupplierName(supplier.getName());
                    update.setSupplierCategory(supplier.getCategoryName());
                }
                updates.add(update);
            }
        }
        if (CollectionUtils.isNotEmpty(updates)) {
            waveUniqueCodeDao.batchUpdate(staff, updates);
        }
    }

    public Map<String, Supplier> queryExistSupplierName(Staff staff, List<String> supplierNames) {
        if (CollectionUtils.isEmpty(supplierNames)) {
            return Collections.emptyMap();
        }
        List<Supplier> suppliers = supplierService.listByNames(staff, supplierNames, CommonConstants.ENABLE_STATUS_NORMARL);
        if (CollectionUtils.isEmpty(suppliers)) {
            return Collections.emptyMap();
        }
        return suppliers.stream().collect(Collectors.toMap(Supplier::getName, a -> a, (a, b) -> a));
    }

    public Map<Long, Supplier> queryExistSupplierMapByIds(Staff staff, List<Long> supplierIds) {
        if (CollectionUtils.isEmpty(supplierIds)) {
            return Collections.emptyMap();
        }
        List<Supplier> suppliers = supplierService.queryByIds(staff, supplierIds);
        if (CollectionUtils.isEmpty(suppliers)) {
            return Collections.emptyMap();
        }
        return suppliers.stream().collect(Collectors.toMap(Supplier::getId, a -> a, (a, b) -> a));
    }

    public Map<String, Warehouse> queryExistWarehouse(Staff staff) {
        List<Warehouse> warehouses = warehouseService.queryAll(staff, CommonConstants.ENABLE_STATUS_NORMARL);
        return Optional.ofNullable(warehouses).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(Warehouse::getName, a -> a, (a, b) -> a));
    }

    public Map<String, GoodsSection> queryExistGoodsSection(Staff staff, List<String> goodsSectionCodes) {
        if (CollectionUtils.isEmpty(goodsSectionCodes)) {
            return Collections.emptyMap();
        }
        List<GoodsSection> byCodes = wmsService.findByCodes(staff, goodsSectionCodes);
        if (CollectionUtils.isEmpty(byCodes)) {
            return Collections.emptyMap();
        }
        return byCodes.stream().collect(Collectors.toMap(this::buildGsKey, a -> a, (a, b) -> a));
    }

    public String buildGsKey(GoodsSection goodsSection) {
        return goodsSection.getWarehouseId() + "_" + goodsSection.getCode();
    }

    @Override
    public Map<String, GoodsSection> queryExistGoodsSection4Import(Staff staff, List<Long> warehouseIds, List<String> goodsSectionCodes) {
        if (CollectionUtils.isEmpty(goodsSectionCodes)) {
            return Maps.newHashMap();
        }
        List<GoodsSection> goodsSections = wmsService.findByCodeAndWarehouseIds(staff, goodsSectionCodes, warehouseIds);
        if (CollectionUtils.isEmpty(goodsSections)) {
            return Maps.newHashMap();
        }
        return goodsSections.stream().collect(Collectors.toMap(g -> g.getWarehouseId() + "_" + g.getCode(), a -> a, (a, b) -> a));
    }

    public void fillDefaultRule(Staff staff, ItemUniqueCodeGenerateParams generateParams) {
        if (generateParams == null || DataUtils.checkLongNotEmpty(generateParams.getRuleId())) {
            return;
        }
        ItemUniqueCodeGenerateRule queryDefault = itemUniqueCodeGenerateRuleService.queryDefault(staff);
        Assert.notNull(queryDefault, "前先设置默认生成规则！");
        generateParams.setGenerateRule(queryDefault);
    }

    public UniqueCodeRelationCheckVO uniqueCodeRelationCheck(Staff staff, List<String> uniqueCodes, List<Integer> businessTypes, List<String> businessCodes, UniqueCodeResult result) {
        if (CollectionUtils.isEmpty(uniqueCodes) && CollectionUtils.isEmpty(businessCodes)) {
            UniqueCodeResult.setFailInfo("500", "单据号为空！", result);
            return new UniqueCodeRelationCheckVO();
        }

        // 根据单据号查询唯一码
        OrderUniqueCodeQueryParams queryParams = new OrderUniqueCodeQueryParams();
        if (CollectionUtils.isNotEmpty(uniqueCodes)) {
            queryParams.setUniqueCodes(uniqueCodes);
            queryParams.setBusinessTypes(businessTypes);
        } else if (CollectionUtils.isNotEmpty(businessCodes)) {
            queryParams.setBusinessCodes(businessCodes);
        } else {
            UniqueCodeResult.setFailInfo("500", "根据单据号未查询到唯一码！", result);
            return new UniqueCodeRelationCheckVO();
        }
        List<UniqueCodeRelation> uniqueCodeRelations = uniqueCodeRelationDao.queryByUniqueCondition(staff, queryParams);
        if (CollectionUtils.isEmpty(uniqueCodeRelations)) {
            UniqueCodeResult.setFailInfo("500", "根据单据号未查询到唯一码！", result);
            return new UniqueCodeRelationCheckVO();
        }

        // 采购单不再次查询唯一码, 部分采购单在唯一码表里, 不在relation表
        if (businessTypes != null && businessTypes.size() == 1 && Objects.equals(businessTypes.get(0), UniqueCodeRelation.BusinessType.PURCHASE_ORDER.getType())) {
            return new UniqueCodeRelationCheckVO().setRelations(uniqueCodeRelations);
        }

        ItemUniqueCodeQueryParams codeParams = new ItemUniqueCodeQueryParams();
        codeParams.setUniqueCodes(uniqueCodeRelations.stream().map(UniqueCodeRelation::getUniqueCode).distinct().collect(Collectors.toList()));
        List<WaveUniqueCode> exists = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, codeParams);
        if (CollectionUtils.isEmpty(exists)) {
            UniqueCodeResult.setFailInfo("500", "根据单据号未查询到唯一码！", result);
            return new UniqueCodeRelationCheckVO();
        }

        return new UniqueCodeRelationCheckVO().setRelations(uniqueCodeRelations).setUniqueCodes(exists);
    }

    @Override
    @Transactional
    public void releaseBindRelation4PurchaseOrder(Staff staff, UniqueCodeGenericParams params, UniqueCodeRelationCheckVO vo) {
        if (CollectionUtils.isEmpty(vo.getRelations()) && CollectionUtils.isEmpty(vo.getUniqueCodes())) {
            return;
        }
        // 解绑relation
        releaseBindRelation(staff, Optional.ofNullable(vo.getRelations()).orElse(Lists.newArrayList()).stream().map(UniqueCodeRelation::getId).distinct().collect(Collectors.toList()));
        // 解绑uniqueCode
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> updateCodeLogs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(vo.getUniqueCodes())) {
            List<WaveUniqueCode> uniqueCodeUpdates = Lists.newArrayList();
            for (WaveUniqueCode uniqueCode : vo.getUniqueCodes()) {
                WaveUniqueCode update = new WaveUniqueCode();
                update.setId(uniqueCode.getId());
                update.setBusinessCode("");
                update.setBusinessId(0L);
                uniqueCodeUpdates.add(update);

                updateCodeLogs.add(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO()
                        .setUniqueCode(uniqueCode.getUniqueCode())
                        .setOldPrintNum(uniqueCode.getPrintNum())
                        .setBusinessCode(uniqueCode.getBusinessCode()));
            }
            waveUniqueCodeDao.batchUpdate(staff, uniqueCodeUpdates);
        }

        if (CollectionUtils.isNotEmpty(vo.getRelations())) {
            Map<String, Integer> printNumMap = Optional.ofNullable(vo.getUniqueCodes()).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getPrintNum, (a, b) -> a));
            for (UniqueCodeRelation relation : vo.getRelations()) {
                updateCodeLogs.add(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO()
                        .setUniqueCode(relation.getUniqueCode())
                        .setOldPrintNum(printNumMap.get(relation.getUniqueCode()))
                        .setBusinessCode(relation.getBusinessCode()));
            }
        }

        if (CollectionUtils.isNotEmpty(updateCodeLogs)) {
            updateCodeLogs = updateCodeLogs.stream().distinct().collect(Collectors.toList());
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, updateCodeLogs, WaveUniqueOpType.RELEASE_PURCHASE_ORDER_RELATION, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(params.getLogTemplate())}), null);
        }
    }

    @Override
    @Transactional
    public void releaseBindRelation(Staff staff, List<Long> releaseRelationIds) {
        if (CollectionUtils.isEmpty(releaseRelationIds)) {
            return;
        }
        // 解绑单据
        List<UniqueCodeRelation> relationUpdates = Lists.newArrayList();
        for (Long relationId : releaseRelationIds) {
            UniqueCodeRelation update = new UniqueCodeRelation();
            update.setId(relationId);
            update.setEnableStatus(0);
            relationUpdates.add(update);
        }
        uniqueCodeRelationDao.batchUpdate(staff, relationUpdates);
    }

    @Override
    public List<WaveUniqueCode> queryUniqueCodesByWaveId(Staff staff, Long waveId) {
        if (!DataUtils.checkLongNotEmpty(waveId)) {
            return Collections.EMPTY_LIST;
        }
        WavePicking picking = wavePickingDao.getByWaveId(staff, waveId);
        if (picking == null || !DataUtils.checkLongNotEmpty(picking.getId())) {
            return Collections.EMPTY_LIST;
        }
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setBusinessType(UniqueCodeRelation.BusinessType.WAVE_PICKING_ID.getType());
        params.setBusinessIds(Lists.newArrayList(picking.getId()));
        params.setBusinessCodes(Lists.newArrayList(picking.getPickingCode()));
        if (logger.isDebugEnabled()) {
            logger.debug(String.format("queryUniqueCodesByWaveId params:【%s】", JSON.toJSONString(params)));
        }
        List<UniqueCodeRelation> relations = uniqueCodeRelationDao.queryByUniqueCondition(staff, params);
        if (logger.isDebugEnabled()) {
            logger.debug(String.format("queryUniqueCodesByWaveId relations:【%s】", JSON.toJSONString(relations)));
        }
        if (CollectionUtils.isEmpty(relations)) {
            return Collections.EMPTY_LIST;
        }
        return waveUniqueCodeDao.queryItemUniqueCodesByCodes(staff, relations.stream().map(UniqueCodeRelation::getUniqueCode).distinct().collect(Collectors.toList()));
    }

    @Override
    public void log4RecycleUniqueCode(Staff staff, List<WaveUniqueCode> codes, Integer type) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> updateCodeLogs = Lists.newArrayList();
        for (WaveUniqueCode exist : codes) {
            updateCodeLogs.add(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO()
                    .setUniqueCode(exist.getUniqueCode())
                    .setOldShortSid(exist.getShortSid())
                    .setOldWaveId(exist.getWaveId())
                    .setOldStatus(exist.getStatus()));
        }
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, updateCodeLogs, Objects.equals(type, 3) ? WaveUniqueOpType.CHANGE_LABEL : WaveUniqueOpType.RECYCLE_UNIQUECODE, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(getLogTemplate4RecycleUniqueCode(type))}), null);
    }

    @Override
    public void uniqueCodeBindTradeCheck(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result) {
        if (Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.PURCHASE_FINISH.getType()) || CollectionUtils.isEmpty(result.getData())) {
            return;
        }
        List<UniqueCodeResult.FailCodeInfo> failCodeInfos = new ArrayList<>();
        for (WaveUniqueCode code : result.getData()) {
            if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.PURCHASE_ORDER.getType())) {
                if (StringUtils.isEmpty(code.getBusinessCode())) {
                    failCodeInfos.add(new UniqueCodeResult.FailCodeInfo(code.getUniqueCode(), "未绑定采购单，无需解绑！"));
                }
                if (Objects.equals(code.getType(), 1) &&
                        !(Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType()) ||
                                Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType()))) {
                    failCodeInfos.add(new UniqueCodeResult.FailCodeInfo(code.getUniqueCode(), "状态不为等待采购/等待收货，不能解绑采购单！"));
                }
                continue;
            }
            if (!DataUtils.checkLongNotEmpty(code.getSid()) && !DataUtils.checkLongNotEmpty(code.getOrderId())) {
                failCodeInfos.add(new UniqueCodeResult.FailCodeInfo(code.getUniqueCode(), "未绑定订单，无需解绑！"));
            }
        }
        if (CollectionUtils.isNotEmpty(failCodeInfos)) {
            result.setSuccess(false).setFailCodeInfos(failCodeInfos);
        }
    }

    @Override
    public void uniqueCodeBatchReleaseCheck(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result) {
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return;
        }
        Set<String> uniqueCodes = new HashSet<>();
        List<UniqueCodeResult.FailCodeInfo> failCodeInfos = new ArrayList<>();
        result.setFailCodeInfos(failCodeInfos);
        for (UniqueCodeGenericParams.ItemGenericParams item : params.getItemParams()) {
            if (StringUtils.isNotEmpty(item.getUniqueCode())) {
                uniqueCodes.add(item.getUniqueCode());
            }
        }
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            UniqueCodeResult.setFailInfo("500", "唯一码全部不存在, 无可更新数据", result);
            return;
        }
        List<WaveUniqueCode> exists = waveUniqueCodeDao.queryItemUniqueCodesByCodes(staff, new ArrayList<>(uniqueCodes));
        if (CollectionUtils.isEmpty(exists)) {
            for (UniqueCodeGenericParams.ItemGenericParams item : params.getItemParams()) {
                failCodeInfos.add(new UniqueCodeResult.FailCodeInfo(item.getUniqueCode(), "唯一码不存在！"));
            }
            UniqueCodeResult.setFailInfo("500", "唯一码全部不存在, 无可更新数据", result);
            return;
        }

        Map<String, WaveUniqueCode> existMap = exists.stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, a -> a, (a1, a2) -> a1));
        List<WaveUniqueCode> filters = new ArrayList<>();
        for (UniqueCodeGenericParams.ItemGenericParams item : params.getItemParams()) {
            WaveUniqueCode code = existMap.get(item.getUniqueCode());
            if (code == null) {
                failCodeInfos.add(new UniqueCodeResult.FailCodeInfo(item.getUniqueCode(), "唯一码不存在！"));
                continue;
            }
            if (!DataUtils.checkLongNotEmpty(code.getSid())) {
                failCodeInfos.add(new UniqueCodeResult.FailCodeInfo(code.getUniqueCode(), "未绑定订单，无需解绑！"));
                continue;
            }
            filters.add(code);
        }
        result.setData(filters);
        if (CollectionUtils.isEmpty(result.getData())) {
            UniqueCodeResult.setFailInfo("500", "参数不正确", result);
        }
    }

    @Override
    public void dealUniqueCodeCancel(Staff staff, List<WaveUniqueCode> uniqueCodes, UnboundReasonTypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(uniqueCodes) || typeEnum == null) {
            return;
        }
        Map<Long, Long> shortSidMap = Optional.ofNullable(waveUseTradeServiceProxy.queryBySids(staff, false, uniqueCodes.stream().filter(u -> DataUtils.checkLongNotEmpty(u.getSid())).map(WaveUniqueCode::getSid).distinct().toArray(Long[]::new))).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(Trade::getSid, Trade::getShortId, (a, b) -> a));
        for (WaveUniqueCode code : uniqueCodes) {
            code.setShortSid(shortSidMap.get(code.getSid()));
        }
        itemUniqueCodeExtendService.dealItemUniqueCodeCancelTypeEnum(staff, uniqueCodes.stream().filter(u -> Objects.equals(u.getType(), 2)).collect(Collectors.toList()), typeEnum);
        orderUniqueCodeExtendService.dealOrderUniqueCodeCancel(staff, uniqueCodes.stream().filter(u -> Objects.equals(u.getType(), 1)).collect(Collectors.toList()), typeEnum);
    }

    @Override
    @Transactional
    public void dealUniqueCodeCancel(Staff staff, List<WaveUniqueCode> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return;
        }
        Map<Long, Long> shortSidMap = Optional.ofNullable(waveUseTradeServiceProxy.queryBySids(staff, false, uniqueCodes.stream().filter(u -> DataUtils.checkLongNotEmpty(u.getSid())).map(WaveUniqueCode::getSid).distinct().toArray(Long[]::new))).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(Trade::getSid, Trade::getShortId, (a, b) -> a));
        for (WaveUniqueCode code : uniqueCodes) {
            code.setShortSid(shortSidMap.get(code.getSid()));
        }
        itemUniqueCodeExtendService.dealItemUniqueCodeCancel(staff, uniqueCodes.stream().filter(u -> Objects.equals(u.getType(), 2)).collect(Collectors.toList()));
        orderUniqueCodeExtendService.dealOrderUniqueCodeCancel(staff, uniqueCodes.stream().filter(u -> Objects.equals(u.getType(), 1)).collect(Collectors.toList()));
    }

    @Override
    public void singleItemUniqueCodeReceiveCheck(WaveUniqueCode code) {
        if (code == null) {
            return;
        }
        // 已采退不校验重复收货
        if (!Objects.equals(OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType(), code.getStatus()) &&
                Objects.equals(CommonConstants.JUDGE_YES, code.getReceiveStatus())) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_COMMON,
                    String.format("唯一码[%s]已重复扫描！", code.getUniqueCode()),
                    code.getUniqueCode(),
                    StringUtils.isBlank(code.getSkuOuterId()) ? code.getOuterId() : code.getSkuOuterId(),
                    StringUtils.isBlank(code.getSkuPicPath()) ? code.getPicPath() : code.getSkuPicPath());
        }
    }

    @Override
    public Integer getStatus4AllowCancel(WaveUniqueCode code) {
        if (code == null) {
            return null;
        }
        return Objects.equals(code.getStockStatus(), 1) ? OrderUniqueCodeStatusEnum.PICKED.getType() : OrderUniqueCodeStatusEnum.RECIVED.getType();
    }

    @Override
    public Integer getStockPosition4AllowCancel(WaveUniqueCode code) {
        if (code == null) {
            return null;
        }
        return Objects.equals(code.getStockStatus(), 1) ? UniqueCodeStockPositionEnum.PICK_AREA.getType() : UniqueCodeStockPositionEnum.WAREHOUSING_AREA.getType();
    }

    private String getLogTemplate4RecycleUniqueCode(Integer type) {
        if (Objects.equals(type, 3)) {
            return "换标绑定唯一码 内部单号：%s 波次号：%s";
        }
        if (Objects.equals(type, 1)) {
            return "唯一码解绑，解绑订单：%s 唯一码状态更新: %s->%s";
        }
        if (Objects.equals(type, 4)) {
            return "唯一码批量解绑，解绑订单：%s";
        }
        return "唯一码绑定，绑定订单：%s 唯一码状态更新: %s->%s";
    }

    @Override
    public boolean judgeOpenItemUniqueCode(Staff staff) {
        return judgeOpenItemUniqueCodeInner(staff);
    }

    private boolean judgeOpenItemUniqueCodeInner(Staff staff) {
        long t0 = System.currentTimeMillis();
        WmsConfig wmsConfig = wmsService.getConfig(staff);
        long t1 = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("judgeOpenItemUniqueCodeInner 查询仓储配置耗时:【%s】", t1 - t0)));
        }
        if (wmsConfig == null) {
            // 新公司不走tj配置
            if (isNewCompany4OpenItemUniqueCode(staff)) {
                return false;
            }
            // 老公司没有保存过开启商品唯一码配置，取开启订单唯一码的配置
            return WaveUtils.openOrderUniqueCode(staff);
        }
        Map<String, Object> extInfoMap = wmsConfig.getExtInfoMap();
        if (extInfoMap.containsKey(WmsConfigExtInfoEnum.OPEN_ITEM_UNIQUE_CODE.getKey())) {
            return Objects.equals(extInfoMap.get(WmsConfigExtInfoEnum.OPEN_ITEM_UNIQUE_CODE.getKey()), 1);
        }
        // 新公司不走tj配置
        if (isNewCompany4OpenItemUniqueCode(staff)) {
            return false;
        }
        // 老公司没有保存过开启商品唯一码配置，取开启订单唯一码的配置
        return WaveUtils.openOrderUniqueCode(staff);
    }

    private boolean isNewCompany4OpenItemUniqueCode(Staff staff) {
        Long companyId = staff.getCompanyId();
        if (!DataUtils.checkLongNotEmpty(companyId)) {
            return false;
        }
        try {
            long t0 = System.currentTimeMillis();
            Company company = companyService.queryById(companyId);
            long t1 = System.currentTimeMillis();
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("isNewCompany4OpenItemUniqueCode 查询公司耗时:【%s】", t1 - t0)));
            }
            if (company != null && company.getCreated() != null && company.getCreated().after(new SimpleDateFormat("yyyy-MM-dd").parse("2024-06-04"))) {
                return true;
            }
        } catch (Exception e) {
            logger.error("商品唯一码判断新公司报错", e);
        }
        return false;
    }

    public void refillParams4PurchaseImport(Staff staff, UniqueCodeGenericParams params) {
        if (!params.isPurchaseGenerateUniqueCode()) {
            return;
        }
        // 过滤不符合订单的商品生成参数
        fillParam4PurchaseOrder(staff, params.getItemParams().stream().filter(p -> DataUtils.checkLongNotEmpty(p.getBusinessId())).map(UniqueCodeGenericParams.ItemGenericParams::getBusinessId).distinct().collect(Collectors.toList()), params);

        List<Integer> statusList = getReuseStatusList4PurchaseImport(staff, params);

        // 唯一码复用
        if (CollectionUtils.isNotEmpty(statusList)) {
            List<Long> orderIds = params.getItemParams().stream().filter(p -> DataUtils.checkLongNotEmpty(p.getSubBusinessId())).map(UniqueCodeGenericParams.ItemGenericParams::getSubBusinessId).distinct().collect(Collectors.toList());
            List<OrderUniqueCodeUnboundLog> logs = unboundLogDao.queryByOrderId4Rebound(staff, orderIds, statusList, params.isFilterNoPurchaseOrder4Reuse());
            if (CollectionUtils.isEmpty(logs)) {
                if (Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.PURCHASE_ORDER_REUSE.getType())) {
                    // 只复用唯一码，没有可复用跳过生成
                    params.setItemParams(Lists.newArrayList());
                }
                return;
            }
            Map<String, List<OrderUniqueCodeUnboundLog>> logsMap = logs.stream().collect(Collectors.groupingBy(l -> l.getSid() + "_" + l.getOrderId() + "_" + DataUtils.getZeroIfDefault(l.getSysItemId()) + "_" + DataUtils.getZeroIfDefault(l.getSysSkuId()) + "_" + l.getStockStatus()));
            List<UniqueCodeGenericParams.ItemGenericParams> newParams = Lists.newArrayList();
            for (UniqueCodeGenericParams.ItemGenericParams param : params.getItemParams()) {
                List<OrderUniqueCodeUnboundLog> subLogs = logsMap.get(param.getBusinessId() + "_" + param.getSubBusinessId() + "_" + DataUtils.getZeroIfDefault(param.getSysItemId()) + "_" + DataUtils.getZeroIfDefault(param.getSysSkuId()) + "_" + (param.isFromStockOrderRecord() ? CommonConstants.JUDGE_YES : CommonConstants.JUDGE_NO));
                if (CollectionUtils.isEmpty(subLogs) || !DataUtils.checkIntegerNotEmpty(param.getNum())) {
                    if (Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.PURCHASE_ORDER_REUSE.getType())) {
                        // 只复用唯一码，排除生成参数
                        continue;
                    }
                    newParams.add(param);
                    continue;
                }
                newParams.addAll(getCopyParams(param, logsMap));
            }
            params.setItemParams(newParams);
            return;
        }
        if (Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.PURCHASE_ORDER_REUSE.getType())) {
            // 只复用唯一码，没有可复用跳过生成
            params.setItemParams(Lists.newArrayList());
        }
    }

    private List<Integer> getReuseStatusList4PurchaseImport(Staff staff, UniqueCodeGenericParams params) {
        if (CollectionUtils.isNotEmpty(params.getReuseStatusList())) {
            return params.getReuseStatusList();
        }
        // 获取唯一码复用配置
        Integer openItemUniqueCode = null;
        List<Integer> statusList = Lists.newArrayList();
        try {
            WmsConfig wmsConfig = wmsService.getConfig(staff);
            openItemUniqueCode = wmsConfig.getInteger(WmsConfigExtInfoEnum.OPEN_UNIQUE_CODE_REUSE.getKey());
            if (!Objects.equals(openItemUniqueCode, CommonConstants.JUDGE_YES)) {
                return statusList;
            }
            String statusStr = wmsConfig.getString(WmsConfigExtInfoEnum.UNIQUE_CODE_REUSE_STATUS.getKey());
            Integer[] statusArr = ArrayUtils.toIntegerArray(statusStr);
            if (statusArr != null && statusArr.length > 0) {
                statusList = Arrays.asList(statusArr);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "获取仓储配置-唯一码复用配置失败:" + e.getMessage()));
        }
        return statusList;
    }

    @Override
    public void batchInsertUniqueCodeRelation(Staff staff, List<UniqueCodeRelation> uniqueCodeRelations) {
        if (CollectionUtils.isEmpty(uniqueCodeRelations)) {
            return;
        }
        uniqueCodeRelationDao.batchInsert(staff, uniqueCodeRelations);
        if (!Objects.equals(uniqueCodeRelations.get(0).getBusinessType(), UniqueCodeRelation.BusinessType.WAREHOUSE_ENTRY_ORDER.getType())) {
            return;
        }
        // 绑定收货单日志
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> dtos = Lists.newArrayList();
        for (UniqueCodeRelation relation : uniqueCodeRelations) {
            dtos.add(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO().setUniqueCode(relation.getUniqueCode()).setWarehouseEntryCode(relation.getBusinessCode()));
        }
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, dtos, WaveUniqueOpType.BIND_WAREHOUSE_ENTRY, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate("唯一码关联收货单：%s")}), null);
    }

    @Override
    @Transactional
    public void batchReceive4Pack(Staff staff, Integer receiveType, List<WaveUniqueCode> needReceives) {
        if (CollectionUtils.isEmpty(needReceives) || receiveType == null) {
            return;
        }
        needReceives = needReceives.stream().filter(code -> Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType()) || Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType()) || Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.WAIT_PURCHASE.getType()) || Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.WAIT_IN.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needReceives)) {
            return;
        }
        ReceiveUniqueCodeParams uniqueCodeParams = new ReceiveUniqueCodeParams();
        uniqueCodeParams.setOpType(0);
        uniqueCodeParams.setReceiveType(receiveType);
        uniqueCodeParams.setLogTemplate(getLogTemplate4Examine(receiveType));
        uniqueCodeParams.setUniqueCodeArr(needReceives.stream().map(WaveUniqueCode::getUniqueCode).distinct().collect(Collectors.toList()));
        uniqueCodeParams.setSource(OperateSourceContext.acquireOperateSourceNew().getProject());
        orderUniqueCodeService.batchReceive(staff, uniqueCodeParams);
    }

    private String getLogTemplate4Examine(Integer receiveType) {
        if (Objects.equals(receiveType, 2)) {
            return "包装验货收货，唯一码状态更新:%s->%s";
        }
        if (Objects.equals(receiveType, 3)) {
            return "后置打印收货，唯一码状态更新:%s->%s";
        }
        if (Objects.equals(receiveType, 4)) {
            return "播种打印收货，唯一码状态更新:%s->%s";
        }
        if (Objects.equals(receiveType, 5)) {
            return "-1盲扫收货，唯一码状态更新:%s->%s";
        }
        if (Objects.equals(receiveType, 6)) {
            return "快销发货收货，唯一码状态更新:%s->%s";
        }
        return null;
    }

    private List<UniqueCodeGenericParams.ItemGenericParams> getCopyParams(UniqueCodeGenericParams.ItemGenericParams param, Map<String, List<OrderUniqueCodeUnboundLog>> logsMap) {
        String key = param.getBusinessId() + "_" + param.getSubBusinessId() + "_" + DataUtils.getZeroIfDefault(param.getSysItemId()) + "_" + DataUtils.getZeroIfDefault(param.getSysSkuId()) + "_" + (param.isFromStockOrderRecord() ? CommonConstants.JUDGE_YES : CommonConstants.JUDGE_NO);
        List<OrderUniqueCodeUnboundLog> subLogs = logsMap.get(key);
        if (CollectionUtils.isEmpty(subLogs)) {
            return Lists.newArrayList();
        }

        List<OrderUniqueCodeUnboundLog> usedLogs = Lists.newArrayList();
        List<UniqueCodeGenericParams.ItemGenericParams> copies = Lists.newArrayList();
        for (int i = 0; i < param.getNum(); i++) {
            UniqueCodeGenericParams.ItemGenericParams subParam = new UniqueCodeGenericParams.ItemGenericParams();
            BeanUtils.copyProperties(param, subParam);
            if (i >= subLogs.size()) {
                copies.add(subParam.setNum(param.getNum() - subLogs.size()));
                break;
            }
            copies.add(subParam.setUniqueCode(subLogs.get(i).getUniqueCode()).setNum(0));
            usedLogs.add(subLogs.get(i));
        }
        // 去掉已经复用过的
        subLogs.removeAll(usedLogs);

        return copies;
    }


    @Override
    public void log4PdaReleaseUniqueCode(Staff staff, List<WaveUniqueCode> codes, UnboundReasonTypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(codes, typeEnum.getDesc()), WaveUniqueOpType.ORDER_RELEASE}), null);
    }

    /**
     * 根据库存锁定记录，有货部分生成在库商品唯一码
     * @param staff
     * @param params 唯一码生成参数
     * @param trades 订单包含合单隐藏单
     */
    private void buildParamsByStockOrderRecord(Staff staff, UniqueCodeGenericParams params, List<Trade> trades) {
        if (!params.isPurchaseGenerateUniqueCode() || CollectionUtils.isEmpty(trades)) {
            return;
        }
        // 订单所有子订单
        List<Order> orders = WaveUtils.getOrdersForWave(TradeUtils.getOrders4Trade(trades), true);
        Map<Long, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getId, o -> o, (a, b) -> a));
        List<Long> orderIdList = orders.stream().map(Order::getId).collect(Collectors.toList());
        logger.debug(String.format("子订单orderIds: %s", orderIdList));

        // 查询库存锁定记录（包含隐藏单的）
        List<StockOrderRecord> stockOrderRecords = stockOrderRecordDao.queryByOrderIds(staff, orderIdList.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(stockOrderRecords)) {
            logger.debug(String.format("未查询到库存锁定记录,orderIdList: %s", orderIdList));
            return;
        }

        // 过滤掉套件本身、虚拟商品或无需发货商品的库存锁定记录
        stockOrderRecords = stockOrderRecords.stream().filter(stockOrderRecord -> !stockOrderRecord.isSuitSelf() &&
                !OrderUtils.isVirtualOrNonConsign(orderMap.get(stockOrderRecord.getOrderId()))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(stockOrderRecords)) {
            logger.debug("无需要生成唯一码的家里货！");
            return;
        }

        // 查询对应子订单已绑定的家里的在库商品唯一码
        Map<Long, Long> existCodeNumMap = Maps.newHashMap();
        List<Long> orderIds = stockOrderRecords.stream().map(StockOrderRecord::getOrderId).collect(Collectors.toList());
        logger.debug(String.format("库存锁定记录对应orderIds:%s", orderIds));
        ItemUniqueCodeQueryParams queryParams = new ItemUniqueCodeQueryParams();
        queryParams.setType(2);
        queryParams.setOrderIds(orderIds);
        queryParams.setStockStatus(1);
        queryParams.setStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.IN.getType()));
        List<WaveUniqueCode> existCodes = itemUniqueCodeService.queryItemUniqueCodeByCondition(staff, queryParams);
        if (CollectionUtils.isNotEmpty(existCodes)) {
            existCodeNumMap = existCodes.stream().collect(Collectors.groupingBy(WaveUniqueCode::getOrderId, Collectors.counting()));
        }

        // 根据库存锁定记录生成商品参数
        for (StockOrderRecord record : stockOrderRecords) {
            UniqueCodeGenericParams.ItemGenericParams itemGenericParams = new UniqueCodeGenericParams.ItemGenericParams();
            Order order = orderMap.get(record.getOrderId());
            Long mainSid = DataUtils.checkLongNotEmpty(order.getBelongSid()) ? order.getBelongSid() : order.getSid(); // 订单sid，合单时为主单sid
            Long stockNum = record.getStockNum(); // 库存锁定数量
            Long existNum = existCodeNumMap.getOrDefault(record.getOrderId(), 0L); // 家里货已绑定唯一码数量
            logger.debug(String.format("mainSid:%s, orderId:%s, 库存锁定数量:%s, 已绑唯一码数量:%s", mainSid, record.getOrderId(), stockNum, existNum));
            if (stockNum == null || stockNum == 0L || (stockNum.intValue() - existNum.intValue() <= 0)) {
                continue;
            }
            itemGenericParams.setBusinessId(mainSid);
            itemGenericParams.setSubBusinessId(record.getOrderId());
            itemGenericParams.setSysItemId(record.getSysItemId());
            itemGenericParams.setSysSkuId(record.getSysSkuId());
            itemGenericParams.setOuterId(order.getSysOuterId());
            itemGenericParams.setStatus(OrderUniqueCodeStatusEnum.IN.getType());
            itemGenericParams.setWarehouseId(record.getWarehouseId());
            itemGenericParams.setNum(stockNum.intValue() - existNum.intValue());
            itemGenericParams.setCodeType(2); // 采购单审核有货部分生成唯一码，因存在缺货部分所以是多件订单
            itemGenericParams.setFromStockOrderRecord(true);
            if (logger.isInfoEnabled()) {
                logger.info(String.format(String.format("采购单审核,有货部分生成唯一码,订单:%s,商品:%s[%s],有货部分生成数量:%s", record.getSid(), order.getSysOuterId(), WmsKeyUtils.buildItemKey(record.getSysItemId(), record.getSysSkuId()), stockNum.intValue() - existNum.intValue())));
            }
            params.getItemParams().add(itemGenericParams);
        }
    }

}

package com.raycloud.dmj.business.wave;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.dao.wave.WaveUniqueCodeDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.wave.OrderUniqueCodeStatusEnum;
import com.raycloud.dmj.domain.wave.UniqueCodeStockPositionEnum;
import com.raycloud.dmj.domain.wave.WaveUniqueCode;
import com.raycloud.dmj.domain.wave.model.ItemUniqueCodeGenerateParams;
import com.raycloud.dmj.domain.wave.utils.OrderUniqueCodeUtils;
import com.raycloud.dmj.domain.wms.GoodsSection;
import com.raycloud.dmj.domain.wms.WorkingStorageSection;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.utils.wms.DataUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import com.raycloud.dmj.services.trades.wave.IUniqueCodeBaseService;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UniqueCodeImportBusiness {

    private final static Logger logger = Logger.getLogger(UniqueCodeImportBusiness.class);
    @Resource
    private UniqueCodeAutoIncBusiness uniqueCodeAutoIncBusiness;
    @Resource
    private IUniqueCodeBaseService uniqueCodeBaseService;
    @Resource
    private IItemServiceDubbo itemServiceDubbo;
    @Resource
    private WaveUniqueCodeDao waveUniqueCodeDao;


    public Map<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> importBaseCheck(Staff staff, String[][] datas, List<String> errorMsg, Integer convertType) {
        List<String> uniqueCodes = new ArrayList<>();
        Map<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> excelData = new HashMap<>();
        int lineNum = 1;
        Map<String, Warehouse> warehouseMap = uniqueCodeBaseService.queryExistWarehouse(staff);
        // 必须从1开始，excel表格头没被忽略，第二行才是真正的数据
        for (int i = 1; i < datas.length; i++) {
            String[] lineData = datas[i];
            lineNum++;
            ImportBaseCheckParams params = getImportBaseCheckParams(lineData, convertType);
            int preErrorMsgSize = errorMsg.size();
            if (Objects.equals(convertType, 0)) {
                importBaseCheck4Import(staff, params, warehouseMap, uniqueCodes, errorMsg, lineNum);
                int postErrorMsgSize = errorMsg.size();
                // 前校验有报错跳过
                if (!Objects.equals(preErrorMsgSize, postErrorMsgSize)) {
                    continue;
                }
                excelData.put(lineNum, getDetail4Import(params));
            } else if (Objects.equals(convertType, 1)) {
                importBaseCheck4ImportModify(params, uniqueCodes, errorMsg, lineNum);
                int postErrorMsgSize = errorMsg.size();
                // 前校验有报错跳过
                if (!Objects.equals(preErrorMsgSize, postErrorMsgSize)) {
                    continue;
                }
                excelData.put(lineNum, getDetail4ImportModify(params));
            } else {
                return new HashMap<>();
            }
        }
        if (CollectionUtils.isEmpty(errorMsg) && Objects.equals(convertType, 0)) {
            judgeUniqueCodeEqualOuterId(staff, datas, uniqueCodes, errorMsg);
        }
        return excelData;
    }

    private void judgeUniqueCodeEqualOuterId(Staff staff, String[][] datas, List<String> uniqueCodes, List<String> errorMsg) {
        if (CollectionUtils.isEmpty(uniqueCodes) || datas == null || datas.length == 0) {
            return;
        }
        Map<String, Object> itemMap = itemServiceDubbo.queryItemSkuByOuterId(staff, uniqueCodes, 12, null);
        if (MapUtils.isEmpty(itemMap)) {
            return;
        }
        for (int i = 1; i < datas.length; i++) {
            String[] lineData = datas[i];
            String uniqueCode = StringUtils.trimToNull(lineData[1]);
            if (itemMap.get(uniqueCode) != null) {
                errorMsg.add("第" + (i + 1) + "行，唯一码不能和商家编码相同！");
            }
        }
    }

    private ItemUniqueCodeGenerateParams.GenerateDetail getDetail4Import(ImportBaseCheckParams params) {
        ItemUniqueCodeGenerateParams.GenerateDetail detail = new ItemUniqueCodeGenerateParams.GenerateDetail();
        Integer status = OrderUniqueCodeStatusEnum.getTypeByName(params.getStatus());
        detail.setStatus(status);
        detail.setAddStock(!params.isThirdWarehouseGenerate());
        if (!Objects.equals(OrderUniqueCodeStatusEnum.WAIT_IN.getType(), status)
                && !Objects.equals(OrderUniqueCodeStatusEnum.OUT.getType(), status)) {
            detail.setGoodsSectionCode(params.getGoodsSectionCode());
        }
        fillDetail(params, detail);
        if (params.getUniqueCodeAutoIncVO() == null) {
            detail.setNum(1);
            detail.setUniqueCode(params.getUniqueCode());
        } else {
            detail.setNum(params.getUniqueCodeAutoIncVO().getUniqueCodes().size());
            detail.setUniqueCodes(params.getUniqueCodeAutoIncVO().getUniqueCodes());
        }
        return detail;
    }

    private ItemUniqueCodeGenerateParams.GenerateDetail getDetail4ImportModify(ImportBaseCheckParams params) {
        ItemUniqueCodeGenerateParams.GenerateDetail detail = new ItemUniqueCodeGenerateParams.GenerateDetail();
        detail.setUniqueCode(params.getUniqueCode());
        detail.setGoodsSectionCode(params.getGoodsSectionCode());
        fillDetail(params, detail);
        return detail;
    }

    public ItemUniqueCodeGenerateParams.GenerateDetail getDetail4WarehouseEntry(ImportBaseCheckParams params) {
        ItemUniqueCodeGenerateParams.GenerateDetail detail = new ItemUniqueCodeGenerateParams.GenerateDetail();
        detail.setUniqueCode(params.getUniqueCode());
        detail.setOuterId(params.getOuterId());
        detail.setWarehouseEntryCode(params.getWarehouseEntryCode());
        return detail;
    }

    private void fillDetail(ImportBaseCheckParams params, ItemUniqueCodeGenerateParams.GenerateDetail detail) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy/MM/dd");
        detail.setOuterId(params.getOuterId());
        detail.setSupplierName(params.getSupplierName());
        if (params.getSellingPrice() != null) {
            detail.setSellingPrice(Double.valueOf(params.getSellingPrice()));
        }
        if (params.getCostPrice() != null) {
            detail.setCostPrice(Double.valueOf(params.getCostPrice()));
        }
        if (params.getWeight() != null) {
            detail.setWeight(Double.valueOf(params.getWeight()));
        }
        try {
            if (OrderUniqueCodeUtils.isValidDate(params.getProductionDate(), sdf)) {
                detail.setProductionDate(sdf.parse(params.getProductionDate()));
            }
            if (OrderUniqueCodeUtils.isValidDate(params.getProductionDate(), sdf2)) {
                detail.setProductionDate(sdf2.parse(params.getProductionDate()));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        detail.setItemBatchNo(params.getItemBatchNo());
        detail.setWarehouseName(params.getWarehouseName());
        detail.setICCID(params.getICCID());
        detail.setExtendField2(params.getExtendField2());
        detail.setExtendField3(params.getExtendField3());
        detail.setExtendField4(params.getExtendField4());
        detail.setExtendField5(params.getExtendField5());
    }

    public ImportBaseCheckParams getImportBaseCheckParams(String[] lineData, Integer convertType) {
        // 商家编码
        String outerId = StringUtils.trimToNull(lineData[0]);
        // 唯一码
        String uniqueCode = StringUtils.trimToNull(lineData[1]);
        // 唯一码状态
        String status = Objects.equals(convertType, 0) ? StringUtils.trimToNull(lineData[2]) : null;
        // 供应商
        String supplierName = Objects.equals(convertType, 0) ? StringUtils.trimToNull(lineData[3]) : StringUtils.trimToNull(lineData[2]);
        // 销售价
        String sellingPrice = Objects.equals(convertType, 0) ? StringUtils.trimToNull(lineData[4]) : StringUtils.trimToNull(lineData[3]);
        // 成本价
        String costPrice = Objects.equals(convertType, 0) ? StringUtils.trimToNull(lineData[5]) : StringUtils.trimToNull(lineData[4]);
        // 重量
        String weight = Objects.equals(convertType, 0) ? StringUtils.trimToNull(lineData[6]) : StringUtils.trimToNull(lineData[5]);
        // 生产日期
        String productionDate = Objects.equals(convertType, 0) ? lineData[7] : lineData[6];
        // 批次
        String itemBatchNo = Objects.equals(convertType, 0) ? StringUtils.trimToNull(lineData[8]) : StringUtils.trimToNull(lineData[7]);
        // 所在仓库
        String warehouseName = Objects.equals(convertType, 0) ? StringUtils.trimToNull(lineData[9]) : StringUtils.trimToNull(lineData[8]);
        // 货位编码
        String goodsSectionCode = Objects.equals(convertType, 0) ? StringUtils.trimToNull(lineData[10]) : StringUtils.trimToNull(lineData[9]);
        // ICCID
        String ICCID = Objects.equals(convertType, 0) ? getICCID4Import(lineData, convertType) : getICCID4Import(lineData, convertType);

        String extendField2 = null;
        String extendField3 = null;
        String extendField4 = null;
        String extendField5 = null;
        boolean importContainExtendFields = lineData != null && lineData.length > 18;
        boolean modifyContainExtendFields = lineData != null && lineData.length > 14;
        if (Objects.equals(convertType, 0) && importContainExtendFields) {
            extendField2 = StringUtils.trimToNull(lineData[15]);
            extendField3 = StringUtils.trimToNull(lineData[16]);
            extendField4 = StringUtils.trimToNull(lineData[17]);
            extendField5 = StringUtils.trimToNull(lineData[18]);
        } else if (Objects.equals(convertType, 1) && modifyContainExtendFields) {
            extendField2 = StringUtils.trimToNull(lineData[11]);
            extendField3 = StringUtils.trimToNull(lineData[12]);
            extendField4 = StringUtils.trimToNull(lineData[13]);
            extendField5 = StringUtils.trimToNull(lineData[14]);
        }

        // 起始唯一码
        String startUniqueCode = null;
        // 结束唯一码
        String endUniqueCode = null;
        // 唯一码数量
        String num = null;
        boolean containBatch = Objects.equals(convertType, 0) && lineData.length > 11;
        if (containBatch) {
            // 起始唯一码
            startUniqueCode = StringUtils.trimToNull(lineData[11]);
            // 结束唯一码
            endUniqueCode = StringUtils.trimToNull(lineData[12]);
            // 唯一码数量
            num = StringUtils.trimToNull(lineData[13]);
        }

        return new ImportBaseCheckParams().setOuterId(outerId).setUniqueCode(uniqueCode).setStatus(status).setSupplierName(supplierName)
                .setSellingPrice(sellingPrice).setCostPrice(costPrice).setWeight(weight).setProductionDate(productionDate)
                .setItemBatchNo(itemBatchNo).setWarehouseName(warehouseName).setGoodsSectionCode(goodsSectionCode)
                .setStartUniqueCode(startUniqueCode).setEndUniqueCode(endUniqueCode).setNum(num).setICCID(ICCID)
                .setExtendField2(extendField2).setExtendField3(extendField3).setExtendField4(extendField4).setExtendField5(extendField5);
    }

    private String getICCID4Import(String[] lineData, Integer convertType) {
        if (Objects.equals(convertType, 0) && lineData != null && lineData.length > 14) {
            return StringUtils.trimToNull(lineData[14]);
        }
        if (Objects.equals(convertType, 1) && lineData != null && lineData.length > 10) {
            return StringUtils.trimToNull(lineData[10]);
        }
        return null;
    }

    public ImportBaseCheckParams getCheckParams4BindWarehouseEntry(String[] lineData) {
        // 唯一码
        String uniqueCode = StringUtils.trimToNull(lineData[0]);
        // 商家编码
        String outerId = StringUtils.trimToNull(lineData[1]);
        // 收货单
        String warehouseEntryCode = StringUtils.trimToNull(lineData[2]);

        return new ImportBaseCheckParams().setOuterId(outerId).setUniqueCode(uniqueCode).setWarehouseEntryCode(warehouseEntryCode);
    }

    private void importBaseCheck4Import(Staff staff, ImportBaseCheckParams params, Map<String, Warehouse> warehouseMap, List<String> uniqueCodes, List<String> errorMsg, int lineNum) {
        List<String> statusNames = OrderUniqueCodeStatusEnum.getItemStatusName();
        if (!judgeOuterIdNotEmpty(params.getOuterId(), errorMsg, lineNum)) {
            return;
        }
        boolean batchUniqueCodeAllEmptyFlag = StringUtils.isEmpty(params.getStartUniqueCode()) && StringUtils.isEmpty(params.getEndUniqueCode()) && StringUtils.isEmpty(params.getNum());
        boolean batchUniqueCodeAllNotEmptyFlag = StringUtils.isNotEmpty(params.getStartUniqueCode()) && StringUtils.isNotEmpty(params.getEndUniqueCode()) && StringUtils.isNotEmpty(params.getNum());
        if (!(batchUniqueCodeAllEmptyFlag || batchUniqueCodeAllNotEmptyFlag)) {
            errorMsg.add("第" + lineNum + "行，【起始唯一码】/【截止唯一码】/【数量】必填！");
            return;
        }
        if (batchUniqueCodeAllNotEmptyFlag) {
            UniqueCodeAutoIncBusiness.UniqueCodeAutoIncVO vo = uniqueCodeAutoIncCheck(staff, params, uniqueCodes, errorMsg, lineNum);
            if (vo == null) {
                return;
            }
            uniqueCodes.addAll(vo.getUniqueCodes());
            params.setUniqueCodeAutoIncVO(vo);
        } else {
            if (!judgeUniqueCode(params.getUniqueCode(), uniqueCodes, errorMsg, lineNum)) {
                return;
            }
        }

        // 唯一码状态
        String status = StringUtils.isEmpty(params.getStatus()) ? "待入库" : params.getStatus();
        params.setStatus(status);
        if (!statusNames.contains(status)) {
            errorMsg.add("第" + lineNum + "行，唯一码状态不存在！");
            return;
        }
        // 唯一码状态为在库，则所在仓库/货位编码，必填
        boolean thirdWarehouseGenerate = warehouseMap.get(params.getWarehouseName()) != null && Objects.equals(warehouseMap.get(params.getWarehouseName()).getType(), 1);
        params.setThirdWarehouseGenerate(thirdWarehouseGenerate);
        boolean judgeGoodsSectionCode = StringUtils.isNotEmpty(status) && Objects.equals(OrderUniqueCodeStatusEnum.IN.getName(), status)
                && ((StringUtils.isEmpty(params.getWarehouseName()) || StringUtils.isEmpty(params.getGoodsSectionCode())) && !thirdWarehouseGenerate);
        if (judgeGoodsSectionCode) {
            errorMsg.add("第" + lineNum + "行，在库唯一码，仓库/货位编码必填！");
            return;
        }
        importBaseCheck4Common(params, errorMsg, lineNum);
    }

    public boolean judgeOuterIdNotEmpty(String outerId, List<String> errorMsg, int lineNum) {
        if (StringUtils.isBlank(outerId)) {
            errorMsg.add("第" + lineNum + "行，商家编码不能为空！");
            return false;
        }
        return true;
    }

    public boolean judgeUniqueCode(String uniqueCode, List<String> uniqueCodes, List<String> errorMsg, int lineNum) {
        if (StringUtils.isBlank(uniqueCode)) {
            errorMsg.add("第" + lineNum + "行，唯一码不能为空！");
            return false;
        }
        if (uniqueCodes.contains(uniqueCode)) {
            errorMsg.add("第" + lineNum + "行，唯一码重复！");
            return false;
        }
        uniqueCodes.add(uniqueCode);
        return true;
    }

    private UniqueCodeAutoIncBusiness.UniqueCodeAutoIncVO uniqueCodeAutoIncCheck(Staff staff, ImportBaseCheckParams params, List<String> uniqueCodes, List<String> errorMsg, int lineNum) {
        if (!NumberUtils.isNumber(params.getNum())) {
            errorMsg.add("第" + lineNum + "行，数量格式不正确！");
            return null;
        }
        UniqueCodeAutoIncBusiness.UniqueCodeAutoIncVO uniqueCodeAutoIncVO = uniqueCodeAutoIncBusiness.uniqueCodeAutoInc(staff, new UniqueCodeAutoIncBusiness.UniqueCodeAutoIncParams()
                .setStartUniqueCode(params.getStartUniqueCode()).setEndUniqueCode(params.getEndUniqueCode())
                .setNum(Integer.parseInt(params.getNum())).setExistUniqueCodes(uniqueCodes));
        if (uniqueCodeAutoIncVO.isStrEqual()) {
            errorMsg.add("第" + lineNum + "行，【起始唯一码】和【截止唯一码】相同！");
            return null;
        }
        if (!uniqueCodeAutoIncVO.isStrLengthEqual()) {
            errorMsg.add("第" + lineNum + "行，【起始唯一码】和【截止唯一码】长度不同！");
            return null;
        }
        if (uniqueCodeAutoIncVO.isOver()) {
            errorMsg.add("第" + lineNum + "行，【数量】必须大于0小于等于5000！");
            return null;
        }
        if (uniqueCodeAutoIncVO.isPostContainLetter()) {
            errorMsg.add("第" + lineNum + "行，不符合自增格式！");
            return null;
        }
        if (!uniqueCodeAutoIncVO.isEqual()) {
            errorMsg.add("第" + lineNum + "行，【起始唯一码】【截止唯一码】生成数量和填写【数量】不一致！");
            return null;
        }
        if (uniqueCodeAutoIncVO.isExist()) {
            errorMsg.add("第" + lineNum + "行，唯一码重复！");
            return null;
        }
        return uniqueCodeAutoIncVO;
    }

    private void importBaseCheck4ImportModify(ImportBaseCheckParams params, List<String> uniqueCodes, List<String> errorMsg, int lineNum) {
        // 导入修改参数为空判断
        boolean noDataModify = StringUtils.isBlank(params.getOuterId()) && StringUtils.isBlank(params.getSupplierName())
                && StringUtils.isBlank(params.getSellingPrice()) && StringUtils.isBlank(params.getCostPrice())
                && StringUtils.isBlank(params.getWeight()) && StringUtils.isBlank(params.getProductionDate())
                && StringUtils.isBlank(params.getItemBatchNo()) && StringUtils.isBlank(params.getICCID())
                && StringUtils.isBlank(params.getExtendField2()) && StringUtils.isBlank(params.getExtendField3())
                && StringUtils.isBlank(params.getExtendField4()) && StringUtils.isBlank(params.getExtendField5())
                && StringUtils.isBlank(params.getWarehouseName()) && StringUtils.isBlank(params.getGoodsSectionCode());
        if (noDataModify) {
            errorMsg.add("第" + lineNum + "行，无可更新数据！");
            return;
        }
        if (StringUtils.isBlank(params.getUniqueCode())) {
            errorMsg.add("第" + lineNum + "行，唯一码不能为空！");
            return;
        }
        if (uniqueCodes.contains(params.getUniqueCode())) {
            errorMsg.add("第" + lineNum + "行，唯一码重复！");
            return;
        }
        uniqueCodes.add(params.getUniqueCode());
        importBaseCheck4Common(params, errorMsg, lineNum);
    }

    private void importBaseCheck4Common(ImportBaseCheckParams params, List<String> errorMsg, int lineNum) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy/MM/dd");
        // 销售价
        if (StringUtils.isNotBlank(params.getSellingPrice()) && !NumberUtils.isNumber(params.getSellingPrice())) {
            errorMsg.add("第" + lineNum + "行，销售价格式不正确！");
            return;
        }
        // 成本价
        if (StringUtils.isNotBlank(params.getCostPrice()) && !NumberUtils.isNumber(params.getCostPrice())) {
            errorMsg.add("第" + lineNum + "行，成本价格式不正确！");
            return;
        }
        // 重量
        if (StringUtils.isNotBlank(params.getWeight()) && !NumberUtils.isNumber(params.getWeight())) {
            errorMsg.add("第" + lineNum + "行，重量格式不正确！");
            return;
        }
        // 生产日期
        boolean productionDateFlag = StringUtils.isNotBlank(params.getProductionDate())
                && !(OrderUniqueCodeUtils.isValidDate(params.getProductionDate(), sdf)
                || OrderUniqueCodeUtils.isValidDate(params.getProductionDate(), sdf2));
        if (productionDateFlag) {
            errorMsg.add("第" + lineNum + "行，生产日期格式不正确！");
            return;
        }
    }

    public void importInfoCheck(Staff staff, Map<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> baseDetails, List<String> errorMsg, Integer convertType) {
        if (MapUtils.isEmpty(baseDetails)) {
            return;
        }
        Set<Map.Entry<Integer, ItemUniqueCodeGenerateParams.GenerateDetail>> entries = baseDetails.entrySet();
        ImportInfoCheckParams importInfoCheckParams = getImportInfoCheckParams(staff, entries);
        for (Map.Entry<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> entry : entries) {
            importInfoCheck4Item(importInfoCheckParams.getItemMap(), entry, errorMsg);
            if (Objects.equals(convertType, 0)) {
                importInfoCheck4Code0(importInfoCheckParams, entry, errorMsg);
            } else if (Objects.equals(convertType, 1)) {
                importInfoCheck4Code1(importInfoCheckParams, entry, errorMsg);
            }
            importInfoCheck4Common(importInfoCheckParams, entry, errorMsg);
        }
    }

    private void importInfoCheck4Common(ImportInfoCheckParams importInfoCheckParams, Map.Entry<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> entry, List<String> errorMsg) {
        ItemUniqueCodeGenerateParams.GenerateDetail value = entry.getValue();

        // 判断供应商
        if (StringUtils.isNotEmpty(value.getSupplierName())
                && importInfoCheckParams.getExistSupplierMap().get(value.getSupplierName()) == null) {
            errorMsg.add("第" + entry.getKey() + "行，供应商：" + value.getSupplierName() + "系统中不存在！");
            return;
        }
        if (StringUtils.isNotEmpty(value.getSupplierName())) {
            value.setSupplierId(importInfoCheckParams.getExistSupplierMap().get(value.getSupplierName()).getId());
        }

        // 判断仓库
        if (StringUtils.isNotEmpty(value.getWarehouseName())
                && importInfoCheckParams.getWarehouseMap().get(value.getWarehouseName()) == null) {
            errorMsg.add("第" + entry.getKey() + "行，仓库：" + value.getWarehouseName() + "系统中不存在！");
            return;
        }
        Long warehouseId = null;
        if (StringUtils.isNotEmpty(value.getWarehouseName())) {
            value.setWarehouseId(warehouseId = importInfoCheckParams.getWarehouseMap().get(value.getWarehouseName()).getId());
        }

        // 判断货位库存
        if (StringUtils.isNotEmpty(value.getGoodsSectionCode()) && !value.getGoodsSectionCode().contains("暂存区")) {
            if (importInfoCheckParams.getGoodsSectionMap().get(value.getWarehouseId() + "_" + value.getGoodsSectionCode()) == null) {
                errorMsg.add("第" + entry.getKey() + "行，货位：" + value.getGoodsSectionCode() + "系统中不存在！");
                return;
            } else {
                value.setGoodsSectionId(importInfoCheckParams.getGoodsSectionMap().get(value.getWarehouseId() + "_" + value.getGoodsSectionCode()).getId());
            }
            if (warehouseId != null && importInfoCheckParams.getWarehouseGoodsSectionMap().get(warehouseId + "_" + value.getGoodsSectionCode()) == null) {
                errorMsg.add("第" + entry.getKey() + "行，货位：" + value.getGoodsSectionCode() + "与仓库：" + value.getWarehouseName() + "不一致！");
                return;
            }
            value.setGoodsSectionId(importInfoCheckParams.getWarehouseGoodsSectionMap().get(warehouseId + "_" + value.getGoodsSectionCode()).getId());
        }

        //判断暂存位
        if (StringUtils.isNotEmpty(value.getGoodsSectionCode()) && value.getGoodsSectionCode().contains("暂存区")) {
            Integer stockPosition = UniqueCodeStockPositionEnum.getTypeByName(value.getGoodsSectionCode());
            if (stockPosition == null) {
                errorMsg.add("第" + entry.getKey() + "行，货位：" + value.getGoodsSectionCode() + "系统中不存在！");
                return;
            } else {
                value.setStockPosition(stockPosition);
            }
        }
    }

    private void importInfoCheck4Code1(ImportInfoCheckParams importInfoCheckParams, Map.Entry<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> entry, List<String> errorMsg) {
        WaveUniqueCode code = importInfoCheckParams.getCodeMap().get(entry.getValue().getUniqueCode());
        // 判断唯一码是否重复
        if (importInfoCheckParams.getExistsCodes().contains(entry.getValue().getUniqueCode())) {
            if (!Objects.equals(code.getStatus(), OrderUniqueCodeStatusEnum.WAIT_IN.getType())) {
                errorMsg.add("第" + entry.getKey() + "行，唯一码状态不符合！");
                return;
            }
            if (DataUtils.checkLongNotEmpty(code.getSid()) || DataUtils.checkLongNotEmpty(code.getOrderId())) {
                errorMsg.add("第" + entry.getKey() + "行，唯一码已绑定订单！");
                return;
            }
            if (DataUtils.checkLongNotEmpty(code.getBusinessId())) {
                errorMsg.add("第" + entry.getKey() + "行，唯一码已绑定采购单！");
                return;
            }
            if (Objects.equals(code.getOuterId(), entry.getValue().getOuterId())) {
                // 商品一样不更新，也不填充最优供应商
                entry.getValue().setOuterId(null);
                entry.getValue().setSysItemId(null);
                entry.getValue().setSysSkuId(null);
            }
            entry.getValue().setId(code.getId());
        } else {
            errorMsg.add("第" + entry.getKey() + "行，唯一码不存在！");
            return;
        }
    }

    private void importInfoCheck4Code0(ImportInfoCheckParams importInfoCheckParams, Map.Entry<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> entry, List<String> errorMsg) {
        // 判断唯一码是否重复
        List<String> existsCodes = importInfoCheckParams.getExistsCodes();
        if (entry.getValue().getNum() > 1) {
            for (String code : entry.getValue().getUniqueCodes()) {
                if (existsCodes.contains(code)) {
                    errorMsg.add("第" + entry.getKey() + "行，唯一码：" + code + "系统中已存在！");
                    return;
                }
            }
        } else {
            if (existsCodes.contains(entry.getValue().getUniqueCode())) {
                errorMsg.add("第" + entry.getKey() + "行，唯一码：" + entry.getValue().getUniqueCode() + "系统中已存在！");
                return;
            }
        }
    }

    public void importInfoCheck4Item(Map<String, Object> itemMap, Map.Entry<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> entry, List<String> errorMsg) {
        String outerId = entry.getValue().getOuterId();
        if (StringUtils.isEmpty(outerId)) {
            return;
        }
        if (!itemMap.containsKey(outerId)) {
            errorMsg.add("第" + entry.getKey() + "行，商家编码不存在！");
            return;
        }

        // 判断商品信息
        DmjItem item = (DmjItem) itemMap.get(outerId);
        if (item.getActiveStatus() != 1) {
            errorMsg.add("第" + entry.getKey() + "行，商品编码" + outerId + "对应的商品已停用！");
            return;
        } else if (CollectionUtils.isNotEmpty(item.getSkus())) {
            errorMsg.add("第" + entry.getKey() + "行，商品编码" + outerId + "对应的商品含有规格！");
            return;
        } else if (Integer.valueOf(1).equals(item.getIsVirtual())) {
            errorMsg.add("第" + entry.getKey() + "行，商品编码" + outerId + "对应的商品是虚拟商品！");
            return;
        } else if (StringUtils.isNotEmpty(item.getType()) && (Objects.equals(item.getType(), "1") || Objects.equals(item.getType(), "2"))) {
            errorMsg.add("第" + entry.getKey() + "行，商品编码" + outerId + "对应的商品是套件商品！");
            return;
        }

        //判断商品是否为唯一码类型商品
        if (Objects.equals(item.getUniqueCodeType(), 2)) {
            errorMsg.add("第" + entry.getKey() + "行，商品编码" + outerId + "为通用码类型商品！");
            return;
        }

        if (Objects.equals(entry.getValue().getStatus(), 11)) {
            if (Objects.equals(item.getHasProduct(), 1) && entry.getValue().getProductionDate() == null) {
                errorMsg.add("第" + entry.getKey() + "行，生产日期不能为空！");
                return;
            }
            if (Objects.equals(item.getHasBatch(), 1) && entry.getValue().getItemBatchNo() == null) {
                errorMsg.add("第" + entry.getKey() + "行，批次不能为空！");
                return;
            }
        }

        entry.getValue().setSysItemId(item.getSysItemId());
        if (item instanceof DmjSku) {
            entry.getValue().setSysSkuId(((DmjSku) item).getSysSkuId());
        } else {
            entry.getValue().setSysSkuId(0L);
        }

        // 覆盖多码的商家编码
        entry.getValue().setOuterId(item.getOuterId());

    }

    private ImportInfoCheckParams getImportInfoCheckParams(Staff staff, Set<Map.Entry<Integer, ItemUniqueCodeGenerateParams.GenerateDetail>> entries) {
        List<String> outerIds = entries.stream().map(en -> en.getValue().getOuterId()).distinct().collect(Collectors.toList());
        // 商品信息
        Map<String, Object> itemMap = itemServiceDubbo.queryItemSkuByOuterId(staff, outerIds, 123);
        // 多码查询商品
        itemMap.putAll(getMultiMap(staff, outerIds));
        // 供应商信息
        Map<String, Supplier> existSupplierMap = uniqueCodeBaseService.queryExistSupplierName(staff, entries.stream().map(en -> en.getValue().getSupplierName()).collect(Collectors.toList()));
        // 仓库信息
        Map<String, Warehouse> warehouseMap = uniqueCodeBaseService.queryExistWarehouse(staff);
        // 货位信息
        Map<String, GoodsSection> goodsSectionMap = uniqueCodeBaseService.queryExistGoodsSection(staff, entries.stream().map(en -> en.getValue().getGoodsSectionCode()).collect(Collectors.toList()));
        Map<String, GoodsSection> warehouseGoodsSectionMap = uniqueCodeBaseService.queryExistGoodsSection4Import(staff, warehouseMap.values().stream().map(Warehouse::getId).collect(Collectors.toList()), entries.stream().map(en -> en.getValue().getGoodsSectionCode()).collect(Collectors.toList()));
        // 唯一码
        List<String> importUniqueCodes = Lists.newArrayList();
        for (Map.Entry<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> entry : entries) {
            if (CollectionUtils.isNotEmpty(entry.getValue().getUniqueCodes())) {
                importUniqueCodes.addAll(entry.getValue().getUniqueCodes());
            } else {
                importUniqueCodes.add(entry.getValue().getUniqueCode());
            }
        }
        // 已存在的唯一码信息
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodesByCodes(staff, importUniqueCodes);
        Map<String, WaveUniqueCode> codeMap = codes.stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, a -> a, (a, b) -> a));
        List<String> existsCodes = CollectionUtils.isEmpty(codes) ? Collections.emptyList() : codes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
        return new ImportInfoCheckParams().setItemMap(itemMap).setExistSupplierMap(existSupplierMap)
                .setWarehouseMap(warehouseMap).setGoodsSectionMap(goodsSectionMap).setWarehouseGoodsSectionMap(warehouseGoodsSectionMap)
                .setCodeMap(codeMap).setExistsCodes(existsCodes);
    }

    private Map<String, DmjItem> getMultiMap(Staff staff, List<String> outerIds) {
        Map<String, DmjItem> multiMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(outerIds)) {
            return multiMap;
        }
        List<DmjItem> items = itemServiceDubbo.getItemByMulticode(staff, outerIds);
        if (CollectionUtils.isEmpty(items)) {
            return multiMap;
        }
        for (DmjItem item : items) {
            if (CollectionUtils.isEmpty(item.getMultiCodeList())) {
                continue;
            }
            for (String code : item.getMultiCodeList()) {
                multiMap.put(code, item);
            }
        }
        return multiMap;
    }

    public List<String> sortErrorMsgs(List<String> errorMsgs) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(errorMsgs)) {
            return result;
        }
        Map<Integer, List<String>> sortMap = new LinkedHashMap<>();
        // key排序
        fillErrorMsgMap(errorMsgs).entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(msg -> sortMap.put(msg.getKey(), msg.getValue()));
        for (Map.Entry<Integer, List<String>> entry : sortMap.entrySet()) {
            result.addAll(entry.getValue());
        }
        return result;
    }

    public List<String> sortErrorMsgs(Map<Integer, List<String>> errorMsgMap) {
        List<String> result = Lists.newArrayList();
        if (errorMsgMap == null || errorMsgMap.isEmpty()) {
            return result;
        }
        Map<Integer, List<String>> sortMap = new LinkedHashMap<>();
        // key排序
        errorMsgMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(msg -> sortMap.put(msg.getKey(), msg.getValue()));
        for (Map.Entry<Integer, List<String>> entry : sortMap.entrySet()) {
            result.addAll(entry.getValue());
        }
        return result;
    }

    public Map<Integer, List<String>> fillErrorMsgMap(List<String> errorMsgs) {
        Map<Integer, List<String>> errorMsgMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(errorMsgs)) {
            return errorMsgMap;
        }
        for (String errorMsg : errorMsgs) {
            boolean isNum = false;
            StringBuilder numStr = new StringBuilder();
            for (int i = 0; i < errorMsg.length(); i++) {
                char c = errorMsg.charAt(i);
                if (c >= '0' && c <= '9') {
                    numStr.append(c);
                    isNum = true;
                } else {
                    if (isNum) {
                        break;
                    }
                }
            }
            Integer num = Integer.parseInt(numStr.toString());
            List<String> errorMsgList = errorMsgMap.get(num) == null ? Lists.newArrayList() : errorMsgMap.get(num);
            errorMsgList.add(errorMsg);
            errorMsgMap.put(num, errorMsgList);
        }
        return errorMsgMap;
    }

    @Data
    @Accessors(chain = true)
    public class ImportBaseCheckParams {
        // 商家编码
        private String outerId;
        // 唯一码
        private String uniqueCode;
        // 收货单
        private String warehouseEntryCode;
        // 唯一码状态
        private String status;
        // 供应商
        private String supplierName;
        // 销售价
        private String sellingPrice;
        // 成本价
        private String costPrice;
        // 重量
        private String weight;
        // 生产日期
        private String productionDate;
        // 批次
        private String itemBatchNo;
        // 所在仓库
        private String warehouseName;
        // 货位编码
        private String goodsSectionCode;
        // 起始唯一码
        private String startUniqueCode;
        // 结束唯一码
        private String endUniqueCode;
        // 唯一码数量
        private String num;
        /**
         * ICCID
         */
        private String ICCID;
        private String extendField2;
        private String extendField3;
        private String extendField4;
        private String extendField5;
        private UniqueCodeAutoIncBusiness.UniqueCodeAutoIncVO uniqueCodeAutoIncVO;
        private boolean thirdWarehouseGenerate = false;
    }

    @Data
    @Accessors(chain = true)
    class ImportInfoCheckParams {
        private Map<String, Object> itemMap;
        private Map<String, Supplier> existSupplierMap;
        private Map<String, Warehouse> warehouseMap;
        private Map<String, GoodsSection> goodsSectionMap;
        private Map<String, GoodsSection> warehouseGoodsSectionMap;
        private Map<String, WaveUniqueCode> codeMap;
        private List<String> existsCodes;
    }

}

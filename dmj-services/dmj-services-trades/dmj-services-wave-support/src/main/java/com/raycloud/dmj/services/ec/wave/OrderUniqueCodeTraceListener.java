package com.raycloud.dmj.services.ec.wave;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.dao.wave.WaveUniqueCodeLogDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeTrace;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.domain.trades.utils.WaveUniqueCodeLogUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.model.ItemUniqueCodeQueryParams;
import com.raycloud.dmj.domain.wave.utils.OrderUniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.UniqueCodeUtils;
import com.raycloud.dmj.domain.wave.vo.UniqueCodeExtendField;
import com.raycloud.dmj.domain.wms.enums.WmsConfigExtInfoEnum;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.domain.ItemTraceActionEnum;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import com.raycloud.dmj.services.trades.wave.IItemUniqueCodeService;
import com.raycloud.dmj.services.trades.wave.IOrderUniqueCodeTagService;
import com.raycloud.dmj.services.trades.wave.IWaveWriteTradeTraceService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.ec.api.CommonEventSource;
import com.raycloud.ec.api.EventSourceBase;
import com.raycloud.ec.api.IEventListener;
import com.raycloud.ec.api.annotation.ListenerBind;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 订单唯一码监听
 * @author: chunri
 * @create: 2021-05-13 17:02
 **/
@Component
@ListenerBind("order.unique.code.trace")
public class OrderUniqueCodeTraceListener implements IEventListener {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private WaveUniqueCodeLogDao waveUniqueCodeLogDao;
    @Resource
    private IWaveWriteTradeTraceService waveWriteTradeTraceService;
    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;
    @Resource(name = "tbTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    IWarehouseService warehouseService;
    @Resource
    private IOrderUniqueCodeTagService uniqueCodeTagService;
    @Resource
    private IWmsService wmsService;

    @Override
    public void onObserved(EventSourceBase eventSourceBase) {
        CommonEventSource event = (CommonEventSource) eventSourceBase;
        Staff staff = event.getArg(0, Staff.class);
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> waveUniqueCodeLogDTOs = event.getArg(1, List.class);
        WaveUniqueOpType type = event.getArg(2, WaveUniqueOpType.class);
        String staffName = event.getArg(3, String.class);
        // 是否为备货唯一码
        Boolean isPrepare = event.getArg(4, Boolean.class) == null ? false : event.getArg(4, Boolean.class);
        // 其他信息，后续增加字段直接往这个类里拓展
        WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo otherInfo = Optional.ofNullable(event.getArg(5, WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo.class)).orElse(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo());
        String projectSource = event.getArg(6, String.class);
        if (StringUtils.isNotEmpty(staffName)) {
            staff.setName(staffName);
        }
        List<String> codes = waveUniqueCodeLogDTOs.stream().map(WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO::getUniqueCode).distinct().collect(Collectors.toList());

        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams().setNeedRelationMsg(false).setNeedCategroyAndCatName(false);
        if (WaveUniqueOpType.PRINT.equals(type) || WaveUniqueOpType.PRINT_TIME_TYPE.equals(type) || WaveUniqueOpType.PRINT_AFTER_RECEIVED_PICKED.equals(type) || WaveUniqueOpType.REFRESH_SUPPLIER.equals(type)) {
            params.setUniqueCodeIds(waveUniqueCodeLogDTOs.stream().map(WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO::getId).distinct().collect(Collectors.toList()));
        } else {
            params.setUniqueCodes(codes);
        }

        if (CollectionUtils.isEmpty(params.getUniqueCodeIds()) && CollectionUtils.isEmpty(params.getUniqueCodes())) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "唯一码日志记录异常，type：" + type));
            }
            return;
        }

        params.setAuthorityFilter(false);
        List<WaveUniqueCode> waveUniqueCodes = getByBatch(staff, params);
        fillUniqueCodes(waveUniqueCodeLogDTOs, waveUniqueCodes);
        if (WaveUniqueOpType.UPDATE_TAG.equals(type)) {
            uniqueCodeTagService.fillTagNames(staff, waveUniqueCodes);
        }
        Map<String, WaveUniqueCode> code2Identity = waveUniqueCodes.stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, Function.identity()));
        waveUniqueCodeLogDTOs.removeIf(dto -> !code2Identity.containsKey(dto.getUniqueCode()));
        waveUniqueCodeLogDao.batchInsert(staff, buildWaveUniqueLogs(staff, waveUniqueCodeLogDTOs, type, code2Identity, isPrepare, otherInfo, projectSource));

        // 记录订单日志
        unbindFillOldSid(code2Identity, waveUniqueCodeLogDTOs, type);
        Map<Long, List<WaveUniqueCode>> sid2CodesMap = code2Identity.values().stream()
                .filter(data -> data.getSid() != null && data.getSid() > 0L)
                .collect(Collectors.groupingBy(WaveUniqueCode::getSid));

        waveWriteTradeTraceService.batchAddTradeTrace(staff, buildTradeTraces(staff, type, sid2CodesMap, CollectionUtils.isEmpty(waveUniqueCodeLogDTOs) ? null : waveUniqueCodeLogDTOs.get(0).getSupplier(), waveUniqueCodeLogDTOs, projectSource));
    }

    // 已解绑要写订单日志，sid从LogDTOs.oldSid取
    private void unbindFillOldSid(Map<String, WaveUniqueCode> code2Identity, List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> waveUniqueCodeLogDTOs, WaveUniqueOpType type) {
        if (MapUtils.isEmpty(code2Identity) || CollectionUtils.isEmpty(waveUniqueCodeLogDTOs) || type == null) {
            return;
        }
        // 取消必解绑
        if (!type.equals(WaveUniqueOpType.ORDER_CANCEL) && !type.equals(WaveUniqueOpType.ORDER_CANCEL_AUTO) && !type.equals(WaveUniqueOpType.ORDER_CANCEL_REJECT)) {
            return;
        }

        Map<String, Long> uniqueCodeOldSidMap = waveUniqueCodeLogDTOs.stream().filter(dto -> dto.getUniqueCode() != null && dto.getOldSid() != null).collect(Collectors.toMap(WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO::getUniqueCode, WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO::getOldSid, (a1, a2) -> a1));
        for (Map.Entry<String, WaveUniqueCode> entry : code2Identity.entrySet()) {
            Long oldSid = uniqueCodeOldSidMap.get(entry.getKey());
            if (oldSid == null || oldSid <= 0L) {
                continue;
            }
            entry.getValue().setSid(oldSid);
        }
    }

    /**
     * params uniqueCodes 和 uniqueCodeIds 只有一个不为空
     * @param staff
     * @param params
     * @return
     */
    private List<WaveUniqueCode> getByBatch(Staff staff, ItemUniqueCodeQueryParams params) {
        List<WaveUniqueCode> codes = new ArrayList<>();
        if (!CollectionUtils.isEmpty(params.getUniqueCodes())) {
             for (List<String> subCodes : Lists.partition(params.getUniqueCodes(), 3000)) {
                 ItemUniqueCodeQueryParams subParams = new ItemUniqueCodeQueryParams();
                 BeanUtils.copyProperties(params, subParams);
                 subParams.setUniqueCodes(subCodes);
                 List<WaveUniqueCode> listByCodes = itemUniqueCodeService.queryItemUniqueCodeByCondition(staff, subParams);
                 if (!CollectionUtils.isEmpty(listByCodes)) {
                     codes.addAll(listByCodes);
                 }
             }
        } else {
            for (List<Long> subIds : Lists.partition(params.getUniqueCodeIds(), 3000)) {
                ItemUniqueCodeQueryParams subIdsParams = new ItemUniqueCodeQueryParams();
                BeanUtils.copyProperties(params, subIdsParams);
                subIdsParams.setUniqueCodeIds(subIds);
                List<WaveUniqueCode> listByIds = itemUniqueCodeService.queryItemUniqueCodeByCondition(staff, subIdsParams);
                if (!CollectionUtils.isEmpty(listByIds)) {
                    codes.addAll(listByIds);
                }
            }
        }
        return codes;
    }

    private static void fillUniqueCodes(List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> dtOs, List<WaveUniqueCode> waveUniqueCodes) {
        if (CollectionUtils.isEmpty(dtOs) || CollectionUtils.isEmpty(waveUniqueCodes)) {
            return;
        }
        Map<Long, String> uniqueCodeMap = waveUniqueCodes.stream().collect(Collectors.toMap(WaveUniqueCode::getId, WaveUniqueCode::getUniqueCode, (a, b) -> a));
        for (WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto : dtOs) {
            if (StringUtils.isNotEmpty(dto.getUniqueCode()) || !DataUtils.checkLongNotEmpty(dto.getId())) {
                continue;
            }
            dto.setUniqueCode(uniqueCodeMap.get(dto.getId()));
        }
    }

    private List<TradeTrace> buildTradeTraces(Staff staff, WaveUniqueOpType type, Map<Long, List<WaveUniqueCode>> sid2CodesMap, Supplier supplier, List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> waveUniqueCodeLogDTOs, String projectSource) {
        List<TradeTrace> tradeTraces = Lists.newArrayList();
        Map<String, String> uniqueCodeFreeScanOuterIdsMap = waveUniqueCodeLogDTOs.stream().filter(w -> StringUtils.isNotEmpty(w.getFreeScanOuterIds())).collect(Collectors.toMap(WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO::getUniqueCode, WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO::getFreeScanOuterIds, (a, b) -> a));
        Map<String, WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> codeLogMap = waveUniqueCodeLogDTOs.stream().collect(Collectors.toMap(WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO::getUniqueCode, a -> a, (a, b) -> a));
        sid2CodesMap.forEach((k, v) -> {
            String content = null;
            String action = "生成唯一码标签";
            List<String> codes = v.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
            String freeScanOuterIds = (MapUtils.isEmpty(uniqueCodeFreeScanOuterIdsMap) || CollectionUtils.isEmpty(codes)) ? "" : uniqueCodeFreeScanOuterIdsMap.get(codes.get(0));
            List<String> bindList = Lists.newArrayList();
            if (type.equals(WaveUniqueOpType.SEED_BIND) || type.equals(WaveUniqueOpType.POST_BIND) || type.equals(WaveUniqueOpType.BLIND_BIND) || type.equals(WaveUniqueOpType.PACK_BIND)) {
                bindList = v.stream().filter(code -> codeLogMap.get(code.getUniqueCode()) != null && Objects.equals(codeLogMap.get(code.getUniqueCode()).getBindType(), 2)).map(data -> String.format("%s（%s）", data.getOuterId(), data.getUniqueCode())).collect(Collectors.toList());
            }
            switch (type) {
                case CREATE:
                    List<String> labelList = v.stream().map(data -> String.format("%s(%s)", data.getOuterId(), data.getUniqueCode())).collect(Collectors.toList());
                    content = "生成唯一码标签:" + Joiner.on("、").useForNull("").join(labelList);
                    if (StringUtils.isNotEmpty(freeScanOuterIds)) {
                        content += " 免扫商品未生成唯一码：" + freeScanOuterIds;
                    }
                    break;
                case BIND_PURCHASE_ORDER:
                    List<String> labels = v.stream().map(data -> String.format("商家编码%s(唯一码%s)", data.getOuterId(), data.getUniqueCode())).collect(Collectors.toList());
                    content = "采购单审核生成商品唯一码:" + Joiner.on("、").useForNull("").join(labels);
                    break;
                case PRINT:
                    content = String.format("打印唯一码标签 %s", Strings.join(",", codes));
                    break;
                case ORDER_CANCEL:
                    content = String.format("唯一码标签%s手动更新为已取消", Strings.join(",", codes));
                    break;
                case ORDER_CANCEL_AUTO:
                    content = String.format("唯一码标签%s自动更新为已取消", Strings.join(",", codes));
                    break;
                case CHANGE_ITEM_GENERATE:
                    content = String.format("订单换商品新生成标签：%s", Strings.join(",", codes));
                    break;
                case ORDER_CANCEL_REJECT:
                    content = String.format("唯一码：%s取消", Strings.join(",",v.stream().map(uniqueCode->uniqueCode.getOuterId()+"("+uniqueCode.getUniqueCode()+")").collect(Collectors.toList())));
                    action = "唯一码取消";
                    break;
                case ORDER_RECEIVE:
                    List<String> receiveList = v.stream().filter(r -> !Objects.equals(r.getStatus(), OrderUniqueCodeStatusEnum.WAIT_PICK.getType()) && !Objects.equals(r.getStatus(), OrderUniqueCodeStatusEnum.PICKED.getType())).map(data -> String.format("【%s、%s】", data.getUniqueCode(), data.getOuterId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(receiveList)) {
                        content = "唯一码收货:" + Joiner.on(",").useForNull("").join(receiveList);
                    }
                    break;
                case INSPECTION:
                    List<String> infoList = v.stream().map(data -> String.format("【%s、%s】", data.getUniqueCode(), data.getOuterId())).collect(Collectors.toList());
                    content = "唯一码验货:" + Joiner.on(",").useForNull("").join(infoList);
                    break;
                case FAST_CONSIGN_INSPECTION:
                    List<String> fastConsignInfo = v.stream().map(data -> String.format("【%s、%s】", data.getUniqueCode(), data.getOuterId())).collect(Collectors.toList());
                    content = "快销发货验货:" + Joiner.on(",").useForNull("").join(fastConsignInfo);
                    break;
                case EXCEPT_ORDER_RECEIVE:
                    List<String> exceptReceiveList = v.stream().map(data -> String.format("【%s、%s】", data.getUniqueCode(), data.getOuterId())).collect(Collectors.toList());
                    content = "异常订单唯一码收货(验货出库):" + Joiner.on(",").useForNull("").join(exceptReceiveList);
                    break;
                case ORDER_MULTIPLEXING:
                    content = String.format("唯一码复用，被%s替换", Strings.join(",", codes));
                    break;
                case PRINT_UNIQUE_MERCHANT:
                    List<String> merchantList = v.stream().map(data -> String.format("【%s、%s】", data.getUniqueCode(), data.getOuterId())).collect(Collectors.toList());
                    content = "打印唯一码对应吊牌：" + Joiner.on(",").useForNull("").join(merchantList);
                    break;
                // 唯一码绑定订单
                case SEED_BIND:
                    if (!CollectionUtils.isEmpty(bindList)) {
                        content = "播种打印绑定唯一码标签:" + Joiner.on(",").useForNull("").join(bindList);
                    }
                    break;
                case POST_BIND:
                    if (!CollectionUtils.isEmpty(bindList)) {
                        content = "后置打印绑定唯一码标签:" + Joiner.on(",").useForNull("").join(bindList);
                    }
                    break;
                case BLIND_BIND:
                    if (!CollectionUtils.isEmpty(bindList)) {
                        content = String.format("%s盲扫绑定唯一码标签:%s", codeLogMap.get(v.get(0).getUniqueCode()).getOldWaveId(), Joiner.on(",").useForNull("").join(bindList));
                    }
                    break;
                case PACK_BIND:
                    if (!CollectionUtils.isEmpty(bindList)) {
                        if ("PDA".equalsIgnoreCase(projectSource)) {
                            content = "PDA包装验货绑定唯一码标签:" + Joiner.on(",").useForNull("").join(bindList);
                        } else {
                            content = "包装验货绑定唯一码标签:" + Joiner.on(",").useForNull("").join(bindList);
                        }
                    }
                    break;
                case UNIQUE_CODE_IMPORT_BIND_TRADE:
                    content = String.format("导入订单绑定，唯一码%s", Strings.join("；", codes));
                    break;
                case GENERATE_REUSE:
                    List<String> orderUniqueReuse = v.stream().map(data -> String.format("商家编码%s(唯一码%s)", data.getOuterId(), data.getUniqueCode())).collect(Collectors.toList());
                    content = "复用唯一码标签:" + Joiner.on("、").useForNull("").join(orderUniqueReuse);
                    break;
                case ITEM_UNIQUE_CODE_REUSE:
                    List<String> itemUniqueReuse = v.stream().map(data -> String.format("商家编码%s(唯一码%s)", data.getOuterId(), data.getUniqueCode())).collect(Collectors.toList());
                    content = "复用商品唯一码:" + Joiner.on("、").useForNull("").join(itemUniqueReuse);
                    break;
            }
            if (content != null) {
                TradeTrace tradeTrace = TradeTraceUtils.createTradeTrace(staff.getCompanyId(), k, action, (supplier == null ? staff.getName() : supplier.getName()), new Date(), content);
                tradeTraces.add(tradeTrace);
            }
        });
        return tradeTraces;
    }

    List<WaveUniqueCodeLog> buildWaveUniqueLogs(Staff staff, List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> waveUniqueCodeLogDTOS, WaveUniqueOpType type, Map<String, WaveUniqueCode> waveUniqueCodes, boolean isPrepare, WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo otherInfo, String projectSource) {
        List<WaveUniqueCodeLog> logs = Lists.newArrayList();
        // 驳回二次收货的唯一码
        List<WaveUniqueCode> rejectCodes = new ArrayList<>();
        Date now = new Date();
        String goodsSectionCode = otherInfo.getGoodsSectionCode();
        String itemTraceAction = otherInfo.getItemTraceAction();
        String logTemplate = otherInfo.getLogTemplate();
        for (List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> subList : Lists.partition(waveUniqueCodeLogDTOS, 200)) {
            for (WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto : subList) {
                String content = "";
                List<String> contents = Lists.newArrayList();
                String relationOrder = "";
                switch (type) {
                    case CREATE:
                        content = "创建唯一码 唯一码状态：" + OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus());
                        WaveUniqueCode uniqueCode = waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode());
                        if(uniqueCode.getShortSid()!=null) {
                            content += " 内部单号：" + uniqueCode.getShortSid();
                            relationOrder += "内部单号：" + uniqueCode.getShortSid();
                        }
                        if (DataUtils.checkLongNotEmpty(dto.getHotTradeThreshold())) {
                            content += " 爆款阈值：" + dto.getHotTradeThreshold();
                        }
                        break;
                    case GENERATE_REUSE:
                        content = String.format("唯一码复用，关联新订单：%s", waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getShortSid());
                        break;
                    case PURCHASE_CREATE_BACK:
                        content = "采购拿货打印生成唯一码，关联采购单：" + dto.getBusinessCode();
                        break;
                    case PRINT:
                        content = getPrintLogContent(waveUniqueCodes, dto);
                        break;
                    case PRINT_TIME_TYPE:
                    case PRINT_AFTER_RECEIVED_PICKED:
                        content = "打印唯一码 " + (Objects.equals(CommonConstants.JUDGE_YES, dto.getPrintTimeType()) ? "同唯一码生成时间" : "同系统时间");
                        if (dto.getCreated() != null) {
                            content += "，拆分更新生成时间";
                        }
                        if (Objects.equals(UniqueCodeOperateSourceEnum.CAIGOU_PRINT, dto.getOperateSource())) {
                            content += "（采购单打印）";
                        }
                        break;
                    case ORDER_RELEASE:
                        String unboundReason = (dto.getUnboundReason() == null ? buildCancelReason(staff, dto.getOldSid()) : dto.getUnboundReason());
                        content = unboundReason == null ? String.format("唯一码与原订单：%s解绑", dto.getOldShortSid()) : String.format("唯一码与原订单：%s解绑(原因: %s)", dto.getOldShortSid(), unboundReason);
                        relationOrder = "内部单号：" + dto.getOldShortSid();
                        break;
                    case ORDER_MULTIPLEXING:
                        content = String.format("唯一码复用，关联新订单：%s", dto.getOldShortSid()) + (isPrepare ? " 备->单" : "");
                        relationOrder = "内部单号：" + dto.getOldShortSid();
                        break;
                    case ORDER_REJECT:
                        content = String.format("唯一码驳回，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        break;
                    case MEMORANDUM_REJECT:
                        content = String.format("错标唯一码驳回，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        break;
                    case RECEIVE:
                        content = String.format("唯一码扫描已验货，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        break;
                    case ORDER_SEC_RECEIVE:
                        content = "唯一码二次收货/拣选";
                        break;
                    case INSPECTION:
                        content = "唯一码扫描验货：已验货";
                        break;
                    case ORDER_CANCEL_REJECT:
                        content = String.format("唯一码取消（复制新增），与原订单：%s解绑", dto.getOldShortSid());
                        break;
                    case ORDER_RETURN_EXCHANGE:
                        content = String.format("唯一码与原订单：%s解绑(原因: 退档换货)", dto.getOldShortSid());
                        break;
                    case ORDER_RE_GENERATE:
                        content = String.format("创建唯一码（取消复制新增） 唯一码状态：%s 内部单号：%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()), DataUtils.checkLongNotEmpty(dto.getOldShortSid()) ? dto.getOldShortSid() : "");
                        break;
                    case ORDER_RE_GENERATE_SPLIT:
                        content = String.format("创建唯一码（拆分新增） 唯一码状态：%s 内部单号：%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()), dto.getOldShortSid());
                        break;
                    case RETURN_EXCHANGE_RE_GENERATE:
                        content = String.format("创建唯一码（退档换货） 唯一码状态：%s 内部单号：%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()), dto.getOldShortSid());
                        break;
                    case CHANGE_ITEM_GENERATE:
                        content = String.format("创建唯一码（换商品） 唯一码状态：%s 内部单号：%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()), dto.getOldShortSid());
                        break;
                    case RELATION_CHANGED_GENERATE:
                        content = String.format("创建唯一码（对应关系异常换商品） 唯一码状态：%s 内部单号：%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()), dto.getOldShortSid());
                        break;
                    case ORDER_FORCE_EXAMINE:
                        content = "唯一码扫描验货（强制发货)";
                        break;
                    case ORDER_FORCE_INSPECTION:
                        content = "唯一码扫描验货（强制发货）：已验货";
                        break;
                    case ORDER_FORCE_UN_INSPECTION:
                        content = "唯一码扫描验货（强制发货）：未扫描";
                        break;
                    case ORDER_CANCEL:
                        content = ((dto.isPickReturnCancel() ? "唯一码库存归还，手动更新为已取消" : "唯一码手动更新为已取消") + getCancelReleaseText(dto));
                        break;
                    case REFRESH_SUPPLIER:
                        content = "唯一码供应商更新：" + DataUtils.getDefault(dto.getBeforeSupplierName(), "空") + " -> " + DataUtils.getDefault(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getSupplierName(), "无") ;
                        break;
                    case ORDER_CANCEL_AUTO:
                        String cancelReason = (dto.getUnboundReason() == null ? buildCancelReason(staff, dto.getOldSid()) : dto.getUnboundReason());
                        content = ((cancelReason == null ? "唯一码自动更新为已取消" : String.format("唯一码自动更新为已取消(原因: %s)", cancelReason)) + getCancelReleaseText(dto));
                        break;
                    case AUTO_UNBOUND:
                        content = "自动解绑";
                        break;
                    case ARRIVED_PRINT_RECEIVE:
                        content = String.format("唯一码到货打印自动收货，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        break;
                    case EXCEPT_ORDER_RECEIVE:
                        content = String.format("异常订单唯一码收货(验货出库)，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        break;
                    case RE_ALLOCATE:
                        content = getReallocateContent(waveUniqueCodes, dto);
                        break;
                    case UPDATE_TAG:
                        String tagNames = "";
                        List<String> tagNameList;
                        if (!CollectionUtils.isEmpty(tagNameList = waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getTagNames())) {
                            tagNames = String.join(",", tagNameList);
                        }
                        content = String.format("标签修改为：%s", tagNames);
                        break;
                    case ARRIVED_CHECK_RECEIVE_PRINT_UC:
                        content = "到货清点打印唯一码";
                        break;
                    // 下面是商品唯一码日志
                    case ITEM_BATCH_CREATE:
                        content = "批量生成唯一码";
                        break;
                    case PRINT_ITEM_GENERATE:
                        content = "商品打印生成";
                        break;
                    case BIND_WAREHOUSE_ENTRY:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getWarehouseEntryCode());
                        }
                        break;
                    case RELEASE_PURCHASE_ORDER_RELATION:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getBusinessCode());
                            if (DataUtils.checkIntegerNotEmpty(dto.getOldPrintNum()) && !DataUtils.checkIntegerNotEmpty(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getPrintNum())) {
                                content += "，打印状态：已打印->未打印";
                            }
                        }
                        break;
                    case ITEM_RECEIVE_BATCH_CREATE:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        } else {
                            content = "创建唯一码（收货打印） 唯一码状态：在库";
                        }
                        break;
                    case ORDER_RECEIVE:
                    case PURCHASE_RETURN_OUT:
                    case PRODUCT_ORDER_IN_WAREHOUSE:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        } else {
                            content = getDefaultContent(waveUniqueCodes, dto, rejectCodes);
                        }
                        break;
                    case ALLOCATE_OUT:
                    case ALLOCATE_IN:
                    case REPAIR_ORDER_RETURN:
                    case REPAIR_ORDER_OUT:
                    case STOCK_PRODUCT_FINISH:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getBusinessCode(), OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        }
                        break;
                    case OFF_SHELF_AND_OUT:
                    case ITEM_MOVE_STOCK:
                    case OTHER_WAREHOUSING_IN_ON_SHELF:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus())
                                    , dto.getGoodsSectionCode(), dto.getBeforeWarehouseName(), dto.getWarehouseName());
                        }
                        break;
                    case ALLOCATE_IN_GOODS_SECTION:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getBusinessCode(), OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()), dto.getGoodsSectionCode());
                        }
                        break;
                    case UNSHELVE_ORDER_OFF_SHELF:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getBusinessCode(),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()),
                                    dto.getBeforeWarehouseName(), dto.getWarehouseName());
                        }
                        break;
                    case MOVE_STOCK:
                    case PDA_ON_SHELF:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()), dto.getGoodsSectionCode());
                        }
                        break;
                    case MOVE_BOX:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getGoodsSectionCode());
                        }
                        break;
                    case PDA_OFF_SHELF:
                    case WAVE_PICK_ITEM_BOX:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, UniqueCodeStockPositionEnum.getNameByType(dto.getStockPosition()));
                        }
                        break;
                    case PACK_MOVE_STOCK:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, UniqueCodeStockPositionEnum.getNameByType(dto.getBeforeStockPosition()), UniqueCodeStockPositionEnum.getNameByType(dto.getStockPosition()));
                        }
                        break;
                    case CHANGE_BOX:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getBeforeBoxCode(), dto.getBoxCode(), dto.getGoodsSectionCode());
                        }
                        break;
                    case RECYCLE_UNIQUECODE:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getOldShortSid(),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        }
                        break;
                    case CHANGE_LABEL:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getOldShortSid(), dto.getOldWaveId());
                        }
                        break;
                    case ITEM_POST_BIND_CREATE:
                        content = "创建唯一码（后置绑定） 唯一码状态：已拣选";
                        break;
                    case POST_BIND_UNIQUE_CODE:
                        content = String.format("后置绑定唯一码 内部单号：%s", dto.getOldShortSid());
                        break;
                    case ITEM_UNIQUE_CODE_TRADE_BIND:
                        //绑定唯一码 单号：%s
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getOldSid());
                        }
                        break;
                    case ITEM_UNIQUE_CODE_PURCHASE_BIND:
                        // 唯一码绑定，绑定采购单：%s
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getBusinessCode());
                        }
                        break;
                    case ITEM_BATCH_CANCEL:
                    case BATCH_STATUS_MODIFY:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        } else {
                            content = String.format("批量作废，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        }
                        break;
                    case OTHER_WAREHOUSING_IN:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                    OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()),
                                    dto.getBeforeWarehouseName(), dto.getWarehouseName());
                        }
                        break;
                    case BATCH_SUPPLIER_MODIFY:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getBeforeSupplierName(), dto.getSupplierName());
                        }
                        break;
                    case DELETE_UNIQUE_CODE:
                        content = String.format("删除唯一码，操作人：%s", staff.getName());
                        break;
                    case ITEM_BATCH_IMPORT:
                    case SPLIT_BOX:
                    case WAVE_PICK_ITEM:
                    case CROSS_BORDER_GENERATE:
                    case TAKEN_ORDER_OUT:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = logTemplate;
                        } else {
                            content = "批量导入唯一码";
                        }
                        break;
                    case BIND_PURCHASE_ORDER:
                        if (StringUtils.isNotEmpty(logTemplate)) {
                            content = String.format(logTemplate, dto.getBusinessCode(), dto.getOldSid() != null ? dto.getOldSid() : "");
                        }
                        break;
                    case UNIQUE_CODE_IMPORT_BIND_TRADE:
                        content = String.format("导入订单绑定，唯一码状态更新: %s->已出库", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()));
                        break;
                    case UNIQUE_CODE_UPDATE_CODE_TYPE:
                        content = String.format("唯一码类型修改: %s->%s", UniqueCodeUtils.getCodeTypeName(dto.getOldCodeType()), UniqueCodeUtils.getCodeTypeName(dto.getCodeType()));
                        break;
                    case UNIQUE_CODE_IMPORT_MODIFY:
                        content = getContentForUniqueCodeImportModify(staff, dto, waveUniqueCodes);
                        break;
                    case AFTER_SALE_GENERATE:
                        content = String.format("创建唯一码（售后），唯一码状态：%s", OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        break;
                    case ITEM_OUT:
                        content = String.format("验货出库，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        break;
                    case AFTER_SALE_REGISTER:
                        content = String.format("退货登记，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        relationOrder = "售后工单：" + dto.getAfterSaleOrderCode();
                        break;
                    case AFTER_SALE_ON_SHELF:
                        content = String.format("销退上架，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        relationOrder = "售后工单：" + dto.getAfterSaleOrderCode();
                        break;
                    case PDA_AFTER_SALE_ON_SHELF:
                        content = String.format("PDA销退上架，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        relationOrder = "售后工单：" + dto.getAfterSaleOrderCode();
                        break;
                    case AFTER_SALE_INVALID:
                        content = String.format("关联售后工单作废，自动解绑，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        relationOrder = "售后工单：" + dto.getAfterSaleOrderCode();
                        break;
                    case CANCEL_CONSIGN:
                        content = String.format("%s订单撤销发货，状态更新：%s->%s, 并解除订单绑定关系",
                                dto.getOldShortSid() == null ? StringUtils.EMPTY : String.valueOf(dto.getOldShortSid()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        break;
                    case OTHER_IN:
                        content = String.format("其他入库，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        relationOrder = "其他入库：" + dto.getBusinessCode();
                        break;
                    case OTHER_OUT:
                        if (StringUtils.isBlank(logTemplate)) {
                            logTemplate = "其他出库，状态更新：%s->%s";
                        }
                        content = String.format(logTemplate, OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        relationOrder = "其他出库：" + dto.getBusinessCode();
                        break;
                    case REVERT_OFF_SHELF:
                        content = String.format("撤销下架，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        break;
                    case REVERT_OFF_SHELF_BY_ITEM:
                        content = String.format("商品上架，系统自动撤销下架，状态更新：%s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        break;
                    case SPLIT_SUIT_2_SINGLE:
                        content = "套件拆分，套件转单品";
                        break;
                    case CHECK_UNIQUE_CODE_BACK:
                        content = "售后转发质检 唯一码状态更新: 已退回->已拣选";
                        relationOrder = "质检扫描：" + dto.getBusinessCode();
                        break;
                    case CHECK_UNIQUE_CODE:
                        content = "唯一码质检扫描";
                        relationOrder = "质检扫描：" + dto.getBusinessCode();
                        break;
                    case CHECK_DEFECTIVE_UNIQUE_CODE:
                        content = "唯一码扫描质检为次品";
                        relationOrder = "质检扫描：" + dto.getBusinessCode();
                        break;
                    case SCAN_PICK:
                        content = "唯一码分拣扫描";
                        break;
                    case TIMEOUT_AUTO_CANCEL:
                        content = String.format("订单唯一码超时60天，状态更新：%s->%s" + getCancelReleaseText(dto), OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()),
                                OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        dto.setSystemHandle(true);
                        break;

                    // wms操作
                    case WORK_STORAGE_UP_SHELVE:
                        if (Objects.equals(itemTraceAction, ItemTraceActionEnum.WMS_PDA_REFUND_SHELVE.toString())) {
                            content = String.format("PDA销退上架，上架货位：%s", goodsSectionCode);
                        } else if (Objects.equals(itemTraceAction, ItemTraceActionEnum.WMS_PDA_PICK_SHELVE.toString())) {
                            content = String.format("PDA拣选归还，上架货位：%s", goodsSectionCode);
                        } else if (Objects.equals(itemTraceAction, ItemTraceActionEnum.WMS_PDA_PURCHASE_SHELVE.toString())) {
                            content = String.format("PDA入库上架，上架货位：%s", goodsSectionCode);
                        }else {
                            continue;
                        }
                        break;
                    case WAVE_PICKING:
                        content = String.format("PDA波次拣选下架，波次号：%s", otherInfo.getWaveId());
                        break;
                    case INVENTORY:
                        content = String.format("PDA货位盘点，盘点货位：%s", goodsSectionCode);
                        break;
                    case PURCHASE_RETURN:
                        content = String.format("PDA采购退货，采退货位：%s", dto.getGoodsSectionCode());
                        break;
                    case SCAN_PURCHASE_RETURN:
                        content = String.format("%s扫描采退",  otherInfo.getSource());
                        break;
                    // 唯一码绑定或验货
                    case SEED_BIND:
                        if (Objects.equals(dto.getBindType(), 1)) {
                            content = "唯一码播种打印：已验货";
                        } else if (Objects.equals(dto.getBindType(), 2)) {
                            contents.add("唯一码播种打印：已验货");
                            contents.add(String.format("播种打印绑定唯一码 内部单号：%s", dto.getOldShortSid()));
                        } else {
                            continue;
                        }
                        break;
                    // pda唯一码绑定或验货
                    case PDA_SEED_BIND:
                        if (Objects.equals(dto.getBindType(), 1)) {
                            content = String.format("PDA波次播种，波次号：%s，位置号：%s", dto.getTempWaveId(), dto.getPositionNo());
                        } else if (Objects.equals(dto.getBindType(), 2)) {
                            contents.add(String.format("PDA波次播种，波次号：%s，位置号：%s", dto.getTempWaveId(), dto.getPositionNo()));
                            contents.add(String.format("PDA波次播种绑定唯一码 内部单号：%s", dto.getOldShortSid()));
                        } else {
                            continue;
                        }
                        break;
                    case POST_BIND:
                        if (Objects.equals(dto.getBindType(), 1)) {
                            content = "唯一码后置打印：已验货";
                        } else if (Objects.equals(dto.getBindType(), 2)) {
                            contents.add("唯一码后置打印：已验货");
                            contents.add(String.format("后置打印绑定唯一码 内部单号：%s", dto.getOldShortSid()));
                        } else {
                            continue;
                        }
                        break;
                    case POST_BIND_CHANGE_WAREHOUSE:
                        if (dto.getOldWaveId() != null && dto.getOldWaveId() < 0) {
                            content = String.format("%s盲扫绑定更新仓库：%s", dto.getOldWaveId(), warehouseService.queryLightById(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getWarehouseId()).getName());
                        } else {
                            content = String.format("后置绑定更新仓库：%s", warehouseService.queryLightById(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getWarehouseId()).getName());
                        }
                        break;
                    case BLIND_BIND:
                        if (Objects.equals(dto.getBindType(), 1)) {
                            content = String.format("唯一码%s盲扫：已验货", dto.getOldWaveId());
                        } else if (Objects.equals(dto.getBindType(), 2)) {
                            contents.add(String.format("唯一码%s盲扫：已验货", dto.getOldWaveId()));
                            contents.add(String.format("%s盲扫绑定唯一码 内部单号：%s", dto.getOldWaveId(), dto.getOldShortSid()));
                        } else {
                            continue;
                        }
                        break;
                    case PACK_BIND:
                        if (Objects.equals(dto.getBindType(), 1)) {
                            if ("PDA".equalsIgnoreCase(projectSource)) {
                                content = "唯一码PDA包装验货：已验货";
                            } else {
                                content = "唯一码包装验货：已验货";
                            }
                        } else if (Objects.equals(dto.getBindType(), 2)) {
                            if ("PDA".equalsIgnoreCase(projectSource)) {
                                contents.add("唯一码PDA包装验货：已验货");
                                contents.add(String.format("PDA包装验货绑定唯一码 内部单号：%s", dto.getOldShortSid()));
                            } else {
                                contents.add("唯一码包装验货：已验货");
                                contents.add(String.format("包装验货绑定唯一码 内部单号：%s", dto.getOldShortSid()));
                            }
                        } else {
                            continue;
                        }
                        break;
                    // 唯一码解绑
                    case RESEED_RELEASE:
                        contents.add("播种打印操作重新播种");
                        contents.add(String.format("唯一码与原订单：%s解绑(原因: 重新验货)", dto.getOldShortSid()));
                        break;
                    case COVER_RELEASE:
                        contents.add("播种打印操作复验");
                        contents.add(String.format("唯一码与原订单：%s解绑(原因:复验)", dto.getOldShortSid()));
                        break;
                    case POST_REBIND_UNIQUE_RELEASE_OLD:
                        contents.add(String.format("唯一码与原订单：%s解绑(原因:后置打印开启重新绑定唯一码)", dto.getOldShortSid()));
                        break;
                    case THIRD_WAREHOUSE_CREATE:
                        content = "三方仓对接创建唯一码 唯一码状态：在库";
                        relationOrder = String.format("关联%s：%s", UniqueCodeRelation.BusinessType.getDesc(dto.getBusinessType()), dto.getRelationBusinessCode());
                        break;
                    case THIRD_WAREHOUSE_UPDATE:
                        content = String.format("三方仓更新 唯一码状态：%s", OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus()));
                        relationOrder = String.format("关联%s：%s", UniqueCodeRelation.BusinessType.getDesc(dto.getBusinessType()), dto.getBusinessCode());
                        break;
                    case PDA_ON_SHELF_MULTI_SPLIT:
                        content = "混放上架，多件订单执行拆分";
                        break;
                    case UNIQUE_RECEIVE_STATUS_SPLIT:
                        content = String.format("唯一码到货拆分，原订单：%s 绑定新订单：%s", dto.getOldSid(), dto.getNewSid());
                        break;
                    case FAST_CONSIGN_SPLIT:
                        content = String.format("快销发货拆分，原订单：%s 绑定新订单：%s", dto.getOldSid(), dto.getNewSid());
                        break;
                    case PURCHASE_CODE_STOCK_CREATE:
                        content = "采购单审核，有货部分生成唯一码，绑定订单：" + dto.getOldSid();
                        break;
                    case FAST_CONSIGN_INSPECTION:
                        content = "快销发货验货: 已验货";
                        break;
                    case FAST_CONSIGN_FORCE_INSPECTION:
                        content = "快销发货扫描验货（强制发货）：已验货";
                        break;
                    case FAST_CONSIGN_UN_INSPECTION:
                        content = "快销发货扫描验货（强制发货）：未扫描";
                        break;
                    case ITEM_UNIQUE_CODE_REUSE:
                        // 记录有绑定日志,不记录唯一码复用日志
                        break;
                    default:
                        content = getDefaultContent(waveUniqueCodes, dto, rejectCodes);
                }
                if (!CollectionUtils.isEmpty(contents)) {
                    for (String c : contents) {
                        if (StringUtils.isBlank(c)) {
                            continue;
                        }
                        WaveUniqueCodeLog log = WaveUniqueCodeLogUtils.buildWaveUniqueCodeLog(staff, type, dto.getUniqueCode(), c, relationOrder);
                        fillOperateInfo(dto, log);
                        fillOtherLogInfo(log, waveUniqueCodes.get(dto.getUniqueCode()), dto);
                        logs.add(log);
                    }
                } else {
                    if (StringUtils.isNotBlank(content)) {
                        WaveUniqueCodeLog log = WaveUniqueCodeLogUtils.buildWaveUniqueCodeLog(staff, type, dto.getUniqueCode(), content, relationOrder);
                        fillOperateInfo(dto, log);
                        fillOtherLogInfo(log, waveUniqueCodes.get(dto.getUniqueCode()), dto);
                        logs.add(log);
                    }
                }
            }
            convertRejectUniqueCodeLog(staff, type, rejectCodes, logs);
        }
        return logs;
    }

    private String getContentForUniqueCodeImportModify(Staff staff, WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto, Map<String, WaveUniqueCode> waveUniqueCodes) {
        String content = "";
        if (StringUtils.isNotBlank(dto.getBeforeUniqueCode()) && StringUtils.isNotBlank(dto.getUniqueCode()) && !Objects.equals(dto.getBeforeUniqueCode(), dto.getUniqueCode())) {
            content += String.format("唯一码：%s  修改为：%s；", dto.getBeforeUniqueCode(), dto.getUniqueCode());
        }
        if (StringUtils.isNotBlank(dto.getOuterId()) && !Objects.equals(dto.getBeforeOuterId(), dto.getOuterId())) {
            content += String.format("商家编码：%s  修改为：%s；", dto.getBeforeOuterId(), dto.getOuterId());
        }
        if (StringUtils.isNotBlank(dto.getSupplierName()) && !Objects.equals(dto.getBeforeSupplierName(), dto.getSupplierName())) {
            content += String.format("供应商：%s  修改为：%s；", dto.getBeforeSupplierName(), dto.getSupplierName());
        }
        if (dto.getSellingPrice() != null && !Objects.equals(dto.getBeforeSellingPrice(), dto.getSellingPrice())) {
            content += String.format("销售价：%s  修改为：%s；", dto.getBeforeSellingPrice(), dto.getSellingPrice());
        }
        if (dto.getCostPrice() != null && !Objects.equals(dto.getBeforeCostPrice(), dto.getCostPrice())) {
            content += String.format("成本价：%s  修改为：%s；", dto.getBeforeCostPrice(), dto.getCostPrice());
        }
        if (dto.getWeight() != null && !Objects.equals(dto.getBeforeWeight(), dto.getWeight())) {
            content += String.format("重量：%s  修改为：%s；", dto.getBeforeWeight(), dto.getWeight());
        }
        if (dto.getProductionDate() != null && !Objects.equals(dto.getBeforeProductionDate(), dto.getProductionDate())) {
            content += String.format("生产日期：%s  修改为：%s；", DateUtils.datetime2Str(dto.getBeforeProductionDate()), DateUtils.datetime2Str(dto.getProductionDate()));
        }
        if (StringUtils.isNotBlank(dto.getWarehouseName()) && !Objects.equals(dto.getBeforeWarehouseName(), dto.getWarehouseName())) {
            content += String.format("仓库名称：%s  修改为：%s；", dto.getBeforeWarehouseName(), dto.getWarehouseName());
        }
        if (StringUtils.isNotBlank(dto.getGoodsSectionCode()) && !Objects.equals(dto.getBeforeGoodsSectionCode(), dto.getGoodsSectionCode())) {
            content += String.format("货位编码：%s  修改为：%s；", dto.getBeforeGoodsSectionCode(), dto.getGoodsSectionCode());
        }
        if (StringUtils.isNotBlank(dto.getItemBatchNo()) && !Objects.equals(dto.getBeforeItemBatchNo(), dto.getItemBatchNo())) {
            content += String.format("批次：%s  修改为：%s；", dto.getBeforeItemBatchNo(), dto.getItemBatchNo());
        }
        if (dto.getSupplierName() != null && !Objects.equals(dto.getBeforeSupplierName(), dto.getSupplierName())) {
            content += String.format("供应商：%s  更新为：%s；", dto.getBeforeSupplierName(), dto.getSupplierName());
        }
        if (dto.getSupplierCategory() != null && !Objects.equals(dto.getBeforesupplierCategory(), dto.getSupplierCategory())) {
            content += String.format("供应商分类：%s  更新为：%s；", dto.getBeforesupplierCategory(), dto.getSupplierCategory());
        }
        Integer oldStatus = dto.getOldStatus();
        Integer newStatus = waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus();
        boolean printStatus = DataUtils.checkIntegerNotEmpty(oldStatus) && DataUtils.checkIntegerNotEmpty(newStatus) && !Objects.equals(oldStatus, newStatus);
        if (printStatus) {
            content += String.format("状态：%s  修改为：%s；", OrderUniqueCodeStatusEnum.getNameByStatus(oldStatus), OrderUniqueCodeStatusEnum.getNameByStatus(newStatus));
        }
        if (StringUtils.isNotBlank(dto.getICCID()) && !Objects.equals(dto.getBeforeICCID(), dto.getICCID())) {
            content += String.format("ICCID：%s  修改为：%s；", dto.getBeforeICCID(), dto.getICCID());
        }
        content = fill4ExtendField(staff, content, dto);
        return content;
    }

    private String fill4ExtendField(Staff staff, String content, WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto) {
        Map<String, Object> extInfoMap = wmsService.getConfig(staff).getExtInfoMap();
        if (MapUtils.isEmpty(extInfoMap)) {
            return content;
        }
        Object fields = extInfoMap.get(WmsConfigExtInfoEnum.UNIQUE_CODE_EXTEND_FIELD.getKey());
        if (fields == null) {
            return content;
        }
        Map<String, String> fieldMap = JSON.parseArray(fields.toString(), UniqueCodeExtendField.class).stream().collect(Collectors.toMap(UniqueCodeExtendField::getCode, UniqueCodeExtendField::getName));
        if (StringUtils.isNotBlank(dto.getExtendField2()) && !Objects.equals(dto.getBeforeExtendField2(), dto.getExtendField2())) {
            content += String.format("%s属性：%s修改为：%s；", StringUtils.isNotEmpty(fieldMap.get("extendField2")) ? fieldMap.get("extendField2") : "属性2", dto.getBeforeExtendField2(), dto.getExtendField2());
        }
        if (StringUtils.isNotBlank(dto.getExtendField3()) && !Objects.equals(dto.getBeforeExtendField3(), dto.getExtendField3())) {
            content += String.format("%s属性：%s修改为：%s；", StringUtils.isNotEmpty(fieldMap.get("extendField3")) ? fieldMap.get("extendField3") : "属性3", dto.getBeforeExtendField3(), dto.getExtendField3());
        }
        if (StringUtils.isNotBlank(dto.getExtendField4()) && !Objects.equals(dto.getBeforeExtendField4(), dto.getExtendField4())) {
            content += String.format("%s属性：%s修改为：%s；", StringUtils.isNotEmpty(fieldMap.get("extendField4")) ? fieldMap.get("extendField4") : "属性4", dto.getBeforeExtendField4(), dto.getExtendField4());
        }
        if (StringUtils.isNotBlank(dto.getExtendField5()) && !Objects.equals(dto.getBeforeExtendField5(), dto.getExtendField5())) {
            content += String.format("%s属性：%s修改为：%s；", StringUtils.isNotEmpty(fieldMap.get("extendField5")) ? fieldMap.get("extendField5") : "属性5", dto.getBeforeExtendField5(), dto.getExtendField5());
        }
        return content;
    }

    private String getDefaultContent(Map<String, WaveUniqueCode> waveUniqueCodes, WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto, List<WaveUniqueCode> rejectCodes) {
        WaveUniqueCode waveUniqueCode = waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode());
        String content = String.format("唯一码状态更新: %s->%s", OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus()), OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCode.getStatus()));
        if (dto.getOperateSource() != null) {
            content += "(" + dto.getOperateSource().getName() + ")";
        }
        if (OrderUniqueCodeUtils.isIncludeRejectTag(waveUniqueCode) && Objects.equals(waveUniqueCode.getStatus(), OrderUniqueCodeStatusEnum.OUT_NOT_RECIVE.getType())) {
            rejectCodes.add(waveUniqueCode);
        }
        return content;
    }

    private String getReallocateContent(Map<String, WaveUniqueCode> waveUniqueCodes, WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto) {
        String content;
        content = "唯一码重新配货";
        WaveUniqueCode waveUniqueCode = waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode());
        String oldStatus = OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus());
        String newStatus = OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCode.getStatus());

        String oldTags = OrderUniqueCodeTagEnum.getNamesByIds(dto.getTagIds());
        String newTags = OrderUniqueCodeTagEnum.getNamesByIds(waveUniqueCode.getTagIds());

        String oldGoodsSectionCode = Optional.ofNullable(dto.getGoodsSectionCode()).orElse(StringUtils.EMPTY);
        String newGoodsSectionCode = Optional.ofNullable(waveUniqueCode.getGoodsSectionCode()).orElse(StringUtils.EMPTY);
        if (!Objects.equals(oldStatus, newStatus)) {
            content += String.format(" ，状态变更：%s->%s", oldStatus, newStatus);
        }
        if (!Objects.equals(oldTags, newTags)) {
            content += String.format(" ，标签变更：%s->%s", oldTags, newTags);
        }
        if (!Objects.equals(oldGoodsSectionCode, newGoodsSectionCode)) {
            content += String.format(" ，拣选货位变更：%s->%s", oldGoodsSectionCode, newGoodsSectionCode);
        }
        return content;
    }

    private static String getPrintLogContent(Map<String, WaveUniqueCode> waveUniqueCodes, WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto) {
        // 唯一码老状态
        String oldNameByStatus = OrderUniqueCodeStatusEnum.getNameByStatus(dto.getOldStatus());
        // 唯一码新状态
        String newNameByStatus = OrderUniqueCodeStatusEnum.getNameByStatus(waveUniqueCodes.getOrDefault(dto.getUniqueCode(), new WaveUniqueCode()).getStatus());
        if (Objects.equals(oldNameByStatus, newNameByStatus)) {
            return "打印唯一码";
        } else {
            if (Objects.equals(newNameByStatus, "已收货")) {
                // 到货清点自动收货打印唯一码日志状态置为等待收货
                newNameByStatus = "等待收货";
            }
            return String.format("打印唯一码 唯一码状态更新：%s->%s", oldNameByStatus, newNameByStatus);
        }
    }

    private static void fillOtherLogInfo(WaveUniqueCodeLog log, WaveUniqueCode code, WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto) {
        if (log == null) {
            return;
        }
        if (code != null) {
            log.setCustomPrintTime(code.getCustomPrintTime());
            log.setStatus(dto.getCurrentStatus() == null ? code.getStatus() : dto.getCurrentStatus());
        }
        log.setOperateSource(dto == null || dto.getOperateSource() == null
                // 默认PC操作
                ? CommonConstants.JUDGE_YES
                : dto.getOperateSource().getSource());
    }

    public String buildCancelReason(Staff staff, Long sid) {
        Trade trade = tradeSearchService.queryBySid(staff, false, sid);
        if (trade != null) {
            if (CommonConstants.VALUE_YES.equals(trade.getIsCancel())) {
                //订单作废
                return UnboundReasonTypeEnum.ORDER_VOID.getDesc();
            } else {
                switch (trade.getSysStatus()) {
                    case Trade.SYS_STATUS_WAIT_AUDIT:
                        //反审核
                        return UnboundReasonTypeEnum.ORDER_DE_AUDIT.getDesc();
                    case Trade.SYS_STATUS_CLOSED:
                        //交易关闭
                        return UnboundReasonTypeEnum.ORDER_TRANSACTION_CLOSED.getDesc();
                    case Trade.SYS_STATUS_FINISHED:
                        //交易完成
                        return UnboundReasonTypeEnum.ORDER_TRANSACTION_SUCCEEDED.getDesc();
                    case Trade.SYS_STATUS_SELLER_SEND_GOODS:
                        //订单发货
                        return UnboundReasonTypeEnum.ORDER_SHIPPED.getDesc();
                    default:
                        return null;
                }
            }
        }
        return null;
    }

    private static void fillOperateInfo(WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto, WaveUniqueCodeLog log) {
        if (dto.getSupplier() != null) {
            log.setStaffName(dto.getSupplier().getName());
            log.setStaffId(dto.getSupplier().getId());
        }
        if (dto.isSystemHandle() == true){
            log.setStaffName("系统自动操作");
        }
    }

    public void convertRejectUniqueCodeLog(Staff staff, WaveUniqueOpType type, List<WaveUniqueCode> codes, List<WaveUniqueCodeLog> logs) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }

        codes.forEach(t -> logs.add(WaveUniqueCodeLogUtils.buildWaveUniqueCodeLog(staff, type, t.getUniqueCode(), "收货失败，收货失败原因: 驳回的唯一码", null)));
    }

    private static String getCancelReleaseText(WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto) {
        if (dto == null || dto.getOldSid() == null || dto.getOldSid() == 0) {
            return "";
        } else {
            return " 并与原订单" + dto.getOldSid() + "解绑";
        }
    }
}

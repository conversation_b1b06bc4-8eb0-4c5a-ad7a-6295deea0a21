package com.raycloud.dmj.dao.wave;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.domain.wave.CrossBorderWavePrintInfo;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.DbInfo;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.CompanyUtils;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 波次分拣
 * Created by shaoxianchang on 2017/2/6.
 */
@Repository
public class WaveSortingDao extends WaveBaseDao {

    private static final int MERGE_SQL_SIZE = 2000;

    public WaveSortingDao() {
        this.domain = "WaveSorting";
    }

    private void init(Staff staff, WaveSorting obj) {
        obj.setCompanyId(staff.getCompanyId());
        obj.setDbNo(staff.getDbInfo().getWaveSortingDbNo());
        obj.setDbKey(staff.getDbNo());
    }

    private void initDetail(Staff staff, WaveSortingDetail obj) {
        obj.setCompanyId(staff.getCompanyId());
        obj.setDbNo(staff.getDbInfo().getWaveSortingDetailDbNo());
        obj.setDbKey(staff.getDbNo());
    }

    public void insert(Staff staff, WaveSorting waveSorting) {
        init(staff, waveSorting);
        getSqlMapClientTemplate(staff).insert(buildStatementName("insert"), waveSorting);
    }

    public void batchInsertOld(Staff staff, List<WaveSorting> waveSortings) {
        for (WaveSorting waveSorting : waveSortings) {
            init(staff, waveSorting);
            Long id = (Long) getSqlMapClientTemplate(staff).insert(buildStatementName("insert"), waveSorting);
            waveSorting.setId(id);
        }
    }

    public void batchInsert(Staff staff, List<WaveSorting> waveSortings,Boolean withPositionNo) {
        if (CollectionUtils.isEmpty(waveSortings)) {
            return;
        }

        List<Long> waveIds = new ArrayList<>();
        List<Long> sids = new ArrayList<>();
        for (WaveSorting waveSorting : waveSortings) {
            init(staff, waveSorting);
            waveIds.add(waveSorting.getWaveId());
            sids.add(waveSorting.getSid());
        }
        batchInsertMerge(staff, waveSortings);
        // 回表查询新插入的id并填充
        queryAndFillInsertId(staff, waveSortings, waveIds, sids,withPositionNo);
    }

    public void batchInsertMerge(Staff staff, List<WaveSorting> waveSortings) {
        Field[] declaredFields = WaveSorting.class.getDeclaredFields();
        Map<String, List<WaveSorting>> mergeSqlMaps = waveSortings.stream().collect(Collectors.groupingBy(r -> WmsUtils.createSqlMergeKey(declaredFields, r)));
        for (Map.Entry<String, List<WaveSorting>> entry : mergeSqlMaps.entrySet()) {
            Map<String, Object> map = Maps.newHashMap();
            List<WaveSorting> values = entry.getValue();
            List<List<WaveSorting>> partitions = Lists.partition(values, MERGE_SQL_SIZE);
            for (List<WaveSorting> partition : partitions) {
                map.put("column", partition.get(0));
                map.put("values", partition);
                getSqlMapClientTemplate(staff).insert("WaveSorting.insertMerge", map);
            }
        }
    }

    public void queryAndFillInsertId(Staff staff, List<WaveSorting> waveSortings, List<Long> waveIds, List<Long> sids,Boolean withPositionNo) {
        List<WaveSorting> list = queryByWaveIdsAndSids(staff, sids, waveIds);
        Map<String, Long> map = list.stream().collect(Collectors.toMap(t -> t.getWaveId() + "_" + t.getSid() + (BooleanUtils.isTrue(withPositionNo) ? ("_" + t.getPositionNo()) : ""), WaveSorting::getId, (v1, v2) -> v2));
        waveSortings.forEach(t -> t.setId(map.get(t.getWaveId() + "_" + t.getSid() + (BooleanUtils.isTrue(withPositionNo) ? ("_" + t.getPositionNo()) : ""))));
    }

    public void updateStatus(Staff staff, WaveSorting waveSorting) {
        init(staff, waveSorting);
        getSqlMapClientTemplate(staff).delete(buildStatementName("updateStatus"), waveSorting);
    }

    public void batchUpdateStatus(Staff staff, List<WaveSorting> waveSortings) {
        for (WaveSorting waveSorting : waveSortings) {
            init(staff, waveSorting);
        }
        sortWaveSortingList(waveSortings);
        batchUpdate(buildStatementName("updateStatus"), waveSortings);
    }

    public void rollBackPostStatus(Staff staff, Long waveId,List<Long> sidList) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("waveId", waveId);
        params.put("sids", sidList);
        getSqlMapClientTemplate(staff).delete(buildStatementName("rollBackPostStatus"), params);
    }

    private void sortWaveSortingList(List<WaveSorting> waveSortings) {
        if (CollectionUtils.isEmpty(waveSortings)) {
            return;
        }
        List<WaveSorting> sortList = waveSortings.stream().filter(ws -> null != ws.getId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sortList) && sortList.size() == waveSortings.size()) {
            waveSortings.sort(Comparator.comparing(WaveSorting::getId));
        }
    }

    public WaveSorting queryById(Staff staff, Long id) {
        if (id == null) {
            throw new IllegalArgumentException("请输入id");
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("id", id);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());

        return (WaveSorting) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryById"), params);
    }

    public List<WaveSorting> queryByIds(Staff staff, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("请输入ids");
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("ids", ids);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryById"), params);
    }

    public WaveSorting queryBySid(Staff staff, Long sid) {
        if (sid == null) {
            throw new IllegalArgumentException("请输入sid");
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("sid", sid);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());

        return (WaveSorting) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryById"), params);
    }

    public List<WaveSorting> queryBySids(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            throw new IllegalArgumentException("请输入sids");
        }

        return queryByPickingAndSids(staff, null, sids);
    }

    public List<WaveSorting> queryByWaveIdsAndSids(Staff staff, List<Long> sids, List<Long> waveIds) {
        if (CollectionUtils.isEmpty(sids) && CollectionUtils.isEmpty(waveIds)) {
            throw new IllegalArgumentException("查询参数不能为空! ");
        }

        Map<String, Object> params = Maps.newHashMap();
        params.put("waveIds", waveIds);
        params.put("sids", sids);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryByWaveIdsAndSids"), params);
    }

    public List<WaveSorting> queryByPickingAndSids(Staff staff, Long pickingId, List<Long> sids) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("pickingId", pickingId);
        params.put("sids", sids);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryById"), params);
    }

    public List<WaveSortingDetail> queryDetailsWithWaveId(Staff staff, List<Long> sids) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("sids", sids);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("detailDbNo", staff.getDbInfo().getWaveSortingDetailDbNo());

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsWithWaveId"), params);
    }

    public List<WaveSortingDetail> queryDetailBySids(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            throw new IllegalArgumentException("请输入sids");
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("sids", sids);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("detailDbNo", staff.getDbInfo().getWaveSortingDetailDbNo());
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailBySids"), params);
    }

    @SuppressWarnings("unchecked")
    public List<WaveSorting> queryByPickingId(Staff staff, Long pickingId, Integer status, Page page) {
        return queryByPickingIds(staff, Lists.newArrayList(pickingId), status, page);
    }

    public List<WaveSorting> queryByPickingIds(Staff staff, List<Long> pickingIds, Integer status, Page page) {
        return queryByPickingIds(staff, pickingIds, status, page, false);
    }

    public List<WaveSorting> queryByPickingIds(Staff staff, List<Long> pickingIds, Integer status, Page page, boolean complete) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("pickingIds", pickingIds);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("status", status);
        params.put("complete", complete);
        if (null != page) {
            params.put("startRow", page.getStartRow());
            params.put("pageSize", page.getPageSize());
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryByPickingId"), params);
    }

    public List<WaveSorting> querySortingByPickIdsForOpen(Staff staff, List<Long> pickingIds, Page page, Integer status) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("pickingIds", pickingIds);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("tradeDbNo", staff.getDbInfo().getTradeDbNo());
        params.put("status", status);
        if (null != page) {
            params.put("startRow", page.getStartRow());
            params.put("pageSize", page.getPageSize());
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("querySortingByPickIdsForOpen"), params);
    }

    public Long queryCountByPickingIds(Staff staff, List<Long> pickingIds, Integer status) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("pickingIds", pickingIds);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("status", status);
        return (Long) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryCountByPickingId"), params);
    }

    public Long queryCountByPickingId(Staff staff, Long pickingId, Integer status) {
        return queryCountByPickingIds(staff, Lists.newArrayList(pickingId), status);
    }

    public Long queryCountByPickingId(Staff staff, Long pickingId, boolean complete) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("pickingId", pickingId);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("complete", complete);
        return (Long) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryCountByWaveId"), params);
    }

    public boolean hasNotPrintedWaveSorting(Staff staff, Long pickingId) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        WaveSorting waveSorting = (WaveSorting) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryNotPrintedWaveSorting"), params);
        return waveSorting != null;
    }

    @SuppressWarnings("unchecked")
    public Map<Long, Long> querySidPositionNoMapByPickingId(Staff staff, Long pickingId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("pickingId", pickingId);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());

        return getSqlMapClientTemplate(staff).queryForMap(buildStatementName("querySidPositionNoMapByPickingId"), params, "sid", "position_no");
    }

    public WaveSorting queryWaveSortingBySid(Staff staff, Long pickingId, Long sid) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("sid", sid);

        return (WaveSorting) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryWaveSortingBySid"), params);
    }


    public void batchMergeInsertDetails(Staff staff, List<WaveSortingDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        for (WaveSortingDetail detail : details) {
            initDetail(staff, detail);
        }
        Field[] declaredFields = WaveSortingDetail.class.getDeclaredFields();
        Map<String, List<WaveSortingDetail>> mergeSqlMaps = details.stream().collect(Collectors.groupingBy(r -> WmsUtils.createSqlMergeKey(declaredFields, r)));
        for (Map.Entry<String, List<WaveSortingDetail>> entry : mergeSqlMaps.entrySet()) {
            Map<String, Object> map = Maps.newHashMap();
            List<WaveSortingDetail> values = entry.getValue();
            List<List<WaveSortingDetail>> partitions = Lists.partition(values, MERGE_SQL_SIZE);
            for (List<WaveSortingDetail> partition : partitions) {
                map.put("column", partition.get(0));
                map.put("values", partition);
                getSqlMapClientTemplate(staff).insert("WaveSorting.insertMergeDetails", map);
            }
        }
    }

    public void batchInsertDetails(Staff staff, List<WaveSortingDetail> details) {
        batchMergeInsertDetails(staff, details);
    }

    public void batchUpdateDetails(Staff staff, List<WaveSortingDetail> details) {
        for (WaveSortingDetail detail : details) {
            initDetail(staff, detail);
        }
        batchUpdate(buildStatementName("updateDetail"), details);
    }

    /**
     * 批量更新
     */
    public void batchMergeUpdateDetails(Staff staff, List<WaveSortingDetail> details) {
        List<List<WaveSortingDetail>> partitions = Lists.partition(details, MERGE_SQL_SIZE);
        for (List<WaveSortingDetail> partition : partitions) {
            Map<String, Object> map = Maps.newHashMap();
            map.put("list", partition);
            map.put("companyId", staff.getCompanyId());
            map.put("dbKey", staff.getDbNo());
            map.put("dbNo", staff.getDbInfo().getWaveSortingDetailDbNo());
            getSqlMapClientTemplate(staff).update(buildStatementName("batchMergeUpdateDetails"), map);
        }
    }

    public int batchUpdateOuterId(Staff staff, List<WaveSortingDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return 0;
        }
        for (WaveSortingDetail detail : details) {
            initDetail(staff, detail);
        }
        return batchUpdate(buildStatementName("updateDetailOuterId"), details);
    }

    public void updateOuterIdBySids(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sids", sids);

        getSqlMapClientTemplate(staff).update(buildStatementName("updateOuterIdBySids"), params);
    }

    /**
     * 清空位置号
     */
    public void clearPosition(Staff staff, Long pickingId, Long sid) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("sid", sid);
        getSqlMapClientTemplate(staff).update(buildStatementName("clearPosition"), params);
    }

    public boolean isWaveClearPosition(Staff staff, Long pickingId) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        Long id = (Long) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryOneWaveClearPosition"), params);
        return id != null && id > 0L;
    }

    public void updatePositionNo(Staff staff, WaveSorting waveSorting) {
        init(staff, waveSorting);
        getSqlMapClientTemplate(staff).delete(buildStatementName("updatePositionNo"), waveSorting);
    }

    public void updatePositionNoById(Staff staff, WaveSorting waveSorting) {
        init(staff, waveSorting);
        getSqlMapClientTemplate(staff).delete(buildStatementName("updatePositionNoById"), waveSorting);
    }

    public void updatePositionNo(Staff staff, List<WaveSorting> waveSortings) {
        for (WaveSorting waveSorting : waveSortings) {
            init(staff, waveSorting);
        }
        sortWaveSortingList(waveSortings);
        batchUpdate(buildStatementName("updatePositionNo"), waveSortings);
    }

    public void updateMatchedDetail(Staff staff, WaveSortingDetail detail) {
        initDetail(staff, detail);
        getSqlMapClientTemplate(staff).update(buildStatementName("updateMatchedDetail"), detail);
    }

    public void batchUpdateMatchedDetails(Staff staff, List<WaveSortingDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        for (WaveSortingDetail detail : details) {
            initDetail(staff, detail);
        }
        batchUpdate(buildStatementName("updateMatchedDetail"), details);
    }

    public List<WaveSortingDetail> queryDetailByOuterId(Staff staff, Long sortingId, String outerId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDetailDbNo());
        params.put("sortingId", sortingId);
        params.put("outerId", outerId);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailByOuterId"), params);
    }

    public List<WaveSortingDetail> queryOrderIdsByOuterIds(Staff staff, Long sortingId, List<String> outerIds) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDetailDbNo());
        params.put("sortingId", sortingId);
        params.put("outerIds", outerIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryOrderIdsByOuterIds"), params);
    }

    @SuppressWarnings("unchecked")
    public List<WaveSortingDetail> queryDetailsBySortingId(Staff staff, Long sortingId) {
        return queryDetailsBySortingId(staff, sortingId, false);
    }

    public List<WaveSortingDetail> queryDetailsBySortingId(Staff staff, Long sortingId, boolean containSuitSelf) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sortingId", sortingId);
        params.put("containSuitSelf", containSuitSelf);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsBySortingId"), params);
    }

    @SuppressWarnings("unchecked")
    public List<WaveSortingDetail> queryDetailsBySortingIds(Staff staff, List<Long> sortingIds) {
        return queryDetailsBySortingIds(staff, sortingIds, false);
    }

    public List<WaveSortingDetail> queryDetailsBySortingIdsForOpen(Staff staff, List<Long> sortingIds, Integer status) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sortingIds", sortingIds);
        params.put("status", status);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsBySortingIdsForOpen"), params);
    }

    public List<WaveSortingDetail> queryDetailsBySortingIdsWithReport(Staff staff, List<Long> sortingIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sortingIds", sortingIds);
        params.put("containSuitSelf", false);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsBySortingIdsWithReport"), params);
    }

    @SuppressWarnings("unchecked")
    public List<WaveSortingDetail> queryDetailsBySortingIds(Staff staff, List<Long> sortingIds, boolean containSuitSelf) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sortingIds", sortingIds);
        params.put("containSuitSelf", containSuitSelf);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsBySortingId"), params);
    }

    public List<WaveSortingDetail> queryDetailsByOrderIds(Staff staff, List<Long> orderIds) {
        Map<String, Object> params = buildBaseParams(staff);
        if (CollectionUtils.isEmpty(orderIds)) {
            return Lists.newArrayList();
        }
        params.put("orderIds", orderIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsByOrderIds"), params);
    }

    public List<Long> queryNotExistDetailOrderIds(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return Lists.newArrayList();
        }
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sids", sids);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryNotExistDetailOrderIds"), params);
    }

    public Integer queryNotMatchedDetailsCount(Staff staff, Long sortingId) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sortingId", sortingId);
        return (Integer) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryNotMatchedDetailsCount"), params);
    }

    public List<WaveSortingDetail> queryDetailsBySidsAndPickingIds(Staff staff, List<Long> pickingIds, List<Long> sids, Integer suitType) {
        return queryDetailsBySidsAndPickingIds(staff, pickingIds, sids, suitType, null, null);
    }

    public List<WaveSortingDetail> queryDetailsBySidsAndPickingIds(Staff staff, List<Long> pickingIds, List<Long> sids, Integer suitType, Integer printStatus, Boolean needUnConsign) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);
        params.put("sids", sids);
        params.put("printStatus", printStatus);
        if (suitType != null) {
            params.put("suitType", suitType);
        }
        if (needUnConsign != null) {
            params.put("needUnConsign", needUnConsign);
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsBySidsAndPickingIds"), params);
    }

    public List<WaveSortingDetail> queryDetailsBySidsAndPickingIds(Staff staff, List<Long> pickingIds, List<Long> sids) {
        return queryDetailsBySidsAndPickingIds(staff, pickingIds, sids, null);
    }

    @SuppressWarnings("unchecked")
    public List<WaveSortingDetail> queryDetailsBySid(Staff staff, Long pickingId, Long sid, boolean containsSuitSelf) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("sid", sid);
        params.put("containsSuitSelf", containsSuitSelf);

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsBySid"), params);
    }

    public List<WaveSortingDetail> queryDetailsBySidsAndItem(Staff staff, Long pickingId, Long sysItemId, Long sysSkuId, List<Long> sids, boolean includeDelete) {
        return queryDetailsBySidsAndItem(staff,pickingId,sysItemId,sysSkuId,sids,includeDelete, null);
    }


    public List<WaveSortingDetail> queryDetailsBySidsAndItem(Staff staff, Long pickingId, Long sysItemId, Long sysSkuId, List<Long> sids, boolean includeDelete,List<Integer> giftPickAndChecks) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("sysItemId", sysItemId);
        params.put("sysSkuId", sysSkuId);
        params.put("sids", sids);
        params.put("includeDelete", includeDelete);
        if(CollectionUtils.isEmpty(giftPickAndChecks)){
            params.put("giftPickAndCheck",Lists.newArrayList(1,2));
        }else {
            params.put("giftPickAndCheck",giftPickAndChecks);
        }

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsBySidsAndItem"), params);
    }

    public List<WaveSortingDetail> queryDetailsByOrderIds(Staff staff, Long pickingId, List<Long> orderIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("orderIds", orderIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsBySidsAndItem"), params);
    }

    public List<WaveSortingDetail> queryDetailsByPickingIdsAndItem(Staff staff, List<Long> pickingIds, Long sysItemId, Long sysSkuId, List<Long> sids) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);
        params.put("sysItemId", sysItemId);
        params.put("sysSkuId", sysSkuId);
        params.put("sids", sids);

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsBySidsAndItem"), params);
    }

    public List<WaveSortingDetail> queryWaveSortingDetailsByParam(Staff staff, WaveSortingParam param) {
        Map<String, Object> params = buildBaseParams(staff);
        if (param.getPickingId() != null) {
            params.put("pickingId", param.getPickingId());
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryWaveSortingDetailsByParam"), params);
    }

    public List<WaveSortingDetail> queryDetailsByItemIds(Staff staff, Long pickingId, List<Long> sysItemIds, List<Long> sysSkuIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("sysItemIds", sysItemIds);
        params.put("sysSkuIds", sysSkuIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsByItemIds"), params);
    }

    public List<WaveSortingDetail> queryPickerNamesBySid(Staff staff, List<Long> waveIds, List<Long> sids) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("waveIds", waveIds);
        params.put("sids", sids);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryPickerNamesBySid"), params);
    }

    public List<WaveSortingDetail> queryMatchedDetails(Staff staff, Long pickingId, List<Long> orderIds, Boolean seed, Integer printStatus) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("orderIds", orderIds);
        params.put("seed", seed);
        params.put("printStatus", printStatus);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryOneMatchedDetailByOuterId"), params);
    }

    public WaveSortingDetail queryOneMatchedDetailByOuterId(Staff staff, WaveSortingParam sortingParam, WaveConfig waveConfig) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", sortingParam.getPickingId());
        params.put("outerId", sortingParam.getOuterId());
        //如果有唯一码，不传位置号，可能是拆单，条码还是原来的位置号，实际上拆出的订单可能已经变了
        if (sortingParam.getOrderId() != null) {
            params.put("orderId", sortingParam.getOrderId());
        } else {
            params.put("positionNo", sortingParam.getPositionNo());
        }
        if(CompanyUtils.openMultiShipper(staff)&&sortingParam.getShipperSpeSysItemId()!=null){
            params.put("shipperSpeSysItemId",sortingParam.getShipperSpeSysItemId());
        }
        params.put("seed", sortingParam.getSeed());
        params.put("printStatus", sortingParam.getPrintStatus());
        params.put("lockSid", sortingParam.getLockSid());
        params.put("postPrintMatchTradeSort", Optional.ofNullable(waveConfig).map(WaveConfig::getPostPrintMatchTradeSort).orElse(1));
        params.put("matchTradePickedFirst", sortingParam.isMatchTradePickedFirst());
        return (WaveSortingDetail) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryOneMatchedDetailByOuterId"), params);
    }

    public WaveSortingDetail queryOneMatchedDetailByOuterId(Staff staff, Long pickingId, String outerId, Long positionNo, Long orderId, Boolean seed, Integer printStatus,  WaveConfig waveConfig) {
        WaveSortingParam sortingParam = new WaveSortingParam();
        sortingParam.setPickingId(pickingId);
        sortingParam.setOuterId(outerId);
        sortingParam.setPositionNo(positionNo);
        sortingParam.setOrderId(orderId);
        sortingParam.setSeed(seed);
        sortingParam.setPrintStatus(printStatus);
        return queryOneMatchedDetailByOuterId(staff, sortingParam, waveConfig);
    }

    public Map<Long, String> queryRemoveWaveSidSysStatusByOuterId(Staff staff, Long pickingId, String outerId, Long positionNo, Long orderId) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("outerId", outerId);
        params.put("positionNo", positionNo);
        params.put("orderId", orderId);
        return getSqlMapClientTemplate(staff).queryForMap(buildStatementName("queryRemoveWaveSidSysStatusByOuterId"), params, "sid", "sys_status");
    }

    public List<WaveSortingDetail> queryMultiMatchedByOuterCodes(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildParams(staff, param);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryMultiMatchedByOuterCodes"), params);
    }

    public List<WaveSortingDetail> queryMultiMatchedBySuitOrderId(Staff staff, WavePickingParam param, WaveUniqueCode waveUniqueCode) {
        Map<String, Object> params = buildParams(staff, param);
        if (waveUniqueCode != null) {
            params.put("orderId", waveUniqueCode.getOrderId());
            params.remove("positionNo");
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryMultiMatchedBySuitOrderId"), params);
    }

    public WaveSorting queryOneUnPrintedByOuterCodes(Staff staff, WavePickingParam param, boolean combCheck) {
        Map<String, Object> params = buildParams(staff, param);
        if (combCheck) {
            params.put("combCheck", true);
        }
        if (param.isPostPrintCheck()) {
            logSql(staff, buildStatementName("queryOneUnPrintedByOuterCodes"), params);
        }
        if(CompanyUtils.openMultiShipper(staff)&&CollectionUtils.isNotEmpty(param.getUserIds())){
            params.put("userIds",param.getUserIds());
        }
        return (WaveSorting) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryOneUnPrintedByOuterCodes"), params);
    }

    public WaveSorting querySingleWaveUnPrintedByOuterId(Staff staff, WavePickingParam param) {
        param.setPrintNum(1);
        List<WaveSorting> sortings = this.queryMultiWaveUnPrintedByOuterId(staff, param);
        if (CollectionUtils.isEmpty(sortings)) {
            return null;
        }
        return sortings.iterator().next();
    }

    public List<WaveSorting> queryMultiWaveUnPrintedByOuterId(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildParams(staff, param);
        params.put("outerId", param.getOuterId());
        params.put("pickingIds", param.getPickingIds());
        if (StringUtils.isNotEmpty(param.getTemplateStr())) {
            params.put("templateList", ArrayUtils.toStringList(param.getTemplateStr()));
        }
        if (StringUtils.isNotEmpty(param.getNotInTemplateStr())) {
            params.put("notInTemplateList", ArrayUtils.toStringList(param.getNotInTemplateStr()));
        }
        if (StringUtils.isNotEmpty(param.getAppointUserIds())) {
            params.put("appointUserIdList", ArrayUtils.toStringList(param.getAppointUserIds()));
        }
        if (StringUtils.isNotEmpty(param.getExcludeUserIds())) {
            params.put("excludeUserIdList", ArrayUtils.toStringList(param.getExcludeUserIds()));
        }

        if (StringUtils.isNotEmpty(param.getLogisticsCompanyIdStr())) {
            params.put("logisticsCompanyIdList", ArrayUtils.toLongList(param.getLogisticsCompanyIdStr()));
        }
        if (StringUtils.isNotEmpty(param.getNotInLogisticsCompanyIdStr())) {
            params.put("notInLogisticsCompanyIdList", ArrayUtils.toLongList(param.getNotInLogisticsCompanyIdStr()));
        }
        params.put("printNum", param.getPrintNum());
        // mysql 自定义排序函数field()
        // -------order by  field（str，str1，str2）desc
        // 设置顺序,结果集是先按照str2,str1的顺序倒序来,接下来是非包含的字段根据id排列
        // 按照店铺发货优先级，需要将店铺列表参数反转
        params.put("postPrintMatchPriorityShopList", ArrayUtils.reverse(param.getPostPrintMatchPriorityShopList()));
        params.put("openPostPrintUploadConsignFirst", param.isOpenPostPrintUploadConsignFirst());
        params.put("postPrintMatchTradeSort", param.getPostPrintMatchTradeSort());
        params.put("filterBindUniqueCodeTrade", param.isFilterBindUniqueCodeTrade());

        if (CollectionUtils.isNotEmpty(param.getPickingIds())) {
            List<Long> sortingIds = querySortingIdsByOuterId(staff, param);
            if (CollectionUtils.isEmpty(sortingIds)) {
                return Lists.newArrayList();
            }
            params.put("sortingIds", sortingIds);
        }
        if (param.isPostPrintCheck()) {
            logSql(staff, buildStatementName("queryMultiWaveUnPrintedByOuterId"), params);
        }
        if(CompanyUtils.openMultiShipper(staff)&&CollectionUtils.isNotEmpty(param.getUserIds())){
            params.put("userIds",param.getUserIds());
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryMultiWaveUnPrintedByOuterId"), params);
    }

    private List<Long> querySortingIdsByOuterId(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildParams(staff, param);
        params.put("outerId", param.getOuterId());
        params.put("pickingIds", param.getPickingIds());

        return param.isNegative2PostPrintOpt()
                ? getSqlMapClientTemplate(staff).queryForList(buildStatementName("querySortingIdsByOuterIdNegative2PostPrintOpt"), params)
                : getSqlMapClientTemplate(staff).queryForList(buildStatementName("querySortingIdsByOuterId"), params);
    }

    public List<WaveSorting> queryBatchUnPrintedByOuterId(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildParams(staff, param);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryBatchUnPrintedByOuterId"), params);
    }

    public Integer queryBatchUnPrintPickedNum(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildParams(staff, param);
        return (Integer) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryBatchUnPrintPickedNum"), params);
    }

    public Integer queryBatchUnPrintPickedNumAPlusN(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildParams(staff, param);
        return (Integer) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryBatchUnPrintPickedNumAPlusN"), params);
    }

    public Integer queryNotMatchedOverCount(Staff staff, Long pickingId) {
        return queryNotMatchedOverCount(staff, Lists.newArrayList(pickingId));
    }

    public Integer queryNotMatchedOverCount(Staff staff, List<Long> pickingIds) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("detailDbNo", staff.getDbInfo().getWaveSortingDetailDbNo());
        params.put("pickingIds", pickingIds);

        return (Integer) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryNotMatchedOverCount"), params);
    }

    public int queryNotPrintOverCount(Staff staff, Long pickingId) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);

        return (Integer) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryNotPrintOverCount"), params);
    }

    public WavePicking queryTradeAndItemNumByPickingId(Staff staff, List<Long> pickingIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);

        return (WavePicking) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryTradeAndItemNumByPickingId"), params);
    }

    public WavePicking queryTradeAndItemNumByPickingId(Staff staff, Long pickingId) {
        return queryTradeAndItemNumByPickingId(staff, Lists.newArrayList(pickingId));
    }

    public List<WaveSorting> queryItemAndMatchedNumGroupForSid(Staff staff, Long pickingId) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryItemAndMatchedNumGroupForSid"), params);
    }

    public List<WaveSorting> querySeedProportionBySortingId(Staff staff, List<Long> sortingIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sortingIds", sortingIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("querySeedProportionBySortingId"), params);
    }

    public void resetMatchedSortingsByPickingId(Staff staff, Long pickingId, Integer printStatus, boolean needClearPosition) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("needClearPosition", needClearPosition);
        params.put("printStatus", printStatus);
        getSqlMapClientTemplate(staff).update(buildStatementName("resetMatchedSortingsByPickingId"), params);
    }

    public void resetMatchedSortingsBySid(Staff staff, Long sid) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sid", sid);
        getSqlMapClientTemplate(staff).update(buildStatementName("resetMatchedSortingsBySid"), params);
    }

    public void pickAllByPickingId(Staff staff, Long pickingId) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);

        getSqlMapClientTemplate(staff).update(buildStatementName("pickAllByPickingId"), params);
    }

    public void deleteDetailsBySids(Staff staff, List<Long> sids, Long pickingId) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sids", sids);
        params.put("pickingId", pickingId);
        getSqlMapClientTemplate(staff).delete(buildStatementName("deleteDetailsBySids"), params);
    }

    public void deleteSortingBySid(Staff staff, Long sid, Long pickingId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("sid", sid);
        params.put("pickingId", pickingId);
        getSqlMapClientTemplate(staff).delete(buildStatementName("deleteSortingBySid"), params);
    }

    public List<WaveSortingDetail> queryItemMapByPickingId(Staff staff, WavePickingParam param, Page page) {
        Map<String, Object> params = buildItemParams(staff, param);
        if (page != null) {
            params.put("startRow", page.getStartRow());
            params.put("pageSize", page.getPageSize());
        }
        params.put("joinItem", 1);
        params.put("usePostStatus", param.isUsePostStatus());
        List<WaveSortingDetail> itemMapList = getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryItemMapByPickingId"), params);
        if (itemMapList.size() == 1) {
            if (itemMapList.get(0).getSysItemId() == null) {
                return Collections.emptyList();
            }
        }
        return itemMapList;
    }

    /**
     * 不用唯一码时查询需要打印的商品信息
     */
    public List<WaveSortingDetail> queryNotUniqueItems4Print(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildItemParams(staff, param);
        params.put("joinItem", 1);
        List<WaveSortingDetail> itemMapList = getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryNotUniqueItems4Print"), params);
        if (itemMapList.size() == 1) {
            if (itemMapList.get(0).getSysItemId() == null) {
                return Collections.emptyList();
            }
        }
        return itemMapList;
    }

    public List<WaveItemPosition> queryItemPositionMapByPickingId(Staff staff, WavePickingParam param, Page page) {
        Map<String, Object> params = buildItemParams(staff, param);
        if (page != null) {
            params.put("startRow", page.getStartRow());
            params.put("pageSize", page.getPageSize());
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryItemPositionMapByPickingId"), params);
    }

    /**
     * 根据商品查询拣选波次的位置号等信息
     *
     * @param staff
     * @return
     */
    public Long queryItemPositionCountByPickingId(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildItemParams(staff, param);
        return (Long) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryPositionAndOrderIdByItemCount"), params);
    }

    public Long queryItemMapByPickingIdCount(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildItemParams(staff, param);
        params.put("joinItem", 1);
        return (Long) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryItemMapByPickingIdCount"), params);
    }

    private Map<String, Object> buildItemParams(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildParams(staff, param);
        params.put("pickingIds", param.getPickingIds());
        params.put("suitType", param.getSuiteType());
        params.put("matchedStatus", param.getMatchedStatus());
        params.put("printStatus", param.getPrintStatus());
        params.put("stockStatus", ObjectUtils.defaultIfNull(param.getStockStatus(), 0));
        params.put("uniqueCodeStockStatus", param.getUniqueCodeStockStatus());
        params.put("searchKeyword", param.getSearchKeyword());
        params.put("dmjItemDbNo", staff.getDbInfo().getDmjItemDbNo());
        params.put("sysItemId", param.getSysItemId());
        params.put("sysSkuId", param.getSysSkuId());
        params.put("openWaveUniqueCode", Objects.equals(param.getOpenWaveUniqueCode(), 1));
        params.put("sids", param.getSids());
        params.put("searchOuterId", param.getSearchOuterId());
        if (param.getNeedUnConsign() != null) {
            params.put("needUnConsign", param.getNeedUnConsign());
        }
        return params;
    }

    /**
     * 根据商品查询拣选波次的位置号等信息
     */
    public List<WaveItemPosition> queryPositionAndOrderId(Staff staff, Long pickingId, List<Long> sids, Integer printStatus, Integer matchedStatus, Integer stockStatus, Integer suitType) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("matchedStatus", matchedStatus);
        params.put("stockStatus", stockStatus == null ? 0 : stockStatus);
        params.put("printStatus", printStatus);
        params.put("suitType", suitType);
        params.put("sids", sids);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryPositionAndOrderIdByItem"), params);
    }

    /**
     * 根据商品查询拣选波次的位置号等信息
     *
     * @param num 限制数量，在编码打印的时候用
     */
    public List<WaveItemPosition> queryPositionAndOrderIdByItem(Staff staff, Long pickingId, Long sysItemId, Long sysSkuId, Integer num, Integer printStatus, Integer matchedStatus, Integer stockStatus, Page page) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("sysItemId", sysItemId);
        params.put("sysSkuId", sysSkuId);
        params.put("num", num);
        params.put("matchedStatus", matchedStatus);
        params.put("stockStatus", stockStatus == null ? 0 : stockStatus);
        params.put("printStatus", printStatus);
        if (page != null) {
            params.put("startRow", page.getStartRow());
            params.put("pageSize", page.getPageSize());
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryPositionAndOrderIdByItem"), params);
    }

    public List<Long> queryAfterSendOrderIds(Staff staff, List<Long> pickingIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryAfterSendOrderIds"), params);
    }

    public int deleteDetailByIds(Staff staff, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        ids.sort(Comparator.comparingLong(Long::longValue));
        int count = 0;
        Map<String, Object> params = buildBaseParams(staff);
        for (List<Long> subIds : Lists.partition(ids, 200)) {
            params.put("ids", subIds);
            count += getSqlMapClientTemplate(staff).delete(buildStatementName("deleteDetailByIds"), params);
        }
        return count;
    }

    public void batchDeleteDetails(Staff staff, List<WaveSortingDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        List<Long> ids = Lists.newArrayListWithCapacity(details.size());
        for (WaveSortingDetail detail : details) {
            ids.add(detail.getId());
        }
        deleteDetailByIds(staff, ids);
    }

    public int fixSortingDetails(Staff staff, Long sortingId) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sortingId", sortingId);

        return getSqlMapClientTemplate(staff).update(buildStatementName("fixSortingDetails"), params);
    }

    /**
     * 拣选状态过滤
     *
     * @param staff
     * @param pickingId
     * @param pickedStatus
     * @param page
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<WaveSorting> queryUnSeedSortingsByPickingIdPickStatus(Staff staff, Long pickingId, Integer pickedStatus, Integer printStatus, Page page, Integer sortType) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("printStatus", printStatus);
        params.put("sortType", sortType);
        Optional.ofNullable(pickedStatus).filter(s -> s.equals(2)).ifPresent(status -> params.put("pickedStatus", pickedStatus));
        if (page != null) {
            params.put("startRow", page.getStartRow());
            params.put("pageSize", page.getPageSize());
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryUnSeedSortingsByPickingIdPickStatus"), params);
    }

    /**
     * 播种状态过滤
     *
     * @param staff
     * @param pickingId
     * @param sids
     * @param matchedStatus
     * @param printStatus   0 && null :  未打印    1:已打印    -1:不限制打印状态
     * @param page
     * @return
     */
    public List<WaveSorting> queryUnSeedSortingsByPickingId(Staff staff, WaveSortingSeedParam seedParam) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", seedParam.getPickingIds());
        params.put("matchedStatus", seedParam.getMatchedStatus());
        params.put("printStatus", seedParam.getPrintStatus());
        params.put("sids", seedParam.getSids());
        params.put("sortType", seedParam.getSortType());
        Page page = seedParam.getPage();
        if (page != null) {
            params.put("startRow", page.getStartRow());
            params.put("pageSize", page.getPageSize());
        }
        if (StringUtils.isNotEmpty(seedParam.getTemplateStr())) {
            params.put("templateList", ArrayUtils.toStringList(seedParam.getTemplateStr()));
        }
        if (StringUtils.isNotEmpty(seedParam.getNotInTemplateStr())) {
            params.put("notInTemplateList", ArrayUtils.toStringList(seedParam.getNotInTemplateStr()));
        }
        if (StringUtils.isNotEmpty(seedParam.getLogisticsCompanyIdStr())) {
            params.put("logisticsCompanyIdList", ArrayUtils.toLongList(seedParam.getLogisticsCompanyIdStr()));
        }
        if (StringUtils.isNotEmpty(seedParam.getNotInLogisticsCompanyIdStr())) {
            params.put("notInLogisticsCompanyIdList", ArrayUtils.toLongList(seedParam.getNotInLogisticsCompanyIdStr()));
        }
        if (seedParam.getSuitType() != null) {
            params.put("suitType", seedParam.getSuitType());
        }
        if (seedParam.getPackTradePrinted() != null) {
            params.put("packTradePrinted", seedParam.getPackTradePrinted());
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryUnSeedSortingsByPickingId"), params);
    }

    public Long queryUnSeedSortingsByPickingIdCount(Staff staff, Long pickingId, Integer printStatus) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("printStatus", printStatus);
        return (Long) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryUnSeedSortingsByPickingIdCount"), params);
    }

    public void fixTotalNum(Staff staff) {
        Map<String, Object> param = buildBaseParams(staff);
        getSqlMapClientTemplate(staff).update(buildStatementName("fixTotalNum"), param);
    }

    public Map<Long, Long> queryPickingIdItemKindCountMap(Staff staff, List<Long> pickingIds) {
        return queryPickingIdItemKindCountMap(staff, pickingIds, null);
    }

    public Map<Long, Long> queryPickingIdItemKindCountMap(Staff staff, List<Long> pickingIds, List<Long> ignoreSids) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);
        params.put("ignoreSids", ignoreSids);
        return getSqlMapClientTemplate(staff).queryForMap(buildStatementName("queryPickingIdItemKindCountMap"), params, "picking_id", "item_kind_count");
    }

    public Map<Long, Long> queryPickingIdTradesCountMap(Staff staff, List<Long> pickingIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);

        return getSqlMapClientTemplate(staff).queryForMap(buildStatementName("queryPickingIdTradesCountMap"), params, "picking_id", "trades_count");
    }

    public Map<Long, BigDecimal> queryPickingIdItemCountMap(Staff staff, List<Long> pickingIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);
        return getSqlMapClientTemplate(staff).queryForMap(buildStatementName("queryPickingIdItemCountMap"), params, "picking_id", "item_count");
    }

    public List<WaveSorting> queryByPickingIds(Staff staff, List<Long> pickingIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryByPickingIds"), params);
    }


    public List<WaveSorting> queryNeedWavePrintByPickingIds(Staff staff, List<Long> pickingIds, Integer printStatus, WaveConfig waveConfig) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);
        params.put("printStatus", printStatus);
        params.put("postPrintMatchTradeSort", Optional.ofNullable(waveConfig).map(WaveConfig::getPostPrintMatchTradeSort).orElse(1));
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryNeedWavePrintByPickingIds"), params);
    }

    public List<WaveSorting> queryNeedWavePrintByPickingIdsAndUserIds(Staff staff, List<Long> pickingIds, Integer printStatus, WaveConfig waveConfig,List<Long>userIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);
        params.put("printStatus", printStatus);
        if(CollectionUtils.isNotEmpty(userIds)){
            params.put("userIds",userIds);
        }
        params.put("postPrintMatchTradeSort", Optional.ofNullable(waveConfig).map(WaveConfig::getPostPrintMatchTradeSort).orElse(1));
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryNeedWavePrintByPickingIds"), params);
    }

    public Long queryMaxUsedPositionNoReleasedByPickingIds(Staff staff, List<Long> pickingIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);
        return (Long) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryMaxUsedPositionNoReleasedByPickingIds"), params);
    }

    public List<WaveSorting> statWaveItemNumGroupForPickingSid(Staff staff, List<Long> pickingIds, Boolean giftCanCheck) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);
        params.put("giftCanCheck", BooleanUtils.isTrue(giftCanCheck));
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("statWaveItemNumGroupForPickingSid"), params);
    }

    public List<WaveSorting> statWaveItemNumGroupForSid(Staff staff, List<Long> sids) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sids", sids);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("statWaveItemNumGroupForSid"), params);
    }

    public List<WaveSortingDetail> statWaveItemDetailGroupForSid(Staff staff, List<Long> sids) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sids", sids);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("statWaveItemDetailGroupForSid"), params);
    }

    public List<WaveSorting> statWaveSeedLogGroupForSid(Staff staff, List<Long> sids, List<Long> waveIds) {
        Assert.isTrue(CollectionUtils.isNotEmpty(sids)
                || CollectionUtils.isNotEmpty(waveIds), "参数不能为空！");
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sids", sids);
        params.put("waveIds", waveIds);
        params.put("seedLogDbNo", staff.getDbInfo().getWaveSortingDetailDbNo());
        List<WaveSorting> sortings = getSqlMapClientTemplate(staff).queryForList(buildStatementName("statWaveSeedLogGroupForSid"), params);
        if (CollectionUtils.isNotEmpty(sortings)) {
            return sortings.stream().filter(sorting -> sorting.getSid() != null).collect(Collectors.toList());
        }
        return sortings;
    }

    public List<Long> queryWaveIdsNotInSeedRecord(Staff staff, Date startTime, Date endTime) {
        if (startTime == null && endTime == null) {
            return Lists.newArrayList();
        }
        Map<String, Object> params = buildBaseParams(staff);
        params.put("seedLogDbNo", staff.getDbInfo().getWaveSortingDetailDbNo());
        params.put("scanRecordDbNo", staff.getDbInfo().getWaveSortingDetailDbNo());
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryWaveIdsNotInSeedRecord"), params);
    }

    private Map<String, Object> buildBaseParams(Staff staff) {
        Map<String, Object> params = Maps.newHashMap();
        DbInfo dbInfo = staff.getDbInfo();
        params.put("companyId", staff.getCompanyId());
        params.put("waveDbNo", dbInfo.getWaveDbNo());
        params.put("pickingDbNo", dbInfo.getWavePickingDbNo());
        params.put("waveTradeDbNo", dbInfo.getWaveSortingDbNo());
        params.put("dbNo", dbInfo.getWaveSortingDbNo());
        params.put("detailDbNo", dbInfo.getWaveSortingDetailDbNo());
        params.put("sortingDbNo", dbInfo.getWaveSortingDbNo());
        params.put("orderDbNo", dbInfo.getOrderDbNo());
        params.put("tradeDbNo", dbInfo.getTradeDbNo());
        params.put("uniqueCodeDbNo", staff.getDbInfo().getOrderDbNo());
        params.put("stockOrderRecordDbNo", dbInfo.getStockOrderRecordDbNo());
        params.put("allocateGoodsRecordDbNo", dbInfo.getAllocateGoodsRecordDbNo());
        params.put("itemDbNo", dbInfo.getItemDbNo());
        params.put("skuDbNo", dbInfo.getSkuDbNo());
        return params;
    }

    private Map<String, Object> buildParams(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", param.getPickingId());
        params.put("positionNo", param.getPositionNo());
        params.put("sid", param.getSid());
        params.put("printNum", param.getPrintNum());
        params.put("outerId", param.getOuterId());
        params.put("orderId", param.getOrderId());
        params.put("printStatus", param.getPrintStatus());
        if (DataUtils.checkLongNotEmpty(param.getWaveId()) && Objects.equals(CommonConstants.ENABLE_STATUS_NORMARL, param.getOrderBySort())) {
            // 按照 位置号倒序排
            params.put("positionNoBackSort", 1);
        }
        params.put("postPrintGiftNotCheck", param.isPostPrintGiftNotCheck());
        // 是否是-2盲扫
        params.put("negativeTwoBlindScan", param.isNegativeTwoBlindScan());
        if (StringUtils.isNotEmpty(param.getTemplateStr())) {
            params.put("templateList", ArrayUtils.toStringList(param.getTemplateStr()));
        }
        if (StringUtils.isNotEmpty(param.getNotInTemplateStr())) {
            params.put("notInTemplateList", ArrayUtils.toStringList(param.getNotInTemplateStr()));
        }

        if (StringUtils.isNotEmpty(param.getAppointUserIds())) {
            params.put("appointUserIdList", ArrayUtils.toStringList(param.getAppointUserIds()));
        }
        if (StringUtils.isNotEmpty(param.getExcludeUserIds())) {
            params.put("excludeUserIdList", ArrayUtils.toStringList(param.getExcludeUserIds()));
        }

        if (StringUtils.isNotEmpty(param.getLogisticsCompanyIdStr())) {
            params.put("logisticsCompanyIdList", ArrayUtils.toLongList(param.getLogisticsCompanyIdStr()));
        }
        if (StringUtils.isNotEmpty(param.getNotInLogisticsCompanyIdStr())) {
            params.put("notInLogisticsCompanyIdList", ArrayUtils.toLongList(param.getNotInLogisticsCompanyIdStr()));
        }
        List<WaveSortingDetail> details = param.getDetails();
        if (CollectionUtils.isNotEmpty(details)) {
            if (param.getBindUniqueCode() == null) {
                params.put("details", details);
                params.put("num", details.size());
                int totalNum = 0;
                for (WaveSortingDetail detail : details) {
                    totalNum += detail.getItemNum();
                }
                params.put("totalNum", totalNum);
            } else {
                Map<String, List<WaveSortingDetail>> detailMap = details.stream().collect(Collectors.groupingBy(WaveSortingDetail::getOuterId));
                int totalNum = 0;
                List<WaveSortingDetail> detailsCopy = Lists.newArrayList();
                for (Map.Entry<String, List<WaveSortingDetail>> entry : detailMap.entrySet()) {
                    List<WaveSortingDetail> value = entry.getValue();
                    WaveSortingDetail detailCopy = new WaveSortingDetail();
                    BeanUtils.copyProperties(value.get(0), detailCopy);
                    // 根据outerId聚合，针对唯一码绑定
                    int detailTotalNum = 0;
                    for (WaveSortingDetail detail : value) {
                        totalNum += detail.getItemNum();
                        detailTotalNum += detail.getItemNum();
                    }
                    detailCopy.setItemNum(detailTotalNum);
                    detailsCopy.add(detailCopy);
                }
                params.put("totalNum", totalNum);
                params.put("details", detailsCopy);
                params.put("num", detailsCopy.size());
            }
        }
        params.put("postPrintMatchTradeSort", param.getPostPrintMatchTradeSort());
        params.put("noExcept", param.getNoExcept());
        params.put("needQueryPrint", param.getNeedQueryPrint());
        params.put("suitMode", param.getSuitMode());
        params.put("matchTradePickedFirst", param.isMatchTradePickedFirst());
        params.put("openPostPrintUploadConsignFirst", param.isOpenPostPrintUploadConsignFirst());
        params.put("postItemLockTrade", param.isPostItemLockTrade());
        if(CompanyUtils.openMultiShipper(staff)&&param.getShipperSpeSysItemId()!=null){
            params.put("shipperSpeSysItemId",param.getShipperSpeSysItemId());
        }
        if (StringUtils.isNotEmpty(param.getFixedItemOuterId())) {
            params.put("fixedItemOuterId", param.getFixedItemOuterId());
            params.put("aPlusAModel", Objects.equals(param.getFixedItemOuterId(), param.getOuterId()));
        }
        return params;
    }

    public List<WaveSortingDetail> queryUnSeedDetailsByPickingId(Staff staff, Long pickingId) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("matchedStatus", 0);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryUnSeedDetailsByPickingId"), params);
    }

    public List<Long> queryNeedFixSortingIds(Staff staff, List<Long> pickingIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingIds", pickingIds);

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryNeedFixSortingIds"), params);
    }

    public List<WaveSortingDetail> queryGroupItemSortingDetails(Staff staff, WaveSortingParam param) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("warehouseId", param.getWarehouseId());
        params.put("sysItemIds", param.getSysItemIds());
        params.put("sysSkuIds", param.getSysSkuIds());
        params.put("waveStatus", param.getWaveStatus());
        params.put("pickingType", param.getPickingType());
        params.put("combineItemSingle", param.getCombineItemSingle());
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryGroupItemSortingDetails"), params);
    }

    public List<WaveSorting> querySortingsByUniqueCodes(Staff staff, List<String> uniqueCodes) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("uniqueCodes", uniqueCodes);

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("querySortingsByUniqueCodes"), params);
    }

    public Integer queryBatchPickedNum(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildParams(staff, param);
        params.put("needQueryPrint", true);
        return (Integer) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryBatchPickedNum"), params);
    }

    public Integer queryWavePickedNumByOuterId(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", param.getPickingId());
        params.put("outerId", param.getOuterId());
        return (Integer) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryWavePickedNumByOuterId"), params);
    }

    public List<WaveSortingDetail> queryDetailsByWaveId(Staff staff, List<Long> waveIds, List<Long> pickingIds, List<Integer> pickAndCheckList) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("waveIds", waveIds);
        params.put("pickingIds", pickingIds);
        params.put("pickAndCheckList", pickAndCheckList);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("detailDbNo", staff.getDbInfo().getWaveSortingDetailDbNo());

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsByWaveId"), params);
    }

    public CrossBorderWavePrintInfo queryItemPositionByWaveId(Staff staff, Long waveId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("waveId", waveId);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        return (CrossBorderWavePrintInfo)getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryCrossBorderItemPositionByWaveId"), params);
    }

    /**
     * 根据pickingId 和 sids 查询 WaveSorting
     */
    public List<WaveSorting> queryWaveSortingBySids(Staff staff, Long pickingId, List<Long> pickingIds, List<Long> sids) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("pickingIds", pickingIds);
        params.put("sids", sids);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryWaveSortingBySids"), params);
    }

    /**
     * 根据 sortingId 获取 sortingDetail
     */
    public List<WaveSortingDetail> queryDetailBySortingId(Staff staff, List<Long> sortingIds) {
        if(CollectionUtils.isEmpty(sortingIds)){
            return new ArrayList<>();
        }
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sortingIds", sortingIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailBySortingId"), params);
    }

    public List<WaveSorting> queryAllStatusByPickingIds(Staff staff, List<Long> pickingIds) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("pickingIds", pickingIds);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryAllStatusByPickingId"), params);
    }

    public List<WaveSortingDetail> queryDetailsAllStatusBySortingId(Staff staff, List<Long> sortingIds) {
        Map<String, Object> params = buildBaseParams(staff);
        params.put("sortingIds", sortingIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailsAllStatusBySortingId"), params);
    }

    /**
     * 根据pickingId 和 positionNos 查询
     */
    public List<WaveSorting> queryByPositionNos(Staff staff, Long pickingId, List<Integer> positionNos) {
        if (CollectionUtils.isEmpty(positionNos)) {
            return new ArrayList<>();
        }
        Map<String, Object> params = buildBaseParams(staff);
        params.put("pickingId", pickingId);
        params.put("positionNos", positionNos);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryByPositionNos"), params);
    }

    public List<WaveSortingDetail> queryItemPositionByPickingId(Staff staff, WavePickingParam param, Page page) {
        Map<String, Object> params = buildItemParams(staff, param);
        if (page != null) {
            params.put("startRow", page.getStartRow());
            params.put("pageSize", page.getPageSize());
        }
        List<WaveSortingDetail> itemMapList = getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryItemPositionByPickingId"), params);
        if (itemMapList.size() == 1) {
            if (itemMapList.get(0).getSysItemId() == null) {
                return Collections.emptyList();
            }
        }
        return itemMapList;
    }

    public Long queryItemPositionByPickingIdCount(Staff staff, WavePickingParam param) {
        Map<String, Object> params = buildItemParams(staff, param);
        return (Long) getSqlMapClientTemplate(staff).queryForObject(buildStatementName("queryItemPositionByPickingIdCount"), params);
    }

    public List<WaveSortingDetail> queryNotMatchedMergeDetails(Staff staff, WaveSortingDetail detail, boolean excludeSelf) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("companyId", staff.getCompanyId());
        params.put("detailDbNo", staff.getDbInfo().getWaveSortingDetailDbNo());
        params.put("sortingId", detail.getSortingId());
        params.put("outerId", detail.getOuterId());
        if (excludeSelf) {
            params.put("id", detail.getId());
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryNotMatchedMergeDetails"), params);
    }

    public List<WaveSortingDetail> queryDetailByOuterId4ChangeLabel(Staff staff, Long waveId, String outerId, List<Long> notDetailIds) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("waveDbNo", staff.getDbInfo().getWaveDbNo());
        params.put("pickingDbNo", staff.getDbInfo().getWavePickingDbNo());
        params.put("sortingDbNo", staff.getDbInfo().getWaveSortingDbNo());
        params.put("detailDbNo", staff.getDbInfo().getWaveSortingDetailDbNo());

        params.put("companyId", staff.getCompanyId());
        params.put("waveId", waveId);
        params.put("outerId", outerId);
        params.put("notDetailIds", notDetailIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDetailByOuterId4ChangeLabel"), params);
    }


    /**
     * 根据sids和waveIds查waveSorting,可查出enable_status = 0
     */
    public List<WaveSorting> queryBySidsAndWaveIds(Staff staff, List<Long> sids, List<Long> waveIds) {
        if (CollectionUtils.isEmpty(sids)) {
            throw new IllegalArgumentException("请输入sids");
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("sids", sids);
        params.put("waveIds", waveIds);
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getWaveSortingDbNo());

        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryBySidsAndWaveIds"), params);
    }

}

package com.raycloud.dmj.services.trades.support.wave;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.wave.*;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.wave.*;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.enums.CustomPrivilegeType;
import com.raycloud.dmj.domain.enums.Feature;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.enums.WaveTypeEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemSupplierBridge;
import com.raycloud.dmj.domain.item.params.QueryItemDetailParams;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.TradePackParams;
import com.raycloud.dmj.domain.trades.utils.DateUtils;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.trades.utils.WaveUniqueCodeLogUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.model.*;
import com.raycloud.dmj.domain.wave.utils.OrderUniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.UniqueCodeUtils;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wave.vo.UniqueCodeInfoQueryVO;
import com.raycloud.dmj.domain.wms.GoodsSection;
import com.raycloud.dmj.domain.wms.WmsConfig;
import com.raycloud.dmj.domain.wms.WorkingStorageSection;
import com.raycloud.dmj.domain.wms.enums.WmsConfigExtInfoEnum;
import com.raycloud.dmj.express.api.IPrintExpressTemplateService;
import com.raycloud.dmj.express.response.template.UserLogisticsCompanyDTO;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import com.raycloud.dmj.services.basis.ISupplierService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.pt.IUserExpressTemplateService;
import com.raycloud.dmj.services.response.ItemCatIdAndSellerCidsResponse;
import com.raycloud.dmj.services.trades.ITradePostPrintService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.trades.support.wave.business.PostPrintUniqueCodeBusiness;
import com.raycloud.dmj.services.trades.support.wave.proxy.WaveUseTradeServiceProxy;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsKeyUtils;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.source.OperateSourceContext;
import com.raycloud.dmj.web.utils.DateUtil;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import com.raycloud.erp.db.router.jdbc.JdbcTemplateAdapter;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;
import static java.util.stream.Collectors.summingInt;

/**
 * @Author: qingfeng
 * @Description: 商品唯一码服务
 * @Date: 2021-07-05 11:18
 */
@Service
public class ItemUniqueCodeService implements IItemUniqueCodeService {

    private static final Integer UNIVERSAL_CODE_TYPE = 2;

    private Logger logger = Logger.getLogger(this.getClass());
    private static final String WORKING_STORAGE_SECTION = "暂存区";
    @Resource
    private WaveUniqueCodeDao waveUniqueCodeDao;
    @Resource
    private IWarehouseService warehouseService;
    @Resource
    private IItemUniqueCodeGenerateRuleService itemUniqueCodeGenerateRuleService;
    @Resource
    private WaveProgressBusiness waveProgressBusiness;
    @Resource
    private IItemServiceDubbo itemServiceDubbo;
    @Resource
    private ISupplierService supplierService;
    @Resource
    private IWmsService wmsService;
    @Resource
    private WaveHelpBusiness waveHelpBusiness;
    @Resource
    private IOrderUniqueCodeService orderUniqueCodeService;
    @Resource
    private IEventCenter eventCenter;
    @Resource(name = "tbTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    JdbcTemplateAdapter jdbcTemplateAdapter;
    @Resource
    private WaveUseTradeServiceProxy waveUseTradeServiceProxy;
    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;
    @Resource
    private WaveTradeDao waveTradeDao;
    @Resource
    private WaveDao waveDao;
    @Resource
    private IIndexDubboService indexDubboService;
    @Resource
    private ITradePostPrintService tradePostPrintService;
    @Resource
    private IUserExpressTemplateService userExpressTemplateService;
    @Resource
    private IWaveItemServiceWrapper waveItemServiceWrapper;
    @Resource
    private UniqueCodeRelationDao uniqueCodeRelationDao;
    @Resource
    private TbOrderDAO tbOrderDAO;
    @Resource
    private ItemUniqueCodeGenerateBusiness itemUniqueCodeGenerateBusiness;
    @Resource
    private IAfterSaleUniqueCodeService afterSaleUniqueCodeService;
    @Resource
    private PostPrintUniqueCodeBusiness postPrintUniqueCodeBusiness;
    @Resource
    private FeatureService featureService;
    @Resource
    private IPrintExpressTemplateService printExpressTemplateService;
    @Resource
    private IUniqueCodeBaseService uniqueCodeBaseService;
    @Resource
    private UniqueCodeImportBusiness uniqueCodeImportBusiness;
    @Resource
    private UniqueCodeHelpBusiness uniqueCodeHelpBusiness;
    @Resource
    private ItemUniqueCodeErrorMsgExportBusiness itemUniqueCodeErrorMsgExportBusiness;
    @Resource
    private UniqueCodeExtendDao uniqueCodeExtendDao;
    @Resource
    private IOrderUniqueCodeTagService uniqueCodeTagService;
    @Resource
    private LogisticsWaveRelationBusiness logisticsWaveRelationBusiness;

    private void fillSupplierAuthority(Staff staff, ItemUniqueCodeQueryParams params) {
        if (staff.isDefaultStaff()) {
            return;
        }
        List<Long> supplierIds = indexDubboService.querySettingListByStaff(staff, CustomPrivilegeType.HAS_SUPPLIER);
        boolean noSupplier = indexDubboService.checkCustomPrivilegeEnable(staff, CustomPrivilegeType.HAS_NO_SUPPLIER);
        if (noSupplier) {
            supplierIds.add(0L);
        }
        if (!CollectionUtils.isEmpty(supplierIds)) {
            if (!CollectionUtils.isEmpty(params.getSupplierIds())) {
                // 前端查询条件和权限做交集
                supplierIds = Lists.newArrayList(org.apache.commons.collections.CollectionUtils.intersection(supplierIds, params.getSupplierIds()));
            }
            params.setSupplierIds(supplierIds);
        } else {
            params.setSupplierIds(Lists.newArrayList(-1L));
        }
    }

    @Override
    public PageListBase<WaveUniqueCode> queryList(Staff staff, ItemUniqueCodeQueryParams params) {
        PageListBase<WaveUniqueCode> pageList = new PageList<>();
        // 给一个默认分页，防止OOM
        if (params.getPage() == null) {
            params.setPage(new Page());
        }
        fillSupplierAuthority(staff, params);
        Long count = waveUniqueCodeDao.countItemUniqueCode(staff, params);
        pageList.setTotal(count);
        pageList.setPage(params.getPage());
        if (count > params.getPage().getStartRow()) {
            List<WaveUniqueCode> codes = queryItemUniqueCodeByCondition(staff, params);
            if (params.isFillTagNamesFlag()) {
                uniqueCodeTagService.fillTagNames(staff, codes);
            }
            pageList.setList(codes);
        } else {
            pageList.setList(new ArrayList<>());
        }
        return pageList;
    }

    @Override
    public Long countItemUniqueCode(Staff staff, ItemUniqueCodeQueryParams params) {
        return waveUniqueCodeDao.countItemUniqueCode(staff, params);
    }

    private void initQueryVal(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<Warehouse> warehouses = warehouseService.queryAll(staff, CommonConstants.ENABLE_STATUS_NORMARL);
        Map<Long, String> warehouseMap = warehouses.stream().collect(Collectors.toMap(Warehouse::getId, Warehouse::getName));
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodes(codes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList()));
        List<UniqueCodeRelation> uniqueCodeRelationList = uniqueCodeRelationDao.queryByUniqueCondition(staff, params);
        Map<String, String> unicode2BusinessCodeList = Maps.newHashMap();
        Map<Long, String> otherWarehousingOrderList = Maps.newHashMap();
        Map<Long, String> otherOutboundOrderList = Maps.newHashMap();
        Map<String, String> otherWarehousingOrderStrList = Maps.newHashMap();
        Map<String, String> otherOutboundOrderStrList = Maps.newHashMap();
        Map<String, String> saleReturnOrderCodeMaps = Maps.newHashMap();
        Map<String, String> warehouseEntryCodeMaps = Maps.newHashMap();
        Map<String, Long> warehouseEntryIdMaps = Maps.newHashMap();
        Map<String, String> purchaseOrderCodeMaps = Maps.newHashMap();
        Map<String, String> boxCodeMaps = Maps.newHashMap();
        Map<String, String> purchaseReturnCodeMaps = Maps.newHashMap();
        Map<String, String> takenOrderCodeMaps = Maps.newHashMap();
        Map<String, Integer> takenOrderStatusMaps = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(uniqueCodeRelationList)) {
            for (UniqueCodeRelation code : uniqueCodeRelationList) {
                if (code.filterBusinessType(UniqueCodeRelation.BusinessType.AFTER_SALE_ORDER)) {
                    unicode2BusinessCodeList.put(code.getUniqueCode(), code.getBusinessCode());
                } else if (code.filterBusinessType(UniqueCodeRelation.BusinessType.OTHER_WAREHOUSING_ORDER)) {
                    otherWarehousingOrderList.put(code.getUniqueCodeId(), code.getBusinessCode());
                    otherWarehousingOrderStrList.put(code.getUniqueCode(), code.getBusinessCode());
                } else if (code.filterBusinessType(UniqueCodeRelation.BusinessType.OTHER_OUTBOUND_ORDER)) {
                    otherOutboundOrderList.put(code.getUniqueCodeId(), code.getBusinessCode());
                    otherOutboundOrderStrList.put(code.getUniqueCode(), code.getBusinessCode());
                } else if (code.filterBusinessType(UniqueCodeRelation.BusinessType.SALES_RETURN_RECEIPT_ORDER)) {
                    saleReturnOrderCodeMaps.put(code.getUniqueCode(), code.getBusinessCode());
                } else if (code.filterBusinessType(UniqueCodeRelation.BusinessType.WAREHOUSE_ENTRY_ORDER)) {
                    warehouseEntryCodeMaps.put(code.getUniqueCode(), code.getBusinessCode());
                    warehouseEntryIdMaps.put(code.getUniqueCode(), code.getBusinessId());
                } else if (code.filterBusinessType(UniqueCodeRelation.BusinessType.PURCHASE_ORDER)) {
                    purchaseOrderCodeMaps.put(code.getUniqueCode(), code.getBusinessCode());
                } else if (code.filterBusinessType(UniqueCodeRelation.BusinessType.BOX_CODE)) {
                    boxCodeMaps.put(code.getUniqueCode(), code.getBusinessCode());
                }else if (code.filterBusinessType(UniqueCodeRelation.BusinessType.PURCHASE_RETURN_ORDER)) {
                    purchaseReturnCodeMaps.put(code.getUniqueCode(), code.getBusinessCode());
                } else if (code.filterBusinessType(UniqueCodeRelation.BusinessType.TAKEN_ORDER)) {
                    takenOrderCodeMaps.put(code.getUniqueCode(), code.getBusinessCode());
                    takenOrderStatusMaps.put(code.getUniqueCode(), code.getStatus());
                }
            }
        }
        for (WaveUniqueCode code : codes) {
            code.setNum(1);
            code.setSerialNumberStr(code.getDateNo() + "-" + code.getSerialNumber());
            code.setWarehouseName(warehouseMap.get(code.getWarehouseId()));
            if (!DataUtils.checkLongNotEmpty(code.getSid())) {
                code.setSid(null);
            }
            if (Objects.equals(TradeTimeUtils.INIT_DATE_STR, DateUtil.format(code.getPrintTime(), "yyyy-MM-dd HH:mm:ss"))) {
                code.setPrintTime(null);
            }
            if (Objects.equals(TradeTimeUtils.INIT_DATE_STR, DateUtil.format(code.getProductionDate(), "yyyy-MM-dd HH:mm:ss"))) {
                code.setProductionDate(null);
            }
            code.setAfterSaleOrderCode(unicode2BusinessCodeList.get(code.getUniqueCode()));
            code.setOtherWarehousingOrderCode(StringUtils.isNotBlank(otherWarehousingOrderList.get(code.getId())) ? otherWarehousingOrderList.get(code.getId()) : otherWarehousingOrderStrList.get(code.getUniqueCode()));
            code.setOtherOutboundOrderCode(StringUtils.isNotBlank(otherOutboundOrderList.get(code.getId())) ? otherOutboundOrderList.get(code.getId()) : otherOutboundOrderStrList.get(code.getUniqueCode()));
            code.setSaleReturnOrderCode(saleReturnOrderCodeMaps.get(code.getUniqueCode()));
            code.setWarehouseEntryCode(warehouseEntryCodeMaps.get(code.getUniqueCode()));
            code.setWarehouseEntryId(warehouseEntryIdMaps.get(code.getUniqueCode()));
            if (StringUtils.isNotBlank(purchaseOrderCodeMaps.get(code.getUniqueCode()))) {
                code.setBusinessCode(purchaseOrderCodeMaps.get(code.getUniqueCode()));
            }
            code.setBoxCode(boxCodeMaps.get(code.getUniqueCode()));
            code.setPurchaseReturnCode(purchaseReturnCodeMaps.get(code.getUniqueCode()));
            code.setTakenOrderCode(takenOrderCodeMaps.get(code.getUniqueCode()));
            code.setTakenOrderStatus(takenOrderStatusMaps.get(code.getUniqueCode()) != null && !Objects.equals(takenOrderStatusMaps.get(code.getUniqueCode()), 20) ? 1 : 0);
        }
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodeByCondition(Staff staff, ItemUniqueCodeQueryParams params) {
        if (params.isAuthorityFilter()) {
            fillSupplierAuthority(staff, params);
        }
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, params);
        if (params.isNeedRelationMsg()) {
            initQueryVal(staff, codes);
        }
        if (params.isNeedCategroyAndCatName()) {
            fillItemCategroyAndCatName(staff, codes);
        }
        return codes;
    }

    private void fillOrderInfo(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        Map<Long, TbOrder> id2OrderMap = tbOrderDAO.queryByIds(staff, codes.stream().map(WaveUniqueCode::getOrderId).filter(Objects::nonNull).distinct().toArray(Long[]::new))
                .stream().collect(Collectors.toMap(TbOrder::getId, Function.identity(), (d1, d2) -> d2));
        for (WaveUniqueCode code : codes) {
            if (code.getOrderId() == null) {
                continue;
            }
            code.setRefundStatus(id2OrderMap.getOrDefault(code.getOrderId(), new TbOrder()).getRefundStatus());
        }

    }

    private void fillItemCategroyAndCatName(Staff staff, List<WaveUniqueCode> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<String> outerIds = new ArrayList<>();
        for (WaveUniqueCode code : codes) {
            outerIds.add(StringUtils.isNotEmpty(code.getSkuOuterId()) ? code.getSkuOuterId() : code.getMainOuterId());
        }
        ItemCatIdAndSellerCidsResponse response = itemServiceDubbo.queryItemCatIdAndSellerCids(staff,outerIds);
        if (response != null && !CollectionUtils.isEmpty(response.getItemList())) {
            List<ItemCatIdAndSellerCidsResponse.SimpleItem> itemList = response.getItemList();
            Map<String, ItemCatIdAndSellerCidsResponse.SimpleItem> map
                    = itemList.stream().collect(Collectors.toMap(ItemCatIdAndSellerCidsResponse.SimpleItem::getOuterId, v -> v, (v1, v2) -> v2));
            for (WaveUniqueCode code : codes) {
                ItemCatIdAndSellerCidsResponse.SimpleItem simpleItem = map.get(StringUtils.isNotEmpty(code.getSkuOuterId()) ? code.getSkuOuterId() : code.getMainOuterId());
                if (null != simpleItem) {
                    code.setItemCatName(simpleItem.getItemCatName());
                    code.setItemCategoryNames(simpleItem.getItemCategoryNames());
                }
            }
        }
    }

    @Override
    @Transactional
    public void saveUniqueCode(Staff staff, WaveUniqueCode code) {
        // 参数判断
        preJudge4Save(staff, code);

        // 查询存在的唯一码
        WaveUniqueCode existCode = queryCode4Save(staff, code.getId());

        // 唯一码修改不能重复
        judgeCode4Save(staff, code, existCode);

        // 商品批次、生产日期判断
        judgeItem4Save(staff, code, existCode);

        // 更新
        update4Save(staff, code, existCode);
    }

    private void judgeCode4Save(Staff staff, WaveUniqueCode code, WaveUniqueCode existCode) {
        if (Objects.equals(code.getUniqueCode(), existCode.getUniqueCode())) {
            return;
        }
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodesByCodes(staff, Lists.newArrayList(code.getUniqueCode()));
        if (!CollectionUtils.isEmpty(codes)) {
            throw new IllegalArgumentException("唯一码已存在！");
        }
        Map<String, Object> itemMap = itemServiceDubbo.queryItemSkuByOuterId(staff, Lists.newArrayList(code.getUniqueCode()), 12, null);
        if (MapUtils.isNotEmpty(itemMap)) {
            throw new IllegalArgumentException("唯一码不能和商家编码相同！");
        }
    }

    private void preJudge4Save(Staff staff, WaveUniqueCode code) {
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("编辑唯一码，成本价 = %s，销售价 = %s，重量 = %s，生产日期 = %s，唯一码 = %s，商品id = %s，规格id = %s，商家编码 = %s，供应商id = %s，供应商名称 = %s，供应商分类 = %s",
                    code.getCostPrice(), code.getSalePrice(), code.getWeight(), code.getProductionDate(),
                    code.getUniqueCode(), code.getSysItemId(), code.getSysSkuId(), code.getOuterId(), code.getSupplierId(),
                    code.getSupplierName(), code.getSupplierCategory())));
        }
        if (!DataUtils.checkLongNotEmpty(code.getId())) {
            throw new IllegalArgumentException("唯一码Id不能为空！");
        }
    }

    private WaveUniqueCode queryCode4Save(Staff staff, Long id) {
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams().setNeedExtendMsg(true);
        params.setUniqueCodeIds(Lists.newArrayList(id));
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, params);
        if (CollectionUtils.isEmpty(codes)) {
            throw new IllegalArgumentException("该唯一码不存在！");
        }
        return codes.get(0);
    }

    private void judgeItem4Save(Staff staff, WaveUniqueCode code, WaveUniqueCode existCode) {
        DmjItem item = null;
        if (DataUtils.checkLongNotEmpty(existCode.getSysSkuId())) {
            item = itemServiceDubbo.querySkuWithSysSkuId(staff, existCode.getSysSkuId());
        } else {
            if (DataUtils.checkLongNotEmpty(existCode.getSysItemId())) {
                item = itemServiceDubbo.queryItemWithSysItemId(staff, existCode.getSysItemId());
            }
        }
        if (item == null) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "商品不存在！"));
            }
            return;
        }
        boolean flag = Objects.equals(existCode.getStatus(), OrderUniqueCodeStatusEnum.IN.getType()) &&
                ((StringUtils.isNotEmpty(code.getItemBatchNo()) && !Objects.equals(existCode.getItemBatchNo(), code.getItemBatchNo()))
                        || (code.getProductionDate() != null && !Objects.equals(existCode.getProductionDate(), code.getProductionDate())));
        if (flag) {
            throw new IllegalArgumentException("该状态唯一码不支持修改生产日期和批次！");
        }
    }

    private void update4Save(Staff staff, WaveUniqueCode code, WaveUniqueCode existCode) {
        WaveUniqueCode update = new WaveUniqueCode();
        update.setId(code.getId());
        update.setCompanyId(staff.getCompanyId());
        update.setCostPrice(code.getCostPrice());
        update.setSellingPrice(code.getSellingPrice());
        update.setWeight(code.getWeight());
        if (code.getProductionDate() == null) {
            update.setProductionDate(TradeTimeUtils.INIT_DATE);
        } else {
            update.setProductionDate(code.getProductionDate());
        }
        update.setItemBatchNo(code.getItemBatchNo());
        // 导入的商品唯一码信息错误，修改
        fillAndJudgeParamsForUpdate(staff, update, code, existCode);
        if (DataUtils.checkIntegerNotEmpty(code.getStatus()) && DataUtils.checkIntegerNotEmpty(existCode.getStatus()) && !Objects.equals(code.getStatus(), existCode.getStatus())) {
            update.setStatus(code.getStatus());
            update.setReceiveStatus(UniqueCodeUtils.getReceiveStatus4UpdateStatus(code.getStatus()));
        }
        waveUniqueCodeDao.updateItemUniqueCodeCustom(staff, update);

        boolean updateExtend = (StringUtils.isNotEmpty(code.getICCID()) && !Objects.equals(existCode.getICCID(), code.getICCID())) ||
                (StringUtils.isNotEmpty(code.getExtendField2()) && !Objects.equals(existCode.getExtendField2(), code.getExtendField2())) ||
                (StringUtils.isNotEmpty(code.getExtendField3()) && !Objects.equals(existCode.getExtendField3(), code.getExtendField3())) ||
                (StringUtils.isNotEmpty(code.getExtendField4()) && !Objects.equals(existCode.getExtendField4(), code.getExtendField4())) ||
                (StringUtils.isNotEmpty(code.getExtendField5()) && !Objects.equals(existCode.getExtendField5(), code.getExtendField5())) ||
                (StringUtils.isNotEmpty(code.getUniqueCode()) && !Objects.equals(existCode.getUniqueCode(), code.getUniqueCode()));
        if (updateExtend) {
            UniqueCodeExtend extendUpdate = new UniqueCodeExtend().setUniqueCodeId(existCode.getId())
                    .setUniqueCode(code.getUniqueCode()).setICCID(code.getICCID()).setExtendField2(code.getExtendField2())
                    .setExtendField3(code.getExtendField3()).setExtendField4(code.getExtendField4()).setExtendField5(code.getExtendField5());
            update.setICCID(code.getICCID());
            update.setExtendField2(code.getExtendField2());
            update.setExtendField3(code.getExtendField3());
            update.setExtendField4(code.getExtendField4());
            update.setExtendField5(code.getExtendField5());
            uniqueCodeExtendDao.batchUpdateByUniqueCodeIds(staff, Lists.newArrayList(extendUpdate));
        }

        // 打日志
        WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto = getWaveUniqueCodeLogDTO(existCode, update);
        if (dto != null) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, Lists.newArrayList(dto), WaveUniqueOpType.UNIQUE_CODE_IMPORT_MODIFY}), null);
        }
    }

    private void fillAndJudgeParamsForUpdate(Staff staff, WaveUniqueCode update, WaveUniqueCode code, WaveUniqueCode existCode) {
        // 打日志用到
        update.setUniqueCode(existCode.getUniqueCode());
        // 更新商品唯一码
        boolean updateUniqueCode = StringUtils.isNotBlank(code.getUniqueCode()) && !Objects.equals(code.getUniqueCode(), existCode.getUniqueCode());
        // 更新商品信息
        boolean updateItem = StringUtils.isNotBlank(code.getOuterId()) && !Objects.equals(code.getOuterId(), existCode.getOuterId());
        // 更新供应商信息
        boolean updateSupplier = DataUtils.checkLongNotEmpty(code.getSupplierId()) && !Objects.equals(code.getSupplierId(), existCode.getSupplierId());
        if (!updateUniqueCode && !updateItem && !updateSupplier) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "不更新唯一码、商品和供应商"));
            }
            return;
        }

        if (!Objects.equals(existCode.getStatus(), OrderUniqueCodeStatusEnum.WAIT_IN.getType())) {
            throw new IllegalArgumentException("唯一码状态不符合！");
        }
        if (DataUtils.checkLongNotEmpty(existCode.getSid()) || DataUtils.checkLongNotEmpty(existCode.getOrderId())) {
            throw new IllegalArgumentException("唯一码已绑定订单！");
        }
        if (DataUtils.checkLongNotEmpty(existCode.getBusinessId())) {
            throw new IllegalArgumentException("唯一码已绑定采购单！");
        }

        if (updateUniqueCode) {
            ItemUniqueCodeQueryParams param = new ItemUniqueCodeQueryParams();
            param.setUniqueCodes(Lists.newArrayList(code.getUniqueCode()));
            Assert.isTrue(!DataUtils.checkLongNotEmpty(waveUniqueCodeDao.countItemUniqueCode(staff, param)), "唯一码重复！");
            update.setUniqueCode(code.getUniqueCode());
        }
        if (updateItem) {
            if (!updateSupplier) {
                Pair<Long, Long> itemId = Pair.of(code.getSysItemId(), DataUtils.getZeroIfDefault(code.getSysSkuId()));
                Map<String, ItemSupplierBridge> bestSupplierMap = waveHelpBusiness.getBestSupplierMap(staff, Lists.newArrayList(itemId));
                ItemSupplierBridge itemSupplierBridge = bestSupplierMap.get(WmsKeyUtils.buildItemKey(code.getSysItemId(), code.getSysSkuId()));
                if (itemSupplierBridge != null) {
                    update.setSupplierId(itemSupplierBridge.getSupplierId());
                    update.setSupplierName(itemSupplierBridge.getSupplierName());
                    update.setSupplierCategory(itemSupplierBridge.getSupplierCategoryName());
                }
            }
            update.setSysItemId(code.getSysItemId());
            update.setSysSkuId(DataUtils.getZeroIfDefault(code.getSysSkuId()));
            update.setOuterId(code.getOuterId());
        }
        if (updateSupplier) {
            Supplier supplier = supplierService.queryById(staff, code.getSupplierId());
            Assert.notNull(supplier, "供应商不存在！");
            update.setSupplierId(supplier.getId());
            update.setSupplierName(supplier.getName());
            update.setSupplierCategory(supplier.getCategoryName());
        }
    }

    private WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO getWaveUniqueCodeLogDTO(WaveUniqueCode existCode, WaveUniqueCode update) {
        if (StringUtils.isBlank(update.getUniqueCode()) && StringUtils.isBlank(update.getOuterId()) && StringUtils.isBlank(update.getSupplierName())) {
            return null;
        }
        WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO dto = new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO();
        dto.setBeforeUniqueCode(existCode.getUniqueCode());
        dto.setBeforeOuterId(existCode.getOuterId());
        dto.setBeforeSupplierName(existCode.getSupplierName());
        dto.setBeforeCostPrice(existCode.getCostPrice());
        dto.setBeforeSellingPrice(existCode.getSellingPrice());
        dto.setBeforeWeight(existCode.getWeight());
        dto.setBeforeProductionDate(existCode.getProductionDate());
        dto.setBeforeItemBatchNo(existCode.getItemBatchNo());
        dto.setOldStatus(existCode.getStatus());
        dto.setBeforeICCID(existCode.getICCID());
        dto.setBeforeExtendField2(existCode.getExtendField2());
        dto.setBeforeExtendField3(existCode.getExtendField3());
        dto.setBeforeExtendField4(existCode.getExtendField4());
        dto.setBeforeExtendField5(existCode.getExtendField5());

        dto.setUniqueCode(update.getUniqueCode());
        dto.setOuterId(update.getOuterId());
        dto.setSupplierName(update.getSupplierName());
        dto.setCostPrice(update.getCostPrice());
        dto.setSellingPrice(update.getSellingPrice());
        dto.setWeight(update.getWeight());
        dto.setProductionDate(update.getProductionDate());
        dto.setItemBatchNo(update.getItemBatchNo());
        dto.setICCID(update.getICCID());
        dto.setExtendField2(update.getExtendField2());
        dto.setExtendField3(update.getExtendField3());
        dto.setExtendField4(update.getExtendField4());
        dto.setExtendField5(update.getExtendField5());
        return dto;
    }

    @Override
    public void generateValidate(Staff staff, ItemUniqueCodeGenerateParams generateParams) {
        Assert.notNull(generateParams, "唯一码生成参数不能为空！");
        Assert.notEmpty(generateParams.getDetails(), "生成的商品不能为空！");
        // 判断仓库
        judgeWarehouse4Generate(staff, generateParams);
        List<ItemUniqueCodeGenerateParams.GenerateDetail> details = generateParams.getDetails();

        Map<String, Object> itemMap = itemServiceDubbo.queryItemSkuByOuterId(staff, details.stream()
                .map(ItemUniqueCodeGenerateParams.GenerateDetail::getOuterId).collect(Collectors.toList()), 123);
        //通用码类型商品编码
        List<String> errorItemList = new ArrayList<>();
        for (ItemUniqueCodeGenerateParams.GenerateDetail detail : details) {
            Assert.isTrue(DataUtils.checkIntegerNotEmpty(detail.getNum()), detail.getOuterId() + "的生成数量应为大于0的整数！");
            Assert.isTrue(DataUtils.checkLongNotEmpty(detail.getSupplierId()), detail.getOuterId() + "的供应商不能为空！");
            Assert.notNull(detail.getSysItemId(), detail.getOuterId() + "的sysItemId不能为空！");
            Assert.notNull(detail.getSysSkuId(), detail.getOuterId() + "的sysSkuId不能为空！");
            DmjItem item = (DmjItem) itemMap.get(detail.getOuterId());
            if (item == null) {
                throw new IllegalArgumentException("商品：" + detail.getOuterId() + "不存在！");
            }
            if (StringUtils.isNotEmpty(item.getType()) && ("1".equals(item.getType()) || "2".equals(item.getType()))) {
                throw new IllegalArgumentException("包含套件商品：" + detail.getOuterId() + "，生成失败！");
            }
            //通用码类型商品不能生成唯一码
            if (UNIVERSAL_CODE_TYPE.equals(item.getUniqueCodeType())) {
                errorItemList.add(item.getOuterId());
                continue;
            }
        }
        if (!CollectionUtils.isEmpty(errorItemList)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("生成商品唯一码-过滤通用码类型商品：errorItemList=%s", errorItemList)));
            }
            throw new IllegalArgumentException(String.format("[%s]商品，通用码管理模式，不允许生成唯一码", errorItemList.stream().distinct().collect(Collectors.toList())));
        }
        ItemUniqueCodeGenerateRule generateRule;
        if (DataUtils.checkLongNotEmpty(generateParams.getRuleId())) {
            generateRule = itemUniqueCodeGenerateRuleService.getById(staff, generateParams.getRuleId());
        } else {
            // 收货单收货打印用默认规则
            generateRule = itemUniqueCodeGenerateRuleService.queryDefault(staff);
        }
        Assert.notNull(generateRule, "未查询到编码生成规则！");
        generateParams.setGenerateRule(generateRule);
    }

    private void judgeWarehouse4Generate(Staff staff, ItemUniqueCodeGenerateParams generateParams) {
        if (Objects.equals(generateParams.getSource(), 10) || (Objects.equals(generateParams.getSource(), 3) && !Objects.equals(generateParams.getSubSource(), 1))) {
            if (WmsUtils.isOpenWms(staff)) {
                for (ItemUniqueCodeGenerateParams.GenerateDetail detail : generateParams.getDetails()) {
                    if (!DataUtils.checkLongNotEmpty(detail.getWarehouseId())) {
                        throw new IllegalArgumentException(String.format("商品【%s】请选择仓库！", detail.getOuterId()));
                    }
                }
            }
            return;
        }
        if (WmsUtils.isOpenWms(staff) && !DataUtils.checkLongNotEmpty(generateParams.getWarehouseId())) {
            throw new IllegalArgumentException("请选择仓库！");
        }
    }

    @Override
    public ItemUniqueCodeGenerateParams buildImportGenerateParams(Staff staff, String[][] datas) {
        ProgressData progressCompany = waveProgressBusiness.getOrCreate(staff, ProgressEnum.PROGRESS_GENERATE_ITEM_UNIQUE_CODE_COMPANY);
        ProgressData progress = waveProgressBusiness.getOrCreate(staff, ProgressEnum.PROGRESS_GENERATE_ITEM_UNIQUE_CODE);
        List<String> errorMsg = new ArrayList<>();
        try {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "商品唯一码导入：开始校验Excel数据！"));
            }
            Assert.notNull(datas, "excel数据不能为空！");
            ItemUniqueCodeGenerateParams generateParams = convertAndCheckImportData(staff, datas, errorMsg);
            uniqueCodeBaseService.fillDefaultRule(staff, generateParams);
            if (!CollectionUtils.isEmpty(errorMsg)) {
                Map<Integer, List<String>> errorMsgMap = uniqueCodeImportBusiness.fillErrorMsgMap(errorMsg);
                errorMsg = uniqueCodeImportBusiness.sortErrorMsgs(errorMsgMap);
                progress.setErrorMsg(errorMsg);
                progress.setProgress(2);
                progress.setCountAll(DataUtils.getZeroIfDefaultI(datas.length - 1));
                progress.setErrorNum(Long.parseLong(String.valueOf(errorMsgMap.size())));

                // 错误信息导出
                itemUniqueCodeErrorMsgExportBusiness.doExport4Import(staff, datas, errorMsg, progress);

                waveProgressBusiness.updateProgress(staff, ProgressEnum.PROGRESS_GENERATE_ITEM_UNIQUE_CODE, progress);

                progressCompany.setProgress(2);
                waveProgressBusiness.setProgress(staff, ProgressEnum.PROGRESS_GENERATE_ITEM_UNIQUE_CODE_COMPANY, progressCompany);
            }
            return generateParams;
        } catch (Exception e) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("商品唯一码导入：e:【%s】", e.getMessage())));
            }
            errorMsg.add("商品唯一码导入失败！");
            progress.setErrorMsg(errorMsg);
            progress.setProgress(2);
            progress.setCountAll(DataUtils.getZeroIfDefaultI(datas.length - 1));
            progress.setErrorNum(Long.parseLong(String.valueOf(DataUtils.getZeroIfDefaultI(datas.length - 1))));
            waveProgressBusiness.updateProgress(staff, ProgressEnum.PROGRESS_GENERATE_ITEM_UNIQUE_CODE, progress);

            progressCompany.setProgress(2);
            waveProgressBusiness.setProgress(staff, ProgressEnum.PROGRESS_GENERATE_ITEM_UNIQUE_CODE_COMPANY, progressCompany);
        }
        return null;
    }

    @Override
    @Transactional
    public void batchReject(Staff staff, List<WaveUniqueCode> uniqueCodes, List<UniqueCodeRelation> relations, UniqueCodeResult result, UniqueCodeGenericParams params) {
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return;
        }

        // 回退唯一码
        List<WaveUniqueCode> uniqueUpdates = Lists.newArrayList();
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> updateCodeLogs = Lists.newArrayList();
        Map<String, Integer> stockPositionMap = null;
        if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.PURCHASE_RETURN_ORDER.getType())) {
            stockPositionMap = params.getItemParams().stream().collect(Collectors.toMap(UniqueCodeGenericParams.ItemGenericParams::getUniqueCode, UniqueCodeGenericParams.ItemGenericParams::getStockPosition, (a, b) -> a));
        }
        for (WaveUniqueCode uniqueCode : uniqueCodes) {
            WaveUniqueCode uniqueUpdate = getUpdateCode4BatchReject(params, uniqueCode, stockPositionMap);
            if (uniqueUpdate == null) {
                continue;
            }
            uniqueUpdates.add(uniqueUpdate);

            updateCodeLogs.add(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO()
                    .setUniqueCode(uniqueCode.getUniqueCode())
                    .setOldStatus(uniqueCode.getStatus()));
        }
        if (CollectionUtils.isEmpty(uniqueUpdates)) {
            UniqueCodeResult.setFailInfo("500", "无可回退唯一码数据！", result);
            return;
        }
        waveUniqueCodeDao.batchUpdate(staff, uniqueUpdates);

        uniqueCodeBaseService.releaseBindRelation(staff, relations.stream().map(UniqueCodeRelation::getId).collect(Collectors.toList()));

        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, updateCodeLogs, WaveUniqueOpType.ITEM_BATCH_CANCEL, null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(params.getLogTemplate())}), null);
    }

    private WaveUniqueCode getUpdateCode4BatchReject(UniqueCodeGenericParams params, WaveUniqueCode uniqueCode, Map<String, Integer> stockPositionMap) {
        Integer updateStatus = getUpdateStatus4BatchReject(params, uniqueCode);
        if (updateStatus == null) {
            return null;
        }
        WaveUniqueCode uniqueUpdate = new WaveUniqueCode();
        uniqueUpdate.setId(uniqueCode.getId());
        uniqueUpdate.setStatus(updateStatus);
        if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.WAREHOUSE_ENTRY_ORDER.getType())) {
            uniqueUpdate.setReceiveStatus(CommonConstants.JUDGE_NO);
            uniqueUpdate.setReceiveTime(UniqueCodeUtils.getDefaultDate());
            uniqueUpdate.setStockPosition(UniqueCodeStockPositionEnum.DEFAULT.getType());
            uniqueUpdate.setGoodsSectionId(0L);
            uniqueUpdate.setGoodsSectionCode("");
            uniqueUpdate.setBusinessId(0L);
            uniqueUpdate.setBusinessCode("");
        } else if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.PURCHASE_RETURN_ORDER.getType())) {
            uniqueUpdate.setStockPosition(stockPositionMap.get(uniqueCode.getUniqueCode()));
        } else {
            return null;
        }
        return uniqueUpdate;
    }

    private Integer getUpdateStatus4BatchReject(UniqueCodeGenericParams params, WaveUniqueCode uniqueCode) {
        Integer updateStatus = null;
        if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.WAREHOUSE_ENTRY_ORDER.getType())) {
            if (Objects.equals(uniqueCode.getStatus(), OrderUniqueCodeStatusEnum.IN.getType())) {
                updateStatus = OrderUniqueCodeStatusEnum.WAIT_IN.getType();
            } else if (Objects.equals(uniqueCode.getStatus(), OrderUniqueCodeStatusEnum.RECIVED.getType())) {
                updateStatus = OrderUniqueCodeStatusEnum.WAIT_RECIVE.getType();
            }
        } else if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.PURCHASE_RETURN_ORDER.getType())) {
            updateStatus = OrderUniqueCodeStatusEnum.IN.getType();
        }
        return updateStatus;
    }

    private ItemUniqueCodeGenerateParams convertAndCheckImportData(Staff staff, String[][] datas, List<String> errorMsg) {
        return convertAndCheckImportData(staff, datas, errorMsg, 0);
    }

    public ItemUniqueCodeGenerateParams convertAndCheckImportData(Staff staff, String[][] datas, List<String> errorMsg, Integer convertType) {
        Map<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> baseDetails = uniqueCodeImportBusiness.importBaseCheck(staff, datas, errorMsg, convertType);
        return buildItemUniqueCodeGenerateParams(staff, errorMsg, baseDetails, convertType);
    }

    private ItemUniqueCodeGenerateParams buildItemUniqueCodeGenerateParams(Staff staff, List<String> errorMsg, Map<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> baseDetails) {
        return buildItemUniqueCodeGenerateParams(staff, errorMsg, baseDetails, 0);
    }

    private ItemUniqueCodeGenerateParams buildItemUniqueCodeGenerateParams(Staff staff, List<String> errorMsg, Map<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> baseDetails, Integer convertType) {
        if (MapUtils.isEmpty(baseDetails)) {
            return null;
        }
        uniqueCodeImportBusiness.importInfoCheck(staff, baseDetails, errorMsg, convertType);
        // 设置最优供应商
        fillBestSupplier(staff, baseDetails, errorMsg);
        if (!CollectionUtils.isEmpty(errorMsg)) {
            return null;
        }
        ItemUniqueCodeGenerateParams generateParams = new ItemUniqueCodeGenerateParams();
        generateParams.setDetails(new ArrayList<>(baseDetails.values()));
        return generateParams;
    }

    private void fillBestSupplier(Staff staff, Map<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> baseDetails, List<String> errorMsg) {
        if (MapUtils.isEmpty(baseDetails)) {
            return;
        }
        List<Pair<Long, Long>> itemIds = baseDetails.entrySet().stream().map(en -> Pair.of(en.getValue().getSysItemId(), en.getValue().getSysSkuId())).collect(Collectors.toList());
        Map<String, ItemSupplierBridge> bestSupplierMap = waveHelpBusiness.getBestSupplierMap(staff, itemIds);
        for (Map.Entry<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> entry : baseDetails.entrySet()) {
            if (DataUtils.checkLongNotEmpty(entry.getValue().getSupplierId())) {
                continue;
            }
            // 更新唯一码，修改了商家编码且没填供应商采取最优供应商
            // 导入唯一码一定会有商家编码
            if (entry.getValue().getOuterId() == null) {
                continue;
            }
            ItemUniqueCodeGenerateParams.GenerateDetail value = entry.getValue();
            ItemSupplierBridge itemSupplierBridge = bestSupplierMap.get(WmsKeyUtils.buildItemKey(value.getSysItemId(), value.getSysSkuId()));
            if (itemSupplierBridge == null) {
                errorMsg.add("第" + entry.getKey() + "行，未绑定供应商！");
                continue;
            }
            value.setSupplierId(itemSupplierBridge.getSupplierId());
            value.setSupplierName(itemSupplierBridge.getSupplierName());
        }
    }

    public List<WaveUniqueCode> queryByUniqueCodes(Staff staff, List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Collections.emptyList();
        }
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodes);
        return queryItemUniqueCodeByCondition(staff, params);
    }

    private Map<String, Supplier> queryExistSupplierName(Staff staff, List<String> supplierNames) {
        if (CollectionUtils.isEmpty(supplierNames)) {
            return Collections.emptyMap();
        }
        List<Supplier> suppliers = supplierService.listByNames(staff, supplierNames, CommonConstants.ENABLE_STATUS_NORMARL);
        if (CollectionUtils.isEmpty(suppliers)) {
            return Collections.emptyMap();
        }
        return suppliers.stream().collect(Collectors.toMap(Supplier::getName, a -> a, (a, b) -> a));
    }

    private List<String> queryExistUniqueCodes(Staff staff, List<String> uniqueCodes, Integer convertType) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Collections.emptyList();
        }
        List<WaveUniqueCode> codes = queryByUniqueCodes(staff, uniqueCodes);
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        return codes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
    }

    private Map<String, Warehouse> queryExistWarehouse(Staff staff) {
        List<Warehouse> warehouses = warehouseService.queryAll(staff, CommonConstants.ENABLE_STATUS_NORMARL);
        return warehouses.stream().collect(Collectors.toMap(Warehouse::getName, a -> a, (a, b) -> a));
    }

    @Override
    @Transactional
    public int batchCancel(Staff staff, List<Long> ids) {
        return batchCancel(staff, ids, false);
    }

    @Override
    @Transactional
    public int batchCancel(Staff staff, List<Long> ids, boolean fromAfterSale) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setUniqueCodeIds(ids);
        List<WaveUniqueCode> codes = queryItemUniqueCodeByCondition(staff, params);
        if (CollectionUtils.isEmpty(codes)) {
            return 0;
        }
        List<WaveUniqueCode> orderCodes = codes.stream().filter(code -> Objects.equals(1, code.getType())).collect(Collectors.toList());
        List<WaveUniqueCode> itemCodes = codes.stream().filter(code -> Objects.equals(2, code.getType())).collect(Collectors.toList());
        int sucNum = 0;
        // 订单唯一码取消
        if (!CollectionUtils.isEmpty(orderCodes)) {
            Integer orderSucNum = 0;
            try {
                orderSucNum = orderUniqueCodeService.batchOffShelfOrCancelNotThrowException(staff, orderCodes.stream().map(WaveUniqueCode::getId).collect(Collectors.toList()), 1);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "取消订单唯一码失败！"), e);
            }
            sucNum += orderSucNum;
        }

        // 商品唯一码取消
        List<WaveUniqueCode> filters = fromAfterSale ? (itemCodes.stream().filter(code -> Objects.equals(OrderUniqueCodeStatusEnum.WAIT_IN.getType(), code.getStatus()) || Objects.equals(OrderUniqueCodeStatusEnum.IN.getType(), code.getStatus())).collect(Collectors.toList()))
                : (itemCodes.stream().filter(code -> Objects.equals(OrderUniqueCodeStatusEnum.WAIT_IN.getType(), code.getStatus())).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(filters)) {
            return sucNum;
        }
        List<WaveUniqueCode> updates = new ArrayList<>();
        for (WaveUniqueCode filter : filters) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setStatus(OrderUniqueCodeStatusEnum.DELETE.getType());
            update.setId(filter.getId());
            updates.add(update);
        }
        sucNum += filters.size();
        waveUniqueCodeDao.batchUpdate(staff, updates);
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(filters), WaveUniqueOpType.ITEM_BATCH_CANCEL}), null);
        return sucNum;
    }

    @Override
    @Transactional
    public int batchDelete(Staff staff, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setUniqueCodeIds(ids);
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, params);
        if (CollectionUtils.isEmpty(codes)) {
            return 0;
        }
        List<WaveUniqueCode> updates = Lists.newArrayList();
        List<UniqueCodeExtend> extendUpdates = Lists.newArrayList();
        for (WaveUniqueCode code : codes) {
            if (DataUtils.checkLongNotEmpty(code.getSid()) || DataUtils.checkLongNotEmpty(code.getOrderId())) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("唯一码【%s】唯一码已绑定订单", code.getUniqueCode())));
                }
                continue;
            }
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setUniqueCode(code.getUniqueCode());
            update.setEnableStatus(0);
            updates.add(update);
            extendUpdates.add(new UniqueCodeExtend().setUniqueCodeId(code.getId()).setEnableStatus(0));
        }
        if (CollectionUtils.isEmpty(updates)) {
            return 0;
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);
        uniqueCodeExtendDao.batchUpdateByUniqueCodeIds(staff, extendUpdates, true);
        // 记录日志
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(updates), WaveUniqueOpType.DELETE_UNIQUE_CODE}), null);
        return updates.size();
    }

    @Override
    @Transactional
    public void examine(Staff staff, Long sid, List<String> uniqueCodes) {
        examine(staff, sid, uniqueCodes, true);
    }

    @Override
    @Transactional
    public void examine(Staff staff, Long sid, List<String> uniqueCodes, boolean needInspection) {
        examine(staff, sid, uniqueCodes, needInspection, false);
    }

    @Override
    @Transactional
    public void examine(Staff staff, Long sid, List<String> uniqueCodes, boolean needInspection, boolean supportSellerSend) {
        examine(staff, sid, uniqueCodes, needInspection, supportSellerSend, null, null);
    }

    /**
     * 验货
     *
     * @param staff
     * @param sid
     * @param uniqueCodes
     * @param needInspection
     * @param supportSellerSend
     * @param receiveType       收货类型 2:包装验货
     */
    @Override
    @Transactional
    public void examine(Staff staff, Long sid, List<String> uniqueCodes, boolean needInspection, boolean supportSellerSend, Integer receiveType, Integer openPackAutoReceiveGoods) {
        Trade trade = tradeSearchService.queryBySid(staff, true, sid);
        Assert.notNull(trade, "未查询到订单！");
        if (!(Objects.equals(Trade.SYS_STATUS_FINISHED_AUDIT, trade.getSysStatus())
                && !BooleanUtils.toBoolean(trade.getIsPackage())) && !supportSellerSend) {
            throw new IllegalArgumentException("该订单非待包装状态！");
        }
        if (supportSellerSend && ((!Objects.equals(Trade.SYS_STATUS_FINISHED_AUDIT, trade.getSysStatus())
                && !Objects.equals(Trade.SYS_STATUS_SELLER_SEND_GOODS, trade.getSysStatus())
                && !Objects.equals(Trade.SYS_STATUS_FINISHED, trade.getSysStatus()))
                || BooleanUtils.toBoolean(trade.getIsPackage()))) {
            throw new IllegalArgumentException("该订单非待包装或已发货未包装状态！");
        }
        OrderUniqueCodeUtils.validateTrade(staff, trade);
        tradePostPrintService.handleExceptionTrades(staff, Lists.newArrayList(trade));
        // 过滤虚拟商品不过滤发货
        List<Order> orders = Optional.ofNullable(WaveUtils.getOrdersForWave(new WaveUtils.GetOrdersForWaveParams.Builder().orders(TradeUtils.getOrders4Trade(trade)).splitSuit(true).containVirtual(true).skipJudgeAfterSendGoods(true).build())).orElse(Lists.newArrayList()).stream().filter(o -> !o.isVirtual()).collect(Collectors.toList());
        uniqueCodes = uniqueCodes.stream().distinct().collect(Collectors.toList());

        //包装验货的后置绑定逻辑不需要校验唯一码数量和验货
        if (needInspection) {
            long sum = orders.stream().filter(o -> !OrderUtils.notCheck(o)).collect(Collectors.summarizingInt(Order::getNum)).getSum();
            Assert.isTrue(uniqueCodes.size() == sum, "唯一码数量与商品数量不等，验货失败！");
            //对订单进行验货
            tradeServiceDubbo.packTradesWithPost(staff, new Long[]{sid}, uniqueCodes, null);
        }
        // 修改唯一码验货状态
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodes);
        List<WaveUniqueCode> codes = queryItemUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "未查询到唯一码！");
        checkMatchUniqueCodes(codes, trade.getWarehouseId(), sid, needInspection);
        // 唯一码绑定
        if (!needInspection) {
            postPrintUniqueCodeBusiness.checkAllBindUniqueCodeForPack(staff, codes, trade);
        }
        matchUniqueCodes(staff, sid, codes, orders, trade.getWarehouseId(), needInspection, trade.getShortId());
        // 更新订单信息
        updateTradeWaveId(staff, trade);
        // 唯一码收货
        List<WaveUniqueCode> needReceives = codes.stream().filter(code -> Objects.equals(code.getReceiveStatus(), 0)).collect(Collectors.toList());
        List<WaveUniqueCode> needMoves = codes.stream().filter(code -> !Objects.equals(code.getReceiveStatus(), 0) && Objects.equals(code.getStockPosition(), UniqueCodeStockPositionEnum.WAREHOUSING_AREA.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needReceives)) {
            if (Objects.equals(receiveType, 2)) {
                // 包装验货收货后平移库存
                moveStock4Pack(staff, needReceives, needMoves);
            }
            return;
        }
        if (receiveType == null && openPackAutoReceiveGoods == null) {
            ReceiveUniqueCodeParams uniqueCodeParams = new ReceiveUniqueCodeParams();
            uniqueCodeParams.setOpType(0);
            uniqueCodeParams.setUniqueCodeArr(needReceives.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList()));
            uniqueCodeParams.setStockPosition(UniqueCodeStockPositionEnum.WAREHOUSING_AREA.getType());
            uniqueCodeParams.setSource(OperateSourceContext.acquireOperateSourceNew().getProject());
            orderUniqueCodeService.batchReceive(staff, uniqueCodeParams);
            return;
        }
        if (Objects.equals(openPackAutoReceiveGoods, 1)) {
            // 包装验货收货后平移库存
            moveStock4Pack(staff, Lists.newArrayList(), needMoves);
            // 包装验货开启自动收货, 不走下面收货逻辑
            return;
        }

        // 包装验货, 【已采退】/【等待收货】/【等待采购】/【待入库】的唯一码收货
        uniqueCodeBaseService.batchReceive4Pack(staff, receiveType, needReceives);
        // 包装验货收货后平移库存
        moveStock4Pack(staff, needReceives, needMoves);
    }

    private void moveStock4Pack(Staff staff, List<WaveUniqueCode> needReceives, List<WaveUniqueCode> needMoves) {
        List<WaveUniqueCode> needMoveList = needReceives.stream().filter(n -> UniqueCodeUtils.canReceive(n.getStatus())).collect(toList());
        needMoveList.addAll(needMoves);
        if (CollectionUtils.isEmpty(needMoveList)) {
            return;
        }
        postPrintUniqueCodeBusiness.moveWmsStock4UniqueCode(staff, needMoveList, WorkingStorageSection.MoveStockEnum.PURCHASE);
        postPrintUniqueCodeBusiness.moveStock4UniqueCode(staff, needMoveList, 5);
    }

    private void checkMatchUniqueCodes(List<WaveUniqueCode> codes, Long warehouseId, Long sid, boolean needInspection) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        for (WaveUniqueCode code : codes) {
            if (DataUtils.checkLongNotEmpty(code.getSid())) {
                if (needInspection) {
                    throw new IllegalArgumentException("唯一码：" + code.getUniqueCode() + "已绑定订单！");
                } else {
                    if (Objects.equals(code.getSid(), sid) && Objects.equals(code.getMatchedStatus(), 2)) {
//                        throw new IllegalArgumentException("唯一码已验！");
                    } else if (!Objects.equals(code.getSid(), sid)) {
                        throw new IllegalArgumentException("唯一码：" + code.getUniqueCode() + "已绑定订单！");
                    }
                }
            }
            if (DataUtils.checkLongNotEmpty(code.getWarehouseId()) && !Objects.equals(warehouseId, code.getWarehouseId())) {
                throw new IllegalArgumentException("唯一码：" +  code.getUniqueCode() + "与订单仓库不匹配！");
            }
        }
    }

    private void matchUniqueCodes(Staff staff, Long sid, List<WaveUniqueCode> codes, List<Order> orders, Long warehouseId, boolean needInspection, Long shortSid) {
        List<WaveUniqueCode> updates = new ArrayList<>();
        Map<Long, Integer> orderNumMap =  orders.stream().collect(Collectors.toMap(Order::getId, Order::getNum, (v1, v2) -> v2));
        int orderItemNum = orders.stream().filter(o -> DataUtils.checkIntegerNotEmpty(o.getNum())).mapToInt(Order::getNum).sum();
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> codeTypeChangeLogs = Lists.newArrayList();
        for (WaveUniqueCode code : codes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setUniqueCode(code.getUniqueCode());
            update.setMatchedStatus(2);
            Integer codeType;
            if (!Objects.equals(code.getCodeType(), codeType = orderItemNum > 1 ? 2 : 1)) {
                update.setCodeType(codeType);
                codeTypeChangeLogs.add(new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO().setUniqueCode(code.getUniqueCode()).setOldCodeType(code.getCodeType()).setCodeType(codeType));
            }
            // 包装验货前置已经绑定过了
            if (Objects.equals(sid, code.getSid()) && !needInspection) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("包装验货唯一码前置已绑定该订单，sid=%s，uniqueCode=%s", sid, code.getUniqueCode())));
                }
                update.setBindType(1);
                updates.add(update);
                continue;
            }
            update.setBindType(2);
            update.setSid(sid);
            update.setWaveId(-6L);
            update.setWarehouseId(warehouseId);
            update.setOrderId(getMatchOrderId(orders, code, orderNumMap));
            updates.add(update);
            code.setShortSid(shortSid);
            code.setSid(sid);
        }
        waveUniqueCodeDao.batchUpdate(staff, updates);
        // 保存唯一码绑定日志
        if (!needInspection) {
            postPrintUniqueCodeBusiness.saveUniqueCodeLogsForPack(staff, updates);
        }

        if (needInspection) {
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace")
                    .setArgs(new Object[]{staff, WaveUniqueCodeLogUtils.getWaveUniqueCodeLogDTOS(codes), WaveUniqueOpType.POST_BIND_UNIQUE_CODE}), null);
        }

        // 唯一码修改单多类型：唯一码类型修改：单/多/备->单/多/备
        eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, codeTypeChangeLogs, WaveUniqueOpType.UNIQUE_CODE_UPDATE_CODE_TYPE}), null);
    }

    private static Long getMatchOrderId(List<Order> orders, WaveUniqueCode code, Map<Long, Integer> orderNumMap) {
//        Assert.isTrue(Objects.equals(2, code.getType()), String.format("唯一码[%s]为非商品唯一码! ", code.getUniqueCode()));
        for (Order order : orders) {
            if (order.getNum() <= 0) {
                continue;
            }
            //子订单和唯一码匹配商家编码
            boolean isMatch = Objects.equals(code.getSysItemId(), order.getItemSysId()) && Objects.equals(DataUtils.getZeroIfDefault(code.getSysSkuId()), DataUtils.getZeroIfDefault(order.getSkuSysId())) && DataUtils.checkIntegerNotEmpty(orderNumMap.get(order.getId()));
            if (isMatch) {
                orderNumMap.put(order.getId(), orderNumMap.get(order.getId()) - CommonConstants.JUDGE_YES);
                return order.getId();
            }
        }
        return 0L;
    }

    private void updateTradeWaveId(Staff staff, Trade trade) {
        Trade update = new Trade();
        update.setSid(trade.getSid());
        update.setCompanyId(staff.getCompanyId());
        update.setWaveId(WaveTypeEnum.ITEM_UNIQUE_CODE.getWaveId());
        List<Trade> trades = Lists.newArrayList(update);
        waveTradeDao.updateTradeWaveId(staff, trades);
        logisticsWaveRelationBusiness.batchInsertCheckExist(staff, trades,LogisticsWaveRelation.LogisticsType.TRADE, LogisticsWaveRelation.WaveTypeEnum.UNIQUE_CODE, LogisticsWaveRelation.WaveSubTypeEnum.ITEM_UNIQUE_CODE,true);
    }

    @Override
    public Trade getOrderInfoByOutSid(Staff staff , String outSid){
        List<Trade> trades = tradeSearchService.queryByOutSid(staff, outSid, true, null);
        Assert.notEmpty(trades, "根据该快递单号未查询到订单信息！");
        Trade trade = trades.get(0);
        if (!(trade.getExpressPrintTime() != null && trade.getExpressPrintTime().after(TradeTimeUtils.INIT_DATE))) {
            throw new IllegalArgumentException("请先打印快递单！");
        }
        if (!(Objects.equals(Trade.SYS_STATUS_FINISHED_AUDIT, trade.getSysStatus())
                && !BooleanUtils.toBoolean(trade.getIsPackage()))) {
            throw new IllegalArgumentException("该订单非待包装状态！");
        }
        OrderUniqueCodeUtils.validateTrade(staff, trade);
        // 取有效的订单商品，套件取子订单，加工和组合同普通商品取本身
        TradeUtils.setOrders(trade, fillMainOuterId(staff, WaveUtils.getOrdersForWave(TradeUtils.getOrders4Trade(trade), true)));
        if (featureService.checkHasFeature(staff.getCompanyId(), Feature.PRINT_TEMPLATE_INTEGRATE)) {
            fillLogisticsCompany(staff, trade);
            return trade;
        }
        Map<String, UserExpressTemplate> templateMap = userExpressTemplateService.getUserExpressWlbAllIncHiddenWithIdMap(staff);
        trade.setTemplateName(getTemplateName(trade.getTemplateType(), trade.getTemplateId(), templateMap));

        //订单中仅含免验商品 直接验货完成
        if (!CollectionUtils.isEmpty(TradeUtils.getOrders4Trade(trade)) && TradeUtils.getOrders4Trade(trade).stream().allMatch(OrderUtils::notCheck)) {
            try {
                tradeServiceDubbo.packTradesWithPost(staff, new Long[]{trade.getSid()}, null, null);
                trade.setFinishNoCheckPack(true);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "直接验货免验订单失败！"), e);
                trade.setFinishNoCheckPack(false);
                trade.setFinishNoCheckPackErrorMsg(e.getMessage());
            }
        }
        return trade;
    }

    @Override
    public Trade getOrderInfoByOutSidNew(Staff staff, String outSid, Boolean fillSupplier) {
        Trade trade = getOrderInfoByOutSid(staff, outSid);
        if(BooleanUtils.isNotTrue(fillSupplier)){
            return trade;
        }
        fillSupplier(staff, ((Orderable<Order>) trade).getOrders());
        return trade;
    }

    private void fillLogisticsCompany(Staff staff, Trade trade) {
        if (trade == null || trade.getLogisticsCompanyId() == null || trade.getLogisticsCompanyId() < 1) {
            return;
        }
        UserLogisticsCompanyDTO userLogisticsCompanyDTO = printExpressTemplateService.queryById(staff, trade.getLogisticsCompanyId(), 1);
        if (userLogisticsCompanyDTO != null && StringUtils.isNotEmpty(userLogisticsCompanyDTO.getName())) {
            trade.setLogisticsCompanyName(userLogisticsCompanyDTO.getName());
        }
    }

    @Override
    public WaveUniqueCode getItemUniqueInfo(Staff staff , String uniqueCode){
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setUniqueCodes(Lists.newArrayList(uniqueCode));
        List<WaveUniqueCode> codes = queryItemUniqueCodeByCondition(staff, params);
        if (CollectionUtils.isEmpty(codes)) {
            throw new WaveScanException(WaveScanException.ERROR_CODE_UNIQUE_CODE_NOT_EXIST, "唯一码不存在！");
        }

        WaveUniqueCode code = codes.get(0);
        if (DataUtils.checkLongNotEmpty(code.getSid())) {
            throw new IllegalArgumentException("该唯一码已绑定其他订单！");
        }
        Assert.isTrue(code.getType() != null && code.getType() == 2, "请输入商品唯一码！");
        Assert.isTrue(code.getMatchedStatus() == 0, "该唯一码已验货！");
        if (Objects.equals(OrderUniqueCodeStatusEnum.DELETE.getType(), code.getStatus())
                || Objects.equals(OrderUniqueCodeStatusEnum.OUT.getType(), code.getStatus())) {
            throw new IllegalArgumentException("该唯一码，已作废或已出库！");
        }
        return code;
    }

    @Override
    public String getOuterIdByUniqueCode(Staff staff, String uniqueCode) {
        return waveUniqueCodeDao.getOuterIdByUniqueCode(staff, uniqueCode);
    }

    @Override
    public List<WaveUniqueCode> packScanItemUniqueCodeInfo(Staff staff , List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return new ArrayList<>();
        }
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodes);
        List<WaveUniqueCode> codes = queryItemUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "唯一码不存在！");
        for (WaveUniqueCode code : codes) {
            if (DataUtils.checkLongNotEmpty(code.getSid())) {
                throw new IllegalArgumentException(String.format("该[%s]唯一码已绑定订单！", code.getUniqueCode()));
            }
//            Assert.isTrue(code.getType() != null && code.getType() == 2, String.format("该[%s]唯一码非商品唯一码！！", code.getUniqueCode()));
            Assert.isTrue(OrderUniqueCodeStatusEnum.WAIT_IN.getType().equals(code.getStatus()) || OrderUniqueCodeStatusEnum.IN.getType().equals(code.getStatus()), String.format("该[%s]唯一码状态为非待入库或在库状态！", code.getUniqueCode()));
        }
        return codes;
    }

    @Override
    public List<WaveUniqueCode> packScanItemUniqueCodeInfoNew(Staff staff, List<String> uniqueCodes, Long sid) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return new ArrayList<>();
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("包装验货校验绑定唯一码，sid：【%s】，uniqueCodes：【%s】", sid, JSON.toJSONString(uniqueCodes))));
        }
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodes);
        List<WaveUniqueCode> codes = queryItemUniqueCodeByCondition(staff, params);
        Assert.notEmpty(codes, "唯一码不存在！");
        WmsConfig wmsConfig = wmsService.getConfig(staff);
        boolean packUniqueCodeStatusValidate = Objects.equals(wmsConfig.getInteger(WmsConfigExtInfoEnum.PACK_UNIQUE_CODE_STATUS_VALIDATE.getKey()), 1);
        for (WaveUniqueCode code : codes) {
            if (DataUtils.checkLongNotEmpty(code.getSid())) {
                if (Objects.equals(code.getSid(), sid) && Objects.equals(code.getMatchedStatus(), 2)) {
//                    throw new IllegalArgumentException("唯一码已验！");
                } else if (!Objects.equals(code.getSid(), sid)) {
                    throw new IllegalArgumentException(String.format("该[%s]唯一码已绑定订单！", code.getUniqueCode()));
                }
            }
            if (packUniqueCodeStatusValidate) {
                Assert.isTrue(code.getStatus() == 11 || code.getStatus() == 3 || code.getStatus() == 5, "唯一码状态不支持包装验货！");
            }
            //Assert.isTrue(code.getStatus() != 7 && code.getStatus() != 12 && code.getStatus() != 15, String.format("该[%s]唯一码状态不支持操作！", code.getUniqueCode()));
        }
        return codes;
    }

    @Override
    public List<WaveUniqueCode> checkAndFilterScanUniqueCodes(Staff staff, List<String> uniqueCodes) {
        List<WaveUniqueCode> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return list;
        }

        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setUniqueCodes(uniqueCodes);
        List<WaveUniqueCode> codes = queryItemUniqueCodeByCondition(staff, params);

        if (CollectionUtils.isEmpty(codes)) {
            return list;
        }

        for (WaveUniqueCode code : codes) {
            if (DataUtils.checkLongNotEmpty(code.getSid()) || code.getType() == null || !Objects.equals(code.getType(), 2)) {
                continue;
            }

            if (!(OrderUniqueCodeStatusEnum.WAIT_IN.getType().equals(code.getStatus()) || OrderUniqueCodeStatusEnum.IN.getType().equals(code.getStatus()))) {
                continue;
            }

            list.add(code);
        }

        return list;
    }

    private void fillSupplier(Staff staff, List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        List<Long> sysItemIds = orders.stream().map(Order::getItemSysId).collect(Collectors.toList());
        List<Long> sysSkuIds = orders.stream().map(Order::getSkuSysId).map(sysSkuId -> null == sysSkuId || sysSkuId <= 0 ? 0L : sysSkuId).distinct().collect(Collectors.toList());
        List<ItemSupplierBridge> itemSupplierBridges = itemServiceDubbo.queryItemSuppliersBySysItemIdAndSysSkuId(staff, sysItemIds, sysSkuIds).stream().filter(e -> Objects.equals(e.getMainState(), 1)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemSupplierBridges)) {
            return;
        }
        Map<Long, Supplier> idSupplierMap = supplierService.queryByIds(staff, itemSupplierBridges.stream().map(ItemSupplierBridge::getSupplierId).collect(Collectors.toList())).stream().collect(Collectors.toMap(Supplier::getId, Function.identity()));
        for (ItemSupplierBridge bridge : itemSupplierBridges) {
            Supplier supplier = idSupplierMap.get(bridge.getSupplierId());
            if (null != supplier) {
                bridge.setSupplierName(supplier.getName());
            }
        }
        Map<String, ItemSupplierBridge> itemSupplierBridgeMap = itemSupplierBridges.stream().collect(Collectors.toMap(ItemSupplierBridge::getSysItemSkuIdKey, Function.identity()));
        for (Order order : orders) {
            ItemSupplierBridge bridge = itemSupplierBridgeMap.get(order.getItemSysId() + "_" + (DataUtils.checkLongNotEmpty(order.getSkuSysId()) ? order.getSkuSysId() : 0));
            Supplier supplier;
            if (bridge != null && (supplier = idSupplierMap.get(bridge.getSupplierId())) != null) {
                order.setSupplierList(Collections.singletonList(supplier));
            }
        }
    }

    private List<Order> fillMainOuterId(Staff staff, List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return orders;
        }
        List<Long> sysItemIds = orders.stream().map(Order::getItemSysId).collect(Collectors.toList());
        List<DmjItem> itemList = waveItemServiceWrapper.queryBySysItemIds(staff, sysItemIds, "sysItemId,outerId");
        if (CollectionUtils.isEmpty(itemList)) {
            return orders;
        }
        Map<Long, String> itemOuterMap = itemList.stream().collect(Collectors.toMap(DmjItem::getSysItemId, DmjItem::getOuterId));
        for (Order order : orders) {
            order.setMainOuterId(itemOuterMap.get(order.getItemSysId()));
            if (order.getSkuSysId() == null
                    || order.getSkuSysId() <= 0) {
                order.setSysOuterId("");
            }
            // 填充isPick
            if (!OrderUtils.notCheck(order)) {
                order.setIsPick(2);
            }
        }
        return orders;
    }

    private static String getTemplateName(Integer templateType, Long templateId, Map<String, UserExpressTemplate> templateMap) {
        if (DataUtils.checkLongNotEmpty(templateId)) {
            UserExpressTemplate template = templateMap.get((templateType == 0 ? "" : "w") + templateId);
            if (template != null) {
                return template.getName();
            }
        }

        return "";
    }

    /**
     * 仅供开放平台使用--查询唯一码
     */
    @Override
    public PageListBase<WaveUniqueCode> queryItemUniqueCodeList(Staff staff, ItemUniqueCodeParams params) {
        PageListBase<WaveUniqueCode> pageList = new PageList<>();
        Long count = null;
        if (!params.isNoNeedTotal()) {
            count = waveUniqueCodeDao.queryItemUniqueCodeCount(staff, params);
            if (count == null || count == 0) {
                pageList.setTotal(0L);
                pageList.setList(Lists.newArrayList());
                return pageList;
            }
        }
        List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeDao.queryItemUniqueCodeList(staff, params);
        // 填充已解绑历史信息
        orderUniqueCodeService.fillUnBoundHistoryInfo(staff, waveUniqueCodes);
        // 填充位置号
        orderUniqueCodeService.fillPositionNoInfo(staff, waveUniqueCodes);
        // 填充子订单信息
        fillOrderInfo(staff, waveUniqueCodes);
        // 填充唯一码关联信息
        initQueryVal(staff, waveUniqueCodes);
        pageList.setPage(params.getPage());
        pageList.setList(waveUniqueCodes);
        pageList.setTotal(count);
        return pageList;
    }

    @Override
    public WaveUniqueCodeValidateResult validateWaveUniqueCode(Staff staff, List<String> uniqueCodeList, Date startTime, Date endTime, Page page) {
        if (startTime != null && endTime != null) {
            return getUniqueCodeByDate(staff, startTime, endTime, page);
        }
        WaveUniqueCodeValidateResult result = new WaveUniqueCodeValidateResult();
        result.setHasNext(false);
        if (CollectionUtils.isEmpty(uniqueCodeList)) {
            result.setWaveUniqueCodeList(Lists.newArrayList());
            return result;
        }
        Map<String, String> codeMap = new HashMap<>();
        List<String> uniqueCodes = new ArrayList<>();
        for (String code : uniqueCodeList) {
            WaveUtils.MultiWavesSeedKey multiWavesSeedKey = WaveUtils.splitMultiWavesUniqueBarcodeNew(code);
            if (multiWavesSeedKey == null) {
                continue;
            }
            if (StringUtils.isNotEmpty(multiWavesSeedKey.getUniqueCode())) {
                uniqueCodes.add(multiWavesSeedKey.getUniqueCode());
                codeMap.put(multiWavesSeedKey.getUniqueCode(), code);
            }
        }
        result.setWaveUniqueCodeList(validateWaveUniqueCodeAndTradeStatus(staff, uniqueCodes, null, codeMap));
        return result;
    }

    private List<WaveUniqueCode> validateWaveUniqueCodeAndTradeStatus(Staff staff, List<String> uniqueCodes, List<WaveUniqueCode> waveUniqueCodes, Map<String, String> codeMap) {
        if (CollectionUtils.isEmpty(uniqueCodes) && CollectionUtils.isEmpty(waveUniqueCodes)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            waveUniqueCodes = waveUniqueCodeDao.queryByUniqueCodes(staff, uniqueCodes);
        }
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            return Lists.newArrayList();
        }
        for (WaveUniqueCode code : waveUniqueCodes) {
            if (codeMap != null) {
                code.setOriginCode(codeMap.get(code.getUniqueCode()));
            }
            if (code.getWaveId() == null || code.getWaveId() <= 0L) {
                code.setValidateErrorMsg("没有关联的波次");
                continue;
            }
            if (code.getSid() == null || code.getSid() <= 0L) {
                code.setValidateErrorMsg("没有关联的订单");
            }
        }
        Set<Long> waveIds = waveUniqueCodes.stream().map(WaveUniqueCode::getWaveId).collect(toSet());
        Set<Long> sids = waveUniqueCodes.stream().map(WaveUniqueCode::getSid).collect(toSet());
        if (CollectionUtils.isEmpty(waveIds) || CollectionUtils.isEmpty(sids)) {
            return waveUniqueCodes;
        }
        Map<Long, Wave> waveMap = waveDao.queryByIds(staff, waveIds.toArray(new Long[0])).stream().collect(Collectors.toMap(Wave::getId, w -> w, (k1,k2)->k1));
        Map<Long, Trade> tradeMap = tradeServiceDubbo.queryTradeBySids(staff, true, false, true, sids.toArray(new Long[0])).stream().collect(Collectors.toMap(Trade::getSid, w -> w, (k1,k2)->k1));
        for (WaveUniqueCode code : waveUniqueCodes) {
            if (StringUtils.isNotEmpty(code.getValidateErrorMsg())) {
                continue;
            }
            Wave wave = waveMap.get(code.getWaveId());
            Trade trade = tradeMap.get(code.getSid());
            if (wave == null) {
                code.setValidateErrorMsg("波次不存在");
                continue;
            }
            if (trade == null) {
                code.setValidateErrorMsg("订单不存在");
                continue;
            }
            if (!Objects.equals(trade.getWaveId(), wave.getId())) {
                code.setValidateErrorMsg("唯一码关联订单已踢出");
                continue;
            }
            //已审核或发货都不限制
            if (!Objects.equals(trade.getSysStatus(), Trade.SYS_STATUS_FINISHED_AUDIT) && !Objects.equals(trade.getSysStatus(), Trade.SYS_STATUS_FINISHED)
                    && !Objects.equals(trade.getSysStatus(), Trade.SYS_STATUS_SELLER_SEND_GOODS)) {
                code.setValidateErrorMsg("关联订单非已审核/发货状态");
                continue;
            }
            TradeValidator validator = new TradeValidator();
            validator.setThrowExceptionIfError(false);
            validator.check(staff, trade);
            if (validator.hasError() && TradeValidator.Error.INSUFFICIENT.getCode() != validator.getCode() && TradeValidator.Error.UPLOAD_EXCEPTION.getCode() != validator.getCode()) {
                code.setValidateErrorMsg("关联订单异常");
            }
        }
        return waveUniqueCodes;
    }

    private WaveUniqueCodeValidateResult getUniqueCodeByDate(Staff staff, Date startTime, Date endTime, Page page) {
        WaveUniqueCodeValidateResult result = new WaveUniqueCodeValidateResult();
        if (page == null) {
            page = new Page();
            page.setPageNo(1);
            page.setPageSize(50);
        }
        String sql = "SELECT sid FROM trade_not_consign_" + staff.getDbInfo().getTradeDbNo() + " WHERE sys_consigned!=1 and company_id = " + staff.getCompanyId() +
                " AND upd_time >=" + " '" + DateUtils.datetime2Str(startTime) + "' " +
                " AND upd_time <=" + " '" + DateUtils.datetime2Str(endTime) + "' " + " order by sid limit " + page.getStartRow() + "," + page.getPageSize();
        List<Long> sidList = jdbcTemplateAdapter.get(staff).queryForList(sql, Long.class);
        if (CollectionUtils.isEmpty(sidList)) {
            result.setHasNext(false);
            result.setWaveUniqueCodeList(Lists.newArrayList());
            return result;
        }
        if (sidList.size() < page.getPageSize()) {
            result.setHasNext(false);
        } else {
            result.setHasNext(true);
        }
        List<WaveUniqueCode> errorUniqueCodeList = new ArrayList<>();
        for (List<Long> subSids : Lists.partition(sidList, 100)) {
            List<WaveUniqueCode> codeList = waveUniqueCodeDao.queryWaveUniqueCodeBySids(staff, subSids);
            List<WaveUniqueCode> codes = codeList.stream().filter(code -> Objects.equals(code.getEnableStatus(), 1)).collect(toList());
            List<WaveUniqueCode> deleteUniqueCodes = codeList.stream().filter(code -> Objects.equals(code.getEnableStatus(), 0)).collect(toList());
            List<WaveUniqueCode> validateUniqueCodes = validateWaveUniqueCodeAndTradeStatus(staff, null, codes, null);
            if (CollectionUtils.isNotEmpty(validateUniqueCodes)) {
                errorUniqueCodeList.addAll(validateUniqueCodes.stream().filter(code -> StringUtils.isNotEmpty(code.getValidateErrorMsg())).collect(toList()));
            }
            if (CollectionUtils.isNotEmpty(deleteUniqueCodes)) {
                deleteUniqueCodes.forEach(c -> c.setValidateErrorMsg("该唯一码不存在"));
                errorUniqueCodeList.addAll(deleteUniqueCodes);
            }
        }
        result.setWaveUniqueCodeList(errorUniqueCodeList);
        return result;
    }

    @Override
    @Transactional
    public List<WaveUniqueCode> createAndQueryUniqueCodes(Staff staff, List<WaveUniqueCode> requestWaveUniqueCodes) {
        List<WaveUniqueCode> waveUniqueCodeList = doCreateAndQueryUniqueCodes(staff, requestWaveUniqueCodes);
        // 查询仓库名称
        orderUniqueCodeService.fillWarehouseName(staff, waveUniqueCodeList);
        return waveUniqueCodeList;
    }

    private List<WaveUniqueCode> doCreateAndQueryUniqueCodes(Staff staff, List<WaveUniqueCode> requestWaveUniqueCodes) {
        Assert.isTrue(!CollectionUtils.isEmpty(requestWaveUniqueCodes), "唯一码不存在");
        // 查询唯一码
        List<String> requestCodes = requestWaveUniqueCodes.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
        ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
        params.setUniqueCodes(requestCodes);
        List<WaveUniqueCode> waveUniqueCodeList = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, params);
        List<String> queryUniqueCodes = waveUniqueCodeList.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList());
        // 删除已有的
        requestWaveUniqueCodes.removeIf(code -> queryUniqueCodes.contains(code.getUniqueCode()));
        if (CollectionUtils.isEmpty(requestWaveUniqueCodes)) {
            return waveUniqueCodeList;
        }
        // 不存在的唯一码则新增
        createUniqueCodes(staff, requestWaveUniqueCodes);
        return waveUniqueCodeDao.queryItemUniqueCodesByCodes(staff, requestCodes);
    }

    public void createUniqueCodes(Staff staff, List<WaveUniqueCode> uniqueCodes) {
        List<String> errorMsg = Lists.newArrayList();
        ItemUniqueCodeGenerateParams generateParams = buildItemUniqueCodeGenerateParams(staff, uniqueCodes, errorMsg);
        if (!CollectionUtils.isEmpty(errorMsg)) {
            // 截取逗号后面的消息转化为异常
            String[] splitMsg = errorMsg.get(0).split("，");
            throw new IllegalArgumentException(splitMsg.length > 1 ? splitMsg[1] : splitMsg[0]);
        }
        generateParams.setSource(CommonConstants.JUDGE_YES);
        itemUniqueCodeGenerateBusiness.doGenerate(staff, generateParams, null);
    }

    private ItemUniqueCodeGenerateParams buildItemUniqueCodeGenerateParams(Staff staff, List<WaveUniqueCode> list, List<String> errorMsg) {
        Map<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> baseDetails = Maps.newHashMap();
        Integer num = 1;
        for (WaveUniqueCode code : list) {
            ItemUniqueCodeGenerateParams.GenerateDetail detail = new ItemUniqueCodeGenerateParams.GenerateDetail();
            detail.setOuterId(code.getOuterId());
            detail.setUniqueCode(code.getUniqueCode());
            detail.setStatus(OrderUniqueCodeStatusEnum.WAIT_IN.getType());
            detail.setSupplierName(code.getSupplierName());
            detail.setWarehouseName(code.getWarehouseName());
            detail.setNum(1);
            baseDetails.put(num, detail);
            num ++;
        }
        return buildItemUniqueCodeGenerateParams(staff, errorMsg, baseDetails);
    }

    @Override
    @Transactional
    public List<WaveUniqueCode> afterSaleGenerate(Staff staff, AfterSaleGenerateUniqueCodeParams generateUniqueCodeParams) {
        // 校验并记录参数
        afterSaleGenerateParamsCheck(staff, generateUniqueCodeParams);
        List<AfterSaleGenerateUniqueCodeParams.ItemInfo> itemInfoList = generateUniqueCodeParams.getItemInfoList();
        Map<Long, List<AfterSaleGenerateUniqueCodeParams.ItemInfo>> warehouseItemInfoMap = itemInfoList.stream().collect(Collectors.groupingBy(AfterSaleGenerateUniqueCodeParams.ItemInfo::getWarehouseId));

        // 生成前的数据准备
        Map<String, List<WaveUniqueCode>> orderCodesMap = getOrderCodesMap(staff, itemInfoList);
        // 已生成唯一码的修改状态
        List<WaveUniqueCode> result = new ArrayList<>();

        // 生成唯一码
        for (Map.Entry<Long, List<AfterSaleGenerateUniqueCodeParams.ItemInfo>> entry : warehouseItemInfoMap.entrySet()) {
            ItemUniqueCodeGenerateParams generateParams = buildAfterSaleGenerateParams(staff, entry.getValue(), entry.getKey(), orderCodesMap);
            generateParams.setWarehouseId(entry.getKey());
            itemUniqueCodeGenerateBusiness.doGenerate(staff, generateParams, null);
            // 生成唯一码关联信息
            generateUniqueCodeRelation(staff, generateParams.getInsertCodes());
            result.addAll(generateParams.getInsertCodes());
        }
        return result;
    }

    /**
     * 修改已经生成的唯一码状态，在库、消退暂存区等
     * @param itemInfoList
     * @param afterSaleCodesMap
     * @param updates
     * @param staff
     */
    private void updateExistsUniqueCode(Staff staff, List<AfterSaleGenerateUniqueCodeParams.ItemInfo> itemInfoList, Map<String, List<WaveUniqueCode>> afterSaleCodesMap, List<WaveUniqueCode> updates, List<WaveUniqueCode> result) {
        for (AfterSaleGenerateUniqueCodeParams.ItemInfo itemInfo : itemInfoList) {
            String afterSaleKey = itemInfo.getAfterSaleCode() + WmsKeyUtils.KEY_JOINER + itemInfo.getAfterSaleDetailId();
            List<WaveUniqueCode> uniqueCodes = afterSaleCodesMap.get(afterSaleKey);
            if (CollectionUtils.isEmpty(uniqueCodes)) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, "afterSaleKey未关联商品唯一码：" + afterSaleKey));
                }
                continue;
            }

            // 还可以生成的唯一码数量
            Integer canInsertNum = itemInfo.getAfterSaleDetailNum() - uniqueCodes.size();
            Integer needInsertNum = (canInsertNum >= itemInfo.getNum() ? itemInfo.getNum() : canInsertNum);
            Integer needUpdateNum = itemInfo.getNum() - needInsertNum;
            itemInfo.setNum(DataUtils.getZeroIfDefaultI(needInsertNum));
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "afterSaleKey：" + afterSaleKey + " canInsertNum:" + canInsertNum + " needInsertNum：" + needInsertNum + " needUpdateNum：" + needUpdateNum + " uniqueCodes:" + uniqueCodes.size()));
            }

            if (needUpdateNum > 0) {
                // 先匹配在库状态的唯一码
                uniqueCodes.sort(Comparator.comparing(WaveUniqueCode::getStatus));
                buildAfterSaleUniqueCodes(updates, uniqueCodes, needUpdateNum, result);
            }
        }
    }

    private void buildAfterSaleUniqueCodes(List<WaveUniqueCode> updates, List<WaveUniqueCode> uniqueCodes, Integer needUpdateNum, List<WaveUniqueCode> result) {
        List<WaveUniqueCode> needUpdateCodes = uniqueCodes.subList(0, needUpdateNum);
        for (WaveUniqueCode code : needUpdateCodes) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(code.getId());
            update.setUniqueCode(code.getUniqueCode());
            update.setStatus(OrderUniqueCodeStatusEnum.IN.getType());
            update.setGoodsSectionCode(WorkingStorageSection.TypeEnum.REFUND.getName());
            updates.add(update);
            result.add(code);
        }
    }

    /**
     * 根据生成的唯一码，生成关联信息表
     * @param staff
     * @param inserts
     */
    private void generateUniqueCodeRelation(Staff staff, List<WaveUniqueCode> inserts) {
        List<UniqueCodeRelation> relations = new ArrayList<>();
        for (WaveUniqueCode insert : inserts) {
            UniqueCodeRelation relation = new UniqueCodeRelation();
            relation.setUniqueCodeId(0L);
            relation.setUniqueCode(insert.getUniqueCode());
            relation.setBusinessCode(insert.getAfterSaleOrderCode());
            relation.setBusinessId(0L);
            relation.setSubBusinessId(Long.valueOf(insert.getAfterSaleDetailId()));
            relation.setBusinessType(UniqueCodeRelation.BusinessType.AFTER_SALE_ORDER.getType());
            relations.add(relation);
            // 关联销退入库单
            UniqueCodeRelation uniqueCodeRelation = new UniqueCodeRelation();
            BeanUtils.copyProperties(relation, uniqueCodeRelation);
            uniqueCodeRelation.setBusinessType(UniqueCodeRelation.BusinessType.SALES_RETURN_RECEIPT_ORDER.getType());
            uniqueCodeRelation.setBusinessCode(insert.getSaleReturnOrderCode());
            uniqueCodeRelation.setSubBusinessId(Long.valueOf(insert.getSalesReturnOrderDetailId()));
            relations.add(uniqueCodeRelation);
        }

        uniqueCodeRelationDao.batchInsert(staff, relations);
    }

    private ItemUniqueCodeGenerateParams buildAfterSaleGenerateParams(Staff staff, List<AfterSaleGenerateUniqueCodeParams.ItemInfo> itemInfos, Long warehouseId, Map<String, List<WaveUniqueCode>> orderCodesMap) {
        ItemUniqueCodeGenerateParams generateParams = new ItemUniqueCodeGenerateParams();
        ItemUniqueCodeGenerateRule generateRule = new ItemUniqueCodeGenerateRule();
        generateRule.setCustomFieldSort("skuOuterId,identifier,uniqueCode");
        generateRule.setStyle(0);
        generateParams.setGenerateRule(generateRule);
        generateParams.setSource(2);

        Map<Integer, ItemUniqueCodeGenerateParams.GenerateDetail> detailMap = Maps.newHashMap();
        List<ItemUniqueCodeGenerateParams.GenerateDetail> details = new ArrayList<>();
        generateParams.setDetails(details);
        int i = 0;
        for (AfterSaleGenerateUniqueCodeParams.ItemInfo itemInfo : itemInfos) {
            if (itemInfo.getNum() <= 0) {
                continue;
            }

            ItemUniqueCodeGenerateParams.GenerateDetail detail = new ItemUniqueCodeGenerateParams.GenerateDetail();
            detail.setNum(itemInfo.getNum());
            detail.setWarehouseId(warehouseId);
            detail.setSysItemId(itemInfo.getSysItemId());
            detail.setSysSkuId(DataUtils.getZeroIfDefault(itemInfo.getSysSkuId()));
            detail.setStatus(OrderUniqueCodeStatusEnum.IN.getType());
            detail.setGoodsSectionCode("");
            detail.setAfterSaleOrderCode(itemInfo.getAfterSaleCode());
            detail.setAfterSaleDetailId(itemInfo.getAfterSaleDetailId());
            detail.setOuterId(itemInfo.getOuterId());
            detail.setSid(itemInfo.getSid());
            detail.setOrderId(itemInfo.getOrderIds());
            detail.setSaleReturnOrderCode(itemInfo.getSaleReturnOrderCode());
            detail.setSalesReturnOrderDetailId(itemInfo.getSalesReturnOrderDetailId());
            setSupplierInfoFromOrderUniqueCode(staff, orderCodesMap, itemInfo, detail);

            details.add(detail);
            detailMap.put(i, detail);
            i++;
        }
        fillBestSupplier(staff, detailMap, new ArrayList<>());
        return generateParams;
    }

    private void setSupplierInfoFromOrderUniqueCode(Staff staff, Map<String, List<WaveUniqueCode>> orderCodesMap, AfterSaleGenerateUniqueCodeParams.ItemInfo itemInfo, ItemUniqueCodeGenerateParams.GenerateDetail detail) {
        if (DataUtils.checkLongNotEmpty(itemInfo.getSid()) && DataUtils.checkLongNotEmpty(itemInfo.getOrderIds())) {
            List<WaveUniqueCode> uniqueCodes = orderCodesMap.get(itemInfo.getSid() + WmsKeyUtils.KEY_JOINER + itemInfo.getOrderIds());
            if (!CollectionUtils.isEmpty(uniqueCodes)) {
                detail.setSupplierId(uniqueCodes.get(0).getSupplierId());
                detail.setSupplierName(uniqueCodes.get(0).getSupplierName());

                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, itemInfo.getAfterSaleCode() + WmsKeyUtils.KEY_JOINER + itemInfo.getAfterSaleDetailId() + " 关联订单唯一码，supplierId：" + detail.getSupplierId() + "，supplierName：" + detail.getSupplierName()));
                }
            }
        }
    }

    /**
     * 获取 售后唯一码
     * @param staff
     * @param itemInfos
     * @return
     */
    private Map<String, List<WaveUniqueCode>> getAfterSaleCodesMap(Staff staff, List<AfterSaleGenerateUniqueCodeParams.ItemInfo> itemInfos) {
        List<String> afterSaleCodes = itemInfos.stream().map(AfterSaleGenerateUniqueCodeParams.ItemInfo::getAfterSaleCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(afterSaleCodes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "afterSaleCodes为空，不查询关联单据！"));
            }
            return Collections.emptyMap();
        }

        // 性能问题，先根据售后工单号查询关联单据表，取得唯一码，再回唯一码表查询
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setBusinessCodes(afterSaleCodes);
        params.setBusinessType(UniqueCodeRelation.BusinessType.AFTER_SALE_ORDER.getType());
        List<UniqueCodeRelation> uniqueCodeRelations = uniqueCodeRelationDao.queryByUniqueCondition(staff, params);
        if (CollectionUtils.isEmpty(uniqueCodeRelations)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "未查询到关联单据，uniqueCodeRelations：" + afterSaleCodes));
            }
            return Collections.emptyMap();
        }

        Map<String, UniqueCodeRelation> relationMap = uniqueCodeRelations.stream().collect(Collectors.toMap(UniqueCodeRelation::getUniqueCode, a -> a, (c1, c2) -> c1));
        // 拿到售后唯一码
        ItemUniqueCodeQueryParams itemParams = new ItemUniqueCodeQueryParams();
        itemParams.setUniqueCodes(uniqueCodeRelations.stream().map(UniqueCodeRelation::getUniqueCode).collect(Collectors.toList()));
        itemParams.setStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.IN.getType(), OrderUniqueCodeStatusEnum.OUT.getType()));
        List<WaveUniqueCode> dbUniqueCodes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, itemParams);
        if (CollectionUtils.isEmpty(dbUniqueCodes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "根据售后唯一码，未查询到唯一码，uniqueCodes：" + itemParams.getUniqueCodes()));
            }
            return Collections.emptyMap();
        }
        setAfterSaleInfo(relationMap, dbUniqueCodes);
        return dbUniqueCodes.stream().collect(Collectors.groupingBy(code -> code.getAfterSaleOrderCode() + WmsKeyUtils.KEY_JOINER + code.getAfterSaleDetailId()));
    }

    /**
     * 获取售后工单对应的订单唯一码
     * @param staff
     * @param itemInfos 售后商品信息
     * @return
     */
    private Map<String, List<WaveUniqueCode>> getOrderCodesMap(Staff staff, List<AfterSaleGenerateUniqueCodeParams.ItemInfo> itemInfos) {
        List<Long> sids = itemInfos.stream().map(AfterSaleGenerateUniqueCodeParams.ItemInfo::getSid).distinct().collect(Collectors.toList());
        List<Long> orderIds = itemInfos.stream().map(AfterSaleGenerateUniqueCodeParams.ItemInfo::getOrderIds).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sids)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "sids未空，未查询订单唯一码！"));
            }
            return Collections.emptyMap();
        }

        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setSids(sids);
        params.setOrderIds(orderIds);
        List<WaveUniqueCode> uniqueCodes = waveUniqueCodeDao.queryOrderUniqueCodeByCondition(staff, params);
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "未查询到订单唯一码，sids：" + sids));
            }
            return Collections.emptyMap();
        }
        return uniqueCodes.stream().collect(Collectors.groupingBy(code -> code.getSid() + WmsKeyUtils.KEY_JOINER + code.getOrderId()));
    }

    private void afterSaleGenerateParamsCheck(Staff staff, AfterSaleGenerateUniqueCodeParams generateUniqueCodeParams) {
        Assert.notEmpty(generateUniqueCodeParams.getItemInfoList(), "生成商品信息不能为空！");
        List<AfterSaleGenerateUniqueCodeParams.ItemInfo> itemInfoList = generateUniqueCodeParams.getItemInfoList();

        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "开始生成售后唯一码，参数：" + itemInfoList.stream().map(item -> WmsKeyUtils.buildItemKey(item.getSysItemId(), item.getSysSkuId()) + "_num:" + item.getNum() + "_afterSaleDetailNum:" + item.getAfterSaleDetailNum() + "_afterSaleCode:" + item.getAfterSaleCode() + "_saleReturnCode" + item.getAfterSaleCode()).collect(Collectors.toSet())));
        }

        for (AfterSaleGenerateUniqueCodeParams.ItemInfo itemInfo : itemInfoList) {
            Assert.notNull(itemInfo.getSysItemId(), "主商家编码Id不能为空！");
            Assert.notNull(itemInfo.getSysSkuId(), "规格商家编码Id不能为空！");
            Assert.hasText(itemInfo.getAfterSaleCode(), "售后单号不能为空！");
            Assert.notNull(itemInfo.getAfterSaleDetailId(), "售后工单明细Id不能为空！");
            Assert.notNull(itemInfo.getAfterSaleDetailNum(), "售后工单明细商品数量不能为空！");
            Assert.notNull(itemInfo.getWarehouseId(), "仓库不能为空！");
            Assert.notNull(itemInfo.getNum(), "生成商品数量不能为空！");
            Assert.hasText(itemInfo.getSaleReturnOrderCode(), "销退入库单号不能为空！");
            Assert.notNull(itemInfo.getSalesReturnOrderDetailId(), "销退入库单明细Id不能为空！");
            Assert.hasText(itemInfo.getOuterId(), "商家编码不能为空！");
        }
    }

    @Override
    @Transactional
    public void cancelAfterSaleUniqueCode(Staff staff, List<String> uniqueCodes) {
        List<WaveUniqueCode> dbCodes = queryByUniqueCodes(staff, uniqueCodes);
        if (CollectionUtils.isEmpty(dbCodes)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "数据库未查询到唯一码！"));
            }
            return;
        }

        List<Long> codeIds = dbCodes.stream().map(WaveUniqueCode::getId).collect(Collectors.toList());
        batchCancel(staff, codeIds, true);
    }

    @Override
    public List<WaveUniqueCode> queryByCondition4AfterSale(Staff staff, ItemUniqueCodeQueryParams params) {
        if (CollectionUtils.isEmpty(params.getAfterSaleOrderCodes()) && StringUtils.isEmpty(params.getAfterSaleOrderCode()) && CollectionUtils.isEmpty(params.getAfterSaleDetailIds())) {
            return Collections.emptyList();
        }

        // 先根据售后工单号查询唯一码关联信息表
        List<UniqueCodeRelation> uniqueCodeRelations = queryRelation4AfterSale(staff, params);
        if (CollectionUtils.isEmpty(uniqueCodeRelations)) {
            return Collections.emptyList();
        }

        // 再根据唯一码查询
        ItemUniqueCodeQueryParams itemParams = new ItemUniqueCodeQueryParams();
        itemParams.setUniqueCodes(uniqueCodeRelations.stream().map(UniqueCodeRelation::getUniqueCode).collect(Collectors.toList()));
        Map<String, UniqueCodeRelation> relationMap = uniqueCodeRelations.stream().collect(Collectors.toMap(UniqueCodeRelation::getUniqueCode, a -> a, (c1, c2) -> c1));
        List<WaveUniqueCode> dbUniqueCodes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, itemParams);
        setAfterSaleInfo(relationMap, dbUniqueCodes);
        return dbUniqueCodes;
    }

    private void setAfterSaleInfo(Map<String, UniqueCodeRelation> relationMap, List<WaveUniqueCode> dbUniqueCodes) {
        if (CollectionUtils.isEmpty(dbUniqueCodes)) {
            return;
        }
        for (WaveUniqueCode code : dbUniqueCodes) {
            UniqueCodeRelation uniqueCodeRelation = relationMap.get(code.getUniqueCode());
            if (uniqueCodeRelation != null) {
                code.setAfterSaleOrderCode(uniqueCodeRelation.getBusinessCode());
                code.setAfterSaleDetailId(uniqueCodeRelation.getSubBusinessId() == null ? null : uniqueCodeRelation.getSubBusinessId().toString());
            }
        }
    }

    /**
     * 查询售后唯一码关联的单据
     * @param staff
     * @param params
     * @return
     */
    private List<UniqueCodeRelation> queryRelation4AfterSale(Staff staff, ItemUniqueCodeQueryParams params) {
        List<String> afterSaleOrderCodes = new ArrayList<>();
        if (!CollectionUtils.isEmpty(params.getAfterSaleOrderCodes())) {
            afterSaleOrderCodes.addAll(params.getAfterSaleOrderCodes());
        }
        if (StringUtils.isNotEmpty(params.getAfterSaleOrderCode())) {
            afterSaleOrderCodes.add(params.getAfterSaleOrderCode());
        }

        OrderUniqueCodeQueryParams relationParams = new OrderUniqueCodeQueryParams();
        relationParams.setBusinessCodes(afterSaleOrderCodes);
        relationParams.setSubBusinessIds(params.getAfterSaleDetailIds());
        relationParams.setBusinessType(UniqueCodeRelation.BusinessType.AFTER_SALE_ORDER.getType());
        return uniqueCodeRelationDao.queryByUniqueCondition(staff, relationParams);
    }

    @Override
    @Transactional
    public void updateAfterSaleUniqueCode(Staff staff, List<WaveUniqueCode> codes, Boolean qualityType) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }

        List<WaveUniqueCode> updateList = codes.stream().filter(t -> DataUtils.checkLongNotEmpty(t.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }

        fillUniqueCodeRelationInfo(staff, updateList);
        if (logger.isDebugEnabled()) {
            logger.debug("售后唯一码上架,更新唯一码的状态, uniqueCodes" + updateList.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList()));
        }

        updateList.forEach(t -> t.setGoodsSectionCode(t.getGoodsSectionCode() + "," + t.getNum() + "," + t.getGoodsSectionId()));
        // 发送售后唯一码上架事件到售后
        eventCenter.fireEvent(this, new EventInfo("pda.unique.code.shelve").setArgs(new Object[]{staff, updateList, qualityType}), null);
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodeByCodes(Staff staff, List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return new ArrayList<>();
        }
        ItemUniqueCodeQueryParams itemParams = new ItemUniqueCodeQueryParams();
        itemParams.setUniqueCodes(uniqueCodes);
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, itemParams);
        initQueryVal(staff, codes);

        return codes;
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodeByCodes(Staff staff, List<String> uniqueCodes, Boolean convertSkuId) {
        List<WaveUniqueCode> codes = queryItemUniqueCodeByCodes(staff, uniqueCodes);
        if (!convertSkuId) {
            return codes;
        }
        for (WaveUniqueCode code : codes) {
            code.setSysSkuId(DataUtils.getZeroIfDefault(code.getSysSkuId()));
        }
        return codes;
    }

    @Override
    public List<WaveUniqueCode> returnToWarehouse(Staff staff, ReturnToWarehouseParams params) {
        return afterSaleUniqueCodeService.returnToWarehouse(staff, params);
    }

    @Override
    public void cancelReturnToWarehouse(Staff staff, CancelReturnToWarehouseParams params) {
        afterSaleUniqueCodeService.cancelReturnToWarehouse(staff, params);
    }

    @Override
    public void afterSaleOnShelf(Staff staff, AfterSaleOnShelfParams param) {
        afterSaleUniqueCodeService.afterSaleOnShelf(staff, param);
    }

    /**
     * 查询各种码信息
     * @param staff
     * @param params
     * @return
     */
    @Override
    public UniqueCodeInfoQueryVO queryUniqueCodeInfo(Staff staff, UniqueCodeInfoQueryParams params) {
        return uniqueCodeHelpBusiness.queryUniqueCodeInfo(staff, params);
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodesByCodes(Staff staff, List<String> uniqueCodes, Boolean fillRelation) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Collections.EMPTY_LIST;
        }
        List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodesByCodes(staff, uniqueCodes);
        if (BooleanUtils.isTrue(fillRelation)) {
            initQueryVal(staff, codes);
        }
        return codes;
    }

    /**
     * 填充唯一码关联信息
     * @param staff
     * @param updateList
     */
    public void fillUniqueCodeRelationInfo(Staff staff, List<WaveUniqueCode> updateList) {
        OrderUniqueCodeQueryParams params = new OrderUniqueCodeQueryParams();
        params.setUniqueCodes(updateList.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList()));
        List<UniqueCodeRelation> uniqueCodeRelationList = uniqueCodeRelationDao.queryByUniqueCondition(staff, params);
        // 关联的售后单
        Map<String, List<UniqueCodeRelation>> afterSaleOrderCodeMap = uniqueCodeRelationList.stream().filter(code -> code.filterBusinessType(UniqueCodeRelation.BusinessType.AFTER_SALE_ORDER)).collect(Collectors.groupingBy(UniqueCodeRelation::getUniqueCode));
        // 关联的销退单
        Map<String, List<UniqueCodeRelation>> saleReturnOrderCodeMap = uniqueCodeRelationList.stream().filter(code -> code.filterBusinessType(UniqueCodeRelation.BusinessType.SALES_RETURN_RECEIPT_ORDER)).collect(Collectors.groupingBy(UniqueCodeRelation::getUniqueCode));
        for (WaveUniqueCode code : updateList) {
            code.setOldStatus(code.getStatus());
            code.setStatus(OrderUniqueCodeStatusEnum.WAIT_PICK.getType());
            code.setStockPosition(UniqueCodeStockPositionEnum.SHELVES.getType());
            code.setStockStatus(CommonConstants.VALUE_YES);
            code.setReceiveStatus(CommonConstants.VALUE_NO);

            List<UniqueCodeRelation> afterSaleOrderRelations = afterSaleOrderCodeMap.get(code.getUniqueCode());
            if (!CollectionUtils.isEmpty(afterSaleOrderRelations)) {
                UniqueCodeRelation uniqueCodeRelation = afterSaleOrderRelations.stream().max(Comparator.comparing(UniqueCodeRelation::getId)).get();
                code.setAfterSaleOrderCode(uniqueCodeRelation.getBusinessCode());
                code.setAfterSaleDetailId(uniqueCodeRelation.getSubBusinessId() + "");
            }

            List<UniqueCodeRelation> saleReturnOrderRelations = saleReturnOrderCodeMap.get(code.getUniqueCode());
            if (!CollectionUtils.isEmpty(saleReturnOrderRelations)) {
                UniqueCodeRelation relation = saleReturnOrderRelations.stream().max(Comparator.comparing(UniqueCodeRelation::getId)).get();
                code.setSaleReturnOrderCode(relation.getBusinessCode());
                code.setSalesReturnOrderDetailId(relation.getSubBusinessId() + "");
            }
        }
    }

    @Override
    public List<WaveUniqueCode> queryByOrderIds(Staff staff, ItemUniqueCodeQueryParams params) {
        if (params == null || CollectionUtils.isEmpty(params.getOrderIds())) {
            return new ArrayList<>();
        }
        // 强制走orderId的索引
        params.setForceOrderIdIndex(true);
        if (params.getPage() == null) {
            params.setPage(new Page().setPageSize(1000));
        }
        return waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, params);
    }

    @Override
    public List<UniqueCodeTradeExport> queryByOrderIds4TradeExport(Staff staff, ItemUniqueCodeQueryParams params) {
        List<WaveUniqueCode> codes = queryByOrderIds(staff, params);
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        } else {
            return codes.stream().map(code -> {
                UniqueCodeTradeExport uniqueCodeTradeExport = new UniqueCodeTradeExport();
                uniqueCodeTradeExport.setOrderId(code.getOrderId());
                uniqueCodeTradeExport.setUniqueCode(code.getUniqueCode());
                return uniqueCodeTradeExport;
            }).collect(toList());
        }
    }

    /**
     * 根据orderId查询商品/订单唯一码
     */
    @Override
    public PageListBase<WaveUniqueCode> queryByOrderId(Staff staff, ItemUniqueCodeQueryParams params) {
        PageListBase<WaveUniqueCode> pageList = new PageList<>();
        if (CollectionUtils.isEmpty(params.getOrderIds())) {
            return pageList;
        }
        if (params.getPage() == null) {
            params.setPage(new Page().setPageSize(1000));
        }
        Long count = waveUniqueCodeDao.countItemUniqueCode(staff, params);
        pageList.setTotal(count);
        pageList.setPage(params.getPage());
        if (count > params.getPage().getStartRow()) {
            List<WaveUniqueCode> codes = waveUniqueCodeDao.queryItemUniqueCodeByCondition(staff, params);
            pageList.setList(codes);
        } else {
            pageList.setList(new ArrayList<>());
        }
        return pageList;
    }

    public List<WaveUniqueCode> queryItemUniqueCodesByCodes(Staff staff, List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Collections.EMPTY_LIST;
        }
        return waveUniqueCodeDao.queryItemUniqueCodesByCodes(staff, uniqueCodes);
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodesByCodes(Staff staff, Boolean joinItem, List<String> uniqueCodes) {
        return waveUniqueCodeDao.queryItemUniqueCodesByCodes(staff, uniqueCodes, joinItem);
    }

    @Override
    public boolean openUniqueCode(Staff staff) {
        return uniqueCodeHelpBusiness.openUniqueCode(staff);
    }

    public void batchOut(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result) {
        List<WaveUniqueCode> exists;
        if (params.isDirectOut()) {
            exists = result.getData();
        } else {
            UniqueCodeRelationCheckVO vo = uniqueCodeBaseService.uniqueCodeRelationCheck(staff, null, null, params.getBusinessCodes(), result);
            if (BooleanUtils.isFalse(result.isSuccess())) {
                return;
            }
            exists = vo.getUniqueCodes();
        }

        // 更新唯一码
        outUpdateUniqueCode(staff, params, result, exists);
    }

    @Transactional
    public void outUpdateUniqueCode(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result, List<WaveUniqueCode> exists) {
        if (BooleanUtils.isFalse(result.isSuccess())) {
            return;
        }

        OutUniqueCodeVO vo;
        if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.TAKEN_ORDER.getType())) {
            vo = handleUniqueCodeRelation4Out(staff, params, result, exists);
        } else {
            vo = handleUniqueCode4Out(staff, params, result, exists);
        }

        if (CollectionUtils.isNotEmpty(vo.getUpdateCodes())) {
            waveUniqueCodeDao.batchUpdate(staff, vo.getUpdateCodes());
        }

        if (CollectionUtils.isNotEmpty(vo.getUniqueCodeRelations())) {
            uniqueCodeRelationDao.batchUpdateByCode(staff, vo.getUniqueCodeRelations());
        }

        if (CollectionUtils.isNotEmpty(vo.getUpdateCodeLogs())) {
            // PC其他出库单出库，唯一码状态更新：在库/已收货/已拣选 → 已出库
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.trace").setArgs(new Object[]{staff, vo.getUpdateCodeLogs(), getOpType4Out(params.getBusinessType(), params.getSubBusinessType()), null, null, new WaveUniqueCodeLogUtils.WaveUniqueCodeLogOtherInfo().setLogTemplate(StringUtils.isNotEmpty(params.getLogTemplate()) ? params.getLogTemplate() : getLogTemplate(params.getBusinessType()))}), null);
        }

        result.setData(exists);
    }

    private OutUniqueCodeVO handleUniqueCode4Out(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result, List<WaveUniqueCode> exists) {
        List<WaveUniqueCode> updateCodes = Lists.newArrayList();
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> updateCodeLogs = Lists.newArrayList();
        Map<String, String> relationCodeMap = params.getItemParams().stream().filter(p -> StringUtils.isNotEmpty(p.getBusinessCode())).collect(Collectors.toMap(UniqueCodeGenericParams.ItemGenericParams::getUniqueCode, UniqueCodeGenericParams.ItemGenericParams::getBusinessCode, (a, b) -> a));
        Map<String, Long> warehouseIdMap = params.getItemParams().stream().filter(p -> DataUtils.checkLongNotEmpty(p.getWarehouseId())).collect(Collectors.toMap(UniqueCodeGenericParams.ItemGenericParams::getUniqueCode, UniqueCodeGenericParams.ItemGenericParams::getWarehouseId, (a, b) -> a));
        Map<Long, String> warehouseNameMap = uniqueCodeBaseService.getWarehouseNameMap(params, exists);
        Map<String, String> goodsSectionCodeMap = getGoodsSectionCodeMap(params);
        for (WaveUniqueCode exist : exists) {
            WaveUniqueCode update = new WaveUniqueCode();
            update.setId(exist.getId());
            update.setStatus(getStatus(params.getBusinessType()));
            if (Objects.equals(params.getBusinessType(), UniqueCodeRelation.BusinessType.PURCHASE_RETURN_ORDER.getType())) {
                // 采退单出库，更新收货状态，方便再次收货
                update.setReceiveStatus(0);
            }
            update.setGoodsSectionCode("");
            update.setGoodsSectionId(0L);
            update.setStockPosition(UniqueCodeStockPositionEnum.DEFAULT.getType());
            update.setWarehouseId(warehouseIdMap.get(exist.getUniqueCode()));
            updateCodes.add(update);

            WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO updateCodeLog = new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO()
                    .setUniqueCode(exist.getUniqueCode())
                    .setOldStatus(exist.getStatus())
                    .setGoodsSectionCode(goodsSectionCodeMap.get(exist.getUniqueCode()))
                    .setBeforeWarehouseName(warehouseNameMap.get(exist.getWarehouseId()))
                    .setWarehouseName(warehouseNameMap.get(warehouseIdMap.get(exist.getUniqueCode())))
                    .setBusinessCode(relationCodeMap.get(exist.getUniqueCode()));
            updateCodeLogs.add(updateCodeLog);
        }

        return new OutUniqueCodeVO().setUpdateCodes(updateCodes).setUpdateCodeLogs(updateCodeLogs);
    }

    private OutUniqueCodeVO handleUniqueCodeRelation4Out(Staff staff, UniqueCodeGenericParams params, UniqueCodeResult result, List<WaveUniqueCode> exists) {
        List<UniqueCodeRelation> uniqueCodeRelations = Lists.newArrayList();
        List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> updateCodeLogs = Lists.newArrayList();
        Map<String, String> relationCodeMap = params.getItemParams().stream().filter(p -> StringUtils.isNotEmpty(p.getBusinessCode())).collect(Collectors.toMap(UniqueCodeGenericParams.ItemGenericParams::getUniqueCode, UniqueCodeGenericParams.ItemGenericParams::getBusinessCode, (a, b) -> a));

        for (WaveUniqueCode exist : exists) {
            String businessCode;
            if ((businessCode = relationCodeMap.get(exist.getUniqueCode())) == null) {
                continue;
            }
            UniqueCodeRelation updateRelation = new UniqueCodeRelation();
            updateRelation.setUniqueCode(exist.getUniqueCode());
            updateRelation.setBusinessCode(businessCode);
            updateRelation.setStatus(20);
            uniqueCodeRelations.add(updateRelation);

            WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO updateCodeLog = new WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO()
                    .setUniqueCode(exist.getUniqueCode());
            updateCodeLogs.add(updateCodeLog);
        }

        return new OutUniqueCodeVO().setUniqueCodeRelations(uniqueCodeRelations).setUpdateCodeLogs(updateCodeLogs);
    }

    @Data
    @Accessors(chain = true)
    class OutUniqueCodeVO {
        private List<WaveUniqueCode> updateCodes = Lists.newArrayList();
        private List<WaveUniqueCodeLogUtils.WaveUniqueCodeLogDTO> updateCodeLogs = Lists.newArrayList();
        private List<UniqueCodeRelation> uniqueCodeRelations = Lists.newArrayList();
    }

    private Map<String, String> getGoodsSectionCodeMap(UniqueCodeGenericParams params) {
        if (!Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_AND_OUT.getType()) &&
                !Objects.equals(params.getSubBusinessType(), UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_ESTABLISH_RELATION_AND_OUT.getType())) {
            return Maps.newHashMap();
        }
        return params.getItemParams().stream().filter(p -> StringUtils.isNotEmpty(p.getGoodsSectionCode())).collect(Collectors.toMap(UniqueCodeGenericParams.ItemGenericParams::getUniqueCode, UniqueCodeGenericParams.ItemGenericParams::getGoodsSectionCode, (a, b) -> a));
    }

    private WaveUniqueOpType getOpType4Out(Integer businessType, Integer subBusinessType) {
        if (Objects.equals(businessType, UniqueCodeRelation.BusinessType.PURCHASE_RETURN_ORDER.getType())) {
            return WaveUniqueOpType.PURCHASE_RETURN_OUT;
        }
        if (Objects.equals(businessType, UniqueCodeRelation.BusinessType.ALLOT.getType())) {
            return WaveUniqueOpType.ALLOCATE_OUT;
        }
        if (Objects.equals(businessType, UniqueCodeRelation.BusinessType.TAKEN_ORDER.getType())) {
            return WaveUniqueOpType.TAKEN_ORDER_OUT;
        }
        if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_AND_OUT.getType()) ||
                Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.OFF_SHELF_ESTABLISH_RELATION_AND_OUT.getType())) {
            return WaveUniqueOpType.OFF_SHELF_AND_OUT;
        }
        if (Objects.equals(subBusinessType, UniqueCodeGenericParams.SubBusinessType.REPAIR_ORDER_OUT.getType()) ) {
            return WaveUniqueOpType.REPAIR_ORDER_OUT;
        }
        return WaveUniqueOpType.OTHER_OUT;
    }

    private Integer getStatus(Integer businessType) {
        if (Objects.equals(businessType, UniqueCodeRelation.BusinessType.PURCHASE_RETURN_ORDER.getType())) {
            return OrderUniqueCodeStatusEnum.PURCHASE_RETURN.getType();
        }
        if (Objects.equals(businessType, UniqueCodeRelation.BusinessType.ALLOT.getType())) {
            return OrderUniqueCodeStatusEnum.WAIT_IN.getType();
        }
        return OrderUniqueCodeStatusEnum.OUT.getType();
    }

    private String getLogTemplate(Integer businessType) {
        if (Objects.equals(businessType, 4)) {
            String operateSource = "PDA".equalsIgnoreCase(OperateSourceContext.acquireOperateSourceNew().getProject()) ? "PDA" : "PC";
            return operateSource + UniqueCodeRelation.BusinessType.getDesc(businessType) + "出库，唯一码状态更新：%s->%s";
        }
        return "";
    }

    /**
     *  -------- 原代码在PackBusiness中，迁移
     */
    private void exitUniqueCodeItem(Staff staff, TradeConfig tradeConfig, Long sid, List<String> uniqueCodes) {
        //校验扫描的强唯一码
        TradeStaffConfig tradeStaffConfig = tradeServiceDubbo.queryTradeStaffConfig(staff);
        List<WaveUniqueCode> codeList = packScanItemUniqueCodeInfoNew(staff, uniqueCodes, sid);
        List<WaveUniqueCode> uniqueCodeList = codeList.stream().filter(t -> Objects.equals(CommonConstants.VALUE_YES, t.getItemUniqueCodeType())).collect(Collectors.toList());
        Trade trade = waveUseTradeServiceProxy.queryBySids(staff, true, sid).get(0);
        //校验订单与唯一码仓库匹配
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("包装验货扫描唯一码，uniqueCodes=%s", uniqueCodes)));
        }
        List<Order> orders = isSellerSendOrFinish(trade.getSysStatus()) ? WaveUtils.getCanSortingOrders(trade, true) : WaveUtils.getCanSortingOrders(trade);
        List<Long> sysItemIds = orders.stream().map(Order::getItemSysId).distinct().collect(toList());
        List<Long> sysSkuIds = orders.stream().map(Order::getSkuSysId).distinct().collect(toList());
        List<String> outerIds = checkExitUniqueCodeItem(staff, sysItemIds, sysSkuIds);
        checkUniqueCode(uniqueCodeList, sysItemIds, sysSkuIds);
        Map<String, List<WaveUniqueCode>> uniqueCodeMap = uniqueCodeList.stream().collect(groupingBy(WaveUniqueCode::getOuterId, LinkedHashMap::new, toList()));
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(outerIds)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("包装验货订单中包含的强唯一码商品，outerIds=%s", outerIds)));
            }
            //订单强唯一码数量 = 强唯一码商品 * 数量
            Integer num = 0;
            for (Order order : mergeOrdersByOuterId(orders)) {
                if (outerIds.contains(order.getSysOuterId())) {
                    num += order.getNum();
                    List<WaveUniqueCode> codes = uniqueCodeMap.get(order.getSysOuterId());
                    if (org.apache.commons.collections.CollectionUtils.isEmpty(codes) || !Objects.equals(codes.size(), order.getNum())) {
                        throw new IllegalArgumentException(String.format("强唯一码数量和强唯一码商品[%s]数量不相等!", order.getSysOuterId()));
                    }
                }
            }
            if (!Objects.equals(uniqueCodeList.size(), num)) {
                throw new IllegalArgumentException("强唯一码数量和强唯一码商品数量不相等!");
            }
            if (CommonConstants.VALUE_YES.equals(tradeStaffConfig.getOpenForceTradePack()) && org.apache.commons.collections.CollectionUtils.isEmpty(uniqueCodes)) {
                throw new IllegalArgumentException("订单包含强唯一码商品，不允许强制验货! ");
            }
            if (org.apache.commons.collections.CollectionUtils.isEmpty(uniqueCodes)) {
                throw new IllegalArgumentException("订单包含强唯一码商品，不允许强制完成! ");
            }
        }
    }

    /**
     * 找出包含的唯一码商品
     *-------- 原代码在PackBusiness中，迁移
     *
     * @param staff
     * @param sysItemIds
     * @param sysSkuIds
     * @return
     */
    @Override
    public List<String> checkExitUniqueCodeItem(Staff staff, List<Long> sysItemIds, List<Long> sysSkuIds) {
        //唯一码商品
        List<String> outerIds = Lists.newArrayList();
        QueryItemDetailParams params = new QueryItemDetailParams();
        params.setNeedSuitSingleOrNot(true);
        params.setSysItemIdList(sysItemIds);
        params.setSysSkuIdList(sysSkuIds);
        List<DmjItem> dmjItems = itemServiceDubbo.queryItemDetail(staff, params);
        for (DmjItem dmjItem : dmjItems) {
            //判断是否有sku
            //当唯一码类型为强唯一码时,则判定该商品为唯一码商品
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dmjItem.getSkus())) {
                for (DmjSku dmjSku : dmjItem.getSkus()) {
                    if (CommonConstants.VALUE_YES.equals(dmjSku.getUniqueCodeType())) {
                        outerIds.add(dmjSku.getOuterId());
                    }
                }
            } else {
                if (CommonConstants.VALUE_YES.equals(dmjItem.getUniqueCodeType())) {
                    outerIds.add(dmjItem.getOuterId());
                }
            }
        }
        return outerIds;
    }

    @Override
    public Map<Integer,List<String>> uniqueCodeTypeMap(Staff staff, List<Long> sysItemIds, List<Long> sysSkuIds) {
        Map<Integer,List<String>> result = new HashMap<>();
        //唯一码商品
        QueryItemDetailParams params = new QueryItemDetailParams();
        params.setNeedSuitSingleOrNot(true);
        params.setSysItemIdList(sysItemIds);
        params.setSysSkuIdList(sysSkuIds);
        List<DmjItem> dmjItems = itemServiceDubbo.queryItemDetail(staff, params);
        for (DmjItem dmjItem : dmjItems) {
            //判断是否有sku
            //当唯一码类型为强唯一码时,则判定该商品为唯一码商品
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dmjItem.getSkus())) {
                for (DmjSku dmjSku : dmjItem.getSkus()) {
                    if (CommonConstants.VALUE_YES.equals(dmjSku.getUniqueCodeType())) {
                        result.computeIfAbsent(CommonConstants.VALUE_YES,v->new ArrayList<>()).add(dmjSku.getOuterId());
                    } else if (CommonConstants.VALUE_NO.equals(dmjSku.getUniqueCodeType())) {
                        result.computeIfAbsent(CommonConstants.VALUE_NO,v->new ArrayList<>()).add(dmjSku.getOuterId());
                    }else {
                        result.computeIfAbsent(UNIVERSAL_CODE_TYPE,v->new ArrayList<>()).add(dmjSku.getOuterId());
                    }
                }
            } else {
                if (CommonConstants.VALUE_YES.equals(dmjItem.getUniqueCodeType())) {
                    result.computeIfAbsent(CommonConstants.VALUE_YES,v->new ArrayList<>()).add(dmjItem.getOuterId());
                } else if (CommonConstants.VALUE_NO.equals(dmjItem.getUniqueCodeType())) {
                    result.computeIfAbsent(CommonConstants.VALUE_NO,v->new ArrayList<>()).add(dmjItem.getOuterId());
                }else {
                    result.computeIfAbsent(UNIVERSAL_CODE_TYPE,v->new ArrayList<>()).add(dmjItem.getOuterId());
                }
            }
        }
        return result;
    }

    private boolean isSellerSendOrFinish(String sysStatus) {
        return Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus) || Trade.SYS_STATUS_FINISHED.equals(sysStatus);
    }


    /**
     * 校验唯一码商品是否属于订单商品
     * -------- 原代码在PackBusiness中，迁移
     * @param uniqueCodes
     * @param sysItemIds
     * @param sysSkuIds
     */
    public void checkUniqueCode(List<WaveUniqueCode> uniqueCodes, List<Long> sysItemIds, List<Long> sysSkuIds) {
        for (WaveUniqueCode uniqueCode : uniqueCodes) {
            if (com.raycloud.dmj.domain.utils.DataUtils.checkLongNotEmpty(uniqueCode.getSysSkuId())) {
                if (!sysSkuIds.contains(uniqueCode.getSysSkuId())) {
                    throw new IllegalArgumentException(String.format("唯一码[%s]对应的sku商品不属于该订单商品! ", uniqueCode.getUniqueCode()));
                }
            } else {
                if (!sysItemIds.contains(uniqueCode.getSysItemId())) {
                    throw new IllegalArgumentException(String.format("唯一码[%s]对应的纯商品不属于该订单商品! ", uniqueCode.getUniqueCode()));
                }
            }
        }
    }

    /**
     * 根据商家编码合并order,过滤套件本身
     * -------- 原代码在PackBusiness中，迁移
     * @param orders
     * @return
     */
    private static List<Order> mergeOrdersByOuterId(List<Order> orders) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }

        Map<String, Order> mergeOrderMap = Maps.newHashMap();
        for (Order order : orders) {
            Order existOrder = mergeOrderMap.get(order.getSysOuterId());
            if (existOrder != null) {
                existOrder.setNum(existOrder.getNum() + order.getNum());
            } else {
                mergeOrderMap.put(order.getSysOuterId(), order);
            }
        }
        return new ArrayList<>(mergeOrderMap.values());
    }

    @Override
    public void bindItemUniqueCode(Staff staff, Long sid, TradeConfig tradeConfig, List<String> uniqueCodes, boolean packSellerSend) {
        bindItemUniqueCode(staff, sid, tradeConfig, uniqueCodes, packSellerSend, null);
    }

    @Override
    public void bindItemUniqueCode(Staff staff, Long sid, TradeConfig tradeConfig, List<String> uniqueCodes, boolean packSellerSend, Integer openPackAutoReceiveGoods) {
        //判断订单下是否包含唯一码商品-校验配置
        exitUniqueCodeItem(staff, tradeConfig, sid, uniqueCodes);
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return;
        }
        //唯一码后置绑定处理
        examine(staff, sid, uniqueCodes, false, packSellerSend, 2, openPackAutoReceiveGoods);
    }

    /**
     * 校验订单下非强唯一码商品验货数量
     * -------- 原代码在PackBusiness中，迁移
     */
    @Override
    public void checkOrderItemNum(Staff staff, TradePackParams params) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(params.getOrders())) {
            throw new IllegalArgumentException("子订单信息不能为空! ");
        }
        List<Order> orders = params.getOrders();
        //获取订单中强唯一码商品
        List<String> outerIds = checkExitUniqueCodeItem(staff,
                orders.stream().map(Order::getItemSysId).distinct().collect(toList()),
                orders.stream().map(Order::getSkuSysId).distinct().collect(toList()));
        Map<String, Integer> itemNumMap = orders.stream().collect(groupingBy(Order::getSysOuterId, summingInt(Order::getNum)));
        Map<String, Integer> scanInfoMap = params.getPackScanInfos().stream().collect(groupingBy(TradePackScanInfo::getScanCode, summingInt(TradePackScanInfo::getNum)));
        //组装强制验货扫描数量
        List<Order> orderList = new ArrayList<>();
        //允许强制验货,数量非强校验
        if (params.isCanForce()) {
            //组装订单商品扫描数量
            List<TradePackScanInfo> packScanInfos = new ArrayList<>();
            for (Order order : params.getOrders()) {
                String outerId = order.getSysOuterId();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(params.getPackScanInfos())) {
                    if (outerIds.contains(outerId)) {
                        orderList.add(TradeUtils.identCode2Order(order.getId(), "", order.getNum()));
                        continue;
                    }
                    //计算子订单的强制验货数
                    if (scanInfoMap.getOrDefault(outerId, CommonConstants.VALUE_NO) >= order.getNum() ) {
                        orderList.add(TradeUtils.identCode2Order(order.getId(), "", CommonConstants.VALUE_NO));
                        scanInfoMap.put(outerId, scanInfoMap.getOrDefault(outerId, CommonConstants.VALUE_NO) - order.getNum());
                        packScanInfos.add(TradePackScanInfo.buildTradePackScanInfo(null, order.getId(), order.getNum(), TradePackScanInfo.ScanCodeType.NORMAL.getType(), outerId));
                    } else {
                        orderList.add(TradeUtils.identCode2Order(order.getId(), "", order.getNum() - scanInfoMap.getOrDefault(outerId, CommonConstants.VALUE_NO)));
                        if (scanInfoMap.getOrDefault(outerId, CommonConstants.VALUE_NO) > CommonConstants.VALUE_NO) {
                            packScanInfos.add(TradePackScanInfo.buildTradePackScanInfo(null, order.getId(), scanInfoMap.get(outerId), TradePackScanInfo.ScanCodeType.NORMAL.getType(), outerId));
                            packScanInfos.add(TradePackScanInfo.buildTradePackScanInfo(null, order.getId(), order.getNum() - scanInfoMap.get(outerId), 99, outerId));
                        } else {
                            packScanInfos.add(TradePackScanInfo.buildTradePackScanInfo(null, order.getId(), order.getNum(), 99, outerId));
                        }
                    }
                } else {
                    orderList.add(TradeUtils.identCode2Order(order.getId(), "", outerIds.contains(outerId) ? CommonConstants.VALUE_NO : order.getNum()));
                }
            }
            params.setPackScanInfos(packScanInfos);
        } else {
            List<String> errorItem = new ArrayList<>();
            Map<String, Integer> scanOrderInfoMap = params.getPackScanInfos().stream().collect(groupingBy(TradePackScanInfo::getScanCode, summingInt(TradePackScanInfo::getNum)));
            for (Map.Entry<String, Integer> entry : itemNumMap.entrySet()) {
                if (!outerIds.contains(entry.getKey())) {
                    if (!Objects.equals(entry.getValue(), scanOrderInfoMap.get(entry.getKey()))) {
                        errorItem.add(entry.getKey());
                    }
                }
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(errorItem)) {
                throw new IllegalArgumentException(String.format("未开启强制验货,商品:%s 验货数量与订单不一致! ", errorItem.toString()));
            }
            params.setPackScanInfos(convertTradePackScanInfo(params));
        }
        params.setOrders(orderList);
    }

    /**
     * -------- 原代码在PackBusiness中，迁移
     */
    public List<TradePackScanInfo> convertTradePackScanInfo(TradePackParams params) {
        List<Order> orders = params.getOrders();
        List<TradePackScanInfo> tradePackScanInfos = params.getPackScanInfos();
        List<TradePackScanInfo> list = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orders) || org.apache.commons.collections.CollectionUtils.isEmpty(tradePackScanInfos)) {
            return list;
        }
        for (Order order : orders) {
            list.add(TradePackScanInfo.buildTradePackScanInfo(null, order.getId(), order.getNum(), TradePackScanInfo.ScanCodeType.NORMAL.getType(), order.getOuterId()));
        }

        return list;
    }

    @Override
    public List<WaveUniqueCode> matchOneByOuterId4FastConsign(Staff staff, ItemUniqueCodeQueryParams params) {
        return waveUniqueCodeDao.matchOneByOuterId4FastConsign(staff, params);
    }

    @Override
    public boolean currentMatchedAll(Staff staff, String uniqueCode) {
        if (uniqueCode == null) {
            return true;
        }

        List<WaveUniqueCode> codes = queryByUniqueCodes(staff, Lists.newArrayList(uniqueCode));
        Assert.notEmpty(codes, "未查询到唯一码：" + uniqueCode + "信息！");
        WaveUniqueCode code = codes.get(0);
        Assert.isTrue(DataUtils.checkLongNotEmpty(code.getSid()), uniqueCode + "未绑定订单！");
        // 单件直接出单
        if (Objects.equals(1, code.getCodeType())) {
            return true;
        }

        ItemUniqueCodeQueryParams queryParams = new ItemUniqueCodeQueryParams();
        queryParams.setSids(Lists.newArrayList(code.getSid().toString()));
        queryParams.setNotStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.CANCEL.getType()));
        List<WaveUniqueCode> tradeAllCodes = queryItemUniqueCodeByCondition(staff, queryParams);
        Assert.notEmpty(tradeAllCodes, "根据单号：" + code.getSid() + "未查询到唯一码信息！");
        return tradeAllCodes.stream().allMatch(c -> Objects.equals(c.getMatchedStatus(), WaveSorting.MATCHED_STATUS_OVER));
    }

    @Override
    public List<WaveUniqueCode> matchBatchByOuterId4FastConsign(Staff staff, ItemUniqueCodeQueryParams params) {
        return waveUniqueCodeDao.matchBatchByOuterId4FastConsign(staff, params);
    }

    @Override
    public List<Long> querySids4MatchMutli(Staff staff, ItemUniqueCodeQueryParams queryParams) {
        return waveUniqueCodeDao.querySids4MatchMutli(staff, queryParams);
    }

    @Override
    public List<WaveUniqueCode> matchUnboundUniqueCode4FastConsign(Staff staff, ItemUniqueCodeQueryParams params) {
        return waveUniqueCodeDao.matchUnboundUniqueCode4FastConsign(staff, params);
    }
}

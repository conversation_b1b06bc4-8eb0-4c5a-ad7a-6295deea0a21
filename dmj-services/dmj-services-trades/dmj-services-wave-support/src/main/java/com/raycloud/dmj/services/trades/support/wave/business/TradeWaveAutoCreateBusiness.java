package com.raycloud.dmj.services.trades.support.wave.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.raycloud.dmj.dao.wave.WaveQueryConditionDao;
import com.raycloud.dmj.dao.wave.WaveRuleGroupDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wave.WaveAutoCreateRule;
import com.raycloud.dmj.domain.wave.WaveQueryCondition;
import com.raycloud.dmj.domain.wave.WaveRuleGroup;
import com.raycloud.dmj.domain.wave.dto.WaveAutoCreateRuleDTO;
import com.raycloud.dmj.domain.wave.enums.AutoWaveTypeEnum;
import com.raycloud.dmj.domain.wave.vo.WaveAutoCreateRuleNewVO;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @ClassName TradeWaveAutoCreateBusiness.java
 * @Description 自动生成波次业务
 * @createTime 2023年06月07日 16:54:00
 */
@Service
public class TradeWaveAutoCreateBusiness {
    private final Logger logger = Logger.getLogger(TradeWaveAutoCreateBusiness.class);

    @Resource
    private WaveQueryConditionDao waveQueryConditionDao;
    @Resource
    private WaveRuleGroupDao waveRuleGroupDao;
    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;


    public WaveAutoCreateRuleNewVO queryWaveAutoCreateRule(Staff staff, Integer autoWaveType) {
        autoWaveType = Optional.ofNullable(autoWaveType).orElse(AutoWaveTypeEnum.LaborAuto.getType());
        WaveQueryCondition condition = new WaveQueryCondition();
        condition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
        condition.setGroupId(Long.valueOf(autoWaveType));
        List<WaveQueryCondition> conditions = waveQueryConditionDao.queryListPage(staff, condition, null);

        // 初始化
        if (CollectionUtils.isEmpty(conditions)) {
            conditions = initWaveAutoCreateRule(staff, autoWaveType, condition);
        }
        // 过滤已删除的数据
        conditions.removeIf(c -> Objects.equal(c.getEnableStatus(), CommonConstants.JUDGE_NO));
        return new WaveAutoCreateRuleNewVO()
                .setAutoWaveType(autoWaveType)
                .setRuleList(convert2WaveAutoCreateRuleDTO(staff, conditions));
    }

    public List<WaveAutoCreateRuleDTO> convert2WaveAutoCreateRuleDTO(Staff staff, List<WaveQueryCondition> conditions) {
        List<WaveAutoCreateRuleDTO> dtos = Lists.newArrayList();
        Set<Long> allGroupIds = Sets.newHashSet();
        for (WaveQueryCondition waveQueryCondition : conditions) {
            WaveAutoCreateRuleDTO dto = new WaveAutoCreateRuleDTO();
            dto.setId(waveQueryCondition.getId());
            dto.setName(waveQueryCondition.getName());
            dto.setOrderNo(waveQueryCondition.getOrderNo());
            dto.setRule(JSONObject.parseObject(waveQueryCondition.getContent(), WaveAutoCreateRule.class));
            WaveAutoCreateRule rule = dto.getRule();
            List<Long> groupIds = rule.getGroupIds();
            if (CollectionUtils.isNotEmpty(groupIds)) {
                allGroupIds.addAll(groupIds);
            }
            dtos.add(dto);
        }
        if (CollectionUtils.isEmpty(allGroupIds)) {
            return dtos;
        }
        Map<Long, String> id2NameMap = waveRuleGroupDao.queryByIds(staff, Lists.newArrayList(allGroupIds)).stream().collect(Collectors.toMap(WaveRuleGroup::getId, WaveRuleGroup::getName));
        for (WaveAutoCreateRuleDTO dto : dtos) {
            WaveAutoCreateRule rule = dto.getRule();
            List<Long> groupIds = rule.getGroupIds();
            List<WaveRuleGroup> waveRuleGroupList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(groupIds)) {
                for (Long groupId : groupIds) {
                    String groupName = id2NameMap.get(groupId);
                    if (StringUtils.isEmpty(groupName)) {
                        continue;
                    }
                    WaveRuleGroup waveRuleGroup = new WaveRuleGroup();
                    waveRuleGroup.setId(groupId);
                    waveRuleGroup.setName(groupName);
                    waveRuleGroupList.add(waveRuleGroup);
                }
                rule.setGroups(waveRuleGroupList);
            }
        }
        return dtos;
    }

    @Transactional
    public void saveWaveAutoCreateRule(Staff staff, WaveAutoCreateRuleDTO waveAutoCreateRuleDTO) {
        Integer autoWaveType = waveAutoCreateRuleDTO.getAutoWaveType();
        WaveAutoCreateRule waveAutoCreateRule = waveAutoCreateRuleDTO.getRule();
        Long id = waveAutoCreateRuleDTO.getId();
        String name = waveAutoCreateRuleDTO.getName();
        Integer orderNo = waveAutoCreateRuleDTO.getOrderNo();
        check(staff, waveAutoCreateRuleDTO);
        if (id == null) {
            WaveQueryCondition waveQueryCondition = new WaveQueryCondition();
            waveQueryCondition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
            waveQueryCondition.setGroupId(Long.valueOf(autoWaveType));
            waveQueryCondition.setOrderNo(waveAutoCreateRuleDTO.getOrderNo());
            waveQueryCondition.setName(name);
            waveQueryCondition.setContent(JSON.toJSONString(waveAutoCreateRule));
            waveQueryCondition.setCompanyId(staff.getCompanyId());
            waveQueryCondition.setCreated(new Date());
            waveQueryCondition.setEnableStatus(CommonConstants.ENABLE_STATUS_NORMARL);
            waveQueryConditionDao.insert(staff, waveQueryCondition);
        } else {
            WaveQueryCondition update = new WaveQueryCondition();
            update.setId(id);
            update.setOrderNo(waveAutoCreateRuleDTO.getOrderNo());
            update.setName(name);
            update.setContent(JSON.toJSONString(waveAutoCreateRule));
            waveQueryConditionDao.update(staff, update);
        }

        if (autoWaveType != null) {
            logger.debug(LogHelper.buildLog(staff, "update autoWaveType:" + autoWaveType));
            tradeServiceDubbo.updateConfig4Wave(staff, "auto_create_wave", autoWaveType);
        }
    }

    private void check(Staff staff, WaveAutoCreateRuleDTO waveAutoCreateRuleDTO) {
        Long id = waveAutoCreateRuleDTO.getId();
        String name = waveAutoCreateRuleDTO.getName();
        Integer orderNo = waveAutoCreateRuleDTO.getOrderNo();
        Integer autoWaveType = waveAutoCreateRuleDTO.getAutoWaveType();
        if (id == null) {
            Assert.notNull(orderNo, "优先级不能为空");
            Assert.notNull(autoWaveType, "生成规则不能为空");
            Assert.isTrue(StringUtils.isNotEmpty(name), "自动生成波次规则名称不能为空");
            WaveQueryCondition nameCondition = new WaveQueryCondition();
            nameCondition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
            nameCondition.setGroupId(Long.valueOf(autoWaveType));
            nameCondition.setName(name);
            nameCondition.setEnableStatus(CommonConstants.JUDGE_YES);
            WaveQueryCondition data = waveQueryConditionDao.queryOne(staff, nameCondition);
            Assert.isNull(data, "自动生成波次规则名称不允许重复");

            WaveQueryCondition orderNoCondition = new WaveQueryCondition();
            orderNoCondition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
            orderNoCondition.setGroupId(Long.valueOf(autoWaveType));
            orderNoCondition.setOrderNo(orderNo);
            orderNoCondition.setEnableStatus(CommonConstants.JUDGE_YES);
            data = waveQueryConditionDao.queryOne(staff, orderNoCondition);
            Assert.isNull(data, "优先级" + orderNo + "不允许重复");
        } else {

            WaveQueryCondition dbCondition = new WaveQueryCondition();
            dbCondition.setId(id);
            WaveQueryCondition dbData = waveQueryConditionDao.queryOne(staff, dbCondition);
            Assert.notNull(dbData, "id不存在");
            autoWaveType = dbData.getGroupId().intValue();
            // 防止数据被覆盖
            WaveAutoCreateRule dbWaveAutoCreateRule = JSONObject.parseObject(dbData.getContent(), WaveAutoCreateRule.class);
            WaveAutoCreateRule newWaveAutoCreateRule = waveAutoCreateRuleDTO.getRule();
            if (newWaveAutoCreateRule.getLastAutoCreateWaveTime() == null) {
                newWaveAutoCreateRule.setLastAutoCreateWaveTime(dbWaveAutoCreateRule.getLastAutoCreateWaveTime());
            }
            if (newWaveAutoCreateRule.getCronTime() == null) {
                newWaveAutoCreateRule.setCronTime(dbWaveAutoCreateRule.getCronTime());
            }

            if (StringUtils.isNotEmpty(name)) {
                WaveQueryCondition nameCondition = new WaveQueryCondition();
                nameCondition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
                nameCondition.setGroupId(Long.valueOf(autoWaveType));
                nameCondition.setName(name);
                nameCondition.setNotId(id);
                nameCondition.setEnableStatus(CommonConstants.JUDGE_YES);
                WaveQueryCondition data = waveQueryConditionDao.queryOne(staff, nameCondition);
                Assert.isNull(data, "自动生成波次规则名称不允许重复");
            }
            if (orderNo != null) {
                WaveQueryCondition orderNoCondition = new WaveQueryCondition();
                orderNoCondition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
                orderNoCondition.setGroupId(Long.valueOf(autoWaveType));
                orderNoCondition.setOrderNo(orderNo);
                orderNoCondition.setEnableStatus(CommonConstants.JUDGE_YES);
                orderNoCondition.setNotId(id);
                WaveQueryCondition data = waveQueryConditionDao.queryOne(staff, orderNoCondition);
                Assert.isNull(data, "优先级" + orderNo + "不允许重复");
            }
        }
    }


    private List<WaveQueryCondition> initWaveAutoCreateRule(Staff staff, Integer autoWaveType, WaveQueryCondition condition) {
        List<WaveQueryCondition> conditions = Lists.newArrayList();
        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        if (autoWaveType == null || autoWaveType == AutoWaveTypeEnum.CloseAuto.getType()) {
            return conditions;
        }
        switch (AutoWaveTypeEnum.getByType(autoWaveType)) {
            case LaborAuto:
                List<WaveRuleGroup> result = waveRuleGroupDao.queryList(staff, new WaveRuleGroup())
                        .stream().filter(w -> StringUtils.isNotEmpty(w.getExtend())).collect(toList());
                Integer count = 1;

                if (CollectionUtils.isNotEmpty(result)) {
                    // 相同劳动力合并
                    Map<Pair<Integer, Integer>, List<WaveRuleGroup>> key2GroupsMap = result.stream()
                            .collect(groupingBy(group -> Pair.of((Integer) group.getExtend("wavePoolCountMin"), (Integer) group.getExtend("wavePoolCountMax"))));
                    List<WaveQueryCondition> waveQueryConditionList = Lists.newArrayList();
                    for (Map.Entry<Pair<Integer, Integer>, List<WaveRuleGroup>> entry : key2GroupsMap.entrySet()) {
                        List<Long> groupIds = entry.getValue().stream().map(WaveRuleGroup::getId).collect(toList());
                        Pair<Integer, Integer> key = entry.getKey();
                        WaveQueryCondition waveQueryCondition = new WaveQueryCondition();
                        waveQueryCondition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
                        waveQueryCondition.setGroupId(Long.valueOf(autoWaveType));
                        waveQueryCondition.setOrderNo(count);
                        waveQueryCondition.setName("默认自动生成波次规则名称" + count++);
                        WaveAutoCreateRule waveAutoCreateRule = new WaveAutoCreateRule();
                        waveAutoCreateRule.setWavePoolCountMax(key.getValue());
                        waveAutoCreateRule.setWavePoolCountMin(key.getKey());
                        waveAutoCreateRule.setGroupIds(groupIds);
                        waveQueryCondition.setContent(JSON.toJSONString(waveAutoCreateRule));
                        waveQueryCondition.setCompanyId(staff.getCompanyId());
                        waveQueryCondition.setCreated(new Date());
                        waveQueryCondition.setEnableStatus(CommonConstants.ENABLE_STATUS_NORMARL);
                        waveQueryConditionList.add(waveQueryCondition);
                    }
                    waveQueryConditionDao.batchInsert(staff, waveQueryConditionList);
                }

                break;
            case TimedAuto:
                Integer autoCreateWaveTimeInterval = tradeConfig.getAutoCreateWaveTimeInterval();
                if (autoCreateWaveTimeInterval != null) {
                    WaveQueryCondition waveQueryCondition = new WaveQueryCondition();
                    waveQueryCondition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
                    waveQueryCondition.setGroupId(Long.valueOf(autoWaveType));
                    waveQueryCondition.setOrderNo(1);
                    waveQueryCondition.setName("默认自动生成波次规则名称1");
                    WaveAutoCreateRule waveAutoCreateRule = new WaveAutoCreateRule();
                    waveAutoCreateRule.setCronTime(autoCreateWaveTimeInterval);
                    waveQueryCondition.setContent(JSON.toJSONString(waveAutoCreateRule));
                    waveQueryCondition.setCompanyId(staff.getCompanyId());
                    waveQueryCondition.setCreated(new Date());
                    waveQueryCondition.setEnableStatus(CommonConstants.ENABLE_STATUS_NORMARL);
                    waveQueryConditionDao.insert(staff, waveQueryCondition);
                }
                break;
            case HourMinuteAuto:
                String waveAutoCreateTime = tradeConfig.getWaveAutoCreateTime();
                if (StringUtils.isNotEmpty(waveAutoCreateTime)) {
                    WaveQueryCondition waveQueryCondition = new WaveQueryCondition();
                    waveQueryCondition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
                    waveQueryCondition.setGroupId(Long.valueOf(autoWaveType));
                    waveQueryCondition.setOrderNo(1);
                    waveQueryCondition.setName("默认自动生成波次规则名称1");
                    WaveAutoCreateRule waveAutoCreateRule = new WaveAutoCreateRule();
                    waveAutoCreateRule.setWaveAutoCreateTime(waveAutoCreateTime);
                    waveQueryCondition.setContent(JSON.toJSONString(waveAutoCreateRule));
                    waveQueryCondition.setCompanyId(staff.getCompanyId());
                    waveQueryCondition.setCreated(new Date());
                    waveQueryCondition.setEnableStatus(CommonConstants.ENABLE_STATUS_NORMARL);
                    waveQueryConditionDao.insert(staff, waveQueryCondition);
                }
                break;
        }

        conditions = waveQueryConditionDao.queryListPage(staff, condition, null);
        return conditions;
    }

    @Transactional
    public void deleteWaveAutoCreateRule(Staff staff, Long id, boolean init, Integer type) {
        waveQueryConditionDao.delete(staff, id);

        // 重新删除初始化
        if (init) {
            WaveQueryCondition condition = new WaveQueryCondition();
            condition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
            condition.setEnableStatus(CommonConstants.JUDGE_YES);
            condition.setGroupId(type != null ? Long.valueOf(type) : null);
            List<WaveQueryCondition> conditions = waveQueryConditionDao.queryListPage(staff, condition, null);
            waveQueryConditionDao.batchDelete(staff, conditions.stream().map(WaveQueryCondition::getId).collect(toList()));

            initWaveAutoCreateRule(staff, type, condition);
        }
    }

    public List<WaveAutoCreateRuleDTO> getWaveAutoCreateRuleByGroupIds(Staff staff, List<Long> waveGroupIds, Integer autoWaveType) {
        List<WaveAutoCreateRuleDTO> dtos = Lists.newArrayList();
        WaveQueryCondition condition = new WaveQueryCondition();
        condition.setType(WaveQueryCondition.WAVE_AUTO_CREATE_RULE);
        condition.setGroupId(Long.valueOf(autoWaveType));
        List<WaveQueryCondition> conditions = waveQueryConditionDao.queryListPage(staff, condition, null);
        if (CollectionUtils.isEmpty(conditions)) {
            conditions = initWaveAutoCreateRule(staff, autoWaveType, condition);
        }
        // 过滤已删除的数据
        conditions.removeIf(c -> Objects.equal(c.getEnableStatus(), CommonConstants.JUDGE_NO));
        for (Long waveGroupId : waveGroupIds) {
            WaveAutoCreateRuleDTO waveAutoCreateRuleDTO = new WaveAutoCreateRuleDTO();
            for (WaveQueryCondition waveQueryCondition : conditions) {
                String content = waveQueryCondition.getContent();
                if (StringUtils.isEmpty(content)) {
                    continue;
                }
                WaveAutoCreateRule waveAutoCreateRule = JSONObject.parseObject(waveQueryCondition.getContent(), WaveAutoCreateRule.class);
                List<Long> groupIds = waveAutoCreateRule.getGroupIds();
                // 优先级前的优先
                if (CollectionUtils.isEmpty(groupIds) || groupIds.contains(waveGroupId)) {
                    waveAutoCreateRuleDTO.setSourceWaveGroupId(waveGroupId);
                    waveAutoCreateRuleDTO.setRule(waveAutoCreateRule);
                    waveAutoCreateRuleDTO.setId(waveQueryCondition.getId());
                    dtos.add(waveAutoCreateRuleDTO);
                    break;
                }
            }
        }
        return dtos;
    }
}

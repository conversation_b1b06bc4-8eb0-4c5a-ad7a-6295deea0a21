package com.raycloud.dmj.business.wave;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.dao.wave.WaveQueryDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.enums.WaveTradeSortEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wave.PdaConfigConvert;
import com.raycloud.dmj.domain.wave.Wave;
import com.raycloud.dmj.domain.wave.WaveConfig;
import com.raycloud.dmj.domain.wave.WaveCreateType;
import com.raycloud.dmj.domain.wave.WaveRuleCondition;
import com.raycloud.dmj.domain.wave.enums.WaveChatConfigsEnum;
import com.raycloud.dmj.domain.wave.model.WaveContext;
import com.raycloud.dmj.domain.wave.model.WaveFilterParams;
import com.raycloud.dmj.domain.wave.model.WaveProgressContext;
import com.raycloud.dmj.domain.wave.model.WaveFilterParams;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.enums.WmsConfigExtInfoEnum;
import com.raycloud.dmj.product.domain.OrderStockProduct;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.trades.ITradeWaveQueryService;
import com.raycloud.dmj.services.trades.wave.IWaveServiceDubbo;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.WavePickUtils;
import com.raycloud.dmj.services.wave.filter.SingleWaveMatchFilter;
import com.raycloud.dmj.services.wave.filter.StockRegionWaveMatchFilter;
import com.raycloud.dmj.services.wms.IWmsPdaDubboService;
import com.raycloud.dmj.services.wms.IWaveAllocateGoodsService;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.WmsKeyUtils;
import com.raycloud.dmj.utils.wms.WaveBackItemRangeStrategy;
import com.raycloud.dmj.utils.wms.WmsUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.wave.model.CrossBorderWaveConfig.CROSS_WAVE_TYPE;

/**
 * @Author: qingfeng
 * @Description: 波次配货逻辑
 * @Date: 2019-12-10 15:28
 */
@Service
public class WaveAllocateGoodsBusiness {
    private Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private WaveTradesQueryBusiness waveTradesQueryBusiness;

    @Resource
    private IWmsService wmsService;

    @Resource
    private WaveProgressBusiness waveProgressBusiness;

    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;

    @Resource
    private IWaveServiceDubbo waveServiceDubbo;

    @Resource
    private WaveQueryDao waveQueryDao;

    @Autowired(required = false)
    private IWmsPdaDubboService wmsPdaDubboService;

    @Autowired(required = false)
    private IWaveAllocateGoodsService waveAllocateGoodsService;

    /**
     * 波次订单配货
     * @param staff
     * @param queryWaveResult
     * @param filterTrades
     * @param allocateBefore true：先配货，后过滤订单；false：先过滤订单，后配货
     * @param progressData
     * @param waveRuleCondition
     * @return
     */
    public List<Trade> allocateGoodsStock(Staff staff, ITradeWaveQueryService.QueryWaveResult queryWaveResult,
                                          List<Trade> filterTrades, ProgressData progressData, Boolean allocateBefore,
                                          WaveRuleCondition waveRuleCondition, WaveFilterParams waveFilterParams) {
        return allocateBefore
                ? allocateGoodsStockInBefore(staff, queryWaveResult, progressData,waveFilterParams)
                : allocateGoodsStockInAfter(staff, queryWaveResult, filterTrades, waveRuleCondition,waveFilterParams);
    }

    /**
     * 忽略后配货配置
     * @param companyId
     * @return
     */
    public boolean isQueryIgnoreAfterAllocate(Long companyId) {
        try {
            String ignoreAfterAllocateIds = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getWave().getQueryIgnoreAfterAllocate();
            if (StringUtils.isNotEmpty(ignoreAfterAllocateIds) && ArrayUtils.toLongSet(ignoreAfterAllocateIds).contains(companyId)) {
                return true;
            }
        } catch (Exception e) {
            logger.error("获取波次忽略后配货配置失败！", e);
        }

        return false;
    }

    /**
     * 先配货，再按规则过滤订单
     * @param staff
     * @param queryWaveResult
     */
    private List<Trade> allocateGoodsStockInBefore(Staff staff, ITradeWaveQueryService.QueryWaveResult queryWaveResult, ProgressData progressData,WaveFilterParams waveFilterParams) {
        // 是否开启了先过滤后配货
        TradeConfig tradeConfig = getTradeConfig(staff, queryWaveResult.getTradeConfig());
        WaveConfig waveConfig = getWaveConfig(staff, queryWaveResult.getWaveConfig());
        if (WmsUtils.isNewWms(staff) && (waveConfig.getOpenRuleFirstThenAllocate() == null
                || waveConfig.getOpenRuleFirstThenAllocate() == 0)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "进入前配货逻辑"));
            }
            // 波次订单默认顺序 前配货
            List<Trade> trades = queryWaveResult.getTrades();
            if (CollectionUtils.isNotEmpty(trades)) {
                trades.sort(WaveTradeSortEnum.getEnumByCode(waveConfig.getInteger(WaveChatConfigsEnum.WAVE_TRADE_SORT.getKey())).getComparator());
            }
            long allocateStart = System.currentTimeMillis();
            // 会将配货数量不足的订单设置为：NOT_IN_WAVE_ID
            allocateGoodsStock0(staff, queryWaveResult, true, null,waveFilterParams);
            if (progressData != null) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("生成波次，配货完成 took= %s ms", (System.currentTimeMillis() - allocateStart))));
                }
                if (CollectionUtils.isNotEmpty(trades)) {
                    waveProgressBusiness.touchProgress(staff, null, progressData, 1, WaveProgressContext.build(trades.get(0).getWarehouseId()));
                }
            }
        }

        if (WmsUtils.isNewWms(staff) && Objects.equal(waveConfig.getOpenRuleFirstThenAllocate(), 1)) {
            initAllocateGoodsData(staff, queryWaveResult);
        }
        return null;
    }

    /**
     * 先按规则过滤订单，再配货
     * @param staff
     * @param queryWaveResult
     * @return
     */
    private List<Trade> allocateGoodsStockInAfter(Staff staff, ITradeWaveQueryService.QueryWaveResult queryWaveResult, List<Trade> filterTrades, WaveRuleCondition waveRuleCondition,WaveFilterParams waveFilterParams) {
        TradeConfig tradeConfig = getTradeConfig(staff, queryWaveResult.getTradeConfig());
        WaveConfig waveConfig = getWaveConfig(staff, queryWaveResult.getWaveConfig());
        if (CollectionUtils.isNotEmpty(filterTrades)
                && WmsUtils.isNewWms(staff)
                && waveConfig.getOpenRuleFirstThenAllocate() != null
                && waveConfig.getOpenRuleFirstThenAllocate() == 1) {
            // 缺货生成波次的订单是否需要生成配货记录
            setUnderstockedWaveGenerateAllocateGoodsRecord(staff, waveRuleCondition, waveConfig);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "进入后配货逻辑，缺货生成波次的订单是否需要生成配货记录：" + (waveRuleCondition == null ? "0" : waveRuleCondition.isUnderstockedWaveGenerateAllocateGoodsRecord())));
            }

            //清空上一次订单配货库区字段
            filterTrades.forEach(t -> t.setSectionAreaCodes(""));

            filterTrades = filterTradeBeforeAllocateInAfter(staff, filterTrades, waveFilterParams);
            // 1. 指定订单配货
            ITradeWaveQueryService.QueryWaveResult allocateWaveResult = new ITradeWaveQueryService.QueryWaveResult();
            allocateWaveResult.setTrades(filterTrades);
            allocateWaveResult.setAlreadyAllocateRecord(queryWaveResult.getAlreadyAllocateRecord());
            allocateWaveResult.setFromQuery(queryWaveResult.getFromQuery());
            allocateWaveResult.setReusable(queryWaveResult.getReusable());
            allocateWaveResult.setBackRegionPickFirst(queryWaveResult.getBackRegionPickFirst());
            allocateWaveResult.setOrderStockProducts(queryWaveResult.getOrderStockProducts());
            allocateGoodsStock0(staff, allocateWaveResult, true, waveRuleCondition,waveFilterParams);

            // 2. 过滤无配货数的订单，如果允许缺货先不过滤
            if (Objects.equal(waveRuleCondition.isAllowUnderstocked(), true)) {
                filterAllocateGoodsIfAllowUnderstocked(staff, filterTrades, allocateWaveResult);
                return filterTradesByAllowUnderstockedRange(waveRuleCondition, allocateWaveResult);
            }
            List<Trade> trades = allocateWaveResult.getTrades().stream().filter(trade ->
                    !(Objects.equal(trade.getWaveId(), Wave.NOT_IN_WAVE_ID))).collect(Collectors.toList());
            return filterSectionArea(trades, waveRuleCondition);
        } else {
            return filterTrades;
        }
    }

    private List<Trade> filterTradeBeforeAllocateInAfter(Staff staff, List<Trade> filterTrades, WaveFilterParams waveFilterParams) {
        //去除特定场景下的配货，例如商品所有货位库存都是0,无需配货
        if(CollectionUtils.isNotEmpty(waveFilterParams.getUnderStockedRemoveSids())){
            if(waveFilterParams.getUnderStockedRemoveSids().size()<50){
                logger.debug(LogHelper.buildLog(staff,String.format("后配货前过滤订单不参与配货 sids:%s", waveFilterParams.getUnderStockedRemoveSids())));
            }else {
                logger.debug(LogHelper.buildLog(staff,String.format("后配货前过滤订单不参与配货 sids:%s", waveFilterParams.getUnderStockedRemoveSids().size())));
            }
            filterTrades = filterTrades.stream().filter(trade -> !waveFilterParams.getUnderStockedRemoveSids().contains(trade.getSid())).collect(Collectors.toList());

        }
        return filterTrades;
    }

    private void setUnderstockedWaveGenerateAllocateGoodsRecord(Staff staff, WaveRuleCondition waveRuleCondition, WaveConfig waveConfig) {
        Integer openUnderstockedWaveAllocateGoodsRecord = getOpenUnderstockedWaveAllocateGoodsRecord(staff);

        if (waveRuleCondition != null && Objects.equal(openUnderstockedWaveAllocateGoodsRecord, 1) && waveRuleCondition.isAllowUnderstocked()) {
            waveRuleCondition.setUnderstockedWaveGenerateAllocateGoodsRecord(true);
            waveRuleCondition.setUnderstockedWaveAllocatePickRegionRecord(Objects.equal(getUnderstockedWaveAllocatePickRegionRecord(staff), 1));
        }
    }

    private Integer getOpenUnderstockedWaveAllocateGoodsRecord(Staff staff) {
        try {
            PdaConfigConvert pdaConfig = wmsPdaDubboService.getPdaConfig(staff);
            return (Integer) pdaConfig.getExtConfig().getOrDefault("openUnderstockedWaveAllocateGoodsRecord", 0);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "获取pda配置失败"));
            return 0;
        }
    }

    private Integer getUnderstockedWaveAllocatePickRegionRecord(Staff staff) {
        try {
            PdaConfigConvert pdaConfig = wmsPdaDubboService.getPdaConfig(staff);
            return (Integer) pdaConfig.getExtConfig().getOrDefault("understockedWaveAllocatePickRegionRecord", 0);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "获取pda配置失败"));
            return 0;
        }
    }

    /**
     * 【货位可配库存不足时，仍允许生成波次】后增加多选项【部分有库存】【全部无库存】
     * 【部分有库存】勾选表示符合波次规则的订单部分配到货也支持生成波次
     * 【全部无库存】勾选表示符合波次规则的订单全部都没配到货也支持生成波次
     *  仅支持后配货逻辑
     */
    private List<Trade> filterTradesByAllowUnderstockedRange(WaveRuleCondition waveRuleCondition, ITradeWaveQueryService.QueryWaveResult allocateWaveResult) {
        Set<Long> unAllocatedGoodsSids = Optional.ofNullable(allocateWaveResult.getAllocateGoodsResult())
                .map(AllocateGoodsResult::getUnAllocatedGoodsSids).orElse(Sets.newHashSet());
        Set<Long> allocateRecordSids = Optional.ofNullable(allocateWaveResult.getAllocateGoodsResult())
                .map(AllocateGoodsResult::getAllocateGoodsRecords).orElse(Lists.newArrayList())
                .stream().filter(r -> !r.isUnderstockedGenerated()).map(AllocateGoodsRecord::getSid).collect(Collectors.toSet());
        List<Trade> trades = allocateWaveResult.getTrades();
        // 过滤部分有库存
        if (WaveRuleCondition.PART_ALLOCATE.equals(waveRuleCondition.getAllowUnderstockedRange())) {
            return allocateWaveResult.getTrades().stream().filter(trade -> allocateRecordSids.contains(trade.getSid())).collect(Collectors.toList());
        }
        // 过滤全部无库存
        if (WaveRuleCondition.ALL_UN_ALLOCATE.equals(waveRuleCondition.getAllowUnderstockedRange())) {
            return allocateWaveResult.getTrades().stream().filter(trade -> !(allocateRecordSids.contains(trade.getSid()) && unAllocatedGoodsSids.contains(trade.getSid()))).collect(Collectors.toList());

        }
        return trades;
    }

    /**
     * 允许缺货情况下，需要删除备货区配货记录
     */
    public void filterAllocateGoodsIfAllowUnderstocked(Staff staff, List<Trade> filterTrades, ITradeWaveQueryService.QueryWaveResult allocateWaveResult) {
        if (CollectionUtils.isEmpty(filterTrades) || CollectionUtils.isEmpty(allocateWaveResult.getAlreadyAllocateRecord()) || allocateWaveResult.getAllocateGoodsResult() == null || CollectionUtils.isEmpty(allocateWaveResult.getAllocateGoodsResult().getAllocateGoodsRecords())) {
            return;
        }
        Long warehouseId = filterTrades.get(0).getWarehouseId();
        WaveBackItemRangeStrategy waveBackItemRangeStrategy = WaveBackItemRangeStrategy.build(allocateWaveResult.getWmsConfig() == null ? wmsService.getConfig(staff) : allocateWaveResult.getWmsConfig());
        List<Long> backRegionGsIds = waveQueryDao.queryBackRegionGsIds(staff, warehouseId);
        Set<Long> pickInBackSids = allocateWaveResult.getAllocateGoodsResult().getPickInBackSids();

        List<AllocateGoodsRecord> notAllowBackRecords = allocateWaveResult.getAllocateGoodsResult().getAllocateGoodsRecords().stream()
                .filter(record -> backRegionGsIds.contains(record.getGoodsSectionId()) && !waveBackItemRangeStrategy.allowBack(pickInBackSids, record)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notAllowBackRecords)) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "允许缺货情况下，需要删除不满足下限的备货区配货记录, 满足备货区下限的订单" + notAllowBackRecords.stream().map(AllocateGoodsRecord::getOrderId).distinct().collect(Collectors.toList())));
        }
        allocateWaveResult.getAlreadyAllocateRecord().removeIf(notAllowBackRecords::contains);
        allocateWaveResult.getUnGenerateAllocateGoodsRecord().addAll(notAllowBackRecords);
    }

    private static List<Trade> filterSectionArea(List<Trade> trades, WaveRuleCondition ruleCondition) {
        if (CollectionUtils.isEmpty(trades) || ruleCondition == null
                || ruleCondition.getSectionAreaType() == null) {
            return trades;
        }
        SingleWaveMatchFilter singleWaveMatchFilter = new SingleWaveMatchFilter(ruleCondition, true);
        return trades.stream().filter(trade ->
                trade.getSectionAreaCodes() == null || singleWaveMatchFilter.matchSingle(trade, singleWaveMatchFilter.matchSectionAreaType(trade), "同/跨库区不匹配！")
        ).collect(Collectors.toList());
    }

    /**
     * 按照订单配货，并设置可配数是否充足
     */
    public void allocateGoodsStock0(Staff staff, ITradeWaveQueryService.QueryWaveResult queryWaveResult, Boolean isReturnAlreadyAllocateRecord, WaveRuleCondition waveRuleCondition,WaveFilterParams waveFilterParams) {
        if (!WmsUtils.isNewWms(staff)) {
            return;
        }
        List<Trade> trades = queryWaveResult.getTrades();
        if (CollectionUtils.isNotEmpty(trades)) {
            final AllocateGoodsResult allocateGoodsResult = allocateGoods(staff, trades, isReturnAlreadyAllocateRecord, queryWaveResult.getAlreadyAllocateRecord(), waveRuleCondition, queryWaveResult.getBackRegionPickFirst(), queryWaveResult.getFromQuery(), queryWaveResult.getReusable(), queryWaveResult.getOrderStockProducts(),waveFilterParams);
            if (allocateGoodsResult.isSuccess()) {
                Set<Long> needRemoveSid = allocateGoodsResult.getNeedRemoveSid();
                Set<Long> unAllocatedGoodsSids = allocateGoodsResult.getUnAllocatedGoodsSids();

                setIsBoxPick(waveRuleCondition, allocateGoodsResult);
                // 塞入库区
                buildWmsInfoFromAllocateGoodsRecord(staff, trades, allocateGoodsResult);

                Set<Long> unAllocatedGoodsOrderIds = allocateGoodsResult.getUnAllocatedGoodsOrderIds();
                Map<Pair<Long, Long>, List<AssoGoodsSectionSku>> noExistAssoGoodsSectionSkuOids = allocateGoodsResult.getsPeStockRegionSectionSkuMap();
                logSepStockRegion(staff, noExistAssoGoodsSectionSkuOids);
                WaveConfig waveConfig = getWaveConfig(staff, queryWaveResult.getWaveConfig());
                WmsConfig wmsConfig = wmsService.getConfig(staff);
                Set<Long> removeSids=new HashSet<>();
                for (Trade waveTrade : trades) {
                    if (needRemoveSid.contains(waveTrade.getSid()) || unAllocatedGoodsSids.contains(waveTrade.getSid())) {
                        WaveUtils.setNotInWave(waveTrade, "货位可配数不足商品" +
                                OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(waveTrade)).stream()
                                        .filter(oid -> unAllocatedGoodsOrderIds.contains(oid.getId()))
                                        .map(order -> {
                                            return order.getSysOuterId() + ":" + (order.getSysSkuPropertiesName() != null ? order.getSysSkuPropertiesName() : " ");
                                        }).collect(Collectors.joining(",", "(", ")")));
                    }
                    //处理指定货位情况，过滤一些不满足货位的配货记录
                    boolean remove = assembleSpeStockRegion(staff, waveRuleCondition, waveTrade, noExistAssoGoodsSectionSkuOids, allocateGoodsResult,queryWaveResult,waveConfig,wmsConfig);
                    if(!remove){
                        removeSids.add(waveTrade.getSid());
                    }
                }
                if(waveFilterParams.isNoRuleAllowUnderStocked()&&CollectionUtils.isNotEmpty(allocateGoodsResult.getUnderStockedRemoveSids())){
                    waveFilterParams.setUnderStockedRemoveSids(allocateGoodsResult.getUnderStockedRemoveSids());
                    removeSids.addAll(allocateGoodsResult.getUnderStockedRemoveSids());
                }
                if (queryWaveResult.getFromQuery() != null
                        && !queryWaveResult.getFromQuery()) {
                    logIds(staff, new ArrayList(needRemoveSid), "needRemoveSid");
                    logIds(staff, new ArrayList(unAllocatedGoodsSids), "unAllocatedGoodsSids");
                    logIds(staff, new ArrayList(unAllocatedGoodsOrderIds), "unAllocatedGoodsOrderIds");
                }
                if(CollectionUtils.isNotEmpty(removeSids)){
                    if(removeSids.size()<50){
                        logger.debug(LogHelper.buildLog(staff,String.format("配货直接踢出订单 sids:%s rule:%s",removeSids,waveRuleCondition)));
                    }else {
                        logger.debug(LogHelper.buildLog(staff,String.format("配货直接踢出订单 sids:%s rule:%s",removeSids.size(),waveRuleCondition)));
                    }
                    trades=trades.stream().filter(trade -> !removeSids.contains(trade.getSid())).collect(Collectors.toList());
                }
                queryWaveResult.setAllocateGoodsResult(allocateGoodsResult);
                // 如果是查询，不记录已配数
                if (!queryWaveResult.getFromQuery()) {
                    if (queryWaveResult.getAllocateGoodsResult() != null) {
                        logRecords(staff, queryWaveResult.getAllocateGoodsResult().getAllocateGoodsRecords());
                    }
                    fillAlreadyAllocateRecord(queryWaveResult);
                }
            } else {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("计算波次时配货失败，失败原因：%s", allocateGoodsResult.getMessage())));
                }
                trades = new ArrayList<>();
            }
        }
        queryWaveResult.setTrades(trades);
    }

    private void logSepStockRegion(Staff staff, Map<Pair<Long, Long>, List<AssoGoodsSectionSku>> noExistAssoGoodsSectionSkuOids) {
        if(MapUtils.isNotEmpty(noExistAssoGoodsSectionSkuOids)){
            try{
                List<Long>companyIds=Lists.newArrayList(38413L,288532L);
                Map<Pair<Long, Long>, List<String>> errorMsg=new HashMap<>();
                noExistAssoGoodsSectionSkuOids.forEach((key, value) -> errorMsg.put(key,(value==null?new ArrayList<AssoGoodsSectionSku>():value)
                        .stream().map(AssoGoodsSectionSku::getStockRegion).collect(Collectors.toList())));
                if(companyIds.contains(staff.getCompanyId())){
                    List<Map<Pair<Long, Long>, List<String>>> maps = partitionMap(errorMsg, 5);
                    for (Map<Pair<Long, Long>, List<String>> map : maps) {
                        logger.info(LogHelper.buildLog(staff,String.format("指定货位配货, 存在配货记录不存在的商品:%s", JSON.toJSONString(map))));
                    }
                }else {
                    if(noExistAssoGoodsSectionSkuOids.size()<20){
                        logger.info(LogHelper.buildLog(staff,String.format("指定货位配货, 存在配货记录不存在的商品:%s", JSON.toJSONString(errorMsg))));
                    }else {
                        logger.info(LogHelper.buildLog(staff,String.format("指定货位配货, 存在配货记录不存在的商品,size:%s", noExistAssoGoodsSectionSkuOids.size())));
                    }
                }
            }catch (Exception e){
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("指定货位配货，存在配货记录不存在的商品失败原因：%s", e.getMessage())));
                }
            }
        }
    }


    public static <K, V> List<Map<K, V>> partitionMap(Map<K, V> map, int partitionSize) {
        if (MapUtils.isEmpty(map)) {
            return new ArrayList<>();
        }

        List<Map.Entry<K, V>> entries = new ArrayList<>(map.entrySet());
        List<List<Map.Entry<K, V>>> entryLists = ListUtils.partition(entries, partitionSize);

        List<Map<K, V>> result = new ArrayList<>();
        for (List<Map.Entry<K, V>> entryList : entryLists) {
            Map<K, V> partition = new HashMap<>();
            for (Map.Entry<K, V> entry : entryList) {
                partition.put(entry.getKey(), entry.getValue());
            }
            result.add(partition);
        }

        return result;
    }

    private boolean assembleSpeStockRegion(Staff staff, WaveRuleCondition waveRuleCondition, Trade waveTrade, Map<Pair<Long, Long>, List<AssoGoodsSectionSku>> noExistAssoGoodsSectionSkuOids, AllocateGoodsResult allocateGoodsResult, ITradeWaveQueryService.QueryWaveResult queryWaveResult,WaveConfig waveConfig,WmsConfig wmsConfig) {
        AtomicBoolean flag = new AtomicBoolean(true);
        //配货记录要不存在,缺货生成 后配货
        if(MapUtils.isNotEmpty(noExistAssoGoodsSectionSkuOids)
                &&waveRuleCondition!=null
                &&waveRuleCondition.isAllowUnderstocked()
                && WmsUtils.isNewWms(staff)
                && waveConfig.getOpenRuleFirstThenAllocate() != null
                && waveConfig.getOpenRuleFirstThenAllocate() == 1
        ){

            List<Pair<Long, Long>> pairs = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(waveTrade))
                    .stream().map(order -> Pair.of(order.getItemSysId(),(order.getSkuSysId()==null||order.getSkuSysId()<0L?0L:order.getSkuSysId()))).collect(Collectors.toList());
            pairs.forEach(sku->{
                if(noExistAssoGoodsSectionSkuOids.containsKey(sku)){
                    logger.info(LogHelper.buildLog(staff,String.format("指定货位配货,存在配货记录为空的商品sid:%s sku:%s",waveTrade.getSid(),JSON.toJSONString(sku))));
                    List<AssoGoodsSectionSku> goodsSectionSkus = noExistAssoGoodsSectionSkuOids.get(sku);
                    if(CollectionUtils.isNotEmpty(goodsSectionSkus)){
                        StockRegionWaveMatchFilter filter = new StockRegionWaveMatchFilter(staff, wmsConfig, waveRuleCondition, true);
                        //是否至少存在一个货位满足指定货位
                        boolean exist = goodsSectionSkus.stream().anyMatch(filter::match);
                        if(!exist){
                            //货位库存存在，不满足
                            stockRegionMatchResetAllocateGoodsRecords(staff, waveTrade, allocateGoodsResult,queryWaveResult);
                            flag.set(false);
                        }
                    }else {
                        //货位库存存在，不满足
                        stockRegionMatchResetAllocateGoodsRecords(staff, waveTrade, allocateGoodsResult,queryWaveResult);
                        flag.set(false);
                    }
                }
            });
        }
        return flag.get();
    }

    private void stockRegionMatchResetAllocateGoodsRecords(Staff staff, Trade waveTrade, AllocateGoodsResult allocateGoodsResult, ITradeWaveQueryService.QueryWaveResult queryWaveResult) {
        List<AllocateGoodsRecord> allocateGoodsRecords = allocateGoodsResult.getAllocateGoodsRecords();
        logger.info(LogHelper.buildLog(staff,String.format("指定货位不匹配 订单,sid:%s ", waveTrade.getSid())));
        WaveUtils.setNotMatchWave(waveTrade, "指定货位不匹配！");

        //过滤货位库存不匹配的订单，不是主过滤货位流程，为了修复bug的兼容方案
        List<Trade> trades = queryWaveResult.getTrades();
        List<Trade> filter = trades.stream().filter(trade -> !waveTrade.getSid().equals(trade.getSid())).collect(Collectors.toList());
        queryWaveResult.setTrades(filter);
        //添加未配货成功id
        if(CollectionUtils.isNotEmpty(allocateGoodsResult.getUnAllocatedGoodsSids())){
            allocateGoodsResult.getUnAllocatedGoodsSids().add(waveTrade.getSid());
        }
        Set<Long> ids = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(waveTrade))
                .stream().map(Order::getId).collect(Collectors.toSet());
        //添加未配货成功id
        if(CollectionUtils.isNotEmpty(allocateGoodsResult.getUnAllocatedGoodsOrderIds())){
            allocateGoodsResult.getUnAllocatedGoodsOrderIds().addAll(ids);
        }
        allocateGoodsRecords= allocateGoodsRecords.stream().filter(allocateGoodsRecord -> !ids.contains(allocateGoodsRecord.getOrderId())).collect(Collectors.toList());
        allocateGoodsResult.setAllocateGoodsRecords(allocateGoodsRecords);
    }

    private void setIsBoxPick(WaveRuleCondition waveRuleCondition, AllocateGoodsResult allocateGoodsResult) {
        if (waveRuleCondition != null && waveRuleCondition.getBigPick() != null && BooleanUtils.isTrue(waveRuleCondition.getBigPick())) {
            allocateGoodsResult.setBoxNumPick(true);
        }
    }

    private void logRecords(Staff staff, List<AllocateGoodsRecord> records) {
        try {
            if (!Objects.equal(staff.getCompanyId(), 21476L) || CollectionUtils.isEmpty(records)) {
                return;
            }
            List<Long> orderIds = records.stream().map(AllocateGoodsRecord::getOrderId).collect(Collectors.toList());
            logIds(staff, orderIds, "配货成功orderIds");
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "记录配货记录Id失败！"));
        }
    }

    private void logIds(Staff staff, List<Long> ids, String logName) {
        try {
            if (!Objects.equal(staff.getCompanyId(), 21476L) || CollectionUtils.isEmpty(ids)) {
                return;
            }
            WaveContext context = WaveUtils.WAVE_CONTEXT.get();
            String ruleName = (context == null ? "" : context.getRuleName());
            if (logger.isDebugEnabled()) {
                for (List<Long> subIds : Lists.partition(ids, 500)) {
                    logger.debug(LogHelper.buildLog(staff, logName + "日志：规则名称：" + ruleName + "，" + subIds));
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "记录Id日志失败！"));
        }
    }

    /**
     * 汇总配货记录
     * @param queryWaveResult
     */
    private void fillAlreadyAllocateRecord(ITradeWaveQueryService.QueryWaveResult queryWaveResult) {
        if (queryWaveResult.getAllocateGoodsResult() != null
                && CollectionUtils.isNotEmpty(queryWaveResult.getAllocateGoodsResult().getAllocateGoodsRecords())) {
            List<AllocateGoodsRecord> records = queryWaveResult.getAllocateGoodsResult().getAllocateGoodsRecords();
            queryWaveResult.getAlreadyAllocateRecord().addAll(mergeRecordsById(records));
        }
    }

    /**
     * 合并配货记录，【order + 货位 + 商品】维度，有些订单先进行了前置配货（部分配货），然后再生成波次
     * @param records
     * @return
     */
    public static List<AllocateGoodsRecord> mergeRecordsById(List<AllocateGoodsRecord> records) {
        // 有id的配货记录
        Map<String, AllocateGoodsRecord> recordMap = records.stream().filter(r -> DataUtils.checkLongNotEmpty(r.getId())).collect(Collectors.toMap(WmsKeyUtils::buildOrderRecordItemKey, t -> t, (a, b) -> {
            a.setAllocatedNum(a.getAllocatedNum() + b.getAllocatedNum());
            return a;
        }));
        if (MapUtils.isEmpty(recordMap)) {
            return records;
        }

        for (AllocateGoodsRecord record : records) {
            if (DataUtils.checkLongNotEmpty(record.getId())) {
                continue;
            }
            recordMap.compute(WmsKeyUtils.buildOrderRecordItemKey(record), (k, v) -> {
                if (v == null) {
                    return record;
                } else {
                    v.setAllocatedNum(v.getAllocatedNum() + record.getAllocatedNum());
                    return v;
                }
            });
        }
        return new ArrayList<>(recordMap.values());
    }

    private void buildWmsInfoFromAllocateGoodsRecord(Staff staff, List<Trade> waveTrades, AllocateGoodsResult allocateGoodsResult) {
        List<AllocateGoodsRecord> allocateGoodsRecords = allocateGoodsResult.getAllocateGoodsRecords();
        if (CollectionUtils.isEmpty(waveTrades) || CollectionUtils.isEmpty(allocateGoodsRecords)) {
            return;
        }
        WmsConfig config = wmsService.getConfig(staff);
        List<Long> orderIds = Lists.newArrayListWithCapacity(waveTrades.size());
        for (Trade trade : waveTrades) {
            List<Order> orders = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(trade));
            for (Order order : orders) {
                orderIds.add(order.getId());
            }
        }
        if (orderIds.isEmpty()) {
            logger.warn(LogHelper.buildLog(staff, "当前无可拣选子订单，sids：" + TradeUtils.toSidList(waveTrades)));
            return;
        }
        Map<Long, Map<String, Integer>> orderIdGsCodeNumMap = Maps.newHashMapWithExpectedSize(orderIds.size());
        Map<String, Long> gsCodeGsIdMap = Maps.newHashMap();
        for (AllocateGoodsRecord record : allocateGoodsRecords) {
            Map<String, Integer> codeNumMap = orderIdGsCodeNumMap.computeIfAbsent(record.getOrderId(), k -> Maps.newHashMapWithExpectedSize(1));
            codeNumMap.put(record.getGoodsSectionCode(), record.getAllocatedNum());
            gsCodeGsIdMap.put(record.getGoodsSectionCode(), record.getGoodsSectionId());
        }

        Map<Long, List<String>> warehouseStockRegionCodesOrderMap = WavePickUtils.toWarehouseSectionCodesOrderMap(wmsService.getPickGoodsRouteConfig(staff));
        waveTradesQueryBusiness.assembleWithSectionInfo(staff, waveTrades, config, orderIdGsCodeNumMap, gsCodeGsIdMap, warehouseStockRegionCodesOrderMap.get(waveTrades.get(0).getWarehouseId()), allocateGoodsResult.getPickInBackSids(), allocateGoodsResult.isBoxNumPick());
    }

    private void initAllocateGoodsData(Staff staff, ITradeWaveQueryService.QueryWaveResult queryWaveResult) {
        if (CollectionUtils.isEmpty(queryWaveResult.getTrades())) {
            return;
        }
        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        WaveConfig waveConfig = getWaveConfig(staff, queryWaveResult.getWaveConfig());
        WmsConfig wmsConfig = wmsService.getConfig(staff);
        List<Trade> copyTrades = getCopyTrades(queryWaveResult.getTrades());
        AllocateGoodsParams allocateGoodsParams = new AllocateGoodsParams.Builder().trades(copyTrades)
                .isReturnAlreadyAllocateRecord(true)
                .fromQuery(queryWaveResult.getFromQuery())
                .dataReusable(queryWaveResult.getReusable())
                .allocateGoodsRule(new AllocateGoodsRule.Builder()
                        .onlyAllocateWhenSufficient(false)
                        .pickGoodsRouteConfig(wmsService.getPickGoodsRouteConfig(staff))
                        .build())
                .alreadyAllocateRecords(queryWaveResult.getAlreadyAllocateRecord())
                .waveRuleCondition(null)
                .sectionCanAllocate(true) //订单配货,只查参与订单配货的货位
                .openRuleFirstThenAllocate(waveConfig.getOpenRuleFirstThenAllocate())
                .wmsConfig(wmsConfig)
                .build();

        long start = System.currentTimeMillis();
        waveAllocateGoodsService.getAllocateGoodsData(staff, allocateGoodsParams);
        queryWaveResult.setReusable(allocateGoodsParams.getDataReusable());
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "后配货初始化配货数据耗时：" + (System.currentTimeMillis() - start)));
        }
    }

    /**
     * 调用仓储配货服务
     *
     * @param staff
     * @param trades
     * @param isReturnAlreadyAllocateRecord
     * @param alreadyAllocateRecords
     * @param orderStockProducts
     * @param waveFilterParams
     * @return
     */
    private AllocateGoodsResult allocateGoods(Staff staff, List<Trade> trades, Boolean isReturnAlreadyAllocateRecord,
                                              List<AllocateGoodsRecord> alreadyAllocateRecords, WaveRuleCondition waveRuleCondition,
                                              Boolean backRegionPickFirst, Boolean fromQuery, AllocateGoodsDataReusable reusable,
                                              List<OrderStockProduct> orderStockProducts, WaveFilterParams waveFilterParams) {
        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        WaveConfig waveConfig = waveServiceDubbo.queryWaveConfig(staff);
        WmsConfig wmsConfig = wmsService.getConfig(staff);
        // 这里onlyAllocateWhenSufficient设置为false的原因是预配货只需要得到能配到货的订单的配货记录
        List<Trade> copyTrades = getCopyTrades(trades);
        AllocateGoodsParams allocateGoodsParams = new AllocateGoodsParams.Builder().trades(copyTrades)
                .isReturnAlreadyAllocateRecord(isReturnAlreadyAllocateRecord)
                .fromQuery(fromQuery)
                .dataReusable(reusable)
                .allocateGoodsRule(new AllocateGoodsRule.Builder()
                        .onlyAllocateWhenSufficient(false)
                        .backRegionPickFirst(BooleanUtils.isTrue(backRegionPickFirst))
                        .pickGoodsRouteConfig(wmsService.getPickGoodsRouteConfig(staff))
                        .build())
                .alreadyAllocateRecords(alreadyAllocateRecords)
                .waveRuleCondition(waveRuleCondition)
                .boxAllocate(waveRuleCondition != null ? waveRuleCondition.getBoxAllocate() : Integer.valueOf(0))
                .sectionCanAllocate(true) //订单配货,只查参与订单配货的货位
                .openRuleFirstThenAllocate(waveConfig.getOpenRuleFirstThenAllocate())
                .orderStockProducts(orderStockProducts)
                .source(AllocateGoodsParams.SOURCE.WAVE.getType())
                .wmsConfig(wmsConfig)
                //所有规则不允许缺货优化 不是主流程
                .noRuleAllowUnderStocked(waveFilterParams.isNoRuleAllowUnderStocked())
                .build();

        long start = System.currentTimeMillis();
        AllocateGoodsResult result = waveAllocateGoodsService.allocateGoods(staff, allocateGoodsParams);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "波次配货时间：" + (System.currentTimeMillis() - start)));
        }

        if (BooleanUtils.isTrue(backRegionPickFirst)) {
            WaveBackItemRangeStrategy.build(wmsService.getConfig(staff)).addIds(result);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "开启备货区优先拣选，备货区订单：" + result.getPickInBackSids()));
            }
        }
        return result;
    }

    private List<Trade> filterTrade(List<Trade> copyTrades, WaveFilterParams waveFilterParams) {

        if(WaveCreateType.CHECKED_CROSS_TRADE.equals(waveFilterParams.getWaveCreateType())&&CollectionUtils.isNotEmpty(waveFilterParams.getAllocateOrderIds())){
            copyTrades.forEach(trade -> {
                List<Order> orders4Trade = ((Orderable<Order>) trade).getOrders();
                if(CollectionUtils.isNotEmpty(orders4Trade)){
                    orders4Trade.removeIf(o->!o.isSuit()&&!waveFilterParams.getAllocateOrderIds().contains(o.getId()));
                    for(Order order:orders4Trade){
                        if(order.isSuit()){
                            order.getSuits().removeIf(o->!waveFilterParams.getAllocateOrderIds().contains(o.getId()));
                        }
                    }
                }
            });
        }
        return copyTrades;
    }

    private TradeConfig getTradeConfig(Staff staff, TradeConfig tradeConfig) {
        if (tradeConfig == null) {
            tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        }
        return tradeConfig;
    }

    private WaveConfig getWaveConfig(Staff staff, WaveConfig waveConfig) {
        if (waveConfig == null) {
            waveConfig = waveServiceDubbo.queryWaveConfig(staff);
        }
        return waveConfig;
    }

    private List<Trade> getCopyTrades(List<Trade> trades) {
        BeanCopier copier = BeanCopier.create(TbTrade.class, TbTrade.class, false);
        List<Trade> copyTrades = trades.stream().map(trade -> {
            Trade copyTrade = new TbTrade();
            copier.copy(trade, copyTrade, null);
            return copyTrade;
        }).collect(Collectors.toList());

        return copyTrades;
    }
}

package com.raycloud.dmj.services.ec.wave;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.raycloud.dmj.business.wave.WaveProgressBusiness;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.enums.WlbTemplateTypeEnum;
import com.raycloud.dmj.domain.pt.model.waybill.bean.WlbResult;
import com.raycloud.dmj.domain.pt.model.waybill.get.WlbRequestGet;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.wave.Wave;
import com.raycloud.dmj.domain.wave.WaveTraceOperateEnum;
import com.raycloud.dmj.domain.wave.enums.WaveChatConfigsEnum;
import com.raycloud.dmj.domain.wave.params.TradeWaveWaybillGetParams;
import com.raycloud.dmj.domain.wave.utils.WaveTraceUtils;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.pt.dubbo.IWaybillPrintServiceDubbo;
import com.raycloud.dmj.services.pt.model.waybill.bean.WlbStatus;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.wave.IWaveConfigService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.WavePickUtils;
import com.raycloud.ec.api.CommonEventSource;
import com.raycloud.ec.api.EventSourceBase;
import com.raycloud.ec.api.IEventListener;
import com.raycloud.ec.api.annotation.ListenerBind;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * 波次订单电子面单号获取监听
 * Created by shaoxianchang on 2017/5/11.
 */
@Deprecated
@Component
@ListenerBind("trade.wave.waybill.get")
public class TradeWaveWayBillGetListener implements IEventListener {
    private Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private ITradeWaveService tradeWaveService;

    @Resource
    private IUserWlbExpressTemplateService userWlbExpressTemplateService;

    @Resource
    private IOpLogService opLogService;

    @Resource
    private WaveProgressBusiness waveProgressBusiness;

    @Resource
    private IWaveTraceService waveTraceService;

    @Resource
    private IWaybillPrintServiceDubbo waybillPrintServiceDubbo;
    @Resource(name = "dubboTradeService")
    protected ITradeServiceDubbo tradeServiceDubbo;

    @Resource
    IWaveConfigService waveConfigService;

    public static final List<String> KUAI_YUN_CP_CORES = Arrays.asList("CN7000001009020", "BESTQJT", "CN7000001000869", "2744832184_543", "CN7000001021040", "3108002701_1011", "SURE", "CP471906", "ZTKY", "CP446169", "YMDD", "JDKY", "JD", "DBKD");
    public static final List<String> PDD_KUAI_YUN_CP_CODES = Arrays.asList("3108002701_1011", "CP471906", "SFKY", "BESTQJT", "YMDD", "CN7000001021040", "CN7000001000869", "XFWL");
    public static final List<String> KUAISHOU_KUAI_YUN_CP_CODES = Arrays.asList("CP471906", "JDKY", "JD");

    @Override
    public void onObserved(EventSourceBase eventSourceBase) {
        CommonEventSource event = (CommonEventSource) eventSourceBase;
        TradeWaveWaybillGetParams params = event.getArg(0, TradeWaveWaybillGetParams.class);

        Staff staff = params.getStaff();
        String waveIds = params.getWaveIds();
        TradeWaveWaybillGetParams.TradeWaveWaybillGetTypeEnum type = params.getType();
        String ip = params.getIp();
        boolean needOpLog = TradeWaveWaybillGetParams.TradeWaveWaybillGetTypeEnum.AUTO.equals(type);
        execute(staff, ArrayUtils.toLongList(waveIds), ip, needOpLog, ProgressEnum.PROGRESS_TRADE_WAVE_WAYBILL_GET);
    }

    public void execute(Staff staff, List<Long> waveIds, String ip, boolean needOpLog, ProgressEnum progressEnum) {
        long start = System.currentTimeMillis();
        int totalSidNum = 0;
        ProgressData progressData = waveProgressBusiness.getOrCreate(staff, progressEnum);
        try {
            List<String> errors = Lists.newArrayList();
            List<String> warnningMsgs = Lists.newArrayList();
            List<Wave> waves = tradeWaveService.queryWaveByIds(staff, waveIds.toArray(new Long[0]));
            WavePickUtils.filterNotTradeWaves(waves);
            Assert.notEmpty(waves, "无需要获取电子面单的波次！");

            Map<Long, Multimap<Long, Long>> wareTemplateIdSidMap = Maps.newHashMap();
            Map<Long, Multimap<Long, Long>> wareKJTemplateIdSidMap = Maps.newHashMap();
            Map<String, Long> sidWaveMap = groupWaveTradesTemplate(staff, waves, wareTemplateIdSidMap, wareKJTemplateIdSidMap, errors);

            totalSidNum = sidWaveMap.size();
            waveProgressBusiness.setProgress(staff, progressEnum, totalSidNum, 0);
            logger.debug(LogHelper.buildLog(staff, String.format("加载完波次电子面单[%s个]数据，耗时：%s", totalSidNum, (System.currentTimeMillis() - start))));

            // 分组获取电子面单
            Map<Long, ResultOpLogBean> getResultOpLogMap = getWaybill(staff, wareTemplateIdSidMap, ip, sidWaveMap, errors, warnningMsgs);

            progressData.setCountCurrent(progressData.getCountAll() / 2);
            waveProgressBusiness.setProgress(staff, progressEnum, progressData);

            // 重新查询波次订单的单号信息，修改波次单号状态
            updateWaveOutsidStatus(staff, waves);
            if (needOpLog) {
                writeOpLog(staff, getResultOpLogMap, ip);
            }
            progressData.setCountCurrent(progressData.getCountAll());
            progressData.setErrorMsg(errors);
            progressData.setWarnningMsgs(warnningMsgs);
            waveTraceService.batchAddWaveTrace(staff, WaveTraceUtils.buildBatchWaveTrace(staff, waveIds, WaveTraceOperateEnum.WAVE_WAYBILL_GET, "批量获取电子面单"));
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "获取波次订单电子面单号失败"), e);
            waveTraceService.batchAddWaveTrace(staff, WaveTraceUtils.buildBatchWaveTrace(staff, waveIds, WaveTraceOperateEnum.WAVE_WAYBILL_GET, "批量获取电子面单", 1));
            progressData.setErrorMsg(Lists.newArrayList(e.getMessage()));
        } finally {
            progressData.setProgress(2);
            waveProgressBusiness.setProgress(staff, progressEnum, progressData);
        }
        logger.info(LogHelper.buildLog(staff, String.format("批量获取波次[%s]电子面单号成功，共[%s]个，耗时：%s", waveIds, totalSidNum, (System.currentTimeMillis() - start))));
    }

    /**
     * 将波次的订单根据仓库、快递模板进行分组，过滤掉普通面单
     */
    private Map<String, Long> groupWaveTradesTemplate(Staff staff, List<Wave> waves, Map<Long, Multimap<Long, Long>> wareTemplateIdSidMap, Map<Long, Multimap<Long, Long>> wareKJTemplateIdSidMap, List<String> errors) {
        // 将波次的订单根据仓库、快递模板进行分组，过滤掉普通面单
        Iterator<Wave> waveIte = waves.iterator();
        Map<String, Long> sidWaveMap = Maps.newHashMap();

        while (waveIte.hasNext()) {
            Wave wave = waveIte.next();
            if (wave.getOutsidStatus() == Wave.OUTSID_STATUS_OVER) {
                waveIte.remove();
                continue;
            }
            Long waveId = wave.getId();
            List<Long> sids = tradeWaveService.querySidsByWaveId(staff, waveId);
            List<Trade> trades = tradeServiceDubbo.queryTradeBySids(staff, false, sids.toArray(new Long[0]));
            if (CollectionUtils.isNotEmpty(trades)) {
                for (Trade trade : trades) {
                    if (StringUtils.isEmpty(trade.getOutSid())) {
                        sidWaveMap.put(trade.getSid().toString(), waveId);
                        if (trade.getTemplateType() == 2) {
                            Multimap<Long, Long> templateIdSidMap = wareKJTemplateIdSidMap.get(wave.getWarehouseId());
                            if (templateIdSidMap == null) {
                                templateIdSidMap = HashMultimap.create();
                            }
                            templateIdSidMap.put(trade.getTemplateId(), trade.getSid());
                            wareKJTemplateIdSidMap.put(wave.getWarehouseId(), templateIdSidMap);
                        } else if (trade.getTemplateType() == 1) {
                            Multimap<Long, Long> templateIdSidMap = wareTemplateIdSidMap.get(wave.getWarehouseId());
                            if (templateIdSidMap == null) {
                                templateIdSidMap = HashMultimap.create();
                            }
                            templateIdSidMap.put(trade.getTemplateId(), trade.getSid());
                            wareTemplateIdSidMap.put(wave.getWarehouseId(), templateIdSidMap);
                        } else if (trade.getTemplateType() == 0) {
                            errors.add(trade.getSid() + "，该订单快递模板为普通面单");
                        }
                    }
                }
            }
        }
        return sidWaveMap;
    }

    private Map<Long, ResultOpLogBean> getWaybill(Staff staff, Map<Long, Multimap<Long, Long>> wareTemplateIdSidMap, String ip, Map<String, Long> sidWaveMap, List<String> errors, List<String> warnningMsgs) {
        Map<Long, ResultOpLogBean> getResultOpLogMap = Maps.newHashMap();
        int integer = waveConfigService.get(staff).getInteger(WaveChatConfigsEnum.KY_WAVE_AUTO_GET_WAYBILL_CODE.getKey());
        if (wareTemplateIdSidMap.size() > 0) {
            for (Map.Entry<Long, Multimap<Long, Long>> wareTemplateIdSidEntry : wareTemplateIdSidMap.entrySet()) {
                for (Map.Entry<Long, Collection<Long>> templateIdSidMap : wareTemplateIdSidEntry.getValue().asMap().entrySet()) {
                    Collection<Long> sids = templateIdSidMap.getValue();
                    UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, templateIdSidMap.getKey(), false);
                    if (integer == 0 && isKuaiYun(userWlbExpressTemplate)){
                        logger.debug(LogHelper.buildLog(staff, String.format("波次获取单号时，跳过快运订单，仓库：%s，快递模板Id：%s，订单：%s", wareTemplateIdSidEntry.getKey(), templateIdSidMap.getKey(), sids)));
                        continue;
                    }
                    logger.debug(LogHelper.buildLog(staff, String.format("获取电子面单，仓库：%s，快递模板Id：%s，订单：%s", wareTemplateIdSidEntry.getKey(), templateIdSidMap.getKey(), sids)));

                    WlbRequestGet wlbRequestGet = new WlbRequestGet(wareTemplateIdSidEntry.getKey(), templateIdSidMap.getKey(), sids.toArray(new Long[0]));
                    wlbRequestGet.setWlbType(userWlbExpressTemplate.getWlbType());
                    wlbRequestGet.setClientIp(ip);
                    WlbStatus wlbStatus = waybillPrintServiceDubbo.getWaybillCodeGroupByUserId(staff, userWlbExpressTemplate, wlbRequestGet);
                    handleWlbResult(wlbStatus, sidWaveMap, getResultOpLogMap, errors, warnningMsgs);
                }
            }
        }
        return getResultOpLogMap;
    }

    private void updateWaveOutsidStatus(Staff staff, List<Wave> waves) {
        for (Wave wave : waves) {
            List<Long> sids = tradeWaveService.querySidsByWaveId(staff, wave.getId());
            List<Trade> trades = tradeServiceDubbo.queryTradeBySids(staff, false, sids.toArray(new Long[0]));
            if (CollectionUtils.isNotEmpty(trades)) {
                int hasOutSidNum = 0;
                boolean hasWayBill = false;
                for (Trade trade : trades) {
                    if (StringUtils.isNotEmpty(trade.getOutSid())) {
                        hasOutSidNum++;
                    }
                    if (trade.getTemplateType() == 1) {
                        hasWayBill = true;
                    }
                }
                if (hasOutSidNum == 0 && !hasWayBill) {
                    //都是普通面单，且没有面单编号，直接过滤
                    continue;
                }
                if (hasOutSidNum > 0) {
                    if (hasOutSidNum == trades.size()) {
                        tradeWaveService.updateOutsidStatus(staff, wave.getId(), Wave.OUTSID_STATUS_OVER);
                    } else if (hasOutSidNum < trades.size()) {
                        tradeWaveService.updateOutsidStatus(staff, wave.getId(), Wave.OUTSID_STATUS_PART);
                    }
                    logger.debug(LogHelper.buildLog(staff, String.format("波次%s订单数%s，获取单号数%s", wave.getId(), trades.size(), hasOutSidNum)));
                }
            }
        }
    }

    @SuppressWarnings("unchecked")
    private void handleWlbResult(WlbStatus wlbStatus, Map<String, Long> sidWaveMap, Map<Long, ResultOpLogBean> getResultOpLogMap, List<String> errors, List<String> warnningMsgs) {
        List<WlbResult> successResult = (List<WlbResult>) wlbStatus.getSuccessResult();
        List<WlbResult> errResults = (List<WlbResult>) wlbStatus.getErrorResult();
        if (CollectionUtils.isNotEmpty(successResult)) {
            for (WlbResult wlbResult : successResult) {
                Long waveId = sidWaveMap.get(wlbResult.getSid());
                if (null != waveId) {
                    ResultOpLogBean resultOpLogBean = getResultOpLogMap.get(waveId);
                    if (null == resultOpLogBean) {
                        resultOpLogBean = new ResultOpLogBean(waveId);
                        getResultOpLogMap.put(waveId, resultOpLogBean);
                    }
                    resultOpLogBean.getSuccessResults().add(wlbResult);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(errResults)) {
            for (WlbResult result : errResults) {
                errors.add(result.getSid() + "，" + result.getErrorMsg());
                Long waveId = sidWaveMap.get(result.getSid());
                if (null != waveId) {
                    ResultOpLogBean resultOpLogBean = getResultOpLogMap.get(waveId);
                    if (null == resultOpLogBean) {
                        resultOpLogBean = new ResultOpLogBean(waveId);
                        getResultOpLogMap.put(waveId, resultOpLogBean);
                    }
                    resultOpLogBean.getErrorResults().add(result);
                }
            }
        }

        String warnningMsg = wlbStatus.getWarnningMsg();
        if (StringUtils.isNotBlank(warnningMsg)) {
            warnningMsgs.add(warnningMsg);
        }
    }

    private void writeOpLog(Staff staff, Map<Long, ResultOpLogBean> getResultOpLogMap, String ip) {
        List<OpLog> opLogs = new ArrayList<OpLog>(getResultOpLogMap.size());
        for (ResultOpLogBean resultOpLogBean : getResultOpLogMap.values()) {
            OpLog opLog = new OpLog();
            opLog.setCompanyId(staff.getCompanyId());
            opLog.setStaffId(staff.getId());
            opLog.setAccountName(staff.getAccountName());
            opLog.setStaffName(staff.getName());
            opLog.setIp(ip);
            opLog.setDomain(Domain.TRADE);
            opLog.setAction("waveAutoGetWaybillCode");
            opLog.setKey(resultOpLogBean.getWaveId().toString());
            StringBuilder sb = new StringBuilder("波次号：").append(resultOpLogBean.getWaveId()).append("自动获取单号，成功")
                    .append(resultOpLogBean.getSuccessResults().size()).append("单，失败").append(resultOpLogBean.getErrorResults().size())
                    .append("单。");
            if (resultOpLogBean.getErrorResults().size() > 0) {
                sb.append("< /br>失败明细为");
                for (WlbResult wlbResult : resultOpLogBean.getErrorResults()) {
                    sb.append("< /br>订单").append(wlbResult.getSid()).append("，失败原因：").append(wlbResult.getErrorMsg()).append("；");
                }
            }
            opLog.setContent(sb.toString());
            opLog.setArgs("");
            opLog.setCreated(new Date());

            opLogs.add(opLog);
        }
        opLogService.batchRecord(staff, opLogs);
    }

    private static class ResultOpLogBean {
        private Long waveId;

        private List<WlbResult> successResults;

        private List<WlbResult> errorResults;

        public ResultOpLogBean(Long waveId) {
            this.waveId = waveId;
            this.successResults = new ArrayList<WlbResult>();
            this.errorResults = new ArrayList<WlbResult>();
        }

        public Long getWaveId() {
            return waveId;
        }

        public List<WlbResult> getSuccessResults() {
            return successResults;
        }

        public List<WlbResult> getErrorResults() {
            return errorResults;
        }
    }

    public static boolean isKuaiYun(UserWlbExpressTemplate template) {
        if (template == null || WlbTemplateTypeEnum.JDALPHA.getValue().equals(template.getWlbTemplateType())) {
            return false;
        }
        // 字节的德邦快运当快递逻辑
        if ((WlbTemplateTypeEnum.FXG.getValue().equals(template.getWlbTemplateType()) || WlbTemplateTypeEnum.DIVIDEFXG.getValue().equals(template.getWlbTemplateType()) || WlbTemplateTypeEnum.FXG_XSD.getValue().equals(template.getWlbTemplateType()) )
                && template.getCpCode().equals("CN7000001009020")) {
            return false;
        }
        return isKuaiYun(template.getCpCode());
    }

    public static boolean isKuaiYunAll(UserWlbExpressTemplate template) {
        if (template == null || WlbTemplateTypeEnum.JDALPHA.getValue().equals(template.getWlbTemplateType())) {
            return false;
        }
        // 字节的德邦快运当快递逻辑
        if ((WlbTemplateTypeEnum.FXG.getValue().equals(template.getWlbTemplateType()) || WlbTemplateTypeEnum.DIVIDEFXG.getValue().equals(template.getWlbTemplateType()) || WlbTemplateTypeEnum.FXG_XSD.getValue().equals(template.getWlbTemplateType()) )
                && template.getCpCode().equals("CN7000001009020")) {
            return false;
        }
        return isKuaiYun(template.getCpCode());
    }


    public static boolean isKuaiYun(String cpCode) {
        return KUAI_YUN_CP_CORES.contains(cpCode);
    }
}

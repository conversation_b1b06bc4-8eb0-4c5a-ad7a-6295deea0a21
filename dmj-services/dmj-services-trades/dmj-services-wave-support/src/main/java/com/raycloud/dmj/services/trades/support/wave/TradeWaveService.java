package com.raycloud.dmj.services.trades.support.wave;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.*;
import com.github.rholder.retry.*;
import com.google.common.base.Objects;
import com.google.common.base.*;
import com.google.common.collect.*;
import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.Buffers;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.*;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.wave.utils.UniqueCodeUtils;
import com.raycloud.dmj.domain.pt.log.WavePrintType;
import com.raycloud.dmj.domain.wms.enums.WmsConfigExtInfoEnum;
import com.raycloud.dmj.domain.wms.vo.FastInOutSimpleItemVO;
import com.raycloud.dmj.domain.wms.WaveType;
import com.raycloud.dmj.index.request.CheckHasFeatureRequest;
import com.raycloud.dmj.index.response.CheckHasFeatureResponse;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import com.raycloud.dmj.services.dubbo.ITradeServiceDubbo;
import com.raycloud.dmj.business.wave.*;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.pt.WaveTradePrintingStatusDAO;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.dao.wave.*;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.*;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.basis.*;
import com.raycloud.dmj.domain.constant.*;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.item.*;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.pt.enums.*;
import com.raycloud.dmj.domain.pt.wlb.*;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyBusiness;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.pt.dubbo.IWaybillPrintServiceDubbo;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.vo.WaveAssembleInfo;
import com.raycloud.dmj.domain.user.*;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.DmjSkuUtils;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.enums.*;
import com.raycloud.dmj.domain.wave.model.*;
import com.raycloud.dmj.domain.wave.params.WaveBatchUpdateRemarkParams;
import com.raycloud.dmj.domain.wave.utils.*;
import com.raycloud.dmj.domain.wave.vo.*;
import com.raycloud.dmj.domain.wms.*;
import com.raycloud.dmj.domain.wms.enums.*;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyBusiness;
import com.raycloud.dmj.express.request.template.LogisticsCompanyExpressTemplateListRequest;
import com.raycloud.dmj.item.search.api.DmjItemCommonSearchApi;
import com.raycloud.dmj.item.search.dto.DmjItemDto;
import com.raycloud.dmj.item.search.request.*;
import com.raycloud.dmj.item.search.response.*;
import com.raycloud.dmj.product.services.IOrderStockProductService;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.*;
import com.raycloud.dmj.services.basis.*;
import com.raycloud.dmj.services.domain.*;
import com.raycloud.dmj.services.dubbo.*;
import com.raycloud.dmj.services.items.shipper.IShipperDubbo;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.pt.*;
import com.raycloud.dmj.services.pt.dubbo.*;
import com.raycloud.dmj.services.trace.IItemTraceService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.filter.TradeFilterException;
import com.raycloud.dmj.services.trades.support.wave.business.WaveCodePrintBusiness;
import com.raycloud.dmj.services.trades.support.wave.efficient.EfficientEnum;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.trades.support.wave.proxy.WaveUseTradeServiceProxy;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.services.wms.*;
import com.raycloud.dmj.template.enums.PickerTemplateTypeEnum;
import com.raycloud.dmj.utils.ERPLockUtils;
import com.raycloud.dmj.utils.wms.DataUtils;
import com.raycloud.dmj.utils.wms.*;
import com.raycloud.ec.api.*;
import com.raycloud.erp.buffer.model.ErpBuffer;
import com.raycloud.erp.buffer.service.IBufferService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.*;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Predicate;
import java.util.regex.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.Strings.*;
import static com.raycloud.dmj.domain.wave.WaveRuleType.*;
import static java.util.stream.Collectors.*;

/**
 * 波次打印服务类
 * Created by guzy on 16/10/26.
 */
@Service
public class TradeWaveService implements ITradeWaveService {
    private Logger logger = Logger.getLogger(TradeWaveService.class);

    /**
     * 波次备注最大长度
     */
    public static final Integer MAX_WAVE_REMARK = 200;
    public static final Integer BATCH_SIZE = 500;
    public final static int DEFAULT_MAX_PAGE_NUM = 1000;

    /**
     * 订单一次性最大上锁数
     */
    public static final Integer MAX_LOCKS = 1000;
    /**
     * 展示套件商家编码
     */
    public static final String PRINT_COMBINE_ITEM_CODE = "3";
    /**
     * 全部店铺，前端传参 -1L
     */
    private static final Long ALL_USER_ID_PARAM = -1L;

    /**
     * 已打印订单数量 列配置code
     */
    private static final String PRINTED_TRADE_NUM_COLUMN_STR = "printedTradeNum";
    /**
     * 是否已获取订单数量 列配置code
     */
    private static final String OUT_SID_COLUMN_STR = "outsidStatus";

    public static final Long WAVE_LIST_COLUMN_PAGE_ID = 80L;
    public static final Long WAVE_MANAGE_COLUMN_PAGE_ID = 83L;
    public static final Long WAVE_LOG_COLUMN_PAGE_ID = 81L;

    /**
     * 查询来源：pda
     */
    public static final String SOURCE_TYPE_PDA = "PDA";
    /**
     * 最大展示商品数量
     */
    private static final Integer ITEM_SHOW_NUM = 6;
    /**
     * 波次列表商品种类入口
     */
    private static final Integer FROM_ITEM_KIND = 4;
    /**
     * 波次导出商品
     */
    public static final Integer QUERY_TYPE_FOR_EXPORT = 5;

    /**
     * 排除商品
     */
    private static final Integer EXCLUDE_ITEM = 1;


    private static final Pattern OUTER_ID_SPLIT_PATTERN = Pattern.compile("[,，]");

    private static final String EMPTY_WORD = "";

    public static final Long STAMP_TAG=1000000000L;


    private static final String UNDERSTOCKED_WAVE_SEED_DISTRIBUTIONSTATUS = "0,1,6,3";

    @Resource
    private WaveDao waveDao;
    @Autowired
    private WaveQueryDao waveQueryDao;
    @Autowired
    private WaveQueryPglDao waveQueryPglDao;
    @Resource
    private WaveRuleDao waveRuleDao;
    @Resource
    private WaveRuleGroupDao waveRuleGroupDao;
    @Resource
    private WavePickingDao wavePickingDao;
    @Resource(name = "dubboTradeService")
    private ITradeServiceDubbo tradeServiceDubbo;
    @Resource
    private ITradeWaveQueryService tradeWaveQueryService;
    @Resource
    private IUserExpressTemplateService userExpressTemplateService;
    @Resource
    private WaveOperateCacheBusiness waveOperateCacheBusiness;
    @Resource(name = "tbTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    private IIndexDubboService indexDubboService;

    @Resource
    private WaveUseTradeServiceProxy waveUseTradeServiceProxy;
    @Resource
    private TbTradeDao tbTradeDao;
    @Resource
    private TbOrderDAO tbOrderDAO;
    @Resource
    private IEventCenter eventCenter;
    @Resource
    private IWaveSortingService waveSortingService;
    @Resource
    private IOpLogService opLogService;
    @Resource
    private WaveTradeDao waveTradeDao;
    @Resource
    private AssoWaveItemDAO assoWaveItemDAO;
    @Resource
    private WaveSortingDao waveSortingDao;
    @Resource
    private IWaveConfigService waveConfigService;
    @Resource
    private WavePickingCodeDao wavePickingCodeDao;
    @Resource
    private TradePackLogDao tradePackLogDao;
    @Resource
    private WaveTradesQueryBusiness waveTradesQueryBusiness;
    @Resource
    private IItemServiceDubbo itemService;
    @Resource
    private IWmsService wmsService;
    @Resource
    private WaveProgressBusiness waveProgressBusiness;
    @Resource
    private WaveCacheBusiness waveCacheBusiness;
    @Resource
    private IStaffService staffService;
    @Resource
    private IWarehouseService warehouseService;
    @Resource
    private IUserWlbExpressTemplateService userWlbExpressTemplateService;
    @Resource
    private WavePickerDao wavePickerDao;
    @Autowired(required = false)
    private ICustomSortService customSortServiceDubbo;
    @Resource
    private DmjItemCommonSearchApi dmjItemCommonSearchApi;
    @Resource
    private IWavePositionService wavePositionService;

    @Resource
    private IWaveTraceService waveTraceService;

    @Resource
    private ILockService lockService;

    @Resource
    private WaveHelpBusiness waveHelpBusiness;

    @Resource
    private IWaveUniqueCodeService waveUniqueCodeService;

    @Resource
    private IOrderUniqueCodeService orderUniqueCodeService;

    @Resource(name = "tradeTraceServiceDubbo")
    private ITradeTraceService tradeTraceServiceDubbo;
    @Resource
    private IWaveWriteTradeTraceService waveWriteTradeTraceService;

    @Resource
    private ITradePostPrintService tradePostPrintService;

    @Resource
    private IItemTraceService itemTraceService;
    @Resource
    private IShopService shopService;
    @Resource
    private IShipperDubbo shipperDubbo;

    @Resource
    private WaveCodePrintBusiness waveCodePrintBusiness;

    @Resource
    private IExpressCompanyService expressCompanyService;

    @Resource
    private ILogisticsProviderService logisticsProviderService;

    @Resource
    private IBufferService bufferService;

    @Resource
    private IWaybillPrintServiceDubbo waybillPrintServiceDubbo;
    @Resource
    private IWaveColumnConfService waveColumnConfService;
    @Resource
    private IOrderStockProductService orderStockProductService;

    @Resource
    private IUserLogisticsCompanyBusiness userLogisticsCompanyBusiness;

    @Resource
    private IExpressTemplateDubboService expressTemplateDubboService;
    @Autowired(required = false)
    private IWmsPdaDubboService wmsPdaDubboService;

    @Resource
    private ISupplierService supplierService;
    @Resource
    private ICompanyService companyService;

    @Resource
    private WaveTradePrintingStatusDAO waveTradePrintingStatusDAO;
    @Resource
    private ICache cache;

    @Resource
    private IShopService iShopService;

    @Resource
    private WaveUniqueCodeDao waveUniqueCodeDao;

    @Resource
    private IWaveRuleOpLogService waveRuleOpLogService;

    @Resource
    private FeatureService featureService;

    @Resource
    private WaveScanRecordDao waveScanRecordDao;

    @Resource
    private LogisticsWaveRelationBusiness logisticsWaveRelationBusiness;

    @Resource
    private Configurable config;

    @Resource
    private WaveItemChangeBusiness waveItemChangeBusiness;

    /**
     * 批量删除波次规则
     *
     * @param ids 波次规则ids
     */
    @Override
    public void deleteRules(Staff staff, List<Long> ids) {
        waveRuleDao.deleteByIds(staff, ids);
    }


    @Override
    @Transactional
    public void saveWaveRule(Staff staff, WaveRule waveRule) {
        checkWaveRule(staff, waveRule, getRulesCheckTools(staff));
        WaveRules rules = new WaveRules();
        rules.setList(Collections.singletonList(waveRule));
        List<WaveRule> originWaveRules = waveRuleDao.queryList(staff, null);
        checkAPlusNRulesBeforeSave(rules, originWaveRules);
        checkStallRuleBeforeSave(rules);

        waveRule.setCondition(JSONObject.toJSONString(waveRule.getRuleCondition()));
        if (waveRule.getId() != null) {
            waveRuleDao.update(staff, waveRule);
            addWaveRuleLog(staff, null,Lists.newArrayList(waveRule), originWaveRules,null,null, waveRule.getIp());
        } else {
            checkMultiStallRules(originWaveRules, rules.getList());
            waveRuleDao.insert(staff, waveRule);
            addWaveRuleLog(staff, Lists.newArrayList(waveRule),null, originWaveRules,null,null,  waveRule.getIp());

        }

        //删除缓存
        this.deleteWaveRuleCache(staff, null);
    }

    private class RulesCheckTools {

        private WmsConfig wmsConfig;

        private List<Warehouse> warehouses;

        private Map<String, Integer> stockRegionTypeMaps;

        private Pattern sectionCodeStoreDefaultPattern;

        private Pattern notDefaultPattern;

        private RulesCheckTools(WmsConfig wmsConfig, List<Warehouse> warehouses, Map<String, Integer> stockRegionTypeMaps, Pattern sectionCodeStoreDefaultPattern, Pattern notDefaultPattern) {
            this.wmsConfig = wmsConfig;
            this.warehouses = warehouses;
            this.stockRegionTypeMaps = stockRegionTypeMaps;
            this.sectionCodeStoreDefaultPattern = sectionCodeStoreDefaultPattern;
            this.notDefaultPattern = notDefaultPattern;
        }

        public WmsConfig getWmsConfig() {
            return wmsConfig;
        }

        public void setWmsConfig(WmsConfig wmsConfig) {
            this.wmsConfig = wmsConfig;
        }

        public List<Warehouse> getWarehouses() {
            return warehouses;
        }


        public Map<String, Integer> getStockRegionTypeMaps() {
            return stockRegionTypeMaps;
        }

        public Pattern getSectionCodeStoreDefaultPattern() {
            return sectionCodeStoreDefaultPattern;
        }

        public Pattern getNotDefaultPattern() {
            return notDefaultPattern;
        }

    }

    private RulesCheckTools getRulesCheckTools(Staff staff) {
        if (BooleanUtils.isNotTrue(staff.getConf().isOpenWms())) {
            return null;
        }
        WmsConfig wmsConfig = wmsService.getConfig(staff);

        List<Warehouse> warehouses = warehouseService.queryAll(staff, 1);
        Assert.notEmpty(warehouses, "未查询到仓库！");

        List<StockRegion> stockRegions = wmsService.findByIds(staff, null);
        Assert.notEmpty(stockRegions, "未查询到库区！");
        Map<String, Integer> stockRegionTypeMaps = stockRegions.stream()
                .filter(s -> Objects.equal(StockRegionTypeEnum.STOCK_REGION_TYPE_1.key, s.getStockRegionType()) ||
                        Objects.equal(StockRegionTypeEnum.STOCK_REGION_TYPE_2.key, s.getStockRegionType()))
                .collect(Collectors.toMap(StockRegion::getCode, StockRegion::getStockRegionType, (a, b) -> a));

        int regionLimitNum = wmsConfig.getRegionLimitNum() == null ? WmsConstants.DEFAULT_REGION_LEN : wmsConfig.getRegionLimitNum();
        Pattern sectionCodeStoreDefaultPattern = Pattern.compile(String.format("^[A-Za-z0-9]{%s}$", regionLimitNum));
        Pattern pattern = Pattern.compile("^[A-Za-z0-9]+$");

        return new RulesCheckTools(wmsConfig, warehouses, stockRegionTypeMaps, sectionCodeStoreDefaultPattern, pattern);
    }

    @Override
    @Transactional
    public Map<String, List<WaveRule>> batchSaveRules(Staff staff, WaveRules waveRules) {
        Map<String, List<WaveRule>> result = Maps.newHashMap();

        //这里需要查询全部的，包含停用和可用的
        WaveRule waveRule = new WaveRule();
        waveRule.setIncludeDisable(true);
        List<WaveRule> originWaveRules = waveRuleDao.queryList(staff, waveRule);

        List<WaveRule> rules = waveRules.getList();
        checkAPlusNRulesBeforeSave(waveRules, originWaveRules);
        checkStallRuleBeforeSave(waveRules);
        Map<Long, WaveRulePriority> ruleIdPriorityMap = checkRulePrioritiesBeforeSave(waveRules.getRulePriorities());

        List<WaveRule> updates = Lists.newArrayList();
        List<WaveRule> inserts = Lists.newArrayList();

        // 获取波次规则校验需要的工具
        RulesCheckTools checkWaveRulesParams = getRulesCheckTools(staff);

        if (CollectionUtils.isNotEmpty(rules)) {
            for (WaveRule rule : rules) {
                checkWaveRule(staff, rule, checkWaveRulesParams);
                if (rule.getId() != null) {
                    WaveRulePriority rulePriority = ruleIdPriorityMap.get(rule.getId());
                    if (rulePriority == null) {
                        logger.debug(LogHelper.buildLog(staff, String.format("波次规则[%s-%s]找不到对应的优先级记录", rule.getId(), rule.getName())));
                    } else {
                        Assert.isTrue(rule.getSort().intValue() == rulePriority.getSort(), String.format("规则[%s]的优先级不一致！", rule.getName()));
                        rule.setCondition(JSONObject.toJSONString(rule.getRuleCondition()));
                        updates.add(rule);
                    }
                } else {
                    rule.setCondition(JSONObject.toJSONString(rule.getRuleCondition()));
                    inserts.add(rule);
                }
            }
        }

        List<Long> updateAndDeleteRuleIds = new ArrayList<>();
        List<Long> updateLoopWaves = Lists.newArrayList();
        Map<Long, WaveRule> originWaveRuleMap = new HashMap<>();

        checkMultiStallRules(originWaveRules, inserts);

        List<WaveRule> disable = Lists.newArrayList();
        boolean needDisable = CollectionUtils.isNotEmpty(waveRules.getDisableIds());
        List<WaveRule> enable = Lists.newArrayList();
        boolean needEnable = CollectionUtils.isNotEmpty(waveRules.getEnableIds());

        if (CollectionUtils.isNotEmpty(originWaveRules)) {
            List<WaveRulePriority> updatePriorities = Lists.newArrayList();
            List<WaveRule> deletes = Lists.newArrayList();
            boolean needDelete = CollectionUtils.isNotEmpty(waveRules.getDeleteIds());
            for (WaveRule originWaveRule : originWaveRules) {
                originWaveRuleMap.put(originWaveRule.getId(), originWaveRule);
                WaveRulePriority rulePriority = ruleIdPriorityMap.get(originWaveRule.getId());
                if (needDelete && waveRules.getDeleteIds().contains(originWaveRule.getId())) {
                    assembleCheckIds(originWaveRule, deletes, updateLoopWaves, updateAndDeleteRuleIds);
                } else if(rulePriority!=null&&needDisable&&waveRules.getDisableIds().contains(originWaveRule.getId())){
                    originWaveRule.setRuleStatus(2);
                    originWaveRule.setSort(rulePriority.getSort());
                    assembleCheckIds(originWaveRule,disable,updateLoopWaves,updateAndDeleteRuleIds);
                    updatePriorities.add(rulePriority);
                }else if(rulePriority!=null&&needEnable&&waveRules.getEnableIds().contains(originWaveRule.getId())){
                    originWaveRule.setRuleStatus(1);
                    originWaveRule.setSort(rulePriority.getSort());
                    enable.add(originWaveRule);
                    updatePriorities.add(rulePriority);
                } else if (rulePriority != null && !originWaveRule.getSort().equals(rulePriority.getSort())) {
                    updatePriorities.add(rulePriority);
                }
            }
            waveRuleDao.batchUpdatePriority(staff, updatePriorities);
            waveRuleDao.batchDelete(staff, deletes);
            waveRuleDao.batchDisable(staff,disable);
            waveRuleDao.batchEnable(staff,enable);
            result.put("deletes", deletes);
        }

        if (updates.size() > 0) {
            for (WaveRule update : updates) {
                WaveRule origin = originWaveRuleMap.get(update.getId());
                if (origin == null) {
                    throw new IllegalArgumentException(String.format("波次规则[%s-%s]不存在", update.getId(), update.getName()));
                }
                if (changeLoopWave(origin, update)) {
                    updateLoopWaves.add(update.getId());
                }
            }
        }
        checkUpdateAndDeleteRule(staff, updateLoopWaves, updateAndDeleteRuleIds);
        setNumDownUpIfNull(inserts);
        waveRuleDao.batchUpdate(staff, updates);
        waveRuleDao.batchInsert(staff, inserts);
        result.put("inserts", inserts);
        result.put("updates", updates);
        result.put("disables",disable);
        result.put("enables", enable);
        //删除缓存
        long start = System.currentTimeMillis();
        this.deleteWaveRuleCache(staff, null);
        logger.debug(LogHelper.buildLog(staff, "保存波次规则：" + JSON.toJSONString(result)));
        if (WaveUtils.needLog(start) && logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("删除波次规则缓存，耗时=%s", (System.currentTimeMillis() - start))));
        }
        addWaveRuleLog(staff, inserts, updates, originWaveRules, disable,enable, CollectionUtils.isNotEmpty(waveRules.getList())?waveRules.getList().get(0).getIp():"");
        return result;
    }

    private void assembleCheckIds(WaveRule originWaveRule, List<WaveRule> waveRules, List<Long> updateLoopWaves, List<Long> updateAndDeleteRuleIds) {
        waveRules.add(originWaveRule);
        if (originWaveRule.getOpenLoopWave() != null && originWaveRule.getOpenLoopWave() > 0) {
            updateLoopWaves.add(originWaveRule.getId());
        } else {
            updateAndDeleteRuleIds.add(originWaveRule.getId());
        }
    }

    private void setNumDownUpIfNull(List<WaveRule> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }
        for (WaveRule rule : rules) {
            if (rule.getNumUp() != null && rule.getNumDown() != null) {
                continue;
            }

            WaveRuleCondition ruleCondition = rule.getRuleCondition();
            if (ruleCondition == null) {
                continue;
            }

            if ((ruleCondition.getBigPick() != null && org.apache.commons.lang3.BooleanUtils.isTrue(ruleCondition.getBigPick()))) {
                if (rule.getNumDown() == null) {
                    rule.setNumDown(1);
                }
                if (rule.getNumUp() == null) {
                    rule.setNumUp(10000);
                }
            }
        }
    }

    public  void addWaveRuleLog(Staff staff, List<WaveRule> addWaveRules,List<WaveRule> newWaveRules, List<WaveRule> oldWaveRules,List<WaveRule> disableWaveRule,List<WaveRule> enableWaveRule, String ip) {
        ArrayList<WaveRuleOpLog> waveRuleOpLogs = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(addWaveRules)){
            for (WaveRule waveRule:addWaveRules) {
                waveRuleOpLogs.add(WaveRuleInfoLogEnum.buildWaveRuleOpLog(waveRule.getIp(), staff, String.format("创建波次规则【%s】", waveRule.getName()), waveRule.getId(),waveRule.getName()));
            }
        }
        if(CollectionUtils.isNotEmpty(disableWaveRule)){
            for (WaveRule waveRule:disableWaveRule) {
                waveRuleOpLogs.add(WaveRuleInfoLogEnum.buildWaveRuleOpLog(waveRule.getIp(), staff, String.format("更新波次规则【%s】：【规则启用状态】：启用-> 禁用",  waveRule.getName()), waveRule.getId(),waveRule.getName()));
            }
        }
        if(CollectionUtils.isNotEmpty(enableWaveRule)){
            for (WaveRule waveRule:enableWaveRule) {
                waveRuleOpLogs.add(WaveRuleInfoLogEnum.buildWaveRuleOpLog(waveRule.getIp(), staff, String.format("更新波次规则【%s】：【规则启用状态】：禁用-> 启用", waveRule.getName()), waveRule.getId(),waveRule.getName()));
            }
        }
        if(CollectionUtils.isNotEmpty(newWaveRules)) {
            Map<Long, WaveRule> oldWaveRuleMap = oldWaveRules.stream().collect(Collectors.toMap(WaveRule::getId, v -> v, (k1, k2) -> k1));
            for (WaveRule newWaveRule : newWaveRules) {
                WaveRule oldWaveRule = oldWaveRuleMap.get(newWaveRule.getId());
                buildRuleChLog(staff, oldWaveRule);
                buildRuleChLog(staff, newWaveRule);
                WaveRuleInfoLogEnum.buildWaveRuleOpLog(staff, oldWaveRule, newWaveRule, ip, waveRuleOpLogs);
            }
        }
        logger.debug(LogHelper.buildLog(staff, "waveRuleOpLogs:" + JSON.toJSONString(waveRuleOpLogs)));
        waveRuleOpLogService.batchSave(staff, waveRuleOpLogs);
    }


    /**
     * 构建规则中文日志
     */
    private void buildRuleChLog(Staff staff, WaveRule waveRule) {
        WaveRuleChCondition waveRuleChCondition = new WaveRuleChCondition();
        waveRule.setWaveRuleChCondition(waveRuleChCondition);
        if (waveRule.getPickerId() != null) {
            Staff pickerStaff = staffService.get(waveRule.getPickerId());
            waveRuleChCondition.setPickerName(pickerStaff != null ? pickerStaff.getName() : null);
        }
        if (waveRule.getWaveRuleGroupId() != null) {
            WaveRuleGroup waveRuleGroup = waveRuleGroupDao.queryById(staff, waveRule.getWaveRuleGroupId());
            waveRuleChCondition.setWaveRuleGroupName(waveRuleGroup != null ? waveRuleGroup.getName() : null);

        }
        WaveRuleCondition ruleCondition = waveRule.getRuleCondition();
        if (ruleCondition != null) {
            if (ruleCondition.getSpecifyUserCondition() != null && CollectionUtils.isNotEmpty(ruleCondition.getSpecifyUserCondition().getUserIds())) {
                List<Long> userIds = Lists.newArrayList(ruleCondition.getSpecifyUserCondition().getUserIds());
                String shopNames = queryShopNames(staff, userIds, shopService);
                waveRuleChCondition.setUserNames(ArrayUtils.toStringSet(shopNames));
            }
            if (ruleCondition.getSpecifyPlatformCondition() != null && CollectionUtils.isNotEmpty(ruleCondition.getSpecifyPlatformCondition().getSources())) {
                Set<String> sourceNames = ruleCondition.getSpecifyPlatformCondition().getSources().stream().map(WaveRuleCondition.SimpleSources::getSourceName).collect(Collectors.toSet());
                waveRuleChCondition.setSources(sourceNames);
            }
            if (ruleCondition.getSpecifySupplierCondition() != null && CollectionUtils.isNotEmpty(ruleCondition.getSpecifySupplierCondition().getSuppliers())) {
                List<Long> ids = ruleCondition.getSpecifySupplierCondition().getSuppliers().stream().map(WaveRuleCondition.SimpleSupplier::getId).collect(Collectors.toList());
                List<Supplier> suppliers = supplierService.queryByIds(staff, ids);
                waveRuleChCondition.setSupperNames(suppliers.stream().map(Supplier::getName).collect(toSet()));
            }
            if (ruleCondition.getSpecifyExpressTemplateCondition() != null && CollectionUtils.isNotEmpty(ruleCondition.getSpecifyExpressTemplateCondition().getExpressTemplates())) {
                Map<String, UserExpressTemplate> templateMap = userExpressTemplateService.getUserExpressWlbAllIncHiddenWithIdMap(staff);
                Set<String> templateNames = ruleCondition.getSpecifyExpressTemplateCondition().getExpressTemplates().stream().map(e -> getTemplateName(e.getTemplateType(), e.getTemplateId(), templateMap)).collect(toSet());
                waveRuleChCondition.setTemplateNames(templateNames);
            }
            if (ruleCondition.getSpecifyExpressTemplateCondition() != null && CollectionUtils.isNotEmpty(ruleCondition.getSpecifyExpressTemplateCondition().getExpressTemplates())) {
                Map<String, UserExpressTemplate> templateMap = userExpressTemplateService.getUserExpressWlbAllIncHiddenWithIdMap(staff);
                Set<String> templateNames = ruleCondition.getSpecifyExpressTemplateCondition().getExpressTemplates().stream().map(e -> getTemplateName(e.getTemplateType(), e.getTemplateId(), templateMap)).collect(toSet());
                waveRuleChCondition.setTemplateNames(templateNames);
            }
            if (ruleCondition.getSpecifyDistributorCondition() != null && CollectionUtils.isNotEmpty(ruleCondition.getSpecifyDistributorCondition().getDistributors())) {
                Set<WaveRuleCondition.Distributor> distributors = ruleCondition.getSpecifyDistributorCondition().getDistributors();

                List<Long> sourceIds = distributors.stream().map(WaveRuleCondition.Distributor::getCompanyId).collect(toList());
                waveRuleChCondition.setSourceNames(ArrayUtils.toStringSet(queryCompanyNames(staff, sourceIds, companyService)));

                List<Long> fxTaobaoIds = distributors.stream().map(WaveRuleCondition.Distributor::getUserId).flatMap(Set::stream).collect(toList());
                if (CollectionUtils.isNotEmpty(fxTaobaoIds)) {
                    waveRuleChCondition.setFxUserNames(ArrayUtils.toStringSet(queryShopNames(staff, fxTaobaoIds, shopService)));
                }
            }
            if (ruleCondition.getAutoPrinterId() != null) {
                Staff autoPrinter = staffService.get(ruleCondition.getAutoPrinterId());
                waveRuleChCondition.setAutoPrinterName(autoPrinter != null ? autoPrinter.getName() : null);
            }
            if (ruleCondition.getSpecifyShipperCondition() != null&& CollectionUtils.isNotEmpty(ruleCondition.getSpecifyShipperCondition().getSimpleShippers())) {
                Set<String> shipperNames = ruleCondition.getSpecifyShipperCondition().getSimpleShippers().stream().map(WaveRuleCondition.SimpleShipper::getShipperName).collect(toSet());
                waveRuleChCondition.setShipperNames(shipperNames);
            }
        }

    }

    /**
     * 根据店铺id查店铺名称,分销店铺也用这个方法
     */
    public static String queryShopNames(Staff staff, List<Long> userIds, IShopService shopService){
        if(CollectionUtils.isEmpty(userIds)){
            return EMPTY_WORD;
        }
        List<Shop> shopList = shopService.queryByUserIds(null, userIds.toArray(new Long[0]));
        if(CollectionUtils.isEmpty(shopList)){
            return EMPTY_WORD;
        }
        return shopList.stream().map(o-> StringUtils.isNotEmpty(o.getShortTitle()) ? o.getShortTitle() : o.getTitle()).collect(Collectors.joining(","));
    }

    /**
     * 根据公司id查询公司名称,查供分销公司直接用这个方法
     */
    public static String queryCompanyNames(Staff staff, List<Long> companyIds, ICompanyService companyService){
        if(CollectionUtils.isEmpty(companyIds)){
            return EMPTY_WORD;
        }
        List<Company> companyList = companyService.querComapanyListByIds(companyIds.toArray(new Long[0]));
        if(CollectionUtils.isEmpty(companyList)){
            return EMPTY_WORD;
        }
        return companyList.stream().map(Company::getName).collect(Collectors.joining(","));
    }

    protected OpLog buildOpLog(String ip, Staff staff, Domain domain, String action, String content, String key) {
        //记录操作日志
        OpLog log = new OpLog();
        log.setCompanyId(staff.getCompanyId());
        log.setKey(key);
        log.setDomain(domain.getValue());
        log.setAction(action);
        log.setContent(content);
        if(ip != null)
            log.setIp(ip);
        return log;
    }

    /**
     * 构建波次规则日志
     * @param buf
     * @param newWaveRule
     * @param oldWaveRule
     */
    private void buildWaveRuleLog(StringBuilder buf, WaveRule newWaveRule, WaveRule oldWaveRule) {
        if (oldWaveRule == null || newWaveRule == null) {
            return;
        }
        addLogContentWithNewValueNotNull(buf, "波次名称", newWaveRule.getName(), oldWaveRule.getName());
        addLogContentWithNewValueNotNull(buf, "参与批量生成波次", newWaveRule.getOpenBatchCreate(), oldWaveRule.getOpenBatchCreate());
        addLogContentWithNewValueNotNull(buf, "剩余订单合并波次数", newWaveRule.getTradeMergeNum(), oldWaveRule.getTradeMergeNum());
        addLogContentWithNewValueNotNull(buf, "单品单件波次：当波次中最后一笔订单的商品的其他订单", newWaveRule.getSameMergeNum(), oldWaveRule.getSameMergeNum());
        addLogContentWithNewValueNotNull(buf, "波次订单数下限", newWaveRule.getNumDown(), oldWaveRule.getNumDown());
        addLogContentWithNewValueNotNull(buf, "波次订单数上限", newWaveRule.getNumUp(), oldWaveRule.getNumUp());
        addLogContentWithNewValueNotNull(buf, "每个位置号循环播种", newWaveRule.getOpenLoopWave(), oldWaveRule.getOpenLoopWave());
        addLogContentWithNewValueNotNull(buf, "循环播种-虚拟位置号", newWaveRule.getVirtualNum(), oldWaveRule.getVirtualNum());
        addLogContentWithNewValueNotNull(buf, "循环播种-混合建选号", newWaveRule.getMixPickCode(), oldWaveRule.getMixPickCode());
        addLogContentWithNewValueNotNull(buf, "优先级", newWaveRule.getSort(), oldWaveRule.getSort());
        addLogContentWithNewValueNotNull(buf, "波次分组", newWaveRule.getWaveRuleGroupId(), oldWaveRule.getWaveRuleGroupId());
        addLogContentWithNewValueNotNull(buf, "相同快递", newWaveRule.getExpressEq(), oldWaveRule.getExpressEq());
        addLogContent(buf, "指定拣选员", newWaveRule.getPickerId(), oldWaveRule.getPickerId());
    }

    /**
     * 构建波次规则条件日志
     * @param buf
     * @param newRuleCondition
     * @param oldRuleCondition
     */
    private void buildWaveRuleConditionLog(StringBuilder buf, WaveRuleCondition newRuleCondition, WaveRuleCondition oldRuleCondition) {
        if (oldRuleCondition == null || newRuleCondition == null) {
            return;
        }
        addLogContent(buf, "自动波次订单数", newRuleCondition.getAutoWaveNumRange() != null ? newRuleCondition.getAutoWaveNumRange().toString() : null,
                oldRuleCondition.getAutoWaveNumRange() != null ? oldRuleCondition.getAutoWaveNumRange().toString() : null);
        addLogContent(buf, "波次位置号", newRuleCondition.getPositionCodeNumRange() != null ? newRuleCondition.getPositionCodeNumRange().toString() : null,
                oldRuleCondition.getPositionCodeNumRange() != null ? oldRuleCondition.getPositionCodeNumRange().toString() : null);
        addLogContent(buf, "相同店铺", newRuleCondition.getShopEq(), oldRuleCondition.getShopEq());
        addLogContent(buf, "指定店铺类型", newRuleCondition.getSpecifyUserCondition() != null ? newRuleCondition.getSpecifyUserCondition().getType() : null,
                oldRuleCondition.getSpecifyUserCondition() != null ? oldRuleCondition.getSpecifyUserCondition().getType() : null);
        addLogContent(buf, "指定店铺内容", newRuleCondition.getSpecifyUserCondition() != null ? newRuleCondition.getSpecifyUserCondition().getUserIds() : null,
                oldRuleCondition.getSpecifyUserCondition() != null ? oldRuleCondition.getSpecifyUserCondition().getUserIds() : null);
        addLogContent(buf, "相同商品", newRuleCondition.getItemEq(), oldRuleCondition.getItemEq());
        addLogContent(buf, "相同主商家编码", newRuleCondition.getItemSysIdEq(), oldRuleCondition.getItemSysIdEq());
        addLogContent(buf, "相同主商家编码", newRuleCondition.getItemSysIdEq(), oldRuleCondition.getItemSysIdEq());
        addLogContent(buf, "商品类型", newRuleCondition.getHasSuit(), oldRuleCondition.getHasSuit());
        addLogContent(buf, "套件商品计算规则", newRuleCondition.getSuitBySingle(), oldRuleCondition.getSuitBySingle());
        addLogContent(buf, "指定商品类型", newRuleCondition.getSpecifyItemCondition() != null ? newRuleCondition.getSpecifyItemCondition().getType() : null,
                oldRuleCondition.getSpecifyItemCondition() != null ? oldRuleCondition.getSpecifyItemCondition().getType() : null);
        addLogContent(buf, "指定商品内容", newRuleCondition.getSpecifyItemCondition() != null ? newRuleCondition.getSpecifyItemCondition().getItems() : null,
                oldRuleCondition.getSpecifyItemCondition() != null ? oldRuleCondition.getSpecifyItemCondition().getItems() : null);
        addLogContent(buf, "商品-排序", newRuleCondition.getSortRuleType(), oldRuleCondition.getSortRuleType());
        addLogContent(buf, "指定供应商类型", newRuleCondition.getSpecifySupplierCondition() != null ? newRuleCondition.getSpecifySupplierCondition().getType() : null,
                oldRuleCondition.getSpecifySupplierCondition() != null ? oldRuleCondition.getSpecifySupplierCondition().getType() : null);
        addLogContent(buf, "指定供应商内容", newRuleCondition.getSpecifySupplierCondition() != null ? newRuleCondition.getSpecifySupplierCondition().getSuppliers() : null,
                oldRuleCondition.getSpecifySupplierCondition() != null ? oldRuleCondition.getSpecifySupplierCondition().getSuppliers() : null);
        addLogContent(buf, "商品-有无品牌", newRuleCondition.getSpecifyItemBrandCondition() != null ? newRuleCondition.getSpecifyItemBrandCondition().isNoBrand() : null,
                oldRuleCondition.getSpecifyItemBrandCondition() != null ? oldRuleCondition.getSpecifyItemBrandCondition().isNoBrand() : null);
        addLogContent(buf, "商品-指定品牌内容", newRuleCondition.getSpecifyItemBrandCondition() != null ? buildValueToString(newRuleCondition.getSpecifyItemBrandCondition().getItemBrands()) : null,
                oldRuleCondition.getSpecifyItemBrandCondition() != null ? buildValueToString(oldRuleCondition.getSpecifyItemBrandCondition().getItemBrands()) : null);
        addLogContent(buf, "商品-分类类型", newRuleCondition.getSpecifyItemCategoryCondition() != null ? newRuleCondition.getSpecifyItemCategoryCondition().getType() : null,
                oldRuleCondition.getSpecifyItemCategoryCondition() != null ? oldRuleCondition.getSpecifyItemCategoryCondition().getType() : null);
        addLogContent(buf, "商品-分类内容", newRuleCondition.getSpecifyItemCategoryCondition() != null ? buildValueToString(newRuleCondition.getSpecifyItemCategoryCondition().getItemCategories()) : null,
                oldRuleCondition.getSpecifyItemCategoryCondition() != null ? buildValueToString(oldRuleCondition.getSpecifyItemCategoryCondition().getItemCategories()) : null);
        addLogContent(buf, "指定快递类型", newRuleCondition.getSpecifyExpressTemplateCondition() != null ? newRuleCondition.getSpecifyExpressTemplateCondition().getType() : null,
                oldRuleCondition.getSpecifyExpressTemplateCondition() != null ? oldRuleCondition.getSpecifyExpressTemplateCondition().getType() : null);
        addLogContent(buf, "指定快递内容", newRuleCondition.getSpecifyExpressTemplateCondition() != null ? newRuleCondition.getSpecifyExpressTemplateCondition().getExpressTemplates() : null,
                oldRuleCondition.getSpecifyExpressTemplateCondition() != null ? oldRuleCondition.getSpecifyExpressTemplateCondition().getExpressTemplates() : null);
        addLogContent(buf, "快递运单号", newRuleCondition.getOutSid(), oldRuleCondition.getOutSid());
        addLogContent(buf, "发票", newRuleCondition.getHasTaxNo(), oldRuleCondition.getHasTaxNo());
        addLogContent(buf, "订单标记", newRuleCondition.getTradeMark(), oldRuleCondition.getTradeMark());
        addLogContent(buf, "订单类型", newRuleCondition.getSpecifyTradeTypeCondition() != null ? newRuleCondition.getSpecifyTradeTypeCondition().getTradeTypeEnums() : null,
                oldRuleCondition.getSpecifyTradeTypeCondition() != null ? oldRuleCondition.getSpecifyTradeTypeCondition().getTradeTypeEnums() : null);
        addLogContent(buf, "包含标签类型", newRuleCondition.getSpecifyIncludeTradeTagCondition() != null ? newRuleCondition.getSpecifyIncludeTradeTagCondition().getType() : null,
                oldRuleCondition.getSpecifyIncludeTradeTagCondition() != null ? oldRuleCondition.getSpecifyIncludeTradeTagCondition().getType() : null);
        addLogContent(buf, "包含标签内容", newRuleCondition.getSpecifyIncludeTradeTagCondition() != null ? newRuleCondition.getSpecifyIncludeTradeTagCondition().getTags() : null,
                oldRuleCondition.getSpecifyIncludeTradeTagCondition() != null ? oldRuleCondition.getSpecifyIncludeTradeTagCondition().getTags() : null);
        addLogContent(buf, "排除标签类型", newRuleCondition.getSpecifyExcludeTradeTagCondition() != null ? newRuleCondition.getSpecifyExcludeTradeTagCondition().getType() : null,
                oldRuleCondition.getSpecifyExcludeTradeTagCondition() != null ? oldRuleCondition.getSpecifyExcludeTradeTagCondition().getType() : null);
        addLogContent(buf, "排除标签内容", newRuleCondition.getSpecifyExcludeTradeTagCondition() != null ? newRuleCondition.getSpecifyExcludeTradeTagCondition().getTags() : null,
                oldRuleCondition.getSpecifyExcludeTradeTagCondition() != null ? oldRuleCondition.getSpecifyExcludeTradeTagCondition().getTags() : null);
        addLogContent(buf, "分销商及分销商店铺类型", newRuleCondition.getSpecifyDistributorCondition() != null ? newRuleCondition.getSpecifyDistributorCondition().getType() : null,
                oldRuleCondition.getSpecifyDistributorCondition() != null ? oldRuleCondition.getSpecifyDistributorCondition().getType() : null);
        addLogContent(buf, "分销商及分销商店铺", newRuleCondition.getSpecifyDistributorCondition() != null ? buildValueToString(newRuleCondition.getSpecifyDistributorCondition().getDistributors()) : null,
                oldRuleCondition.getSpecifyDistributorCondition() != null ? buildValueToString(oldRuleCondition.getSpecifyDistributorCondition().getDistributors()) : null);
        addLogContent(buf, "bic质检订单送检方式", newRuleCondition.getBicDeliveryType(), oldRuleCondition.getBicDeliveryType());
        addLogContent(buf, "bic质检订单出仓方式", newRuleCondition.getBicShipType(), oldRuleCondition.getBicShipType());
        addLogContent(buf, "bic质检订单标记有赠品", newRuleCondition.getBicIsGift(), oldRuleCondition.getBicIsGift());
        addLogContent(buf, "留言/备注", newRuleCondition.getMessageMemoandOr(), oldRuleCondition.getMessageMemoandOr());
        addLogContent(buf, "卖家留言类型", newRuleCondition.getSpecifyBuyerMessageCondition() != null ? newRuleCondition.getSpecifyBuyerMessageCondition().getType() : null,
                oldRuleCondition.getSpecifyBuyerMessageCondition() != null ? oldRuleCondition.getSpecifyBuyerMessageCondition().getType() : null);
        addLogContent(buf, "卖家留言内容", newRuleCondition.getSpecifyBuyerMessageCondition() != null ? newRuleCondition.getSpecifyBuyerMessageCondition().getMessages() : null,
                oldRuleCondition.getSpecifyBuyerMessageCondition() != null ? oldRuleCondition.getSpecifyBuyerMessageCondition().getMessages() : null);
        addLogContent(buf, "卖家备注类型", newRuleCondition.getSpecifySellerMemoCondition() != null ? newRuleCondition.getSpecifySellerMemoCondition().getType() : null,
                oldRuleCondition.getSpecifySellerMemoCondition() != null ? oldRuleCondition.getSpecifySellerMemoCondition().getType() : null);
        addLogContent(buf, "卖家备注内容", newRuleCondition.getSpecifySellerMemoCondition() != null ? newRuleCondition.getSpecifySellerMemoCondition().getMemos() : null,
                oldRuleCondition.getSpecifySellerMemoCondition() != null ? oldRuleCondition.getSpecifySellerMemoCondition().getMemos() : null);
        addLogContent(buf, "商品数量下限", newRuleCondition.getItemNumDown(), oldRuleCondition.getItemNumDown());
        addLogContent(buf, "商品数量上限", newRuleCondition.getItemNumUp(), oldRuleCondition.getItemNumUp());
        addLogContent(buf, "包裹重量下限", newRuleCondition.getWeightDown(), oldRuleCondition.getWeightDown());
        addLogContent(buf, "包裹重量上限", newRuleCondition.getWeightUp(), oldRuleCondition.getWeightUp());
        addLogContent(buf, "包装体积下限", newRuleCondition.getVolumeDown(), oldRuleCondition.getVolumeDown());
        addLogContent(buf, "包装体积上限", newRuleCondition.getVolumeUp(), oldRuleCondition.getVolumeUp());
        addLogContent(buf, "商品种类下限", newRuleCondition.getItemKindsDown(), oldRuleCondition.getItemKindsDown());
        addLogContent(buf, "商品种类上限", newRuleCondition.getItemKindsUp(), oldRuleCondition.getItemKindsUp());
        addLogContent(buf, "订单金额下限", newRuleCondition.getPaymentDown(), oldRuleCondition.getPaymentDown());
        addLogContent(buf, "订单金额上限", newRuleCondition.getPaymentUp(), oldRuleCondition.getPaymentUp());
        addLogContent(buf, "相同常态合作码", newRuleCondition.getCooperationNoEq(), oldRuleCondition.getCooperationNoEq());
        addLogContent(buf, "相同送货仓库", newRuleCondition.getSellSiteEq(), oldRuleCondition.getSellSiteEq());
        addLogContent(buf, "相同货位", newRuleCondition.isSameGoodsSectionGroup(), oldRuleCondition.isSameGoodsSectionGroup());
        addLogContent(buf, "库区", newRuleCondition.getSectionAreaType(), oldRuleCondition.getSectionAreaType());
        addLogContent(buf, "指定货位类型", newRuleCondition.getSpecifyStockRegionCondition() != null ? newRuleCondition.getSpecifyStockRegionCondition().getType() : null,
                oldRuleCondition.getSpecifyStockRegionCondition() != null ? oldRuleCondition.getSpecifyStockRegionCondition().getType() : null);
        addLogContent(buf, "指定货位内容", newRuleCondition.getSpecifyStockRegionCondition() != null ? buildValueToString(newRuleCondition.getSpecifyStockRegionCondition().getRegions()) : null,
                oldRuleCondition.getSpecifyStockRegionCondition() != null ? buildValueToString(oldRuleCondition.getSpecifyStockRegionCondition().getRegions()) : null);
        addLogContent(buf, "货位可配库存不足时，仍允许生成波次", newRuleCondition.isAllowUnderstocked(), oldRuleCondition.isAllowUnderstocked());
        addLogContent(buf, "箱库存参与拣货", newRuleCondition.getBoxAllocate(), oldRuleCondition.getBoxAllocate());
        addLogContent(buf, "按照货位编码排序生成波次", newRuleCondition.getSortType(), oldRuleCondition.getSortType());
        addLogContent(buf, "波次拣选完成自动发货", newRuleCondition.getPickEndAutoConsign(), oldRuleCondition.getPickEndAutoConsign());
        addLogContent(buf, "包含商品标签类型", newRuleCondition.getSpecifyItemTagCondition() != null ? newRuleCondition.getSpecifyItemTagCondition().getType() : null,
                oldRuleCondition.getSpecifyItemTagCondition() != null ? oldRuleCondition.getSpecifyItemTagCondition().getType() : null);
        addLogContent(buf, "包含商品标签内容", newRuleCondition.getSpecifyItemTagCondition() != null ? newRuleCondition.getSpecifyItemTagCondition().getItemTagIds() : null,
                oldRuleCondition.getSpecifyItemTagCondition() != null ? oldRuleCondition.getSpecifyItemTagCondition().getItemTagIds() : null);
        addLogContent(buf, "排除商品标签内容", newRuleCondition.getSpecifyItemTagCondition() != null ? newRuleCondition.getSpecifyItemTagCondition().getExcludeItemTagIds() : null,
                oldRuleCondition.getSpecifyItemTagCondition() != null ? oldRuleCondition.getSpecifyItemTagCondition().getExcludeItemTagIds() : null);
    }


    private <T> String buildValueToString(T value) {
        if (value == null) {
            return null;
        }
        return value.toString();
    }

    private <T> void addLogContent(StringBuilder buf, String title, T newValue, T oldValue) {
        if (!Objects.equal(newValue, oldValue)) {
            if (oldValue == null) {
                buf.append(title).append(":").append(newValue).append("\n");
            } else {
                buf.append(title).append(":").append(oldValue).append("->").append(newValue).append("\n");
            }
        }
    }

    private <T> void addLogContentWithNewValueNotNull(StringBuilder buf, String title, T newValue, T oldValue) {
        if (newValue == null) {
            return;
        }
        addLogContent(buf, title, newValue, oldValue);
    }

    private void checkMultiAPlusNRules(List<WaveRule> origins, List<WaveRule> inserts) {
        long anAddCount = inserts.stream().filter(WaveUtils::isAPlusNRule).count();
        long originCount = Optional.ofNullable(origins).orElse(Collections.emptyList()).stream().filter(WaveUtils::isAPlusNRule).count();

        Assert.isTrue(anAddCount + originCount <= 1, "只能设置一个A+N波次规则！");
    }

    private static void checkMultiStallRules(List<WaveRule> origins, List<WaveRule> inserts) {
        long anAddCount = inserts.stream().filter(WaveUtils::isStallRule).count();
        long originCount = Optional.ofNullable(origins).orElse(Collections.emptyList()).stream().filter(WaveUtils::isStallRule).count();

        Assert.isTrue(anAddCount + originCount <= 1, "只能设置一个档口波次规则！");
    }

    private static void checkStallRuleBeforeSave(WaveRules waveRules) {
        if (waveRules == null) return;
        List<WaveRule> rules = waveRules.getList();
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }
        List<WaveRule> stallRules = rules.stream().filter(WaveUtils::isStallRule).collect(toList());
        if (CollectionUtils.isEmpty(stallRules)) {
            return;
        }
        Assert.isTrue(stallRules.size() == 1, "档口配货规则不能超过1个！");

        // 波次规则固定参数
        WaveRule rule = stallRules.get(0);
        WaveUtils.buildStallWaveRule(rule);
    }

    private void checkAPlusNRulesBeforeSave(WaveRules waveRules, List<WaveRule> origins) {
        List<WaveRule> rules = waveRules.getList();
        Map<Long, WaveRule> idOriginRuleMap = origins.stream().collect(Collectors.toMap(WaveRule::getId, java.util.function.Function.identity()));
        List<WaveRule> anRules = new ArrayList<>();
        for (WaveRule rule : rules) {
            if (rule.getId() == null && WaveUtils.isAPlusNRule(rule)) {
                anRules.add(rule);
                continue;
            }
            WaveRule existRule = idOriginRuleMap.get(rule.getId());
            if (existRule != null) {
                //档口规则暂时不支持修改类型 不传ruleType 设置原类型
                if (WaveUtils.isStallRule(existRule) || rule.getRuleType() == null) {
                    rule.setRuleType(existRule.getRuleType());
                    continue;
                }
                if (WaveUtils.isAPlusNRule(rule) && !WaveUtils.isStallRule(existRule)) {
                    anRules.add(rule);
                }
            }
        }

        if (CollectionUtils.isEmpty(anRules)) {
            return;
        }

        List<WaveRulePriority> priorities = Optional.ofNullable(waveRules.getRulePriorities()).orElse(Lists.newArrayList());
        for (WaveRule anRule : anRules) {
            Integer numDown = anRule.getNumDown();
            Integer numUp = anRule.getNumUp();
            //如果填固定值，表示删除，此时优先级列表没有这个规则，会自动删除
            if (numDown == null && anRule.getId() > 0L) {
                continue;
            }
            Assert.isTrue(numDown != null && numDown > 0, "请先设置设定值下限！");
            Assert.isTrue(numUp != null && numUp > 0, "请先设置设定值上限！");
            WaveRuleCondition condition = Optional.ofNullable(anRule.getRuleCondition()).orElse(new WaveRuleCondition());
            Integer itemKindsDown = condition.getItemKindsDown();
            Integer itemKindsUp = condition.getItemKindsUp();
            Assert.isTrue(itemKindsDown != null && itemKindsDown > 0, "请先设置商品种类下限！");
            Assert.isTrue(itemKindsUp != null && itemKindsUp > 0, "请先设置商品种类上限!");
            condition.setItemNumUp(2);
            condition.setItemNumDown(2);
            condition.setItemKindsUp(itemKindsUp);
            condition.setItemKindsDown(itemKindsDown);
            condition.setAutoWaveNumRange(new NumberRange<Integer>(anRule.getNumDown(), anRule.getNumUp()));
            condition.setSuitBySingle(1);
            anRule.setRuleType(WaveRule.RULE_TYPE_A_N);
            anRule.setRuleCondition(condition);
            //添加一个规则列表
            if (anRule.getId() != null && priorities.stream().map(WaveRulePriority::getSort).noneMatch(each -> each.equals(anRule.getSort()))) {
                priorities.add(0, new WaveRulePriority(anRule.getId(), anRule.getSort()));
            }
        }
        waveRules.setRulePriorities(priorities);
    }

    private void checkUpdateAndDeleteRule(Staff staff, List<Long> updateLoopWaves, List<Long> updateAndDeleteRuleIds) {
        List<Long> warehouseIds = queryWarehouseIds(staff);
        if (CollectionUtils.isNotEmpty(updateLoopWaves) && waveDao.checkNotFinishByRuleIds(staff, updateLoopWaves, warehouseIds) > 0) {
            throw new IllegalArgumentException("修改失败，原因：多波次轮播的规则下存在进行中的波次！");
        }
        if (updateAndDeleteRuleIds.size() > 0 && waveDao.checkNotFinishByRuleIds(staff, updateAndDeleteRuleIds, warehouseIds) > 0) {
            throw new IllegalArgumentException("存在未完结的波次，不能修改或删除或停用波次规则！");
        }
    }


    @Override
    public void deleteWaveRuleCache(Staff staff, Long ruleGroupId) {
        List<Warehouse> warehouses = warehouseService.queryAll(staff, 1);
        List<Long> warehouseIdList = Optional.ofNullable(warehouses).orElse(Collections.emptyList())
                .stream().map(Warehouse::getId).collect(Collectors.toList());
        this.deleteWaveRuleCache(staff, ruleGroupId, warehouseIdList);
    }

    @Override
    public List<Long> allocateTradeStockRegion(Staff staff, List<Long> sids, Integer stockRegionType) {
        Assert.notEmpty(sids, "订单传递错误，未找到任何订单信息!");
        Assert.notNull(stockRegionType, "trade's stockRegion not null!");
        StockRegionTypeEnum regionTypeEnum = StockRegionTypeEnum.getKey(stockRegionType);
        Assert.notNull(regionTypeEnum, "参数传递错误，未找到配货库区类型！");
        List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, false, sids.toArray(new Long[1]));
        Assert.notEmpty(trades, "订单传递错误，不能为空!");

        QueryAllocateGoodsRecordParams params = new QueryAllocateGoodsRecordParams.Builder().companyId(staff.getCompanyId())
                .sids(sids).build();
        List<AllocateGoodsRecord> records = wmsService.queryAllocateGoodsRecords(staff, params);
        Map<Long, List<AllocateGoodsRecord>> recordMap = CollectionUtils.isEmpty(records) ? Maps.newHashMap() : records.stream().collect(Collectors.groupingBy(AllocateGoodsRecord::getSid));

        List<Long> errorSids = new ArrayList<>();
        List<Trade> updateTrades = new ArrayList<>();
        List<TradeTrace> tradeTraces = new ArrayList<>();
        List<AllocateGoodsRecord> deleteRecords = new ArrayList<>();
        for (Trade trade : trades) {
            //已经进入波次的不允许修改
            if (trade.getWaveId() != null && trade.getWaveId() > 0L) {
                errorSids.add(trade.getSid());
                continue;
            }
            //同库区不做操作
            StockRegionTypeEnum oldRegionTypeEnum = Optional.ofNullable(StockRegionTypeEnum.getKey(trade.getStockRegionType())).orElse(StockRegionTypeEnum.STOCK_REGION_TYPE_0);
            if (regionTypeEnum.key.equals(oldRegionTypeEnum.key)) {
                continue;
            }

            //不同库区，清空配货记录，变更trades
            Optional.ofNullable(recordMap.get(trade.getSid())).ifPresent(deleteRecords::addAll);

            TbTrade update = new TbTrade();
            update.setSid(trade.getSid());
            update.setStockRegionType(regionTypeEnum.key);
            updateTrades.add(update);

            tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff, trade, "订单分配库区", "系统", new Date(), "修改配货库区：" + oldRegionTypeEnum.name + "->" + regionTypeEnum.name));
        }

        if (!deleteRecords.isEmpty()) {
            List<Long> deleteIds = deleteRecords.stream().map(AllocateGoodsRecord::getId).collect(Collectors.toList());
            wmsService.deleteAllocateGoodsRecords(staff, deleteIds);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLogHead(staff).append("分配配货库区[").append(stockRegionType).append("]删除配货记录:").append(deleteIds));
            }
        }

        if (!updateTrades.isEmpty()) {
            tradeServiceDubbo.updateTrades4Wave(staff, updateTrades);
            waveWriteTradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        }
        return errorSids;
    }


    @Override
    public void deleteWaveRuleCache(Staff staff, Long ruleGroupId, List<Long> warehouseIdList) {
        waveHelpBusiness.deleteWaveRuleCache(staff, ruleGroupId, warehouseIdList,null);
    }

    private boolean changeLoopWave(WaveRule origin, WaveRule update) {
        int originOpenLoopWave = origin.getOpenLoopWave();
        int updateOpenLoopWave = update.getOpenLoopWave() == null ? 0 : update.getOpenLoopWave();
        if (originOpenLoopWave != updateOpenLoopWave) {
            return true;
        }
        if (originOpenLoopWave == 0) {
            return false;
        }
        // 比较虚拟位置号
        if (!Objects.equal(origin.getVirtualNum(), update.getVirtualNum())) {
            return true;
        }
        // 比较混合拣选号
        if (origin.getOpenLoopWave() == 2 && !Objects.equal(origin.getMixPickCode(), update.getMixPickCode())) {
            return true;
        }
        Integer originMax = null;
        WaveRuleCondition condition = origin.getRuleCondition();
        if (condition != null) {
            NumberRange<Integer> numberRange = origin.getRuleCondition().getPositionCodeNumRange();
            if (numberRange != null) {
                originMax = WaveUtils.toInteger(numberRange.getMax());
            }
        }
        if (originMax == null) {
            originMax = 0;
        }
        Assert.notNull(update.getRuleCondition().getPositionCodeNumRange().getMax(), "位置号数量最大值不能为空");
        int updateMax = WaveUtils.toInteger(update.getRuleCondition().getPositionCodeNumRange().getMax());
        return originMax != updateMax;
    }

    private Map<Long, WaveRulePriority> checkRulePrioritiesBeforeSave(List<WaveRulePriority> rulePriorities) {
        Assert.notEmpty(rulePriorities, "波次规则优先级列表不能为空!");

        Set<Integer> sorts = Sets.newHashSetWithExpectedSize(rulePriorities.size());
        Map<Long, WaveRulePriority> ruleIdPriorityMap = Maps.newHashMapWithExpectedSize(rulePriorities.size());
        for (WaveRulePriority rulePriority : rulePriorities) {
            Assert.notNull(rulePriority.getSort(), "规则优先级不能为空！");
            Assert.isTrue(rulePriority.getSort() >= -1, "规则优先级应大于等于-1！");
            sorts.add(rulePriority.getSort());
            if (rulePriority.getRuleId() != null && rulePriority.getRuleId() > 0L) {
                ruleIdPriorityMap.put(rulePriority.getRuleId(), rulePriority);
            }
        }
        Assert.isTrue(sorts.size() == rulePriorities.size(), "规则优先级不能重复！");
        return ruleIdPriorityMap;
    }

    private void checkWaveRule(Staff staff, WaveRule waveRule, RulesCheckTools rulesCheckTools) {
        Assert.isTrue(StringUtils.isNotBlank(waveRule.getName()), "规则名称不能为空!");
        Assert.isTrue(waveRule.getName().length() <= 64, "规则名称长度超过限制！");
        Assert.notNull(waveRule.getSort(), "规则优先级不能为空！");
        Assert.isTrue(waveRule.getSort() >= -1, "规则优先级应大于-1！");
        WaveRuleCondition condition = waveRule.getRuleCondition();
        if (condition == null) {
            return;
        }

        // 如果设置了SameMergeNum，则商品数量上限为1
        if (waveRule.getSameMergeNum() != null && waveRule.getSameMergeNum() > 0) {
            Assert.isTrue(condition.getItemNumUp() != null && condition.getItemNumUp() == 1, waveRule.getName() + "：波次订单数的补充设置与商品数量最大值设置冲突，请重新设置！");
        }
        // 校验轮播信息
        if (waveRule.getOpenLoopWave() != null && waveRule.getOpenLoopWave() == 2) {
            Assert.isTrue(waveRule.getMixPickCode() != null && waveRule.getMixPickCode() != 0, "多波次轮播必须设置混合拣选号！");
            Assert.isTrue(waveRule.getMixPickCode() < 0, "混合拣选号必须小于0！");
        }
        //共享位置号与同商品不能同时存在，若存在，则优先取共享位置号
        if (BooleanUtils.isTrue(condition.getSharePosition())) {
            condition.setItemEq(null);
        }
        //未开启仓储不能设置库区和按货位排序
        if (BooleanUtils.isNotTrue(staff.getConf().isOpenWms())) {
            Assert.isNull(condition.getSectionAreaType(), "未开启仓储，不能设置库区条件！");
            Assert.isNull(condition.getSpecifyStockRegionCondition(), "未开启仓储，不能设置指定货位条件！");
            if (Objects.equal(condition.getSortType(), WaveRuleCondition.SORT_TYPE_SECTION)) {
                throw new IllegalArgumentException("未开启仓储，不能设置按照货位编码排序生成波次！");
            }
        } else {
            //开启指定货位的，校验货位信息
            if (condition.getSpecifyStockRegionCondition() != null) {
                checkWaveRuleSpecifyCode(staff, condition, rulesCheckTools);
            }
        }

        if (condition.getSpecifyIncludeTradeTagCondition() != null
                || condition.getSpecifyExcludeTradeTagCondition() != null) {
            condition.setSpecifyTradeTagCondition(null);
        }
        if (checkTradeTagConditionTypeEmpty(condition.getSpecifyIncludeTradeTagCondition())
                || checkTradeTagConditionTypeEmpty(condition.getSpecifyExcludeTradeTagCondition())) {
            throw new IllegalArgumentException("订单标签条件不能为空！");
        }
        //校验指定库区编码
        if (checkSectionAreaCombineCodes(condition.getSectionAreaCombineCodes())) {
            throw new IllegalArgumentException("指定库区组合重复！");
        }
        // 校验商品总数范围
        Integer slicingType = waveRule.getRuleCondition().getSlicingType();
        checkItemTotalNumRange(Objects.equal(slicingType, WaveSlicingEnum.ITEM_TOTAL.getValue()), condition.getAutoWaveNumRange());
        if (Objects.equal(slicingType, WaveSlicingEnum.ITEM_KIND.getValue()) || Objects.equal(slicingType, WaveSlicingEnum.ITEM_TOTAL.getValue())) {
            waveRule.setNumDown(WaveRule.DEFAULT_NUM_RANGE_MIN);
            waveRule.setNumUp(WaveRule.DEFAULT_POSITIONNO_NUM_RANGE_MAX);
        }
        if (Objects.equal(slicingType, WaveSlicingEnum.ITEM_KIND.getValue())) {
            checkItemKindNumRange(condition.getItemKindNumRange());
        }
    }

    private static void checkItemKindNumRange(NumberRange<Integer> itemKindNumRange) {
        Assert.isTrue(itemKindNumRange != null, "波次商品种类范围不能为空");
        Assert.isTrue(itemKindNumRange.getMin() != null, "波次商品种类下限不能为空");
        Assert.isTrue(itemKindNumRange.getMax() != null, "波次商品种类上限不能为空");
        Assert.isTrue(WaveUtils.toInteger(itemKindNumRange.getMin()) >= 0, "波次商品种类下限必须大于等于0");
        Assert.isTrue(WaveUtils.toInteger(itemKindNumRange.getMax()) >= WaveUtils.toInteger(itemKindNumRange.getMin()), "波次商品种类上限不能小于波次商品种类下限");
    }

    /**
     * 判断指定库区编码是否重复
     */
    private boolean checkSectionAreaCombineCodes(String sectionAreaCombineCodes){
        if (StringUtils.isBlank(sectionAreaCombineCodes)){
            return false;
        }
        Set<String> sectionAreaCombineCodeSet = WaveUtils.sectionAreaCombineCodesToStringSet(sectionAreaCombineCodes);
        List<String> sectionAreaCombineCodeList = WaveUtils.sectionAreaCombineCodesToStringList(sectionAreaCombineCodes);
        if (sectionAreaCombineCodeList.size() > sectionAreaCombineCodeSet.size()){
            return true;
        }
        HashSet<String> newSectionAreaCombineCodesSet = new HashSet<>(sectionAreaCombineCodeSet.size());
        for (String code : sectionAreaCombineCodeSet) {
            if (code.length() == 1) {
                newSectionAreaCombineCodesSet.add(code);
                continue;
            }
            List<String> collect = Arrays.stream(code.split(Wave.SEPARATOR_SECTION_AREA_CODE)).distinct().sorted().collect(toList());
            newSectionAreaCombineCodesSet.add(Joiner.on(Wave.SEPARATOR_SECTION_AREA_CODE).join(collect));
        }
        if (sectionAreaCombineCodeSet.size() > newSectionAreaCombineCodesSet.size()){
            return true;
        }
        return false;
    }

    /**
     * 校验商品总数范围
     * 商品总数量上限必须大于等于0， 商品总数量上限不能小于商品总数量下限
     * @param itemTotalNumRange
     */
    void checkItemTotalNumRange(Boolean openItemTotalNumRange, NumberRange<Integer> itemTotalNumRange) {
        if (BooleanUtils.isNotTrue(openItemTotalNumRange)) {
            return;
        }
        Assert.isTrue(itemTotalNumRange != null, "商品总数量范围不能为空");
        Assert.isTrue(itemTotalNumRange.getMin() != null, "商品总数量下限不能为空");
        Assert.isTrue(itemTotalNumRange.getMax() != null, "商品总数量上限不能为空");
        Assert.isTrue(WaveUtils.toInteger(itemTotalNumRange.getMin()) >= 0, "商品总数量下限必须大于等于0");
        Assert.isTrue(WaveUtils.toInteger(itemTotalNumRange.getMax()) >= WaveUtils.toInteger(itemTotalNumRange.getMin()), "商品总数量上限不能小于商品总数量下限");
    }

    /**
     * 判断订单标签条件是否为空
     */
    private boolean checkTradeTagConditionTypeEmpty(WaveRuleCondition.SpecifyTradeTagCondition specifyTradeTagCondition) {
        return specifyTradeTagCondition != null
                && CollectionUtils.isNotEmpty(specifyTradeTagCondition.getTags())
                && specifyTradeTagCondition.getType() == null;
    }

    private void checkWaveRuleSpecifyCode(Staff staff, WaveRuleCondition waveRuleCondition, RulesCheckTools rulesCheckTools) {
        WaveRuleCondition.SpecifyStockRegionCondition condition = waveRuleCondition.getSpecifyStockRegionCondition();
        if (condition == null) {
            return;
        }
        Assert.notNull(condition.getType(), "指定货位类型不能为空!");
        Set<WaveRuleCondition.SimpleStockRegion> regions = condition.getRegions();
        Assert.notEmpty(regions, "指定货位不能为空!");
        checkSimpleStockRegions(staff, rulesCheckTools, regions);

        Set<WaveRuleCondition.SimpleStockRegion> spareRegions = condition.getSpareRegions();
        if (SpecifyConditionTypeEnum.INCLUDE_MORE_EXCLUDE.equals(condition.getType())) {
            Assert.notEmpty(spareRegions, "排除货位不能为空!");
            checkSimpleStockRegions(staff, rulesCheckTools, spareRegions);
            //指定货位真包含排除货位
            for (WaveRuleCondition.SimpleStockRegion spareRegion : spareRegions) {
                Assert.isTrue(regions.stream().anyMatch(region -> spareRegion.getCode().startsWith(region.getCode()) && !spareRegion.getCode().equals(region.getCode())),
                        "排除货位必须以指定货位开头且范围比指定货位小");
            }
        }
    }

    /**
     * 检测指定货位编码是否符合规范
     * @param staff
     * @param rulesCheckTools
     * @param regions
     */
    private void checkSimpleStockRegions(Staff staff, RulesCheckTools rulesCheckTools, Set<WaveRuleCondition.SimpleStockRegion> regions){
        WmsConfig wmsConfig = rulesCheckTools.getWmsConfig();
        List<Warehouse> warehouses = rulesCheckTools.getWarehouses();
        Map<String, Integer> stockRegionTypeMaps = rulesCheckTools.getStockRegionTypeMaps();

        for (WaveRuleCondition.SimpleStockRegion region : regions) {
            String code = region.getCode();
            Assert.hasText(code, "指定具体货位不能为空!");
            String[] codes = code.split("-");
            String regionCode = codes[0];
            Assert.hasText(regionCode, "库区不能为空!");
            // 库区编码
            String stockRegionCode;
            if (!WmsUtils.isSectionCodeStoreDefault(staff)) {
                stockRegionCode = regionCode;
                Assert.isTrue(rulesCheckTools.getNotDefaultPattern().matcher(regionCode).matches(), "指定货位的库区编码格式不正确！");
                Assert.isTrue(stockRegionTypeMaps != null && stockRegionTypeMaps.get(stockRegionCode) != null, "指定货位[" + code + "]必须是拣货区!");
            }
        }
    }



    /**
     * 根据ids 查询波次规则列表
     *
     * @param ids 规则ids
     */
    @Override
    public List<WaveRule> queryRulesByIds(Staff staff, List<Long> ids) {
        return waveRuleDao.queryByIds(staff, ids);
    }

    /**
     * 删除波次规则
     *
     * @param id 规则id
     */
    @Override
    public int deleteRule(Staff staff, Long id) {
        return waveRuleDao.delete(staff, id);
    }

    @Override
    public List<WaveRule> queryRules(Staff staff) {
        return waveRuleDao.queryList(staff, null);
    }

    @Override
    public List<WaveRule> queryRules(Staff staff, WaveRule waveRuleQuery) {
        if (waveRuleQuery != null && BooleanUtils.isTrue(waveRuleQuery.getNeedRuleGroupPrivilege())) {
            waveRuleQuery.setNotInWaveRuleGroupIds(getWaveRuleGroupPrivilege(staff));
        }
        return waveRuleDao.queryList(staff, waveRuleQuery);
    }


    @Override
    public List<WaveRuleGroup> queryRuleGroups(Staff staff, WaveRuleGroup waveRuleGroup) {
        return queryRuleGroups(staff, waveRuleGroup, false);
    }

    /**
     * 查询波次分组，并判断是否需要返回未分组
     * @param staff
     * @param waveRuleGroup
     * @param ungrouped
     * @return
     */
    @Override
    public List<WaveRuleGroup> queryRuleGroups(Staff staff, WaveRuleGroup waveRuleGroup, boolean ungrouped) {
        List<Long> waveRuleGroupPrivilege = getWaveRuleGroupPrivilege(staff); // 不具备的波次分组权限
        waveRuleGroup.setNotInIds(waveRuleGroupPrivilege);
        List<WaveRuleGroup> result = Lists.newArrayList(); // 波次分组
        if (ungrouped && !waveRuleGroupPrivilege.contains(0L)) { // 是否返回不分组
            WaveRuleGroup ruleGroup = new WaveRuleGroup();
            ruleGroup.setCompanyId(staff.getCompanyId());
            ruleGroup.setId(0L);
            ruleGroup.setName("不分组");
            ruleGroup.setEnableStatus(true);
            result.add(ruleGroup);
        }
        List<WaveRuleGroup> list = waveRuleGroupDao.queryList(staff, waveRuleGroup); // 查询有权限的波次分组
        if (CollectionUtils.isNotEmpty(list)) {
            result.addAll(list);
        }

        return result;
    }

    /**
     * 根据ids 查询波次规则分组列表
     *
     * @param ids 规则ids
     */
    @Override
    public List<WaveRuleGroup> queryRuleGroupsByIds(Staff staff, List<Long> ids) {
        return waveRuleGroupDao.queryByIds(staff, ids);
    }

    @Override
    public void saveWaveRuleGroup(Staff staff, WaveRuleGroup waveRuleGroup) {
        Assert.hasText(waveRuleGroup.getName(), "波次分组名称不能为空！");
        WaveRuleGroup waveRuleGroupQuery = new WaveRuleGroup();
        waveRuleGroupQuery.setName(waveRuleGroup.getName());
        List<WaveRuleGroup> queryList = waveRuleGroupDao.queryList(staff, waveRuleGroupQuery);
        OpLog opLog;
        if (waveRuleGroup.getId() != null) {
            if (CollectionUtils.isNotEmpty(queryList) && !waveRuleGroup.getId().equals(queryList.get(0).getId())) {
                throw new IllegalArgumentException("已存在该分组名称！");
            }
            WaveRuleGroup oldGroup = waveRuleGroupDao.queryById(staff, waveRuleGroup.getId());
            waveRuleGroupDao.update(staff, waveRuleGroup);
            opLog = WaveRuleInfoLogEnum.buildOpLog(null, staff, String.format("更新波次规则：【波次分组】：%s -> %s", Optional.ofNullable(oldGroup).map(WaveRuleGroup::getName).orElse("无"), waveRuleGroup.getName()), waveRuleGroup.getId() + "");
        } else {
            if (CollectionUtils.isNotEmpty(queryList)) {
                throw new IllegalArgumentException("已存在该分组名称！");
            }
            waveRuleGroupDao.insert(staff, waveRuleGroup);
            opLog = WaveRuleInfoLogEnum.buildOpLog(null, staff, String.format("更新波次规则：【波次分组】：无 -> %s", waveRuleGroup.getName()), waveRuleGroup.getId() + "");
        }
        opLogService.record(staff, opLog);
    }

    @Override
    public void saveWaveRuleGroupList(Staff staff, List<WaveRuleGroup> waveRuleGroupList) {
        List<WaveRuleGroup> insertList = new ArrayList<>();
        List<WaveRuleGroup> updateList = new ArrayList<>();

        waveRuleGroupList.stream().forEach(waveRuleGroup -> {
            Assert.hasText(waveRuleGroup.getName(), "波次分组名称不能为空！");


            //TODO, 沿用之前的方法, 还有优化空间暂时未动
//            WaveRuleGroup waveRuleGroupQuery = new WaveRuleGroup();
//            waveRuleGroupQuery.setName(waveRuleGroup.getName());
//            List<WaveRuleGroup> queryList = waveRuleGroupDao.queryList(staff, waveRuleGroupQuery);
            if (waveRuleGroup.getId() != null) {
//                if (CollectionUtils.isNotEmpty(queryList) && !waveRuleGroup.getId().equals(queryList.get(0).getId())) {
//                    throw new IllegalArgumentException("已存在该分组名称！");
//                }
                updateList.add(waveRuleGroup);
//                waveRuleGroupDao.update(staff, waveRuleGroup);
            } else {
//                if (CollectionUtils.isNotEmpty(queryList)) {
//                    throw new IllegalArgumentException("已存在该分组名称！");
//                }
                insertList.add(waveRuleGroup);
//                waveRuleGroupDao.insert(staff, waveRuleGroup);
            }
        });
        waveRuleGroupDao.batchInsertOrUpdate(staff, insertList, updateList);
    }

    /**
     * 删除波次分组
     *
     * @param id 分组id
     */
    @Override
    public int deleteWaveRuleGroup(Staff staff, Long id) {
        WaveRule waveRule = new WaveRule();
        waveRule.setWaveRuleGroupId(id);
        List<WaveRule> waveRules = waveRuleDao.queryList(staff, waveRule);
        if (CollectionUtils.isNotEmpty(waveRules)) {
            throw new IllegalArgumentException("已有波次规则占用，不允许删除！");
        }
        return waveRuleGroupDao.delete(staff, id);
    }

    @Override
    public Map<String, Object> waveStatsWithTrades(Staff staff, Long warehouseId, Long waveRuleGroupId) {
        return waveStatsWithTrades(staff, warehouseId, waveRuleGroupId, DateRange.init(null, null, null));
    }

    /**
     * 生成波次页面的订单统计信息 + 波次统计信息
     *
     * @param warehouseId 仓库id
     */
    @Override
    public Map<String, Object> waveStatsWithTrades(Staff staff, Long warehouseId, Long waveRuleGroupId, DateRange dateRange) {
        Map<String, Object> map = Maps.newHashMap();
        WaveFilterParams params = new WaveFilterParams().setWarehouseId(warehouseId).setPrinted(false).setWithExcep(true).setPayTimeRange(dateRange);
        params.setUserIds(waveHelpBusiness.getActiveUserIds(staff));
        fillWaveStatInfo(staff, map, params);

        List<WaveRule> waveRules = getWaveRules(staff, waveRuleGroupId,true);
        fillSpecialInfo(staff, waveRules);
        List<WaveRule> rules = waveHelpBusiness.addAllRule(waveRules, staff);
        map.put("rules", rules);
        map.put("replenishItemKindNum", WmsUtils.isOpenWms(staff) ? wmsService.queryNeedReplenishItemKindCount(staff, warehouseId) : 0);
        map.put("generateTime", System.currentTimeMillis());
        return map;
    }

    private List<WaveRule> getWaveRules(Staff staff, Long waveRuleGroupId,boolean includeDisable) {
        WaveRule waveRuleQuery = new WaveRule();
        waveRuleQuery.setWaveRuleGroupId(waveRuleGroupId);
        waveRuleQuery.setIncludeDisable(includeDisable);
        List<WaveRule> waveRules = queryRules(staff, waveRuleQuery);

        //给全部波次规则根据账户的分组权限做一次过滤
        //调用dubbo服务获取账号没有的波次分组权限，作为一个集合保存，并用stream流过滤掉非账户权限内的波次规则
        List<Long> waveGroupIds = getWaveRuleGroupPrivilege(staff);
        waveRules=waveRules.stream().filter(waveRule -> !waveGroupIds.contains(waveRule.getWaveRuleGroupId())).collect(toList());
        return waveRules;
    }

    @Override
    public ProgressData waveStatsProgress(Staff staff, Long warehouseId, Long waveRuleGroupId, DateRange dateRange) throws CacheException {
        String key = WaveUtils.buildWaveStatsKey(staff, warehouseId, waveRuleGroupId, waveHelpBusiness.isOpenStaffCache(staff.getCompanyId()));
        if(!DateRange.isNull(dateRange)){
            return getProgressDataRealTime(staff, warehouseId, waveRuleGroupId, dateRange, key);
        }
        //加载规则统计信息
        if (waveCacheBusiness.add(key, WaveUtils.buildRuleStatsData(staff, key), 120)) {
            //没有缓存信息，默认设置进度条为0，前端接着轮询，到2状态终止
            //没有缓存则重新计算，这里是异步调用,dubbo接口异步调用
            WaveRule waveRuleQuery = new WaveRule();
            waveRuleQuery.setWaveRuleGroupId(waveRuleGroupId);

            tradeWaveQueryService.queryWaveAsync(staff, new WaveFilterParams().setWarehouseId(warehouseId)
                    .setWaveRuleGroupId(waveRuleGroupId).setWaveRuleQuery(waveRuleQuery).setPayTimeRange(dateRange));
            //返回状态值0，前端继续轮询
            return WaveUtils.buildRuleStatsData(staff, key);
        }else {
            //有缓存信息，查询进度条状态
            ProgressData progressData = cache.get(WaveUtils.buildWaveStatsKey(staff, warehouseId, waveRuleGroupId, waveHelpBusiness.isOpenStaffCache(staff.getCompanyId())));
            if (progressData == null) {
                logger.info(LogHelper.buildLog(staff,"waveStatsProgress 异常分支"));
                progressData = new ProgressData();
                progressData.setProgress(2);
            }
            return progressData;
        }
    }

    private ProgressData getProgressDataRealTime(Staff staff, Long warehouseId, Long waveRuleGroupId, DateRange dateRange, String key) {
        //实时查询
        List<WaveRule> waveRules = getWaveRules(staff, waveRuleGroupId,false);
        WaveRule waveRuleQuery = new WaveRule();
        waveRuleQuery.setWaveRuleGroupId(waveRuleGroupId);
        queryWave(staff, warehouseId, waveRules, waveRuleQuery, dateRange);

        ProgressData progressData = new ProgressData();
        progressData.setCountAll(10);
        progressData.setCountCurrent(progressData.getCountAll());
        progressData.setCacheKey(key);
        progressData.setProgress(2);
        progressData.setExecutResult(WaveHelpBusiness.buildSimpleRuleForStats(waveRules));
        return progressData;
    }

    @Override
    @SuppressWarnings("unchecked")
    public ProgressData waveNumStats(Staff staff, Long warehouseId, DateRange dateRange) throws CacheException {

        String cacheKey = ProgressEnum.PROGRESS_TRADE_WAVE_NUM_STATS.getKey() + "_" + staff.getCompanyId();
        if(waveCacheBusiness.add(cacheKey,WaveUtils.buildRuleStatsData(staff,cacheKey),120)){
            //异步调用
            ForkJoinPool.commonPool().execute(()-> queryWaveNumStats(staff,warehouseId,dateRange,cacheKey));
            queryWaveNumStats(staff, warehouseId, dateRange, cacheKey);
            //返回状态值0，前端继续轮询
            return WaveUtils.buildRuleStatsData(staff,cacheKey);
        }else {
            //有缓存信息，查询进度条状态
            ProgressData progressData = cache.get(cacheKey);
            if(progressData!=null){
                Integer progress = progressData.getProgress();
                if(progress!=null&&progress==2){
                    cache.delete(cacheKey);
                }
            }else {
                logger.info(LogHelper.buildLog(staff,"waveNumStats 查询结果为空,warehouseId:"+warehouseId));
            }
            return progressData;
        }
    }

    private void queryWaveNumStats(Staff staff, Long warehouseId, DateRange dateRange, String cacheKey) {
        try {
            Map<String, Object> map = Maps.newHashMap();
            WaveFilterParams params = new WaveFilterParams().setWarehouseId(warehouseId).setPrinted(false).setWithExcep(true).setPayTimeRange(dateRange);
            params.setUserIds(waveHelpBusiness.getActiveUserIds(staff));
            fillWaveStatInfo(staff, map, params);
            map.put("replenishItemKindNum", WmsUtils.isOpenWms(staff) ? wmsService.queryNeedReplenishItemKindCount(staff, warehouseId) : 0);
            ProgressData progressData = new ProgressData();
            progressData.setCacheKey(cacheKey);
            progressData.setCountAll(10);
            progressData.setCountCurrent(progressData.getCountAll());
            progressData.setProgress(2);
            progressData.setExecutResult(map);
            cache.set(cacheKey, progressData, 120);
        }catch (Exception e){
            logger.error(LogHelper.buildErrorLog(staff,e,"queryWaveNumStats fail."+String.format("warehouseId:{},dateRange:{},cacheKey:{}",warehouseId,JSON.toJSONString(dateRange),cacheKey)));
        }
    }

    private boolean needAsyncStats(Staff staff, long totalNum, long wavedCount, List<WaveRule> rules) {
        return (totalNum - wavedCount) > 2000 || rules.size() > 20;
    }

    private void queryWave(Staff staff, Long warehouseId, List<WaveRule> waveRules, WaveRule waveRuleQuery, DateRange payTimeRange) {
        List<WaveRule> resultRules = tradeWaveQueryService.queryWave(staff, new WaveFilterParams().setWarehouseId(warehouseId)
                .setWaveRuleQuery(waveRuleQuery).setNeedSort(false).setPayTimeRange(payTimeRange)).getWaveRules();
        Map<Long, WaveRule> resultMap = resultRules.stream().collect(Collectors.toMap(WaveRule::getId, a -> a, (k1, k2) -> k1));
        for (WaveRule rule : waveRules) {
            WaveRule resultRule = resultMap.get(rule.getId());
            if (resultRule != null) {
                rule.setTradeCount(resultRule.getTradeCount());
            }
        }
    }

    private void fillWaveStatInfo(Staff staff, Map<String, Object> map, WaveFilterParams params) {
        //判断是否开启pgl查询
        boolean openUsePgl = waveTradesQueryBusiness.openUsePgl(staff);
        if (openUsePgl) {
            try {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("波次PG查询，入参:warehouseId:%s, waveId:%s, multi:%s", params.getWarehouseId(), params.getWaveId(), params.getMulti())));
                }
                executeQuery(staff, map, params, openUsePgl);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "波次订单统计查询调用pgl失败！"), e);
                executeQuery(staff, map, params, false);
            }
        } else {
            executeQuery(staff, map, params, openUsePgl);
        }
    }

    public void executeQuery(Staff staff, Map<String, Object> map, WaveFilterParams params, boolean openUsePgl) {
        //排除唯一码订单
        List<Long> ignoreWaveIds = new ArrayList<>(Collections.singletonList(EfficientEnum.EFFICIENT_ORDER_UNIQUE_CODE.getTradeWaveId()));
        //排除盲扫订单
        ignoreWaveIds.add(-2L);
        params.setIgnoreWaveIds(ignoreWaveIds);
        params.setWaveId(null).setMulti(false);
        TradeWaveResult resultwave = openUsePgl ? dealExceptionByPgQuery(staff, params) : waveQueryDao.countStatWave(staff, params, null);
        long totalNum = resultwave.getTradesCount().longValue();
        map.put("totalNum", totalNum);
        if (DataUtils.checkLongNotEmpty(resultwave.getTradesCount().longValue())) {
            map.put("wavedCount", resultwave.getTradesWaveCount().longValue());
            params.setWaveId(0L).setMulti(true);
            params.setIgnoreWaveIds(null);
            map.put("multiUnCreateCount", openUsePgl ? dealExceptionByPgCount(staff, params) : waveQueryDao.statWave(staff, params, null).getTradesCount().longValue());
        } else {
            //已生成波次订单数
            map.put("wavedCount", 0L);
            // 多件未生成订单数
            map.put("multiUnCreateCount", 0);
        }
    }

    private TradeWaveResult dealExceptionByPgQuery(Staff staff, WaveFilterParams params) {
        try {
            return waveQueryPglDao.countStatWave(staff, params, null);
        } catch (NullPointerException e) {
            throw new IllegalArgumentException("用户所在库未配置pg数据源! ");
        }
    }

    private Long dealExceptionByPgCount(Staff staff, WaveFilterParams params) {
        try {
            return waveQueryPglDao.count(staff, params, null);
        } catch (NullPointerException e) {
            throw new IllegalArgumentException("用户所在库未配置pg数据源! ");
        }
    }

    @Override
    public PageListBase<DmjSku> queryAssignSkuList(Staff staff, ItemQueryParams queryParams, Long waveRuleId, Integer type) {
        PageListBase<DmjSku> skuPageList = new PageList<>();
        List<Long> sysItemIds = new ArrayList<Long>();
        List<Long> sysSkuIds = new ArrayList<Long>();
        WaveRule waveRule = waveRuleDao.queryById(staff, waveRuleId);
        WaveRuleCondition.SpecifyItemCondition specifyItemCondition;
        // 排除货位
        if (Objects.equal(type, EXCLUDE_ITEM)) {
            WaveRuleCondition ruleCondition = waveRule.getRuleCondition();
            // 初始化排除商品条件
            initSpecifyExcludeItemCondition(ruleCondition);
            specifyItemCondition = ruleCondition.getSpecifyExcludeItemCondition();
        } else { // 指定货位
            specifyItemCondition = waveRule.getRuleCondition().getSpecifyItemCondition();
        }
        if (null != specifyItemCondition) {
            Set<WaveRuleCondition.SimpleDmjItem> items = specifyItemCondition.getItems();
            for (WaveRuleCondition.SimpleDmjItem item : items) {
                sysItemIds.add(item.getSysItemId());
                sysSkuIds.add(item.getSysSkuId());
            }
            queryParams.setSysItemIds(sysItemIds);
            queryParams.setSysSkuIds(sysSkuIds);
            queryParams.setNeedTotal(true);
            return itemService.queryMiniItemList(staff, queryParams);
        }
        return null;
    }

    @Override
    public DmjItems queryAssignItemList(Staff staff, ItemQueryParams queryParams, Long waveRuleId) {
        List<Long> sysItemIds = new ArrayList<Long>();
        List<Long> sysSkuIds = new ArrayList<Long>();
        List<String> keyList = new ArrayList<>();
        //查询当前波次的sysItemIds
        WaveRule waveRule = waveRuleDao.queryById(staff, waveRuleId);
        WaveRuleCondition.SpecifyItemCondition specifyItemCondition = waveRule.getRuleCondition().getSpecifyItemCondition();
        if (null != specifyItemCondition) {
            Set<WaveRuleCondition.SimpleDmjItem> items = specifyItemCondition.getItems();
            for (WaveRuleCondition.SimpleDmjItem item : items) {
                sysItemIds.add(item.getSysItemId());
                sysSkuIds.add(item.getSysSkuId());
                keyList.add(item.getSysItemId() + "_" + item.getSysSkuId());
            }
            queryParams.setSysItemIds(sysItemIds);
            queryParams.setSysSkuIds(sysSkuIds);
            DmjItems dmjItems = itemService.search(staff, queryParams);
            if (CollectionUtils.isEmpty(dmjItems.getList())) {
                return dmjItems;
            }
            //筛选
            List<DmjItem> result = new ArrayList<>();
            for (DmjItem item : dmjItems.getList()) {
                List<DmjSku> skuList = new ArrayList<>();
                if (item.getIsSkuItem() == 1) {
                    for (DmjSku sku : item.getSkuERP()) {
                        if (keyList.contains(sku.getSysItemId() + "_" + sku.getSysSkuId())) {
                            skuList.add(sku);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(skuList)) {
                        item.setSkuERP(skuList);
                        result.add(item);
                    }
                } else {
                    if (keyList.contains(item.getSysItemId() + "_0")) {
                        result.add(item);
                    }
                }
            }
            dmjItems.setList(result);
            return dmjItems;
        }
        return null;
    }

    /**
     * 填充波次规则中的特殊信息
     */
    private void fillSpecialInfo(Staff staff, List<WaveRule> waveRules) {
        if (CollectionUtils.isEmpty(waveRules)) {
            return;
        }

        List<WaveRuleCondition.SimpleTradeTag> simpleTradeTags = Lists.newArrayList();
        for (WaveRule waveRule : waveRules) {
            WaveRuleCondition ruleCondition = waveRule.getRuleCondition();
            if (ruleCondition != null) {
                WaveUtils.add2SimpleTradeTags(simpleTradeTags, ruleCondition.getSpecifyTradeTagCondition());
                WaveUtils.add2SimpleTradeTags(simpleTradeTags, ruleCondition.getSpecifyExcludeTradeTagCondition());
                WaveUtils.add2SimpleTradeTags(simpleTradeTags, ruleCondition.getSpecifyIncludeTradeTagCondition());
                // 初始化排除商品条件
                initSpecifyExcludeItemCondition(ruleCondition);
            }
        }

        if (!simpleTradeTags.isEmpty()) {
            Set<Long> tagIds = Sets.newHashSet();
            for (WaveRuleCondition.SimpleTradeTag tag : simpleTradeTags) {
                tagIds.add(tag.getId());
            }
            List<TradeTag> tradeTags = tradeServiceDubbo.queryTradeTagByIds(staff, tagIds.toArray(new Long[0]));
            Map<Long, String> tagIdNameMap = Maps.newHashMapWithExpectedSize(tagIds.size());
            for (TradeTag tradeTag : tradeTags) {
                tagIdNameMap.put(tradeTag.getId(), tradeTag.getTagName());
            }
            tagIdNameMap.put(-1L, "无标签");
            for (WaveRuleCondition.SimpleTradeTag simpleTradeTag : simpleTradeTags) {
                String name = tagIdNameMap.get(simpleTradeTag.getId());
                if (name != null) {
                    simpleTradeTag.setTagName(name);
                }
            }
        }
    }

    /**
     * 初始化排除商品条件
     * 原客户配置的【指定商品】中【排除商品】，新需求发布需要将CheckBox【排除商品】勾选，同时在配置的商品带入已添加数据中；
     */
    private static void initSpecifyExcludeItemCondition(WaveRuleCondition ruleCondition) {
        WaveRuleCondition.SpecifyItemCondition specifyExcludeItemCondition = ruleCondition.getSpecifyExcludeItemCondition();
        WaveRuleCondition.SpecifyItemCondition specifyItemCondition = ruleCondition.getSpecifyItemCondition();
        if (specifyItemCondition != null && Objects.equal(specifyItemCondition.getType(), SpecifyConditionTypeEnum.EXCLUDE)) {
//            ruleCondition.setSpecifyItemCondition(null); 暂时不清，防止切环境
            if (specifyExcludeItemCondition == null) {
                ruleCondition.setSpecifyExcludeItemCondition(specifyItemCondition);
            }
        }
    }

    /**
     * 填充快递模板名称
     *
     * @param waves 波次
     */
    @Override
    public void fillTemplateName(Staff staff, List<Wave> waves) {
        Map<String, UserExpressTemplate> templateMap = userExpressTemplateService.getUserExpressWlbAllIncHiddenWithIdMap(staff);
        /**
         * 跨境物流商
         */
        Map<Long,UserExpressTemplate> abroadMap= getAbroadTemplate(staff);
        for (Wave w : waves) {
            UserExpressTemplate userExpressTemplate= abroadMap.get(w.getExpressTemplateId());
            if(userExpressTemplate!=null){
                w.setExpressTemplateName(userExpressTemplate.getName());
            }else{
                w.setExpressTemplateName(getTemplateName(w.getExpressTemplateType(), w.getExpressTemplateId(), templateMap));
            }
        }
    }

    @Override
    public void fillTradeTemplateName(Staff staff, List<Trade> trades) {
        Map<String, UserExpressTemplate> templateMap = userExpressTemplateService.getUserExpressWlbAllIncHiddenWithIdMap(staff);

        for (Trade trade : trades) {
            trade.setTemplateName(getTemplateName(trade.getTemplateType(), trade.getTemplateId(), templateMap));
        }
    }

    private String getTemplateName(Integer templateType, Long templateId, Map<String, UserExpressTemplate> templateMap) {
        if (DataUtils.checkLongNotEmpty(templateId)) {
            UserExpressTemplate template = templateMap.get((templateType == 0 ? "" : "w") + templateId);
            if (template != null) {
                return template.getName();
            }
        }

        return "";
    }

    /**
     * 填充统计信息
     *
     * @param staff
     * @param waves
     */
    private void fillStatisticsInfo(Staff staff, List<Wave> waves) {
        List<Long> pickingIds = new ArrayList<Long>(waves.size());
        for (Wave wave : waves) {
            Long pickingId = wave.getPickingId();
            if (null != pickingId) {
                pickingIds.add(pickingId);
            }
        }
        if (CollectionUtils.isEmpty(pickingIds)) {
            return;
        }
        Map<Long, Long> pickingIdItemKindCountMap = waveSortingDao.queryPickingIdItemKindCountMap(staff, pickingIds);
        Map<Long, Long> pickingIdTradesCountMap = waveSortingDao.queryPickingIdTradesCountMap(staff, pickingIds);
        Map<Long, BigDecimal> pickingIdItemCountMap = waveSortingDao.queryPickingIdItemCountMap(staff, pickingIds);
        for (Wave wave : waves) {
            Long pickingId = wave.getPickingId();
            if (null == pickingId) {
                continue;
            }
            Long itemKindCount = pickingIdItemKindCountMap.get(pickingId);
            Long tradesCount = pickingIdTradesCountMap.get(pickingId);
            BigDecimal itemCount = pickingIdItemCountMap.get(pickingId);
            wave.setItemKindNum(null != itemKindCount ? itemKindCount.intValue() : 0);
            if (null != tradesCount && WavePickUtils.isTradeWave(wave)) {
                wave.setTradesCount(tradesCount.intValue());
            }
            if (null != itemCount) {
                wave.setItemCount(itemCount.intValue());
            }
        }
    }

    @Override
    public List<Long> querySidsByWaveIds(Staff staff, List<Long> waveIds, WaveFilterParams params, Page page) {
        WaveFilterParams filterParams = new WaveFilterParams();

        if (CollectionUtils.isNotEmpty(waveIds)) {
            List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
            Assert.notEmpty(waves, "波次不存在！");
            filterParams.setWaveIds(waves.stream().map(Wave::getId).collect(Collectors.toList()));
            filterParams.setQueryWarehouseIds(waves.stream().map(Wave::getWarehouseId).collect(Collectors.toList()));
        }

        filterParams.setPrinted(params.getPrinted())
                .setWithExcep(params.getWithExcep())
                .setIgnoreStatus(params.getIgnoreStatus())
                .setOrderByItem(params.getOrderByItem())
                .setUserIds(params.getUserIds())
                .setKey(params.getKey());
        if (page != null) {
            filterParams.setStartRow(page.getStartRow()).setPageSize(page.getPageSize());
        }
        return waveQueryDao.queryWaveSids(staff, filterParams);
    }

    @Override
    public List<Long> querySidsByWaveId(Staff staff, Long waveId, WaveFilterParams params, Page page) {
        if (waveId != null && waveId > 0L) {
            Wave wave = waveDao.queryById(staff, waveId);
            if (wave == null) {
                throw new IllegalArgumentException("该波次不存在！");
            }
            params.setWarehouseId(wave.getWarehouseId());
        }

        WaveFilterParams filterParams = new WaveFilterParams()
                .setWaveId(waveId)
                .setUseTradeNotConsignTable(params.isUseTradeNotConsignTable())
                .setWarehouseId(params.getWarehouseId())
                .setPrinted(params.getPrinted())
                .setWithExcep(params.getWithExcep())
                .setIgnoreStatus(params.getIgnoreStatus())
                .setOrderByItem(params.getOrderByItem())
                .setUserIds(params.getUserIds())
                .setKey(params.getKey())
                .setPayTimeRange(params.getPayTimeRange())
                .setSids(params.getSids())
                .setOutSids(params.getOutSids())
                .setShortIdsList(params.getShortIdsList())
                .setIsExcep(params.getIsExcep())
                .setIsPackage(params.getIsPackage())
                .setTemplateIds(params.getTemplateIds())
                .setGroupByTrade(params.isGroupByTrade())
                .setLogisticsCompanyIdList(params.getLogisticsCompanyIdList())
                .setNotInWaveReason(params.getNotInWaveReason());

        if (StringUtils.isNotBlank(params.getItemNumSort())){
            filterParams.setItemNumSort(params.getItemNumSort());
        }

        if (page != null) {
            filterParams.setStartRow(page.getStartRow()).setPageSize(page.getPageSize());
        }
        return waveQueryDao.queryWaveSids(staff, filterParams);
    }

    /**
     * 根据波次id查询 sid列表
     *
     * @param waveId 波次id
     */
    @Override
    public List<Long> querySidsByWaveId4Print(Staff staff, Long waveId, boolean printed) {
        return querySidsByWaveId(staff, waveId, new WaveFilterParams().setPrinted(printed ? null : printed).setWithExcep(false), null);
    }

    @Override
    public List<Long> querySidsByWaveIds4Print(Staff staff, List<Long> waveIds, boolean printed) {
        return querySidsByWaveIds(staff, waveIds, new WaveFilterParams().setPrinted(printed ? null : printed).setWithExcep(false), null);
    }

    @Override
    public List<Long> querySidsByWaveIdAndExcep(Staff staff, Long waveId, Integer isExcep) {
        return querySidsByWaveId(staff, waveId, new WaveFilterParams().setWithExcep(true).setIsExcep(isExcep).setIgnoreStatus(true), null);
    }

    /**
     * 根据波次id查询 sid列表
     *
     * @param waveId 波次id
     * @param groupByTrade 查询订单时是否需要group by
     */
    @Override
    public List<Long> querySidsByWaveId(Staff staff, Long waveId, Long warehouseId, boolean groupByTrade) {
        WaveFilterParams waveFilterParams = new WaveFilterParams().setWithExcep(true).setIgnoreStatus(true).setGroupByTrade(groupByTrade);
        if (warehouseId != null) {
            waveFilterParams.setWarehouseId(warehouseId);
        }
        return querySidsByWaveId(staff, waveId, waveFilterParams, null);
    }

    @Override
    public List<Long> querySidsByWaveId(Staff staff, Long waveId) {
        return querySidsByWaveId(staff, waveId, null, false);
    }

    /**
     * 根据波次id查询 待包装验货的 sid列表
     *
     * @param waveId 波次id
     */
    @Override
    public List<Long> querySids4PackByWaveId(Staff staff, Long waveId) {
        return querySidsByWaveId(staff, waveId, new WaveFilterParams().setWithExcep(false).setIgnoreStatus(true).setIsExcep(0).setIsPackage(0), null);
    }

    @Override
    public List<Long> querySidsByWaveIds(Staff staff, List<Long> waveIds) {
        return querySidsByWaveIds(staff, waveIds, new WaveFilterParams().setWithExcep(true).setIgnoreStatus(true), null);
    }

    @Override
    public List<Trade> queryTradesByWaveIds(Staff staff, List<Long> waveIds) {
        WaveFilterParams waveFilterParams = new WaveFilterParams().setWithExcep(true).setIgnoreStatus(true);
        if (CollectionUtils.isEmpty(waveIds)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isNotEmpty(waveIds)) {
            List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
            Assert.notEmpty(waves, "波次不存在！");
            waveFilterParams.setWaveIds(waves.stream().map(Wave::getId).collect(Collectors.toList()));
            waveFilterParams.setQueryWarehouseIds(waves.stream().map(Wave::getWarehouseId).collect(Collectors.toList()));
        }
        return waveQueryDao.queryWaveTrades(staff, waveFilterParams, null, null, false);
    }

    @Override
    public Long queryWaveTradeCount(Staff staff, Long waveId, WaveFilterParams params) {
        WaveFilterParams queryParams = new WaveFilterParams()
                .setWaveId(waveId)
                .setPrinted(params.getPrinted())
                .setWithExcep(params.getWithExcep())
                .setIgnoreStatus(params.getIgnoreStatus())
                .setUserIds(params.getUserIds())
                .setKey(params.getKey());
        return waveQueryDao.queryWaveTradeCount(staff, queryParams);
    }

    @Override
    public List<Long> querySidsByWaveId4Print(Staff staff, Long waveId) {
        if (null == waveId || waveId < 1L) {
            return Collections.emptyList();
        }
        Wave wave = waveDao.queryById(staff, waveId);
        if (null == wave || wave.getStatus() != Wave.STATUS_CREATED) {
            return Collections.emptyList();
        }
        //未打印订单
        List<Long> sids = waveQueryDao.queryWaveSids(staff, new WaveFilterParams().setPrinted(false).setWaveId(waveId).setWarehouseId(wave.getWarehouseId()).setIgnoreStatus(true));
        return sids;
    }

    /**
     * 校验过滤波次
     * @param staff
     * @param waveIds
     * @param isWaveAutoPrint
     * @return
     */
    @Override
    public List<Wave> filterWaveList(Staff staff, List<Long> waveIds, boolean isWaveAutoPrint) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return new ArrayList<>();
        }
        List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));

        if (CollectionUtils.isEmpty(waves)) {
            if (isWaveAutoPrint) {
                logger.info(LogHelper.buildLog(staff, "[波次自动打印] 波次不存在"));
                return new ArrayList<>();
            } else {
                throw new IllegalArgumentException("[波次自动打印] 波次不存在");
            }
        }

        List<Wave> cancelWave = waves.stream().filter(wave -> WavePickUtils.isTradeWave(wave) && wave.getStatus() == Wave.STATUS_DELETED).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cancelWave) && !isWaveAutoPrint) {
            List<Long> collect = cancelWave.stream().map(Wave::getId).collect(toList());
            String waveIdString = StringUtils.join(collect, ",");
            throw new IllegalArgumentException("所选波次"+waveIdString+"状态为已取消，不支持打印！");
        }

        if (isWaveAutoPrint) {
            waves = waves.stream().filter(wave -> WavePickUtils.isTradeWave(wave) && wave.getStatus() == Wave.STATUS_CREATED).collect(Collectors.toList());
        } else {
            waves = waves.stream().filter(wave -> WavePickUtils.isTradeWave(wave) && (wave.getStatus() == Wave.STATUS_CREATED || wave.getStatus() == Wave.STATUS_FINISHED)).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(waves)) {
            if (isWaveAutoPrint) {
                logger.info(LogHelper.buildLog(staff, "[波次自动打印] 所选波次中的订单均不满足打印条件，请重新勾选"));
                return new ArrayList<>();
            } else {
                throw new IllegalArgumentException("所选波次中的订单均不满足打印条件，请重新勾选");
            }
        }
        Assert.isTrue(waves.stream().map(Wave::getWarehouseId).distinct().count() == 1, "请选择同一个仓库下的波次！");
        return waves;
    }

    @Override
    public void addWavePickingStatLogOnlyEs(Staff staff, WavePicking old, WavePicking update, String source) {
        try {
            if (update == null || update.getId() == null) {
                return;
            }
            if (old == null) {
                old = wavePickingDao.queryById(staff, update.getId());
            }
            if (old == null) {
                return;
            }
            String content = String.format("修改波次拣选统计数据,source:%s,[PlanPickNum:%s -> %s, PickedNum:%s -> %s, UnPickNum:%s -> %s, ShortageNum:%s -> %s] ",
                    source, old.getPlanPickNum(), update.getPlanPickNum(), old.getPickedNum(), update.getPickedNum(), old.getUnPickNum(), update.getUnPickNum(), old.getShortageNum(), update.getShortageNum());
            addWaveTraceLog(staff, old.getWaveId(), WaveTraceOperateEnum.WAVE_PICKING_STAT, content);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "输出拣选统计信息失败"), e);
        }
    }

    private Map<Long, UserLogisticsCompany> getLogisticsCompanyMap(Staff staff, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        LogisticsCompanyExpressTemplateListRequest request = new LogisticsCompanyExpressTemplateListRequest();
        request.setStaff(staff);
        request.setLogisticsCompanyIds(ids);
        List<UserLogisticsCompany> logisticsCompanyList = userLogisticsCompanyBusiness.queryByIds(staff, ids, false);
        if (CollectionUtils.isEmpty(logisticsCompanyList)) {
            return null;
        }
        return logisticsCompanyList.stream().collect(Collectors.toMap(UserLogisticsCompany::getId, java.util.function.Function.identity(), (t1, t2) -> t1));
    }
    @Override
    public void fillWaveLogisticsCompanyName(Staff staff, List<Wave> waves) {
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }
        List<Long> logisticsIdList = waves.stream().filter(wave -> wave.getLogisticsCompanyId() != null && wave.getLogisticsCompanyId() > 0).map(Wave::getLogisticsCompanyId).collect(Collectors.toList());
        Map<Long, UserLogisticsCompany> logisticsCompanyMap = getLogisticsCompanyMap(staff, logisticsIdList);
        if (logisticsCompanyMap == null || logisticsCompanyMap.size() == 0) {
            return;
        }
        for (Wave wave : waves) {
            if (wave.getLogisticsCompanyId() == null) {
                continue;
            }
            if (logisticsCompanyMap.get(wave.getLogisticsCompanyId()) == null) {
                continue;
            }
            wave.setLogisticsCompanyName(logisticsCompanyMap.get(wave.getLogisticsCompanyId()).getName());
        }
    }

    @Override
    public void fillTradeLogisticsCompanyName(Staff staff, List<Trade> tradeList) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        List<Long> logisticsIdList = tradeList.stream().filter(trade -> trade.getLogisticsCompanyId() != null && trade.getLogisticsCompanyId() > 0).map(Trade::getLogisticsCompanyId).collect(Collectors.toList());
        Map<Long, UserLogisticsCompany> logisticsCompanyMap = getLogisticsCompanyMap(staff, logisticsIdList);
        if (logisticsCompanyMap == null || logisticsCompanyMap.size() == 0) {
            return;
        }
        for (Trade trade : tradeList) {
            if (trade.getLogisticsCompanyId() == null) {
                continue;
            }
            if (logisticsCompanyMap.get(trade.getLogisticsCompanyId()) == null) {
                continue;
            }
            trade.setLogisticsCompanyName(logisticsCompanyMap.get(trade.getLogisticsCompanyId()).getName());
        }
    }

    @Override
    public void fillTradeExpressCompanyName(Staff staff, List<Trade> tradeList) {
        List<Long> templateIds = tradeList.stream().filter(trade -> trade.getTemplateId() != null && trade.getTemplateId() > 0).map(Trade::getTemplateId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(templateIds)) {
            List<UserWlbExpressTemplate> userWlbExpressTemplates = userWlbExpressTemplateService.getUserExpressTemplateIdNameExpressNeme(staff, templateIds.toArray(new Long[templateIds.size()]));
            Map<Long, UserWlbExpressTemplate> expressTemplateMap = userWlbExpressTemplates.stream().collect(Collectors.toMap(UserWlbExpressTemplate::getId, java.util.function.Function.identity(), (k1, k2) -> k2));
            for (Trade trade : tradeList) {
                UserWlbExpressTemplate userWlbExpressTemplate = expressTemplateMap.get(trade.getTemplateId());
                if(userWlbExpressTemplate!=null) {
                    //快递公司信息
                    trade.setExpressName(userWlbExpressTemplate.getExpressName());
                }
            }
        }
    }

    @Override
    public List<Trade> queryTradesByWaveIds4Print(Staff staff, List<Long> waveIds, int maxSize, boolean showdetail, boolean isWaveAutoPrint) {
        return queryTradesByWaveIds4Print(staff, waveIds, maxSize, showdetail, isWaveAutoPrint, false);
    }

    @Override
    public List<Trade> queryTradesByWaveIds4Print(Staff staff, List<Long> waveIds, int maxSize ,boolean showdetail, boolean isWaveAutoPrint, boolean fromController) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return Collections.emptyList();
        }

        List<Wave> waves = filterWaveList(staff, waveIds, isWaveAutoPrint);
        if (CollectionUtils.isEmpty(waves)) {
            logger.info(LogHelper.buildLog(staff, "[波次自动打印] 所选波次中的订单均不满足打印条件，请重新勾选"));
            return new ArrayList<>();
        }
        List<Long> sids = waveQueryDao.queryWaveSids(staff, new WaveFilterParams().setWaveIds(waveIds).setWarehouseId(waves.get(0).getWarehouseId()).setIgnoreStatus(true));
        if (isWaveAutoPrint && CollectionUtils.isEmpty(sids)) {
            logger.info(LogHelper.buildLog(staff, "[波次自动打印] 所选波次中的订单均不满足打印条件，请重新勾选"));
            return new ArrayList<>();
        }
        String waveIdString = StringUtils.join(waveIds, ",");
        Assert.notEmpty(sids, "所选波次"+waveIdString+"中的订单均为异常订单，不支持打印！(注：异常订单包含有异常、待付款、待审核、待财审、待人工审核、已踢出波次等状态订单)");
        if (maxSize > 0) {
            Assert.isTrue(sids.size() <= maxSize, String.format("单次批量打印仅支持%s条订单，请重新选择波次", maxSize));
        }
        List<Trade> trades = Lists.newArrayListWithCapacity(sids.size());
        //分批次查询trades
        sids = sids.stream().distinct().sorted(Long::compareTo).collect(Collectors.toList());
        for ( List<Long> sid : Lists.partition(sids,BATCH_SIZE)){
            if (fromController) {
                try {
                    List<Trade> trade = waveUseTradeServiceProxy.queryBySidsNoFilter(staff, showdetail, sid.toArray(new Long[0]));
                    trade = tradeServiceDubbo.fillTradeTemplate(staff, trade);
                    trades.addAll(trade);
                } catch (TradeFilterException e) {
                    logger.error(LogHelper.buildLog(staff, "DefaultTemplateFilter#filterTrades失败！"));
                }
            } else {
                List<Trade> trade = waveUseTradeServiceProxy.queryBySids(staff, showdetail, true, true, sid.toArray(new Long[0]));
                trades.addAll(trade);
            }
        }

        List<Trade> results = Lists.newArrayListWithCapacity(trades.size());
        Map<Long, List<Trade>> waveIdTradesMap = trades.stream().collect(Collectors.groupingBy(Trade::getWaveId));
        for (Map.Entry<Long, List<Trade>> entry : waveIdTradesMap.entrySet()) {
            List<WaveTrade> waveTrades = waveTradeDao.queryByWaveIdAndSids(staff, entry.getKey(), entry.getValue().stream().map(Trade::getSid).toArray(Long[]::new));
            Map<Long, WaveTrade> sidWaveTradeMap = waveTrades.stream().collect(Collectors.toMap(WaveTrade::getSid, v -> v, (v1, v2) -> v2));
            entry.getValue().sort((left, right) -> {
                WaveTrade leftWaveTrade = sidWaveTradeMap.get(left.getSid());
                WaveTrade rightWaveTrade = sidWaveTradeMap.get(right.getSid());
                if (leftWaveTrade == null && rightWaveTrade == null) {
                    return 0;
                } else if (leftWaveTrade == null) {
                    return 1;
                } else if (rightWaveTrade == null) {
                    return -1;
                } else {
                    return leftWaveTrade.getId().compareTo(rightWaveTrade.getId());
                }
            });
        }

        waves.sort(Comparator.comparing(Wave::getId).reversed());
        for (Wave wave : waves) {
            List<Trade> subTrades = waveIdTradesMap.get(wave.getId());
            if (subTrades != null) {
                results.addAll(waveIdTradesMap.get(wave.getId()));
            }
        }
        return results;
    }

    @Override
    public List<Trade> queryWaveTradeByCondition(Staff staff, WaveFilterTradeParams params) {
        if(params.isBackendWaveNotInReasonQuery()){
            return waveQueryDao.queryWaveTradeIncludeConsign(staff,params);
        }else {
            return waveQueryDao.queryWaveTradeByCondition(staff, params);
        }
    }


    @Override
    public void cancelInsufficientByWave(Staff staff, List<Long> waveIds) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return;
        }
        WaveConfig waveConfig = waveConfigService.get(staff);
        int isAllow = waveConfig.getInteger(WaveChatConfigsEnum.SUPPORT_INSUFFICIENT_TRADE_WAVE_PRINT.getKey());
        if (isAllow == 0) {
            return;
        }
        Page page = new Page(1, 1000);
        List<Trade> trades;
        List<Long> insufficientSids = Lists.newArrayList();
        WaveFilterParams params = new WaveFilterParams().setWaveIds(waveIds).setWithExcep(true).setIsExcep(1);
        while (!(trades = waveQueryDao.queryWaveTrades(staff, params, null, page, true)).isEmpty()) {
            for (Trade trade : trades) {
                if (Objects.equal(trade.getIsExcep(), CommonConstants.JUDGE_YES) && Trade.STOCK_STATUS_INSUFFICIENT.equals(trade.getStockStatus())) {
                    insufficientSids.add(trade.getSid());
                }
            }
            if (trades.size() < page.getPageSize()) {
                break;
            }
            page.setPageNo(page.getPageNo() + 1);
        }
        doCancelWaveTradeInsufficient(staff, insufficientSids);
    }

    @Override
    public void cancelInsufficientBySid(Staff staff, List<Long> sids) {
        WaveConfig waveConfig = waveConfigService.get(staff);
        int isAllow = waveConfig.getInteger(WaveChatConfigsEnum.SUPPORT_INSUFFICIENT_TRADE_WAVE_PRINT.getKey());
        if (isAllow == 0) {
            return;
        }
        List<Trade> tradeList = waveUseTradeServiceProxy.queryBySids(staff, false, false, true, sids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        List<Long> insufficientSids = Lists.newArrayList();
        for (Trade trade : tradeList) {
            if (Objects.equal(trade.getIsExcep(), CommonConstants.JUDGE_YES) && Trade.STOCK_STATUS_INSUFFICIENT.equals(trade.getStockStatus())) {
                insufficientSids.add(trade.getSid());
            }
        }
        doCancelWaveTradeInsufficient(staff, insufficientSids);
    }

    private void doCancelWaveTradeInsufficient(Staff staff, List<Long> insufficientSids) {
        if (CollectionUtils.isEmpty(insufficientSids)) {
            return;
        }
        logger.debug(LogHelper.buildLog(staff, String.format("波次批量打印订单[%s]自动取消缺货", insufficientSids)));
        try {
            for (List<Long> sid : Lists.partition(insufficientSids,BATCH_SIZE)) {
                Map<String, String> errMap = tradeServiceDubbo.cancelInsufficient(staff, OpEnum.WAVE_PRINT_INSUFFICIENT_CANCEL, sid.toArray(new Long[0]));
                if (errMap != null && !errMap.isEmpty()) {
                    logger.warn(LogHelper.buildLog(staff, String.format("取消缺货异常存在异常返回，订单号：%s，错误map：%s", sid, errMap)));
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "取消缺货异常失败！"), e);
        }
    }

    /**
     * 更新波次的状态
     *
     * @param wave 波次
     */
    @Override
    public void updateWaveStatus(Staff staff, Wave wave) {
        Wave update = new Wave();
        update.setId(wave.getId());
        update.setStatus(wave.getStatus());
        waveDao.update(staff, update);
    }

    @Override
    public void updateWavePickListPrintStatus(Staff staff, List<Wave> waves) {
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }
        List<Wave> updates = new ArrayList<>();
        for (Wave wave : waves) {
            Wave update = new Wave();
            update.setId(wave.getId());
            update.setPickListPrintStatus(wave.getPickListPrintStatus());
            updates.add(update);
        }
        waveDao.batchUpdate(staff, updates);
    }

    @Override
    public void batchUpdateWaveRemark(Staff staff, WaveBatchUpdateRemarkParams params){
        List<Wave> waves = params.getWaveList();
        Integer append = params.getAppend();
        Assert.notEmpty(waves, "波次信息不能为空！");
        List<Wave> filterWaves = waves.stream().filter(w -> DataUtils.checkLongNotEmpty(w.getId()) && w.getCustomerName() != null).collect(Collectors.toList());
        Assert.notEmpty(filterWaves, "无可更新都波次！");
        //批量查询波次
        Long[] ids = filterWaves.stream().map(Wave::getId).distinct().toArray(Long[]::new);
        Map<Long, Wave> oldWaveMap = Optional.ofNullable(waveDao.queryByIds(staff, ids)).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(Wave::getId, a->a));

        List<Wave> updates = Lists.newArrayList();
        List<WaveTrace> waveTraces = Lists.newArrayList();
        for (Wave wave : waves) {
            // 1. 参数校验
            Assert.isTrue(DataUtils.checkLongNotEmpty(wave.getId()), "波次Id不能为空！");
            Assert.isTrue(WmsUtils.validateRemarkLength(wave.getCustomerName(), MAX_WAVE_REMARK), "波次备注最大字符数超过限制！");
            // 2. 更新波次波次备注，插入修改日志
            Wave oldWave = oldWaveMap.get(wave.getId());
            Wave update = new Wave();
            update.setId(wave.getId());
            update.setCustomerName(wave.getCustomerName());
            //追加备注
            if (WaveBatchUpdateRemarkParams.APPEND.equals(append)) {
                update.setCustomerName(StringUtils.defaultIfEmpty(oldWave.getCustomerName(),"") + wave.getCustomerName());
            }

            Assert.notNull(oldWave, "根据波次Id未查询到波次！");
            createWaveTraces(staff, append, waveTraces, update, oldWave);
            updates.add(update);
        }
        if (CollectionUtils.isNotEmpty(updates)) {
            waveDao.batchUpdate(staff, updates);
            waveTraceService.batchAddWaveTrace(staff, waveTraces);
        }
    }

    private void createWaveTraces(Staff staff, Integer append, List<WaveTrace> waveTraces, Wave update, Wave oldWave) {
        if (WaveBatchUpdateRemarkParams.COVER.equals(append)) {
            waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, update.getId(), WaveTraceOperateEnum.WAVE_UPDATE_REMARK, "修改波次备注，修改前：" + WmsUtils.getNullToEmpty(oldWave.getCustomerName()) + "，修改后：" + WmsUtils.getNullToEmpty(update.getCustomerName())));
        } else {
            waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, update.getId(), WaveTraceOperateEnum.WAVE_UPDATE_REMARK, "修改波次备注，修改前：" + WmsUtils.getNullToEmpty(oldWave.getCustomerName()) + "，修改后：" + WmsUtils.getNullToEmpty(oldWave.getCustomerName())+WmsUtils.getNullToEmpty(update.getCustomerName())));
        }
    }

    @Override
    public void updateWaveRemakes(Staff staff, List<Wave> waves) {
        batchUpdateWaveRemark(staff, new WaveBatchUpdateRemarkParams(waves, WaveBatchUpdateRemarkParams.COVER));
    }

    @Override
    @Transactional
    public void giveUpPick(Staff staff, List<Long> waveIds, boolean cancelWave) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return;
        }
        List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
        Assert.notEmpty(waves, "根据波次Id未查询到波次！");
        List<Wave> filterWaves = cancelWave ? waves : waves.stream().filter(w -> Objects.equal(w.getDistributionStatus(), Wave.DISTRIBUTION_STATUS_ON)).collect(Collectors.toList());
        Assert.notEmpty(filterWaves, "波次非拣选中状态，不允许放弃拣选！");
        for (Wave wave : filterWaves) {
            try {
                if (Objects.equal(wave.getDistributionStatus(), Wave.DISTRIBUTION_STATUS_NONE)) {
                    continue;
                }
                WavePicking picking = wavePickingDao.getByWaveId(staff, wave.getId());
                if (picking == null || !DataUtils.checkLongNotEmpty(picking.getId())) {
                    continue;
                }
                // 处理分拣信息：交易波次删除，其他波次清空
                if (WaveUtils.isTradeWave(wave.getPickingType())) {
                    waveSortingService.deleteWaveSortingByPickingId(staff, picking.getId());
                } else {
                    waveSortingService.clearSortingDetails(staff, picking.getId());
                }
                tradePostPrintService.cancelWavePicking(staff, wave.getId());
                if (!cancelWave) {
                    wmsService.giveUpPick(staff, wave.getId());
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "PC放弃拣选异常，波次Id：" + wave.getId()), e);
                throw new RuntimeException("波次放弃拣选发生异常，信息:" + e.getMessage());
            }
        }
        // 取消拣选 商品日志输出
        addItemTraceLogByCancelPick(staff, filterWaves);
        List<Long> filterWaveIds = filterWaves.stream().map(Wave::getId).collect(Collectors.toList());
        if (cancelWave && CollectionUtils.isNotEmpty(filterWaveIds)) {
            this.cancelWaves(staff, filterWaveIds, null);
        }
    }

    /**
     * 修改波次的配货状态
     *
     * @param waveId             波次id
     * @param distributionStatus 波次配货状态
     */
    @Override
    @Transactional
    public void updateDistributionStatus(Staff staff, Long waveId, Integer distributionStatus) {
        Wave wave = waveDao.queryById(staff, waveId);
        if (wave != null) {
            Wave update = new Wave();
            if (WaveDistributionStatus.SEEDING.getValue().equals(distributionStatus)) {
                if (wave.getSeedStartTime() == null) {
                    update.setSeedStartTime(new Date());
                }
                update.setClearSeedEndTime(1);
            }
            update.setId(wave.getId());
            update.setDistributionStatus(distributionStatus);
            waveDao.update(staff, update);
        } else {
            throw new IllegalArgumentException("该波次不存在！");
        }
    }

    @Override
    public void batchUpdateDistributionStatus(Staff staff, List<Long> waveIds, Integer distributionStatus) {
        List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
        List<Wave> updates = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(waves)) {
            waves.forEach(w -> {
                Wave update = new Wave();
                update.setId(w.getId());
                update.setDistributionStatus(distributionStatus);
                if (WaveDistributionStatus.WAIT_SEED.getValue().equals(distributionStatus) || WaveDistributionStatus.PICKING.getValue().equals(distributionStatus)) {
                    update.setClearSeedStartTime(1);
                    update.setClearSeedEndTime(1);
                }
                updates.add(update);
            });
        } else {
            throw new IllegalArgumentException("该波次不存在！");
        }
        if (CollectionUtils.isNotEmpty(updates)) {
            waveDao.batchUpdate(staff, updates);
        }
    }

    @Override
    public void updateOutsidStatus(Staff staff, Long waveId, Integer outsidStatus) {
        Wave wave = waveDao.queryById(staff, waveId);
        if (wave != null) {
            Wave update = new Wave();
            update.setId(wave.getId());
            update.setOutsidStatus(outsidStatus);
            waveDao.update(staff, update);
        } else {
            throw new IllegalArgumentException("该波次不存在！");
        }
    }

    @Override
    public void updateCheckGoodsFinished(Staff staff, List<Wave> waves) {
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }

        List<Wave> updates = Lists.newArrayListWithCapacity(waves.size());
        for (Wave wave : waves) {
            Wave update = new Wave();
            update.setId(wave.getId());
            update.setCheckGoodsFinished(wave.getCheckGoodsFinished());
            updates.add(update);
        }
        waveDao.batchUpdate(staff, updates);
    }

    @Override
    public List<Wave> queryWaveByRule(Staff staff, Long ruleId, Long warehouseId) {
        return queryWaveByRule(staff, ruleId, warehouseId, null);
    }

    @Override
    public List<Wave> queryWaveByRule(Staff staff, Long ruleId, Long warehouseId, DateRange payTimeRange) {
        return queryWaveByRule(staff, ruleId, warehouseId, payTimeRange, null);
    }

    /**
     * 根据波次规则以及仓库id查询波次分组
     *
     * @param ruleId      波次规则 id
     * @param warehouseId 仓库id
     */
    @Override
    public List<Wave> queryWaveByRule(Staff staff, Long ruleId, Long warehouseId, DateRange payTimeRange, WaveRule waveRule) {
        WaveRule rule = (waveRule == null ? waveRuleDao.queryById(staff, ruleId) : waveRule);
        Assert.notNull(rule, "该波次规则不存在或已删除！");
        WaveRule ruleQuery = new WaveRule();
        ruleQuery.setId(rule.getId());
        WaveFilterParams waveFilterParams = new WaveFilterParams().setWarehouseId(warehouseId).setWaveRuleQuery(ruleQuery).setNeedSort(true).setPayTimeRange(payTimeRange);
        if (waveRule != null) {
            waveFilterParams.setRules(Lists.newArrayList(waveRule));
        }
        List<Wave> waves = tradeWaveQueryService.queryWave(staff, waveFilterParams).getWaves();
        if (BooleanUtils.isTrue(rule.getExpressEq())) {
            fillTemplateName(staff, waves);
        }
        if (rule.getRuleCondition() != null && BooleanUtils.isTrue(rule.getRuleCondition().getLogisticsCompanyEq())) {
            fillWaveLogisticsCompanyName(staff, waves);
        }
        return waves;
    }

    /**
     * 波次分组权限
     * @param staff
     * @return
     */
    private List<Long> getWaveRuleGroupPrivilege(Staff staff) {
        return staff.isDefaultStaff() ? Lists.newArrayList() : staffService.queryWaveGroupByStaff(staff);
    }

    @Override
    public PageListBase<Wave> queryCreatedWaves(Staff staff, WaveFilterParams params, Page page) {
        PageListBase<Wave> pageList = new PageList<Wave>();
        if (StringUtils.isEmpty(params.getStatus())) {
            params.setStatus(String.valueOf(Wave.STATUS_CREATED));
        }

        //填充根据系统单号/内部单号/平台单号/快递单号查询到的waveId集合
        if (StringUtils.isNotEmpty(params.getQuerySids()) || StringUtils.isNotEmpty(params.getQueryOutSids())
                || StringUtils.isNotEmpty(params.getQueryTids()) || StringUtils.isNotEmpty(params.getQueryShortIds()) || StringUtils.isNotEmpty(params.getQueryKey())){
            //查询订单信息并获取波次ID
            List<Long> waveIdLIst = queryTradeByMixKey(staff, params);
            if (CollectionUtils.isEmpty(waveIdLIst)){
                pageList.setTotal(0L);
                pageList.setPage(page);
                pageList.setList(new ArrayList<>());
                return pageList;
            }
            params.setWaveIds(waveIdLIst);
        }

        if (SOURCE_TYPE_PDA.equals(params.getSourceType())) {
            //pda波次拣选 兼容档口波次查询
            if(PickingType.STALL_V2.getValue().toString().equals(params.getPickingTypes())){
                WaveRule ruleParam = new WaveRule();
                ruleParam.setRuleType(WaveRule.RULE_TYPE_STALL);
                List<WaveRule> waveRules = this.queryRules(staff, ruleParam);
                if (!waveRules.isEmpty()) {
                    params.setPickingTypes(null);
                    params.setRuleIds(String.valueOf(waveRules.get(0).getId()));
                }
            }
        }

        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (waveConfig != null && waveConfig.getInteger(WaveChatConfigsEnum.ONE_WAVE_MULTI_PICKING_CODE.getKey()) != 0) {
            params.setQueryMultiPickingCode(1);
        }
        params.setOpenWaveUniqueCode(waveConfig.getOpenWaveUniqueCode());
        params.setExpressMap(dealExpressParam(staff, params.getExpress()));
        if (params.getRuleGroupId() != null || StringUtils.isNotEmpty(params.getRuleGroupIds())) {
            Boolean isHandle = handleRuleGroupIds(staff, params, page, pageList);
            if (!isHandle) {
                pageList.setTotal(0L);
                pageList.setPage(page);
                pageList.setList(new ArrayList<>());
                return pageList;
            }
        }
        params.setNotInWaveRuleGroupIds(getWaveRuleGroupPrivilege(staff));
        params.setOpenPrintStatusV2(waveHelpBusiness.openPrintStatusV2(staff));
        params.setAccurateStatWaveTradeNum(waveHelpBusiness.isAccurateStatWaveTradeNum(staff));
        fillParamsSysAItemSkuIds(staff, params);
        //货主查询条件转换
        if(StringUtils.isNotEmpty(params.getSpecifyShipperIds())){
            params.setShipperIds(Strings.getAsStringList(params.getSpecifyShipperIds(),",",true));
        }
        if(params.getPickerRoleMatch()!=null&&params.getPickerRoleMatch()){
            //拣货过滤角色
            Map<Long, String> pickerRoleName = waveHelpBusiness.getPickerRoleName(staff, false);
            if(!pickerRoleName.isEmpty()){
                params.setAssignPickerRoleIds(new ArrayList<>(pickerRoleName.keySet()));
            }
        }
        long count = waveDao.count(staff, params);
        pageList.setTotal(count);
        pageList.setPage(page);
        if (page == null || count > page.getStartRow()) {
            if (!"PDA".equals(params.getSourceType())) {
                params.setWaveSortRule(waveConfig.getWaveSortRule());
                params.setWaveSortTime(waveConfig.getWaveSortTime());
            }
            List<Wave> list = waveDao.queryPageList(staff, params, page);
            if (CollectionUtils.isNotEmpty(list)) {
                filterWithNum(staff, list.iterator(), params, tradeConfig);
                Boolean isShowPrintStatus = StringUtils.isNotBlank(params.getPrintStatus()) || BooleanUtils.isTrue(params.getIsFillInfo());
                fillWaveInfo(staff, list, isShowPrintStatus, true);
                fillWaveOutSidStatus(staff, list,params.getPageId());
                //判断波次标签是否含有组团标签，并设置为组团波次类型
                list.stream().filter(wave -> WaveUtils.containsWaveTag(wave.getTagIds(), WaveTagEnum.SAME_ITEM.getId()))
                        .forEach(wave -> wave.setPickingType(PickingType.GROUP.getValue()));
            }
            checkWaveStatistics(staff, list, params);
            fillPrintedTradeNum(staff, list, WAVE_LIST_COLUMN_PAGE_ID);
            fillMultiPickingCode(staff, list, params);
            fillWaveSendPrintStatus(staff, list);
            waveHelpBusiness.assembleShipperInfo(list,staff);
            //如果拣货人是角色，处理下拣选人
            fillWavePickerRole(staff,list);
            pageList.setList(list);
        } else {
            pageList.setList(new ArrayList<>());
        }

        return pageList;
    }

    /**
     * 四合一查询订单信息，并返回订单所在波次号ID
     * @param staff
     * @param params
     * @return
     */
    public List<Long> queryTradeByMixKey(Staff staff, WaveFilterParams params){
        List<Long> waveIds = new ArrayList<>();
        //判断是不是四合一查询
        if (StringUtils.isNotEmpty(params.getQueryKey())){
            Trade trade = tradeServiceDubbo.queryByMixKey(staff, params.getQueryKey());
            if (ObjectUtil.isNotEmpty(trade) && ObjectUtil.isNotEmpty(trade.getWaveId())){
                waveIds.add(trade.getWaveId());
            }
        } else {
            //如果没有配置，默认支持最大查询数量1000
            int pageSize = DEFAULT_MAX_PAGE_NUM;
            //读取最大查询数量配置
            String queryTradeMixKeyPageSize = config.getProperty("query_trade_mix_key_page_size");
            if (StringUtils.isNotEmpty(queryTradeMixKeyPageSize)){
                pageSize = Integer.parseInt(queryTradeMixKeyPageSize);
            }

            //按系统单号查询
            if (StringUtils.isNotEmpty(params.getQuerySids())){
                checkNum(params.getQuerySids(), "请输入正确的系统单号");
                List<Long> querySids = ArrayUtils.toLongList(params.getQuerySids());
                Assert.isTrue(querySids.size() <= pageSize, "每次最多只能查询" + pageSize + "行数据!");
                for (List<Long> sids : Lists.partition(querySids, DEFAULT_MAX_PAGE_NUM)) {
                    TradeQueryParams tradeQueryParams = new TradeQueryParams();
                    tradeQueryParams.setSid(sids.toArray(new Long[0]));
                    tradeQueryParams.setPage(new Page(Page.DEFAULT_PAGE_NUM,DEFAULT_MAX_PAGE_NUM));
                    Trades trades = tradeServiceDubbo.search(staff, tradeQueryParams);
                    if (ObjectUtil.isNotEmpty(trades) && CollectionUtil.isNotEmpty(trades.getList())){
                        List<Long> waveIdList = trades.getList().stream().map(Trade::getWaveId).filter(ObjectUtil::isNotEmpty).collect(toList());
                        waveIds.addAll(waveIdList);
                    }
                }
            }

            //按订单短号查询
            if (StringUtils.isNotEmpty(params.getQueryShortIds())){
                checkNum(params.getQueryShortIds(), "请输入正确的内部单号");
                List<Long> queryShortIds = ArrayUtils.toLongList(params.getQueryShortIds());
                Assert.isTrue(queryShortIds.size() <= pageSize, "每次最多只能查询" + pageSize + "行数据!");
                for (List<Long> shortIds : Lists.partition(queryShortIds, DEFAULT_MAX_PAGE_NUM)) {
                    TradeQueryParams tradeQueryParams = new TradeQueryParams();
                    tradeQueryParams.setShortId(shortIds.toArray(new Long[0]));
                    tradeQueryParams.setPage(new Page(Page.DEFAULT_PAGE_NUM,DEFAULT_MAX_PAGE_NUM));
                    Trades trades = tradeServiceDubbo.search(staff, tradeQueryParams);
                    if (ObjectUtil.isNotEmpty(trades) && CollectionUtil.isNotEmpty(trades.getList())){
                        List<Long> waveIdList = trades.getList().stream().map(Trade::getWaveId).filter(ObjectUtil::isNotEmpty).collect(toList());
                        waveIds.addAll(waveIdList);
                    }
                }
            }

            //按订单快递单号查询
            if (StringUtils.isNotEmpty(params.getQueryOutSids())){
                List<String> queryOutSids = ArrayUtils.toStringList(params.getQueryOutSids());
                Assert.isTrue(queryOutSids.size() <= pageSize, "每次最多只能查询" + pageSize + "行数据!");
                for (List<String> outSids : Lists.partition(queryOutSids, DEFAULT_MAX_PAGE_NUM)) {
                    TradeQueryParams tradeQueryParams = new TradeQueryParams();
                    tradeQueryParams.setOutSids(outSids.toArray(new String[0]));
                    tradeQueryParams.setPage(new Page(Page.DEFAULT_PAGE_NUM,DEFAULT_MAX_PAGE_NUM));
                    Trades trades = tradeServiceDubbo.search(staff, tradeQueryParams);
                    if (ObjectUtil.isNotEmpty(trades) && CollectionUtil.isNotEmpty(trades.getList())){
                        List<Long> waveIdList = trades.getList().stream().map(Trade::getWaveId).filter(ObjectUtil::isNotEmpty).collect(toList());
                        waveIds.addAll(waveIdList);
                    }
                }
            }

            //按平台单号查询
            if (StringUtils.isNotEmpty(params.getQueryTids())){
                List<String> queryTids = ArrayUtils.toStringList(params.getQueryTids());
                Assert.isTrue(queryTids.size() <= pageSize, "每次最多只能查询" + pageSize + "行数据!");
                for (List<String> tids : Lists.partition(queryTids, DEFAULT_MAX_PAGE_NUM)) {
                    TradeQueryParams tradeQueryParams = new TradeQueryParams();
                    tradeQueryParams.setTid(tids.toArray(new String[0]));
                    tradeQueryParams.setPage(new Page(Page.DEFAULT_PAGE_NUM,DEFAULT_MAX_PAGE_NUM));
                    Trades trades = tradeServiceDubbo.search(staff, tradeQueryParams);
                    if (ObjectUtil.isNotEmpty(trades) && CollectionUtil.isNotEmpty(trades.getList())){
                        List<Long> waveIdList = trades.getList().stream().map(Trade::getWaveId).filter(ObjectUtil::isNotEmpty).collect(toList());
                        waveIds.addAll(waveIdList);
                    }
                }
            }
        }

        //过滤查询输入的波次号
        if (StringUtils.isNotEmpty(params.getWaveIdStr())){
            waveIds = ArrayUtils.toLongList(params.getWaveIdStr()).stream().filter(waveIds::contains).collect(toList());
        }

        //去波次号进行去重处理
        return waveIds.stream().distinct().collect(Collectors.toList());
    }

    private void checkNum(String str, String msg) {
        if (StringUtils.isBlank(str)) {
            return;
        }
        List<String> err = new ArrayList<>();
        for (String s : ArrayUtils.toStringArray(str)) {
            try {
                Long.parseLong(s);
            } catch (NumberFormatException e) {
                err.add(s);
            }
        }
        if (CollectionUtils.isNotEmpty(err))
            throw new IllegalArgumentException(msg + " 不合法数据明细：" + String.join(",", err));
    }


    @Override
    public PdaIndexNotifyResult queryPdaIndexNotify(Staff staff, PdaIndexNotifyParams params) {
        PdaIndexNotifyResult result = new PdaIndexNotifyResult();
        if (params.getWaveFilterParams() != null) {
            result.setWavePickNum(queryCreatedWaveTotalNum(staff, params.getWaveFilterParams()));
        }
        return result;
    }

    private Long queryCreatedWaveTotalNum(Staff staff, WaveFilterParams params) {
        PageListBase<Wave> pageList = new PageList<Wave>();
        if (StringUtils.isEmpty(params.getStatus())) {
            params.setStatus(String.valueOf(Wave.STATUS_CREATED));
        }
        params.setNotInWaveRuleGroupIds(getWaveRuleGroupPrivilege(staff));
        params.setOpenPrintStatusV2(waveHelpBusiness.openPrintStatusV2(staff));
        return waveDao.count(staff, params);
    }

    @Override
    public void fillPrintedTradeNum(Staff staff, List<Wave> waves, Long pageId) {
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }
        // 获取当前用户是否勾选了 "已打印订单数量" 列配置
        ColumnConfListWrapper columnConfigList = waveColumnConfService.getColumnConfigList(staff, pageId);
        if (columnConfigList == null || CollectionUtils.isEmpty(columnConfigList.getColumnConfList())) {
            return;
        }

        if (columnConfigList.getColumnConfList().stream().anyMatch(c -> Objects.equal(PRINTED_TRADE_NUM_COLUMN_STR, c.getField()) && Objects.equal(CommonConstants.ENABLE_STATUS_NORMARL, c.getVisible()))) {
            Map<Long, Long> printedTradeNumMap = waveTradeDao.queryPrintedTradeNum(staff, waves.stream().map(Wave::getId).collect(toList()));
            if (org.apache.commons.collections.MapUtils.isEmpty(printedTradeNumMap)) {
                for (Wave wave : waves) {
                    if (WaveUtils.isTradeWave(wave.getPickingType())) {
                        wave.setPrintedTradeNum(0);
                    }
                }
                return;
            }

            for (Wave wave : waves) {
                Long printedTradeNum = printedTradeNumMap.get(wave.getId());
                if (printedTradeNum != null) {
                    wave.setPrintedTradeNum(printedTradeNum.intValue());
                }
                if (printedTradeNum == null && WaveUtils.isTradeWave(wave.getPickingType())) {
                    wave.setPrintedTradeNum(0);
                }
            }
        }
    }

    private void fillMultiPickingCode(Staff staff, List<Wave> list, WaveFilterParams params) {
        if (params.getQueryMultiPickingCode() == null || params.getQueryMultiPickingCode() == 0 || CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> ids = list.stream().map(Wave::getId).collect(toList());
        List<WavePickingCode> wavePickingCodes = wavePickingCodeDao.queryByWaveIds(staff, ids, null);
        Map<Long, List<WavePickingCode>> pickingCodeMap = wavePickingCodes.stream().collect(Collectors.groupingBy(WavePickingCode::getWaveId));
        for (Wave wave : list) {
            if (pickingCodeMap.get(wave.getId()) == null) {
                continue;
            }
            Set<String> stringSet = pickingCodeMap.get(wave.getId()).stream().map(WavePickingCode::getPickingCode).collect(toSet());
            if (StringUtils.isNotEmpty(wave.getPickingCode())) {
                stringSet.add(wave.getPickingCode());
            }
            removeDefaultWaveId(pickingCodeMap.get(wave.getId()), stringSet, wave.getId());
            wave.setMultiPickingCode(String.join(",", stringSet));
        }
    }

    /**
     * 波次的拣货车全部被别的波次占用时需要有一个拣选号，系统默认将波次号作为拣选号，该情况下列表中，系统默认绑定的拣选号不显示
     * @param pickingCodes
     * @param stringSet
     */
    private static void removeDefaultWaveId(List<WavePickingCode> pickingCodes, Set<String> stringSet, Long waveId) {
        if (CollectionUtils.isEmpty(pickingCodes) || CollectionUtils.isEmpty(stringSet) || waveId == null) {
            return;
        }

        String waveIdStr = String.valueOf(waveId);
        // 是否所有拣选号都被抢占
        boolean allGrab = pickingCodes.stream().allMatch(p -> Objects.equal(p.getEnableStatus(), CommonConstants.VALUE_NO));
        // 是否真的使用波次号拣选了
        boolean useWaveIdPick = pickingCodes.stream().anyMatch(p -> Objects.equal(waveIdStr, p.getPickingCode()));
        if (allGrab && !useWaveIdPick) {
            stringSet.remove(waveIdStr);
            return;
        }
    }

    private void fillWaveRuleGroupName(Staff staff, List<Wave> list) {
        for (Wave wave : list) {
            String tagIds = wave.getTagIds();
            if (StringUtils.isNotEmpty(tagIds)) {
                StringBuilder builder = new StringBuilder();
                List<Long> tagIdList = ArrayUtils.toLongList(tagIds);
                for (Long tagId : tagIdList) {
                    builder.append(WaveTagEnum.gainNameById(tagId)).append(",");
                }
                wave.setTagNames(builder.substring(0, builder.length() - 1).toString());
            }
        }
        Set<Long> ruleGroupIdSet = list.stream().map(Wave::getRuleGroupId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(ruleGroupIdSet)) {
            return;
        }
        List<WaveRuleGroup> ruleGroups = queryRuleGroupsByIds(staff, Lists.newArrayList(ruleGroupIdSet));
        Map<Long, WaveRuleGroup> ruleGroupMap = Optional.ofNullable(ruleGroups)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(WaveRuleGroup::getId, java.util.function.Function.identity(),
                        (existing, replacement) -> existing));
        for (Wave wave : list) {
            if (Long.valueOf(0).equals(wave.getRuleGroupId())) {
                wave.setRuleGroupName("未分组");
            } else {
                WaveRuleGroup waveRuleGroup = ruleGroupMap.get(wave.getRuleGroupId());
                if (waveRuleGroup != null) {
                    wave.setRuleGroupName(waveRuleGroup.getName());
                }
            }
        }
    }


    private Boolean handleRuleGroupIds(Staff staff, WaveFilterParams params, Page page, PageListBase<Wave> pageList) {
        WaveRule waveRuleQuery = new WaveRule();
        waveRuleQuery.setWaveRuleGroupId(params.getRuleGroupId());
        waveRuleQuery.setWaveRuleGroupIds(ArrayUtils.toLongList(params.getRuleGroupIds()));
        List<WaveRule> waveRules = waveRuleDao.queryList(staff, waveRuleQuery);
        if (CollectionUtils.isNotEmpty(waveRules)) {
            List<Long> waveRuleIds = waveRules.stream().map(WaveRule::getId).collect(Collectors.toList());
            List<Long> paramRuleIds = ArrayUtils.toLongList(params.getRuleIds());
            if (CollectionUtils.isNotEmpty(paramRuleIds)) {
                waveRuleIds.retainAll(paramRuleIds);
            }
            if (CollectionUtils.isNotEmpty(waveRuleIds)) {
                params.setRuleIds(StringUtils.join(waveRuleIds.toArray(), ","));
                return true;
            }
        }
        return false;
    }

    /**
     * 校验波次中的统计信息，包括：
     * 计划拣选数、已拣数、未拣数、缺货数
     *
     * @param wavelist
     */
    @Override
    public void checkWaveStatistics(Staff staff, List<Wave> wavelist, WaveFilterParams params) {
        if (wavelist != null && wavelist.size() > 0) {
            for (Wave wave : wavelist) {
                // 未拣选或刚开始拣选，计划拣选数包含赠品，未拣选数不包含赠品
                if (Wave.DISTRIBUTION_STATUS_NONE == wave.getDistributionStatus()
                        || wave.getPlanPickNum() == null || wave.getPlanPickNum() == 0) {
                    wave.setPickedNum(0);
                    wave.setShortageNum(0);
                    if (params != null && !Objects.equal(params.getReCalculationNum(), CommonConstants.JUDGE_YES)) {
                        //改造：计划拣选数=商品数量－未拣选踢出数
                        wave.setPlanPickNum(DataUtils.getZeroIfDefaultI(DataUtils.substract(wave.getItemCount(), wave.getRemovedCount())));
                    } else {
                        wave.setPlanPickNum(DataUtils.getZeroIfDefaultI(wave.getItemCount()));
                    }
                    wave.setUnPickNum(wave.getPlanPickNum());
                } else if (Wave.DISTRIBUTION_STATUS_ON == wave.getDistributionStatus()) {
                    continue;
                }
            }
        }
    }

    @Override
    public Integer getWaveUnPickNum(Staff staff, Long waveId) {
        Integer unPickNum = 0;
        List<WaveItem> waveItems = this.queryWavePickingDetailsByItem(staff, waveId, null);
        if (waveItems == null || waveItems.size() <= 0) {
            return unPickNum;
        }

        for (WaveItem waveItem : waveItems) {
            unPickNum += waveItem.getNum();
        }
        return unPickNum;
    }

    /**
     * 过滤未开始拣选时的拣选员
     *
     * @param staff
     * @param list
     */
    private void filterUnBeginWavePicker(Staff staff, List<Wave> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (Wave wave : list) {
            if (Wave.DISTRIBUTION_STATUS_NONE == wave.getStatus()) {
                wave.setPickerName(wave.getAssignPickerName());
                wave.setPickerId(wave.getAssignPicker());
            }
        }
    }

    private void fillPickers(Staff staff, List<Wave> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> waveIds = Lists.newArrayListWithCapacity(list.size());
        for (Wave wave : list) {
            waveIds.add(wave.getId());
        }
        List<WavePicker> wavePickers = wavePickerDao.getByWaveIds(staff, waveIds, 1);
        Map<Long, Set<String>> waveIdPickerListMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(wavePickers)) {
            for (WavePicker picker : wavePickers) {
                if (StringUtils.isEmpty(picker.getPickerName())) {
                    continue;
                }
                Set<String> pickers = waveIdPickerListMap.get(picker.getWaveId());
                if (pickers == null) {
                    pickers = Sets.newLinkedHashSet();
                    waveIdPickerListMap.put(picker.getWaveId(), pickers);
                }
                pickers.add(picker.getPickerName());
            }
        }
        if (org.apache.commons.collections.MapUtils.isNotEmpty(waveIdPickerListMap)) {
            for (Wave wave : list) {
                if (!waveIdPickerListMap.containsKey(wave.getId())) {
                    continue;
                }
                Set<String> pickers = waveIdPickerListMap.get(wave.getId());
                if (StringUtils.isNotEmpty(wave.getPickerName())) {
                    pickers.add(wave.getPickerName());
                }
                if (CollectionUtils.isEmpty(pickers)) {
                    continue;
                }
                wave.setPickerName(Joiner.on(",").join(pickers));

            }
        }

    }

    private void filterWithNum(Staff staff, Iterator<Wave> iterator, WaveFilterParams params, TradeConfig tradeConfig) {
        if (Objects.equal(SOURCE_TYPE_PDA, params.getSourceType())) {
            return;
        }

        while (iterator.hasNext()) {
            Wave wave = iterator.next();
            if (wave.getRuleName() == null) {
                wave.setRuleName("");
            }

            if (!WavePickUtils.isTradeWave(wave) || Wave.STATUS_CREATED != wave.getStatus()) {
                continue;
            }
            Wave stat = queryWaveStatistic(staff, wave.getId(), null);
            if (null != stat) {
                wave.setTradesCount(stat.getTradesCount());
                wave.setItemCount(wave.getItemCount() == null ? stat.getItemCount() : wave.getItemCount());
                if (0 == wave.getTradesCount()) {
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, "发现未完结的波次订单都已完成，自动结束波次,waveId=" + wave.getId()));
                    }
                    updateWaveFinished(staff, wave.getId(), null, true, Objects.equal(tradeConfig.getOpenPackageExamine(), CommonConstants.JUDGE_YES));
                    iterator.remove();
                } else {
                    if (params.getTradeNumDown() != null && wave.getTradesCount() < params.getTradeNumDown() ||
                            params.getTradeNumUp() != null && wave.getTradesCount() > params.getTradeNumUp() ||
                            params.getItemNumDown() != null && wave.getItemCount() < params.getItemNumDown() ||
                            params.getItemNumUp() != null && wave.getItemCount() > params.getItemNumUp()) {
                        iterator.remove();
                    }
                }
            }

        }
    }

    private void sortSectionArea(Staff staff, List<Wave> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<Long, List<Wave>> waveMap = list.stream().collect(Collectors.groupingBy(Wave::getWarehouseId));
        for (Map.Entry<Long, List<Wave>> entry : waveMap.entrySet()) {
            CustomSortRule customSortRule = customSortServiceDubbo.queryRuleByBizType(staff,
                    String.join("_", CustomSortRule.CustomSortRuleBizTypeEnum.PICK_GOODS_ROUTE.name(), entry.getKey().toString()));
            if (customSortRule == null || CollectionUtils.isEmpty(customSortRule.getDetails())) {
                continue;
            }
            Map<String, Integer> sortMap = customSortRule.getDetails().stream().collect(Collectors.toMap(
                    CustomSortRuleDetail::getValue, CustomSortRuleDetail::getOrder, (k1, k2) -> k1
            ));
            List<Wave> waves = entry.getValue();
            for (Wave wave : waves) {
                if (StringUtils.isEmpty(wave.getSectionAreas())) {
                    continue;
                }
                wave.setSectionAreas(Arrays.stream(wave.getSectionAreas().split(","))
                        .sorted((s1, s2) -> {
                            Integer v1 = sortMap.get(s1);
                            Integer v2 = sortMap.get(s2);
                            return v1 == null && v2 == null ? s1.compareTo(s2) :
                                    Optional.ofNullable(v1).orElse(Integer.MAX_VALUE).compareTo(Optional.ofNullable(v2).orElse(Integer.MAX_VALUE));
                        }).collect(Collectors.joining(",")));
            }
        }
    }

    private void fillPrintStatus(Staff staff, Boolean isShowPrintStatus, List<Wave> list) {
        if (BooleanUtils.isNotTrue(isShowPrintStatus)) {
            return;
        }
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> waveIds = list.stream().filter(WavePickUtils::isTradeWave).map(Wave::getId).collect(toList());
        if (CollectionUtils.isEmpty(waveIds)) {
            return;
        }
        // 查询未踢出波次订单
        Map<Long, Wave> waveCountMap = waveTradeDao.queryWaveStatCountByWaveIds(staff, waveIds).stream().collect(Collectors.toMap(Wave::getId, w -> w, (k1, k2) -> k1));
        //wave中的tradesCount是精确的，此处需要判断存储订单总数和订单打印状态
        List<Wave> originWaves = waveDao.queryByIds(staff, list.stream().map(Wave::getId).toArray(Long[]::new));
        if (CollectionUtils.isEmpty(originWaves)) {
            return;
        }
        Map<Long, Wave> originMap = originWaves.stream().collect(Collectors.toMap(Wave::getId, w -> w));
        for (Wave wave : list) {
            if (!WavePickUtils.isTradeWave(wave)) {
                wave.setPrintStatus("");
                continue;
            }
            Wave waveStatCount = waveCountMap.get(wave.getId());
            if (waveStatCount == null) {
                continue;
            }
            // 未踢出已打印
            long unRemoveHasPrintCount = waveStatCount.getUnRemoveHasPrintCount() == null ? 0 : waveStatCount.getUnRemoveHasPrintCount();
            // 已打印
            long printCount = waveStatCount.getHasPrintCount() == null ? 0 : waveStatCount.getHasPrintCount();
            // 未踢出
            long unRemoveCount = waveStatCount.getUnRemoveCount() == null ? 0 : waveStatCount.getUnRemoveCount();
            if (waveHelpBusiness.openPrintStatusV2(staff)) {
                wave.setTradePrintCount((int) unRemoveHasPrintCount);
                if (unRemoveCount == 0) {
                    wave.setPrintStatus(Wave.WavePrintStatusEnum.NO_PRINT.getValue());
                } else if (unRemoveCount == unRemoveHasPrintCount) {
                    //  未踢出=未踢出已打印 全部打印
                    wave.setPrintStatus(Wave.WavePrintStatusEnum.ALL_PRINT.getValue());
                } else if (unRemoveHasPrintCount == 0) {
                    wave.setPrintStatus(Wave.WavePrintStatusEnum.UN_PRINT.getValue());
                } else {
                    wave.setPrintStatus(Wave.WavePrintStatusEnum.PART_PRINT.getValue());
                }
            } else {
                wave.setTradePrintCount((int) printCount);
                Wave origin = originMap.getOrDefault(wave.getId(), wave);
                if (wave.getTradePrintCount().equals(0)) {
                    wave.setPrintStatus(Wave.WavePrintStatusEnum.NO_PRINT.getValue());
                } else if (wave.getTradePrintCount().equals(origin.getTradesCount())) {
                    wave.setPrintStatus(Wave.WavePrintStatusEnum.ALL_PRINT.getValue());
                } else {
                    wave.setPrintStatus(Wave.WavePrintStatusEnum.PART_PRINT.getValue());
                }
            }
        }
    }

    @Override
    public PageListBase<Wave> queryWaves(Staff staff, WaveFilterParams params, Page page) {
        PageListBase<Wave> pageList = new PageList<Wave>();

        long count = waveDao.count(staff, params);
        pageList.setTotal(count);
        pageList.setPage(page);

        if (page != null && count <= page.getStartRow()) {
            pageList.setList(new ArrayList<Wave>());
            return pageList;
        }

        List<Wave> list = waveDao.queryPageList(staff, params, page);
        if (CollectionUtils.isNotEmpty(list)) {
            fillStatisticsInfo(staff, list);
            fillTemplateName(staff, list);
        }
        pageList.setList(list);
        return pageList;
    }

    @Override
    public Wave queryWave(Staff staff, WaveFilterParams params) {
        Wave wave = waveDao.queryByCondition(staff, params);
        Assert.notNull(wave, "波次不存在");
        fillStatisticsInfo(staff, Arrays.asList(wave));
        if (params.isNeedRemoveTradeCount()){
            wave.setNoRemovedSidCount(waveTradeDao.queryNotRemoveCountJoinTrade(staff,params.getWaveId()).intValue());
        }
        return wave;
    }

    @Override
    public PageListBase<Wave> queryWavesLog(Staff staff, WaveFilterParams params, Page page) {
        PageListBase<Wave> pageList = new PageList<Wave>();
        params.setExpressMap(dealExpressParam(staff, params.getExpress()));
        if (params.getRuleGroupId() != null || StringUtils.isNotEmpty(params.getRuleGroupIds())) {
            Boolean isHandle = handleRuleGroupIds(staff, params, page, pageList);
            if (!isHandle) {
                pageList.setTotal(0L);
                pageList.setPage(page);
                pageList.setList(new ArrayList<Wave>());
                return pageList;
            }
        }

        WaveConfig waveConfig = waveConfigService.get(staff);
        params.setOpenWaveUniqueCode(waveConfig.getOpenWaveUniqueCode());
        params.setNotInWaveRuleGroupIds(getWaveRuleGroupPrivilege(staff));
        fillParamsSysAItemSkuIds(staff, params);
        //按照创建时间排序，优化sql，false 按照id排序，走到主键索引，效率低
        params.setFromPage(true);
        long count = waveDao.count(staff, params);
        pageList.setTotal(count);
        pageList.setPage(page);

        if (page == null || count > page.getStartRow()) {
            List<Wave> list = waveDao.queryPageList(staff, params, page);
            if (CollectionUtils.isNotEmpty(list)) {
                Boolean isShowPrintStatus = StringUtils.isNotBlank(params.getPrintStatus()) || BooleanUtils.isTrue(params.getIsFillInfo());
                fillWaveInfo(staff, list, isShowPrintStatus, BooleanUtils.isTrue(params.getIsFillInfo()));
            }
            checkWaveStatistics(staff, list, params);
            fillPrintedTradeNum(staff, list, WAVE_LOG_COLUMN_PAGE_ID);
            pageList.setList(list);
        } else {
            pageList.setList(new ArrayList<>());
        }
        return pageList;
    }

    @Override
    public boolean checkWaveTypeQueryFeature(Staff staff) {
        CheckHasFeatureRequest request = new CheckHasFeatureRequest();
        request.setCompanyId(staff.getCompanyId());
        request.setCode("waveTypeQuery");
        CheckHasFeatureResponse response = indexDubboService.checkHasFeature(request);
        if (response == null) {
            return false;
        }
        return response.isHasFeature();
    }

    /**
     * 根据部分的sids移出波次，一般是合单的一部分完成或关闭，不需要整个合单都移出
     *
     * @param staff
     * @param sids
     * @param opName
     * @return
     */
    @Override
    @Transactional
    public List<Trade> removePartSids(final Staff staff, final Long[] sids, final String opName) {
        List<TbTrade> tbTrades = Lists.newArrayList(tbTradeDao.queryBySids(staff, sids));
        if (CollectionUtils.isEmpty(tbTrades)) {
            return Collections.emptyList();
        }
        List<TbOrder> tbOrders = tbOrderDAO.queryBySids(staff, sids);
        if (CollectionUtils.isNotEmpty(tbOrders)) {
            TradeUtils.assemblyBySid(tbTrades, tbOrders);
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "移出部分订单，part sids: " + StringUtils.join(sids, ",")));
        }
        final List<Trade> trades = Lists.newArrayListWithCapacity(tbTrades.size());
        for (TbTrade tbTrade : tbTrades) {
            if (DataUtils.checkLongNotEmpty(tbTrade.getWaveId())) {
                trades.add(tbTrade);
            }
        }

        if (trades.isEmpty()) {
            return Collections.emptyList();
        }

        //只需要移出waveTrade记录以及清掉trade的waveId字段
        return removeByBatch(staff, opName, WaveTraceOperateEnum.TRADE_OUT_WAVE,false, trades);
    }

    /**
     * 根据sids 删除波次订单记录，并移除没有订单信息的波次
     *
     * @param sids   订单号
     * @param opName 操作名称
     */
    @Override
    @Transactional
    public List<Trade> removeSids(final Staff staff, Long[] sids, final String opName) {
        return removeSids(staff, sids, opName, WaveTraceOperateEnum.TRADE_OUT_WAVE);
    }

    @Override
    @Transactional
    public List<Trade> removeSids(Staff staff, Long[] sids, String opName, WaveTraceOperateEnum operateEnum) {
        List<Trade> trades = waveUseTradeServiceProxy.queryBySidsContainMergeTrade(staff, true, false, true, sids);
        if (logger.isDebugEnabled()) {
            logger.warn(LogHelper.buildLog(staff, String.format("根据sids删除波次订单记录，并移除没有订单信息的波次,sids=%s", TradeUtils.toSidList(trades))));
        }
        final List<Trade> updateTrades = filterRemoveTrades(staff, trades);
        if (CollectionUtils.isEmpty(updateTrades)) {
            return null;
        }
        return removeByBatch(staff, opName, operateEnum, true , Lists.newArrayList(updateTrades));

    }

    /**
     * 分批次移出波次
     * @param staff
     * @param opName 操作名称
     * @param operateEnum 操作类型
     * @param removeSorting 是否需要删除分拣记录
     * @param updateTrades 移出哪些订单
     * @return
     */
    private List<Trade> removeByBatch(Staff staff, String opName, WaveTraceOperateEnum operateEnum, boolean removeSorting, List<Trade> updateTrades) {
        List<Trade> result = new ArrayList<>();
        for (List<Trade> subTrade : Lists.partition(updateTrades, MAX_LOCKS)) {
            result.addAll(retryRemoveSids(staff,
                    () -> lockService.locks(WaveLockBusiness.getKey2WaveIdAndSid(staff, Lists.newArrayList(subTrade)),
                            () -> handleRemoveWaveTrades(staff, Lists.newArrayList(subTrade), opName, removeSorting, operateEnum))));
        }
        return result;
    }

    public List<Trade> retryRemoveSids(Staff staff, Callable<List<Trade>> callable) {
        Retryer<List<Trade>> retryer = RetryerBuilder
                .<List<Trade>>newBuilder()
                // 递增间隔
                .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 20, TimeUnit.SECONDS))
                // 重试3次
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .retryIfException()
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        if (attempt.getAttemptNumber() > 1 || attempt.hasException()) {
                            logger.debug(String.format("踢出波次重试次数=%s，结果=%s，异常=%s", attempt.getAttemptNumber(), attempt.hasResult() ? attempt.getResult() : "", attempt.hasException() ? attempt.getExceptionCause().getMessage() : ""));
                        }
                    }
                })
                .build();
        try {
            return retryer.call(callable);
        } catch (ExecutionException | RetryException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "踢出波次重试失败"), e);
            throw new RuntimeException(e);
        }
    }

    private List<Trade> filterRemoveTrades(Staff staff, List<Trade> trades) {
        List<Trade> updateTrades = trades.stream().filter(trade -> trade.getWaveId() != null && trade.getWaveId() != 0 && trade.getWaveId() != -5L).collect(Collectors.toList());

        if (updateTrades.isEmpty()) {
            return null;
        }

        // 已经后置打印的订单再次打印不会移出波次
        Set<Long> printedSids = queryPrintedSids(staff, updateTrades);
        if (!printedSids.isEmpty()) {
            logger.debug(LogHelper.buildLog(staff, String.format("已经后置打印的订单：%s", printedSids)));
        }
        List<Trade> mergeTrades = updateTrades.stream().filter(TradeUtils::isMerge).collect(toList());
        Set<Long> hideSid = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(mergeTrades) && CollectionUtils.isNotEmpty(printedSids)) {
            Map<Long, List<Trade>> sidMap = mergeTrades.stream().collect(Collectors.groupingBy(Trade::getMergeSid));
            for (Long printSid : printedSids) {
                if (sidMap.get(printSid) != null) {
                    List<Trade> trades1 = sidMap.get(printSid);
                    hideSid.addAll(trades1.stream().map(Trade::getSid).collect(toList()));
                }
            }
        }
        printedSids.addAll(hideSid);

        updateTrades.removeIf(trade -> trade.getExpressPrintTime() != null && trade.getExpressPrintTime().compareTo(TradeTimeUtils.INIT_DATE) > 0
                && DataUtils.getZeroIfDefaultI(trade.getPrintCount()) > 1
                && printedSids.contains(trade.getSid()));
        return updateTrades;
    }

    private List<Trade> handleRemoveWaveTrades(Staff staff, List<Trade> trades, String opName, boolean removeSorting) {
        return handleRemoveWaveTrades(staff, trades, opName, removeSorting, WaveTraceOperateEnum.TRADE_OUT_WAVE);
    }

    private List<Trade> handleRemoveWaveTrades(Staff staff, List<Trade> trades, String opName, boolean removeSorting, WaveTraceOperateEnum operateEnum) {
        // 过滤已经踢出的订单
        filterHasEnterWaveTrades(staff, trades);
        if (CollectionUtils.isEmpty(trades)) {
            return trades;
        }
        List<Long> removeSids = TradeUtils.toSidList(trades);
        // 订单移除波次时，扣去未拣选数，记录踢出数
        calculateRemoveNumAndUnPickedNum(staff, trades);

        if (removeSorting) {
            //删除分拣记录
            waveSortingService.deleteSortingBySids(staff, removeSids);
        }
        //修改订单waveId字段
        updateRemoveWaveTrades(staff, trades);

        Map<Long, List<Trade>> waveIdTradesMap = MapUtils.toMapList(trades, Trade::getWaveId);
        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        List<Long> waveIds = Lists.newArrayList(waveIdTradesMap.keySet());
        // 更新波次要有顺序，防止交叉更新导致死锁
        Collections.sort(waveIds);
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, "handleRemoveWaveTrades.waveIds:" + waveIds));
        }

        //记录系统操作日志，以及检查波次是否完成
        for (Long waveId : waveIds) {
            recordOpLog(staff, waveId, waveIdTradesMap.get(waveId), opName);
            updateWaveFinished(staff, waveId, TradeUtils.toSidList(waveIdTradesMap.get(waveId)), true, Objects.equal(tradeConfig.getOpenPackageExamine(), CommonConstants.JUDGE_YES), opName);
        }

        if (WaveOutBusinessEnum.TRADE_PRINT.getOpName().equals(opName)) {
            updateAllocateGoodsRecordWaveId(staff, removeSids, 0L);
        } else {
            deleteUnPickedAllocateGoodsRecords(staff, removeSids, opName);
        }

        //记录订单日志
        List<Trade> tradeLogs = WaveUtils.buildSimpleTradeTraces(trades);
        for (List<Trade> subTrades : Lists.partition(tradeLogs, 2000)) {
            eventCenter.fireEvent(this, new EventInfo("trade.wave.remove").setArgs(new Object[]{staff, opName}), Lists.newArrayList(subTrades));
        }
        if (WmsUtils.isOldWms(staff)){
            for (List<Long> subSids : Lists.partition(removeSids, 2000)) {
                eventCenter.fireEvent(this, new EventInfo("goodsSection.lock.wave.remove").setArgs(new Object[]{staff}), Lists.newArrayList(subSids));
            }
        }
        waveTraceService.batchAddWaveTrace(staff, WaveTraceUtils.buildBatchWaveTraceByTrade(staff, tradeLogs, operateEnum, StringUtils.isNotEmpty(opName) ? opName : "退出波次" + "，订单号："));
        return tradeLogs;
    }

    /**
     * 并发情况下，加锁后需要过滤已经踢出的订单
     */
    private void filterHasEnterWaveTrades(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Long> removeSids = TradeUtils.toSidList(trades);
        List<Long> filterSids = Optional.ofNullable(tbTradeDao.queryBySids(staff, removeSids.toArray(new Long[0])))
                .orElse(Lists.newArrayList()).stream().filter(trade -> Objects.equal(trade.getWaveId(), 0L))
                .map(Trade::getSid).collect(toList());
        if (CollectionUtils.isEmpty(filterSids)) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("已经踢出的订单，无需继续踢出：%s", filterSids)));
        }
        trades.removeIf(trade -> filterSids.contains(trade.getSid()));
    }

    /**
     * 订单移除波次时，扣去未拣选数，记录踢出数
     * @param staff
     * @param trades
     */
    void calculateRemoveNumAndUnPickedNum(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        // 1、获取波次信息
        List<Wave> waveList = this.queryWaveByIds(staff, trades.stream().map(Trade::getWaveId).distinct().toArray(Long[]::new));
        Map<Long, Wave> waveId2WaveMap = waveList.stream().collect(Collectors.toMap(Wave::getId, java.util.function.Function.identity()));
        Map<Long, Integer> waveId2UnpickedNumMap = Maps.newHashMap();
        Map<Long, Integer> waveId2PickedNumMap = Maps.newHashMap();
        List<Long> pickedWaveNeedRemovedSids = Lists.newArrayList();
        // 2、如果订单所在的波次是未拣选，踢出数取订单的商品和。
        for (Trade trade : trades) {
            Wave wave = waveId2WaveMap.get(trade.getWaveId());
            if (wave == null) {
                continue;
            }
            Integer itemNum = CollectionUtils.isNotEmpty(TradeUtils.getOrders4Trade(trade)) ?
                    WaveUtils.getTradeSingleItemNum(trade) : trade.getItemNum();
            if (Objects.equal(Wave.DISTRIBUTION_STATUS_NONE, wave.getDistributionStatus())) {
                waveId2UnpickedNumMap.merge(trade.getWaveId(), itemNum, Integer::sum);
            } else {
                pickedWaveNeedRemovedSids.add(trade.getSid());
            }
        }
        // 3、已拣选的订单，踢出数取这票订单的未拣选的商品数
        if (CollectionUtils.isNotEmpty(pickedWaveNeedRemovedSids)) {
            List<WaveSortingDetail> unPickedDetails = waveSortingService.queryDetailsWithWaveId(staff, pickedWaveNeedRemovedSids);
            waveId2UnpickedNumMap.putAll(unPickedDetails.stream().collect(Collectors.toMap(WaveSortingDetail::getWaveId,
                    data -> DataUtils.getZeroIfDefaultI(DataUtils.substract(data.getItemNum(), data.getPickedNum(), data.getShortageNum())),
                    Integer::sum)));
            //计算已拣踢出数
            for (WaveSortingDetail detail : unPickedDetails) {
                waveId2PickedNumMap.merge(detail.getWaveId(), detail.getPickedNum(), Integer::sum);
            }
        }

        // 4、获取波次未拣选数量旧值
        List<WavePicking> wavePickingList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(waveId2UnpickedNumMap.keySet())) {
            wavePickingList = wavePickingDao.getByWaveIds(staff, Lists.newArrayList(waveId2UnpickedNumMap.keySet()));
        }
        Map<Long, WavePicking> waveId2WavePickingMap = wavePickingList.stream().collect(Collectors.toMap(WavePicking::getWaveId, java.util.function.Function.identity()));

        // 5、更新未拣选和踢出的数量,
        updateRemovedNumAndUnPickedNum(staff, waveId2WaveMap, waveId2UnpickedNumMap, waveId2PickedNumMap, waveId2WavePickingMap);
    }

    /**
     * 更新未拣选和踢出的数量
     * 更新已拣选踢出数
     * @param staff
     * @param waveId2WaveMap
     * @param waveId2UnpickedNumMap
     * @param waveId2WavePickingMap
     */
    private void updateRemovedNumAndUnPickedNum(Staff staff, Map<Long, Wave> waveId2WaveMap, Map<Long, Integer> waveId2UnpickedNumMap, Map<Long, Integer> waveId2PickedNumMap, Map<Long, WavePicking> waveId2WavePickingMap) {
        List<WavePicking> updateWavePickingList = Lists.newArrayList();
        List<Wave> updateWaveList = Lists.newArrayList();
        List<TradeWaveRemoveInfo> tradeWaveRemoveInfos = Lists.newArrayList();
        waveId2UnpickedNumMap.forEach((k, v) -> {
            Wave wave = waveId2WaveMap.get(k);
            if (wave == null) {
                return;
            }
            Wave updateWave = new Wave();
            updateWave.setRemovedCount(DataUtils.add(wave.getRemovedCount(), v));
            updateWave.setId(wave.getId());
            //更新 已拣选踢出数
            Integer pickedRemovedCount = waveId2PickedNumMap.get(k);
            if (pickedRemovedCount != null) {
                updateWave.setPickedRemovedCount(DataUtils.add(wave.getPickedRemovedCount(), pickedRemovedCount));
            }
            tradeWaveRemoveInfos.add(new TradeWaveRemoveInfo().setId(wave.getId()).setRemovedCount(v).setPickedRemovedCount(pickedRemovedCount));
            updateWaveList.add(updateWave);

            WavePicking wavePicking = waveId2WavePickingMap.get(k);
            // 未拣选时，wavePicking为空，不记录未拣选数，改成查询列表时实时计算，
            // @see com.raycloud.dmj.services.trades.support.wave.TradeWaveService#checkWaveStatistics
            if (wavePicking == null) {
                return;
            }
            WavePicking updateWavePicking = new WavePicking();
            updateWavePicking.setUnPickNum(DataUtils.getZeroIfDefaultI(DataUtils.substract(wavePicking.getUnPickNum(), v)));
            //改造：计划拣选数=商品数量－未拣选踢出数
            updateWavePicking.setPlanPickNum(DataUtils.getZeroIfDefaultI(DataUtils.substract(wave.getItemCount().intValue(), updateWave.getRemovedCount())));
            updateWavePicking.setId(wavePicking.getId());
            updateWavePickingList.add(updateWavePicking);
        });
        bufferService.buffer(Buffers.build(staff, "wave.remove.num"), TradeWaveRemoveInfo.toKeys(tradeWaveRemoveInfos), true);
        eventCenter.fireEvent(this, new EventInfo("wave.remove.num").setArgs(new Object[]{staff}), null);
    }

    /**
     * 合并数据，更新踢出数
     */
    @Override
    @Transactional
    public void mergeUpdateRemovedNum(Staff staff, List<ErpBuffer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> keys = list.stream().map(ErpBuffer::getKey).collect(Collectors.toList());
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "记录待更新踢出数据，keys：" + keys));
        }
        List<TradeWaveRemoveInfo> removeChangeInfos = keys.stream().map(TradeWaveRemoveInfo::resolveBufferKey).collect(Collectors.toList());
        removeChangeInfos = TradeWaveRemoveInfo.sumNum(removeChangeInfos);
        //输出统计数据修改日志
        addWavePickingLogOnlyEsBatch(staff, removeChangeInfos);

        // 先排序，防止wave.update死锁
        removeChangeInfos.sort(Comparator.comparing(TradeWaveRemoveInfo::getId));
        waveDao.updateNumByWaveIds(staff, removeChangeInfos);
        wavePickingDao.updateNumByWaveIds(staff, removeChangeInfos);
    }

    private void addWavePickingLogOnlyEsBatch(Staff staff, List<TradeWaveRemoveInfo> removeChangeInfos) {
        try {
            if (CollectionUtils.isEmpty(removeChangeInfos)) {
                return;
            }
            Map<Long, TradeWaveRemoveInfo> waveRemoveInfoMap = removeChangeInfos.stream().collect(Collectors.toMap(TradeWaveRemoveInfo::getId, r -> r, (k1, k2) -> k1));
            List<Long> waveIdList = new ArrayList<>(waveRemoveInfoMap.keySet());
            for (List<Long> waveIds : Lists.partition(waveIdList, 500)) {
                List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
                List<WavePicking> wavePickings = wavePickingDao.getByWaveIds(staff, waveIds);
                if (CollectionUtils.isEmpty(waves) || CollectionUtils.isEmpty(waveIds)) {
                    return;
                }
                Map<Long, Wave> waveMap = waves.stream().collect(Collectors.toMap(Wave::getId, w -> w, (k1, k2) -> k1));
                Map<Long, WavePicking> wavePickingMap = wavePickings.stream().collect(Collectors.toMap(WavePicking::getWaveId, p -> p, (k1, k2) -> k1));
                for (Long waveId : waveIds) {
                    if (wavePickingMap.get(waveId) == null || waveMap.get(waveId) == null || waveRemoveInfoMap.get(waveId) == null) {
                        continue;
                    }
                    Integer removedCount = waveRemoveInfoMap.get(waveId).getRemovedCount();
                    Integer newRemovedCount = DataUtils.getZeroIfDefaultI(DataUtils.add(waveMap.get(waveId).getRemovedCount(), removedCount));
                    Integer newPlanPickNum = DataUtils.getZeroIfDefaultI(DataUtils.substract(waveMap.get(waveId).getItemCount(), newRemovedCount));
                    Integer newUnpickedNum = DataUtils.getZeroIfDefaultI(DataUtils.substract(wavePickingMap.get(waveId).getUnPickNum(), removedCount));
                    String content = String.format("订单踢出修改波次拣选统计数据,[RemovedCount:%s -> %s, PlanPickNum:%s -> %s, UnPickNum:%s -> %s] ",
                            waveMap.get(waveId).getRemovedCount(), newRemovedCount,
                            wavePickingMap.get(waveId).getPlanPickNum(), newPlanPickNum, wavePickingMap.get(waveId).getUnPickNum(), newUnpickedNum);
                    addWaveTraceLog(staff, waveId, WaveTraceOperateEnum.WAVE_PICKING_STAT, content);
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "输出拣选统计信息失败"), e);
        }
    }

    /**
     * 删除订单里面 未拣选 的配货记录
     *
     * @param staff
     * @param removeSids
     * @Title: deleteUnPickedAllocateGoodsRecords
     */
    private void deleteUnPickedAllocateGoodsRecords(Staff staff, List<Long> removeSids, String opName) {
        List<Long> removeOrderIds = new ArrayList<>();
        List<TbOrder> orderList = tbOrderDAO.queryBySids(staff, removeSids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        for (TbOrder order : orderList) {
            removeOrderIds.add(order.getId());
        }
        List<AllocateGoodsRecord> removeRecords = wmsService.queryAllocateGoodsRecords(staff,
                new QueryAllocateGoodsRecordParams.Builder().containerTypes(Lists.newArrayList(ContainerTypeEnum.GOODS_SECTION.getValue(), ContainerTypeEnum.BOX.getValue(), ContainerTypeEnum.WORKING_STORAGE_SECTION.getValue())).wssType(WorkingStorageSection.TypeEnum.PICK.getCode()).orderIds(removeOrderIds).build());
        if (!WaveOutBusinessEnum.FINISH_SEED.getOpName().equals(opName) && !WaveOutBusinessEnum.FINISH_WAVE.getOpName().equals(opName)) {
            removeRecords = Optional.ofNullable(removeRecords).orElse(Collections.emptyList()).stream().filter(record -> {
                // PS:这里不直接用trade的waveId过滤出需要删除的配货,防止并发waveId不对;
                if (AllocateGoodsRecord.AllocateGoodsStatusEnum.UNPICKED.getValue().equals(record.getStatus()) && record.getWaveId() > 0) {
                    return true;
                } else {
                    return false;
                }
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(removeRecords)) {
            List<Long> removeIds = removeRecords.stream().map(AllocateGoodsRecord::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(removeIds)) {
                wmsService.deleteAllocateAndCancelOrderStockProduct(staff, removeIds);
            }
        }
    }

    private void updateAllocateGoodsRecordWaveId(Staff staff, List<Long> removeSids, Long waveId) {
        List<Long> removeOrderIds = new ArrayList<>();
        List<TbOrder> orderList = tbOrderDAO.queryBySids(staff, removeSids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        for (TbOrder order : orderList) {
            removeOrderIds.add(order.getId());
        }
        List<AllocateGoodsRecord> allocateGoodsRecords = wmsService.queryAllocateGoodsRecords(staff,
                new QueryAllocateGoodsRecordParams.Builder().containerTypes(Lists.newArrayList(ContainerTypeEnum.GOODS_SECTION.getValue(), ContainerTypeEnum.BOX.getValue(), ContainerTypeEnum.WORKING_STORAGE_SECTION.getValue())).wssType(WorkingStorageSection.TypeEnum.PICK.getCode()).orderIds(removeOrderIds).build());
        if (CollectionUtils.isNotEmpty(allocateGoodsRecords)) {
            List<AllocateGoodsRecord> records = allocateGoodsRecords.stream().filter(v -> v.getWaveId().equals(waveId)).collect(Collectors.toList());
            //存在合适的配货记录,有多余的把多余的删除; 不存在合适的配货记录,更改waveId----防止 波次生成 与 前置打印 并发,并且触发了退出波次事件
            if (CollectionUtils.isNotEmpty(records)) {
                allocateGoodsRecords.removeAll(records);
                if (CollectionUtils.isEmpty(allocateGoodsRecords)) {
                    return;
                }
                wmsService.deleteAllocateGoodsRecords(staff, allocateGoodsRecords.stream().map(AllocateGoodsRecord :: getId).collect(Collectors.toList()));
            } else {
                for (AllocateGoodsRecord allocateGoodsRecord : allocateGoodsRecords) {
                    allocateGoodsRecord.setWaveId(waveId);
                }
                wmsService.saveAllocateGoodsRecords(staff, allocateGoodsRecords);
            }
        }



    }

    @Override
    public void updateRemoveWaveTrades(Staff staff, List<Trade> trades) {
        int count = 3;
        while (count > 0) {
            try {
                waveTradeDao.updateTradeWaveId(staff, buildRemoveTrades(trades));
                Set<Long> sids = trades.stream().map(Trade::getSid).collect(toSet());
                logisticsWaveRelationBusiness.removeLogisticsWaveRelation(staff, LogisticsWaveRelation.LogisticsType.TRADE, sids);
                break;
            } catch (Exception e) {
                try {
                    Thread.sleep(100);
                    count--;
                    if (count == 1) {
                        logger.error("批量更新trade重试三次失败");
                        throw new LockException(e);
                    }
                    logger.error("批量更新trade失败,重试");
                } catch (Exception ex) {
                    throw new LockException(ex);
                }
            }
        }
    }

    @Override
    public List<WavePickerInfo> queryPickingParams(Staff staff, WaveFilterParams params, Page page) {
        return waveDao.queryPickingParams(staff, params, page);
    }

    private List<Trade> buildRemoveTrades(List<Trade> trades) {
        List<Trade> toUpdates = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            Trade update = new TbTrade();
            update.setSid(trade.getSid());
            update.setWaveId(0L);
            toUpdates.add(update);
        }

        toUpdates.sort(Comparator.comparing(Trade::getSid));
        return toUpdates;
    }

    private List<WaveTrade> buildUpdateWaveTrades(Staff staff, Long waveId, List<Long> sids, Integer tradeWaveStatus, boolean isPack, String opName) {
        List<WaveTrade> waveTrades = Lists.newArrayListWithCapacity(sids.size());
        Date now = new Date();
        for (Long sid : sids) {
            WaveTrade waveTrade = new WaveTrade();
            waveTrade.setWaveId(waveId);
            waveTrade.setSid(sid);
            waveTrade.setTradeWaveStatus(tradeWaveStatus);
            if (isPack) {
                waveTrade.setPackTime(now);
                waveTrade.setPackStaffId(staff.getId());
            }
            WaveOutBusinessEnum waveOutBusinessEnum = WaveOutBusinessEnum.praseOpName(opName);
            waveTrade.setOutBusinessType(waveOutBusinessEnum != null ? waveOutBusinessEnum.name() : null);
            waveTrades.add(waveTrade);
        }
        return waveTrades;
    }

    private Set<Long> queryPrintedSids(Staff staff, List<Trade> trades) {

        //开启白名单-波次拣货单打印取值新逻辑时，可查询enabel_status = 0 的waveSorting
        boolean pickBillGetDataFromTrade = featureService.checkHasFeatureByCode(staff.getCompanyId(), "pickBillGetDataFromTrade");
        if (pickBillGetDataFromTrade && logger.isDebugEnabled()) {
            logger.debug("开启了白名单波次拣货单打印取值新逻辑,可查出enable_status=0的waveSorting");
        }
        List<Long> sids = TradeUtils.toSidList(trades);
        List<Long> waveIds = Optional.ofNullable(trades).orElse(new ArrayList<>()).stream().map(Trade::getWaveId).collect(toList());
        List<WaveSorting> waveSortings = pickBillGetDataFromTrade ? waveSortingDao.queryBySidsAndWaveIds(staff, sids, waveIds) : waveSortingDao.queryBySids(staff, sids);
        Set<Long> printedSids = Sets.newHashSetWithExpectedSize(waveSortings.size());
        for (WaveSorting waveSorting : waveSortings) {
            if (waveSorting.getPrintStatus() == WaveSorting.PRINT_STATUS_OVER) {
                printedSids.add(waveSorting.getSid());
            }
        }
        // 在波次列表打印的也不踢除
        Map<Long, Date> waveId2CreatedMap = this.queryWaveByIds(staff, trades.stream().map(Trade::getWaveId).distinct().toArray(Long[]::new))
                .stream().filter(w -> w.getCreated() != null).collect(toMap(Wave::getId, Wave::getCreated));
        Map<Long, Date> sid2WaveCreatedMap = trades.stream()
                .filter(t -> waveId2CreatedMap.get(t.getWaveId()) != null)
                .collect(toMap(Trade::getSid, (t -> waveId2CreatedMap.get(t.getWaveId())), (d1, d2) -> d1));
        // 获取比波次生成时间晚的打印次数（修复 订单生成波次后前置打印踢出波次，再重审重新进入波次，在前置打印无法踢出的情况）
        Map<Long, Long> sid2PrintCountMap = waybillPrintServiceDubbo.queryDetailsBySids(staff, sids.toArray(new Long[0]))
                .stream().filter(data -> data.getCreated() != null && sid2WaveCreatedMap.get(data.getSid()) != null
                        && data.getCreated().after(sid2WaveCreatedMap.get(data.getSid())))
                .collect(groupingBy(PrintTradeLogDetail::getSid, counting()));
        // 打印次数>1，避免前置打印未踢出
        for (Map.Entry<Long, Long> entry : sid2PrintCountMap.entrySet()) {
            Long printCount = entry.getValue() == null ? 0L : entry.getValue();
            if (printCount > 1) {
                printedSids.add(entry.getKey());
            }
        }
        return printedSids;
    }

    private void recordOpLog(Staff staff, Long waveId, List<Trade> trades, String opName) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        OpLog log = new OpLog();
        log.setDomain(Domain.TRADE);
        log.setAction("removeFromWave");
        log.setKey(String.valueOf(waveId));
        log.setContent(String.format("%s退出波次，波次号：%s，订单号：%s", opName, waveId, TradeUtils.toSidList(trades)));
        log.setCreated(new Date());
        opLogService.record(staff, log);
    }

    private void recordOpLog(Staff staff, String action, String key, String content) {
        OpLog log = new OpLog();
        log.setDomain(Domain.TRADE);
        log.setAction(action);
        log.setKey(key);
        log.setContent(content);
        log.setCreated(new Date());
        opLogService.record(staff, log);
    }

    @Override
    @Transactional
    public void updateWaveFinished(Staff staff, Long waveId, List<Long> sids, boolean removed, boolean openPackageExamine) {
        updateWaveFinished(staff, waveId, sids, removed, openPackageExamine, null);
    }

    /**
     * 更新波次是否可以完成
     *
     * @param waveId             波次id
     * @param sids
     * @param removed            是否移出
     * @param openPackageExamine 是否开启包装验货
     */
    @Override
    @Transactional
    public void updateWaveFinished(Staff staff, Long waveId, List<Long> sids, boolean removed, boolean openPackageExamine, String opName) {
        //更新波次订单状态
        if (CollectionUtils.isNotEmpty(sids)) {
            waveTradeDao.batchUpdate(staff, buildUpdateWaveTrades(staff, waveId, sids, removed ? WaveTrade.TRADE_WAVE_STATUS_NOT : WaveTrade.TRADE_WAVE_STATUS_OVER, openPackageExamine, opName));
        }

        Wave wave = waveDao.queryById(staff, waveId);
        if (wave == null || wave.getStatus() != Wave.STATUS_CREATED) {
            return;
        }

        Wave update = new Wave();
        update.setId(wave.getId());

        Integer notFinishCount = waveQueryDao.queryWaveTradeStatusCount(staff, wave.getWarehouseId(), waveId, openPackageExamine, 0);
        if (notFinishCount == null || notFinishCount == 0) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "波次订单都已完成，结束波次，waveId：" + waveId));
            }
            wave.setId(waveId);
            wave.setStatus(Wave.STATUS_FINISHED);
            wave.setFinished(new Date());
            if (removed) {
                //如果波次订单全部移出，波次状态更新为取消
                Long notRemoveCount = waveTradeDao.queryNotRemoveCount(staff, waveId);
                if (notRemoveCount == null || notRemoveCount == 0L) {
                    wave.setStatus(Wave.STATUS_DELETED);
                    if ("订单前置打印".equals(opName)) {
                        wave.setCustomerName(StringUtils.isEmpty(wave.getCustomerName()) ? "波次前置打印取消波次" : wave.getCustomerName() + "；波次前置打印取消波次");
                    }
                }
            } else {
                update.setFinished(new Date());
                wave.setDistributionStatus(WaveDistributionStatus.EXAMINED.getValue());
            }
            if (Objects.equal(update.getStatus(), Wave.STATUS_FINISHED)) {
                update.setFinished(new Date());
            }
            update.setStatus(wave.getStatus());
            if (Objects.equal(update.getStatus(), Wave.STATUS_FINISHED)) {
                update.setFinished(new Date());
            }
            update.setDistributionStatus(wave.getDistributionStatus());
            update.setCustomerName(wave.getCustomerName());
            waveDao.update(staff, update);
            if (Objects.equal(WaveOutBusinessEnum.FINISH_WAVE.getOpName(), opName)) {
                logger.info(LogHelper.buildLog(staff, "通过手动完结波次！"));
            } else {
                waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.PRINT_FINISH_AUTO, "自动完结波次"));
            }
            eventCenter.fireEvent(this, new EventInfo("wave.finish.auto").setArgs(new Object[]{staff, Lists.newArrayList(wave)}), null);
        } else if (WaveDistributionStatus.WAIT_EXAMINE.getValue().equals(wave.getDistributionStatus())
                || WaveDistributionStatus.SEEDED.getValue().equals(wave.getDistributionStatus())
                || WaveDistributionStatus.PICKED.getValue().equals(wave.getDistributionStatus())) {
            //判断是否需要改为验货中
            Integer finishedCount = waveQueryDao.queryWaveTradeStatusCount(staff, wave.getWarehouseId(), waveId, openPackageExamine, 1);
            if (finishedCount != null && finishedCount > 0) {
                update.setDistributionStatus(WaveDistributionStatus.EXAMINING.getValue());
                waveDao.update(staff, update);
            }
        }
    }

    @Override
    public List<Wave> createWaves(Staff staff, List<Wave> waves) {
        return createWaves(staff, waves, Boolean.FALSE);
    }

    @Override
    @Transactional
    public List<Wave> createWaves(Staff staff, List<Wave> waves, Boolean autoCreate,Boolean withPositionNo) {
        Assert.notEmpty(waves, "波次信息不能为空！");

        List<WaveTrace> waveTraces = new ArrayList<>();
        WaveConfig waveConfig = waveConfigService.get(staff);
        waveHelpBusiness.setShortId(staff, waves, Wave.WAVE_SHORT_PREFIX);
        //todo 校验业务单据是否重复了
        for (Wave wave : waves) {
            List<WaveSorting> sortings = wave.getWaveSortings();
            Assert.notEmpty(sortings, "波次单据明细不能为空！");
            PickingType pickingType = PickingType.parseType(wave.getPickingType());
            setWavePickStyle(waveConfig, wave);
            wave.setWaveType(WaveType.convertToWaveType(pickingType));
            wave.setCreatorId(staff.getId());
            wave.setCreatorName(staff.getName());
            waveDao.insert(staff, wave);

            StringBuilder codes = new StringBuilder("生成").append(pickingType.getName()).append("波次:").append(wave.getId()).append("，关联单据：");
            for (WaveSorting sorting : sortings) {
                Assert.notNull(sorting.getSid(), "分拣单据不能为空！");
                codes.append(sorting.getCode()).append(",");
            }

            codes.deleteCharAt(codes.length() - 1);
            logger.debug(LogHelper.buildLog(staff, codes.toString()));

            // 生成日志
            if (!autoCreate) {
                recordOpLog(staff, "createWave", String.valueOf(wave.getId()), codes.toString());
                if (pickingType == PickingType.PURCHASE_RETURN) {
                    waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, wave.getId(), WaveTraceOperateEnum.CREATE_RETURN, "手工生成采退波次"));
                } else if (pickingType == PickingType.REPLENISH) {
                    waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, wave.getId(), WaveTraceOperateEnum.CREATE_REPLENISH, "手工生成补货波次"));
                } else if (pickingType == PickingType.STALL) {
                    waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, wave.getId(), WaveTraceOperateEnum.CREATE_STALL, "生成批发配货波次"));
                } else if (pickingType == PickingType.STALL_V2) {
                    waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, wave.getId(), WaveTraceOperateEnum.CREATE_STALL_V2, "生成档口配货波次"));
                } else if (pickingType == PickingType.ALLOCATE_OUT) {
                    waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, wave.getId(), WaveTraceOperateEnum.CREATE_ALLOCATE, "手工生成调拨波次"));
                }
            } else {
                opLogService.record(staff, new OpLog.Builder().domain(Domain.TRADE.getValue()).action("createWave").key(String.valueOf(wave.getId())).content(codes.toString()).created(new Date()).build());
                if (pickingType == PickingType.REPLENISH) {
                    waveTraces.add(WaveTraceUtils.buildWaveTrace(staff, wave.getId(), WaveTraceOperateEnum.CREATE_REPLENISH, "自动生成补货波次", 0, "系统自动"));
                }
            }

        }

        waveTraceService.batchAddWaveTrace(staff, waveTraces);

        createPickingAndSortings(staff, waves,withPositionNo);
        eventCenter.fireEvent(this, new EventInfo("wave.create").setArgs(new Object[]{staff, null, waves.stream().map(Wave::getId).collect(Collectors.toList())}), null);
        return waves;
    }

    @Override
    public Status pdaScanCreateWaves(Staff staff, String scanCode, Long warehouseId, Integer minGenerateWaveNum, Integer maxGenerateWaveNum,String ip) {
        Assert.notNull(warehouseId, "请选择仓库！");
        Assert.notNull(warehouseId,"仓库不能为空");
        Assert.notNull(minGenerateWaveNum,"最小触发波次数不能为空");
        Assert.notNull(maxGenerateWaveNum,"最大补充波次数不能为空");
        if(StringUtils.isEmpty(scanCode)){
            Assert.notNull(maxGenerateWaveNum,"扫码条码不能为空");
        }
        String pattern = "^S---.*";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(scanCode);
        if (!m.find()) {
            throw new IllegalArgumentException("扫码条码格式不符合格式");
        }
        String ruleGroupName = scanCode.substring(4);
        //查询规则分组下的规则
        List<WaveRule> waveRules= waveHelpBusiness.queryWaveRulesByRuleGroupName(staff,ruleGroupName);
        List<Long> ruleIds = waveRules.stream().map(WaveRule::getId).collect(Collectors.toList());
        boolean asyncGenerate = tradeWaveQueryService.asyncGenerateWave(staff, warehouseId, null, waveRules.get(0).getWaveRuleGroupId(), null,
                ip, StringUtils.EMPTY, WaveUtils.initDateRange(null, null, null, null),ruleIds,
                new PdaScanWaveConfig(minGenerateWaveNum,maxGenerateWaveNum));
        if (!asyncGenerate) {
            return new Status().setStatus("generating");
        }
        return Status.buildSuccessStatus();
    }

    @Override
    public List<Wave> createWaves(Staff staff, List<Wave> waves, Boolean autoCreate) {
        return createWaves(staff,waves,autoCreate,false);
    }

    private void setWavePickStyle(WaveConfig waveConfig, Wave wave) {
        wave.setSubSectionPick(WaveUtils.isSubSectionPick(wave.getPickingType(), waveConfig));
        if (Objects.equal(wave.getSubSectionPick(), CommonConstants.ENABLE_STATUS_NORMARL)) {
            wave.setPickStyle(PickStyle.SUB_SECTION.getStyleId());
        }
    }

    private void createPickingAndSortings(Staff staff, List<Wave> waves,Boolean withPositionNo) {
        List<WaveTrade> waveTrades = Lists.newArrayList();
        List<WaveSorting> allSortings = Lists.newArrayList();
        for (Wave wave : waves) {
            WavePicking picking = new WavePicking();
            picking.setWaveId(wave.getId());
            picking.setEnableStatus(WavePicking.STATUS_CREATED);
            picking.setPickingType(wave.getPickingType());
            wavePickingDao.insert(staff, picking);

            List<WaveSorting> sortings = wave.getWaveSortings();
            for (WaveSorting sorting : sortings) {
                WaveTrade waveTrade = new WaveTrade();
                waveTrade.setWaveId(wave.getId());
                waveTrade.setCode(sorting.getCode());
                waveTrade.setSid(sorting.getSid());
                waveTrade.setPositionNo(sorting.getPositionNo().longValue());
                waveTrades.add(waveTrade);

                sorting.setPickingId(picking.getId());
                if (CollectionUtils.isNotEmpty(sorting.getDetails())) {
                    sorting.getDetails().forEach(detail -> detail.setPickingId(sorting.getPickingId()));
                }
                sorting.setWaveId(wave.getId());
            }
            allSortings.addAll(sortings);
        }

        waveTradeDao.batchInsert(staff, waveTrades);
        logisticsWaveRelationBusiness.batchInsert(staff,waves);
        waveSortingService.batchInsert(staff, allSortings,withPositionNo);
    }

    @Override
    @Transactional
    public void cancelWave(Staff staff, Long waveId) {
        Assert.isTrue(waveId != null && waveId > 0L, "请选择波次！");

        cancelWaves(staff, Collections.singletonList(waveId), null, null);
    }

    @Override
    @Transactional
    public void cancelWave(Staff staff, Long waveId, String content) {
        Assert.isTrue(waveId != null && waveId > 0L, "请选择波次！");

        cancelWaves(staff, Collections.singletonList(waveId), content, null);
    }

    @Override
    @Transactional
    public void cancelWavesWithContent(Staff staff, List<Long> waveIds, String content) {
        cancelWaves(staff, waveIds, content, null);
    }

    @Override
    @Transactional
    public void cancelWaves(Staff staff, List<Long> waveIds, ProgressData progressData) {
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (waveConfig != null
                && waveConfig.getOpenPickingCancel() != null
                && waveConfig.getOpenPickingCancel() == 0) {
            Assert.notEmpty(waveIds, "波次Ids不能为空！");
            List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
            Assert.notEmpty(waves, "波次不存在！");
            waves.forEach(wave -> Assert.isTrue(!Objects.equal(wave.getDistributionStatus(), Wave.DISTRIBUTION_STATUS_ON), String.format("波次[%s]正在拣选中，无法取消！", wave.getId())));
        }
        cancelWaves(staff, waveIds, null, progressData);
    }

    @Override
    @Transactional
    public void forceFinishWave(Staff staff, Long waveId) {
        Wave waveInfo = queryWaveById(staff, waveId);
        Assert.notNull(waveInfo, String.format("波次[%s]不存在！", waveId));
        //全部未拣选已拣选部分拣选都删除
        if (wmsService.isNewWms(staff)) {
            wmsService.deleteAllocateGoodsRecords(staff, new ArrayList<Long>(Collections2.transform(wmsService.queryAllocateGoodsRecords(staff, new QueryAllocateGoodsRecordParams.Builder().waveId(waveId).containerTypes(WmsUtils.getRecordContainerTypesByBoxAllocate(waveInfo.getBoxAllocate())).statusList(AllocateGoodsRecord.AllocateGoodsStatusEnum.getList()).build()), new Function<AllocateGoodsRecord, Long>() {
                @Override
                public Long apply(AllocateGoodsRecord input) {
                    return input.getId();
                }
            })));
        }

        Wave update = new Wave();
        update.setStatus(Wave.STATUS_FINISHED);
        update.setFinished(new Date());
        update.setId(waveId);
        update.setFinished(new Date());
        logger.debug(LogHelper.buildLog(staff, "force finish wave:" + waveId));
        waveDao.update(staff, update);

        waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.WAVE_FORCE_FINISH, "采退单打回待出库状态强制完成波次"));

        eventCenter.fireEvent(this, new EventInfo("wave.finish").setArgs(new Object[]{staff, Lists.newArrayList(waveInfo)}), null);

        this.deleteWaveRuleCache(staff, null, Lists.newArrayList(waveInfo.getWarehouseId()));
    }

    private void cancelWaves(Staff staff, List<Long> waveIds, String content, ProgressData progressData) {
        Assert.notEmpty(waveIds, "请选择波次！");

        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "取消波次，waveIds=" + waveIds));
        }

        lockService.locks(WaveLockBusiness.key2ERPLock(staff, waveIds), () -> {
            waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_TRADE_WAVE_CANCEL, progressData, 20);
            List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
            Assert.notEmpty(waves, "波次不存在！");
            Assert.isTrue(waveIds.size() == waves.size(), "部分波次状态已更新，请刷新页面！");

            for (Wave wave : waves) {
                Assert.isTrue(Wave.STATUS_CREATED == wave.getStatus(), String.format("波次[%s]已完成或已取消！", wave.getId()));
                Assert.isTrue(Wave.DISTRIBUTION_STATUS_NONE == wave.getDistributionStatus() || Wave.DISTRIBUTION_STATUS_ON == wave.getDistributionStatus(), String.format("波次[%s]已拣选，无法取消！", wave.getId()));
            }

            long t1, t2, t3;
            long start = System.currentTimeMillis();
            List<Wave> updates = Lists.newArrayListWithCapacity(waves.size());
            List<Long> removeWaveSids = Lists.newArrayList();
            for (Wave wave : waves) {
                if (WavePickUtils.isTradeWave(wave)) {
                    // 能把隐藏单查出来
                    List<Long> sids = waveQueryDao.querySidsByWaveIds(staff, wave.getWarehouseId(), Lists.newArrayList(wave.getId()));
                    removeWaveSids.addAll(sids);
                }

                Wave update = new Wave();
                update.setId(wave.getId());
                update.setStatus(Wave.STATUS_DELETED);
                updates.add(update);
            }
            waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_TRADE_WAVE_CANCEL, progressData, 20);

            if (CollectionUtils.isNotEmpty(removeWaveSids)) {
                for (List<Long> subSids : Lists.partition(removeWaveSids, 2500)) {
                    UniqueCodeUtils.debugLog(staff, subSids, logger, "波次取消，踢出波次订单：", 3);
                    // 隐藏单的波次号也要置为0
                    List<Trade> updateTrades = subSids.stream().map(t -> {
                        Trade trade = new TbTrade();
                        trade.setSid(t);
                        trade.setWaveId(0L);
                        return trade;
                    }).collect(Collectors.toList());
                    waveTradeDao.updateTradeWaveId(staff, updateTrades);
                    logisticsWaveRelationBusiness.removeLogisticsWaveRelation(staff, LogisticsWaveRelation.LogisticsType.TRADE,new HashSet<>(subSids));
                    // 订单操作日志事件
                    eventCenter.fireEvent(this, new EventInfo("trade.wave.cancel").setArgs(new Object[]{staff}), WaveUtils.createSimpleTradesForTrace(staff, updateTrades));
                    if (WmsUtils.isOldWms(staff)){
                        eventCenter.fireEvent(this, new EventInfo("goodsSection.lock.wave.remove").setArgs(new Object[]{staff}), Lists.newArrayList(subSids));
                    }
                }
            }
            t1 = System.currentTimeMillis() - start;
            start = System.currentTimeMillis();

            //暂存位删除配货记录
            if (WmsUtils.isNewWms(staff)) {
                wmsService.deleteAllocateGoodsRecordsByMultiIds(staff, waveIds, null, null);
                orderStockProductService.cancelProductLock(staff, WmsUtils.toStringList(removeWaveSids));
            }
            waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_TRADE_WAVE_CANCEL, progressData, 20);
            t2 = System.currentTimeMillis() - start;
            start = System.currentTimeMillis();

            waveDao.batchUpdate(staff, updates);
            waveTraceService.batchAddWaveTrace(staff, WaveTraceUtils.buildBatchWaveTrace(staff, waveIds, WaveTraceOperateEnum.WAVE_CANCEL, StringUtils.isEmpty(content) ? "手工取消波次" : content));
            // 手动传入操作日志,说明从单据操作取消波次 ---手动操作
            boolean byHand = StringUtils.isNotEmpty(content);
            eventCenter.fireEvent(this, new EventInfo("wave.cancel").setArgs(new Object[]{staff, waves.stream().map(Wave::getId).collect(Collectors.toList()), byHand}), null);
            this.deleteWaveRuleCache(staff, null, waves.stream().map(Wave::getWarehouseId).distinct().collect(Collectors.toList()));

            // 波次取消后事件，异步处理wavesorting、wavetrade
            eventCenter.fireEvent(this, new EventInfo("wave.cancel.after").setArgs(new Object[]{staff, waves.stream().map(Wave::getId).collect(Collectors.toList())}), null);
            t3 = System.currentTimeMillis() - start;
            if (logger.isInfoEnabled()) {
                logger.info(LogHelper.buildLog(staff, "取消波次耗时：t1 = " + t1 + ", t2 = " + t2 + ", t3 = " + t3));
            }
            return null;
        });
    }

    @Override
    public void waveCancelAfter(Staff staff, List<Long> waveIds) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return;
        }
        List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }

        // 查waveTrade，能把隐藏单的sid查出来
        List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradesByWaveIds(staff, waves.stream().map(Wave::getId).collect(toList()));
        if (CollectionUtils.isEmpty(waveTrades)) {
            return;
        }
        // 已踢出的订单不用再踢
        waveTrades = waveTrades.stream().filter(waveTrade -> !Objects.equal(2, waveTrade.getTradeWaveStatus())).collect(toList());
        if (CollectionUtils.isEmpty(waveTrades)) {
            return;
        }
        List<List<WaveTrade>> partition = Lists.partition(waveTrades, 3000);
        for (List<WaveTrade> subTrades : partition) {
            //删除分拣记录
            waveSortingService.deleteSortingBySids(staff, subTrades.stream().map(WaveTrade::getSid).collect(toList()));
            // 更新波次订单状态
            for (Map.Entry<Long, List<WaveTrade>> entry : MapUtils.toMapList(subTrades, WaveTrade::getWaveId).entrySet()) {
                List<WaveTrade> value = entry.getValue();
                waveTradeDao.batchUpdate(staff, buildUpdateWaveTrades(staff, entry.getKey(), value.stream().map(WaveTrade::getSid).collect(toList()), WaveTrade.TRADE_WAVE_STATUS_NOT, Objects.equal(tradeServiceDubbo.queryTradeConfig(staff).getOpenPackageExamine(), CommonConstants.JUDGE_YES), WaveOutBusinessEnum.WAVE_CANCEL.getOpName()));
            }
        }
    }

    /**
     * 取消拣选增加商品日志
     * @param staff
     * @param waves
     */
    @Override
    public void addItemTraceLogByCancelPick(Staff staff, List<Wave> waves) {
        try {
            // 根据交易波次的id查询订单信息
            List<Trade> genTrades = queryTradesByWaveIds(staff, waves.stream()
                    .filter(WavePickUtils::isTradeWave)
                    .map(Wave::getId)
                    .collect(toList()));

            if (CollectionUtils.isEmpty(genTrades)) {
                return;
            }

            // 获取sid->waveId map
            Map<Long, String> sid2WaveIdMap = Optional.ofNullable(genTrades)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .collect(Collectors.toMap(Trade::getSid, data -> String.valueOf(data.getWaveId())));
            List<Long> removeWaveSids = Lists.newArrayList(sid2WaveIdMap.keySet());
            // 老代码迁移

            List<Order> orders = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(waveUseTradeServiceProxy.queryBySids(staff, true, removeWaveSids.toArray(new Long[0]))));
            if (CollectionUtils.isNotEmpty(removeWaveSids)) {
                WaveItemTraceLogHelper.batchRecodeItemTraceLogByWmsModelOrders(wmsService, itemTraceService, staff, Lists.newArrayList(sid2WaveIdMap.keySet()), ItemTraceActionEnum.WAVE_PICK_CANCEL.getCode(),
                        ItemTraceBillTypeEnum.WAVE.getCode(), WaveItemTraceActionModulePathEnum.WAVE_PICK_CANCEL_BY_WAVEMANAGE.getName(), orders, sid2WaveIdMap);
            } else {
                logger.error(LogHelper.buildLog(staff, String.format("取消拣选商品日志输出，商品为空")));
            }
        } catch (Exception e) {
            logger.error("取消拣选商品日志输出失败", e);
        }
    }

    /**
     * 根据波次id查询统计信息
     *
     * @param waveId 波次id
     */
    @Override
    public Wave queryWaveStatistic(Staff staff, Long waveId, Boolean printed) {
        Wave pre = waveDao.queryById(staff, waveId);
        if (pre == null || pre.getStatus() != Wave.STATUS_CREATED) {
            return null;
        }
        WaveFilterParams params = new WaveFilterParams()
                .setWaveId(waveId)
                .setWarehouseId(pre.getWarehouseId())
                .setPrinted(printed)
                .setWithExcep(true)
                .setAccurateStatWaveTradeNum(waveHelpBusiness.isAccurateStatWaveTradeNum(staff))
                .setIgnoreStatus(true);
        Wave wave = waveQueryDao.statWave(staff, params, null);
        if (wave == null) {
            wave = new Wave();
            wave.setTradesCount(0);
            wave.setItemCount(0);
        }
        return wave;
    }


    /**
     * 查询满足条件的波次订单关联记录列表
     *
     * @param wave   波次
     * @param ruleId 波次规则 id
     */
    @Override
    public PageListBase<AssoWaveTrade> queryTrades(Staff staff, Wave wave, Long ruleId, Page page) {
        WaveRule rule = waveRuleDao.queryById(staff, ruleId);
        if (rule == null) {
            throw new IllegalArgumentException("该波次规则不存在或已删除！");
        }
        return queryTrades(staff, wave, rule, page);
    }

    /**
     * 查询满足条件的波次订单关联记录列表
     *
     * @param wave     波次
     * @param waveRule 波次规则
     */
    private PageListBase<AssoWaveTrade> queryTrades(Staff staff, Wave wave, WaveRule waveRule, Page page) {
        PageListBase<AssoWaveTrade> pageList = new PageList<>();
        String items = wave.getWaveCondition().getItems();
        if (StringUtils.isEmpty(items)) {
            pageList.setTotal(0L);
            pageList.setList(Collections.emptyList());
            return pageList;
        }

        Long[] sids = ArrayUtils.toLongArray(items, ",");
        int total = sids.length;
        final Long[] subSids = org.apache.commons.lang3.ArrayUtils.subarray(sids, page.getStartRow(), Math.min(page.getPageNo() * page.getPageSize(), total));
        List<Trade> trades = waveTradesQueryBusiness.queryWaveTradesBySids(staff, subSids, waveRule);
        if (WaveHelpBusiness.isSuitSingleRule(staff, waveRule)) {
            WaveHelpBusiness.calculateItemNumAndItemKind(staff, trades);
        }
        Map<Long, Trade> sidTradeMap = TradeUtils.toMapBySid(trades);

        //获取trades对应的商品集合
        Set<Long> sysItemIds = Sets.newHashSet();
        Set<Long> sysSkuIds = Sets.newHashSet();
        List<Order>allOrders=new ArrayList<>();
        trades.stream().map(TradeUtils::getOrders4Trade).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                .forEach(order -> {
                    sysItemIds.add(order.getItemSysId());
                    sysSkuIds.add(order.getSkuSysId());
                    allOrders.add(order);
                });
        if(WaveItemChangeBusiness.changeItemFlag(staff)&&CollectionUtils.isNotEmpty(sysSkuIds)){
            List<WaveItem> waveItems = waveQueryDao.queryChangeItemSku(staff, new ArrayList<>(sysSkuIds));
            Set<Long> changeItemIds = waveItems.stream().map(WaveItem::getLastSysItemId).collect(toSet());
            waveItemChangeBusiness.filterChangeItemConditionOrders(staff,allOrders);
            sysItemIds.addAll(changeItemIds);
        }
        Map<String, DmjItem> itemMap = waveHelpBusiness.queryItemMap(staff, Lists.newArrayList(sysItemIds), Lists.newArrayList(sysSkuIds));
        Map<Long, String> itemOuterIdMap = itemMap.values().stream().filter(item -> !(item instanceof DmjSku)).collect(Collectors.toMap(DmjItem::getSysItemId, DmjItem::getOuterId, (k1, k2) -> k1));
        Map<Long, Pair<String,String>> userIdShipperMap=new HashMap<>();
        if(CompanyUtils.openMultiShipper(staff)){
            userIdShipperMap=waveHelpBusiness.queryShipByUserIds(staff, trades.stream().map(Trade::getUserId).filter(java.util.Objects::nonNull).collect(toSet()));
        }
        List<AssoWaveTrade> assos = Lists.newArrayList();
        for (Long sid : sids) {
            if (sidTradeMap.containsKey(sid)) {
                Trade trade = sidTradeMap.get(sid);
                AssoWaveTrade asso = new AssoWaveTrade();
                asso.setItemNums(trade.getItemNum());
                asso.setItemKinds(trade.getItemKindNum());
                asso.setSid(trade.getSid());
                asso.setShortId(trade.getShortId());
                asso.setMergeSid(trade.getMergeSid());
                asso.setOuterIdConcat(WaveUtils.buildOuterIdNumStr(trade));
                asso.setItemOuterIdConcat(WaveUtils.buildItemOuterIdNumStr(trade, itemOuterIdMap));
                asso.setPicPathConcat(buildPicPathStr(trade, itemMap));
                asso.setPositionNo(trade.getPositionNo());
                if(CompanyUtils.openMultiShipper(staff)){
                    Long userId = trade.getUserId();
                    Pair<String, String> stringStringPair = userIdShipperMap.get(userId);
                    if(stringStringPair!=null){
                        asso.setShipperId(stringStringPair.getKey());
                        asso.setShipperName(stringStringPair.getValue());
                    }
                }
                assos.add(asso);
            }
        }

        //每个波次的订单数量，共享位置不计算
        pageList.setTotal((long) total);
        pageList.setList(assos);
        return pageList;
    }

    private String buildPicPathStr(Trade trade, Map<String, DmjItem> itemMap) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            return "";
        }
        List<String> picPaths = new ArrayList<>();
        for (Order order : orders) {
            if (CollectionUtils.isNotEmpty(order.getSuits()) && order.isSuit(false)) {
                boolean hasEffectSuit = false;
                for (Order suit : order.getSuits()) {
                    if (!OrderUtils.isVirtualOrNonConsign(suit)) {
                        hasEffectSuit = true;
                    }
                }
                if (!hasEffectSuit) {
                    continue;
                }
            }
            if (OrderUtils.isVirtualOrNonConsign(order)) {
                continue;
            }
            if (StringUtils.isNotBlank(order.getSysPicPath())) {
                picPaths.add(order.getSysPicPath());
                continue;
            }
            DmjItem dmjItem = itemMap.get(WmsKeyUtils.buildOrderKey(order));
            if (dmjItem != null) {
                if (dmjItem instanceof DmjSku) {
                    if (!StockConstants.PATH_NO_PIC.equals(((DmjSku) dmjItem).getSkuPicPath())) {
                        picPaths.add(((DmjSku) dmjItem).getSkuPicPath());
                    } else {
                        DmjItem item = itemMap.get(WmsKeyUtils.buildItemKey(order.getItemSysId(), 0L));
                        if (item != null) {
                            picPaths.add(item.getPicPath());
                        }
                    }
                } else {
                    picPaths.add(dmjItem.getPicPath());
                }
            }
        }
        return String.join(",", picPaths);
    }


    @Override
    public PageListBase<Long> queryNotInWaveSids(Staff staff, Long warehouseId, List<Long> querySids, Page page) {
        return queryNotInWaveSids(staff, warehouseId, querySids, null, null, null, page);
    }

    @Override
    public PageListBase<Long> queryNotInWaveSids(Staff staff, Long warehouseId, List<Long> querySids, DateRange payTimeRange, Integer isExcep, List<Long> userIds, Page page) {
        WaveFilterParams params = new WaveFilterParams()
                .setWarehouseId(warehouseId)
                .setPrinted(false)
                .setWaveId(0L)
                .setWithExcep(true)
                .setSids(querySids)
                .setPayTimeRange(payTimeRange)
                .setUserIds(filterUserIds(userIds))
                .setIsExcep(isExcep);

        PageListBase<Long> pageList = new PageList<Long>();
        Long count = waveQueryDao.statWave(staff, params, null).getTradesCount().longValue();
        pageList.setTotal(count);
        if (count > 0L) {
            List<Long> sids = querySidsByWaveId(staff, 0L, params, page);
            pageList.setList(sids);
        }
        return pageList;
    }

    @Override
    public PageListBase<Long> queryNotInWaveSids(Staff staff, NotInWaveTradesQueryParams params, Page page) {
        List<Long> querySids = StringUtils.isBlank(params.getQuerySids()) ? null : getAsLongList(params.getQuerySids(), ",", true);
        DateRange payTimeRange = WaveUtils.initDateRange(params.getPayTimeStart(), params.getPayTimeEnd(), params.getAuditTimeStart(), params.getAuditTimeEnd());
        List<Long> userIds = ArrayUtils.toLongList(params.getUserIds());
        List<Long> activeUserIds = waveHelpBusiness.getActiveUserIds(staff);
        List<Long> templateIds = parseTemplateId(ArrayUtils.toStringList(params.getTemplateIds()));
        List<Long> logisticsCompanyIdList = ArrayUtils.toLongList(params.getLogisticsCompanyIds());
        List<Long> queryShortIds = StringUtils.isBlank(params.getQueryShortIds()) ? null : getAsLongList(params.getQueryShortIds(), ",", true);
        List<String> queryOutSids = StringUtils.isBlank(params.getQueryOutSids()) ? null : Arrays.asList(params.getQueryOutSids().split(","));
        WaveFilterParams filterParams = new WaveFilterParams()
                .setWarehouseId(params.getWarehouseId())
                .setPrinted(false)
                .setWaveId(0L)
                .setWithExcep(true)
                .setSids(querySids)
                .setOutSids(queryOutSids)
                .setShortIdsList(queryShortIds)
                .setPayTimeRange(payTimeRange)
                .setUserIds(filterUserIds(userIds))
                .setTemplateIds(templateIds)
                .setIsExcep(params.getIsExcep())
                .setLogisticsCompanyIdList(logisticsCompanyIdList)
                .setIncludeScalping(params.getScalping())
                .setIsExcep(params.getIsExcep())
                .setNotInWaveReason(params.getNotInWaveReason());
        if (CollectionUtils.isEmpty(filterParams.getUserIds())) {
            filterParams.setUserIds(activeUserIds);
        } else {
            filterParams.setUserIds(filterParams.getUserIds().stream().filter(activeUserIds::contains).collect(Collectors.toList()));
        }
        PageListBase<Long> pageList = new PageList<Long>();
        Long count = waveQueryDao.statWave(staff, filterParams, null).getTradesCount().longValue();
        pageList.setTotal(count);
        if (count > 0L) {
            filterParams.setItemNumSort(params.getItemNumSort());
            filterParams.setUseTradeNotConsignTable(true);
            List<Long> sids = querySidsByWaveId(staff, 0L, filterParams, page);
            pageList.setList(sids);
        }
        return pageList;
    }

    private List<Long> parseTemplateId(List<String> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return null;
        }
        List<Long> templateIdList = new ArrayList<>();
        for(String templateId : templateIds) {
            if (StringUtils.isBlank(templateId)) {
                continue;
            }
            if (templateId.contains("_")) {
                String[] exs = templateId.split("_");
                templateIdList.add(Long.parseLong(exs[0].trim()));
                continue;
            }
            if (templateId.startsWith("w")) {
                templateIdList.add(Long.parseLong(templateId.substring(1)));
                continue;
            }
            templateIdList.add(Long.parseLong(templateId));
        }
        return templateIdList;
    }

    private static List<Long> filterUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return userIds;
        }
        return userIds.stream().filter(u -> !Objects.equal(u, ALL_USER_ID_PARAM)).collect(toList());
    }

    @Override
    public Wave queryWaveById(Staff staff, Long waveId, Boolean fillTemplateName) {
        if (!DataUtils.checkLongNotEmpty(waveId)) {
            return null;
        }
        Wave wave = waveDao.queryById(staff, waveId);
        if (BooleanUtils.isTrue(fillTemplateName) && wave != null) {
            fillTemplateName(staff, Lists.newArrayList(wave));
            fillWaveLogisticsCompanyName(staff, Lists.newArrayList(wave));
        }
        if(CompanyUtils.openMultiShipper(staff)&&wave!=null&&StringUtils.isNotEmpty(wave.getShipperIds())){
            waveHelpBusiness.assembleShipperInfo(wave,staff);
        }
        return wave;
    }

    @Override
    public Wave queryWaveById(Staff staff, Long waveId) {
        return queryWaveById(staff, waveId, true);
    }

    @Override
    public Wave queryByCode(Staff staff, String code) {
        WavePicking wavePicking = wavePickingDao.getByWavePickingCode(staff, code, -1);
        if (wavePicking == null) {
            return null;
        }
        return waveDao.queryById(staff, wavePicking.getWaveId());
    }

    @Override
    public List<Wave> queryWaveByIds(Staff staff, Long[] ids) {
        return waveDao.queryByIds(staff, ids);
    }

    @Override
    public List<Wave> queryWaveByIds(Staff staff, Long[] ids, boolean needRemovedSidCount) {
        List<Wave> waves = queryWaveByIds(staff, ids);
        if (CollectionUtils.isNotEmpty(waves) && needRemovedSidCount) {
            Map<Long, Long> waveIdRemovedSidCountMap = waveTradeDao.queryRemovedSidCount(staff, Arrays.stream(ids).collect(toList()));
            if (org.apache.commons.collections.MapUtils.isNotEmpty(waveIdRemovedSidCountMap)) {
                for (Wave wave : waves) {
                    Long removedSidCount = waveIdRemovedSidCountMap.get(wave.getId());
                    if (removedSidCount != null) {
                        wave.setRemovedSidCount(removedSidCount.intValue());
                    }
                }
            }
        }
        return waves;
    }

    @Override
    public List<Wave> queryWaveByParam(Staff staff, WaveQueryFilterParam queryParam, Boolean isFillInfo) {
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (waveConfig != null && waveConfig.getInteger(WaveChatConfigsEnum.ONE_WAVE_MULTI_PICKING_CODE.getKey()) != 0) {
            queryParam.setQueryMultiPickingCode(1);
        }
        List<Wave> waveList = waveDao.queryPageList(staff, queryParam, null);
        if (CollectionUtils.isNotEmpty(waveList) && BooleanUtils.isTrue(isFillInfo)) {
            fillWaveInfo(staff, waveList, true, true);
        }
        fillMultiPickingCode(staff, waveList, queryParam);
        return  waveList;
    }

    @Override
    public Map<Long, WaveSorting> queryPickerNamesBySid(Staff staff, List<Long> sids, List<Long> waveIds) {
        if (CollectionUtils.isEmpty(sids) || CollectionUtils.isEmpty(waveIds)) {
            return Collections.emptyMap();
        }
        Map<Long, WaveSorting> sidWaveSortingMaps = Maps.newHashMap();

        // 查询明细拣选员
        List<WaveSortingDetail> sortingDetails = waveSortingDao.queryPickerNamesBySid(staff, waveIds, sids);
        if (CollectionUtils.isNotEmpty(sortingDetails)) {
            logger.debug(LogHelper.buildLog(staff, "根据波次Id和订单Sid未查询到分拣信息！"));

            Map<Long, List<WaveSortingDetail>> sidSortingDetailMaps = sortingDetails.stream().collect(Collectors.groupingBy(WaveSortingDetail::getSid));
            for (Map.Entry<Long, List<WaveSortingDetail>> entry : sidSortingDetailMaps.entrySet()) {
                if (CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                Set<String> pickerNames = Sets.newHashSet();
                entry.getValue().stream().filter(e -> StringUtils.isNotEmpty(e.getPickerNames()))
                        .forEach(d -> pickerNames.addAll(Lists.newArrayList(d.getPickerNames().split(","))));
                WaveSorting ws = new WaveSorting();
                ws.setPickerNames(Strings.join(",", pickerNames));
                sidWaveSortingMaps.put(entry.getKey(), ws);
            }
        }

        // 查询位置号
        List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradeByWaveIds(staff, waveIds);
        waveTrades = waveTrades.stream().filter(t -> sids.contains(t.getSid())).collect(toList());
        wavePositionService.fillPosition(staff, waveIds, waveTrades);
        if (CollectionUtils.isNotEmpty(waveTrades)) {
            Map<Long, Long> sidPositionCodeMaps = waveTrades.stream().collect(Collectors.toMap(WaveTrade::getSid, WaveTrade::getPositionNo, (a, b) -> a));
            for (Map.Entry<Long, Long> entry : sidPositionCodeMaps.entrySet()) {
                WaveSorting waveSorting = sidWaveSortingMaps.get(entry.getKey());
                Long positionNo = DataUtils.getZeroIfDefault(entry.getValue());
                if (waveSorting == null) {
                    WaveSorting ws = new WaveSorting();
                    ws.setPositionNo(positionNo > 0L ? positionNo.intValue() : null);
                    sidWaveSortingMaps.put(entry.getKey(), ws);
                } else {
                    waveSorting.setPositionNo(positionNo > 0L ? positionNo.intValue() : null);
                }
            }
        }

        return sidWaveSortingMaps;
    }

    @Override
    public Map<Long, TradePerformanceOptLog> queryTradePackLogBySid(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return Collections.emptyMap();
        }
        List<TradePerformanceOptLog> logList = tradePackLogDao.queryPackLogBySids(staff, sids);
        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyMap();
        }
        return logList.stream().collect(Collectors.toMap(TradePerformanceOptLog::getSid, v->v, (k1,k2)->k1));
    }


    @Override
    public List<Wave> queryFullWaveInfoByIds(Staff staff, List<Long> waveIds) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return Collections.emptyList();
        }

        WaveFilterParams params = new WaveFilterParams();
        params.setWaveIds(waveIds);
        return waveDao.queryPageList(staff, params, null);
    }

    @Override
    public List<Wave> queryWaveInfoByIds(Staff staff, List<Long> waveIds, boolean needPickerSorterInfo) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return Collections.emptyList();
        }

        WaveFilterParams params = new WaveFilterParams();
        params.setWaveIds(waveIds);
        List<Wave> waves = waveDao.queryPageList(staff, params, null);
        if (CollectionUtils.isNotEmpty(waves) && needPickerSorterInfo) {
            fillPickers(staff, waves);
            filterUnBeginWavePicker(staff, waves);
        }
        return waves;
    }

    @Override
    public boolean checkHasDistributionWaves(Staff staff, String distributionStatus) {
        Long count = waveDao.getDistributionWaveCount(staff, distributionStatus);
        return count != null && count > 0L;
    }

    @Override
    public void deleteAfterSendGoodsDetails(Staff staff) {
        deleteAfterSendGoodsDetails(staff, Lists.newArrayList());
    }

    @Override
    public void deleteAfterSendGoodsDetails(Staff staff, List<Long> warehouseIds) {
        WaveFilterParams condition = new WaveFilterParams();
        condition.setStatus(String.valueOf(Wave.STATUS_CREATED));
        if (CollectionUtils.isNotEmpty(warehouseIds)) {
            condition.setWarehouseIds(StringUtils.join(warehouseIds, ","));
        }
        Page page = new Page().setPageNo(1).setPageSize(500);
        List<Wave> waves;
        while (page.getPageNo() <=10 && (waves = waveDao.queryPageList(staff, condition, page)).size() > 0) {
            List<Wave> filterWaves = waves.stream().filter(wave -> wave.getDistributionStatus() >= Wave.DISTRIBUTION_STATUS_ON).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterWaves)) {
                List<Long> pickingIds = filterWaves.stream().map(Wave::getPickingId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(pickingIds)) {
                    waveSortingService.deleteDetailsAfterSendGoodsDefaultTx(staff, pickingIds);
                }
            }
            page.setPageNo(page.getPageNo() + 1);
        }
        logger.debug(LogHelper.buildLog(staff, "处理波次发货后分拣明细完成"));
    }

    @Override
    public PageListBase<WaveTrade> queryWaveTradeLogPageList(Staff staff, WaveTradeQueryParams params, Page page) {
        if (params.getWaveId() == null && CollectionUtils.isEmpty(params.getWaveIds())) {
            throw new IllegalArgumentException("请选择波次！");
        }
        TradeConfig tc = tradeServiceDubbo.queryTradeConfig(staff);
        WaveConfig waveConfig = waveConfigService.get(staff);
        PageListBase<WaveTrade> pageList = new PageList<WaveTrade>();
        pageList.setPage(page);
        handleWaveTradeQueryParams(staff, params);
        params.setOpenWaveUniqueCode(waveConfig.getOpenWaveUniqueCode());
        List<Long> loopWaveIds = wavePositionService.filterLoopWave(staff, params.getWaveIds());
        if (CollectionUtils.isNotEmpty(loopWaveIds) && params.getPositionNo() != null) {
            Long  positionNo = params.getPositionNo();
            params.setPositionNo(null);
            List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradePage(staff, params, null, tc);
            wavePositionService.fillPosition(staff, params.getWaveIds(), waveTrades);
            waveTrades = Optional.ofNullable(waveTrades).orElse(Collections.emptyList()).stream().filter(wt -> positionNo.equals(wt.getPositionNo())).collect(Collectors.toList());
            pageList.setTotal(Long.valueOf(waveTrades.size()));
            if (CollectionUtils.isNotEmpty(waveTrades) && page != null) {
                if (waveTrades.size() > page.getPageNo() * page.getPageSize()) {
                    waveTrades = waveTrades.subList((page.getPageNo() - 1 ) * page.getPageSize(), page.getPageNo() * page.getPageSize());
                } else {
                    waveTrades = waveTrades.subList((page.getPageNo() - 1 ) * page.getPageSize(), waveTrades.size());
                }
            }
            pageList.setList(waveTrades);
        } else {
            Long count = waveTradeDao.queryWaveTradeCount(staff, params, tc);
            pageList.setTotal(count);
            if (count != null && count > 0L) {
                List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradePage(staff, params, page, tc);
                wavePositionService.fillPosition(staff, params.getWaveIds(), waveTrades);
                pageList.setList(waveTrades);
            }
        }
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, "queryWaveTradeLogPageList查询到的订单数量：" + (CollectionUtils.isEmpty(pageList.getList()) ? "0" : pageList.getList().size())));
        }
        return pageList;
    }

    private void handleWaveTradeQueryParams(Staff staff, WaveTradeQueryParams params) {
        //处理加密参数
        String queryType = params.getQueryType();
        String queryText = params.getQueryText();

        if (StringUtils.isNotEmpty(queryType) && StringUtils.isNotEmpty(queryText)) {
            if ("receiverName".equals(queryType)) {
                //平台加密字段->不加密,查询特殊处理
                params.setOriginQueryText(queryText);
                queryText = tradeServiceDubbo.secretEncodeStr(staff, queryText);
                params.setQueryMethod(1);
            } else if ("receiverMobile".equals(queryType)) {
                //平台加密字段->不加密,查询特殊处理
                params.setOriginQueryText(queryText);
                queryText = tradeServiceDubbo.secretEncodeNum(staff, queryText);
                params.setQueryMethod(1);
            }
            params.setQueryText(queryText);
        }

        params.setExpressMap(dealExpressParam(staff, params.getExpress()));
    }


    private Map<Integer, List<Long>> dealExpressParam(Staff staff, String expressStr) {
        //处理模板参数
        if (StringUtils.isNotEmpty(expressStr)) {
            String[] express = ArrayUtils.toStringArray(expressStr);
            if (express != null && express.length > 0) {
                Map<Integer, List<Long>> typeIdMap = Maps.newHashMapWithExpectedSize(express.length);
                List<Long> wlbExpressIds = Lists.newArrayList();
                for (String template : express) {
                    String[] array = template.split("_");
                    if (array.length == 2 && "1".equals(array[1])) {
                        wlbExpressIds.add(Long.parseLong(array[0]));
                    } else if (array.length == 2) {
                        MapUtils.putToMapList(typeIdMap, Integer.parseInt(array[1].trim()), Long.parseLong(array[0].trim()));
                    } else if (template.startsWith("w")) {
                        MapUtils.putToMapList(typeIdMap, ExpressTemplateTypeEnum.ELECTRONIC.getValue(), Long.parseLong(template.trim().substring(1)));
                    } else if ("-1".equals(template) || "-2".equals(template)) {
                        typeIdMap.put(Integer.parseInt(template), null);
                    } else if ("0".equals(template)) {
                        typeIdMap.put(-3, null);
                    } else if (array.length == 1) {
                        MapUtils.putToMapList(typeIdMap, ExpressTemplateTypeEnum.NORMAL.getValue(), Long.parseLong(template.trim()));
                    }
                }

                if (!wlbExpressIds.isEmpty()) {
                    Map<Long, List<UserWlbExpressTemplate>> wlbTemplateMap = userWlbExpressTemplateService.queryAllByMasterTemplateId(staff, wlbExpressIds);
                    if (wlbTemplateMap != null && !wlbTemplateMap.isEmpty()) {
                        for (List<UserWlbExpressTemplate> templateList : wlbTemplateMap.values()) {
                            if (CollectionUtils.isNotEmpty(templateList)) {
                                for (UserWlbExpressTemplate template : templateList) {
                                    MapUtils.putToMapList(typeIdMap, ExpressTemplateTypeEnum.ELECTRONIC.getValue(), template.getId());
                                }
                            }
                        }
                    }
                }
                return typeIdMap;
            }
        }
        return null;
    }

    @Override
    public List<WaveTrade> queryWaveTradesByWaveId(Staff staff, Long waveId) {
        return waveTradeDao.queryWaveTradeByWaveId(staff, waveId);
    }

    @Override
    public List<WaveTrade> queryWaveTradesByWaveIds(Staff staff, List<Long> waveIds) {
        Assert.notEmpty(waveIds, "请选择波次！");
        List<WaveTrade> waveTrades = Lists.newArrayList();
        for (List<Long> subWaveIds : Lists.partition(waveIds, 200)) {
            List<WaveTrade> waveTradeList = waveTradeDao.queryWaveTradeByWaveIds(staff, subWaveIds);
            if (CollectionUtils.isNotEmpty(waveTradeList)) {
                waveTrades.addAll(waveTradeList);
            }
        }
        return waveTrades;
    }

    @Override
    public List<WaveTrade> queryRemovedWaveTradeBySysItemSkuId(Staff staff, WavePickingParam param) {
        List<WaveTrade> waveTrades = waveTradeDao.queryRemovedWaveTradeBySysItemSkuId(staff, param);
        if (CollectionUtils.isEmpty(waveTrades)) {
            return null;
        }
        String itemKey = WmsKeyUtils.buildItemKey(param.getSysItemId(), param.getSysSkuId());
        for (List<WaveTrade> part : Lists.partition(waveTrades, 500)) {
            Map<Long, Order> orderMap = new HashMap<>();
            List<Long> sids = part.stream().map(WaveTrade::getSid).collect(toList());
            List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, true, sids.toArray(new Long[0]));
            if (CollectionUtils.isEmpty(trades)) {
                continue;
            }
            for (Trade trade : trades) {
                List<Order> orders = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(trade));
                //筛选相同商品
                orders = orders.stream().filter(o -> itemKey.equals(WmsKeyUtils.buildItemKey(o.getItemSysId(), o.getSkuSysId()))).collect(toList());
                for (Order order : orders) {
                    Order existOrder = orderMap.get(trade.getSid());
                    if (existOrder != null) {
                        existOrder.setNum(ObjectUtils.defaultIfNull(existOrder.getNum(), 0) + ObjectUtils.defaultIfNull(order.getNum(), 0));
                    } else {
                        orderMap.put(trade.getSid(), order);
                    }
                }
            }
            for (WaveTrade waveTrade : part) {
                if (orderMap.get(waveTrade.getSid()) == null) {
                    continue;
                }
                WaveOrderVO waveOrderVO = WaveOrderVO.toBaseOrderModel(orderMap.get(waveTrade.getSid()));
                waveTrade.setOrders(Lists.newArrayList(waveOrderVO));
            }
        }
        return waveTrades;
    }

    @Override
    public List<WaveTrade> queryRemovedWaveTrade(Staff staff, WavePickingParam param) {
        List<WaveTrade> waveTrades = waveTradeDao.queryRemovedWaveTrade(staff, param);
        if (CollectionUtils.isEmpty(waveTrades)) {
            return null;
        }
        for (List<WaveTrade> part : Lists.partition(waveTrades, 500)) {
            List<Long> sids = part.stream().map(WaveTrade::getSid).collect(toList());
            List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, true, sids.toArray(new Long[0]));
            if (CollectionUtils.isEmpty(trades)) {
                continue;
            }
            Map<Long, Trade> tradeMap = trades.stream().collect(toMap(Trade::getSid, t -> t, (k1, k2) -> k1));
            for (WaveTrade waveTrade : part) {
                Trade trade = tradeMap.get(waveTrade.getSid());
                if (trade == null) {
                    continue;
                }
                List<Order> orders = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(trade));
                List<WaveOrderVO> orderVOS = new ArrayList<>();
                for (Order order : orders) {
                    orderVOS.add(WaveOrderVO.toBaseOrderModel(order));
                }
                waveTrade.setOrders(orderVOS);
            }
        }
        return waveTrades;
    }

    /**
     * 填充商品维度的拣选人名称
     *
     * @param details   波次拣选明细
     * @param waveItems
     */
    private void fillSortDetailPickerNames(List<WaveSortingDetail> details, List<WaveItem> waveItems) {
        Map<String, Set<String>> pickNameMap = Maps.newHashMap();
        if (details == null || waveItems == null) {
            return;
        }
        for (WaveSortingDetail detail : details) {
            String pickerNames = detail.getPickerNames();
            if (StringUtils.isEmpty(pickerNames)) {
                continue;
            }
            String key = ItemSkuBatch.initKey(detail.getSysItemId(), detail.getSysSkuId());
            if (CollectionUtils.isEmpty(pickNameMap.get(key))) {
                pickNameMap.put(key, new HashSet<>(Arrays.asList(pickerNames.split(","))));
            } else {
                Set<String> pickNameSet = pickNameMap.get(key);
                pickNameSet.addAll(Arrays.asList(pickerNames.split(",")));
            }
        }
        for (WaveItem waveItem : waveItems) {
            String waveItemKey = ItemSkuBatch.initKey(waveItem.getSysItemId(), waveItem.getSysSkuId());
            if (CollectionUtils.isNotEmpty(pickNameMap.get(waveItemKey))) {
                waveItem.setPickerNames(TradeIOUtils.join(pickNameMap.get(waveItemKey), ","));
            }
        }
    }

    /**
     * 查询波次商品维度信息
     *
     * @param staff
     * @param waveId    波次号
     * @param queryType 查询类型 0：已拣 1：未拣 2：缺货 4:商品种类 5导出
     * @return
     */
    @Override
    public List<WaveItem> queryWavePickingDetailsByItem(Staff staff, Long waveId, Integer queryType) {
        Wave wave = waveDao.queryById(staff, waveId);
        boolean forExport = Objects.equal(queryType, QUERY_TYPE_FOR_EXPORT);
        if (wave == null) {
            throw new IllegalArgumentException("该波次不存在！波次号："+ waveId);
        }
        WaveRule waveRule = null;
        if (wave.getRuleId() != null && Wave.PositionNoTypeEnum.ITEM.getValue().equals(wave.getPositionNoType())) {
            List<WaveRule> waveRules = waveRuleDao.queryByIds(staff, Lists.newArrayList(wave.getRuleId()));
            if (CollectionUtils.isNotEmpty(waveRules)) {
                waveRule = waveRules.get(0);
            }
        }

        List<WaveItem> waveItems;

        WavePicking wavePicking = wavePickingDao.getByWaveId(staff, waveId);
        if (wavePicking != null) {
            List<Integer> giftPickAndCheck;
            //跨境印花波次需要额外3
            if(STOCK_PRODUCT.getRuleId().equals(wave.getRuleId())&&StringUtils.isNotEmpty(wave.getUnderstockedGroup())){
                giftPickAndCheck=Lists.newArrayList(1,2,3);
            }else {
                giftPickAndCheck=Lists.newArrayList(1,2);
            }
            List<WaveSortingDetail> details = waveSortingDao.queryDetailsBySidsAndItem(staff, wavePicking.getId(), null, null, null, Objects.equal(queryType, 2) || Objects.equal(queryType, 5),giftPickAndCheck);
            Map<String,Map<String,List<WaveSortingDetail>>> waveSortingMap =  bulidWaveSortingDetailMap(staff,details);
            if (wavePositionService.needClearPosition(staff, wavePicking)) {
                wavePositionService.fillPosition(staff, details);
            }
            List<WaveItemPosition> itemPositions = details.stream().map(detail -> WaveUtils.buildWaveItemPosition(staff, detail)).collect(Collectors.toList());
            if (Wave.PositionNoTypeEnum.ITEM.getValue().equals(wave.getPositionNoType())) {
                waveItems = convertToWaveItemsOfItemPositionNo(staff, itemPositions, waveId, waveSortingMap, queryType, waveRule, null);
            } else {
                waveItems = convertToWaveItems(staff, itemPositions, waveSortingMap, queryType);
            }
            // 填充拣选人
            fillSortDetailPickerNames(details, waveItems);
        } else if (wave.getDistributionStatus() == Wave.DISTRIBUTION_STATUS_NONE || forExport) {
            if (queryType != null && queryType == 2) {
                return Collections.emptyList();
            }
            List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradeByWaveId(staff, waveId);
            Map<Long, Long> sidPositionMap = Maps.newHashMapWithExpectedSize(waveTrades.size());
            for (WaveTrade waveTrade : waveTrades) {
                if (waveTrade.getTradeWaveStatus() != WaveTrade.TRADE_WAVE_STATUS_NOT) {
                    sidPositionMap.put(waveTrade.getSid(), waveTrade.getPositionNo());
                }
            }
            Assert.notEmpty(sidPositionMap, "该波次无可拣选的商品！波次号："+ waveId);
            List<WaveItemPosition> suitSelfItemPositions = new ArrayList<>();
            List<WaveItemPosition> itemPositions = (forExport ? getWaveItemPositions4Export(staff, true, sidPositionMap, suitSelfItemPositions) : getWaveItemPositions(staff, false, sidPositionMap, suitSelfItemPositions));
            if (Wave.PositionNoTypeEnum.ITEM.getValue().equals(wave.getPositionNoType())) {
                waveItems = convertToWaveItemsOfItemPositionNo(staff, itemPositions, waveId,null, queryType, waveRule, suitSelfItemPositions);
            } else {
                for (WaveItemPosition itemPosition : itemPositions) {
                    itemPosition.setPositionNo(sidPositionMap.get(itemPosition.getSid()));
                    itemPosition.setPositionCode(WaveUtils.generatePositionCode(staff, itemPosition.getPositionNo()));
                }
                waveItems = convertToWaveItems(staff,itemPositions,null, queryType);
            }
        } else {
            throw new IllegalArgumentException("请先拣选！波次号：" + waveId);
        }

        waveItems = filterByQueryType(waveItems, queryType);
        fillWaveItemsInfo(staff, waveItems);
        return waveItems;
    }

    private List<WaveItemPosition> getWaveItemPositions4Export(Staff staff, boolean forExport, Map<Long, Long> sidPositionMap, List<WaveItemPosition> suitSelfItemPositions) {
        List<WaveItemPosition> itemPositions = new ArrayList<>();
        List<Order> orders = OrderUtils.toTree(tbOrderDAO.queryBySids(staff, sidPositionMap.keySet().toArray(new Long[0])));
        if (CollectionUtils.isEmpty(orders)) {
            return itemPositions;
        }

        for (Order order : OrderUtils.toEffectiveOrders(orders)) {
            if (OrderUtils.isVirtualOrNonConsign(order) || (!forExport && OrderUtils.isAfterSendGoods(order)) || !sidPositionMap.containsKey(order.getSid()) || giftNotPick(order)) {
                continue;
            }
            itemPositions.add(WaveUtils.buildWaveItemPosition(staff, null, order));
        }

        for (Order order : OrderUtils.toSuitSelfOrders(orders)) {
            if (OrderUtils.isVirtualOrNonConsign(order) || (!forExport && OrderUtils.isAfterSendGoods(order)) || !sidPositionMap.containsKey(order.getSid()) || giftNotPick(order)) {
                continue;
            }
            suitSelfItemPositions.add(WaveUtils.buildWaveItemPosition(staff, null, order));
        }

        return itemPositions;
    }

    private List<WaveItemPosition> getWaveItemPositions(Staff staff, boolean forExport, Map<Long, Long> sidPositionMap, List<WaveItemPosition> suitSelfItemPositions) {
        List<Trade> trades = waveUseTradeServiceProxy.queryBySidsNoFilter(staff, true, sidPositionMap.keySet().toArray(new Long[0]));
        List<WaveItemPosition> itemPositions = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            List<Order> orders = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(trade));
            for (Order order : orders) {
                if (OrderUtils.isVirtualOrNonConsign(order) || (!forExport && OrderUtils.isAfterSendGoods(order)) || !sidPositionMap.containsKey(trade.getSid()) || giftNotPick(order)) {
                    continue;
                }
                itemPositions.add(WaveUtils.buildWaveItemPosition(staff, trade, order));
            }
            for (Order order : OrderUtils.toSuitSelfOrders(TradeUtils.getOrders4Trade(trade))) {
                if (OrderUtils.isVirtualOrNonConsign(order) || (!forExport && OrderUtils.isAfterSendGoods(order)) || !sidPositionMap.containsKey(order.getSid()) || giftNotPick(order)) {
                    continue;
                }
                suitSelfItemPositions.add(WaveUtils.buildWaveItemPosition(staff, trade, order));
            }
        }
        return itemPositions;
    }

    /**
     * 构建商品播种信息map
     * Map<itemID+skuID,Map<播种状态,List<WaveSortingDetail>>>
     * @return
     */
    private Map<String,Map<String,List<WaveSortingDetail>>> bulidWaveSortingDetailMap(Staff staff,List<WaveSortingDetail> details){
        Map<String,Map<String,List<WaveSortingDetail>>> map = new HashMap();
        for(WaveSortingDetail detail:details){
            String key = detail.getSysItemId()+"_"+(detail.getSysSkuId()<0?0:detail.getSysSkuId());
            String positionCode = WaveUtils.generatePositionCode(staff, (long) detail.getPositionNo());
            if(map.containsKey(key)){
                Map<String,List<WaveSortingDetail>> wsdMap = map.get(key);
                if(wsdMap.containsKey(positionCode)){
                    List<WaveSortingDetail> list = wsdMap.get(positionCode);
                    list=CollectionUtils.isEmpty(list)?new ArrayList<>():list;
                    list.add(detail);
                    wsdMap.put(positionCode,list);
                }else{
                    wsdMap.put(positionCode,new ArrayList<WaveSortingDetail>(){{add(detail);}});
                }
            }else{
                Map<String,List<WaveSortingDetail>> wsdMap = new HashMap();
                wsdMap.put(positionCode,new ArrayList<WaveSortingDetail>(){{add(detail);}});
                map.put(key,wsdMap);
            }
        }
        return map;
    }

    private List<WaveItemPosition> getMatchedInfo(Staff staff, List<WaveItemPosition> waveItemPositions, Map<String, Map<String, List<WaveSortingDetail>>> waveSortingMap, Integer queryType){
        if(waveSortingMap!=null){
            for(WaveItemPosition position:waveItemPositions) {
                String key = position.getSysItemId() + "_" + NumberUtils.negative2Zero(position.getSysSkuId());
                String positionCode = WaveUtils.generatePositionCode(staff, (long) position.getPositionNo());
                if(waveSortingMap.get(key)!=null) {
                    List<WaveSortingDetail> waveSortingDetails = waveSortingMap.get(key).get(positionCode);
                    if(CollectionUtils.isNotEmpty(waveSortingDetails)) {
                        //该位置号商品总数
                        position.setMatchedNumCount(waveSortingDetails.stream().mapToInt(WaveSortingDetail::getPickedNum).sum());
                        //该位置号未播种商品数
                        position.setUnMatchedNum(position.getMatchedNumCount() - waveSortingDetails.stream().mapToInt(WaveSortingDetail::getMatchedNum).sum());
                    }
                }
            }
        }
        if (!Objects.equal(queryType, FROM_ITEM_KIND)) {
            return waveItemPositions;
        }
        //若某个位置号对应的未播种数量=0，则不显示该位置号及数量,按位置号排列结果集
        return waveItemPositions.stream().filter(waveItemPosition -> waveItemPosition.getUnMatchedNum()!=null&&waveItemPosition.getUnMatchedNum()>0).sorted(Comparator.comparing(WaveItemPosition::getPositionNo)).collect(Collectors.toList());}

    @Override
    public List<WaveSortingDetail> queryWavePickingDetails(Staff staff, List<Long> waveIds, Long sysItemId, Long sysSkuId) {

        List<Wave> waveList = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(waveList)) {
            throw new IllegalArgumentException("波次不存在！");
        }

        List<WavePicking> wavePickings = wavePickingDao.getByWaveIds(staff, waveIds);
        List<Long> pickingIds = wavePickings.stream().map(WavePicking::getId).collect(toList());
        List<WaveSortingDetail> details = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(wavePickings)) {
            details = waveSortingDao.queryDetailsByPickingIdsAndItem(staff, pickingIds, sysItemId, sysSkuId, null);
        }

        return details;
    }

    @Override
    public List<WaveSortingDetail> queryWaveSortingDetailsByParam(Staff staff, WaveSortingParam param) {
        return waveSortingDao.queryWaveSortingDetailsByParam(staff, param);
    }

    @Override
    public List<WaveSortingDetail> queryWaveItemByWaveId(Staff staff, Long waveId, Integer showType, String outerId, boolean usePostStatus) {
        List<WaveSortingDetail> sortingDetails = null;
        Wave wave = waveDao.queryById(staff, waveId);
        if (wave == null) {
            throw new IllegalArgumentException("该波次不存在！");
        }
        WavePicking wavePicking = wavePickingDao.getByWaveId(staff, waveId);
        if (wavePicking != null) {
            WavePickingParam param = new WavePickingParam();
            param.setStockStatus(0);
            param.setOpenWaveUniqueCode(0);
            param.setPickingIds(Lists.newArrayList(wavePicking.getId()));
            param.setSearchOuterId(outerId);
            param.setUsePostStatus(usePostStatus);
            // showType 1 按商品拣选倒序，null 老逻辑
            sortingDetails = Objects.equal(showType, CommonConstants.JUDGE_YES) ? queryDetailsSortByPickingDesc(staff, waveId, wavePicking,outerId, usePostStatus) : waveSortingDao.queryItemMapByPickingId(staff, param, null);
            if (CollectionUtils.isEmpty(sortingDetails)) {
                sortingDetails = waveSortingDao.queryItemMapByPickingId(staff, param, null);
            }
            Map<String, DmjItem> itemKeyMap = waveHelpBusiness.getDetailsItemSkuMap(staff, sortingDetails);
            for (WaveSortingDetail detail : sortingDetails) {
                DmjItem dmjItem = itemKeyMap.get(WaveHelpBusiness.buildItemKey(detail));
                fillItemInfo(detail,dmjItem );
                if(CompanyUtils.openMultiShipper(staff)){
                    detail.setShipper(dmjItem.getShipper());
                    detail.setShipperName(dmjItem.getShipper());
                }
            }
        }
        return sortingDetails;
    }

    /**
     * 按商品拣选倒序
     */
    private List<WaveSortingDetail> queryDetailsSortByPickingDesc(Staff staff, Long waveId, WavePicking wavePicking,String outerId, boolean usePostStatus) {
        List<WaveSortingDetail> pickItemWaveDetails;
        // 获取pickitem数据，货位维度
        try {
            pickItemWaveDetails = wmsPdaDubboService.queryPdaItemByWaveId(staff, waveId, getRemovedSidsByWaveId(staff, waveId));
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "按商品拣选倒序调用dubbo失败"), e);
            return Lists.newArrayList();
        }
        // 获取商品打印数
        WavePickingParam param = new WavePickingParam();
        param.setStockStatus(0);
        param.setOpenWaveUniqueCode(0);
        param.setPickingIds(Lists.newArrayList(wavePicking.getId()));
        param.setSearchOuterId(outerId);
        param.setUsePostStatus(usePostStatus);
        List<WaveSortingDetail> sortingDetails = waveSortingDao.queryItemMapByPickingId(staff, param, null);
        // 分配打印数
        Map<String, List<WaveSortingDetail>> waveSortingDetailsMap = pickItemWaveDetails.stream().collect(groupingBy(WmsKeyUtils::buildItemKey));
        Map<String, IntSummaryStatistics> key2PrintNum = sortingDetails.stream().collect(groupingBy(WmsKeyUtils::buildItemKey, summarizingInt(WaveSortingDetail::getPrintItemNum)));
        for (Map.Entry<String, List<WaveSortingDetail>> stringListEntry : waveSortingDetailsMap.entrySet()) {
            List<WaveSortingDetail> details = stringListEntry.getValue();
            String key = stringListEntry.getKey();
            int sum = Optional.ofNullable(key2PrintNum.get(key)).map(IntSummaryStatistics::getSum).orElse(0L).intValue();
            for (WaveSortingDetail detail : details) {
                int printNum = Math.min(sum, DataUtils.getZeroIfDefaultI(detail.getTotalNum()));
                detail.setPrintItemNum(printNum);
                sum -= printNum;
                if (sum <= 0) {
                    break;
                }
            }
        }
        return pickItemWaveDetails;
    }

    private List<Long> getRemovedSidsByWaveId(Staff staff, Long waveId) {
        List<Long> sids = new ArrayList<>();
        if (waveId == null) {
            return sids;
        }
        WaveTradeQueryParams queryParams = new WaveTradeQueryParams();
        queryParams.setWaveId(waveId);
        queryParams.setTradeWaveStatus(WaveTrade.TRADE_WAVE_STATUS_NOT);
        List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradePage(staff, queryParams, null, null);
        if (CollectionUtils.isEmpty(waveTrades)) {
            return sids;
        } else {
            return waveTrades.stream().map(WaveTrade::getSid).distinct().collect(toList());
        }
    }

    private void fillItemInfo(WaveSortingDetail detail, DmjItem item) {
        if (detail == null || item == null) {
            return;
        }
        detail.setOrderId(null);
        WavePickUtils.fillItemInfo(detail, item);
    }

    private boolean giftNotPick(Order order) {
        return OrderUtils.notPick(order);
    }

    private List<WaveItem> filterByQueryType(List<WaveItem> waveItems, Integer queryType) {
        if (waveItems.isEmpty() || queryType == null || queryType == 0) {
            return waveItems;
        }

        if (queryType == 1) {
            return filterWaveItems(waveItems, waveItem -> waveItem.getUnPickNum() > 0, waveItemPosition -> waveItemPosition.getUnPickNum() > 0);
        } else if (queryType == 2) {
            return filterWaveItems(waveItems, waveItem -> waveItem.getShortageNum() > 0, waveItemPosition -> waveItemPosition.getShortageNum() > 0);
        }
        return waveItems;
    }

    private List<WaveItem> filterWaveItems(List<WaveItem> waveItems, Predicate<WaveItem> itemPredicate, Predicate<WaveItemPosition> positionPredicate) {
        return waveItems.stream()
                .filter(itemPredicate)
                .peek(waveItem -> waveItem.setPositions(Optional.ofNullable(waveItem.getPositions()).orElse(Collections.emptyList())
                        .stream().filter(positionPredicate).collect(Collectors.toList())))
                .collect(Collectors.toList());
    }

    private List<WaveItem> convertToWaveItems(Staff staff, List<WaveItemPosition> itemPositions, Map<String, Map<String, List<WaveSortingDetail>>> waveSortingMap, Integer queryType) {
        List<WaveItem> waveItems = Lists.newArrayList();
        Multimap<String, WaveItemPosition> itemDetailMap = HashMultimap.create();
        //先根据相同商品进行分组
        for (WaveItemPosition itemPosition : itemPositions) {
            itemDetailMap.put(WmsKeyUtils.buildItemKey(itemPosition.getSysItemId(), itemPosition.getSysSkuId()), itemPosition);
        }
        for (Map.Entry<String, Collection<WaveItemPosition>> entry : itemDetailMap.asMap().entrySet()) {
            Collection<WaveItemPosition> itemDetails = entry.getValue();
            if (itemDetails.isEmpty()) {
                continue;
            }
            //根据相同位置号进行合并数量，按位置号排序
            Map<Long, WaveItemPosition> waveItemPosMap = Maps.newTreeMap(Long::compareTo);
            int itemNum = 0, pickedNum = 0, matchedNum = 0, shortageNum = 0;
            for (WaveItemPosition itemDetail : itemDetails) {
                itemNum += itemDetail.getItemNum();
                pickedNum += itemDetail.getPickedNum();
                matchedNum += itemDetail.getMatchedNum();
                shortageNum += itemDetail.getShortageNum();
                itemDetail.setUnPickNum(itemDetail.getItemNum() - itemDetail.getPickedNum() - itemDetail.getShortageNum());
                WaveItemPosition itemPosition = waveItemPosMap.get(itemDetail.getPositionNo());
                if (itemPosition == null) {
                    waveItemPosMap.put(itemDetail.getPositionNo(), itemDetail);
                } else {
                    itemPosition.setPickedNum(itemDetail.getPickedNum() + itemPosition.getPickedNum());
                    itemPosition.setItemNum(itemDetail.getItemNum() + itemPosition.getItemNum());
                    itemPosition.setMatchedNum(itemDetail.getMatchedNum() + itemPosition.getMatchedNum());
                    itemPosition.setShortageNum(itemDetail.getShortageNum() + itemPosition.getShortageNum());
                    itemPosition.setUnPickNum(itemDetail.getUnPickNum() + itemPosition.getUnPickNum());
                }
            }
            List<WaveItemPosition> positions = Lists.newArrayList(waveItemPosMap.values());
            WaveItemPosition one = positions.get(0);
            WaveItem waveItem = new WaveItem();
            waveItem.setSysItemId(one.getSysItemId());
            waveItem.setSysSkuId(one.getSysSkuId());
            waveItem.setNum(itemNum);
            // 获取实际拣选数量
            waveItem.setPickedNum(pickedNum);
            waveItem.setMatchedNum(matchedNum);
            waveItem.setShortageNum(shortageNum);
            // 未捡练数 = item总数 - 已捡练数 - shortageNum
            waveItem.setUnPickNum(itemNum - waveItem.getPickedNum() - shortageNum);
            waveItem.setUnMatchedNum(waveItem.getPickedNum()-waveItem.getMatchedNum());
            waveItem.setPositions(getMatchedInfo(staff,positions,waveSortingMap, queryType));
            //减少返回内容
            for (WaveItemPosition position : positions) {
                position.setSysItemId(null);
                position.setSysSkuId(null);
                position.setSid(null);
            }
            waveItems.add(waveItem);
        }
        return waveItems;
    }

    private List<WaveItem> convertToWaveItemsOfItemPositionNo(Staff staff, List<WaveItemPosition> itemPositions, Long waveId, Map<String, Map<String, List<WaveSortingDetail>>> waveSortingMap, Integer queryType, WaveRule waveRule, List<WaveItemPosition> suitSelfItemPositions) {
        if (CollectionUtils.isNotEmpty(itemPositions) && waveRule != null && waveRule.getRuleCondition() != null && Objects.equal(waveRule.getRuleCondition().getSeedItemPositionSuit(), 1)) {
            return convertToWaveItemsOfSuitItemPositionNo(staff, waveId, itemPositions, suitSelfItemPositions);
        }
        Multimap<String, WaveItemPosition> itemDetailMap = HashMultimap.create();
        for (WaveItemPosition itemPosition : itemPositions) {
            itemDetailMap.put(itemPosition.getSysItemId() + "_" + NumberUtils.negative2Zero(itemPosition.getSysSkuId()), itemPosition);
        }

        Map<String, WaveItemPosition> itemPositionMap = new HashMap<String, WaveItemPosition>(itemDetailMap.size(), 1);
        for (Map.Entry<String, Collection<WaveItemPosition>> entry : itemDetailMap.asMap().entrySet()) {
            int itemNum = 0, pickedNum = 0, matchedNum = 0, shortageNum = 0;
            Collection<WaveItemPosition> itemDetails = entry.getValue();
            for (WaveItemPosition itemDetail : itemDetails) {
                itemNum += itemDetail.getItemNum();
                pickedNum += itemDetail.getPickedNum();
                matchedNum += itemDetail.getMatchedNum();
                shortageNum += itemDetail.getShortageNum();
                itemDetail.setUnPickNum(itemDetail.getItemNum() - itemDetail.getPickedNum() - itemDetail.getShortageNum());
            }
            WaveItemPosition waveItemPosition = new WaveItemPosition();
            waveItemPosition.setItemNum(itemNum);
            waveItemPosition.setPickedNum(pickedNum);
            waveItemPosition.setMatchedNum(matchedNum);
            waveItemPosition.setShortageNum(shortageNum);
            waveItemPosition.setUnPickNum(itemNum - pickedNum - shortageNum);
            itemPositionMap.put(entry.getKey(), waveItemPosition);
        }

        List<AssoWaveItem> assoWaveItems = assoWaveItemDAO.queryAssoWaveItemPage(staff, waveId, null);
        List<WaveItem> waveItems = Lists.newArrayListWithCapacity(assoWaveItems.size());
        for (AssoWaveItem assoWaveItem : assoWaveItems) {
            String key = assoWaveItem.getItemSysId() + "_" + NumberUtils.negative2Zero(assoWaveItem.getSkuSysId());
            WaveItemPosition position = itemPositionMap.get(key);
            if (null == position) {
                continue;
            }
            position.setPositionNo(assoWaveItem.getPositionNo());
            WaveItem waveItem = new WaveItem();
            waveItem.setSysItemId(assoWaveItem.getItemSysId());
            waveItem.setSysSkuId(assoWaveItem.getSkuSysId());
            waveItem.setNum(position.getItemNum());
            waveItem.setShortageNum(position.getShortageNum());
            waveItem.setPickedNum(position.getPickedNum());
            waveItem.setMatchedNum(position.getMatchedNum());
            waveItem.setUnPickNum(position.getItemNum() - position.getPickedNum() - position.getShortageNum());
            //未播种总数 = 商品数-已播种数
            waveItem.setUnMatchedNum(waveItem.getPickedNum()-waveItem.getMatchedNum());
            waveItem.setPositions(getMatchedInfo(staff, Collections.singletonList(position), waveSortingMap, queryType));
            waveItems.add(waveItem);
        }
        return waveItems;
    }

    private List<WaveItem> convertToWaveItemsOfSuitItemPositionNo(Staff staff, Long waveId, List<WaveItemPosition> itemPositions, List<WaveItemPosition> suitSelfItemPositions) {
        List<Long> suitOrderIds = itemPositions.stream().filter(p -> Objects.equal(p.getSuitType(), 1) && p.getCombineId() != null && p.getCombineId() > 0).map(WaveItemPosition::getCombineId).collect(toList());
        Map<Long, WaveItemPosition> suitDetailMap = null;
        if (suitSelfItemPositions == null && CollectionUtils.isNotEmpty(suitOrderIds)) {
            suitSelfItemPositions = new ArrayList<>();
            List<WaveSortingDetail> suitDetails = waveSortingDao.queryDetailsByOrderIds(staff, suitOrderIds);
            for (WaveSortingDetail detail : suitDetails) {
                suitSelfItemPositions.add(WaveUtils.buildWaveItemPosition(staff, detail));
            }
        }
        if (CollectionUtils.isNotEmpty(suitSelfItemPositions)) {
            suitDetailMap = suitSelfItemPositions.stream().collect(toMap(WaveItemPosition::getOrderId, d -> d, (k1, k2) -> k1));
        }
        Map<String, WaveItemPosition> itemPositionMap = new HashMap<>();
        for (WaveItemPosition detail : itemPositions) {
            String key = detail.getSysItemId() + "_" + NumberUtils.negative2Zero(detail.getSysSkuId());
            WaveItemPosition sumPosition = itemPositionMap.get(key);
            if (sumPosition != null) {
                sumPosition.setItemNum(sumPosition.getItemNum() + detail.getItemNum());
                sumPosition.setPickedNum(sumPosition.getPickedNum() + detail.getPickedNum());
                sumPosition.setMatchedNum(sumPosition.getMatchedNum() + detail.getMatchedNum());
                sumPosition.setShortageNum(sumPosition.getShortageNum() + detail.getShortageNum());
            } else {
                WaveItemPosition waveItemPosition = new WaveItemPosition();
                waveItemPosition.setSysItemId(detail.getSysItemId());
                waveItemPosition.setSysSkuId(detail.getSysSkuId());
                waveItemPosition.setItemNum(detail.getItemNum());
                waveItemPosition.setPickedNum(detail.getPickedNum());
                waveItemPosition.setMatchedNum(detail.getMatchedNum());
                waveItemPosition.setShortageNum(detail.getShortageNum());
                itemPositionMap.put(key, waveItemPosition);
            }
            if (itemPositionMap.get(key).getPositions() == null) {
                itemPositionMap.get(key).setPositions(new ArrayList<>());
            }
            if (Objects.equal(detail.getSuitType(), 1) && detail.getCombineId() != null && detail.getCombineId() > 0 && suitDetailMap != null && suitDetailMap.get(detail.getCombineId()) != null) {
                WaveItemPosition suitPosition = new WaveItemPosition();
                suitPosition.setSysItemId(suitDetailMap.get(detail.getCombineId()).getSysItemId());
                suitPosition.setSysSkuId(suitDetailMap.get(detail.getCombineId()).getSysSkuId());
                suitPosition.setSid(detail.getSid());
                suitPosition.setItemNum(detail.getItemNum());
                suitPosition.setPickedNum(detail.getPickedNum());
                suitPosition.setMatchedNum(detail.getMatchedNum());
                suitPosition.setShortageNum(detail.getShortageNum());
                suitPosition.setMatchedNumCount(detail.getPickedNum());
                suitPosition.setUnMatchedNum(detail.getPickedNum()-detail.getMatchedNum());
                itemPositionMap.get(key).getPositions().add(suitPosition);
            } else {
                WaveItemPosition singlePosition = new WaveItemPosition();
                singlePosition.setSid(detail.getSid());
                singlePosition.setSysItemId(detail.getSysItemId());
                singlePosition.setSysSkuId(detail.getSysSkuId());
                singlePosition.setItemNum(detail.getItemNum());
                singlePosition.setPickedNum(detail.getPickedNum());
                singlePosition.setMatchedNum(detail.getMatchedNum());
                singlePosition.setShortageNum(detail.getShortageNum());
                singlePosition.setMatchedNumCount(detail.getPickedNum());
                singlePosition.setUnMatchedNum(detail.getPickedNum()-detail.getMatchedNum());
                itemPositionMap.get(key).getPositions().add(singlePosition);
            }
        }
        List<AssoWaveItem> assoWaveItems = assoWaveItemDAO.queryAssoWaveItemPage(staff, waveId, null);
        Map<String, AssoWaveItem> assoWaveItemMap = assoWaveItems.stream().collect(toMap(a -> a.getItemSysId() + "_" + NumberUtils.negative2Zero(a.getSkuSysId()) + "_" + a.getSid(), a -> a, (k1, k2) -> k1));
        List<WaveItem> waveItems = Lists.newArrayListWithCapacity(assoWaveItems.size());
        for (Map.Entry<String, WaveItemPosition> entry : itemPositionMap.entrySet()) {
            WaveItemPosition position = entry.getValue();
            WaveItem waveItem = new WaveItem();
            waveItem.setSysItemId(position.getSysItemId());
            waveItem.setSysSkuId(position.getSysSkuId());
            waveItem.setNum(position.getItemNum());
            waveItem.setShortageNum(position.getShortageNum());
            waveItem.setPickedNum(position.getPickedNum());
            waveItem.setMatchedNum(position.getMatchedNum());
            waveItem.setUnPickNum(position.getItemNum() - position.getPickedNum() - position.getShortageNum());
            //未播种总数 = 商品数-已播种数
            waveItem.setUnMatchedNum(waveItem.getPickedNum()-waveItem.getMatchedNum());
            waveItem.setPositions(mergeSubPosition(staff, position, assoWaveItemMap));
            waveItems.add(waveItem);
        }
        return waveItems;
    }

    private List<WaveItemPosition> mergeSubPosition(Staff staff, WaveItemPosition position, Map<String, AssoWaveItem> assoWaveItemMap) {
        List<WaveItemPosition> waveItemPositions = position.getPositions();
        if (CollectionUtils.isEmpty(waveItemPositions)) {
            return Lists.newArrayList();
        }
        Map<String, WaveItemPosition> subItemPositionMap = new HashMap<>();

        for (WaveItemPosition subPosition : waveItemPositions) {
            String key = subPosition.getSysItemId() + "_" + NumberUtils.negative2Zero(subPosition.getSysSkuId()) + "_" + subPosition.getSid();
            if (assoWaveItemMap.get(key) == null) {
                continue;
            }
            subPosition.setPositionNo(assoWaveItemMap.get(key).getPositionNo());
            WaveItemPosition sumPosition = subItemPositionMap.get(key);
            if (sumPosition != null) {
                sumPosition.setItemNum(sumPosition.getItemNum() + subPosition.getItemNum());
                sumPosition.setPickedNum(sumPosition.getPickedNum() + subPosition.getPickedNum());
                sumPosition.setMatchedNum(sumPosition.getMatchedNum() + subPosition.getMatchedNum());
                sumPosition.setShortageNum(sumPosition.getShortageNum() + subPosition.getShortageNum());
                sumPosition.setMatchedNumCount(sumPosition.getMatchedNumCount() + subPosition.getMatchedNumCount());
                sumPosition.setUnMatchedNum(sumPosition.getUnMatchedNum() + subPosition.getUnMatchedNum());
                sumPosition.setUnPickNum(sumPosition.getUnPickNum() + subPosition.getItemNum() - subPosition.getPickedNum() - subPosition.getShortageNum());
            } else {
                WaveItemPosition waveItemPosition = new WaveItemPosition();
                waveItemPosition.setSysItemId(subPosition.getSysItemId());
                waveItemPosition.setSysSkuId(subPosition.getSysSkuId());
                waveItemPosition.setItemNum(subPosition.getItemNum());
                waveItemPosition.setPickedNum(subPosition.getPickedNum());
                waveItemPosition.setMatchedNum(subPosition.getMatchedNum());
                waveItemPosition.setShortageNum(subPosition.getShortageNum());
                waveItemPosition.setMatchedNumCount(subPosition.getMatchedNumCount());
                waveItemPosition.setUnMatchedNum(subPosition.getUnMatchedNum());
                waveItemPosition.setPositionNo(subPosition.getPositionNo());
                waveItemPosition.setPositionCode(WaveUtils.generatePositionCode(staff, waveItemPosition.getPositionNo()));
                waveItemPosition.setUnPickNum(subPosition.getItemNum() - subPosition.getPickedNum() - subPosition.getShortageNum());
                subItemPositionMap.put(key, waveItemPosition);
            }
        }
        List<WaveItemPosition> positions = Lists.newArrayList(subItemPositionMap.values());
        return positions.stream().sorted(Comparator.comparing(WaveItemPosition::getPositionNo)).collect(Collectors.toList());
    }

    private void fillWaveItemsInfo(Staff staff, List<WaveItem> waveItems) {
        if (CollectionUtils.isEmpty(waveItems)) {
            return;
        }

        Set<Long> sysItemIds = Sets.newHashSet();
        Set<Long> pureSysSkuIds = Sets.newHashSet();
        for (WaveItem waveItem : waveItems) {
            sysItemIds.add(waveItem.getSysItemId());
            if (waveItem.getSysSkuId() != null && waveItem.getSysSkuId() > 0L) {
                pureSysSkuIds.add(waveItem.getSysSkuId());
            }
        }

        Map<Long, DmjItem> itemIdMap = Maps.newHashMapWithExpectedSize(sysItemIds.size());
        for (List<Long> subItemIds : Lists.partition(Lists.newArrayList(sysItemIds), 200)) {
            List<DmjItem> dmjItems = itemService.queryItemWithSysItemId(staff, subItemIds);
            itemIdMap.putAll(dmjItems.stream().collect(Collectors.toMap(DmjItem::getSysItemId, java.util.function.Function.identity(), (v1, v2) -> v2)));
        }

        Map<Long, DmjSku> skuIdMap = Maps.newHashMapWithExpectedSize(pureSysSkuIds.size());
        if (!pureSysSkuIds.isEmpty()) {
            for (List<Long> subSkuIds : Lists.partition(Lists.newArrayList(pureSysSkuIds), 200)) {
                List<DmjSku> dmjSkus = itemService.querySkuWithSysSkuId(staff, subSkuIds);
                skuIdMap.putAll(dmjSkus.stream().collect(Collectors.toMap(DmjSku::getSysSkuId, java.util.function.Function.identity(), (v1, v2) -> v2)));
            }
        }
        Map<Long,WaveItem> itemChangeWaveItemMap=new HashMap<>();
        if(WaveItemChangeBusiness.changeItemFlag(staff)){
            itemChangeWaveItemMap=waveItemChangeBusiness.assembleChangeItemWaveItems(staff,waveItems);
        }
        for (WaveItem waveItem : waveItems) {
            DmjItem item = itemIdMap.get(waveItem.getSysItemId());
            DmjSku sku = skuIdMap.get(waveItem.getSysSkuId());
            if (item != null) {
                fillWaveItem(waveItem, item, sku,itemChangeWaveItemMap,staff);
            }
        }

        waveItems.sort((o1, o2) -> {
            if (o1.getOuterId() == null && o2.getOuterId() == null) {
                return 0;
            }
            if (o1.getOuterId() == null) {
                return 1;
            }
            if (o2.getOuterId() == null) {
                return -1;
            }
            return o1.getOuterId().compareTo(o2.getOuterId());
        });
    }

    private void fillWaveItem(WaveItem waveItem, DmjItem item, DmjSku sku, Map<Long, WaveItem> itemChangeWaveItemMap, Staff staff) {
        waveItem.setOuterId(item.getOuterId());
        waveItem.setTitle(item.getTitle());
        waveItem.setShortTitle(item.getShortTitle());
        waveItem.setMainOuterId(item.getOuterId());
        waveItem.setItemRecord(item.getRecord());
        waveItem.setItemRemark(item.getRemark());
        if(CompanyUtils.openMultiShipper(staff)){
            waveItem.setShipperId(item.getShipperId());
            waveItem.setShipperName(item.getShipper());
        }
        WaveItem changeWaveItem = itemChangeWaveItemMap.get(waveItem.getSysSkuId());
        if(changeWaveItem!=null){
            waveItem.setPicPath(changeWaveItem.getPicPath());
        }else {
            waveItem.setPicPath(item.getPicPath());
        }
        if (sku != null) {
            waveItem.setOuterId(sku.getOuterId());
            waveItem.setPropertiesAlias(sku.getPropertiesAlias());
            waveItem.setPropertiesName(sku.getPropertiesName());
            if (StringUtils.isNotEmpty(sku.getSkuPicPath()) && !StockConstants.PATH_NO_PIC.equals(sku.getSkuPicPath())) {
                waveItem.setPicPath(sku.getSkuPicPath());
            }
            waveItem.setMainOuterId(item.getOuterId());
            waveItem.setItemRecord(sku.getRecord());
            waveItem.setItemRemark(sku.getRemark());
            if (changeWaveItem != null) {
                waveItem.setTitle(changeWaveItem.getTitle());
                waveItem.setShortTitle(changeWaveItem.getShortTitle());
            }
        }
    }

    @Override
    public Map<Long, List<Long>> checkCloseWaveTrades(Staff staff, Boolean fix) {
        Map<Long, List<Long>> waveSids = Maps.newHashMap();
        WaveFilterParams params = new WaveFilterParams();
        params.setStatus(String.valueOf(Wave.STATUS_CREATED));
        List<Wave> waves = waveDao.queryPageList(staff, params, new Page(1, 10000));
        for (Wave wave : waves) {
            List<WaveTrade> waveTrades = waveTradeDao.queryWaveTradeByWaveId(staff, wave.getId());
            List<WaveTrade> filters = waveTrades.stream().filter(waveTrade -> waveTrade.getTradeWaveStatus() == WaveTrade.TRADE_WAVE_STATUS_IN).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(filters)) {
                Long[] sids = new Long[filters.size()];
                int i = 0;
                for (WaveTrade filter : filters) {
                    sids[i++] = filter.getSid();
                }
                List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, false, sids);
                if (CollectionUtils.isNotEmpty(trades)) {
                    List<Long> removeSids = Lists.newArrayListWithCapacity(filters.size());
                    for (Trade trade : trades) {
                        if (Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus()) && trade.getWaveId() > 0L) {
                            removeSids.add(trade.getSid());
                        }
                    }

                    if (!removeSids.isEmpty() && BooleanUtils.isTrue(fix)) {
                        logger.debug(LogHelper.buildLog(staff, "订单关闭，移出波次，sid：" + removeSids));
                        removeSids(staff, removeSids.toArray(new Long[0]), WaveOutBusinessEnum.TRADE_CLOSE.getOpName());
                        waveSids.put(wave.getId(), removeSids);
                    }
                }
            }
        }
        return waveSids;
    }

    @Override
    public PageListBase<Wave> queryWavesLog4Wuxian(Staff staff, WaveFilterParams params, Page page) {
        PageListBase<Wave> pageList = new PageList<Wave>();

        long count = waveDao.count(staff, params);
        pageList.setTotal(count);
        pageList.setPage(page);

        if (count > 0) {
            List<Wave> list = waveDao.queryPageList(staff, params, page);
            pageList.setList(list);
        } else {
            pageList.setList(Collections.<Wave>emptyList());
        }
        return pageList;
    }

    @Override
    public List<WaveRule> queryList4Wuxian(Staff staff) {
        return waveRuleDao.queryList4Wuxian(staff);
    }

    @Override
    public Integer queryItemNumByWaveIds(Staff staff, Long warehouseId, List<Long> waveIds) {
        return waveQueryDao.queryItemNumByWaveIds(staff, warehouseId, waveIds);
    }

    @Override
    public List<Long> querySidsByWaveIds(Staff staff, List<Long> waveIds, WaveFilterParams waveFilterParams) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return Collections.emptyList();
        }
        Long[] waveIdArr = waveIds.toArray(new Long[0]);
        List<Wave> waves = waveDao.queryByIds(staff, waveIdArr);
        if (CollectionUtils.isNotEmpty(waves)) {
            List<Long> sids = Lists.newArrayList();
            waves.stream()
                    .collect(Collectors.groupingBy(Wave::getWarehouseId))
                    .forEach((warehouseId, subWaves) -> {
                        sids.addAll(waveQueryDao.queryWaveSids(staff,
                                waveFilterParams.setWarehouseId(warehouseId)
                                        .setWaveIds(subWaves.stream().map(Wave::getId).collect(Collectors.toList()))
                        ));
                    });
            return sids;
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public WaveGoodsChangeResult changeWaveStockRegion(final Staff staff, final Long warehouseId, final List<Long> waveIds, final Long toStockRegionId) {
        final List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
        Assert.notEmpty(waves, "波次不存在或已完成！");
        WavePickUtils.filterNotTradeWaves(waves);
        Assert.notEmpty(waves, "无需要重新分配库区的波次！");

        Assert.isTrue(waveIds.size() == waves.size(), "存在已经完成的波次！");
        for (Wave wave : waves) {
            Assert.isTrue(wave.getDistributionStatus() == Wave.DISTRIBUTION_STATUS_NONE, "仅支持未拣选的波次重新分配库区，请重新选择波次！");
        }

        WaveOperateCacheBusiness.WaveOperateCallback<WaveGoodsChangeResult> callback = new WaveOperateCacheBusiness.WaveOperateCallback<WaveGoodsChangeResult>() {
            @Override
            public WaveGoodsChangeResult callback() {
                List<Long> sids = waveQueryDao.querySidsByWaveIds(staff, warehouseId, waveIds);
                Assert.notEmpty(sids, "波次订单不存在！");
                List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, true, sids.toArray(new Long[0]));
                Assert.notEmpty(trades, "波次订单不存在！");

                Map<Long, List<Long>> waveIdOrderIdsMap = toWaveIdOrderIdsMap(staff,trades);
                ProgressData progressData = waveProgressBusiness.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_WAVE_GOODS_CHANGE);
                progressData.setCountCurrent(10);
                waveProgressBusiness.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_WAVE_GOODS_CHANGE, progressData);
                WaveGoodsChangeResult result = null;
                if (waveIdOrderIdsMap != null && !waveIdOrderIdsMap.isEmpty()) {
                    try {
                        result = wmsService.changeWaveRegion(staff, waveIdOrderIdsMap, toStockRegionId, progressData);
                        logger.debug(LogHelper.buildLog(staff, "波次重新分配库区完成！"));
                        updateWaveSectionAreas(staff, waves);
                    } catch (Exception e) {
                        logger.error(LogHelper.buildErrorLog(staff, e, "波次重新分配库区失败"), e);
                        progressData.setErrorMsg(Lists.newArrayList(e.getMessage()));
                    }
                } else {
                    progressData.setErrorMsg(Lists.newArrayList("无可分配订单！"));
                }

                progressData.setCountCurrent(progressData.getCountAll());
                progressData.setProgress(2);
                progressData.setExecutResult(result);
                waveProgressBusiness.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_WAVE_GOODS_CHANGE, progressData);
                return result;
            }
        };
        return waveOperateCacheBusiness.operateWaves(staff, buildOperateCaches(waveIds, WaveOperateEnum.CHANGE_REGION), callback);
    }

    @Override
    public void updateWaveSectionAreas(Staff staff, List<Wave> waves) {
        final WmsConfig wmsConfig = wmsService.getConfig(staff);
        List<Wave> updates = Lists.newArrayListWithCapacity(waves.size());
        for (Wave wave : waves) {
            List<String> goodsSections = waveQueryDao.queryGoodsSectionsByWaveId(staff, wave.getWarehouseId(), wave.getId());
            Set<String> sectionAreas = Sets.newTreeSet(String::compareTo);
            for (String goodsSection : goodsSections) {
                sectionAreas.add(WmsUtils.getStockRegionCode(wmsConfig, goodsSection));
            }

            String changeSectionAreas = StringUtils.join(sectionAreas, ",");
            if (Objects.equal(wave.getSectionAreas(), changeSectionAreas)) {
                logger.debug(LogHelper.buildLog(staff, String.format("波次%s库区未发生变化，不变更", wave.getId())));
                continue;
            } else {
                logger.debug(LogHelper.buildLog(staff, String.format("波次%s库区由%s更换为%s", wave.getId(), wave.getSectionAreas(), sectionAreas)));
            }

            Wave update = new Wave();
            update.setId(wave.getId());
            update.setSectionAreas(changeSectionAreas);
            updates.add(update);
        }

        if (!updates.isEmpty()) {
            waveDao.batchUpdate(staff, updates);
        }
    }

    @Override
    public void updateWaveSectionAreasByBuffer(Staff staff, List<ErpBuffer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> keys = list.stream().map(ErpBuffer::getKey).collect(Collectors.toList());
        List<Wave> waves = waveDao.queryByIds(staff, keys.stream().map(Long::valueOf).toArray(Long[]::new));
        waves.removeIf(wave -> wave.getStatus() != Wave.STATUS_CREATED);
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }
        updateWaveSectionAreas(staff, waves);
        // 修改拣选库区日志
        waveTraceService.batchAddWaveTrace(staff, WaveTraceUtils.buildBatchWaveTrace(staff, waves.stream().map(Wave::getId).collect(Collectors.toList()), WaveTraceOperateEnum.WAVE_CHANGE_REGION, "重新分配库区"));
    }

    @Override
    public List<Wave> queryPageList(Staff staff, WaveFilterParams params, Page page) {
        return waveDao.queryPageList(staff, params, page);
    }

    @Override
    public List<AssoWaveItem> queryAssoWaveItemsByWaveId(Staff staff, Long waveId) {
        return assoWaveItemDAO.queryAssoWaveItemPage(staff, waveId, null);
    }

    @Override
    public List<AssoWaveItem> queryAssoWaveItemsByWaveIds(Staff staff, List<Long> waveIds) {
        return assoWaveItemDAO.queryAssoWaveItemByWaveIds(staff, waveIds);
    }

    private Map<Long, List<Long>> toWaveIdOrderIdsMap(Staff staff,List<Trade> trades) {
        Map<Long, List<Long>> waveIdOrderIdsMap = Maps.newHashMap();
        for (Trade trade : trades) {
            if (trade.getWaveId() == null || trade.getWaveId() <= 0L) {
                continue;
            }
            if (TradeUtils.isExcep(staff,trade)) {
                continue;
            }
            List<Long> orderIds = waveIdOrderIdsMap.get(trade.getWaveId());
            if (orderIds == null) {
                waveIdOrderIdsMap.put(trade.getWaveId(), getCanChangeOrderIds(trade));
            } else {
                orderIds.addAll(getCanChangeOrderIds(trade));
            }
        }
        return waveIdOrderIdsMap;
    }

    private List<Long> getCanChangeOrderIds(Trade trade) {
        List<Order> orders = OrderUtils.toEffectiveOrders(TradeUtils.getOrders4Trade(trade));
        List<Long> orderIds = Lists.newArrayListWithCapacity(orders.size());
        for (Order order : orders) {
            if (OrderUtils.isVirtualOrNonConsign(order) || OrderUtils.isAfterSendGoods(order)) {
                continue;
            }
            orderIds.add(order.getId());
        }
        return orderIds;
    }

    @Override
    @Transactional
    public void assignWaveOperator(final Staff staff, final List<Long> waveIds, final Long operatorId, final Integer assignType) {
        assignWaveOperator(staff, waveIds, operatorId, assignType, null);
    }

    @Override
    @Transactional
    public void assignWaveOperator(final Staff staff, final List<Long> waveIds, final Long operatorId, final Integer assignType, Map<Long, List<String>> cannotAssignPickReason) {
        Assert.notEmpty(waveIds, "请选择波次！");
        Assert.isTrue(operatorId != null, "请选择一个人员！");
        Assert.notNull(assignType, "请选择分配类型！");

        WaveConfig waveConfig = waveConfigService.get(staff);
        WaveAssignType waveAssignType = WaveAssignType.parseType(assignType);
        lockService.locks(WaveLockBusiness.getBeginWavePickingLock(staff, waveIds), () -> {
            Staff operator;
            if (operatorId == 0L) {
                operator = buildNoneStaff();
            } else {
                operator = staffService.queryById(operatorId);
                Assert.notNull(operator, "该人员不存在！");
            }

            List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
            Assert.notEmpty(waves, "请至少选择一笔可以分配拣选员的波次！");
            handleCannotAssignPickReason(staff, cannotAssignPickReason, waveAssignType, operator, waves);

            Map<Long, WavePicking> pickingMaps = Maps.newHashMap();
            List<WavePicking> wavePickings = wavePickingDao.getByWaveIds(staff, waves.stream().map(Wave::getId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(wavePickings)) {
                pickingMaps = wavePickings.stream().collect(Collectors.toMap(WavePicking::getWaveId, a -> a, (c1, c2) -> c1));
            }

            List<WavePicking> pickingUpdates = Lists.newArrayListWithCapacity(waveIds.size());
            List<Wave> updates = Lists.newArrayListWithCapacity(waveIds.size());
            List<Long> needGiveUps = Lists.newArrayList();
            for (Wave wave : waves) {
                if (Wave.STATUS_CREATED != wave.getStatus()) {
                    continue;
                }
                WavePicking wavePicking = pickingMaps.get(wave.getId());
                switch (waveAssignType) {
                    case PICKER : buildAssignPicker(wave, waveConfig, updates, pickingUpdates, operator, wavePicking, needGiveUps); break;
                    case SORTER : buildAssignSorter(staff, wave, waveConfig, updates, pickingUpdates, operator, wavePicking); break;
                    case ALL : buildAssign(staff, wave, waveConfig, updates, pickingUpdates, operator, wavePicking, needGiveUps); break;
                    default:
                        throw new IllegalArgumentException("不支持的分配类型！");
                }
            }

            if (CollectionUtils.isEmpty(updates)) {
                if (waveIds.size() == 1) {
                    throw new IllegalArgumentException("该波次状态已改变无法领取！");
                } else {
                    throw new IllegalArgumentException("请至少选择一笔可以分配拣选员的波次！");
                }
            }

            giveUpPick(staff, needGiveUps, false);
            // 安排拣选员后，波次变为非分段波次
            updates.forEach(w -> w.setSubSectionPick(0));
            waveDao.batchUpdate(staff, updates);
            if (CollectionUtils.isNotEmpty(pickingUpdates)) {
                wavePickingDao.batchUpdate(staff, pickingUpdates);
                pickingUpdates.forEach(p -> {
                    if (DataUtils.checkLongNotEmpty(p.getPickerId()) && DataUtils.checkLongNotEmpty(p.getWaveId())) {
                        wavePickerDao.insert(staff, p.getWaveId(), p.getPickerId(), p.getPickerName());
                    }
                });
                eventCenter.fireEvent(this, new EventInfo("wave.assign.operator").setArgs(new Object[]{staff, pickingUpdates, WaveAssignType.PICKER}), null);
            }
            return true;
        });
    }

    private void handleCannotAssignPickReason(Staff staff, Map<Long, List<String>> cannotAssignPickReason, WaveAssignType waveAssignType, Staff operator, List<Wave> waves) {
        if (cannotAssignPickReason == null || Objects.equal(waveAssignType, WaveAssignType.SORTER)) {
            return;
        }

        // 不具备权限的分组
        List<Long> noPrivilegeRuleGroups = WaveUtils.getDefaultListIfNull(getWaveRuleGroupPrivilege(operator));
        List<WaveRule> waveRules = waveRuleDao.queryByIds(staff, waves.stream().map(Wave::getRuleId).collect(toList()));

        Map<Long, Long> ruleMap = (CollectionUtils.isEmpty(waveRules) ? Maps.newHashMap() : waveRules.stream().collect(toMap(WaveRule::getId, WaveRule::getWaveRuleGroupId, (a, b) -> a)));

        for (Wave wave : waves) {
            // 非订单波次，使用 未分组 的权限控制
            Long ruleGroupId = (WavePickUtils.isTradeWave(wave) ? ruleMap.get(wave.getRuleId()) : 0L);
            if (ruleGroupId == null) {
                continue;
            }
            if (noPrivilegeRuleGroups.contains(ruleGroupId)) {
                List<String> reasons = cannotAssignPickReason.get(wave.getId());
                if (reasons == null) {
                    cannotAssignPickReason.put(wave.getId(), Lists.newArrayList("波次 ["+wave.getId()+"] 安排拣选员 [" + operator.getName() + "] 失败，无该波次分组权限！"));
                } else {
                    reasons.add("波次 ["+wave.getId()+"] 安排拣选员 [" + operator.getName() + "] 失败，无该波次分组权限！");
                    cannotAssignPickReason.put(wave.getId(), reasons);
                }
                wave.setAssignPickerFailReasons(Lists.newArrayList("波次 ["+wave.getId()+"] 安排拣选员 [" + operator.getName() + "] 失败，无该波次分组权限！"));
            }
        }
    }

    private void addAssignLog(Staff staff, Wave wave, WaveConfig waveConfig, Staff operator) {
        if (logger.isDebugEnabled()) {
            try {
                logger.debug(LogHelper.buildLog(staff, "安排拣选员，waveId：" + wave.getId()
                        + "，distributionStatus：" + wave.getDistributionStatus()
                        + "，pickingArrange：" + waveConfig.getPickingArrange()
                        + "，operator：" + operator.getId())
                        + "，isPickingArrange：" + WaveUtils.isPickingArrange(waveConfig.getPickingArrange(), wave.getDistributionStatus(), operator));
            } catch (Exception e) {

            }

        }
    }

    private void buildAssign(Staff staff, Wave wave, WaveConfig waveConfig, List<Wave> updates,
                             List<WavePicking> pickingUpdates, Staff operator, WavePicking wavePicking, List<Long> needGiveUps) {
        buildAssignPicker(wave, waveConfig, updates, pickingUpdates, operator, wavePicking, needGiveUps);
        buildAssignSorter(staff, wave, waveConfig, updates, pickingUpdates, operator, wavePicking);
    }

    /**
     * 安排波次拣选员，以下两种情况可以安排：
     * 1. 波次未拣选；2. 拣选中，并且开启拣选中的波次安排拣选
     * @param wave
     * @param waveConfig
     * @param updates
     * @param pickingUpdates
     * @param operator
     * @param wavePicking
     */
    private void buildAssignPicker(Wave wave, WaveConfig waveConfig, List<Wave> updates,
                                   List<WavePicking> pickingUpdates, Staff operator, WavePicking wavePicking, List<Long> needGiveUps) {
        if ((Wave.DISTRIBUTION_STATUS_NONE == wave.getDistributionStatus()
                || WaveUtils.isPickingArrange(waveConfig.getPickingArrange(), wave.getDistributionStatus(), operator)) && wave.getAssignPickerFailReasons() == null) {
            updates.add(buildUpdateWaveOperator(wave, operator, null));
            WaveUtils.buildUpdateWavePicking(pickingUpdates, operator, wavePicking);
            boolean pickingArrange = WaveUtils.isPickingArrange(waveConfig.getPickingArrange(), wave.getDistributionStatus(), operator);
            if (pickingArrange && Objects.equal(wave.getSubSectionPick(), CommonConstants.JUDGE_YES)) {
                needGiveUps.add(wave.getId());
            }
        }
    }

    /**
     * 安排波次播种员，以下两种情况可以安排：
     * 1. 未播种状态；2. 播种中，并且开启播种中的波次安排播种
     * @param wave
     * @param updates
     * @param pickingUpdates
     * @param operator
     * @param wavePicking
     */
    private void buildAssignSorter(Staff staff, Wave wave, WaveConfig waveConfig, List<Wave> updates,
                                   List<WavePicking> pickingUpdates, Staff operator, WavePicking wavePicking) {
        if (wave.getDistributionStatus() <= WaveDistributionStatus.PICKED.getValue()
                || WaveDistributionStatus.WAIT_SEED.getValue().equals(wave.getDistributionStatus())
                || WaveUtils.isSeedingArrange(waveConfig.getSeedingArrange())) {
            updates.add(buildUpdateWaveOperator(wave, null, operator));
            buildUpdateWavePicking(pickingUpdates, wavePicking);

            Long assignSorter = wave.getAssignSorter();
            if (wavePicking != null && DataUtils.checkLongNotEmpty(wavePicking.getId())
                    && (!Objects.equal(operator.getId(), assignSorter)
                    || !Objects.equal(operator.getId(), wavePicking.getSorterId()))) {
                tradePostPrintService.cancelSeed(staff, wave.getId(), wavePicking.getId(), false);
            }
        }
    }

    private Staff buildNoneStaff() {
        Staff staff = new Staff();
        staff.setId(0L);
        staff.setName("");
        return staff;
    }

    private void buildUpdateWavePicking(List<WavePicking> updates, WavePicking wavePicking) {
        if (wavePicking != null && DataUtils.checkLongNotEmpty(wavePicking.getSorterId())) {
            WavePicking update = new WavePicking();
            update.setId(wavePicking.getId());
            update.setSorterId(0L);
            update.setSorterName("");
            updates.add(update);
        }
    }

    private Wave buildUpdateWaveOperator(Wave wave, Staff picker, Staff sorter) {
        Wave update = new Wave();
        update.setId(wave.getId());
        if (picker != null) {
            update.setAssignPicker(picker.getId());
            update.setAssignPickerName(picker.getName());
            //需要更新拣选角色组为空
            update.setClearAssignPickerRoles(1);
        }
        if (sorter != null) {
            update.setAssignSorter(sorter.getId());
            update.setAssignSorterName(sorter.getName());
        }
        return update;
    }

    private List<WaveOperateCacheBusiness.WaveOperateCache> buildOperateCaches(List<Long> waveIds, WaveOperateEnum waveOperateEnum) {
        List<WaveOperateCacheBusiness.WaveOperateCache> caches = Lists.newArrayListWithCapacity(waveIds.size());
        for (Long waveId : waveIds) {
            caches.add(new WaveOperateCacheBusiness.WaveOperateCache(waveId, waveOperateEnum));
        }
        return caches;
    }

    /**
     * 根据波次id查询波次 pda 接力 拣选 记录
     */
    @Override
    public List<WavePicker> getWavePickerByWaveId(Staff staff, Long waveId) {
        return wavePickerDao.getByWaveId(staff, waveId);
    }

    @Override
    @Transactional
    public List<Trade> batchSeedFinish(Staff staff, List<Long> waveIds) {
        Assert.notEmpty(waveIds, "请输入波次Ids！");
        List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
        Assert.notEmpty(waves, String.format("波次不存在，无法结束播种，waveIds=%s", Strings.join(",", waveIds)));

        List<Wave> filterWaves = waves.stream().filter(w ->
                !(WaveDistributionStatus.SEEDED.getValue().equals(w.getDistributionStatus())
                        || WaveDistributionStatus.WAIT_EXAMINE.getValue().equals(w.getDistributionStatus())
                        || WaveDistributionStatus.EXAMINED.getValue().equals(w.getDistributionStatus()))
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterWaves)) {
            return null;
        }

        List<Long> filterWaveIds = filterWaves.stream().map(Wave::getId).collect(Collectors.toList());
        List<WavePicking> wavePickings = wavePickingDao.getByWaveIds(staff, filterWaveIds);
        Assert.notEmpty(wavePickings, String.format("拣选信息不存在，无法结束播种，waveIds=%s", Strings.join(",", waveIds)));

        List<WaveSorting> waveSortings = waveSortingDao.queryByPickingIds(staff, wavePickings.stream().map(WavePicking::getId).collect(Collectors.toList()));
        Assert.notEmpty(waveSortings, "拣选信息不存在，无法结束播种");
        List<Long> sids = waveSortings.stream().filter(w -> w.getMatchedStatus() != WaveSorting.MATCHED_STATUS_OVER).map(WaveSorting::getSid).collect(Collectors.toList());
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("结束波次，未完成播种需要提出波次的订单，sids=%s", sids)));
        }

        List<Wave> updates = Lists.newArrayList();
        Map<Long, Wave> id2WaveMap = waves.stream().collect(Collectors.toMap(Wave::getId, java.util.function.Function.identity(), (t1, t2) -> t2));
        wavePickings.forEach(wavePicking -> {
            Wave update = new Wave();
            update.setId(wavePicking.getWaveId());
            update.setDistributionStatus(WaveDistributionStatus.WAIT_EXAMINE.getValue());
            // 结束播种非交易播种波次更改为已完成
            if (WavePickUtils.isNeedSeed4NoTradeWave(id2WaveMap.get(wavePicking.getWaveId()))) {
                update.setStatus(Wave.STATUS_FINISHED);
                update.setFinished(new Date());
                update.setDistributionStatus(WaveDistributionStatus.EXAMINED.getValue());
            }
            update.setSeedEndTime(new Date());
            updates.add(update);
        });
        waveDao.batchUpdate(staff, updates);
        eventCenter.fireEvent(this, new EventInfo("wave.seed.end").setArgs(new Object[]{staff, filterWaveIds, null}), null);

        TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
        if (sids.size() == 0) {
            filterWaves.forEach(w -> updateWaveFinished(staff, w.getId(), null, false, Objects.equal(tradeConfig.getOpenPackageExamine(), CommonConstants.JUDGE_YES)));
            return null;
        }
        return removeSids(staff, sids.toArray(new Long[0]), WaveOutBusinessEnum.FINISH_SEED.getOpName(), WaveTraceOperateEnum.FINISH_WAVE_PDA);
    }

    @Transactional
    @Override
    public List<Trade> seedFinish(Staff staff, Long waveId) {
        return batchSeedFinish(staff, Lists.newArrayList(waveId));
    }

    /**
     * 获取打印 需要的拣选人信息
     */
    @Override
    public Map<String, String> getWavePickers(Staff staff, Wave wave) {
        WavePicking wavePicking = wavePickingDao.getByWaveId(staff, wave.getId());
        List<WavePicker> wavePickers = wavePickerDao.getByWaveId(staff, wave.getId());

        Set<String> pickerNameList = new HashSet<>();
        if (WaveDistributionStatus.WAIT_PICK.getValue().equals(wave.getDistributionStatus())) {
            if (StringUtils.isNotBlank(wave.getAssignPickerName())) {
                pickerNameList.add(wave.getAssignPickerName());
            }
        } else {
            if (CollectionUtils.isNotEmpty(wavePickers)) {
                pickerNameList.addAll(wavePickers.stream().filter(v -> v.getParticipatePick() == 1).map(WavePicker::getPickerName).collect(Collectors.toList()));
            }
            if (wavePicking != null && StringUtils.isNotBlank(wavePicking.getPickerName())) {
                pickerNameList.add(wavePicking.getPickerName());
            }
        }

        String sorterName = null;
        if (wavePicking != null && StringUtils.isNotBlank(wavePicking.getSorterName())) {
            sorterName = wavePicking.getSorterName();
        } else if (StringUtils.isNotBlank(wave.getAssignSorterName())) {
            sorterName = wave.getAssignSorterName();
        }
        String pickerNames = Strings.join(",", pickerNameList);
        Map<String, String> result = new HashMap<>();
        result.put("pickerNames", StringUtils.isNotBlank(pickerNames) ? pickerNames : "");
        result.put("sorterName", StringUtils.isNotBlank(sorterName) ? sorterName : "");
        return result;
    }

    @Override
    public Wave countWavePickers(Staff staff, Long waveId) {
        Wave wave = waveDao.queryById(staff, waveId);
        List<WavePicker> wavePickers = Lists.newArrayList();
        // assignPicker = 0，清空拣选员
        if (Wave.DISTRIBUTION_STATUS_NONE == wave.getDistributionStatus() && wave.getAssignPicker() != null) {
            WavePicker assignPicker = new WavePicker();
            assignPicker.setPickerId(wave.getAssignPicker());
            assignPicker.setPickerName(wave.getAssignPickerName());
            wavePickers.add(assignPicker);
        } else {
            List<WavePicker> participatePickers = wavePickerDao.getByWaveIds(staff, Lists.newArrayList(wave.getId()), 1);
            wavePickers.addAll(participatePickers);
            // 正在拣选的拣选员
            WavePicking wavePicking = wavePickingDao.getByWaveId(staff, wave.getId());
            if (wavePicking != null && DataUtils.checkLongNotEmpty(wavePicking.getPickerId()) && StringUtils.isNotEmpty(wavePicking.getPickerName())) {
                WavePicker pickingPicker = new WavePicker();
                pickingPicker.setPickerId(wavePicking.getPickerId());
                pickingPicker.setPickerName(wavePicking.getPickerName());
                wavePickers.add(pickingPicker);
            }
        }
        if (CollectionUtils.isNotEmpty(wavePickers)) {
            List<Long> pickerIds = wavePickers.stream().map(WavePicker::getPickerId).distinct().collect(Collectors.toList());
            List<String> pickerNames = wavePickers.stream().map(WavePicker::getPickerName).distinct().collect(Collectors.toList());
            wave.setPickerNames(Strings.join(",", pickerNames));
            wave.setPickerIds(Strings.join(",", pickerIds));
        }
        return wave;
    }


    /**
     * status: 1,3,4 未完成/已完成/已取消
     * 此页面要将三者合并到一起,并且将未完成拆分为distribution_status
     * distribution_status:0,1,6,3,5,7 未拣选/拣选中/等待播种/播种中/等待验货/验货中 && status:1
     * 如果页面选了"已完成/已取消"(status=3,4) + 未完成中的任一状态,
     * 要将((status=1 and distributionStatus in(0,1,6,3,5,7)) or status in (3,4))
     */
    @Override
    public PageListBase<Wave> queryAllWaves(Staff staff, WaveFilterParams params, Page page) {
        PageListBase<Wave> pageList = new PageList<Wave>();
        //status distribution_status都有
        if (StringUtils.isNotEmpty(params.getStatus()) && StringUtils.isNotEmpty(params.getDistributionStatus())) {
            params.setStatusAndDistributionStatus(true);
        }
        //只有未完成  status=1 && distribution_status in ()
        if (StringUtils.isEmpty(params.getStatus()) && StringUtils.isNotEmpty(params.getDistributionStatus())) {
            params.setStatus(String.valueOf(Wave.STATUS_CREATED));
        }
        if (params.getRuleGroupId() != null || StringUtils.isNotEmpty(params.getRuleGroupIds())) {
            Boolean isHandle = handleRuleGroupIds(staff, params, page, pageList);
            if (!isHandle) {
                pageList.setTotal(0L);
                pageList.setPage(page);
                pageList.setList(new ArrayList<Wave>());
                return pageList;
            }
        }
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (waveConfig != null && waveConfig.getInteger(WaveChatConfigsEnum.ONE_WAVE_MULTI_PICKING_CODE.getKey()) != 0) {
            params.setQueryMultiPickingCode(1);
        }
        params.setExpressMap(dealExpressParam(staff, params.getExpress()));
        return getWavesByParams(staff, params, page);
    }

    private PageListBase<Wave> getWavesByParams(Staff staff, WaveFilterParams params, Page page) {
        PageListBase<Wave> pageList = new PageList<Wave>();
        params.setNotInWaveRuleGroupIds(getWaveRuleGroupPrivilege(staff));
        params.setOpenPrintStatusV2(waveHelpBusiness.openPrintStatusV2(staff));
        params.setAccurateStatWaveTradeNum(waveHelpBusiness.isAccurateStatWaveTradeNum(staff));
        fillParamsSysAItemSkuIds(staff, params);

        pageList.setPage(page);
        //填充根据系统单号/内部单号/平台单号/快递单号查询到的waveId集合
        if (StringUtils.isNotEmpty(params.getQuerySids()) || StringUtils.isNotEmpty(params.getQueryOutSids())
                || StringUtils.isNotEmpty(params.getQueryTids()) || StringUtils.isNotEmpty(params.getQueryShortIds()) || StringUtils.isNotEmpty(params.getQueryKey())){
            List<Long> waveIdLIst = queryTradeByMixKey(staff, params);
            if (CollectionUtils.isEmpty(waveIdLIst)){
                pageList.setTotal(0L);
                pageList.setList(new ArrayList<Wave>());
                return pageList;
            }
            params.setWaveIds(waveIdLIst);
        }
        //设置货主
        //货主查询条件转换
        if(StringUtils.isNotEmpty(params.getSpecifyShipperIds())){
            params.setShipperIds(Strings.getAsStringList(params.getSpecifyShipperIds(),",",true));
        }
        //设置拣货角色组
        if(StringUtils.isNotEmpty(params.getPickerRoleId())){
            params.setAssignPickerRoleIds(Strings.getAsLongList(params.getPickerRoleId(),",",true));
        }
        long count = waveDao.count(staff, params);
        pageList.setTotal(count);

        if (count > page.getStartRow()) {
            TradeConfig tradeConfig = tradeServiceDubbo.queryTradeConfig(staff);
            WaveConfig waveConfig = waveConfigService.get(staff);
            params.setWaveSortRule(waveConfig.getWaveSortRule());
            params.setWaveSortTime(waveConfig.getWaveSortTime());
            List<Wave> list = waveDao.queryPageList(staff, params, page);
            if (CollectionUtils.isNotEmpty(list)) {
                filterWithNum(staff, list.iterator(), params, tradeConfig);
                Boolean isShowPrintStatus = StringUtils.isNotBlank(params.getPrintStatus()) || BooleanUtils.isTrue(params.getIsFillInfo());
                fillWaveInfo(staff, list, isShowPrintStatus, true);
                fillWaveOutSidStatus(staff, list,params.getPageId());
            }
            checkWaveStatistics(staff, list, params);
            fillPrintedTradeNum(staff, list, WAVE_MANAGE_COLUMN_PAGE_ID);
            fillMultiPickingCode(staff, list, params);

            //判断波次标签是否含有组团标签，并设置为组团波次类型
            list.stream()
                    .filter(wave -> WaveUtils.containsWaveTag(wave.getTagIds(), WaveTagEnum.SAME_ITEM.getId()))
                    .forEach(wave -> wave.setPickingType(PickingType.GROUP.getValue()));

            fillWaveSendPrintStatus(staff, list);
            waveHelpBusiness.assembleShipperInfo(list,staff);
            //处理拣货人员，如果是角色，需要显示特殊格式
            fillWavePickerRole(staff,list);
            pageList.setList(list);
        } else {
            pageList.setList(new ArrayList<Wave>());
        }
        return pageList;
    }

    private void fillWavePickerRole(Staff staff,List<Wave>waves){
        if(CollectionUtils.isNotEmpty(waves)){
            List<Wave> pickerRoleWaves = waves.stream().filter(wave -> StringUtils.isNotEmpty(wave.getAssignPickerRoles())
                    && (wave.getAssignPicker() == null || wave.getAssignPicker() == 0)
                    &&WaveDistributionStatus.WAIT_PICK.getValue().equals(wave.getDistributionStatus())).collect(toList());
            if(CollectionUtils.isNotEmpty(pickerRoleWaves)){
                Map<Long, String> pickerRoleName = waveHelpBusiness.getPickerRoleName(staff, true);
                pickerRoleWaves.forEach(wave -> {
                    List<Long> roleIds = getAsLongList(wave.getAssignPickerRoles(), ",", true);
                    List<String>roleNames=new ArrayList<>();
                    for (Long roleId : roleIds) {
                        roleNames.add(pickerRoleName.get(roleId));
                    }
                    wave.setAssignPickerName(roleNames.stream().filter(StringUtils::isNotEmpty).collect(joining(",")));
                    wave.setPickerName(roleNames.stream().filter(StringUtils::isNotEmpty).collect(joining(",")));
                });
            }
        }
    }


    /**
     * 填充波次送打状态
     * @param staff 员工
     * @param list 波次列表
     */
    private void fillWaveSendPrintStatus(Staff staff, List<Wave> list){
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> waveIds = list.stream().map(Wave::getId).collect(Collectors.toList());
        //查询送打状态 能查询到的都是已经送打的  waveTradePrintingStatusDAO
        List<WaveTradePrintingStatus> waveTradePrintingStatusList = waveTradePrintingStatusDAO.queryAllByWaveIds(staff, waveIds);
        //waveTradePrintingStatusList 转换成以 waveId 为key的map
        Map<Long, WaveTradePrintingStatus> waveTradePrintingStatusMap = waveTradePrintingStatusList.stream()
                .collect(Collectors.toMap(WaveTradePrintingStatus::getWaveId, o->o));
        for (Wave wave : list) {
            if (waveTradePrintingStatusMap.get(wave.getId()) !=null){
                wave.setWaveSendPrintStatus(1);
            }
        }
    }



    /**
     * 填充波次获取单号状态
     * @param staff
     * @param list
     */
    private void fillWaveOutSidStatus(Staff staff, List<Wave> list,Long pageId) {
        //判断当前页面是否是波次管理页面或者波次日志页面
        if (pageId == WAVE_MANAGE_COLUMN_PAGE_ID || pageId == WAVE_LIST_COLUMN_PAGE_ID){
            //获取列配置列表
            ColumnConfListWrapper columnConfigList = waveColumnConfService.getColumnConfigList(staff, pageId);
            if (columnConfigList == null || CollectionUtils.isEmpty(columnConfigList.getColumnConfList())) {
                return;
            }
            if (columnConfigList.getColumnConfList().stream().anyMatch(c -> Objects.equal(OUT_SID_COLUMN_STR, c.getField()) && Objects.equal(CommonConstants.ENABLE_STATUS_NORMARL, c.getVisible()))) {
                //获取未取消的波次id或波次类型小于4
                List<Long> waveIds = list.stream().filter(w -> w.getStatus() != Wave.STATUS_DELETED || w.getPickingType() < PickingType.PURCHASE_RETURN.getValue()).map(Wave::getId).collect(toList());
                //获取已取消的波次id或波次类型大于4
                List<Long> deletedWaveIds = list.stream().filter(w -> w.getStatus() == Wave.STATUS_DELETED || w.getPickingType() >= PickingType.PURCHASE_RETURN.getValue()).map(Wave::getId).collect(toList());
                //查询波次订单信息
                List<Wave> waveList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(waveIds)){
                    waveList = waveTradeDao.queryWaveOutSidStatus(staff, waveIds);
                }
                Map<Long, List<Wave>> waveMap = waveList.stream().collect(groupingBy(Wave::getId));
                for (Wave wave : list){
                    if (deletedWaveIds.contains(wave.getId())){
                        wave.setOutsidStatus(3);
                        continue;
                    }
                    //获取对应的相关信息
                    List<Wave> waves = waveMap.get(wave.getId());

                    if (CollectionUtils.isEmpty(waves)){
                        wave.setOutsidStatus(0);
                        continue;
                    }

                    //获取波次踢出订单数量，未踢出已获取快递单号数量、未踢出未获取快递单号数量信息
                    Wave resultWave = waves.get(0);

                    //如果订单提出数等于订单数，且未踢出已获取快递单号数量 == 0 and 未踢出未获取快递单号数量 == 0，则说明无需获取快递单号
                    if (resultWave.getRemoveTradeCount().equals(resultWave.getTradesCount()) && resultWave.getUnOutIdTradeCount().equals(0) && resultWave.getHasOutIdTradeCount().equals(0)){
                        wave.setOutsidStatus(3);
                        continue;
                    }

                    //如果未获取得到快递单号的数据条数等于全部订单数，则说明全部未获取单号
                    if (resultWave.getUnOutIdTradeCount().equals(resultWave.getTradesCount() - resultWave.getRemoveTradeCount()) && resultWave.getHasOutIdTradeCount().equals(0)){
                        wave.setOutsidStatus(0);
                        continue;
                    }

                    //如果获取得到的数据条数等于全部订单数，则说明全部获取单号
                    if (resultWave.getHasOutIdTradeCount().equals(resultWave.getTradesCount() - resultWave.getRemoveTradeCount())){
                        wave.setOutsidStatus(2);
                        continue;
                    }

                    //部分获取
                    wave.setOutsidStatus(1);
                }
            }
        }
    }

    /**
     * 填充SysItemIds、SysSkuIds
     */
    private void fillParamsSysAItemSkuIds(Staff staff, WaveFilterParams params) {
        if (StringUtils.isEmpty(params.getOuterId())) {
            return;
        }
        // 包含任意+模糊搜索 商家编码
        if (Objects.equal(params.getOuterIdQueryType(), 0) && Objects.equal(params.getOuterIdsVagueType(), 1)) {
            ItemQueryParams queryParams = new ItemQueryParams();
            queryParams.setTileOuterId(params.getOuterId());
            queryParams.setIsAccurate(0);
            DmjItems items = itemService.search(staff, queryParams);
            List<DmjItem> dmjItems = filterItemAndSkuByOuterId(items, params.getOuterId());
            if (CollectionUtils.isEmpty(dmjItems)) {
                params.setSysItemIds(Sets.newHashSet(0L));
                params.setSysSkuIds(Sets.newHashSet(0L));
                params.setItemKeyList(Sets.newHashSet(new ItemKey(0L, 0L, "")));
            } else {
                Set<Long> sysItemIds = Sets.newHashSet();
                Set<Long> sysSkuIds = Sets.newHashSet();
                Set<ItemKey> itemKeyList = Sets.newHashSet();
                Set<String> outerIds = Sets.newHashSet();
                for (DmjItem item : dmjItems) {
                    sysItemIds.add(item.getSysItemId());
                    // 保持和非模糊查询逻辑一致 纯商品的itemKey中sysSkuId -1 sysSkuIds中额外add(0L)
                    Long sysSkuId = item instanceof DmjSku ? ((DmjSku) item).getSysSkuId() : -1L;
                    if (Objects.equal(sysSkuId, -1L)) {
                        sysSkuIds.add(0L);
                    }
                    sysSkuIds.add(sysSkuId);
                    String outerId = item instanceof DmjSku ? ((DmjSku) item).getSkuOuterId() : item.getOuterId();
                    outerIds.add(outerId);
                    itemKeyList.add(new ItemKey(item.getSysItemId(), sysSkuId, outerId));
                }
                logger.debug(String.format("%s模糊查询到的商家编码:%s", params.getOuterId(), outerIds));
                params.setSysItemIds(sysItemIds);
                params.setSysSkuIds(sysSkuIds);
                params.setItemKeyList(itemKeyList);
                // 包含任意商家编码,会先查出waveId根据波次Id查询波次列表
                params.setWaveIdsByItemSearch(waveDao.queryWaveIdByItemIds(staff, params));
            }
            return;
        }

        Integer flag = params.getOuterIdType() == null ? 12 : (Objects.equal(params.getOuterIdType(), CommonConstants.JUDGE_YES) ? 1 : 2);
        Map<String, Object> outerIdItemMap = itemService.queryItemSkuByOuterId(staff, Lists.newArrayList(Splitter.on(OUTER_ID_SPLIT_PATTERN).trimResults().omitEmptyStrings().split(params.getOuterId())), flag, null);
        if (org.apache.commons.collections.MapUtils.isEmpty(outerIdItemMap)) {
            params.setSysItemIds(Sets.newHashSet(0L));
            params.setSysSkuIds(Sets.newHashSet(0L));
            params.setItemKeyList(Sets.newHashSet(new ItemKey(0L, 0L, "")));
            return;
        }
        Set<Long> sysItemIds = Sets.newHashSet();
        Set<Long> sysSkuIds = Sets.newHashSet();
        Set<ItemKey> itemKeyList = Sets.newHashSet();
        for (Map.Entry<String, Object> outerIdEntrySet : outerIdItemMap.entrySet()) {
            DmjItem dmjItem = (DmjItem) outerIdEntrySet.getValue();
            Long sysItemId = dmjItem.getSysItemId();
            Long sysSkuId = -1L;
            ItemKey itemKey = new ItemKey(sysItemId, sysSkuId, outerIdEntrySet.getKey());
            sysItemIds.add(sysItemId);
            if (dmjItem instanceof DmjSku) {
                DmjSku sku = (DmjSku) dmjItem;
                sysSkuId = sku.getSysSkuId();
            }
            sysSkuIds.add(sysSkuId);
            itemKey.setSysSkuId(sysSkuId);
            if (Objects.equal(sysSkuId, -1L)) {
                sysSkuIds.add(0L);
            }
            itemKeyList.add(itemKey);
        }
        params.setSysItemIds(sysItemIds);
        params.setSysSkuIds(sysSkuIds);
        params.setItemKeyList(itemKeyList);
        params.setWaveIdsByItemSearch(waveDao.queryWaveIdByItemIds(staff, params));
    }

    /**
     * 从DmjItems中获取商家编码包含{originOuterId}的商品
     * @param items
     * @param originOuterId
     * @return
     */
    private List<DmjItem> filterItemAndSkuByOuterId(DmjItems items, String originOuterId) {
        if (items == null || CollectionUtils.isEmpty(items.getList()) || StringUtils.isEmpty(originOuterId)) {
            return new ArrayList<>();
        }
        List<DmjItem>itemList = Lists.newArrayList();
        for (DmjItem dmjItem : items.getList()) {
            itemList.add(dmjItem);
            if (CollectionUtils.isNotEmpty(dmjItem.getSkus())) {
                itemList.addAll(dmjItem.getSkus());
            }
        }
        itemList = itemList.stream().filter(i -> {
            String outerId = i instanceof DmjSku ? ((DmjSku) i).getSkuOuterId() : i.getOuterId();
            return StringUtils.isNotEmpty(outerId) && outerId.contains(originOuterId);
        }).collect(toList());
        return itemList;
    }

    /**
     * 波次用 异步 获取单号 都用审核后获取单号的事件
     */
    @Override
    public void getWaybillCode(Staff staff, List<Long> waveIds, WaybillGetOperationEnum operationEnum) {
        waveHelpBusiness.getWaybillCode(staff, waveIds, operationEnum, false);
    }

    @Override
    public void getWaybillCode(Staff staff, List<Long> waveIds, WaybillGetOperationEnum operationEnum, ProgressEnum progress) {
        waveHelpBusiness.getWaybillCode(staff, waveIds, operationEnum, false, progress);
    }

    void fillWaveInfo(Staff staff, List<Wave> list, Boolean isShowPrintStatus, Boolean isFillFine) {
        fillTemplateName(staff, list);
        fillPickers(staff, list);
        fillWaveRuleGroupName(staff, list);
        fillPrintStatus(staff, isShowPrintStatus, list);
        fillShopInfo(staff, list);
        if (BooleanUtils.isTrue(isFillFine)) {
            filterUnBeginWavePicker(staff, list);
            sortSectionArea(staff, list);
        }
        fillExpressInfo(staff,list);
        fillExpressCompanyName(list);
        fillWaveLogisticsCompanyName(staff, list);
    }

    /**
     * 填充快递信息
     */
    private void fillExpressInfo(Staff staff, List<Wave> waves){
        List<Long> templateIds = waves.stream().filter(wave->wave.getExpressTemplateId()!=null&&wave.getExpressTemplateId()!=0&&wave.getExpressTemplateId()!=-1).map(Wave::getExpressTemplateId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(templateIds)) {
            List<UserWlbExpressTemplate> userWlbExpressTemplates = userWlbExpressTemplateService.getUserExpressTemplateIdNameExpressNeme(staff, templateIds.toArray(new Long[templateIds.size()]));
            Map<Long, UserWlbExpressTemplate> expressTemplateMap = userWlbExpressTemplates.stream().collect(Collectors.toMap(UserWlbExpressTemplate::getId, java.util.function.Function.identity(), (k1, k2) -> k2));
            for (Wave wave : waves) {
                UserWlbExpressTemplate userWlbExpressTemplate = expressTemplateMap.get(wave.getExpressTemplateId());
                if(userWlbExpressTemplate!=null) {
                    //快递公司信息
                    wave.setPublicExpressName(userWlbExpressTemplate.getExpressName());
                    wave.setPublicExpressId(userWlbExpressTemplate.getExpressId());
                }
            }
        }
    }

    private void fillExpressCompanyName(List<Wave> waves) {
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }
        List<ExpressCompany> expressCompanies = expressCompanyService.getExpressCompanys();
        if (CollectionUtils.isEmpty(expressCompanies)) {
            return;
        }
        Map<Long, ExpressCompany> expressCompanyMap = expressCompanies.stream().collect(Collectors.toMap(ExpressCompany::getId, e -> e, (k1, k2) -> k1));
        for (Wave wave : waves) {
            ExpressCompany expressCompany = expressCompanyMap.get(wave.getExpressCompanyId());
            if (expressCompany != null) {
                wave.setExpressName(expressCompany.getName());
            }
        }
    }

    /**
     * 填充店铺信息
     */
    private void fillShopInfo(Staff staff, List<Wave> waves) {
        Set<Wave> filterWaves = waves.stream().filter(wave -> wave.getShopUserId() != null).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(filterWaves)) {
            return;
        }
        Map<Long, Shop> userId2ShopMap = shopService.queryByUserIds(staff, filterWaves.stream().map(Wave::getShopUserId).toArray(Long[]::new))
                .stream().collect(Collectors.toMap(Shop::getUserId, java.util.function.Function.identity(), (v1, v2) -> v2));
        for (Wave wave : filterWaves) {
            Shop shop = Optional.ofNullable(userId2ShopMap.get(wave.getShopUserId())).orElse(new Shop());
            wave.setShopFlag(shop.getFlag());
            wave.setShopName(WaveUtils.getShopName(shop));
            wave.setShopSource(shop.getSource());
        }
    }

    @Override
    public List<Long> queryNoOutSidWave(Staff staff, List<Long> waveIds) {
        return waveQueryDao.queryNoOutSidWave(staff, waveIds);
    }

    @Override
    public List<Long> queryMergeSidBySid(Staff staff, List<Long> sids) {
        return waveQueryDao.queryMergeSidBySid(staff, sids);
    }

    @Override
    public List<TbTrade> queryMergeTradesByParentTrades(Staff staff, List<Trade> list) {
        return tbTradeDao.queryByKeys(staff, "sid,merge_sid,seller_memo,seller_flag,buyer_message,ac_payment", "merge_sid", TradeUtils.toSids(list));
    }

    @Override
    public Map<Pair<Long, Long>, List<String>> queryMultiCodeByItemInfo(Staff staff, List<Pair<Long, Long>> pairs) {
        if (CollectionUtils.isEmpty(pairs) || !tradePostPrintService.checkHasFeature(staff, Feature.ONE_ITEM_MULTI_CODE)) {
            return Maps.newHashMap();
        }
        QueryMultiCodeByItemInfoRequest request = new QueryMultiCodeByItemInfoRequest();
        request.setStaffRequest(buildStaffRequest(staff));
        request.setItemInfoList(pairs.stream().map(pair -> {
            QueryMultiCodeByItemInfoRequest.ItemInfo itemInfo = new QueryMultiCodeByItemInfoRequest.ItemInfo();
            itemInfo.setSysItemId(pair.getLeft());
            itemInfo.setSysSkuId(pair.getRight());
            return itemInfo;
        }).collect(Collectors.toList()));

        QueryMultiCodeByItemInfoResponse response = dmjItemCommonSearchApi.queryMultiCodeByItemInfoV2(request);
        Assert.isTrue(response.isSuccess(), "查询一品多码发生异常,code:" + response.getErrorCode() + ",错误信息:" + response.getErrorMsg());
        List<QueryMultiCodeByItemInfoResponse.ItemMultiCodeInfo> list = response.getList();
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(codeInfo -> Pair.of(codeInfo.getSysItemId(), codeInfo.getSysSkuId()),
                codeInfo -> CollectionUtils.isEmpty(codeInfo.getMultiCodeList()) ? new ArrayList<>() : codeInfo.getMultiCodeList(), (v1, v2) -> v1));
    }

    @Override
    public Map<String, String> queryOuterIdByMultiCodes(Staff staff, List<String> multiCodes) {
        if (!tradePostPrintService.checkHasFeature(staff, Feature.ONE_ITEM_MULTI_CODE) || openNoCheckMultiCodeUniqueness(staff)) {
            return Maps.newHashMap();
        }

        // 根据一品多码查询商品信息
        DmjItemSearchField dmjItemSearchField = DmjItemSearchField.DmjItemSearchFieldBuilder.builder().build();
        QueryDmjItemByMultiCodeRequest request = new QueryDmjItemByMultiCodeRequest();
        request.setStaffRequest(buildStaffRequest(staff));
        request.setCodeList(multiCodes);
        request.setDmjItemSearchField(dmjItemSearchField);
        QueryDmjItemByMultiCodeResponse response = dmjItemCommonSearchApi.queryDmjItemByMultiCode(request);
        Assert.isTrue(response.isSuccess(), "查询一品多码发生异常，code:" + response.getErrorCode() + "，错误信息:" + response.getErrorMsg());
        if (CollectionUtils.isEmpty(response.getList())) {
            return Maps.newHashMap();
        }
        return response.getList().stream().collect(Collectors.toMap(m -> StringUtils.defaultString(m.getMultiCode()).toLowerCase(), DmjItemDto::getSkuOuterId, (a, b) -> a));
    }

    public StaffRequest buildStaffRequest(Staff staff) {
        return StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build();
    }

    @Override
    public List<WaveItemPosition> queryWaveItemPositionWithNum(Staff staff, List<Long> waveIds, List<Long> sids, List<WaveItem> waveItems, Integer stockStatus, Integer suitType, List<String> uniqueCodes) {
        return waveCodePrintBusiness.queryWaveItemPositionWithNum(staff, waveIds, sids, waveItems, stockStatus, suitType, uniqueCodes);
    }

    @Override
    public WavePageList<WaveItem> queryWavePrintItemPageList(Staff staff, QueryWaveItemPageParams params) {
        // 这个接口目前是查询在用，需要合并结果
        if (params.isFromQuery() == null) {
            params.setFromQuery(true);
        }
        return waveCodePrintBusiness.queryWavePrintItemPageList(staff, params);
    }

    @Override
    public boolean switchPickPerformanceValidate(Staff staff) {
        // 查询拣选完成，但是未完结的波次
        WaveFilterParams params = new WaveFilterParams();
        params.setDistributionStatus(WaveDistributionStatus.getPickedAfterStatus());
        params.setStatus(String.valueOf(Wave.STATUS_CREATED));

        Long count = waveDao.count(staff, params);
        return count == null || count <= 0;
    }

    @Override
    public Map<Long, Long> queryWaveIdAndShortId(Staff staff, List<Long> shortIds, List<Long> waveIds) {
        if (CollectionUtils.isEmpty(shortIds)
                && CollectionUtils.isEmpty(waveIds)) {
            return Maps.newHashMap();
        }
        List<Long> filterShortIds = CollectionUtils.isNotEmpty(shortIds)
                ? shortIds.stream().filter(DataUtils::checkLongNotEmpty).collect(toList()) : shortIds;
        List<Long> filterWaveIds = CollectionUtils.isNotEmpty(waveIds)
                ? waveIds.stream().filter(DataUtils::checkLongNotEmpty).collect(toList()) : waveIds;
        return waveDao.queryWaveIdAndShortId(staff, filterShortIds, filterWaveIds);
    }

    @Override
    public List<WaveItem> queryWavesItems(Staff staff, List<Long> waveIds) {
        return waveQueryDao.queryWavesItems(staff, waveIds);
    }

    @Override
    public List<WaveItem> queryWavesUnCombineItems(Staff staff, List<Long> waveIds) {
        return waveQueryDao.queryWavesUnCombineItems(staff, waveIds);
    }

    @Override
    public List<WaveItemGroupVO> queryWavesItemsGroup(Staff staff, String waveIds) {
        List<WaveItemGroupVO> waveItemGroupVOS = Lists.newArrayList();
        if (StringUtils.isEmpty(waveIds)) {
            return waveItemGroupVOS;
        }
        List<Long> waveIdList = ArrayUtils.toLongList(waveIds);

        TradeStaffConfig tradeStaffConfig = tradeServiceDubbo.queryTradeStaffConfig(staff);
        String waveListItemShowConf = tradeStaffConfig.getWaveListItemShowConf();
        if (StringUtils.isEmpty(waveListItemShowConf)) {
            return waveItemGroupVOS;
        }
        List<Long> emptyIdList = getNewWaveItemGroupVOS(staff, waveItemGroupVOS, waveIdList, waveListItemShowConf);
        if (CollectionUtils.isNotEmpty(emptyIdList)) {
            return ArrayUtils.saveAddAll(waveItemGroupVOS, getOldWaveItemGroupVOS(staff, emptyIdList, waveListItemShowConf));
        }
        return waveItemGroupVOS;
    }

    private List<Long> getNewWaveItemGroupVOS(Staff staff, List<WaveItemGroupVO> waveItemGroupVOS, List<Long> waveIdList, String waveListItemShowConf) {
        //判断是否显示套件商家编码
        boolean suitShow = WaveItemInfo.ItemShowConf.isShowSuit(waveListItemShowConf);
        List<Wave> waves = waveQueryDao.queryWaveItemInfoByWaveIds(staff, waveIdList).stream()
                .filter(WavePickUtils::isTradeWave).collect(toList());
        List<Long> emptyIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(waves)) {
            return emptyIdList;
        }
        for (Wave wave : waves) {
            String itemInfo = wave.getItemInfo();
            if (StringUtils.isEmpty(itemInfo)) {
                emptyIdList.add(wave.getId());
                continue;
            }
            List<WaveItemInfo> waveItemInfoList = JSON.parseArray(itemInfo, WaveItemInfo.class);
            waveItemInfoList.removeIf(data -> Objects.equal(data.getSuitType(), suitShow ? WaveItemInfo.SuitType.SUIT_DETAIL.getType() : WaveItemInfo.SuitType.SUIT.getType()));
            // 非套件需要合并套件明细和单品的数量
            if (!suitShow) {
                Map<String, WaveItemInfo> map = Maps.newHashMap();
                for (WaveItemInfo waveItemInfo : waveItemInfoList) {
                    map.merge(WmsKeyUtils.buildItemKey(waveItemInfo.getSysItemId(), waveItemInfo.getSysSkuId()), waveItemInfo, (old, value) -> {
                        old.setNum(old.getNum() + value.getNum());
                        return old;
                    });
                }
                waveItemInfoList = Lists.newArrayList(map.values());
            }
            waveItemInfoList = waveItemInfoList.subList(0, Math.min(ITEM_SHOW_NUM, waveItemInfoList.size()));
            waveItemGroupVOS.add(new WaveItemGroupVO().setWaveId(wave.getId()).setWaveItems(waveItemInfoList));
        }
        fillWaveItemInfo(staff, waveItemGroupVOS);
        return emptyIdList;
    }

    private void fillWaveItemInfo(Staff staff, List<WaveItemGroupVO> waveItemGroupVOS) {
        if (CollectionUtils.isEmpty(waveItemGroupVOS)) {
            return;
        }
        Map<Pair<Long, Long>, List<WaveItemInfo>> pairListMap = waveItemGroupVOS.stream()
                .flatMap(data -> Optional.ofNullable(data.getWaveItems()).orElse(Lists.newArrayList()).stream())
                .collect(Collectors.groupingBy(data -> Pair.of(data.getSysItemId(), data.getSysSkuId())));

        List<Long> sysItemIds = pairListMap.keySet().stream().map(Pair::getKey).distinct().collect(toList());
        List<Long> sysSyuIds = pairListMap.keySet().stream().map(Pair::getValue).distinct().collect(toList());
        Map<String, DmjItem> dmjItemMap = waveHelpBusiness.queryItemMap(staff, sysItemIds, sysSyuIds);
        Map<Long, WaveItem> waveItemMap=new HashMap<>();
        if(WaveItemChangeBusiness.changeItemFlag(staff)){
            List<WaveItem> waveItems = waveQueryDao.queryChangeItemSku(staff, sysSyuIds);
            waveItemMap = waveItems.stream().collect(toMap(WaveItem::getSysSkuId, java.util.function.Function.identity(), (v1, v2) -> v1));
        }
        for (Map.Entry<Pair<Long, Long>, List<WaveItemInfo>> pairListEntry : pairListMap.entrySet()) {
            Pair<Long, Long> pair = pairListEntry.getKey();
            List<WaveItemInfo> itemList = pairListEntry.getValue();
            if (pair == null || CollectionUtils.isEmpty(itemList)) {
                continue;
            }
            DmjItem dmjItem = dmjItemMap.get(WmsKeyUtils.buildItemKey(pair.getLeft(), pair.getRight()));
            if (dmjItem == null) {
                continue;
            }
            for (WaveItemInfo waveItemInfo : itemList) {
                WaveItem waveItem = waveItemMap.get(pair.getRight());
                if(waveItem!=null){
                    waveItemInfo.setPicPath(waveItem.getPicPath());
                }else {
                    waveItemInfo.setPicPath(dmjItem.getPicPath());
                }
                waveItemInfo.setShortTitle(dmjItem.getShortTitle());
                if (dmjItem instanceof DmjSku) {
                    DmjSku sku = (DmjSku) dmjItem;
                    waveItemInfo.setPropertiesName(sku.getPropertiesName());
                    //判断是否有系统规格ID，如果有则说明存在规格商家，则商品简称字段显示为规格商家编码的简称
                    waveItemInfo.setItemShortTitle(sku.getShortTitle());
                    String skuPicPath = sku.getSkuPicPath();
                    if (StringUtils.isNotEmpty(skuPicPath) && !skuPicPath.contains(StockConstants.PATH_NO_PIC)) {
                        waveItemInfo.setPicPath(skuPicPath);
                    }
                    if(waveItem!=null){
                        waveItemInfo.setTitle(waveItem.getTitle());
                    }
                }
                // 只展示套件简称
                if (!dmjItem.isSuite()) {
                    waveItemInfo.setShortTitle(null);
                }
            }
        }
    }

    /**
     * 老逻辑兼容
     */
    @Deprecated
    private List<WaveItemGroupVO> getOldWaveItemGroupVOS(Staff staff, List<Long> waveIdList, String waveListItemShowConf) {
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "查询波次商品信息仍走到老逻辑,waveIds:" + waveIdList));
        }
        List<WaveItemGroupVO> waveItemGroupVOS = Lists.newArrayList();
        waveIdList.sort(Comparator.comparing(java.util.function.Function.identity()));
        Map<Long, List<WaveItem>> waveId2ItemsMap = Maps.newHashMap();
        if (WaveItemInfo.ItemShowConf.isShowSuit(waveListItemShowConf)) {
            for (List<Long> subWaveIds : Lists.partition(waveIdList, 100)) {
                waveId2ItemsMap.putAll(queryWavesUnCombineItems(staff, subWaveIds).stream().collect(Collectors.groupingBy(WaveItem::getWaveId)));
            }
        } else {
            for (List<Long> subWaveIds : Lists.partition(waveIdList, 100)) {
                waveId2ItemsMap.putAll(queryWavesItems(staff, subWaveIds).stream().collect(Collectors.groupingBy(WaveItem::getWaveId)));
            }
        }

        for (Map.Entry<Long, List<WaveItem>> entry : waveId2ItemsMap.entrySet()) {
            waveItemGroupVOS.add(new WaveItemGroupVO().setWaveId(entry.getKey())
                    .setWaveItems(Optional.ofNullable(entry.getValue()).orElse(Lists.newArrayList())
                            .stream().map(data -> new WaveItemInfo()
                                    .setSysSkuId(data.getSysSkuId())
                                    .setSysItemId(data.getSysItemId())
                                    .setPropertiesName(data.getPropertiesName())
                                    .setOuterId(data.getOuterId())
                                    .setNum(data.getNum())
                                    .setWaveId(data.getWaveId())).collect(toList())
                    ));
        }
        return waveItemGroupVOS;
    }

    @Override
    public Map<Long, List<GoodsSectionOrderRecord>> getSid2GsOrderMap(Staff staff, List<Long> sids, List<Trade> trades, boolean showSkuGood) {
        Map<Long, List<GoodsSectionOrderRecord>> sid2GsMap = Maps.newHashMap();
        if (!showSkuGood) {
            return sid2GsMap;
        }
        List<Long> orderIds = WaveUtils.getOrdersForWave(TradeUtils.getOrders4Trade(trades), Boolean.TRUE).stream().map(Order::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return sid2GsMap;
        }
        List<GoodsSectionOrderRecord> gsRecords = WmsUtils.isNewWms(staff)
                ? wmsService.queryGoodsSectionInfoByOrderIdsOfNewWms(staff, orderIds)
                : wmsService.queryGoodsSectionInfoByOrderIds(staff, orderIds);
        if (CollectionUtils.isEmpty(gsRecords)) {
            return sid2GsMap;
        }
        fillItemInfo(staff, gsRecords);
        return gsRecords.stream().filter(r -> StringUtils.isNumeric(r.getSid())).collect(Collectors.groupingBy(data -> Long.valueOf(data.getSid())));
    }

    private void fillItemInfo(Staff staff, List<GoodsSectionOrderRecord> gsRecords) {
        Set<Long> sysSkuList = Sets.newHashSet();
        Set<Long> sysItemList = Sets.newHashSet();
        List<GoodsSectionOrderRecord> skuRecords = Lists.newArrayList();
        List<GoodsSectionOrderRecord> itemRecords = Lists.newArrayList();
        for (GoodsSectionOrderRecord record : gsRecords) {
            if (record.getSysSkuId() != null && record.getSysSkuId() > 0 ) {
                sysSkuList.add(record.getSysSkuId());
                skuRecords.add(record);
            } else {
                sysItemList.add(record.getSysItemId());
                itemRecords.add(record);
            }
        }
        Map<Long, DmjItem> itemId2DmjItemMap = CollectionUtils.isNotEmpty(sysItemList) ? DMJItemUtils.itemList2Map(itemService.queryItemWithSysItemId(staff, Lists.newArrayList(sysItemList))) : Maps.newHashMap();
        Map<Long, DmjSku> skuId2DmjSkuMap = CollectionUtils.isNotEmpty(sysSkuList) ? DmjSkuUtils.skuList2Map(itemService.querySkuWithSysSkuId(staff, Lists.newArrayList(sysSkuList))) : Maps.newHashMap();
        for (GoodsSectionOrderRecord record : skuRecords) {
            record.setOuterId(Optional.ofNullable(skuId2DmjSkuMap.get(record.getSysSkuId())).map(DmjSku::getSkuOuterId).orElse(StringUtils.EMPTY));
        }
        for (GoodsSectionOrderRecord record : itemRecords) {
            record.setOuterId(Optional.ofNullable(itemId2DmjItemMap.get(record.getSysItemId())).map(DmjItem::getOuterId).orElse(StringUtils.EMPTY));
        }
    }

    @Override
    @Transactional
    public void updateWaveFailedLast(Staff staff, List<Long> waveIds, Integer failedLast) {
        if (CollectionUtils.isEmpty(waveIds) || failedLast == null) {
            return;
        }
        List<Wave> updates = new ArrayList<>();
        for (Long waveId : waveIds) {
            Wave update = new Wave();
            update.setId(waveId);
            update.setFailedLast(failedLast);
            updates.add(update);
        }
        waveDao.batchUpdate(staff, updates);
    }

    public Map<Long,UserExpressTemplate> getAbroadTemplate(Staff staff){
        UserLogisticsProvider param = new UserLogisticsProvider();
        param.setSource(EnumLogisticsProviderType.ALIEXPRESS.getValue());
        param.setEnableStatus(EnumLogisticsProviderType.SHOPEE.getValue());
        List<UserExpressTemplate> userExpressTemplates = logisticsProviderService.getUserForeignExpress(staff, param);
        if(CollectionUtils.isEmpty(userExpressTemplates)){
            return Maps.newHashMap();
        }
        return userExpressTemplates.stream().collect(Collectors.toMap(UserExpressTemplate::getId, java.util.function.Function.identity(), (k1, k2) -> k1));
    }


    @Override
    public Map<String, Integer> queryFastInOutTradeCount(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds) {
        WmsConfig config = wmsService.getConfig(staff);
        List<WaveItem> items = waveQueryDao.queryFastInOutTradeCount(staff, warehouseId, sysItemIds, sysSkuIds, config, true, isFastInOutForIndex(staff.getCompanyId()), null);
        if (CollectionUtils.isNotEmpty(items)) {
            /**
             * sysItemId + sysSkuId + stockStatus 维度
             * 区分有货单件订单 和 无货单件订单
             */
            return items.stream().collect(Collectors.toMap(WmsKeyUtils::buildFastInOutKey, WaveItem::getNum, (a1, b1) -> a1));
        }
        return Maps.newHashMap();
    }

    @Override
    public Map<String, Integer> queryFastInOutTradeCount(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds, FastInOutTradeQueryParam param) {
        WmsConfig config = wmsService.getConfig(staff);
        List<WaveItem> items = waveQueryDao.queryFastInOutTradeCount(staff, warehouseId, sysItemIds, sysSkuIds, config, true, isFastInOutForIndex(staff.getCompanyId()), param);
        if (CollectionUtils.isNotEmpty(items)) {
            /**
             * sysItemId + sysSkuId + stockStatus 维度
             * 区分有货单件订单 和 无货单件订单
             */
            return items.stream().collect(Collectors.toMap(WmsKeyUtils::buildFastInOutKey, WaveItem::getNum, (a1, b1) -> a1));
        }
        return Maps.newHashMap();
    }

    @Override
    public WaveItem queryFastInOutMultiTrade(Staff staff, Long warehouseId, List<FastInOutSimpleItemVO> simpleItemVOS, FastInOutTradeQueryParam param) {
        WmsConfig config = wmsService.getConfig(staff);
        return waveQueryDao.queryFastInOutMultiTrade(staff, warehouseId, simpleItemVOS, config, param);
    }

    @Override
    public Map<String, List<Long>> queryFastInOutTradeList(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds) {
        WmsConfig config = wmsService.getConfig(staff);
        List<WaveItem> items = waveQueryDao.queryFastInOutTradeCount(staff, warehouseId, sysItemIds, sysSkuIds, config, false, isFastInOutForIndex(staff.getCompanyId()), null);
        if (CollectionUtils.isNotEmpty(items)) {
            return items.stream().collect(Collectors.groupingBy(WmsKeyUtils::buildFastInOutKey, Collectors.mapping(WaveItem::getSid, Collectors.toList())));
        }
        return Maps.newHashMap();
    }

    @Override
    public Map<String, List<Long>> queryFastInOutTradeList(Staff staff, Long warehouseId, List<Long> sysItemIds, List<Long> sysSkuIds, FastInOutTradeQueryParam param) {
        WmsConfig config = wmsService.getConfig(staff);
        List<WaveItem> items = waveQueryDao.queryFastInOutTradeCount(staff, warehouseId, sysItemIds, sysSkuIds, config, false, isFastInOutForIndex(staff.getCompanyId()), param);
        if (CollectionUtils.isNotEmpty(items)) {
            return items.stream().collect(Collectors.groupingBy(WmsKeyUtils::buildFastInOutKey, Collectors.mapping(WaveItem::getSid, Collectors.toList())));
        }
        return Maps.newHashMap();
    }

    @Override
    @Transactional
    public void modifyTradeTag4FastInOut(Staff staff, List<Long> needAddTagSids, List<Long> needRemoveTagSids) {
        long start = System.currentTimeMillis();
        filterFastInOutTagSids(staff, needAddTagSids, needRemoveTagSids);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "查询订单标签耗时：" +  (System.currentTimeMillis() - start) + "，修改订单数量：" + (getListSize(needAddTagSids) + getListSize(needRemoveTagSids))));
        }

        long start2 = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(needAddTagSids)) {
            tradeServiceDubbo.handleTagsBySids(staff, needAddTagSids.stream().distinct().collect(toList()), OpEnum.ADD_TAG, Collections.singletonList(SystemTags.TAG_FAST_IN_FAST_OUT), Boolean.TRUE);
            //生成唯一码的需要解绑
            eventCenter.fireEvent(this, new EventInfo("order.unique.code.cancel").setArgs(new Object[]{staff, needAddTagSids, true, UnboundReasonTypeEnum.RELEASE_BIND_FAST_IN_OUT}), null);
            // 已加入波次的需要踢出波次
            eventCenter.fireEvent(this, new EventInfo("trade.wave.remove.sid").setArgs(new Object[]{staff, needAddTagSids.toArray(new Long[0]), WaveOutBusinessEnum.FAST_IN_OUT.getOpName()}), null);
        }

        if (CollectionUtils.isNotEmpty(needRemoveTagSids)) {
            tradeServiceDubbo.handleTagsBySids(staff, needRemoveTagSids.stream().distinct().collect(toList()), OpEnum.REMOVE_TAG, Collections.singletonList(SystemTags.TAG_FAST_IN_FAST_OUT), Boolean.FALSE);
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "更新订单标签耗时：" +  (System.currentTimeMillis() - start2)));
        }
    }

    private static Integer getListSize(List<Long> needAddTagSids) {
        if (CollectionUtils.isEmpty(needAddTagSids)) {
            return 0;
        } else {
            return needAddTagSids.size();
        }
    }

    /**
     * 过滤即入即出标签的订单
     */
    private void filterFastInOutTagSids(Staff staff, List<Long> needAddTagSids, List<Long> needRemoveTagSids) {
        List<Long> sids = ArrayUtils.saveAddAll(needAddTagSids, needRemoveTagSids);
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        for (List<Long> subList : Lists.partition(sids, 2000)) {
            List<Trade> trades = waveQueryDao.queryTradeLabel(staff, subList);
            Set<Long> noFastInOutTagTrades = Sets.newHashSet();
            Set<Long> hasFastInOutTagTrades = Sets.newHashSet();
            for (Trade trade : trades) {
                if (StringUtils.isEmpty(trade.getTagIds()) || !trade.getTagIds().contains(FastInOutStockUtils.FAST_IN_OUT_TAG.getId().toString())) {
                    noFastInOutTagTrades.add(trade.getSid());
                } else if (trade.getTagIds().contains(FastInOutStockUtils.FAST_IN_OUT_TAG.getId().toString())) {
                    hasFastInOutTagTrades.add(trade.getSid());
                }
            }
            if (CollectionUtils.isNotEmpty(noFastInOutTagTrades)) {
                logger.debug(LogHelper.buildLog(staff, String.format("没有即入即出标签的订单，无需删除标签，%s", noFastInOutTagTrades)));
                needRemoveTagSids.removeIf(noFastInOutTagTrades::contains);
            }
            if (CollectionUtils.isNotEmpty(hasFastInOutTagTrades)) {
                logger.debug(LogHelper.buildLog(staff, String.format("有即入即出标签的订单，无需添加标签，%s", hasFastInOutTagTrades)));
                needAddTagSids.removeIf(hasFastInOutTagTrades::contains);
            }
        }
    }

    /**
     * 快进快出开启强制索引公司
     * @param companyId
     * @return
     */
    private boolean isFastInOutForIndex(Long companyId) {
        try {
            String fastInOutForIndexCompanyIds = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getWave().getFastInOutForIndexCompanyIds();
            if (StringUtils.isNotEmpty(fastInOutForIndexCompanyIds) && ArrayUtils.toLongSet(fastInOutForIndexCompanyIds).contains(companyId)) {
                return true;
            }
        } catch (Exception e) {
            logger.error("获取快进快出开启强制索引公司失败", e);
        }
        return false;
    }

    @Override
    public List<Long> querySidsBySection(Staff staff, Long waveId, Long firstSid, Long lastSid) {
        if (!com.raycloud.dmj.domain.utils.DataUtils.checkLongNotEmpty(firstSid) || !com.raycloud.dmj.domain.utils.DataUtils.checkLongNotEmpty(lastSid)) {
            return new ArrayList<>();
        }
        return waveTradeDao.querySidsBySectionAndWaveId(staff, waveId, firstSid, lastSid);
    }

    @Override
    public List<Long> queryAllStatusSidsBySection(Staff staff, Long waveId, Long firstSid, Long lastSid, List<Integer> statusList) {
        if (!com.raycloud.dmj.domain.utils.DataUtils.checkLongNotEmpty(firstSid) || !com.raycloud.dmj.domain.utils.DataUtils.checkLongNotEmpty(lastSid)) {
            return new ArrayList<>();
        }
        return waveTradeDao.queryAllStatusSidsBySectionAndWaveId(staff, waveId, firstSid, lastSid, statusList);
    }

    @Override
    public PageListBase<WaveTrade> queryExcepAndRemoveWaveTradeList(Staff staff, Long waveId, Page page) {
        PageListBase<WaveTrade> pageList = new PageList<WaveTrade>();
        pageList.setPage(page);
        Long count = waveTradeDao.queryExcepAndRemoveWaveTradeCount(staff, waveId);
        pageList.setTotal(count != null ? count : 0L);


        List<WaveTrade> waveTrades = waveTradeDao.queryExcepAndRemoveWaveTradeList(staff, waveId, page);
        wavePositionService.fillPosition(staff, waveId, waveTrades);
        pageList.setList(waveTrades);
        fillTradeInfo(staff,waveTrades);
        return pageList;
    }

    @Override
    @Transactional
    public void updateWavesExpressId(Staff staff,List<Long> waveIds,Long expressId){
        if (CollectionUtils.isEmpty(waveIds)) {
            return;
        }
        List<Wave> updateWaves = Lists.newArrayListWithCapacity(waveIds.size());
        for (Long waveId : waveIds) {
            Wave updateWave = new Wave();
            updateWave.setId(waveId);
            updateWave.setExpressCompanyId(expressId);
            updateWaves.add(updateWave);
        }
        waveDao.batchUpdate(staff,updateWaves);
    }

    @Override
    public void updateWavesLogisticsCompanyId(Staff staff, List<Long> waveIds, Long logisticsCompanyId, Long expressId) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return;
        }
        List<Wave> updateWaves = Lists.newArrayListWithCapacity(waveIds.size());
        for (Long waveId : waveIds) {
            Wave updateWave = new Wave();
            updateWave.setId(waveId);
            updateWave.setLogisticsCompanyId(logisticsCompanyId);
            updateWave.setExpressCompanyId(expressId);
            updateWaves.add(updateWave);
        }
        waveDao.batchUpdate(staff,updateWaves);
    }

    public List<Map<String, Object>> pickingHand(Staff staff, List<Long> waveIds, List<WavePicking> pickingList,ProgressData progressData) {
        List<Map<String, Object>> result = Lists.newArrayListWithCapacity(pickingList.size());
        Set<Long> successWaveIds = Sets.newHashSet();
        Map<Long, String> failWaveIdsMap = Maps.newHashMap();
        for (WavePicking picking : pickingList) {
            try {
                tradePostPrintService.pickingByHand(staff, picking);
                // 直接拣选完成，更新拣选统计信息
                tradePostPrintService.updateWavePickingStatistics(staff, getDirectPickFinish(staff, picking.getWaveId(), picking.getId()));
                //直接拣选商品日志输出
                waveBeginPickingLog(picking, staff);
                successWaveIds.add(picking.getWaveId());
                if (progressData != null) {
                    waveProgressBusiness.touchProgress(staff, ProgressEnum.PROGRESS_TRADE_POST_PRINT_PICKING_HAND, progressData, 1);
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "人工手动拣选失败"), e);
                Map<String, Object> map = new HashMap<>();
                map.put("code", picking.getWaveCode());
                map.put("error", e.getMessage());
                result.add(map);
                failWaveIdsMap.put(picking.getWaveId(), e.getMessage());
            }
        }
        // 记录波次日志
        batchAddWaveTrace(staff, successWaveIds, failWaveIdsMap);
        return result;
    }

    private void waveBeginPickingLog(WavePicking wavePicking, Staff staff) {
        try{
            List<Long> sids = querySidsByWaveId(staff, wavePicking.getWaveId());
            if (CollectionUtils.isNotEmpty(sids)){
                List<WaveSortingDetail> waveSortingDetails = waveSortingService.queryDetailBySids(staff,sids);
                WaveItemTraceLogHelper.batchRecodeItemTraceLogByWmsModel(wmsService,itemTraceService,staff,sids, ItemTraceActionEnum.WAVE_PICK.getCode(),
                        ItemTraceBillTypeEnum.WAVE.getCode(), String.valueOf(wavePicking.getWaveId()), PickingType.parseType(wavePicking.getPickingType()).getName(),waveSortingDetails);
            }else {
                logger.error(LogHelper.buildLog(staff, String.format("直接拣选商品日志输出 waveId:%s，商品为空", wavePicking.getWaveId())));
            }
        }catch (Exception e){
            logger.error("直接拣选商品日志输出失败 ,message:" + e.getMessage(), e);
        }
    }

    private WavePicking getDirectPickFinish(Staff staff, Long waveId, Long pickId) {
        Wave wave = queryWaveById(staff, waveId);
        WavePicking update = new WavePicking();
        update.setId(pickId == null ? wave.getPickingId() : pickId);
        if (java.util.Objects.equals(PickStyle.PAPER.getStyleId(), wave.getPickStyle())) {
            setPaperPickStat(staff, wave, update);
        } else {
            update.setUnPickNum(0);
            update.setShortageNum(0);
            //改造：计划拣选数=商品数量－未拣选踢出数
            update.setPlanPickNum(wave.getItemCount() - wave.getRemovedCount());
            update.setPickedNum(update.getPlanPickNum());
        }
        addWavePickingStatLogOnlyEs(staff, null, update, "getDirectPickFinish");
        return update;
    }

    private void batchAddWaveTrace(Staff staff, Set<Long> successWaveIds, Map<Long, String> failWaveIdsMap) {
        if (CollectionUtils.isNotEmpty(successWaveIds)) {
            waveTraceService.batchAddWaveTrace(staff, WaveTraceUtils.buildBatchWaveTrace(staff, Lists.newArrayList(successWaveIds), WaveTraceOperateEnum.POST_PRINT_PICKING_HAND, "人工拣选完成"));
        }
        if (org.apache.commons.collections.MapUtils.isNotEmpty(failWaveIdsMap)) {
            List<WaveTrace> waveTraces = Lists.newArrayList();
            for (Map.Entry<Long, String> entry : failWaveIdsMap.entrySet()) {
                WaveTrace waveTrace = WaveTraceUtils.buildWaveTrace(staff, entry.getKey(), WaveTraceOperateEnum.POST_PRINT_PICKING_HAND, "人工拣选失败,原因:" + entry.getValue(), 1);
                waveTraces.add(waveTrace);
            }
            waveTraceService.batchAddWaveTrace(staff, waveTraces);
        }
    }

    private void setPaperPickStat(Staff staff, Wave wave, WavePicking update) {
        QueryAllocateGoodsRecordParams params = new QueryAllocateGoodsRecordParams();
        params.setWaveId(wave.getId());
        List<AllocateGoodsRecord> records = wmsService.queryAllocateGoodsRecords(staff, params);
        if (CollectionUtils.isEmpty(records)) {
            update.setUnPickNum(0);
            update.setPickedNum(0);
            // 全部缺货
            update.setShortageNum(wave.getItemCount());
            update.setPlanPickNum(wave.getItemCount());
        } else {
            Long sum = records.stream().collect(Collectors.summarizingInt(AllocateGoodsRecord::getAllocatedNum)).getSum();
            update.setUnPickNum(0);
            update.setPickedNum(sum.intValue());
            update.setShortageNum(wave.getItemCount() - sum.intValue());
            update.setPlanPickNum(wave.getItemCount());
        }
    }


    @Override
    public PageListBase<WaveItem> queryExcepAndRemoveWaveOrderList(Staff staff, Long waveId, Page page) {
        PageListBase<WaveItem> pageList = new PageList<>();
        pageList.setPage(page);
        Long count = waveQueryDao.queryExcepAndRemoveWaveOrderCount(staff, waveId);
        pageList.setTotal(count == null ? 0L : count);
        List<TbOrder> tbOrders = waveQueryDao.queryExcepAndRemoveWaveOrder(staff, waveId, page);
        pageList.setList(exchangeWaveItem(staff,tbOrders));
        return pageList;
    }

    private List<WaveItem> exchangeWaveItem(Staff staff,List<TbOrder> tbOrders){
        if (CollectionUtils.isEmpty(tbOrders)) {
            return Collections.emptyList();
        }
        List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, false, OrderUtils.toSids(tbOrders));
        Map<Long, Trade> sidMap = null;
        if (CollectionUtils.isNotEmpty(trades)) {
            sidMap = TradeUtils.toMapBySid(trades);
        }
        List<WaveItem> waveItemLists = Lists.newArrayListWithCapacity(tbOrders.size());
        for (TbOrder tbOrder : tbOrders) {
            WaveItem waveItem = new WaveItem();
            waveItem.setSid(tbOrder.getSid());
            waveItem.setOuterId(tbOrder.getSysOuterId());
            waveItem.setTitle(tbOrder.getSysTitle());
            waveItem.setPropertiesName(tbOrder.getSysSkuPropertiesName());
            waveItem.setPropertiesAlias(tbOrder.getSysSkuPropertiesAlias());
            waveItem.setPicPath(tbOrder.getSysPicPath());
            if (org.apache.commons.collections.MapUtils.isNotEmpty(sidMap) && sidMap.containsKey(tbOrder.getSid())) {
                waveItem.setOutSid(sidMap.get(tbOrder.getSid()).getOutSid());
            }
            waveItemLists.add(waveItem);
        }
        return waveItemLists;
    }

    private void fillTradeInfo(Staff staff,List<WaveTrade> waveTrades) {
        if (CollectionUtils.isEmpty(waveTrades)) {
            return;
        }

        Set<Long> sidSet = waveTrades.stream().map(WaveTrade::getSid).collect(Collectors.toSet());
        List<Trade> trades = waveUseTradeServiceProxy.queryBySids(staff, true, sidSet.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        Map<Long, Trade> sidMap = TradeUtils.toMapBySid(trades);
        List<Trade> hasExcepIds = trades.stream().filter(trade ->  TradeExceptUtils.isContainCustomExcept(staff,trade)).collect(toList());
        List<TradeTag> tradeTags = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hasExcepIds)) {
            tradeTags = tradeServiceDubbo.queryTradeTagList(staff,1);
        }
        for (WaveTrade waveTrade : waveTrades) {
            if (sidMap.containsKey(waveTrade.getSid())) {
                Trade trade = sidMap.get(waveTrade.getSid());
                waveTrade.setOutSid(trade.getOutSid());
                waveTrade.setSysStatus(trade.getSysStatus());
                waveTrade.setExceptIdSet(TradeUtils.parseExcept(staff,trade));

                if (!TradeExceptUtils.isContainCustomExcept(staff,trade)) {
                    continue;
                }
                List<TradeTag> systemTradeTagExcepts = SystemExcepts.getSystemExcepts();
                systemTradeTagExcepts.addAll(CollectionUtils.isEmpty(tradeTags) ? new ArrayList<>() : tradeTags);

                //自定义异常
                Set<Long> exceptIds = TradeExceptUtils.getCustomExceptIds(staff,trade);
                List<TradeTag> tradeExceptTag = systemTradeTagExcepts.stream().filter(tradeTag -> exceptIds.contains(tradeTag.getId())).collect(Collectors.toList());
                waveTrade.setExceptIds(StringUtils.join(exceptIds,","));
                waveTrade.setExceptTradeTags(tradeExceptTag);
            }
        }
    }

    /**
     * 查询已取消波次未踢出订单sid
     */
    @Override
    public List<Long> queryCanceledUnRemoveSids(Staff staff) {
        return waveTradeDao.queryCanceledUnRemoveSids(staff);
    }

    @Override
    public Map<String, String> getGoodsSectionCodeOfWaveUniqueCode(Staff staff, List<Long> waveIds) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return Maps.newHashMap();
        }
        WaveConfig waveConfig = waveConfigService.get(staff);
        if (!Objects.equal(waveConfig.getOpenWaveUniqueCode(), 1)) {
            return Maps.newHashMap();
        }

        WaveUniqueCodeParams params = new WaveUniqueCodeParams();
        params.setWaveIds(waveIds);
        List<WaveUniqueCode> waveUniqueCodes = waveUniqueCodeService.queryByCondition(staff, params);
        if (CollectionUtils.isEmpty(waveUniqueCodes)) {
            return Maps.newHashMap();
        }
        List<WaveUniqueCode> haveGoodsSectionCodes = waveUniqueCodes.stream().filter(code -> StringUtils.isNotEmpty(code.getGoodsSectionCode())).collect(toList());
        if (CollectionUtils.isEmpty(haveGoodsSectionCodes)) {
            return Maps.newHashMap();
        } else {
            return haveGoodsSectionCodes.stream().collect(Collectors.toMap(WaveUniqueCode::getUniqueCode, WaveUniqueCode::getGoodsSectionCode));
        }
    }

    @Override
    public void updateWaveSeedTime(Staff staff, Long waveId, Date time, Integer type) {
        if (type == null) {
            return;
        }
        Wave wave = waveDao.queryById(staff, waveId);
        if (wave == null) {
            return;
        }
        Wave update = null;
        if (type == 1 && wave.getSeedStartTime() == null) {
            update = new Wave();
            update.setSeedStartTime(time);
            update.setId(wave.getId());
        } else if (type == 2 && wave.getSeedEndTime() == null) {
            update = new Wave();
            update.setSeedStartTime(time);
            update.setId(wave.getId());
        } else if (type == 3) {
            update = new Wave();
            update.setId(wave.getId());
            update.setClearSeedEndTime(1);
            update.setClearSeedStartTime(1);
        }
        if (update != null) {
            waveDao.update(staff, update);
        }
    }

    @Override
    public void checkTradePickStatus(Staff staff, Trade trade, WaveConfig waveConfig, TradeValidator validator) {
        if (validator.hasError()) {
            return;
        }
        Map<String, Object> chatConfigMap = WaveConfigUtils.parseExtendConfig(waveConfig.getChatConfigs());
        if (chatConfigMap.get(WaveChatConfigsEnum.SHORTAGE_PICKED_ALLOW_PACK.getKey()) == null || chatConfigMap.get(WaveChatConfigsEnum.SHORTAGE_PICKED_ALLOW_PACK.getKey()).equals(1)) {
            return;
        }

        WavePicking wavePicking = null;
        if (trade.getWaveId() != null && trade.getWaveId() > 0) {
            wavePicking = wavePickingDao.getByWaveId(staff, trade.getWaveId());
        }
        //校验订单是否全拣
        try {
            if (!wmsPdaDubboService.checkTradeAllPicked(staff, trade, wavePicking)) {
                validator.setMessage("单据未拣选完成，不允许验货");
                validator.setCode(300100);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "查询订单拣选状态失败"), e);
            validator.setMessage("单据未拣选完成，不允许验货");
            validator.setCode(300100);
        }
    }

    @Override
    public void fillShipper(Staff staff, Trade trade) {
        if (!CompanyUtils.openMultiShipper(staff)) {
            return;
        }
        if (trade == null || trade.getUserId() == null) {
            return;
        }
        List<User> userList = shipperDubbo.queryByUserId(staff, Lists.newArrayList(trade.getUserId()));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(userList)) {
            return;
        }
        WaveAssembleInfo waveAssembleInfo = trade.getWaveAssembleInfo() == null ? new WaveAssembleInfo() : trade.getWaveAssembleInfo();
        waveAssembleInfo.setShipperId(userList.get(0).getShipperId());
        waveAssembleInfo.setShipperName(userList.get(0).getShipperName());
        trade.setWaveAssembleInfo(waveAssembleInfo);
    }

    public void addWaveTraceLog(Staff staff, Long waveId, WaveTraceOperateEnum operateEnum, String msg) {
        if (StringUtils.isEmpty(msg)) {
            return;
        }
        logger.debug(convert2Es(staff, waveId, operateEnum, msg));
    }

    private WaveTraceLog convert2Es(Staff staff, Long waveId, WaveTraceOperateEnum operateEnum, String msg) {
        WaveTraceLog waveTraceLog = new WaveTraceLog();
        waveTraceLog.setCompanyId(staff.getCompanyId());
        waveTraceLog.setContent(msg);
        waveTraceLog.setCreated(new Date());
        waveTraceLog.setStaffName(staff.getName());
        if (java.util.Objects.nonNull(operateEnum)) {
            if (java.util.Objects.nonNull(operateEnum.getDomain())) {
                waveTraceLog.setDomain(operateEnum.getDomain().getValue());
            }
            waveTraceLog.setOperate(operateEnum.getOperate());
        }
        waveTraceLog.setWaveId(waveId);
        waveTraceLog.setStaffId(staff.getId());
        return waveTraceLog;
    }

    /**
     * 查询拣选信息
     */
    @Override
    public List<WavePicking> getWavePickingByWaveIds(Staff staff, List<Long> waveIds) {
        return wavePickingDao.getByWaveIds(staff, waveIds);
    }


    @Override
    public List<WaveTrade> queryWaveTradeByParams(Staff staff, WaveTradeQueryParams params) {
        return waveTradeDao.queryWaveTradePage(staff, params, null, null);
    }
    private boolean openNoCheckMultiCodeUniqueness(Staff staff) {
        ItemConfig itemConfig;
        try {
            itemConfig = itemService.queryItemConfigByCompanyId(staff.getCompanyId());
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "获取商品配置报错"), e);
            return false;
        }
        if (itemConfig == null) {
            return false;
        }
        ItemConfigConf configConf = itemConfig.getConf();
        if (configConf == null || configConf.getNoCheckMultiCodeUniqueness() == null) {
            return false;
        }
        return configConf.getNoCheckMultiCodeUniqueness() == 1;
    }


    /**
     * 校验波次的快递打印状态和配货状态(仅取消波次使用）
     * @param staff
     * @param waveIdList
     * @return
     */
    @Override
    public Map<String, Object> checkPrintStatusAndDistributionStatus(Staff staff, List<Long> waveIdList) {
        WaveFilterParams params = new WaveFilterParams();
        params.setWaveIds(waveIdList);
        params.setPrintStatus("3");
        // 波次打印/波次管理单页最大显示数量为500，即最多勾选数量为500，设置pageSize为500即可查询所有勾选波次
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(500);
        PageListBase<Wave> wavePageList = queryAllWaves(staff, params, page);
        List<Wave> waves = wavePageList.getList();
        // 已完成和已取消不校验
        Set<Integer> filterStatus = Sets.newHashSet(Wave.STATUS_FINISHED, Wave.STATUS_DELETED);
        waves = waves.stream().filter(wave -> !filterStatus.contains(wave.getStatus())).collect(Collectors.toList());
        // 波次快递单打印状态：全部打印/部分打印
        Set<String> filterPrintStatus = Sets.newHashSet(Wave.WavePrintStatusEnum.ALL_PRINT.getValue(), Wave.WavePrintStatusEnum.PART_PRINT.getValue());
        List<String> printStatusList = waves.stream().map(Wave::getPrintStatus).filter(filterPrintStatus::contains).distinct().collect(Collectors.toList());
        // 波次配货状态：未拣选/拣选中
        Set<Integer> filterDistributionStatus = Sets.newHashSet(Wave.DISTRIBUTION_STATUS_NONE, Wave.DISTRIBUTION_STATUS_ON);
        List<Integer> distributionStatusList = waves.stream().map(Wave::getDistributionStatus).filter(filterDistributionStatus::contains).distinct().collect(Collectors.toList());
        Map<String, Object> statusMap = Maps.newHashMap();
        statusMap.put("printStatusList", printStatusList);
        statusMap.put("distributionStatusList", distributionStatusList);
        return statusMap;
    }
    @Override
    public List<WaveTrade> queryPrintPickWaveTradesByWaveIds(Staff staff, List<Long> waveIds, boolean pickBillGetDataFromTrade) {
        if (CollectionUtils.isEmpty(waveIds)) {
            return Lists.newArrayList();
        }
        return waveTradeDao.queryPrintPickWaveTradesByWaveIds(staff, waveIds, pickBillGetDataFromTrade);
    }

    @Override
    public List<UnderstockedGroup> queryWaveIdUnderStockedGroupMap(Staff staff) {
        List<UnderstockedGroup> understockedGroups = Lists.newArrayList();
        Map<Long, String> resultMap = Maps.newHashMap();
        String warehouseGroup = staff.getWarehouseGroup();
        if (StringUtils.isBlank(warehouseGroup)) {
            logger.debug(String.format("员工%s(%s)无仓库权限！", staff.getName(), staff.getId()));
            return understockedGroups;
        }
        WaveFilterParams params = new WaveFilterParams();
        params.setWarehouseIds(warehouseGroup);
        params.setStatus(String.valueOf(Wave.STATUS_CREATED));
        params.setDistributionStatus(UNDERSTOCKED_WAVE_SEED_DISTRIBUTIONSTATUS);
        // 只查订单波次
        params.setPickingTypes("0,1,2,3,12");

        List<Wave> waves = waveDao.queryPageList(staff, params, null);
        if (CollectionUtils.isNotEmpty(waves)) {
            resultMap = waves.stream().filter(wave -> StringUtils.isNotEmpty(wave.getUnderstockedGroup())).collect(Collectors.toMap(Wave::getId, Wave::getUnderstockedGroup));
        }
        for (Map.Entry<Long, String> entry : resultMap.entrySet()) {
            UnderstockedGroup understockedGroup = new UnderstockedGroup();
            understockedGroup.setWaveId(entry.getKey());
            understockedGroup.setUnderstockedGroup(entry.getValue());
            understockedGroups.add(understockedGroup);
        }
        return understockedGroups;
    }

    @Override
    @Cacheable(value = "defaultCache#600", key = "'wave_trade_sync_feature_' + #staff.companyId")
    public boolean checkWaveTradeSyncFeature(Staff staff) {
        CheckHasFeatureRequest request = new CheckHasFeatureRequest();
        request.setCompanyId(staff.getCompanyId());
        request.setCode("waveTradeSync");
        CheckHasFeatureResponse response = indexDubboService.checkHasFeature(request);
        if (response == null) {
            return false;
        }
        return response.isHasFeature();
    }


    @Override
    public CrossBorderWavePrintInfo queryCrossBorderWavePrintInfo(Staff staff, Long waveId) {
        CrossBorderWavePrintInfo result = new CrossBorderWavePrintInfo();
        result.setWaveId(waveId);
        if(staff==null||waveId==null){
            return result ;
        }
        CrossBorderWavePrintInfo info = waveSortingDao.queryItemPositionByWaveId(staff, waveId);
        if(info==null){
            logger.info(LogHelper.buildLog(staff,"queryCrossBorderWavePrintInfo info 为空 waveId: "+ waveId));
            return result;
        }
        info.setPositionCode(WaveUtils.generatePositionCode(staff, info.getPositionNo()));
        return info;
    }
    @Override
    public void modifyWaveTag(Staff staff, List<Long> tagIds, List<Long> waveIds, int operateType) {
        if (CollectionUtils.isEmpty(tagIds) || CollectionUtils.isEmpty(waveIds)) {
            return;
        }
        if (logger.isInfoEnabled()) {
            logger.info(LogHelper.buildLog(staff, "修改波次标签，waveIds:" + waveIds + ",tagIds：" + tagIds + ",operateType:" + operateType));
        }
        List<Wave> waves = waveDao.queryByIds(staff, waveIds.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(waves)) {
            return;
        }
        List<Wave> updates = new ArrayList<>();
        for (Wave wave : waves) {
            List<Long> dbTagIds = (StringUtils.isEmpty(wave.getTagIds()) ? new ArrayList<>() : DataUtils.string2LongList(DataUtils.strs2List(wave.getTagIds())));
            // 先把无标签去掉，如果到最后没有标签了，则加上
            dbTagIds.remove(0L);
            if (operateType == 0) {
                dbTagIds.addAll(tagIds);
            }
            if (operateType == 1) {
                dbTagIds.removeAll(tagIds);
            }
            Wave update = new Wave();
            update.setId(wave.getId());
            if (CollectionUtils.isEmpty(dbTagIds)) {
                update.setTagIds("0");
            } else {
                update.setTagIds(Strings.join(",", Sets.newHashSet(dbTagIds)));
            }
            updates.add(update);
        }
        if (CollectionUtils.isNotEmpty(updates)) {
            waveDao.batchUpdate(staff, updates);
        }
    }

    @Override
    public void stallWaveSplitConsign(Staff staff, String waveIds) {
        Assert.isTrue(StringUtils.isNotEmpty(waveIds), "请选择待拆分订单关联的波次");
        Long[] sids = ArrayUtils.toLongArray(waveIds);
        List<Wave> waves = waveDao.queryByIds(staff, sids);
        for (Wave wave : waves) {
            Assert.isTrue(Objects.equal(wave.getRuleType(), WaveRule.RULE_TYPE_STALL), "仅允许已完成的档口配货波次的关联订单拆分");
            Assert.isTrue(Objects.equal(wave.getStatus(), Wave.STATUS_FINISHED), "仅允许已完成的档口配货波次的关联订单拆分");
        }

        ProgressData progressData = new ProgressData();
        progressData.setCountAll(100);
        Assert.isTrue(waveProgressBusiness.addProgress(staff, ProgressEnum.PROGRESS_STALL_WAVE_SPLIT, progressData), "正在进行订单拆分，请稍候！");
        eventCenter.fireEvent(this, new EventInfo("stall.wave.split.consign").setArgs(new Object[]{staff, ArrayUtils.toLongList(waveIds)}), null);
    }

    @Override
    public void waveBeginPickingLogFromListener(WavePicking wavePicking, Staff staff) {
        try{
            List<Long> sids = querySidsByWaveId(staff, wavePicking.getWaveId());
            if (CollectionUtils.isNotEmpty(sids)){
                List<WaveSortingDetail> waveSortingDetails = waveSortingService.queryDetailBySids(staff,sids);
                WaveItemTraceLogHelper.batchRecodeItemTraceLogByWmsModel(wmsService,itemTraceService,staff,sids, ItemTraceActionEnum.WAVE_PICK.getCode(),
                        ItemTraceBillTypeEnum.WAVE.getCode(), String.valueOf(wavePicking.getWaveId()), PickingType.parseType(wavePicking.getPickingType()).getName(),waveSortingDetails);
            }else {
                logger.error(LogHelper.buildLog(staff, String.format("直接拣选商品日志输出 waveId:%s，商品为空", wavePicking.getWaveId())));
            }
        }catch (Exception e){
            logger.error("直接拣选商品日志输出失败 ,message:" + e.getMessage(), e);
        }
    }

    /**
     * 按照时间对播种绩效进行修复
     * @param staff
     * @param startTime
     * @param endTime
     */
    @Override
    public void repairSeedRecordByDate(Staff staff, Date startTime, Date endTime) {
        Assert.notNull(startTime, "startTime不能为空！");
        Assert.notNull(endTime, "endTime不能为空！");
        List<Long> waveIds = waveSortingDao.queryWaveIdsNotInSeedRecord(staff, startTime, endTime);
        if (CollectionUtils.isEmpty(waveIds)) {
            return;
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, "修改播种绩效，总波次数：" + waveIds.size()));
        }

        List<List<Long>> partition = Lists.partition(waveIds, 10);
        for (List<Long> partWaveIds : partition) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "修改播种绩效，partWaveIds：" + partWaveIds));
            }

            List<WaveSorting> sortings = waveSortingDao.statWaveSeedLogGroupForSid(staff, null, partWaveIds);
            sortings = sortings.stream().filter(s -> s.getSeederId() != null && s.getSeederId() > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sortings)) {
                logger.debug(LogHelper.buildLog(staff, "波次完结记录绩效失败，未查询到播种人！"));
                continue;
            }
            doSaveRecords(staff, sortings, null, waveIds, WavePrintType.SEED);
        }
    }

    private void doSaveRecords(Staff staff, final List<WaveSorting> sortings, final List<Long> sids, final List<Long> waveIds, final WavePrintType printType) {
        lockService.locks(Lists.newArrayList(getERPLocks(staff, sortings)), () -> {
            List<WaveScanRecord> records = Lists.newArrayListWithCapacity(sortings.size());
            List<WaveScanRecord> scanRecords = waveScanRecordDao.queryBySids(staff, sids, waveIds);
            Map<Long, List<WaveScanRecord>> sidRecordMap = scanRecords.stream().collect(Collectors.groupingBy(WaveScanRecord::getSid));

            for (WaveSorting sorting : sortings) {
                List<WaveScanRecord> sidRecords = sidRecordMap.get(sorting.getSid());
                if (sidRecords != null) {
                    boolean contains = sidRecords.stream().map(WaveScanRecord::getWaveId).collect(Collectors.toSet()).contains(sorting.getWaveId());
                    if (contains) {
                        logger.debug(LogHelper.buildLog(staff, String.format("订单[%s]已经存在波次[%s]扫描记录，不记录！", sorting.getSid(), sorting.getWaveId())));
                        continue;
                    }
                }
                WaveScanRecord record = new WaveScanRecord();
                record.setSid(sorting.getSid());
                record.setWaveId(sorting.getWaveId());
                record.setItemNum(sorting.getItemNum().intValue());
                record.setCreated(sorting.getSeedEndTime() == null ? new Date() : sorting.getSeedEndTime());
                record.setPrintType(printType.getValue());
                record.setStaffId(ObjectUtils.defaultIfNull(sorting.getSeederId(), staff.getId()));
                record.setStaffName(ObjectUtils.defaultIfNull(sorting.getSeederName(), staff.getName()));
                records.add(record);
            }

            if (!records.isEmpty()) {
                waveScanRecordDao.batchInsert(staff, records);
            }

            return null;
        });
    }

    public List<ERPLock> getERPLocks(Staff staff, List<WaveSorting> sortings) {
        List<Long> waveIds = sortings.stream().map(WaveSorting::getWaveId).collect(Collectors.toList());

        return Sets.newHashSet(waveIds).stream().map(w -> {
            ERPLock erpLock = ERPLockUtils.createERPLock(staff.getDbNo(), staff.getCompanyId(),
                    "wave_scan_record_save_" + staff.getCompanyId() + "_" + w);
            return erpLock;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateTradeWaveId(Staff staff, List<Trade> trades) {
        waveTradeDao.updateTradeWaveId(staff, trades);
    }

    @Override
    public void rollBackStatusByWaveId(Staff staff, Long waveId) {
        List<Long> sidList = waveQueryDao.queryUnPrintTradeList(staff, waveId);
        Assert.notEmpty(sidList, "不存在需要回滚的订单数据");
        waveSortingDao.rollBackPostStatus(staff,  waveId, sidList);
    }



    @Override
    public List<Long> queryWarehouseIds(Staff staff) {
        List<Warehouse> warehouses = warehouseService.queryAll(staff.getCompanyId(), 1);
        List<Long> warehouseIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(warehouses)) {
            warehouseIds = warehouses.stream().map(Warehouse::getId).collect(toList());
        }
        return warehouseIds;
    }
}

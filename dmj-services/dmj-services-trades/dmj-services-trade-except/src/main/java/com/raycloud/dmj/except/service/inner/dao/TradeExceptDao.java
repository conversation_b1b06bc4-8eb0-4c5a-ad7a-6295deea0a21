package com.raycloud.dmj.except.service.inner.dao;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.except.domain.TradeExceptCount;
import com.raycloud.dmj.except.domain.TradeExcept;
import com.raycloud.dmj.except.domain.TradeExceptType;
import com.raycloud.erp.db.router.ibatis.SqlMapClientBaseDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName TradeExceptDao
 * @Description 订单异常Dao
 * <AUTHOR>
 * @Date 2023/2/14 12:56
 * @Version 1.0
 */
@Repository
public class TradeExceptDao extends SqlMapClientBaseDao {

    public TradeExceptDao() {
        domain = "tradeExcept";
    }

    private void init(Staff staff, TradeExcept except) {
        except.setDbNo(staff.getDbInfo().getTradeExceptDbNo());
        except.setDbKey(staff.getDbKey());
        except.setCompanyId(staff.getCompanyId());
    }

    public void insert(Staff staff, List<TradeExcept> excepts) {
        if (CollectionUtils.isEmpty(excepts)) {
            return;
        }
        excepts.forEach(except -> init(staff, except));
        super.batchInsert(staff, buildStatementName("insert"), excepts);
    }

    public void delete(Staff staff, List<TradeExcept> excepts) {
        if (CollectionUtils.isEmpty(excepts)) {
            return;
        }
        excepts.forEach(except -> init(staff, except));
        super.batchUpdate(staff, buildStatementName("delete"), excepts);
    }

    public void logicDelete(Staff staff, List<TradeExcept> excepts) {
        if (CollectionUtils.isEmpty(excepts)) {
            return;
        }
        excepts.forEach(except -> init(staff, except));
        super.batchUpdate(staff, buildStatementName("logicDelete"), excepts);
    }


    public void insertBatch(Staff staff, List<TradeExcept> excepts) {
        if (CollectionUtils.isEmpty(excepts)) {
            return;
        }
        for(TradeExcept tradeExcept:excepts){
            initExceptFied(tradeExcept);
        }
        List<List<TradeExcept>> exceptLists = ListUtils.splitList(excepts, 100);
        for(List<TradeExcept> exceptList:exceptLists){
            Map<String, Object> params = initParams(staff);
            params.put("excepts", exceptList);
            getSqlMapClientTemplate(staff).update(buildStatementName("insertBatch"), params);
        }

    }

    public void batchDeleteByIds(Staff staff, List<TradeExcept> excepts) {
        if (CollectionUtils.isEmpty(excepts)) {
            return;
        }
        List<Long> ids = excepts.stream().map(TradeExcept::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<List<Long>> lists = ListUtils.splitList(ids, 50);
        for(List<Long> idList:lists){
            Map<String, Object> params = initParams(staff);
            params.put("ids", idList);
            if(CollectionUtils.isEmpty(idList)){
                continue;
            }
            getSqlMapClientTemplate(staff).update(buildStatementName("batchDeleteByIds"), params);
        }

    }

    public void logicBatchDeleteByIds(Staff staff, List<TradeExcept> excepts) {
        if (CollectionUtils.isEmpty(excepts)) {
            return;
        }
        List<Long> ids = excepts.stream().map(TradeExcept::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<List<Long>> lists = ListUtils.splitList(ids, 50);
        for(List<Long> idList:lists){
            Map<String, Object> params = initParams(staff);
            params.put("ids", idList);
            if(CollectionUtils.isEmpty(idList)){
                continue;
            }
            getSqlMapClientTemplate(staff).update(buildStatementName("logicBatchDeleteByIds"), params);
        }

    }

    /**
     * 查询用户自定义的异常
     */
    public List<TradeExceptType> queryCustomExceptType(Staff staff, List<Long> exceptIds) {
        Map<String, Object> params = initParams(staff);
        if (CollectionUtils.isNotEmpty(exceptIds)) {
            params.put("exceptIds", exceptIds);
        }
        return getSqlMapClientTemplate(staff).queryForList("queryCustomExceptType", params);
    }

    public List<TradeExcept> queryBySids(Staff staff, Long... sids) {
        //必须输入sid
        if (sids == null || sids.length == 0) {
            return new ArrayList<>();
        }
        Map<String, Object> params = initParams(staff);
        params.put("sids", sids);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("query"), params);
    }

    public List<TradeExceptCount> queryCount(Staff staff, List<Long> exceptIds) {
        Map<String, Object> params = initParams(staff);
        if (CollectionUtils.isNotEmpty(exceptIds)) {
            params.put("exceptIds", exceptIds);
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryCount"), params);
    }

    private Map<String, Object> initParams(Staff staff) {
        Map<String, Object> params = new HashMap<>();
        params.put("dbNo", staff.getDbInfo().getTradeExceptDbNo());
        params.put("companyId", staff.getCompanyId());
        params.put("tradeDbNo", staff.getDbInfo().getTradeDbNo());
        return params;
    }



    public List<TradeExceptCount> queryCountStat(Staff staff, Map<String,Object> queryParams) {
        Map<String, Object> params = initParams(staff);
        if(MapUtils.isNotEmpty(queryParams)){
            params.putAll(queryParams);
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryCountStat"), params);
    }

    public List<TradeExceptCount> queryNotConsignCountStat(Staff staff, Map<String,Object> queryParams) {
        Map<String, Object> params = initParams(staff);
        if(MapUtils.isNotEmpty(queryParams)){
            params.putAll(queryParams);
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryNotConsignCountStat"), params);
    }

    public List<TradeExceptCount> queryCountExcept(Staff staff, Map<String,Object> queryParams) {
        Map<String, Object> params = initParams(staff);
        if(MapUtils.isNotEmpty(queryParams)){
            params.putAll(queryParams);
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryCountExcept"), params);
    }

    public List<TradeExceptCount> queryNotConsignCountExcept(Staff staff, Map<String,Object> queryParams) {
        Map<String, Object> params = initParams(staff);
        if(MapUtils.isNotEmpty(queryParams)){
            params.putAll(queryParams);
        }
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryNotConsignCountExcept"), params);
    }


    public List<TradeExceptCount> queryCountPointExcept(Staff staff, List<Long> exceptIds, Map<String,Object> queryParams) {
        Map<String, Object> params = initParams(staff);
        if (CollectionUtils.isEmpty(exceptIds)) {
            return Lists.newArrayList();
        }
        if(MapUtils.isNotEmpty(queryParams)){
            params.putAll(queryParams);
        }
        params.put("exceptIds", exceptIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryCountPointExcept"), params);
    }

    public List<TradeExceptCount> queryNotConsignCountPointExcept(Staff staff, List<Long> exceptIds, Map<String,Object> queryParams) {
        Map<String, Object> params = initParams(staff);
        if (CollectionUtils.isEmpty(exceptIds)) {
            return Lists.newArrayList();
        }
        if(MapUtils.isNotEmpty(queryParams)){
            params.putAll(queryParams);
        }
        params.put("exceptIds", exceptIds);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryNotConsignCountPointExcept"), params);
    }


    public List<TradeExcept> queryRepairMergeRecords(Staff staff, Long ... sids){
        Map<String, Object> params = initParams(staff);
        params.put("sids", sids);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryRepairMergeRecords"), params);
    }


    private void initExceptFied(TradeExcept tradeExcept){
       /*
  `enable_status` tinyint NOT NULL DEFAULT '1',
  `msg` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `is_self` tinyint NOT NULL DEFAULT '1' COMMENT '是否是自己的异常',
  `merge_sid` bigint NOT NULL DEFAULT '-1' COMMENT '合单情况下主单的sid',
  `auto_sign` tinyint NOT NULL DEFAULT '0' COMMENT '0：系统自动标记，1：用户手动标记（重算的时候可能会用到)',
  `is_custom` tinyint NOT NULL DEFAULT '0' COMMENT '0：非自定义异常，1：自定义异常）',
  `priority` int NOT NULL DEFAULT '0' COMMENT '优先级',
  `handled` tinyint NOT NULL DEFAULT '0' COMMENT '0：未处理，1：已手动处理（某些异常处理过之后不用再次标记）',
  `except_source_sid` bigint NOT NULL DEFAULT '-1' COMMENT '异常继承的来源sid,is_self=0非自身异常时才存在',*/

        Integer handled = Optional.ofNullable(tradeExcept.getHandled()).orElse(0);
        tradeExcept.setHandled(handled);
        Integer isSelf = Optional.ofNullable(tradeExcept.getIsSelf()).orElse(1);
        tradeExcept.setIsSelf(isSelf);
        Long mergeSid = Optional.ofNullable(tradeExcept.getMergeSid()).orElse(-1L);
        tradeExcept.setMergeSid(mergeSid);
        Long exceptSourceSid = Optional.ofNullable(tradeExcept.getExceptSourceSid()).orElse(-1L);
        tradeExcept.setExceptSourceSid(exceptSourceSid);
        Integer autoSign = Optional.ofNullable(tradeExcept.getAutoSign()).orElse(0);
        tradeExcept.setAutoSign(autoSign);
        Integer isCustom = Optional.ofNullable(tradeExcept.getIsCustom()).orElse(0);
        tradeExcept.setIsCustom(isCustom);
        Integer priority =  Optional.ofNullable(tradeExcept.getPriority()).orElse(0);
        tradeExcept.setPriority(priority);
    }


}

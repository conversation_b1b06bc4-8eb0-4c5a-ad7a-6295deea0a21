package com.raycloud.dmj.business.operate;

import com.alibaba.fastjson.*;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.audit.help.AuditFxUtils;
import com.raycloud.dmj.dms.domain.dto.TradeAlibabaEcDto;
import com.raycloud.dmj.dms.domain.dto.TradeAlibabaOpTypeEnum;
import com.raycloud.dmj.domain.OrderConstant;
import com.raycloud.dmj.business.fx.FxOperateSyncBusiness;
import com.raycloud.dmj.domain.constant.*;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.trade.history.OrderModifyLogUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.CancelResult;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.fx.*;
import com.raycloud.dmj.business.gift.GiftDeductCountBusiness;
import com.raycloud.dmj.business.order.OrderModifyLogBusiness;
import com.raycloud.dmj.business.part3.TradeParty3Business;
import com.raycloud.dmj.business.trade.SysTradeDmsBusiness;
import com.raycloud.dmj.business.tradepay.TradePayBusiness;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.*;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.ec.AfterArgs;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.enums.EnumOutSidStatus;
import com.raycloud.dmj.domain.pt.model.waybill.bean.WlbResult;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.audit.CancelData;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.newfx.trades.Constants;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.pt.IExpressTemplateCommonService;
import com.raycloud.dmj.services.pt.model.waybill.bean.WlbStatus;
import com.raycloud.dmj.services.trade.label.*;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.trades.support.OrderProductServiceImpl;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.waybill.common.context.PtWaybillPathContext;
import com.raycloud.dmj.waybill.common.params.*;
import com.raycloud.ec.api.*;
import org.apache.commons.collections.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.waybill.common.enums.RecycledPathEnum.*;

/**
 * Created by yangheng on 17/2/27.
 * 作废订单
 */
@Service
public class CancelBusiness {

    private final Logger logger = Logger.getLogger(this.getClass());

    public static final String TRADE_CANCEL_BATCH_EVENT_NAME = "trade.cancel.batch";

    @Resource
    TradeLockBusiness tradeLockBusiness;

    @Resource
    private ILockService lockService;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    IOrderStockService orderStockService;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    ITradePtService tradePtService;

    @Resource
    TradeParty3Business tradeParty3Business;

    @Resource
    private IEventCenter eventCenter;
    @Resource
    private FxBusiness fxBusiness;
    @Resource
    FxCashFlowBusiness fxCashFlowBusiness;

    @Resource
    private IExpressTemplateCommonService expressTemplateCommonService;

    @Resource
    private IProgressService progressService;
    @Resource
    TradePayBusiness tradePayBusiness;
    @Resource
    SysTradeDmsBusiness sysTradeDmsBusiness;

    @Resource
    private ITradeConfigService tradeConfigService;

    @Resource
    ITradeLabelService tradeLabelService;

    @Resource
    OrderModifyLogBusiness orderModifyLogBusiness;

    @Resource
    IStaffService staffService;

    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    FxOperateSyncBusiness fxOperateSyncBusiness;

    @Resource
    TradeTimeoutContextBuilder tradeTimeoutContextBuilder;

    @Resource
    GiftDeductCountBusiness giftDeductCountBusiness;

    private static final int DEFAULT_BATCH_SIZE = 50;

    public void cancelBatch(Staff staff, TradeQueryParams params, ProgressData progressData) {

        params.setPage(new Page(1, 10000))
                .setQueryOrder(false).setIsCancel(0)
                .setFields("t.sid,t.tid,t.user_id,t.template_id,t.template_type,t.is_excep");
        Trades tradeQuery = tradeSearchService.search(staff, params);
        List<Trade> trades = null;
        int tradeSize = 0;
        if (tradeQuery.getTotal() > 0) {
            trades = tradeQuery.getList();
            tradeSize = tradeQuery.getList().size();
        }
        int countCurrent = 0;
        long sucNum = 0, errorNum = 0;

        progressService.updateProgress(staff, ProgressEnum.PROGRESS_CANCEL_BATCH, progressData.setCountAll(tradeSize).setCountCurrent(0).setSucNum(sucNum).setErrorNum(0L));

        int loop = tradeSize / DEFAULT_BATCH_SIZE + ((tradeSize % DEFAULT_BATCH_SIZE == 0) ? 0 : 1);
        for (int i = 0; i < loop; i++) {
            int fromIndex = i * DEFAULT_BATCH_SIZE;
            int endIndex = Math.min((fromIndex + DEFAULT_BATCH_SIZE), tradeSize);
            List<Trade> subTrades = trades.subList(fromIndex, endIndex);
            countCurrent += subTrades.size();
            try {

                CancelData cancelData = CancelData.builder().staff(staff).sidArr(TradeUtils.toSids(subTrades)).gxTrades(null).isCancelAttr(0).ifBatch(true).build();
                CancelResult cancelResult = cancelTrades(cancelData);
                sucNum += cancelResult.getSuccessList().size();
                errorNum += subTrades.size() - cancelResult.getSuccessList().size();
                if (Objects.nonNull(cancelResult.getErrorMap()) && progressData.getErrorMsg().size()<500){
                    for (Map.Entry<String,String> entry:cancelResult.getErrorMap().entrySet()){
                        progressData.getErrorMsg().add(JSON.toJSONString(entry));
                    }
                }
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, String.format("批量作废失败，fromIndex=%s, endIndex=%s, sids=%s", fromIndex, endIndex, TradeUtils.toSidList(subTrades))), e);
                errorNum += subTrades.size();
            } finally {
                if (countCurrent >= tradeSize) {
                    countCurrent = tradeSize - 1;
                }
                progressService.updateProgress(staff, ProgressEnum.PROGRESS_CANCEL_BATCH, progressData.setCountCurrent(countCurrent).setSucNum(sucNum).setErrorNum(errorNum));
            }
        }
        progressService.updateProgress(staff, ProgressEnum.PROGRESS_CANCEL_BATCH, progressData.setCountCurrent(tradeSize - 1));
    }

    public List<Trade> cancel(Staff staff, Long[] sids, List<Trade> gxtrades,int isCancelAttriBute) {
        return cancel(staff, sids, gxtrades, isCancelAttriBute, false);
    }

    public List<Trade> cancel(Staff staff, Long[] sids, List<Trade> gxTrades,int isCancelAttr, boolean batch) {
        CancelData cancelData = CancelData.builder()
                .staff(staff).sidArr(sids)
                .gxTrades(gxTrades).isCancelAttr(isCancelAttr)
                .ifBatch(batch)
                .build();
        return cancel(cancelData);
    }
    public List<Trade> cancel(CancelData cancelData) {
        CancelResult cancelResult = cancelTrades(cancelData);
        return cancelResult.getSuccessList();
    }

    public Map<String, String> checkTradeCancelByFx(Staff staff, Long[] sids, List<Trade> originTrades, int level) {
        if (TradeUtils.checkFxAndGxLevel(level)){
            return new HashMap<>();
        }
        //查询数据
        if (CollectionUtils.isEmpty(originTrades) && null != sids && sids.length != 0){
            originTrades = tradeSearchService.queryBySids(staff, false, sids);
        }
        if (CollectionUtils.isEmpty(originTrades)){
            return new HashMap<>();
        }
        Map<String,String> errorMap = new HashMap<>();
        final int oldLevel = level;
        TradeUtils.groupByDestId(originTrades).forEach((k, v) ->{
            if (null == k || 0 == k){
                return;
            }
            Staff gxStaff = staffService.queryFullByCompanyId(k);
            if (null == gxStaff){
                return;
            }
            String[] gxTids = Arrays.stream(TradeUtils.toSids(v)).map(String::valueOf).toArray(String[]::new);
            //过滤掉已作废的订单
            List<Trade> trade = tradeSearchService.queryByTids(staff, false, gxTids).stream().filter(t ->Integer.valueOf(0).equals(t.getIsCancel())).collect(Collectors.toList());
            //过滤一遍非供销单，奇门供销订单如果新增分销，供销商没有匹配成功，destId可能还是原先的值，即为分销商自己
            List<Trade> gxTrade = trade.stream().filter(TradeUtils::isGxTrade).collect(Collectors.toList());
            List<Trade> gxAndFxTrade = trade.stream().filter(TradeUtils::isGxAndFxTrade).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(gxTrade)){
                CancelResult cancelResult = new CancelResult();
                cancelTrade(staff, gxTrade, true, 0, cancelResult);
                errorMap.putAll(cancelResult.getErrorMap());
            }
            if (CollectionUtils.isNotEmpty(gxAndFxTrade)) {
                CancelResult cancelResult = new CancelResult();
                cancelTrade(staff, gxAndFxTrade, true, 0, cancelResult);
                errorMap.putAll(cancelResult.getErrorMap());
                //多级供分销订单，继续往下校验
                errorMap.putAll(checkTradeCancelByFx(gxStaff, null, gxAndFxTrade, (oldLevel + 1)));
            }
        });
        return errorMap;
    }

    public CancelResult cancelTrades(CancelData cancelData) {
        CancelResult cancelResult = new CancelResult();
        Staff staff = cancelData.getStaff();
        List<Trade> gxtrades = cancelData.getGxTrades();
        int isCancelAttriBute = cancelData.getIsCancelAttr();
        boolean batch = cancelData.isIfBatch();
        String opName = cancelData.getOpName();
        String opMsg  = cancelData.getOpMsg();

        //订单追踪记录
        AfterArgs afterArgs = new AfterArgs();
        if (StringUtils.isNotBlank(opName)) {
            afterArgs.opName=opName;
            afterArgs.opMsg=opMsg;
        }
        handleTradeGxEditNotRelFx(cancelData,cancelResult);
        fxOperateSyncBusiness.cancelTradeFxSync(cancelData, cancelResult);
        Long[] sids = cancelData.getSidArr();
        if (Objects.isNull(sids) || sids.length == 0){
            return cancelResult;
        }
        CancelResult result = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            Map<String, Trade> tradeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(gxtrades)) {
                tradeMap = TradeUtils.toMapByTid(gxtrades);
            }
            //查询数据
            List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            if(cancelData.getOpType()!=null){
                originTrades.forEach(t->t.setCancelFrom(cancelData.getOpType()));
            }
            //校验
            List<Trade> validateSuccess = cancelTrade(staff, originTrades, batch, cancelData.getIsCancelAttr(), cancelResult);
            afterCancelTrade(staff,validateSuccess,cancelData,cancelResult);
            tradeParty3Business.filterCancel(staff, validateSuccess);
            if(CollectionUtils.isEmpty(validateSuccess)){
                cancelResult.getErrorMap().putIfAbsent("","没有数据通过作废前检查");
                return cancelResult;
            }
            String logHeader = LogHelper.buildLogHead(staff).toString();
            //如果是正常订单呢归还库存
            List<Trade> normalTrades = validateSuccess.stream().filter(trade -> !TradeUtils.isFxOrMixTrade(trade)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(normalTrades)) {
                try {
                    logger.debug(new StringBuilder(logHeader).append(String.format("normalTrades=%s", JSON.toJSONString(normalTrades))));
                    logger.debug(new StringBuilder(logHeader).append(String.format("正常订单还库存%s", TradeUtils.toSidList(normalTrades))));
                    orderStockService.resumeTradeStockLocal(staff, normalTrades);
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "订单作废时归还库存报错，订单号：").append(TradeUtils.toSidList(validateSuccess)), e);
                }
            }
            //作废支付单
            try {
                tradePayBusiness.tradePayCancel(staff, validateSuccess);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "订单作废时归作废支付单报错，订单号：").append(TradeUtils.toSidList(validateSuccess)), e);
            }
            //   orderStockService.resumeTradeStockLocal(staff, validateSuccess);
            //创建修改数据
            List<Order> updateOrders = new ArrayList<>();
            if (logger.isDebugEnabled()) {
                logger.debug(new StringBuilder(logHeader).append(String.format("作废订单%s", TradeUtils.toSidList(validateSuccess))));
            }
            List<Trade> updateTrades = createCancelTrades(staff, validateSuccess, updateOrders,tradeMap,isCancelAttriBute);
            tradeLabelService.deleteBySids(staff, TradeUtils.toSidList(updateTrades.stream().filter(t->!t.hasOpV(OpVEnum.TRADE_KEEP_TAG)).collect(Collectors.toList())));
            if (logger.isDebugEnabled()) {
                logger.debug(new StringBuilder(logHeader).append(String.format("作废时更新订单%s", TradeUtils.toSidList(updateTrades))));
            }
            TradeUtils.assemblyBySid(updateTrades,updateOrders);
            TradeEsConTimeUtils.handleTimeoutActionTime(tradeTimeoutContextBuilder.build(staff), updateTrades);

            //更新数据库
            tradePtService.saveByTrades(staff, updateTrades);
            tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
            // tradeExceptAdapter.saveExcept(staff,updateTrades, ExceptSignWayEnum.AUTO.getAutoSign());
            //批次数据处理
            eventCenter.fireEvent(this, new EventInfo(OrderProductServiceImpl.PROGRESS_ORDER_PRODUCT_CANCEL).setArgs(new Object[]{staff.getId(), TradeUtils.toSidList(updateTrades)}), null);
            if (updateTrades.size() > 0 && isCancelAttriBute !=1) {
                eventCenter.fireEvent(this, new EventInfo("trade.cancel").setArgs(new Object[]{staff, TradeUtils.toSids(updateTrades),afterArgs}), updateTrades);
                notifyGx(staff,validateSuccess,updateTrades,cancelData.isCancelFxSync());
                fxBusiness.gxClearOutSid(staff,validateSuccess,updateTrades);
            }

            //奇门分销订单资金流水重算
            List<Trade> qimenFxTradeList = validateSuccess.stream().filter(TradeUtils::isQimenFxSource).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(qimenFxTradeList)) {
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_QIMEN_FX_CASH_FLOW).setArgs(new Object[]{staff, 4, TradeUtils.toSidList(qimenFxTradeList)}), null);
            }
            List<Trade> alibabaFxCancelTrades = validateSuccess.stream()
                    .filter(TradeUtils::isAlibabaFxRoleTrade)
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(alibabaFxCancelTrades)){
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_ALIBABA_FX_CANCEL).setArgs(new Object[]{staff, TradeAlibabaEcDto.builder().sids(TradeUtils.toSidList(alibabaFxCancelTrades)).opTypeEnum(TradeAlibabaOpTypeEnum.TRADE_CANCEL).build()}), null);
            }

            List<Order> giftOrders = updateOrders.stream().filter(Order::isGift).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(giftOrders)) {
                giftDeductCountBusiness.returnGiftCountByOrderIds(staff, giftOrders.stream().map(Order::getId).collect(Collectors.toList()));
            }

            List<Trade> gxTrades = validateSuccess.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(gxTrades) && ConfigHolder.FX_GLOBAL_CONFIG.isUploadFxCommission(staff.getCompanyId())) {
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_COMMISSION_UPLOAD).setArgs(new Object[]{staff, TradeUtils.toSidList(gxTrades)}), null);
            }
            cancelResult.setSuccessList(validateSuccess);
            return cancelResult;

        });
        //供分销业务流水统一处理 -- 供销单、奇门+助手强推订单需要返还流水
        if (isCancelAttriBute != 1 && CollectionUtils.isNotEmpty(result.getSuccessList())) {
            List<Trade> needResumeTrade = result.getSuccessList().stream()
                    .filter(t -> TradeUtils.isGxOrMixTrade(t) || (TradeUtils.isQimenFxSource(t) && t.getIfFxForcePushTrade() && !(TradeUtils.isQimenPayLock(t))))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needResumeTrade)){
                logger.debug(LogHelper.buildLogHead(staff).append(String.format("作废订单时返回流水,sid=%s", TradeUtils.toSidList(needResumeTrade))));
                fxCashFlowBusiness.resumeCashFlow(staff, needResumeTrade, true, tradeLocalConfigurable, cancelData);
            }
        }
        //取消电子面单号
        if (!cancelResult.getSuccessList().isEmpty()) {
            cacelOutSid(staff,cancelResult.getSuccessList());
        }
        return result;
    }

    /**
     * 前提：开启了分销和供销不一一的模式
     *
     * 需要把供销是拆单，tid一样的订单当成一个订单来看
     *
     * 1.供销订单并且校验失败的 && tid一样的都需要校验失败
     * 2.供销订单并且校验成功的 && tid一样的都需要校验成功
     *
     * @param staff
     * @param validateSuccess
     * @param cancelData
     * @param cancelResult
     */
    private void afterCancelTrade(Staff staff, List<Trade> validateSuccess, CancelData cancelData, CancelResult cancelResult) {
        if(!cancelData.isIfDmsTradeGxEditNotRelFx() || CollectionUtils.isEmpty(cancelData.getGxTrades())){
            return;
        }
        FxLogBuilder logBuilder = FxLogBuilder.gx(staff).append("供销订单级联作废:").startWatch();

        Set<String> sids = cancelResult.getErrorMap().keySet();
        // 供销订单并且校验失败的 && 开启了分销和供销不一一的模式 tid一样的都需要校验失败
        Set<String> validateErrorTidSet = cancelData.getGxTrades().stream().filter(t->TradeUtils.isGxTrade(t) && TradeUtils.isSplit(t)  && sids.contains(String.valueOf(t.getSid())))
                .map(Trade::getTid).collect(Collectors.toSet());
        if(CollectionUtils.isNotEmpty(validateErrorTidSet)){
            validateSuccess.removeIf(t->validateErrorTidSet.contains(t.getTid()));
        }

        Set<Long> validateSuccessSidSet = validateSuccess.stream().filter(t->TradeUtils.isGxTrade(t) && TradeUtils.isSplit(t))
                .map(Trade::getSid).collect(Collectors.toSet());
        if(MapUtils.isNotEmpty(cancelData.getGxTidMap()) && CollectionUtils.isNotEmpty(validateSuccessSidSet)){
            cancelData.getGxTidMap().values().forEach(trades -> {
                for (Trade trade : trades) {
                    if(!validateSuccessSidSet.contains(trade.getSid())){
                        validateSuccess.add(trade);
                        validateSuccessSidSet.add(trade.getSid());
                        logBuilder.group(trade.getTid(),trade.getSid());
                    }
                }
            });
        }
        logBuilder.printDebug(logger);
    }


    private void handleTradeGxEditNotRelFx(CancelData cancelData,CancelResult cancelResult) {

        List<Trade> gxTrades = cancelData.getGxTrades();
        // 分销作废触发供销作废是根据tid来查询的。不需要处理供销是否拆单的场景
        if(CollectionUtils.isEmpty(gxTrades)){
            return;
        }

        cancelData.setIfDmsTradeGxEditNotRelFx(TradeConfigGetUtil.get(cancelData.getStaff(), TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen());
        if(!cancelData.isIfDmsTradeGxEditNotRelFx()){// 这里兜底取一次历史开过的公司
            cancelData.setIfDmsTradeGxEditNotRelFx(tradeLocalConfigurable.isTradeGxEditNotRelFxLog(cancelData.getStaff().getCompanyId()));
        }
        cancelResult.setIfDmsTradeGxEditNotRelFx(cancelData.isIfDmsTradeGxEditNotRelFx());
        if(!cancelData.isIfDmsTradeGxEditNotRelFx()){
            return;
        }

        List<Trade> tradeList = tradeSearchService.queryBySplitSids(cancelData.getStaff(), true, gxTrades.stream()
                .filter(tradeSplit -> tradeSplit.getSplitSid() != null && tradeSplit.getSplitSid() > 0)
                .map(Trade::getSplitSid).distinct().toArray(Long[]::new));
        if(CollectionUtils.isEmpty(tradeList)){
            return;
        }
        Map<String, List<Trade>> collect = tradeList.stream().filter(tradeSplit -> !TradeUtils.isCancel(tradeSplit) && tradeSplit.getSplitSid() != null && tradeSplit.getSplitSid() > 0)
                .collect(Collectors.groupingBy(Trade::getTid));

        cancelData.setGxTidMap(collect);

    }


    /**
     * 作废通知供销
     * @param staff
     * @param validateSuccess
     * @param updateTrades
     */
    private void notifyGx(Staff staff,List<Trade> validateSuccess,List<Trade> updateTrades, boolean cancelFxSync) {
        if (cancelFxSync){
            return;
        }
        //通知供销
        Map<Long, Trade> longTradeMap = TradeUtils.toMapBySid(validateSuccess);
        List<Long> longs = TradeUtils.toSidList(updateTrades);
        List<Trade> fxTrades = Lists.newArrayListWithCapacity(longs.size());
        for (Long sid : longs) {
            if (longTradeMap.containsKey(sid)) {
                Trade trade = longTradeMap.get(sid);
                if (TradeUtils.isFxOrMixTrade(trade)) {
                    if (TradeUtils.isMerge(trade)) {
                        if (trade.getSid() - trade.getMergeSid() == 0) {
                            trade.setCancelFrom(1);
                            fxTrades.add(trade);
                        }
                    } else {
                        trade.setCancelFrom(1);
                        fxTrades.add(trade);
                    }
                }
            }
        }
        fxBusiness.notifyGxDownload(staff,fxTrades,0);
    }

    /**
     *  作废取消电子面单号
     * @param staff
     * @param result
     */
    private void cacelOutSid(Staff staff,List<Trade> result) {
        try {
            List<Trade> filterTrades = result.stream().filter(trade -> StringUtils.isNotBlank(trade.getOutSid()) && !trade.hasOpV(OpVEnum.TRADE_GX_EXTEND_OUTSID)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(filterTrades)){
                return;
            }
            long start = System.currentTimeMillis();
            WlbStatus status = expressTemplateCommonService.cancelTidSysTidOutSid(staff, filterTrades, EnumOutSidStatus.BOUND.getValue());

            // 作废订单回收单号记录运单生命周期
            List<WlbResult> errorResults = (List<WlbResult>) status.getErrorResult();
            PtWaybillPathContext ptWaybillPathContext = PtWaybillPathContext.builder()
                    .staff(staff)
                    .pathParam(PathParam.builder().sidAndOutSidMap(filterTrades.stream().collect(Collectors.toMap(Trade::getSid, Trade::getOutSid, (t1, t2) -> t1))).build())
                    .pathResult(PathResult.builder()
                            .errorResults(CollectionUtils.isEmpty(errorResults) ? new ArrayList<>() :
                                    errorResults.stream()
                                            .map(errorResult -> ResultInfo.builder()
                                                    .sid(Long.valueOf(errorResult.getSid()))
                                                    .outSid(errorResult.getOutSid())
                                                    .message(errorResult.getErrorMsg())
                                                    .isSuccess(false)
                                                    .build())
                                            .collect(Collectors.toList()))
                            .build())
                    .waybillPath(CANCEL_RECYCLED)
                    .build();
            eventCenter.fireEvent(this, new EventInfo("waybill.cycle.modify").setArgs(new Object[]{staff, ptWaybillPathContext.getWaybillPath().getName(), ptWaybillPathContext}), null);

            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("作废订单时取消电子面单号,took=%s,result=%s", (System.currentTimeMillis() - start), JSONArray.toJSONString(status))));
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "作废订单时取消电子面单号出错"), e);
        }
    }

    public List<Trade> cancelTrade(Staff staff, List<Trade> trades, boolean batch,Integer isCancelAttr,CancelResult cancelResult) {
        List<Trade> validateSuccess = new ArrayList<>(trades.size());
        Map<String, Trade> gxMapByFxSid = sysTradeDmsBusiness.getGxMapByFxSid(trades);
        Map<Long, List<Trade>> mergeSidMap = new HashMap<>();
        if(cancelResult.isIfDmsTradeGxEditNotRelFx()){
            Map<String, Trade> tidTradeMap = fxBusiness.getFxTradeMapByGx(trades);
            if(MapUtils.isNotEmpty(tidTradeMap)){
                trades.forEach(t-> t.setSourceTrade(tidTradeMap.get(t.getTid())));
            }
        }
        for (Trade trade : trades) {
            mergeSidMap.computeIfAbsent(trade.getMergeSid() > 0 ? trade.getMergeSid() : trade.getSid(), k -> new ArrayList<>()).add(trade);
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        for (Map.Entry<Long, List<Trade>> entry : mergeSidMap.entrySet()) {
            List<Trade> tradeList = entry.getValue();
            // 全部通过，才通过
            boolean isSuccess=true;
            for (Trade trade : tradeList) {
                if (!_checkCancelTrade(staff, trade, batch, isCancelAttr, cancelResult, gxMapByFxSid, mergeSidMap, tradeConfig)) {
                    isSuccess = false;
                }
            }
            // 全部通过，才通过
            if (isSuccess) {
                validateSuccess.addAll(tradeList);
            }
        }
        return validateSuccess;
    }

    private boolean _checkCancelTrade(Staff staff,Trade trade,boolean batch,Integer isCancelAttr,CancelResult cancelResult,Map<String, Trade> gxMapByFxSid,Map<Long, List<Trade>> mergeSidMap,TradeConfig tradeConfig){
        // KMERP-265218
        if (PlatformUtils.isFxgWaitGxConsign(staff, trade)) {
            cancelResult.getErrorMap().putIfAbsent(trade.getSid().toString(),String.format("系统订单号=%s FXG待供销商发货订单无需再作废",trade.getSid()));
            return false;
        }
        //已作废的订单无需再作废
        if (trade.getIsCancel() == 1) {
            cancelResult.getErrorMap().putIfAbsent(trade.getSid().toString(),String.format("系统订单号=%s 已作废的订单无需再作废",trade.getSid()));
            return false;
        }
        if(!TradeUtils.isGxOrMixTrade(trade)) {
            if (staff.getUserIdMap() != null && staff.getUserIdMap().get(trade.getUserId()) == null) {
                logger.warn(LogHelper.buildLog(staff, String.format("您无权操作此订单[sid=%s]!", trade.getSid())));
                cancelResult.getErrorMap().putIfAbsent(trade.getSid().toString(),String.format("系统订单号=%s 您无权操作此订单(店铺权限)",trade.getSid()));
                return false;
            }
        }
        if(TradeUtils.isGxOrMixTrade(trade) && TradeUtils.isSplit(trade)){
            if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus()) && TradeUtils.getOrders4Trade(trade).stream().noneMatch(OrderUtils::isOtherErpConsigned)) {
                logger.warn(LogHelper.buildLog(staff, String.format("系统状态为发货后，不能作废[sid=%s]", trade.getSid())));
                cancelResult.getErrorMap().putIfAbsent(trade.getSid().toString(),String.format("系统订单号=%s 系统状态为发货后，不能作废",trade.getSid()));
                return false;
            }
        } else {
            if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
                logger.warn(LogHelper.buildLog(staff, String.format("系统状态为发货后，不能作废[sid=%s]", trade.getSid())));
                cancelResult.getErrorMap().putIfAbsent(trade.getSid().toString(),String.format("系统订单号=%s 系统状态为发货后，不能作废",trade.getSid()));
                return false;
            }
        }

        int isAllow = tradeConfig.getInteger(NewTradeExtendConfigEnum.ALLOW_BATCH_CANCEL_UPLOADED.getKey());
        if (batch && null != trade.getIsUpload() && 1 == trade.getIsUpload() && !TradeUtils.isGxTrade(trade) && !(isAllow == 1 && TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_LAZADA))) {
            logger.info(LogHelper.buildLog(staff, String.format("预发货订单不能作废[sid=%s]", trade.getSid())));
            cancelResult.getErrorMap().putIfAbsent(trade.getSid().toString(),String.format("系统订单号=%s 预发货订单不能作废",trade.getSid()));
            return false;
        }

        if (TradeUtils.isFxTrade(trade) && !MapUtils.isEmpty(gxMapByFxSid) && AuditFxUtils.checkGxExcept(staff,gxMapByFxSid.get(sysTradeDmsBusiness.getMapKeyByFx(trade)), trade)) {
            logger.info(LogHelper.buildLog(staff, String.format("关联供销单发货/上传异常不能作废[sid=%s]",trade.getSid())));
            cancelResult.getErrorMap().putIfAbsent(trade.getSid().toString(),String.format("系统订单号=%s 关联供销单发货/上传异常不能作废",trade.getSid()));
            return false;
        }
        if (TradeUtils.isFxOrMixTrade(trade) && ((null != trade.getIsUpload() && 1 == trade.getIsUpload() && !Objects.equals(isCancelAttr, 1)) || (TradeUtils.isMerge(trade) && mergeSidMap.get(trade.getMergeSid()).stream().anyMatch(t -> Objects.equals(1, t.getIsUpload()))) && !Objects.equals(isCancelAttr, 1))) {
            logger.info(LogHelper.buildLog(staff, String.format("预发货订单不能作废[sid=%s]", trade.getSid())));
            cancelResult.getErrorMap().putIfAbsent(trade.getSid().toString(), String.format("供销订单已操作了预发货，不能作废[sid=%s]", trade.getSid()));
            return false;
        }
        return true;
    }


    /**
     * 作废订单
     */
    public List<Trade> createCancelTrades(Staff staff, List<Trade> originTrades, List<Order> updateOrders,Map<String, Trade> tradeMap,int isCancelAttriBute) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        Map<Long, List<OrderModifyLog>> orderIdLogsMap = OrderModifyLogUtils.groupOrderId(staff, orderModifyLogBusiness.query(staff, OrderUtils.toIdList(TradeUtils.getOrders4Trade(originTrades)), OrderModifyLogTypeEnum.SUIT_REPLACE.getType()));

        List<Trade> updateTrades = new ArrayList<>(originTrades.size());
        List<ExceptEnum> exceptEnums=new ArrayList<>();
        exceptEnums.add(ExceptEnum.ITEM_CHANGED);
        exceptEnums.add(ExceptEnum.RELATION_CHANGED);
        for (Trade originTrade : originTrades) {
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(originTrade);
            // 原单的异常分销商待付款
            boolean hasFxWaitPayExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.FX_WAITPAY);
            boolean hasFxUnAudit = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.FX_UNAUDIT);
            boolean hasGxItemChangeExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
            createCancelOrders(staff,originTrade, orders4Trade, updateOrders,isCancelAttriBute, orderIdLogsMap);
            // createCancelOrders 内部 order 的stock_status 做过重新赋值，需要重新计算trade的stock_status
            TradeStockUtils.resetTradeStockStatus(staff,originTrade,tradeConfig);
            Trade update =  TradeBuilderUtils.builderUpdateTrade(originTrade);
            update.setSid(originTrade.getSid());
            update.setTid(originTrade.getTid());
            update.setMergeSid(originTrade.getMergeSid());
            update.setCompanyId(originTrade.getCompanyId());
            update.setWarehouseId(originTrade.getWarehouseId());
            update.setEnableStatus(originTrade.getEnableStatus());
            // order存在的并处理过的异常，trade需要重新计算
            for (ExceptEnum exceptEnum : exceptEnums) {
                TradeExceptUtils.resetTradeExcept(staff, originTrade, exceptEnum);
                TradeExceptUtils.syncTradeExcept(staff, update, originTrade, exceptEnum);
            }

            if (TradeUtils.isFxOrMixTrade(originTrade)) {
                //取消分销属性作废
                if (isCancelAttriBute == 1) {
                    // update.setStockStatus(Trade.STOCK_STATUS_EMPTY);
                    update.setDestId(0L);
                    update.setV(originTrade.getV());
                    TradeUtils.setSourceId(update,0L);
                    update.setConvertType(0);
                    update.setBelongType(0);
                    if(TradeUtils.isQimenAndNotFx(update)){
                        update.setConvertType(3);
                        update.setBelongType(2);
                    }
                    TradeExceptUtils.clearExcep(staff,update);
                    update.removeV(16);
                    update.removeV(TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID);
                    TradeExceptUtils.updateExcept(staff,update,ExceptEnum.FX_AMBIGUITY,0L);
                    // 奇门订单保留分销商待付款异常
                    if (TradeUtils.isQimenFxSource(originTrade) && hasFxWaitPayExcept) {
                        TradeExceptUtils.updateExcept(staff, update, ExceptEnum.FX_WAITPAY, 1L);
                    }
                    update.setWarehouseId(null);
                } else {
                    // update.setStockStatus(originTrade.getStockStatus());
                    update.setDestId(originTrade.getDestId());
                    update.setConvertType(originTrade.getConvertType());
                    update.setBelongType(originTrade.getBelongType());
                    update.setSourceId(originTrade.getSourceId());
                }
            }

            // 商品未匹配属于itemExcep放在 TradeExceptUtils.setStockStatus 之前啊
            update.setItemExcep(0);
            TradeExceptUtils.clearItemExcep(staff,update);

            // trade 的stock_status 的值根据order来计算TradeStockUtils.resetTradeStockStatus
            TradeExceptUtils.setStockStatus(staff,update,originTrade.getStockStatus());
            update.setInsufficientNum(0);
            update.setInsufficientRate(0D);
            update.setIsCancel(1);
            TradeExceptUtils.updateExcept(staff,update, ExceptEnum.HALT,0L);
            update.setIsExcep(0);
            TradeExceptUtils.updateExcept(staff,update, ExceptEnum.UNATTAINABLE,0L);

            //业务的原始数据，获取错误信息与作废来源
            Trade trade = tradeMap.get(originTrade.getTid());
            if (trade != null){
                update.setCancelFrom(trade.getCancelFrom());
                update.setOperations(trade.getOperations());
                TradeExt tradeExt = trade.getTradeExt();
                if(TagUtils.isMatchTargetTag(trade, SystemTags.TAG_PRE_UPLOAD_CONSIGN.getId().toString())){
                    TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.PRE_UPLOAD_CONSIGN_FLAG.getField(), 1);
                    if(tradeExt!=null){
                        update.setTradeExt(tradeExt);
                    }
                }
            }
            //合单订单作废需要取消合并
            if (originTrade.getMergeSid() != null && originTrade.getMergeSid() > 0) {
                update.setMergeSid(-1L);
                update.setMergeType(TradeMergeEnum.MERGE_NORMAL.getDbType());
                update.setEnableStatus(1);
                if (originTrade.getSid() - originTrade.getMergeSid() == 0) {
                    TradeUtils.resetTradeItemNum(originTrade, tradeConfig);
                    update.setItemKindNum(originTrade.getItemKindNum());
                    update.setItemNum(originTrade.getItemNum());
                    update.setSingleItemKindNum(originTrade.getSingleItemKindNum());
                    update.setNetWeight(TradeUtils.calculateTradeNetWeight(originTrade));
                }
            }

            //作废时取消标签与自定义异常
            // 供销打回且预发货过 || 供销订单商品信息发生变更  触发的作废不清空标签
            if(!(TradeUtils.isGxTrade(originTrade) && ((Objects.equals(update.getCancelFrom(),2) &&
                    TradeTagUtils.checkIfExistTag(originTrade, SystemTags.TAG_PRE_UPLOAD_CONSIGN)) || Objects.equals(originTrade.getCancelFrom(),10)))){
                update.setTagIds("");
            }else {
                update.addOpV(OpVEnum.TRADE_KEEP_TAG);
                update.addOpV(OpVEnum.TRADE_GX_EXTEND_OUTSID);
                originTrade.addOpV(OpVEnum.TRADE_GX_EXTEND_OUTSID);
            }
            //  update.setExceptIds("");
            TradeExceptUtils.clearCustomExcept(staff,update);
            if (TradeUtils.isFxOrMixTrade(originTrade) && isCancelAttriBute == 1) {
                if (hasFxWaitPayExcept) {
                    TradeExceptUtils.updateExcept(staff, update, ExceptEnum.FX_WAITPAY, 1L);
                }
                if (hasFxUnAudit) {
                    TradeExceptUtils.updateExcept(staff, update, ExceptEnum.FX_UNAUDIT, 1L);
                    Logs.debug(LogHelper.buildLog(staff,String.format("sid=%s标记[%s]异常",trade.getSid(),ExceptEnum.FX_UNAUDIT.getChinese())));
                }
            }
            if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(originTrade.getSource()) && !CommonConstants.PLAT_FORM_TYPE_SELF_BUILT.equals(originTrade.getSource())) {
                update.setSysStatus(Trade.SYS_STATUS_WAIT_BUYER_PAY);
            }
            //未发货的平台订单，需要重置部分信息
            //lazada不清空运单号及快递模板
            // 供销打回且预发货过 || 供销订单商品信息发生变更 -->不清空
            if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(originTrade.getSource())
                    && !CommonConstants.PLAT_FORM_TYPE_SUMAITONG.equals(originTrade.getSource())
                    && !CommonConstants.PLAT_FORM_TYPE_LAZADA.equals(originTrade.getSource())
                    && !(CommonConstants.PLAT_FORM_TYPE_POISON.equals(originTrade.getSource()) && TradeTypeConstants.POISON_BRAND_DELIVER_TRADE_TYPE.equals(originTrade.getType()))
                    && !(TradeUtils.isGxTrade(originTrade) && ((Objects.equals(update.getCancelFrom(),2) && TradeTagUtils.checkIfExistTag(originTrade, SystemTags.TAG_PRE_UPLOAD_CONSIGN)) || Objects.equals(originTrade.getCancelFrom(),10)))
                    && !TradeStatusUtils.isAfterSendGoods(originTrade.getSysStatus())
            ) {
                update.setTemplateId(-1L);
                update.setLogisticsCompanyId(0L);
                update.setTemplateType(0);
                update.setOutSid("");
            }
            TradeExceptUtils.updateExcept(staff, update, ExceptEnum.GX_ITEM_CHANGE_EXCEPT, hasGxItemChangeExcept);
            updateTrades.add(update);
        }
        return updateTrades;
    }

    private void createCancelOrders(Staff staff,Trade originTrade,List<Order> orders, List<Order> updateOrders,int isCancelAttriBute, Map<Long, List<OrderModifyLog>> orderIdLogsMap) {
        for (Order originOrder : orders) {
            if (TradeStatusUtils.isAfterSendGoods(originOrder.getSysStatus())) {//已发货已完成已关闭的子订单仅修改is_cancel
                cancelOrder(originOrder, updateOrders);
                continue;
            }
            buildUpdateOrder(staff,originTrade,originOrder, updateOrders,isCancelAttriBute, orderIdLogsMap);
        }
    }

    private void buildUpdateOrder(Staff staff,Trade originTrade,Order order, List<Order> updateOrders,int isCancelAttriBute, Map<Long, List<OrderModifyLog>> orderIdLogsMap) {
        Order update = OrderBuilderUtils.builderUpdateOrderWithV(order);
        updateOrders.add(update);
        update.setId(order.getId());
        update.setExceptData(order.getExceptData());
        update.setSysItemChanged(0);
        update.setInsufficientCanceled(0);
        update.setIsCancel(1);
        update.setGiftNum(order.getGiftNum());
        update.setCustomGiftType(order.getCustomGiftType());
        update.setTid(order.getTid());
        update.setSid(order.getSid());

        if (OrderUtils.isFxOrder(order)) {
            if (isCancelAttriBute == 1) {
                update.setStockNum(0);
                update.setSourceId(0L);
                update.setDestId(0L);
                update.setConvertType(0);
                update.setBelongType(0);
                if(TradeUtils.isQimenFxSource(originTrade)){
                    update.setConvertType(3);
                    update.setBelongType(2);
                }
                update.removeV(OrderConstant.V_KMT_DF);
            } else {
                update.setStockNum(order.getNum());
            }
        }
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())
                || CommonConstants.PLAT_FORM_TYPE_SELF_BUILT.equals(order.getSource())
                || OrderModifyLogUtils.containComplexParseLog(orderIdLogsMap, order)
                || Constants.FxDefaultSysNumId.equals(order.getItemSysId())) {
            update.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
            if (OrderUtils.isFxOrder(order)) {
                OrderExceptUtils.setStockStatus(staff,update,Trade.STOCK_STATUS_NORMAL);
                OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_NORMAL);
            } else {
                OrderExceptUtils.setStockStatus(staff,update,Trade.STOCK_STATUS_EMPTY);
                OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_EMPTY);
            }
            if (order.getSuits() != null) {
                for (Order son : order.getSuits()) {
                    buildUpdateOrder(staff,originTrade,son, updateOrders, isCancelAttriBute, orderIdLogsMap);
                }
            }
        } else {//未发货的平台子订单，需要重置部分信息
            if (order.isSuit(true) || order.isProcess(true) || order.isGroup(true)) {
                update.setEnableStatus(0);//删除套件或加工商品的单品
                return;
            }
            update.setSysStatus(Trade.SYS_STATUS_WAIT_BUYER_PAY);
            update.setItemSysId(-1L);
            update.setSkuSysId(-1L);
            if (OrderUtils.isFxOrder(order) && isCancelAttriBute != 1) {
                OrderExceptUtils.setStockStatus(staff,update,Trade.STOCK_STATUS_NORMAL);
                OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_NORMAL);
            } else {
                OrderExceptUtils.setStockStatus(staff,update,Trade.STOCK_STATUS_UNALLOCATED);
                OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_UNALLOCATED);
            }
            OrderExceptUtils.updateExceptOrder(staff,order,ExceptEnum.ITEM_CHANGED,0L);
            OrderExceptUtils.updateExceptOrder(staff,update,ExceptEnum.ITEM_CHANGED,0L);
            OrderExceptUtils.updateExceptOrder(staff,order,ExceptEnum.RELATION_CHANGED,0L);
            OrderExceptUtils.updateExceptOrder(staff,update,ExceptEnum.RELATION_CHANGED,0L);
            OrderExceptUtils.syncOrderExcept(staff,update,order,ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
            if (order.getSuits() != null) {
                update.setType(Order.TypeOfNormal);//套件本身变为非套件
                for (Order son : order.getSuits()) {
                    buildUpdateOrder(staff,originTrade,son, updateOrders,isCancelAttriBute, orderIdLogsMap);
                }
            }
        }
    }

    private void cancelOrder(Order order, List<Order> updateOrders) {
        Order update = OrderBuilderUtils.builderUpdateOrder(order);
        updateOrders.add(update);
        update.setId(order.getId());
        update.setIsCancel(1);
        update.setTid(order.getTid());
        update.setSid(order.getSid());
        if (update.getSuits() != null) {
            for (Order son : order.getSuits()) {
                cancelOrder(son, updateOrders);
            }
        }
    }
}

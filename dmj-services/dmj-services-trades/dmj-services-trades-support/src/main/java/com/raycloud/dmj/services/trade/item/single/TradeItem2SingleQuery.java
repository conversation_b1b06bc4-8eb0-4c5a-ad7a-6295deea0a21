package com.raycloud.dmj.services.trade.item.single;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.services.trade.item.common.TradeItemQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/17
 * @description 订单商品查询
 */
@Service
public class TradeItem2SingleQuery {

    @Resource
    TradeItemQuery tradeItemQuery;

    @Resource
    TradeItem2SingleFill tradeItem2SingleFill;

    @Resource
    TradeItem2SingleFilter tradeItem2SingleFilter;

    public List<Trade> query(Staff staff, TradeItemContext itemContext, Long[] sids) {
        return fillAndFilter(staff, itemContext, tradeItemQuery.query(staff, sids));
    }

    public List<Trade> query(Staff staff, TradeItemContext itemContext, TradeQueryParams params) {
        return fillAndFilter(staff, itemContext, tradeItemQuery.query(staff, params));
    }

    public List<Trade> fillAndFilter(Staff staff, TradeItemContext itemContext, List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            tradeItem2SingleFill.fill(staff, itemContext, trades);
            tradeItem2SingleFilter.filter(staff, itemContext, trades);
        }
        return trades;
    }

}

package com.raycloud.dmj.services.trade.item.formula;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.item.TradeItemConfigUtils;
import com.raycloud.dmj.domain.trade.common.TradeBusinessUtils;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description 按公式替换商品
 */
@Service
public class TradeItemFormula {

    @Resource
    TradeItemFormulaQuery tradeItemFormulaQuery;

    public void formula(Staff staff, TradeItemContext itemContext, Long[] sids) {
        doFormula(staff, itemContext, tradeItemFormulaQuery.query(staff, sids));
    }

    public void formula(Staff staff, TradeItemContext itemContext, TradeQueryParams params) {
        List<Trade> trades = tradeItemFormulaQuery.query(staff, params);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        if (trades.size() < 1000) {
            doFormula(staff, itemContext, trades);
            return;
        }
        List<List<Trade>> groupTrades = TradeBusinessUtils.groupTradeByWarehouseId(staff, trades, TradeItemConfigUtils.getTradeItemBatchSize(staff.getCompanyId()));
        for (List<Trade> groupTrade : groupTrades) {
            doFormula(staff, itemContext, groupTrade);
        }
    }

    private void doFormula(Staff staff, TradeItemContext itemContext, List<Trade> trades) {
        //TODO
    }
}

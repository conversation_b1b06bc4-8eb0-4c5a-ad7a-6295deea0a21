package com.raycloud.dmj.utils.spider;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.business.common.SimpleTrade;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.tag.TradeTagRule;
import com.raycloud.dmj.domain.trade.spider.SpiderUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * 双地址spider
 */
public abstract class SpiderDifferentAddressUtils {
    public static void addResult(Staff staff, String msg) {
        SpiderUtils.addResult(staff, msg);
    }

    public static void addResult(SimpleTrade st, boolean matched, String reason) {
        if (SpiderUtils.isSpider()) {
            String msg = "表达式" + (matched ? "匹配成功" : "匹配失败") +
                    "订单sid=[" + st.getSid() + "]," +
                    "原因=[" + reason + "],";
            SpiderUtils.addResult(msg);
        }
    }

    public static void addResult(String companyId, SimpleTrade st, String ruleId, boolean matched, String reason) {
        if (SpiderUtils.isSpider(companyId)) {
            String msg = "表达式" + (matched ? "匹配成功" : "匹配失败") +
                    "订单sid=[" + st.getSid() + "]," +
                    "原因=[" + reason + "]," +
                    "规则id=[" + ruleId + "],";
            SpiderUtils.addResult(companyId, msg);
        }
    }

    public static void addResult(Staff staff, List<TradeTagRule> matchRules) {
        if (SpiderUtils.isSpider(staff) && CollectionUtils.isNotEmpty(matchRules)) {
            StringBuilder msg = new StringBuilder("匹配成功:");
            for (TradeTagRule rule : matchRules) {
                msg.append("规则id=" + rule.getId() + ",name=" + rule.getName() + ";");
            }
            SpiderUtils.addResult(staff, msg.toString());
        }
    }

    private static String getLogRule(TradeTagRule rule) {
        return "id=" + rule.getId() + ",名称=" + rule.getName() + ",优先级=" + rule.getPriority();
    }

}

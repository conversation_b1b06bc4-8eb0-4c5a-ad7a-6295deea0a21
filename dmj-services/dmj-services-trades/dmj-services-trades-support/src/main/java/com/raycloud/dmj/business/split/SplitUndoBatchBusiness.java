package com.raycloud.dmj.business.split;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.ThreadPoolBusiness;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.split.support.*;
import com.raycloud.dmj.business.stock.TradeConfigStockBusiness;
import com.raycloud.dmj.business.trade.RecordQueryBusiness;
import com.raycloud.dmj.business.trade.ShopeeTradeSplitBusiness;
import com.raycloud.dmj.business.trade.StockSplitBusiness;
import com.raycloud.dmj.business.trade.TradeStatBusiness;
import com.raycloud.dmj.business.tradepay.TradePayBusiness;
import com.raycloud.dmj.common.enums.SaleListenerEnums;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.ERPLock;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.tradepay.params.TradePayUpdateParams;
import com.raycloud.dmj.domain.trades.utils.TradeSalesmanUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.trade.order.IOrderCaigouService;
import com.raycloud.dmj.services.trade.split.ITradeSplitService;
import com.raycloud.dmj.services.trade.supplier.IOrderSupplierService;
import com.raycloud.dmj.services.tradepay.ITradePayService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.fill.ITradeFillService;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量取消拆单
 */
@Service
public class SplitUndoBatchBusiness extends ThreadPoolBusiness<Trade> {
    @Resource
    ILockService lockService;

    @Resource
    TbTradeDao tbTradeDao;

    @Resource
    ITradeFillService tradeFillService;

    @Resource
    TradeConfigStockBusiness tradeConfigStockBusiness;

    @Resource
    ShopeeTradeSplitBusiness shopeeTradeSplitBusiness;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    StockSplitBusiness stockSplitBusiness;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    IEventCenter eventCenter;

    @Resource
    SplitUndoBusiness splitUndoBusiness;

    @Resource
    TradePayBusiness tradePayBusiness;

    @Resource
    ITradePayService tradePayService;

    @Resource
    IOrderSupplierService orderSupplierService;

    @Resource
    TradeStatBusiness tradeStatBusiness;

    @Resource
    RecordQueryBusiness recordQueryBusiness;

    @Resource
    ITradeSalesmanService tradeSalesManService;

    @Resource
    IOrderCaigouService orderCaigouService;

    @Resource
    IProgressService progressService;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    ITradeSplitService tradeSplitService;

    @Resource
    TradeSysProductTimeoutActionTimeService tradeSysProductTimeoutActionTimeService;

    @Resource
    TradeTimeoutContextBuilder tradeTimeoutContextBuilder;


    public void splitUndoBatch(Staff staff, TradeQueryParams params, ProgressData progressData) {

        Trades tradePages = tradeSearchService.search(staff, params.setQueryOrder(false).setFields("t.sid, t.tid, t.split_sid, t.split_type").setPage(new Page(1, 100000)));
        if (CollectionUtils.isEmpty(tradePages.getList())) {
            progressService.updateProgressComplete(staff, ProgressEnum.PROGRESS_SPLIT_UNDO, progressData);
            return;
        }
        run(staff, tradePages.getList(), new AbstractBusiness() {
            @Override
            public void doBusiness(List<Trade> data) {
                List<ERPLock> undoTradeLocks = getUndoTradeLocks(staff, data);
                if (CollectionUtils.isEmpty(undoTradeLocks)) {
                    return;
                }
                
                lockService.locks(undoTradeLocks, () -> {
                    try {
                        TradeConfig tradeConfig = tradeConfigService.get(staff);
                        SplitUndoData splitUndoData = undoTradeBefore(staff, data, tradeConfig);
                        if (splitUndoData == null) {
                            return null;
                        }
                        undoTrade(staff, splitUndoData, tradeConfig);
                        undoTradeAfter(staff, splitUndoData, tradeConfig);

                        progressData.setErrorNum(progressData.getAtomicErrorNum().addAndGet(splitUndoData.getErrorMessages().size()));
                        splitUndoData.getErrorMessages().forEach((k, v) -> progressData.getErrorMsg().add(k + "_" + v));
                        progressData.setSucNum(progressData.getAtomicSucNum().addAndGet(splitUndoData.getSplitUndoDataList().size()));
                    } catch (Exception e) {
                        progressData.setErrorNum(progressData.getAtomicErrorNum().addAndGet(tradePages.getList().size())).getErrorMsg().add("1" + "_" + e.getMessage());
                    } finally {
                        progressService.updateProgress(staff, ProgressEnum.PROGRESS_SPLIT_UNDO, progressData);
                    }
                    return null;
                });
            }
        });
    }

    @Override
    protected List<List<Trade>> groupData(Staff staff, List<Trade> data) {
        return ListUtils.splitList(data, 200);
    }

    /**
     * 前置处理
     */
    private SplitUndoData undoTradeBefore(Staff staff, List<Trade> trades, TradeConfig tradeConfig) {
        SplitUndoData splitUndoData = new SplitUndoData();
        Long[] sids = trades.stream().map(Trade::getSplitSid).filter(splitSid -> splitSid > 0L).toArray(Long[]::new);
        List<TbTrade> originTrades = querySplitTrades(staff, sids);
        if (CollectionUtils.isEmpty(originTrades)) {
            return null;
        }
        //填充订单信息
        tradeFillService.fill(staff, originTrades);

        //初始化SplitUndoData
        initSplitUndoData(staff, splitUndoData, originTrades);

        List<Order> originOrders = new ArrayList<>();
        splitUndoData.getSplitUndoDataList().forEach(groupData -> {
            originOrders.addAll(groupData.getOriginOrders());
            // 取消拆分，提前处理还库存
            tradeConfigStockBusiness.resumeUndoSplitTradeStock(staff, groupData.getUndoSplitTrades(), groupData.getOriginOrders());
        });
        //填充库存信息
        recordQueryBusiness.fillRealStockNum(staff, originOrders, tradeConfig);

        return splitUndoData;
    }

    private void initSplitUndoData(Staff staff, SplitUndoData splitUndoData, List<TbTrade> originTrades) {
        //校验过滤订单
        prefilter(staff, originTrades, splitUndoData);
        List<Trade> originSplitTrades = new ArrayList<>();
        splitUndoData.addOriginTrades(originTrades).addUndoSplitTrades(originSplitTrades);
        splitUndoData.getSplitUndoDataList().forEach(groupData -> originSplitTrades.addAll(groupData.getUndoSplitTrades()));
        //填充order到splitUndoData
        fillOrder(staff, splitUndoData, originSplitTrades);
        // 无需发货拆单校验
        nonConsignFilter(splitUndoData);
    }

    /**
     * 取消拆单
     */
    private void undoTrade(Staff staff, SplitUndoData splitUndoData, TradeConfig tradeConfig) {
        //虾皮取消
        shopeeTradeSplitBusiness.undoSplit(staff, splitUndoData.getOriginTrades(), true);

        SplitUpdateData splitUpdateData = splitUndoData.getSplitUpdateData();
        SplitStockData splitStockData = splitUndoData.getSplitStockData();

        splitUndoData.getSplitUndoDataList().forEach(groupData -> groupData.setMainTrade(SplitUndoUtils.splitUndo(staff, splitUpdateData, splitStockData, groupData.getOriginTrades(), groupData.getUndoSplitTrades(), groupData.getOriginOrders(), tradeConfig, tradeTimeoutContextBuilder.build(staff))));
        splitUndoData.getSplitUndoDataList().forEach(groupData -> tradeSysProductTimeoutActionTimeService.recalculationAfterUndoSplitting(staff, groupData.getMainTrade(), groupData.getOriginTrades(), splitUpdateData));
        if (!CollectionUtils.isEmpty(splitStockData.getOriginRecordMap())) {
            stockSplitBusiness.split(staff, splitStockData.getOriginRecords(), splitStockData.getNewRecords(), TradeSplitEnum.SPLIT_UNDO.getEventName());
        }
        if (!splitUpdateData.hasData()) {
            splitUndoData.getUndoSplitTrades().forEach(trade -> splitUndoData.getErrorMessages().put(trade.getSid(), "数据已过期，刷新后重试"));
            return;
        }

        splitUndoBusiness.recalculationSubSource(splitUpdateData);
        //计算重量
        splitUndoBusiness.handleCoverWeight(staff, splitUpdateData.getUpdateTrades(), splitUpdateData.insertTrades);
        //更新订单
        tradeUpdateService.updateTrades(staff, splitUpdateData.getUpdateTrades(), splitUpdateData.getUpdateOrders(), splitUpdateData.insertTrades, splitUpdateData.insertOrders, false, false);
        //更新orderExt
        splitUndoBusiness.updateOrderExt(staff, splitUpdateData.updateOrderExt);

        Map<Long, List<Long>> dangkouMap = splitUndoData.getDangkouSplitSidMap();
        if (!dangkouMap.isEmpty()) {    //档口订单取消拆分处理锁定金额
            eventCenter.fireEvent(this, new EventInfo(SaleListenerEnums.SALE_DMS_LOCK_BALANCE).setArgs(new Object[]{staff, OpEnum.SPLIT_UNDO, Lists.newArrayList(dangkouMap.keySet()), dangkouMap}), null);
        }
    }

    /**
     * 后置处理
     */
    @Transactional
    public void undoTradeAfter(Staff staff, SplitUndoData splitUndoData, TradeConfig tradeConfig) {
        SplitUpdateData splitUpdateData = splitUndoData.getSplitUpdateData();
        eventCenter.fireEvent(this.getClass(), new EventInfo(TradeSplitEnum.SPLIT_UNDO.getEventName()).setArgs(new Object[]{staff}), splitUpdateData.getSplitTrades());
        List<Trade> updateTrades = splitUpdateData.getUpdateTrades();

        // 把取消拆单后的分销单，推送给供销-->供销需要反审核，因为分销单子单已经设置enable_status=0
        fxCancel(staff, updateTrades);

        List<Trade> mainTrades = splitUndoData.getSplitUndoDataList().stream().map(SplitUndoData.GroupData::getMainTrade).filter(Objects::nonNull).collect(Collectors.toList());

        //更新支付单
        tradePaySplitUndo(staff, splitUndoData);

        // 重新计算供应商信息
        orderSupplierService.sync(staff, null, TradeUtils.getOrders4Trade(mainTrades));

        // 删除业务员信息
        hanleTradeSalesMan(staff, splitUndoData);

        // 同步采购单
        orderCaigouService.sync(staff, TradeUtils.getOrders4Trade(mainTrades), null);

        //处理预售订单
        splitUndoBusiness.updatePlatformPresell(staff, mainTrades);

        //处理库存
        refundChangeStock(splitUndoData, staff);

        //异步删除 平台拆单类型标记
        removeSplitPlat(splitUndoData, staff);

        // 取消拆单 根据拆单配置 重新匹配仓库，赠品，快递
        tradeSplitService.splitUndoRecalculationUpdate(staff, mainTrades);
    }

    private void hanleTradeSalesMan(Staff staff, SplitUndoData splitUndoData) {
        // 非主单的业务员记录都要删除
        List<Trade> tradeList = new ArrayList<>();
        splitUndoData.getSplitUndoDataList().forEach(data -> {
            // 非主单的业务员记录都要删除
            data.getUndoSplitTrades().stream().filter(a -> a.getSid() - data.getMainTrade().getSid() != 0).forEach(tradeList::add);

        });
        tradeSalesManService.saveTradeSalesman(staff, TradeSalesmanUtils.buildSalesmanList(staff, null, tradeList));
    }

    private void tradePaySplitUndo(Staff staff, SplitUndoData splitUndoData) {
        if (!tradePayBusiness.isOpenTradePay(staff)) return;
        TradePayUpdateParams tradePayUpdateParams = new TradePayUpdateParams();
        List<Long> sidsAll = new ArrayList<>();
        splitUndoData.getSplitUndoDataList().forEach(groupData -> {
            List<Long> sids = groupData.getUndoSplitTrades().stream().map(Trade::getSid).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(sids)){
               return;
            }
            tradePayUpdateParams.setSids(sids);
            tradePayUpdateParams.setUpdateSid(groupData.getMainTrade().getSid());
            tradePayService.tradePayUpdate(staff, tradePayUpdateParams);
            sidsAll.addAll(sids);
        });

        tradePayBusiness.updateTradeAcPayment(staff, sidsAll);
    }

    /**
     * 异步删除 平台拆单类型标记
     */
    private void removeSplitPlat(SplitUndoData splitUndoData, Staff staff) {
        List<Trade> originTrades = new ArrayList<>();
        splitUndoData.getSplitUndoDataList().forEach(groupData -> originTrades.addAll(groupData.getOriginTrades()));
        eventCenter.fireEvent(this, new EventInfo("type.remove.splitPlat").setArgs(new Object[]{staff, TradeUtils.toSidSet(originTrades)}), null);
    }

    /**
     * 退回库存
     */
    private void refundChangeStock(SplitUndoData splitUndoData, Staff staff) {
        List<Trade> originSplitTrades = new ArrayList<>();
        splitUndoData.getSplitUndoDataList().forEach(groupData -> originSplitTrades.addAll(groupData.getUndoSplitTrades()));
        eventCenter.fireEvent(this, new EventInfo(TradeConfigStockBusiness.EVENT_NAME).setArgs(new Object[]{staff, TradeUtils.toSidSet(originSplitTrades)}), null);
    }

    /**
     * 把取消拆单后的分销单，推送给供销-->供销需要反审核，因为分销单子单已经设置enable_status=0
     */
    private void fxCancel(Staff staff, List<Trade> updateTrades) {
        if (CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        Long[] sids = updateTrades.stream().filter(o -> Objects.equals(0, o.getEnableStatus()) &&
                !Objects.equals(o.getSplitSid(), o.getSid())).map(Trade::getSid).toArray(Long[]::new);
        if (sids.length > 0) {
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_CANCEL).setArgs(new Object[]{staff, sids}), null);
        }
    }


    /**
     * 填充order信息
     */
    private void fillOrder(Staff staff, SplitUndoData splitUndoData, List<Trade> originSplitTrades) {
        List<Order> orders = splitUndoBusiness.queryOrders(staff, TradeUtils.toSids(originSplitTrades));
        TradeExceptUtils.fillOrderExceptData(staff,originSplitTrades,orders);
        Map<Long, List<Order>> orderMap = orders.stream().collect(Collectors.groupingBy(Order::getSid));
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        //填充OrderExt信息，当前流程只在BTAS订单触发
        splitUndoBusiness.fillOrderExt(originSplitTrades, staff, orders);
        splitUndoData.getSplitUndoDataList().forEach(groupData -> groupData.getUndoSplitTrades().forEach(trade -> groupData.getOriginOrders().addAll(orderMap.get(trade.getSid()))));
    }


    private void prefilter(Staff staff, List<TbTrade> originTrades, SplitUndoData splitUndoData) {
        if (CollectionUtils.isEmpty(originTrades)) {
            return;
        }
        originTrades.stream().collect(Collectors.groupingBy(TbTrade::getSplitSid)).forEach((k, tbTrades) -> {
            try {
                List<Trade> trades = SplitUndoUtils.validateAndFilterTrades(staff, tbTrades);
                splitUndoData.addNewGroupData(tbTrades, trades);
            } catch (Exception e) {
                splitUndoData.getErrorMessages().put(k, e.getMessage());
            }
        });
    }

    private void nonConsignFilter(SplitUndoData splitUndoData) {
        splitUndoData.getSplitUndoDataList().removeIf(groupData -> {
            Set<String> conflictOrderInfo = SplitUndoUtils.getNonConsignSplitConflictOrderInfo(groupData.getOriginOrders());
            if (!CollectionUtils.isEmpty(conflictOrderInfo)) {
                splitUndoData.getErrorMessages().
                        put(groupData.getUndoSplitTrades().get(0).getSplitSid(), String.format("商品%s标记了无需发货，不能取消拆单", conflictOrderInfo));
                return true;
            }
            return false;
        });
    }

    private List<TbTrade> querySplitTrades(Staff staff, Long[] splitSids) {
        return tbTradeDao.queryBySplitSids(staff, splitSids);
    }

    private List<ERPLock> getUndoTradeLocks(Staff staff, List<Trade> trades) {
        List<Trade> splits = trades.stream().filter(TradeUtils::isSplit).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(splits)) {
            return null;
        }
        return TradeLockBusiness.trades2ERPLocks(staff, splits);
    }

}
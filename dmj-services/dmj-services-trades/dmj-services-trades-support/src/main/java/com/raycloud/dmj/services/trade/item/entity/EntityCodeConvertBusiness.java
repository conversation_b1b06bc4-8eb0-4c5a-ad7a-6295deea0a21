package com.raycloud.dmj.services.trade.item.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONValidator;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.modify.ModifyParentBusiness;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemIdInfo;
import com.raycloud.dmj.domain.item.SuiteSingle;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.FarERPStock;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.item.EntityCodeStock;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.OrderCopier;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.request.ItemSwitchRequest;
import com.raycloud.dmj.services.response.ItemSwitchResponse;
import com.raycloud.dmj.services.stock.IStockServiceDubbo;
import com.raycloud.dmj.services.trade.item.EntityCodeConvertContext;
import com.raycloud.dmj.services.trade.item.EntityCodeItem;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.IdWorkerService;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.raycloud.dmj.services.trades.ITradeConfigService;


import javax.annotation.Resource;
import java.util.*;

import static com.raycloud.dmj.services.trade.item.entity.EntityCodeConvertUtils.ENTITY_2_SUIT;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022/6/15 14:35
 * @Description
 */
@Service
public class EntityCodeConvertBusiness {

    @Resource(name = "solrTradeSearchService")
    public ITradeSearchService tradeSearchService;

    @Resource
    public IItemServiceDubbo itemServiceDubbo;

    @Resource
    public IStockServiceDubbo stockServiceDubbo;

    @Resource
    public ModifyParentBusiness modifyParentBusiness;

    @Resource
    public IdWorkerService idWorkerService;
    @Resource
    public ITradeConfigService tradeConfigService;
    @Resource
    public ITradeConfigNewService tradeConfigNewService;

    public static final String QUERY_FILEDS =  "t.sid,t.tid,t.warehouse_id, t.is_excep,t.user_id";

    private final OrderCopier<Order, Order> orderCopier = new OrderCopier<>();

    public List<Trade> queryTrades(Staff staff, TradeControllerParams params) {
        TradeQueryParams queryParams = TradeQueryParams.copyParams(params)
                .setFields(QUERY_FILEDS)
                .setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT)
                .setQueryOrder(false)
                .setPage(new Page().setPageNo(1).setPageSize(50000));
        return queryParams.getSid().length>0  ? tradeSearchService.queryBySids(staff, false, queryParams.getSid()) : tradeSearchService.search(staff, queryParams).getList();
    }

    public void fillEntityCodeData(Staff staff, EntityCodeConvertContext convertContext, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            int count = 0;
            for (Order order : orders) {
                if (EntityCodeConvertUtils.needSwitch(staff, convertContext.convertParams, order)) {
                    convertContext.addCurEntityCodeItem(order.getItemSysId(), order.getSkuSysId());
                }else{
                    count++;
                }
            }
            if(count == orders.size()){
                String errorMsg = "";
                if(convertContext.convertParams.getOnlyInsufficient() == 1 && ENTITY_2_SUIT == convertContext.convertParams.getSuitsConvertType()){
                    errorMsg = String.format("已开启「库存不足的实体商品才转换」，订单商品库存充足，不进行转换,sid=%s",trade.getSid());
                    if (null!= convertContext.progressData ){
                        convertContext.progressData.getErrorMsg().add(errorMsg);
                    }
                }
            }
        }
        fillEntityCodeItem(staff, convertContext);
        fillEntityCodeStock(staff, convertContext);
    }

    public void item2Order(Staff staff, EntityCodeConvertContext convertContext, Trade trade, Order originOrder, DmjItem dmjItem) {
        List<SuiteSingle> suites = dmjItem.getSuiteSingleList();
        DmjSku dmjSku = EntityCodeConvertUtils.getDmjSku(staff, dmjItem);
        Order newOrder = orderCopier.copy(originOrder, new TbOrder());
        newOrder.setId(idWorkerService.nextId());
        newOrder.setDiscountFee(originOrder.getDiscountFee());
        newOrder.setDiscountRate(originOrder.getDiscountRate());
        newOrder.setDivideOrderFee(originOrder.getDivideOrderFee());
        newOrder.setPlatformDiscountFee(originOrder.getPlatformDiscountFee());
        newOrder.setNumIid(originOrder.getNumIid());
        newOrder.setOid(originOrder.getOid());
        newOrder.setOuterIid(originOrder.getOuterIid());
        newOrder.setAcPayment(originOrder.getAcPayment());
        newOrder.setPayTime(originOrder.getPayTime());
        newOrder.setPrice(originOrder.getPrice());
        newOrder.setWarehouseId(trade.getWarehouseId());
        newOrder.setOrigin(originOrder);
        newOrder.setNonConsign(0);  // 实体转单品后取消无需发货标识
        modifyParentBusiness.fillItem(staff,newOrder, dmjItem, false, convertContext.tradeConfig);
        newOrder.setType(ModifyParentBusiness.getType(dmjItem));
        if (dmjSku != null) {
            modifyParentBusiness.fillSku(newOrder, dmjSku, false);
            newOrder.setType(ModifyParentBusiness.getType(dmjSku));
            suites = dmjSku.getSuiteSingleList();
        }else {
            newOrder.setSkuSysId(-1L);
        }
        // sku 级别有图片，取sku级别的图片
        if (dmjSku !=null){
            String skuPicPath = StringUtils.trimToEmpty(dmjSku.getSkuPicPath());
            if (!StringUtils.equals(skuPicPath, StockConstants.PATH_NO_PIC)) {
                newOrder.setSysPicPath(skuPicPath);
                newOrder.setPicPath(skuPicPath);
            }
        }
        modifyParentBusiness.fillSuit(staff, newOrder, newOrder.getType(), suites, convertContext.tradeConfig);
        convertContext.addCurTradeOldNewOrderMap(trade, originOrder, newOrder);
    }

    private void fillEntityCodeItem(Staff staff, EntityCodeConvertContext convertContext) {
        List<EntityCodeItem> noQuery = new ArrayList<>();
        for (Map.Entry<String, EntityCodeItem> entry : convertContext.curEntityCodeItemMap.entrySet()) {
            String itemKey = entry.getKey();
            EntityCodeItem curEntityCodeItem = entry.getValue();
            EntityCodeItem entityCodeItem = convertContext.entityCodeItemMap.get(itemKey);
            if (entityCodeItem != null) {
                curEntityCodeItem.setDmjItem(entityCodeItem.getDmjItem());
            } else {
                convertContext.entityCodeItemMap.put(itemKey, curEntityCodeItem);
                noQuery.add(curEntityCodeItem);
            }
        }
        if (noQuery.size() > 0) {
            Set<Long> sysItemIds = new HashSet<>(), sysSkuIds = new HashSet<>();
            for (EntityCodeItem entityCodeItem : noQuery) {
                sysItemIds.add(entityCodeItem.getSysItemId());
                sysSkuIds.add(entityCodeItem.getSysSkuId()<0?0:entityCodeItem.getSysSkuId());
            }
            Map<String, DmjItem> itemMap = queryEntityCodeItem(staff, convertContext.convertParams.getSuitsConvertType(), new ArrayList<>(sysItemIds), new ArrayList<>(sysSkuIds));
            for (EntityCodeItem entityCodeItem : noQuery) {
                entityCodeItem.setDmjItem(itemMap.get(entityCodeItem.getKey()));
            }
        }
    }

    private void fillEntityCodeStock(Staff staff, EntityCodeConvertContext convertContext) {
        //套件转实体 并且需要考虑可用库存
        if (EntityCodeConvertUtils.needStock(staff, convertContext.convertParams)) {
            List<ItemIdInfo> queryStock = new ArrayList<>();
            for (Map.Entry<String, EntityCodeItem> entry : convertContext.curEntityCodeItemMap.entrySet()) {
                Map<Long, EntityCodeStock> stockMap = convertContext.entityCodeStockMap.get(entry.getKey());
                if (stockMap != null) {
                    convertContext.curEntityCodeStockMap.put(entry.getKey(), stockMap);
                } else {
                    DmjItem dmjItem = entry.getValue().getDmjItem();
                    if (dmjItem!=null){
                        ItemIdInfo itemIdInfo = new ItemIdInfo();
                        itemIdInfo.setSysItemId(dmjItem.getSysItemId());
                        itemIdInfo.setSysSkuId(EntityCodeConvertUtils.getSysSkuId(staff, dmjItem));
                        queryStock.add(itemIdInfo);
                    }
                }
            }
            if (queryStock.size() > 0) {
                Map<String, List<FarERPStock>> farERPStockMap = queryStock(staff, queryStock);
                for (ItemIdInfo itemIdInfo : queryStock) {
                    Map<Long, EntityCodeStock> stockMap = new HashMap<>();
                    String itemKey = EntityCodeConvertContext.getItemKey(itemIdInfo.getSysItemId(), itemIdInfo.getSysSkuId());
                    List<FarERPStock> farERPStocks = farERPStockMap.get(itemKey);
                    if (CollectionUtils.isNotEmpty(farERPStocks)) {
                        for (FarERPStock farERPStock : farERPStocks) {
                            EntityCodeStock entityCodeStock = EntityCodeConvertContext.buildEntityCodeStock(farERPStock.getWareHouseId(), farERPStock.getSysItemId(), farERPStock.getSysSkuId());
                            entityCodeStock.setAllStock(farERPStock.getAvailableInStock().intValue());
                            entityCodeStock.setUseStock(0);
                            stockMap.put(farERPStock.getWareHouseId(), entityCodeStock);
                        }
                    }
                    convertContext.entityCodeStockMap.put(itemKey, stockMap);
                    convertContext.curEntityCodeStockMap.put(itemKey, stockMap);
                }
            }
        }
    }

    private Map<String, DmjItem> queryEntityCodeItem(Staff staff, Integer switchFlag, List<Long> sysItemIds, List<Long> sysSkuIds) {
        ItemSwitchRequest itemSwitchRequest = new ItemSwitchRequest();
        itemSwitchRequest.setSwitchFlag(switchFlag);
        itemSwitchRequest.setSysItemIdList(sysItemIds);
        itemSwitchRequest.setSysSkuIdList(sysSkuIds);
        ItemSwitchResponse itemSwitchResponse = itemServiceDubbo.querySuitOrEntityItem(staff, itemSwitchRequest);
        Map<String, DmjItem> result = itemSwitchResponse != null && itemSwitchResponse.getResult() != null ? itemSwitchResponse.getResult() : new HashMap<>();
        // Logs.ifDebug(LogHelper.buildLog(staff, String.format("按实体编码转换查询结果，request=%s,response=%s", JSON.toJSONString(itemSwitchRequest), JSON.toJSONString(itemSwitchResponse))));
        return result;
    }

    private Map<String, List<FarERPStock>> queryStock(Staff staff, List<ItemIdInfo> itemIdInfoList) {
        Map<String, List<FarERPStock>> stockMap = new HashMap<>();
        List<FarERPStock> stocks = stockServiceDubbo.queryItemStocks(staff, itemIdInfoList, null);
        if (CollectionUtils.isNotEmpty(stocks)) {
            for (FarERPStock stock : stocks) {
                stockMap.computeIfAbsent(EntityCodeConvertContext.getItemKey(stock.getSysItemId(), stock.getSysSkuId()), k -> new ArrayList<>()).add(stock);
            }
        }
        return stockMap;
    }



    /**
     * 判断是否可以自动套件转实体商品，开了自动套件转实体编码配置，并且没开 (套件转单品)
     * @param staff
     * @return
     */
    public boolean canConvertAuto(Staff staff) {
        // 自动套件转实体开关开启
        TradeConfigNew tradeConfigNew =  tradeConfigNewService.get(staff, TradeConfigEnum.ENTITY_CODE_CONVERT_AUTO);
        int entityCodeConvertAuto =  Integer.parseInt(tradeConfigNew.getConfigValue());
        if (entityCodeConvertAuto>0){
            return canConvert(staff);
        }
        return false;
    }

    public boolean canConvert(Staff staff) {
        //开了自动转套件 都不能自动套件转实体
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        return tradeConfig.getAutoSuitTransformSingle(staff) != 1;
    }

    public Set<String> canConvertAutoTagIdSet(Staff staff) {
        // 自动套件转实体,标签配置获取
        TradeConfigNew tradeConfigNew =  tradeConfigNewService.get(staff, TradeConfigEnum.ENTITY_CODE_CONVERT_AUTO_TAG_ID_SET);
        return getStringSet(tradeConfigNew.getConfigValue());
    }

    private Set<String> getStringSet(String setStr) {
        if (StringUtils.isBlank(setStr)){
            return new HashSet<>();
        }
        JSONValidator validator = JSONValidator.from(setStr);
        if (validator.validate() && JSONValidator.Type.Array.equals(validator.getType())) {
            return new HashSet<>(JSONArray.parseArray(setStr, String.class));
        }
        String[] strings = setStr.split(",|，");
        Set<String> set = new HashSet<>();
        Collections.addAll(set, strings);
        return set;
    }


    public List<Trade> tradeTagInSet(List<Trade> allTrades, Set<String> tagIdSet) {
        List<Trade> trades = new ArrayList<>();
        if (tagIdSet.contains("CLOSE") || tagIdSet.size()==0){ // 未开启过滤 || 公司没配置标签
            return allTrades;
        }else{
            for (Trade trade: allTrades){
                if (StringUtils.isBlank(trade.getTagIds())){ // 订单无标签。
                    continue;
                }

                Set<String> set = getStringSet(trade.getTagIds());
                if (CollectionUtils.containsAny(tagIdSet,set)){ // 订单标签集合 和配置标签集合有交集
                    trades.add(trade);
                }
            }
        }
        return trades;
    }
}

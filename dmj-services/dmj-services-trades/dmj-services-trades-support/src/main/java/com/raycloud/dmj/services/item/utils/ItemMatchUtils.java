package com.raycloud.dmj.services.item.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.config.entity.ItemKeywordParse;
import com.raycloud.dmj.domain.trade.item.ItemKeywordParseData;
import com.raycloud.dmj.domain.trade.item.ItemMatchResult;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.mvel2.ast.Or;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

public class ItemMatchUtils {

    private static final int PARSE_BY_TITLE = 0; //从名称解析
    private static final int PARSE_BY_SKU = 1;  //从规格属性名解析
    private static final int PARSE_BY_OUTER_ID = 2;  //从平台商家编码解析

    public static boolean validateOpenItemKeywordParse(Long userId, ItemKeywordParse parse){
        return parse != null && parse.isEnableStatus() && (parse.getParseType() == 1 || parse.checkUserId(userId) != -1);
    }

    public static ItemKeywordParse validate(User user) {
        try {
            TradeConfigNew tradeConfigNew = TradeConfigGetUtil.get(user.getStaff(), TradeConfigEnum.ITEM_KEYWORD_PARSE);
            ItemKeywordParse itemKeywordParse = new ItemKeywordParse().configValueToEntity(tradeConfigNew);
            return validateOpenItemKeywordParse(user.getId(), itemKeywordParse) ? itemKeywordParse : null;
        } catch (Exception e) {
            return null;
        }
    }

    public static String getParseField(Order order, int type) {
        switch (type) {
            case PARSE_BY_SKU: return order.getSkuPropertiesName();
            case PARSE_BY_OUTER_ID: return order.getOuterSkuId();
            default: return order.getTitle();
        }
    }

    public static String getParseFieldName(int type) {
        switch (type) {
            case PARSE_BY_SKU: return "平台规格名称";
            case PARSE_BY_OUTER_ID: return "平台商家编码";
            default: return "平台商品名称";
        }
    }

    public static Map<String, Integer> parseOuterId(Order order, ItemKeywordParse parse, ItemMatchResult result) {
        String originStr = getParseField(order, parse.checkUserId(result.getUser().getId()));
        if(StringUtils.isBlank(originStr)){
            return null;
        }
        try {
            ItemKeywordParseData data = new ItemKeywordParseData(parse);
            data.setOriginStr(StringUtils.deleteWhitespace(originStr));
            data.setCurrentStr(data.getOriginStr());

            if (result.getIsInsert()) { //仅新增订单时允许多商家编码匹配
                if (parse.getParseType() == 1) {    //标准格式解析
                    return parseStandard(data).getOuterIdMap();
                }
                if (parse.getComplex() == 1) {  //多商家编码自定义匹配
                    return addOuterId(data).getOuterIdMap();
                }
            }
            String outerId = parseOneOuterId(data);
            if (StringUtils.isBlank(outerId)) {
                return null;
            }
            Map<String , Integer> outerIdMap = Maps.newHashMap();
            outerIdMap.put(outerId, 1);
            return outerIdMap;
        } catch (Exception e) {
            Logs.info("商品解析失败！msg: " + e.getMessage());
        }
        return null;
    }

    private static ItemKeywordParseData parseStandard(ItemKeywordParseData data) {
        String originStr = data.getOriginStr();
        String[] splits = originStr.split("\\++");
        for (String split : splits) {
            if (StringUtils.isBlank(split)) {   //现在解析出来如果是空将跳过
                continue;
            }
            if (split.startsWith("*")) {
                split = split.replaceFirst("\\*+", ""); //去掉字符串前面的*号
            }
            String[] outerIdAndNum = split.split("[*]", -1);

            Assert.isTrue(outerIdAndNum.length == 1 || outerIdAndNum.length == 2, String.format("公式[%s]格式错误！", split));
            String outerId = outerIdAndNum[0];
            Assert.isTrue(StringUtils.isNotBlank(outerId), "公式格式错误, 商家编码为空！");
            String num = outerIdAndNum.length == 2 ? outerIdAndNum[1] : "1";
            Assert.isTrue(NumberUtils.isNumber(num) && Integer.parseInt(num) > 0, String.format("公式格式错误, 商品数量[%s]非数字或小于1!", num));

            data.putOuterId(outerId, Integer.parseInt(num));
        }
        return data;
    }

    public static String parseOneOuterId(ItemKeywordParseData data) {
        String originStr = data.getCurrentStr();
        String start = data.getStart();
        String end = data.getEnd();

        int startPoint = StringUtils.isEmpty(start) ? 0 : originStr.indexOf(start);
        if (startPoint == -1) {
            return null;
        }
        int endPoint = StringUtils.isEmpty(end) ? originStr.length() : originStr.indexOf(end, startPoint + start.length());
        if (endPoint == -1 ) {
            return null;
        }
        return originStr.substring(startPoint + start.length(),endPoint);
    }

    public static void main(String[] args) {
        ItemKeywordParseData data = new ItemKeywordParseData();
        data.setOuterIdMap(Maps.newHashMap());
        data.setStart("【");
        data.setEnd("】");
        data.setNumStart("#");
        data.setNumEnd("");
        data.setOriginStr("秒拍@G001@G002@@G003@@");
        data.setCurrentStr("测试关键字匹配商品【化妆品1】#1正常信息【化妆品2】额外信息#2XXX【化妆品3】哦啦啦【化妆品4【");

        addOuterId(data);
        System.out.println(JSONObject.toJSONString(data.getOuterIdMap()));
    }

    public static ItemKeywordParseData addOuterId(ItemKeywordParseData data) {
        String start = data.getStart();
        String end = data.getEnd();
        String originStr = data.getCurrentStr();

        int nextStart = -1; //下一个起始字符截取位置
        int endPoint = -1; //字符截取结束位置
        if (StringUtils.isNotEmpty(start)) {
            int startPoint = originStr.indexOf(start);
            if (startPoint == -1) { //字符串中不再包含起始字符，直接返回
                return data;
            }
            originStr = originStr.substring(startPoint + start.length());
            nextStart = originStr.indexOf(start);   //截取后再读一次起始字符位置，如果为-1说明后面没有这个字符了
        }
        if (StringUtils.isNotEmpty(end)) {
            endPoint = originStr.indexOf(end);
            if (endPoint == -1) {   //字符串中不再包换结束字符，直接返回
                return data;
            }
            if (nextStart != -1) {  //结束不为空时，下一个开头应该从结束后开始算
                nextStart = originStr.substring(endPoint + end.length()).indexOf(start);
            }
        }
        if (endPoint != -1) {   //说明确实存在结束字符，商家编码为0到截止字符截止位置处
            String outerId = originStr.substring(0, endPoint);
            Assert.isTrue(StringUtils.isNotBlank(outerId), String.format("从关键字[%s, %s]中提取到的商家编码为空", start, end));

            data.setCurrentStr(originStr.substring(endPoint + end.length()));
            endNotEmpty(data, outerId, nextStart);
            addOuterId(data);
        } else if (nextStart != -1) {   //说明存在第二个起始字符, 但是没有结尾字符，商品编码截止到此位置
            String outerId = originStr.substring(0, nextStart);
            data.setCurrentStr(originStr.substring(nextStart));
            endEmpty(data, outerId);
            addOuterId(data);
        } else {    //既没有截止字符，也没有第二个起始字符，商家编码为剩余字符，处理后直接返回
            endEmpty(data, originStr);
        }
        return data;
    }

    /**
     * 商品结束字符不为空时的处理
     * @param data
     * @param outerId
     * @param nextStart
     */
    public static void endNotEmpty(ItemKeywordParseData data, String outerId, int nextStart) {
        String start = data.getStart();
        String end = data.getEnd();
        String numStart = data.getNumStart();
        String numEnd = data.getNumEnd();
        String originStr = data.getCurrentStr();

        int num = 1;

        String numStr = originStr;
        int nextEnd = originStr.indexOf(end);   //下一个商品结束字符位置
        if (nextStart != -1 && nextEnd != -1) { //这种情况说明还存在下一个商品，数量提取的范围在下一个商品起始前
            numStr = originStr.substring(0, nextStart);
        } else if (StringUtils.isBlank(start) && nextEnd != -1) {  //只存在截止字符，数量提取要在下一个截止前
            numStr = originStr.substring(0, nextEnd);
        }

        //商品起始字符和组合比例结尾字符为空，无法解析数量，默认为1
        if (StringUtils.isNotBlank(numStr) && (StringUtils.isNotBlank(start) || StringUtils.isNotBlank(numEnd))) {
            //截取数量时，如果没有组合比例结尾，就取商品的开头
            num = substringNum(numStr, numStart, numEnd);
            int subPoint = numStr.indexOf(StringUtils.defaultIfEmpty(numEnd, start));
            if (subPoint > 0) {
                data.setCurrentStr(originStr.substring(subPoint + numEnd.length()));
            }
        }
        data.putOuterId(outerId, num);
    }

    /**
     * 商品结束字符为空时提取
     * @param data
     * @param outerId
     */
    public static void endEmpty(ItemKeywordParseData data, String outerId) {
        String start = data.getStart();
        String end = data.getEnd();
        String numStart = data.getNumStart();
        String numEnd = data.getNumEnd();

        int num = 1;

        if (StringUtils.isNotBlank(numStart)) { //包含组合起始字符才能解析出数量
            int numStartPoint = outerId.indexOf(numStart);
            if (numStartPoint != -1) {
                num = substringNum(outerId, numStart, numEnd);
                outerId = outerId.substring(0, numStartPoint);
            }
        }
        Assert.isTrue(StringUtils.isNotBlank(outerId), String.format("从关键字[%s, %s]中提取到的商家编码为空", start, end));
        data.putOuterId(outerId, num);
    }

    /**
     * 提取组合比例
     */
    public static int substringNum(String originStr, String numStart, String numEnd) {
        int defNum = 1;
        if (StringUtils.isBlank(numStart) && StringUtils.isBlank(numEnd)) {
            return defNum;
        }
        int startPoint = StringUtils.isEmpty(numStart) ? 0 : originStr.indexOf(numStart);
        if (startPoint == -1) {
            return defNum;
        }
        int endPoint = StringUtils.isEmpty(numEnd) ? originStr.length() : originStr.indexOf(numEnd,startPoint + numStart.length());
        if (endPoint == -1) {
            return defNum;
        }
        String numStr = originStr.substring(startPoint + numStart.length(), endPoint);
        Assert.isTrue(StringUtils.isNotBlank(numStr), String.format("从关键字[%s, %s]中提取到的组合比例为空", numStart, numEnd));
        Assert.isTrue(NumberUtils.isNumber(numStr), String.format("从关键字[%s, %s]中提取到的组合比例[%s]不为数字！", numStart, numEnd, numStr));
        Assert.isTrue(Integer.parseInt(numStr) > 0, String.format("从关键字[%s, %s]中提取到的组合比例[%s]必须大于0！", numStart, numEnd, numStr));
        return Integer.parseInt(numStr);
    }

}

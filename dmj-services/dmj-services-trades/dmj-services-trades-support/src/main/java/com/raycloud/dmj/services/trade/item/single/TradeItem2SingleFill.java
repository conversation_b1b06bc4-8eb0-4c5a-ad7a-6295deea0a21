package com.raycloud.dmj.services.trade.item.single;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.services.trade.item.common.TradeItemFill;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/17
 * @description 订单商品填充
 */
@Service
public class TradeItem2SingleFill extends TradeItemFill {


    public void fill(Staff staff, TradeItemContext itemContext, List<Trade> trades) {
        if(CollectionUtils.isEmpty(trades)){
            return;
        }

        //TODO
    }

}

package com.raycloud.dmj.business.logistics;

import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.dao.trade.ConsignRecordDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.enums.ExpressMappingCacheKeyEnum;
import com.raycloud.dmj.domain.enums.NewTradeExtendConfigEnum;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.platform.trades.PackagesNoticeRequest;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLog;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLogDetail;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.bo.ExpressCompanyMappingBO;
import com.raycloud.dmj.domain.trades.utils.TradeExceptionUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.trades.utils.UploadServiceWhitelistUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.basis.PlatformResponse;
import com.raycloud.dmj.services.platform.trades.ConsignUploader;
import com.raycloud.dmj.services.pt.IMultiPacksPrintTradeLogService;
import com.raycloud.dmj.services.pt.dubbo.IExpressTemplateDubboService;
import com.raycloud.dmj.services.trades.IExpressCompanyMappingService;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import com.raycloud.dmj.services.trades.TradeSearchService;
import com.raycloud.dmj.services.trades.upload.ConsignUploadDubbo;
import com.raycloud.dmj.services.trades.upload.UploadRequest;
import com.raycloud.dmj.services.trades.upload.UploadResponse;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.utils.CommonConstants.PLAT_FORM_TYPE_SYS;

/**
 * 已发货订单调用上传单号接口（淘宝天猫按数量拆单、已发货后拆单上传单号至平台）
 */
@Service
public class UploadPackagesNoticeBusiness {

    private static final Logger logger = Logger.getLogger(UploadPackagesNoticeBusiness.class);

    @Resource
    IEventCenter eventCenter;
    @Resource
    IExpressCompanyService expressCompanyService;
    @Resource
    IExpressTemplateDubboService expressTemplateDubboService;
    @Resource
    UploadRecordBusiness uploadRecordBusiness;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;
    @Resource(name = "solrTradeSearchService")
    TradeSearchService tradeSearchService;
    @Resource
    PlatformManagement platformManagement;
    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;
    @Resource
    IExpressCompanyMappingService expressCompanyMappingService;

    @Resource
    ConsignRecordDao consignRecordDao;
    @Autowired(required = false)
    ConsignUploadDubbo consignUploadDubbo;


    /**
     * 发货后触发通知，接收事件
     */
    public void packagesNoticeAuto(Staff staff, Long[] sids){
        PackagesNoticeData data = packagesNotice(staff, sids, Boolean.TRUE);
        if(data!=null&&data.errors!=null&&data.errors.size()>0){
            logger.info(LogHelper.buildLog(staff,String.format("发货后处理物流多包裹通知被过滤,errorMap=[%s]", data.errors)));
        }
        if(data==null){
            logger.info(LogHelper.buildLog(staff,String.format("发货后处理物流多包裹通知被过滤,sid:%s", sids)));
        }
    }


    /**
     * 手动触发通知
     */
    public List<Map<String,String>> packagesNoticeManual(Staff staff, Long[] sids){
        PackagesNoticeData data = packagesNotice(staff, sids, Boolean.FALSE);
        return toFrontResponse(null == data ? null : data.errors);
    }

    public PackagesNoticeData packagesNotice(Staff staff, Long[] sids, Boolean isAuto) {
        if (null == sids || sids.length <= 0) {
            return null;
        }
        PackagesNoticeData data = buildPackagesNoticeData(staff, sids);
        if (Objects.isNull(data)) {
            return null;
        }
        // 走新版发货服务
        distributeUploadService(staff, data, isAuto);

        filterSysTrades(data);
        fillSplitTrade(staff,data);
        filterTrades(data, isAuto);
        filterTradesOrderItemId(data);
        fillTradesMultiOutSid(data);
        buildPackagesNoticeRequests(data);
        doNotice(data);
        return data;
    }

    private PackagesNoticeData buildPackagesNoticeData(Staff staff, Long[] sids){
        if (null == sids || sids.length <= 0) {
            return null;
        }
        //先查打印记录可减少trade的查询数量，否则两边查询同样多数量的数据
        Map<Long, List<MultiPacksPrintTradeLog>> sidMultiLogMap = multiPacksPrintTradeLogService.queryBySids(staff, sids);
//        if(MapUtils.isEmpty(sidMultiLogMap)){
//            return null;
//        }
        List<Trade> trades = tradeSearchService.queryBySids(staff,true,sids);
        if (CollectionUtils.isEmpty(trades)) {
            return null;
        }
        PackagesNoticeData packagesNoticeData = new PackagesNoticeData(staff, trades);
        if(sidMultiLogMap!=null&&sidMultiLogMap.size()>0){
            packagesNoticeData.sidMultiLogMap.putAll(sidMultiLogMap);
        }
        return packagesNoticeData;
    }

    private void distributeUploadService(Staff staff, PackagesNoticeData data, boolean isAuto) {
        if (Objects.isNull(consignUploadDubbo)) {
            return;
        }

        List<Trade> trades = UploadServiceWhitelistUtils.distributeUploadServiceTrades(staff, 2, data.trades);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        data.trades.removeAll(trades);

        UploadRequest request = new UploadRequest();
        request.setStaff(staff);
        request.setSendType(SendType.MUL_PACK_UPLOAD.name());
        request.setSids(trades.stream().map(Trade::getSid).distinct().collect(Collectors.toList()));
        // 异步调用
        if (isAuto) {
            consignUploadDubbo.asyncUpload(request);
            return;
        }
        // 同步调用
        UploadResponse response = consignUploadDubbo.upload(request);
        List<UploadResponse.Result> results;
        if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(results = response.getResults())) {
            results.stream().filter(r -> !r.isSuccess()).forEach(r -> {
                data.errors.put(r.getSid(), r.getMessage());
            });
        }
    }

    /**
     * 发货后触发通知，发送事件
     */
    public void asyncPackagesNotice(Staff staff, List<Trade> trades, TradeConfig tradeConfig){
        try{
            boolean b = tradeConfig.getInteger(NewTradeExtendConfigEnum.ALLOW_PACKAGES_NOTICE.getKey()) != 1;
            Lists.partition(TradeUtils.toSidList(trades), 10).forEach(e -> {
                Logs.debug(LogHelper.buildLog(staff, String.format("ALLOW_PACKAGES_NOTICE =%s 多包裹物流上传通知，sids=%s！", b, e)));
            });
            if (CollectionUtils.isEmpty(trades)) {
                Logs.debug(LogHelper.buildLog(staff, String.format("ALLOW_PACKAGES_NOTICE =%s 多包裹物流上传通知，订单不存在，sids.size()=0！", b)));
                return;
            }
            if(b){
                return;
            }
            PackagesNoticeData data = new PackagesNoticeData(staff, trades);
            fillSplitTrade(staff,data);
            filterTrades(data, Boolean.TRUE);
            if(CollectionUtils.isNotEmpty(trades)){
                Long[] sids = TradeUtils.toSids(trades);
                eventCenter.fireEvent(this, new EventInfo("trade.packages.notice").setArgs(new Object[]{staff, sids}),null);
                if(logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("发货后发送物流多包裹通知事件，sids=[%s]", Arrays.toString(sids))));
                }
            }
        }catch (Exception e){
            logger.error(LogHelper.buildErrorLog(staff,e,String.format("发货后发送物流多包裹通知事件失败,%s", ExceptionUtils.getFullStackTrace(e))),e);
        }
    }

    /**
     * 改成前端需要的格式
     */
    private List<Map<String, String>> toFrontResponse(Map<Long, String> errorMap) {
        if(MapUtils.isEmpty(errorMap)){
            return new ArrayList<>();
        }
        List<Map<String, String>> result = new ArrayList<>();
        for(Map.Entry<Long,String> entry : errorMap.entrySet()){
            Map<String,String> temp = new HashMap<>();
            temp.put("sid",String.valueOf(entry.getKey()));
            temp.put("errorMsg",String.valueOf(entry.getValue()));
            result.add(temp);
        }
        return result;
    }

    /**
     * 自动触发时的过滤条件
     */
    private void filterTrades(PackagesNoticeData data, Boolean isAuto) {
        if(CollectionUtils.isEmpty(data.trades)){
            return;
        }
        Iterator<Trade> iterator = data.trades.iterator();
        while(iterator.hasNext()){
            Trade trade = iterator.next();
            String errorStr = validateTrade(data.staff, trade,data.getUser(trade), isAuto);
            if(StringUtils.isBlank(errorStr)){
                continue;
            }
            iterator.remove();
            data.errors.put(trade.getSid(),errorStr);
        }
    }

    /**
     * 自动触发时仅支持淘宝天猫已发货平台拆单
     */
    private String validateTrade(Staff staff, Trade trade, User user, Boolean isAuto) {
        if (!validateTradeSource(user)) {
            return "非拼多多、好衣库、快手、奇门订单";
        }
        if (StringUtils.isBlank(trade.getOutSid())) {
            return "运单号为空";
        }
        if (!TradeUtils.isNorMalSource(trade)) {
            return "供分销订单不支持该操作";
        }
        if (Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU)
                && trade.getSplitSid() > 0&&trade.getContainGift()!=null&&trade.getContainGift()==true&&!PlatformUtils.isKuaiShouLogisticsTransitTrade(staff,trade)) {
            return "快手赠品拆分订单不支持该操作";
        }
      /*  if (isAuto && !UploadUtils.platformIsConsigned(staff, trade)) {
            return "非平台已发货订单";
        }*/
        return null;
    }

    /**
     * KMERP-134036: 系统手工单，无需触发多包裹物流
     */
    private void filterSysTrades(PackagesNoticeData data) {
        if (CollectionUtils.isEmpty(data.trades)) {
            return;
        }
        Iterator<Trade> iterator = data.trades.iterator();
        while (iterator.hasNext()) {
            Trade trade = iterator.next();
            if (isSysTrade(trade)) {
                data.errors.put(trade.getSid(), "系统手工单不支持该操作");
                iterator.remove();
            }
        }
    }

    private boolean isSysTrade(Trade trade) {
        if (!PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
            return false;
        }
        //noinspection RedundantIfStatement
        if (TradeUtils.isSplit(trade) && Objects.nonNull(trade.getSplitSource()) && !PLAT_FORM_TYPE_SYS.equals(trade.getSplitSource())) {
            return false;
        }
        return true;
    }

    public boolean validateTradeSource(User user) {
        return user != null && (
                   CommonConstants.PLAT_FORM_TYPE_PDD.equals(user.getSource())
                || CommonConstants.PLAT_FORM_TYPE_HYK2.equals(user.getSource())
                || CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(user.getSource())
        );
    }

    private boolean validateTbOrTmOrPddSource(User user){
        return user != null && (TradeUtils.isTbTrade(user.getSource())
                || CommonConstants.PLAT_FORM_TYPE_PDD.equals(user.getSource())
        );
    }


    /**
     * 设置num_iid并做过滤
     */
    private void filterTradesOrderItemId(PackagesNoticeData data) {
        if(CollectionUtils.isEmpty(data.trades)){
            return;
        }
        List<Trade> needFilterTrades = data.trades.stream().filter(trade->needFilterOrderItemId(data.getUser(trade))).collect(Collectors.toList());
        List<Order> orders = TradeUtils.getOrders4Trade(needFilterTrades);
        Map<String, Map<Long, Order>> tidOidPlatOrderMap = getPlatOrderMap(data.staff, orders);
        Iterator<Trade> iterator = data.trades.iterator();
        while(iterator.hasNext()){
            Trade trade = iterator.next();
            if(!needFilterOrderItemId(data.getUser(trade))){
                continue;
            }
            boolean needUpload = false;
            for(Order order : TradeUtils.getOrders4Trade(trade)){
                if(StringUtils.isBlank(order.getNumIid())){
                    Order platOrder = null;
                    Map<Long, Order> oidPlatOrderMap = tidOidPlatOrderMap.get(order.getTid());
                    if(oidPlatOrderMap != null){
                        platOrder = oidPlatOrderMap.get(order.getOid());
                    }
                    if(platOrder != null){
                        order.setNumIid(platOrder.getNumIid());
                    }
                }
                if(StringUtils.isNotBlank(order.getNumIid())){
                    order.setNeedUploadConsign(true);
                    needUpload = true;
                }
            }
            if(!needUpload){
                iterator.remove();
                data.errors.put(trade.getSid(),"未包含平台商品");
            }
        }
    }

    private boolean needFilterOrderItemId(User user) {
        return user!=null && TradeUtils.isTbTrade(user.getSource());
    }

    /**
     * 如果订单没有num_iid，则查出其是否有对应的平台子单
     * @return
     */
    private Map<String, Map<Long, Order>> getPlatOrderMap(Staff staff, List<Order> orders) {
        Set<Long> oids = new HashSet<>();
        Set<String> tids = new HashSet<>();
        orders.stream().filter(o->StringUtils.isBlank(o.getNumIid())).forEach(o->{
            tids.add(o.getTid());
            oids.add(o.getOid());
        });
        return uploadRecordBusiness.getGroupOidPlatOrderByOid(staff, tids.toArray(new String[0]), oids.toArray(new Long[0]));
    }


    /**
     * 设置expressCode并做过滤
     */
    private void buildPackagesNoticeRequests(PackagesNoticeData data) {
        if(CollectionUtils.isEmpty(data.trades)){
            return;
        }
        Map<String, UserExpressTemplate> expressTemplateMap = UploadUtils.getCompanyTemplate(data.staff, expressTemplateDubboService);
        Map<Long, ExpressCompany> expressCompanyMap = expressCompanyService.getExpressCompanyIdMap();
        List<Trade> pddSplitTrades = new ArrayList<>();
        for (Trade trade : data.trades){
            // 拼多多，多包裹物流上传，单独处理
            User user = data.getUser(trade);
            if (user != null && (Objects.equals(user.getSource(), CommonConstants.PLAT_FORM_TYPE_PDD)||Objects.equals(user.getSource(), CommonConstants.PLAT_FORM_TYPE_QIMEN))) {
                pddSplitTrades.add(trade);
                continue;
            }
            String tplKey = UploadUtils.createTplKey(trade.getTemplateId(), trade.getTemplateType());
            UserExpressTemplate tpl = expressTemplateMap.get(tplKey);
            //找不到快递模板或者找不到快递公司的需要移除掉
            if(tpl == null || tpl.getExpressId() == null ){
                data.errors.put(trade.getSid(),"找不到快递模板");
                continue;
            }
            ExpressCompany ec = expressCompanyMap.get(tpl.getExpressId());
            if(ec == null){
                data.errors.put(trade.getSid(),"找不到对应的快递公司");
                continue;
            }
            PackagesNoticeRequest request = data.buildPackagesNoticeRequest(trade);
            request.setExpressCompany(ec);
            //部分平台需要设置物流映射
            if(ExpressMappingCacheKeyEnum.getBySource(user.getSource())!=null){
                request.setExpressCompanyMapping(data.getExpressCompanyMappingMap(trade, expressCompanyMappingService).get(ec.getId()));
            }
            data.requestList.add(request);
        }
        buildPddSplitPackagesNoticeRequests( data, pddSplitTrades, expressTemplateMap, expressCompanyMap);
    }

    private void fillTradesMultiOutSid(PackagesNoticeData data) {
        if(CollectionUtils.isEmpty(data.trades) || MapUtils.isEmpty(data.sidMultiLogMap)){
            return;
        }
        List<Trade> noNeedUpload = new ArrayList<>();
        List<Trade> multiOutSidTrades = new ArrayList<>();
        for(Trade trade : data.trades){
            List<MultiPacksPrintTradeLog> logs = data.sidMultiLogMap.get(trade.getSid());
            if(CollectionUtils.isEmpty(logs)){
                continue;
            }
            //如果含有一单多包信息，则构建一个新trade加到请求对象中，该trade仅包含需要的基本信息
            multiOutSidTrades.addAll(buildSimpleOutSidTrades(trade,logs.get(0)));
            //快手订单已发货的单号不进行多包裹上传
            if (CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(trade.getSource())) {
                noNeedUpload.add(trade);
            }
        }
        data.trades.removeIf(noNeedUpload::contains);
        data.trades.addAll(multiOutSidTrades);
        List<UploadRecord> uploadRecords = uploadRecordBusiness.queryBySid(data.staff, TradeUtils.toSids(data.trades));
        //  上传成功的运单号
        Set<String> outSids = uploadRecords.stream().filter(uploadRecord -> Objects.equals(uploadRecord.getUploadStatus(), 1)).map(UploadRecord::getOutSid).collect(Collectors.toSet());
        Iterator<Trade> iterator = data.trades.iterator();
        while (iterator.hasNext()){
            Trade next = iterator.next();
            User user = data.getUser(next);
            // 已经上传成功过的运单号，不要再上传， 目前只针对平多多单处理，淘宝天猫的支持重复上传,非上传失败
            if (outSids.contains(next.getOutSid()) && Objects.equals(user.getSource(), CommonConstants.PLAT_FORM_TYPE_PDD) && !TradeExceptionUtils.hasUploadExcept(data.staff,next)) {
                Logs.debug(LogHelper.buildLog(data.staff,String.format("sid=%s,outSid=%s v=%s已经上传过不需要再上传!",next.getSid(),next.getOutSid(),next.getV())));
                iterator.remove();
            }
        }
    }

    private List<Trade> buildSimpleOutSidTrades(Trade trade, MultiPacksPrintTradeLog log) {
        if(CollectionUtils.isEmpty(log.getDetails())){
            return Collections.emptyList();
        }
        List<Trade> simpleOutSidTrades = new ArrayList<>(log.getDetails().size());
        for(MultiPacksPrintTradeLogDetail detail : log.getDetails()){
            if(StringUtils.isBlank(detail.getOutSid())){
                continue;
            }
            if(detail.getOutSid().equals(trade.getOutSid())){
                continue;
            }
            simpleOutSidTrades.add(buildSimpleOutSidTrade(trade, detail));
        }
        return simpleOutSidTrades;
    }

    private Trade buildSimpleOutSidTrade(Trade trade, MultiPacksPrintTradeLogDetail detail) {
        Trade simpleTrade = new TbTrade();
        simpleTrade.setSid(trade.getSid());
        simpleTrade.setTid(trade.getTid());
        simpleTrade.setUserId(trade.getUserId());
        simpleTrade.setOutSid(detail.getOutSid());
        simpleTrade.setTemplateId(trade.getTemplateId());
        simpleTrade.setTemplateType(trade.getTemplateType());
        simpleTrade.setSplitSid(trade.getSplitSid());
        TradeUtils.setOrders(simpleTrade,TradeUtils.getOrders4Trade(trade));
        return simpleTrade;
    }

    private  void doNotice( PackagesNoticeData data) {
        if(CollectionUtils.isEmpty(data.requestList)){
            return ;
        }
        List<Trade> updateTraceTrades = new ArrayList<>(data.requestList.size());
        for(PackagesNoticeRequest request : data.requestList){
            Trade trade = request.getTrade();
            updateTraceTrades.add(trade);
            User user = data.getUser(trade);
            PlatformResponse result = null;

            try {
                ConsignUploader access = platformManagement.getAccess(user.getSource(), ConsignUploader.class);
                result = access.packagesNotice(user, request);
            }catch (Exception e){
                fillMultiOutTradeLog(data,updateTraceTrades, request,"多包裹物流上传失败", ExceptionUtils.getFullStackTrace(e),true);
                logger.error(LogHelper.buildErrorLog(data.staff, e, "多包裹物流上传失败,sid:" + trade.getSid()), e);
                trade.getOperations().put(OpEnum.TRADE_PACKAGES_NOTICE, "多包裹物流上传失败:" + e.getMessage());
                data.errors.put(trade.getSid(), e.getMessage());
                continue;
            }
            if(result==null || !BooleanUtils.isTrue(result.isSuccess())){
                String errorMsg = getErrorMsg(result);
                fillMultiOutTradeLog(data, updateTraceTrades, request, "多包裹物流上传失败", errorMsg, true);
                if (!isSuccess(errorMsg)) {
                    trade.getOperations().put(OpEnum.TRADE_PACKAGES_NOTICE, "多包裹物流上传失败:" + errorMsg);
                }
                data.errors.put(trade.getSid(), errorMsg);

            }else {
                fillMultiOutTradeLog(data, updateTraceTrades,request, "多包裹物流上传成功",trade.getSid().toString(),false);
                trade.getOperations().put(OpEnum.TRADE_PACKAGES_NOTICE,"多包裹物流上传成功");
            }
        }
        tradeTraceBusiness.asyncTrace(data.staff,updateTraceTrades,OpEnum.TRADE_PACKAGES_NOTICE);
    }



    private void fillMultiOutTradeLog(PackagesNoticeData data,List<Trade> updateTraceTrades,PackagesNoticeRequest request,String prefix,String errorMsg,boolean isError ){
        List<PackagesNoticeRequest> packagesNoticeRequests = request.getPackagesNoticeRequests();
        if (CollectionUtils.isEmpty(packagesNoticeRequests)) {
            return;
        }
        List<Trade> trades = packagesNoticeRequests.stream().map(PackagesNoticeRequest::getTrade).collect(Collectors.toList());
        if(isError){
            Logs.debug(LogHelper.buildLog(data.staff, String.format("%s,%s,sid:%s",prefix, errorMsg, TradeUtils.toSidSet(trades))));
        }
        Set<Long> traceSids = TradeUtils.toSidSet(updateTraceTrades);
        for (Trade tempTrade : trades) {
            if(isError){
                if(!isSuccess(errorMsg)){
                    tempTrade.getOperations().put(OpEnum.TRADE_PACKAGES_NOTICE, String.format("%s:%s" ,prefix, errorMsg));
                }
                data.errors.put(tempTrade.getSid(), errorMsg);
            }else{
                tempTrade.getOperations().put(OpEnum.TRADE_PACKAGES_NOTICE, String.format("%s" ,prefix));
            }
            if(!traceSids.contains(tempTrade.getSid())){
                updateTraceTrades.add(tempTrade);
            }
        }
    }

    /**
     *  总的处理方法
     * @param staff
     * @param data
     */
     public void fillSplitTrade(Staff staff, PackagesNoticeData data){
         filterPddSplitTrade(staff,data);
     }
    /**
     *  拼多多拆单(拼多多只会按数量拆单)，多包裹物流上传，只有所有的子单都系统发货了的，子单才一起上传
     *  https://tb.raycloud.com/task/638029878cd464001e903dc6
     * @return
     */
    public void filterPddSplitTrade(Staff staff, PackagesNoticeData data) {
        if (CollectionUtils.isEmpty(data.trades)) {
            return;
        }
        // 拼多多订单，并且是拆单的
        Set<Long> pddSplitTradeSids = new HashSet<>();
        Set<Long> existSidSet = TradeUtils.toSidSet(data.trades);
        for (Trade trade : data.trades) {
            User user = data.getUser(trade);
            if (user == null) {
                Logs.debug(LogHelper.buildLog(staff, String.format(" 拼多多拆单,多包裹物流上传逻辑处理，userId=%s 找不到订单所属用户!", trade.getUserId())));
                continue;
            }
            if (Objects.equals(user.getSource(), CommonConstants.PLAT_FORM_TYPE_PDD)
                    && trade.getSplitSid() > 0) {
                pddSplitTradeSids.add(trade.getSplitSid());
            }
        }
        if (CollectionUtils.isEmpty(pddSplitTradeSids)) {
            return;
        }
        List<Trade> tradeList = tradeSearchService.queryBySplitSids(staff, true, pddSplitTradeSids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        List<Long> sids = TradeUtils.toSidList(tradeList);
        ListUtils.splitList(sids, 10).forEach(e -> {
            Logs.debug(LogHelper.buildLog(staff, String.format("0.拼多多订单，多包裹物流上传订单,sid=%s", e)));
        });
        List<UploadRecord> uploadRecords = uploadRecordBusiness.queryBySid(staff, sids.toArray(new Long[0]));
        // 上传成功单
        Set<Long> uploadSuccessSids = uploadRecords.stream().filter(uploadRecord -> Objects.equals(uploadRecord.getUploadStatus(),1)).map(UploadRecord::getSid).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(uploadSuccessSids)) {
            // 没有上传成功记录，无需处理
            Set<Long> sidSet = TradeUtils.toSidSet(tradeList);
            Iterator<Trade> iterator = data.trades.iterator();
            while (iterator.hasNext()) {
                Trade trade = iterator.next();
                if (sidSet.contains(trade.getSid())) {
                    data.errors.put(trade.getSid(), "拼多多订单，多包裹物流上传必须存在已经发货的订单!");
                    Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s,1.拼多多订单，多包裹物流上传必须存在已经发货的订单!", trade.getSid())));
                    iterator.remove();
                }
            }
            return;
        }
        Map<Long, List<Trade>> collect = tradeList.stream().collect(Collectors.groupingBy(Trade::getSplitSid));
        Set<Map.Entry<Long, List<Trade>>> entries = collect.entrySet();
        Set<Long> needRemoveTradeSids = new HashSet<>();
        List<Trade> needUploadTrades = new ArrayList<>();
        for (Map.Entry<Long, List<Trade>> entry : entries) {
            List<Trade> pddTrades = entry.getValue();
            long count = pddTrades.stream().filter(trade -> {
                boolean b = (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus())
                        || Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus())) && (!TradeExceptionUtils.hasUploadExcept(staff,trade));
                if (!b) {
                    Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s,系统状态=%s，平台状态=%s,v=%s!", trade.getSid(), trade.getSysStatus(), trade.getUnifiedStatus(),trade.getV())));
                }
                return b;
            }).count();
            Set<Long> pddTradeSids = TradeUtils.toSidSet(pddTrades);
            if (count != pddTrades.size()) {
                // 全部订单为交易完成或者卖家已发货时触发多包裹上传,存在非交易完成或者卖家已发货的单
                needRemoveTradeSids.addAll(pddTradeSids);
                Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s,拼多多拆分订单，多包裹物流上传 必须全为系统状已发货或者已完成的单!", pddTradeSids)));
                continue;
            }
            long outSidblankCount = pddTrades.stream().filter(trade -> StringUtils.isBlank(trade.getOutSid())).count();
            if (outSidblankCount != 0) {
                needRemoveTradeSids.addAll(pddTradeSids);
                Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s,拼多多拆分订单，多包裹物流上传 必须所有的单都存在快递模版运单号!", pddTradeSids)));
                continue;
            }
            boolean b = pddTrades.stream().allMatch(e -> uploadSuccessSids.contains(e.getSid()) && !TradeExceptionUtils.hasUploadExcept(staff,e));
            boolean b1 = pddTrades.stream().noneMatch(e -> uploadSuccessSids.contains(e.getSid()) && !TradeExceptionUtils.hasUploadExcept(staff,e));
            if (b || b1) {
                // 全部已发货或者全部未发货，跳过，至少存在一个已发货的子单，但是没有全部发货的
                Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s,拼多多拆分订单，多包裹物流上传 不存在全部已发货或者全部未发货，至少存在一个已发货的子单，但是没有全部发货的单!", pddTradeSids)));
                needRemoveTradeSids.addAll(pddTradeSids);
                continue;
            }


            for (Trade trade : pddTrades) {
                // 未上传发货，但是已经系统发货
                if (!uploadSuccessSids.contains(trade.getSid())) {
                    if (!existSidSet.contains(trade.getSid())) {
                        needUploadTrades.add(trade);
                    }
                } else {
                    // 已经上传发货，不需要在上传
                    needRemoveTradeSids.add(trade.getSid());
                }
            }
        }
        data.trades.addAll(needUploadTrades);
        Iterator<Trade> iterator = data.trades.iterator();
        while (iterator.hasNext()) {
            Trade trade = iterator.next();
            if (needRemoveTradeSids.contains(trade.getSid())) {
                Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s,2.拼多多拆分订单，多包裹物流上传必须所有拆单都已经系统发货或者已完成的单!", trade.getSid())));
                iterator.remove();
            }
        }
    }


    private void buildPddSplitPackagesNoticeRequests(PackagesNoticeData data, List<Trade> trades, Map<String, UserExpressTemplate> expressTemplateMap, Map<Long, ExpressCompany> expressCompanyMap) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        // 拼多多一单多包,拼多多多包裹物流，一个tid 多个运单号 fillTradesMultiOutSid 已经填充了多包裹的运单号，tid相同，按tid分组
        Map<String, List<Trade>> tradeNotSplitMap = trades.stream().collect(Collectors.groupingBy(Trade::getTid));
        Set<Map.Entry<String, List<Trade>>> tradeNotSplitEntries = tradeNotSplitMap.entrySet();
        for (Map.Entry<String, List<Trade>> tradeNotSplitEntry : tradeNotSplitEntries) {
            List<Trade> tradeList = tradeNotSplitEntry.getValue();
            fillPddPackagesNotice(data, tradeList, expressTemplateMap, expressCompanyMap);
        }
    }


    private void fillPddPackagesNotice(PackagesNoticeData data, List<Trade> tradeList, Map<String, UserExpressTemplate> expressTemplateMap, Map<Long, ExpressCompany> expressCompanyMap) {
        Boolean isCanUpload = true;
        Long tempSid = 0L;
        PackagesNoticeRequest request = data.buildPackagesNoticeRequest(tradeList.get(0));
        List<PackagesNoticeRequest> packagesNoticeRequests = new ArrayList<>();
        for (Trade trade : tradeList) {
            PackagesNoticeRequest packagesNoticeRequest = buildPddPackagesNoticeRequest(data, trade, expressTemplateMap, expressCompanyMap);
            if (packagesNoticeRequest == null) {
                isCanUpload = false;
                tempSid = trade.getSid();
                break;
            }
            packagesNoticeRequests.add(packagesNoticeRequest);
        }
        if (CollectionUtils.isEmpty(packagesNoticeRequests) || !isCanUpload) {
            Logs.debug(LogHelper.buildLog(data.staff, String.format("%s 拼多多拆单多包裹物流或者一包多单上传失败，%s %s", TradeUtils.toSidSet(tradeList), tempSid, data.errors.get(tempSid))));
            return;
        }
        request.setPackagesNoticeRequests(packagesNoticeRequests);
        data.requestList.add(request);
    }

    private PackagesNoticeRequest buildPddPackagesNoticeRequest(PackagesNoticeData data, Trade trade, Map<String, UserExpressTemplate> expressTemplateMap, Map<Long, ExpressCompany> expressCompanyMap) {
        String tplKey = UploadUtils.createTplKey(trade.getTemplateId(), trade.getTemplateType());
        UserExpressTemplate tpl = expressTemplateMap.get(tplKey);
        //找不到快递模板或者找不到快递公司的需要移除掉
        if (tpl == null || tpl.getExpressId() == null) {
            data.errors.put(trade.getSid(), "找不到快递模板");
            return null;
        }
        ExpressCompany ec = expressCompanyMap.get(tpl.getExpressId());
        if (ec == null) {
            data.errors.put(trade.getSid(), "找不到对应的快递公司");
            return null;
        }
        PackagesNoticeRequest packagesNoticeRequest = new PackagesNoticeRequest();
        packagesNoticeRequest.setExpressCompany(ec);
        packagesNoticeRequest.setTrade(trade);
        //部分平台需要设置物流映射
        if (ExpressMappingCacheKeyEnum.getBySource(data.getUser(trade).getSource()) != null) {
            packagesNoticeRequest.setExpressCompanyMapping(data.getExpressCompanyMappingMap(trade, expressCompanyMappingService).get(ec.getId()));
        }
        return packagesNoticeRequest;

    }

    private String getErrorMsg(PlatformResponse result) {
        String errorMsg = null;
        if(result==null){
            errorMsg = "平台响应为空";
        }else if(StringUtils.isNotBlank(result.getSubMsg())){
            errorMsg = result.getSubMsg();
        }else {
            errorMsg = result.getMsg();
        }
        if (errorMsg != null && errorMsg.length() > 1024) {
            errorMsg = errorMsg.substring(0, 1024);
        }
        return errorMsg;
    }

    private static class PackagesNoticeData {

        PackagesNoticeData(Staff staff, List<Trade> trades) {
            this.trades = trades;
            this.staff = staff;
        }

        User getUser(Trade trade){
            if (userIdMap.containsKey(trade.getUserId())){
                return userIdMap.get(trade.getUserId());
            }
            User user = staff.getUserByUserId(trade.getUserId());
            userIdMap.put(trade.getUserId(), user);
            return user;
        }

        Map<Long, ExpressCompanyMapping> getExpressCompanyMappingMap(Trade trade, IExpressCompanyMappingService expressCompanyMappingService){
            String source = getUser(trade).getSource();
            if(sourceExpressMappingMap.containsKey(source)){
                return sourceExpressMappingMap.get(source);
            }
            ExpressCompanyMappingBO expressCompanyMappingBO = new ExpressCompanyMappingBO();
            expressCompanyMappingBO.setSource(source);
            Map<Long, ExpressCompanyMapping> expressMappingMap = expressCompanyMappingService.getExpressMappingMap(expressCompanyMappingBO);
            sourceExpressMappingMap.put(source, expressMappingMap);
            return expressMappingMap;
        }

        PackagesNoticeRequest buildPackagesNoticeRequest(Trade trade){
            PackagesNoticeRequest request = new PackagesNoticeRequest();
            request.setTrade(trade);
            return request;
        }

        private final Staff staff;
        /**
         * 需要操作的订单
         */
        private final List<Trade> trades;
        /**
         * 通过userId获取user
         */
        private final Map<Long,User> userIdMap = new HashMap<>();
        /**
         * 通过source获取expressMapping
         */
        private final Map<String, Map<Long,ExpressCompanyMapping>> sourceExpressMappingMap = new HashMap<>();
        /**
         * 不符合条件的订单号及错误信息
         */
        private final Map<Long, String> errors = new HashMap<>();
        /**
         * 请求体
         */
        private final List<PackagesNoticeRequest> requestList = new ArrayList<>();

        private final Map<Long, List<MultiPacksPrintTradeLog>> sidMultiLogMap = new HashMap<>();
    }

    private boolean isSuccess(String errorMsg){
        boolean success = StringUtils.isNotBlank(errorMsg) && errorMsg.indexOf("已上传额外运单号") != -1;
        return success;
    }
}

package com.raycloud.dmj.business.modify;

import com.google.common.collect.Lists;
import com.raycloud.dmj.dms.domain.basic.QueryDmsPriceSourceEnum;
import com.raycloud.dmj.dms.domain.dto.DmsOrderPriceDto;
import com.raycloud.dmj.dms.request.QueryDmsPriceOrderInfo;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OpVEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trades.ModifyData;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.payment.util.BigDecimalWrapper;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.PaymentUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import org.apache.commons.collections.CollectionUtils;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2019-08-08 22:27
 * @Description 订单赠品管理
 */
@Service
public class TradeGiftUpdateBusiness {

    @Resource
    IDmsTradeService dmsTradeService;
    @Resource
    ModifyParentBusiness modifyParentBusiness;
    @Resource
    TradeUpdateBusiness tradeUpdateBusiness;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    ITradeConfigService tradeConfigService;

    @Transactional
    public ModifyData updateTradeGift(final Staff staff, final Trade trade) {
        List<Order> frontGiftOrders = TradeUtils.getOrders4Trade(trade);

        Trade existTrade = tradeSearchService.queryBySid(staff, true, trade.getSid());
        Assert.isTrue(!TradeUtils.isPlatformFxSource(existTrade), String.format("天猫平台分销无法进行此操作[sid:%s]", existTrade.getSid()));
        //奇门分销单子单独处理
        boolean isQimenFxSource = TradeUtils.isQimenFxSource(existTrade);
        if (isQimenFxSource) {
            TradeConfigNew qimenFxTradeMatchGift = TradeConfigGetUtil.get(staff, TradeConfigEnum.QIMEN_FX_TRADE_MATCH_GIFT);
            //开关未打开
            if (qimenFxTradeMatchGift == null || !qimenFxTradeMatchGift.isOpen()) {
                Assert.isTrue(false, String.format("奇门供销订单无法进行此操作,请开启系统设置-奇门分销版订单支持赠品规则[sid:%s]", existTrade.getSid()));
            }
        }

        Assert.isTrue(!CommonConstants.PLAT_FORM_TYPE_FXG_DF.equals(existTrade.getSource()), String.format("抖音厂商代发订单无法进行此操作[sid:%s]", existTrade.getSid()));
        Assert.isTrue(!TradeUtils.isTbDfTrade(existTrade), String.format("淘宝厂商代发订单无法进行此操作[sid:%s]", existTrade.getSid()));
        Assert.isTrue(!TradeUtils.isKsDfTrade(existTrade), String.format("快手代发订单无法进行此操作[sid:%s]", existTrade.getSid()));

        initOrder(frontGiftOrders);
        List<Order> existOrders = TradeUtils.getOrders4Trade(existTrade);
        for (Order order : existOrders) {
            if (order.getGiftNum() == null || order.getGiftNum() == 0) {
                frontGiftOrders.add(order);
            }
        }
        TradeUtils.setOrders(existTrade, frontGiftOrders);

        //https://gykj.yuque.com/entavv/xb9xi5/ypfuis
        handleGiftOrderSalePrice(staff, Lists.newArrayList(existTrade));

        return tradeUpdateBusiness.update(staff, existTrade, true, false);
    }

    private void initOrder(List<Order> frontGiftOrders) {
        //前端传值不规范，比如：***。这里初始化一下。
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(frontGiftOrders)) {
            for (Order frontGiftOrder : frontGiftOrders) {
                frontGiftOrder.setSalePrice("0");
                frontGiftOrder.setSaleFee("0");
            }
        }
    }

    /**
     * 分销订单，赠品分销价格引用线上分销价
     * https://gykj.yuque.com/entavv/xb9xi5/ypfuis
     * @param staff
     * @param trades
     */
    public void handleGiftOrderSalePrice(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        if (!Objects.isNull(tradeConfig.getGiftOrderSalePriceConfig())
                && tradeConfig.getGiftOrderSalePriceConfig().equals(1)) {

            //设置order的userId，与trade保持一致
            for (Trade trade : trades) {
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                if (!CollectionUtils.isEmpty(orders)) {
                    for (Order order : orders) {
                        order.setUserId(trade.getUserId());
                    }
                }
            }

            modifyParentBusiness.setOrderSaleFee(staff, TradeUtils.getGiftOrders4Trade(trades));
            //重新计算trade的分销金额
            for (Trade trade : trades) {
                trade.setSaleFee(TradeUtils.calculateTradeSaleFee(trade));
            }
        }
        //赠品匹配分销价
        handleGiftPrice(staff, trades);
    }

    /**
     * 赠品匹配分销价
     *
     * @param staff
     * @param trades
     */
    public void handleGiftPrice(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> matchTrades = trades.stream()
                .filter(trade -> TradeUtils.isQimenFxSource(trade)
                        && TradeUtils.getOrders4Trade(trade).stream().anyMatch(Order::isGift))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchTrades)) {
            return;
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        if (tradeConfig != null && tradeConfig.isGiftRecalculateCashFlowEnabled()) {
            Map<Long, DmsOrderPriceDto> dmsOrderPriceDtoMap = matchPrice(matchTrades);
            for (Trade trade : matchTrades) {
                List<Order> orderList = TradeUtils.getOrders4Trade(trade);
                for (Order order : orderList) {
                    if (!order.isGift()) {
                        continue;
                    }
                    DmsOrderPriceDto dmsOrderPriceDto = dmsOrderPriceDtoMap.get(order.getOid());
                    if (dmsOrderPriceDto != null) {
                        Double dmsOrderPrice = dmsOrderPriceDto.getPrice();
                        if (dmsOrderPrice == null) {
                            dmsOrderPrice = 0.0D;
                        }
                        String total = new BigDecimalWrapper(dmsOrderPrice).multiply(order.getNum()).getString();
                        order.setPayment(total);
                        order.setPayAmount(total);
                        trade.addOpV(OpVEnum.ORDER_MATCH_FX_PRICE);
                    }
                }
                if (trade.hasOpV(OpVEnum.ORDER_MATCH_FX_PRICE)) {
                    trade.setPayment(MathUtils.toString(PaymentUtils.calculateQimenFxAmount(trade, false, false)));
                }
            }
        }
    }

    private Map<Long, DmsOrderPriceDto> matchPrice(List<Trade> trades) {
        Map<Long, Map<Long, List<QueryDmsPriceOrderInfo>>> destSourceMap = new HashMap<>();
        trades.forEach(trade -> {
            Long destId = trade.getCompanyId();
            Long sourceId = trade.getSourceId();
            List<QueryDmsPriceOrderInfo> infos = convertTradeToPriceOrderInfo(trade);
            if (CollectionUtils.isNotEmpty(infos)) {
                destSourceMap
                        .computeIfAbsent(destId, k -> new HashMap<>())
                        .computeIfAbsent(sourceId, k -> new ArrayList<>())
                        .addAll(infos);
            }
        });
        Map<Long, DmsOrderPriceDto> priceDtoMap = new HashMap<>();
        destSourceMap.forEach((destId, sourceMap) ->
                sourceMap.forEach((sourceId, infos) ->
                        Lists.partition(infos, 10).forEach(partition -> {
                            List<DmsOrderPriceDto> dtos = dmsTradeService.queryDmsPrice(destId, sourceId, partition);
                            Optional.ofNullable(dtos).ifPresent(list -> list.forEach(dto -> priceDtoMap.put(dto.getOid(), dto)));
                        }))
        );
        return priceDtoMap;
    }

    private List<QueryDmsPriceOrderInfo> convertTradeToPriceOrderInfo(Trade trade) {
        return TradeUtils.getOrders4Trade(trade).stream()
                .filter(Order::isGift)
                .map(order -> buildPriceOrderInfo(trade, order))
                .collect(Collectors.toList());
    }

    private QueryDmsPriceOrderInfo buildPriceOrderInfo(Trade trade, Order order) {
        QueryDmsPriceOrderInfo info = new QueryDmsPriceOrderInfo();
        info.setAuditPushTime(new Date());
        info.setCreateTime(trade.getCreated());
        info.setPayTime(trade.getPayTime());
        info.setOid(order.getOid());
        info.setOuterId(OrderUtils.getFxTrueOuterId(trade, order));
        info.setOrderNum(order.getNum());
        info.setSource(QueryDmsPriceSourceEnum.TRADE);
        return info;
    }
}

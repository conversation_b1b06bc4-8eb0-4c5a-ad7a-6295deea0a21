package com.raycloud.dmj.business.fx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.modify.TradeCalculateTheoryPostFeeBusiness;
import com.raycloud.dmj.business.trade.SysTradeDmsBusiness;
import com.raycloud.dmj.dms.request.*;
import com.raycloud.dmj.dms.response.*;
import com.raycloud.dmj.dms.domain.dto.DmsDistributorInfoDto;
import com.raycloud.dmj.dms.domain.dto.DmsFreightCostDto;
import com.raycloud.dmj.dms.domain.dto.DmsFreightMatchDto;
import com.raycloud.dmj.dms.domain.finance.DmsCashFlowTypeEnum;
import com.raycloud.dmj.dms.request.DmsGenerateCashFlowRequest;
import com.raycloud.dmj.dms.request.DmsOperateBalanceRequest;
import com.raycloud.dmj.dms.request.DmsRefundGenerateCashFlowRequest;
import com.raycloud.dmj.dms.request.FlowRequest;
import com.raycloud.dmj.dms.response.DmsGenerateCashFlowResponse;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.payment.util.BigDecimalWrapper;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.trades.ITradeQueryService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.LogKit;
import com.raycloud.dmj.warehouse.services.ITradeWarehouseService;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/8/30 11:38
 */
@Service
public class QiMenFxCashFlowBusiness {

    private final Logger logger = Logger.getLogger(this.getClass());
    @Resource
    IEventCenter eventCenter;
    @Resource
    IDmsTradeService dmsTradeService;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    ITradeWarehouseService tradeWarehouseService;
    @Resource
    SysTradeDmsBusiness sysTradeDmsBusiness;
    @Resource
    ITradeQueryService tradeQueryService;
    @Resource
    TradeCalculateTheoryPostFeeBusiness theoryPostFeeBusiness;

    public void invalidAsync(Staff staff, List<Trade> trades) {
        try {
            if (CollectionUtils.isNotEmpty(trades)) {
                List<Object> requests = new ArrayList<>(buildDmsRefundGenerateCashFlowRequest(staff, trades));
                if (CollectionUtils.isNotEmpty(requests)) {
                    eventCenter.fireEvent(this, new EventInfo("dms.operate.lock.balance").setArgs(new Object[]{staff, requests, 0}), null);
                }
            }
        } catch (Exception ex) {
            logger.error("作废资金流水失败,sids:" + StringUtils.join(TradeUtils.toSids(trades), ","), ex);
        }
    }

    public CashFlowData invalid(Staff staff, List<Trade> trades) {
        CashFlowData cashFlowData = new CashFlowData();
        List<DmsRefundGenerateCashFlowRequest> requests = buildDmsRefundGenerateCashFlowRequest(staff, trades);
        Map<String, List<DmsRefundGenerateCashFlowRequest>> group = requests.stream()
                .collect(Collectors.groupingBy(
                        request -> request.getSupplierCompanyId() + "_" + request.getDistributorCompanyId(),
                        Collectors.toList()
                ));
        group.forEach((key, value) -> {
            try {
                String[] supplierDistributorCompanyId = key.split("_");
                Long supplierCompanyId = Long.parseLong(supplierDistributorCompanyId[0]);
                Long distributorCompanyId = Long.parseLong(supplierDistributorCompanyId[1]);
                DmsRefundCashFlowBatchRequest request = new DmsRefundCashFlowBatchRequest();
                request.setSupplierCompanyId(supplierCompanyId);
                request.setDistributorCompanyId(distributorCompanyId);
                request.setStaff(staff);
                request.setRequests(value);
                LogKit.took(() -> {
                    DmsRefundCashFlowBatchResponse response = dmsTradeService.refundDmsBalanceBatch(request);
                    if (response != null && response.getDetails() != null) {
                        for (DmsGenerateCashFlowResponse detail : response.getDetails()) {
                            long sid = 0;
                            if (StringUtils.isNotEmpty(detail.getSysOrderNumber())) {
                                sid = Long.parseLong(detail.getSysOrderNumber());
                            }
                            if (Integer.valueOf(1).equals(detail.getStatus())) {
                                cashFlowData.successSids.add(sid);
                            } else {
                                cashFlowData.errorMap.put(sid, detail.getErrorMsg());
                            }
                        }
                    }
                    Logs.info(LogHelper.buildLog(staff, "作废资金流水响应信息:" + JSONObject.toJSONString(response)));
                    return null;
                }, staff, logger);
            } catch (Exception ex) {
                logger.error("作废资金流水失败,sids:" + StringUtils.join(value.stream().map(DmsRefundGenerateCashFlowRequest::getSysOrderNumber).collect(Collectors.toList()), ","), ex);
            }
        });
        return cashFlowData;
    }

    private List<DmsRefundGenerateCashFlowRequest> buildDmsRefundGenerateCashFlowRequest(Staff staff, List<Trade> trades) {
        List<DmsRefundGenerateCashFlowRequest> requests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            requests = trades.stream().map(trade -> QiMenFxCashFlowUtils.buildDmsRefundRequest(staff, trade)).collect(Collectors.toList());
        }
        return requests;
    }

    public CashFlowData apply(Staff staff, List<Trade> trades, Integer type, boolean verifyBalance) {
        CashFlowData cashFlowData = new CashFlowData();
        if (CollectionUtils.isNotEmpty(trades)) {
            Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(trades);
            Map<Long, List<Trade>> mergeSids = getMergeSids(staff, trades);
            Map<String, Object> party3Config = queryParty3Config(staff);
            Integer node = getNode(party3Config);
            List<DmsOperateBalanceRequest> requests = trades.stream().map(trade -> QiMenFxCashFlowUtils.buildDmsRequest(staff, mergeSids, QiMenFxCashFlowUtils.getOptType(trade, type, node), verifyBalance, trade))
                    .collect(Collectors.toList());
            Map<String, List<DmsOperateBalanceRequest>> group = requests.stream()
                    .collect(Collectors.groupingBy(
                            request -> request.getSupplierCompanyId() + "_" + request.getDistributorCompanyId(),
                            Collectors.toList()
                    ));
            group.forEach((key, value) -> {
                try {
                    String[] supplierDistributorCompanyId = key.split("_");
                    Long supplierCompanyId = Long.parseLong(supplierDistributorCompanyId[0]);
                    Long distributorCompanyId = Long.parseLong(supplierDistributorCompanyId[1]);
                    DmsOperateBalanceBatchRequest request = new DmsOperateBalanceBatchRequest();
                    request.setSupplierCompanyId(supplierCompanyId);
                    request.setDistributorCompanyId(distributorCompanyId);
                    request.setStaff(staff);
                    request.setRequests(value);
                    LogKit.took(() -> {
                        DmsOperateBalanceBatchResponse response = dmsTradeService.operateOrLockDmsBalanceBatch(request);
                        if (response != null && response.getDetails() != null) {
                            for (DmsGenerateCashFlowResponse detail : response.getDetails()) {
                                long sid = 0;
                                if (StringUtils.isNotEmpty(detail.getSysOrderNumber())) {
                                    sid = Long.parseLong(detail.getSysOrderNumber());
                                }
                                Trade trade = tradeMap.get(sid);
                                if (null != trade && Integer.valueOf(1).equals(detail.getStatus())) {
                                    //操作完成或重复扣款
                                    cashFlowData.successSids.add(sid);
                                } else if (null != trade &&
                                        Integer.valueOf(0).equals(detail.getStatus())
                                        && !detail.isBalanceEnough()) {
                                    //分销商资金不足
                                    cashFlowData.errorMap.put(trade.getSid(), "分销商余额不足，分销商付款失败");
                                }
                            }
                        }
                        Logs.info(LogHelper.buildLog(staff, "申请资金流水响应信息:" + JSONObject.toJSONString(response)));
                        return null;
                    }, staff, logger);
                } catch (Exception ex) {
                    logger.error("申请资金流水失败,sids:" + StringUtils.join(value.stream().map(DmsOperateBalanceRequest::getSysOrderNumber).collect(Collectors.toList()), ","), ex);
                }
            });
        }
        Set<Long> successSidsSet = new HashSet<>(cashFlowData.successSids);
        for (Trade trade : trades) {
            if (!successSidsSet.contains(trade.getSid()) && !cashFlowData.errorMap.containsKey(trade.getSid())) {
                cashFlowData.errorMap.put(trade.getSid(), "分销商余额不足，分销商付款失败");
            }
        }
        return cashFlowData;
    }

    public void applyAsync(Staff staff, List<Trade> trades, Integer type, boolean verifyBalance) {
        try {
            Map<Long, List<Trade>> mergeSids = getMergeSids(staff, trades);
            Map<String, Object> party3Config = queryParty3Config(staff);
            Integer node = getNode(party3Config);
            List<Object> requests = trades.stream().map(trade -> QiMenFxCashFlowUtils.buildDmsRequest(staff, mergeSids, QiMenFxCashFlowUtils.getOptType(trade, type, node), verifyBalance, trade))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(requests)) {
                eventCenter.fireEvent(this, new EventInfo("dms.operate.lock.balance").setArgs(new Object[]{staff, requests, 1}), null);
            }
        } catch (Exception ex) {
            logger.error("申请资金流水失败,sids:" + StringUtils.join(TradeUtils.toSids(trades), ","), ex);
        }
    }

    public Map<Long, List<Trade>> getMergeSids(Staff staff, List<Trade> trades) {
        Map<Long, List<Trade>> mergeSids = new HashMap<>();
        Set<Long> mergeSidSet = new HashSet<>();
        for (Trade trade : trades) {
            if (TradeUtils.isMerge(trade)) {
                mergeSidSet.add(trade.getMergeSid());
            }
        }
        if (CollectionUtils.isNotEmpty(mergeSidSet)) {
            List<Trade> mergeTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, false, true, mergeSidSet.toArray(new Long[0]));
            if (CollectionUtils.isNotEmpty(mergeTrades)) {
                for (Trade trade : mergeTrades) {
                    mergeSids.computeIfAbsent(trade.getMergeSid(), (k) -> new ArrayList<>()).add(trade);
                }
            }
        }
        return mergeSids;
    }

    private List<Trade> filter(Staff staff, List<Trade> trades,int type) {
        List<Trade> filterTrades = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            if ((!TradeUtils.isQimenFxSource(trade))
                    || TradeUtils.isReissue(trade)) {
                continue;
            }
            if ((!TradeUtils.isCancel(trade) && !(type == 6 && Objects.equals(Trade.SYS_STATUS_CLOSED, trade.getSysStatus()))) && QiMenFxCashFlowUtils.unMatched(trade)) {
                //商品未匹配
                continue;
            }
            filterTrades.add(trade);
        }
        return filterTrades;
    }

    public QiMenFxCashFlowData validate(Staff staff, QiMenFxCashFlowParam param) {
        QiMenFxCashFlowData cashFlowData = new QiMenFxCashFlowData();
        Map<String, Object> party3Config = queryParty3Config(staff);
        boolean openCashFlow = openCashFlow(party3Config);
        List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, param.getSids().toArray(new Long[0]));
        if (CollectionUtils.isEmpty(tradeList)) {
            return cashFlowData;
        }
        if (!openCashFlow) {
            calculateTradePostFee(staff,TradeUtils.getQimenFxTrades(tradeList));
            Logs.debug(LogHelper.buildLog(staff, "没有开启分销资金流水配置 sids:" + param.getSids()));
            return cashFlowData;
        }
        tradeList = filter(staff, tradeList, param.getType());
        if (CollectionUtils.isNotEmpty(tradeList)) {
            for (Trade trade : tradeList) {
                if (QiMenFxCashFlowUtils.ifFxWaitPay(trade)) {
                    cashFlowData.fxWaitPays.add(trade);
                } else if (QiMenFxCashFlowUtils.ifFPTrade(trade)) {
                    cashFlowData.fpTrades.add(trade);
                } else if (QiMenFxCashFlowUtils.ifQiMenTrade(trade)) {
                    cashFlowData.qmTrades.add(trade);
                } else {
                    Logs.debug(LogHelper.buildLog(staff, "未知的流水类型,sid:" + trade.getSid()));
                }
            }
        }
        return cashFlowData;
    }

    private void handleQMFPCashFlow(Staff staff, List<Trade> qmFpTrades, Integer type, String reCalGxPostFee) {
        if (CollectionUtils.isEmpty(qmFpTrades) || !(QiMenFxCashFlowUtils.isAuditType(type) || QiMenFxCashFlowUtils.isConsignType(type) || Integer.valueOf(5).equals(type))) {
            //当前仅开放审核、发货节点，强推订单的流水重算
            return;
        }
        List<Trade> resumeTrades = new ArrayList<>();
        List<Trade> reCalTrades = new ArrayList<>();
        //是否来自取消合单
        boolean unMerge = Integer.valueOf(5).equals(type);
        for (Trade trade : qmFpTrades) {
            //取消合单流程，走重算流程
            if (unMerge) {
                reCalTrades.add(trade);
                continue;
            }
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                //合单类型，走重算流程
                reCalTrades.add(trade);
            } else {
                //非合单，走归还流程
                resumeTrades.add(trade);
            }
        }
        if (reCalGxPostFee == null || "1".equals(reCalGxPostFee)) {
            //记录原始运费
            qmFpTrades.forEach(t -> t.setOldPostFee(t.getPostFee()));
            calculateTradePostFee(staff, qmFpTrades);
        }
        //重算流水（运费 + 分销价）
        if (CollectionUtils.isNotEmpty(reCalTrades)) {
            Logs.info(LogHelper.buildLog(staff, String.format("强推奇门单，重算流程开始: type=%s; sid=%s", type, TradeUtils.toSidList(reCalTrades))));
            sysTradeDmsBusiness.reCalculateCashFlowByInvalid(staff, reCalTrades, false);
        }

        //归还流水（只计算运费的增减）
        if (CollectionUtils.isNotEmpty(resumeTrades)) {
            Logs.info(LogHelper.buildLog(staff, String.format("强推奇门单，归还流程开始: type=%s; sid=%s", type, TradeUtils.toSidList(resumeTrades))));
            resumeCashFlowByPostFee(staff, resumeTrades, type);
        }
    }

    /**
     * 计算运费的归还流水
     *
     * @param staff
     * @param resumeTrades
     * @param type         流水类型
     */
    private void resumeCashFlowByPostFee(Staff staff, List<Trade> resumeTrades, Integer type) {
        Map<Long, Map<Long, List<DmsGenerateCashFlowRequest>>> resume = new HashMap<>();
        for (Trade trade : resumeTrades) {
            //计算运费的变化值
            BigDecimalWrapper cashFlow = new BigDecimalWrapper(trade.getOldPostFee()).subtract(trade.getPostFee());
            if (cashFlow.get().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            DmsGenerateCashFlowRequest request = CashFlowUtils.getCashFlowRequestInstance(trade);
            request.setAmount(cashFlow.getDouble());
            request.setFlowType(DmsCashFlowTypeEnum.orderModify.getValue());
            //不校验余额，会扣成负数
            request.setCheckCashBalance(false);
            if (QiMenFxCashFlowUtils.isAuditType(type)) {
                request.setRemark("订单审核后重算运费");
            }
            if (QiMenFxCashFlowUtils.isConsignType(type)) {
                request.setRemark("订单系统发货后重算运费");
            }
            resume.computeIfAbsent(request.getSupplierCompanyId(), k -> new HashMap<>()).computeIfAbsent(request.getDistributorCompanyId(), t -> new ArrayList<>()).add(request);
        }
        if (MapUtils.isNotEmpty(resume)) {
            FlowRequest consumeRequest = new FlowRequest();
            consumeRequest.setFlowType(DmsCashFlowTypeEnum.orderModify);
            consumeRequest.setSupplierDistributorResumeFlowListMap(resume);
            dmsTradeService.resumeFlow(staff, consumeRequest);
        }
    }

    public void handleQMCashFlow(Staff staff, QiMenFxCashFlowParam param) {
        try {
            Logs.info(LogHelper.buildLog(staff, "处理奇门资金流水,参数:" + param.toString()));
            QiMenFxCashFlowData fxCashFlowData = validate(staff, param);
            Map<Long, Integer> reCalCashFlowConfig = getReCalCashFlowConfigMap(staff, fxCashFlowData.getTrades());
            //重算运费运费
            calculateTradePostFee(staff, fxCashFlowData.getTrades(), param, reCalCashFlowConfig);
            List<Trade> qmTrades = fxCashFlowData.getTrades(0);
            if (CollectionUtils.isNotEmpty(qmTrades)) {
                doHandle(staff, param, qmTrades, reCalCashFlowConfig);
            }
            List<Trade> fpTrades = fxCashFlowData.getTrades(1);
            if (CollectionUtils.isNotEmpty(fpTrades)) {
                handleQMFPCashFlow(staff, fpTrades, param.getType(), param.getReCalGxPostFee());
            }
        } catch (Exception ex) {
            Logs.error(LogHelper.buildErrorLog(staff, ex, "处理奇门资金流水失败:" + ex.getMessage()));
        }
    }

    public void doHandle(Staff staff, QiMenFxCashFlowParam param, List<Trade> trades, Map<Long, Integer> reCalCashFlowConfigMap) {
        if (CollectionUtils.isNotEmpty(trades)) {
            if (QiMenFxCashFlowUtils.isRefund(param.getType())) {
                invalidAsync(staff, trades);
            } else {
                Map<Long, List<Trade>> sourceIdMap = trades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
                for (Long sourceId : sourceIdMap.keySet()) {
                    Integer reCalCashFlowConfig = reCalCashFlowConfigMap.get(sourceId);
                    applyAsync(staff, sourceIdMap.get(sourceId), param.getType(), reCalCashFlowConfig == 1);
                }
            }
        }
    }

    public void calculateTradePostFee(Staff staff, List<Trade> trades, QiMenFxCashFlowParam param, Map<Long, Integer> reCalCashFlowConfigMap) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> validateTrades = Lists.newArrayListWithCapacity(trades.size());
        Map<Long, List<Trade>> map = trades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
        for (Long sourceId : map.keySet()) {
            List<Trade> tradeList = map.get(sourceId);
            Integer config = reCalCashFlowConfigMap.getOrDefault(sourceId, 2);
            //发货不重算运费
            boolean reCalPostFee = (config != 1 || !param.getType().equals(2)) && config > 0;
            for (Trade trade : tradeList) {
                if (trade.getIfFxForcePushTrade() || trade.getIfFxWaitPay()
                        && !QiMenFxCashFlowUtils.ifPayLock(trade)) {
                    continue;
                }
                if (ifReCalculatePostFee(staff, trade, reCalPostFee, param.getType(), param.getReCalGxPostFee())) {
                    validateTrades.add(trade);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(validateTrades)) {
            calculateTradePostFee(staff, validateTrades);
        }
    }

    public void calculateTradePostFee(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> validateTrades = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            //未匹配商品,不匹配运费
            if (!TradeUtils.isCancel(trade) && QiMenFxCashFlowUtils.unMatched(trade)) {
                continue;
            }
            validateTrades.add(trade);
        }
        if (CollectionUtils.isEmpty(validateTrades)) {
            return;
        }
        theoryPostFeeBusiness.recalGxPostFee(staff, validateTrades, null);
        List<DmsFreightCostDto> dmsFreightCostDtoList = trades.stream().filter(trade -> StringUtils.isNotEmpty(trade.getPostFee())
                && StringUtils.isNotEmpty(trade.getGxFreightRuleName())).map(trade -> {
            DmsFreightCostDto dmsFreightCostDto = new DmsFreightCostDto();
            dmsFreightCostDto.setSid(trade.getSid());
            dmsFreightCostDto.setCost(new BigDecimalWrapper(trade.getPostFee()).get());
            dmsFreightCostDto.setFreightRuleName(trade.getGxFreightRuleName());
            return dmsFreightCostDto;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dmsFreightCostDtoList)) {
            eventCenter.fireEvent(this, new EventInfo("trade.fx.postfee.update").setArgs(new Object[]{staff, dmsFreightCostDtoList}), null);
        }
    }

    public void calculateTradePostFee(Staff staff, List<Long> sids,boolean containMerge) {
        if(CollectionUtils.isEmpty(sids)) {
            return;
        }
        calculateTradePostFee(staff,tradeSearchService.queryBySidsContainMergeTrade(staff, true, sids.toArray(new Long[0])));
    }

    /**
     * 1.开启重算资金流水
     * 2.强推未付款
     * 3.接收节点
     * 4.重算运费
     *
     * @param staff
     * @param trade
     * @param reCalCashFlow
     * @param type
     * @param reCalGxPostFee
     * @return
     */
    boolean ifReCalculatePostFee(Staff staff, Trade trade, boolean reCalCashFlow, Integer type, String reCalGxPostFee) {
        return reCalCashFlow
                || ((trade.getIfFxForcePushTrade() || trade.getIfFxWaitPay()) && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_WAITPAY))
                || type == 0
                || Objects.equals("1", reCalGxPostFee);
    }

    /**
     * 第三方接口配置:上游推送的发货单统计到资金流水中
     * @param config
     * @return
     */
    public boolean openCashFlow(Map<String, Object> config) {
        //快麦WMS分销模式 上游推送的发货单统计到资金流水中 0 不统计 1 统计 默认 不统计
        return (boolean) config.getOrDefault("OpenFXAmountRecord", false);
    }

    public Map<String, Object> queryParty3Config(Staff staff) {
        try {
            Map<String, Object> config = tradeWarehouseService.queryParty3Config(staff);
            if (config == null) {
                Logs.info(LogHelper.buildLog(staff, "获取三方仓配置失败，跳过！"));
                return Maps.newHashMap();
            }
            return config;
        } catch (Exception ex) {
            Logs.debug(LogHelper.buildErrorLog(staff, ex, ex.getMessage()));
        }
        return Maps.newHashMap();
    }

    public Integer getNode(Map<String, Object> config) {
        return (Integer) config.getOrDefault("DMSOperatorNode", 2);
    }

    /**
     * 是否重算资金流水
     * 0.不重算
     * 1.重算资金流水+余额不足时，不扣减资金+打异常
     * 2.重算资金流水，余额不足时，扣减资金
     *
     * @param party3Config
     * @param distributorId
     * @return
     */
    public int getReCalCashFlowConfig(Map<String, Object> party3Config, Long distributorId) {
        Object fxReCalCashFlowConfig = party3Config.get("FxReCalCashFlowConfig");
        Object fxRetainCashFlowConfig = party3Config.get("FxRetainCashFlowConfig");
        Set<Long> fxReCalCashFlowConfigSet = new HashSet<>();
        Set<Long> fxRetainCashFlowConfigSet = new HashSet<>();
        if (null != fxReCalCashFlowConfig) {
            fxReCalCashFlowConfigSet.addAll((List<Long>) fxReCalCashFlowConfig);
        }
        if (null != fxRetainCashFlowConfig) {
            fxRetainCashFlowConfigSet.addAll((List<Long>) fxRetainCashFlowConfig);
        }
        if (fxReCalCashFlowConfigSet.size() == 0 && fxRetainCashFlowConfigSet.size() == 0) {
            return 2;//重算
        }
        if (fxReCalCashFlowConfigSet.contains(distributorId)) {
            return 1;//重算+分销商付款异常+反审核
        }
        if (fxRetainCashFlowConfigSet.contains(distributorId)) {
            return 0;//不重算
        }
        return 2;
    }

    /**
     * 获取三方仓配置
     *
     * @param staff
     * @param trades
     * @return
     */
    public Map<Long, Integer> getReCalCashFlowConfigMap(Staff staff, List<Trade> trades) {
        Map<Long, Integer> reCalCashFlowConfigMap = new HashMap<>();
        Map<String, Object> party3Config = queryParty3Config(staff);
        Map<Long, List<Trade>> map = trades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
        for (Long sourceId : map.keySet()) {
            reCalCashFlowConfigMap.computeIfAbsent(sourceId, (k) -> getReCalCashFlowConfig(party3Config, sourceId));
        }
        return reCalCashFlowConfigMap;
    }

    /**
     * 校验奇门订单资金是否充足,返回错误信息
     *
     * @param staff
     * @param trades
     * @return
     */
    public Map<Long, Map<Long, String>> verifyBalance(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return null;
        }
        Map<String, Object> party3Config = queryParty3Config(staff);
        Map<Long, List<Trade>> sourceMap = new HashMap<>();
        Map<Long, Map<Long, String>> errorMap = new HashMap<>();
        Map<Long, Boolean> reCalCashFlowConfigMap = new HashMap<>();
        Map<Long, String> postFeeMap = new HashMap<>();
        trades.forEach(trade -> {
            if (TradeUtils.isQimenFxSource(trade)
                    && reCalCashFlowConfigMap.computeIfAbsent(trade.getSourceId(), (k) -> ifReCalCashFlow(party3Config, trade.getSourceId()))) {
                sourceMap.computeIfAbsent(trade.getSourceId(), (k) -> new ArrayList<>()).add(trade);
                postFeeMap.put(trade.getSid(), trade.getPostFee());
            }
        });
        if (sourceMap.size() == 0) {
            return errorMap;
        }
        List<Trade> tradeList = new ArrayList<>();
        sourceMap.values().forEach(tradeList::addAll);
        LogKit.took(() -> {
            calculateTradePostFee(staff, tradeList);
            return null;
        }, staff, logger);
        tradeQueryService.fillDestAndSourceName(tradeList);
        for (Long sourceId : sourceMap.keySet()) {
            List<Trade> gxTrades = sourceMap.get(sourceId);
            List<Trade> filterTrades = new ArrayList<>();
            for (Trade gxTrade : gxTrades) {
                if (!MathUtils.equals(gxTrade.getPostFee(), postFeeMap.get(gxTrade.getSid()))) {
                    filterTrades.add(gxTrade);
                }
            }
            if (filterTrades.size() == 0) {
                continue;
            }
            List<Trade> verifyTrades = tradeSearchService.queryBySids(staff, true, TradeUtils.toSids(filterTrades));
            CashFlowData cashFlowData = LogKit.took(() -> apply(staff, verifyTrades, getNode(party3Config) - 1, true), staff, logger);
            if (null != cashFlowData && cashFlowData.errorMap.size() > 0) {
                errorMap.computeIfAbsent(sourceId, (k) -> new HashMap()).putAll(cashFlowData.errorMap);
            }
        }
        return errorMap;
    }

    /**
     * 开启资金且开启重算资金
     *
     * @param party3Config
     * @param distributorId
     * @return
     */
    public boolean ifReCalCashFlow(Map<String, Object> party3Config, Long distributorId) {
        return openCashFlow(party3Config) && getReCalCashFlowConfig(party3Config, distributorId) == 1;
    }
}

package com.raycloud.dmj.services.trade.rule;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.dao.trade.rule.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.rematch.ReMatchContext;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.enums.*;
import com.raycloud.dmj.domain.trade.rule.*;
import com.raycloud.dmj.domain.trade.rule.history.TradeRuleConditionDetailHistory;
import com.raycloud.dmj.domain.trade.rule.history.TradeRuleConditionHistory;
import com.raycloud.dmj.domain.trade.rule.history.TradeRuleHistory;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyService;
import com.raycloud.dmj.item.search.dto.DmjItemDto;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.pt.*;
import com.raycloud.dmj.services.trade.merge.TradeMergeConfBusiness;
import com.raycloud.dmj.services.trade.rule.converter.TradeRuleConverter;
import com.raycloud.dmj.services.trades.TradeException;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.web.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trade.rule.TradeRuleConditionTypeEnum.EQUAL;
import static com.raycloud.dmj.services.trade.rule.TradeRuleMatchBusiness.*;

/**
 * @ClassName TradeRuleService
 * @Description 订单规则接口实现
 * <AUTHOR>
 * @Date 2024/4/18
 * @Version 1.0
 */
@Service
public class TradeRuleService implements ITradeRuleService {

    @Resource
    TradeRuleDao tradeRuleDao;

    @Resource
    TradeRuleConditionDao tradeRuleConditionDao;

    @Resource
    TradeRuleConditionFilterDao tradeRuleConditionFilterDao;

    @Resource
    TradeRuleConditionDetailDao tradeRuleConditionDetailDao;

    @Resource
    TradeRuleMatchLogDao tradeRuleMatchLogDao;

    @Resource
    TradeRuleHistoryBusiness tradeRuleHistoryBusiness;

    @Resource
    TradeRuleMatchContextBusiness tradeRuleMatchContextBusiness;

    @Resource
    TradeRuleMatchQuery tradeRuleMatchQuery;

    @Resource
    TradeRuleMatchFill tradeRuleMatchFill;

    @Resource
    TradeRuleMatchFilter tradeRuleMatchFilter;

    @Resource
    TradeRuleMatchBusiness tradeRuleMatchBusiness;

    @Resource
    IItemServiceWrapper itemServiceWrapper;

    @Resource
    IUserLogisticsCompanyService userLogisticsCompanyService;

    @Resource
    IUserExpressTemplateService templateService;

    @Resource
    IUserWlbExpressTemplateService wlbTemplateService;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    TradeRuleConverter tradeRuleConverter;

    @Resource
    ILockService lockService;

    @Resource
    TradeLockBusiness tradeLockBusiness;

    @Resource
    public TradeRulePlatformMatch tradeRulePlatformMatch;
    @Resource
    public TradeMergeConfBusiness tradeMergeConfBusiness;

    @Override
    public TradeRule initTradeRule(Staff staff, TradeRule oldRule) {
        //老的不存在，先保存一份老的数据
        TradeRule tradeRule = queryByRuleName(staff, oldRule.getBusinessType(), oldRule.getName());
        if (tradeRule == null) {
            tradeRule = tradeRuleConverter.convert(staff, oldRule);
            add(staff, tradeRule);
        }
        return tradeRule;
    }

    @Override
    public TradeRule remove(Staff staff, TradeRule oldRule) {
        TradeRule tradeRule = queryByRuleName(staff, oldRule.getBusinessType(), oldRule.getName());
        // 老规则存在 则删除
        if (tradeRule != null) {
            remove(staff, tradeRule.getBusinessType(), tradeRule.getId());
        }
        return tradeRule;
    }

    @Override
    @Transactional
    public void add(Staff staff, TradeRule rule) {
        TradeRuleUtils.empty2Default(rule);
        rule.setOperationType(TradeRuleOperationTypeEnum.INSERT.getType());
        tradeRuleDao.insert(staff, rule);

        Long ruleId = rule.getId();
        List<TradeRuleCondition> conditions = rule.getTradeRuleConditions();
        Map<String, TradeRuleCondition> keyTypeConditionMap = new TreeMap<>();
        List<TradeRuleCondition> dbConditions = null;
        List<TradeRuleConditionDetail> dbDetails = null;
        if (CollectionUtils.isNotEmpty(conditions)) {
            Map<String, List<TradeRuleConditionDetail>> keyTypeDetailsMap = new HashMap<>();
            for (TradeRuleCondition condition : conditions) {
                condition.setOperationType(TradeRuleOperationTypeEnum.INSERT.getType());
                condition.setBusinessType(rule.getBusinessType());
                condition.setRuleId(ruleId);
                String conditionKey = TradeRuleUtils.condition2Key(condition);
                keyTypeConditionMap.put(conditionKey, condition);
                if (CollectionUtils.isNotEmpty(condition.getTradeRuleConditionDetails())) {
                    keyTypeDetailsMap.put(conditionKey, condition.getTradeRuleConditionDetails());
                }
            }
            tradeRuleConditionDao.insert(staff, conditions);
            dbConditions = tradeRuleConditionDao.query(staff, rule.getBusinessType(), ruleId);

            List<TradeRuleConditionDetail> insertDetails = new ArrayList<>();
            Map<String, TradeRuleConditionDetail> keyDetailmap = new HashMap<>();
            for (TradeRuleCondition dbCondition : dbConditions) {
                dbCondition.setOperationType(TradeRuleOperationTypeEnum.INSERT.getType());
                String conditionKey = TradeRuleUtils.condition2Key(dbCondition);
                keyTypeConditionMap.get(conditionKey).setId(dbCondition.getId());
                List<TradeRuleConditionDetail> details = keyTypeDetailsMap.get(conditionKey);
                if (CollectionUtils.isNotEmpty(details)) {
                    for (TradeRuleConditionDetail detail : details) {
                        detail.setRuleId(ruleId);
                        detail.setRuleConditionId(dbCondition.getId());
                        insertDetails.add(detail);
                        keyDetailmap.put(TradeRuleUtils.detail2Key(detail), detail);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(insertDetails)) {
                tradeRuleConditionDetailDao.insert(staff, insertDetails);
                dbDetails = tradeRuleConditionDetailDao.query(staff, ruleId);
                for (TradeRuleConditionDetail dbDetail : dbDetails) {
                    dbDetail.setOperationType(TradeRuleOperationTypeEnum.INSERT.getType());
                    keyDetailmap.get(TradeRuleUtils.detail2Key(dbDetail)).setId(dbDetail.getId());
                }
            }
        }

        //保存历史版本
        tradeRuleHistoryBusiness.add(staff, rule, dbConditions, dbDetails);
    }

    @Override
    @Transactional
    public TradeRule remove(Staff staff, Integer businessType, Long ruleId) {
        TradeRule rule = tradeRuleDao.queryById(staff, businessType, ruleId);
        if (rule == null) {
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("需要删除的规则不存在，ruleId=%s", ruleId)));
            return null;
        }
        rule.setOperationType(TradeRuleOperationTypeEnum.DELETE.getType());
        //删除规则
        tradeRuleDao.delete(staff, businessType, ruleId);
        //删除规则条件
        List<TradeRuleCondition> conditions = tradeRuleConditionDao.query(staff, businessType, ruleId);
        if (CollectionUtils.isEmpty(conditions)) {
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("根据规则id找不到对应的规则条件，ruleId=%s", ruleId)));
        } else {
            List<Long> conditionIds = new ArrayList<>();
            for (TradeRuleCondition condition : conditions) {
                condition.setOperationType(TradeRuleOperationTypeEnum.DELETE.getType());
                conditionIds.add(condition.getId());
            }
            tradeRuleConditionDao.delete(staff, conditionIds);
        }

        //删除
        List<TradeRuleConditionDetail> details = tradeRuleConditionDetailDao.query(staff, ruleId);
        if (CollectionUtils.isNotEmpty(details)) {
            List<Long> detailIds = new ArrayList<>();
            for (TradeRuleConditionDetail detail : details) {
                detailIds.add(detail.getId());
                detail.setOperationType(TradeRuleOperationTypeEnum.DELETE.getType());
            }
            tradeRuleConditionDetailDao.delete(staff, detailIds);
        }
        //保存历史版本
        tradeRuleHistoryBusiness.add(staff, rule, conditions, details);
        return rule;
    }

    @Override
    @Transactional
    public void modify(Staff staff, TradeRule rule) {
        TradeRule existRule = queryByRuleId(staff, rule.getBusinessType(), rule.getId());
        if (existRule == null) {
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("根据规则id找不到对应的规则，ruleId=%s", rule.getId())));
            throw new TradeException("对应规则找不到，可能已删除，请刷新页面重试！");
        }
        // 默认值处理
        TradeRuleUtils.empty2Default(rule);
        //比较
        TradeRuleModifyData modifyData = TradeRuleUtils.compare(staff, rule, existRule);
        //规则更新
        if (modifyData.getUpdateTradeRule() != null) {
            tradeRuleDao.update(staff, modifyData.getUpdateTradeRule());
        }
        //规则条件更新
        modifyCondition(staff, modifyData, rule, tradeRuleConditionDao);
        //规则条件详情更新
        modifyDetail(staff, modifyData, rule);
        //保存历史版本
        if (modifyData.getUpdateTradeRule() != null || CollectionUtils.isNotEmpty(modifyData.getModifyConditions()) || CollectionUtils.isNotEmpty(modifyData.getModifyDetails())) {
            tradeRuleHistoryBusiness.add(staff, rule, modifyData.getModifyConditions(), modifyData.getModifyDetails());
        }
    }

    @Override
    @Transactional
    public void modifyConditionFilter(Staff staff, TradeRule tradeRule) {
        List<TradeRuleCondition> oldConditions = tradeRuleConditionFilterDao.query(staff, tradeRule.getBusinessType(), tradeRule.getId());
        TradeRuleModifyData modifyData = TradeRuleUtils.compareCondition(staff, tradeRule, oldConditions);
        //规则条件更新
        modifyCondition(staff, modifyData, tradeRule, tradeRuleConditionFilterDao);
        //保存历史版本
        if (CollectionUtils.isNotEmpty(modifyData.getModifyConditions())) {
            tradeRuleHistoryBusiness.addConditionFilter(staff, tradeRule, modifyData.getModifyConditions());
        }
    }

    private void modifyCondition(Staff staff, TradeRuleModifyData modifyData, TradeRule rule, TradeRuleConditionDao conditionDao) {
        //条件更新
        if (CollectionUtils.isNotEmpty(modifyData.getDeleteConditions())) {
            conditionDao.delete(staff, modifyData.getDeleteConditions().stream().map(TradeRuleCondition::getId).collect(Collectors.toList()));
        }
        conditionDao.update(staff, modifyData.getUpdateConditions());
        if (CollectionUtils.isNotEmpty(modifyData.getInsertConditions())) {
            conditionDao.insert(staff, modifyData.getInsertConditions());
            Map<String, TradeRuleCondition> conditionMap = TradeRuleUtils.condition2Map(modifyData.getInsertConditions());
            List<TradeRuleCondition> existConditions = conditionDao.query(staff, rule.getBusinessType(), rule.getId());
            for (TradeRuleCondition existCondition : existConditions) {
                TradeRuleCondition condition = conditionMap.get(TradeRuleUtils.condition2Key(existCondition));
                if (condition != null) {
                    condition.setId(existCondition.getId());
                    List<TradeRuleConditionDetail> details = condition.getTradeRuleConditionDetails();
                    if (CollectionUtils.isNotEmpty(details)) {
                        for (TradeRuleConditionDetail detail : details) {
                            detail.setRuleId(rule.getId());
                            detail.setRuleConditionId(existCondition.getId());
                        }
                    }
                }
            }
        }
    }

    private void modifyDetail(Staff staff, TradeRuleModifyData modifyData, TradeRule rule) {
        if (CollectionUtils.isNotEmpty(modifyData.getDeleteDetails())) {
            tradeRuleConditionDetailDao.delete(staff, modifyData.getDeleteDetails().stream().map(TradeRuleConditionDetail::getId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(modifyData.getInsertDetails())) {
            tradeRuleConditionDetailDao.insert(staff, modifyData.getInsertDetails());
            Map<String, TradeRuleConditionDetail> detailMap = TradeRuleUtils.detail2Map(modifyData.getInsertDetails());
            List<TradeRuleConditionDetail> existDetails = tradeRuleConditionDetailDao.query(staff, rule.getId());
            for (TradeRuleConditionDetail existDetail : existDetails) {
                TradeRuleConditionDetail detail = detailMap.get(TradeRuleUtils.detail2Key(existDetail));
                if (detail != null) {
                    detail.setId(existDetail.getId());
                }
            }
        }
    }

    @Override
    @Transactional
    public Map<String, Object> switchOpen(Staff staff, String ids, Integer isOpen, Integer closeNoMatch, Integer businessType) {
        List<TradeRule> updateTradeRules = new ArrayList<>();
        if (closeNoMatch == 0) {
            // 根据ID切换
            List<Long> ruleIds = ArrayUtils.toLongList(ids);

            if (CollectionUtils.isEmpty(ruleIds)) {
                throw new TradeException("请传入规则id");
            }

            List<TradeRule> tradeRules = queryByRuleIds(staff, businessType, ruleIds);

            if (CollectionUtils.isEmpty(tradeRules)) {
                throw new TradeException("对应规则找不到，可能已删除，请刷新页面重试！");
            }
            updateTradeRules.addAll(tradeRules);
        } else if (closeNoMatch == 1) {
            // 禁用近一个月也未匹配规则
            TradeRuleQuery ruleQuery = new TradeRuleQuery();
            ruleQuery.setIsOpen(1);
            ruleQuery.setBusinessType(businessType);
            List<TradeRule> tradeRuleList = tradeRuleDao.query(staff, ruleQuery);
            if (CollectionUtils.isEmpty(tradeRuleList)) {
                throw new TradeException("不存在开启的规则, 可能已禁用, 请检查后重试");
            }
            List<Long> ruleIds = tradeRuleList.stream()
                    .map(TradeRule::getId)
                    .collect(Collectors.toList());
            List<TradeRuleMatchLog> tradeRuleMatchLogs = tradeRuleMatchLogDao.queryRuleMatchGroup(staff, ruleIds);

            if (CollectionUtils.isEmpty(tradeRuleMatchLogs)) {
                // 未找到匹配记录
                updateTradeRules.addAll(tradeRuleList);
            } else {
                Map<Long, TradeRuleMatchLog> tradeRuleMatchLogMap = tradeRuleMatchLogs.stream()
                        .collect(Collectors.toMap(TradeRuleMatchLog::getRuleId, Function.identity()));
                Date date = DateUtil.getMonthBeforeDate(1);
                for (TradeRule tradeRule : tradeRuleList) {
                    if (tradeRuleMatchLogMap.containsKey(tradeRule.getId())) {
                        if (tradeRuleMatchLogMap.get(tradeRule.getId()).getCreated().before(date)) {
                            // 最后匹配时间在一个月之前
                            updateTradeRules.add(tradeRule);
                        }
                    } else {
                        // 不存在匹配记录
                        updateTradeRules.add(tradeRule);
                    }
                }
            }
            isOpen = 0;
        }

        if (CollectionUtils.isNotEmpty(updateTradeRules)) {

            for (TradeRule rule : updateTradeRules) {
                rule.setIsOpen(isOpen);
            }
            TradeRule tradeRule = new TradeRule();
            tradeRule.setIds(TradeRuleUtils.rule2Ids(updateTradeRules));
            tradeRule.setIsOpen(isOpen);
            tradeRuleDao.batchSwitch(staff, tradeRule);
            for (TradeRule updateTradeRule : updateTradeRules) {
                tradeRuleHistoryBusiness.add(staff, updateTradeRule, null, null);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("successNum", updateTradeRules.size());
        return result;

    }

    public List<TradeRule> queryByRuleIds(Staff staff, int businessType, List<Long> ruleIds) {
        return tradeRuleDao.queryByIds(staff, businessType, ruleIds);
    }

    @Override
    public TradeRule queryByRuleId(Staff staff, int businessType, Long ruleId, boolean fillCondition, boolean fillDetail) {
        TradeRule rule = tradeRuleDao.queryById(staff, businessType, ruleId);
        if (rule != null) {
            fillCondition(staff, businessType, Collections.singletonList(rule), fillCondition, fillDetail);
        }
        return rule;
    }

    @Override
    public List<TradeRule> queryByRuleNames(Staff staff, int businessType, List<String> ruleNames, boolean fillCondition, boolean fillDetail) {
        List<TradeRule> rules = tradeRuleDao.queryByRuleNames(staff, businessType, ruleNames);
        fillCondition(staff, businessType, rules, fillCondition, fillDetail);
        return rules;
    }

    private void fillCondition(Staff staff, int businessType, List<TradeRule> rules, boolean fillCondition, boolean fillDetail) {
        if (CollectionUtils.isEmpty(rules) || !fillCondition) {
            return;
        }
        Map<Long, TradeRule> ruleMap = new HashMap<>();
        for (TradeRule rule : rules) {
            rule.setTradeRuleConditions(new ArrayList<>());
            ruleMap.put(rule.getId(), rule);
        }
        List<TradeRuleCondition> conditions = tradeRuleConditionDao.query(staff, businessType, new ArrayList<>(ruleMap.keySet()));
        if (CollectionUtils.isEmpty(conditions)) {
            return;
        }
        Map<Long, TradeRuleCondition> conditionMap = new HashMap<>();
        for (TradeRuleCondition condition : conditions) {
            condition.setTradeRuleConditionDetails(new ArrayList<>());
            conditionMap.put(condition.getId(), condition);
            ruleMap.get(condition.getRuleId()).getTradeRuleConditions().add(condition);
        }
        if (fillDetail) {
            List<TradeRuleConditionDetail> details = tradeRuleConditionDetailDao.query(staff, new ArrayList<>(ruleMap.keySet()), new ArrayList<>(conditionMap.keySet()));
            if (CollectionUtils.isEmpty(details)) {
                return;
            }
            for (TradeRuleConditionDetail detail : details) {
                conditionMap.get(detail.getRuleConditionId()).getTradeRuleConditionDetails().add(detail);
            }
        }
    }

    @Override
    public TradeRuleVo query(Staff staff, TradeRuleQuery ruleQuery) {
        List<TradeRule> rules = tradeRuleDao.query(staff, ruleQuery);
        fillRule(staff, rules);
        return TradeRuleVo.builder()
                .tradeRules(rules)
                .tradeRuleCount(tradeRuleDao.queryCount(staff, ruleQuery))
                .defaultWarehouseId(getDefaultWarehouseId(staff))
                .build();
    }

    @Override
    public Integer queryCount(Staff staff, TradeRuleQuery ruleQuery) {
        return tradeRuleDao.queryCount(staff, ruleQuery);
    }

    @Override
    public List<TradeRuleHistory> queryAllHistories(Staff staff, TradeRuleQuery ruleQuery) {
        return tradeRuleHistoryBusiness.queryAllHistories(staff, ruleQuery);
    }

    private Long getDefaultWarehouseId(Staff staff) {
        Long defaultWarehouseId = 0L;
        Warehouse warehouse = warehouseService.queryWarehouseAllocateDefault(staff.getCompanyId());
        if (warehouse == null) {
            warehouse = warehouseService.queryDefault(staff.getCompanyId());
        }
        if (warehouse != null) {
            defaultWarehouseId = warehouse.getId();
        }
        return defaultWarehouseId;
    }

    @Override
    public List<TradeRuleCondition> queryConditon(Staff staff, TradeRuleQuery ruleQuery) {
        List<TradeRuleCondition> conditions = tradeRuleConditionDao.query(staff, ruleQuery.getBusinessType(), ruleQuery.getRuleId());
        if (CollectionUtils.isNotEmpty(conditions)) {
            Map<Long, TradeRuleCondition> idConditionMap = new HashMap<>();
            for (TradeRuleCondition condition : conditions) {
                condition.setDetailCount(0);
                idConditionMap.put(condition.getId(), condition);
            }
            List<TradeRuleCondition> detailCounts = tradeRuleConditionDetailDao.queryCount(staff, ruleQuery.getRuleId(), new ArrayList<>(idConditionMap.keySet()));
            if (CollectionUtils.isNotEmpty(detailCounts)) {
                detailCounts.forEach(detail -> idConditionMap.get(detail.getId()).setDetailCount(detail.getDetailCount()));
            }
        }
        return conditions;
    }

    @Override
    public List<TradeRuleCondition> queryConditonFilter(Staff staff, TradeRuleQuery ruleQuery){
        return tradeRuleConditionFilterDao.query(staff, ruleQuery.getBusinessType(), ruleQuery.getRuleId());
    }

    @Override
    public List<TradeRuleConditionDetail> queryConditonDetail(Staff staff, TradeRuleQuery ruleQuery) {
        List<TradeRuleConditionDetail> details = tradeRuleConditionDetailDao.query(staff, ruleQuery.getRuleId(), Collections.singletonList(ruleQuery.getConditionId()));
        fillRuleConditionDetails(staff, details);
        return details;
    }

    @Override
    public List<TradeRuleHistory> queryHistories(Staff staff, TradeRuleQuery ruleQuery) {
        return tradeRuleHistoryBusiness.queryAll(staff, ruleQuery);
    }
    @Override
    public Pair<Long, List<TradeRuleHistory>> queryHistoriesByPage(Staff staff, TradeRuleQuery tradeRuleQuery) {
        return tradeRuleHistoryBusiness.queryHistoriesByPage(staff, tradeRuleQuery);
    }


    @Override
    public TradeRuleHistory queryHistory(Staff staff, TradeRuleQuery ruleQuery) {
        TradeRuleHistory rule = tradeRuleHistoryBusiness.query(staff, ruleQuery);
        fillRule(staff, Collections.singletonList(rule));
        fillRuleConditionDetails(staff, rule.getConditionDetails());
        //排序，规则日志商品信息和编辑态顺序保持一致
        doSortHistoryDetails(rule.getTradeRuleConditionHistorys());
        //填充拆单、合单规则商品信息
        fillSysItemSkuId(staff, ruleQuery, rule);
        rule.setRuleChange(TradeRuleUtils.jsonStrToRuleChange(rule.getRuleChangeMsg()));
        return rule;
    }

    private void fillSysItemSkuId(Staff staff, TradeRuleQuery ruleQuery, TradeRuleHistory rule){
        List<TradeRuleConditionHistory> tradeRuleConditionHistories = rule.getTradeRuleConditionHistorys();
        boolean mergeType = Objects.equals(ruleQuery.getBusinessType(), TradeBusinessRuleEnum.MERGE_RULE.getBusinessId());
        boolean splitType = Objects.equals(ruleQuery.getBusinessType(), TradeBusinessRuleEnum.SPLIT_RULE.getBusinessId());
        if (CollectionUtils.isEmpty(tradeRuleConditionHistories)){
            return;
        }
        if (!(mergeType || splitType)){
            return;
        }
        if (mergeType){
            handleMergeType(staff, rule, tradeRuleConditionHistories);
        } else {
            handleSplitType(rule, tradeRuleConditionHistories);
        }

    }
    @SuppressWarnings("all")
    private static void handleSplitType(TradeRuleHistory rule, List<TradeRuleConditionHistory> tradeRuleConditionHistories) {
        List<TradeRuleConditionDetailHistory> tradeRuleConditionDetailHistories = tradeRuleConditionHistories.stream()
                .filter(s -> Objects.equals(s.getConditionKey(), TradeRuleConditionKeyEnum.SYS_OUTER_ID.getKey()))
                .flatMap(tradeRuleConditionHistory -> tradeRuleConditionHistory.getTradeRuleConditionDetailHistorys().stream())
                .collect(Collectors.toList());
        List<String> itemIdConcatSkuIdCollect = tradeRuleConditionDetailHistories.stream().map(s -> {
            Long sysItemId = s.getSysItemId();
            Long sysSkuId = s.getSysSkuId();
            return sysItemId + "_" + sysSkuId;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemIdConcatSkuIdCollect)){
            return;
        }
        tradeRuleConditionHistories.add(TradeRuleUtils.buildRuleConditionHistory(TradeRuleConditionKeyEnum.SPLIT_SYS_ITEM_SKU_IDS, EQUAL, String.join(",", itemIdConcatSkuIdCollect), rule.getVersionId()));
        tradeRuleConditionHistories.add(TradeRuleUtils.buildRuleConditionHistory(TradeRuleConditionKeyEnum.SPLIT_SYS_ITEM_SKU_IDS_SUM, EQUAL, String.valueOf(itemIdConcatSkuIdCollect.size()), rule.getVersionId()));
    }

    @SuppressWarnings("all")
    private void handleMergeType(Staff staff, TradeRuleHistory rule, List<TradeRuleConditionHistory> tradeRuleConditionHistories) {
        tradeRuleConditionHistories.stream()
                .filter(tradeRuleConditionHistory -> Objects.equals(tradeRuleConditionHistory.getConditionKey(), TradeRuleConditionKeyEnum.MERGE_OUTER_IDS.getKey()) && StringUtils.isNotBlank(tradeRuleConditionHistory.getConditionValue()))
                .findFirst()
                .ifPresent(tradeRuleConditionHistory -> {
                    String mergeOuterIds = tradeRuleConditionHistory.getConditionValue();
                    List<String> mergeOuterIdList = Arrays.asList(mergeOuterIds.split(","));
                    List<DmjItemDto> dmjItemDtoList = tradeMergeConfBusiness.queryItemList(staff, mergeOuterIdList);
                    List<String> itemIdConcatSkuIdCollect = dmjItemDtoList.stream().map(dmjItemDto -> {
                        Long sysItemId = dmjItemDto.getSysItemId();
                        Long sysSkuId = dmjItemDto.getSysSkuId();
                        return sysItemId + "_" + sysSkuId;
                    }).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(itemIdConcatSkuIdCollect)){
                        return;
                    }
                    tradeRuleConditionHistories.add(TradeRuleUtils.buildRuleConditionHistory(TradeRuleConditionKeyEnum.MERGE_SYSTEM_SKU_IDS, EQUAL, String.join(",", itemIdConcatSkuIdCollect), rule.getVersionId()));
                    tradeRuleConditionHistories.add(TradeRuleUtils.buildRuleConditionHistory(TradeRuleConditionKeyEnum.MERGE_SKU_NUM, EQUAL, String.valueOf(dmjItemDtoList.size()), rule.getVersionId()));
                });
    }

    private void doSortHistoryDetails(List<TradeRuleConditionHistory> tradeRuleConditionHistories) {
        if (CollectionUtils.isEmpty(tradeRuleConditionHistories)){
            return;
        }
        tradeRuleConditionHistories.stream()
                .filter(s -> Objects.equals(s.getConditionKey(), TradeRuleConditionKeyEnum.SYS_OUTER_ID.getKey()))
                .filter(tradeRuleConditionHistory -> CollectionUtils.isNotEmpty(tradeRuleConditionHistory.getTradeRuleConditionDetailHistorys()))
                .forEach(tradeRuleConditionHistory -> {
                     List<TradeRuleConditionDetailHistory> sortedConditionDetailHistories = tradeRuleConditionHistory.getTradeRuleConditionDetailHistorys()
                             .stream().sorted(Comparator.comparing(TradeRuleConditionDetailHistory::getSysItemId, Comparator.nullsLast(Comparator.naturalOrder()))
                                 .thenComparing(TradeRuleConditionDetailHistory::getSysSkuId, Comparator.nullsLast(Comparator.naturalOrder())))
                             .collect(Collectors.toList());
            tradeRuleConditionHistory.setTradeRuleConditionDetailHistorys(sortedConditionDetailHistories);
        });
    }

    @Override
    public void matchSync(Staff staff, List<Trade> trades, boolean isInsert) {
        TradeRuleMatchContext ruleContext = tradeRuleMatchContextBusiness.init(staff, OpEnum.TRADE_MATCH_RULE_SYNC, TradeBusinessRuleEnum.AI_MATCH_RULE);
        if (!isInsert) {
            // 新增不需要走fill, 更新需要选择性fill
            tradeRuleMatchFill.fill(staff, ruleContext, trades);
        }
        tradeRuleMatchBusiness.match(staff, ruleContext, trades);
    }

    @Override
    public void matchAi(Staff staff, TradeQueryParams queryParams, ProgressData progressData, /*标签策略 0 覆盖, 1 新增*/Integer tagMode, /*快递策略 0 保留 1 清空*/Integer expressMode) {
        TradeRuleMatchContext context = tradeRuleMatchContextBusiness.init(staff, OpEnum.TRADE_MATCH_RULE_AI, TradeBusinessRuleEnum.AI_MATCH_RULE, tagMode, expressMode);
        if (CollectionUtils.isEmpty(context.getTradeRules())) {
            return;
        }
        List<Trade> trades;
        if (queryParams.getSid() != null && queryParams.getSid().length > 0) {
            trades = tradeRuleMatchQuery.queryBySids(staff, context, queryParams.getSid());
        } else {
            trades = tradeRuleMatchQuery.query(staff, context, queryParams);
        }
        tradeRuleMatchFilter.filter(staff, context, trades);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        // 分批次处理
        List<List<Trade>> batchTrades = ArrayUtils.split(trades, DEFAULT_BATCH_SIZE);

        long successNum = 0;
        for (List<Trade> subTrades : batchTrades) {
            Long[] sids = TradeUtils.toSids(subTrades);
            TradeRuleAfterMatchedData actionData = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
                // 上下文清空
                context.clear();
                List<Trade> matchTrades = tradeRuleMatchQuery.queryBySids(staff, context, sids);
                TradeRuleAfterMatchedData action = tradeRuleMatchBusiness.match(staff, context, matchTrades);
                tradeRuleMatchBusiness.update(staff, context, action);
                return action;
            });
            successNum += actionData.getUpdateTradeList().size();
        }

        if (progressData != null) {
            progressData.setSucNum(successNum);
        }
    }

    @Override
    public void rematch(ReMatchContext reMatchContext) {
        Staff staff = reMatchContext.getStaff();

        // 重算事件不同触发操作不同,  如果是新建各类订单事件, 需要忽略规则中配置的[允许重算]配置
        OpEnum opEnum;
        switch (reMatchContext.getEvent()) {
            case EVENT_CREATE_SYS_TRADE:
            case EVENT_TRADE_COPY:
            case EVENT_EXCEL_IMPORT_TRADE:
            case EVENT_AFTER_SALE_CREATE_TRADE:
            case EVENT_CREATE_AFTER_SALE_TRADE:
                opEnum = OpEnum.TRADE_MATCH_RULE_SYNC;
                break;
            default:
                opEnum = OpEnum.TRADE_MATCH_RULE_REMATCH;
                break;
        }
        TradeRuleMatchContext context = tradeRuleMatchContextBusiness.init(staff, opEnum, TradeBusinessRuleEnum.AI_MATCH_RULE, reMatchContext.getEvent());
        List<Trade> trades = tradeRuleMatchQuery.queryBySids(staff, context, TradeUtils.toSids(reMatchContext.getNeedRematchTrades()));

        if (reMatchContext.getEvent().equals(EventEnum.EVENT_TRADE_CHANGE_SHIPPING_ADDRESS)) {
            // 修改收件人信息重算特殊处理
            String configValue = TradeConfigGetUtil.get(reMatchContext.getStaff(), TradeConfigEnum.TRADE_EXCEPTION_REMARK).getConfigValue();
            if (StringUtils.isNotEmpty(configValue)) {
                // 配置不为空, 序列化配置
                TradeConfig.TradeExceptionRemarkDTO tradeExceptionRemarkDTO = JSONObject.parseObject(configValue, TradeConfig.TradeExceptionRemarkDTO.class);
                if (tradeExceptionRemarkDTO.isEnableStatus()) {
                    // 如果配置启用
                    // 标签/异常 处理策略
                    context.setTagMode(tradeExceptionRemarkDTO.getRemarkType());
                    // 如果不是所有店铺, 则设置过滤店铺条件
                    context.setExcludeUserId(tradeExceptionRemarkDTO.getUserIdList());
                }
            }
        }
        tradeRuleMatchFilter.filter(staff, context, trades);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        // 分批次处理
        List<List<Trade>> batchTrades = ArrayUtils.split(trades, DEFAULT_BATCH_SIZE);

        for (List<Trade> subTrades : batchTrades) {
            Long[] sids = TradeUtils.toSids(subTrades);
            lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
                // 上下文清空
                context.clear();
                List<Trade> matchTrades = tradeRuleMatchQuery.queryBySids(staff, context, sids);
                TradeRuleAfterMatchedData action = tradeRuleMatchBusiness.match(staff, context, matchTrades);
                tradeRuleMatchBusiness.update(staff, context, action);
                return action;
            });
        }
    }

    @Override
    public List<TradeRuleMatchSpider> ruleMatchSpider(Staff staff, TradeQueryParams queryParams, int businessType, List<Long> ruleIds) {
        List<TradeRuleMatchSpider> spiderDatas = new ArrayList<>();

        List<TradeRule> rules = tradeRuleConverter.convert(staff, businessType, ruleIds);
        if (CollectionUtils.isEmpty(rules)) {
            return spiderDatas;
        }
        TradeRuleMatchContext context = tradeRuleMatchContextBusiness.init(staff, rules, OpEnum.TRADE_MATCH_RULE_SPIDER, null, null);
        List<Trade> trades;
        if (queryParams.getSid() != null && queryParams.getSid().length > 0) {
            trades = tradeRuleMatchQuery.queryBySids(staff, context, queryParams.getSid());
        } else {
            trades = tradeRuleMatchQuery.query(staff, context, queryParams);
        }
        if (CollectionUtils.isEmpty(trades)) {
            return spiderDatas;
        }
        for (Trade trade : trades) {
            TradeRuleMatchResult result = TradeRuleMatchUtils.match(staff, context, trade);
            tradeRulePlatformMatch.match(staff, Collections.singletonList(result));
            spiderDatas.add(tradeRuleMatchResult2TradeRuleMatchSpider(trade, context, result));
        }
        return spiderDatas;
    }


    @Override
    public <T extends TradeRule> void setTradeRule(Staff staff, List<T> oldRules, TradeBusinessRuleEnum businessEnum, boolean fillCondition, boolean fillDetail) {
        if (CollectionUtils.isNotEmpty(oldRules)) {
            Map<String, T> nameOldRuleMap = new HashMap<>();
            for (T rule : oldRules) {
                if (StringUtils.isNotBlank(rule.getName())) {
                    nameOldRuleMap.put(rule.getName(), rule);
                }
            }
            if (nameOldRuleMap.size() > 0) {
                List<TradeRule> rules = queryByRuleNames(staff, businessEnum.getBusinessId(), new ArrayList<>(nameOldRuleMap.keySet()), fillCondition, fillDetail);
                if (CollectionUtils.isNotEmpty(rules)) {
                    for (TradeRule rule : rules) {
                        T tradeRule = nameOldRuleMap.get(rule.getName());
                        if (Objects.isNull(tradeRule)) {
                            Logs.ifDebug(LogHelper.buildLog(staff, String.format("规则名称映射失败，ruleName=%s", rule.getName())));
                            continue;
                        }
                        tradeRule.setTradeRule(rule);
                    }
                }
            }
        }
    }

    @Override
    public Integer queryHistoryCount(Staff staff, TradeRuleQuery tradeRuleQuery) {
        return tradeRuleHistoryBusiness.queryHistoryCount(staff, tradeRuleQuery);
    }

    private TradeRuleMatchSpider tradeRuleMatchResult2TradeRuleMatchSpider(Trade trade, TradeRuleMatchContext context, TradeRuleMatchResult matchResult) {
        List<String> matchRules = new ArrayList<>();
        Map<String, String> noMatchedRuleReason = new HashMap<>();
        Set<Long> matchWarehouseIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(matchResult.getMatchedRules())) {
            for (TradeRule matchRule : matchResult.getMatchedRules()) {
                matchRules.add(getRuleName(matchRule));
                matchWarehouseIds.add(matchRule.getActionWarehouseId());
            }
        }
        if (matchResult.getNoMatchedRuleReasonMap() != null && matchResult.getNoMatchedRuleReasonMap().size() > 0) {
            for (Map.Entry<TradeRule, String> entry : matchResult.getNoMatchedRuleReasonMap().entrySet()) {
                noMatchedRuleReason.put(getRuleName(entry.getKey()), entry.getValue());
            }
        }
        return TradeRuleMatchSpider.builder()
                .sid(trade.getSid())
                .tid(trade.getTid())
                .userId(trade.getUserId())
                .warehouseId(trade.getWarehouseId())
                .matchWarehouseIds(matchWarehouseIds)
                .created(trade.getCreated())
                .payTime(trade.getPayTime())
                .matchRules(matchRules)
                .noMatchedRules(new ArrayList<>(noMatchedRuleReason.keySet()))
                .noMatchedRuleReason(noMatchedRuleReason)
                .build();
    }

    private String getRuleName(TradeRule tradeRule) {
        if (StringUtils.isNotBlank(tradeRule.getName())) {
            return tradeRule.getName();
        }
        return tradeRule.getId() + "(名称未设置显示规则id)";
    }

    private void fillRule(Staff staff, List<TradeRule> rules) {
        if (CollectionUtils.isNotEmpty(rules)) {
            TradeRuleFillContext context = TradeRuleFillContext.build(rules);
            // 匹配记录汇总
            context.fillMatchLogGroup(ids -> tradeRuleMatchLogDao.queryRuleMatchGroup(staff, ids));
            // 普通快递模板
            context.fillNormalTemplateName(ids -> templateService.queryNameByIds(staff, ids));
            // 电子快递模板
            context.fillWlbTemplateName(ids -> wlbTemplateService.queryNamesByIds(staff, ids));
            // 快递公司
            context.fillLogisticsCompanyName(ids -> userLogisticsCompanyService.queryByIds(staff, ids, 1));
        }
    }

    private void fillRuleConditionDetails(Staff staff, List<TradeRuleConditionDetail> details) {
        if (CollectionUtils.isEmpty(details)){
            return;
        }
        try {
            //itemId和skuId收集
            Pair<List<Long>, List<Long>> itemIdAndSkuIdCollectPair = getItemIdAndSkuIdCollectPair(details);
            List<DmjItem> dmjItems = itemServiceWrapper.queryBySysItemIds(staff, itemIdAndSkuIdCollectPair.getLeft(), "sysItemId,title,shortTitle,outerId,remark,type");
            Map<Long,DmjItem> itemMap = dmjItems.stream()
                    .collect(Collectors.toMap(DmjItem::getSysItemId, Function.identity(), (v1, v2) -> v1));
            List<DmjSku> dmjSkus = itemServiceWrapper.queryBySysSkuIds(staff, itemIdAndSkuIdCollectPair.getRight(), "sysItemId,sysSkuId,outerId,propertiesName,propertiesAlias,remark,type");
            Map<Long, DmjSku> skuMap = dmjSkus.stream()
                    .collect(Collectors.toMap(DmjSku::getSysSkuId, Function.identity(), (v1, v2) -> v1));
            if (MapUtils.isEmpty(itemMap) && MapUtils.isEmpty(skuMap)){
                return;
            }
            details.forEach(detail ->
                    Optional.ofNullable(itemMap.get(detail.getSysItemId()))
                            .ifPresent(dmjItem -> {
                                detail.setOuterId(dmjItem.getOuterId());
                                detail.setSysItemName(dmjItem.getTitle());
                                if (detail.getSysSkuId() > 0){
                                    Optional.ofNullable(skuMap.get(detail.getSysSkuId()))
                                            .ifPresent(dmjSku -> {
                                                detail.setSysSkuId(dmjSku.getSysSkuId());
                                                detail.setSysSkuName(dmjSku.getPropertiesName());
                                                detail.setOuterId(dmjSku.getOuterId());
                                            });
                                }
                            }));
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, "规则商品信息填充失败"), e);
        }
    }
    private Pair<List<Long>, List<Long>> getItemIdAndSkuIdCollectPair(List<TradeRuleConditionDetail> details){
        List<Long> systemItemIdCollect = new ArrayList<>();
        List<Long> systemSkuIdCollect = new ArrayList<>();
        for (TradeRuleConditionDetail detail : details) {
            Long sysItemId = detail.getSysItemId();
            if (sysItemId != null) {
                systemItemIdCollect.add(sysItemId);
            }
            Long sysSkuId = detail.getSysSkuId();
            if (sysSkuId != null && sysSkuId > 0) {
                systemSkuIdCollect.add(sysSkuId);
            }
        }
        return Pair.of(systemItemIdCollect, systemSkuIdCollect);
    }
}

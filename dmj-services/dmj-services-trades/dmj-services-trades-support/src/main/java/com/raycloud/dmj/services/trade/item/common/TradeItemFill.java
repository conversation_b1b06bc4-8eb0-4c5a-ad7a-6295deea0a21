package com.raycloud.dmj.services.trade.item.common;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.services.trade.common.ITradeBusinessFill;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description
 */
public abstract class TradeItemFill {

    @Resource
    protected ITradeBusinessFill tradeBusinessFill;

    public abstract void fill(Staff staff, TradeItemContext itemContext, List<Trade> trades);

}

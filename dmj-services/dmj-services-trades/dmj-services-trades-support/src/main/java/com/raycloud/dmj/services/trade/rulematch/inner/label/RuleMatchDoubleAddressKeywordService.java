package com.raycloud.dmj.services.trade.rulematch.inner.label;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.tag.context.TradeRuleMatchContext;
import com.raycloud.dmj.domain.trade.rulematch.AiRule;
import com.raycloud.dmj.domain.trade.rulematch.AiRuleOperateDetail;
import com.raycloud.dmj.domain.trade.rulematch.AiTrade;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.services.trade.rulematch.inner.AbstractRuleMatchService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 双地址关键字
 * <AUTHOR>
 * @Date 2023/2/22 3:10 下午
 */
@Service
public class RuleMatchDoubleAddressKeywordService extends AbstractRuleMatchService {

    private static final String matchType = "双地址关键字";

    @Override
    public String matchType() {
        return matchType;
    }

    @Override
    public String getTradeValue(AiTrade aiTrade, AiRule aiRule) {
        return aiTrade.getReceiverAddress() +  nvl(aiTrade.getReceiverAddressMask());
    }

    @Override
    public String getRuleValue(Staff staff, AiTrade aiTrade, AiRule aiRule, TradeRuleMatchContext context) {
        return ruleValueLog(aiRule.getDoubleAddress());
    }

    @Override
    public boolean match(Staff staff, AiTrade aiTrade, AiRule aiRule, TradeRuleMatchContext context) {
        AiRuleOperateDetail doubleAddress = aiRule.getDoubleAddress();
        if (Objects.isNull(doubleAddress)) {
            return Boolean.TRUE;
        }

        //关键字未配置，默认成功
        if (StringUtils.isEmpty(doubleAddress.getValue())) {
            return Boolean.FALSE;
        }

        String tradeValue = aiTrade.getReceiverAddress() + nvl(aiTrade.getReceiverAddressMask());

        //详细地址为空，不是双地址，成功
        if (StringUtils.isEmpty(tradeValue)) {
            return Boolean.FALSE;
        }

        //从diamond配置中获取省市列表
        Map<String, List<String>> provinceMap = TradeDiamondUtils.getAddressForMatchTag();
        //解析地址关键字,取出订单中包含关键字的地址。

        //兼容，；、
        String doubleAddressKeywords;
        List<String> doubleAddressKeywordList = new ArrayList<>();
        if (doubleAddress.getValue().trim().contains("，") || doubleAddress.getValue().trim().contains(",")) {
            doubleAddressKeywords = doubleAddress.getValue().trim().replace("，", ",");
            doubleAddressKeywordList = Arrays.stream(doubleAddressKeywords.split(",")).map(String::trim).collect(Collectors.toList());
        }
        if (doubleAddress.getValue().trim().contains("；") || doubleAddress.getValue().trim().contains(";")) {
            doubleAddressKeywords = doubleAddress.getValue().trim().replace("；", ";");
            doubleAddressKeywordList = Arrays.stream(doubleAddressKeywords.split(";")).map(String::trim).collect(Collectors.toList());
        }
        if (doubleAddress.getValue().trim().contains("、") || doubleAddress.getValue().trim().contains("\\")) {
            doubleAddressKeywords = doubleAddress.getValue().trim().replace("\\", "、");
            doubleAddressKeywordList = Arrays.stream(doubleAddressKeywords.split("、")).map(String::trim).collect(Collectors.toList());
        }

        //如果为空的时候，标识规则里面没有，、；，把原始规则值加入即可。
        if (CollectionUtils.isEmpty(doubleAddressKeywordList)) {
            doubleAddressKeywordList.add(doubleAddress.getValue().trim());
        }
        //详细地址中包含的关键字
        List<String> receiverAddressContainKeywords = doubleAddressKeywordList.stream().filter(tradeValue::contains).collect(Collectors.toList());
        //双地址匹配失败，【订单详细地址未匹配双地址关键字】
        if (CollectionUtils.isEmpty(receiverAddressContainKeywords)) {
            return Boolean.FALSE;
        }

        //通过关键字解析出省份（关键字可能是省也可能是市，最后只取省）
        List<String> analysisProvinces = analysisProvince(receiverAddressContainKeywords, provinceMap);
        if (CollectionUtils.isEmpty(analysisProvinces)) {
            return Boolean.FALSE;
        }

        //通过关键词解析出来的省份与订单的省份进行比较
        for (String analysisProvince : analysisProvinces) {
            //详细地址中解析的省份和订单的省份 都不互相包含，双地址匹配成功
            if (!analysisProvince.contains(aiTrade.getReceiverState()) && !aiTrade.getReceiverState().contains(analysisProvince)) {
                return Boolean.TRUE;
            }
        }
        // 详细地址中解析的省份和订单的省份 存在包含情况，双地址匹配失败
        return Boolean.FALSE;
    }

    /**
     * 省份解析
     */
    private static List<String> analysisProvince(List<String> receiverAddressContainKeywords, Map<String, List<String>> provinceMap) {
        if (CollectionUtils.isEmpty(receiverAddressContainKeywords) || CollectionUtils.isEmpty(provinceMap)) {
            return null;
        }
        List<String> result = new ArrayList<>();
        for (String str : receiverAddressContainKeywords) {
            Set<String> provinceKeys = provinceMap.keySet();
            for (String provinceKey : provinceKeys) {
                //关键字包含省份
                if (provinceKey.contains(str) || str.contains(provinceKey)) {
                    result.add(str);
                    //判断是否包含市
                } else {
                    List<String> cityList = provinceMap.get(provinceKey);
                    List<String> cities = cityList.stream().filter(item -> item.contains(str) || str.contains(item)).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(cities)) {
                        continue;
                    }
                    result.add(provinceKey);
                }
            }
        }
        return result;
    }


}

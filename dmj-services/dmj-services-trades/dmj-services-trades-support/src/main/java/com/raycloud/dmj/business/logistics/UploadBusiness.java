package com.raycloud.dmj.business.logistics;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Buffers;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.StaffAssembleBusiness;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.item.ItemReplaceSingleToSuitBusiness;
import com.raycloud.dmj.business.operate.TradeUpdateSellerMemoFlagBusiness;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.dao.trade.ConsignRecordDao;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.dao.trade.UploadRecordDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.platform.trades.ConsignRequest;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLogDetail;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.memo.TradeMemo;
import com.raycloud.dmj.domain.trade.memo.TradeMemoConstant;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeBuilderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.diamond.TradeBufferConfigDiamondUtils;
import com.raycloud.dmj.domain.utils.diamond.TradeConsignConfigDiamondUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.express.api.IPrintExpressTemplateService;
import com.raycloud.dmj.express.response.template.MatchTemplateDTO;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.filter.support.DefaultTemplateFilter;
import com.raycloud.dmj.services.pt.IMultiPacksPrintTradeLogService;
import com.raycloud.dmj.services.pt.dubbo.IExpressTemplateDubboService;
import com.raycloud.dmj.services.trade.audit.old.AuditConsignBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.filter.TradeFilterException;
import com.raycloud.dmj.services.trades.support.stock.OrderStockService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.ConsignUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import com.raycloud.erp.buffer.model.ErpBuffer;
import com.raycloud.erp.buffer.service.IBufferService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.business.logistics.SkuOneTimeUploadBusiness.isMultiplePackagesUpload;
import static com.raycloud.dmj.business.logistics.SkuOneTimeUploadBusiness.isMultiplePackagesUploadBySku;
import static com.raycloud.dmj.business.logistics.UploadUtils.*;
import static com.raycloud.dmj.domain.trades.Trade.SYS_STATUS_SELLER_SEND_GOODS;
import static com.raycloud.dmj.domain.trades.TradeStatus.TB_SELLER_CONSIGNED_PART;
import static com.raycloud.dmj.domain.trades.utils.TradeUtils.isNfgoodHelpSellTrade;


/**
 * 发货上传业务类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2017-06-09 14:28
 */
@Service
public class UploadBusiness {

    private static Set<String> jstdPlatforms = new HashSet<>();

    static {
        jstdPlatforms.add(CommonConstants.PLAT_FORM_TYPE_FXG_GX);
        jstdPlatforms.add(CommonConstants.PLAT_FORM_TYPE_FXG);
        jstdPlatforms.add(CommonConstants.PLAT_FORM_TYPE_PDD);
        jstdPlatforms.add(CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU);
    }

    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    ConsignRecordDao consignRecordDao;

    @Resource
    IExpressCompanyService expressCompanyService;

    @Resource
    IEventCenter eventCenter;

    @Resource
    UploadDelayBusiness uploadDelayBusiness;

    @Resource
    IExpressTemplateDubboService expressTemplateDubboService;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    IUserService userService;

    @Resource
    IStaffService staffService;

    @Resource
    StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    IProgressService progressService;

    @Resource
    IBufferService bufferService;

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    DefaultTemplateFilter defaultTemplateFilter;

    @Resource
    UploadBatchBusiness uploadBatchBusiness;

    @Resource
    UploadBeforeBusiness uploadBeforeBusiness;

    @Resource
    UploadAfterBusiness uploadAfterBusiness;

    @Resource
    UploadRecordBusiness uploadRecordBusiness;

    @Resource
    UploadRecordDao uploadRecordDao;
    @Resource
    FxBusiness fxBusiness;
    @Resource
    FxUploadBusiness fxUploadBusiness;
    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    @Resource
    UploadLockBusiness uploadLockBusiness;

    @Resource
    ILockService lockService;

    @Resource
    UploadPackagesNoticeBusiness uploadPackagesNoticeBusiness;

    @Resource
    OrderStockService orderStockService;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    ITradePtService tradePtService;

    @Resource
    SkuOneTimeUploadBusiness skuOneTimeUploadBusiness;

    @Resource
    UploadSplitOnceBusiness uploadSplitOnceBusiness;

    @Resource
    DangDangUploadBusiness dangDangUploadBusiness;

    @Resource
    ConsignCacheContainer consignCacheContainer;
    @Resource
    NewUploadBusiness newUploadBusiness;

    @Resource
    private ConsignBusiness consignBusiness;

    @Resource
    private ItemReplaceSingleToSuitBusiness itemReplaceSingleToSuitBusiness;

    @Resource
    private IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;
    @Resource
    private PlatformUploader platformUploader;

    @Resource
    private TradeLockBusiness tradeLockBusiness;

    @Resource
    private TradeTraceBusiness tradeTraceBusiness;

    @Resource
    private IPrintExpressTemplateService printExpressTemplateService;

    @Resource
    private TradeUpdateSellerMemoFlagBusiness tradeUpdateSellerMemoFlagBusiness;

    private final Logger logger = Logger.getLogger(this.getClass());

    public void asyncUpload(Staff staff, List<Long> sids, SendType consignType, String clientIp, Integer dummyType, String noLogisticsName, String noLogisticsTel, Boolean isExternal) {
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, true, sids.toArray(new Long[0]));
        // KMERP-136775: 如果有合单，则只需要传主单
        List<Trade> hiddenTrades = Lists.newArrayList();
        for (Trade trade : trades) {
            if (TradeUtils.isMerge(trade) && !Objects.equals(trade.getSid(), trade.getMergeSid())) {
                hiddenTrades.add(trade);
            }
        }
        if (CollectionUtils.isNotEmpty(hiddenTrades)) {
            trades.removeAll(hiddenTrades);
            logger.info(LogHelper.buildLog(staff, String.format("发货上传移除隐藏单 sids:%s", TradeUtils.toSidList(hiddenTrades))));
        }
        if (CollectionUtils.isEmpty(trades)) {
            logger.warn(LogHelper.buildLog(staff, String.format("订单未找到 sids:%s", sids)));
            return;
        }

        // 走新版发货上传服务
        newUploadBusiness.asyncUpload(staff, trades, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        if (TradeConsignConfigDiamondUtils.openAsyncDirectUpload(staff)) {
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_CONSIGN_UPLOAD_DIRECT).setArgs(new Object[]{staff, TradeUtils.toSids(trades), consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal}), null);
        } else {
            bufferService.buffer(Buffers.build(staff, TradeEvents.TRADE_CONSIGN_UPLOAD, UploadParams.buildParamsKey(consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal)), TradeUtils.toSidList(trades));
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_CONSIGN_UPLOAD).setArgs(new Object[]{staff, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal}), null);
            consignCacheContainer.register(staff.getCompanyId(), TradeEvents.TRADE_CONSIGN_UPLOAD);
        }
    }

    public void upload(Staff staff, SendType consignType, String clientIp, Integer dummyType, String noLogisticsName, String noLogisticsTel, Boolean isExternal) {
        if (TradeBufferConfigDiamondUtils.openConsumConcurrent(staff.getCompanyId(), TradeEvents.TRADE_CONSIGN_UPLOAD)) {
            bufferService.consumConcurrent(Buffers.build(staff, TradeEvents.TRADE_CONSIGN_UPLOAD, 1000), list -> upload(staff, list, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal));
        } else {
            bufferService.consume(Buffers.build(staff, TradeEvents.TRADE_CONSIGN_UPLOAD, 200), list -> upload(staff, list, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal));
        }
        consignCacheContainer.unregister(staff.getCompanyId(), TradeEvents.TRADE_CONSIGN_UPLOAD);
    }

    private void upload(Staff staff, List<ErpBuffer> buffers, SendType consignType, String clientIp, Integer dummyType, String noLogisticsName, String noLogisticsTel, Boolean isExternal) {
        Map<Long, Map<String, Set<Long>>> staffUploadKeySidMap = groupBuffer(buffers, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal);
        // 直接走发货服务
        if (tradeLocalConfig.isSendTradeConsignServer(staff.getCompanyId())) {
            staffUploadKeySidMap.forEach((staffId, uploadKeySidMap) -> {
                Staff tempStaff = staffAssembleBusiness.getSafeStaff(staff, staffId);
                uploadKeySidMap.forEach((uploadKey, sids) -> {
                    UploadParams uploadParams = UploadParams.parseParamsKey(uploadKey, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal);
                    sids.forEach(sid -> eventCenter.fireEvent(this, new EventInfo(TradeEvents.SERVER_TRADE_CONSIGN_UPLOAD).setArgs(new Object[]{tempStaff, new Long[]{sid}, uploadParams.sendType, uploadParams.clientIp, uploadParams.dummyType, uploadParams.noLogisticsName, uploadParams.noLogisticsTel, false, uploadParams.isExternal}), null));
                });
            });
        } else {
            staffUploadKeySidMap.forEach((staffId, uploadKeySidMap) -> {
                Staff tempStaff = staffAssembleBusiness.getSafeStaff(staff, staffId);
                uploadKeySidMap.forEach((uploadKey, sids) -> {
                    UploadParams uploadParams = UploadParams.parseParamsKey(uploadKey, consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal);
                    upload(tempStaff, sids.toArray(new Long[0]), uploadParams.sendType, uploadParams.clientIp, uploadParams.dummyType, uploadParams.noLogisticsName, uploadParams.noLogisticsTel, false, uploadParams.isExternal);
                });
            });
        }
    }

    private Map<Long, Map<String, Set<Long>>> groupBuffer(List<ErpBuffer> buffers, SendType consignType, String clientIp, Integer dummyType, String noLogisticsName, String noLogisticsTel, Boolean isExternal) {
        String defaultUploadKey = UploadParams.buildParamsKey(consignType, clientIp, dummyType, noLogisticsName, noLogisticsTel, isExternal);
        Map<Long, Map<String, Set<Long>>> staffUploadKeySidMap = new HashMap<>();
        buffers.forEach(b -> staffUploadKeySidMap.computeIfAbsent(b.getStaffId(), k -> new HashMap<>()).computeIfAbsent(getUploadKey(b, defaultUploadKey), k -> new HashSet<>()).add(Long.valueOf(b.getKey())));
        return staffUploadKeySidMap;
    }

    private String getUploadKey(ErpBuffer buffer, String defaultUploadKey) {
        return StringUtils.isEmpty(buffer.getArgs()) ? defaultUploadKey : buffer.getArgs();
    }

    public UploadResult upload(Staff staff, Long[] sids, SendType consignType, String clientIp, Integer dymmyType, String noLogisticsName, String noLogisticsTel, boolean process, Boolean isExternal) {
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, true, sids);
        return upload(staff, UploadData.build(staff, trades, consignType, clientIp, 0, dymmyType, noLogisticsName, noLogisticsTel, process, isExternal));
    }

    private List<Trade> uploadFilter(Staff staff, UploadData uploadData) {
        List<Trade> gxTrades = uploadData.trades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
        if (gxTrades.size() > 0) {
            uploadRecordBusiness.addUploadRecord(staff, gxTrades, uploadData.sendType);
            //得到分销订单对应的快递公司Id(根据templateId,templateType实现)
            List<Trade> gxUploadTrades;
            try {
                gxUploadTrades = defaultTemplateFilter.filterTrades(staff, gxTrades);
            } catch (TradeFilterException e) {
                logger.error(LogHelper.buildLog(staff, String.format("上传发货GX，过滤模板报错，sids=%s", TradeUtils.toSidList(gxTrades))), e);
                return Collections.emptyList();
            }
            Map<Long, String> failTradeMap = UploadUtils.checkFxBeforeUpload(staff, uploadData, gxUploadTrades);
            gxUploadTrades.removeIf(trade -> failTradeMap.containsKey(trade.getSid()));
            if (CollectionUtils.isEmpty(gxUploadTrades)) {
                return Collections.emptyList();
            }

            Map<Long, List<Trade>> tradesBySourceId = gxUploadTrades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
            tradesBySourceId.forEach((sourceId, trades) -> {
                //获取分销信息
                Staff fxStaff = staffService.queryFullByCompanyId(sourceId);
                List<Trade> fxTrades = fxBusiness.getFxTradesByTids(fxStaff, trades);
                Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("供销触发分销上传，分销sid[%s]", TradeUtils.toSidList(fxTrades))));
                //放入buffer
                bufferService.buffer(Buffers.build(fxStaff, TradeEvents.TRADE_CONSIGN_UPLOAD), TradeUtils.toSidList(fxTrades));
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_CONSIGN_UPLOAD).setArgs(new Object[]{fxStaff, uploadData.sendType, uploadData.clientIp, uploadData.dymmyType, uploadData.noLogisticsName, uploadData.noLogisticsTel, uploadData.isExternal}), null);
            });
            touchProgressGx(staff, uploadData, gxTrades);
            uploadData.trades.removeIf(trade -> TradeUtils.toSidList(gxTrades).contains(trade.getSid()));
        }
        //过滤平台分销订单
        uploadData.trades.removeIf(TradeUtils::isPlatformFxSource);
        return uploadData.trades;
    }

    public UploadResult upload(Staff staff, UploadData uploadData) {
        UploadResult result = new UploadResult();
        // 走新版发货上传服务
        UploadResult result2 = newUploadBusiness.upload(staff, uploadData,false);
        if (!uploadData.hasData()) {
            return result2;
        }
        // 获取配置
        uploadData.fillTradeConfig(tradeConfigService.get(staff));
        // 处理商品搭配-单品转套件数据-用原始单品数据进行上传
        itemReplaceSingleToSuitBusiness.restoreUploadSingleToSuitOrders(staff, uploadData.trades);
        //上传
        List<Trade> lockFails = doUpload(staff, uploadData, result);
        //对加锁失败的订单进行重试
        uploadRetry(staff, uploadData, result, lockFails);

        // 合并结果
        result.successTrades.addAll(result2.successTrades);
        result.consignRecords.addAll(result2.consignRecords);
        return result;
    }

    /**
     * 对加锁失败的订单进行重试
     */
    private void uploadRetry(Staff staff, UploadData uploadData, UploadResult result, List<Trade> lockFails) {
        int retryNum = lockFails.size();
        try {
            while (lockFails != null && lockFails.size() > 0 && retryNum-- > 0) {
                //上传后里面触发一次同步
                uploadAfterBusiness.syncLockFailTrade(staff, result.successTrades);

                Logs.warn(LogHelper.buildLog(staff, String.format("上传发货对加锁失败的订单进行重试,retryNum=%s,sids=%s", retryNum, TradeUtils.toSidList(lockFails))));
                uploadData.clearData();
                uploadData.trades = lockFails;

                UploadResult temp = new UploadResult();
                lockFails = doUpload(staff, uploadData, temp);
                if (temp.consignRecords.size() > 0) {
                    result.consignRecords.addAll(temp.consignRecords);
                }
                if (temp.successTrades.size() > 0) {
                    result.successTrades.addAll(temp.successTrades);
                }
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("上传发货对加锁失败的订单进行重试报错,retryNum=%s,sids=%s", retryNum, TradeUtils.toSidList(lockFails))), e);
        }
        laterRetryLockFailedTrade(staff, uploadData.sendType, lockFails);
    }

    private List<Trade> doUpload(Staff staff, UploadData uploadData, UploadResult result) {
        //上传开始
        List<Trade> lockFails = uploadLockBusiness.lockUpload(staff, uploadData);
        //初始化需要多上传的trade
        uploadData.addMultiPackagesUploadTrades(uploadData.trades);
        try {
            // 移除忽略上传平台订单
            filterIgnoreUploadTrades(staff, uploadData, result);
            uploadBeforeBusiness.uploadBefore(staff, uploadData);
            if (!uploadData.hasData()) {
                return lockFails;
            }
            Set<Trade> setRiskTrade = ConsignUtils.filterRiskTrade(staff, uploadData.trades);
            if (CollectionUtils.isNotEmpty(setRiskTrade)) {
                List<Trade> riskTrades = new ArrayList<>(setRiskTrade);
                for (List<Trade> subTrades : Lists.partition(riskTrades, 200)) {
                    eventCenter.fireEvent(this, new EventInfo("trade.risk.upload").setArgs(new Object[]{staff}), Lists.newArrayList(subTrades));
                }
            }
            if (CollectionUtils.isEmpty(uploadData.trades)) {
                return lockFails;
            }
            ConsignUtils.filterWmsTrade(staff, uploadData.trades);
            List<Trade> kttTradeList = new ArrayList<>();
            ConsignUtils.filterKttTrade(staff, uploadData.trades,kttTradeList);
            if (CollectionUtils.isNotEmpty(kttTradeList)) {
                result.addConsignRecord(staff, kttTradeList, uploadData.sendType, 1, 0, "快团团帮卖订单禁止发货");
            }
            if (CollectionUtils.isEmpty(uploadData.trades)) {
                return lockFails;
            }
            //如果是供销订单，那么上传供销订单对应的分销订单
            if (CollectionUtils.isEmpty(uploadFilter(staff, uploadData))) {
                return lockFails;
            }



            if (uploadData.warehouseIdTypeMap == null || uploadData.warehouseIdTypeMap.size() == 0) {
                uploadData.warehouseIdTypeMap = warehouseService.getWarehouseIdType(staff);
            }

            List<ConsignRequest> requestList = buildUploadRequest(staff, uploadData, result);
            preJdUpload(staff, uploadData.trades,requestList);
            if (isMergeUpload(uploadData.extendedParams)) {
                mergeTradeProcess(requestList); // KMERP-165834: 速卖通支持勾选合并发货
            } else {
                poisonBrandDeliverMergeTradeProcess(requestList);   // KMERP-135497: 得物、希音直发合并订单处理
            }
//            kuaiShouLogisticsTransitTradeProcess(requestList, staff);
            if (requestList.size() > 0) {
                touchProgress(staff, uploadData, result, requestList);
                //提前记录上传状态
                uploadData.uploadRecords.addAll(uploadRecordBusiness.addUploadRecord(staff, uploadData.uploadTrades, uploadData.sendType));
                //
                fillOtherData(staff, uploadData, requestList);
                //过滤需要上传的订单
                uploadRecordBusiness.filterUpload(staff, uploadData, requestList, result);

                // KMERP-123905: 淘工厂拆单发货上传处理
                skuOneTimeUploadBusiness.multiplePackageHandle(staff, uploadData, requestList, result);

                //阿里健康按sku/数量拆单上传处理
                skuOneTimeUploadBusiness.multiplePackageHandleBySku(staff, uploadData, requestList, result);

                //拆单订单一次上传所有单号 KMERP-137924 群接龙支持拆单发货，并上传平台
                uploadSplitOnceBusiness.onceUploadHandle(staff, requestList, result, uploadData);
                // KMERP-143103: 对接当当网拆分发货场景
                dangDangUploadBusiness.uploadHandle(staff, uploadData, requestList, result);
                fillQimenMultiPackageOutsid(staff, uploadData, requestList, result);

                //上传
                uploadBatchBusiness.upload(staff, uploadData, requestList, result);
                //上传后里面触发一次同步,
                // 先过滤掉分销，分销的同步在handleFxMultiOutSid()之后，需要处理合单的卖家备注，这边直接同步可能导致分销和供销备注不一致从而触发反审核
                uploadAfterBusiness.sendSyncTrade(staff, result.successTrades, true);
            }
            uploadAfterBusiness.uploadAfter(staff, uploadData, result, true);

        } finally {//上传结束
            uploadLockBusiness.unlockUpload(staff, uploadData);
            // 必须放在这里，不能放在uploadAfter 中，不然有些不需要上传的会被过滤掉
            uploadData.addMultiPackagesUploadTrades(result.successTrades);
            uploadData.addMultiPackagesUploadTrades(result.updateTrades);
            //根据配置检测拆单订单是否需要调用上传单号接口,多包裹物流上传,只处理上传成功的
            List<Trade> tradeList = tradeSearchService.queryBySids(staff, false, TradeUtils.toSids(uploadData.getNeedMultiPackagesUploadTrades()));
            Logs.debug(LogHelper.buildLog(staff, String.format("多包裹物流上传%s", TradeUtils.toSidSet(tradeList))));
            uploadPackagesNoticeBusiness.asyncPackagesNotice(staff, tradeList, uploadData.tradeConfig);
            if (uploadData.getNeedDummyUpload() != null && uploadData.getNeedDummyUpload() == 1) {
                try {
                    if (CollectionUtils.isNotEmpty(result.successTrades)) {
                        ConsignHandler handler = new ConsignHandler(SendType.DUMMY)
                                .setEventCenter(eventCenter).setDymmyType(null).setNoLogisticsName(null).setNoLogisticsTel(null);
                         consignBusiness.consign(staff,result.successTrades, handler, uploadData.clientIp);
                    }
                } catch (Exception e) {
                    Logs.debug(LogHelper.buildLog(staff, String.format("全托管多调一次无需物流发货失败%s", JSONObject.toJSONString(e.getMessage()))));
                }
                }else if(uploadData.getNeedDummyUpload()!=null&&uploadData.getNeedDummyUpload()==2){
                    try{
                        if(CollectionUtils.isNotEmpty(result.successTrades)){
                            ConsignHandler handler = new ConsignHandler(SendType.OFFLINE)
                                    .setEventCenter(eventCenter).setDymmyType(null).setNoLogisticsName(null).setNoLogisticsTel(null);
                            consignBusiness.consign(staff, result.successTrades, handler, uploadData.clientIp);
                        }
                    }catch (Exception e){
                        Logs.debug(LogHelper.buildLog(staff,String.format("全托管多调一次系统发货失败%s", JSONObject.toJSONString(e.getMessage()))));
                    }
                }
        }
        return lockFails;
    }

    private void fillQimenMultiPackageOutsid(Staff staff, UploadData uploadData, List<ConsignRequest> requestList, UploadResult result) {
        // 设置一单多包打印信息
        Map<String/*outSid*/, List<MultiPacksPrintTradeLogDetail>> outSid2printDetail = multiPacksPrintTradeLogService.queryPrintLogDetailByOutSids(staff, uploadData.trades);
        Map<String/*outSid*/, Set<String/*subOutSid*/>> printDetailMap = new HashMap<>();

        for (Map.Entry<String/*outSid*/, List<MultiPacksPrintTradeLogDetail>> entry : outSid2printDetail.entrySet()) {
            List<MultiPacksPrintTradeLogDetail> printDetails = entry.getValue();
            if (CollectionUtils.isEmpty(printDetails)) {
                continue;
            }

            String outSid = entry.getKey();
            Set<String> subOutSids = printDetails.stream()
                    .map(MultiPacksPrintTradeLogDetail::getOutSid)
                    .collect(Collectors.toSet());


            printDetailMap.put(outSid, subOutSids);
        }
        for(ConsignRequest request : requestList){
            Trade trade = request.getTrade();
            if(PlatformUtils.isQimenSource(trade)){
                Set<String> linkedSet = new LinkedHashSet<>();
                String outSid = trade.getOutSid();
                linkedSet.add(outSid);
                Set<String> subOutSids = printDetailMap.get(outSid);
                if(CollectionUtils.isNotEmpty(subOutSids)){
                    linkedSet.addAll(subOutSids);
                    if(CollectionUtils.isNotEmpty(linkedSet)){
                        trade.setOutSid(String.join(",",linkedSet));
                        request.setTrade(trade);
                    }
                }
            }
        }
    }

    private void filterIgnoreUploadTrades(Staff staff, UploadData uploadData, UploadResult result) {
        List<Long> ignoreSids = Lists.newArrayList();
        Iterator<Trade> iterator = uploadData.trades.iterator();
        while (iterator.hasNext()) {
            Trade trade = iterator.next();
            if (isIgnoreUploadTrade(staff, trade)) {
                iterator.remove();
                result.addConsignRecord(staff, trade, uploadData.sendType, 0, 0, "当前平台忽略上传");
                ignoreSids.add(trade.getSid());
            }
        }
        if (CollectionUtils.isNotEmpty(ignoreSids)) {
            Logs.info(LogHelper.buildLog(staff, String.format("移除忽略上传平台订单: %s", ignoreSids)));
        }
    }

    private void kuaiShouLogisticsTransitTradeProcess(List<ConsignRequest> requestList, Staff staff) {
        if (CollectionUtils.isEmpty(requestList)) {
            return;
        }
        String[] tids = requestList.stream().map(ConsignRequest::getTrade)
                .filter(TradeUtils::isSplit)
                .filter(t -> PlatformUtils.isKuaiShouLogisticsTransitTrade(staff, t))//仅处理快手流转单
                .map(Trade::getTid)
                .toArray(String[]::new);
        if (tids.length == 0) {
            return;
        }
        // 订单数据
        List<TbTrade> trades = tradeSearchService.queryByTids(staff, true, tids);
        // 上传记录
        List<UploadRecord> uploadRecords = uploadRecordDao.queryByTid(staff, tids);
        Map<String, Set<Long>> uploadSidMap = new HashMap<>();//上传记录map
        Map<Long, String> successRecordMap = new HashMap<>();
        Map<String, Set<Long>> allSidMap = new HashMap<>();//总sid map
        Map<String, Set<Long>> dependTradeSidsMap = new HashMap<>();//剩余上传sid map
        Map<String, List<Trade>> tradesMap = new HashMap<>();
        for (TbTrade trade : trades) {
            String tradeStatus = TradeStatus.getSysStatus(trade.getStatus(), trade.getSysStatus());
            if (CollectionUtils.isEmpty(trade.getOrders()) || 1 == trade.getIsCancel() || Trade.SYS_STATUS_CLOSED.equals(tradeStatus) || Trade.SYS_STATUS_CANCEL.equals(tradeStatus)) {
                continue;
            }
            allSidMap.computeIfAbsent(trade.getTid(), k -> Sets.newHashSet()).add(trade.getSid());
            tradesMap.computeIfAbsent(trade.getTid(), k -> Lists.newArrayList()).add(trade);
        }
        for (UploadRecord uploadRecord : uploadRecords) {
            uploadSidMap.computeIfAbsent(uploadRecord.getTid(), k -> Sets.newHashSet()).add(uploadRecord.getSid());
            if (uploadRecord.getUploadStatus() == 1) {
                successRecordMap.put(uploadRecord.getSid(), uploadRecord.getOutSid());
            }
        }
        for (String key : allSidMap.keySet()) {
            Set<Long> allSidSet = allSidMap.get(key);
            Set<Long> uploadSidSet = uploadSidMap.get(key);
            Set<Long> dependTradeSids = new HashSet<>();
            for (Long sid : allSidSet) {
                if (uploadSidSet == null || !uploadSidSet.contains(sid)) {
                    dependTradeSids.add(sid);
                }
            }
            dependTradeSidsMap.put(key, dependTradeSids);
        }
        Iterator<ConsignRequest> iterator = requestList.iterator();
        while (iterator.hasNext()) {
            ConsignRequest request = iterator.next();
            Trade trade = request.getTrade();

            if (Objects.isNull(trade)) {
                continue;
            }
            if (!PlatformUtils.isKuaiShouLogisticsTransitTrade(staff, trade)) {
                continue;
            }
            Set<Long> leftSids = dependTradeSidsMap.get(trade.getTid());
            Map<String, Object> extendParams = new HashMap<String, Object>() {
            };
            if (CollectionUtils.isEmpty(leftSids)) {
                extendParams.put("isLast", 1);
            }
            String oldOutSid = successRecordMap.get(trade.getSid());
            if (Objects.nonNull(oldOutSid)) {
                extendParams.put("oldOutSid", oldOutSid);
            }
            request.setExtendParams(extendParams);
        }

    }

    private boolean isIgnoreUploadTrade(Staff staff, Trade trade) {
        if (!CommonConstants.PLAT_FORM_TYPE_QINSI.equals(getPlatSource(trade)) && AuditConsignBusiness.consignConsumeStockSource(staff, getPlatSource(trade))) {
            if (Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_KTT)) {
                // 快团团核销订单特殊处理，用上传
                return TradeUtils.isKttSelfAndCancelledTrade(trade);
            }
            if(Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_YYJK)){
                if(PlatformUtils.isTradeO2o(trade)){
                    return true;
                }else{
                    return false;
                }
            }
            return true;
        }
        if(CommonConstants.PLAT_FORM_TYPE_TIKTOK.equals(trade.getSource())){
            String sysStatus = TradeStatus.getSysStatus(trade.getStatus(), null);
            if(Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus)){
                logger.info(LogHelper.buildLog(staff, "对于tiktok平台已发货订单，手动上传、节点自动上传发货都不触发上传发货：" + trade.getSid()));
                return true;
            }
        }
        return TradeUtils.isWxSphBicTrade(trade) || isNfgoodHelpSellTrade(trade);
    }

    private List<ConsignRequest> buildUploadRequest(Staff staff, UploadData uploadData, UploadResult result) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        List<ConsignRequest> list = new ArrayList<>();

        boolean uploadFilterClosed = uploadData.tradeConfig.getInteger(TradeExtendConfigsEnum.TRADE_UPLOAD_FILTER_CLOSED.getKey()) == 1;
        //这里如果是供销订单触发分销发货，因为要使用供销的快递信息发货所以这里要查询供销的
        Map<String, UserExpressTemplate> expressTemplateMap = new HashMap<>();
        Map<Long, ExpressCompany> expressCompanyMap = null;
        if (SendType.DUMMY != uploadData.sendType) {
            expressTemplateMap = UploadUtils.getCompanyTemplate(staff, expressTemplateDubboService);
            expressCompanyMap = expressCompanyService.getExpressCompanyIdMap();
        }

        Map<Long, ConsignRecord> existMap = uploadData.sendType != null ? null : queryRecords(staff, uploadData.trades);
        //如果是分销订单填充对应的供销模版信息
        fxUploadBusiness.fillGxExpress(staff, uploadData.trades, true);
        if (uploadData.trades.size() > 0) {
            if (uploadData.sendType == SendType.BIC_BATCH) {
                ConsignRequest req = UploadUtils.buildConsignRequest(staff, uploadData, uploadData.trades, existMap);
                list.add(req);
            } else {
                for (Trade trade : uploadData.trades) {
                    ConsignRecord consignRecord = null;
                    if (existMap != null && existMap.size() > 0) {
                        consignRecord = existMap.get(trade.getSid());
                    }

                    //这里面就会对重新上传的订单校验了.
                    if (!UploadUtils.validateBase(staff, uploadDelayBusiness, uploadData, trade, result, uploadFilterClosed, consignRecord)) {
                        continue;
                    }
                    //构造上传请求
                    ConsignRequest req = UploadUtils.buildConsignRequest(staff, uploadData, trade, existMap);
                    //无需物流发货不需要验证快递信息，自己联系物流发货需要验证快递信息
                    if (SendType.DUMMY == uploadData.sendType || Objects.equals(getDeliveryType(uploadData.extendedParams), 2)) {
                        req.setValid(true);
                    } else {
                        validateExpress(staff, tradeConfig, expressTemplateDubboService, staffService, uploadData, req, result, expressTemplateMap, expressCompanyMap, fxUploadBusiness, expressCompanyService);
                        if (req.isValid()) {
                            UploadUtils.validateResendOutSid(staff, req, uploadData, result);
                        }
                    }
                    if(StringUtils.isNotBlank(trade.getTemplateName())&&trade.getTemplateName().contains("自提")&&CommonConstants.PLAT_FORM_TYPE_1688.equals(getPlatSource(trade))){
                        req.setDymmyType(0);
                        req.setType(SendType.DUMMY);
                    }
                    if (req.isValid()) {
                        list.add(req);
                        uploadData.uploadTrades.add(trade);
                    }
                }
                if(SendType.MANUAL_UPLOAD.equals(uploadData.sendType)){
                    uploadData.sendType = SendType.UPLOAD;
                }
                //记录延迟发货订单trade trace
                uploadDelayBusiness.addDelayTradeTrace(eventCenter, staff, result);
            }
        }

        result.uploadExcepRecords.putAll(result.consignRecords.stream().filter(t -> (t.getIsError() == 1 && t.getEnableStatus() != 0)).collect(Collectors.toMap(ConsignRecord::getSid, t -> t, (k1, k2) -> k2)));
        return list;
    }

    // KMERP-165834: 速卖通支持勾选合并发货
    private void mergeTradeProcess(List<ConsignRequest> requestList) {
        if (CollectionUtils.isEmpty(requestList) || requestList.size() == 1) {
            return;
        }

        ConsignRequest mergeRequest = requestList.get(0);
        mergeRequest.setDataSendType(3); // 合并发货
        mergeRequest.setTradeList(Lists.newArrayList(mergeRequest.getTrade()));

        Iterator<ConsignRequest> iterator = requestList.iterator();
        while (iterator.hasNext()) {
            ConsignRequest request = iterator.next();
            Trade trade = request.getTrade();
            if (Objects.isNull(trade) || Objects.equals(mergeRequest, request)) {
                continue;
            }
            mergeRequest.getTradeList().add(trade);
            iterator.remove();
        }
    }

    // KMERP-135497: 得物、希音直发合并订单处理
    private void poisonBrandDeliverMergeTradeProcess(List<ConsignRequest> requestList) {
        if (CollectionUtils.isEmpty(requestList)) {
            return;
        }

        Map<Long/*mergeSid*/, ConsignRequest> mergeTradeRequestMap = Maps.newHashMap();
        Iterator<ConsignRequest> iterator = requestList.iterator();
        while (iterator.hasNext()) {
            ConsignRequest request = iterator.next();
            Trade trade = request.getTrade();

            if (Objects.isNull(trade)) {
                continue;
            }
            if (!TradeUtils.isMerge(trade)) {
                continue;
            }
            if (!(isPoisonBrandDeliverMergeTrade(trade) || TradeUtils.isVipJitxTrade(trade))) {
                continue;
            }

            ConsignRequest mergeRequest = mergeTradeRequestMap.get(trade.getMergeSid());
            if (Objects.isNull(mergeRequest)) {
                request.setDataSendType(3); // 合并发货
                request.setTradeList(Lists.newArrayList(trade));
                mergeTradeRequestMap.put(trade.getMergeSid(), request);
            } else {
                mergeRequest.getTradeList().add(trade);
                iterator.remove();
            }
        }
    }

    // 得物直发合并订单
    private boolean isPoisonBrandDeliverMergeTrade(Trade trade) {
        return (TradeUtils.isPoisonBrandDeliverTrade(trade) || TradeUtils.isSheinDeliverTrade(trade) || TradeUtils.isSmtqtgDeliverTrade(trade)) && TradeUtils.isMerge(trade);
    }

    private Map<Long, ConsignRecord> queryRecords(Staff staff, List<Trade> trades) {
        Long[] sids = new Long[trades.size()];
        for (int i = 0; i < trades.size(); i++) {
            sids[i] = trades.get(i).getSid();
        }
        //查询包括enableStatus=0在内的上传记录
        List<ConsignRecord> records = consignRecordDao.listBySids(staff, sids);
        Map<Long, ConsignRecord> map = new HashMap<>(records.size());
        for (ConsignRecord record : records) {
            map.put(record.getSid(), record);
        }
        return map;
    }

    private void fillOtherData(Staff staff, UploadData uploadData, List<ConsignRequest> requestList) {
        User gxUser = null;
        Map<Long, Trade> fdsTradeMap = new HashMap<>();
        Set<String> tids = new HashSet<>();
        Set<Long> oids = new HashSet<>();
        if (uploadData.sendType == SendType.BIC_BATCH) {
            ConsignRequest req = requestList.get(0);
            List<Trade> tradeList = req.getTradeList();
            tids.addAll(TradeUtils.toTidList(tradeList));
            for (Trade trade : tradeList) {
                if (TradeUtils.isSplit(trade)) {
                    TradeUtils.getOrders4Trade(trade).forEach(order -> {
                        tids.add(order.getTid());
                        oids.add(order.getOid());
                    });
                }
                if (TradeUtils.isGxTrade(trade)) {
                    if (gxUser == null) {
                        gxUser = UploadUtils.buildGxUser(staffService.queryDefaultStaffByCompanyId(trade.getCompanyId()), trade.getCompanyId());
                    }
                } else {
                    Long userId = trade.getUserId();
                    getAndSetUser(staff, uploadData, trade, userId);
                }
            }
        } else {
            for (ConsignRequest req : requestList) {
                List<Trade> trades = Lists.newArrayList(req.getTrade());
                if (Objects.equals(req.getDataSendType(), 2) || Objects.equals(req.getDataSendType(), 3)) {
                    trades = req.getTradeList();
                }
                for (Trade trade : trades) {
                    tids.add(trade.getTid());
                    if (TradeUtils.isSplit(trade)) {
                        TradeUtils.getOrders4Trade(trade).forEach(order -> {
                            tids.add(order.getTid());
                            oids.add(order.getOid());
                        });
                    }
                    if ("fds".equals(trade.getSubSource())) {
                        fdsTradeMap.put(trade.getSid(), trade);
                    }
                    if (TradeUtils.isGxTrade(trade)) {
                        if (gxUser == null) {
                            gxUser = UploadUtils.buildGxUser(staffService.queryDefaultStaffByCompanyId(trade.getCompanyId()), trade.getCompanyId());
                        }
                    } else {
                        Long userId = trade.getUserId();
                        getAndSetUser(staff, uploadData, trade, userId);
                    }
                }
            }
        }

        uploadData.gxUser = gxUser;
        //代打订单
        if (fdsTradeMap.size() > 0) {
            List<TradeExt> tradeExts = tradeExtDao.tradeExtsGetBySids(staff, new ArrayList<>(fdsTradeMap.keySet()));
            if (tradeExts != null && tradeExts.size() > 0) {
                tradeExts.forEach(ext -> fdsTradeMap.get(ext.getSid()).setTradeExt(ext));
            }
        }
        uploadRecordBusiness.fillRecordByTid(staff, tids.toArray(new String[0]), uploadData.tidOidRecordMap);
        uploadData.tidOidPlatOrderMap = uploadRecordBusiness.getGroupOidPlatOrderByOid(staff, tids.toArray(new String[0]), oids.toArray(new Long[0]));
    }

    private void getAndSetUser(Staff staff, UploadData uploadData, Trade trade, Long userId) {
        User user = uploadData.userMap.get(userId);
        // 20230515: 【工单-AYvUyRDfwUAiE】发货上传授权优先从 UserService 中获取
        if (Objects.nonNull(user)) {
            return;
        }
        // KMERP-143784:【优化任务】这个先记录下，建个优化任务：发货上传尽量减少对首页的调用
        user = staff.getUserByUserId(userId);
        if (Objects.isNull(user) || TradeConsignConfigDiamondUtils.needQueryAuth(user.getSource())) {
            user = userService.queryById(userId);
        }
        if (Objects.nonNull(user)) {
            uploadData.userMap.put(user.getId(), user);
            user.setStaff(staff);
        }
        // if (user == null) {
        //     user = staff.getUserByUserId(trade.getUserId());
        //     if (user != null) {
        //         uploadData.userMap.put(trade.getUserId(), user);
        //     } else {
        //         user = uploadData.userMap.get(trade.getUserId());
        //         if (user == null) {
        //             user = userService.queryById(trade.getUserId());
        //             if (user != null) {
        //                 uploadData.userMap.put(user.getId(), user);
        //                 user.setStaff(staff);
        //             }
        //         }
        //     }
        // }
    }

    /**
     * 京东订单发货前绑定去青龙单号关系
     */
    private void preJdUpload(Staff staff, List<Trade> trades,List<ConsignRequest> consignRequests) {
        Set<Long> allowUploadSids = new HashSet<>();
        for(ConsignRequest consignRequest : consignRequests){
          allowUploadSids.add(consignRequest.getTrade().getSid());
        }
        if (CollectionUtils.isNotEmpty(trades)) {
            List<Trade> needbind = trades.stream().filter(trade -> trade.getTemplateType() != null && 1 == trade.getTemplateType()&&allowUploadSids.contains(trade.getSid())).collect(Collectors.toList());
            if (needbind.size() > 0) {
                try {
                    eventCenter.fireEvent(this, new EventInfo("jd.waybill.send").setArgs(new Object[]{staff, null}).setAsync(false), needbind);
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "处理京东青龙绑定关系出错:" + e.getMessage()), e);
                }
            }
        }
    }

    private void touchProgress(Staff staff, UploadData uploadData, UploadResult result, List<ConsignRequest> requestList) {
        if (uploadData.process) {
            ProgressData progressData = progressService.queryProgress(staff, ProgressEnum.PROGRESS_TRADE_CONSIGN_UPLOAD);
            if (progressData != null) {
                uploadData.progressData = progressData;
                progressData.setCountAll(requestList.size() + result.consignRecords.size() + (progressData.getCountAll() != null ? progressData.getCountAll() : 0))
                        .setErrorNum((long) result.consignRecords.size())
                        .setCountCurrent(result.consignRecords.size() + (progressData.getCountCurrent() != null ? progressData.getCountCurrent() : 0))
                        .setProgress(1);
                progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_CONSIGN_UPLOAD, progressData);
            }
        }
    }

    private void touchProgressGx(Staff staff, UploadData uploadData, List<Trade> gxTrades) {
        if (uploadData.process) {
            ProgressData progressData = progressService.queryProgress(staff, ProgressEnum.PROGRESS_TRADE_CONSIGN_UPLOAD);
            if (progressData != null) {
                uploadData.progressData = progressData;
                List<String> successMsgs = progressData.getSuccessMsgs();
                if (successMsgs == null) {
                    successMsgs = new ArrayList<>();
                }
                successMsgs.addAll(gxTrades.stream().map(trade -> trade.getSid().toString()).collect(Collectors.toList()));
                progressData.setSuccessMsgs(successMsgs);
                progressData.setCountAll((progressData.getCountAll() != null ? progressData.getCountAll() : 0) + gxTrades.size())
                        .setCountCurrent((progressData.getCountCurrent() != null ? progressData.getCountCurrent() : 0) + gxTrades.size())
                        .setSucNum((progressData.getSucNum() != null ? progressData.getSucNum() : 0) + gxTrades.size())
                        .setProgress(1);
                progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_CONSIGN_UPLOAD, progressData);
            }
        }
    }

    // 可以忽略上传的订单
    private List<Trade> ignoreUploadTrades(Staff staff, UploadData uploadData) {
        List<Trade> ignoreUploadTrades = Lists.newArrayList();
        Iterator<Trade> iterator = uploadData.trades.iterator();
        while (iterator.hasNext()) {
            Trade trade = iterator.next();
            if (ignoreUpload(staff, uploadData, trade)) {
                iterator.remove();
                ignoreUploadTrades.add(trade);
                logger.info(LogHelper.buildLog(staff, String.format("sid: %s, tid: %s 存在重复oid忽略上传", trade.getSid(), trade.getTid())));
            }
        }
        return ignoreUploadTrades;
    }

    // 忽略上传
    private boolean ignoreUpload(Staff staff, UploadData uploadData, Trade trade) {
        // 不为拆单
        if (!TradeUtils.isSplit(trade)) {
            return false;
        }

        //KMERP-137922【快团团-供货商】支持拆单发货，并上传平台
        if (TradeUtils.isSupportAppendUpload(staff, trade)) {
            return false;
        }
        // KMERP-123905: 淘工厂拆单发货上传处理
        if (isMultiplePackagesUpload(uploadData.tradeConfig, trade)) {
            return false;
        }
        // KMERP-155033: 阿里健康拆单发货上传处理
        if (isMultiplePackagesUploadBySku(uploadData.tradeConfig, trade)) {
            return false;
        }
        //KMERP-137924 群接龙支持拆单发货，并上传平台(支持按数量、商品拆分一次性上传平台)
        if (TradeUtils.isSupportOnceUpload(staff, trade)) {
            return false;
        }
        // 子单为空
        Set<Long> oids = Optional.ofNullable(TradeUtils.getOrders4Trade(trade))
                .filter(CollectionUtils::isNotEmpty)
                .map(orders -> orders.stream().map(Order::getOid).collect(Collectors.toSet()))
                .orElse(null);
        if (CollectionUtils.isEmpty(oids)) {
            return false;
        }

        // 订单下子单全部已上传发货，无需继续上传发货
        return Optional.ofNullable(uploadData.tidOidSplitInfo.get(trade.getTid()))
                .filter(CollectionUtils::isNotEmpty)
                .filter(sets -> sets.containsAll(oids))
                .isPresent();
    }

    private void laterRetryLockFailedTrade(Staff staff, SendType sendType, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        if(SendType.AHEAD.equals(sendType)) {
            return;
        }
        // 存在的问题：1.发货延迟、2. 不会标记异常、3.不会记录订单日志、4.如果该订单已经发货成功了，会重试一次（重试可能会标记上传异常）
        Logs.error(LogHelper.buildLog(staff, String.format("上传发货并发重试失败订单,sids=%s", TradeUtils.toSidList(trades))));
        List<ConsignRecord> laterRetryRecords = trades.stream()
                .map(trade -> ConsignUtils.buildFullRecord(trade, sendType, 1, 1).setErrorDesc("上传发货失败，请稍后重试"))
                .collect(Collectors.toList());
        consignRecordDao.batchInsert(staff, laterRetryRecords);
    }

    /**
     * 代码从ConsignWaveBusiness迁移过来
     */
    public void upload(Staff staff, Long[] sids, String clientIp) {
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, true, true, sids);
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        UploadResult uploadResult = this.upload(staff, UploadUtils.filterUploadByConfig(staff, trades, tradeConfig).toArray(new Long[0]), SendType.MANUAL_UPLOAD, clientIp, null, null, null, false, false);
        addPreUploadTag(staff, trades, uploadResult.consignRecords);
    }

    /**
     * 代码从ConsignWaveBusiness迁移过来
     */
    private void addPreUploadTag(Staff staff, List<Trade> trades, List<ConsignRecord> consignRecords) {
        Map<Long, Trade> sidTradeMap = trades.stream().collect(Collectors.toMap(Trade::getSid, Function.identity(), (o1, o2) -> o1));
        List<Long> tagSids = consignRecords.stream()
                .filter(r -> r.getIsError() == 0)   // 发货成功
//                .filter(r -> !r.isUploadByService()) // 通过新版发货服务的不用处理
                .map(ConsignRecord::getSid)
                .filter(sid -> Optional.ofNullable(sidTradeMap.get(sid)).filter(trade -> !TradeUtils.isAfterSendGoods(trade)).isPresent())  // 订单状态不为系统已发货
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tagSids)) {
            eventCenter.fireEvent(this, new EventInfo("trade.autotag.preupload").setArgs(new Object[]{staff, tagSids}), null);
        }
    }

    public List<Long> uploadExternal(Staff staff, Trade origin, boolean isSysDelivered) {
        String expressCode = origin.getExpressCode();
        String outSid = origin.getOutSid();
        Long sid = origin.getSid();


        Logs.info(LogHelper.buildLogHead(staff).append(String.format("收到采购发货请求,sid=%s,outsid=%s,expressCode=%s,isSysDelivered=%s,actualPostFee=%s,orders=%s", sid,outSid,expressCode,isSysDelivered,origin.getActualPostFee(),CollectionUtils.isEmpty(TradeUtils.getOrders4Trade(origin)))));

        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, true, sid);
        boolean isSpecialPlatform = false;
        for(Trade trade : trades) {
            if(jstdPlatforms.contains(trade.getSource())){
                isSpecialPlatform = true;
            }
        }
        List<Long> successSidList = new ArrayList<>();
        try {
            if (StringUtils.isNotEmpty(outSid)) {
                if (StringUtils.isEmpty(expressCode)) {
                    throw new IllegalArgumentException("面单号和快递公司编码需要一起传递.");
                }
                if(isSpecialPlatform&&"HTKY".equals(expressCode)){
                    expressCode = "JTSD";
                }
                ExpressCompany expressCompany = expressCompanyService.getExpressCompanyByCode(expressCode);
                Assert.notNull(expressCompany, String.format("该快递公司%s不存在.", expressCode));
                List<Long> uploadSellerMemoSids = new ArrayList<>();
                Long templateId = null;
                Integer templateType = null;
                Long logisticsCompanyId = null;
                for(Trade trade : trades){
                    if(StringUtils.isNotEmpty(trade.getOutSid())&&!outSid.equals(trade.getOutSid())){//过来的单号不是老的 走追加备注逻辑
                        uploadSellerMemoSids.add(trade.getSid());
                    }
                    trade.setOutSid(outSid);
                    trade.setExpressCompanyId(expressCompany.getId());
                    MatchTemplateDTO templateDTO = printExpressTemplateService.matchTemplate(staff,expressCompany.getId(),trade.getSource());
                    if(templateDTO!=null){
                        trade.setTemplateId(templateDTO.getTemplateId());
                        templateId = templateDTO.getTemplateId();
                        templateType = templateDTO.getTemplateType();
                        logisticsCompanyId = templateDTO.getLogisticsCompanyId();
                    }
                }
                Logs.info(LogHelper.buildLogHead(staff).append(String.format("从打印获取到的templateId,templateId=%s", templateId)));
                if(CollectionUtils.isNotEmpty(uploadSellerMemoSids)){
                    Map<Long, List<Trade>> userIdTradesMap = TradeUtils.groupByUserId(trades);
                    Map<Long, User> userMap = new HashMap<>();
                    for(Map.Entry<Long,List<Trade>> entry : userIdTradesMap.entrySet()) {
                        staffAssembleBusiness.getUser(staff, entry.getKey(), userMap);
                    }
                    staff.setUserIdMap(userMap);
                    staff.setUsers(new ArrayList<>(userMap.values()));
                    Logs.info(LogHelper.buildLogHead(staff).append(String.format("需要更新备注的sid,sid=%s", JSONObject.toJSONString(uploadSellerMemoSids))));
                    TradeMemo tradeMemo = TradeMemo.builder()
                            .handType(TradeMemoConstant.HAND_TYPE_PLAT)
                            .sellerMemo(String.format("一键代发多包裹：快递公司：%s，快递单号：%s", expressCompany.getName(), outSid))
                            .isHandlerMemo(0)
                            .append(true)
                            .handFrom(TradeMemoConstant.HAND_FROM_WEB).build();

                    tradeUpdateSellerMemoFlagBusiness.update(staff,tradeMemo,uploadSellerMemoSids.toArray(new Long[uploadSellerMemoSids.size()]));
                    successSidList.add(sid);
                    sendActualPosTFeeAndOrder(staff,origin, SYS_STATUS_SELLER_SEND_GOODS);
                    return successSidList;
                }
                if (isSysDelivered) {
                    try {
                        Long[] lockSids = TradeUtils.toSids(trades);
                        List<Trade> updateTrades=new ArrayList<>();
                        List<Order> updateOrders = new ArrayList<>();
                        Date consignTime = new Date();
                        Logs.info(LogHelper.buildLogHead(staff).append(String.format("开始更新订单状态,sid=%s", JSONObject.toJSONString(lockSids))));
                        Long finalTemplateId = templateId;
                        Integer finalTemplateType = templateType;
                        Long finalLogisticsCompanyId = logisticsCompanyId;
                        lockService.locks(tradeLockBusiness.getERPLocks(staff, lockSids), () -> {
                            List<Trade> tradeList = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, lockSids);
                            for (Trade indexTrade : tradeList) {
                                if (TradeStatusUtils.isAfterSendGoods(indexTrade.getSysStatus())) {
                                    continue;
                                }
                                Trade updateTrade = TradeBuilderUtils.builderUpdateExcep(indexTrade);
                                updateTrade.setSysStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS);
                                indexTrade.setSysStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS);
                                updateTrade.setSysConsigned(1);
                                updateTrade.setConsignTime(consignTime);
                                updateTrade.setOutSid(outSid);
                                updateTrade.setTemplateId(finalTemplateId);
                                updateTrade.setTemplateType(finalTemplateType);
                                updateTrade.setExpressCompanyId(expressCompany.getId());
                                updateTrade.setLogisticsCompanyId(finalLogisticsCompanyId);
                                indexTrade.setOutSid(outSid);
                                indexTrade.setExpressCompanyId(expressCompany.getId());
                                indexTrade.setTemplateType(finalTemplateType);
                                indexTrade.setTemplateId(finalTemplateId);
                                indexTrade.setLogisticsCompanyId(finalLogisticsCompanyId);
                                List<Order> orders = TradeUtils.getOrders4Trade(indexTrade);
                                if (CollectionUtils.isEmpty(orders)) {
                                    Logs.warn(LogHelper.buildLogHead(staff).append(String.format("一键代发的商品不存在,sid=%s", indexTrade.getSid())));
                                    continue;
                                }
                                for (Order order : orders) {
                                    if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                                        continue;
                                    }
                                    order.setSysStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS);
                                    order.setSysConsigned(1);
                                    order.setConsignTime(consignTime);
                                    updateOrders.add(buildUpdateOrder(staff, order));
                                    List<Order> suits = order.getSuits();
                                    if (CollectionUtils.isNotEmpty(suits)) {
                                        for (Order suit : suits) {
                                            suit.setSysStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS);
                                            suit.setSysConsigned(1);
                                            suit.setConsignTime(consignTime);
                                            updateOrders.add(buildUpdateOrder(staff, suit));
                                        }
                                    }
                                }
                                if (TradeExceptUtils.isContainExcept(staff, indexTrade, ExceptEnum.CAI_GOU_TRADE_EXCEPT)) {
                                    TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.CAI_GOU_TRADE_EXCEPT, 0L);
                                    updateTrade.getOperations().put(OpEnum.EXCEPT_UPDATE,String.format("发货后取消【%s】异常",ExceptEnum.CAI_GOU_TRADE_EXCEPT.getChinese()));
                                }
                                orderStockService.resumeOrderStockCommonLocal(staff,orders,null);
                                updateTrades.add(updateTrade);
                            }
                            tradePtService.saveByTrades(staff, updateTrades);
                            tradeUpdateService.updateTrades(staff, updateTrades,updateOrders);
                            return null;
                        });

                        // 记录操作日志
                        List<Trade> tradeTraces = updateTrades.stream().filter(index -> StringUtils.isNotBlank(index.getOperations().get(OpEnum.EXCEPT_UPDATE))).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(tradeTraces)) {
                            tradeTraceBusiness.asyncTrace(staff, tradeTraces, OpEnum.EXCEPT_UPDATE);
                        }
                    } catch (Throwable e) {
                        logger.error(LogHelper.buildLog(staff, "发货上传后更新订单失败"), e);
                    }
                }
                sendActualPosTFeeAndOrder(staff,origin,Trade.SYS_STATUS_WAIT_AUDIT);
                UploadData data = UploadData.build(staff,trades,SendType.UPLOAD,null,0);
                UploadResult result = newUploadBusiness.upload(staff,data,true);
                if (!data.hasData()) {
                    return TradeUtils.toSidList(result.successTrades);
                }
                List<UploadRecord> uploadRecords = uploadRecordBusiness.addUploadRecord(staff, trades, SendType.UPLOAD);
                for(Trade trade : trades){
                    UploadResult result1 = new UploadResult();
                    trade.setOutSid(outSid);
                    trade.setExpressCode(expressCode);
                    trade.setExpressCompanyId(expressCompany.getId());
                    ConsignRequest consignRequest = new ConsignRequest();
                    consignRequest.setType(SendType.UPLOAD);
                    consignRequest.setExpress(expressCompany);
                    User user = userService.queryById(trade.getUserId());
                    Assert.notNull(user, "该店铺不存在");
                    user.setStaff(staff);
                    consignRequest.setTrade(trade);
                    List<ConsignRecord> consignRecords = platformUploader.upload(user, consignRequest, result);
                    uploadRecordBusiness.updateUploadRecordByConsignResult(staff, uploadRecords , result1);
                    consignRecordDao.batchInsert(staff, result.consignRecords);
                    uploadAfterBusiness.uploadAfter(staff,data,result1,true);
                    successSidList.addAll(TradeUtils.toSidList(result1.successTrades));
                    if(successSidList.size()==0){
                        for(ConsignRecord record : consignRecords){
                            if(record.getIsError()!=null&&record.getIsError()==0){
                                successSidList.add(record.getSid());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "发货失败:"), e);
        }
        return successSidList;
    }

    public boolean platformIsConsigned(Trade trade) {
        if (SYS_STATUS_SELLER_SEND_GOODS.equals(TradeStatus.getSysStatus(trade.getStatus(), null))) {//如果订单在系统的平台状态是卖家已发货
            return true;
        } else if (TB_SELLER_CONSIGNED_PART.equals(trade.getStatus()) || TradeUtils.isSplit(trade)) {
            boolean splitOrderAllConsigned = true;
            for (Order order : TradeUtils.getOrders4Trade(trade)) {//拆单的order平台状态只要存在待发货，则返回false，需要上传
                splitOrderAllConsigned = splitOrderAllConsigned && TradeStatusUtils.isAfterSendGoods(TradeStatus.getSysStatus(order.getStatus(), null));
            }
            return splitOrderAllConsigned;
        }
        return false;
    }

    private Order buildUpdateOrder(Staff staff, Order order) {
        Order update = new TbOrder();
        update.setId(order.getId());
        update.setSysStatus(order.getSysStatus());
        update.setSysConsigned(order.getSysConsigned());
        update.setConsignTime(order.getConsignTime());
        return update;
    }

    private void sendActualPosTFeeAndOrder(Staff staff,Trade origin,String sysStatus) {
        List<Order> orders = TradeUtils.getOrders4Trade(origin);
        String actualPostFee = origin.getActualPostFee();
        if(!CollectionUtils.isEmpty(orders)||StringUtils.isNotBlank(actualPostFee)){
            List<Trade> trades1 = new ArrayList<>();
            origin.setSysStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS);
            origin.setSysConsigned(1);
            Trade beforeTrade = new Trade();
            beforeTrade.setSid(origin.getSid());
            beforeTrade.setSysStatus(sysStatus);
            origin.setOrigin(beforeTrade);
            trades1.add(origin);
            eventCenter.fireEvent(this, new EventInfo("trade.1688.cost.update").setArgs(new Object[]{staff,trades1}), null);
        }
    }
}

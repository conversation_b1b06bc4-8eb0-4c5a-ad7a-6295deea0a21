package com.raycloud.dmj.business.stock;

import com.google.common.collect.Lists;
import com.raycloud.dmj.Buffers;
import com.raycloud.dmj.business.common.StockKey;
import com.raycloud.dmj.business.common.StockLockBusiness;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.common.StockOrderAdjustEventBusiness;
import com.raycloud.dmj.business.trade.StockTradeHelpBusiness;
import com.raycloud.dmj.business.trade.VirtualWarehouseStockSwapBusiness;
import com.raycloud.dmj.business.warehouse.TradeWarehouseBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.enums.Feature;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.stock.StockOrderRecord;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.OrderBuilderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeBuilderUtils;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStockUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.except.domain.TradeExcept;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.trades.IProgressService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeUpdateService;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.trades.support.TbTradeSearchService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import com.raycloud.ec.api.*;
import com.raycloud.ec.api.annotation.ListenerBind;
import com.raycloud.erp.buffer.service.IBufferService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单库存重新分配业务
 *
 * <AUTHOR>
 * @created 2019-06-19 15:50
 */
@Service
@ListenerBind("trade.stock.reallocate")
public class StockReallocateBusiness implements IEventListener {

    @Resource
    StockTradeHelpBusiness stockTradeHelpBusiness;
    @Resource
    private ILockService lockService;
    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource
    TbTradeSearchService tbTradeSearchService;
    @Resource
    ITradeUpdateService tradeUpdateService;
    @Resource
    IProgressService progressService;
    @Resource
    IEventCenter eventCenter;
    @Resource
    StockNormalReallocateBusiness stockNormalReallocateBusiness;
    @Resource
    TradeWarehouseBusiness tradeWarehouseBusiness;
    @Resource
    IBufferService bufferService;
    @Resource(name = "tbTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    private ITradeConfigService tradeConfigService;

    @Resource
    FeatureService featureService;

    @Resource
    TbOrderDAO tbOrderDAO;

    @Resource
    IOrderStockService orderStockService;

    private static final Logger logger = Logger.getLogger(StockReallocateBusiness.class);

    @Override
    public void onObserved(EventSourceBase eventSourceBase) {
        CommonEventSource evt = (CommonEventSource) eventSourceBase;
        Staff staff = evt.getArg(0, Staff.class);
        /**
         * 0 按预发货、付款时间重新分配库存
         * 1 按预发货、能成单、付款时间重新分配库存
         * 2 按预发货、加急、付款时间重新分配库存
         * 3 按预发货、多件、付款时间重新分配库存
         * 4 按预发货、承诺时间存在且正序、付款时间重新分配库存
         * 5 按预发货、承诺时间、付款时间重新分配库存 (承诺时间默认2000-01-01)
         */
        Integer type = evt.getArg(1, Integer.class);
        long start = System.currentTimeMillis();
        try {
            if (type != null && type - 1 == 0) {
                stockNormalReallocateBusiness.execute(staff);
            } else {
                execute(staff, type);
            }
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("库存重新分配成功,[type=%s]耗时%sms",type, (System.currentTimeMillis() - start))));
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, String.format("[type=%s]库存重新分配报错", type)), e);
        }

    }

    public List<Trade> check(Staff staff) {
        List<StockOrderRecord> list = stockTradeHelpBusiness.queryRecord2Correct(staff);
        if (!list.isEmpty()) {
            Set<Long> orderIds = new HashSet<Long>();
            Set<Long> sids = new HashSet<Long>();
            list.forEach(r -> {
                orderIds.add(r.getOrderId());
                sids.add(r.getSid());
            });
            Long[] sidArr = sids.toArray(new Long[0]);
            final  List<String> logs = new ArrayList<String>();
            List<Trade> tradeList = lockService.locks(tradeLockBusiness.getERPLocks(staff, sidArr), () -> {
                Map<Long, StockOrderRecord> map = stockTradeHelpBusiness.queryByOrderIds(staff, orderIds).stream().collect(Collectors.toMap(StockOrderRecord::getOrderId, Function.identity()));
                List<Trade> trades = tbTradeSearchService.queryBySidsContainMergeTrade(staff, true, sidArr);
                return reallocate(staff, trades, map, logs);
            });
            if (logger.isDebugEnabled()) {
                logs.forEach(logger::debug);
            }
            eventCenter.fireEvent(this, new EventInfo("trade.stock.check").setArgs(new Object[]{staff}), tradeList);
            return tradeList;
        }
        return null;
    }

    public void execute(Staff staff, Integer type) {
        ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_SWAP_STOCK_OUTERID).setProgress(1);
        List<Warehouse> warehouses = tradeWarehouseBusiness.getWarehouses(staff);
        try {
            for (Warehouse warehouse : warehouses) {
                StockKey stockKey = new StockKey(warehouse.getId(), null, null);
                stockKey.setSysSkuId(null);
                List<StockOrderRecord> itemHasInsufficient = stockTradeHelpBusiness.queryItemHasInsufficient(staff, stockKey, 10000L);

                String msg = String.format("仓库[%s]共查询到%s个缺货商品,准备重新分配库存", warehouse.getName(), itemHasInsufficient.size());
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, msg));
                }
                for (StockOrderRecord r : itemHasInsufficient) {
                    try {
                        stockKey.setSysItemId(r.getSysItemId());
                        stockKey.setSysSkuId(r.getSysSkuId());
                        int[] rs = execute(staff, stockKey, type);
                    } catch (Exception e) {
                        progressData.setErrorNum(Optional.ofNullable(progressData.getErrorNum()).orElse(0L) + 1);
                        logger.error(LogHelper.buildLog(staff, String.format("%s库存重新分配出错", stockKey)), e);
                    }
                }
            }
        } finally {
            //设置完成标志
            Long sucNum = searcherWaitAudit(staff);
            progressData.setSucNum(sucNum);
            progressService.updateProgressComplete(staff, ProgressEnum.PROGRESS_TRADE_SWAP_STOCK_OUTERID, progressData);
            progressService.updateProgressComplete(staff, ProgressEnum.PROGRESS_TRADE_AUDIT_MANUAL, progressData);
        }
    }

    public int[] execute(Staff staff, StockKey stockKey, Integer type) {
        return lockService.lock(StockLockBusiness.key2ERPLock(staff, StockLockBusiness.getKey(stockKey), true), () -> reallocate(staff, stockKey, type));
    }

    private String getOrderName(Integer type) {
        if (type != null) {
            if (type - 2 == 0) {
                return "urgent";
            } else if (type - 3 == 0) {
                return "itemNum";
            } else if (type == 4) {
                return "timeoutExist";
            } else if (type == 5) {
                return "timeout";
            }
        }
        return "time";
    }

    public int[] reallocate(Staff staff, StockKey stockKey, Integer type) {
        //矫正stockOrderRecord中订单的付款时间
        int[] rs = new int[2];
        stockTradeHelpBusiness.updatePayTime(staff, buildParams(stockKey));
        List<StockOrderRecord> allRecords = stockTradeHelpBusiness.query4Reallocate(staff, stockKey, getOrderName(type), 30000L);
        if (isSwapVirtual(staff, allRecords)) {
            logger.debug(LogHelper.buildLog(staff, String.format("[sysItemId=%s,sysSkuId=%s,warehouseId=%s]进行虚拟库存调剂，stockOrderRecord总数:%s", stockKey.getSysItemId(), stockKey.getSysSkuId(), stockKey.getWarehouseId(), allRecords.size())));
            reallocateVirtual(staff, allRecords, type);
            return rs;
        }
        long totalAllocateNum = allRecords.stream().mapToLong(StockOrderRecord::getStockNum).sum();
        if (totalAllocateNum > 0) {
            List<StockOrderRecord> updateRecords = new ArrayList<StockOrderRecord>();
            for (int i = 0; i < allRecords.size(); i++) {
                StockOrderRecord obj = allRecords.get(i);
                long oldStockNum = obj.getStockNum();
                obj.setStockNum(totalAllocateNum >= obj.getNum() ? obj.getNum() : (totalAllocateNum > 0 ? totalAllocateNum : 0));
                totalAllocateNum -= obj.getStockNum();
                if (oldStockNum - obj.getStockNum() != 0) {
                    obj.setDifferenceValue(obj.getNum() - obj.getStockNum());
                    obj.setStockStatus(obj.getDifferenceValue() == 0 ? 1 : 3);
                    updateRecords.add(obj);
                    rs[1]++;
                }
                //每100个做一次批量处理
                if (updateRecords.size() == 100 || (!updateRecords.isEmpty() && i == allRecords.size() - 1)) {
                    stockTradeHelpBusiness.batchUpdate4Adjust(staff, updateRecords);
                    bufferService.buffer(Buffers.build(staff, StockOrderAdjustEventBusiness.BUFFER_TYPE), updateRecords.stream().map(s -> s.getOrderId().toString()).collect(Collectors.toList()));
                    //type=0/2/3
                    eventCenter.fireEvent(this, new EventInfo(StockOrderAdjustEventBusiness.EVENT_NAME).setArgs(new Object[]{staff, (type == null ? 0 : type) + 1}), null);
                    updateRecords.clear();
                }
            }
            rs[0] = 1;
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("[sysItemId=%s,sysSkuId=%s,warehouseId=%s]执行库存重新分配,totalAllocateNum=%s,stockOrderRecord总数:%s,其中更新的数量:%s", stockKey.getSysItemId(), stockKey.getSysSkuId(), stockKey.getWarehouseId(), totalAllocateNum, allRecords.size(), rs[1])));
            }
        } else {
            if (logger.isDebugEnabled()) {
                if (allRecords.isEmpty()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("[sysItemId=%s,sysSkuId=%s,warehouseId=%s]没有库存锁定记录,忽略库存重新分配", stockKey.getSysItemId(), stockKey.getSysSkuId(), stockKey.getWarehouseId())));
                } else if (totalAllocateNum == 0) {
                    logger.debug(LogHelper.buildLog(staff, String.format("[sysItemId=%s,sysSkuId=%s,warehouseId=%s]当前总分配数为0,忽略库存重新分配", stockKey.getSysItemId(), stockKey.getSysSkuId(), stockKey.getWarehouseId())));
                }
            }
            rs[0] = 0;
        }
        return rs;
    }


    /**
     * 虚拟仓库存调剂
     */
    public void reallocateVirtual(Staff staff, List<StockOrderRecord> allRecords, Integer type) {
        if (CollectionUtils.isEmpty(allRecords)) {
            return;
        }
        List<TbOrder> orders = tbOrderDAO.queryByIds(staff, allRecords.stream().map(StockOrderRecord::getOrderId).toArray(Long[]::new));
        List<Order> targetOrders = new ArrayList<>();
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        Map<Long, Order> orderMap = OrderUtils.toMapById(orders);
        for (StockOrderRecord orderRecord : allRecords) {
            if (Objects.isNull(orderMap.get(orderRecord.getOrderId()))) {
                continue;
            }
            targetOrders.add(orderMap.get(orderRecord.getOrderId()));
        }
        List<StockOrderRecord> swapRecords = orderStockService.swapVirtualStock(staff, targetOrders, targetOrders);
        for (List<StockOrderRecord> subRecords : Lists.partition(swapRecords, 100)) {
            bufferService.buffer(Buffers.build(staff, StockOrderAdjustEventBusiness.BUFFER_TYPE), subRecords.stream().map(s -> s.getOrderId().toString()).collect(Collectors.toList()));
            //type=0/2/3
            eventCenter.fireEvent(this, new EventInfo(StockOrderAdjustEventBusiness.EVENT_NAME).setArgs(new Object[]{staff, (type == null ? 0 : type) + 1}), null);
        }
    }

    //是否是虚拟仓调剂
    private boolean isSwapVirtual(Staff staff, List<StockOrderRecord> stockOrderRecords) {
        // 未开启虚拟仓 跳过
        if (featureService.checkHasFeature(staff.getCompanyId(), Feature.VIRTUAL_WAREHOUSE_STOCK)) {
            return true;
        }
        // 开启虚拟仓，但是调剂的商品没有虚拟仓的， 跳过
        Set<StockOrderRecord> collect = stockOrderRecords.stream().filter(StockOrderRecord::getUsePrivateStock).collect(Collectors.toSet());
        if (!collect.isEmpty()) {
            return true;
        }
        return false;
    }


    private Order buildUpdateOrder(Staff staff,Order order) {
        Order updateOrder = OrderBuilderUtils.builderUpdateOrder(order);
        updateOrder.setId(order.getId());
        updateOrder.setStockNum(order.getStockNum());
       // updateOrder.setStockStatus(order.getStockStatus());
        OrderExceptUtils.setStockStatus(staff,updateOrder,order.getStockStatus());
        return updateOrder;
    }

    private Trade buildUpdateTrade(Staff staff,Trade trade) {
        Trade updateTrade = TradeBuilderUtils.builderUpdateTrade(trade,false);
        updateTrade.setSid(trade.getSid());
       // updateTrade.setStockStatus(trade.getStockStatus());
        updateTrade.setInsufficientNum(trade.getInsufficientNum());
        updateTrade.setInsufficientRate(trade.getInsufficientRate());
        TradeExceptUtils.setStockStatus(staff,updateTrade,trade.getStockStatus());
        updateTrade.setIsExcep(trade.getIsExcep());
        return updateTrade;
    }

    private Map<String, Object> buildParams(StockKey stockKey) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("sysItemId", stockKey.getSysItemId());
        params.put("sysSkuId", stockKey.getSysSkuId());
        params.put("warehouseId", stockKey.getWarehouseId());
        return params;
    }

    boolean updateOrder(Staff staff, Order order, Map<Long, StockOrderRecord> recordMap, List<Order> updateOrders, List<String> logs, TradeConfig tradeConfig) {
        if (order.getSuits() != null) {
            boolean f = false;
            for (Order son : order.getSuits()) {
                f |= updateOrder(staff, son, recordMap, updateOrders, logs, tradeConfig);
            }
            if (f) {
                TradeStockUtils.handleSuitOrderStock(staff, order, false, tradeConfig);
                updateOrders.add(buildUpdateOrder(staff,order));
                logs.add(LogHelper.buildLog(staff, String.format("套件order[id=%s,sid=%s]库存重新调整[stockNum:%s->%s, stockStatus:%s->%s]", order.getId(), order.getSid(), order.getOldStockNum(), order.getStockNum(), order.getOldStockStatus(), order.getStockStatus())).toString());
                return true;
            }
        } else {
            StockOrderRecord record = recordMap.get(order.getId());
            if (record != null) {
                order.setOldStockNum(order.getStockNum());
                order.setOldStockStatus(order.getStockStatus());
                order.setStockNum(record.getStockNum().intValue());
                String stockStatus = record.getStockStatus() - 1 == 0 ? Trade.STOCK_STATUS_NORMAL : Trade.STOCK_STATUS_INSUFFICIENT;
               // order.setStockStatus(stockStatus);
                OrderExceptUtils.setStockStatus(staff,order,stockStatus);
                updateOrders.add(buildUpdateOrder(staff,order));
                logs.add(LogHelper.buildLog(staff, String.format("order[id=%s,sid=%s]库存重新调整[stockNum:%s->%s, stockStatus:%s->%s]", order.getId(), order.getSid(), order.getOldStockNum(), order.getStockNum(), order.getOldStockStatus(), order.getStockStatus())).toString());
                return true;
            }
        }
        return false;
    }

    private List<Trade> reallocate(Staff staff, List<Trade> trades, Map<Long, StockOrderRecord> recordMap, List<String> logs) {
        List<Trade> updateTrades = new ArrayList<Trade>();
        List<Order> updateOrders = new ArrayList<Order>();
        List<Trade> mainTrades = new ArrayList<Trade>();
        Map<Long, List<Order>> mergeOrdersMap = new HashMap<Long, List<Order>>();
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                updateOrder(staff, order, recordMap, updateOrders, logs, tradeConfig);
            }
            if (trade.getMergeSid() - trade.getSid() != 0) {
                Object[] oldValues = getOldValues(trade);
                TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
                TradeUtils.setTradeExcep(staff,trade);
                updateTrades.add(buildUpdateTrade(staff,trade));
                logs.add(LogHelper.buildLog(staff, String.format("订单[sid=%s]库存重新调整[isExcep:%s->%s,stockStatus:%s->%s,insufficientNum:%s->%s,insufficientRate:%s->%s]", trade.getSid(), oldValues[0], trade.getIsExcep(), trade.getOldStockStatus(), trade.getStockStatus(), oldValues[1], trade.getInsufficientNum(), oldValues[2], trade.getInsufficientRate())).toString());
            } else {
                mainTrades.add(trade);
            }
            if (trade.getMergeSid() > 0) {
                mergeOrdersMap.computeIfAbsent(trade.getMergeSid(), s -> new ArrayList<Order>()).addAll(orders);
            }
        }
        for (Trade trade : mainTrades) {
            Object[] oldValues = getOldValues(trade);
            TradeUtils.setOrders(trade, mergeOrdersMap.get(trade.getMergeSid()));
            TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
            TradeUtils.setTradeExcep(staff,trade);
            updateTrades.add(buildUpdateTrade(staff,trade));
            logs.add(LogHelper.buildLog(staff, String.format("主单[sid=%s]库存重新调整[isExcep:%s->%s,stockStatus:%s->%s,insufficientNum:%s->%s,insufficientRate:%s->%s]", trade.getSid(), oldValues[0], trade.getIsExcep(), trade.getOldStockStatus(), trade.getStockStatus(), oldValues[1], trade.getInsufficientNum(), oldValues[2], trade.getInsufficientRate())).toString());
        }
        tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
        return updateTrades;
    }

    private Object[] getOldValues(Trade trade) {
        Object[] oldValues = new Object[3];
        oldValues[0] = trade.getIsExcep();
        oldValues[1] = trade.getInsufficientNum();
        oldValues[2] = trade.getInsufficientRate();
        return oldValues;
    }

     public Long  searcherWaitAudit(Staff staff) {
        TradeControllerParams controllerParams = new TradeControllerParams();
        controllerParams.setQueryId(SystemTradeQueryParamsContext.QUERY_WAIT_AUDIT);
        controllerParams.setQueryType(0);
        TradeQueryParams params = TradeQueryParams.copyParams(controllerParams);
        params.setQueryFlag(2);
        params.setNeedOrder(0);
        //params.setIsOutstock(0);
        params.setIsPresell(0);
        params.setStockStatus(Trade.STOCK_STATUS_NORMAL);
        params.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
        Trades trades = tradeSearchService.search(staff, params);
        if (trades != null) {
            return trades.getTotal();
        }
        return 0L;
    }
}

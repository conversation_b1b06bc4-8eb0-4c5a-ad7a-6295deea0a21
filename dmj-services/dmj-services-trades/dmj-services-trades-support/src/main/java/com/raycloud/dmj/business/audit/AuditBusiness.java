package com.raycloud.dmj.business.audit;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.audit.ai.help.filter.AuditAiFilter;
import com.raycloud.dmj.business.audit.help.AuditUtils;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.fx.*;
import com.raycloud.dmj.business.part3.TradeParty3Business;
import com.raycloud.dmj.dms.domain.dto.DmsDistributorInfoDto;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.diamond.*;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.trade.audit.*;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.audit.AuditData;
import com.raycloud.dmj.domain.trades.fx.SimulateImportGxTradeData;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.utils.SortUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.print.api.enums.TradePrintEntranceEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.ec.RepeatCodTradeExcepListener;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.trade.audit.TradeAuditUpdate;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.*;
import com.raycloud.dmj.services.trades.support.TradeConfigService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.utils.wms.DataUtils;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.TradeConstants.IDX_INSUFFICIENT;

/**
 * Created by yangheng on 17/2/27.
 * 审核
 */
@Service
public class AuditBusiness extends SysTradeBusiness {

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    TradeAuditUpdate tradeAuditUpdate;


    @Resource
    ITradePtService tradePtService;

    @Resource
    TradeParty3Business tradeParty3Business;
    @Resource
    SharedCacheBusiness sharedCacheBusiness;
    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource
    ILockService lockService;
    @Resource
    IFinanceAuditConfigService financeAuditConfigService;

    @Resource
    AuditStockBusiness auditStockBusiness;

    @Resource
    protected AuditAfterBusiness auditAfterBusiness;
    @Resource
    AuditFxBusiness auditFxBusiness;

    @Resource
    AuditAutoInsufficientBusiness auditAutoInsufficientBusiness;

    @Resource
    IDmsTradeService dmsTradeService;

    @Resource
    AuditInsufficientValidateBusiness auditInsufficientValidateBusiness;
    @Resource
    RepeatCodTradeExcepListener repeatCodTradeExcepListener;
    @Resource
    AuditJitxBusiness auditJitxBusiness;

    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    AuditSuitSwitchBusiness auditSuitSwitchBusiness;

    @Resource
    SimulateImportGxTradeBusiness simulateImportGxTradeBusiness;

    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    @Resource
    FxBusiness fxBusiness;

    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;

    @Resource
    FeatureService featureService;

    @Resource
    TradeConfigService tradeConfigService;

    @Resource
    ITradeConfigNewService tradeConfigNewService;

    @Resource
    protected IStaffService staffService;

    @Resource
    AuditAiFilter auditAiFilter;

    public static List<String> IGNORE_SOURCE_LIST = Lists.newArrayList(CommonConstants.PLAT_FORM_TYPE_PDD, CommonConstants.PLAT_FORM_TYPE_TIAN_MAO, CommonConstants.PLAT_FORM_TYPE_TAO_BAO);

    public static List<String> ADDRESS_MD5_LIST = Lists.newArrayList(CommonConstants.PLAT_FORM_TYPE_TAO_BAO, CommonConstants.PLAT_FORM_TYPE_TIAN_MAO, CommonConstants.PLAT_FORM_TYPE_1688_C2M, CommonConstants.PLAT_FORM_TYPE_1688, CommonConstants.PLAT_FORM_TYPE_FX);

    /**
     * 调用方加锁
     */
    public AuditData doAudit(Staff staff, AuditData auditData) {
        //过滤订单
        if (CollectionUtils.isEmpty(auditData.trades)) {
            auditData.trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, auditData.originSids);
        }
        boolean openAlibabaAETradeAllowAudit = tradeConfigNewService.get(staff, TradeConfigEnum.ALIBABA_AE_TRADE_SUB_STOCK).isOpen();
        auditData.trades = auditAiFilter.filter(staff, auditData, openAlibabaAETradeAllowAudit);
        if (CollectionUtils.isNotEmpty(auditData.trades)) {
            auditData.needFinanceAuditSids = TradeUtils.toSidList(financeAuditConfigService.tradeCheck(staff, auditData.trades));
            //检查cod订单重复
            List<Long> repeatSid = repeatCodTradeExcep(staff, auditData.tradeConfig, auditData.trades);
            // checkBefore(staff, auditData, auditData.tradeConfig);
            //校验并设置更新值
            check(staff, auditData, auditData.tradeConfig, repeatSid, false);
            buildUpdateList(staff, auditData, auditData.tradeConfig, auditData.stockKeys, false);

            // 清除[重新审核]标签, 此代码要放在trade update链路前, 函数实现中有对tag_ids做修改
            tradeSysLabelBusiness.removeTags(staff, auditData.updateTrades, auditData.opEnum, Collections.singletonList(SystemTags.TAG_AUDIT_UNDO));

            //判断是否有货到付款订单，有重复的需要标记异常
            tradePtService.saveByTrades(staff, auditData.updateTrades, TradePrintEntranceEnum.TRADE_PRINT_ENTRANCE_2001);
            tradeAuditUpdate.update(staff, auditData.updateTrades, auditData.updateOrders);

            //配置：订单审核（自动/智能/手动/缺货审核）时，先执行套件转单品, 排除BTAS订单
            auditSuitSwitchBusiness.suitSwitch(staff, auditData.tradeConfig, AuditUtils.filterBtasOrder(auditData.trades, auditData.updateOrders));
            //三方仓
            tradeParty3Business.filterAudit(staff, auditData.finishAuditTrades);
            //审核后
            auditAfterBusiness.after(staff, auditData);
        }
        return auditData;
    }


    /**
     * 前置审核通用（目前是强制审核用）
     */
    public AuditData auditForce(AuditContext auditContext, List<Trade> trades) {
        if (StringUtils.isNotEmpty((String) auditContext.getTradeConfig().get(TradeExtendConfigsEnum.AI_AUDIT_ORDER.getKey()))) {
            SortUtils.sort(trades, "payTime", true);
        }
        AuditData data = AuditUtils.init(auditContext.getOpEnum(), TradeUtils.toSids(trades), auditContext.getTradeConfig(), false, false, auditContext.getClientIp(), 0, 1);
        data.trades = trades;
        data.needFinanceAuditSids = TradeUtils.toSidList(financeAuditConfigService.tradeCheck(auditContext.getStaff(), trades));
        //检查cod订单重复
        List<Long> repeatSid = repeatCodTradeExcep(auditContext.getStaff(), auditContext.getTradeConfig(), data.trades);
        //校验并设置更新值
        check(auditContext.getStaff(), data, auditContext.getTradeConfig(), repeatSid, true);
        buildUpdateList(auditContext.getStaff(), data, auditContext.getTradeConfig(), data.stockKeys, true);

        // 清除[重新审核]标签, 此代码要放在trade update链路前, 函数实现中有对tag_ids做修改
        tradeSysLabelBusiness.removeTags(auditContext.getStaff(), data.updateTrades, data.opEnum, Collections.singletonList(SystemTags.TAG_AUDIT_UNDO));

        return data;
    }

    public <T extends Trade> List<Long> repeatCodTradeExcep(Staff staff, TradeConfig tradeConfig, List<T> trades) {
        if ("1".equals(tradeConfig.getOpenCheckRepeatCod()) && CollectionUtils.isNotEmpty(trades)) {
            return getRepeatCodTradeExcep(staff, trades);
        }
        return Lists.newArrayList();
    }

    public <T extends Trade> List<Long> getRepeatCodTradeExcep(Staff staff, List<T> trades) {
        List<Trade> codTrades = trades.stream().filter(trade -> "cod".equals(trade.getType()) || "jd-1".equals(trade.getType())).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(codTrades) ? repeatCodTradeExcepListener.handleRepeat(staff, TradeUtils.toSids(codTrades)) : new ArrayList<>();
    }

    private void check(Staff staff, AuditData data, TradeConfig tradeConfig, List<Long> repeatSid, Boolean isForceAudit) {
        auditFxBusiness.fillFxConfig(staff, data.trades, data);
        auditFxBusiness.fillFxOrderPriceInfo(staff, data.trades, data);
        boolean hasFeature = featureService.checkHasFeature(staff.getCompanyId(), Feature.OPEN_POST_FINANCE_AUDIT_STOCK);
        boolean openAlibabaAETradeAllowAudit = tradeConfigNewService.get(staff, TradeConfigEnum.ALIBABA_AE_TRADE_SUB_STOCK).isOpen();
        boolean ifExistsAlibabaFxUser = staff.getUsers().stream().anyMatch(u->com.raycloud.dmj.domain.utils.UserUtils.isAlibabaFxRole(u) && Objects.equals(u.getActive(),1) && Objects.equals(u.getInvalidSession(),0));
        for (Trade trade : data.trades) {
            //验证
            if (!check(staff, data, trade, tradeConfig, repeatSid, isForceAudit, openAlibabaAETradeAllowAudit)) {
                continue;
            }
            if(TradeUtils.isAlibabaFxRoleTrade(trade) && !ifExistsAlibabaFxUser){
                TradeResult result = new TradeResult();
                result.setSid(trade.getSid());
                result.setErrorMsg("1688分销小站店铺过期，请重新授权");
                data.results.put(trade.getSid(), result);
                if (TradeUtils.isMerge(trade)) {
                    result.setMergeSid(trade.getMergeSid());
                    data.errorMergeSids.put(trade.getMergeSid(), 1);
                }
                continue;
            }
            //合单稍后处理
            if (TradeUtils.isMerge(trade)) {
                data.mergeTradesMap.computeIfAbsent(trade.getMergeSid(), k -> new ArrayList<>()).add(trade);
                continue;
            }
            if (staff.openAuditActiveStockRecord() && hasFeature) {
                data.finishAuditTrades.add(trade);
            } else {
                //开启财审的条件下(不包含出库单)审核后符合条件进入待财审状态
                (data.needFinanceAuditSids.contains(trade.getSid()) ? data.financeAuditTrades : data.finishAuditTrades).add(trade);
            }
            //分销订单校验通过,要通知供销,这里加进来
            if (TradeUtils.isFxOrMixTrade(trade)) {
                data.addSourceTrades(trade);
            }
        }
        //合单处理
        if (!data.mergeTradesMap.isEmpty()) {
            for (Map.Entry<Long, List<Trade>> entry : data.mergeTradesMap.entrySet()) {
                //合单下有一笔订单校验不通过整个订单就不通过
                if (data.errorMergeSids.containsKey(entry.getKey())) {
                    continue;
                }
                String sysOuterId = null;
                Trade mainTrade = null;
                List<Trade> mergeTrades = entry.getValue();
                Map<Long, User> userIdMap = staff.getUserIdMap();
                Set<String> addresses = new HashSet<>();
                for (Trade trade : mergeTrades) {
                    if (trade.getSid() - trade.getMergeSid() == 0) {
                        mainTrade = trade;
                    }
                    if (sysOuterId == null) {
                        sysOuterId = trade.getSysOuterId();
                    }
                    //pdd  jd 的不校验
                    //暂时不校验淘宝和天猫订单
                    //fxg加密后不校验
                    //微信视频号虚拟号不校验
                    //暂时不校验淘宝和天猫订单
                    //1688c2m脱敏之后不校验
                    if (TradePlatformUtils.platformsMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_FXG, CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU, CommonConstants.PLAT_FORM_TYPE_XHS)) {
                        // source+subSource+user判断
                        continue;
                    }
                    // 直接判断source
                    if (TradePlatformUtils.platformSourceMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_JD, CommonConstants.PLAT_FORM_TYPE_PDD, CommonConstants.PLAT_FORM_TYPE_WXSPH, CommonConstants.PLAT_FORM_TYPE_JD_VC, CommonConstants.PLAT_FORM_TYPE_QIMEN)) {
                        continue;
                    }
                    // source +md5 判断
                    if (TradePlatformUtils.platformMatchMd5NotNull(staff, trade, ADDRESS_MD5_LIST)) {
                        continue;
                    }
                    if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
                        User user = userIdMap.get(trade.getUserId());
                        if (Objects.nonNull(user) && IGNORE_SOURCE_LIST.contains(user.getSource())) {
                            continue;
                        }
                        if (Objects.nonNull(user) && TradePlatformUtils.platformMatchMd5NotNull(staff, user.getSource(), StringUtils.isNotBlank(trade.getAddressMd5()), ADDRESS_MD5_LIST)) {
                            continue;
                        }
                    }
                    String addressMd5 = AddressUtils.buildAddMd5(staff, trade);
                    addresses.add(addressMd5);
                }
                if (addresses.size() > 1) {//合单下有地址不一样的订单
                    TradeResult result = new TradeResult();
                    result.setSid(entry.getKey());
                    result.setMergeSid(entry.getKey());
                    result.setErrorMsg("合单下存在地址不一样的订单");
                    data.results.put(entry.getKey(), result);
                    data.errorMergeSids.put(entry.getKey(), 1);
                    Logs.error(LogHelper.buildLog(staff, String.format("合单[sid=%s]下有地址不一样的订单,忽略审核", entry.getKey())));
                    continue;
                }
                // 分销合单校验
                if (mainTrade != null && TradeUtils.isFxTrade(mainTrade) && !Objects.equals(0L, mainTrade.getDestId())) {
                    Long destId = mainTrade.getDestId();
                    // 分销单商品有多个供销商不允许审核
                    if (TradeUtils.getOrders4Trade(mergeTrades).stream().anyMatch(o -> !Objects.equals(destId, o.getDestId()))) {
                        TradeResult result = new TradeResult();
                        result.setSid(entry.getKey());
                        result.setMergeSid(entry.getKey());
                        data.results.put(entry.getKey(), result);
                        data.errorMergeSids.put(entry.getKey(), 1);
                        Set<Long> collect = TradeUtils.getOrders4Trade(mainTrade).stream().map(Order::getDestId).collect(Collectors.toSet());
                        FxLogBuilder builder = new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).appendTrade(mainTrade).append("destId", mainTrade.getDestId()).append("存在与订单供销商不一致的商品 ")
                                .append("orderDestId", collect);
                        logger.info(builder.toString());
                        result.setErrorMsg(String.format("一个分销订单只有一个供销商才可以推送,订单[%s]", mainTrade.getSid()));
                        continue;
                    }
                }
                //如果主单没有设置sysOuterId,取第一个含有sysOuterId的隐藏订单的sysOuterId
                if (mainTrade != null && mainTrade.getSysOuterId() == null) {
                    mainTrade.setSysOuterId(sysOuterId);
                }

                if (staff.openAuditActiveStockRecord() && hasFeature) {
                    data.finishAuditTrades.addAll(mergeTrades);
                } else {
                    //合单中只要有一个系统订单，在开启财审的条件下审核后变为待财审
                    (AuditUtils.needFinanceAudit(data.needFinanceAuditSids, mergeTrades) ? data.financeAuditTrades : data.finishAuditTrades).addAll(mergeTrades);
                }
                //分销订单校验通过,要通知供销,这里加进来
                for (Trade mergeTrade : mergeTrades) {
                    if (TradeUtils.isFxOrMixTrade(mergeTrade)) {
                        data.addSourceTrades(mergeTrade);
                    }
                }
            }
        }

        //校验余额
        if (CollectionUtils.isNotEmpty(data.sourceTrades)) {
            checkFxBalance(staff, data);
            if (TransactionSynchronizationManager.isSynchronizationActive() && MapUtils.isNotEmpty(data.auditFxData.sid2DmsStockCheckMap) && ConfigHolder.FX_GLOBAL_CONFIG.open(FxGlobalConfig.FX_AUDIT_LOCK_GX_ITEM_STOCK, staff.getCompanyId())) {
                TransactionSynchronizationManager.registerSynchronization(new FxBusiness.PreLockGxTradeStockTransactionSynchronization(staff, data.auditFxData, sharedCacheBusiness));
            }
        }

        if(CollectionUtils.isNotEmpty(data.trades)){
            List<Trade> aliGxTrades = data.trades.stream().filter(TradeUtils::isAlibabaFxRoleTrade).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(aliGxTrades) && !ifExistsAlibabaFxUser){
                    new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).append("1688分销小站店铺过期，请重新授权").appendTrade(aliGxTrades).printWarn(logger);
            }
        }
    }

    /**
     * 校验分销余额
     *
     * @param staff
     * @param data
     */
    private void checkFxBalance(Staff staff, AuditData data) {
        Map<Long, List<Trade>> destMap = data.sourceTrades.stream().collect(Collectors.groupingBy(Trade::getDestId));
        destMap.forEach((destId, trades) -> {
            Staff gxStaff = staffService.queryFullByCompanyId(destId);
            Map<String, TradeConfigNew> configMap = tradeConfigNewService.getMap(gxStaff, CashFlowUtils.REISSUE_OR_CHANGE_ITEM_FLOW_ENUMS);
            //售后补发单不计算分销流水 跳过校验
            trades = trades.stream().filter(v -> !(CashFlowUtils.isReissueOrChangeItemPassCalFlow(gxStaff, v, configMap))).collect(Collectors.toList());
            List<DmsDistributorInfoDto> dmsDistributorInfoDtos = dmsTradeService.queryDmsDistributorInfoList(destId, Collections.singletonList(staff.getCompanyId()));
            if (CollectionUtils.isNotEmpty(dmsDistributorInfoDtos)) {
                DmsDistributorInfoDto dmsDistributorInfoDto = dmsDistributorInfoDtos.get(0);
                //余额
                BigDecimal balance = BigDecimal.valueOf(dmsDistributorInfoDto.getTotalBalance() == null ? 0 : dmsDistributorInfoDto.getTotalBalance());
                BigDecimal sum = BigDecimal.ZERO;
                boolean ifFirst = true;
                if (!tradeLocalConfig.isTradeFxAuditNotCalcGxPostFee(staff.getCompanyId())) {
                    simulateImportGxTradeBusiness.handle(SimulateImportGxTradeData.builder()
                            .staff(staff)
                            .fxTrades(trades)
                            .ifMatchFxPrice(true)
                            .build());
                }
                for (Trade trade : trades) {
                    BigDecimal saleFee = fxBusiness.getFxTotalSaleFee(staff, data.tradeConfig, trade, false, true);
                    sum = sum.add(saleFee);
                    Map<String, Double> cashFlowMap = data.auditFxData != null ? data.auditFxData.cashFlowMap.get(destId) : new HashMap<>();
                    String tid = String.valueOf(trade.getSid());
                    Double amount = cashFlowMap != null ? cashFlowMap.getOrDefault(tid, 0D) : 0D;
                    if (amount != 0D) {
                        cashFlowMap.put(tid, 0D);
                    }
                    balance = balance.add(new BigDecimal(amount.toString()).multiply(new BigDecimal(-1)));
                    if (MathUtils.scaleUp(balance).compareTo(MathUtils.scaleUp(sum)) < 0) {
                        if (ifFirst) {
                            ifFirst = false;
                            logger.warn(LogHelper.buildLog(staff, String.format("分销账户余额不足,destId=%s,balance=%s,sum=%s", destId, balance, sum)));
                        }
                        //余额不足
                        data.sourceTrades.removeIf(trade1 -> needRemove(trade1, trade));
                        data.needFinanceAuditSids.removeIf(sid -> Objects.equals(sid, trade.getSid()) || (TradeUtils.isMerge(trade) && Objects.equals(sid, trade.getMergeSid())));
                        data.financeAuditTrades.removeIf(trade1 -> needRemove(trade1, trade));
                        data.finishAuditTrades.removeIf(trade1 -> needRemove(trade1, trade));
                        data.auditFxData.sid2DmsStockCheckMap.remove(trade.getSid());
                        TradeResult tradeResult1 = data.results.get(trade.getSid());
                        if (tradeResult1 != null) {
                            tradeResult1.setErrorMsg(tradeResult1.getErrorMsg() + ",分销账户余额不足");
                        } else {
                            tradeResult1 = new TradeResult();
                            tradeResult1.setSid(trade.getSid());
                            if (TradeUtils.isMerge(trade)) {
                                tradeResult1.setMergeSid(trade.getMergeSid());
                            }
                            tradeResult1.setErrorMsg("分销账户余额不足");
                            data.results.put(trade.getSid(), tradeResult1);
                        }
                        TradeResult tradeResult = new TradeResult();
                        tradeResult.setSid(-1L);
                        tradeResult.setDestId(trade.getDestId());
                        String diff = new BigDecimalWrapper(sum).subtract(balance).getString();
                        tradeResult.setErrorMsg("分销账户余额不足,(分销订单总分销价" + MathUtils.toString(sum) + "元,账户余额" + MathUtils.toString(balance) + "元，差额:" + diff + "元)");
                        data.results.put(-1L, tradeResult);
                    }
                }

            }
        });
    }

    /**
     * 审核的时候把合单当成多个订单处理，但只能看出一体审核，要么一起通过，要么一起审核不通过
     * 非合单对比sid合单对比 mergeSid
     *
     * @param trade1
     * @param trade
     * @return
     */
    private boolean needRemove(Trade trade1, Trade trade) {
        return Objects.equals(trade1.getSid(), trade.getSid()) ||
                (TradeUtils.isMerge(trade) && Objects.equals(trade1.getMergeSid(), trade.getMergeSid()));
    }

    /**
     * 校验一笔订单是否符合审核条件
     */
    private boolean check(Staff staff, AuditData data, Trade trade, TradeConfig tradeConfig, List<Long> repeatSid, Boolean isForceAudit, boolean openAlibabaAETradeAllowAudit) {
        TradeResult result = new TradeResult();
        result.setSid(trade.getSid());
        User user = staff.getUserIdMap().get(trade.getUserId());
        if (!(trade.isOutstock() || TradeUtils.isGxOrMixTrade(trade))) {//非出库单校验是否有店铺权限以及店铺是否已停用
            if (user == null) {
                result.setErrorMsg("没有该订单所属店铺的权限");
            } else if (user.getActive() != null && user.getActive() - CommonConstants.JUDGE_NO == 0) {
                result.setErrorMsg("订单所属店铺已停用");
            }
        }
        //BTAS订单，保单价格与实付金额的判断
        if (TradeTypeUtils.isFxgBtasTrade(trade) && Integer.valueOf(2).equals(TradeExtUtils.getInsuredPriceType(trade.getTradeExt()))) {
            Double insuranceCost = TradeExtUtils.getInsuranceCost(trade.getTradeExt());
            if (insuranceCost == null) {
                result.setErrorMsg("未填写订单保价金额");
            } else {
                if (Double.parseDouble(trade.getPayment()) < insuranceCost) {
                    result.setErrorMsg("订单保价金额不得大于订单金额");
                }
            }
        }
        if (TradeUtils.isAlibabaAeTrade(trade) && !openAlibabaAETradeAllowAudit) {
            result.setErrorMsg("AE自营订单只能通过1688商家后台发货，请前往1688后台发货，并注意贴货品标签和包装的箱唛");
        }

        if (TradeUtils.isZhenkunhangLogisticsTrade(trade)) {
            result.setErrorMsg("直发/代管代发的订单，需要在震坤行后台操作。");
        }

//        if (TradeUtils.isTbXsdTrade(trade)&&!TradeUtils.isTbXsdB2cTrade(trade)) {
//            int tbXsdMode = tradeConfigService.getTbXsdUploadSet(staff);
//            if (tbXsdMode == 0 || tbXsdMode == 1) {
//                result.setErrorMsg("淘宝小时达的订单，1/2模式下不能审核。");
//            }
//        }

        if (TradeUtils.isKttHelpSaleTrade(trade)) {
            result.setErrorMsg("快团团帮卖订单禁止发货。");
        }

        if (result.getErrorMsg() == null) {//非出库单与出库单都要进行的校验
            if (!TradeStatusUtils.isWaitAudit(trade.getSysStatus())) {
                result.setErrorMsg("订单【" + trade.getSid() + "】状态非待审核");
            } else if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.HALT)) {
                result.setErrorMsg("订单已挂起");
            } else if (trade.getIsCancel() != null && trade.getIsCancel() - 1 == 0) {
                result.setErrorMsg("订单已作废");
            } else if (staff.getConf().getOpenWave() != null && staff.getConf().getOpenWave() == 1
                    && !DataUtils.checkLongNotEmpty(trade.getTemplateId()) && !trade.isOutstock() && isWarehouseSys(staff, trade.getWarehouseId())
                    && !(trade.getType().equals("26") && trade.getSource().equals(CommonConstants.PLAT_FORM_TYPE_POISON))
                    && !(CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource()) || CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource()))
                    && !(TradeUtils.isFxOrMixTrade(trade))
                    && !PlatformUtils.isTradeO2o(trade)
            ) {
                result.setErrorMsg("开启了波次打印，订单审核快递模板不能为空！");
            } else if (CollectionUtils.isNotEmpty(repeatSid) && repeatSid.contains(trade.getSid())) {
                result.setErrorMsg("订单【" + trade.getSid() + "】是货到付款重复订单！");
            } else if (TradeUtils.isFxgBicTrade(trade, tradeConfig) && StringUtils.isBlank(trade.getOutSid())) {
                result.setErrorMsg("BIC订单必须存在订单码");
            } else if (TradeUtils.isPlatformFxSource(trade)) {
                result.setErrorMsg("订单【" + trade.getSid() + "】为天猫分销订单");
            } else if (TradeUtils.isFreshHalfDayTrade(trade)) {
                result.setErrorMsg("订单【" + trade.getSid() + "】为半日生鲜达订单");
            } else if (TradeUtils.isCainiaoWarehouseTrade(trade)) {
                result.setErrorMsg("订单【" + trade.getSid() + "】为菜鸟仓订单");
            } else {
                //强制审核这里不校验，会进行缺货发货
                if (!isForceAudit) {
                    TradeUtils.setTradeExcep(staff, trade);
                    //开启后置
                    boolean needIncluedInsufficientStock = AuditUtils.needIncluedInsufficientStock(staff, tradeConfig);
                    if (staff.openAuditActiveStockRecord() || data.insufficientAnalyze || needIncluedInsufficientStock) {
                        // 校验是否存在除缺货异常以外的其他异常
                        Set<Integer> tradeSysExceptIds = TradeUtils.parseExcept(staff, trade);
                        tradeSysExceptIds.remove(IDX_INSUFFICIENT);
                        if (CollectionUtils.isNotEmpty(tradeSysExceptIds)) {
                            result.setErrorMsg("订单是异常订单");
                        } else if (trade.getIsExcep() == 1) {
                            boolean marked = AuditUtils.markOldStockAndSetNewStock(staff, trade, Trade.STOCK_STATUS_NORMAL, true);
                            if (trade.getIsExcep() == 1) {
                                result.setErrorMsg("订单是异常订单");
                            }
                            if (marked && needIncluedInsufficientStock) {
                                AuditUtils.recoveryStockStatus(staff, trade);
                            }
                        }
                    } else {
                        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.INSUFFICIENT)) {
                            result.setErrorMsg("订单库存状态异常[" + StockStatusEnum.getMsgByStockStatus(trade.getStockStatus()) + "]");
                        } else if (trade.getIsExcep() != null && trade.getIsExcep() - 1 == 0) {
                            logger.warn(LogHelper.buildLog(staff, String.format("订单%s,有%s异常", trade.getSid(), TradeExceptionUtils.analyze(staff, trade))));
                            result.setErrorMsg("订单是异常订单");
                        }
                    }
                }
                if (TradeUtils.isFxTrade(trade) && !Objects.equals(0L, trade.getDestId())) {
                    // 分销单商品有多个供销商不允许审核
                    if (TradeUtils.getOrders4Trade(trade).stream().anyMatch(o -> !Objects.equals(trade.getDestId(), o.getDestId()))) {

                        Set<Long> collect = TradeUtils.getOrders4Trade(trade).stream().map(Order::getDestId).collect(Collectors.toSet());
                        FxLogBuilder builder = new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).appendTrade(trade).append("destId", trade.getDestId()).append("存在与订单供销商不一致的商品 ")
                                .append("orderDestId", collect);
                        logger.info(builder.toString());
                        result.setErrorMsg(String.format("一个分销订单只有一个供销商才可以推送,订单[%s]", trade.getSid()));
                    }
                }
            }
            Map<String, String> errorMap = new HashMap<>();
            auditInsufficientValidateBusiness.validateGoodsSectionSku(staff, Collections.singletonList(trade), errorMap);
            if (!errorMap.isEmpty()) {
                result.setErrorMsg(JSON.toJSONString(errorMap));
            }
            if (StringUtils.isBlank(result.getErrorMsg()) && auditJitxBusiness.jitxAuditJudge(staff, trade)) {
                result.setErrorMsg("订单【" + trade.getSid() + "】寻仓失败");
            }
        }

        //校验分销订单
        if (TradeUtils.isFxOrMixTrade(trade)) {
            auditFxBusiness.checkSourceTrades(staff, data.auditFxData, Collections.singletonList(trade), result);
        }
        if (result.getErrorMsg() != null) {//如果检验不通过，记下错误
            data.results.put(trade.getSid(), result);
            AuditUtils.updateHandMessageMemo(tradeConfig, trade, result);
            if (TradeUtils.isMerge(trade)) {
                result.setMergeSid(trade.getMergeSid());
                data.errorMergeSids.put(trade.getMergeSid(), 1);
            }
            return false;
        }
        return true;
    }

    private void buildUpdateList(Staff staff, AuditData data, TradeConfig tradeConfig, Set<String> stockKeys, Boolean isForceAudit) {
        //变为待财审
        for (Trade trade : data.financeAuditTrades) {
            // 变为财审，强制取消缺货异常
            buildUpdateList(staff, trade, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT, data, tradeConfig, true, 0);
        }
        // 取消了缺货异常
        if (CollectionUtils.isNotEmpty(data.cancelInsufficientTrades)) {
            tradeSysLabelBusiness.matchSystemLabels(staff, data.cancelInsufficientTrades, Lists.newArrayList(SystemTags.TAG_CANCEL_INSUFFICIENT, SystemTags.TAG_FAST_MOVING_ORDER), true, false, true);
        }
        //变为已审核
        if (data.finishAuditTrades == null || data.finishAuditTrades.size() == 0) {
            return;
        }
        //库存分配
        if (AuditUtils.allowAuditIncluedInsufficientStock(staff, tradeConfig)) {
            List<Trade> markStockInsufficientTrades = AuditUtils.markOldStockAndSetNewStock(staff, data.finishAuditTrades, Trade.STOCK_STATUS_NORMAL);
            auditStockBusiness.audit(staff, data.finishAuditTrades, data.preLock, tradeConfig, stockKeys);
            auditAutoInsufficientBusiness.markInsufficientCanceled(staff, tradeConfig, AuditUtils.filterStockChange2Normal(markStockInsufficientTrades, data.finishAuditTrades));
        } else {
            auditStockBusiness.audit(staff, data.finishAuditTrades, data.preLock, tradeConfig, stockKeys);
        }
        auditAutoInsufficientBusiness.markInsufficientCanceledAndAudit(staff, tradeConfig, data.finishAuditTrades, stockKeys, isForceAudit, data);

        Map<Long, List<Trade>> mergeSidTradesMap = new HashMap<>();
        Map<Long, Trade> mergeSidMainTrade = new HashMap<>();
        boolean hasFeature = featureService.checkHasFeature(staff.getCompanyId(), Feature.OPEN_POST_FINANCE_AUDIT_STOCK);
        for (Trade trade : data.finishAuditTrades) {
            if (TradeUtils.isMerge(trade)) {
                mergeSidTradesMap.computeIfAbsent(trade.getMergeSid(), k -> new ArrayList<>()).add(trade);
                if (trade.getSid().equals(trade.getMergeSid())) {
                    mergeSidMainTrade.put(trade.getMergeSid(), trade);
                }
            } else {
                if (staff.openAuditActiveStockRecord() && hasFeature && data.needFinanceAuditSids.contains(trade.getSid())) {
                    buildUpdateList(staff, trade, trade.getIsExcep() == 0 ? Trade.SYS_STATUS_WAIT_FINANCE_AUDIT : trade.getSysStatus(), data, tradeConfig, isForceAudit, trade.getIsExcep());
                } else {
                    buildUpdateList(staff, trade, trade.getIsExcep() == 0 ? Trade.SYS_STATUS_FINISHED_AUDIT : trade.getSysStatus(), data, tradeConfig, isForceAudit, trade.getIsExcep());

                }
            }
        }

        //合单
        if (mergeSidMainTrade.size() > 0) {
            //如果主单异常，则隐藏订单都不通过
            for (Map.Entry<Long, List<Trade>> entry : mergeSidTradesMap.entrySet()) {
                Trade mainTrade = mergeSidMainTrade.get(entry.getKey());
                if (mainTrade == null) {
                    Logs.warn(LogHelper.buildLog(staff, String.format("审核订单找不到主单,mergeSid=%s,sids=%s", entry.getKey(), TradeUtils.toSidList(entry.getValue()))));
                    continue;
                }
                List<Trade> mergeTrades = entry.getValue();
                if (staff.openAuditActiveStockRecord() && hasFeature && data.needFinanceAuditSids.contains(mainTrade.getSid())) {
                    buildUpdateList(staff, mainTrade, mainTrade.getIsExcep() == 0 ? Trade.SYS_STATUS_WAIT_FINANCE_AUDIT : mainTrade.getSysStatus(), data, tradeConfig, isForceAudit, mainTrade.getIsExcep());
                } else {
                    //先处理主单
                    buildUpdateList(staff, mainTrade, mainTrade.getIsExcep() == 0 ? Trade.SYS_STATUS_FINISHED_AUDIT : mainTrade.getSysStatus(), data, tradeConfig, isForceAudit, mainTrade.getIsExcep());
                }
                for (Trade trade : mergeTrades) {
                    //排除主单和已发货的订单
                    if (!TradeStatusUtils.isAfterSendGoods(trade.getSysStatus()) && trade != mainTrade) {
                        if (staff.openAuditActiveStockRecord() && hasFeature && data.needFinanceAuditSids.contains(mainTrade.getSid())) {
                            buildUpdateList(staff, trade, mainTrade.getIsExcep() == 0 ? Trade.SYS_STATUS_WAIT_FINANCE_AUDIT : trade.getSysStatus(), data, tradeConfig, isForceAudit, mainTrade.getIsExcep());
                        } else {
                            buildUpdateList(staff, trade, mainTrade.getIsExcep() == 0 ? Trade.SYS_STATUS_FINISHED_AUDIT : trade.getSysStatus(), data, tradeConfig, isForceAudit, mainTrade.getIsExcep());
                        }
                    }
                }
            }
        }
    }

    private void buildUpdateList(Staff staff, Trade trade, String sysStatus, AuditData data, TradeConfig tradeConfig, Boolean isForceAudit, int isExcep) {
        trade.setSysOuterId(TradeUtils.calculateOuterId(staff, trade));
        trade.setSysStatus(sysStatus);

        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        boolean containsInsufficient = orders.stream().anyMatch(order -> OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.INSUFFICIENT));
        for (Order order : orders) {
            if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                continue;
            }

            order.setSysStatus(sysStatus);
            if (isForceAudit && Objects.equals(sysStatus, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT)
                    && OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.INSUFFICIENT)) {
                order.setInsufficientCanceled(1);
                OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.INSUFFICIENT, 0L);
            }
            if (order.getSuits() != null) {
                for (Order son : order.getSuits()) {
                    son.setSysStatus(sysStatus);
                    if (isForceAudit && Objects.equals(sysStatus, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT)
                            && (OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.INSUFFICIENT))) {
                        son.setInsufficientCanceled(1);
                        OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.INSUFFICIENT, 0L);
                    }
                }
            }
        }
        if (isForceAudit && Objects.equals(sysStatus, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT)
                && (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.INSUFFICIENT) || containsInsufficient)) {
            TradeExceptUtils.setStockStatus(staff, trade, Trade.STOCK_STATUS_NORMAL);
            data.cancelInsufficientTrades.add(trade);
            TradeExceptUtils.clearTradePointExcept(staff, trade, ExceptEnum.INSUFFICIENT, true);
        }
        // order更新必须放在trade之前
        for (Order order : orders) {
            AuditUtils.createUpdateOrder(staff, data.updateOrders, order);
        }
        Trade updateTrade = AuditUtils.createUpdateTrade(staff, data.updateTrades, trade);
        AuditUtils.updateHandMessageMemo(tradeConfig, trade, updateTrade);
        putResult(staff, data, trade, tradeConfig, isExcep == 0);

        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.INSUFFICIENT)) {
            tradeTraceService.addTradeTrace(staff, TradeTraceUtils.createTradeTrace(staff, trade.getSid(), trade.getTaobaoId(), "添加标签", "标记库存不足异常"));
        }
    }

    private void putResult(Staff staff, AuditData data, Trade trade, TradeConfig tradeConfig, boolean success) {
        TradeResult result = new TradeResult();
        result.setSuccess(success);
        if (!success && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.INSUFFICIENT)) {
            result.setErrorMsg("订单库存状态异常[" + StockStatusEnum.getMsgByStockStatus(trade.getStockStatus()) + "]");
        }
        result.setSid(trade.getSid());
        result.setSysStatus(TradeStatusUtils.convertSysStatusToView(trade, tradeConfig));
        if (TradeUtils.isMerge(trade)) {
            result.setMergeSid(trade.getMergeSid());
        }
        if (StringUtils.isEmpty(result.getErrorMsg()) &&
                (Trade.SYS_STATUS_WAIT_FINANCE_AUDIT.equals(trade.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus()))) {
            result.setSuccess(true);
        }
        data.results.put(trade.getSid(), result);
    }
}

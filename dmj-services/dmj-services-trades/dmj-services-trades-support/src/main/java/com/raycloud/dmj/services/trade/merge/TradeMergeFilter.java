package com.raycloud.dmj.services.trade.merge;

import com.alibaba.fastjson.*;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.PlatformSourceConstants;
import com.raycloud.dmj.business.trade.CommonTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.FxgTradeDecryptBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.diamond.merge.TradeMergeLimitConfig;
import com.raycloud.dmj.domain.enums.TradeMergeEnum;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import com.raycloud.dmj.domain.trade.common.FilterResult;
import com.raycloud.dmj.domain.trade.config.TradeConfigContext;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.merge.*;
import com.raycloud.dmj.domain.trade.split.*;
import com.raycloud.dmj.domain.trade.type.TradeTypeNewEnum;
import com.raycloud.dmj.domain.trade.type.TypeUtils;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeExt;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.ptException.PopUpRemainderException;
import com.raycloud.dmj.services.trade.utils.TradeFilterLogUtils;
import com.raycloud.dmj.services.trades.TradeException;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName TradeMergeFilter
 * @Description 合单过滤
 * <AUTHOR>
 * @Date 2023/9/21
 * @Version 1.0
 */
@Service
public class TradeMergeFilter {

    @Resource
    FxgTradeDecryptBusiness fxgTradeDecryptBusiness;

    @Resource
    CommonTradeDecryptBusiness commonTradeDecryptBusiness;

    /**
     * 合单过滤
     */
    public void mergeFilter(Staff staff, TradeMergeContext mergeContext, List<Trade> trades) {
        Map<String, Map<Long, String>> errorSidMsgMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(trades)) {
            // 获取当前批次 合单数据中有多个相同的tid数据, 下面合单数量过滤会用到
            Set<String> duplicateTids = trades.stream().collect(Collectors.groupingBy(Trade::getTid))
                    .entrySet().stream().filter(e -> e.getValue().size() > 1).map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
            trades.removeIf(trade -> trade.getEnableStatus() == 1 && mergeFilter(staff, mergeContext, trade, duplicateTids, errorSidMsgMap));
        }
        //记录日志
        TradeFilterLogUtils.printLog(staff, errorSidMsgMap, mergeContext.isSpider(), mergeContext.isReturnErrorLog() ? mergeContext.getErrorMsg() : null);
    }

    private boolean mergeFilter(Staff staff, TradeMergeContext mergeContext, Trade trade, Set<String> duplicateTids, Map<String, Map<Long, String>> errorSidMsgMap) {
        boolean filter = filterSysExcept(staff, mergeContext, trade, errorSidMsgMap);//系统异常过滤
        if (!filter && mergeContext.getTradeMergeConf() != null) {
            filter = filterMergeType(staff, mergeContext, trade, errorSidMsgMap)
                    || filterSplitTrade(staff, mergeContext, trade, errorSidMsgMap)
                    || filterFakeTrade(staff, mergeContext, trade, errorSidMsgMap)
                    || filterSplitType(staff, mergeContext, trade, errorSidMsgMap)
                    || filterNetWeight(mergeContext, trade, errorSidMsgMap) //重量过滤
                    || filterItemNum(mergeContext, trade, errorSidMsgMap)//商品数量过滤
                    || filterSysStatus(mergeContext, mergeContext.getConfigContext(), trade, errorSidMsgMap) //系统状态过滤
                    || filterExcept(staff, mergeContext, trade, errorSidMsgMap)//异常过滤
                    || filterPreUpload(mergeContext, trade, errorSidMsgMap) //预发货订单过滤
                    || filterOuterId(mergeContext, trade, errorSidMsgMap)//商品设置过滤
                    || filterAllowSingleSku(staff, mergeContext, trade, errorSidMsgMap)//只允许单SKU订单合单设置过滤
                    || filterSysPreSellForUnlockedInventory(mergeContext, trade, errorSidMsgMap)// 合单规则开启允许系统预售合并普通单, 过滤没有锁定库存的系统预售单
                    || filterLabel(mergeContext, trade, errorSidMsgMap);// 标签过滤
        }
        if (!filter) {
            filter = filterSys(mergeContext, trade, errorSidMsgMap)//手工单过滤
                    || filterDangkou(mergeContext, trade, errorSidMsgMap)//档口订单过滤
                    || filterMergeMultil(staff, mergeContext, trade, errorSidMsgMap) // 跨店合单过滤
                    || filterKuaishou(trade, errorSidMsgMap) //快手过滤
                    || filterFxg(staff, mergeContext, trade, errorSidMsgMap) //抖音过滤
                    || filterFx(trade, errorSidMsgMap) //分销过滤
                    || filterGx(trade, errorSidMsgMap) //供销过滤
                    || filterPdd(trade, errorSidMsgMap)//拼多多过滤
                    || filterQimen(trade, errorSidMsgMap)//奇门过滤
                    || filterVip(staff, trade, errorSidMsgMap)//唯品会订单过滤
                    || filterJd(trade, errorSidMsgMap)//京东直发过滤
                    || filterJps(trade, errorSidMsgMap)//京品试订单过滤
                    || filterAlibaba(trade, errorSidMsgMap)//阿里巴巴过滤
                    || filterTb(trade, errorSidMsgMap)//淘宝
                    || filterTmgjzy(trade, errorSidMsgMap)//天猫国际直营订单过滤
                    || filterTmDelivery(trade, errorSidMsgMap)//天猫直送订单过滤
                    || filterLazada(trade, errorSidMsgMap)//lazada订单过滤
                    || filterTiktok(trade, errorSidMsgMap)//tiktok订单不支持合单
                    || filterPoision(trade, errorSidMsgMap)//得物直发过滤
                    || filterOutSid(trade, mergeContext, errorSidMsgMap)//运单号过滤
                    || filterWxSphBicTrade(staff, trade, errorSidMsgMap) // 过滤微信小店（微信小店（微信视频号）质检订单
                    || filterLimit(staff, mergeContext, trade, duplicateTids, errorSidMsgMap)//合单数量过滤
                    || filterAlibabaDropShop(trade, errorSidMsgMap)//1688跨境转运单合单过滤
                    || filterMergeZhenkunhangTrades(trade, errorSidMsgMap)//有“震坤行代管代发”、“震坤行直发”系统标签的订单不允许与其他订单操作合单。
                    || filterTbXsd(trade, errorSidMsgMap)//1688跨境转运单合单过滤
                    || filter1688MJZT(trade, errorSidMsgMap)//1688跨境转运单合单过滤
                    || filterNfgoodHelpSell(trade, errorSidMsgMap)  // 花城农夫帮卖订单
                    || filterMergeKttHelpTrades(trade, errorSidMsgMap)//有“快团团帮卖”系统标签的订单不允许与其他订单操作合单。
                    || filterHwscGov(trade, errorSidMsgMap)//华为国补3C
                    || filterO2oTrades(trade, errorSidMsgMap)//过滤o2o订单
                    || filterZAMY(trade, errorSidMsgMap)//挚爱母婴 平台订单不允许合单
            ;
        }
        return filter;
    }

    /**
     *合单规则 运单号过滤
     * <a href="https://gykj.yuque.com/entavv/xb9xi5/mslp9stxz2g78g1x">运单号过滤</a>
     */
    private boolean filterOutSid(Trade trade, TradeMergeContext context, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (context.isPoisionOutsid() && StringUtils.isNotBlank(trade.getOutSid())) {
            errorSidMsgMap.computeIfAbsent("规则勾选了[已获取运单号的单不参与合单]且该订单有运单号不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,type=%s,outSid=%s", trade.getSid(), trade.getTid(), trade.getType(), trade.getOutSid()));
            return true;
        }
        return false;
    }

    private static final Set<String> ADDRESS_MD5_PALT = new HashSet<>();
    private static final Set<String> SYS_ADDRESS_MD5_PALT = new HashSet<>();

    static {
        ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_HWSC);
        ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_FXG_GX);
        ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_JD);
        ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_JD_VC);
        ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_JD_GXPT);
        ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_BAIDU_HEALTH);
        ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_ZFBDG);
        ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_YZ);

        SYS_ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_JD);
        SYS_ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_JD_VC);
        SYS_ADDRESS_MD5_PALT.add(CommonConstants.PLAT_FORM_TYPE_JD_GXPT);
    }

    /**
     * 1688跨境转运单不支持合单
     *
     * @param trade
     * @param errorSidMsgMap
     * @return
     */
    private boolean filterAlibabaDropShop(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (PlatformUtils.is1688DropShippingTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("1688跨境转运单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    private boolean filterTbXsd(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isTbXsdTrade(trade)&&!TradeUtils.isTbXsdB2cTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("淘宝小时达不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    private boolean filterHwscGov(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (PlatformUtils.isHuaweiGovTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("华为商城国补订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    private boolean filter1688MJZT(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (PlatformUtils.is1688ZiTiTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("1688「现场自提」的订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }


    /**
     * 手工合单弹窗提醒
     */
    public void mergeManualPopUpReminder(Staff staff, TradeMergeContext mergeContext, List<Trade> trades) {
        if (trades == null || trades.isEmpty()) {
            return;
        }
        List<Long> uploadSids = new ArrayList<>(trades.size());
        for (Trade trade : trades) {
            if (Objects.equals(1, trade.getIsUpload())) {
                uploadSids.add(trade.getSid());
            }
        }
        boolean containAcrossWeeks = containAcrossWeeks(trades);
        if (uploadSids.isEmpty() && !containAcrossWeeks) {
            return;
        }


        String patten = "<div style=\"word-break:break-all;\">" +
                "<div style=\"font-size: 24px;color: red;\">风险提示！！！</div>" +
                "%s%s" +
                "<span>确认要</span><span style=\"font-size:20px;color:red\">强制执行</span>" +
                "<span>:" + "手工合单操作?</span>" +
                "</div>";
        String msg = String.format(patten,
                !uploadSids.isEmpty() ? "<span>平台预发货订单合并后可能会导致原单号取消，可能会被平台</span><span style=\"font-size:20px;color:red\">罚款!!!</span><br>" +
                        "<span>已上传发货订单:" + JSONObject.toJSONString(uploadSids) + "</span><br>" : "",
                containAcrossWeeks ? "<span style=\"font-size:20px;color:red\">1688跨周订单合单后可能导致无法取号和上传，请谨慎操作</span><br>" : "");
        throw new PopUpRemainderException(msg);
    }

    /**
     * 手工合单校验
     */
    public void mergeManualCheck(Staff staff, TradeMergeContext mergeContext, List<Trade> trades) {
        if (!mergeContext.isMergeManualCheck()) {
            return;
        }
        mergeFilter(staff, mergeContext, trades);
        if (mergeContext.getErrorMsg() != null && !mergeContext.getErrorMsg().isEmpty()) {
            throw new TradeException(JSON.toJSONString(mergeContext.getErrorMsg()));
        }
        if (trades.size() <= 1) {
            throw new TradeException("只查询到一笔订单无法合单");
        }
        if (!mergeContext.isLocked()) {
            checkAddress(staff, mergeContext, trades);
        }
        checkLimit(staff, trades);
        // 平台规则限制跨周期订单不允许合单
        validateExistAcrossWeeks(mergeContext, trades);

        //货到付款的订单数量、确认发货的订单数量
        int codNum = 0, canConfirmNum = 0, platNum = 0, btasNum = 0, noBtasNum = 0;
        Set<Long> warehouseIds = new HashSet<>();
        Set<Integer> scalpings = new HashSet<>();
        Set<Integer> belongTypes = new HashSet<>();
        Set<String> platSources = new HashSet<>();
        Set<String> addressMd5s = new HashSet<>();
        Set<String> sysAddressMd5s = new HashSet<>();
        int jdBigDeliveryNum = 0, jdSmallDeliveryNum = 0;

        int sysTradeNum = 0;
        int sysAddressMd5Num = 0;
        int gxdNum = 0;//工小达
        int tradeNum = 0;
        int pddJyNum = 0;
        int pddNum = 0;
        List<Trade> pddJyTrades = new ArrayList<>();//拼多多集运
        for (Trade trade : trades) {
            if (PlatformUtils.isJdVCGongXiaoDaOrder(trade)) {
                gxdNum++;
            }
            if (TradeConsolidateInfoUtils.isPddConsolidatedTrade(trade)) {
                pddJyTrades.add(trade);
            }
            if (trade.getEnableStatus() == 1) {//隐藏订单不校验
                tradeNum++;
                if (!TradeStatusUtils.isWaitAudit(trade.getSysStatus())) {
                    throw new TradeException(String.format("非待审核的订单不能合单系统订单号=%s，请刷新页面重试", trade.getSid()));
                }
                if (TradeConsolidateInfoUtils.isPddConsolidatedTrade(trade)) {
                    pddJyNum++;
                }
                warehouseIds.add(trade.getWarehouseId());
                scalpings.add(trade.getScalping());
                belongTypes.add(trade.getBelongType());
                if (warehouseIds.size() > 1) {
                    throw new TradeException("不同仓库订单不能合单，请刷新页面重试！");
                }
                if (scalpings.size() > 1) {
                    throw new TradeException("空包单和非空包单不能合单，请刷新页面重试！");
                }
                if (belongTypes.size() > 1) {
                    throw new TradeException("不同类型的订单不能合单，请刷新页面重试！");
                }
                if (TradeTypeUtils.isFxgBtasTrade(trade)) {
                    btasNum++;
                } else {
                    noBtasNum++;
                }
                String source = TradeMergePlatformRoute.getRealSource(staff, trade);
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isSplit(staff, trade) && CommonConstants.PLAT_FORM_TYPE_SYS.equals(source)) {
                    source = trade.getSplitSource();
                }
                String addressMd5 = StringUtils.trimToEmpty(trade.getAddressMd5());
                if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(source)) {
                    sysTradeNum++;
                    if (StringUtils.isNotBlank(addressMd5) && (SYS_ADDRESS_MD5_PALT.contains(trade.getSubSource()))) {
                        sysAddressMd5s.add(addressMd5);
                        sysAddressMd5Num++;
                    }
                } else {
                    platSources.add(source);
                    if (PlatformUtils.isJdBigDelivery(trade.getTradeExt(), source)) {
                        jdBigDeliveryNum++;
                    }
                    if (PlatformUtils.isJdSmallDelivery(trade.getTradeExt(), source)) {
                        jdSmallDeliveryNum++;
                    }
                    if (CommonConstants.PLAT_FORM_TYPE_PDD.equals(source)) {
                        pddNum++;
                    }
                    if (platSources.size() > 1 && TradeMergeEnum.MERGE_NULTIL != mergeContext.getTradeMergeEnum()) {
                        throw new TradeException("暂不支持多个平台订单的合单，请刷新页面重试！");
                    }
                    platNum++;

                    if (PlatformUtils.isCod(staff, trade)) {
                        codNum++;
                    }
                    if (codNum > 0) {
                        if (codNum != platNum) {
                            throw new TradeException("货到付款订单不能与普通订单合单，请刷新页面重试！");
                        }
                    }

                    if (trade.getCanConfirmSend() == 1) {
                        canConfirmNum++;
                    }
                    if (canConfirmNum > 0 && canConfirmNum != platNum) {
                        throw new TradeException("在线发货待确认订单不能与普通订单合单，请刷新页面重试！");
                    }
                    if (ADDRESS_MD5_PALT.contains(source) && !TradeConsolidateInfoUtils.isPddConsolidatedTrade(trade)) {
                        addressMd5s.add(addressMd5);
                    }
                }
            }
            if (jdBigDeliveryNum > 0 && jdSmallDeliveryNum > 0) {
                throw new TradeException("受京东平台规则限制，京东大件送货上门订单不能和京东中小件送货上门订单合单，请刷新页面重试！");
            }
            if (btasNum > 0 && btasNum != noBtasNum) {
                throw new TradeException("BTAS订单不允许与其他类型订单合单，请刷新页面重试，请刷新页面重试！");
            }
            if (pddNum > 0 && sysTradeNum > 0) {
                throw new TradeException("平台单和非平台单（导入，手工新建，复制新建，手动新建换货/补发）不支持手动合单。");
            }
            if (!addressMd5s.isEmpty()) {
                if (addressMd5s.size() != 1) {
                    throw new TradeException(String.format("%s订单加密之后地址信息不一样不允许合单！", platSources));
                }
                if (sysTradeNum > 0) {
                    throw new TradeException(String.format("%s订单不允许跟系统订单合单！", platSources));
                }
            }
            if (sysAddressMd5Num > 0 && (sysAddressMd5Num != sysTradeNum || sysAddressMd5s.size() > 1)) {
                throw new TradeException("收件地址不同不支持合单,请刷新页面重试！");
            }
            if (TradeMergeEnum.MERGE_NULTIL == mergeContext.getTradeMergeEnum() || TradeMergeEnum.MERGE_AUTO_NULTIL == mergeContext.getTradeMergeEnum()) {
                if (codNum > 0) {
                    throw new TradeException(String.format("货到付款订单不支持跨店铺合单,系统订单号=%s,请刷新页面重试！", trade.getSid()));
                }
                if (TradeUtils.isPlatformFxTrade(trade)) {
                    throw new TradeException(String.format("平台分销订单不支持跨店铺合单,系统订单号=%s,请刷新页面重试！", trade.getSid()));
                }
            }
        }
        if (gxdNum > 0 && gxdNum != tradeNum) {
            throw new TradeException(String.format("京东工小达订单不能和非工小达的订单合并在一起,系统订单号=%s,请刷新页面重试！", TradeUtils.toSidList(trades)));
        }
        if (pddJyNum > 0) {
            if (pddJyNum != tradeNum) {
                if (tradeNum - pddJyNum == 1) {
                    throw new TradeException("合单同时包含集运单和非集运单默认只处理非集运单，当前所选订单只有一个非集运单，请重新选择!");
                }
                trades.removeAll(pddJyTrades);
            } else if (TradeMergeEnum.MERGE_NULTIL == mergeContext.getTradeMergeEnum()) {
                throw new TradeException("拼多多集运订单不支持跨店合单。");
            }
        }
    }

    /**
     * 取消合单过滤
     */
    public boolean mergeUndoFilter(Staff staff, TradeMergeContext mergeContext, Trade mainTrade, List<Trade> mergeTrades) {
        boolean filter;
        Map<String, Map<Long, String>> errorSidMsgMap = new HashMap<>();
        if (mainTrade.getIsCancel() == 1) {
            errorSidMsgMap.computeIfAbsent("作废订单不能取消合单", k -> new HashMap<>()).put(mainTrade.getSid(), String.format("sid=%s,tid=%s,isCancel=%s", mainTrade.getSid(), mainTrade.getTid(), mainTrade.getIsCancel()));
        } else if (!Trade.SYS_STATUS_WAIT_AUDIT.equals(mainTrade.getSysStatus())) {
            errorSidMsgMap.computeIfAbsent("非待审核的订单不能取消合单", k -> new HashMap<>()).put(mainTrade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s", mainTrade.getSid(), mainTrade.getTid(), mainTrade.getSysStatus()));
        } else if (TradeExceptUtils.isContainExcept(staff, mainTrade, ExceptEnum.PART_PAY_EXCEPT)) {
            errorSidMsgMap.computeIfAbsent("部分付款异常的订单不能取消合单", k -> new HashMap<>()).put(mainTrade.getSid(), String.format("sid=%s,tid=%s", mainTrade.getSid(), mainTrade.getTid()));
        } else {
            for (Trade trade : mergeTrades) {
                //得物直发过滤
                if (TradeTypeEnum.POISION_DIRECT_SPOT_TRADE.getTypeToConfirm().apply(trade) && Objects.equals(trade.getIsUpload(), 1)) {
                    errorSidMsgMap.computeIfAbsent("得物直发的订单已经预发货不能取消合单", k -> new HashMap<>()).put(mainTrade.getSid(), String.format("sid=%s,tid=%s,type=%s,isUpload=%s,mergeSid=%s", trade.getSid(), trade.getTid(), trade.getType(), trade.getIsUpload(), trade.getMergeSid()));
                }
                // 部分付款异常订单过滤
                boolean containExcept = TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PART_PAY_EXCEPT);
                if (containExcept) {
                    errorSidMsgMap.computeIfAbsent("部分付款异常的订单不能取消合单", k -> new HashMap<>()).put(mainTrade.getSid(), String.format("sid=%s,tid=%s", mainTrade.getSid(), mainTrade.getTid()));
                }
            }
        }
        filter = !errorSidMsgMap.isEmpty();
        //记录日志
        TradeFilterLogUtils.printLog(staff, errorSidMsgMap, mergeContext.isSpider(), mergeContext.isReturnErrorLog() ? mergeContext.getErrorMsg() : null);
        return filter;
    }

    public void checkAddress(Staff staff, TradeMergeContext mergeContext, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        if (CollectionUtils.isNotEmpty(trades) && trades.size() >= 2) {
            List<Trade> sysTrades = new ArrayList<>();
            for (Trade trade : trades) {
                if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && (trade.getSplitSid() < 0 || CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSplitSource()))) {
                    sysTrades.add(trade);
                }
            }
            if (sysTrades.size() == trades.size()) {
                return;
            }

            if (!fxgTradeDecryptBusiness.fxgTradeAddressCompare(staff, trades)) {
                throw new TradeException("放心购订单加密之后，地址不同暂时不允许合单");
            }
            // https://gykj.yuque.com/entavv/xb9xi5/xvlsx188m0oax389
            boolean flag = fxgTradeDecryptBusiness.fxgMergeSysCompare(staff, trades, mergeContext.getMergeManualAddressSid());
            if (!flag) {
                throw new TradeException("放心购订单与手工单合单, 选的地址必须为手工单");
            }

            if (!TradeMergeEnum.MERGE_MANUAL.equals(mergeContext.getTradeMergeEnum()) &&
                    !commonTradeDecryptBusiness.equalsAddressMd5(staff, trades, CommonConstants.PLAT_FORM_TYPE_1688)
            ) {
                throw new TradeException("1688订单加密之后，地址不同暂时不允许合单");
            }
            if (!commonTradeDecryptBusiness.thhtradeAddressCompare(staff, trades)) {
                throw new TradeException("团好货订单加密之后，地址不同暂时不允许合单");
            }
            if (!commonTradeDecryptBusiness.kuaiShouManualMergeCheck(staff, trades)) {
                throw new TradeException("快手订单加密之后，地址不同暂时不允许合单！");
            }
            if (!commonTradeDecryptBusiness.vipTradeAddressCompare(staff, trades)) {
                throw new TradeException("唯品会订单加密之后，地址不同暂时不允许合单！");
            }
            if (!commonTradeDecryptBusiness.xhsTradeAddressCompare(staff, trades)) {
                throw new TradeException("小红书订单加密之后，地址不同暂时不允许合单！");
            }
            if (!commonTradeDecryptBusiness.wdTradeAddressCompare(staff, trades)) {
                throw new TradeException("微店订单加密之后，地址不同暂时不允许合单");
            }
        }
    }

    public void checkLimit(Staff staff, List<Trade> trades) {
        int poisionCount = 0;
        int jitxCount = 0;
        int jitxNormalCount = 0;
        int alihealthCount = 0;
        for (Trade trade : trades) {
            if (TradeTypeEnum.POISION_DIRECT_SPOT_TRADE.getTypeToConfirm().apply(trade)) {
                poisionCount++;
            }
            if (TradeTypeEnum.JITX_TRADE.getTypeToConfirm().apply(trade)) {
                jitxCount++;
                if ("1".equals(trade.getType())) {
                    jitxNormalCount++;
                }
            }
            if (CommonConstants.PLAT_FORM_TYPE_ALIHEALTH.equals(trade.getSource())) {
                alihealthCount++;
            }
        }

        if (poisionCount > 8) {
            throw new TradeException("得物直发订单合单数量不能超过8!");
        }
        if (poisionCount != 0 && poisionCount != trades.size()) {
            throw new TradeException("只允许得物品牌直发之间且满足合单条件订单进行合单!");
        }
        if (jitxCount > 66) {
            throw new TradeException("唯品会jitx订单合单数量不能超过66!");
        }
        if (jitxNormalCount > 0 && jitxNormalCount != jitxCount) {
            throw new TradeException("只允许满足合单条件普通JITX订单之间进行合单!");
        }
        if (alihealthCount > 5) {
            throw new TradeException("阿里健康订单合单数量不能超过5!");
        }
    }

    /**
     * 系统状态过滤
     */
    private boolean filterSysStatus(TradeMergeContext mergeContext, TradeConfigContext configContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (mergeContext.isMergeFinishAudit()) {
            if (!mergeContext.isSpider() && TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
                errorSidMsgMap.computeIfAbsent("已发货后的订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s", trade.getSid(), trade.getTid(), trade.getSysStatus()));
                return true;
            }
            //不支持合并待打印
            if (!mergeContext.isMergeWaitPrint() && TradeStatusUtils.isWaitPrint(trade)) {
                errorSidMsgMap.computeIfAbsent("待打印的订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s,expressPrintTime=%s,mergeWaitPrint=%s", trade.getSid(), trade.getTid(), trade.getSysStatus(), DateUtils.datetime2Str(trade.getExpressPrintTime()), mergeContext.isMergeWaitPrint()));
                return true;
            }

            //不支持合并待包装
            if (!mergeContext.isMergeWaitPack() && TradeStatusUtils.isWaitPack(trade, configContext.getOpenPackage())) {
                errorSidMsgMap.computeIfAbsent("待包装的订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s,expressPrintTime=%s,mergeWaitPrint=%s", trade.getSid(), trade.getTid(), trade.getSysStatus(), DateUtils.datetime2Str(trade.getExpressPrintTime()), mergeContext.isMergeWaitPrint()));
                return true;
            }

            //不支持合并待称重
            if (!mergeContext.isMergeWaitWeight() && TradeStatusUtils.isWaitWeigh(trade, configContext.getOpenPackage(), configContext.getOpenWeigh())) {
                errorSidMsgMap.computeIfAbsent("待称重的订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s,expressPrintTime=%s,isPackage=%s,isWeigh=%s,mergeWaitPack=%s,openPackageExamine=%s,mergeWaitWeight=%s,openPackageWeigh=%s", trade.getSid(), trade.getTid(), trade.getSysStatus(), DateUtils.datetime2Str(trade.getExpressPrintTime()), trade.getIsPackage(), trade.getIsWeigh(), mergeContext.isMergeWaitPack(), configContext.getOpenPackage(), mergeContext.isMergeWaitWeight(), configContext.getOpenWeigh()));
                return true;
            }

            //不支持合并待发货的
            if (!mergeContext.isMergeWaitConsign() && TradeStatusUtils.isWaitConsign(trade, configContext.getOpenPackage(), configContext.getOpenWeigh())) {
                errorSidMsgMap.computeIfAbsent("待发货的订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s,expressPrintTime=%s,isPackage=%s,isWeigh=%s,mergeWaitPack=%s,openPackageExamine=%s,mergeWaitWeight=%s,openPackageWeigh=%s, mergeWaitConsign=%s", trade.getSid(), trade.getTid(), trade.getSysStatus(), DateUtils.datetime2Str(trade.getExpressPrintTime()), trade.getIsPackage(), trade.getIsWeigh(), mergeContext.isMergeWaitPack(), configContext.getOpenPackage(), mergeContext.isMergeWaitWeight(), configContext.getOpenWeigh(), mergeContext.isMergeWaitConsign()));
                return true;
            }
        } else {
            if (!mergeContext.isSpider() && !Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus())) {
                errorSidMsgMap.computeIfAbsent("非待审核的订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s", trade.getSid(), trade.getTid(), trade.getSysStatus()));
                return true;
            }
        }
        return false;
    }

    /**
     * 系统订单过滤
     */
    private boolean filterSys(TradeMergeContext mergeContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        boolean platformSplit = trade.getSplitSid() != null && trade.getSplitSid() > 0 && !CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSplitSource());
        if (!mergeContext.isMergeSys() && CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && !platformSplit) {
            errorSidMsgMap.computeIfAbsent("允许合并手工单配置未开启手工单单不能合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 档口订单过滤
     */
    private boolean filterDangkou(TradeMergeContext mergeContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (trade.isDangkouTrade() && mergeContext.getTradeMergeEnum() == TradeMergeEnum.MERGE_MANUAL) {
            if (!mergeContext.isAllowDangkouMerge()) {
                errorSidMsgMap.computeIfAbsent("档口订单合单配置未开启不能合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s, type=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getType()));
                return true;
            }
            if (StringUtils.isNotBlank(trade.getTagIds()) && trade.getTagIds().contains(String.valueOf(SystemTags.TAG_SALE_TRDE_PRE_ALLOCATE_GOODS.getId()))) {
                errorSidMsgMap.computeIfAbsent("档口订单已预配货不能合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s, type=%s,tagIds=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getType(), trade.getTagIds()));
                return true;
            }
        }
        return false;
    }

    /**
     * 预发货订单过滤
     */
    private boolean filterPreUpload(TradeMergeContext mergeContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (trade.getIsUpload() != null && trade.getIsUpload() == 1 && !mergeContext.isMergePreUpload()) {
            errorSidMsgMap.computeIfAbsent("预发货订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,isUpload=%s,mergePreUpload=%s", trade.getSid(), trade.getTid(), trade.getIsUpload(), mergeContext.isMergePreUpload()));
            return true;
        }
        return false;
    }

    /**
     * 智能合单已经取消合单的订单是否合并
     */
    public boolean filterMergeType(Staff staff, TradeMergeContext mergeContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeMergeEnum.MERGE_AUTO_MANUAL == mergeContext.getTradeMergeEnum() && !mergeContext.isMergeUndoMerge() && trade.getMergeSid() <= 0 && TradeMergeEnum.MERGE_UNDO.getDbType() - trade.getMergeType() == 0) {
            errorSidMsgMap.computeIfAbsent("已取消合单的订单不支持合单(可配置)", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeType=%s", trade.getSid(), trade.getTid(), trade.getMergeType()));
            return true;
        }
        return false;
    }

    /**
     * 智能合单拆单是否合并
     */
    public boolean filterSplitType(Staff staff, TradeMergeContext mergeContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeMergeEnum.MERGE_AUTO_MANUAL == mergeContext.getTradeMergeEnum() && !mergeContext.isMergeSplit() && trade.getSplitSid() > 0) {
            errorSidMsgMap.computeIfAbsent("拆单的订单不支持合单(可配置)", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,splitSid=%s", trade.getSid(), trade.getTid(), trade.getSplitSid()));
            return true;
        }
        return false;
    }

    /**
     * 重量过滤
     */
    public boolean filterNetWeight(TradeMergeContext mergeContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        double tradeNetWeight = NumberUtils.nvlDouble(trade.getNetWeight());
        if (mergeContext.getMaxNetWeight() > 0 && tradeNetWeight >= mergeContext.getMaxNetWeight()) {
            errorSidMsgMap.computeIfAbsent("订单商品总重量不满足配置不能合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,tradeNetWeight=%s,mergeConfMaxNetWeight=%s", trade.getSid(), trade.getTid(), tradeNetWeight, mergeContext.getMaxNetWeight()));
            return true;
        }
        return false;
    }

    /**
     * 商品数量过滤
     */
    public boolean filterItemNum(TradeMergeContext mergeContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (mergeContext.getMaxItemNum() > 0 && trade.getNotIsVirtualItemNum() >= mergeContext.getMaxItemNum()) {
            errorSidMsgMap.computeIfAbsent("订单商品数量不满足配置不能合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,notIsVirtualItemNum=%s,mergeConfMaxItemNum=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), trade.getNotIsVirtualItemNum(), mergeContext.getMaxItemNum()));
            return true;
        }
        return false;
    }

    /**
     * 跨店合单过滤
     */
    public boolean filterMergeMultil(Staff staff, TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeMergeEnum.MERGE_NULTIL == context.getTradeMergeEnum() || TradeMergeEnum.MERGE_AUTO_NULTIL == context.getTradeMergeEnum()) {
            if (CommonConstants.PLAT_FORM_TYPE_JD.equals(trade.getSource())) {
                errorSidMsgMap.computeIfAbsent("京东订单不支持跨店合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                return true;
            }
            if (CommonConstants.PLAT_FORM_TYPE_FXG_DF.equals(trade.getSource())) {
                errorSidMsgMap.computeIfAbsent("抖音厂商代发订单不支持跨店合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                return true;
            }
            if (CommonConstants.PLAT_FORM_TYPE_FXG.equals(trade.getSource()) && (trade.getTradeExt() == null || StringUtils.isEmpty(trade.getTradeExt().getReceiverMobileIndex()))) {
                errorSidMsgMap.computeIfAbsent("抖音订单不支持跨店铺合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                return true;
            }
            if (CommonConstants.PLAT_FORM_TYPE_FXG_GX.equals(trade.getSource())) {
                errorSidMsgMap.computeIfAbsent("抖音供销订单不支持跨店铺合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                return true;
            }
            if (TradeMergeEnum.MERGE_AUTO_NULTIL == context.getTradeMergeEnum()) {
                if (CommonConstants.PLAT_FORM_TYPE_PDD.equals(trade.getSource())) {
                    errorSidMsgMap.computeIfAbsent("拼多多订单不支持自动跨店铺合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                    return true;
                }
                if (TradeTypeEnum.POISION_DIRECT_SPOT_TRADE.getTypeToConfirm().apply(trade)) {
                    errorSidMsgMap.computeIfAbsent("得物直发订单不支持跨店合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,type=%s", trade.getSid(), trade.getTid(), trade.getType()));
                    return true;
                }
            }
            if (TradeMergeEnum.MERGE_NULTIL == context.getTradeMergeEnum()) {
                if (CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(trade.getSource())) {
                    errorSidMsgMap.computeIfAbsent("快手订单不支持手动跨店铺合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                    return true;
                }
            }
            if (CommonConstants.PLAT_FORM_TYPE_1688.equals(trade.getSource())) {
                errorSidMsgMap.computeIfAbsent("1688订单不支持跨店铺合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                return true;
            }
            if (CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(trade.getSource())) {
                errorSidMsgMap.computeIfAbsent("1688_c2m订单不支持跨店铺合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                return true;
            }
            if (CommonConstants.PLATFROM_TYPE_THH.equals(trade.getSource())) {
                errorSidMsgMap.computeIfAbsent("团好货订单不支持跨店铺合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                return true;
            }
            if (CommonConstants.PLAT_FORM_TYPE_XHS.equals(trade.getSource())) {
                errorSidMsgMap.computeIfAbsent("小红书订单不支持跨店铺合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                return true;
            }
            if (CommonConstants.PLAT_FORM_TYPE_HWSC.equals(trade.getSource())) {
                errorSidMsgMap.computeIfAbsent("华为商城订单不支持跨店铺合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
                return true;
            }
            if (CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSource())) {
                if (CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource())) {
                    errorSidMsgMap.computeIfAbsent("唯品会JIT订单不支持跨店合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource()));
                    return true;
                }
                if (CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource())) {
                    errorSidMsgMap.computeIfAbsent("唯品会JITX订单不支持跨店合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource()));
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 快手过滤
     */
    private boolean filterKuaishou(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_KUAISHOU_DF.equals(trade.getSubSource())) {
            errorSidMsgMap.computeIfAbsent("快手代发不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSubSource()));
            return true;
        }
        if (CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(trade.getSource())) {
            //不允许合单的情况
            if (!TradeUtils.isKuaishouMerge(trade)) {
                errorSidMsgMap.computeIfAbsent("快手礼物订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSubSource()));
                return true;
            }
        }
        return false;
    }

    /**
     * 抖音过滤
     */
    public boolean filterFxg(Staff staff, TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (PlatformUtils.isFxgNYuanMPiecesTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("抖音N元M件订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        if (TradeUtils.isFxgBicTrade(trade, context.getConfigContext().getOpenBicQualityTestingShopUserIds())) {
            errorSidMsgMap.computeIfAbsent("抖音BIC质检订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,userId=%s,openBicQualityTestingShopUserIds=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getUserId(), context.getConfigContext().getOpenBicQualityTestingShopUserIds()));
            return true;
        }
        if (TradeUtils.isFxgcsSource(trade)) {
            errorSidMsgMap.computeIfAbsent("抖音超市订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        if (PlatformUtils.isFxgGovSubsidy3CTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("受平台规则限制，国补3C订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 分销过滤
     */
    private boolean filterFx(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isPlatformFxTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("平台分销不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,convertType=%s,belongType=%s", trade.getSid(), trade.getTid(), trade.getConvertType(), trade.getBelongType()));
            return true;
        }
        if (TradeUtils.isFxOrMixTrade(trade) && Trade.SYS_STATUS_FINISHED_AUDIT.equals(trade.getSysStatus())) {
            errorSidMsgMap.computeIfAbsent("已审核的分销单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s,convertType=%s,belongType=%s,sourceId=%s,destId=%s", trade.getSid(), trade.getTid(), trade.getSysStatus(), trade.getConvertType(), trade.getBelongType(), trade.getSourceId(), trade.getDestId()));
            return true;
        }
        return false;
    }

    /**
     * 供销过滤
     */
    private boolean filterGx(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isGxTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("供销订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,convertType=%s,belongType=%s,destId=%s", trade.getSid(), trade.getTid(), trade.getConvertType(), trade.getBelongType(), trade.getDestId()));
            return true;
        }
        return false;
    }

    /**
     * 拼多多 过滤
     */
    public boolean filterPdd(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TypeUtils.hasTradeType(trade, TradeTypeNewEnum.PDD_GLOBAL_CUSTODY)) {
            errorSidMsgMap.computeIfAbsent("拼多多全境托管订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,tradeTypeMap=%s", trade.getSid(), trade.getTid(), trade.getTradeTypeMap()));
            return true;
        }
        if (PlatformUtils.isPddLocalWarehouseTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("拼多多本地仓订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,tradeTypeMap=%s", trade.getSid(), trade.getTid(), trade.getTradeTypeMap()));
            return true;
        }
        return false;
    }

    /**
     * 奇门 过滤
     */
    public boolean filterQimen(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(trade.getSource()) && TradeConsolidateInfoUtils.isPddConsolidatedTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("奇门拼多多集运订单，不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource()));
            return true;
        }
        return false;
    }

    /**
     * 唯品会的订单过滤
     */
    public boolean filterVip(Staff staff, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSource())) {
            if (CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource())) {
                errorSidMsgMap.computeIfAbsent("唯品会jit订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource()));
                return true;
            }
            if (CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource())) {
                if (StringUtils.isBlank(trade.getAddressMd5())) {
                    errorSidMsgMap.computeIfAbsent("唯品会jitx订单没有合包码不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,addressMd5=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getAddressMd5()));
                    return true;
                }
                if (!"1".equals(trade.getType())) {
                    errorSidMsgMap.computeIfAbsent("唯品会jitx订单不是普通单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,type=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getType()));
                    return true;
                }
                if (trade.getIsUpload() != null && trade.getIsUpload() == 1) {
                    errorSidMsgMap.computeIfAbsent("唯品会jitx订单已预发货不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,isUpload=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getIsUpload()));
                    return true;
                }
                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RISK)) {
                    errorSidMsgMap.computeIfAbsent("唯品会jitx订单不可发货不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,itemExcep=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getItemExcep()));
                    return true;
                }

            }
            if (StringUtils.isBlank(trade.getSubSource()) && StringUtils.isNotBlank(trade.getPostFee()) && NumberUtils.str2Double(trade.getPostFee()) != 0) {
                errorSidMsgMap.computeIfAbsent("唯品会MP订单有运费不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,postFee=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getPostFee()));
                return true;
            }
        }
        return false;
    }

    public <T extends Trade> boolean filterMergeKttHelpTrades(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isKttHelpSaleTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("快团团帮卖单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,type=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getType()));
            return true;
        }
        return false;
    }

    public <T extends Trade> boolean filterO2oTrades(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (PlatformUtils.isTradeO2o(trade)) {
            errorSidMsgMap.computeIfAbsent("O2O订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,type=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getType()));
            return true;
        }
        return false;
    }

    public <T extends Trade> boolean filterMergeZhenkunhangTrades(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_ZHENKUNHANG.equals(trade.getSource())) {
            if (TradeUtils.isZhenkunhangLogisticsTrade(trade)) {
                errorSidMsgMap.computeIfAbsent("震坤行代管代发不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,type=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getType()));
                return true;
            }
        }
        return false;
    }


    /**
     * 京东直发过滤
     */
    public boolean filterJd(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeTypeEnum.JD_DIRECT_TRADE.getTypeToConfirm().apply(trade) || PlatformUtils.isJdWarehouseOrder(trade)) {
            errorSidMsgMap.computeIfAbsent("京东直发订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,subSource=%s,type=%s", trade.getSid(), trade.getTid(), trade.getSubSource(), trade.getType()));
            return true;
        }
        return false;
    }

    /**
     * 京品试订单过滤
     */
    public boolean filterJps(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (StringUtils.isNotBlank(trade.getTagIds()) && trade.getTagIds().contains(String.valueOf(SystemTags.TAG_JD_JPS.getId()))) {
            errorSidMsgMap.computeIfAbsent("京品试订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSubSource()));
            return true;
        }
        return false;
    }

    /**
     * 阿里巴巴
     */
    public boolean filterAlibaba(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_ALIBABA_LST.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("零售通订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        if (CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("阿里国际站订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        if (TradeUtils.isAlibabaAeTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("阿里1688AE自营订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        if (PlatformUtils.isAlibabaZtTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("阿里1688自提订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 阿里巴巴-零售通过滤
     */
    public boolean filterAlibabaLst(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_ALIBABA_LST.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("零售通订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 阿里巴巴-阿里国际站订单不支持合单
     */
    public boolean filterAlibabaIcbu(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("阿里国际站订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 阿里巴巴-1688AE自营订单不支持合单
     */
    public boolean filterAlibaba1688AE(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isAlibabaAeTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("阿里1688AE自营订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 淘宝
     */
    public boolean filterTb(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isCainiaoWarehouseTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("菜鸟仓自流转订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource()));
            return true;
        }
        if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO_DF.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("淘宝厂商代发订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
//        if (TradeUtils.isTbPresentTrade(trade)) {
//            errorSidMsgMap.computeIfAbsent("淘宝礼物订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s", trade.getSid(), trade.getTid()));
//            return true;
//        }
        return false;
    }

    /**
     * 天猫国际直营订单过滤
     */
    public boolean filterTmgjzy(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_TMGJZY.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("天猫国际直营订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 天猫直送订单过滤
     */
    public boolean filterTmDelivery(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (trade.getIsTmallDelivery() != null && trade.getIsTmallDelivery() == 1) {
            errorSidMsgMap.computeIfAbsent("天猫直送订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,isTmallDelivery=%s", trade.getSid(), trade.getTid(), trade.getIsTmallDelivery()));
            return true;
        }
        return false;
    }

    /**
     * Lazada订单过滤
     */
    public boolean filterLazada(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_LAZADA.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("lazada订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * Tiktok订单过滤
     */
    public boolean filterTiktok(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_TIKTOK.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("tiktok订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }


    /**
     * 得物直发过滤
     */
    public boolean filterPoision(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeTypeEnum.POISION_DIRECT_SPOT_TRADE.getTypeToConfirm().apply(trade)) {
            if (TradeUtils.isAfterSendGoods(trade) || TradeStatusUtils.isWaitPay(trade.getSysStatus())) {
                errorSidMsgMap.computeIfAbsent("得物品牌直发订单已发货或者待付款不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s", trade.getSid(), trade.getTid(), trade.getSysStatus()));
                return true;
            }

            if (trade.getIsUpload() != null && trade.getIsUpload() == 1) {
                errorSidMsgMap.computeIfAbsent("得物品牌直发订单已经预发货不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,type=%s,source=%s", trade.getSid(), trade.getTid(), trade.getType(), trade.getSource()));
                return true;
            }

            TradeExt tradeExt = trade.getTradeExt();
            if (tradeExt == null || StringUtils.isBlank(tradeExt.getExtraFields())) {
                errorSidMsgMap.computeIfAbsent("得物品牌直发订单扩展信息不存在不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,extraFields=%s", trade.getSid(), trade.getTid(), (tradeExt == null ? "" : tradeExt.getExtraFields())));
                return true;
            }

            String logisticsCode = StringUtils.isBlank(tradeExt.getLogisticsCode()) ? "noCode" : tradeExt.getLogisticsCode();
            if (StringUtils.isNotBlank(logisticsCode) && !"noCode".equals(logisticsCode)) {
                errorSidMsgMap.computeIfAbsent("得物直发订单指定了物流不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,logisticsCode=%s", trade.getSid(), trade.getTid(), logisticsCode));
                return true;
            }

            String canMergeDeliveryFlag = (String) JSONObject.parseObject(tradeExt.getExtraFields(), Map.class).get("can_merge_delivery_flag");
            if (!"1".equals(canMergeDeliveryFlag)) {
                errorSidMsgMap.computeIfAbsent("得物直发订单平台返回数据不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,can_merge_delivery_flag=%s", trade.getSid(), trade.getTid(), canMergeDeliveryFlag));
                return true;
            }
        }
        return false;
    }

    public boolean filterWxSphBicTrade(Staff staff, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isWxSphBicTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("微信小店（微信视频号）质检订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,type=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getType()));
            return true;
        }
        return false;
    }

    public boolean filterSysExcept(Staff staff, TradeMergeContext mergeContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        //地址异常的订单不能合单 https://gykj.yuque.com/entavv/xb9xi5/sgrgqnd2rgu7wdhy
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.ADDRESS_CHANGED)) {
            errorSidMsgMap.computeIfAbsent("地址异常的订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,itemExcep=%s", trade.getSid(), trade.getTid(), trade.getItemExcep()));
            return true;
        }
        //pdd风控异常订单不参与自动合单
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RISK)) {
            errorSidMsgMap.computeIfAbsent("pdd风控异常订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,itemExcep=%s", trade.getSid(), trade.getTid(), trade.getItemExcep()));
            return true;
        }
        //分销商反审核异常
        if (TradeUtils.isUnAuditExcep(staff, trade)) {
            errorSidMsgMap.computeIfAbsent("分销商反审核异常订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,convertType=%s,destId=%s,belongType=%s,sourceId=%s,excep=%s", trade.getSid(), trade.getTid(), trade.getConvertType(), trade.getDestId(), trade.getBelongType(), trade.getSourceId(), trade.getExcep()));
            return true;
        }
        //黑名单订单不参与合单
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.BLACK_NICK)) {
            errorSidMsgMap.computeIfAbsent("黑名单订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,blackBuyerNick=%s", trade.getSid(), trade.getTid(), TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.BLACK_NICK)));
            return true;
        }
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PART_PAY_EXCEPT) || TradeExceptUtils.isSubTradeContainExcept(staff, trade, ExceptEnum.PART_PAY_EXCEPT)) {
            errorSidMsgMap.computeIfAbsent("部分付款异常订单不能合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        //奇门分销商未付款不参与合单
        if (TradeUtils.isQimenFxSource(trade) && (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_WAITPAY) || TradeExceptUtils.isSubTradeContainExcept(staff, trade, ExceptEnum.FX_WAITPAY))) {
            errorSidMsgMap.computeIfAbsent("所选订单有“分销商未付款”的异常，暂不支持合单，请付款后再来操作！", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        // 挂起异常
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.HALT) && !TradeMergeEnum.MERGE_MANUAL.equals(mergeContext.getTradeMergeEnum()) && !TradeMergeEnum.MERGE_NULTIL.equals(mergeContext.getTradeMergeEnum())) {
            errorSidMsgMap.computeIfAbsent("所选订单有\"挂起异常\"的异常，暂不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 异常过滤
     */
    public boolean filterExcept(Staff staff, TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        //异常过滤：自定义异常 & 系统异常
        if (context.getTradeMergeEnum() == TradeMergeEnum.MERGE_AUTO || context.getTradeMergeEnum() == TradeMergeEnum.MERGE_AUTO_MANUAL || context.getTradeMergeEnum() == TradeMergeEnum.MERGE_AUTO_NULTIL) {
            if (context.isMergeExcep()) {
                //设置了自定义异常，订单没有对应的自定义异常，不允许合并
                if (CollectionUtils.isNotEmpty(context.getSelfExcepSet()) && filterExceptSelf(staff, context.getSelfExcepSet(), trade, errorSidMsgMap)) {
                    return true;
                }
                //设置了系统异常，订单没有对应的系统异常，不允许合并
                else if (CollectionUtils.isNotEmpty(context.getSysExcepSet()) && filterExceptSys(staff, context, trade, errorSidMsgMap)) {
                    return true;
                }
                //设置了自定义异常，没有设置系统异常，订单有系统异常，不允许合并
                else if (CollectionUtils.isNotEmpty(context.getSelfExcepSet()) && CollectionUtils.isEmpty(context.getSysExcepSet()) && filterExceptSys(staff, context, trade, errorSidMsgMap)) {
                    return true;
                }
                //设置了系统异常，没有设置自定义异常，订单有自定义异常，不允许合并
                else if (CollectionUtils.isEmpty(context.getSelfExcepSet()) && CollectionUtils.isNotEmpty(context.getSysExcepSet()) && filterExceptSelf(staff, context.getSelfExcepSet(), trade, errorSidMsgMap)) {
                    return true;
                }
                //没有设置系统异常、没有设置自定义异常，订单有异常
                else if (CollectionUtils.isEmpty(context.getSelfExcepSet()) && CollectionUtils.isEmpty(context.getSysExcepSet()) && trade.getIsExcep() == 1) {
                    errorSidMsgMap.computeIfAbsent("异常单不支持合单（请检查合单配置）", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,isExcep=%s,confMergeExcep=%s", trade.getSid(), trade.getTid(), trade.getIsExcep(), context.isMergeExcep()));
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 自定义异常过滤
     */
    private boolean filterExceptSelf(Staff staff, Set<String> selfExcepSet, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        Set<String> excepIdsArray = TradeExceptUtils.getCustomExceptIdsStr(staff, trade);
        for (String excepId : excepIdsArray) {
            if (StringUtils.isNotBlank(excepId) && !selfExcepSet.contains(excepId)) {
                errorSidMsgMap.computeIfAbsent("此自定义异常单不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,trade.exceptIds=%s,configSelfExceptIds=%s", trade.getSid(), trade.getTid(), trade.getExceptIds(), selfExcepSet));
                return true;
            }
        }
        return false;
    }

    /**
     * 系统异常过滤
     */
    private boolean filterExceptSys(Staff staff, TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (_filterExceptSys(staff, context, trade, errorSidMsgMap)) {
            return true;
        }
        if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
            Map<Long, List<Trade>> mergeSidTradesMap = context.getMergeSidTradesMap();
            if (mergeSidTradesMap != null && !mergeSidTradesMap.isEmpty()) {
                List<Trade> trades = mergeSidTradesMap.get(trade.getMergeSid());
                if (CollectionUtils.isEmpty(trades)) {//未加锁之前有一次校验，这里可能数据会存在变化
                    Logs.ifDebug(LogHelper.buildLog(staff, String.format("合单异常过滤,主单找不到隐藏单,sid=%s,mergeSid=%s,mergeSids=%s", trade.getSid(), trade.getMergeSid(), mergeSidTradesMap.keySet())));
                } else {
                    for (Trade mergeTrade : trades) {
                        if (mergeTrade.getSid() - trade.getSid() != 0 && _filterExceptSys(staff, context, mergeTrade, errorSidMsgMap)) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * 系统异常过滤
     */
    private boolean _filterExceptSys(Staff staff, TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        Set<String> sysExcepSet = context.getSysExcepSet();
        if (!sysExcepSet.contains("2") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.REFUNDING)) {
            errorSidMsgMap.computeIfAbsent("退款中的订单单不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,trade.exceptIds=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), trade.getExceptIds(), sysExcepSet));
            return true;
        }

        //校验系统异常，大部分异常用主单的itemExcep校验即可
        int itemExcep = trade.getItemExcep();
        long excep = trade.getExcep();
        if (!TradeExceptUtils.isExcept(staff, trade) && trade.getIsExcep() == 0) {
            return false;
        }
        if (!sysExcepSet.contains("3") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UNALLOCATED)) {//商品未匹配
            errorSidMsgMap.computeIfAbsent("商品未匹配异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("4") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RELATION_CHANGED)) {//对应关系改动
            errorSidMsgMap.computeIfAbsent("对应关系改动异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("5") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.INSUFFICIENT)) {//库存不足
            errorSidMsgMap.computeIfAbsent("库存不足异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }

        if (!sysExcepSet.contains("7") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.ITEM_CHANGED)) {//平台更换商品
            errorSidMsgMap.computeIfAbsent("平台更换商品异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("8") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.SELLER_MEMO_UPDATE)) {//平台修改备注异常
            errorSidMsgMap.computeIfAbsent("平台修改备注异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("10") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.LOST_MSG)) {//订单信息缺失
            errorSidMsgMap.computeIfAbsent("订单信息缺失异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("12") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PART_REFUND)) {//部分关闭
            errorSidMsgMap.computeIfAbsent("部分关闭异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("11") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UNATTAINABLE)) {//快递停发异常
            errorSidMsgMap.computeIfAbsent("快递停发异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,unattainable=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UNATTAINABLE), sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("14") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.DELIVER_EXCEPT)) {
            errorSidMsgMap.computeIfAbsent("发货异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("15") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UPLOAD_EXCEPT)) {
            errorSidMsgMap.computeIfAbsent("上传异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,v=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), trade.getV(), sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("16") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.SUITE_CHANGE)) {
            errorSidMsgMap.computeIfAbsent("套件更改异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("17") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.COD_REPEAT)) {
            errorSidMsgMap.computeIfAbsent("货到付款重复订单异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("18") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PDD_STOCK_OUT)) {
            errorSidMsgMap.computeIfAbsent("pdd缺货已处理异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("19") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.WAITING_RETURN_WMS)) {
            errorSidMsgMap.computeIfAbsent("等待退货入仓异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("20") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.WAIT_MERGE)) {
            errorSidMsgMap.computeIfAbsent("等待合并异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }
        if (!sysExcepSet.contains("21") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.ITEM_SHUTOFF)) {
            errorSidMsgMap.computeIfAbsent("商品停用异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,itemExcep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), itemExcep, sysExcepSet));
            return true;
        }

        if (!sysExcepSet.contains("22") && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PLATFORM_WAREHOUSE_MATCH)) {
            errorSidMsgMap.computeIfAbsent("天猫物流升级平台仓映射异常不支持自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,excep=%s,configSysExceptIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), excep, sysExcepSet));
            return true;
        }
        return false;
    }

    /**
     * 商品设置过滤 https://gykj.yuque.com/entavv/xb9xi5/dnf01i
     */
    private boolean filterOuterId(TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        //跨店合单商品组合过滤 https://gykj.yuque.com/entavv/xb9xi5/xia0yba09cubx2fz
        Map<Integer, Set<String>> mergeOuterIdsMap = context.getMergeOuterIdsMap();
        if (MapUtils.isNotEmpty(mergeOuterIdsMap)){
            for (Map.Entry<Integer, Set<String>> entry : mergeOuterIdsMap.entrySet()) {
                //设置多规则中不同的商品排除或者指定类型
                context.setMergeSkuType(entry.getKey());
                boolean shouldFilter = doFilterMergeOuterId(context, trade, errorSidMsgMap, entry.getValue());
                if (shouldFilter) {
                    return true;
                }
            }
            return false;
        }else {
            Set<String> mergeOuterIds = context.getMergeOuterIds();
            return doFilterMergeOuterId(context, trade, errorSidMsgMap, mergeOuterIds);
        }
    }

    private static boolean doFilterMergeOuterId(TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap, Set<String> mergeOuterIds) {
        if (CollectionUtils.isNotEmpty(mergeOuterIds)) {
            int mergeSkuType = context.getMergeSkuType();
            Set<String> outerIds = TradeUtils.getOrders4Trade(trade).stream().filter(order -> !(order.isVirtual() || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()))).map(Order::getSysOuterId).collect(Collectors.toSet());
            //合单指定商品类型
            if (mergeSkuType == MergeConstant.MERGE_SKU_INCLUDE) {
                //只要订单有一个商品不在配置的商品里面，则不能合单
                if (CollectionUtils.isEmpty(outerIds) || !mergeOuterIds.containsAll(outerIds)) {
                    errorSidMsgMap.computeIfAbsent("订单商品不符合合单商品设置不能自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,outerIds=%s,mergeSkuType=%s,mergeOuterIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), outerIds, mergeSkuType, mergeOuterIds.size() > 20 ? "数量为" + mergeOuterIds.size() : mergeOuterIds));
                    return true;
                }
            } else if (mergeSkuType == MergeConstant.MERGE_SKU_EXCLUDE) {//合单排除指定商品类型
                if (outerIds.stream().anyMatch(mergeOuterIds::contains)) {
                    errorSidMsgMap.computeIfAbsent("订单商品不符合合单商品设置不能自动合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,outerIds=%s,mergeSkuType=%s,mergeOuterIds=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), outerIds, mergeSkuType, mergeOuterIds.size() > 20 ? "数量为" + mergeOuterIds.size() : mergeOuterIds));
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 只允许单SKU订单合单设置过滤
     */
    public boolean filterAllowSingleSku(Staff staff, TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (context.isAllowSingleSku()) {
            SingleSkuData singleSkuData = new SingleSkuData();
            if (filterAllowSingleSku(singleSkuData, trade, errorSidMsgMap)) {
                return true;
            }
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                List<Trade> mergeTrades = context.getMergeSidTradesMap().get(trade.getMergeSid());
                if (CollectionUtils.isEmpty(mergeTrades)) {
                    Logs.ifDebug(LogHelper.buildLog(staff, String.format("合单只允许单SKU订单合单设置过滤,主单找不到隐藏单,sid=%s,mergeSid=%s,mergeSids=%s", trade.getSid(), trade.getMergeSid(), context.getMergeSidTradesMap().keySet())));
                } else {
                    for (Trade mergeTrade : mergeTrades) {
                        if (mergeTrade.getMergeSid() - trade.getSid() != 0 && filterAllowSingleSku(singleSkuData, mergeTrade, errorSidMsgMap)) {
                            return true;
                        }
                    }
                }
            }
            if (singleSkuData.sku == null && singleSkuData.orderCount > 0 && singleSkuData.orderCount != singleSkuData.virtualCount) {
                errorSidMsgMap.computeIfAbsent("订单商品不符合只允许单SKU订单合单被过滤", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,sku=null,orderCount=%s,virtualCount=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), singleSkuData.orderCount, singleSkuData.virtualCount));
                return true;
            }
        }
        return false;
    }

    private boolean filterAllowSingleSku(SingleSkuData singleSkuData, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            //交易关闭的不判断
            if (!Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                if (order.getItemSysId() <= 0) {
                    errorSidMsgMap.computeIfAbsent("订单商品未匹配不符合只允许单SKU订单合单被过滤", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,orderId=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), order.getId()));
                    return true;
                } else {
                    singleSkuData.orderCount++;
                    if (order.isVirtual()) {
                        singleSkuData.virtualCount++;
                    } else {
                        String singleSku = order.getItemSysId() + "_" + NumberUtils.negative2Zero(order.getSkuSysId());
                        if (singleSkuData.sku == null) {
                            singleSkuData.sku = singleSku;
                        } else if (!StringUtils.equals(singleSkuData.sku, singleSku)) {
                            errorSidMsgMap.computeIfAbsent("订单商品不符合只允许单SKU订单合单被过滤", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,singleSku0=%s, singleSku1=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), singleSkuData.sku, singleSku));
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private boolean filterSplitTrade(Staff staff, TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        boolean mergeAllGiftSplitTrade = context.isMergeAllGiftSplitTrade();
        boolean mergeRefundSplitTrade = context.isMergeRefundSplitTrade();
        if (!mergeAllGiftSplitTrade && !mergeRefundSplitTrade) {
            return false;
        }
        Map<String, Map<Long, String>> errorMsgMap = new HashMap<>();
        boolean filterMergeRefundSplitTrade = filterMergeRefundSplitTrade(staff, context, trade, errorMsgMap);
        boolean filterMergeAllGiftSplitTrade = filterMergeAllGiftSplitTrade(staff, context, trade, errorMsgMap);
        if ((!mergeAllGiftSplitTrade && filterMergeRefundSplitTrade) || (!mergeRefundSplitTrade && filterMergeAllGiftSplitTrade)) {
            errorSidMsgMap.computeIfAbsent("非全是赠品或者非仅做过退款拆分的拆单不允许合单(可配置)", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,splitSid=%s filterMergeAllGiftSplitTrade=%s filterMergeRefundSplitTrade=%s 配置只支持自动合单", trade.getSid(), trade.getTid(), trade.getSplitSid(), filterMergeAllGiftSplitTrade, filterMergeRefundSplitTrade));
            if (MapUtils.isNotEmpty(errorMsgMap)) {
                Set<Map.Entry<String, Map<Long, String>>> entries = errorMsgMap.entrySet();
                for (Map.Entry<String, Map<Long, String>> entry : entries) {
                    errorSidMsgMap.computeIfAbsent(entry.getKey(), k -> new HashMap<>()).putAll(entry.getValue());
                }
            }
            return true;
        }
        // 全是赠品和退款任意符合都能合单
        if (filterMergeRefundSplitTrade && filterMergeAllGiftSplitTrade) {
            errorSidMsgMap.computeIfAbsent("非全是赠品或者非仅做过退款拆分的拆单不允许合单(可配置)", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,splitSid=%s 配置只支持自动合单", trade.getSid(), trade.getTid(), trade.getSplitSid()));
            if (MapUtils.isNotEmpty(errorMsgMap)) {
                Set<Map.Entry<String, Map<Long, String>>> entries = errorMsgMap.entrySet();
                for (Map.Entry<String, Map<Long, String>> entry : entries) {
                    errorSidMsgMap.computeIfAbsent(entry.getKey(), k -> new HashMap<>()).putAll(entry.getValue());
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 过滤退款过的订单
     *
     * @param staff
     * @param context
     * @param trade
     * @param errorMsgMap
     * @return
     */
    private boolean filterMergeRefundSplitTrade(Staff staff, TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorMsgMap) {
        boolean mergeRefundSplitTrade = context.isMergeRefundSplitTrade();
        Map<Long, List<TradeMergeSplitRecord>> tradeMergeSplitRecordMap = context.getTradeMergeSplitRecordMap();
        if (TradeMergeEnum.MERGE_AUTO != context.getTradeMergeEnum() || !mergeRefundSplitTrade || MapUtils.isEmpty(tradeMergeSplitRecordMap)) {
            return false;
        }
        List<TradeMergeSplitRecord> tradeMergeSplitRecords = tradeMergeSplitRecordMap.get(trade.getSid());
        if (trade.getSplitSid() < 0 || CollectionUtils.isEmpty(tradeMergeSplitRecords)) {
            return false;
        }
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        boolean allRefund = orders4Trade.stream().allMatch(RefundUtils::isRefundOrder);
        if (allRefund) {
            errorMsgMap.computeIfAbsent("非仅做过退款拆分的拆单不允许合单(可配置)", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,splitSid=%s,退款订单 配置只支持自动合单", trade.getSid(), trade.getTid(), trade.getSplitSid()));
            return true;
        }
        boolean mergeAllGiftSplitTrade = context.isMergeAllGiftSplitTrade();
        boolean allGift = orders4Trade.stream().allMatch(Order::isGift);
        if (CollectionUtils.isNotEmpty(orders4Trade) && allGift && mergeAllGiftSplitTrade) {
            return false;
        }
        Set<Integer> collect = tradeMergeSplitRecords.stream().map(TradeMergeSplitRecord::getSplitType).collect(Collectors.toSet());
        if (collect.size() == 1 && (collect.contains(TradeSplitEnum.SPLIT_REFUND.getType())
                || collect.contains(TradeSplitEnum.SPLIT_REFUND_AUTO.getType()))) {
            return false;
        }
        errorMsgMap.computeIfAbsent("非仅做过退款拆分的拆单不允许合单(可配置)", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,splitSid=%s,splitTyes=%s 配置只支持自动合单", trade.getSid(), trade.getTid(), trade.getSplitSid(), collect));
        return true;
    }

    private boolean filterMergeAllGiftSplitTrade(Staff staff, TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorMsgMap) {
        boolean mergeAllGiftSplitTrade = context.isMergeAllGiftSplitTrade();
        if (TradeMergeEnum.MERGE_AUTO != context.getTradeMergeEnum() || !mergeAllGiftSplitTrade) {
            return false;
        }
        if (trade.getSplitSid() < 0) {
            return false;
        }
        // 只有拆单才判断是否都是赠品
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        boolean allGift = orders4Trade.stream().allMatch(Order::isGift);
        if (allGift && CollectionUtils.isNotEmpty(orders4Trade)) {
            return false;
        }
        errorMsgMap.computeIfAbsent("非全是赠品的拆单不允许合单(可配置)", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,splitSid=%s,allGift=%s 配置只支持自动合单", trade.getSid(), trade.getTid(), trade.getSplitSid(), allGift));
        return true;
    }


    private static class SingleSkuData {
        private String sku;
        private int orderCount = 0;
        private int virtualCount = 0;
    }

    /**
     * 合单数量限制过滤
     * 订单是合单, 且数量已经超过限制, 同时订单下的tid与当前批次所有订单的tid不重复，则需要过滤
     */
    private boolean filterLimit(Staff staff, TradeMergeContext context, Trade trade, Set<String> duplicateTids, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (trade.getMergeSid() >= 0) {
            if (CollectionUtils.containsAny(duplicateTids, com.raycloud.dmj.domain.trade.utils.TradeUtils.getTids(staff, trade, context.getMergeSidTradesMap()))) {
                // 当前订单下的tid与当前批次下所有订单的tid存在重复，先不过滤（在这里过滤可能会不准确，放到后面 TradeMergeGroup.groupByMergeLimitNumber 分组去处理）
                return false;
            }
            return filterLimit(trade, context.getMergeLimitConfig(), errorSidMsgMap);
        }
        return false;
    }

    private boolean filterLimit(Trade trade, TradeMergeLimitConfig limitConfig, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (limitConfig != null) {
            Map<String, Integer> configTypeLimit = limitConfig.getTradeTypeLimit();
            if (configTypeLimit != null && !configTypeLimit.isEmpty()) {
                for (Map.Entry<String, Integer> typeLimit : configTypeLimit.entrySet()) {
                    Integer limitCount = typeLimit.getValue();
                    if (limitCount != null && limitCount > 0 && StringUtils.isNotBlank(typeLimit.getKey())) {
                        TradeTypeEnum typeEnum = TradeTypeEnum.getInstanceByCode(typeLimit.getKey());
                        if (trade.getMergeCount() >= limitCount && typeEnum != null && typeEnum.getTypeToConfirm().apply(trade)) {
                            errorSidMsgMap.computeIfAbsent(typeEnum.getName() + "类型合单数量超过限制订单被过滤不能合单(最多" + limitCount + "个)", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,mergeTrades.size=%s,typeName=%s,typeLimitCount=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), trade.getMergeCount(), typeEnum.getName(), limitCount));
                            return true;
                        }
                    }
                }
            }
            Map<String, Integer> configSourceLimit = limitConfig.getSourceLimit();
            if (configSourceLimit != null && !configSourceLimit.isEmpty()) {
                for (Map.Entry<String, Integer> sourceLimit : configSourceLimit.entrySet()) {
                    Integer limitCount = sourceLimit.getValue();
                    if (limitCount != null && limitCount > 0 && trade.getMergeCount() >= limitCount && StringUtils.isNotBlank(sourceLimit.getKey()) && sourceLimit.getKey().equalsIgnoreCase(trade.getSource())) {
                        errorSidMsgMap.computeIfAbsent("类型合单数量超过平台限制订单被过滤不能合单(" + PlatformSourceConstants.getSourceName(sourceLimit.getKey()) + "平台最多" + limitCount + "个)", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,mergeTrades.size=%s,source=%s, sourceLimitCount=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), trade.getMergeCount(), trade.getSource(), limitCount));
                        return true;
                    }
                }
            }
            if (limitConfig.getCompanyLimitNum() != null && limitConfig.getCompanyLimitNum() > 0 && trade.getMergeCount() >= limitConfig.getCompanyLimitNum()) {
                errorSidMsgMap.computeIfAbsent("类型合单数量超过限制订单被过滤不能合单(最多" + limitConfig.getCompanyLimitNum() + "个)", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,mergeSid=%s,mergeTrades.size=%s,source=%s, limitCount=%s", trade.getSid(), trade.getTid(), trade.getMergeSid(), trade.getMergeCount(), trade.getSource(), limitConfig.getCompanyLimitNum()));
                return true;
            }
        }
        return false;
    }

    /**
     * 合单规则开启允许系统预售合并普通单, 过滤没有锁定库存的系统预售单
     */
    private boolean filterSysPreSellForUnlockedInventory(TradeMergeContext context, Trade t, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isSysPresell(t) && Objects.equals(context.getTradeMergeConf().getMergeSysPresellWithNormal(), 1)) {
            if (!Trade.STOCK_STATUS_NORMAL.equals(t.getStockStatus())) {
                errorSidMsgMap.computeIfAbsent("合单规则开启允许系统预售合并普通单, 过滤没有锁定库存的系统预售单", k -> new HashMap<>()).put(t.getSid(), String.format("sid=%s,tid=%s,isPresell=%s,stockStatus=%s", t.getSid(), t.getTid(), t.getIsPresell(), t.getStockStatus()));
                return true;
            }
        }
        return false;
    }

    /**
     * 标签过滤
     */
    private boolean filterLabel(TradeMergeContext mergeContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        Set<Long> onlyMergeConfigTagIds = mergeContext.getOnlyMergeConfigTagIds();
        Set<Long> excludeMergeConfigTagIds = mergeContext.getExcludeMergeConfigTagIds();
        Set<Long> tradeLabelIds = ArrayUtils.toLongSet(trade.getTagIds());
        if (CollectionUtils.isNotEmpty(onlyMergeConfigTagIds)) {
            if (CollectionUtils.isEmpty(tradeLabelIds) && !onlyMergeConfigTagIds.contains(0L)) {
                errorSidMsgMap.computeIfAbsent("合单规则开启仅包含指定订单标签合单,订单没有标签且规则未勾选[订单无标签]被过滤", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,onlyMergeConfigTagIds=%s,tradeLabelIds=%s", trade.getSid(), trade.getTid(), onlyMergeConfigTagIds, tradeLabelIds));
                return true;
            }
            for (Long tradeLabelId : tradeLabelIds) {
                if (!onlyMergeConfigTagIds.contains(tradeLabelId)) {
                    errorSidMsgMap.computeIfAbsent("合单规则开启仅包含指定订单标签合单,订单有其他标签被过滤", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,onlyMergeConfigTagIds=%s,tradeLabelIds=%s", trade.getSid(), trade.getTid(), onlyMergeConfigTagIds, tradeLabelIds));
                    return true;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(excludeMergeConfigTagIds)) {
            if (CollectionUtils.isEmpty(tradeLabelIds) && excludeMergeConfigTagIds.contains(0L)){
                errorSidMsgMap.computeIfAbsent("合单规则开启排除指定订单标签合单,订单没有标签且规则勾选[订单无标签]被过滤", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,excludeMergeConfigTagIds=%s,tradeLabelIds=%s", trade.getSid(), trade.getTid(), excludeMergeConfigTagIds, tradeLabelIds));
                return true;
            }
            if (CollectionUtils.containsAny(excludeMergeConfigTagIds, tradeLabelIds)){
                errorSidMsgMap.computeIfAbsent("合单规则开启排除指定订单标签合单,订单有包含配置的标签被过滤", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,excludeMergeConfigTagIds=%s,tradeLabelIds=%s", trade.getSid(), trade.getTid(), excludeMergeConfigTagIds, tradeLabelIds));
                return true;
            }
        }
        return false;
    }

    /**
     * 跨周: 本周五 下周一
     * 不是相隔时间超过7天
     */
    private void validateExistAcrossWeeks(TradeMergeContext mergeContext, List<Trade> trades) {
        if (trades == null || trades.isEmpty()) {
            return;
        }

        if (!Objects.equals(mergeContext.getTradeMergeEnum(), TradeMergeEnum.MERGE_MANUAL) &&
                containAcrossWeeks(trades)
        ) {
            throw new IllegalArgumentException("1688订单存在跨周合单");
        }
    }

    private boolean containAcrossWeeks(List<Trade> trades) {
        if (trades == null || trades.isEmpty()) {
            return false;
        }

        Map<String, List<Trade>> sourceTots = new HashMap<>();
        for (Trade trade : trades) {
            sourceTots.computeIfAbsent(trade.getSource(), t -> new ArrayList<>()).add(trade);
        }

        Date dbDefaultPayTime = new Date(946656000000L);
        boolean existAcrossWeeks = false;
        for (String k : sourceTots.keySet()) {
            if (existAcrossWeeks) {
                break;
            }
            if (!k.equals(CommonConstants.PLAT_FORM_TYPE_1688)) {
                continue;
            }

            List<Trade> ts = sourceTots.get(k);

            long min = Long.MAX_VALUE, max = 0;
            for (Trade t : ts) {
                Date payTime = t.getPayTime();
                if (payTime == null || dbDefaultPayTime.equals(payTime)) {
                    continue;
                }

                min = Math.min(min, payTime.getTime());
                max = Math.max(max, payTime.getTime());
            }

            if (max == Long.MAX_VALUE || max == 0) {
                continue;
            }

            Calendar minCalendar = Calendar.getInstance(), maxCalendar = Calendar.getInstance();
            minCalendar.setTimeInMillis(min);
            maxCalendar.setTimeInMillis(max);

            existAcrossWeeks = maxCalendar.get(Calendar.YEAR) != minCalendar.get(Calendar.YEAR) ||
                    maxCalendar.get(Calendar.WEEK_OF_YEAR) != minCalendar.get(Calendar.WEEK_OF_YEAR);
        }
        return existAcrossWeeks;
    }

    /**
     * 过滤花城农夫帮卖订单
     */
    public boolean filterNfgoodHelpSell(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isNfgoodHelpSellTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("花城农夫帮卖订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,subSource=%s,type=%s", trade.getSid(), trade.getTid(), trade.getSubSource(), trade.getType()));
            return true;
        }
        return false;
    }


    /**
     * 过滤刷单
     */
    public boolean filterFakeTrade(Staff staff, TradeMergeContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        FilterResult filterResult = com.raycloud.dmj.domain.trade.utils.TradeUtils.isFakeTrade(staff, trade, context.getTradeFakeRule());
        if (filterResult.isFilter()) {
            errorSidMsgMap.computeIfAbsent("刷单的订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,msg=%s", trade.getSid(), trade.getTid(), filterResult.getMsg()));
            return true;
        }
        return false;
    }


    public boolean filterZAMY(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_ZAMY.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("挚爱母婴平台订单不支持合单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,type=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getType()));
            return true;
        }
        return false;
    }
}

package com.raycloud.dmj.business.split.support;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.payment.support.PaymentCalculateSupports;
import com.raycloud.dmj.business.payment.support.share.ShareInOrders4Combine;
import com.raycloud.dmj.business.split.SplitUtils;
import com.raycloud.dmj.business.stock.TradeConfigStockBusiness;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeExtraFieldEnum;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.payment.util.BigDecimalWrapper;
import com.raycloud.dmj.domain.trades.request.TradeTimeoutContext;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * <p>
 * 18/11/24
 */
public class SplitUndoUtils {

    private static final Logger logger = Logger.getLogger(SplitUndoUtils.class);

    /**
     * 校验订单，需要将非待审核的订单过滤掉
     */
    public static List<Trade> validateAndFilterTrades(Staff staff, List<TbTrade> splitTrades) {
        List<Trade> validates = new ArrayList<>();
        Set<Long> warehouseIds = new HashSet<>();
        Set<Integer> scalpings = new HashSet<>();
        for (TbTrade splitTrade : splitTrades) {
            //合单订单、非待审核、作废的订单直接过滤
            if (TradeUtils.isMerge(splitTrade) || !TradeStatusUtils.isWaitAudit(splitTrade.getSysStatus()) || splitTrade.getIsCancel() == 1L) {
                logger.warn(LogHelper.buildLogHead(staff).append(String.format("合单、非待审核、作废订单不能取消拆单[sid=%s,splitSid=%s,mergeSid=%s,sysStatus=%s,isCancel=%s]", splitTrade.getSid(), splitTrade.getSplitSid(), splitTrade.getMergeSid(), splitTrade.getSysStatus(), splitTrade.getIsCancel())));
                continue;
            }
            //校验
            Assert.isTrue(TradeUtils.isSplit(splitTrade), String.format("订单已取消拆单,请刷新页面再操作[平台订单号=%s]", splitTrade.getTid()));
            Assert.isTrue(!TradeExceptUtils.isContainExcept(staff,splitTrade, ExceptEnum.HALT), String.format("挂起拆单不能取消拆单[系统订单号=%s,平台订单号=%s]", splitTrade.getSid(), splitTrade.getTid()));
            Assert.isTrue(!splitTrade.isPresellTrade(), String.format("拆分的订单中含有未解锁的预售订单，无法取消拆单。以下预售订单仍未解锁，请先解锁后再取消拆单，系统单号：%s", splitTrade.getSid()));
            Assert.isTrue(!TradeUtils.isPlatformFxTrade(splitTrade), String.format("拆分的订单中含有平台分销订单，不能取消拆单[系统订单号=%s,平台订单号=%s]", splitTrade.getSid(), splitTrade.getTid()));
            Assert.isTrue(!TradeExceptUtils.isContainExcept(staff, splitTrade, ExceptEnum.PART_PAY_EXCEPT), "订单有部分付款异常，不可执行拆合单、取消拆单/合单，请处理异常后重试!");
            Assert.isTrue(!(TradeUtils.isQimenFxSource(splitTrade) && TradeExceptUtils.isContainExcept(staff, splitTrade, ExceptEnum.FX_WAITPAY)), "子单“分销商未付款”的异常，暂不支持取消拆单，请付款后再来操作!");
            Assert.isTrue(!PlatformUtils.isSmtCfCreatedShippingOrder(splitTrade.getTradeExt(), splitTrade.getSource()), "速卖通仓发备货单已创建发货单，请取消发货单后再操作!");

            warehouseIds.add(splitTrade.getWarehouseId());
            scalpings.add(splitTrade.getScalping());
            validates.add(splitTrade);
        }
        Assert.isTrue(validates.size() > 1, "待审核订单少于一笔，不能取消拆单(合单订单不能取消拆单)！");
        Assert.isTrue(warehouseIds.size() == 1, "不同仓库的订单不能取消拆单！");
        Assert.isTrue(scalpings.size() == 1, "存在部分空单，部分非空单，不允许取消拆单！");
        return validates;
    }

    public static List<Trade> validateNonConsignTrade(Staff staff, List<Trade> trades, List<Order> orders, boolean isAssert) {
        if (CollectionUtils.isEmpty(trades) || CollectionUtils.isEmpty(orders)) {
            return Lists.newArrayList();
        }
        List<Trade> ignoreTrades = Lists.newArrayList();
        Map<Long/*sid*/, List<Order>> sid2orders = orders.stream().collect(Collectors.groupingBy(Order::getSid));
        trades.stream().collect(Collectors.groupingBy(Trade::getSplitSid)).forEach((sSid, sTrades) -> {
            List<Order> allOrders = sTrades.stream().map(Trade::getSid).map(sid2orders::get).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
            Set<String> conflict = getNonConsignSplitConflictOrderInfo(allOrders);
            if (CollectionUtils.isEmpty(conflict)) {
                return;
            }
            if (isAssert) {
                throw new IllegalArgumentException(String.format("商品%s标记了无需发货，不能取消拆单", conflict));
            }
            ignoreTrades.addAll(sTrades);
        });
        if (CollectionUtils.isNotEmpty(ignoreTrades)) {
            Logs.info(LogHelper.buildLog(staff, String.format("商品标记了无需发货，不能取消拆单: %s", ignoreTrades.stream().map(Trade::getSid).collect(Collectors.toSet()))));
            trades.removeAll(ignoreTrades);
        }
        return ignoreTrades;
    }

    // 如果相同的oid，相同source，一个是无需发货，一个是非无需发货，不允许拆单
    public static Set<String> getNonConsignSplitConflictOrderInfo(List<Order> orders) {
        List<String/*source-oid*/> nonConsignOids = orders.stream()
                .filter(Order::ifNonConsign)
                .map(o -> String.join("-", o.getSource(), o.getOid() + ""))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nonConsignOids)) {
            return Sets.newHashSet();
        }

        return orders.stream()
                .filter(o -> !o.ifNonConsign())
                .filter(o -> nonConsignOids.contains(String.join("-", o.getSource(), o.getOid() + "")))
                .map(Order::getSysOuterId)
                .collect(Collectors.toSet());
    }


    public static Trade splitUndo(Staff staff, SplitUpdateData splitUpdateData, SplitStockData splitStockData, List<TbTrade> originTrades, List<Trade> originSplitTrades, List<Order> originSplitOrders, TradeConfig tradeConfig, TradeTimeoutContext tradeTimeoutContext) {
        TradeConfigStockBusiness.fillOrderScalping(staff, originTrades, originSplitOrders);

        boolean splitAll = originSplitTrades.size() == originTrades.size();
        // 原始需要取消拆单的sid
        List<Long> originSplitSids = TradeUtils.toSidList(originSplitTrades);
        //订单取消拆单
        Trade mainTrade = SplitUndoUtils.splitUndoTrade(staff,originSplitTrades, splitAll);
        //子订单取消拆单
        List<Order> splitUndoOrders = new ArrayList<>();
        List<Order> updateOrders = new ArrayList<>();
        //按商品种类分组
        Map<String, List<Order>> itemKeyOrderMap = new HashMap<>();
        for (Order splitOrder : originSplitOrders) {
            if (TradeStatusUtils.isAfterSendGoods(splitOrder.getSysStatus())) {//已发货的订单
                splitUndoOrders.add(splitOrder);
                splitOrder.setSid(mainTrade.getSid());
                // order的异常要复制过来
                OrderExceptUtils.copyOrderExcept(staff,TradeExceptUtils.getTradeExceptData(mainTrade),splitOrder,splitOrder);
                splitOrder.setExceptData(TradeExceptUtils.getTradeExceptData(mainTrade));
                splitOrder.setSubTradeExceptDatas(mainTrade.getSubTradeExceptDatas());
                updateOrders.add(splitOrder);
                List<Order> splitSuits = splitOrder.getSuits();
                if (splitSuits == null || splitSuits.size() == 0) {
                    continue;
                }
                for (Order splitSuit : splitSuits) {
                    splitSuit.setSid(mainTrade.getSid());
                    OrderExceptUtils.copyOrderExcept(staff,TradeExceptUtils.getTradeExceptData(mainTrade),splitSuit,splitSuit);
                    splitSuit.setExceptData(TradeExceptUtils.getTradeExceptData(mainTrade));
                    splitSuit.setSubTradeExceptDatas(mainTrade.getSubTradeExceptDatas());
                }
            } else {
                itemKeyOrderMap.computeIfAbsent(getItemKey(splitOrder), k -> new ArrayList<>()).add(splitOrder);
            }
            // 取消拆单order的sid会改变
            Set<Long> orderExceptIds = OrderExceptUtils.getOrderExceptIds(staff, splitOrder);
            splitOrder.setOrderExceptIds(orderExceptIds);
            List<Order> suits = splitOrder.getSuits();
            if(!org.springframework.util.CollectionUtils.isEmpty(suits)){
                for(Order suit:suits){
                    Set<Long> orderSuitExceptIds = OrderExceptUtils.getOrderExceptIds(staff, suit);
                    suit.setOrderExceptIds(orderSuitExceptIds);
                    OrderExceptUtils.clearOrderExcept(staff,suit);
                }
            }
        }

        Map<String, List<Order>> itemOrderMap = new HashMap<>();

        for (Map.Entry<String, List<Order>> entry : itemKeyOrderMap.entrySet()) {
            List<Order> list = entry.getValue();
            if (list.size() == 1) {
                Order order = list.get(0);
                if (order.getSid().longValue() == mainTrade.getSid().longValue()) {
                    splitUndoOrders.add(order);
                    continue;
                }
            }
            itemOrderMap.put(entry.getKey(), list);
        }

        //设置原库存申请记录
        splitStockData.addOriginRecords(staff,getOriginOrders(itemOrderMap), mainTrade, tradeConfig);

        //子订单取消拆单
        splitUndoOrder(staff,itemOrderMap, mainTrade, splitUpdateData);
        List<Order> newOrders = SplitUndoUtils.getNewOrders(itemOrderMap);

        //设置新的库存申请记录
        splitStockData.addNewRecords(staff,newOrders, mainTrade, tradeConfig);
        //对拆分后的订单和子订单进行组装
        splitUndoOrders.addAll(newOrders);
        TradeUtils.setOrders(mainTrade, splitUndoOrders);


        TradeEsConTimeUtils.handleTimeoutActionTime(tradeTimeoutContext, mainTrade);
        // 将旧的异常合并到新的单上,只合单能取消拆单的异常
        List<TbTrade> collect = originTrades.stream().filter(trade -> originSplitSids.contains(trade.getSid())).collect(Collectors.toList());
        TradeExceptUtils.splitUndoExcept(staff,collect,mainTrade);
        TradeExceptUtils.resetTradeExceptByOrder(staff, mainTrade, ExceptEnum.RELATION_CHANGED, ExceptEnum.ITEM_CHANGED, ExceptEnum.ITEM_SHUTOFF, ExceptEnum.ITEM_PROCESS,ExceptEnum.GX_ITEM_CHANGE_EXCEPT,ExceptEnum.REFUND_ITEM_NUM_EXCEPT, ExceptEnum.EXCHANGE_ITEM_PLAT_MODIFY_EXCEPT,ExceptEnum.SMALL_REFUND_EXCEPT);
        SplitUtils.resetSplitTrade(staff, mainTrade, tradeConfig);
        List<Order> originOrders = SplitUndoUtils.getOriginOrders(itemOrderMap);
        if(CollectionUtils.isNotEmpty(originOrders)){
            updateOrders.addAll(originOrders);
        }

        splitUpdateData.addUpdateTrades(staff,originSplitTrades, tradeTimeoutContext).addUpdateOrders(staff,updateOrders);
        //承诺时间在计算好之后在mainTrade中,这里要重新赋值到originSplitTrades,originSplitTrades才是后面会更新到数据库中的对象
        if (CommonConstants.PLAT_FORM_TYPE_FXG.equals(mainTrade.getSource())) {
            originSplitTrades.forEach(trade -> trade.setTimeoutActionTime(mainTrade.getTimeoutActionTime()));
        }
        return mainTrade;
    }

    /**
     * 订单取消拆分
     */
    private static Trade splitUndoTrade(Staff staff,List<Trade> splitTrades, boolean splitUndoAll) {
        Trade mainTrade = null;
        Set<String> tagIds = new HashSet<>();
        Set<Long> exceptIds = new HashSet<>();
        BigDecimalWrapper actualPostFee = new BigDecimalWrapper();
        BigDecimalWrapper postFee = new BigDecimalWrapper();
        // 取消拆单需要重新计算主单理论运费
        BigDecimalWrapper theoryPostFee = new BigDecimalWrapper();
        BigDecimalWrapper discountFee = new BigDecimalWrapper();
        int printCount = 0;
        BigDecimalWrapper acPayment = new BigDecimalWrapper();
        Double allInsuranceCost = null;
        BigDecimalWrapper payAmount = new BigDecimalWrapper();
        BigDecimalWrapper selfBuiltDepositAmountWrapper = null;
        BigDecimalWrapper selfBuiltPaymentReceivableWrapper = null;
        double platformDiscountFee = 0.0;
        boolean ifKmtDf = true;
        Integer isPresell = null;
        //寻找主单，并将订单设置为隐藏(逻辑删除)
        for (Trade trade : splitTrades) {
            if (trade.getIsPresell() != null && trade.getIsPresell() == 3) {
                isPresell = 3;
            }
            trade.setEnableStatus(0);
            if (trade.getSplitSid().longValue() == trade.getSid().longValue()) {
                mainTrade = trade;
            }
            if (StringUtils.isNotBlank(trade.getTagIds()) && StringUtils.isNotBlank(trade.getTagIds().replaceAll(",", ""))) {
                tagIds.addAll(Strings.getAsStringSet(trade.getTagIds(), ",", true));
            }
            Set<Long> tradeExceptIds = TradeExceptUtils.getTradeExceptData(trade).getExceptIds();
            if(CollectionUtils.isNotEmpty(tradeExceptIds)){
                exceptIds.addAll(tradeExceptIds);
            }

            if (StringUtils.isNotBlank(trade.getActualPostFee())) {
                actualPostFee.add(trade.getActualPostFee());
                trade.setActualPostFee("0.00");
            }

            if(StringUtils.isNotBlank(trade.getAcPayment())) {
                acPayment.add(trade.getAcPayment());
            }
            if(StringUtils.isNotBlank(trade.getPayAmount())) {
                payAmount.add(trade.getPayAmount());
                Logs.debug(String.format("取消拆单主单维度 sid:[{%s}] tradePayAmount:[{%s}]", trade.getSid(),  payAmount));
            }
            platformDiscountFee += NumberUtils.str2Double(trade.getPlatformDiscountFee(), 0D);
            if (StringUtils.isNotBlank(trade.getPostFee())) {
                postFee.add(trade.getPostFee());
                trade.setPostFee("0.00");
            }

            if (trade.getTheoryPostFee() != null && trade.getTheoryPostFee() >= 0) {
                theoryPostFee.add(trade.getTheoryPostFee());
                trade.setTheoryPostFee(0.00D);
            }

            if (StringUtils.isNotBlank(trade.getDiscountFee())) {
                discountFee.add(trade.getDiscountFee());
                trade.setDiscountFee("0.00");
            }
            if (trade.getPrintCount() > printCount) {
                printCount = trade.getPrintCount();
            }
            if (TradeTypeUtils.isFxgBtasTrade(trade)){
                allInsuranceCost = TradeExtUtils.countInsuranceCost(allInsuranceCost, TradeExtUtils.getInsuranceCost(trade.getTradeExt()));
            }
            if(ifKmtDf && !TradeUtils.isContainV(trade, TradeConstants.V_IF_KMT_DF)){
                ifKmtDf = false;
            }
            if (trade.getTradeExt() != null && TradeExtUtils.ifSelfBuiltDepositAmount(trade.getTradeExt())) {
                String selfBuiltPaymentReceivable = TradeExtUtils.getSelfBuiltPaymentReceivable(trade.getTradeExt());
                String selfBuiltDepositAmount = TradeExtUtils.getSelfBuiltDepositAmount(trade.getTradeExt());
                if (StringUtils.isNotEmpty(selfBuiltDepositAmount)) {
                    if (selfBuiltDepositAmountWrapper == null) {
                        selfBuiltDepositAmountWrapper = new BigDecimalWrapper();
                    }
                    selfBuiltDepositAmountWrapper.add(selfBuiltDepositAmount);
                    TradeExtUtils.setExtraFieldValue(trade.getTradeExt(),TradeExtraFieldEnum.SELF_BUILT_DEPOSIT_AMOUNT.getField(), "");
                }
                if (StringUtils.isNotEmpty(selfBuiltPaymentReceivable)) {
                    if (selfBuiltPaymentReceivableWrapper == null) {
                        selfBuiltPaymentReceivableWrapper = new BigDecimalWrapper();
                    }
                    selfBuiltPaymentReceivableWrapper.add(selfBuiltPaymentReceivable);
                    TradeExtUtils.setExtraFieldValue(trade.getTradeExt(),TradeExtraFieldEnum.SELF_BUILT_PAYMENT_RECEIVABLE.getField(), "");
                }
            }
        }
        //找不到主单，则需要寻找一个伪主单
        //找到第一个平台订单为伪主单
        if (mainTrade == null) {
            for (Trade trade : splitTrades) {
                if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
                    mainTrade = trade;
                    break;
                }
            }
        }
        //没有平台订单，则取第一个订单为主单
        if (mainTrade == null) {
            mainTrade = splitTrades.get(0);
        }

        //主单设置为显示
        mainTrade.setEnableStatus(1);
        mainTrade.setTagIds(Strings.join(",", tagIds));
     //   mainTrade.setExceptIds(Strings.join(",", exceptIds));
     //   TradeExceptUtils.updateCustomExcept(staff,mainTrade,Strings.join(",", exceptIds));
        for(Long exceptId:exceptIds){
            TradeExceptUtils.updateExcept(staff,mainTrade,exceptId,1L);
        }
        mainTrade.setActualPostFee(actualPostFee.getString());
        mainTrade.setPostFee(postFee.getString());
        mainTrade.setTheoryPostFee(theoryPostFee.getDouble());
        mainTrade.setAcPayment(acPayment.getString());
        mainTrade.setPayAmount(payAmount.getString());
        mainTrade.setPlatformDiscountFee(NumberUtils.double2Str(platformDiscountFee));
        mainTrade.setDiscountFee(discountFee.getString());
        mainTrade.setPrintCount(printCount);
        //如果是全取消拆单，则需要将拆单标记设置为正常（取消拆单）
        if (splitUndoAll) {
            mainTrade.setSplitType(TradeSplitEnum.SPLIT_UNDO.getDbType());
            mainTrade.setSplitSid(-1L);
        }
        //取消拆单时,取消地址已处理标记
        mainTrade.removeV(2);
        if(!ifKmtDf){
            mainTrade.removeV(TradeConstants.V_IF_KMT_DF);
        }
        //BTAS订单重新设置订单保价
        if (TradeTypeUtils.isFxgBtasTrade(mainTrade)){
            mainTrade.setTradeExt(TradeExtUtils.setExtraFieldValue(mainTrade.getTradeExt(), TradeExtraFieldEnum.INSURED_PRICE.getField(), allInsuranceCost));
        }
        //开放平台/手工单重算定金/代收金额
        if (selfBuiltDepositAmountWrapper != null || selfBuiltPaymentReceivableWrapper != null) {
            if (mainTrade.getTradeExt() == null) {
                TradeExt tradeExt = new TradeExt();
                mainTrade.setTradeExt(tradeExt);
                tradeExt.setUserId(mainTrade.getUserId());
                tradeExt.setTid(mainTrade.getTid());
                tradeExt.setCompanyId(mainTrade.getCompanyId());
            }
            if (selfBuiltDepositAmountWrapper != null) {
                mainTrade.setTradeExt(TradeExtUtils.setExtraFieldValue(mainTrade.getTradeExt(), TradeExtraFieldEnum.SELF_BUILT_DEPOSIT_AMOUNT.getField(), selfBuiltDepositAmountWrapper.getString()));
            }
            if (selfBuiltPaymentReceivableWrapper != null) {
                mainTrade.setTradeExt(TradeExtUtils.setExtraFieldValue(mainTrade.getTradeExt(), TradeExtraFieldEnum.SELF_BUILT_PAYMENT_RECEIVABLE.getField(), selfBuiltPaymentReceivableWrapper.getString()));
            }
        }
        if (isPresell != null) {
            mainTrade.setIsPresell(isPresell);
        }
        return mainTrade;
    }

    /**
     * 子订单取消拆单
     */
    private static void splitUndoOrder(Staff staff,Map<String, List<Order>> itemOrderMap, Trade mainTrade, SplitUpdateData splitUpdateData) {
        for (Map.Entry<String, List<Order>> entry : itemOrderMap.entrySet()) {
            List<Order> splitOrders = entry.getValue();
            Order mainOrder = null;
            //寻找平台子订单为主子订单
            int num = 0;
            int stockNum = 0;
            BigDecimalWrapper totalPayment = new BigDecimalWrapper();
            BigDecimalWrapper totalPayAmount = new BigDecimalWrapper();
            BigDecimalWrapper totalDivideOrderFee = new BigDecimalWrapper();
            BigDecimalWrapper totalPlatformDiscountFee = new BigDecimalWrapper(0D);

            Map<String, Order> split_suit_map = new HashMap<>();
            //BTAS订单OrderExt的处理
            List<OrderExt> splitOrderExts = new ArrayList<>();
            List<OrderExt> suitOrderExts = new ArrayList<>();
            for (Order splitOrder : splitOrders) {
                splitOrder.setEnableStatus(0);
                num += splitOrder.getNum();
                stockNum += splitOrder.getStockNum() == null ? 0 : splitOrder.getStockNum();
                totalPayment.add(splitOrder.getPayment());
                Logs.debug(String.format("取消拆单子单维度order sid:[{%s}] orderPayAmount:[{%s}]", mainTrade.getSid(),  splitOrder.getPayAmount()));
                totalPayAmount.add(splitOrder.getPayAmount());
                totalDivideOrderFee.add(splitOrder.getDivideOrderFee());
                totalPlatformDiscountFee.add(MathUtils.toBigDecimal(splitOrder.getPlatformDiscountFee()));
                splitOrderExts.add(splitOrder.getOrderExt());

                if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(splitOrder.getSource())) {
                    mainOrder = splitOrder;
                }
                //处理套件
                List<Order> splitSuits = splitOrder.getSuits();
                if (splitSuits == null || splitSuits.size() == 0) {
                    continue;
                }
                for (Order splitSuit : splitSuits) {
                    splitSuit.setEnableStatus(0);
                    suitOrderExts.add(splitSuit.getOrderExt());
                    String key = getItemKey(splitSuit);
                    Order suit = split_suit_map.get(key);
                    if (suit == null) {
                        split_suit_map.put(key, splitSuit);
                        continue;
                    }
                    suit.setNum(suit.getNum() + splitSuit.getNum());
                    suit.setStockNum(suit.getStockNum() + splitSuit.getStockNum());
                }
            }
            Logs.debug(String.format("取消拆单子单维度 sid:[{%s}] tradePayAmount:[{%s}]", mainTrade.getSid(),  totalPayAmount));
            //找不到平台子订单，已第一个子订单为主子订单
            if (mainOrder == null) {
                mainOrder = splitOrders.get(0);
            }
            setSplitUndoOrder(staff, mainOrder, num, stockNum, totalPayment.getString(),totalPayAmount.getString(), totalPlatformDiscountFee.getString(), mainTrade.getSid(), splitOrderExts);

            mainOrder.setDivideOrderFee(totalDivideOrderFee.getString());
            //BTAS订单取消合单时，OrderExt的信息需要更新/ 速卖通合单时，需要更新OrderExt
            if (Objects.nonNull(mainOrder.getOrderExt())) {
                mainOrder.getOrderExt().setSid(mainTrade.getSid());
                splitUpdateData.updateOrderExt.add(mainOrder.getOrderExt());
            }
            if (split_suit_map.size() > 0) {
                List<Order> mainSuits = mainOrder.getSuits();
                for (Order mainSuit : mainSuits) {
                    String key = getItemKey(mainSuit);
                    //主子订单下单品需要显示
                    Order splitSuit = split_suit_map.get(key);
                    Assert.notNull(splitSuit, String.format("套件下的单品找不到，不能取消拆单[系统订单号=%s,子订单号=%s]", mainOrder.getSid(), mainOrder.getId()));
                    setSplitUndoOrder(staff, mainSuit, splitSuit.getNum(), splitSuit.getStockNum(), splitSuit.getPayment(), splitSuit.getPayAmount(), splitSuit.getPlatformDiscountFee(), mainTrade.getSid(), suitOrderExts);
                    if (CollectionUtils.isNotEmpty(OrderUtils.getBtasOrderCode(mainSuit))  || CommonConstants.PLAT_FORM_TYPE_SMT.equals(mainOrder.getSource())){
                        mainSuit.getOrderExt().setSid(mainSuit.getSid());
                        splitUpdateData.updateOrderExt.add(mainSuit.getOrderExt());
                    }
                }
                ShareInOrders4Combine shareInOrders4Combine = PaymentCalculateSupports.getInstance().getShareInOrders4Combine();
                shareInOrders4Combine.share(staff,mainTrade.getSid(),mainOrder,mainSuits);
            }
        }
    }

    private static void setSplitUndoOrder(Staff staff, Order mainOrder, int num, int stockNum, String totalPayment, String totalPayAmount, String totalPlatformDiscountFee, Long sid, List<OrderExt> orderExts) {
        // 原单的异常
        mainOrder.setSid(sid);
        mainOrder.setEnableStatus(1);
        mainOrder.setNum(num);
        mainOrder.setStockNum(stockNum);
        mainOrder.setSaleFee(mainOrder.getSalePrice() == null ? "0" : new BigDecimal(mainOrder.getSalePrice()).multiply(new BigDecimal(mainOrder.getNum() == null ? 0 : mainOrder.getNum())) + "");
        if (TradeStatusUtils.isAfterSendGoods(mainOrder.getSysStatus())) {
           // mainOrder.setStockStatus(Trade.STOCK_STATUS_NORMAL);
           OrderExceptUtils.setStockStatus(staff,mainOrder,Trade.STOCK_STATUS_NORMAL);
        } else {
             String stockStatus = mainOrder.getItemSysId() > 0 ? TradeStockUtils.getStockStatusStr(stockNum, num) : Trade.STOCK_STATUS_UNALLOCATED;
             OrderExceptUtils.setStockStatus(staff,mainOrder,stockStatus);
        }
        mainOrder.setPayment(totalPayment);
        mainOrder.setPayAmount(totalPayAmount);
        mainOrder.setPlatformDiscountFee(totalPlatformDiscountFee);
        //btas订单取消拆单时，订单码的合并
        undoSplitOrderExt(mainOrder, orderExts);
    }

    private static void undoSplitOrderExt(Order mainOrder, List<OrderExt> orderExts){
        if (mainOrder.getOrderExt() == null || CollectionUtils.isEmpty(orderExts)){
            return;
        }
       try {
           //订单码、质检结果处理
           List<String> mainBtasCodes = new ArrayList<>();
           Map<String, String> mainPictureQualityResult = new HashMap<>();
           Map<String, String> mainObjectQualityStatus = new HashMap<>();
           for (OrderExt orderExt : orderExts){
               if (null == orderExt || StringUtils.isEmpty(orderExt.getCustomization())){
                   continue;
               }
               JSONObject jsonObject = JSONObject.parseObject(orderExt.getCustomization());
               JSONArray codeList = jsonObject.getJSONArray(CommonConstants.BTAS_ORDER_CODE);
               mainBtasCodes.addAll(null == codeList ? new ArrayList<>() : codeList.toJavaList(String.class));
               Map<String, String> picRes = jsonObject.getObject(CommonConstants.BTAS_PICTURE_QUALITY_RESULT,Map.class);
               mainPictureQualityResult.putAll(null == picRes ? new HashMap<>() : picRes);
               Map<String, String> objectQualityStatus = jsonObject.getObject(CommonConstants.BTAS_OBJECT_QUALITY_STATUS, Map.class);
               mainObjectQualityStatus.putAll(null == objectQualityStatus ? new HashMap<>() : objectQualityStatus);
           }
           JSONObject mainJsonObject = JSONObject.parseObject(mainOrder.getOrderExt().getCustomization());
           if (null == mainJsonObject){
               mainJsonObject = new JSONObject();
           }
           if (CollectionUtils.isNotEmpty(mainBtasCodes)){
               mainJsonObject.put(CommonConstants.BTAS_ORDER_CODE, mainBtasCodes);
           }
           if (MapUtils.isNotEmpty(mainPictureQualityResult)){
               mainJsonObject.put(CommonConstants.BTAS_PICTURE_QUALITY_RESULT, mainPictureQualityResult);
           }
           if (MapUtils.isNotEmpty(mainObjectQualityStatus)){
               mainJsonObject.put(CommonConstants.BTAS_OBJECT_QUALITY_STATUS, mainObjectQualityStatus);
           }
           if (!mainJsonObject.isEmpty()){
               mainOrder.getOrderExt().setCustomization(JSONObject.toJSONString(mainJsonObject));
           }
       }catch (Exception ignored){
       }
    }


    /**
     * 原库存申请记录
     */
    private static List<Order> getOriginOrders(Map<String, List<Order>> itemOrderMap) {
        List<Order> originOrders = new ArrayList<>();
        for (Map.Entry<String, List<Order>> entry : itemOrderMap.entrySet()) {
            originOrders.addAll(entry.getValue());
        }
        return originOrders;
    }

    /**
     * 新库存申请记录
     */
    private static List<Order> getNewOrders(Map<String, List<Order>> itemOrderMap) {
        List<Order> newOrders = new ArrayList<>();
        for (Map.Entry<String, List<Order>> entry : itemOrderMap.entrySet()) {
            List<Order> splitOrders = entry.getValue();
            for (Order splitOrder : splitOrders) {
                if (splitOrder.getEnableStatus() == 1) {
                    newOrders.add(splitOrder);
                }
            }
        }
        return newOrders;
    }

    /**
     * 返回商品的key:hasSamePlatItem方法返回false，调用此方法
     */
    private static String getItemKey(Order order) {
        StringBuilder key = new StringBuilder();
        key.append(order.getSource());

        if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
            key.append("_").append(StringUtils.trimToEmpty(order.getNumIid())).append("_").append(StringUtils.trimToEmpty(order.getSkuId()));
            if (order.getOid() != null) {
                key.append("_").append(order.getOid());
            }
        }
        Integer type = order.getType();
        if (type == null) {
            type = -1;
        }
        if (order.isGift()) {//赠品
            key.append("_gift");
            type = 1;
        }
        key.append("_").append(type);
        if (order.getItemSysId() != null && order.getItemSysId() > 0) {//匹配了系统商品
            key.append(order.getItemSysId()).append("_").append(order.getSkuSysId() == null || order.getSkuSysId() < 0L ? 0L : order.getSkuSysId());
        }
        return key.toString();
    }

}

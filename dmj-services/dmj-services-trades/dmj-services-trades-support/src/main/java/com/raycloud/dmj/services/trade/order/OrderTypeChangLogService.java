package com.raycloud.dmj.services.trade.order;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.dao.order.OrderTypeChangLogDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.fms.OrderTypeChangDto;
import com.raycloud.dmj.domain.fms.OrderTypeChangVo;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.OrderTypeChangLog;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-31 16:01:46
 */
@Slf4j
@Service
public class OrderTypeChangLogService implements IOrderTypeChangLogService{

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private OrderTypeChangLogDao orderTypeChangLogDao;

    @Override
    public void saveLog(Staff staff, List<Order> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        try {
            List<OrderTypeChangLog> orderTypeChangeLogs = new ArrayList<>();
            for (Order order : orders) {
                OrderTypeChangLog orderTypeChangeLog = new OrderTypeChangLog();
                orderTypeChangeLog.setCompanyId(order.getCompanyId());
                orderTypeChangeLog.setOrderId(order.getId());
                orderTypeChangeLog.setSid(order.getSid());
                orderTypeChangeLog.setSysOuterId(order.getSysOuterId());
                orderTypeChangeLog.setItemSysId(order.getItemSysId());
                orderTypeChangeLog.setSkuSysId(order.getSkuSysId());
                orderTypeChangeLog.setType(2);
                orderTypeChangeLog.setCombineId(NumberUtils.nvlLong(order.getCombineId(), 0L));
                orderTypeChangeLog.setCreated(new Date());
                orderTypeChangeLogs.add(orderTypeChangeLog);
            }
            orderTypeChangLogDao.batchInsert(staff, orderTypeChangeLogs);
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(staff, e, String.format("记录套件转单品日志出错，orderIds=%s", OrderUtils.toIdList(orders))), e);
        }
    }

    @Override
    public List<OrderTypeChangVo> queryOrderTypeChangLog(Staff staff, OrderTypeChangDto orderTypeChangDto) {
        List<OrderTypeChangLog> list;
        if (StringUtils.isEmpty(orderTypeChangDto.getOrderId())) {
            list = orderTypeChangLogDao.query(staff, Long.valueOf(orderTypeChangDto.getSid()));
        } else {
            list = orderTypeChangLogDao.queryBySidAndOrderId(staff, NumberUtils.str2Long(orderTypeChangDto.getSid()), NumberUtils.str2Long(orderTypeChangDto.getOrderId()));
        }
        List<OrderTypeChangVo> voList = new ArrayList<>();
        for (OrderTypeChangLog orderTypeChangLog : list) {
            OrderTypeChangVo vo = new OrderTypeChangVo();
            vo.setCompanyId(orderTypeChangLog.getCompanyId());
            vo.setOrderId(orderTypeChangLog.getOrderId());
            vo.setSid(orderTypeChangLog.getSid());
            vo.setSysOuterId(orderTypeChangLog.getSysOuterId());
            vo.setItemSysId(orderTypeChangLog.getItemSysId());
            vo.setSkuSysId(orderTypeChangLog.getSkuSysId());
            vo.setType(orderTypeChangLog.getType());
            vo.setCombineId(orderTypeChangLog.getCombineId());
            vo.setCreated(orderTypeChangLog.getCreated());
            voList.add(vo);
        }
        return voList;
    }
}

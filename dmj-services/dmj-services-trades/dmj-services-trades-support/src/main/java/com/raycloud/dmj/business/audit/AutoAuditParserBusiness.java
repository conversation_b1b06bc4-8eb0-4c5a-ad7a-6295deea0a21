package com.raycloud.dmj.business.audit;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.ThreadPoolBusiness;
import com.raycloud.dmj.business.common.SimpleTrade;
import com.raycloud.dmj.business.common.SpelConditionBusiness;
import com.raycloud.dmj.business.trade.TradePaymentSwitchBusiness;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.rule.RuleOpEnum;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.rulematch.AiRule;
import com.raycloud.dmj.domain.trade.rulematch.AiTrade;
import com.raycloud.dmj.domain.trades.AutoAuditConfig;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.audit.AuditFxData;
import com.raycloud.dmj.domain.trades.audit.AuditThreadLocal;
import com.raycloud.dmj.domain.trades.payment.util.BigDecimalWrapper;
import com.raycloud.dmj.domain.trades.rulematch.RuleMatchUtils;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.DataUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.trade.rulematch.IRuleMatchFilterService;
import com.raycloud.dmj.services.trades.ISysTradeService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.utils.TradeAddressUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by yangheng on 17/3/24.
 */
@Service
public class AutoAuditParserBusiness extends ThreadPoolBusiness<Trade> {

    @Resource
    private AuditFxBusiness auditFxBusiness;

    @Resource
    private ISysTradeService sysTradeService;

    @Resource
    private AuditJitxBusiness auditJitxBusiness;


    @Resource
    private IRuleMatchFilterService iRuleMatchFilterService;


    private final SpelExpressionParser parser = new SpelExpressionParser();

    private final static int BATCH_SIZE = 30;

    private final static int THREAD_COUNT = 20;

    @Override
    protected int getTouchPoolSize() {
        return BATCH_SIZE;
    }

    @Override
    protected int getBatchSize() {
        return BATCH_SIZE;
    }

    @Override
    protected int getCoreThreadCount() {
        return THREAD_COUNT;
    }

    @Override
    protected int getMaxThreadCount() {
        return THREAD_COUNT;
    }

    public void analyse(Staff staff, AuditThreadLocal auditThreadLocal, AutoAuditData autoAuditData) {
        //合单的自动审核
        if (autoAuditData.originMergeTrades.size() > 0) {
            autoAuditData.originMergeTrades.forEach((mergeSid, mergeTrades) -> analyseMerge(staff, auditThreadLocal, autoAuditData, mergeSid, mergeTrades));
        }

        //非合单的自动审核
        if (autoAuditData.originNormalTrades.size() > 0) {
            long start = System.currentTimeMillis();
            if (auditThreadLocal.insufficientAnalyze) {
                autoAuditData.auditSuccessTrades.addAll(autoAuditData.originNormalTrades);
            } else {
                run(staff, autoAuditData.originNormalTrades, new AbstractBusiness() {
                    @Override
                    public void doBusiness(List<Trade> data) {
                        for (Trade trade : data) {
                            trade.setAuditMatchRule(analyse(staff, auditThreadLocal, autoAuditData, trade));
                        }
                    }
                });
            }
            Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("非合单自动审核通过表达式匹配订单结束，took=%s", (System.currentTimeMillis() - start))));
        }
    }

    private void analyseMerge(Staff staff, AuditThreadLocal auditThreadLocal, AutoAuditData autoAuditData, Long mergeSid, List<Trade> mergeTrades) {
        long start = System.currentTimeMillis();
        if (mergeTrades == null || mergeTrades.size() == 0) {
            return;
        }
        Trade mainTrade = findMainTrade(mergeTrades);
        if (auditThreadLocal.insufficientAnalyze) {
            if (mainTrade != null) {
                autoAuditData.auditSuccessTrades.add(mainTrade);
            }
            return;
        }
        Integer matchResult = 0;
        if (mainTrade == null) {
            Logs.warn(LogHelper.buildLogHead(staff).append(String.format("自动审核找不到主单，sids=%s", TradeUtils.toSidList(mergeTrades))));
            autoAuditData.auditFailTrades.addAll(mergeTrades);
        } else if (!auditThreadLocal.openAuditAutoInsufficientCanceled && !auditThreadLocal.openAuditActiveStockRecord && mainTrade.getIsExcep() == 1) {
            mainTrade.getOperations().put(OpEnum.MANUAL_AUTO_AUDIT, "智能审核是异常订单不通过。");
            autoAuditData.auditFailTrades.add(mainTrade);
        } else {//找到主单
            matchResult = analyse(staff, auditThreadLocal, autoAuditData, mainTrade);
        }
        for (Trade trade : mergeTrades) {
            trade.setAuditMatchRule(matchResult);
        }
        Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("合单自动审核通过表达式匹配订单结束，took=%s", (System.currentTimeMillis() - start))));
    }

    public Trade findMainTrade(List<Trade> trades) {
        Trade mainTrade = null;
        //商品数量
        int itemNum = 0;
        //商品种类数量
        int itemKindNum = 0;
        //买家留言
        StringBuilder buyerMessage = new StringBuilder();
        //买家备注
        StringBuilder sellerMemo = new StringBuilder();
        //旗帜 TODO 旗帜先不管
        Set<Long> sellerFlag = new HashSet<>();
        //发票
        StringBuilder invoiceName = new StringBuilder();
        //实付金额
        BigDecimalWrapper payment = new BigDecimalWrapper();
        //标签
        Set<Long> tagIdSet = new HashSet<>();

        List<Order> mainOrders = new ArrayList<>();

        int isHandlerMessage = 1;
        int isHandlerMemo = 1;

        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            if (orders != null && orders.size() > 0) {
                mainOrders.addAll(orders);
                itemKindNum += orders.size();
                for (Order order : orders) {
                    itemNum += order.getNum();
                }
            }

            if (StringUtils.isNotEmpty(trade.getBuyerMessage())) {
                buyerMessage.append(trade.getBuyerMessage()).append(",");
                if (isHandlerMessage == 1 && trade.getIsHandlerMessage() == 0) {
                    isHandlerMessage = 0;
                }
            }
            if (StringUtils.isNotEmpty(trade.getSellerMemo())) {
                sellerMemo.append(trade.getSellerMemo()).append(",");
                if (isHandlerMemo == 1 && trade.getIsHandlerMemo() == 0) {
                    isHandlerMemo = 0;
                }
            }
            if (StringUtils.isNotEmpty(trade.getInvoiceName())) {
                invoiceName.append(trade.getInvoiceName()).append(",");
            }
            if (trade.getSellerFlag() != null) {
                sellerFlag.add(trade.getSellerFlag());
            }
            if (StringUtils.isNotEmpty(trade.getTagIds())) {
                Set<Long> tagIds = Arrays.stream(trade.getTagIds().split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toSet());
                tagIdSet.addAll(tagIds);
            }
            payment.add(trade.getPayment());

            if (mainTrade == null && trade.getEnableStatus() == 1) {
                mainTrade = trade;
            }
        }

        if (mainTrade != null) {
            mainTrade.setItemNum(itemNum);
            mainTrade.setItemKindNum(itemKindNum);
            mainTrade.setBuyerMessage(buyerMessage.toString());
            mainTrade.setSellerMemo(sellerMemo.toString());
            mainTrade.setInvoiceName(invoiceName.toString());
            mainTrade.setPayment(payment.getString());
            mainTrade.setIsHandlerMessage(isHandlerMessage);
            mainTrade.setIsHandlerMemo(isHandlerMemo);
            if (CollectionUtils.isNotEmpty(tagIdSet)) {
                mainTrade.setTagIds(tagIdSet.stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
            TradeUtils.setOrders(mainTrade, mainOrders);
        }

        return mainTrade;
    }

    private Integer analyse(Staff staff, AuditThreadLocal auditThreadLocal, AutoAuditData autoAuditData, Trade trade) {
        Long userId = TradeUtils.isGxOrMixTrade(trade) ? trade.getTaobaoId() : trade.getUserId();
        fillBrandSupplier(staff, auditThreadLocal.userConfigMap.get(userId), trade);
        SimpleTrade st = TradePaymentSwitchBusiness.switchPayment(staff, auditThreadLocal.tradeConfig, trade, () -> SimpleTrade.build(trade));
        // 处理特殊地址 用于兼容自动化规则匹配
        TradeAddressUtil.compatibleSpecialAddress(st);
        try {
            if (auditThreadLocal.validateTemplate && !DataUtils.checkLongNotEmpty(trade.getTemplateId()) && !StringUtils.equals(CommonConstants.PLAT_FORM_TYPE_VIPJITX, trade.getSubSource())) {
                trade.getOperations().put(auditThreadLocal.opEnum, auditThreadLocal.opEnum.getName() + "没有模板不通过。");
                autoAuditData.auditFailTrades.add(trade);
                return 0;
            }
            if (!validateFxTrade(staff, auditThreadLocal.isManualAutoAudit() || auditThreadLocal.isAutoAudit(), trade, autoAuditData.auditFxData)) {
                Logs.warn(LogHelper.buildLogHead(staff).append(String.format("%s审核分销验证不通过", trade.getSid())));
                autoAuditData.auditFailTrades.add(trade);
                return 0;
            }
            if (trade.getIsExcep() == 1) {
                Set<Integer> except = TradeUtils.parseExcept(staff,trade);
                if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.INSUFFICIENT) && (auditThreadLocal.needIncluedInsufficientStock || auditThreadLocal.allowAuditIncluedInsufficientStock)) {
                    except.remove((int) ExceptEnum.INSUFFICIENT.getOldExceptEnum().getOldIdx());
                    if (except.size() > 0) {
                        trade.getOperations().put(auditThreadLocal.opEnum, auditThreadLocal.opEnum.getName() + "订单是非缺货的异常订单不通过。");
                        autoAuditData.auditFailTrades.add(trade);
                        return 0;
                    }
                } else {
                    Logs.warn(LogHelper.buildLogHead(staff).append(String.format("%s %s是异常订单，except=%s", trade.getSid(), auditThreadLocal.opEnum.getName(), except)));
                    if (auditThreadLocal.needIncluedInsufficientStock || auditThreadLocal.allowAuditIncluedInsufficientStock) {
                        except.remove((int) ExceptEnum.INSUFFICIENT.getOldExceptEnum().getOldIdx());
                    }
                    if (except.size() > 0) {
                        trade.getOperations().put(auditThreadLocal.opEnum, auditThreadLocal.opEnum.getName() + "订单是异常订单不通过。");
                        autoAuditData.auditFailTrades.add(trade);
                        return 0;
                    }
                }
            }
            if (auditJitxBusiness.jitxAuditJudge(staff, trade)) {
                trade.getOperations().put(auditThreadLocal.opEnum, auditThreadLocal.opEnum.getName() + "JITX订单寻仓失败审核不通过。");
                autoAuditData.auditFailTrades.add(trade);
                return 0;
            }

            AiTrade aiTrade = RuleMatchUtils.transferAiTrade(trade);
            iRuleMatchFilterService.matchAndFilter(staff, auditThreadLocal.tradeConfig, RuleMatchUtils.transferAiRule(auditThreadLocal.userConfigMap.get(userId), auditThreadLocal.ignoreBeforeDate), aiTrade, RuleOpEnum.AUDIT);
            if (CollectionUtils.isEmpty(aiTrade.getAiRuleResult().getSuccessResults())) {
                Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("智能审核规则不通过, config=%s, trade=%s", auditThreadLocal.userConfigMap.get(userId), JSON.toJSONString(st))));
                trade.getOperations().put(auditThreadLocal.opEnum, auditThreadLocal.opEnum.getName() + "规则不匹配不通过。");
                autoAuditData.auditFailTrades.add(trade);
                return 0;
            } else {
                autoAuditData.auditSuccessTrades.add(trade);
                List<Long> matchRules = aiTrade.getAiRuleResult().getSuccessResults();
                trade.getOperations().put(auditThreadLocal.opEnum, "审核订单;" + auditThreadLocal.opEnum.getName() + "审单规则id:" + matchRules.get(0));
                return 1;
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(staff, e, "解析表达式出错,trade.getUserId=").append(trade.getUserId()).append(",expr:").append(auditThreadLocal.userExpressionMap).append(",data:").append(JSON.toJSONString(st)), e);
            trade.getOperations().put(OpEnum.MANUAL_AUTO_AUDIT, "智能审核匹配规则报错不通过。");
            autoAuditData.auditFailTrades.add(trade);
            return 0;
        }
    }

    /**
     * 根据规则设置填充供应商和品牌
     */
    private void fillBrandSupplier(Staff staff, List<AutoAuditConfig> configs, Trade trade) {
        if (CollectionUtils.isNotEmpty(configs)) {
            for (AutoAuditConfig config : configs) {
                List<SpelCondition> conditions = config.getConditions();
                if (CollectionUtils.isNotEmpty(conditions)) {
                    List<String> files = conditions.stream().map(SpelCondition::getField).collect(Collectors.toList());
                    //如果规则配置品牌和供应商
                    if (files.contains(SpelCondition.FIELD_ORDER_BRAND_IDS) || files.contains(SpelCondition.FIELD_ORDER_SUPPLIER_IDS)) {
                        sysTradeService.fillBrandSupplierId(staff, Collections.singletonList(trade));
                        break;
                    }
                }
            }
        }
    }


    /**
     * 分销订单并且是智能审核才校验分销订单审核条件
     */
    private boolean validateFxTrade(Staff staff, boolean isManualAudit, Trade trade, AuditFxData auditFxData) {
        if (isManualAudit && TradeUtils.isFxOrMixTrade(trade)) {
            List<Trade> fxTrades = auditFxBusiness.checkSourceTrades(staff, auditFxData, Collections.singletonList(trade), null);
            return CollectionUtils.isNotEmpty(fxTrades);
        }
        return true;
    }

    public static String buildAutoAuditExpression(AutoAuditConfig config) {
        if (config == null) {
            return "";
        }
        return SpelConditionBusiness.buildExpression(config.getConditions());
    }


}

package com.raycloud.dmj.services.trade.merge;

import cn.hutool.core.map.MapUtil;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.*;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.TradeMergeEnum;
import com.raycloud.dmj.domain.stalls.SaleConfig;
import com.raycloud.dmj.domain.stalls.enums.SaleConfigEnums;
import com.raycloud.dmj.domain.trade.common.TradeBusinessUtils;
import com.raycloud.dmj.domain.trade.config.*;
import com.raycloud.dmj.domain.trade.merge.*;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trade.merge.TradeMergeConf;
import com.raycloud.dmj.domain.trade.merge.TradeMergeItemsVo;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.diamond.TradeMergeConfigUtils;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.stalls.application.service.ISaleConfigService;
import com.raycloud.dmj.services.trade.config.TradeConfigContextBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trade.config.TradeConfigEnum.*;

/**
 * @ClassName MergeContextBusiness
 * @Description 合单上下文
 * <AUTHOR>
 * @Date 2023/9/28
 * @Version 1.0
 */
@Service
public class MergeContextBusiness {

    private static final TradeConfigEnum[] TRADE_CONFIG_ENUMS = {
            EXCEPTION_NOT_LOCK_STOCK,
            MERGE_DIFF_MD5_PDD,
            MERGE_MULTIL_PDD,
            GROSS_CACULATE_TYPE
    };

    @Resource
    TradeMergeConfBusiness tradeMergeConfBusiness;
    @Resource
    IUserService userService;
    @Resource
    ICompanyService companyService;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    ITradeConfigNewService tradeConfigNewService;
    @Resource
    TradeConfigContextBusiness tradeConfigContextBusiness;
    @Resource
    ISaleConfigService saleConfigService;
    @Resource
    IMergeItemGroupService mergeItemGroupService;

    @Resource
    TradeTimeoutContextBuilder tradeTimeoutContextBuilder;

    /**
     * 取消合单上下文
     */
    public TradeMergeContext initMergeUndoContext(Staff staff, List<Long> mergeSids, TradeMergeEnum tradeMergeEnum) {
        TradeMergeContext context = initMergeContext(staff, tradeConfigService.get(staff), tradeMergeEnum);
        context.setMergeSids(mergeSids);
        return context;
    }

    /**
     * 初始化合单检查上下文
     *
     * @param staff
     * @return
     */
    public MergeCheckContext initMergeCheckContext(Staff staff, User user, TradeConfig tradeConfig, Map<String, Set<String>> sourceQueryKeysMap) {
        return MergeCheckContext.builder()
                .configContext(tradeConfigContextBusiness.init(tradeConfig, new HashMap<>()))
                .user(user)
                .sourceQueryKeysMap(sourceQueryKeysMap)
                .checkMergeKeys(new HashSet<>())
                .minCreated(DateUtils.addDays(new Date(), -1 * getMergeCheckDays(staff)))
                .belongType(0)
                .build();
    }

    public int getMergeCheckDays(Staff staff) {
        CompanyProfile profile = null;
        try {
            Company company = staff.getCompany();
            if (company != null) {
                profile = company.getProfile();
            }
            if (profile == null) {
                profile = companyService.getCompanyProfile(staff.getCompanyId());
            }
            // 获取mergeCheckDays
            Integer mergeCheckDays = profile.getConf().getMergeCheckDays();
            if (mergeCheckDays != null && mergeCheckDays >= MergeConstant.MERGE_CHECK_DAYS_MIN && mergeCheckDays <= MergeConstant.MERGE_CHECK_DAYS_MAX) {
                return mergeCheckDays;
            }
            return MergeConstant.MERGE_CHECK_DAYS_DEFAULT;
        } catch (Exception e) {
            Logs.error(LogHelper.buildLogHead(staff).append("获取mergeCheckDays出错"), e);
            return MergeConstant.MERGE_CHECK_DAYS_DEFAULT;
        }
    }

    /**
     * 初始化合单上下文
     *
     * @param user           店铺
     * @param tradeMergeConf 店铺合单配置
     * @param tradeMergeEnum 合单操作类型
     * @return 合单上下文
     */
    public TradeMergeContext initMergeContext(Staff staff, User user, TradeMergeConf tradeMergeConf, TradeMergeEnum tradeMergeEnum) {
        return initMergeContext(staff, user, tradeConfigService.get(staff), saleConfigService.get(staff), tradeMergeConf, tradeMergeEnum);
    }

    /**
     * 初始化合单上下文
     *
     * @param staff
     * @param user           店铺
     * @param tradeConfig    订单配置
     * @param tradeMergeConf 店铺合单配置
     * @param tradeMergeEnum 合单操作类型
     * @return 合单上下文
     */
    public TradeMergeContext initMergeContext(Staff staff, User user, TradeConfig tradeConfig, SaleConfig saleConfig, TradeMergeConf tradeMergeConf, TradeMergeEnum tradeMergeEnum) {
        TradeMergeContext context = initMergeContext(staff, tradeConfig, tradeMergeEnum);
        context.setUser(user);

        List<Long> queryUserIds = new ArrayList<>();
        if (user != null) {
            queryUserIds.add(user.getId());
        } else if (tradeMergeConf != null && tradeMergeConf.getUserId() != null) {
            queryUserIds.add(tradeMergeConf.getUserId());
        }
        context.setQueryUserIds(queryUserIds);

        context.setMergeLimitConfig(TradeMergeConfigUtils.getMergeLimitConfig(staff));
        if (saleConfig != null) {
            context.setAllowDangkouMerge(saleConfig.getIntegerVal(SaleConfigEnums.DANGKOU_TRADE_ALLOW_MERGE) == 1);
        }

        handleTradeMergeConf(context, tradeMergeConf, tradeMergeEnum);
        handleMergeTypes(context, tradeMergeEnum);
        handleMergeOuterIds(staff, context, tradeMergeEnum);
        handleMergeItemGroup(staff, context, tradeMergeConf);

        if (context.getMaxItemNum() > 0
                || (CollectionUtils.isNotEmpty(context.getSysExcepSet()) && context.getSysExcepSet().contains("2"))
                || CollectionUtils.isNotEmpty(context.getMergeOuterIds())
                || CollectionUtils.isNotEmpty(context.getMergeItemGroups())
                || context.isAllowSingleSku()
                || context.isAllowMultiSku()
                || context.isMergeRefundSplitTrade()
                || context.isMergeAllGiftSplitTrade()
                || MapUtil.isNotEmpty(context.getMergeOuterIdsMap())
        ) {
            context.setQueryOrder(true);
        }
        return context;
    }

    public TradeMergeContext initMergeContext(Staff staff, TradeConfig tradeConfig, TradeMergeEnum tradeMergeEnum) {
        Map<String, TradeConfigNew> configMap = tradeConfigNewService.getMap(staff, TRADE_CONFIG_ENUMS);
        TradeMergeContext mergeContext = TradeMergeContext.builder()
                .tradeMergeEnum(tradeMergeEnum)
                .tradeFakeRule(TradeBusinessUtils.getMergeTradeFakeRule(staff.getCompanyId()))
                .opEnum(tradeMergeEnum.getDbType() - 3 == 0 ? OpEnum.MERGE_UNDO : OpEnum.MERGE)
                .mergeSidTradesMap(new HashMap<>())
                .splitSidTradesMap(new HashMap<>())
                .errorMsg(new HashMap<>())
                .unauditWaveTrades(new ArrayList<>())
                .cancelOutsidTradeMap(new HashMap<>())
                .poisonTrades(new ArrayList<>())
                .opEnumTradeTraceMap(new HashMap<>())
                .sysOutSidSidMap(new HashMap<>())
                .mergeSidPlatOutSidSids(new HashMap<>())
                .successNum(new AtomicInteger(0))
                .keepOutSidTrades(new ArrayList<>())
                .newMergeSidChangeOldSidsMap(new HashMap<>())
                .tradeTimeoutContext(tradeTimeoutContextBuilder.build(staff))
                .tradeMergeSplitRecordMap(new HashMap<>())
                .mergeOuterIdsMap(new HashMap<>())
                .build();

        mergeContext.setConfigContext(tradeConfigContextBusiness.init(tradeConfig, configMap));
        return mergeContext;
    }

    public void resetMergeContext(TradeMergeContext context) {
        context.getMergeSidTradesMap().clear();
        context.getSplitSidTradesMap().clear();
        context.getErrorMsg().clear();
        context.getUnauditWaveTrades().clear();
        context.getCancelOutsidTradeMap().clear();
        context.getPoisonTrades().clear();
        context.getOpEnumTradeTraceMap().clear();
        context.getSysOutSidSidMap().clear();
        context.getMergeSidPlatOutSidSids().clear();
        context.getKeepOutSidTrades().clear();
        context.getSuccessNum().set(0);
        context.getTradeMergeSplitRecordMap().clear();
        context.setQueryOrder(true);
        context.setFillWxsphRecycledOutSid(true);
        context.getNewMergeSidChangeOldSidsMap().clear();
    }

    private void handleTradeMergeConf(TradeMergeContext context, TradeMergeConf tradeMergeConf, TradeMergeEnum tradeMergeEnum) {
        double maxNetWeight = -1;
        int maxItemNum = -1;
        boolean mergeFinishAudit = false;
        boolean mergeWaitPrint = false;
        boolean mergeWaitPack = false;
        boolean mergeWaitWeight = false;
        boolean mergeWaitConsign = false;
        boolean mergeExcep = false;
        Set<String> selfExcepSet = new HashSet<>();
        Set<String> sysExcepSet = new HashSet<>();
        boolean mergePreUpload = false;
        boolean mergeUndoMerge = false;
        boolean mergeSplit = false;
        boolean mergeSys = true;
        Set<String> mergeConfigTagIds = new HashSet<>();
        Set<Long> onlyMergeConfigTagIds = new HashSet<>();
        Set<Long> excludeMergeConfigTagIds = new HashSet<>();
        boolean merge1688DiffType = false;
        boolean mergeTmWithSf = false;
        boolean mergePresellSys = false;
        boolean mergePresellPlatform = false;
        Integer stepPayTime = null;
        Long stepPayTimeUnitMillis = null;
        Integer stepCommitmentTime = null;
        Long stepCommitmentTimeUnitMillis = null;
        int mergeSkuType = -1;
        boolean allowSingleSku = false;
        boolean allowMultiSku = false;
        boolean poisionOutsid = false;
        //允许只做过退款拆分的订单合单
        boolean enableMergeRefundSplitTrade = false;
        //允许合并全是赠品的拆分订单
        boolean enableMergeAllGiftSplitTrade = false;
        if (tradeMergeConf != null) {
            if (tradeMergeConf.getMaxNetWeight() != null && tradeMergeConf.getMaxNetWeight() > 0) {
                maxNetWeight = tradeMergeConf.getMaxNetWeight();
            }
            if (tradeMergeConf.getMaxItemNum() != null && tradeMergeConf.getMaxItemNum() > 0) {
                maxItemNum = tradeMergeConf.getMaxItemNum();
            }

            mergeFinishAudit = tradeMergeConf.getMamFilterFinishAudit() != null && tradeMergeConf.getMamFilterFinishAudit() == 1;
            mergeWaitPrint = tradeMergeConf.getMergeWaitPrint() != null && tradeMergeConf.getMergeWaitPrint() == 1;
            mergeWaitPack = tradeMergeConf.getMergeWaitPack() != null && tradeMergeConf.getMergeWaitPack() == 1;
            mergeWaitWeight = tradeMergeConf.getMergeWaitWeight() != null && tradeMergeConf.getMergeWaitWeight() == 1;
            mergeWaitConsign = tradeMergeConf.getMergeWaitConsign() != null && tradeMergeConf.getMergeWaitConsign() == 1;
            mergeExcep = tradeMergeConf.getMamFilterExcep() != null && tradeMergeConf.getMamFilterExcep() == 1;
            if (mergeExcep && tradeMergeConf.getSpeExcep() != null && tradeMergeConf.getSpeExcep() == 1) {
                selfExcepSet = com.raycloud.dmj.domain.utils.ArrayUtils.toStringSet(tradeMergeConf.getSelfExceps());
                sysExcepSet = com.raycloud.dmj.domain.utils.ArrayUtils.toStringSet(tradeMergeConf.getSysExceps());
            }
            mergePreUpload = tradeMergeConf.getMergePreSendOrder() != null && tradeMergeConf.getMergePreSendOrder() == 1;
            //仅支持系统执行智能合单时生效
            mergeUndoMerge = (TradeMergeEnum.MERGE_AUTO_NULTIL == tradeMergeEnum || TradeMergeEnum.MERGE_AUTO_MANUAL == tradeMergeEnum) && tradeMergeConf.getMamFilterUndo() != null && tradeMergeConf.getMamFilterUndo() == 1;
            //仅支持系统执行智能合单时生效
            mergeSplit = (TradeMergeEnum.MERGE_AUTO_NULTIL == tradeMergeEnum || TradeMergeEnum.MERGE_AUTO_MANUAL == tradeMergeEnum) && tradeMergeConf.getMamFilterSplit() != null && tradeMergeConf.getMamFilterSplit() == 1;
            // 对自动合单和智能合单都生效
            if (Objects.equals(tradeMergeEnum, TradeMergeEnum.MERGE_AUTO_MANUAL) || Objects.equals(tradeMergeEnum, TradeMergeEnum.MERGE_AUTO)) {
                mergeSys = Objects.equals(1, tradeMergeConf.getHasSysTrade());
            }
            if (StringUtils.isNotBlank(tradeMergeConf.getTagIds()) && Objects.equals(Optional.ofNullable(tradeMergeConf.getEnableMergeTag()).orElse(0), 1)) {
                mergeConfigTagIds.addAll(Arrays.asList(tradeMergeConf.getTagIds().split("[,，]")));
            }
            if (StringUtils.isNotBlank(tradeMergeConf.getOnlyTagIds()) && Objects.equals(Optional.ofNullable(tradeMergeConf.getOnlyMergeTag()).orElse(0), 1)) {
                onlyMergeConfigTagIds.addAll(ArrayUtils.toLongSet(tradeMergeConf.getOnlyTagIds()));
            }
            if (StringUtils.isNotBlank(tradeMergeConf.getExcludeMergeTagIds()) && Objects.equals(Optional.ofNullable(tradeMergeConf.getEnableMergeExcludeTagId()).orElse(0), 1)) {
                excludeMergeConfigTagIds.addAll(ArrayUtils.toLongSet(tradeMergeConf.getExcludeMergeTagIds()));
            }
            merge1688DiffType = tradeMergeConf.getMamFilter1688() != null && tradeMergeConf.getMamFilter1688() == 1;
            mergeTmWithSf = tradeMergeConf.getEnableMergeTmOrSfTrade() != null && tradeMergeConf.getEnableMergeTmOrSfTrade() == 1;
            mergePresellSys = tradeMergeConf.getMergeSysPresellWithNormal() != null && tradeMergeConf.getMergeSysPresellWithNormal() == 1;
            mergePresellPlatform = tradeMergeConf.getMergePresellNorlMal() != null && tradeMergeConf.getMergePresellNorlMal() == 1;
            stepPayTime = tradeMergeConf.getApartTime();
            stepPayTimeUnitMillis = timeTypeToUnitMillis(tradeMergeConf.getApartTimeType());
            stepCommitmentTime = tradeMergeConf.getCommitmentTime();
            stepCommitmentTimeUnitMillis = timeTypeToUnitMillis(tradeMergeConf.getCommitmentTimeType());
            if (tradeMergeConf.getMergeSkuType() != null && (tradeMergeConf.getMergeSkuType() == 1 || tradeMergeConf.getMergeSkuType() == 2)) {
                mergeSkuType = tradeMergeConf.getMergeSkuType();
            }
            allowSingleSku = tradeMergeConf.getAllowSingleSku() != null && tradeMergeConf.getAllowSingleSku() == 1;
            allowMultiSku = tradeMergeConf.getAllowMultiSku() != null && tradeMergeConf.getAllowMultiSku() == 1;
            poisionOutsid = tradeMergeConf.getPoisionOutsid() != null && tradeMergeConf.getPoisionOutsid() == 1;
            if (Objects.equals(tradeMergeEnum, TradeMergeEnum.MERGE_AUTO)) {
                // 仅对自动合单生效
                enableMergeRefundSplitTrade = tradeMergeConf.getEnableMergeRefundSplitTrade() != null && tradeMergeConf.getEnableMergeRefundSplitTrade() == 1;
                enableMergeAllGiftSplitTrade = tradeMergeConf.getEnableMergeAllGiftSplitTrade() != null && tradeMergeConf.getEnableMergeAllGiftSplitTrade() == 1;
            }
        }

        context.setTradeMergeConf(tradeMergeConf);
        context.setMaxNetWeight(maxNetWeight);
        context.setMaxItemNum(maxItemNum);
        context.setMergeFinishAudit(mergeFinishAudit);
        context.setMergeWaitPrint(mergeWaitPrint);
        context.setMergeWaitPack(mergeWaitPack);
        context.setMergeWaitWeight(mergeWaitWeight);
        context.setMergeWaitConsign(mergeWaitConsign);
        context.setMergeExcep(mergeExcep);
        context.setSelfExcepSet(selfExcepSet);
        context.setSysExcepSet(sysExcepSet);
        context.setMergePreUpload(mergePreUpload);
        context.setMergeUndoMerge(mergeUndoMerge);
        context.setMergeSplit(mergeSplit);
        context.setMergeSys(mergeSys);
        context.setMergeConfigTagIds(mergeConfigTagIds);
        context.setOnlyMergeConfigTagIds(onlyMergeConfigTagIds);
        context.setExcludeMergeConfigTagIds(excludeMergeConfigTagIds);
        context.setMerge1688DiffType(merge1688DiffType);
        context.setMergeTmWithSf(mergeTmWithSf);
        context.setMergePresellSys(mergePresellSys);
        context.setMergePresellPlatform(mergePresellPlatform);
        context.setStepPayTime(stepPayTime);
        context.setStepPayTimeUnitMillis(stepPayTimeUnitMillis);
        context.setStepCommitmentTime(stepCommitmentTime);
        context.setStepCommitmentTimeUnitMillis(stepCommitmentTimeUnitMillis);
        context.setMergeSkuType(mergeSkuType);
        context.setAllowSingleSku(allowSingleSku);
        context.setAllowMultiSku(allowMultiSku);
        context.setPoisionOutsid(poisionOutsid);
        context.setMergeAllGiftSplitTrade(enableMergeAllGiftSplitTrade);
        context.setMergeRefundSplitTrade(enableMergeRefundSplitTrade);
    }

    private Long timeTypeToUnitMillis(String type) {
        if (type == null || type.isEmpty()) {
            return null;
        }

        switch (type) {
            case "hour":
                return TimeUnit.HOURS.toMillis(1);
            case "day":
                return TimeUnit.DAYS.toMillis(1);
            default:
                return null;
        }
    }

    /**
     * 处理合单操作类型
     */
    private void handleMergeTypes(TradeMergeContext context, TradeMergeEnum tradeMergeEnum) {
        List<Integer> mergeTypes = new ArrayList<>(TradeMergeEnum.getDbTypes());
        int mergeRefund = 0;
        switch (tradeMergeEnum) {
            case MERGE_AUTO://自动合单
                mergeTypes.remove(TradeMergeEnum.MERGE_NULTIL.getDbType());
                mergeTypes.remove(TradeMergeEnum.MERGE_UNDO.getDbType());
                break;
            case MERGE_AUTO_MANUAL://智能合单
                //跨店合单的订单不支持自动合单
                mergeTypes.remove(TradeMergeEnum.MERGE_NULTIL.getDbType());
                //已经取消合单的订单不支持合单
                if (!context.isMergeUndoMerge()) {
                    mergeTypes.remove(TradeMergeEnum.MERGE_UNDO.getDbType());
                }
                break;
            case MERGE_AUTO_NULTIL://跨店智能合单
                //已经取消合单的订单不支持合单
                if (!context.isMergeUndoMerge()) {
                    mergeTypes.remove(TradeMergeEnum.MERGE_UNDO.getDbType());
                }
                break;
            case MERGE_MANUAL://手工合单
                mergeTypes.remove(TradeMergeEnum.MERGE_NULTIL.getDbType());
                break;
        }
        if (tradeMergeEnum == TradeMergeEnum.MERGE_AUTO_MANUAL || tradeMergeEnum == TradeMergeEnum.MERGE_AUTO_NULTIL) {
            mergeRefund = 1;
        }
        context.setMergeTypes(mergeTypes);
        context.setMergeRefund(mergeRefund);

    }

    /**
     * 处理合单商品设置
     */
    private void handleMergeOuterIds(Staff staff, TradeMergeContext context, TradeMergeEnum tradeMergeEnum) {
        //跨店合单商品信息并集聚合处理
        if (Objects.equals(tradeMergeEnum, TradeMergeEnum.MERGE_AUTO_NULTIL) && context.getTradeMergeConf() != null && MapUtil.isNotEmpty(context.getTradeMergeConf().getMergeRuleIdSkuTypeMap())){
            fillMergeOuterIds(staff, context,  context.getTradeMergeConf().getMergeRuleIdSkuTypeMap());
        }
        if (context.getTradeMergeConf() == null || context.getMergeSkuType() < 0) {
            return;
        }
        List<TradeMergeItemsVo> mergeItemList = tradeMergeConfBusiness.getMergeOuterIdList(staff, context.getTradeMergeConf());
        if (CollectionUtils.isEmpty(mergeItemList)) {
            return;
        }
        Set<String> mergeOuterIds = mergeItemList.stream().map(TradeMergeItemsVo::getSkuOuterId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mergeOuterIds)) {
            return;
        }
        context.setMergeOuterIds(mergeOuterIds);
    }

    private void fillMergeOuterIds(Staff staff, TradeMergeContext context, Map<Long, Integer> mergeTypeRuleIdsMap) {
        mergeTypeRuleIdsMap.forEach((ruleId, mergeSkuType) -> {
            TradeMergeConf tradeMergeConf = new TradeMergeConf();
            tradeMergeConf.setId(ruleId);
            tradeMergeConf.setMergeSkuType(mergeSkuType);
            List<TradeMergeItemsVo> mergeItemList = tradeMergeConfBusiness.getMergeOuterIdList(staff, tradeMergeConf);
            Set<String> mergeOuterIds = mergeItemList.stream().map(TradeMergeItemsVo::getSkuOuterId).collect(Collectors.toSet());
            context.getMergeOuterIdsMap().computeIfAbsent(mergeSkuType, k -> new HashSet<>()).addAll(mergeOuterIds);
        });
    }

    /**
     * 商品分组（不同的商品分组不允许合并）
     */
    private void handleMergeItemGroup(Staff staff, TradeMergeContext context, TradeMergeConf mergeConf) {
        if (mergeConf != null && mergeConf.getItemGroup() != null && mergeConf.getItemGroup() == 1) {
            context.setMergeItemGroups(mergeItemGroupService.query(staff, MergeItemGroupQuery.builder().userId(mergeConf.getUserId()).open(1).fillDetail(true).build()));
        }

    }


    /**
     * 根据店铺id获取店铺信息
     *
     * @param userId 店铺id
     * @return 店铺
     */
    public User getUser(Staff staff, long userId) {
        if (staff.getUserIdMap() != null && staff.getUserIdMap().containsKey(userId)) {
            return staff.getUserIdMap().get(userId);
        }
        List<User> users = staff.getUsers();
        if (CollectionUtils.isNotEmpty(users)) {
            for (User staffUser : users) {
                if (staffUser.getId() - userId == 0) {
                    return staffUser;
                }
            }
        }
        return userService.queryById(userId);
    }

    /**
     * 多店铺的配置合并交集配置
     */
    public TradeMergeConf mergeMergeConf(List<TradeMergeConf> tradeMergeConfs) {
        if (CollectionUtils.isEmpty(tradeMergeConfs)) {
            return null;
        }
        List<Long> userIds = new ArrayList<>();

        if (tradeMergeConfs.size() == 1) {
            TradeMergeConf conf = tradeMergeConfs.get(0);
            conf.setUserIds(userIds);
            return conf;
        }
        int mamFilterExcep = 1;
        int mamFilterSplit = 1;
        int mamFilterUndo = 1;
        int mamFilterFinishAudit = 1;
        int enableMergeTmOrSfTrade = 1;
        int enableMergeTag = 1;
        boolean isTags = true;
        int mergePresellPlatform = 0;
        int platConf = 0;
        Map<Long, Integer> mergeRuleIdSkuTypeMap = new HashMap<>();
        List<String> tags = new ArrayList<>();
        for (TradeMergeConf conf : tradeMergeConfs) {
            if (conf == null || CommonConstants.PLAT_FORM_TYPE_SYS.equals(conf.getSource())) {
                continue;
            }
            platConf++;
            userIds.add(conf.getUserId());
            if (conf.getMamFilterExcep() == null || conf.getMamFilterExcep().equals(0)) {
                mamFilterExcep = 0;
            }
            if (conf.getMamFilterSplit() == null || conf.getMamFilterSplit().equals(0)) {
                mamFilterSplit = 0;
            }
            if (conf.getMamFilterUndo() == null || conf.getMamFilterUndo().equals(0)) {
                mamFilterUndo = 0;
            }
            if (conf.getMamFilterFinishAudit() == null || conf.getMamFilterFinishAudit().equals(0)) {
                mamFilterFinishAudit = 0;
            }
            if (conf.getEnableMergeTmOrSfTrade() == null || Objects.equals(conf.getEnableMergeTmOrSfTrade(), 0)) {
                enableMergeTmOrSfTrade = 0;
            }
            if (conf.getEnableMergeTag() == null || Objects.equals(conf.getEnableMergeTag(), 0)) {
                enableMergeTag = 0;
            }
            // 指定合单的标签取交集
            if (StringUtils.isBlank(conf.getTagIds())) {
                isTags = false;
            } else {
                tags.add(conf.getTagIds());
            }
            if (conf.getMergePresellNorlMal() != null && conf.getMergePresellNorlMal() == 1) {
                mergePresellPlatform++;
            }
            if (Objects.nonNull(conf.getMergeSkuType()) && conf.getMergeSkuType() > 0){
                mergeRuleIdSkuTypeMap.put(conf.getId(), conf.getMergeSkuType());
            }
        }
        TradeMergeConf conf = new TradeMergeConf();
        conf.setMamFilterExcep(mamFilterExcep);
        conf.setMamFilterSplit(mamFilterSplit);
        conf.setMamFilterUndo(mamFilterUndo);
        conf.setMamFilterFinishAudit(mamFilterFinishAudit);
        conf.setUserIds(userIds);
        conf.setEnableMergeTmOrSfTrade(enableMergeTmOrSfTrade);
        conf.setEnableMergeTag(enableMergeTag);
        conf.setMergeRuleIdSkuTypeMap(mergeRuleIdSkuTypeMap);
        if (isTags && CollectionUtils.isNotEmpty(tags)) {
            conf.setTagIds(mergeTags(tags));
        }
        conf.setMergePresellNorlMal(mergePresellPlatform > 0 && mergePresellPlatform == platConf ? 1 : 0);
        return conf;
    }

    private String mergeTags(List<String> tags) {
        List<List<String>> tagLists = new ArrayList<>();
        for (String tag : tags) {
            if (StringUtils.isBlank(tag)) {
                return "";
            }
            String[] split = tag.split(",|，");
            tagLists.add(Arrays.asList(split));
        }
        List<String> intersection = intersection(tagLists);
        return String.join(",", intersection);
    }

    /**
     * 取多个集合的交集
     */
    private List<String> intersection(List<List<String>> tags) {
        List<String> rst = tags.get(0);
        for (List<String> next : tags) {
            rst = rst.stream().filter(e -> next.contains(e)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(rst)) {
                /// rst 为空不存在交集，没必要继续走下去了
                break;
            }
        }
        return rst;
    }
}

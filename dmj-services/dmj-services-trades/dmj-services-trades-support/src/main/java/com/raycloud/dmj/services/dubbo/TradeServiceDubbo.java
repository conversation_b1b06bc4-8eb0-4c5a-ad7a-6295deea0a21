package com.raycloud.dmj.services.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.*;
import com.google.common.base.Function;
import com.google.common.collect.*;
import com.raycloud.dmj.*;
import com.raycloud.dmj.base.*;
import com.raycloud.dmj.business.audit.AuditInsufficientBusiness;
import com.raycloud.dmj.business.buyout.*;
import com.raycloud.dmj.business.clear.*;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.export.TradeOrderExportBusiness;
import com.raycloud.dmj.business.fx.*;
import com.raycloud.dmj.business.item.*;
import com.raycloud.dmj.business.logistics.*;
import com.raycloud.dmj.business.modify.*;
import com.raycloud.dmj.business.operate.*;
import com.raycloud.dmj.business.part3.TradeParty3Business;
import com.raycloud.dmj.business.payment.support.check.PaymentCheckService;
import com.raycloud.dmj.business.query.*;
import com.raycloud.dmj.business.split.*;
import com.raycloud.dmj.business.split.filter.ITradeSplitFilterService;
import com.raycloud.dmj.business.split.support.SplitParams;
import com.raycloud.dmj.business.stalls.SaleTradeBusiness;
import com.raycloud.dmj.business.tag.*;
import com.raycloud.dmj.business.trade.*;
import com.raycloud.dmj.business.warehouse.WarehouseAllocateBusiness;
import com.raycloud.dmj.business.wave.split.*;
import com.raycloud.dmj.business.wms.TradeWmsOpenValidateBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.performance.TradePickDao;
import com.raycloud.dmj.dao.trade.*;
import com.raycloud.dmj.dao.wave.WaveSortingDao;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.PageList;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.buyout.*;
import com.raycloud.dmj.domain.check.DeleteReceipt;
import com.raycloud.dmj.domain.consign.*;
import com.raycloud.dmj.domain.constant.SystemExcepts;
import com.raycloud.dmj.domain.dubbo.TradeUpdateResponse;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.fms.*;
import com.raycloud.dmj.domain.item.*;
import com.raycloud.dmj.domain.party3.TradeParty3Response;
import com.raycloud.dmj.domain.performance.TradePick;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.pt.enums.EnumTemplateKind;
import com.raycloud.dmj.domain.pt.model.print.*;
import com.raycloud.dmj.domain.pt.wlb.*;
import com.raycloud.dmj.domain.stalls.*;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.config.*;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.invoice.*;
import com.raycloud.dmj.domain.trade.label.TradeLabelOpEnum;
import com.raycloud.dmj.domain.trade.memo.*;
import com.raycloud.dmj.domain.trade.order.*;
import com.raycloud.dmj.domain.trade.rule.*;
import com.raycloud.dmj.domain.trade.rule.history.TradeRuleHistory;
import com.raycloud.dmj.domain.trade.split.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.consign.ExternalConsignRequest;
import com.raycloud.dmj.domain.trades.export.TradeExcelExportTask;
import com.raycloud.dmj.domain.trades.params.*;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.request.ModifyUnAuditTradeReceiveAddressRequest;
import com.raycloud.dmj.domain.trades.search.*;
import com.raycloud.dmj.domain.trades.search.ItemParams;
import com.raycloud.dmj.domain.trades.search.ItemQueryParams;
import com.raycloud.dmj.domain.trades.search.dubbo.AssembleParams;
import com.raycloud.dmj.domain.trades.search.utils.QueryLogBuilder;
import com.raycloud.dmj.domain.trades.split.*;
import com.raycloud.dmj.domain.trades.tradepay.TradePayVO;
import com.raycloud.dmj.domain.trades.tradepay.params.TradePayGetParams;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.vip.*;
import com.raycloud.dmj.domain.trades.vo.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.wave.*;
import com.raycloud.dmj.domain.wave.enums.WaveChatConfigsEnum;
import com.raycloud.dmj.domain.wave.model.*;
import com.raycloud.dmj.domain.wave.params.*;
import com.raycloud.dmj.domain.wave.utils.WaveTraceUtils;
import com.raycloud.dmj.dto.audit.AuditResultDTO;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyService;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.product.domain.OrderStockProduct;
import com.raycloud.dmj.print.api.enums.TradePrintEntranceEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.dubbo.dto.request.*;
import com.raycloud.dmj.services.dubbo.dto.response.FreightSettlementResp;
import com.raycloud.dmj.services.dubbo.dto.response.trade.CheckReceiptResp;
import com.raycloud.dmj.services.ec.itemsku.ItemShutoffListener;
import com.raycloud.dmj.services.filter.support.*;
import com.raycloud.dmj.services.item.*;
import com.raycloud.dmj.services.pt.*;
import com.raycloud.dmj.services.sensitive.DataSecurityHandleService;
import com.raycloud.dmj.services.stalls.*;
import com.raycloud.dmj.services.tag.ITradeTagService;
import com.raycloud.dmj.services.trade.audit.*;
import com.raycloud.dmj.services.trade.item.IEntityCodeConvertService;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trade.merge.ITradeMergeService;
import com.raycloud.dmj.services.trade.order.*;
import com.raycloud.dmj.services.trade.rule.ITradeRuleService;
import com.raycloud.dmj.services.trade.split.ITradeSplitService;
import com.raycloud.dmj.services.trade.warehouse.TradeWarehouseChangeBusiness;
import com.raycloud.dmj.services.tradepay.ITradePayService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.trades.deliverylimit.*;
import com.raycloud.dmj.services.trades.fill.TradeExtFill;
import com.raycloud.dmj.services.trades.filter.*;
import com.raycloud.dmj.services.trades.support.*;
import com.raycloud.dmj.services.trades.vip.IVipDeliveryDataService;
import com.raycloud.dmj.services.trades.wave.*;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.stalls.infrastructure.dao.SaleTradeDao;
import com.raycloud.dmj.template.base.SearchTemplateParams;
import com.raycloud.dmj.template.wlb.SearchWlbTemplateParams;
import com.raycloud.dmj.utils.CommonSecretUtils;
import com.raycloud.ec.api.*;
import org.apache.commons.collections.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trade.common.TradeBusinessFromEnum.*;
import static com.raycloud.dmj.except.enums.ExceptEnum.*;
import static com.raycloud.dmj.services.utils.LogKit.*;


/**
 * 交易模块对外提供的dubbo服务接口实现
 * Created by CXW on 16/5/31.
 */
@Service("dubboTradeService")
public class TradeServiceDubbo implements ITradeServiceDubbo {

    private static Logger logger = Logger.getLogger(TradeServiceDubbo.class);

    @Resource(name = "solrTradeSearchService")
    AbsTradeSearchService tradeSearchService;
    @Resource
    ITradeConfigNewService tradeConfigNewService;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    ITradeStaffConfigService tradeStaffConfigService;
    @Resource
    TbOrderDAO tbOrderDAO;
    @Resource
    ITradePtService tradePtService;

    @Resource
    TbTradeDao tbTradeDao;
    @Resource
    TradeExtFill tradeExtFill;

    @Resource
    IPrintService printAdapterService;

    @Resource
    ISysTradeService sysTradeService;

    @Resource
    Clear3MonthBeforeDataBusiness clear3MonthBeforeDataBusiness;

    @Resource
    ClearAllDataBusiness clearAllDataBusiness;

    @Autowired
    ITradeWaveService tradeWaveService;

    @Autowired
    ITradePostPrintService tradePostPrintService;

    @Resource
    IFreightTemplateService freightTemplateService;

    @Resource
    ITradeImportService tradeImportService;

    @Resource
    CacheBusiness cacheBusiness;

    @Resource
    IStaffService staffService;

    @Resource
    ITradeInspectService tradeInspectService;

    @Resource
    LogisticsOfflineBusiness logisticsOfflineBusiness;

    @Resource
    ITradeRecoverService tradeRecoverService;

    @Resource
    IUserService userService;

    @Resource
    IItemCheckExistService itemCheckExistService;

    @Resource
    IEventCenter eventCenter;

    @Resource
    TradeSqlCountService tradeSqlCountService;
    @Resource
    TradeCountBusiness tradeCountBusiness;
    @Resource
    private ITradeFilterService tradeFilterService;

    @Resource
    WaveSortingDao waveSortingDao;

    @Resource
    CalculateBusiness calculateBusiness;

    @Resource
    TradeWmsOpenValidateBusiness tradeWmsOpenValidateBusiness;

    @Resource
    FastOutBusiness fastOutBusiness;

    @Resource
    ITradeService tradeService;
    @Resource
    ITradeAuditService tradeAuditService;
    @Resource
    ITradeQueryService tradeQueryService;
    @Resource
    ITradeWeighConfigService tradeWeighConfigService;
    @Resource
    ExportBusiness exportBusiness;
    @Resource
    TradeUpdateSellerMemoFlagBusiness tradeUpdateSellerMemoFlagBusiness;
    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    private IPickBackService pickBackService;

    @Resource
    private SaleTradeDao saleTradeDao;

    @Resource
    private ITradeTraceService tradeTraceService;

    @Resource
    private IWaveSplitGenericService waveSplitGenericService;

    @Resource
    private OrderUniqueCodeNewSplitBusiness orderUniqueCodeNewSplitBusiness;

    @Resource
    private TradePickDao tradePickDao;
    @Resource
    private ITradeTagService tradeTagService;

    @Resource
    private TradeValidateBusiness tradeValidateBusiness;

    @Resource
    private ISaleTradeService saleTradeService;

    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;

    @Resource
    TradeTraceBusiness tradeTraceBusiness;

    @Resource
    private IExpressCompanyService expressCompanyService;

    @Resource
    IFreightTemplateVolumeService freightTemplateVolumeService;

    @Resource
    DeliveryLimitBusiness deliveryLimitBusiness;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    ISaleOrderService saleOrderService;

    @Resource
    private TbTradeDecryptBusiness tbTradeDecryptBusiness;

    @Resource
    private TradePddTradeBusiness tradePddTradeBusiness;
    @Resource
    private FxgTradeDecryptBusiness fxgTradeDecryptBusiness;
    @Resource
    private CommonTradeDecryptBusiness commonTradeDecryptBusiness;

    @Resource
    private IShareUrlService shareUrlService;
    @Resource
    private TradeExceptBusiness tradeExceptBusiness;

    @Resource
    private TradeCheckGoodsDao tradeCheckGoodsDao;


    @Resource
    private TradeCustomDao tradeCustomDao;

    @Resource
    private SaleTradeBusiness saleTradeBusiness;

    @Resource
    ITradePayService tradePayService;

    @Resource
    IUniqueCodeScanRecordService uniqueCodeScanRecordService;

    @Resource
    private IItemUniqueCodeService iItemUniqueCodeService;

    @Resource
    ITradeSplitService tradeSplitService;

    @Resource
    private SplitAllInOneBusiness splitAllInOneBusiness;

    @Resource
    ITradeItemPackService tradeItemPackService;

    @Resource
    IItemServiceWrapper itemServiceWrapper;

    @Resource
    TradeParty3Business tradeParty3Business;

    @Resource
    WarehouseAllocateBusiness warehouseAllocateBusiness;

    @Resource
    private CancelInsufficientBusiness cancelInsufficientBusiness;

    @Resource
    private StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    private DataSecurityHandleService dataSecurityHandleService;

    @Resource
    private ICheckGoodsServicePc checkGoodsServicePc;


    @Resource
    ShipBoxWaveSplitBusiness shipBoxWaveSplitBusiness;

    @Resource
    private IUserExpressTemplateService templateService;

    @Resource
    private IUserWlbExpressTemplateService wlbTemplateService;
    @Resource
    private IProgressService progressService;
    @Resource
    private TradeOpenUidTransformBusiness tradeOpenUidTransformBusiness;

    @Resource
    private IWaveTraceService waveTraceService;

    @Resource
    private ITradeDataService tradeDataService;

    @Resource
    private UnionTradeDecryptBusiness unionTradeDecryptBusiness;

    @Resource
    private TradeParty3ConsignBusiness tradeParty3ConsignBusiness;

    @Resource
    private IOrderProductService orderProductService;
    @Resource
    private IUserWlbExpressTemplateService userWlbExpressTemplateService;
    @Resource
    private IUserExpressTemplateService userExpressTemplateService;
    @Resource
    private IOrderUniqueCodePositionAnalyseService orderUniqueCodePositionAnalyseService;

    @Resource
    private IUserLogisticsCompanyService userLogisticsCompanyService;
    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;
    @Resource
    CattleTradeBusiness cattleTradeBusiness;

    @Resource
    ITradeItemReplaceRuleService tradeItemReplaceRuleService;

    @Resource
    IVipDeliveryDataService iVipDeliveryDataService;

    @Resource
    ILockService lockService;

    @Resource
    TradeLockBusiness tradeLockBusiness;

    @Resource
    ITradeInvoiceService tradeInvoiceService;

    @Resource
    ITradeMergeService tradeMergeService;

    @Resource
    ITradeRuleService tradeRuleService;

    @Resource
    ITradeCancelExceptService tradeCancelExceptService;

    @Resource
    ITradeSplitFilterService tradeSplitFilterService;

    @Resource
    SecretBusiness secretBusiness;

    @Resource
    TradeUpdateBusiness tradeUpdateBusiness;

    @Resource
    TagNameFilter tagNameFilter;
    @Resource
    DefaultTemplateFilter defaultTemplateFilter;
    @Resource
    IEntityCodeConvertService entityCodeConvertService;

    @Resource
    ItemShutoffListener itemShutoffListener;

    @Resource
    TradeDubboCancelBusiness tradeDubboCancelBusiness;
    @Resource
    TradeDubboCloseBusiness tradeDubboCloseBusiness;

    @Resource
    TradeTagBusiness tradeTagBusiness;

    @Resource
    Order2SingleService order2SingleService;

    @Resource
    ConsignAuditedBusiness consignAuditedBusiness;
    @Resource
    UploadBusiness uploadBusiness;
    @Resource
    ConsignRecordDao consignRecordDao;
    @Resource
    IConsignRecordService consignRecordService;
    @Resource
    TradeOrderExportBusiness tradeOrderExportBusiness;
    @Resource
    TradeExportBusiness tradeExportBusiness;

    @Resource
    SimulateImportGxTradeBusiness simulateImportGxTradeBusiness;
    @Resource
    OrderProductServiceImpl orderProductServiceImpl;
    @Resource
    TradeDownloadService tradeDownloadService;

    @Resource
    TradeAssembleBusiness tradeAssembleBusiness;
    @Resource
    OrderTypeChangLogService orderTypeChangLogService;

    @Resource
    OfflineCombineParcelServiceImpl offlineCombineParcelService;
    @Resource
    TradeAuditDubboBusiness tradeAuditDubboBusiness;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    TradeWarehouseChangeBusiness tradeWarehouseChangeBusiness;

    @Resource
    IWaveServiceDubbo waveServiceDubbo;

    @Resource
    FxMemoBusiness fxMemoBusiness;

    @Resource
    private AuditInsufficientBusiness auditInsufficientBusiness;


    @Resource
    protected ITradeSplitOldService tradeSplitOldService;

    @Resource
    protected OrderChangeItemRecordBusiness orderChangeItemRecordBusiness;

    @Resource
    protected ChangeItemByRatioBusiness changeItemByRatioBusiness;

    @Resource
    private TradeItemChangeBusiness tradeItemChangeBusiness;


    public List<Trade> updateSellerMemo(Staff staff, String memo, Long flag, boolean append, Long... sids) {
        return updateSellerMemo(staff, TradeMemo.builder().handType(TradeMemoTypeEnum.PLAT_SELLER_EMEO_FLAG.getType()).sellerMemo(memo).sellerFlag(flag).append(append).handFrom(TradeMemoFromEnum.HAND_FROM_DUBBO.getType()).build(), sids);
    }

    @Override
    public List<Trade> updateSellerMemo(UpdateSellerMemoRequest r) {
        return updateSellerMemo(r.staff, TradeMemo.builder().handType(TradeMemoTypeEnum.PLAT_SELLER_EMEO_FLAG.getType()).sellerMemo(r.memo).sellerFlag(r.flag).append(r.append).handFrom(TradeMemoFromEnum.HAND_FROM_DUBBO_AFTER.getType()).handleMergeTrade(r.handleMergeTrade).notifyFx(r.notifyFx).build(), r.sids);
    }

    @Override
    public List<Trade> updateSellerMemo(Staff staff, TradeMemo tradeMemo, Long... sids) {
        if (Objects.nonNull(tradeMemo) && Boolean.TRUE.equals(tradeMemo.getNotifyFx()) && Objects.nonNull(sids) && sids.length > 0){
            fxMemoBusiness.notifySellerMemo2Fx(staff, tradeMemo, sids[0]);
        }
        return tradeUpdateSellerMemoFlagBusiness.update(staff, tradeMemo, sids).getUpdatedTrades();
    }

    @Override
    public List<TbOrder> queryOrders(Staff staff, Long... orderIds) {
        return took(() -> tbOrderDAO.queryByIds(staff, orderIds), staff, logger);
    }

    @Override
    public List<Order> listSuits(Staff staff, Long orderId) {
        return tradeSearchService.listSuits(staff, orderId);
    }

    @Override
    public void updateTradePrinted(Staff staff, Long[] sidArray) {
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, sidArray);
        List<Long> sidsOfTemp0 = new ArrayList<Long>();
        List<Long> sidsOfTemp1 = new ArrayList<Long>();
        for (Trade trade : trades) {
            Integer templateType = trade.getTemplateType();
            if (0 == templateType.intValue()) {
                sidsOfTemp0.add(trade.getSid());
            } else if (1 == templateType.intValue()) {
                sidsOfTemp1.add(trade.getSid());
            }
        }
        if (sidsOfTemp0.size() > 0) {
            Long[] sid0Array = sidsOfTemp0.toArray(new Long[sidsOfTemp0.size()]);

            IPrintRequest printRequest = new EndPrintTimeRequest(sid0Array, "奇门", null);
            printRequest.setTemplateKind(EnumTemplateKind.EXPRESS.getValue());
            printAdapterService.endPrintTime(staff, printRequest);
        }
        if (sidsOfTemp1.size() > 0) {
            Long[] sid1Array = sidsOfTemp1.toArray(new Long[sidsOfTemp1.size()]);

            IPrintRequest printRequest = new EndPrintTimeRequest(sid1Array, "奇门", null);
            printRequest.setTemplateKind(EnumTemplateKind.WLB.getValue());
            printAdapterService.endPrintTime(staff, printRequest);
        }
    }

    /**
     * 为第三方业务提供的更新订单接口，目前仅更新:outSid,weight，以及order的识别码
     *
     * @param staff
     * @param trades
     */
    public void updateTrade4Party3(Staff staff, List<Trade> trades) {
        took(() -> {
            sysTradeService.updateTrade4Party3(staff, trades);
            return true;
        }, staff, logger);
    }

    @Override
    public boolean checkDeliveryLimit(Staff staff, Trade trade, String cpCode) {
        try {
            if (StringUtils.isBlank(trade.getOutSid()) && StringUtils.isBlank(trade.getSource())) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLogHead(staff).append(String.format("第三方仓发货校验不通过: 运单号或者订单来源不能为空[%s],无法发货", trade.getSid())));
                }
                return false;
            }
            return deliveryLimitBusiness.checkDeliveryLimit(trade.getSource(), cpCode, trade.getOutSid());
        } catch (DeliveryLimitException e) {
            ConsignData consignData = buildConsignData(staff, trade, e.getMessage());
            //更新发货异常,发事件记录发货异常日志
            updateDeliverExecp(staff, consignData.result, consignData.deliverExecpTrades, Lists.newArrayList(trade));
            return false;
        }
    }

    private ConsignData buildConsignData(Staff staff, Trade trade, String errMsg) {
        ConsignData consignData = new ConsignData();
        ConsignRecord record = ConsignUtils.buildFullRecord(trade, SendType.ONLINE, 1, 1);
        record.setConsigned(new Date());
        record.setBuyType(staff.getCompany().getOrderType());
        record.setMergeSid(trade.getMergeSid());
        record.setErrorDesc(errMsg);
        consignData.result.add(record);
        consignData.deliverExecpTrades.put(trade.getSid(), errMsg);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(staff).append(String.format("第三方仓发货校验不通过: %s[%s],无法发货", errMsg, trade.getSid())));
        }
        return consignData;
    }

    private void updateDeliverExecp(Staff staff, List<ConsignRecord> result, Map<Long, String> deliverExecpTrades, List<Trade> trades) {
        List<ConsignRecord> excepRecord = result.stream().filter(c -> c.getIsError() == 1).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(excepRecord)) {
            List<Trade> updates = new ArrayList<>();
            List<Trade> origins = new ArrayList<>();
            Map<Long, Trade> tradeMap = trades.stream().collect(Collectors.toMap(t -> t.getSid(), t -> t, (k1, k2) -> k2));
            for (ConsignRecord consignRecord : excepRecord) {
                Trade origin = tradeMap.get(consignRecord.getSid());
                //如果该订单已发货则不标记异常
                if (origin != null && !TradeUtils.isAfterSendGoods(origin)) {
                    Trade trade = TradeBuilderUtils.builderUpdateItemExcep(origin);
                    trade.setSid(origin.getSid());
                    trade.setCompanyId(origin.getCompanyId());
                    //  trade.setItemExcep(origin.getItemExcep() == null ? TradeConstants.DELIVER_EXCEPT : (origin.getItemExcep() | TradeConstants.DELIVER_EXCEPT));
                    TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.DELIVER_EXCEPT, 1L);
                    trade.setIsExcep(1);
                    updates.add(trade);
                    origins.add(origin);
                }
            }
            tradeUpdateService.updateTrades(staff, updates);
            eventCenter.fireEvent(this, new EventInfo("trade.consign.exception").setArgs(new Object[]{staff, deliverExecpTrades}), origins);
        }
    }

    @Override
    public Map<Long, String> pack(Staff staff, Long... sids) {
        return took(() -> sysTradeService.packTrades(staff, sids), staff, logger);
    }

    @Override
    public Map<Long, String> weigh(Staff staff, Long... sids) {
        return took(() -> sysTradeService.weightTrades(staff, sids), staff, logger);
    }

    @Override
    public List<Trade> cancel(Staff staff, Long... sids) {
        return sysTradeService.cancel(staff, sids);
    }

    @Override
    public List<Trade> asCancel(Staff staff, Long... sids) {
        return tradeDubboCancelBusiness.cancel(staff, sids, FROM_AS);
    }

    @Override
    public void closeTrade(Staff staff, List<Long> sids) {
        if (CollectionUtils.isNotEmpty(sids)) {
            tradeDubboCloseBusiness.close(staff, sids.toArray(new Long[0]), FROM_AS);
        }
    }

    @Override
    public List<Trade> kmtCancel(Staff staff, Long... sids) {
        return tradeDubboCancelBusiness.cancel(staff, sids, FROM_KMT);
    }

    @Override
    public Map<String, String> checkTradeCancelByFx(Staff staff, Long... sids) {
        return sysTradeService.checkTradeCancelByFx(staff, sids);
    }

    @Override
    public Trades search(Staff staff, TradeQueryParams params) {
        return took(() -> {
            Assert.notNull(params,"查询条件 params不能为空");
            params.getContext().setClientType("dubbo");
            DubboLogBuilder logBuilder = new DubboLogBuilder(staff.getCompanyId(), LogBusinessEnum.QUERY.getSign()).appendMethod("search").append("查询参数").appendJSon(params).startTimer();
            params.getContext().setLogBuilder(logBuilder);
            Trades trades = tradeSearchService.search(staff, params);
            if (CollectionUtils.isEmpty(trades.getList())) {
                return trades;
            }
            //通过TJ进入模糊敏感信息
            if (!org.apache.commons.lang3.StringUtils.isEmpty(staff.getShadowToken()) && "2".equals(staff.getIsVague()) &&
                    !CollectionUtils.isEmpty(trades.getList())) {
                TradeUtils.blurTrades(trades.getList());
                logBuilder.recordTimer("blurTrades");
            }
            if (!Objects.isNull(params) && params.isShowSysItemOuterId()) {
                fillItemInfo(staff, trades.getList());
                logBuilder.recordTimer("fillItemInfo");
            }
            if (!Objects.isNull(params) && params.isNeedUniqueCode()) {
                fillOrderUniqueCode(staff, trades.getList());
                logBuilder.recordTimer("fillOrderUniqueCode");
            }
            if (!Objects.isNull(params) && params.isNeedTemplateName()) {
                tradeAssembleBusiness.fillTemplate(staff, trades.getList());
                logBuilder.recordTimer("fillTemplate");
            }
            if (StringUtils.equals("1", params.getSensitive())) {
                trades.getList().forEach(t -> {
                    t.setOriReceiverAddress(t.getReceiverAddress());
                    t.setOriReceiverMobile(t.getReceiverMobile());
                    t.setOriReceiverName(t.getReceiverName());
                });
                tradePddTradeBusiness.pddMaskDataReplace(staff, trades.getList());
                fxgTradeDecryptBusiness.batchSensitive(staff, trades.getList());
                commonTradeDecryptBusiness.batchSensitive(staff, trades.getList());
                logBuilder.recordTimer("sensitive");
            }
            logBuilder.startWatch().appendTook((params.getDebug() !=null && params.getDebug())?1L: 8000L).multiPrintDebug(logger);
            return trades;
        }, staff, logger);
    }

    /**
     * 填充模板和快递公司
     */
    private void fillTemplate(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        Map<Long, UserWlbExpressTemplate> userWlbExpressTemplateMap = new HashMap<>();
        Map<Long, UserExpressTemplate> userExpressTemplateMap = new HashMap<>();
        Set<Long> expressCompanyIds = new HashSet<>();
        for (Trade trade : trades) {
            if (Integer.valueOf(1).equals(trade.getTemplateType())) {
                UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateMap.get(trade.getTemplateId());
                if (userWlbExpressTemplate == null) {
                    userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, trade.getTemplateId(), false);
                    if (userWlbExpressTemplate == null) {
                        continue;
                    }
                    userWlbExpressTemplateMap.put(trade.getTemplateId(), userWlbExpressTemplate);
                }
                trade.setTemplateName(userWlbExpressTemplate.getName());
                trade.setExpressCompanyId(userWlbExpressTemplate.getExpressId());
                expressCompanyIds.add(userWlbExpressTemplate.getExpressId());
            } else {
                UserExpressTemplate userExpressTemplate = userExpressTemplateMap.get(trade.getTemplateId());
                if (userExpressTemplate == null) {
                    userExpressTemplate = userExpressTemplateService.userQuery(staff, trade.getTemplateId(), false);
                    if (userExpressTemplate == null) {
                        continue;
                    }
                    userExpressTemplateMap.put(trade.getTemplateId(), userExpressTemplate);
                }
                trade.setTemplateName(userExpressTemplate.getName());
                trade.setExpressCompanyId(userExpressTemplate.getExpressId());
                expressCompanyIds.add(userExpressTemplate.getExpressId());
            }
        }
        List<ExpressCompany> expressCompanies = expressCompanyService.getExpressCompanyByIds(expressCompanyIds.toArray(new Long[0]));
        Map<Long, ExpressCompany> expressCompanyMap = expressCompanies.stream().collect(Collectors.toMap(ExpressCompany::getId, v -> v, (v1, v2) -> v1));
        for (Trade trade : trades) {
            ExpressCompany expressCompany = expressCompanyMap.get(trade.getExpressCompanyId());
            if (expressCompany == null) {
                continue;
            }
            trade.setExpressCode(expressCompany.getCode());
            trade.setExpressName(expressCompany.getName());
        }
    }

    private void fillItemInfo(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Long> sysItemIds = new ArrayList<Long>();
        //如果纯商品Id<0那么是纯商品
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                //判断是不是系统商品
                if (order.getItemSysId() > 0) {
                    sysItemIds.add(order.getItemSysId());
                }
                List<Order> suits = order.getSuits();
                if (suits != null && !suits.isEmpty()) {
                    for (Order suit : suits) {
                        if (suit.getItemSysId() > 0) {
                            sysItemIds.add(suit.getItemSysId());
                        }
                    }
                }
            }
        }

        Map<Long, SysItemSku> map = new HashMap<Long, SysItemSku>();
        if (!sysItemIds.isEmpty()) {//纯商品
            List<List<Long>> partition = Lists.partition(sysItemIds, 50);
            for (List<Long> itemIds : partition) {
                List<DmjItem> itemList = itemServiceWrapper.queryBySysItemIds(staff, itemIds, "sysItemId,outerId");
                for (DmjItem item : itemList) {
                    map.put(item.getSysItemId(), buildItemSku(item.getSysItemId(), null, item.getOuterId(), item.getBarcode()));
                }
            }
        }
        //放入order返回
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order o : orders) {
                SysItemSku target = map.get(o.getItemSysId());
                if (target != null) {
                    o.setSysItemOuterId(target.getOuterId());
                }
                List<Order> suits = o.getSuits();
                if (suits != null && !suits.isEmpty()) {
                    for (Order suit : suits) {
                        Long suitItemSysId = suit.getItemSysId();
                        if (suitItemSysId == null || suitItemSysId <= 0) {
                            continue;
                        }
                        SysItemSku suitSysItemSku = map.get(suitItemSysId);
                        if (suitSysItemSku != null) {
                            suit.setSysItemOuterId(suitSysItemSku.getOuterId());
                        }
                    }
                }
            }
        }
    }

    private SysItemSku buildItemSku(Long sysItemId, Long sysSkuId, String outerId, String barcode) {
        SysItemSku itemSku = new SysItemSku();
        itemSku.setSysItemId(sysItemId);
        itemSku.setSysSkuId(sysSkuId);
        itemSku.setOuterId(outerId);
        itemSku.setBarcode(barcode);
        return itemSku;
    }

    public void fillOrderUniqueCode(Staff staff, List<Trade> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        OrderUniqueCodeQueryParams queryParams = new OrderUniqueCodeExportParams();
        queryParams.setSids(TradeUtils.toSidList(list));
        List<WaveUniqueCode> uniqueCodeList = queryOrderUniqueCode(staff, queryParams);
        if (CollectionUtils.isEmpty(uniqueCodeList)) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("填充order唯一码时，从数据库中查询到的数据集为空。trade sids:%s", TradeUtils.toSidList(list))));
            }
            return;
        }

        Map<Long, List<WaveUniqueCode>> orderUniqueCodeMap = new HashMap<>();
        for (WaveUniqueCode waveUniqueCode : uniqueCodeList) {
            List<WaveUniqueCode> uniqueCodes = orderUniqueCodeMap.get(waveUniqueCode.getOrderId());
            if (CollectionUtils.isEmpty(uniqueCodes)) {
                uniqueCodes = Lists.newArrayList();
                orderUniqueCodeMap.put(waveUniqueCode.getOrderId(), uniqueCodes);
            }
            uniqueCodes.add(waveUniqueCode);
        }

        for (Trade trade : list) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isEmpty(orders)) {
                continue;
            }
            for (Order order : orders) {
                List<WaveUniqueCode> orderUniqueCodeList = orderUniqueCodeMap.get(order.getId());
                if (CollectionUtils.isNotEmpty(orderUniqueCodeList)) {
                    order.setUniqueCodes(orderUniqueCodeList.stream().map(WaveUniqueCode::getUniqueCode).collect(Collectors.toList()));
                }
            }
        }
    }

    @Override
    public TradeImportResult syncTrades(Staff staff, Long userId, Date start, Date end, String status, Integer syncType) throws Exception {
        User user = getUser(staff, userId);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(user).append(String.format("管理员同步订单[%s, %s, %s, %s, %s]", user.getId(), user.getNick(), DateUtils.datetime2Str(start), DateUtils.datetime2Str(end), status)));
        }
        return tradeImportService.importTrades(user, start, end, status, syncType == null ? TradeConstants.SYNC_FULL_API : syncType);
    }

    @Override
    public List<ConsignRecord> offlineConsign(Staff staff, Long[] sids) {
        ConsignParams params = ConsignParams.builder().sids(sids).consignType(SendType.OFFLINE).build();
        return logisticsOfflineBusiness.consign(staff, params);
    }

    @Override
    public List<ConsignRecord> dummyConsign(Staff staff, Long[] sids) {
        ConsignParams params = ConsignParams.builder().sids(sids).consignType(SendType.DUMMY).build();
        return logisticsOfflineBusiness.consign(staff, params);
    }

    @Override
    public TradeImportResult syncTradesByTid(Staff staff, Long userId, String... tids) {
        User user = getUser(staff, userId);
        if (tids == null || tids.length == 0) {
            throw new IllegalArgumentException("请指定平台订单号!");
        }
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(user).append(String.format("按订单号下载订单[%s, %s, %s, [%s]]", user.getSource(), user.getId(), user.getNick(), Strings.join(",", tids))));
        }
        return tradeImportService.syncTradesByTid(user, tids);
    }

    private User getUser(Staff staff, Long userId) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("请指定要同步订单的店铺！");
        }
        User user = staff.getUserByUserId(userId);
        if (user == null) {
            throw new IllegalArgumentException("您没有该店铺的权限！");
        }
        return user;
    }

    @Override
    public boolean checkTradeByItem(Staff staff, String itemId, String skuId) throws Exception {
        itemId = itemId == null ? "" : itemId.trim();
        skuId = skuId == null ? "" : skuId.trim();
        if (itemId.isEmpty() && skuId.isEmpty()) {
            throw new IllegalArgumentException("请传递正确的平台商品(规格)ID");
        }
        List<String> itemIds = itemId.length() == 0 ? null : Arrays.asList(itemId);
        List<String> skuIds = skuId.length() == 0 ? null : Arrays.asList(skuId);
        Page page = new Page();
        return (itemIds != null && !tradeSearchService.querySidsByItemIds(staff, itemIds, true, page).isEmpty()) ||
                (skuIds != null && !tradeSearchService.querySidsBySkuIds(staff, skuIds, true, page).isEmpty());
    }

    @Override
    public List<CheckReceiptResp> checkTradeByItems(Staff staff, List<String> itemIds, List<String> skuIds) {
        if (CollectionUtils.isEmpty(itemIds) && CollectionUtils.isEmpty(skuIds)) {
            throw new IllegalArgumentException("请传递正确的平台商品(规格)ID");
        }
        if (itemIds.size() > 1000) {
            throw new IllegalArgumentException("最多支持1000条商品数据操作");
        }
        List<ItemParams> itemParams = Lists.newArrayList(
                ItemParams.<String>builder().name("numIid").field("num_iid").values(itemIds).build(),
                ItemParams.<String>builder().name("skuId").field("sku_id").values(skuIds).build()
        );

        List<TbOrder> orders = tradeSearchService.queryFieldByItemSkuIds(staff, ItemQueryParams.builder()
                .itemParams(itemParams).match(true).page(new Page(1, 1000)).build());
        if (CollectionUtils.isEmpty(orders)) {
            return Lists.newArrayList();
        }
        logger.info("商品查询结果: " + orders.size());
        //按key分组，并将sid转化为string类型
        List<CheckReceiptResp> resps = Lists.newArrayList();
        for (TbOrder order : orders) {
            CheckReceiptResp resp = new CheckReceiptResp();
            String key = (order.getNumIid() == null ? "0" : order.getNumIid()) + "_" + (order.getSkuId() == null ? "0" : order.getSkuId());
            List<String> list = Arrays.asList(order.getTid().split(","));
            resp.setKey(key);
            resp.setReceipt(list.size() > 5 ? list.subList(0, 5) : list);
            resps.add(resp);
        }
        logger.info("组装的返回结果集: " + JSONObject.toJSONString(resps));
        return resps;
    }

    @Override
    public DeleteReceipt checkTradeBySysItem(Staff staff, Long sysItemId, Long sysSkuId) {
        Page page = new Page().setPageNo(1).setPageSize(30);
        List<Long> sids = tradeSearchService.querySidsBySysItemSkuId(staff, sysItemId, sysSkuId, page);
        DeleteReceipt receipt = new DeleteReceipt();
        receipt.setDeleteable(sids.isEmpty());
        receipt.setReceipt(Lists.transform(sids, new Function<Long, String>() {
            @Override
            public String apply(Long sid) {
                return sid + "";
            }
        }));
        return receipt;
    }

    @Override
    public DeleteReceipt checkSaleTradeByItem(Staff staff, Long sysItemId, Long sysSkuId) {
        if (sysItemId == null || sysItemId <= 0) {
            throw new IllegalArgumentException("参数异常: sysItemId不能为空！");
        }
        List<Long> saleTradeIds = saleTradeDao.searchSettledTradeCountByItem(staff, sysItemId, sysSkuId);
        DeleteReceipt receipt = new DeleteReceipt();
        if (saleTradeIds != null && saleTradeIds.size() > 0) {
            receipt.setDeleteable(false);
            receipt.setReceipt(saleTradeIds.stream().map(String::valueOf).collect(Collectors.toList()));
        } else {
            receipt.setDeleteable(true);
        }
        return receipt;
    }

    /**
     * 根据系统商品ID或规格ID判断是否存在有效的订单,qimen模块调用
     * 订单要求：已审核、非异常、非挂起、非作废
     *
     * @param staff
     * @param sysItemIds
     * @param sysSkuIds
     * @return
     */
    public Map<String, Boolean> hasTradeBySysItemsAudited(Staff staff, List<Long> sysItemIds, List<Long> sysSkuIds, Long warehouseId) {
        List<Map<String, Object>> list = tbOrderDAO.listAuditedByItemSku(staff, sysItemIds, sysSkuIds, warehouseId);
        List<String> itemIds = new ArrayList<String>();
        for (Map<String, Object> map : list) {
            Long sysSkuId = (Long) map.get("sku_sys_id");
            if (sysSkuId < 0l) {
                sysSkuId = 0l;
            }
            itemIds.add(map.get("item_sys_id") + "_" + sysSkuId);
        }

        Map<String, Boolean> resultMap = new HashMap<String, Boolean>();
        for (int i = 0; i < sysItemIds.size(); i++) {
            Long sysItemId = sysItemIds.get(i);
            Long sysSkuId = sysSkuIds.get(i);

            resultMap.put(sysItemId + "_" + sysSkuId, itemIds.contains(sysItemId + "_" + sysSkuId));
        }
        return resultMap;
    }

    @Override
    public Trade saveTrade(Staff staff, TbTrade trade, boolean force) {
        return took(() -> sysTradeService.safeSaveTrade(staff, trade, force), staff, logger);
    }

    @Override
    public List<Trade> saveCattle(Staff staff, List<TbTrade> trades) {
        return took(() -> cattleTradeBusiness.saveCattle(staff, trades), staff, logger);
    }

    @Override
    public Trade saveFastOutTrade(Staff staff, Trade trade, boolean consign) {
        return took(() -> fastOutBusiness.saveAndConsign(staff, trade, consign), staff, logger);
    }

    @Override
    public void exportTrade(Staff staff, TradeQueryParams params) {
        sysTradeService.exportTrade(staff, TradeExportParams.build(params, null, null, false), true);
    }

    @Override
    public void exportOrder(Staff staff, TradeQueryParams params) {
        sysTradeService.exportOrder(staff, TradeExportParams.build(params, null, null, false), true);
    }

    @Override
    public List<LogisticsTrackingPollPool> queryExporConsignRecords(Staff staff, LogisticsTrackingPollPoolQueryParams params, String sids) {
        return exportBusiness.queryExporConsignRecords(staff, params, sids);
    }



    @Override
    public List<Trade> queryTradeReceiverInfoBySids(Staff staff, Long... sids) {
        String tradeFields = "sid,merge_sid,split_sid,tid,user_id,taobao_id,source,sub_source,address_md5,buyer_nick,receiver_country,receiver_state,receiver_city,receiver_district,receiver_street,receiver_zip,receiver_name,receiver_mobile,receiver_phone,receiver_address";
        List<Trade> trades = TradeUtils.toTrades(tbTradeDao.queryByKeys(staff, tradeFields, "sid", sids));
        try {
            trades = tradeFilterService.filterTrades(staff, trades, Lists.newArrayList(TagNameFilter.class, DefaultTemplateFilter.class));
        } catch (TradeFilterException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤失败"), e);
        }
        tradeExtFill.fill(staff, trades);

        trades.stream().filter(t -> !((!CommonSecretUtils.ifEncrypt(t) && !unionTradeDecryptBusiness.ifSensitived(t)) || secretBusiness.isAllSysEncode(t)))
                .forEach(t -> t.addOpV(OpVEnum.TRADE_IF_ENCRYPT));

        trades.forEach(t -> {
            t.setOriReceiverAddress(t.getReceiverAddress());
            t.setOriReceiverMobile(t.getReceiverMobile());
            t.setOriReceiverName(t.getReceiverName());
        });

        tradePddTradeBusiness.pddMaskDataReplace(staff, trades);
        fxgTradeDecryptBusiness.batchSensitive(staff, trades);
        commonTradeDecryptBusiness.batchSensitive(staff, trades);
        return trades;
    }

    @Override
    public List<Trade> queryTradeBySids(Staff staff, boolean queryOrders, Long... sids) {
        if (sids.length == 0) {
            return new ArrayList<Trade>(0);
        }
        AssembleParams params = new AssembleParams();
        params.setQueryOrders(queryOrders);
        params.setFilter(true);
        return queryTradeBySids(staff, params, sids);
    }

    @Override
    public List<Trade> queryTradeBySids(Staff staff, boolean queryOrders, boolean doTradeStatus, Long... sids) {
        return queryTradeBySids(staff, queryOrders, doTradeStatus, false, sids);
    }

    /**
     * 根据系统订单号查询订单
     */
    @Override
    public List<Trade> queryTradeBySids(Staff staff, boolean queryOrders, boolean doTradeStatus, boolean noFilter, Long... sids) {
        AssembleParams params = new AssembleParams();
        params.setQueryOrders(queryOrders);
        params.setDoTradeStatus(doTradeStatus);
        params.setAnalyzeExcept(true);

        /**
         *  这个方法 调整之前的逻辑是 如果传入的参数 filter = true, 则走 queryBySidsNoFilter
         *  List<Trade> trades = filter ? tradeSearchService.queryBySidsNoFilter(staff, queryOrders, sids) : tradeSearchService.queryBySids(staff, queryOrders, sids);
         *
         *  也就是说 参数名和实际的执行方法是反的 -_-!
         *
         *  但是代码太久了 这里只能仍维持原有的逻辑 只是将参数名改成了 noFilter
         *
         */
        params.setFilter(!noFilter);
        return queryTradeBySids(staff, params, sids);
    }


    /**
     * 根据系统订单号查询订单
     */
    private List<Trade> queryTradeBySids(Staff staff, AssembleParams params, Long... sids) {
        if (sids == null || sids.length == 0) {
            return new ArrayList<Trade>(0);
        }

        DubboLogBuilder logBuilder = new DubboLogBuilder(staff.getCompanyId(), LogBusinessEnum.QUERY.getSign()).appendMethod("queryTradeBySids ")
                .appendJSon(params).append("keySize",sids.length).startTimer();

        List<Trade> trades = (params.getFilter() !=null && params.getFilter()) ?  tradeSearchService.queryBySids(staff, params.getQueryOrders(),logBuilder, sids) :tradeSearchService.queryBySidsNoFilter(staff, params.getQueryOrders(),logBuilder, sids);
        logBuilder.recordTimer("doQuery");

        if (CollectionUtils.isEmpty(trades)) {
            logBuilder.startWatch().appendTook(3000L).printDebug(logger);
            return trades;
        }

        if (params.getDoTradeStatus() != null && params.getDoTradeStatus() ) {
            TradeConfig config = tradeConfigService.get(staff);
            for (Trade trade : trades) {
                trade.setChSysStatus(TradeStatusUtils.convertChSysStatus(trade, config));
            }
            logBuilder.recordTimer("convertStatus");
        }
        if(params.getAnalyzeExcept() !=null && params.getAnalyzeExcept()){
            //处理异常信息，并按异常权重大小返回结果
            TradeExceptionUtils.analyzeException(staff, trades, true);
            logBuilder.recordTimer("analyzeExcept");
        }
        //通过TJ进入模糊敏感信息
        if (!org.apache.commons.lang3.StringUtils.isEmpty(staff.getShadowToken()) && "2".equals(staff.getIsVague())
                && !CollectionUtils.isEmpty(trades)) {
            TradeUtils.blurTrades(trades);
            logBuilder.recordTimer("blurTrades");
        }
        logBuilder.startWatch().appendTook(3000L).printDebug(logger);
        return trades;
    }

    /**
     * 根据sids查询订单，合单后隐藏的订单也会显示， 子订单的组装与平台保持一致
     *
     * @param staff
     * @param queryOrder 是否查询子订单
     * @param sids
     * @return
     */
    public List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, Long... sids) {
        return queryBySidsContainMergeTrade(staff, queryOrder, false, sids);
    }

    public List<Trade> queryBySidsContainMergeTrade(Staff staff, boolean queryOrder, boolean filter, Long... sids) {
        if (sids.length == 0) {
            return new ArrayList<Trade>(0);
        }
        DubboLogBuilder logBuilder = new DubboLogBuilder(staff.getCompanyId(), LogBusinessEnum.QUERY.getSign()).appendMethod("queryBySidsContainMergeTrade ")
                .append("queryOrder",queryOrder).append("filter",filter).append("keySize",sids.length).startTimer();

        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, queryOrder, filter, true,logBuilder, sids);
        //通过TJ进入模糊敏感信息
        if (!org.apache.commons.lang3.StringUtils.isEmpty(staff.getShadowToken()) && "2".equals(staff.getIsVague()) &&
                !CollectionUtils.isEmpty(trades)) {
            TradeUtils.blurTrades(trades);
            logBuilder.recordTimer("blurTrades");
        }
        logBuilder.startWatch().appendTook(3000L).printDebug(logger);
        return trades;
    }


    @Override
    public List<Trade> queryTradeByTids(Staff staff, boolean queryOrders, String... tids) {
        return queryTradeByTids(staff, queryOrders, false, tids);
    }

    @Override
    public List<Trade> queryTradeByTids(Staff staff, boolean queryOrders, boolean queryTradePay, String... tids) {
        if (tids.length == 0) {
            return new ArrayList<Trade>(0);
        }
        DubboLogBuilder logBuilder = new DubboLogBuilder(staff.getCompanyId(), LogBusinessEnum.QUERY.getSign()).appendMethod("queryTradeByTids ")
                .append("queryOrders",queryOrders).append("queryTradePay",queryTradePay).append("keySize",tids.length).startTimer();
        List<Trade> trades = TradeUtils.toTrades(tradeSearchService.queryByTids(staff, queryOrders, queryTradePay,logBuilder, tids));
        //通过TJ进入模糊敏感信息
        if (!org.apache.commons.lang3.StringUtils.isEmpty(staff.getShadowToken()) && "2".equals(staff.getIsVague())
                && !CollectionUtils.isEmpty(trades)) {
            TradeUtils.blurTrades(trades);
            logBuilder.recordTimer("blurTrades");
        }
        logBuilder.startWatch().appendTook(3000L).printDebug(logger);
        return trades;
    }

    @Override
    public <T> List<Trade> queryTradeByKeys(Staff staff, String tradeFields, String orderFields, String key, T... values) {
        return tradeSearchService.queryByKeys(staff, tradeFields, orderFields, key, true, values);
    }

    @Override
    public <T> List<Trade> queryAndAssemblyByKeys(Staff staff, TradeAssembleParams params, String key, T... values) {
        return tradeSearchService.queryAndAssemblyByKeys(staff, params, key, values);
    }

    @Override
    public <T> List<Trade> queryAndAssemblyByKeys(Staff staff, TradeAssembleParams params, QueryKeyEnum key, T... values) {
        Assert.notNull(key,"查询key不能为空");
        return tradeSearchService.queryAndAssemblyByKeys(staff,  params,  key.getField(),  values);
    }

    @Override
    public List<TbTrade> assemblyTrades(Staff staff, TradeAssembleParams params, List<TbTrade> tbTrades) {
        if (CollectionUtils.isEmpty(tbTrades)) {
            return tbTrades;
        }
        QueryLogBuilder logBuilder = new QueryLogBuilder(staff).append("assemblyTrades:");
        List<Trade> trades = tradeAssembleBusiness.assemblyTrades(staff, params, logBuilder, tbTrades);
        logBuilder.startWatch().appendTook(DevLogBuilder.isDevEnv() ? 100L : 5000L).printInfo(logger);
        return trades.stream().map(x -> (TbTrade) x).collect(Collectors.toList());
    }

    @Override
    public List<ConsignRecord> queryConsignRecordBySids(Staff staff, Long... sids) {
        return tradeSearchService.queryConsignRecordBySids(staff, sids);
    }

    @Override
    public List<ConsignRecord> queryConsignRecordBySids(Staff staff, Integer enableStatus, Long... sids) {
        return consignRecordDao.queryBySids(staff, enableStatus, sids);
    }

    @Override
    public List<Trade> searchDbTradeByDate(Staff staff, Page page, List<String> statusList, Date startDate, Date endDate) {
        return tradeSearchService.searchDbTradeByDate(staff, page, statusList, startDate, endDate);
    }

    @Override
    public TradeCount searchTradeCount(Staff staff, String queryIds) {
        StringTokenizer tokenizer = new StringTokenizer(queryIds, ",");
        TradeCount tc = new TradeCount();
        while (tokenizer.hasMoreTokens()) {
            long queryId = Long.parseLong(tokenizer.nextToken().trim());
            tradeSqlCountService.setTradeNum(staff, queryId, tc);
        }
        return tc;
    }

    @Override
    public Long searchDbTradeByDateOfCount(Staff staff, List<String> statusList, Date startDate, Date endDate) {
        return tradeSearchService.searchDbTradeByDateOfCount(staff, statusList, startDate, endDate);
    }

    @Override
    public List<SysMemoLog> querySysMemoLogs(Staff staff, Long sid) {
        return sysTradeService.querySysMemoLogs(staff, sid);
    }

    @Override
    public void packTrades(Staff staff, Long[] sids, List<Order> orders) {
        try {
            sysTradeService.packTrades(staff, sids, orders, null);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "包装验货失败！"), e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    @Override
    public void packTrades(Staff staff, Long[] sids, List<Order> orders, List<String> uniqueCodes) {
        try {
            sysTradeService.packTradesWithScanInfo(staff, sids, orders, null, uniqueCodes, null);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "包装验货失败！"), e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    @Override
    public void packTradesBatchByParams(Staff staff, TradePackParams params) {
        try {
            sysTradeService.packTradesBatchByParams(staff, params);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "包装验货失败！"), e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    @Override
    public void packTradesByParams(Staff staff, TradePackParams params) {
        try {
            sysTradeService.packTradesByParams(staff, params);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "包装验货失败！"), e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    @Override
    public TradeWavePackResult packTradesWithResult(Staff staff, TradePackParams params) {
        try {
            sysTradeService.packTradesByParams(staff, params);
            if (params.getPackSplitPrintSid() != null) {
                TradeWavePackResult result = new TradeWavePackResult();
                result.setPackSplitPrintSid(params.getPackSplitPrintSid());
                return result;
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "包装验货失败！"), e);
            throw new IllegalArgumentException(e.getMessage());
        }
        return null;
    }

    @Override
    public void packTrades(Staff staff, TradePackParams params) {
        Assert.notNull(params, "参数不能为空! ");
        if (DataUtils.checkLongNotEmpty(params.getStaffId())) {
            staff = staffAssembleBusiness.getStaffById(params.getStaffId());
            Assert.notNull(staff, "请选择正确的员工账号!");
            WaveConfig waveConfig = waveServiceDubbo.queryWaveConfig(staff);
            if (waveConfig != null && waveConfig.getInteger(WaveChatConfigsEnum.PACK_SUPPORT_SWITCH_ACCOUNT.getKey()) == 0) {
                Assert.isTrue(false, "页面已过期或配置已变更，请刷新页面重试！");
            }
        }
        //开放平台接口暂时不回传子订单信息
        if (CollectionUtils.isEmpty(params.getOrders())) {
            List<Trade> trades = new ArrayList<>();
            //判断是通过sid还是运单号验货
            if (StringUtils.isNotEmpty(params.getOutSid())) {
                trades = tradeSearchService.queryByOutSid(staff, params.getOutSid(), true);
            } else {
                trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, params.getSids());
            }
            if (CollectionUtils.isNotEmpty(trades)) {
                List<Order> orderList = TradeUtils.getOrders4Trade(trades);
                params.setOrders(orderList);
                if (params.getSids() == null || params.getSids().length == CommonConstants.JUDGE_NO) {
                    params.setSids(new Long[]{trades.get(0).getSid()});
                }
                sysTradeService.packTradesWithScanInfo(staff, params);
            } else {
                throw new IllegalArgumentException("sid或运单号对应订单不存在!");
            }
            return;
        }
        sysTradeService.packTradesWithScanInfo(staff, params);
    }

    @Override
    public TradeValidator checkConsign(Staff staff, Trade trade, TradeValidator validator) {
        tradeValidateBusiness.checkConsign(staff, trade, validator, tradeConfigService.get(staff));
        return validator;
    }

    /**
     * 清除公司的订单数据
     *
     * @param staff
     * @return
     */
    @Override
    public boolean clearTradeData(Staff staff) {
        return clearAllDataBusiness.clearTradeData(staff);
    }

    /**
     * 清除三个月之前的订单
     */
    @Override
    public boolean clear3MonthBefore(Staff staff) {
        return clear3MonthBeforeDataBusiness.clearAll(staff);
    }

    @Override
    public boolean clearAll(Staff staff) {
        return clearAllDataBusiness.clearAll(staff, true);
    }


    @Override
    public List<FreightCost> batchCountWeight(Staff staff, List<Trade> list) {
        return freightTemplateService.batchCountWeight(staff, list);
    }

    @Override
    public List<CalculateResult> batchDistributionPaymenteCalculate(Staff staff, List<Long> sids, boolean ignoreAfterSale) {
        logger.debug(LogHelper.buildLogHead(staff).append("计算分销价的订单号:").append(sids));
        return calculateBusiness.calculateDistributionPaymentBySids(staff, sids, ignoreAfterSale);
    }

    @Override
    public List<WaveTrade> queryWaveTradesByWaveIds(Staff staff, List<Long> waveIds) {
        logger.debug("ITradeServiceDubbo中波次方法被调用,方法:queryWaveTradesByWaveIds");
        return waveServiceDubbo.queryWaveTradesByWaveIds(staff, waveIds);
    }

    @Override
    public List<Trade> updateTradeExcept(Staff staff, Long[] sids, Long[] exceptIds, boolean ifCancelExcept, String remark, Integer coverRemark, Boolean autoUnaudit) {
        return tradeExceptBusiness.update(staff, sids, exceptIds, ifCancelExcept, remark, coverRemark, autoUnaudit);
    }

    @Override
    public TradeWaveSplitForOpenResult tradeWaveSplitForOpen(Staff staff, TradeWaveSplitForOpenParam param) {
        return waveSplitGenericService.splitTradeForOpen(staff, param);
    }

    @Override
    public WavePicking scanByPickingCode(Staff staff, WavePickingScanParam param) throws Exception {
        try {
            return tradePostPrintService.scanByPickingCode(staff, param);
        } catch (WaveScanException e) {
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    @Override
    public boolean asyncFinishWave(Staff staff, List<Long> waveIds) {
        return tradePostPrintService.asyncFinishWave(staff, waveIds);
    }

    @Override
    public List<Long> querySidsByWaveIds4Print(Staff staff, List<Long> waveIds, boolean printed) {
        Assert.notNull(waveIds, "波次不能为空！");
        return tradeWaveService.querySidsByWaveIds4Print(staff, waveIds, printed);
    }

    //============================================== 订单配置相关接口 ============================================

    @Override
    public TradeConfig queryTradeConfig(Staff staff) {
        return took(() -> tradeConfigService.get(staff), staff, logger);
    }

    @Override
    public TradeConfigNew getNewConfig(Staff staff, TradeConfigEnum tradeConfigEnum) {
        return tradeConfigNewService.get(staff, tradeConfigEnum);
    }

    @Override
    public List<MultiPacksPrintTradeLog> queryByOutSidsNew(Staff staff, List<String> outSids) {
        return multiPacksPrintTradeLogService.queryByOutSidsNew(staff, outSids);
    }

    @Override
    public boolean updateTradeConfig(Staff staff, TradeConfig tradeConfig) {
        return took(() -> {
            tradeConfigService.update(staff, tradeConfig);
            return true;
        }, staff, logger);
    }

    @Override
    public boolean updateLogisticsTrackingConfig(Staff staff, TradeConfig tradeConfig) {
        TradeConfig oldConfig = tradeConfigService.get(staff);
        // 不走老接口
//        tradeConfigService.update(staff, tradeConfig);
        if (1 == tradeConfig.getOpenLogisticsTracking() && (null == oldConfig.getOpenLogisticsTracking() || 0 == oldConfig.getOpenLogisticsTracking())) {
            //开启操作
            eventCenter.fireEvent(this, new EventInfo("trade.logistics.switch.on").setArgs(new Object[]{staff}), null);
            // 更新新表
            tradeConfigNewService.update(staff, TradeConfigEnum.OPEN_LOGISTICS_TRACKING.getConfigKey(), "1");
        } else if (0 == tradeConfig.getOpenLogisticsTracking() && (null != oldConfig.getOpenLogisticsTracking() || 1 == oldConfig.getOpenLogisticsTracking())) {
            //关闭操作
            eventCenter.fireEvent(this, new EventInfo("trade.logistics.switch.off").setArgs(new Object[]{staff}), null);
            // 更新新表
            tradeConfigNewService.update(staff, TradeConfigEnum.OPEN_LOGISTICS_TRACKING.getConfigKey(), "0");

        }
        return true;
    }

    @Override
    public TradeStaffConfig queryTradeStaffConfig(Staff staff) {
        return tradeStaffConfigService.get(staff);
    }

    //============================================== 买断型业务相关接口 ============================================

    @Resource
    TopUpBusiness topUpBusiness;
    @Resource
    DeductBusiness deductBusiness;

    @Override
    public TopUpCompany getByCompanyId(Staff staff) {
        return topUpBusiness.getByCompanyId(staff);
    }

    @Override
    public long saveTopUpRule(Staff staff, TopUpRule topUpRule) {
        return topUpBusiness.saveTopUpRule(staff, topUpRule);
    }

    @Override
    public List<TradeConfigNew> queryTradeNewConfig(Staff staff, TradeConfigEnum... configEnums) {
        return took(() -> tradeConfigNewService.getList(staff, configEnums), staff, logger);
    }

    @Override
    public int deleteTopUpRule(Staff staff, Long id) {
        return topUpBusiness.deleteTopUpRule(staff, id);
    }

    @Override
    public List<TopUpRule> listTopUpRules(Staff staff) {
        return topUpBusiness.listRules(staff);
    }

    @Override
    public void setFreeTime(Staff staff, Date freeStart, Date freeEnd) {
        topUpBusiness.setFreeTime(staff, freeStart, freeEnd);
    }

    @Override
    public void topUp(Staff staff, TopUpTransaction transaction) {
        topUpBusiness.topup(staff, transaction);
    }

    @Override
    public void deduct(Staff staff, Long deductAmount) {
        deductBusiness.deduct(staff, deductAmount);
    }

    @Override
    public void postDeduct(Staff staff, Date date) {
        deductBusiness.postDeduct(staff, date);
    }

    @Override
    public List<TopUpTransaction> listTopUpTransactions(Staff staff, Date startTime, Date endTime, Page page) {
        return topUpBusiness.listTransactions(staff, startTime, endTime, page);
    }

    @Override
    public List<TopUpDeduction> listTopUpDeductions(Staff staff, Integer startDay, Integer endDay, Page page) {
        return deductBusiness.listDeductions(staff, startDay, endDay, page);
    }

    @Override
    public TopUpCompanies listByAmountRange(int dbNo, Long minAmount, Long maxAmount, Page page) {
        return topUpBusiness.listByAmountRange(dbNo, minAmount, maxAmount, page);
    }


    //============================================== 订单控制开关 ============================================


    @Override
    public boolean encryptExport(Long companyId, boolean yes) {
        if (companyId == null) {
            throw new IllegalArgumentException("请传递公司ID");
        }
        return cacheBusiness.encryptExport(companyId, yes);
    }

    @Override
    public boolean checkIfExportEncrypt(Long companyId) {
        if (companyId == null) {
            throw new IllegalArgumentException("请传递公司ID");
        }
        return !cacheBusiness.ifExportNotEncrypt(companyId);
    }

    @Override
    public boolean switchTradeSync(Long companyId, Long userId, int value) {
        tradeConfigService.updateTradeSyncStatus(companyId, userId, value);
        return true;
    }

    @Override
    public boolean ifCloseTradeSync(Long companyId, Long userId) {
        if (userId != null && userId > 0) {
            User user = userService.queryById(userId);
            return user.getTradeSyncStatus() == 0;
        }
        return tradeLocalConfigurable.isCloseTradeSync(companyId, true);
    }

    @Override
    public Integer getTradeSyncType(Long userId) {
        User user = userService.queryById(userId);
        if (user.getUserConf() != null && user.getUserConf().getTradeSyncType() != null) {
            return user.getUserConf().getTradeSyncType();
        } else if (TradeUtils.isTbTrade(user.getSource())) {//淘宝天猫没有设置同步方式默认按照RDS方式同步
            return TradeConstants.SYNC_INCR_RDS;
        }
        return TradeConstants.SYNC_INCR_API;//多平台目前均按API增量方式同步
    }

    @Override
    public void setTradeSyncType(Long userId, Integer syncType) {
        tradeConfigService.updateTradeSyncType(userId, syncType);
    }


    @Override
    public boolean moduleOpenValidate(Staff staff, Integer onoff) {
        return tradeWmsOpenValidateBusiness.moduleOpenValidate(staff, onoff);
    }

    @Override
    public boolean switchPickPerformanceValidate(Staff staff) {
        return tradeWaveService.switchPickPerformanceValidate(staff);
    }

    //=======================================================订单体检及修复=========================================================
    @Override
    public TradeInspectResult inspectTrades(Long companyId) {
        Assert.isTrue(null != companyId && companyId > 0, "请传入公司id");
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        Assert.isTrue(null != staff, "该公司不存在默认员工信息");
        List<User> users = userService.queryByCompanyId(companyId);
        staff.setUsers(users);
        for (User user : users) {
            user.setStaff(staff);
        }
        return tradeInspectService.inspectTrades(staff);
    }

    @Override
    public TradeRecoverResult recoverTrades(Long companyId, TradeInspectResult result) {
        Assert.notNull(result, "请传入错误的订单信息");
        Assert.isTrue(null != companyId && companyId > 0, "请传入公司id");
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        Assert.isTrue(null != staff, "该公司不存在默认员工信息");
        List<User> users = userService.queryByCompanyId(companyId);
        staff.setUsers(users);
        for (User user : users) {
            user.setStaff(staff);
        }
        return tradeRecoverService.recoverTrades(staff, result);
    }

    //=======================================================校验订单模块商品是否存在===============================================
    @Override
    public List<String> checkExistItem(Staff staff, Long sysItemId, Long sysSkuId) {
        return itemCheckExistService.check(staff, sysItemId, sysSkuId);
    }

    @Override
    public void migrate(Long companyId, Integer sourceDb, Integer targetDb) {
        eventCenter.fireEvent(this, new EventInfo("company.data.migrate").setArgs(new Object[]{companyId, sourceDb, targetDb}), null);
    }

    @Override
    @Transactional
    public void updateCheckGoodsFinished(Staff staff, List<Wave> waves) {
        if (CollectionUtils.isNotEmpty(waves)) {
            List<Long> pickingIds = new ArrayList<Long>(waves.size());
            for (Wave wave : waves) {
                wave.setCheckGoodsFinished(true);
                if (null != wave.getPickingId()) {
                    pickingIds.add(wave.getPickingId());
                }
            }
            List<WaveSorting> waveSortings = waveSortingDao.queryByPickingIds(staff, pickingIds);
            tradeWaveService.updateCheckGoodsFinished(staff, waves);
            if (CollectionUtils.isNotEmpty(waveSortings)) {
                List<Long> sids = new ArrayList<Long>(waveSortings.size());
                for (WaveSorting waveSorting : waveSortings) {
                    sids.add(waveSorting.getSid());
                }
                List<Trade> trades = tradeSearchService.queryBySids(staff, false, sids.toArray(new Long[sids.size()]));

                eventCenter.fireEvent(this, new EventInfo("trade.checkGoods.finish").setArgs(new Object[]{staff}), trades);
            }
        }
    }

    @Override
    public void updatePackmaCost(Staff staff, Long sid, Double packmaCost) {
        tbTradeDao.updatePackmaItemCost(staff, sid, packmaCost);
    }

    /**
     * 不仅仅是更新包材成本 还会将包材的体积更新到订单体积、重量更新到订单净重
     * 次更新为覆盖更新
     * @param staff
     * @param params
     */
    @Override
    public void batchUpdatePackmaCost(Staff staff, List<TradePackmaCostParams> params) {
        Assert.isTrue(CollectionUtils.isNotEmpty(params),"params must not be empty");
        Assert.isTrue(staff!=null,"params must not be null");
        Assert.isTrue(params.size()<=200,"params size must less than 200");
        List<List<TradePackmaCostParams>> partitionParams = Lists.partition(params,50);

        logger.debug(LogHelper.buildLog(staff,"【批量修改订单包材相关信息】开始批量修改,params:"+ JSONObject.toJSONString(params)));
        boolean needCalculateFreightCost = params.stream().anyMatch(e->"TRADE_WEIGHT".equals(e.getScene()));
        List<Trade> existTemplateTradeList = new ArrayList<>();
        List<Trade> recalculateFreightCostTradeList = new ArrayList<>();
        List<Trade> allUpdateList = new ArrayList<>();
        for(List<TradePackmaCostParams> partitionParam:partitionParams){
            Long[] sids = partitionParam.stream().map(TradePackmaCostParams::getSid).toArray(Long[]::new);
            lockService.locks(tradeLockBusiness.getERPLocks(staff,sids),()->{
                List<Trade> searchTrades = tradeSearchService.queryBySids(staff,true,true,false,sids);
                List<Trade> list = searchTrades.stream().filter(trade -> !TradeUtils.isAfterSendGoods(trade) && Objects.nonNull(trade.getTemplateId()) && trade.getTemplateId() > 0).collect(Collectors.toList());
                List<TbTrade> toUpdateTradeList = buildTrades(staff,searchTrades,partitionParam);
                calculateFreightCost(needCalculateFreightCost,staff,list,toUpdateTradeList,recalculateFreightCostTradeList);
                tbTradeDao.batchUpdate(staff, toUpdateTradeList);
                existTemplateTradeList.addAll(list);
                allUpdateList.addAll(toUpdateTradeList);
                return null;
            });
        }

        //记录订单重算运费日志
        if(CollectionUtils.isNotEmpty(recalculateFreightCostTradeList)){
            eventCenter.fireEvent(this, new EventInfo("trade.weight.recalculateFreightCost").setArgs(new Object[]{staff}), recalculateFreightCostTradeList);
        }
        //记录包材更新日志
        if(CollectionUtils.isNotEmpty(allUpdateList)){
            eventCenter.fireEvent(this, new EventInfo("trade.packma.batchUpdate").setArgs(new Object[]{staff,params}), allUpdateList);
        }
        // 修改快递需要计算理论运费
        if (CollectionUtils.isNotEmpty(existTemplateTradeList)) {
            eventCenter.fireEvent(this, new EventInfo("modify.template.calculate.theoryPostFee").setArgs(new Object[]{staff, TradeUtils.toSidList(existTemplateTradeList),BusinessNodeEnum.TRADE_PACKMA_UPD}), null);
        }
    }

    private void calculateFreightCost(boolean needCalculateFreightCost,Staff staff,List<Trade> trades,List<TbTrade> toUpdateTradeList,
                                      List<Trade> recalculateFreightCostTradeList){
        if(!needCalculateFreightCost ||CollectionUtils.isEmpty(toUpdateTradeList)){
            return;
        }
        Map<Long,Trade> tradeMap = trades.stream().collect(Collectors.toMap(Trade::getSid,v->v,(k1,k2)->k1));
        for(TbTrade currentUpdateTrade:toUpdateTradeList){
            Trade currentSearchTrade = tradeMap.get(currentUpdateTrade.getSid());
            if(currentSearchTrade==null){
                continue;
            }
            currentSearchTrade.setVolume(currentUpdateTrade.getVolume());
            CalculateFreightCostParams costParams = new CalculateFreightCostParams
                    .Builder().sid(currentUpdateTrade.getSid())
                    .staff(staff)
                    .weight(currentSearchTrade.getWeight().toString())
                    .currentTrade(currentSearchTrade)
                    .build();
            CalculateFreightCostResult result = sysTradeService.calculateFreightCost(costParams);
            if(result!=null && result.getCost()!=null && result.getCost().getCount()!=null){
                currentUpdateTrade.setActualPostFee(result.getCost().getCount().toString());
                //这两个字段用来记录订单日志用
                currentUpdateTrade.setOutSid(currentSearchTrade.getOutSid());
                currentUpdateTrade.setWeight(currentSearchTrade.getWeight());
                recalculateFreightCostTradeList.add(currentUpdateTrade);
            }
        }

    }

    private  List<TbTrade> buildTrades(Staff staff,List<Trade> searchTrades,List<TradePackmaCostParams> params){

        Map<Long,Trade> tradeMap = searchTrades.stream().collect(Collectors.toMap(Trade::getSid,v->v,(k1,k2)->k1));
        List<TbTrade> trades = Lists.newArrayListWithExpectedSize(searchTrades.size());
        for(TradePackmaCostParams packmaParam:params){
            Double packmaCost = packmaParam.getPackmaCost();
            Double packmaWeight = packmaParam.getPackmaWeight();
            Double packmaVolume = packmaParam.getPackmaVolume() ;

            Trade currentTrade = tradeMap.get(packmaParam.getSid());
            if(currentTrade==null){
                continue;
            }
            TradeExt tradeExt = currentTrade.getTradeExt();
            if (tradeExt == null) {
                tradeExt = new TradeExt();
                tradeExt.setCompanyId(currentTrade.getCompanyId());
                tradeExt.setSid(currentTrade.getSid());
            }
            TbTrade trade = new TbTrade();
            trade.setCompanyId(currentTrade.getCompanyId());
            trade.setSid(currentTrade.getSid());
            trade.setTid(currentTrade.getTid());
            trade.setPackmaCost(packmaCost);
            TradeExtUtils.setExtraFieldValue(tradeExt,TradeExtraFieldEnum.PACKMA_WEIGHT.getField(),packmaWeight);
            double netWeight = TradeUtils.calculateTradeNetWeight(currentTrade);
            if (!MathUtils.equalsZero(packmaWeight)) {
                netWeight = netWeight + packmaWeight;
            }
            trade.setNetWeight(netWeight);
            TradeExtUtils.setExtraFieldValue(tradeExt,TradeExtraFieldEnum.PACKMA_VOLUME.getField(),packmaVolume);
            if (MathUtils.equalsZero(packmaVolume)) {
                //取消包材时 需将订单体积还原为商品体积之和
                trade.setVolume(TradeUtils.calculateVolume(currentTrade));
            }else {
                trade.setVolume(packmaVolume);
            }
            trade.setTradeExt(tradeExt);
            trades.add(trade);
        }
        return trades;
    }


    @Override
    public List<ConsignRecord> consign(Staff staff, Long[] sids, String consignType, String clientIp) {
        return tradeService.consign(staff, sids, consignType, clientIp, null, null, null, null);
    }

    @Override
    public List<ConsignRecord> consign(Staff staff, Long[] sids, String consignType, String clientIp, Boolean exceptionConsign, Integer dymmyType, String noLogisticsName, String noLogisticsTel) {
        return tradeService.consign(staff, sids, consignType, clientIp, exceptionConsign, dymmyType, noLogisticsName, noLogisticsTel);
    }

    @Override
    public List<ConsignRecord> consign(Staff staff, Long[] sids, String consignType, String clientIp, Boolean exceptionConsign, Integer dymmyType, String noLogisticsName, String noLogisticsTel, boolean isFilterPartyWarehouse) {
        return tradeService.consign(staff, sids, consignType, clientIp, exceptionConsign, dymmyType, noLogisticsName, noLogisticsTel, isFilterPartyWarehouse);
    }

    @Override
    public List<ConsignRecord> externalConsignUpload(Staff staff, ExternalConsignRequest request) {
        return tradeService.externalConsignUpload(staff, request);
    }

    @Override
    public List<ConsignRecord> consignStall(Staff staff, Long[] sids) {
        return tradeService.consignStall(staff, sids);
    }

    @Override
    public SplitPickResponse splitPick(Staff staff, Long sid, List<Order> splitOrders) {
        SplitResult splitResult = tradeSplitService.splitPick(staff, sid, splitOrders);
        SplitPickResponse splitPickResponse = new SplitPickResponse();
        splitPickResponse.setSuccess(splitResult.isSuccess());
        splitPickResponse.setMsg(splitResult.getMsg());
        splitPickResponse.setPickData(splitResult.getPickData());
        splitPickResponse.setUnPickData(splitResult.getUnPickData());
        return splitPickResponse;
    }

    @Override
    @Transactional
    public WaveSplitResultDataTransfer splitForWavePack(Staff staff, TradeSplitEnum tradeSplitEnum, WaveSplitParamsTransfer params) {
        SplitParams splitParams = SplitUtils.convertToSplitParams(params);
        SplitResultData splitResultData = splitAllInOneBusiness.split(staff, tradeSplitEnum, splitParams);
        WaveSplitResultDataTransfer resultDataTransfer = SplitUtils.convertToWaveSplitData(splitResultData);
        WaveSplitParamsTransfer waveSplitParamsTransfer = SplitUtils.convertToWaveSplitParams(splitParams);
        resultDataTransfer.setWaveSplitParamsTransfer(waveSplitParamsTransfer);
        return resultDataTransfer;
    }

    @Override
    public WavePicking queryWavePickingById(Staff staff, Long id) {
        logger.debug("ITradeServiceDubbo中波次方法被调用,方法:queryWavePickingById");
        return waveServiceDubbo.queryWavePickingById(staff, id);
    }

    @Override
    public Map<String, Integer> queryNotFinishedRefundOrScalpingItemNumMap(Staff staff, Long warehouseId) {
        return tradeSearchService.queryNotFinishedRefundOrScalpingItemNumMap(staff, warehouseId);
    }

    @Override
    public TradeCount count(TradeCountParams tradeCountParams) throws Exception {
        return tradeQueryService.count(tradeCountParams);
    }

    @Override
    public void weigh(TradeWeighParams tradeWeighParams) {
        sysTradeService.weigh(tradeWeighParams);
    }

    @Override
    public CalculateFreightCostResult calculateFreightCostAndWeight(TradeWeightParams params) {
        return sysTradeService.calculateFreightCostAndWeight(params);
    }

    @Override
    public void reweigh(Staff staff, Long sid, Double weight, String actualPostFee, String outerId) {
        sysTradeService.reWeightTrade(staff, sid, weight, actualPostFee, outerId,null);
    }

    @Override
    public void reweigh(Staff staff, Long sid, Double weight, String actualPostFee) {
        sysTradeService.reWeightTrade(staff, sid, weight, actualPostFee, null,null);
    }

    @Override
    public void weighDeliveredTrade(Staff staff, Long sid, Double weight, String actualPostFee) {
        sysTradeService.weighDeliveredTrade(staff, sid, weight, actualPostFee);
    }

    @Override
    public void updateTradeWeighConfig(Staff staff, TradeWeighConfig config) {
        tradeWeighConfigService.update(staff, config);
    }

    @Override
    public TradeWeighConfig getTradeWeighConfig(Staff staff) {
        return tradeWeighConfigService.get(staff);
    }

    @Override
    public CalculateFreightCostResult calculateFreightCost(CalculateFreightCostParams params) {
        return sysTradeService.calculateFreightCost(params);
    }

    @Override
    public Trade queryByMixKey(Staff staff, String mixKey) {
        return getTrade(staff, mixKey, true);
    }

    private Trade getTrade(Staff staff, String mixKey, boolean includeMaskData) {
        Trade trade = tradeQueryService.queryByMixKey(staff, mixKey, includeMaskData);
        if (trade == null) {
            return trade;
        }
        try {
            // tradeFillService.fill(staff, trade);
            trade = tradeFilterService.filterTrade(staff, trade);
        } catch (TradeFilterException e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "订单过滤失败"), e);
        }
        return trade;
    }

    @Override
    public Trades queryByMixKey(Staff staff, TradeQueryParams params) {
        Assert.notNull(params,"查询条件 params不能为空");
        params.getContext().setClientType("dubbo");
        return tradeQueryService.queryByMixKey(staff, params);
    }

    @Override
    public Trade queryByMixKey(Staff staff, String mixKey, boolean includeMaskData) {
        return getTrade(staff, mixKey, includeMaskData);
    }

    @Override
    public List<PickBackOrder> queryPickBackOrders(Staff staff, PickBackOrder condition) {
        return pickBackService.queryByCondition(staff, condition);
    }

    @Override
    public void pickBack(Staff staff, List<PickBackOrder> pickBackOrders) {
        pickBackService.pickBack(staff, pickBackOrders);
    }

    @Override
    public boolean hasFinishAuditTrade(Staff staff) {
        return tradeCountBusiness.hasFinishedAudit(staff);
    }

    @Override
    public Integer updateSaleTrade(Staff staff, SaleTrade saleTrade) {
        return saleTradeBusiness.save(staff, saleTrade);
    }

    @Override
    public void endPrintTime(Staff staff, List<Long> ids) {
        saleTradeBusiness.endPrintTime(staff, ids, "PDA打印");
    }

    /**
     * 根据id查询销货单
     *
     * @param staff
     * @param id
     * @return
     */
    @Override
    public SaleTrade querySaleTradeById(Staff staff, Long id) {
        logger.debug("ITradeServiceDubbo中老版销货单查询接口被调用!");
        return saleTradeService.queryDetail(staff, SaleTradeQueryParam.builder().id(id).build());
    }

    @Override
    public List<WaveUniqueCode> queryOrderUniqueCode(Staff staff, OrderUniqueCodeQueryParams params) {
        logger.debug("ITradeServiceDubbo中波次方法被调用,方法:queryOrderUniqueCode");
        return waveServiceDubbo.queryOrderUniqueCode(staff, params);
    }

    @Override
    public void tradeWeight(Staff staff, Long sid, String weight, String cost, Integer kind, String ip) {
        sysTradeService.tradeWeight(staff, sid, weight, cost, kind, ip, null,null);
    }

    @Override
    public List<SaleOrder> queryReturnOrdersBySaleTradeId(Staff staff, Long saleTradeId) {
        logger.debug("ITradeServiceDubbo中批发收银方法被调用,方法:queryReturnOrdersBySaleTradeId");
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("saleTradeId", saleTradeId);
        conditions.put("type", SaleOrderTypeEnum.RETURN_GOODS.getType());
        return saleOrderService.queryList(staff, conditions);
    }

    @Override
    public List<TradeTrace> getTradeTraceByCondition(Staff staff, Date start, Date end, String actionLike, List<Long> sids) {
        return tradeTraceService.getTradeTraceByCondition(staff, start, end, actionLike, sids);
    }

    @Override
    public List<WarehouseAllocate> queryWarehouseAllocates(Staff staff, WarehouseAllocate query, Page page, int type) {
        return warehouseAllocateBusiness.list(staff, query, page, type);
    }

    /**
     * 售后更新订单系统备注
     */
    @Override
    public List<Trade> updateSysMemo(Staff staff, String memo, boolean append, Long... sids) {
        return tradeUpdateSellerMemoFlagBusiness.update(staff, TradeMemo.builder()
                        .handType(TradeMemoTypeEnum.SYS_EMEO.getType())
                        .sysMemo(memo)
                        .append(append)
                        .handFrom(TradeMemoFromEnum.HAND_FROM_DUBBO.getType())
                        .build(), sids)
                .getUpdatedTrades();
    }

    @Override
    public List<TradePick> getTradePick(Staff staff, Timestamp fromTime, Timestamp toTime, int limit, int size) {
        return tradePickDao.getTradeList(staff, fromTime, toTime, limit, size);
    }

    @Override
    public List<TradeTag> getTagsByCompanyId(Staff staff, Integer kind) {
        return tradeTagService.list(staff, kind);
    }

    @Override
    public boolean moduleOpenAuditActiveStockValidate(Staff staff, Integer onoff) {
        return tradeWmsOpenValidateBusiness.moduleOpenAuditActiveStockValidate(staff, onoff);
    }

    @Override
    public List<ExpressCompany> queryExpressCompanyByIds(Staff staff, Long[] ids) {
        return expressCompanyService.getExpressCompanyByIds(ids);
    }

    @Override
    public FreightCost calculateFreightCost(Staff staff, Trade trade) {
        String selectType = freightTemplateService.getSelectFreightTemplateType(staff, trade.getTemplateId(), trade.getTemplateType());
        FreightCost cost;
        if ("volume".equals(selectType)) {
            // 体积计算
            trade.setVolume(TradeUtils.calculateVolume(trade));
            cost = freightTemplateVolumeService.countVolume(staff, trade);
        } else {
            cost = freightTemplateService.countWeight(staff, trade);
        }
        if (cost.isError()) {
            logger.error(LogHelper.buildLogHead(staff).append(StringUtils.isEmpty(cost.getReason()) ? "计算运费出错" : cost.getReason()));
        }
        return cost;
    }


    @Override
    public TradeValidator checkPack(Staff staff, Trade trade, TradeValidator validator) {
        tradeValidateBusiness.checkPack(staff, trade, validator);
        return validator;
    }

    @Override
    public TradeValidator checkTradeWeight(Staff staff, Trade trade, TradeValidator validator, Integer kind) {
        if (null == trade) {
            return null;
        }
        tradeValidateBusiness.checkTradeWeight(staff, trade, validator, queryTradeConfig(staff), kind, null);
        return validator;
    }

    @Override
    public List<TradeTag> getTagByIds(Staff staff, Long[] tagIds) {
        return tradeTagService.queryByIds(staff, tagIds);
    }

    @Override
    public SaleTrade getSingleSaleTradeDetail(Staff staff, Long id) {
        throw new IllegalArgumentException("老版接口已弃用，请使用ISaleTradeServiceDubbo.queryByPage");
    }

    @Override
    public List<SaleTrade> getBatchSaleTradeDetail(Staff staff, List<Long> ids) {
        throw new IllegalArgumentException("老版接口已弃用，请使用ISaleTradeServiceDubbo.queryByPage");
    }

    /**
     * 查询销货单收款记录明细
     *
     * @param staff
     * @param ids   记录明细id集合
     * @return
     */
    @Override
    public List<SaleTradeFinanceDetail> queryTradeFinanceDetail(Staff staff, List<Long> ids) {
        throw new IllegalArgumentException("方法已作废，新的支付记录明细已存储在saleTrade.payInfos中!");
    }

    @Override
    public Map<String, String> decryptPddData(User user, List<DataListItemVO> dataList) {
        Map<String, String> dataMaps = Maps.newHashMap();
/*        IPlatformToolAccess toolAccess = platformManagement.getAccess(CommonConstants.PLAT_FORM_TYPE_PDD, IPlatformToolAccess.class);
        for(List<DataListItemVO> subList : Lists.partition(dataList, 100)){
            dataMaps.putAll(toolAccess.decryptBatch(subList, user.getSessionId()));
        }*/
        return dataMaps;
    }

    @Override
    public List<SaleOrder> querySaleOrderByCondition(Staff staff, Map<String, Object> condition) {
        logger.debug("ITradeServiceDubbo中批发收银方法被调用,方法:querySaleOrderByCondition");
        return saleOrderService.queryList(staff, condition);
    }

    @Override
    public void deleteSaleOrderByCondition(Staff staff, Map<String, Object> condition) {
        saleOrderService.delete(staff, condition);
    }

    @Override
    public void deleteTradesByCondition(Staff staff, TradeDeleteParams params) {
        sysTradeService.deleteTradesByCondition(staff, params);
    }

    @Override
    public Trade pickingTrade(Staff staff, Long staffId, String staffName, String outSid, boolean force) {
        return sysTradeService.pickingTrade(staff, staffId, staffName, outSid, force, false, null);
    }

    @Override
    public List<Trade> pickingTrades(Staff staff, Long staffId, String staffName, Long waveId, boolean force) {
        return sysTradeService.pickingTrades(staff, staffId, staffName, waveId, force, false, null);
    }

    @Override
    public List<Trade> pickingTrades(Staff staff, Long staffId, String staffName, Long waveId, boolean force, boolean consign, String clientIP) {
        return sysTradeService.pickingTrades(staff, staffId, staffName, waveId, force, consign, clientIP);
    }

    @Override
    public Map<String, Object> pickingBatchByWavePosition(Staff staff, Long staffId, String staffName, boolean force, boolean consign, String clientIP, String firstOutId, String lastOutId) {
        return sysTradeService.pickingBatchByWavePosition(staff, staffId, staffName, force, consign, clientIP, firstOutId, lastOutId);
    }

    @Override
    public WavePickingScanResult pickingPapersScanCode(Staff staff, Long waveId, Integer isExcep) {
        return sysTradeService.pickingPapersScanCode(staff, waveId, isExcep);
    }

    @Override
    public SaleTrades querySaleTradeByParams(Staff staff, SaleTradeQueryParam queryParam) {
        throw new IllegalArgumentException("老版接口已弃用，请使用ISaleTradeServiceDubbo.queryByPage");
    }

    @Override
    public SaleTrade settleV2SaleTrade(Staff staff, SaleTrade saleTrade) {
        throw new IllegalArgumentException("老版接口已弃用，请尽快升级pda版本!");
    }

    @Override
    public TradeInfoVO tradeGetBySid(TradeGetRequest request) {
        if (request.getSid() == null) {
            return null;
        }
        List<Trade> trades = tradeSearchService.queryBySids(request.getStaff(), request.isQueryOrders(), request.getSid());
        if (CollectionUtils.isNotEmpty(trades)) {
            Trade trade = trades.get(0);
            List<Long> sids = Lists.newArrayList();
            if (trade.getMergeSid() > 0) {
                List<TbTrade> mergeTrades = tbTradeDao.queryByKeys(request.getStaff(), "sid", "merge_sid", trade.getMergeSid());
                sids = mergeTrades.stream().map(TbTrade::getSid).collect(Collectors.toList());
            } else {
                sids.add(request.getSid());
            }
            TradePayGetParams params = new TradePayGetParams();
            params.setSids(sids);
            List<TradePayVO> tradePayVOS = tradePayService.tradePayGet(request.getStaff(), params);
            return new TradeInfoVO(trade, tradePayVOS);
        }
        return null;
    }

    @Override
    public PageList<Long> dangkouSidsGet(DangkouSidsGetRequest request) {
        PageList<Long> pageList = new PageList<>();
        if (request.getStartDate() == null || request.getEndDate() == null) {
            return pageList;
        }
        if (request.isNeedTotal()) {
            Long total = tbTradeDao.countDangkouTradeSid(request);
            pageList.setTotal(total);
        } else {
            pageList.setTotal(-1L);
        }
        List<Long> sids = tbTradeDao.queryDangkouTradeSid(request);
        pageList.setList(sids);
        return pageList;
    }

    @Override
    public void haltSaleTrade(Staff staff, SaleTrade saleTrade) {
        throw new IllegalArgumentException("老版接口已弃用，请尽快升级pda版本!");
    }

    @Override
    public void cancelHaltSaleTrade(Staff staff, Long id) {
        saleTradeService.delete(staff, SaleTradeQueryParam.builder().id(id).stVersion(2).status(1).build());
    }

    @Override
    public Map<String, Object> validateAvailableInStock(Staff staff, StallTrade stallTrade) {
        throw new IllegalArgumentException("老版接口已弃用，请尽快升级pda版本!");
    }

    @Override
    public List<Long> querySupplierFromShareUrl(Staff staff) {
        return shareUrlService.querySupplierFromShareUrl(staff);
    }

    @Override
    public ShareUrlVO queryShareUrlBySupplierId(Staff staff, Long supplierId) {
        return shareUrlService.queryBySupplierId(staff, supplierId);
    }

    @Override
    public List<ShareUrlVO> queryBySupplierIds(Staff staff, List<Long> supplierIds) {
        return shareUrlService.queryBySupplierIds(staff, supplierIds);
    }

    @Override
    public List<ShareUrlVO> queryShareUrlsByCondition(Staff staff, ShareUrlDTO condition) {
        return shareUrlService.queryShareUrlsByCondition(staff, condition);
    }

    @Override
    public void supplierChangeAutoDisableUrl(Staff staff, Long supplierId) {
        shareUrlService.supplierChangeAutoDisableUrl(staff, supplierId);
    }

    @Override
    public List<WaveTradeSplitResult> tradeSplitWave(Staff staff, TradeSplitWaveGenericParams params) {
        return waveSplitGenericService.tradeSplitWave(staff, params);
    }

    @Override
    public WaveTradeSplitResult orderUniqueCodeNewSplit(Staff staff, OrderUniqueCodeSplitParams params) {
        return orderUniqueCodeNewSplitBusiness.singleSplit(staff, params);
    }

    @Override
    public List<String> batchQueryByReceiver(Staff staff, BatchRequestParameter parameter) {
        return tbTradeDecryptBusiness.batchQueryByReceiver(staff, parameter);
    }

    @Override
    public Trades customSearch(Long companyId, boolean searchWithin3Month, String startTime, String endTime, Page page, String... fields) {
        return tradeCustomDao.customSearch(companyId, searchWithin3Month, startTime, endTime, page, fields);
    }

    @Override
    public Trades advancedCustomSearch(TradeQueryParams params, boolean searchWithin3Month, String... fields) {
        return tradeCustomDao.advancedCustomSearch(params, searchWithin3Month, fields);
    }

    @Override
    public void createWaveSortings(Staff staff, WavePicking wavePicking, List<Long> sids, boolean careGift) {
        tradePostPrintService.createWaveSortings(staff, wavePicking, sids, careGift, false);
    }

    @Override
    public Map<Long, Boolean> party3WarehouseUpdateDefinedException(final Staff staff, final Long[] sids, final Long[] exceptIds) {
        List<Trade> updates = tradeExceptBusiness.update(staff, sids, exceptIds, false, null, 0, false);

        Map<Long, Boolean> resultMap = Arrays.stream(sids).filter(Objects::nonNull).collect(Collectors.toMap(sid -> sid, sid -> false));
        for (Trade trade : updates) {
            resultMap.put(trade.getSid(), true);
        }
        return resultMap;
    }


    @Override
    public Map<Long, Boolean> tagExcepTrade(Staff staff, Long[] sids, Long[] exceptIds) {
        return party3WarehouseUpdateDefinedException(staff, sids, exceptIds);
    }

    @Override
    public Integer queryPackingNumberByStaff(Staff staff, Date date) {
        return tradeCheckGoodsDao.queryCheckGoodCountByTime(staff, date);
    }

    @Override
    public TradeReceiverInfo decryptReceiverInfo(Staff staff, Trade trade) {
        try {
            return unionTradeDecryptBusiness.decryptTrade(staff, trade, TradeDecryptSourceEnum.DUBBO_DECRYPT, null, false, null);
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("解密收件人信息，调用新解密接口失败 sid:").append(trade.getSid()).append(" error:").append(e.getMessage()).toString());
            return oldDecryptReceiverInfo(staff, trade);
        }
    }

    @Override
    public TradeReceiverInfo decryptReceiverInfo(Staff staff, Long sid) {
        if (null == sid) {
            return null;
        }
        List<Trade> trades = tradeSearchService.queryBySids(staff, false, sid);
        if (CollectionUtils.isNotEmpty(trades)) {
            try {
                return decryptReceiverInfo(staff, trades.get(0));
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "解密收件人信息，调用解密接口失败，sid=" + sid), e);
            }
        }
        return null;
    }

    private TradeReceiverInfo oldDecryptReceiverInfo(Staff staff, Trade trade) {
        logger.warn(LogHelper.buildLogHead(staff).append("解密收件人信息，调用新解密接口失败,兜底尝试走原有逻辑 sid:").append(trade.getSid()).toString());
        try {
            User user;
            if (TradeUtils.isGxTrade(trade)) {
                user = userService.queryById(trade.getTaobaoId());
            } else {
                user = staff.getUserByUserId(trade.getUserId());
            }
            if (CommonConstants.PLAT_FORM_TYPE_FXG.equals(user.getSource())) {
                logger.info(LogHelper.buildLogHead(user).append("采用 fxgTradeDecryptBusiness 解密 source:").append(user.getSource()).toString());
                return fxgTradeDecryptBusiness.decryptReceiverInfo(staff, user, trade, TradeDecryptSourceEnum.DUBBO_DECRYPT);
            } else if (TradeUtils.platformContain(staff, trade, CommonTradeDecryptBusiness.SUPPORT_PLATFORM)) {
                logger.info(LogHelper.buildLogHead(user).append("采用 commonTradeDecryptBusiness 解密 source:").append(user.getSource()).toString());
                return commonTradeDecryptBusiness.decryptReceiverInfo(staff, trade, TradeDecryptSourceEnum.DUBBO_DECRYPT);
            } else {
                logger.info(LogHelper.buildLogHead(user).append("采用 tbTradeDecryptBusiness 解密 source:").append(user.getSource()).append(" addressMd5:").append(trade.getAddressMd5()).toString());
                return tbTradeDecryptBusiness.generalDecrypt(user, trade);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "解密收件人信息，兜底调用原解密接口失败，sid:" + trade.getSid()), e);
            throw e;
        }
    }


    @Override
    public List<TradeReceiverInfo> decryptReceiverInfoBySids(Staff staff, List<Long> sids, boolean ignoreErrors) {
        if (CollectionUtils.isEmpty(sids)) {
            return new ArrayList<>();
        }
        List<Trade> trades = tradeSearchService.queryBySids(staff, false, sids.toArray(new Long[0]));
        return decryptReceiverInfos(staff, trades, ignoreErrors);
    }

    @Override
    public List<TradeReceiverInfo> decryptReceiverInfos(Staff staff, List<Trade> trades, boolean ignoreErrors) {
        if (CollectionUtils.isEmpty(trades)) {
            return Collections.emptyList();
        }
        return unionTradeDecryptBusiness.decryptTrades(staff, trades, null, TradeDecryptSourceEnum.DUBBO_DECRYPT, ignoreErrors, null, null);
    }

    @Override
    public void saveScanRecord(Staff staff, List<UniqueCodeScanRecordBO> uniqueCodeScanRecordBOList) {
        uniqueCodeScanRecordService.saveScanRecord(staff, uniqueCodeScanRecordBOList);
    }

    @Override
    public List<WaveUniqueCode> queryItemUniqueCodeByCondition(Staff staff, ItemUniqueCodeQueryParams params) {
        // ItemSearchService 不加载IWaveServiceDubbo
        return iItemUniqueCodeService.queryItemUniqueCodeByCondition(staff, params);
    }

    @Override
    public Trade getOrderInfoByOutSid(Staff staff, String outId) {
        return iItemUniqueCodeService.getOrderInfoByOutSid(staff, outId);
    }

    @Override
    public Trade getOrderInfoByOutSidNew(Staff staff, String outId, Boolean fillSupplier) {
        return iItemUniqueCodeService.getOrderInfoByOutSidNew(staff, outId, fillSupplier);
    }

    @Override
    public void addTradeTrace(Staff staff, TradeTrace tradeTrace) {
        tradeTraceService.addTradeTrace(staff, tradeTrace);
    }

    @Override
    public void batchAddTradeTrace(Staff staff, List<TradeTrace> tradeTraces) {
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
    }

    @Override
    public List<TradeItemPackLog> queryItemPackLogById(Staff staff, com.raycloud.dmj.domain.trades.TradeItemPackLog log) {
        return tradeItemPackService.queryById(staff, log);
    }

    @Override
    public void updateItemPackLog(Staff staff, List<TradeItemPackLog> tradeItemPackLogs) {
        tradeItemPackService.update(staff, tradeItemPackLogs);
    }

    @Override
    public void party3Callback(Staff staff, TradeParty3Response response) {
        tradeParty3Business.party3Callback(staff, response);
    }

    @Override
    public List<TradeExt> getTradeExtBySid(Staff staff, List<Long> sids) {
        return tradeSearchService.getTradeExtBySid(staff, sids);
    }

    @Override
    public PackWaveResult packTradesWave(Staff staff, Long waveId, String ip) {
        return sysTradeService.packTradesWave(staff, waveId, ip);
    }

    @Override
    public PackWaveResult packTradesCombineParcel(Staff staff, Long combineParcelId, String clientIp) {
        return sysTradeService.packTradesCombineParcel(staff, combineParcelId, clientIp);
    }

    @Override
    public Map<String, String> cancelInsufficient(Staff staff, List<Trade> trades) {
        return cancelInsufficientBusiness.cancel(staff, trades);
    }

    @Override
    public TradeUpdateResponse updateTradeSellerMemoAndFlag(Staff staff, String memo, Long flag, boolean append, Long userId, String tid) {
        TradeMemo tradeMemo = TradeMemo.builder()
                .sellerMemo(memo)
                .sellerFlag(flag)
                .handType(TradeMemoTypeEnum.PLAT_SELLER_EMEO_FLAG.getType())
                .handFrom(TradeMemoFromEnum.WEB.getType())
                .build();
        return tradeUpdateSellerMemoFlagBusiness.uploadByTid(staff, tradeMemo, userId, tid);
    }

    @Override
    public Trade queryByPickingParams(Staff staff, Long staffId, String staffName, TradePickingParams tradePickingParams, boolean force, boolean consign, String clientIP) {
        return sysTradeService.queryByPickingParams(staff, staffId, staffName, tradePickingParams, force, consign, clientIP);
    }

    @Override
    public void consumePackMa(Staff staff, String text, JSONArray packMaOuterIdArr, String clientId) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (Object packmaOuterId : packMaOuterIdArr) {
            if (!(packmaOuterId instanceof JSONObject)) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            JSONObject jsonObject = (JSONObject) packmaOuterId;
            map.put("outerId", jsonObject.getString("outerId"));
            map.put("amount", jsonObject.getInteger("amount"));
            list.add(map);
        }
        eventCenter.fireEvent(this, new EventInfo("trade.pack.packma.item").setArgs(new Object[]{staff, Long.parseLong(text), list, clientId}), null);
    }

    @Override
    public void updateShareUrlPassword(Staff staff, ShareUrlDTO shareUrlDTO) {
        shareUrlService.updatePassword(staff, shareUrlDTO);
    }

    @Override
    public ShareUrlVO queryShareTabTotal(Staff staff, Long supplierId) {
        return shareUrlService.queryShareTabTotal(staff, supplierId);
    }

    @Override
    public void tradeNewWeight(TradeWeightParams tradeWeightParams) {
        sysTradeService.tradeNewWeight(tradeWeightParams);
    }

    @Override
    public TradeValidator checkTradeWeight(Staff staff, Trade trade, TradeValidator validator, Integer kind, String outSid) {
        tradeValidateBusiness.checkTradeWeight(staff, trade, validator, queryTradeConfig(staff), kind, true, outSid, null);
        return validator;
    }

    @Override
    public List<WavePickerInfo> queryPickingParams(Staff staff, WaveFilterParams params, Page page) {
        return tradeWaveService.queryPickingParams(staff, params, page);
    }

    @Override
    public List<Trade> sensitiveTrades(Staff staff, List<Trade> trades) {
        dataSecurityHandleService.sensitiveTrade(staff, trades, true);
        return trades;
    }

    @Override
    public void modifyUnAuditTradeReceiveAddress(Staff staff, ModifyUnAuditTradeReceiveAddressRequest req) {
        Assert.notNull(req, "ModifyUnAuditTradeReceiveAddressRequest 对象不能为空！");
        Assert.notNull(req.getSid(), "系统订单号不能为空!");
        Assert.hasLength(req.getReceiverAddress(), "详细地址必填");

        Assert.isTrue(req.getReceiverAddress().length() <= 228, "详细收货地址字符长度不能超过228个");
        logger.info(LogHelper.buildLog(staff, "快麦开放平台更新待审核订单地址"));

        List<Trade> trades = queryTradeBySids(staff, false, req.getSid());
        Assert.isTrue(trades != null && !trades.isEmpty(), String.format("sid{%s}, 查询订单为空！", req.getSid()));
        for (Trade trade : trades) {
            Assert.isTrue(TradeStatusUtils.isWaitAudit(trade.getSysStatus()), String.format("sid{%s}, sysStatus{%s}, 非待审核订单", trade.getSid(), trade.getSysStatus()));
            Assert.isTrue(!TradeUtils.isMerge(trade) && !TradeUtils.isSplit(trade), String.format("sid{%s}, 合单或拆单, 不能修改地址", trade.getSid()));
        }

        Trade updateShippingTrade = req.convertToTrade(trades.get(0));

        sysTradeService.updateShippingAddress(staff, updateShippingTrade, true);
    }

    @Override
    public Wave finishInspectionRegistration(Staff staff, String firstOutSid, String lastOutSid, boolean forceRegister, boolean complete) {
        TradeCheckGoodsParams params = new TradeCheckGoodsParams();
        params.setFirstOutSid(firstOutSid);
        params.setLastOutSid(lastOutSid);
        params.setForce(forceRegister);
        params.setComplete(complete);
        return checkGoodsServicePc.finishByWaveAndPosition(staff, params);
    }

    @Override
    public Wave finishInspectionRegistration(Staff staff, TradeCheckGoodsParams params) {
        return checkGoodsServicePc.finishByWaveAndPosition(staff, params);
    }

    @Override
    @Transactional
    public void splitWaveTrade(Staff staff, Long[] sidArr, ProgressData progressData, Map<Long, List<Order>> splitMap, List<Long> unNeedSpiltIds, Boolean unAudit) {
        List<WaveTradeSplitResult> results = shipBoxWaveSplitBusiness.split(staff, sidArr, progressData, splitMap, unAudit);
        List<Long> splitIds = results.stream().map(WaveTradeSplitResult::getSplitSid).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unNeedSpiltIds)) {
            splitIds.addAll(unNeedSpiltIds);
        }
        Long[] splitIdArr = splitIds.toArray(new Long[0]);
        tradeAuditService.unaudit(staff, OpEnum.AUDIT_UNDO_AUTO_WAVE_SPLI, splitIdArr);
        Long[] excepts = new Long[1];
        // 反审核操作发送事件修改数据有延时 每一万条数据增加睡眠时间疫苗
        try {
            Long sleepTime = 2000L + (splitIdArr.length / 10000L) * 1000L;
            Thread.sleep(sleepTime);
        } catch (Exception e) {
            e.getMessage();
        }
        excepts[0] = SystemExcepts.SHIP_BOX_SPLIT_ORDER.getId();
        // 将新拆分出来的订单设置异常标签
        tradeExceptBusiness.update(staff, splitIdArr, excepts, true, null, 1, true);
    }

    @Override
    public void saveOrderProduct(Staff staff, List<OrderProduct> orderProducts) {
        Assert.notEmpty(orderProducts, "批次信息不能为空");
        orderProductService.save(staff, orderProducts);
    }

    @Override
    public List<UserWlbExpressTemplate> querySomeFieldsByParams(Staff staff, SearchWlbTemplateParams params) {
        return wlbTemplateService.querySomeFieldsByParams(staff, params);
    }

    @Override
    public Long filterTradeTemplateInfo(Staff staff, Long templateId, Integer templateType) {
        if (templateId == null || templateId <= 0 || templateType == null) {
            return 0L;
        }
        IExpressTemplateBase template = null;
        if (templateType == 0) {
            template = templateService.userQuery(staff, templateId, false);
        } else if (templateType == 1) {
            template = wlbTemplateService.userQuery(staff, templateId, false);
        } else {
            logger.error(LogHelper.buildLog(staff, "快递模版类型找不到"));
        }
        if (template == null) {
            return 0L;
        }
        return template.getExpressId();
    }

    @Override
    public void weightBatchByWave(Staff staff, Long waveId, String weight, String cost, Integer kind) {
        Assert.notNull(waveId, "请传入波次号");
        //如果需要进行沉重称重
        Assert.notNull(weight, "重量不能为空！");
        Assert.notNull(kind, "称重类型不能为空！");
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_ACCORDING_WAVE_WEIGHT_BATCH), "上一次批量称重还未执行完毕，请稍等！");
        //异步处理
        eventCenter.fireEvent(this, new EventInfo("trade.weight.batch").setArgs(new Object[]{staff, waveId, weight, cost, kind, null}), null);
        waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.PDA_WAVE_WEIGHT, String.format("执行波次称重，重量：【%s】,费用：【%s】", weight, cost)));
    }

    @Override
    public CalculateFreightCostResult calculateFreightCostAndWeightBatchByWave(Staff staff, Long sid, Long waveId, String weight, String cost, Integer kind) {
        Assert.notNull(waveId, "请传入波次号");
        //如果需要进行沉重称重
        Assert.notNull(weight, "重量不能为空！");
        Assert.notNull(kind, "称重类型不能为空！");
        CalculateFreightCostResult calculateFreightCostResult;
        if (StringUtils.isEmpty(cost)) {
            calculateFreightCostResult = sysTradeService.calculateFreightCost(new CalculateFreightCostParams.Builder().sid(sid).weight(weight).staff(staff).build());
            FreightCost freightCost = calculateFreightCostResult.getCost();
            if (!freightCost.isError()) {
                cost = String.valueOf(freightCost.getCount());
            } else {
                throw new IllegalArgumentException("称重时计算运费失败，" + freightCost.getReason());
            }
        } else {
            FreightCost freightCost = new FreightCost();
            freightCost.setSid(sid);
            freightCost.setError(false);
            freightCost.setCount(Double.valueOf(cost.trim()));
            calculateFreightCostResult = new CalculateFreightCostResult(sid, freightCost);
        }
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_ACCORDING_WAVE_WEIGHT_BATCH), "上一次批量称重还未执行完毕，请稍等！");
        //异步处理
        eventCenter.fireEvent(this, new EventInfo("trade.weight.batch").setArgs(new Object[]{staff, waveId, weight, cost, kind, null}), null);
        waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.PDA_WAVE_WEIGHT, String.format("执行波次称重，重量：【%s】,费用：【%s】", weight, cost)));
        return calculateFreightCostResult;
    }

    @Override
    public CalculateFreightCostResult calculateFreightCostAndWeightBatchByWave(Staff staff, WaveWeightParam waveWeightParam) {
        Long sid = waveWeightParam.getSid();
        Long waveId = waveWeightParam.getWaveId();
        String weight = waveWeightParam.getWeight();
        String cost = waveWeightParam.getCost();
        Integer kind = waveWeightParam.getKind();
        String packmaOuterIds = waveWeightParam.getPackmaOuterIds();
        Assert.notNull(waveId, "请传入波次号");
        //如果需要进行沉重称重
        Assert.notNull(weight, "重量不能为空！");
        Assert.notNull(kind, "称重类型不能为空！");
        CalculateFreightCostResult calculateFreightCostResult;
        if (StringUtils.isEmpty(cost)) {
            calculateFreightCostResult = sysTradeService.calculateFreightCost(new CalculateFreightCostParams.Builder().sid(sid).weight(weight).staff(staff).build());
            FreightCost freightCost = calculateFreightCostResult.getCost();
            if (!freightCost.isError()) {
                cost = String.valueOf(freightCost.getCount());
            } else {
                throw new IllegalArgumentException("称重时计算运费失败，" + freightCost.getReason());
            }
        } else {
            FreightCost freightCost = new FreightCost();
            freightCost.setSid(sid);
            freightCost.setError(false);
            freightCost.setCount(Double.valueOf(cost.trim()));
            calculateFreightCostResult = new CalculateFreightCostResult(sid, freightCost);
        }
        Assert.isTrue(!progressService.hasProgress(staff, ProgressEnum.PROGRESS_ACCORDING_WAVE_WEIGHT_BATCH), "上一次批量称重还未执行完毕，请稍等！");
        //异步处理
        eventCenter.fireEvent(this, new EventInfo("trade.weight.batch").setArgs(new Object[]{staff, waveId, weight, cost, kind, null, null, packmaOuterIds}), null);
        waveTraceService.addWaveTrace(staff, WaveTraceUtils.buildWaveTrace(staff, waveId, WaveTraceOperateEnum.PDA_WAVE_WEIGHT, String.format("执行波次称重，重量：【%s】,费用：【%s】", weight, cost)));
        return calculateFreightCostResult;
    }

    @Override
    public String loginId2OpenUid(User user, String loginId) {
        return tradeOpenUidTransformBusiness.loginId2OpenUid(user, loginId);
    }

    @Override
    public String openUid2LoginId(User user, String openUid) {
        return tradeOpenUidTransformBusiness.openUid2LoginId(user, openUid);
    }

    /**
     * 三个月之前订单的查询
     *
     * @param staff
     * @param params
     * @return
     */
    @Override
    public Trades search3Month(Staff staff, TradeQueryParams params, boolean onlyQuery3monthAgo) {
        return tradeDataService.search3Month(staff, params, onlyQuery3monthAgo);
    }


    @Override
    public List<ReplenishTrades> checkReplenishTrade(Staff staff, Long... sids) {
        Assert.notEmpty(sids, "订单sid信息不能为空");
        return tradeSearchService.checkReplenishTrade(staff, sids);
    }

    @Override
    public List<Party3TradeConsignVO> party3TradePreConsign(Staff staff, List<Party3TradeConsignDTO> party3TradeConsignDTOList) {
        return tradeParty3ConsignBusiness.party3TradePreConsign(staff, party3TradeConsignDTOList);
    }

    @Override
    public MultiPacksPrintTradeLog getMultiPacksPrintTradeLogAndDetail(Staff staff, String outSid) {
        MultiPacksPrintTradeLog log = multiPacksPrintTradeLogService.queryByOutSid(staff, outSid);
        if (log != null && CollectionUtils.isNotEmpty(log.getDetails())) {
            for (MultiPacksPrintTradeLogDetail logDetail : log.getDetails()) {
                if (StringUtils.isEmpty(logDetail.getOutSid())) {
                    logDetail.setPrimary(false);
                }
                logDetail.setPrimary(logDetail.getOutSid().equals(log.getOutSid()));
            }
        }
        return log;
    }

    @Override
    public Map<String, String> checkTradeReplaceItemsRule(Staff staff, List<Long> itemIds, List<Long> skuIds) {
        return tradeItemReplaceRuleService.checkRules(staff, itemIds, skuIds);
    }

    @Override
    public void fillMergeTradeInfo(Staff staff, List<Trade> trades) {
        tradePostPrintService.fillMergeTradeInfo(staff, trades);
    }

    @Override
    public List<Party3TradeConsignVO> party3TradeConsign(Staff staff, List<Party3TradeConsignDTO> party3TradeConsignDTOList) {
        return tradeParty3ConsignBusiness.party3TradeConsign(staff, party3TradeConsignDTOList);
    }

    @Override
    public List<FreightSettlementResp> calculateFreightCostBySids(Staff staff, FreightCalculationParam param) {
        return freightTemplateService.calculateFreightCostBySids(staff, param);
    }

    @Override
    public List<FreightSettlementResp> calculateFreightCostByAddr(Staff staff, List<FreightSettlementParam> param) {
        return freightTemplateService.calculateFreightCostByAddr(staff, param);
    }


    @Override
    public void fillTradeLogisticsCompanyName(Staff staff, List<Trade> tradeList) {
        tradeWaveService.fillTradeLogisticsCompanyName(staff, tradeList);
    }

    @Override
    public List<UserLogisticsCompany> queryAllLogisticsCompany(Staff staff) {
        return userLogisticsCompanyService.queryAllLogisticsCompany(staff);
    }

    @Override
    public List<Long> getPickedMatchRangeSidList(Staff staff, OrderUniqueCodeQueryParams params) {
        return orderUniqueCodePositionAnalyseService.getPickedMatchRangeSidList(staff, params);
    }

    @Override
    public List<ConsignRecord> getConsignRecordList(Staff staff, ConsignRecordQueryParams params) {
        return consignRecordService.list(staff, params);
    }

    @Override
    public List<UserLogisticsCompany> queryByLogisticsCompanyIds(Staff staff, List<Long> ids) {
        return userLogisticsCompanyService.queryByIds(staff, ids);
    }

    @Override
    public List<VipDeliveryVO> getVipDeliveryList(Staff staff, VipDeliveryParams params) {
        return iVipDeliveryDataService.getVipDeliveryList(staff, params);
    }

    @Override
    public List<TradeTag> queryLabels(Staff staff, Integer type){
        return tradeTagService.list(staff, type);
    }

    @Override
    public void saveLabels(Staff staff, List<Long> sids, List<TradeTag> tags, boolean add) {
        if (CollUtil.isEmpty(tags)) {
            return;
        }
        Map<Long, TradeTag> systemTagsMap = tradeSysLabelBusiness.getSystemTagsMap();
        List<TradeTag> systemTags = new ArrayList<>();
        List<Long> otherTagIds = new ArrayList<>();
        for (TradeTag tag : tags) {
            if (systemTagsMap.containsKey(tag.getId())) {
                systemTags.add(tag);
            } else {
                otherTagIds.add(tag.getId());
            }
        }
        if (CollUtil.isNotEmpty(systemTags)) {
            tradeSysLabelBusiness.handleTagsBySids(staff, sids, add ? OpEnum.ADD_TAG : OpEnum.REMOVE_TAG, systemTags, add);
        }
        if (CollUtil.isNotEmpty(otherTagIds)) {
            Integer mode = (add ? TradeLabelOpEnum.TRADE_CUS_LABEL_ADD : TradeLabelOpEnum.TRADE_CUS_LABEL_REMOVE).getCode();
            tradeTagBusiness.setTags(staff, sids, otherTagIds, mode);
        }
    }

    /**
     * 目前仅支持发货上传异常
     */
    @Override
    public void saveTradeErrors(Staff staff, List<SaveTradeErrorParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }

        Long[] sids = params.stream().map(SaveTradeErrorParam::getSid).toArray(Long[]::new);
        lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            Map<Long, Trade> sid2trade = tradeSearchService.queryBySids(staff, false, sids).stream()
                    .collect(Collectors.toMap(TradeBase::getSid, java.util.function.Function.identity(), (o1, o2) -> o1));

            List<Trade> updTrades = Lists.newArrayList();
            for (SaveTradeErrorParam param : params) {
                Trade trade;
                if (!UPLOAD_EXCEPT.equals(param.getExceptEnum()) ||
                        Objects.isNull(param.getIsAdd()) ||
                        Objects.isNull(trade = sid2trade.get(param.getSid()))) {
                    Logs.warn(LogHelper.buildLog(staff, String.format("忽略异常处理: %s", JSON.toJSONString(param))));
                    continue;
                }

                Trade updateTrade = new TbTrade();
                updateTrade.setSid(trade.getSid());
                updateTrade.setTid(trade.getTid());
                updateTrade.setV(trade.getV());
                updateTrade.setSource(trade.getSource());
                if (param.getIsAdd()) {
                    updateTrade.addV(4);
                } else {
                    updateTrade.removeV(4);
                }
                updTrades.add(updateTrade);
            }
            if (CollectionUtils.isNotEmpty(updTrades)) {
                tradeUpdateService.updateTrades(staff, updTrades);
            }
            return null;
        });
    }

    @Override
    public void saveTradeInvoice(Staff staff, InvoiceHandleFrom from, List<TradeInvoice> tradeInvoices) {
        tradeInvoiceService.saveTradeInvoices(staff, from, tradeInvoices);
    }

    @Override
    public Map<String, String> mergeSpider(Staff staff, Long[] sids, TradeMergeEnum tradeMergeEnum) {
        return tradeMergeService.mergeSpider(staff, sids, tradeMergeEnum);
    }

    @Override
    public List<TradeRuleMatchSpider> ruleMatchSpider(Staff staff, Long[] sids, Integer businessType, List<Long> ruleIds) {
        return tradeRuleService.ruleMatchSpider(staff, new TradeQueryParams().setSid(sids), businessType, ruleIds);
    }

    @Override
    public List<TradeRuleHistory> queryRuleHistories(Staff staff, int businessType, long ruleId) {
        TradeRuleQuery ruleQuery = TradeRuleQuery.builder().businessType(businessType).ruleId(ruleId).build();
        return tradeRuleService.queryHistories(staff, ruleQuery);
    }

    @Override
    public TradeRuleHistory queryRuleHistory(Staff staff, int businessType, long ruleId, long versionId) {
        TradeRuleQuery ruleQuery = TradeRuleQuery.builder().businessType(businessType).ruleId(ruleId).versionId(versionId).build();
        return tradeRuleService.queryHistory(staff, ruleQuery);
    }

    @Override
    public void deleteWarehouseAllocateByWarehouseId(Staff staff, Long warehouseId) {
        warehouseAllocateBusiness.deleteRuleByWarehouseId(staff, warehouseId);
    }

    @Override
    public TradeConfig getForSync(Staff staff) {
        return tradeConfigService.getForSync(staff);
    }

    @Override
    public void syncUpdateFromPackConfig(Staff staff, Map<String, String> syncMap) {
        tradeConfigNewService.update(staff, syncMap);
    }

    @Override
    public void updateTrades4Wave(Staff staff, List<Trade> updateTrades) {
        tradePtService.saveByTrades(staff, updateTrades, TradePrintEntranceEnum.TRADE_PRINT_ENTRANCE_3001);
        tradeUpdateService.updateTrades(staff, updateTrades);

    }

    @Override
    public void updateTrades4Wave(Staff staff, List<Trade> updateTrades, List<Order> updateOrders) {
        tradePtService.saveByTrades(staff, updateTrades, TradePrintEntranceEnum.TRADE_PRINT_ENTRANCE_3001);
        tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
    }

    @Override
    public List<Trade> cancelExcep4Wave(Staff staff, List<String> systems, List<Long> customs, Long... sids) {
        ExceptHandlerDto exceptHandlerDto = new ExceptHandlerDto();
        exceptHandlerDto.setRecordTrace(true);
        exceptHandlerDto.setSystems(systems);
        exceptHandlerDto.setCustoms(customs);
        exceptHandlerDto.setSids(sids);
        return tradeCancelExceptService.cancelExcept(staff, exceptHandlerDto);
    }

    @Override
    public <T extends Trade> List<T> filterSplitTrades(Staff staff, List<T> trades, boolean isAssert) {
        return tradeSplitFilterService.filterTrades(staff, trades, isAssert);
    }

    @Override
    public List<Trade> refuseRefund(Staff staff, Long[] sids) {
        return sysTradeService.refuseRefund(staff, sids);
    }

    @Override
    public List<TradeTag> queryTradeTagByIds(Staff staff, Long... ids) {
        return tradeTagService.queryByIds(staff, ids);
    }

    @Override
    public List<TradeTag> queryTradeTagList(Staff staff, Integer type) {
        return tradeTagService.list(staff, type);
    }

    @Override
    public String secretEncodeStr(Staff staff, String str) {
        return secretBusiness.encodeStr(staff, str);
    }

    @Override
    public String secretEncodeNum(Staff staff, String num) {
        return secretBusiness.encodeNum(staff, num);
    }

    @Override
    public Map<String, String> cancelInsufficient(Staff staff, OpEnum opEnum, Long... sids) {
        return cancelInsufficientBusiness.cancel(staff, opEnum, sids);
    }

    @Override
    public List<Trade> handleTagsBySids(Staff staff, List<Long> sids, OpEnum op, List<TradeTag> tags, Boolean add) {
        return tradeSysLabelBusiness.handleTagsBySids(staff, sids, op, tags, add);
    }

    @Override
    public void updateOrders4Wave(Staff staff, Trade trade, List<Order> orders) {
        tradeUpdateBusiness.updateOrders(staff, trade, orders);
    }

    @Override
    public List<Trade> cancelAudit(Staff staff, Long[] sids, boolean throwExceptWhenCheckError) {
        return removeUnnecessaryInfo(tradeAuditService.unaudit(staff, OpEnum.AUDIT_UNDO_AUTO_DUBBO, sids).getSuccessTrades());
    }

    @Override
    public List<Trade> cancelAudit(Staff staff, OpEnum opEnum, Long[] sids){
        return removeUnnecessaryInfo(tradeAuditService.unaudit(staff, opEnum, sids).getSuccessTrades());
    }

    private List<Trade> removeUnnecessaryInfo(List<Trade> trades) {
        // KMERP-244821: 处理dubbo反序列化失败问题
        if (CollectionUtils.isNotEmpty(trades)) {
            trades.forEach(t -> t.setOperations(null));
        }
        return trades;
    }

    @Override
    public <T extends Trade> List<T> fillTradeTagName(Staff staff, List<T> trades) throws TradeFilterException {
        return tagNameFilter.filterTrades(staff, trades);
    }

    @Override
    public Trade fillTradeTagName(Staff staff, Trade trade) throws TradeFilterException {
        return tagNameFilter.filter(staff, trade);
    }

    @Override
    public boolean addTags(Staff staff, List<Trade> trades, OpEnum op, List<TradeTag> tags) {
        return tradeSysLabelBusiness.addTags(staff, trades, op, tags);
    }

    @Override
    public boolean removeTags(Staff staff, List<Trade> trades, OpEnum op, List<TradeTag> tags) {
        return tradeSysLabelBusiness.removeTags(staff, trades, op, tags);
    }

    @Override
    public void asyncTrace(Staff staff, Collection<Trade> trades, OpEnum op, Object... otherArgs) {
        tradeTraceBusiness.asyncTrace(staff, trades, op, otherArgs);
    }

    @Override
    public void updateConfig4Wave(Staff staff, String field, Object value) {
        tradeConfigService.update(staff, field, value);
    }

    @Override
    public <T extends Trade> List<T> fillTradeTemplate(Staff staff, List<T> trades) throws TradeFilterException {
        return defaultTemplateFilter.filterTrades(staff, trades);
    }

    @Override
    public void convertSuit2EntityCode4Wms(Staff staff, Long[] sids, Integer suitsConvertType) {
        entityCodeConvertService.convertSuit2EntityCode4Wms(staff, sids, suitsConvertType);
    }

    @Override
    public void packTradesWithPost(Staff staff, Long[] sids, List<String> uniqueCodes, String clientIp) {
        sysTradeService.packTradesWithPost(staff, sids, uniqueCodes, clientIp);
    }

    @Override
    public void uploadSellerMemo(Staff staff, List<Trade> trades, String action) {
        itemShutoffListener.uploadSellerMemo(staff, trades, action);
    }

    @Override
    public Trade updateMemo4Wave(Staff staff, Long sid, String memo, Long flag) {
        TradeMemo tradeMemo = TradeMemo.builder()
                .handType(TradeMemoTypeEnum.PLAT_SELLER_EMEO_FLAG.getType())
                .sellerMemo(memo)
                .sellerFlag(flag)
                .handFrom(TradeMemoFromEnum.HAND_FROM_DUBBO.getType())
                .build();
        List<Trade> updatedTrades = tradeUpdateSellerMemoFlagBusiness.update(staff, tradeMemo, new Long[]{sid}).getUpdatedTrades();
        return CollectionUtils.isNotEmpty(updatedTrades) ? updatedTrades.get(0) : null;
    }

    @Override
    public List<Trade> setTradeNotMemo(Staff staff, Long[] sids, Long[] tagIds, String notMemo, int mode) {
        return tradeTagBusiness.setTradeNotMemo(staff, sids, tagIds, notMemo, mode);
    }

    @Override
    public Order2SingleResponse order2Single(Staff staff, Order2SingleRequest request) {
        return order2SingleService.order2Single(staff, request);
    }

    @Override
    public void packTradesWithScanInfo(Staff staff, Long[] sids, List<Order> orders, List<TradePackScanInfo> packScanInfos, String clientIp) {
        sysTradeService.packTradesWithScanInfo(staff, sids, orders, packScanInfos, clientIp);
    }

    @Override
    public List<Map<String, Object>> consign(Long[] sids, com.raycloud.dmj.domain.logistics.ConsignContext consignContext) {
        return consignAuditedBusiness.consign(sids, new ConsignContext(consignContext.staff, consignContext.clientIp, consignContext.sendType, consignContext.dymmyType, consignContext.noLogisticsName, consignContext.noLogisticsTel, consignContext.paramMap, consignContext.needValid));
    }

    @Override
    public void upload(Staff staff, Long[] sids, String clientIp) {
        uploadBusiness.upload(staff, sids, clientIp);
    }

    @Override
    public void exportTrade(Staff staff, TradeExportParams tradeExportParams) throws UnsupportedEncodingException {
        tradeOrderExportBusiness.exportTrade(staff, tradeExportParams);
    }

    @Override
    public String[][] exportOrder(Staff staff, TradeExportParams exportParams, boolean isAsync, TradeExcelExportTask task, Long[] sids) {
        return tradeExportBusiness.transformExportData(tradeExportBusiness.exportOrder(staff, exportParams, isAsync, task, sids));
    }

    @Override
    public SplitResult splitAuditedKind(Staff staff, Long sid, Long[] orderIds) {
        return tradeSplitService.splitAuditedKind(staff, sid, orderIds);
    }

    @Override
    public SplitResult splitAuditedNum(Staff staff, TradeSplitEnum splitEnum, Long sid, List<Order> splitOrders, Map<Long, Long> sourceAndNewOrderIdMap, boolean needUnAudit, boolean splitFx2GxNew) {
        return splitAuditedNum(staff, splitEnum, sid, splitOrders, sourceAndNewOrderIdMap, needUnAudit, splitFx2GxNew, null);
    }

    @Override
    public SplitResult splitAuditedNum(Staff staff, TradeSplitEnum splitEnum, Long sid, List<Order> splitOrders, Map<Long, Long> sourceAndNewOrderIdMap, boolean needUnAudit, boolean splitFx2GxNew, String splitEnumMsg) {
        if (StringUtils.isNotEmpty(splitEnumMsg)) {
            splitEnum.setMsg(splitEnumMsg);
        }
        return tradeSplitService.splitAuditedNum(staff, splitEnum, sid, splitOrders, sourceAndNewOrderIdMap, needUnAudit, splitFx2GxNew);
    }

    /**
     * 根据ID查询电子模板和普通模板
     */
    @Override
    public List<IExpressTemplateBase> queryExpressTemplateByIds(Staff staff, Long[] wlbTemplateIds, Long[] commonTemplateIds) {
        List<IExpressTemplateBase> templateBases = new ArrayList<>();
        if (wlbTemplateIds != null && wlbTemplateIds.length > 0) {
            SearchWlbTemplateParams params = new SearchWlbTemplateParams();
            params.setIds(Arrays.asList(wlbTemplateIds));
            templateBases.addAll(wlbTemplateService.querySomeFieldsByParams(staff, params));
        }
        if (commonTemplateIds != null && commonTemplateIds.length > 0) {
            SearchTemplateParams params = new SearchTemplateParams();
            params.setIds(Arrays.asList(commonTemplateIds));
            templateBases.addAll(templateService.querySomeFieldsByParams(staff, params));
        }
        return templateBases;
    }

    @Override
    public Long[] mergeUndo(Staff staff, List<Long> sids) {
        return tradeMergeService.mergeUndo(staff, sids);
    }

    @Override
    public void updTheoryFreight(Staff staff, List<TradeTheoryFreightUpdParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        Long[] sids = params.stream().map(x -> x.getSid()).collect(Collectors.toList()).toArray(new Long[0]);

        lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
            Map<Long, Trade> sid2trade = tradeSearchService.queryBySids(staff, false, sids).stream()
                    .collect(Collectors.toMap(TradeBase::getSid, java.util.function.Function.identity(), (o1, o2) -> o1));

            List<Trade> updTrades = Lists.newArrayList();

            for (TradeTheoryFreightUpdParam param : params) {
                Trade trade = sid2trade.get(param.getSid());
                Assert.notNull(trade, "对应订单" + param.getSid() + "不存在");
                if (!Objects.equals(trade.getTheoryPostFee(), param.getTheoryFreight())) {
                    Trade update = new TbTrade();
                    update.setSid(trade.getSid());
                    update.setCompanyId(trade.getCompanyId());
                    update.setTheoryPostFee(param.getTheoryFreight());
                    updTrades.add(update);
                }
            }

            if (CollectionUtils.isNotEmpty(updTrades)) {
                tradeUpdateService.updateTrades(staff, updTrades);
            }
            return null;
        });

    }

    @Override
    public List<Trade> matchFxTrade(Staff staff, List<Trade> trades) {
        return simulateImportGxTradeBusiness.matchFxTrade(staff, trades);
    }

    @Override
    public Trade downloadTrade(User user, String tid) {
        return tradeDownloadService.downloadTrade(user, tid);
    }

    @Override
    public List<OrderTypeChangVo> queryOrderTypeChangLog(Staff staff, OrderTypeChangDto orderTypeChangDto) {
        Assert.notNull(orderTypeChangDto.getSid(), "订单sid信息不能为空");
        return orderTypeChangLogService.queryOrderTypeChangLog(staff, orderTypeChangDto);
    }


    @Override
    public CursorListBase<BaseTrade> searchBaseTrade(TradeQueryRequest request) {
        return tradeSearchService.searchBaseTrade(request);
    }

    @Override
    public CursorListBase<TbTrade> searchTrade(TradeQueryRequest request, TradeAssembleParams params) {
        return tradeSearchService.searchTrade(request, params);
    }

    @Override
    public List<Long> uploadExternal(Staff staff, Long sid, String outSid, String expressCode,String source) {
        boolean is1688Crm = StringUtils.isNotBlank(source)&&"1688crm".equals(source);
        Trade trade = new Trade();
        trade.setSid(sid);
        trade.setOutSid(outSid);
        trade.setExpressCode(expressCode);
        trade.setSource(source);
        return uploadBusiness.uploadExternal(staff,trade,is1688Crm);
    }

    @Override
    public List<Long> uploadExternal(Staff staff, Trade trade) {
        String source = trade.getSource();
        boolean is1688Crm = StringUtils.isNotBlank(source)&&"1688crm".equals(source);
        return uploadBusiness.uploadExternal(staff,trade,is1688Crm);
    }

    @Override
    public Object repairUploadConsign(Long companyId, String sidStr) {
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        Long[] sidArray = ArrayUtils.toLongArray(sidStr);
        if (staff != null) {
            tradeService.consignUpload(staff, sidArray, null, null, false, 0);
        }
        return Status.buildSuccessStatus();
    }

    @Override
    public List<String> updateShareUrl(Staff staff, ShareUrlDTO shareUrlDTO) {
        return shareUrlService.update(staff, shareUrlDTO);
    }

    @Override
    public TradeCombineParcelDetail getTradeCombineParcelDetail(Staff staff, Long combineParcelId) {
        return offlineCombineParcelService.getTradeCombineParcelDetail(staff, combineParcelId);
    }

    @Override
    public AuditResultDTO auditForce(Long companyId, List<Long> sids) {
        return tradeAuditDubboBusiness.auditForce(companyId, sids);
    }

    @Override
    public Long countTrade(TradeQueryRequest request, TradeAssembleParams params) {
        return tradeSearchService.countTrade(request,params);
    }

    @Override
    public List<ConsignRecord> resend(Staff staff, List<Trade> frontTrades, String clientIp, Integer flag) {
        return tradeService.resend(staff, frontTrades, clientIp, flag);
    }

    @Override
    public void changeWarehouse(Staff staff, OpEnum opEnum, Long warehouseId, Long[] sids, boolean force) {
        if (Objects.isNull(sids) || sids.length == 0) {
            return;
        }
        Assert.notNull(warehouseId, "warehouseId不能为空！");
        Logs.warn(LogHelper.buildLog(staff, String.format("外部请求换仓库，opEnum=%s,warehouseId=%s,sids=%s", opEnum, warehouseId, Arrays.asList(sids))));
        Assert.notNull(warehouseService.queryById(warehouseId), String.format("仓库不存在[仓库id=%s]", warehouseId));
        tradeWarehouseChangeBusiness.change(staff, opEnum, warehouseId, sids, force);
    }

    @Override
    public Map<String, List<OrderStockProduct>> getOriginProductNoConsiderConsign(Staff staff, List<Order> orders) {
        return orderProductServiceImpl.getOriginProductNoConsiderConsign(staff, orders);
    }

    @Override
    public TradeConfigNew getOpenLogisticsTracking(Long companyId) {
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        if (staff == null) {
            throw new IllegalArgumentException("无权限!");
        }
        // 获取新版配置
        return tradeConfigNewService.get(staff, TradeConfigEnum.OPEN_LOGISTICS_TRACKING);
    }

    @Override
    public List<Trade> setTags(Staff staff, Long[] sids, Long[] tagIds, int mode) {
        return tradeTagBusiness.setTags(staff, sids, tagIds, mode);
    }

    @Override
    @Transactional
    public SplitAuditedNumResponse splitAuditedNum(Staff staff, SplitAuditedNumRequest req) {
        if (StringUtils.isNotEmpty(req.getSplitEnumMsg())) {
            req.getSplitEnum().setMsg(req.getSplitEnumMsg());
        }
        return tradeSplitOldService.splitAuditedNum(staff, req);
    }

    @Override
    public List<OrderChangeItemRecord> saveBatchRecordList(Staff staff, long warehouseId, ChangeItemBatchSource source, List<OrderChangeItemRecord> recordList) {
        orderChangeItemRecordBusiness.saveBatchRecordList(staff, warehouseId, source, recordList);
        return recordList;
    }

    @Override
    public OrderChangeItemRecord executeOrderChangeItemRecord(Staff staff, OrderChangeItemRecord record, ChangeItemBatchSource source, TradeBatchRequest request, TradeConfig tradeConfig) {
        changeItemByRatioBusiness.executeOrderChangeItemRecord(staff, record, source, request, tradeConfig);
        return record;
    }



    @Override
    public List<WaveUniqueCode> queryByUniqueCodes(Staff staff, List<String> uniqueCodes) {
        logger.debug("ITradeServiceDubbo中波次方法被调用,方法:queryByUniqueCodes");
        return waveServiceDubbo.queryByUniqueCodes(staff, uniqueCodes);
    }

    @Override
    public void updateAsUniqueCode(Staff staff, AsUniqueCodeBatchDTO asUniqueCodeBatchDTO) {
        logger.debug("ITradeServiceDubbo中波次方法被调用,方法:updateAsUniqueCode");
        waveServiceDubbo.updateAsUniqueCode(staff, asUniqueCodeBatchDTO);
    }

    @Override
    public List<Long> querySidsByWaveId(Staff staff, Long waveId) {
        return took(() -> tradeWaveService.querySidsByWaveId(staff, waveId), staff, logger);
    }

    @Override
    public List<WaveRule> queryWaveRuleByIds(Staff staff, List<Long> ids) {
        logger.debug("ITradeServiceDubbo中波次方法被调用,方法:queryRulesByIds");
        return waveServiceDubbo.queryWaveRuleByIds(staff, ids);
    }

    @Override
    public List<TradeResult> auditInsufficient(Staff staff, Long[] sids) {
        Assert.isTrue(sids != null && sids.length > 0, "请选择要审核的订单!");
        if (MapUtils.isEmpty(staff.getUserIdMap())) {
            List<User> users = userService.queryByCompanyId(staff.getCompanyId());
            Map<Long, User> userMap = new HashMap<>();
            for (User user : users) {
                user.setStaff(staff);
                staff.getUsers().add(user);
                userMap.put(user.getId(), user);
            }
            staff.setUserIdMap(userMap);
        }
        Map<Long, TradeResult> auditResult = auditInsufficientBusiness.auditInsufficient(staff, sids, null);
        return MapUtils.isEmpty(auditResult) ? Lists.newArrayList() : new ArrayList<>(auditResult.values());
    }

    @Resource
    PaymentCheckService paymentCheckService;

    @Override
    public Map<String, JSONObject> checkPayment(Staff staff, Long sid) {
        return paymentCheckService.checkPayment(staff,sid);
    }

    @Override
    public void updateOrderRemark(Staff staff, Long orderId, String orderRemark) {
        tradeItemChangeBusiness.changeOrderRemark(staff, orderId, orderRemark);
    }
}
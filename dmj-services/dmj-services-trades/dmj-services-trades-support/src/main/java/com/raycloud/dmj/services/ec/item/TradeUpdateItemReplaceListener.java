package com.raycloud.dmj.services.ec.item;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.item.replace.TradeItemReplaceAllBusiness;
import com.raycloud.dmj.business.item.replace.support.ReplaceResult;
import com.raycloud.dmj.business.rematch.business.ReMatchBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.ec.api.CommonEventSource;
import com.raycloud.ec.api.EventSourceBase;
import com.raycloud.ec.api.IEventListener;
import com.raycloud.ec.api.annotation.ListenerBind;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * TradeItemReplaceFromUpdateTradeListener
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Component
@ListenerBind("trade.update.item.replace")
public class TradeUpdateItemReplaceListener implements IEventListener {

    @Resource
    private TradeItemReplaceAllBusiness tradeItemReplaceAllBusiness;

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private ITradeConfigService tradeConfigService;

    @Resource
    private IStaffService staffService;

    @Resource
    ReMatchBusiness reMatchBusiness;


    @Override
    public void onObserved(EventSourceBase eventSourceBase) {
        CommonEventSource event = (CommonEventSource) eventSourceBase;
        Long staffId = event.getArg(0, Long.class);
        List<Long> sids = event.getArgList(1, Long.class);
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        Staff staff = staffService.queryFullById(staffId);
        if (Objects.isNull(staff)) {
            Logs.error("未获取到到用户信息 sid: %s" + sids);
            return;
        }

        List<Trade> trades = tradeSearchService.queryBySids(staff, true, sids.toArray(new Long[] {}));
        List<ReplaceResult> results = tradeItemReplaceAllBusiness.replaceItem(staff, trades, tradeConfigService.get(staff));
        // KMERP-248361: 修改商品后，执行商品搭配失败，也需要执行后续重算业务
        failTradeSendReMatchEvent(staff, sids, results);
    }

    private void failTradeSendReMatchEvent(Staff staff, List<Long> allSids, List<ReplaceResult> results) {
        Set<Long> successSids = results.stream().filter(Objects::nonNull)
                .filter(r -> r.matchSuccess && Objects.nonNull(r.originTrade))
                .map(replaceResult -> replaceResult.originTrade.getSid())
                .collect(Collectors.toSet());
        List<Long> failedSids = allSids.stream().filter(sid -> !successSids.contains(sid)).collect(Collectors.toList());
        reMatchBusiness.reMatch(staff, failedSids, EventEnum.EVENT_CHANGE_ITEM, Boolean.TRUE);
    }
}

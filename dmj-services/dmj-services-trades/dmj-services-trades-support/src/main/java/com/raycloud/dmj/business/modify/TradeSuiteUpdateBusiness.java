package com.raycloud.dmj.business.modify;

import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.rematch.business.ReMatchBusiness;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.base.AttrCopier;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.fx.Constants;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.SuiteSingle;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptWhiteUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.DMJItemUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeOrderSyncItemTag;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeUpdateService;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.item.TradeItemContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.business.nonconsign.TradeNonConsignBusiness.addRemoveNonConsignTradeTrace;

/**
 * <AUTHOR>
 * @created 2020-04-15 19:57
 */
@Service
public class TradeSuiteUpdateBusiness {

    protected AttrCopier<Order, Order> orderCopier = new OrderCopier<>();

    @Resource
    ReMatchBusiness reMatchBusiness;

    @Resource
    ITradeOrderSyncItemTag tradeOrderSyncItemTagFill;
    @Resource
    TradeSuiteModifyExceptBizBusiness tradeSuiteModifyExceptBizBusiness;
    @Resource
    ModifyParentBusiness modifyParentBusiness;
    @Resource
    ILockService lockService;
    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    IItemServiceDubbo itemServiceDubboImpl;
    @Resource
    IOrderStockService orderStockService;
    @Resource
    ITradeUpdateService tradeUpdateService;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;

    public List<Trade> update(Staff staff, Long... sids) {
        if (TradeExceptWhiteUtils.openHandlerSuitUpdateCompanyIds(staff)) {
            return tradeSuiteModifyExceptBizBusiness.handlerSuitUpdate(staff, sids);
        }
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> update(staff, tradeSearchService.queryBySidsContainMergeTrade(staff, true, sids)));
    }

    public List<Trade> update(Staff staff, List<Trade> trades) {
        ItemChangeData data = check(staff, trades);
        List<Trade> updateTrades = new ArrayList<>();
        if (!data.updateOrders.isEmpty()) {
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            List<Long> sysItemIds = new ArrayList<>(), sysSkuIds = new ArrayList<>();
            extractItemIds(data.updateOrders, sysItemIds, sysSkuIds);
            Map<Long, DmjItem> itemMap = DMJItemUtils.itemList2Map(itemServiceDubboImpl.queryItemDetail(staff, sysItemIds, sysSkuIds, true));
            List<Order> resumeStockOrders = new ArrayList<>();
            List<Order> applyStockOrders = new ArrayList<>();
            TradeItemContext tradeItemContext = new TradeItemContext();
            data.updateOrders.removeIf(order -> {
                DmjItem item = itemMap.get(order.getItemSysId());
                int flag = checkOrderUpdate(staff, order, item);
                if (flag == 1) {
                    Order resumeStockOrder = orderCopier.copy(order, new TbOrder());
                    resumeStockOrder.setExceptData(order.getExceptData());
                    resumeStockOrder.setSuits(order.getSuits());
                    resumeStockOrders.add(resumeStockOrder);

                    order.setSuits(null);
                    OrderExceptUtils.setStockStatus(staff, order, Trade.STOCK_STATUS_EMPTY);
                    order.setStockNum(0);
                    order.setOrigin(resumeStockOrder);
                    //去除套异常
                    OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.SUITE_CHANGE, 0L);
                    order.setNonConsign(0); // KMERP-148078: 换商品后需要清理无需发货标记
                    modifyParentBusiness.fillOrderItemSkuInfos(staff, tradeItemContext, order, item, false);
                    applyStockOrders.add(order);
                    order.getOperations().put(OpEnum.SUITE_SON_UPDATE, getContent(resumeStockOrder) + " -> " + getContent(order));
                    //添加新的套件单品
                    data.insertOrders.addAll(order.getSuits());
                    return false;
                }
                boolean b = (flag == 2) && OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.SUITE_CHANGE);
                OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.SUITE_CHANGE, 0L);
                return !b;//商品档案中的套件与order中的套件完全一样，去除"套"异常
            });
            //归还原套件库存
            orderStockService.resumeOrderStockLocal(staff, resumeStockOrders, null);
            //申请新套件库存
            orderStockService.applyOrderStockLocal(staff, applyStockOrders);
            //计算trade
            Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(trades);
            data.updateOrders.stream().map(Order::getSid).distinct().map(sid -> {
                Trade trade = tradeMap.get(sid);
                if (trade.getMergeSid() - trade.getSid() != 0) {
                    calculateTrade(staff, trade, tradeConfig);
                    setTraceContent(trade);
                    updateTrades.add(buildUpdateTrade(staff, trade));
                }
                return trade.getMergeSid();
            }).distinct().forEach(mergeSid -> {
                if (mergeSid > 0) {
                    List<Trade> mergeTrades = data.mergeTradeMap.get(mergeSid);
                    for (Trade trade : mergeTrades) {
                        if (trade.getSid() - mergeSid == 0) {
                            mergeTrades.remove(trade);
                            trade.setMergeList(mergeTrades);
                            calculateTrade(staff, trade, tradeConfig);
                            updateTrades.add(buildUpdateTrade(staff, trade));
                            break;
                        }
                    }
                }
            });
            List<Order> updateOrders = new ArrayList<>(data.updateOrders.size() + resumeStockOrders.size());
            updateOrders.addAll(data.updateOrders);
            //删除原先的套件单品
            resumeStockOrders.forEach(order -> order.getSuits().forEach(son -> updateOrders.add(buildDeleteOrder(son))));
            //填充商品标签信息到order
            tradeOrderSyncItemTagFill.fillByItemDubbo(staff, data.insertOrders);
            //标记是否拣选验货
            OrderUtils.fillIsPick(data.insertOrders);
            tradeUpdateService.updateTrades(staff, updateTrades, updateOrders, null, data.insertOrders);
            //处理套件信息修改
            reMatchBusiness.reMatch(staff, TradeUtils.toSidList(updateTrades), EventEnum.EVENT_CHANGE_ITEM, Boolean.TRUE);
            // KMERP-84702: 无需发货记录日志
            for (Trade trade : updateTrades) {
                addRemoveNonConsignTradeTrace(trade, data.updateOrders.stream().filter(o -> Objects.equals(trade.getSid(), o.getSid())).collect(Collectors.toList()));
            }
            tradeTraceBusiness.asyncTrace(staff, updateTrades.stream().filter(trade -> trade.getOperations().containsKey(OpEnum.SUITE_SON_UPDATE)).collect(Collectors.toList()), OpEnum.SUITE_SON_UPDATE);
        }
        return updateTrades;
    }

    private ItemChangeData check(Staff staff, List<Trade> trades) {
        ItemChangeData data = new ItemChangeData();
        for (Trade trade : trades) {
            if (trade.getMergeSid() > 0) {
                data.mergeTradeMap.computeIfAbsent(trade.getMergeSid(), s -> new ArrayList<>()).add(trade);
                continue;
            }
            if (check(staff, trade)) {
                checkOrder(staff, trade, data);
            }
        }
        data.mergeTradeMap.forEach((mergeSid, list) -> {
            boolean success = true;
            for (Trade trade : list) {
                if (!(success = check(staff, trade))) {
                    break;
                }
            }
            if (success) {
                list.forEach(trade -> checkOrder(staff, trade, data));
            }
        });
        return data;
    }

    private boolean check(Staff staff, Trade trade) {
        if (!trade.isOutstock()) {
            if (TradeUtils.isGxOrMixTrade(trade)) {
                if (Constants.FxDefaultUserId.equals(trade.getUserId())) {
                    return true;
                }
            } else {
                User user = staff.getUserByUserId(trade.getUserId());
                if (user == null) {
                    return false;
                } else if (user.getActive() == 0) {
                    return false;
                }
            }
        }
        return trade.getIsCancel() == 0;
    }

    private void checkOrder(Staff staff, Trade trade, ItemChangeData data) {
        if (canUpdate(trade.getSysStatus())) {
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
            orders4Trade.stream().filter(order -> {
                if (canUpdate(order.getSysStatus())) {
                    Set<Integer> except = OrderUtils.parseExcept(staff, order);
                    except.remove(TradeConstants.IDX_SUITE_CHANGE);
                    except.remove(TradeConstants.IDX_HALT);
                    except.remove(TradeConstants.IDX_INSUFFICIENT);
                    //没有异常或者只有"套"异常
                    return except.isEmpty();
                }
                return false;
            }).peek(order -> fillWarehouseAndUser(order, trade)).forEach(data.updateOrders::add);
        }
    }

    private void fillWarehouseAndUser(Order order, Trade trade) {
        order.setWarehouseId(trade.getWarehouseId());
        order.setUserId(trade.getUserId());
    }

    private boolean canUpdate(String sysStatus) {
        return TradeStatusUtils.isWaitPay(sysStatus) || TradeStatusUtils.isWaitAudit(sysStatus);
    }


    private void extractItemIds(List<Order> orders, List<Long> sysItemIds, List<Long> sysSkuIds) {
        for (Order order : orders) {
            if (order.getItemSysId() > 0 && !sysItemIds.contains(order.getItemSysId())) {
                sysItemIds.add(order.getItemSysId());
            }
            if (order.getSkuSysId() > 0 && !sysSkuIds.contains(order.getSkuSysId())) {
                sysSkuIds.add(order.getSkuSysId());
            }
        }
    }

    public int checkOrderUpdate(Staff staff, Order order, DmjItem item) {
        if (item != null) {
            List<SuiteSingle> suiteList = null;
            if (order.getSkuSysId() <= 0) {
                suiteList = item.getSuiteSingleList();
            } else {
                if (item.getSkus() != null) {
                    for (DmjSku sku : item.getSkus()) {
                        if (sku.getSysSkuId() - order.getSkuSysId() == 0) {
                            suiteList = sku.getSuiteSingleList();
                            break;
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(suiteList) && CollectionUtils.isNotEmpty(order.getSuits())) {
                Map<String, List<Order>> orderMap = new HashMap<>();
                for (Order son : order.getSuits()) {
                    orderMap.computeIfAbsent(TradeItemUtils.getItemKey(son.getItemSysId(), son.getSkuSysId()), k -> new ArrayList<>()).add(son);
                }
                if (order.getSuits().size() == suiteList.size()) {
                    //检查order中的套件单品与商品档案中的单品及其比例是否一致
                    for (SuiteSingle obj : suiteList) {
                        List<Order> sonList = orderMap.get(TradeItemUtils.getItemKey(obj.getSubItemId(), obj.getSubSkuId()));
                        if (CollectionUtils.isEmpty(sonList)) {
                            return 1;
                        }
                        for (Order son : sonList) {
                            if (son.getNum() / order.getNum() - obj.getRatio() != 0) {
                                return 1;
                            }
                        }
                    }
                    return 2;
                }
                return 1;
            } else {
                return 2;
            }
        }
        return 0;
    }

    private Order buildDeleteOrder(Order order) {
        Order temp = new TbOrder();
        temp.setId(order.getId());
        temp.setEnableStatus(0);
        return temp;
    }

    private Trade buildUpdateTrade(Staff staff, Trade trade) {
        Trade updateTrade = TradeBuilderUtils.builderUpdateTrade(trade);
        updateTrade.setSid(trade.getSid());
        updateTrade.setIsExcep(trade.getIsExcep());
        updateTrade.setItemExcep(trade.getItemExcep());
        updateTrade.setInsufficientRate(trade.getInsufficientRate());
        updateTrade.setInsufficientNum(trade.getInsufficientNum());
        TradeExceptUtils.setStockStatus(staff, updateTrade, trade.getStockStatus());
        updateTrade.setOperations(trade.getOperations());
        return updateTrade;
    }

    private void calculateTrade(Staff staff, Trade trade, TradeConfig tradeConfig) {
        if (trade.getMergeList() != null) {
            TradeUtils.getOrders4Trade(trade).addAll(TradeUtils.getOrders4Trade(trade.getMergeList()));
        }
        TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
        if (trade.getMergeList() != null) {
            TradeUtils.getOrders4Trade(trade).removeIf(order -> order.getSid() - trade.getSid() != 0);
        }
        calculateItemExcep(staff, trade);
        Set<Integer> except = TradeUtils.parseExcept(staff, trade);
        trade.setIsExcep(except.isEmpty() ? 0 : 1);
        if (except.contains(TradeConstants.IDX_SUITE_CHANGE)) {
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.SUITE_CHANGE, 1L);
        }
    }

    public void calculateItemExcep(Staff staff, Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        // 计算套件修改异常
        boolean b = orders.stream().anyMatch(order -> OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.SUITE_CHANGE));
        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.SUITE_CHANGE, b);
    }

    public void setTraceContent(Trade trade) {
        String s = TradeUtils.getOrders4Trade(trade).stream().filter(order -> order.getOperations().containsKey(OpEnum.SUITE_SON_UPDATE)).map(order -> order.getOperations().get(OpEnum.SUITE_SON_UPDATE)).collect(Collectors.joining(", "));
        if (!s.isEmpty()) {
            trade.getOperations().put(OpEnum.SUITE_SON_UPDATE, "更新最新套件明细: " + s);
        }
    }

    private String getContent(Order order) {
        StringBuilder s = new StringBuilder();
        s.append(order.getSysOuterId()).append("(");
        for (int i = 0; i < order.getSuits().size(); i++) {
            if (i > 0) {
                s.append("+");
            }
            Order son = order.getSuits().get(i);
            s.append(son.getNum() / order.getNum()).append("*").append(son.getSysOuterId());
        }
        s.append(")");
        return s.toString();
    }

}

package com.raycloud.dmj.business.split;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.order.OrderModifyLogBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OrderModifyLogTypeEnum;
import com.raycloud.dmj.domain.trade.history.OrderModifyLogUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.IPlatformSupportSplitByNumInfo;
import com.raycloud.dmj.services.trades.ITradeDownloadService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 按数量拆单的business组件
 *
 * <AUTHOR>
 */
@Service
public class SplitOrderByNumBusiness {

    private TbOrderDAO tbOrderDAO;

    private OrderModifyLogBusiness orderModifyLogBusiness;

    private PlatformManagement platformManagement;

    private ITradeDownloadService tradeDownloadService;

    private TradeLocalConfigurable configurable;

    @Autowired
    public SplitOrderByNumBusiness(TbOrderDAO tbOrderDAO,
                                   OrderModifyLogBusiness orderModifyLogBusiness,
                                   PlatformManagement platformManagement,
                                   ITradeDownloadService tradeDownloadService,
                                   TradeLocalConfigurable configurable) {
        this.tbOrderDAO = tbOrderDAO;
        this.orderModifyLogBusiness = orderModifyLogBusiness;
        this.platformManagement = platformManagement;
        this.tradeDownloadService = tradeDownloadService;
        this.configurable = configurable;
    }


    public SplitNumContextData initContextData(User user, Trade trade) {

        SplitNumContextData contextData = new SplitNumContextData();
        contextData.setCurrentOrderList(TradeUtils.getOrders4Trade(trade));

        contextData.setOids(getOidsFromOrderList(contextData.getCurrentOrderList()));
        contextData.setTids(getTidsFromOrderList(contextData.getCurrentOrderList()));
        List<SplitUploadRecord> splitUploadRecords = initSplitUploadRecord(user, trade, contextData);
        contextData.setExistSplitUploadRecordList(splitUploadRecords);
        handleSuitOrder(user, contextData);

        return contextData;
    }

    /**
     * 初始化拆单发货记录，因为存在这样的一种情况就是，如果刚上线的时候，已经存在的订单是没有拆单发货记录的，这里会给他重新指定下载一下
     *
     * @param user
     * @param trade
     * @param contextData
     */
    private List<SplitUploadRecord> initSplitUploadRecord(User user, Trade trade, SplitNumContextData contextData) {
        List<SplitUploadRecord> splitUploadRecordListByOid = new ArrayList<>();
        Map<String, List<SplitUploadRecord>> splitUploadRecordListGroupByOid = new HashMap<>();
        boolean isSynced = false;
        for (SplitUploadRecord splitUploadRecord : splitUploadRecordListByOid) {
            splitUploadRecordListGroupByOid.computeIfAbsent(splitUploadRecord.getOid(), K -> new ArrayList<>()).add(splitUploadRecord);
        }
        for (String oid : contextData.getOids()) {
            List<SplitUploadRecord> splitUploadRecords = splitUploadRecordListGroupByOid.get(oid);
            if (CollectionUtils.isEmpty(splitUploadRecords)) {
                reSaveSplitUploadRecord(user, trade, splitUploadRecordListByOid);
                isSynced = true;
            } else {
                boolean isNeedSync = true;
                for (SplitUploadRecord splitUploadRecord : splitUploadRecords) {
                    if (SplitUploadRecord.NOT_UPLOAD.equals(splitUploadRecord.getUploadStatus())) {
                        isNeedSync = false;
                        break;
                    }
                }
                if (isNeedSync) {
                    reSaveSplitUploadRecord(user, trade, splitUploadRecordListByOid);
                    isSynced = true;
                }
            }
        }
        if (isSynced) {
            return null;
        }
        return splitUploadRecordListByOid;
    }

    private void reSaveSplitUploadRecord(User user, Trade trade, List<SplitUploadRecord> splitUploadRecordListByOid) {
        SplitNumContextData splitNumContextData = new SplitNumContextData();
        splitNumContextData.setCurrentSplitUploadRecordList(SplitUploadRecord.fillRecord(user, TradeUtils.getOrders4Trade(tradeDownloadService.downloadTrade(user, trade.getTid()))));
        assembSplitUploadRecordToDb(splitUploadRecordListByOid, splitNumContextData);
    }

    public boolean isAfterSendGoodsOrder(User user, Order order, List<SplitUploadRecord> splitUploadRecords, SplitNumContextData contextData) {
        for (SplitUploadRecord splitUploadRecord : splitUploadRecords) {
            if (null == splitUploadRecord.getOrderId() || SplitUploadRecord.NOT_UPLOAD.equals(splitUploadRecord.getUploadStatus())) {
                continue;
            }

            if (splitUploadRecord.getOrderId().equals(getUploadOrderId(user, order, contextData))) {
                return true;
            }

        }
        return TradeStatusUtils.isAfterSendGoods(TradeStatus.getSysStatus(order.getStatus(), null));
    }

    public Long getUploadOrderId(User user,
                                 Order order,
                                 SplitNumContextData contextData) {
        Order originSuitOrder = getOriginSuitOrder(user, order, contextData);
        if (null == originSuitOrder) {
            return order.getId();
        }
        return originSuitOrder.getId();
    }

    /**
     * 判断两个itemIds是否相等
     *
     * @param originItemIdsString
     * @param waitMatchedItemIdsString
     * @return
     */
    public boolean isMatchedItemIds(String originItemIdsString, String waitMatchedItemIdsString) {
        String[] originItemIds = originItemIdsString.split(",");
        String[] waitMatchedItemIds = waitMatchedItemIdsString.split(",");
        if (originItemIds.length != waitMatchedItemIds.length) {
            return false;
        }
        Set<String> originItemIdsSet = new HashSet<>(Arrays.asList(originItemIds));
        Set<String> waitMatchedItemIdsSet = new HashSet<>(Arrays.asList(waitMatchedItemIds));
        originItemIdsSet.removeAll(waitMatchedItemIdsSet);

        return CollectionUtils.isEmpty(originItemIdsSet);
    }

    public <T extends Trade> List<Order> getOrders4Trade(T trade) {
        return ((Orderable<Order>) trade).getOrders();
    }

    /**
     * 套件商品的更新订单信息比较复杂
     * 1. 更新整个套件（没有转单品） 通过自身的orderId
     * 2. 套件转单品更新套件子订单   套件转单品会把原始的套件本身只会enable_status=0的子订单，套件子订单都需要通过找到原始的套件本身，通过原始的套件本身的orderId去更新状态
     * 3. 套件按数量拆单            通过自身的orderId
     * 4. 套件按数量拆单在转单品     同2逻辑
     * 5. 转单品之后按数量拆单       找到原拆单，然后同2逻辑
     *
     * @param user
     * @param contextData
     */
    private void handleSuitOrder(User user, SplitNumContextData contextData) {
        Staff staff = user.getStaff();
        List<TbOrder> suitSelfOrderList = tbOrderDAO.querySuitSelfOrderByOids(staff, null, "sid",
                contextData.getCurrentOrderList().stream().map(Order::getSid).distinct().toArray(Long[]::new), contextData.getOids());
        Map<Long, TbOrder> orderIdMap = new HashMap<>();
        Map<Long, TbOrder> sidMap = new HashMap<>();
        suitSelfOrderList.forEach(suitSelfOrder -> {
            orderIdMap.put(suitSelfOrder.getId(), suitSelfOrder);
            sidMap.put(suitSelfOrder.getSid(), suitSelfOrder);
        });
        List<Long> orderIdList = contextData.getCurrentOrderList().stream().map(Order::getId).collect(Collectors.toList());
        Map<Long, List<OrderModifyLog>> groupOrderModifyLogListByOrderId =  OrderModifyLogUtils.groupOrderId(user.getStaff(), orderModifyLogBusiness.query(user.getStaff(), orderIdList, new Integer[]{}));
        if (groupOrderModifyLogListByOrderId.isEmpty()) {
            return;
        }
        List<Order> currentOrderList = contextData.getCurrentOrderList();
        for (Order currentOrder : currentOrderList) {
            //
            List<OrderModifyLog> orderModifyLogList = groupOrderModifyLogListByOrderId.get(currentOrder.getId());

            if (CollectionUtils.isEmpty(orderModifyLogList)) {
                continue;
            }

            OrderModifyLog remainOrderModifyLog = null;
            for (OrderModifyLog orderModifyLog : orderModifyLogList) {
                if (null == remainOrderModifyLog) {
                    remainOrderModifyLog = orderModifyLog;
                } else {
                    if (orderModifyLog.getModifyType() > remainOrderModifyLog.getModifyType()) {
                        remainOrderModifyLog = orderModifyLog;
                    }
                }
            }

            if(remainOrderModifyLog != null){
                if (OrderModifyLogTypeEnum.SUIT_SUB_ORDER_SPLIT_SKU.getType() == remainOrderModifyLog.getModifyType()
                        || OrderModifyLogTypeEnum.SUIT_SUB_ORDER_SPLIT_NUM.getType() == remainOrderModifyLog.getModifyType()) {
                    if (currentOrder.getId().equals(remainOrderModifyLog.getOrderId())) {
                        if (StringUtils.isBlank(remainOrderModifyLog.getContent())) {
                            // todo
                        }
                        Long relatedSuitSelfOrderId = Long.valueOf(remainOrderModifyLog.getContent());
                        TbOrder relatedSuitSelfOrder = orderIdMap.get(relatedSuitSelfOrderId);
                        Logs.ifDebug(LogHelper.buildLogHead(user).append("检测到为套件子订单的拆单，").append(buildOrderLog(currentOrder)).append(" 关联的套件子订单为").append(buildOrderLog(relatedSuitSelfOrder)));
                        contextData.getSuitSubOrderMapGroupByOrderId().computeIfAbsent(relatedSuitSelfOrder.getId(), o -> new ArrayList<>()).add(currentOrder);
                        contextData.getOriginSuitSelfOrderByOrderId().put(currentOrder.getId(), relatedSuitSelfOrder);
                    }
                }
                if (OrderModifyLogTypeEnum.SUIT_REPLACE.getType()== remainOrderModifyLog.getModifyType()
                        || OrderModifyLogTypeEnum.COMBINE_REPLACE.getType() == remainOrderModifyLog.getModifyType()) {
                    TbOrder relatedSuitSelfOrder = sidMap.get(remainOrderModifyLog.getSid());
                    if (OrderModifyLogUtils.isSuitSubOrderOrSplit(remainOrderModifyLog, currentOrder, relatedSuitSelfOrder)) {
                        contextData.getSuitSubOrderMapGroupByOrderId().computeIfAbsent(relatedSuitSelfOrder.getId(), o -> new ArrayList<>()).add(currentOrder);
                        contextData.getOriginSuitSelfOrderByOrderId().put(currentOrder.getId(), relatedSuitSelfOrder);
                        Logs.ifDebug(LogHelper.buildLogHead(user).append("检测到为套件子订单，").append(buildOrderLog(currentOrder)).append(" 关联的套件子订单为").append(buildOrderLog(relatedSuitSelfOrder)));
                    }
                }
            }
        }
    }

    public Order getOriginSuitOrder(User user, Order order, SplitNumContextData contextData) {
        return contextData.getOriginSuitSelfOrderByOrderId().get(order.getId());
    }

    private void assembSplitUploadRecordToDb(List<SplitUploadRecord> existSplitUploadRecordList, SplitNumContextData contextData) {
        if (CollectionUtils.isEmpty(existSplitUploadRecordList)) {
            contextData.getNeedInsertSplitUploadRecordList().addAll(contextData.getCurrentSplitUploadRecordList());
        } else {
            for (SplitUploadRecord currentSplitUploadRecord : contextData.getCurrentSplitUploadRecordList()) {
                if (SplitUploadRecord.NOT_UPLOAD.equals(currentSplitUploadRecord.getUploadStatus())) {
                    boolean matched = false;
                    // 一个oid只需要对应一个没有发货的拆单发货记录，所以如果数据库已经存在的话，则不需要插入这条
                    for (SplitUploadRecord existSplitUploadRecord : existSplitUploadRecordList) {
                        if (SplitUploadRecord.NOT_UPLOAD.equals(existSplitUploadRecord.getUploadStatus())) {
                            matched = true;
                            break;
                        }
                    }
                    if (!matched) {
                        contextData.getNeedInsertSplitUploadRecordList().add(currentSplitUploadRecord);
                    }
                } else {
                    boolean matched = false;
                    SplitUploadRecord matchedSplitUploadRecord = null;
                    for (SplitUploadRecord existSplitUploadRecord : existSplitUploadRecordList) {
                        if (currentSplitUploadRecord.getOid().equals(existSplitUploadRecord.getOid())
                                && isMatchedItemIds(currentSplitUploadRecord.getItemIds(), existSplitUploadRecord.getItemIds())) {
                            matched = true;
                            matchedSplitUploadRecord = existSplitUploadRecord;
                            break;
                        }
                    }
                    if (matched) {
                        // 一个已经发过货的订单如果在系统发货了，然后在指定下载会导致uploadStatus变成平台上传，所以这边做下重置
                        currentSplitUploadRecord.setUploadStatus(matchedSplitUploadRecord.getUploadStatus());
                        currentSplitUploadRecord.setOrderId(matchedSplitUploadRecord.getOrderId());
                        currentSplitUploadRecord.setSid(matchedSplitUploadRecord.getSid());
                        currentSplitUploadRecord.setId(matchedSplitUploadRecord.getId());
                        contextData.getNeedUpdateSplitUploadRecordList().add(currentSplitUploadRecord);
                    } else {
                        contextData.getNeedInsertSplitUploadRecordList().add(currentSplitUploadRecord);
                    }
                }
            }
        }
    }

    public String[] getOidsFromOrderList(List<Order> orderList) {
        return orderList.stream().map(order -> {
            Long oid = order.getOid();
            return oid.toString();
        }).collect(Collectors.toSet()).toArray(new String[]{});
    }

    public String[] getTidsFromOrderList(List<Order> orderList) {
        return orderList.stream().map(Order::getTid).collect(Collectors.toSet()).toArray(new String[]{});
    }

    public boolean isSupportSplitByNum(User user) {
        IPlatformSupportSplitByNumInfo platformSupportSplitByNum = getPlatformSupportSplitByNumInfo(user.getSource());
        if (null == platformSupportSplitByNum) {
            return false;
        }
        return configurable.isEnableSpiltOrderByNum(user.getCompanyId()) && platformSupportSplitByNum.isSupportSplitByNum();
    }

    private IPlatformSupportSplitByNumInfo getPlatformSupportSplitByNumInfo(String source) {
        try {
            return platformManagement.getAccess(source, IPlatformSupportSplitByNumInfo.class);
        } catch (Exception e) {
            return null;
        }
    }

    public String getStatus(User user, Order order) {
        List<SplitUploadRecord> splitUploadRecords = order.getSplitUploadRecords();
        if (CollectionUtils.isEmpty(splitUploadRecords)) {
            return order.getStatus();
        }
        IPlatformSupportSplitByNumInfo platformSupportSplitByNumInfo = getPlatformSupportSplitByNumInfo(user.getSource());
        for (SplitUploadRecord splitUploadRecord : splitUploadRecords) {
            if (SplitUploadRecord.NOT_UPLOAD.equals(splitUploadRecord.getUploadStatus())
                    && null != splitUploadRecord.getNum()
                    && splitUploadRecord.getNum() != 0) {
                return platformSupportSplitByNumInfo.getNotConsignStatus();
            }
        }
        return platformSupportSplitByNumInfo.getConsignStatus();
    }

    public String buildOrderLog(Order order) {
        return String.format("order[id=%d,sid=%d,tid=%s]",
                order.getId(),
                order.getSid(),
                order.getTid());
    }


    public boolean checkOrderIsNeedUpload(User user, Trade trade, Order order, SplitNumContextData contextData) {
        Map<String, List<SplitUploadRecord>> groupSplitUploadRecordMapByOid = contextData.groupSplitUploadRecordMapByOid();
        List<SplitUploadRecord> splitUploadRecordList = groupSplitUploadRecordMapByOid.get(order.getOid().toString());
        if (CollectionUtils.isEmpty(splitUploadRecordList)) {
            Logs.ifDebug(LogHelper.buildLogHead(user).append(String.format("找不到对应的拆单发货记录, 可能是个手动增加的系统商品，忽略上传，order[id=%d,sid=%d,tid=%s,oid=%d]", order.getId(), order.getSid(), order.getTid(), order.getOid())));
            return false;
        }
        if (isAfterSendGoodsOrder(user, order, splitUploadRecordList, contextData)) {
            Logs.ifDebug(LogHelper.buildLogHead(user).append(String.format("已经发货，忽略上传，order[id=%d,sid=%d,tid=%s,oid=%d]", order.getId(), order.getSid(), order.getTid(), order.getOid())));
            return false;
        }
        for (SplitUploadRecord existSplitUploadRecord : splitUploadRecordList) {
            if (SplitUploadRecord.NOT_UPLOAD.equals(existSplitUploadRecord.getUploadStatus())) {
                String itemIdsString = existSplitUploadRecord.getItemIds();
                String[] itemIds = itemIdsString.split(",");
                if (itemIds.length == 1 && StringUtils.isBlank(itemIds[0])) {
                    String msg = String.format("找不到可用的itemIds, 忽略上传 orderId=%d", order.getId());
                    Logs.ifDebug(LogHelper.buildLogHead(user).append(msg));
                    return false;
                }
                Integer num = null;
                Order originSuitOrder = getOriginSuitOrder(user, order, contextData);
                if (null != originSuitOrder) {
                    if (contextData.getSuitOrderUploadSet().contains(originSuitOrder.getSid())) {
                        String msg = String.format("检测到为已经upload的子订单, 忽略上传，order[orderId=%d,sid=%d,tid=%s]", order.getId(),
                                order.getSid(),
                                order.getTid());
                        Logs.ifDebug(LogHelper.buildLogHead(user).append(msg));
                        return false;
                    }
                    Logs.ifDebug(LogHelper.buildLogHead(user).append(String.format("检测到关联的套件子订单，重置发货数量%d -> %d, orderId重置为%d -> %d， order[orderId=%d,sid=%d,tid=%s]",
                            order.getNum(),
                            originSuitOrder.getNum(),
                            order.getId(),
                            originSuitOrder.getId(),
                            order.getId(),
                            order.getSid(),
                            order.getTid())));
                    contextData.getSuitOrderUploadSet().add(originSuitOrder.getSid());
                    order.setNeedUploadOrder(originSuitOrder);
                    num = originSuitOrder.getNum();
                }
                if (null == num) {
                    num = order.getNum();
                }
                // 有可能是在系统增加子订单数量
                if (num > itemIds.length) {
                    Logs.ifDebug(LogHelper.buildLogHead(user).append(String.format("需要发货的子订单商品数量超过可以上传发货的商品数量，重置发货数量%d -> %d, orderId=%d,itemIds=%s", num, itemIds.length, order.getId(), itemIdsString)));
                    num = itemIds.length;
                }
                StringBuilder remainItemIds = new StringBuilder();
                StringBuilder uploadItemIds = new StringBuilder();
                int remainNum = 0;
                int uploadNum = 0;
                for (int i = 0; i < itemIds.length; i++) {
                    String itemId = itemIds[i];
                    if (i < num) {
                        uploadItemIds.append(itemId);
                        if (i != num - 1) {
                            uploadItemIds.append(",");
                        }
                        uploadNum += 1;
                    } else {
                        remainItemIds.append(itemId);
                        if (i != itemIds.length - 1) {
                            remainItemIds.append(",");
                        }
                        remainNum += 1;
                    }
                }
                existSplitUploadRecord.setItemIds(remainItemIds.toString());
                existSplitUploadRecord.setNum(remainNum);
                contextData.getNeedUpdateSplitUploadRecordList().add(existSplitUploadRecord);
                SplitUploadRecord insertSplitUploadRecord = SplitUploadRecord.buildSysUploadRecord(user, null != originSuitOrder ? originSuitOrder : order, uploadItemIds.toString(), uploadNum, trade.getOutSid());
                order.setInsertSplitUploadRecord(insertSplitUploadRecord);
                contextData.getNeedInsertSplitUploadRecordList().add(insertSplitUploadRecord);
                return true;
            }
        }
        Logs.ifDebug(LogHelper.buildLogHead(user).append(String.format("找不到对应的拆单发货记录, 可能是个手动增加的系统商品，忽略上传，order[id=%d,sid=%d,tid=%s,oid=%d]", order.getId(), order.getSid(), order.getTid(), order.getOid())));
        return false;
    }

}

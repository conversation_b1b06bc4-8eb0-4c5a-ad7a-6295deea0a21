package com.raycloud.dmj.services.trade.item.entity;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.Trade;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23
 * @description 实体编码转换Filter
 */
@Service
public class TradeEntityCodeConvertFilter {

    public void filter(Staff staff, TradeItemContext itemContext, List<Trade> trades) {
        //TODO
    }
}

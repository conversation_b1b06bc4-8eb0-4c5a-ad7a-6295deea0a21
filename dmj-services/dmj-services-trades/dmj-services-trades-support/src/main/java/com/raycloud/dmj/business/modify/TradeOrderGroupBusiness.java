package com.raycloud.dmj.business.modify;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.WmsConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemKey;
import com.raycloud.dmj.domain.item.SuiteSingle;
import com.raycloud.dmj.domain.item.params.QueryItemDetailParams;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.FarERPStock;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.stock.StockQueryParams;
import com.raycloud.dmj.domain.trade.except.OrderPlatExceptUtils;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.trades.vo.TradeOrderGroupVo;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.stock.IStockServiceDubbo;
import com.raycloud.dmj.services.trades.IProgressService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <EMAIL>
 * @date 2022/7/15 17:07
 */
@Component
public class TradeOrderGroupBusiness {

    /**
     * 批量查询库存最大值
     */
    final static int ITEM_MAX_QUERY_STOCK_SIZE = 200;
    final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    IWarehouseService warehouseService;
    @Resource
    IStockServiceDubbo stockServiceDubbo;
    @Resource
    IProgressService progressService;
    @Resource
    private ITradeConfigService tradeConfigService;

    /**
     * 按key进行分组
     *
     * @param trades 订单列表包含order
     * @return
     */
    public Map<String, TradeOrderGroupVo> group(Staff staff, List<Trade> trades, boolean querySuitStock, TradeOrderGroupBusiness.ItemContext itemContext) {
        if (CollectionUtils.isEmpty(trades)) {
            return null;
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        Map<String, TradeOrderGroupVo> result = Maps.newHashMap();
        trades.forEach(t -> TradeUtils.getOrders4Trade(t).forEach(order -> {
            //过滤未匹配
            boolean matched = order != null && (order.getItemSysId() != null && order.getItemSysId() != -1) && !TradeStatusUtils.isAfterSendGoods(order.getSysStatus());

            if (matched) {
                boolean sysPicPath = getSysPicPath(order, tradeConfig, t.getSubSource());
                if (order.isSuit() && !querySuitStock) {
                    //套件按单品查询
                    DmjItem dmjItem = itemContext.retain(Pair.of(order.getItemSysId(), order.getSkuSysId()));
                    if (dmjItem != null) {
                        List<SuiteSingle> suiteSingles;
                        if (order.getSkuSysId() != null && order.getSkuSysId() > 0) {
                            DmjSku sku = null;
                            if (dmjItem instanceof DmjSku) {
                                DmjSku dmjSku = (DmjSku) dmjItem;
                                if (order.getSkuSysId() - dmjSku.getSysSkuId() == 0) {
                                    sku = dmjSku;
                                }
                            } else {
                                sku = getSku(order.getSkuSysId(), dmjItem.getSkus());
                            }
                            if (sku == null) {
                                //商品不存在或已删除
                                return;
                            }
                            suiteSingles = sku.getSuiteSingleList();
                        } else {
                            suiteSingles = dmjItem.getSuiteSingleList();
                        }
                        if (CollectionUtils.isNotEmpty(suiteSingles)) {
                            for (SuiteSingle suiteSingle : suiteSingles) {
                                TradeOrderGroupVo tradeOrderGroupVo = TradeOrderGroupVo.builder()
                                        .sysItemId(suiteSingle.getSubItemId())
                                        .sysSkuId(suiteSingle.getSubSkuId())
                                        .isSuitSingle(true)
                                        .skuPropertiesName(suiteSingle.getPropertiesName())
                                        .title(suiteSingle.getTitle())
                                        .skuOuterId(StringUtils.isNotEmpty(suiteSingle.getSkuOuterId())?suiteSingle.getSkuOuterId():suiteSingle.getOuterId())
                                        .isSuit(false)
                                        .num(order.getNum() * suiteSingle.getRatio())//套件数量*单品比例
                                        .picPath(getSuiteSinglePicPath(suiteSingle, order.getSuits(), sysPicPath))
                                        .build();
                                append(tradeOrderGroupVo, result);
                            }
                        }
                    }
                } else {
                    //套件及单品汇总
                    TradeOrderGroupVo tradeOrderGroupVo = TradeOrderGroupVo.builder()
                            .sysItemId(order.getItemSysId())
                            .sysSkuId(order.getSkuSysId())
                            .skuPropertiesName(order.getSysSkuPropertiesName())
                            .title(order.getSysTitle())
                            .skuOuterId(order.getSysOuterId())
                            .isSuit(order.isSuit())
                            .num(order.getNum())
                            .picPath(getOrderPicPath(order, sysPicPath))
                            .build();
                    append(tradeOrderGroupVo, result);
                }
            }
        }));
        return result;
    }

    /**
     * 图片是否获取系统商品的图片
     * @param order 商品信息
     * @param tc 交易配置
     * @param subSource 订单subSource属性
     * @return
     */
    private boolean getSysPicPath(Order order, TradeConfig tc, String subSource){
        //不匹配，展示平台图片
        if (order.getItemSysId() == null || order.getItemSysId() <=0){
            return false;
        }
        //系统单、商品更改过、交易配置，展示系统图片
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource()) || order.getSysItemChanged() - 1 == 0 || (tc.getOrderImageSource() == null || tc.getOrderImageSource() != 1)) {
            return true;
        }
        String platform = (String) tc.get(TradeExtendConfigsEnum.PICTURE_FROM_PLATFORM.getKey());
        //配置信息，展示系统图片
        if (null== platform){
            return true;
        }
        List<String> platformList = Strings.getAsStringList(platform,",",true);
        if (platformList.contains("all") || platformList.contains(order.getSource()) || (StringUtils.isNotBlank(subSource) && platformList.contains(subSource))) {
            //平台图片不存在，展示系统图片
            if (StringUtils.isBlank(order.getPicPath())){
                return true;
            }
            return false;
        } else {
            return true;
        }
    }

    /**
     * 套件单品，获取图片信息
     * @param suiteSingle 单品 商品信息
     * @param suits 单品 商品订单信息
     * @param sysPicPath 是否获取系统商品图片 true：获取系统商品图片
     * @return
     */
    private String getSuiteSinglePicPath(SuiteSingle suiteSingle, List<Order> suits, boolean sysPicPath){
        String path;
        if (CollectionUtils.isEmpty(suits)){
            path = StringUtils.defaultIfBlank(suiteSingle.getSkuPicPath(),suiteSingle.getPicPath());
        }else {
            Order single = null;
            for (Order suit : suits){
                if (Objects.equals(suiteSingle.getSysItemId(), suit.getItemSysId()) && Objects.equals(suiteSingle.getSysSkuId(), suit.getSkuSysId())){
                    single = suit;
                    break;
                }
            }
            //没有匹配到单品订单信息，使用单品商品信息，没有平台图片了
            if (null == single){
                path = StringUtils.defaultIfBlank(suiteSingle.getSkuPicPath(),suiteSingle.getPicPath());

            }else {
                if (sysPicPath){
                    path = single.getSysPicPath();
                }else {
                    //如果平台图片不存在，展示系统图片
                    path = StringUtils.isNotBlank(single.getPicPath()) ? single.getPicPath() : single.getSysPicPath();
                }
            }
        }
        //默认值进行兜底
        if (StringUtils.isBlank(path)){
            path = StockConstants.PATH_NO_PIC;
        }
        return path;
    }

    /**
     * 订单商品获取图片信息
     * @param order 商品订单信息
     * @param sysPicPath 是否获取系统商品图片 true：获取系统商品图片
     * @return
     */
    private String getOrderPicPath(Order order, boolean sysPicPath){
        String path;
        if (sysPicPath){
           path = order.getSysPicPath();
        }else {
            path = StringUtils.isNotBlank(order.getPicPath()) ? order.getPicPath() : order.getSysPicPath();
        }
        //默认值进行兜底
        if (StringUtils.isBlank(path)){
            path = StockConstants.PATH_NO_PIC;
        }
        return path;
    }

    private DmjSku getSku(Long sysSkuId, List<DmjSku> skuList) {
        for (DmjSku sku : skuList) {
            if (sku.getSysSkuId() - sysSkuId == 0) {
                return sku;
            }
        }
        return null;
    }

    /**
     * 处理重复及新增处理
     *
     * @param tradeOrderGroupVo
     * @param result
     */
    private void append(TradeOrderGroupVo tradeOrderGroupVo, Map<String, TradeOrderGroupVo> result) {
        if (result == null || tradeOrderGroupVo == null) {
            return;
        }
        String key = tradeOrderGroupVo.getKey();
        if (result.containsKey(key)) {
            TradeOrderGroupVo exists = result.get(key);
            tradeOrderGroupVo.setNum(tradeOrderGroupVo.getNum() + exists.getNum());
        }
        result.put(key, tradeOrderGroupVo);
    }

    /**
     * 填充商品库存信息
     *
     * @param staff          staff
     * @param result         分组展示相关
     * @param querySuitStock 查询套件库存
     * @param progressData   进度条相关
     */
    public void fullStockInfo(Staff staff, List<TradeOrderGroupVo> result, List<Long> warehouseIds, boolean querySuitStock, ProgressData progressData) {
        if (CollectionUtils.isEmpty(warehouseIds)) {
            return;
        }
        List<Warehouse> warehouses = warehouseService.queryAllIncludePrivilege(staff, 1);
        if (CollectionUtils.isEmpty(warehouses)) {
            throw new IllegalArgumentException("您没有任何自有仓库的操作权限");
        }
        //所有仓库缓存
        Map<Long, Warehouse> allWarehouse = warehouses.stream().collect(Collectors.toMap(Warehouse::getId, Function.identity(), (k1, k2) -> k1));

        //库存查询条件
        StockQueryParams stockQueryParams = new StockQueryParams();
        //按仓库查询
        stockQueryParams.setWarehouseIds(warehouseIds);

        //查询商品库存
        for (List<TradeOrderGroupVo> tradeOrderGroupVos : Lists.partition(result, ITEM_MAX_QUERY_STOCK_SIZE)) {
            List<ItemKey> itemKeys = tradeOrderGroupVos.stream().map(vo -> {
                ItemKey itemKey = new ItemKey();
                itemKey.setSuite(vo.isSuit());
                itemKey.setSysItemId(vo.getSysItemId());
                itemKey.setSysSkuId(vo.getSysSkuId());
                return itemKey;
            }).collect(Collectors.toList());
            List<FarERPStock> farERPStocks = queryItemStocks(staff, itemKeys, stockQueryParams, querySuitStock);
            if (CollectionUtils.isEmpty(farERPStocks)) {
                logger.debug(LogHelper.buildLog(staff, String.format("商品未匹配到库存信息:[%s]", String.join(",", itemKeys.stream().map(k -> String.format("sysItemId:%s,sysSkuId:%s", k.getSysItemId(), k.getSysSkuId())).toArray(String[]::new)))));
            } else {
                Map<String, List<FarERPStock>> erpStockMap = farERPStocks.stream().collect(Collectors.groupingBy(this::buildFarERPStockKey));
                for (TradeOrderGroupVo orderGroupVo : tradeOrderGroupVos) {
                    if (orderGroupVo == null) {
                        progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_ORDER_GROUP, progressData.setCountCurrent(progressData.getCountCurrent() + 1));
                        continue;
                    }
                    if (orderGroupVo.getSysItemId() == null) {
                        progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_ORDER_GROUP, progressData.setCountCurrent(progressData.getCountCurrent() + 1));
                        continue;
                    }
                    List<FarERPStock> stocks = erpStockMap.get(orderGroupVo.getKey());
                    if (stocks != null) {
                        Map<Long, List<FarERPStock>> warehouseGroup = stocks.stream().collect(Collectors.groupingBy(FarERPStock::getWareHouseId));
                        for (Map.Entry<Long, List<FarERPStock>> entry : warehouseGroup.entrySet()) {
                            Pair<Long, List<FarERPStock>> pair = Pair.of(entry.getKey(), entry.getValue());
                            Warehouse warehouse = allWarehouse.get(pair.getLeft());
                            if (warehouse != null) {
                                TradeOrderGroupVo.WarehouseStockVo warehouseStockVo = TradeOrderGroupVo.WarehouseStockVo.builder()
                                        .warehouseId(warehouse.getId())
                                        .warehouseName(warehouse.getName())
                                        .isDefaultWarehouse(warehouse.getIsDefault().equals(1) ? 1 : 0)
                                        .lockNum(pair.getRight().stream().mapToLong(FarERPStock::getLockStock).sum())
                                        .stockNum(pair.getRight().stream().mapToLong(FarERPStock::getAvailableInStock).sum())
                                        .build();
                                if (orderGroupVo.getStocks() == null) {
                                    orderGroupVo.setStocks(new ArrayList<>());
                                }
                                orderGroupVo.getStocks().add(warehouseStockVo);
                            }
                        }
                    }
                    if (orderGroupVo.getStocks() != null) {
                        orderGroupVo.setLockNum(orderGroupVo.getStocks().stream().mapToLong(TradeOrderGroupVo.WarehouseStockVo::getLockNum).sum());
                        orderGroupVo.setStockNum(orderGroupVo.getStocks().stream().mapToLong(TradeOrderGroupVo.WarehouseStockVo::getStockNum).sum());
                    }
                }
            }
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_ORDER_GROUP, progressData.setCountCurrent(progressData.getCountCurrent() + 1));
        }
    }


    /**
     * @param staff          staff
     * @param itemKeys       商品列表
     * @param params         参数
     * @param querySuitStock 按套件查询套件库存
     * @return 库存相关信息
     */
    public List<FarERPStock> queryItemStocks(Staff staff, List<ItemKey> itemKeys, StockQueryParams params, boolean querySuitStock) {
        List<FarERPStock> farERPStocks = Lists.newArrayList();
        List<Pair<Long, Long>> normalSysItemKeys = new ArrayList<>();
        List<Pair<Long, Long>> suiteSysItemKeys = new ArrayList<>();
        for (ItemKey key : itemKeys) {
            long sysSkuId = key.getSysSkuId() != null && key.getSysSkuId() > 0 ? key.getSysSkuId() : 0L;
            if (key.isSuite() && querySuitStock) {
                suiteSysItemKeys.add(Pair.of(key.getSysItemId(), sysSkuId));
            } else {
                normalSysItemKeys.add(Pair.of(key.getSysItemId(), sysSkuId));
            }
        }
        if (!normalSysItemKeys.isEmpty()) {
            params.setQuerySuitStock(false);
            for (List<Pair<Long, Long>> subList : Lists.partition(normalSysItemKeys, WmsConstants.BATCH_QUERY_MAX_SIZE)) {
                params.setSysItemIds(subList.stream().map(Pair::getLeft).collect(Collectors.toList()));
                params.setSysSkuIds(subList.stream().map(Pair::getRight).collect(Collectors.toList()));
                farERPStocks.addAll(stockServiceDubbo.queryStocks(staff, params));
            }
        }
        if (!suiteSysItemKeys.isEmpty()) {
            params.setQuerySuitStock(true);
            for (List<Pair<Long, Long>> subList : Lists.partition(suiteSysItemKeys, WmsConstants.BATCH_QUERY_MAX_SIZE)) {
                params.setSysItemIds(subList.stream().map(Pair::getLeft).collect(Collectors.toList()));
                params.setSysSkuIds(subList.stream().map(Pair::getRight).collect(Collectors.toList()));
                farERPStocks.addAll(stockServiceDubbo.queryStocks(staff, params));
            }
        }
        return farERPStocks;
    }

    /**
     * @param stock
     * @return
     * @see com.raycloud.dmj.domain.trades.vo.TradeOrderGroupVo#getKey()
     */
    private String buildFarERPStockKey(FarERPStock stock) {
        if (stock == null || stock.getSysItemId() <= 0) {
            return "";
        }
        String key = String.valueOf(stock.getSysItemId());
        //stock.sysSkuId=0,跟order.skuSysId不兼容
        if (stock.getSysSkuId() != null && stock.getSysSkuId() > 0) {
            key = key + "," + stock.getSysSkuId();
        } else {
            key = key + ",-1";
        }
        return key;
    }


    public static class ItemContext {
        final Map<String, DmjItem> itemMap = new HashedMap();
        final AtomicBoolean initialize = new AtomicBoolean(false);
        final IItemServiceDubbo itemServiceDubbo;
        final Staff staff;

        public ItemContext(Staff staff, IItemServiceDubbo itemServiceDubbo) {
            this.itemServiceDubbo = itemServiceDubbo;
            this.staff = staff;
        }

        public void put(Pair<Long, Long> pair, DmjItem item) {
            itemMap.put(key(pair), item);
        }

        public DmjItem retain(Pair<Long, Long> pair) {
            if (!init()) {
                return null;
            }
            return itemMap.get(key(pair));
        }

        public void remove(Pair<Long, Long> pair) {
            if (!init()) {
                return;
            }
            itemMap.remove(key(pair));
        }

        String key(Pair<Long, Long> pair) {
            if (pair == null || pair.getLeft() <= 0) {
                return "-1";
            }
            String key = String.valueOf(pair.getLeft());
            if (pair.getRight() != null && pair.getRight() > 0) {
                key = key + "," + pair.getRight();
            } else {
                key = key + ",-1";
            }
            return key;
        }

        public void setItems(Staff staff, List<Long> sysItemId,List<Long> sysSkuId) {
            if (CollectionUtils.isEmpty(sysItemId)&&CollectionUtils.isEmpty(sysSkuId)) {
                return;
            }
            if (!init()) {
                initialize.compareAndSet(false, true);
            }
            QueryItemDetailParams params = new QueryItemDetailParams();
            params.setNeedSuitSingleOrNot(true);
            params.setSysItemIdList(sysItemId);
            params.setSysSkuIdList(sysSkuId);
            List<DmjItem> dmjItems = itemServiceDubbo.queryItemDetail(staff, params);
            for (DmjItem item : dmjItems) {
                if (item instanceof DmjSku) {
                    DmjSku sku = (DmjSku) item;
                    itemMap.put(key(Pair.of(item.getSysItemId(), sku.getSysSkuId())), item);
                } else if(CollectionUtils.isNotEmpty(item.getSkus())) {
                    for (DmjSku sku : item.getSkus()){
                        itemMap.put(key(Pair.of(item.getSysItemId(), sku.getSysSkuId())), sku);
                    }
                } else {
                    itemMap.put(key(Pair.of(item.getSysItemId(), -1L)), item);
                }
            }
        }

        boolean init() {
            return initialize.get();
        }
    }
}

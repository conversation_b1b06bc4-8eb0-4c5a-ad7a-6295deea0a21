package com.raycloud.dmj.services.trade.item.entity;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.modify.ModifyParentBusiness;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.domain.OrderConstant;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.item.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.trade.item.EntityCodeConvertContext;
import com.raycloud.dmj.services.trade.item.IEntityCodeConvertService;
import com.raycloud.dmj.services.trades.IProgressService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeUpdateService;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.business.nonconsign.TradeNonConsignBusiness.addRemoveNonConsignTradeTrace;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022/6/15 11:35
 * @Description 按实体编码转换商品实现
 */
@Service
public class EntityCodeConvertService implements IEntityCodeConvertService {

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    IProgressService progressService;

    @Resource
    ILockService lockService;

    @Resource
    EntityCodeConvertBusiness entityCodeConvertBusiness;

    @Resource
    IOrderStockService orderStockService;

    @Resource
    ModifyParentBusiness modifyParentBusiness;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    TradeTraceBusiness tradeTraceBusiness;

    @Override
    public void convert(Staff staff, EntityCodeConvertContext convertContext) {
        List<Trade> trades = entityCodeConvertBusiness.queryTrades(staff, convertContext.queryParams);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        int countAll = trades.size() + 1;

        updateProgress(staff, convertContext.progressData, countAll, 0, 0, 0);
        convertContext.tradeConfig = tradeConfigService.get(staff);
        Map<Long, List<Trade>> warehouseTradesMap = new HashMap<>();
        trades.forEach(trade -> warehouseTradesMap.computeIfAbsent(trade.getWarehouseId(), k -> new ArrayList<>()).add(trade));
        for (Map.Entry<Long, List<Trade>> entry : warehouseTradesMap.entrySet()) {
            List<List<Trade>> groupTrades = ListUtils.splitList(entry.getValue(), 100);
            for (List<Trade> subTrades : groupTrades) {
                int incCountCurrent = subTrades.size(), incSucNum = 0;
                try {
                    incSucNum = convert(staff, convertContext, subTrades);
                } catch (Exception e) {
                    String errorMsg = String.format("按实体编码转换出错[sids=%s]", TradeUtils.toSidList(subTrades));
                    Logs.error(LogHelper.buildLog(staff, errorMsg), e);
                    if (null!= convertContext.progressData){
                        convertContext.progressData.getErrorMsg().add(errorMsg);
                    }
                } finally {
                    convertContext.clearCur();
                }
                updateProgress(staff, convertContext.progressData, countAll, incCountCurrent, incSucNum, incCountCurrent - incSucNum);
            }
        }
    }


    private int convert(Staff staff, EntityCodeConvertContext convertContext, List<Trade> originTrades) {
        return lockService.locks(TradeLockBusiness.tids2ERPLocks(staff, TradeUtils.toTidList(originTrades)), () -> {
            //查询订单
            List<Trade> trades = filter(staff, tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, true, TradeUtils.toSids(originTrades)));
            entityCodeConvertBusiness.fillEntityCodeData(staff, convertContext, trades);
            List<Trade> filteredTrades = new ArrayList<>();
            Map<Long, List<Trade>> groupTrades = gourpTradesMergeTradesInSameGroupMap(trades);
            for (Map.Entry<Long,List<Trade>> entry:groupTrades.entrySet()){
                List<Trade> list = entry.getValue();
                int count = 0;
                for (Trade trade : list) {
                    List<Order> orders = TradeUtils.getOrders4Trade(trade);
                    for (Order order : orders) {
                        if (EntityCodeConvertUtils.needSwitch(staff, convertContext.convertParams, order)) {
                            DmjItem dmjItem = convertContext.getSwitchItem(order.getItemSysId(), order.getSkuSysId());
                            if(dmjItem != null){
                                count+=1;
                                TradeConfigNew tradeConfigNew =  TradeConfigGetUtil.get(staff, TradeConfigEnum.ENTITY_CODE_CONVERT_CHECK_STOCK);// KMERP-193875
                                if (tradeConfigNew == null || "1".equals(tradeConfigNew.getConfigValue())) {
                                    if (!EntityCodeConvertUtils.checkStock(staff, convertContext, trade.getWarehouseId(), dmjItem, order.getNum(),order)) {//库存校验不通过，直接跳过
                                        String errorMsg = String.format("按实体编码转换,校验可用库存不通过,sid=%s",trade.getSid());
                                        Logs.error(LogHelper.buildLog(staff, errorMsg), null);
                                        if (null!= convertContext.progressData ){
                                            convertContext.progressData.getErrorMsg().add(errorMsg);
                                        }
                                        continue;
                                    }
                                } else {
                                    Logs.debug(LogHelper.buildLog(staff, String.format("转换时忽略库存转换（选择后订单首次下载默认转换成实体编码）sid=%s", trade.getSid())));
                                }
                                count-=1;
                                entityCodeConvertBusiness.item2Order(staff, convertContext, trade, order, dmjItem);
                            }
                        }
                    }
                }
                TradeConfigNew tradeConfigNew =  TradeConfigGetUtil.get(staff, TradeConfigEnum.ENTITY_CODE_CONVERT_DIMENSION);
                if (tradeConfigNew!=null && "1".equals(tradeConfigNew.getConfigValue())){
                    if (count>0){
                        String errorMsg = String.format("按实体编码转换,订单维度-校验可用库存不通过,sid或者mergeSid=%s",entry.getKey());
                        Logs.error(LogHelper.buildLog(staff, errorMsg), null);
                        if (null!= convertContext.progressData ) {
                            convertContext.progressData.getErrorMsg().add(errorMsg);
                        }
                        if (null!=convertContext.convertParams.getSource() && convertContext.convertParams.getSource()==1){
                            convertContext.addFailReason(entry.getKey(),errorMsg);
                        }
                    }else {
                        filteredTrades.addAll(list);
                    }
                }
                if (tradeConfigNew==null ||"0".equals(tradeConfigNew.getConfigValue()) ){
                    filteredTrades.addAll(list);
                }
            }
            return convertPersist(staff, convertContext, filteredTrades);
        });
    }



    private Map<Long, List<Trade>> gourpTradesMergeTradesInSameGroupMap(List<Trade> trades) {
        Map<Long,List<Trade>> groupTrades = new HashMap<>();

        for (Trade trade: trades){
            Long key = getMergeSidOrSidKey(trade);
            List<Trade> temp = groupTrades.get(key);
            if (null ==temp){
                temp = new ArrayList<>();
                groupTrades.putIfAbsent(key,temp);
            }
            temp.add(trade);
        }
        return groupTrades;
    }

    private Long getMergeSidOrSidKey(Trade trade) {
        Long key = -1L;
        if (TradeUtils.isMerge(trade)){
            key = trade.getMergeSid();
        }else{
            key = trade.getSid();
        }
        return key;
    }

    private int convertPersist(Staff staff, EntityCodeConvertContext convertContext, List<Trade> trades) {
        if (convertContext.curTradeOldNewOrderMap.size() == 0) {
            String errorMsg = "按编码实体转换,没有可以转换的 实体编码<==> 套件 对应关系";
            Logs.error(LogHelper.buildLog(staff, errorMsg), null);
            if (null!=convertContext.progressData){
                convertContext.progressData.getErrorMsg().add(errorMsg);
            }
            return 0;
        }
        List<EntityCodeConvertLog> logs = new ArrayList<>();
        List<Trade> tradeTraces = new ArrayList<>();
        ModifyData modifyData = new ModifyData();
        for (Trade trade : trades) {
            Map<Order, Order> oldNewOrderMap = convertContext.curTradeOldNewOrderMap.get(trade);
            if (oldNewOrderMap==null || oldNewOrderMap.isEmpty()){
                String errorMsg = String.format("按编码实体转换,未找到转换的目标商品 [sids=%s]",trade.getSid());
                Logs.error(LogHelper.buildLog(staff, errorMsg), null);
                if (null!=convertContext.progressData){
                    convertContext.progressData.getErrorMsg().add(errorMsg);
                }
                if (null!=convertContext.convertParams.getSource() && convertContext.convertParams.getSource()==1){
                    convertContext.addFailReason(trade.getSid(),errorMsg);
                }
                continue;
            }
            List<Order> orders = TradeUtils.getOrders4Trade(trade);

            for (Map.Entry<Order, Order> entry : oldNewOrderMap.entrySet()) {
                Order oldOrder = entry.getKey();
                oldOrder.setEnableStatus(0);
                Order newOrder = entry.getValue();
                TradeItemUtils.copyOrderExt(newOrder, oldOrder.getOrderExt());
                orders.add(newOrder);
                orders.remove(oldOrder);
                if (TradeUtils.isGxOrMixTrade(trade) || TradeUtils.isQimenAndNotFx(trade)){
                    //供销订单，对商品标记修改动作
                    newOrder.addV(OrderConstant.V_GX_ITEM_EDIT);
                    newOrder.addV(OrderConstant.V_GX_ITEM_EDIT_NOT_REL_FX);
                }
                modifyData.resumeStocks.add(oldOrder);
                modifyData.applyOrders.add(newOrder);

                modifyData.deletes.add(oldOrder);
                if ((oldOrder.isSuit() || oldOrder.isGroup() || oldOrder.isProcess()) && CollectionUtils.isNotEmpty(oldOrder.getSuits())){
                    List<Order> suits = oldOrder.getSuits();
                    for (Order order:suits){
                        order.setEnableStatus(0);
                        modifyData.deletes.add(order);
                    }
                }
                modifyData.inserts.add(newOrder);
                modifyData.updateTrades.add(trade);
                logs.add(EntityCodeConvertUtils.getLog(staff, oldOrder, newOrder));
            }
            tradeTraces.add(getTradeTrace(staff, trade, oldNewOrderMap));
        }
        //处理库存
        orderStockService.modifyOrders(staff, modifyData.applyOrders, modifyData.resumeStocks);
        for (Trade trade : modifyData.updateTrades) {
            //计算库存
            modifyParentBusiness.calculate(staff, trade, convertContext.tradeConfig);
            // 商品种类数，商品数 无需更新。
            trade.setItemNum(null);
            trade.setItemKindNum(null);
            trade.setSingleItemKindNum(null);
        }
        tradeUpdateService.updateTrades(staff, modifyData.updateTrades, modifyData.deletes, null, modifyData.inserts);
        //记录日志 save logs
        Logs.ifDebug(LogHelper.buildLog(staff, JSON.toJSONString(logs)));
        tradeTraceBusiness.asyncTrace(staff, tradeTraces, OpEnum.ENTITY_CODE_CONVERT);
        if (convertContext.convertParams.getSource().equals(1)){
            for (Trade trade: modifyData.updateTrades){
                convertContext.addSuccessList(trade.getSid());
            }
        }
        return modifyData.updateTrades.size();
    }

    public Trade getTradeTrace(Staff staff, Trade trade, Map<Order, Order> oldNewOrderMap) {
        Trade trace = new TbTrade();
        trace.setSid(trade.getSid());
        StringBuilder content = new StringBuilder("按实体编码转换商品");
        for (Map.Entry<Order, Order> entry : oldNewOrderMap.entrySet()) {
            content.append(entry.getKey().getSysOuterId()).append("<-转成->").append(entry.getValue().getSysOuterId()).append("；");
        }
        trace.getOperations().put(OpEnum.ENTITY_CODE_CONVERT, content.toString());
        addRemoveNonConsignTradeTrace(trace, new ArrayList<>(oldNewOrderMap.values()));
        return trace;
    }



    private List<Trade> filter(Staff staff, List<Trade> trades) {
        return trades.stream().filter(t -> !CommonConstants.PLAT_FORM_TYPE_FXG_DF.equals(t.getSource())).collect(Collectors.toList());
    }

    private void updateProgress(Staff staff, ProgressData progressData, int countAll, int incCountCurrent, int incSucNum, int incErrorNum) {
        if(null == progressData){
            return;
        }
        if (progressData.getCountAll()==null){
            progressData.setCountAll(0);
        }
        if (progressData.getCountCurrent()==null){
            progressData.setCountCurrent(0);
        }
        if (progressData.getSucNum()==null){
            progressData.setSucNum(0L);
        }
        if (progressData.getErrorNum()==null){
            progressData.setErrorNum(0L);
        }
        progressData.setCountAll(countAll)
                .setCountCurrent(progressData.getCountCurrent() + incCountCurrent)
                .setSucNum(progressData.getSucNum() + incSucNum)
                .setErrorNum(progressData.getErrorNum() + incErrorNum);
        progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_SUITS_CONVERT, progressData);
    }

    /**
     * 套件订单转实体编码订单
     * 开启新事务，防止跟库存平移库存锁冲突导致死锁
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void convertSuit2EntityCode4Wms(Staff staff, Long[] sids, Integer suitsConvertType) {
        EntityCodeConvertContext convertContext = new EntityCodeConvertContext();
        TradeControllerParams tradeControllerParams = new TradeControllerParams();
        tradeControllerParams.setSids(Arrays.stream(sids).map(String::valueOf).collect(Collectors.joining(",")));
        EntityCodeConvertParams convertParams = new EntityCodeConvertParams();
        convertParams.setSuitsConvertType(suitsConvertType); // 套件转实体编码
        convertParams.setAllowableStockOut(CommonConstants.JUDGE_YES); // 忽略库存
        convertParams.setIgnoreProcess(CommonConstants.JUDGE_YES); // 不需要进度条
        convertParams.setSource(CommonConstants.JUDGE_YES); // 支持已审核订单
        convertContext.queryParams = tradeControllerParams;
        convertContext.convertParams = convertParams;
        Logs.ifDebug(String.format("调用套件订单实体编码订单互转接口, sid:%s, suitsConvertType:%s", tradeControllerParams.getSids(), convertParams.getSuitsConvertType()));
        convert(staff, convertContext);
        Assert.isTrue(org.apache.commons.collections.MapUtils.isEmpty(convertContext.failSidAndReasonMap), "套件订单转实体编码失败" + convertContext.failSidAndReasonMap.keySet());
    }
}

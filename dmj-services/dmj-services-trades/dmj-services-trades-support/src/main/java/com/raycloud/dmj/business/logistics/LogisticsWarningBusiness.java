package com.raycloud.dmj.business.logistics;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.fx.FxLogisticsWarningBusiness;
import com.raycloud.dmj.business.pt.PrintTemplateHelper;
import com.raycloud.dmj.business.trade.UnionTradeDecryptBusiness;
import com.raycloud.dmj.context.SenderAddressContext;
import com.raycloud.dmj.domain.account.Conf;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.TradeDecryptSourceEnum;
import com.raycloud.dmj.domain.pt.*;
import com.raycloud.dmj.domain.pt.bo.MultiPacksOutSidDetail;
import com.raycloud.dmj.domain.pt.enums.*;
import com.raycloud.dmj.domain.pt.model.waybill.bean.WlbResult;
import com.raycloud.dmj.domain.pt.printvo.ExpressOutSidRegularVO;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeTraceUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.log.JsonFilterUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.user.UserConf;
import com.raycloud.dmj.logistics.common.enums.CpCodeEnum;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.pt.*;
import com.raycloud.dmj.services.pt.helper.AddressHelper;
import com.raycloud.dmj.services.pt.helper.WarehouseHelper;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import com.raycloud.dmj.services.user.IAddressService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.dmj.utils.LogisticsTraceHelper;
import com.raycloud.dmj.utils.TradeLogisticsTrackingUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 物流预警
 */
@Service
public class LogisticsWarningBusiness {
    private final Logger logger = Logger.getLogger(this.getClass());

    public static final List<Integer> NOT_SUBSCRIBE_SOURCES = Arrays.asList(
            WlbTemplateTypeEnum.PDD.getValue(),
            WlbTemplateTypeEnum.DIVIDEPDD.getValue(),
            WlbTemplateTypeEnum.DIVIDEFXG.getValue(),
            WlbTemplateTypeEnum.FXG.getValue(),
            WlbTemplateTypeEnum.FXG_XSD.getValue());


    @Resource
    IEventCenter eventCenter;
    @Resource
    private ICompanyService companyService;
    @Resource
    private IShopService shopService;
    @Resource
    private IExpressCompanyService expressCompanyService;
    @Resource
    private IUserWlbExpressTemplateService userWlbExpressTemplateService;
    @Resource
    private IUserExpressTemplateService userExpressTemplateService;
    @Resource
    private TradeLocalConfigurable tradeLocalConfigurable;
    @Resource
    private IWarehouseService warehouseService;
    @Resource
    private IAddressService addressService;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    LogisticsTrackingService logisticsTrackingService;
    @Resource
    IExpressOutSidRegularService expressOutSidRegularService;
    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    FxLogisticsWarningBusiness fxLogisticsWarningBusiness;

    @Resource
    UnionTradeDecryptBusiness unionTradeDecryptBusiness;
    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;
    @Resource
    IOutSidPoolService outSidPoolService;


    /**
     * 订阅物流预警事件
     *
     * @param staff
     * @param template
     * @param successResult
     * @param trades
     */
    public void subscribeLogisticsEvent(Staff staff, IExpressTemplateBase template, List<WlbResult> successResult, List<Trade> trades) {
        if (CollectionUtils.isEmpty(successResult)) {
            return;
        }
        successResult = successResult.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(successResult)) {
            return;
        }

//        if (!openLogisticsTracking(staff)) return;

        // 过滤没有运单号的订单
        trades = filterTradeByWlbResult(staff, successResult, trades);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        // 过滤跨境订单
        trades = filterTrade(staff, trades);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        // 简化订单
        List<Trade> tradeList = new ArrayList<>();
        for (Trade trade : trades) {
            tradeList.add(buildTrade(trade, template));
        }
        // 订阅快递助手并生成物流预警任务
        eventCenter.fireEvent(this, new EventInfo("trade.logistics.warning.subscribe").setArgs(new Object[]{staff, template, tradeList}), null);
    }

    private boolean openLogisticsTracking(Staff staff) {
        // 判断是否开启物流预警配置
        Conf conf = getCompanyProfileConf(staff, companyService);
        // 判断是否开启物流跟踪
        boolean isLogisticsTracking = TradeLogisticsTrackingUtils.isOpenLogisticsTracking(staff, tradeConfigService);
        // 两个都没开，直接返回
        if (conf.getOpenLogisticsWarning() || isLogisticsTracking) {
            return true;
        }
        return false;
    }


    /**
     * 分销订单发货订阅物流预警事件
     *
     * @param staff
     * @param template
     * @param trades
     */
    public void fxSyncConsignStatusEvent(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        //分销开启物流预警白名单
//        if (!fxLogisticsWarningBusiness.supportLogisticsWarning(staff)) {
//            return;
//        }

        //如果是自发单 没有订阅物流预警的 但是又在白名单里的 这里补发一下
        List<Trade> noFxs =  trades.stream().filter(x->!(TradeUtils.isGxTrade(x) || TradeUtils.isFxOrMixTrade(x))).collect(Collectors.toList());

        // 过滤跨境订单
        if (CollectionUtils.isNotEmpty(noFxs)) {
            doSyncConsignStatusEvent(staff,noFxs);
        }

        trades =  trades.stream().filter(x->TradeUtils.isFxTrade(x)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        // 过滤没有运单号的订单
        trades = trades.stream().filter(x -> StringUtils.isNotBlank(x.getOutSid())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        // 过滤跨境订单
        trades = filterTrade(staff, trades);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        // 简化订单
        List<Trade> tradeList = new ArrayList<>();


        List<Trade> needDecypt = new ArrayList<>();
        Map<String, List<Trade>> needReFill = new HashMap<>();

        DevLogBuilder builder = new DevLogBuilder(staff).appendHead("分销业务").append("分销订单订阅物流预警:").startArray();
        for (Trade trade : trades) {
            Trade buildTrade = buildTrade(trade, null);
            buildTrade.setTemplateId(null);
            buildTrade.setTemplateType(null);
            //这里拿到的实际是供销的快递公司 在FxUploadBusiness.fillGxExpress() 里进行了填充
            buildTrade.setExpressCode(trade.getExpressCode());
            buildTrade.setExpressName(trade.getExpressName());

            buildTrade.setSysStatus(Trade.SYS_STATUS_SELLER_SEND_GOODS);
            //SF订阅需提供收件人手机尾号
            if (Objects.equals("SF", buildTrade.getExpressCode())) {
                if (StringUtils.isNotBlank(trade.getMobileTail())) {
                    buildTrade.setMobileTail(trade.getMobileTail());
                } else {
                    buildTrade.setReceiverMobile(trade.getReceiverMobile());
                    buildTrade.setReceiverPhone(trade.getReceiverPhone());
                    needDecypt.add(trade);
                    needReFill.computeIfAbsent(trade.getTid(), x -> new ArrayList<>()).add(buildTrade);
                }
            }

            if (CollectionUtils.isNotEmpty(needDecypt)) {
                List<TradeReceiverInfo> tradeReceiverInfos = unionTradeDecryptBusiness.decryptTrades(staff, trades, null, TradeDecryptSourceEnum.LOGISTICS_WARNING_DECRYPT, true, null);
                if (CollectionUtils.isNotEmpty(tradeReceiverInfos)) {
                    for (TradeReceiverInfo tradeReceiverInfo : tradeReceiverInfos) {
                        List<Trade> list = needReFill.get(tradeReceiverInfo.getTid());
                        if (CollectionUtils.isNotEmpty(list)) {
                            for (Trade t : list) {
                                t.setReceiverMobile(tradeReceiverInfo.getReceiverMobile());
                                t.setReceiverPhone(tradeReceiverInfo.getReceiverPhone());
                            }
                        }
                    }
                }
            }
            builder.append(JSON.toJSONString(buildTrade)).append(",");
            tradeList.add(buildTrade);
        }
        builder.endArray().printDebug(logger);
        // 订阅快递助手并生成物流预警任务
        eventCenter.fireEvent(this, new EventInfo("trade.logistics.warning.sync.consign").setArgs(new Object[]{staff, tradeList}), null);
    }

    /**
     * 同步发货状态事件
     *
     * @param staff
     * @param trades
     */
    public void syncConsignStatusEvent(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        doSyncConsignStatusEvent(staff, trades);
    }

    private void doSyncConsignStatusEvent(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        // 过滤订单
        trades = filterTrade(staff, trades);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        // 简化订单
        List<Trade> tradeList = new ArrayList<>();
        for (Trade trade : trades) {
            tradeList.add(buildTrade(trade, null));
        }
        eventCenter.fireEvent(this, new EventInfo("trade.logistics.warning.sync.consign").setArgs(new Object[]{staff, tradeList}), null);
    }

    /**
     * 同步订单关闭状态
     *
     * @param staff
     * @param trades
     */
    public void syncTradeClosedStatus(Staff staff, List<Trade> trades) {
        if (!checkAndFilter(staff, trades)) {
            return;
        }

        List<Map<String, Object>> list = new ArrayList<>();
        for (Trade trade : trades) {
            if (!Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("clueId", staff.getClueId());
            map.put("companyId", staff.getCompanyId());
            map.put("sid", trade.getSid());
            map.put("sysStatus", trade.getSysStatus());
            map.put("sysConsigned", trade.getSysConsigned());
            list.add(map);
        }

        if (CollectionUtils.isNotEmpty(list)) {
            eventCenter.fireEvent(this, new EventInfo("sync.logistics.warning.closed.status").setArgs(new Object[]{ list}), null);
            logger.debug(LogHelper.buildLog(staff, "物流预警同步订单关闭状态事件发送成功。" + JSONObject.toJSONString(list)));
        }
    }

    /**
     * 同步发货状态
     */
    public void syncConsignStatus(Staff staff, List<Trade> trades) {
        // 快递公司Map
        Map<String, IExpressTemplateBase> expressIdMap = new HashMap<>();
        try {
            expressIdMap = getExpressIdMap(staff, trades);
            List<TradeTrace> tradeTraces = new ArrayList<>();
            List<ExpressOutSidRegularVO> regularList = new ArrayList<>();
            if (MapUtils.isEmpty(expressIdMap)) {
                // 没有模板则要匹配快递公司的规则
                regularList = expressOutSidRegularService.getRegularList(null, null, null);
            }

            // 只开启物流跟踪不订阅pdd和fxg
//            trades = filterTradeByLogisticsTracking(staff, trades, expressIdMap);

            // 处理合单,主单子单一起改变同步状态
            trades = buildMergeTrades(staff, trades, false);
            if (CollectionUtils.isEmpty(trades)) {
                return;
            }

            // 填充发货时间、平台发货时间、支付时间
            fillConsignTime(staff, trades);

            // 获取店铺Map
            Map<Long, String> shopNameMap = getShopNameMap(staff, trades);
            // 仓库名称Map
            Map<Long, String> warehouseNameMap = WarehouseHelper.getWarehouseNameMap(staff, trades, warehouseService);
            // 获取一单多包记录
            Map<Long, MultiPacksOutSidDetail> childOutSidDetailMap = getMultiPacksOutSidDetail(staff, trades, expressIdMap, true);
            // 获取FunctionFlag 为1代表开启 为0代表没有开启 顺序不能变
            String functionFlag = getFunctionFlag(staff);
            List<Map<String, Object>> list = new ArrayList<>();
            for (Trade trade : trades) {
                IExpressTemplateBase template = expressIdMap.get(buildExpressMapKey(trade.getTemplateId(), trade.getTemplateType()));
                buildTradeMap(staff, trade, template, shopNameMap, warehouseNameMap, null, tradeTraces, list, regularList, true, childOutSidDetailMap, functionFlag);
            }

            if (CollectionUtils.isNotEmpty(tradeTraces) && MapUtils.isEmpty(expressIdMap)) {
                // template为空是其他ERP发货,只有其他ERP发货订阅报错,正常单子按老逻辑只记录error日志,后续要改再说
                // 记录订单操作轨迹,落库
                tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
            }

            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            eventCenter.fireEvent(this, new EventInfo("sync.logistics.warning.consign.status").setArgs(new Object[]{list}), null);

            List<Long> sids = trades.stream().map(Trade::getSid).collect(Collectors.toList());
            logger.debug(LogHelper.buildLog(staff, "同步物流预警发货状态事件发送成功,订单号:" + JSONObject.toJSONString(sids)));
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("同步物流预警发货状态失败"), e);
            if (MapUtils.isEmpty(expressIdMap)) {
                List<TradeTrace> tradeTraces = new ArrayList<>();
                for (Trade trade : trades) {
                    tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff.getCompanyId(), trade, "订阅物流预警", staff.getName(), new Date(), e.getMessage()));
                }
                tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
            }
        }
    }

    /**
     * 订阅快递助手并生成物流预警任务
     *
     * @param staff
     * @param template 快递模板
     * @param trades   订单
     */
    public void subscribeLogisticsWarningTask(Staff staff, IExpressTemplateBase template, List<Trade> trades) {
        // 只开启物流跟踪不订阅pdd和fxg
//        trades = filterTradeByLogisticsTracking(staff, trades, template);

        // 处理合单,主单子单一起订阅
        trades = buildMergeTrades(staff, trades, true);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        // 获取店铺Map
        Map<Long, String> shopNameMap = getShopNameMap(staff, trades);
        // 仓库名称Map
        Map<Long, String> warehouseNameMap = WarehouseHelper.getWarehouseNameMap(staff, trades, warehouseService);
        // 获取一单多包记录,记录里包含模板
        Map<String, IExpressTemplateBase> expressIdMap = new HashMap<>();
        if (template != null) {
            expressIdMap.put(buildExpressMapKey(template.getId(), template.getExpressType()), template);
        }
        Map<Long, MultiPacksOutSidDetail> childOutSidDetailMap = getMultiPacksOutSidDetail(staff, trades, expressIdMap, false);


        List<Map<String, Object>> list = new ArrayList<>();
        List<TradeTrace> tradeTraces = new ArrayList<>();
        List<ExpressOutSidRegularVO> regularList = new ArrayList<>();
        if (template == null) {
            // 没有模板则要匹配快递公司的规则
            regularList = expressOutSidRegularService.getRegularList(null, null, null);
        }
        String functionFlag = getFunctionFlag(staff);
        for (Trade trade : trades) {
            buildTradeMap(staff, trade, template, shopNameMap, warehouseNameMap, null, tradeTraces, list, regularList, false, childOutSidDetailMap, functionFlag);
        }

        if (CollectionUtils.isNotEmpty(tradeTraces) && template == null) {
            // template为空是其他ERP发货,只有其他ERP发货订阅报错,正常单子按老逻辑只记录error日志,后续要改再说
            // 记录订单操作轨迹,落库
            tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        }

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        eventCenter.fireEvent(this, new EventInfo("subscribe.logistics.warning.task").setArgs(new Object[]{list}), null);

        List<Object> sids = list.stream().map(map -> map.get("sid")).collect(Collectors.toList());

        logger.debug(LogHelper.buildLog(staff, String.format("订阅物流预警事件发送成功,订单号:【%s】", JSONObject.toJSONString(sids))));
    }

    /**
     * 获取店铺
     *
     * @param staff
     * @param trades 订单列表
     * @return
     */
    Map<Long, String> getShopNameMap(Staff staff, List<Trade> trades) {
        // 获取店铺名称Map
        Set<Long> userIds = new HashSet<>();
        for (Trade trade : trades) {
            Long userId = null;
            if (TradeUtils.isGxTrade(trade)) {
                userId = trade.getTaobaoId();
            } else {
                userId = trade.getUserId();
            }
            if (userId != null) {
                userIds.add(userId);
            }
        }
        Map<Long, String> shopNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<Shop> shops = shopService.queryByUserIds(null, userIds.toArray(new Long[0]));
            shopNameMap = shops.stream().collect(Collectors.toMap(Shop::getUserId, shop -> StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : StringUtils.trimToEmpty(shop.getTitle()), (v1, v2) -> v2));
        }
        return shopNameMap;
    }


    /**
     * 获取快递公司列表Id
     *
     * @param staff
     * @param trades 订单列表
     * @return
     */
    Map<String, IExpressTemplateBase> getExpressIdMap(Staff staff, List<Trade> trades) {
        // 找出没有templateId的订单
//        List<Long> notTemplateSids = new ArrayList<>();
//        for (Trade trade : trades) {
//            if (trade.getTemplateId() == null || trade.getTemplateType() == null) {
//                notTemplateSids.add(trade.getSid());
//            }
//        }
//        if (CollectionUtils.isNotEmpty(notTemplateSids)) {
//            List<Trade> tradeList = tradeSearchService.queryBySids(staff, false, notTemplateSids.toArray(new Long[0]));
//            Map<Long, Trade> tradeMap = tradeList.stream().collect(Collectors.toMap(Trade::getSid, Function.identity()));
//            for (Trade trade : trades) {
//                if (tradeMap.containsKey(trade.getSid())) {
//                    trade.setTemplateId(tradeMap.get(trade.getSid()).getTemplateId());
//                    trade.setTemplateType(tradeMap.get(trade.getSid()).getTemplateType());
//                }
//            }
//        }

        // 模板Id列表
        Set<Long> templateIds = new HashSet<>();
        Set<Long> wlbTemplateIds = new HashSet<>();
        for (Trade trade : trades) {
            if (trade.getTemplateId() != null && trade.getTemplateType() != null) {
                if (trade.getTemplateType() == TemplateTypeEnum.ELECTRONIC_EXPRESS.ordinal()) {
                    wlbTemplateIds.add(trade.getTemplateId());
                } else {
                    templateIds.add(trade.getTemplateId());
                }
            }
        }
        // 1电子快递单模板
        Map<String, IExpressTemplateBase> expressIdMap = new HashMap<>();
        queryExpressTemplate(staff, expressIdMap, wlbTemplateIds, templateIds);
        return expressIdMap;
    }

    static String buildExpressMapKey(Long templateId, Integer templateType) {
        return templateId + "_" + templateType;
    }

    public Map<String, Object> buildTradeMap(Staff staff, Trade trade, IExpressTemplateBase template, Map<Long, String> shopNameMap, Map<Long, String> warehouseNameMap, String outSid) {
        // 共用信息,除了快递公司信息
        Map<String, Object> map = buildCommonTradeMsg(staff, trade, template, shopNameMap, warehouseNameMap, outSid);
        Long userId = map.get("userId") == null ? null : Long.parseLong(map.get("userId").toString());
        // 公司信息
        Long expressId = template != null ? template.getExpressId() : null;
        String cpCode = null;
        if (expressId != null) {
            ExpressCompany expressCompany = expressCompanyService.getExpressCompanyById(expressId);
            cpCode = expressCompany != null ? expressCompany.getCode() : null;
            map.put("cpCode", cpCode);
            map.put("logisticsCompany", expressCompany != null ? expressCompany.getName() : null);
        }
        if (CpCodeEnum.isSF(cpCode)) {
            SenderAddressContext context = new SenderAddressContext(warehouseService, addressService, template, trade.getWarehouseId(), userId);
            map.put("senderPhone", LogisticsTraceHelper.getSenderPhoneLastFour(staff, context));
        }
        return map;
    }


    public static Conf getCompanyProfileConf(Staff staff, ICompanyService companyService) {
        if (staff.getCompany() != null && staff.getCompany().getProfile() != null) {
            return staff.getCompany().getProfile().getConf();
        }
        return companyService.getCompanyProfile(staff.getCompanyId()).getConf();
    }

    /**
     * 过滤订单
     *
     * @param staff
     * @param trades
     * @return
     */
    public List<Trade> filterTrade(Staff staff, List<Trade> trades) {
        // 获取跨境平台列表
        List<String> kjPlatformSource = new ArrayList<>(tradeLocalConfigurable.queryKjPlatformSource());

        List<Trade> tradeList = new ArrayList<>(trades.size());
        for (Trade trade : trades) {
            // 过滤跨境订单
            if (!TradeUtils.platformContain(staff, trade, kjPlatformSource)) {
                tradeList.add(trade);
            }
        }
        return tradeList;
    }

    /**
     * 物流跟踪不订阅pdd和fxg
     *
     * @param trades
     * @return
     */
    public List<Trade> filterTradeByLogisticsTracking(Staff staff, List<Trade> trades, IExpressTemplateBase template) {
        // 判断是否开启物流预警配置
        Conf conf = getCompanyProfileConf(staff, companyService);
        boolean openLogisticsWarning = conf.getOpenLogisticsWarning();
        boolean isLogisticsTracking = TradeLogisticsTrackingUtils.isOpenLogisticsTracking(staff, tradeConfigService);
        // 只开启了物流跟踪，过滤掉pdd、fxg订单
        if (!openLogisticsWarning && isLogisticsTracking) {
            List<Trade> tradeList = new ArrayList<>(trades.size());
            for (Trade trade : trades) {
                if (NOT_SUBSCRIBE_SOURCES.contains(PrintTemplateHelper.getWlbTemplateType(template))) {
                    continue;
                }
                tradeList.add(trade);
            }
            return tradeList;
        }
        return trades;
    }

    /**
     * 物流跟踪不订阅pdd和fxg
     *
     * @param trades
     * @return
     */
    public List<Trade> filterTradeByLogisticsTracking(Staff staff, List<Trade> trades, Map<String, IExpressTemplateBase> expressIdMap) {
        // 判断是否开启物流预警配置
        Conf conf = getCompanyProfileConf(staff, companyService);
        boolean isLogisticsTracking = TradeLogisticsTrackingUtils.isOpenLogisticsTracking(staff, tradeConfigService);
        // 只开启了物流跟踪，过滤掉pdd、fxg订单
        if (!conf.getOpenLogisticsWarning() && isLogisticsTracking) {
            List<Trade> tradeList = new ArrayList<>(trades.size());
            for (Trade trade : trades) {
                IExpressTemplateBase template = expressIdMap.get(buildExpressMapKey(trade.getTemplateId(), trade.getTemplateType()));
                if (NOT_SUBSCRIBE_SOURCES.contains(PrintTemplateHelper.getWlbTemplateType(template))) {
                    continue;
                }
                tradeList.add(trade);
            }
            return tradeList;
        }
        return trades;
    }


    public List<Trade> filterTradeByWlbResult(Staff staff, List<WlbResult> successResult, List<Trade> trades) {
        if (CollectionUtils.isEmpty(successResult) || CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        List<Trade> tradeList = new ArrayList<>(successResult.size());
        Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(trades);
        for (WlbResult wlbResult : successResult) {
            Trade trade = tradeMap.get(Long.valueOf(wlbResult.getSid()));
            if (trade == null) {
                logger.info(LogHelper.buildLog(staff, String.format("订阅物流预警时过滤订单,订单号:【%s】", wlbResult.getSid())));
                continue;
            }
            if (StringUtils.isNotBlank(wlbResult.getOutSid())) {
                trade.setOutSid(wlbResult.getOutSid());
            }
            if (StringUtils.isBlank(trade.getOutSid())) {
                logger.info(LogHelper.buildLog(staff, String.format("订阅物流预警运单号为空,订单号:【%s】", wlbResult.getSid())));
                continue;
            }
            tradeList.add(trade);
        }
        return tradeList;
    }


   public static Trade buildTrade(Trade trade, IExpressTemplateBase template) {
        Trade target = new TbTrade();
        target.setCompanyId(trade.getCompanyId());
        target.setSource(trade.getSource());
        target.setSubSource(trade.getSubSource());
        target.setSid(trade.getSid());
        target.setMergeSid(trade.getMergeSid());
        target.setOutSid(trade.getOutSid());
        target.setTid(trade.getTid());
        target.setWarehouseId(trade.getWarehouseId());
        target.setWarehouseName(trade.getWarehouseName());
        target.setUserId(trade.getUserId());
        target.setTaobaoId(trade.getTaobaoId());
        target.setConvertType(trade.getConvertType());
        target.setDestId(trade.getDestId());
        target.setBelongType(trade.getBelongType());
        target.setSysStatus(trade.getSysStatus());
        target.setConsignTime(trade.getConsignTime());
        target.setTemplateId(trade.getTemplateId());
        target.setTemplateType(trade.getTemplateType());
        target.setReceiverState(trade.getReceiverState());
        target.setReceiverCity(trade.getReceiverCity());
        target.setPayTime(trade.getPayTime());
        target.setPtConsignTime(trade.getPtConsignTime());
        target.setTagIds(trade.getTagIds());
        target.setWeight(trade.getWeight());
        target.setVolume(trade.getVolume());
        target.setSysConsigned(trade.getSysConsigned());
        if (trade instanceof TbTrade || trade instanceof LogisticsTrackingPollPool) {
            TradeUtils.setOrders(target, TradeUtils.getOrders4Trade(trade));
        }
        if (template != null) {
            if (template instanceof UserWlbExpressTemplate) {
                UserWlbExpressTemplate userWlbExpressTemplate = (UserWlbExpressTemplate) template;
                target.setTemplateId(userWlbExpressTemplate.getId());
                target.setTemplateType(userWlbExpressTemplate.getTemplateType());
            }
            if (template instanceof UserExpressTemplate) {
                UserExpressTemplate userExpressTemplate = (UserExpressTemplate) template;
                target.setTemplateId(userExpressTemplate.getId());
                target.setTemplateType(userExpressTemplate.getTemplateType());
            }
        }
        target.setMobileTail(trade.getMobileTail());
        target.setReceiverMobile(trade.getReceiverMobile());
        target.setReceiverPhone(trade.getReceiverPhone());
        return target;
    }


    public Boolean checkAndFilter(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return false;
        }

        // 判断是否开启物流预警配置
        Conf conf = getCompanyProfileConf(staff, companyService);
        if (!conf.getOpenLogisticsWarning()) {
            return false;
        }

        // 过滤订单
        trades = filterTrade(staff, trades);
        if (CollectionUtils.isEmpty(trades)) {
            return false;
        }
        return true;
    }

    /**
     * 填充发货时间、支付时间、平台发货时间
     *
     * @param staff
     * @param trades
     */
    public void fillConsignTime(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        // fixme 为什么要填充
        List<Long> sidList = trades.stream().filter(x -> x.getConsignTime() == null || x.getPayTime() == null || x.getPtConsignTime() == null).map(Trade::getSid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sidList)) {
            return;
        }

        List<Trade> tradeList = tradeSearchService.queryBySidsContainMergeTrade(staff, false, true, true, sidList.toArray(new Long[0]));

        Map<Long, Trade> tradeMap = tradeList.stream().collect(Collectors.toMap(Trade::getSid, trade -> trade, (v1, v2) -> v2));

        for (Trade trade : trades) {
            Trade tempTrade = tradeMap.get(trade.getSid());
            if (tempTrade != null) {
                if (trade.getConsignTime() == null && tempTrade.getConsignTime() != null) {
                    trade.setConsignTime(tempTrade.getConsignTime());
                }
                if (trade.getPtConsignTime() == null && tempTrade.getPtConsignTime() != null) {
                    trade.setPtConsignTime(tempTrade.getPtConsignTime());
                }
                trade.setPayTime(tempTrade.getPayTime());
                trade.setSysStatus(tempTrade.getSysStatus());
                trade.setStatus(tempTrade.getStatus());
            }
        }
    }

    /**
     * 处理合单
     */
    public List<Trade> buildMergeTrades(Staff staff, List<Trade> trades, boolean isSubscribe) {
        // 订阅的时候默认都重新查遍trade,因为拆单子母单取号的时候没有将拆单子母单的tagId传进来,导致后面订阅有问题
        List<Long> sids = trades.stream().filter(t -> isSubscribe || TradeUtils.printIsMerge(t)).map(Trade::getSid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sids)) {
            return trades;
        }
        List<Trade> tradeList = tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, true, sids.toArray(new Long[0]));
        Map<Long, List<Trade>> tradeMap = tradeList.stream().collect(Collectors.groupingBy(t -> t.getMergeSid() == null || t.getMergeSid() < 1 ? t.getSid() : t.getMergeSid()));
        List<Trade> allTrades = new ArrayList<>();
        for (Trade trade : trades) {
            List<Trade> mergeTrades = tradeMap.get(trade.getSid());
            if (CollectionUtils.isEmpty(mergeTrades)) {
                // 可能存在已经有子单的数据了,通过子单的sid从map中拿不到,要过滤这种数据,不重复添加
                if (!TradeUtils.printIsMerge(trade)) {
                    allTrades.add(trade);
                }
                continue;
            }
            allTrades.addAll(mergeTrades);
        }
        return allTrades;
    }

    public void buildTradeMap(Staff staff, Trade trade, IExpressTemplateBase template, Map<Long, String> shopNameMap, Map<Long, String> warehouseNameMap, String outSid, List<TradeTrace> tradeTraces, List<Map<String, Object>> list,
                              List<ExpressOutSidRegularVO> regularList, boolean isConsign, Map<Long, MultiPacksOutSidDetail> childOutSidDetailMap, String functionFlag) {
        if (StringUtils.isBlank(trade.getOutSid()) && StringUtils.isBlank(outSid)) {
            tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff.getCompanyId(), trade, "订阅物流预警", staff.getName(), new Date(), "未查询到快递单号不能订阅物流信息,请检查是否有真实快递单号!"));
            return;
        }
        // 共用信息,除了快递公司信息
        Map<String, Object> map = buildCommonTradeMsg(staff, trade, template, shopNameMap, warehouseNameMap, outSid);
        Long userId = map.get("userId") == null ? null : Long.parseLong(map.get("userId").toString());
        // 公司信息
        Long expressId = template != null ? template.getExpressId() : null;
        String cpCode = null;
        if (expressId != null) {
            ExpressCompany expressCompany = expressCompanyService.getExpressCompanyById(expressId);
            cpCode = expressCompany != null ? expressCompany.getCode() : null;
            map.put("logisticsCompany", expressCompany != null ? expressCompany.getName() : null);
        } else {
            if (trade.getExpressCode() != null) {
                cpCode = trade.getExpressCode();
                map.put("logisticsCompany", trade.getExpressName() != null ? trade.getExpressName() : null);
            } else {
                // 根据运单号匹配cpCode
                // 获取匹配规则,页面https://erp3.superboss.cc/index.html#/aftersale/reg_express_next/
                if (CollectionUtils.isNotEmpty(regularList)) {
                    boolean isMatch = false;
                    for (ExpressOutSidRegularVO expressOutSidRegularVO : regularList) {
                        String regular = expressOutSidRegularVO.getRegular() == null ? "" : expressOutSidRegularVO.getRegular();
                        isMatch = Pattern.matches(regular, trade.getOutSid());
                        if (isMatch) {
                            cpCode = expressOutSidRegularVO.getExpressCode();
                            map.put("logisticsCompany", expressOutSidRegularVO.getExpressName());
                            break;
                        }
                    }
                }
            }
            if (CpCodeEnum.isSF(cpCode)) {
                logger.info(LogHelper.buildLog(staff, String.format("订阅物流预警,订单号:【%s】,templateId:【%s】,templateType:【%s】", trade.getSid(), trade.getTemplateId(), trade.getTemplateType())));
            }
            // 没有模板取不到寄件号码,取收件号码后四位,优先取数据库中截取好的后四位,没有就自己截取
            String receiverMobile = trade.getReceiverMobile();
            String receiverPhone = trade.getReceiverPhone();
            if (StringUtils.isNotBlank(trade.getMobileTail()) && trade.getMobileTail().length() > 4) {
                map.put("senderPhone", trade.getMobileTail());
            } else if (StringUtils.isNotBlank(receiverMobile) && receiverMobile.length() > 4) {
                map.put("senderPhone", receiverMobile.substring(receiverMobile.length() - 4));
            } else if (StringUtils.isNotBlank(receiverPhone) && receiverPhone.length() > 4) {
                map.put("senderPhone", receiverPhone.substring(receiverPhone.length() - 4));
            }
        }
        // 获取单子的一单多包单号\子母单单号\多物流单号,普通运单
        Map<String, String> senderPhoneMap = new HashMap<>();
        buildOutSidMap(staff, map, trade, childOutSidDetailMap, cpCode, template, userId, senderPhoneMap);

        // 简单组装商品信息
        buildTradeItemMap(map, trade);

        if (isConsign) {
            // 同步发货才塞
            map.put("consignStatus", 2); // 已发货
            map.put("consignTime", trade.getConsignTime() != null ? trade.getConsignTime().getTime() : System.currentTimeMillis());
            map.put("ptConsignTime", trade.getPtConsignTime() != null ? trade.getPtConsignTime().getTime() : System.currentTimeMillis());
            boolean isSysAfterSendGoods = trade.getSysStatus() != null && TradeStatusUtils.isAfterSendGoods(trade.getSysStatus());
            String ptStatus = TradeStatus.getSysStatus(trade.getStatus(), null);
            boolean isPtAfterSendGoods = trade.getStatus() != null && TradeStatusUtils.isAfterSendGoods(ptStatus);
            if (isSysAfterSendGoods) {
                map.put("consignType", 1); //系统发货
            } else if (isPtAfterSendGoods) {
                map.put("consignType", 2); //平台发货
            } else {
                map.put("consignType", 0); //未发货(如果isConsign为true,这个字段还是0,那说明获取订单的系统状态或平台状态有问题)
                logger.info("同步发货事件没有正确读到是系统发货还是平台发货,需排查订单数据!系统状态:[" + trade.getSysStatus() + "],平台状态:[" + ptStatus + "]");
            }
        }
        map.put("functionFlag", functionFlag);
        list.add(map);
    }

    /**
     * 组装trade的一些公用信息
     */
    public Map<String, Object> buildCommonTradeMsg(Staff staff, Trade trade, IExpressTemplateBase template, Map<Long, String> shopNameMap, Map<Long, String> warehouseNameMap, String outSid) {
        Long userId = TradeUtils.isGxTrade(trade) ? trade.getTaobaoId() : trade.getUserId();

        Map<String, Object> map = new HashMap<>();
        map.put("clueId", staff.getClueId());
        map.put("companyId", staff.getCompanyId());
        map.put("userId", userId);
        map.put("sid", trade.getSid());
        map.put("tid", trade.getTid());
        map.put("warehouseId", trade.getWarehouseId());
        String warehouseName = trade.getWarehouseName();
        if (StringUtils.isBlank(warehouseName) && trade.getWarehouseId() != null) {
            warehouseName = warehouseNameMap.get(trade.getWarehouseId());
        }
        map.put("warehouseName", warehouseName);
        map.put("sysStatus", trade.getSysStatus());
        map.put("shopId", userId);
        map.put("shopName", shopNameMap.get(userId));  // 店铺名称
//        新结构不要传outSid了
//        map.put("outSid", trade.getOutSid());
//        if (StringUtils.isNotBlank(outSid)) {
//            map.put("outSid", outSid);
//        }
        Map<String, String> sendAddrMap = logisticsTrackingService.getSendAddr(staff, userId, trade.getWarehouseId());
        if (trade.getSysStatus() != null && TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
            map.put("consignStatus", 2); // 已发货
        } else {
            map.put("consignStatus", 1);  // 未发货
        }
        map.put("consignTime", trade.getConsignTime() != null ? trade.getConsignTime().getTime() : null);
        map.put("source", PrintTemplateHelper.getWlbTemplateType(template) == null ? 0 : PrintTemplateHelper.getWlbTemplateType(template));//快递来源
        map.put("tradeSource", trade.getSource()); // 订单平台
        map.put("receiverState", trade.getReceiverState()); // 收件省
        map.put("receiverCity", trade.getReceiverCity()); // 收件市
        map.put("sendState", sendAddrMap.get("state")); //寄件省
        map.put("sendCity", sendAddrMap.get("city")); //寄件市
        map.put("payTime", trade.getPayTime() != null ? trade.getPayTime().getTime() : null);// 支付时间
        map.put("stateStatus", TradeLogisticsTrackingUtils.getStateStatus(sendAddrMap.get("state"), trade.getReceiverState())); //收寄省份状态
        map.put("ptConsignTime", trade.getPtConsignTime() != null ? trade.getPtConsignTime().getTime() : null);
        map.put("unifiedStatus", TradeStatus.getSysStatus(trade.getStatus(), null)); // 平台状态,直接用unifiedStatus可能不准
        return map;
    }

    /**
     * 回收单号发送事件
     * 运单作废通知
     */
    public void syncWayBillRecycle(Staff staff, List<MultiPacksOutSidDetail> outSidDetails) {
        // 判断是否开启物流预警配置,没有开启直接return
        Conf conf = getCompanyProfileConf(staff, companyService);
        boolean openLogisticsWarning = conf.getOpenLogisticsWarning();
        if (!openLogisticsWarning || CollectionUtils.isEmpty(outSidDetails)) {
            return;
        }
        try {
            // 快递公司Map
            Map<String, IExpressTemplateBase> expressIdMap = getOutSidExpressIdMap(staff, outSidDetails);
//            先不考虑其他erp发货这种没有具体快递的数据
//            List<ExpressOutSidRegularVO> regularList = new ArrayList<>();
//            if (MapUtils.isEmpty(expressIdMap)) {
//                // 没有模板则要匹配快递公司的规则
//                regularList = expressOutSidRegularService.getRegularList(null, null, null);
//            }
            List<Map<String, Object>> list = new ArrayList<>();
            List<String> logList = new ArrayList<>();
            // 订单信息
            Map<String, Object> map = new HashMap<>();
            map.put("companyId", staff.getCompanyId());
            List<Map<String, Object>> outSidMsgList = new ArrayList<>();
            for (MultiPacksOutSidDetail outSidDetail : outSidDetails) {
                Long sid = outSidDetail.getSid();
                List<String> outSids = new ArrayList<>();
                IExpressTemplateBase template = expressIdMap.get(buildExpressMapKey(outSidDetail.getTemplateId(), outSidDetail.getTemplateType()));
                // 单号信息--主单的信息
                Map<String, Object> outSidMsgMap = new HashMap<>();
                String masterOutSid = outSidDetail.getOutSid();
                String cpCode = null;
                if (template instanceof UserWlbExpressTemplate) {
                    cpCode = ((UserWlbExpressTemplate) template).getCpCode();
                } else if (template instanceof UserExpressTemplate) {
                    cpCode = ((UserExpressTemplate) template).getCpCode();
                }
                outSids.add(masterOutSid);
                outSidMsgMap.put("outSid", masterOutSid);
                outSidMsgMap.put("cpCode", cpCode);
                outSidMsgList.add(outSidMsgMap);
                List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail> childOutSidDetails = outSidDetail.getMultiPacksChildOutSidDetails();
                if (CollectionUtils.isNotEmpty(childOutSidDetails)) {
                    // 子单的信息
                    Map<String, List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail>> packsChildOutSidDetailMap = childOutSidDetails.stream().filter(t -> Objects.equals(0, t.getOutSidType()) && t.getMasterOutSid() != null)
                            .collect(Collectors.groupingBy(MultiPacksOutSidDetail.MultiPacksChildOutSidDetail::getMasterOutSid));
                    if (MapUtils.isNotEmpty(packsChildOutSidDetailMap) && CollectionUtils.isNotEmpty(packsChildOutSidDetailMap.get(masterOutSid))) {
                        // 子母件的单号整合在一起,放到主单下面
                        List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail> masterChildOutSids = packsChildOutSidDetailMap.get(masterOutSid);
                        List<String> subOutSids = masterChildOutSids.stream().map(MultiPacksOutSidDetail.MultiPacksChildOutSidDetail::getChildOutSid).filter(Objects::nonNull).collect(Collectors.toList());
                        // 过滤子母件的数据
                        childOutSidDetails.removeAll(masterChildOutSids);
                        outSids.addAll(subOutSids);
                        outSidMsgMap.put("subOutSidList", subOutSids);
                    }
                    // 除了子母件数据外的数据,除非不同单号,不然不会有值了,子母单和不同单号不会存在于一个场景,除了订阅,回收没有
                    if (CollectionUtils.isNotEmpty(childOutSidDetails)) {
                        for (MultiPacksOutSidDetail.MultiPacksChildOutSidDetail childOutSidDetail : childOutSidDetails) {
                            Map<String, Object> childOutSidMsgMap = new HashMap<>();
                            childOutSidMsgMap.put("outSid", childOutSidDetail.getChildOutSid());
                            outSids.add(childOutSidDetail.getChildOutSid());
                            IExpressTemplateBase wlbTemplate = expressIdMap.get(buildExpressMapKey(childOutSidDetail.getChildTemplateId(), TemplateTypeEnum.ELECTRONIC_EXPRESS.ordinal()));
                            String childCpCode = null;
                            if (wlbTemplate instanceof UserWlbExpressTemplate) {
                                childCpCode = ((UserWlbExpressTemplate) wlbTemplate).getCpCode();
                            }
                            childOutSidMsgMap.put("cpCode", childCpCode);
                            outSidMsgList.add(childOutSidMsgMap);
                        }
                    }
                }
                logList.add(String.format("订单号[%s]回收的单号:[%s]", sid, outSids));
            }
            map.put("list", outSidMsgList);
            list.add(map);

            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            eventCenter.fireEvent(this, new EventInfo("recycle.logistics.warning.task").setArgs(new Object[]{list}), null);
            JsonFilterUtils.flushLogBatch(logger, "同步物流预警单号回收事件发送成功,", logList, 13);
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append("同步物流预警单号回收状态事件发送失败"), e);
        }
    }

    /**
     * 获取快递公司列表Id
     *
     * @param staff
     * @param outSidDetails 运单列表详情
     * @return
     */
    Map<String, IExpressTemplateBase> getOutSidExpressIdMap(Staff staff, List<MultiPacksOutSidDetail> outSidDetails) {
        // 模板Id列表
        Set<Long> templateIds = new HashSet<>();
        Set<Long> wlbTemplateIds = new HashSet<>();
        for (MultiPacksOutSidDetail outSidDetail : outSidDetails) {
            if (outSidDetail.getTemplateId() != null) {
                if (Objects.equals(TemplateTypeEnum.ELECTRONIC_EXPRESS.ordinal(), outSidDetail.getTemplateType())) {
                    wlbTemplateIds.add(outSidDetail.getTemplateId());
                } else {
                    templateIds.add(outSidDetail.getTemplateId());
                }
            }
            List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail> childOutSidDetails = outSidDetail.getMultiPacksChildOutSidDetails();
            if (CollectionUtils.isNotEmpty(childOutSidDetails)) {
                for (MultiPacksOutSidDetail.MultiPacksChildOutSidDetail childOutSidDetail : childOutSidDetails) {
                    // 有子单号的都是电子面单的
                    wlbTemplateIds.add(childOutSidDetail.getChildTemplateId());
                }
            }
        }
        // 1电子快递单模板
        Map<String, IExpressTemplateBase> expressIdMap = new HashMap<>();
        queryExpressTemplate(staff, expressIdMap, wlbTemplateIds, templateIds);
        return expressIdMap;
    }

    /**
     * 查询模板
     *
     * @param expressIdMap   模板map,key是templateId+"_"+templateType
     * @param wlbTemplateIds 电子面单
     * @param templateIds    普通模板
     */
    private void queryExpressTemplate(Staff staff, Map<String, IExpressTemplateBase> expressIdMap, Set<Long> wlbTemplateIds, Set<Long> templateIds) {
        // 1电子快递单模板
        for (Long templateId : wlbTemplateIds) {
            UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQueryWithCache(staff, templateId, false);
            if (userWlbExpressTemplate != null && userWlbExpressTemplate.getExpressId() != null) {
                expressIdMap.put(buildExpressMapKey(templateId, TemplateTypeEnum.ELECTRONIC_EXPRESS.ordinal()), userWlbExpressTemplate);
            }
        }
        // 0普通快递
        for (Long templateId : templateIds) {
            UserExpressTemplate userExpressTemplate = userExpressTemplateService.userQueryWithCache(staff, templateId, false);
            if (userExpressTemplate != null && userExpressTemplate.getExpressId() != null) {
                expressIdMap.put(buildExpressMapKey(templateId, TemplateTypeEnum.EXPRESS.ordinal()), userExpressTemplate);
            }
        }
    }

    /**
     * 获取单子的一单多包单号\子母单单号\多物流单号
     */
    private void buildOutSidMap(Staff staff, Map<String, Object> map, Trade trade, Map<Long, MultiPacksOutSidDetail> childOutSidDetailMap, String cpCode,
                                IExpressTemplateBase template, Long userId, Map<String, String> senderPhoneMap) {
        // 一单多包单号\子母单\多物流单号
        Long sid = trade.getSid();
        if (TradeUtils.printIsMerge(trade)) {
            // 合单下要把主单的单号塞进来,好获取主单的一单多包记录
            sid = trade.getMergeSid();
        }
        Long warehouseId = trade.getWarehouseId();
        List<Map<String, Object>> outSidObjs = new ArrayList<>();
        String masterSendPhone = null;
        // 主单号信息
        Map<String, Object> masterOutSidObj = new HashMap<>();
        masterOutSidObj.put("isMaster", true);
        outSidObjs.add(masterOutSidObj);
        // 获取一单多包记录
        MultiPacksOutSidDetail multiPacksOutSidDetail = childOutSidDetailMap.get(sid);
        if (multiPacksOutSidDetail != null && CollectionUtils.isNotEmpty(multiPacksOutSidDetail.getMultiPacksChildOutSidDetails())) {
            //有一单多包记录 且详情不为空1
            buildMultiOutSidMsg(staff, map, multiPacksOutSidDetail, senderPhoneMap, warehouseId, userId, masterOutSidObj, outSidObjs, cpCode);
        } else {
            map.put("scene", 1); // 1-普通运单 2-一单多包 3-快递子母单
            if (CpCodeEnum.isSF(cpCode)) {
                if (template == null) {
                    // 没有模板取不到寄件号码,取收件号码后四位,优先取数据库中截取好的后四位,没有就自己截取
                    String receiverMobile = trade.getReceiverMobile();
                    String receiverPhone = trade.getReceiverPhone();
                    if (StringUtils.isNotBlank(trade.getMobileTail()) && trade.getMobileTail().length() > 4) {
                        masterSendPhone = trade.getMobileTail();
                    } else if (StringUtils.isNotBlank(receiverMobile) && receiverMobile.length() > 4) {
                        masterSendPhone = receiverMobile.substring(receiverMobile.length() - 4);
                    } else if (StringUtils.isNotBlank(receiverPhone) && receiverPhone.length() > 4) {
                        masterSendPhone = receiverPhone.substring(receiverPhone.length() - 4);
                    }
                } else {
                    // 有模板就取寄件号码,老逻辑
                    masterSendPhone = getSenderPhone(staff, senderPhoneMap, template, warehouseId, userId);
                }
                masterOutSidObj.put("senderPhone", masterSendPhone);
            }
            masterOutSidObj.put("cpCode", cpCode);
            masterOutSidObj.put("outSid", trade.getOutSid());
            int templateType = template == null ? 0 : template.getExpressType();
            masterOutSidObj.put("templateType", templateType);
        }
        map.put("outSidObj", outSidObjs);
    }

    /**
     * @param map
     * @param trade
     * @description: 组装商品信息传给物流预警
     * @author: tanyi
     * @date: 2025-06-12 15:05
     */
    public void buildTradeItemMap(Map<String, Object> map, Trade trade) {
        // 商品名称
        if (trade instanceof TbTrade || trade instanceof LogisticsTrackingPollPool) {
            String productName = null;
            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isNotEmpty(orderList)) {
                Order order = orderList.get(0);
                // 先取sysTitle 没有则取 title
                productName = StringUtils.isNotEmpty(order.getSysTitle()) ? order.getSysTitle() : StringUtils.isNotEmpty(order.getTitle()) ? order.getTitle() : null;
            }
            if (productName != null) {
                map.put("productName", productName);
            }
        }
        // 重量
        if (trade.getWeight() != null) {
            map.put("weight", trade.getWeight());
        }
        // 体积重 - 先不传
//        map.put("volumeWeight", );
        // 长 - 先不传
//        map.put("length", );
        // 宽 - 先不传
//        map.put("width", );
        // 高 - 先不传
//        map.put("height", );
        // 体积
        if (trade.getVolume() != null) {
            map.put("volume", trade.getVolume());
        }
        // 发货类型 必填 为空就给个默认值
        map.put("sysConsigned", trade.getSysConsigned() != null ? trade.getSysConsigned() : 1);
    }

    /**
     * 查询订单一单多包记录并填充快递公司
     */
    private Map<Long, MultiPacksOutSidDetail> getMultiPacksOutSidDetail(Staff staff, List<Trade> trades, Map<String, IExpressTemplateBase> expressIdMap, boolean isConsign) {
        // 查询单子一单多包记录,没有一单多包记录的这里也返回了,一单多包明细不去除与trade.getOutSid()一样的数据,因为多物流取号传进来的trade的单号就已经是这次获取的单号,与生成的一单多包记录明细是一个了,过滤会导致少订阅单号
        List<MultiPacksOutSidDetail> multiPacksOutSidDetails = multiPacksPrintTradeLogService.queryMultiPacksDetailByTrades(staff, trades, false, false);
        // 查询模板
        Set<Long> wlbTemplateIds = new HashSet<>();
        Set<Long> templateIds = new HashSet<>();
        int eleticTemplateType = TemplateTypeEnum.ELECTRONIC_EXPRESS.ordinal();
        List<Long> needQuerySids = new ArrayList<>();
        // 有一单多包记录的重新查遍trade,防止这次订阅发货是多物流的,多物流的话这次trade信息就是多物流的信息,非主单的
        Map<Long, Trade> tradeMap = new HashMap<>();
        // 只保留有一单多包的数据
        multiPacksOutSidDetails = multiPacksOutSidDetails.stream().filter(t -> CollectionUtils.isNotEmpty(t.getMultiPacksChildOutSidDetails()))
                .peek(multiPacksOutSidDetail -> {
                    Long templateId = multiPacksOutSidDetail.getTemplateId();
                    List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail> childOutSidDetails = multiPacksOutSidDetail.getMultiPacksChildOutSidDetails();
                    if (isNotContainTemplate(expressIdMap, templateId, eleticTemplateType)) {
                        if (Objects.equals(1, multiPacksOutSidDetail.getTemplateType())) {
                            wlbTemplateIds.add(templateId);
                        } else {
                            templateIds.add(templateId);
                        }
                    }
                    // 需要重新查订单的数据
                    if (isConsign) {
                        // 订阅的时候在查询合单那里已经重新查过了,覆盖了多物流取号的数据,这里只有发货的时候再单独查询了
                        needQuerySids.add(multiPacksOutSidDetail.getSid());
                    }

                    childOutSidDetails.stream()
                            .map(MultiPacksOutSidDetail.MultiPacksChildOutSidDetail::getChildTemplateId)
                            .filter(childTemplateId -> isNotContainTemplate(expressIdMap, childTemplateId, eleticTemplateType))
                            .distinct()
                            .forEach(wlbTemplateIds::add);
                }).collect(Collectors.toList());
        // 处理子母单一单多包的数据
        buildSplitTradesMultiMsg(staff, trades, needQuerySids, tradeMap, wlbTemplateIds, templateIds, multiPacksOutSidDetails, isConsign);
        // 查询模板
        queryExpressTemplate(staff, expressIdMap, wlbTemplateIds, templateIds);
        // 查快递公司,目前看不需要,在一单多包记录里塞快递公司ID的时候有模板ID
        for (MultiPacksOutSidDetail multiPacksOutSidDetail : multiPacksOutSidDetails) {
            Trade trade = tradeMap.get(multiPacksOutSidDetail.getSid());
            if (trade != null) {
                // 将重新查到的塞到单号记录里去,防止这次订阅发货是多物流的,多物流的信息不一定是trade的数据
                if (!TradeUtils.haveAllChildSplitTag(trade)) {
                    // 拆单子母单的子单在前面的处理逻辑中改了运单号,这里不覆盖,不然又把子单号给物流预警了,没有给主单号
                    multiPacksOutSidDetail.setOutSid(trade.getOutSid());
                }
                multiPacksOutSidDetail.setTemplateId(trade.getTemplateId());
                multiPacksOutSidDetail.setTemplateType(trade.getTemplateType());
                multiPacksOutSidDetail.setLogisticsCompanyId(trade.getLogisticsCompanyId());
            }
            IExpressTemplateBase expressTemplateBase = expressIdMap.get(buildExpressMapKey(multiPacksOutSidDetail.getTemplateId(), eleticTemplateType));
            multiPacksOutSidDetail.setExpressTemplate(expressTemplateBase);
            List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail> childOutSidDetails = multiPacksOutSidDetail.getMultiPacksChildOutSidDetails();
            if (CollectionUtils.isNotEmpty(childOutSidDetails)) {
                Iterator<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail> childOutSidDetailIterator = childOutSidDetails.iterator();
                while (childOutSidDetailIterator.hasNext()) {
                    MultiPacksOutSidDetail.MultiPacksChildOutSidDetail childOutSidDetail = childOutSidDetailIterator.next();
                    if (Objects.equals(multiPacksOutSidDetail.getOutSid(), childOutSidDetail.getChildOutSid())) {
                        // 因为查询一单多包记录不过滤明细了,所以这里重新过滤下
                        childOutSidDetailIterator.remove();
                        continue;
                    }
                    Long childTemplateId = childOutSidDetail.getChildTemplateId();
                    IExpressTemplateBase childExpressTemplateBase = expressIdMap.get(buildExpressMapKey(childTemplateId, eleticTemplateType));
                    if (childExpressTemplateBase instanceof UserWlbExpressTemplate) {
                        UserWlbExpressTemplate wlbExpressTemplate = (UserWlbExpressTemplate) childExpressTemplateBase;
                        childOutSidDetail.setChildCpCode((wlbExpressTemplate).getCpCode());
                        childOutSidDetail.setWlbExpressTemplate(wlbExpressTemplate);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(multiPacksOutSidDetails)) {
            return new HashMap<>();
        }
        return multiPacksOutSidDetails.stream().collect(Collectors.toMap(MultiPacksOutSidDetail::getSid, Function.identity(), (t1, t2) -> t1));
    }

    /**
     * 模板map中不包含此模板,用于查询模板的时候,不重复查询
     */
    private boolean isNotContainTemplate(Map<String, IExpressTemplateBase> expressIdMap, Long templateId, Integer templateType) {
        return !Objects.equals(templateId, -1L) && (MapUtils.isEmpty(expressIdMap) || !expressIdMap.containsKey(buildExpressMapKey(templateId, templateType)));
    }

    private String getSenderPhone(Staff staff, Map<String, String> senderPhoneMap, IExpressTemplateBase template, Long warehouseId, Long userId) {
        if (template == null) {
            logger.debug(LogHelper.buildLog(staff, "模板为空，无法获取发件人手机号!"));
            return "";
        }
        String senderPhone;
        String phoneKey = buildExpressMapKey(template.getId(), template.getExpressType());
        if (!senderPhoneMap.containsKey(phoneKey)) {
            SenderAddressContext context = new SenderAddressContext(warehouseService, addressService, template, warehouseId, userId);
            String senderPhoneLastFour = LogisticsTraceHelper.getSenderPhoneLastFour(staff, context);
            senderPhone = senderPhoneLastFour;
            senderPhoneMap.put(phoneKey, senderPhoneLastFour);
        } else {
            senderPhone = senderPhoneMap.get(phoneKey);
        }
        return senderPhone;
    }

    /**
     * 处理一单多包单号的信息
     */
    private void buildMultiOutSidMsg(Staff staff, Map<String, Object> map, MultiPacksOutSidDetail multiPacksOutSidDetail, Map<String, String> senderPhoneMap, Long warehouseId, Long userId,
                                     Map<String, Object> masterOutSidObj, List<Map<String, Object>> outSidObjs, String cpCode) {
        // 有一单多包的主log信息,就取主log的信息,包含单号模板等,不取传进来的值,避免多物流取号传的模板与订单的不一致
        IExpressTemplateBase expressTemplateBase = multiPacksOutSidDetail.getExpressTemplate();
        int templateType = 0;
        ExpressCompany expressCompany = null;
        if (expressTemplateBase == null) {
            logger.debug(LogHelper.buildLog(staff, "运单号:" + multiPacksOutSidDetail.getOutSid() + ",没有获取到模板!templateId_type:" + multiPacksOutSidDetail.getTemplateId() + "_" + multiPacksOutSidDetail.getTemplateType()));
        } else {
            templateType = expressTemplateBase.getExpressType();
            expressCompany = expressCompanyService.getExpressCompanyById(expressTemplateBase.getExpressId());
            cpCode = expressCompany != null ? expressCompany.getCode() : cpCode;
        }
        if (CpCodeEnum.isSF(cpCode)) {
            // 有模板就取寄件号码,老逻辑
            String masterSendPhone = getSenderPhone(staff, senderPhoneMap, expressTemplateBase, warehouseId, userId);
            masterOutSidObj.put("senderPhone", masterSendPhone);
        }
        masterOutSidObj.put("outSid", multiPacksOutSidDetail.getOutSid());
        masterOutSidObj.put("cpCode", cpCode);
        masterOutSidObj.put("templateType", templateType);
        map.put("logisticsCompany", expressCompany != null ? expressCompany.getName() : null);

        // 子单号信息
        List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail> multiPacksChildOutSidDetails = multiPacksOutSidDetail.getMultiPacksChildOutSidDetails();
        if (CollectionUtils.isNotEmpty(multiPacksChildOutSidDetails)) {
            // 子母件类型的一单多包记录明细
            Map<String, List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail>> packsChildOutSidDetailMap = new HashMap<>();
            List<String> allPackChildOutSids = new ArrayList<>();
            for (MultiPacksOutSidDetail.MultiPacksChildOutSidDetail multiPacksChildOutSidDetail : multiPacksChildOutSidDetails) {
                if (Objects.equals(0, multiPacksChildOutSidDetail.getOutSidType()) && !Objects.equals(multiPacksChildOutSidDetail.getMasterOutSid(), multiPacksChildOutSidDetail.getChildOutSid())) {
                    // 子母单的子单号统计,不包含多物流一单多包的主单号
                    allPackChildOutSids.add(multiPacksChildOutSidDetail.getChildOutSid());
                    packsChildOutSidDetailMap.computeIfAbsent(multiPacksChildOutSidDetail.getMasterOutSid(), k -> new ArrayList<>()).add(multiPacksChildOutSidDetail);
                }
            }
            if (MapUtils.isNotEmpty(packsChildOutSidDetailMap)) {
                List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail> masterChildOutSids = packsChildOutSidDetailMap.get(multiPacksOutSidDetail.getOutSid());
                if (CollectionUtils.isNotEmpty(masterChildOutSids)) {
                    // 主单的子母件的单号整合在一起
                    List<String> subOutSids = masterChildOutSids.stream().map(MultiPacksOutSidDetail.MultiPacksChildOutSidDetail::getChildOutSid).filter(Objects::nonNull).collect(Collectors.toList());
                    // 过滤主单的子母件的数据
                    multiPacksChildOutSidDetails.removeAll(masterChildOutSids);
                    masterOutSidObj.put("subOutSid", subOutSids);
                }
            }
            // 去除掉主单的子母单数据后的子单数据,只要有就是一单多包不同单号非子母单了
            if (CollectionUtils.isNotEmpty(multiPacksChildOutSidDetails)) {
                // 不同单号的就是非子母单的,每个子单号明细单独记录详情数据
                for (MultiPacksOutSidDetail.MultiPacksChildOutSidDetail childOutSidDetail : multiPacksChildOutSidDetails) {
                    String childCpCode = childOutSidDetail.getChildCpCode();
                    String childOutSid = childOutSidDetail.getChildOutSid();
                    // 是子母单取号的,但是非主单号的,过滤掉,只有是主单号的留下组装属于它的子单号subOutSid
                    if (CollectionUtils.isNotEmpty(allPackChildOutSids) && allPackChildOutSids.contains(childOutSid)) {
                        continue;
                    }
                    Map<String, Object> childOutSidObj = new HashMap<>();
                    if (CpCodeEnum.isSF(childCpCode)) {
                        // 有模板就取寄件号码,老逻辑
                        UserWlbExpressTemplate wlbExpressTemplate = childOutSidDetail.getWlbExpressTemplate();
                        String childSendPhone = getSenderPhone(staff, senderPhoneMap, wlbExpressTemplate, warehouseId, userId);
                        childOutSidObj.put("senderPhone", childSendPhone);
                    }
                    childOutSidObj.put("cpCode", childCpCode);
                    childOutSidObj.put("outSid", childOutSid);
                    childOutSidObj.put("isMaster", false);
                    childOutSidObj.put("templateType", templateType);
                    outSidObjs.add(childOutSidObj);
                    if (MapUtils.isNotEmpty(packsChildOutSidDetailMap) && packsChildOutSidDetailMap.containsKey(childOutSid)) {
                        List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail> childOutSids = packsChildOutSidDetailMap.get(childOutSid);
                        List<String> subOutSids = childOutSids.stream().map(MultiPacksOutSidDetail.MultiPacksChildOutSidDetail::getChildOutSid).filter(t -> !Objects.equals(childOutSid, t)).filter(Objects::nonNull).collect(Collectors.toList());
                        childOutSidObj.put("subOutSid", subOutSids);
                    }
                }
                map.put("scene", 2); //一单多包,包含多物流取的号
            } else {
                map.put("scene", 3); //快递子母单
            }
        } else {
            logger.info("有一单多包主数据,没有明细数据,请排查!sid为:" + multiPacksOutSidDetail.getSid());
            map.put("scene", 1); // 1-普通运单 2-一单多包 3-快递子母单
        }
    }

    /**
     * 处理拆单子母单的物流预警数据
     */
    private void buildSplitTradesMultiMsg(Staff staff, List<Trade> trades, List<Long> needQuerySids, Map<Long, Trade> tradeMap, Set<Long> wlbTemplateIds, Set<Long> templateIds,
                                          List<MultiPacksOutSidDetail> multiPacksOutSidDetails, boolean isConsign) {
        Map<Long, MultiPacksOutSidDetail> outSidDetailMap = multiPacksOutSidDetails.stream().collect(Collectors.toMap(MultiPacksOutSidDetail::getSid, Function.identity(), (t1, t2) -> t1));
        List<String> allChildOutSids = new ArrayList<>();
        List<Long> allSids = new ArrayList<>();
        for (Trade trade : trades) {
            String outSid = trade.getOutSid();
            allSids.add(trade.getSid());
            MultiPacksOutSidDetail multiPacksOutSidDetail = outSidDetailMap.get(trade.getSid());
            if (TradeUtils.haveAllMainSplitTag(trade) && !isConsign && multiPacksOutSidDetail != null && CollectionUtils.isNotEmpty(multiPacksOutSidDetail.getMultiPacksChildOutSidDetails())) {
                // 只有拆单子母单取号的时候,且当前订单的单号是主单,订阅的时候还要加子单的,拿到子单的运单号,再去查对应的sid,发货的时候不需要
                allChildOutSids.addAll(multiPacksOutSidDetail.getMultiPacksChildOutSidDetails().stream().filter(t -> Objects.equals(t.getMasterOutSid(), outSid) && !Objects.equals(t.getChildOutSid(), t.getMasterOutSid()) && Objects.equals(0, t.getOutSidType()))
                        .map(MultiPacksOutSidDetail.MultiPacksChildOutSidDetail::getChildOutSid).collect(Collectors.toList()));
            }
            if (TradeUtils.haveAllChildSplitTag(trade)) {
                //拆单子母单默认重新查遍trade,拿trade的运单号去查一单多包记录中这个单号对应的主单号是哪个,
                needQuerySids.add(trade.getSid());
            }
        }
        if (CollectionUtils.isNotEmpty(allChildOutSids)) {
            // 多物流取的号的没有在运单池,是查不到的,能查到的且对应的系统单号不在原有sids中的,只有子母单的子单
            Map<String, Long> outSidPoolMap = outSidPoolService.queryMapByOutSids(staff, allChildOutSids);
            for (String outSid : allChildOutSids) {
                if (outSidPoolMap.get(outSid) != null && !allSids.contains(outSidPoolMap.get(outSid))) {
                    needQuerySids.add(outSidPoolMap.get(outSid));
                }
            }
        }
        Map<Long, String> needQueryMultiLogMap = new HashMap<>();
        // 有一单多包记录的重新查遍trade,防止这次订阅发货是多物流的,多物流的话这次trade信息就是多物流的信息,非主单的,需要填充最新的订单信息
        if (CollectionUtils.isNotEmpty(needQuerySids)) {
            needQuerySids = needQuerySids.stream().distinct().collect(Collectors.toList());
            List<Trade> tradeList = tradeSearchService.queryBySids(staff, false, needQuerySids.toArray(new Long[0]));
            for (Trade trade : tradeList) {
                tradeMap.put(trade.getSid(), trade);
                Long templateId = trade.getTemplateId();
                if (Objects.equals(1, trade.getTemplateType())) {
                    wlbTemplateIds.add(templateId);
                } else {
                    templateIds.add(templateId);
                }
                if (TradeUtils.haveAllChildSplitTag(trade)) {
                    // 拆单子母单取号的子单数据,要拿运单号去查一单多包记录里该单号对应的主单号及其主单号下的其他子单号
                    needQueryMultiLogMap.put(trade.getSid(), trade.getOutSid());
                }
                if (!allSids.contains(trade.getSid())) {
                    // 原订单列表中不包含的单子,就是新的查到的拆单子母单的子单数据,订阅的时候要将子单一起订阅上
                    trades.add(trade);
                }
            }
        }
        // 拿查到的单号重新去查一单多包记录里该单号对应的主单号及其主单号下的其他子单号
        if (MapUtils.isNotEmpty(needQueryMultiLogMap)) {
            List<MultiPacksPrintTradeLogDetail> printTradeLogDetails = multiPacksPrintTradeLogService.queryDetailByChildOutSid(staff, new ArrayList<>(needQueryMultiLogMap.values()));
            // 子单号对应的主单号
            Map<String, String> detailOutSidMap = new HashMap<>();
            Map<String, List<MultiPacksPrintTradeLogDetail>> detailByMasterOutSidMap = new HashMap<>();
            printTradeLogDetails.stream()
                    // 过滤就是主单号的数据,保留子单号的数据
                    .filter(t -> t.getMasterOutSid() != null && !Objects.equals(t.getOutSid(), t.getMasterOutSid()))
                    .forEach(t -> {
                        // 构建 子单号对应的主单号
                        detailOutSidMap.put(t.getOutSid(), t.getMasterOutSid());

                        // 构建 该主单号下其余的子单数据
                        detailByMasterOutSidMap.computeIfAbsent(t.getMasterOutSid(), k -> new ArrayList<>())
                                .add(t);
                    });

            for (Map.Entry<Long, String> logEntry : needQueryMultiLogMap.entrySet()) {
                // 拆单子母单的子单的系统单号及运单号(运单号就是子单号)
                Long sid = logEntry.getKey();
                String outSid = logEntry.getValue();
                String masterOutSid = detailOutSidMap.get(outSid);
                MultiPacksOutSidDetail multiPacksOutSidDetail = outSidDetailMap.get(sid);
                if (StringUtils.isBlank(masterOutSid) || CollectionUtils.isEmpty(detailByMasterOutSidMap.get(masterOutSid))) {
                    continue;
                }
                if (multiPacksOutSidDetail == null) {
                    if (tradeMap.get(sid) == null) {
                        continue;
                    }
                    // 拆单子母单取号订阅的时候,只有主单信息,新查出来的子单数据是没有MultiPacksOutSidDetail的,重新组装一个
                    multiPacksOutSidDetail = new MultiPacksOutSidDetail();
                    multiPacksOutSidDetails.add(multiPacksOutSidDetail);
                }
                if (tradeMap.get(sid) != null) {
                    Trade newTrade = tradeMap.get(sid);
                    multiPacksOutSidDetail.setSid(sid);
                    multiPacksOutSidDetail.setTemplateId(newTrade.getTemplateId());
                    multiPacksOutSidDetail.setTemplateType(newTrade.getTemplateType());
                }
                // 拆单子母单的子单订阅物流同步发货的时候要塞一单多包的主单号
                multiPacksOutSidDetail.setOutSid(masterOutSid);
                List<MultiPacksPrintTradeLogDetail> details = detailByMasterOutSidMap.get(masterOutSid);
                if (CollectionUtils.isNotEmpty(details)) {
                    List<MultiPacksOutSidDetail.MultiPacksChildOutSidDetail> childOutSidDetails = new ArrayList<>();
                    for (MultiPacksPrintTradeLogDetail detail : details) {
                        MultiPacksOutSidDetail.MultiPacksChildOutSidDetail childOutSidDetail = new MultiPacksOutSidDetail.MultiPacksChildOutSidDetail();
                        childOutSidDetail.setChildOutSid(detail.getOutSid());
                        childOutSidDetail.setOutSidType(detail.getOutSidType());
                        childOutSidDetail.setChildTemplateId(detail.getTemplateId());
                        childOutSidDetail.setChildLogisticsCompanyId(detail.getLogisticsCompanyId());
                        childOutSidDetail.setMasterOutSid(detail.getMasterOutSid());
                        childOutSidDetail.setSid(sid); //一单多包记录的sid是主单sid,这里填子单sid
                        childOutSidDetails.add(childOutSidDetail);
                    }
                    if (CollectionUtils.isNotEmpty(multiPacksOutSidDetail.getMultiPacksChildOutSidDetails())) {
                        multiPacksOutSidDetail.getMultiPacksChildOutSidDetails().addAll(childOutSidDetails);
                    } else {
                        multiPacksOutSidDetail.setMultiPacksChildOutSidDetails(childOutSidDetails);
                    }
                }
            }
        }
    }

    /**
     * @param staff
     * @return java.lang.String
     * @description: 组装FunctionFlag 为1代表开启 为0代表没有开启 顺序不能变 一共五位 每一位对应一个配置
     * 1、物流预警 2、揽收上传 3、物流异常跟踪 4、打单-发货极速版 5、快递揽收信息表
     * @author: tanyi
     * @date: 2025-05-22 16:05
     */
    public String getFunctionFlag(Staff staff) {
        StringBuilder functionFlag = new StringBuilder();

        // 1. 是否开启物流预警配置
        functionFlag.append(isOpenLogisticsWarning(staff) ? "1" : "0");

        // 2. 是否开启拼多多揽件上传
        functionFlag.append(isPddUploadStrategy(staff) ? "1" : "0");

        // 3. 是否开启物流异常跟踪
        functionFlag.append(isOpenLogisticsTracking(staff) ? "1" : "0");

        // 4. 默认值：打单发货极速版、快递揽收报表
        functionFlag.append("10");

        return functionFlag.toString();
    }

    /**
     * @param staff
     * @return boolean
     * @description: 判断是否开启物流预警配置
     * @author: tanyi
     * @date: 2025-05-22 15:56
     */
    private boolean isOpenLogisticsWarning(Staff staff) {
        Conf conf = getCompanyProfileConf(staff, companyService);
        return conf != null && conf.getOpenLogisticsWarning();
    }

    /**
     * @param staff
     * @return boolean
     * @description: 判断是否开启揽件上传
     * @author: tanyi
     * @date: 2025-05-22 15:57
     */
    private boolean isPddUploadStrategy(Staff staff) {
        List<User> users = staff.getUsers();
        if (CollectionUtils.isEmpty(users)) {
            return false;
        }
        for (User user : users) {
            if (StringUtils.isEmpty(user.getConf())) {
                continue;
            }
            UserConf userConf = JSON.parseObject(user.getConf(), UserConf.class);
            if (userConf != null && Integer.valueOf(2).equals(userConf.getPddUploadStrategy())) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param staff
     * @return boolean
     * @description: 判断是否开启物流异常跟踪
     * @author: tanyi
     * @date: 2025-05-22 15:57
     */
    private boolean isOpenLogisticsTracking(Staff staff) {
        TradeConfig config = tradeConfigService.get(staff);
        return config != null && Integer.valueOf(1).equals(config.getOpenLogisticsTracking());
    }


}

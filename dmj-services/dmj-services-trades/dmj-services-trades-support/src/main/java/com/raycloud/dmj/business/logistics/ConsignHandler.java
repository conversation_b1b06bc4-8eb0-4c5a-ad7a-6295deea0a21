package com.raycloud.dmj.business.logistics;

import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.common.enums.SaleListenerEnums;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.aoxiang.AxMessage;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.constant.*;
import com.raycloud.dmj.domain.enums.OpVEnum;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.tb.trade.aoxiang.OrderProcessReportAccess;
import com.raycloud.dmj.utils.*;
import com.raycloud.ec.api.*;
import org.apache.commons.collections.*;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 非出库单发货处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2017-06-07 19:46
 */
@Getter
public class ConsignHandler {

    public SendType consignType;
    protected Date consignTime;

    protected IEventCenter eventCenter;

    public Integer dymmyType;
    protected String noLogisticsName;
    protected String noLogisticsTel;

    private String operateType;

    protected Map<Long, List<UploadRecord>> sidUploadRecordsMap;

    /**
     * BTAS组包信息
     */
    public TradeCombineParcel tradeCombineParcel;

    /**
     * 默认供销订单发货流程： 供销 --> 分销 。
     * 供销发货校验分销订单成功，但分销执行发货的时候分销有异常。供销发货成功了，分销发货失败。
     * 期间分销的平台订单发生的变更导致有异常，可能是修改备注。可能是其他动作
     *
     * 供销发货从对应分销订单开始
     */
    public boolean ifGxConsignStartWithFx = false;

    public ConsignHandler() {
        this(SendType.OFFLINE);
    }

    public ConsignHandler(SendType consignType) {
        this.consignType = consignType;
        this.consignTime = new Date();
    }

    public ConsignHandler setDymmyType(Integer dymmyType) {
        this.dymmyType = dymmyType;
        return this;
    }

    public ConsignHandler setNoLogisticsName(String noLogisticsName) {
        this.noLogisticsName = noLogisticsName;
        return this;
    }

    public ConsignHandler setNoLogisticsTel(String noLogisticsTel) {
        this.noLogisticsTel = noLogisticsTel;
        return this;
    }

    public ConsignHandler setEventCenter(IEventCenter eventCenter) {
        this.eventCenter = eventCenter;
        return this;
    }

    public ConsignHandler setOperateType(String operateType) {
        this.operateType = operateType;
        return this;
    }

    public ConsignHandler setTradeCombineParcel(TradeCombineParcel tradeCombineParcel) {
        this.tradeCombineParcel = tradeCombineParcel;
        return this;
    }

    /**
     * 校验订单是否满足发货条件
     *
     * @param staff       操作员
     * @param trade       要校验的订单
     * @param consignData 发货数据容器
     */
    public boolean check(Staff staff, Trade trade, ConsignData consignData) {
        String error = ConsignUtils.validateConsign(staff, trade, true, consignType, consignData.tradeConfig, consignData.warehouseIdType, consignData.fxTradeMap, consignData.customerBalanceMap, dymmyType, noLogisticsName, noLogisticsTel, tradeCombineParcel, consignData.fxCashFlowData, "2".equals(consignData.operateType));
        if (error != null) {
            consignData.result.add(buildConsignRecord(staff, trade, 1, 1).setErrorDesc(error));
            consignData.deliverExecpTrades.put(trade.getSid(), error);
            if (TradeUtils.isAfterSendGoods(trade)) {
//                consignData.trades2ReUpload.add(trade);//先注释下 杭州维美品牌管理有限公司 平台交易号：4870399274794122601A,操作日志2021/11/29 11:20:14触发了2次上传导致订单上传异
            }
            if (TradeUtils.isQimenFxSource(trade)
                    && MapUtils.isNotEmpty(consignData.fxCashFlowData)
                    && consignData.fxCashFlowData.containsKey(trade.getSourceId())
                    && error.contains("余额不足")) {
                consignData.checkBalanceFailTradeMap.put(trade.getSid(), trade);
            }
            Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("系统发货校验不通过: %s[%s],无法发货", error, trade.getSid())));
            return false;
        }
        //系统未触发过上传 1 需要上传
        //系统触发过上传 模板、运单号 不一样 1 需要上传
        //系统触发过上传 模板、运单号 一样 存在某个order没有触发上传 1 需要触发上传
        int enableStatus = !trade.isSysTriggerUpload() ? 1 : consignType!=null&&consignType==SendType.DUMMY||needResend(staff, trade) ? 1 : TradeUtils.getOrders4Trade(trade).stream().anyMatch(this::needUpload) ? 1 : 0;
        if (CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSource()) && CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSubSource())) {//jit订单只需要系统发货
            enableStatus = 0;
        }
        //分销单不需要从这边触发上传 （应该分销发货后去触发上传 否则先触发上传再发货会导致发货的记录覆盖掉上传的记录）后续优化
//        if (TradeUtils.isFxTrade(trade)) {
//            enableStatus = 0;
//        }
        if (CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(trade.getSource())) {
            enableStatus = 0;
        }
        // 走发货上传服务时 都传过去，避免按数量拆单发货这里就被过滤掉了
        if (enableStatus == 0 && UploadServiceWhitelistUtils.isDistributeUploadServiceTrade(staff, 1, trade)) {
            enableStatus = 1;
        }
        // 系统发货前已经发送订单上传请求，则不进行发货
        if (Objects.equals(false, trade.getNeedUploadTrade())) {
            enableStatus = 0;
        }
        if (enableStatus == 1|| PlatformUtils.isQimenSource(trade)) {//奇门二次上传
            consignData.trades2Upload.add(trade);
        }
        int isError = 0;
        int isUploadError = 0;
        //如果存在上传异常，则上传失败的记录里面需要显示
        if (isError == 0 && enableStatus == 0 && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UPLOAD_EXCEPT)) {
            enableStatus = 1;
            isError = 1;
            isUploadError = 1;
        }
        trade.setConsignType(consignType);
        consignData.trades2Consigned.add(trade);
        ConsignRecord record = buildConsignRecord(staff, trade, isError, enableStatus);
        record.setIsUploadError(isUploadError);
        consignData.consignRecords.add(record);
        //系统发货都是成功的
        consignData.result.add(record);
        consignData.tradeCombineParcel = tradeCombineParcel;
        //生成用于DB更新的trade和order
        buildUpdateTrade(staff, trade, consignData);
        return true;
    }

    private boolean needUpload(Order order) {
        return !order.isSysTriggerUpload() && order.getStatus() != null && !TradeStatusUtils.isAfterSendGoods(TradeStatus.getSysStatus(order.getStatus(), null));
    }

    /**
     * 判断之前上传的模板/运单号跟现在的模板运单号是否一致，一致则不需要上传，不一致则需要根据配置看是否触发重新发货
     */
    private boolean needResend(Staff staff, Trade trade) {
        if (sidUploadRecordsMap == null) {
            return false;
        }
        List<UploadRecord> uploadRecords = sidUploadRecordsMap.get(trade.getSid());
        return uploadRecords == null || uploadRecords.size() == 0 || !UploadUtils.equalsExpress(UploadUtils.buildExpressData(trade), UploadUtils.buildExpressData(uploadRecords.get(0)));
    }

    protected ConsignRecord buildConsignRecord(Staff staff, Trade trade, Integer isError, Integer enableStatus) {
        ConsignRecord record = ConsignUtils.buildFullRecord(trade, consignType, isError, enableStatus);
        record.setConsigned(consignTime);
        record.setBuyType(staff.getCompany().getOrderType());
        record.setMergeSid(trade.getMergeSid());
        return record;
    }

    /**
     * 系统发货完成之后需要做的事
     *
     * @param staff       操作员
     * @param consignData 本次发货数据
     * @param clientIp    上传京东日志时需要的客户端IP
     */
    public void postConsign(Staff staff, ConsignData consignData, String clientIp, Integer dymmyType, String noLogisticsName, String noLogisticsTel) {
        if (!consignData.trades2Consigned.isEmpty()) {
            //发送事件添加订单操作记录
            new EventFireSplitTradeUtils(eventCenter, 150).splitResult(this, new EventInfo("trade.consign.success").setArgs(new Object[]{staff, consignType, dymmyType, noLogisticsName, noLogisticsTel}), consignData.trades2Consigned);
            //发送事件更新面单池面单号
            eventCenter.fireEvent(this, new EventInfo("trade.outSid.updateStatusSend").setArgs(new Object[]{staff, TradeUtils.toSidList(consignData.trades2Consigned)}), null);
            //发送订单发货完成的事件给售后以及库存消费监听器
            eventCenter.fireEvent(this, new EventInfo("inner.trade.consign.success").setArgs(new Object[]{staff, buildTrade4InnerConsignSuccess(consignData.trades2Consigned)}), null);
            // 翱象 仓作业信息同步
            List<AxMessage> ms = AoxiangUtils.canSendSidsToOrderProcessReportListener(staff, consignData.trades2Consigned);
            if (ms != null && !ms.isEmpty()) {
                eventCenter.fireEvent(this, new EventInfo("aoxiang.order.process.report").setArgs(new Object[]{staff, OrderProcessReportAccess.ProcessStatus.CONFIRM.value, ms}), null);
            }

            List<Trade> sysTrades = new ArrayList<>();

            List<Long> dangkouSids = Lists.newArrayList();
            Map<String, List<Long>> tidSidsMap = new HashMap<>();
            for (Trade trade : consignData.trades2Consigned) {
                if (trade.isDangkouTrade()) {
                    dangkouSids.add(trade.getSid());
                }
                if (TradeUtils.isSplit(trade)) {
                    tidSidsMap.computeIfAbsent(trade.getTid(), k -> new ArrayList<>()).add(trade.getSid());
                }
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) || TradeUtils.isSplit(trade)) {
                    continue;
                }
                if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
                    sysTrades.add(trade);
                }
            }
            if (!dangkouSids.isEmpty()) {   //档口订单发货同步销货单
                eventCenter.fireEvent(this, new EventInfo(SaleListenerEnums.SALE_CONSIGN_SYNC).setArgs(new Object[]{staff.getId(), dangkouSids}), null);
            }
            if (!sysTrades.isEmpty()) {//发送事件给财务作电子发票处理
                eventCenter.fireEvent(this, new EventInfo("trade.status.change").setArgs(new Object[]{staff, sysTrades}), null);
            }

            if (!tidSidsMap.isEmpty() && consignData.splitConsignUploadSellerMemo) {
                eventCenter.fireEvent(this, new EventInfo("split.consign.upload.seller.memo").setArgs(new Object[]{staff, tidSidsMap}), null);
            }

            //发货完成交易订单商品成本价更新
            if (consignData.tradeConfig != null
                    && consignData.tradeConfig.getInteger(TradeExtendConfigsEnum.TRADE_ITEM_COST_CONFIG.getKey()) == 1
                    && consignData.tradeConfig.getInteger(TradeExtendConfigsEnum.OPEN_UPDATE_TRADE_COST.getKey()) == 1) {
                //https://gykj.yuque.com/entavv/xb9xi5/aw27l5#jgvPZ
                eventCenter.fireEvent(this, new EventInfo("trade.item.cost.calculate").setArgs(new Object[]{staff, TradeUtils.toSidList(consignData.trades2Consigned)}), null);
            }
            //奇门分销订单资金流水重算
            List<Trade> qimenFxTrade = consignData.trades2Consigned.stream().filter(trade -> TradeUtils.isQimenFxSource(trade) &&
                    (Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus())
                            || Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(qimenFxTrade)) {
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_QIMEN_FX_CASH_FLOW).setArgs(new Object[]{staff, 2, TradeUtils.toSidList(qimenFxTrade)}), null);
            }
            // 1688分销小站重算运费
            List<Trade> alibabaFxTrades = consignData.trades2Consigned.stream().filter(trade -> TradeUtils.isAlibabaFxTrade(trade) &&
                    (Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus())
                            || Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(trade.getSysStatus()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(alibabaFxTrades)) {
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_RECALC_POST_FEE).setArgs(new Object[]{staff, TradeUtils.toSidList(alibabaFxTrades)}), null);
            }
            //发货未称重覆盖订单实际体积
            if (consignData.tradeConfig != null
                    && Objects.equals(1, consignData.tradeConfig.getInteger("isCoverWeight"))) {
                List<Trade> needCoverTrades = Lists.newArrayListWithCapacity(consignData.trades2Consigned.size());
                for (Trade trade : consignData.trades2Consigned) {
                    if (TradeWeightUtils.isNeedCoverActualVolume(trade)) {
                        needCoverTrades.add(trade);
                    }
                }
                if (CollectionUtils.isNotEmpty(needCoverTrades)) {
                    eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_UPDATE_ACTUAL_VOLUME).setArgs(new Object[]{staff, needCoverTrades, 0}), null);
                }
            }
            // 供销发货后通知快卖通佣金结算
            List<Trade> gxTrades = consignData.trades2Consigned.stream().filter(trade -> TradeUtils.isGxOrMixTrade(trade)
                    && (
                    (trade.getTradeExt() != null
                            && Objects.equals(CommonConstants.PLAT_FORM_TYPE_SYS, TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "fxSource")))
                            || TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())
            )).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(gxTrades) && ConfigHolder.FX_GLOBAL_CONFIG.isUploadFxCommission(staff.getCompanyId())) {
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_COMMISSION_UPLOAD).setArgs(new Object[]{staff, TradeUtils.toSidList(gxTrades)}), null);
            }
        }
        if (!consignData.checkBalanceFailTradeMap.isEmpty()) {
            //奇门分销订单发货校验流水失败
            List<Long> checkBalanceFailSids = new ArrayList<>(consignData.checkBalanceFailTradeMap.keySet());
            eventCenter.fireEvent(this, new EventInfo("trade.qimen.fx.cash.flow.fail").setArgs(new Object[]{staff, checkBalanceFailSids}), null);
        }
    }

    private List<Trade> buildTrade4InnerConsignSuccess(List<Trade> trades) {
        List<Trade> list = new ArrayList<>(trades.size());
        for (Trade trade : trades) {
            TbTrade tempTrade = new TbTrade();
            tempTrade.setUserId(trade.getUserId());
            tempTrade.setSid(trade.getSid());
            tempTrade.setTid(trade.getTid());
            tempTrade.setOutSid(trade.getOutSid());
            tempTrade.setWarehouseId(trade.getWarehouseId());
            tempTrade.setCreated(trade.getCreated());
            tempTrade.setTemplateId(trade.getTemplateId());
            tempTrade.setLogisticsCompanyId(trade.getLogisticsCompanyId());
            tempTrade.setTemplateType(trade.getTemplateType());
            tempTrade.setTemplateName(trade.getTemplateName());
            tempTrade.setType(trade.getType());
            tempTrade.setConsignTime(trade.getConsignTime());
            tempTrade.setNetWeight(trade.getNetWeight());
            tempTrade.setSaleFee(trade.getSaleFee());
            tempTrade.setMergeSid(trade.getMergeSid());
            tempTrade.setMergeType(trade.getMergeType());
            tempTrade.setPayment(trade.getPayment());
            tempTrade.setItemNum(trade.getItemNum());
            tempTrade.setItemKindNum(trade.getItemKindNum());
            tempTrade.setConsignType(trade.getConsignType());
            tempTrade.setActualPostFee(trade.getActualPostFee());
            tempTrade.setReceiverCountry(trade.getReceiverCountry());
            tempTrade.setReceiverState(trade.getReceiverState());
            tempTrade.setReceiverCity(trade.getReceiverCity());
            tempTrade.setReceiverDistrict(trade.getReceiverDistrict());
            tempTrade.setReceiverAddress(trade.getReceiverAddress());
            tempTrade.setReceiverName(trade.getReceiverName());
            tempTrade.setReceiverMobile(trade.getReceiverMobile());
            tempTrade.setReceiverPhone(trade.getReceiverPhone());
            tempTrade.setShortId(trade.getShortId());
            tempTrade.setLogisticsCompanyId(trade.getLogisticsCompanyId());
            tempTrade.setBelongType(trade.getBelongType());
            tempTrade.setConvertType(trade.getConvertType());
            tempTrade.setSourceId(trade.getSourceId());
            tempTrade.setDestId(trade.getDestId());
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                TbOrder tempOrder = buildOrder4InnerConsignSuccess(order);
                if (order.getSuits() != null) {
                    tempOrder.setSuits(new ArrayList<>(order.getSuits().size()));
                    for (Order son : order.getSuits()) {
                        tempOrder.getSuits().add(buildOrder4InnerConsignSuccess(son));
                    }
                }
                tempTrade.getOrders().add(tempOrder);
            }
            list.add(tempTrade);
        }
        return list;
    }

    private TbOrder buildOrder4InnerConsignSuccess(Order order) {
        TbOrder tempOrder = new TbOrder();
        tempOrder.setId(order.getId());
        tempOrder.setSid(order.getSid());
        tempOrder.setTid(order.getTid());
        tempOrder.setOid(order.getOid());
        tempOrder.setItemSysId(order.getItemSysId());
        tempOrder.setSkuSysId(order.getSkuSysId());
        tempOrder.setSysOuterId(order.getSysOuterId());
        tempOrder.setSysSkuPropertiesName(order.getSysSkuPropertiesName());
        tempOrder.setSysSkuRemark(order.getSysSkuRemark());
        tempOrder.setSysItemRemark(order.getSysItemRemark());
        tempOrder.setNum(order.getNum());
        return tempOrder;
    }

    /**
     * 构造系统发货后需要更新的trade与order
     */
    void buildUpdateTrade(Staff staff, Trade trade, ConsignData consignData) {
        trade.setConsignTime(consignTime);
        trade.setSysStatus(getSysStatus(trade));
        trade.setSysConsigned(1);
        trade.setIsExcep(0);
        Trade updateTrade = TradeBuilderUtils.builderUpdateTrade(trade);
        // 优先构建order
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                continue;
            }
            buildUpdateOrder(staff, order, trade, consignData);
        }
        updateTrade.setSid(trade.getSid());
        updateTrade.setConsignTime(trade.getConsignTime());
        updateTrade.setSysStatus(trade.getSysStatus());
        updateTrade.setSysConsigned(trade.getSysConsigned());
        updateTrade.setIsDeduct(trade.getIsDeduct());
        updateTrade.setIsExcep(0);
        if (TradeUtils.isFxOrMixTrade(trade)) {
            updateTrade.addOpV(OpVEnum.TRADE_FX);
        }
        // 已发货的订单清除所有异常
       /* TradeExceptUtils.clearAllExcept(staff,trade);
        TradeExceptUtils.clearAllExcept(staff,updateTrade);*/
        updateTrade.setActualPostFee(trade.getActualPostFee());
        if (SendType.DUMMY == consignType && !trade.isDangkouTrade()) {//无需物流发货需清空物流信息, 档口订单无需清空
            updateTrade.setOutSid("");
            updateTrade.setTemplateId(0L);
            updateTrade.setTemplateType(0);
            updateTrade.setLogisticsCompanyId(0L);
            updateTrade.setTheoryPostFee(0d);
            updateTrade.setActualPostFee("0");
        }
        //发货后取消的异常
        List<ExceptEnum> exceptEnums = Lists.newArrayList(ExceptEnum.SUITE_CHANGE, ExceptEnum.ITEM_PROCESS, ExceptEnum.FX_AMBIGUITY,
                ExceptEnum.FX_REPULSE, ExceptEnum.FX_UNAUDIT, ExceptEnum.RELATION_CHANGED, ExceptEnum.ITEM_SHUTOFF, ExceptEnum.COD_REPEAT);
        for (ExceptEnum exceptEnum : exceptEnums) {
            TradeExceptUtils.updateExcept(staff, trade, exceptEnum, 0L);
            TradeExceptUtils.updateExcept(staff, updateTrade, exceptEnum, 0L);
        }

        if ((TradeUtils.isFxSource(trade) || null != consignData.tradeCombineParcel || "5".equals(consignData.operateType)) && trade.getOutSid() != null && trade.getOutSid().length() > 0) {
            //处理OutSid
            updateTrade.setOutSid(trade.getOutSid());
        }
        //系统发货的做一些更新标签的操作
        //系统订单取消即将超时标签
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())
                && StringUtils.contains(trade.getTagIds(), String.valueOf(SystemTags.TAG_OVERTIME.getId()))){
            consignData.removeTradeLabelMap.computeIfAbsent(SystemTags.TAG_OVERTIME, k -> new HashSet<>()).add(trade);
            // if (trade.tag(SystemTags.TAG_OVERTIME.getId(), false)) {
            //     updateTrade.setTagIds(trade.getTagIds());
            // }
            // eventCenter.fireEvent(this, new EventInfo("trade.system.tags")
            //         .setArgs(new Object[]{staff, TradeUtils.toSid(trade), Lists.newArrayList(SystemTags.TAG_OVERTIME), OpEnum.TAG_UPDATE, false}), null);
        }
        // 系统订单，已完成状态，更新end_time时间
        if (Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_SYS)
                && Objects.equals(getSysStatus(trade), Trade.SYS_STATUS_FINISHED)) {
            updateTrade.setEndTime(consignTime);
        }
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.CAI_GOU_TRADE_EXCEPT)) {
            TradeExceptUtils.updateExcept(staff, updateTrade, ExceptEnum.CAI_GOU_TRADE_EXCEPT, 0L);
            updateTrade.getOperations().put(OpEnum.EXCEPT_UPDATE, String.format("发货后取消【%s】异常", ExceptEnum.CAI_GOU_TRADE_EXCEPT.getChinese()));
            consignData.resumeSids.add(updateTrade.getSid());
        }
        // 奇门分销更新运费
        if (TradeUtils.isQimenFxSource(trade) && trade.getPostFee() != null) {
            updateTrade.setPostFee(trade.getPostFee());
        }
        consignData.updateTrades.add(updateTrade);

    }

    private void buildUpdateOrder(Staff staff, Order order, Trade trade, ConsignData consignData) {
        order.setSysStatus(trade.getSysStatus());
        order.setConsignTime(consignTime);
        order.setSysConsigned(1);
        Order updateOrder = OrderBuilderUtils.builderUpdateOrderWithV(order);
        updateOrder.setId(order.getId());
        updateOrder.setSid(order.getSid());
        updateOrder.setSysStatus(order.getSysStatus());
        updateOrder.setConsignTime(order.getConsignTime());
        updateOrder.setSysConsigned(order.getSysConsigned());
        // 发货后取消的异常
        List<ExceptEnum> exceptEnums = Lists.newArrayList(ExceptEnum.RELATION_CHANGED, ExceptEnum.ITEM_SHUTOFF, ExceptEnum.SUITE_CHANGE, ExceptEnum.ITEM_PROCESS,ExceptEnum.SMALL_REFUND_EXCEPT);
        for (ExceptEnum exceptEnum : exceptEnums) {
            OrderExceptUtils.updateExceptOrder(staff, order, exceptEnum, 0L);
            OrderExceptUtils.updateExceptOrder(staff, updateOrder, exceptEnum, 0L);
        }

        // KMERP-138988: 系统订单，已完成状态，更新end_time时间
        if (Objects.equals(trade.getSource(), CommonConstants.PLAT_FORM_TYPE_SYS)
                && Objects.equals(trade.getSysStatus(), Trade.SYS_STATUS_FINISHED)) {
            updateOrder.setEndTime(consignTime);
        }
        consignData.updateOrders.add(updateOrder);
        if (order.getSuits() != null) {
            for (Order son : order.getSuits()) {
                buildUpdateOrder(staff, son, trade, consignData);
            }
        }
    }

    /**
     * 计算系统发货后订单系统状态，平台订单为已发货，系统订单为已完成
     */
    private String getSysStatus(Trade trade) {
        //如果是系统和唯品会jit订单，直接交易完成
        if ((CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())) || (CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSource()) && (trade.getStatus() == null || !trade.getStatus().contains("vipjit")))) {//唯品会git订单直接返回交易成功
            return Trade.SYS_STATUS_FINISHED;
        }
        return Trade.SYS_STATUS_FINISHED.equals(trade.getUnifiedStatus()) ? Trade.SYS_STATUS_FINISHED : Trade.SYS_STATUS_SELLER_SEND_GOODS;
    }

}

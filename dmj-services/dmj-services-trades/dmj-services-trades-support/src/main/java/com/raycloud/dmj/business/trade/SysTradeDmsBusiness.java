package com.raycloud.dmj.business.trade;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.*;
import com.raycloud.dmj.*;
import com.raycloud.dmj.business.audit.AuditFxBusiness;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.fx.*;
import com.raycloud.dmj.business.modify.*;
import com.raycloud.dmj.business.operate.PayAmountCalculateBusiness;
import com.raycloud.dmj.business.order.OrderModifyLogBusiness;
import com.raycloud.dmj.business.warehouse.WarehouseAllocateBusiness;
import com.raycloud.dmj.dms.domain.dto.DmsSupplierForDisConfigDto;
import com.raycloud.dmj.dms.domain.finance.DmsCashFlowTypeEnum;
import com.raycloud.dmj.dms.request.*;
import com.raycloud.dmj.dms.response.*;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.params.QueryItemDetailParams;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.utils.PaymentUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.filter.support.DefaultTemplateFilter;
import com.raycloud.dmj.services.pt.smart_match.IExpressMatchEngine;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.trades.filter.TradeFilterException;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.*;
import com.raycloud.ec.api.EventInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/***
 * 分销业务处理
 */
@Service
public class SysTradeDmsBusiness {

    @Resource
    IDmsTradeService dmsTradeService;
    @Resource
    AuditFxBusiness auditFxBusiness;
    @Resource
    IStaffService staffService;
    @Resource
    OrderModifyLogBusiness orderModifyLogBusiness;
    @Resource
    protected TradeCalculateTheoryPostFeeBusiness tradeCalculateTheoryPostFeeBusiness;
    @Resource
    ILockService lockService;
    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource
    ITradeTraceService tradeTraceService;
    @Resource
    FxCashFlowBusiness fxCashFlowBusiness;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    WarehouseAllocateBusiness warehouseAllocateBusiness;
    @Resource
    IExpressMatchEngine expressMatchEngine;
    @Resource
    DefaultTemplateFilter defaultTemplateFilter;
    @Resource
    FxBusiness fxBusiness;
    @Resource
    IEventCenter eventCenter;
    @Resource
    FxCalculateComissionConfigBusiness fxCalculateComissionConfigBusiness;
    @Resource
    ITradeConfigNewService tradeConfigNewService;
    @Resource
    PayAmountCalculateBusiness payAmountCalculateBusiness;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    ITradeUpdateService tradeUpdateService;
    @Resource
    IOrderStockService orderStockService;
    @Resource
    IItemServiceDubbo itemServiceDubboImpl;

    private final OrderCopier<Order, Order> orderCopier = new OrderCopier<>();
    private final Logger logger = Logger.getLogger(this.getClass());

    public Order buildUpdateOrder(Staff staff, Order order, DmsData dmsData) {
        Order update = orderCopier.copy(order, new TbOrder());
        update.setId(order.getId());
        update.setBelongType(0);
        update.setConvertType(0);
        update.setIsCancelDistributorAttribute(1);
        update.setDestId(0L);
        update.setSourceId(0L);
        //如果是系统商品，没有num_iid,sku_id，当取消分销属性的时候不能用这些id去匹配，要用商家编码去匹配
        if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
            update.setUseOuterIdMatch(true);
            update.setItemSysId(order.getItemSysId());
            update.setSkuSysId(order.getSkuSysId());
        } else {
            update.setUseOuterIdMatch(false);
            update.setItemSysId(-1L);
            update.setSkuSysId(-1L);
        }
        OrderExceptUtils.setStockStatus(staff, update, Trade.STOCK_STATUS_EMPTY);
        update.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
        update.setStockNum(0);
        update.setInsufficientCanceled(0);
        if (order.getSuits() != null) {
            for (Order son : order.getSuits()) {
                buildUpdateOrder(staff, son, dmsData);
            }
        }
        dmsData.updateOrders.add(update);
        return update;
    }


    /**
     * 过滤分销订单并且设置值
     * @param staff
     * @param tradeList
     * @param type
     */
    public void filterFxTrade(Staff staff, List<Trade> tradeList, int type) {
        for (Trade trade : tradeList) {
            TradeUtils.applyStockForFxTrade(staff, trade);
            //有模版要清空,这里可能有问题 不能直接清
            if (type == 1 && (Objects.nonNull(trade.getTemplateId()) && trade.getTemplateId() > 0L)) {
                trade.setTemplateId(-1L);
                trade.setLogisticsCompanyId(0L);
                trade.setTemplateType(0);
                trade.setOutSid("");
            }
            TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfigService.get(staff));
            //平台分销订单不取消商品未匹配异常
            if (TradeUtils.isPlatformFxTrade(trade)) {
                continue;
            }
            //这里计算异常,如果是商品未匹配的异常
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            orders.forEach(order -> {
                order.setOldItemSysId(order.getItemSysId());
                order.setItemSysId(1L);
            });
            TradeIOUtils.fillTradeExcep(staff, trade);
            orders.forEach(order -> order.setItemSysId(order.getOldItemSysId()));

        }
    }


    /**
     * 计算供销订单实付金额信息
     * 订单实付=商品实付+运费收入-订单维度优惠
     * 商品实付调用商品分销价计算出，运费收入调用运费规则计算出，订单维度优惠调用佣金规则(选择现结类型的)计算出
     * @param trades
     */
    public void caculateGxPayment(Staff staff, List<Trade> trades) {
        DmsSupplierForDisConfigDto supplierForDisConfig = dmsTradeService.queryDmsSupplierConfig(staff.getCompanyId());
        boolean enablePreShipmentCommissionRefundStrategy = supplierForDisConfig != null &&
                supplierForDisConfig.getPreShipmentCommissionRefundStrategy() != null &&
                supplierForDisConfig.getPreShipmentCommissionRefundStrategy() == 1;
        try {
            Map<Long, Boolean> needCalculateCommissionTradeMap = fxCalculateComissionConfigBusiness.fillNeedCalculateCommissionByGxTrade(staff, trades, enablePreShipmentCommissionRefundStrategy);
            List<DmsCalculateTradeCommissionRequest> commissionRequest = Lists.newArrayListWithCapacity(trades.size());
            HashMap<String, Trade> mapTrades = new HashMap<>();
            trades.forEach(trade -> {
                if (!(TradeUtils.isGxOrMixTrade(trade) || TradeUtils.isAlibabaFxTrade(trade))) {
                    return;
                }
                List<Order> validOrders = new ArrayList<>();
                BigDecimalWrapper payment = new BigDecimalWrapper();
                for (Order order : TradeUtils.getOrders4Trade(trade)) {
                    if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()) || OrderUtils.isOtherErpConsigned(order)) {
                        continue;
                    }
                    validOrders.add(order);
                    payment.add(MathUtils.multiply(order.getPrice(), order.getNum()));
                }
                //临时赋值，后面会进行覆盖
                trade.setPayment(payment.getString());
                trade.setPayAmount(payment.getString());

                boolean needCalculateCommission = !enablePreShipmentCommissionRefundStrategy || Boolean.TRUE.equals(needCalculateCommissionTradeMap.get(trade.getSid()));
                if (CollectionUtils.isNotEmpty(validOrders) && (needCalculateCommission || TradeUtils.isAlibabaFxTrade(trade))) {
                    mapTrades.put(trade.getSid().toString(), trade);
                    DmsCalculateTradeCommissionRequest request = new DmsCalculateTradeCommissionRequest();
                    request.setDistributorCompanyId(trade.getSourceId());
                    request.setFreightCost(Double.parseDouble(trade.getPostFee() == null ? "0" : trade.getPostFee()));
                    request.setGoodsCost(Double.parseDouble(trade.getPayment() == null ? "0" : trade.getPayment()));
                    request.setSupplierCompanyId(trade.getCompanyId());
                    request.setSysOrderNumber(trade.getSid().toString());
                    request.setTradeTagIds(trade.getTagIds());
                    if (CollectionUtils.isNotEmpty(validOrders)) {
                        request.setOuterIds(validOrders.stream().map(Order::getSysOuterId).filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
                        List<DmsCalculateTradeCommissionRequest.OrderNum> list = Lists.newArrayList();
                        //佣金查询支持商品数量计算
                        for (Order order : validOrders) {
                            if (StringUtils.isNotEmpty(order.getSysOuterId())) {
                                list.add(DmsCalculateTradeCommissionRequest.OrderNum.builder()
                                        .num(order.getNum())
                                        .sysOuterId(order.getSysOuterId())
                                        .build());
                            }
                        }
                        request.setOrderNumList(list);
                    }
                    commissionRequest.add(request);
                } else {
                    trade.addOpV(OpVEnum.GX_TRADE_NOT_RE_CALCULATE_COMMISSION);
                }

            });
            if (CollectionUtils.isNotEmpty(commissionRequest)) {
                List<List<DmsCalculateTradeCommissionRequest>> partition = Lists.partition(commissionRequest, 10);
                for (List<DmsCalculateTradeCommissionRequest> requests : partition) {
                    List<DmsCalculateTradeCommissionResponse> responses = dmsTradeService.calculateTradeCommission(requests);
                    if (!responses.isEmpty()) {
                        responses.forEach(dmsCalculateTradeCommissionResponse -> {
                            Trade trade = mapTrades.get(dmsCalculateTradeCommissionResponse.getSysOrderNumber());
                            if (trade != null) {
                                //订单实付=商品实付+运费收入-订单维度优惠
                                BigDecimalWrapper realPayment = new BigDecimalWrapper(trade.getPayment()).add(trade.getPostFee());
                                //佣金收入
                                Double commission = dmsCalculateTradeCommissionResponse.getCommission();
                                realPayment.subtract(commission);
                                if (PaymentLogBuilder.isPrintMoneyDetailLog(staff.getCompanyId()) && !MathUtils.equalsZero(commission)) {
                                    logger.debug(new PaymentLogBuilder(staff).appendTrade(trade).append("供销实付",realPayment.getString()).eq().append("商品payment总和",trade.getPayment()).add().append("运费",trade.getPostFee()).sub().append("佣金",commission).toString());
                                }
                                //订单实付=商品实付+运费收入-订单维度优惠
                                trade.setDiscountFee(commission.toString());
                                trade.setPayment(realPayment.getString());
                                trade.setPayAmount(realPayment.getString());
                                if (dmsCalculateTradeCommissionResponse.getDmsCommissionRuleDomain() != null && Objects.equals(3, dmsCalculateTradeCommissionResponse.getDmsCommissionRuleDomain().getSettlementType())) {
                                    TradeExtUtils.setExtraFieldValue(trade.getTradeExt(), TradeExtraFieldEnum.GX_SETTLEMENT_TYPE.getField(), 3);
                                }
                            }
                        });
                    }
                }
            }

            for (Trade trade : trades) {
                payAmountCalculateBusiness.sharePayAmount(staff, trade, null);
                PaymentUtils.calculateTrade(trade);//重算金钱
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("计算供销订单实付金额信息失败,sids=%s", TradeUtils.toSidList(trades))), e);
        }

    }

    /**
     * 计算奇门供销订单实付金额信息
     * 订单实付= 商品实付 + 运费收入 - 订单维度优惠
     * 商品实付:调用商品payment
     * 运费收入:调取订单的运费金额（调用分销运费规则计算得出）
     * 订单维度优惠: 取订单的优惠金额
     * @param staff
     * @param trades 奇门订单列表
     */
    public void calculateQiMenGxPayment(Staff staff, List<Trade> trades) {
        trades.forEach(t -> {
            if (!TradeUtils.isQimenFxSource(t)) {
                return;
            }
            List<Order> orders = TradeUtils.getOrders4Trade(t);
            BigDecimalWrapper payment = new BigDecimalWrapper();

            for (Order order : orders) {
                payment.add(order.getPayment());
            }
            //订单实付=商品实付+运费收入-订单维度优惠
            BigDecimalWrapper realPayment = new BigDecimalWrapper(payment.getString()).add(t.getPostFee()).subtract(t.getDiscountFee());
            t.setPayment(realPayment.getString());
            t.setPayAmount(realPayment.getString());
            //Order分摊PayAmount
            payAmountCalculateBusiness.sharePayAmount(staff, t, null);
            //重算金钱
            PaymentUtils.calculateTrade(t);
        });
    }


    /**
     * 计算实付金额并生成流水
     * @param staff
     * @param trades
     */
    public void caculateGxPayments(Staff staff, List<Trade> trades) {
        List<Trade> waitSellerSendLists = trades.stream().filter(trade -> TradeStatusUtils.isWaitSellerSend(trade.getSysStatus()) && TradeUtils.isGxOrMixTrade(trade)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(waitSellerSendLists)) {
            caculateGxPayment(staff, waitSellerSendLists);
            Map<String, TradeConfigNew> configMap = tradeConfigNewService.getMap(staff, CashFlowUtils.REISSUE_OR_CHANGE_ITEM_FLOW_ENUMS);
            List<Trade> needException = new ArrayList<>();
            FxLogBuilder log = new FxLogBuilder(staff, FxLogBuilder.ROLE_GX).append("补发换货单不用扣流水不标记异常:");
            for (Trade trade : waitSellerSendLists) {
                //补发换货单不用扣流水 这里直接不标记异常
                if (CashFlowUtils.isReissueOrChangeItemPassCalFlow(staff, trade, configMap)) {
                    log.append(trade.getSid()).append(",");
                    if (TradeUtils.isReissue(trade)) {
                        trade.getOperations().put(OpEnum.TRADE_PAY_DISTRIBUTOR, "补发订单，开启了不计算流水的配置，该订单不扣流水");
                    } else {
                        trade.getOperations().put(OpEnum.TRADE_PAY_DISTRIBUTOR, "换货订单，开启了不计算流水的配置，该订单不扣流水");
                    }
                } else {
                    needException.add(trade);
                }
            }
            if (needException.size() != waitSellerSendLists.size()) {
                logger.debug(log.toString());
            }

            needException = needException.stream().filter(TradeUtils::ifNeedAddWaitPayExcept).collect(Collectors.toList());

            logger.debug(LogHelper.buildLog(staff, String.format("[先标记付款异常=%s]", TradeUtils.toSidList(needException))));
            //先标记未付款异常
            for (Trade trade : needException) {
                TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_WAITPAY, 1L);
                trade.setIsExcep(1);
            }
        }
        waitSellerSendLists = trades.stream().filter(trade -> TradeStatusUtils.isWaitSellerSend(trade.getSysStatus()) && TradeUtils.isAlibabaFxTrade(trade)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(waitSellerSendLists)) {
            caculateGxPayment(staff, waitSellerSendLists);
        }

    }

    public List<Trade> handGxTradeExpressCashFlow(Staff staff, Long... sids) {
        try {
            List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, sids);
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("供销订单资金流水重算trades=%s", JSON.toJSONString(trades))));
            }
            DmsSupplierForDisConfigDto dmsConfig = dmsTradeService.queryDmsSupplierConfig(staff.getCompanyId());
            return handGxTradeExpressCashFlow(staff, trades, dmsConfig.getPostFeeUseActualWeight());
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "供销订单修改运费/模版生成流水失败:" + e.getMessage()), e);
        }
        return Collections.emptyList();
    }

    public void handGxTradeWeightCashFlow(Staff staff, Long... sids) {
        try {
            DmsSupplierForDisConfigDto dmsConfig = dmsTradeService.queryDmsSupplierConfig(staff.getCompanyId());
            if (dmsConfig.getPostFeeUseActualWeight() == null || !dmsConfig.getPostFeeUseActualWeight()) {
                return;
            }
            List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, sids);
            resumeCashFlow(staff, trades, true);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "供销订单修改运费/模版生成流水失败:" + e.getMessage()), e);
        }
    }

    /**
     * 快递模板变化供销资金流水重算
     * @param staff
     * @param trades
     */
    public List<Trade> handGxTradeExpressCashFlow(Staff staff, List<Trade> trades, Boolean postFeeUseActualWeight) {
        if (CollectionUtils.isNotEmpty(trades)) {
            List<Trade> needRemoveTrades = new ArrayList<>();
            List<Integer> modifyTypeList = Lists.newArrayList(OrderModifyLogTypeEnum.GX_ADDRESS_CHANGE.getType(), OrderModifyLogTypeEnum.EXPRESS_TEMPLATE_REPLACE.getType());
            List<OrderModifyLog> orderModifyLogs = orderModifyLogBusiness.queryByModifyTypeList(staff, OrderUtils.toIdList(TradeUtils.getOrders4Trade(trades)), modifyTypeList);
            if (CollectionUtils.isEmpty(orderModifyLogs)) {
                return Collections.emptyList();
            }
            Map<Long, OrderModifyLog> modifyLogMap = orderModifyLogs.stream().collect(Collectors.toMap(OrderModifyLog::getSid, a -> a, (v1, v2) -> v1));
            for (Trade trade : trades) {
                if (!TradeUtils.needCalcGxPostFee(trade)) {
                    needRemoveTrades.add(trade);
                }
                if (!modifyLogMap.containsKey(trade.getSid())) {
                    needRemoveTrades.add(trade);
                }
            }
            if (!needRemoveTrades.isEmpty()) {
                trades.removeAll(needRemoveTrades);
            }
            return resumeCashFlow(staff, trades, postFeeUseActualWeight);
        }
        return Collections.emptyList();
    }

    /**
     * 手动修改过运费的订单不自动重算
     * @param staff
     * @param trades
     */
    public List<Trade> resumeCashFlow(Staff staff, List<Trade> trades, Boolean postFeeUseActualWeight) {
        List<OrderModifyLog> orderModifyLogs = orderModifyLogBusiness.query(staff, OrderUtils.toIdList(TradeUtils.getOrders4Trade(trades)), OrderModifyLogTypeEnum.GX_POST_FEE_MODIFY.getType());
        Map<Long, OrderModifyLog> modifyLogMap = orderModifyLogs.stream().collect(Collectors.toMap(OrderModifyLog::getSid, a -> a, (v1, v2) -> v1));
        trades.removeIf(trade -> !TradeUtils.needCalcGxPostFee(trade) || modifyLogMap.containsKey(trade.getSid()));
        return resumeCashFlow(staff, trades, true, null, postFeeUseActualWeight);
    }

    public List<Trade> resumeCashFlow(Staff staff, List<Trade> trades, boolean resumeFlag, Double freight, Boolean postFeeUseActualWeight) {
        Map<Long, BigDecimal> resumeCashFlowMap = new HashMap<>(), consumeCashFlowMap = new HashMap<>();
        List<TradeTrace> tradeTraces = new ArrayList<>();
        Date matchTime = new Date();
        Map<Long, String> oldPostFeeMap = trades.stream().collect(Collectors.toMap(Trade::getSid, trade -> StringUtils.isEmpty(trade.getPostFee()) ? "0" : trade.getPostFee(), (v1, v2) -> v1));
        List<Trade> result = lockService.locks(tradeLockBusiness.getERPLocks(staff, TradeUtils.toSids(trades)), () -> {
            //供销订单邮费计算
            if (resumeFlag) {
                tradeCalculateTheoryPostFeeBusiness.recalGxPostFee(staff, trades, postFeeUseActualWeight);
            } else {
                trades.get(0).setPostFee(freight == null ? "0" : freight.toString());
            }
            List<Trade> updateTrades = Lists.newArrayList();
            List<Order> updateOrders = Lists.newArrayList();
            Map<String, TradeConfigNew> configMap = tradeConfigNewService.getMap(staff, CashFlowUtils.REISSUE_OR_CHANGE_ITEM_FLOW_ENUMS);
            FxLogBuilder logBuilder = new FxLogBuilder(staff, FxLogBuilder.ROLE_GX).append("供销金额重算 ");
            for (Trade trade : trades) {
                if (TradeUtils.isGxOrMixTrade(trade) && TradeUtils.isSplit(trade)) {
                    continue;
                }
                Trade updateTrade = TradeBuilderUtils.builderUpdateTrade(trade);
                updateTrade.setSid(trade.getSid());
                String oldPayment = trade.getPayment();
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                BigDecimal price = new BigDecimal("0");
                for (Order order : orders) {
                    price = price.add(new BigDecimal(order.getPrice() == null ? "0" : order.getPrice()));
                }
                //重算金钱 这里会把运费重新算到payment里去
                PaymentUtils.calculateTrade(trade);
                String newPayment = trade.getPayment();
                updateTrade.setPayment(newPayment);
                updateTrade.setPayAmount(newPayment);
                trade.setPayAmount(updateTrade.getPayAmount());
                updateTrade.setPostFee(trade.getPostFee());
                updateTrades.add(updateTrade);
                payAmountCalculateBusiness.sharePayAmount(staff, trade, null);
                orders.forEach(order -> {
                    updateOrders.add(payAmountCalculateBusiness.buildUpdateOrder(order));
                    if (CollectionUtils.isNotEmpty(order.getSuits())) {
                        for (Order single : order.getSuits()) {
                            updateOrders.add(payAmountCalculateBusiness.buildUpdateOrder(single));
                        }
                    }
                });
                //不存在付款异常
                if (!TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_WAITPAY)) {
                    BigDecimal cashFlow = MathUtils.subtract(newPayment, oldPayment);
                    if (MathUtils.equalsZero(cashFlow)) {
                        logBuilder.group("金额无变更", trade.getSid());
                    } else {
                        //不存在付款异常
                        if (!TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_WAITPAY)) {
                            if (CashFlowUtils.isReissueOrChangeItemPassCalFlow(staff, trade, configMap)) {
                                logBuilder.group("补发换货单不计流水", trade.getSid());
                            } else {
                                logBuilder.group("金额变更", String.format("{sid:%s %s ->%s}", trade.getSid(), oldPayment, newPayment));
                                if (cashFlow.compareTo(BigDecimal.ZERO) > 0) {
                                    //要冲负，要多扣
                                    consumeCashFlowMap.put(trade.getSid(), cashFlow);
                                } else {
                                    //要冲正,要少扣
                                    resumeCashFlowMap.put(trade.getSid(), cashFlow);
                                }
                            }
                        }
                    }
                }
                String orgiPostFee = oldPostFeeMap.get(trade.getSid());
                String postFee = trade.getPostFee();
                if (!MathUtils.equals(orgiPostFee, postFee) || resumeFlag) {
                    String action = "修改运费";
                    String content = "修改运费为:" + orgiPostFee + "->" + postFee;
                    content = content + (resumeFlag && StringUtils.isNotBlank(trade.getGxFreightRuleName()) ? ",匹配供销运费规则名称:" + trade.getGxFreightRuleName() : "");
                    TradeTrace tradeTrace = TradeTraceUtils.createTradeTraceWithTrade(staff, trade, action, staff.getName(), matchTime, content);
                    tradeTraces.add(tradeTrace);
                }
            }

            logBuilder.multiPrintDebug(logger);

            if (!updateTrades.isEmpty()) {
                tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
            }
            return updateTrades;
        });
        //记录oplog
        if (CollectionUtils.isNotEmpty(tradeTraces)) {
            tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        }
        //根据实付，重新计算流水
        //补发单不重算流水
        fxCashFlowBusiness.resumeCashFlowByPayment(staff, resumeCashFlowMap, consumeCashFlowMap);
        //清除模板修改记录
        orderModifyLogBusiness.removeLogByTypeList(staff, OrderUtils.toIdList(TradeUtils.getOrders4Trade(trades)), Lists.newArrayList(OrderModifyLogTypeEnum.EXPRESS_TEMPLATE_REPLACE.getType(), OrderModifyLogTypeEnum.GX_ADDRESS_CHANGE.getType()));
        //供销订单修改运费,同步分销
        fxBusiness.syncFxData(staff, trades);
        return result;
    }

    public static class DmsData {
        //成功订单
        public List<Trade> successTrades = new ArrayList<>();
        //错误订单
        public List<Trade> errorTrades = new ArrayList<>();
        //原始订单
        public List<Trade> originTrades = new ArrayList<>();
        //更新的订单
        public List<Trade> updateTrades = new ArrayList<>();
        //更新的Order
        public List<Order> updateOrders = new ArrayList<>();
        //系统订单
        //平台订单
        public List<Trade> platTrades = new ArrayList<>();

    }

    /**
     * 分销订单取消分销属性
     */
    public void fxTradeCancelDistributorAttribute(Staff staff, List<Trade> originTrades) {
        List<Trade> cancelWaybillCode = new ArrayList<>();
        for (Trade originTrade : originTrades) {
            if (StringUtils.isNotBlank(originTrade.getOutSid()) && originTrade.getTemplateId() != null && originTrade.getTemplateId() > 0){
                cancelWaybillCode.add(TradeUtils.buildHaveOutsidTrade(originTrade));
            }
            boolean ifQiMenTrade = TradeUtils.isQimenFxSource(originTrade);
            // 原单的异常分销商待付款
            boolean hasFxWaitPayExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.FX_WAITPAY);
            boolean gxItemChangeExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
            for (Order originOrder : TradeUtils.getOrders4Trade(originTrade)) {
                originOrder.setSourceId(0L);
                originOrder.setDestId(0L);
                originOrder.setConvertType(ifQiMenTrade ? 3 : 0);
                originOrder.setBelongType(ifQiMenTrade ? 2 : 0);
                OrderExceptUtils.setStockStatus(staff, originOrder, Trade.STOCK_STATUS_EMPTY);
                originOrder.setStockNum(0);
                if (originOrder.getSuits() != null) {
                    for (Order suit : originOrder.getSuits()) {
                        suit.setSourceId(0L);
                        suit.setDestId(0L);
                        suit.setConvertType(ifQiMenTrade ? 3 : 0);
                        suit.setBelongType(ifQiMenTrade ? 2 : 0);
                        OrderExceptUtils.setStockStatus(staff, originOrder, Trade.STOCK_STATUS_EMPTY);
                        suit.setStockNum(0);
                    }
                }
            }
            TradeUtils.setSourceId(originTrade, 0L);
            TradeExceptUtils.clearCustomExcept(staff, originTrade);
            TradeExceptUtils.clearExcep(staff, originTrade);
            originTrade.setDestId(0L);
            originTrade.setConvertType(ifQiMenTrade ? 3 : 0);
            originTrade.setBelongType(ifQiMenTrade ? 2 : 0);
            originTrade.removeV(16);
            originTrade.removeV(TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID);
            originTrade.setWarehouseId(null);
            originTrade.setIsExcep(0);
            //奇门订单，取消分销属性时，保留分销商未付款异常
            if (TradeUtils.isQimenFxSource(originTrade) && hasFxWaitPayExcept) {
                TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.FX_WAITPAY, 1L);
            }
            TradeExceptUtils.setStockStatus(staff, originTrade, Trade.STOCK_STATUS_EMPTY);
            TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.GX_ITEM_CHANGE_EXCEPT, gxItemChangeExcept);
            //取消分销属性，回收原始打印模版信息
            originTrade.setTemplateId(-1L);
            originTrade.setLogisticsCompanyId(0L) ;
            originTrade.setTemplateType(0);
            originTrade.setOutSid("");
        }
        doGxAndFxTradeCancelDistributorAttribute(staff, originTrades, 1);
        if (CollectionUtils.isNotEmpty(cancelWaybillCode)){
            eventCenter.fireEvent(this, new EventInfo("wlb.waybillCode.cancel").setArgs(new Object[]{staff, cancelWaybillCode, "取消分销属性"}), null);
        }
    }

    /**
     * 分销和供销订单取消分销属性
     */
    public void gxAndFxTradeCancelDistributorAttribute(Staff staff, List<Trade> originTrades) {
        lockService.locks(tradeLockBusiness.getERPLocks(staff, TradeUtils.toSids(originTrades)), () -> {
            Map<Long, List<Trade>> sourceIdMap = originTrades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
            List<Trade> fxTradeList = new ArrayList<>();
            for (Map.Entry<Long, List<Trade>> entry : sourceIdMap.entrySet()) {
                Long sourceId = entry.getKey();
                List<Trade> tradeList = entry.getValue();
                //获取分销信息
                Staff fxStaff = staffService.queryFullByCompanyId(sourceId);
                List<String> tids = TradeUtils.toTidList(tradeList);
                List<Long> fxSids = Lists.newArrayListWithCapacity(tids.size());
                tids.forEach(s -> fxSids.add(Long.parseLong(s)));
                List<Trade> fxTrades = tradeSearchService.queryBySidsContainMergeTrade(fxStaff, true, false, true, fxSids.toArray(new Long[0]));
                try {
                    defaultTemplateFilter.filterTrades(fxStaff, fxTrades);
                } catch (TradeFilterException e) {
                    throw new RuntimeException(e);
                }
                for (Trade trade : fxTrades) {
                    fillTemplateType(fxStaff, trade);
                }
                fxTradeList.addAll(fxTrades);
            }
            Map<Long, Trade> fxTradeMap = fxTradeList.stream().collect(Collectors.toMap(Trade::getSid, trade -> trade));
            for (Trade originTrade : originTrades) {
                boolean hasFxWaitPayExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.FX_WAITPAY);
                boolean hasFxUnAudit = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.FX_UNAUDIT);

                originTrade.setBelongType(2);
                originTrade.setWarehouseId(null);
                TradeExceptUtils.clearCustomExcept(staff, originTrade);
                TradeExceptUtils.clearExcep(staff, originTrade);
                originTrade.removeV(16);
                if (hasFxWaitPayExcept) {
                    TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.FX_WAITPAY, 1L);
                }
                if (hasFxUnAudit) {
                    TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.FX_UNAUDIT, 1L);
                    Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s标记[%s]异常", originTrade.getSid(), ExceptEnum.FX_UNAUDIT.getChinese())));
                }
                if (hasFxWaitPayExcept) {
                    TradeExceptUtils.updateExcept(staff, originTrade, ExceptEnum.GX_ITEM_CHANGE_EXCEPT, 1L);
                }
                if (fxTradeMap.containsKey(Long.valueOf(originTrade.getTid()))) {
                    Trade fxTrade = fxTradeMap.get(Long.valueOf(originTrade.getTid()));
                    originTrade.setExpressCompanyId(fxTrade.getExpressCompanyId());
                    originTrade.setWlbTemplateType(fxTrade.getWlbTemplateType());
                }
                for (Order originOrder : TradeUtils.getOrders4Trade(originTrade)) {
                    originOrder.setBelongType(2);
                    OrderExceptUtils.setStockStatus(staff, originOrder, Trade.STOCK_STATUS_EMPTY);
                    originOrder.setStockNum(0);
                    if (originOrder.getSuits() != null) {
                        for (Order suit : originOrder.getSuits()) {
                            OrderExceptUtils.setStockStatus(staff, suit, Trade.STOCK_STATUS_EMPTY);
                            suit.setStockNum(0);
                        }
                    }
                }
                TradeExceptUtils.setStockStatus(staff, originTrade, Trade.STOCK_STATUS_EMPTY);
            }
            doGxAndFxTradeCancelDistributorAttribute(staff, originTrades, null);
            return null;
        });
    }

    /**
     * 取消分销属性，后续资源处理
     * @param staff 职工信息
     * @param originTrades 取消分销属性的订单（包括合单子单）
     * @param cancelType 取消分销属性操作
     * null： 分销且供销订单，取消分销属性
     * 1：分销订单，取消分销属性
     * 2：平台分销订单取消分销属性
     */
    public void doGxAndFxTradeCancelDistributorAttribute(Staff staff, List<Trade> originTrades, Integer cancelType) {
        if (CollectionUtils.isEmpty(originTrades)) {
            return;
        }
        //匹配仓库
        warehouseAllocateBusiness.matchWarehouse(staff, originTrades);
        //匹配快递模板
        expressMatchEngine.match(staff, originTrades);
        //申请库存
        orderStockService.applyTradeStockLocal(staff, originTrades);
        //更新金额
        if (Integer.valueOf(1).equals(cancelType)) {
            recalculatePrice(staff, originTrades);
        }
        //创建修改数据
        List<Order> updateOrders = new ArrayList<>();
        List<Trade> exceptTrades = originTrades.stream().filter(trade -> TradeExceptUtils.isExcept(staff, trade)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(exceptTrades)) {
            Logs.debug(LogHelper.buildLog(staff, String.format("取消分销属性时会取消excep，exceptIds 的异常 %s", TradeUtils.toSidList(exceptTrades))));
        }
        List<Trade> updateTrades = createUpdateTrade(staff, originTrades, updateOrders, cancelType);
        //更新数据库
        tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);

    }

    private void recalculatePrice(Staff staff, List<Trade> originTrades) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        boolean isSalePriceSourceFx = fxBusiness.isSalePriceSourceFx(staff, tradeConfig);
        Lists.partition(TradeUtils.getOrders4Trade(originTrades), 500).forEach(part -> {
            Set<Long> sysItemIdList = new HashSet<>();
            Set<Long> sysSkuIdList = new HashSet<>();
            QueryItemDetailParams itemDetailParams = new QueryItemDetailParams();
            part.forEach(order -> {
                if (null == order.getItemSysId() || order.getItemSysId() <= 0) {
                    //商品未匹配, 跳过
                    return;
                }
                sysItemIdList.add(order.getItemSysId());
                sysSkuIdList.add(order.getSkuSysId());
            });
            itemDetailParams.setSysItemIdList(new ArrayList<>(sysItemIdList));
            itemDetailParams.setSysSkuIdList(new ArrayList<>(sysSkuIdList));
            itemDetailParams.setNeedSuitSingleOrNot(true);
            Map<Long, DmjItem> dmjItems = itemServiceDubboImpl.queryItemDetail(staff, itemDetailParams).stream().collect(Collectors.toMap(DmjItem::getSysItemId, Function.identity(), (k1, k2) -> k2));
            Map<Long, DmjSku> dmjSkuMap = new HashMap<>();
            dmjItems.forEach((dmjItem, value) -> value.getSkus().forEach(sku -> dmjSkuMap.put(sku.getSysSkuId(), sku)));

            part.forEach(order -> {
                if (null == order.getItemSysId() || order.getItemSysId() <= 0) {
                    //商品未匹配
                    order.setCost(0.00D);
                    order.setSalePrice("0.00");
                    order.setSaleFee("0.00");
                    return;
                }
                if (null == order.getSkuSysId() || order.getSkuSysId() <= 0) {
                    //纯商品
                    DmjItem dmjItem = dmjItems.get(order.getItemSysId());
                    if (CollectionUtils.isNotEmpty(dmjItem.getSkus())) {
                        //ERP查询不是纯商品
                        order.setCost(0.00D);
                        order.setSalePrice("0.00");
                        order.setSaleFee("0.00");
                        return;
                    }
                    //纯商品，匹配上
                    order.setCost(dmjItem.getPriceImport());
                    order.setSalePrice(isSalePriceSourceFx ? "0.00" : String.valueOf(dmjItem.getSalePrice(order.getUserId(), order.getNum())));
                    order.setSaleFee(order.getSalePrice() == null ? "0" : new BigDecimal(order.getSalePrice()).multiply(new BigDecimal(order.getNum() == null ? 0 : order.getNum())).toString());
                    return;
                }
                //sku商品
                if (dmjSkuMap.containsKey(order.getSkuSysId())) {
                    DmjSku sku = dmjSkuMap.get(order.getSkuSysId());
                    order.setCost(sku.getPriceImport());
                    order.setSalePrice(isSalePriceSourceFx ? "0.00" : String.valueOf(sku.getSalePrice(order.getUserId(), order.getNum())));
                    order.setSaleFee(order.getSalePrice() == null ? "0" : new BigDecimal(order.getSalePrice()).multiply(new BigDecimal(order.getNum() == null ? 0 : order.getNum())).toString());
                } else {
                    //没找到规格商品
                    order.setCost(0.00D);
                    order.setSalePrice("0.00");
                    order.setSaleFee("0.00");
                }
            });
        });
        //计算订单金额
        recalculateTradePriceContainMerge(staff, originTrades);
    }

    private void recalculateTradePriceContainMerge(Staff staff, List<Trade> originTrades) {
        Map<Long, List<Trade>> mergeTrades = new HashMap<>();
        for (Trade trade : originTrades) {
            trade.setActualPostFee("0.00");
            trade.setCost(TradeUtils.calculateCost(trade));
            trade.setSaleFee(TradeUtils.calculateTradeSaleFee(trade));
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                mergeTrades.computeIfAbsent(trade.getMergeSid(), value -> new ArrayList<>()).add(trade);
            }
        }
        mergeTrades.forEach((key, value) -> {
            Trade main = null;
            BigDecimalWrapper saleFee = new BigDecimalWrapper();
            BigDecimalWrapper totalCost = new BigDecimalWrapper();
            for (Trade trade : value) {
                totalCost.add(trade.getCost());
                saleFee.add(trade.getSaleFee());
                if (trade.getSid().equals(trade.getMergeSid())) {
                    main = trade;
                }
            }
            if (null != main) {
                main.setCost(totalCost.getDouble());
                main.setSaleFee(saleFee.getString());
            }
        });
    }

    private List<Trade> createUpdateTrade(Staff staff, List<Trade> originTrades, List<Order> updateOrders, Integer cancelType) {
        List<Trade> updateTrades = new ArrayList<>(originTrades.size());
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        for (Trade originTrade : originTrades) {
            boolean gxItemChangeExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
            boolean hasFxWaitPayExcept = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.FX_WAITPAY);
            boolean hasFxUnAudit = TradeExceptUtils.isContainExcept(staff, originTrade, ExceptEnum.FX_UNAUDIT);
            createUpdateOrder(staff, TradeUtils.getOrders4Trade(originTrade), updateOrders, cancelType, tradeConfig);
            Trade update = TradeBuilderUtils.builderUpdateTrade(originTrade, false);
            update.setSid(originTrade.getSid());
            update.setTid(originTrade.getTid());
            update.setCompanyId(originTrade.getCompanyId());
            if (cancelType != null && cancelType - 2 == 0) {
                update.setConvertType(TradeConstants.NORMAL_CONVERT_TYPE);
                update.setBelongType(TradeConstants.NORMAL_CONVERT_TYPE);
                update.setDestId(0L);
            } else if (Integer.valueOf(1).equals(cancelType)) {
                update.setConvertType(originTrade.getConvertType());
                update.setBelongType(originTrade.getBelongType());
                update.setDestId(originTrade.getDestId());
                update.setSourceId(originTrade.getSourceId());
                //更新实际运费、分销价、成本价
                update.setSalePrice(originTrade.getSalePrice());
                update.setCost(originTrade.getCost());
                update.setActualPostFee(originTrade.getActualPostFee());
                update.setV(originTrade.getV());
                //更新打印模版相关信息
                update.setLogisticsCompanyId(originTrade.getLogisticsCompanyId());
                update.setOutSid(originTrade.getOutSid());
            } else {
                update.setBelongType(2);
                update.setDestId(originTrade.getCompanyId());
                update.setV(originTrade.getV());
            }
            update.setTemplateId(originTrade.getTemplateId());
            update.setTemplateType(originTrade.getTemplateType());
            update.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
            update.setInsufficientNum(originTrade.getInsufficientNum());
            update.setInsufficientRate(originTrade.getInsufficientRate());
            TradeExceptUtils.setStockStatus(staff, update, originTrade.getStockStatus());
            update.setWarehouseId(originTrade.getWarehouseId());
            update.setWarehouseName(originTrade.getWarehouseName());
            update.setConsignTime(TradeTimeUtils.INIT_DATE);
            update.setExpressPrintTime(TradeTimeUtils.INIT_DATE);
            update.setDeliverPrintTime(TradeTimeUtils.INIT_DATE);
            update.setAssemblyPrintTime(TradeTimeUtils.INIT_DATE);
            update.setAuditTime(TradeTimeUtils.INIT_DATE);
            update.setIsAutoAudit(0);
            update.setExcep(originTrade.getExcep());
            TradeExceptUtils.updateExcept(staff, update, ExceptEnum.HALT, 0L);
            TradeUtils.setTradeExcep(staff, originTrade, update);
            TradeExceptUtils.updateExcept(staff, update, ExceptEnum.GX_ITEM_CHANGE_EXCEPT, gxItemChangeExcept);
            if (hasFxWaitPayExcept) {
                TradeExceptUtils.updateExcept(staff, update, ExceptEnum.FX_WAITPAY, 1L);
            }
            if (hasFxUnAudit) {
                TradeExceptUtils.updateExcept(staff, update, ExceptEnum.FX_UNAUDIT, 1L);
                Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s标记[%s]异常", update.getSid(), ExceptEnum.FX_UNAUDIT.getChinese())));
            }
            updateTrades.add(update);
        }
        return updateTrades;
    }

    private void createUpdateOrder(Staff staff, List<Order> orders, List<Order> updateOrders, Integer cancelType, TradeConfig tradeConfig) {
        List<Order> originOrders = OrderUtils.toFullOrderList(orders, false);
        for (Order originOrder : originOrders) {
            Order update = OrderBuilderUtils.builderUpdateOrderWithV(originOrder);
            update.setId(originOrder.getId());
            if (cancelType != null && cancelType - 2 == 0) {
                update.setConvertType(TradeConstants.NORMAL_CONVERT_TYPE);
                update.setBelongType(TradeConstants.NORMAL_CONVERT_TYPE);
                update.setDestId(0L);
            } else if (Integer.valueOf(1).equals(cancelType)) {
                update.setConvertType(originOrder.getConvertType());
                update.setBelongType(originOrder.getBelongType());
                update.setDestId(originOrder.getDestId());
                //更新分销价、成本价
                update.setCost(originOrder.getCost());
                update.setSalePrice(originOrder.getSalePrice());
                update.setSaleFee(originOrder.getSaleFee());
            } else {
                update.setBelongType(2);
                update.setDestId(originOrder.getCompanyId());
            }
            //如果分销价字段 取值分销模块分销价 则不会将客户模块分销价写入 这里置零
            if (fxBusiness.isSalePriceSourceFx(staff, tradeConfig) && (update.getDestId() == null || update.getDestId() == 0L)) {
                update.setSalePrice("0.0");
                update.setSaleFee("0.0");
            }
            update.setStockNum(originOrder.getStockNum());
            OrderExceptUtils.setStockStatus(staff, update, originOrder.getStockStatus());
            update.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
            // 取消分销属性时取消供销订单商品信息变更 异常
            OrderExceptUtils.syncOrderExcept(staff, update, originOrder, ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
            updateOrders.add(update);
        }
    }

    public void fillTemplateType(Staff staff, Trade trade) {
        String userSource = auditFxBusiness.getUserSource(staff, trade.getUserId());
        if (CommonConstants.PLAT_FORM_TYPE_PDD.equals(userSource)) {
            trade.setWlbTemplateType(6);
        } else if (CommonConstants.PLAT_FORM_TYPE_JD.equals(userSource)) {
            trade.setWlbTemplateType(5);
        }
    }

    public List<Trade> getGxTradeByFx(Trade trade) {
        if (!TradeUtils.isFxOrMixTrade(trade)) {
            return null;
        }
        Staff destStaff = staffService.queryFullByCompanyId(trade.getDestId());
        if (destStaff == null) {
            return null;
        }
        List<TbTrade> gxTrades = tradeSearchService.queryByTids(destStaff, false, trade.getSid().toString());
        if (CollectionUtils.isEmpty(gxTrades)) {
            return null;
        }
        return gxTrades.stream().filter(t -> !TradeUtils.isCancel(t)).collect(Collectors.toList());
    }

    /**
     * 填充供销订单商品重量与体积
     * @param staff
     * @param gxTrades 供销订单列表
     * @param updateForce 是否强制更新（会更新其他ERP发货的、系统前退款）
     * @param calTrade 是否计算订单维度的商品信息
     */
    public void fillGxTradeItemInfo(Staff staff, List<Trade> gxTrades, boolean updateForce, boolean calTrade) {
        if (CollectionUtils.isEmpty(gxTrades)) {
            return;
        }
        List<Order> gxOrders = TradeUtils.getOrders4Trade(gxTrades);
        List<Order> fillOrders = new ArrayList<>(gxOrders.size());
        for (Order gxOrder : gxOrders) {
            if (!updateForce && (OrderUtils.isOtherErpConsigned(gxOrder) || OrderUtils.isRefundedBeforeSysConsign(gxOrder))) {
                continue;
            }
            fillOrders.add(gxOrder);
            if (CollectionUtils.isNotEmpty(gxOrder.getSuits())) {
                fillOrders.addAll(gxOrder.getSuits());
            }
        }
        fillGxOrderItemInfo(staff, fillOrders);
        /**
         * 供销单，暂时不考虑合单场景
         * 实际重量先不考虑
         */
        if (calTrade) {
            for (Trade trade : gxTrades) {
                //订单净重
                trade.setNetWeight(TradeUtils.calculateTradeNetWeight(trade));
                //订单体积
                trade.setVolume(TradeUtils.calculateVolume(trade));
            }
        }

    }

    private void fillGxOrderItemInfo(Staff staff, List<Order> gxOrders) {
        Map<String, DmjItem> dmjItemMap = new HashMap<>();
        Lists.partition(gxOrders, 500).forEach(part -> {
            Set<Long> sysItemIdList = new HashSet<>();
            Set<Long> sysSkuIdList = new HashSet<>();
            QueryItemDetailParams itemDetailParams = new QueryItemDetailParams();
            part.forEach(order -> {
                if (null == order.getItemSysId() || order.getItemSysId() <= 0) {
                    //商品未匹配, 跳过
                    return;
                }
                String key = TradeItemUtils.getItemKey(order.getItemSysId(), order.getSkuSysId());
                if (dmjItemMap.containsKey(key)) {
                    return;
                }
                sysItemIdList.add(order.getItemSysId());
                sysSkuIdList.add(order.getSkuSysId());
            });
            itemDetailParams.setSysItemIdList(new ArrayList<>(sysItemIdList));
            itemDetailParams.setSysSkuIdList(new ArrayList<>(sysSkuIdList));
            List<DmjItem> dmjItemList = itemServiceDubboImpl.queryItemDetail(staff, itemDetailParams);
            for (DmjItem dmjItem : dmjItemList) {
                if (CollectionUtils.isEmpty(dmjItem.getSkus())) {
                    dmjItemMap.put(TradeItemUtils.getItemKey(dmjItem.getSysItemId(), 0L), dmjItem);
                } else {
                    dmjItem.getSkus().forEach(dmjSku -> {
                        dmjItemMap.put(TradeItemUtils.getItemKey(dmjSku.getSysItemId(), dmjSku.getSysSkuId()), dmjSku);
                    });
                }
            }
            for (Order order : part) {
                DmjItem dmjItem = dmjItemMap.get(TradeItemUtils.getItemKey(order.getItemSysId(), order.getSkuSysId()));
                if (null == dmjItem) {
                    continue;
                }
                order.setNetWeight(dmjItem.getWeight());
                order.setVolume(dmjItem.getVolume());
            }
        });
    }

    public Map<String, Trade> getGxMapByFxSid(List<Trade> tradeList) {
        List<Trade> fxTradeList = tradeList.stream().filter(TradeUtils::isFxTrade).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fxTradeList)) {
            return null;
        }
        Map<Long, List<Trade>> destMap = fxTradeList.stream().collect(Collectors.groupingBy(Trade::getDestId));
        Iterator<Map.Entry<Long, List<Trade>>> iterator = destMap.entrySet().iterator();
        Map<String, Trade> gxTradeMap = new HashMap<>();
        while (iterator.hasNext()) {
            Map.Entry<Long, List<Trade>> next = iterator.next();
            Long destId = next.getKey();
            List<Trade> fxTrades = next.getValue();
            Staff destStaff = staffService.queryFullByCompanyId(destId);
            if (destStaff == null) {
                continue;
            }
            List<TbTrade> gxTrades = tradeSearchService.queryByTids(destStaff, false, Strings.listToString(TradeUtils.toSidList(fxTrades)));
            if (CollectionUtils.isNotEmpty(gxTrades)) {
                gxTradeMap.putAll(gxTrades.stream().filter(t -> !TradeUtils.isCancel(t)).collect(Collectors.toMap(this::getMapKeyByGx, t -> t, (a, b) -> a)));
            }
        }
        return gxTradeMap;
    }

    /**
     * 重算流水 -- 作废流水 + 消费流水
     * 支持合单，主单与子单流水都作废，消费的流水挂在主单上
     * @param gxStaff
     * @param gxTrades
     * @param containMerge 订单结构是否包含合单
     * false:只有主单,子单在trade.messageMemos，
     * true：主单子单同一纬度
     */
    public void reCalculateCashFlowByInvalid(Staff gxStaff, List<Trade> gxTrades, boolean containMerge) {
        Map<Long, Map<Long, List<Trade>>> invalidTrades = new HashMap<>();
        Map<Long, Map<Long, List<Trade>>> invalid2ConsumeTrades = new HashMap<>();
        Map<Long, Map<Long, List<DmsGenerateCashFlowRequest>>> consume = new HashMap<>();
        if (containMerge) {
            Map<Long, List<Trade>> mergeTrades = new HashMap<>();
            for (Trade gxTrade : gxTrades) {
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(gxStaff, gxTrade)) {
                    mergeTrades.computeIfAbsent(gxTrade.getMergeSid(), k -> new ArrayList<>()).add(gxTrade);
                    if (Objects.equals(gxTrade.getSid(), gxTrade.getMergeSid())) {
                        //待付款异常的订单不操作
                        if (!TradeExceptUtils.isContainExcept(gxStaff, gxTrade, ExceptEnum.FX_WAITPAY)) {
                            invalid2ConsumeTrades.computeIfAbsent(gxTrade.getCompanyId(), v -> new HashMap<>()).computeIfAbsent(gxTrade.getSourceId(), t -> new ArrayList<>()).add(gxTrade);
                        }
                    } else {
                        //待付款异常的订单不操作
                        if (!TradeExceptUtils.isContainExcept(gxStaff, gxTrade, ExceptEnum.FX_WAITPAY)) {
                            invalidTrades.computeIfAbsent(gxTrade.getCompanyId(), v -> new HashMap<>()).computeIfAbsent(gxTrade.getSourceId(), t -> new ArrayList<>()).add(gxTrade);
                        }
                    }
                } else {
                    //待付款异常的订单不操作
                    if (!TradeExceptUtils.isContainExcept(gxStaff, gxTrade, ExceptEnum.FX_WAITPAY)) {
                        invalid2ConsumeTrades.computeIfAbsent(gxTrade.getCompanyId(), v -> new HashMap<>()).computeIfAbsent(gxTrade.getSourceId(), t -> new ArrayList<>()).add(gxTrade);
                    }
                }
            }
            //构建消费流水
            invalid2ConsumeTrades.forEach((destId, value) -> {
                value.forEach((sourceId, v) -> {
                    v.forEach(gxTrade -> {
                        DmsGenerateCashFlowRequest request = CashFlowUtils.getCashFlowRequestInstance(gxTrade);
                        request.setFlowType(DmsCashFlowTypeEnum.orderModify.getValue());
                        request.setCheckCashBalance(false);
                        List<Trade> trades = mergeTrades.get(gxTrade.getSid());
                        if (CollectionUtils.isNotEmpty(trades)) {
                            request.setAmount(-(CashFlowUtils.getOrdersPayment(TradeUtils.getOrders4Trade(trades)).add(gxTrade.getPostFee()).subtract(gxTrade.getDiscountFee()).getDouble()));
                        } else {
                            request.setAmount(-(CashFlowUtils.getOrdersPayment(TradeUtils.getOrders4Trade(gxTrade)).add(gxTrade.getPostFee()).subtract(gxTrade.getDiscountFee()).getDouble()));
                        }
                        consume.computeIfAbsent(request.getSupplierCompanyId(), k -> new HashMap<>()).computeIfAbsent(request.getDistributorCompanyId(), t -> new ArrayList<>()).add(request);
                    });
                });
            });
        } else {
            for (Trade gxTrade : gxTrades) {
                //待付款异常的订单不操作
                if (!TradeExceptUtils.isContainExcept(gxStaff, gxTrade, ExceptEnum.FX_WAITPAY)) {
                    invalid2ConsumeTrades.computeIfAbsent(gxTrade.getCompanyId(), v -> new HashMap<>()).computeIfAbsent(gxTrade.getSourceId(), t -> new ArrayList<>()).add(gxTrade);
                    //构建消费流水
                    DmsGenerateCashFlowRequest request = CashFlowUtils.getCashFlowRequestInstance(gxTrade);
                    request.setFlowType(DmsCashFlowTypeEnum.orderModify.getValue());
                    request.setCheckCashBalance(false);
                    request.setAmount(-(CashFlowUtils.getOrdersPayment(TradeUtils.getOrders4Trade(gxTrade)).add(gxTrade.getPostFee()).subtract(gxTrade.getDiscountFee()).getDouble()));
                    consume.computeIfAbsent(request.getSupplierCompanyId(), k -> new HashMap<>()).computeIfAbsent(request.getDistributorCompanyId(), t -> new ArrayList<>()).add(request);
                }
                if (CollectionUtils.isNotEmpty(gxTrade.getMessageMemos())) {
                    gxTrade.getMessageMemos().forEach(messageMemo -> {
                        if (!Objects.equals(gxTrade.getSid(), messageMemo.getSid())) {
                            Trade trade = new TbTrade();
                            trade.setSid(messageMemo.getSid());
                            invalidTrades.computeIfAbsent(gxTrade.getCompanyId(), v -> new HashMap<>()).computeIfAbsent(gxTrade.getSourceId(), t -> new ArrayList<>()).add(trade);
                        }
                    });
                }
            }
        }
        invalidFxCashFlow(gxStaff, invalidTrades, false);
        //这快后续需要dms人员支持，需要针对单笔订单给出返回值
        invalidFxCashFlow(gxStaff, invalid2ConsumeTrades, true);
        if (MapUtils.isNotEmpty(consume)) {
            FlowRequest consumeRequest = new FlowRequest();
            consumeRequest.setFlowType(DmsCashFlowTypeEnum.orderModify);
            consumeRequest.setSupplierDistributorResumeFlowListMap(consume);
            CashFlowData cashFlowData = CashFlowUtils.fillResponseInfo(dmsTradeService.resumeFlow(gxStaff, consumeRequest).getSupplierDistributorResumeFlowListMap(), false);
            if (MapUtils.isNotEmpty(cashFlowData.errorMap)) {
                new FxLogBuilder(gxStaff, FxLogBuilder.ROLE_GX).append("供销订单重新消费流水失败：", cashFlowData.errorMap).printWarn(logger);
            }
        }
    }


    public InvalidFlowData invalidFxCashFlow(Staff gxStaff, Map<Long, Map<Long, List<Trade>>> invalidSids, boolean printLog) {
        return invalidFxCashFlow(gxStaff, invalidSids, printLog, false);
    }

    public InvalidFlowData invalidFxCashFlow(Staff gxStaff, Map<Long, Map<Long, List<Trade>>> invalidSids, boolean printLog, boolean ignoreErr) {
        InvalidFlowData invalidFlowData = new InvalidFlowData();
        List<Long> allSids = new ArrayList<>();
        invalidSids.forEach((destId, v) -> v.forEach((sourceId, gxSids) -> Lists.partition(gxSids, 100).forEach(part -> {
            List<Long> sids = part.stream().map(Trade::getSid).collect(Collectors.toList());
            allSids.addAll(sids);
            try {
                DmsInvalidCashFlowRequest invalidCashFlowRequest = new DmsInvalidCashFlowRequest();
                invalidCashFlowRequest.setSupplierCompanyId(destId);
                invalidCashFlowRequest.setDistributorCompanyId(sourceId);
                invalidCashFlowRequest.setSysOrderNumberList(sids.stream().map(String::valueOf).collect(Collectors.toList()));
                dmsTradeService.invalidFxCashFlow(invalidCashFlowRequest);
                invalidFlowData.sucSids.addAll(sids);
            } catch (Exception e) {
                if (printLog) {
                    new FxLogBuilder(gxStaff, FxLogBuilder.ROLE_GX).append("作废流水失败的供销订单", part).printError(logger, e);
                    sids.forEach(failSid -> {
                        invalidFlowData.errMap.put(failSid, e.getMessage());
                    });
                }
            }
        })));
        //作废流水，无法区分异常场景与成功失败场景。如果流水已经作废过了，会抛出异常，针对业务，这是正常的，依赖后续DMS的接口升级
        if (ignoreErr) {
            invalidFlowData.sucSids = allSids;
            invalidFlowData.errMap.clear();
        }
        return invalidFlowData;
    }

    public String getMapKeyByGx(Trade gxTrade) {
        return gxTrade.getSourceId() + "_" + gxTrade.getCompanyId() + gxTrade.getTid();
    }

    public String getMapKeyByFx(Trade fxTrade) {
        return fxTrade.getCompanyId() + "_" + fxTrade.getDestId() + fxTrade.getSid();
    }
}

package com.raycloud.dmj.business.trade;

import com.alibaba.fastjson.*;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.platform.enums.fxg.FxgBTASExpressProductEnum;
import com.raycloud.dmj.domain.pt.enums.WlbTemplateTypeEnum;
import com.raycloud.dmj.domain.pt.model.UserExpressWlbModel;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompany;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.config.entity.ItemKeywordParse;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.trade.sync.TradeSyncConstant;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.statusenum.RefundStatusEnum;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.ec.TradeTraceListener;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.item.utils.ItemMatchUtils;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.pt.IUserExpressTemplateService;
import com.raycloud.dmj.services.tag.ITradeTagService;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import com.raycloud.dmj.services.trades.support.TradeConfigService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.web.utils.IpUtils;
import com.raycloud.ec.api.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.enums.OpEnum.*;
import static com.raycloud.dmj.domain.trade.sync.TradeSyncConstant.IGNORE_CHANGE_SELLER_FLAG_SOURCE;

@Service
public class TradeTraceBusiness {
    private static final Logger logger = Logger.getLogger(TradeTraceBusiness.class);

    @Resource
    ITradeTraceService tradeTraceService;

    @Resource
    private ITradeTagService tradeTagService;

    @Resource
    private IOpLogService opLogService;

    @Resource
    private SecretBusiness secretBusiness;

    @Resource
    private IUserExpressTemplateService userExpressTemplateService;

    @Resource
    protected IStaffService staffService;

    @Resource
    private TradeConfigService tradeConfigService;

    @Resource
    ChangeAddressTraceBusiness changeAddressTraceBusiness;

    @Resource
    IEventCenter eventCenter;

    @Resource
    protected FeatureService featureService;

    @Resource
    private IUserLogisticsCompanyService userLogisticsCompanyService;

    public void asyncTrace(Staff staff, Collection<Trade> trades, OpEnum op, Object... otherArgs) {
        if (trades != null && !trades.isEmpty()) {
            Object[] args = new Object[otherArgs.length + 2];
            args[0] = staff;
            args[1] = op;
            if (otherArgs.length > 0) {
                System.arraycopy(otherArgs, 0, args, 2, otherArgs.length);
            }
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_TRACE_ADD).setArgs(args), TradeTraceUtils.simplifyWithOrders(trades, needRecordOrders(op)));
        }
    }

    boolean needRecordOrders(OpEnum opEnum){
        return opEnum != null && (opEnum == OpEnum.TRADE_SYNC);
    }

    public void tradeInsertProcess(User user, List<Trade> tradeList,TradeImportResult result){
        if(CollectionUtils.isEmpty(tradeList)){
            return;
        }
        Staff staff = user.getStaff();
        List<TradeTrace> tradeTraceLogs=new ArrayList<>();
        for (Trade trade : tradeList) {
            trade.getOperations().put(OpEnum.TRADE_SYNC, TradeTrace.DOWNLOAD_TRADE + "[" + TradeStatusUtils.getChStatus(trade.getUnifiedStatus(), true) + "]");
            if (trade.getPresellRule() != null) {
                trade.getOperations().put(OpEnum.PRESELL_MATCH, "自动匹配预售[" + trade.getPresellRule().getName() + "]");
            }
            stockExceptLog(staff,trade);
            //匹配商品日志
            itemMatchLog(user, trade);
            //pdd fxg 平台赠品拣选、验货日志
            if (CommonConstants.PLAT_FORM_TYPE_PDD.equals(trade.getSource())
                    || CommonConstants.PLAT_FORM_TYPE_FXG.equals(trade.getSource())
                    || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(trade.getSource())
                    || CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(trade.getSource())
                    || CommonConstants.PLAT_FORM_TYPE_XHS.equals(trade.getSource())
                    || CommonConstants.PLAT_FORM_TYPE_WXSPH.equals(trade.getSource())
            ) {
                handlePlatformGiftPickConfigTrace(user, trade);
            }
            //BTAS订单，匹配配置的日志
            btasConfigMatchLog(trade);
            if (trade.getWarehouseId() != null && trade.getWarehouseId() > 0 && !trade.getOperations().containsKey(OpEnum.WAREHOUSE_MATCH)) {
                trade.getOperations().put(OpEnum.WAREHOUSE_MATCH, "自动匹配仓库: " + trade.getWarehouseName());
            }
            if (trade.getSplitType() - TradeSplitEnum.SPLIT_SKU.getDbType() == 0) {
                trade.getOperations().put(OpEnum.SPLIT, "自动拆单");
            }
            String autoSplitTrace = trade.getOperations().get(OpEnum.SPLIT);
            if (StringUtils.isNotEmpty(autoSplitTrace)){
                trade.getOperations().remove(OpEnum.SPLIT);
                trade.getOperations().put(OpEnum.SPLIT,autoSplitTrace);
            }
            // O2O匹配O2O平台模版和自配送模版的订单不打日志
            boolean o2ODefaultTradeTemplate = PlatformUtils.isO2ODefaultTradeTemplate(trade);
            if (!o2ODefaultTradeTemplate && trade.getTemplateId() != null && trade.getTemplateId() > 0 && !CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource())) {
                boolean hasVirtualWarehouse = featureService.checkHasFeature(staff.getCompanyId(), Feature.PRINT_TEMPLATE_INTEGRATE);
                UserExpressWlbModel userExpressWlbModel = userExpressTemplateService.getMixExpressById(staff, trade.getTemplateId(), trade.getTemplateType());
                String matchType = trade.hasOpV(OpVEnum.TRADE_FX_APPOINT_TEMPLATE_ID)?"分销指定供销快递":"自动匹配快递";
                if (hasVirtualWarehouse) {
                    UserLogisticsCompany userLogisticsCompany = userLogisticsCompanyService.queryById(staff, trade.getLogisticsCompanyId());
                    String userLogisticsCompanyName = userLogisticsCompany == null ? "" : userLogisticsCompany.getName();
                    String platformName = userExpressWlbModel.getWlbTemplateType() != null ? ";电子面单:" + WlbTemplateTypeEnum.getNameByValue(userExpressWlbModel.getWlbTemplateType()) + ";" : "";
                    trade.getOperations().put(OpEnum.EXPRESS_MATCH, String.format("%s,快递公司【%s】%s%s%s%s", matchType, userLogisticsCompanyName, (trade.getTemplateMatchRuleId() != null ? ",快递匹配规则id:" + trade.getTemplateMatchRuleId() : ""), platformName, TradeTraceListener.buildGxTradeFreightLog(trade), ("ExpressMatchFreightStrategy".equalsIgnoreCase(trade.getExpressMatchStrategy()) ? ",匹配规则：运费匹配规则" : "")));
                } else {
                    trade.getOperations().put(OpEnum.EXPRESS_MATCH, String.format("%s:【%s】%s%s%s", matchType, userExpressWlbModel.getTemplateName(), (trade.getTemplateMatchRuleId() != null ? ",快递匹配规则id:" + trade.getTemplateMatchRuleId() : ""), TradeTraceListener.buildGxTradeFreightLog(trade), ("ExpressMatchFreightStrategy".equalsIgnoreCase(trade.getExpressMatchStrategy()) ? ",匹配规则：运费匹配规则" : "")));
                }
            } else if(!o2ODefaultTradeTemplate && TradeUtils.isGxOrMixTrade(trade) && StringUtils.isNotBlank(trade.getGxFreightRuleName())){
                trade.getOperations().put(OpEnum.EXPRESS_MATCH, String.format("自动匹配快递%s%s",  TradeTraceListener.buildGxTradeFreightLog(trade), ("ExpressMatchFreightStrategy".equalsIgnoreCase(trade.getExpressMatchStrategy()) ? ",匹配规则：运费匹配规则" : "")));
            }
            if(TradeExceptUtils.isPlatRiskExcept(trade)){
                if (TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.RISK)){
                    trade.getOperations().put(OpEnum.TRADE_SYNC, "同步平台风控状态，标记风控异常");
                }
                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PDD_STOCK_OUT)) {
                    trade.getOperations().put(OpEnum.TRADE_SYNC, "同步平台缺货已处理，标记缺货已处理异常");
                }
            }
            if (CommonConstants.PLAT_FORM_TYPE_YUNCANG.equals(trade.getSource()) || CommonConstants.PLAT_FORM_TYPE_POISON.equals(trade.getSource())
                    && (trade.getDelayShipTime()!=null&&trade.getDelayShipTime()>0&& TradeExceptUtils.isContainExcept(staff,trade, ExceptEnum.HALT))) {
                trade.getOperations().put(OpEnum.TRADE_SYNC, "平台预售订单自动挂起，挂起时长：" + trade.getDelayShipTime() + "小时");
            }
            String s = trade.getOperations().get(OpEnum.EXCEPT_UPDATE);
            if(StringUtils.isNotBlank(s)){
                trade.getOperations().put(OpEnum.TRADE_SYNC, s);
            }
            Set<String> operationLogs = trade.getOperationSetLogs().get(OpEnum.EXCEPT_SYNC);
            if(CollectionUtils.isNotEmpty(operationLogs)){
                for(String operation:operationLogs){
                    tradeTraceLogs.add(TradeTraceUtils.createTradeTraceWithTrade(user.getStaff(), trade, operation, "系统", new Date(), ""));
                }
            }
            //O2O订单信息变更日志
            addTradeO2oInfoChange(user, trade, new Date(), tradeTraceLogs);
        }

        Staff stf = new Staff().setId(staff.getId()).setCompanyId(staff.getCompanyId()).setName("系统");
        stf.setCompany(staff.getCompany());
        ClueIdUtil.fillClueId(stf);
        asyncTrace(stf, tradeList, OpEnum.TRADE_SYNC);
        if(CollectionUtils.isNotEmpty(tradeTraceLogs)){
            tradeTraceService.batchAddTradeTrace(staff, tradeTraceLogs);
        }
    }

    public void stockExceptLog(Staff staff,Trade trade) {
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.INSUFFICIENT)) {
            trade.getOperations().put(OpEnum.ADD_TAG, "标记库存不足异常");
        }
    }


    public void btasConfigMatchLog(Trade trade){
        if (TradeTypeUtils.isFxgBtasTrade(trade)){
            StringBuilder builder = new StringBuilder();
            //快递产品
            Object btasExpressProduct = TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), TradeExtendConfigsEnum.BTAS_EXPRESS_PRODUCT.getKey());
            Integer btasExpressProductKey  = Objects.isNull(btasExpressProduct) ? Integer.valueOf(0) : (Integer) btasExpressProduct;
            builder.append("【BTAS质检】【快递产品】：").append("快递产品为").append(FxgBTASExpressProductEnum.getByKey(btasExpressProductKey).getName());
            //订单保价
            Object btasInsuredPrice = TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), TradeExtendConfigsEnum.BTAS_INSURED_PRICE.getKey());
            builder.append("; ").append("【BTAS质检】【订单保价】：").append((null == btasInsuredPrice || (Integer)btasInsuredPrice != 1 ? "订单保价为否，订单不保价":"订单保价为是，订单需保价"));
            //退货发货拦截
            Object btasGoodsRejectedIntercept = TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), TradeExtendConfigsEnum.BTAS_GOODS_REJECTED_INTERCEPT.getKey());
            builder.append("; ").append("【BTAS质检】【退货发货拦截】：").append((null == btasGoodsRejectedIntercept || (Integer)btasGoodsRejectedIntercept != 1 ? "退货发货拦截为发货优先":"退货发货拦截为售后优先"));
            //取回方式
            Object btasQualityCheckReturnType = TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), TradeExtendConfigsEnum.BTAS_QUALITY_CHECK_RETURN_TYPE.getKey());
            builder.append("; ").append("【BTAS质检】【取回方式】：").append((null == btasQualityCheckReturnType || (Integer)btasQualityCheckReturnType != 1 ? "质检退回取回方式为邮寄":"质检退回取回方式为线下自提"));
            trade.getOperations().put(OpEnum.CONFIG_MATCH, builder.toString());
        }
    }

    public void itemMatchLog(User user, Trade trade) {
        String s1 = TradeUtils.getOrders4Trade(trade).stream().peek(OrderUtils::addItemMatchLog).filter(o -> o.getOperations().containsKey(OpEnum.ITEM_RELATION_BIND_SUCCESS) && !o.getOperations().containsKey(OpEnum.ITEM_KEYWORD_BIND)).map(o -> o.getOperations().get(OpEnum.ITEM_RELATION_BIND_SUCCESS)).collect(Collectors.joining(","));
        String s2 = OrderUtils.addItemUnmatchLog(trade);
        String s3 = TradeUtils.getOrders4Trade(trade).stream().filter(o -> o.getOperations().containsKey(OpEnum.ITEM_KEYWORD_BIND) && StringUtils.isNotBlank(o.getOperations().get(OpEnum.ITEM_KEYWORD_BIND))).map(o -> o.getOperations().get(OpEnum.ITEM_KEYWORD_BIND)).collect(Collectors.joining(","));

        StringBuilder logs = new StringBuilder("匹配系统商品: ");
        if (!s1.isEmpty()) {
            logs.append(s1);
        }
        if (!s3.isEmpty()) {
            ItemKeywordParse itemParse = ItemMatchUtils.validate(user);
            String fieldName = ItemMatchUtils.getParseFieldName(Objects.nonNull(itemParse) ? itemParse.checkUserId(user.getId()) : 0);
            logs.append(String.format(" 通过%s解析多个商品: ", fieldName)).append(s3);
        }
        if (!s2.isEmpty()) {
            logs.append(" 匹配商品失败: ").append(s2);
        }
        if (s1.isEmpty() && s2.isEmpty() && s3.isEmpty()) {
            logs.append("失败");
        }
        trade.getOperations().put(OpEnum.ITEM_RELATION_BIND, logs.toString());
    }

    /**
     * 订单的同步的时候需要记录下平台赠品属性值的日志，记录格式“时间 平台赠品#配置内容#”，例“2021/12/29 10:00:00 平台赠品参与拣选和不验货“
     * @param user
     * @param trade
     */
    private void handlePlatformGiftPickConfigTrace(User user, Trade trade) {
        List<Order> platformGiftList = TradeUtils.getOrders4Trade(trade).stream().filter(Order::isPlatformGift).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(platformGiftList)){
            trade.getOperations().put(OpEnum.TRADE_SYNC, "平台赠品 " + getPlatformGiftPickDescByValue(platformGiftList.get(0).getIsPick()));
        }
    }

    /**
     * 0：不参与拣选和不验货；1：参与拣选和不验货；2：参与拣选和验货；3：不参与拣选但验货
     * {@link Order#getIsPick()}
     */
    private String getPlatformGiftPickDescByValue(Integer isPick) {
        switch (isPick) {
            case 0:
                return "不参与拣选和不验货";
            case 1:
                return "参与拣选和不验货";
            case 2:
                return "参与拣选和验货";
            case 3:
                return "不参与拣选但验货";
            default:
                return "";
        }
    }

    public void tradeUpdateProcess(User user, List<Trade> tradeList, TradeImportResult result){
        if(CollectionUtils.isEmpty(tradeList)){
            return;
        }
        int insertTraceType = 0;
        Date updateTime = new Date();
        List<TradeTrace> tradeTraceList = new ArrayList<TradeTrace>();
        List<Trade> itemChangedList = new ArrayList<Trade>();
        Staff staff = staffService.queryDefaultStaffByCompanyId(user.getCompanyId());
        secretBusiness.decodeTrades(user.getStaff(), tradeList);
        for(Trade trade : tradeList){
            if (insertTraceType == 0 && isUpdateTradeBuyerMessage(trade)) {
                //如果买家留言有改动就在系统打印日志
                tradeTraceList.add(createTradeTrace(user, trade, "留言改变", String.format("留言改变: %s->%s", trade.getOrigin().getBuyerMessage(), trade.getBuyerMessage()), updateTime));
            }
            if (insertTraceType == 0 && isUpdateTradeStatus(trade)) {
                String action = String.format(OpEnum.TRADE_SYNC_UPDATED.getName() + ": %s->%s", TradeStatusUtils.getChStatus(trade.getOrigin().getUnifiedStatus(), true), TradeStatusUtils.getChStatus(trade.getUnifiedStatus(), true));
                tradeTraceList.add(createTradeTrace(user, trade, action, updateTime));
            }
            if ((insertTraceType == 0 || insertTraceType == 1) && isUpdateReceivedAddress(trade)) {
                tradeTraceList.add(createAddressChanged(staff, user, trade, updateTime));
            }
            if ((insertTraceType == 0 || insertTraceType == 1) && isUpdateRefundStatus(user.getStaff(),trade)) {
                TradeTrace refundChanged = createRefundChanged(user, trade, updateTime);
                if(refundChanged!=null){
                    tradeTraceList.add(refundChanged);
                }
            }
            if ((insertTraceType == 0 || insertTraceType == 2) && TradeUtils.isUpdateSellerMemo(trade)) {
                tradeTraceList.add(createSellerMemoChanged(user, trade, updateTime));
                //处理自动重新匹配
                processChangeMemoTrace(user,trade,tradeTraceList);
            }
            // 日志会反复 屏蔽掉
//            if (insertTraceType == 0 && TradeUtils.notUpdateSellerMemo(trade)) {
//                tradeTraceList.add(craeteSellerMemoNotChange(user, trade, updateTime));
//            }
            if (insertTraceType == 0 && isUpdateOrderProperties(staff,trade)&&!result.openPlatformToCommodity) {
                tradeTraceList.add(createTradeTrace(user, trade, "同步商品修改", updateTime));
            }
            if (trade.getPresellRule() != null && TradeStatusUtils.isWaitPay(trade.getOldSysStatus()) && TradeStatusUtils.isWaitAudit(trade.getSysStatus())) {
                tradeTraceList.add(createTradeTrace(user, trade, "自动匹配预售", "自动匹配预售[" + trade.getPresellRule().getName() + "]", updateTime));
            }
            if (trade.getOperations().containsKey(OpEnum.GIFT_MATCH)) {
                tradeTraceList.add(createTradeTrace(user, trade, OpEnum.GIFT_MATCH.getName(), trade.getOperations().get(OpEnum.GIFT_MATCH), updateTime));
            }
            if (trade.getOperations().containsKey(OpEnum.TAG_MATCH)) {
                tradeTraceList.add(createTradeTrace(user, trade, OpEnum.TAG_MATCH.getName(), trade.getOperations().get(OpEnum.TAG_MATCH), updateTime));
            }
            if (trade.getOperations().containsKey(OpEnum.SUITE_TO_SINGLE)) {
                tradeTraceList.add(createTradeTrace(user, trade, OpEnum.SUITE_TO_SINGLE.getName(), trade.getOperations().get(OpEnum.SUITE_TO_SINGLE), updateTime));
            }
            if (trade.getOperations().containsKey(OpEnum.ITEM_REPLACE)) {
                tradeTraceList.add(createTradeTrace(user, trade, OpEnum.ITEM_REPLACE.getName(), trade.getOperations().get(OpEnum.ITEM_REPLACE), updateTime));
            }
            if (trade.getOperations().containsKey(OpEnum.TAG_UPDATE)) {
                tradeTraceList.add(createTradeTrace(user, trade, OpEnum.TAG_UPDATE.getName(), trade.getOperations().get(OpEnum.TAG_UPDATE), updateTime));
            }
            if (trade.getOperations().containsKey(OpEnum.CONFIG_MATCH)) {
                tradeTraceList.add(createTradeTrace(user, trade, OpEnum.CONFIG_MATCH.getName(), trade.getOperations().get(OpEnum.CONFIG_MATCH), updateTime));
            }
            if (trade.getOperations().containsKey(OpEnum.OUT_SID_SYNC_UPDATE)) {
                tradeTraceList.add(createTradeTrace(user, trade, OpEnum.OUT_SID_SYNC_UPDATE.getName(), trade.getOperations().get(OpEnum.OUT_SID_SYNC_UPDATE), updateTime));
            }
            if (trade.getOperations().containsKey(OpEnum.FX_PLAT_ITEM_CHANGE)) {
                tradeTraceList.add(createTradeTrace(user, trade, OpEnum.FX_PLAT_ITEM_CHANGE.getName(), trade.getOperations().get(OpEnum.FX_PLAT_ITEM_CHANGE), updateTime));
            }
            if (trade.getOperations().containsKey(OpEnum.FX_PRICE_EXCEPT)) {
                tradeTraceList.add(createTradeTrace(user, trade, OpEnum.FX_PRICE_EXCEPT.getName(), trade.getOperations().get(OpEnum.FX_PRICE_EXCEPT), updateTime));
            }
            if(insertTraceType == 0 && isUpdateTemplate(trade)){
                boolean hasVirtualWarehouse = featureService.checkHasFeature(staff.getCompanyId(), Feature.PRINT_TEMPLATE_INTEGRATE);
                UserExpressWlbModel userExpressWlbModel = userExpressTemplateService.getMixExpressById(staff, trade.getTemplateId(), trade.getTemplateType());
                String matchType = trade.hasOpV(OpVEnum.TRADE_FX_REAPPOINT_TEMPLATE_ID)?"分销重新指定供销快递":"自动匹配快递";
                if (hasVirtualWarehouse) {
                    UserLogisticsCompany userLogisticsCompany = userLogisticsCompanyService.queryById(staff, trade.getLogisticsCompanyId());
                    String userLogisticsCompanyName = userLogisticsCompany == null ? "" : userLogisticsCompany.getName();
                    String platformName = userExpressWlbModel.getWlbTemplateType() != null ? ";电子面单:" + WlbTemplateTypeEnum.getNameByValue(userExpressWlbModel.getWlbTemplateType()) + ";" : "";
                    tradeTraceList.add(createTradeTrace(user, trade, "匹配快递", matchType+",快递公司:【" + userLogisticsCompanyName + "】" + (trade.getTemplateMatchRuleId() != null ? ",快递匹配规则id:" + trade.getTemplateMatchRuleId() : "") + platformName + TradeTraceListener.buildGxTradeFreightLog(trade), updateTime));
                } else {
                    tradeTraceList.add(createTradeTrace(user, trade, "匹配快递",matchType+":【"+userExpressWlbModel.getTemplateName()+"】"+(trade.getTemplateMatchRuleId()!=null ? ",快递匹配规则id:"+trade.getTemplateMatchRuleId() : "")+ TradeTraceListener.buildGxTradeFreightLog(trade), updateTime));
                }
            } else if(TradeUtils.isGxOrMixTrade(trade) && StringUtils.isNotBlank(trade.getGxFreightRuleName())){
                String matchType = trade.hasOpV(OpVEnum.TRADE_FX_REAPPOINT_TEMPLATE_ID)?"分销重新指定供销快递":"自动匹配快递";
                tradeTraceList.add(createTradeTrace(user, trade, "匹配快递",matchType+TradeTraceListener.buildGxTradeFreightLog(trade), updateTime));
            }
            if(isUpdateWarehouse(trade)){
                tradeTraceList.add(createTradeTrace(user, trade, "自动重算分仓", "自动重算分仓:" + trade.getOrigin().getWarehouseName() + "->" + trade.getWarehouseName(), updateTime));
            }
            if (isItemChanged(staff, trade) && !result.openPlatformToCommodity) {
                tradeTraceList.add(createItemChanged(user, trade, updateTime));
                itemChangedList.add(trade);
            }
            if (isItemMatched(trade)) {
                tradeUpdateItemMatchLog(trade);
                String content = trade.getOperations().get(OpEnum.ITEM_RELATION_BIND);
                if (StringUtils.isNotEmpty(content)) {
                    tradeTraceList.add(createTradeTrace(user, trade, OpEnum.ITEM_RELATION_BIND.getName(), trade.getOperations().get(OpEnum.ITEM_RELATION_BIND), updateTime));
                }
            }
            // 无需发货标签变更
            addRemoveNonConsignTradeTrace(user, trade, updateTime, tradeTraceList);
            //O2O订单信息变更日志
            addTradeO2oInfoChange(user, trade, updateTime, tradeTraceList);
            if (StringUtils.isNotEmpty(trade.getTradeTrace())) {
                tradeTraceList.add(createTradeTrace(user, trade, "订单同步处理订单", trade.getTradeTrace(), updateTime));
            }

            if (trade.getOrigin() !=null && StringUtils.isNotBlank(trade.getOrigin().getSaleFee()) && TradeStatusUtils.isWaitSellerSend(trade.getOrigin().getSysStatus()) && Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
                tradeTraceList.add(createTradeTrace(user, trade, "订单交易关闭","订单交易关闭清空订单分销金额", updateTime));
            }
            if(TradeExceptUtils.isPlatRiskExcept(trade)){
                if (TradeExceptUtils.isContainExcept(user.getStaff(), trade.getOrigin(), ExceptEnum.RISK) && !TradeExceptUtils.isContainExcept(user.getStaff(), trade, ExceptEnum.RISK)) {
                    tradeTraceList.add(createTradeTrace(user, trade, "同步平台风控状态，取消风控异常", updateTime));
                } else if (!TradeExceptUtils.isContainExcept(user.getStaff(), trade.getOrigin(), ExceptEnum.RISK) && TradeExceptUtils.isContainExcept(user.getStaff(), trade, ExceptEnum.RISK)) {
                    tradeTraceList.add(createTradeTrace(user, trade, "同步平台风控状态，标记风控异常", updateTime));
                }
                if (TradeExceptUtils.isContainExcept(staff, trade.getOrigin(), ExceptEnum.PDD_STOCK_OUT) && !TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PDD_STOCK_OUT)) {
                    tradeTraceList.add(createTradeTrace(user, trade, "同步平台缺货处理，取消缺货待处理异常", updateTime));
                } else if (!TradeExceptUtils.isContainExcept(staff, trade.getOrigin(), ExceptEnum.PDD_STOCK_OUT) && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.PDD_STOCK_OUT)) {
                    tradeTraceList.add(createTradeTrace(user, trade, "同步平台缺货已处理，标记缺货待处理异常", updateTime));
                }
            }
            String s = trade.getOperations().get(OpEnum.EXCEPT_UPDATE);
            if(StringUtils.isNotBlank(s)){
                tradeTraceList.add(createTradeTrace(user, trade,"订单同步处理异常",  s, updateTime));
            }

            Set<String> operationLogs = trade.getOperationSetLogs().get(OpEnum.EXCEPT_SYNC);
            if(CollectionUtils.isNotEmpty(operationLogs)){
                for(String operation:operationLogs){
                    tradeTraceList.add(createTradeTrace(user, trade,"订单同步处理异常",  operation, updateTime));
                }
            }

            if (trade.getOrigin() != null && !TradeExceptUtils.isEqualsExcept(staff, trade, trade.getOrigin(), ExceptEnum.ADDRESS_CHANGED)) {
                tradeTraceList.add(createTradeTrace(user, trade, "订单同步处理异常", String.format("%s [%s] 异常", TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.ADDRESS_CHANGED) ? "添加" : "取消", ExceptEnum.ADDRESS_CHANGED.getChinese()), updateTime));
            }
        }
        // 商品变更日志,开启白名单走新的
        if (MapUtils.isNotEmpty(result.itemChangeLogMap) && result.openPlatformToCommodity) {
            Map<Trade, String> itemChangeLogMap = result.itemChangeLogMap;
            Set<Map.Entry<Trade, String>> entries = itemChangeLogMap.entrySet();
            for (Map.Entry<Trade, String> entry : entries) {
                Trade trade = entry.getKey();
                String content = entry.getValue();
                tradeTraceList.add(createTradeTrace(user, trade, "订单同步", content, updateTime));
                recodeOpLog(opLogService, user.getStaff(), null, content, IpUtils.getMyIPLocal());
            }
        }
        // 未开启走旧的
        if(!result.openPlatformToCommodity){
            for (Trade trade : itemChangedList) {
                StringBuilder sb = new StringBuilder("自动覆盖商品，系统单号：").append(trade.getSid()).append(", 商品由");
                List<Order> currentOrders = TradeUtils.getOrders4Trade(trade);
                for (Order order : currentOrders) {
                    if (Trade.SYS_STATUS_WAIT_AUDIT.equals(order.getSysStatus()) && order.getOrigin() != null && OrderExceptUtils.isContainsExcept(staff,order.getOrigin(),ExceptEnum.ITEM_CHANGED)) {
                        String currentOuterId = StringUtils.isNotEmpty(order.getOuterSkuId()) ? order.getOuterSkuId() : order.getOuterId();
                        String originOuterId = StringUtils.isNotEmpty(order.getOrigin().getOuterSkuId()) ? order.getOrigin().getOuterSkuId() : order.getOrigin().getOuterId();
                        sb.append("【").append(originOuterId).append("】修改为").append("【").append(currentOuterId).append("】");
                    }
                }
                recodeOpLog(opLogService, user.getStaff(), null, sb.toString(), IpUtils.getMyIPLocal());
            }
        }
        if (!tradeTraceList.isEmpty()) {
            ClueIdUtil.fillClueId(staff);
            tradeTraceService.batchAddTradeTrace(staff, tradeTraceList);
        }
    }

    /**
     * action和content相同的操作日志
     * @param user
     * @param trade
     * @param action
     * @param insertTime
     * @return
     */
    TradeTrace createTradeTrace(User user, Trade trade, String action, Date insertTime) {
        return createTradeTrace(user, trade, action, action, insertTime);
    }

    TradeTrace createTradeTrace(User user, Trade trade, String action, Date insertTime, boolean recordOrders) {
        return createTradeTrace(user, trade, action, action, insertTime, recordOrders);
    }

    TradeTrace createTradeTrace(User user, Trade trade, String action, String content, Date insertTime) {
        return TradeTraceUtils.createTradeTraceWithTrade(user.getCompanyId(), trade, action, "系统", insertTime, content);
    }

    TradeTrace createTradeTrace(User user, Trade trade, String action, String content, Date insertTime, boolean recordOrders) {
        return TradeTraceUtils.createTradeTraceWithTrade(user.getCompanyId(), trade, action, "系统", insertTime, content, recordOrders);
    }


    /**
     * 是否包含赠品
     *
     * @param trade
     * @return
     */
    private boolean haveGiftOrder(Trade trade) {
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orderList)) {
            return false;
        }

        for (Order order : orderList) {
            if (order.getGiftNum() != null && order.getGiftNum() > 0) {
                return true;
            }
        }

        return false;
    }

    private String getTagNames(String ids, Staff staff){
        if(StringUtils.isBlank(ids)){
            return "";
        }
        Long[] exceptArr = Strings.getAsLongArray(ids,",",true);
        List<TradeTag> tagList = tradeTagService.queryByIds(staff, exceptArr);
        String tagNames;
        if(CollectionUtils.isNotEmpty(tagList)){
            List<String> exceptNames = tagList.stream().map(e->e.getTagName()).collect(Collectors.toList());
            tagNames = Strings.join(",",exceptNames);
        }else {
            tagNames = "";
        }
        return tagNames;
    }


    private String getTagNames(Set<Long> ids, Staff staff){
        if(CollectionUtils.isEmpty(ids)){
            return "";
        }
        List<TradeTag> tagList = tradeTagService.queryByIds(staff, ids.toArray(new Long[0]));
        String tagNames;
        if(CollectionUtils.isNotEmpty(tagList)){
            List<String> exceptNames = tagList.stream().map(e->e.getTagName()).collect(Collectors.toList());
            tagNames = Strings.join(",",exceptNames);
        }else {
            tagNames = "";
        }
        return tagNames;
    }


    private void processChangeMemoTrace(User user, Trade trade, List<TradeTrace> tradeTraceList) {
        //重新计算标签
        try {
            Staff staff = user.getStaff();
            String chatConfigs = tradeConfigService.get(staff).getChatConfigs();
            Integer recalculation = JSONObject.parseObject(chatConfigs).getInteger(TradeExtendConfigsEnum.CHANGE_MEMO_RECALCULATION_TAG.getKey());
            if (1 == recalculation) {
                //
                StringBuilder content = new StringBuilder();
                content.append("自动重算标记规则：标签：").append(getTagNames(trade.getOrigin().getTagIds(), staff)).append("->").append(getTagNames(trade.getTagIds(), staff))
                        .append("；自定义异常：").append(getTagNames(TradeExceptUtils.getCustomExceptIds(staff, trade.getOrigin()), staff)).append("->").append(getTagNames(TradeExceptUtils.getCustomExceptIds(staff, trade), staff))
                        .append("；便签：").append(trade.getOrigin().getSysMemo() == null ? "" : trade.getOrigin().getSysMemo()).append("->").append(trade.getSysMemo() == null ? "" : trade.getSysMemo());
                tradeTraceList.add(TradeTraceUtils.createTradeTraceWithTrade(staff, trade, "修改备注自动重算标签", staff.getName(), new Date(), content.toString()));
            }
        }catch (Exception e){
            logger.error("订单同步自动重算标签生成操作日志失败");
        }
    }

    private TradeTrace createAddressChanged(Staff staff, User user, Trade trade, Date updateTime){
        TradeTrace trace = createTradeTrace(user, trade, "平台自动覆盖地址", updateTime);
        Trade origin = trade.getOrigin();
        trace.setContent(changeAddressTraceBusiness.encode(staff, origin, trade, false));
        return trace;
    }

    private TradeTrace createRefundChanged(User user, Trade trade, Date updateTime) {
        TradeTrace trace = createTradeTrace(user, trade, OpEnum.SYNC_REFUND.getName(), updateTime, true);
        Trade origin = trade.getOrigin();
        if (origin == null) {
            trace.setContent(OpEnum.SYNC_REFUND.getName());
            logger.debug(String.format("sid=%s,退款状态增加校验,未获取到trade老数据!",trade.getSid()));
            return trace;
        }
        StringBuilder sb = new StringBuilder();
        String headStr=OpEnum.SYNC_REFUND.getName() + ":";
        sb.append(headStr);
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        List<Order> originOrders = TradeUtils.getOrders4Trade(origin);
        int tradeIsRefund = TradeExceptUtils.isContainExcept(user.getStaff(), trade, ExceptEnum.REFUNDING)?1:0;
        int originIsRefund = TradeExceptUtils.isContainExcept(user.getStaff(), origin, ExceptEnum.REFUNDING)?1:0;
        if (CollectionUtils.isEmpty(originOrders)) {
            sb.append(RefundStatusEnum.isRefundMsgMap.get(originIsRefund)).append("->").append(RefundStatusEnum.isRefundMsgMap.get(tradeIsRefund));
            trace.setContent(sb.toString());
            logger.debug(String.format("sid=%s,退款状态增加校验,未获取到originOrders老数据!",origin.getSid()));
            return trace;
        }
        if (CollectionUtils.isEmpty(orders)) {
            sb.append(RefundStatusEnum.isRefundMsgMap.get(originIsRefund)).append("->").append(RefundStatusEnum.isRefundMsgMap.get(tradeIsRefund));
            trace.setContent(sb.toString());
            logger.debug(String.format("sid=%s,退款状态增加校验,未获取到orders数据!",trade.getSid()));
            return trace;
        }
        Map<Long, Order> oidOrderMap = originOrders.stream().collect(Collectors.toMap(Order::getId, order -> order, (order1, order2) -> {
            return order1;
        }));
        for (Order order : orders) {
            Order originOrder = oidOrderMap.get(order.getId());
            if (originOrder != null) {
                String originRefundStatus = StringUtils.isBlank(originOrder.getRefundStatus()) ? null : originOrder.getRefundStatus();
                String refundStatus = StringUtils.isBlank(order.getRefundStatus()) ? null : order.getRefundStatus();
                originRefundStatus = RefundStatusEnum.refundStatusMsgMap.get(originRefundStatus);
                refundStatus = RefundStatusEnum.refundStatusMsgMap.get(refundStatus);
                if(Objects.equals(originRefundStatus,refundStatus)){
                    continue;
                }
                logger.debug(String.format("sid:%s,orderId:%s,子订单退款状态:%s->%s",originOrder.getSid(),originOrder.getId(), originRefundStatus, refundStatus));
                if (!Objects.equals(originRefundStatus,refundStatus)) {
                    sb.append("平台商品（").append(originOrder.getTitle());
                    if (StringUtils.isNotBlank(originOrder.getSkuPropertiesName())) {
                        sb.append(":").append(originOrder.getSkuPropertiesName());
                    }
                    sb.append("）退款状态发生变化")
                            .append(StringUtils.isNoneBlank(originRefundStatus) ?
                                    originRefundStatus : "").append("->")
                            .append(StringUtils.isNoneBlank(refundStatus) ?
                                    refundStatus : "").append(";");
                }
            } else {
                String log = String.format("sid:%s,orderId:%s,子订单退款状态: %s,oid: %s 未找到老的子订单.", order.getSid(), order.getId(), order.getRefundStatus(), order.getOid());
                logger.debug(log);
            }
        }
        if (sb.toString().endsWith(",") || sb.toString().endsWith(";")) {
            sb.deleteCharAt(sb.length() - 1);
        }
        trace.setContent(sb.toString());
        if(sb.length()>headStr.length()){
            return trace;
        }
        return null;
    }

    private TradeTrace createItemChanged(User user, Trade trade, Date updateTime){
        TradeTrace trace = createTradeTrace(user, trade, "平台商品发生变化", updateTime);
        StringBuilder sb = new StringBuilder();
        sb.append("平台商品发生变化");
        sb.append(",").append(getItemChangedInfo(user,trade));
        trace.setContent(sb.toString());
        return trace;
    }

    private TradeTrace createSellerMemoChanged(User user, Trade trade, Date updateTime){
        TradeTrace trace = createTradeTrace(user, trade, "平台自动覆盖备注", updateTime);
        Trade origin = trade.getOrigin();
        StringBuilder sb = new StringBuilder();
        sb.append("平台自动覆盖备注");
        if(origin != null){//理论上必须存在
            if (IGNORE_CHANGE_SELLER_FLAG_SOURCE.contains(trade.getSource())) {
                sb.append(",修改前:备注 ").append(StringUtils.isBlank(getSellerMemoInfo(origin))?"无备注":getSellerMemoInfo(origin));
                sb.append(";修改后:备注 ").append(StringUtils.isBlank(getSellerMemoInfo(trade))?"无备注":getSellerMemoInfo(trade));
            } else {
                sb.append(",修改前:备注 ").append(StringUtils.isBlank(getSellerMemoInfo(origin))?"无备注":getSellerMemoInfo(origin)).append(" ,旗帜: ").append(origin.getSellerFlag()==null?"无旗帜":TradeTagUtils.getFlagColor(origin.getSellerFlag()));
                Long afterFlag = trade.getSellerFlag();
                sb.append(";修改后:备注 ").append(StringUtils.isBlank(getSellerMemoInfo(trade))?"无备注":getSellerMemoInfo(trade)).append(" ,旗帜: ").append(afterFlag ==null?"无旗帜": TradeTagUtils.getFlagColor(afterFlag));
            }
        }
        trace.setContent(sb.toString());
        return trace;
    }

    private TradeTrace craeteSellerMemoNotChange(User user, Trade trade, Date updateTime) {
        TradeTrace trace = createTradeTrace(user, trade, "系统手动修改备过注平台不能覆盖备注", updateTime);
        StringBuilder sb = new StringBuilder();
        sb.append("订单系统中已手工修改过备注：").append(isExistPlatformSellerMemo(trade, true) ? trade.getSellerMemo() : "无备注").append(" ,旗帜：").append(null != trade.getSellerFlag() ? TradeTagUtils.getFlagColor(trade.getSellerFlag()) : "无旗帜");
        sb.append("不会再次同步平台备注：").append(isExistPlatformSellerMemo(trade, false) ? trade.getPlatformSellerMemo() : "无备注").append(" ,旗帜：").append(null != trade.getPlatformFlag() ? TradeTagUtils.getFlagColor(trade.getPlatformFlag()) : "无旗帜");
        trace.setContent(sb.toString());
        return trace;
    }

    private StringBuilder getAddressInfo(Trade trade){
        StringBuilder sb = new StringBuilder();
        sb.append(trade.getReceiverName()).append(" ")
                .append(!StringUtils.isEmpty(trade.getReceiverMobile()) ? trade.getReceiverMobile(): trade.getReceiverPhone()).append(" ")
                .append(trade.getReceiverState()).append(trade.getReceiverCity())
                .append(trade.getReceiverDistrict()).append(trade.getReceiverAddress())
                .append(",").append(trade.getReceiverZip());
        return sb;
    }

    private StringBuilder getSellerMemoInfo(Trade trade){
        StringBuilder sb = new StringBuilder();
        sb.append(trade.getSellerMemo());
        return sb;
    }

    private boolean isExistPlatformSellerMemo(Trade trade, boolean bool) {
        if (bool && StringUtils.isNotBlank(trade.getSellerMemo())) {
            return true;
        }
        if (!bool && StringUtils.isNotBlank(trade.getPlatformSellerMemo())) {
            return true;
        }
        return false;
    }

    private StringBuilder getItemChangedInfo(User user,Trade trade){
        StringBuilder beforeSb = new StringBuilder("修改前:");
        StringBuilder afterSb = new StringBuilder("修改后:");
        List<Order> currentOrders = TradeUtils.getOrders4Trade(trade);
        String splitChar = "";
        for (Order order : currentOrders) {
            if (ifNeedRecordItemChange(trade,order) && order.getOrigin() != null && (OrderExceptUtils.isContainsExcept(user.getStaff(), order.getOrigin(), ExceptEnum.ITEM_CHANGED) || order.isSyncItemChanged())) {
                String currentOuterId = StringUtils.isNotEmpty(order.getOuterSkuId()) ? order.getOuterSkuId() : StringUtils.defaultIfBlank(order.getOuterId(),order.getOuterIid());
                String originOuterId = StringUtils.isNotEmpty(order.getOrigin().getOuterSkuId()) ? order.getOrigin().getOuterSkuId() : StringUtils.defaultIfBlank(order.getOrigin().getOuterId(),order.getOrigin().getOuterIid()) ;
                if(originOuterId == null) {
                    originOuterId = "";
                }
                if(currentOuterId == null) {
                    currentOuterId = "";
                }
                beforeSb.append(splitChar).append(" ").append(order.getOrigin().getSkuPropertiesName()).append("【").append(originOuterId).append("】");
                afterSb.append(splitChar).append(" ").append(order.getSkuPropertiesName()).append("【").append(currentOuterId).append("】");
                splitChar = ",";
            }
        }
        beforeSb.append(",").append(afterSb);
        return beforeSb;
    }

    private boolean isItemChanged(Staff staff, Trade trade) {
        for (Order order : TradeUtils.getOrders4Trade(trade)) {
            if (ifNeedRecordItemChange(trade,order) && order.getOrigin() != null && (OrderExceptUtils.isContainsExcept(staff, order.getOrigin(), ExceptEnum.ITEM_CHANGED) || order.isSyncItemChanged())) {
                return true;
            }
        }
        return false;
    }

    //商品由未匹配到匹配
    private boolean isItemMatched(Trade trade) {
        boolean itemMatched = false;
        for (Order order : TradeUtils.getOrders4Trade(trade)) {
            if (order.getOrigin() == null) {
                continue;
            }
            Order originOrder = order.getOrigin();//获取更新前的商品信息。
            if (Optional.ofNullable(originOrder.getItemSysId()).orElse(0L) <= 0 && order.getItemSysId() > 0) {
                OrderUtils.addItemMatchLog(order);
                itemMatched = true;
            }
            if (originOrder.isSuit()) {//如果是套件,被转了单品的话,originOrder是被匹配的套件本身。originOrder.getOrigin()才是更新前的商品信息
                Order suitOriginOrder = originOrder.getOrigin();//获取更新前的商品信息。
                if (suitOriginOrder != null && Optional.ofNullable(suitOriginOrder.getItemSysId()).orElse(0L) <= 0 && originOrder.getItemSysId() > 0) {
                    OrderUtils.addItemMatchLog(order);
                    itemMatched = true;
                }
            }
        }
        return itemMatched;
    }

    public void tradeUpdateItemMatchLog(Trade trade) {
        String s = TradeUtils.getOrders4Trade(trade).stream().filter(o -> o.getOperations().containsKey(OpEnum.ITEM_RELATION_BIND_SUCCESS) && !o.getOperations().containsKey(OpEnum.ITEM_KEYWORD_BIND)).map(o -> o.getOperations().get(OpEnum.ITEM_RELATION_BIND_SUCCESS)).collect(Collectors.joining(","));
        StringBuilder logs = new StringBuilder("匹配系统商品: ");
        if (!s.isEmpty()) {
            logs.append(s);
        }
        if (!StringUtils.isEmpty(s)) {
            trade.getOperations().put(OpEnum.ITEM_RELATION_BIND, logs.toString());
        }
    }

    /**
     * 非分销类型的order 只记录待审核的，分销类型的：待审核和已审核两个状态
     *
     * @param trade
     * @param order
     * @return
     */
    private boolean ifNeedRecordItemChange(Trade trade,Order order){
        if(!TradeUtils.isFxSource(trade)){
            return Trade.SYS_STATUS_WAIT_AUDIT.equals(order.getSysStatus());
        }
        return Trade.SYS_STATUS_WAIT_AUDIT.equals(order.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(order.getSysStatus());
    }


    private void recodeOpLog(IOpLogService opLogService, Staff staff, Object args, String content, String clientIp) {
        OpLog log = new OpLog();
        log.setArgs(JSON.toJSONString(args));
        log.setStaffId(staff.getId());
        log.setStaffName(staff.getName());
        log.setAccountName(staff.getAccountName());
        log.setContent(content);
        log.setAction("saveTradeTagRule");
        log.setCompanyId(staff.getCompanyId());
        log.setDomain(OpLog.DOMAIN_TRADE);
        log.setIp(clientIp != null ? clientIp : "0.0.0.0");
        opLogService.record(staff, log);
    }


    private boolean isUpdateRefundStatus(Staff staff,Trade trade) {
        if (trade == null || trade.getOrigin() == null) {
            return false;
        }
        Trade origin = trade.getOrigin();
        if (!TradeExceptUtils.isEqualsExcept(staff,origin,trade,ExceptEnum.REFUNDING)) {
            return true;
        }
        return false;
    }

    /**
     * 是否修改过买家留言
     * @param trade
     * @return
     */
    public boolean isUpdateTradeBuyerMessage(Trade trade){
        if (trade == null || trade.getOrigin() == null  || trade.getBuyerMessage() == null) {
            return false;
        }
        //增加为null与为""的时候不打印日志
        if((trade.getBuyerMessage() == null || trade.getBuyerMessage().equals("")) &&
                (trade.getOrigin().getBuyerMessage() == null || trade.getOrigin().getBuyerMessage().equals(""))){
            return false;
        }
        return !trade.getOrigin().getBuyerMessage().equals(trade.getBuyerMessage());
    }



    /**
     * 是否同步时对收货地址进行了修改
     * @param trade
     * @return
     */
    private boolean isUpdateReceivedAddress(Trade trade){
        if(trade == null || trade.getOrigin() == null){
            return false;
        }
        if(TradeAddressUtil.isSpecialAddress(trade)){
            // 特殊地址处理 https://tb.raycloud.com/task/629465071902a00076443c2e
            TradeAddressUtil.processNoDistrictsCityAddress(trade);
            TradeAddressUtil.processMunicipalityCountyAddress(trade);
        }
        return compareAddressByTrade(trade);
    }

    private boolean compareAddressByTrade(Trade trade) {
        Trade origin = trade.getOrigin();
        //兼容京东vc历史地址信息
        compatibleJdvcOriginReceiverDistrict(origin);
        if(TradeAddressUtil.isSpecialAddress(trade)){
            // 特殊地址处理 https://tb.raycloud.com/task/629465071902a00076443c2e
            TradeAddressUtil.processNoDistrictsCityAddress(trade);
            TradeAddressUtil.processMunicipalityCountyAddress(trade);
        }
        if(!addressStrEquals(trade.getReceiverState(),origin.getReceiverState())){
            return true;
        }
        if(!addressStrEquals(trade.getReceiverCity(),origin.getReceiverCity())){
            return true;
        }
        if(!addressStrEquals(trade.getReceiverDistrict(),origin.getReceiverDistrict())){
            return true;
        }
        if(!addressStrEquals(trade.getReceiverAddress(),origin.getReceiverAddress())){
            return true;
        }

        if(!addressStrEquals(trade.getReceiverName(),origin.getReceiverName())){
            return true;
        }

        if(!addressStrEquals(trade.getReceiverMobile(),origin.getReceiverMobile())){
            return true;
        }

        if(!addressStrEquals(trade.getReceiverPhone(),origin.getReceiverPhone())){
            return true;
        }

        if(trade.getReceiverZip() != null && !addressStrEquals(trade.getReceiverZip(),origin.getReceiverZip())) {//trade的zip可能为null，见SyncCoverBusiness逻辑
            return true;
        }
        return false;
    }

    private void compatibleJdvcOriginReceiverDistrict(Trade origin) {
        if (StringUtils.equals(origin.getSource(), CommonConstants.PLAT_FORM_TYPE_JD_VC)) {
            //旧数据receiver_country有值
            if (StringUtils.isNotBlank(origin.getReceiverCountry()) && !"null".equalsIgnoreCase(origin.getReceiverCountry())) {
                //新进来的数据 receiver_district 有值
                if (StringUtils.isNotBlank(origin.getReceiverDistrict())) {
                    origin.setReceiverDistrict(origin.getReceiverCountry()); //存在的receiver_country赋值给receiver_district
                    origin.setReceiverCountry(null); //清空原有receiver_country
                }
                //剩下的就交给下面的判断逻辑做判断吧。
            }
        }
    }

    private boolean addressStrEquals(String str1, String str2) {
        return StringUtils.trimToEmpty(str1).replaceAll(" ","").equals(StringUtils.trimToEmpty(str2).replaceAll(" ",""));
    }

    /**
     * 是否同步时对商品属性进行了修改
     * @param trade
     * @return
     */
    private boolean isUpdateOrderProperties(Staff staff,Trade trade){
        if(trade != null && (Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus()) || Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getSysStatus()))){
            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
            for(Order order : orderList){
                if(OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.ITEM_CHANGED)){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否对订单的状态进行改变
     * 对以下几种状态的变更：
     *  待付款到已付款
     已付款到已发货
     已付款到交易成功
     已付款到交易关闭
     已发货到交易成功
     * @param trade
     * @return
     */
    private boolean isUpdateTradeStatus(Trade trade){
        if (trade == null || trade.getOrigin() == null) {
            return false;
        }
        return !trade.getOrigin().getStatus().equals(trade.getStatus());
    }

    /**
     * 是否匹配模版
     * @param trade
     * @return
     */
    private boolean isUpdateTemplate(Trade trade){
        if(trade == null || trade.getOrigin() == null){
            return false;
        }
        Trade origin = trade.getOrigin();

        if(trade.getTemplateId() == null || trade.getTemplateId() <= 0){
            return false;
        }

        if(origin.getTemplateId() != null && origin.getTemplateId() > 0 && trade.getTemplateId()!=null && trade.getTemplateId().equals(origin.getTemplateId())){
            return false;
        }


        return true;

    }

    /**
     * 是否匹配赠品
     * @param trade
     * @return
     */
    private boolean isMatchGift(Trade trade){
        if(trade == null || trade.getOrigin() == null){
            return false;
        }

        if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getOldSysStatus()) && TradeStatusUtils.isWaitAudit(trade.getSysStatus())){
            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
            if(CollectionUtils.isEmpty(orderList)){
                return false;
            }
            for(Order order : orderList){
                if(order.getGiftNum() != null && order.getGiftNum() > 0){
                    return true;
                }
            }
        }

        return false;
    }


    public void giftSaveLog(Staff staff, List<Trade> trades,List<Order> giftOrders,String ip) {
        if (CollectionUtils.isEmpty(trades) || giftOrders.isEmpty()) {
            return;
        }
        Set<String> sysOuterId = giftOrders.stream().map(Order::getOuterId).collect(Collectors.toSet());
        String join = Strings.join(",", sysOuterId);
        //新增手工赠品事件
        eventCenter.fireEvent(this, new EventInfo("trade.manual.gift.save").setArgs(new Object[]{staff,join}), trades);
        saveLog(staff, trades.get(0).getSid() + "", StringUtils.join(TradeUtils.toSids(trades)) + "手工添加赠品:"+Strings.join(",",sysOuterId),ip);
    }

    public void saveLog(Staff staff, String key, String content,String ip) {
        OpLog opLog = new OpLog();
        opLog.setCompanyId(staff.getCompanyId());
        opLog.setStaffId(staff.getId());
        opLog.setAccountName(staff.getAccountName());
        opLog.setStaffName(staff.getName());
        opLog.setIp(ip);
        opLog.setDomain(Domain.TRADE);
        opLog.setAction("platform_save");
        opLog.setKey(key);
        opLog.setContent(content);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(staff).append(content));
        }
        opLog.setCreated(new Date());
        opLogService.record(staff, opLog);
    }

    /**
     * 是否更新仓库
     * @param trade
     * @return
     */
    private boolean isUpdateWarehouse(Trade trade){
        if(trade == null || trade.getOrigin() == null){
            return false;
        }
        Trade origin = trade.getOrigin();
        if(trade.getWarehouseId() == null || trade.getWarehouseId() <= 0){
            return false;
        }
        if(origin.getWarehouseId() != null && origin.getWarehouseId() > 0 && trade.getWarehouseId()!=null && trade.getWarehouseId().equals(origin.getWarehouseId())){
            return false;
        }
        return true;
    }

    public void feedBackPlatformWarehouse(Staff staff, Trade trade, String msg){
        List<Trade> list = new ArrayList<>();
        Trade feedbackTrade = new Trade();
        feedbackTrade.setSid(trade.getSid());
        feedbackTrade.getOperations().put(FEED_BACK_PLATFORM_WAREHOUSE, msg + ",系统单号:" + trade.getSid());
        list.add(feedbackTrade);
        asyncTrace(staff,list , FEED_BACK_PLATFORM_WAREHOUSE);
    }

    public void addRemoveNonConsignTradeTrace(User user, Trade trade, Date updateTime, List<TradeTrace> tradeTraces) {
        Set<String> sysOuterIds = TradeUtils.getOrders4Trade(trade).stream().filter(order->!order.ifNonConsign())
                .map(Order::getOrigin)
                .filter(Objects::nonNull)
                .filter(Order::ifNonConsign)
                .map(Order::getSysOuterId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(sysOuterIds)) {
            tradeTraces.add(createTradeTrace(user, trade, "取消无需发货", "取消无需发货:" + sysOuterIds, updateTime));
        }
    }

    public void saveTrace(Staff staff, Trade trade, String msg, OpEnum opEnum) {
        List<Trade> list = new ArrayList<>();
        Trade feedbackTrade = new Trade();
        feedbackTrade.setSid(trade.getSid());
        feedbackTrade.getOperations().put(opEnum, msg + ",系统单号:" + trade.getSid());
        list.add(feedbackTrade);
        asyncTrace(staff, list, opEnum);
    }

    private void addTradeO2oInfoChange(User user, Trade trade, Date updateTime, List<TradeTrace> tradeTraces) {
        if (TradeO2oTraceUtils.isTradeO2oStatusChange(trade)) {
            tradeTraces.add(createTradeTrace(user, trade, TRADE_O2O_STATUS_CHANGE.getName(), TradeO2oTraceUtils.builderTradeO2oStatusChangeContent(trade), updateTime));
        }
        if (TradeO2oTraceUtils.isTradeO2oException(trade)) {
            tradeTraces.add(createTradeTrace(user, trade, TRADE_O2O_EXCEPTION.getName(), TradeO2oTraceUtils.builderTradeO2oExceptionContent(trade), updateTime));
        }
    }
}

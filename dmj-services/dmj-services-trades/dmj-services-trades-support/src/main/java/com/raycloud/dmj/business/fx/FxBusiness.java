package com.raycloud.dmj.business.fx;

import com.alibaba.dubbo.rpc.RpcException;
import com.alibaba.fastjson.*;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.google.common.base.Joiner;
import com.google.common.collect.*;
import com.raycloud.cache.*;
import com.raycloud.dmj.*;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.audit.AuditFxBusiness;
import com.raycloud.dmj.business.audit.help.AuditFxUtils;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.operate.CancelBusiness;
import com.raycloud.dmj.business.order.OrderModifyLogBusiness;
import com.raycloud.dmj.business.payment.TradeSuitCalculateBusiness;
import com.raycloud.dmj.business.payment.support.PaymentCalculateSupports;
import com.raycloud.dmj.business.split.*;
import com.raycloud.dmj.business.trade.*;
import com.raycloud.dmj.business.warehouse.WarehouseAllocateBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dao.trade.*;
import com.raycloud.dmj.dms.domain.basic.QueryDmsPriceSourceEnum;
import com.raycloud.dmj.dms.domain.dto.*;
import com.raycloud.dmj.dms.request.*;
import com.raycloud.dmj.dms.request.distributor.*;
import com.raycloud.dmj.dms.request.scm.*;
import com.raycloud.dmj.dms.response.ResponseInfo;
import com.raycloud.dmj.dms.response.distributor.*;
import com.raycloud.dmj.dms.response.scm.ChangeDistributionItemByErpItemResponse;
import com.raycloud.dmj.dms.service.trade.api.*;
import com.raycloud.dmj.domain.*;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.base.AttrCopier;
import com.raycloud.dmj.domain.constant.*;
import com.raycloud.dmj.domain.diamond.*;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.pt.enums.EnumOutSidStatus;
import com.raycloud.dmj.domain.trade.config.*;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.history.OrderModifyLogUtils;
import com.raycloud.dmj.domain.trade.split.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.audit.*;
import com.raycloud.dmj.domain.trades.fx.*;
import com.raycloud.dmj.domain.trades.fx.refreshPrice.TradeRefreshParams;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.utils.PaymentUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.enums.DmsItemMatchErrorEnum;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.newfx.trades.*;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.pt.IExpressTemplateCommonService;
import com.raycloud.dmj.services.request.QuerySysOuterIdByItemInfoRequest;
import com.raycloud.dmj.services.trade.audit.ITradeAuditService;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.*;
import com.raycloud.dmj.services.trades.fx.IFxSupplierSrcService;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.trades.support.TradeUpdateService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.services.utils.item.TradeItemContext;
import com.raycloud.ec.api.*;
import com.raycloud.erp.db.router.DbContextHolder;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.*;
import org.apache.log4j.Priority;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.*;
import org.springframework.transaction.support.*;
import org.springframework.util.Assert;

import javax.annotation.*;
import java.beans.Introspector;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.TradeConstants.TYPE_TRADE_EXCHANGE;
import static com.raycloud.dmj.domain.TradeConstants.TYPE_TRADE_REISSUE;

/**
 * Created by windy26205 on 19/12/30.
 */
@Service
public class FxBusiness {

    @Resource
    IDmsTradeService dmsTradeService;

    @Resource
    IdWorkerService idWorkerService;

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    IDmsScmFxService dmsScmFxService;

    @Resource
    TbOrderDAO tbOrderDAO;

    @Resource
    private IExpressTemplateCommonService expressTemplateCommonService;

    @Resource
    private ITradeService tradeService;

    @Resource
    private CancelBusiness cancelBusiness;

    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    private ITradeAuditService tradeAuditService;

    @Resource
    private TradeUpdateService tradeUpdateService;

    @Resource
    private ITradePtService tradePtService;

    @Resource
    IItemServiceDubbo iItemServiceDubbo;

    @Resource
    private IStaffService staffService;

    @Resource
    WarehouseAllocateBusiness warehouseAllocateBusiness;

    @Resource
    SysTradeDmsBusiness sysTradeDmsBusiness;

    @Resource
    private ILockService lockService;

    @Resource
    TradeLockBusiness tradeLockBusiness;

    @Resource
    PlatformTransactionManager transactionManager;

    @Resource
    ISharedCache sharedCache;

    @Resource
    private IEventCenter eventCenter;

    @Resource
    private ITradeConfigService tradeConfigService;
    @Resource
    private OrderModifyLogBusiness orderModifyLogBusiness;
    @Resource
    private FxTradeNotifyBusiness fxTradeNotifyBusiness;
    @Resource
    IOrderStockService orderStockService;
    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;

    @Resource
    TbTradeDao tbTradeDao;

    @Resource
    ApplicationContext applicationContext;
    FxBusiness beanProxy;

    @Resource
    SharedCacheBusiness sharedCacheBusiness;

    @Resource
    protected TradeTraceBusiness tradeTraceBusiness;

    @Resource
    PaymentCalculateSupports paymentCalculateSupports;
    @Resource
    ITradeConfigNewService tradeConfigNewService;

    @Resource
    private GxReCalculateBusiness gxReCalculateBusiness;
    @Resource
    TradeSuitCalculateBusiness tradeSuitCalculateBusiness;

    @Resource
    FxItemBindBusiness fxItemBindBusiness;

    @Resource
    IDmsTradeDistributorDubbo dmsTradeDistributorDubbo;
    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    AuditFxBusiness auditFxBusiness;
    @Resource
    QiMenFxCashFlowBusiness qiMenFxCashFlowBusiness;


    private final SimplePropertyPreFilter ITEM_REQUEST_JSON_FILTER = new SimplePropertyPreFilter("userId", "numIid", "skuId", "title", "outerIid", "outerSkuId", "companyId", "erpSysItemId", "erpSysSkuId", "supplierCompanyId", "supplierBaseItemId", "supplierSkuId", "distributionPlatformItemInfo");


    @Autowired(required = false)
    private IFxSupplierSrcService fxSupplierSrcService;

    public static final int NORMAL = 1;//正常订单

    public static final int SAME_SUPPLY = 2;//同供应商

    public static final int AMBIGUITY_SUPPLY = 3;//不明确供应商

    public static final String DELAY_SYNC_CACHE_PREFIX = "trade_fx_delay_sync_";

    /**
     * key:type
     * value: 作废原因
     */
    public static final Map<Integer,String> CONSUME_TRADE_FXCANCEL_TYPE_2_MSG_MAP = Maps.newHashMapWithExpectedSize(2);

    public static final String FX_STOCK_CLUE_ID_LIST   = "fx_stock_clue_id_list";

    public static final String GX_ITEM_STOCK_CACHE_PREFIX   = "gx_item_stock_cache_";

    AttrCopier<Trade, Trade> tradeCopier = new TradeCopier<Trade, Trade>();
    private final Logger logger = Logger.getLogger(this.getClass());

    static {
        CONSUME_TRADE_FXCANCEL_TYPE_2_MSG_MAP.put(1,"分销合单,自动作废供销订单!");
        CONSUME_TRADE_FXCANCEL_TYPE_2_MSG_MAP.put(2,"分销商作废分销订单，自动作废供销订单!");
    }


    @PostConstruct
    public void startup() {
        beanProxy = (FxBusiness) applicationContext.getBean(Introspector.decapitalize(this.getClass().getSimpleName()));
    }


    /**
     *
     * 1688_fx 分销订单 把订单的 distributorLoginId 转成 distributorCompanyId
     *
     *
     * 1.查询已经存在的的1688分销列表
     * 2.不存在的就创建分销关系
     * 3.设置好分销商
     * 4.设置好分销价
     * 5.商品不是分销商品，获取不到分销价记录tradeTrace
     *
     * @param user
     * @param normalTrades
     */
    public void handleAlibabaFx(User user, List<Trade> normalTrades) {
        List<Trade> alibabaFxTrades = normalTrades.stream()
                .filter(t -> StringUtils.equals(t.getSource(), CommonConstants.PLAT_FORM_TYPE_1688_FX) && StringUtils.isNotBlank(t.getSourceName()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(alibabaFxTrades)){
            return;
        }

        FxLogBuilder fxLogBuilder = FxLogBuilder.fx(user.getStaff()).append("handleAlibabaFx").startTimer();


        Set<String> distributorLoginIds = alibabaFxTrades.stream()
                .map(Trade::getSourceName).collect(Collectors.toSet());

        Map<String,Long> distributorLoginId2CompanyIdMap = Maps.newHashMapWithExpectedSize(8);


        DmsDistributorInfoRequest request = new DmsDistributorInfoRequest();
        request.setSupplierCompanyId(user.getCompanyId());
        request.setType(5);
        List<DmsDistributorInfoDto> dmsDistributorInfoDtos = dmsTradeService.queryDmsDistributorInfoList(request);
        fxLogBuilder.recordTimer("queryDmsDistributorInfoList");
        if(CollectionUtils.isNotEmpty(distributorLoginIds)){
            distributorLoginId2CompanyIdMap.putAll(dmsDistributorInfoDtos.stream().filter(t->StringUtils.isNotBlank(t.getBusinessUserId())).collect(Collectors.toMap(DmsDistributorInfoDto::getBusinessUserId,DmsDistributorInfoDto::getDistributorCompanyId,(a,b)->a)));
            distributorLoginIds.removeAll(distributorLoginId2CompanyIdMap.keySet());
        }


        if(CollectionUtils.isNotEmpty(distributorLoginIds)){
            List<DmsAddDistributorRequest> reqList = new ArrayList<>();
            distributorLoginIds.forEach(id->{
                DmsAddDistributorRequest req = new DmsAddDistributorRequest();
                req.setSupplierCompanyId(user.getCompanyId());
                req.setDistributorCompanyName(id);
                req.setBusinessUserId(id);
                req.setType(5);
                reqList.add(req);
            });

            DmsBatchAddDistributorRequest distributorRequest = new DmsBatchAddDistributorRequest();
            distributorRequest.setRequests(reqList);
            ResponseInfo<DmsBatchAddDistributorResponse> resp = dmsTradeDistributorDubbo.dmsBatchAddOrUpdateDistributor(distributorRequest);
            fxLogBuilder.recordTimer("dmsBatchAddOrUpdateDistributor");
            if(resp == null || resp.getData() == null || CollectionUtils.isEmpty(resp.getData().getResponseList())){
                logger.warn(LogHelper.buildUserLog(user, String.format("分销商创建发生异常，request：%s,responseList：%s", JSONObject.toJSONString(request), JSONObject.toJSONString(resp))));
            } else {
                List<DmsAddDistributorResponse> responseList = resp.getData().getResponseList();
                responseList.forEach(r->{
                    // key 最好是：businessUserId，公司名称可能会改。 暂时没有返回
                    distributorLoginId2CompanyIdMap.put(r.getDistributorCompanyName(),r.getDistributorCompanyId());
                });
                logger.debug(LogHelper.buildUserLog(user, String.format("分销商创建，request:%s,responseList:%s", JSONObject.toJSONString(request), JSONObject.toJSONString(responseList))));
            }
        }


        alibabaFxTrades.forEach(t->{
            Long sourceId = distributorLoginId2CompanyIdMap.get(t.getSourceName());
            if(sourceId == null){
                fxLogBuilder.append("未匹配到分销商的tid",t.getTid());
            }else {
                t.setSourceId(sourceId);
                t.setDestId(user.getCompanyId());
            }
        });
        caculateGxPrice(user.getStaff(),alibabaFxTrades);
        fxLogBuilder.recordTimer("caculateGxPrice4AlibabaFxTrades");

        alibabaFxTrades.stream()
                .filter(t->TradeUtils.getOrders4Trade(t).stream().anyMatch(order ->!order.hasOpV(OpVEnum.ORDER_MATCH_FX_PRICE)))
                .forEach(t->{
                    String outerIidStr = TradeUtils.getOrders4Trade(t).stream().filter(order ->!order.hasOpV(OpVEnum.ORDER_MATCH_FX_PRICE))
                            .map(o->StringUtils.defaultIfBlank(o.getOuterSkuId(),o.getOuterIid())).collect(Collectors.joining(","));
                    t.getOperations().put(OpEnum.FX_PRICE_NOT_MATCH,String.format("%s,商品不是分销商品，获取不到分销价，请设置商品为可分销商品后，再重算资金流水，重新匹配最新分销价",outerIidStr));
                });

        fxLogBuilder.appendTook(DevLogBuilder.isDevEnv()?0L: 2000L).printWarn(logger);
    }


    /**
     * 1. 商品未匹配的设置平台商品信息
     * 2. 调用SCM接口绑定供销商品  此接口会绑定供分销商品关系，如果商品未匹配同时创建平台商品跟系统商品对应关系
     * 3. 如果分销是商品未匹配，处理未匹配异常 tradeService.matchItemByRelation
     * 4. 商品匹配后，如果一个订单存在多个商品未匹配需要设置order级别的分销属性
     * 5. controllerParams 后续新增分销属性要排除商品未匹配异常
     * 优先走：skuId 模式。可能存在outrSkuId,outerIid 都为空
     * key: skuId                   text: skuId  如果 text=9007199254740992 提示错误
     * key: originPlatformOuterId   text: outerSkuId,outerIid
     *
     * @param controllerParams
     * @param itemRequest
     * @param currentSid
     */
    public void changeDistributionItem(TradeControllerParams controllerParams, ChangeDistributionItemByErpItemRequest itemRequest, Long currentSid) {

        Assert.notNull(currentSid, "请输入订单号!");

        Staff staff = itemRequest.getStaff();
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, currentSid);
        Assert.notEmpty(trades, "sid=" + currentSid + "订单不存在");
        // skuId 等于Constants.FxDefaultSkuId 直接报错
        if (StringUtils.equals(controllerParams.getKey(), "skuId") && StringUtils.equals(Constants.FxDefaultSkuId + "", controllerParams.getText())) {
            throw new IllegalArgumentException("数据错误");
        }

        boolean ifItemUnallocated = false;
        // 商品未匹配才需要设置DistributionPlatformItemInfo
        if (Objects.nonNull(itemRequest.getErpSysItemId()) && itemRequest.getErpSysItemId() <= 0) {
            ifItemUnallocated = true;
            Long userId = trades.get(0).getUserId();
            TradeUtils.getOrders4Trade(trades).stream()
                    .filter(o -> (StringUtils.equals(controllerParams.getKey(), "originPlatformOuterId") && StringUtils.equals(o.getOuterSkuId(), controllerParams.getText()) || StringUtils.equals(o.getOuterIid(), controllerParams.getText())
                            || (StringUtils.equals(controllerParams.getKey(), "skuId") && StringUtils.equals(o.getSkuId(), controllerParams.getText())))
                    ).findFirst().map(o -> {
                        DistributionPlatformItemInfo itemInfo = new DistributionPlatformItemInfo();
                        itemInfo.setUserId(userId);
                        itemInfo.setNumIid(o.getNumIid());
                        itemInfo.setSkuId(o.getSkuId());
                        itemInfo.setTitle(o.getTitle());
                        itemInfo.setOuterIid(o.getOuterIid());
                        itemInfo.setOuterSkuId(o.getOuterSkuId());
                        itemInfo.setPicPath(o.getPicPath());
                        itemInfo.setSkuPropertiesName(o.getSkuPropertiesName());
                        itemRequest.setDistributionPlatformItemInfo(itemInfo);
                        return o;
                    });
        }
        Assert.isTrue(!(ifItemUnallocated && itemRequest.getDistributionPlatformItemInfo() == null), "没有找到对应的平台商品信息");
        logger.info(new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).append("changeDistributionItemByErpItem", JSON.toJSONString(itemRequest, ITEM_REQUEST_JSON_FILTER)).append("currentSid", currentSid).toString());
        ChangeDistributionItemByErpItemResponse response = LogKit.took(() -> dmsScmFxService.changeDistributionItemByErpItem(itemRequest), staff, 2000, LogKit.getFullMethodName(dmsScmFxService, "changeDistributionItemByErpItem"), logger);
        Assert.isTrue(response.isSuccess(), String.format("商品转为分销商品失败,原因：%s", response.getErrorMsg()));

        // 商品未匹配的处理商品未匹配
        // 不需要count,不需要trade.order 不需要查询合单 不需要fill 不需要filter  只查询未作废
        // 查询条件： 有商品未匹配异常 && 小表 && 排除分销订单 && 状态： 待付款，待审核，已审核，待财审
        controllerParams.setExcludeTradeType("33");
        controllerParams.setSysStatus(String.format("%s,%s,%s,%s", Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT, Trade.SYS_STATUS_FINISHED_AUDIT, Trade.SYS_STATUS_WAIT_FINANCE_AUDIT));
        if (ifItemUnallocated) {
            TradeQueryParams params = TradeQueryParams.copyParams(controllerParams);

            params.setQueryId(SystemTradeQueryParamsContext.QUERY_UN_CONSIGNED).setNeedFill(false).setIsCancel(0)
                    .setQueryFlag(1).setAllowedPgl(true).setUndoMergeFill(true).setBreakQuery(true).setQueryOrder(false)
                    .setFields("t.sid,t.tid,t.split_sid,t.user_id,t.merge_sid").setIgnoreFilter(1)
                    .setPage(new Page(1, 50000));
            params.setExceptionStatus(TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED);
            trades = tradeSearchService.search(staff, params).getList();

            if (CollectionUtils.isEmpty(trades)) {
                return;
            }

            Lists.partition(trades, 500).forEach(list -> {
                tradeService.matchItemByRelation(staff, TradeUtils.toSids(list));
            });

            // 商品匹配后，如果一个订单存在多个商品未匹配需要设置order级别的分销属性
            trades = tradeSearchService.search(staff, params).getList();
            Lists.partition(trades, 500).forEach(list -> {
                Long[] sids = TradeUtils.toSids(list);
                lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> {
                    params.setSid(sids);
                    List<Trade> tradesUnallocated = tradeSearchService.search(staff, params).getList();
                    if (CollectionUtils.isEmpty(tradesUnallocated)) {
                        return null;
                    }
                    List<Order> orders = Lists.newArrayListWithExpectedSize(8);
                    TradeUtils.getOrders4Trade(tradesUnallocated).stream()
                            .filter(o -> (StringUtils.equals(controllerParams.getKey(), "originPlatformOuterId") && StringUtils.equals(o.getOuterSkuId(), controllerParams.getText()) || StringUtils.equals(o.getOuterIid(), controllerParams.getText())
                                    || (StringUtils.equals(controllerParams.getKey(), "skuId") && StringUtils.equals(o.getSkuId(), controllerParams.getText())))
                            ).forEach(o -> {
                                Order tbOrder = new TbOrder();
                                tbOrder.setId(o.getId());
                                tbOrder.setDestId(itemRequest.getSupplierCompanyId());
                                orders.add(tbOrder);
                            });
                    if (CollectionUtils.isEmpty(orders)) {
                        return null;
                    }
                    tbOrderDAO.batchUpdate(staff, orders);
                    return null;
                });
            });

            //  controllerParams 后续新增分销属性要排除商品未匹配异常
            controllerParams.setOnlyContain(2);
            controllerParams.setExceptionStatus(TradeQueryParams.STATUS_EXCEP_ITEM_UNALLOCATED);

        }
    }


    /**
     * @param companyId
     * @param queryType 1:分销，2：供销，3：供销和分销
     * @return 1:分销  2:供销 3:供销且分销
     */
    public Integer queryDmsRole(Long companyId, Integer queryType) {
        String key = String.format("dms_role_%s_%s", companyId, queryType);
        Integer dmsRole = sharedCacheBusiness.get(key);
        if (Objects.nonNull(dmsRole)) {
            return dmsRole;
        }
        dmsRole = dmsTradeService.queryDmsRole(companyId, queryType);
        sharedCacheBusiness.set(key, dmsRole, 600);
        return dmsRole;
    }

    /**
     * 1.先查询当前公司的角色，如果不是供销角色肯定没有分销列表，就不需要缓存，减少缓存量
     * <p>
     * 返回companyId对应的分销商
     *
     * @param companyId
     * @return
     */
    public List<DmsDistributorInfoDto> queryDmsDistributorInfoListBySupperlierId(Long companyId) {
        Integer dmsRole = queryDmsRole(companyId, DmsConstant.DMS_ROLE_TYPE_GX);
        if (!Objects.equals(dmsRole, DmsConstant.DMS_ROLE_TYPE_GX)) {
            return Collections.emptyList();
        }
        String key = String.format("dms_distributor_info_list_%s", companyId);
        List<DmsDistributorInfoDto> dmsDistributorInfoDtos = sharedCacheBusiness.get(key);
        if (Objects.nonNull(dmsDistributorInfoDtos)) {
            return dmsDistributorInfoDtos;
        }
        dmsDistributorInfoDtos = dmsTradeService.queryDmsDistributorInfoListBySupperlierId(companyId);
        sharedCacheBusiness.set(key, dmsDistributorInfoDtos, 600);
        return dmsDistributorInfoDtos;
    }

    /**
     * 1.先查询当前公司的角色，如果不是分销角色肯定没有供销列表，就不需要缓存，减少缓存量
     * <p>
     * 返回companyId对应的供销商信息
     *
     * @param companyId
     * @return
     */
    public List<DmsSupplierInfoDto> queryDmsSupplierInfoListByDmsDistributorId(Long companyId) {
        Integer dmsRole = queryDmsRole(companyId, DmsConstant.DMS_ROLE_TYPE_FX);
        if (!Objects.equals(dmsRole, DmsConstant.DMS_ROLE_TYPE_FX)) {
            return Collections.emptyList();
        }
        String key = String.format("dms_supplier_info_list_%s", companyId);
        List<DmsSupplierInfoDto> supplierInfoDtos = sharedCacheBusiness.get(key);
        if (Objects.nonNull(supplierInfoDtos)) {
            return supplierInfoDtos;
        }
        supplierInfoDtos = dmsTradeService.queryDmsSupplierInfoListByDmsDistributorId(companyId);
        sharedCacheBusiness.set(key, supplierInfoDtos, 600);
        return supplierInfoDtos;
    }

    public List<Trade> matchSupplyIdsWithDmsAttrType(User user, List<Trade> tradeList, int matchType, Integer addDistributorAttributeType) {
        return matchSupplyIdsWithDmsAttrType(user, tradeList, matchType, addDistributorAttributeType, new FxOperateResult());
    }

    /**
     * matchSysItem 参数从配置里面读取
     *
     * @param user
     * @param tradeList
     * @param matchType
     * @param addDistributorAttributeType
     * @param fxOperateResult             执行结果集
     * @return
     */
    public List<Trade> matchSupplyIdsWithDmsAttrType(User user, List<Trade> tradeList, int matchType, Integer addDistributorAttributeType, FxOperateResult fxOperateResult) {
        //根据配置，是否先匹配系统商品再匹配分销策略
        DmsDistributorConfigDto dmsDistributorConfigDto = dmsTradeService.queryDmsDistributorConfig(user.getCompanyId());
        boolean matchSysItem = dmsDistributorConfigDto.getAutoItemMatch() != null && Boolean.TRUE.equals(dmsDistributorConfigDto.getAutoItemMatch());
        return matchSupplyIds(user, tradeList, matchSysItem, matchType, false, addDistributorAttributeType, fxOperateResult);
    }

    public List<Trade> matchSupplyIdsWithDmsAttrTypeAndSave(User user,List<Trade> tradeList,int matchType,Integer  addDistributorAttributeType){
        return matchSupplyIdsWithDmsAttrTypeAndSave(user, tradeList, matchType, addDistributorAttributeType, new FxOperateResult());
    }

    /**
     *  非分销 --> 分销+商品不变
     *  非分销 --> 非分销+商品变更
     *  分销   -->  分销+商品不变
     *  分销   -->  分销+商品变更
     *
     * 前置条件：待付款和待审核的订单才匹配分销属性
     *
     * 1.之前不是分销，现在是需要归还库存，设置分销库存信息
     * 2.订单分销属性有变化的的进行仓库匹配
     * 3.都进行保存--合单的子单分销属性跟主单同步保持一致
     * 4.变成分销的订单计算“可能亏损”的标签
     * 5.供销商发生变更的作废原供销订单
     *
     * @param user
     * @param tradeList
     * @param matchType
     * @param addDistributorAttributeType
     * @param fxOperateResult 分销操作日志
     * @return
     */
    public List<Trade> matchSupplyIdsWithDmsAttrTypeAndSave(User user, List<Trade> tradeList, int matchType, Integer addDistributorAttributeType,FxOperateResult fxOperateResult) {
        if (user == null) {
            logger.debug(LogHelper.buildUserLog(user, "[分销业务]staff为null，跳过分销属性匹配"));
            return tradeList;
        }
        if (user.getStaff() == null) {
            logger.debug(LogHelper.buildUserLog(user, "[分销业务]user的staff为null，跳过分销属性匹配"));
            return tradeList;
        }
        if (CollectionUtils.isEmpty(tradeList)) {
            return tradeList;
        }
        FxLogBuilder logBuilder = FxLogBuilder.fx(user.getStaff()).append("matchSupplyIdsWithDmsAttrTypeAndSave耗时统计:").append("tradeSize", tradeList.size()).startTimer();
        if (!validateIfNeedMatchDmsAttr(user.getStaff(), tradeList)) {
            logBuilder.recordTimer("validateIfNeedMatchDmsAttr");
            logBuilder.startWatch().appendTook(DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV, user.getCompanyId()) ? 1L : 1000L).printDebug(logger);
            return tradeList;
        }
        Long[] sids = TradeUtils.toSids(tradeList);
        //记录取消分销属性与更改供销商的分销订单
        List<Trade> notifyFxTrade2Change = new ArrayList<>();
        List<Trade> notifyFxTrade2Cancel = new ArrayList<>();
        List<Trade> result = lockService.locks(tradeLockBusiness.getERPLocks(user.getStaff(), sids), () -> {
            logBuilder.recordTimer("getERPLocks");
            List<Trade> trades = tradeSearchService.queryBySids(user.getStaff(), true, true, true, sids);
            logBuilder.recordTimer("queryBySids");
            if (CollectionUtils.isEmpty(trades)){
                return trades;
            }

            List<Trade> list = trades.stream().filter(t->TradeStatusUtils.isWaitAudit(t.getSysStatus()) || TradeStatusUtils.isWaitPay(t.getSysStatus())).collect(Collectors.toList()) ;
            if(CollectionUtils.isEmpty(list)){
                logger.debug(LogHelper.buildUserLog(user, "[分销业务]分销属性匹配，订单都不是待付款和待审核状态，sid="+TradeUtils.toSidList(trades)));
                return trades;
            }
            Map<Long, Boolean> sid2IfFxMap = list.stream().collect(Collectors.toMap(TradeBase::getSid, TradeUtils::isFxOrMixTrade, (a, b) -> a));
            List<Trade> srcTrades = Lists.newArrayListWithCapacity(8);
            this.copyTrades(list, srcTrades);
            matchSupplyIdsWithDmsAttrType(user, list, matchType, addDistributorAttributeType, fxOperateResult);
            fxOperateResult.parseMatchSupplyIdsWithDmsAttrType(list);
            logBuilder.recordTimer("matchSupplyIdsWithDmsAttrType");
            List<Trade> updateTrades = Lists.newArrayListWithCapacity(8);
            List<Order> updateOrders = Lists.newArrayListWithCapacity(8);
            List<Long> successSids = Lists.newArrayListWithCapacity(8);
            List<Long> needDoSids = Lists.newArrayListWithCapacity(8);

            list.forEach(t -> {
                // 之前是分销现在不是分销需要重重置仓库，后面的matchWarehouse才会再次匹配，因为之前是分销不管库存，直接重置。
                if (!TradeUtils.isFxOrMixTrade(t) && Objects.equals(Boolean.TRUE, sid2IfFxMap.getOrDefault(t.getSid(), Boolean.FALSE))) {
                    t.setWarehouseName("");
                    t.setWarehouseId(null);
                    needDoSids.add(t.getSid());
                    t.addOpV(OpVEnum.TRADE_NEED_APPLY_STOCK);
                }
                // 之前不是分销，现在是需要归还库存
                if (TradeUtils.isFxOrMixTrade(t) && !Objects.equals(Boolean.TRUE, sid2IfFxMap.getOrDefault(t.getSid(), Boolean.FALSE))) {
                    successSids.add(t.getSid());
                    needDoSids.add(t.getSid());
                }
            });
            if (CollectionUtils.isNotEmpty(successSids)) {
                List<Trade> needResumeStockTrades = srcTrades.stream().filter(trade -> successSids.contains(trade.getSid())).collect(Collectors.toList());
                //释放库存
                try {
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(user.getStaff(), String.format("[分销业务]needResume:%s", TradeUtils.toSidList(needResumeStockTrades))));
                    }
                    orderStockService.resumeTradeStockLocal(user.getStaff(), needResumeStockTrades);
                    logBuilder.recordTimer("resumeTradeStockLocal");
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(user.getStaff(), e, "[分销业务]分销属性匹配时归还库存报错，订单号：").append(TradeUtils.toSidList(needResumeStockTrades)), e);
                }
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_SYNC).setArgs(new Object[]{user.getStaff(), successSids}), null);
            }
            if (CollectionUtils.isNotEmpty(needDoSids)) {
                warehouseAllocateBusiness.matchWarehouse(user.getStaff(), list.stream().filter(t -> needDoSids.contains(t.getSid())).collect(Collectors.toList()));
                logBuilder.recordTimer("matchWarehouse");
            }
            List<Trade> needApplyStockTrades = list.stream().filter(t->t.hasOpV(OpVEnum.TRADE_NEED_APPLY_STOCK)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(needApplyStockTrades)){
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(user.getStaff(), String.format("[分销业务]needApplyStock:%s", TradeUtils.toSidList(needApplyStockTrades))));
                }
                orderStockService.applyTradeStockLocal(user.getStaff(),needApplyStockTrades,false);
                logBuilder.recordTimer("applyTradeStockLocal");
            }
            //设置库存信息
            sysTradeDmsBusiness.filterFxTrade(user.getStaff(), list.stream().filter(TradeUtils::isFxOrMixTrade).collect(Collectors.toList()), 1);
            logBuilder.recordTimer("filterFxTrade");
            TradeConfig tradeConfig = tradeConfigService.get(user.getStaff());
            list.forEach(t -> {
                createUpdateTrade(user.getStaff(), updateTrades, t, tradeConfig);
                createUpdateOrders(user.getStaff(), updateOrders, TradeUtils.getOrders4Trade(t));
            });
            tradePtService.saveByTrades(user.getStaff(), updateTrades);
            tradeUpdateService.updateTrades(user.getStaff(), updateTrades, updateOrders);
            logBuilder.recordTimer("updateTrades");
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(user.getStaff(), String.format("[分销业务]订单数据更新，订单号:%s", TradeUtils.toSidList(updateTrades))));
            }
            getNotifyFxTrade(user.getStaff(), srcTrades, updateTrades, notifyFxTrade2Change, notifyFxTrade2Cancel);
            return trades;
        });
        logBuilder.startWatch().appendTook(DevLogBuilder.curEnabled(DevLogBuilder.LEVEL_DEV, user.getCompanyId()) ? 1L : 2000L).printDebug(logger);
        notifyGxDownload(user.getStaff(), notifyFxTrade2Cancel, 2);
        notifyGxDownload(user.getStaff(), notifyFxTrade2Change, 3);
        return result;
    }

    private boolean validateIfNeedMatchDmsAttr(Staff staff, List<Trade> tradeList) {
        Integer dmsRole = queryDmsRole(staff.getCompanyId(), DmsConstant.DMS_ROLE_TYPE_FX);
        if (Objects.equals(DmsConstant.DMS_ROLE_TYPE_FX, dmsRole)) {
            return true;
        }
        return tradeList.stream().anyMatch(TradeUtils::isFxOrMixTrade);
    }

    private void getNotifyFxTrade(Staff staff, List<Trade> copies, List<Trade> updates, List<Trade> notifyFxTrade2Change, List<Trade> notifyFxTrade2Cancel) {
        if (CollectionUtils.isEmpty(copies) || CollectionUtils.isEmpty(updates)) {
            return;
        }
        Map<Long, Trade> copyMap = copies.stream().filter(TradeUtils::isFxOrMixTrade).collect(Collectors.toMap(Trade::getSid, t -> t, (k1, k2) -> k2));
        updates.forEach(update -> {
            Trade copy = copyMap.get(update.getSid());
            if (Objects.isNull(copy)) {
                return;
            }
            //原始分销订单，destId <= 0
            if (Objects.isNull(copy.getDestId()) || copy.getDestId() <= 0) {
                return;
            }
            //合单的子单，不处理
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, copy) && !Objects.equals(copy.getSid(), copy.getMergeSid())) {
                return;
            }
            //供销商没有变化，不处理
            if (Objects.equals(copy.getDestId(), update.getDestId())) {
                return;
            }
            //最新单据，依然是分销订单，走供销商变更流程
            if (TradeUtils.isFxOrMixTrade(update)) {
                notifyFxTrade2Change.add(copy);
            } else {
                notifyFxTrade2Cancel.add(copy);
            }
        });
    }

    public void copyTrades(List<Trade> srcTrades, List<Trade> destTrades) {
        srcTrades.forEach(trade -> {
            Trade newTrade = new TbTrade();
            BeanUtils.copyProperties(trade, newTrade);
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            List<Order> newOrders = Lists.newArrayListWithCapacity(orders.size());
            for (Order order : orders) {
                Order newOrder = new TbOrder();
                BeanUtils.copyProperties(order, newOrder);
                newOrders.add(newOrder);
            }
            TradeUtils.setOrders(newTrade, newOrders);
            destTrades.add(newTrade);
        });
    }

    /**
     * 之前不是分销现在是分销
     * <p>
     * 1.归还库存
     * 2.重现匹配仓库
     * 3.设置库存信息
     *
     * @param staff
     * @param trades
     */
    public void handleFxTradeStockAndWarehouse(Staff staff, List<Trade> trades) {

        List<Trade> trades4Update = trades.stream().filter(TradeUtils::isFxOrMixTrade).collect(Collectors.toList());
        //设置库存信息
        sysTradeDmsBusiness.filterFxTrade(staff, trades4Update, 1);

        List<Trade> tradeList = trades.stream().filter(t -> BooleanUtils.isNotTrue(t.getOldIsFxOrMix()) && TradeUtils.isFxOrMixTrade(t)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tradeList)) {
            trades4Update.addAll(tradeList);
            //释放库存
            try {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("[分销业务]needResume:%s", TradeUtils.toSidList(tradeList))));
                }
                List<Trade> srcTrades = Lists.newArrayListWithCapacity(8);
                this.copyTrades(tradeList, srcTrades);
                srcTrades.forEach(t -> {
                    t.setConvertType(0);
                    t.setBelongType(0);
                    t.setV(0L);
                    OrderUtils.toFullOrderList(TradeUtils.getOrders4Trade(t), false).forEach(order -> {
                        order.setConvertType(0);
                        order.setBelongType(0);
                    });
                });
                orderStockService.resumeTradeStockLocal(staff, srcTrades);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(staff, e, "[分销业务]归还库存报错，订单号：").append(TradeUtils.toSidList(tradeList)), e);
            }
            warehouseAllocateBusiness.matchWarehouse(staff, tradeList);
        }
        eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_SYNC).setArgs(new Object[]{staff, TradeUtils.toSidList(trades4Update)}), null);

        if (CollectionUtils.isEmpty(trades4Update)) {
            return;
        }


        List<Trade> updateTrades = Lists.newArrayListWithCapacity(8);
        List<Order> updateOrders = Lists.newArrayListWithCapacity(8);
        trades4Update.forEach(t -> {
            createUpdateOrders(staff, updateOrders, TradeUtils.getOrders4Trade(t));
            createUpdateTrade(staff, updateTrades, t);
        });
        tradePtService.saveByTrades(staff, updateTrades);
        tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(staff, String.format("[分销业务]订单数据更新，订单号:%s", TradeUtils.toSidList(updateTrades))));
        }

    }


    public static Trade createUpdateTrade(Staff staff, List<Trade> updateTrades, Trade trade) {
        return createUpdateTrade(staff, updateTrades, trade, null);
    }

    public static Trade createUpdateTrade(Staff staff, List<Trade> updateTrades, Trade trade, TradeConfig tradeConfig) {
        Trade update = TradeBuilderUtils.builderUpdateTrade(trade);
        update.setSid(trade.getSid());
        update.setSysStatus(trade.getSysStatus());
        update.setIsAutoAudit(trade.getIsAutoAudit());
        update.setSysOuterId(trade.getSysOuterId());

        update.setIsExcep(trade.getIsExcep());
        update.setExcep(trade.getExcep());
        update.setItemExcep(trade.getItemExcep());
        update.setV(trade.getV());


        update.setWarehouseId(trade.getWarehouseId());//第三方仓库用到
        update.setWarehouseMatchRuleId(trade.getWarehouseMatchRuleId());
        update.setWarehouseName(trade.getWarehouseName());
        update.setTemplateId(trade.getTemplateId());
        update.setLogisticsCompanyId(trade.getLogisticsCompanyId());
        update.setTemplateType(trade.getTemplateType());
        update.setOutSid(trade.getOutSid());
        // update.setStockStatus(trade.getStockStatus());
        TradeExceptUtils.updateStockStatus(staff, update, trade.getStockStatus());
        update.setValidItemNum(trade.getValidItemNum());
        update.setInsufficientNum(trade.getInsufficientNum());
        update.setInsufficientRate(trade.getInsufficientRate());
        update.setBelongType(trade.getBelongType());
        update.setConvertType(trade.getConvertType());
        update.setSourceId(trade.getSourceId());
        update.setDestId(trade.getDestId());
        update.setCost(trade.getCost());
        update.setSaleFee(trade.getSaleFee());
        update.setSalePrice(trade.getSaleFee());
        TradeExceptUtils.syncTradeExcept(staff, update, trade, ExceptEnum.LOST_MSG);
        TradeExceptUtils.syncTradeExcept(staff, update, trade, ExceptEnum.REFUNDING);
        TradeExceptUtils.syncTradeExcept(staff, update, trade, ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
        // 合单的子单分销属性跟主单同步保持一致
        if (TradeUtils.isMerge(trade)) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isNotEmpty(orders)) {
                orders.forEach(o -> {
                    if (!Objects.equals(o.getSid(), trade.getSid()) && !TradeUtils.toSidList(updateTrades).contains(o.getSid())) {
                        Trade subUpdateTrade = new TbTrade();
                        subUpdateTrade.setSid(o.getSid());
                        subUpdateTrade.setBelongType(trade.getBelongType());
                        subUpdateTrade.setConvertType(trade.getConvertType());
                        subUpdateTrade.setSourceId(trade.getSourceId());
                        subUpdateTrade.setDestId(trade.getDestId());

                        if (tradeConfig != null) {
                            Map<Long, List<Order>> orderMap = OrderUtils.toMapBySid(OrderUtils.toTree(orders));
                            TradeUtils.setOrders(subUpdateTrade, orderMap.get(o.getSid()));
                            subUpdateTrade.setMergeSid(trade.getMergeSid());
                            TradeStockUtils.resetTradeStockStatus(staff, subUpdateTrade, tradeConfig);
                            subUpdateTrade.setMergeSid(null);
                        }

                        updateTrades.add(subUpdateTrade);
                    }
                });
            }
        }

        updateTrades.add(update);
        return update;
    }

    public void createUpdateOrders(Staff staff, List<Order> updateOrders, List<Order> orders) {
        for (Order order : orders) {
            createUpdateOrder(staff, updateOrders, order);
        }
    }

    public static void createUpdateOrder(Staff staff, List<Order> updateOrders, Order order) {
        Order update = OrderBuilderUtils.builderUpdateOrderWithV(order);
        update.setId(order.getId());
        update.setDestId(order.getDestId());
        update.setSourceId(order.getSourceId());
        update.setScalping(order.getScalping());
        update.setStockNum(order.getStockNum());
        // update.setStockStatus(order.getStockStatus());
        OrderExceptUtils.setStockStatus(staff, update, order.getStockStatus());
        update.setBelongType(order.getBelongType());
        update.setConvertType(order.getConvertType());
        if (OrderUtils.isFxOrMixOrder(order)) {
            update.setCost(order.getCost());
            update.setSalePrice(order.getSalePrice());
            update.setSaleFee(order.getSaleFee());
        }
        update.setV(order.getV());
        OrderExceptUtils.syncOrderExcept(staff, order, update, ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
        updateOrders.add(update);
        if (order.getSuits() != null) {
            update.setType(order.getType());
            for (Order son : order.getSuits()) {
                son.setSysStatus(order.getSysStatus());
                createUpdateOrder(staff, updateOrders, son);
            }
        }
    }


    /**
     * 快麦通供销商不明确异常需要去掉。且设置订单不是分销单
     *
     * @param trades
     */
    public void handleDistributorAttribute(Staff fxStaff, TradeConfig tradeConfig, List<Trade> trades, boolean ifKmt) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        trades.forEach(t -> {
            if (TradeUtils.isFxTrade(t)) {
                // 包含0的
                List<Order> orders4Trade = TradeUtils.getOrders4Trade(t);
                if (CollectionUtils.isEmpty(orders4Trade)) {
                    return;
                }
                orders4Trade.stream().filter(o -> Objects.isNull(o.getDestId())).forEach(o1 -> o1.setDestId(0L));
                Set<Long> destIdSet = orders4Trade.stream().map(Order::getDestId).collect(Collectors.toSet());
                if (orders4Trade.stream().anyMatch(o -> Objects.equals(0L, o.getDestId()))) {
                    // 快麦通只要一个order是非分销整个订单都是普通订单
                    if (ifKmt) {
                        TradeUtils.setSourceId(t, 0L);
                        t.setBelongType(0);
                        t.setConvertType(0);
                        t.setDestId(0L);
                        TradeExceptUtils.updateExcept(fxStaff, t, ExceptEnum.FX_AMBIGUITY, 0L);
                        t.setIsExcep(TradeExceptUtils.isExcept(fxStaff, t) ? 1 : 0);

                        orders4Trade.stream().filter(o -> Objects.equals(0L, o.getDestId())).forEach(order -> {
                            order.setSourceId(0L);
                            order.setBelongType(0);
                            order.setConvertType(0);
                        });
                        // 非快麦通如果trade是分销，order的belongType,convertTye都改成1。库存是按order维度
                    } else {
                        orders4Trade.forEach(order -> {
                            order.setBelongType(1);
                            order.setConvertType(1);
                        });
                    }
                } else if (destIdSet.size() == 1 && !destIdSet.contains(0L) && !destIdSet.contains(t.getCompanyId())) {
                    // 全部都是分销，订单取商品的供销商
                    TradeExceptUtils.updateExcept(fxStaff, t, ExceptEnum.FX_AMBIGUITY, 0L);
                    t.setIsExcep(TradeExceptUtils.isExcept(fxStaff, t) ? 1 : 0);
                    TradeUtils.setSourceId(t, t.getCompanyId());
                    t.setDestId(orders4Trade.get(0).getDestId());
                } else {
                    if (ifKmt) {
                        // 全部都是分销，订单取商品的供销商
                        TradeExceptUtils.updateExcept(fxStaff, t, ExceptEnum.FX_AMBIGUITY, 0L);
                        t.setIsExcep(TradeExceptUtils.isExcept(fxStaff, t) ? 1 : 0);
                        t.setDestId(orders4Trade.get(0).getDestId());
                    } else if (destIdSet.size() > 1) {
                        // 打上供销商不明确异常
                        TradeExceptUtils.updateExcept(fxStaff, t, ExceptEnum.FX_AMBIGUITY, 1L);
                        t.setIsExcep(1);
                        t.setDestId(0L);
                    }
                }
            } else {
                if (TradeUtils.isGxOrMixTrade(t)) {
                    return;
                }
                List<Order> orders4Trade = TradeUtils.getOrders4Trade(t);
                if (CollectionUtils.isEmpty(orders4Trade)) {
                    return;
                }
                orders4Trade.stream().filter(o -> Objects.equals(0L, o.getDestId())).forEach(order -> {
                    order.setSourceId(0L);
                    order.setBelongType(0);
                    order.setConvertType(0);
                });
            }

            if (isSalePriceSourceFx(fxStaff, tradeConfig)) {
                List<Order> orders4Trade = TradeUtils.getOrders4Trade(t);
                orders4Trade.stream().filter(o -> Objects.equals(0L, o.getDestId())).forEach(order -> {
                    order.setSalePrice("0.00");
                    order.setSaleFee("0.00");
                });
                paymentCalculateSupports.gatherSaleFeeFromOrders(t);
            }

            if (TradeUtils.isQimenAndNotFx(t)) {
                t.setConvertType(3);
                t.setBelongType(2);
                TradeUtils.getOrders4Trade(t).forEach(o -> {
                    o.setConvertType(3);
                    o.setBelongType(2);
                });
            }
        });
    }

    /**
     * matchSysItem 参数从匹配里面读取
     *
     * @param user
     * @param tradeList
     * @param matchType
     * @param needSplit
     * @return
     */
    public List<Trade> matchSupplyIds(User user, List<Trade> tradeList, int matchType, boolean needSplit) {
        //根据配置，是否先匹配系统商品再匹配分销策略
        DmsDistributorConfigDto dmsDistributorConfigDto = dmsTradeService.queryDmsDistributorConfig(user.getCompanyId());
        boolean matchSysItem = dmsDistributorConfigDto.getAutoItemMatch() != null && Boolean.TRUE.equals(dmsDistributorConfigDto.getAutoItemMatch());
        return matchSupplyIds(user, tradeList, matchSysItem, matchType, needSplit, 0);
    }

    public List<Trade> matchSupplyIds(User user, List<Trade> tradeList, boolean matchSysItem, int matchType) {
        return matchSupplyIds(user, tradeList, matchSysItem, matchType, false, 0);
    }

    public List<Trade> matchSupplyIds(User user, List<Trade> tradeList, boolean matchSysItem, int matchType, boolean needSplit, Integer addDistributorAttributeType) {
        return matchSupplyIds(user, tradeList, matchSysItem, matchType, needSplit, addDistributorAttributeType, new FxOperateResult());
    }

    /**
     * 匹配供销商Id，并根据matchType和needSplit来判断是否按供销商拆单
     *
     * @param user                        用户
     * @param tradeList                   订单列表
     * @param matchSysItem                true:按系统商品匹配
     * @param matchType                   1:自动流程 其他值非自动  自动流程才会拆分，审核的时候只会标记异常  3：不自动拆单，要不会强制打上分销属性
     * @param needSplit                   是否需要拆分，如果此值为false且 matchType=1 会根据配置去读取
     * @param addDistributorAttributeType 分销属性类型
     * @param fxOperateResult             执行结果集
     * @return
     */
    public List<Trade> matchSupplyIds(User user, List<Trade> tradeList, boolean matchSysItem, int matchType, boolean needSplit, Integer addDistributorAttributeType, FxOperateResult fxOperateResult) {
        Staff fxStaff = user.getStaff();
        TradeConfig tradeConfig = tradeConfigService.get(fxStaff);
        addDistributorAttributeType = (fxStaff != null && fxStaff.getCompany().isKmt()) ? 1 : 0;
        if (Objects.equals(addDistributorAttributeType, 1)) {
            new FxLogBuilder(user.getStaff(), FxLogBuilder.ROLE_FX).append("当前为快麦通公司").printDebug(logger);
        }
        List<DmsSupplierInfoDto> dmsSupplierInfoDtos = beanProxy.queryDmsSupplierInfoListByDmsDistributorId(user.getCompanyId());
        Map<Long, Long> supplyCompanyMap = supplyInfoToMap(dmsSupplierInfoDtos);
        if (dmsSupplierInfoDtos != null && dmsSupplierInfoDtos.size() > 0) {
            DmsDistributorConfigDto dmsDistributorConfigDto = dmsTradeService.queryDmsDistributorConfig(user.getCompanyId());
            if (matchType == 1) {//自动流程才根据outerId自动匹配
                matchSupplyIdsByOuterId(user, tradeConfig, matchSysItem, tradeList, supplyCompanyMap);
            }
            List<Trade> tradeList4Fxxz = tradeList.stream().filter(t -> Objects.equals(CommonConstants.PLAT_FORM_TYPE_FXXZ, t.getSubSource())).collect(Collectors.toList());
            if (matchType != 1 && !tradeList4Fxxz.isEmpty()) {
                matchSupplyIdsByOuterId(user, tradeConfig, false, tradeList4Fxxz, supplyCompanyMap, fxOperateResult);
            }
            fillFxKmtSupplierItemInfo(FxKmtSupplierItem.builder().dmsDistributorConfigDto(dmsDistributorConfigDto).matchType(matchType).staff(user.getStaff()).trades(tradeList).ifHandleDmsAttr(true).build(), fxOperateResult);
            String dmsTradeMatchFxAttrType = TradeConfigGetUtil.get(user.getStaff(), TradeConfigEnum.DMS_TRADE_MATCH_FX_ATTR_TYPE).getConfigValue();
            boolean ifMatchFxAttrByTradeRule = TradeConstants.DMS_TRADE_MATCH_FX_ATTR_ALL.equals(dmsTradeMatchFxAttrType) || TradeConstants.DMS_TRADE_MATCH_FX_ATTR_TRADE_RULE.equals(dmsTradeMatchFxAttrType);
            if (ifMatchFxAttrByTradeRule) {
                // 通过快麦通匹配分销属性，走fillFxKmtSupplierItemInfo(),此处走分销策略type=0 的话通过商品匹配供销商兜底处理
                Integer matchSupplierType = org.apache.commons.lang3.math.NumberUtils.toInt(dmsTradeMatchFxAttrType, 0);
                List<DmsSupplierMatchDto> dmsSupplierMatchDtos = convertToSupplierMatchDtos(user, matchSysItem, tradeList, matchType, matchSupplierType);//规则匹配参数
                if (CollectionUtils.isNotEmpty(dmsSupplierMatchDtos)) {
                    List<List<DmsSupplierMatchDto>> partition = Lists.partition(dmsSupplierMatchDtos, 20);
                    for (List<DmsSupplierMatchDto> supplierMatchDtos : partition) {
                        List<DmsSupplierMatchDto> dmsSupplierMatchDtos1 = dmsTradeService.matchSupplier(supplierMatchDtos);
                        buildLog(user, dmsSupplierMatchDtos1);
                        parseDmsMatchSupplierErrMsg(dmsSupplierMatchDtos1, fxOperateResult);
                        fillOrderInfoWithSupplyId(fxStaff, tradeConfig, tradeList, dmsSupplierMatchDtos1, matchType);
                    }
                }
            }
            // matchType 等于1 并且没有指定是否拆分读取配置
            if (matchType == 1 && !needSplit) {
                needSplit = needSplit(fxStaff, user.getCompanyId());
            }
            List<Trade> result = new ArrayList<>();
            for (Trade trade : tradeList) {
                // 处理历史数据 convertType=3 AND belongType=3 和 v有128对齐。都有V 128后可以去掉
                if (TradeUtils.isQimenFxSource(trade) && !TradeUtils.getIfQimenMix(trade)) {
                    trade.addV(128);
                }
                //平台分销订单过滤
                if (TradeUtils.notNeedMatchFxAttr(trade)) {
                    fxOperateResult.addTempMsgByOperate(trade.getSid(), "平台分销单、1688分销小店、档口订单不支持添加分销属性");
                    result.add(trade);
                    continue;
                }
                // 非待审核和待付款的订单不支持添加分销属性
                if (!(TradeStatusUtils.isWaitAudit(trade.getSysStatus()) || TradeStatusUtils.isWaitPay(trade.getSysStatus()))) {
                    fxOperateResult.addTempMsgByOperate(trade.getSid(), "非待审核和待付款的订单不支持添加分销属性");
                    result.add(trade);
                    continue;
                }
                result.addAll(assambleTrade(user, tradeConfig, trade, matchType, needSplit));
            }
            if (Objects.equals(1, addDistributorAttributeType)) {
                handleDistributorAttribute(fxStaff, tradeConfig, result, true);
            } else {
                handleDistributorAttribute(fxStaff, tradeConfig, result, false);
            }
            return result;

        }
        return tradeList;
    }

    private void buildLog(User user, List<DmsSupplierMatchDto> dmsSupplierMatchDtos1) {
        if (CollectionUtils.isEmpty(dmsSupplierMatchDtos1)) {
            return;
        }
        try {
            FxLogBuilder append = new FxLogBuilder(user.getStaff(), FxLogBuilder.ROLE_FX).append("匹配供销商(分销策略):").startArray();
            List<String> outerSids = new ArrayList<>();
            for (int i = 0; i < dmsSupplierMatchDtos1.size(); i++) {
                DmsSupplierMatchDto dmsSupplierMatchDto = dmsSupplierMatchDtos1.get(i);
                for (DmsItemMatchDto dmsItemMatchDto : dmsSupplierMatchDto.getDmsItemMatchDtos()) {
                    String outerId = dmsItemMatchDto.getOuterId();
                    if (!outerSids.contains(outerId)) {
                        outerSids.add(outerId);
                        append.appendJSon(dmsItemMatchDto);
                    }
                }
            }
            append.endArray().printDebug(logger);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(user.getStaff(), e, "打印日志出错！"), e);
        }
    }

    /**
     * 记录操作失败原因
     *
     * @param dmsSupplierMatchDtos
     * @param fxOperateResult
     */
    private void parseDmsMatchSupplierErrMsg(List<DmsSupplierMatchDto> dmsSupplierMatchDtos, FxOperateResult fxOperateResult) {
        if (CollectionUtils.isEmpty(dmsSupplierMatchDtos) || !fxOperateResult.isAddDistributorAttributeOperate()) {
            return;
        }
        dmsSupplierMatchDtos.forEach(dmsSupplierMatchDto -> {
            dmsSupplierMatchDto.getDmsItemMatchDtos().forEach(dmsItemMatchDto -> {
                //匹配失败的，记录一条原因就行
                if (dmsItemMatchDto.getSupplierId() == 0) {
                    DmsItemMatchErrorEnum errorEnum = DmsItemMatchErrorEnum.getRemarkByKey(dmsItemMatchDto.getErrMsgKey());
                    if (null != errorEnum) {
                        fxOperateResult.addTempMsgByOperate(dmsSupplierMatchDto.getSid(), errorEnum.getRemark());
                    }
                }
            });
        });
    }


    /**
     * 供销商id－－供销商公司id
     *
     * @param supplierInfoDtos
     * @return
     */
    private Map<Long, Long> supplyInfoToMap(List<DmsSupplierInfoDto> supplierInfoDtos) {
        Map<Long, Long> supplyCompanyMap = new HashMap<>();
        for (DmsSupplierInfoDto dmsSupplierInfoDto : supplierInfoDtos) {
            supplyCompanyMap.put(dmsSupplierInfoDto.getSupplierCompanyId(), dmsSupplierInfoDto.getContractId());
        }
        return supplyCompanyMap;
    }


    /**
     * 是否开启了拆分配置
     *
     * @param companyId
     * @return
     */
    private boolean needSplit(Staff staff, Long companyId) {
        DmsDistributorConfigDto dmsDistributorConfigDto = dmsTradeService.queryDmsDistributorConfig(companyId);
        if (dmsDistributorConfigDto != null) {
            Integer splitTrade = dmsDistributorConfigDto.getSplitTrade();
            if (splitTrade != null && splitTrade == 1) {
                new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).append("开启了 下载平台不明确供销商订单，自动按商品所属供销商拆分订单").printDebug(logger);
                return true;
            }
        }
        return false;
    }

    /**
     * 是否平台修改备注直接覆盖供销订单，不自动反审核供分销订单
     *
     * @param companyId
     * @return
     */
    public boolean needAutoNotAuditUndo(Staff staff, Long companyId) {
        DmsDistributorConfigDto dmsDistributorConfigDto = dmsTradeService.queryDmsDistributorConfig(companyId);
        if (dmsDistributorConfigDto != null) {
            Boolean fxPlatformSellerMemoChangeNotAuditUndo = dmsDistributorConfigDto.getPlatformSellerMemoChangeNotAuditUndo();
            boolean equals = Objects.equals(true, fxPlatformSellerMemoChangeNotAuditUndo);
            if (equals) {
                new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).append("开启了 平台修改备注直接覆盖供销订单，不自动反审核供分销订单").printDebug(logger);
            }
            return equals;
        }
        return false;
    }

    /**
     * 是否平台修改备地址接覆盖供销订单，不自动反审核供销订单
     *
     * @param companyId
     * @return
     */
    public boolean needAutoNotAuditUndo4AddressChange(Staff staff, Long companyId) {
        DmsDistributorConfigDto dmsDistributorConfigDto = dmsTradeService.queryDmsDistributorConfig(companyId);
        if (dmsDistributorConfigDto != null) {
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(staff, String.format("[是否开启：平台修改地址直接覆盖供销订单，不自动反审核供销订单=%s]", dmsDistributorConfigDto.getPlatformAddressChangeNotAuditUndo())));
            }
            Boolean platformAddressChangeNotAuditUndo = dmsDistributorConfigDto.getPlatformAddressChangeNotAuditUndo();
            return Objects.equals(Boolean.TRUE, platformAddressChangeNotAuditUndo);
        }
        return false;
    }

    public List<Trade> matchSupplyIdsByOuterId(User user, TradeConfig tradeConfig, boolean matchSysItem, List<Trade> tradeList, Map<Long, Long> supplyCompanyMap) {
        return matchSupplyIdsByOuterId(user, tradeConfig, matchSysItem, tradeList, supplyCompanyMap, new FxOperateResult());
    }

    /**
     * 根据outerId匹配supplyId
     *
     * @param user
     * @param tradeList
     * @param supplyCompanyMap
     * @return
     */
    protected List<Trade> matchSupplyIdsByOuterId(User user, TradeConfig tradeConfig, boolean matchSysItem, List<Trade> tradeList, Map<Long, Long> supplyCompanyMap, FxOperateResult fxOperateResult) {
        for (Trade trade : tradeList) {
            if (TradeUtils.isPlatformFxSource(trade) || TradeUtils.isDangKou(trade)) {
                fxOperateResult.addTempMsgByOperate(trade.getSid(), String.format("订单[系统订单号:%s]是平台分销订单或奇门供销订单,不能匹配供销商", trade.getSid()));
                continue;
            }
            if (Trade.STOCK_STATUS_RELATION_MODIFIED.equals(trade.getStockStatus())) {
                String msg = String.format("订单[系统订单号:%s],商品对应关系修改的订单不能匹配供销商", trade.getSid());
                fxOperateResult.addTempMsgByOperate(trade.getSid(), msg);
                logger.warn(LogHelper.buildLogHead(user).append(msg));
                continue;
            }
            if (trade.getIsCancel() == 1) {
                String msg = String.format("订单[系统订单号:%s]是作废订单,不能匹配供销商", trade.getSid());
                fxOperateResult.addTempMsgByOperate(trade.getSid(), msg);
                logger.warn(LogHelper.buildLogHead(user).append(msg));
                continue;
            }
            if (TradeExceptUtils.isContainExcept(user.getStaff(), trade, ExceptEnum.HALT)) {
                String msg = String.format("订单[系统订单号:%s]是挂起订单,不能匹配供销商", trade.getSid());
                fxOperateResult.addTempMsgByOperate(trade.getSid(), msg);
                logger.warn(LogHelper.buildLogHead(user).append(msg));
                continue;
            }
            handlerFxTradeWithOuterId(user, tradeConfig, matchSysItem, trade, supplyCompanyMap, fxOperateResult);
        }
        return tradeList;
    }


    /**
     * 根据DmsSupplierMatchDto填充order信息
     *
     * @param tradeList
     * @param dmsSupplierMatchDtos
     */
    private void fillOrderInfoWithSupplyId(Staff staff, TradeConfig tradeConfig, List<Trade> tradeList, List<DmsSupplierMatchDto> dmsSupplierMatchDtos, int matchType) {
        List<Order> orderList = TradeUtils.getOrders4Trade(tradeList);
        Map<String, Order> orderMap = new HashMap<>();
        for (Order order : orderList) {
            orderMap.put(order.getId() + "", order);
        }

        if (dmsSupplierMatchDtos != null && dmsSupplierMatchDtos.size() > 0) {
            for (DmsSupplierMatchDto dmsSupplierMatchDto : dmsSupplierMatchDtos) {
                List<DmsItemMatchDto> dmsItemMatchDtos = dmsSupplierMatchDto.getDmsItemMatchDtos();
                boolean notSetFxAttr = false;
                if (dmsItemMatchDtos != null && dmsItemMatchDtos.size() > 0) {
                    if (staff.getCompany().isKmt() && dmsItemMatchDtos.stream().map(DmsItemMatchDto::getSupplierId).collect(Collectors.toSet()).size() > 1) {
                        notSetFxAttr = true;
                    }
                    for (DmsItemMatchDto dmsItemMatchDto : dmsItemMatchDtos) {
                        String key = dmsItemMatchDto.getId();
                        Order order = orderMap.get(key);
                        if (order != null) {
                            if (notSetFxAttr) {
                                order.setDestId(dmsItemMatchDto.getSupplierId());
                            } else {
                                fillOrderFxSourcePros(staff, tradeConfig, order, dmsItemMatchDto.getSupplierId(), dmsItemMatchDto.getFxPrice(), matchType);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * tradelist convetTo DmsSupplierMatchDtoList
     *
     * @param tradeList
     * @param matchType
     * @return
     */
    private List<DmsSupplierMatchDto> convertToSupplierMatchDtos(User user, boolean matchSysItem, List<Trade> tradeList, int matchType, Integer addDistributorAttributeType) {
        List<DmsSupplierMatchDto> dmsSupplierMatchDtos = new ArrayList<>();
        for (Trade trade : tradeList) {
            if (TradeUtils.notNeedMatchFxAttr(trade) || !(TradeStatusUtils.isWaitAudit(trade.getSysStatus()) || TradeStatusUtils.isWaitPay(trade.getSysStatus()))) {
                continue;
            }
            DmsSupplierMatchDto dmsSupplierMatchDto = convertToSupplierMatchDto(user, matchSysItem, trade, matchType, addDistributorAttributeType);
            if (dmsSupplierMatchDto == null || CollectionUtils.isEmpty(dmsSupplierMatchDto.getDmsItemMatchDtos())) {
                continue;
            }
            // 有商品未匹配，即没有outerId就不会去匹配
            if (dmsSupplierMatchDto.getDmsItemMatchDtos().stream().anyMatch(itemMatchDto -> StringUtils.isEmpty(itemMatchDto.getOuterId()))) {
                continue;
            }
            dmsSupplierMatchDtos.add(dmsSupplierMatchDto);
        }
        return dmsSupplierMatchDtos;
    }

    /**
     * 得到所有未匹配到destId的order，转为Dto参数
     *
     * @param trade
     * @param matchType
     * @return
     */
    private DmsSupplierMatchDto convertToSupplierMatchDto(User user, boolean matchSysItem, Trade trade, int matchType, Integer addDistributorAttributeType) {
        DmsSupplierMatchDto dmsSupplierMatchDto = new DmsSupplierMatchDto();
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        dmsSupplierMatchDto.setCompanyId(trade.getCompanyId());
        dmsSupplierMatchDto.setMatchType(matchType);
        dmsSupplierMatchDto.setAddDistributorAttributeType(addDistributorAttributeType);
        dmsSupplierMatchDto.setSid(trade.getSid());
        dmsSupplierMatchDto.setUserId(trade.getUserId());
        dmsSupplierMatchDto.setTagIds(trade.getTagIds());
        List<DmsItemMatchDto> dmsItemMatchDtos = new ArrayList<>();
        Integer errMsgKey = 0;
        if(Objects.equals(1,addDistributorAttributeType)){
            errMsgKey = DmsItemMatchErrorEnum.MATCH_BY_KMT_ITEM_FAIL.getKey();
        }else if(Objects.equals(2,addDistributorAttributeType)){
            errMsgKey = DmsItemMatchErrorEnum.MATCH_BY_DMS_RULE_FAIL.getKey();
        }
        boolean ifForceSupplyCode = StringUtils.equals(CommonConstants.PLAT_FORM_TYPE_FXXZ, trade.getSubSource());
        for (Order order : orderList) {
            // 未匹配supplyId 或者 （快麦通 && 不是强制供销商商品编码）
            if (OrderUtils.needDestIdMatchOrder(order) || (Objects.equals(1, addDistributorAttributeType) && !ifForceSupplyCode)) //未匹配到supplyorder
            {
                DmsItemMatchDto dmsItemMatchDto = new DmsItemMatchDto();
                dmsItemMatchDto.setErrMsgKey(errMsgKey);
                dmsItemMatchDto.setId(order.getId() + "");
                if (matchType == 2 && order.getSysOuterId() != null && order.getSysOuterId().length() > 0) {//用系统商品去匹配
                    dmsItemMatchDto.setOuterId(order.getSysOuterId());
                } else {
                    dmsItemMatchDto.setOuterId(getOuterId(user, matchSysItem, order));//用平台商品去匹配
                }
                if (dmsItemMatchDto.getOuterId() != null) {
                    int index = dmsItemMatchDto.getOuterId().indexOf("##") > 0 ? dmsItemMatchDto.getOuterId().indexOf("##") : dmsItemMatchDto.getOuterId().indexOf("$$");
                    if (index > 0) {
                        dmsItemMatchDto.setOuterId(dmsItemMatchDto.getOuterId().substring(0, index));
                    }
                }
                // 快麦通商品未匹配也需要
                if (StringUtils.isBlank(dmsItemMatchDto.getOuterId())) {
                    continue;
                }
                dmsItemMatchDtos.add(dmsItemMatchDto);
            }
        }

        dmsSupplierMatchDto.setDmsItemMatchDtos(dmsItemMatchDtos);
        if (dmsItemMatchDtos.size() > 0) {
            return dmsSupplierMatchDto;
        } else {
            return null;
        }
    }

    /**
     * 不需要返回对应的供销商品编码，只需要返回分销的商品编码
     *
     * @param user
     * @param matchSysItem
     * @param order
     * @return
     */
    public String getOuterId(User user, boolean matchSysItem, Order order) {
        if (matchSysItem) {
            return order.getSysOuterId();
        }


        String outerId = "";
        if (StringUtils.isNotBlank(order.getOuterSkuId())) {
            outerId = order.getOuterSkuId();
        } else if (StringUtils.isNotBlank(order.getOuterIid())) {
            outerId = order.getOuterIid();
        } else {
            outerId = order.getSysOuterId();
        }
        if (StringUtils.isBlank(outerId)) {
            outerId = getPlatformOuterId(user, order);
        }
        return outerId;
    }


    public String getPlatformOuterId(User user, Order order) {
        try {
            QuerySysOuterIdByItemInfoRequest request = new QuerySysOuterIdByItemInfoRequest();
            request.setNumIid(order.getNumIid());
            request.setSkuId(order.getSkuId());
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(user.getStaff(), String.format("根据平台id查询商家编码入参[numIid=%s],[skuId=%s]", request.getNumIid(), request.getSkuId())));
            }
            Map<String, String> stringStringMap = iItemServiceDubbo.queryPlatformOuterIdByItemInfo(user.getStaff(), user, Collections.singletonList(request));
            if (stringStringMap != null && stringStringMap.size() > 0) {
                String skuId = StringUtils.defaultString(order.getSkuId(), "0");
                String numIid = order.getNumIid();
                String key = Joiner.on("_").appendTo(new StringBuilder(), numIid, skuId).toString();
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(user.getStaff(), String.format("根据平台id查询商家编码结果[key=%s]", key)));
                }
                return stringStringMap.get(key);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(LogHelper.buildErrorLog(user.getStaff(), e, "根据平台id查询商家编码出错！"), e);
        }
        return "";
    }

    public void handlerFxTradeWithOuterId(User user, TradeConfig tradeConfig, boolean matchSysItem, Trade trade, Map<Long, Long> supplyCompanyMap) {
        handlerFxTradeWithOuterId(user, tradeConfig, matchSysItem, trade, supplyCompanyMap, new FxOperateResult());
    }

    /**
     * 根据outerId匹配supplyId
     *
     * @param user
     * @param trade
     * @param supplyCompanyMap
     */
    protected void handlerFxTradeWithOuterId(User user, TradeConfig tradeConfig, boolean matchSysItem, Trade trade, Map<Long, Long> supplyCompanyMap, FxOperateResult fxOperateResult) {
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        for (Order order : orderList) {
            if (OrderUtils.needDestIdMatchOrder(order)) //未匹配到supplyorder
            {
                String outerId = getOuterId(user, matchSysItem, order);
                long supplyCompanyId = 0;
                if (StringUtils.isNotBlank(outerId)) {
                    int splitIndex = outerId.indexOf("##") > 0 ? outerId.indexOf("##") : outerId.indexOf("$$");
                    if (splitIndex > 0 && splitIndex + 2 < outerId.length()) {
                        try {
                            String supplyCompanyIdStr = outerId.substring(splitIndex + 2);
                            if (!StringUtils.isNumeric(supplyCompanyIdStr)) {
                                logger.warn(LogHelper.buildLog(user.getStaff(), String.format("商家编码:%s,格式不正确", outerId)));
                            }
                            supplyCompanyId = NumberUtils.str2Long(supplyCompanyIdStr, 0L);
                            if (!supplyCompanyMap.containsKey(supplyCompanyId)) {
                                supplyCompanyId = 0;
                                fxOperateResult.addTempMsgByOperate(order.getSid(), String.format("订单[系统订单号:%s]根据outerId匹配supplyId失败", order.getSid()));
                            }
                        } catch (Exception e) {
                            logger.error(LogHelper.buildErrorLog(user.getStaff(), e, "根据outerId匹配supplyId出错！"), e);
                            fxOperateResult.addTempMsgByOperate(order.getSid(), String.format("订单[系统订单号:%s]根据outerId匹配supplyId出错", order.getSid()));
                        }
                    }
                }
                fillOrderFxSourcePros(user.getStaff(), tradeConfig, order, supplyCompanyId, 1);
            } else {
                fillOrderFxSourcePros(user.getStaff(), tradeConfig, order, 0L, 1);
            }
        }
    }


    /**
     * 分销标记
     *
     * @param staff
     * @param trade
     * @param matchType
     */
    public void fillTradeFxSourcePros(Staff staff, Trade trade, int matchType) {
        int supplyType = calFxTradeSupplyType(staff, trade, matchType);
        if (SAME_SUPPLY == supplyType) {
            trade.setDestId(TradeUtils.getOrders4Trade(trade).get(0).getDestId());
            if (TradeUtils.isGxOrMixTrade(trade)) {
                trade.setBelongType(3);
            } else {
                TradeUtils.setSourceId(trade, trade.getCompanyId());
                trade.setBelongType(1);
            }
            trade.setConvertType(1);
        } else if (AMBIGUITY_SUPPLY == supplyType) {
            if (TradeUtils.isGxOrMixTrade(trade)) {
                trade.setBelongType(3);
            } else {
                TradeUtils.setSourceId(trade, trade.getCompanyId());
                trade.setBelongType(1);
            }
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_AMBIGUITY, 1L);
            trade.setConvertType(1);
            trade.setIsExcep(1);
            trade.setDestId(0L);
        } else {
            if (!TradeUtils.isGxOrMixTrade(trade)) {
                TradeUtils.setSourceId(trade, 0L);
                trade.setConvertType(0);
                trade.setBelongType(0);
            }
            trade.setDestId(0L);
        }
        if (TradeUtils.isGxAndFxTrade(trade) && trade.getSourceId() != null && trade.getDestId() != null
                && trade.getSourceId().equals(trade.getDestId())) {
            throw new RuntimeException("不能设置订单的供销商和分销商为同一个公司");
        }
    }

    protected void fillOrderFxSourcePros(Staff fxStaff, TradeConfig tradeConfig, Order order, Long destId, int matchType) {//  order分销订单标记
        fillOrderFxSourcePros(fxStaff, tradeConfig, order, destId, null, matchType);
    }

    protected void fillOrderFxSourcePros(Staff fxStaff, TradeConfig tradeConfig, Order order, Long destId, Double fxPrice, int matchType) {//  order分销订单标记
        if(!(Objects.equals(destId,0L) && OrderUtils.isGxOrMixOrder(order))){
            order.setDestId(destId);
        }
        if (fxPrice != null) {
            if (isSalePriceSourceFx(fxStaff, tradeConfig)) {
                order.setSalePrice(String.valueOf(fxPrice));
                order.setSaleFee(new BigDecimalWrapper(fxPrice).multiply(order.getNum()).getString());
            }
            if (isCostSourceFx(fxStaff, tradeConfig)) {
                order.setCost(fxPrice);
            }
        }

        if (destId != null && destId > 0) {
            order.setConvertType(1);
            if (OrderUtils.isGxOrMixOrder(order)) {
                order.setBelongType(3);
            } else {
                order.setBelongType(1);
                order.setSourceId(order.getCompanyId());
            }
        } else {
            //手动，不管匹配与否都要打标记
            if (matchType == 2) {
                order.setConvertType(1);
                if (OrderUtils.isGxOrMixOrder(order)) {
                    order.setBelongType(3);
                } else {
                    order.setBelongType(1);
                    order.setSourceId(order.getCompanyId());
                }
            } else if (!OrderUtils.isGxOrMixOrder(order)) {
                order.setConvertType(0);
                order.setBelongType(0);
            }
            if (isSalePriceSourceFx(fxStaff, tradeConfig)) {
                order.setSalePrice("0.00");
            }
        }
        if (OrderUtils.isGxAndFxOrder(order) && order.getSourceId() != null && order.getDestId() != null
                && order.getSourceId().equals(order.getDestId())) {
            throw new RuntimeException("不能设置订单的供销商和分销商为同一个公司");
        }
    }

    /**
     * 组装最后的tradeList
     *
     * @param user
     * @param trade
     * @param matchType
     * @param needSplit
     * @return
     */
    protected List<Trade> assambleTrade(User user, TradeConfig tradeConfig, Trade trade, int matchType, boolean needSplit) {
        // 供销订单不拆分
        if (TradeUtils.isGxOrMixTrade(trade)) {
            needSplit = false;
        }
        Staff staff = user.getStaff();
        int supplyType = calFxTradeSupplyType(staff, trade, matchType);
        List<Trade> tradeList = new ArrayList<>();
        if (SAME_SUPPLY == supplyType) {
            fillTradeFxSourcePros(staff, trade, matchType);
            tradeList.add(trade);
        } else if (AMBIGUITY_SUPPLY == supplyType) {
            if (matchType != 2) {//自动流程
                if (needSplit) {
                    List<Trade> splitTrades = splitBySupplyId(staff, trade);
                    for (Trade splitTrade : splitTrades) {//splitBySupplyId已经塞过值了
                        fillTradeFxSourcePros(staff, splitTrade, matchType);
                    }
                    tradeList.addAll(splitTrades);
                } else {
                    for (Order order : TradeUtils.getOrders4Trade(trade)) {
                        fillOrderFxSourcePros(staff, tradeConfig, order, order.getDestId(), matchType);
                    }
                    fillTradeFxSourcePros(staff, trade, matchType);
                    tradeList.add(trade);
                }
            } else {//手动添加流程
                if (TradeUtils.isGxOrMixTrade(trade)) {
                    trade.setBelongType(3);
                } else {
                    TradeUtils.setSourceId(trade, user.getCompanyId());
                    trade.setBelongType(1);
                }
                TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_AMBIGUITY, 1L);
                trade.setConvertType(1);
                trade.setIsExcep(1);
                tradeList.add(trade);
            }

        } else {
            //快卖通比较特殊，里面商品如果匹配了不同的供销商，supplyType = NORMAL，视为正常订单，在这里处理自动拆分
            if (matchType != 2 && staff.getCompany().isKmt() && needSplit) {
                List<Trade> splitTrades = splitBySupplyId(staff, trade);
                //拆出数量大于1，表示正确拆分
                if (splitTrades.size() > 1) {
                    for (Trade splitTrade : splitTrades) {
                        fillTradeFxSourcePros(staff, splitTrade, matchType);
                    }
                }
                tradeList.addAll(splitTrades);
            } else {
                tradeList.add(trade);
            }
        }
        return tradeList;
    }


    public List<Trade> splitBySupplyId(Staff staff, Trade trade) {
        TradeConfigNew splitPlatGiftFollowMainOrderEnum = tradeConfigNewService.get(staff, TradeConfigEnum.SPLIT_PLAT_GIFT_FOLLOW_MAIN_ORDER);
        String splitPlatGiftFollowMainOrder = splitPlatGiftFollowMainOrderEnum.getConfigValue();
        // 拆分后没有供销商不明确异常
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_AMBIGUITY)) {
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_AMBIGUITY, 0L);
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        Map<Long, List<Order>> orderMapByDestId = new HashMap<>();
        GiftFollowMasterContext giftFollowMasterContext = PlatGiftSplitUtils.initGiftFollowMasterContext(staff, orderList);
        List<Long> hasUseOrderIds = new ArrayList<>();
        for (Order order : orderList) {
            Long destId = Optional.ofNullable(order.getDestId()).orElse(0L);
            if (hasUseOrderIds.contains(order.getId())) {
                continue;
            }
            if (Objects.equals(splitPlatGiftFollowMainOrder, "1")) {
                List<Order> followedGiftOrders = PlatGiftSplitUtils.getFollowedGiftOrders(staff, giftFollowMasterContext.getGiftOrderIdsFollowMasterOrderMap(), order, giftFollowMasterContext.getOrderMap());
                Logs.debug(LogHelper.buildLog(staff, String.format("主品orderId=%s 对应的赠品orderId=%s", order.getId(), OrderUtils.toIdList(followedGiftOrders))));
                if (CollectionUtils.isEmpty(followedGiftOrders)) {
                    orderMapByDestId.computeIfAbsent(destId, k -> new ArrayList<>()).add(order);
                    hasUseOrderIds.add(order.getId());
                } else {
                    orderMapByDestId.computeIfAbsent(destId, k -> new ArrayList<>()).addAll(followedGiftOrders);
                    hasUseOrderIds.addAll(OrderUtils.toIdList(followedGiftOrders));
                }
            } else {
                orderMapByDestId.computeIfAbsent(destId, k -> new ArrayList<>()).add(order);
                hasUseOrderIds.add(order.getId());
            }
        }
        List<Trade> splitTradeList = new ArrayList<>();
        //只有一个destId,直接返回
        if (orderMapByDestId.size() <= 1) {
            splitTradeList.add(trade);
            return splitTradeList;
        }
        Long mainTradeKey = null;
        for (Long key : orderMapByDestId.keySet()) {
            mainTradeKey = key;
            if (mainTradeKey == 0) {
                break;
            }
        }

        for (Map.Entry<Long, List<Order>> entry : orderMapByDestId.entrySet()) {
            Long key = entry.getKey();
            if (key.longValue() != mainTradeKey.longValue()) {
                Trade splitTrade = buildSplitTrade(staff, trade, entry.getValue(), entry.getKey());
                splitTradeList.add(splitTrade);
            }
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        List<Order> mainOrders = orderMapByDestId.get(mainTradeKey);
        TradeUtils.setOrders(trade, mainOrders);
        TradeUtils.resetTradeItemNum(trade, tradeConfig);
        splitTradeList.add(trade);

        for (Trade splitTrade : splitTradeList) {
            calculate(splitTrade, trade.getSid(), tradeConfig);
        }

        return splitTradeList;
    }

    private Trade buildSplitTrade(Staff staff, Trade origin, List<Order> splitOrders, Long destId) {
        Trade trade = tradeCopier.copy(origin, new TbTrade());
        if (TradeUtils.isGxTrade(trade)) {
            trade.setBelongType(3);
        } else {
            trade.setBelongType(1);
            TradeUtils.setSourceId(trade, origin.getCompanyId());
        }
        trade.setConvertType(1);
        trade.setDestId(destId);
        trade.setWarehouseMatchRuleId(0L);
        trade.setWarehouseId(0L);
        trade.setSid(0L);
        trade.setPostFee("0.00");//拆分出来的订单运费为0
        trade.setActualPostFee("0.00");//拆分出来的订单实际运费为0
        trade.setTaxFee("0.00");//拆分出来的税费为0
        trade.setDiscountFee("0.00");//拆分出来的订单优惠为0
        trade.setPackmaCost(0d);//拆分出来的包材商品成本为0
        trade.setOutSid("");
        TradeUtils.setOrders(trade, splitOrders);
        TradeUtils.resetTradeItemNum(trade, tradeConfigService.get(staff));
        if (splitOrders.stream().allMatch(o->StringUtils.isNotBlank(o.getSupplierOuterId()) && Objects.nonNull(o.getSupplierCompanyId()))){
            trade.addV(TradeConstants.V_IF_KMT_DF);
        }
        return trade;
    }


    public int calFxTradeSupplyType(Staff staff, Trade trade, int matchType) {//计算已计算供销商id状态
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orderList)) {
            return NORMAL;
        }
        Map<Long, List<Order>> destIdSet = new HashMap<>();
        for (Order order : orderList) {
            if (order.getDestId() == null) {
                destIdSet.computeIfAbsent(0L, x -> new ArrayList<>()).add(order);
            } else {
                destIdSet.computeIfAbsent(order.getDestId(), x -> new ArrayList<>()).add(order);
            }
        }
        if (destIdSet.size() == 1 && !destIdSet.containsKey(0L) && !destIdSet.containsKey(trade.getCompanyId())) {
            return SAME_SUPPLY;
            // 都没有供销商 且是自动模式
        } else if (destIdSet.size() == 1 && (destIdSet.containsKey(0L) || destIdSet.containsKey(trade.getCompanyId())) && matchType != 2) {
            return NORMAL;
        } else {
            if (staff.getCompany().isKmt()) {
                return NORMAL;
            }
            FxLogBuilder append = new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).appendTrade(trade).append("存在多个供销商:");
            for (Map.Entry<Long, List<Order>> entry : destIdSet.entrySet()) {
                append.append(entry.getKey() + "", entry.getValue().stream().map(x -> x.getSysOuterId()).collect(Collectors.toList()));
            }
            append.printDebug(logger);
            return AMBIGUITY_SUPPLY;
        }


    }

    private void calculate(Trade trade, Long splitSid, TradeConfig tradeConfig) {

        //重新计算订单的相关信息，并筛选出拆出去的订单
        trade.setSplitSid(splitSid);
        trade.setSplitType(TradeSplitEnum.SPLIT_SKU.getDbType());
        //拆出去的订单下的子订单重新设置sid
        if (trade.getSid() == 0) {
            trade.setSid(idWorkerService.nextId());
            SplitUtils.resetSplitOrderSids(TradeUtils.getOrders4Trade(trade), trade.getSid());
        }
        trade.setSource(TradeUtils.getTradeSource(trade));//重新计算source
        trade.setSysStatus(TradeStatusUtils.getTradeSysStatus(trade));//重新计算系统状态
        trade.setUnifiedStatus(TradeStatusUtils.getUnifiedStatus(trade.getStatus()));
        TradeUtils.resetTradeItemNum(trade, tradeConfig);//商品数量和种类计算
        PaymentUtils.calculateTrade(trade);//金额计算
    }


    public void cancelGxConsign(Staff staff, Long[] sidArr) {
        tradeService.cancelConsign(staff, sidArr, 0, false, true, false);
    }

    public List<Trade> cancelGxTrade(CancelData cancelData) {
        Staff staff = cancelData.getStaff();
        try {
            List<Trade> updateTrades = cancelBusiness.cancel(cancelData);
            if (CollectionUtils.isNotEmpty(updateTrades)) {
                //取消电子面单号
                List<Trade> needCancelOutSidTrades = updateTrades.stream().filter(trade -> StringUtils.isNotBlank(trade.getOutSid()) && !trade.hasOpV(OpVEnum.TRADE_GX_EXTEND_OUTSID)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(needCancelOutSidTrades)) {
                    expressTemplateCommonService.cancelTidSysTidOutSid(staff, needCancelOutSidTrades, EnumOutSidStatus.BOUND.getValue());
                }
            }
            return updateTrades;
        } catch (Exception e) {
            new FxLogBuilder(staff, FxLogBuilder.ROLE_GX).append("作废供销订单失败").append("sids", Arrays.asList(cancelData.getSidArr())).printError(logger, e);
            return null;
        }
    }

    /**
     * 分销促使供销变为订单作废统一走这个入口
     *
     * @param staff
     * @param sidArr
     * @param cancelGxTrades（没有sid,只有tid）
     */
    public List<Trade> cancelGxTrade(Staff staff, Long[] sidArr, List<Trade> cancelGxTrades) {
        CancelData cancelData = CancelData.builder()
                .staff(staff).sidArr(sidArr)
                .gxTrades(cancelGxTrades).isCancelAttr(0)
                .build();
        return cancelGxTrade(cancelData);
    }


    /**
     * 允许供销订单与分销订单的商品信息和订单信息不一致模式下：
     * 1、供销拆单需要一起反审核
     *
     * 重新审核供销订单
     * @param staff
     * @param sidArr
     */
    public void cancelAuditGxTrade(Staff staff,OpEnum opEnum,Long[] sidArr) {
        try {
            if(!(tradeLocalConfigurable.isTradeGxEditNotRelFxLog(staff.getCompanyId()) || TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen())){
                tradeAuditService.unaudit(staff,opEnum,sidArr);
            }else {
                List<TbTrade> gxTradeList = tbTradeDao.queryByKeys(staff,"sid,tid,split_sid,split_type,v,is_cancel,convert_type,belong_type,dest_id,source_id","sid",sidArr);
                Map<Boolean, List<Trade>> gxTradeMap = gxTradeList.stream().collect(Collectors.groupingBy(t-> !TradeUtils.isCancel(t) && TradeUtils.isSplit(t)));

                Set<Long> sids = Sets.newHashSet(sidArr);
                List<Trade> gxTrades = gxTradeMap.get(Boolean.TRUE);

                if(CollectionUtils.isNotEmpty(gxTrades)){
                    Long[] splitSids = gxTrades.stream().map(Trade::getSplitSid).collect(Collectors.toSet()).toArray(new Long[0]);
                    List<TbTrade> splitGxTradeList = tbTradeDao.queryByKeys(staff,"sid,tid,split_sid,split_type,v,is_cancel,convert_type,belong_type,dest_id,source_id","split_sid", splitSids);


                    if(CollectionUtils.isNotEmpty(splitGxTradeList)){

                        sids.addAll(splitGxTradeList.stream().filter(t->!TradeUtils.isCancel(t)).map(Trade::getSid).collect(Collectors.toSet()));

                        FxLogBuilder logBuilder = FxLogBuilder.gx(staff).append("开启了允许供销订单和分销订单不一一对应，分销反审核，作废供销订单").startWatch();
                        logBuilder.appendArray(TradeUtils.toSidList(splitGxTradeList));
                        logBuilder.printDebug(logger);
                    }
                }
                tradeAuditService.unaudit(staff,opEnum,sids.toArray(new Long[0]));
            }
        } catch (Exception e) {
            new FxLogBuilder(staff,FxLogBuilder.ROLE_GX).append("取消审核供销订单出错").append("sids",Arrays.asList(sidArr)).printError(logger,e);
        }
    }


    /**
     * 重新审核分销订单
     *
     * @param staff
     * @param sidArr
     */
    public List<Trade> cancelAuditFxTrade(Staff staff, Long[] sidArr) {
        try {
            tradeAuditService.unaudit(staff, OpEnum.AUDIT_UNDO_AUTO_FX, sidArr);
        } catch (Exception e) {
            new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).append("取消审核分销订单出错").append("sids", Arrays.asList(sidArr)).printError(logger, e);
        }
        return null;
    }

    /**
     * 合并订单修改地址，重新审核分销订单
     * @param staff
     * @param sidArr
     */
    public void cancelAuditFxMergeTrade(Staff staff, Long[] sidArr) {
        List<Trade> successTrade = cancelAuditFxTrade(staff, sidArr);
        if (CollectionUtils.isNotEmpty(successTrade)){
            //供销订单记录操作日志
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_MERGE_UNAUDIT).setArgs(new Object[]{staff}), buildSimpFxTrade(successTrade));
        }
    }


    /**
     * 根据规则计算供销订单分销价
     *
     * @param staff
     * @param tradeList
     */
    public void caculateGxPrice(Staff staff, List<Trade> tradeList, boolean needFillControlPrice) {
        if(CollectionUtils.isEmpty(tradeList)){
            return;
        }
        tradeList = tradeList.stream().filter(t->!t.hasOpV(OpVEnum.TRADE_GX_HAS_CALC_GX_PRICE)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(tradeList)){
            return;
        }
        Map<Long, List<QueryDmsPriceOrderInfo>> dmsPriceOrderInfoListMap = new HashMap<>();
        Long sourceId = null;
        for (Trade trade : tradeList) {
            sourceId = trade.getSourceId();
            Long destId = trade.getDestId();
            List<QueryDmsPriceOrderInfo> queryDmsPriceOrderInfoList = dmsPriceOrderInfoListMap.get(destId);
            if (queryDmsPriceOrderInfoList == null) {
                queryDmsPriceOrderInfoList = new ArrayList<>();
                dmsPriceOrderInfoListMap.put(destId, queryDmsPriceOrderInfoList);
            }
            queryDmsPriceOrderInfoList.addAll(convertTradeToPriceOrderInfo(trade));
            trade.addOpV(OpVEnum.TRADE_GX_HAS_CALC_GX_PRICE);
        }

        Map<Long, DmsPriceInfoDto> dmsPriceInfoDtoMap = new HashMap<>();

        for (Map.Entry<Long, List<QueryDmsPriceOrderInfo>> entry : dmsPriceOrderInfoListMap.entrySet()) {
            Long destId = entry.getKey();
            List<QueryDmsPriceOrderInfo> queryDmsPriceOrderInfoList = entry.getValue();
            List<List<QueryDmsPriceOrderInfo>> partition = Lists.partition(queryDmsPriceOrderInfoList, 10);
            for (List<QueryDmsPriceOrderInfo> queryDmsPriceOrderInfos : partition) {
                List<DmsPriceInfoDto> dmsPriceInfoDtos = null;
                try {
                    if (ConfigHolder.FX_GLOBAL_CONFIG.open("mockDmsPriceException", staff.getCompanyId())) {
                        throw new RpcException("获取分销价失败!!!");
                    }

                    DmsQueryDmsPriceInfoRequest request = new DmsQueryDmsPriceInfoRequest();
                    request.setNeedFillControlPrice(needFillControlPrice);
                    request.setOrderInfoList(queryDmsPriceOrderInfos);
                    request.setSupplierCompanyId(destId);
                    request.setDistributorCompanyId(sourceId);
                    dmsPriceInfoDtos = dmsTradeService.queryDmsPriceInfo(request);
                    //dmsOrderPriceDtos = dmsTradeService.queryDmsPrice(destId, sourceId, queryDmsPriceOrderInfos);
                } catch (Exception ex) {
                    logger.debug(new FxLogBuilder(staff, String.format("获取分销价失败,destId:%s,sourceId:%s", destId, sourceId)).toString(), ex);
                }
                if (dmsPriceInfoDtos != null) {
                    for (DmsPriceInfoDto dmsPriceInfoDto : dmsPriceInfoDtos) {
                        dmsPriceInfoDtoMap.put(dmsPriceInfoDto.getOid(), dmsPriceInfoDto);
                    }
                }
            }
        }
        TradeItemContext tradeItemContext = new TradeItemContext();
        FxLogBuilder logBuilder = new FxLogBuilder(staff, FxLogBuilder.ROLE_GX).append("查询分销价").startWatch();
        for (Trade trade : tradeList) {
            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
            boolean matched = false;
            for (Order order : orderList) {
                // 商品搭配的不需要再次取分销价
                if(OrderUtils.isContainV(order,OrderConstant.V_GX_ITEM_EDIT_NOT_REL_FX)){
                    continue;
                }
                DmsPriceInfoDto dmsPriceInfoDto = dmsPriceInfoDtoMap.get(order.getOid());
                if (dmsPriceInfoDto != null) {
                    Double dmsOrderPrice = dmsPriceInfoDto.getPrice();
                    if (dmsOrderPrice == null) {
                        dmsOrderPrice = 0.0;
                        logBuilder.group(trade.getSid(),OrderUtils.getFxTrueOuterId(trade,order)+":价格为null");
                    } else {
                        order.addOpV(OpVEnum.ORDER_MATCH_FX_PRICE);
                    }

                    if (needFillControlPrice && dmsPriceInfoDto.checkOrderPriceBroken()) {
                        new DevLogBuilder(staff).appendHead("破价异常-快麦通返回标识")
                                .appendTrade(trade).printDebug(logger);
                        trade.addOpV(OpVEnum.GX_TRADE_CONTROL_PRICE_EXCEPT);
                    }
                    logBuilder.group(trade.getSid(), OrderUtils.getFxTrueOuterId(trade, order) + ":" + dmsOrderPrice);



                    String total = new BigDecimalWrapper(dmsOrderPrice).multiply(order.getNum()).getString();
                    matched = true;
                    order.setPrice(dmsOrderPrice + "");
                    //应付
                    order.setTotalFee(total);
                    //供销订单不允许修改价格，就实付就等于应付了
                    order.setPayment(total);
                    order.setPayAmount(total);
                } else {
                    logBuilder.group(trade.getSid(),OrderUtils.getFxTrueOuterId(trade,order)+":无返回");
                    //处理接口无返回oid对应商品
                    trade.addV(TradeConstants.V_TRADE_FX_PRICE_EXCEPT);
                }
            }
            //套件实付金额分摊
            tradeSuitCalculateBusiness.calculate(staff, orderList, tradeItemContext, false);
            PaymentUtils.calculateTrade(trade);//重算金钱
            if (trade.getV() != null && (trade.getV() | TradeConstants.V_TRADE_FX_PRICE_EXCEPT) - trade.getV() == 0) {
                TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_WAITPAY, 1L);
                trade.setIsExcep(1);
                trade.getOperations().putIfAbsent(OpEnum.FX_PRICE_EXCEPT, "未获取到商品的分销价，所以没有自动付款，请手动执行分销商付款。");
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLog(staff, String.format("标记分销商付款异常:%s", trade.getSid())));
                }
            }
        }
        //logBuilder.multiPrintDebug(logger);
    }

    private List<QueryDmsPriceOrderInfo> convertTradeToPriceOrderInfo(Trade trade) {
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        List<QueryDmsPriceOrderInfo> queryDmsPriceOrderInfoList = new ArrayList<>();
        for (Order order : orderList) {
            // 商品搭配的不需要再次取分销价
            if(OrderUtils.isContainV(order,OrderConstant.V_GX_ITEM_EDIT_NOT_REL_FX)){
                continue;
            }
            QueryDmsPriceOrderInfo queryDmsPriceOrderInfo = new QueryDmsPriceOrderInfo();
            queryDmsPriceOrderInfo.setAuditPushTime(new Date());
            queryDmsPriceOrderInfo.setCreateTime(trade.getCreated());
            queryDmsPriceOrderInfo.setPayTime(trade.getPayTime());
            queryDmsPriceOrderInfo.setOid(order.getOid());
            queryDmsPriceOrderInfo.setOuterId(OrderUtils.getFxTrueOuterId(trade, order));
            queryDmsPriceOrderInfo.setOrderNum(order.getNum());
            queryDmsPriceOrderInfo.setSource(QueryDmsPriceSourceEnum.TRADE);
            queryDmsPriceOrderInfoList.add(queryDmsPriceOrderInfo);
        }
        return queryDmsPriceOrderInfoList;
    }


    /**
     * 是否开启自动支付，如果关闭，则需要标记异常
     *
     * @param staff
     * @param tradeList
     */
    public void caculateWaitPay(Staff staff, List<Trade> tradeList) {
        Map<Long, List<Trade>> sourceIdTradeListMap = new HashMap<>();
        for (Trade trade : tradeList) {
            List<Trade> sourceIdTradeList = sourceIdTradeListMap.get(trade.getSourceId());
            if (sourceIdTradeList == null) {
                sourceIdTradeList = new ArrayList<>();
                sourceIdTradeListMap.put(trade.getSourceId(), sourceIdTradeList);
            }
            sourceIdTradeList.add(trade);
        }
        Map<String, TradeConfigNew> configMap = tradeConfigNewService.getMap(staff, CashFlowUtils.REISSUE_OR_CHANGE_ITEM_FLOW_ENUMS);
        for (Map.Entry<Long, List<Trade>> entry : sourceIdTradeListMap.entrySet()) {
            Long sourceId = entry.getKey();
            List<Trade> sourceIdTradeList = entry.getValue();
            Long companyId = staff.getCompanyId();
            DmsSupplierForDisConfigDto dmsConfigs = dmsTradeService.queryDmsSupplierConfig(companyId, sourceId);
            Boolean autoPay = dmsConfigs.getAutoPay();
            if (autoPay == null || !autoPay) {
                List<Trade> needException = new ArrayList<>();
                FxLogBuilder log = new FxLogBuilder(staff, FxLogBuilder.ROLE_GX).append("补发换货单不用扣流水不标记异常:");
                for (Trade trade : sourceIdTradeList) {
                    if (CashFlowUtils.isReissueOrChangeItemPassCalFlow(staff, trade, configMap)) {
                        log.append(trade.getSid()).append(",");
                    } else {
                        needException.add(trade);
                    }
                }
                if (needException.size() != sourceIdTradeList.size()) {
                    logger.debug(log.toString());
                }
                // 供销拆单子单不需要打上待付款异常
                needException = needException.stream().filter(t->TradeUtils.ifNeedAddWaitPayExcept(t)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(needException)) {
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLog(staff, String.format("标记分销商付款异常:%s", TradeUtils.toSidList(needException))));
                    }
                    for (Trade trade : needException) {
                        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_WAITPAY, 1L);
                        trade.setIsExcep(1);
                    }
                }
            } else {
                sourceIdTradeList.stream().filter(Trade::getIfFxForcePushTrade).forEach(trade -> {
                    TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.FX_WAITPAY, 1L);
                });
            }

        }

    }

    /**
     * 后面再封装
     * 逻辑：
     * 1、currentTrade的isCancel＝1的，gx订单全部作废，并且currentTrade清空
     * 2、如果currentTrade对应的existGxTrade存在orderOid完全一致，但是tid不一样的，更新
     * 2、currentTrade的FxSysStatus＝待审核,currentTrade和对应的existGxTrade下的orderOid不完全一样的，gx订单作废，
     * 3、currentTrade的FxSysStatus＝待审核,currentTrade和对应的existGxTrade下的orderOid完全一样的，非作废gx订单反审核处理
     * 4、currentTrade的SysStatus＝已审核或者以后,currentTrade对应的existGxTrade下的orderOid（不包含已关闭订单）不完全一样的，
     *
     * @param user
     * @param trades
     */
    public List<Trade> preHandleGxTrade(User user, List<Trade> trades) {
        List<Trade> currentTrades = trades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentTrades)) {
            return trades;
        }
        //找出所有的tid
        List<String> currentTids = TradeUtils.toTidList(currentTrades);
        if (CollectionUtils.isEmpty(currentTids)) {
            return currentTrades;
        }


        //更具tid查找供销的订单
        List<TbTrade> exsitedTbTrade = tradeSearchService.queryByTids(user.getStaff(), true, currentTids.toArray(new String[0]));

        if (CollectionUtils.isEmpty(exsitedTbTrade)) {
            return currentTrades;
        }
        List<Trade> exsitedTrades = TradeUtils.toTrades(exsitedTbTrade);


        //根据tid,sid分组
        Map<String, List<Trade>> currentTidToExistSidSetMap = exsitedTrades.stream().collect(Collectors.groupingBy(Trade::getTid));
        Map<Long, Trade> existGxSidTradeMap = TradeUtils.toMapBySid(exsitedTrades);


        //需要作废的供销sid
        Set<Long> needCancelGxSidSet = new HashSet<>();
        Set<String> needRemoveFromCurrentTidSet = new HashSet<>();
        //sid对应的tid修改为value（主单变化，但是order没有变）
        Map<Long, String> needChangeTidExistSidMap = new HashMap<>();
        coverGxTradeByFx(user.getStaff(),currentTrades, exsitedTrades);
        preHandleGxTrade(user, currentTrades, currentTidToExistSidSetMap, existGxSidTradeMap, needCancelGxSidSet, needRemoveFromCurrentTidSet, needChangeTidExistSidMap);

        if (logger.isDebugEnabled() && (CollectionUtils.isNotEmpty(needCancelGxSidSet) || CollectionUtils.isNotEmpty(needRemoveFromCurrentTidSet) || MapUtils.isNotEmpty(needChangeTidExistSidMap))) {
            logger.debug(LogHelper.buildLog(user.getStaff(), String.format("needCancelGxSidSet=%s,needRemoveFromCurrentTidSet=%s,needChangeTidExistSidMap=%s]", needCancelGxSidSet, needRemoveFromCurrentTidSet, needChangeTidExistSidMap)));
        }

        //开始处理作废供销sid
        if (CollectionUtils.isNotEmpty(needCancelGxSidSet)) {
            CancelData cancelData = CancelData.builder()
                    .staff(user.getStaff()).sidArr(needCancelGxSidSet.toArray(new Long[0]))
                    .gxTrades(currentTrades).opName("系统").opMsg("商品信息变更，自动作废订单").opType(FxCancelFromEnum.FX_ITEM_CHANGE.getType())
                    .isCancelAttr(0)
                    .build();
            List<Trade> successTrades = cancelGxTrade(cancelData);
            if (CollectionUtils.isNotEmpty(successTrades)) {
                List<String> needGenerateFlower = successTrades.stream().filter(t -> !needRemoveFromCurrentTidSet.contains(t.getTid())).map(Trade::getTid).collect(Collectors.toList());
                fxTradeNotifyBusiness.generateTempCashFlower(user.getStaff(), currentTrades.stream().filter(t -> needGenerateFlower.contains(t.getTid()) && t.getFlowNumber() == null).collect(Collectors.toList()));
            }
        }
        //处理changeSid场景
        handleChangeSid(user, needChangeTidExistSidMap, existGxSidTradeMap);
        return afterHandleCurrentTrades(needRemoveFromCurrentTidSet, currentTrades);
    }

    private List<Trade> afterHandleCurrentTrades(Set<String> needRemoveFromCurrentTidSet, List<Trade> currentTrades) {
        //用remove会 触发_importToDB(user,currentTrades.subList(fromIndex, endIndex),result,forceUpdate); 越界 原因见对list的subList remove会remove掉list的数据
        if (needRemoveFromCurrentTidSet.size() > 0) {
            List<Trade> newTradeList = new ArrayList<>();
            Iterator<Trade> iterator = currentTrades.iterator();
            while (iterator.hasNext()) {
                Trade currentTrade = iterator.next();
                if (!needRemoveFromCurrentTidSet.contains(currentTrade.getTid())) {
                    newTradeList.add(currentTrade);
                }
            }
            return newTradeList;
        } else {
            return currentTrades;
        }
    }

    private void handleChangeSid(User user, Map<Long, String> needChangeTidExistSidMap, Map<Long, Trade> existGxSidTradeMap) {
        //开始修改tid
        if ((needChangeTidExistSidMap.size() > 0)) {
            List<Trade> updateTradeList = new ArrayList<>();
            List<Order> updateOrderList = new ArrayList<>();
            for (Map.Entry<Long, String> entry : needChangeTidExistSidMap.entrySet()) {
                Long sid = entry.getKey();
                String tid = entry.getValue();
                //处理数据库
                Trade existGxTrade = existGxSidTradeMap.get(sid);
                Trade changeTidUpdateTrade = new TbTrade();
                changeTidUpdateTrade.setCompanyId(existGxTrade.getCompanyId());
                changeTidUpdateTrade.setTid(tid);
                changeTidUpdateTrade.setSid(existGxTrade.getSid());
                updateTradeList.add(changeTidUpdateTrade);
                List<Order> orderList = TradeUtils.getOrders4Trade(existGxTrade);
                for (Order order : orderList) {
                    Order changeTidUpdateOrder = new TbOrder();
                    changeTidUpdateOrder.setCompanyId(order.getCompanyId());
                    changeTidUpdateOrder.setId(order.getId());
                    changeTidUpdateOrder.setTid(tid);
                    updateOrderList.add(changeTidUpdateOrder);
                    if (order.getSuits() != null && order.getSuits().size() > 0) {
                        for (Order suit : order.getSuits()) {
                            Order changeTidUpdateSuitOrder = new TbOrder();
                            changeTidUpdateSuitOrder.setCompanyId(suit.getCompanyId());
                            changeTidUpdateSuitOrder.setId(suit.getId());
                            changeTidUpdateSuitOrder.setTid(tid);
                            updateOrderList.add(changeTidUpdateSuitOrder);
                        }
                    }
                }
            }
            if (updateTradeList.size() > 0 || updateOrderList.size() > 0) {
                tradeUpdateService.updateTrades(user.getStaff(), updateTradeList, updateOrderList);
            }
        }
    }

    public void recalculateGxTradeWaitAuditExcept(Staff staff, List<Trade> gxTrades) {
        for (Trade gxTrade : gxTrades) {
            if (Trade.SYS_STATUS_WAIT_AUDIT.equals(gxTrade.getFxSysStatus())) {
                TradeExceptUtils.updateExcept(staff, gxTrade, ExceptEnum.FX_UNAUDIT, 1L);
                Logs.debug(LogHelper.buildLog(staff,String.format("sid=%s标记[%s]异常",gxTrade.getSid(),ExceptEnum.FX_UNAUDIT.getChinese())));
            } else if (TradeExceptUtils.isContainExcept(staff, gxTrade, ExceptEnum.FX_UNAUDIT)) {
                TradeExceptUtils.updateExcept(staff, gxTrade, ExceptEnum.FX_UNAUDIT, 0L);

            }
        }
    }

    public void handleGxTradeTags(Staff staff, List<Trade> gxTrades) {
        //处理现场自提标签
        List<Trade> trades = gxTrades.stream().filter(Trade::getIfKmtSelfPick).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(trades)) {
            tradeSysLabelBusiness.addTags(staff, trades, OpEnum.ADD_TAG, Collections.singletonList(SystemTags.TAG_SELF_PICK));
        }
        //处理分销强推标签
        List<Trade> fxForcePushTrades = gxTrades.stream().filter(t -> t.hasOpV(OpVEnum.TRADE_FX_FORCE_PUSH)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fxForcePushTrades)){
            tradeSysLabelBusiness.addTags(staff, fxForcePushTrades, OpEnum.ADD_TAG, Collections.singletonList(SystemTags.TAG_FX_FORCE_PUSH));
            fxForcePushTrades.forEach(t ->{
                t.getOperations().put(OpEnum.TRADE_FX_FORCE_PUSH, "分销强推订单");
            });
        }
    }

    //加锁中查询分销覆盖供销 防并发问题
    private void coverGxTradeByFx(Staff staff,List<Trade> currentTrades, List<Trade> existTrades) {
        Map<Long, List<Trade>> sourceIdMap = existTrades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
        Map<String, Trade> currentTidTradeMap = TradeUtils.toMapByTid(currentTrades);
        FxLogBuilder log = new FxLogBuilder(staff, FxLogBuilder.ROLE_GX).append("coverGxTradeByFx:").startWatch();
        sourceIdMap.forEach((k, v) -> {
            Staff fxStaff = staffService.queryFullByCompanyId(k);
            if(fxStaff == null){
                log.group("分销staff为空",String.format("companyId=%s,sid=%s",k,TradeUtils.toSidList(v)));
                return;
            }
            List<Trade> fxTrades = tradeSearchService.queryBySids(fxStaff, false, v.stream().map(trade -> Long.parseLong(trade.getTid())).distinct().toArray(Long[]::new));
            if (CollectionUtils.isEmpty(fxTrades)) {
                return;
            }
            for (Trade fxTrade : fxTrades) {
                if (!currentTidTradeMap.containsKey(fxTrade.getSid().toString())) {
                    continue;
                }
                Trade currentTrade = currentTidTradeMap.get(fxTrade.getSid().toString());
                currentTrade.setFxSysStatus(fxTrade.getSysStatus());
            }
        });
        log.printWarn(logger);
    }

    private void preHandleGxTrade(User user, List<Trade> currentTrades, Map<String, List<Trade>> currentTidToExistSidSetMap, Map<Long, Trade> existGxSidTradeMap, Set<Long> needCancelGxSidSet, Set<String> needRemoveFromCurrentTidSet, Map<Long, String> needChangeTidExistSidMap) {
        DmsDistributorConfigDto dmsDistributorConfigDto = null;
        if (Constants.FxDefaultUserId.equals(user.getId()) && user.getTaobaoId() != null) {
            dmsDistributorConfigDto = dmsTradeService.queryDmsDistributorConfig(user.getTaobaoId());
        }

        for (Trade currentTrade : currentTrades) {
            String tid = currentTrade.getTid();
            if (TradeUtils.isGxOrMixTrade(currentTrade)) {
                List<Trade> trades = currentTidToExistSidSetMap.get(tid);
                if (CollectionUtils.isNotEmpty(trades)) {//
                    Set<Long> existSidSet = TradeUtils.toSidSet(trades);
                    if (currentTrade.getIsCancel() != null && currentTrade.getIsCancel() == 1) {//直接作废的trade，currentTradeMap也要清空
                        needCancelGxSidSet.addAll(existSidSet);
                        needRemoveFromCurrentTidSet.add(currentTrade.getTid());
                    } else {
                        boolean hasSame = false;
                        Long changeSid = null;
                        for (Long existSid : existSidSet) {
                            Trade existTrade = existGxSidTradeMap.get(existSid);
                            if (existTrade != null) {
                                if (!isSamePush(user, dmsDistributorConfigDto, currentTrade, existTrade) && !TradeStatusUtils.isAfterSendGoods(existTrade.getSysStatus())) {
                                    needCancelGxSidSet.add(existSid);
                                } else if (!existTrade.getTid().equals(currentTrade.getTid())) {
                                    if (logger.isDebugEnabled()) {
                                        logger.debug(LogHelper.buildLog(user.getStaff(), String.format("existTradeTid=%s,currentTradeTid=%s]", existTrade.getTid(), currentTrade.getTid())));
                                    }
                                    changeSid = existSid;
                                } else {
                                    hasSame = true;
                                }
                            }
                        }
                        if (hasSame) {//不做处理，正常逻辑
                            if (changeSid != null) {//理论上不可能有
                                needCancelGxSidSet.add(changeSid);
                            }
                        } else if (changeSid != null) {
                            if (logger.isDebugEnabled()) {
                                new FxLogBuilder(user.getStaff(), FxLogBuilder.ROLE_GX).appendTrade(currentTrade).format("hasSame=%s,changeSid=%s]", hasSame, changeSid).print(logger, Priority.DEBUG);
                            }
                            needChangeTidExistSidMap.put(changeSid, currentTrade.getTid());
                        } else {
                            //不做处理，做插入
                        }
                    }
                } else if (Trade.SYS_STATUS_WAIT_AUDIT.equals(currentTrade.getFxSysStatus())) {//做插入的订单，后面TradeChain的init单独处理哈
                    //  currentTrade.setExcep(TradeConstants.FX_UNAUDIT);
                    TradeExceptUtils.updateExcept(user.getStaff(), currentTrade, ExceptEnum.FX_UNAUDIT, 1L);
                    Logs.debug(LogHelper.buildLog(user.getStaff(),String.format("sid=%s标记[%s]异常",currentTrade.getSid(),ExceptEnum.FX_UNAUDIT.getChinese())));
                }
            }
        }
    }


    public boolean isSamePush(User user, DmsDistributorConfigDto dmsDistributorConfigDto, Trade currentTrade, Trade existTrade) {
        List<Order> currentOrders = TradeUtils.getOrders4Trade(currentTrade);
        List<Order> existOrders = TradeUtils.getOrders4Trade(existTrade);

        //供销这边 order的oid是分销order的id,因此一个oid一定只对应一个order
        Map<Long, String> currentOidLogMap = new HashMap<>();
        Map<Long, String> existOidLogMap = new HashMap<>();

        for (Order order : currentOrders) {
            buildLogs(order, currentOidLogMap);
        }
        for (Order order : existOrders) {
            buildLogs(order, existOidLogMap);
        }
        if (dmsDistributorConfigDto == null) {
            new FxLogBuilder(user.getStaff(), FxLogBuilder.ROLE_GX).format("没有查询到分销商信息[%s]", existTrade.getSid()).printDebug(logger);
            return false;
        }
        // 分销整单关闭不在对比
        if(Trade.SYS_STATUS_CLOSED.equals(currentTrade.getFxSysStatus())){
            return true;
        }
        // 供销进行过商品搭配/套转单后，分销修改过商品,供销单自动作废
        if(TradeUtils.isContainV(currentTrade,TradeConstants.V_IF_FX_EDIT_TRADE_ITEM) &&
                !TradeUtils.isContainV(existTrade,TradeConstants.V_IF_FX_EDIT_TRADE_ITEM) &&
                !TradeUtils.isCancel(existTrade) &&
                TradeUtils.ifContainV(existTrade, TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX)){
            new FxLogBuilder(user.getStaff(),FxLogBuilder.ROLE_GX).format("分销修改过商品,sid=%s,供销单自动作废", existTrade.getSid()).printDebug(logger);
            return false;
        }

        if(!TradeUtils.isCancel(existTrade) &&
                partRefund(currentTrade) &&
                TradeUtils.ifContainV(existTrade, TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX) &&
                TradeUtils.isSplit(existTrade)
        ){// 部分退款/发货时，做过商品搭配或者套转单，但分销的oid 和供销的oia存在不一致的情况，供销订单自动作废，重新生成一笔  -->  这种情况可能导致部分退款流水不对需要作废
            existTrade.addOpV(OpVEnum.TRADE_GX_EDIT_ITEM_PART_REFUND);
            currentTrade.addOpV(OpVEnum.TRADE_GX_EDIT_ITEM_PART_REFUND);
            currentTrade.setCancelFrom(9);
            new FxLogBuilder(user.getStaff(),FxLogBuilder.ROLE_GX).format("供销进行过商品搭配/套转单后修改过商品，订单部分退款[%s]", currentTrade.getSid()).printDebug(logger);
            return false;
        }

        if(!TradeUtils.isCancel(existTrade) &&
                partRefundOrPartSendGoods(currentTrade) &&
                TradeUtils.ifContainV(existTrade, TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX) &&
                !CollectionUtils.isEqualCollection(currentOrders.stream().filter(t -> !t.isGift()).map(OrderBase::getOid).collect(Collectors.toSet()),
                        existOrders.stream().filter(t -> !t.isGift()).map(OrderBase::getOid).collect(Collectors.toSet()))
        ){// 部分退款/发货时，做过商品搭配或者套转单，但分销的oid 和供销的oia存在不一致的情况，供销订单自动作废，重新生成一笔  -->  这种情况可能导致部分退款流水不对需要作废
            existTrade.addOpV(OpVEnum.TRADE_GX_EDIT_ITEM_PART_REFUND);
            currentTrade.addOpV(OpVEnum.TRADE_GX_EDIT_ITEM_PART_REFUND);
            currentTrade.setCancelFrom(8);
            new FxLogBuilder(user.getStaff(),FxLogBuilder.ROLE_GX).format("供销进行过商品搭配/套转单后修改过商品，订单部分/发货退款[%s]", currentTrade.getSid()).printDebug(logger);
            return false;
        }

        if(TradeUtils.ifContainV(existTrade, TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX)){
            return true;
        }

        // 供销修改过商品不对比商品编码是否一致
//        if(TradeUtils.ifContainV(existTrade,TradeConstants.V_IF_GX_EDIT_TRADE_ITEM)){
//            return true;
//        }
        //先比较Oid 系统商品 及数量
        if (!compareOid(user, currentTrade, existTrade, currentOidLogMap, existOidLogMap, currentOrders, existOrders)) {
            return false;
        }
        // 如果走供应商商家编码匹配 还需要判断outerId是否发生变更
        if (null != dmsDistributorConfigDto.getUseSupplierCode() && dmsDistributorConfigDto.getUseSupplierCode() != 0) {
            return compareItemOuterId(user, currentOidLogMap, existOidLogMap, currentOrders, existOrders);
        }
        return true;
    }

    private boolean partRefundOrPartSendGoods(Trade trade){

        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        boolean ifPartRefund = !Trade.SYS_STATUS_CLOSED.equals(trade.getFxSysStatus()) &&
                orders.stream().anyMatch(o->Order.REFUND_SUCCESS.equals(o.getRefundStatus()));

        if(ifPartRefund){
            return true;
        }

        boolean ifPartSendGoods = !NewFxTradeStatus.getNoConflictStatus(NewFxTradeStatus.SYS_STATUS_SELLER_SEND_GOODS).equals(trade.getStatus()) &&
                orders.stream().anyMatch(o->NewFxTradeStatus.getNoConflictStatus(NewFxTradeStatus.SYS_STATUS_SELLER_SEND_GOODS).equals(o.getStatus()));
        if(ifPartSendGoods){
            return true;
        }

        return !NewFxTradeStatus.getNoConflictStatus(NewFxTradeStatus.SYS_STATUS_FINISHED).equals(trade.getStatus()) &&
                orders.stream().anyMatch(o->NewFxTradeStatus.getNoConflictStatus(NewFxTradeStatus.SYS_STATUS_FINISHED).equals(o.getStatus()));
    }

    private boolean partRefund(Trade trade){

        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        boolean ifPartRefund = !Trade.SYS_STATUS_CLOSED.equals(trade.getFxSysStatus()) &&
                orders.stream().anyMatch(o->Order.REFUND_SUCCESS.equals(o.getRefundStatus()));

        return ifPartRefund;
    }

    /**
     * 再对比供应商编码和
     *
     * @param user
     * @param currentOrders
     * @param existOrders
     * @return
     */
    private boolean compareItemOuterId(User user, Map<Long, String> currentOidLogMap, Map<Long, String> existOidLogMap,
                                       List<Order> currentOrders, List<Order> existOrders) {
        List<String> currentItemOuterIds = Lists.newArrayListWithCapacity(currentOrders.size());
        for (Order currentOrder : currentOrders) {
            if (currentOrder.getOrderExt() == null || StringUtils.isBlank(currentOrder.getOrderExt().getSupplierName())) {
                FxLogBuilder.gx(user.getStaff()).append("比较分销和供销商品时没有供应商信息").append("orderId",currentOrder.getId())
                        .appendIf(()->StringUtils.isNotBlank(currentOrder.getSysOuterId()),"sysOuterId",currentOrder.getSysOuterId())
                        .appendIf(()->StringUtils.isNotBlank(currentOrder.getOuterIid()),"outerIid",currentOrder.getOuterIid())
                        .appendIf(()->StringUtils.isNotBlank(currentOrder.getOuterSkuId()),"outerSkuId",currentOrder.getOuterSkuId())
                        .printInfo(logger);
                continue;
            }
            currentItemOuterIds.add(currentOrder.getOrderExt().getSupplierItemOuterId());
        }

        List<String> exsitSysOuterIds = existOrders.stream().filter(t -> !t.isGift()).map(Order::getSysOuterId).collect(Collectors.toList());

        if (currentItemOuterIds.size() != exsitSysOuterIds.size()) {
            FxLogBuilder.gx(user.getStaff()).format("供应商编码对比，商品种类不相同,currentItemOuterIds %s, exsitSysOuterIds:%s", currentItemOuterIds, exsitSysOuterIds).printInfo(logger);
            return false;
        }

        //商品上，打上商品修改标识，不进行系统商品编码的比较
        exsitSysOuterIds = existOrders.stream().filter(t -> !t.isGift()).filter(t -> !OrderUtils.isContainV(t, OrderConstant.V_GX_ITEM_EDIT)).map(Order::getSysOuterId).collect(Collectors.toList());
        for (String exsitSysOuterId : exsitSysOuterIds) {
            if (!currentItemOuterIds.contains(exsitSysOuterId)) {
                FxLogBuilder.gx(user.getStaff()).format("供应商编码对比,分销商供应商商家编码与供销单系统商家编码不一致 %s", exsitSysOuterId).printInfo(logger);
                return false;
            }
        }
        return true;
    }

    /**
     * 先对比商品Id
     *
     * @param user
     * @param currentOrders
     * @param existOrders
     * @return
     */
    private boolean compareOid(User user, Trade currentTrade, Trade existsTrade, Map<Long, String> currentOidLogMap, Map<Long, String> existOidLogMap, List<Order> currentOrders, List<Order> existOrders) {
        Map<Long, Order> currentOidMap = new HashMap<>();
        Map<Long, Order> existOidMap = new HashMap<>();
        for (Order order : currentOrders) {
            currentOidMap.put(order.getOid(), order);
        }

        for (Order order : existOrders) {
            if (order.isGift()) {
                continue;
            }
            existOidMap.put(order.getOid(), order);
        }

        boolean same = true;

        List<Long> diffs = getDiff(currentOidMap.keySet(), existOidMap.keySet());
        if (CollectionUtils.isNotEmpty(diffs)) {
            same = false;
            FxLogBuilder builder = new FxLogBuilder(user.getStaff(), FxLogBuilder.ROLE_GX).append("供销同步多出商品:");
            for (Long diff : diffs) {
                builder.append(currentOidLogMap.get(diff));
            }
            logger.debug(builder.toString());
        }
        diffs = getDiff(existOidMap.keySet(), currentOidMap.keySet());
        if (CollectionUtils.isNotEmpty(diffs)) {
            same = false;
            FxLogBuilder builder = new FxLogBuilder(user.getStaff(), FxLogBuilder.ROLE_GX).append("供销已存在多出商品:");
            for (Long diff : diffs) {
                builder.append(existOidMap.get(diff));
            }
            logger.debug(builder.toString());
        }
        if (!same) {
            return false;
        }

        List<Long> currentOidSet = new ArrayList<Long>(currentOidMap.keySet());
        StringBuilder log = new StringBuilder();
        for (Long oid : currentOidSet) {
            Order currentOrder = currentOidMap.get(oid);
            Order originOrder = existOidMap.get(oid);
            if (OrderUtils.isContainV(existOidMap.get(oid), OrderConstant.V_GX_ITEM_EDIT)){
                //商品打上已修改的标识，不比较商品的变更
                continue;
            }
            if ((OrderUtils.isItemChanged(currentOidMap.get(oid), existOidMap.get(oid)) || OrderUtils.isFxItemChanged(currentOidMap.get(oid), existOidMap.get(oid)))) {
                logger.debug(new FxLogBuilder(user.getStaff(), FxLogBuilder.ROLE_GX).append("平台商品不相同:").append("同步商品", currentOidLogMap.get(oid)).append("已存在商品", existOidLogMap.get(oid)).toString());
                same = false;
            }
            if (!sameNum(oid, currentOidMap, existOidMap)) {
                logger.debug(new FxLogBuilder(user.getStaff(), FxLogBuilder.ROLE_GX).append("商品数量不相同:").append("同步商品", currentOidLogMap.get(oid)).append("已存在商品", existOidLogMap.get(oid)).toString());
                same = false;
            }
            if (!same) {
                handleItemChanged(user, currentTrade, existsTrade, currentOrder, originOrder, log);
            }
        }
        if (StringUtils.isNotEmpty(log.toString())) {
            currentTrade.getOperations().computeIfAbsent(OpEnum.FX_PLAT_ITEM_CHANGE, (k) -> String.format("在分销那边，平台商品更换，供销已做预发货，不作废供销订单，但是订单会反审核。%s", log.substring(0, log.length() - 1)));
        }
        return same;
    }

    /**
     * 处理供销平台信息变更
     *
     * @param user
     * @param currentTrade
     * @param currentOrder
     * @param originOrder
     */
    public void handleItemChanged(User user, Trade currentTrade, Trade existsTrade, Order currentOrder, Order originOrder, StringBuilder log) {
        if (null == currentTrade || null == existsTrade) {
            return;
        }
        if (!TradeUtils.isGxOrMixTrade(existsTrade)) {
            return;
        }
        if (TradeStatusUtils.isAfterSendGoods(existsTrade.getSysStatus())) {
            return;
        }
        //预发货后供销单不作废,打上“平台更换商品“的异常
        boolean preUploadConsign = TradeTagUtils.checkIfExistTag(existsTrade, SystemTags.TAG_PRE_UPLOAD_CONSIGN)
                || (existsTrade.getIsUpload() != null && existsTrade.getIsUpload() == 1);
        if (preUploadConsign && currentOrder != null && originOrder != null) {
            String outerId = StringUtils.isNotBlank(currentOrder.getOuterSkuId()) ? currentOrder.getOuterSkuId() : currentOrder.getOuterIid();
            String originOuterId = StringUtils.isNotBlank(originOrder.getOuterSkuId()) ? originOrder.getOuterSkuId() : originOrder.getOuterIid();
            log.append(String.format("平台商家编码：%s，数量：%s ->平台商家编码：%s，数量：%s,", originOuterId, originOrder.getNum(), outerId, currentOrder.getNum()));
        }
    }

    /**
     * 获取source中存在但是target中不存在的
     *
     * @param source
     * @param target
     * @return
     */
    private <T> List<T> getDiff(Collection<T> source, Collection<T> target) {
        List<T> diffs = new ArrayList<>();
        for (T s : source) {
            if (!target.contains(s)) {
                diffs.add(s);
            }
        }
        return diffs;
    }

    private void buildLogs(Order order, Map<Long, String> oidLogMap) {
        StringBuilder builder = new StringBuilder();
        appendIfNotNull(builder, "id", order.getId());
        appendIfNotNull(builder, "sid", order.getSid());
        appendIfNotNull(builder, "oid", order.getOid());
        appendIfNotNull(builder, "outerSkuId", order.getOuterSkuId());
        appendIfNotNull(builder, "outerIid", order.getOuterIid());
        appendIfNotNull(builder, "sysOuterId", order.getSysOuterId());
        appendIfNotNull(builder, "sid", order.getSid());
        appendIfNotNull(builder, "num", order.getNum());
        if (order.getOrderExt() != null && StringUtils.isNotBlank(order.getOrderExt().getSupplierItemOuterId())) {
            appendIfNotNull(builder, "supplierItemOuterId", order.getOrderExt().getSupplierItemOuterId());
        }
        builder.append("]");
        oidLogMap.put(order.getOid(), builder.toString());
    }


    private void appendIfNotNull(StringBuilder builder, String key, Object value) {
        if (value != null) {
            builder.append(key).append(":").append(value).append(",");
        }
    }

    /**
     * 商品种类相同的情况下 比较商品数量是否相同
     *
     * @param oid
     * @param currentOidMap
     * @param existOidMap
     * @return
     */
    private boolean sameNum(Long oid, Map<Long, Order> currentOidMap, Map<Long, Order> existOidMap) {
        Integer currentNum = currentOidMap.get(oid) == null ? 0 : currentOidMap.get(oid).getNum();
        Integer exsitNum = existOidMap.get(oid) == null ? 0 : existOidMap.get(oid).getNum();
        ;
        if (currentNum.equals(exsitNum)) {
            return true;
        }
        return false;
    }

    /**
     * 分销状态变化通知供销订单统一入口
     *
     * @param staff
     * @param trades
     */
    public void notifyGxDownload(Staff staff, List<Trade> trades, int type) {
        if (CollectionUtils.isNotEmpty(trades)) {
            //分销订单并且有对应的供销订单存在
            List<Trade> fxTrades = getGxTradesByTid(trades);
            List<Long> sids = TradeUtils.toSidList(fxTrades);
            //作废通知供销
            if (type == 0) {
                if(fxTrades.size() != trades.size()){
                    // 已审核及后面的状态触发供销作废： 如果没有生成供销订单的，写入缓存。供销单生成时候消费掉 --> this.doConsumeTradeFxCancel()
                    trades.stream().filter(t->!fxTrades.contains(t) && (TradeStatusUtils.isAfterSendGoods(t.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(t.getSysStatus())))
                            .forEach(t->{
                                sharedCacheBusiness.set(String.format("trade_fx_cancel_%s",t.getDestId()),1);
                                sharedCacheBusiness.set(String.format("trade_fx_cancel_%s_%s",t.getDestId(),t.getSid()),2);
                            });
                }
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(fxTrades)) {
                //作废通知供销
                if (type == 0) {
                    eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_CANCEL).setArgs(new Object[]{staff, sids.toArray(new Long[0])}), null);
                }
                //撤销发货通知供销
                if (type == 1) {
                    eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_DOWNLOAD).setArgs(new Object[]{staff, sids.toArray(new Long[0])}), null);
                }
                //取消分销属性通知供销
                if (type == 2) {
                    Lists.partition(fxTrades, 100).forEach(part -> {
                        eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_CANCEL_ATTRIBUTE).setArgs(new Object[]{staff}), buildSimpFxTrade(part));
                    });
                }
                //分销订单更改分销商通知原供销,100个订单一批
                if (type == 3) {
                    Lists.partition(fxTrades, 100).forEach(part -> {
                        eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_CHANGE_ATTRIBUTE).setArgs(new Object[]{staff}), buildSimpFxTrade(part));
                    });
                }
            }
        }
    }

    private List<Trade> buildSimpFxTrade(List<Trade> fxTrades) {
        List<Trade> result = new ArrayList<>();
        fxTrades.forEach(fxTrade -> {
            Trade simpTrade = new Trade();
            simpTrade.setSid(fxTrade.getSid());
            simpTrade.setTid(fxTrade.getTid());
            simpTrade.setBelongType(fxTrade.getBelongType());
            simpTrade.setConvertType(fxTrade.getConvertType());
            simpTrade.setDestId(fxTrade.getDestId());
            simpTrade.setSourceId(fxTrade.getSourceId());
            simpTrade.setMergeSid(fxTrade.getMergeSid());
            simpTrade.setV(fxTrade.getV());
            result.add(simpTrade);
        });
        return result;
    }

    public void notifyCancelFxAndGx(Staff staff, List<Trade> trades, int delMaxCount) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        if (delMaxCount-- == 0) {
            if (logger.isDebugEnabled()) {
                new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).append("取消分销属性通知供销商处理次数超过最大次数").printDebug(logger);
            }
            return;
        }
        List<Trade> tradeList = new ArrayList<>();
        Map<Long, List<Trade>> destIdTradeMap = trades.stream().collect(Collectors.groupingBy(Trade::getDestId));
        for (Map.Entry<Long, List<Trade>> next : destIdTradeMap.entrySet()) {
            Long destCompanyId = next.getKey();
            List<Trade> fxTrade = next.getValue();
            if (destCompanyId <= 0) {
                continue;
            }
            Staff gxStaff = staffService.queryFullByCompanyId(destCompanyId);
            List<String> sidList = new ArrayList<>();
            for (Trade trade : fxTrade) {
                sidList.add(TradeUtils.isMerge(trade) ? trade.getMergeSid().toString() : trade.getSid().toString());
            }
            List<TbTrade> gxTrades = tradeSearchService.queryByTids(gxStaff, true, sidList.toArray(new String[0]));
            if (CollectionUtils.isNotEmpty(gxTrades)) {
                List<TbTrade> gxAndFxTrade = gxTrades.stream().filter(TradeUtils::isGxAndFxTrade).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(gxAndFxTrade)) {
                    List<Trade> sameDestTradeList = TradeUtils.toTrades(gxAndFxTrade);
                    notifyGxDownload(gxStaff, sameDestTradeList, 2);
                    tradeList.addAll(sameDestTradeList);
                }
            }
        }
        notifyCancelFxAndGx(staff, tradeList, delMaxCount);
    }

    /**
     * 不能用分销订单已审核判断供销就一定存在
     * 这里必须要查一次
     * <p>
     * 此方法返回的分销订单。但校验了一次对应的供销订单是否存在。不要被方法名误导
     *
     * @param trades
     * @return
     */
    @Deprecated
    public List<Trade> getGxTradesByTid(List<Trade> trades) {
        return filterFxTradesIfGxTradeExist(trades, true);
    }

    /**
     * 不能用分销订单已审核判断供销就一定存在
     * 这里必须要查一次
     * <p>
     * 此方法返回的分销订单。但校验了一次对应的供销订单是否存在。不要被方法名误导
     *
     * @param trades
     * @return
     */
    public List<Trade> filterFxTradesIfGxTradeExist(List<Trade> trades, boolean filterByMergeSid) {
        if (CollectionUtils.isEmpty(trades)) {
            return new ArrayList<>();
        }
        List<Trade> fxTrades = Lists.newArrayListWithCapacity(trades.size());
        Map<Long, List<Trade>> destIdTradeMap = trades.stream().collect(Collectors.groupingBy(Trade::getDestId));
        for (Map.Entry<Long, List<Trade>> next : destIdTradeMap.entrySet()) {
            Long destCompanyId = next.getKey();
            List<Trade> fxTrades1 = next.getValue();
            if (destCompanyId <= 0) {
                continue;
            }
            Staff gxStaff = staffService.queryFullByCompanyId(destCompanyId);
            if (gxStaff == null) {
                continue;
            }
            List<String> tids = Lists.newArrayListWithCapacity(fxTrades1.size());
            fxTrades1.forEach(trade -> tids.add(filterByMergeSid && TradeUtils.isMerge(trade) ? trade.getMergeSid().toString() : trade.getSid().toString()));
            List<TbTrade> gxTrades = tradeSearchService.queryByKeys(gxStaff, "tid,sid,merge_sid,is_cancel", "tid", tids.toArray(new String[0]));
            if (CollectionUtils.isEmpty(gxTrades)) {
                continue;
            }
            Map<String, TbTrade> tid2TradeMap = gxTrades.stream().filter(t -> !TradeUtils.isCancel(t)).collect(Collectors.toMap(TbTrade::getTid, a -> a, (a, b) -> a));
            for (Trade trade : fxTrades1) {
                if (tid2TradeMap.get(filterByMergeSid && TradeUtils.isMerge(trade) ? trade.getMergeSid().toString() : trade.getSid().toString()) != null) {
                    fxTrades.add(trade);
                }
            }
        }
        return fxTrades;
    }

    public <R> R executeTransaction(Staff staff, Long[] sids, String action, Supplier<R> callback) {
        return executeTransaction(staff, sids, TransactionDefinition.PROPAGATION_REQUIRES_NEW, action, callback);
    }

    public <R> R executeTransaction(Staff staff, Long[] sids, int propagationLevel, String action, Supplier<R> callback) {
        if (propagationLevel < 0 || propagationLevel > 6) {
            throw new IllegalArgumentException("propagationLevel参数异常");
        }
        Integer oldDbNo = DbContextHolder.get();
        Integer newDbNo = staff.getDbKey();
        DbContextHolder.set(newDbNo);
        R result;
        TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition(propagationLevel));
        try {
            result = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), callback::get);
            transactionManager.commit(transactionStatus);
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            logger.error(new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).append(action).toString(), StackTracesUtils.filterStackTraces(e));
            throw new RuntimeException(action + "失败：" + e.getMessage());
        } finally {
            if (!Objects.equals(oldDbNo, newDbNo)) {
                DbContextHolder.set(oldDbNo);
            }
        }
        return result;
    }

    /**
     * 获取分销订单的更具供销订单的tidd
     *
     * @param trades
     * @return
     */
    public List<Trade> getFxTradesByTids(Staff fxstaff, List<Trade> trades) {
        List<String> gxTids = trades.stream().map(Trade::getTid).collect(Collectors.toList());
        List<Long> fxSids = Lists.newArrayListWithCapacity(gxTids.size());
        List<String> illegalTids = new ArrayList<>();
        gxTids.forEach(s -> {
            if (StringUtils.isNumeric(s)) {
                fxSids.add(Long.parseLong(s));
            } else {
                illegalTids.add(s);
            }
        });
        if (CollectionUtils.isNotEmpty(illegalTids)) {
            Logs.ifDebug(String.format("供销订单[tids:%s],平台单号不是数字，不存在对应的分销订单", illegalTids));
        }
        if (CollectionUtils.isEmpty(fxSids)) {
            return Collections.emptyList();
        }
        //获取分销订单
        List<Trade> fxTrades = tradeSearchService.queryBySidsContainMergeTrade(fxstaff, true, true, true, fxSids.toArray(new Long[0]));
        return fxTrades;
    }

    public void doFxBusinessAfterImport(TradeImportData tradeImportData, User user) {
        //处理供销库存缺货
        List<Trade> gxTrades = tradeImportData.insertTrades.stream().filter(trade -> TradeUtils.isGxTrade(trade) && Trade.STOCK_STATUS_INSUFFICIENT.equals(trade.getStockStatus())).collect(Collectors.toList());
        eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_GX_STOCK_INSUFFICIENT).setArgs(new Object[]{user.getStaff(), TradeUtils.toSids(gxTrades)}), null);
    }

    public List<Trade> findGxTradesByMix(List<Trade> gxMixTrades, List<Trade> gxTrades) {
        //是否有中间状态
        List<Trade> collect = gxMixTrades.stream().filter(TradeUtils::isFxOrMixTrade).collect(Collectors.toList());
        List<Trade> gTrades = gxMixTrades.stream().filter(TradeUtils::isGxTrade).collect(Collectors.toList());
        gxTrades.addAll(gTrades);
        //没有则直接返回
        if (CollectionUtils.isEmpty(collect)) {
            return gxTrades;
        }
        Map<Long, List<Trade>> fxTradedestMap = TradeUtils.groupByDestId(collect);
        Map<Long, Trade> fxTradeMap = TradeUtils.toMapBySid(collect);
        fxTradedestMap.forEach((destId, trades) -> {
            Staff gxStaff2 = staffService.queryFullByCompanyId(destId);
            if (null == gxStaff2 || null == gxStaff2.getDbInfo()) {
                Logs.ifDebug(String.format("供销商[companyId:%s] Staff信息查询为空", destId));
                return;
            }
            List<String> midTids = TradeUtils.toSidList(trades).stream().map(Object::toString).collect(Collectors.toList());
            List<TbTrade> gxTrades2 = tradeSearchService.queryByTids(gxStaff2, true, midTids.toArray(new String[0]));
            if (CollectionUtils.isEmpty(gxTrades2)) {
                return;
            }
            List<TbTrade> norMalTrades2 = gxTrades2.stream().filter(tbTrade -> !TradeUtils.isCancel(tbTrade)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(norMalTrades2)) {
                return;
            }
            fillGuid(norMalTrades2, fxTradeMap);
            findGxTradesByMix(TradeUtils.toTrades(gxTrades2), gxTrades);
        });
        return gxTrades;
    }

    public void filterGxTemplateModifyTrade(List<Trade> trades, TradeImportResult tir) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        for (Trade trade : trades) {
            if (TradeUtils.isGxTrade(trade) && trade.getTemplateId() != null && trade.getTemplateId() > 0) {
                tir.gxRecalculatePostFeeSidSet.add(trade.getSid());
            }
        }
    }

    public <T extends Trade> void fillGuid(List<T> norMalTrades2, Map<Long, Trade> fxTradeMap) {
        norMalTrades2.forEach(tbTrade -> {
            Trade trade = fxTradeMap.get(Long.parseLong(tbTrade.getTid()));
            if (trade == null) {
                return;
            }
            tbTrade.setGuid(trade.getGuid());
        });
    }

    public Map<String, Trade> getFxTradeMapByGx(List<Trade> trades) {
        List<Trade> gxTrades = trades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(gxTrades)) {
            Map<Long, List<Trade>> sourceIdMap = gxTrades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
            List<Trade> fxTradeList = new ArrayList<>();
            for (Map.Entry<Long, List<Trade>> entry : sourceIdMap.entrySet()) {
                Long sourceId = entry.getKey();
                List<Trade> tradeList = entry.getValue();
                //获取分销信息
                Staff fxStaff = staffService.queryFullByCompanyId(sourceId);
                if (null == fxStaff || null == fxStaff.getDbInfo()) {
                    Logs.ifDebug(String.format("分销商[companyId:%s] Staff信息查询为空", sourceId));
                    continue;
                }
                List<String> tids = TradeUtils.toTidList(tradeList);
                List<Long> fxSids = Lists.newArrayListWithCapacity(tids.size());
                List<String> illegalTids = new ArrayList<>();
                tids.forEach(s -> {
                    if (StringUtils.isNumeric(s)) {
                        fxSids.add(Long.parseLong(s));
                    } else {
                        illegalTids.add(s);
                    }
                });
                if (CollectionUtils.isNotEmpty(illegalTids)) {
                    Logs.ifDebug(String.format("供销订单[tids:%s],平台单号不是数字，不存在对应的分销订单", illegalTids));
                }
                List<Trade> fxTrades = tradeSearchService.queryBySidsContainMergeTrade(fxStaff, true, false, true, fxSids.toArray(new Long[0]));
                fxTradeList.addAll(fxTrades);
            }
            return fxTradeList.stream().collect(Collectors.toMap(trade -> String.valueOf(trade.getSid()), trade -> trade, (a, b) -> a));
        }
        return null;
    }

    /**
     * 只处理一层
     *
     * @param tradeList
     * @return
     */
    public void fillGxTradeByFx(List<Trade> tradeList) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        List<Trade> fxTradeList = tradeList.stream().filter(TradeUtils::isFxOrMixTrade).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fxTradeList)) {
            return;
        }
        List<Trade> gxTradeList = new ArrayList<>();

        Map<Long, List<Trade>> destIdMap = fxTradeList.stream().collect(Collectors.groupingBy(Trade::getDestId));
        for (Map.Entry<Long, List<Trade>> entry : destIdMap.entrySet()) {
            Long destId = entry.getKey();
            List<Trade> trades = entry.getValue();
            Staff gxStaff = staffService.queryFullByCompanyId(destId);
            if (gxStaff == null) {
                continue;
            }
            gxTradeList.addAll(TradeUtils.toTrades(tradeSearchService.queryByTids(gxStaff, false, getGxTids(trades).toArray(new String[0]))));
        }
        Map<String, Trade> tradeMap = gxTradeList.stream().filter(t1 -> !TradeUtils.isCancel(t1)).collect(Collectors.toMap(TradeBase::getTid, t -> t, (k1, k2) -> k2));
        tradeList.forEach(t -> t.setSourceTrade(tradeMap.get(TradeUtils.isMerge(t) ? String.valueOf(t.getMergeSid()) : String.valueOf(t.getSid()))));
    }

    private Set<String> getGxTids(List<Trade> trades) {
        Set<String> fxSids = Sets.newHashSet();
        trades.forEach(t -> {
            if (TradeUtils.isMerge(t)) {
                fxSids.add(String.valueOf(t.getMergeSid()));
            } else {
                fxSids.add(String.valueOf(t.getSid()));
            }
        });
        return fxSids;

    }

    /**
     * 根据供销直接更新分销数据
     *
     * @param trades
     * @param function<T,U,R> T：分销staff  U: 供销trade，分销trade R:需要更新的trade,返回null的话就不会去更新
     */
    public void updateFxTradeByGx(List<Trade> trades, BiFunction<Staff, Pair<Trade, Trade>, Trade> function) {
        List<Trade> gxTrades = trades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(gxTrades)) {
            return;
        }

        Map<Long, List<Trade>> sourceIdMap = gxTrades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
        // 分销的sid和trade map
        Map<String, Trade> fxSidMap = getFxTradeMapByGx(trades);
        for (Map.Entry<Long, List<Trade>> entry : sourceIdMap.entrySet()) {
            Long sourceId = entry.getKey();
            List<Trade> tradeList = entry.getValue();
            List<Trade> toUpdateList = new ArrayList<>(tradeList.size());
            //获取分销信息
            Staff fxStaff = staffService.queryFullByCompanyId(sourceId);
            tradeList.forEach(o -> {
                String fxSid = o.getTid();
                Pair<Trade, Trade> pair = Pair.of(o, fxSidMap.get(fxSid));
                Trade applyTrade = function.apply(fxStaff, pair);
                if (null != applyTrade) {
                    toUpdateList.add(applyTrade);
                }
            });
            if (CollectionUtils.isNotEmpty(toUpdateList)) {
                tradeUpdateService.updateTrades(fxStaff, toUpdateList);
            }
        }
    }

    public void saveOrderModifyLog(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Order> orders = new ArrayList<>();
        List<Trade> gxTrades = TradeUtils.filterGxTrades(trades);
        if (CollectionUtils.isEmpty(gxTrades)) {
            return;
        }
        for (Trade trade : gxTrades) {
            orders.addAll(TradeUtils.getOrders4Trade(trade));
        }
        orderModifyLogBusiness.addLog(staff, OrderModifyLogUtils.build(orders, OrderModifyLogTypeEnum.GX_ADDRESS_CHANGE));
    }


    /**
     * 分销商品未匹配强制推送供销商商品编码直接去平台编码
     *
     * @param staff
     * @param trades
     */
    public void fillSupplierItemInfo(Staff staff, List<Trade> trades) {

        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> trades1 = trades.stream().filter(t -> TradeUtils.isContainV(t, TradeConstants.V_IF_FX_FORCE_PUSH_UNALLOCATED)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(trades1)) {
            return;
        }
        logger.debug(LogHelper.buildLog(staff, String.format("[分销业务]分销商品未匹配强制推送供销,sid=%s", TradeUtils.toSidList(trades1))));

        TradeUtils.getOrders4Trade(trades1).forEach(o -> o.setSupplierOuterId(StringUtils.defaultString(o.getOuterSkuId(), o.getOuterIid())));
    }

    public void fillFxKmtSupplierItemInfo(FxKmtSupplierItem fxKmtSupplierItem) {
        fillFxKmtSupplierItemInfo(fxKmtSupplierItem, new FxOperateResult());
    }

    /**
     * 1.调用商品接口返回快麦通商品信息，设置supplierCompanyId，supplierOuterId
     * 2.ifHandleDmsAttr=true ,根据order.destId,belongType,convertType 设置trade的分销属性
     * 2.1 如果是分销单打上快麦通打发标记
     * <p>
     * <p>
     * 批量填充分销订单快麦通商品信息
     */
    public void fillFxKmtSupplierItemInfo(FxKmtSupplierItem fxKmtSupplierItem, FxOperateResult fxOperateResult) {

        if (fxKmtSupplierItem == null || fxKmtSupplierItem.getStaff() == null) {
            return;
        }
        Staff staff = fxKmtSupplierItem.getStaff();
        fillSupplierItemInfo(staff, fxKmtSupplierItem.getTrades());
        if (fxKmtSupplierItem.isIfOnlyFxForcePushUnallocated()) {
            return;
        }
        String dmsTradeMatchFxAttrType = TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_MATCH_FX_ATTR_TYPE).getConfigValue();
        boolean ifMatchFxAttrByKmt = TradeConstants.DMS_TRADE_MATCH_FX_ATTR_ALL.equals(dmsTradeMatchFxAttrType) || TradeConstants.DMS_TRADE_MATCH_FX_ATTR_KMT.equals(dmsTradeMatchFxAttrType);
        if (!ifMatchFxAttrByKmt) {
            return;
        }
        DmsDistributorConfigDto dmsDistributorConfigDto = fxKmtSupplierItem.getDmsDistributorConfigDto();
        if (dmsDistributorConfigDto != null) {
            boolean ifMatchSysItem = dmsDistributorConfigDto.getAutoItemMatch() != null && Boolean.TRUE.equals(dmsDistributorConfigDto.getAutoItemMatch());
            boolean ifUseSysCode = (null == dmsDistributorConfigDto.getUseSupplierCode() || dmsDistributorConfigDto.getUseSupplierCode() == 0);
            if (!(ifMatchSysItem && ifUseSysCode)) {
                return;
            }
        }
        boolean ifHandleDmsAttr = fxKmtSupplierItem.isIfHandleDmsAttr();
        List<Trade> trades = fxKmtSupplierItem.getTrades().stream().filter(t -> !TradeUtils.notNeedMatchFxAttr(t) && (ifHandleDmsAttr || t.getIfKmtDf() || t.getIfFxForcePushTrade()) && !TradeUtils.isContainV(t, TradeConstants.V_IF_FX_FORCE_PUSH_UNALLOCATED)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Order> orders = Optional.ofNullable(fxKmtSupplierItem.getOrders()).orElse(TradeUtils.getOrders4Trade(trades))
                .stream().filter(o -> o.getItemSysId() != null && o.getItemSysId() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        List<Order> orders4Match = Lists.newArrayList();
        Set<String> existsKeySet = Sets.newHashSet();
        StringBuilder logStr = new StringBuilder("fillFxKmtSupplierItemInfo request[");
        orders.forEach(o -> {
            String itemKey = OrderUtils.buildItemKey(o.getItemSysId(), o.getSkuSysId());
            if (!existsKeySet.contains(itemKey)) {
                orders4Match.add(o);
                existsKeySet.add(itemKey);
                logStr.append("{sysOuterId=").append(o.getSysOuterId()).append(",itemSysId=").append(o.getItemSysId()).append(",skuSysId=").append(o.getSkuSysId()).append("},");
            }
        });
        if (CollectionUtils.isEmpty(orders4Match)) {
            new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).append("fillFxKmtSupplierItemInfo 商品都没有匹配到系统商品，不需要匹配快麦通供销信息").printDebug(logger);
            return;
        }
        logStr.append("]");
        new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).append(logStr.toString()).printDebug(logger);
        Map<String, SupplierItemMatchResult> idStr2SupplierItemMatchResult;
        try {
            idStr2SupplierItemMatchResult = iItemServiceDubbo.matchKmtSupplierItem(ItemStaffRequest.buildStaffRequest(staff), orders4Match);
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append(String.format("iItemServiceDubbo.matchKmtSupplierItem 出错!")), e);
            return;
        }
        new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).format("fillFxKmtSupplierItemInfo response %s", JSON.toJSONString(idStr2SupplierItemMatchResult)).printDebug(logger);

        if (MapUtils.isEmpty(idStr2SupplierItemMatchResult)) {
            return;
        }

        TradeConfig tradeConfig = tradeConfigService.get(staff);
        FxLogBuilder builder = FxLogBuilder.fx(staff).append("快麦通商品匹配分销商失败:").startWatch();
        Map<Long,List<String>> notMatched = new HashMap<>();
        orders.forEach(o -> {
            SupplierItemMatchResult matchResult = idStr2SupplierItemMatchResult.get(OrderUtils.buildItemKey(o.getItemSysId(), o.getSkuSysId()));
            if (Objects.nonNull(matchResult) && StringUtils.isNotBlank(matchResult.getOuterId()) && Objects.nonNull(matchResult.getSupplierCompanyId())) {
                Double fxPrice = matchResult.getFxPrice();
                if (fxPrice == null) {
                    builder.group(matchResult.getSupplierCompanyId() + "_"+matchResult.getOuterId(),"未维护分销价");
                }
                o.setSupplierCompanyId(matchResult.getSupplierCompanyId());
                o.setSupplierOuterId(matchResult.getOuterId());
                o.setSupplierPrice(matchResult.getFxPrice());
                o.addV(OrderConstant.V_KMT_DF);
                if (ifHandleDmsAttr && !staff.getCompany().isKmt()) {
                    o.setDestId(matchResult.getSupplierCompanyId());
                    o.setConvertType(1);

                    if (isSalePriceSourceFx(staff, tradeConfig)) {
                        o.setSalePrice(o.getSupplierPrice() == null ?null:String.valueOf(o.getSupplierPrice()));
                        o.setSaleFee(new BigDecimalWrapper(o.getSupplierPrice()).multiply(o.getNum()).getString());
                    }
                    if (isCostSourceFx(staff, tradeConfig)) {
                        o.setCost(o.getSupplierPrice());
                    }

                    if (OrderUtils.isGxOrMixOrder(o)) {
                        o.setBelongType(3);
                    } else {
                        o.setSourceId(o.getCompanyId());
                        o.setBelongType(1);
                    }
                }
                if (Long.valueOf(0L).equals(matchResult.getSupplierCompanyId())) {
                    builder.group("供销商为0",o.getSysOuterId());
                    notMatched.computeIfAbsent(o.getSid(),x->new ArrayList<>()).add(o.getSysOuterId());
                }
            } else {
                builder.group("未返回",o.getSysOuterId());
                notMatched.computeIfAbsent(o.getSid(),x->new ArrayList<>()).add(o.getSysOuterId());
                if(ifHandleDmsAttr && fxKmtSupplierItem.getMatchType()==3){
                    o.setDestId(0L);
                }
            }
        });
        builder.multiPrintDebug(logger);
        if (!notMatched.isEmpty()) {
            for (Map.Entry<Long, List<String>> entry : notMatched.entrySet()) {
                List<String> value = entry.getValue();
                StringBuilder s = new StringBuilder("未匹配到供销商:");
                if (value.size() > 3) {
                    s.append(value.get(0)).append(",").append(value.get(1)).append(",").append(value.get(2)).append(",... ");
                }else {
                    s.append(Strings.join(",",value));
                }
                fxOperateResult.addTempMsgByOperate(entry.getKey(), s.toString());
            }
        }

        if (ifHandleDmsAttr && staff.getCompany().isKmt()) {
            orders.stream().collect(Collectors.groupingBy(Order::getSid)).forEach((sid, orders1) -> {
                if (CollectionUtils.isEmpty(orders1) || orders1.stream().anyMatch(o -> Objects.isNull(o.getSupplierCompanyId()))) {
                    return;
                }
                if (orders1.stream().filter(o -> Objects.nonNull(o.getSupplierCompanyId()))
                        .map(Order::getSupplierCompanyId)
                        .collect(Collectors.toSet()).size() > 1) {
                    orders1.forEach(o -> o.setDestId(Optional.ofNullable(o).map(Order::getSupplierCompanyId).orElse(0L)));
                    return;
                }
                orders1.forEach(o -> {
                    o.setDestId(o.getSupplierCompanyId());
                    o.setConvertType(1);

                    if (isSalePriceSourceFx(staff, tradeConfig)) {
                        o.setSalePrice(String.valueOf(o.getSupplierPrice()));
                        o.setSaleFee(new BigDecimalWrapper(o.getSupplierPrice()).multiply(o.getNum()).getString());
                    }
                    if (isCostSourceFx(staff, tradeConfig)) {
                        o.setCost(o.getSupplierPrice());
                    }

                    if (OrderUtils.isGxOrMixOrder(o)) {
                        o.setBelongType(3);
                    } else {
                        o.setSourceId(o.getCompanyId());
                        o.setBelongType(1);
                    }
                });
            });
        }

        if (ifHandleDmsAttr) {
            handleKmtDmsAttr(staff, trades);
        }

    }

    public void handleKmtDmsAttr(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        // 处理历史数据 convertType=3 AND belongType=3 和 v有128对齐。都有V 128后可以去掉
        trades.stream().filter(t -> TradeUtils.isQimenFxSource(t) && !TradeUtils.getIfQimenMix(t)).forEach(t -> {
            t.addV(TradeConstants.V_IF_FX_FROM_QIMEN);
        });
        TradeUtils.recalculateDistributorAttributeFromOrder2Trade(staff, trades, true);
        trades.stream().filter(TradeUtils::isFxOrMixTrade).forEach(t -> {
            if (TradeUtils.getOrders4Trade(t).stream().allMatch(o -> StringUtils.isNotBlank(o.getSupplierOuterId()) && Objects.nonNull(o.getSupplierCompanyId()))) {
                t.addV(TradeConstants.V_IF_KMT_DF);
            }
        });
    }

    /**
     * fxTrade 审核   txBegin 10s------>  txEnd
     * fxtrade 退款   txBegin 1s------>   txEnd
     * <p>
     * 退款业务流程跑到审核前面去了，此时供销订单还没有生成
     * <p>
     * 分销订单退款，供销订单未生成延迟同步
     * 1. 如果供销订单已经生成直接调用同步，否则写入缓存
     * 2. 缓存是供销订单同步流程after去读取，缓存有效期一天
     *
     * @param staff fxStaff
     * @param sids
     */
    public void delaySyncGxTrade(Staff staff, Long[] sids) {
        if (ArrayUtils.isEmpty(sids)) {
            return;
        }
        // 分销只需要查询主单
        List<Trade> fxTrades = tradeSearchService.queryBySids(staff,true, sids);
        if (CollectionUtils.isEmpty(fxTrades)) {
            return;
        }
        List<Trade> fxTradesByTid = getGxTradesByTid(fxTrades);
        if (CollectionUtils.isNotEmpty(fxTradesByTid)) {
            List<Long> existGxTradeFxSids = TradeUtils.toSidList(fxTradesByTid);
            if (logger.isDebugEnabled()) {
                new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).format("供销订单已存在，分销订单状态变更自动通知供销下载:%s", existGxTradeFxSids).printDebug(logger);
            }
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_DOWNLOAD).setArgs(new Object[]{staff, TradeUtils.toSids(fxTradesByTid)}), null);
            setDelaySyncCache(staff, fxTrades.stream().filter(t -> !existGxTradeFxSids.contains(t.getSid())).map(Trade::getSid).collect(Collectors.toList()));
        } else {
            setDelaySyncCache(staff, TradeUtils.toSidList(fxTrades));
        }
    }

    /**
     * 供销订单同步触发，消费分销订单退款，供销订单未生成延迟同步的缓存
     * <p>
     * 1.根据分销sid获取缓存
     * 2.按分销公司id分组，进行同步
     *
     * @param staff
     * @param fxSid2GxSidMap
     */
    public void consumeDelaySyncCache(Staff staff, Map<String, Long> fxSid2GxSidMap) {
        if (MapUtils.isEmpty(fxSid2GxSidMap)) {
            return;
        }

        // 分销sids
        List<String> sids = Lists.newArrayList(fxSid2GxSidMap.keySet());

        doConsumeTradeFxCancel(staff, fxSid2GxSidMap);
        Map<String, Long> delayTradeMap = getAndDeleteDelaySyncCache(staff, sids);
        Map<Long, List<Long>> companyId2Sids = Maps.newHashMap();

        if (MapUtils.isNotEmpty(delayTradeMap)) {
            sids.forEach(sid -> {
                Long companyId = delayTradeMap.get(DELAY_SYNC_CACHE_PREFIX + sid);
                if (companyId != null) {
                    if (org.apache.commons.lang.math.NumberUtils.isNumber(sid)) {
                        companyId2Sids.computeIfAbsent(companyId, s -> new ArrayList<>()).add(Long.valueOf(sid));
                    }
                }
            });
        }
        if (MapUtils.isNotEmpty(companyId2Sids)) {
            companyId2Sids.forEach((companyId, fxSids) -> {
                if (null == companyId || companyId == 0L) {
                    return;
                }
                Staff fxStaff = staffService.queryFullByCompanyId(companyId);
                if (null == fxStaff) {
                    return;
                }
                new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).format("从缓存读取，分销订单状态变更自动通知供销下载,companyId=%s,sids=%s", fxStaff.getCompanyId(), fxSids).printDebug(logger);
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_DOWNLOAD).setArgs(new Object[]{fxStaff, fxSids.toArray(new Long[0])}), null);
            });
        }
    }


    /**
     * 处理分销合单或者其他业务导致供销需要作废，但当时供销订单还没有生成
     *
     * @param staff
     * @param fxSid2GxSidMap
     */
    private void doConsumeTradeFxCancel(Staff staff, Map<String, Long> fxSid2GxSidMap) {

        Integer ifExists = sharedCacheBusiness.get(String.format("trade_fx_cancel_%s", staff.getCompanyId()));
        if (Objects.isNull(ifExists)) {
            return;
        }

        Map<Integer,Set<Long>> type2Sids = Maps.newHashMap();

        fxSid2GxSidMap.forEach((sid, gxSid) -> {
            String key = String.format("trade_fx_cancel_%s_%s", staff.getCompanyId(), sid);
            Integer type = sharedCacheBusiness.get(key);
            if(!Objects.isNull(type)) {
                type2Sids.computeIfAbsent(type, s -> new HashSet<>()).add(gxSid);
                sharedCacheBusiness.delete(key);
            }
        });

        if(MapUtils.isEmpty(type2Sids)){
            return;
        }

        type2Sids.forEach((type,gxSidList)->{
            if (CollectionUtils.isNotEmpty(gxSidList)) {
                new FxLogBuilder(staff, FxLogBuilder.ROLE_GX).format("分销订单操作导致供销订单作废，但当时供销单未生成，补偿业务供销sids=%s", gxSidList).printWarn(logger);
                CancelData cancelData = CancelData.builder()
                        .staff(staff).sidArr(gxSidList.toArray(new Long[0]))
                        .isCancelAttr(0).opName("系统").opMsg(CONSUME_TRADE_FXCANCEL_TYPE_2_MSG_MAP.getOrDefault(type,"分销触发自动作废供销订单"))
                        .build();
                cancelGxTrade(cancelData);
            }
        });
    }

    /**
     * 供销订单重算流水
     *
     * @param staff
     * @param trades     支持供销订单、奇门供销订单（包括强推、待审核等供销模式）
     * @param calPostFee 是否重算运费
     * @param operate    触发动作
     * @param
     */
    public void reCalculateGxTradeCashFlow(Staff staff, List<Trade> trades, boolean calPostFee, String operate) {
        List<Trade> reCalTrades = filterReCalTrade(staff, trades, operate);
        if (CollectionUtils.isNotEmpty(reCalTrades)) {
            try {
                long start = System.currentTimeMillis();
                //被删除的订单，一般是取消合单时，非主单被删除
                List<Trade> invalidTrades = new ArrayList<>();
                List<Long> aliveSids = new ArrayList<>();
                for (Trade reCalTrade : reCalTrades) {
                    if (Integer.valueOf(0).equals(reCalTrade.getEnableStatus())) {
                        invalidTrades.add(reCalTrade);
                    } else {
                        aliveSids.add(reCalTrade.getSid());
                    }
                }
                //作废逻辑删除订单的流水
                if (CollectionUtils.isNotEmpty(invalidTrades)) {
                    gxReCalculateBusiness.reCalculateByDelete(staff, invalidTrades);
                }
                if (CollectionUtils.isNotEmpty(aliveSids)) {
                    //重新查询数据
                    List<Trade> tradeList = tradeSearchService.queryBySidsContainMergeTrade(staff, true, aliveSids.toArray(new Long[0]));
                    ;
                    TradeRefreshParams refreshParams = new TradeRefreshParams();
                    refreshParams.setReCalGxPostFee(calPostFee ? "1" : null);
                    refreshParams.setReFreshFxPrice(false);
                    refreshParams.setReFreshTradeCommission(false);
                    refreshParams.setOperateLockCashFlowSync(true);
                    refreshParams.setOperateLockCashFlowForce(true);
                    refreshParams.setAddFlowTrace(false);
                    refreshParams.setPrintFlowLog(true);
                    refreshParams.setFlowRefreshMode("2");
                    gxReCalculateBusiness.reCalculate(staff, tradeList, refreshParams);
                    //拆单重算运费
                    if (CollectionUtils.isNotEmpty(tradeList)
                            && !qiMenFxCashFlowBusiness.openCashFlow(qiMenFxCashFlowBusiness.queryParty3Config(staff))
                    ) {
                        qiMenFxCashFlowBusiness.calculateTradePostFee(staff, TradeUtils.toSidList(tradeList), true);
                    }
                }
                long end = System.currentTimeMillis();
                Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("供销订单%s，重算流水: took=%s; sid=%s", operate, (end - start), TradeUtils.toSidList(reCalTrades))));
            } catch (Exception e) {
                Logs.error(LogHelper.buildLogHead(staff).append(String.format("供销订单%s，重算流水失败: sid=%s", operate, TradeUtils.toSidList(reCalTrades))), e);
            }
        }
        //拆单同步分销
        syncFxData(staff,trades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList()));
    }

    /**
     * 过滤出需要进行重算业务的订单
     *
     * @param staff
     * @param trades
     * @param operate
     * @return
     */
    private List<Trade> filterReCalTrade(Staff staff, List<Trade> trades, String operate){
        if ("拆分订单".equals(operate)){
            return trades.stream().filter(t -> TradeUtils.isQimenFxSource(t) || TradeUtils.isGxOrMixTrade(t)).collect(Collectors.toList());
        }
        //当前先放开奇门供销单，后续开放的订单类型，可以在这里添加
        return trades.stream().filter(TradeUtils::isQimenFxSource).collect(Collectors.toList());
    }

    /**
     * 缓存 key= DELAY_SYNC_CACHE_PREFIX+ fxsid
     * value= fxcompanyid
     *
     * @param staff
     * @param sids
     */
    private void setDelaySyncCache(Staff staff, List<Long> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).format("写入缓存，分销订单退款，供销单未生成，分销sids=%s", sids).printWarn(logger);
        sids.forEach(sid -> {
            try {
                sharedCache.set(DELAY_SYNC_CACHE_PREFIX + sid, staff.getCompanyId(), 24 * 3600);
            } catch (Throwable e) {
                Logs.error(LogHelper.buildLog(staff, "缓存操作失败"), e);
            }
        });
    }

    private Map<String, Long> getAndDeleteDelaySyncCache(Staff staff, List<String> sids) {
        if (CollectionUtils.isEmpty(sids)) {
            return Maps.newHashMap();
        }
        try {
            Set<String> keys = new HashSet<>();
            sids.forEach(sid -> keys.add(DELAY_SYNC_CACHE_PREFIX + sid));
            Map<String, Long> map = Maps.newHashMapWithExpectedSize(8);
            keys.forEach(key -> {
                try {
                    Long value = sharedCache.get(key);
                    if (value != null) {
                        map.put(key, value);
                        sharedCache.delete(key);
                    }
                } catch (CacheException e) {
                    Logs.error(LogHelper.buildLog(staff, "缓存操作失败"), e);
                }
            });
            return map;
        } catch (Throwable e) {
            Logs.error(LogHelper.buildLog(staff, "缓存操作失败"), e);
        }
        return Maps.newHashMap();
    }

    /**
     * @param staff       分销商
     * @param fxsid       分销订单sid
     * @param addLog      是否新增日志
     * @param oid2Order   供销订单order信息: key:order.oid; value:order
     * @param sid2Trade   供销订单trade信息: key:tid; value:order
     * @param syncPostFee 是否同步运费
     * @return
     */
    public void SyncFxPrice(Staff staff, Long[] fxsid, Boolean addLog, Map<Long, Order> oid2Order, Map<String, Trade> sid2Trade, Boolean syncPostFee) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        boolean isSalePriceSourceFx = isSalePriceSourceFx(staff, tradeConfig);
        boolean isCostSourceFx = isCostSourceFx(staff, tradeConfig);
        lockService.locks(tradeLockBusiness.getERPLocks(staff, fxsid), () -> {
            //合单也需要处理
            List<Trade> fxTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, fxsid);
            if (CollectionUtils.isEmpty(fxTrades)) {
                return null;
            }
            //todo 后续可以添加 oid2Order为空的处理
            //合单处理
            Map<Long, List<Trade>> mergeTrades = new HashMap<>();
            List<Trade> updateTrades = new ArrayList<>();
            List<Order> updateOrders = new ArrayList<>();
            List<TradeExt> insertExts = Lists.newArrayListWithCapacity(fxTrades.size());
            List<TradeExt> updateExts = Lists.newArrayListWithCapacity(fxTrades.size());
            //更新中合单的主单
            Set<Long> calPriceMain = new HashSet<>();
            for (Trade fxTrade : fxTrades) {
                boolean isInsert = false;
                if (TradeUtils.isMerge(fxTrade)) {
                    mergeTrades.computeIfAbsent(fxTrade.getMergeSid(), t -> new ArrayList<>()).add(fxTrade);
                }
                StringBuilder priceLog = new StringBuilder("订单修改分销价:");
                List<Order> orders = TradeUtils.getOrders4Trade(fxTrade);
                boolean calculateSalePrice = false;
                boolean calculateCost = false;
                boolean calculatePostFee = false;
                for (Order order : orders) {
                    boolean updatePrice = false;
                    Order gxOrder = oid2Order.get(order.getId());
                    if (null == gxOrder) {
                        continue;
                    }
                    //如果是套件，并且是套件里面的单品(不是套件本身)，不更新 --组合、加工单品
                    if ((order.isSuit(true) || order.isProcess(true) || order.isGroup(true))) {
                        continue;
                    }
                    String newPrice = gxOrder.getPrice();
                    if (isCostSourceFx) {
                        String oldPrice = order.getCost().toString();
                        if (!MathUtils.equals(newPrice, oldPrice)) {
                            calculateCost = true;
                            updatePrice = true;
                            order.setCost(gxOrder.getPriceDouble());
                            priceLog.append("商品(").append(order.getSysOuterId()).append(")分销价由").append(oldPrice).append("改为").append(newPrice).append(";");
                        }
                    }
                    if (isSalePriceSourceFx) {
                        String oldPrice = order.getSalePrice();
                        if (!MathUtils.equals(newPrice, oldPrice)) {
                            calculateSalePrice = true;
                            //金额变更时，才进行处理
                            order.setSalePrice(newPrice);
                            order.setSaleFee(MathUtils.toString(MathUtils.multiply(newPrice, order.getNum())));
                            if (!updatePrice) {
                                //cost变更时，已经记录日志了，这边跳过
                                priceLog.append("商品(").append(order.getSysOuterId()).append(")分销价由").append(oldPrice).append("改为").append(newPrice).append(";");
                            }
                            updatePrice = true;
                        }
                    }
                    if (updatePrice) {
                        updateOrders.add(order);
                    }
                }
                if (Boolean.TRUE.equals(syncPostFee)) {
                    Trade gxTrade = sid2Trade.get(fxTrade.getSid().toString());
                    if (null != gxTrade && !MathUtils.equals(fxTrade.getActualPostFee(), gxTrade.getPostFee())) {
                        calculatePostFee = true;
                        fxTrade.setOldAcPayment(fxTrade.getActualPostFee());
                        fxTrade.setActualPostFee(gxTrade.getPostFee());
                    }
                }
                if (calculateSalePrice || calculateCost || calculatePostFee) {
                    updateTrades.add(fxTrade);
                    //分销价刷新日志统计
                    if ((calculateSalePrice || calculateCost) && Boolean.TRUE.equals(addLog)) {
                        fxTrade.getOperations().put(OpEnum.TRADE_REFRESH_FX_PRICE, priceLog.deleteCharAt(priceLog.length() - 1).toString());
                    }
                    if (fxTrade.getTradeExt() == null) {
                        TradeExt tradeExt = new TradeExt();
                        tradeExt.setCompanyId(fxTrade.getCompanyId());
                        tradeExt.setUserId(fxTrade.getUserId());
                        tradeExt.setSid(fxTrade.getSid());
                        tradeExt.setTid(fxTrade.getTid());
                        fxTrade.setTradeExt(tradeExt);
                        isInsert = true;
                    }
                    Trade gxTrade = sid2Trade.get(fxTrade.getSid().toString());
                    String detailCashFlowLog = getDetailCashFlowLog(staff,gxTrade, fxTrade);
                    if (StringUtils.isNotEmpty(detailCashFlowLog)) {
                        if (isInsert) {
                            insertExts.add(fxTrade.getTradeExt());
                        } else {
                            updateExts.add(fxTrade.getTradeExt());
                        }
                        fxTrade.getOperations().put(OpEnum.TRADE_FX_SYNC_GX, "流水明细:"+detailCashFlowLog);
                    }
                }
                if (calculateCost || calculateSalePrice) {
                    //计算订单成本价; 毛利润不用计算，在updateTrades流程之后，进行了更新操作
                    fxTrade.setCost(TradeUtils.calculateCost(fxTrade));
                    //计算订单客户模块分销价
                    fxTrade.setSaleFee(TradeUtils.calculateTradeSaleFee(fxTrade));
                    //合单统计
                    if (TradeUtils.isMerge(fxTrade)) {
                        calPriceMain.add(fxTrade.getMergeSid());
                    }
                }
            }
            //合单主单的Trade.cost、salePrice计算
            for (Long mergeSid : calPriceMain) {
                List<Trade> merges = mergeTrades.get(mergeSid);
                if (CollectionUtils.isEmpty(merges)) {
                    continue;
                }
                Trade main = null;
                BigDecimalWrapper saleFee = new BigDecimalWrapper();
                BigDecimalWrapper totalCost = new BigDecimalWrapper();
                for (Trade trade : merges) {
                    if (trade.getSid().equals(mergeSid)) {
                        main = trade;
                        totalCost.add(trade.getCost());
                        saleFee.add(trade.getSaleFee());
                    }
                }
                if (null != main) {
                    main.setCost(totalCost.getDouble());
                    main.setSaleFee(saleFee.getString());
                    if (!updateTrades.contains(main)) {
                        updateTrades.add(main);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(updateTrades) || CollectionUtils.isNotEmpty(updateOrders)) {
                tradeUpdateService.updateTrades(staff, buildFxPriceUpdTrades(updateTrades), buildFxPriceUpdOrders(updateOrders));
            }
            if (insertExts.size() > 0) {
                tradeExtDao.batchInsert(staff, insertExts);
            }
            if (updateExts.size() > 0) {
                tradeExtDao.batchUpdate(staff, updateExts);
            }
            tradeTraceBusiness.asyncTrace(staff, updateTrades.stream().filter(t -> MapUtils.isNotEmpty(t.getOperations())).collect(Collectors.toList()), OpEnum.TRADE_REFRESH_FX_PRICE);
            return null;
        });
    }

    private List<Trade> buildFxPriceUpdTrades(List<Trade> trades) {
        List<Trade> updates = new ArrayList<>();
        for (Trade trade : trades) {
            Trade update = new TbTrade();
            update.setSid(trade.getSid());
            update.setCost(trade.getCost());
            update.setSaleFee(trade.getSaleFee());
            update.setActualPostFee(trade.getActualPostFee());
            updates.add(update);
        }
        return updates;
    }

    private List<Order> buildFxPriceUpdOrders(List<Order> orders) {
        List<Order> updates = new ArrayList<>();
        for (Order order : orders) {
            Order update = new TbOrder();
            update.setId(order.getId());
            update.setCost(order.getCost());
            update.setSalePrice(order.getSalePrice());
            update.setSaleFee(order.getSaleFee());
            updates.add(update);
        }
        return updates;
    }

    /**
     * 是否订单SalePrice字段的取值供分销模块分销价
     *
     * @param fxStaff
     * @return
     */
    public boolean isSalePriceSourceFx(Staff fxStaff, TradeConfig tradeConfig) {
        if (tradeConfig == null) {
            tradeConfig = tradeConfigService.get(fxStaff);
        }
        Integer config = tradeConfig.getInteger(NewTradeExtendConfigEnum.FX_SALE_PRICE_FIELD_SOURCE);
        boolean equals = Objects.equals(1, config);
        if (equals) {
            logger.debug(new FxLogBuilder(fxStaff, FxLogBuilder.ROLE_FX).append("订单SalePrice字段的取值供分销模块分销价").toString());
        }
        return equals;
    }

    /**
     * 是否订单Cost字段的取值供分销模块分销价
     *
     * @param fxStaff
     * @return
     */
    public boolean isCostSourceFx(Staff fxStaff, TradeConfig tradeConfig) {
        if (tradeConfig == null) {
            tradeConfig = tradeConfigService.get(fxStaff);
        }
        Integer config = tradeConfig.getInteger(NewTradeExtendConfigEnum.FX_COST_FIELD_SOURCE);
        boolean b = !Objects.equals(1, config);
        if (!b) {
            logger.debug(new FxLogBuilder(fxStaff, FxLogBuilder.ROLE_FX).append("分销订单Cost字段的取值商品的成本价").toString());
        }
        return b;
    }


    /**
     * 获取分销订单上总的分销费用
     *
     * @param fxStaff
     * @param tradeConfig
     * @param fxTrades
     * @param containsAfterSendGoods 是否包含已发货的商品
     * @param containsPostFee        是否包含运费
     * @return
     */
    public BigDecimal getBatchFxTotalSaleFee(Staff fxStaff, TradeConfig tradeConfig, List<Trade> fxTrades, boolean containsAfterSendGoods, boolean containsPostFee) {
        if (tradeConfig == null) {
            tradeConfig = tradeConfigService.get(fxStaff);
        }
        BigDecimalWrapper sum = new BigDecimalWrapper();
        for (Trade trade : fxTrades) {
            BigDecimal totalSaleFee = getFxTotalSaleFee(fxStaff, tradeConfig, trade, containsAfterSendGoods, containsPostFee);
            sum.add(totalSaleFee);
        }
        return sum.get();
    }


    /**
     * 获取分销订单上总的分销费用
     *
     * @param containsAfterSendGoods 是否包含已发货的商品
     * @param containsPostFee        是否包含运费
     */
    public BigDecimal getFxTotalSaleFee(Staff fxStaff, TradeConfig tradeConfig, Trade fxTrade, boolean containsAfterSendGoods, boolean containsPostFee) {
        return getFxTotalSaleFee(fxTrade, TradeUtils.getOrders4Trade(fxTrade), isCostSourceFx(fxStaff, tradeConfig), containsAfterSendGoods, containsPostFee);
    }

    public BigDecimal getFxTotalSaleFee(Trade fxTrade, List<Order> orders, boolean costSourceFx, boolean containsAfterSendGoods, boolean containsPostFee){
        BigDecimalWrapper sum = new BigDecimalWrapper();
        for (Order order : orders) {
            //如果是套件，并且是套件里面的单品(不是套件本身)，不计算
            if (order.isSuit(true) || order.isGroup(true) || order.isProcess(true)) {
                continue;
            }
            if (!containsAfterSendGoods && TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                continue;
            }
            if (costSourceFx) {
                sum.addMulti(order.getCost(), order.getNum());
            } else {
                sum.addMulti(order.getSalePrice(), order.getNum());
            }
        }
        if (containsPostFee) {
            sum.add(fxTrade.getActualPostFee());
        }
        return sum.get();
    }


    /**
     * 供销清空运单号,发送事件通知分销
     *
     * @param staff
     * @param cancelTrades
     * @param updateTrades
     */
    public void gxClearOutSid(Staff staff, List<Trade> cancelTrades, List<Trade> updateTrades) {
        if (CollectionUtils.isEmpty(cancelTrades) && CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        List<Trade> gxTrades = cancelTrades.stream().filter(TradeUtils::isGxTrade).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(gxTrades)) {
            Map<Long, Trade> gxTradeMap = TradeUtils.toMapBySid(gxTrades);
            List<Trade> gxClearOutSidTrades = updateTrades.stream().filter(trade -> {
                Trade gxTrade = gxTradeMap.get(trade.getSid());
                if (gxTrade == null) {
                    return false;
                }
                return StringUtils.isEmpty(gxTrade.getOutSid()) || (StringUtils.isNotEmpty(gxTrade.getOutSid()) && StringUtils.isEmpty(trade.getOutSid()));
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(gxClearOutSidTrades)) {
                eventCenter.fireEvent(this, new EventInfo("trade.gx.outSid.clear").setArgs(new Object[]{staff, TradeUtils.toSids(gxClearOutSidTrades)}), null);
            }
        }
    }

    /**
     * 根据供销订单清空分销outSid
     *
     * @param staff
     * @param gxSids
     */
    @SuppressWarnings("unchecked")
    public void clearFxOutSid(Staff staff, Long[] gxSids) {
        if (gxSids != null) {
            Set gxSidSet = new HashSet(Arrays.asList(gxSids));
            List<Trade> trades = tradeSearchService.queryBySids(staff, false, gxSids);
            if (CollectionUtils.isNotEmpty(trades)) {
                List<Trade> gxTrades = trades.stream().filter(TradeUtils::isGxTrade).collect(Collectors.toList());
                Map<Long, List<Trade>> sourceIdMap = gxTrades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
                for (Map.Entry<Long, List<Trade>> entry : sourceIdMap.entrySet()) {
                    Long sourceId = entry.getKey();
                    Staff fxStaff = staffService.queryFullByCompanyId(sourceId);
                    if (null == fxStaff || null == fxStaff.getDbInfo()) {
                        Logs.ifDebug(String.format("分销商[companyId:%s] Staff信息查询为空", sourceId));
                        continue;
                    }
                    List<Trade> cancelOutSidGxTrades = Lists.newArrayListWithCapacity(entry.getValue().size());
                    for (Trade gxTrade : entry.getValue()) {
                        if (gxSidSet.contains(gxTrade.getSid())
                                && TradeUtils.isCancel(gxTrade)
                                && StringUtils.isEmpty(gxTrade.getOutSid())) {
                            cancelOutSidGxTrades.add(gxTrade);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(cancelOutSidGxTrades)) {
                        logger.debug(new FxLogBuilder(staff, FxLogBuilder.ROLE_GX).append("供销作废清空运单号同步分销,cancelOutSidGxTrades:" + cancelOutSidGxTrades));
                        Long[] fxSidsArr = Arrays.stream(TradeUtils.toTids(cancelOutSidGxTrades)).filter(StringUtils::isNumeric).map(NumberUtils::str2Long).toArray(Long[]::new);
                        lockService.locks(tradeLockBusiness.getERPLocks(fxStaff, fxSidsArr), () -> {
                            List<Trade> fxTrades = tradeSearchService.queryBySids(fxStaff, false, fxSidsArr);
                            Map<String, Trade> tradeMap = TradeUtils.toMapByTid(cancelOutSidGxTrades);
                            if (CollectionUtils.isNotEmpty(fxTrades)) {
                                List<Trade> updateTrades = fxTrades.stream().filter(fxTrade ->
                                                TradeUtils.isFxTrade(fxTrade)
                                                        && !TradeUtils.isAfterSendGoods(fxTrade)
                                                        && tradeMap.containsKey(fxTrade.getSid().toString())
                                                        && StringUtils.isNotEmpty(fxTrade.getOutSid()))
                                        .map(fxTrade -> {
                                            TbTrade update = new TbTrade();
                                            update.setSid(fxTrade.getSid());
                                            update.setTemplateId(-1L);
                                            update.setLogisticsCompanyId(0L);
                                            update.setTemplateType(0);
                                            update.setOutSid("");
                                            return update;
                                        }).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(updateTrades)) {
                                    tradePtService.saveByTrades(fxStaff, updateTrades);
                                    tradeUpdateService.updateTrades(fxStaff, updateTrades);
                                    logger.debug(LogHelper.buildLog(fxStaff, String.format("供销作废,触发分销清空运单号:%s", TradeUtils.toSids(updateTrades))));
                                }
                            }
                            return null;
                        });
                    }
                }
            }
        }
    }


    /**
     * 获取供销公司id
     *
     * @param trade
     * @return
     */
    public static Long getDestId(Trade trade) {
        if (TradeUtils.isGxAndFxTrade(trade)
                || TradeUtils.isQimenMixSource(trade) ||
                (TradeUtils.isQimenFxSource(trade)
                        && TradeUtils.isReissueOrChangeitem(trade)
                        && trade.getDestId() != null
                        && trade.getDestId() <= 0)
                || (TradeUtils.isQimenFxSource(trade)
                && trade.getDestId() != null
                && trade.getDestId() <= 0)) {
            return trade.getCompanyId();
        }
        return trade.getDestId();
    }

    /**
     * 自动匹配业务员
     *
     * @param staff
     * @param downloadTradeListMap
     */
    public void matchSalesman(Staff staff, Map<Long, List<Trade>> downloadTradeListMap) {
        if (MapUtils.isEmpty(downloadTradeListMap)) {
            return;
        }
        List<Trade> gxTrades = new ArrayList<>();
        downloadTradeListMap.values().forEach(gxTrades::addAll);
        List<Trade> toMatchTrades = gxTrades.stream().filter(TradeAggregation::getIfAutoMatchSalesman).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(toMatchTrades)) {
            return;
        }
        Map<Long, Staff> gxStaffMap = Maps.newHashMapWithExpectedSize(downloadTradeListMap.size());
        downloadTradeListMap.keySet().forEach(destId -> gxStaffMap.put(destId, staffService.queryFullByCompanyId(destId)));
        Map<Long, Boolean> gxOpenSalesmanConfigMap = new HashMap<>(gxStaffMap.size());
        Map<Long, List<Long>> gxDestIdMap = new HashMap<>(gxStaffMap.size());
        List<Trade> filterTrades = new ArrayList<>(toMatchTrades.size());
        for (Trade trade : toMatchTrades) {
            Boolean openSalesmanConfig = gxOpenSalesmanConfigMap.computeIfAbsent(trade.getCompanyId(), (destId) -> {
                Staff gxStaff = gxStaffMap.get(destId);
                if (null == gxStaff) {
                    return false;
                }
                return TradeConfigGetUtil.get(gxStaff, TradeConfigEnum.OPEN_TRADE_SALESMAN).isOpen();
            });
            if (!openSalesmanConfig) {
                continue;
            }
            if ((TradeUtils.isGxTrade(trade) || TradeUtils.isGxAndFxTrade(trade) || TradeUtils.isQimenFxSource(trade))
                    && trade.getSourceId() != null && trade.getSourceId() > 0) {
                gxDestIdMap.computeIfAbsent(getDestId(trade), (key) -> new ArrayList<>()).add(trade.getSourceId());
                filterTrades.add(trade);
            }
        }
        Map<String, DmsDistributorInfoDto> dmsDistributorInfoDtoMap = new HashMap<>();
        Function<Pair<Long, Long>, String> keyFunction = (key) -> key.getLeft() + "_" + key.getRight();
        if (MapUtils.isNotEmpty(gxDestIdMap)) {
            for (Long destId : gxDestIdMap.keySet()) {
                List<Long> sourceIds = gxDestIdMap.get(destId);
                if (CollectionUtils.isNotEmpty(sourceIds)) {
                    DmsDistributorInfoRequest request = new DmsDistributorInfoRequest();
                    request.setSupplierCompanyId(destId);
                    request.setDistributorCompanyIdList(sourceIds);
                    List<DmsDistributorInfoDto> dmsDistributorInfoDtos = dmsTradeService.queryDmsDistributorBaseList(request);
                    if (CollectionUtils.isNotEmpty(dmsDistributorInfoDtos)) {
                        dmsDistributorInfoDtoMap.putAll(Maps.uniqueIndex(dmsDistributorInfoDtos, (dto) -> keyFunction.apply(Pair.of(destId, dto.getDistributorCompanyId()))));
                    }
                }
            }
        }
        if (MapUtils.isEmpty(dmsDistributorInfoDtoMap)) {
            return;
        }
        for (Trade trade : filterTrades) {
            DmsDistributorInfoDto dmsDistributorInfoDto = dmsDistributorInfoDtoMap.get(keyFunction.apply(Pair.of(getDestId(trade), trade.getSourceId())));
            if (dmsDistributorInfoDto != null) {
                trade.setSalesmanId(dmsDistributorInfoDto.getSaleStaffId());
                trade.setSalesmanName(dmsDistributorInfoDto.getSaleStaffName());
            }
        }
    }

    /**
     * 拼多多代发订单同步要传采购金额(对应系统的分销金额)给平台(单位:分)
     *
     * @param staff
     * @param tradeConfig
     * @param trade
     * @return
     */
    public Long getPddOutGoodsPrice(Staff staff, TradeConfig tradeConfig, Trade trade) {
        try {
            if (trade == null) {
                return null;
            }
            if (!TradeUtils.isFxTrade(trade)) {
                return null;
            }
            BigDecimal saleFee = getFxTotalSaleFee(staff, tradeConfig, trade, true, false);
            BigDecimal payAmount = MathUtils.toBigDecimal(trade.getPayAmount());
            if (MathUtils.greatThan(saleFee, payAmount)) {
                return saleFee.multiply(new BigDecimal(100)).longValue();
            }
            //分销金额上浮10%
            BigDecimal adjusted = saleFee.multiply(new BigDecimal(1.1));
            if (MathUtils.greatThan(adjusted, payAmount)) {
                adjusted = saleFee;
            }
            return adjusted.multiply(new BigDecimal(100)).longValue();
        } catch (Throwable e) {
            FxLogBuilder.fx(staff).appendError("拼多多代发订单采购金额计算失败", e).printWarn(logger, e);
        }
        return null;
    }

    /**
     * 商品变更后，重新计算供销订单的金额与流水
     * @param staff
     * @param gxSids
     * @param async 是否异步执行
     */
    public void itemChangeReCalculate(Staff staff, List<Long> gxSids, boolean async){
        if (CollectionUtils.isEmpty(gxSids)){
            return;
        }
        if (async){
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_GX_ITEM_CHANGE_RECALCULATE).setArgs(new Object[]{staff.getId(), null, gxSids}), null);
        }else {
            fxItemBindBusiness.itemChangeReCalculate(staff, gxSids);
        }
    }


    public void recordSupplierSrcInfo(User user,List<Trade> gxTrades){
        if (CollectionUtils.isEmpty(gxTrades)) {
            return;
        }
        Map<Long,Staff> fxStaffMap = new HashMap<>();
        try{
            List<FxSupplierSrcInfo> srcInfoList = new ArrayList<>();
            for (Trade gxTrade : gxTrades) {
                Trade fxTrade = gxTrade.getSourceTrade();
                if (fxTrade == null) {
                    continue;
                }
                Staff fxStaff = fxStaffMap.get(fxTrade.getCompanyId());
                if (fxStaff == null) {
                    fxStaff = staffService.queryDefaultStaffByCompanyId(fxTrade.getCompanyId());
                    fxStaffMap.put(fxTrade.getCompanyId(),fxStaff);
                }
                List<FxSupplierSrcInfo> list = fillSupplierSrcInfo(fxStaff, fxTrade, gxTrade);
                if (CollectionUtils.isNotEmpty(list)) {
                    srcInfoList.addAll(list);
                }
            }
            if (CollectionUtils.isNotEmpty(srcInfoList)) {
                fxSupplierSrcService.save(user.getStaff(),srcInfoList);
            }
        }catch (Throwable e){
            new FxLogBuilder(user.getCompanyId(),FxLogBuilder.ROLE_GX).appendError("保存供销单对应原始分销信息失败",e).printError(logger,e);
        }

    }

    private List<FxSupplierSrcInfo> fillSupplierSrcInfo(Staff fxStaff, Trade fxTrade, Trade gxTrade){
        try {
            List<FxSupplierSrcInfo> result = new ArrayList<>();
            if (fxTrade == null) {
                return result;
            }
            //如果是分销订单
            if (TradeUtils.isFxTrade(fxTrade)) {

                if (!TradeUtils.isMerge(fxTrade)) {
                    FxSupplierSrcInfo srcInfo = createFxSupplierSrcInfo(fxTrade, gxTrade);
                    srcInfo.setFxTid(fxTrade.getTid());
                    srcInfo.setFxSid(fxTrade.getSid());
                    srcInfo.setFxSource(fxTrade.getSource());
                    srcInfo.setFxSubSource(fxTrade.getSubSource());
                    result.add(srcInfo);

                    //补发单
                    if (TradeUtils.isReissueOrChangeitem(fxTrade) && fxTrade.getTid().contains("-")) {
                        srcInfo = createFxSupplierSrcInfo(fxTrade, gxTrade);
                        String tid = fxTrade.getTid();
                        srcInfo.setFxTid(tid.substring(0, tid.lastIndexOf("-")));
                        srcInfo.setFxSid(fxTrade.getSid());
                        srcInfo.setFxSource(fxTrade.getSource());
                        srcInfo.setFxSubSource(fxTrade.getSubSource());
                        result.add(srcInfo);
                    }

                }else {
                    List<MessageMemo> messageMemos = fxTrade.getMessageMemos();
                    if (CollectionUtils.isNotEmpty(messageMemos)) {
                        for (MessageMemo messageMemo : messageMemos) {
                            FxSupplierSrcInfo srcInfo = createFxSupplierSrcInfo(fxTrade, gxTrade);
                            srcInfo.setFxTid(messageMemo.getTid());
                            srcInfo.setFxSid(messageMemo.getSid());
                            srcInfo.setFxSource(messageMemo.getSource());
                            srcInfo.setFxSubSource(messageMemo.getSubSource());
                            result.add(srcInfo);
                            //补发单
                            if ((Objects.equals(TYPE_TRADE_EXCHANGE, messageMemo.getType()) || Objects.equals(TYPE_TRADE_REISSUE, messageMemo.getType()))
                                    && messageMemo.getTid().contains("-")) {
                                srcInfo = createFxSupplierSrcInfo(fxTrade, gxTrade);
                                String tid = messageMemo.getTid();
                                srcInfo.setFxTid(tid.substring(0, tid.lastIndexOf("-")));
                                srcInfo.setFxSid(messageMemo.getSid());
                                srcInfo.setFxSource(messageMemo.getSource());
                                srcInfo.setFxSubSource(messageMemo.getSubSource());
                                result.add(srcInfo);
                            }
                        }
                    }else{
                        List<Order> orders4Trade = TradeUtils.getOrders4Trade(fxTrade);
                        if (CollectionUtils.isNotEmpty(orders4Trade)) {
                            Set<Long> added = new HashSet<>();
                            for (Order order : orders4Trade) {
                                if (added.contains(order.getSid())) {
                                    continue;
                                }
                                added.add(order.getSid());
                                FxSupplierSrcInfo srcInfo = createFxSupplierSrcInfo(fxTrade, gxTrade);
                                srcInfo.setFxTid(order.getTid());
                                srcInfo.setFxSid(order.getSid());
                                srcInfo.setFxSource(order.getSource());
                                result.add(srcInfo);
                                //补发单
                                if (order.getTid().contains("-")) {
                                    srcInfo = createFxSupplierSrcInfo(fxTrade, gxTrade);
                                    String tid = order.getTid();
                                    srcInfo.setFxTid(tid.substring(0, tid.lastIndexOf("-")));
                                    srcInfo.setFxSid(order.getSid());
                                    srcInfo.setFxSource(order.getSource());
                                    result.add(srcInfo);
                                }
                            }
                        }
                    }
                }
            }
            if (TradeUtils.isGxAndFxTrade(fxTrade)) {
                if (fxStaff == null) {
                    return result;
                }
                //获取对应的源分销单信息
                List<FxSupplierSrcInfo> srcInfos = fxSupplierSrcService.queryByGxSids(fxStaff, fxTrade.getSid());
                if (CollectionUtils.isNotEmpty(srcInfos)) {
                    for (FxSupplierSrcInfo info : srcInfos) {
                        FxSupplierSrcInfo srcInfo = createFxSupplierSrcInfo(fxTrade, gxTrade);
                        srcInfo.setFxTid(info.getFxTid());
                        srcInfo.setFxSid(info.getFxSid());
                        srcInfo.setFxSource(info.getFxSource());
                        srcInfo.setFxSubSource(info.getFxSubSource());
                        result.add(srcInfo);
                    }
                }
            }
            return result;
        }catch (Throwable e){
            new FxLogBuilder(gxTrade.getCompanyId(),FxLogBuilder.ROLE_GX).appendTrade(gxTrade).appendError("获取供销单对应原始分销信息失败",e).printError(logger,e);
        }
        return null;
    }

    @NotNull
    private FxSupplierSrcInfo createFxSupplierSrcInfo(Trade fxTrade, Trade gxTrade) {
        FxSupplierSrcInfo srcInfo = new FxSupplierSrcInfo();
        // 设置供销单sid
        srcInfo.setSid(gxTrade.getSid());
        // 设置供销单tid
        srcInfo.setTid(gxTrade.getTid());
        // 设置供销商
        srcInfo.setCompanyId(gxTrade.getCompanyId());
        srcInfo.setFxCompanyId(fxTrade.getCompanyId());
        return srcInfo;
    }

    /**
     * 作废分销订单,支持多参数
     *
     * @param cancelData
     * @return
     */
    public List<Trade> cancelFxTrade(CancelData cancelData) {
        Staff staff = cancelData.getStaff();
        try {
            //todo
            return cancelBusiness.cancel(cancelData);
        } catch (Exception e) {
            new FxLogBuilder(staff, FxLogBuilder.ROLE_FX).append("作废分销订单失败").append("sids", Arrays.asList(cancelData.getSidArr())).printError(logger, e);
            return null;
        }
    }

    /**
     * 同步供销运费->分销实际运费
     *
     * @param staff
     * @param trades
     */
    public void syncGxPostFee(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> gxTrades = trades.stream().filter(trade -> TradeUtils.isGxOrMixTrade(trade)
                || TradeUtils.isGxTrade(trade)
                || TradeUtils.isGxAndFxTrade(trade)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(gxTrades)) {
            return;
        }
        if(gxTrades.stream().map(Trade::getSourceId).distinct().count()>1)
        {
            throw new IllegalArgumentException("存在不同的分销商");
        }
        logger.info(new FxLogBuilder(staff, FxLogBuilder.ROLE_GX).append("供销同步运费,gxSids:" + Arrays.toString(TradeUtils.toSids(gxTrades))));
        Map<Long, List<Trade>> sourceIdMap = gxTrades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
        for (Map.Entry<Long, List<Trade>> entry : sourceIdMap.entrySet()) {
            Long sourceId = entry.getKey();
            Staff fxStaff = staffService.queryFullByCompanyId(sourceId);
            if (fxStaff == null) {
                continue;
            }
            Set<Long> fxSids = entry.getValue().stream().map(trade -> Long.valueOf(trade.getTid())).collect(Collectors.toSet());
            Map<String, Trade> gxTradeMap = TradeUtils.toMapByTid(entry.getValue());
            lockService.locks(tradeLockBusiness.getERPLocks(fxStaff, fxSids.toArray(new Long[0])), () -> {
                //合单也需要处理
                List<Trade> fxTrades = tradeSearchService.queryBySidsContainMergeTrade(fxStaff, false, false, true, fxSids.toArray(new Long[0]));
                if (CollectionUtils.isEmpty(fxTrades)) {
                    return null;
                }
                //合单处理
                Map<Long, List<Trade>> mergeTrades = new HashMap<>();
                List<Trade> updateTrades = new ArrayList<>();
                //更新中合单的主单
                Set<Long> mainTradeSids = new HashSet<>();
                for (Trade fxTrade : fxTrades) {
                    if (TradeUtils.isMerge(fxTrade)) {
                        mergeTrades.computeIfAbsent(fxTrade.getMergeSid(), t -> new ArrayList<>()).add(fxTrade);
                        mainTradeSids.add(fxTrade.getMergeSid());
                    }
                    Trade gxTrade = gxTradeMap.get(fxTrade.getSid().toString());
                    if (null != gxTrade && StringUtils.isNotEmpty(gxTrade.getPostFee()) && !MathUtils.equals(fxTrade.getActualPostFee(), gxTrade.getPostFee())) {
                        String oldActualPostFee = fxTrade.getActualPostFee();
                        boolean isInsert = fxTrade.getActualPostFee() == null;
                        fxTrade.setActualPostFee(gxTrade.getPostFee());
                        if (!TradeUtils.isMerge(fxTrade)) {
                            if (isInsert) {
                                fxTrade.getOperations().putIfAbsent(OpEnum.TRADE_FX_SYNC_GX, "实际运费:" + fxTrade.getActualPostFee());
                            } else {
                                if (!MathUtils.equals(fxTrade.getActualPostFee(), gxTrade.getPostFee())) {
                                    fxTrade.getOperations().putIfAbsent(OpEnum.TRADE_FX_SYNC_GX, "订单实际运费变动:" + oldActualPostFee + "->" + fxTrade.getActualPostFee());
                                }
                            }
                        }
                        updateTrades.add(fxTrade);
                    }
                }
                for (Long mergeSid : mainTradeSids) {
                    List<Trade> merges = mergeTrades.get(mergeSid);
                    if (CollectionUtils.isEmpty(merges)) {
                        continue;
                    }
                    Trade main = null;
                    BigDecimalWrapper actualPostFee = new BigDecimalWrapper();
                    for (Trade trade : merges) {
                        actualPostFee.add(trade.getActualPostFee());
                        if (trade.getSid().equals(mergeSid)) {
                            main = trade;
                        } else {
                            trade.setActualPostFee("0");
                        }
                    }
                    if (null != main && !MathUtils.equals(main.getActualPostFee(), actualPostFee.getString())) {
                        String oldActualPostFee = main.getActualPostFee();
                        boolean isInsert = main.getActualPostFee() == null;
                        main.setActualPostFee(actualPostFee.getString());
                        main.getOperations().putIfAbsent(OpEnum.TRADE_FX_SYNC_GX, isInsert ? "实际运费:" + main.getActualPostFee() : "订单实际运费变动:" + oldActualPostFee + "->" + main.getActualPostFee());
                        if (!updateTrades.contains(main)) {
                            updateTrades.add(main);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(updateTrades)) {
                    tradeUpdateService.updateTrades(fxStaff, updateTrades.stream().map(trade -> {
                        TbTrade tbTrade = new TbTrade();
                        tbTrade.setSid(trade.getSid());
                        tbTrade.setActualPostFee(trade.getActualPostFee());
                        return tbTrade;
                    }).collect(Collectors.toList()));
                }
                tradeTraceBusiness.asyncTrace(fxStaff, updateTrades.stream().filter(t -> MapUtils.isNotEmpty(t.getOperations())).collect(Collectors.toList()), OpEnum.TRADE_FX_SYNC_GX);
                return null;
            });
        }
    }

    public String getDetailCashFlowLog(Staff staff,Trade gxTrade, Trade fxTrade) {
        if (gxTrade == null || fxTrade == null) {
            return "";
        }
        StringBuilder detailLog = new StringBuilder();
        String fxTotalPrice = new BigDecimalWrapper(gxTrade.getTotalFee()).getString();
        String oldFxTotalPrice = Optional.ofNullable(TradeExtUtils.getExtraFieldValue(fxTrade.getTradeExt(), "fxTotalPrice")).orElse("0.00").toString();
        if (!MathUtils.equals(fxTotalPrice, oldFxTotalPrice)) {
            TradeExtUtils.setExtraFieldValue(fxTrade.getTradeExt(), "fxTotalPrice", fxTotalPrice);
            detailLog.append(String.format("分销价由:%s改为:%s;", StringUtils.defaultString(oldFxTotalPrice, "0.00"), fxTotalPrice));
        }
        if (!MathUtils.equals(new BigDecimalWrapper(gxTrade.getPostFee()).getString(), fxTrade.getOldAcPayment())) {
            detailLog.append(String.format("实际运费由:%s改为:%s;", StringUtils.defaultString(fxTrade.getOldAcPayment(), "0.00"), StringUtils.defaultString(gxTrade.getPostFee(), "0.00")));
            fxTrade.setActualPostFee(gxTrade.getPostFee() == null ? "0.00" : gxTrade.getPostFee());
        }
        String oldFxCommission = Optional.ofNullable(TradeExtUtils.getExtraFieldValue(fxTrade.getTradeExt(), "fxCommission")).orElse("0.00").toString();
        String fxCommission = MathUtils.toString(new BigDecimalWrapper(gxTrade.getDiscountFee()).get().negate());
        if (!MathUtils.equals(fxCommission, oldFxCommission)) {
            detailLog.append(String.format("佣金由:%s改为:%s;", StringUtils.defaultString(oldFxCommission, "0.00"), fxCommission));
            TradeExtUtils.setExtraFieldValue(fxTrade.getTradeExt(), "fxCommission", fxCommission);
        }
        String oldFxCost = Optional.ofNullable(TradeExtUtils.getExtraFieldValue(fxTrade.getTradeExt(), "fxCost")).orElse("0.00").toString();
        String newFxCost = PaymentUtils.calculateFxTotalCost(gxTrade);
        if (!MathUtils.equals(oldFxCost, newFxCost)) {
            TradeExtUtils.setExtraFieldValue(fxTrade.getTradeExt(), "fxCost", newFxCost);
            detailLog.append(String.format("分销总成本由:%s改为:%s;", StringUtils.defaultString(oldFxCost, "0.00"), newFxCost));
        }
        return detailLog.toString();
    }

    /**
     * 供销触发,重算分销(分销价,运费)
     * @param staff
     * @param trades
     */
    public void syncFxData(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        Map<Long, List<Trade>> gxTradeMap = TradeUtils.groupBySourceId(trades);
        for (Long sourceId : gxTradeMap.keySet()) {
            Staff fxStaff = staffService.queryFullByCompanyId(sourceId);
            if (fxStaff == null) {
                continue;
            }
            List<Trade> temp = gxTradeMap.get(sourceId);
            temp = temp.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(temp)) {
                List<Long> fxSids = TradeUtils.toTidList(temp).stream().map(Long::valueOf).collect(Collectors.toList());
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_SYNC).setArgs(new Object[]{fxStaff, fxSids}), null);
            }
        }
    }


    /**
     *
     *  合计已经占用的库存
     *
     *   key:   供销公司id
     *   value: 仓库--> 商品编码，库存数量
     *
     *   分销商对应的供销库存占用信息
     *
     * @param staff
     * @return
     */
    public void handleStockCheckMap(Staff staff,AuditFxData auditFxData){
        if(!auditFxData.hasHandleStockCheckMap && !ConfigHolder.FX_GLOBAL_CONFIG.open(FxGlobalConfig.FX_AUDIT_LOCK_GX_ITEM_STOCK, staff.getCompanyId())){
            return;
        }
        auditFxData.hasHandleStockCheckMap = true;
        List<Long> clueIds = sharedCacheBusiness.get(FX_STOCK_CLUE_ID_LIST);
        if(CollectionUtils.isEmpty(clueIds)){
            return;
        }
        Map<Long, Map<Long, Map<String, Integer>>> dmsStockCheckMap = auditFxData.dmsStockCheckMap;
        for (Long clueId : clueIds) {
            Map<Long, Map<Long, Map<String, Integer>>> dmsStockCheckMapTmp = sharedCacheBusiness.get(GX_ITEM_STOCK_CACHE_PREFIX + clueId);
            if (MapUtils.isNotEmpty(dmsStockCheckMapTmp)) {
                dmsStockCheckMap = mergeStockCheckMaps(dmsStockCheckMap, dmsStockCheckMapTmp);
            }
        }
        auditFxData.dmsStockCheckMap = dmsStockCheckMap;
        if(MapUtils.isNotEmpty(dmsStockCheckMap)){
            new FxLogBuilder(staff,FxLogBuilder.ROLE_FX).append("从缓存中获取到的库存信息").append(dmsStockCheckMap).printDebug(logger);
        }
    }


    /**
     * 把相同的key 合计起来
     *
     * @param map1
     * @param map2
     * @return
     */
    public static Map<Long, Map<Long, Map<String, Integer>>> mergeStockCheckMaps(
            Map<Long, Map<Long, Map<String, Integer>>> map1,
            Map<Long, Map<Long, Map<String, Integer>>> map2) {

        Map<Long, Map<Long, Map<String, Integer>>> resultMap = new HashMap<>(map1);

        map2.forEach((key1, innerMap1) -> {
            resultMap.putIfAbsent(key1, new HashMap<>());
            innerMap1.forEach((key2, innerMap2) -> {
                resultMap.get(key1).putIfAbsent(key2, new HashMap<>());
                innerMap2.forEach((key3, value) ->
                        resultMap.get(key1).get(key2).merge(key3, value, Integer::sum)
                );
            });
        });

        return resultMap;
    }


    /**
     *
     * 供销单生成删除对应分销生成的库存缓存
     *
     *
     * @param fxCompanyId
     */
    public void handleGxItemStockCache(Long fxCompanyId){
        if(!ConfigHolder.FX_GLOBAL_CONFIG.open(FxGlobalConfig.FX_AUDIT_LOCK_GX_ITEM_STOCK, fxCompanyId)){
            return;
        }

        List<Long> clueIds = sharedCacheBusiness.get(FX_STOCK_CLUE_ID_LIST);
        Long clueId = ClueIdUtil.getClueId();

        if(CollectionUtils.isNotEmpty(clueIds) && clueIds.contains(clueId) ){
            clueIds.remove(clueId);
            sharedCacheBusiness.set(FX_STOCK_CLUE_ID_LIST,clueIds,600);
        }

        sharedCacheBusiness.delete(GX_ITEM_STOCK_CACHE_PREFIX+clueId);
    }


    /**
     *
     * 分销审核事务提交，把供销已经占用的库存写入缓存,审核前合计已经存在的库存
     * 供销单生成需要把对应的缓存删除
     *
     *
     */
    public static class PreLockGxTradeStockTransactionSynchronization implements TransactionSynchronization {

        private  Staff staff;
        private  AuditFxData auditFxData;
        private  SharedCacheBusiness sharedCacheBusiness;


        public PreLockGxTradeStockTransactionSynchronization(Staff staff,AuditFxData auditFxData,SharedCacheBusiness sharedCacheBusiness){
            this.staff = staff;
            this.auditFxData = auditFxData;
            this.sharedCacheBusiness = sharedCacheBusiness;
        }

        @Override
        public void suspend() {

        }

        @Override
        public void resume() {

        }

        @Override
        public void flush() {

        }

        @Override
        public void beforeCommit(boolean readOnly) {

        }

        @Override
        public void beforeCompletion() {

        }

        @Override
        public void afterCommit() {

        }


        @Override
        public void afterCompletion(int status) {

            List<Long> clueIds = sharedCacheBusiness.get(FX_STOCK_CLUE_ID_LIST);
            Long clueId = ClueIdUtil.getClueId();
            if(clueIds == null){
                clueIds = Lists.newArrayList(clueId);
            }else if(!clueIds.contains(clueId)){
                clueIds.add(clueId);
            }
            sharedCacheBusiness.set(FX_STOCK_CLUE_ID_LIST,clueIds,600);

            Map<Long, Map<Long, Map<String, Integer>>> dmsStockCheckMap = Maps.newHashMapWithExpectedSize(8);
            if(org.apache.commons.collections4.MapUtils.isNotEmpty(auditFxData.sid2DmsStockCheckMap)){
                for (Map.Entry<Long, Map<Long, Map<Long, Map<String, Integer>>>> entry : auditFxData.sid2DmsStockCheckMap.entrySet()) {
                    Long k = entry.getKey();
                    Map<Long, Map<Long, Map<String, Integer>>> v = entry.getValue();
                    dmsStockCheckMap = mergeStockCheckMaps(dmsStockCheckMap, v);
                }
                sharedCacheBusiness.set(GX_ITEM_STOCK_CACHE_PREFIX+clueId,dmsStockCheckMap,600);
            }

        }
    }


    /**
     * 根据规则计算供销订单分销价
     *
     * @param staff
     * @param tradeList
     */
    public void caculateGxPrice(Staff staff, List<Trade> tradeList) {
        caculateGxPrice(staff, tradeList, false);
    }



    public Map<Long, String> getPlatformIdContainMergeFromGx(Staff gxStaff, List<? extends Trade> trades){
        Map<Long, String> result = new HashMap<>();
        try {
            Map<Long, List<Trade>> source2trade = TradeUtils.groupBySourceId(trades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList()));
            source2trade.forEach((sourceId, gxTrades) ->{
                Staff fxStaff = staffService.queryFullByCompanyId(sourceId);
                Map<String, Trade> tid2trade = TradeUtils.toMapByTid(gxTrades);
                if (Objects.isNull(fxStaff)){
                    return;
                }
                Long[] fxSids = gxTrades.stream().map(Trade::getTid).map(Long::valueOf).toArray(Long[]::new);
                List<TbTrade> tradeList = tbTradeDao.queryByKeys(fxStaff, "sid,tid,source,v,merge_sid,convert_type,dest_id,belong_type,source_id", "sid", fxSids);
                //填充供销单sid作为关联标识
                tradeList.forEach(t ->{
                    Trade gxTrade = tid2trade.get(t.getSid().toString());
                    if (Objects.isNull(gxTrade)){
                        return;
                    }
                    if (Objects.nonNull(gxTrade.getGuid())){
                        t.setGuid(gxTrade.getGuid());
                    }else {
                        t.setGuid(gxTrade.getSid());
                    }
                });
                Map<Long, Trade> sid2trade = TradeUtils.toMapBySid(tradeList);
                Map<Long, String> nextFx = getPlatformIdContainMergeFromFx(fxStaff, tradeList);
                nextFx.forEach((fxSid, platFormId)->{
                    Trade mainFxTrade = sid2trade.get(fxSid);
                    if (Objects.isNull(mainFxTrade)){
                        return;
                    }
                    result.put(mainFxTrade.getGuid(), platFormId);
                });
                Map<Long, String> nextGx = getPlatformIdContainMergeFromGx(fxStaff, tradeList);
                result.putAll(nextGx);
            });
        }catch (Throwable e){
            FxLogBuilder.gx(gxStaff).appendError("供销获取分销合并原始平台单号失败",e).printWarn(logger,e);
        }
        return result;
    }

    public Map<Long, String> getPlatformIdContainMergeFromFx(Staff fxStaff, List<? extends Trade> trades){
        Map<Long, String> result = new HashMap<>();
        try {
            Long[] mergeSids = trades.stream().filter(TradeUtils::isFxTrade).filter(TradeUtils::isMerge).map(Trade::getMergeSid).toArray(Long[]::new);
            if (mergeSids.length == 0){
                return result;
            }
            List<TbTrade> mergeFxTrades = tbTradeDao.queryByKeys(fxStaff, "sid,tid,source,v,merge_sid", "merge_sid", mergeSids);
            if (CollectionUtils.isEmpty(mergeFxTrades)){
                return result;
            }
            Map<Long, TradeExt> tradeExtMap = tradeExtDao.queryTradeExtBySids(fxStaff, mergeFxTrades.stream().map(Trade::getSid).map(String::valueOf).collect(Collectors.toList())).stream().collect(Collectors.toMap(TradeExt::getSid, Function.identity(), (k1, k2)->k1));
            for (Trade fxTrade : mergeFxTrades){
                TradeExt tradeExt = tradeExtMap.get(fxTrade.getSid());
                String platformId = (String) TradeExtUtils.getExtraFieldValue(tradeExt,"thirdPlatTid");
                if(org.springframework.util.StringUtils.isEmpty(platformId)){
                    platformId = fxTrade.getTid();
                    if(Objects.equals(CommonConstants.PLAT_FORM_TYPE_SYS,fxTrade.getSource()) && TradeUtils.isContainV(fxTrade, TradeConstants.V_TRADE_COPY)){
                        platformId = org.apache.commons.lang3.StringUtils.substringBeforeLast(platformId,"-");
                    }
                }
                if (result.containsKey(fxTrade.getMergeSid())){
                    String value = result.get(fxTrade.getMergeSid());
                    if (null == value){
                        value = "";
                    }
                    if (value.contains(platformId)){
                        continue;
                    }
                    if (Objects.equals(fxTrade.getSid(), fxTrade.getMergeSid())){
                        result.put(fxTrade.getMergeSid(), platformId + "," + value);
                    }else {
                        result.put(fxTrade.getMergeSid(), value + "," + platformId);
                    }
                }else {
                    result.put(fxTrade.getMergeSid(), platformId);
                }
            }
        }catch (Throwable e){
            FxLogBuilder.fx(fxStaff).appendError("分销合并获取原始平台单号失败",e).printWarn(logger,e);
        }
        return result;
    }


    /**
     * 获取最新分销价并填充
     *
     * @param staff
     * @param trades
     */
    public void fillDmsPrice(Staff staff, List<Trade> trades) {
        if (staff == null || CollectionUtils.isEmpty(trades)) {
            return;
        }
        AuditFxData auditFxData = auditFxBusiness.fillFxConfig(staff,trades);
        auditFxBusiness.fillFxOrderPriceInfo(staff, trades, auditFxData);
        AuditFxUtils.fillDmsPrice(trades, auditFxData.dmsOrderPriceMap, auditFxData.dmsOrderPriceSupplierMap, auditFxData.dmsDistributorConfigDto.getUseSupplierCode(), auditFxData.isSalePriceSourceFx, auditFxData.isCostSourceFx, dmsTradeService);
    }
    public Long extendV(Long v){
        if(v == null){
            return v;
        }
        int mask = 0xFFFFFFF0; // 掩码，二进制为 11111111 11111111 11111111 11110000
        return v & mask;
    }
    public void saveFxAppointTemplate(Staff staff,Trade trade){
        TradeExt tradeExt = trade.getTradeExt();
        if(tradeExt == null){
            return;
        }
        TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.FX_APPOINT_TPL_ID.getField(), trade.getTemplateId()==null?null:String.valueOf(trade.getTemplateId()));
        TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.FX_APPOINT_TPL_NAME.getField(), trade.getTemplateName());
        TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.FX_APPOINT_TPL_TYPE.getField(), trade.getTemplateType()==null?null:String.valueOf(trade.getTemplateType()));
        TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.FX_APPOINT_LOGISTICS_COMPANY_ID.getField(), trade.getLogisticsCompanyId()==null?null:String.valueOf(trade.getLogisticsCompanyId()));
        TradeExtUtils.setExtraFieldValue(tradeExt, TradeExtraFieldEnum.FX_APPOINT_LOGISTICS_COMPANY_NAME.getField(), trade.getLogisticsCompanyName());
    }

    public void fxAppointTemplate(Staff staff,List<Trade> trades){
        if(CollectionUtils.isEmpty(trades)){
            return;
        }
        boolean ifDmsAllowFxAppointTemplateId = TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_ALLOW_FX_APPOINT_TEMPLATE_ID).isOpen();
        if(!ifDmsAllowFxAppointTemplateId){
            new FxLogBuilder(staff,FxLogBuilder.ROLE_GX).append("分销指定供销快递,供销没有开启：【允许分销商指定快递模板】配置").printDebug(logger);
            return;
        }
        boolean ifDmsFxAppointTemplateCalcPostFee = TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_FX_APPOINT_TEMPLATE_CALC_POST_FEE).isOpen();

        FxLogBuilder fxLogBuilder = new FxLogBuilder(staff,FxLogBuilder.ROLE_GX).append(trades.get(0).hasOpV(OpVEnum.TRADE_IMPORT_INSERT_CHAIN)?"分销指定供销快递":"分销重新指定供销快递").startWatch();
        for (Trade trade : trades) {
            Trade sourceTrade = trade.getSourceTrade();
            if(sourceTrade == null){
                continue;
            }
            // 分销订单已经选择了供销的快递模板
            if (TradeUtils.ifContainV(trade, TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID) || TradeUtils.ifContainV(sourceTrade, TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID)) {
                trade.setTemplateType(sourceTrade.getTemplateType());
                trade.setTemplateId(sourceTrade.getTemplateId());
                trade.setTemplateName(sourceTrade.getTemplateName());
                trade.setExpressCompanyId(sourceTrade.getExpressCompanyId());
                trade.setLogisticsCompanyName(sourceTrade.getLogisticsCompanyName());
                trade.setLogisticsCompanyId(sourceTrade.getLogisticsCompanyId());
                if(ifDmsFxAppointTemplateCalcPostFee){
                    saveFxAppointTemplate(staff,trade);
                }
                trade.addOpV(OpVEnum.TRADE_FX_REAPPOINT_TEMPLATE_ID);
                trade.addOpV(OpVEnum.TRADE_GX_RECALC_CASHFLOW);
                fxLogBuilder.append("sid",trade.getSid())
                        .appendIf(()->StringUtils.isNotBlank(sourceTrade.getTemplateName()),"模板",sourceTrade.getTemplateName())
                        .appendIf(()->StringUtils.isNotBlank(sourceTrade.getLogisticsCompanyName()),"快递公司",sourceTrade.getTemplateName());
            }
        }
        fxLogBuilder.printDebug(logger);
    }

}

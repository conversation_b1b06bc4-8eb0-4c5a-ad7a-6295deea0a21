package com.raycloud.dmj.business.modify;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.raycloud.dmj.*;
import com.raycloud.dmj.business.callback.TradeUpdateLockCallback;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.except.TradePlatModifyItemNumExceptionBusiness;
import com.raycloud.dmj.business.gift.GiftDeductCountBusiness;
import com.raycloud.dmj.business.item.TradeItemChangeBusiness;
import com.raycloud.dmj.business.operate.CalculateBusiness;
import com.raycloud.dmj.business.modify.support.SuitSwitchUtils;
import com.raycloud.dmj.business.operate.MoneyChangeBusiness;
import com.raycloud.dmj.business.order.OrderModifyLogBusiness;
import com.raycloud.dmj.business.payment.TradeSuitCalculateBusiness;
import com.raycloud.dmj.business.rematch.business.ReMatchBusiness;
import com.raycloud.dmj.business.stock.TradeConfigStockBusiness;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.business.tradepay.TradePayBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.base.AttrCopier;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.trade.history.OrderModifyLogUtils;
import com.raycloud.dmj.domain.trade.common.TradeBusinessEnum;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.history.OrderHistory;
import com.raycloud.dmj.domain.trade.history.TradeHistory;
import com.raycloud.dmj.domain.trade.history.TradeHistoryUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.gift.utils.GiftRuleUtils;
import com.raycloud.dmj.services.trace.IItemTraceService;
import com.raycloud.dmj.services.trade.except.TradeExceptBizBusiness;
import com.raycloud.dmj.services.trade.history.TradeHistoryService;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trade.supplier.IOrderSupplierService;
import com.raycloud.dmj.services.trade.supplier.TradeOrderSupplier;
import com.raycloud.dmj.services.trade.warehouse.TradeWarehouseContextBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.item.TradeItemContext;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import com.raycloud.erp.trade.search.db.OrderFixBusiness;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.*;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.*;

import static com.raycloud.dmj.business.nonconsign.TradeNonConsignBusiness.*;
import static com.raycloud.dmj.domain.TradeConstants.*;

/**
 * Created by yangheng on 17/7/11.
 * 修改订单
 */
@Service
public class TradeUpdateBusiness {

    protected AttrCopier<Order, Order> orderCopier = new OrderCopier<>();

    @Resource
    OrderFixBusiness orderFixBusiness;

    @Resource
    TradePayBusiness tradePayBusiness;

    @Resource
    TradeConfigStockBusiness tradeConfigStockBusiness;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    ITradePtService tradePtService;
    @Resource
    MoneyChangeBusiness moneyChangeBusiness;
    @Resource
    IOrderSupplierService orderSupplierService;
    @Resource
    TradeUpdateValidateBusiness tradeUpdateValidateBusiness;

    @Resource
    ITradeOrderSyncItemTag tradeOrderSyncItemTagFill;
    @Resource
    TradeItemChangeBusiness tradeItemChangeBusiness;

    @Resource
    ReMatchBusiness reMatchBusiness;
    @Resource
    TradeWarehouseContextBusiness tradeWarehouseContextBusiness;

    @Resource
    TradeExceptBizBusiness tradeExceptBizBusiness;

    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;

    @Resource
    FeatureService featureService;

    @Resource
    ModifyParentBusiness modifyParentBusiness;
    @Resource
    ILockService lockService;
    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    PaymentLinkBusiness paymentLinkBusiness;
    @Resource
    CalculateBusiness calculateBusiness;
    @Resource
    GxTradeItemModifyBusiness gxTradeItemModifyBusiness;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;
    @Resource
    IEventCenter eventCenter;
    @Resource
    OrderModifyLogBusiness orderModifyLogBusiness;
    @Resource
    IdWorkerService idWorkerService;
    @Resource
    TradeSuitCalculateBusiness tradeSuitCalculateBusiness;
    @Resource
    TbOrderDAO tbOrderDao;
    @Resource
    IOrderStockService orderStockService;
    @Resource
    TradeHistoryService tradeHistoryService;
    @Resource
    GiftDeductCountBusiness giftDeductCountBusiness;

    private final Logger logger = Logger.getLogger(TradeUpdateBusiness.class);

    @Transactional
    public ModifyData update(final Staff staff, final Trade trade, final boolean isGiftUpdate, boolean isSyncChain) {
        return update(staff, trade, isGiftUpdate, false, isSyncChain);
    }

    @Transactional
    public void updateOsa(final Staff staff, final Trade trade, final boolean isGiftUpdate) {
        update(staff, trade, isGiftUpdate, true, false);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ModifyData updateAndRemove(final Staff staff, final Trade trade, final boolean isGiftUpdate) {
        return update(staff, trade, isGiftUpdate, false, false);
    }

    @Transactional
    public ModifyData update(final Staff staff, final Trade trade, final boolean isGiftUpdate, final boolean isOsa, boolean isSyncChain) {
        Assert.isTrue(!TradeUtils.getOrders4Trade(trade).isEmpty(), "一笔订单下至少需要有一个商品");
        TradeUpdateLockCallback callback = new TradeUpdateLockCallback(staff, trade, this, isGiftUpdate, isOsa, isSyncChain);
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, trade.getSid()), callback);
    }

    /**
     * 执行update方法，需要在拿到锁以后的callback里面调用，不要直接调用
     */
    @Transactional
    public ModifyData doUpdate(Staff staff, Trade trade, boolean isGiftUpdate, boolean isOsa, boolean isSyncChain) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, trade.getSid()), () -> {
            moneyChangeBusiness.tradeUpdateMoneyLog(staff, OpEnum.MONEY_CHANGE_PARAMAS_BEFORE, OpEnum.TRADE_UPDATE, Collections.singletonList(trade));
            //查询原订单信息
            List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, trade.getSid());
            List<TradeHistory> tradeHistories = new ArrayList<>();List<OrderHistory> orderHistories = new ArrayList<>();
            modifyParentBusiness.setHistory(staff, originTrades, TradeBusinessEnum.TRADE_ITEM_CHANGE, tradeHistories, orderHistories);
            // KMERP-184748 https://tb.raycloud.com/task/6639e7fc1ed72940098dc430
            if (originTrades.get(0).getSid() - trade.getSid() != 0) {
                throw new IllegalArgumentException("合单的主单发生变化，请刷新页面重试！");
            }
            //初始化旧的信息
            for (Trade originTrade : originTrades) {
                originTrade.setOldNotSysGiftNum(originTrade.getItemNum() - TradeUtils.getSysGiftNum(staff, originTrade));
            }
            tradeUpdateValidateBusiness.validateDeleteOrder(staff, trade, originTrades);
            moneyChangeBusiness.tradeUpdateMoneyLog(staff, OpEnum.MONEY_CHANGE_BEFORE, OpEnum.TRADE_UPDATE, originTrades);
            //校验
            Trade originTrade = validate(staff, trade, originTrades);
            boolean validateTitle = fillPlatformTitle(staff, trade, originTrade);
            //新的trade继承旧的trade异常
            trade.setExceptData(TradeExceptUtils.getTradeExceptData(originTrade));
            trade.setSubTradeExceptDatas(originTrade.getSubTradeExceptDatas());
            //设置需要更新的信息
            ModifyData modifyData = handle4Update(staff, trade, originTrade, isGiftUpdate, tradeConfig, isOsa, isSyncChain, validateTitle);
            checkDelete(staff, trade, originTrade);
            // 透传一些旧的信息给update对象
            trade.setOldNotSysGiftNum(originTrade.getOldNotSysGiftNum());

            List<Trade> paymentTradeList = Stream.of(trade).collect(Collectors.toList());
            Calculator calculator = modifyParentBusiness.getCalculator(staff);
            calculator.setCalculatePayment(false);
            paymentLinkBusiness.paymentCalculator(staff, calculator);
            calculateBusiness.calculate(staff, paymentTradeList, calculator);
            handleTradeUpdate(staff, trade, modifyData);

            modifyParentBusiness.calculate(staff, trade, tradeConfig);
            //理论重量是否覆盖实际重量
            modifyParentBusiness.isCoverWeight(staff, Collections.singletonList(trade), tradeConfig);

            //金额不一致的标签判断
            tradePayBusiness.judgeFeeUpdate(staff, trade);
            //更新订单
            List<Trade> updateTrades = new ArrayList<>();
            // 删除的order对应的异常也要删除
            modifyData.deletes.forEach(order -> TradeExceptUtils.delOrderExcept(staff, trade, order));
            updateTrades.add(trade);
            setTraceContent(staff, trade, modifyData, isGiftUpdate, isOsa);
            //校验实付金额
            checkTradePayment(staff, updateTrades);
            boolean openPartialPay = featureService.checkHasFeature(staff.getCompanyId(), Feature.PARTIAL_PAYMENT_EXCEPT);
            if (openPartialPay) {
                updateTrades.forEach(updateTrade -> modifyParentBusiness.checkPartPayExcept(staff, updateTrade));
            }

            gxTradeItemModifyBusiness.beforeUpdate(updateTrades);
            tradePtService.saveByTrades(staff, updateTrades);
            tradeUpdateService.updateTrades(staff, updateTrades);
            // 删除赠品匹配日志
            if (isGiftUpdate) {
                List<Order> deleteGiftOrders = modifyData.deletes.stream().filter(Order::isGift).collect(Collectors.toList());
                giftDeductCountBusiness.returnGiftCountByOrderIds(staff, deleteGiftOrders.stream().map(Order::getId).collect(Collectors.toList()));
            }
            orderModifyLogBusiness.addLog(staff,modifyData.orderModifyLogs);
            //计算订单修改后金额相关计算
            modifyParentBusiness.calculateTrade(staff, trade, originTrade);
            //处理合单的修改
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, originTrade)) {
                modifyParentBusiness.handleUpdateMergeTrade(staff, originTrade.getSid());
                modifyParentBusiness.handleUpdateMergeOrder(staff, originTrade.getSid());
            }

            tradeConfigStockBusiness.handleStock(staff, updateTrades);
            if(isSyncChain){
                tradeTraceBusiness.asyncTrace(staff, updateTrades, OpEnum.TRADE_UPDATE,"系统");
            }else {
                tradeTraceBusiness.asyncTrace(staff, updateTrades, OpEnum.TRADE_UPDATE);
            }
            List<Trade> updateAfterTrades = tradeSearchService.queryBySids(staff, true, trade.getSid());
            moneyChangeBusiness.tradeUpdateMoneyLog(staff, OpEnum.MONEY_CHANGE_AFTER, OpEnum.TRADE_UPDATE, updateAfterTrades);
            tradeHistoryService.addTradeHistory(staff, tradeHistories);
            tradeHistoryService.addOrderHistory(staff, orderHistories);

            //修改商品重算
            if (Boolean.TRUE.equals(trade.getRematchItemReplace())) {
                eventCenter.fireEvent(this, new EventInfo("trade.update.item.replace").setArgs(new Object[]{staff.getId(), Lists.newArrayList(trade.getSid())}), null);
            } else {
                reMatchBusiness.reMatch(staff, TradeUtils.toSidList(updateTrades), EventEnum.EVENT_CHANGE_ITEM, Boolean.TRUE);
            }
            tradeSysLabelBusiness.matchSystemLabels(staff, updateTrades, Lists.newArrayList(SystemTags.TAG_ITEM_PRICE_EXCEPTION), true, true, true);
            //分销修改商品
            gxTradeItemModifyBusiness.updateTradeAfter(staff,updateAfterTrades);
            return modifyData;
        });
    }

    /**
     * 填充平台title，系统手工单不需要
     */
    public boolean fillPlatformTitle(Staff staff, Trade trade, Trade originTrade) {
        boolean validateTitle = true;
        if (Objects.isNull(trade) || Objects.isNull(originTrade)) {
            return validateTitle;
        }
        if (TradeUtils.isSystemTrade(originTrade)) {
            Logs.info(LogHelper.buildLog(staff, "系统订单不继承平台属性"));
            return validateTitle;
        }

        List<Order> originOrders = TradeUtils.getOrders4Trade(originTrade);
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        Map<Long, Order> oIdMap = OrderUtils.toMapByOid(originOrders);
        for (Order order : orders) {
            Order originOrder = oIdMap.get(order.getOid());
            if (Objects.isNull(originOrder)) {
                continue;
            }
            if (StringUtils.isEmpty(originOrder.getTitle())) {
                validateTitle = false;
            }
            // 存在原订单就是用原订单的title
            order.setTitle(originOrder.getTitle());
        }
        return validateTitle;
    }

    @Transactional
    public void updateAndRemoveBatch(Staff staff, ReplaceItemData replaceData) {
        if (!replaceData.hasData()) {
            return;
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        moneyChangeBusiness.tradeUpdateMoneyLog(staff, OpEnum.MONEY_CHANGE_PARAMAS_BEFORE, OpEnum.TRADE_UPDATE, replaceData.getUpdateTradeList());
        moneyChangeBusiness.tradeUpdateMoneyLog(staff, OpEnum.MONEY_CHANGE_BEFORE, OpEnum.TRADE_UPDATE, new ArrayList<>(replaceData.getOriginTradeMap().values()));
        Calculator calculator = modifyParentBusiness.getCalculator(staff);
        calculator.setCalculatePayment(false);
        paymentLinkBusiness.paymentCalculator(staff, calculator);
        calculateBusiness.calculate(staff, replaceData.getUpdateTradeList(), calculator);

        handleTradeUpdateBatch(staff, replaceData);
        replaceData.getUpdateTradeList().forEach(t -> {
            TradeStockUtils.resetTradeStockStatus(staff, t, tradeConfig);
            modifyParentBusiness.calculate(staff, t, tradeConfig);
        });

        //理论重量是否覆盖实际重量
        modifyParentBusiness.isCoverWeight(staff, replaceData.getUpdateTradeList(), tradeConfig);
        //金额不一致的标签判断
        tradePayBusiness.judgeFeeUpdates(staff, replaceData.getUpdateTradeList());
        setReplaceTraceContent(staff, replaceData);
        //校验实付金额
        checkTradePayment(staff, replaceData.getUpdateTradeList());
        tradeUpdateService.updateTrades(staff, replaceData.getUpdateTradeList());
        //计算订单修改后金额相关计算
        modifyParentBusiness.calculateTrade(staff, replaceData.getUpdateTradeList(), replaceData.getOriginTradeMap());

        tradeConfigStockBusiness.handleStock(staff, replaceData.getUpdateTradeList());
        tradeTraceBusiness.asyncTrace(staff, replaceData.getUpdateTradeList(), OpEnum.TRADE_UPDATE);
        List<Trade> updateAfterTrades = tradeSearchService.queryBySids(staff, true, TradeUtils.toSids(replaceData.getUpdateTradeList()));
        moneyChangeBusiness.tradeUpdateMoneyLog(staff, OpEnum.MONEY_CHANGE_AFTER, OpEnum.TRADE_UPDATE, updateAfterTrades);

        // KMERP-221935
        List<Order> updateAfterOrders = TradeUtils.getOrders4Trade(updateAfterTrades);
        List<OrderModifyLog> orderModifyLogs = new ArrayList<>();
        for (Order updateAfterOrder : updateAfterOrders) {
            OrderModifyLog orderModifyLog = OrderModifyLogUtils.build(updateAfterOrder, OrderModifyLogTypeEnum.ORDER_CHANGE);
            Order originOrder = updateAfterOrder.getOrigin();
            if (Objects.nonNull(originOrder)) {
                OrderModifyLogContent content = OrderModifyLogContent.builder().sysOuterId(originOrder.getSysOuterId()).sysItemId(originOrder.getItemSysId()).sysSkuId(originOrder.getSkuSysId()).build();
                orderModifyLog.setContent(JSON.toJSONString(content));
            }
            orderModifyLogs.add(orderModifyLog);
        }
        orderModifyLogBusiness.addLog(staff, orderModifyLogs);

        //修改商品重算
        reMatchBusiness.reMatch(staff, TradeUtils.toSidList(replaceData.getUpdateTradeList()), EventEnum.EVENT_CHANGE_ITEM, Boolean.TRUE);
        //分销修改订单
        gxTradeItemModifyBusiness.updateTradeAfter(staff,replaceData.getUpdateTradeList());
    }

    private void checkTradePayment(Staff staff, List<Trade> updateTrades) {
        if (CollectionUtils.isNotEmpty(updateTrades)) {
            for (Trade trade : updateTrades) {
                if (StringUtils.isNotBlank(trade.getPayment()) && !PaymentUtils.isNumeric(trade.getPayment())) {
                    throw new IllegalArgumentException("订单号:" + trade.getSid() + "，传入的实付金额非数字，请检查");
                }
            }
        }
    }

    /**
     * 将需要更新的系统订单，设置到原始订单中，主要设置修改后订单的库存状态,包括新增的子订单以及删除的子订单以及修改的子订单
     */
    ModifyData handle4Update(Staff staff, Trade trade, Trade origin, boolean isGiftUpdate, TradeConfig tradeConfig, boolean isOsa, boolean isSyncChain, boolean validateTitle) {
        //是否选择金额不联动
        ModifyData tradeUpdate = new ModifyData();
        fillOriginTradeInfo(staff, trade, origin);
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        Assert.isTrue(!orders.isEmpty(), "订单至少需要有一个商品(规格)");
        List<Order> originOrders = TradeUtils.getOrders4Trade(origin);
        Map<Long, Order> orderMap = OrderUtils.toMap(originOrders);
        Map<Long, Order> oidMap = getOrderMap(originOrders);
        for (Order order : orders) {
            if (StringUtils.isBlank(order.getStatus())) {
                order.setStatus(origin.getStatus());
            }
            if (Objects.isNull(order.getUserId()) || order.getUserId() <= 0) {
                order.setUserId(origin.getUserId());
            }
            modifyParentBusiness.verifyOrder(staff, order, trade);
            if (order.getId() == null || order.getId() <= 0) {//新增的子订单
                Order originOrder = oidMap.get(order.getOid());
                // 新增的标识
                order.setInsert(true);
                // KMERP-210677
                if (featureService.checkHasFeature(staff.getCompanyId(), Feature.ONE_EXCHANGE_MANY_INHERIT_PROPERTY_COMPANY_IDS)) {
                    if (Objects.nonNull(originOrder)) {
                        order.setNumIid(originOrder.getNumIid());
                        order.setSkuId(originOrder.getSkuId());
                    }
                }
                order.setId(Optional.ofNullable(order.getNewOrderId()).orElse(idWorkerService.nextId()));
                if (!haveSidAndTid(order)) {
                    if (!Objects.isNull(originOrder)) {
                        order.setTid(originOrder.getTid());
                        order.setSid(originOrder.getSid());
                        OrderExt orderExt = OrderExtUtils.initOrderExt(staff, originOrder);
                        if (orderExt != null) {
                            orderExt.setId(order.getId());
                            order.setOrderExt(orderExt);
                        }
                    } else {
                        order.setTid(trade.getTid());
                        order.setSid(trade.getSid());
                    }
                }
                // 换商品时继承原商品的orderExt
                if (!Objects.isNull(originOrder) && originOrder.getOrderExt() != null) {
                    OrderExt orderExt = OrderExtUtils.initOrderExt(staff, originOrder);
                    if (orderExt != null) {
                        orderExt.setId(order.getId());
                        order.setOrderExt(orderExt);
                    }
                }
                // KMERP-153008: 换商品子单状态不对问题修复
                setOrderStatus(staff, order, oidMap.get(order.getOid()));
                // 继承原单的异常
                if (!Objects.isNull(originOrder)) {
                    // 新增的系统单可能不存在原始的order
                    OrderExceptUtils.syncOrderExcept(staff, order, originOrder, ExceptEnum.ITEM_CHANGED);
                    OrderExceptUtils.syncOrderExcept(staff, order, originOrder, ExceptEnum.GX_ITEM_CHANGE_EXCEPT);
                    OrderExceptUtils.syncOrderExcept(staff,order,originOrder,ExceptEnum.SMALL_REFUND_EXCEPT);
                }
                order.setOrderOpeart(OrderOpeartEnum.INSERT);
                order.setOrigin(originOrder);
                tradeUpdate.orderModifyLogs.add(OrderModifyLogUtils.build(order, OrderModifyLogTypeEnum.MODIFY_ITEM));
                tradeUpdate.inserts.add(order);
                gxTradeItemModifyBusiness.addGxItemEdit(order,origin);//标记供销改商品
            } else {
                Order originOrder = orderMap.get(order.getId());
                Assert.notNull(originOrder, "商品已删除，请刷新页面再试！");
                order.setOrigin(originOrder);
                // KMERP-210677
                if (featureService.checkHasFeature(staff.getCompanyId(), Feature.ONE_EXCHANGE_MANY_INHERIT_PROPERTY_COMPANY_IDS)) {
                    order.setNumIid(originOrder.getNumIid());
                    order.setSkuId(originOrder.getSkuId());
                }
                originOrder.setWarehouseId(origin.getWarehouseId());
                if (!originOrder.canUpdate() && !TradeUtils.ifAllowEditTradeItem(origin, originOrder.getSysStatus())) {//已发货已完成已关闭以及组合商品的子订单不修改Trade
                    orderCopier.copy(originOrder, order);
                    Logs.warn(LogHelper.buildLog(staff, String.format("交易的子订单禁止修改,修改的属性恢复原值[sid=%s,orderId=%s,sysStatus=%s]", order.getSid(), order.getId(), order.getSysStatus())));
                    continue;
                }
                OrderExt originOrderExt = order.getOrderExt();
                order.setOrderOpeart(OrderOpeartEnum.UPDATE);
                order.setSysItemChanged(isSameItem(order, originOrder) ? 0 : 1);
                //商品金额不联动，修改商品是payment金额不变
                order.setOriginPayment(originOrder.getPayment());
                fillOriginOrderInfo(staff, trade, order, originOrder);
                // KMERP-153008: 换商品子单状态不对问题修复
                setOrderStatus(staff, order, originOrder);
                if (trade.getWarehouseId() - origin.getWarehouseId() != 0) {
                    tradeUpdate.warehouseChanges.add(order);
                } else if (order.getSysItemChanged() == 1) {
                    order.setSysItemChanged(1);
                    OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.RELATION_CHANGED, 0L);
                    OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.UNALLOCATED, 0L);
                    //平台商家编码不变
                    order.setOuterId(null);
                    order.setOuterSkuId(null);
                    order.setNonConsign(0); // KMERP-148078: 换商品后需要清理无需发货标记
                    tradeUpdate.itemChanges.add(order);
                    gxTradeItemModifyBusiness.addGxItemEdit(order, origin);//标记供销改商品
                } else if (order.getItemSysId() > 0 && order.getNum() - originOrder.getNum() != 0) {//已匹配系统商品的子订单更改了商品数量
                    //修改商品数量，平台商家编码不变
                    order.setOuterSkuId(null);
                    tradeUpdate.numChanges.add(order);
                    OrderExt orderExt = order.getOrderExt();
                    if (canUpdateOrderExt(order, originOrder) || orderExtNumUpdate(order, originOrder)) {
                        if (orderExtNumUpdate(order, originOrder)) {
                            OrderExt updateOrderExt = tradeUpdate.getUpdateOrderExt(orderExt);
                           // updateOrderExt.setFirstRecordOrderNum(order.getNum());
                        }
                        if (canUpdateOrderExt(order, originOrder)) {
                            OrderExt updateOrderExt = tradeUpdate.getUpdateOrderExt(orderExt);
                            updateOrderExt.setOrderRemark(orderExt.getOrderRemark());
                        }
                        tradeUpdate.orderExts.add(tradeUpdate.getUpdateOrderExt(orderExt));
                    }
                    gxTradeItemModifyBusiness.addGxItemEdit(order, origin);//标记供销改商品

                    // 修改了数量，存在平台改商品异常
                    if (originOrder.getNum() - order.getNum() != 0 && !isSyncChain) {
                        OrderExceptUtils.updateExceptOrder(staff, originOrder, ExceptEnum.PLAT_MODIFY_ITEM_NUM_EXCEPT, 0L);
                        OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.PLAT_MODIFY_ITEM_NUM_EXCEPT, 0L);
                        OrderModifyLog orderModifyLog = OrderModifyLogUtils.build(originOrder, OrderModifyLogTypeEnum.MODIFY_ITEM_NUM);
                        tradeUpdate.orderModifyLogs.add(orderModifyLog);
                    }
                } else if (canUpdateOrder(originOrder, order)) {
                    //修改实付，平台商家编码不变
                    order.setOuterSkuId(null);
                    OrderExt orderExt = order.getOrderExt();
                    tradeUpdate.updates.add(order);
                  /*  if (canUpdateOrderExt(order, originOrder)) {
                        tradeUpdate.addOrderExtOnlyOrderRemark(order.getOrderExt());
                    }*/
                    if (canUpdateOrderExt(order, originOrder) || orderExtNumUpdate(order, originOrder)) {
                        if (orderExtNumUpdate(order, originOrder)) {
                            OrderExt updateOrderExt = tradeUpdate.getUpdateOrderExt(orderExt);
                           // updateOrderExt.setFirstRecordOrderNum(order.getNum());
                        }
                        if (canUpdateOrderExt(order, originOrder)) {
                            OrderExt updateOrderExt = tradeUpdate.getUpdateOrderExt(orderExt);
                            updateOrderExt.setOrderRemark(orderExt.getOrderRemark());
                        }
                        tradeUpdate.orderExts.add(tradeUpdate.getUpdateOrderExt(orderExt));
                    }

                    // 修改了数量，存在平台改商品异常
                    if (originOrder.getNum() - order.getNum() != 0 && !isSyncChain) {
                        OrderExceptUtils.updateExceptOrder(staff, originOrder, ExceptEnum.PLAT_MODIFY_ITEM_NUM_EXCEPT, 0L);
                        OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.PLAT_MODIFY_ITEM_NUM_EXCEPT, 0L);
                        OrderModifyLog orderModifyLog = OrderModifyLogUtils.build(originOrder, OrderModifyLogTypeEnum.MODIFY_ITEM_NUM);
                        tradeUpdate.orderModifyLogs.add(orderModifyLog);
                    }

                } else if (canUpdateOrderExt(order, originOrder)) {
                    OrderExt updateOrderExt = tradeUpdate.getUpdateOrderExt(originOrderExt);
                    updateOrderExt.setOrderRemark(originOrderExt.getOrderRemark());
                    updateOrderExt.setAuthorName(originOrderExt.getAuthorName());
                    updateOrderExt.setAuthorId(originOrderExt.getAuthorId());
                    updateOrderExt.setAuthorNo(originOrderExt.getAuthorNo());
                    updateOrderExt.setOrderRemark(originOrderExt.getOrderRemark());
                    updateOrderExt.setCooperationNoJitx(originOrderExt.getCooperationNoJitx());
                    updateOrderExt.setCustomization(originOrderExt.getCustomization());
                    updateOrderExt.setPromiseAcceptTime(originOrderExt.getPromiseAcceptTime());
                    tradeUpdate.orderExts.add(updateOrderExt);
                }

                //订单换店铺的情况下,套件下的单品的taobaoId也需要修改
                if (order.getSysItemChanged() == 0 && originOrder.isSuit(false) && originOrder.getSuits() != null) {
                    if (trade.getUserId() != null && trade.getUserId() - origin.getUserId() != 0) {
                        if (order.getSuits() == null) {
                            order.setSuits(new ArrayList<>(originOrder.getSuits().size()));
                        }
                        for (Order son : originOrder.getSuits()) {
                            son.setUserId(order.getUserId());
                            son.setTaobaoId(order.getTaobaoId());
                            order.getSuits().add(son);
                        }
                    }
                }
            }
        }
        //检查哪些子订单需要删除
        Map<Long, Order> map = OrderUtils.toMap(orders);
        for (Order oo : originOrders) {
            Order order = map.get(oo.getId());
            if (order == null) { //原先存在现在不存在
                //判断能否删除
                if (canDelete(staff, oo, tradeConfig)) {
                    oo.setOldStockNum(oo.getStockNum());
                    oo.setWarehouseId(origin.getWarehouseId());
                    oo.setEnableStatus(0);
                    tradeUpdate.deletes.add(oo);
                    // 删除的订单清除异常
                    OrderExceptUtils.clearOrderExcept(staff, oo);
                    oo.setOrderOpeart(OrderOpeartEnum.DELETE);
                    oo.setWarehouseId(origin.getWarehouseId());
                } else {// 已发货已完成已关闭的子订单不能删除,此处加入orders中是为了计算订单金额之用
                    orders.add(oo);
                }
            }
        }
        if (!isOsa && !isSyncChain) {
            // KMERP-221935
            if (Objects.nonNull(tradeUpdate.inserts) && !tradeUpdate.inserts.isEmpty()) {
                tradeUpdate.orderModifyLogs.addAll(OrderModifyLogUtils.build(tradeUpdate.inserts, OrderModifyLogTypeEnum.ORDER_ADD));
            }
            if (Objects.nonNull(tradeUpdate.itemChanges) && !tradeUpdate.itemChanges.isEmpty()) {
                tradeUpdate.orderModifyLogs.addAll(OrderModifyLogUtils.build(tradeUpdate.itemChanges, OrderModifyLogTypeEnum.ORDER_CHANGE));
            }
        }
        mapInsertOrder4Update(tradeUpdate);
        return tradeUpdate;
    }

    // KMERP-256472: 如果存在oid相同的order，取非交易关闭的Order。（PDD平台，平台商品一样，即使是不同订单，oid也是一样的）
    private Map<Long, Order> getOrderMap(List<Order> orders) {
        Map<Long, Order> map = new HashMap<>(orders.size(), 1);
        for (Order order : orders) {
            Order oldOrder = map.put(order.getOid(), order);
            if (Objects.nonNull(oldOrder)
                    && Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())
                    && !Trade.SYS_STATUS_CLOSED.equals(oldOrder.getSysStatus())) {
                map.put(oldOrder.getOid(), oldOrder);
            }
        }
        return map;
    }


    /**
     * 将需要更新的系统订单，设置到原始订单中，主要设置修改后订单的库存状态,包括新增的子订单以及删除的子订单以及修改的子订单
     *
     * @param staff    职工信息
     * @param newTrade 新的订单
     * @param origin   原始订单
     */
    public void handleReplaceDate4Update(Staff staff, ReplaceItemData replaceData, Trade newTrade, Trade origin) {
        try {
            //更改商品、更改数量、删除商品
            Order itemChange = null;
            Order numChange = null;
            List<Order> deleteChanges = new ArrayList<>();

            TradeConfig tradeConfig = replaceData.getTradeConfig();
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("进入TradeUpdateBusiness.handleReplaceDate4Update,原始订单:%s,订单:%s,是否开启覆盖模式:getCoverSysStatus=%s,getAllowRemoveItem=%s", origin.getSid(), newTrade.getSid(), tradeConfig.getCoverSysStatus(), tradeConfig.getAllowRemoveItem())));
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("进入TradeUpdateBusiness.handleReplaceDate4Update,sid:%s,原始订单金额:payment:%s payAmount:%s postFee:%s discountFee:%s,订单金额:payment:%s payAmount:%s postFee:%s discountFee:%s,", newTrade.getSid(), origin.getPayment(), origin.getPayAmount(), origin.getPostFee(), origin.getDiscountFee(), newTrade.getPayment(), newTrade.getPayAmount(), newTrade.getPostFee(), newTrade.getDiscountFee())));
            fillOriginTradeInfo(staff, newTrade, origin);
            List<Order> newOrders = TradeUtils.getOrders4Trade(newTrade);
            List<Order> originOrders = TradeUtils.getOrders4Trade(origin);
            Map<Long, Order> orderMap = OrderUtils.toMap(originOrders);
            Assert.isTrue(CollectionUtils.isNotEmpty(newOrders) && newOrders.size() == 1, "订单只能保留一个商品！");
            Assert.isTrue(CollectionUtils.isNotEmpty(originOrders), "商品已删除，请刷新页面再试！");
            boolean isOrderDelete = originOrders.size() > newOrders.size();
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("进入新老订单fill之后的逻辑 newTrade.status:%s---origin.status:%s", newTrade.getStatus(), origin.getStatus())));
            Order newOrder = newOrders.get(0);
            if (StringUtils.isBlank(newOrder.getStatus())) {
                newOrder.setStatus(origin.getStatus());
            }
            if (Objects.isNull(newOrder.getUserId()) || newOrder.getUserId() <= 0) {
                newOrder.setUserId(origin.getUserId());
            }
            modifyParentBusiness.verifyOrder(staff, newOrder, newTrade);
            Order originOrder = orderMap.get(newOrder.getId());
            Assert.notNull(originOrder, "商品已删除，请刷新页面再试！");
            newOrder.setOrigin(originOrder);
            originOrder.setWarehouseId(origin.getWarehouseId());
            newOrder.setOrderOpeart(OrderOpeartEnum.UPDATE);
            newOrder.setSysItemChanged(isSameItem(newOrder, originOrder) ? 0 : 1);
            //商品金额不联动，修改商品是payment金额不变
            newOrder.setOriginPayment(originOrder.getPayment());
            fillOriginOrderInfo(staff, newTrade, newOrder, originOrder);
            // KMERP-153008: 换商品子单状态不对问题修复
            setOrderStatus(staff, newOrder, originOrder);
            if (newOrder.getSysItemChanged() == 1) {
                newOrder.setSysItemChanged(1);
                newOrder.setRelationChanged(0);
                //平台商家编码不变
                newOrder.setOuterId(null);
                newOrder.setOuterSkuId(null);
                newOrder.setNonConsign(0); // KMERP-148078: 换商品后需要清理无需发货标记
                itemChange = newOrder;
            } else if (newOrder.getItemSysId() > 0 && newOrder.getNum() - originOrder.getNum() != 0) {
                //修改商品数量，平台商家编码不变
                newOrder.setOuterSkuId(null);
                numChange = newOrder;
            }
            //检查哪些子订单需要删除
            Iterator<Order> iterator = originOrders.iterator();
            while (iterator.hasNext()) {
                Order oo = iterator.next();
                //原先存在现在不存在
                if (!oo.getId().equals(newOrder.getId())) {
                    //判断能否删除
                    if (canDelete(staff, oo, tradeConfig)) {
                        oo.setOldStockNum(oo.getStockNum());
                        oo.setWarehouseId(origin.getWarehouseId());
                        deleteChanges.add(oo);
                        oo.setOrderOpeart(OrderOpeartEnum.DELETE);
                        oo.setWarehouseId(origin.getWarehouseId());
                        TradeExceptUtils.delOrderExcept(staff, newTrade, oo);
                        iterator.remove();
                    } else {// 已发货已完成已关闭的子订单不能删除,此处加入orders中是为了计算订单金额之用
                        newOrders.add(oo);
                    }
                }
            }
            if (null != itemChange) {
                replaceData.addItemChanges(newTrade, origin, newOrder);
            }
            if (null != numChange) {
                replaceData.addNumChanges(newTrade, origin, newOrder);
            }
            if (CollectionUtils.isNotEmpty(deleteChanges)) {
                deleteChanges.forEach(t -> replaceData.addDeleteChanges(newTrade, origin, t));
            }
        } catch (Exception e) {
            replaceData.getError().put(String.valueOf(newTrade.getSid()), e.getMessage());
        }

    }

    private boolean haveSidAndTid(Order order) {
        return null != order.getSid() && StringUtils.isNotBlank(order.getTid());
    }

    private boolean canUpdateOrderExt(Order order, Order originOrder) {
        if (null == order.getOrderExt()) {
            return false;
        }
        if (null == originOrder.getOrderExt() && null != order.getOrderExt()) {
            return true;
        }
        if (null != originOrder.getOrderExt() && null != order.getOrderExt()) {
            if (null == order.getOrderExt().getOrderRemark() && null == originOrder.getOrderExt().getOrderRemark()) {
                return false;
            } else if (null == order.getOrderExt().getOrderRemark() && null != originOrder.getOrderExt().getOrderRemark()) {
                return true;
            } else if (null != order.getOrderExt().getOrderRemark() && null == originOrder.getOrderExt().getOrderRemark()) {
                return true;
            }
        }
        return null != originOrder.getOrderExt() && null != order.getOrderExt() && !order.getOrderExt().getOrderRemark().equalsIgnoreCase(originOrder.getOrderExt().getOrderRemark());
    }

    private boolean orderExtNumUpdate(Order order, Order originOrder) {
        if (!TradePlatModifyItemNumExceptionBusiness.SUPPORT_PLATFORMS.contains(originOrder.getSource())) {
            return false;
        }
        OrderExt orderExt = originOrder.getOrderExt();
        if (orderExt == null) {
            return false;
        }
        Integer firstRecordOrderNum = orderExt.getFirstRecordOrderNum();
        return firstRecordOrderNum != null && firstRecordOrderNum - order.getNum() != 0;
    }

    private boolean isSameItem(Order order, Order origin) {
        if (order.getOid() != null && origin.getOid() != null && origin.getOid() - order.getOid() != 0) {
            return false;
        }
        if (order.getItemSysId() - origin.getItemSysId() != 0) {
            return false;
        }
        long skuSysId0 = order.getSkuSysId() == null || order.getSkuSysId() < 0L ? 0 : order.getSkuSysId();
        long skuSysId1 = origin.getSkuSysId() == null || origin.getSkuSysId() < 0L ? 0 : origin.getSkuSysId();
        return skuSysId0 - skuSysId1 == 0;
    }

    private void mapInsertOrder4Update(ModifyData tradeUpdate) {
        if (!tradeUpdate.itemChanges.isEmpty() && !tradeUpdate.inserts.isEmpty()) {
            Map<Long, List<Order>> oidInsertOrders = tradeUpdate.inserts.stream().filter(order -> order.getOid() != null).collect(Collectors.groupingBy(Order::getOid));
            for (Order order : tradeUpdate.itemChanges) {
                if (order.getOid() != null) {
                    List<Order> insertsOrders = oidInsertOrders.get(order.getOid());
                    if (CollectionUtils.isNotEmpty(insertsOrders)) {
                        for (Order insertsOrder : insertsOrders) {
                            insertsOrder.setRefundId(order.getRefundId());
                            insertsOrder.setRefundStatus(order.getRefundStatus());
                        }
                    }
                }
            }
        }
    }

    @Transactional
    public void handleTradeUpdate(Staff staff, Trade trade, ModifyData modifyData) {
        //需要初始化商品的子订单
        List<Order> list = new ArrayList<>();
        for (Order order : modifyData.inserts) {
            if (order.isInsert()) {
                modifyParentBusiness.initOrder(staff, order, trade);
            }
            list.add(order);
        }
        list.addAll(modifyData.itemChanges);
        for (Order order : modifyData.warehouseChanges) {
            if (order.getSysItemChanged() == 1) {
                list.add(order);
            }
        }
        modifyParentBusiness.initItems4Trade(staff, list, trade.isOutstock(), false);
        List<Order> updates = Lists.newArrayListWithCapacity(modifyData.updates.size() + modifyData.declareInfoModifieds.size());
        updates.addAll(modifyData.updates);
        updates.addAll(modifyData.declareInfoModifieds);
        updateOrders(staff, trade, updates);
        deleteOrders(staff, trade, modifyData.deletes);
        insertOrders(staff, trade, modifyData.inserts);
        changeWarehouse(staff, trade, modifyData.warehouseChanges);
        changeItemKind(staff, trade, modifyData.itemChanges);

        changeItemNum(staff, trade, modifyData.numChanges);
        if (Boolean.TRUE.equals(trade.getSaveOrderSupplier())) {
            saveOrderSupplier(staff, trade);
        } else {
            syncSupplier(staff, modifyData.updates, modifyData.inserts, modifyData.deletes, modifyData.itemChanges);
        }
        syncOrderItemTag(staff, modifyData.updates, modifyData.inserts, modifyData.deletes, modifyData.itemChanges);

        changeOrderExts(staff, modifyData);
    }

    @Transactional
    public void handleTradeUpdateBatch(Staff staff, ReplaceItemData replaceData) {
        //初始化商品的子订单
        initItems4Trade(staff, replaceData);
        //这个订单无意义，避免空指针
        Trade trade = new TbTrade();
        // 修正更新的trade的异常
        TradeExceptUtils.fillOrderExceptData(staff, replaceData.getUpdateTradeList(), replaceData.getDeleteOrders());
        TradeExceptUtils.fillOrderExceptData(staff, replaceData.getUpdateTradeList(), replaceData.getItemChanges());
        TradeExceptUtils.fillOrderExceptData(staff, replaceData.getUpdateTradeList(), replaceData.getNumChanges());
        deleteOrders(staff, trade, replaceData.getDeleteOrders());
        changeItemKind(staff, trade, replaceData.getItemChanges());
        changeItemNum(staff, trade, replaceData.getNumChanges());
        syncSupplier(staff, null, null, replaceData.getDeleteOrders(), replaceData.getItemChanges());
        syncOrderItemTag(staff, null, null, replaceData.getDeleteOrders(), replaceData.getItemChanges());
    }

    public void initItems4Trade(Staff staff, ReplaceItemData replaceData) {
        List<Order> updatePriceOrder = new ArrayList<>();
        List<Order> noUpdatePriceOrder = new ArrayList<>();
        for (Order newOrder : replaceData.getItemChanges()) {
            Trade origin = replaceData.getOriginTradeMap().get(newOrder.getSid());
            if (origin.isOutstock()) {
                updatePriceOrder.add(newOrder);
            } else {
                noUpdatePriceOrder.add(newOrder);
            }
        }
        if (CollectionUtils.isNotEmpty(updatePriceOrder)) {
            modifyParentBusiness.initItems4Trade(staff, updatePriceOrder, true, false);
        }
        if (CollectionUtils.isNotEmpty(noUpdatePriceOrder)) {
            modifyParentBusiness.initItems4Trade(staff, noUpdatePriceOrder, false, false);
        }
    }

    private void changeOrderExts(Staff staff, ModifyData modifyData) {
        if (CollectionUtils.isNotEmpty(modifyData.orderExts)){
            for (OrderExt temp: modifyData.orderExts){
                tradeItemChangeBusiness.changeOrderRemark(staff,temp.getId(),temp.getOrderRemark());
            }
        }
    }

    private void syncSupplier(Staff staff, List<Order> updates, List<Order> inserts, List<Order> deletes, List<Order> itemChanges) {
        List<Order> updateOrders = new ArrayList<>();
        List<Order> insertOrders = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(updates)) {
            updateOrders.addAll(updates);
        }
        if (CollectionUtils.isNotEmpty(deletes)) {
            // 删除状态补偿 避免某些地方删除 状态还是 1
            deletes.forEach(a -> a.setEnableStatus(0));
            updateOrders.addAll(deletes);
        }
        if (CollectionUtils.isNotEmpty(itemChanges)) {
            updateOrders.addAll(itemChanges);
        }
        if (CollectionUtils.isNotEmpty(inserts)) {
            insertOrders.addAll(inserts);
        }
        orderSupplierService.sync(staff, insertOrders, updateOrders);
    }

    private void saveOrderSupplier(Staff staff, Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        List<TradeOrderSupplier.OrderSupplier> orderSuppliers = orders.stream().filter(o -> o.getEnableStatus() > 0).map(o -> {
            TradeOrderSupplier.OrderSupplier orderSupplier = new TradeOrderSupplier.OrderSupplier();
            orderSupplier.setOrderId(o.getId());
            orderSupplier.setSupplierIds(Optional.ofNullable(o.getSupplierIds()).map(HashSet::new).orElse(null));
            return orderSupplier;
        }).collect(Collectors.toList());

        TradeOrderSupplier tradeOrderSupplier = new TradeOrderSupplier();
        tradeOrderSupplier.setSid(trade.getSid());
        tradeOrderSupplier.setOrderSuppliers(orderSuppliers);

        orderSupplierService.updateOrderSupplier(staff, trade, tradeOrderSupplier);
    }

    private void syncOrderItemTag(Staff staff, List<Order> updates, List<Order> inserts, List<Order> deletes, List<Order> itemChanges) {
        List<Order> orders = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(updates)) {
            orders.addAll(updates);
        }
        if (CollectionUtils.isNotEmpty(deletes)) {
            deletes.forEach(a -> a.setEnableStatus(0));
            orders.addAll(deletes);
        }
        if (CollectionUtils.isNotEmpty(itemChanges)) {
            orders.addAll(itemChanges);
        }
        if (CollectionUtils.isNotEmpty(inserts)) {
            orders.addAll(inserts);
        }
        tradeOrderSyncItemTagFill.syncTradeOrderItemTagByOrders(staff, orders);
    }

    @Transactional
    public void updateOrders(Staff staff, Trade trade, List<Order> orders) {
        if (CollectionUtils.isNotEmpty(orders)) {
            try {
                List<Order> orderList = new ArrayList<>();
                for (Order order : orders) {
                    if (order.isSuit(false) || order.isProcess(false) || order.isGroup(false)) {
                        if (CollectionUtils.isEmpty(order.getSuits())) {
                            order.setSuits(order.getOrigin().getSuits());
                        }
                    }
                    //套件,组合,加工订单需要做金额分摊
                    if ((order.isSuit(false) || order.isProcess(false) || order.isGroup(false))
                            && NumberUtils.str2Double(order.getPayment()) > 0
                            && CollectionUtils.isNotEmpty(order.getSuits())) {
                        orderList.add(order);
                    }
                }
                if (CollectionUtils.isNotEmpty(orderList)) {
                    TradeItemContext tradeItemContext = new TradeItemContext();
                    for (Order order : orderList) {
                        tradeSuitCalculateBusiness.calculate(staff, order, order.getSuits(), tradeItemContext);
                    }
                }
                List<Order> orderLists = OrderUtils.toFullOrderList(orders, false);
                delOrderExcpet(staff, trade, orderLists);
                tbOrderDao.batchUpdate(staff, orderLists);
            } catch (Exception e) {
                Logs.error(LogHelper.buildErrorLog(staff, e, String.format("更新交易子订单失败[sid=%s，orderIds=%s]", trade == null ? "" : trade.getSid(), org.apache.commons.lang3.StringUtils.join(OrderUtils.toIds(orders), ","))), e);
                throw new TradeException("修改子订单失败！");
            }
        }
    }

    @Transactional
    public void deleteOrders(Staff staff, Trade trade, List<Order> orders) {
        if (CollectionUtils.isNotEmpty(orders)) {
            try {
                tbOrderDao.deleteByIds(staff, orders);
                orderStockService.resumeOrderStockLocal(staff, orders, null);
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("交易删除了子订单[sid=%s,orderIds=%s]", trade.getSid(), org.apache.commons.lang3.StringUtils.join(OrderUtils.toIds(orders), ","))));
                orders.forEach(order -> TradeExceptUtils.delOrderExcept(staff, trade, order));
                List<Long> orderIds = orders.stream().map(Order::getId).filter(Objects::nonNull).collect(Collectors.toList());
                List<Long> sids = orders.stream().map(Order::getSid).filter(Objects::nonNull).collect(Collectors.toList());
                tradeExceptBizBusiness.deleteOrderExcept(staff, sids,orderIds);
            } catch (Exception e) {
                Logs.error(LogHelper.buildErrorLog(staff, e, String.format("交易删除子订单失败[sid=%s,orderIds=%s]", trade.getSid(), org.apache.commons.lang3.StringUtils.join(OrderUtils.toIds(orders), ","))), e);
                throw new TradeException("删除子订单失败！");
            }
        }
    }

    @Transactional
    public void insertOrders(Staff staff, Trade trade, List<Order> orders) {
        if (CollectionUtils.isNotEmpty(orders)) {
            try {
                orderStockService.applyOrderStockLocal(staff, orders);
                tbOrderDao.batchInsert(staff, orders);
                orderFixBusiness.fixOrderBelongSid(staff, orders);
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("交易新增了子订单[sid=%s,orderIds=%s]", trade.getSid(), org.apache.commons.lang3.StringUtils.join(OrderUtils.toIds(orders), ","))));
            } catch (Exception e) {
                Logs.error(LogHelper.buildErrorLog(staff, e, String.format("交易新增商品失败[sid=%s,orderIds=%s]", trade.getSid(), org.apache.commons.lang3.StringUtils.join(OrderUtils.toIds(orders)))), e);
                throw new TradeException("新增商品失败！");
            }
        }
    }

    @Transactional
    public void changeWarehouse(Staff staff, Trade trade, List<Order> orders) {
        if (CollectionUtils.isNotEmpty(orders)) {
            try {
                for (Order order : orders) {
                    order.setOldWarehouseId(order.getOrigin().getWarehouseId());
                    if (order.isSuit(false)) {
                        order.setSuits(order.getOrigin().getSuits());
                        resetNum4SuitOrder(order);
                    }
                }
                orderStockService.changeWarehouse(staff, tradeWarehouseContextBusiness.initUpdate(staff, OpEnum.WAREHOUSE_CHANGE,null), orders);
                delOrderExcpet(staff, trade, orders);
                tbOrderDao.batchUpdate(staff, orders);
            } catch (Exception e) {
                Logs.error(LogHelper.buildErrorLog(staff, e, String.format("更换交易仓库失败[sid=%s,orderIds=%s]", trade.getSid(), org.apache.commons.lang3.StringUtils.join(OrderUtils.toIds(orders)))), e);
                throw new TradeException("更换仓库失败！");
            }
        }
    }


    @Transactional
    public void changeItemKind(Staff staff, Trade trade, List<Order> orders) {
        if (orders != null && !orders.isEmpty()) {
            try {
                List<Order> toInserts = new ArrayList<>(), toUpdates = new ArrayList<>();
                List<Order> toApplys = new ArrayList<>(), toResumes = new ArrayList<>(), toChanges = new ArrayList<>();
                for (Order order : orders) {
                    handleItemChangeStock(order, order.getOrigin(), toApplys, toResumes, toChanges, toUpdates, toInserts);
                }
                if (!toChanges.isEmpty()) {
                    orderStockService.changeItemKind(staff, toChanges);
                }
                if (!toResumes.isEmpty()) {
                    orderStockService.resumeOrderStockLocal(staff, toResumes, null);
                }
                if (!toApplys.isEmpty()) {
                    orderStockService.applyOrderStockLocal(staff, toApplys);
                }
                for (Order order : toUpdates) {//换商品时，如果原先是商品对应关系改动需要取消改动
                    OrderExceptUtils.updateExceptOrder(staff, order, ExceptEnum.RELATION_CHANGED, 0L);
                }
                delOrderExcpet(staff, trade, toUpdates);
                tbOrderDao.batchUpdate(staff, toUpdates);

                if (!toInserts.isEmpty()) {
                    tbOrderDao.batchInsert(staff, toInserts);
                    orderFixBusiness.fixOrderBelongSid(staff, toInserts);
                }
                modifyParentBusiness.changeItemKindLog(staff, orders);
            } catch (Exception e) {
                Logs.error(LogHelper.buildErrorLog(staff, e, String.format("更换交易子订单商品失败[sid=%s,orderIds=%s]", trade.getSid(), org.apache.commons.lang3.StringUtils.join(OrderUtils.toIds(orders), ","))), e);
                throw new TradeException("更换商品失败！");
            }
        }
    }

    @Transactional
    public void clearExpressTemplate(Staff staff, Long[] sids) {
        if (sids == null || sids.length == 0) {
            return;
        }
        List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, sids);
        Long[] updateSids = originTrades.stream()
                .filter(originTrade -> !StringUtils.equals(CommonConstants.PLAT_FORM_TYPE_VIPJITX, originTrade.getSubSource()))
                .map(Trade::getSid)
                .toArray(Long[]::new);
        if (updateSids.length == 0) {
            return;
        }
        lockService.locks(tradeLockBusiness.getERPLocks(staff, updateSids), () -> {
            List<Trade> updateTrades = Arrays.stream(updateSids).map(sid -> {
                Trade updateTrade = new TbTrade();
                updateTrade.setSid(sid);
                updateTrade.setTemplateId(-1L);
                updateTrade.setLogisticsCompanyId(0L);
                updateTrade.setTemplateType(0);
                updateTrade.setTemplateName("");
                updateTrade.setOutSid("");
                return updateTrade;
            }).collect(Collectors.toList());

            tradeUpdateService.updateTrades(staff, updateTrades);

            return updateTrades;
        });
    }

    private void resetNum4SuitOrder(Order order) {
        if (Objects.isNull(order) || CollectionUtils.isEmpty(order.getSuits())) {
            return;
        }
        for (Order son : order.getSuits()) {
            son.setOldNum(son.getNum());
            son.setNum((son.getNum() / order.getOldNum()) * order.getNum());
        }
    }

    private void resetNumAndStockNum4GroupOrProcess(Order order) {
        for (Order son : order.getSuits()) {
            son.setNum((son.getNum() / order.getOldNum()) * order.getNum());
            son.setStockNum(son.getNum());
        }
    }

    private void handleItemChangeStock(Order order, Order origin, List<Order> toApplys, List<Order> toResumes, List<Order> toChanges, List<Order> toUpdates, List<Order> toInserts) {
        if (origin.getItemSysId() <= 0) {//原先未匹配,更换了新的系统商品,这种情况只需要申请新商品库存(无论套件还是非套件)
            if (order.getIsVirtual() == 0) {
                toApplys.add(order);
            }
        } else {//原先有系统商品
            if (order.getSuits() != null || origin.getSuits() != null) {
                toResumes.add(origin);
                toApplys.add(order);
            } else {
                if ((order.getIsVirtual() == 0 && origin.getIsVirtual() == 1) || (!order.ifNonConsign() && origin.ifNonConsign())) {//虚拟商品换普通商品
                    toApplys.add(order);
                } else if (order.getIsVirtual() == 1 && origin.getIsVirtual() == 0) {//普通商品换虚拟商品
                    toResumes.add(origin);
                    toApplys.add(order);
                } else if (order.getIsVirtual() == 0) {////普通商品换普通商品
                    toChanges.add(order);
                }
            }
        }
        if (order.getSuits() != null) {//新商品是套件，套件单品做插入操作
            List<Order> suits = order.getSuits();
            for (Order suit : suits) {
                suit.setExceptData(order.getExceptData());
                suit.setSubTradeExceptDatas(order.getSubTradeExceptDatas());
                toInserts.add(suit);
            }
        }
        toUpdates.add(order);
        if (origin.getSuits() != null) {//原先是套件，更换后，原先套件单品需删除
            for (Order o : origin.getSuits()) {
                o.setEnableStatus(0);
                toUpdates.add(o);
            }
        }
    }

    @Transactional
    public void changeItemNum(Staff staff, Trade trade, List<Order> orders) {
        List<Order> updateOrders = null;
        if (orders != null && !orders.isEmpty()) {
            try {
                for (Order order : orders) {
                    if (order.isSuit(false)) {
                        order.setSuits(order.getOrigin().getSuits());
                        resetNum4SuitOrder(order);
                    }
                }
                orderStockService.changeItemNum(staff, orders);
                List<Order> orderList = new ArrayList<>();
                for (Order order : orders) {
                    if (order.isGroup() || order.isProcess()) {
                        Order origin = order.getOrigin();
                        if (origin != null) {
                            order.setSuits(origin.getSuits());
                            resetNumAndStockNum4GroupOrProcess(order);
                        }
                    }

                    //套件,组合,加工订单需要做金额分摊
                    if ((order.isSuit(false) || order.isProcess(false) || order.isGroup(false))
                            && NumberUtils.str2Double(order.getPayment()) > 0 && CollectionUtils.isNotEmpty(order.getSuits())) {
                        orderList.add(order);
                    }
                }
                if (CollectionUtils.isNotEmpty(orderList)) {
                    TradeItemContext tradeItemContext = new TradeItemContext();
                    for (Order order : orderList) {
                        tradeSuitCalculateBusiness.calculate(staff, order, order.getSuits(), tradeItemContext);
                    }
                }
                List<Order> ordersList = OrderUtils.toFullOrderList(orders, false);
                //setOrderSaleFee(staff,ordersList);
                delOrderExcpet(staff, trade, ordersList);
                tbOrderDao.batchUpdate(staff, ordersList);
                changeItemNumLog(staff, orders);
            } catch (Exception e) {
                Logs.error(LogHelper.buildErrorLog(staff, e, String.format("修改交易子订单商品数量失败[sid=%s,orderIds=%s]", trade.getSid(), org.apache.commons.lang3.StringUtils.join(OrderUtils.toIds(orders), ","))), e);
                throw new TradeException("修改商品数量失败！");
            }
        }
    }

    private void changeItemNumLog(Staff staff, List<Order> orders) {
        StringBuilder buf = new StringBuilder();
        for (Order order : orders) {
            if (buf.length() > 0) {
                buf.append(",");
            }
            buf.append("[sid=").append(order.getSid()).append(",orderId=").append(order.getId()).append(",oldNum=").append(order.getOldNum()).append(",newNum=").append(order.getNum());
        }
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("交易子订单修改了商品数量[%s]", buf)));

    }

    private boolean canUpdateOrder(Order originOrder, Order order) {
        return (NumberUtils.str2Double(originOrder.getDivideOrderFee()) - NumberUtils.str2Double(order.getDivideOrderFee()) != 0) || (NumberUtils.str2Double(originOrder.getPayment()) - NumberUtils.str2Double(order.getPayment()) != 0) || (Math.abs(NumberUtils.str2Double(originOrder.getPrice()) - NumberUtils.str2Double(order.getPrice())) > 0.0001) || originOrder.getNum() - order.getNum() != 0;
    }

    /**
     * 判断子订单能否删除
     */
    private boolean canDelete(Staff staff, Order order, TradeConfig tradeConfig) {
        if (CommonConstants.PLAT_FORM_TYPE_NEWFX.equals(order.getSource()) || CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
            return true;
        }
        Assert.isTrue(tradeConfig.getCoverSysStatus() == 0 || tradeConfig.getAllowRemoveItem() == 1, "覆盖模式下不能删除平台商品！");
        return Trade.SYS_STATUS_WAIT_AUDIT.equals(order.getSysStatus()) || Trade.SYS_STATUS_FINISHED_AUDIT.equals(order.getSysStatus());
    }

    protected void fillOriginTradeInfo(Staff staff, Trade trade, Trade origin) {
        trade.setOrigin(origin);
        TradeBuilderUtils.tradeCopierExcept(trade, origin);
        trade.setTid(origin.getTid());
        trade.setExceptData(TradeExceptUtils.getTradeExceptData(origin));
        trade.setCompanyId(origin.getCompanyId());
        if (trade.getTaobaoId() == null) {
            trade.setTaobaoId(origin.getTaobaoId());
        }
        trade.setSource(origin.getSource());
        trade.setSubSource(origin.getSubSource());
        trade.setOldWarehouseId(origin.getWarehouseId());
        trade.setSysStatus(origin.getSysStatus());
        trade.setInsufficientNum(origin.getInsufficientNum());
        trade.setInsufficientRate(origin.getInsufficientRate());
        trade.setMergeSid(Objects.nonNull(origin.getMergeSid()) ? origin.getMergeSid() : -1L);
        TradeExceptUtils.setStockStatus(staff, trade, origin.getStockStatus());
        if (trade.getWarehouseId() == null || trade.getWarehouseId() == 0) {
            trade.setWarehouseId(origin.getWarehouseId());
        }
        trade.setSysStatus(origin.getSysStatus());
        trade.setIsPresell(origin.getIsPresell());
        TradeExceptUtils.syncTradeExcept(staff, trade, origin, ExceptEnum.HALT);
        TradeExceptUtils.syncTradeExcept(staff, trade, origin, ExceptEnum.REFUNDING);
        trade.setType(origin.getType());
        trade.setCreated(origin.getCreated());
        trade.setPayTime(origin.getPayTime());
        trade.setReceiverAddress(replaceUtf8mb4(origin.getSource(), origin.getReceiverAddress()));
        trade.setReceiverName(replaceUtf8mb4(origin.getSource(), origin.getReceiverName()));
        trade.setReceiverMobile(origin.getReceiverMobile());
        trade.setReceiverPhone(origin.getReceiverPhone());
        if (origin.getSellerMemoUpdate() != null) {
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.SELLER_MEMO_UPDATE, origin.getSellerMemoUpdate().longValue());
        }
        TradeExceptUtils.syncTradeExcept(staff, trade, origin, ExceptEnum.BLACK_NICK);
        TradeExceptUtils.syncTradeExcept(staff, trade, origin, ExceptEnum.ADDRESS_CHANGED);
        TradeExceptUtils.syncExceptIds(staff, trade, origin);
        trade.setItemExcep(origin.getItemExcep());
        trade.setTagIds(origin.getTagIds());
        trade.setBelongType(origin.getBelongType());
        trade.setConvertType(origin.getConvertType());
        trade.setSourceId(origin.getSourceId());
        trade.setDestId(origin.getDestId());
        //要更新trade的payment不以前端传递的为准，以原数据库的数据为准 KMERP-56796
        trade.setPayment(origin.getPayment());
        if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, origin)) {
            //合单以原单据实付金额为准
            trade.setPayAmount(origin.getPayAmount());
        }
        //KMERP-57255 不以前端传递的运费，以数据库的为准
        trade.setPostFee(origin.getPostFee());
        //先给个默认值 防止计算的时候空指针
        if (StringUtils.isBlank(trade.getDiscountFee())) {
            trade.setDiscountFee("0");
        }
        trade.setTaxFee(origin.getTaxFee());
        trade.setTotalFee(origin.getTotalFee());
        trade.setMergeSid(-1L);
        if (Objects.nonNull(origin.getMergeSid())) {
            trade.setMergeSid(origin.getMergeSid());
        }
        List<Order> originOrders = TradeUtils.getOrders4Trade(origin);
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isNotEmpty(originOrders) && CollectionUtils.isNotEmpty(orders4Trade)) {
            Map<Long, Order> orderMap = OrderUtils.toMap(originOrders);
            for (Order order : orders4Trade) {
                Order originOrder = orderMap.get(order.getId());
                if (originOrder != null && order.getSid() == null) {
                    order.setSid(originOrder.getSid());
                }
            }
        }

    }

    private String replaceUtf8mb4(String source, String str) {
        if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(source) || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(source)) {
            return str;
        }
        return com.raycloud.dmj.domain.trades.utils.StringUtils.replaceUtf8mb4(str);
    }

    private void fillOriginOrderInfo(Staff staff, Trade trade, Order order, Order origin) {
        order.setSid(origin.getSid());
        order.setTid(origin.getTid());
        order.setOid(origin.getOid());
        order.setTaobaoId(origin.getTaobaoId() > 0 ? origin.getTaobaoId() : trade.getTaobaoId());
        order.setUserId(trade.getUserId());
        order.setCompanyId(origin.getCompanyId());
        order.setOldNum(origin.getNum());
        order.setSource(origin.getSource());
        order.setDiscountFee(origin.getDiscountFee());//设置子订单修改之前的优惠金额,供计算订单优惠金额之用(针对平台子订单,系统子订单会重新计算)
        order.setSysTitle(origin.getSysTitle());//设置子订单商品标题,共库存不足时显示之用
        order.setCost(origin.getCost());
        order.setAdjustFee(origin.getAdjustFee());
        OrderExceptUtils.setStockStatus(staff, order, origin.getStockStatus());
        order.setSysStatus(origin.getSysStatus());
        order.setNumIid(origin.getNumIid());
        order.setSkuId(origin.getSkuId());
        if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
            order.setPicPath(origin.getPicPath());
        }
        order.setWarehouseId(trade.getWarehouseId());
        order.setType(origin.getType());
        order.setCombineId(origin.getCombineId());
        order.setRefundStatus(origin.getRefundStatus());
        order.setRefundId(origin.getRefundId());
        order.setIsVirtual(origin.getIsVirtual());
        order.setNonConsign(origin.getNonConsign());
        OrderExceptUtils.syncOrderExcept(staff,order,origin,ExceptEnum.RELATION_CHANGED);
        order.setInsufficientCanceled(origin.getInsufficientCanceled());
        order.setNetWeight(origin.getNetWeight());
        order.setVolume(origin.getVolume());
        order.setCreated(origin.getCreated());//创建时间
        order.setGiftNum(origin.getGiftNum());
        order.setBelongType(origin.getBelongType());
        order.setConvertType(origin.getConvertType());
        order.setDestId(origin.getDestId());
        order.setSourceId(origin.getSourceId());
        if (OrderUtils.isFxOrMixOrder(order)) {
            order.setStockNum(order.getNum());
        } else {
            order.setStockNum(origin.getStockNum());
        }
        order.setPayTime(origin.getPayTime());

    }

    /**
     * 删除商品时校验是否可删除，一个平台订单下至少保留一个平台商品
     */
    private void checkDelete(Staff staff, Trade trade, Trade origin) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, origin)) {
            Set<Long> platformSids = new HashSet<>();
            for (Order order : orders) {
                //非新增的平台子订单，记下保留下来的平台子订单的tid
                if (order.getOrigin() != null && !CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getOrigin().getSource())) {
                    platformSids.add(order.getOrigin().getSid());
                }
            }
            //收集原始订单下的所有平台子订单的tid
            List<Order> originOrders = TradeUtils.getOrders4Trade(origin);
            Set<Long> originPlatformSids = new HashSet<>();
            for (Order originOrder : originOrders) {
                if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(originOrder.getSource())) {
                    originPlatformSids.add(originOrder.getSid());
                }
            }
            for (Long originPlatformSid : originPlatformSids) {
                Assert.isTrue(platformSids.contains(originPlatformSid), "合单下的每笔平台订单至少需要保留一个平台商品");
            }
        } else {
            if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(origin.getSource())) {
                return;
            }
            int platformOrderNum = 0;//剩下的平台商品数
            for (Order order : orders) {
                String source = order.getSource();
                if (StringUtils.isBlank(source) && order.getOrigin() != null && order.getOrigin().getEnableStatus() != null && order.getOrigin().getEnableStatus() != 0) {
                    source = order.getOrigin().getSource();
                }
                if (StringUtils.isNotBlank(source) && !CommonConstants.PLAT_FORM_TYPE_SYS.equals(source)) {
                    platformOrderNum++;
                }
            }
            Assert.isTrue(platformOrderNum > 0, "平台订单至少需要保留一个平台商品");
        }
    }

    protected Trade validate(Staff staff, Trade toUpdateTrade, List<Trade> originTrades) {
        Assert.isTrue(!originTrades.isEmpty(), String.format("找不到需要修改的订单[sid:%s]", toUpdateTrade.getSid()));
        Trade originTrade = originTrades.get(0);
        gxTradeItemModifyBusiness.validateGxTrade(staff, originTrade);
        if (!TradeUtils.isGxTrade(originTrade) && (toUpdateTrade.getUserId() != null && toUpdateTrade.getUserId() - originTrade.getUserId() != 0)) {
            User user = staff.getUserByUserId(toUpdateTrade.getUserId());
            Assert.notNull(user, String.format("您没有订单%s所属店铺的权限!", toUpdateTrade.getSid()));
            toUpdateTrade.setTaobaoId(user.getTaobaoId());
        }
        Assert.isTrue(TradeUtils.ifAllowEditTradeItem(originTrade, originTrade.getSysStatus()), String.format("非待审核的订单%s不能修改", originTrade.getSid()));
        Assert.isTrue(!ObjectUtils.equals(originTrade.getIsCancel(), 1), String.format("订单已作废，无法修改商品[系统订单号=%s]", originTrade.getSid()));
        return originTrade;
    }

    private void setTraceContent(Staff staff, Trade trade, ModifyData data, boolean isGiftUpdate, boolean isOsa) {
        if (isOsa) {
            return;
        }
        StringBuilder s = new StringBuilder();
        if (isGiftUpdate) {
            if (!data.inserts.isEmpty()) {
                s.append("添加赠品: ").append(data.inserts.stream().map(o -> {
                    return "{商家编码:" + o.getSysOuterId() + ",数量:" +
                            o.getStockNum() + "/" + o.getNum() +
                            "," + GiftRuleUtils.getIsPickChinese(o.getIsPick()) +
                            "}";
                }).collect(Collectors.joining(", ")));

            }
            if (!data.deletes.isEmpty()) {
                s.append(s.length() > 0 ? "; 删除赠品: " : "删除赠品: ").append(data.deletes.stream().map(this::getOuterId).collect(Collectors.joining(", ")));
            }
            trade.getOperations().put(OpEnum.TRADE_UPDATE, s.toString());
            tradeSysLabelBusiness.addTags(staff, Collections.singletonList(trade), OpEnum.TRADE_UPDATE, Collections.singletonList(SystemTags.TAG_CHANGE_ITEM));
        } else {
            if (!data.inserts.isEmpty()) {
                s.append("新增商品: ").append(data.inserts.stream().map(this::buildLog).collect(Collectors.joining(", ")));
            }
            if (!data.deletes.isEmpty()) {
                s.append(s.length() > 0 ? "; 删除商品: " : "删除商品: ").append(data.deletes.stream().map(this::getOuterId).collect(Collectors.joining(", ")));
            }
            if (!data.itemChanges.isEmpty()) {
                s.append(s.length() > 0 ? "; 换商品: " : "换商品: ").append(data.itemChanges.stream().map(o -> buildItemChangeLog(o, o.getOrigin())).collect(Collectors.joining(", ")));
            }
            List<Order> tempList = new ArrayList<>(data.numChanges.size() + data.updates.size());
            tempList.addAll(data.numChanges);
            tempList.addAll(data.updates);
            if (!tempList.isEmpty()) {
                s.append(s.length() > 0 ? "; 改数量/金额: " : "改数量/金额: ").append(tempList.stream().map(o -> buildNumPaymentChangeLog(o, o.getOrigin())).collect(Collectors.joining(", ")));
            }
            if (s.length() > 0) {
                String optStrPre = "";
                if (TradeUtils.isFxTrade(trade) && Objects.equals(MDC.get(TRADE_ITEM_EDIT_FROM_GX), "1")) {
                    optStrPre = "供销商修改订单商品信息,";
                }
                trade.getOperations().put(OpEnum.TRADE_UPDATE, optStrPre + "修改订单, 实付:" + trade.getPayment() + " ; " + s.toString());
                tradeSysLabelBusiness.addTags(staff, Collections.singletonList(trade), OpEnum.TRADE_UPDATE, Collections.singletonList(SystemTags.TAG_CHANGE_ITEM));
            }
            // KMERP-148078: 换商品后需要清理无需发货标记并添加日志
            addRemoveNonConsignTradeTrace(trade, data.itemChanges);
        }
    }

    private void setReplaceTraceContent(Staff staff, ReplaceItemData replaceData) {
        List<Trade> tagTrades = new ArrayList<>();
        for (Trade trade : replaceData.getUpdateTradeList()) {
            StringBuilder s = new StringBuilder();
            Order itemChangeOrder = replaceData.getItemChangesOrderMap().get(trade.getSid());
            Order numChangeOrder = replaceData.getNumChangesOrderMap().get(trade.getSid());
            List<Order> deleteOrders = replaceData.getDeleteOrderMap().get(trade.getSid());
            if (CollectionUtils.isNotEmpty(deleteOrders)) {
                s.append(s.length() > 0 ? "; 删除商品: " : "删除商品: ").append(deleteOrders.stream().map(this::getOuterId).collect(Collectors.joining(", ")));
            }
            if (null != itemChangeOrder) {
                s.append(s.length() > 0 ? "; 换商品: " : "换商品: ").append(buildItemChangeLog(itemChangeOrder, itemChangeOrder.getOrigin()));
                // KMERP-148078: 换商品后需要清理无需发货标记并添加日志
                addRemoveNonConsignTradeTrace(trade, Lists.newArrayList(itemChangeOrder));
            }
            if (null != numChangeOrder) {
                s.append(s.length() > 0 ? "; 改数量/金额: " : "改数量/金额: ").append(buildNumPaymentChangeLog(numChangeOrder, numChangeOrder.getOrigin()));
            }
            if (s.length() > 0) {
                trade.getOperations().put(OpEnum.TRADE_UPDATE, "修改订单, 实付:" + trade.getPayment() + " ; " + s);
                tagTrades.add(trade);

            }
        }
        if (CollectionUtils.isNotEmpty(tagTrades)) {
            tradeSysLabelBusiness.addTags(staff, tagTrades, OpEnum.TRADE_UPDATE, Collections.singletonList(SystemTags.TAG_CHANGE_ITEM));
        }
    }

    private String buildLog(Order order) {
        StringBuilder s = new StringBuilder("{");
        appendOuterId(order, s);
        s.append(", 数量:").append(order.getStockNum()).append("/").append(order.getNum());
        s.append(", 价格:").append(order.getPrice());
        s.append(", 实付:").append(StringUtils.isBlank(order.getPayment()) ? "0.00" : order.getPayment());
        s.append(", 平台实付:").append(StringUtils.isBlank(order.getDivideOrderFee()) ? "0.00" : order.getDivideOrderFee());
        s.append(", 分销金额:").append(StringUtils.isBlank(order.getSaleFee()) ? "0.00" : order.getSaleFee())
                .append("}");
        return s.toString();
    }

    private String buildItemChangeLog(Order order, Order origin) {
        return buildLog(origin) + "->" + buildLog(order);
    }

    private String buildNumPaymentChangeLog(Order order, Order origin) {
        StringBuilder s = new StringBuilder("{");
        appendOuterId(order, s);
        if (order.getNum() - origin.getNum() != 0) {
            s.append(", 数量:").append(origin.getNum()).append("->").append(order.getNum());
        }
        if (!Strings.contentEquals(origin.getPrice(), order.getPrice())) {
            s.append(", 价格:").append(origin.getPrice()).append("->").append(order.getPrice());
        }
        if (!Strings.contentEquals(origin.getPayment(), order.getPayment())) {
            s.append(", 实付:").append(origin.getPayment()).append("->").append(order.getPayment());
        }
        if (!Strings.contentEquals(origin.getDivideOrderFee(), order.getDivideOrderFee())) {
            s.append(", 平台实付:").append(origin.getDivideOrderFee()).append("->").append(order.getDivideOrderFee());
        }
        if (!Strings.contentEquals(origin.getSaleFee(), order.getSaleFee())) {
            s.append(", 分销金额:").append(origin.getSaleFee()).append("->").append(order.getSaleFee());
        }
        return s.append("}").toString();
    }

    private void appendOuterId(Order order, StringBuilder s) {
        if (order.getItemSysId() > 0) {
            s.append("商家编码:");
            if (StringUtils.isNotBlank(order.getSysOuterId())) {
                s.append(order.getSysOuterId());
            } else if (order.getOrigin() != null) {
                s.append(order.getOrigin().getSysOuterId());
            }
        } else {
            s.append("平台商家编码:");
            String outerId = StringUtils.isNotBlank(order.getOuterSkuId()) ? order.getOuterSkuId() : order.getOuterIid();
            if (StringUtils.isNotBlank(outerId) && order.getOrigin() != null) {
                outerId = StringUtils.isNotBlank(order.getOrigin().getOuterSkuId()) ? order.getOrigin().getOuterSkuId() : order.getOrigin().getOuterIid();
            }
            s.append(outerId);
        }
        if (order.getIsPick() != null) {
            s.append(",是否拣选：").append(GiftRuleUtils.getIsPickChinese(order.getIsPick()));
        }
    }

    private String getOuterId(Order order) {
        if (StringUtils.isNotBlank(order.getSysOuterId())) {
            return order.getSysOuterId();
        } else if (StringUtils.isNotBlank(order.getOuterSkuId())) {
            return order.getOuterSkuId();
        } else if (StringUtils.isNotBlank(order.getOuterIid())) {
            return order.getOuterIid();
        }
        return order.getTitle();
    }

    private void setOrderStatus(Staff staff, Order newOrder, Order originOrder) {
        String status = Optional.ofNullable(originOrder).map(Order::getStatus).orElse(null);
        if (StringUtils.isBlank(status)) {
            return;
        }

        if (!Objects.equals(newOrder.getStatus(), status)) {
            Logs.info(LogHelper.buildLog(staff, String.format("换商品时Order状态处理 sid:%s, oid:%s, id:%s, newStatus:%s, oldStatus:%s", newOrder.getSid(), newOrder.getOid(), newOrder.getId(), newOrder.getStatus(), status)));
            newOrder.setStatus(status);
        }
    }

    /**
     * 继承原单的异常
     *
     * @param staff
     * @param updateOrders
     * @param originOrder
     */
    public <T extends Order> void extendOrderExcept(Staff staff, List<T> updateOrders, Order originOrder) {
        for (T order : updateOrders) {
            OrderExceptUtils.syncOrderExcept(staff, order, originOrder, ExceptEnum.ITEM_CHANGED);
            OrderExceptUtils.syncOrderExcept(staff, order, originOrder, ExceptEnum.RELATION_CHANGED);
        }
    }


    private void delOrderExcpet(Staff staff, Trade trade, List<Order> orders) {
        List<Order> collect = orders.stream().filter(order -> Objects.equals(order.getEnableStatus(), 0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        collect.forEach(order -> TradeExceptUtils.delOrderExcept(staff, trade, order));
        List<Long> orderIds = orders.stream().map(Order::getId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> sids = orders.stream().map(Order::getSid).filter(Objects::nonNull).collect(Collectors.toList());
        tradeExceptBizBusiness.deleteOrderExcept(staff, sids,orderIds);
    }
}

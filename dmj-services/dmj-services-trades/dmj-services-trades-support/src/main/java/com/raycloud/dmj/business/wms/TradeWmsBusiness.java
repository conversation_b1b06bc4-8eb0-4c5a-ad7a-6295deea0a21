package com.raycloud.dmj.business.wms;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.TradeWmsEvents;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.stock.StockChangeBusiType;
import com.raycloud.dmj.domain.stock.message.InOutMessage;
import com.raycloud.dmj.domain.trade.config.TradeConfigContext;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.audit.AuditUndoData;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.utils.MapUtils;
import com.raycloud.dmj.domain.wms.bo.WmsTradeBusinessBo;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.stock.IInOutMessageService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.services.utils.TradeWmsUtils.*;

/**
 * Created by yangheng on 17/9/20.
 * 交易和仓储发送数据的工具类
 */
@Service
public class TradeWmsBusiness {

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    IEventCenter eventCenter;

    @Resource
    StockWmsBridgeBusiness stockWmsBridgeBusiness;

    @Resource
    IWarehouseService warehouseService;
    @Resource
    IInOutMessageService inOutMessageService;

    public boolean handleWms(Staff staff) {
        return stockWmsBridgeBusiness.isOpenWms(staff) && !stockWmsBridgeBusiness.isNewWms(staff);
    }

    /**
     * 审核
     */
    public void sendAudit(Staff staff, List<Trade> trades) {
        if (trades == null || trades.size() == 0) {
            return;
        }
        try {
            if (handleWms(staff)) {
                sendAuditOrderIds(staff, filterAudit(staff, trades, warehouseService));
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("订单审核后发送事件到仓储出错,sid=%s", TradeUtils.toSidList(trades))), e);
        }
    }

    public void sendAudit(Staff staff, TradeConfigContext configContext, List<Trade> trades) {
        if (trades == null || trades.size() == 0) {
            return;
        }
        try {
            if (handleWms(staff)) {
                sendAuditOrderIds(staff, filterAudit(staff, configContext, trades, warehouseService));
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("订单审核后发送事件到仓储出错,sid=%s", TradeUtils.toSidList(trades))), e);
        }
    }


    /**
     * 审核
     */
    public void sendAuditOrderIds(Staff staff, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        try {
            if (handleWms(staff)) {
                if (orderIds.size() >= 1) {
                    sendEvent(staff, TradeWmsEvents.EVENT_AUDIT, orderIds);
                }
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("订单审核后发送事件到仓储出错,orderIds=%s", orderIds)), e);
        }
    }

    /**
     * 审核
     */
    public void sendAuditOrders(Staff staff, List<Order> orders) {
        if (orders == null || orders.size() == 0) {
            return;
        }
        List<Order> filter = filterParty3WarehouseOrders(staff, orders.stream().filter(o -> Trade.SYS_STATUS_FINISHED_AUDIT.equals(o.getSysStatus())).collect(Collectors.toList()), warehouseService);
        if (filter.size() == 0) {
            return;
        }
        try {
            if (handleWms(staff)) {
                List<Long> orderIds = new ArrayList<>();
                filterOrderId(staff, filter, orderIds);
                if (orderIds.size() >= 1) {
                    sendEvent(staff, TradeWmsEvents.EVENT_AUDIT, orderIds);
                }
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("发送事件到仓储出错,orderId=%s", OrderUtils.toIdList(filter))), e);
        }
    }

    /**
     * 反审核
     */
    public void sendUnaudit(Staff staff, List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            try {
                if (stockWmsBridgeBusiness.isOpenWms(staff)) {
                    List<Long> orderIds = filterUnAudit(staff, trades, warehouseService);
                    if (orderIds != null && orderIds.size() >= 1) {
                        if (stockWmsBridgeBusiness.isNewWms(staff)) {
                            sendEvent(staff, TradeWmsEvents.EVENT_UN_AUDIT_NEW, orderIds);
                        } else {
                            sendEvent(staff, TradeWmsEvents.EVENT_UN_AUDIT, orderIds);
                        }
                    }
                }
            } catch (Exception e) {
                Logs.error(LogHelper.buildLog(staff, String.format("订单反审核后发送事件到仓储出错,sids=%s", TradeUtils.toSidList(trades))), e);
            }
        }
    }

    /**
     * 还库存（订单作废、平台发货、换商品等）
     */
    public void sendResume(Staff staff, List<Order> orders) {
        if (orders == null || orders.size() == 0) {
            return;
        }
        if (stockWmsBridgeBusiness.isOpenWms(staff)) {
            List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, false, true, OrderUtils.toSidList(orders).toArray(new Long[0]));
            sendResume(staff, trades, orders);
        }
    }

    /**
     * 还库存（订单作废、平台发货、换商品等）
     */
    public void sendResume(Staff staff, List<Trade> trades, List<Order> orders) {
        if (orders == null || orders.size() == 0) {
            return;
        }
        List<Long> sids = OrderUtils.toSidList(orders);
        try {
            if (stockWmsBridgeBusiness.isOpenWms(staff)) {
                List<Long> orderIds = filterResume(staff, sids, trades, orders, warehouseService);
                if (orderIds != null && orderIds.size() >= 1) {
                    if (stockWmsBridgeBusiness.isNewWms(staff)) {
                        sendEvent(staff, TradeWmsEvents.EVENT_UN_AUDIT_NEW, orderIds);
                    } else {
                        sendEvent(staff, TradeWmsEvents.EVENT_UN_AUDIT, orderIds);
                    }
                }
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("订单反审核后发送事件到仓储出错,sids=%s,orderIds=%s", sids, OrderUtils.toIdList(orders))), e);
        }
    }

    /**
     * 取消发货
     */
    public void sendCancelConsign(Staff staff, List<Trade> tradeList, List<Long> orderIds) {
        List<Order> orders = TradeUtils.getOrders4Trade(tradeList);
        filterNonConsignOrders(staff, orders, orderIds);
        if (orderIds == null || orderIds.size() == 0) {
            return;
        }
        try {
            Map<Long, Order> orderMap = MapUtils.toMap(orders, Order::getId);
            if (WmsUtils.isNewWms(staff)) {
                Date date = new Date();
                inOutMessageService.save(staff, orderIds.stream().map(s -> InOutMessage.builder()
                        .companyId(staff.getCompanyId())
                        .created(date)
                        .operateTime(date)
                        .orderNumber(orderMap.getOrDefault(s, new Order()).getSid() == null ? "" : orderMap.getOrDefault(s, new Order()).getSid().toString())
                        .orderId(orderMap.getOrDefault(s, new Order()).getId())
                        .type(StockChangeBusiType.SEND_GOODS)
                        .build()).collect(Collectors.toList()));
            }
            if (stockWmsBridgeBusiness.isOpenWms(staff)) {
                List<Long> sidList = orderIds.stream().map(orderId -> orderMap.getOrDefault(orderId, new Order())).filter(Objects::nonNull).map(Order::getSid).collect(Collectors.toList());
                Map<Long, Trade> tradeSidMap = tradeList.stream().collect(Collectors.toMap(Trade::getSid, Function.identity()));
                Map<Long, Pair<Date, Date>> timeMap = Maps.newHashMap();
                for (Long sid: sidList) {
                    Trade trade = tradeSidMap.get(sid);
                    if (Objects.nonNull(trade)) {
                        timeMap.put(trade.getSid(), Pair.of(trade.getAuditTime(), new Date()));
                    }
                }
                WmsTradeBusinessBo wmsTradeBusinessBo = new WmsTradeBusinessBo();
                wmsTradeBusinessBo.setCancelTradeTimeMap(timeMap);
                if (stockWmsBridgeBusiness.isNewWms(staff)) {
                    sendWmsCancelCongignEvent(staff, TradeWmsEvents.EVENT_CANCEL_CONSIGN_NEW, orderIds, wmsTradeBusinessBo);
                } else {
                    sendWmsCancelCongignEvent(staff, TradeWmsEvents.EVENT_CANCEL_CONSIGN, orderIds, wmsTradeBusinessBo);
                }
            }
            if (stockWmsBridgeBusiness.isOpenWms(staff)) {
                sendPDAEvent(staff, "pda.wms2.trade.cancel.consign", orderIds);
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, String.format("订单取消发货后发送事件到仓储出错,sids=%s", TradeUtils.toSidList(tradeList))), e);
        }
    }

    public void sendWmsCancelCongignEvent(Staff staff, String eventName, List<Long> orderIds, WmsTradeBusinessBo wmsTradeBusinessBo) {
        eventCenter.fireEvent(this, new EventInfo("trade.wms.transfer").setArgs(new Object[]{staff, eventName, orderIds, wmsTradeBusinessBo}), null);
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("交易发送事件到仓储（transfer）[companyId=%s,eventName=%s,orderIds=%s,timeMapKey=%s]", staff.getCompanyId(), eventName, orderIds, Objects.nonNull(wmsTradeBusinessBo) ? JSONObject.toJSONString(wmsTradeBusinessBo) : "")));
    }

    public void sendEvent(Staff staff, String eventName, List<Long> orderIds) {
        eventCenter.fireEvent(this, new EventInfo("trade.wms.transfer").setArgs(new Object[]{staff, eventName, orderIds}), null);
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("交易发送事件到仓储（transfer）[companyId=%s,eventName=%s,orderIds=%s]", staff.getCompanyId(), eventName, orderIds)));
    }

    public void sendPDAEvent(Staff staff, String eventName, List<Long> orderIds) {
        eventCenter.fireEvent(this, new EventInfo(eventName).setArgs(new Object[]{staff, orderIds}), null);
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("交易发送事件到PDA[companyId=%s, eventName=%s, orderIds=%s]", staff.getCompanyId(), eventName, orderIds)));
    }
}

package com.raycloud.dmj.services.trade.item.single;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.trade.item.common.TradeItemFilter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/3/17
 * @description 订单商品过滤
 */
@Service
public class TradeItem2SingleFilter extends TradeItemFilter {

    @Override
    public boolean filter(Staff staff, TradeItemContext itemContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (itemContext.getAudit() == 1 && !TradeStatusUtils.isWaitAudit(trade.getSysStatus())) {
            errorSidMsgMap.computeIfAbsent("非待审核订单不支持转换单品", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s", trade.getSid(), trade.getTid(), trade.getSysStatus()));
            itemContext.getErrorMsg().add(String.format("sid=%s 非待审核订单不支持转换单品", trade.getSid()));
            return true;
        }
        if (TradeUtils.isPlatformFxTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("平台分销订单不支持转单品", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,convertType=%s,belongType=%s", trade.getSid(), trade.getTid(), trade.getConvertType(), trade.getBelongType()));
            itemContext.getErrorMsg().add(String.format("sid=%s 平台分销订单不支持转单品", trade.getSid()));
            return true;
        }
        if (TradeUtils.isTbDfTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("淘宝厂商代发订单不支持转单品", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            itemContext.getErrorMsg().add(String.format("sid=%s 淘宝厂商代发订单不支持转单品", trade.getSid()));
            return true;
        }
        if (TradeUtils.isGxOrMixTrade(trade) && !itemContext.getConfigContext().isIfDmsTradeGxEditNotRelFx()) {
            errorSidMsgMap.computeIfAbsent("系统供销订单不支持转单品", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,convertType=%s,destId=%s,belongType=%s,sourceId=%s", trade.getSid(), trade.getTid(), trade.getConvertType(), trade.getDestId(), trade.getBelongType(), trade.getSourceId()));
            itemContext.getErrorMsg().add(String.format("sid=%s 系统供销订单不支持转单品", trade.getSid()));
            return true;
        }
        if (TradeTypeUtils.isFxgBtasTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("BTAS订单不支持转单品", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSubSource()));
            itemContext.getErrorMsg().add(String.format("sid=%s BTAS订单不支持转单品", trade.getSid()));
            return true;
        }
        if (!staff.isDefaultStaff()) {
            User user = staff.getUserByUserId(trade.getUserId());
            if (user == null && !TradeUtils.isGxOrMixTrade(trade)) {
                errorSidMsgMap.computeIfAbsent("无订单所属店铺权限", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,userId=%s,source=%s", trade.getSid(), trade.getTid(), trade.getUserId(), trade.getSource()));
                itemContext.getErrorMsg().add(String.format("sid=%s 无订单所属店铺权限", trade.getSid()));
                return true;
            }
        }
        return CollectionUtils.isEmpty(getSwitchOrder(staff, itemContext, trade, errorSidMsgMap));
    }

    private List<Order> getSwitchOrder(Staff staff, TradeItemContext itemContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        List<Order> orders = com.raycloud.dmj.domain.trade.utils.TradeUtils.getOrders(staff, trade, itemContext.getMergeSidTradesMap());
        if (CollectionUtils.isEmpty(orders)) {
            return new ArrayList<>();
        }

        List<Order> result = new ArrayList<>();
        for (Order order : orders) {
            if (CollectionUtils.isEmpty(order.getSuits()) //没有单品
                    || !NumberUtils.isEquals(order.getType(), itemContext.getItemType())//类型不匹配
            ) {
                continue;
            }
            if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(order.getSysStatus()) //待付款
                    || OrderUtils.isAfterSendGoods(order) //已发货
            ) {
                continue;
            }
            if ((itemContext.getInsufficient() == 1 && NumberUtils.isEquals(order.getNum(), order.getStockNum()))) {//仅处理缺货的，但是商品不缺货
                continue;
            }

            String sysOuterId = org.apache.commons.lang3.StringUtils.trimToEmpty(order.getSysOuterId());
            if (StringUtils.isNotBlank(sysOuterId)) {
                if (CollectionUtils.isNotEmpty(itemContext.getIncludOuterIds()) && !itemContext.getIncludOuterIds().contains(sysOuterId)) {//指定商家编码
                    continue;
                }
                if (CollectionUtils.isNotEmpty(itemContext.getExcludeOuterIds()) && itemContext.getExcludeOuterIds().contains(sysOuterId)) {//排除商家编码
                    continue;
                }
            }

            if (order.ifNonConsign()) {//无需发货
                continue;
            }
            result.add(order);
        }

        //该订单没有可以转化的套件商品信息，记录错误信息
        if (result.isEmpty()) {
            String msg = String.format("没有可以转换的%s商品", getItemTypeName(itemContext.getItemType()));
            errorSidMsgMap.computeIfAbsent(msg, k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s", trade.getSid(), trade.getTid()));
            itemContext.getErrorMsg().add(String.format("sid=%s " + msg, trade.getSid()));
        }
        return result;
    }

    private static String getItemTypeName(int itemType) {
        if (itemType == 2) {
            return "套件";
        } else if (itemType == 3) {
            return "组合";
        } else if (itemType == 4) {
            return "加工";
        } else {
            return "";
        }
    }

}

package com.raycloud.dmj.services.trade.item.common;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.item.TradeItemConfigUtils;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/17
 * @description 订单商品查询
 */
@Service
public class TradeItemQuery {

    public static final String QUERY_FIELDS = "t.sid, t.tid, t.merge_sid, t.merge_type, t.split_sid, t.split_type, t.user_id, t.sys_status," +
            " t.is_upload, t.convert_type, t.belong_type, t.dest_id, t.source_id, t.source, t.sub_source";

    @Resource
    TbTradeDao tbTradeDao;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    public List<Trade> query(Staff staff, Long[] sids) {
        return TradeUtils.toTrades(tbTradeDao.queryBySids(staff, sids));
    }

    public List<Trade> query(Staff staff, TradeQueryParams params) {
        long start = System.currentTimeMillis();
        params.setFields(QUERY_FIELDS)
                .setBreakQuery(true)
                .setCheckItem(false)
                .setNeedFill(false)
                .setQueryOrder(false)
                .setQueryFlag(1)
                .setPage(new Page(1, TradeItemConfigUtils.getTradeItemLimitNum(staff.getCompanyId())));
        List<Trade> trades = tradeSearchService.backTradeSearchUnfuse(staff, params).getList();
        long end = System.currentTimeMillis();
        if (end - start > 5000) {
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("查询耗时超过5秒，took=%s ms", (end - start))));
        }
        return trades;

    }

}

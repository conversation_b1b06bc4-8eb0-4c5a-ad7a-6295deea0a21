package com.raycloud.dmj.services.trade.split;

import com.raycloud.dmj.business.split.SplitUtils;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.OpVEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.split.*;
import com.raycloud.dmj.domain.trade.type.*;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @ClassName TradeSplitFilter
 * @Description 拆单过滤
 * <AUTHOR>
 * @Date 2023/12/21
 * @Version 1.0
 */
@Service
public class TradeSplitFilter {

    @Resource
    TradeSplitFill tradeSplitFill;

    public void filter(Staff staff, TradeSplitContext context, List<Trade> trades) {
        splitFilterBefore(staff, context, trades);
        tradeSplitFill.splitFill(staff, context, trades);
        splitFilterAfter(staff, context, trades);
    }

    public void splitFilterBefore(Staff staff, TradeSplitContext context, List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            handleGxTrade(staff,trades);
            Map<String, Map<Long, String>> filterSidMsg = new HashMap<>();
            trades.removeIf(trade -> trade.getEnableStatus() == 1 && splitFilterBefore(staff, context, trade, filterSidMsg));
            //记录日志
            TradeSplitLogUtils.printLog(staff, context, filterSidMsg);
        }
    }

    private void handleGxTrade(Staff staff,List<Trade> trades) {
        if(CollectionUtils.isEmpty(trades)){
            return;
        }
        if(trades.stream().anyMatch(TradeUtils::isGxOrMixTrade) && TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()){
            trades.stream().filter(TradeUtils::isGxOrMixTrade).forEach(t->{
                t.addOpV(OpVEnum.TRADE_GX_ITEM_EDIT_NOT_REL_FX);
            });
        }
    }

    public void splitFilterAfter(Staff staff, TradeSplitContext context, List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            Map<String, Map<Long, String>> filterSidMsg = new HashMap<>();
            trades.removeIf(trade -> trade.getEnableStatus() == 1 && splitFilterAfter(staff, context, trade, filterSidMsg));
        }
    }

    /**
     * fill之前的过滤
     */
    private boolean splitFilterBefore(Staff staff, TradeSplitContext context, Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        return filterBeforeCommon(staff, context, trade, filterSidMsg)
                || filterFxgBefore(staff, context, trade, filterSidMsg)
                || filterShopee(context, trade, filterSidMsg)
                || filterYyjk(trade, filterSidMsg)
                || filterVip(trade, filterSidMsg)
                || filterTb(trade, filterSidMsg)
                || filterTmcs(trade, filterSidMsg)
                || filterTmgjzy(trade, filterSidMsg)
                || filterTmyp(trade, filterSidMsg)
                || filterAlibabaLst(trade, filterSidMsg)
                || filterAlibabaIcbu(trade, filterSidMsg)
                || filterPdd(staff, trade, filterSidMsg)
                || filterPddTemu(context, trade, filterSidMsg)
                || filterShein(context, trade, filterSidMsg)
                || filterWxSph(trade, filterSidMsg)
                || filterJoom(trade, filterSidMsg)
                || filterPoison(trade, filterSidMsg)
                ;
    }

    /**
     * fill之前通用过滤
     */
    private boolean filterBeforeCommon(Staff staff, TradeSplitContext context, Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        if (trade.getMergeSid() > 0 && trade.getEnableStatus() != 1) {
            //这里说明合单的主单已经发生变化，这种数据需要刷新页面重新操作
            filterSidMsg.computeIfAbsent("合单的主单已变更不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,isCancel=%s", trade.getSid(), trade.getTid(), trade.getIsCancel()));
            return true;
        }
        if (TradeSplitEnum.SPLIT_WAVE != context.getTradeSplitEnum() && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RELATION_CHANGED)) {
            filterSidMsg.computeIfAbsent(String.format("商品对应关系修改的订单不支持%s", context.getTradeSplitEnum().getMsg()), k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,stockStatus=%s,itemExcep=%s", trade.getSid(), trade.getTid(), trade.getStockStatus(), trade.getItemExcep()));
            return true;
        }
        if (trade.getIsCancel() != 0) {
            filterSidMsg.computeIfAbsent("作废订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,isCancel=%s", trade.getSid(), trade.getTid(), trade.getIsCancel()));
            return true;
        }
        if (TradeExceptUtils.isContainExcept(staff,trade, ExceptEnum.HALT)) {
            filterSidMsg.computeIfAbsent("挂起订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,isCancel=%s", trade.getSid(), trade.getTid(), trade.getIsCancel()));
            return true;
        }
        if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RISK)) {
            filterSidMsg.computeIfAbsent("风控订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,isCancel=%s", trade.getSid(), trade.getTid(), trade.getIsCancel()));
            return true;
        }
        if (TradeUtils.isPlatformFxTrade(trade)) {
            filterSidMsg.computeIfAbsent("平台分销订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,convertType=%s,belongType=%s", trade.getSid(), trade.getTid(), trade.getConvertType(), trade.getBelongType()));
            return true;
        }
        if (TradeSplitEnum.SPLIT_WAVE != context.getTradeSplitEnum()
                && TradeSplitEnum.SPLIT_PICK != context.getTradeSplitEnum()
                && !SplitUtils.checkPackWmsSplitEnum(context.getTradeSplitEnum())
                && TradeSplitEnum.SPLIT_PACK_FORCE != context.getTradeSplitEnum()
                && TradeSplitConstant.FX_MODE != context.getSplitFx2GxNew()
                && TradeSplitConstant.GX_MODE != context.getSplitFx2GxNew()
                && !TradeStatusUtils.isWaitAudit(trade.getSysStatus())) {
            filterSidMsg.computeIfAbsent(String.format("系统状态非待审核不支持%s", context.getTradeSplitEnum().getMsg()), k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,sysStatus=%s", trade.getSid(), trade.getTid(), trade.getSysStatus()));
            return true;
        }

        if (!(TradeSplitConstant.GX_MODE == context.getSplitFx2GxNew() || (trade.hasOpV(OpVEnum.TRADE_GX_ITEM_EDIT_NOT_REL_FX) && Objects.equals(context.getSplitByGx(),1))) && TradeUtils.isGxTrade(trade)) {
            filterSidMsg.computeIfAbsent(String.format("供销订单不支持%s", context.getTradeSplitEnum().getMsg()), k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,convertType=%s,belongType=%s,destId=%s", trade.getSid(), trade.getTid(), trade.getConvertType(), trade.getBelongType(), trade.getDestId()));
            return true;
        }


        return false;
    }

    /**
     * 抖音过滤
     */
    private boolean filterFxgBefore(Staff staff, TradeSplitContext context, Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        if (CommonConstants.PLAT_FORM_TYPE_FXG_DF.equals(trade.getSource())) {
            filterSidMsg.computeIfAbsent("抖音厂商代发订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        if (TradeUtils.isFxgBicTrade(trade, context.getOpenBicQualityTestingShopUserIds())) {
            filterSidMsg.computeIfAbsent("抖音BIC质检订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s,userId=%s,openBicQualityTestingShopUserIds=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource(), trade.getUserId(), context.getOpenBicQualityTestingShopUserIds()));
            return true;
        }
        return false;
    }

    /**
     * 抖音过滤
     */
    private boolean filterFxgAfter(Staff staff, TradeSplitContext context, Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        if (PlatformUtils.isFxgNYuanMPiecesTrade(trade)) {
            filterSidMsg.computeIfAbsent("抖音N元M件订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource()));
        }
        return false;
    }

    /**
     * 虾皮过滤
     */
    private boolean filterShopee(TradeSplitContext context, Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        if (!context.isShopeeSplit() && CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(trade.getSource())) {
            filterSidMsg.computeIfAbsent(String.format("虾皮订单不支持%s", context.getTradeSplitEnum().getMsg()), k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 美团闪购-医药健康过滤
     */
    private boolean filterYyjk(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_YYJK.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("美团闪购-医药健康订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 唯品会的过滤
     */
    private boolean filterVip(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_VIPJIT.equals(trade.getSource()) && CommonConstants.PLAT_FORM_TYPE_VIPJITX.equals(trade.getSubSource())) {
            errorSidMsgMap.computeIfAbsent("唯品会jitx订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource()));
        }
        return false;
    }

    /**
     * 淘宝
     */
    private boolean filterTb(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isCainiaoWarehouseTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("菜鸟仓自流转订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,subSource=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getSubSource()));
            return true;
        }
        return false;
    }

    /**
     * 天猫超市过滤
     */
    private boolean filterTmcs(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_TMCS.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("天猫超市订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 天猫国际直营过滤
     */
    private boolean filterTmgjzy(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_TMGJZY.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("天猫国际直营订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 天猫优品过滤
     */
    private boolean filterTmyp(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_TMYP.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("天猫优品订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 阿里健康过滤
     */
    private boolean filterAlibabaLst(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_ALIBABA_LST.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("阿里零售通订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 阿里巴巴-阿里国际站过滤
     */
    private boolean filterAlibabaIcbu(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("阿里国际站订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * 拼多多
     */
    private boolean filterPdd(Staff staff, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (trade.getItemExcep() != null && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RISK)) {
            errorSidMsgMap.computeIfAbsent("拼多多风控订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * temu(拼多多跨境)过滤
     */
    private boolean filterPddTemu(TradeSplitContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_TEMU.equals(trade.getSource()) && context.getTradeSplitEnum() != TradeSplitEnum.SPLIT_CROSS_BORDER) {
            if(!TradeUtils.isPddTemuTradeCreatedStatus(trade)){
                errorSidMsgMap.computeIfAbsent("拼多多跨境订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,splitEnum=%s", trade.getSid(), trade.getTid(), trade.getSource(), context.getTradeSplitEnum()));
                return true;
            }
        }
        return false;
    }

    /**
     * 希音过滤
     */
    private boolean filterShein(TradeSplitContext context, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_SHEIN.equals(trade.getSource()) && context.getTradeSplitEnum() != TradeSplitEnum.SPLIT_CROSS_BORDER) {
            errorSidMsgMap.computeIfAbsent("希音订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,splitEnum=%s", trade.getSid(), trade.getTid(), trade.getSource(), context.getTradeSplitEnum()));
            return true;
        }
        return false;
    }

    /**
     * 视频号过滤
     */
    private boolean filterWxSph(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (TradeUtils.isWxSphBicTrade(trade)) {
            errorSidMsgMap.computeIfAbsent("视频号质检订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,type=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getType()));
            return true;
        }
        return false;
    }

    /**
     * 得物直发过滤
     */
    private boolean filterPoison(Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        if (TradeTypeEnum.POISION_DIRECT_SPOT_TRADE.getTypeToConfirm().apply(trade)) {
            filterSidMsg.computeIfAbsent("得物直发订单指定了物流不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,logisticsCode=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getTradeExt().getLogisticsCode()));
            return true;
        }
        return false;
    }

    /**
     * joom 过滤
     */
    private boolean filterJoom(Trade trade, Map<String, Map<Long, String>> errorSidMsgMap) {
        if (CommonConstants.PLAT_FORM_TYPE_JOOM.equals(trade.getSource())) {
            errorSidMsgMap.computeIfAbsent("joom订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s", trade.getSid(), trade.getTid(), trade.getSource()));
            return true;
        }
        return false;
    }

    /**
     * fill之后的过滤
     */
    private boolean splitFilterAfter(Staff staff, TradeSplitContext context, Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        return filterAfterCommon(staff, context, trade, filterSidMsg)
                || filterFxgAfter(staff, context, trade, filterSidMsg)
                || filterPddAfter(staff, trade, filterSidMsg)
                || filterLabel(trade, filterSidMsg)
                || filterSmt(trade, filterSidMsg)
                ;
    }

    /**
     * fill之后通用过滤
     */
    private boolean filterAfterCommon(Staff staff, TradeSplitContext context, Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        List<Order> orders = TradeSplitUtils.getOrders(staff, context, trade);
        int orderNum = 0;
        for (Order order : orders) {
            if (TradeSplitEnum.SPLIT_WAVE != context.getTradeSplitEnum() && OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.RELATION_CHANGED)) {
                filterSidMsg.computeIfAbsent("商品对应关系改动异常不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,orderId=%s", trade.getSid(), trade.getTid(), order.getId()));
                return true;
            }
            if (!TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                orderNum += order.getNum();
            }
        }
        //一单一件直接过滤
        if (orderNum == 1) {
            filterSidMsg.computeIfAbsent("一单一件订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s", trade.getSid(), trade.getTid()));
            return true;
        }
        return false;
    }

    /**
     * 拼多多过滤
     */
    private boolean filterPddAfter(Staff staff, Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        if (TypeUtils.hasTradeType(trade, TradeTypeNewEnum.PDD_GLOBAL_CUSTODY)) {
            filterSidMsg.computeIfAbsent("拼多多全境托管订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,type=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getTradeTypeMap()));
            return true;
        }
        return false;
    }

    /**
     * 标签过滤
     */
    private boolean filterLabel(Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        List<TradeTag> tags = trade.getTags();
        if (CollectionUtils.isNotEmpty(tags)) {
            for (TradeTag tag : tags) {
                if (tag.getNotAllowSplit() != null && tag.getNotAllowSplit() == 1) {
                    filterSidMsg.computeIfAbsent("订单含有不允许拆分的标签不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,labelId=%s,labelName=%s", trade.getSid(), trade.getTid(), tag.getId(), tag.getTagName()));
                    return true;
                }
                if (tag.getId() - SystemTags.TAG_JD_JPS.getId() == 0) {
                    filterSidMsg.computeIfAbsent("京品试订单不支持拆单", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,labelId=%s,labelName=%s", trade.getSid(), trade.getTid(), tag.getId(), tag.getTagName()));
                    return true;
                }
            }

        }
        return false;
    }

    /**
     * smt过滤
     */
    public boolean filterSmt(Trade trade, Map<String, Map<Long, String>> filterSidMsg) {
        if (CommonConstants.PLAT_FORM_TYPE_SMTQTG.equals(trade.getSource()) && trade.getTradeExt() != null && PlatformUtils.isSmtCfCreatedShippingOrder(trade.getTradeExt(), trade.getSource())) {
            filterSidMsg.computeIfAbsent("速卖通仓发备货单已创建发货单，请取消发货单后再操作", k -> new HashMap<>()).put(trade.getSid(), String.format("sid=%s,tid=%s,source=%s,extraFieldsMap=%s", trade.getSid(), trade.getTid(), trade.getSource(), trade.getTradeExt().getExtraFieldsMap()));
            return true;
        }
        return false;
    }

    /**
     * 按数量拆分的校验
     *
     * @param staff
     * @param context  上下文
     * @param order    子订单
     * @param splitNum 需要拆分出去的数量
     * @return true：需要过滤 false：不用过滤
     */
    public boolean filterSplitNum(Staff staff, TradeSplitContext context, Order order, int splitNum) {
        if (order.getItemSysId() <= 0) {
            context.getErrorMsg().put(order.getSid() + "", String.format("商品未匹配的子订单不能按数量拆分,orderId=%s,itemSysId=%s", order.getId(), order.getItemSysId()));
            return true;
        }
        if (RefundUtils.isRefundOrder(order)) {
            context.getErrorMsg().put(order.getSid() + "", String.format("退款子订单不能按数量拆分,orderId=%s,refundStatus=%s", order.getId(), order.getRefundStatus()));
            return true;
        }
        if (TradeSplitEnum.SPLIT_WAVE != context.getTradeSplitEnum() && OrderExceptUtils.isContainsExcept(staff, order, ExceptEnum.RELATION_CHANGED)) {
            context.getErrorMsg().put(order.getSid() + "", String.format("商品对应关系改动子订单不能按数量拆分,orderId=%s,relationChanged=%s", order.getId(), order.getRelationChanged()));
            return true;
        }
        if (order.getIsVirtual() != 0) {
            context.getErrorMsg().put(order.getSid() + "", String.format("虚拟商品不能按数量拆分,orderId=%s,isVirtual=%s", order.getId(), order.getIsVirtual()));
            return true;
        }
        if (splitNum > order.getNum() - NumberUtils.nvlInteger(order.getSplitNum(), 0)) {
            context.getErrorMsg().put(order.getSid() + "", String.format("子订单数量无法满足不能按数量拆分,orderId=%s,orderNum=%s,useNum=%s,needSplitNum=%s", order.getId(), order.getNum(), order.getSplitNum(), splitNum));
            return true;
        }
        return false;
    }
}

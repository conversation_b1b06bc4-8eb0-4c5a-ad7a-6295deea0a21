package com.raycloud.dmj.services.trade.item.common;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.Trade;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/3/17
 * @description 订单商品过滤
 */
public abstract class TradeItemFilter {

    public void filter(Staff staff, TradeItemContext itemContext, List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            return;
        }
        Map<String, Map<Long, String>> errorSidMsgMap = new HashMap<>();
        Iterator<Trade> iterator = trades.iterator();
        while (iterator.hasNext()) {
            Trade trade = iterator.next();
            if (trade.getEnableStatus() != 1) {
                iterator.remove();
                continue;
            }
            if (filter(staff, itemContext, trade, errorSidMsgMap)) {
                iterator.remove();
            }
        }
    }

    public abstract boolean filter(Staff staff, TradeItemContext itemContext, Trade trade, Map<String, Map<Long, String>> errorSidMsgMap);

}

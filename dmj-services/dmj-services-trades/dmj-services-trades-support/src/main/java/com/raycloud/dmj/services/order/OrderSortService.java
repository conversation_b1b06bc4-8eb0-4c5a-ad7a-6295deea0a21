package com.raycloud.dmj.services.order;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.Feature;
import com.raycloud.dmj.domain.gift.GiftPromotionMatchTradeLog;
import com.raycloud.dmj.domain.gift.GiftPromotionRule;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.order.IOrderSortService;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.gift.GiftPromotionMatchTradeLogService;
import com.raycloud.dmj.services.gift.GiftPromotionRuleService;
import com.raycloud.dmj.services.gift.utils.GiftRuleUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("orderSortV1Service")
public class OrderSortService implements IOrderSortService {
    @Resource
    public GiftPromotionMatchTradeLogService giftPromotionMatchTradeLogService;

    @Resource
    public GiftPromotionRuleService giftPromotionRuleService;

    @Resource
    public FeatureService featureService;

    @Override
    public void orderSortByGift(Staff staff, List<Trade> trades) {

        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        if (!checkHasFeature(staff)) {
            return;
        }

        List<Order> allOrders = TradeUtils.getOrders4Trade(trades);
        if (CollectionUtils.isEmpty(allOrders)) {
            return;
        }

        Set<Long> sids = allOrders.stream().filter(OrderUtils::isSysGift).map(Order::getSid).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }

        List<GiftPromotionMatchTradeLog> giftMatchLog = giftPromotionMatchTradeLogService.queryBySids(staff, new ArrayList<>(sids));
        if (CollectionUtils.isEmpty(giftMatchLog)) {
            return;
        }
        Map<Long, List<GiftPromotionMatchTradeLog>> giftMatchLogMap = giftMatchLog.stream().collect(Collectors.groupingBy(GiftPromotionMatchTradeLog::getSid));

        Set<Long> giftPromotionIds = giftMatchLog.stream().map(GiftPromotionMatchTradeLog::getGiftPromotionId).collect(Collectors.toSet());
        List<GiftPromotionRule> giftPromotionRules = giftPromotionRuleService.getRuleListWithGiftPromotionIds(staff, new ArrayList<>(giftPromotionIds));
        if (CollectionUtils.isEmpty(giftPromotionRules)) {
            return;
        }
        Map<Long, GiftPromotionRule> giftPromotionRuleMap = giftPromotionRules.stream().collect(Collectors.toMap(GiftPromotionRule::getId, v -> v, (v1, v2) -> v1));

        trades.forEach(trade -> orderSortByGift(trade, giftPromotionRuleMap, giftMatchLogMap.get(trade.getSid())));

    }

    /**
     * 根据赠品排序
     */
    private void orderSortByGift(Trade trade, Map<Long, GiftPromotionRule> giftPromotionRuleMap, List<GiftPromotionMatchTradeLog> giftMatchLogs) {

        if (CollectionUtils.isEmpty(giftMatchLogs)) {
            return;
        }


        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        Map<Long, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getId, v -> v));
        Map<Long, List<Order>> suiteOrderMap = orders.stream().filter(order -> Objects.nonNull(order.getCombineId()) && order.getCombineId() > 0).collect(Collectors.groupingBy(Order::getCombineId));

        List<Long> isAddOrderSids = new ArrayList<>();
        List<Order> sortOrders = new ArrayList<>();
        List<Order> notAddGiftOrders = new ArrayList<>();

        giftMatchLogs.stream().filter(log -> Objects.nonNull(log.getMatchOrderIds()))
                .sorted(Comparator.comparing(GiftPromotionMatchTradeLog::getMatchOrderIds))
                .sorted(Comparator.comparing(log -> log.getMatchOrderIds().length())).forEach(log -> {

                    if (!StringUtils.hasText(log.getMatchOrderIds())) {
                        return;
                    }

                    Long orderId = log.getOrderId();
                    List<Order> giftOrders = new ArrayList<>();

                    Order giftOrder = orderMap.get(orderId);
                    if (!Objects.isNull(giftOrder)) {
                        giftOrders.add(giftOrder);
                    }
                    List<Order> suitOrders = suiteOrderMap.get(orderId);
                    if (!CollectionUtils.isEmpty(suitOrders)) {
                        giftOrders.addAll(suitOrders);
                    }

                    if (CollectionUtils.isEmpty(giftOrders)) {
                        return;
                    }

                    GiftPromotionRule giftPromotionRule = giftPromotionRuleMap.get(log.getGiftPromotionRuleId());
                    if (!GiftRuleUtils.checkGiftPromotionRule(giftPromotionRule) && !isAddOrderSids.contains(orderId)) {
                        notAddGiftOrders.addAll(giftOrders);
                        isAddOrderSids.addAll(giftOrders.stream().map(Order::getId).collect(Collectors.toList()));
                        return;
                    }

                    Set<Long> originOrderIds = Arrays.stream(log.getMatchOrderIds().split(",")).map(Long::parseLong).collect(Collectors.toSet());
                    originOrderIds.forEach(originOrderId -> {
                        if (isAddOrderSids.contains(originOrderId)) {
                            return;
                        }
                        isAddOrderSids.add(originOrderId);

                        Order originOrder = orderMap.get(originOrderId);
                        if (Objects.isNull(originOrder)) {
                            return;
                        }
                        sortOrders.add(originOrder);
                    });
                    isAddOrderSids.addAll(giftOrders.stream().map(Order::getId).collect(Collectors.toList()));
                    sortOrders.addAll(giftOrders);
                });

        List<Order> notAddOriginOrders = orders.stream().filter(order -> !isAddOrderSids.contains(order.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(notAddOriginOrders)) {
            sortOrders.addAll(notAddOriginOrders);
        }

        if (!CollectionUtils.isEmpty(notAddGiftOrders)) {
            sortOrders.addAll(notAddGiftOrders);
        }

        TradeUtils.setOrders(trade, sortOrders);
    }

    /**
     * 检查公司『订单商品根据赠品排序』是否开启
     */
    public boolean checkHasFeature(Staff staff) {
        return featureService.checkHasFeatureByCode(staff.getCompanyId(), Feature.ODER_SORT_BY_GIFT.getCode());
    }
}

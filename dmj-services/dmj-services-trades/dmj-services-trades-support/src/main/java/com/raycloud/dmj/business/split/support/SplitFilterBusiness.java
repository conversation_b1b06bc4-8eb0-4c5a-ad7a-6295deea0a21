package com.raycloud.dmj.business.split.support;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.split.PlatGiftSplitUtils;
import com.raycloud.dmj.business.split.config.SplitConfigFilter;
import com.raycloud.dmj.business.split.filter.ITradeSplitFilterService;
import com.raycloud.dmj.business.trade.RecordQueryBusiness;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.split.SplitNumLimtConfig;
import com.raycloud.dmj.domain.trade.split.TradeSplitConfigTypeEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.platform.trades.TradeGiftDetail;
import com.raycloud.dmj.domain.trade.split.SplitConfigRule;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.split.TradeSplitUtils;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trade.split.GiftFollowMasterContext;
import com.raycloud.dmj.domain.trade.split.TradeSplitConstant;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.platform.trades.GiftRelationship;
import com.raycloud.dmj.platform.PlatformGiftFollowMasterAccess;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.domain.utils.TradeSplitDiamondUtils;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import com.raycloud.dmj.services.trades.TradeException;
import com.raycloud.dmj.services.trades.config.ITradeConfigNewService;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-08-31 14:37
 * @Description SplitFilterBusiness
 */
@Service
public class SplitFilterBusiness {

    private static final TradeCopier<Trade, Trade> tradeCopier = new TradeCopier<>();

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    RecordQueryBusiness recordQueryBusiness;
    @Resource
    IItemServiceWrapper itemServiceWrapper;
    @Resource
    ITradeConfigNewService tradeConfigNewService;

    @Resource
    ITradeSplitFilterService tradeSplitFilterService;
    @Resource
    PlatformGiftFollowMasterAccess platformGiftFollowMasterAccess;


    @Resource
    ITradeTraceService tradeTraceService;
    @Resource
    IItemServiceDubbo itemServiceDubbo;
    @Resource
    TbTradeDao tbTradeDao;
    @Resource
    TradeLocalConfigurable tradeLocalConfig;


    public SplitParams filterRefund(Staff staff, Long[] sids) {
        SplitParams filterData = new SplitParams();
        List<Trade> lockTrades = Lists.newArrayList();
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        for (Trade originTrade : originTrades) {
            List<Order> originOrders = TradeUtils.getOrders4Trade(originTrade);
            List<Order> splitOrders = originOrders.stream().filter(o -> RefundUtils.isRefundOrder(o) && !o.isVirtual() && !o.ifNonConsign()).collect(Collectors.toList());
            if (!splitOrders.isEmpty() && splitOrders.size() < originOrders.size()) {
                filterData.sids.add(originTrade.getSid());
                lockTrades.add(originTrade);
            }
        }
        if (filterData.sids.isEmpty()) {
            throw new TradeException("没有需要拆分的订单！");
        }
        fillLockContainMergeTrade(staff, filterData, lockTrades);
        return filterData;
    }

    public SplitParams getRefundSplitParams(Staff staff, Long[] sids) {
        SplitParams params = new SplitParams();
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        for (Trade originTrade : originTrades) {
            List<Order> originOrders = TradeUtils.getOrders4Trade(originTrade);
            List<Order> splitOrders = originOrders.stream().filter(o -> RefundUtils.isRefundOrder(o) && !o.isVirtual() && !o.ifNonConsign()).collect(Collectors.toList());
            if (!splitOrders.isEmpty() && splitOrders.size() < originOrders.size()) {
                Trade splitTrade = tradeCopier.copy(originTrade, new TbTrade());
                TradeUtils.getOrders4Trade(splitTrade).addAll(splitOrders);
                params.splitDataMap.put(originTrade, Lists.newArrayList(splitTrade));
            }
        }
        if (params.splitDataMap.isEmpty()) {
            throw new TradeException("没有需要拆分的订单！");
        }
        params.splitPlatGiftFollowMainOrder = false;
        tradeSplitFilterService.filter(staff, params);
        return params;
    }

    public SplitParams getRefundAutoSplitParams(Staff staff, Long[] sids) {
        SplitParams params = new SplitParams();
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        for (Trade originTrade : originTrades) {
            if (Trade.SYS_STATUS_WAIT_AUDIT.equals(originTrade.getSysStatus())) {
                List<Order> splitOrders = new ArrayList<>(), leavePlatOrders = new ArrayList<>();
                Map<Long, List<Order>> leaveOrdersMap = fillRefundAuto(originTrade, splitOrders, leavePlatOrders);
                if (!splitOrders.isEmpty() && !leavePlatOrders.isEmpty()) {
                    fillRefundAutoSysOrder(staff, params, originTrade, leaveOrdersMap, splitOrders, leavePlatOrders);
                    Trade splitTrade = tradeCopier.copy(originTrade, new TbTrade());
                    TradeUtils.setOrders(splitTrade, splitOrders);
                    params.splitDataMap.put(originTrade, Lists.newArrayList(splitTrade));
                }
            }
        }
        params.splitPlatGiftFollowMainOrder = false;
        tradeSplitFilterService.filter(staff, params);
        return params;
    }

    private Map<Long, List<Order>> fillRefundAuto(Trade trade, List<Order> splitOrders, List<Order> leavePlatOrders) {
        Map<Long, List<Order>> leaveOrdersMap = new HashMap<>();
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        Set<String> splitPlatOrdersSet = new HashSet<>();
        List<Order> sysOrders = new ArrayList<>();
        for (Order order : orders) {
            if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
                if (RefundUtils.isRefundOrder(order) || Order.REFUND_SUCCESS.equals(order.getRefundStatus())) {
                    splitOrders.add(order);
                    splitPlatOrdersSet.add(order.getTidOidKey());
                } else {
                    leavePlatOrders.add(order);
                    leaveOrdersMap.computeIfAbsent(order.getSid(), k -> new ArrayList<>()).add(order);
                }
            } else {
                sysOrders.add(order);
            }
        }
        for (Order order : sysOrders) {
            if (!splitPlatOrdersSet.isEmpty() && splitPlatOrdersSet.contains(order.getTidOidKey())) {
                splitOrders.add(order);
            } else {
                leaveOrdersMap.computeIfAbsent(order.getSid(), k -> new ArrayList<>()).add(order);
            }
        }
        return leaveOrdersMap;
    }

    private void fillRefundAutoSysOrder(Staff staff, SplitParams params, Trade trade, Map<Long, List<Order>> leaveOrdersMap, List<Order> splitOrders, List<Order> leavePlatOrders) {
        if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
            leaveOrdersMap.forEach((sid, leaveOrders) -> {
                boolean hasLeavePlatOrder = false;
                List<Order> leaveSysOrders = new ArrayList<>();
                for (Order leaveOrder : leaveOrders) {
                    if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(leaveOrder.getSource())) {
                        hasLeavePlatOrder = true;
                        break;
                    } else {
                        leaveSysOrders.add(leaveOrder);
                    }
                }
                if (!hasLeavePlatOrder) {
                    splitOrders.addAll(leaveSysOrders);
                    params.rematchGiftOrderIds.add(splitOrders.get(0).getId());
                    params.rematchGiftOrderIds.add(leavePlatOrders.get(0).getId());
                }
            });
        }
    }

    public SplitParams filterUnmatched(Staff staff, Long[] sids) {
        SplitParams filterData = new SplitParams();
        List<Trade> lockTrades = Lists.newArrayList();
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        for (Trade originTrade : originTrades) {
            List<Order> originOrders = TradeUtils.getOrders4Trade(originTrade);
            List<Order> splitOrders = originOrders.stream().filter(o -> Trade.SYS_STATUS_WAIT_AUDIT.equals(o.getSysStatus()) && o.getItemSysId() <= 0).collect(Collectors.toList());
            if (!splitOrders.isEmpty() && splitOrders.size() < originOrders.size()) {
                filterData.sids.add(originTrade.getSid());
                lockTrades.add(originTrade);
            }
        }
        fillLockContainMergeTrade(staff, filterData, lockTrades);
        return filterData;
    }

    public SplitParams getUnmatchedSplitParams(Staff staff, Long[] sids, boolean throwException) {
        SplitParams params = new SplitParams();
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        for (Trade originTrade : originTrades) {
            List<Order> originOrders = TradeUtils.getOrders4Trade(originTrade);
            List<Order> splitOrders = originOrders.stream().filter(o -> Trade.SYS_STATUS_WAIT_AUDIT.equals(o.getSysStatus()) && o.getItemSysId() <= 0).collect(Collectors.toList());
            if (!splitOrders.isEmpty() && splitOrders.size() < originOrders.size()) {
                Trade splitTrade = tradeCopier.copy(originTrade, new TbTrade());
                TradeUtils.setOrders(splitTrade, splitOrders);
                params.splitDataMap.put(originTrade, Lists.newArrayList(splitTrade));
            }
        }
        // 赠品跟随主品
        tradeSplitFilterService.handlePlatGiftFollowMainOrder(staff, params);
        if (params.splitDataMap.isEmpty()) {
            if (throwException) {
                throw new TradeException("没有需要拆分的订单！");
            } else {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("没有需要拆分的订单，sids：%s", Lists.newArrayList(sids))));
            }
        }
        return params;
    }

    public SplitParams filterInsufficient(Staff staff, Long[] sids, TradeConfig tradeConfig) {
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        //后置锁定真实分配数
        if (staff.openAuditActiveStockRecord()) {
            recordQueryBusiness.fillRealStockNum(staff, TradeUtils.getOrders4Trade(originTrades), tradeConfig.getOpenScalpNotApplyStock(), tradeConfig.getOpenRefundNotApplyStock(), tradeConfig.getAutoUnattainableNotApplyStock());
        }
        //计算拆分数据
        SplitParams params = SplitInsufficientUtils.calculate(staff, originTrades, tradeConfig);
        tradeSplitFilterService.filterVirtual(staff, params);
        fillLock(staff, params);
        return params;
    }

    public SplitParams filterWeight(Staff staff, Long[] sids, Double weight, boolean isContinue) {
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        SplitParams params = SplitWeightUtils.calculate(staff, originTrades, weight, isContinue);
        tradeSplitFilterService.filter(staff, params);
        fillLock(staff, params);
        return params;
    }

    public SplitParams filterConfig(Staff staff, Long[] sids, SplitConfigFilter splitConfig, SplitConfigRule rule, TradeConfig tradeConfig) {
        return filterConfig(staff, sids, splitConfig, rule, tradeConfig, false);
    }

    public SplitParams filterConfig(Staff staff, Long[] sids, SplitConfigFilter splitConfig, SplitConfigRule rule, TradeConfig tradeConfig, boolean splitFx2GxNew) {
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        return filterConfig(staff, originTrades, splitConfig, rule, tradeConfig, splitFx2GxNew);
    }

    public SplitParams filterConfig(Staff staff, List<Trade> originTrades, SplitConfigFilter splitConfig, SplitConfigRule rule, TradeConfig tradeConfig, boolean splitFx2GxNew) {
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        //后置锁定真实分配数
        if ((TradeSplitConfigTypeEnum.INSUFFICIENT.getType() == rule.getSplitType()
                || TradeSplitConfigTypeEnum.INSUFFICIENT_KIND.getType() == rule.getSplitType()
                || TradeSplitConfigTypeEnum.STOCK_RATIO.getType() == rule.getSplitType())
                && staff.openAuditActiveStockRecord()) {
            recordQueryBusiness.fillRealStockNum(staff, TradeUtils.getOrders4Trade(originTrades), tradeConfig.getOpenScalpNotApplyStock(), tradeConfig.getOpenRefundNotApplyStock(), tradeConfig.getAutoUnattainableNotApplyStock());
        }
        originTrades.removeIf(t -> isRemove(staff, t, rule));
        SplitParams params = new SplitParams();
        if (splitFx2GxNew) {
            params.splitFx2GxNew = TradeSplitConstant.FX_AND_GX_MODE;
        }
        if (!originTrades.isEmpty()) {
            fillSellerCatsIfSplittingBySkuCat(staff, params, originTrades, rule);
            splitConfig.calculate(staff, params, originTrades, rule);
            fillLock(staff, params);
        }
        params.splitConfigRule = rule;
        return params;
    }

    public SplitParams filterAutoSplitTrade(Staff staff, List<Trade> originTrades, SplitConfigFilter splitConfig, SplitConfigRule rule, TradeConfig tradeConfig) {
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        originTrades.removeIf(t -> !Trade.SYS_STATUS_WAIT_AUDIT.equals(t.getSysStatus()) || !SplitConfigUtils.isMatchRule(staff, t, rule) || isRemove(staff, t, rule));
        //后置锁定真实分配数
        if ((TradeSplitConfigTypeEnum.INSUFFICIENT.getType() == rule.getSplitType()
                || TradeSplitConfigTypeEnum.INSUFFICIENT_KIND.getType() == rule.getSplitType()
                || TradeSplitConfigTypeEnum.STOCK_RATIO.getType() == rule.getSplitType())
                && staff.openAuditActiveStockRecord()) {
            recordQueryBusiness.fillRealStockNum(staff, TradeUtils.getOrders4Trade(originTrades), tradeConfig.getOpenScalpNotApplyStock(), tradeConfig.getOpenRefundNotApplyStock(), tradeConfig.getAutoUnattainableNotApplyStock());
        }
        SplitParams params = new SplitParams();
        if (StringUtils.isNotBlank(rule.getFxUserIds()) && tradeLocalConfig.isTradeSplitFx2GxCompanyIds(staff.getCompanyId()) && !TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()) {
            params.splitFx2GxNew = TradeSplitConstant.FX_AND_GX_MODE;
        }
        if (!originTrades.isEmpty()) {
            fillSellerCatsIfSplittingBySkuCat(staff, params, originTrades, rule);
            splitConfig.calculate(staff, params, originTrades, rule);
            //处理赠品不单独拆单
            // handleGiftNotAlone(staff,params);
            fillLock(staff, params);
        }
        params.splitConfigRule = rule;
        return params;
    }

    public SplitParams filterInactiveItem(Staff staff, Long[] originSids, Set<String> inactiveItemKeys) {
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, originSids);
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        //计算拆分数据
        SplitParams params = new SplitParams();
        for (Trade originTrade : originTrades) {
            List<Order> splitOrders = TradeUtils.getOrders4Trade(originTrade).stream().filter(order -> TradeSplitUtils.needSplitInactiveItem(staff, order, inactiveItemKeys)).collect(Collectors.toList());
            if (!splitOrders.isEmpty()) {
                Trade splitTrade = tradeCopier.copy(originTrade, new TbTrade());
                TradeUtils.setOrders(splitTrade, splitOrders);
                params.splitDataMap.put(originTrade, Collections.singletonList(splitTrade));
            }
        }
        fillLock(staff, params);
        return params;
    }

    private boolean isRemove(Staff staff, Trade trade, SplitConfigRule rule) {
        //是拆单订单但其配置又不支持拆单继续拆分
        //是合单订单但其配置又不支持合单拆分
        return ((TradeUtils.isSplit(trade) && rule.getSupportSplit() != null && rule.getSupportSplit() == 0 && !Objects.equals(rule.getRuleType(), 1))
                || (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) && rule.getSupportMerge() != null && rule.getSupportMerge() == 0));
    }

    private void fillLock(Staff staff, SplitParams params) {
        if (!params.splitDataMap.isEmpty()) {
            for (Map.Entry<Trade, List<Trade>> entry : params.splitDataMap.entrySet()) {
                params.sids.add(entry.getKey().getSid());
            }
            fillLockContainMergeTrade(staff, params, new ArrayList<>(params.splitDataMap.keySet()));
        }
    }

    private void fillLockContainMergeTrade(Staff staff, SplitParams splitParams, List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            splitParams.locks.addAll(TradeLockBusiness.tids2ERPLocks(staff, tbTradeDao.queryTids(staff, TradeUtils.toSids(trades))));
        }
    }

    public void handleGiftNotAlone(Staff staff, SplitParams params, List<Trade> originTrades) {
        if (params.splitDataMap.isEmpty()) {
            return;
        }
        TradeConfigNew tradeConfigNew = tradeConfigNewService.get(staff, TradeConfigEnum.SUPPORT_GIFTNOT_ALONE);
        if (params.splitDataMap.isEmpty() || !Objects.equals(tradeConfigNew.getConfigValue(), "1")) {
            return;
        }
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(originTrades);
        Map<Long, Order> orderMap = TradeUtils.getOrders4Trade(originTrades).stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        Iterator<Map.Entry<Trade, List<Trade>>> iterator = params.splitDataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Trade, List<Trade>> next = iterator.next();
            List<Trade> splitTrades = next.getValue();
            Long sid = next.getKey().getSid();
            if (CollectionUtils.isEmpty(splitTrades)) {
                continue;
            }
            Trade originTrade = tradeMap.get(sid);
            if (originTrade == null) {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("赠品不单独拆单处理找不到原始订单,原始单可能已被过滤，sid=%s", sid)));
                continue;
            }
            List<Order> originOrderList = TradeUtils.getOrders4Trade(originTrade);
            splitTrades.removeIf(splitTrade -> {
                List<Order> splitOrderList = TradeUtils.getOrders4Trade(splitTrade);
                if (!CollectionUtils.isEmpty(splitOrderList)) {
                    List<Order> originSplitOrders = getOriginSplitOrder(splitOrderList, originOrderList);
                    // 存在全是赠品的子单不拆
                    return isOrderPureGift(originSplitOrders);
                }
                return false;
            });
            // 获取剩余的order
            List<Order> remainOrders = remainOrderPureOrder(originOrderList, splitTrades);
            //如果剩余不拆单都是赠品 取消第一个拆的单子
            if (!CollectionUtils.isEmpty(splitTrades) && isOrderPureGift(remainOrders)) {
                //剩下的都是赠品,判断剩下的赠品都是属于那些sid（可能是合单，order 都分属与不同的sid),根据sid归类
                // 剩下的赠品order的sid
                Set<Long> remainOrderSids = remainOrders.stream().filter(Order::isGift).map(Order::getSid).collect(Collectors.toSet());
                Iterator<Trade> splitTradeIterator = splitTrades.iterator();
                while (splitTradeIterator.hasNext()) {
                    if (CollectionUtils.isEmpty(remainOrderSids)) {
                        break;
                    }
                    Trade splitTrade = splitTradeIterator.next();
                    List<Order> orders4Trade = TradeUtils.getOrders4Trade(splitTrade);
                    List<Long> splitSids = new ArrayList<>();
                    for (Order order : orders4Trade) {
                        Order originOrder = orderMap.get(order.getId());
                        if (originOrder != null) {
                            splitSids.add(originOrder.getSid());
                        }
                    }
                    Set<Long> collect = remainOrderSids.stream().filter(splitSids::contains).collect(Collectors.toSet());
                    // 随机找一个和剩下的都是赠品order的一样的sid的不拆
                    if (!CollectionUtils.isEmpty(collect)) {
                        Logs.ifDebug(LogHelper.buildLog(staff, String.format("剩余不拆单都是赠品，取消第一个拆单，sid=%s", collect)));
                        remainOrderSids.removeAll(collect);
                        splitTradeIterator.remove();
                    }
                }
            }
            if (CollectionUtils.isEmpty(splitTrades)) {
                iterator.remove();
            }
        }
    }

    private List<Order> getOriginSplitOrder(List<Order> splitOrders, List<Order> originOrders) {
        Map<Long, Order> orderMap = originOrders.stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        List<Order> orders = new ArrayList<>();
        for (Order splitOrder : splitOrders) {
            Order order = orderMap.get(splitOrder.getId());
            if (order != null) {
                orders.add(order);
            }
        }
        return orders;
    }

    public void handleGoodsNotAlone(Staff staff, SplitParams params, List<Trade> originTrades) {
        if (params.splitDataMap == null || params.splitDataMap.isEmpty()) {
            return;
        }

        Map<Long, Trade> sidToOrigin = TradeUtils.toMapBySid(originTrades);
        for (Trade trade : params.splitDataMap.keySet()) {
            List<Trade> splittings = params.splitDataMap.get(trade);
            if (splittings == null || splittings.isEmpty()) {
                continue;
            }
            Trade origin = sidToOrigin.get(trade.getSid());
            if (trade.getMergeSid() != null
                    && trade.getMergeSid() > 0
                    && !Objects.equals(trade.getSid(), trade.getMergeSid())
            ) {
                origin = sidToOrigin.get(trade.getMergeSid());
            }
            if (origin == null) {
                continue;
            }
            SplitNotAlone splitNotAlone = new SplitNotAlone(true, true, false);
            splitNotAlone.init(staff, origin, splittings);
            splitNotAlone.handler();
            if (splittings.isEmpty()) {
                Logs.warn(LogHelper.buildLog(staff, String.format("订单拆分分组后过滤,没有拆单分组,不做拆分,sid: %s", origin.getSid())));
            }
        }
    }

    public void handleRefundOrderSplitNum(Staff staff, SplitParams params, List<Trade> originTrades) {
        if (params.splitDataMap == null || params.splitDataMap.isEmpty()) {
            return;
        }

        Map<Long, Trade> sidToOrigin = TradeUtils.toMapBySid(originTrades);
        List<TradeTrace> traces = new ArrayList<>();
        for (Trade trade : params.splitDataMap.keySet()) {
            List<Trade> splittings = params.splitDataMap.get(trade);
            if (splittings == null || splittings.isEmpty()) {
                continue;
            }
            Trade origin = sidToOrigin.get(trade.getSid());
            if (trade.getMergeSid() != null
                    && trade.getMergeSid() > 0
                    && !Objects.equals(trade.getSid(), trade.getMergeSid())
            ) {
                origin = sidToOrigin.get(trade.getMergeSid());
            }
            if (origin == null) {
                continue;
            }

            List<Order> originOrders = TradeUtils.getOrders4Trade(origin);
            Map<Long, List<Order>> orderIdToSplitOrder = new HashMap<>(originOrders.size());
            Map<Long, Integer> orderIdToNum = new HashMap<>(originOrders.size());
            for (Order originOrder : originOrders) {
                if (!RefundUtils.isRefundOrder(originOrder)) {
                    continue;
                }
                orderIdToSplitOrder.put(originOrder.getId(), new ArrayList<>());
                orderIdToNum.put(originOrder.getId(), originOrder.getNum());
            }
            if (orderIdToSplitOrder.isEmpty()) {
                continue;
            }

            for (Trade splitting : splittings) {
                List<Order> splittingOrders = TradeUtils.getOrders4Trade(splitting);
                for (Order splittingOrder : splittingOrders) {
                    if (!orderIdToSplitOrder.containsKey(splittingOrder.getId())) {
                        continue;
                    }
                    orderIdToSplitOrder.get(splittingOrder.getId()).add(splittingOrder);
                }
            }

            boolean hasSplitNum = orderIdToSplitOrder.entrySet().stream().anyMatch(entry -> {
                        List<Order> value = entry.getValue();
                        if (value.isEmpty()) {
                            return false;
                        }
                        if (value.size() > 1) {
                            return true;
                        }

                        Order order = value.get(0);
                        Integer num = orderIdToNum.get(order.getId());
                        Integer splitNum = order.getNum();
                        return splitNum > 0 && !Objects.equals(num, order.getNum());
                    }
            );
            if (hasSplitNum) {
                splittings.clear();
                TradeTrace trace = TradeTraceUtils.createTradeTraceWithTrade(staff, origin,
                        "订单拆分", "系统", new Date(), "退款订单中有子订单做数量拆分, 订单不做拆分");
                traces.add(trace);
            }
        }

        if (traces.isEmpty()) {
            return;
        }
        tradeTraceService.batchAddTradeTrace(staff, traces);
    }

    private boolean isOrderPureGift(List<Order> remainOrders) {
        // 可能是合单按sid分组
        Map<Long, List<Order>> collect = remainOrders.stream().collect(Collectors.groupingBy(Order::getSid));
        Set<Map.Entry<Long, List<Order>>> entries = collect.entrySet();
        for (Map.Entry<Long, List<Order>> entry : entries) {
            List<Order> value = entry.getValue();
            boolean b = value.stream().allMatch(Order::isGift);
            if (b) {
                return true;
            }
        }
        return false;
    }


    /**
     * 剩余的order
     *
     * @return
     */
    private List<Order> remainOrderPureOrder(List<Order> originOrders, List<Trade> splitTrades) {
        List<Order> splitOrders = new ArrayList<>();
        List<Order> remainOrders = new ArrayList<>();
        Map<Long, Integer> splitOrderIdNumMap = new HashMap<>();
        for (Trade trade : splitTrades) {
            splitOrders.addAll(TradeUtils.getOrders4Trade(trade));
        }
        for (Order splitOrder : splitOrders) {
            splitOrderIdNumMap.merge(splitOrder.getId(), splitOrder.getNum(), Integer::sum);
        }
        OrderUtils.toMapById(splitOrders);
        for (Order originOrder : originOrders) {
            //分类拆分， 指定了数量时，原order num的值可能会为0 ，与拆分的差值小于0
            if (splitOrderIdNumMap.get(originOrder.getId()) == null || originOrder.getNum() - splitOrderIdNumMap.get(originOrder.getId()) > 0) {
                remainOrders.add(originOrder);
            }
        }
        return remainOrders;
    }

    public SplitParams filterDefaultBoxRule(Staff staff, Long[] sids, Boolean isIncludeSplit, Boolean isIncludeMerge) {
        List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, sids);
        tradeList = tradeList.stream().filter(t -> {
            if (!isIncludeSplit && TradeUtils.isSplit(t)) {
                return false;
            }
            Long mergeSid = t.getMergeSid();
            return isIncludeMerge || (mergeSid == null || mergeSid <= 0);
        }).collect(Collectors.toList());
        tradeList = tradeSplitFilterService.filterTrades(staff, tradeList, false);
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("isIncludeSplit=%s,isIncludeMerge=%s ,默认箱柜拆分的订单sids=%s!!", isIncludeSplit, isIncludeMerge, TradeUtils.toSidSet(tradeList))));
        List<Trade> removeTrades = new ArrayList<>();
        for (Trade trade : tradeList) {
            if ((!isIncludeSplit && trade.getSplitSid() > 0L)
                    || (!isIncludeMerge && trade.getMergeSid() > 0L)) {
                removeTrades.add(trade);
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(removeTrades)) {
            tradeList.removeAll(removeTrades);
        }
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("过滤后，默认箱柜拆分的订单sids=%s!!", TradeUtils.toSidSet(tradeList))));
        Map<String, Long> boxRuleMap = itemServiceWrapper.queryDefaultItemBox(staff, TradeUtils.getOrders4Trade(tradeList));
        Assert.isTrue(MapUtils.isNotEmpty(boxRuleMap), "默认箱规不存在！");
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("仓储返回的默认箱规:%s!!", JSON.toJSONString(boxRuleMap))));
        SplitParams params = new SplitParams();
        SplitConfigDefaultBoxRuleUtils.calculate(staff, params, tradeList, boxRuleMap);
        tradeSplitFilterService.filter(staff, params);
        fillLock(staff, params);
        return params;
    }

    public SplitParams filterFxgAutoLz(Staff staff, Long... sids) {
        SplitParams filterData = new SplitParams();
        List<Trade> lockTrades = Lists.newArrayList();
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        originTrades = tradeSplitFilterService.filterTrades(staff, originTrades, false);
        for (Trade originTrade : originTrades) {
            List<Trade> splitTrades = buildFxgAutoLzSplitTrades(staff, originTrade);
            if (!CollectionUtils.isEmpty(splitTrades)) {
                filterData.sids.add(originTrade.getSid());
                lockTrades.add(originTrade);
            }
        }
        fillLockContainMergeTrade(staff, filterData, lockTrades);
        return filterData;
    }

    public SplitParams getFxgAutoLz(Staff staff, Long... sids) {
        SplitParams params = new SplitParams();
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, sids);
        for (Trade originTrade : originTrades) {
            List<Trade> splitTrades = buildFxgAutoLzSplitTrades(staff, originTrade);
            if (!CollectionUtils.isEmpty(splitTrades)) {
                params.splitDataMap.put(originTrade, splitTrades);
            }
        }
        return params;
    }

    private List<Trade> buildFxgAutoLzSplitTrades(Staff staff, Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)) {
            return null;
        }
        // 取出 orderExt.customization 中 warehouseType = fxg_yun_warehouse 数据，并根据 preWarehouseCode 分组， 其余数据另分一组
        Map<String, List<Order>> orderGroups = orders.stream().collect(Collectors.groupingBy(order -> PlatformUtils.getFxgAutoLzGroup(staff, order)));
        // 只有一个组，无需拆单
        if (orderGroups.size() == 1) {
            return null;
        }
        List<Trade> splitTrades = orderGroups.values().stream().map(orderList -> {
            TbTrade splitTrade = new TbTrade();
            TradeUtils.setOrders(splitTrade, orderList);
            return splitTrade;
        }).collect(Collectors.toList());
        splitTrades.remove(0);
        return splitTrades;
    }


    /**
     * 判断拆单数量是否超过最大数量
     */
    public Set<Long> filterSplitMaxNum(Staff staff, SplitParams params) {
        Set<Long> notSplitSids = new HashSet<>();

        boolean b = TradeSplitDiamondUtils.openSplitLimtNumConfig(staff);
        if (!b) {
            return notSplitSids;
        }
        Map<Trade, List<Trade>> splitDataMap = params.splitDataMap;
        Map<Trade, Long> splitTradeNumMap = new HashMap<>();
        Map<Long, Long> splitNumMap = new HashMap<>();
        SplitNumLimtConfig splitNumLimtConfig = TradeSplitDiamondUtils.getSplitNumLimtConfig(staff);
        Long maxNum = splitNumLimtConfig.getMaxNum();
        long acrossMaxNum = Optional.ofNullable(splitNumLimtConfig.getAcrossMaxNum()).orElse(0L);
        if (MapUtils.isNotEmpty(splitDataMap)) {
            Set<Map.Entry<Trade, List<Trade>>> entries = splitDataMap.entrySet();
            for (Map.Entry<Trade, List<Trade>> entry : entries) {
                int splitNum = entry.getValue().size();
                Long sid = entry.getKey().getSid();
                if (splitNum > maxNum) {
                    // 大于限制数量不拆
                    notSplitSids.add(sid);
                    splitNumMap.put(sid, (long) splitNum);
                } else {
                    //当maxNum 和splitNum 相差过大时可以不用校验，避免拆单慢
                    if ((maxNum - splitNum) < acrossMaxNum) {
                        // 非拆单的不需要再去查数据库校验
                        if (entry.getKey().getSplitSid() != null && entry.getKey().getSplitSid() > 0) {
                            splitTradeNumMap.put(entry.getKey(), (long) splitNum);
                        }
                    }
                }
            }
        }
        if (MapUtils.isNotEmpty(splitTradeNumMap)) {
            Set<Trade> trades = splitTradeNumMap.keySet();
            List<Long> sids = new ArrayList<>();
            for (Trade trade : trades) {
                if (trade.getSplitSid() != null && trade.getSplitSid() > 0) {
                    sids.add(trade.getSplitSid());
                } else {
                    sids.add(trade.getSid());
                }
            }
            List<Trade> tradeList = tradeSearchService.queryBySplitSids(staff, false, sids.toArray(new Long[0]));
            Map<Long, List<Trade>> collect = tradeList.stream().filter(tradeSplit -> tradeSplit.getSplitSid() != null && tradeSplit.getSplitSid() > 0).collect(Collectors.groupingBy(Trade::getSplitSid));
            Set<Map.Entry<Trade, Long>> entries = splitTradeNumMap.entrySet();
            for (Map.Entry<Trade, Long> entry : entries) {
                Long splitNum = entry.getValue();
                Long sid = entry.getKey().getSid();
                Long splitSid = entry.getKey().getSplitSid() != null && entry.getKey().getSplitSid() > 0 ? entry.getKey().getSplitSid() : sid;
                List<Trade> splitTrades = Optional.ofNullable(collect.get(splitSid)).orElse(new ArrayList<>());
                int haveSplitSize = splitTrades.size();
                long sum = splitNum + haveSplitSize;
                splitNumMap.put(sid, sum);
                if (maxNum < sum) {
                    notSplitSids.add(sid);
                }
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(notSplitSids)) {
            ListUtils.splitList(new ArrayList<>(notSplitSids), 10).forEach(e -> Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s 超过拆单的数量%s 最大数量限制 %s 不允许拆单!", e, splitNumMap, maxNum))));
        }
        return notSplitSids;
    }

    private void fillSellerCatsIfSplittingBySkuCat(Staff staff, SplitParams params, List<Trade> originTrades, SplitConfigRule rule) {
        if (!Objects.equals(rule.getSplitType(), TradeSplitConfigTypeEnum.SKU_CAT.getType())
                || originTrades == null
                || originTrades.isEmpty()
        ) {
            return;
        }

        Set<Long> sysItemIds = new HashSet<>(originTrades.size()),
                sysSkuIds = new HashSet<>(originTrades.size());

        for (Trade originTrade : originTrades) {
            List<Order> orders = TradeUtils.getOrders4Trade(originTrade);
            for (Order order : orders) {
                String cidStr = order.getCids();
                if ((order.getSkuSysId() == null || order.getSkuSysId() < 1)
                        && (cidStr == null || cidStr.isEmpty())
                ) {
                    sysItemIds.add(order.getItemSysId());
                }
                if (order.getSkuSysId() != null && order.getSkuSysId() > 0) {
                    sysSkuIds.add(order.getSkuSysId());
                }
            }
        }

        Map<Long, String> pureItemOrSkuSysIdToSellerCatIds = new HashMap<>(sysItemIds.size() + sysSkuIds.size());
        if (!sysItemIds.isEmpty()) {
            List<DmjItem> dmjItems = itemServiceDubbo.queryItemWithSysItemId(staff, new ArrayList<>(sysItemIds));
            for (DmjItem dmjItem : dmjItems) {
                pureItemOrSkuSysIdToSellerCatIds.put(dmjItem.getSysItemId(), dmjItem.getSellerCids());
            }
        }
        if (!sysSkuIds.isEmpty()) {
            List<DmjSku> dmjSkus = itemServiceDubbo.querySkuWithSysSkuId(staff, new ArrayList<>(sysSkuIds));
            for (DmjSku sku : dmjSkus) {
                pureItemOrSkuSysIdToSellerCatIds.put(sku.getSysSkuId(), sku.getSellerCids());
            }
        }
        params.pureItemOrSkuSysIdToSellerCatIds = pureItemOrSkuSysIdToSellerCatIds;
    }


    /**
     * 拆单平台赠品跟随主品
     */
    public void handlePlatGiftFollowMainOrder(Staff staff, SplitParams params, List<Trade> originTrades) {
        if (params.splitDataMap.isEmpty()) {
            return;
        }
        TradeConfigNew tradeConfigNew = tradeConfigNewService.get(staff, TradeConfigEnum.SPLIT_PLAT_GIFT_FOLLOW_MAIN_ORDER);
        if (params.splitDataMap.isEmpty() || !Objects.equals(tradeConfigNew.getConfigValue(), "1")) {
            return;
        }
        for (Trade trade : originTrades) {
            TradeUtils.getOrders4Trade(trade).forEach(order -> order.setUserId(trade.getUserId()));
        }
        Map<Long, Trade> originTradeMap = originTrades.stream().collect(Collectors.toMap(Trade::getSid, Function.identity()));
        List<String> logs = new ArrayList<>();
        List<Order> originOrders = TradeUtils.getOrders4Trade(originTrades);
        GiftFollowMasterContext giftFollowMasterContext = PlatGiftSplitUtils.initGiftFollowMasterContext(staff, originOrders);
        List<Order> hasNoGiftRelationOrders = giftFollowMasterContext.getHasNoGiftRelationOrders();
        if (!CollectionUtils.isEmpty(hasNoGiftRelationOrders)) {
            Map<Long, Set<String>> userTidsMap = new HashMap<>();
            for (Order order : hasNoGiftRelationOrders) {
                userTidsMap.computeIfAbsent(order.getUserId(), k -> new HashSet<>()).add(order.getTid());
            }
            // 系统没有主品信息的，去多平台查一遍
            if (MapUtils.isNotEmpty(userTidsMap)) {
                fillPlatGiftFollowMasterOrder(staff, userTidsMap, logs, giftFollowMasterContext);
                // 重新初始化部分信息
                PlatGiftSplitUtils.reInitGiftOrderIdsFollowMasterOrderMap(staff, giftFollowMasterContext);
            }
        }
        // 移除有主品映射关系的平台赠品
        PlatGiftSplitUtils.removeGiftSplitOrder(staff, params, giftFollowMasterContext);
        Set<Map.Entry<Trade, List<Trade>>> entries = params.splitDataMap.entrySet();
        List<Long> hasAllocateOrderIds = new ArrayList<>();
        for (Map.Entry<Trade, List<Trade>> entry : entries) {
            List<Trade> splitTrades = entry.getValue();
            for (Trade splitTrade : splitTrades) {
                List<Order> orders4Trade = TradeUtils.getOrders4Trade(splitTrade);
                List<Order> followedGiftOrders = PlatGiftSplitUtils.getFollowedGiftOrders(staff, giftFollowMasterContext.getGiftOrderIdsFollowMasterOrderMap(), orders4Trade, giftFollowMasterContext.getOrderMap());
                Logs.debug(LogHelper.buildLog(staff, String.format("赠品 orderId=%s 跟随主品 masterOrderIds=%s", OrderUtils.toIdList(followedGiftOrders), OrderUtils.toIdList(orders4Trade))));
                for (Order order : followedGiftOrders) {
                    if (hasAllocateOrderIds.contains(order.getId())) {
                        continue;
                    }
                    // 发货后的不需要拆分出来
                    if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                        continue;
                    }
                    Order tbOrder = new TbOrder();
                    tbOrder.setId(order.getId());
                    tbOrder.setNum(order.getNum());
                    orders4Trade.add(tbOrder);
                }
                hasAllocateOrderIds.addAll(OrderUtils.toIdList(followedGiftOrders));
            }
        }
        Iterator<Map.Entry<Trade, List<Trade>>> iterator = params.splitDataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Trade, List<Trade>> next = iterator.next();
            List<Trade> splitTrades = next.getValue();
            Trade trade = next.getKey();
            Trade originTrade = originTradeMap.get(trade.getSid());
            if (splitTrades.size() == 1 && originTrade != null && TradeUtils.getOrders4Trade(splitTrades).size() == TradeUtils.getOrders4Trade(originTrade).size()) {
                List<Order> orders4Trade = TradeUtils.getOrders4Trade(splitTrades);
                boolean isRemove = orders4Trade.stream().allMatch(order -> {
                    Order originOrder = giftFollowMasterContext.getOrderMap().get(order.getId());
                    return originOrder != null && order.getNum() - originOrder.getNum() == 0;
                });
                // 全拆出去了，不需要拆
                if (isRemove) {
                    Logs.debug(LogHelper.buildLog(staff, String.format("sid=%s order全被拆出去了，不需要拆分", trade.getSid())));
                    iterator.remove();
                }
            }
        }
        if (!CollectionUtils.isEmpty(logs)) {
            ListUtils.splitList(logs, 10).forEach(log -> Logs.debug(LogHelper.buildLog(staff, String.format("%s", log))));
        }
    }

    private void fillPlatGiftFollowMasterOrder(Staff staff, Map<Long, Set<String>> userTidsMap, List<String> logs, GiftFollowMasterContext giftFollowMasterContext) {
        Set<Map.Entry<Long, Set<String>>> entries = userTidsMap.entrySet();
        for (Map.Entry<Long, Set<String>> entry : entries) {
            User user = staff.getUserByUserId(entry.getKey());
            if (user == null) {
                Logs.debug(LogHelper.buildLog(staff, String.format("拆单时，赠品跟随主品 userId=%s 在staff 中不存在user信息!", entry.getKey())));
                continue;
            }
            Set<String> tids = entry.getValue();
            List<TradeGiftDetail> tradeGiftDetails = platformGiftFollowMasterAccess.batchGetRelation(user, new ArrayList<>(tids));
            if (CollectionUtils.isEmpty(tradeGiftDetails)) {
                Logs.debug(LogHelper.buildLog(staff, String.format("查询多平台接口 userId=%s tids=%s 找不到平台赠品主品关系!", entry.getKey(), tids)));
                continue;
            }
            for (TradeGiftDetail tradeGiftDetail : tradeGiftDetails) {
                List<GiftRelationship> giftRelationships = tradeGiftDetail.getGiftRelationships();
                for (GiftRelationship giftRelationship : giftRelationships) {
                    String giftOid = giftRelationship.getGiftOrderId();
                    List<String> masterOidList = giftRelationship.getMasterOrderIds();
                    Set<Order> masterOrders = new HashSet<>();
                    for (String masterOid : masterOidList) {
                        //主品
                        List<Order> masterOrderList = giftFollowMasterContext.getOidGrouMap().get(Long.valueOf(masterOid));
                        if (CollectionUtils.isEmpty(masterOrderList)) {
                            logs.add(String.format("查询多平台接口 userId=%s 平台赠品  oid=%s 找不到主品 masterOid =%s ", entry.getKey(), giftOid, masterOid));
                            continue;
                        }
                        masterOrders.addAll(masterOrderList);
                    }
                    logs.add(String.format("查询多平台接口[平台赠品-对应的主品],userId=%s,[oid=%s,masterOids=%s],orderIds=%s", entry.getKey(), giftOid, masterOrders, OrderUtils.toIdList(new ArrayList<>(masterOrders))));
                    giftFollowMasterContext.getGiftOidsFollowMasterOrderMap().computeIfAbsent(Long.valueOf(giftOid), k -> new HashSet<>()).addAll(masterOrders);
                }
            }
        }

    }

}

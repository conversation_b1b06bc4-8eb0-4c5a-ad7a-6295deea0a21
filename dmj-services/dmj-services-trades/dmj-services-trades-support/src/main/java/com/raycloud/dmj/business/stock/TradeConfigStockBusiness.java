package com.raycloud.dmj.business.stock;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.trade.RecordQueryBusiness;
import com.raycloud.dmj.business.wms.TradeWmsBusiness;
import com.raycloud.dmj.dao.trade.PresellRuleDao;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.stock.StockOrderRecord;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptWhiteUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.bo.StockApplyConfigBO;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeUpdateService;
import com.raycloud.dmj.domain.trades.TradeImportResult;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeIOUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.domain.trade.config.TradeConfigEnum.*;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-03-31 16:41
 * @Description 交易关于库存的配置
 */
@Service
public class TradeConfigStockBusiness {

    private final Logger logger = Logger.getLogger(this.getClass());

    public static final String EVENT_NAME = "trade.refund.change.stock";
    public static final String SPLIT_REFUND_AUTO_STOCK = "split.refund.auto.stock";
    @Resource
    private ITradeConfigService tradeConfigService;

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;

    @Resource
    private IOrderStockService orderStockService;

    @Resource
    private ITradeUpdateService tradeUpdateService;

    @Resource
    private TbTradeDao tbTradeDao;

    @Resource
    private TradeWmsBusiness tradeWmsBusiness;

    @Resource
    private ILockService lockService;

    @Resource
    private RecordQueryBusiness recordQueryBusiness;
    @Resource
    private StockTradeBuildCateBusiness stockTradeBuildCateBusiness;
    @Resource
    private PresellRuleDao presellRuleDao;
    private final OrderCopier<Order, Order> orderCopier = new OrderCopier<>();
    @Transactional
    public List<Trade> handleStock(Staff staff, List<Trade> originTrades) {
        if (originTrades == null || originTrades.size() == 0) {
            return null;
        }
        return handleStock(staff, TradeUtils.toSids(originTrades),null);
    }

    @Transactional
    public List<Trade> handleStock(Staff staff, Long[] originSids, TradeImportResult result) {
        if (originSids == null || originSids.length == 0) {
            return null;
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        int openScalpNotApplyStock = tradeConfig.getOpenScalpNotApplyStock(), openRefundNotApplyStock = tradeConfig.getOpenRefundNotApplyStock();
        TradeConfigNew exceptionNotLockStock = TradeConfigGetUtil.get(staff, EXCEPTION_NOT_LOCK_STOCK);
        TradeConfigNew sendGoodsExceptUnaudit = TradeConfigGetUtil.get(staff, SEND_GOODS_EXCEPT_UNAUDIT);

        if ( openScalpNotApplyStock==0
                && Objects.equals(exceptionNotLockStock.getConfigValue(),"0")
                &&openRefundNotApplyStock==0) {
            return null;
        }
        StockApplyConfigBO stockApplyConfigBO=new StockApplyConfigBO();
        stockApplyConfigBO.setOpenScalpNotApplyStockConfig(openScalpNotApplyStock);
        stockApplyConfigBO.setOpenRefundNotApplyStock(openRefundNotApplyStock);
        stockApplyConfigBO.setExceptionNotLockStockConfig(exceptionNotLockStock.getConfigValue());
        List<Trade> dbTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, originSids);
        if (CollectionUtils.isEmpty(dbTrades)) {
            return null;
        }
        // 待审核,已审核或者待打印的单
        logger.debug(LogHelper.buildLog(staff,String.format("异常不申请库存处理,openScalpNotApplyStock=%s,openRefundNotApplyStock=%s,exceptionNotLockStock=%s,sendGoodsExceptUnaudit=%s,sid=%s",
                openScalpNotApplyStock,openRefundNotApplyStock,exceptionNotLockStock.getConfigValue(),sendGoodsExceptUnaudit.getConfigValue(),TradeUtils.toSidSet(dbTrades))));
        return lockService.locks(TradeLockBusiness.trades2ERPLocks(staff, dbTrades), () -> {
            List<Trade>  trades= tradeSearchService.queryBySidsContainMergeTrade(staff, true, TradeUtils.toSids(dbTrades));
            // 快递不锁异常
            if(Objects.equals(exceptionNotLockStock.getConfigValue(),"1")){
                 trades = stockTradeBuildCateBusiness.handleTradeStockPrefixAction(staff, trades, sendGoodsExceptUnaudit);
                if(CollectionUtils.isEmpty(trades)){
                    return null;
                }
            }
            // 前置日志，处理前参数状态
            List<String> logs = UnattainableUilts.buildLog(staff, trades);
            if(CollectionUtils.isNotEmpty(logs)){
                Lists.partition(logs,5).forEach(e->{
                    logger.debug(LogHelper.buildLog(staff,"当前数据库查询的结果:").append(e.toString()));
                });
            }
            UnattainableUilts.setTradeListAutoUnattainable(staff,trades);
            List<Order> resumeOrders = new ArrayList<>(), applyOrders = new ArrayList<>();
            Set<Long> sids = groupOrder(staff, trades, resumeOrders, applyOrders, stockApplyConfigBO);
            resumeStock(staff, resumeOrders, result);
            applyStock(staff, applyOrders);
            return resumeOrders.size() > 0 || applyOrders.size() > 0 ? doHandleStock(staff, trades, sids, tradeConfig) : null;
        });
    }

    private void resumeStock(Staff staff, List<Order> orders, TradeImportResult result) {
        Map<Long, Order> orderMap = order2Map(staff, orders);
        if (MapUtils.isEmpty(orderMap)) {
            return;
        }

        Map<Long, StockOrderRecord> orderIdRecordMap = recordQueryBusiness.queryByRecords(staff, orderMap.keySet().toArray(new Long[0]));
        if (MapUtils.isEmpty(orderIdRecordMap)) {
            return;
        }

        orderMap.forEach((orderId, order) -> {
            StockOrderRecord record = orderIdRecordMap.get(orderId);
            if (record == null) {
                orders.remove(order);
            }
        });
        orderStockService.resumeOrderStockLocal(staff, orders, result);
    }

    private void applyStock(Staff staff, List<Order> orders) {
        Map<Long, Order> orderMap = order2Map(staff, orders);
        if (MapUtils.isEmpty(orderMap)) {
            return;
        }
        Map<Long, StockOrderRecord> orderIdRecordMap = recordQueryBusiness.queryByRecords(staff, orderMap.keySet().toArray(new Long[0]));
        if (MapUtils.isNotEmpty(orderIdRecordMap)) {
            orderIdRecordMap.forEach((id, r) -> {
                Order order = orderMap.get(id);
                if (order != null) {
                    orders.remove(order);
                }
            });
        }

        orderStockService.applyOrderStockLocal(staff, orders, false);
        tradeWmsBusiness.sendAuditOrders(staff, orders);
    }

    private Map<Long, Order> order2Map(Staff staff, List<Order> orders) {
        Map<Long, Order> orderMap = new HashMap<>();
        if (orders != null && orders.size() > 0) {
            orders.forEach(o -> {
                orderMap.put(o.getId(), o);
                List<Order> suits = o.getSuits();
                if (suits != null && suits.size() > 0) {
                    suits.forEach(s -> orderMap.put(s.getId(), s));
                }
            });
        }
        return orderMap;
    }

    private List<Trade> doHandleStock(Staff staff, List<Trade> trades, Set<Long> sids, TradeConfig tradeConfig) {
        if (TradeExceptWhiteUtils.openExceptInsufficientCompanyIds(staff)) {
            return handlerUpdateTrade(staff, trades, sids, tradeConfig);
        }
        Map<Long, Trade> updateTradeMap = new HashMap<>();
        List<Order> updateOrders = new ArrayList<>();
        Set<Long> mergeSids = new HashSet<>();
        //merge_sid->trades
        Map<Long, Trade> mergeMainTradesMap = new HashMap<>();
        //merge_sid->orders
        Map<Long, List<Order>> mergeOrdersMap = new HashMap<>();
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            if (trade.getMergeSid() > 0) {
                if (trade.getMergeSid() - trade.getSid() == 0) {
                    mergeMainTradesMap.put(trade.getSid(), trade);
                }
                mergeOrdersMap.computeIfAbsent(trade.getMergeSid(), s -> new ArrayList<>()).addAll(orders);
            }

            if (sids.contains(trade.getSid())) {
                if (trade.getMergeSid() > 0) {
                    mergeSids.add(trade.getMergeSid());
                }
                createUpdate(staff, trade, updateTradeMap, tradeConfig);
                for (Order order : orders) {
                    createUpdate(staff, order, updateOrders);
                    List<Order> suits = order.getSuits();
                    if (suits != null && suits.size() > 0) {
                        for (Order suit : suits) {
                            createUpdate(staff, suit, updateOrders);
                        }
                    }
                }
            }
        }
        if (mergeSids.size() > 0) {
            for (Long mergeSid : mergeSids) {
                Trade mainTrade = mergeMainTradesMap.get(mergeSid);
                if (mainTrade == null) {
                    continue;
                }
                List<Order> mainOrders = mergeOrdersMap.get(mergeSid);
                if (mainOrders == null || mainOrders.size() == 0) {
                    continue;
                }
                TradeUtils.setOrders(mainTrade, mainOrders);
                createUpdate(staff, mainTrade, updateTradeMap, tradeConfig);
            }
        }
        List<Trade> updateTrades = new ArrayList<>(updateTradeMap.values());
        tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
        return updateTrades;
    }

    private void createUpdate(Staff staff, Trade trade, Map<Long, Trade> updateTradeMap, TradeConfig tradeConfig) {
        TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
        //重新计算异常状态
        TradeIOUtils.fillTradeExcep(staff, trade);
        Trade update = TradeBuilderUtils.builderUpdateItemExcep(trade);
        update.setSid(trade.getSid());
        update.setCompanyId(trade.getCompanyId());
        update.setEnableStatus(trade.getEnableStatus());
        update.setValidItemNum(trade.getValidItemNum());
        update.setInsufficientNum(trade.getInsufficientNum());
        update.setInsufficientRate(trade.getInsufficientRate());
       // update.setStockStatus(trade.getStockStatus());
        update.setIsExcep(trade.getIsExcep());
        TradeExceptUtils.setStockStatus(staff,update,trade.getStockStatus());
        updateTradeMap.put(update.getSid(), update);
    }

    private Set<Long> groupOrder(Staff staff, List<Trade> trades, List<Order> resumeOrders, List<Order> applyOrders, StockApplyConfigBO stockApplyConfigBO) {

        Set<Long> sids = new HashSet<>();
        List<String> logList=new ArrayList<>();
        Map<Long, Trade> tradeMaps = trades.stream().collect(Collectors.toMap(Trade::getSid, trade -> trade));
        StringBuilder configlog = new StringBuilder("交易库存相关的配置，openScalpNotApplyStock=").append(stockApplyConfigBO.getOpenScalpNotApplyStockConfig())
                .append(",openRefundNotApplyStock=").append(stockApplyConfigBO.getOpenRefundNotApplyStock())
                .append("autoUnattainableNotApplyStock=").append(stockApplyConfigBO.getExceptionNotLockStockConfig());
        logger.debug(LogHelper.buildLogHead(staff).append(configlog.toString()));
        Set<Long> preSellRuleIds = trades.stream().map(Trade::getPresellRuleId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        List<PresellRule> preSellRules = presellRuleDao.queryByIds(staff, Lists.newArrayList(preSellRuleIds));
        for (Trade trade : trades) {
            StringBuilder sb = new StringBuilder();
            if (trade.getIsCancel() != null && trade.getIsCancel() == 1) {
                continue;
            }
            Integer scalping = trade.getScalping();
            if (trade.getMergeSid() > 0 && (trade.getSid() - trade.getMergeSid()) != 0) {
                Trade mainTrade = tradeMaps.get(trade.getMergeSid());
                if (mainTrade != null) {
                    scalping = mainTrade.getScalping();
                }
            }
            sb.append(",sid=").append(trade.getSid()).append(",scalping=").append(scalping).append(",isRefund=").append(TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.REFUNDING));
            sb.append(",autoUnattainable=").append(TradeExceptUtils.isContainExcept(staff,trade, ExceptEnum.UNATTAINABLE));
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            UnattainableUilts.setOrderListAutoUnattainable(staff,trade, orders);
            boolean mergePreSellLockStock = getTradePreSellLockStock(trade, preSellRules);
            for (Order order : orders) {
                sb.append(",orderId=").append(order.getId()).append(",refundStatus=").append(order.getRefundStatus()).append(",SysStatus=").append(order.getSysStatus());
                if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) || order.getItemSysId() <= 0) {
                    continue;
                }
                fillOrderMsg(staff,order,scalping,trade);
                List<Order> suits = order.getSuits();
                if (suits != null && suits.size() > 0) {
                    for (Order suit : suits) {
                        suit.setRefundStatus(order.getRefundStatus());
                        fillOrderMsg(staff,suit, scalping,trade);
                    }
                }
                sids.add(trade.getSid());
                boolean resumeScalp = Objects.equals(stockApplyConfigBO.getOpenScalpNotApplyStockConfig(),1) && scalping!=null&&scalping == 1;
                boolean resumeUnattainable = Objects.equals(stockApplyConfigBO.getExceptionNotLockStockConfig(),"1") && UnattainableUilts.isAutoMatchUnattainable(staff,trade);
                boolean resumeRefund =  Objects.equals(stockApplyConfigBO.getOpenRefundNotApplyStock(),1) && RefundUtils.isRefundOrder(order);
                sb.append("resumeScalp=").append(resumeScalp).append("resumeUnattainable=").append(resumeUnattainable).append("resumeRefund=").append(resumeRefund)
                        .append(",mergePreSellLockStock=").append(mergePreSellLockStock);
                // 空包单和快递异常同时取消，才申请库存,分销trade不锁库存，合单预售锁库存看规则配置
                if (resumeScalp || resumeUnattainable || resumeRefund || TradeUtils.isFxOrMixTrade(trade) || !mergePreSellLockStock) {
                    resumeOrders.add(order);
                } else {
                    applyOrders.add(order);
                }
            }
            logList.add(sb.toString());
        }
        if(CollectionUtils.isNotEmpty(logList)){
            Lists.partition(logList,5).forEach(e->{
                logger.debug(LogHelper.buildLog(staff,"交易库存相关的配置是否锁库存处理的数据:").append(e.toString()));
            });
        }
        return sids;
    }

    /**
     * 合单预售锁库存 重新合单后要去判断订单命中的规则是否锁库存
     */
    private boolean getTradePreSellLockStock(Trade trade, List<PresellRule> preSellRules) {
        if (CollectionUtils.isEmpty(preSellRules)) {
            return true;
        }
        Map<Long, Boolean> ruleIdToLockMap = preSellRules.stream().collect(Collectors.toMap(PresellRule::getId, PresellRule::isLockStock));
        if (Objects.nonNull(trade.getPresellRuleId())) {
            Long preSellRuleId = trade.getPresellRuleId();
            Boolean isLock = ruleIdToLockMap.get(preSellRuleId);
            return isLock != null && isLock;
        }
        return true;
    }

    private void fillOrderMsg(Staff staff,Order order,Integer scalping ,Trade trade) {
        order.setUserId(trade.getUserId());
        order.setWarehouseId(trade.getWarehouseId());
        order.setScalping(scalping);
        order.setAutoUnattainable(TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.UNATTAINABLE)?1:0);
        order.setExceptData(trade.getExceptData());
    }

    public void fillOrderWarehouseIds(Staff staff, List<Order> orders) {
        List<Order> noWarehouseIdOrders = orders.stream().filter(order -> order.getWarehouseId() == null).collect(Collectors.toList());
        if (noWarehouseIdOrders.size() > 0) {
            Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(tbTradeDao.queryByKeys(staff, "sid,warehouse_id", "sid", OrderUtils.toSids(noWarehouseIdOrders)));
            noWarehouseIdOrders.forEach(order -> {
                Trade trade = tradeMap.get(order.getSid());
                if (trade != null) {
                    order.setWarehouseId(trade.getWarehouseId());
                }
            });
        }
    }


    public List<Order> filterNeedFillOrderScalping(Staff staff, List<Order> orders) {
        if (orders == null || orders.size() == 0 ) {
            return null;
        }
        List<Order> fillScalpingOrders = new ArrayList<>();
        for (Order order : orders) {
            List<Order> suits = order.getSuits();
            if (order.getScalping() != null) {
                if (suits != null && suits.size() > 0) {
                    for (Order suit : suits) {
                        suit.setRefundStatus(order.getRefundStatus());
                        if (suit.getScalping() == null) {
                            suit.setScalping(order.getScalping());
                        }
                        suit.setAutoUnattainable(order.getAutoUnattainable());
                    }
                }
            } else {
                order.setScalping(0);
                fillScalpingOrders.add(order);
                if (suits != null && suits.size() > 0) {
                    for (Order suit : suits) {
                        suit.setRefundStatus(order.getRefundStatus());
                        suit.setScalping(0);
                        suit.setAutoUnattainable(order.getAutoUnattainable());
                        fillScalpingOrders.add(suit);
                    }
                }
            }
        }
        return fillScalpingOrders;
    }


    public static <T extends Trade> void fillOrderScalping(Staff staff, List<T> trades, List<Order> fillScalpingOrders) {
        if (trades == null || trades.size() == 0 || fillScalpingOrders == null || fillScalpingOrders.size() == 0) {
            return;
        }
        Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(trades);
        for (Order order : fillScalpingOrders) {
            Trade trade = tradeMap.get(order.getSid());
            if (trade != null && trade.getScalping() != null) {
                order.setScalping(trade.getScalping());
            }
            if(trade != null){
                order.setAutoUnattainable(TradeExceptUtils.isContainExcept(staff,trade,ExceptEnum.UNATTAINABLE)?1:0);
                order.setExceptData(trade.getExceptData());
            }
        }
    }

    private void createUpdate(Staff staff, Order order, List<Order> updateOrders) {
        Order update = OrderBuilderUtils.builderUpdateOrder(order);
        update.setId(order.getId());
        update.setCompanyId(order.getCompanyId());
        update.setEnableStatus(order.getEnableStatus());
        //update.setStockStatus(order.getStockStatus());
        OrderExceptUtils.setStockStatus(staff,update,order.getStockStatus());
        update.setStockNum(order.getStockNum());
        updateOrders.add(update);
    }

    /**
     * @param staff
     * @param undoSplitTrades 取消拆分的订单 注意：undoSplitTrades 是拥有同一个splitSid 的一批单
     * @param orders 取消拆分的订单下面的order
     */
    public void resumeUndoSplitTradeStock(Staff staff, List<Trade> undoSplitTrades,List<Order> orders){
        if(CollectionUtils.isEmpty(undoSplitTrades)|| CollectionUtils.isEmpty(orders)){
            return;
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        int openScalpNotApplyStock = tradeConfig.getOpenScalpNotApplyStock(), openRefundNotApplyStock = tradeConfig.getOpenRefundNotApplyStock();
        TradeConfigNew exceptionNotLockStock = TradeConfigGetUtil.get(staff, EXCEPTION_NOT_LOCK_STOCK);
        long unattainableCount = undoSplitTrades.stream().filter(trade -> TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UNATTAINABLE)).count();
        long scalpingCount = undoSplitTrades.stream().filter(trade -> Objects.equals(trade.getScalping(), 1)).count();
        boolean resumeScalp = Objects.equals(openScalpNotApplyStock,1) && scalpingCount> 0;
        boolean resumeUnattainable = Objects.equals(exceptionNotLockStock.getConfigValue(),"1") && unattainableCount>0;
        for(Order order:orders){
            order.setAutoUnattainable(unattainableCount>0?1:0);
            order.setScalping(scalpingCount>0?1:0);
            List<Order> suits = order.getSuits();
            if(CollectionUtils.isEmpty(suits)){
                continue;
            }
            for(Order suit:suits){
                suit.setAutoUnattainable(order.getAutoUnattainable());
                suit.setScalping(order.getScalping());
                suit.setRefundStatus(order.getRefundStatus());
            }

        }
        for(Trade trade:undoSplitTrades){
            TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.UNATTAINABLE,unattainableCount>0?1L:0L);
            trade.setScalping(scalpingCount>0?1:0);
        }
        logger.debug(LogHelper.buildLog(staff,String.format("订单取消拆分,异常不申请库存处理,openScalpNotApplyStock=%s,openRefundNotApplyStock=%s,exceptionNotLockStock=%s,unattainableCount=%s,scalpingCount=%s,sid=%s",
                openScalpNotApplyStock,openRefundNotApplyStock,exceptionNotLockStock.getConfigValue(),unattainableCount,scalpingCount,TradeUtils.toSidSet(undoSplitTrades))));
       // 只在这处理不需要锁库存的order，锁库存的在业务最后会走事件handleStock 方法处理
        List<Order> resumeOrders=new ArrayList<>();
        for(Order order:orders){
            boolean resumeRefund = Objects.equals(openRefundNotApplyStock, 1) && RefundUtils.isRefundOrder(order);
            if (resumeScalp || resumeUnattainable || resumeRefund) {
                resumeOrders.add(order);
            }
        }
        if(CollectionUtils.isEmpty(resumeOrders)){
            return;
        }
        resumeStock(staff, resumeOrders);
    }

    /**
     *  orders 对象的异常在后面不会再查询数据库填充，需要自己在此方法前自己填充异常，例如：空包单，快递异常等，
     *  或者调用resumeStock(Staff staff, List<Order> orders, TradeImportResult result)
     * @param staff
     * @param orders
     */
    private void resumeStock(Staff staff, List<Order> orders) {
        Map<Long, Order> orderMap = order2Map(staff, orders);
        if (MapUtils.isEmpty(orderMap)) {
            return;
        }
        Set<Long> orderIds = orderMap.keySet();
        Map<Long, StockOrderRecord> orderIdRecordMap = recordQueryBusiness.queryByRecords(staff, orderIds.toArray(new Long[0]));
        if (MapUtils.isEmpty(orderIdRecordMap)) {
            return;
        }

        orderMap.forEach((orderId, order) -> {
            StockOrderRecord record = orderIdRecordMap.get(orderId);
            if (record == null) {
                orders.remove(order);
            }
        });
        orderStockService.resumeOrderStockCommonLocal(staff, orders, null);
        tradeWmsBusiness.sendResume(staff, orders);
    }


    private List<Trade> handlerUpdateTrade(Staff staff, List<Trade> trades, Set<Long> sids, TradeConfig tradeConfig) {

        Map<Long, List<Trade>> tradeMap = new HashMap<>();
        for (Trade trade : trades) {
            tradeMap.computeIfAbsent(trade.getMergeSid() > 0 ? trade.getMergeSid() : trade.getSid(), k -> new ArrayList<>()).add(trade);
        }
        List<Trade> updateTrades=new ArrayList<>();
        List<Order> updateOrders=new ArrayList<>();
        Set<Map.Entry<Long, List<Trade>>> entries = tradeMap.entrySet();
        for (Map.Entry<Long, List<Trade>> entry : entries) {
            List<Trade> tradeList = entry.getValue();
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(tradeList);
            Set<Long> updateSids = TradeUtils.toSidSet(tradeList);
            boolean noUpdate = updateSids.stream().noneMatch(sid -> sids.contains(sid));
            if (noUpdate) {
                continue;
            }
            for (Trade trade : tradeList) {
                if (Objects.equals(trade.getSid(), trade.getMergeSid())) {
                    // 主单
                    TradeStockUtils.resetTradeStockStatus(staff, trade, orders4Trade, true, tradeConfig);
                    TradeUtils.resetTradeItemNum(trade, orders4Trade, tradeConfig);
                } else {
                    TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
                    TradeUtils.resetTradeItemNum(trade, tradeConfig);
                }
            }
            for(Trade trade:tradeList){
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                for(Order order:orders){
                    addUpdateOrder(staff, order, updateOrders);
                }
                Trade update = TradeBuilderUtils.builderUpdateItemExcep(trade);
                update.setSid(trade.getSid());
                update.setInsufficientNum(trade.getInsufficientNum());
                update.setInsufficientRate(trade.getInsufficientRate());
                update.setItemKindNum(trade.getItemKindNum());
                update.setItemNum(trade.getItemNum());
                update.setSingleItemKindNum(trade.getSingleItemKindNum());
                update.setCompanyId(trade.getCompanyId());
                update.setIsExcep(trade.getIsExcep());
                TradeExceptUtils.setStockStatus(staff,update,trade.getStockStatus());
                updateTrades.add(update);
            }
        }
        tradeUpdateService.updateTrades(staff, updateTrades, updateOrders);
        return updateTrades;
    }


    private void addUpdateOrder(Staff staff,Order order,List<Order> updateOrders){
        Order updateOrder = OrderBuilderUtils.builderUpdateOrder(order);
        updateOrder.setId(order.getId());
        updateOrder.setCompanyId(order.getCompanyId());
        OrderExceptUtils.setStockStatus(staff,updateOrder,order.getStockStatus());
        updateOrder.setStockNum(order.getStockNum());
        updateOrders.add(updateOrder);
        List<Order> suits = order.getSuits();
        if(CollectionUtils.isEmpty(suits)){
            return;
        }
        for(Order suit:suits){
            addUpdateOrder(staff, suit, updateOrders);
        }
    }
}

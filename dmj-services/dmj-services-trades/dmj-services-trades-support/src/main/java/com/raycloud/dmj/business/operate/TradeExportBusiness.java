package com.raycloud.dmj.business.operate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.PlatformSourceConstants;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.common.CacheBusiness;
import com.raycloud.dmj.business.common.CipherTextUtils;
import com.raycloud.dmj.business.data.TradeExportData;
import com.raycloud.dmj.business.data.TradeExportResourceSwitchData;
import com.raycloud.dmj.business.export.TradeExportContext;
import com.raycloud.dmj.business.export.TradeExportHelper;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.item.TradeItemChangeDetailBusiness;
import com.raycloud.dmj.business.trade.CommonTradeDecryptBusiness;
import com.raycloud.dmj.business.trade.UnionTradeDecryptBusiness;
import com.raycloud.dmj.business.tradepay.TradePayBusiness;
import com.raycloud.dmj.dao.params.TradeItemChangeDetailParam;
import com.raycloud.dmj.dao.trade.TradeExtDao;
import com.raycloud.dmj.dao.trade.TradeItemChangeDetailDao;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.DownloadCenter;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.constant.ExportEventConstants;
import com.raycloud.dmj.domain.diamond.ConfigHolder;
import com.raycloud.dmj.domain.diamond.domian.TradeSourcePlatformCodeRelation;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.domain.enums.TradeDecryptSourceEnum;
import com.raycloud.dmj.domain.enums.TradeExportFieldEnum;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.ItemSupplierBridge;
import com.raycloud.dmj.domain.item.tb.TbItem;
import com.raycloud.dmj.domain.item.tb.TbSku;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLog;
import com.raycloud.dmj.domain.pt.MultiPacksPrintTradeLogDetail;
import com.raycloud.dmj.domain.pt.enums.WlbTemplateTypeEnum;
import com.raycloud.dmj.domain.pt.wlb.UserWlbExpressTemplate;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.common.TradeTimeUtils;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.invoice.InvoiceKindEnum;
import com.raycloud.dmj.domain.trade.invoice.InvoiceTypeEnum;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.export.TradeExcelExportTask;
import com.raycloud.dmj.domain.trades.payment.util.BigDecimalWrapper;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.QiMenShop;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.TradeDiamondUtils;
import com.raycloud.dmj.domain.utils.diamond.TradeExportDiamondUtils;
import com.raycloud.dmj.domain.utils.diamond.TradeSourcePlatformCodeDiamondUtils;
import com.raycloud.dmj.domain.wave.OrderUniqueCodeStatusEnum;
import com.raycloud.dmj.domain.wave.WaveRule;
import com.raycloud.dmj.domain.wave.WaveSorting;
import com.raycloud.dmj.domain.wave.WaveUniqueCode;
import com.raycloud.dmj.domain.wave.model.ItemUniqueCodeQueryParams;
import com.raycloud.dmj.domain.wave.utils.WaveUtils;
import com.raycloud.dmj.domain.wms.GoodsSectionOrderRecord;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.jd.trade.JdTradeAccess;
import com.raycloud.dmj.logistics.api.ILogisticsWarningService;
import com.raycloud.dmj.logistics.common.model.LastLogisticsTraceDTO;
import com.raycloud.dmj.logistics.common.model.LogisticsTraceDTO;
import com.raycloud.dmj.logistics.common.param.LogisticsFullTraceList;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.dubbo.IUniqueCodeServiceDubbo;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.pt.IFreightTemplateService;
import com.raycloud.dmj.services.pt.IFreightTemplateVolumeService;
import com.raycloud.dmj.services.pt.IMultiPacksPrintTradeLogService;
import com.raycloud.dmj.services.pt.IUserWlbExpressTemplateService;
import com.raycloud.dmj.services.sensitive.DataSecurityHandleService;
import com.raycloud.dmj.services.response.PackmaItemDetailResponse;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.fill.TradeInvoiceFill;
import com.raycloud.dmj.services.trades.support.ExportTimeSliceSearcher;
import com.raycloud.dmj.services.trades.support.TradeColdDataService;
import com.raycloud.dmj.services.trades.support.search.TradeFieldUtils;
import com.raycloud.dmj.services.trades.wave.IItemUniqueCodeService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.LogKit;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.dmj.services.wms.IWmsService;
import com.raycloud.dmj.utils.excel.ExcelHelper;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.dmj.web.utils.DateUtil;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * TradeExportBusiness
 *
 * <AUTHOR>
 * @Date 2019-03-21
 * @Time 15:21
 */
@Service
public class TradeExportBusiness {

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    IShopService shopService;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    ITradeDataService tradeDataService;

    @Resource
    CacheBusiness cacheBusiness;

    @Resource
    IEventCenter eventCenter;

    @Resource
    IMultiPacksPrintTradeLogService multiPacksPrintTradeLogService;

    @Resource
    IUserWlbExpressTemplateService userWlbExpressTemplateService;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    IFreightTemplateService freightTemplateService;

    @Resource
    IFreightTemplateVolumeService freightTemplateVolumeService;

    @Resource
    IDownloadCenterService downloadCenterService;

    @Resource
    private IItemServiceWrapper itemServiceWrapper;

    @Resource
    ITradeWaveService tradeWaveService;

    @Resource
    private ITradeQueryService tradeQueryService;

    @Resource
    private TradePayBusiness tradePayBusiness;

    @Autowired
    ICache cache;

    @Resource
    TradeExtDao tradeExtDao;

    @Resource
    private IStaffService staffService;

    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    private DataSecurityHandleService dataSecurityHandleService;


    @Resource
    private CommonTradeDecryptBusiness commonTradeDecryptBusiness;

    @Resource
    private UnionTradeDecryptBusiness unionTradeDecryptBusiness;

    @Resource
    private IDmsTradeService dmsTradeService;

    @Resource
    FxBusiness fxBusiness;

    @Resource
    private TradeItemChangeDetailDao tradeItemChangeDetailDao;

    @Resource
    private TradeItemChangeDetailBusiness tradeItemChangeDetailBusiness;

    @Resource
    private ILogisticsTrackingRecordService logisticsTrackingRecordService;

    @Resource
    private IItemServiceDubbo itemServiceDubbo;
    @Resource
    private IWmsService wmsService;

    @Resource
    private ILogisticsWarningService logisticsWarningService;

    @Resource
    private TradeColdDataService tradeColdDataService;
    @Resource
    TradeInvoiceFill tradeInvoiceFill;

    @Resource
    private IItemUniqueCodeService itemUniqueCodeService;

    @Resource
    private IUniqueCodeServiceDubbo uniqueCodeServiceDubbo;

    @Resource
    private LogisticBusiness logisticBusiness;

    private final Logger logger = Logger.getLogger(this.getClass());

    private static final String pdd_whiteCompanyId = "1476";

    private static final Integer MAX_SIZE = 999999;

    public static final String QUERY_FILEDS = "t.sid,t.tid,t.merge_sid,t.user_id,t.taobao_id,t.warehouse_id,t.is_excep,t.enable_status,t.source_id,t.dest_id,t.belong_type";


    /**
     * 用户店铺缓存
     * key:userId, value:Shop,只缓存部分字段
     */
    private static final Cache<Long, Shop> UserShopCache  = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();
    /**
     * 用户仓库缓存
     * key:warehouseId, value:Warehouse,只缓存部分字段
     */
    private static final Cache<Long, Warehouse> UserWarehousesCache  = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();
    /**
     * 用户订单配置缓存
     */
    private static final Cache<Long, TradeConfig> UserTradeConfigCache  = CacheBuilder.newBuilder()
            .maximumSize(20)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();
    /**
     * 用户是否需要加解密
     */
    private static final Cache<Long, Boolean> UserNotEncryptCache  = CacheBuilder.newBuilder()
            .maximumSize(20)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    /**
     * 供销单，分销店铺的缓存
     * key：userId
     * value：Shop -- 只保留需要的字段
     */
    private static final Cache<Long, Shop> UserFxShopCache  = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    private static final List<String> PLAIN_TEXT_PLAT_FORM_LIST = Lists.newArrayList(
            CommonConstants.PLAT_FORM_TYPE_TIAN_MAO,CommonConstants.PLAT_FORM_TYPE_TAO_BAO,
            CommonConstants.PLAT_FORM_TYPE_TAO_BAO_TJB,CommonConstants.PLAT_FORM_TYPE_1688,
            CommonConstants.PLAT_FORM_TYPE_FX,CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU,
            CommonConstants.PLAT_FORM_TYPE_XHS);

    /**
     * 获取默认订单头部信息
     * @deprecated 不支持原始列明白名单
     * @see TradeLocalConfigurable#isTradeExportOrgiFieldNameCompanyIds(long)
     * @return
     */
    public String[][] getExporTitle(String scop3) {
        String[][] excelTitle = new String[1][];
        List<TradeExportFieldEnum> fieldsByScope = TradeExportFieldEnum.getFieldsByScope(scop3);
        List<String> titles = new ArrayList<String>();
        for (TradeExportFieldEnum tradeExportFieldEnum : fieldsByScope) {
            titles.add(tradeExportFieldEnum.getName());
        }
        excelTitle[0] = new String[titles.size()];
        titles.toArray(excelTitle[0]);
        return excelTitle;
    }

    /**
     * 获取默认订单头部信息
     *
     * @return
     */
    public String[][] getExporTitle(String scop3,Long companyId) {
        String[][] excelTitle = new String[1][];
        List<TradeExportFieldEnum> fieldsByScope = TradeExportFieldEnum.getFieldsByScope(scop3);
        List<String> titles = new ArrayList<String>();
        for (TradeExportFieldEnum tradeExportFieldEnum : fieldsByScope) {
            titles.add(TradeExportHelper.getExportFieldName(companyId,tradeExportFieldEnum));
        }
        excelTitle[0] = new String[titles.size()];
        titles.toArray(excelTitle[0]);
        return excelTitle;
    }

    /**
     * 供销订单导出需要导出分销商的店铺信息
     * @param staff
     * @return
     */
    private Map<Long, Shop> getFxShopInfo(Staff staff, List<Long> taobaoIds){
        if (CollectionUtils.isEmpty(taobaoIds)){
            return null;
        }
        Map<Long, Shop> shopsMap = Maps.newHashMap();
        Lists.partition(taobaoIds, 200).forEach(part ->{
            List<Shop> shops = shopService.queryByUserIds(null, part.toArray(new Long[0]));
            if (CollectionUtils.isNotEmpty(shops)){
                for (Shop shop : shops){
                    Shop fxShop = new Shop();
                    fxShop.setUserId(shop.getUserId());
                    fxShop.setTitle(shop.getTitle());
                    fxShop.setShortTitle(shop.getShortTitle());
                    fxShop.setSimpleTitleConfig(shop.getSimpleTitleConfig());
                    fxShop.setExternalName(shop.getExternalName());
                    shopsMap.put(fxShop.getUserId(), fxShop);
                }
            }
        });
        return shopsMap;
    }

    /**
     *  导出订单,导出工程里三个月内订单与归档订单导出流程区分开了，exportTradeNew：三个月内订单导出；exportTrade：归档订单导出
     * @param staff
     * @param exportParams
     * @param isAsync 如果是导出工程触发，改值为false
     * @param task 导出工程任务
     * @param sids sid列表
     * @return
     */
    public List<LinkedList<String>> exportTrade(Staff staff, TradeExportParams exportParams, boolean isAsync, TradeExcelExportTask task, Long[] sids) {
        if (isAsync) {
            try {
                launchAsyncTradeExportEvent(staff, exportParams);
            } catch (Exception e) {
                logger.error(LogHelper.buildLogHead(staff).append("订单导出失败"), e);
            }
            return null;
        }
        Long beginTime=System.currentTimeMillis();
        TradeExportContext context = getTradeExportContext(staff, exportParams, task, TradeExcelExportTask.TASK_TYPE_TRADE);
        //查询数据
        List<Trade> trades = queryExportTradesTook(staff, exportParams, sids);
        trades = queryRelationTradeTook(staff, trades, context);
        trades = handleSelectedExport(staff,task,trades);
        //数据为空
        if (trades == null || trades.size() == 0) {
            return null;
        }
        fillFxSourceTradeTook(staff, trades, context);
        fillPlatFormIdTook(staff, trades, context);
        fillGxTemplateNameTook(staff, context, trades);
        sensitiveTradeTook(staff, trades, context);
        List<JSONObject> excelContent = new ArrayList<JSONObject>();
        Map<Long, Shop> fxShopInfo = getFxUserShopTook(staff, trades, context);
        Map<Long, Shop> shops = getUserShopsTook(staff, trades, context);
        Map<Long, Warehouse> warehouses = getUserWarehousesTook(staff, trades, context);
        //一单多包记录
        Map<String, MultiPacksPrintTradeLog> outSidMultiPacksMap = getMultiPacksPrintTradeOutSidTook(staff, trades, context);
        Map<Long, WaveRule> waveIdWaveRuleMap = getWaveIdWaveRuleTook(staff, trades, context);
		//支付单相关信息
		fullTradePayTook(staff, trades, context);
        //订单发票信息
        fillTradeInvoiceTook(staff, trades, context);
        Map<Long, WaveSorting> pickNameMap = getPickNameTook(staff, trades, context);
        Map<Long, String> packCheckNameMap = getPackCheckNameMapTook(staff, trades, context);
        fillDestAndSourceNameTook(staff, trades, context);
        //订单消耗的包材商品
        Map<Long, List<PackmaItemDetailResponse>> packmaItemsDetail = getPackmaItemDetailTook(staff, trades, context);
        //物流预警信息
        Map<String, Map<String, String>> logisticsTraceInfoMap = getLogisticsTraceInfoTook(staff, trades, context, outSidMultiPacksMap);
        TradeExportData tradeExportData = new TradeExportData().setWarehouses(warehouses).setShops(shops).setFxShopInfo(fxShopInfo).setWaveIdWaveRuleMap(waveIdWaveRuleMap)
                .setPickNameMap(pickNameMap).setPackCheckNameMap(packCheckNameMap).setPackmaItemsDetailMap(packmaItemsDetail).setLogisticsTraceInfoMap(logisticsTraceInfoMap);
        int size = trades.size();
        for (int i = 0; i < size; i++) {
			Trade trade = trades.get(i);
            TradeExceptionUtils.analyze(staff, trade);
            JSONObject columnJson = exportCommon(staff,trade, tradeExportData, context);
            excelContent.add(columnJson);
            MultiPacksPrintTradeLog multiPacksPrintTradeLog = outSidMultiPacksMap.get(trade.getOutSid());
            String[] outSidArray = TradeUtils.getMultiPrintOutsid(multiPacksPrintTradeLog);
            if (outSidArray != null && outSidArray.length > 0) {
                Map<String, MultiPacksPrintTradeLogDetail> outSidMutilPacksMap = multiPacksPrintTradeLog.getDetails()
                        .stream().filter(o->StringUtils.isNotBlank(o.getOutSid())).collect(Collectors.toMap(MultiPacksPrintTradeLogDetail::getOutSid,a->a,(a,b)->b));
                String cost = "",weight ="";
                for (String outSid : outSidArray) {
                    MultiPacksPrintTradeLogDetail logDetail = outSidMutilPacksMap.get(outSid);
                    if (outSid.equals(trade.getOutSid())) {
                        if(context.exportMultiPackActualInfo){
                            cost = String.valueOf(logDetail==null || logDetail.getCost() == null ? "" : logDetail.getCost());
                            weight =logDetail==null?"": MathUtils.toString(logDetail.getWeight(),4);
                        }
                        continue;
                    }
                    JSONObject jsonObject = createNewJSONObject(columnJson, false, true);
                    executePut(jsonObject, TradeExportFieldEnum.OUT_SID, outSid);
                    executePut(jsonObject, TradeExportFieldEnum.TEMPLATE_NAME, trade.getTemplateName());
                    executePut(jsonObject, TradeExportFieldEnum.LOGISTICS_TRACE_COUNT, tradeExportData.getLogisticsTraceInfo(outSid, TradeExportFieldEnum.LOGISTICS_TRACE_COUNT.getField()));
                    executePut(jsonObject, TradeExportFieldEnum.LAST_LOGISTICS_TRACE, tradeExportData.getLogisticsTraceInfo(outSid, TradeExportFieldEnum.LAST_LOGISTICS_TRACE.getField()));
                    executePut(jsonObject, TradeExportFieldEnum.LOGISTICS_MODIFIED, tradeExportData.getLogisticsTraceInfo(outSid, TradeExportFieldEnum.LOGISTICS_MODIFIED.getField()));
                    executePut(jsonObject, TradeExportFieldEnum.NEVER_MODIFIED_TIME, tradeExportData.getLogisticsTraceInfo(outSid, TradeExportFieldEnum.NEVER_MODIFIED_TIME.getField()));
                    if(context.exportMultiPackActualInfo && logDetail!=null){
                        executePut(jsonObject, TradeExportFieldEnum.ACTUAL_POST_FEE, String.valueOf(logDetail.getCost() == null ?"":logDetail.getCost()));//实付运费
                        executePut(jsonObject, TradeExportFieldEnum.WEIGHT, MathUtils.toString(logDetail.getWeight(),4));//实际重量
                    }
                    excelContent.add(jsonObject);
                }
                if(context.exportMultiPackActualInfo){
                    executePut(columnJson, TradeExportFieldEnum.ACTUAL_POST_FEE, cost);//实付运费
                    executePut(columnJson, TradeExportFieldEnum.WEIGHT, weight);//实际重量
                }
            }
        }
        tradeExportData = null;
        List<LinkedList<String>> result = getListResultByNeedFields(staff, excelContent, context.exportFieldEnums);
        Long time = (System.currentTimeMillis() - beginTime) / 1000;
        logger.info(String.format("本次归档订单查询数据组成时间为：【%s】，数据组装结束",time));
        return result;
    }

    /**
     * 填充供销对应的分销trade
     * 用于解密
     * @param trades
     */
    private void fillFxSourceTrade(List<Trade> trades) {
        Map<String, Trade> tidTradeMap = fxBusiness.getFxTradeMapByGx(trades);
        if(tidTradeMap == null){
            return;
        }
        trades.forEach(t-> t.setSourceTrade(tidTradeMap.get(t.getTid())));
    }

    private void fillPlatFormId(Staff staff, List<Trade> trades) {
        List<Trade> mergeQmTrade = new ArrayList<>();
        List<Trade> needFillGxTrade = new ArrayList<>();
        for (Trade trade : trades){
            if (Objects.isNull(trade.getTradeExt())){
                continue;
            }
            if (TradeUtils.isGxOrMixTrade(trade)){
                Object platformId = TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "platformId");
                if (Objects.nonNull(platformId)){
                    trade.setFxPlatformTid(platformId.toString());
                }
                Object mergePlatformId = TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "mergePlatformId");
                if (Objects.nonNull(mergePlatformId)){
                    trade.setFxPlatformTid(mergePlatformId.toString());
                }
                if (TradeUtils.getOrders4Trade(trade).size() <= 1){
                    continue;
                }
                needFillGxTrade.add(trade);
            }
            if (TradeUtils.isQimenFxSource(trade) || Objects.equals("qimen", trade.getSource())){
                Object platformId = TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "thirdPlatTid");
                if (Objects.nonNull(platformId)){
                    trade.setFxPlatformTid(platformId.toString());
                }
                if (TradeUtils.isMerge(trade)){
                    mergeQmTrade.add(trade);
                }
            }
        }
        //开启白名单的，查询原始分销订单去获取原始平台单号
        if (CollectionUtils.isNotEmpty(needFillGxTrade) && ConfigHolder.FX_GLOBAL_CONFIG.open("gx_platId_merge_key", staff.getCompanyId())){
            Map<Long, Trade> sid2Trade = TradeUtils.toMapBySid(needFillGxTrade);
            Map<Long, String> mergePlatformIdMap = fxBusiness.getPlatformIdContainMergeFromGx(staff, needFillGxTrade);
            mergePlatformIdMap.forEach((sid ,platformId) ->{
                Trade gxTrade = sid2Trade.get(sid);
                if (Objects.nonNull(gxTrade)){
                    gxTrade.setFxPlatformTid(platformId);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(mergeQmTrade)){
            List<String> allSids = TradeUtils.getOrders4Trade(mergeQmTrade).stream().map(Order::getSid).distinct().map(String::valueOf).collect(Collectors.toList());
            Map<Long, TradeExt> tradeExtMap = tradeExtDao.queryTradeExtBySids(staff, allSids).stream().collect(Collectors.toMap(TradeExt::getSid, java.util.function.Function.identity(), (k1, k2)->k1));
            for (Trade mainTrade : mergeQmTrade){
                List<String> platformIds = new ArrayList<>();
                TradeUtils.getOrders4Trade(mainTrade).stream().map(Order::getSid).distinct().collect(Collectors.toList()).forEach(sid ->{
                    TradeExt tradeExt = tradeExtMap.get(sid);
                    String thirdPlatTid = (String) TradeExtUtils.getExtraFieldValue(tradeExt,"thirdPlatTid");
                    if (StringUtils.isNotEmpty(thirdPlatTid) && !platformIds.contains(thirdPlatTid)){
                        platformIds.add(thirdPlatTid);
                    }
                });
                if (CollectionUtils.isNotEmpty(platformIds)){
                    mainTrade.setFxPlatformTid(String.join(",", platformIds));
                }
            }
        }
    }

    /**
     * 填充分销对应的供销快递模版名称
     * @param trades
     */
    private void fillGxTemplateName(List<Trade> trades){
        Map<Long, List<Trade>> fillTradeMap = trades.stream().filter(TradeUtils::isFxOrMixTrade).filter(TradeUtils::isAfterSendGoods).collect(Collectors.groupingBy(Trade::getDestId));
        if (MapUtils.isEmpty(fillTradeMap)){
            return ;
        }
        try {
            for (Map.Entry<Long, List<Trade>> entry : fillTradeMap.entrySet()){
                Long destId = entry.getKey();
                if (null == destId || destId <= 0){
                    continue;
                }
                Staff destStaff = staffService.queryDefaultStaffByCompanyId(destId);
                if (null == destStaff){
                    continue;
                }
                Lists.partition(entry.getValue(), 500).forEach(part ->{
                    Map<String, Trade> tidGxTradeMap = tradeSearchService.queryByTids(destStaff, false, Strings.listToString(TradeUtils.toSidList(part))).stream().filter(t -> !TradeUtils.isCancel(t)).collect(Collectors.toMap(Trade::getTid, k ->k, (t1, t2) -> t2));
                    part.forEach(fxTrade ->{
                        Trade gxTrade = tidGxTradeMap.get(fxTrade.getSid().toString());
                        if (null != gxTrade){
                            fxTrade.setTemplateName(gxTrade.getTemplateName());
                        }
                    });
                });
            }
        }catch (Exception e){
            logger.error("分销订单导出，填充供销订单快递模版名称出错" + e);
        }
    }

    /**
     *  导出订单
     * @param staff
     * @param task
     * @param sids
     * @return
     */
    public List<LinkedList<String>> exportTradeNew(Staff staff, TradeExcelExportTask task, Long[] sids ) {
        logger.info("查询订单拼装数据开始执行");
        Long beginTime =System.currentTimeMillis();
        TradeExportContext context = getTradeExportContext(staff, null, task, TradeExcelExportTask.TASK_TYPE_TRADE);
        //查询数据
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, sids);
        //查询订单的关联订单：换货、补发、手工单
        trades = queryRelationTradeTook(staff, trades, context);
        //数据为空
        if (trades == null || trades.size() == 0) {
            return null;
        }
        fillFxSourceTradeTook(staff, trades, context);
        fillPlatFormIdTook(staff, trades, context);
        fillGxTemplateNameTook(staff, context, trades);
        sensitiveTradeTook(staff, trades, context);
        List<JSONObject> excelContent = new ArrayList<>(trades.size());
        Map<Long, Shop> shops = getUserShopsTook(staff, trades, context);
        Map<Long, Warehouse> warehouses = getUserWarehousesTook(staff, trades, context);
        Map<Long, Shop> fxShopInfoMap = getFxUserShopTook(staff, trades, context);
        //一单多包记录
        Map<String, MultiPacksPrintTradeLog> outSidMultiPacksMap = getMultiPacksPrintTradeOutSidTook(staff, trades, context);
        Map<Long, WaveRule> waveIdWaveRuleMap = getWaveIdWaveRuleTook(staff, trades, context);
        //支付单相关信息
        fullTradePayTook(staff, trades, context);
        //订单发票信息
        fillTradeInvoiceTook(staff, trades, context);
        Map<Long, WaveSorting> pickNameMap = getPickNameTook(staff, trades, context);
        Map<Long, String> packCheckNameMap = getPackCheckNameMapTook(staff, trades, context);
        fillDestAndSourceNameTook(staff, trades, context);
        //订单排序准备，修复合单订单sid找不到的问题
        Map<Long, Trade> tradeMap = new HashMap<>(trades.size());
        Set<Long> tradeIds=new HashSet<>(trades.size());
        //如果需要导出关联订单，重新设置sids
        if (context.exportRelation){
            sids = trades.stream().map(Trade::getSid).toArray(Long[]::new);
        }
        for (Trade trade : trades) {
            tradeMap.put(trade.getSid(), trade);
            if (CollectionUtils.isNotEmpty(trade.getMessageMemos())) {
                for (MessageMemo me : trade.getMessageMemos()) {
                    tradeMap.put(me.getSid(), trade);
                }
            }
        }
        //订单消耗的包材商品
        Map<Long, List<PackmaItemDetailResponse>> packmaItemsDetail = getPackmaItemDetailTook(staff, trades, context);
        //物流预警信息
        Map<String, Map<String, String>> logisticsTraceInfoMap = getLogisticsTraceInfoTook(staff, trades, context, outSidMultiPacksMap);
        TradeExportData tradeExportData = new TradeExportData().setWarehouses(warehouses).setShops(shops).setFxShopInfo(fxShopInfoMap).setWaveIdWaveRuleMap(waveIdWaveRuleMap)
                .setPickNameMap(pickNameMap).setPackCheckNameMap(packCheckNameMap).setPackmaItemsDetailMap(packmaItemsDetail).setLogisticsTraceInfoMap(logisticsTraceInfoMap);
        //根据sid分组map,根据查询条件的进行排序导出
        sids = handleSelectedExport(staff, task, sids, context.exportRelation);
        for (int i = 0; i < sids.length; i++) {
            Trade trade = tradeMap.get(sids[i]);
            if(trade==null){
                continue;
            }
            //根据Tid查询合单订单有多个sid，sid会出现重复，该方法只能减少重复的概率
            if(tradeIds.contains(trade.getSid())){
                continue;
            }
            tradeIds.add(trade.getSid());
            TradeExceptionUtils.analyze(staff, trade);
            JSONObject columnJson = exportCommon(staff, trade, tradeExportData, context);
            excelContent.add(columnJson);
            MultiPacksPrintTradeLog multiPacksPrintTradeLog = outSidMultiPacksMap.get(trade.getOutSid());
            String[] outSidArray = TradeUtils.getMultiPrintOutsid(multiPacksPrintTradeLog);
            if (outSidArray != null && outSidArray.length > 0) {
                Map<String, MultiPacksPrintTradeLogDetail> outSidMutilPacksMap = multiPacksPrintTradeLog.getDetails()
                        .stream().filter(o->StringUtils.isNotBlank(o.getOutSid())).collect(Collectors.toMap(MultiPacksPrintTradeLogDetail::getOutSid,a->a,(a,b)->b));
                String cost = "",weight ="";
                for (String outSid : outSidArray) {
                    MultiPacksPrintTradeLogDetail logDetail = outSidMutilPacksMap.get(outSid);
                    if (outSid.equals(trade.getOutSid())) {
                        if(context.exportMultiPackActualInfo){
                            cost = String.valueOf(logDetail==null || logDetail.getCost() == null ? "" : logDetail.getCost());
                            weight = logDetail==null?"":MathUtils.toString(logDetail.getWeight(),4);
                        }
                        continue;
                    }
                    JSONObject jsonObject = createNewJSONObject(columnJson, false, true);
                    executePut(jsonObject, TradeExportFieldEnum.OUT_SID, outSid);
                    executePut(jsonObject, TradeExportFieldEnum.TEMPLATE_NAME, trade.getTemplateName());
                    executePut(jsonObject, TradeExportFieldEnum.LOGISTICS_TRACE_COUNT, tradeExportData.getLogisticsTraceInfo(outSid, TradeExportFieldEnum.LOGISTICS_TRACE_COUNT.getField()));
                    executePut(jsonObject, TradeExportFieldEnum.LAST_LOGISTICS_TRACE, tradeExportData.getLogisticsTraceInfo(outSid, TradeExportFieldEnum.LAST_LOGISTICS_TRACE.getField()));
                    executePut(jsonObject, TradeExportFieldEnum.LOGISTICS_MODIFIED, tradeExportData.getLogisticsTraceInfo(outSid, TradeExportFieldEnum.LOGISTICS_MODIFIED.getField()));
                    executePut(jsonObject, TradeExportFieldEnum.NEVER_MODIFIED_TIME, tradeExportData.getLogisticsTraceInfo(outSid, TradeExportFieldEnum.NEVER_MODIFIED_TIME.getField()));
                    if(context.exportMultiPackActualInfo && logDetail!=null){
                        executePut(jsonObject, TradeExportFieldEnum.ACTUAL_POST_FEE, String.valueOf(logDetail.getCost() == null ?"":logDetail.getCost()));//实付运费
                        executePut(jsonObject, TradeExportFieldEnum.WEIGHT, MathUtils.toString(logDetail.getWeight(),4));//实际重量
                    }
                    excelContent.add(jsonObject);
                }
                if(context.exportMultiPackActualInfo){
                    executePut(columnJson, TradeExportFieldEnum.ACTUAL_POST_FEE, cost);//实付运费
                    executePut(columnJson, TradeExportFieldEnum.WEIGHT, weight);//实际重量
                }
            }
        }
        tradeExportData = null;
        List<LinkedList<String>> result= getListResultByNeedFields(staff, excelContent, context.exportFieldEnums);
        Long time = (System.currentTimeMillis() - beginTime) / 1000;
        logger.info(String.format("本次订单查询数据组成时间为：【%s】，数据组装结束",time));
        return result;

    }

    //获取员工所有平台的白名单情况 排除拼多多和京东
    private Map<String,Boolean> getAllFlatformWhiteCompanyIdList(Staff staff, TradeExportResourceSwitchData switchData){
        Map<String,Boolean> result = new HashMap<String,Boolean>(10);
        if (!switchData.sensitiveAndDecryptSwitch){
            return result;
        }
        String allSecriteWhiteCompanyIds = ConfigHolder.GLOBAL_CONFIG.getModuleSwitch().getTrade().getAllSecriteWhiteCompanyIds();
        if(StringUtils.isNotBlank(allSecriteWhiteCompanyIds)){
            JSONObject jsonObject = JSONObject.parseObject(allSecriteWhiteCompanyIds);
            if(null != jsonObject){
                Set<String> keys = jsonObject.keySet();
                if(CollectionUtils.isNotEmpty(keys)){
                    for(Iterator<String> iterator = keys.iterator();iterator.hasNext();){
                        String next = iterator.next();
                        //如果是拼多多和京东则跳过
                        if(CommonConstants.PLAT_FORM_TYPE_JD.equals(next) || CommonConstants.PLAT_FORM_TYPE_PDD.equals(next)){
                            continue;
                        }
                        JSONArray jsonArray = jsonObject.getJSONArray(next);
                        boolean ifHave = false;
                        for(int i=0;i<jsonArray.size();i++){
                            Long aLong = Optional.ofNullable(Strings.getAsLong(jsonArray.getString(i), false)).orElse(0L);
                            if(aLong.equals(staff.getCompanyId())){
                                ifHave = true;
                                break;
                            }
                        }
                        result.put(next,ifHave);
                    }
                }
            }
        }
        return result;
    }

    private Map<Long, WaveRule> getWaveIdWaveRuleMap(Staff staff, List<Trade> trades) {
        List<Long> waveIds = trades.stream().filter(trade -> Objects.nonNull(trade.getWaveId()) && trade.getWaveId().longValue() > 0l).map(trade -> trade.getWaveId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(waveIds)) {
            return new HashMap<>();
        }
        Map<Long, WaveRule> waveRuleMap = tradeWaveService.queryRules(staff).stream().collect(Collectors.toMap(WaveRule::getId, waveRule -> waveRule));
        if (MapUtils.isEmpty(waveRuleMap)){
            return new HashMap<>();
        }
        return tradeWaveService.queryWaveByIds(staff, waveIds.toArray(new Long[waveIds.size()])).stream().collect(HashMap::new, (map, wave) -> map.put(wave.getId(), waveRuleMap.get(wave.getRuleId())), HashMap::putAll);
    }

    private Map<String, Map<String, String>> getLogisticsTraceInfo(Staff staff, List<Trade> trades, TradeExportContext context, Map<String, MultiPacksPrintTradeLog> outSidMultiPacksMap){
        Map<String, Map<String, String>> result = new HashMap<>();
        try {
            List<Trade> queryRecordTrades = new ArrayList<>();
            //物流预警服务调用
            getLogisticsRecordByWarningService(staff, trades, context, queryRecordTrades, result, outSidMultiPacksMap);
            //物流预警查询不到时，从发货记录服务中获取
            getLogisticsRecordByDao(staff, queryRecordTrades, result);
            //白名单 + queryId符合要求，还没有物流预警信息的订单，查询平台去获取物流预警信息
            if (tradeLocalConfigurable.isTradeExportRemoteLogisticsCompanyIds(staff.getCompanyId()) && Long.valueOf(SystemTradeQueryParamsContext.QUERY_DELIVER_EXCEPTION).equals(context.queryId)){
                getLogisticsRecordByRemoteServiceTook(staff, trades, result);
            }
        }catch (Exception e){
            logger.error("分销订单导出，获取订单物流信息出错" + e);
        }
        return result;
    }

    private void getLogisticsRecordByWarningService(Staff staff, List<Trade> trades, TradeExportContext context, List<Trade> queryRecordTrades, Map<String, Map<String, String>> result, Map<String, MultiPacksPrintTradeLog> outSidMultiPacksMap){
        List<String> outSids = getAllOutSids(trades, outSidMultiPacksMap);
        //如果不包含logisticsTraceCount(物流记录条数)这个指标，可以使用批量接口
        if (!context.switchData.logisticsCountSwitch){
            Lists.partition(outSids, 200).forEach(part ->{
                Set<LastLogisticsTraceDTO> records = logisticsWarningService.batchQueryLastLogisticsRecord(staff.getCompanyId(), part);
                for (LastLogisticsTraceDTO record : records){
                    if (StringUtils.isBlank(record.getLastLogisticsTrace())){
                        continue;
                    }
                    HashMap<String, String> logisticsTraceMap = new HashMap<>();
                    //条数默认设置为空
                    logisticsTraceMap.put(TradeExportFieldEnum.LOGISTICS_TRACE_COUNT.getField(), "");
                    logisticsTraceMap.put(TradeExportFieldEnum.LAST_LOGISTICS_TRACE.getField(), record.getLastLogisticsTrace());
                    logisticsTraceMap.put(TradeExportFieldEnum.LOGISTICS_MODIFIED.getField(), Objects.isNull(record.getLastLogisticsTime()) ? "" : DateFormatUtils.format(record.getLastLogisticsTime(), "yyyy-MM-dd HH:mm:ss"));
                    logisticsTraceMap.put(TradeExportFieldEnum.NEVER_MODIFIED_TIME.getField(), Objects.isNull(record.getLastLogisticsTime()) ? "" : intervalDays(new Date(), record.getLastLogisticsTime()));
                    result.put(record.getOutSid(), logisticsTraceMap);
                }
            });
        }else {
            //包含logisticsTraceCount(物流记录条数)这个指标，物流预警服务，单个订单调用
            for (String outSid : outSids){
                LogisticsTraceDTO dto = logisticsWarningService.getLogisticsRecord(staff, outSid);
                if (null != dto && CollectionUtils.isNotEmpty(dto.getLogisticsFullTraceList())){
                    LogisticsFullTraceList trace = dto.getLogisticsFullTraceList().get(dto.getLogisticsFullTraceList().size() - 1);
                    if (StringUtils.isBlank(trace.getDesc())){
                        continue;
                    }
                    HashMap<String, String> logisticsTraceMap = new HashMap<>();
                    logisticsTraceMap.put(TradeExportFieldEnum.LOGISTICS_TRACE_COUNT.getField(), String.valueOf(dto.getLogisticsFullTraceList().size()));
                    logisticsTraceMap.put(TradeExportFieldEnum.LAST_LOGISTICS_TRACE.getField(), trace.getDesc());
                    logisticsTraceMap.put(TradeExportFieldEnum.LOGISTICS_MODIFIED.getField(), Objects.isNull(trace.getTime()) ? "" : DateFormatUtils.format(new Date(trace.getTime()), "yyyy-MM-dd HH:mm:ss"));
                    logisticsTraceMap.put(TradeExportFieldEnum.NEVER_MODIFIED_TIME.getField(), Objects.isNull(trace.getTime()) ? "" : intervalDays(new Date(), new Date(trace.getTime())));
                    result.put(outSid, logisticsTraceMap);
                }
            }
        }
        //主单号未返回物流轨迹处理
        trades.forEach(trade -> {
            if (!result.containsKey(trade.getOutSid())){
                queryRecordTrades.add(trade);
            }
        });
    }

    private List<String> getAllOutSids(List<Trade> trades, Map<String, MultiPacksPrintTradeLog> outSidMultiPacksMap){
        Set<String> outSids = trades.stream().map(Trade::getOutSid).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (MapUtils.isEmpty(outSidMultiPacksMap)){
            return new ArrayList<>(outSids);
        }
        outSidMultiPacksMap.forEach((k, v) ->{
            if (Objects.isNull(v) || CollectionUtils.isEmpty(v.getDetails())){
                return;
            }
            v.getDetails().forEach(detail ->{
                outSids.add(detail.getOutSid());
            });
        });
        return new ArrayList<>(outSids);
    }

    private void getLogisticsRecordByDao(Staff staff, List<Trade> queryRecordTrades, Map<String, Map<String, String>> result){
        if (CollectionUtils.isNotEmpty(queryRecordTrades)){
            List<LogisticsTrackingPollPool> logisticsTrackingPollPools= logisticsTrackingRecordService.exceptLogisticsQueryBysids(staff, TradeUtils.toSids(queryRecordTrades));
            if (CollectionUtils.isEmpty(logisticsTrackingPollPools)){
                return;
            }
            logisticsTrackingPollPools.forEach(pool ->{
                if (StringUtils.isBlank(pool.getLastLogisticsTrace())){
                    return;
                }
                HashMap<String, String> logisticsTraceMap = new HashMap<>();
                logisticsTraceMap.put(TradeExportFieldEnum.LOGISTICS_TRACE_COUNT.getField(), Objects.isNull(pool.getLogisticsTraceCount()) ? "" : String.valueOf(pool.getLogisticsTraceCount()));
                logisticsTraceMap.put(TradeExportFieldEnum.LAST_LOGISTICS_TRACE.getField(), pool.getLastLogisticsTrace());
                logisticsTraceMap.put(TradeExportFieldEnum.LOGISTICS_MODIFIED.getField(), Objects.isNull(pool.getLogisticsModified()) || !pool.getLogisticsModified().after(TradeTimeUtils.INIT_DATE) ? "" : DateFormatUtils.format(pool.getLogisticsModified(), "yyyy-MM-dd HH:mm:ss"));
                logisticsTraceMap.put(TradeExportFieldEnum.NEVER_MODIFIED_TIME.getField(), Objects.isNull(pool.getLogisticsModified()) || !pool.getLogisticsModified().after(TradeTimeUtils.INIT_DATE) ? "" : intervalDays(new Date(), pool.getLogisticsModified()));
                result.put(pool.getOutSid(), logisticsTraceMap);
            });
        }
    }

    private void getLogisticsRecordByRemoteService(Staff staff, List<Trade> trades, Map<String, Map<String, String>> result){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            for (Trade trade : trades){
                Map<String, String> logistics = result.get(trade.getOutSid());
                if (null != logistics && StringUtils.isNotBlank(logistics.get(TradeExportFieldEnum.LAST_LOGISTICS_TRACE.getField()))){
                    continue;
                }
                List<TradeLogisticsTrace> tradeLogisticsTraces = logisticBusiness.searchTraceLogistics(staff, trade);
                if (CollectionUtils.isEmpty(tradeLogisticsTraces)){
                    continue;
                }
                TradeLogisticsTrace lastTrace = tradeLogisticsTraces.get(0);
                if (CollectionUtils.isEmpty(lastTrace.getTraceList())){
                    continue;
                }
                int size = lastTrace.getTraceList().size();
                TradeTransitStepInfo lastInfo = lastTrace.getTraceList().get(size - 1);
                HashMap<String, String> logisticsTraceMap = new HashMap<>();
                logisticsTraceMap.put(TradeExportFieldEnum.LOGISTICS_TRACE_COUNT.getField(), String.valueOf(size));
                logisticsTraceMap.put(TradeExportFieldEnum.LAST_LOGISTICS_TRACE.getField(), lastInfo.getStatusDesc());
                logisticsTraceMap.put(TradeExportFieldEnum.LOGISTICS_MODIFIED.getField(), Objects.isNull(lastInfo.getStatusTime()) ? "" : lastInfo.getStatusTime());
                logisticsTraceMap.put(TradeExportFieldEnum.NEVER_MODIFIED_TIME.getField(), Objects.isNull(lastInfo.getStatusTime()) ? "" : intervalDays(new Date(), format.parse(lastInfo.getStatusTime())));
                result.put(trade.getOutSid(), logisticsTraceMap);
            }
        }catch (Exception e){
            logger.error("分销订单导出，通过平台获取订单物流信息出错" + e);
        }
    }

    private Map<Long, List<PackmaItemDetailResponse>> getPackmaItemDetail(Staff staff, List<Trade> trades){
        Map<Long, List<PackmaItemDetailResponse>> result = new HashMap<>();
        List<Long> sids = new ArrayList<>();
        trades.forEach(t->{
            if (CollectionUtils.isNotEmpty(t.getMessageMemos())){
                t.getMessageMemos().forEach(memo ->{
                    sids.add(memo.getSid());
                });
            }else {
                sids.add(t.getSid());
            }
        });
        List<PackmaItemDetailResponse> responses = itemServiceDubbo.queryPackmaItemDetail(staff, sids);
        if (CollectionUtils.isEmpty(responses)){
            return result;
        }

        for (PackmaItemDetailResponse response : responses){
            result.computeIfAbsent(response.getSid(), t -> new ArrayList<>()).add(response);
        }
        return result;
    }

    private Map<String, TbItem> queryTbItemInfo(Staff staff, List<Trade> trades){
        Map<String, TbItem> result = new HashMap<>();
        try {
            List<Order> orders = new ArrayList<>();
            Set<String> keys = new HashSet<>();
            for (Order order : TradeUtils.getOrders4Trade(trades)){
                if (StringUtils.isBlank(order.getNumIid())){
                    continue;
                }
                if (keys.add(buildTbKey(order))){
                    orders.add(order);
                }
            }
            Lists.partition(orders, 200).forEach(part ->{
                Set<String> numIidList = new HashSet<>();
                Set<String> skuIdList = new HashSet<>();
                part.forEach(order -> {
                    numIidList.add(order.getNumIid());
                    if (StringUtils.isNotBlank(order.getSkuId())){
                        skuIdList.add(order.getSkuId());
                    }
                });
                List<TbItem> tbItems = itemServiceDubbo.queryTbItemListNew(staff, new ArrayList<>(numIidList), new ArrayList<>(skuIdList));
                if (CollectionUtils.isEmpty(tbItems)){
                    return;
                }
                for (TbItem tbItem : tbItems){
                    if (CollectionUtils.isEmpty(tbItem.getSkuList())){
                        result.put(tbItem.getNumIid() + "_0", tbItem);
                        continue;
                    }
                    for (TbSku tbSku : tbItem.getSkuList()){
                        result.put(tbSku.getNumIid() + "_" + tbSku.getSkuId(), tbSku);
                    }
                }
            });
        }catch (Exception e){
            logger.error(LogHelper.buildLogHead(staff).append("导出订单流程-平台商品信息查询失败"), e);
        }
        return result;
    }

    private String buildTbKey(Order order){
        return order.getNumIid() + "_" + (StringUtils.isBlank(order.getSkuId()) ? "0" : order.getSkuId());
    }

    private Map<Long,WaveSorting> getPickNameMap(Staff staff,List<Trade> trades) {
        try {
            List<List<Trade>> partitions = Lists.partition(trades, 500);
            Map<Long, WaveSorting> pickNameMap = Maps.newHashMapWithExpectedSize(trades.size());
            for (List<Trade> tradeLists : partitions) {
                List<Trade> waveTrades = tradeLists.stream().filter(trade -> trade.getWaveId() > 0).collect(Collectors.toList());
                List<Long> sidList = waveTrades.stream().map(Trade::getSid).collect(Collectors.toList());
                List<Long> waveIdList = waveTrades.stream().map(Trade::getWaveId).collect(Collectors.toList());
                pickNameMap.putAll(tradeWaveService.queryPickerNamesBySid(staff, sidList, waveIdList));
            }
            return pickNameMap;
        }catch (Exception e){
            logger.error(LogHelper.buildLogHead(staff).append("导出订单流程-波次打印信息查询失败"), e);
            return new HashMap<>();
        }
    }

    private Map<Long,String> getPackCheckNameMap(Staff staff,List<Trade> trades) {
        try {
            List<List<Trade>> partitions = Lists.partition(trades, 500);
            Map<Long, String> packCheckNameMap = Maps.newHashMapWithExpectedSize(trades.size());
            for (List<Trade> tradeLists : partitions) {
                List<Long> sidList = tradeLists.stream().map(Trade::getSid).collect(Collectors.toList());
                Map<Long, TradePerformanceOptLog> map = tradeWaveService.queryTradePackLogBySid(staff, sidList);
                if (map != null) {
                    for (Map.Entry<Long, TradePerformanceOptLog> entry : map.entrySet()) {
                        packCheckNameMap.put(entry.getKey(),entry.getValue().getStaffName());
                    }
                }
            }
            return packCheckNameMap;
        }catch (Exception e){
            logger.error(LogHelper.buildLogHead(staff).append("导出订单流程-验货员信息查询失败"), e);
            return new HashMap<>();
        }
    }

    private Map<String, MultiPacksPrintTradeLog> getMultiPacksPrintTradeOutSid(Staff staff, List<Trade> trades, TradeExportContext context) {
        List<Trade> tradeList = trades.stream().filter(trade -> trade.getTemplateId() != null && trade.getTemplateId() > 0 && trade.getTemplateType() != null && trade.getTemplateType() > 0 && TradeStatusUtils.getPrintStatus(trade.getExpressPrintTime()).equals(1)).collect(Collectors.toList());
        Map<String, MultiPacksPrintTradeLog> result = new HashMap<>();
        boolean query_3month_ago = Objects.nonNull(context.queryId) && context.queryId == SystemTradeQueryParamsContext.QUERY_3MONTH_AGO;
        if (CollectionUtils.isNotEmpty(tradeList) && !query_3month_ago) {
            Map<Long, UserWlbExpressTemplate> templateMap = new HashMap<>();
            List<String> jdTemplateTrade = new ArrayList<>();
            List<Trade> needSearch = new ArrayList<>();
            for (Trade trade : tradeList) {
                if (TradeStatusUtils.getPrintStatus(trade.getExpressPrintTime()).equals(1)
                        && trade.getTemplateId() != null
                        && trade.getTemplateId() > 0
                        && Integer.valueOf(1).equals(trade.getTemplateType())) {
                    needSearch.add(trade);
                    UserWlbExpressTemplate template = templateMap.computeIfAbsent(trade.getTemplateId(), templateId -> {
                        UserWlbExpressTemplate userWlbExpressTemplate = userWlbExpressTemplateService.userQuery(staff, templateId, false);
                        return null == userWlbExpressTemplate ? new UserWlbExpressTemplate() : userWlbExpressTemplate;
                    });
                    if (WlbTemplateTypeEnum.JD.getValue().equals(template.getWlbTemplateType()) && Integer.valueOf(0).equals(template.getIsManulfill())) {
                        jdTemplateTrade.add(trade.getOutSid());
                    }
                }
            }
            if (needSearch.size() > 0 ){
                Map<String, MultiPacksPrintTradeLog> multiLog = multiPacksPrintTradeLogService.queryByOutSidsNew(staff, Lists.newArrayList(TradeUtils.toOutSids(trades)))
                        .stream().filter(l->StringUtils.isNotBlank(l.getOutSid())).collect(Collectors.toMap(MultiPacksPrintTradeLog::getOutSid,a->a,(a,b)->a));
                if (MapUtils.isNotEmpty(multiLog)) {
                    for (Map.Entry<String,MultiPacksPrintTradeLog> entry : multiLog.entrySet()) {
                        String outSid = entry.getKey();
                        MultiPacksPrintTradeLog log = entry.getValue();
                        if (!jdTemplateTrade.contains(outSid) && log!=null) {
                            result.put(outSid, log);
                        }
                    }
                }
            }
        }
        return result;
    }


    /**
     * 导出子订单, 导出工程里三个月内订单与归档订单导出流程区分开了，exportOrderNew：三个月内订单导出；exportOrder：归档订单导出
     *
     * @param staff
     * @param task 导出工程导出任务
     * @param sids sid列表
     * @return
     */
    public List<LinkedList<String>> exportOrder(Staff staff, TradeExportParams exportParams, boolean isAsync, TradeExcelExportTask task, Long[] sids) {
        if (isAsync) {
            try {
                launchAsyncOrderExportEvent(staff, exportParams);
            } catch (Exception e) {
                logger.error(LogHelper.buildLogHead(staff).append("订单明细导出失败"), e);
            }
            return null;
        }
        Long beginTime=System.currentTimeMillis();
        TradeExportContext context = getTradeExportContext(staff, exportParams, task, TradeExcelExportTask.TASK_TYPE_TRADE_ORDER);
        //查询数据
        List<Trade> trades = queryExportTradesTook(staff, exportParams, sids);
        trades = queryRelationTradeTook(staff, trades, context);
        trades = handleSelectedExport(staff,task,trades);
        //数据为空
        if (trades == null || trades.size() == 0) {
            return null;
        }
        fillFxSourceTradeTook(staff, trades, context);
        fillPlatFormIdTook(staff, trades, context);
        fillGxTemplateNameTook(staff, context, trades);
        sensitiveTradeTook(staff, trades, context);
        Map<Long, Shop> fxShopInfoMap = getFxUserShopTook(staff, trades, context);
        Map<Long, Shop> shops = getUserShopsTook(staff, trades, context);
        Map<Long, Warehouse> warehouses = getUserWarehousesTook(staff, trades, context);
        //一单多包记录
        Map<String, MultiPacksPrintTradeLog> outSidMultiPacksMap = getMultiPacksPrintTradeOutSidTook(staff, trades, context);
        List<JSONObject> jsonData = new ArrayList<JSONObject>();
        Map<Long, WaveRule> waveIdWaveRuleMap = getWaveIdWaveRuleTook(staff, trades, context);
        //支付单相关信息
        fullTradePayTook(staff, trades, context);
        //订单发票信息
        fillTradeInvoiceTook(staff, trades, context);
        Map<Long, WaveSorting> pickNameMap = getPickNameTook(staff, trades, context);
        Map<Long, String> packCheckNameMap = getPackCheckNameMapTook(staff, trades, context);
        fillDestAndSourceNameTook(staff, trades, context);
        //订单消耗的包材商品
        Map<Long, List<PackmaItemDetailResponse>> packmaItemsDetail = getPackmaItemDetailTook(staff, trades, context);
        //查询平台商品信息
        Map<String, TbItem> tbItemMap = queryTbItemInfoTook(staff, trades, context);
        //物流预警信息
        Map<String, Map<String, String>> logisticsTraceInfoMap = getLogisticsTraceInfoTook(staff, trades, context, null);
        //供应商名称
        Map<String, ItemSupplierBridge> trade2ItemSupplierMap = handleItemInfo2TradeTook(staff, trades, context);
        TradeExportData tradeExportData = new TradeExportData().setWarehouses(warehouses).setShops(shops).setFxShopInfo(fxShopInfoMap).setWaveIdWaveRuleMap(waveIdWaveRuleMap).setPickNameMap(pickNameMap)
                .setPackCheckNameMap(packCheckNameMap).setPackmaItemsDetailMap(packmaItemsDetail).setLogisticsTraceInfoMap(logisticsTraceInfoMap).setTbItemMap(tbItemMap).setTrade2ItemSupplierMap(trade2ItemSupplierMap);
        for (Trade trade : trades) {
            List<Order> noRepeatList1 = new ArrayList<>();
            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isEmpty(orderList)) {
                continue;
            }
            TradeExceptionUtils.analyze(staff, trade);
            JSONObject tradeJSON = exportCommon(staff, trade, tradeExportData, context);
            setMultiOutSids(staff, trade, tradeJSON, outSidMultiPacksMap);
            for (int i = 0; i < orderList.size(); i++) {
                Order order = orderList.get(i);
                if ((3 == order.getType() || 4 == order.getType()) && null != order.getCombineId() && order.getCombineId()>0) {
                    continue;
                }
                order.setScalping(trade.getScalping());
                noRepeatList1.add(order);
                Map<Long, List<Order>> collect = noRepeatList1.stream().collect(Collectors.groupingBy(Order::getSid));
                JSONObject orderJson = createNewJSONObject(tradeJSON, context.needCopyTrade || i == 0, collect.get(order.getSid()).size() == 1);

                if (context.needCopyTrade && i > 0) {//再过滤
                    orderJson = filterTradeColumn(orderJson);
                }

                boolean isSuite = order.getSuits() != null && !order.getSuits().isEmpty() && 2 == order.getType();
                Double doublePayment = Strings.getAsDouble(order.getPayment(), false);
                Double doublePrice = Strings.getAsDouble(order.getPrice(), false);
                Double doubleDiscountFee = Strings.getAsDouble(order.getDiscountFee(), false);
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_ORDER_ID, order.getId() + "");//order唯一标示
                executePut(orderJson, TradeExportFieldEnum.ORDER_ITEM_SYS_ID, order.getItemSysId() + "");//order商家编码
                executePut(orderJson, TradeExportFieldEnum.ORDER_SKU_SYS_ID, order.getSkuSysId()+"");//order商家规格编码
                if (order.getItemSysId() != null && order.getItemSysId() > 0) {
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_MAIN_OUTER_ID, order.getSysOuterId());//主商家编码
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_OUTER_ID, order.getSysOuterId());//商家编码
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_TITLE, order.getSysTitle());//商品名称
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SHORT_TITLE, order.getShortTitle());//商品简称
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SKU_PROPERTIES_NAME, order.getSysSkuPropertiesName());//规格属性
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SKU_PROPERTIES_ALIAS, order.getSysSkuPropertiesAlias());//规格别名
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SKU_REMARK, StringUtils.isNotBlank(order.getSysSkuRemark()) ? order.getSysSkuRemark() : order.getSysItemRemark());//商品/规格备注

                } else {
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_MAIN_OUTER_ID, StringUtils.isNotBlank(order.getOuterSkuId()) ? order.getOuterSkuId() : order.getOuterId());//主商家编码
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_OUTER_ID, StringUtils.isNotBlank(order.getOuterSkuId()) ? order.getOuterSkuId() : order.getOuterIid());//商家编码
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_TITLE, order.getTitle());//商品名称
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SHORT_TITLE, order.getShortTitle());//商品简称
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SKU_PROPERTIES_NAME, order.getSkuPropertiesName());//规格属性
                }
                executePut(orderJson, TradeExportFieldEnum.ORDER_PLAT_SKU_PROPERTIES, CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource()) ? "" : order.getSkuPropertiesName());//平台规格名称
                String lockNum;
                //缺货数量
                if (OrderExceptUtils.isContainsExcept(staff,order, ExceptEnum.UNALLOCATED)) {
                    lockNum = "商品未匹配";
                } else {
                    //咖啡测试3 tid 2702780729593344 退款不锁库存的订单号 ，导出订单明细这里缺货数量应该是0.。。。空单也有问题，顺手改下 马鹏程2020-04-15 17：13
                    if(TradeStockUtils.needNotApplyStock(staff,order, context.tradeConfig)){
                        lockNum = "0";
                    }else{
                        lockNum = order.getNum() - (order.getStockNum() == null ? 0 : order.getStockNum()) + "";
                    }
                }
                executePut(orderJson, TradeExportFieldEnum.ORDER_LACK_NUM, lockNum);

                executePutMoney(staff,orderJson, TradeExportFieldEnum.COST, context.hasPowerCost, (order.getItemSysId() > 0 ? (order.getCost() == null ? TradeUtils.doubleToString(0.00) : TradeUtils.doubleToString(order.getCost() * order.getNum())) : "0"));//成本价
                executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_PAYMENT, context.hasPowerPayment, order.getPayment());//商品实付金额
                executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_SALE_FEE, context.hasPowerFxPrice, order.getSaleFee());//商品分销金额
                executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_PAY_AMOUNT, context.hasPowerPayment , order.getPayAmount() );//商品买家已付金额
                executePut(orderJson, TradeExportFieldEnum.ORDER_PLATFORM_DISCOUNT_FEE, context.hasPowerPayment ? order.getPlatformDiscountFee() : "***");
                executePut(orderJson, TradeExportFieldEnum.ORDER_NUM, order.getNum() + "");//数量
                executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_UNIT_PRICE, context.hasPowerPayment , order.getPrice() );//单价
                Double discountedPrice = (null == order.getNum() || Integer.valueOf(0).equals(order.getNum()) || null == doublePayment) ? null : doublePayment / order.getNum();
                executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_PRICE, context.hasPowerPayment , discountedPrice );//折后价
                //折扣
                executePut(orderJson, TradeExportFieldEnum.ORDER_DISCOUNT_RATE, context.hasPowerPayment ? (null == discountedPrice || null == doublePrice ||  Double.valueOf(0.0).equals(doublePrice) ? "" : String.format("%.2f", discountedPrice / doublePrice * 100) + "%") : "***");
                executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_DISCOUNT_FEE, context.hasPowerOrderDiscountFee , ((null == doubleDiscountFee || null == order.getNum() || Integer.valueOf(0).equals(order.getNum())) ? null : (doubleDiscountFee / order.getNum())) );//优惠金额
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_STATUS, TradeStatusUtils.convertOrderSysStatus(order));//子单状态
                executePut(orderJson, TradeExportFieldEnum.ORDER_CONSIGN_TIME, DateUtil.format(order.getConsignTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));//商品发货时间
                executePut(orderJson, TradeExportFieldEnum.ORDER_PT_CONSIGN_TIME, DateUtil.format(order.getPtConsignTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));//商品发货时间
                executePut(orderJson, TradeExportFieldEnum.ORDER_NUM_iid, order.getNumIid());
                executePut(orderJson, TradeExportFieldEnum.ORDER_SKU_ID, order.getSkuId());
                executePut(orderJson, TradeExportFieldEnum.ORDER_OUTER_SKU_ID, order.getOuterSkuId());
                executePut(orderJson, TradeExportFieldEnum.ORDER_OUTER_IID, order.getOuterIid());
                executePut(orderJson, TradeExportFieldEnum.ORDER_PLAT_OUTER_ID, OrderUtils.getPlatOuterId(order));
                if (context.btasCodeExport){
                    executePut(orderJson, TradeExportFieldEnum.ORDER_BTAS_CODE, String.join(",", Optional.ofNullable(OrderUtils.getBtasOrderCode(order)).orElse(new ArrayList<>())));
                }
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SID, String.valueOf(order.getSid()));
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_TID, String.valueOf(order.getTid()));
                executePut(orderJson, TradeExportFieldEnum.ORDER_TITLE, order.getTitle());
                executePut(orderJson, TradeExportFieldEnum.ORDER_ESTIMATE_CON_TIME, DateUtil.format(order.getEstimateConTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_CONSIGNED, getSysConsigned(order.getSysConsigned()));
                executePut(orderJson,TradeExportFieldEnum.ORDER_DIVIDE_ORDER_FEE,StringUtils.isNotEmpty(order.getDivideOrderFee())?order.getDivideOrderFee():"0");
                executePut(orderJson, TradeExportFieldEnum.ORDER_VOLUME, order.getVolume() == null ? "" : String.valueOf(order.getVolume()));
                executePut(orderJson, TradeExportFieldEnum.ORDER_OID, order.getOid() == null ? "" : String.valueOf(order.getOid()));
                handleItemSupplierInfo(orderJson, order, tradeExportData);
                executePut(orderJson, TradeExportFieldEnum.ORDER_IDENT_CODE, getIdentCode(order));
                executePut(orderJson, TradeExportFieldEnum.ORDER_PT_BARCODE, tradeExportData.getPtBarcode(buildTbKey(order)));

                //达人相关信息
                if(context.switchData.orderExtInfoSwitch){
                    handleAuthorInfo(orderJson, order);
                    executePut(orderJson, TradeExportFieldEnum.ORDER_EXT_ORDER_REMARK,!Objects.isNull(order.getOrderExt())?(StringUtils.isNotEmpty(order.getOrderExt().getOrderRemark())?order.getOrderExt().getOrderRemark():""):"");
                }


                //付款金额=单价*数量*折扣
                // 单价 = 付款金额 / (数量* 折扣)
                if (isSuite && context.switchData.exportSuitsSwitch) {
                    Order son = order.getSuits().get(0);
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_ITEM_SYS_ID,  son.getItemSysId() + "");
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SKU_SYS_ID,  son.getSkuSysId() + "");
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SYS_OUTER_ID,  son.getSysOuterId());
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SYS_TITLE, son.getSysTitle());
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SHORT_TITLE, son.getShortTitle());
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SYS_SKU_PROPERTIES_NAME, son.getSysSkuPropertiesName());
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SKU_PROPERTIES_ALIAS, son.getSysSkuPropertiesAlias());
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_NUM, String.valueOf(son.getNum()));
                    executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_SUIT_PAYMENT, context.hasPowerPayment , son.getPayment());
                    executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_SUIT_PAY_AMOUNT, context.hasPowerPayment , son.getPayAmount());
                    //executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_SUIT_DIVIDE_ORDER_FEE, true ,son.getDivideOrderFee());
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SID, String.valueOf(son.getSid()));
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_TID, String.valueOf(son.getTid()));
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_CONSIGNED, getSysConsigned(son.getSysConsigned()));
                    executePut(orderJson, TradeExportFieldEnum.ORDER_IDENT_CODE, son.getIdentCode());
                    executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SYS_ORDER_ID, son.getId() + "");
                    executePut(orderJson, TradeExportFieldEnum.ORDER_OID, son.getOid() + "");
                }
                jsonData.add(orderJson);
                if (isSuite && context.switchData.exportSuitsSwitch) {
                    for (int j = 1; j < order.getSuits().size(); j++) {
                        Order son = order.getSuits().get(j);
                        JSONObject suitJSON = createNewJSONObject(orderJson, context.needCopyTrade, true);
                        hidenDetail(suitJSON,true);
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_ITEM_SYS_ID,  son.getItemSysId() + "");
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SKU_SYS_ID,  son.getSkuSysId() + "");
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SYS_OUTER_ID, son.getSysOuterId());
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SYS_TITLE, son.getSysTitle());
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SHORT_TITLE, son.getShortTitle());
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SYS_SKU_PROPERTIES_NAME, son.getSysSkuPropertiesName());
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SKU_PROPERTIES_ALIAS, son.getSysSkuPropertiesAlias());
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_NUM, String.valueOf(son.getNum()));
                        executePutMoney(staff,suitJSON, TradeExportFieldEnum.ORDER_SUIT_PAYMENT, context.hasPowerPayment ,son.getPayment());
                        executePutMoney(staff,suitJSON, TradeExportFieldEnum.ORDER_SUIT_PAY_AMOUNT, context.hasPowerPayment , son.getPayAmount());
                        //executePutMoney(staff,suitJSON, TradeExportFieldEnum.ORDER_SUIT_DIVIDE_ORDER_FEE, true ,son.getDivideOrderFee());
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SYS_SID, String.valueOf(son.getSid()));
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SYS_TID, String.valueOf(son.getTid()));
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SYS_CONSIGNED, getSysConsigned(son.getSysConsigned()));
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_IDENT_CODE, son.getIdentCode());
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SYS_ORDER_ID, son.getId() + "");
                        executePut(suitJSON, TradeExportFieldEnum.ORDER_OID, son.getOid() + "");
                        jsonData.add(suitJSON);
                    }
                }
            }
        }
        tradeExportData = null;
        //需要查询商品信息
        handleItemInfoTook(staff, jsonData, trades, context);
        // 导出商品货位
        handleGsTook(staff, trades, jsonData, context);
        handleUniqueCodeTook(staff, trades, jsonData, context);
        List<LinkedList<String>> result = getListResultByNeedFields(staff, jsonData, context.exportFieldEnums);
        Long time = (System.currentTimeMillis() - beginTime) / 1000;
        logger.info(String.format("本次归档订单查询数据组成时间为：【%s】，数据组装结束",time));
        return result;
    }

    private void handleItemSupplierInfo(JSONObject json, Order order, TradeExportData tradeExportData) {
        if (MapUtils.isEmpty(tradeExportData.getTrade2ItemSupplierMap())){
            return;
        }
        ItemSupplierBridge supplierBridge;
        if (order.getSkuSysId() > 0) {
            supplierBridge = tradeExportData.getTrade2ItemSupplierMap().get(order.getItemSysId() + "_" + order.getSkuSysId());
        }else {
            supplierBridge = tradeExportData.getTrade2ItemSupplierMap().get(order.getItemSysId() + "_" + 0);
        }
        if (Objects.isNull(supplierBridge)){
            return;
        }
        executePut(json, TradeExportFieldEnum.ORDER_SUPPLIER_CODE, supplierBridge.getSupplierCode());
        executePut(json, TradeExportFieldEnum.ORDER_SUPPLIER_NAME, supplierBridge.getSupplierName());
    }

    private void handleAuthorInfo(JSONObject orderJson, Order order){

        String authorId = "";
        String authorName = "";
        OrderExt orderExt = order.getOrderExt();
        if(orderExt != null){
            if(StringUtils.isNotBlank(orderExt.getAuthorNo())){
                authorId = orderExt.getAuthorNo();
            } else if(orderExt.getAuthorId() != null){
                authorId = orderExt.getAuthorId().toString();
            }
            if(StringUtils.isNotBlank(orderExt.getAuthorName())){
                authorName = orderExt.getAuthorName();
            }
        }
        executePut(orderJson, TradeExportFieldEnum.ORDER_EXT_AUTHOR_ID, authorId);
        executePut(orderJson, TradeExportFieldEnum.ORDER_EXT_AUTHOR_NAME, authorName);
    }


    public List<LinkedList<String>> exportOrderNew(Staff staff, TradeExcelExportTask task, Long[] sids) {
        Long beginTime=System.currentTimeMillis();
        TradeExportContext context = getTradeExportContext(staff, null, task, TradeExcelExportTask.TASK_TYPE_TRADE_ORDER);
        //查询数据
        List<Trade> trades = tradeSearchService.queryBySids(staff, true, sids);
        //订单关联数据查询
        trades = queryRelationTradeTook(staff, trades, context);
        //数据为空
        if (trades == null || trades.size() == 0) {
            return null;
        }
        logger.info(String.format("任务Id【%s】,本次查询sid数量【%s】",task.getTaskKey(),trades.size()));
        fillFxSourceTradeTook(staff, trades, context);
        fillPlatFormIdTook(staff, trades, context);
        fillGxTemplateNameTook(staff, context, trades);
        sensitiveTradeTook(staff, trades, context);
        Map<Long, Shop> shops = getUserShopsTook(staff, trades, context);
        Map<Long, Warehouse> warehouses = getUserWarehousesTook(staff, trades, context);
        Map<Long, Shop> fxShopInfoMap = getFxUserShopTook(staff, trades, context);
        //一单多包记录
        Map<String, MultiPacksPrintTradeLog> outSidMultiPacksMap = getMultiPacksPrintTradeOutSidTook(staff, trades, context);
        List<JSONObject> jsonData = new ArrayList<JSONObject>();
        Map<Long, WaveRule> waveIdWaveRuleMap = getWaveIdWaveRuleTook(staff, trades, context);
        //支付单相关信息
        fullTradePayTook(staff, trades, context);
        //订单发票信息
        fillTradeInvoiceTook(staff, trades, context);
        Map<Long, WaveSorting> pickNameMap = getPickNameTook(staff, trades, context);
        Map<Long, String> packCheckNameMap = getPackCheckNameMapTook(staff, trades, context);
        fillDestAndSourceNameTook(staff, trades, context);
        //订单排序准备，修复合单订单sid找不到的问题
        Map<Long, Trade> tradeMap = new HashMap<>(trades.size());
        Set<Long> tradeIds=new HashSet<>(trades.size());
        //如果需要导出关联订单，重新设置sids
        if (context.exportRelation){
            sids = trades.stream().map(Trade::getSid).toArray(Long[]::new);
        }
        for (Trade trade : trades) {
            tradeMap.put(trade.getSid(), trade);
            if (CollectionUtils.isNotEmpty(trade.getMessageMemos())) {
                for (MessageMemo me : trade.getMessageMemos()) {
                    tradeMap.put(me.getSid(), trade);
                }
            }
        }
        //订单消耗的包材商品
        Map<Long, List<PackmaItemDetailResponse>> packmaItemsDetail = getPackmaItemDetailTook(staff, trades, context);
        //查询平台商品信息
        Map<String, TbItem> tbItemMap = queryTbItemInfoTook(staff, trades, context);
        //物流预警信息
        Map<String, Map<String, String>> logisticsTraceInfoMap = getLogisticsTraceInfoTook(staff, trades, context, null);
        //供应商名称
        Map<String, ItemSupplierBridge> trade2ItemSupplierMap = handleItemInfo2TradeTook(staff, trades, context);
        TradeExportData tradeExportData = new TradeExportData().setWarehouses(warehouses).setShops(shops).setFxShopInfo(fxShopInfoMap).setWaveIdWaveRuleMap(waveIdWaveRuleMap).setPickNameMap(pickNameMap)
                .setPackCheckNameMap(packCheckNameMap).setPackmaItemsDetailMap(packmaItemsDetail).setLogisticsTraceInfoMap(logisticsTraceInfoMap).setTbItemMap(tbItemMap).setTrade2ItemSupplierMap(trade2ItemSupplierMap);
        //根据sid分组map,根据查询条件的进行排序导出
        sids = handleSelectedExport(staff,task,sids,context.exportRelation);
        for (int i = 0; i < sids.length; i++) {
            Trade trade = tradeMap.get(sids[i]);
            if (trade == null) {
                continue;
            }
            //根据Tid查询合单订单有多个sid，sid会出现重复，该方法只能减少重复的概率
            if(tradeIds.contains(trade.getSid())){
                continue;
            }
            tradeIds.add(trade.getSid());
            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
            if (CollectionUtils.isEmpty(orderList)) {
                continue;
            }
            TradeExceptionUtils.analyze(staff, trade);
            JSONObject tradeJSON = exportCommon(staff,trade, tradeExportData, context);
            setMultiOutSids(staff, trade, tradeJSON, outSidMultiPacksMap);
            buildExportOrderField(staff, orderList, jsonData, trade, tradeJSON, tradeExportData, context);
        }
        tradeExportData = null;
        //需要查询商品信息,按需查找
        handleItemInfoTook(staff, jsonData, trades, context);
        // 导出商品货位
        handleGsTook(staff, trades, jsonData, context);
        handleUniqueCodeTook(staff, trades, jsonData, context);
        Long time = (System.currentTimeMillis() - beginTime) / 1000;
        List<LinkedList<String>>  result = getListResultByNeedFields(staff, jsonData, context.exportFieldEnums);
        logger.info(String.format("本次订单明细查询数据组成时间为：【%s】，数据组装结束",time));
        return result;
    }

    /**
     *
     * 工单：K6IZ1iPDs4CQiE
     *
     * 方案：后端做判断 如果传入page对象为空 则认定为前端界面勾选 按传入的sids的顺序对查询后的订单做内存排序
     *
     * @param staff
     * @param task
     * @param sids
     * @return
     */
    private Long[]  handleSelectedExport(Staff staff, TradeExcelExportTask task,Long[] sids,boolean exportRelation) {

        List<Long> selectSids = task.getSelectedSids();
        if(CollectionUtils.isEmpty(selectSids) || org.apache.commons.lang3.ArrayUtils.isEmpty(sids) || exportRelation){
            return sids;
        }
        return selectSids.toArray(new Long[0]);
    }

    /**
     *
     * 工单：K6IZ1iPDs4CQiE
     *
     * 方案：后端做判断 如果传入page对象为空 则认定为前端界面勾选 按传入的sids的顺序对查询后的订单做内存排序
     *
     * @param staff
     * @param task
     * @param trades
     * @return
     */
    private List<Trade> handleSelectedExport(Staff staff, TradeExcelExportTask task,List<Trade> trades) {
        if(task == null){
            return trades;
        }
        List<Long> selectSids = task.getSelectedSids();
        if(CollectionUtils.isEmpty(selectSids) || CollectionUtils.isEmpty(trades)){
            return trades;
        }
        Map<Long,Trade> sid2TradeMap = trades.stream().filter(t->Objects.nonNull(t.getSid())).collect(Collectors.toMap(Trade::getSid,a->a,(a,b)->a));
        List<Trade> resultTrades = Lists.newArrayListWithExpectedSize(trades.size());
        selectSids.forEach(sid-> Optional.ofNullable(sid2TradeMap.get(sid)).ifPresent(resultTrades::add));
        return resultTrades;
    }

    /**
     * 导出商品货位
     */
    private void handleGs(Staff staff, List<Trade> trades, List<JSONObject> jsonData) {
        List<Long> orderIds = WaveUtils.getOrdersForWave(TradeUtils.getOrders4Trade(trades), Boolean.TRUE).stream().map(Order::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        List<GoodsSectionOrderRecord> gsRecords = WmsUtils.isNewWms(staff)
                ? wmsService.queryGoodsSectionInfoByOrderIdsOfNewWms(staff, orderIds)
                : wmsService.queryGoodsSectionInfoByOrderIds(staff, orderIds);
        Map<Long, List<GoodsSectionOrderRecord>> orderId2RecordMap = gsRecords.stream().collect(Collectors.groupingBy(GoodsSectionOrderRecord::getOrderId));

        for (JSONObject json : jsonData) {
            Long orderId = Optional.ofNullable(json.getLong(TradeExportFieldEnum.ORDER_SUIT_SYS_ORDER_ID.getField())).orElse(json.getLong(TradeExportFieldEnum.ORDER_SYS_ORDER_ID.getField()));
            if (orderId == null) {
                continue;
            }
            List<GoodsSectionOrderRecord> goodsSectionOrderRecords = orderId2RecordMap.get(orderId);
            String gs = CollectionUtils.isEmpty(goodsSectionOrderRecords) ? "" : goodsSectionOrderRecords.stream().map(g -> g.getGoodsSectionDisplay() + "*" + g.getApplyNum()).collect(Collectors.joining("；"));
            executePut(json, TradeExportFieldEnum.ORDER_GS, gs);
        }
    }

    private void handleUniqueCode(Staff staff, List<Trade> trades, List<JSONObject> jsonData, TradeExportContext context) {
        if (!context.openItemUniqueCodeOrOrderUniqueCode) {
            return;
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(trades);
        List<Long> orderIds = new ArrayList<>();
        for (Order order : orderList) {
            orderIds.add(order.getId());
            if (CollectionUtils.isNotEmpty(order.getSuits())) {
                for (Order suit : order.getSuits()) {
                    orderIds.add(suit.getId());
                }
            }
        }

        Lists.partition(orderIds, 1000).forEach(ids -> {
            ItemUniqueCodeQueryParams params = new ItemUniqueCodeQueryParams();
            // 排除已取消的
            params.setNotStatusList(Lists.newArrayList(OrderUniqueCodeStatusEnum.CANCEL.getType()));
            Page page = new Page();
            page.setPageSize(1000);
            params.setPage(page);
            params.setOrderIds(ids);

            Map<Long,List<String>> unionCodes = new HashMap<>();
            while (true){
                List<WaveUniqueCode> uniqueCodes= itemUniqueCodeService.queryByOrderIds(staff, params);
                if (CollectionUtils.isEmpty(uniqueCodes)) {
                    break;
                }
                for (WaveUniqueCode waveUniqueCode : uniqueCodes) {
                    Long orderId = waveUniqueCode.getOrderId();
                    unionCodes.computeIfAbsent(orderId,x->new ArrayList<>()).add(waveUniqueCode.getUniqueCode());
                }
                if (uniqueCodes.size() < page.getPageSize()) {
                    break;
                }
                page.setPageNo(page.getPageNo() +1);
            }

            for (JSONObject json : jsonData) {
                // 假如有一个套件商品A  其下子商品包含 a1,a2, 那么这里实际导出的excel结构会包含两行数据
                //   ORDER_SYS_ORDER_ID    商家编码    ORDER_SUIT_SYS_ORDER_ID  套件明细商家编码   唯一码标签
                //       1                    A            11                     a1           (商品A 和 a1 )
                //                                         12                     a2           (商品 a2 )
                // 对于第一行,实际需要将主商品A 及套件子商品 a1 的唯一码都展示出来,也就是 Id 11 和 1 对应的值

                List<String> list = new ArrayList<>();
                Long orderId =  json.getLong(TradeExportFieldEnum.ORDER_SUIT_SYS_ORDER_ID.getField());
                if (orderId != null && unionCodes.containsKey(orderId)) {
                    list.addAll(unionCodes.get(orderId));
                }
                orderId =  json.getLong(TradeExportFieldEnum.ORDER_SYS_ORDER_ID.getField());
                if (orderId != null && unionCodes.containsKey(orderId)) {
                    list.addAll(unionCodes.get(orderId));
                }
                if (CollectionUtils.isNotEmpty(list)) {
                    String code = CollectionUtils.isEmpty(list) ? "" : Strings.join(",",list);
                    executePut(json, TradeExportFieldEnum.ORDER_UNIQUE_CODE, code);
                }
            }
        });
    }

    private void buildExportOrderField(Staff staff, List<Order> orderList, List<JSONObject> jsonData, Trade trade, JSONObject tradeJSON, TradeExportData tradeExportData, TradeExportContext context) {
        List<Order> noRepeatList = new ArrayList<>(orderList.size());
        for (int i = 0; i < orderList.size(); i++) {
            Order order = orderList.get(i);
            Integer orderNum=order.getNum();
            Integer orderStockNum=order.getStockNum();
            Double doublePrice = Strings.getAsDouble(order.getPrice(), false);
            Double doublePayment = Strings.getAsDouble(order.getPayment(), false);
            Double doubleDiscountFee = Strings.getAsDouble(order.getDiscountFee(), false);
            if ((3 == order.getType() || 4 == order.getType()) && null != order.getCombineId() && order.getCombineId()>0) {
                continue;
            }
            order.setScalping(trade.getScalping());
            noRepeatList.add(order);
            Map<Long, List<Order>> collect = noRepeatList.stream().collect(Collectors.groupingBy(Order::getSid));
            boolean isOne=true;
            if (CollectionUtils.isNotEmpty(collect.get(order.getSid())) && collect.get(order.getSid()).size() != 1) {
                isOne = false;
            }
            JSONObject orderJson = createNewJSONObject(tradeJSON, context.needCopyTrade || i == 0, isOne);
            if (context.needCopyTrade && i > 0) {//再过滤
                orderJson = filterTradeColumn(orderJson);
            }
            boolean isSuite = order.getSuits() != null && !order.getSuits().isEmpty() && 2 == order.getType();
            executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_ORDER_ID, order.getId() + "");//order唯一标示
            executePut(orderJson, TradeExportFieldEnum.ORDER_ITEM_SYS_ID, order.getItemSysId() + "");//order商家编码
            executePut(orderJson, TradeExportFieldEnum.ORDER_SKU_SYS_ID, order.getSkuSysId()+"");//order商家规格编码
            if (order.getItemSysId() != null && order.getItemSysId() > 0) {
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_MAIN_OUTER_ID, order.getSysOuterId());//主商家编码
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_OUTER_ID, order.getSysOuterId());//商家编码
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_TITLE, order.getSysTitle());//商品名称
                executePut(orderJson, TradeExportFieldEnum.ORDER_SHORT_TITLE, order.getShortTitle());//商品简称
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SKU_PROPERTIES_NAME, order.getSysSkuPropertiesName());//规格属性
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SKU_PROPERTIES_ALIAS, order.getSysSkuPropertiesAlias());//规格别名
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SKU_REMARK, StringUtils.isNotBlank(order.getSysSkuRemark()) ? order.getSysSkuRemark() : order.getSysItemRemark());//商品/规格备注

            } else {
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_MAIN_OUTER_ID, StringUtils.isNotBlank(order.getOuterSkuId()) ? order.getOuterSkuId() : order.getOuterId());//主商家编码
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_OUTER_ID, StringUtils.isNotBlank(order.getOuterSkuId()) ? order.getOuterSkuId() : order.getOuterIid());//商家编码
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_TITLE, order.getTitle());//商品名称
                executePut(orderJson, TradeExportFieldEnum.ORDER_SHORT_TITLE, order.getShortTitle());//商品简称
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SKU_PROPERTIES_NAME, order.getSkuPropertiesName());//规格属性
            }
            executePut(orderJson, TradeExportFieldEnum.ORDER_PLAT_SKU_PROPERTIES, CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource()) ? "" : order.getSkuPropertiesName());//平台规格名称
            String lockNum;
            //缺货数量
            if (OrderExceptUtils.isContainsExcept(staff,order,ExceptEnum.UNALLOCATED)) {
                lockNum = "商品未匹配";
            } else {
                //咖啡测试3 tid 2702780729593344 退款不锁库存的订单号 ，导出订单明细这里缺货数量应该是0.。。。空单也有问题，顺手改下 马鹏程2020-04-15 17：13
                if(TradeStockUtils.needNotApplyStock(staff,order, context.tradeConfig)){
                    lockNum = "0";
                }else{
                    lockNum = orderNum - (orderStockNum == null ? 0 : orderStockNum) + "";
                }
            }
            executePut(orderJson, TradeExportFieldEnum.ORDER_LACK_NUM, lockNum);
            executePutMoney(staff,orderJson, TradeExportFieldEnum.COST, context.hasPowerCost , (order.getItemSysId() > 0 ? (order.getCost() == null ? TradeUtils.doubleToString(0.00) : TradeUtils.doubleToString(order.getCost() * orderNum)) : "0") );//成本价
            executePut(orderJson, TradeExportFieldEnum.ORDER_OID, order.getOid() == null ? "" : String.valueOf(order.getOid()));
            executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_PAYMENT, context.hasPowerPayment , order.getPayment());//商品实付金额
            executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_SALE_FEE, context.hasPowerFxPrice, order.getSaleFee());//商品分销金额
            executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_PAY_AMOUNT, context.hasPowerPayment , order.getPayAmount() );//商品买家已付金额
            executePut(orderJson, TradeExportFieldEnum.ORDER_PLATFORM_DISCOUNT_FEE, context.hasPowerPayment ? order.getPlatformDiscountFee() : "***");
            executePut(orderJson, TradeExportFieldEnum.ORDER_NUM, orderNum + "");//数量
            executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_UNIT_PRICE, context.hasPowerPayment , order.getPrice() );//单价
            Double discountedPrice = (null == orderNum || Integer.valueOf(0).equals(orderNum) || null == doublePayment) ? null : doublePayment / orderNum;
            executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_PRICE, context.hasPowerPayment , discountedPrice);//折后价
            //折扣
            executePut(orderJson, TradeExportFieldEnum.ORDER_DISCOUNT_RATE, context.hasPowerPayment ? (null == discountedPrice || null == doublePrice ||  Double.valueOf(0.0).equals(doublePrice) ? "" : String.format("%.2f", discountedPrice / doublePrice * 100) + "%") : "***");
            executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_DISCOUNT_FEE,context.hasPowerPayment, (null == doubleDiscountFee || null == orderNum || Integer.valueOf(0).equals(orderNum)) ? null : doubleDiscountFee / orderNum);//优惠金额
            executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_STATUS, TradeStatusUtils.convertOrderSysStatus(order));//子单状态
            executePut(orderJson, TradeExportFieldEnum.ORDER_CONSIGN_TIME, DateUtil.format(order.getConsignTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));//商品发货时间
            executePut(orderJson, TradeExportFieldEnum.ORDER_PT_CONSIGN_TIME, DateUtil.format(order.getPtConsignTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));//商品发货时间
            executePut(orderJson, TradeExportFieldEnum.ORDER_NUM_iid, order.getNumIid());
            executePut(orderJson, TradeExportFieldEnum.ORDER_SKU_ID, order.getSkuId());
            executePut(orderJson, TradeExportFieldEnum.ORDER_OUTER_SKU_ID, order.getOuterSkuId());
            executePut(orderJson, TradeExportFieldEnum.ORDER_OUTER_IID, order.getOuterIid());
            executePut(orderJson, TradeExportFieldEnum.ORDER_PLAT_OUTER_ID, OrderUtils.getPlatOuterId(order));
            if (context.btasCodeExport){
                executePut(orderJson, TradeExportFieldEnum.ORDER_BTAS_CODE, String.join(",", Optional.ofNullable(OrderUtils.getBtasOrderCode(order)).orElse(new ArrayList<>())));
            }
            executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SID, String.valueOf(order.getSid()));
            executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_TID, String.valueOf(order.getTid()));
            executePut(orderJson, TradeExportFieldEnum.ORDER_TITLE, order.getTitle());
            executePut(orderJson, TradeExportFieldEnum.ORDER_ESTIMATE_CON_TIME, DateUtil.format(order.getEstimateConTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));
            executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_CONSIGNED, getSysConsigned(order.getSysConsigned()));
            executePut(orderJson,TradeExportFieldEnum.ORDER_DIVIDE_ORDER_FEE,StringUtils.isNotEmpty(order.getDivideOrderFee())?order.getDivideOrderFee():"0");
            handleItemSupplierInfo(orderJson, order, tradeExportData);
            executePut(orderJson, TradeExportFieldEnum.ORDER_IDENT_CODE, getIdentCode(order));
            executePut(orderJson, TradeExportFieldEnum.ORDER_PT_BARCODE, tradeExportData.getPtBarcode(buildTbKey(order)));
            //达人相关信息
            if(context.switchData.orderExtInfoSwitch){
                handleAuthorInfo(orderJson, order);
                executePut(orderJson, TradeExportFieldEnum.ORDER_EXT_ORDER_REMARK,!Objects.isNull(order.getOrderExt())?(StringUtils.isNotEmpty(order.getOrderExt().getOrderRemark())?order.getOrderExt().getOrderRemark():""):"");
            }

            //付款金额=单价*数量*折扣
            // 单价 = 付款金额 / (数量* 折扣)
            if (isSuite && context.switchData.exportSuitsSwitch) {
                Order son = order.getSuits().get(0);
                executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_ITEM_SYS_ID,  son.getItemSysId() + "");
                executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SKU_SYS_ID,  son.getSkuSysId() + "");
                executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SYS_OUTER_ID,  son.getSysOuterId());
                executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SYS_TITLE, son.getSysTitle());
                executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SHORT_TITLE, son.getShortTitle());
                executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SYS_SKU_PROPERTIES_NAME, son.getSysSkuPropertiesName());
                executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SKU_PROPERTIES_ALIAS, son.getSysSkuPropertiesAlias());
                executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_NUM, String.valueOf(son.getNum()));
                executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_SUIT_PAYMENT, context.hasPowerPayment , son.getPayment());
                executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_SUIT_PAY_AMOUNT, context.hasPowerPayment , son.getPayAmount());
                //executePutMoney(staff,orderJson, TradeExportFieldEnum.ORDER_SUIT_DIVIDE_ORDER_FEE, true ,son.getDivideOrderFee());
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_SID, String.valueOf(son.getSid()));
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_TID, String.valueOf(son.getTid()));
                executePut(orderJson, TradeExportFieldEnum.ORDER_SYS_CONSIGNED, getSysConsigned(son.getSysConsigned()));
                executePut(orderJson, TradeExportFieldEnum.ORDER_IDENT_CODE, son.getIdentCode());
                executePut(orderJson, TradeExportFieldEnum.ORDER_SUIT_SYS_ORDER_ID, son.getId() + "");
                executePut(orderJson, TradeExportFieldEnum.ORDER_OID, son.getOid() + "");
            }
            jsonData.add(orderJson);
            if (isSuite && context.switchData.exportSuitsSwitch) {
                for (int j = 1; j < order.getSuits().size(); j++) {
                    Order son = order.getSuits().get(j);
                    JSONObject suitJSON = createNewJSONObject(orderJson, context.needCopyTrade, true);
                    hidenDetail(suitJSON,true);
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_ITEM_SYS_ID,  son.getItemSysId() + "");
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SKU_SYS_ID,  son.getSkuSysId() + "");
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SYS_OUTER_ID, son.getSysOuterId());
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SYS_ORDER_ID, son.getId() + "");
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SYS_TITLE, son.getSysTitle());
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SHORT_TITLE, son.getShortTitle());
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SYS_SKU_PROPERTIES_NAME, son.getSysSkuPropertiesName());
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_SKU_PROPERTIES_ALIAS, son.getSysSkuPropertiesAlias());
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SUIT_NUM, String.valueOf(son.getNum()));
                    executePutMoney(staff,suitJSON, TradeExportFieldEnum.ORDER_SUIT_PAYMENT, context.hasPowerPayment , son.getPayment());
                    executePutMoney(staff,suitJSON, TradeExportFieldEnum.ORDER_SUIT_PAY_AMOUNT, context.hasPowerPayment , son.getPayAmount());
                    //executePutMoney(staff,suitJSON, TradeExportFieldEnum.ORDER_SUIT_DIVIDE_ORDER_FEE, true ,son.getDivideOrderFee());
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SYS_SID, String.valueOf(son.getSid()));
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SYS_TID, String.valueOf(son.getTid()));
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_SYS_CONSIGNED, getSysConsigned(son.getSysConsigned()));
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_IDENT_CODE, son.getIdentCode());
                    executePut(suitJSON, TradeExportFieldEnum.ORDER_OID, son.getOid() + "");
                    jsonData.add(suitJSON);
                }
            }
        }
    }

    /**
     * 获取商品识别码，套件商品识别码有明细商品组成
     * @param order
     * @return
     */
    private String getIdentCode(Order order){
        boolean isSuite = order.getSuits() != null && !order.getSuits().isEmpty() && 2 == order.getType();
        if (isSuite){
            List<Order> suits = order.getSuits();
            StringBuilder identCode = new StringBuilder();
            for (Order son : suits){
                identCode.append(son.getIdentCode()).append(",");
            }
            return identCode.substring(0, identCode.length() -1);
        }else {
            return order.getIdentCode();
        }
    }

    /**
     * 获取用户仓库信息
     * @param staff
     * @return
     */
    private Map<Long, Warehouse> getUserWarehouses(Staff staff, List<Trade> trades) {
        List<Long> warehouseIds = trades.stream().map(Trade::getWarehouseId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> loadIds = new ArrayList<>();
        Map<Long, Warehouse> warehouseMap = new HashMap<>();
        for (Long warehouseId : warehouseIds){
            Warehouse warehouse = UserWarehousesCache.getIfPresent(warehouseId);
            if (Objects.isNull(warehouse)){
                loadIds.add(warehouseId);
            }else {
                warehouseMap.put(warehouseId, warehouse);
            }
        }
        if (CollectionUtils.isNotEmpty(loadIds)){
            List<Warehouse> warehouses = warehouseService.queryByIds(loadIds);
            Map<Long, Warehouse> loadMap = new HashMap<>();
            Optional.ofNullable(warehouses).orElse(new ArrayList<>()).forEach(warehouse ->{
                Warehouse cacheWarehouse = new Warehouse();
                cacheWarehouse.setId(warehouse.getId());
                cacheWarehouse.setName(warehouse.getName());
                loadMap.put(warehouse.getId(), cacheWarehouse);
            });
            if (MapUtils.isNotEmpty(loadMap)){
                warehouseMap.putAll(loadMap);
                UserWarehousesCache.putAll(loadMap);
            }
        }
        return warehouseMap;
    }

    /**
     * 获取用户店铺信息
     * @param staff
     * @return
     */
    private Map<Long, Shop> getUserShops(Staff staff, List<Trade> trades) {
        List<Long> userIds = trades.stream().map(Trade::getUserId).distinct().collect(Collectors.toList());
        List<Long> loadIds = new ArrayList<>();
        Map<Long, Shop> shopMap = new HashMap<>();
        for (Long userId : userIds){
            Shop shop = UserShopCache.getIfPresent(userId);
            if (Objects.isNull(shop)){
                loadIds.add(userId);
            }else {
                shopMap.put(userId, shop);
            }
        }
        if (CollectionUtils.isNotEmpty(loadIds)){
            List<Shop> shops = shopService.queryByUserIds(null, userIds.toArray(new Long[0]));
            Map<Long, Shop> loadShopMap = new HashMap<>();
            Optional.ofNullable(shops).orElse(new ArrayList<>()).forEach(shop ->{
                Shop cacheShop = new Shop();
                cacheShop.setUserId(shop.getUserId());
                cacheShop.setSource(shop.getSource());
                cacheShop.setSubSource(shop.getSubSource());
                cacheShop.setTitle(shop.getTitle());
                cacheShop.setShortTitle(shop.getShortTitle());
                cacheShop.setRemark(shop.getRemark());
                loadShopMap.put(shop.getUserId(), cacheShop);
            });
            if (MapUtils.isNotEmpty(loadShopMap)){
                shopMap.putAll(loadShopMap);
                UserShopCache.putAll(loadShopMap);
            }
        }
        return shopMap;
    }

    /**
     * 获取是否填充订单参数
     * @return
     */
    private Boolean getNeedCopyTrade(TradeExcelExportTask task) {
        JSONObject object = JSON.parseObject(task.getTaskParams());
        return object.getBoolean("needCopyTrade") == null ? false : object.getBoolean("needCopyTrade");
    }

    /**
     * 获取导出一单多包各包裹的实际重量和实付运费
     * @return
     */
    private Boolean getExportMultiPackActualInfo(TradeExcelExportTask task) {
        JSONObject object = JSON.parseObject(task.getTaskParams());
        return BooleanUtils.isTrue(object.getBoolean("exportMultiPackActualInfo"));
    }

    /**
     * 获取是否导出关联订单
     * @return
     */
    private Boolean getExportRelationTrade(TradeExcelExportTask task) {
        JSONObject object = JSON.parseObject(task.getTaskParams());
        return "1".equals(object.getString("exportRelation")) ;
    }

    /**
     * 获取用户是否需要加解密信息
     * @param staff
     * @return
     */
    private Boolean getUserEncrypt(Staff staff) {
        Boolean isNotEncrypt  = UserNotEncryptCache.getIfPresent(staff.getCompanyId());
        if (isNotEncrypt == null) {
            isNotEncrypt = (cacheBusiness.ifExportNotEncrypt(null) || cacheBusiness.ifExportNotEncrypt(staff.getCompanyId()));
            UserNotEncryptCache.put(staff.getCompanyId(), isNotEncrypt);
            return isNotEncrypt;
        }
        return isNotEncrypt;
    }

    /**
     * 获取分销店铺
     * @param staff
     * @return
     */
    private Map<Long, Shop> getFxUserShopByCache(Staff staff, List<Trade> trades) {
        Map<Long, Shop> result = Maps.newHashMap();
        try {
            List<Long> gxTbIds = trades.stream().filter(TradeUtils::isGxOrMixTrade).map(Trade::getTaobaoId).distinct().collect(Collectors.toList());
            List<Long> loadTbIds = new ArrayList<>();
            for (Long tbId : gxTbIds){
                Shop fxShop = UserFxShopCache.getIfPresent(tbId);
                if (Objects.isNull(fxShop)){
                    loadTbIds.add(tbId);
                }else {
                    result.put(tbId, fxShop);
                }
            }
            Map<Long, Shop> loadFxShop = getFxShopInfo(staff, loadTbIds);
            if (MapUtils.isNotEmpty(loadFxShop)){
                UserFxShopCache.putAll(loadFxShop);
                result.putAll(loadFxShop);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildLogHead(staff).append(e.getMessage()),e);
        }
        return  result;
    }


    /**
     * 获取交易配置
     * @param staff
     * @return
     */
    private TradeConfig getTradeConfig(Staff staff, TradeExportContext context) {
        if (!context.switchData.tradeConfigSwitch){
            return null;
        }
        TradeConfig tradeConfig = UserTradeConfigCache.getIfPresent(staff.getCompanyId());
        if (tradeConfig == null) {
            tradeConfig = tradeConfigService.get(staff);
            UserTradeConfigCache.put(staff.getCompanyId(), tradeConfig);
            return tradeConfig;
        }
        return tradeConfig;
    }

    private Map<String, ItemSupplierBridge> handleItemInfo2Trade(Staff staff, List<Trade> trades) {
        Map<String, ItemSupplierBridge> result = new HashMap<>();
        try {
            HashSet<Long> itemIds = new HashSet<>();
            HashSet<Long> skuIds = new HashSet<>();
            TradeUtils.getOrders4Trade(trades).forEach(order -> {
                if (order.getSkuSysId() > 0 ) {
                    //sku
                    skuIds.add(order.getSkuSysId());
                }else {
                    //spu
                    itemIds.add(order.getItemSysId());
                }
            });
            Map<String, List<ItemSupplierBridge>> itemSupplierBridgeMap = itemServiceWrapper.matchItemSupplierBridges(staff, new ArrayList<>(itemIds), new ArrayList<>(skuIds));
            for (Trade trade : trades){
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                for (Order order : orders){
                    List<ItemSupplierBridge> supplierBridges;
                    String key;
                    if (order.getSkuSysId() > 0) {
                        key = order.getItemSysId() + "_" + order.getSkuSysId();
                        supplierBridges = itemSupplierBridgeMap.get(key);
                    }else {
                        key = order.getItemSysId() + "_" + 0;
                        supplierBridges = itemSupplierBridgeMap.get(key);
                    }
                    if (CollectionUtils.isEmpty(supplierBridges)){
                        continue;
                    }
                    if (result.containsKey(key)){
                        continue;
                    }
                    //取第一条
                    ItemSupplierBridge supplierBridge = supplierBridges.get(0);
                    if (StringUtils.isNotBlank(supplierBridge.getSupplierName()) || StringUtils.isNotBlank(supplierBridge.getSupplierCode())){
                        ItemSupplierBridge copy = new ItemSupplierBridge();
                        copy.setSupplierName(supplierBridge.getSupplierName());
                        copy.setSupplierCode(supplierBridge.getSupplierCode());
                        result.put(key, copy);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("订单导出，订单纬度获取供应商信息出错" + e);
        }
        return result;
    }

    /**
     * 订单维度供应商，从Order中获取（归档订单暂不支持）
     * @param trade
     * @return
     */
    private String getSupplierName4Trade(Trade trade){
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (CollectionUtils.isEmpty(orders)){
            return "";
        }
        List<String> supplierNameList = new ArrayList<>();
        for (Order order : orders){
            if (StringUtils.isNotBlank(order.getSupplierName())){
                for(String name : order.getSupplierName().split(",")){
                    if (!supplierNameList.contains(name)){
                        supplierNameList.add(name);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(supplierNameList)){
            return String.join(",", supplierNameList);
        }
        return "";
    }

    /**
     * 异步查询汇总导出商品信息字段
     * @param staff
     * @param jsonData
     * @param
     * @param trades
     */
    private void handleItemInfo(Staff staff, List<JSONObject> jsonData, List<Trade> trades, TradeExportContext context) {
        try {
            HashSet<Long> itemIds = new HashSet<>();
            HashSet<Long> skuIds = new HashSet<>();
            for (Order order : TradeUtils.getOrders4Trade(trades)){
                boolean isSuite = order.getSuits() != null && !order.getSuits().isEmpty() && 2 == order.getType();
                if (Objects.isNull(order.getItemSysId()) || order.getItemSysId() <= 0){
                    continue;
                }
                if (Objects.isNull(order.getSkuSysId()) || order.getSkuSysId() <= 0){
                    //纯商品
                    if (context.switchData.itemOrSkyInfoByTypeSwitch || context.switchData.itemInfoSwitch){
                        itemIds.add(order.getItemSysId());
                    }
                }else {
                    //规格商品
                    if (context.switchData.itemOrSkyInfoByTypeSwitch || context.switchData.skuInfoSwitch){
                        skuIds.add(order.getSkuSysId());
                    }
                    if (context.switchData.itemInfoSwitch || context.switchData.itemInfoBySkuSwitch){
                        itemIds.add(order.getItemSysId());
                    }
                }
                if (context.switchData.exportSuitsSwitch && isSuite && context.switchData.itemOrSkuInfoSuitSwitch){
                    for (Order suit : order.getSuits()){
                        if (Objects.isNull(suit.getItemSysId()) || suit.getItemSysId() <= 0){
                            continue;
                        }
                        if (Objects.isNull(suit.getSkuSysId()) || suit.getSkuSysId() <= 0){
                            //纯商品
                            if (context.switchData.itemOrSkyInfoByTypeSwitch || context.switchData.itemInfoSwitch){
                                itemIds.add(suit.getItemSysId());
                            }
                        }else {
                            //规格商品
                            if (context.switchData.itemOrSkyInfoByTypeSwitch || context.switchData.skuInfoSwitch){
                                skuIds.add(suit.getSkuSysId());
                            }
                            if (context.switchData.itemInfoSwitch || context.switchData.itemInfoBySkuSwitch){
                                itemIds.add(suit.getItemSysId());
                            }
                        }
                    }
                }
            }
            //异步查询纯商品
            String finalItemFields = "sysItemId" + context.switchData.itemFields.toString();
            CompletableFuture<HashMap<Long, DmjItem>> itemFeature = CompletableFuture.supplyAsync(new Supplier<HashMap<Long, DmjItem>>() {
                @Override
                public HashMap<Long, DmjItem> get() {
                    HashMap<Long, DmjItem> itemRecordMap = new HashMap<>();
                    //查询纯商品
                    if (CollectionUtils.isNotEmpty(itemIds)) {
                        List<DmjItem> itemList = itemServiceWrapper.queryBySysItemIds(staff, new ArrayList<>(itemIds), finalItemFields);
                        itemList.forEach(dmjItem -> {
                            itemRecordMap.put(dmjItem.getSysItemId(), dmjItem);
                        });
                    }
                    return itemRecordMap;
                }
            });
            //异步查询sku商品
            String finalSkuFields = "sysSkuId" + context.switchData.skuFields.toString();
            CompletableFuture<HashMap<Long, DmjSku>> skuFeature = CompletableFuture.supplyAsync(new Supplier<HashMap<Long, DmjSku>>() {
                @Override
                public HashMap<Long, DmjSku> get() {
                    HashMap<Long, DmjSku> skuRecordMap = new HashMap<>();
                    //查询sku商品
                    if (CollectionUtils.isNotEmpty(skuIds)) {
                        List<DmjSku> skuList = itemServiceWrapper.queryBySysSkuIds(staff, new ArrayList<>(skuIds), finalSkuFields);
                        skuList.forEach(dmjSku -> {
                            skuRecordMap.put(dmjSku.getSysSkuId(),dmjSku);
                        });
                    }
                    return skuRecordMap;
                }
            });
            //最后一起处理
            CompletableFuture<HashMap<Long, DmjItem>> result = itemFeature.thenCombine(skuFeature, new BiFunction<HashMap<Long, DmjItem>, HashMap<Long, DmjSku>, HashMap<Long, DmjItem>>() {
                @Override
                public HashMap<Long, DmjItem> apply(HashMap<Long, DmjItem> itemRecordMap, HashMap<Long, DmjSku> skuRecordMap) {
                    jsonData.forEach(json->{
                        String orderSkuSysId = json.getString("orderSkuSysId");
                        String orderItemSysId = json.getString("orderItemSysId");
                        String orderSuitSkuSysId = json.getString("orderSuitSkuSysId");
                        String orderSuitItemSysId = json.getString("orderSuitItemSysId");
                        DmjItem dmjItem = Optional.ofNullable(orderItemSysId).filter(StringUtils::isNotEmpty).map(t ->itemRecordMap.get(Long.parseLong(t))).orElse(null);
                        DmjSku dmjSku = Optional.ofNullable(orderSkuSysId).filter(StringUtils::isNotEmpty).map(t -> skuRecordMap.get(Long.parseLong(t))).orElse(null);
                        DmjItem dmjSuitItem = Optional.ofNullable(orderSuitItemSysId).filter(StringUtils::isNotEmpty).map(t ->itemRecordMap.get(Long.parseLong(t))).orElse(null);
                        DmjSku dmjSuitSku = Optional.ofNullable(orderSuitSkuSysId).filter(StringUtils::isNotEmpty).map(t -> skuRecordMap.get(Long.parseLong(t))).orElse(null);
                        if (Objects.nonNull(dmjItem)){
                            executePut(json, TradeExportFieldEnum.ORDER_SYS_MAIN_OUTER_ID, dmjItem.getOuterId());
                            executePut(json, TradeExportFieldEnum.ORDER_SYS_SKU_RECORD, dmjItem.getRecord());
                            executePut(json, TradeExportFieldEnum.ORDER_ITEM_BARCODE, dmjItem.getBarcode());
                        }
                        if (Objects.nonNull(dmjSku)){
                            executePut(json, TradeExportFieldEnum.ORDER_SYS_SKU_RECORD, dmjSku.getRecord());
                            executePut(json, TradeExportFieldEnum.ORDER_SKU_BARCODE, dmjSku.getBarcode());
                        }
                        if (Objects.nonNull(dmjSuitItem)){
                            executePut(json, TradeExportFieldEnum.ORDER_ITEM_BARCODE, dmjSuitItem.getBarcode());
                        }
                        if (Objects.nonNull(dmjSuitSku)){
                            executePut(json, TradeExportFieldEnum.ORDER_SKU_BARCODE, dmjSuitSku.getBarcode());
                        }

                    });
                    return itemRecordMap;
                }
            });
            result.get(120, TimeUnit.SECONDS);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setMultiOutSids(Staff staff, Trade trade, JSONObject jsonObject, Map<String, MultiPacksPrintTradeLog> outSidArrayMap) {
        String[] outSidArray = TradeUtils.getMultiPrintOutsid(outSidArrayMap.get(trade.getOutSid()));
        if (outSidArray != null && outSidArray.length > 0) {
            List<String> outSids = new ArrayList<>(Arrays.asList(outSidArray));
            if (!outSids.contains(trade.getOutSid())) {
                outSids.add(0, trade.getOutSid());
            }
            executePut(jsonObject, TradeExportFieldEnum.OUT_SID, StringUtils.join(outSids, ","));
        }
    }

    private void launchAsyncOrderExportEvent(Staff staff, TradeExportParams exportParams) throws Exception {
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.TRADE.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }
        TradeQueryParams params = exportParams.getParams();
        boolean isOutstock = params.getQueryId() != null && (params.getQueryId() - SystemTradeQueryParamsContext.QUERY_WAIT_OUTSTOCK == 0 || SystemTradeQueryParamsContext.QUERY_FINISH_OUTSTOCK == 0);
        String[][] orderTitle = getTitleByExportFields(getNeedFieldsList(exportParams.getExportFields()), isOutstock ? TradeExportFieldEnum.FIELD_TRADE_OUT_SCOPE : TradeExportFieldEnum.FIELD_ORDER_SCOPE,staff.getCompanyId());
        String fileName = new String((params.getQueryId() == 50 ? "出库单明细导出" : "交易订单明细导出").getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle(params.getQueryId() == 50 ? "出库单明细导出" : "交易订单明细导出");
        param.setTitleArr(orderTitle);
        param.setModule(EnumDownloadCenterModule.TRADE.getCode());
        param.setNeedVerify(needVerify(staff, exportParams.getParams()));
        eventCenter.fireEvent(this, new EventInfo("tradeOrder.info.download.excel").setArgs(new Object[]{staff, param, exportParams}), false);
    }

    private List<String> getNeedFieldsList(String exportFields) {
        return (StringUtils.isBlank(exportFields) || StringUtils.isBlank(exportFields.replaceAll(",", ""))) ? Lists.<String>newArrayList() : ArrayUtils.toStringList(exportFields);
    }



    private String[][] getArrayResultByNeedFields(Staff staff, List<JSONObject> excelContent, List<TradeExportFieldEnum> exportFieldEnums) {
        List<String[]> result = new ArrayList<>();
        List<String> resultFields = exportFieldEnums.stream().map(TradeExportFieldEnum::getField).collect(Collectors.toList());
        for (JSONObject jsonObject : excelContent) {
            List<String> columnList = new ArrayList<>();
            boolean addColumn = false;
            for (String field : resultFields) {
                Object o = jsonObject.get(field);
                String value = (o == null ? "" : o.toString());
                columnList.add(value);
                addColumn = addColumn || StringUtils.isNotBlank(value);
            }
            if (addColumn) {
                String[] columnArray = new String[columnList.size()];
                columnList.toArray(columnArray);
                result.add(columnArray);
            }
        }
        String[][] resultArray = new String[result.size()][];
        result.toArray(resultArray);
        return resultArray;
    }
    private List<LinkedList<String>> getListResultByNeedFields(Staff staff, List<JSONObject> excelContent, List<TradeExportFieldEnum> exportFieldEnums) {
        List<LinkedList<String>> linkedList=new LinkedList<>();
        List<String> resultFields = exportFieldEnums.stream().map(TradeExportFieldEnum::getField).collect(Collectors.toList());
        for (JSONObject jsonObject : excelContent) {
            LinkedList<String> columnList = new LinkedList<>();
            boolean addColumn = false;
            for (String field : resultFields) {
                Object o = jsonObject.get(field);
                String value = (o == null ? "" : o.toString());
                columnList.add(value);
                addColumn = addColumn || StringUtils.isNotBlank(value);
            }
            if (addColumn) {
                linkedList.add(columnList);
            }
        }
        return linkedList;
    }

    private List<TradeExportFieldEnum> getResultFieldEnums(List<String> needFields, String fieldTradeScope) {
        List<TradeExportFieldEnum> fieldsByScope = TradeExportFieldEnum.getFieldsByScope(fieldTradeScope);
        List<TradeExportFieldEnum> result = new ArrayList<>();
        for (TradeExportFieldEnum tradeExportFieldEnum : fieldsByScope) {
            String field = tradeExportFieldEnum.getField();
            if (CollectionUtils.isEmpty(needFields) || needFields.contains(field)) {
                result.add(tradeExportFieldEnum);
            }
        }
        return result;
    }

    private List<String> getResultFields(Staff staff, List<String> needFields, String fieldTradeScope) {
        List<TradeExportFieldEnum> fieldsByScope = TradeExportFieldEnum.getFieldsByScope(fieldTradeScope);
        List<String> result = new ArrayList<String>();
        for (TradeExportFieldEnum tradeExportFieldEnum : fieldsByScope) {
            String field = tradeExportFieldEnum.getField();
            if (CollectionUtils.isEmpty(needFields) || needFields.contains(field)) {
//                if(TradeExportFieldEnum.ORDER_LACK_NUM.getField().equals(field) && staff.openAuditActiveStockRecord()){
//                    continue;
//                }
                result.add(field);
            }
        }
        return result;
    }

    /**
     * 过滤和金钱等相关的tradeColumn
     * @param columnJson
     * @return
     */
    private JSONObject filterTradeColumn(JSONObject columnJson) {
        JSONObject result = new JSONObject();
        if(columnJson != null) {
            hidenDetail(columnJson, false);
            result.putAll(columnJson);
        }
        return result;
    }

    private JSONObject createNewJSONObject(JSONObject columnJson, boolean needCopy  ,boolean isOne) {
        JSONObject result = new JSONObject();
        if (needCopy && columnJson != null) {
            //需要自动填充商品时，过滤以下字段，不进行重复自动填充
            if (!isOne) {
                hidenDetail(columnJson,false);
            }
            result.putAll(columnJson);
        }
        return result;
    }

    /**
     *  需要自动填充商品时，过滤以下字段，不进行重复自动填充
     * */
    private  void hidenDetail (JSONObject result, boolean isHideSuit) {
        if (isHideSuit) {
            //成本价
            executePut(result, TradeExportFieldEnum.COST, "");
            //单价
            executePut(result, TradeExportFieldEnum.ORDER_UNIT_PRICE, "");
            //折后价
            executePut(result, TradeExportFieldEnum.ORDER_PRICE, "");
            //折扣
            executePut(result, TradeExportFieldEnum.ORDER_DISCOUNT_RATE, "");
            //优惠金额
            executePut(result, TradeExportFieldEnum.ORDER_DISCOUNT_FEE, "");
            //商品实付金额
            executePut(result, TradeExportFieldEnum.ORDER_PAYMENT, "");
            //商品分销金额
            executePut(result, TradeExportFieldEnum.ORDER_SALE_FEE, "");
            //数量
            executePut(result, TradeExportFieldEnum.ORDER_NUM, "");
            //缺货数量
            executePut(result, TradeExportFieldEnum.ORDER_LACK_NUM, "");

        }
        //商品数量
        executePut(result, TradeExportFieldEnum.ITEM_COUNT,"");
        //商品净重
        executePut(result, TradeExportFieldEnum.NET_WEIGHT,"");
        //实际重量
        executePut(result, TradeExportFieldEnum.WEIGHT,"");
        //订单金额
        executePut(result, TradeExportFieldEnum.TOTAL_FEE,"");
        //实付金额
        executePut(result, TradeExportFieldEnum.PAYMENT,"");
        //总优惠金额
        executePut(result, TradeExportFieldEnum.TOTAL_DISCOUNT_FEE,"");
        //订单优惠金额
        executePut(result, TradeExportFieldEnum.TRADE_DISCOUNT_FEE,"");
        //分销金额
        executePut(result, TradeExportFieldEnum.SALE_FEE,"");
        //实收运费
        executePut(result, TradeExportFieldEnum.POST_FEE,"");
        //实付运费
        executePut(result, TradeExportFieldEnum.ACTUAL_POST_FEE,"");
        //理论运费
        executePut(result, TradeExportFieldEnum.THEORY_POST_FEE,"");
        //分销总成本
        executePut(result, TradeExportFieldEnum.TRADE_FX_TOTAL_COST,"");

    }

    //订单生成数据位置
    JSONObject exportCommon(Staff staff, Trade trade, TradeExportData tradeExportData, TradeExportContext context) {
        Shop shop = tradeExportData.getShops().get(trade.getUserId()) == null ? new Shop() : tradeExportData.getShops().get(trade.getUserId());
        JSONObject result = new JSONObject();
        boolean isGxOrMixTrade = TradeUtils.isGxOrMixTrade(trade);
        String tradeSource = trade.getSource();
        // 手工供分销单明文导出
        boolean sysAndIsGxOrMixTrade = (CommonConstants.PLAT_FORM_TYPE_SYS.equals(TradeUtils.getSourceByTrade(trade))) && TradeUtils.isFxSource(trade);
        //先把要处理加密的放在上面一起，也方便维护一些
        Boolean aBoolean = context.allFlatformWhiteCompanyIdList.get(tradeSource);
//        下面这些处理 排除京东和PDD的
        if(!Arrays.asList(CommonConstants.PLAT_FORM_TYPE_JD,CommonConstants.PLAT_FORM_TYPE_PDD).contains(tradeSource)){
            //下面区分 三个平台的平台单和其他
            if(PLAIN_TEXT_PLAT_FORM_LIST.contains(tradeSource) || sysAndIsGxOrMixTrade){
                //三个平台的平台单
                // 目前这两个地址没有code   白名单则明文,否则密文
                // 其他几个字段的话，在白名单时，有权明文 无权密文， 不在白名单时，有权无权都是密文
                if((aBoolean!=null && aBoolean) || context.isNotEncrypt || sysAndIsGxOrMixTrade){
                    //白名单
                    executePut(result, TradeExportFieldEnum.RECEIVER_ADDRESS, trade.getReceiverAddress());//详细地址
                    executePut(result, TradeExportFieldEnum.RECEIVER_WHOLE_ADDRESS, new StringBuilder().append(trade.getReceiverState()).append(trade.getReceiverCity()).append(trade.getReceiverDistrict()).append(trade.getReceiverAddress()).toString());//详细地址（包含省市区）
                    //其他几个字段处理  有权明文 无权密文
                    executePut(result, TradeExportFieldEnum.BUYER_NICK, context.nickDecode ? getRealBuyerNick(shop, trade) : (trade.getBuyerNick() != null ? "****" : null));//买家旺旺号转星号*
                    executePut(result, TradeExportFieldEnum.RECEIVER_NAME, context.receiverDecode ? trade.getReceiverName() : (trade.getReceiverName() != null && !(trade.getReceiverName()).isEmpty() ?  "****" : null));//收货人
                    executePut(result, TradeExportFieldEnum.RECEIVER_MOBILE, context.mobileDecode ? trade.getReceiverMobile() : StringUtils.isNotBlank(trade.getReceiverMobile())?"****":null);//手机
                    executePut(result, TradeExportFieldEnum.RECEIVER_PHONE, context.mobileDecode ? trade.getReceiverPhone() : StringUtils.isNotBlank(trade.getReceiverPhone())?"****":null);//固话
                }else{
                    //没有在白名单
                    executePut(result, TradeExportFieldEnum.RECEIVER_ADDRESS, "****");//详细地址
                    executePut(result, TradeExportFieldEnum.RECEIVER_WHOLE_ADDRESS, "****");//详细地址（包含省市区）
                    //其他几个字段处理  有权无权都是密文 改了，nick有权明文无权密文
                    executePut(result, TradeExportFieldEnum.BUYER_NICK, context.nickDecode ? getRealBuyerNick(shop, trade) : (trade.getBuyerNick() != null ? "****" : null));//买家旺旺号转星号*
                    executePut(result, TradeExportFieldEnum.RECEIVER_NAME, "****");//收货人
                    executePut(result, TradeExportFieldEnum.RECEIVER_MOBILE, "****");//手机
                    executePut(result, TradeExportFieldEnum.RECEIVER_PHONE, "****");//固定号码
                }
            }else{
                //三个平台的手工单+其他平台的所有订单
                // 目前这两个地址没有code  全部明文
                // 其他几个字段的话，有权明文 无权密文
                executePut(result, TradeExportFieldEnum.RECEIVER_ADDRESS, trade.getReceiverAddress());//详细地址
                executePut(result, TradeExportFieldEnum.RECEIVER_WHOLE_ADDRESS, new StringBuilder().append(trade.getReceiverState()).append(trade.getReceiverCity()).append(trade.getReceiverDistrict()).append(trade.getReceiverAddress()).toString());//详细地址（包含省市区）
                //其他几个字段处理
                executePut(result, TradeExportFieldEnum.BUYER_NICK, context.nickDecode ? getRealBuyerNick(shop, trade) : (trade.getBuyerNick() != null ? "****" : null));//买家旺旺号转星号*
                executePut(result, TradeExportFieldEnum.RECEIVER_NAME, context.receiverDecode ? trade.getReceiverName() : (trade.getReceiverName() != null && !(trade.getReceiverName()).isEmpty() ?  "****" : null));//收货人
                executePut(result, TradeExportFieldEnum.RECEIVER_MOBILE, context.mobileDecode ? trade.getReceiverMobile() : StringUtils.isNotBlank(trade.getReceiverMobile())?"****":null);//手机
                executePut(result, TradeExportFieldEnum.RECEIVER_PHONE, context.mobileDecode ? trade.getReceiverPhone() : StringUtils.isNotBlank(trade.getReceiverPhone())?"****":null);//固话
            }
        }else{
            //京东和PDD  按之前的代码整
            executePut(result, TradeExportFieldEnum.BUYER_NICK, context.nickDecode ? trade.getBuyerNick() : (trade.getBuyerNick() != null ? "****" : null));//买家旺旺号转星号*
            executePut(result, TradeExportFieldEnum.RECEIVER_NAME, context.receiverDecode ? trade.getReceiverName() : (trade.getReceiverName() != null && !(trade.getReceiverName()).isEmpty() ? trade.getReceiverName().trim().substring(0, 1) + "***" : null));//收货人
            executePut(result, TradeExportFieldEnum.RECEIVER_MOBILE, context.mobileDecode ? trade.getReceiverMobile() : getReceiverMobile(trade.getReceiverMobile()));//手机
            executePut(result, TradeExportFieldEnum.RECEIVER_PHONE, context.mobileDecode ? trade.getReceiverPhone() : getReceiverPhone(trade.getReceiverPhone()));//固话
            executePut(result, TradeExportFieldEnum.RECEIVER_ADDRESS, trade.getReceiverAddress());//详细地址
            executePut(result, TradeExportFieldEnum.RECEIVER_WHOLE_ADDRESS, new StringBuilder().append(trade.getReceiverState()).append(trade.getReceiverCity()).append(trade.getReceiverDistrict()).append(trade.getReceiverAddress()).toString());//详细地址（包含省市区）
        }
		executePut(result, TradeExportFieldEnum.SID, trade.getSid() + "");//系统订单号
		executePut(result, TradeExportFieldEnum.TID, getTids(staff, trade));//平台订单号
        executePut(result, TradeExportFieldEnum.SHORT_ID, trade.getShortId() != null ? trade.getShortId().toString() :"0");//内部单号
        executePut(result, TradeExportFieldEnum.PLATFORM_ID, trade.getFxPlatformTid());
        executePut(result,TradeExportFieldEnum.VIP_PO_NO,trade.getVipPickNo() !=null? trade.getVipPickNo():"");
        executePut(result,TradeExportFieldEnum.VIP_STORAGE_NO,trade.getVipStorageNo()!=null? trade.getVipStorageNo():"");

        if (isGxOrMixTrade) {
            //供销订单使用分销商的店铺导出
            Shop fxShop = tradeExportData.getFxShopInfo().get(trade.getTaobaoId());
            String sourceName = getFxSource(trade, fxShop);
            executePut(result, TradeExportFieldEnum.SOURCE, sourceName);//平台

            if (Objects.nonNull(fxShop)) {
                //分销店铺展示名称变更：简称为空，店名走新逻辑
                executePut(result, TradeExportFieldEnum.SHOP_NAME, TradeUtils.getFxShopShowName(fxShop));
                executePut(result, TradeExportFieldEnum.SHOP_SHOT_TITLE, "");
            } else {
                executePut(result, TradeExportFieldEnum.SHOP_NAME, StringUtils.isNotBlank(shop.getTitle()) ? shop.getTitle() : "");//店铺名称
                executePut(result, TradeExportFieldEnum.SHOP_SHOT_TITLE, StringUtils.isNotBlank(shop.getShortTitle()) ? shop.getShortTitle() : "");//店铺简称
            }
        } else {
            // shop->source 等于其他 就是手工单   trade-->source=sys也是手工单 一样的东西
            executePut(result, TradeExportFieldEnum.SOURCE, shop == null ? null : shop.getSourceName());//平台
            // 奇门店铺昵称 平台名称-店铺名称-卖家昵称
            if (CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(shop.getSource()) &&
                    StringUtils.isNotBlank(trade.getSellerNick()) &&
                    !trade.getSellerNick().equals(shop.getTitle()) && context.switchData.qmShopSwitch) {
                TradeSourcePlatformCodeRelation relation = TradeSourcePlatformCodeDiamondUtils.getPlatformBySource(trade.getSubSource(), "qimen");
                String uniqueKey = trade.getUserId() + trade.getSellerNick() + trade.getSubSource();
                QiMenShop qiMenShop = tradeExportData.getQimenShopMap().computeIfAbsent(uniqueKey, (key) ->
                {
                    try {
                        return shopService.getQiMenShop(staff, trade.getUserId(), trade.getSellerNick(), trade.getSubSource());
                    } catch (Exception ex) {
                        logger.warn(LogHelper.buildLog(staff, String.format("获取奇门店铺信息异常,店铺编号:%s,店铺名称:%s,平台编码:%s", trade.getUserId(), trade.getSellerNick(), trade.getSubSource())), ex);
                    }
                    return null;
                });
                String sellerNick = trade.getSellerNick();
                if (null != qiMenShop && StringUtils.isNotEmpty(qiMenShop.getShortName())) {
                    sellerNick = qiMenShop.getShortName();//优先店铺简称
                }
                executePut(result, TradeExportFieldEnum.SHOP_NAME, relation.getName() + "-" + shop.getTitle() + "-" + sellerNick);//店铺名称
            } else {
                executePut(result, TradeExportFieldEnum.SHOP_NAME, StringUtils.isNotBlank(shop.getTitle()) ? shop.getTitle() : "");//店铺名称
            }
            executePut(result, TradeExportFieldEnum.SHOP_SHOT_TITLE, StringUtils.isNotBlank(shop.getShortTitle()) ? shop.getShortTitle() : "");//店铺简称
        }
        executePut(result, TradeExportFieldEnum.SHOP_REMARK, StringUtils.isNotBlank(shop.getRemark()) ? shop.getRemark() : "");//店铺备注
        executePut(result, TradeExportFieldEnum.SHOP_NUMBER, !TradeUtils.isGxOrMixTrade(trade) ? trade.getUserId() + "" : "");//店铺编号
		executePut(result, TradeExportFieldEnum.TRADE_OUT_TYPE, getOutstockName(trade.getType()));//出库单类型
		executePut(result, TradeExportFieldEnum.WAREHOUSE_NAME, tradeExportData.getWarehouses().get(trade.getWarehouseId()) != null ? tradeExportData.getWarehouses().get(trade.getWarehouseId()).getName() : "");//仓库名称
		executePut(result, TradeExportFieldEnum.RECEIVER_STATE, trade.getReceiverState());//省
		executePut(result, TradeExportFieldEnum.RECEIVER_CITY, trade.getReceiverCity());//市
		executePut(result, TradeExportFieldEnum.RECEIVER_DISTRICT, trade.getReceiverDistrict());//区
        executePut(result, TradeExportFieldEnum.RECEIVER_STREET, trade.getReceiverStreet());//街道
        executePut(result, TradeExportFieldEnum.RECEIVER_ZIP, trade.getReceiverZip());//邮编
		executePut(result, TradeExportFieldEnum.SYS_STATUS, TradeStatusUtils.convertChSysStatus(trade, context.tradeConfig));
		//订单状态
		executePut(result, TradeExportFieldEnum.ITEM_COUNT, getItemCount(trade) + "");//商品数量
		executePut(result, TradeExportFieldEnum.NET_WEIGHT, (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus()) ? trade.getNetWeight() == null ? "0" : MathUtils.toString(trade.getNetWeight(),4) : MathUtils.toString(TradeUtils.calculateTradeNetWeight(trade),4)));//商品净重
		executePut(result, TradeExportFieldEnum.WEIGHT, MathUtils.toString(trade.getWeight(),4));//实际重量
		executePut(result, TradeExportFieldEnum.TEMPLATE_NAME, trade.getTemplateName());//快递模版
		executePut(result, TradeExportFieldEnum.OUT_SID, trade.getOutSid());//运单号
        executePut(result, TradeExportFieldEnum.OPEN_UID, TradeFieldUtils.handleBuyerNickAndOpenUid(staff, trade).getOpenUid());
        executePutMoney(staff,result, TradeExportFieldEnum.TOTAL_FEE, context.hasPowerPayment , TradeUtils.getTotalFee(trade) );//订单金额
        executePutMoney(staff,result, TradeExportFieldEnum.PAYMENT, context.hasPowerPayment , TradeUtils.getPayment(trade) );//实付金额
        executePutMoney(staff,result, TradeExportFieldEnum.TOTAL_DISCOUNT_FEE, context.hasPowerPayment , PaymentUtils.totalDiscountFee(trade) );//总优惠金额
        executePutMoney(staff,result, TradeExportFieldEnum.TRADE_DISCOUNT_FEE, context.hasPowerPayment , PaymentUtils.totalDiscountFee(trade,false) );//订单优惠金额
        executePutMoney(staff,result, TradeExportFieldEnum.SALE_FEE, context.hasPowerFxPrice, getSaleFee(staff, trade));//分销金额
        executePutMoney(staff,result, TradeExportFieldEnum.POST_FEE, context.hasPowerPostFee , getPostFee(staff, trade) );//实收运费
        executePutMoney(staff,result, TradeExportFieldEnum.ACTUAL_POST_FEE, context.hasPowerTheoryOrActualPostFee , trade.getActualPostFee() );//实付运费
        executePutMoney(staff,result, TradeExportFieldEnum.THEORY_POST_FEE, context.hasPowerTheoryOrActualPostFee , (getTheoryPostFeeNew(staff, trade)) );//理论运费
        executePutMoney(staff,result, TradeExportFieldEnum.GROSS_PROFIT, context.hasPowerGrossProfit , MathUtils.toScaleString(trade.getCompanyId(),trade.getGrossProfit()));//毛利润
        executePutMoney(staff,result, TradeExportFieldEnum.PAY_AMOUNT, context.hasPowerPayment  , TradeUtils.getPayAmount(staff, trade));//订单买家已付金额
        executePutMoney(staff,result, TradeExportFieldEnum.TRADE_COST, staff.getPowerDataPrivilegeSettings() != null && staff.getPowerDataPrivilegeSettings().contains("1000304")  ,trade.getCost());//订单成本
        executePut(result, TradeExportFieldEnum.PLATFORM_DISCOUNT_FEE, context.hasPowerPayment ? trade.getPlatformDiscountFee() : "***");
		executePut(result, TradeExportFieldEnum.SYS_MEMO, TradeUtils.getSysMemo(staff, trade));//订单备注
		executePut(result, TradeExportFieldEnum.SELLER_MEMO, TradeUtils.getSellerMemo(trade));//平台备注
		executePut(result, TradeExportFieldEnum.BUYER_MESSAGE, TradeUtils.getBuyerMessage(staff, trade));//买家留言
		executePut(result, TradeExportFieldEnum.SELLER_FLAG, TradeUtils.getSellerFlag(staff, trade));//订单旗帜
		executePut(result, TradeExportFieldEnum.CREATED, DateUtil.format(trade.getCreated(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));//下单时间
		executePut(result, TradeExportFieldEnum.PAY_TIME, DateUtil.format(trade.getPayTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));//付款时间
		executePut(result, TradeExportFieldEnum.CONSIGN_TIME, DateUtil.format(trade.getConsignTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));//发货时间
		executePut(result, TradeExportFieldEnum.EXPRESS_PRINT_TIME, DateUtil.format(trade.getExpressPrintTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));//打印时间
        if (Objects.isNull(trade.getTradeInvoice()) || Objects.isNull(trade.getTradeInvoice().getId())) {
            executePut(result, TradeExportFieldEnum.INVOICE_NAME, trade.getInvoiceName()); // 发票抬头
            executePut(result, TradeExportFieldEnum.BUYER_TAX_NO, trade.getBuyerTaxNo());//买家税号
            executePut(result, TradeExportFieldEnum.INVOICE_TYPE, trade.getInvoiceType()); //"发票抬头类型
            executePut(result, TradeExportFieldEnum.INVOICE_KIND, trade.getInvoiceKind()); //"发票种类
            executePut(result, TradeExportFieldEnum.INVOICE_REMARK, trade.getInvoiceRemark()); //"发票备注
            executePut(result, TradeExportFieldEnum.BUYER_ADDRESS, ""); //"购方地址
            executePut(result, TradeExportFieldEnum.BUYER_PHONE, ""); //"购方电话
            executePut(result, TradeExportFieldEnum.BUYER_BANK, ""); //"购方开户行
            executePut(result, TradeExportFieldEnum.BUYER_BANK_ACCOUNT, ""); //"购方开户行账号
            executePut(result, TradeExportFieldEnum.INVOICE_AMOUNT, ""); //"开票金额
            executePut(result, TradeExportFieldEnum.EMAIL, ""); //邮箱
        } else {
            executePut(result, TradeExportFieldEnum.INVOICE_NAME, trade.getTradeInvoice().getInvoiceName()); // 发票抬头
            executePut(result, TradeExportFieldEnum.BUYER_TAX_NO, null == trade.getTradeInvoice() ? "" : trade.getTradeInvoice().getBuyerTaxNo());//买家税号
            executePut(result, TradeExportFieldEnum.INVOICE_TYPE, null == trade.getTradeInvoice() || StringUtils.isBlank(trade.getTradeInvoice().getInvoiceType()) ? "" : InvoiceTypeEnum.getInvoiceTypeEnum(Strings.getAsInt(trade.getTradeInvoice().getInvoiceType(), false)).getMsg()); //"发票抬头类型
            executePut(result, TradeExportFieldEnum.INVOICE_KIND, null == trade.getTradeInvoice() || StringUtils.isBlank(trade.getTradeInvoice().getInvoiceKind()) ? "" : InvoiceKindEnum.getInvoiceKindEnum(Strings.getAsInt(trade.getTradeInvoice().getInvoiceKind(), false)).getMsg()); //"发票种类
            executePut(result, TradeExportFieldEnum.INVOICE_REMARK, null == trade.getTradeInvoice() ? "" : trade.getTradeInvoice().getInvoiceRemark()); //"发票备注
            executePut(result, TradeExportFieldEnum.BUYER_ADDRESS, null == trade.getTradeInvoice() ? "" : trade.getTradeInvoice().getBuyerAddress()); //"购方地址
            executePut(result, TradeExportFieldEnum.BUYER_PHONE, null == trade.getTradeInvoice() ? "" : trade.getTradeInvoice().getBuyerPhone()); //"购方电话
            executePut(result, TradeExportFieldEnum.BUYER_BANK, null == trade.getTradeInvoice() ? "" : trade.getTradeInvoice().getBuyerBank()); //"购方开户行
            executePut(result, TradeExportFieldEnum.BUYER_BANK_ACCOUNT, null == trade.getTradeInvoice() ? "" : trade.getTradeInvoice().getBuyerBankAccount()); //"购方开户行账号
            executePut(result, TradeExportFieldEnum.INVOICE_AMOUNT, null == trade.getTradeInvoice() ? "" : trade.getTradeInvoice().getInvoiceAmount()); //"开票金额
            executePut(result, TradeExportFieldEnum.EMAIL, null == trade.getTradeInvoice() ? "" : trade.getTradeInvoice().getEmail()); //邮箱
        }

        WaveSorting waveSorting = tradeExportData.getPickNameMap().get(trade.getSid());
        if (waveSorting != null) {
            executePut(result, TradeExportFieldEnum.PICK_NAME, StringUtils.isNotBlank(waveSorting.getPickerNames()) ? waveSorting.getPickerNames() : "");//拣选人
            executePut(result, TradeExportFieldEnum.POSITION_NO, waveSorting.getPositionNo() != null ? waveSorting.getPositionNo().toString() : "");//位置号
        }
        String packCheckName = tradeExportData.getPackCheckNameMap().get(trade.getSid());
        executePut(result, TradeExportFieldEnum.PACK_CHECK_NAME, packCheckName);
        executePut(result, TradeExportFieldEnum.WAVE_ID, Objects.nonNull(trade.getWaveId()) && trade.getWaveId() > 0 ? trade.getWaveId().toString() : null);//波次号
        WaveRule waveRule = tradeExportData.getWaveIdWaveRuleMap().get(trade.getWaveId());
        executePut(result, TradeExportFieldEnum.WAVE_RULE_NAME, null != waveRule ? waveRule.getName() : null);//波次规则名
        executePutMoney(staff,result, TradeExportFieldEnum.COST, context.hasPowerCost , trade.getCost());//成本价
        executePut(result, TradeExportFieldEnum.TRADE_TAGS, CollectionUtils.isEmpty(trade.getTagName()) ? "" : StringUtils.join(trade.getTagName(), ","));//标签名称
        executePut(result, TradeExportFieldEnum.TRADE_TYPE, StringUtils.join(TradeTypeUtils.getTradeType(trade), ","));//标签名称
        executePutMoney(staff,result, TradeExportFieldEnum.TRADE_PURCHASE_AMOUNT, context.hasPowerPayment , trade.getTradePurchaseAmount() );//订单实收金额
        executePutMoney(staff,result, TradeExportFieldEnum.PLATFORM_PAYMENT_AMOUNT, context.hasPowerPayment , trade.getPlatformPaymentAmount());//平台支付金额
        executePutMoney(staff,result, TradeExportFieldEnum.MANUAL_PAYMENT_AMOUNT, context.hasPowerPayment , trade.getManualPaymentAmount());//手工支付金额
        executePut(result, TradeExportFieldEnum.SOURCE_NAME, StringUtils.isNoneBlank(trade.getSourceAliasName()) ? trade.getSourceAliasName() : trade.getSourceName());//分销商
        executePut(result, TradeExportFieldEnum.DEST_NAME, StringUtils.defaultIfBlank(trade.getDestAliasName(),trade.getDestName()));//供销商
        executePut(result, TradeExportFieldEnum.TIMEOUT_ACTION_TIME, DateUtil.format(trade.getTimeoutActionTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, ""));//承诺时间
        executePut(result, TradeExportFieldEnum.AUDIT_TIME, (!TradeStatusUtils.isWaitAudit(trade.getSysStatus()) && !TradeStatusUtils.isWaitPay(trade.getSysStatus())) ? DateUtil.format(trade.getAuditTime(), "yyyy-MM-dd HH:mm:ss").replace(TradeTimeUtils.INIT_DATE_STR, "") : "");//审核时间
        executePut(result, TradeExportFieldEnum.VOLUME, trade.getVolume() == null?"":MathUtils.toString(trade.getVolume()));//体积
        executePut(result, TradeExportFieldEnum.MALL_MASK_NAME, TradeUtils.isDfSource(trade) &&  trade.getTradeExt() != null && StringUtils.isNotBlank(trade.getTradeExt().getMallMaskName())?trade.getTradeExt().getMallMaskName():"");//代发店铺
        executePut(result, TradeExportFieldEnum.SALESMAN_NAME, trade.getSalesmanName());//业务员
        executePut(result, TradeExportFieldEnum.LOGISTICS_COMPANY_NAME, trade.getLogisticsCompanyName());
        executePut(result, TradeExportFieldEnum.LOGISTICS_TRACE_COUNT, tradeExportData.getLogisticsTraceInfo(trade.getOutSid(), TradeExportFieldEnum.LOGISTICS_TRACE_COUNT.getField()));//物流记录条数
        executePut(result, TradeExportFieldEnum.LAST_LOGISTICS_TRACE, tradeExportData.getLogisticsTraceInfo(trade.getOutSid(), TradeExportFieldEnum.LAST_LOGISTICS_TRACE.getField()));//最近物流记录
        executePut(result, TradeExportFieldEnum.LOGISTICS_MODIFIED, tradeExportData.getLogisticsTraceInfo(trade.getOutSid(), TradeExportFieldEnum.LOGISTICS_MODIFIED.getField()));//物流最近更新时间
        executePut(result, TradeExportFieldEnum.NEVER_MODIFIED_TIME, tradeExportData.getLogisticsTraceInfo(trade.getOutSid(), TradeExportFieldEnum.NEVER_MODIFIED_TIME.getField()));//neverModifiedTime
        executePut(result, TradeExportFieldEnum.TRADE_SUPPLIER_NAME, getSupplierName4Trade(trade));
        if (context.switchData.fxCostSwitch) {
            executePut(result, TradeExportFieldEnum.TRADE_FX_TOTAL_COST, getFxTotalCost(staff, trade));
        }
        /**
         * 导出团长信息
         */
        if (context.switchData.distributorInfoSwitch && null != trade.getTradeDistributor()){
            executePut(result, TradeExportFieldEnum.DISTRIBUTOR_ID, trade.getTradeDistributor().getDistributorId());
            executePut(result, TradeExportFieldEnum.DISTRIBUTOR_NAME, trade.getTradeDistributor().getDistributorName());
        }


        String exceptions = TradeExceptionStatusUtils.getExceptionToString(trade.getExceptions());
        List<String> exceptTags = trade.getExceptNames();
        if(CollectionUtils.isNotEmpty(exceptTags)){
            exceptions += " ";
            exceptions += exceptTags.stream().reduce((a, b) -> a + " " + b).get();
        }
        executePut(result, TradeExportFieldEnum.EXCEPTIONS, exceptions);
        //包材信息填充
        buildPackTradeInfo(result, tradeExportData.getPackmaItemsDetailMap(), trade);
        //处理tradeExt
        handleTradeExt(trade, tradeExportData, result, context);

        if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
            if (Objects.equals(trade.getIsPackage(),0)) {
                executePut(result, TradeExportFieldEnum.TRADE_PACKAGE_STATUS, "已发未验");
            }else {
                executePut(result, TradeExportFieldEnum.TRADE_PACKAGE_STATUS, "已发已验");
            }
        }

        return result;
    }

    private String getFxSource(Trade trade, Shop fxShop) {
        if (trade.getSubSource() != null) {
            String sourceName =  PlatformSourceConstants.getSourceName(trade.getSubSource());
            if (Objects.equals("未知",sourceName)) {
                sourceName = trade.getSubSource();
            }
            return sourceName;
        }else if (Objects.nonNull(fxShop)){
            String sourceName =fxShop.getSourceName();
            if (Objects.equals("未知",sourceName)) {
                sourceName = fxShop.getSource();
            }
            return sourceName;
        }
        return null;
    }

    /**
     * 淘系  后续的buyerNick存储在openUid字段上
     * @param tbTrade
     * @return
     */
    private String getRealBuyerNick(Shop shop,Trade tbTrade){
        if(needReturnOpenUidWithBuyerNick(tbTrade.getSource())
                || (CommonConstants.PLAT_FORM_TYPE_NEW_FX.equals(tbTrade.getSource()) && needReturnOpenUidWithBuyerNick(tbTrade.getSubSource()))
                || ("sys".equals(tbTrade.getSource()) && shop != null && needReturnOpenUidWithBuyerNick(shop.getSource()))){
            if(StringUtils.isNotBlank(tbTrade.getOpenUid()) && tbTrade.getBuyerNick() != null && tbTrade.getBuyerNick().length() >= 24){
                //代表buyerNick存的是openUid  两者互换  页面展示用
                return tbTrade.getOpenUid();
            }
        }
        return tbTrade.getBuyerNick();
    }

    private boolean needReturnOpenUidWithBuyerNick(String source){
        if(CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_TAO_BAO_TJB.equals(source)
                || CommonConstants.PLAT_FORM_TYPE_1688.equals(source)){
            return true;
        }
        return false;
    }

    private void buildPackTradeInfo(JSONObject result, Map<Long, List<PackmaItemDetailResponse>> packs, Trade trade){
        List<PackmaItemDetailResponse> packmaItemDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(trade.getMessageMemos())){
            trade.getMessageMemos().forEach(memo ->{
                packmaItemDetails.addAll(Optional.ofNullable(packs.get(memo.getSid())).orElse(new ArrayList<>()));
            });
        }else {
            packmaItemDetails.addAll(Optional.ofNullable(packs.get(trade.getSid())).orElse(new ArrayList<>()));
        }
        if (CollectionUtils.isEmpty(packmaItemDetails)){
            return;
        }
        StringBuilder build = new StringBuilder();
        BigDecimal totalWeight = new BigDecimal("0");
        for (PackmaItemDetailResponse pack : packmaItemDetails){
            build.append(pack.getOuterId()).append(", ").append(pack.getPackmaConsumeAmount()).append("; ");
            totalWeight = BigDecimal.valueOf(pack.getPackmaConsumeAmount() == null ? 0 : pack.getPackmaConsumeAmount()).multiply(BigDecimal.valueOf(pack.getWeight() == null ? 0 : pack.getWeight())).add(totalWeight);
        }
        executePut(result, TradeExportFieldEnum.PACK_DETAIL, build.substring(0, build.length() -2));
        executePut(result, TradeExportFieldEnum.PACK_TOTAL_WEIGHT, MathUtils.toString(totalWeight,4));
    }

    /**
     * 比较两个相差的天数 小时
     *
     */
    public static String intervalDays(Date endDate, Date startDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long diff = endDate.getTime() - startDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        return day + "天" + hour + "时" ;
    }

    private void executePut(JSONObject result, TradeExportFieldEnum fieldEnum, String value) {
        result.put(fieldEnum.getField(), value == null ? "" : value);
    }

    private void executePutMoney(Staff staff,JSONObject result,TradeExportFieldEnum fieldEnum,boolean hasPrivilege, Double value) {
        executePutMoney(staff,result,fieldEnum,hasPrivilege, value == null ? null:MathUtils.toString(value));
    }

    private void executePutMoney(Staff staff,JSONObject result,TradeExportFieldEnum fieldEnum, boolean hasPrivilege,String value) {
        if (!hasPrivilege){
            result.put(fieldEnum.getField(),"***");
            return;
        }
        if (StringUtils.isBlank(value)) {
            result.put(fieldEnum.getField(),"");
            return;
        }
        //  int转double格式 保证写入到excel时为数值类型  @see com.raycloud.dmj.utils.excel.ExcelHelper#specialFormatData
        if (ExcelHelper.isInt(value)) {
            value = value + ".0";
            result.put(fieldEnum.getField(),value);
        }else {
            result.put(fieldEnum.getField(),MathUtils.toScaleString(staff.getCompanyId(),value));
        }
    }

    /**
     * 由于数据库增加了理论运费字段，故导出订单需要新的计算理论运费逻辑
     *
     * @param staff
     * @param trade
     * @return
     */
    private String getTheoryPostFeeNew(Staff staff, Trade trade) {
        if (trade == null) {
            return "";
        }
        // 如果是非合单直接返回数据库理论运费字段
        if (!TradeUtils.isMerge(trade)) {
            return String.valueOf(trade.getTheoryPostFee());
        }
        // 如果是合单需要计算所有的合单子单的理论运费之和作为导出主单的理论运费
        if (TradeUtils.isMerge(trade)) {
            List<MessageMemo> messageMemos = trade.getMessageMemos();
            if (CollectionUtils.isEmpty(messageMemos)) {
                if (logger.isDebugEnabled()) {
                    logger.debug(LogHelper.buildLogHead(staff).append(trade.getSid()).append("合单列表为空"));
                }
                return "";
            }
            return String.valueOf(messageMemos.stream().mapToDouble(MessageMemo::getTheoryPostFee).sum());
        }
        return "";
    }

    private String getPostFee(Staff staff, Trade trade) {
        if (TradeUtils.isMerge(trade) && trade.getMessageMemos() != null && !trade.getMessageMemos().isEmpty()) {
            Double postFee = 0D;
            try {
                for (MessageMemo mm : trade.getMessageMemos()) {
                    if (StringUtils.isNotBlank(mm.getPostFee())) {
                        postFee += Double.parseDouble(mm.getPostFee());
                    }
                }
                return String.format("%.2f", postFee);
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, String.format("计算合单运费出错,sid=%s", trade.getSid())), e);
            }
        }
        return trade.getPostFee();
    }

    private String getSaleFee(Staff staff, Trade trade){
        if(TradeUtils.isMerge(trade) && CollectionUtils.isNotEmpty(trade.getMessageMemos())){
            BigDecimalWrapper saleFee = new BigDecimalWrapper();
            try {
                for (MessageMemo memo : trade.getMessageMemos()) {
                    if (StringUtils.isNotBlank(memo.getSaleFee())) {
                        saleFee.add(memo.getSaleFee());
                    }
                }
                return MathUtils.toScaleString(staff.getCompanyId(),saleFee.get());
            }catch (Exception e){
                logger.error(LogHelper.buildLog(staff, String.format("计算合单分销金额出错,sid=%s", trade.getSid())), e);
            }
        }
        return MathUtils.toScaleString(staff.getCompanyId(),trade.getSaleFee());
    }

    /**
     * 计算商品数量
     *
     * @param trade
     * @return
     */
    private static Integer getItemCount(Trade trade) {
        int count = 0;
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            count += order.getNum();
        }
        return count;
    }

    String getReceiverMobile(String mobile) {
        if (mobile != null && !(mobile = mobile.trim()).isEmpty()) {
            StringBuilder buf = new StringBuilder();
            if (mobile.length() > 3) {
                buf.append(mobile.substring(0, 3));
                if (mobile.length() > 7) {
                    buf.append("****").append(mobile.substring(7));
                } else if (mobile.length() <= 7) {
                    buf.append("****");
                }
                return buf.toString();
            } else {
                return mobile;
            }
        }
        return null;
    }

    String getReceiverPhone(String tel) {
        if (tel != null && !(tel = tel.trim()).isEmpty()) {
            StringBuilder buf = new StringBuilder();
            int i = tel.indexOf("-");
            if (i > -1) {
                String telNo = tel.substring(i + 1);
                int len = telNo.length();
                buf.append(tel.substring(0, i + 1)).append(len > 3 ? telNo.substring(0, 3) + "****" : telNo);
            } else {
                int len = tel.length();
                buf.append(len > 3 ? tel.substring(0, 3) + "****" : tel);
            }
            return buf.toString();
        }
        return null;
    }


    /**
     * 得到平台订单号(合单的时候需要合并)
     *
     * @param staff
     * @param trade
     * @return
     */
    private String getTids(Staff staff, Trade trade) {
        if (!TradeUtils.isMerge(trade)) {
            return trade.getTid();
        }
        List<MessageMemo> memos = trade.getMessageMemos();
        StringBuilder bf = new StringBuilder();
        if (memos != null) {
            for (MessageMemo memo : memos) {
                try {
                    //主单，排在第一位
                    if (trade.getSid().equals(memo.getSid())){
                        bf = new StringBuilder(memo.getTid()).append(",").append(bf);
                    }else {
                        bf.append(memo.getTid()).append(",");
                    }
                } catch (Exception e) {
                    logger.error(LogHelper.buildErrorLog(staff, e, "计算合单平台订单号出错"), e);
                }
            }
        }
        return bf.length() > 0 ? bf.deleteCharAt(bf.length() - 1).toString() : "";
    }

    private void launchAsyncTradeExportEvent(Staff staff, TradeExportParams exportParams) throws Exception {
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(EnumDownloadCenterModule.TRADE.getCode());
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }
        String[][] tradeTitle = getTitleByExportFields(getNeedFieldsList(exportParams.getExportFields()), TradeExportFieldEnum.FIELD_TRADE_SCOPE,staff.getCompanyId());

        String fileName = new String("订单导出".getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle("订单导出");
        param.setTitleArr(tradeTitle);
        param.setModule(EnumDownloadCenterModule.TRADE.getCode());
        param.setNeedVerify(needVerify(staff, exportParams.getParams()));
        eventCenter.fireEvent(this, new EventInfo("tradeOrder.download.excel").setArgs(new Object[]{staff, param, exportParams}), false);
    }

    private boolean needVerify(Staff staff, TradeQueryParams params) {
        if (tradeLocalConfigurable.isExportVerifyWhiteList(staff.getCompanyId())) {
            return false;
        }
        return tradeSearchService.countTaobaoSeries(staff, params) > 0;
    }

    private String[][] getTitleByExportFields(List<String> exportFields, String fieldTradeScope,Long companyId) {
        if (CollectionUtils.isEmpty(exportFields)) {
            return getExporTitle(fieldTradeScope,companyId);
        }
        String[][] excelTitle = new String[1][];
        List<TradeExportFieldEnum> fieldsByScope = TradeExportFieldEnum.getFieldsByScope(fieldTradeScope);
        List<String> titles = new ArrayList<String>();
        for (TradeExportFieldEnum tradeExportFieldEnum : fieldsByScope) {
            if (exportFields.contains(tradeExportFieldEnum.getField())) {
                titles.add(TradeExportHelper.getExportFieldName(companyId,tradeExportFieldEnum));
            }
        }
        excelTitle[0] = new String[titles.size()];
        titles.toArray(excelTitle[0]);
        return excelTitle;
    }

    private List<Trade> queryExportTrades(Staff staff, TradeExportParams exportParams, Long[] sids) {
        Assert.isTrue(!tradeLocalConfigurable.isCloseTradeExport(staff.getCompanyId()), "订单导出已关闭！");
        //导出工程触发的查询,只查询归档订单,归档查询1000个一批查询
        if (null != sids){
            //先全量定义归档查询的字段, 归档查询没有ext数据
            List<Trade> result = new ArrayList<>();
            Lists.partition(Lists.newArrayList(sids), 1000).forEach(part ->{
                TradeQueryParams params = new TradeQueryParams();
                params.setExportSource(TradeExcelExportTask.TASK_EXPORT_3MONTH);
                params.setSid(part.toArray(new Long[0]));
                params.setPage(new Page().setPageNo(1).setPageSize(part.size()));
                result.addAll(Optional.of(tradeDataService.search3Month(staff, params, true).getList()).orElse(new ArrayList<>()));
            });
            return result;
        }
        TradeQueryParams params = exportParams.getParams();
        if (params.getContext().isUniqueQuery() || (params.getOutSids() != null && params.getOutSids().length > 0) || ("mobileTail".equals(params.getKey()) && StringUtils.isNotBlank(params.getText()))) {
            params.setStartTime(null);
        }
        /*if (hasOrderExtInfo(staff,exportParams)) {
            params.setNeedOrderExtInfo(true);
        }*/
        final List<Long> commonIds = params.getCommonIds();
        final List<Long> cloudIds = params.getCloudIds();
        //统一切换为新流程查询，为兼容老数据，newExport为2时，走的是老流程
        List<Trade> tradeList = isNewExport(staff, exportParams)?
                //新版查询订单，按照sids查询
                queryTradeBySids(staff,exportParams)
                :
                //旧版查询 时间切片查询
                new ExportTimeSliceSearcher<Trade>() {
                    @Override
                    protected void filter(ExportParams p) {
                        buildSearchParams(params, exportParams.getOriginStart(), exportParams.getOriginEnd(), exportParams.getParams().getPage(), 2);
                        Trades trades = queryTrades(staff, params);
                        //订单总数小于20000默认走原有查询方式
                        if (trades.getTotal() != null && trades.getTotal() < 100000) {
                            List<TimeSlice> list = new ArrayList<>();
                            list.add(new TimeSlice(exportParams.getOriginStart(), exportParams.getOriginEnd()));
                            p.setTimeSlices(list);
                        }
                    }

                    @Override
                    protected List<Trade> query(Date start, Date end, Page page) {
                        buildSearchParams(params, start, end, page, 1);
                        List<Trade> list = queryTrades(staff, params).getList();
                        jdlogUpload(staff, list);
                        return list;
                    }

                    private void buildSearchParams(TradeQueryParams params, Date start, Date end, Page page, int queryFlag) {
                        params.setStartTime(start);
                        params.setEndTime(end);
                        params.setPage(page);
                        params.setQueryFlag(queryFlag);
                        params.setCommonIds(commonIds);
                        params.setCloudIds(cloudIds);
                    }

                }.search(exportParams);

        return CollectionUtils.isNotEmpty(tradeList)?decryptJd(staff, tradeList):tradeList;
    }

    /**
     * 查询订单的关联订单：换货单、补发单、手工单
     * @param staff
     * @param trades
     * @return
     */
    private List<Trade> queryRelationTrade(Staff staff,  List<Trade> trades){
        if (CollectionUtils.isEmpty(trades)){
            return trades;
        }
        TradeQueryParams relationParam = new TradeQueryParams();
        relationParam.setQueryType(0);
        relationParam.setNeedOrder(0);
        relationParam.setPage(new Page(1, 2000));
        List<Trade> tradeList = new ArrayList<>();
        Set<String> queryTid = new HashSet<>();
        for (Trade trade : trades) {
            queryTid.add(trade.getTid());
            //合单，子单的关联订单也需要查询
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) && CollectionUtils.isNotEmpty(trade.getMessageMemos())) {
                for (MessageMemo messageMemo : trade.getMessageMemos()) {
                    queryTid.add(messageMemo.getTid());
                }
            }
        }
        Map<String, List<Trade>> relationTradeMap = new HashMap<>();
        //每100个tid进行一批关联查询
        Lists.partition(new ArrayList<String>(queryTid), 100).forEach(part ->{
            relationParam.setTid(part.toArray(new String[0]));
            Trades relationTrades = tradeSearchService.search(staff, relationParam);
            if (null == relationTrades || CollectionUtils.isEmpty(relationTrades.getList())){
                return;
            }
            part.forEach(tid ->{
                List<Trade> releaseTrade = relationTrades.getList().stream().filter(trade ->trade.getTid().contains(tid)).collect(Collectors.toList());
                relationTradeMap.computeIfAbsent(tid, t -> new ArrayList<>()).addAll(releaseTrade);
            });
        });
        for (Trade trade : trades) {
            tradeList.add(trade);
            //查询该订单的换货补发手工单,只支持单条查询
            if (!TradeUtils.isMerge(trade)){
                queryAndAddRelationTrade(relationTradeMap, trade.getTid(), trade.getSid(), staff, tradeList);
            }else {
                //合单场景，先查询主单的，在查询子单的
                List<MessageMemo> messageMemos = trade.getMessageMemos();
                if (CollectionUtils.isEmpty(messageMemos)){
                    continue;
                }
                for (MessageMemo memo : messageMemos){
                    if (trade.getSid().equals(memo.getSid())){
                        queryAndAddRelationTrade(relationTradeMap, memo.getTid(), trade.getSid(), staff, tradeList);
                        break;
                    }
                }
                for (MessageMemo memo : messageMemos){
                    if (trade.getSid().equals(memo.getSid())){
                        continue;
                    }
                    queryAndAddRelationTrade(relationTradeMap, memo.getTid(), trade.getSid(), staff, tradeList);
                }
            }
        }
        return tradeList;
    }

    private void queryAndAddRelationTrade(Map<String, List<Trade>> relationTradeMap, String tid, Long sid, Staff staff, List<Trade> trades){
        List<Trade> relationTrades = relationTradeMap.get(tid);
        if (CollectionUtils.isEmpty(relationTrades)){
            return;
        }
        Set<Long> sids = new HashSet<>();
        Map<String, List<Trade>> relationMap = new HashMap<>();
        //订单导出顺序：补发 -> 换货 -> 手工 -> 手工拆单
        for (Trade relationTrade : relationTrades){
            if (!sids.add(relationTrade.getSid())){
                continue;
            }
            if (sid.equals(relationTrade.getSid())){
                continue;
            }
            if (!relationTrade.getTid().contains(tid)){
                continue;
            }
            if ("reissue".equals(relationTrade.getType())){
                relationMap.computeIfAbsent("reissue", t -> new ArrayList<>()).add(relationTrade);
                continue;
            }
            if ("changeitem".equals(relationTrade.getType())){
                relationMap.computeIfAbsent("changeitem", t -> new ArrayList<>()).add(relationTrade);
                continue;
            }
            if ("sys".equals(relationTrade.getSource())){
                if (TradeUtils.isSplit(relationTrade)){
                    relationMap.computeIfAbsent("sys_split", t -> new ArrayList<>()).add(relationTrade);
                    continue;
                }
                relationMap.computeIfAbsent("sys", t -> new ArrayList<>()).add(relationTrade);
            }
        }
        trades.addAll(CollectionUtils.isEmpty(relationMap.get("reissue")) ? new ArrayList<>() : relationMap.get("reissue"));
        trades.addAll(CollectionUtils.isEmpty(relationMap.get("changeitem")) ? new ArrayList<>() : relationMap.get("changeitem"));
        trades.addAll(CollectionUtils.isEmpty(relationMap.get("sys")) ? new ArrayList<>() : relationMap.get("sys"));
        trades.addAll(CollectionUtils.isEmpty(relationMap.get("sys_split")) ? new ArrayList<>() : relationMap.get("sys_split"));
    }

    private List<Trade> queryTradeBySids(Staff staff, TradeExportParams exportParams) {
        initExportSidsPage(staff, exportParams);
        List<List<Long>> exportSidsPage = exportParams.getExportSidsPage();
        if (CollectionUtils.isEmpty(exportSidsPage)){
            return Lists.newArrayList();
        }
        List<Long> first = exportSidsPage.remove(0);
        if (CollectionUtils.isEmpty(first)) {
            return null;
        }
        TradeQueryParams params = exportParams.getParams();
        params.setSid(first.toArray(new Long[0]));
        Trades trades = new Trades();
        try {
            trades = queryByAllSids(staff, params);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "新版订单导出查询报错" + e.getMessage()), e);
        }
        return trades.getList();
    }

    /**
     * 初始化所有的系统订单号
     * @param staff
     * @param exportParams
     */
    private void initExportSidsPage(Staff staff, TradeExportParams exportParams) {
        if (!exportParams.isFirstFilter()){
            return;
        }
        exportParams.setFirstFilter(false);
        long start = System.currentTimeMillis();
        TradeQueryParams params = exportParams.getParams();
        ArrayList<List<Long>> exportSidsPage = new ArrayList<>();
        exportParams.setExportSidsPage(exportSidsPage);
        List<Long> mainSids = new ArrayList<>();
        Trades trades = LogKit.took(() -> {
            params.setFields(QUERY_FILEDS);
            Integer originQueryFlag = params.getQueryFlag();
            Boolean originQueryOrder = params.getQueryOrder();
            //只查询订单不查询总数
            params.setQueryFlag(1);
            params.setQueryOrder(false);
            Trades result = null;
            if(params.getQueryId() != null && params.getQueryId() - SystemTradeQueryParamsContext.QUERY_3MONTH_AGO == 0 && tradeLocalConfigurable.isUseTradeColdQueryCompanyIds(staff.getCompanyId())){
                params.setPage(new Page().setPageSize(MAX_SIZE));
                params.setExportSource(TradeExcelExportTask.TASK_EXPORT_3MONTH);
                mainSids.addAll(tradeColdDataService.queryTradeSidList(staff, params, true));
            }else {
                result = queryByAllSids(staff, params);
            }

            params.setFields(null);
            params.setQueryFlag(originQueryFlag);
            params.setQueryOrder(originQueryOrder);
            return result;
        }, staff, logger);
        if ((trades == null || CollectionUtils.isEmpty(trades.getList())) && CollectionUtils.isEmpty(mainSids)){
            return;
        }
        long took = System.currentTimeMillis() - start;
        int total = (trades == null || CollectionUtils.isEmpty(trades.getList())) ? mainSids.size() : trades.getList().size();
        Logs.ifDebug(LogHelper.buildLog(staff, "新版订单导出查询所有sid,size:"+ total +"花费时间：" + took));
        if (total> 250000){
            Logs.ifDebug(LogHelper.buildLog(staff, "warning   warning sid too long 警告警告新版订单导出查询所有sid,size:"+ total + "花费时间：" + took));
        }
        List<Long> sids = new ArrayList<>();
        if (trades != null && CollectionUtils.isNotEmpty(trades.getList())){
            List<Trade> list = trades.getList();
            for (Trade trade : list) {
                Long sid = TradeUtils.isMerge(trade) ? trade.getMergeSid() : trade.getSid();
                if (!sids.contains(sid)){
                    sids.add(sid);
                }
            }
        }else {
            sids = mainSids;
        }

        for (List<Long> sidPartition: Lists.partition(sids, 2000)) {
            //todo 这儿有bug 2000个sid查询出来的订单少于1000时 后续订单无法导出
            ArrayList<Long> sidList = new ArrayList<>(sidPartition);
            exportSidsPage.add(sidList);
        }
    }

    private Trades queryByAllSids(Staff staff, TradeQueryParams params) {
        Page page = new Page();
        //临时方案 最大仅允许一次性导出100W订单，  此时预计占用内存1000M  多客户导出会出现内存溢出
        page.setPageSize(MAX_SIZE);
        //只查询一页
        params.setPage(page);
        if ((params.getQueryId() != null && params.getQueryId() - SystemTradeQueryParamsContext.QUERY_3MONTH_AGO == 0)) {
            if (null != params.getSid() && params.getSid().length > 0){
                page.setPageSize(params.getSid().length);
            }
            return tradeDataService.search3Month(staff, params, true);
        }
        if ("consign_time".equals(params.getTimeType()) && params.getSysStatus() == null ) {
            params.setSysStatus(Trade.SYS_STATUS_CLOSED, Trade.SYS_STATUS_FINISHED, Trade.SYS_STATUS_SELLER_SEND_GOODS, Trade.SYS_STATUS_CANCEL);
        }

        //TODO  这里没有熔断，有问题
        Trades trades = tradeSearchService.backTradeSearchUnfuse(staff, params);
        if(CollectionUtils.isEmpty(trades.getList())){
            trades.setList(searchOutSid(staff, params));
        }
        return trades;

    }

    private boolean isNewExport(Staff staff, TradeExportParams exportParams) {
        Boolean newExport = exportParams.getNewExport();
        if (newExport == null) {
            exportParams.setNewExport(tradeConfigService.get(staff).isNewExport());
        }
        return exportParams.getNewExport();
    }

    private List<Trade> decryptJd(Staff staff, List<Trade> tradeList) {
        String whitelist = ConfigHolder.JD_HU_GRAY_CONFIG.getWhitelist();
        if (StringUtils.isEmpty(whitelist) || !Arrays.asList(whitelist.split(",")).contains(String.valueOf(staff.getCompanyId()))) {
            tradeList.forEach(trade ->
            {
                if (TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_JD)) {
                    trade.setReceiverName("***");
                    trade.setReceiverAddress("***");
                    trade.setReceiverMobile("***");
                    trade.setReceiverPhone("***");
                }
            });
            return tradeList;
        }
        Long[] jdSids = tradeList.stream().filter(t -> TradeUtils.platformMatch(staff, t, CommonConstants.PLAT_FORM_TYPE_JD)).map(Trade::getSid).toArray(Long[]::new);
        if (jdSids == null || jdSids.length == 0){
            return tradeList;
        }
        Map<Long, Trade> jdTradeMap = TradeUtils.toMapBySid(tradeSearchService.queryBySids(staff, true, jdSids));
        List<Trade> result = new ArrayList<>();
        for (Trade trade : tradeList) {
            result.add(jdTradeMap.getOrDefault(trade.getSid(),trade));
        }
        return result;
    }

    private Trades queryTrades(Staff staff, TradeQueryParams params) {
        //禁用pgl查询
        params.setAllowedPgl(false);
        if ((params.getQueryId() != null && params.getQueryId() - SystemTradeQueryParamsContext.QUERY_3MONTH_AGO == 0)) {
            return tradeDataService.search3Month(staff, params, true);
        }
        if ("consign_time".equals(params.getTimeType()) && params.getSysStatus() == null ) {
            params.setSysStatus(Trade.SYS_STATUS_CLOSED, Trade.SYS_STATUS_FINISHED, Trade.SYS_STATUS_SELLER_SEND_GOODS, Trade.SYS_STATUS_CANCEL);
        }

        Trades trades = tradeSearchService.search(staff, params);
        if(CollectionUtils.isEmpty(trades.getList())){
            trades.setList(searchOutSid(staff, params));
        }
        return trades;
    }

    @Resource
    JdTradeAccess jdTradeAccess;

    private void jdlogUpload(Staff staff, List<Trade> list) {
        try {
            String ip = cache.get("jd_log_ip_" + staff.getId());
            String deviceId = cache.get("jdDeviceId_" + staff.getId());
            cache.touch("jd_log_ip_" + staff.getId(), 60  * 5);
            if (CollectionUtils.isEmpty(list) || StringUtils.isEmpty(deviceId)){
                return;
            }

            String[] deviceIdUserName = deviceId.split("&");
            deviceId = deviceIdUserName[0];
            if (StringUtils.isEmpty(deviceId) || "null".equals(deviceId)){
                return;
            }
            staff.setAccountName(deviceIdUserName[1]);

            Map<Long, List<Trade>> collect = list.stream().filter(t -> CommonConstants.PLAT_FORM_TYPE_JD.equals(t.getSource())).collect(Collectors.groupingBy(Trade::getUserId));
            if (MapUtils.isNotEmpty(collect)){
                for (Map.Entry<Long, List<Trade>> entry : collect.entrySet()) {
                    Long key = entry.getKey();
                    for (List<String> tids : Lists.partition(entry.getValue().stream().map(Trade::getTid).collect(Collectors.toList()), 100)) {
                        String tidStr = StringUtils.join(tids, ",");
                        jdTradeAccess.uploadOrderInfoLog(ip,
                                deviceId,
                                tidStr,
                                tids.size() == 1 ? 7 : 3,
                                "https://erp.superboss.cc/trade/export",
                                staff.getUserIdMap().get(key),
                                staff);

                        jdTradeAccess.uploadDBOperationLog(ip,
                                deviceId,
                                "https://erp.superboss.cc/trade/export",
                                "select * from trade where tid in (" + tidStr + ")",
                                staff.getUserIdMap().get(key),
                                staff);
                    }
                }
            }
        } catch (Exception e) {
            Logs.error("京东订单导出时上传日志失败"+ e.getMessage(), e);
        }
    }

    String getOutstockName(String type) {
        if ("trade_out_1".equals(type)) {
            return "报损单";
        } else if ("trade_out_2".equals(type)) {
            return "领用单";
        } else if ("trade_out_3".equals(type)) {
            return "调整单";
        } else if ("trade_out".equals(type)) {
            return "其他";
        }
        return "";
    }

    String getSysConsigned(int sysConsigned){
        switch (sysConsigned){
            case 1:
                return "ERP发货";
            case 2:
                return "其他ERP发货";
            default:
                return "";
        }
    }

    //一单多包子单号查询
    private List<Trade> searchOutSid(Staff staff, TradeQueryParams params){
        String[] outSids = params.getOutSids();
        if (outSids != null && outSids.length > 0) {
            String outSid = outSids[0];
            Long sid = multiPacksPrintTradeLogService.querySidByOutSid(staff, outSid);
            if (sid != null) {
                params.setSid(sid);
                params.setOutSids(null);
                params.setAllowedPgl(false);
                return tradeSearchService.search(staff, params).getList();
            }
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 商品更换明细导出
     * @param staff
     * @param exportParams
     * @param isAsync
     * @return
     */
    public String[][] exportItemChangeDetail (Staff staff, TradeItemChangeDetailExportParams exportParams, boolean isAsync) {
        String exportFields = exportParams.getExportFields();
        TradeItemChangeQueryParams params = exportParams.getParams();
        List<String> needFields = getNeedFieldsList(exportFields);
        if (isAsync) {
            try {
                launchAsyncExportEvent(staff, exportParams, EnumDownloadCenterModule.TRADE.getCode(), TradeItemChangeDetailExportParams.EXCEL_NAME, ExportEventConstants.ITEM_CHANGE_DETAIL_DOWNLOAD_EXCEL);
            } catch (Exception e) {
                logger.error(LogHelper.buildLogHead(staff).append(TradeItemChangeDetailExportParams.EXCEL_NAME + "导出失败"), e);
            }
            return null;
        }

        //查询数据
        List<TradeItemChangeDetail> details = queryExportTradesItemChangeDetail(staff, params);
        //数据为空
        if (details == null || details.size() == 0) {
            return null;
        }

        return getResultByNeedFields(staff, details, needFields, TradeExportFieldEnum.FIELD_TRADE_ITEM_CHANGE_DETAIL_SCOPE);

    }

    private void launchAsyncExportEvent(Staff staff, TradeItemChangeDetailExportParams exportParams, String module, String excelTitle, String eventName) throws Exception {
        DownloadCenter condition = new DownloadCenter();
        condition.setStaffId(staff.getId());
        condition.setModule(module);
        DownloadCenter downloadCenterOld = downloadCenterService.queryNoExportFinish(staff, condition);
        if (downloadCenterOld != null) {
            throw new IllegalArgumentException("该模块已经有在导出中的任务，请稍后再导出");
        }
        String[][] title = getTitleByExportFields(getNeedFieldsList(exportParams.getExportFields()), TradeExportFieldEnum.FIELD_TRADE_ITEM_CHANGE_DETAIL_SCOPE,staff.getCompanyId());
        String fileName = new String(excelTitle.getBytes(), "utf-8") + DateFormatUtils.format(Calendar.getInstance(), "yyyyMMddHHmmss") + ".xls";

        FileDownloadParam param = new FileDownloadParam();
        param.setFileName(fileName);
        param.setExcelTitle(excelTitle);
        param.setTitleArr(title);
        param.setModule(EnumDownloadCenterModule.TRADE.getCode());
        eventCenter.fireEvent(this, new EventInfo(eventName).setArgs(new Object[]{staff, param, exportParams}), false);
    }

    private List<TradeItemChangeDetail> queryExportTradesItemChangeDetail(Staff staff, TradeItemChangeQueryParams queryParams) {
        TradeItemChangeDetailParam param = new TradeItemChangeDetailParam();
        param.setCompanyId(staff.getCompanyId());

        String operateTimeStart = queryParams.getOperateTimeStart() == null ? null :DateUtils.datetime2Str(queryParams.getOperateTimeStart());
        param.setOperateTimeStart(operateTimeStart);
        String operateTimeEnd = queryParams.getOperateTimeEnd() == null ? null :DateUtils.datetime2Str(queryParams.getOperateTimeEnd());
        param.setOperateTimeEnd(operateTimeEnd);
        List<String> outerIdList = StringUtils.isNotEmpty(queryParams.getOuterId()) ? Arrays.asList(queryParams.getOuterId().split(",")) : new ArrayList<>();
        param.setOuterId(outerIdList);
        List<String> skuOuterIdList = StringUtils.isNotEmpty(queryParams.getSkuOuterId()) ? Arrays.asList(queryParams.getSkuOuterId().split(",")) : new ArrayList<>();
        param.setOldSkuOuterId(skuOuterIdList);

        param.setPageSize(queryParams.getPage().getPageSize());
        param.setPageNo(queryParams.getPage().getPageNo());
        param.setStartRow((param.getPageNo() - 1) * param.getPageSize());

        List<TradeItemChangeDetail> list = tradeItemChangeDetailDao.query(staff, param);

        if (list == null) {
            return new ArrayList<>();
        }

        for (TradeItemChangeDetail detail : list) {
            tradeItemChangeDetailBusiness.convertDetail(detail);
        }

        return list;
    }

    private String[][] getResultByNeedFields(Staff staff, List<TradeItemChangeDetail> details, List<String> needFields, String scope) {
        List<String[]> result = new ArrayList<>();
        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(details));
        List<String> resultFields = getResultFields(staff, needFields, scope);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            List<String> data = new ArrayList<>();
            resultFields.forEach(key -> {
                String value = String.valueOf(jsonObject.get(key));
                if (TradeExportFieldEnum.CHANGE_DETAIL_OPERATE_TIME.getField().equals(key)) {
                    Long longValue = Strings.getAsLong(value, false);
                    value = DateUtils.datetime2Str(null == longValue ? new Date() : new Date(longValue));
                }
                data.add(value);
            });
            result.add(data.toArray(new String[0]));
        }

        return result.toArray(new String[jsonArray.size()][]);
    }

    /**
     * 订单导出的信息脱敏流程
     * @param staff
     * @param trades 订单列表
     * @param usePlatform 是否调用平台脱敏接口
     * @param isSensitiveSysTrade 是否系统订单脱敏
     */
    public void sensitiveTrade(Staff staff, List<Trade> trades, boolean usePlatform, boolean isSensitiveSysTrade, Boolean isSensitive) {
        try {
            boolean yzPlaintext = tradeLocalConfigurable.isTradeExportYzPlaintextCompanyIds(staff.getCompanyId());
            boolean jdVcPlaintext = tradeLocalConfigurable.isTradeExportJdVcPlaintextCompanyIds(staff.getCompanyId());
            boolean guangfaPlaintext = tradeLocalConfigurable.isTradeExportGuangfaPlaintextCompanyIds(staff.getCompanyId());
            boolean hmPlaintext = tradeLocalConfigurable.isTradeExportHMPlaintextCompanyIds(staff.getCompanyId());
            //小大人，快手解密明文导出
            List<Trade> ksTradeList = new ArrayList<>();
            //有赞明文导出
            List<Trade> yzTradeList = new ArrayList<>();
            //盒马明文导出
            List<Trade> hmTradeList = new ArrayList<>();
            //脱敏处理
            List<Trade> sensitiveTradeList = new ArrayList<>();
            //统一解密的集合，后续都往这里添加,上面四个平台，暂时没有移入下面diamond白名单，后续改造
            List<Trade> commonDecrypt = new ArrayList<>();
            Set<String> decryptSource = TradeExportDiamondUtils.getTradeExportDecryptPlatForm(staff);
            for (Trade trade : trades){
                if (isSensitive != null && isSensitive && TradeUtils.isKuaishouTrade(trade)){
                    ksTradeList.add(trade);
                    continue;
                }
                if (yzPlaintext && TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_YZ)){
                    yzTradeList.add(trade);
                    continue;
                }
                //广发明文导出 (系统内保存的就是明文系统加密，不需要调平台方法)
                if (guangfaPlaintext && TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_GUANGFA)){
                    continue;
                }
                //京东自营明文导出 (系统内保存的就是明文系统加密，不需要调平台方法)
                if (jdVcPlaintext && TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_JD_VC)){
                    continue;
                }
                //奇门订单跳过解密与脱敏
                if (Objects.equals(CommonConstants.PLAT_FORM_TYPE_QIMEN, trade.getSource())){
                    if (CipherTextUtils.ifEncrypt(trade)){
                        trade.setReceiverAddress("****");
                        trade.setReceiverName("****");
                        trade.setReceiverMobile("****");
                        trade.setReceiverPhone("****");
                    }
                    continue;
                }
                //盒马订单明文导出
                if (hmPlaintext && TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_HEMAOS)){
                    hmTradeList.add(trade);
                    continue;
                }
                //解密白名单处理
                if (decryptSource.stream().anyMatch(t -> TradeUtils.platformMatch(staff, trade, t))){
                    commonDecrypt.add(trade);
                    continue;
                }
                sensitiveTradeList.add(trade);
            }
            if (CollectionUtils.isNotEmpty(ksTradeList)){
                List<List<Trade>> partitionList = Lists.partition(ksTradeList, 50);
                for (List<Trade> tradeList : partitionList) {
                    commonTradeDecryptBusiness.batchDecrypt(staff, tradeList, TradeDecryptSourceEnum.TRADE_EXPORT_DECRYPT);
                }
            }
            if (CollectionUtils.isNotEmpty(hmTradeList)){
                List<List<Trade>> partitionList = Lists.partition(hmTradeList, 50);
                for (List<Trade> tradeList : partitionList) {
                    unionTradeDecryptBusiness.decryptTrades(staff, tradeList, null, TradeDecryptSourceEnum.TRADE_EXPORT_DECRYPT, true, null);
                }
            }
            if (CollectionUtils.isNotEmpty(yzTradeList)){
                List<List<Trade>> partitionList = Lists.partition(yzTradeList, 50);
                for (List<Trade> tradeList : partitionList) {
                    commonTradeDecryptBusiness.batchDecrypt(staff, tradeList, TradeDecryptSourceEnum.TRADE_EXPORT_DECRYPT);
                }
            }
            if (CollectionUtils.isNotEmpty(commonDecrypt)){
                List<List<Trade>> partitionList = Lists.partition(commonDecrypt, 50);
                for (List<Trade> tradeList : partitionList) {
                    unionTradeDecryptBusiness.decryptTrades(staff, tradeList, null, TradeDecryptSourceEnum.TRADE_EXPORT_DECRYPT, true, null);
                }
            }
            if (CollectionUtils.isNotEmpty(sensitiveTradeList)){
                dataSecurityHandleService.sensitiveTrade(staff, sensitiveTradeList, usePlatform, isSensitiveSysTrade);
            }
        }catch (Exception e){
            logger.error("订单导出时,解密脱敏失败" + e);
        }
    }

    public String[][] transformExportData(List<LinkedList<String>> result){
        List<String[]> arrResult = new ArrayList<>();
        if (CollectionUtils.isEmpty(result)){
            return new String[0][0];
        }
        for (LinkedList<String> list : result){
            arrResult.add(list.toArray(new String[0]));
        }
        return arrResult.toArray(new String[0][]);
    }


    /**
     * 导出tradeExt
     *
     * @param trade
     * @param tradeExportData
     * @param jsonData
     */
    void handleTradeExt(Trade trade, TradeExportData tradeExportData, JSONObject jsonData, TradeExportContext context) {
        if (trade != null && trade.getTradeExt() != null) {
            if (context.switchData.selfBuiltExtSwitch) {
                List<String> selfBuiltDepositAmount = new ArrayList<>();
                List<String> selfBuiltPaymentReceivable = new ArrayList<>();
                if (TradeUtils.isMerge(trade) && CollectionUtils.isNotEmpty(trade.getMessageMemos())) {
                    for (MessageMemo messageMemo : trade.getMessageMemos()) {
                        if (messageMemo.getSelfBuiltDepositAmount() != null) {
                            selfBuiltDepositAmount.add(messageMemo.getSelfBuiltDepositAmount());
                        }
                        if (messageMemo.getSelfBuiltPaymentReceivable() != null) {
                            selfBuiltPaymentReceivable.add(messageMemo.getSelfBuiltPaymentReceivable());
                        }
                        if (CollectionUtils.isNotEmpty(selfBuiltDepositAmount)) {
                            executePut(jsonData, TradeExportFieldEnum.SELF_BUILT_DEPOSIT_AMOUNT, NumberUtils.add(selfBuiltDepositAmount.toArray(new String[0])));
                        }
                        if (CollectionUtils.isNotEmpty(selfBuiltPaymentReceivable)) {
                            executePut(jsonData, TradeExportFieldEnum.SELF_BUILT_PAYMENT_RECEIVABLE, NumberUtils.add(selfBuiltPaymentReceivable.toArray(new String[0])));
                        }
                    }
                } else {
                    Optional.ofNullable(TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "selfBuiltDepositAmount")).ifPresent(val -> executePut(jsonData, TradeExportFieldEnum.SELF_BUILT_DEPOSIT_AMOUNT, (String) val));
                    Optional.ofNullable(TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "selfBuiltPaymentReceivable")).ifPresent(val -> executePut(jsonData, TradeExportFieldEnum.SELF_BUILT_PAYMENT_RECEIVABLE, (String) val));
                }
            }
            if (context.switchData.tradeFilesSwitch){
                List<String> tradePictureMemoUris = (List<String>) TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "tradePictureMemoUris");
                if (CollectionUtils.isNotEmpty(tradePictureMemoUris)){
                    executePut(jsonData, TradeExportFieldEnum.TRADE_FILES, tradePictureMemoUris.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
                }
            }
            if (context.switchData.actualVolumeSwitch) {
                Optional.ofNullable(TradeExtUtils.getExtraFieldValue(trade.getTradeExt(), "actualVolume")).ifPresent(val -> executePut(jsonData, TradeExportFieldEnum.ACTUAL_VOLUME, (String) val));
            }
        }
    }

    private String getFxTotalCost(Staff staff, Trade trade) {
        if (!TradeUtils.isFxOrMixTrade(trade)) {
            return null;
        }
        boolean hasFxCostPower = staff.getPowerDataPrivilegeSettings() != null && staff.getPowerDataPrivilegeSettings().contains("100030015");//分销总成本数据权限
        if (!hasFxCostPower) {
            return "***";
        }
        if (TradeUtils.isMerge(trade) && trade.getSid().equals(trade.getMergeSid()) && trade.getMessageMemos() != null && !trade.getMessageMemos().isEmpty()) {
            Double totalCost = 0D;
            try {
                for (MessageMemo mm : trade.getMessageMemos()) {
                    if (StringUtils.isNotBlank(mm.getFxCost()) && TradeUtils.isMerge(trade) && Objects.equals(trade.getMergeSid(), mm.getSid())) {
                        totalCost += Double.parseDouble(mm.getFxCost());
                    }
                }
                return String.format("%.2f", totalCost);
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, String.format("计算合单分销总成本出错,sid=%s", trade.getSid())), e);
            }
        }
        return PaymentUtils.calculateFxTotalCost(trade);
    }

    public TradeExportContext initTradeExportContext(Staff staff, TradeExportParams exportParams, TradeExcelExportTask task, Integer exportType){
        TradeExportContext context = new TradeExportContext();
        context.fillExportPrivilege(staff);
        String exportFields = null;
        Long queryId = null;
        boolean exportRelation = false;
        boolean exportMultiPackActualInfo = false;
        boolean needCopyTrade = false;
        if (Objects.nonNull(exportParams)){
            TradeQueryParams params = exportParams.getParams();
            exportFields = exportParams.getExportFields();
            queryId = Objects.isNull(params) ? null : params.getQueryId();
            exportRelation = Objects.nonNull(params) && Objects.nonNull(params.getTid()) && params.getTid().length == 0 && exportParams.isExportRelation();
            exportMultiPackActualInfo = exportParams.isExportMultiPackActualInfo();
            needCopyTrade = exportParams.isNeedCopyTrade();
        }
        if (Objects.nonNull(task)){
            exportType = task.getExportType();
            exportFields = task.getFileDownloadParams();
            queryId = task.getQueryId();
            exportRelation = getExportRelationTrade(task);
            exportMultiPackActualInfo = getExportMultiPackActualInfo(task);
            needCopyTrade = getNeedCopyTrade(task);
        }
        List<String> needFields = getNeedFieldsList(exportFields);
        List<TradeExportFieldEnum> exportFieldEnums;
        if (TradeExcelExportTask.TASK_TYPE_TRADE_ORDER.equals(exportType)){
            boolean isOutStock = Objects.nonNull(queryId) && (queryId - SystemTradeQueryParamsContext.QUERY_WAIT_OUTSTOCK == 0 || SystemTradeQueryParamsContext.QUERY_FINISH_OUTSTOCK == 0);
            exportFieldEnums = getResultFieldEnums(needFields, isOutStock ? TradeExportFieldEnum.FIELD_TRADE_OUT_SCOPE : TradeExportFieldEnum.FIELD_ORDER_SCOPE);
        }else {
            exportFieldEnums = getResultFieldEnums(needFields, TradeExportFieldEnum.FIELD_TRADE_SCOPE);
        }
        TradeExportResourceSwitchData switchData = TradeExportResourceSwitchData.getInstance(exportFieldEnums);
        context.exportFieldEnums = exportFieldEnums;
        context.switchData = switchData;
        context.tradeConfig = getTradeConfig(staff,context);;
        context.queryId = queryId;
        context.exportRelation = exportRelation;
        context.exportMultiPackActualInfo = exportMultiPackActualInfo;
        context.allFlatformWhiteCompanyIdList = getAllFlatformWhiteCompanyIdList(staff, switchData);
        context.isNotEncrypt = getUserEncrypt(staff);
        context.needCopyTrade = needCopyTrade;
        if (switchData.waveUniqueCodeSwitch){
            context.openItemUniqueCodeOrOrderUniqueCode = uniqueCodeServiceDubbo.openItemUniqueCodeOrOrderUniqueCode(staff);
        }
        context.btasCodeExport = switchData.orderBtasCodeSwitch && TradeConfigUtils.openBtasQualityTesting(context.tradeConfig);
        return context;
    }

    public TradeExportContext getTradeExportContext(Staff staff, TradeExportParams exportParams, TradeExcelExportTask task, Integer exportType){
        TradeExportContext context = null;
        if (Objects.nonNull(task) && Objects.nonNull(task.getTradeExportContext()) && task.getTradeExportContext() instanceof TradeExportContext){
            context = (TradeExportContext)task.getTradeExportContext();
        }
        if (Objects.isNull(context)){
            context = initTradeExportContext(staff, exportParams, task, exportType);
        }
        return context;
    }

    private List<Trade> queryExportTradesTook(Staff staff, TradeExportParams exportParams, Long[] sids) {
        return LogKit.took(() -> queryExportTrades(staff, exportParams, sids), staff, 5000L, LogKit.getFullMethodName(this, "queryExportTrades"), logger);
    }


    private List<Trade> queryRelationTradeTook(Staff staff,  List<Trade> trades, TradeExportContext context){
        if (context.exportRelation){
            return LogKit.took(() -> queryRelationTrade(staff, trades), staff, 5000L, LogKit.getFullMethodName(this, "queryRelationTrade"), logger);
        }
        return trades;
    }

    private void fillFxSourceTradeTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.sensitiveAndDecryptSwitch){
            LogKit.took(() -> {
                fillFxSourceTrade(trades);
                return null;
            }, staff, 5000L, LogKit.getFullMethodName(this, "fillFxSourceTrade"), logger);
        }
    }

    private void sensitiveTradeTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.sensitiveAndDecryptSwitch){
            LogKit.took(() -> {
                //针对小大人童装，如果配置解密，则调用解密快手接口
                Boolean isSensitive = context.allFlatformWhiteCompanyIdList.get(CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU);
                sensitiveTrade(staff, trades, false, tradeLocalConfigurable.isSensitiveSystemTrades(staff.getCompanyId()), isSensitive);
                return null;
            }, staff, 5000L, LogKit.getFullMethodName(this, "sensitiveTrade"), logger);
        }
    }

    private Map<Long, Shop> getFxUserShopTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.fxShopSwitch){
            return LogKit.took(() -> getFxUserShopByCache(staff, trades), staff, 5000L, LogKit.getFullMethodName(this, "getFxUserShopByCache"), logger);
        }
        return Maps.newHashMap();
    }

    private Map<Long, Shop> getUserShopsTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.shopSwitch){
            return LogKit.took(() -> getUserShops(staff, trades), staff, 5000L, LogKit.getFullMethodName(this, "getUserShops"), logger);
        }
        return Maps.newHashMap();
    }

    private Map<Long, Warehouse> getUserWarehousesTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.warehousesSwitch){
            return LogKit.took(() -> getUserWarehouses(staff, trades), staff, 5000L, LogKit.getFullMethodName(this, "getUserWarehouses"), logger);
        }
        return Maps.newHashMap();
    }

    private Map<String, MultiPacksPrintTradeLog> getMultiPacksPrintTradeOutSidTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        return LogKit.took(() -> getMultiPacksPrintTradeOutSid(staff, trades, context), staff, 5000L, LogKit.getFullMethodName(this, "getMultiPacksPrintTradeOutSid"), logger);
    }

    private Map<Long, WaveRule> getWaveIdWaveRuleTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.wareRuleSwitch){
            return LogKit.took(() -> getWaveIdWaveRuleMap(staff, trades), staff, 5000L, LogKit.getFullMethodName(this, "getWaveIdWaveRuleMap"), logger);
        }
        return Maps.newHashMap();
    }

    private void fullTradePayTook(Staff staff, List<Trade> trades, TradeExportContext context){
        if (context.switchData.tradePaySwitch){
            LogKit.took(() -> {
                tradePayBusiness.fullTradePay(staff, trades);
                return null;
            }, staff, 5000L, LogKit.getFullMethodName(this, "fullTradePay"), logger);
        }
    }

    private void fillTradeInvoiceTook(Staff staff, List<Trade> trades, TradeExportContext context){
        if (context.switchData.tradeInvoiceSwitch){
            LogKit.took(() -> {
                tradeInvoiceFill.fill(staff, trades);
                return null;
            }, staff, 5000L, LogKit.getFullMethodName(this, "fillTradeInvoice"), logger);
        }
    }

    private Map<Long,WaveSorting> getPickNameTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.wareSortingSwitch){
            return LogKit.took(() -> getPickNameMap(staff, trades), staff, 5000L, LogKit.getFullMethodName(this, "getPickNameMap"), logger);
        }
        return Maps.newHashMap();
    }

    private void fillDestAndSourceNameTook(Staff staff, List<Trade> trades, TradeExportContext context){
        if (context.switchData.destAndSourceNameSwitch){
            LogKit.took(() -> {
                tradeQueryService.fillDestAndSourceName(trades);
                return null;
            }, staff, 5000L, LogKit.getFullMethodName(this, "fillDestAndSourceName"), logger);
        }
    }

    private Map<Long, List<PackmaItemDetailResponse>> getPackmaItemDetailTook(Staff staff, List<Trade> trades, TradeExportContext context){
        if (context.switchData.packmaItemDetailSwitch){
            return LogKit.took(() -> getPackmaItemDetail(staff, trades), staff, 5000L, LogKit.getFullMethodName(this, "getPackmaItemDetail"), logger);
        }
        return Maps.newHashMap();
    }

    private void fillGxTemplateNameTook(Staff staff, TradeExportContext context, List<Trade> trades){
        if (context.switchData.templateNameSwitch){
            LogKit.took(() -> {
                fillGxTemplateName(trades);
                return null;
            }, staff, 5000L, LogKit.getFullMethodName(this, "fillGxTemplateName"), logger);
        }
    }

    private Map<String, TbItem> queryTbItemInfoTook(Staff staff, List<Trade> trades, TradeExportContext context){
        if (context.switchData.tbItemInfoSwitch){
            return LogKit.took(() -> queryTbItemInfo(staff, trades), staff, 5000L, LogKit.getFullMethodName(this, "queryTbItemInfo"), logger);
        }
        return Maps.newHashMap();
    }

    private Map<String, Map<String, String>> getLogisticsTraceInfoTook(Staff staff, List<Trade> trades, TradeExportContext context, Map<String, MultiPacksPrintTradeLog> outSidMultiPacksMap){
        if (context.switchData.logisticsTraceSwitch){
            return LogKit.took(() -> getLogisticsTraceInfo(staff, trades, context, outSidMultiPacksMap), staff, 5000L, LogKit.getFullMethodName(this, "getLogisticsTraceInfo"), logger);
        }
        return Maps.newHashMap();
    }

    private void getLogisticsRecordByRemoteServiceTook(Staff staff, List<Trade> trades, Map<String, Map<String, String>> result){
        LogKit.took(() -> {
            getLogisticsRecordByRemoteService(staff, trades, result);
            return null;
        }, staff, 5000L, LogKit.getFullMethodName(this, "getLogisticsRecordByRemoteService"), logger);
    }

    private void handleItemInfoTook(Staff staff, List<JSONObject> jsonData, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.itemInfoSwitch || context.switchData.itemInfoBySkuSwitch || context.switchData.itemOrSkyInfoByTypeSwitch  || context.switchData.skuInfoSwitch){
            LogKit.took(() -> {
                handleItemInfo(staff, jsonData, trades, context);
                return null;
            }, staff, 5000L, LogKit.getFullMethodName(this, "handleItemInfo"), logger);
        }
    }

    private void handleGsTook(Staff staff, List<Trade> trades, List<JSONObject> jsonData, TradeExportContext context) {
        if (context.switchData.orderGsSwitch){
            LogKit.took(() -> {
                handleGs(staff, trades, jsonData);
                return null;
            }, staff, 5000L, LogKit.getFullMethodName(this, "handleGs"), logger);
        }
    }

    private Map<Long,String> getPackCheckNameMapTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.packCheckNameSwitch){
            return LogKit.took(() -> getPackCheckNameMap(staff, trades), staff, 5000L, LogKit.getFullMethodName(this, "getPackCheckNameMap"), logger);
        }
        return Maps.newHashMap();
    }

    private Map<String, ItemSupplierBridge> handleItemInfo2TradeTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.itemSupplierSwitch){
            return LogKit.took(() -> handleItemInfo2Trade(staff, trades), staff, 5000L, LogKit.getFullMethodName(this, "handleItemInfo2Trade"), logger);
        }
        return Maps.newHashMap();
    }

    private void handleUniqueCodeTook(Staff staff, List<Trade> trades, List<JSONObject> jsonData, TradeExportContext context) {
        if (context.switchData.waveUniqueCodeSwitch){
            LogKit.took(() -> {
                handleUniqueCode(staff, trades, jsonData, context);
                return null;
            },staff,5000L, LogKit.getFullMethodName(this, "handleUniqueCode"), logger);
        }
    }

    private void fillPlatFormIdTook(Staff staff, List<Trade> trades, TradeExportContext context) {
        if (context.switchData.platFormIdSwitch){
            LogKit.took(() -> {
                fillPlatFormId(staff, trades);
                return null;
            }, staff, 5000L, LogKit.getFullMethodName(this, "fillPlatFormId"), logger);
        }
    }

}

package com.raycloud.dmj.business.split.config;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.split.support.SplitParams;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.trade.TradeFakeRule;
import com.raycloud.dmj.domain.trade.common.FilterResult;
import com.raycloud.dmj.domain.trade.common.TradeBusinessUtils;
import com.raycloud.dmj.domain.trade.split.SplitConfigRule;
import com.raycloud.dmj.domain.trade.split.TradeSplitConfigTypeEnum;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbOrder;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.services.utils.LogHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-07-06 17:01
 * @Description SplitConfigDeep
 */
@Service
public class SplitConfigDeep extends SplitConfigFilter {

    private static TradeCopier<Trade, Trade> tradeCopier = new TradeCopier<>();

    @Override
    public Integer getSplitType() {
        return TradeSplitConfigTypeEnum.DEEP.getType();
    }

    @Override
    public void doCalculate(Staff staff, SplitParams params, List<Trade> originTrades, SplitConfigRule rule) {
        int splitMaxNum = rule.getSplitMaxNum() == null ? 1 : rule.getSplitMaxNum();
        int splitMinNum = rule.getSplitMinNum() == null ? 0 : rule.getSplitMinNum();
        for (Trade originTrade : originTrades) {
            if (Trade.SYS_STATUS_WAIT_AUDIT.equals(originTrade.getSysStatus())) {
                doCalculate(staff, params, originTrade, splitMaxNum, splitMinNum, rule.getMinPayment());
            }
        }
    }

    @Override
    public boolean hasConfigDetail(Staff staff) {
        return false;
    }

    private void doCalculate(Staff staff, SplitParams params, Trade originTrade, int splitMaxNum, int splitMinNum, Double minPayment) {

        //未匹配，退款中的子订单
        List<Order> unallocatedAndRefundOrders = new ArrayList<>();
        List<Order> hitOrders = new ArrayList<>();
        List<Order> originOrders = TradeUtils.getOrders4Trade(originTrade);
        for (Order order : originOrders) {
            if (!TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) && !order.isVirtual() && !order.ifNonConsign()) {//虚拟商品不参与拆分
                if (order.getItemSysId() <= 0 || RefundUtils.isRefundOrder(order)) {
                    unallocatedAndRefundOrders.add(order);
                } else {
                    hitOrders.add(order);
                }
            }
        }

        List<Trade> splitTrades = getSplitTrades(originTrade, calculate(staff, hitOrders, splitMaxNum, splitMinNum, minPayment));
        if (splitTrades.size() > 0) {
            //未匹配+退款中的单子单独为一单
            if (unallocatedAndRefundOrders.size() > 0) {
                Trade splitTrade = tradeCopier.copy(originTrade, new TbTrade());
                TradeUtils.setOrders(splitTrade, unallocatedAndRefundOrders);
                splitTrades.add(splitTrade);
            }
            params.splitDataMap.put(originTrade, splitTrades);
        }
    }

    private List<Trade> getSplitTrades(Trade originTrade, List<Map<Long, Integer>> splitDatas) {
        List<Trade> splitTrades = new ArrayList<>();
        if (splitDatas != null && splitDatas.size() > 0) {
            for (Map<Long, Integer> splitData : splitDatas) {
                List<Order> splitOrders = new ArrayList<>();
                for (Map.Entry<Long, Integer> entry : splitData.entrySet()) {
                    Order splitOrder = new TbOrder();
                    splitOrder.setId(entry.getKey());
                    splitOrder.setNum(entry.getValue());
                    splitOrders.add(splitOrder);
                }
                Trade splitTrade = tradeCopier.copy(originTrade, new TbTrade());
                TradeUtils.setOrders(splitTrade, splitOrders);
                splitTrades.add(splitTrade);
            }
        }
        return splitTrades;
    }

    private List<Map<Long, Integer>> calculate(Staff staff, List<Order> hitOrders, int splitMaxNum, int splitMinNum, Double minPayment) {
        if (hitOrders == null || hitOrders.size() == 0) {
            return null;
        }
        Map<Long, Integer> hitOrderIdNumMap = new HashMap<>();
        Map<Long, Order> hitOrderMap = new HashMap<>();
        for (Order order : hitOrders) {
            hitOrderIdNumMap.put(order.getId(), order.getNum());
            hitOrderMap.put(order.getId(), order);
        }
        SortUtils.sort(hitOrders, "num", false);
        List<Map<Long, Integer>> splitDatas = new ArrayList<>();
        int lastSplitNum;
        while (true) {
            Map<Long, Integer> splitData = new HashMap<>();
            int sumSplitNum = 0;
            for (Order order : hitOrders) {
                Long orderId = order.getId();
                Integer num = hitOrderIdNumMap.get(orderId);
                if(num == null){
                    continue;
                }
                int diffMax = splitMaxNum - sumSplitNum;
                int splitNum = num <= diffMax ? num : diffMax;
                sumSplitNum += splitNum;
                splitData.put(orderId, splitNum);
                if (sumSplitNum >= splitMaxNum) {
                    break;
                }
            }

            for (Map.Entry<Long, Integer> entry : splitData.entrySet()) {
                int num = hitOrderIdNumMap.get(entry.getKey());
                int splitNum = entry.getValue();
                int leaveNum = num - splitNum;
                if (leaveNum == 0) {
                    hitOrderIdNumMap.remove(entry.getKey());
                    hitOrders.remove(hitOrderMap.get(entry.getKey()));
                } else {
                    hitOrderIdNumMap.put(entry.getKey(), leaveNum);
                }
            }

            if (splitData.size() > 0) {
                splitDatas.add(splitData);
            }
            if (hitOrderIdNumMap.size() == 0) {
                lastSplitNum = sumSplitNum;
                break;
            }
        }

        if (splitDatas.size() > 1 && lastSplitNum > 0 && lastSplitNum < splitMinNum) {
            //移除最后一个拆分
            Map<Long, Integer> removeSplitData = splitDatas.remove(splitDatas.size() - 1);
            //将移除的拆分数量添加到最后一个拆分
            handleSplitMinNum(splitDatas.get(splitDatas.size() - 1), removeSplitData);
        }

        if (splitDatas.size() <= 1) {
            return null;
        }
        //最后一个留给主单
        Map<Long, Integer> mainSplitData = splitDatas.remove(splitDatas.size() - 1);

        handleSplitMinPayment(staff, hitOrderMap, mainSplitData, splitDatas, minPayment);

        return splitDatas;
    }

    /**
     * http://doc.raycloud.com/pages/viewpage.action?pageId=30548690
     */
    private void handleSplitMinPayment(Staff staff, Map<Long, Order> hitOrderMap, Map<Long, Integer> mainSplitData, List<Map<Long, Integer>> splitDatas, Double minPayment) {
        if (minPayment != null && minPayment >= 0) {
            List<Map<Long, Integer>> removeSplitDatas = new ArrayList<>();
            double mainPayment = getSplitPayment(hitOrderMap, mainSplitData);
            for (Map<Long, Integer> splitData : splitDatas) {
                double splitPayment = getSplitPayment(hitOrderMap, splitData);
                if (splitPayment < minPayment) {
                    removeSplitDatas.add(splitData);
                    mainPayment += splitPayment;
                }
            }
            //将拆分商品金额小于指定金额的子订单合并到主单
            if (removeSplitDatas.size() > 0) {
                splitDatas.removeAll(removeSplitDatas);
            }
            while(splitDatas.size() > 0 && mainPayment < minPayment){
                Map<Long, Integer> splitData = splitDatas.remove(splitDatas.size() - 1);
                mainPayment += getSplitPayment(hitOrderMap, splitData);
            }
        }
    }

    private double getSplitPayment(Map<Long, Order> hitOrderMap, Map<Long, Integer> splitData) {
        double splitPayment = 0;
        for (Map.Entry<Long, Integer> entry : splitData.entrySet()) {
            Order order = hitOrderMap.get(entry.getKey());
            int num = order.getNum();
            int splitNum = entry.getValue();
            if (num - splitNum == 0) {
                splitPayment += NumberUtils.str2Double(order.getPayment());
            } else {
                splitPayment += NumberUtils.str2Double(PaymentUtils.calculateSplitPaymentByPercentage(order.getPayment(), num, splitNum));
            }
        }
        return splitPayment;
    }

    private void handleSplitMinNum(Map<Long, Integer> addSplitData, Map<Long, Integer> removeSplitData) {
        for (Map.Entry<Long, Integer> entry : removeSplitData.entrySet()) {
            addSplitData.merge(entry.getKey(), entry.getValue(), Integer::sum);
        }
    }
}

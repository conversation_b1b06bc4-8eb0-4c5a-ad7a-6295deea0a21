package com.raycloud.dmj.services.trade.item.single;

import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.item.TradeItemConfigUtils;
import com.raycloud.dmj.domain.trade.common.TradeBusinessUtils;
import com.raycloud.dmj.domain.trade.item.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.ILockService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/3/17
 * @description 订单商品操作，转单品
 */
@Service
public class TradeItem2Single {

    @Resource
    TradeItem2SingleQuery tradeItem2SingleQuery;

    @Resource
    ILockService lockService;

    @Resource
    TradeLockBusiness tradeLockBusiness;

    public void toSingle(Staff staff, TradeItemContext itemContext, Long[] sids) {
        doTosingle(staff, itemContext, tradeItem2SingleQuery.query(staff, itemContext, sids));
    }

    public void toSingle(Staff staff, TradeItemContext itemContext, TradeQueryParams params) {
        List<Trade> trades = tradeItem2SingleQuery.query(staff, itemContext, params);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        if (trades.size() < 1000) {
            doTosingle(staff, itemContext, trades);
            return;
        }
        List<List<Trade>> groupTrades = TradeBusinessUtils.groupTradeByWarehouseId(staff, trades, TradeItemConfigUtils.getTradeItemBatchSize(staff.getCompanyId()));
        for (List<Trade> groupTrade : groupTrades) {
            doTosingle(staff, itemContext, groupTrade);
        }
    }

    private void doTosingle(Staff staff, TradeItemContext itemContext, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        Long[] sids = TradeUtils.toSids(trades);
        itemContext.reset();
        itemContext.setLocked(true);
        lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> doTosingle(staff, itemContext, sids));
    }

    private Object doTosingle(Staff staff, TradeItemContext itemContext, Long[] sids) {
        //TODO
        return null;
    }
}

package com.raycloud.dmj.business.gift;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.business.common.SecretBusiness;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.gift.help.GiftMatchManualQueryBusiness;
import com.raycloud.dmj.business.modify.GxTradeItemModifyBusiness;
import com.raycloud.dmj.business.modify.TradeGiftUpdateBusiness;
import com.raycloud.dmj.business.payment.support.PaymentCalculateSupports;
import com.raycloud.dmj.business.trade.TradeFilterBusiness;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.OpVEnum;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.fx.Constants;
import com.raycloud.dmj.domain.gift.GiftPromotion;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trade.spider.SpiderResponse;
import com.raycloud.dmj.domain.trade.spider.SpiderUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.payment.util.BigDecimalWrapper;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeBuilderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStockUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.trades.utils.PaymentUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.ec.stock.ApplyOrderStockListener;
import com.raycloud.dmj.services.gift.IGiftPromotionMatchTradeLogService;
import com.raycloud.dmj.services.gift.IGiftPromotionService;
import com.raycloud.dmj.services.gift.adapter.IGiftRuleAdapter;
import com.raycloud.dmj.services.gift.extend.GiftPromotionExtendBusiness;
import com.raycloud.dmj.services.gift.utils.GiftRuleUtils;
import com.raycloud.dmj.services.trace.IItemTraceService;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trade.merge.ITradeMergeService;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.LogKit;
import com.raycloud.dmj.utils.ItemTraceMessageBuilder;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * creatd 18/4/23
 * 手工匹配赠品
 *
 * <AUTHOR>
 */
@Service
public class GiftMatchManualBusiness{

    @Resource(name = "tradeGiftMatchBusiness")
    private ITradeBusiness tradeGiftMatchBusiness;

    @Autowired
    private IItemTraceService itemTraceService;

    @Resource
    private IGiftPromotionMatchTradeLogService giftPromotionMatchTradeLogService;

    @Resource
    private GiftMatchManualQueryBusiness giftMatchManualQueryBusiness;

    @Resource
    private TradeGiftUpdateBusiness tradeGiftUpdateBusiness;

    @Resource
    protected IProgressService progressService;

    private static final int DEFAULT_BATCH_SIZE = 100;

    @Resource
    private TradeFilterBusiness tradeFilterBusiness;

    @Resource
    private ITradeMergeService tradeMergeService;

    @Resource
    private ISysTradeService sysTradeService;

    @Resource
    private IGiftPromotionService giftPromotionService;

    @Resource
    private GiftPromotionExtendBusiness giftPromotionExtendBusiness;

    @Resource
    private IGiftRuleAdapter giftRuleAdapter;

    @Resource
    private SecretBusiness secretBusiness;
    @Resource
    ILockService lockService;
    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    IOrderStockService orderStockService;
    @Resource
    IEventCenter eventCenter;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;
    @Resource
    IItemServiceDubbo itemServiceDubboImpl;
    @Resource
    GxTradeItemModifyBusiness gxTradeItemModifyBusiness;
    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    ITradeUpdateService tradeUpdateService;
    @Resource
    PaymentCalculateSupports paymentCalculateSupports;

    private final Logger logger = Logger.getLogger(GiftMatchManualBusiness.class);


    public void matchGift(Staff staff, Integer reMatch, TradeQueryParams params, ProgressData progressData) {
        List<Trade> trades = giftMatchManualQueryBusiness.doQueryTrades(staff, params);
        if(CollectionUtils.isEmpty(trades)) {
            logger.debug(LogHelper.buildLog(staff, "赠品匹配查询结果为空"));
            return;
        }
        int tradeSize = trades.size();
        progressData.setCountAll(tradeSize);
        progressData.setSucNum(0L);
        progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_GIFT_MATCH, progressData);
        if (tradeSize <= DEFAULT_BATCH_SIZE * 2) {
            doMatch(staff, trades, reMatch, progressData);
        } else {
            int loop = tradeSize / DEFAULT_BATCH_SIZE + ((tradeSize % DEFAULT_BATCH_SIZE == 0) ? 0 : 1);
            for (int i = 0; i < loop; i++) {
                int fromIndex = i * DEFAULT_BATCH_SIZE;
                int endIndex = Math.min((fromIndex + DEFAULT_BATCH_SIZE), tradeSize);
                final List<Trade> subTrades = trades.subList(fromIndex, endIndex);
                doMatch(staff, subTrades, reMatch, progressData);
            }
        }
    }

    private void doMatch(Staff staff, List<Trade> trades, Integer reMatch, ProgressData progressData) {
        try {
            Long[] sids =  TradeUtils.toSids(trades);
            List<Trade> updateTrades = lockService.locks(tradeLockBusiness.getERPLocks(staff, sids), () -> match(staff, reMatch, tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, sids), OpEnum.GIFT_MATCH, false, false));
            progressData.setCountCurrent(progressData.getCountCurrent() + trades.size());
            if (!CollectionUtils.isEmpty(updateTrades)) {
                progressData.setSucNum(progressData.getSucNum() + updateTrades.size());
            }
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_GIFT_MATCH, progressData);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, String.format("匹配赠品失败,sids=%s", TradeUtils.toSidList(trades))), e);
        }
    }


    /**
     * @param reMatch 0：直接匹配 1：重新计算
     */
    public List<Trade> match(final Staff staff, final Integer reMatch, final Long[] sidArray) {
        return lockService.locks(tradeLockBusiness.getERPLocks(staff, sidArray), () -> match(staff, reMatch, tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, sidArray), OpEnum.GIFT_MATCH, false, false));
    }


    /**
     * @param staff
     * @param reMatch
     * @param sidArray
     */
    public void autoReMatch(final Staff staff, final Integer reMatch, final Long[] sidArray) {
        match(staff, reMatch, tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, sidArray), OpEnum.GIFT_MATCH, false, false);
    }

    /**
     *
     * 需要在外层加锁
     *
     * @param reMatch 0：直接匹配 1：重新计算
     * @param isAsyncStock 是否异步申请库存
     */
    public List<Trade> match(Staff staff, Integer reMatch, List<Trade> originTrades, OpEnum op, boolean isAsyn, boolean isAsyncStock) {
        originTrades = tradeFilterBusiness.filterTrades(originTrades, false);
        originTrades = tradeFilterBusiness.filterSumaitongQuanTuoGuanTrades(originTrades, false);
        originTrades = tradeFilterBusiness.filterFxgDfTrades(originTrades, false);
        //阿里国际站不支持手工匹配赠品
        originTrades = tradeFilterBusiness.filterAlibabaIcbuTrades(originTrades, false);
        originTrades = tradeFilterBusiness.filterTbDfTrades(originTrades, false);
        originTrades = tradeFilterBusiness.filterKSDfTrades(originTrades, false);
        originTrades = tradeFilterBusiness.filterJoomTrades(originTrades, false);
        originTrades = tradeFilterBusiness.filterOzonTrades(originTrades, false);
        //初始化数据
        GiftMatchManualData giftMatchManualData = new GiftMatchManualData(staff, originTrades);

        //如果是重新计算,先去掉之前匹配的赠品
        if (reMatch == 1) {
            giftMatchManualData.removeGiftBefore(staff);
            giftPromotionMatchTradeLogService.batchDelete(staff, TradeUtils.toSidList(originTrades));
            //归还之前匹配的赠品库存
            orderStockService.resumeOrderStockLocal(staff, giftMatchManualData.removeOrders, null);
        }

        //合单处理
        Map<Long,String> originPayment = giftMatchManualData.handleMergeTrades(staff, isAsyn);
        if (giftMatchManualData.matchTrades.isEmpty()) {
            logger.warn(LogHelper.buildLogHead(staff).append(String.format("没有需要匹配赠品的订单[sids=%s]", TradeUtils.toSidList(originTrades))));
            return null;
        }

        secretBusiness.decodeTrades(staff, giftMatchManualData.matchTrades);

        // 改代码的童鞋注意了，isManual.set(true)一定要放在doMatch前面
        ItemTraceMessageBuilder.isManual.set(true);
        doMatch(staff, giftMatchManualData.matchTrades);

        //处理匹配的赠品
        giftMatchManualData.handleGift();

        //新增的赠品需要申请库存, 分销订单匹配的赠品不需要申请
        List<Order> applyOrders = giftMatchManualData.insertOrders.stream().filter(e -> !OrderUtils.isFxOrder(e)).collect(Collectors.toList());

        if (isAsyncStock) {
            eventCenter.fireEvent(this, new EventInfo(ApplyOrderStockListener.EVENT_NAME).setArgs(new Object[]{staff, applyOrders}), null);
        } else {
            orderStockService.applyOrderStockLocal(staff, applyOrders);
        }

        tradeGiftUpdateBusiness.handleGiftOrderSalePrice(staff,giftMatchManualData.matchTrades);


        handleTrace(staff, giftMatchManualData, op);
        //doUpdate改为在handleTrace之后，这样才能更新到tag
        List<Trade> updateTrades = LogKit.took(() -> doUpdate(staff, giftMatchManualData,originPayment), staff, logger);

        if (!OpEnum.PRESELEE_UNLOCK_AFTER_GIFT_MATCH.equals(op)) {
            tradeTraceBusiness.asyncTrace(staff, giftMatchManualData.updateTrades.values().stream().filter(t -> t.getOperations().containsKey(OpEnum.GIFT_MATCH)).collect(Collectors.toList()), OpEnum.GIFT_MATCH);
        }

        // 记录商品日志
        itemTraceService.batchRecord(staff,
                ItemTraceMessageBuilder.buildManualMatchGiftItemTraceMessageList(staff,
                        giftMatchManualData.removeOrders,
                        giftMatchManualData.insertOrders));
        // 匹配后续处理
        matchAfter(staff, giftMatchManualData);
        return updateTrades;

    }

    public SimulationGiftMatchData simulationMatch(Staff staff, List<Trade> originTrades, boolean isAsyn) {

        originTrades.forEach(trade -> trade.setGiftOrders(null));

        SpiderResponse response = new SpiderResponse();
        SpiderUtils.addResponse(staff, response);

        List<Trade> trades = originTrades.stream().filter(trade -> checkTrade(staff, trade)).collect(Collectors.toList());
        //初始化数据
        GiftMatchManualData giftMatchManualData = new GiftMatchManualData(staff, trades);
        //合单处理
        giftMatchManualData.handleMergeTrades(staff, isAsyn);
        if (giftMatchManualData.matchTrades.isEmpty()) {
            return null;
        }

        ItemTraceMessageBuilder.isManual.set(true);
        Set<Long> userIds = originTrades.stream().map(Trade::getUserId).collect(Collectors.toSet());
        List<GiftPromotion> giftPromotions = giftPromotionService.findGiftPromotion(staff, userIds);
        if (CollectionUtils.isEmpty(giftPromotions)) {
            return null;
        }
        giftPromotionExtendBusiness.fillExtend(staff, giftPromotions, null);

        //匹配赠品
        giftRuleAdapter.adapter(staff, giftMatchManualData.matchTrades, giftPromotions);

        giftMatchManualData.handleGift();
        SimulationGiftMatchData matchData = new SimulationGiftMatchData(giftMatchManualData.matchTrades);

        List<GiftOrder> giftOrders = TradeUtils.getGiftOrders(giftMatchManualData.matchTrades);
        if (!CollectionUtils.isEmpty(giftOrders)) {
            fillRuleItem(staff, giftOrders);
            matchData.setMatchMsg(buildGiftMessage(giftOrders));
        }

        response.result.forEach(msg -> matchData.getNoMatchMsg().add(JSONObject.parseObject(msg, Map.class)));
        response.result.clear();

        return matchData;
    }

    private List<String> buildGiftMessage(List<GiftOrder> giftOrders) {
        if (CollectionUtils.isEmpty(giftOrders)) {
            return null;
        }

        return giftOrders.stream().map(giftOrder -> giftOrder.getOuterId() + ", 数量(" +
                giftOrder.getNum() + "), " +
                getIsPickName(giftOrder.getIsPick()) + "[" +
                "赠品规则:" + giftOrder.getGiftPromotionName() + "-" + giftOrder.getGiftPromotionRuleName() +
                "]").collect(Collectors.toList());
    }

    public void fillRuleItem(Staff staff, List<GiftOrder> giftOrders) {
        if (CollectionUtils.isEmpty(giftOrders)) {
            return;
        }
        //赠品对应的sysItemId、sysSkuId
        List<Long> sysItemIds = new ArrayList<>(), sysSkuIds = new ArrayList<>();
        List<GiftOrder> allGiftOrder = new ArrayList<>();

        giftOrders.forEach(giftOrder -> {
            if (!sysItemIds.contains(giftOrder.getItemSysId())) {
                sysItemIds.add(giftOrder.getItemSysId());
            }
            if (giftOrder.getSkuSysId() != null && giftOrder.getSkuSysId() > 0 && !sysSkuIds.contains(giftOrder.getSkuSysId())) {
                sysSkuIds.add(giftOrder.getSkuSysId());
            }
            allGiftOrder.add(giftOrder);
        });

        if (CollectionUtils.isEmpty(sysItemIds)) {
            return;
        }
        Map<Long, List<GiftOrder>> giftOrderMap = allGiftOrder.stream().collect(Collectors.groupingBy(GiftOrder::getItemSysId));
        //查询商品信息
        List<DmjItem> dmjItems = itemServiceDubboImpl.queryItemDetail(staff, sysItemIds, sysSkuIds, true);
        dmjItems.forEach(dmjItem -> {
            List<GiftOrder> giftOrderList = giftOrderMap.get(dmjItem.getSysItemId());
            if (CollectionUtils.isEmpty(giftOrderList)) {
                return;
            }
            giftOrderList.forEach(giftOrder -> {
                if (giftOrder.getSkuSysId() == null || giftOrder.getSkuSysId() <= 0) {
                    giftOrder.setOuterId(dmjItem.getOuterId());
                    return;
                }
                dmjItem.getSkuERP().forEach(dmjSku -> {
                    if (!giftOrder.getSkuSysId().equals(dmjSku.getSysSkuId())) {
                        return;
                    }
                    giftOrder.setOuterId(dmjSku.getOuterId());
                });
            });
        });
    }

    private String getIsPickName(Integer isPick) {
        if (Objects.isNull(isPick)) {
            return "未知";
        }
        if (isPick == 0) {
            return "不拣选不验货";
        }
        if (isPick == 1) {
            return "拣选不验货";
        }
        if (isPick == 2) {
            return "拣选验货";
        }
        if (isPick == 3) {
            return "验货不拣选";
        }
        return "未知";
    }

    public boolean checkTrade(Staff staff, Trade trade) {
        return checkTrade(staff, trade, new ArrayList<>());
    }

    /**
     * 检查订单
     */
    public boolean checkTrade(Staff staff, Trade trade, List<String> errMessage) {

        if (Objects.isNull(trade)) {
            errMessage.add("未找到原订单");
            return false;
        }

        if (Objects.equals(CommonConstants.PLAT_FORM_TYPE_VIPJIT, trade.getSubSource()) || Objects.equals(CommonConstants.PLAT_FORM_TYPE_VIPJITX, trade.getSubSource())) {
            errMessage.add("系统订单号：" + trade.getSid() + "，模拟运行失败，失败原因：JIT订单不支持该操作，无法执行赠品规则");
            return false;
        }

        if (CommonConstants.PLAT_FORM_TYPE_FXG_DF.equals(trade.getSource())) {
            errMessage.add("系统订单号：" + trade.getSid() + "，模拟运行失败，失败原因：抖音厂商代发订单不支持该操作，无法执行赠品规则");
            return false;
        }

        if (CommonConstants.PLAT_FORM_TYPE_ALIBABA_ICBU.equals(trade.getSource())) {
            errMessage.add("系统订单号：" + trade.getSid() + "，模拟运行失败，失败原因：阿里巴巴国际站订单不支持该操作，无法执行赠品规则");
            return false;
        }

        if (TradeUtils.isTbDfTrade(trade)) {
            errMessage.add("系统订单号：" + trade.getSid() + "，模拟运行失败，失败原因：淘宝厂商代发订单不支持该操作，无法执行赠品规则");
            return false;
        }

        if (CommonConstants.PLAT_FORM_TYPE_KUAISHOU_DF.equals(trade.getSource())) {
            errMessage.add("系统订单号：" + trade.getSid() + "，模拟运行失败，失败原因：快手代发订单不支持该操作，无法执行赠品规则");
            return false;
        }

        User user = staff.getUserByUserId(trade.getUserId());
        if (TradeUtils.isUnAuditExcep(staff,trade)) {
            errMessage.add("系统订单号：" + trade.getSid() + "，模拟运行失败，失败原因：分销商反审核异常不能进行次操作，无法执行赠品规则");
            return false;
        }
        if (!Constants.FxDefaultUserId.equals(trade.getUserId())) {
            if (null == user) {
                errMessage.add("系统订单号：" + trade.getSid() + "，模拟运行失败，失败原因：没有订单所属店铺的权限，无法执行赠品规则");
                return false;
            }
            if (user.getActive() != 1) {
                errMessage.add("系统订单号：" + trade.getSid() + "，模拟运行失败，失败原因：订单所属店铺已停用，无法执行赠品规则");
                return false;
            }
        }
        if (trade.getIsCancel() == 1) {
            errMessage.add("系统订单号：" + trade.getSid() + "，模拟运行失败，失败原因：订单已作废，无法执行赠品规则");
            return false;
        }
        if (!Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus())) {
            errMessage.add("系统订单号：" + trade.getSid() + "，模拟运行失败，失败原因：订单非待审核状态，无法执行赠品规则");
            return false;
        }

        return true;
    }



    private void matchAfter(Staff staff, GiftMatchManualData giftMatchManualData) {
        // 取消合并
        if (MapUtils.isNotEmpty(giftMatchManualData.mergeUndoTrades)) {
            tradeMergeService.mergeUndo(staff, new ArrayList<>(giftMatchManualData.mergeUndoTrades.keySet()));
        }
        // 作废
        if (MapUtils.isNotEmpty(giftMatchManualData.cancelTrades)) {
            sysTradeService.cancel(staff, giftMatchManualData.cancelTrades.keySet().toArray(new Long[0]));
        }
        // 奇门供销匹配赠品重算流水
        if (!CollectionUtils.isEmpty(giftMatchManualData.matchTrades)) {
            gxTradeItemModifyBusiness.updateTradeAfter(staff, giftMatchManualData.matchTrades);
        }
    }

    private void handleTrace(Staff staff, GiftMatchManualData data, OpEnum op) {
        Map<Long, List<Order>> newGiftOrders = new HashMap<>(), oldGiftOrders = new HashMap<>();
        data.insertOrders.forEach(order -> newGiftOrders.computeIfAbsent(order.getSid(), sid -> new ArrayList<>()).add(order));
        data.removeOrders.forEach(order -> oldGiftOrders.computeIfAbsent(order.getSid(), sid -> new ArrayList<>()).add(order));
        List<Trade> needLabelTrades = new ArrayList<>();
        data.updateTrades.forEach((sid, trade) -> {
            List<Order> newOrders = newGiftOrders.get(sid), oldOrders = oldGiftOrders.get(sid);
            if ((newOrders != null && !newOrders.isEmpty()) || (oldOrders != null && !oldOrders.isEmpty())) {
                StringBuilder s = new StringBuilder();
                if (OpEnum.MERGE_AFTER_GIFT_MATCH == op) {
                    s.append("合单后重新计算赠品,");
                } else if (OpEnum.ADDRESS_CHANGE_AFTER_MATCH_GIFT == op) {
                    s.append("修改地址后重新匹配赠品,");
                } else {
                    s.append("重新匹配赠品,");
                }
                if (oldOrders != null && !oldOrders.isEmpty()) {
                    s.append("删除原赠品: ").append(oldOrders.stream().map(this::buildLog).collect(Collectors.joining(";"))).append(";");
                }
                if (newOrders != null && !newOrders.isEmpty()) {
                    //在之前的操作里，这个值其实已经有了，这里再给他拿出来，重新按格式输出
                    String operation = trade.getOperations().get(OpEnum.GIFT_MATCH);
                    if (operation == null) {
                        logger.error(LogHelper.buildLogHead(staff).append("记录匹配赠品trace出错，trace为空"));
                    }
                    s.append("新增赠品: ").append(operation == null ? "无" : operation.replace("自动匹配赠品: ", ""));
                }
                trade.getOperations().put(OpEnum.GIFT_MATCH, s.toString());
                needLabelTrades.add(trade);
            }
        });
        tradeSysLabelBusiness.addTags(staff, needLabelTrades, OpEnum.GIFT_MATCH, Collections.singletonList(SystemTags.TAG_CHANGE_ITEM));
    }

    private String buildLog(Order order) {
        return getGiftLog(order);
    }

    private String getGiftLog(Order order){
        //0：不拣选不验货；1：拣选不验货；2：拣选验货；3：验货不拣选
        return "[" + order.getSysOuterId() + "]*" +
                order.getNum() + "," +
                GiftRuleUtils.getIsPickChinese(order.getIsPick());
    }

    private void doMatch(Staff staff, List<Trade> trades) {
        //按店铺分组
        Map<Long, List<Trade>> user_trades = TradeUtils.toMapByUserId(trades);
        //匹配赠品
        for (Map.Entry<Long, List<Trade>> entry : user_trades.entrySet()) {
            User user = new User();
            user.setStaff(staff);
            user.setId(entry.getKey());
            tradeGiftMatchBusiness.handleInsert(user, entry.getValue(), null);
        }
    }

    private List<Trade> doUpdate(Staff staff, GiftMatchManualData data,Map<Long,String> originPayment) {
        List<Trade> updateTrades = new ArrayList<>();
        if (!data.updateTrades.isEmpty()) {
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            for (Map.Entry<Long, Trade> entry : data.updateTrades.entrySet()) {
                Trade trade = entry.getValue();
                TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
                //合单赠品匹配会将所有子订单的payment都累加到主单上 这里要先还原回来
                if (originPayment.containsKey(trade.getSid())) {
                    trade.setPayment(originPayment.get(trade.getSid()));
                }
                updateTrades.add(createUpdateTrade(staff,trade, tradeConfig));
            }
        }
        tradeUpdateService.updateTrades(staff, updateTrades, data.getRemoveOrders(), null, data.insertOrders, false, false);
        return updateTrades;
    }

    private Trade createUpdateTrade(Staff staff,Trade trade, TradeConfig tradeConfig) {
        Trade updateTrade = TradeBuilderUtils.builderUpdateTrade(trade);
        updateTrade.setSid(trade.getSid());
        TradeExceptUtils.setStockStatus(staff,updateTrade,trade.getStockStatus());
        TradeUtils.setTradeExcep(staff,trade, updateTrade);
        TradeUtils.resetTradeItemNum(trade, tradeConfig);
        updateTrade.setItemKindNum(trade.getItemKindNum());
        updateTrade.setItemNum(trade.getItemNum());
        updateTrade.setNetWeight(trade.getNetWeight());
        updateTrade.setCost(trade.getCost());
        updateTrade.setSaleFee(trade.getSaleFee());
        updateTrade.setVolume(trade.getVolume());
        updateTrade.setTagIds(trade.getTagIds());
        updateTrade.setTagName(trade.getTagName());
        if (!(TradeUtils.isQimenFxSource(trade) || TradeUtils.isGxOrMixTrade(trade))) {
            if (paymentCalculateSupports.isTradePaymentLink(staff, trade.getSource())) {
                PaymentUtils.calculateTrade(trade);
                updateTrade.setPayment(trade.getPayment());
            } else {
                PaymentUtils.calculateTradeDiscountFee(trade, TradeUtils.getOrders4Trade(trade));
                updateTrade.setDiscountFee(trade.getDiscountFee());
            }
        } else if (TradeUtils.isQimenFxSource(trade)
                && trade.hasOpV(OpVEnum.ORDER_MATCH_FX_PRICE)) {
            //奇门赠品匹配后需要重新计算分销价,重算流水
            updateTrade.setPayment(MathUtils.toScaleString(staff.getCompanyId(), PaymentUtils.calculateQimenFxAmount(trade, false, false)));
            updateTrade.setPayAmount(updateTrade.getPayment());
        }
        if (trade.getOperations() != null && !trade.getOperations().isEmpty()) {
            updateTrade.setOperations(trade.getOperations());
        }
        return updateTrade;
    }

    private boolean validate(Staff staff, Trade trade) {
        User user = staff.getUserByUserId(trade.getUserId());
        if (TradeUtils.isUnAuditExcep(staff,trade)) {
            logger.error(LogHelper.buildLogHead(staff).append(String.format("分销商反审核异常不能进行次操作[系统订单号=%s]", trade.getSid())));
            return false;
        }
        if (!staff.isDefaultStaff()) {
            if (null == user) {
                logger.error(LogHelper.buildLogHead(staff).append(String.format("没有订单所属店铺的权限[系统订单号=%s]", trade.getSid())));
                return false;
            }
            if (user.getActive() != 1) {
                logger.error(LogHelper.buildLogHead(staff).append(String.format("订单所属店铺已停用[系统订单号=%s]", trade.getSid())));
                return false;
            }
        }
        if (trade.getIsCancel() == 1) {
            logger.error(LogHelper.buildLogHead(staff).append(String.format("订单已作废[系统订单号=%s]", trade.getSid())));
            return false;
        }
        return Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus());
    }

    private class GiftMatchManualData {
        /**
         * 合单订单
         */
        private final Map<Long, List<Trade>> mergeTrades = new HashMap<>();

        /**
         * 需要进行赠品匹配的订单
         */
        private final List<Trade> matchTrades = new ArrayList<>();

        /**
         * 在重新计算的时候需要删除之前匹配的赠品
         */
        private final List<Order> removeOrders = new ArrayList<>();

        /**
         * 新增的赠品信息
         */
        private final List<Order> insertOrders = new ArrayList<>();

        /**
         * 需要更新的订单（包括删除赠品和新增赠品）
         */
        private final Map<Long, Trade> updateTrades = new HashMap<>();

        /**
         * 需要作废的订单
         */
        private final Map<Long, Trade> cancelTrades = new HashMap<>();

        /**
         * 需要取消合单的订单
         */
        private final Map<Long, Trade> mergeUndoTrades = new HashMap<>();

        private GiftMatchManualData(Staff staff, List<Trade> originTrades) {
            for (Trade trade : originTrades) {
                if (TradeUtils.isGxTrade(trade) || validate(staff, trade)) {
                    addTrade(staff, trade);
                }
            }
        }

        private void addTrade(Staff staff, Trade trade) {
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                addMergeTrade(trade);
            } else {
                addNormalTrade(trade);
            }
        }

        private void addMergeTrade(Trade trade) {
            mergeTrades.computeIfAbsent(trade.getMergeSid(), k -> new ArrayList<>()).add(trade);
        }

        private void addNormalTrade(Trade trade) {
            matchTrades.add(trade);
        }

        private void removeGiftBefore(Staff staff) {
            removeGiftBefore(staff, matchTrades);
            if (!mergeTrades.isEmpty()) {
                for (Map.Entry<Long, List<Trade>> entry : mergeTrades.entrySet()) {
                    removeGiftBefore(staff, entry.getValue());
                }
            }
        }

        private void removeGiftBefore(Staff staff, List<Trade> trades) {
            if (!trades.isEmpty()) {
                for (Trade trade : trades) {
                    removeGiftBefore(staff, trade);
                }
            }
        }

        private void removeGiftBefore(Staff staff, Trade trade) {
            updateTrades.put(trade.getSid(), trade);

            List<Order> needRemoveOrders = new ArrayList<>();
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                if (order.getGiftNum() > 0 && order.getCustomGiftType() != 1L && order.getCustomGiftType() != 2L && CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) { //平台赠品不去除
                    needRemoveOrders.add(order);
                }
            }
            if (needRemoveOrders.isEmpty()) {
                return;
            }
            orders.removeAll(needRemoveOrders);
            removeOrders.addAll(needRemoveOrders);
            TradeUtils.setOrders(trade, orders);
            // 订单只有赠品 赠品被全部删除了 订单没有order了 需要作废订单 如果是合单 需要先取消合单
            if (CollectionUtils.isEmpty(orders)) {
                cancelTrades.put(trade.getSid(), trade);
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                    mergeUndoTrades.put(trade.getMergeSid(), trade);
                }
            }
        }


        private Map<Long,String> handleMergeTrades(Staff staff, boolean isAsyn) {
            Map<Long,String> originPayment = new HashMap<>();
            if (!mergeTrades.isEmpty()) {
                for (Map.Entry<Long, List<Trade>> entry : mergeTrades.entrySet()) {
                    if(isAsyn){
                        originPayment.putAll(handleMergeTradesAysn(entry.getValue()));
                    } else{
                        originPayment.putAll(handleMergeTrades(staff, entry.getKey(), entry.getValue()));
                    }
                }
            }
            return originPayment;
        }

        private Map<Long,String> handleMergeTrades(Staff staff, long mergeSid, List<Trade> trades) {
            //合单总金额
            BigDecimalWrapper payment = new BigDecimalWrapper();
            //合单主单
            Trade mainTrade = null;

            StringBuilder sellerMemo = new StringBuilder();

            StringBuilder buyerMessage = new StringBuilder();
            //合单所有子订单
            List<Order> orders = new ArrayList<>();
            Map<Long,String> originPayment = new HashMap<>();
            for (Trade trade : trades) {
                payment.add(trade.getPayment());
                orders.addAll(TradeUtils.getOrders4Trade(trade));
                if (StringUtils.isNotEmpty(trade.getSellerMemo())) {
                    sellerMemo.append(trade.getSellerMemo()).append(",");
                }
                if (StringUtils.isNotEmpty(trade.getBuyerMessage())) {
                    buyerMessage.append(trade.getBuyerMessage()).append(",");
                }
                if (mainTrade == null && mergeSid == trade.getSid() && trade.getEnableStatus() == 1) {
                    mainTrade = trade;
                }
            }
            if (mainTrade == null) {
                logger.warn(LogHelper.buildLogHead(staff).append(String.format("重新匹配赠品，合单订单找不到主单[mergeSid=%s]", mergeSid)));
                return originPayment;
            }
            originPayment.put(mainTrade.getSid(),mainTrade.getPayment());
            mainTrade.setPayment(payment.getString());
            if (sellerMemo.length() > 0) {
                mainTrade.setSellerMemo(sellerMemo.substring(0, sellerMemo.length() - 1));
            }

            if (buyerMessage.length() > 0) {
                mainTrade.setBuyerMessage(buyerMessage.substring(0, buyerMessage.length() - 1));
            }
            TradeUtils.setOrders(mainTrade, orders);
            matchTrades.add(mainTrade);
            return originPayment;
        }

        private Map<Long,String> handleMergeTradesAysn(List<Trade> trades) {
            //合单总金额
            BigDecimalWrapper payment = new BigDecimalWrapper();
            StringBuilder sellerMemo = new StringBuilder();
            StringBuilder buyerMessage = new StringBuilder();
            Trade trade = trades.get(0);
            Map<Long,String> originPayment = new HashMap<>();
            List<MessageMemo> memos = trade.getMessageMemos();
            if (!CollectionUtils.isEmpty(memos)) {
                for (MessageMemo messageMemo : memos) {
                    payment.add(messageMemo.getPayment());
                    if (StringUtils.isNotEmpty(messageMemo.getSellerMemo())) {
                        sellerMemo.append(messageMemo.getSellerMemo()).append(",");
                    }
                    if (StringUtils.isNotEmpty(messageMemo.getBuyerMessage())) {
                        buyerMessage.append(messageMemo.getBuyerMessage()).append(",");
                    }
                }
                originPayment.put(trade.getSid(),trade.getPayment());
                trade.setPayment(payment.toString());
                if (sellerMemo.length() > 0) {
                    trade.setSellerMemo(sellerMemo.substring(0, sellerMemo.length() - 1));
                }

                if (buyerMessage.length() > 0) {
                    trade.setBuyerMessage(buyerMessage.substring(0, buyerMessage.length() - 1));
                }
            }
            matchTrades.add(trade);
            return originPayment;
        }

        private void handleGift() {
            for (Trade trade : matchTrades) {
                if (trade.getGiftOrders() == null || trade.getGiftOrders().isEmpty()) {
                    continue;
                }

                updateTrades.put(trade.getSid(), trade);

                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                for (Order order : orders) {
                    //新增的赠品
                    if (order.getGiftNum() != null && order.getGiftNum() > 0 && order.getFlag() == 1 && (Trade.STOCK_STATUS_EMPTY.equals(order.getStockStatus()) || Trade.STOCK_STATUS_EXCEP.equals(order.getStockStatus()))) {
                        if (trade.getMergeSid() != null && trade.getMergeSid() > 0) {
                            order.setBelongSid(trade.getMergeSid());
                        } else {
                            order.setBelongSid(trade.getSid());
                        }
                        insertOrders.add(order);
                    }
                }
            }
        }

        private List<Order> getRemoveOrders() {
            List<Order> updateOrders = new ArrayList<>();
            for (Order removeOrder : removeOrders) {
                updateOrders.add(createRemoveOrder(removeOrder));
                List<Order> removeSuits = removeOrder.getSuits();
                if (removeSuits != null && !removeSuits.isEmpty()) {
                    for (Order removeSuit : removeSuits) {
                        updateOrders.add(createRemoveOrder(removeSuit));
                    }
                }
            }
            return updateOrders;
        }

        private Order createRemoveOrder(Order order) {
            Order updateOrder = new TbOrder();
            updateOrder.setId(order.getId());
            updateOrder.setEnableStatus(0);
            return updateOrder;
        }
    }
}

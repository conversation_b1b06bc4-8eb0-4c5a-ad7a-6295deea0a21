package com.raycloud.dmj.services.trade.item.formula;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.services.trade.item.common.*;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description
 */
@Service
public class TradeItemFormulaFill extends TradeItemFill {

    @Override
    public void fill(Staff staff, TradeItemContext itemContext, List<Trade> trades) {

    }
}

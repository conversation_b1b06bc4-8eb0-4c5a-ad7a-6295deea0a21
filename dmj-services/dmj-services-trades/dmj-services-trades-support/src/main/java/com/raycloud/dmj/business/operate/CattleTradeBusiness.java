package com.raycloud.dmj.business.operate;

import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.modify.ModifyParentBusiness;
import com.raycloud.dmj.business.modify.TradeAddBusiness;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.business.warehouse.WarehouseAllocateBusiness;
import com.raycloud.dmj.domain.ERPLock;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.item.search.api.DmjItemCommonSearchApi;
import com.raycloud.dmj.item.search.dto.DmjItemDto;
import com.raycloud.dmj.item.search.request.DmjItemSearchField;
import com.raycloud.dmj.item.search.request.QueryMiniItemByOuterIdListRequest;
import com.raycloud.dmj.item.search.request.StaffRequest;
import com.raycloud.dmj.item.search.response.QueryMiniItemByOuterIdListResponse;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.pt.IUserExpressTemplateService;
import com.raycloud.dmj.services.trades.ITradeUpdateService;
import com.raycloud.dmj.services.trades.TradeException;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.template.base.SearchTemplateParams;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 *  黄牛订单业务封装
 */
@Service
public class CattleTradeBusiness  {

    private final Logger logger = Logger.getLogger(this.getClass());
    @Resource
    private ITradeUpdateService tradeUpdateService;
    @Resource
    private WarehouseAllocateBusiness warehouseAllocateBusiness;
    @Resource
    private DmjItemCommonSearchApi dmjItemCommonSearchApi;
    @Resource
    private IUserService userService;
    @Resource
    private TradeAddBusiness tradeAddBusiness;
    @Resource
    private IOrderStockService orderStockService;
    @Resource
    private IUserExpressTemplateService userExpressTemplateService;
    @Resource
    private TradeTraceBusiness tradeTraceBusiness;
    @Resource
    private ILockService lockService;
    @Resource
    ModifyParentBusiness modifyParentBusiness;
    @Transactional
    public List<Trade> saveCattle(Staff staff, List<TbTrade> trades) {
        List<Trade> insertTrades;
        try {
            Set<Long> expressIdSet = new HashSet<>();
            Set<String> tidSet = new HashSet<>();
            Assert.isTrue(!CollectionUtils.isEmpty(trades)," 传参校验 trades 为空");
            for(Trade temp: trades){
                Assert.isTrue(temp!=null," 传参校验 trades 包含空对象");
                Assert.isTrue(!StringUtils.isEmpty(temp.getTid()), String.format("传参校验 tid[%s] 为空 ",temp.getTid()));
                Assert.isTrue(!tidSet.contains(temp.getTid()),String.format("传参校验 tid[%s] 重复 ",temp.getTid()));
                tidSet.add(temp.getTid());
                Assert.isTrue(!StringUtils.isEmpty(temp.getOutSid()), String.format("传参校验 outSid[%s] 为空 ",temp.getTid()));
                Assert.isTrue(temp.getSourceId()>0, String.format("传参校验 sourceId[%s] 为空 ",temp.getSourceId()));
                Assert.isTrue(null!= temp.getExpressCompanyId() && temp.getExpressCompanyId() > 0, String.format("传参校验 expressCompanyId[%s] 为空 ",temp.getExpressCompanyId()));
                Assert.isTrue(!StringUtils.isEmpty(temp.getExpressCode()), String.format("传参校验 expressCode[%s] 为空 ",temp.getExpressCode()));
                expressIdSet.add(temp.getExpressCompanyId());
            }

            // fill warehouse，商品，店铺
            Warehouse defaultWarehouse = warehouseAllocateBusiness.getDefaultWarehouse(staff);
            Assert.isTrue(defaultWarehouse!=null && defaultWarehouse.getId()!=null && !StringUtils.isEmpty(defaultWarehouse.getName()), "未获取到默认仓库");
            QueryMiniItemByOuterIdListRequest request = buildQueryMiniItemByOuterIdListRequest(staff);

            QueryMiniItemByOuterIdListResponse response = dmjItemCommonSearchApi.queryMiniItemByOuterIdList(request);
            Assert.isTrue(response != null&& CollectionUtils.isNotEmpty(response.getList()) , "请先创建 主商家编码为[穿透单号]的 item");
            DmjItemDto dmjItem = response.getList().get(0);

            User user = userService.queryByNick("穿透单号业务专用", staff.getCompanyId());
            Assert.isTrue(user != null && user.getId() != 0, "请先创建 店铺名称为[穿透单号业务专用]的店铺");

            SearchTemplateParams params = new SearchTemplateParams();
            params.setExpressIds(new ArrayList<>(expressIdSet));
            List<UserExpressTemplate> userExpressTemplates = userExpressTemplateService.querySomeFieldsByParams(staff, params);
            Map<Long,UserExpressTemplate> templateMap =  new HashMap<>();
            for(UserExpressTemplate template:userExpressTemplates){
                if(null!= template && null != template.getId()){
                    templateMap.putIfAbsent(template.getExpressId(),template);
                }
            }
            insertTrades = initTradeInfoAndOrderInfo(trades, defaultWarehouse, dmjItem, user,templateMap);
            List<Order> insertOrders= new ArrayList<>();
            List<ERPLock> locks = new ArrayList<>();
            for(String tid:tidSet){
                locks.add(TradeLockBusiness.tid2ERPLock(staff, tid));
            }
            lockService.locks(locks, () -> {
                for (Trade trade:insertTrades){
                    //校验订单信息
                    tradeAddBusiness.validate(staff, trade);
                    trade.setLogisticsCompanyId(trade.getExpressCompanyId());
                    //初始化订单信息,包裹订单应付金额,优惠金额的计算等
                    List<Order> orders = tradeAddBusiness.initTrade(staff, trade);
                    // 初始化成已审核
                    trade.setSysStatus(Trade.SYS_STATUS_FINISHED_AUDIT);
                    insertOrders.addAll(orders);
                    // 计算trade信息
                    modifyParentBusiness.calculate(staff, trade);
                }
                orderStockService.applyTradeStockLocal(staff, insertTrades);
                tradeUpdateService.insertTrades(staff,insertTrades,insertOrders);
                return null;
            });

            for(Trade trade: insertTrades){
                String content = "通过验货登记扫描穿透单号创建订单";
                trade.getOperations().put(OpEnum.TRADE_ADD, content);
            }
            tradeTraceBusiness.asyncTrace(staff, insertTrades, OpEnum.TRADE_ADD);
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(staff, e, "新增系统穿透订单出错"), e);
            throw new TradeException("新增系统穿透订单出错 " + e.getMessage(), e);
        }

        return insertTrades;
    }

    private QueryMiniItemByOuterIdListRequest buildQueryMiniItemByOuterIdListRequest(Staff staff) {
        List<String> outerIdList = new ArrayList<>();
        outerIdList.add("穿透单号");
        QueryMiniItemByOuterIdListRequest request = new QueryMiniItemByOuterIdListRequest();
        request.setStaffRequest(StaffRequest.builder().staffId(staff.getId()).companyId(staff.getCompanyId()).build());
        request.setOuterIdList(outerIdList);
        DmjItemSearchField dmjItemSearchField = DmjItemSearchField.DmjItemSearchFieldBuilder.builder().build();
        request.setDmjItemSearchField(dmjItemSearchField);
        return request;
    }

    private List<Trade> initTradeInfoAndOrderInfo(List<TbTrade> trades, Warehouse defaultWarehouse, DmjItemDto dmjItem, User user,Map<Long,UserExpressTemplate> templateMap) {
        for(Trade temp: trades){
            temp.setUserId(user.getId());
            temp.setWarehouseId(defaultWarehouse.getId());
            temp.setWarehouseName(defaultWarehouse.getName());
            UserExpressTemplate template = templateMap.get(temp.getExpressCompanyId());
            if (null !=template && null!= template.getId()){
                temp.setTemplateId(template.getId());
            }
            temp.setIsUrgent(0);
            temp.setPayment("1");
            temp.setDiscountFee("-1.00");
            temp.setTaxFee("0");
            temp.setSaleFee("0");
            temp.setPostFee("0");

            temp.setForce(true);
            temp.setReceiverName("商家");
            temp.setReceiverMobile("12345678910");
            temp.setReceiverPhone("12345678910");
            temp.setReceiverZip("");
            temp.setBuyerNick("商家");
            temp.setReceiverState("北京");
            temp.setReceiverCity("北京市");
            temp.setReceiverDistrict("其他区");
            temp.setReceiverAddress("商家代发");
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(temp);
            for(Order o:orders4Trade){
                // 只取 o.getNum();
                o.setNum(o.getNum());
                o.setItemSysId(dmjItem.getSysItemId());
                o.setSkuSysId(0L);
                o.setSkuPropertiesName("");
                o.setPicPath(dmjItem.getPicPath());
                o.setTitle(dmjItem.getTitle());
                o.setOuterId(dmjItem.getOuterId());
                o.setOuterSkuId("");
                o.setPrice("0");
                o.setPayment("0");
                o.setTotalFee("0");
                o.setDiscountRate(0d);
                o.setSalePrice("0");
                o.setSaleFee("0");
                o.setDiscountFee("0");
            }
        }
        return new ArrayList<>(trades);
    }
}

package com.raycloud.dmj.services.trade.item.entity;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.diamond.item.TradeItemConfigUtils;
import com.raycloud.dmj.domain.trade.common.TradeBusinessUtils;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23
 * @description 实体编码转换
 */
@Service
public class TradeEntityCodeConvert {

    @Resource
    TradeEntityCodeConvertQuery tradeEntityCodeConvertQuery;

    public void convert(Staff staff, TradeItemContext itemContext, TradeQueryParams params) {
        List<Trade> trades = tradeEntityCodeConvertQuery.query(staff, itemContext, params);
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        if (trades.size() < 1000) {
            doConvert(staff, itemContext, trades);
            return;
        }
        List<List<Trade>> groupTrades = TradeBusinessUtils.groupTradeByWarehouseId(staff, trades, TradeItemConfigUtils.getTradeItemBatchSize(staff.getCompanyId()));
        for (List<Trade> groupTrade : groupTrades) {
            doConvert(staff, itemContext, groupTrade);
        }
    }

    private void doConvert(Staff staff, TradeItemContext itemContext, List<Trade> trades) {

    }
}

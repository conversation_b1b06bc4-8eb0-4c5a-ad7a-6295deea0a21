package com.raycloud.dmj.dao.trade;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.Query;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.ConsignRecord;
import com.raycloud.dmj.domain.trades.ConsignRecordQueryParams;
import com.raycloud.dmj.domain.trades.ConsignRecordStatistics;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.diamond.TradeConsignConfigDiamondUtils;
import com.raycloud.dmj.services.trades.TradeQueryBuilder;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.erp.db.router.ibatis.SqlMapClientBaseDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * 发货记录，将发货中和发货失败的保存进来，发货成功后，将标志enableStatus为0
 *
 * <AUTHOR>
 */
@Repository
@SuppressWarnings("deprecation")
public class ConsignRecordDao extends SqlMapClientBaseDao {

    private Logger logger = Logger.getLogger(this.getClass());

    public final static String QUERY_CLOSED = "queryClosed";

    @Resource
    private TradeQueryBuilder tradeSqlQueryBuilder;

    public ConsignRecordDao() {
        super.domain = "ConsignRecord";
    }

    private void init(Staff staff, ConsignRecord obj) {
        obj.setCompanyId(staff.getCompanyId());
        obj.setDbNo(staff.getDbInfo().getTradeDbNo());
        obj.setDbKey(staff.getDbNo());
    }

    /**
     * 支持重复主键触发更新的操作
     */
    public void insert(Staff staff, ConsignRecord record) {
        init(staff, record);
        getSqlMapClientTemplate(staff).insert(buildStatementName("insert"), record);
    }

    public void batchInsert(Staff staff, List<ConsignRecord> records) {
        for (ConsignRecord record : records) {
            init(staff, record);
        }
        batchInsert(buildStatementName("insert"), records);
    }

    public int batchUpdate(Staff staff, List<ConsignRecord> records) {
        if (records == null || records.size() == 0) {
            return 0;
        }
        for (ConsignRecord record : records) {
            init(staff, record);
        }
        return batchUpdate(buildStatementName("update"), records);
    }

    /**
     * 根据某个公司下的发货记录的is_error字段进行统计，返回的key包含,error_count和is_error两个字段
     */
    @SuppressWarnings("unchecked")
    public List<ConsignRecordStatistics> count(Staff staff) {
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("dbNo", staff.getDbInfo().getTradeDbNo());
        condition.put("companyId", staff.getCompanyId());
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("count"), condition);
    }

    /**
     * 根据sids查询发货记录
     */
    public List<ConsignRecord> listBySids(Staff staff, Long... sids) {
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("companyId", staff.getCompanyId());
        condition.put("sids", sids);
        condition.put("dbNo", staff.getDbInfo().getTradeDbNo());
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryBySids"), condition);
    }

    @SuppressWarnings("unchecked")
    public List<ConsignRecord> list(Staff staff, ConsignRecordQueryParams params) {
        if (Objects.isNull(staff) || Objects.isNull(staff.getDbInfo()) || Objects.isNull(staff.getDbInfo().getTradeDbNo())) {
            // 上游staff异常情况 含泪加了条日志
            String errorMsg = "";
            if (Objects.isNull(staff)) {
                errorMsg = "查询发货记录staff为空,上游查下哪里传错了";
            } else if (Objects.isNull(staff.getDbInfo())) {
                errorMsg = "查询发货记录staff异常,DbInfo为空,上游查下哪里传错了";
            } else if (Objects.isNull(staff.getDbInfo().getTradeDbNo())) {
                errorMsg = "查询发货记录staff异常,TradeDbNo为空,上游查下哪里传错了";
            }
            logger.error(LogHelper.buildLog(staff, String.format(errorMsg)));
            return null;
        }
        Query q = buildQuery(staff, params);
        StringBuilder sql = new StringBuilder("SELECT ");
        sql.append(StringUtils.isNotBlank(params.getFields()) ? params.getFields() : " c.* , t.unified_status, t.created , t.logistics_company_id").append(" FROM ");
        sql.append("consign_record_").append(staff.getDbInfo().getTradeDbNo()).append(" c")
                .append(" left join").append(" trade_")
                .append(staff.getDbInfo().getTradeDbNo()).append(" t")
                .append(" on c.sid = t.sid");
        sql.append(" WHERE ").append(q.getQ().toString());
        if (params.getSort() != null) {
            if (StringUtils.isNotBlank(params.getSort().getField())) {
                sql.append(" ORDER BY ").append(params.getSort().getField());
                if (StringUtils.isNotBlank(params.getSort().getOrder())) {
                    sql.append(" ").append(params.getSort().getOrder());
                }
            }
        }
        if (params.getPage() != null) {
            sql.append(" LIMIT ?, ?");
            q.add(params.getPage().getStartRow()).add(params.getPage().getPageSize());
        }
        Logs.debug("sql:" + sql);
        return getJdbcTemplate(staff).query(sql.toString(), q.getArgs().toArray(), new BeanPropertyRowMapper<>(ConsignRecord.class));
    }

    public Integer countOfList(Staff staff, ConsignRecordQueryParams params) {
        Query q = buildQuery(staff, params);
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM consign_record_");
        sql.append(staff.getDbInfo().getTradeDbNo()).append(" c")
                .append(" left join").append(" trade_")
                .append(staff.getDbInfo().getTradeDbNo()).append(" t")
                .append(" on c.sid = t.sid");
        sql.append(" WHERE ").append(q.getQ().toString());
        return getJdbcTemplate(staff).queryForObject(sql.toString(), q.getArgs().toArray(), Integer.class);
    }

    @SuppressWarnings("unchecked")
    public List<ConsignRecord> listImmediateUploadRecord(Staff staff, ConsignRecordQueryParams params) {
        Map<String, Object> condition = buildCondition(staff, params);
        condition.put("poolDbNo", staff.getDbInfo().getLogisticsTrackingPollPoolDbNo());
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("listImmediateUploadRecord"), condition);
    }

    public List<ConsignRecord> queryBySids(Staff staff, Integer enableStatus, Long... sids) {
        if (sids == null || sids.length == 0)
            throw new IllegalArgumentException("请输入正确的参数");
        Map<String, Object> condition = initParams(staff);
        condition.put("sids", sids);
        condition.put("enableStatus", enableStatus);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryBySids"), condition);
    }

    public List<ConsignRecord> queryBySids(Staff staff, Long... sids) {
        return queryBySids(staff, 1, sids);
    }

    Map<String, Object> buildCondition(Staff staff, ConsignRecordQueryParams params) {
        Map<String, Object> condition = initParams(staff);
        condition.put("obj", params);

        if (params.getPage() != null) {
            condition.put("page", params.getPage());
        }

        if (params.getSort() != null) {
            condition.put("sort", params.getSort());
        }

        return condition;
    }


    private Query buildQuery(Staff staff, ConsignRecordQueryParams params) {
        Query q = new Query();
        q.append("c.company_id = ?").add(staff.getCompanyId());
        buildIn(q, "c.sid", params.getSid(), true);
        buildIn(q, "c.sid", params.getSids(), true);
        buildIn(q, "c.tid", params.getTid(), false);
        buildIn(q, "c.out_sid", params.getOutSid(), false);
        buildIn(q, "c.user_id", params.getUserId(), true);
        buildIn(q, "c.warehouse_id", params.getWarehouseId(), true);
        buildIn(q, "c.error_type", params.getErrorType(), true);
        buildIn(q, "t.unified_status", params.getStatus(), false);  // KMERP-134401: 支持平台状态查询
        buildIn(q, "t.logistics_company_id", params.getLogisticsCompanyIds(), true);
        if (params.getIsError() != null) {
            if (params.getIsError() == -1) {//查询上传失败中未触发上传记录
                q.and().append("c.is_error = 1").and().append("LOCATE('未触发上传',c.error_desc)");
            } else {
                q.and().append("c.is_error = ?").add(params.getIsError());
            }
        }
        //add by baiyun-KMERP-36820 start
        if (StringUtils.isNotEmpty(params.getErrorDesc())) {
            //q.and().append("c.error_desc like '" + params.getErrorDesc() + "%'");
            q.and().append("c.error_desc like " + "concat('" + params.getErrorDesc() + "','%')");
        }
        //add by baiyun-KMERP-36820 end
        if (params.getIsCut() != null) {
            q.and().append("c.is_cut = ?").add(params.getIsCut());
        }
        if (params.getTemplateType() != null) {
            StringBuilder buf = new StringBuilder();
            if (CollectionUtils.isNotEmpty(params.getCommonTemplateIds())) {
                buf.append("(c.template_type = 0 AND c.template_id IN(");
                for (int i = 0; i < params.getCommonTemplateIds().size(); i++) {
                    buf.append(i > 0 ? ", ?" : "?");
                    q.add(params.getCommonTemplateIds().get(i));
                }
                buf.append("))");
            }
            if (CollectionUtils.isNotEmpty(params.getWlbTemplateIds())) {
                if (buf.length() > 0) {
                    buf.append(" OR (c.template_type = 1 AND c.template_id IN(");
                } else {
                    buf.append(" (c.template_type = 1 AND c.template_id IN(");
                }
                for (int i = 0; i < params.getWlbTemplateIds().size(); i++) {
                    buf.append(i > 0 ? ", ?" : "?");
                    q.add(params.getWlbTemplateIds().get(i));
                }
                buf.append("))");
            }

            if (CollectionUtils.isNotEmpty(params.getKjTemplateIds())) {
                if (buf.length() > 0) {
                    buf.append(" OR (c.template_type = 2 AND c.template_id IN(");
                } else {
                    buf.append(" (c.template_type = 2 AND c.template_id IN(");
                }
                for (int i = 0; i < params.getKjTemplateIds().size(); i++) {
                    buf.append(i > 0 ? ", ?" : "?");
                    q.add(params.getKjTemplateIds().get(i));
                }
                buf.append("))");
            }
            q.and().conjunct(buf.toString(), buf.length() > 0);
        }
        if ("consigned".equals(params.getTimeType())) {
            tradeSqlQueryBuilder.buildDateRangeQuery(q, "c.consigned", params.getStartDate(), params.getEndDate());
        } else if ("uploadTime".equals(params.getTimeType())) {
            tradeSqlQueryBuilder.buildDateRangeQuery(q, "c.upload_time", params.getStartDate(), params.getEndDate());
        } else if ("tradePay".equals(params.getTimeType())) {
            //另一个查询时间
            tradeSqlQueryBuilder.buildDateRangeQuery(q, "c.trade_pay", params.getStartDate(), params.getEndDate());
        } else if ("pay_time".equals(params.getTimeType())) {
            //前台传来的时间类型
            tradeSqlQueryBuilder.buildDateRangeQuery(q, "c.trade_pay", params.getStartTime() != null ? new Date(params.getStartTime()) : null, params.getEndTime() != null ? new Date(params.getEndTime()) : null);
        } else if ("created".equals(params.getTimeType())) {
            tradeSqlQueryBuilder.buildDateRangeQuery(q, "t.created", params.getStartTime() != null ? new Date(params.getStartTime()) : null, params.getEndTime() != null ? new Date(params.getEndTime()) : null);
        } else if ("timeoutActionTime".equals(params.getTimeType())) {
            q.and().append(" t.timeout_action_time <= ?").add(params.getEndDate());
        }
        if ("deductDetail".equals(params.getQueryFlag())) {
            q.and().append("(c.merge_sid <= 0 OR c.merge_sid = sid) AND c.buy_type = 1");
        } else if (QUERY_CLOSED.equals(params.getQueryFlag())) {
            //交易关闭跟踪界面只显示系统发货后的订单 系统已发货，平台未发货的才需要拦截
            q.and().append("t.sys_consigned = 1");
        } else {
            q.and().append("c.enable_status = 1");
        }
        if (CollectionUtils.isNotEmpty(TradeConsignConfigDiamondUtils.getHideErrors())) {
            q.append(" and (c.error_desc is null or c.error_desc not in ('" + String.join("\',\'", TradeConsignConfigDiamondUtils.getHideErrors()) + "'))");
        }

        return q;
    }

    private void buildIn(Query q, String field, String str, boolean converToLong) {
        if (StringUtils.isBlank(str) || StringUtils.isBlank(field)) {
            return;
        }
        String[] array = ArrayUtils.toStringArray(str.replaceAll("，", ","));
        String sql = field + " IN (";
        for (int i = 0; i < array.length; i++) {
            sql += (i > 0 ? ", ?" : "?");
            if (converToLong) {
                q.add(Long.valueOf(array[i]));
            } else {
                q.add(array[i]);
            }
        }
        sql += ") ";
        q.and().conjunct(sql, true);
    }

    public List<Long> queryRetryList(Staff staff, List<String> errorDescs, int unlimited, Integer retryCount) {
        if (unlimited != 0 && unlimited != 1) {
            return new ArrayList<>();
        }
        Map<String, Object> params = initParams(staff);
        params.put("errorDescs", errorDescs);
        params.put("unlimited", unlimited);
        params.put("retryCount", retryCount);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryRetryList"), params);
    }

    public List<Long> queryDelayRetryList(Staff staff, Date minRetryTime, Date maxRetryTime, Integer minRetryCount, Integer maxRetryMaxCount) {
        Map<String, Object> params = initParams(staff);
        params.put("minRetryTime", minRetryTime);
        params.put("maxRetryTime", maxRetryTime);
        params.put("minRetryCount", minRetryCount);
        params.put("maxRetryCount", maxRetryMaxCount);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryDelayRetryList"), params);
    }

    public int updateRetryList(Staff staff, List<Long> sids) {
        Map<String, Object> params = initParams(staff);
        params.put("sids", sids);
        return getSqlMapClientTemplate(staff).update(buildStatementName("updateRetryList"), params);
    }

    public List<Long> queryRetryListByUserId(Staff staff, Long userId, Date minRetryTime) {
        Map<String, Object> params = initParams(staff);
        params.put("userId", userId);
        params.put("minRetryTime", minRetryTime);
        return getSqlMapClientTemplate(staff).queryForList(buildStatementName("queryRetryListByUserId"), params);
    }

    private Map<String, Object> initParams(Staff staff) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("companyId", staff.getCompanyId());
        params.put("dbNo", staff.getDbInfo().getTradeDbNo());
        return params;
    }
}

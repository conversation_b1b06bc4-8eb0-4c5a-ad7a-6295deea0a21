package com.raycloud.dmj.business.modify;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.*;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.audit.help.AuditUtils;
import com.raycloud.dmj.business.common.StaffAssembleBusiness;
import com.raycloud.dmj.business.common.TradeAutoMatchBusiness;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.tag.TradeTagBusiness;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.domain.enums.OpVEnum;
import com.raycloud.dmj.business.trade.FxTradeNotifyBusiness;
import com.raycloud.dmj.domain.trade.audit.AuditParams;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.except.TradePlatExceptUtils;
import com.raycloud.dmj.domain.trades.audit.AuditData;
import com.raycloud.dmj.domain.trade.invoice.TradeInvoice;
import com.raycloud.dmj.domain.trade.invoice.TradeInvoiceUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.except.domain.ExceptData;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.business.operate.MoneyChangeBusiness;
import com.raycloud.dmj.business.operate.PayAmountCalculateBusiness;
import com.raycloud.dmj.business.operate.PaymentCalculateBusiness;
import com.raycloud.dmj.business.rematch.business.ReMatchBusiness;
import com.raycloud.dmj.business.split.warehouse.ai.help.WarehouseAiClient;
import com.raycloud.dmj.business.stock.TradeConfigStockBusiness;
import com.raycloud.dmj.business.tradeext.TradeExtBusiness;
import com.raycloud.dmj.business.tradepay.TradePayBusiness;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.*;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompany;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.split.TradeSplitEnum;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.diamond.TradeSaveSyncRematchConfigUtils;
import com.raycloud.dmj.express.api.*;
import com.raycloud.dmj.print.api.enums.TradePrintEntranceEnum;
import com.raycloud.dmj.services.account.*;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.gift.utils.GiftRuleUtils;
import com.raycloud.dmj.services.trade.audit.TradeAuditService;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trade.merge.ITradeMergeService;
import com.raycloud.dmj.services.trade.supplier.IOrderSupplierService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.*;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import com.raycloud.erp.db.router.DbContextHolder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.*;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.*;
import org.springframework.util.Assert;

import javax.annotation.*;
import java.beans.Introspector;
import java.util.*;
import java.util.regex.Pattern;

import static com.raycloud.dmj.domain.trade.config.TradeConfigEnum.*;

/**
 * Created by yangheng on 17/7/11.
 * 新增系统订单
 */
@Service
public class TradeAddBusiness{

    private static final TradeCopier<Trade, Trade> tradeCopier = new TradeCopier<>();

    @Resource
    PaymentCalculateBusiness paymentCalculateBusiness;
    @Resource
    PayAmountCalculateBusiness payAmountCalculateBusiness;
    @Resource
    ICompanyService companyService;
    @Resource
    ITradeUserConfigService tradeUserConfigService;
    @Resource
    FxBusiness fxBusiness;
    @Resource
    FxTradeNotifyBusiness fxTradeNotifyBusiness;
    @Resource
    IStaffService staffService;
    @Resource
    StaffAssembleBusiness staffAssembleBusiness;
    @Resource
    ApplicationContext applicationContext;
    TradeAddBusiness beanProxy;
    @Resource
    TradeConfigStockBusiness tradeConfigStockBusiness;
    @Resource
    TradePayBusiness tradePayBusiness;
    @Resource
    TradeExtBusiness tradeExtBusiness;
    @Resource
    MoneyChangeBusiness moneyChangeBusiness;
    @Resource
    IOrderSupplierService orderSupplierService;
    @Resource
    ITradeSalesmanService tradeSalesManService;
    @Resource
    IUserLogisticsCompanyBusiness userLogisticsCompanyBusiness;
    @Resource
    IUserLogisticsCompanyTemplateService userLogisticsCompanyTemplateService;

    @Resource
    ReMatchBusiness reMatchBusiness;
    @Resource
    ITradeInvoiceService tradeInvoiceService;
    @Resource
    WarehouseAiClient warehouseAiClient;

    @Resource
    ITradeConfigNewService tradeConfigNewService;

    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;

    @Resource
    ITradeMergeService tradeMergeService;

    @Resource
    FeatureService featureService;
    @Resource
    TradeTagBusiness tradeTagBusiness;

    @Resource
    TradeSysProductTimeoutActionTimeService tradeSysProductTimeoutActionTimeService;

    @Resource
    TradeAuditService tradeAuditService;

    @Resource
    ModifyParentBusiness modifyParentBusiness;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    IOrderStockService orderStockService;
    @Resource
    ITradeUpdateService tradeUpdateService;
    @Resource
    TradeAutoMatchBusiness tradeAutoMatchBusiness;
    @Resource
    ITradePtService tradePtService;
    @Resource
    TradeTraceBusiness tradeTraceBusiness;
    @Resource
    IEventCenter eventCenter;
    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    IdWorkerService idWorkerService;

    @PostConstruct
    public void startup() {
        beanProxy = (TradeAddBusiness) applicationContext.getBean(Introspector.decapitalize(this.getClass().getSimpleName()));
    }


    @Transactional
    public Trade save(Staff staff, Trade trade, boolean force) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        moneyChangeBusiness.tradeUpdateMoneyLog(staff, OpEnum.MONEY_CHANGE_PARAMAS_BEFORE, OpEnum.TRADE_ADD, Collections.singletonList(trade));
        //校验订单信息
        validate(staff, trade);
        //初始化订单信息,包裹订单应付金额,优惠金额的计算等
        List<Order> orders = initTrade(staff, trade);
        ExceptData tradeExceptData = TradeExceptUtils.getTradeExceptData(trade);
        if (TradePlatExceptUtils.isContainExcept(staff, trade, ExceptEnum.WAITING_RETURN_WMS)) {
            // 售后订单保存，存在等待退货入仓 异常，交易标记等待退货入仓异常
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.WAITING_RETURN_WMS, 1L);
            trade.getOperations().put(OpEnum.EXCEPT_UPDATE, String.format("新增【%s】异常",ExceptEnum.WAITING_RETURN_WMS.getChinese()));
            Logs.debug(LogHelper.buildLog(staff,String.format("sid=%s 标记等待退货入仓异常 %s %s",trade.getSid(),trade.getItemExcep(),tradeExceptData)));
        }
        if (TradePlatExceptUtils.isContainExcept(staff, trade, ExceptEnum.PART_PAY_EXCEPT)) {
            trade.getOperations().put(OpEnum.EXCEPT_UPDATE, String.format("换货商品销售价大于退货商品销售价，标记 [%s] 异常",ExceptEnum.PART_PAY_EXCEPT.getChinese()));
            TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.PART_PAY_EXCEPT, 1L);
        }
        Logs.debug(LogHelper.buildLog(staff,String.format("sid=%s 订单保存日志 %s %s",trade.getSid(),trade.getItemExcep(),tradeExceptData)));
        if (!force) {
            modifyParentBusiness.validateItemStock(staff, trade.getWarehouseId(), orders);
        }
        try {
            // 申请库存
            orderStockService.applyOrderStockLocal(staff, TradeUtils.getOrders4Trade(trade));
            // 计算trade信息
            modifyParentBusiness.calculate(staff, trade, tradeConfig);
            //理论重量是否覆盖实际重量
            modifyParentBusiness.isCoverWeight(staff, Collections.singletonList(trade), tradeConfig);
            // 保存原始输入的快递模版名称
            trade.setOriginTemplateName(trade.getTemplateName());
            List<Trade> tradeList = new ArrayList<>();
            tradeList.add(trade);
            //换货订单自动标记标签
            autoSignExchangeTags(staff, trade);
            //快卖通现场自提订单 自动标记 现场自提标签
            matchSelfPickTags(staff, trade);

            //处理pdd的ext信息
            trade = tradeExtBusiness.initTradeExt(staff, trade);
            //处理京东的ext信息
            tradeExtBusiness.initTradeExtsForJdAsTrades(staff, Lists.newArrayList(trade));
            //增强处理ext信息
            tradeExtBusiness.commonInitTradeExtsForSaleSaveTrade(staff, Lists.newArrayList(trade));

            tradeUpdateService.updateTrades(staff, null, null, trade, orders);
            //标签规则匹配
            tradeTagBusiness.autoReMatchMark(staff, TradeUtils.toSids(tradeList), 0);
            //根据快递匹配规则自动匹配
            tradeAutoMatchBusiness.match(staff, tradeList, tradeConfig);
            tradePtService.saveByTrades(staff, tradeList, TradePrintEntranceEnum.TRADE_PRINT_ENTRANCE_2003);


            //添加支付单
            tradePayBusiness.tradePayAdd(staff, trade);

            setTraceContent(trade);
            tradeTraceBusiness.asyncTrace(staff, tradeList, OpEnum.TRADE_ADD);
            moneyChangeBusiness.tradeUpdateMoneyLog(staff, OpEnum.MONEY_CHANGE_AFTER, OpEnum.TRADE_ADD, Collections.singletonList(trade));
            //记录日志
            Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("新增系统订单成功[sid=%s]", trade.getSid())));
            tradeConfigStockBusiness.handleStock(staff, Collections.singletonList(trade));
            //供应商同步
            orderSupplierService.sync(staff, TradeUtils.getOrders4Trade(trade), null);

            //白名单内走同步，默认异步处理
            boolean rematchInSync = TradeSaveSyncRematchConfigUtils.tradeSaveSyncRematchInSync(staff);
            if (TradeUtils.isReissueOrChangeitem(trade)) {
                reMatchBusiness.reMatch(staff, TradeUtils.toSidList(tradeList), EventEnum.EVENT_CREATE_AFTER_SALE_TRADE, Boolean.TRUE);
            } else {
                //发送重算事件，所有的重算事件都只需要发一个这个事件处理即可
                reMatchBusiness.reMatch(staff, TradeUtils.toSidList(tradeList), EventEnum.EVENT_CREATE_SYS_TRADE, !rematchInSync);
            }
            //增加业务员
            tradeSalesManService.saveTradeDefaultSalesmanIfNotExist(staff, TradeUtils.toTrades(trade));
            //保存发票信息
            saveTradeInvoice(staff, trade);
            tradeSysLabelBusiness.matchSystemLabels(staff, Lists.newArrayList(trade), Lists.newArrayList(SystemTags.TAG_ITEM_PRICE_EXCEPTION, SystemTags.TAG_NEED_INVOICE), true, true, true);
            // 新增之后需要做的处理
            beanProxy.afterSaveTrade(staff, trade);
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(staff, e, "新增系统订单出错"), e);
            throw new TradeException("新建订单失败", e);
        }
        return trade;
    }

    /**
     * 保存发票信息
     */
    private void saveTradeInvoice(Staff staff, Trade trade){
        TradeInvoice tradeInvoice = TradeInvoiceUtils.trade2Invoice(trade);
        if(tradeInvoice != null){
            tradeInvoiceService.addTradeInvoices(staff, Collections.singletonList(tradeInvoice));
        }
    }

    /**
     * 后置业务，新启一个新事务处理后面的业务，保证里面的错误不会影响前面已保存的核心业务
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.NESTED)
    public void afterSaveTrade(Staff staff, Trade trade) {
        User user = staff.getUserByUserId(trade.getUserId());
        String buyerNick = trade.getBuyerNick();

        try{
            MDC.put(TradeConstants.GX_TRADE_REISSUE_SYNC_IMPORT,"1");
            trade.setFxSids(handleFxReissueOrChangeItemTrade(staff,trade));
        }catch (Exception e){
            trade.setFxSids(Lists.newArrayList(trade.getSid()));
            Logs.error(LogHelper.buildErrorLog(staff, e, String.format("[分销业务][sid:%s]handleFxReissueOrChangeItemTrade",trade.getSid())), e);
        }finally {
            MDC.remove(TradeConstants.GX_TRADE_REISSUE_SYNC_IMPORT);
            DbContextHolder.set(staff.getDbKey());
        }
        //分销换货补发订单 自动审核逻辑已经在上面handleFxReissueOrChangeItemTrade中实现 这里中断
        if(trade.hasOpV(OpVEnum.TRADE_NEED_AUTO_AUDIT) && TradeUtils.isReissueOrChangeitem(trade) && TradeUtils.isFxTrade(trade)){
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("[分销业务]售后生成换货补发订单,fxsids=%s,errors=%s",trade.getFxSids(), JSONObject.toJSONString(trade.getErrors()))));
            return;
        }

        if (TradeUtils.isReissueOrChangeitem(trade)) {
            TradeConfigNew tradeConfigNew = tradeConfigNewService.get(staff, REISSUE_OR_CHANGE_TRADE_WAREHOUSE_MATCH);
            if (tradeConfigNew.isOpen()) {
                List<Long> sids = new ArrayList<>();
                sids.add(trade.getSid());
                TradeConfig tradeConfig = tradeConfigService.get(staff);
                // 换货、补发订单，配置开启，同步执行一遍 按规则分仓
                warehouseAiClient.warehouseAiProcessByRule(staff, tradeConfig, sids);
            }
            // 如果是换货补发订单 发送自动审核事件 触发自动审核
            Logs.ifDebug(LogHelper.buildLog(staff, "售后生成换货补发订单触发自动审核"));
            // 发送事件指定系统单号，走自动审核逻辑
            AuditParams auditParams = new AuditParams();
            auditParams.setSids(new HashSet<>(Collections.singletonList(trade.getSid())));
            eventCenter.fireEvent(this, new EventInfo("trade.auto.audit").setArgs(new Object[]{user.getStaff(), user, auditParams}), null);
        }

        boolean reissueOrChangeItemOrderMerge = TradeUtils.isReissueOrChangeitem(trade) &&
                tradeConfigNewService.get(staff, TradeConfigEnum.CHANGE_GOODS_AFTER_MATCH_MERGE_RULE).isOpen();
        boolean normalOrderMerge = !TradeUtils.isReissueOrChangeitem(trade) &&
                tradeConfigNewService.get(staff, TradeConfigEnum.NEW_TRADE_AFTER_MATCH_MERGE_RULE).isOpen();
        if (StringUtils.isNotEmpty(buyerNick) &&
                (reissueOrChangeItemOrderMerge || normalOrderMerge)
        ) {
            Map<String, Set<String>> sourceQueryKeysMap = new HashMap<>();
            Set<String> queryKeys = new HashSet<>();
            queryKeys.add(buyerNick);
            sourceQueryKeysMap.put(trade.getSource(), queryKeys);
            tradeMergeService.mergeAuto(staff, user, sourceQueryKeysMap);
        }
    }

    /**
     * 分销的换货交易订单，自动审核到供销。
     *
     * 1.新增分销属性
     * 2.循环触发审核和供销同步下载
     * 3.一级二级分销需要自动审核，供销不需要
     *
     * @param staff
     * @param trade
     */
    private List<Long> handleFxReissueOrChangeItemTrade(Staff staff, Trade trade) {
        List<Long> sids4Return = Lists.newArrayListWithCapacity(3);
        if(!TradeUtils.isReissueOrChangeitem(trade)){
            return sids4Return;
        }
        boolean tradeNeedAutoAudit = trade.hasOpV(OpVEnum.TRADE_NEED_AUTO_AUDIT);
        try{
            boolean tradeNotNeedMatchDmsAttr = trade.hasOpV(OpVEnum.TRADE_NOT_NEED_MATCH_DMS_ATTR);
            List<Trade> trades;
            if(tradeNotNeedMatchDmsAttr){
                trades = Lists.newArrayList(trade);
            }else {
                trades =  fxBusiness.matchSupplyIdsWithDmsAttrTypeAndSave(staff.getUser(),Lists.newArrayList(trade),3,1);
            }
            if(CollectionUtils.isNotEmpty(trades)){
                BeanCopierUtil.copy(trades.get(0),trade);
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLogHead(staff).append("新增分销属性出错"), e);
        }
        Map<Long, TradeResult> errors = Maps.newHashMapWithExpectedSize(6);
        trade.setErrors(errors);
        sids4Return.add(trade.getSid());
        if(!TradeUtils.isFxTrade(trade) || !tradeNeedAutoAudit){
            return sids4Return;
        }
        int i=0;
        Trade trade1 = tradeCopier.copy(trade, new TbTrade());
        Staff gxStaff = staff;
        do{
            trade1 = auditAndSyncGxTrade(gxStaff,errors,trade1);
            if(trade1 == null){
                break;
            }
            Logs.debug(LogHelper.buildLog(gxStaff, String.format("[分销业务][sid:%s]handleFxReissueOrChangeItemTrade,convertType=%s,belongType=%s,sourceId=%s,destId=%s" ,
                    trade1.getSid(),trade1.getConvertType(),trade1.getBelongType(),trade1.getSourceId(),trade1.getDestId())));
            Long currentCompanyId = trade1.getCompanyId();
            if(currentCompanyId ==null || currentCompanyId <=0){
                break;
            }
            gxStaff = staffAssembleBusiness.getDefaultStaff(currentCompanyId);
            sids4Return.add(trade1.getSid());
            if(!TradeUtils.isFxOrMixTrade(trade1)){
                break;
            }
        }while (i++<1);
        return  sids4Return;
    }

    /**
     * 分销订单审核并且触发供销同步下载
     * 只有分销或分销且供销才会触发供销下载
     *
     * 支持的分销类型，控制了最多三级
     * 二级分销：分销 -->  供销
     * 多级分销：分销 -->  二级分销 --> 供销
     *
     * @param staff
     * @param trade
     * @return
     */
    private Trade auditAndSyncGxTrade(Staff staff, Map<Long, TradeResult> tradeResultErrors,Trade trade){
        Logs.debug(LogHelper.buildLog(staff, String.format("[分销业务][sid:%s]auditAndSyncGxTrade" ,trade.getSid())));
        AuditData auditData = AuditUtils.init(TradeUtils.toSids(trade), tradeConfigService.get(staff), staff.openAuditActiveStockRecord(), false, null, 0, 100);
        Map<Long, TradeResult> errors = tradeAuditService.audit(staff, auditData).results;
        TradeResultVoWrapper resultVoWrapper = TradeResultVoWrapper.models2Vo(errors.values());
        Trade tradeCurrent = tradeSearchService.queryBySid(staff,false,trade.getSid());
        if(tradeCurrent ==null){
            tradeCurrent = trade;
            Logs.debug(LogHelper.buildLog(staff, String.format("[分销业务][sid:%s]当前事务读取trade为空" ,tradeCurrent.getSid())));
        }

        if(CollectionUtils.isNotEmpty(resultVoWrapper.getErrorResult())){
            Logs.debug(LogHelper.buildLog(staff, "[分销业务]审核订单失败数据" + JSONObject.toJSONString(resultVoWrapper)));
            // 新增分销属性时如果勾选了配置会触发审核，这里批判下订单状态如果是已经审核通过就表示已经审核成功
            if(!TradeStatusUtils.isFinishAuditButNotPrint(tradeCurrent) && MapUtils.isNotEmpty(errors)){
                tradeResultErrors.putAll(errors);
            }
            return null;
        }
        if(!TradeStatusUtils.isFinishAuditButNotPrint(tradeCurrent)){
            Logs.debug(LogHelper.buildLog(staff, String.format("[分销业务]审核订单后不是已审核状态，sid:%s,sysStatus:%s",tradeCurrent.getSid(),tradeCurrent.getSysStatus())));
            return null;
        }
        if(!TradeUtils.isFxOrMixTrade(trade)){
            return null;
        }
        fxTradeNotifyBusiness.syncGxTrade(staff,Lists.newArrayList(trade.getSid()));
        if(trade.getDestId()==null || trade.getDestId()<=0){
            Logs.debug(LogHelper.buildLog(staff, String.format("[分销业务][sid:%s]auditAndSyncGxTrade,对应的供销订单不存在",trade.getSid())));
            return null;
        }
        List<Trade> gxTrades = TradeUtils.toTrades(tradeSearchService.queryByTids(staffService.queryFullByCompanyId(trade.getDestId()), true, com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) ? trade.getMergeSid().toString() : trade.getSid().toString()));
        if(CollectionUtils.isNotEmpty(gxTrades)){
            return gxTrades.get(0);
        }
        Logs.debug(LogHelper.buildLog(staff, String.format("[分销业务][sid:%s]auditAndSyncGxTrade没有获取到对应的供销单" ,tradeCurrent.getSid())));
        return  null;
    }



    private void setTraceContent(Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        StringBuilder s = new StringBuilder("新增订单, 实付:").append(trade.getPayment()).append(", 商品: \n");
        int i = 0;
        if (orders.size() <= 100) {
            for (Order order : orders) {
                if (i++ > 0) {
                    s.append(",\n");
                }
                s.append("{商家编码:").append(order.getSysOuterId());
                String isPickChinese = GiftRuleUtils.getIsPickChinese(order.getIsPick());
                if (StringUtils.isNotBlank(isPickChinese)) {
                    s.append(", 是否拣选:").append(isPickChinese);
                }
                s.append(", 数量:").append(order.getStockNum()).append("/").append(order.getNum());
                s.append(", 单价:").append(order.getPrice());
                s.append(", 实付:").append(order.getPayment()).append("}");
            }
        }
        if (StringUtils.isNotEmpty(trade.getWarehouseName())) {
            s.append(", 选择仓库:").append(trade.getWarehouseName());
        }
        if (StringUtils.isNotEmpty(trade.getOriginTemplateName())) {
            s.append(", 选择快递模板:").append(trade.getOriginTemplateName());
        }
        trade.getOperations().put(OpEnum.TRADE_ADD, s.toString());
    }

    public void validate(Staff staff, Trade trade) {
        Assert.isTrue(trade.getWarehouseId() != null && trade.getWarehouseId() != 0, "请选择仓库");
        Assert.isTrue(trade.getPayment() != null && Pattern.matches("^\\d+(\\.\\d+)?$", trade.getPayment()), "请输入正确的付款金额,例如1.00");
        Assert.isTrue(!(StringUtils.isNotBlank(trade.getPostFee()) && !Pattern.matches("^\\d+(\\.\\d+)?$", trade.getPostFee())), "请输入正确的运费,例如1.00,不填为0");
        Assert.isTrue(StringUtils.isBlank(trade.getSysMemo()) || trade.getSysMemo().length() <= 1024, "系统备注超过最大长度（最多1024），请重新输入!");
        validateTid(staff, trade);
        if (trade.isOutstock()) {//出库单不校验一下信息
            return;
        }
        Assert.isTrue(StringUtils.isNotBlank(trade.getReceiverAddress()), "请输入详细地址");
        Assert.isTrue(StringUtils.isNotBlank(trade.getReceiverName()), "请输入收件人姓名");
        Assert.isTrue(!(StringUtils.isBlank(trade.getReceiverPhone()) && StringUtils.isBlank(trade.getReceiverMobile())), "手机号码和电话号码至少需要设置一个");
        Assert.isTrue(trade.getUserId() != null && trade.getUserId() != 0, "请选择店铺");
        if (!(trade.isOutstock() || TradeUtils.isGxOrMixTrade(trade))) {
            User user = staff.getUserIdMap().get(trade.getUserId());
            Assert.notNull(user, String.format("您没有当前所选店铺的权限[sid:%s]", trade.getSid()));
            Assert.isTrue(user.getActive() != null && user.getActive() != CommonConstants.JUDGE_NO, String.format("当前所选店铺已停用[userId:%s,sid:%s]", user.getId(), trade.getSid()));
        }

        trade.setSysMemo(WipeEmojiUtils.wipeEmoji(trade.getSysMemo()));
        trade.setSellerMemo(WipeEmojiUtils.wipeEmoji(trade.getSellerMemo()));
        trade.setBuyerNick(WipeEmojiUtils.wipeEmoji(trade.getBuyerNick()));
        trade.setReceiverAddress(WipeEmojiUtils.wipeEmoji(trade.getReceiverAddress()));

        if (trade.getLogisticsCompanyId() != null && trade.getLogisticsCompanyId() > 0) {
            UserLogisticsCompany userLogisticsCompany = userLogisticsCompanyBusiness.queryById(staff, trade.getLogisticsCompanyId(), true);
            if (userLogisticsCompany == null) {
                return;
            }
            Map<Long, UserExpressTemplate> longUserWlbExpressTemplateMap = userLogisticsCompanyTemplateService.matchOldTemplateProxy(staff, Collections.singletonList(trade), userLogisticsCompany);
            UserExpressTemplate userExpressTemplate = longUserWlbExpressTemplateMap.get(trade.getSid());
            if (userExpressTemplate == null) {
                throw new IllegalArgumentException("当前快递公司未开通电子面单或电子面单未启用");
            }
            trade.setTemplateId(userExpressTemplate.getId());
            trade.setTemplateType(userExpressTemplate.getTemplateType());
        }
    }

    private void validateTid(Staff staff, Trade trade) {
        if (trade.getTid() == null) {
            return;
        }
        List<TbTrade> tbTrades = tradeSearchService.queryByTids(staff, false, trade.getTid());
        Assert.isTrue(CollectionUtils.isEmpty(tbTrades), "该平台交易号" + trade.getTid() + "系统内已存在");
        int maxTidLength = 64;
        if (trade.getTid().length() >= maxTidLength) {
            Assert.isTrue(isYccrm(staff), "平台交易号" + trade.getTid() + "必须小于" + maxTidLength + "位");
        } else {
            Assert.isTrue(!trade.getTid().isEmpty(), "平台交易号" + trade.getTid() + "必须小于" + maxTidLength + "位");
        }
        Assert.isTrue(Pattern.matches("[^\\u4e00-\\u9fa5\\s]+", trade.getTid()), "该平台交易号" + trade.getTid() + "包含中文");
    }

    private boolean isYccrm(Staff staff) {
        Company company = staff.getCompany();
        if (company == null || company.getType() == null) {
            company = companyService.queryById(staff.getCompanyId());
        }
        return company.isYccrm();
    }

    //初始化新增的系统订单
    public List<Order> initTrade(Staff staff, final Trade trade) {

        boolean isClearNumIid = true;
        boolean isReissueOrChangeitem = TradeUtils.isReissueOrChangeitem(trade);
        if (isReissueOrChangeitem && featureService.checkHasFeature(staff.getCompanyId(), Feature.ONE_EXCHANGE_MANY_INHERIT_PROPERTY_COMPANY_IDS)) {
            isClearNumIid = false;
        }
        //发票信息
        if (StringUtils.isNotEmpty(trade.getInvoiceName()) || StringUtils.isNotEmpty(trade.getBuyerTaxNo())) {
            trade.setNeedInvoice(1);
            trade.setInvoiceKind("1");
        }
        trade.setSid(idWorkerService.nextId());
        trade.setSysStatus(Trade.SYS_STATUS_WAIT_AUDIT);
        //给予默认值 防止报错日志
        trade.setMergeSid(Objects.nonNull(trade.getMergeSid()) ? trade.getMergeSid() : -1L);
        //trade.setStockStatus(Trade.STOCK_STATUS_EMPTY);
        TradeExceptUtils.setStockStatus(staff,trade,Trade.STOCK_STATUS_EMPTY);
        if(!isQmTrdade(trade)){
            trade.setSource(CommonConstants.PLAT_FORM_TYPE_SYS);
        }
        if (StringUtils.isBlank(trade.getTid())) {//售后工单已设置过tid
            trade.setTid(trade.getSid().toString());    // 系统订单的tid和sid相同
        }
        trade.setCompanyId(staff.getCompanyId());
        trade.setMergeType(TradeMergeEnum.MERGE_NORMAL.getDbType());
        trade.setMergeSid(-1L);
        trade.setSplitType(TradeSplitEnum.SPLIT_NORMAL.getDbType());
        trade.setSplitSid(-1L);
        trade.setEnableStatus(1);
        if (trade.getIsCancel() == null) {
            trade.setIsCancel(0);
        }
        //系统订单设置下单时间为创建时间,付款时间为下单时间
        trade.setCreated(new Date());
        if (trade.getPayTime() == null) {
            trade.setPayTime(trade.getCreated());
        }
        TradeUtils.setMobileTail(trade);
        if (!isQmTrdade(trade)) {
            if (trade.isOutstock()) {
                trade.setUserId(0L);
                trade.setTaobaoId(0L);
            } else if (TradeUtils.isGxOrMixTrade(trade)) {
                trade.setUserId(100000000L);
                trade.setTaobaoId(100000000L);
            } else {
                User user = staff.getUserByUserId(trade.getUserId());
                Assert.notNull(user, "您没有该店铺的权限!");
                trade.setTaobaoId(user.getTaobaoId());
                if (CommonConstants.PLAT_FORM_TYPE_TAO_BAO_TJB.equals(user.getSubSource())) {
                    trade.setSubSource(user.getSubSource());
                }
            }
        }
        // 校验商品(规格)数据是否符合格式
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        for (Order order : orders) {
            order.setWarehouseId(trade.getWarehouseId());
            order.setUserId(trade.getUserId());
            modifyParentBusiness.verifyOrder(staff, order, trade);
            if (order.getId() == null || order.getId() <= 0) {
                modifyParentBusiness.initOrder(staff, order, trade);
            }
            if (order.getWarehouseId() == null) {
                order.setWarehouseId(trade.getWarehouseId());
            }
        }
        //设置子订单商品规格数据信息,如价格,规格属性名,规格别名等
        modifyParentBusiness.initItems4Trade(staff, orders, trade.isOutstock(), true);
        if(!StringUtils.equals(CommonConstants.PLAT_FORM_TYPE_FXXZ,trade.getSubSource())){
            for(Order order: orders) {
                order.setSkuPropertiesName("");
                order.setTitle("");
                order.setOuterSkuId("");
                order.setOuterIid("");
                if(isClearNumIid) {
                    order.setNumIid("");
                }
            }
        }
        //计算优惠
        trade.setDiscountFee(trade.getDiscountFee());
        //订单标准金额
        trade.setTotalFee(StringUtils.isNotEmpty(trade.getTotalFee()) ? trade.getTotalFee() : PaymentUtils.calculateTradeTotalFee(trade));
        trade.setGrossProfitRate("0");
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        trade.setAcPayment(trade.getPayment());

        if (TradeConfig.needAutoShare(tradeConfig)) {
            paymentCalculateBusiness.shareDiscount(staff, trade);
        }
        //创建手工单时，不分摊订单实付金额   默认配置项是关闭的，配置值为0 ，打开才是1.
        if (tradeConfig.getInteger("createSysTradeNotShareAcPayment") != 1) {
            paymentCalculateBusiness.shareAcPayment(staff, trade);
        }
        payAmountCalculateBusiness.handleInsert(staff, trade, null);

        if (null != trade.getUserId() && trade.getUserId() != 0) { // 非出库单
            TradeUserConfig tradeUserConfig = tradeUserConfigService.getByUser(staff, staff.getUserByUserId(trade.getUserId()));
            TradeUtils.setTimeoutActionTime(trade, tradeConfig, tradeUserConfig);
            tradeSysProductTimeoutActionTimeService.setTradeTimeoutActionTimeFromProduct(staff, Lists.newArrayList(trade));
        }
        return orders;
    }

    /**
     * 换货订单自动标记标签
     */
    public void autoSignExchangeTags(Staff staff, Trade trade) {
        List<TradeTag> tags = Lists.newArrayList();
        if (StringUtils.isNotEmpty(trade.getType())
                && TradeConstants.TYPE_TRADE_EXCHANGE.equals(trade.getType())) {
            //是否开启换货匹配自定义标签
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            String chatConfigs = tradeConfig.getChatConfigs();
            if (StringUtils.isNotEmpty(chatConfigs)) {
                JSONObject jsonObject = JSONObject.parseObject(chatConfigs);
                String autoTagsExchange = jsonObject.getString("autoTagsExchange");
                if (StringUtils.isNotEmpty(autoTagsExchange)) {
                    List<String> existTagIds = new ArrayList<>();
                    if (StringUtils.isNotEmpty(trade.getTagIds())) {
                        existTagIds = Lists.newArrayList(StringUtils.split(trade.getTagIds(), ","));
                    }
                    existTagIds.addAll(Lists.newArrayList(StringUtils.split(autoTagsExchange, ",")));
                    Set<String> tagIds = new HashSet<>(existTagIds);
                    trade.setTagIds(StringUtils.join(tagIds.toArray(), ","));
                }
            }
            tags.add(SystemTags.TAG_EXCHANGE);
            tradeSysLabelBusiness.addTags(staff, Collections.singletonList(trade), OpEnum.TAG_EXCHANGE, tags);
        }
        if (StringUtils.isNotEmpty(trade.getType())
                && TradeConstants.TYPE_TRADE_REISSUE.equals(trade.getType())) {
            tags.add(SystemTags.TAG_REISSUE);
            tradeSysLabelBusiness.addTags(staff, Collections.singletonList(trade), OpEnum.TAG_REISSUE, tags);
        }
    }

    /**
     * 匹配现场自提系统标签，快卖通专用
     */
    private void matchSelfPickTags(Staff staff, Trade trade){
        if (trade.getIfKmtSelfPick()){
            List<TradeTag> tags = Lists.newArrayList();
            tags.add(SystemTags.TAG_SELF_PICK);
            tradeSysLabelBusiness.addTags(staff, Collections.singletonList(trade), OpEnum.ADD_TAG, tags);
        }
    }

    private boolean isQmTrdade(Trade trade) {
        boolean flag = false;
        TradeExt tradeExt = trade.getTradeExt();
        if (Objects.nonNull(tradeExt)) {
            String extraFields = tradeExt.getExtraFields();
            if (StringUtils.isNotBlank(extraFields)) {
                Map map = JSONObject.parseObject(extraFields, Map.class);
                if (MapUtils.isNotEmpty(map)) {
                    String source = (String) map.get("source");
                    if ("qimen".equalsIgnoreCase(source)) {
                        flag = true;
                    }
                }
            }
        }
        return flag;
    }


}

package com.raycloud.dmj.services.ec.fx;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.audit.AuditStockBusiness;
import com.raycloud.dmj.business.audit.help.AuditUtils;
import com.raycloud.dmj.business.common.TradeBatchExecutor;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.split.SplitGxInsufficientData;
import com.raycloud.dmj.business.split.support.SplitInsufficientUtils;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.domain.OrderConstant;
import com.raycloud.dmj.domain.TradeBatchRequest;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.split.SplitResult;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.audit.AuditData;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeBuilderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.trade.audit.TradeAuditService;
import com.raycloud.dmj.services.trade.split.ITradeSplitService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.ec.api.*;
import com.raycloud.ec.api.annotation.ListenerBind;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2021-0105 17:13
 * @Description GxSplitInsufficientBatchListener
 */
@Component
@ListenerBind("gx.split.insufficient.batch")
public class GxSplitInsufficientBatchListener extends TradeBatchExecutor implements IEventListener {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    ITradeSplitService tradeSplitService;
    @Resource
    ILockService lockService;
    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource
    IStaffService staffService;
    @Resource
    protected TradeTraceBusiness tradeTraceBusiness;
    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    AuditStockBusiness auditStockBusiness;
    @Resource
    TradeLocalConfigurable tradeLocalConfig;

    @Resource
    TradeAuditService tradeAuditService;


    @Override
    public void onObserved(EventSourceBase source) {
        CommonEventSource evt = (CommonEventSource) (source);
        Staff staff = evt.getArg(0, Staff.class);
        TradeControllerParams params = evt.getArg(1, TradeControllerParams.class);
        Logs.ifDebug(LogHelper.buildLog(staff, String.format("接收到事件%s", evt.getEventName())));
        long start = System.currentTimeMillis();
        TradeBatchRequest request = new TradeBatchRequest();
        try {
            request.setProgressEnum(ProgressEnum.PROGRESS_GX_SPLIT_INSUFFICIENT);
            request.setParams(params);
            execute(staff, request);
        } catch (Throwable e) {
            Logs.error(LogHelper.buildLog(staff, "供销商缺货拆分出错"), e);
        } finally {
            progressService.updateProgressComplete(staff, request.getProgressEnum(), request.getProgress());
            Logs.ifDebug(LogHelper.buildLog(staff, String.format("事件[%s]处理结束,耗时:%sms", evt.getEventName(), (System.currentTimeMillis() - start))));
        }
    }

    @Override
    protected <R extends TradeBatchRequest> void execute(Staff staff, R request, List<Trade> trades, Object... args) {
        List<Trade> gxTrades = tradeSearchService.queryBySids(staff, true, TradeUtils.toSids(trades));
        List<Trade> needHandleTrades = checkTrades(staff,gxTrades, request);
        logger.debug(LogHelper.buildLog(staff, String.format("needHandleTrades=%s", JSON.toJSONString(needHandleTrades))));
        if (CollectionUtils.isEmpty(needHandleTrades)) {
            return;
        }
        if(TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()){
            splitInsufficientGxNotRelFx(staff,request,needHandleTrades);
            return;
        }
        if (tradeLocalConfig.isTradeSplitFx2GxCompanyIds(staff.getCompanyId())) {
            SplitInsufficientFx2GxNew(staff, request, needHandleTrades);
            return;
        }
        Map<Long, List<Trade>> sourceTradeMap = TradeUtils.groupBySourceId(needHandleTrades);
        ProgressData progressData = request.getProgress();
        sourceTradeMap.forEach((s, t) -> {
            try {
                SplitGxInsufficientData splitData = new SplitGxInsufficientData();
                List<Trade> gxGroupTrades = tradeSearchService.queryBySids(staff, true, TradeUtils.toSids(t));
                splitData.originGxTrades.addAll(gxGroupTrades);
                if (CollectionUtils.isEmpty(gxGroupTrades)) {
                    logger.error(LogHelper.buildLog(staff, "供销订单为空!"));
                    return;
                }
                Staff fxStaff = staffService.queryFullByCompanyId(s);
                fxStaff.setUserIdMap(fxStaff.getUsers().stream().collect(Collectors.toMap(User::getId, user -> user)));
                List<Trade> fxTrades = searchFxTrade(fxStaff, gxGroupTrades, request);
                if (CollectionUtils.isEmpty(fxTrades)) {
                    logger.error(LogHelper.buildLog(staff, "分销订单为空!"));
                    return;
                }
                splitData.originFxTrades.addAll(fxTrades);
                doSplit(staff, fxStaff, splitData, request);
            } catch (Exception e) {
                Logs.error(LogHelper.buildLog(staff, "拆分出错"), e);
                progressData.getMsg().addAll(TradeUtils.toTidList(t));
                progressData.setErrorNum(progressData.getErrorNum() + t.size());
            }
        });

    }

    private <R extends TradeBatchRequest> void SplitInsufficientFx2GxNew(Staff staff, R request, List<Trade> trades) {
        ProgressData progressData = request.getProgress();
        try {
            Map<Trade, String> tradeGroupCount = new HashMap<>();
            for (Trade trade : trades) {
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                for (Order order : orders) {
                    if (order.getStockNum() == null) {
                        order.setStockNum(0);
                    }
                    //非待审核 || 非缺货 过滤
                    if (!SplitInsufficientUtils.isNotInsufficient(staff,order) && order.getStockNum() < order.getNum()) {
                        order.setSplitNum(order.getNum() - order.getStockNum());
                    } else {
                        order.setSplitNum(0);
                    }
                }
                tradeGroupCount.put(trade, String.valueOf(orders.size()));
            }
            SplitResult result = tradeSplitService.splitMixBatch(staff, tradeGroupCount, true);
            List<Long> sucSid = Arrays.asList(result.getResultSids());
            List<String> errorGxTid = trades.stream().filter(t -> !sucSid.contains(t.getSid())).map(Trade::getTid).collect(Collectors.toList());
            int sucNum = trades.size() - errorGxTid.size();
            progressData.getMsg().addAll(errorGxTid);
            progressData.setSucNum(progressData.getSucNum() + sucNum).setErrorNum(progressData.getErrorNum() + errorGxTid.size());
            progressService.updateProgress(staff, request.getProgressEnum(), progressData);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, "拆分出错"), e);
            progressData.getMsg().addAll(TradeUtils.toTidList(trades));
            progressData.setErrorNum(progressData.getErrorNum() + trades.size());
        }
    }

    private <R extends TradeBatchRequest> void splitInsufficientGxNotRelFx(Staff staff, R request, List<Trade> trades) {
        ProgressData progressData = request.getProgress();
        try {
            Map<Trade, String> tradeGroupCount = new HashMap<>();
            for (Trade trade : trades) {
                List<Order> orders = TradeUtils.getOrders4Trade(trade);
                for (Order order : orders) {
                    if (order.getStockNum() == null) {
                        order.setStockNum(0);
                    }
                    //非待审核 || 非缺货 过滤
                    if (!SplitInsufficientUtils.isNotInsufficient(staff,order) && order.getStockNum() < order.getNum()) {
                        order.setSplitNum(order.getNum() - order.getStockNum());
                    } else {
                        order.setSplitNum(0);
                    }
                }
                tradeGroupCount.put(trade, String.valueOf(orders.size()));
            }
            SplitResult result = tradeSplitService.splitMixBatch(staff, tradeGroupCount, false);
            List<Long> sucSid = Arrays.asList(result.getResultSids());
            List<String> errorGxSids = trades.stream().filter(t -> !sucSid.contains(t.getSid())).map(t->String.valueOf(t.getSid())).collect(Collectors.toList());
            int sucNum = trades.size() - errorGxSids.size();
            progressData.getMsg().addAll(errorGxSids);
            progressData.setSucNum(progressData.getSucNum() + sucNum).setErrorNum(progressData.getErrorNum() + errorGxSids.size());
            progressService.updateProgress(staff, request.getProgressEnum(), progressData);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, "拆分出错"), e);
            progressData.getMsg().addAll(trades.stream().map(t->String.valueOf(t.getSid())).collect(Collectors.toList()));
            progressData.setErrorNum(progressData.getErrorNum() + trades.size());
        }
    }

    private List<Trade> handleFxTrade(Staff staff, Staff fxStaff, SplitGxInsufficientData splitData) {
        return lockService.locks(tradeLockBusiness.getERPLocks(fxStaff, TradeUtils.toSids(splitData.originFxTrades)), () -> {
            //反审核分销订单
            List<Trade> unAuditSuccessFxTrades = tradeAuditService.unaudit(fxStaff, OpEnum.AUDIT_UNDO_AUTO_FX_GX_INSUFFICIENT_SPLI,TradeUtils.toSids(splitData.originFxTrades)).getSuccessTrades();
            logger.debug(LogHelper.buildLog(staff, String.format("分销订单反审核成功=%s", TradeUtils.toTidList(unAuditSuccessFxTrades))));
            //分销拆单
            List<Trade> splitFxSid = splitFxTrade(staff, fxStaff, unAuditSuccessFxTrades, splitData);
            logger.debug(LogHelper.buildLog(staff, String.format("doSplit_collect=%s", TradeUtils.toTidList(splitFxSid))));
            return splitFxSid;
        });
    }


    private <R extends TradeBatchRequest> void doSplit(Staff staff, Staff fxStaff, SplitGxInsufficientData splitData, R request) {
        ProgressData progressData = request.getProgress();
        long count = splitData.originGxTrades.size(), sucCount;
        List<Trade> succTrades = handleFxTrade(staff, fxStaff, splitData);
        logger.debug(LogHelper.buildLog(staff, String.format("拆分,反审核成功的订单=%s", TradeUtils.toSidList(succTrades))));
        List<Long> cancelSucc = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(succTrades)) {
            List<Trade> successGxTrades = splitData.originGxTrades.stream().filter(gxTrade -> TradeUtils.toSidList(succTrades).contains(Long.parseLong(gxTrade.getTid()))).collect(Collectors.toList());
            cancelSucc.addAll(TradeUtils.toSidList(successGxTrades));
            if (CollectionUtils.isNotEmpty(splitData.neendAuditFxTrades)) {
                List<Trade> fxTrades = tradeSearchService.queryBySids(fxStaff, false, splitData.neendAuditFxTrades.toArray(new Long[0]));
                Set<Long> fxSplitSid = fxTrades.stream().map(Trade::getSplitSid).collect(Collectors.toSet());
                //已经拆分需要审核的分销订单，找到拆分主单，再找到对应的供销订单增加反审核异常
                preUpdateGxTradeUnAuditExcep(staff, fxSplitSid);
                logger.debug(LogHelper.buildLog(staff, String.format("拆分成功后，自动审核订单=%s", splitData.neendAuditFxTrades)));
                AuditData auditData = AuditUtils.init(splitData.neendAuditFxTrades.toArray(new Long[0]), tradeConfigService.get(staff), staff.openAuditActiveStockRecord(), false, "", 0, 99);
                lockService.locks(tradeLockBusiness.getERPLocks(fxStaff, auditData.originSids), () -> tradeAuditService.audit(fxStaff, auditData));
                auditStockBusiness.auditStockWms(staff, auditData);
            }
        }
        sucCount = cancelSucc.size();
        if (sucCount > count) {
            sucCount = count;
        }
        List<Trade> errorGx = splitData.originGxTrades.stream().filter(gxTrade -> !cancelSucc.contains(gxTrade.getSid())).collect(Collectors.toList());
        long errorCount = count - sucCount;
        progressData.getMsg().addAll(TradeUtils.toTidList(errorGx));
        progressData.setSucNum(progressData.getSucNum() + sucCount).setErrorNum(progressData.getErrorNum() + errorCount);
        progressService.updateProgress(staff, request.getProgressEnum(), progressData);
    }

    private void preUpdateGxTradeUnAuditExcep(Staff staff, Set<Long> fxSplitSid) {
        List<TbTrade> gxTrades = tradeSearchService.queryByTids(staff, false, fxSplitSid.stream().map(Object::toString).toArray(String[]::new));
        if (CollectionUtils.isEmpty(gxTrades)) {
            return;
        }
        List<TbTrade> normalGxTrades = gxTrades.stream().filter(trade -> !TradeUtils.isCancel(trade)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalGxTrades)) {
            return;
        }
        List<Trade> updateTrades = Lists.newArrayListWithCapacity(normalGxTrades.size());
        for (TbTrade normalGxTrade : normalGxTrades) {
            if (TradeExceptUtils.isContainExcept(staff, normalGxTrade, ExceptEnum.FX_UNAUDIT)) {
                continue;
            }
            Trade tbTrade = TradeBuilderUtils.builderUpdateTrade(normalGxTrade);
            tbTrade.setSid(normalGxTrade.getSid());
            TradeExceptUtils.updateExcept(staff,tbTrade,ExceptEnum.FX_UNAUDIT,1L);
            Logs.debug(LogHelper.buildLog(staff,String.format("sid=%s标记[%s]异常",tbTrade.getSid(),ExceptEnum.FX_UNAUDIT.getChinese())));

            updateTrades.add(tbTrade);
        }
        if (CollectionUtils.isEmpty(updateTrades)) {
            return;
        }
        tradeUpdateService.updateTrades(staff, updateTrades);
    }


    private List<Trade> splitFxTrade(Staff staff, Staff fxStaff, List<Trade> fxTrades, SplitGxInsufficientData splitData) {
        if (CollectionUtils.isEmpty(fxTrades)) {
            return Collections.emptyList();
        }
        buildInsufficientSplitOrders(staff, fxTrades, splitData);
        if (CollectionUtils.isEmpty(splitData.buildGxtrades)) {
            return Collections.emptyList();
        }
        List<Trade> trades = splitFxInsuficientNew(staff, fxStaff, splitData);
        logger.debug(LogHelper.buildLog(staff, String.format("splitFxTrade=%s", TradeUtils.toSidSet(trades))));
        return trades;
    }

    @Deprecated
    public List<Trade> splitFxInsuficient(Staff staff, Staff fxStaff, SplitGxInsufficientData splitData) {
        List<Trade> fxTrades = tradeSearchService.queryBySids(fxStaff, true, TradeUtils.toTidList(splitData.buildGxtrades).stream().map(Long::parseLong).toArray(Long[]::new));
        fxTrades.forEach(trade -> {
            List<Trade> gxTrade = splitData.buildGxtrades.stream().filter(gxsplitTrade -> gxsplitTrade.getTid().equals(trade.getSid().toString())).collect(Collectors.toList());
            //不为空并且数量只可能是1
            if (CollectionUtils.isEmpty(gxTrade) || gxTrade.size() > 1) {
                logger.debug(LogHelper.buildLog(staff, String.format("分销拆分数据出错,gxTrade=%s", trade.getSid())));
                return;
            }
            List<Order> fxOrders = TradeUtils.getOrders4Trade(trade);
            Trade destTrade = gxTrade.get(0);
            TbTrade fxTbTrade = buildFxSplitData(staff, trade, fxOrders, TradeUtils.getOrders4Trade(destTrade), splitData);
            if (fxTbTrade == null) {
                logger.debug(LogHelper.buildLog(staff, String.format("分销拆分数据出错,分销订单为空,fxSid=%s", trade.getSid())));
                return;
            }
            SplitResult splitResult = tradeSplitService.splitMix(fxStaff, fxTbTrade, fxOrders.size() + "", false);
            logger.debug(LogHelper.buildLog(staff, String.format("分销订单:%s,拆分结果,splitResult=%s,splitData=%s", trade.getSid(), JSON.toJSONString(splitResult), JSON.toJSONString(splitData))));
            findNeedAudit(fxStaff, splitResult, splitData);
            splitData.splitFxSid.addAll(splitResult.getUpdateSids());
            splitData.splitFxSid.addAll(splitResult.getInsertSids());
            splitData.splitSuccFxTrades.add(trade);
        });
        buildTradeLog(staff, fxStaff, splitData);
        return splitData.splitSuccFxTrades;

    }

    private void findNeedAudit(Staff fxStaff, SplitResult splitResult, SplitGxInsufficientData splitData) {
        if (CollectionUtils.isNotEmpty(splitResult.getMergeUndoSids())) {
            //如果拆单是操作的取消合单
            List<Trade> splitFxTrades = tradeSearchService.queryBySids(fxStaff, true, splitResult.getUpdateSids().toArray(new Long[0]));
            Map<Long, Trade> longTradeMap = TradeUtils.toMapBySid(splitFxTrades);
            longTradeMap.forEach((k, v) -> {
                List<Order> orders4Trade = TradeUtils.getOrders4Trade(v);
                boolean isEqual = true;
                for (Order order : orders4Trade) {
                    Integer splitNum = splitData.splitMap.get(order.getId());
                    if (splitNum == null || !splitNum.equals(order.getNum())) {
                        //不存在
                        isEqual = false;
                        break;
                    }
                }
                if (isEqual) {
                    splitData.neendAuditFxTrades.add(v.getSid());
                }
            });
        } else {
            if (CollectionUtils.isNotEmpty(splitResult.getUpdateSids())) {
                splitData.neendAuditFxTrades.addAll(splitResult.getUpdateSids());
            }
        }


        if (CollectionUtils.isNotEmpty(splitResult.getInsertSids())) {
            splitData.noNeendAuditFxTrades.addAll(splitResult.getInsertSids());
        }
        logger.debug(LogHelper.buildLog(fxStaff, String.format("拆分结果,splitData=%s", JSON.toJSONString(splitData))));

    }

    private void buildTradeLog(Staff staff, Staff fxStaff, SplitGxInsufficientData splitData) {
        Set<Long> noNeendAuditFxTrades = splitData.noNeendAuditFxTrades;
        if (CollectionUtils.isEmpty(noNeendAuditFxTrades)) {
            return;
        }
        List<Trade> fxTrades = tradeSearchService.queryBySids(fxStaff, true, noNeendAuditFxTrades.toArray(new Long[0]));
        Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(fxTrades);
        List<Trade> updateTrades = new ArrayList<>();
        for (Long noNeendAuditFxTrade : noNeendAuditFxTrades) {
            if (tradeMap.containsKey(noNeendAuditFxTrade)) {
                Trade trade = tradeMap.get(noNeendAuditFxTrade);
                Trade tbTrade = TradeBuilderUtils.builderUpdateTrade(trade);
                TradeExceptUtils.updateExcept(staff,tbTrade,ExceptEnum.FX_REPULSE,1L);
                tbTrade.setIsExcep(1);
                tbTrade.setSid(trade.getSid());
                StringBuilder sb;
                if (StringUtils.isNotBlank(trade.getExceptMemo())) {
                    sb = new StringBuilder(trade.getExceptMemo()).append(";").append("供销缺货");
                } else {
                    sb = new StringBuilder("供销缺货");
                }
                tbTrade.setExceptMemo(sb.toString());
                tbTrade.getOperations().put(OpEnum.GX_INSUFFICIENT_SPLIT, "供销缺货拆分,分销无货子单自动标记供销商打回异常");
                updateTrades.add(tbTrade);
            }
        }
        if (CollectionUtils.isNotEmpty(updateTrades)) {
            logger.debug(LogHelper.buildLog(staff, String.format("updatesTrades=%s", TradeUtils.toSidList(updateTrades))));
            tradeTraceBusiness.asyncTrace(fxStaff, updateTrades, OpEnum.GX_INSUFFICIENT_SPLIT);
            tradeUpdateService.updateTrades(fxStaff, updateTrades);
        }
    }

    private <R extends TradeBatchRequest> List<Trade> searchFxTrade(Staff fxStaff, List<Trade> gxGroupTrades, R request) {
        List<Trade> tradeList = tradeSearchService.queryBySids(fxStaff, true, TradeUtils.toTidList(gxGroupTrades).stream().map(Long::parseLong).toArray(Long[]::new));
        if (CollectionUtils.isEmpty(tradeList)) {
            return tradeList;
        }
        Map<String, Trade> gxTidMap = TradeUtils.toMapByTid(gxGroupTrades);
        List<Trade> fxTrade = new ArrayList<>();
        for (Trade trade : tradeList) {
            boolean ifNotAllowSplit = Optional.ofNullable(gxTidMap.get(String.valueOf(trade.getSid())))
                    .map(t->TradeUtils.ifContainV(t, TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX))
                    .orElse(false);
            if(ifNotAllowSplit){
                request.getProgress().getErrorMsg().add("该订单执行了商品搭配规则/套件转单品，暂不支持拆单！");
                request.getProgress().getMsg().add(trade.getTid());
                request.getProgress().setErrorNum(request.getProgress().getErrorNum() + 1);
            } else if (TradeUtils.isFxTrade(trade)) {
                fxTrade.add(trade);
            } else {
                request.getProgress().getErrorMsg().add("多级推送的供销订单不支持供销缺货拆分，请联系上游分销商处理！");
                request.getProgress().getMsg().add(trade.getTid());
                request.getProgress().setErrorNum(request.getProgress().getErrorNum() + 1);
            }
        }
        return fxTrade;
    }


    private TbTrade buildFxSplitData(Staff staff, Trade fxTrade, List<Order> fxOrders, List<Order> gxOrders, SplitGxInsufficientData splitData) {
        TbTrade tbTrade = new TbTrade();
        tbTrade.setSid(fxTrade.getSid());
        List<Order> updateOrders = Lists.newArrayListWithCapacity(gxOrders.size());
        Map<Long, Order> gxOidMap = gxOrders.stream().collect(Collectors.toMap(Order::getOid, order -> order));

        for (Order fxOrder : fxOrders) {
            Order order = new TbOrder();
            order.setId(fxOrder.getId());
            order.setNum(fxOrder.getNum());
            order.setSid(fxTrade.getSid());
            if (gxOidMap.containsKey(fxOrder.getId())) {
                Order gxOrder = gxOidMap.get(fxOrder.getId());
                order.setSplitNum(gxOrder.getSplitNum());
            } else {
                order.setSplitNum(0);
            }
            updateOrders.add(order);
        }

        if (CollectionUtils.isEmpty(updateOrders)) {
            return null;
        }
        TradeUtils.setOrders(tbTrade, updateOrders);
        logger.debug(LogHelper.buildLog(staff, String.format("buildFxSplitData_tbTrade=%s", JSON.toJSONString(tbTrade))));
        return tbTrade;
    }


    /**
     * 构建缺货的供销数据
     */
    private void buildInsufficientSplitOrders(Staff staff, List<Trade> fxTrades, SplitGxInsufficientData splitData) {
        List<Trade> successUnAuditGxTrades = splitData.originGxTrades.stream().filter(trade -> TradeUtils.toSidList(fxTrades).contains(Long.parseLong(trade.getTid()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(successUnAuditGxTrades)) {
            return;
        }
        Map<Long, Integer> splitMap = Maps.newHashMap();
        successUnAuditGxTrades.forEach(trade -> {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                if (order.getStockNum() == null) {
                    order.setStockNum(0);
                }
                //非待审核 || 非缺货 过滤
                if (!SplitInsufficientUtils.isNotInsufficient(staff,order) && order.getStockNum() < order.getNum()) {
                    order.setSplitNum(order.getNum() - order.getStockNum());
                    if (order.getStockNum() != 0) {
                        splitMap.put(order.getOid(), order.getSplitNum());
                    }
                } else {
                    order.setSplitNum(0);
                    splitMap.put(order.getOid(), order.getNum());
                }
            }
        });
        logger.debug(LogHelper.buildLog(staff, String.format("buildInsufficientSplitOrders=%s", JSON.toJSONString(TradeUtils.getOrders4Trade(successUnAuditGxTrades)))));
        splitData.buildGxtrades.addAll(successUnAuditGxTrades);
        splitData.splitMap.putAll(splitMap);
    }


    /**
     * 校验订单，错误的记录错误数量
     *
     * @param trades
     * @param request
     * @param <R>
     * @return
     */
    public static <R extends TradeBatchRequest> List<Trade> checkTrades(Staff staff,List<Trade> trades, R request) {
        boolean dmsTradeGxEditNotRelFx = TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen();
        List<Trade> successTrades = Lists.newArrayListWithCapacity(trades.size());
        for (Trade trade : trades) {
            if (TradeUtils.isGxTrade(trade)) {
                if (TradeUtils.ifContainV(trade, TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX) && !dmsTradeGxEditNotRelFx) {
                    request.getProgress().getMsg().add("执行过套转单/商品搭配，不支持拆分。如需要拆分请开启配置「允许供销订单与分销订单的商品信息和订单信息不一致」");
                    request.getProgress().setErrorNum(request.getProgress().getErrorNum() + 1);
                    continue;
                }
                trade.setCancelFrom(3);
                successTrades.add(trade);
            } else {
                request.getProgress().getMsg().add(trade.getTid());
                request.getProgress().setErrorNum(request.getProgress().getErrorNum() + 1);
            }
        }
        return successTrades;
    }


    @Override
    protected int getTouchPoolSize() {
        return Integer.MAX_VALUE;
    }


    /**
     * 供销单缺货拆分慢优化
     *
     * @param staff
     * @param fxStaff
     * @param splitData
     * @return
     */
    public List<Trade> splitFxInsuficientNew(Staff staff, Staff fxStaff, SplitGxInsufficientData splitData) {
        List<Trade> fxTrades = tradeSearchService.queryBySids(fxStaff, true, TradeUtils.toTidList(splitData.buildGxtrades).stream().map(Long::parseLong).toArray(Long[]::new));
        Map<String, List<Trade>> gxtradesMap = splitData.buildGxtrades.stream().collect(Collectors.groupingBy(Trade::getTid));
        Map<Trade, String> tradeGroupCount = new HashMap<>();
        StringBuilder sb = new StringBuilder();
        for (Trade trade : fxTrades) {
            List<Trade> gxTrade = gxtradesMap.get(trade.getSid().toString());
            //不为空并且数量只可能是1
            if (CollectionUtils.isEmpty(gxTrade) || gxTrade.size() > 1) {
                sb.append(String.format("分销拆分数据出错,gxTrade=%s", trade.getSid()));
                continue;
            }
            List<Order> fxOrders = TradeUtils.getOrders4Trade(trade);
            Trade destTrade = gxTrade.get(0);
            TbTrade fxTbTrade = buildFxSplitData(staff, trade, fxOrders, TradeUtils.getOrders4Trade(destTrade), splitData);
            if (fxTbTrade == null) {
                sb.append(String.format("分销拆分数据出错,分销订单为空,fxSid=%s", trade.getSid()));
                continue;
            }
            tradeGroupCount.put(fxTbTrade, String.valueOf(fxOrders.size()));
        }
        if (sb.length() > 0) {
            logger.debug(LogHelper.buildLog(staff, sb.toString()));
        }
        if (tradeGroupCount.size() == 0) {
            logger.debug(LogHelper.buildLog(staff, "需要拆分的分销单，供销单为空!"));
            return Lists.newArrayList();
        }
        SplitResult splitResult = tradeSplitService.splitMixBatch(fxStaff, tradeGroupCount, false);
        Set<Long> collect = tradeGroupCount.keySet().stream().map(Trade::getSid).collect(Collectors.toSet());
        logger.debug(LogHelper.buildLog(staff, String.format("分销订单:%s,拆分结果,splitResult=%s,splitData=%s", collect, JSON.toJSONString(splitResult), JSON.toJSONString(splitData))));
        findNeedAudit(fxStaff, splitResult, splitData);
        splitData.splitFxSid.addAll(splitResult.getUpdateSids());
        splitData.splitFxSid.addAll(splitResult.getInsertSids());
        List<Trade> splitSuccFxTrades = fxTrades.stream().filter(trade -> Arrays.stream(splitResult.getResultSids()).collect(Collectors.toList()).contains(trade.getSid())).collect(Collectors.toList());
        buildTradeLog(staff, fxStaff, splitData);
        splitData.splitSuccFxTrades.addAll(splitSuccFxTrades);
        return splitData.splitSuccFxTrades;
    }
}

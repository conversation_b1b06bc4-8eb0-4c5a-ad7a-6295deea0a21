package com.raycloud.dmj.business.operate;

import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.common.CipherTextUtils;
import com.raycloud.dmj.business.common.SecretBusiness;
import com.raycloud.dmj.business.merge.support.MergeKeyUtils;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeExt;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.utils.diamond.TradeMergeConfigUtils;
import com.raycloud.dmj.services.platform.basis.PlatformManagement;
import com.raycloud.dmj.services.platform.trades.IPlatformTradeDecryptProvider;
import com.raycloud.dmj.services.trade.merge.MergeContextBusiness;
import com.raycloud.dmj.services.trade.merge.TradeMergeGroup;
import com.raycloud.dmj.services.trade.merge.platform.MergePlatformPdd;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.SecretUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import com.raycloud.dmj.domain.trades.utils.TradeConsolidateInfoUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class AddressAnalyzeBusiness {


    @Resource
    SecretBusiness secretBusiness;

    private final Logger logger = Logger.getLogger(this.getClass());

    public static final List<String> NEED_QUERY_MERGE_PLATFORM = Lists.newArrayList(CommonConstants.PLAT_FORM_TYPE_TAO_BAO, CommonConstants.PLAT_FORM_TYPE_TIAN_MAO, CommonConstants.PLAT_FORM_TYPE_FX, CommonConstants.PLAT_FORM_TYPE_1688, CommonConstants.PLAT_FORM_TYPE_1688_C2M, CommonConstants.PLAT_FORM_TYPE_FXG, CommonConstants.PLAT_FORM_TYPE_WXSPH);
    public final static List<String> TRADE_EXT_SOURCE = new ArrayList<>();
    static {
        TRADE_EXT_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_PDD);
        TRADE_EXT_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU);
        TRADE_EXT_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_XHS);
        TRADE_EXT_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_WD);
        TRADE_EXT_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_KUAISHOU_DF);
        TRADE_EXT_SOURCE.add(CommonConstants.PLAT_FORM_TYPE_FXG_GX);
    }

    @Resource
    PlatformManagement platformManagement;

    @Resource
    MergeContextBusiness mergeContextBusiness;

    /**
     * 这里等同于 NEED_QUERY_MERGE_PLATFORM 但是目前第一版分销平台先不纳入 后面如果放开分销平台，这里需要修改合并一个集合就行
     */
    public static final List<String> QUERY_MERGE_PLATFORM = Lists.newArrayList(CommonConstants.PLAT_FORM_TYPE_TAO_BAO, CommonConstants.PLAT_FORM_TYPE_TIAN_MAO,CommonConstants.PLAT_FORM_TYPE_FXG);


    public Map<String, List<Trade>> groupBySameAddress(Staff staff, List<Trade> trades){
        return groupBySameAddress(staff, trades, true);
    }

    /**
     * 子母单对订单进行分组
     * @param staff 员工信息
     * @param trades 待分组的订单列表
     * @param needDecodeTrade 是否需要解密订单
     * @return 分组后的订单Map，key为分组标识，value为该分组的订单列表
     */
    public Map<String, List<Trade>> groupBySameAddress(Staff staff, List<Trade> trades, boolean needDecodeTrade) {
        if (needDecodeTrade) {
            secretBusiness.decodeTrades(staff, trades);
        }

        Map<String, List<Trade>> groupMap = new HashMap<>();
        Map<Long, List<Trade>> pddTradeMap = new HashMap<>();
        Map<String, Map<String, List<Trade>>> desensitizationGroupMap = new HashMap<>();
        List<Trade> pddConsolidatedTrades = new ArrayList<>();
        AtomicInteger pddConsolidated = new AtomicInteger(0);

        // 1. 初步分组
        for (Trade trade : trades) {
            String groupKey = tradeGroup(staff, trade, pddConsolidated,
                    desensitizationGroupMap, pddConsolidatedTrades, pddTradeMap);
            if (StringUtils.isNotBlank(groupKey)) {
                groupMap.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(trade);
            }
        }

        // 拼多多订单分组
        pddTradeGroup(staff, pddTradeMap, groupMap);

        // 3. 处理其他平台订单分组
        if (!desensitizationGroupMap.isEmpty()) {
            Map<String, List<Trade>> mergeGroupMap = mergeGroupDesensitization(staff, desensitizationGroupMap);
            groupMap.putAll(mergeGroupMap);
            pddConsolidatedTrades.forEach(trade -> trade.setAddressMd5(""));
        }

        return groupMap;
    }

    /**
     * 订单分组
     * @return 分组键，如果返回null则表示该订单已被处理
     */
    private String tradeGroup(Staff staff, Trade trade, AtomicInteger pddConsolidated,
                                        Map<String, Map<String, List<Trade>>> desensitizationGroupMap,
                                        List<Trade> pddConsolidatedTrades,
                                        Map<Long, List<Trade>> pddTradeMap) {

        //处理需要平台查询的订单
        if (TradeUtils.platformContain(staff, trade, QUERY_MERGE_PLATFORM) &&
                StringUtils.isNotBlank(trade.getAddressMd5())) {
            String groupKey = initGroupKey(trade);
            desensitizationGroupMap.computeIfAbsent(groupKey, k -> new HashMap<>())
                    .computeIfAbsent(trade.getAddressMd5(), k -> new ArrayList<>())
                    .add(trade);
            return null;
        }

        //处理需要查询合并的订单
        if (TradeUtils.platformContain(staff, trade, NEED_QUERY_MERGE_PLATFORM) &&
                StringUtils.isNotBlank(trade.getAddressMd5())) {
            return "addressMd5_" + trade.getAddressMd5();
        }

        //获取原始source
        String tradeSource = CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) ?
                TradeUtils.getOrgiPlatform(staff, trade) : trade.getSource();

        //处理拼多多订单
        if (CommonConstants.PLAT_FORM_TYPE_PDD.equals(tradeSource)) {
            processPddTrade(staff, trade, pddConsolidated, desensitizationGroupMap,
                    pddConsolidatedTrades, pddTradeMap);
            return null;
        }

        //处理其他有共性平台订单
        String key = commonKey(staff, trade, tradeSource);
        //处理跨店订单
        key = handleCrossStoreTrade(staff, trade, tradeSource, key);
        //构建最终分组键
        key = trade.getUserId() + key;
        return StringUtils.isNotBlank(trade.getSubSource()) ?
                trade.getSubSource() + key : trade.getSource() + key;
    }

    /**
     * 拼多多订单分组
     */
    private void pddTradeGroup(Staff staff, Map<Long, List<Trade>> pddTradeMap,
                                         Map<String, List<Trade>> groupMap) {
        if (pddTradeMap.isEmpty()) {
            return;
        }

        pddTradeMap.forEach((userId, pddTrades) -> {
            // 1. 处理可合并的订单
            Map<String, List<Trade>> groupKeyTrades = MergePlatformPdd.group(pddTrades);
            if (!groupKeyTrades.isEmpty()) {
                groupKeyTrades.entrySet().stream()
                        .filter(entry -> entry.getValue().size() > 1)
                        .forEach(entry -> {
                            groupMap.put(entry.getKey() + userId, entry.getValue());
                            pddTrades.removeAll(entry.getValue());
                        });
            }

            // 2. 处理需要平台查询的订单
            if (pddTrades.size() > 1) {
                Map<String, Map<String, List<Trade>>> platformGroupMap = new HashMap<>();
                pddTrades.forEach(trade -> {
                    String groupKey = initGroupKey(trade);
                    platformGroupMap.computeIfAbsent(groupKey, k -> new HashMap<>())
                            .computeIfAbsent(trade.getAddressMd5(), k -> new ArrayList<>())
                            .add(trade);
                });

                Map<String, List<Trade>> mergeResult = mergeGroupDesensitization(staff, platformGroupMap);
                groupMap.putAll(mergeResult);
            }
        });
    }

    /**
     * 处理拼多多订单
     */
    private void processPddTrade(Staff staff, Trade trade, AtomicInteger pddConsolidated,
                                 Map<String, Map<String, List<Trade>>> desensitizationGroupMap,
                                 List<Trade> pddConsolidatedTrades,
                                 Map<Long, List<Trade>> pddTradeMap) {
        if (TradeConsolidateInfoUtils.isPddConsolidatedTrade(trade) ||
                StringUtils.isBlank(trade.getBuyerNick())) {
            trade.setAddressMd5(String.valueOf(pddConsolidated.getAndIncrement()));
            String groupKey = initGroupKey(trade);
            desensitizationGroupMap.computeIfAbsent(groupKey, k -> new HashMap<>())
                    .computeIfAbsent(trade.getAddressMd5(), k -> new ArrayList<>())
                    .add(trade);
            pddConsolidatedTrades.add(trade);
        } else {
            MergePlatformPdd.setTradeMerge(staff, trade);
            pddTradeMap.computeIfAbsent(trade.getUserId(), k -> new ArrayList<>()).add(trade);
        }
    }

    /**
     * 处理跨店订单
     */
    private String handleCrossStoreTrade(Staff staff, Trade trade, String tradeSource, String key) {
        if(CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())
                && StringUtils.isNotBlank(trade.getSubSource())
                && !Objects.equals(tradeSource, trade.getSubSource())){
            String key2 = commonKey(staff, trade, trade.getSubSource());
            if(Objects.equals(key, key2)) {
                key = String.format("【userSource=%s】%s【subSource=%s】%s",
                        tradeSource, key, trade.getSubSource(), key2);
            }
        }
        return key;
    }

    /**
     *
     * @param trade
     * @return
     */
    public String commonKey(Staff staff,Trade trade, String tradeSource) {
        String key;
        TradeExt tradeExt = trade.getTradeExt();
        if(CommonConstants.PLAT_FORM_TYPE_PDD.equals(tradeSource)){
            if(TradeConsolidateInfoUtils.isPddConsolidatedTrade(trade) || StringUtils.isBlank(trade.getBuyerNick())){//集运单情况
                key = trade.getBuyerNick() + concat(buildDetailAddress(trade) ,trade.getReceiverName(),trade.getReceiverPhone(), trade.getReceiverMobile());
            } else {//正常订单 不能判断md5  pdd的buyerNick  已经是通过平台的密文名称、电话、地址拼成而得， 相同明文 隔天md5不一样
                key = trade.getBuyerNick() ;
            }
            //理论上没有了 分销跨店不是很清楚所以加个日志
            logger.warn(String.format("commonKeyPdd订单异常进入，tid:%s", trade.getTid()));
        }else if (tradeExt != null && (TRADE_EXT_SOURCE.contains(tradeSource) || TRADE_EXT_SOURCE.contains(trade.getSplitSource()))) {
            key = commonExtKey(tradeExt);
        } else if (CommonConstants.PLAT_FORM_TYPE_JD.equalsIgnoreCase(tradeSource)) {
            boolean isEncryptData = SecretUtils.isJdEncrypt(trade.getReceiverName()) || SecretUtils.isJdEncrypt(trade.getReceiverPhone()) || SecretUtils.isJdEncrypt(trade.getReceiverMobile());
            key = isEncryptData && StringUtils.isNotBlank(trade.getAddressMd5()) ? trade.getAddressMd5() :
                    concat(trade.getReceiverName(), trade.getReceiverPhone(),trade.getReceiverMobile());
        } else if ((CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(tradeSource) || CommonConstants.PLAT_FORM_TYPE_1688.equals(tradeSource)) && StringUtils.isNotBlank(trade.getAddressMd5())) {
            key = build1688C2mKey(trade);
        } else if (CommonConstants.PLAT_FORM_TYPE_SUMAITONG.equals(tradeSource) && StringUtils.isNotBlank(trade.getAddressMd5())) {
            key = trade.getAddressMd5();
        } else if ((CommonConstants.PLAT_FORM_TYPE_FXG.equals(tradeSource) || CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(tradeSource) || CommonConstants.PLAT_FORM_TYPE_XHS.equals(tradeSource)) && CipherTextUtils.ifEncrypt(trade)) {
            key = CipherTextUtils.cutOutIndex(trade.getReceiverName()) + CipherTextUtils.cutOutIndex(trade.getReceiverMobile()) + CipherTextUtils.cutOutIndex(trade.getReceiverAddress());
        } else if (MergeKeyUtils.isNewWxSphMergeKey(trade)) {
            // 微信视频号
            key = buildWxSphKey(trade);
        } else if (CommonConstants.PLAT_FORM_TYPE_JD_VC.equalsIgnoreCase(tradeSource)){
            // 对于京东自营平台，自动合单时不校验手机号
            key  = buildKeyNoPhone(trade);
        } else {
            key = concat(buildDetailAddress(trade) ,trade.getReceiverName(),trade.getReceiverPhone(), trade.getReceiverMobile());
        }

        if(StringUtils.isBlank(key)){
            key = trade.getUserId() + trade.getTid();
            logger.warn(String.format("子母单得到key为空，存在数据异常情况，使用tid作为key兜底，以免误合并，tid:%s", trade.getTid()));
        }
        return key;
    }

    private String initGroupKey(Trade trade) {
        return trade.getUserId() + "_"
                + StringUtils.trimToEmpty(trade.getBuyerNick())
                + StringUtils.trimToEmpty(trade.getReceiverState())
                + StringUtils.trimToEmpty(trade.getReceiverCity())
                + StringUtils.trimToEmpty(trade.getReceiverDistrict())
                + StringUtils.trimToEmpty(trade.getReceiverStreet());
    }

    /**
     * 需要特殊处理的平台（通过平台接口判断是否能合并）
     */
    private Map<String, List<Trade>> mergeGroupDesensitization(Staff staff, Map<String, Map<String, List<Trade>>> groupKeyTradesMap) {
        if (groupKeyTradesMap == null || groupKeyTradesMap.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, List<Trade>> groupKeyTrades = new HashMap<>();
        Map<String, List<Trade>> needMergeQuery = new HashMap<>();
        List<Long> needMergeQuerySids = new ArrayList<>();

        for (Map.Entry<String, Map<String, List<Trade>>> entry0 : groupKeyTradesMap.entrySet()) {
            String groupKey0 = entry0.getKey();
            Map<String, List<Trade>> addressMd5TradesMap = entry0.getValue();

            for (Map.Entry<String, List<Trade>> entry1 : addressMd5TradesMap.entrySet()) {
                List<Trade> trades = entry1.getValue();
                //shopId+buyerNick+省市区详细地址 初步分组如果都只有一个，那么也就不需要请求平台了
                if (addressMd5TradesMap.size() == 1){
                    groupKeyTrades.put(groupKey0 + entry1.getKey(), trades);
                    break;
                }
                //shopId+buyerNick+省市区详细地址一样 但是addressMd5不一样的那就可以一批请求平台
                for (Trade trade : trades) {
                    needMergeQuery.computeIfAbsent(groupKey0, k -> new ArrayList<>()).add(trade);
                    needMergeQuerySids.add(trade.getSid());
                }
            }
        }

        if (!needMergeQuery.isEmpty()) {
            long start = System.currentTimeMillis();
            User user = null;
            int ct = 0;
            for (Map.Entry<String, List<Trade>> entry : needMergeQuery.entrySet()) {
                List<Trade> trades = entry.getValue();
                try {
                    if (trades.size() == 1) {
                        groupKeyTrades.computeIfAbsent(entry.getKey()+trades.get(0).getAddressMd5(), k -> new ArrayList<>()).add(trades.get(0));
                        continue;
                    }

                    user = mergeContextBusiness.getUser(staff, trades.get(0).getUserId());

                    Map<Long, String> sidOldTidMap = TradeMergeGroup.resetTid(staff, trades);
                    List<List<String>> tidsList = platformManagement.getAccess(user.getSource(), IPlatformTradeDecryptProvider.class).queryMerge(user, TradeMergeGroup.getQueryTrade(staff, trades));
                    Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("平台返回可合单数据，source=%s,tidsList=%s", user.getSource(), tidsList)));
                    ct++;
                    if (CollectionUtils.isEmpty(tidsList)) {
                        TradeMergeGroup.recoverTid(staff, trades, sidOldTidMap);

                        for (Trade trade : trades) {
                            groupKeyTrades.computeIfAbsent(entry.getKey()+trade.getAddressMd5(), k -> new ArrayList<>()).add(trade);
                        }
                        continue;
                    }
                    Map<String, List<Trade>> tidTradesMap = new HashMap<>();
                    //同一个tid的分一组对于平台来说，就是同一个订单 单独拆出来改了地址的这里大key就不会分组在一起
                    trades.forEach(trade -> tidTradesMap.computeIfAbsent(trade.getTid(), k -> new ArrayList<>()).add(trade));
                    TradeMergeGroup.recoverTid(staff, trades, sidOldTidMap);
                    int index = 0;
                    for (List<String> tids : tidsList) {
                        if (CollectionUtils.isEmpty(tids)) {
                            continue;
                        }

                        Set<String> tidSet = new HashSet<>(tids);
                        for (String tid : tidSet) {
                            //子母单这里如果是同一个tid下面的拆分或者手工单，在上面通过buyerNick+省市区详细地址分组过是一起的，也就是同店铺+收件地址一样，则合并
                            groupKeyTrades.computeIfAbsent(entry.getKey() + "_oaid_" + index, k -> new ArrayList<>()).addAll(tidTradesMap.get(tid));
                        }
                        index++;
                    }
                } catch (Exception e) {
                    //平台处理失败了，则不能合并
                    for (Trade trade : trades) {
                        groupKeyTrades.computeIfAbsent(entry.getKey()+trade.getAddressMd5(), k -> new ArrayList<>()).add(trade);
                    }
                    Logs.error(LogHelper.buildLog(staff, String.format("调用平台合单接口出错，userId=%s,sids=%s", user == null ? "null" : user.getId(), TradeUtils.toSidList(entry.getValue()))), e);
                }
            }

            Logs.debug(LogHelper.buildLog(staff, String.format("调用平台queryMerge，次数=%s,总订单数=%s",ct,needMergeQuerySids.size())));
            long end = System.currentTimeMillis();

            if (end - start > 3000) {
                Logs.warn(LogHelper.buildLog(staff, String.format("慢业务，调用平台合单接口慢，took= %s ms, 次数=%s, 总订单数=%s", (end - start),ct,needMergeQuerySids.size())));
            }
        }

        return groupKeyTrades;
    }

    /**
     * 省市区 详细地址
     * @param trade
     * @return
     */
    private String buildDetailAddress(Trade trade){
        return normalConcat(buildDistrAddress(trade), trade.getReceiverAddress());
    }

    /**
     * 省市区地址
     * @param trade
     * @return
     */
    private String buildDistrAddress(Trade trade){
        return normalConcat(trade.getReceiverState(), trade.getReceiverCity(), trade.getReceiverDistrict()) ;
    }

    /**
     *  省市区详细地址 收件人名称 不带手机号的Key   （手机号可能是虚拟号）
     *
     *  优化内容：对于京东自营平台，自动合单时不校验手机号
     *  https://tb.raycloud.com/task/63e9be66537f19001e08e89a
     * @param trade
     * @return
     */
    private String buildKeyNoPhone(Trade trade) {
        return concat(buildDetailAddress(trade), trade.getReceiverName());
    }

    private String build1688C2mKey(Trade trade) {
        return trade.getAddressMd5().split("-")[0];
    }

    /**
     * 微信视频号
     * 刘亮亮：buyerNick我塞了openId 这个不会有**  然后判断真正能不能合是  openId  + 脱敏手机号 + 收件地址
     * 脱敏手机号存在trade的receiverPhone字段，   收件地址在合单判断时要去掉最后的 虚拟号尾巴 （如需联系用户，请转xxx）
     */
    private String buildWxSphKey(Trade trade) {
        String mergeReceiverAddress = null;
        String KeyRegex = TradeMergeConfigUtils.getWxSphMergeKeyRegex();
        if (StringUtils.isNotEmpty(KeyRegex)) {
            mergeReceiverAddress = trade.getReceiverAddress().split(KeyRegex)[0].trim();
        } else {
            mergeReceiverAddress = trade.getReceiverAddress();
        }

        return concat(trade.getReceiverPhone(), buildDistrAddress(trade), mergeReceiverAddress);
    }


    String commonExtKey(TradeExt tradeExt) {
        return concat(tradeExt.getReceiverNameIndex() , tradeExt.getReceiverMobileIndex() , tradeExt.getReceiverAddressIndex());
    }

    String concat(String... args){
        StringBuilder sb = new StringBuilder();
        for (String arg: args){
            String s = StringUtils.stripToEmpty(arg);
            if(StringUtils.isNotBlank(s)) {
                sb.append(s).append("__##__");
            }
        }
        return sb.toString();
    }

    String normalConcat(String... args){
        StringBuilder sb = new StringBuilder();
        for (String arg: args){
            String s = StringUtils.stripToEmpty(arg);
            if(StringUtils.isNotBlank(s)) {
                sb.append(s).append("@@");
            }
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        AddressAnalyzeBusiness business = new AddressAnalyzeBusiness();
        String concat = business.concat(null, null);
        System.out.println(concat);
        System.out.println("-------");

    }
}

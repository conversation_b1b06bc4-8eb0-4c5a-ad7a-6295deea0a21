package com.raycloud.dmj.business.item.replace;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.ThreadPoolBusiness;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.item.payment.ExchangeRelUtils;
import com.raycloud.dmj.business.item.replace.support.ReplaceItemUtils;
import com.raycloud.dmj.business.modify.ModifyParentBusiness;
import com.raycloud.dmj.business.payment.TradeItemExchangePaymentBusinessService;
import com.raycloud.dmj.business.rematch.business.ReMatchBusiness;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.item.params.QueryItemDetailParams;
import com.raycloud.dmj.domain.payment.*;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.rematch.enums.EventEnum;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.params.ReplaceNullityItemParams;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.item.TradeItemContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.*;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Component
public class TradeReplaceNullityItemBusiness extends ThreadPoolBusiness<Trade> {

    public static final String EVENT_NAME = "trade.item.replace.nullity.item";

    @Resource
    private IProgressService progressService;
    @Resource
    private TbOrderDAO tbOrderDAO;
    @Resource
    private ReMatchBusiness reMatchBusiness;
    @Resource
    private IdWorkerService idWorkerService;
    @Resource
    private ModifyParentBusiness modifyParentBusiness;
    @Resource
    private IOrderStockService orderStockService;
    @Resource
    private ITradeOrderSyncItemTag tradeOrderSyncItemTagFill;
    @Resource
    private ITradeUpdateService tradeUpdateService;
    @Resource
    private IItemServiceDubbo itemServiceDubboImpl;
    @Resource
    private ITradeConfigService tradeConfigService;
    @Resource
    private TradeReplaceNullityItemBusiness tradeReplaceNullityItemBusiness;
    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    private ILockService lockService;

    @Resource
    private TradeTraceBusiness tradeTraceBusiness;

    @Resource
    TradeSysLabelBusiness tradeTagUpdateBusiness;

    @Resource
    TradeItemExchangePaymentBusinessService tradeItemExchangePaymentBusinessService;

    protected OrderCopier<Order, Order> orderCopier = new OrderCopier<>();

    public void replaceNullityItem(Staff staff, ReplaceNullityItemParams params, ProgressData progressData) {
        if (Objects.isNull(params) || ObjectUtils.isEmpty(params.getOriginItems()) || ObjectUtils.isEmpty(params.getReplaceItems())) {
            progressData.getErrorMsg().add("{\"" + 0L + "\":\"参数异常\"}");
            return;
        }
        //填充上下文
        ReplaceNullityItemContext context = fillContext(staff, params, progressData);
        if (Objects.isNull(context)) {
            return;
        }
        run(staff, context.originTrade, new AbstractBusiness() {
            @Override
            public void doBusiness(List<Trade> data) {
                //替换商品
                try {
                    lockService.locks(TradeLockBusiness.trades2ERPLocks(staff, data), () -> {
                        List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, TradeUtils.toSids(data));
                        if (CollectionUtils.isEmpty(originTrades)) {
                            progressData.getErrorMsg().add("{\"" + 0L + "\":\"订单中不包含待审核且商品未匹配异常的平台商品\"}");
                            return null;
                        }
                        Map<Long, List<Trade>> groupByMergeSid = new HashMap<>();
                        originTrades.forEach(trade -> {
                            if (TradeUtils.isMerge(trade)) {
                                groupByMergeSid.computeIfAbsent(trade.getMergeSid(), v -> new ArrayList<>()).add(trade);
                                return;
                            }
                            groupByMergeSid.put(trade.getSid(), Collections.singletonList(trade));
                        });
                        List<List<Trade>> filterLists = groupByMergeSid.values().stream().filter(tradeList -> check(tradeList, progressData)).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(filterLists)) {
                            return null;
                        }
                        replaceItem(staff, context, filterLists);
                        progressData.getAtomicSucNum().addAndGet(filterLists.size());
                        return null;
                    });
                } catch (Exception e) {
                    progressData.getAtomicErrorNum().addAndGet(data.size());
                    progressData.getErrorMsg().add("{\"" + data.size() + "\":\"无效商品替换商品失败\"}");
                    Logs.error(LogHelper.buildLog(staff, "无效商品替换商品失败"), e);
                } finally {
                    progressService.updateProgress(staff, ProgressEnum.PROGRESS_BATCH_CHANGE_ITEMS, progressData);
                }
            }
        });
    }

    @Override
    protected List<List<Trade>> groupData(Staff staff, List<Trade> data) {
        return ListUtils.splitList(data, 200);
    }

    private Map<String, DmjItem> getReplaceItems(Staff staff, ReplaceNullityItemParams params) {
        List<Long> itemIds = new ArrayList<>(), skuIds = new ArrayList<>();
        params.getReplaceItems().forEach(item -> {
            itemIds.add(item.getSysItemId());
            skuIds.add(item.getSysSkuId());
        });

        QueryItemDetailParams queryParams = new QueryItemDetailParams();

        queryParams.setSysItemIdList(itemIds);
        queryParams.setSysSkuIdList(skuIds);
        queryParams.setNeedSuitSingleOrNot(true);
        List<DmjItem> items = itemServiceDubboImpl.queryItemDetail(staff, queryParams);
        Map<String, DmjItem> itemMap = new HashMap<>();
        for (DmjItem item : items) {
            itemMap.put(joinKey(item.getSysItemId(), 0L), item);
            List<DmjSku> skus = item.getSkus();
            if (!CollectionUtils.isEmpty(skus)) {
                skus.forEach(sku -> itemMap.put(joinKey(item.getSysItemId(), sku.getSysSkuId()), sku));
            }
        }
        return itemMap;
    }

    private String joinKey(Long sysItemId, Long sysSkuId) {
        return sysItemId + "_" + sysSkuId;
    }

    private boolean check(List<Trade> trades, ProgressData progressData) {

        if (CollectionUtils.isEmpty(trades)) {
            return false;
        }

        List<Order> orders = TradeUtils.getOrders4Trade(trades);

        if (org.springframework.util.CollectionUtils.isEmpty(orders)) {
            return true;
        }

        boolean needReplace = orders.stream().anyMatch(order -> Objects.isNull(order.getItemSysId()) || order.getItemSysId() <= 0);

        if (!needReplace) {
            progressData.getErrorMsg().add("{\"" + trades.get(0).getMergeSid() + "\":\"订单中不包含待审核且商品未匹配异常的平台商品\"}");
            progressData.getAtomicErrorNum().addAndGet(1L);
        }

        return needReplace;
    }


    public void replaceItem(Staff staff, ReplaceNullityItemContext context, List<List<Trade>> trades) {
        if (CollectionUtils.isEmpty(trades) || CollectionUtils.isEmpty(context.replaceItems)) {
            return;
        }
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        ModifyData modifyData = new ModifyData();
        TradeItemContext tradeItemContext = new TradeItemContext();
        trades.forEach(tradeList -> {

            //替换商品
            ReplaceData replaceData = replaceItem2Order(staff, tradeList, context);
            if (Objects.isNull(replaceData)) {
                return;
            }
            List<Order> orders = replaceData.getInsertOrdersNoSuit();
            orders.addAll(replaceData.noMatchOrders);
            TradeUtils.setOrders(replaceData.originTrade, orders);

            modifyData.applyOrders.addAll(replaceData.insertOrders);
            modifyData.updateTrades.add(replaceData.originTrade);
            modifyData.deletes.addAll(replaceData.deleteOrders);
            modifyData.inserts.addAll(replaceData.insertOrders);
            modifyData.resumeStocks.addAll(replaceData.deleteOrders);

            fillTrade(staff, context, replaceData,tradeItemContext);
        });

        //计算成本价
        modifyParentBusiness.matchCost(staff, OrderUtils.toFullOrderList(modifyData.inserts, true));

        tradeReplaceNullityItemBusiness.replaceAfter(staff, modifyData,tradeConfig);
    }

    /**
     * 填充订单信息
     */
    private void fillTrade(Staff staff, ReplaceNullityItemContext context, ReplaceData replaceData,TradeItemContext tradeItemContext) {

        //计算金额

        TradeItemExchangeRequest request = new TradeItemExchangeRequest();
        request.setMainTrade(replaceData.originTrade);
        request.setExChangeRel(replaceData.exchangeRels);
        //重算金额
        tradeItemExchangePaymentBusinessService.calculate(staff, request,tradeItemContext, true,null);

    }

    @Transactional
    public void replaceAfter(Staff staff, ModifyData modifyData, TradeConfig tradeConfig) {
        //处理库存
        orderStockService.modifyOrders(staff, modifyData.applyOrders, modifyData.resumeStocks);

        //填充商品标签信息到order
        tradeOrderSyncItemTagFill.fillByItemDubbo(staff, modifyData.inserts);

        //标记是否拣选验货
        OrderUtils.fillIsPick(modifyData.inserts);
        //添加标签
        tradeTagUpdateBusiness.addTags(staff, modifyData.updateTrades,OpEnum.ADD_TAG, Lists.newArrayList(SystemTags.TAG_INVALID_CHANGE_ITEM));
        // 计算库存状态
       // calTradeStockStatus(staff, modifyData, tradeConfig);


        //计算库存
        modifyData.updateTrades.forEach(trade -> modifyParentBusiness.calculate(staff, trade, tradeConfig));

        tradeUpdateService.updateTrades(staff, modifyData.updateTrades, modifyData.deletes, null, modifyData.inserts);

        //发送操作日志
        tradeTraceBusiness.asyncTrace(staff, modifyData.updateTrades, OpEnum.ITEM_CHANGE);

        //修改商品重算
        reMatchBusiness.reMatch(staff, modifyData.updateTrades.stream().map(Trade::getSid).collect(Collectors.toList()), EventEnum.EVENT_CHANGE_ITEM, Boolean.TRUE);
    }

    private boolean equalsItem(Order order, List<ReplaceNullityItemParams.Item> originItem) {
        return originItem.stream().anyMatch(item -> {

            if (Objects.nonNull(order.getItemSysId()) && order.getItemSysId() >= 0) {
                return false;
            }

            if (!StringUtils.hasText(item.getSkuId()) || "-1".equals(item.getSkuId()) || "0".equals(item.getSkuId())) {
                return order.getNumIid().equals(item.getItemId());
            }

            return (Objects.nonNull(item.getItemId()) && item.getItemId().equals(order.getNumIid())) && (Objects.nonNull(item.getSkuId()) && item.getSkuId().equals(order.getSkuId()));
        });
    }

    /**
     * 替换商品
     */
    private ReplaceData replaceItem2Order(Staff staff, List<Trade> trades, ReplaceNullityItemContext context) {
        ReplaceData replaceData = new ReplaceData(trades);
        List<Order> matchOrders = replaceData.originOrders.stream().filter(order -> equalsItem(order, context.originItems)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchOrders)) {
            return null;
        }

        //这些是不用替换的
        replaceData.noMatchOrders = replaceData.originOrders.stream().filter(order -> !equalsItem(order, context.originItems)).collect(Collectors.toList());

        StringBuilder tradeTraceStr = new StringBuilder();
        //需要替换的替换后数据汇总到replaceData
        matchOrders.forEach(originOrder -> {
            List<Order> orders = dmjSku2Order(staff, context, replaceData.originTrade, originOrder);

            replaceData.insertOrders.addAll(orders);

            List<Order> deleteOrders = replaceData.originOrderMap.get(originOrder.getId());
            deleteOrders.forEach(order -> order.setEnableStatus(0));
            deleteOrders.forEach(order->OrderExceptUtils.clearOrderExcept(staff,order));
            replaceData.deleteOrders.addAll(deleteOrders);


            if (tradeTraceStr.length() > 0) {
                tradeTraceStr.append(" | ");
            }
            tradeTraceStr.append(getTraceContent(originOrder, orders));
            replaceData.originTrade.getOperations().put(OpEnum.ITEM_CHANGE, tradeTraceStr.toString());
            replaceData.exchangeRels.add(ExchangeRelUtils.build(originOrder, orders));
        });
        return replaceData;
    }

    private List<Order> dmjSku2Order(Staff staff, ReplaceNullityItemContext context, Trade originTrade, Order originOrder) {
        List<Order> replaceOrders = new ArrayList<>();
        AtomicBoolean isFirst = new AtomicBoolean(true);
        originOrder.setUserId(originOrder.getUserId());

        context.replaceItems.forEach(item -> {
            Order newOrder;
            if (ReplaceItemUtils.isValidSkuId(item.getSysSkuId())) {//sku
                newOrder = dmjSku2Order(staff, context, originTrade, originOrder, item);
            } else {
                newOrder = dmjItem2Order(staff, context, originTrade, originOrder, item);
            }
            if (Objects.isNull(newOrder)) {
                return;
            }
            newOrder.setUserId(originTrade.getUserId());
            newOrder.setWarehouseId(originTrade.getWarehouseId());
            newOrder.setInsert(true);

            String key = StockItemUtils.buildItemKey(item.getSysItemId(), item.getSysSkuId());
            BigDecimal price = context.itemPriceMap.get(key);
            if (Objects.nonNull(price)) {
                newOrder.setPrice(price.toString());
            }

            if (!isFirst.get()) {
                fillSysItem(staff, context, originOrder, newOrder);
            }
            TradeItemUtils.copyOrderExt(newOrder, originOrder.getOrderExt());
            isFirst.set(false);
            replaceOrders.add(newOrder);
        });
        return replaceOrders;
    }


    private void fillSysItem(Staff staff, ReplaceNullityItemContext context, Order originOrder, Order order) {
        OrderUtils.toFullOrderList(Collections.singletonList(order), false).forEach(o -> {
            o.setNumIid("");
            o.setSkuId("");
            o.setOuterIid("");
            o.setTitle("");
            o.setOuterSkuId("");
            o.setSkuPropertiesName("");
            o.setPicPath("");
            o.setPayAmount("0");
            o.setPayment("0");
            o.setSource(CommonConstants.PLAT_FORM_TYPE_SYS);
        });
    }

    private Order dmjSku2Order(Staff staff, ReplaceNullityItemContext context, Trade originTrade, Order originOrder, ReplaceNullityItemParams.Item replaceItem) {
        DmjItem dmjItem = getDmjItem(context.dmjItemMap, replaceItem.getSysItemId(), 0L);
        if (dmjItem == null) {
            return null;
        }
        DmjSku dmjSku = (DmjSku) getDmjItem(context.dmjItemMap, replaceItem.getSysItemId(), replaceItem.getSysSkuId());
        if (dmjSku == null) {
            return null;
        }
        Order newOrder = getDmjItem2Order(staff, originTrade, context.tradeConfig, originOrder, dmjItem, replaceItem.getNum() * originOrder.getNum());
        modifyParentBusiness.fillSku(newOrder, dmjSku, false);
        newOrder.setType(ModifyParentBusiness.getType(dmjSku));
        modifyParentBusiness.fillSuit(staff, newOrder, newOrder.getType(), dmjSku.getSuiteSingleList(), context.tradeConfig);
        return newOrder;
    }

    private Order dmjItem2Order(Staff staff, ReplaceNullityItemContext context, Trade originTrade, Order originOrder, ReplaceNullityItemParams.Item replaceItem) {
        DmjItem dmjItem = getDmjItem(context.dmjItemMap, replaceItem.getSysItemId(), 0L);
        if (dmjItem == null) {
            return null;
        }

        Order newOrder = getDmjItem2Order(staff, originTrade, context.tradeConfig, originOrder, dmjItem, replaceItem.getNum() * originOrder.getNum());
        newOrder.setType(ModifyParentBusiness.getType(dmjItem));
        modifyParentBusiness.fillSuit(staff, newOrder, newOrder.getType(), dmjItem.getSuiteSingleList(), context.tradeConfig);
        return newOrder;
    }

    private Order getDmjItem2Order(Staff staff, Trade trade, TradeConfig tradeConfig, Order originOrder, DmjItem dmjItem, int replaceNum) {
        Order newOrder = orderCopier.copy(originOrder, new TbOrder());
        newOrder.setId(idWorkerService.nextId());
        newOrder.setSkuSysId(-1L);
        newOrder.setOrigin(originOrder);
        newOrder.setNum(replaceNum);
        newOrder.setFlag(1);
        newOrder.setExceptData(TradeExceptUtils.getTradeExceptData(trade));
        newOrder.setSubTradeExceptDatas(trade.getSubTradeExceptDatas());
        modifyParentBusiness.fillItem(staff,newOrder, dmjItem, false, tradeConfig);
        return newOrder;
    }

    /**
     * 填充上下文
     */
    private ReplaceNullityItemContext fillContext(Staff staff, ReplaceNullityItemParams params, ProgressData progressData) {
        Map<String, String> errMsg = new HashMap<>();
        List<ReplaceNullityItemParams.Item> originItems = params.getOriginItems();
        List<String> itemIds = originItems.stream().map(ReplaceNullityItemParams.Item::getItemId).collect(Collectors.toList());
        List<String> skuIds = originItems.stream().map(ReplaceNullityItemParams.Item::getSkuId).filter(Objects::nonNull).collect(Collectors.toList());

        //按平台商品信息获取order
        List<TbOrder> tbOrders = tbOrderDAO.listByPlatItemSku(staff, itemIds, skuIds, true, Trade.SYS_STATUS_WAIT_BUYER_PAY, Trade.SYS_STATUS_WAIT_AUDIT);
        if (CollectionUtils.isEmpty(tbOrders)) {
            errMsg.put("0", "订单中不包含待审核且商品未匹配异常的平台商品");
            progressData.setErrorNum(1L).getErrorMsg().add(JSONObject.toJSONString(errMsg));
            return null;
        }

        //查询订单信息
        Long[] sids = tbOrders.stream().map(TbOrder::getSid).toArray(Long[]::new);
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, sids);
        if (CollectionUtils.isEmpty(trades)) {
            errMsg.put("0", "订单中不包含待审核且商品未匹配异常的平台商品");
            progressData.setErrorNum(1L).getErrorMsg().add(JSONObject.toJSONString(errMsg));
            return null;
        }

        progressData.setCountAll(trades.size());

        Map<String, DmjItem> replaceItemMap = getReplaceItems(staff, params);
        if (CollectionUtils.isEmpty(replaceItemMap)) {
            errMsg.put("0", "要替换的系统商品不存在");
            progressData.setErrorNum(1L).getErrorMsg().add(JSONObject.toJSONString(errMsg));
            return null;
        }

        boolean updatePlatformOrderPayment = TradeConfigGetUtil.get(staff, TradeConfigEnum.OPEN_LINK_PAYMENT).isOpen();

        return new ReplaceNullityItemContext(trades, replaceItemMap, params, tradeConfigService.get(staff), updatePlatformOrderPayment);
    }

    public DmjItem getDmjItem(Map<String, DmjItem> itemMap, Long itemId, Long skuIds) {
        return itemMap != null && itemMap.size() > 0 ? itemMap.get(ReplaceItemUtils.getOriginItemKey(itemId, skuIds)) : null;
    }

    /**
     * 计算库存状态
     * @param staff
     * @param modifyData
     * @param tradeConfig
     */
    private void calTradeStockStatus(Staff staff, ModifyData modifyData, TradeConfig tradeConfig) {
        for (Trade trade : modifyData.updateTrades) {
            TradeStockUtils.resetTradeStockStatus(staff, trade, tradeConfig);
        }
    }

    public static class ReplaceNullityItemContext {
        public ReplaceNullityItemContext(List<Trade> originTrade, Map<String, DmjItem> dmjItemMap, ReplaceNullityItemParams params, TradeConfig tradeConfig, boolean updatePlatformOrderPayment) {
            this.originTrade = originTrade;
            this.dmjItemMap = dmjItemMap;
            this.originItems = params.getOriginItems();
            this.replaceItems = params.getReplaceItems();
            this.tradeConfig = tradeConfig;
            this.updatePlatformOrderPayment = updatePlatformOrderPayment;
            if (!CollectionUtils.isEmpty(dmjItemMap)) {
                this.itemPriceMap = getItemPriceMap(dmjItemMap);
            }
        }

        public boolean updatePlatformOrderPayment;
        public ProgressData progressData;
        public List<Trade> originTrade;
        public Map<String, DmjItem> dmjItemMap;
        public List<ReplaceNullityItemParams.Item> originItems;
        public List<ReplaceNullityItemParams.Item> replaceItems;
        public TradeConfig tradeConfig;
        /***
         * 商品单价
         */
        public Map<String, BigDecimal> itemPriceMap = new HashMap<>();


        private Map<String, BigDecimal> getItemPriceMap(Map<String, DmjItem> replaceItemMap) {
            Map<String, BigDecimal> itemPriceMap = new HashMap<>();
            if (CollectionUtils.isEmpty(replaceItemMap)) {
                return itemPriceMap;
            }
            replaceItemMap.forEach((k, item) -> {
                double priceOutput = item != null && item.getPriceOutput() != null ? item.getPriceOutput() : 0D;
                itemPriceMap.put(k, BigDecimal.valueOf(priceOutput));
            });
            return itemPriceMap;
        }

    }

    private String getTraceContent(Order originOrder, List<Order> newOrders) {
        StringBuilder s = new StringBuilder("无效商品替换 -> 原商品：[");
        if (StringUtils.hasText(originOrder.getNumIid())) {
            s.append("平台商品id: ").append(originOrder.getNumIid());
        }
        if (StringUtils.hasText(originOrder.getSkuId())) {
            s.append(",平台skuId:").append(originOrder.getSkuId());
        }
        s.append(",数量: ").append(originOrder.getNum()).append(",实付: ").append(originOrder.getPayment()).append("] -> 替换后:[");

        newOrders.forEach(order -> s.append("{系统商家编码: ").append(order.getSysOuterId())
                .append(",数量: ").append(order.getNum()).append(",实付: ").append(order.getPayment()).append("}"));
        s.append("]");
        return s.toString();
    }

    public static class ReplaceData {
        public ReplaceData(List<Trade> originTrades) {
            if (originTrades.size() > 1) {
                this.originTrade = originTrades.stream().filter(trade -> trade.getSid().equals(trade.getMergeSid())).findFirst().orElse(originTrades.get(0));
            } else {
                this.originTrade = originTrades.get(0);
            }
            List<Order> orders4Trade = TradeUtils.getOrders4Trade(originTrades);
            originOrders = orders4Trade;
            orders4Trade.forEach(order -> originOrderMap.computeIfAbsent(order.getId(), v -> new ArrayList<>()).addAll(OrderUtils.toFullOrderList(Collections.singletonList(order), false)));

        }

        public Trade originTrade;

        public List<Order> originOrders;
        public Map<Long, List<Order>> originOrderMap = new HashMap<>();

        public List<Order> insertOrders = new ArrayList<>();

        public List<Order> deleteOrders = new ArrayList<>();

        public List<Order> noMatchOrders = new ArrayList<>();

        public List<TradeItemExchangeRel> exchangeRels = Lists.newArrayList();

        public List<Order> getInsertOrdersNoSuit() {
            return insertOrders.stream().filter(order -> order.getCombineId() == null || order.getCombineId() == 0).collect(Collectors.toList());
        }
    }

}

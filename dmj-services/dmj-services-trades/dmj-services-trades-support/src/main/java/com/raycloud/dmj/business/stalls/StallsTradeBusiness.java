package com.raycloud.dmj.business.stalls;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.audit.AuditStockBusiness;
import com.raycloud.dmj.business.audit.help.AuditUtils;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.modify.ModifyParentBusiness;
import com.raycloud.dmj.business.modify.TradeAddBusiness;
import com.raycloud.dmj.business.operate.TradeUpdateSellerMemoFlagBusiness;
import com.raycloud.dmj.business.stock.RecordAdjustBusiness;
import com.raycloud.dmj.business.tag.TradeTagBusiness;
import com.raycloud.dmj.business.wms.SaleTradeWmsBusiness;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.dao.trade.pgl.OrderExtDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.stalls.common.constants.SaleConstants;
import com.raycloud.dmj.domain.stalls.common.dto.*;
import com.raycloud.dmj.domain.stalls.common.result.*;
import com.raycloud.dmj.domain.stalls.common.utils.*;
import com.raycloud.dmj.domain.stalls.enums.*;
import com.raycloud.dmj.domain.stalls.trade.dto.SaleSettleDto;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.memo.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.audit.AuditData;
import com.raycloud.dmj.domain.trades.consign.ConsignCancelData;
import com.raycloud.dmj.domain.trades.params.TradeWaybillGetParams;
import com.raycloud.dmj.domain.trades.request.TradePictureMemoUpdateParams;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.wms.GoodsSectionOrderRecord;
import com.raycloud.dmj.external.builder.ItemBuilder;
import com.raycloud.dmj.external.consumer.*;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.impl.LockService;
import com.raycloud.dmj.services.stalls.IStallWaveService;
import com.raycloud.dmj.services.thirdPartyService.TradeWmsDubboService;
import com.raycloud.dmj.services.trade.audit.TradeAuditService;
import com.raycloud.dmj.services.trade.label.system.impl.TradeSysLabelBusiness;
import com.raycloud.dmj.services.trade.supplier.IOrderSupplierService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.stock.IOrderStockService;
import com.raycloud.dmj.services.trades.support.SysTradeService;
import com.raycloud.dmj.stalls.application.service.ISaleConfigService;
import com.raycloud.dmj.utils.wms.WmsUtils;
import com.raycloud.ec.api.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: WangChao(王超)
 * @Date: 2019/6/27 5:23 PM
 * @Version 1.0
 * @Email <EMAIL>
 * 档口批发业务封装类
 */
@Service
@Slf4j
public class StallsTradeBusiness {

    @Resource
    private IEventCenter eventCenter;
    @Resource
    private LockService lockService;
    @Resource
    private TradeLockBusiness tradeLockBusiness;

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    private ITradeUpdateService tradeUpdateService;

    @Resource
    private ITradePtService tradePtService;
    @Resource
    private SysTradeService sysTradeService;
    @Resource
    private ITradeService tradeService;
    @Resource
    private TradeAuditService tradeAuditService;
    @Resource
    private ITradePictureMemoService tradePictureMemoService;
    @Resource
    private ITradeSalesmanService tradeSalesManService;
    @Resource
    private ITradeConfigService tradeConfigService;
    @Resource
    private ITradeTraceService tradeTraceService;
    @Resource
    private ITradeWaveQueryService tradeWaveQueryService;
    @Resource
    private IStallWaveService stallWaveService;
    @Resource
    private TradeWmsDubboService tradeWmsDubboService;
    @Resource
    public IWarehouseService warehouseService;

    @Resource
    public IOrderStockService orderStockService;

    @Resource
    private TradePerformanceOptLogService tradePerformanceOptLogService;

    @Resource
    private TradeAddBusiness tradeAddBusiness;
    @Resource
    private TradeTagBusiness tradeTagBusiness;

    @Resource
    public SaleTradeStockBusiness saleTradeStockBusiness;

    @Resource
    private RecordAdjustBusiness recordAdjustBusiness;
    @Resource
    private TbTradeDao tbTradeDao;
    @Resource
    private OrderExtDao orderExtDao;

    @Resource
    private ISaleConfigService saleConfigService;
    @Resource
    private SaleTradeWmsBusiness saleTradeWmsBusiness;

    @Resource
    private IOrderSupplierService orderSupplierService;

    @Resource
    private TradeConsumer tradeConsumer;
    @Resource
    private WmsConsumer wmsConsumer;

    @Resource
    AuditStockBusiness auditStockBusiness;

    @Resource
    TradeSysLabelBusiness tradeSysLabelBusiness;

    @Resource
    TradeUpdateSellerMemoFlagBusiness tradeUpdateSellerMemoFlagBusiness;
    @Resource
    ModifyParentBusiness modifyParentBusiness;

    /**
     * 校验商品是否绑定货位
     * @param staff
     * @param orders
     * @param warehouseId
     * @return
     */
    public Result checkGoodsSection(Staff staff, List<Order> orders, Long warehouseId) {
        if (!WmsUtils.isOpenWms(staff)) {
            return Result.isOk();
        }
        //套件转单品
        List<Order> effectiveOrders = OrderUtils.toEffectiveOrders(orders);
        Result<List<ItemStockDto>> result = wmsConsumer.queryGoodsSection(staff, effectiveOrders, warehouseId);
        if (!result.isSucc()) {
            return result;
        }
        List<ItemStockDto> goodsSections = result.getData();
        Map<String, ItemStockDto> sectionSkuMap = goodsSections.stream().collect(Collectors.toMap(r ->
                ItemBuilder.buildOrderKey(r.getSysItemId(), r.getSysSkuId()), Function.identity(), (v1, v2) -> v1));

        List<String> notSection = Lists.newArrayList(); //无货位商品
        for (Order order : effectiveOrders) {
            ItemStockDto section = sectionSkuMap.get(ItemBuilder.buildOrderKey(order.getItemSysId(), order.getSkuSysId()));
            if (section == null) {   //未绑定货位
                log.info("商品[{}]无货位记录", order.getSysOuterId());
                notSection.add(order.getOuterId());
            }
        }
        if (CollectionUtils.isNotEmpty(notSection)) {   //无库存记录时优先返回，货位版现场收银抛出异常
            return Result.isFail(ResultCode.WMS_NO_GOODS_SECTION, "商品" + StringUtils.join(notSection, ",") + "未绑定货位!");
        }
        return Result.isOk();
    }

    /**
     * 销货单结账 当前适用于v1 后续看是否需要改为批量
     * @param staff
     * @param trade
     */
    @Transactional
    public void settle(Staff staff, Trade trade, SaleSettleDto settleDto) {
        tradeAddBusiness.initTrade(staff, trade);
        initFinishTrade(staff,trade, Trade.SYS_STATUS_FINISHED);
        saveTrade(staff, trade, false);
        if (settleDto.isPre()) {   //选择配货单结账需要从暂存区扣减库存
            stallWaveService.out(staff, Lists.newArrayList(trade.getWaveId()), trade.getUserId());
        } else {    //直接开单处理库存
            saleTradeStockBusiness.checkStock(staff, trade);
        }
    }

    public void settleV2(Staff staff, Trade trade, SaleSettleDto settleDto) {
        int settleType = settleDto.getSettleType();
        if (settleDto.getOnlySettleType() == 21) {  //预配货结账
            settlePre(staff, trade, settleDto);
            return;
        }
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        tradeAddBusiness.initTrade(staff, trade);
        initSelfPick(staff, trade);
        if (!"9".equals(settleDto.getPayType()) && !"5".equals(settleDto.getPayType())) {    //打线下支付标签
            tradeSysLabelBusiness.addTags(staff, Lists.newArrayList(trade), OpEnum.ADD_TAG, Lists.newArrayList(SystemTags.TAG_OFFLINE_PAY));
        }
        if (settleType == 0) {  //现场收银
            settleCash(staff, trade);
            return;
        }
        if (settleDto.isPre()) {  //预配货打上标签
            tradeSysLabelBusiness.addTags(staff, Lists.newArrayList(trade), OpEnum.ADD_TAG, Lists.newArrayList(SystemTags.TAG_SALE_TRDE_PRE_ALLOCATE_GOODS));
        }
        if (settleDto.getOrigin() != null && "云助手".equals(settleDto.getOrigin())) {//云助手标签
            tradeSysLabelBusiness.addTags(staff, Lists.newArrayList(trade), OpEnum.ADD_TAG, Lists.newArrayList(SystemTags.TAG_YUN_ZHU_SHOU));
        }
        orderStockService.applyOrderStockLocal(staff, orders);
        saveTrade(staff, trade, true);
    }

    /**
     * 现场收银结账
     */
    public void settleCash(Staff staff, Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        if (WmsUtils.isOpenWms(staff) && !WmsUtils.isNewWms(staff)) {    //货位版校验货位
            Result result = checkGoodsSection(staff, orders, trade.getWarehouseId());
            Assert.isTrue(result.isSucc(), result.getMsg());
        }
        try {
            tradeConsumer.applyLocalStock(staff, orders);
            if (WmsUtils.isOpenWms(staff) && !WmsUtils.isNewWms(staff)) {   //  货位暂存版，需要锁定货位
                //申请货位库存 若为套件则需要取出套件子订单
                List<Long> orderIds = OrderUtils.toIdList(OrderUtils.toEffectiveOrders(orders));
                List<GoodsSectionOrderRecord> sectionRecords = tradeWmsDubboService.applyGoodsStock(staff, orderIds.toArray(new Long[0]), true);
                Assert.isTrue(CollectionUtils.isNotEmpty(sectionRecords), "申请货位库存失败!");

                List<Order> insufficient = orders.stream().filter(e -> Objects.equals(e.getStockStatus(), Trade.STOCK_STATUS_INSUFFICIENT)).collect(Collectors.toList());
                if (!insufficient.isEmpty()) {  //缺货商品触发库存调剂
                    insufficient.forEach(e -> recordAdjustBusiness.adjust4WmsOnly(staff, e.getWarehouseId(), e.getItemSysId(), e.getSkuSysId()));
                }
            }
            initFinishTrade(staff,trade, Trade.SYS_STATUS_FINISHED_AUDIT);
            saveTrade(staff, trade, false);
            tradePerformanceOptLogService.addAuditOptLog(staff, Lists.newArrayList(trade), TradePerformanceOptLog.Audit_Type);  //手动插入绩效日志
            //现场收银直接发货
            tradeService.consign(staff, new Long[]{trade.getSid()}, SendType.DUMMY.name(), null, false, null, null, null);
        } catch (Exception e) {
            tradeConsumer.resumeLocalStock(staff, orders);
            throw new IllegalArgumentException(e.getMessage());
        }
        if (StringUtils.isBlank(trade.getOutSid())) {   //现场收银，没有审核逻辑，快递单号在结账完成后自动获取一次
            TradeWaybillGetParams params = new TradeWaybillGetParams().setStaff(staff).setSids(Lists.newArrayList(trade.getSid()));
            eventCenter.fireEvent(this, new EventInfo("trade.waybill.get").setArgs(new Object[]{params}), null);
        }
    }

    /**
     * 预配货结账
     */
    public void settlePre(Staff staff, Trade trade, SaleSettleDto settleDto) {
        List<Trade> originTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, trade.getSid());
        Assert.isTrue(CollectionUtils.isNotEmpty(originTrades), "根据sid:" + trade.getSid() + ",查询原订单为空!");
        Trade originTrade = originTrades.get(0);
        //先更新部分必须更新的信息
        preUpdateTrade(staff, trade, originTrade);

        List<Order> sources = TradeUtils.getOrders4Trade(originTrade);
        List<Order> targets = TradeUtils.getOrders4Trade(trade);
        String saleLog = compareOrder(sources, targets);
        if (StringUtils.isNotEmpty(saleLog)) {
            tradeTraceService.addTradeTrace(staff, SaleLogUtils.create(staff, settleDto.getId(), SaleOpEnums.PRE_UPDATE, saleLog));
            unAuditPreAllocateGoodsTrade(staff, originTrade, trade);
        }
        if (settleDto.getFileRemarks() != null) {    //更新扩展信息图片备注
            TradePictureMemoUpdateParams params = new TradePictureMemoUpdateParams();
            params.setSid(originTrade.getSid());
            params.setTradePictureMemoUris(settleDto.getFileRemarks());
            tradePictureMemoService.update(staff, params);
        }
        tradeService.consign(staff, new Long[]{trade.getSid()}, SendType.DUMMY.name(), null, false, null, null, null);
    }

    /**
     * 更新订单前。先更新一些需要修改的信息
     */
    private void preUpdateTrade(Staff staff, Trade trade, Trade origin) {
        Trade updTrade = new TbTrade();
        updTrade.setSid(origin.getSid());
        updTrade.setPostFee(trade.getPostFee());
        updTrade.setPayment(trade.getPayment());
        //更新订单地址
        updTrade.setReceiverAddress(trade.getReceiverAddress());
        updTrade.setReceiverPhone(trade.getReceiverPhone());
        updTrade.setReceiverMobile(trade.getReceiverMobile());
        updTrade.setReceiverName(trade.getReceiverName());

        BigDecimal tarDiscountFee = NumberUtils.str2Decimal(trade.getDiscountFee(), "0.00");
        BigDecimal oriDiscountFee = NumberUtils.str2Decimal(origin.getDiscountFee(), "0.00");
        updTrade.setDiscountFee(tarDiscountFee.toString());
        //更新承诺发货时间
        if (trade.getTimeoutActionTime() != null) {
            updTrade.setDeliveryTime(trade.getDeliveryTime());
        }
        tbTradeDao.update(staff, updTrade);
        log.info("预配货结账前更新订单信息! 邮费 {} -> {}, 折扣金额 {} -> {}, 承诺发货时间 {} -> {}", trade.getPostFee(), origin.getPostFee(),
                oriDiscountFee, tarDiscountFee, origin.getTimeoutActionTime(), trade.getTimeoutActionTime());
        //更新orderExt的备注
        Map<Long, Order> orderMap = TradeUtils.getOrders4Trade(origin).stream().collect(Collectors.toMap(Order::getOid, Function.identity(), (a, b) -> a));
        List<OrderExt> updExts = Lists.newArrayList();
        for (Order order : TradeUtils.getOrders4Trade(trade)) {
            if (order.getOrderExt() == null || order.getOid() == null) {
                continue;
            }
            OrderExt orderExt = order.getOrderExt();
            if (StringUtils.isEmpty(orderExt.getOrderRemark())) {
                continue;
            }
            Order oriOrder = orderMap.get(order.getOid());  //找到原订单对应的订单
            if (oriOrder == null) {
                continue;
            }
            OrderExt updExt = new OrderExt();
            updExt.setId(oriOrder.getId());
            updExt.setOrderRemark(orderExt.getOrderRemark());
            updExts.add(updExt);
        }
        orderExtDao.batchUpdate(staff, updExts);
    }

    /**
     * 初始化订单 现场自提 相关数据
     */
    public void initSelfPick(Staff staff, Trade trade) {
        if (Objects.isNull(trade) || TradePickGoosTypeEnum.SELF_PICK.getPickGoodsType() != trade.getPickGoodsType()) {
            return;
        }
        Map<String, Object> condition = Maps.newHashMap();
        condition.put("name", TradePickGoosTypeEnum.SELF_PICK.getMsg());
        CalculateTransportDto data = tradeConsumer.queryExpress(staff, condition).assertTrue().getData();
        Warehouse warehouse = warehouseService.queryById(trade.getWarehouseId());
        Assert.notNull(warehouse, "仓库不存在！");
        //初始化收货人等信息
        trade.setTemplateId(data.getTemplateId());
        trade.setLogisticsCompanyId(data.getLogisticsCompanyId());
        trade.setPostFee("0");
        trade.setOutSid(SaleConstants.SELF_PICK_OUT_SID);
        trade.setReceiverAddress(warehouse.getAddress() + "（不邮寄）");
        trade.setReceiverPhone(SaleConstants.SELF_PICK_RECEIVER_PHONE);
        trade.setReceiverMobile(SaleConstants.SELF_PICK_RECEIVER_PHONE);
        trade.setReceiverState(warehouse.getState());
        trade.setReceiverCity(warehouse.getCity());
        trade.setReceiverDistrict(warehouse.getDistrict());
        trade.setReceiverName(trade.getBuyerNick());
        //设置 "现场自提" 系统标签
        tradeSysLabelBusiness.addTags(staff, Lists.newArrayList(trade), OpEnum.ADD_TAG, Lists.newArrayList(SystemTags.TAG_SELF_PICK));
    }

    /**
     * 保存订单相关信息
     * @param staff
     */
    public void saveTrade(Staff staff, Trade trade, boolean match){
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        //计算订单信息
        modifyParentBusiness.calculate(staff, trade);
        //理论重量是否覆盖实际重量
        modifyParentBusiness.isCoverWeight(staff, Lists.newArrayList(trade));
        // v1，v2现场收银，则直接生成已完成订单
        if (match) {
            try {
                tradeTagBusiness.matchTags(staff, Lists.newArrayList(trade), 2, 1, 1, new ArrayList<>());
            } catch (Exception e) {
                log.error("订单匹配标签失败, msg: {}", e.getMessage(), e);
            }
        }
        tradeUpdateService.updateTrades(staff, null, null, trade, orders);
        tradePtService.saveByTrades(staff, Lists.newArrayList(trade));
        final List<TradeTrace> tradeTraces = Lists.newArrayList(TradeTraceUtils.createTradeTraceWithTrade(staff, trade, "新增订单", staff.getName(), new Date(), ""));
        if (MapUtils.isNotEmpty(trade.getOperations())) {
            trade.getOperations().forEach((k, v) -> tradeTraces.add(TradeTraceUtils.createTradeTraceWithTrade(staff, trade, k.getName(), staff.getName(), new Date(), v)));
        }
        tradeTraceService.batchAddTradeTrace(staff, tradeTraces);
        orderSupplierService.sync(staff, TradeUtils.getOrders4Trade(trade), null);
        log.info("新增系统订单成功! sids: {}", trade.getSid());
        if (!trade.isDangkouTrade()) {
            return;
        }
        //后续业务执行失败不能影响主业务执行
        try {
            //档口订单增加业务员
            tradeSalesManService.saveTradeSalesman(staff, TradeSalesmanUtils.buildSalesmanList(staff, Collections.singletonList(trade), null));
            wmsConsumer.bindingUniqueCodes(staff, trade);
        } catch (Exception e) {
            log.error("档口订单补充业务执行失败!", e);
        }
    }

    /**
     * v1，v2现场收银 创建为已完成
     * @param trade
     */
    public void initFinishTrade(Staff staff,Trade trade, String status) {
        trade.setSysStatus(status);
        // trade.setStockStatus(Trade.STOCK_STATUS_NORMAL);
        TradeExceptUtils.setStockStatus(staff,trade,Trade.STOCK_STATUS_NORMAL);
        trade.setConsignTime(new Date());
        trade.setEndTime(new Date());
        if (status.equals(Trade.SYS_STATUS_FINISHED)) {
            trade.setSysConsigned(1);
            trade.setConsignTime(new Date());
        }
        trade.setTradeFrom("DKPF");
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        final List<Order> fullOrders = OrderUtils.toFullOrderList(orders, false);
        fullOrders.forEach(e -> setOrderStatus(staff,e, status));
    }
    public void setOrderStatus(Staff staff,Order order, String status){
        order.setSysStatus(status);
        // order.setStockStatus(Trade.STOCK_STATUS_NORMAL);
        OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_NORMAL);
        order.setStockNum(order.getNum());
        order.setEndTime(new Date());
        if (status.equals(Trade.SYS_STATUS_FINISHED)) {
            order.setSysConsigned(1);
            order.setConsignTime(new Date());
        }

    }

    /**
     * 档口订单审核并生成波次，新开事务处理
     * @param staff
     * @param trade
     * @param isAudit 强制自动审核
     */
    public void auditAndCreateWave(Staff staff, Trade trade, boolean isAudit) {
        if (trade == null || trade.getSid() == null) {
            return;
        }
        Integer billingWave = saleConfigService.get(staff).getIntegerVal(SaleConfigEnums.ST_BILLING_INTO_WAVE_V2);
        if (billingWave == 0 && !isAudit) {
            log.info("未开启自动审核功能, 未指定审核，不审核");
            return;
        }
        AuditData auditData = AuditUtils.init(new Long[]{trade.getSid()}, tradeConfigService.get(staff), (billingWave & 2) == 2, false, null, 0, 1);
        lockService.locks(tradeLockBusiness.getERPLocks(staff, auditData.originSids), () -> tradeAuditService.audit(staff, auditData));
        auditStockBusiness.auditStockWms(staff, auditData);

        TradeResult result = auditData.results.get(trade.getSid());
        log.info("订单审核的结果是: {}", JSONObject.toJSONString(result));
        if (result != null && result.getSuccess() != null && result.getSuccess()) {
            //订单已经同步过备注，所以这里直接取订单备注即可
            String remark = trade.getSysMemo();
            if ((billingWave & 4) == 4) {   //同步生成波次
                List<Trade> trades = tradeWaveQueryService.generateWave4StallRuleSync(staff, trade.getWarehouseId(), Lists.newArrayList(trade.getSid()), remark);
                log.info("同步生成波次处理完成, 生成失败的sid: {}", TradeUtils.toSidList(trades));
                if (CollectionUtils.isEmpty(trades)) {
                    trade.setWaveId(-1L);
                }
            } else {
                tradeWaveQueryService.generateWave4StallRule(staff, trade.getWarehouseId(), Lists.newArrayList(trade.getSid()), remark);
            }
        }
    }

    /**
     * 修改商品信息反审核预配货订单
     */
    public void unAuditPreAllocateGoodsTrade(Staff staff, Trade originTrade, Trade targetTrade) {
        //修改了商品的订单需要反审核、修改商品重新计算金额再自动化审核发货变为交易成功
        List<Trade> updateTrades = tradeAuditService.unaudit(staff, OpEnum.AUDIT_UNDO_AUTO_SALE, new Long[]{originTrade.getSid()}).getSuccessTrades();
        saleTradeWmsBusiness.sendUnaudit(staff, updateTrades);
        //修改商品
        sysTradeService.updateTrade(staff, targetTrade);
        //重新审核
        AuditData auditData = AuditUtils.init(new Long[]{originTrade.getSid()}, tradeConfigService.get(staff), false, false, null, 0, 1);
        lockService.locks(tradeLockBusiness.getERPLocks(staff, originTrade.getSid()), () -> tradeAuditService.audit(staff, auditData));
        saleTradeWmsBusiness.sendAudit(staff, auditData.finishAuditTrades);

        TradeResult result = auditData.results.get(originTrade.getSid());
        log.info("重新审核后的结果是:" + JSONObject.toJSONString(result));
    }

    public void updateTrade(Staff staff, Trade originTrade, Trade targetTrade, SaleSettleDto settleDto) {
        if (originTrade == null && targetTrade == null) {   //原订单和修改后订单都为空，不做处理
            return;
        }
        if (originTrade == null) {  //原订单为空，走结账逻辑
            settleV2(staff, targetTrade, settleDto);
            settleDto.getTradeTraces().add("新增订单: " + targetTrade.getSid());
            return;
        }
        Long[] oriSids = new Long[]{originTrade.getSid()};
        //原订单不为空，已审核订单需反审核，已发货订单需撤销发货
        if (originTrade.getSysStatus().equals(Trade.SYS_STATUS_FINISHED_AUDIT)) {   //已审核订单反审核
            List<Trade> updateTrades = tradeAuditService.unaudit(staff, OpEnum.AUDIT_UNDO_AUTO_SALE, new Long[]{originTrade.getSid()}).getSuccessTrades();
            saleTradeWmsBusiness.sendUnaudit(staff, updateTrades);
        } else if (originTrade.getSysStatus().equals(Trade.SYS_STATUS_FINISHED)) { //已发货订单撤销发货
            ConsignCancelData cancelData = tradeService.cancelConsign(staff, oriSids, 0, true, false);
            if (!cancelData.errors.isEmpty()) { //撤销发货失败直接报错
                throw new IllegalArgumentException(cancelData.errors.get(originTrade.getSid().toString()));
            }
        }
        if (targetTrade == null) {  //修改后订单为空，作废原订单
            sysTradeService.cancelTrades(staff, oriSids);
            settleDto.getTradeTraces().add("作废订单: " + originTrade.getSid());
            return;
        }

        List<String> fileRemarks = settleDto.getFileRemarks();
        preUpdateTrade(staff, targetTrade, originTrade);
        if (fileRemarks != null) {    //更新扩展信息图片备注
            TradePictureMemoUpdateParams params = new TradePictureMemoUpdateParams();
            params.setSid(originTrade.getSid());
            params.setTradePictureMemoUris(fileRemarks);
            tradePictureMemoService.update(staff, params);
        }
        initSelfPick(staff, targetTrade);
        //修改商品
        sysTradeService.updateTrade(staff, targetTrade);
        if (Objects.equals(0, settleDto.getSettleType())) {    //如果是走现场收银修改，直接审核并发货
            AuditData auditData = AuditUtils.init(oriSids, tradeConfigService.get(staff), true, false, null, 0, 1);
            lockService.locks(tradeLockBusiness.getERPLocks(staff, originTrade.getSid()), () -> tradeAuditService.audit(staff, auditData));
            TradeResult auditResult = auditData.results.get(originTrade.getSid());
            if (Objects.nonNull(auditResult) && Objects.nonNull(auditResult.getSuccess()) && !auditResult.getSuccess()) { //订单审核失败后续操作终止
                log.info("订单{}审核失败! msg: {}", originTrade.getSid(), auditResult.getErrorMsg());
                return;
            }
            saleTradeWmsBusiness.sendAudit(staff, auditData.finishAuditTrades);
            tradeService.consign(staff, oriSids, SendType.DUMMY.name(), null, false, null, null, null);
        }
    }

    /**
     * 获取存在修改的商品
     * @param sources   原有订单
     * @param targets   修改订单
     * @return
     */
    public String compareOrder(List<Order> sources, List<Order> targets) {
        final List<String> addLog = Lists.newArrayList() , delLog = Lists.newArrayList(),
                updLog = Lists.newArrayList(), suitLog = Lists.newArrayList();

        Map<Long, Order> orderMap = targets.stream().collect(Collectors.toMap(Order::getOid, Function.identity()));
        for (Order source : sources) {
            Order target = orderMap.remove(source.getOid());
            if (target == null) {   //原商品在现有商品中不存在，被删除或套件转单品
                if (CollectionUtils.isEmpty(source.getSuits())) {
                    delLog.add(SaleLogUtils.build(null, source, 2));
                    continue;
                }
                List<String> singLog = Lists.newArrayList();
                for (Order single : source.getSuits()) {
                    Order singleTarget = orderMap.remove(single.getId());
                    if (singleTarget != null) { //执行过套件转单品，把单品的id置空，当一个新的商品创建
                        singleTarget.setId(null);
                        singLog.add(SaleLogUtils.build(singleTarget, null, 1));
                    }
                }
                if (singLog.isEmpty()) {
                    delLog.add(SaleLogUtils.build(null, source, 2));
                } else {
                    suitLog.add(SaleLogUtils.buildSuit(source, singLog));
                }
            } else if (!Objects.equals(target.getNum(), source.getNum()) ||
                    DataFormatUtils.formatDecimal(target.getPrice()).compareTo(DataFormatUtils.formatDecimal(source.getPrice())) != 0) {
                updLog.add(SaleLogUtils.build(target, source, 3));
            }
        }
        if (!orderMap.isEmpty()) {
            orderMap.forEach((k, v) -> addLog.add(SaleLogUtils.build(v, null, 1)));
        }
        return SaleLogUtils.createLog(addLog, delLog, updLog, suitLog);
    }

    /**
     * 修改订单系统备注和平台备注
     * @param staff
     * @param sid
     * @param remark
     */
    public void updateSaleTradeRemark(Staff staff, List<Long> sids, String remark) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        //更新订单系统备注
        tradeUpdateSellerMemoFlagBusiness.update(staff, TradeMemo.builder()
                .handType(TradeMemoConstant.HAND_TYPE_SYS)
                .sysMemo(remark)
                .append(true)
                .handFrom(TradeMemoConstant.HAND_FROM_WEB)
                .build(), sids.toArray(new Long[]{}));
        //更新订单平台备注信息(档口订单不需要上传和重算逻辑 直接拼接备注即可)
        TradeMemo tradeMemo = TradeMemo.builder()
                .handType(TradeMemoConstant.HAND_TYPE_PLAT)
                .handFrom(TradeMemoConstant.HAND_FROM_WEB)
                .sellerMemo(remark)
                .isHandlerMemo(0)
                .append(true)
                .rematch(false).build();
        tradeUpdateSellerMemoFlagBusiness.update(staff, tradeMemo, sids.toArray(new Long[]{}));
    }

}

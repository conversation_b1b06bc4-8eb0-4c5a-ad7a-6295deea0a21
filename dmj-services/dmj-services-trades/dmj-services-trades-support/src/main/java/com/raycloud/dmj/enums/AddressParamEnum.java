package com.raycloud.dmj.enums;

import com.raycloud.dmj.domain.base.address.AddressExtContext;
import com.raycloud.dmj.domain.enums.OperationType;
import com.raycloud.dmj.utils.AddressExtUtil;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;

public enum AddressParamEnum {

    // map类型入参解析
    MAP_GET(Map.class, AddressExtUtil::getAddressStrToMap),

    LIST_GET(List.class, AddressExtUtil::getAddressStrToList),

    OBJECT_GET(AddressExtUtil::getAddressStrToObject)
    ;

    private Class<?> aClass;
    private final BiConsumer<Object, AddressExtContext> doGetAddressStr;

    AddressParamEnum(BiConsumer<Object, AddressExtContext> doGetAddressStr) {
        this.doGetAddressStr = doGetAddressStr;
    }

    AddressParamEnum(Class<?> aClass, BiConsumer<Object, AddressExtContext> doGetAddressStr) {
        this.aClass = aClass;
        this.doGetAddressStr = doGetAddressStr;
    }

    public Class<?> getaClass() {
        return aClass;
    }

    public BiConsumer<Object, AddressExtContext> getDoGetAddressStr() {
        return doGetAddressStr;
    }

    public static void initAddressStr(Object o, AddressExtContext addressExtContext) {
        Arrays.stream(values())
                .filter(paramEnum -> (!addressExtContext.isNeedSaveExt() || addressExtContext.getOperationType().equals(OperationType.SELECT))
                        && Objects.nonNull(paramEnum.aClass) && paramEnum.aClass.isInstance(o))
                .findFirst()
                .orElse(OBJECT_GET)
                .getDoGetAddressStr()
                .accept(o, addressExtContext);
    }

}

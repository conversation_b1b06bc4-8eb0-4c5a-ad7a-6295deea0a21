package com.raycloud.dmj.services.trade.item.entity;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.DmjItem;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.trade.item.*;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.services.trade.item.EntityCodeConvertContext;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

import static com.raycloud.dmj.domain.trades.statusenum.RefundStatusEnum.REFUND_WAIT_SELLER_AGREE;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022/6/16 10:12
 * @Description 按实体编码转换商品工具类
 */
public class EntityCodeConvertUtils {

    /**
     * 套件转实体
     */
    public static final int SUIT_2_ENTITY = 1;

    /**
     * 实体转套件
     */
    public static final int ENTITY_2_SUIT = 2;


    /**
     * @return true 库存校验通过 false 库存校验不通过
     */
    public static boolean checkStock(Staff staff, EntityCodeConvertContext convertContext, Long warehouseId, DmjItem dmjItem, int applyNum, Order order) {
        if (needStock(staff, convertContext.convertParams)) {
            Long sysSkuId = EntityCodeConvertUtils.getSysSkuId(staff, dmjItem);
            EntityCodeStock stock = convertContext.getSwitchStock(warehouseId, dmjItem.getSysItemId(), sysSkuId);
            if (stock == null) {
                String errorMsg = String.format("按实体编码转换,校验可用库存时,找不到对应的库存信息,warehouseId=%s,sysItemId=%s,sysSkuId=%s", warehouseId, dmjItem.getSysItemId(), sysSkuId);
                Logs.ifDebug(LogHelper.buildLog(staff, errorMsg));
                if (null!=convertContext.progressData){
                    convertContext.progressData.getErrorMsg().add(errorMsg);
                }
                if (null!=convertContext.convertParams.getSource() && convertContext.convertParams.getSource()==1){
                    convertContext.addFailReason(order.getSid(),errorMsg);
                }
                return false;
            }
            if (stock.getCurStock() < applyNum) {
                String errorMsg = String.format("按实体编码转换,可用库存不足,warehouseId=%s,sysItemId=%s,sysSkuId=%s,allStock=%s,useStock=%s,curStock=%s", warehouseId, dmjItem.getSysItemId(), sysSkuId, stock.getAllStock(), stock.getUseStock(), stock.getCurStock());
                Logs.ifDebug(LogHelper.buildLog(staff,errorMsg));
                if (null!=convertContext.progressData){
                    convertContext.progressData.getErrorMsg().add(errorMsg);
                }
                if (null!=convertContext.convertParams.getSource() && convertContext.convertParams.getSource()==1){
                    convertContext.addFailReason(order.getSid(),errorMsg);
                }
                return false;
            }
            //这里当前已用过的数量要记录下，已保证后续校验的准确
            stock.setUseStock(stock.getUseStock() + applyNum);
        }
        return true;
    }

    public static boolean needStock(Staff staff, EntityCodeConvertParams convertParams) {
        return convertParams.getSuitsConvertType() == SUIT_2_ENTITY && convertParams.getAllowableStockOut() != 1;
    }

    public static boolean needSwitch(Staff staff, EntityCodeConvertParams convertParams, Order order) {
        // 默认来源订单，这只处理待审核order
        if (convertParams.getSource().equals(0)){
            if (!Trade.SYS_STATUS_WAIT_AUDIT.equals(order.getSysStatus()) || order.getItemSysId() <= 0) {
                return false;
            }
        // 请求来源仓储，已审核未发货的单子也可以执行这个操作
        }else if (convertParams.getSource().equals(1)){
            if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) || order.getItemSysId() <=0) {
                return false;
            }
        }
        if ((SUIT_2_ENTITY == convertParams.getSuitsConvertType() && !order.isSuit()) || (ENTITY_2_SUIT == convertParams.getSuitsConvertType() && order.isSuit())) {
            return false;
        }
        // 实体转套件，勾选 库存不足才进行转换。 过滤掉非缺货order
        if(convertParams.getOnlyInsufficient() == 1 && ENTITY_2_SUIT == convertParams.getSuitsConvertType() && !Trade.STOCK_STATUS_INSUFFICIENT.equals(order.getStockStatus())){
            return false;
        }
        if(order.getRefundStatus().equals(REFUND_WAIT_SELLER_AGREE)){
            return false;
        }
        return true;
    }

    public static Long getSysSkuId(Staff staff, DmjItem dmjItem) {
        DmjSku dmjSku = getDmjSku(staff, dmjItem);
        return dmjSku == null ? -1L : dmjSku.getSysSkuId();
    }

    public static DmjSku getDmjSku(Staff staff, DmjItem dmjItem) {
        DmjSku dmjSku = null;
        List<DmjSku> dmjSkus = dmjItem.getSkus();
        if (CollectionUtils.isNotEmpty(dmjSkus)) {
            if (dmjSkus.size() > 1) {
                Logs.ifDebug(LogHelper.buildLog(staff, String.format("按实体编码转换,出现多个sku默认取第一个,sysItemId=%s,sku.size=%s", dmjItem.getSysItemId(), dmjSkus.size())));
            }
            dmjSku = dmjSkus.get(0);
        }
        return dmjSku;
    }


    public static EntityCodeConvertLog getLog(Staff staff, Order oldOrder, Order newOrder) {
        EntityCodeConvertLog log = new EntityCodeConvertLog();
        log.setCompanyId(staff.getCompanyId());
        log.setSid(oldOrder.getSid());
        log.setOldOrderId(oldOrder.getId());
        log.setOldSysItemId(oldOrder.getItemSysId());
        log.setOldSysSkuId(oldOrder.getSkuSysId());
        log.setNewOrderId(newOrder.getId());
        log.setNewSysItemId(newOrder.getItemSysId());
        log.setNewSysSkuId(newOrder.getSkuSysId());
        Map<String, Object> msg = new HashMap<>();
        msg.put("clueId", ClueIdUtil.getClueId());
        log.setMsg(JSON.toJSONString(msg));
        return log;
    }

}

package com.raycloud.dmj.services.trade.audit.fill;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.audit.AuditUndoBusiness;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.part3.TradeParty3Business;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.trade.audit.TradeUnAuditContext;
import com.raycloud.dmj.domain.trade.audit.TradeUnAuditData;
import com.raycloud.dmj.domain.trade.audit.TradeUnAuditResult;
import com.raycloud.dmj.domain.trade.common.TradeBusinessFillEnum;
import com.raycloud.dmj.domain.trade.common.TradeBusinessFillParams;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.trade.audit.utils.CostRecorder;
import com.raycloud.dmj.services.trade.common.ITradeBusinessFill;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.raycloud.dmj.domain.trade.utils.TradeUtils.getAllTrades;

/**
 * @ClassName TradeAuditUndoFill
 * @Description 订单反审核fill
 * <AUTHOR>
 * @Date 2025/1/7
 * @Version 1.0
 */
@Service
public class TradeUnAuditFill {

    private static final List<TradeBusinessFillEnum> FILL_ENUMS = Lists.newArrayList(TradeBusinessFillEnum.FILL_ORDER, TradeBusinessFillEnum.FILL_TRADE_EXCEPT);

    @Resource
    ITradeBusinessFill tradeBusinessFill;
    @Resource
    IWarehouseService warehouseService;
    @Resource
    FxBusiness fxBusiness;


    public void fill(Staff staff, TradeUnAuditContext context, TradeUnAuditData data, TradeUnAuditResult result, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        Set<Long> mergeSids = new HashSet<>();
        Set<Long> warehouseIds = new HashSet<>();
        Iterator<Trade> iterator = trades.iterator();
        while (iterator.hasNext()) {
            Trade trade = iterator.next();
            if (trade.getEnableStatus() != 1) {
                iterator.remove();
                continue;
            }
            if (trade.getMergeSid() > 0) {
                mergeSids.add(trade.getMergeSid());
            }
            warehouseIds.add(trade.getWarehouseId());
        }
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        CostRecorder recorder = CostRecorder.build("AuditUndoFill", 1000);
        // 填充合单信息
        recorder.exec(() -> tradeBusinessFill.fillMerge(staff, data.getMergeSidTradesMap(), mergeSids));
        // 填充子单、订单异常信息
        recorder.exec(() -> {
            TradeBusinessFillParams fillParams = TradeBusinessFillParams.builder()
                    .mergeSidTradesMap(data.getMergeSidTradesMap())
                    .fillEnums(FILL_ENUMS)
                    .build();
            tradeBusinessFill.fill(staff, trades, fillParams);
        });
        // 三方仓仓库id
        recorder.exec(() -> fillParty3Warehouse(context, warehouseIds));
        // 存在供销订单的分销单sid
        recorder.exec(() -> fillGxTrades(staff, context, data, trades));
        // 供销单生成删除对应分销生成的库存缓存
        recorder.exec(() -> handleGxItemStockCache(staff, context, trades));
        recorder.finish(staff);
    }

    /**
     * 填充仓库信息
     */
    private void fillParty3Warehouse(TradeUnAuditContext context, Set<Long> warehouseIds) {
        if (context.isLocked() || CollectionUtils.isEmpty(warehouseIds)) {
            return;
        }
        List<Warehouse> warehouses = warehouseService.queryByIds(new ArrayList<>(warehouseIds));
        if (CollectionUtils.isEmpty(warehouses)) {
            return;
        }
        for (Warehouse warehouse : warehouses) {
            if (TradeParty3Business.isParty3Warehouse(warehouse)) {
                context.getParty3WarehouseIds().add(warehouse.getId());
            } else if (Warehouse.TYPE_OWN == warehouse.getType()) {
                context.getSysWarehouseIds().add(warehouse.getId());
            }
        }
    }

    /**
     * 存在供销订单的分销单sid
     * @see AuditUndoBusiness#check(Staff, OpEnum, List, TradeConfig, List, List)
     */
    private void fillGxTrades(Staff staff, TradeUnAuditContext context, TradeUnAuditData data, List<Trade> trades) {
        if (context.isLocked() || !context.getConfig().isValidateGxTradeExists() || CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> fxTrades = Lists.newArrayList();
        for (Trade mainTrade : trades) {
            for (Trade trade : getAllTrades(staff, mainTrade, data.getMergeSidTradesMap())) {
                if (TradeUtils.isFxTrade(trade)) {
                    fxTrades.add(trade);
                }
            }
        }
        if (CollectionUtils.isEmpty(fxTrades)) {
            return;
        }
        data.getHasGxTradeSids().addAll(TradeUtils.toSidSet(fxBusiness.getGxTradesByTid(fxTrades)));
    }

    /**
     * 供销单生成删除对应分销生成的库存缓存
     * @see AuditUndoBusiness#check(Staff, OpEnum, List, TradeConfig, List, List)
     */
    private void handleGxItemStockCache(Staff staff, TradeUnAuditContext context, List<Trade> trades) {
        if (context.isLocked() || context.getConfig().isValidateGxTradeExists() || CollectionUtils.isEmpty(trades)) {
            return;
        }
        fxBusiness.handleGxItemStockCache(staff.getCompanyId());
    }
}

package com.raycloud.dmj.services.trade.audit.filter;

import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.dms.domain.dto.DmsDistributorInfoDto;
import com.raycloud.dmj.dms.domain.dto.DmsOrderPriceDto;
import com.raycloud.dmj.dms.domain.dto.DmsSupplierForDisConfigDto;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.SystemTags;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.trade.audit.*;
import com.raycloud.dmj.domain.trades.ExpressCompany;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeResult;
import com.raycloud.dmj.domain.trades.audit.AuditFxSrockData;
import com.raycloud.dmj.domain.trades.payment.util.BigDecimalWrapper;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.newfx.trades.Constants;
import com.raycloud.dmj.services.trade.audit.utils.FilterMsg;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.math.BigDecimal;
import java.util.*;

import static com.raycloud.dmj.domain.trade.audit.TradeAuditUtils.getAuditExceptChineseNames;
import static com.raycloud.dmj.domain.trade.config.TradeConfigEnum.DMS_INTERCEPT_TAG_POSSIBLE_LOSS;
import static com.raycloud.dmj.domain.trade.utils.TradeUtils.getAllTrades;
import static com.raycloud.dmj.domain.trades.utils.TradeStatusUtils.isWaitSellerSend;
import static com.raycloud.dmj.domain.trades.utils.TradeUtils.isDisableUser;
import static com.raycloud.dmj.services.trade.audit.stock.AuditStockUtils.*;

/**
 * @ClassName TradeAuditFxFilter
 * @Description 订单分销审核过滤
 * <AUTHOR>
 * @Date 2024/12/12
 * @Version 1.0
 */
@Service
public class TradeAuditFxFilter {

    @Resource
    IExpressCompanyService expressCompanyService;

    @Resource
    FxBusiness fxBusiness;

    /**
     * 分销过滤
     */
    public boolean filter(Staff staff, TradeAuditContext context, TradeAuditData data, TradeAuditResult result, Trade trade, FilterMsg filterData) {
        if (!TradeUtils.isFxOrMixTrade(trade)) {
            return false;
        }
        Long destId = trade.getDestId();
        if (NumberUtils.nvlLong(destId, 0L) == 0) {
            filterData.addFilterMsg("分销订单供销商Id不符合要求不允许审核", String.format("sid=%s,tid=%s,destId=%s", trade.getSid(), trade.getTid(), destId));
            return true;
        }
        if (trade.getSourceId() - destId == 0) {
            filterData.addFilterMsg("分销订单分销商和供销商相同不允许审核", String.format("sid=%s,tid=%s,destId=%s,sourceId=%s", trade.getSid(), trade.getTid(), destId, trade.getSourceId()));
            return true;
        }
        String exceptNames;
        if (StringUtils.isNotBlank(exceptNames = getExceptNames(staff, context, trade))) {
            filterData.addFilterMsg("分销订单存在异常不允许审核", String.format("excepts:[%s]", exceptNames));
            return true;
        }
        if (isDisableUser(staff, trade)) {
            filterData.addFilterMsg("分销订单店铺已停用");
            return true;
        }
        TradeAuditFxContext fxContext = data.getAuditFxContext();
        Staff gxStaff = fxContext.getDestStaffMap().get(destId);
        if (gxStaff == null) {
            filterData.addFilterMsg("分销订单对应的供销商信息为空不允许审核", String.format("sid=%s,tid=%s,sourceId=%s,destId=%s", trade.getSid(), trade.getTid(), trade.getSourceId(), destId));
            return true;
        }

        DmsSupplierForDisConfigDto dmsConfig = fxContext.getDmsSupplierMap().get(destId);
        if (dmsConfig == null) {
            filterData.addFilterMsg("分销订单供销商关系不存在不允许审核", String.format("sid=%s,tid=%s,sourceId=%s,destId=%s", trade.getSid(), trade.getTid(), trade.getSourceId(), destId));
            return true;
        }

        TradeAuditConfig config = context.getTradeConfig();
        if(context.getOpEnum()!=null && context.getOpEnum() == OpEnum.AUTO_AUDIT && config.isOpen(DMS_INTERCEPT_TAG_POSSIBLE_LOSS) && TradeTagUtils.checkIfExistTag(trade, SystemTags.TAG_FX_POSSIBLE_LOSS) && Integer.valueOf(0).equals(trade.getIsAutoAudit())){
            // isAutoAudit订单这边没有处理好先不记录tradetrace
            // filterData.addTraceAndFilterMsg(staff,auditContext.getOpEnum(),trade, "订单自动审核失败，原因：订单有可能亏损的标签。如需继续审核请手动操作审核，或者关掉【拦截亏损订单】的配置","开启亏损拦截配置不自动推单,订单有可能亏损的标签不允许自动审核", String.format("sid=%s,tid=%s,sourceId=%s,destId=%s", trade.getSid(), trade.getTid(), trade.getSourceId(), destId));
            filterData.addFilterMsg("开启亏损拦截配置不自动推单,订单有可能亏损的标签不允许自动审核", String.format("sid=%s,tid=%s,sourceId=%s,destId=%s", trade.getSid(), trade.getTid(), trade.getSourceId(), destId));
            return true;
        }
        //校验分销快递在供销是否存在
        if (dmsConfig.getDesignatedExpress() == 1 && (trade.getTemplateId() != null && trade.getTemplateId() > 0L)) {
            ExpressCompany company = expressCompanyService.getExpressCompanyById(trade.getExpressCompanyId());
            if (company == null) {
                filterData.addFilterMsg("分销订单快递公司不存在不允许审核", String.format("sid=%s,tid=%s,sourceId=%s,destId=%s,expressCompanyId=%s,templateId=%s,templateName=%s", trade.getSid(), trade.getTid(), trade.getSourceId(), destId, trade.getExpressCompanyId(), trade.getTemplateId(), trade.getTemplateName()));
                return true;
            }
            Set<Long> gxExpressIds = fxContext.getDmsExpressIdsMap().get(destId);
            if (CollectionUtils.isEmpty(gxExpressIds) || !gxExpressIds.contains(company.getId())) {
                filterData.addFilterMsg("分销订单快递模版对应的快递公司在供销系统不存在不允许审核", String.format("sid=%s,tid=%s,sourceId=%s,destId=%s,expressCompanyId=%s,templateId=%s,templateName=%s", trade.getSid(), trade.getTid(), trade.getSourceId(), destId, trade.getExpressCompanyId(), trade.getTemplateId(), trade.getTemplateName()));
                return true;
            }
        }
        //代发分销店铺设置,中间状态不校验
        if (!TradeUtils.isGxAndFxTrade(trade)) {
            Set<Long> dfUserIds = fxContext.getDmsShopUserIdsMap().get(destId);
            if (CollectionUtils.isNotEmpty(dfUserIds) && !dfUserIds.contains(trade.getUserId())) {
                filterData.addFilterMsg("分销订单分销订单店铺不属于供销商的代发店铺不允许审核", String.format("sid=%s,tid=%s,sourceId=%s,destId=%s,userId=%s,dfUserIds=%s", trade.getSid(), trade.getTid(), trade.getSourceId(), destId, trade.getUserId(), dfUserIds));
                return true;
            }
        }
        //分销余额校验
        if (filterBalance(staff, fxContext, data, result, trade, dmsConfig, filterData)) {
            return true;
        }

        //未包含分销强推分销商品未匹配
        if (!TradeUtils.isContainV(trade, TradeConstants.V_IF_FX_FORCE_PUSH_UNALLOCATED)) {
            //分销价格校验
            if (filterPrice(staff, fxContext, data, trade, dmsConfig, filterData)) {
                return true;
            }
            //分销库存校验 // KMERP-264022: 分销订单需要标记缺货异常
            if (filterStock(staff, context, fxContext, data, trade, dmsConfig, gxStaff, filterData)) {
                return true;
            }
        }
        return false;
    }

    private boolean filterBalance(Staff staff, TradeAuditFxContext fxContext, TradeAuditData data, TradeAuditResult result, Trade trade, DmsSupplierForDisConfigDto dmsConfig, FilterMsg filterData) {
        DmsDistributorInfoDto dmsDistributorInfoDto = fxContext.getDmsDistributorInfoMap().get(trade.getDestId());
        if (dmsDistributorInfoDto == null) {
            filterData.addFilterMsg("分销订单对应的供销商维护的分销商信息为空不允许审核", String.format("sid=%s,tid=%s,sourceId=%s,destId=%s", trade.getSid(), trade.getTid(), trade.getSourceId(), trade.getDestId()));
            return true;
        }
        // 账户余额
        BigDecimal balance = new BigDecimal(dmsDistributorInfoDto.getTotalBalance() == null ? "0" : dmsDistributorInfoDto.getTotalBalance() + "");
        // 已使用余额
        BigDecimal use = fxContext.getUseBalance().getOrDefault(trade.getDestId(), BigDecimal.ZERO);
        // 总共需要的余额
        BigDecimal sum = fxContext.getSumBalance().getOrDefault(trade.getDestId(), BigDecimal.ZERO);
        // 当前订单需要的余额
        List<Order> orders = com.raycloud.dmj.domain.trade.utils.TradeUtils.getOrders(staff, trade, data.getMergeSidTradesMap());
        BigDecimal saleFee = fxBusiness.getFxTotalSaleFee(trade, orders, fxContext.isCostSourceFx(), false, true);
        Map<String, Double> cashFlowMap = fxContext.cashFlowMap != null ? fxContext.cashFlowMap.get(trade.getDestId()) : new HashMap<>();
        //流水
        String tid = String.valueOf(trade.getSid());
        double amount = cashFlowMap != null ? cashFlowMap.getOrDefault(tid, 0D) : 0D;
        if (amount != 0D) {
            cashFlowMap.put(tid, 0D);
        }
        balance = balance.add(new BigDecimal(String.valueOf(amount)).multiply(new BigDecimal(-1)));
        // 总金额累加，用于日志输出
        sum = sum.add(saleFee);
        fxContext.getSumBalance().put(trade.getDestId(), sum);

        //余额不足
        if (MathUtils.scaleUp(balance).compareTo(MathUtils.scaleUp(use.add(saleFee))) < 0) {
            filterData.addFilterMsg("分销订单账号余额不足不允许审核", String.format("sid=%s,tid=%s,sourceId=%s,destId=%s,balance=%s,useBalance=%s,saleFee=%s", trade.getSid(), trade.getTid(), trade.getSourceId(), trade.getDestId(), balance, use.doubleValue(), saleFee));
            fillBalanceResult(result, trade, sum, balance);
            return true;
        }
        // 需要等后续审核过滤逻辑都执行完，才能算进去
        // auditFxContext.getUseBalance().put(trade.getDestId(), use.add(saleFee));
        fxContext.getTradeSaleFee().put(trade.getSid(), saleFee);
        return false;
    }

    private void fillBalanceResult(TradeAuditResult result, Trade trade, BigDecimal sum, BigDecimal balance) {
        TradeResult tradeResult = new TradeResult();
        tradeResult.setSid(-1L);
        tradeResult.setDestId(trade.getDestId());
        tradeResult.setSuccess(false);
        tradeResult.setErrorMsg("分销账户余额不足,(分销订单总分销价" + sum.setScale(2, RoundingMode.HALF_UP) + "元,账户余额" + balance.setScale(2, RoundingMode.HALF_UP) + "元，差额:" + (sum.subtract(balance).setScale(2, RoundingMode.HALF_UP)) + "元)");
        result.getAuditTradeResultMap().put(-1L, tradeResult);
    }

    private boolean filterPrice(Staff staff, TradeAuditFxContext fxContext, TradeAuditData data, Trade trade, DmsSupplierForDisConfigDto dmsConfig, FilterMsg filterData) {
        List<Order> orders = com.raycloud.dmj.domain.trade.utils.TradeUtils.getOrders(staff, trade, data.getMergeSidTradesMap());
        //如果开启了不校验分销价，则允许推送
        boolean isAllowZero = dmsConfig.getAllowZeroDistributorPrice() != null && dmsConfig.getAllowZeroDistributorPrice();
        int useSupplierCode = NumberUtils.nvlInteger(fxContext.getDmsDistributorConfigDto().getUseSupplierCode(), 0);
        //查询商家编码或者供应商商家编码
        Map<Long, DmsOrderPriceDto> dmsOuterSupplierIdDtoMap;
        if (useSupplierCode == 1 || CommonConstants.PLAT_FORM_TYPE_FXXZ.equals(trade.getSubSource())) {
            dmsOuterSupplierIdDtoMap = fxContext.getDmsOrderIdPriceSupplierMap();
        } else {
            dmsOuterSupplierIdDtoMap = fxContext.getDmsOrderIdPriceMap();
        }

        //销售价总和
        double sumOrderPrice = 0.00D;
        double controlPrice = 0.00D;

        BigDecimalWrapper tradeCost = new BigDecimalWrapper();
        BigDecimalWrapper tradeSaleFee = new BigDecimalWrapper();
        for (Order order : orders) {
            sumOrderPrice += NumberUtils.str2Double(order.getPrice(), 0D);
            if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                continue;
            }
            DmsOrderPriceDto priceDto = dmsOuterSupplierIdDtoMap.get(order.getId());
            if (priceDto == null || StringUtils.isBlank(priceDto.getOuterId())) {
                filterData.addFilterMsg("商家编码或者对应的供应商商家编码不存在!", String.format("sid=%s,tid=%s,destId=%s,orderId=%s", trade.getSid(), trade.getTid(), trade.getDestId(), order.getId()));
                return true;
            }
            if (useSupplierCode == 1 && (Constants.FxDefaultSysSkuId.equals(order.getItemSysId()) || Constants.FxDefaultSysSkuId.equals(order.getSkuSysId()))) {
                filterData.addFilterMsg("销售未授权商品,不支持自动标记分销标记且按供应商商家编码匹配策略! 商家编码:" + priceDto.getOuterId(), String.format("sid=%s,tid=%s,destId=%s,orderId=%s,itemSysId=%s,skuSysId=%s", trade.getSid(), trade.getTid(), trade.getDestId(), order.getId(), order.getItemSysId(), order.getSkuSysId()));
                return true;
            }
            if (!priceDto.isAllowSale()) {
                filterData.addFilterMsg("商品不允许分销！商家编码:" + priceDto.getOuterId(), String.format("sid=%s,tid=%s,destId=%s,orderId=%s,dmsAllowSale=%s", trade.getSid(), trade.getTid(), trade.getDestId(), order.getId(), priceDto.isAllowSale()));
                return true;
            }
            double dmsPrice = NumberUtils.nvlDouble(priceDto.getPrice(), 0D);
            if (!isAllowZero && dmsPrice == 0) {
                filterData.addFilterMsg("商品未配置分销价格，请在供销商设置！商家编码:" + priceDto.getOuterId(), String.format("sid=%s,tid=%s,destId=%s,orderId=%s,isAllowZero=%s,dmsPrice=%s", trade.getSid(), trade.getTid(), trade.getDestId(), order.getId(), isAllowZero, dmsPrice));
                return true;
            }
            double v = priceDto.getControlPrice() == null ? 0 : priceDto.getControlPrice();
            controlPrice += v;
            if (!order.isSuit(true)) {
                if (fxContext.isSalePriceSourceFx()) {
                    order.setSalePrice(String.valueOf(dmsPrice));
                    order.setSaleFee(MathUtils.toString(MathUtils.multiply(dmsPrice, order.getNum())));
                    tradeSaleFee.add(order.getSaleFee());
                }
                if (fxContext.isCostSourceFx()) {
                    order.setCost(dmsPrice);
                    tradeCost.add(MathUtils.multiply(dmsPrice, order.getNum()));
                }
            }
        }
        if (sumOrderPrice - controlPrice < 0) {
            filterData.addFilterMsg("分销订单所有商品销售价低于等级管控销售价不允许审核", String.format("sid=%s,tid=%s,destId=%s,sumOrderPrice=%s,controlPrice=%s", trade.getSid(), trade.getTid(), trade.getDestId(), sumOrderPrice, controlPrice));
            return true;
        }
        //这里取分销价当作成本价,只计算了订单本身的成本 对于合单的处理见 com.raycloud.dmj.business.audit.AuditFxBusiness.recalculateCost
        if (fxContext.isSalePriceSourceFx()) {
            trade.setSalePrice(tradeSaleFee.getString());
        }
        if (fxContext.isCostSourceFx()) {
            trade.setCost(tradeCost.getDouble());
        }
        return false;
    }

    private boolean filterStock(Staff staff, TradeAuditContext context, TradeAuditFxContext fxContext, TradeAuditData data, Trade trade, DmsSupplierForDisConfigDto dmsConfig, Staff gxStaff, FilterMsg filterData) {
        int stockCheck = NumberUtils.nvlInteger(dmsConfig.getStockCheck(), 0);
        if (stockCheck < 1) {
            clearTradeInsufficient(staff, data, trade);
            return false;
        }

        Map<String, AuditFxSrockData> fxStockMap = fxContext.getStockMap().get(trade.getDestId());
        if (fxStockMap == null || fxStockMap.size() == 0) {
            fillFxInsufficientOrders(staff, data, trade);
            fillInsufficientTrade(staff, data, trade);
            String message = String.format("供销商[%s]未返回库存数据，请重试！", trade.getDestId());
            filterData.addFilterMsg(message);
            context.getAuditTradeTraces().addTradeTrace(context.getOpEnum(), TradeTraceUtils.createTradeTraceWithTrade(staff, trade, context.getOpEnum().getName(), staff.getName(), context.getOpDate(), message));
            return true;
        }

        DmsDistributorInfoDto dmsDistributorInfoDto = fxContext.getDmsDistributorInfoMap().get(gxStaff.getCompanyId());
        Map<String, Integer> stockUseMap = fxContext.getDmsStockUseMap().computeIfAbsent(gxStaff.getCompanyId(), k -> new HashMap<>()).computeIfAbsent(dmsConfig.getWarehouseId(), k -> new HashMap<>());
        Map<String, Integer> stockTmpMap = new HashMap<>();
        Set<String> stockUnEnoughList = new HashSet<>();
        Set<String> stockEmptyList = new HashSet<>();
        boolean ifStockError = filterStock(staff, context, data, trade, stockCheck, dmsDistributorInfoDto, fxStockMap, stockUseMap, stockTmpMap, stockUnEnoughList, stockEmptyList);
        if (ifStockError) {
            StringBuilder msg = new StringBuilder();
            if (CollectionUtils.isNotEmpty(stockUnEnoughList)) {
                msg.append("分销商品[").append(StringUtils.join(stockUnEnoughList, ",")).append("]在供销库存不足;");
            }
            if (CollectionUtils.isNotEmpty(stockEmptyList)) {
                msg.append("分销商品[").append(StringUtils.join(stockEmptyList, ",")).append("]在供销库存不存在;");
            }
            filterData.addFilterMsg("分销商品库存异常");
            context.getAuditTradeTraces().addTradeTrace(context.getOpEnum(), TradeTraceUtils.createTradeTraceWithTrade(staff, trade, context.getOpEnum().getName(), staff.getName(), context.getOpDate(), String.format("供销商[%s]开启库存校验，%s", trade.getDestId(), msg)));
            return true;
        }

        if (stockTmpMap.size() > 0) {
            stockTmpMap.forEach((fxOuterId, useNum) -> stockUseMap.put(fxOuterId, NumberUtils.nvlInteger(stockUseMap.get(fxOuterId), 0) + useNum));
        }
        return false;
    }

    private static boolean filterStock(Staff staff, TradeAuditContext context, TradeAuditData data, Trade trade, int stockCheck, DmsDistributorInfoDto dmsDistributorInfoDto, Map<String, AuditFxSrockData> fxStockMap, Map<String, Integer> stockUseMap, Map<String, Integer> stockTmpMap, Set<String> stockUnEnoughList, Set<String> stockEmptyList) {
        boolean ifStockError = false;
        boolean breakError = context.getOpEnum() == OpEnum.AUTO_AUDIT || context.getOpEnum() == OpEnum.MANUAL_AUTO_AUDIT;
        for (Trade t : getAllTrades(staff, trade, data.getMergeSidTradesMap())) {
            if (filterStock(staff, data, t, TradeUtils.getOrders4Trade(t), breakError, stockCheck, dmsDistributorInfoDto, fxStockMap, stockUseMap, stockTmpMap, stockUnEnoughList, stockEmptyList)) {
                fillInsufficientTrade(staff, data, t);
                ifStockError = true;
            } else {
                clearTradeInsufficient(staff, t);
            }

            if (breakError && ifStockError) {
                break;
            }
        }
        if (ifStockError) {
            fillInsufficientTrade(staff, data, trade);
        }
        return ifStockError;
    }

    private static boolean filterStock(Staff staff, TradeAuditData data, Trade trade, List<Order> orders, boolean breakError, int stockCheck, DmsDistributorInfoDto dmsDistributorInfoDto, Map<String, AuditFxSrockData> fxStockMap, Map<String, Integer> stockUseMap, Map<String, Integer> stockTmpMap, Set<String> stockEmptyList, Set<String> stockUnEnoughList) {
        boolean ifStockError = false;
        for (Order order : orders) {
            if (order.isVirtual() || order.ifNonConsign() || !isWaitSellerSend(order.getSysStatus())) {
                clearOrderInsufficient(staff, order);
                continue;
            }
            String fxOuterId = OrderUtils.getFxTrueOuterId(trade, order);
            AuditFxSrockData auditFxSrockData = fxStockMap.get(fxOuterId);
            if (auditFxSrockData == null) {
                fillInsufficientOrder(staff, data, order);
                ifStockError = true;
                stockEmptyList.add(fxOuterId);
                if (breakError) {
                    break;
                } else {
                    continue;
                }
            }
            long stockNum = NumberUtils.nvlLong(auditFxSrockData.getAvailableStock(), 0L);
            if (stockCheck == 3) {
                if (dmsDistributorInfoDto != null && dmsDistributorInfoDto.getSyncRatio() != null) {
                    stockNum = (long) Math.floor(stockNum * dmsDistributorInfoDto.getSyncRatio());
                }
            } else if (stockCheck == 2) {
                stockNum += NumberUtils.nvlLong(auditFxSrockData.getPurchaseNum(), 0L);
            }

            int tmpNum = NumberUtils.nvlInteger(stockTmpMap.get(fxOuterId), 0);
            int useNum = NumberUtils.nvlInteger(stockUseMap.get(fxOuterId), 0);
            stockNum = stockNum - tmpNum - useNum;
            if (stockNum < order.getNum()) {
                fillInsufficientOrder(staff, data, order);
                ifStockError = true;
                stockUnEnoughList.add(fxOuterId);
                if (breakError) {
                    break;
                } else {
                    continue;
                }
            }
            clearOrderInsufficient(staff, order);
            tmpNum += order.getNum();
            stockTmpMap.put(fxOuterId, tmpNum);
        }
        return ifStockError;
    }

    public static String getExceptNames(Staff staff, TradeAuditContext context, Trade trade) {
        // 已完成，已关闭订单不算异常订单
        if (Trade.SYS_STATUS_FINISHED.equals(trade.getSysStatus()) || Trade.SYS_STATUS_CLOSED.equals(trade.getSysStatus())) {
            return null;
        }
        List<String> exceptNames = getAuditExceptChineseNames(staff, trade, context.getExceptNameMap());
        exceptNames.remove(ExceptEnum.INSUFFICIENT.getChinese());    // KMERP-264022: 分销订单不校验缺货异常
        return CollectionUtils.isNotEmpty(exceptNames) ? String.join(",", exceptNames) : null;
    }
}

package com.raycloud.dmj.business.modify;

import com.raycloud.dmj.dao.item.OperateChangeLogDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.OperateChangeLog;
import com.raycloud.dmj.domain.item.request.OperateChangeLogRequest;
import com.raycloud.dmj.domain.item.response.OperateChangeLogResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/11/24 下午4:48
 **/
@Service
public class OperateChangeLogBusiness {

    @Resource
    OperateChangeLogDao operateChangeLogDao;

    public OperateChangeLogResponse<OperateChangeLog> queryLog(Staff staff, OperateChangeLogRequest request) {
        Assert.notNull(staff.getCompanyId(), "公司不能为空！");

        int pageNo = request.getPageNo() == null ? Page.DEFAULT_PAGE_NUM : request.getPageNo();
        int pageSize = request.getPageSize() == null ? Page.DEFAULT_PAGE_SIZE : request.getPageSize();
        request.setStartRow((pageNo - 1) * pageSize);

        List<OperateChangeLog> list = operateChangeLogDao.queryByCondition(staff, request);
        Integer countAll = operateChangeLogDao.countAll(staff, request);

        OperateChangeLogResponse<OperateChangeLog> response = new OperateChangeLogResponse<>();
        response.setList(list);
        response.setPageNo(request.getPageNo());
        response.setPageSize(request.getPageSize());
        response.setTotal(countAll);

        return response;
    }
}

package com.raycloud.dmj.services.ec;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.dao.trade.TbTradeDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeBuilderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.trades.ITradeUpdateService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.*;
import com.raycloud.ec.api.annotation.ListenerBind;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020-04-09 10:37
 * @Description 货到付款标记异常
 */
@Component
@ListenerBind("trade.cod.repeat.batch")
public class RepeatCodTradeExcepListener implements IEventListener {

    @Resource
    TbTradeDao tbTradeDao;

    @Resource(name = "tbTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    IEventCenter eventCenter;

    @Override
    public void onObserved(EventSourceBase source) {
        CommonEventSource evt = (CommonEventSource) source;
        Staff staff = evt.getArg(0, Staff.class);
        Long[] sids = evt.getArg(1, Long[].class);
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, sids);
        handleRepeat(staff, trades);
    }

    public List<Long> handleRepeat(Staff staff, Long[] sids) {
        return handleRepeat(staff, tradeSearchService.queryBySidsContainMergeTrade(staff, true, sids));
    }

    public List<Long> handleRepeat(Staff staff, List<Trade> trades) {
        List<Long> successSids = Lists.newArrayList();
        try {
            Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("接收到货到付款标记异常事件[trades=%s]", JSON.toJSONString(trades))));
            List<List<Trade>> partition = Lists.partition(trades, 200);
            for (List<Trade> tradeList : partition) {
                List<Trade> needHandleTrades = Lists.newArrayList();
                Map<Long, List<Trade>> userTradeMap = tradeList.stream().collect(Collectors.groupingBy(Trade::getUserId));
                for (Map.Entry<Long, List<Trade>> next : userTradeMap.entrySet()) {
                    Long userId = next.getKey();
                    List<Trade> exsitTrades = next.getValue();
                    List<String> buyerNicks = TradeUtils.getBuyerNicks(exsitTrades);
                    Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("[buyerNicks=%s]", JSON.toJSONString(buyerNicks))));
                    List<TbTrade> tempTrades = tbTradeDao.queryCodRepeat(staff, userId, buyerNicks);
                    Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("[tbTradeDao_tbTrades=%s]", JSON.toJSONString(tempTrades))));
                    if (exsitTrades.size() > 1) {
                        checkSelf(staff, exsitTrades, needHandleTrades);
                    }
                    if (CollectionUtils.isNotEmpty(tempTrades)) {
                        checkOther(staff, tempTrades, exsitTrades, needHandleTrades);
                    }
                }
                Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("[needHandleTrades=%s]", TradeUtils.toSidList(needHandleTrades))));
                //标记异常
                if (CollectionUtils.isNotEmpty(needHandleTrades)) {
                    List<Trade> toUpdateTrades = Lists.newArrayListWithCapacity(needHandleTrades.size());
                    //排出已标记的
                    needHandleTrades.stream().filter(existTrade -> !TradeExceptUtils.isContainExcept(staff, existTrade, ExceptEnum.COD_REPEAT)).forEach(existTrade -> {
                        Trade tbTrade = TradeBuilderUtils.builderUpdateTrade(existTrade);
                        tbTrade.setSid(existTrade.getSid());
                        tbTrade.setIsExcep(1);
                        tbTrade.setCompanyId(existTrade.getCompanyId());
                        TradeExceptUtils.updateExcept(staff, tbTrade, ExceptEnum.COD_REPEAT, 1L);
                        tbTrade.setTradeTrace("货到付款订单重复异常");
                        toUpdateTrades.add(tbTrade);
                    });
                    Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("[toUpdateTrades=%s]", TradeUtils.toSidList(toUpdateTrades))));
                    if (CollectionUtils.isNotEmpty(toUpdateTrades)) {
                        tradeUpdateService.updateTrades(staff, toUpdateTrades);
                        successSids.addAll(TradeUtils.toSidList(toUpdateTrades));
                        //记录日志
                        eventCenter.fireEvent(this, new EventInfo("trade.cod.repeat.excep").setArgs(new Object[]{staff}), toUpdateTrades);
                    }
                }
            }
            return successSids;
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(staff, e, "处理货到付款订单失败！"), e);
            return successSids;
        }
    }

    private void checkSelf(Staff staff, List<Trade> exsitTrades, List<Trade> needHandleTrades) {
        Map<String, List<Trade>> exsitMap = groupByCondition(staff, null, exsitTrades);
        for (Map.Entry<String, List<Trade>> next : exsitMap.entrySet()) {
            List<Trade> value = next.getValue();
            if (value.size() > 1) {
                for (int i = 1; i < value.size(); i++) {
                    needHandleTrades.add(value.get(i));
                }
            }
        }

    }

    private void checkOther(Staff staff, List<TbTrade> tempTrades, List<Trade> exsitTrades, List<Trade> needHandleTrades) {
        List<Trade> tbTrades = tradeSearchService.queryBySidsContainMergeTrade(staff, true, TradeUtils.toSids(tempTrades));
        Map<String, List<Trade>> exsitMap = groupByCondition(staff, null, exsitTrades);
        Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("[exsitMap=%s]", JSON.toJSONString(exsitMap))));
        Map<String, List<Trade>> stringListMap = groupByCondition(staff, TradeUtils.toSidList(exsitTrades), tbTrades);
        Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("[stringListMap=%s]", JSON.toJSONString(stringListMap))));
        Set<String> strings = stringListMap.keySet();
        for (String string : strings) {
            if (exsitMap.containsKey(string)) {
                needHandleTrades.addAll(exsitMap.get(string));
            }
        }
    }

    private Map<String, List<Trade>> groupByCondition(Staff staff, List<Long> exsitSid, List<? extends Trade> tbTrades) {
        Map<String, List<Trade>> map = new HashMap<>();
        for (Trade tbTrade : tbTrades) {
            if (exsitSid != null) {
                if (exsitSid.contains(tbTrade.getSid())) {
                    continue;
                }
            }
            //跳过已经标记重复异常的订单
            if (TradeExceptUtils.isContainExcept(staff, tbTrade, ExceptEnum.COD_REPEAT)) {
                continue;
            }
            String mergeKey = commonMergeKey(tbTrade);
            map.computeIfAbsent(mergeKey, k -> new ArrayList<>()).add(tbTrade);
        }
        return map;
    }

    String commonMergeKey(Trade trade) {
        return StringUtils.stripToEmpty(trade.getReceiverAddress()) +
                StringUtils.stripToEmpty(trade.getReceiverName()) +
                StringUtils.stripToEmpty(trade.getReceiverPhone()) +
                StringUtils.stripToEmpty(trade.getReceiverMobile()) +
                StringUtils.join(itemIds(trade), ",");
    }

    private HashSet<Long> itemIds(Trade trade) {
        List<Order> orders = TradeUtils.getOrders4Trade(trade);
        List<Long> ids = Lists.newArrayList();
        for (Order order : orders) {
            //非赠品
            if (order.getGiftNum() == null || order.getGiftNum() == 0) {
                ids.add(order.getItemSysId());
                ids.add(order.getSkuSysId());
            }
        }
        return new HashSet<>(ids);
    }
}

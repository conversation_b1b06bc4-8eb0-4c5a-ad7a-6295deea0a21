package com.raycloud.dmj.services.item;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.fx.FxBusiness;
import com.raycloud.dmj.business.modify.ModifyParentBusiness;
import com.raycloud.dmj.business.operate.MoneyChangeBusiness;
import com.raycloud.dmj.business.order.OrderModifyLogBusiness;
import com.raycloud.dmj.business.payment.support.PaymentCalculateSupports;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.business.payment.TradeItemParsePaymentBusinessService;
import com.raycloud.dmj.business.payment.TradeSuitCalculateBusiness;
import com.raycloud.dmj.domain.account.Conf;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.base.AttrCopier;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.enums.Feature;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.OrderModifyLogTypeEnum;
import com.raycloud.dmj.domain.enums.StockStatusEnum;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.tag.TradeTagRule;
import com.raycloud.dmj.domain.trade.history.OrderModifyLogUtils;
import com.raycloud.dmj.domain.trade.common.TradeBusinessEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.entity.ItemKeywordParse;
import com.raycloud.dmj.domain.trade.history.TradeHistoryUtils;
import com.raycloud.dmj.domain.trade.item.ItemMatchResult;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.item.search.dto.ItemIdInfoDto;
import com.raycloud.dmj.newfx.trades.Constants;
import com.raycloud.dmj.services.basis.ISupplierService;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.item.utils.ItemMatchUtils;
import com.raycloud.dmj.services.item.utils.ItemParsePaymentUtils;
import com.raycloud.dmj.services.stock.ITradeStockService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.IdWorkerService;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.utils.BeanCopierUtil;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.TradeLocalConfigurable;
import com.raycloud.dmj.services.utils.item.TradeItemContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by CXW on 16/8/4.
 */
@Service
@Slf4j
public class ItemMatchService implements IItemMatchService {

    private static final int BATCH_MATCH_SIZE = 100;

    private final AttrCopier<Order, TbOrder> orderCopier = new OrderCopier<>();

    private final static String MATCH_TYPE_WITH_OUTER_ID = "matchSysItemsWithOuterIds";

    public final static String MATCH_TYPE_WITH_RELATION = "matchSysItemsWithRelation";

    private final static String ConfigUseItemOuterIdMatchOrder = "useItemOuterIdMatchOrder";

    @Resource
    ITradeStockService tradeStockService;

    @Resource
    IdWorkerService idWorkerService;

    @Resource
    TradeLocalConfigurable tradeLocalConfigurable;

    @Resource
    ModifyParentBusiness modifyParentBusiness;

    @Resource
    ITradeConfigService tradeConfigService;

    @Resource
    FxBusiness fxBusiness;

    @Resource
    private ISupplierService supplierService;
    @Resource
    MoneyChangeBusiness moneyChangeBusiness;
    @Resource
    OrderModifyLogBusiness orderModifyLogBusiness;

    @Resource
    TradeItemParsePaymentBusinessService tradeItemParsePaymentBusinessService;

    @Resource
    TradeSuitCalculateBusiness tradeSuitCalculateBusiness;

    @Resource
    FeatureService featureService;

    @Resource
    PaymentCalculateSupports paymentCalculateSupports;

    private final Logger logger = Logger.getLogger(this.getClass());


    @Override
    public List<Trade> matchTradeSysItem(User user, List<Trade> trades) {
        return matchTradeSysItem(user, trades, null, false);
    }


    @Override
    public List<Trade> matchTradeSysItem(User user, List<Trade> trades, TradeImportResult result, Boolean isInsert) {
        TradeItemContext tradeItemContext = new TradeItemContext();
        List<Order> toMatchOrders = new ArrayList<>();//存放要匹配系统商品的原始子订单
        List<Trade> toMatchTrades = filterUnMatchedTrades(user, trades, toMatchOrders, result != null ? result.orderIdLogsMap : null, isInsert);
        List<Trade> matchedTrades = new ArrayList<>(toMatchTrades.size());
        if (!toMatchTrades.isEmpty()) {
            TradeConfig tradeConfig = tradeConfigService.get(user.getStaff());

            ItemMatchResult matchResult = new ItemMatchResult(user, toMatchTrades, toMatchOrders, getMatchType(user,toMatchTrades), isInsert);
            List<Order> matchedList = matchSysItems(matchResult);
            handleOrders(matchResult,tradeItemContext);
            if (result != null) {
                //订单同步获取商品停用状态，后续判断是否需要进行停用拆单、标记 https://tb.raycloud.com/task/609c7c66933f542a1a893092
                filterItemInactiveOrders(toMatchOrders, result);
                filterItemGoodsShellfOffOrders(user, matchedList, result);
            }

            orderSupplierConfig(user.getStaff(), matchedList, OrderUtils.toFullOrderList(TradeUtils.getOrders4Trade(toMatchTrades), false));

            Set<Long> matchedSidSet = matchedList.stream().map(OrderBase::getSid).collect(Collectors.toSet());
            for (Trade trade : toMatchTrades) {
                Set<Long> supplierIdSet = TradeUtils.getOrders4Trade(trade).stream()
                        .filter(order -> CollectionUtils.isNotEmpty(order.getSupplierIds()))
                        .flatMap(order -> order.getSupplierIds().stream()).collect(Collectors.toSet());
                trade.setSupplierIds(supplierIdSet);
                TradeStockUtils.resetTradeStockStatus(user.getStaff(), trade, TradeUtils.getOrders4Trade(trade), false, tradeConfig);//确定是否有子订单匹配过系统商品
                if (matchedSidSet.contains(trade.getSid())) {
                    matchedTrades.add(trade);
                }
                trade.setNetWeight(TradeUtils.calculateTradeNetWeight(trade));
                trade.setCost(TradeUtils.calculateCost(trade));
                trade.setVolume(TradeUtils.calculateVolume(trade));
                if (!CommonConstants.PLAT_FORM_TYPE_FX.equals(trade.getSource())) {
                    trade.setSaleFee(TradeUtils.calculateTradeSaleFee(trade));
                }
            }
            //理论重量是否覆盖实际重量
            modifyParentBusiness.isCoverWeight(user.getStaff(), toMatchTrades);
        }

        getItemSupplier(user,trades);

        moneyChangeBusiness.tradeUpdateMoneyLog(user.getStaff(),OpEnum.MONEY_CHANGE_BEFORE,OpEnum.TRADE_UPDATE,trades);
        return matchedTrades;
    }



    /**
     * 获取匹配模式
     */
    private String getMatchType(User user,List<Trade> toMatchTrades){
        String forceUseMatchType = null;
        Trade firstTrade = toMatchTrades.get(0);
        //如果是供销平台订单、奇门订单，强制走E匹配方案; 拼多多代打订单走E匹配方案
        if((CommonConstants.PLAT_FORM_TYPE_JD.equals(firstTrade.getSource()) && CommonConstants.PLAT_FORM_TYPE_JD_GXPT.equals(firstTrade.getSubSource()))
                || CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(user.getSource())
                || CommonConstants.PLAT_FORM_TYPE_SELF_BUILT.equals(firstTrade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_SELF_BUILT.equals(user.getSource())
                || (CommonConstants.PLAT_FORM_TYPE_PDD.equals(firstTrade.getSource()) && CommonConstants.PLAT_FORM_TYPE_FDS.equals(firstTrade.getSubSource()))) {
            forceUseMatchType = MATCH_TYPE_WITH_OUTER_ID;
        }
        return forceUseMatchType;
    }

    private void getItemSupplier(User user,List<Trade> trades){
        if (user.getCompanyId() != 19982L && user.getCompanyId() != 33042L) {
            List<Order> originOrderList = TradeUtils.getOrders4Trade(trades);
            if (CollectionUtils.isNotEmpty(originOrderList)) {
                List<Order> matchOrderList = originOrderList.stream()
                        .filter(order -> !StockStatusEnum.STOCK_STATUS_UNALLOCATED.stockStatus.equals(order.getStockStatus()) && order.getItemSysId() > 0)
                        .collect(Collectors.toList());

                orderSupplierConfig(user.getStaff(), matchOrderList, originOrderList);

                trades.forEach(trade -> {
                    Set<Long> supplierIdSet = TradeUtils.getOrders4Trade(trade).stream()
                            .filter(order -> CollectionUtils.isNotEmpty(order.getSupplierIds()))
                            .flatMap(order -> order.getSupplierIds().stream()).collect(Collectors.toSet());
                    trade.setSupplierIds(supplierIdSet);
                });
            }
        }
    }

    private void filterItemInactiveOrders(List<Order> toMatchOrders, TradeImportResult result) {
        List<Order> itemInactiveOrders = toMatchOrders.stream().filter(order -> !Objects.isNull(order.getItemActiveStatus()) && order.getItemActiveStatus().equals(0)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(itemInactiveOrders)){
            result.itemInactiveOrders.addAll(itemInactiveOrders);
        }
    }

    private void filterItemGoodsShellfOffOrders(User user, List<Order> toMatchOrders, TradeImportResult result) {
        List<Order> itemInactiveOrders = toMatchOrders.stream().filter(order -> !Objects.isNull(order.getItemGoodsStatus()) && order.getItemGoodsStatus().equals(0)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(itemInactiveOrders)){
            result.itemShelfOffOrders.addAll(itemInactiveOrders);
        }
    }

    /**
     * 商品供应商匹配
     */
    @Override
    public void orderSupplierConfig(Staff staff, List<Order> matchedList, List<Order> originOrderList) {
        try {
            if (CollectionUtils.isEmpty(matchedList) || CollectionUtils.isEmpty(originOrderList)) {
                return;
            }
            Set<Long> sysItemIdList = new HashSet<>();
            Set<Long> sysSkuIdList = new HashSet<>();
            List<Order> needSupplierOrders = matchedList.stream().filter(order -> NumberUtils.nvlInteger(order.getItemSysId(), 0L) > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(needSupplierOrders)) {
                return;
            }
            needSupplierOrders.forEach(order -> {
                sysItemIdList.add(order.getItemSysId());
                sysSkuIdList.add(NumberUtils.negative2Zero(order.getSkuSysId()));
            });
            // 商品的对应的供应商信息
            Map<String, List<Long>> supplierMapInfo = tradeStockService.querySupplierIds(staff, new ArrayList<>(sysItemIdList), new ArrayList<>(sysSkuIdList));
            if (MapUtils.isNotEmpty(supplierMapInfo)) {
                Set<Long> allSupplierIdSet = new HashSet<>();
                for (List<Long> supplierIds : supplierMapInfo.values()) {
                    if (CollectionUtils.isNotEmpty(supplierIds)) {
                        allSupplierIdSet.addAll(supplierIds);
                    }
                }
                // 查询所有的供应商
                List<Supplier> allSupplierList = supplierService.queryByIds(staff, new ArrayList<>(allSupplierIdSet));
                Map<Long, List<Supplier>> allSupplierMap = allSupplierList.stream().collect(Collectors.groupingBy(Supplier::getId));
                originOrderList.forEach(order -> {
                    List<Long> supplierIdList = supplierMapInfo.get(order.getItemSysId() + "_" + NumberUtils.negative2Zero(order.getSkuSysId()));
                    if (CollectionUtils.isNotEmpty(supplierIdList)) {
                        List<Supplier> supplierList = new ArrayList<>();
                        supplierIdList.forEach(a -> {
                            List<Supplier> suppliers = allSupplierMap.get(a);
                            if (CollectionUtils.isNotEmpty(suppliers)) {
                                supplierList.addAll(suppliers);
                            }
                        });
                        order.setSupplierList(supplierList);
                        order.setSupplierIds(supplierIdList);
                    }
                });
            }
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, "获取商品供应商失败:" + e.getMessage()), e);
        }
    }

    @Override
    public List<Order> matchSysItem(User user, List<Order> orders, String matchType, TradeConfig tradeConfig) {
        TradeItemContext tradeItemContext = new TradeItemContext();
        tradeItemContext.setTradeConfig(tradeConfig);
        List<Order> unallocatedList = filterUnmatchedOrders(user, orders);
        if (!unallocatedList.isEmpty()) {
            ItemMatchResult itemMatchResult = new ItemMatchResult();
            itemMatchResult.setUser(user);
            itemMatchResult.setMatchType(matchType);
            itemMatchResult.setOrders(orders);
            itemMatchResult.setTrades(new ArrayList<>());
            itemMatchResult.setIsInsert(false);
            matchSysItems(itemMatchResult);
            return handleOrders(itemMatchResult,tradeItemContext);
        }
        return new ArrayList<>();
    }

    /**
     * @author: pxh
     * @description: 标签匹配供应商信息设置
     * @date: 2021/10/8 7:28 下午
     * @param: expr2Rules
     * @param: trades
     * @param: validTrades
     *
     * 写这个的开发之前是写在智能标签那里的 TradeTagBusiness 但是 智能匹配异常也需要用的 TradeExceptBusiness 漏了 没有加  挪一下位置
     */
    public void orderSupplierConfigByFilter(Staff staff, Map<TradeTagRule, String> expr2Rules, List<Trade> trades, List<Trade> validTrades) {
        Optional.of(expr2Rules).filter(tradeTagRuleStringMap -> tradeTagRuleStringMap.entrySet().stream()
                .filter(tradeTagRuleStringEntry -> CollectionUtils.isNotEmpty(tradeTagRuleStringEntry.getKey().getConditions()))
                .anyMatch(tradeTagRuleStringEntry -> tradeTagRuleStringEntry.getKey().getConditions().stream().anyMatch(this::conditionFieldJudgment))).ifPresent(tradeTagRuleStringMap -> {
            List<Order> matchSupplierOrderList = TradeUtils.getOrders4Trade(trades).stream()
                    .filter(order -> CollectionUtils.isEmpty(order.getSupplierIds()) || CollectionUtils.isEmpty(order.getSupplierList()))
                    .collect(Collectors.toList());

            orderSupplierConfig(staff, matchSupplierOrderList, TradeUtils.getOrders4Trade(validTrades));

            validTrades.forEach(trade -> {
                Set<Long> supplierIdSet = TradeUtils.getOrders4Trade(trade).stream()
                        .filter(order -> CollectionUtils.isNotEmpty(order.getSupplierIds()))
                        .flatMap(order -> order.getSupplierIds().stream()).collect(Collectors.toSet());
                trade.setSupplierIds(supplierIdSet);
            });
        });
    }

    /**
     * @author: pxh
     * @description: 条件字段名称判断
     * @date: 2021/10/9 9:26 上午
     * @param: spelCondition
     * @return: boolean
     *
     * 写这个的开发之前是写在智能标签那里的 TradeTagBusiness 但是 智能匹配异常也需要用的 TradeExceptBusiness 没有加  挪一下位置
     * 这里配置的都是自动化标签规则里有用到供应商id的才会在这里加 之前的人应该是考虑到性能的问题
     */
    private boolean conditionFieldJudgment(SpelCondition spelCondition) {
        return SpelCondition.SUPPLIER_CATEGORY_MATCH.equals(spelCondition.getField())
                || SpelCondition.FIELD_ORDER_SUPPLIER_IDS.equals(spelCondition.getField())
                || SpelCondition.FIELD_ORDER_CONTAIN_SUPPLIER_IDS.equals(spelCondition.getField());
    }

    @Override
    public List<Order> matchSysItem(User user, List<Order> orders) {
        TradeItemContext tradeItemContext = new TradeItemContext();
        List<Order> unallocatedList = filterUnmatchedOrders(user, orders);
        if (!unallocatedList.isEmpty()) {
            ItemMatchResult matchResult = new ItemMatchResult(user, Lists.newArrayList(), orders, null, false);
            matchSysItems(matchResult);
            return handleOrders(matchResult,tradeItemContext);
        }
        return new ArrayList<>();
    }

    @Override
    public List<Order> matchSysItem(User user, List<Order> orders, String matchType) {
        TradeItemContext tradeItemContext = new TradeItemContext();
        List<Order> unallocatedList = filterUnmatchedOrders(user, orders);
        if (!unallocatedList.isEmpty()) {
            ItemMatchResult itemMatchResult = new ItemMatchResult();
            itemMatchResult.setUser(user);
            itemMatchResult.setMatchType(matchType);
            itemMatchResult.setOrders(orders);
            itemMatchResult.setTrades(new ArrayList<>());
            itemMatchResult.setIsInsert(false);
            matchSysItems(itemMatchResult);
            return handleOrders(itemMatchResult,tradeItemContext);
        }
        return new ArrayList<>();
    }

    private List<Trade> filterUnMatchedTrades(User user, List<Trade> trades, List<Order> origins, Map<Long, List<OrderModifyLog>> orderIdLogsMap, Boolean isInsert) {
        Staff staff = user.getStaff();
        boolean isMatchAll = tradeLocalConfigurable.isOpenMatchAllTrade(user.getCompanyId());
        List<Trade> list = new ArrayList<>();
        for (Trade trade : trades) {
            //订单第一次同步时,分销订单直接过滤
            if (TradeUtils.isFxOrMixTrade(trade)) {
                matchFxTrade(user, trade);//处理分销订单
                continue;
            }
            if (!isMatchAll && !checkTrade4Match(staff,trade)) {//白名单控制，已发货已完成已关闭的订单不再匹配系统商品
                continue;
            }
            // 供销订单：如果分销强推分销 && 分销商品未匹配 供销不去匹配商品
            if(TradeUtils.isGxTrade(trade) && TradeUtils.isContainV(trade, TradeConstants.V_IF_FX_FORCE_PUSH_UNALLOCATED)){
                continue;
            }
            // 助手奇门强推的
            boolean ifQimenForcePushTrade = TradeUtils.isQimenAndNotFx(trade) && trade.getIfFxForcePushTrade();


            boolean toMatch = false;
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                order.setWarehouseId(trade.getWarehouseId());
                order.setUserId(user.getId());
                // 商品级别：助手奇门强推的 不去匹配商品
                if(ifQimenForcePushTrade && "KDZS_NOT_BIND_ITEM".equals(order.getOuterIid())){
                    continue;
                }
                if (doCheckOrder4Match(staff, order, orderIdLogsMap)
                        || (isMatchAll && order.getItemSysId() != null && order.getItemSysId() <= 0)) {
                    toMatch = true;
                    origins.add(order);
                }
            }
            if (toMatch) {//至少有一个子订单尚未匹配系统商品的订单;非首次同步时,已经匹配过系统商品的订单,无需再匹配
                list.add(trade);
            }
        }
        return list;
    }

    private boolean checkTrade4Match(Staff staff,Trade trade) {
        if (TradeStatusUtils.isAfterSendGoods(trade.getSysStatus())) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_NORMAL);
            }
            trade.setInsufficientNum(0);
            trade.setInsufficientRate(0D);
            TradeExceptUtils.setStockStatus(staff,trade,Trade.STOCK_STATUS_NORMAL);
            return false;
        }
        return true;
    }

    /**
     * 过滤需要匹配系统商品的子订单
     */
    private List<Order> filterUnmatchedOrders(User user, List<Order> orders) {
        boolean isMatchAll = tradeLocalConfigurable.isOpenMatchAllTrade(user.getCompanyId());
        List<Order> list = new ArrayList<>(orders.size());
        for (Order order : orders) {
            if (checkOrder4Match(user.getStaff(), order) || (isMatchAll && order.getItemSysId() <= 0)) {
                list.add(TradeStockUtils.createStockOrder(user.getStaff(),order));
            }
        }
        return list;
    }

    @Override
    public boolean checkOrder4Match(Order order) {
        return doCheckOrder4Match(null, order, null);
    }

    @Override
    public boolean checkOrder4Match(Staff staff, Order order) {
        return doCheckOrder4Match(staff, order, null);
    }

    private boolean doCheckOrder4Match(Staff staff, Order order, Map<Long, List<OrderModifyLog>> orderIdLogsMap) {
        boolean needMatch = false;
        if (TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
            OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_NORMAL);
        } else if (order.getItemSysId() <= 0) {
            needMatch = true;
            OrderExceptUtils.updateExceptOrder(staff,order, ExceptEnum.UNALLOCATED,1L);
        } else if (order.isNeedRematchItem() || order.isSyncItemChanged()) {//后面优化处理
            // 平台处理商品异常过,商品信息变更过，重新匹配商品
            needMatch = true;
            OrderExceptUtils.setStockStatus(staff,order,Trade.STOCK_STATUS_UNALLOCATED);
            order.setItemSysId(-1L);
            order.setSkuSysId(-1L);
        }
        return needMatch;
    }

    /**
     * 分销订单系统商品默认化
     */
    private void matchFxTrade(User user, Trade trade) {
        if (TradeUtils.isFxOrMixTrade(trade)) {
            List<Order> orderList = TradeUtils.getOrders4Trade(trade);
            for (Order order : orderList) {
                Order originOrder = order.getOrigin();
                boolean outerIdChange = false;
                if (originOrder != null) {
                    String oldOuterIid = org.apache.commons.lang3.StringUtils.isNotEmpty(originOrder.getOuterSkuId()) ? originOrder.getOuterSkuId() : originOrder.getOuterIid();
                    String outerIid = org.apache.commons.lang3.StringUtils.isNotEmpty(order.getOuterSkuId()) ? order.getOuterSkuId() : order.getOuterIid();
                    if (oldOuterIid != null && !oldOuterIid.equals(outerIid)) {
                        outerIdChange = true;
                        Logs.ifDebug(LogHelper.buildLogHead(user).append(String.format("交易[sid=%s,tid=%s]子订单[id=%s,numIid=%s,skuId=%s,oldOuterIid=%s,outerIid:%s]平台商品有变化，重新设置系统商家编码", order.getSid(), order.getTid(), order.getId(), order.getNumIid(), order.getSkuId(), oldOuterIid, outerIid)));
                    }
                }
                if (order.getItemSysId() == null || order.getItemSysId() <= 0 || (outerIdChange && !TradeStatusUtils.isAfterSendGoods(originOrder.getSysStatus()))) {
                    order.setItemSysId(Constants.FxDefaultSysNumId);
                    order.setSkuSysId(Constants.FxDefaultSysSkuId);
                    order.setSysOuterId(fxBusiness.getOuterId(user,false, order));
                    if (!tradeLocalConfigurable.isFxOuteriidUnchangeWhenMatched(trade.getCompanyId())) {
                        order.setOuterIid(fxBusiness.getPlatformOuterId(user, order));
                    }
                    order.setSysSkuPropertiesName(order.getSkuPropertiesName());
                    order.setSysPicPath(order.getPicPath());
                    order.setSysTitle(order.getTitle());
                }
            }
        }
    }

    /**
     * 将匹配的商品信息设置到子订单中
     */
    private Order fillMatchedOrder(Staff staff, Order order, Order matched, TradeConfig tradeConfig) {
        // KMERP-210677
        if (featureService.checkHasFeature(staff.getCompanyId(), Feature.ONE_EXCHANGE_MANY_INHERIT_PROPERTY_COMPANY_IDS)) {
            order.setNumIid(matched.getNumIid());
            order.setSkuId(matched.getSkuId());
        }
        order.setItemSysId(matched.getItemSysId());
        order.setSubStock(matched.getSubStock());
        order.setShortTitle(cutIfOverLength(matched.getShortTitle(), 128));
        order.setSysTitle(matched.getSysTitle());
        order.setSysOuterId(matched.getSysOuterId());
        order.setSysSkuPropertiesName(matched.getSysSkuPropertiesName() != null ? matched.getSysSkuPropertiesName() : "");
        order.setSysSkuPropertiesAlias(matched.getSysSkuPropertiesAlias() != null ? matched.getSysSkuPropertiesAlias() : "");
        order.setSysItemRemark(cutIfOverLength(matched.getSysItemRemark(), 255));
        order.setSysSkuRemark(cutIfOverLength(matched.getSysSkuRemark(), 255));
        order.setSysPicPath(matched.getSysPicPath());
        order.setUnit(matched.getUnit() != null ? matched.getUnit() : "");
        order.setSkuUnit(matched.getSkuUnit() != null ? matched.getSkuUnit() : "");
        order.setNum(matched.getNum());
        order.setStockNum(0);
        order.setCost(matched.getCost());
        order.setSkuSysId(matched.getSkuSysId() == null || matched.getSkuSysId() <= 0 ? -1L : matched.getSkuSysId());
        order.setNetWeight(matched.getNetWeight());
        order.setVolume(matched.getVolume());
        order.setCombineId(matched.getCombineId());
        order.setIsVirtual(matched.getIsVirtual());
        TradeStockUtils.needNotApplyStock(staff,order, tradeConfig);
        OrderExceptUtils.updateExceptOrder(staff,order, ExceptEnum.RELATION_CHANGED,0L);
        order.setInsufficientCanceled(0);
        order.setSalePrice(matched.getSalePrice());
        order.setSaleFee(matched.getSaleFee());
        order.setCids(matched.getCids());
        //如果配置是取档案销售价 或者是套件子商品 取档案销售价
        if(paymentCalculateSupports.userItemPrice(staff) || (order.getCombineId() !=null && order.getCombineId() > 0L)){
            order.setPrice(matched.getPrice());
        }
        order.setSupplierOuterId(matched.getSupplierOuterId());
        order.setSupplierCompanyId(matched.getSupplierCompanyId());
        order.setSupplierPrice(matched.getSupplierPrice());
        order.setItemActiveStatus(matched.getItemActiveStatus());
        String stockStatus = TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) ? Trade.STOCK_STATUS_NORMAL : Trade.STOCK_STATUS_EMPTY;
        OrderExceptUtils.setStockStatus(staff,order,stockStatus);
        OrderUtils.addItemMatchLog(order);
        return order;
    }

    private String cutIfOverLength(String str, int length){
        if(StringUtils.isBlank(str)){
            return "";
        }
        return str.length() <= length ? str : str.substring(0, length);
    }

    private void handleUnMatched(User user, Order order) {
        //未匹配的情况下不可能为套件，此处清空套件标志
        order.setType(Order.TypeOfNormal);
        order.setCombineId(0L);
        OrderExceptUtils.updateExceptOrder(user.getStaff(),order,ExceptEnum.RELATION_CHANGED,0L);
        order.setInsufficientCanceled(0);
        String stockStatus = TradeStatusUtils.isAfterSendGoods(order.getSysStatus()) ? Trade.STOCK_STATUS_NORMAL : Trade.STOCK_STATUS_UNALLOCATED;
        OrderExceptUtils.setStockStatus(user.getStaff(),order,stockStatus);
        Logs.ifDebug(LogHelper.buildLogHead(user).append(String.format("交易[sid=%s,tid=%s]子订单[id=%s,numIid=%s,skuId=%s,status=%s,sysStatus=%s,convertType=%s,outerIid=%s,outerSkuId=%s,num=%s]未匹配到系统商品", order.getSid(), order.getTid(), order.getId(), order.getNumIid(), order.getSkuId(), order.getStatus(), order.getSysStatus(), order.getConvertType(), order.getOuterIid(), order.getOuterSkuId(),order.getNum())));
    }

    /**
     * 商品匹配处理,返回匹配到系统商品的子订单集合
     */
    private List<Order> handleOrders(ItemMatchResult result,TradeItemContext tradeItemContext) {
        User user = result.getUser();
        TradeConfig tradeConfig =tradeItemContext.getTradeConfig();
        if (tradeConfig == null) {
            tradeConfig = tradeConfigService.get(user.getStaff());
            tradeItemContext.setTradeConfig(tradeConfig);
        }

        List<Order> matches = Lists.newArrayList();
        Set<Trade> keywordMatchTrades = Sets.newHashSet();
        Map<Long, List<Order>> map = result.getMatchedOrders().stream().collect(Collectors.groupingBy(Order::getId));
        Map<Long, Trade> tradeMap = result.getTrades().stream().collect(Collectors.toMap(Trade::getSid, Function.identity(), (v1, v2) -> v2));
        int useItemOuterIdMatchOrder = -1;
        if (result.getIsInsert()) {
            Conf conf = user.getStaff().getConf();
            if (conf != null && conf.getConfAttrInfo() != null) {
                //获取配置，useItemOuterIdMatchOrder用于判断是否开启E方案，0 关闭 1 开启
                useItemOuterIdMatchOrder = null == conf.getConfAttrInfo().get(ConfigUseItemOuterIdMatchOrder) ? 0 : (Integer) conf.getConfAttrInfo().get(ConfigUseItemOuterIdMatchOrder);
            }
        }
        for (Order order : result.getOrders()) {
            List<Order> matchedOrdersAll = map.get(order.getId());
            //剔除itemSysId为-1的情况
            List<Order> matchedOrders = removeUnMatchOrder(user, matchedOrdersAll);
            if (matchedOrders.isEmpty()) {
                handleUnMatched(user, order);
                order.getOperations().put(OpEnum.ITEM_RELATION_BIND_FAILURE, order.getOuterId());
                if (1 == useItemOuterIdMatchOrder) {
                    order.getOperations().put(OpEnum.ITEM_MATCH_BY_OUTERID, order.getOuterId());
                } else {
                    String pfId = order.getSkuId() != null ? order.getSkuId() : order.getNumIid();
                    order.getOperations().put(OpEnum.ITEM_MATCH_BY_RELATION, pfId);
                }
                continue;
            }
            handOrder(user, order, matchedOrders, tradeConfig,tradeItemContext);
            if (MapUtils.isNotEmpty(result.getComplexMap()) && result.getComplexMap().containsKey(order.getId())) {
                Trade trade = tradeMap.get(order.getSid());
                List<Order> complexOrders = handComplexOrder(trade, order, result, map, tradeConfig,tradeItemContext);
                if (CollectionUtils.isEmpty(complexOrders)) {
                    continue;
                }
                matches.addAll(complexOrders);
                keywordMatchTrades.add(trade);
            } else {
                addTrace(order);
                matches.add(order);
            }
        }
        if (CollectionUtils.isNotEmpty(result.getParseParams())) {
            tradeItemParsePaymentBusinessService.calculate(user.getStaff(), result.getParseParams());
            tradeItemParsePaymentBusinessService.calculateTrade(user.getStaff(), Lists.newArrayList(keywordMatchTrades));
        } else {
            long start = System.currentTimeMillis();
            //KMERP-104210
            TradeItemContext itemContext = new TradeItemContext();
            itemContext.setTradeConfig(tradeConfig);
            tradeSuitCalculateBusiness.calculate(user.getStaff(), matches, itemContext);
            Logs.ifDebug(LogHelper.buildLog(user.getStaff(), String.format("慢业务，%s耗时:%sms", "calculate", System.currentTimeMillis() - start)));
        }
        return matches;
    }

    private List<Order> handComplexOrder(Trade trade, Order order, ItemMatchResult result, Map<Long, List<Order>> map, TradeConfig tradeConfig,TradeItemContext tradeItemContext) {
        if (trade == null) {    //未找到参与匹配的订单，直接返回null
            return null;
        }
        //填充商品备注
        OrderExt ext = order.getOrderExt();
        if (ext == null) {
            ext = new OrderExt();
            order.setOrderExt(ext);
        }
        String orderRemark = "解析前公式【" + result.getComplexRemark().get(order.getId()) + "】";
        ext.setOrderRemark(StringUtils.isBlank(ext.getOrderRemark()) ? orderRemark : ext.getOrderRemark() + ";" + orderRemark);

        User user = result.getUser();
        List<Long> complexSids = result.getComplexMap().get(order.getId());
        List<Order> complexSelfs = Lists.newArrayList();
        for (Long id : complexSids) {
            if (Objects.equals(id, order.getId())) {
                continue;
            }
            List<Order> complexOrders = map.get(id);
            Order self = complexOrders.stream().filter(e -> e.getCombineId() == null || e.getCombineId() <= 0).findFirst().orElse(null);
            if (self == null) { //没找到商品本身，应该中断整个商品的匹配
                Logs.info(LogHelper.buildLog(user.getStaff(), String.format("匹配失败, 匹配的商品[%s]中未找到商品本身!", complexOrders.get(0).getOuterIid())));
                return null;
            }
            self.setSource("sys");
            self.setPicPath(StockConstants.PATH_NO_PIC);
            handOrder(user, self, complexOrders, tradeConfig,tradeItemContext);
            OrderExt complexExt = new OrderExt();
            BeanCopierUtil.copy(order.getOrderExt(), complexExt);
            self.setOrderExt(complexExt);
            complexSelfs.add(self);
        }
        TradeUtils.getOrders4Trade(trade).addAll(complexSelfs);

        complexSelfs.add(order);
        complexSelfs.forEach(e -> e.getOperations().put(OpEnum.ITEM_KEYWORD_BIND, ""));
        order.getOperations().put(OpEnum.ITEM_KEYWORD_BIND, result.getComplexLog().get(order.getId()));
        order.setSysItemChanged(1);
        //记录下商品复数关键字解析的操作
        orderModifyLogBusiness.addLog(user.getStaff(), OrderModifyLogUtils.build(complexSelfs, OrderModifyLogTypeEnum.ITEM_KEYWORD_COMPLEX_PARSE));
        result.addParseParam(ItemParsePaymentUtils.build(order, complexSelfs));
        return complexSelfs;
    }

    private void handOrder(User user, Order order, List<Order> matchedOrders, TradeConfig tradeConfig,TradeItemContext tradeItemContext) {
        Order firstMatchOrder = matchedOrders.get(0);
        if (matchedOrders.size() == 1) {
            handleOrdersCommon(user, order, firstMatchOrder, tradeConfig);
        } else {
            Integer type = matchedOrders.get(0).getType();
            //如果未返回type，默认为套件
            if (type == null) {
                type = Order.TypeOfCombineOrder;
            }
            //如果不是套件，不是组合装，不是加工商品，则默认为套件
            if (type != Order.TypeOfCombineOrder && type != Order.TypeOfGroupOrder && type != Order.TypeOfProcessOrder) {
                type = Order.TypeOfCombineOrder;
            }

            order.setType(type);
            order.setSuits(new ArrayList<>());
            handleOrdersSuits(user, order, matchedOrders, tradeConfig,tradeItemContext);
        }
    }

    /**
     * 兼容根据主商家编码
     *
     * @param matchedOrders
     * @return
     */
    private List<Order> removeUnMatchOrder(User user, List<Order> matchedOrders) {
        List<Order> matchOrders = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(matchedOrders)) {
            for (Order matchedOrder : matchedOrders) {
                //匹配
                if (matchedOrder.getItemSysId() != null && matchedOrder.getItemSysId() > 0) {
                    matchOrders.add(matchedOrder);
                } else {
                    //没有匹配，可以确定是商品返回的信息就没有匹配还是交易这边的问题
                    Logs.ifDebug(LogHelper.buildLog(user.getStaff(), String.format("从商品返回的order信息[sid=%s,tid=%s]子订单[id=%s,numIid=%s,skuId=%s,status=%s,sysStatus=%s]匹配到系统商品[sysItemId=%s,sysSkuId=%s,outerId=%s,isVirtual=%s,sysSkuRemark=%s]", matchedOrder.getSid(), matchedOrder.getTid(), matchedOrder.getId(), matchedOrder.getNumIid(), matchedOrder.getSkuId(), matchedOrder.getStatus(), matchedOrder.getSysStatus(), matchedOrder.getItemSysId(), matchedOrder.getSkuSysId(), matchedOrder.getSysOuterId(), matchedOrder.getIsVirtual(),matchedOrder.getSysSkuRemark())));
                }
            }
        }
        return matchOrders;
    }

    /**
     * 无单品
     */
    private void handleOrdersCommon(User user, Order order, Order matchOrder, TradeConfig tradeConfig) {
        order.setType(Order.TypeOfNormal);
        fillMatchedOrder(user.getStaff(),order, matchOrder, tradeConfig);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLog(user.getStaff(), String.format("交易[sid=%s,tid=%s]子订单[id=%s,numIid=%s,skuId=%s,status=%s,sysStatus=%s]匹配到系统商品[sysItemId=%s,sysSkuId=%s,outerId=%s,isVirtual=%s,sysSkuRemark=%s],单价取值[%s]", order.getSid(), order.getTid(), order.getId(), order.getNumIid(), order.getSkuId(), order.getStatus(), order.getSysStatus(), order.getItemSysId(), order.getSkuSysId(), order.getSysOuterId(), order.getIsVirtual(), order.getSysSkuRemark(),paymentCalculateSupports.userItemPrice(user.getStaff())?"档案单价":"平台单价")));
        }
    }

    /**
     * 有单品
     */
    private void handleOrdersSuits(User user, Order order, List<Order> matchedOrders, TradeConfig tradeConfig, TradeItemContext tradeItemContext) {
        StringBuilder buf = new StringBuilder();
        for (Order matchedOrder : matchedOrders) {//匹配到套件商品
            if (matchedOrder.getCombineId() == null || matchedOrder.getCombineId() <= 0) {//套件子订单本身
                fillMatchedOrder(user.getStaff(),order, matchedOrder, tradeConfig);
                continue;
            }
            //套件子订单中的单品
            Order son = orderCopier.copy(order, new TbOrder());
            son.setId(idWorkerService.nextId());//重新设置ID
            son.setFlag(1);//标志该子订单需要做插入操作
            // 商品匹配之后,子商品填充主品的orderExt,后续业务需要用到
            OrderExt orderExt = OrderExtUtils.initOrderExt(user.getStaff(), order);
            if (orderExt != null) {
                orderExt.setId(son.getId());
                son.setOrderExt(orderExt);
            }
            order.getSuits().add(fillMatchedOrder(user.getStaff(),son, matchedOrder, tradeConfig));
            //KMERP-145030 套件单品计算应付与优惠
            PaymentUtils.calculateOrder(son);
            if (buf.length() > 0) {
                buf.append("; ");
            }
            buf.append("id=").append(son.getId()).append(",sysItemId=").append(son.getItemSysId()).append(",sysSkuId=").append(son.getSkuSysId()).append(",num=").append(son.getNum()).append(",outerId=").append(son.getSysOuterId());
        }

        //临时先跳过43386这家公司
        if (!Objects.equals(user.getCompanyId(),43386L)) {
            //KMERP-41330 套件单品的分销价、分销金额取在分销价管理设置的分销价、分销金额
            modifyParentBusiness.setOrderSaleFee(user.getStaff(),tradeItemContext, order.getSuits());
            if (logger.isDebugEnabled()) {
                logger.debug(LogHelper.buildLog(user.getStaff(), String.format("交易[sid=%s,tid=%s]子订单[id=%s,numIid=%s,skuId=%s]匹配到系统套件商品[sysItemId=%s,sysSkuId=%s,outerId=%s,isVirtual=%s,sysSkuRemark=%s],单价取值[%s],包含单品[%s]", order.getSid(), order.getTid(), order.getId(), order.getNumIid(), order.getSkuId(), order.getItemSysId(), order.getSkuSysId(), order.getSysOuterId(), order.getIsVirtual(), order.getSysSkuRemark(),paymentCalculateSupports.userItemPrice(user.getStaff())?"档案单价":"平台单价", buf)));
            }
        }

    }

    private void addTrace(Order order) {
        StringBuilder s = new StringBuilder();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getOuterSkuId())) {
            s.append(order.getOuterSkuId());
        } else if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getOuterIid())) {
            s.append(order.getOuterIid());
        }
        if (StringUtils.isNotBlank(order.getSysOuterId())) {
            if (s.length() > 0) {
                s.append("->");
            }
            s.append(order.getSysOuterId());
        }
        if (s.length() > 0) {
            order.getOperations().put(OpEnum.ITEM_RELATION_BIND, s.toString());
        }
    }

    private List<Order> matchSysItems(ItemMatchResult result) {
        User user = result.getUser();
        List<Order> orders = result.getOrders();

        List<Order> matchedOrders = Lists.newArrayList();
        for (List<Order> splitOrders : Lists.partition(orders, BATCH_MATCH_SIZE)) {
            matchedOrders.addAll(matchSysItemPart(user, splitOrders, result.getMatchType()));
        }
        //开启了根据商品名称匹配商品的如果没有匹配成功的需要再根据商品名称匹配一次
        ItemKeywordParse itemParse = ItemMatchUtils.validate(user);
        if (itemParse != null) {
            Set<Long> matchedIds = matchedOrders.stream().map(Order::getId).collect(Collectors.toSet());
            List<Order> needMatchAgainOrders = orders.stream().filter(order->!matchedIds.contains(order.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needMatchAgainOrders)) {
                Logs.info(LogHelper.buildLog(user.getStaff(), String.format("店铺[%s]已开启关键字匹配, 匹配配置: [%s], 需要匹配的商品数量: %s",
                        user.getId(), JSONObject.toJSONString(itemParse), needMatchAgainOrders.size())));
                matchedOrders.addAll(matchItemByKeyword(user, needMatchAgainOrders, itemParse, result));
            }
        }
        result.setMatchedOrders(matchedOrders);
        return matchedOrders;
    }

    /**
     * 根据关键字匹配商品
     */
    private List<Order> matchItemByKeyword(User user, List<Order> orders, ItemKeywordParse itemParse, ItemMatchResult result) {
        List<Order> needMatches = Lists.newArrayList();
        Map<Long, List<Long>> complexMatchMap = Maps.newHashMap();
        Map<Long, String> complexLogs = Maps.newHashMap();
        Map<Long, String> complexRemark = Maps.newHashMap();
        for (Order order : orders) {
            Map<String, Integer> outerIdMap = ItemMatchUtils.parseOuterId(order, itemParse, result);
            if (MapUtils.isEmpty(outerIdMap)) {
                continue;
            }
            int i = 1;  //计数
            String parseField = ItemMatchUtils.getParseField(order, itemParse.checkUserId(user.getId()));
            StringBuilder logs = new StringBuilder(parseField + " -> ");
            for (Map.Entry<String, Integer> entry : outerIdMap.entrySet()) {
                TbOrder matchOrder = orderCopier.copy(order, new TbOrder());
                matchOrder.setOrderHistory(TradeHistoryUtils.order2OrderHistory(user.getStaff(), matchOrder, TradeBusinessEnum.TRADE_ITEM_CHANGE));
                matchOrder.setOuterIid(entry.getKey());
                matchOrder.setNum(matchOrder.getNum() * entry.getValue());
                //非单个商品解析，加入map进行处理
                if (itemParse.getParseType() == 1 || itemParse.getComplex() == 1) {
                    if (i > 1) {    //从解析出来的第二个商品开始新生成id
                        matchOrder.setId(idWorkerService.nextId());
                    }
                    matchOrder.setSysItemOuterId(parseField);   //商品创建时需要解析公式做主商家编码
                    complexMatchMap.computeIfAbsent(order.getId(), e -> Lists.newArrayList()).add(matchOrder.getId());
                    logs.append(entry.getKey()).append(", 组合比例: ").append(entry.getValue()).append(";");
                }
                needMatches.add(matchOrder);
                i++;
            }
            complexLogs.put(order.getId(), logs.toString());
            complexRemark.put(order.getId(), parseField);
        }
        List<Order> matches = Lists.newArrayList();
        for (List<Order> splitOrders : Lists.partition(needMatches, BATCH_MATCH_SIZE)) {
            matches.addAll(tradeStockService.matchSysItemsWithOuterIds(user, splitOrders));
        }
        List<Long> matchedSids = matches.stream().map(Order::getId).collect(Collectors.toList());
        for (Map.Entry<Long, List<Long>> entry : complexMatchMap.entrySet()) {
            final List<Long> complexSids = entry.getValue();
            List<Long> notMatchedIds = complexSids.stream().filter(e -> !matchedSids.contains(e)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(notMatchedIds)) {
                continue;
            }
            String noOuterIid = needMatches.stream().filter(e -> notMatchedIds.contains(e.getId())).map(Order::getOuterIid).collect(Collectors.joining(","));
            Logs.info(LogHelper.buildLog(user.getStaff(), "通过平台名称关键字解析多个商品: " + complexLogs.get(entry.getKey()) + " 但商品档案中不存在下列商家编码: " + noOuterIid));
            //如果匹配商品不全包含复数匹配，需要剔除
            matches.removeIf(e -> complexSids.contains(e.getId()));
        }
        result.setComplexMap(complexMatchMap);
        result.setComplexLog(complexLogs);
        result.setComplexRemark(complexRemark);
        return matches;
    }

    private void splitItemIds(String outerId, Order order, boolean isSkuOuterId) {
        if (StringUtils.isBlank(outerId)) {
            return;
        }
        int splitIndex = outerId.indexOf("##") > 0 ? outerId.indexOf("##") : outerId.indexOf("$$");
        if (splitIndex > 0 && splitIndex + 2 < outerId.length()) {
            String itemId = outerId.substring(0, splitIndex);
            if (isSkuOuterId) {
                order.setOuterSkuId(itemId);
            } else {
                order.setOuterIid(itemId);
            }
        }
    }

    /**
     *
     * @param forceUseMatchType  指定强制使用商品匹配类型，（针对一个user多种订单来源，且匹配方案不一样情况）
     */
    private List<Order> matchSysItemPart(User user, List<Order> orders, String forceUseMatchType) {
        if (OrderUtils.isGxOrMixOrder(orders.get(0)) || (orders.get(0).getUseOuterIdMatch() != null && orders.get(0).getUseOuterIdMatch())) {
            for (Order order : orders) {
                order.setOldOuterIid(order.getOuterIid());
                order.setOldOuterSkuId(order.getOuterSkuId());
                order.setOuterIid(order.getOuterSkuId() != null ? order.getOuterSkuId() : order.getOuterIid());
            }
            //截取
            for (Order order : orders) {
                splitItemIds(order.getOuterIid(), order, false);
                splitItemIds(order.getOuterSkuId(), order, true);
            }

            //数据还原
            List<Order> matchedOrders = tradeStockService.matchSysOuterIdItems(user.getStaff(), orders);
            for (Order order : matchedOrders) {
                ItemIdInfoDto itemIdInfoDto =new ItemIdInfoDto();
                itemIdInfoDto.setSysItemId(order.getItemSysId());
                itemIdInfoDto.setSysSkuId(order.getSkuSysId());

                order.setOuterIid(order.getOldOuterIid());
                order.setOuterSkuId(order.getOuterSkuId());
            }
            //数据还原
            for (Order order : orders) {
                order.setOuterIid(order.getOldOuterIid());
                order.setOuterSkuId(order.getOuterSkuId());
            }
            return matchedOrders;

        } else {
            List<Order> needMatchByOuterIdOrders = new ArrayList<>();
            List<Order> needMatchOrders = new ArrayList<>();
            for(Order order: orders){
                if(needMatchByOuterId(user, order)){
                    needMatchByOuterIdOrders.add(order);
                } else {
                    needMatchOrders.add(order);
                }
            }
            List<Order> resultOrders = new ArrayList<>();
            if(!needMatchByOuterIdOrders.isEmpty()){
                List<Order> matchedOrders = tradeStockService.matchSysOuterIdItems(user.getStaff(), orders);
                if(CollectionUtils.isNotEmpty(matchedOrders)) {
                    resultOrders.addAll(matchedOrders);
                }
            }
            if (MATCH_TYPE_WITH_RELATION.equals(forceUseMatchType)) {
                List<Order> matchedOrders = tradeStockService.matchSysItemsByRelation(user, needMatchOrders);
                if(CollectionUtils.isNotEmpty(matchedOrders)) {
                    resultOrders.addAll(matchedOrders);
                }
                return resultOrders;
            }

            List<Order> matchedOrders = tradeStockService.matchSysItems(user, needMatchOrders);
            if(CollectionUtils.isNotEmpty(matchedOrders)) {
                resultOrders.addAll(matchedOrders);
            }
            return resultOrders;
        }
    }

    private boolean needMatchByOuterId(User user, Order order){
        //淘工厂换货单 使用编码匹配
        return CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(order.getSource()) && order.getTitle() != null && order.getTitle().contains("换货");
    }
}

package com.raycloud.dmj.business.audit;

import com.raycloud.dmj.Logs;
import com.raycloud.dmj.business.audit.help.*;
import com.raycloud.dmj.business.common.CacheBusiness;
import com.raycloud.dmj.dao.trade.AutoAuditQueryDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.trades.AutoAuditConfig;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeConfig;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.domain.trades.audit.AuditThreadLocal;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.newfx.trades.Constants;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.services.utils.SystemTradeQueryParamsContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GxAuditAutoBusiness extends AuditAutoParentBusiness {

    private final Logger logger = Logger.getLogger(this.getClass());

    /**
     * 一次最多审核1W单
     */
    private static final int DEFAULT_MANUAL_AUDIT_QUERY_PAGE_SIZE = 10000;

    @Resource
    WaybillGetBusiness waybillGetBusiness;

    @Resource
    CacheBusiness cacheBusiness;

    @Resource
    IStaffService staffService;

    @Resource
    IUserService userService;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    private static final int DEFAULT_BATCH_SIZE = 100;

    public void audit(Long companyId) {
        Staff staff = staffService.queryDefaultStaffByCompanyId(companyId);
        if (staff == null) {
            return;
        }
        if(!checkIfAutoAudit(staff)){
            return;
        }
        List<User> users = userService.queryByCompanyId(staff.getCompanyId());
        staff.setUsers(users);
        String key = getCacheKey(staff);
        ProgressData progressData = cacheBusiness.get(key);
        if (progressData != null && progressData.getProgress() != 2) {
            Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("上一次供销订单自动审核还未结束，本次忽略[countAll=%s,countCurrent=%s,sucNum=%s]开始自动审核功能", progressData.getCountAll(), progressData.getCountCurrent(), progressData.getSucNum())));
            return;
        }
        progressData = new ProgressData().setProgress(1);
        cacheBusiness.set(key, progressData, 3600);
        try {
            audit(staff, progressData);
        } finally {
            cacheBusiness.delete(key);
            Logs.ifDebug(LogHelper.buildLogHead(staff).append(String.format("供销订单自动审核结束[countAll=%s,countCurrent=%s,sucNum=%s]", progressData.getCountAll(), progressData.getCountCurrent(), progressData.getSucNum())));
        }
    }

    private void audit(Staff staff, ProgressData progressData) {
        AuditThreadLocal auditThreadLocal = initAuditLocal(staff);
        if (auditThreadLocal == null) {
            return;
        }
        List<Trade> firstTrades = queryGxTrades(staff, auditThreadLocal, null);
        List<Trade> filterTrades = filterIgnoreBeforeDate(staff, auditThreadLocal, firstTrades);
        //没有订单直接返回
        if (CollectionUtils.isEmpty(filterTrades)) {
            return;
        }
        int tradeSize = filterTrades.size();
        String key = getCacheKey(staff);
        progressData.setCountAll(tradeSize).setCountCurrent(0).setSucNum(0L);
        if (tradeSize <= DEFAULT_BATCH_SIZE * 2) {
            doAudit(staff, filterTrades, auditThreadLocal, key, progressData);
        } else {
            int loop = tradeSize / DEFAULT_BATCH_SIZE + ((tradeSize % DEFAULT_BATCH_SIZE == 0) ? 0 : 1);
            for (int i = 0; i < loop; i++) {
                int fromIndex = i * DEFAULT_BATCH_SIZE;
                int endIndex = Math.min((fromIndex + DEFAULT_BATCH_SIZE), tradeSize);
                final List<Trade> subTrades = filterTrades.subList(fromIndex, endIndex);
                doAudit(staff, subTrades, auditThreadLocal, key, progressData);
            }
        }
        cacheBusiness.set(key, progressData.setProgress(2));
    }

    public boolean checkIfAutoAudit(Staff staff){
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        if (staff.openAuditActiveStockRecord()) {
            return false;
        }
        if (tradeConfig.getOpenAutoAudit() == null || tradeConfig.getOpenAutoAudit() == 0) {
            FxLogBuilder.gx(staff).append("未开启自动审核").printInfo(logger);
            return false;
        }
        return true;
    }


    public void auditGxDownload(Staff staff, Set<String> tids) {
        if(!checkIfAutoAudit(staff)){
            return;
        }
        AuditThreadLocal auditThreadLocal = initAuditLocal(staff);
        if (auditThreadLocal == null) {
            return;
        }
        List<Trade> firstTrades = queryGxTrades(staff, auditThreadLocal, new ArrayList<>(tids));
        List<Trade> filterTrades = filterIgnoreBeforeDate(staff, auditThreadLocal, firstTrades);
        //没有订单直接返回
        if (CollectionUtils.isEmpty(filterTrades)) {
            return;
        }
        AuditManualData auditManualData = AuditUtils.buildAuditManualData(staff, auditThreadLocal, filterTrades);
        AutoAuditData autoAuditData = lockService.locks(getAuditLocks(staff, auditManualData), ()-> doAuditLockCallback(staff, auditThreadLocal, auditManualData));
        waybillGetBusiness.fireTradeWaybillGet(staff, TradeUtils.toSidList(autoAuditData.auditSuccessTrades), auditThreadLocal.tradeConfig);
    }

    private String getCacheKey(Staff staff) {
        return "auto_audit_" + staff.getCompanyId() + "_" + Constants.FxDefaultUserId;
    }

    private List<AutoAuditConfig> getGxAutoAuditConfigList(Staff staff) {
        List<AutoAuditConfig> configs = autoAuditConfigDao.queryByCompanyId(staff, 1);
        if (CollectionUtils.isEmpty(configs)) {
            return null;
        }
        return configs.stream().filter(config -> config.getRuleType() == 1 && AuditUtils.checkAuditTime(staff, config.getExcludeStart(), config.getExcludeEnd())).collect(Collectors.toList());
    }

    private AuditThreadLocal initAuditLocal(Staff staff) {
        List<AutoAuditConfig> configs = getGxAutoAuditConfigList(staff);
        if (CollectionUtils.isEmpty(configs)) {
            return null;
        }
        AuditThreadLocal auditThreadLocal = AuditUtils.buildAuditThreadLocal(staff, configs);
        if (auditThreadLocal.userConfigMap.size() == 0) {
            return null;
        }
        AuditUtils.parseTradeConfig(staff, auditThreadLocal, tradeConfigService.get(staff));
        auditThreadLocal.opEnum = OpEnum.AUTO_AUDIT;
        auditThreadLocal.ignoreBeforeDate = false;
        return auditThreadLocal;
    }

    public <T extends Trade> void doAudit(Staff staff, List<T> trades, AuditThreadLocal auditThreadLocal, String key, ProgressData progressData) {
        progressData.setCountCurrent(progressData.getCountCurrent() + trades.size());
        try {
            AuditManualData auditManualData = AuditUtils.buildAuditManualData(staff, auditThreadLocal, trades);
            AutoAuditData autoAuditData = lockService.locks(getAuditLocks(staff, auditManualData), ()-> doAuditLockCallback(staff, auditThreadLocal, auditManualData));
            progressData.setSucNum(progressData.getSucNum() + autoAuditData.auditSuccessTrades.size());
            waybillGetBusiness.fireTradeWaybillGet(staff, TradeUtils.toSidList(autoAuditData.auditSuccessTrades), auditThreadLocal.tradeConfig);
        } catch (Exception e) {
            Logs.error(LogHelper.buildErrorLog(staff, e, String.format("自动审核失败,sids=%s", TradeUtils.toSidList(trades))), e);
        } finally {
            cacheBusiness.set(key, progressData);
        }
    }

    public List<Trade> queryGxTrades(Staff staff, AuditThreadLocal auditThreadLocal, List<String> tidList) {
        TradeQueryParams params = new TradeQueryParams();
        Date now = new Date();
        params.setFields(AutoAuditQueryDao.QUERY_FILEDS)
                .setQueryFlag(1).setExceptIds(new String[]{"-1"}).setQueryOrder(false)
                .setCheckItem(false).setBreakQuery(true)
                .setQueryId(SystemTradeQueryParamsContext.QUERY_WAIT_AUDIT)
                .setPage(new Page(1, DEFAULT_MANUAL_AUDIT_QUERY_PAGE_SIZE));

        StringBuilder condition = new StringBuilder();
        condition.append(" t.company_id=").append(staff.getCompanyId())
                .append(" and ").append("t.user_id=").append(Constants.FxDefaultUserId)
                .append(" and t.sys_status='").append(Trade.SYS_STATUS_WAIT_AUDIT).append("'")
                .append(" and t.pay_time < '").append(new java.sql.Timestamp(now.getTime())).append("' ")
                .append(" and t.enable_status >= 1 and is_auto_audit in (0,1) and is_halt = 0 and is_refund = 0 and is_cancel = 0 and is_presell !=1 and type not like 'trade_out%' and (convert_type = 1 and belong_type in(2,3))");
        if (!auditThreadLocal.openAuditAutoInsufficientCanceled) {
            condition.append(" and is_excep = 0 ").append(" and t.stock_status='").append(Trade.STOCK_STATUS_NORMAL).append("'");
        } else {
            params.setCheckExcep(false);
        }
        if (CollectionUtils.isNotEmpty(tidList)) {
            condition.append(" and t.tid in (").append(tidList.stream().map(tid -> "'" + tid + "'").collect(Collectors.joining(","))).append(")");
        }
        params.setCustomWhere(condition.toString());
        return tradeSearchService.search(staff, params).getList();
    }

    /**
     * 过滤订单暂存时间
     * GX代码不要藕合到审核里面
     */
    public List<Trade> filterIgnoreBeforeDate(Staff staff, AuditThreadLocal auditThreadLocal, List<Trade> trades) {
        if (auditThreadLocal.ignoreBeforeDate) {
            return trades;
        }
        List<Trade> filterTrades = new ArrayList<>();
        Date date = new Date();
        Map<Long, Date> userBeforeDateMap = new HashMap<>();
        StringBuilder ignoreBeforeDateLog = new StringBuilder("付款时间属于订单暂存时间,故排除,");

        int logSize = 0;
        for (Trade trade : trades) {
            Long userId = TradeUtils.isGxOrMixTrade(trade) ? trade.getTaobaoId() : trade.getUserId();
            List<AutoAuditConfig> config = auditThreadLocal.userConfigMap.get(userId);
            if (config == null) {
                continue;
            }
            Date beforeDate = userBeforeDateMap.get(userId);
            if (beforeDate == null) {
                int min = config.stream().mapToInt(AutoAuditConfig::getBeforePayTime).min().orElse(0);
                beforeDate = DateUtils.addMinutes(date, -min);
                userBeforeDateMap.put(userId, beforeDate);
            }

            if (trade.getPayTime().after(beforeDate)) {
                ignoreBeforeDateLog.append("sid=").append(trade.getSid()).append(",payTime=").append(trade.getPayTime()).append(",beforeDate=").append(beforeDate).append(";");
                logSize++;
                if (logSize >= 50) {
                    Logs.ifDebug(LogHelper.buildLogHead(staff).append(ignoreBeforeDateLog));
                    ignoreBeforeDateLog = new StringBuilder("付款时间属于订单暂存时间,故排除,");
                    logSize = 0;
                }
                continue;
            }
            filterTrades.add(trade);
        }
        if (logSize > 0) {
            Logs.ifDebug(LogHelper.buildLogHead(staff).append(ignoreBeforeDateLog));
        }
        return filterTrades;
    }

}

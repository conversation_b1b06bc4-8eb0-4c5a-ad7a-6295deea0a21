package com.raycloud.dmj.business.modify;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.business.callback.TradeItemChangeLockCallback;
import com.raycloud.dmj.business.common.StaffAssembleBusiness;
import com.raycloud.dmj.business.common.TradeLockBusiness;
import com.raycloud.dmj.business.item.TradeItemChangeBusiness;
import com.raycloud.dmj.business.payment.TradeItemChangePaymentBusinessService;
import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.dms.request.scm.ItemFxInfo;
import com.raycloud.dmj.dms.request.scm.ItemParam;
import com.raycloud.dmj.dms.request.scm.QueryAndSaveKmtFxItemBySupplierRequest;
import com.raycloud.dmj.dms.service.trade.api.IDmsScmFxService;
import com.raycloud.dmj.domain.Domain;
import com.raycloud.dmj.domain.OrderConstant;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.constant.TradeEvents;
import com.raycloud.dmj.domain.log.OpLog;
import com.raycloud.dmj.domain.payment.TradeItemChangeRequest;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.item.Gx2FxChangeItem;
import com.raycloud.dmj.domain.trade.item.Gx2FxChangeItemData;
import com.raycloud.dmj.domain.trade.item.ItemChangeSingleData;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.item.ITradeItemOldService;
import com.raycloud.dmj.services.log.IOpLogService;
import com.raycloud.dmj.services.trace.IItemTraceService;
import com.raycloud.dmj.services.trades.ISysTradeService;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.IdWorkerService;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.fill.ITradeFill;
import com.raycloud.dmj.services.trades.fill.TradeOrderSyncItemTagFill;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.utils.ItemTraceMessageBuilder;
import com.raycloud.ec.api.EventInfo;
import com.raycloud.ec.api.IEventCenter;
import com.raycloud.erp.db.router.DbContextHolder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.log4j.Logger;
import org.apache.log4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;


/**
 *
 * 供销订单修改
 *
 * <AUTHOR>
 *
 */
@Service
public class GxTradeItemModifyBusiness {

    private static final Logger logger = Logger.getLogger(GxTradeItemModifyBusiness.class);

    /**
     * 操作类型：非供销订单
     */
    private static final int OPT_TYPE_NORMAL = 1;

    /**
     * 操作类型：来源于供销
     */
    private static final int OPT_TYPE_FROM_GX = 2;

    private static final SimplePropertyPreFilter ITEM_FX_INFO_SIMPLE_PROPERTY_PRE_FILTER = new SimplePropertyPreFilter("supplierErpSysItemId","supplierErpSysSkuId","supplierErpOuterId","supplierErpSkuOuterId","distributorErpSysItemId","distributorErpSysSkuId","distributorErpOuterId","distributorErpSkuOuterId");

    private static final SimplePropertyPreFilter ITEM_CHANGE_SIMPLE_PROPERTY_PRE_FILTER = new SimplePropertyPreFilter("id","newOrderId","num","price","payment","divideOrderFee","title","itemSysId","skuSysId","outerId","outerSkuId");


    @Resource
    IDmsScmFxService dmsScmFxService;

    @Resource
    StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    TbOrderDAO tbOrderDao;

    @Resource
    IdWorkerService idWorkerService;

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;

    @Resource
    ISysTradeService sysTradeService;

    @Resource
    ITradeItemOldService tradeItemOldService;

    @Resource
    IItemTraceService itemTraceService;

    @Resource
    IEventCenter eventCenter;

    @Resource
    IOpLogService opLogService;

    @Resource
    ITradeFill tradeOrderSyncItemTagFill;

    @Resource
    TradeItemChangePaymentBusinessService tradeItemChangePaymentBusinessService;

    @Resource
    TradeLockBusiness tradeLockBusiness;

    @Resource
    ILockService lockService;

    @Resource
    TradeItemChangeBusiness tradeItemChangeBusiness;


    @Resource
    ITradeConfigService tradeConfigService;
    /**
     *
     * 1. 没有orderId的根据供销的商品itemSysId,skuSysId 找对对应的分销商品信息
     * 2. 生成新的orderId 修改商品的逻辑是把旧的商品软删除，新增一条新的商品 对应供销的oid
     *
     * @param itemData
     * @return
     */
    public List<ItemFxInfo> relFxItemInfo(Gx2FxChangeItemData itemData){

        QueryAndSaveKmtFxItemBySupplierRequest request = new QueryAndSaveKmtFxItemBySupplierRequest();
        request.setStaffId(itemData.getGxStaff().getId());
        request.setSupplierCompanyId(itemData.getGxCompanyId());
        request.setDistributorCompanyId(itemData.getFxCompanyId());

        List<ItemParam> supplierItems = Lists.newArrayListWithExpectedSize(8);
        List<Gx2FxChangeItem> gx2FxChangeItemList = itemData.getGx2FxChangeItemList();
        if(CollectionUtils.isNotEmpty(gx2FxChangeItemList)) {
            gx2FxChangeItemList.stream().filter(e->Objects.nonNull(e.getGxItemSysId()) && e.isIfChange()).forEach(e->{
                ItemParam itemParam = new ItemParam();
                itemParam.setErpSysItemId(e.getGxItemSysId());
                itemParam.setErpSysSkuId(Optional.ofNullable(e.getGxSkuSysId()).orElse(0L));
                supplierItems.add(itemParam);
            });
        }

        if(CollectionUtils.isEmpty(supplierItems)){
            return Collections.emptyList();
        }
        List<ItemFxInfo> itemFxInfoList = Lists.newArrayListWithExpectedSize(8);
        Lists.partition(supplierItems,10).forEach(list->{
            request.setSupplierItems(list);
            List<ItemFxInfo> itemFxInfoListPart  = dmsScmFxService.queryAndSaveKmtFxItemBySupplier(request).getItemFxInfoList();
            if(CollectionUtils.isNotEmpty(itemFxInfoListPart)){
                logger.info(new FxLogBuilder(itemData.getGxStaff(),FxLogBuilder.ROLE_GX).append("根据供销商品信息查询对应分销商品,response:").append(JSON.toJSONString(itemFxInfoListPart,ITEM_FX_INFO_SIMPLE_PROPERTY_PRE_FILTER)).toString());
                itemFxInfoList.addAll(itemFxInfoListPart);
            }
        });


        if(CollectionUtils.isEmpty(itemFxInfoList)){
            logger.warn(new FxLogBuilder(itemData.getGxStaff(),FxLogBuilder.ROLE_GX).append("根据供销商品信息查询对应分销商品,response 为空").toString());
            return itemFxInfoList;
        }
        List<String> outerIds = itemFxInfoList.stream().filter(i->Objects.equals(i.getDistributorActiveStatusValue(),0))
                .map(i-> org.apache.commons.lang3.StringUtils.defaultIfBlank(i.getDistributorErpSkuOuterId(),i.getDistributorErpOuterId()))
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(outerIds)){
            throw new IllegalArgumentException("对应的分销商品已停用，编码："+ Joiner.on(",").join(outerIds));
        }

        Map<String, List<ItemFxInfo>> itemFxInfoMap = itemFxInfoList.stream().collect(Collectors.groupingBy(e->OrderUtils.buildItemKey(e.getSupplierErpSysItemId(), e.getSupplierErpSysSkuId())));


        gx2FxChangeItemList.stream().filter(e->Objects.nonNull(e.getGxItemSysId()) && e.isIfChange()).forEach(e->{
            String key = OrderUtils.buildItemKey(e.getGxItemSysId(), e.getGxSkuSysId());
            List<ItemFxInfo> itemFxInfos = itemFxInfoMap.get(key);
            if(CollectionUtils.isEmpty(itemFxInfos) || itemFxInfos.get(0).getDistributorErpSysItemId()==null){
                logger.info(new FxLogBuilder(itemData.getGxStaff(),FxLogBuilder.ROLE_GX).append("key",key)
                        .append("itemFxInfos",JSON.toJSONString(itemFxInfos))
                        .append("itemFxInfoMap：").append(JSON.toJSONString(itemFxInfoMap)).toString());
                throw new IllegalArgumentException("没有对应的分销商品，操作失败，请重试");
            }
            if(Objects.isNull(e.getFxNewOrderId())){
                e.setFxNewOrderId(idWorkerService.nextId());
            }
            ItemFxInfo itemFxInfo = itemFxInfos.get(0);
            e.setFxItemSysId(itemFxInfo.getDistributorErpSysItemId());
            e.setFxSkuSysId(itemFxInfo.getDistributorErpSysSkuId());
            e.setFxSysOuterId(org.apache.commons.lang3.StringUtils.defaultIfBlank(itemFxInfo.getDistributorErpSkuOuterId(), itemFxInfo.getDistributorErpOuterId()));
        });

        return itemFxInfoList;

    }

    /**
     *
     * 根据供销的oid  --> 找到对应的分销商品
     *
     *
     * @param itemData
     */
    public  void fillFxItemInfo(Gx2FxChangeItemData itemData){

        List<Gx2FxChangeItem> gx2FxChangeItemList = itemData.getGx2FxChangeItemList();
        if(CollectionUtils.isEmpty(gx2FxChangeItemList)){
            return;
        }

        Long[] ids = gx2FxChangeItemList.stream().filter(o->Objects.nonNull(o.getGxOrderOid())).map(Gx2FxChangeItem::getGxOrderOid).toArray(Long[]::new);
        if(ArrayUtils.isEmpty(ids)){
            return;
        }
        List<TbOrder> tbOrders = tbOrderDao.queryByIds(itemData.getFxStaff(), ids);

        if(CollectionUtils.isEmpty(tbOrders)){
            logger.warn(new FxLogBuilder(itemData.getGxStaff(),FxLogBuilder.ROLE_GX)
                    .append("根据供销oid查询没有对应的分销商品信息").append("gxOid",Lists.newArrayList(ids)).toString());
            return;
        }

        Map<Long,Gx2FxChangeItem> gx2FxChangeItemMap = gx2FxChangeItemList.stream().collect(Collectors.toMap(Gx2FxChangeItem::getGxOrderOid,a->a,(a,b)->a));

        FxLogBuilder fxLogBuilder = new FxLogBuilder(itemData.getGxStaff(),FxLogBuilder.ROLE_GX)
                .append("没有对应的分销商品信息").startArray();
        AtomicBoolean ifHasLog = new AtomicBoolean(false);

        tbOrders.forEach(o->{
            Gx2FxChangeItem item = gx2FxChangeItemMap.get(o.getId());
            if(item == null){
                ifHasLog.set(true);
                fxLogBuilder.startObject().append("gxOrderId",o.getId()).endObject();
                return;
            }
            item.setFxItemSysId(o.getItemSysId());
            item.setFxSkuSysId(o.getSkuSysId());
            item.setFxSid(o.getSid());
            item.setFxOrderId(o.getId());
            if(Objects.isNull(item.getFxNewOrderId())){
                item.setFxNewOrderId(idWorkerService.nextId());
            }
            item.setFxOrder(o);
        });

        if(ifHasLog.get() && logger.isDebugEnabled()){
            logger.debug(fxLogBuilder.endArray().toString());
        }

    }


    /**
     * 处理供销对应分销的商品
     *
     * @param itemData
     * @param gxOrderId
     */
    public void changeItemAfterGx(Gx2FxChangeItemData itemData,Long gxOrderId){
        if(CollectionUtils.isEmpty(itemData.getGx2FxChangeItemList())){
            return;
        }

        if(!itemData.isIfRelEditFx()){
            logger.debug(new FxLogBuilder(itemData.getGxStaff(),FxLogBuilder.ROLE_GX).append("开启【允许供销订单与分销订单的商品信息和订单信息不一致】配置，对应的分销订单不进行商品修改").toString());
            return;
        }

        Order fxOrderFront = new Order();
        Gx2FxChangeItem changeItemByOrderId = itemData.getChangeItemByOrderId(gxOrderId);
        if(changeItemByOrderId == null || changeItemByOrderId.getFxSid() == null){
            logger.debug(new FxLogBuilder(itemData.getGxStaff(),FxLogBuilder.ROLE_GX).append("没有对应的分销的商品").toString());
            return;
        }
        MDC.put(TradeConstants.TRADE_ITEM_EDIT_FROM_GX,"1");
        fxOrderFront.setId(changeItemByOrderId.getFxOrderId());
        fxOrderFront.setNewOrderId(changeItemByOrderId.getFxNewOrderId());
        fxOrderFront.setSid(changeItemByOrderId.getFxSid());
        fxOrderFront.setItemSysId(changeItemByOrderId.getFxItemSysId());
        fxOrderFront.setSkuSysId(changeItemByOrderId.getFxSkuSysId());
        ItemChangeSingleData itemChangeSingleData4Fx = new ItemChangeSingleData(fxOrderFront.getSid(), fxOrderFront);
        TradeItemChangeLockCallback callback = new TradeItemChangeLockCallback(itemData.getFxStaff(), tradeItemChangeBusiness, itemChangeSingleData4Fx);
        lockService.locks(tradeLockBusiness.getERPLocks(itemData.getFxStaff(), fxOrderFront.getSid()), callback);

    }

    /**
     *
     * 1.如果不是供销订单走原来的删除逻辑
     * 2.如果是供销订单，如果分销商品存在先删除对应的分销商品，如果不存在直接返回，再删除对应的供销商品
     *
     *
     * @param staff
     * @param sid
     * @param orderIds
     * @param clientId
     * @return
     */
    public List<Trade> deleteTradeItem(Staff staff,Long sid, String orderIds,String clientId){
        Trade trade = tradeSearchService.queryBySid(staff, true, sid);
        Assert.isTrue(trade != null, String.format("系统订单%s不存在", sid));

        List<Long> idList = Strings.getAsLongList(orderIds, ",", false);

        if(CollectionUtils.isEmpty(idList)){
            throw new IllegalArgumentException("orderIds参数不能为空");
        }

        if(!TradeUtils.isGxTrade(trade) || TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()){
           return doDeleteTradeItem(staff,sid, idList,clientId,OPT_TYPE_NORMAL);
        }

        List<TbOrder> tbOrders = tbOrderDao.queryByIds(staff, idList.toArray(new Long[0]));


        Gx2FxChangeItemData gx2FxChangeItemData = Gx2FxChangeItemData.builder().gxCompanyId(staff.getCompanyId()).gxStaff(staff).build();

        List<Gx2FxChangeItem> gx2FxChangeItemList = Lists.newArrayListWithExpectedSize(idList.size());
        gx2FxChangeItemData.setGx2FxChangeItemList(gx2FxChangeItemList);

        List<Long> fxOrderOids = Lists.newArrayListWithExpectedSize(idList.size());

        tbOrders.forEach(o->{
            fxOrderOids.add(o.getOid());
            gx2FxChangeItemList.add(Gx2FxChangeItem.builder().gxOrderId(o.getId()).gxOrderOid(o.getOid()).build());
        });


        validateGxTrade(staff,trade,gx2FxChangeItemData);

        try{
            List<Long> fxOrderOidList = gx2FxChangeItemData.getGx2FxChangeItemList().stream()
                    .filter(o->Objects.nonNull(o.getFxOrderId())).map(Gx2FxChangeItem::getFxOrderId)
                    .collect(Collectors.toList());
            if(!TradeUtils.ifContainV(trade, TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX)){
                if(CollectionUtils.isNotEmpty(fxOrderOidList)){
                    MDC.put(TradeConstants.TRADE_ITEM_EDIT_FROM_GX,"1");
                    doDeleteTradeItem(gx2FxChangeItemData.getFxStaff(),Strings.getAsLong(trade.getTid(),false),fxOrderOidList,clientId,OPT_TYPE_FROM_GX);
                }else {
                    logger.warn(new FxLogBuilder(staff,FxLogBuilder.ROLE_FX).append("根据供销商品信息查询对应分销商品为空").toString());
                }
            }
        }finally {
            DbContextHolder.set(staff.getDbKey());
        }

        return doDeleteTradeItem(staff,sid,idList,clientId,OPT_TYPE_NORMAL);
    }

    public List<Trade> doDeleteTradeItem(Staff staff,Long sid, List<Long> orderIds,String clientIp,int optType){
        Trade trade = tradeSearchService.queryBySid(staff, true, sid);
        Assert.isTrue(trade != null, String.format("系统订单%s不存在", sid));
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        Assert.isTrue(CollectionUtils.isNotEmpty(orderList), String.format("系统订单%s不存在子订单", sid));

        Set<Long> longIdSet = new HashSet<>();
        for (Long orderId : orderIds) {
            longIdSet.add(orderId);
        }

        List<Order> delOrders = new ArrayList<>();
        for (Order order : orderList) {
            if (longIdSet.contains(order.getId())) {
                delOrders.add(order);
            }
        }

        if(delOrders.size()==0){
            if(optType == OPT_TYPE_NORMAL){
                throw new IllegalArgumentException(String.format("删除的系统子订单%s:%s不存在", sid, orderIds));
            }else{
                logger.warn(new FxLogBuilder(staff,FxLogBuilder.ROLE_FX).append("根据供销商品信息查询对应分销商品为空").toString());
                return tradeSearchService.queryBySids(staff, true, false, true, trade.getSid());
            }
        }

        orderList.removeAll(delOrders);

        TradeConfigNew openLinkPaymentConfig = TradeConfigGetUtil.get(staff, TradeConfigEnum.OPEN_LINK_PAYMENT);
        Integer openLinkPayment = StringUtils.isEmpty(openLinkPaymentConfig.getConfigValue()) ? null: Integer.parseInt(openLinkPaymentConfig.getConfigValue());
        tradeItemOldService.removeItemCaculateDiscountFee(staff, trade, delOrders, openLinkPayment);
        ModifyData modifyData = sysTradeService.updateTrade(staff, trade);
        saveLog(staff, trade.getSid() + "", getPlatformSaveContent(trade, modifyData),clientIp);
        giftSaveLog(staff, trade, modifyData.inserts,clientIp);
        // 记录商品日志
        itemTraceService.batchRecord(staff, ItemTraceMessageBuilder.buildItemTraceMessageList(staff, modifyData));
        List<Trade> result = new ArrayList<>();
        return  tradeSearchService.queryBySids(staff, true, false, true, trade.getSid());
    }


    /**
     *
     * 修改商品
     *
     * @param staff
     * @param changeModel
     * @param clientIp
     * @return
     */
    public List<Trade> saveTradeItem(Staff staff, TradeItemChangeRequest changeModel,String clientIp){
        if(CollectionUtils.isNotEmpty(changeModel.getOrders()) && TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()){
            changeModel.getOrders().forEach(o->{
                o.setNewOrderId(idWorkerService.nextId());
            });
        }

        TradeItemChangeRequest changeModel4Fx = JSON.parseObject(JSON.toJSONString(changeModel),TradeItemChangeRequest.class);
        changeModel4Fx.setTrade(changeModel.getTrade());
        changeModel4Fx.setSaveOrderSupplier(false);

        if(changeModel.getTrade() != null &&
                TradeUtils.isGxTrade(changeModel.getTrade()) &&
                !TradeUtils.ifContainV(changeModel.getTrade(), TradeConstants.V_GX_ITEM_EDIT_NOT_REL_FX) &&
                handle4TradeItemChangeRequest(staff,changeModel4Fx) &&
                CollectionUtils.isNotEmpty(changeModel4Fx.getOrders())){
            try{
                List<TradeItemChangeRequest.ItemChange> giftAndErrorData = changeModel4Fx.getOrders().stream().filter(o -> Objects.nonNull(o.getId()) && Objects.isNull(o.getItemSysId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(giftAndErrorData)){
                    logger.warn(new FxLogBuilder(staff,FxLogBuilder.ROLE_FX).append("供销商品没有对应的分销商品").append("itemChanges",JSON.toJSONString(giftAndErrorData,ITEM_CHANGE_SIMPLE_PROPERTY_PRE_FILTER)).toString());
                }
                changeModel4Fx.setOrders(changeModel4Fx.getOrders().stream().filter(o->Objects.nonNull(o.getItemSysId())).collect(Collectors.toList()));
                MDC.put(TradeConstants.TRADE_ITEM_EDIT_FROM_GX,"1");
                doSaveTradeItem(changeModel4Fx.getGx2FxChangeItemData().getFxStaff(), changeModel4Fx,clientIp);
            }finally {
                DbContextHolder.set(staff.getDbKey());
            }

        }

        return doSaveTradeItem(staff,changeModel,clientIp);
    }



    public List<Trade> doSaveTradeItem(Staff staff, TradeItemChangeRequest changeModel,String clientIp){
        Assert.isTrue(CollectionUtils.isNotEmpty(changeModel.getOrders()), "一笔订单下至少需要有一个商品");
        logger.debug(new FxLogBuilder(staff,FxLogBuilder.ROLE_FX)
                .append("orders",JSON.toJSONString(changeModel.getOrders(),ITEM_CHANGE_SIMPLE_PROPERTY_PRE_FILTER)).toString()
        );

        List<Trade> changeds = tradeItemChangePaymentBusinessService.beforeTradeUpdate(staff, changeModel);
        Long mainSid = changeModel.getSid();
        //获取当前订单 如果是合单,对应子订单的处理见 com.raycloud.dmj.business.modify.ModifyParentBusiness.handleUpdateMergeTrade
        Trade trade = null;
        for (Trade changed : changeds) {
            if (Objects.equals(changed.getSid(), mainSid)) {
                trade = changed;
                break;
            }
        }
        //changeds返回的sid与传入的不一致 说明合单情况发生了变更
        //https://tb.raycloud.com/task/65017f0b393853001dc402a1
        Assert.notNull(trade,"当前订单已合单或合单主单已发生变化，请查询后重新进行修改。");

        //3 更新trade
        List<Trade> originTrades = tradeSearchService.queryBySids(staff, true, false, true, trade.getSid());
        List<Long> giftOrders = TradeUtils.getOrders4Trade(originTrades).parallelStream().filter(Order::isGift).map(Order::getId).collect(Collectors.toList());
        //填充商品标签信息到order
        ((TradeOrderSyncItemTagFill)tradeOrderSyncItemTagFill).fillByItemDubbo(staff, TradeUtils.getOrders4Trade(trade));
        //标记是否拣选验货
        OrderUtils.toFullOrderList(TradeUtils.getOrders4Trade(trade), false).stream().filter(order -> !giftOrders.contains(order.getId())).forEach(OrderUtils::fillIsPick);
        // KMERP-234322: 订单支持修改商品供应商
        modifyOrderSupplierIds(trade, changeModel);
        // 标记是否执行商品搭配规则
        trade.setRematchItemReplace(changeModel.getRematchItemReplace());
        ModifyData modifyData = sysTradeService.updateTrade(staff, trade);
        saveLog(staff, trade.getSid() + "", getPlatformSaveContent(trade, modifyData),clientIp);

        giftSaveLog(staff, trade, modifyData.inserts,clientIp);
        //记录商品操作日志
        itemTraceService.batchRecord(staff,ItemTraceMessageBuilder.buildItemTraceMessageList(staff, modifyData));

        List<Trade> trades = tradeSearchService.queryBySids(staff, true, false, true, trade.getSid());
        tradeOrderSyncItemTagFill.fill(staff, trades);

        return trades;
    }

    /**
     *
     *
     *
     * @param staff
     * @param changeModel
     * @return  返回true的话就需要处理对应的分销订单
     */
    private boolean handle4TradeItemChangeRequest(Staff staff, TradeItemChangeRequest changeModel) {

        if(TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()){
            return false;
        }

        Gx2FxChangeItemData itemData = new Gx2FxChangeItemData();
        itemData.setGxStaff(staff);
        itemData.setGxCompanyId(staff.getCompanyId());

        changeModel.setGx2FxChangeItemData(itemData);
        Long fxSid = Strings.getAsLong(changeModel.getTrade().getTid(), false);
        changeModel.setSid(fxSid);
        changeModel.setPayAmount(null);

        List<TradeItemChangeRequest.ItemChange> orders = changeModel.getOrders();
        List<Gx2FxChangeItem> gx2FxChangeItemList = Lists.newArrayListWithExpectedSize(orders.size());
        itemData.setGx2FxChangeItemList(gx2FxChangeItemList);

        List<Order> oriOrders = TradeUtils.getOrders4Trade(changeModel.getTrade());
        Map<Long, Order> oriOrderMap = oriOrders.stream().collect(Collectors.toMap(a -> a.getId(), a -> a,(a,b)->a));

        // orderId 为空的是新增的商品。 不为空对比数据的itemSysId,skuSysId 不一样就是修改
        orders.forEach(o->{
            if(o.getId() == null){
                gx2FxChangeItemList.add(Gx2FxChangeItem.builder().gxOrderId(o.getId()).fxSid(fxSid).ifChange(true).fxNewOrderId(o.getNewOrderId())
                        .gxItemSysId(o.getItemSysId()).gxSkuSysId(o.getSkuSysId()).gxOrderOid(o.getOid()).build());
                return;
            }

           Order oriOrder = oriOrderMap.get(o.getId());
           //
           if(oriOrder != null) {
               gx2FxChangeItemList.add(Gx2FxChangeItem.builder().gxOrderId(o.getId())
                       .gxItemSysId(o.getItemSysId()).gxSkuSysId(o.getSkuSysId()).gxOrderOid(o.getOid())
                       .fxSid(fxSid).fxNewOrderId(o.getNewOrderId())
                       .ifChange(!OrderUtils.buildItemKey(o.getItemSysId(),o.getSkuSysId()).equals(OrderUtils.buildItemKey(oriOrder.getItemSysId(),oriOrder.getSkuSysId())))
                       .build());
               oriOrderMap.remove(o.getId());
           }

           // orgiOrderMap 剩下的就是删除的商品


        });

        validateGxTrade(staff, changeModel.getTrade(), itemData);


        if(CollectionUtils.isEmpty(gx2FxChangeItemList)){
            logger.warn(new FxLogBuilder(itemData.getGxStaff(),FxLogBuilder.ROLE_GX)
                    .appendTrade(changeModel.getTrade()).append("根据供销商品信息查询对应分销商品,response 为空").toString());
            return false;
        }

//      Trade tradeDb = tradeSearchService.queryBySid(itemData.getFxStaff(), false,  fxSid);


        FxLogBuilder fxLogBuilder = new FxLogBuilder(itemData.getGxStaff(),FxLogBuilder.ROLE_GX)
                .appendTrade(changeModel.getTrade()).append("没有对应的分销商品信息").startArray();
        AtomicBoolean ifHasLog = new AtomicBoolean(false);
        orders.forEach(o->{
            o.setSid(null);
            o.setTid(null);
            o.setOid(null);
            if(o.getId() ==null){
                Gx2FxChangeItem changeItem = itemData.getChangeItemByItemSysIdAndSysSkuId(o.getItemSysId(),o.getSkuSysId());
                if(changeItem !=null){
                    o.setItemSysId(changeItem.getFxItemSysId());
                    o.setSkuSysId(changeItem.getFxSkuSysId());
                    if(Objects.isNull(o.getNewOrderId())){
                        o.setNewOrderId(changeItem.getFxNewOrderId());
                    }
                }else {
                    ifHasLog.set(true);
                    // 找不到对应的分销商品需要删除
                    fxLogBuilder.startObject().append("itemSysId", o.getItemSysId()).append("skuSysId",o.getSkuSysId()).endObject();
                    orders.remove(o);
                }
            }else {
                Gx2FxChangeItem changeItem = itemData.getChangeItemByOrderId(o.getId());
                if (changeItem != null) {
                    o.setItemSysId(changeItem.getFxItemSysId());
                    o.setSkuSysId(changeItem.getFxSkuSysId());
                    o.setId(changeItem.getFxOrderId());
                    if(Objects.isNull(o.getNewOrderId())){
                        o.setNewOrderId(changeItem.getFxNewOrderId());
                    }
                    Order fxOrder = changeItem.getFxOrder();
                    if (fxOrder != null) {
                        o.setOuterId(fxOrder.getOuterId());
                        o.setOuterSkuId(fxOrder.getOuterSkuId());
                        o.setPicPath(fxOrder.getPicPath());
                        if(StringUtils.isNotBlank(fxOrder.getTitle())){
                            o.setTitle(fxOrder.getTitle());
                        }
                        o.setPrice(fxOrder.getPrice());
                        o.setPayment(fxOrder.getPayment());
                        o.setStatus(fxOrder.getStatus());
                        o.setDivideOrderFee(fxOrder.getDivideOrderFee());
                    }
                } else {
                        ifHasLog.set(true);
                        // 找不到对应的分销商品需要删除
                        fxLogBuilder.startObject().append("itemSysId", o.getItemSysId()).append("skuSysId",o.getSkuSysId()).endObject();
                        orders.remove(o);
                    }
                }


        });
        if(ifHasLog.get()){
            logger.warn(fxLogBuilder.endArray().toString());
        }

        return true;

    }



    /**
     *
     * 供销订单校验
     * 1.分销是否允许供销商编辑订单中的商品
     * 2.供销是否是多级分销
     * 3.根据供销商品找到对应的分销商品
     *
     *
     * @param staff
     * @param trade
     */
    public Staff validateGxTrade(Staff staff, Trade trade) {
        if(!TradeUtils.isGxTrade(trade)){
            return staff;
        }

        if(TradeUtils.ifContainV(trade, TradeConstants.V_IF_MULTI_GX)){
            throw new IllegalArgumentException(String.format("多级供销订单，无法换商品[系统订单号=%s]", trade.getSid()));
        }

        Staff fxStaff = staffAssembleBusiness.getDefaultStaff(trade.getSourceId());

        if(!TradeConfigGetUtil.get(fxStaff, TradeConfigEnum.DMS_ALLOW_GX_EDIT_TRADE_ITEM).isOpen() && !TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()){
            throw new IllegalArgumentException("分销不允许供销商编辑订单中的商品");
        }

        MDC.put(TradeConstants.TRADE_ITEM_EDIT_FROM_GX,"1");
        return fxStaff;

    }


    /**
     *
     * 供销订单校验
     * 1.分销是否允许供销商编辑订单中的商品
     * 2.供销是否是多级分销
     * 3.根据供销商品找到对应的分销商品
     *
     *
     * @param staff
     * @param trade
     * @param itemData
     */
    public void validateGxTrade(Staff staff, Trade trade, Gx2FxChangeItemData itemData) {

        Staff fxStaff = validateGxTrade(staff,trade);
        if(!TradeUtils.isGxTrade(trade) || TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX).isOpen()){
            return;
        }

        itemData.setFxStaff(fxStaff);
        itemData.setFxCompanyId(fxStaff.getCompanyId());


        fillFxItemInfo(itemData);
        relFxItemInfo(itemData);

    }

    public void validateGxTrade(Staff staff, Trade trade, ItemChangeSingleData itemChangeBatchData) {

        validateGxTrade(staff,trade,itemChangeBatchData.gx2FxChangeItemData);
        if(TradeUtils.isGxTrade(trade) && CollectionUtils.isNotEmpty(itemChangeBatchData.gx2FxChangeItemData.getGx2FxChangeItemList())){
            itemChangeBatchData.orderFront.setNewOrderId(itemChangeBatchData.gx2FxChangeItemData.getGx2FxChangeItemList().get(0).getFxNewOrderId());
        }
    }


    public void beforeUpdate(List<Trade> trades){
        if(!Objects.equals(MDC.get(TradeConstants.TRADE_ITEM_EDIT_FROM_GX),"1") || CollectionUtils.isEmpty(trades)){
            return;
        }
        trades.forEach(t->{
            t.addV(TradeConstants.V_IF_GX_EDIT_TRADE_ITEM);
        });
    }

    /**
     * 标记供销改商品
     * @param order
     * @param trade
     */
    public void addGxItemEdit(Order order, Trade trade){
        if (Objects.equals(MDC.get(TradeConstants.TRADE_ITEM_EDIT_FROM_GX), "1") && TradeUtils.isGxOrMixTrade(trade)) {
            order.addV(OrderConstant.V_GX_ITEM_EDIT);//标记供销改商品
        }
    }


    public void giftSaveLog(Staff staff, Trade trade, List<Order> orders,String clientIp) {
        if (CollectionUtils.isEmpty(orders) || trade == null) {
            return;
        }
        for (Order order : orders) {
            if (order.getCustomGiftType() != null && order.getCustomGiftType() == 1L) {
                List<Trade> trades = new ArrayList<>();
                trades.add(trade);
                giftSaveLog(staff, trades, true,clientIp);
                break;
            }
        }
    }

    public void giftSaveLog(Staff staff, List<Trade> trades, boolean isInsert,String clientIp) {
        if (CollectionUtils.isEmpty(trades) && !isInsert) {
            return;
        }
        //新增手工赠品事件
        eventCenter.fireEvent(this, new EventInfo("trade.manual.gift.save").setArgs(new Object[]{staff}), trades);
        saveLog(staff, trades.get(0).getSid() + "", org.apache.commons.lang3.StringUtils.join(TradeUtils.toSids(trades)) + "手工添加赠品",clientIp);
    }

    public void saveLog(Staff staff, String key, String content,String clientIp) {
        saveLog(staff,"platform_save", key, content, clientIp);
    }

    public void saveLog(Staff staff, String action, String key, String content,String clientIp) {
        OpLog opLog = new OpLog();
        opLog.setCompanyId(staff.getCompanyId());
        opLog.setStaffId(staff.getId());
        opLog.setAccountName(staff.getAccountName());
        opLog.setStaffName(staff.getName());
        opLog.setIp(clientIp);
        opLog.setDomain(Domain.TRADE);
        opLog.setAction(action);
        opLog.setKey(key);
        opLog.setContent(content);
        if (logger.isDebugEnabled()) {
            logger.debug(LogHelper.buildLogHead(staff).append(content));
        }
        opLog.setCreated(new Date());
        opLogService.record(staff, opLog);
    }

    public String getPlatformSaveContent(Trade trade, ModifyData modifyData) {
        return getPlatformSaveContent(trade, modifyData, "商品");
    }

    public String getPlatformSaveContent(Trade trade, ModifyData modifyData, String orderType) {
        StringBuilder content = new StringBuilder();
        content.append("修改订单，系统订单号：" + trade.getSid() + ";");
        //新增的日志
        List<Order> inserts = modifyData.inserts;
        if (inserts.size() > 0) {
            content.append("新增").append(orderType).append("：[");
            for (Order order : inserts) {
                content.append("{商家编码：").append(order.getSysOuterId()).append(",数量：").append(order.getNum()).append(",金额：").append(order.getPayment()).append("}");
            }
            content.append("]");
        }
        //删除的日志
        List<Order> deletes = modifyData.deletes;
        if (deletes.size() > 0) {
            content.append("删除").append(orderType).append("：[");
            for (Order order : deletes) {
                content.append("{商家编码：").append(order.getSysOuterId()).append(",数量：").append(order.getNum()).append(",金额：").append(order.getPayment()).append("}");
            }
            content.append("]");
        }
        //换商品种类
        content.append(getContent("换" + orderType + "种类", modifyData.itemChanges));
        //换数量
        content.append(getContent("换" + orderType + "数量", modifyData.numChanges));
        //修改金额
        content.append(getContent("换金额", modifyData.updates));

        return content.toString();
    }


    public String getContent(String operate, List<Order> orders) {
        StringBuilder content = new StringBuilder();
        if (orders.size() > 0) {
            content.append(operate).append("：[");
            for (Order order : orders) {
                Order origin = order.getOrigin();
                if (origin != null) {
                    content.append("{修改前 商家编码：").append(origin.getSysOuterId()).append(",数量：").append(origin.getNum()).append(",金额：").append(origin.getPayment());
                }
                content.append(",修改后 商家编码：").append(order.getSysOuterId() == null ? origin.getSysOuterId() : order.getSysOuterId()).append(",数量：").append(order.getNum()).append(",金额：").append(order.getPayment()).append("}");
            }
            content.append("]");
        }
        return content.toString();
    }

    /**
     * 修改分销订单后续处理
     * @param staff
     * @param trades
     */
    public void updateTradeAfter(Staff staff,List<Trade> trades){
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Trade> fxTrades = trades.stream().filter(TradeUtils::isFxOrMixTrade).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fxTrades)) {
            FxLogBuilder.fx(staff).appendTrade(fxTrades).append("分销修改商品重算").printDebug(logger);
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_SYNC).setArgs(new Object[]{staff, TradeUtils.toSidList(fxTrades)}), null);
        }
        List<Trade> qimenFxTrades = trades.stream()
                .filter(TradeUtils::isQimenFxSource)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(qimenFxTrades)) {
            TradeConfig tradeConfig = tradeConfigService.get(staff);
            if (tradeConfig != null && tradeConfig.isGiftRecalculateCashFlowEnabled()) {
                logger.debug(LogHelper.buildLog(staff, "奇门赠品匹配重算流水,sids:" + TradeUtils.toSidsStr(qimenFxTrades)));
                eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_QIMEN_FX_CASH_FLOW).setArgs(new Object[]{staff, 0, TradeUtils.toSidList(qimenFxTrades)}), null);
            }
        }
    }

    // 由前端传参修改子单供应商信息
    private void modifyOrderSupplierIds(Trade trade, TradeItemChangeRequest changeModel) {
        if (Boolean.TRUE.equals(changeModel.getSaveOrderSupplier())) {
            trade.setSaveOrderSupplier(true);
            return;
        }
        // 这里设置成null，后面会从商品模块获取最新的供应商信息 com.raycloud.dmj.services.trade.supplier.OrderSupplierService.fillUpdate
        TradeUtils.getOrders4Trade(trade).forEach(order -> order.setSupplierIds(null));
    }
}

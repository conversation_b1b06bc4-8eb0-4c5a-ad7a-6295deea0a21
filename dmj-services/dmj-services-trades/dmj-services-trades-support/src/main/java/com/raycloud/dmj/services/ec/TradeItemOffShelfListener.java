package com.raycloud.dmj.services.ec;

/**
 * @Description 商品下架事件 更新上传订单备注
 * <AUTHOR>
 * @Date 2022/8/4
 */

import com.raycloud.dmj.dao.order.TbOrderDAO;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.contants.BaseConstants;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.TbOrder;
import com.raycloud.dmj.domain.trades.TbTrade;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.utils.OrderUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.wave.OrderUniqueCodeConfig;
import com.raycloud.dmj.domain.wave.utils.OrderUniqueStatusChangeUtils;
import com.raycloud.dmj.services.ec.itemsku.ItemShutoffListener;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeUpdateService;
import com.raycloud.dmj.services.trades.wave.IOrderUniqueCodeConfigService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.ec.api.CommonEventSource;
import com.raycloud.ec.api.EventSourceBase;
import com.raycloud.ec.api.IEventListener;
import com.raycloud.ec.api.annotation.ListenerBind;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@ListenerBind(BaseConstants.EVENT_TRADE_ITEM_OFF_SHELF)
public class TradeItemOffShelfListener implements IEventListener {
    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    private IOrderUniqueCodeConfigService orderUniqueCodeConfigService;
    @Resource
    private ITradeUpdateService tradeUpdateService;
    @Resource
    private TbOrderDAO tbOrderDAO;
    @Resource
    private ItemShutoffListener itemShutoffListener;

    @Override
    public void onObserved(EventSourceBase source) {
        CommonEventSource evt = (CommonEventSource) source;
        Staff staff = evt.getArg(0, Staff.class);
        Set<Long> orderIds = evt.getArg(1, Set.class);
        log.info(LogHelper.buildLog(staff, "开出处理商品下架事件，orderIds=" + orderIds).toString());
        dealWithOrderIds(staff, orderIds);
    }

    private void dealWithOrderIds(Staff staff, Set<Long> orderIds) {
        List<TbOrder> tbOrders = tbOrderDAO.queryByKeys(staff, true, true, "order_not_consign_" + staff.getDbInfo().getOrderDbNo(), "*", "id", orderIds.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(tbOrders)) {
            return;
        }
        // 下架的order商品ID集合
        List<String> offShelfIdList = buildOffShelfId(tbOrders);
        List<Long> sids = OrderUtils.toSidList(tbOrders);
        List<Trade> tradeList = tradeSearchService.queryBySids(staff, true, sids.toArray(new Long[0]));
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        List<Trade> uploadTradeList = new ArrayList<>();
        List<Trade> updateTradeList = new ArrayList<>();
        tradeList.forEach(trade -> {
            Trade updateTrade = buildUpdateTrade(staff, trade, offShelfIdList);
            if (Objects.nonNull(updateTrade)) {
                uploadTradeList.add(trade);
                updateTradeList.add(updateTrade);
            }
        });
        if (CollectionUtils.isNotEmpty(updateTradeList)) {
            tradeUpdateService.updateTrades(staff, updateTradeList);
        }
        if (CollectionUtils.isNotEmpty(uploadTradeList)) {
            itemShutoffListener.uploadSellerMemo(staff, uploadTradeList, "订单同步");
        }
    }

    private List<String> buildOffShelfId(List<TbOrder> tbOrders) {
        List<String> itemSkuSysIds = new ArrayList<>();
        for (TbOrder order : tbOrders) {
            if (order.getSkuSysId() != null && order.getSkuSysId() > 0) {
                itemSkuSysIds.add(OrderUniqueStatusChangeUtils.buildItemSkuIds(order.getItemSysId(), order.getSkuSysId()));
            } else {
                itemSkuSysIds.add(OrderUniqueStatusChangeUtils.buildItemSkuIds(order.getItemSysId(), null));
            }
        }
        return itemSkuSysIds;
    }

    private Trade buildUpdateTrade(Staff staff, Trade trade, List<String> offShelfIdList) {
        Trade update = new TbTrade();
        update.setSid(trade.getSid());
        // 下架标签 设置了自定义旗帜的取自定义旗帜
        Integer flag = null;
        OrderUniqueCodeConfig orderUniqueCodeConfig = orderUniqueCodeConfigService.get(staff);
        // 开关开了才更新
        boolean openOffShelfFlag = orderUniqueCodeConfig.getExtConfigMap() != null && Objects.equals(orderUniqueCodeConfig.getExtConfigMap().getOrDefault("openOffShelfFlag", 1), 1);
        if (openOffShelfFlag) {
            flag = Optional.of(orderUniqueCodeConfig).map(OrderUniqueCodeConfig::getOffShelfFlag).orElse(null);
        }
        List<Order> orderList = TradeUtils.getOrders4Trade(trade);
        // 过滤下架的order
        List<Order> offShelfOrderList = orderList.stream().filter(o ->
                offShelfIdList.contains(OrderUniqueStatusChangeUtils.buildItemSkuIds(o.getItemSysId(), o.getSkuSysId()))
                        && !OrderUtils.isAfterSendGoods(o)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(offShelfOrderList)) {
            return null;
        }
        Set<String> sysOuterIdSet = offShelfOrderList.stream().map(Order::getSysOuterId).collect(Collectors.toSet());
        String memo = OrderUniqueStatusChangeUtils.buildSellerMemo(trade.getSellerMemo(), sysOuterIdSet, null);
        if (flag != null) {
            update.setSellerFlag(Long.valueOf(flag));
            trade.setSellerFlag(update.getSellerFlag());
        }
        update.setSellerMemo(memo);
        trade.setSellerMemo(update.getSellerMemo());
        return update;
    }
}

package com.raycloud.dmj.services.trade.label.system.handler;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.label.TradeSystemLabelEnum;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.services.trade.label.handle.ITradeSystemLabelHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.raycloud.dmj.domain.utils.CommonConstants.*;

@Component
public class TradeSystemLabelSfFreeShippingHandler implements ITradeSystemLabelHandler {

    @Override
    public TradeTag getAssignLabel() {
        return TradeSystemLabelEnum.TAG_SF_FREE_SHIPPING.getSystemTag();
    }


    @Override
    public boolean match(Staff staff, TradeConfig tradeConfig, Trade trade) {
        // 获取订单来源
        String source = trade.getSource();

        if (StringUtils.isBlank(source)) {
            // 如果不存在来源直接返回false
            return false;
        }

        // 获取订单拓展数据
        TradeExt tradeExt = trade.getTradeExt();
        if (Objects.isNull(tradeExt)) {
            // 如果不存在拓展数据直接返回false
            return false;
        }

        switch (source) {
            case PLAT_FORM_TYPE_FXG: // 抖音 new TradeSystemLabelRuleConfig("sf_free_shipping", "", "1", "tradeExt")
                Object sfFreeShipping = tradeExt.get("sf_free_shipping");
                if (Objects.isNull(sfFreeShipping)) {
                    return false;
                }
                if (StringUtils.equals(String.valueOf(sfFreeShipping), "1")) {
                    // 等于1即为匹配
                    return true;
                }
                return false;
//            case PLAT_FORM_TYPE_PDD: // 拼多多 new TradeSystemLabelRuleConfig("free_sf", "", "1", "tradeExt")
//            case PLAT_FORM_TYPE_KUAI_SHOU: // 快手 new TradeSystemLabelRuleConfig("free_sf", "", "1", "tradeExt")
            default: // 全平台通用逻辑
                Object freeSf = tradeExt.get("free_sf");
                if (Objects.isNull(freeSf)) {
                    return false;
                }
                if (StringUtils.equals(String.valueOf(freeSf), "1")) {
                    // 等于1即为匹配
                    return true;
                }
                return false;
        }
    }
}

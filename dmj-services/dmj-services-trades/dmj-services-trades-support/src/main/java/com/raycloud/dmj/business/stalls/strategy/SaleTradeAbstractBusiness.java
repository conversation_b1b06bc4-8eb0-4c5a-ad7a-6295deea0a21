package com.raycloud.dmj.business.stalls.strategy;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.business.modify.ModifyParentBusiness;
import com.raycloud.dmj.business.modify.TradeAddBusiness;
import com.raycloud.dmj.business.stalls.StallsTradeBusiness;
import com.raycloud.dmj.business.stalls.factory.SaleTradeFactory;
import com.raycloud.dmj.common.basic.BaseBusiness;
import com.raycloud.dmj.dms.request.DmsGenerateShareShortUrlRequest;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.Warehouse;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.enums.afterorder.WorkOrderGoodStatusEnum;
import com.raycloud.dmj.domain.enums.afterorder.WorkOrderStatusEnum;
import com.raycloud.dmj.domain.pt.FreightCost;
import com.raycloud.dmj.domain.stalls.*;
import com.raycloud.dmj.domain.stalls.common.Pagination;
import com.raycloud.dmj.domain.stalls.common.constants.SaleConstants;
import com.raycloud.dmj.domain.stalls.common.dto.CalculateTransportDto;
import com.raycloud.dmj.domain.stalls.common.dto.ItemStockDto;
import com.raycloud.dmj.domain.stalls.common.result.Result;
import com.raycloud.dmj.domain.stalls.common.result.ResultCode;
import com.raycloud.dmj.domain.stalls.common.utils.SaleLogUtils;
import com.raycloud.dmj.domain.stalls.common.utils.SaleTradeUtils;
import com.raycloud.dmj.domain.stalls.enums.SaleOpEnums;
import com.raycloud.dmj.domain.stalls.payment.entity.Deposit;
import com.raycloud.dmj.domain.stalls.trade.dto.SaleSettleDto;
import com.raycloud.dmj.domain.stalls.trade.vobj.Customer;
import com.raycloud.dmj.domain.stalls.trade.vobj.OrderInfo;
import com.raycloud.dmj.domain.stalls.trade.vobj.TradeExtInfo;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeTrace;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.workorder.WorkOrder;
import com.raycloud.dmj.external.builder.ItemBuilder;
import com.raycloud.dmj.external.builder.TradeBuilder;
import com.raycloud.dmj.external.consumer.AfsConsumer;
import com.raycloud.dmj.external.consumer.ItemConsumer;
import com.raycloud.dmj.external.consumer.TradeConsumer;
import com.raycloud.dmj.external.consumer.WmsConsumer;
import com.raycloud.dmj.external.stall.customer.FmsConsumer;
import com.raycloud.dmj.fms.service.response.QueryReceivableOrderResponse;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.stalls.SaleOrderService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.ITradeTraceService;
import com.raycloud.dmj.services.trades.IdWorkerService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.stalls.application.service.ISaleConfigService;
import com.raycloud.dmj.stalls.business.SalePaymentBusiness;
import com.raycloud.dmj.stalls.infrastructure.repo.SaleQueryRepo;
import com.raycloud.dmj.stalls.infrastructure.repo.SaleTradeRepo;
import com.raycloud.ec.api.IEventCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: YangBaoPing(杨保平)
 * @Date: 2022/3/25 11:37 上午
 * @Version 1.0
 * @Email <EMAIL>
 */
@Slf4j
public abstract class SaleTradeAbstractBusiness extends BaseBusiness {

    @Resource
    protected IdWorkerService idWorkerService;
    @Resource
    protected IEventCenter eventCenter;
    @Resource
    protected StallsTradeBusiness stallsTradeBusiness;
    @Resource(name = "tbTradeSearchService")
    protected ITradeSearchService tradeSearchService;
    @Resource
    protected ITradeTraceService tradeTraceService;

    @Resource
    protected ISaleConfigService saleConfigService;
    @Resource
    protected SaleTradeRepo saleTradeRepo;
    @Resource
    protected SaleQueryRepo saleQueryRepo;
    @Resource
    protected SaleTradeFactory saleTradeFactory;

    @Resource
    protected IShopService shopService;
    @Resource
    protected IWarehouseService warehouseService;

    @Resource
    protected SalePaymentBusiness salePaymentBusiness;

    @Resource
    protected AfsConsumer afsCustomer;
    @Resource
    protected ItemConsumer itemConsumer;
    @Resource
    protected WmsConsumer wmsConsumer;
    @Resource
    protected FmsConsumer fmsConsumer;
    @Resource
    protected TradeConsumer tradeConsumer;


    //--------------- 后续可能删除的引用-----------------
    @Resource
    protected SaleOrderService saleOrderService;

    @Resource
    IDmsTradeService dmsTradeService;
    @Resource
    protected ModifyParentBusiness modifyParentBusiness;

    @Transactional
    public Result saleSave(Staff staff, SaleTrade saleTrade, SaleSettleDto saleSettle) {
        initStatus(staff, saleTrade, saleSettle);
        save(staff, saleTrade, saleSettle.getOnlySettleType());
        //v1保存和预配货保存，v2挂起直接返回
        if (saleSettle.isDraft()) {
            log.info("销货单草稿保存成功！id: {}", saleTrade.getId());
            return saveDraft(staff, saleTrade, saleSettle);
        }
        //v1直接开单和配货单结账，v2现场收银，批发收银和预配货结账
        return settle(staff, saleTrade, saleSettle);
    }

    /**
     * 结账前初始化处理 (销货单状态)
     */
    protected abstract void initStatus(Staff staff, SaleTrade saleTrade, SaleSettleDto saleSettle);

    /**
     * 保存草稿
     */
    protected Result saveDraft(Staff staff, SaleTrade saleTrade, SaleSettleDto settleDto) {
        return Result.isOk();
    }

    /**
     * 结账
     */
    protected abstract Result settle(Staff staff, SaleTrade saleTrade, SaleSettleDto saleSettle);

    /**
     * 剔除需要转换的套件商品并返回
     */
    public abstract List<SaleOrder> rejectSuits(Staff staff, SaleTrade saleTrade, SaleSettleDto settleDto);

    /**
     * 填充客户信息
     */
    public abstract Result fillCustomer(Staff staff, List<SaleTrade> saleTrades);

    /**
     * 填充客户欠款
     */
    public abstract Result fillDebtInfo(Staff staff, List<SaleTrade> saleTrades);

    /**
     * 销货单初始化
     * @param staff
     * @param saleTrade
     */
    protected void initSaleTrade(Staff staff, SaleTrade saleTrade) {
        saleTrade.setCompanyId(staff.getCompanyId());
        //新建销货单属性初始化
        if (saleTrade.getId() == null) {
            saleTrade.setSysMakerId(staff.getId());
            saleTrade.setSysMaker(staff.getName());
            if (saleTrade.getStatus() == null) {
                saleTrade.setStatus(SaleTradeStatusEnum.DRAFT.getSaleOrderStatus());
            }
            if (saleTrade.getCheckoutTime() == null) {
                saleTrade.setCheckoutTime(new Date());
            }
            saleTrade.initOrderInfo();  //id为空，新增，重算商品信息
        }
        if (CollectionUtils.isNotEmpty(saleTrade.getOrders())) {
            //初始化商品信息
            for (SaleOrder saleOrder : saleTrade.getOrders()) {
                if (saleOrder.getId() == null) {
                    saleOrder.setId(idWorkerService.nextId());
                    saleOrder.setCreated(new Date());
                }
                saleOrder.setCompanyId(staff.getCompanyId());
                saleOrder.setSaleTradeId(saleTrade.getId());
                saleOrder.setSkuSysId(ObjectUtils.defaultIfNull(saleOrder.getSkuSysId(), -1L));
                saleOrder.setModified(new Date());
            }
        }
        //自提方式结账的销货单新增和修改逻辑设置默认地址 目前订单是获取的仓库地址信息
        if (Objects.equals(saleTrade.getPickGoodsType(), TradePickGoosTypeEnum.SELF_PICK.getPickGoodsType())) {
            saleTrade.setReceiverAddress(SaleConstants.SELF_PICK_RECEIVER_ADDRESS + "（不邮寄）");
            saleTrade.setReceiverPhone(SaleConstants.SELF_PICK_RECEIVER_PHONE);
            saleTrade.setReceiverMobile(SaleConstants.SELF_PICK_RECEIVER_PHONE);
            saleTrade.setReceiverState(SaleConstants.SELF_PICK_RECEIVER_STATE);
            saleTrade.setReceiverCity(SaleConstants.SELF_PICK_RECEIVER_CITY);
            saleTrade.setReceiverDistrict(SaleConstants.SELF_PICK_RECEIVER_DISTRICT);
            saleTrade.setReceiverName(SaleConstants.SELF_PICK_RECEIVER_NAME);
        }
    }

    public int save(Staff staff, SaleTrade saleTrade, Integer settleType) {
        initSaleTrade(staff, saleTrade);
        int i = 0;
        if(saleTrade.getId() == null) {
            saleTrade.setId(idWorkerService.nextId());
            saleTrade.setCreated(new Date());
            saleTrade.getOrders().forEach(r -> r.setSaleTradeId(saleTrade.getId()));
            saleTrade.initOrderInfo();
            //生成销货单时需要生成分享链接用于打印生成二维码
            saleTrade.setExtInfo(Objects.nonNull(saleTrade.getExtInfo()) ? saleTrade.getExtInfo() : new TradeExtInfo());
            saleTrade.getExtInfo().setShareUrl(getShareUrl(staff, saleTrade.getId()));
            saleTradeRepo.save(staff, saleTrade);
            Customer customer = saleTrade.getCustomer();
            OrderInfo orderInfo = saleTrade.getOrderInfo();
            log.info("批发收银数据监控, 销货单版本: {}; 销货单创建终端: {}; 销货单出货商品数: {}, 出货商品金额: {}; 退货商品数: {}, 退货商品金额: {}",
                    customer.getStVersion(), saleTrade.getOrigin(), orderInfo.getOutItemNum(), orderInfo.getOutTotalFee(),
                    orderInfo.getRetItemNum(), orderInfo.getRetTotalFee());
            if (customer.getStVersion() == 2) {  //减少数据，仅v2插入日志
                addOperationLog(staff, saleTrade, settleType);
            }
        } else {
            i = saleTradeRepo.update(staff, saleTrade);
        }
        return i;
    }

    /**
     * 获取共享链接信息
     */
    private String getShareUrl(Staff staff, Long saleTradeId) {
        String shareUrl = StringUtils.EMPTY;
        try {
            DmsGenerateShareShortUrlRequest request = new DmsGenerateShareShortUrlRequest();
            request.setCompanyId(staff.getCompanyId());
            request.setSaleTradeIds(saleTradeId.toString());
            shareUrl = dmsTradeService.generateShareShortUrl(request);
        } catch (Exception e) {
            log.error("销货单{}获取共享链接信息失败, msg: {}", saleTradeId, e.getMessage());
        }
        return shareUrl;
    }

    /**
     * 添加操作日志
     */
    protected void addOperationLog(Staff staff, SaleTrade saleTrade, Integer settleType) {
        List<TradeTrace> opList = new ArrayList<>();
        SaleOpEnums enums = SaleOpEnums.findBySettleType(settleType);
        TradeTrace tradeTrace = SaleLogUtils.create(staff, saleTrade.getId(), enums, saleTrade.getOrigin());
        opList.add(tradeTrace);
        tradeTraceService.batchAddTradeTrace(staff, opList);
    }

    /**
     * 销货单保存
     * @param staff
     * @param saleTrade
     */
    public int save(Staff staff, SaleTrade saleTrade) {
        //保存逻辑默认收银方式为直接开单
        return save(staff, saleTrade, 1);
    }

    public void batchUpdate(Staff staff, List<SaleTrade> saleTrades) {
        saleTradeRepo.batchUpdate(staff, saleTrades);
    }

    /**
     * 销货单删除前状态校验
     */
    public Result deleteCheck(Staff staff, SaleTrade saleTrade, SaleTradeQueryParam params) {
        if (params.getStatus() != null && !Objects.equals(saleTrade.getStatus(), params.getStatus())) {
            return Result.isFail(ResultCode.SALE_STATUS_ERROR);
        }
        Integer stVersion = saleTrade.getCustomer().getStVersion();
        if (saleTrade.getWorkOrderId() != null) {         //存在售后工单先查询工单状态
            Result<List<WorkOrder>> workOrderResult = afsCustomer.getWorkOrders(staff, Lists.newArrayList(saleTrade.getWorkOrderId()));
            if (!workOrderResult.isSucc()) {
                return workOrderResult;
            }
            WorkOrder workOrder = workOrderResult.getData().get(0);
            //售后工单状态 和 退货入仓状态
            Integer status = workOrder.getStatus(), goodStatus = workOrder.getGoodStatus();
            //v1删除销货单售后工单必须作废
            //v2售后工单为解决或未退货入仓就可以执行作废
            if ((stVersion == 1 && WorkOrderStatusEnum.CANCEL.getCode() != status) ||
                (stVersion == 2 && (WorkOrderStatusEnum.RESOLVE.getCode() == status || Objects.equals(WorkOrderGoodStatusEnum.SELLER_RECEIVED_GOODS.getKey(), goodStatus)))) {
                return Result.isFail(ResultCode.AF_STATUS_CANCEL, Lists.newArrayList(workOrder.getId()));
            }
        }
        if (saleTrade.getSid() != null && saleTrade.getCustomer().getStVersion() == 2) {    //v2判断订单是否发货
            List<Trade> trades = tradeConsumer.queryAllTrades(staff, Lists.newArrayList(saleTrade.getSid()), false);
            List<Long> consignSid = trades.stream().filter(SaleTradeUtils::isTradeConsign).map(Trade::getSid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(consignSid)) {
                return Result.isFail(ResultCode.TRADE_AFTER_GOODS, consignSid);
            }
        }
        //批发收银v1版本档口删除判断逻辑提前
        if (saleTrade.getCustomer().getStVersion() == 1) {
            Result<QueryReceivableOrderResponse> result = fmsConsumer.queryReceivableOrder(staff, saleTrade.getId());
            if (result.isSucc() && !Objects.equals(result.getData().getReceivableOrder().getStatus(),100)) {
                return Result.isFail("DELETE_FAIL", "档口订单关联应收单已审核，请先到财务模块'取消审核'应收单");
            }
        }
        return Result.isOk();
    }

    /**
     * 销货单删除
     */
    @Transactional
    public abstract Result delete(Staff staff, SaleTrade saleTrade);

    public SaleTrade queryDetail(Staff staff, SaleTradeQueryParam param) {
        Pagination<SaleTrade> page = queryByPage(staff, param);
        if (CollectionUtils.isEmpty(page.getList())) {
            log.info("销货单为空，直接返回");
            return null;
        }
        SaleTrade saleTrade = page.getList().get(0);
        if (param.isBindStock()) {  //查询出货商品当前库存
            log.info("查询销货单出货商品库存");
            List<SaleOrder> outOrders = saleTrade.buildOrderByType(SaleOrderTypeEnum.OUT_GOODS.getType());
            fillOrderStock(staff, outOrders, saleTrade.getWarehouseId());
        }
        return saleTrade;
    }

    public Pagination<SaleTrade> queryByPage(Staff staff, SaleTradeQueryParam param) {
        Result<List<Long>> itemResult = buildItemQueryParam(staff, param);
        if (!itemResult.isSucc()) {
            return Pagination.empty();
        }
        final Pagination<SaleTrade> pagination = saleTradeRepo.queryByList(staff, param);
        if (CollectionUtils.isEmpty(pagination.getList())) {
            return Pagination.empty();
        }
        final List<SaleTrade> saleTrades = pagination.getList();
        if (param.getStVersion() == null) { //特殊处理，因为当前查询没办法保证所有的查询都有版本号
            param.setStVersion(saleTrades.get(0).getStVersion());
        }
        fillSaleTrade(staff, saleTrades, param);
        return pagination;
    }

    public Long queryCount(Staff staff, SaleTradeQueryParam params) {
        return saleTradeRepo.queryCount(staff, params);
    }

    /**
     * 商品维度查询条件构建
     * @param staff
     * @param param
     */
    public Result<List<Long>> buildItemQueryParam(Staff staff, SaleTradeQueryParam param) {
        if (param.getId() != null || param.getIds() != null) {   //如果原本的查询条件已经存在id了，忽略这个查询条件
            return Result.isOk();
        }
        if (StringUtils.isEmpty(param.getQueryText()) ||
                !SaleTradeQueryTypeEnum.getQueryTypeMaps().containsKey(param.getQueryType())) {
            return Result.isOk();
        }
        SaleOrderQueryParams orderParams = SaleOrderQueryParams.builder()
                .queryFields(Lists.newArrayList("sale_trade_id as saleTradeId"))
                .queryType(param.getQueryType()).queryText(param.getQueryText())
                .enableStatus(1)
                .build();
        List<Map<String, Object>> fieldMaps = saleQueryRepo.queryFieldMap(staff, orderParams);
        if (CollectionUtils.isEmpty(fieldMaps)) {
            return Result.isFail(ResultCode.SALE_ORDER_EMPTY);
        }
        List<Long> saleIds = fieldMaps.stream().map(e -> MapUtils.getLong(e,"saleTradeId")).distinct().collect(Collectors.toList());
        param.setIds(saleIds);
        return Result.isOk();
    }

    /**
     * 填充销货单信息
     * @param staff
     * @param saleTrades
     * @param param
     */
    public void fillSaleTrade(Staff staff, List<SaleTrade> saleTrades, SaleTradeQueryParam param) {
        //填充基础信息
        if (param.isBindBasic()) {
            fillBasicInfo(staff, saleTrades);
            salePaymentBusiness.fillSalePayment(staff, saleTrades, param.getCancelData());
        }
        //填充订单信息
        if (param.isBindTrade()) {
            fillTradeInfo(staff, saleTrades, param);
        }
        //填充商品信息
        if (param.isBindOrders()) {
            buildSaleOrder(staff, saleTrades);
        }
        if (param.bindDeposit()) {
            fillDeposit(staff, saleTrades);
        }
    }

    /**
     * 填充销货单退/出货仓库名称、店铺名称、开单人/制单人名称
     * @param staff
     * @param saleTrades
     */
    public void fillBasicInfo(Staff staff, List<SaleTrade> saleTrades) {
        final List<Staff> staffs = staffService.queryByCompanyId(staff.getCompanyId(), staff.getCompanyName());
        final List<Shop> shops = shopService.queryShopByCompanyId(staff.getCompanyId());
        final List<Warehouse> warehouses = warehouseService.queryByCompanyIds(Lists.newArrayList(staff.getCompanyId()));

        Map<Long, Staff> staffMap = staffs.stream().collect(Collectors.toMap(Staff::getId, Function.identity()));
        Map<Long, Shop> shopMap = shops.stream().collect(Collectors.toMap(Shop::getUserId, Function.identity(), (v1, v2) -> v1));
        Map<Long, Warehouse> warehouseMap = warehouses.stream().collect(Collectors.toMap(Warehouse::getId, Function.identity()));

        for (SaleTrade saleTrade : saleTrades) {
            OrderInfo orderInfo = saleTrade.getOrderInfo();

            Staff submitter = staffMap.get(saleTrade.getSubmitterId()); //开单人
            Staff sysMaker = staffMap.get(saleTrade.getSysMakerId());   //制单人
            Shop shop = shopMap.get(saleTrade.getUserId()); //店铺
            Warehouse outWarehouse = warehouseMap.get(orderInfo.getOutWarehouseId());  //出货仓库
            Warehouse retWarehouse = warehouseMap.get(orderInfo.getRetWarehouseId());    //退货仓库

            if (submitter != null) {
                saleTrade.setSubmitter(submitter.getName());
            }
            if (sysMaker != null) {
                saleTrade.setSysMaker(sysMaker.getName());
            }
            if (shop != null) {
                if (shop.getSimpleTitleConfig() != null && shop.getSimpleTitleConfig() == 1) {
                    saleTrade.setShopName(StringUtils.isNotEmpty(shop.getShortTitle()) ? shop.getShortTitle() : StringUtils.trimToEmpty(shop.getTitle()));
                } else {
                    saleTrade.setShopName(shop.getTitle());
                }
            }
            if (outWarehouse != null) {
                saleTrade.setWarehouseName(outWarehouse.getName());
                orderInfo.setOutWarehouseName(outWarehouse.getName());
            }
            if (retWarehouse != null) {
                saleTrade.setReturnWarehouseName(retWarehouse.getName());
                orderInfo.setRetWarehouseName(retWarehouse.getName());
            }
        }
    }

    /**
     * 填充订单信息 拆单订单只填充主单信息
     * @param staff
     * @param saleTrades
     */
    protected void fillTradeInfo(Staff staff, List<SaleTrade> saleTrades, SaleTradeQueryParam params) {
        List<SaleTrade> outSaleTrade = saleTrades.stream().filter(r -> r.getSid() != null && r.getSid() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outSaleTrade)) {
            log.info("销货单sid为空，不填充订单信息");
            return;
        }
        List<Long> sids = outSaleTrade.stream().map(SaleTrade::getSid).collect(Collectors.toList());
        List<Trade> allTrades = tradeConsumer.queryAllTrades(staff, sids, params.isBindOrders());
        if (CollectionUtils.isEmpty(allTrades)) {
            log.info("订单不存在或已删除, sids: " + JSONObject.toJSONString(sids));
            return;
        }
        if (params.isBindOrders() && params.getStVersion() == 2) {    //如果需要填充商品，填充订单的唯一码信息
            wmsConsumer.fillUniqueCodes(staff, allTrades);
        }
        Map<Long, List<Trade>> tradeMap = allTrades.stream().collect(Collectors.groupingBy(e -> e.getSplitSid() > 0 ? e.getSplitSid() : e.getSid()));
        for (SaleTrade saleTrade : outSaleTrade) {
            List<Trade> trades = tradeMap.get(saleTrade.getSid());
            if (trades == null) {
                continue;
            }
            saleTrade.getTrade().setTrades(trades);
            saleTrade.initTradeInfo();

            if (CollectionUtils.isNotEmpty(saleTrade.getOrders()) && params.getStVersion() == 2) {
                TradeBuilder.fillSaleOrder(saleTrade, trades);
            }
        }
    }

    /**
     * 填充销货单订金信息
     */
    protected void fillDeposit(Staff staff, List<SaleTrade> saleTrades) {
        for (SaleTrade saleTrade : saleTrades) {    //当前查询接口为单个，所以最好不要批量查询
            if (saleTrade.getCustomer().getStVersion() == 1) {  //v1目前没有订金
                continue;
            }
            Result<List<Deposit>> result = fmsConsumer.queryDeposit(staff, saleTrade.getId());
            if (!result.isSucc()) {
                continue;
            }
            saleTrade.getPaymentGather().setDepositInfos(result.getData());
        }
    }

    /**
     * 填充销货单销货商品维度信息
     * @param staff
     * @param saleTrades
     */
    public void buildSaleOrder(Staff staff, List<SaleTrade> saleTrades) {
        List<SaleOrder> saleOrders = Lists.newArrayList();
        Map<Long, List<SaleOrder>> singleMap = Maps.newHashMap();
        for (SaleTrade saleTrade : saleTrades) {
            if (CollectionUtils.isEmpty(saleTrade.getOrders())) {
                continue;
            }
            saleOrders.addAll(saleTrade.getOrders());
            if (saleTrade.getStatus() == SaleTradeStatusEnum.PICKING_GOODS_SUCCESS.getSaleOrderStatus()) {
                Long customerId = saleTrade.getCustomer().getCustomerId();
                saleOrders.stream().filter(e -> CollectionUtils.isNotEmpty(e.getSingleSuits())).forEach(e -> singleMap.computeIfAbsent(customerId, v -> Lists.newArrayList()).addAll(e.getSingleSuits()));
            }
        }
        itemConsumer.fillOrderInfo(staff, saleOrders);
        wmsConsumer.fillAsUniqueCodes(staff, saleTrades);   //填充售后唯一码
        if (!singleMap.isEmpty()) {
            final Integer stVersion = saleTrades.get(0).getCustomer().getStVersion();
            for (Map.Entry<Long, List<SaleOrder>> entry : singleMap.entrySet()) {
                Long customerId = entry.getKey();
                List<SaleOrder> singles = entry.getValue();
                saleOrderService.fillFullOrders(singles, HotItemQueryParam.builder()
                        .stVersion(stVersion).distributorCompanyId(customerId).stockType(0).fillPrice(0).build()
                );
            }
        }
    }

    /**
     * 填充商品库存
     * @param staff
     * @param saleOrders
     */
    public void fillOrderStock(Staff staff, List<SaleOrder> saleOrders, Long warehouseId) {
        //这里转换为order,原因是order代表套件的type在salerOrder中代表出货/退货,坑之一,后续希望能把order和saleOrder分开
        List<Order> orders = saleTradeFactory.buildOrders(staff, saleOrders);
        Result<List<ItemStockDto>> result = wmsConsumer.queryOrderStock(staff, orders, warehouseId);
        if (!result.isSucc()) {
            return;
        }
        Map<String, ItemStockDto> stockDtoMap = result.getData().stream().collect(Collectors.toMap(r ->
                ItemBuilder.buildOrderKey(r.getSysItemId(), r.getSysSkuId()), Function.identity()));
        for (SaleOrder saleOrder : saleOrders) {
            ItemStockDto stockDto = stockDtoMap.get(ItemBuilder.buildOrderKey(saleOrder.getItemSysId(), saleOrder.getSkuSysId()));
            if (stockDto != null) {
                saleOrder.setAvailableStock(stockDto.getAvailableStock());
            } else {
                saleOrder.setAvailableStock(0);
            }
        }
    }

    /**
     * 预配货库存校验，校验可配数
     */
    public Result validateTradeStock(Staff staff, List<Order> orders, Long warehouseId) {
        //初始化商品，填充套件信息
        modifyParentBusiness.initItems4Trade(staff, orders, false, false);
        //填充商品的有效库存
        Result result = wmsConsumer.fillAvailStock(staff, orders, warehouseId);
        if (!result.isSucc()) {
            // 查询库存失败，商品有效库存匹配为0
            orders.forEach(r -> r.setAvailableStock(0));
            return Result.isFail(result.getCode(), result.getMsg(), orders);
        }
        List<String> insufficients = Lists.newArrayList();
        for (Order order : orders) {
            if (order.getNum() > order.getAvailableStock()) {
                insufficients.add(order.getOuterId() + "[" + order.getAvailableStock() + "]");
            }
        }
        if (CollectionUtils.isNotEmpty(insufficients)) {
            return Result.isFail(ResultCode.ITEM_STOCK_ENOUGH, "商品" + StringUtils.join(insufficients, ",") + "可配库存不足!", orders);
        }
        return Result.isOk();
    }

    /**
     * 校验退货商品是否充足
     * @param staff
     * @param saleTrade
     * @return
     */
    public Result checkReturnNum(Staff staff, SaleTrade saleTrade) {
        if (saleTrade.getOriginId() == null) {
            log.info("未绑定出货单，无需计算退货数据");
            return Result.isOk();
        }
        Pagination<SaleTrade> page = queryByPage(staff, SaleTradeQueryParam.builder().stVersion(saleTrade.getStVersion())
                .bindOrders(true).id(saleTrade.getOriginId()).notDel("1").build());
        if(CollectionUtils.isEmpty(page.getList())) {
            return Result.isFail(ResultCode.SALE_TRADE_EMPTY, "绑定的出货单未找到");
        }
        SaleTrade origin = page.getList().get(0);
        List<SaleOrder> retOrders = saleTrade.buildOrderByType(SaleOrderTypeEnum.RETURN_GOODS.getType());
        List<SaleOrder> originOrders = origin.buildOrderByType(SaleOrderTypeEnum.OUT_GOODS.getType());
        Map<String, SaleOrder> originMap = originOrders.stream().collect(Collectors.toMap(SaleOrder::getOrderKey, Function.identity(),
                (v1, v2) -> {   //如果存在重复商品，将数量归类到第一个商品上面计算
                    v1.setNum(v1.getNum() + v2.getNum());
                    v1.setReturnedItemSum(v1.getReturnedItemSum() + v2.getReturnedItemSum());
                    return v1;
                })
        );
        List<SaleOrder> updOrders = Lists.newArrayList();
        Integer returnedItemSum = origin.getReturnedItemSum();
        for (SaleOrder order : retOrders) {
            SaleOrder originOrder = originMap.get(order.getOrderKey());
            if (originOrder == null) {
                continue;
            }
            int ableNum = originOrder.getNum() - originOrder.getReturnedItemSum();
            if (ableNum < order.getNum()) {
                return Result.isFail(ResultCode.RETURN_ORDER_OVER_LIMIT, "退货商品" + order.getOuterId() +"超出可退数");
            }
            returnedItemSum += order.getNum();  //原销货单已退数增加
            // 原出货商品已退数增加
            SaleOrder updOrder = new SaleOrder();
            updOrder.setId(originOrder.getId());
            updOrder.setReturnedItemSum(originOrder.getReturnedItemSum() + order.getNum());
            updOrders.add(updOrder);
        }
        SaleTrade updTrade = new SaleTrade();
        updTrade.setId(saleTrade.getOriginId());
        updTrade.setReturnedItemSum(returnedItemSum);
        updTrade.setOrders(updOrders);
        updTrade.setDealOrder(2);   //销货商品更新
        saleTradeRepo.update(staff, updTrade);  //循环里面update，后续看速度来决定优化
        return Result.isOk();
    }

    /**
     * 计算销货单理论运费
     * @param staff
     * @param saleTrades
     * @return
     */
    public Result calculateTheoryFreight(Staff staff, List<SaleTrade> saleTrades) {
        List<Trade> trades = saleTrades.stream().filter(e -> e.getOrderInfo().getOutItemNum() > 0).map(r -> {
            Trade trade = saleTradeFactory.buildTrade(staff, r);
            trade.setTemplateType(ObjectUtils.defaultIfNull(trade.getTemplateType(), 0));
            trade.setSid(r.getId());    //设置订单sid为销货单id，方便后面取值对应
            return trade;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(trades)) {
            return Result.isFail(ResultCode.CALCULATE_FREIGHT_ERROR, "销货单不存在或无出货商品!");
        }
        Result<List<FreightCost>> result = tradeConsumer.calculateTheoryFreight(staff, trades);
        if (!result.isSucc()) {
            return result;
        }
        List<FreightCost> costs = result.getData();
        //统计参与计算的订单净重
        double netWeight = trades.stream().mapToDouble(Trade::getNetWeight).sum();
        //统计计算的运费总数
        double transportFee = costs.stream().filter(r -> !r.isError()).mapToDouble(FreightCost::getCount).sum();
        List<FreightCost> errorMsg = costs.stream().filter(FreightCost::isError).collect(Collectors.toList());
        CalculateTransportDto transportDto = CalculateTransportDto.builder()
                .transportFee(transportFee).netWeight(netWeight).resultMsg(errorMsg).build();

        //过滤出id不为空的销货单，为这些销货单设置查询出来的理论运费
        List<SaleTrade> collect = saleTrades.stream().filter(r -> r.getId() != null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            Map<Long, FreightCost> costMap = costs.stream().collect(Collectors.toMap(FreightCost::getSid, Function.identity()));
            for (SaleTrade saleTrade : collect) {
                FreightCost cost = costMap.get(saleTrade.getId());
                if (cost == null || cost.isError()) {
                    saleTrade.getOrderInfo().setTransportFee(BigDecimal.ZERO);
                    continue;
                }
                saleTrade.getOrderInfo().setTransportFee(BigDecimal.valueOf(cost.getCount()));
                if (cost.getFreightTemplateId() != null && cost.getFreightTemplateId() > 0) {
                    saleTrade.getAddress().setTemplateId(cost.getFreightTemplateId());
                }
            }
        }
        return Result.isOk(transportDto);
    }

}

package com.raycloud.dmj.business.logistics;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.SendType;
import com.raycloud.dmj.domain.enums.TradeExtendConfigsEnum;
import com.raycloud.dmj.domain.platform.trades.ConsignRequest;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.tag.TradeTag;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trade.except.TradeExceptUtils;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.domain.trades.utils.TradeTagUtils;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.user.UserConf;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.enums.UserExtraConfEnum;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.pt.dubbo.IExpressTemplateDubboService;
import com.raycloud.dmj.services.trades.IExpressCompanyService;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.utils.ConsignUtils;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.business.logistics.SkuOneTimeUploadBusiness.isMultiplePackagesUpload;
import static com.raycloud.dmj.domain.constant.SystemTags.TAG_AFTER_DELIVER_AFTER_SALE;
import static com.raycloud.dmj.domain.constant.SystemTags.TAG_BEFORE_DELIVER_AFTER_SALE;
import static com.raycloud.dmj.domain.utils.CommonConstants.*;

import static com.raycloud.dmj.services.utils.ConsignUtils.isSupportNonOutSid;
import static com.raycloud.dmj.business.logistics.SkuOneTimeUploadBusiness.isMultiplePackagesUploadBySku;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2020-09-15 13:55
 * @Description UploadUtils
 */
public class UploadUtils {

    private static Logger logger = Logger.getLogger(UploadUtils.class);

    public final static Set<String> SPLIT_SUPPORTS_PLAT = new HashSet<>();

    static {
        SPLIT_SUPPORTS_PLAT.add(CommonConstants.PLAT_FORM_TYPE_SYS);
        SPLIT_SUPPORTS_PLAT.add(CommonConstants.PLAT_FORM_TYPE_TIAN_MAO);
        SPLIT_SUPPORTS_PLAT.add(CommonConstants.PLAT_FORM_TYPE_TAO_BAO);
        SPLIT_SUPPORTS_PLAT.add(CommonConstants.PLAT_FORM_TYPE_PDD);
        SPLIT_SUPPORTS_PLAT.add(CommonConstants.PLAT_FORM_TYPE_FXG);
        //修复苏宁按数量拆单后可能的发货失败(按数量拆单后没有复制soid）
        SPLIT_SUPPORTS_PLAT.add(CommonConstants.PLAT_FORM_TYPE_SN);
    }

    public final static Set<String> LIMIT_SECOND_UPLOAD_PLATS = new HashSet<>();

    static {
        LIMIT_SECOND_UPLOAD_PLATS.add(PLAT_FORM_TYPE_POISON);
        LIMIT_SECOND_UPLOAD_PLATS.add(PLAT_FORM_TYPE_FXG_GX);
    }

    public static boolean isSpcPlatSplitTrade(String source) {
        return SPLIT_SUPPORTS_PLAT.contains(source);
    }

    public static void groupUploadTrades(List<Trade> trades, List<Trade> normalTrades, List<Trade> fxTrades) {
        trades.forEach(trade -> (trade.getConvertType() != null && trade.getConvertType() == 1 ? fxTrades : normalTrades).add(trade));
    }

    public static boolean hasShopPrivilege(Set<String> userIds, String[] staffUserIds) {
        for (String staffUserId : staffUserIds) {
            if (userIds.contains(staffUserId)) {
                return true;
            }
        }
        return false;
    }

    public static Map<String, Long> groupByUserId(Collection<ConsignRecord> failedTrades) {
        Map<String, Long> map = new HashMap<>();
        for (ConsignRecord record : failedTrades) {
            Long count = map.get(record.getUserId() + "");
            if (count == null) {
                count = 0L;
            }
            count++;
            map.put(record.getUserId() + "", count);
        }
        return map;
    }

    public static List<Trade> filterSubscribeLogisticsPlatformTrades(List<ConsignRecord> records, List<Trade> trades) {
        //只订阅非跨境的订单
        List<ConsignRecord> consignRecords = records.stream().filter(c -> !CommonConstants.PLAT_FORM_TYPE_KUAJ.equals(c.getSource())).collect(Collectors.toList());
        Map<Long, Trade> sid2Trades = TradeUtils.toMapBySid(trades);
        List<Trade> pddTrades = Lists.newArrayList();
        for (ConsignRecord consignRecord : consignRecords) {
            if(consignRecord.getIsError()==1||"订单在平台已发货".equals(consignRecord.getErrorDesc())||"0".equals(consignRecord.getEnableStatus())){
                continue;
            }
            Trade origionTrade = sid2Trades.get(consignRecord.getSid());
            Trade trade = new Trade();
            trade.setCompanyId(consignRecord.getCompanyId());
            trade.setSource(consignRecord.getSource());
            trade.setSid(consignRecord.getSid());
            trade.setConsignTime(consignRecord.getUploadTime());
            trade.setUserId(consignRecord.getUserId());
            trade.setOutSid(consignRecord.getOutSid());
            trade.setTemplateId(consignRecord.getTemplateId());
            trade.setTemplateType(consignRecord.getTemplateType());
            trade.setWarehouseId(consignRecord.getWarehouseId());
            trade.setWarehouseName(consignRecord.getWarehouseName());
            trade.setTid(consignRecord.getTid());
            trade.setPtConsignTime(consignRecord.getUploadTime());
            if (origionTrade != null) {
                trade.setReceiverName(origionTrade.getReceiverName());
                trade.setReceiverMobile(origionTrade.getReceiverMobile());
                trade.setReceiverState(origionTrade.getReceiverState());
                trade.setReceiverCity(origionTrade.getReceiverCity());
                trade.setReceiverDistrict(origionTrade.getReceiverDistrict());
                trade.setReceiverAddress(origionTrade.getReceiverAddress());
                trade.setTid(origionTrade.getTid());
                trade.setTaobaoId(origionTrade.getTaobaoId());
                trade.setAddressMd5(origionTrade.getAddressMd5());
                trade.setDestId(origionTrade.getDestId());
                trade.setSubSource(origionTrade.getSubSource());
                trade.setConvertType(origionTrade.getConvertType());
                trade.setBelongType(origionTrade.getBelongType());
                trade.setPayTime(origionTrade.getPayTime());
            }

            pddTrades.add(trade);
        }
        return pddTrades;
    }

    private static List<UploadRecord> buildUploadRecord(Staff staff, List<Trade> trades, SendType sendType) {
        List<UploadRecord> uploadRecords = new ArrayList<>();
        for (Trade trade : trades) {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            for (Order order : orders) {
                String platSysStatus = CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSysStatus()) ? order.getSysStatus() : TradeStatus.getSysStatus(order.getStatus(), order.getSysStatus());
                if (Trade.SYS_STATUS_CLOSED.equals(platSysStatus)) {//交易关闭的就不处理了
                    continue;
                }
                //重新发货 或者 平台状态没有变成已发货
                if (sendType == SendType.RESEND || (!Trade.SYS_STATUS_FINISHED.equals(platSysStatus) && !Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(platSysStatus))) {
                    uploadRecords.add(buildUploadRecord(staff, trade, order));
                }
            }
        }
        return uploadRecords;
    }

    public static UploadRecord buildUploadRecord(Staff staff, Trade trade, Order order) {
        UploadRecord uploadRecord = new UploadRecord();
        uploadRecord.setCompanyId(staff.getCompanyId());
        uploadRecord.setOrderId(order.getId());
        uploadRecord.setSid(order.getSid());
        uploadRecord.setTid(order.getTid());
        uploadRecord.setOid(order.getOid());
        uploadRecord.setUploadStatus(1);
        if (trade != null) {
            uploadRecord.setTemplateId(trade.getTemplateId());
            uploadRecord.setTemplateType(trade.getTemplateType());
            uploadRecord.setOutSid(trade.getOutSid());
        }
        return uploadRecord;
    }

    public static Long getSplitUploadKey(Staff staff, Order order) {
        return order.getOid() != null && order.getOid() > 0 ? order.getOid() : order.getId();
    }

    /**
     * order是否需要在upload_record里面记录
     */
    public static boolean needRecord(Staff staff, Order order, SendType sendType) {
        String platSysStatus = StringUtils.isEmpty(order.getStatus()) ? order.getSysStatus() : TradeStatus.getSysStatus(order.getStatus(), order.getSysStatus());
        return needRecord(staff, platSysStatus, sendType);
    }

    /**
     * order是否需要在upload_record里面记录
     */
    public static boolean needRecord(Staff staff, String platSysStatus, SendType sendType) {
        //交易关闭的订单不允许上传
        if (Trade.SYS_STATUS_CLOSED.equals(platSysStatus)) {
            return false;
        }
        //已发货的只允许直接上传、二次上传和重新发货,其中直接上传也会在TbConsignUploader那里过滤掉
        if (Trade.SYS_STATUS_FINISHED.equals(platSysStatus) || Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(platSysStatus)) {
            return sendType == SendType.SEND_TWICE || sendType == SendType.RESEND;
        }
        //正常的订单不允许调重新发货
        return sendType != SendType.RESEND;
    }

    public static List<Trade> buildSimpleTrades(List<Trade> trades) {
        List<Trade> list = new ArrayList<>(trades.size());
        for (Trade trade : trades) {
            TbTrade tempTrade = new TbTrade();
            tempTrade.setSid(trade.getSid());
            tempTrade.setTid(trade.getTid());
            tempTrade.setUserId(trade.getUserId());
            tempTrade.setType(trade.getType());
            tempTrade.setOutSid(trade.getOutSid());
            tempTrade.setTemplateId(trade.getTemplateId());
            tempTrade.setLogisticsCompanyId(trade.getLogisticsCompanyId());
            tempTrade.setTemplateName(trade.getTemplateName());
            tempTrade.setTemplateType(trade.getTemplateType());
            tempTrade.setCreated(trade.getCreated());
            tempTrade.setConsignTime(trade.getConsignTime());
            tempTrade.setCompanyId(trade.getCompanyId());
            tempTrade.setSource(trade.getSource());
            tempTrade.setExpressPrintTime(trade.getExpressPrintTime());
            tempTrade.setConsignType(trade.getConsignType());
            tempTrade.setSellerMemo(trade.getSellerMemo());
            tempTrade.setMergeType(trade.getMergeType());
            tempTrade.setMergeSid(trade.getMergeSid());
            tempTrade.setSysStatus(trade.getSysStatus());
            tempTrade.setSellerFlag(trade.getSellerFlag());
            tempTrade.setShortId(trade.getShortId());
            tempTrade.setLogisticsCompanyId(trade.getLogisticsCompanyId());
            tempTrade.setLogisticsCompanyName(trade.getLogisticsCompanyName());
            list.add(tempTrade);
            TradeUtils.getOrders4Trade(trade).forEach(o -> {
                TbOrder tempOrder = new TbOrder();
                tempOrder.setId(o.getId());
                tempOrder.setSid(o.getSid());
                tempOrder.setTid(o.getTid());
                tempOrder.setItemSysId(o.getItemSysId());
                tempOrder.setSkuSysId(o.getSkuSysId());
                tempOrder.setSysSkuRemark(o.getSysSkuRemark());
                tempOrder.setSysSkuPropertiesName(o.getSysSkuPropertiesName());
                tempOrder.setSysOuterId(o.getSysOuterId());
                tempOrder.setNum(o.getNum());
                tempTrade.getOrders().add(tempOrder);
            });
        }
        return list;
    }

    // deliveryType: 1-快递、2-送货上门
    public static Integer getDeliveryType(String extendedParams) {
        if (StringUtils.isBlank(extendedParams) || !extendedParams.contains("deliveryType")) {
            return -1;
        }
        JSONObject json = JSON.parseObject(extendedParams);
        return json.getInteger("deliveryType");
    }

    // 是否合并发货
    public static boolean isMergeUpload(String extPrams) {
        if (StringUtils.isBlank(extPrams) || !extPrams.contains("isMerge")) {
            return false;
        }
        return JSON.parseObject(extPrams).getBoolean("isMerge");
    }

    public static ConsignRequest buildConsignRequest(Staff staff, UploadData uploadData, Trade trade, Map<Long, ConsignRecord> existMap) {
        ConsignRequest req = new ConsignRequest();
        req.setTrade(trade);
        if(CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(trade.getSource())||CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(trade.getSplitSource())||CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(trade.getSubSource())) {
            if(SendType.MANUAL_UPLOAD.equals(uploadData.sendType)||uploadData.reUpload!=null&&uploadData.reUpload==1){
                req.setDataSendType(5);
            }
        }
        req.setType(getSendType(staff, uploadData, trade, existMap));
        req.setDymmyType(uploadData.dymmyType);
        req.setNoLogisticsName(uploadData.noLogisticsName);
        req.setNoLogisticsTel(uploadData.noLogisticsTel);
        trade.setConsignType(req.getType());
        if (StringUtils.isNotBlank(uploadData.extendedParams)) {
            req.setExtendParams(new HashMap<String, Object>() {{put("extendedParams", uploadData.extendedParams);}});
        }
        return req;
    }

    public static ConsignRequest buildConsignRequest(Staff staff, UploadData uploadData, List<Trade> tradeList, Map<Long, ConsignRecord> existMap) {
        ConsignRequest req = new ConsignRequest();
        req.setTradeList(tradeList);
        req.setType(uploadData.sendType);
        req.setDymmyType(uploadData.dymmyType);
        req.setNoLogisticsName(uploadData.noLogisticsName);
        req.setNoLogisticsTel(uploadData.noLogisticsTel);
        return req;
    }

    public static User buildGxUser(Staff staff, Long companyId) {
        User gxUser = new User();
        gxUser.setStaff(staff);
        gxUser.setCompanyId(companyId);
        gxUser.setActive(1);
        gxUser.setEnableStatus(1);
        return gxUser;
    }

    public static String getPlatformSource(Trade trade){
        if("sys".equals(trade.getSource())){
            return trade.getSplitSource();
        }else{
            return trade.getSource();
        }
    }

    public static SendType getSendType(Staff staff, UploadData uploadData, Trade trade, Map<Long, ConsignRecord> existMap) {

        if(PLAT_FORM_TYPE_QIMEN.equals(getPlatformSource(trade))&&uploadData.sendType==SendType.SEND_TWICE) {
            //奇门快递转发识别为二次上传
            return SendType.SEND_TWICE;
        }

        //如果是淘宝天猫订单 并且 开启了重新发货配置 并且订单在平台已发货 则调用重新发货
        if ((CommonConstants.PLAT_FORM_TYPE_TAO_BAO.equals(getPlatformSource(trade))
                || CommonConstants.PLAT_FORM_TYPE_TIAN_MAO.equals(getPlatformSource(trade))
                || PLAT_FORM_TYPE_XHS.equals(getPlatformSource(trade))
                || CommonConstants.PLAT_FORM_TYPE_FXG.equals(getPlatformSource(trade))
                || CommonConstants.PLAT_FORM_TYPE_KUAI_SHOU.equals(getPlatformSource(trade))
                || CommonConstants.PLAT_FORM_TYPE_PDD.equals(getPlatformSource(trade))
                || CommonConstants.PLAT_FORM_TYPE_JD.equals(getPlatformSource(trade))
                || CommonConstants.PLAT_FORM_TYPE_WXSPH.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_1688_C2M.equals(getPlatformSource(trade))
                || PLAT_FORM_TYPE_FXG_DF.equals(getPlatformSource(trade))
                || PLAT_FORM_TYPE_QIMEN.equals(getPlatformSource(trade))
//                || (CommonConstants.PLAT_FORM_TYPE_KTT.equals(getPlatformSource(trade)) && CommonConstants.PLAT_FORM_TYPE_KTT_SUPPLY.equals(trade.getSubSource()))
                || (CommonConstants.PLAT_FORM_TYPE_BAIDU_HEALTH.equals(getPlatformSource(trade)) || CommonConstants.PLAT_FORM_TYPE_BAIDU_HEALTH.equals(trade.getSubSource()))
            )   && (uploadData.openReUploadConsign || cancelConsigned(trade) || TradeUtils.isFxOrMixTrade(trade)) //分销默认开启重传配置
                && platformIsConsigned(staff, trade)) {
            if (isAutoResendType(uploadData, trade)) {
                trade.setIsAutoReUpload(1);
            }

            return SendType.RESEND;
        }
        // KMERP-143111: 快团团订单拆单上传优化
        if (isKttSupplyTrade(trade)
                && (uploadData.openReUploadConsign || cancelConsigned(trade))
                && platformIsConsigned(staff, trade)
                && Objects.nonNull(uploadData.getSuccessExistUploadRecord(trade))) {
            if (isAutoResendType(uploadData, trade)) {
                trade.setIsAutoReUpload(1);
            }
            return SendType.RESEND;
        }

        //如果是网易严选订单，并且订单在平台已发货，则调用二次上传
        if (CommonConstants.PLAT_FORM_TYPE_NETEASE.equals(getPlatformSource(trade)) && platformIsConsigned(staff, trade)) {
            return SendType.SEND_TWICE;
        }

        if (uploadData.sendType != null) {
            if(SendType.MANUAL_UPLOAD.equals(uploadData.sendType)){
                return SendType.UPLOAD;
            }
            return uploadData.sendType;
        }
        if (existMap != null && existMap.size() > 0) {
            ConsignRecord record = existMap.get(trade.getSid());
            //重新上传时根据上次发货记录确认发货类型，如果没有发货记录，则根据订单是否有快递单号和模版确定是虚拟发货还是线下发货
            if (record != null && StringUtils.isNotBlank(record.getConsignType())) {
                return ConsignUtils.getSendType(record.getConsignType());
            }
        }
        return StringUtils.isNotBlank(trade.getOutSid()) && trade.getTemplateId() > 0 ? SendType.OFFLINE : SendType.DUMMY;
    }

    /**
     * 判断是否是由配置自动转换的重新发货
     * 判断依据为:非撤销发货订单 且 原参数中的sendType不为Resend
     */
    private static boolean isAutoResendType(UploadData uploadData, Trade trade) {
        return !cancelConsigned(trade) && !SendType.RESEND.equals(uploadData.sendType);
    }

    /**
     * 是否是撤销发货订单
     */
    private static boolean cancelConsigned(Trade trade) {
        return trade.getIsUpload() != null && trade.getIsUpload() - 3 >= 0;
    }

    /**
     * 是否支持已经预发货的订单拆分后重新发货上传
     * 目前只校验根据配置自动转换而来的重新发货
     * 这里的判断依据是，这笔订单为拆单，且所有子单都已被其他sid上传过了
     */
    public static boolean validateResendSplit(UploadData uploadData, Trade trade) {
        //允许重新发货 或者 非拆单
        if (uploadData.openSplitReUploadConsign || !TradeUtils.isSplit(trade)) {
            return true;
        }
        //拆单判断这笔子单存在oid没有上传记录或者记录归属于当前sid，才会去触发重新上传。如果oid都已被其他sid上传过了则不触发重新上传。
        Map<Long, List<UploadRecord>> oidUploadRecordMap = uploadData.beforeTidOidUploadRecordsMap.get(trade.getTid());
        if (MapUtils.isEmpty(oidUploadRecordMap)) {
            return true;
        }
        for (Order order : TradeUtils.getOrders4Trade(trade)) {
            List<UploadRecord> uploadRecords = oidUploadRecordMap.get(order.getOid());
            if (CollectionUtils.isEmpty(uploadRecords)) {
                return true;
            }
            for (UploadRecord uploadRecord : uploadRecords) {
                if (uploadRecord.getSid() - order.getSid() == 0) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否支持其他erp发货订单重新发货上传
     * 目前只校验根据配置自动转换而来的重新发货
     */
    public static boolean validateResendOtherConsigned(UploadData uploadData, Trade trade) {
        return uploadData.openReUploadOtherConsigned || !ObjectUtils.nullSafeEquals(trade.getSysConsigned(), 2);
    }

    /**
     * 是否平台已发货
     */
    public static boolean platformIsConsigned(Staff staff, Trade trade) {
        if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(TradeStatus.getSysStatus(trade.getStatus(), null))) {//如果订单在系统的平台状态是卖家已发货
            return true;
        } else if (TradeStatus.TB_SELLER_CONSIGNED_PART.equals(trade.getStatus()) || TradeUtils.isSplit(trade)) {
            boolean splitOrderAllConsigned = true;
            for (Order order : TradeUtils.getOrders4Trade(trade)) {//拆单的order平台状态只要存在待发货，则返回false，需要上传
                splitOrderAllConsigned = splitOrderAllConsigned && TradeStatusUtils.isAfterSendGoods(TradeStatus.getSysStatus(order.getStatus(), null));
            }
            return splitOrderAllConsigned;
        }
        return false;
    }

    public static boolean validateBase(Staff staff, UploadDelayBusiness uploadDelayBusiness, UploadData uploadData, Trade trade, UploadResult result, boolean filterClose, ConsignRecord consignRecord) {
        //系统订单及已成功上传的订单不需要上传，也不生成发货上传记录
        if (trade.getNeedUploadTrade() != null && !trade.getNeedUploadTrade()) {
            return false;
        }

        if(LIMIT_SECOND_UPLOAD_PLATS.contains(trade.getSource())){
            List<UploadRecord> uploadRecords = uploadData.beforeSidUploadRecordsMap.get(trade.getSid());
            if(CollectionUtils.isNotEmpty(uploadRecords)){
                boolean isAllUploaded = true;
                for(UploadRecord uploadRecord : uploadRecords){
                    if(uploadRecord.getUploadStatus()==0){
                        isAllUploaded = false;
                    }
                }
                if(isAllUploaded){
                    return false;
                }
            }
        }


        //非重新发货的情况下，已成功上传的订单不需要再上传
        SendType sendType = getSendType(staff, uploadData, trade, null);
        String sysStatus = TradeStatus.getSysStatus(trade.getStatus(), null);
        if(PlatformUtils.isQTGTrades(trade)&& UploadUtils.platformIsConsigned(staff, trade)){
            result.addConsignRecord(staff, trade, sendType, 0, 1, "全托管平台已发货不再触发上传");
            return false;
        }

        if((PLAT_FORM_TYPE_KTT.equals(trade.getSource())||PLAT_FORM_TYPE_KTT.equals(trade.getSubSource()))&&SendType.DUMMY.equals(sendType)){
            result.addConsignRecord(staff, trade, sendType, 0, 1, "快团团订单虚拟发货，无需向平台发货上传");
            return false;
        }

        if(PlatformUtils.is1688ZiTiTrade(trade)) {
            result.addConsignRecord(staff, trade, sendType, 1, 1, "1688自提订单不支持快递发货上传");
            return false;
        }

        if(PlatformUtils.isTradeO2o(trade)){
            result.addConsignRecord(staff, trade, sendType, 1, 0, "O2o订单不需要上传");
            return false;
        }

        if(Trade.SYS_STATUS_CLOSED.equals(sysStatus)){//当前单交易关闭 才考虑打不打异常

            if(filterClose){
                if(TradeUtils.isMerge(trade)){
                    //合单
                    if(!trade.getMergeSid().equals(trade.getSid())){
                        //用主单的状态进行判断
                        for(Trade trade1 : uploadData.trades){
                            if(trade.getMergeSid().equals(trade1.getMergeSid())&&!trade.getSid().equals(trade1.getSid())){
                                sysStatus = TradeStatus.getSysStatus(trade1.getStatus(), null);
                                break;
                            }
                        }
                    }
                    if (Trade.SYS_STATUS_SELLER_SEND_GOODS.equals(sysStatus)) {//      合单订单系统状态为已发货
                        if(TradeTagUtils.checkIfExistTag(trade, TAG_AFTER_DELIVER_AFTER_SALE)){//        有发货后售后标签：打异常
                            result.addConsignRecord(staff, trade, sendType, 1, 1, "未触发上传:订单交易关闭");
                        }
                    }else if(!TradeStatusUtils.isAfterSendGoods(sysStatus)){
                        //      合单订单系统状态为未发货：不打异常
                        result.addConsignRecord(staff, trade, sendType, 1, 0, "交易关闭，按配置过滤不上传");
                    }else{
                        if(TradeTagUtils.checkIfExistTag(trade, TAG_BEFORE_DELIVER_AFTER_SALE)){//
                            result.addConsignRecord(staff, trade, sendType, 1, 0, "交易关闭，按配置过滤不上传");
                        }else{
                            //     合单整单交易关闭 打异常
                            result.addConsignRecord(staff, trade, sendType, 1, 1, "未触发上传:订单交易关闭");
                        }
                    }
                }else{
                    //非合单情况下   非合单订单整单交易关闭打异常。
                    result.addConsignRecord(staff, trade, sendType, 1, 1, "未触发上传:订单交易关闭");
                }
            }else{
                //不开启图1-1配置，交易关闭的订单都会打异常，现有逻辑。
                result.addConsignRecord(staff, trade, sendType, 1, 1, "未触发上传:订单交易关闭");
            }
            return false;
        }
        if(CommonConstants.PLAT_FORM_TYPE_SELF_BUILT.equals(trade.getSource())|| PLAT_FORM_TYPE_SELF_BUILT.equals(trade.getSplitSource())){
            result.addConsignRecord(staff, trade, sendType, 0, 1, "自建平台的订单默认过滤不上传发货");
            return false;
        }



        String error;


        //重新上传时根据上次发货记录确认发货类型，如果没有发货记录，则根据订单是否有快递单号和模版确定是虚拟发货还是线下发货
        if (null != consignRecord && StringUtils.isNotBlank(consignRecord.getConsignType())) {
            //基本条件校验
            error = ConsignUtils.validateConsign(staff, trade, false, ConsignUtils.getSendType(consignRecord.getConsignType()), uploadData.tradeConfig, uploadData.warehouseIdTypeMap, null, null, uploadData.dymmyType, uploadData.noLogisticsName, uploadData.noLogisticsTel,uploadData.isExternal);
        } else {
            error = ConsignUtils.validateConsign(staff, trade, false, uploadData.sendType, uploadData.tradeConfig, uploadData.warehouseIdTypeMap, null, null, uploadData.dymmyType, uploadData.noLogisticsName, uploadData.noLogisticsTel,uploadData.isExternal);
        }
        if (error != null) {
            result.addConsignRecord(staff, trade, sendType, 1, 1, error);
            return false;
        }
        String errMsg = null;
        //重新发货时，平台状态已经是已发货，不需要校验
        //非重新发货，且平台已经发货&平台支持追加上传，不需要校验
        if (sendType != SendType.RESEND && sendType != SendType.SEND_TWICE && UploadUtils.platformIsConsigned(staff, trade) && !TradeUtils.isSupportAppendUpload(staff, trade)) {
            int enableStatus;
            int isError = 1;
            if (equalsExpress(buildExpressData(trade), buildExpressData(uploadData.getExistUploadRecord(trade)))) {
                errMsg = "订单在平台已发货";
                enableStatus = 0;
            } else if(CommonConstants.PLAT_FORM_TYPE_BTAS.equals(trade.getSubSource()) || isMultiplePackagesUpload(uploadData.tradeConfig, trade)||isMultiplePackagesUploadBySku(uploadData.tradeConfig, trade)){
                return true;
            }else{
                errMsg = "订单在平台已发货(请检查模板，快递单号等信息，如果一致可以直接取消上传，如果不一致操作重新发货(重新发货只支持淘宝天猫))";
                enableStatus = uploadData.openReUploadConsign ? 1 : 0;
            }
            if(ConsignUtils.isSupportNonOutSid(trade)){
                errMsg = "订单所有商品已发货，不支持继续上传发货";
                enableStatus = 1;
                isError = 0;
            }
            result.addConsignRecord(staff, trade, sendType, enableStatus, isError, errMsg);
            return false;
        } else if (sendType == SendType.RESEND) { // 重新发货上传
            if(CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(trade.getSource())||CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(trade.getSplitSource())||CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(trade.getSubSource())) {
                return true;
            }
            if (equalsExpress(buildExpressData(trade), buildExpressData(uploadData.getExistUploadRecord(trade)))) {
                errMsg = "订单在平台已发货，快递信息一致，无需重新发货上传";
                result.addConsignRecord(staff, trade, sendType, 0, 1, errMsg);
                return false;
            }
            //根据配置自动转换而来的重新发货需要再次校验
            if (trade.getIsAutoReUpload() != null && trade.getIsAutoReUpload() == 1) {
                if (!validateResendOtherConsigned(uploadData, trade)) {
                    result.addConsignRecord(staff, trade, sendType, 0, 3, "未开启「其他erp发货的订单，上传平台发货」配置");
                    return false;
                }
                if (!validateResendSplit(uploadData, trade)) {
                    result.addConsignRecord(staff, trade, sendType, 0, 3, "未开启「预发货的拆分订单重新发货上传」配置");
                    return false;
                }
            }
        } else if (sendType == SendType.SEND_TWICE) { // 二次上传
            if (equalsExpress(buildExpressData(trade), buildExpressData(uploadData.getExistUploadRecord(trade)))) {
                errMsg = "快递信息重复，无需合并上传";
                result.addConsignRecord(staff, trade, sendType, 0, 1, errMsg);
                return false;
            }
        } else if (Trade.SYS_STATUS_FINISHED.equals(sysStatus)) {
            errMsg = "订单在平台已完成(可以直接取消上传)";
            result.addConsignRecord(staff, trade, sendType, 0, 1, errMsg);
            return false;
        }

        if (trade.getUserId() > 0 && !TradeUtils.isGxTrade(trade)) {//非出库单
            //供销发货时不校验
            if (!TradeUtils.isGxTrade(trade)) {
                if (trade.getUserId() > 0) {//非出库单
                    User user = staff.getUserByUserId(trade.getUserId());
                    if (user != null && CommonConstants.PLAT_FORM_TYPE_POISON.equals(user.getSource()) && user.getInvalidSession() != null && user.getInvalidSession() == 1) {
                        errMsg = "店铺授权已失效";
                    }
                }
            }
        }

        if (errMsg != null) {
            result.uploadExcepRecords.put(trade.getSid(), result.addConsignRecord(staff, trade, sendType, 1, 1, errMsg));
            return false;
        }

        //不是重新上传 && 是否延迟上传发货
        if ((uploadData.reUpload == null || uploadData.reUpload != 1) && uploadDelayBusiness.isUploadDelay(staff, trade)) {
            result.addConsignRecord(staff, trade, sendType, 1, 2, "延时发货");
            result.uploadDelayTrades.add(trade);
            return false;
        }

        //京东自营订单延迟上传发货
        if ((uploadData.reUpload == null || uploadData.reUpload != 1) && TradeUtils.isPlatformSourceTrade(staff, trade, CommonConstants.PLAT_FORM_TYPE_JD_VC)) {
            result.addConsignRecord(staff, trade, sendType, 1, 2, "京东自营出库中");
            uploadData.jdVcDelaySendTrades.add(trade);
            return false;
        }

        return true;
    }

    public static boolean equalsExpress(ExpressData e1, ExpressData e2) {
        return e1 != null && e2 != null && StringUtils.equals(createTplKey(e1.templateId, e1.templateType), createTplKey(e2.templateId, e2.templateType)) && StringUtils.equals(e1.outSid, e2.outSid);
    }

    public static void validateExpress(Staff staff, TradeConfig tradeConfig, IExpressTemplateDubboService expressTemplateDubboService, IStaffService staffService, UploadData uploadData, ConsignRequest req, UploadResult result, Map<String, UserExpressTemplate> expressTemplateMap, Map<Long, ExpressCompany> expressCompanyMap, FxUploadBusiness fxUploadBusiness, IExpressCompanyService expressCompanyService) {
        Trade trade = req.getTrade();
        if (TradeUtils.isFxgBicTrade(trade, tradeConfig)
                || CommonConstants.PLAT_FORM_TYPE_SUMAITONG.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_LAZADA.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_TIKTOK.equals(trade.getSource())
                || CommonConstants.PLAT_FORM_TYPE_TEMU.equals(trade.getSource())
                || PLAT_FORM_TYPE_SHOPEE.equals(trade.getSource())
        ) {
            req.setValid(true);
            return;
        }
        if(PlatformUtils.is1688SmtNoConsignTrade(trade)) {
            req.setValid(true);
            return;
        }
        if (TradeUtils.isFxOrMixTrade(trade)) {
            expressTemplateMap = getGxTemplateInfo(trade, expressTemplateDubboService, staffService, fxUploadBusiness);
        }
        if (StringUtils.isBlank(trade.getOutSid()) && !isSupportNonOutSid(trade) && isNotSheinTrade(trade)) {
            //定时预发货如果出现没有单号的订单不需要报异常
            Integer enableStatus = SendType.AHEAD.equals(uploadData.sendType) ? 0 : 1;
            result.addConsignRecord(staff, trade, req.getType(), enableStatus, 1, "订单没有设置快递单号");
            return;
        }
        String tplKey = createTplKey(trade.getTemplateId(), trade.getTemplateType());
        UserExpressTemplate tpl = expressTemplateMap.get(tplKey);
        //isGx 不校验供销发货的流程
        if (tpl == null) {
            if(PlatformUtils.isQTGTrades(trade)) {
                req.setValid(true);
                return;
            }
            result.addConsignRecord(staff, trade, req.getType(), 1, 1, "订单快递模版找不到");
        } else {
            if (tpl.getExpressId() == null) {
                result.addConsignRecord(staff, trade, req.getType(), 1, 1, "订单快递模版找不到");
            }
            long expressId = tpl.getExpressId();
            Logs.debug(LogHelper.buildLog(staff, String.format("订单上传发货快递公司相关信息。sid=%s,tplKey=%s,expressId=%s,expressTemplateName=%s", trade.getSid(), tplKey, expressId, tpl.getName())));
            // KMERP-146076: 【交易】【平台任务】菜鸟裹裹上传发货时用真实的快递code发货
            // ExpressCompany ec = expressCompanyMap.get(expressId);
            ExpressCompany ec = expressCompanyService.getRealExpressCompany(staff, trade, tpl);
            req.setExpress(ec);
            req.setExpressTemplate(tpl);
            if (null == ec) {
                result.addConsignRecord(staff, trade, req.getType(), 1, 1, "订单的快递模板所属的快递公司找不到");
            } else {
                //京东订单发货需要京东快递公司ID
                if (CommonConstants.PLAT_FORM_TYPE_JD.equals(trade.getSource()) && (ec.getExpressCompanyJdId() == null || ec.getExpressCompanyJdId() <= 0)) {
                    result.addConsignRecord(staff, trade, req.getType(), 1, 1, String.format("[%s]没有对应的京东物流公司", ec.getCode()));
                } else {//订单校验通过
                    req.setValid(true);
                }
            }
        }
    }

    private static Map<String, UserExpressTemplate> getGxTemplateInfo(Trade trade, IExpressTemplateDubboService expressTemplateDubboService, IStaffService staffService, FxUploadBusiness fxUploadBusiness) {
        Staff gxStaff = staffService.queryFullByCompanyId(trade.getFinalDestCompanyId());
        return getCompanyTemplate(gxStaff, expressTemplateDubboService);
    }


    public static Map<String, UserExpressTemplate> getCompanyTemplate(Staff staff, IExpressTemplateDubboService expressTemplateDubboService) {
        Map<String, UserExpressTemplate> gxExpressTemplateMap = new HashMap<>();
        List<UserExpressTemplate> gxExpressTemplates = expressTemplateDubboService.getUserExpressWlbMixIncHidden(staff);
        for (UserExpressTemplate tpl : gxExpressTemplates) {
            gxExpressTemplateMap.put(UploadUtils.createTplKey(tpl.getId(), tpl.getIsWlb()), tpl);
        }
        return gxExpressTemplateMap;
    }


    public static String createTplKey(Long templateId, Integer templateType) {
        return templateId + "_" + templateType;
    }

    public static void fillGxExpress(Trade gxTrade, Trade fxTrade) {
        fxTrade.setTemplateType(gxTrade.getTemplateType());
        fxTrade.setTemplateId(gxTrade.getTemplateId());
        fxTrade.setTemplateName(gxTrade.getTemplateName());
        fxTrade.setExpressCompanyId(gxTrade.getExpressCompanyId());
        fxTrade.setOutSid(gxTrade.getOutSid());
        fxTrade.setWarehouseName(gxTrade.getWarehouseName());
        // 实际运费只需要取下一级的运费
        if(StringUtils.isNotBlank(fxTrade.getGxPostFee())){
            fxTrade.setActualPostFee(fxTrade.getGxPostFee());
        }else {
            fxTrade.setActualPostFee(gxTrade.getPostFee());
        }
        if(StringUtils.isBlank(fxTrade.getActualPostFee())){
            fxTrade.setActualPostFee("0");
        }
        fxTrade.setFinalDestCompanyId(gxTrade.getCompanyId());
        fxTrade.setLogisticsCompanyName(gxTrade.getLogisticsCompanyName());
        fxTrade.setLogisticsCompanyId(gxTrade.getLogisticsCompanyId());
    }

    public static void fillGxOrderInfo(List<Trade> fxTrades, List<Trade> gxTrades){
        Map<Long, Order> fxOrderId2GxOrderMap = TradeUtils.getOrders4Trade(gxTrades).stream().filter(t -> !Objects.isNull(t.getFxOrderId())).collect(Collectors.toMap(Order::getFxOrderId, Function.identity(), (k1, k2) -> k2));
        TradeUtils.getOrders4Trade(fxTrades).forEach(fxOrder ->{
            Order gxOrder = fxOrderId2GxOrderMap.get(fxOrder.getId());
            if (null != gxOrder){
                fxOrder.setIdentCode(gxOrder.getIdentCode());
                fxOrder.setUniqueCodes(gxOrder.getUniqueCodes());
            }
        });
    }


    public static ExpressData buildExpressData(Trade trade) {
        return trade == null ? null : new ExpressData(trade.getTemplateId(), trade.getTemplateType(), trade.getOutSid());
    }

    public static ExpressData buildExpressData(UploadRecord uploadRecord) {
        return uploadRecord == null ? null : new ExpressData(uploadRecord.getTemplateId(), uploadRecord.getTemplateType(), uploadRecord.getOutSid());
    }

    public static class ExpressData {
        Long templateId;
        Integer templateType;
        String outSid;

        public ExpressData(Long templateId, Integer templateType, String outSid) {
            this.templateId = templateId;
            this.templateType = templateType;
            this.outSid = outSid;
        }
    }


    public static List<Long> filterUploadByConfig(Staff staff, List<Trade> tradeList, TradeConfig tradeConfig) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return new ArrayList<>();
        }
        Integer checkType = tradeConfig.getInteger(TradeExtendConfigsEnum.UPLOAD_CHECK_ITEM_NUM.getKey());
        List<Long> sids = tradeList.stream().map(Trade::getSid).collect(Collectors.toList());
        if (null == checkType || 0 == checkType || 3 == checkType) {
            return sids;
        }
        List<Long> needFilter = tradeList.stream().filter(t -> (1 == checkType) ? (null != t.getItemNum() && t.getItemNum() > 1) : (null != t.getItemNum() && 1 == t.getItemNum())).map(Trade::getSid).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needFilter)) {
            sids.removeAll(needFilter);
            logger.info(LogHelper.buildLog(staff, "开启自动上传过滤单件或多件配置，过滤订单：" + JSONObject.toJSONString(needFilter)));
        }
        return sids;
    }


    /** 支持承诺时间进行预发货的平台 */
    public static final List<String> UPLOAD_AHEAD_BY_TIMEOUT_PLATS = Lists.newArrayList(
            PLAT_FORM_TYPE_TIAN_MAO
            ,PLAT_FORM_TYPE_TAO_BAO
            ,PLAT_FORM_TYPE_KUAI_SHOU
            ,PLAT_FORM_TYPE_PDD
            ,PLAT_FORM_TYPE_1688
            ,PLAT_FORM_TYPE_FXG
            ,PLAT_FORM_TYPE_POISON
            ,PLAT_FORM_TYPE_WXSPH
            ,PLAT_FORM_TYPE_XHS
    );
    /** 支持付款时间进行预发货的平台 */
    public static final List<String> UPLOAD_AHEAD_BY_PAY_TIME_PLATS = Lists.newArrayList(PLAT_FORM_TYPE_WXSPH
            );

    /**
     * 用户是否开启了提前发货上传
     */
    public static boolean isUploadAhead(User user) {
        if (user == null) {
            return false;
        }
        if (!UPLOAD_AHEAD_BY_TIMEOUT_PLATS.contains(user.getSource()) && !UPLOAD_AHEAD_BY_PAY_TIME_PLATS.contains(user.getSource())) {
            return false;
        }
        return Math.abs(getAheadSendGoodsHour(user)) > 0;
    }

    /**
     * 获取提前发货上传时间
     */
    public static Integer getAheadSendGoodsHour(User user) {
        if (user == null) {
            return 0;
        }
        UserConf userConf = user.getUserConf();
        if (userConf == null) {
            return 0;
        }
        Map<String, Object> confAttrInfo = userConf.getConfAttrInfo();
        if (confAttrInfo == null) {
            return 0;
        }
        Object value = confAttrInfo.get(UserExtraConfEnum.AHEAD_SEND_GOODS_HOUR.getKey());
        if (value == null) {
            return 0;
        }
        return Integer.parseInt(String.valueOf(value));
    }

    /**
     * 获取延长发货时间的设置，单位为小时
     * @param staff
     * @param trade
     * @return
     */
    public static int getDelaySendGoodsHour(Staff staff, Trade trade) {
        User user = staff.getUserByUserId(trade.getUserId());
        if (Objects.isNull(user)) {
            return 0;
        }
        UserConf userConf = user.getUserConf();
        if (Objects.isNull(userConf) || userConf.getDelaySendGoodsHour() == null || (userConf.getPddUploadStrategy() != null && userConf.getPddUploadStrategy() == 1)) {
            return 0;
        }

        return userConf.getDelaySendGoodsHour().intValue();
    }


    /** 允许无需物流发货的平台 */
    public static final List<String> ALLOW_SPLIT_TRADES_DUMMY_UPLOAD_PLATS = Lists.newArrayList(
            PLAT_FORM_TYPE_1688 // KMERP-110396: 1688支持子单无需物流发货
            ,PLAT_FORM_TYPE_TEMU
    );

    public static boolean isAllowSplitTradeDummyUploadPlat(String platform) {
        return ALLOW_SPLIT_TRADES_DUMMY_UPLOAD_PLATS.contains(platform);
    }

    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    public static boolean isAllowSplitTradeDummyUploadPlat(Trade trade) {
        return isAllowSplitTradeDummyUploadPlat(trade.getSource()) || isAllowSplitTradeDummyUploadPlat(trade.getSplitSource())|| isAllowSplitTradeDummyUploadPlat(trade.getSubSource());
    }

    public static void validateResendOutSid(Staff staff, ConsignRequest req, UploadData uploadData, UploadResult result) {
        Trade trade = req.getTrade();
        // KMERP-143111: 快团团订单拆单上传优化
        if (SendType.RESEND.equals(req.getType()) && isKttSupplyTrade(trade)) {
            String beforeOutSid = Optional.ofNullable(uploadData.getSuccessExistUploadRecord(trade)).map(UploadRecord::getOutSid).orElse(null);
            if (StringUtils.isBlank(beforeOutSid)) {
                result.addConsignRecord(staff, trade, req.getType(), 1, 1, "重新发货失败，未找到之前发货的运单号");
                req.setValid(false);
                return;
            }

            Map<String, Object> extParams = Optional.ofNullable(req.getExtendParams()).map(map -> new HashMap<String, Object>(map)).orElseGet(HashMap::new);
            extParams.put("beforeOutSid", beforeOutSid);
            req.setExtendParams(extParams);
            req.setValid(true);
        }
    }

    public static boolean isKttSupplyTrade(Trade trade) {
        return Objects.nonNull(trade)
                && (CommonConstants.PLAT_FORM_TYPE_KTT.equals(trade.getSource()) || CommonConstants.PLAT_FORM_TYPE_KTT.equals(trade.getSplitSource()))
                && CommonConstants.PLAT_FORM_TYPE_KTT_SUPPLY.equals(trade.getSubSource());
    }

    private static boolean isNotSheinTrade(Trade trade) {
        return !(CommonConstants.PLAT_FORM_TYPE_SHEIN.equals(trade.getSource()) || CommonConstants.PLAT_FORM_TYPE_SHEIN.equals(trade.getSplitSource()));
    }


    public static String getPlatSource(Trade trade) {
        return PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && StringUtils.isNotBlank(trade.getSplitSource()) ? trade.getSplitSource() : trade.getSource();
    }

    public String getUploadWay(Staff staff){
        TradeConfigNew deviceSnConfig = TradeConfigGetUtil.get(staff, TradeConfigEnum.AUTO_GIFT_MATCH);
        return deviceSnConfig.getConfigValue();
    }

    public static List<Order> fillTradePlatOrders(Trade trade, List<Order> orders) {
        return fillTradePlatOrders(TradeUtils.getOrders4Trade(trade), orders);
    }

    public static List<Order> fillTradePlatOrders(List<Order> tradeOrders, List<Order> platOrders) {
        if (CollectionUtils.isEmpty(tradeOrders)) {
            return platOrders;
        }

        List<Order> result = Optional.ofNullable(platOrders).orElse(Lists.newArrayList());
        Set<Long> platOids = result.stream().map(OrderBase::getOid).collect(Collectors.toSet());
        for (Order order : tradeOrders) {
            if (platOids.contains(order.getOid())) {
                continue;
            }
            if (!CommonConstants.PLAT_FORM_TYPE_SYS.equals(order.getSource())) {
                platOids.add(order.getOid());
                result.add(order);
            }
        }
        return result;
    }

    /**
     * 上传校验供销单状态,返回错误信息
     *
     * @param staff
     * @param uploadData
     * @param trades
     * @return
     */
    public static Map<Long, String> checkFxBeforeUpload(Staff staff, UploadData uploadData, List<Trade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return Maps.newHashMap();
        }
        Map<Long, String> errorMap = Maps.newHashMapWithExpectedSize(trades.size());
        for (Trade trade : trades) {
            if (!TradeUtils.isGxOrMixTrade(trade)) {
                continue;
            }
            if (SendType.MANUAL_UPLOAD == uploadData.sendType && !trade.isForce()
                    && TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.FX_UNAUDIT)) {
                Logs.debug(LogHelper.buildLog(staff, String.format("订单上传校验失败，供销订单有分销商反审核异常,sid=%s", trade.getSid())));
                errorMap.putIfAbsent(trade.getSid(), "供销订单有分销商反审核异常，不支持该操作");
            }
        }
        return errorMap;
    }
}

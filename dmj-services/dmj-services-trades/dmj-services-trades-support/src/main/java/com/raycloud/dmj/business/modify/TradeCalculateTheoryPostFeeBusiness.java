package com.raycloud.dmj.business.modify;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.raycloud.dmj.*;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.fx.QiMenFxCashFlowUtils;
import com.raycloud.dmj.business.fx.SimulateImportGxTradeBusiness;
import com.raycloud.dmj.dms.domain.dto.*;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.TradeConstants;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.pt.FreightCost;
import com.raycloud.dmj.domain.pt.UserExpressTemplate;
import com.raycloud.dmj.domain.pt.wlb.UserLogisticsCompany;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.fx.SimulateImportGxTradeData;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.*;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.trades.utils.PaymentUtils;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.domain.wms.LogisticsOrder;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyBusiness;
import com.raycloud.dmj.express.api.IUserLogisticsCompanyTemplateService;
import com.raycloud.dmj.index.request.CheckHasFeatureRequest;
import com.raycloud.dmj.index.response.CheckHasFeatureResponse;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import com.raycloud.dmj.services.feature.FeatureService;
import com.raycloud.dmj.services.pt.*;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.wave.IWaveServiceDubbo;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.LogHelper;
import com.raycloud.dmj.web.model.wms.LogisticsOrderQueryParams;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class TradeCalculateTheoryPostFeeBusiness {

    @Resource(name = "solrTradeSearchService")
    ITradeSearchService tradeSearchService;
    @Resource
    IFreightTemplateService freightTemplateService;
    @Resource
    IFreightExpressService freightExpressService;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    IDmsTradeService dmsTradeService;
    @Resource
    SimulateImportGxTradeBusiness simulateImportGxTradeBusiness;
    @Resource
    IStaffService staffService;
    @Resource
    IIndexDubboService indexDubboService;

    @Autowired(required = false)
    IWaveServiceDubbo waveServiceDubbo;

    @Resource
    IShopService shopService;


    /**
     * key: userId
     * value: userSource
     */
    private static final Cache<Long, String> SHOP_CACHE = CacheBuilder.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    @Resource
    FeatureService featureService;

    @Resource
    IUserLogisticsCompanyTemplateService userLogisticsCompanyTemplateService;

    @Resource
    IUserLogisticsCompanyBusiness userLogisticsCompanyBusiness;



    private final Logger logger = Logger.getLogger(TradeCalculateTheoryPostFeeBusiness.class);

    /**
     * 订单同步，或者订单发货时，重新计算理论运费
     *
     * @param staff
     * @param trades
     * @param businessNode 详见BusinessNodeEnum
     */
    public void calculateTheoryFreight(Staff staff, List<Trade> trades, BusinessNodeEnum businessNode) {
        calculateTheoryFreight(staff,trades,businessNode,false);
    }

    /**
     * 订单同步，或者订单发货时，重新计算理论运费
     *
     * @param staff
     * @param trades
     * @param businessNode 详见BusinessNodeEnum
     */
    public void calculateTheoryFreight(Staff staff, List<Trade> trades, BusinessNodeEnum businessNode, boolean forceCoverActual) {
        if (CollectionUtils.isNotEmpty(trades)) {
            List<String> logList = new ArrayList<>();
            Integer isCoverWeight = (Integer) tradeConfigService.get(staff).get("isCoverWeight");
            List<Trade> targets = new ArrayList<>();
            PostFeeLogBuilder logBuilder = new PostFeeLogBuilder(staff).appendNode(businessNode).append("理论运费计算:").startWatch();
            for (Trade trade : trades) {
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                    //合单情况下 子订单不需要计算运费 并且需要置零
                    if (!Objects.equals(trade.getMergeSid(), trade.getSid())) {
                        trade.setTheoryPostFee(0.0d);
                        this.dealWithTheoryCoverActual(trade, isCoverWeight, logList);
                        logBuilder.group("子单置零", trade.getSid());
                    } else {
                        targets.add(trade);
                    }
                } else {
                    targets.add(trade);
                }
            }
            logBuilder.printInfo(logger);

            if (CollectionUtils.isEmpty(targets)) {
                return;
            }

            Trade frist = targets.get(0);
            new DevLogBuilder(staff, LogBusinessEnum.POST_FEE.getSign()).append("计算环节", businessNode == null ? "null" : businessNode.getNodeName()).append("首个订单信息").appendTrade(frist)
                    .append("templateId", frist.getTemplateId()).append("templateType", frist.getTemplateType()).append("logisticsCompanyId", frist.getLogisticsCompanyId())
                    .append("weight", frist.getWeight()).append("netWeight", frist.getNetWeight()).append("volume", frist.getVolume()).printInfo(logger);

            //订单发货节点 兜底去查一下新包材的重量体积修正
            /*暂时注释不修正测试过程中发现订单发货时 订单的重量修正有问题 有些订单上下文拿不到子单 无法计算净重
            if (BusinessNodeEnum.TRADE_SEND_GOODS.equals(businessNode)) {
                fixByNewPackma(staff, businessNode, targets);
            }*/

            List<Trade> filterTrades = fillOrigin(staff, targets, businessNode);
            boolean useLogisticsCompany = userLogisticsCompany(staff);
            //logger.info(new PostFeeLogBuilder(staff).appendNode(businessNode).format("按快递公司：[%s],订单总数：[%s],重新计算理论运费列表:%s", useLogisticsCompany, trades.size(), TradeUtils.toSidList(filterTrades)).build());
            List<FreightCost> freightCostList = useLogisticsCompany ? freightExpressService.getFreightCostList(staff, filterTrades) : freightTemplateService.getFreightCostList(staff, filterTrades, businessNode);
            calculateAfter(staff, filterTrades, freightCostList, businessNode, useLogisticsCompany, forceCoverActual);
        }
    }

    private void fixByNewPackma(Staff staff, BusinessNodeEnum businessNode, List<Trade> targets) {
        if (waveServiceDubbo != null) {
            PostFeeLogBuilder builder = new PostFeeLogBuilder(staff).appendNode(businessNode).append("兜底查询新包材重量体积").append("size", targets.size()).startTimer();
            try {
                LogisticsOrderQueryParams params = new LogisticsOrderQueryParams();
                params.setSidList(TradeUtils.toSidList(targets));
                List<LogisticsOrder> logisticsOrders = waveServiceDubbo.queryLogisticsOrderAndDetail(staff, params);
                if (CollectionUtils.isNotEmpty(logisticsOrders)) {

                    Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(targets);
                    for (LogisticsOrder logisticsOrder : logisticsOrders) {
                        Trade trade = tradeMap.get(logisticsOrder.getRelatedBillId());
                        if (trade == null) {
                            continue;
                        }
                        Double orgiNetWeight = trade.getNetWeight();
                        double tradeNetWeight = TradeUtils.calculateTradeNetWeight(trade);
                        tradeNetWeight = MathUtils.add(tradeNetWeight,logisticsOrder.getTotalPackageWeight());
                        if (!Objects.equals(orgiNetWeight,tradeNetWeight)) {
                            builder.group(trade.getSid(),"w:"+orgiNetWeight + "->"+tradeNetWeight );
                        }
                        trade.setNetWeight(tradeNetWeight);

                        Double packageVolume = logisticsOrder.getTotalPackageVolume();
                        if (!MathUtils.equalsZero(packageVolume) && !Objects.equals(trade.getVolume(),packageVolume)) {
                            builder.group(trade.getSid(),"v:"+trade.getVolume() + "->"+packageVolume );
                            trade.setVolume(packageVolume);
                        }
                    }
                }
                builder.startWatch().appendTook(DevLogBuilder.isDevEnv()?100L:2000L).printDebug(logger);
            }catch (Throwable e){
                builder.appendError("处理异常",e).printError(logger,e);
            }
        }else {
            new PostFeeLogBuilder(staff).appendNode(businessNode).append("订单发货 未找到waveServiceDubbo实例 不兜底查询新包材重量体积");
        }
    }

    /**
     * 是否按快递公司维度参与订单业务
     */
    public boolean userLogisticsCompany(Staff staff) {
        CheckHasFeatureRequest checkHasFeatureRequest = new CheckHasFeatureRequest();
        checkHasFeatureRequest.setCompanyId(staff.getCompanyId());
        checkHasFeatureRequest.setCode("printTemplateIntegrate");
        CheckHasFeatureResponse checkHasFeatureResponse = indexDubboService.checkHasFeature(checkHasFeatureRequest);
        return checkHasFeatureResponse != null && checkHasFeatureResponse.isHasFeature();
    }

    private void calculateAfter(Staff staff, List<Trade> trades, List<FreightCost> freightCosts, BusinessNodeEnum businessNode,boolean useLogisticsCompany,boolean forceCoverActual) {
        if (freightCosts != null && freightCosts.size() > 0) {
            // 如果是发货节点，需要根据是否符合条件将理论运费覆盖实际运费
            Integer isCoverWeight = 0;

            if (forceCoverActual) {
                isCoverWeight = YesNoEnum.YES.getValue();
            }else {
                if (BusinessNodeEnum.TRADE_SEND_GOODS.equals(businessNode)) {
                    Integer CoverWeight = (Integer) tradeConfigService.get(staff).get("isCoverWeight");
                    if (logger.isDebugEnabled()) {
                        logger.debug(LogHelper.buildLogHead(staff).append(String.format("是否理论运费覆盖实际运费条件为isCoverWeight=%s forceCoverActual=%s", CoverWeight,forceCoverActual)));
                    }
                    isCoverWeight = CoverWeight;
                }
            }

            Map<Long, Trade> tradeMap = TradeUtils.toMapBySid(trades);
            Map<Long, List<FreightCost>> freightCostMap = new HashMap<>();

            Map<String,List<Long>> errMap = new HashMap<>();

            Integer finalIsCoverWeight = isCoverWeight;
            List<String> logList = new ArrayList<>();
            freightCosts.forEach(cost -> {
                if (Objects.nonNull(cost)){
                    Trade trade = tradeMap.get(cost.getSid());
                    // 已经发货的订单，不重新计算理论运费，此处加一个判断，发货的时候由于传过来的trade系统状态已经设置为发货，因此此处略过
                    if (trade != null && !(TradeUtils.isAfterSendGoods(trade) && !BusinessNodeEnum.TRADE_SEND_GOODS.equals(businessNode))) {
                        if (cost.isError()) {
                            if (logger.isDebugEnabled()) {
                                String reason = StringUtils.isNotBlank(cost.getReason())?cost.getReason():"unknown";
                                errMap.computeIfAbsent(reason,x->new ArrayList<>()).add(cost.getSid());
                            }
                            trade.setTheoryPostFee(0.00D);
                        } else if (!cost.isError()) {
                            // 得到新的理论运费
                            trade.setTheoryPostFee(cost.getCount());
                            dealWithTheoryCoverActual(trade, finalIsCoverWeight, logList);
                            freightCostMap.computeIfAbsent(trade.getTemplateId(), k -> new ArrayList<>()).add(cost);
                        }
                    }
                    tradeMap.remove(cost.getSid());
                }
            });
            if (tradeMap.size() > 0) {
                errMap.put("未返回",new ArrayList<>(tradeMap.keySet()));
            }
            if (!errMap.isEmpty() && logger.isDebugEnabled()) {
                PostFeeLogBuilder errLogBuilder = new PostFeeLogBuilder(staff).appendNode(businessNode).append(useLogisticsCompany ? "按快递公司":"按快递模板")
                        .append("重新计算理论运费失败:").startArray();
                for (Map.Entry<String, List<Long>> entry : errMap.entrySet()) {
                    errLogBuilder.startObject().append(entry.getKey(),entry.getValue()).endObject();
                }
                errLogBuilder.endArray().multiPrint(logger, Priority.DEBUG);
            }

            if (logger.isDebugEnabled()) {
                // 记录计算理论运费日志
                saveCalculateLog(staff, freightCostMap, businessNode,useLogisticsCompany);
                Lists.partition(logList, 20).forEach(logs -> {
                    logger.debug(new StringBuilder(LogHelper.buildLogHead(staff)).append(Strings.join("。", logs)));
                });
            }
        }else {
            if (logger.isDebugEnabled()) {
               new PostFeeLogBuilder(staff).appendNode(businessNode).append(useLogisticsCompany ? "按快递公司":"按快递模板")
                        .append("重新计算理论运费失败:freightCosts 为空").printDebug(logger);
            }
        }

    }

    private List<Trade> fillOrigin(Staff staff, List<Trade> trades, BusinessNodeEnum businessNode) {
        List<Trade> filterTrade = new ArrayList<>();
        if (trades == null || trades.size() == 0) {
            return filterTrade;
        }
        Map<Long, Trade> noOriginTradeMap = new HashMap<>();
        for (Trade trade : trades) {
            if (needRepair(trade, businessNode)) {
                Trade originTrade = trade.getOrigin();
                if (trade.getOrigin() == null) {
                    noOriginTradeMap.put(trade.getSid(), trade);
                } else {
                    //用originTrade 回填
                    fillOrigin(staff,trade, originTrade);
                    if (trade.getTemplateId() != null && trade.getTemplateId() > 0) {
                        filterTrade.add(trade);
                    }
                }
            } else {
                filterTrade.add(trade);
            }
        }

        if (noOriginTradeMap.size() > 0) {
            //用查询的订单 回填
            List<Trade> dbTrades = tradeSearchService.queryBySids(staff, false, noOriginTradeMap.keySet().toArray(new Long[0]));
            if (dbTrades != null && dbTrades.size() > 0) {
                dbTrades.forEach(dbTrade -> {
                    Trade noOriginTrade = noOriginTradeMap.get(dbTrade.getSid());
                    fillOrigin(staff,noOriginTrade, dbTrade);
                    if (noOriginTrade.getTemplateId() != null && noOriginTrade.getTemplateId() > 0) {
                        filterTrade.add(noOriginTrade);
                    }
                });
            }
        }
        return filterTrade;
    }

    private void fillOrigin(Staff staff,Trade trade, Trade dbTrade) {
        // 此处由于传参可能没有设置重新计算理论运费的参数，故直接查询数据库取出记录设置参数
        //待付款变为待审核
        if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getOldSysStatus()) && Trade.SYS_STATUS_WAIT_AUDIT.equals(trade.getSysStatus())) {
            return;
        }
        if (trade.getWarehouseId() == null) {
            trade.setWarehouseId(dbTrade.getWarehouseId());
        }
        if (trade.getTemplateId() == null) {
            trade.setTemplateId(dbTrade.getTemplateId());
        }
        if (trade.getTemplateType() == null) {
            trade.setTemplateType(dbTrade.getTemplateType());
        }
        if (trade.getVolume() == null) {
            trade.setVolume(dbTrade.getVolume());
        }
        if (trade.getReceiverState() == null) {
            trade.setReceiverState(dbTrade.getReceiverState());
        }
        if (trade.getReceiverCity() == null) {
            trade.setReceiverCity(dbTrade.getReceiverCity());
        }
        if (trade.getLogisticsCompanyId() == null) {
            Logs.debug(LogHelper.buildLog(staff,String.format("快递公司有误填充日志 查问题 sid:%s LogisticsCompanyId=%s",dbTrade.getSid(),dbTrade.getLogisticsCompanyId())));
            trade.setLogisticsCompanyId(dbTrade.getLogisticsCompanyId());
        }
    }

    private boolean needRepair(Trade trade, BusinessNodeEnum businessNode) {
        // 已经发货的订单，不重新计算理论运费，此处加一个判断，发货的时候由于传过来的trade系统状态已经设置为发货，因此此处略过
        if (TradeUtils.isAfterSendGoods(trade) && !BusinessNodeEnum.TRADE_SEND_GOODS.equals(businessNode)) {
            return false;
        }
        return trade.getWarehouseId() == null
                || trade.getTemplateId() == null
                || trade.getTemplateType() == null
                || trade.getVolume() == null
                || trade.getReceiverState() == null
                || trade.getReceiverCity() == null;
    }


    private String getTemplateKey(Trade trade) {
        return trade.getTemplateId() + "_" + trade.getTemplateType();
    }

    /**
     * 处理理论运费是否覆盖实际运费的逻辑
     *
     * @param trade
     */
    private void dealWithTheoryCoverActual(Trade trade, Integer isCoverWeight, List<String> logList) {
        // 开启净重覆盖包裹重量，未称重，情况下才将理论运费覆盖实际运费
        boolean coverFlag = YesNoEnum.YES.getValue().equals(isCoverWeight) && YesNoEnum.NO.getValue().equals(trade.getIsWeigh());
        if (coverFlag) {
            if (logger.isDebugEnabled()) {
                logList.add(String.format("订单%s理论运费【%s】覆盖实际运费【%s】", trade.getSid(), trade.getTheoryPostFee(), trade.getActualPostFee()));
            }
            trade.setActualPostFee(String.valueOf(trade.getTheoryPostFee()));
        }
    }


    /**
     *  根据trade分成按运费匹配和最低运费匹配
     *
     * @param staff
     * @param trades
     * @param postFeeUseActualWeight
     */
    public void recalGxPostFee4Sync(Staff staff, List<Trade> trades, Boolean postFeeUseActualWeight) {
        // 到这里已经按分销商分好组
        boolean ifDdmsMatchLowPriceFreight = false;
        // 非(继承运单号 或者 指定模板) && 订单同步新增流程 && 供销或供销且分销  --> 才按最低运费匹配
        List<Trade> lowPriceFreightGxTrades = trades.stream().filter(t-> !(t.hasOpV(OpVEnum.TRADE_GX_EXTEND_OUTSID) || TradeUtils.isContainV(t, TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID)) && t.hasOpV(OpVEnum.TRADE_IMPORT_INSERT_CHAIN) && TradeUtils.isGxOrMixTrade(t))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(lowPriceFreightGxTrades) && lowPriceFreightGxTrades.stream().anyMatch(t->t.hasOpV(OpVEnum.TRADE_FX_MATCH_LOW_PRICE_FREIGHT))){
            ifDdmsMatchLowPriceFreight = true;
            doRecalGxPostFee(staff, lowPriceFreightGxTrades, postFeeUseActualWeight, ifDdmsMatchLowPriceFreight);
        }
        List<Trade> restTrades = trades;
        if(ifDdmsMatchLowPriceFreight){
            restTrades =  trades.stream().filter(t->!lowPriceFreightGxTrades.contains(t)).collect(Collectors.toList());
        }

        if(CollectionUtils.isNotEmpty(restTrades)){
            doRecalGxPostFee(staff, restTrades, postFeeUseActualWeight, false);
        }

    }
    /**
     *  根据trade分成按运费匹配和最低运费匹配
     *
     * @param staff
     * @param trades
     * @param postFeeUseActualWeight
     */
    public void recalGxPostFee(Staff staff, List<Trade> trades, Boolean postFeeUseActualWeight) {
        List<Trade> restTrades = trades;
        if(trades.stream().anyMatch(TradeUtils::isGxOrMixTrade)){
            boolean ifDmsFxAppointTemplateCalcPostFee = TradeConfigGetUtil.get(staff, TradeConfigEnum.DMS_FX_APPOINT_TEMPLATE_CALC_POST_FEE).isOpen();
            if(ifDmsFxAppointTemplateCalcPostFee){
                List<Trade> notCalcTrades = new ArrayList<>();
                trades.forEach(t -> {
                    boolean containsTemplateId = TradeUtils.isContainV(t, TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID) ||
                            TradeUtils.isContainV(t.getSourceTrade(), TradeConstants.V_IF_FX_APPOINT_TEMPLATE_ID);
                    boolean hasRefreshCashFlow = t.hasOpV(OpVEnum.TRADE_GX_REFRESH_CASHFLOW);
                    if (TradeUtils.isGxOrMixTrade(t) && containsTemplateId) {
                        if (!hasRefreshCashFlow) {
                            notCalcTrades.add(t);
                        } else {
                            t.addOpV(OpVEnum.TRADE_FX_APPOINT_TEMPLATE_CALC_POST_FEE);
                        }
                    }
                });
                if(CollectionUtils.isNotEmpty(notCalcTrades)){
                    new FxLogBuilder(staff,FxLogBuilder.ROLE_GX).append("计算分销运费，开启了【用分销商指定的快递公司计算的运费进行结算】配置跳过运费计算 ").appendTrade(notCalcTrades).printDebug(logger);
                }
                restTrades =  trades.stream().filter(t->!notCalcTrades.contains(t)).collect(Collectors.toList());
            }
        }
        doRecalGxPostFee(staff,restTrades,postFeeUseActualWeight,false);
    }

    public void doRecalGxPostFee(Staff staff, List<Trade> trades, Boolean postFeeUseActualWeight,boolean ifDdmsMatchLowPriceFreight) {
        StringBuilder logHead = new FxLogBuilder(staff,FxLogBuilder.ROLE_GX).append("计算分销运费 ").appendIf(()->ifDdmsMatchLowPriceFreight,",分销订单自动匹配供销商低价快递").getBuilder();
        if (CollectionUtils.isEmpty(trades)) {
            if (logger.isDebugEnabled()) {
                logger.debug(new StringBuilder(logHead).append("没有需要重新计算运费的订单"));
            }
            return;
        }
        List<Trade> gxTradeList = new ArrayList<>();
        Map<Long, Trade> gxTradeMap = new HashMap<>();
        Map<Long, List<Trade>> mergeTradeMap = new HashMap<>();
        for (Trade trade : trades) {
            if (TradeUtils.needCalcGxPostFee(trade) || TradeUtils.isQimenFxSource(trade) || TradeUtils.isAlibabaFxTrade(trade)) {
                gxTradeList.add(trade);
                gxTradeMap.put(trade.getSid(), trade);
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)){
                    mergeTradeMap.computeIfAbsent(trade.getMergeSid(), t -> new ArrayList<>()).add(trade);
                }
            }
        }

        if (gxTradeList.size() > 0) {
            List<String> logList = Lists.newArrayListWithExpectedSize(gxTradeList.size());
            List<DmsFreightMatchDto> matchDtoList = new ArrayList<>();
            fillUserSource(staff,gxTradeList);
            for (Trade trade : gxTradeList) {
                //合单子单，运费直接设置为0
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) && !Objects.equals(trade.getSid(), trade.getMergeSid())){
                    trade.setPostFee("0.00");
                    continue;
                }
                DmsFreightMatchDto dto = convertTradeToDto(trade, postFeeUseActualWeight, mergeTradeMap);
                if (Objects.nonNull(dto)){
                    matchDtoList.add(dto);
                }
            }
            // 是否走模版重构新规则
            boolean ifNewTemplateRule = featureService.checkHasFeature(staff.getCompanyId(), Feature.PRINT_TEMPLATE_INTEGRATE);

            List<List<DmsFreightMatchDto>> partition = Lists.partition(matchDtoList, 20);
            for (List<DmsFreightMatchDto> dmsFreightMatchDtos : partition) {
                Set<Long> sidSet = dmsFreightMatchDtos.stream().map(DmsFreightMatchDto::getSid).collect(Collectors.toSet());
                List<DmsFreightCostDto> dmsFreightCostDtoList = Lists.newArrayList();
                if(ifNewTemplateRule && ifDdmsMatchLowPriceFreight){
                    dmsFreightCostDtoList = matchLowPriceFreight(staff,trades,dmsFreightMatchDtos);
                }else {
                    dmsFreightCostDtoList = dmsTradeService.matchFreight(dmsFreightMatchDtos,ifDdmsMatchLowPriceFreight);
                }
                if (dmsFreightCostDtoList != null && dmsFreightCostDtoList.size() > 0) {
                    if (logger.isDebugEnabled()) {
                        logger.debug(new StringBuilder(logHead).append("计算理论运费结果：" + JSON.toJSONString(dmsFreightCostDtoList)));
                    }
                    for (DmsFreightCostDto dmsFreightCostDto : dmsFreightCostDtoList) {
                        if (dmsFreightCostDto.isSuccess() && dmsFreightCostDto.getCost() != null) {
                            Trade trade = gxTradeMap.get(dmsFreightCostDto.getSid());
                            if (trade != null) {
                                trade.setPostFee(dmsFreightCostDto.getCost().toString());
                                trade.setGxFreightRuleName(dmsFreightCostDto.getFreightRuleName());
                                // 最低运费匹配 会忽略dmsTradeService.matchFreight() 参数里面的快递公司/快递模板反一个最低的
                                if(ifDdmsMatchLowPriceFreight){
                                    if(dmsFreightCostDto.getTemplateId()!=null && dmsFreightCostDto.getTemplateId()>0L){
                                        trade.setTemplateId(dmsFreightCostDto.getTemplateId());
                                        trade.setTemplateName(dmsFreightCostDto.getTemplateName());
                                        trade.setTemplateType(dmsFreightCostDto.getTemplateType());
                                    }
                                    if(dmsFreightCostDto.getLogisticsCompanyId()!=null && dmsFreightCostDto.getLogisticsCompanyId()>0L){
                                        trade.setLogisticsCompanyId(dmsFreightCostDto.getLogisticsCompanyId());
                                        trade.setLogisticsCompanyName(dmsFreightCostDto.getLogisticsCompanyName());
                                    }
                                }
                                if (TradeUtils.isQimenFxSource(trade)) {
                                    //奇门分销重算订单应付
                                    trade.setPayment(new BigDecimalWrapper(PaymentUtils.calculateQimenFxAmount(trade,false,false)).getString());
                                }
                                if (logger.isDebugEnabled()) {
                                    logList.add(String.format("供销订单:%s,理论重量覆盖实际重量时，重新计算运费为postFee=%s,覆盖的实际运费为actualPostFee=%s%s", trade.getSid(), trade.getPostFee(), trade.getActualPostFee(),TradeUtils.isGxOrMixTrade(trade) && trade.hasOpV(OpVEnum.TRADE_FX_APPOINT_TEMPLATE_CALC_POST_FEE)?",用分销商指定的快递公司计算的运费进行结算":""));
                                }
                            }
                        }
                    }
                } else {
                    logger.debug(new StringBuilder(logHead).append("匹配运费模版为空!：" + sidSet));
                }
            }
            if (logger.isDebugEnabled()) {
                Lists.partition(logList, 10).forEach(logs -> {
                    logger.debug(new StringBuilder(logHead).append(logs.stream().collect(Collectors.joining("  "))));
                });
            }
        }
    }

    /**
     *  1. dmsTradeService.matchGroupFreight 返回的结果 没有templateId
     *  2. userLogisticsCompanyTemplateService.matchOldTemplateMap 找到 logisticsCompanyId --> sid --> templateId的对应关系
     *  3. 返回DmsFreightCostDto 集合
     *
     * @param staff
     * @param trades
     * @param dmsFreightMatchDtos
     * @return
     */
    private List<DmsFreightCostDto> matchLowPriceFreight(Staff staff, List<Trade> trades, List<DmsFreightMatchDto> dmsFreightMatchDtos) {

        List<DmsFreightCostDto> freightCostDtos = Lists.newArrayList();
        List<DmsOrderFreightDto> orderFreightDtos = dmsTradeService.matchGroupFreight(dmsFreightMatchDtos);

        if(CollectionUtils.isEmpty(orderFreightDtos)){
            return freightCostDtos;
        }

        Set<Long> logisticsCompanyIds = Sets.newHashSet();
        orderFreightDtos.stream().filter(o->o.isSuccess() && CollectionUtils.isNotEmpty(o.getList())).forEach(list->{
            logisticsCompanyIds.addAll(list.getList().stream().filter(o->Objects.nonNull(o.getLogisticsCompanyId())).map(DmsOrderFreightDto.DmsMatchFreightRsp::getLogisticsCompanyId).collect(Collectors.toSet()));
        });
        Set<Long> sids = orderFreightDtos.stream().filter(DmsOrderFreightDto::isSuccess).map(DmsOrderFreightDto::getSid).collect(Collectors.toSet());
        List<UserLogisticsCompany> logisticsCompanyList = userLogisticsCompanyBusiness.queryByIds(staff, Lists.newArrayList(logisticsCompanyIds), true);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(logisticsCompanyList)) {
            return freightCostDtos;
        }

        try {
            //  如果 gxStaff为空， 订单的仓库有不在logisticsCompanyList 对应的仓库会报错--> 说明前面的 dmsTradeService.matchGroupFreight 最大运费匹配没有根据仓库去过滤对应的运费模板
            //  Map<Long, Map<Long,UserExpressTemplate>> matchOldTemplateMap(Staff staff, List<Trade> tradeList, List<UserLogisticsCompany> userLogisticsCompanyList, Staff gxStaff);
            Map<Long, Map<Long, UserExpressTemplate>>  logisticsCompanyIdAndSidAndTemplateMap = userLogisticsCompanyTemplateService.matchOldTemplateMap(staff,trades.stream().filter(t-> sids.contains(t.getSid())).collect(Collectors.toList()),logisticsCompanyList,staff);

            orderFreightDtos.stream().filter(o->o.isSuccess() && CollectionUtils.isNotEmpty(o.getList())).forEach(o->{
                // 已经按cost从小到大排序好
                for (DmsOrderFreightDto.DmsMatchFreightRsp dmsMatchFreightRsp : o.getList()) {
                    Map<Long, UserExpressTemplate> longUserExpressTemplateMap = logisticsCompanyIdAndSidAndTemplateMap.get(dmsMatchFreightRsp.getLogisticsCompanyId());
                    if(longUserExpressTemplateMap != null && longUserExpressTemplateMap.get(o.getSid())!=null){
                        DmsFreightCostDto costDto = new DmsFreightCostDto();
                        costDto.setCost(dmsMatchFreightRsp.getCost());
                        costDto.setSid(o.getSid());
                        costDto.setSuccess(true);
                        costDto.setExpressId(dmsMatchFreightRsp.getExpressId());
                        costDto.setFreightRuleName(dmsMatchFreightRsp.getFreightRuleName());
                        costDto.setFreightRuleId(dmsMatchFreightRsp.getFreightRuleId());
                        costDto.setLogisticsCompanyId(dmsMatchFreightRsp.getLogisticsCompanyId());
                        costDto.setLogisticsCompanyName(dmsMatchFreightRsp.getLogisticsCompanyName());

                        UserExpressTemplate userExpressTemplate = longUserExpressTemplateMap.get(o.getSid());
                        costDto.setTemplateId(userExpressTemplate.getId());
                        costDto.setTemplateType(userExpressTemplate.getTemplateType()==null?0:userExpressTemplate.getTemplateType());
                        costDto.setTemplateName(userExpressTemplate.getName());
                        costDto.setExpressId(userExpressTemplate.getExpressId());
                        freightCostDtos.add(costDto);
                        break;
                    }
                }
            });
        }catch (Exception e){
            new FxLogBuilder(staff,FxLogBuilder.ROLE_GX).append("matchOldTemplateMap error").printWarn(logger,e);
            return freightCostDtos;
        }

        return freightCostDtos;
    }

    private DmsFreightMatchDto convertTradeToDto(Trade trade, Boolean postFeeUseActualWeight, Map<Long, List<Trade>> mergeTradeMap) {
        DmsFreightMatchDto dmsFreightMatchDto = new DmsFreightMatchDto();
        dmsFreightMatchDto.setCompanyId(trade.getCompanyId());
        dmsFreightMatchDto.setDistributorId(trade.getSourceId());
        dmsFreightMatchDto.setReceiverAddress(trade.getReceiverAddress());
        dmsFreightMatchDto.setReceiverCity(trade.getReceiverCity());
        dmsFreightMatchDto.setReceiverDistrict(trade.getReceiverDistrict());
        dmsFreightMatchDto.setReceiverState(trade.getReceiverState());
        dmsFreightMatchDto.setSid(trade.getSid());
        dmsFreightMatchDto.setSourcePlatform(trade.getUserSource());
        dmsFreightMatchDto.setTemplateId(trade.getTemplateId());
        dmsFreightMatchDto.setTemplateName(trade.getTemplateName());
        dmsFreightMatchDto.setTemplateType(trade.getTemplateType());
        dmsFreightMatchDto.setLogisticsCompanyId(trade.getLogisticsCompanyId());
        dmsFreightMatchDto.setExpressName(trade.getLogisticsCompanyName());
        // 计算快递运费时，用分销商指定的快递公司计算的运费进行结算
        if(TradeUtils.isGxOrMixTrade(trade) && trade.hasOpV(OpVEnum.TRADE_FX_APPOINT_TEMPLATE_CALC_POST_FEE) && trade.getTradeExt()!=null){
            TradeExt tradeExt = trade.getTradeExt();
            dmsFreightMatchDto.setTemplateId(NumberUtils.str2Long((String)TradeExtUtils.getExtraFieldValue(tradeExt,TradeExtraFieldEnum.FX_APPOINT_TPL_ID.getField()),trade.getTemplateId()));
            dmsFreightMatchDto.setTemplateName(Optional.ofNullable((String)TradeExtUtils.getExtraFieldValue(tradeExt,TradeExtraFieldEnum.FX_APPOINT_TPL_NAME.getField())).orElse(trade.getTemplateName()));
            dmsFreightMatchDto.setTemplateType(NumberUtils.str2Int((String)TradeExtUtils.getExtraFieldValue(tradeExt,TradeExtraFieldEnum.FX_APPOINT_TPL_TYPE.getField()),trade.getTemplateType()));
            dmsFreightMatchDto.setLogisticsCompanyId(NumberUtils.str2Long((String)TradeExtUtils.getExtraFieldValue(tradeExt,TradeExtraFieldEnum.FX_APPOINT_LOGISTICS_COMPANY_ID.getField()),trade.getLogisticsCompanyId()));
            dmsFreightMatchDto.setExpressName(Optional.ofNullable((String)TradeExtUtils.getExtraFieldValue(tradeExt,TradeExtraFieldEnum.FX_APPOINT_LOGISTICS_COMPANY_NAME.getField())).orElse(trade.getLogisticsCompanyName()));
        }
        List<Order> orders = new ArrayList<>();
        BigDecimalWrapper totalPayment = new BigDecimalWrapper();
        BigDecimalWrapper totalVolume = new BigDecimalWrapper();
        BigDecimalWrapper totalNetWeight = new BigDecimalWrapper();
        List<Trade> mergeTrades = mergeTradeMap.get(trade.getSid());
        for (Order order : CollectionUtils.isNotEmpty(mergeTrades) ? TradeUtils.getOrders4Trade(mergeTrades) : TradeUtils.getOrders4Trade(trade)){
            if (Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()) || OrderUtils.isOtherErpConsigned(order)){
            }else {
                totalPayment.add(order.getPayment());
                totalVolume.add(order.getVolume() != null ? order.getVolume() * order.getNum() : 0.0);
                totalNetWeight.add(order.getNetWeight() != null ? order.getNetWeight() * order.getNum() : 0.0);
                orders.add(order);
            }
        }
        if (CollectionUtils.isNotEmpty(orders)) {
            dmsFreightMatchDto.setOuterIds(orders.stream().filter(o -> StringUtils.isNotBlank(o.getSysOuterId()))
                    .map(Order::getSysOuterId).collect(Collectors.joining(",")));
            List<DmsFreightMatchDto.FreightMatchItemDto> orderItemList = new ArrayList<>(orders.size());
            orders.forEach(order -> {
                DmsFreightMatchDto.FreightMatchItemDto freightMatchItemDto = new DmsFreightMatchDto.FreightMatchItemDto(order.getItemSysId(), order.getSkuSysId(), order.getSysOuterId(), order.getNum());
                orderItemList.add(freightMatchItemDto);
            });
            dmsFreightMatchDto.setOrderItemList(orderItemList);
        }else {
            return null;
        }
        dmsFreightMatchDto.setPayment(totalPayment.add(trade.getPostFee()).subtract(trade.getDiscountFee()).get().setScale(2, BigDecimal.ROUND_HALF_UP));
        dmsFreightMatchDto.setVolume(totalVolume.get().setScale(3, BigDecimal.ROUND_HALF_UP));
        if (postFeeUseActualWeight != null && postFeeUseActualWeight && trade.getWeight() != null && trade.getWeight() > 0) {
            dmsFreightMatchDto.setWeight(BigDecimal.valueOf(trade.getWeight() != null ? trade.getWeight() : 0.00).setScale(3, BigDecimal.ROUND_HALF_UP));
        } else {
            dmsFreightMatchDto.setWeight(totalNetWeight.get().setScale(3, BigDecimal.ROUND_HALF_UP));
        }
        return dmsFreightMatchDto;
    }


    public void fillUserSource(Staff staff, List<Trade> gxTradeList) {

        if(CollectionUtils.isEmpty(gxTradeList)){
            return;
        }

        gxTradeList.forEach(t->{
            if(TradeUtils.isQimenFxSource(t)){
                t.setUserSource(CommonConstants.PLAT_FORM_TYPE_QIMEN);
            }else if(TradeUtils.isAlibabaFxTrade(t)){
                t.setUserSource(CommonConstants.PLAT_FORM_TYPE_1688_FX);
            }
        });

        List<Trade> needCalcTrades = gxTradeList.stream().filter(TradeUtils::needCalcGxPostFee).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(needCalcTrades)){
            return;
        }

        Set<Long> userIds =  needCalcTrades.stream().map(Trade::getTaobaoId).filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Long, String> shopMap = Maps.newHashMapWithExpectedSize(userIds.size());

        ImmutableMap<Long,String> shopImmutableMap = SHOP_CACHE.getAllPresent(userIds);
        if(MapUtils.isNotEmpty(shopImmutableMap)){
            shopMap.putAll(shopImmutableMap);
        }
        ImmutableSet<Long> hasExistsIds = shopImmutableMap.keySet();
        Set<Long> needLoadUserIds = userIds.stream().filter(o->!hasExistsIds.contains(o)).collect(Collectors.toSet());

        if(CollectionUtils.isNotEmpty(needLoadUserIds)){
            List<Shop> shops = shopService.queryByUserIds(null,needLoadUserIds.toArray(new Long[0]));
            if(CollectionUtils.isNotEmpty(shops)){
                shops.forEach(shop->{
                    shopMap.put(shop.getUserId(),shop.getSource());
                    SHOP_CACHE.put(shop.getUserId(),shop.getSource());
                });
            }
        }

        needCalcTrades.forEach(t-> t.setUserSource(shopMap.get(t.getTaobaoId())));

    }

    /**
     * 处理是否重新计算理论运费覆盖实际运费的逻辑
     */
    public void dealWithRealPostFee(Staff staff, List<Trade> trades, TradeConfig tradeConfig) {
        // 如果开启包裹未称重时，包裹重量直接取商品净重时，需要重新计算理论运费覆盖实际运费
        Integer isCoverWeight = tradeConfig.getInteger("isCoverWeight");
        if (YesNoEnum.YES.getValue().equals(isCoverWeight)) {
            //开启了配置
            List<Trade> coverList = new ArrayList<>();
            trades.forEach(trade -> {
                //已称重的不能覆盖
                if (!TradeConstants.YES.equals(trade.getIsWeigh() + "")) {
                    coverList.add(trade);
                }
            });
            recalGxPostFee(staff, coverList, null);
        }
    }

    /**
     * 记录计算理论运费日志
     */
    public void saveCalculateLog(Staff staff,Map<Long, List<FreightCost>> freightCostMap, BusinessNodeEnum businessNode,boolean useLogisticsCompany) {
        for (Map.Entry<Long, List<FreightCost>> costEntry : freightCostMap.entrySet()) {
            List<FreightCost> costList = costEntry.getValue();
            if (CollectionUtils.isEmpty(costList)) {
                return;
            }
            for (List<FreightCost> freightCosts : Lists.partition(costList, 20)) {
                PostFeeLogBuilder logSb = new PostFeeLogBuilder(staff).appendNode(businessNode).append(useLogisticsCompany ? "按快递公司":"按快递模板").append(costEntry.getKey()).append("计算理论运费，").startArray();
                for (FreightCost freightCost : freightCosts) {
                    logSb.startObject().append("sid:").append(freightCost.getSid()).append(",cost:").append(NumberUtils.formatDouble(freightCost.getCount(), 2)).endObject();
                }
                logSb.endArray().multiPrint(logger, Priority.DEBUG);
            }
        }
    }

    /**
     *
     * 分销订单模拟生成供销再匹配供销商运费最低的快递
     *
     * @param fxStaff
     * @param fxTrades
     * @return
     */
    public List<DmsOrderFreightDto> matchGroupFreight(Staff fxStaff, List<Trade> fxTrades) {
       if(CollectionUtils.isEmpty(fxTrades)){
           return Lists.newArrayList();
       }
       List<Trade> trades =  simulateImportGxTradeBusiness.handle(SimulateImportGxTradeData.builder()
                .staff(fxStaff)
                .fxTrades(fxTrades)
                .ifMatchFxPrice(true)
                .build());

        if(CollectionUtils.isEmpty(trades)){
            return Lists.newArrayList();
        }

        Staff staff;
        Trade gxTrade = trades.stream().filter(TradeUtils::isGxOrMixTrade).findFirst().orElse(null);
        if(gxTrade!=null){
            staff = staffService.queryFullByCompanyId(gxTrade.getCompanyId());
            if(staff == null){
                throw new  IllegalArgumentException("未找到对应的供销商信息");
            }
        }else {
            throw new  IllegalArgumentException("模拟生成供销单失败");
        }

        StringBuilder logHead = new FxLogBuilder(staff,FxLogBuilder.ROLE_GX).append("计算分销运费 ").getBuilder();
        if (CollectionUtils.isEmpty(trades)) {
            if (logger.isDebugEnabled()) {
                logger.debug(new StringBuilder(logHead).append("没有需要计算运费的订单"));
            }
            return Lists.newArrayList();
        }
        List<Trade> gxTradeList = new ArrayList<>();
        Map<Long, Trade> gxTradeMap = new HashMap<>();
        Map<Long, List<Trade>> mergeTradeMap = new HashMap<>();
        for (Trade trade : trades) {
            if (TradeUtils.needCalcGxPostFee(trade) || TradeUtils.isQimenFxSource(trade) || TradeUtils.isAlibabaFxTrade(trade)) {
                gxTradeList.add(trade);
                gxTradeMap.put(trade.getSid(), trade);
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)){
                    mergeTradeMap.computeIfAbsent(trade.getMergeSid(), t -> new ArrayList<>()).add(trade);
                }
            }
        }

        List<DmsOrderFreightDto> dmsFreightCostDtoList = Lists.newArrayList();

        if (gxTradeList.size() > 0) {
            List<DmsFreightMatchDto> matchDtoList = new ArrayList<>();
            for (Trade trade : gxTradeList) {
                //合单子单，运费直接设置为0
                if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade) && !Objects.equals(trade.getSid(), trade.getMergeSid())){
                    continue;
                }
                matchDtoList.add(convertTradeToDto(trade, null, mergeTradeMap));
            }
            List<List<DmsFreightMatchDto>> partition = Lists.partition(matchDtoList, 20);
            for (List<DmsFreightMatchDto> dmsFreightMatchDtos : partition) {
                Set<Long> sidSet = dmsFreightMatchDtos.stream().map(DmsFreightMatchDto::getSid).collect(Collectors.toSet());
                List<DmsOrderFreightDto> dmsFreightCostDtoListPart = dmsTradeService.matchGroupFreight(dmsFreightMatchDtos);
                if (CollectionUtils.isNotEmpty(dmsFreightCostDtoListPart)) {
                    dmsFreightCostDtoList.addAll(dmsFreightCostDtoListPart);
                    if (logger.isDebugEnabled()) {
                        logger.debug(new StringBuilder(logHead).append("计算理论运费结果：" + JSON.toJSONString(dmsFreightCostDtoListPart)));
                    }
                } else {
                    logger.debug(new StringBuilder(logHead).append("匹配运费模版为空!：" + sidSet));
                }
            }
        }
        return dmsFreightCostDtoList;
    }
}

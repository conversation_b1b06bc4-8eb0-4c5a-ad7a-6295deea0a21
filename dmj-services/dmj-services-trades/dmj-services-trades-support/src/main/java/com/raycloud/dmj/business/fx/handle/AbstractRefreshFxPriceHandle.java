package com.raycloud.dmj.business.fx.handle;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.fx.*;
import com.raycloud.dmj.business.modify.TradeCalculateTheoryPostFeeBusiness;
import com.raycloud.dmj.business.operate.PayAmountCalculateBusiness;
import com.raycloud.dmj.business.trade.SysTradeDmsBusiness;
import com.raycloud.dmj.business.trade.TradeTraceBusiness;
import com.raycloud.dmj.dms.domain.basic.QueryDmsPriceSourceEnum;
import com.raycloud.dmj.dms.domain.dto.DmsOrderPriceDto;
import com.raycloud.dmj.dms.request.*;
import com.raycloud.dmj.dms.response.DmsCalculateTradeCommissionResponse;
import com.raycloud.dmj.dms.service.trade.api.IDmsTradeService;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigEnum;
import com.raycloud.dmj.domain.trade.config.TradeConfigNew;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.params.TradeAssembleParams;
import com.raycloud.dmj.domain.trades.payment.util.BigDecimalWrapper;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.services.trades.ITradeConfigService;
import com.raycloud.dmj.services.trades.ITradeSearchService;
import com.raycloud.dmj.services.trades.config.TradeConfigGetUtil;
import com.raycloud.dmj.services.trades.support.TradeUpdateService;
import com.raycloud.dmj.warehouse.services.ITradeWarehouseService;
import com.raycloud.ec.api.IEventCenter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

public abstract class AbstractRefreshFxPriceHandle implements IRefreshFxPriceHandle{

    private Logger logger = Logger.getLogger(this.getClass());

    @Resource
    protected IDmsTradeService dmsTradeService;

    @Resource
    protected TradeCalculateTheoryPostFeeBusiness tradeCalculateTheoryPostFeeBusiness;

    @Resource
    protected TradeUpdateService tradeUpdateService;

    @Resource(name = "tbTradeSearchService")
    protected ITradeSearchService tradeSearchService;

    @Resource
    protected ITradeConfigService tradeConfigService;

    @Resource
    protected TradeTraceBusiness tradeTraceBusiness;

    @Resource
    protected IEventCenter eventCenter;

    @Resource
    protected SysTradeDmsBusiness sysTradeDmsBusiness;

    @Resource
    protected PayAmountCalculateBusiness payAmountCalculateBusiness;

    @Resource
    protected ITradeWarehouseService tradeWarehouseService;

    protected boolean match(TradeGxReCalculateData gxReCalculateData, Trade trade) {
        return false;
    }

    /**
     * 是否退货单并且配置不计算流水
     * @param gxReCalculateData 重算容器
     * @param trade
     * @return
     */
    protected boolean isReissueOrChangeItemNotCalFLow(TradeGxReCalculateData gxReCalculateData, Trade trade) {
        return CashFlowUtils.isReissueOrChangeItemPassCalFlow(gxReCalculateData.staff, trade, gxReCalculateData.configMap);
    }

    protected boolean isDmsTradeGxEditNotRelFx(TradeGxReCalculateData gxReCalculateData){
        if (MapUtils.isEmpty(gxReCalculateData.configMap)){
            return false;
        }
        TradeConfigNew dmsTradeGxEditNotRelFx = gxReCalculateData.configMap.get(TradeConfigEnum.DMS_TRADE_GX_EDIT_NOT_REL_FX.getConfigKey());
        return Objects.nonNull(dmsTradeGxEditNotRelFx) && Objects.equals("1", dmsTradeGxEditNotRelFx.getConfigValue());
    }

    public List<Trade> filter(TradeGxReCalculateData refreshFxPriceData){
        return refreshFxPriceData.tradeMap.values().stream().filter(x->match(refreshFxPriceData,x)).collect(Collectors.toList());
    }

    @Override
    public Map<Long, DmsOrderPriceDto> queryDmsPrice(Staff staff, List<Trade> originTrades) {
        Map<Long, List<Trade>> originTradeMap = TradeUtils.groupBySourceId(originTrades);
        Map<Long, DmsOrderPriceDto> dmsOrderPriceDtoMap = new HashMap<>();
        originTradeMap.forEach((sourceId, trades) ->{
            List<QueryDmsPriceOrderInfo> queryDmsPriceOrderInfoList = new ArrayList<>();
            boolean force = queryDmsPriceForce();
            for (Trade trade : trades){
                List<Order> orderList = TradeUtils.getOrders4Trade(trade);
                orderList.forEach(order ->{
                    //其他ERP发货的商品，不请求分销价; 退款的商品(系统前退款与系统后退款，都不请求分销价)
                    if (!force && (OrderUtils.isOtherErpConsigned(order) || Order.REFUND_SUCCESS.equals(order.getRefundStatus()))){
                        return;
                    }
                    QueryDmsPriceOrderInfo queryDmsPriceOrderInfo = new QueryDmsPriceOrderInfo();
                    //去订单的创建时间作为推送时间
                    queryDmsPriceOrderInfo.setAuditPushTime(trade.getCreated());
                    queryDmsPriceOrderInfo.setCreateTime(trade.getCreated());
                    queryDmsPriceOrderInfo.setPayTime(trade.getPayTime());
                    queryDmsPriceOrderInfo.setOid(order.getId());
                    queryDmsPriceOrderInfo.setOuterId(OrderUtils.getFxTrueOuterId(trade, order));
                    queryDmsPriceOrderInfo.setOrderNum(order.getNum());
                    queryDmsPriceOrderInfo.setSource(QueryDmsPriceSourceEnum.TRADE);
                    queryDmsPriceOrderInfoList.add(queryDmsPriceOrderInfo);
                });
            }
            //请求分销价，每100个一批查询
            for (List<QueryDmsPriceOrderInfo> queryDmsPriceOrderInfos : Lists.partition(queryDmsPriceOrderInfoList, 100)) {
                List<DmsOrderPriceDto> dmsOrderPriceDtos = dmsTradeService.queryDmsPrice(staff.getCompanyId(), sourceId, queryDmsPriceOrderInfos);
                if(dmsOrderPriceDtos != null) {
                    for(DmsOrderPriceDto dmsOrderPriceDto : dmsOrderPriceDtos) {
                        dmsOrderPriceDtoMap.put(dmsOrderPriceDto.getOid(),dmsOrderPriceDto);
                    }
                }
            }
        }) ;
        return dmsOrderPriceDtoMap;
    }

    @Override
    public void updateFxTrade(Staff staff, List<Trade> trades, TradeGxReCalculateData tradeGxReCalculateData){
        return;
    }

    /**
     * 供销订单金额计算，包括佣金
     */
    public void calculatePayments(List<Trade> trades, TradeGxReCalculateData refreshFxPriceData){
        if (CollectionUtils.isEmpty(trades)){
            return;
        }
        Staff staff = refreshFxPriceData.staff;
        for (Trade trade : trades) {
            payAmountCalculateBusiness.sharePayAmount(staff, trade, null);
            PaymentUtils.calculateTrade(trade);
        }
    }

    @Override
    public void reFreshTradeCommission(List<Trade> trades, TradeGxReCalculateData refreshFxPriceData){
        if (CollectionUtils.isEmpty(trades) || !(reCalAllIndex() || refreshFxPriceData.reFreshTradeCommission())){
            return;
        }
        List<DmsCalculateTradeCommissionRequest> commissionRequest = Lists.newArrayListWithCapacity(trades.size());
        HashMap<String, Trade> mapTrades = new HashMap<>();
        boolean excludePayment = refreshFxPriceData.excludePaymentOnCommission();
        boolean excludeOrder = refreshFxPriceData.excludeOrderOnCommission();
        trades.forEach(trade -> {
            List<Order> orders;
            //佣金计算，合单处理
            List<Trade> mergeTrades = refreshFxPriceData.mergeTrade.get(trade.getSid());
            if (CollectionUtils.isNotEmpty(mergeTrades)){
                orders = new ArrayList<>(TradeUtils.getOrders4Trade(mergeTrades));
            }else {
                orders = TradeUtils.getOrders4Trade(trade);
            }
            if (CollectionUtils.isEmpty(orders)){
               return;
            }
            BigDecimalWrapper payment  = new BigDecimalWrapper();
            List<String> outerIds = new ArrayList<>();
            List<DmsCalculateTradeCommissionRequest.OrderNum> list = Lists.newArrayList();
            boolean empty = true;
            for (Order order : orders) {
                if (!excludePayment){
                    payment.add(MathUtils.multiply(order.getPrice(),order.getNum()));
                }
                if (excludeOrder && (OrderUtils.isOtherErpConsigned(order) || Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus()))){
                    continue;
                }
                empty = false;
                if (StringUtils.isNotBlank(order.getSysOuterId())){
                    outerIds.add(order.getSysOuterId());
                    list.add(DmsCalculateTradeCommissionRequest.OrderNum.builder()
                            .num(order.getNum())
                            .sysOuterId(order.getSysOuterId())
                            .build());
                }
                if (excludePayment){
                    payment.add(MathUtils.multiply(order.getPrice(),order.getNum()));
                }
            }
            //商品全部都是退款或其他ERP发货，不请求佣金
            if (empty){
                return;
            }
            mapTrades.put(trade.getSid().toString(),trade);
            DmsCalculateTradeCommissionRequest request = new DmsCalculateTradeCommissionRequest();
            request.setDistributorCompanyId(trade.getSourceId());
            request.setFreightCost(new BigDecimalWrapper(trade.getPostFee()).getDouble());
            request.setGoodsCost(payment.getDouble());
            request.setSupplierCompanyId(trade.getCompanyId());
            request.setSysOrderNumber(trade.getSid().toString());
            request.setTradeTagIds(trade.getTagIds());
            commissionRequest.add(request);
            if (CollectionUtils.isNotEmpty(outerIds)){
                request.setOuterIds(String.join(",", outerIds));
                request.setOrderNumList(list);
            }
        });

        if (CollectionUtils.isNotEmpty(commissionRequest)) {
            List<List<DmsCalculateTradeCommissionRequest>> partition = Lists.partition(commissionRequest, 10);
            for (List<DmsCalculateTradeCommissionRequest> requests : partition) {
                List<DmsCalculateTradeCommissionResponse> responses=dmsTradeService.calculateTradeCommission(requests);
                if (!responses.isEmpty()) {
                    responses.forEach(dmsCalculateTradeCommissionResponse -> {
                        Trade trade = mapTrades.get(dmsCalculateTradeCommissionResponse.getSysOrderNumber());
                        if (trade != null) {
                            //订单实付=商品实付+运费收入-订单维度优惠
                            BigDecimalWrapper realPayment = new BigDecimalWrapper(trade.getPayment()).add(trade.getPostFee());
                            //佣金收入
                            Double commission = dmsCalculateTradeCommissionResponse.getCommission();
                            realPayment.subtract(commission);
                            //订单实付=商品实付+运费收入-订单维度优惠
                            trade.setDiscountFee(new BigDecimalWrapper(commission).getString());
                            trade.setPayment(realPayment.getString());
                            trade.setPayAmount(realPayment.getString());
                        }
                    });
                }
            }
        }
    }

    /**
     * 分销价是否变更
     * @param order 商品信息
     * @param dmsOrderPrice 最新分销价
     * @return
     */
    protected boolean isUpdatePrice(Order order, String dmsOrderPrice){
        return !MathUtils.equals(dmsOrderPrice, order.getPrice());
    }

    public String getFxPriceLogContent(int logType){
        switch (logType){
            case 1:
                return "订单修改分销价:";
            case 2:
                return "";
            default:
                return null;
        }
    }

    protected void addDefaultPriceLog(StringBuilder priceLog, Order order, String dmsOrderPrice){
        priceLog.append("最新分销价为").append(dmsOrderPrice).append(";");
    }

    public void addFxPriceLog(StringBuilder priceLog, Order order, String outerId, String dmsOrderPrice, int result, int fxPriceLogType){
        priceLog.append("商品(").append(outerId).append(")");
        switch (fxPriceLogType){
            case 1:
                if (1 == result){
                    addDefaultPriceLog(priceLog, order, ":商品退款或其他ERP发货,分销价不变");
                }else if (2 == result){
                    addDefaultPriceLog(priceLog, order, ":商品不允许分销,分销价不变");
                }else {
                    addDefaultPriceLog(priceLog, order, dmsOrderPrice);
                }
                break;
            case 2:
                if (1 == result){
                    priceLog.append("分销价为").append(order.getPrice()).append(",商品退款或其他ERP发货,分销价不变;");
                }else if (2 == result){
                    priceLog.append("分销价为0,为不可分销商品;");
                }else {
                    priceLog.append("分销价为").append(order.getPrice()).append(";");
                }
                break;
            default:
                break;
        }
    }

    /**
     * 重算供销Order的金额
     * 1：去掉Order优惠
     * 2: price(分销价) * num 作为totalFee与payment
     * 3: payAmount = payment
     * 4: price = 分销价
     * @param order
     * @param dmsOrderPrice 分销价
     */
    protected void refreshOrderPrice(Order order, String dmsOrderPrice){
        if (StringUtils.isBlank(dmsOrderPrice)){
            dmsOrderPrice = order.getPrice();
        }
        order.setPrice(dmsOrderPrice);
        String total = new BigDecimalWrapper(order.getPrice()).multiply(order.getNum()).getString();
        order.setTotalFee(total);
        //payment = totalFee
        order.setPayment(total);
        order.setPayAmount(order.getPayment());
    }

    private void refreshWeightAndVolume(List<Trade> trades){
        trades.forEach(trade -> {
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            boolean isAfterSendGoods = TradeStatusUtils.isAfterSendGoods(trade.getSysStatus());
            double netWeight = 0.0;
            double volume = 0.0;
            for (Order order : orders) {
                if (order.getEnableStatus() != null && order.getEnableStatus() == 0) {
                    continue;
                }
                //未匹配的跳过
                if (order.getItemSysId() <= 0) {
                    continue;
                }
                //套件、组合、加工单品跳过
                if (order.isSuit(true) || order.isProcess(true) || order.isGroup(true)){
                    continue;
                }
                //其他ERP发货的商品、系统发货前退款成功的跳过，系统发货后退款的要参与计算
                if (OrderUtils.isOtherErpConsigned(order) || OrderUtils.isRefundedBeforeSysConsign(order)){
                    continue;
                }
                //排除合单的子单
                if (!Objects.equals(trade.getSid(), order.getSid())){
                    continue;
                }
                //未发货的订单，排除掉已发货的商品
                if (isAfterSendGoods || !TradeStatusUtils.isAfterSendGoods(order.getSysStatus())) {
                    netWeight += (order.getNetWeight() != null ? order.getNetWeight() * order.getNum() : 0.0);
                    volume += (order.getVolume() != null ? order.getVolume() * order.getNum() : 0.0);
                }
            }
            trade.setNetWeight(NumberUtils.formatDouble(netWeight, 3));
            trade.setVolume(volume);
        });
    }

    @Override
    public List<TradeCashFlowData> calculateTradeCashFlow(List<Trade> trades, TradeGxReCalculateData refreshFxPriceData){
        if (CollectionUtils.isEmpty(trades)){
            return new ArrayList<>();
        }
        List<TradeCashFlowData> cashFlowData = new ArrayList<>();
        for (Trade trade : trades){
            TradeCashFlowData tradeCashFlowData = TradeCashFlowData.getInstance(trade, refreshFxPriceData);
            fillTradeCashFlowData(tradeCashFlowData, refreshFxPriceData);
            cashFlowData.add(tradeCashFlowData);
            //流水日志添加
            if (refreshFxPriceData.isAddFlowTrace()){
                trade.getOperations().put(OpEnum.TRADE_REFRESH_FX_CASH_FLOW, String.format("订单重算流水，本次计算的流水为%s", tradeCashFlowData.getNewAmount()));
                //如果有原始订单，给原始订单添加流水日志
                if (Objects.nonNull(trade.getSourceTrade())){
                    trade.getSourceTrade().getOperations().put(OpEnum.TRADE_REFRESH_FX_CASH_FLOW, String.format("订单重算流水，本次计算的流水为%s", tradeCashFlowData.getNewAmount()));
                }
            }
        }
        printCashFlowLog(refreshFxPriceData, cashFlowData);
        return cashFlowData;
    }

    private void printCashFlowLog(TradeGxReCalculateData refreshFxPriceData, List<TradeCashFlowData> tradeCashFlowDatas){
        if (refreshFxPriceData.printFlowLog() && CollectionUtils.isNotEmpty(tradeCashFlowDatas)){
            Lists.partition(tradeCashFlowDatas, 10).forEach(part ->{
                StringBuilder stringBuilder = new StringBuilder("[");
                for (TradeCashFlowData tradeCashFlowData : part){
                    stringBuilder.append("{sid=").append(tradeCashFlowData.getTrade().getSid())
                            .append(",flowMod=").append(tradeCashFlowData.getFlowMod())
                            .append(",oldAmount=").append(tradeCashFlowData.getOldAmount())
                            .append(",newAmount=").append(tradeCashFlowData.getNewAmount()).append("},");
                }
                new FxLogBuilder(refreshFxPriceData.staff, FxLogBuilder.ROLE_GX).append(stringBuilder.append("]")).printInfo(logger);
            });
        }
    }

    protected void fillTradeCashFlowData(TradeCashFlowData tradeCashFlowData, TradeGxReCalculateData gxReCalculateData){
        tradeCashFlowData.fillTraditionCashFlowData(gxReCalculateData, true, null);
    }

    @Override
    public void reFreshFxPrice(List<Trade> trades, TradeGxReCalculateData refreshFxPriceData){
        if (CollectionUtils.isEmpty(trades) || !(reCalAllIndex() || refreshFxPriceData.reFreshFxPrice())){
            return;
        }
        boolean force = refreshFxPriceData.isRefreshForce();
        int fxPriceSetType = getFxPriceSetType(refreshFxPriceData);
        int fxPriceLogType = refreshFxPriceData.getFxPriceLogType();
        String content = getFxPriceLogContent(fxPriceLogType);
        String defaultOrderPrice = 1 == fxPriceSetType ? null : "0.0";
        Map<Long, DmsOrderPriceDto> priceDtoMap = queryDmsPrice(refreshFxPriceData.staff, trades);
        for (Trade trade : trades){
            List<Order> orders = TradeUtils.getOrders4Trade(trade);
            StringBuilder priceLog = new StringBuilder(content);
            //是否记录日志
            boolean update = false;
            for (Order order : orders){
                //赠品跳过
                if (order.isGift()){
                    continue;
                }
                DmsOrderPriceDto dmsOrderPriceDto = priceDtoMap.get(order.getId());
                if (Objects.isNull(dmsOrderPriceDto)){
                    //没有返回分销价
                    if (force){
                        refreshOrderPrice(order, null);
                    }
                    addFxPriceLog(priceLog, order, OrderUtils.getFxTrueOuterId(trade, order), null, 1, fxPriceLogType);
                    update = true;
                    continue;
                }
                if (!dmsOrderPriceDto.isAllowSale()){
                    if (force || 1 != fxPriceSetType){
                        refreshOrderPrice(order, defaultOrderPrice);
                    }
                    addFxPriceLog(priceLog, order, dmsOrderPriceDto.getOuterId(), null, 2, fxPriceLogType);
                    update = true;
                    continue;
                }
                String dmsOrderPrice = null == dmsOrderPriceDto.getPrice() ? "0.0" : dmsOrderPriceDto.getPrice() + "";
                //分销价是否变更
                boolean updatePrice = isUpdatePrice(order, dmsOrderPrice);
                //分销价无论是否更改，记录最新分销价与操作日志
                addFxPriceLog(priceLog, order, dmsOrderPriceDto.getOuterId(), dmsOrderPrice, 3, fxPriceLogType);
                update = true;
                if (updatePrice || force){
                    refreshOrderPrice(order, dmsOrderPrice);
                }
            }
            if (update){
                trade.getOperations().put(OpEnum.TRADE_REFRESH_FX_PRICE, priceLog.deleteCharAt(priceLog.length() -1 ).toString());
            }
        }
    }

    @Override
    public void refreshWeightAndVolume(List<Trade> trades, TradeGxReCalculateData refreshFxPriceData) {
        //重量(净重)与体积刷新
        if (CollectionUtils.isNotEmpty(trades) && refreshFxPriceData.isRefreshWeightAndVolume()){
            sysTradeDmsBusiness.fillGxTradeItemInfo(refreshFxPriceData.staff, trades, false, false);
            //这里不处理合单场景
            refreshWeightAndVolume(trades);
        }
    }

    @Override
    public void refreshGxPostFee(List<Trade> trades, TradeGxReCalculateData refreshFxPriceData) {
        if (CollectionUtils.isEmpty(trades) || !(reCalAllIndex() || refreshFxPriceData.isReCalGxPostFee())){
            return;
        }
        Set<Trade> unMatch = new HashSet<>();
        for (Trade trade : trades){
            unMatch.add(trade);
            //合单场景下，主单请求运费，把子单一起带上
            if (TradeUtils.isMerge(trade)){
                List<Trade> mergeTrades = refreshFxPriceData.mergeTrade.get(trade.getSid());
                if (CollectionUtils.isNotEmpty(mergeTrades)){
                    unMatch.addAll(mergeTrades);
                }
            }
        }
        //合单场景下，子单跳过运费重算,净重
        tradeCalculateTheoryPostFeeBusiness.recalGxPostFee(refreshFxPriceData.staff, new ArrayList<>(unMatch), null);
    }

    protected int getFxPriceSetType (TradeGxReCalculateData refreshFxPriceData){
        return refreshFxPriceData.getFxPriceSetType();
    }

    protected boolean queryDmsPriceForce (){
        return false;
    }

    /**
     * 是否需要重算所有指标 -- 默认是false
     * 针对一些特定的处理，默认是必须重算所有指标的
     * @return
     */
    protected boolean reCalAllIndex(){
        return false;
    }

    /**
     * 填充拆分订单
     * @param trades 需要处理的订单
     * @param refreshFxPriceData 刷新数据同容器
     */
    protected void fillSplitTrades(List<Trade> trades, TradeGxReCalculateData refreshFxPriceData){
        List<Trade> splitTrade = trades.stream().filter(TradeUtils::isSplit).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(splitTrade)){
            return;
        }
        List<Trade> originSplitTrades = tradeSearchService.queryAndAssemblyByKeys(refreshFxPriceData.staff, TradeAssembleParams.tradeWithOrder(), "t.split_sid", splitTrade.stream().map(Trade::getSplitSid).toArray());
        for (Trade split : originSplitTrades){
            refreshFxPriceData.splitTrade.computeIfAbsent(split.getSplitSid(), t -> new ArrayList<>()).add(split);
            if (refreshFxPriceData.tradeMap.containsKey(split.getSid())){
                continue;
            }
            //新查询出来的订单，放入操作订单池中(更新订单数据)与操作集合中(操作流水)
            refreshFxPriceData.tradeMap.put(split.getSid(), split);
            trades.add(split);
        }
    }
}

package com.raycloud.dmj.services.trade.merge;

import com.raycloud.dmj.dao.trade.merge.MergeItemGroupDao;
import com.raycloud.dmj.dao.trade.merge.MergeItemGroupTypeDao;
import com.raycloud.dmj.dao.trade.merge.MergeItemGroupTypeDetailDao;
import com.raycloud.dmj.domain.account.PropertyClassify;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.item.SellerCat;
import com.raycloud.dmj.domain.trade.item.TradeItemGroupType;
import com.raycloud.dmj.domain.trade.item.TradeItemGroupTypeDetail;
import com.raycloud.dmj.domain.trade.item.TradeItemGroupTypeEnum;
import com.raycloud.dmj.domain.trade.merge.MergeItemGroup;
import com.raycloud.dmj.domain.trade.merge.MergeItemGroupQuery;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.item.search.api.DmjItemCommonSearchApi;
import com.raycloud.dmj.item.search.dto.BrandDto;
import com.raycloud.dmj.item.search.dto.SupplierDto;
import com.raycloud.dmj.item.search.request.QueryBrandByBrandIdListRequest;
import com.raycloud.dmj.item.search.request.QuerySupplierBySupplierIdListRequest;
import com.raycloud.dmj.item.search.request.StaffRequest;
import com.raycloud.dmj.item.search.response.QueryBrandByBrandIdListResponse;
import com.raycloud.dmj.item.search.response.QuerySupplierBySupplierIdListResponse;
import com.raycloud.dmj.services.basis.ISupplierService;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.items.ISellerCatService;
import com.raycloud.dmj.services.trades.TradeException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName MergeItemGroupService
 * @Description 合单商品分组信息实现
 * <AUTHOR>
 * @Date 2023/12/13
 * @Version 1.0
 */
@Service
public class MergeItemGroupService implements IMergeItemGroupService {

    @Resource
    MergeItemGroupDao mergeItemGroupDao;
    @Resource
    MergeItemGroupTypeDao mergeItemGroupTypeDao;
    @Resource
    MergeItemGroupTypeDetailDao mergeItemGroupTypeDetailDao;

    @Resource
    ISupplierService supplierService;

    @Resource
    ISellerCatService sellerCatService;

    @Resource
    DmjItemCommonSearchApi dmjItemCommonSearchApi;

    @Resource
    IItemServiceDubbo itemServiceDubbo;

    @Override
    @Transactional
    public void save(Staff staff, MergeItemGroup itemGroup) {
        if (itemGroup.getEnableStatus() == 0) {//删除
            delete(staff, itemGroup);
        } else {
            if (itemGroup.getId() == null) {//新增
                add(staff, itemGroup);
            } else {//修改
                update(staff, itemGroup);
            }
        }

    }

    @Override
    public List<MergeItemGroup> query(Staff staff, MergeItemGroupQuery queryParams) {
        List<MergeItemGroup> itemGroups = mergeItemGroupDao.query(staff, queryParams);
        fill(staff, queryParams, itemGroups);
        return itemGroups;
    }

    private void fill(Staff staff, MergeItemGroupQuery queryParams, List<MergeItemGroup> groups) {
        if (!queryParams.isFillDetail() || CollectionUtils.isEmpty(groups)) {
            return;
        }

        Map<Long, MergeItemGroup> idItemGroupMap = new HashMap<>();
        groups.forEach(itemGroup -> idItemGroupMap.put(itemGroup.getId(), itemGroup));

        List<TradeItemGroupType> types = mergeItemGroupTypeDao.queryByGroupIds(staff, new ArrayList<>(idItemGroupMap.keySet()));
        if (CollectionUtils.isEmpty(types)) {
            return;
        }

        Map<Long, List<TradeItemGroupType>> groupIdItemTypesMap = new HashMap<>();
        Map<Long, TradeItemGroupType> idTypeFillDetailMap = new HashMap<>();
        for (TradeItemGroupType type : types) {
            type.setGroupTypeName(TradeItemGroupTypeEnum.getGroupTypeEnum(type.getGroupType()).getMsg());
            groupIdItemTypesMap.computeIfAbsent(type.getGroupId(), k -> new ArrayList<>()).add(type);
            if (type.getGroupSingle() == 0) {
                idTypeFillDetailMap.put(type.getId(), type);
            }
        }

        for (Map.Entry<Long, List<TradeItemGroupType>> entry : groupIdItemTypesMap.entrySet()) {
            idItemGroupMap.get(entry.getKey()).setTypes(entry.getValue());
        }

        if (idTypeFillDetailMap.size() == 0) {
            return;
        }
        List<TradeItemGroupTypeDetail> details = mergeItemGroupTypeDetailDao.queryByGroupTypeIds(staff, new ArrayList<>(idTypeFillDetailMap.keySet()));
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        Map<Long, List<TradeItemGroupTypeDetail>> typeIdDetailsMap = new HashMap<>();
        details.forEach(detail -> typeIdDetailsMap.computeIfAbsent(detail.getGroupTypeId(), k -> new ArrayList<>()).add(detail));
        for (Map.Entry<Long, List<TradeItemGroupTypeDetail>> entry : typeIdDetailsMap.entrySet()) {
            TradeItemGroupType type = idTypeFillDetailMap.get(entry.getKey());
            List<TradeItemGroupTypeDetail> typeDetails = entry.getValue();
            type.setDetails(typeDetails);
            if (queryParams.isFillDetailBusinessName()) {
                fillDetailBusinessName(staff, type, typeDetails);
            }
        }
    }

    private void fillDetailBusinessName(Staff staff, TradeItemGroupType type, List<TradeItemGroupTypeDetail> details) {
        switch (TradeItemGroupTypeEnum.getGroupTypeEnum(type.getGroupType())) {
            case SUPPLIER: {
                Map<Long, TradeItemGroupTypeDetail> supplierIdDetailMap = new HashMap<>();
                details.forEach(detail -> supplierIdDetailMap.put(NumberUtils.str2Long(detail.getBusinessKey()), detail));

                QuerySupplierBySupplierIdListRequest req = new QuerySupplierBySupplierIdListRequest();
                req.setSupplierIdList(new ArrayList<>(supplierIdDetailMap.keySet()));
                req.setStaffRequest(StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build());
                QuerySupplierBySupplierIdListResponse resp = dmjItemCommonSearchApi.querySupplierBySupplierIdList(req);
                List<SupplierDto> suppliers = resp.getList();
                if (CollectionUtils.isNotEmpty(suppliers)) {
                    for (SupplierDto supplier : suppliers) {
                        supplierIdDetailMap.get(supplier.getId()).setBusinessName(supplier.getName());
                    }
                }
                break;
            }
            case BRAND: {
                Map<Long, TradeItemGroupTypeDetail> brandIdToDetail = new HashMap<>();
                details.forEach(detail -> brandIdToDetail.put(NumberUtils.str2Long(detail.getBusinessKey()), detail));

                QueryBrandByBrandIdListRequest req = new QueryBrandByBrandIdListRequest();
                req.setBrandIdList(new ArrayList<>(brandIdToDetail.keySet()));
                req.setStaffRequest(StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build());
                QueryBrandByBrandIdListResponse resp = dmjItemCommonSearchApi.queryBrandByBrandIdList(req);

                List<BrandDto> brands = resp.getList();
                if (CollectionUtils.isNotEmpty(brands)) {
                    for (BrandDto brand : brands) {
                        brandIdToDetail.get(brand.getBrandId()).setBusinessName(brand.getBrandName());
                    }
                }
                break;
            }
            case FIRST_CAT: {
                Map<Long, TradeItemGroupTypeDetail> catToDetail = new HashMap<>(details.size());
                details.forEach(detail -> catToDetail.put(NumberUtils.str2Long(detail.getBusinessKey()), detail));
                List<PropertyClassify> ps = itemServiceDubbo.queryPropertyClassifyByIds(staff, new ArrayList<>(catToDetail.keySet()));
                if (CollectionUtils.isNotEmpty(ps)) {
                    for (PropertyClassify p : ps) {
                        catToDetail.get(p.getId()).setBusinessName(p.getName());
                    }
                }
                break;
            }
            case CID: {
                Map<Long, TradeItemGroupTypeDetail> cidToDetail = new HashMap<>();
                details.forEach(detail -> cidToDetail.put(NumberUtils.str2Long(detail.getBusinessKey()), detail));
                List<SellerCat> cats = sellerCatService.queryByCidList(staff, new ArrayList<>(cidToDetail.keySet()));
                if (CollectionUtils.isNotEmpty(cats)) {
                    for (SellerCat cat : cats) {
                        cidToDetail.get(cat.getCid()).setBusinessName(cat.getName());
                    }
                }
                break;
            }
        }
    }

    private void add(Staff staff, MergeItemGroup itemGroup) {
        checkName(staff, itemGroup);
        mergeItemGroupDao.insert(staff, itemGroup);

        List<TradeItemGroupType> types = itemGroup.getTypes();
        List<TradeItemGroupTypeDetail> details = new ArrayList<>();
        for (TradeItemGroupType type : types) {
            type.setGroupId(itemGroup.getId());
            mergeItemGroupTypeDao.insert(staff, type);
            if (CollectionUtils.isNotEmpty(type.getDetails())) {
                for (TradeItemGroupTypeDetail detail : type.getDetails()) {
                    detail.setGroupTypeId(type.getId());
                    details.add(detail);
                }
            }
        }
        mergeItemGroupTypeDetailDao.insert(staff, details);
    }

    private void delete(Staff staff, MergeItemGroup itemGroup) {
        mergeItemGroupDao.delete(staff, itemGroup.getId());
        List<TradeItemGroupType> types = mergeItemGroupTypeDao.queryByGroupIds(staff, Collections.singletonList(itemGroup.getId()));
        if (CollectionUtils.isEmpty(types)) {
            return;
        }
        List<Long> typeIds = types.stream().map(TradeItemGroupType::getId).collect(Collectors.toList());
        mergeItemGroupTypeDao.delete(staff, typeIds);
        List<TradeItemGroupTypeDetail> details = mergeItemGroupTypeDetailDao.queryByGroupTypeIds(staff, typeIds);
        if (CollectionUtils.isNotEmpty(details)) {
            mergeItemGroupTypeDetailDao.delete(staff, details.stream().map(TradeItemGroupTypeDetail::getId).collect(Collectors.toList()));
        }
    }

    private void update(Staff staff, MergeItemGroup itemGroup) {
        MergeItemGroup dbItemGroup = mergeItemGroupDao.queryById(staff, itemGroup.getUserId(), itemGroup.getId());
        if (dbItemGroup == null) {
            throw new TradeException("当前分组信息已被删除，请刷新页面重试！");
        }
        //修改分组
        boolean updateGroupName = !StringUtils.equals(itemGroup.getGroupName(), dbItemGroup.getGroupName());
        if (updateGroupName || itemGroup.getOpen() - dbItemGroup.getOpen() != 0) {
            if (updateGroupName) {
                checkName(staff, itemGroup);
            }
            mergeItemGroupDao.update(staff, itemGroup);
        }

        //修改分组类型
        Map<Long, TradeItemGroupType> dbTypeMap = new HashMap<>();
        List<TradeItemGroupType> dbTypes = mergeItemGroupTypeDao.queryByGroupIds(staff, Collections.singletonList(itemGroup.getId()));
        if (CollectionUtils.isNotEmpty(dbTypes)) {
            dbTypes.forEach(type -> dbTypeMap.put(type.getId(), type));
        }

        //新增
        List<TradeItemGroupTypeDetail> insertDetails = new ArrayList<>();
        //删除
        List<Long> deleteTypeIds = new ArrayList<>();
        List<Long> deleteDetailIds = new ArrayList<>();
        //修改
        List<TradeItemGroupType> updateTypes = new ArrayList<>();
        List<TradeItemGroupType> updateTypeDetails = new ArrayList<>();
        for (TradeItemGroupType type : itemGroup.getTypes()) {
            if (type.getId() == null) {//新增类型
                type.setGroupId(itemGroup.getId());
                mergeItemGroupTypeDao.insert(staff, type);
                if (CollectionUtils.isNotEmpty(type.getDetails())) {
                    for (TradeItemGroupTypeDetail detail : type.getDetails()) {
                        detail.setGroupTypeId(type.getId());
                        insertDetails.add(detail);
                    }
                }
            } else {
                TradeItemGroupType dbType = dbTypeMap.get(type.getId());
                if (dbType == null) {
                    throw new TradeException("当前分组类型信息已被删除，请刷新页面重试！");
                }
                if (type.getEnableStatus() == 0) {//删除类型
                    deleteTypeIds.add(type.getId());
                    List<TradeItemGroupTypeDetail> details = type.getDetails();
                    if (CollectionUtils.isNotEmpty(details)) {
                        details.forEach(detail -> deleteDetailIds.add(detail.getId()));
                    }
                } else {
                    //类型明细可能有修改
                    updateTypeDetails.add(type);
                    if (type.getGroupSingle() - dbType.getGroupSingle() != 0) {//修改类型
                        updateTypes.add(type);
                    }
                }
            }
        }

        mergeItemGroupTypeDao.delete(staff, deleteTypeIds);
        mergeItemGroupTypeDao.update(staff, updateTypes);

        //修改分组类型明细
        Map<Long, TradeItemGroupTypeDetail> dbDetailMap = new HashMap<>();
        List<TradeItemGroupTypeDetail> dbDetails = mergeItemGroupTypeDetailDao.queryByGroupTypeIds(staff, new ArrayList<>(dbTypeMap.keySet()));
        Map<Long, List<Long>> typeIdToDetailIds = new HashMap<>(dbTypeMap.size());
        if (CollectionUtils.isNotEmpty(dbDetails)) {
            dbDetails.forEach(detail -> {
                        dbDetailMap.put(detail.getId(), detail);
                        typeIdToDetailIds.computeIfAbsent(detail.getGroupTypeId(), k -> new ArrayList<>()).add(detail.getId());
                    }
            );
        }
        for (TradeItemGroupType type : updateTypeDetails) {
            List<TradeItemGroupTypeDetail> details = type.getDetails();
            if (CollectionUtils.isNotEmpty(details)) {
                for (TradeItemGroupTypeDetail detail : details) {
                    detail.setGroupTypeId(type.getId());
                    if (detail.getId() == null) {//新增
                        insertDetails.add(detail);
                    } else {
                        TradeItemGroupTypeDetail dbDetail = dbDetailMap.get(detail.getId());
                        if (dbDetail == null) {
                            throw new TradeException("当前分组类型明细信息已被删除，请刷新页面重试！");
                        }
                        if (detail.getEnableStatus() == 0) {//删除
                            deleteDetailIds.add(detail.getId());
                        } else if (!StringUtils.equals(detail.getBusinessKey(), dbDetail.getBusinessKey())) {//修改 这里正常情况下不会发生
                            deleteDetailIds.add(detail.getId());//删除
                            insertDetails.add(detail);//再新增
                        }
                    }
                }
            }
            List<Long> deleteDetailsIds;
            if (Objects.equals(type.getGroupSingle(), 1) && (deleteDetailsIds = typeIdToDetailIds.get(type.getId())) != null) {
                deleteDetailIds.addAll(deleteDetailsIds);
            }
        }
        mergeItemGroupTypeDetailDao.delete(staff, deleteDetailIds);
        mergeItemGroupTypeDetailDao.insert(staff, insertDetails);
    }


    private void checkName(Staff staff, MergeItemGroup itemGroup) {
        MergeItemGroup existItemGroup = mergeItemGroupDao.queryByName(staff, itemGroup.getUserId(), itemGroup.getGroupName());
        if (existItemGroup != null) {
            throw new TradeException("合单分组名称不允许重复！");
        }
    }
}

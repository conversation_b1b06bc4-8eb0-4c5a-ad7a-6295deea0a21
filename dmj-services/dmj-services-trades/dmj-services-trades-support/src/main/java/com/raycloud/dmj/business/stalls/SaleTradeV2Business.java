package com.raycloud.dmj.business.stalls;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raycloud.dmj.business.stalls.strategy.SaleTradeAbstractBusiness;
import com.raycloud.dmj.common.enums.SaleListenerEnums;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.OpEnum;
import com.raycloud.dmj.domain.enums.SaleOrderTypeEnum;
import com.raycloud.dmj.domain.enums.SaleTradeStatusEnum;
import com.raycloud.dmj.domain.stalls.SaleConfig;
import com.raycloud.dmj.domain.stalls.SaleOrder;
import com.raycloud.dmj.domain.stalls.SaleTrade;
import com.raycloud.dmj.domain.stalls.SaleTradeQueryParam;
import com.raycloud.dmj.domain.stalls.common.Pagination;
import com.raycloud.dmj.domain.stalls.common.constants.SaleConstants;
import com.raycloud.dmj.domain.stalls.common.dto.CustomerInfoDto;
import com.raycloud.dmj.domain.stalls.common.result.Result;
import com.raycloud.dmj.domain.stalls.common.result.ResultCode;
import com.raycloud.dmj.domain.stalls.common.utils.SaleLogUtils;
import com.raycloud.dmj.domain.stalls.enums.SaleConfigEnums;
import com.raycloud.dmj.domain.stalls.enums.SaleOpEnums;
import com.raycloud.dmj.domain.stalls.payment.entity.SalePayment;
import com.raycloud.dmj.domain.stalls.trade.dto.SaleSettleDto;
import com.raycloud.dmj.domain.stalls.trade.vobj.*;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.request.TradePictureMemoUpdateParams;
import com.raycloud.dmj.domain.trades.utils.TradeUtils;
import com.raycloud.dmj.external.consumer.DmsConsumer;
import com.raycloud.dmj.external.notice.DmsNotice;
import com.raycloud.dmj.services.trades.support.TradePictureMemoService;
import com.raycloud.dmj.stalls.business.SalePaymentBusiness;
import com.raycloud.ec.api.EventInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Author: WangChao(王超)
 * @Date: 2020/11/26 2:35 PM
 * @Version 1.0
 * @Email <EMAIL>
 */
@Service
@Slf4j
public class SaleTradeV2Business extends SaleTradeAbstractBusiness {

    @Resource
    private SalePaymentBusiness salePaymentBusiness;

    @Resource
    private DmsNotice dmsNotice;
    @Resource
    private DmsConsumer dmsConsumer;
    @Resource
    private TradePictureMemoService tradePictureMemoService;


    @Override
    public void initStatus(Staff staff, SaleTrade saleTrade, SaleSettleDto saleSettle) {
        if (saleSettle.getOnlySettleType() == 22) { //预配货重新配货，删除原有销货单，作废订单
            cleanOrigin(staff, saleTrade, saleSettle);
        }
        saleTrade.setDealOrder(1);
        final OrderInfo orderInfo = saleTrade.getOrderInfo();
        if (saleSettle.getSettleType() == 0 || saleSettle.isDirect()) {   //现场收银｜直接开单｜导入开单
            saleTrade.setStatus(SaleTradeStatusEnum.SETTLED.getSaleOrderStatus());
            orderInfo.setAllocateItemNum(orderInfo.getNum());
        }
        if (saleSettle.getOnlySettleType() == 20) { //预配货
            saleTrade.setStatus(SaleTradeStatusEnum.PICKING_GOODS.getSaleOrderStatus());
            orderInfo.setAllocateItemNum(orderInfo.getNum());
        }
        if (saleSettle.getOnlySettleType() == 21) { //预配货结账
            saleTrade.setStatus(SaleTradeStatusEnum.SETTLED.getSaleOrderStatus());
        }
        if (saleSettle.isDraft()) {      //挂起
            saleTrade.setStatus(SaleTradeStatusEnum.DRAFT.getSaleOrderStatus());
            if(saleSettle.getSettleType() == 31) {  //在线付款后结账，设置付款状态为待付款
                saleTrade.setPayStatus(0);
                saleTrade.getPaymentGather().setPayStatus(0);
            }
        }
    }

    /**
     * 清空唯一码信息
     */
    private void cleanUniqueCodes(SaleTrade saleTrade) {
        for (SaleOrder e : saleTrade.getOrders()) {
            if (e.getExtInfo() != null) {
                e.getExtInfo().setUniqueCodes(null);
                e.getExtInfo().setUniqueCodeType(null);
            }
        }
    }

    /**
     * 重新配货清空原销货单订单信息，并设置结账方式为预配货
     */
    private void cleanOrigin(Staff staff, SaleTrade saleTrade, SaleSettleDto saleSettle) {
        tradeConsumer.cancelTrade(staff, saleTrade.getSid());
        saleTradeRepo.delete(staff, saleTrade.getId(), 0, null);
        saleTrade.setReallocateId(saleTrade.getId());
        saleTrade.setId(null);  //销货单id置空，销货单结账类型变预配货
        saleTrade.setSid(null);
        saleTrade.setTradeShortId(null);
        String remark = "配货作废的销货单 " + saleTrade.getReallocateId();
        saleTrade.setRemark(StringUtils.isBlank(saleTrade.getRemark()) ? remark : saleTrade.getRemark() + ";" + remark);
        saleTrade.getOrders().forEach(e -> e.setId(null));

        saleSettle.setSettleType(20);
    }

    @Override
    protected Result saveDraft(Staff staff, SaleTrade saleTrade, SaleSettleDto settleDto) {
        if (settleDto.getSettleType() == 31) {  //挂起并付款，草稿阶段就保存支付记录
            PaymentGather paymentGather = saleTrade.initPaymentGather(0);
            dmsNotice.sendOnlineRecord(staff, paymentGather.getPaymentRecords().get(0), saleTrade).assertTrue();
            salePaymentBusiness.save(staff, paymentGather.getSalePayments());
        }
        return Result.isOk();
    }

    @Override
    public List<SaleOrder> rejectSuits(Staff staff, SaleTrade saleTrade, SaleSettleDto settleDto) {
        List<SaleOrder> suits = Lists.newArrayList();
        if (settleDto.getOnlySettleType() != 21) {    //v2套件转单品在结账时
            return suits;
        }
        Iterator<SaleOrder> iterator = saleTrade.getOrders().iterator();
        while (iterator.hasNext()) {
            SaleOrder next = iterator.next();
            //非套件||配货完成套件不需要自动转单品
            if (!next.itemIsSuit() || CollectionUtils.isEmpty(next.getSingleSuits()) ||
                    next.getInsufficientNum() == null || next.getInsufficientNum() == 0) {
                continue;
            }
            //结账时新增的套件商品，要把suits置为空，不然update订单的时候，添加套件单品使用的是add
            if (next.getId() == null) {
                next.setSingleSuits(null);
                continue;
            }
            suits.add(next);
            iterator.remove();
        }
        return suits;
    }

    @Transactional
    public Result settle(Staff staff, SaleTrade saleTrade, SaleSettleDto settleDto) {
        if (saleTrade.getSubmitterId() == null) {
            saleTrade.setSubmitterId(staff.getId());
        }
        PaymentGather paymentGather = saleTrade.initPaymentGather(0);
        Trade trade = saleTradeFactory.buildTrade(staff, saleTrade);
        if (trade != null) {
            settleDto.setPayType(saleTrade.getPayType());
            stallsTradeBusiness.settleV2(staff, trade, settleDto);
            saleTrade.setSid(trade.getSid());
            saleTrade.setTradeShortId(trade.getShortId());
            saleTrade.setPrinted(0);
        } else if (!paymentGather.findRecordPay(1, null).isEmpty()) {
            return Result.isFail(ResultCode.EXTRA_VERIFY_FAIL, "仅退货不支持收订金，请修改后重试");
        }
        List<SaleOrder> retOrders = saleTrade.buildOrderByType(SaleOrderTypeEnum.RETURN_GOODS.getType());
        List<SaleOrder> reissueOrders = saleTrade.buildOrderByGoodsType(SaleConstants.AFTER_SALE_REISSUE_ORDER_TYPE);
        //v2版本创建售后工单 目前支考虑(退 退+换 补)三种场景
        if (CollectionUtils.isNotEmpty(retOrders) || CollectionUtils.isNotEmpty(reissueOrders)) {    //发送事件通知售后生成售后工单
            //批发收银v2版本开单创建售后单时传递开单备注给售后
            String afterSaleRemark = buildSysMemo(staff, saleTrade);
            Long workOrderId = afsCustomer.createWorkOrder(staff, saleTrade, trade, afterSaleRemark);
            saleTrade.setWorkOrderId(workOrderId);
        }
        salePaymentBusiness.calculateSettlePayment(staff, saleTrade, trade, settleDto.getOnlySettleType());
        cleanUniqueCodes(saleTrade);    //结账时清空销货单保存的唯一码
        save(staff, saleTrade);
        log.info("销货单结账完成！id: {}, sid: {}", saleTrade.getId(), saleTrade.getSid());
        if (settleDto.isDirect()) {   //直接开单发送微信通知
            dmsConsumer.pushWxCart(staff, Lists.newArrayList(saleTrade), null, 1);
        }
        return Result.isOk(trade);
    }

    @Transactional
    public void updatePicMemo(Staff staff, TradePictureMemoUpdateParams param) {
        Pagination<SaleTrade> page = queryByPage(staff, SaleTradeQueryParam.builder().ids(Collections.singletonList(param.getSaleTradeId())).build());
        if (page.isEmpty()) {
            log.info("根据销货单号:{}查询结果为空，直接返回", param.getSaleTradeId());
            return;
        }
        if (page.getList().get(0).getSid() == null) {
            log.info("根据销货单号:{}查询到的系统订单号为空，直接返回", param.getSaleTradeId());
            return;
        }
        SaleTrade originTrade = page.getList().get(0);
        boolean doUpdateFlag = false;
        //目标是清空图片备注
        if (CollectionUtils.isEmpty(param.getTradePictureMemoUris())) {
            if (originTrade.getExtInfo() == null || CollectionUtils.isEmpty(originTrade.getExtInfo().getFileRemarks())) {//原数据也没有图片备注,不处理
                return;
            }
            doUpdateFlag = true;
        } else {
            //目标是更新图片备注,对比原始图片备注,有变化就更新
            List<String> originPicList = new ArrayList<>();//获取原始图片备注数据
            if (originTrade.getExtInfo() != null && CollectionUtils.isNotEmpty(originTrade.getExtInfo().getFileRemarks())) {
                originPicList = originTrade.getExtInfo().getFileRemarks();
            }
            if (!JSONObject.toJSONString(originPicList).equals(JSONObject.toJSONString(param.getTradePictureMemoUris()))) {
                doUpdateFlag = true;
            }
        }
        if (doUpdateFlag) {
            TradeExtInfo tradeExtInfoNew = new TradeExtInfo();
            tradeExtInfoNew.setFileRemarks(param.getTradePictureMemoUris());
            SaleTrade updateTrade = new SaleTrade();
            updateTrade.setId(originTrade.getId());
            updateTrade.setExtInfo(tradeExtInfoNew);
            param.setSid(originTrade.getSid());
            saleTradeRepo.update(staff, updateTrade);//更新销货单的图片备注
            tradePictureMemoService.update(staff, param);//更新系统订单的图片备注
        }
    }

    @Override
    public Result fillCustomer(Staff staff, List<SaleTrade> saleTrades) {
        List<Long> customerIds = saleTrades.stream().map(e -> e.getCustomer().getCustomerId()).collect(Collectors.toList());
        Result<List<Customer>> result = queryCustomer(staff, customerIds);
        if (!result.isSucc()) {
            return result;
        }
        Map<Long, Customer> customerMap = result.getData().stream().collect(Collectors.toMap(Customer::getCustomerId, Function.identity()));
        for (SaleTrade saleTrade : saleTrades) {
            Customer customer = customerMap.get(saleTrade.getCustomer().getCustomerId());
            if (customer == null) {
                continue;
            }
            Customer cn = saleTrade.getCustomer();
            cn.setCustomerName(customer.getCustomerName());
            cn.setAliasName(customer.getAliasName());
            cn.setBuyerNick(customer.getBuyerNick());
            if (cn.getSalesmanId() == null) {   //没有业务员时默认业务员为分销业务员
                cn.setSalesmanId(customer.getSalesmanId());
            }
            cn.setRemark(customer.getRemark());
            cn.setShowState(customer.getShowState());
            cn.setTotalBalance(customer.getTotalBalance());
            cn.setBalance(customer.getBalance());
            cn.setCredit(customer.getCredit());
            cn.setOpenLock(customer.isOpenLock());
            cn.setNotConsignPayment(customer.getNotConsignPayment());
            cn.setActualBalance(customer.getActualBalance());
            cn.setShowBalance(customer.getShowBalance());
        }
        return result;
    }

    @Override
    public Result fillDebtInfo(Staff staff, List<SaleTrade> saleTrades) {
        try {
            Map<Long, CustomerInfoDto> debtMap = dmsConsumer.queryDebtInfos(staff, saleTrades).assertTrue().getData()
                    .stream().collect(Collectors.toMap(CustomerInfoDto::getSaleTradeId, Function.identity()));
            for (SaleTrade saleTrade : saleTrades) {
                CustomerInfoDto debt = debtMap.get(saleTrade.getId());
                if (debt == null) {
                    continue;
                }
                Customer customer = saleTrade.getCustomer();
                customer.setLastTimeTotalDebt(debt.getLastTotalDebt());
                customer.setCurrentTotalDebt(debt.getCurrentTotalDebt());
                customer.setTotalDebt(debt.getTotalDebt());
                customer.setNowDebt(debt.getNowDebt());
            }
            return Result.isOk();
        } catch (Exception e) {
            log.error("查询分销欠款信息失败: {}", e.getMessage(), e);
            return Result.isFail(ResultCode.DMS_SYS_ERROR, "查询分销欠款信息失败: " + e.getMessage());
        }
    }

    @Override
    public Result delete(Staff staff, SaleTrade saleTrade) {
        if (saleTrade.getSid() != null) {   //订单作废
            tradeConsumer.cancelTrade(staff, saleTrade.getSid()).assertTrue("对应的订单超过三个月已归档，如仍需删除请联系客服处理！");
        }
        if (saleTrade.getWorkOrderId() != null) {   //售后工单作废
            afsCustomer.cancelWorkOrder(staff, saleTrade.getWorkOrderId()).assertTrue();
        }
        //判断销货单支付方式是否为线上支付并且未完成，这种情况要作废线上支付
        salePaymentBusiness.cancelOnlineRecord(staff, saleTrade, "销货单删除");
        saleTradeRepo.delete(staff, saleTrade.getId(), 1, saleTrade.getPaymentGather().getPayStatus());
        tradeTraceService.addTradeTrace(staff, SaleLogUtils.create(staff, saleTrade.getId(), SaleOpEnums.DELETE, null));
        return Result.isOk();
    }

    @Transactional
    public Result<SaleTrade> update(Staff staff, SaleTrade saleTrade, SaleTrade origin, SaleSettleDto settleDto) {
        PaymentGather paymentGather = saleTrade.initPaymentGather(1);
        List<SalePayment> recordPays = paymentGather.findRecordPay(0, 9);
        if (CollectionUtils.isNotEmpty(recordPays) && recordPays.stream().anyMatch(e -> e.getPayFee().signum() <= 0)) {
            return Result.isFail(ResultCode.ONLINE_PAY_FEE_ERROR);
        }
        List<SaleOrder> orders = saleTrade.getOrders();
        itemConsumer.fillOrderInfo(staff, orders);

        if (!Objects.equals(saleTrade.getOrigin(), "PC")) { //非PC请求不能修改售后工单, 取原有退货商品
            List<SaleOrder> retOrders = origin.buildOrderByType(SaleConstants.RET_ORDER_TYPE);
            orders.removeIf(e -> e.getType() == 2);
            orders.addAll(retOrders);
            saleTrade.setDealOrder(SaleConstants.DEAL_ORDER_CONSIGN);
        } else {
            saleTrade.setDealOrder(SaleConstants.DEAL_ORDER_COVER);
        }
        saleTrade.initOrderInfo();  //重新计算orderInfo属性
        saleTrade.getOrderInfo().setAllocateItemNum(saleTrade.getOrderInfo().getNum());
        saleTrade.setWorkOrderId(origin.getWorkOrderId());

        settleDto.setPayType(saleTrade.getPayType());
        settleDto.setSysMemo(buildSysMemo(staff, saleTrade));
        settleDto.setTradeTraces(Lists.newArrayList(saleTrade.getOrigin() + ", 更新销货单金额: " + origin.getOrderInfo().getPayment() + "->" + saleTrade.getOrderInfo().getPayment()));
        //防止修改图片链接的时候把共享链接清除
        saleTrade.getExtInfo().setShareUrl(origin.getExtInfo().getShareUrl());

        //1.修改订单，后续流水处理失败方便事物回滚
        Trade trade = saleTradeFactory.buildTrade(staff, saleTrade);
        Trade oriTrade = saleTrade.getSid() == null ? null : origin.getTrade().getTrades().get(0);
        stallsTradeBusiness.updateTrade(staff, oriTrade, trade, settleDto);
        if (trade != null) {
            saleTrade.setSid(trade.getSid());
            saleTrade.setTradeShortId(trade.getShortId());
        } else if (!paymentGather.findRecordPay(1, null).isEmpty()) {
            return Result.isFail(ResultCode.EXTRA_VERIFY_FAIL, "仅退货不支持收订金，请修改后重试");
        }
        //2.修改售后工单 (优先修改售后单据信息 防止报错生成了不可逆的分销流水)
        saleTrade.setWorkOrderId(afsCustomer.updateWorkOrder(staff, saleTrade, origin, trade, settleDto));
        //3.处理支付状态和分销流水，其结果关系到销货单的状态
        salePaymentBusiness.calculateUpdatePayment(staff, saleTrade, origin, settleDto.getCancelPaymentIds());
        //4.保存销货单信息并添加日志
        save(staff, saleTrade);
        tradeTraceService.addTradeTrace(staff, SaleLogUtils.create(staff, saleTrade.getId(), SaleOpEnums.UPDATE, String.join(";", settleDto.getTradeTraces())));
        //发送事件处理销货单发货退货状态
        eventCenter.fireEvent(this, new EventInfo(SaleListenerEnums.SALE_TRADE_STATUS_CHANGE).setArgs(new Object[]{staff.getId(), saleTrade.getId()}), null);
        return Result.isOk(saleTrade);
    }

    public Result<List<Customer>> queryCustomer(Staff staff, List<Long> customerIds) {
        final Result<List<Customer>> result = dmsConsumer.queryDmsInfos(staff, customerIds);
        if (!result.isSucc()) {
            return result;
        }
        boolean openLock = saleConfigService.get(staff).isOpen(SaleConfigEnums.BALANCE_SUBTRACT_TRADE);
        result.getData().forEach(e -> {
            e.setOpenLock(openLock);
            e.setShowBalance(openLock ? e.getTotalBalance() : e.getTotalBalance().add(e.getNotConsignPayment()));
        });
        return result;
    }

    public void createByTrade(Staff staff, List<Trade> trades, int type) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        List<Long> sids = TradeUtils.toSidList(trades);
        Pagination<SaleTrade> page = queryByPage(staff, SaleTradeQueryParam.builder().stVersion(2).sids(sids).build());
        Map<Long, SaleTrade> tradeMap = page.getList().stream().collect(Collectors.toMap(SaleTrade::getSid, Function.identity()));
        List<SaleTrade> saleTrades = trades.stream().map(e -> saleTradeFactory.formatTrade(staff, e)).collect(Collectors.toList());
        for (SaleTrade saleTrade : saleTrades) {
            SaleTrade origin = tradeMap.get(saleTrade.getSid());
            if (origin != null) {   //销货单已存在，设置id
                saleTrade.setId(origin.getId());
            } else if (type != 1) { //原销货单为空且不需要插入新的销货单
                continue;
            }
            saleTrade.setPrinted(0);
            saleTrade.setStatus(SaleTradeStatusEnum.SETTLED.getSaleOrderStatus());
            saleTrade.setDealOrder(1);  //订单生成的销货单覆盖商品信息
            saleTrade.initOrderInfo();
            save(staff, saleTrade);
        }
        eventCenter.fireEvent(this, new EventInfo(SaleListenerEnums.SALE_DMS_LOCK_BALANCE).setArgs(new Object[]{staff, OpEnum.TRADE_ADD, sids}), null);
    }

    public void consignCancel(Staff staff, List<Trade> trades) {
        List<Trade> dangkous = trades.stream().filter(Trade::isDangkouTrade).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dangkous)) {
            return;
        }
        List<Long> sids = dangkous.stream().map(Trade::getSid).collect(Collectors.toList());
        //删除应收单据
        fmsConsumer.deleteReceivableOrder(staff, sids);
        //撤销发货作废分销流水 (后续生成锁定金额将会自动作废分销流水, 所以这段代码后续可以取消)
        dmsNotice.cancelCashFlowRecord(staff, dangkous);

        //更新销货单订单发货状态
        List<Trade> allTrades = tradeConsumer.queryAllTrades(staff, sids, false).stream().filter(Trade::isDangkouTrade).collect(Collectors.toList());
        List<Long> allSids = allTrades.stream().map(e -> e.getSplitSid() > 0 ? e.getSplitSid() : e.getSid()).distinct().collect(Collectors.toList());
        Pagination<SaleTrade> page = queryByPage(staff, SaleTradeQueryParam.builder().sids(allSids).stVersion(2).build());
        if (page.isEmpty()) {
            return;
        }
        Map<Long, List<Trade>> tradeMap = allTrades.stream().collect(Collectors.groupingBy(e -> e.getSplitSid() > 0 ? e.getSplitSid() : e.getSid()));
        for (SaleTrade saleTrade : page.getList()) {
            List<Trade> list = tradeMap.get(saleTrade.getSid());
            TradeInfo retry = saleTrade.getTrade().retry(saleTrade.getStatus(), list);
            SaleTrade upd = new SaleTrade();
            upd.setId(saleTrade.getId());
            upd.setTrade(retry);
            save(staff, upd);
        }
        eventCenter.fireEvent(this, new EventInfo(SaleListenerEnums.SALE_DMS_LOCK_BALANCE).setArgs(new Object[]{staff, OpEnum.CONSIGN_CANCEL, sids}), null);
    }

    /**
     * v2订单备注按配置构建
     * @param staff
     * @param saleTrade
     * @return
     */
    public String buildSysMemo(Staff staff, SaleTrade saleTrade) {
        final String remark = saleTrade.getRemark();
        final Customer customer = saleTrade.getCustomer();
        final String customerRemark = customer.getRemark();

        SaleConfig saleConfig = saleConfigService.get(staff);
        StringBuilder waveRemarkSb = new StringBuilder();
        if (saleConfig.getIntegerVal(SaleConfigEnums.SYNC_WAVE_REMARK) == 1) {
            if (saleConfig.getIntegerVal(SaleConfigEnums.SYNC_DISTRIBUTOR_NAME) == 1) {
                waveRemarkSb.append("分:").append(customer.getCustomerName()).append(";");
            }
            if (saleConfig.getIntegerVal(SaleConfigEnums.SYNC_TRADE_REMARK) == 1 && StringUtils.isNotBlank(remark)) {
                waveRemarkSb.append("开注:").append(remark).append(";");
            }
            if (saleConfig.getIntegerVal(SaleConfigEnums.SYNC_DISTRIBUTOR_REMARK) == 1 && StringUtils.isNotBlank(customerRemark)) {
                waveRemarkSb.append("分注:").append(customerRemark).append(";");
            }
        }
        return waveRemarkSb.toString();
    }
}
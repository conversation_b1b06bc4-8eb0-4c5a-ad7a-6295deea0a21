package com.raycloud.dmj.business.logistics;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.*;
import com.google.api.client.util.Sets;
import com.google.common.collect.*;
import com.raycloud.dmj.*;
import com.raycloud.dmj.base.DevLogBuilder;
import com.raycloud.dmj.business.buyout.DeductRevenueBusiness;
import com.raycloud.dmj.business.common.*;
import com.raycloud.dmj.business.fx.*;
import com.raycloud.dmj.business.modify.TradeCalculateTheoryPostFeeBusiness;
import com.raycloud.dmj.business.operate.TradeUpdateSellerMemoFlagBusiness;
import com.raycloud.dmj.business.trade.TradeFilterBusiness;
import com.raycloud.dmj.dao.trade.ConsignRecordDao;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.consign.*;
import com.raycloud.dmj.domain.constant.*;
import com.raycloud.dmj.domain.customer.basis.CmCustomerBalanceVO;
import com.raycloud.dmj.domain.enums.*;
import com.raycloud.dmj.domain.trade.config.*;
import com.raycloud.dmj.domain.trade.config.entity.UploadSellerMemoWhenConsigningSplit;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.memo.*;
import com.raycloud.dmj.domain.trade.utils.PlatformUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.fx.util.FxLogBuilder;
import com.raycloud.dmj.domain.trades.payment.util.LogBusinessEnum;
import com.raycloud.dmj.domain.trades.payment.util.MathUtils;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.domain.utils.ListUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.print.api.base.ITradePtService;
import com.raycloud.dmj.services.ILockService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.basis.IWarehouseService;
import com.raycloud.dmj.services.customer.basis.CmCustomerDubboService;
import com.raycloud.dmj.services.tag.TradeTagService;
import com.raycloud.dmj.services.trade.consign.TradeConsignUpdate;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.config.TradeConfigNewService;
import com.raycloud.dmj.services.trades.support.OfflineCombineParcelServiceImpl;
import com.raycloud.dmj.services.utils.*;
import com.raycloud.dmj.utils.TradeLogisticsTrackingUtils;
import com.raycloud.dmj.waybill.common.context.PtWaybillPathContext;
import com.raycloud.dmj.waybill.common.params.PathParam;
import com.raycloud.dmj.web.source.*;
import com.raycloud.ec.api.*;
import com.raycloud.erp.buffer.model.BufferRequest;
import com.raycloud.erp.buffer.service.IBufferService;
import com.raycloud.erp.db.router.DbContextHolder;
import org.apache.commons.collections.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.*;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.waybill.common.enums.UploadPathEnum.*;

/**
 * 系统发货业务类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2017-06-08 19:09
 */
@Service
public class ConsignBusiness {

    @Resource
    ITradeUpdateService tradeUpdateService;

    @Resource
    ITradePtService tradePtService;

    @Resource
    StaffAssembleBusiness staffAssembleBusiness;

    @Resource
    private TradeUpdateSellerMemoFlagBusiness tradeUpdateSellerMemoFlagBusiness;

    @Resource
    ConsignRecordDao consignRecordDao;
    @Resource
    DeductRevenueBusiness deductRevenueBusiness;
    @Resource
    ITradeConfigService tradeConfigService;
    @Resource
    IWarehouseService warehouseService;
    @Resource
    IEventCenter eventCenter;
    @Resource
    IBufferService bufferService;
    @Resource
    IConsignUploadService consignUploadService;

    @Resource
    private IStaffService staffService;
    @Resource
    private UploadRecordBusiness uploadRecordBusiness;

    @Resource
    private TradeConfigNewService tradeConfigNewService;
    @Resource
    private FxBusiness fxBusiness;
    @Resource
    FxUploadBusiness fxUploadBusiness;
    @Resource
    TradePerformanceOptLogService tradePerformanceOptLogService;
    @Resource
    TradeCalculateTheoryPostFeeBusiness tradeCalculateTheoryPostFeeBusiness;
    @Resource
    TradeFilterBusiness tradeFilterBusiness;

    @Resource
    SysOrderStatusMappingBusiness sysOrderStatusMappingBusiness;
    @Resource
    LogisticsWarningBusiness logisticsWarningBusiness;

    @Resource
    private CmCustomerDubboService cmCustomerDubboService;

    @Resource(name = "solrTradeSearchService")
    private ITradeSearchService tradeSearchService;
    @Resource
    UploadPackagesNoticeBusiness uploadPackagesNoticeBusiness;
    @Resource
    TradeLockBusiness tradeLockBusiness;
    @Resource
    ILockService lockService;
    @Resource
    PlatformTransactionManager transactionManager;
    @Resource
    TradeLocalConfigurable tradeLocalConfig;
    @Resource
    QiMenFxCashFlowBusiness qiMenFxCashFlowBusiness;
    @Resource
    OfflineCombineParcelServiceImpl offlineCombineParcelService;

    @Resource
    IExpressCompanyService expressCompanyService;
    @Resource
    TradeTagService tradeTagService;

    @Resource
    TradeConsignUpdate tradeConsignUpdate;

    private static final Logger logger = Logger.getLogger(ConsignBusiness.class);

    public List<ConsignRecord> consign(Staff staff, List<Trade> trades, ConsignHandler handler, String clientIp) {
        return consign(staff, trades, handler, clientIp, false);
    }

    /**
     * 默认走供销发货然后再分销发货
     * <p>
     * 新增的流程
     * 分销发货 --> 供销且分销发货 --> 供销发货
     * 非供分销发货
     *
     * @param staff
     * @param trades
     * @param handler
     * @param clientIp
     * @param isFilterPartyWarehouse
     * @return
     */
    public List<ConsignRecord> consign(Staff staff, List<Trade> trades, ConsignHandler handler, String clientIp, boolean isFilterPartyWarehouse) {

        if (trades.isEmpty()) {
            logger.debug(LogHelper.buildLog(staff, String.format("[trades=%s]", trades)));
            return null;
        }

        List<Trade> gxTrades = trades.stream().filter(TradeUtils::isGxTrade).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(gxTrades)) {
            return doConsign(staff, trades, handler, clientIp, isFilterPartyWarehouse);
        }

        if (!tradeLocalConfig.isTradeGxConsignStartWithFx(staff.getCompanyId())) {
            return doConsign(staff, trades, handler, clientIp, isFilterPartyWarehouse);
        }

        if (CollectionUtils.isNotEmpty(gxTrades)) {
            logger.debug(LogHelper.buildLog(staff, String.format("供销发货从分销开始触发,供销sid=%s", TradeUtils.toSidList(gxTrades))));
        }

        handler.ifGxConsignStartWithFx = true;
        List<ConsignRecord> normalConsignRecordes = doConsign(staff, trades.stream().filter(t -> !TradeUtils.isGxTrade(t)).collect(Collectors.toList()), handler, clientIp, isFilterPartyWarehouse);

        try {
            MDC.put(ConsignUtils.IF_GX_CONSIGN_START_WITH_FX, 1);
            Map<String, Trade> fxTradeMap = getFxTradeMap(gxTrades);

            List<Trade> fxTrades = Lists.newArrayListWithExpectedSize(8);
            List<Trade> fxAndGxTrades = Lists.newArrayListWithExpectedSize(8);

            if (MapUtils.isNotEmpty(fxTradeMap)) {
                fxTradeMap.values().forEach(t -> {
                    if (TradeUtils.isFxTrade(t)) {
                        fxTrades.add(t);
                    } else if (TradeUtils.isGxAndFxTrade(t)) {
                        fxAndGxTrades.add(t);
                    }
                });
            }
            // 发货失败的订单会打上异常。暂时不控制上一级的发货成功下一级才接着发货，不然供销感知不到发货失败
            List<ConsignRecord> consignRecordes = Lists.newArrayListWithExpectedSize(8);
            fxDoConsign(staff, fxTrades, handler, clientIp, isFilterPartyWarehouse, consignRecordes);
            fxDoConsign(staff, fxAndGxTrades, handler, clientIp, isFilterPartyWarehouse, consignRecordes);
            normalConsignRecordes = ListUtils.union(normalConsignRecordes, doConsign(staff, gxTrades, handler, clientIp, isFilterPartyWarehouse));
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "供销发货异常"), e);
            throw new RuntimeException(e);
        } finally {
            MDC.remove(ConsignUtils.IF_GX_CONSIGN_START_WITH_FX);
        }

        return normalConsignRecordes;
    }

    /**
     * 上一级的发货成功下一级才接着发货
     *
     * @param trades          下一级的订单
     * @param consignRecordes 上一级的发货结果
     * @return
     */
    private List<Trade> getNeedConsignTrades(List<Trade> trades, List<ConsignRecord> consignRecordes) {

        if (CollectionUtils.isEmpty(trades) || CollectionUtils.isEmpty(consignRecordes)) {
            consignRecordes.clear();
            return Collections.emptyList();
        }
        List<String> tids = consignRecordes.stream().filter(c -> Objects.equals(0, c.getIsError())).map(c -> String.valueOf(c.getSid())).collect(Collectors.toList());
        consignRecordes.clear();
        return trades.stream().filter(t -> tids.contains(t.getTid())).collect(Collectors.toList());
    }

    /**
     * 新的事物里面处理：分销和供销且分销发货
     *
     * @param staff
     * @param trades
     * @param handler
     * @param clientIp
     * @param isFilterPartyWarehouse
     * @param consignRecordes
     */
    public void fxDoConsign(Staff staff, List<Trade> trades, ConsignHandler handler, String clientIp, boolean isFilterPartyWarehouse, List<ConsignRecord> consignRecordes) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }
        trades.stream().collect(Collectors.groupingBy(Trade::getCompanyId)).forEach((companyId, trades1) -> {
            Staff currentStaff = staffService.queryFullByCompanyId(companyId);
            if (currentStaff == null || CollectionUtils.isEmpty(trades1)) {
                return;
            }
            DbContextHolder.set(currentStaff.getDbKey());
            TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_REQUIRES_NEW));
            try {
                lockService.locks(tradeLockBusiness.getERPLocks(currentStaff, TradeUtils.toSids(trades1)), () -> {
                    ListUtils.union(consignRecordes, doConsign(currentStaff, tradeSearchService.queryBySidsContainMergeTrade(currentStaff, true, true, TradeUtils.toSids(trades1)), handler, clientIp, isFilterPartyWarehouse));
                    return null;
                });
                transactionManager.commit(transactionStatus);
            } catch (Exception e) {
                transactionManager.rollback(transactionStatus);
                logger.error(new FxLogBuilder(currentStaff, FxLogBuilder.ROLE_FX).append("分销发货失败").toString(), e);
                throw new RuntimeException("分销发货失败：" + e.getMessage());
            } finally {
                DbContextHolder.set(staff.getDbKey());
            }
        });
    }

    public List<ConsignRecord> doConsign(Staff staff, List<Trade> trades, ConsignHandler handler, String clientIp, boolean isFilterPartyWarehouse) {
        logger.debug(LogHelper.buildLog(staff, String.format("[trades=%s]", trades)));

        if (trades.isEmpty()) {
            return null;
        }
        fullParcelOutSid(trades, handler);
        if (isFilterPartyWarehouse) {
            logger.debug(LogHelper.buildLogHead(staff).append(String.format("consign准备发货的订单列表sids=%s", TradeUtils.toSidList(trades))));
            trades = TradeWmsUtils.filterParty3Warehouse(staff, trades, warehouseService, true);
            if (CollectionUtils.isEmpty(trades)) {
                throw new IllegalArgumentException("订单发货仓库属于第三方仓，不支持无需物流发货/打印发货/平台上传发货/直接发货/仅发货");
            }
        }
        //发货过滤风控订单
        Set<Trade> setRiskTrade = ConsignUtils.filterRiskTrade(staff, trades);
        if (CollectionUtils.isNotEmpty(setRiskTrade)) {
            List<Trade> riskTrades = new ArrayList<>(setRiskTrade);
            for (List<Trade> subTrades : Lists.partition(riskTrades, 200)) {
                eventCenter.fireEvent(this, new EventInfo("trade.risk.consign").setArgs(new Object[]{staff}), Lists.newArrayList(subTrades));
            }
        }
        if (CollectionUtils.isEmpty(trades)) {
            return null;
        }
        //过滤平台分销订单
        trades = tradeFilterBusiness.filterPlatformFxTrades(trades, true);

        if (handler.getConsignType() == SendType.DUMMY) {//无需物流发货不支持拆单
            filterSplit(trades, handler.getDymmyType());
//            tradeFilterBusiness.filterShopeeTrades(trades, true);
        }
        long start = System.currentTimeMillis();
        //如果是分销订单填充对应的供销模版信息
        fxUploadBusiness.fillGxExpress(staff, trades, false);
        handler.sidUploadRecordsMap = Maps.newHashMap();
        ConsignData consignData = filter(staff, trades, handler);
        validateMergeTradeConsign(staff, consignData);

        //先系统发货
        if (!consignData.trades2Consigned.isEmpty()) {
            try {
                //重新计算订单成本价、体积、净重
                calculateCost(staff, trades, consignData.updateTrades);
            } catch (Exception e) {
                logger.error(LogHelper.buildLog(staff, "计算订单成本价、体积、净重，不应阻断发货主流程" + e.getMessage()), e);
            }
            //更新订单系统状态，标记为已发货或已完成
            List<Trade> fxTrades = consignData.updateTrades.stream().filter(t->t.hasOpV(OpVEnum.TRADE_FX)).collect(Collectors.toList());
            //前面已分组，这里还是判断一下，有分销单，要去取供销的单号发货。
            if (CollectionUtils.isNotEmpty(fxTrades)) {
                tradePtService.saveByTrades(staff, fxTrades);
            }
//            tradeUpdateService.updateTrades(staff, consignData.updateTrades, consignData.updateOrders);
            tradeConsignUpdate.update(staff, consignData);
//            logisticsSubscribe(staff, consignData.trades2Consigned);
            // 更新物流推送信息
            logisticsWarningBusiness.syncConsignStatusEvent(staff, consignData.trades2Consigned);
            //分销订单发货订阅物流预警事件
            logisticsWarningBusiness.fxSyncConsignStatusEvent(staff, consignData.trades2Consigned);
            //增加发货记录
            consignRecordDao.batchInsert(staff, consignData.consignRecords);
            //更新按单付费用户可用订单数
            deductRevenueBusiness.asyncDeduct(staff, consignData.deductNum);//扣减与系统发货放在一个事务中，保证扣减失败的话，发货回滚

            // 系统发货更新运单生命周期记录
            sendWaybillCycle(staff, consignData.updateTrades);
        }
        //分销订单不在系统发货的时候触发上传
        //1：如果是预发货，分销订单已经上传过了，这里不再上传
        //2：如果不是预发货，系统发货后上传的，其实供销订单系统发货后，会自己上传从而触发对应的分销订单上传，供销订单系统发货触发分销订单的系统发货时就不用再次上传了
        //3：供销订单虽然没有平台，但是上传流程不可以跳过--上传标识、上传记录表都需要记录
        List<Trade> needUploadTrades = consignData.trades2Upload.stream().filter(trade -> !TradeUtils.isFxOrMixTrade(trade)&&!PlatformUtils.isQTGTrades(trade)).collect(Collectors.toList());
        if (!consignData.trades2ReUpload.isEmpty()) {
            needUploadTrades.addAll(consignData.trades2ReUpload);
        }
        needUploadTrades = handleBtasTradeUpload(staff, handler, needUploadTrades, consignData);
        if (StringUtils.isNotBlank(handler.getOperateType()) && "1".equalsIgnoreCase(handler.getOperateType())) {
            //只系统发货不走erp上传平台(开放平台使用 拼吖-拼多多使用)
        } else {    //走erp系统上传平台(系统发货兜底上传平台)
            consignUploadService.asyncUpload(staff, needUploadTrades.stream().map(Trade::getSid).collect(Collectors.toList()), handler.getConsignType(), clientIp, handler.getDymmyType(), handler.getNoLogisticsName(), handler.getNoLogisticsTel());
        }

        //记录发货日志，供kibana统计使用
        logConsign(staff, consignData.result, System.currentTimeMillis() - start);

        if (!consignData.trades2Consigned.isEmpty()) {
            if (handler.getConsignType() == SendType.RESEND) {
                tradePerformanceOptLogService.addAuditOptLog(staff, consignData.trades2Consigned, TradePerformanceOptLog.Re_Consign_Type);
            } else {
                tradePerformanceOptLogService.addAuditOptLog(staff, consignData.trades2Consigned, TradePerformanceOptLog.Consign_Type);
            }
        }
        consignedAfter(staff, handler, consignData, clientIp, trades, needUploadTrades);
        return consignData.result;
    }

    private List<Trade> handleBtasTradeUpload(Staff staff, ConsignHandler handler, List<Trade> needUploadTrades, ConsignData consignData) {
        try {
            //btas组包不需要上传。WMS的BTAS组包，支持上传
            if (null != handler.getTradeCombineParcel() && !handler.getTradeCombineParcel().isWmsBtasParcel()) {
                return new ArrayList<>();
            }
            //这里进入的BTAS订单：直接发货的BTAS订单，WMS组包触发的OMS BTAS订单
            List<Trade> btasTrades = needUploadTrades.stream().filter(TradeTypeUtils::isFxgBtasTrade).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(btasTrades)) {
                return needUploadTrades;
            }
            //排除掉BTAS订单(WMS触发的BTAS订单，直接走的上传发货)
            List<Trade> normalTrade = needUploadTrades.stream().filter(t -> !TradeTypeUtils.isFxgBtasTrade(t)).collect(Collectors.toList());
            //同步执行BTAS订单的特殊上传（由WMS触发，一单一批）
            int type = SendType.DUMMY == handler.consignType ? 2 : 1;
            Map<Long, BtasUploadResult> resultMap = offlineCombineParcelService.uploadOMSBtasTrackingNo(staff, btasTrades.stream().filter(ConsignUtils::btasCheckTrade).collect(Collectors.toList()), type);
            if (MapUtils.isEmpty(resultMap)) {
                return normalTrade;
            }
            Map<Long, ConsignRecord> recordMap = consignData.result.stream().collect(Collectors.toMap(ConsignRecord::getSid, Function.identity(), (a, b) -> b));
            resultMap.forEach((k, v) -> {
                ConsignRecord record = recordMap.get(k);
                if (Objects.isNull(record) || v.isSuccess()) {
                    return;
                }
                //记录上传错误信息
                record.setIsError(1);
                record.setIsUploadError(1);
                record.setErrorDesc(v.getContent());
            });
            return normalTrade;
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "OMS BTAS订单上传异常"), e);
        }
        return needUploadTrades;
    }

    private void fullParcelOutSid(List<Trade> trades, ConsignHandler handler) {
        if (null != handler.getTradeCombineParcel() && TradeCombineParcel.EnumGatherType.YOUJI.getType().equals(handler.getTradeCombineParcel().getGatherType()) && CollectionUtils.isNotEmpty(trades)) {
            trades.forEach(t -> t.setOutSid(handler.getTradeCombineParcel().getTrackingNo()));
        }
    }

    // KMERP-133419: 发货时需要将合单看成一个整体进行发货
    private void validateMergeTradeConsign(Staff staff, ConsignData consignData) {
        if (CollectionUtils.isEmpty(consignData.trades2Consigned)) {
            return;
        }
        Map<Long/*mergeSid*/, Set<Long>/*sid*/> mergeTradeMap = Maps.newHashMap();
        consignData.trades2Consigned.forEach(trade -> {
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                mergeTradeMap.computeIfAbsent(trade.getMergeSid(), k -> Sets.newHashSet()).add(trade.getSid());
            }
        });
        if (MapUtils.isEmpty(mergeTradeMap)) {
            return;
        }

        // 合单发货，存在订单缺失的数据
        Map<Long/*mergeSid*/, Set<Long>/*sid*/> tradeMissMap = Maps.newHashMap();
        List<Trade> trades = tradeSearchService.queryBySidsContainMergeTrade(staff, false, mergeTradeMap.keySet().toArray(new Long[]{}));
        trades.forEach(trade -> {
            if (!mergeTradeMap.get(trade.getMergeSid()).contains(trade.getSid())) {
                tradeMissMap.put(trade.getMergeSid(), mergeTradeMap.get(trade.getMergeSid()));
            }
        });
        if (MapUtils.isEmpty(tradeMissMap)) {
            return;
        }

        logger.info(LogHelper.buildLog(staff, String.format("合单发货，存在缺失订单：%s", JSONObject.toJSONString(tradeMissMap))));
        Set<Long> tradeMissSids = tradeMissMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        restoreConsignData(staff, consignData, tradeMissSids);
    }

    private void restoreConsignData(Staff staff, ConsignData consignData, Set<Long/*sid*/> sidList) {
        ConsignRecord consignRecord = consignData.consignRecords.get(0);
        SendType sendType = SendType.valueOf(consignRecord.getConsignType());
        Date consigned = consignRecord.getConsigned();

        // 还原 deductNum, 移除 trades2Consigned, trades2Upload, consignRecords, result, updateTrades, updateOrders
        List<Trade> removeTrade = Lists.newArrayList();
        Iterator<Trade> iterator = consignData.trades2Consigned.iterator();
        while (iterator.hasNext()) {
            Trade trade = iterator.next();
            if (!sidList.contains(trade.getSid())) {
                continue;
            }
            if (consignData.deductTradeSids.contains(trade.getSid())) {
                consignData.deductNum--;
            }
            removeTrade.add(trade);
            iterator.remove();
        }
        consignData.trades2Upload.removeIf(trade -> sidList.contains(trade.getSid()));
        consignData.consignRecords.removeIf(record -> sidList.contains(record.getSid()));
        consignData.result.removeIf(record -> sidList.contains(record.getSid()));
        consignData.updateTrades.removeIf(trade -> sidList.contains(trade.getSid()));
        consignData.updateOrders.removeIf(order -> sidList.contains(order.getSid()));

        // 添加 result, deliverExecpTrades
        for (Trade trade : removeTrade) {
            ConsignRecord record = ConsignUtils.buildFullRecord(trade, sendType, 1, 1)
                    .setBuyType(staff.getCompany().getOrderType())
                    .setErrorDesc("合单发货校验失败")
                    .setConsigned(consigned)
                    .setMergeSid(trade.getMergeSid());
            consignData.result.add(record);
            consignData.deliverExecpTrades.put(trade.getSid(), "合单发货校验失败");
        }
    }

    /**
     * 系统发货更新运单生命周期记录
     *
     * @param staff
     * @param updateTrades
     */
    private void sendWaybillCycle(Staff staff, List<Trade> updateTrades) {
        if (CollectionUtils.isEmpty(updateTrades)) {
            logger.info(LogHelper.buildLog(staff, "没有系统发货订单，不发送生命周期事件！"));
            return;
        }
        try {
            // 运单生命周期处理
            eventCenter.fireEvent(this, new EventInfo("waybill.cycle.modify").setArgs(new Object[]{staff, ERP_CONSIGN_UPDATE.name(),
                    PtWaybillPathContext.builder()
                            .pathParam(new PathParam(
                                    updateTrades.stream()
                                            .map(Trade::getSid)
                                            .toArray(Long[]::new)
                            ))
                            .waybillPath(ERP_CONSIGN_UPDATE)
                            .build()}), null);
        } catch (Exception e) {
            logger.error(LogHelper.buildLog(staff, "系统发货构建运单生命周期参数报错！错误信息：" + e.getMessage()), e);
        }
    }

    private void consignedAfter(Staff staff, ConsignHandler handler, ConsignData consignData, String clientIp, List<Trade> trades, List<Trade> needUploadTrades) {
        //发货完成之后执行后续操作
        handler.postConsign(staff, consignData, clientIp, handler.getDymmyType(), handler.getNoLogisticsName(), handler.getNoLogisticsTel());

        TradeConfig config = tradeConfigService.get(staff);

        // 虾皮是否开启首公里预报功能  SHOPEE_FIRST_MILE_PUSH_STEP 为0 称重后组包 1发货后组包
        if (config != null
                && TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.SHOPEE_IS_FIRST_MILE_PUSH.getKey()) != null &&
                (int) TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.SHOPEE_IS_FIRST_MILE_PUSH.getKey()) == 1
                && TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.SHOPEE_FIRST_MILE_PUSH_STEP.getKey()) != null &&
                (int) TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.SHOPEE_FIRST_MILE_PUSH_STEP.getKey()) == 1
        ) {
            Long shopeeId = Optional.ofNullable(TradeConfigUtils.parseExtendConfig(config.getChatConfigs()).get(TradeExtendConfigsEnum.SHOPEE_SHOP_TAOBAO_ID.getKey()))
                    .map(String::valueOf)
                    .filter(StringUtils::isNotBlank)
                    .map(Long::valueOf)
                    .orElse(null);
            if (Objects.nonNull(shopeeId)) {
                List<Trade> shopeeTrade = trades.stream().filter(trade -> trade.getSource().equals(CommonConstants.PLAT_FORM_TYPE_SHOPEE)).collect(Collectors.toList());
                for (Trade tr : shopeeTrade) {
                    eventCenter.fireEvent(this, new EventInfo("combineParcel.first.mile.push").setArgs(new Object[]{staff, tr.getSid(), shopeeId}), null);
                }
            }
        }

        //取出订单的系统单号
        List<Long> sidList = obtainPddHandmadeSid(staff, trades);
        if (CollectionUtils.isNotEmpty(sidList)) {
            eventCenter.fireEvent(this, new EventInfo("outsid.information.upload").setArgs(new Object[]{staff, sidList}), null);
        }
        //根据配置处理补发/换货订单，发货后是否发送消息给客服
        handlerChatMessage(staff, consignData.trades2Consigned);
        if (!SendType.DUMMY.equals(handler.getConsignType())) {//虚拟发货不支持上传备注
            handleCopyTradeUploadExpressMsg(staff, consignData.trades2Consigned);
        }
        consumeStock(staff, consignData);
        //更新发货异常,发事件记录发货异常日志
        updateDeliverExecp(staff, consignData.result, consignData.deliverExecpTrades, trades);
        consignFilter(staff, consignData.trades2Consigned, handler, clientIp);

        //根据配置检测拆单订单是否需要调用上传单号接口,预发货的单已经上传过了，不会再次触发上传平台,为了兼容预发货，系统发货时也触发一次多包裹上传
        List<Trade> needPackagesNoticeTrades = consignData.trades2Consigned;
        if (CollectionUtils.isNotEmpty(needUploadTrades)) {
            Set<Long> sidSet = TradeUtils.toSidSet(needUploadTrades);
            // 需要上传的不需要多包裹物流通知，上传链路会自动通知
            needPackagesNoticeTrades = consignData.trades2Consigned.stream().filter(trade -> !sidSet.contains(trade.getSid())).collect(Collectors.toList());
        }
        List<Trade> fxSysTrade = trades.stream().filter(TradeUtils::isFxTrade).filter(t ->CommonConstants.PLAT_FORM_TYPE_SYS.equals(t.getSource())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fxSysTrade)){
            eventCenter.fireEvent(this, new EventInfo("trade.upload.handle.fx.multi.outsid").setArgs(new Object[]{staff, buildUpdateTrade(fxSysTrade), handler.getConsignType()}), null);
        }
        sendO2oAutoPickEvent(staff,trades);
        uploadPackagesNoticeBusiness.asyncPackagesNotice(staff, needPackagesNoticeTrades, consignData.tradeConfig);
    }

    //取出订单的系统单号
    public List<Long> obtainPddHandmadeSid(Staff staff, List<Trade> trades) {
        List<Long> sidList = trades.stream().filter(trade -> TradeUtils.platformMatch(staff, trade, CommonConstants.PLAT_FORM_TYPE_PDD)&&CommonConstants.PLAT_FORM_TYPE_PDD.equals(trade.getSplitSource())).filter(trade -> CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())).filter(trade -> !"reissue".equals(trade.getType())&&!"changeitem".equals(trade.getType())).map(trade -> trade.getSid()).collect(Collectors.toList());
        return sidList;
    }

    private void consignFilter(Staff staff, List<Trade> trades, ConsignHandler handler, String clientIp) {
        List<Trade> gxTrades = trades.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(gxTrades)) {
            return;
        }
        Map<Long, List<Trade>> sourceIdMap = gxTrades.stream().collect(Collectors.groupingBy(Trade::getSourceId));
        //供销订单系统发货后重算运费流水
        List<Trade> gxTrades4CalcCashFlow = gxTrades.stream().filter(TradeUtils::needCalcGxPostFee).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(gxTrades4CalcCashFlow)) {
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_EXPRESS_CASHFLOW).setArgs(new Object[]{staff, TradeUtils.toSids(gxTrades4CalcCashFlow), "consign", true}), null);
        }

        if (handler.ifGxConsignStartWithFx) {
            return;
        }

        for (Map.Entry<Long, List<Trade>> next : sourceIdMap.entrySet()) {
            Long key = next.getKey();
            List<Trade> value = next.getValue();
            Staff fxStaff = staffService.queryFullByCompanyId(key);
            fxStaff.setClueId(IdWorkerFactory.getIdWorker().nextId());
            List<Trade> fxTrades = fxBusiness.getFxTradesByTids(fxStaff, value);
            if (CollectionUtils.isEmpty(fxTrades)) {
                continue;
            }
            List<Long> sids = TradeUtils.toSidList(fxTrades);
            logger.debug(LogHelper.buildLog(staff, String.format("供销触发分销订单系统发货[gxCompanyId=%s,供销sids=%s,分销sids=%s]", staff.getCompanyId(), TradeUtils.toSidList(value), sids)));
            bufferService.buffer(Buffers.build(fxStaff, TradeEvents.TRADE_FX_CONSIGN), sids.stream().map(String::valueOf).collect(Collectors.toList()));
            eventCenter.fireEvent(this, new EventInfo(TradeEvents.TRADE_FX_CONSIGN).setArgs(new Object[]{fxStaff, handler.consignType, clientIp, handler.dymmyType, handler.noLogisticsName, handler.noLogisticsTel}), null);
        }
    }


    private void updateDeliverExecp(Staff staff, List<ConsignRecord> result, Map<Long, String> deliverExecpTrades, List<Trade> trades) {
        List<ConsignRecord> excepRecord = result.stream().filter(c -> c.getIsError() == 1).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(excepRecord)) {
            List<Trade> updates = new ArrayList<>();
            List<Trade> origins = new ArrayList<>();
            Map<Long, Trade> tradeMap = trades.stream().collect(Collectors.toMap(Trade::getSid, t -> t, (k1, k2) -> k2));
            for (ConsignRecord consignRecord : excepRecord) {
                Trade origin = tradeMap.get(consignRecord.getSid());
                //如果该订单已发货则不标记异常
                if (origin != null && !TradeUtils.isAfterSendGoods(origin)) {
                    Trade trade = TradeBuilderUtils.builderUpdateItemExcep(origin);
                    trade.setSid(origin.getSid());
                    trade.setCompanyId(origin.getCompanyId());
                    TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.DELIVER_EXCEPT, 1L);
                    trade.setIsExcep(1);
                    updates.add(trade);
                    origins.add(origin);
                    if (StringUtils.isNotEmpty(consignRecord.getErrorDesc()) && consignRecord.getErrorDesc().contains("异常订单")) {
                        Map<Long, String> tagNameMap = tradeTagService.getAllExceptMap(staff);
                        if (tagNameMap != null && !tagNameMap.isEmpty()) {
                            List<String> exceptNames = TradeExceptUtils.getExceptChineseNames(staff, origin, tagNameMap);
                            if (!exceptNames.isEmpty()) {
                                consignRecord.setErrorDesc(StringUtils.join(exceptNames));
                            }
                        }
                    }
                }
            }
            tradeUpdateService.updateTrades(staff, updates);
            eventCenter.fireEvent(this, new EventInfo("trade.consign.exception").setArgs(new Object[]{staff, deliverExecpTrades}), origins);


        }


    }

    private void consumeStock(Staff staff, ConsignData consignData) {
        List<Long> consumeSids = new ArrayList<>();
        List<Long> resumeSids = new ArrayList<>();
        for (Trade trade : consignData.trades2Consigned) {
            if (trade.isForce() || (trade.getScalping() != null && trade.getScalping() - 1 == 0) || TradeUtils.isFxOrMixTrade(trade)) {
                resumeSids.add(trade.getSid());
            } else {
                consumeSids.add(trade.getSid());
            }
        }
        resumeSids.addAll(consignData.resumeSids);
        resumeSids = resumeSids.stream().distinct().collect(Collectors.toList());
        if (!consumeSids.isEmpty()) {
            BufferRequest req = Buffers.build(staff, TradeEvents.TRADE_STOCK_CONSUME, acquireConsumeArgs());
            bufferService.buffer(req, consumeSids);
            eventCenter.fireEvent(this, new EventInfo(req.getType()).setArgs(new Object[]{staff}), null);
        }
        if (!resumeSids.isEmpty()) {
            BufferRequest req = Buffers.build(staff, TradeEvents.TRADE_STOCK_RESUME);
            bufferService.buffer(req, resumeSids);
            eventCenter.fireEvent(this, new EventInfo(req.getType()).setArgs(new Object[]{staff}), null);
        }
    }

    private String acquireConsumeArgs() {
        OperateSourceHolder operateSource = OperateSourceContext.getOperateSourceNew();
        String msg = JSON.toJSONString(operateSource);
        if (msg.length() >= 1000) {
            logger.info("operateSource.length" + msg.length());
            int page = msg.length() % 500 == 0 ? (msg.length() / 500) : (msg.length() / 500) + 1;
            for (int i = 0; i < page; i++) {
                logger.info("operateSource:" + msg.substring(500 * i, Math.min(500 * (i + 1), msg.length())));
            }
            msg = "";
        }
        return msg;
    }

    /**
     * 根据配置处理补发/换货订单，发货后是否发送消息给客服
     **/
    private void handlerChatMessage(Staff staff, List<Trade> trades) {
        TradeConfig tradeConfig = tradeConfigService.get(staff);
        Integer openChatConfig = tradeConfig.getOpenChatConfig();
        if (openChatConfig == null || openChatConfig == 0) {
            return;
        }
        String chatConfigs = tradeConfig.getChatConfigs();
        if (StringUtils.isNotBlank(chatConfigs)) {
            JSONObject jsonObject = JSONObject.parseObject(chatConfigs);
            String openReissueChat = jsonObject.getString("openReissueChat");
            String openchangeitemChat = jsonObject.getString("openChangeitemChat");
            List<Trade> chatMessageList = new ArrayList<>();
            if (!trades.isEmpty()) {
                trades.forEach(trade -> {
                    //开启了配置前提下
                    //补发订单
                    if ("reissue".equals(trade.getType()) && "1".equals(openReissueChat)) {
                        chatMessageList.add(trade);
                    }
                    //换货订单
                    if ("changeitem".equals(trade.getType()) && "1".equals(openchangeitemChat)) {
                        chatMessageList.add(trade);
                    }
                });
            }
            if (!chatMessageList.isEmpty()) {
                eventCenter.fireEvent(this, new EventInfo("trade.consigned.chatmessages").setArgs(new Object[]{staff}), chatMessageList);
            }
        }
    }

    private boolean openCopyTradeExpressMemo(Staff staff) {
        TradeConfigNew config = tradeConfigNewService.get(staff, TradeConfigEnum.OPEN_COPY_TRADE_EXPRESS_MEMO);
        return Optional.ofNullable(config).map(c -> Objects.equals("1", c.getConfigValue())).orElse(false);
    }

    private void handleCopyTradeUploadExpressMsg(Staff staff, List<Trade> trades) {
        if (!this.openCopyTradeExpressMemo(staff)) {
            return;
        }
        logger.info(LogHelper.buildLog(staff, String.format("开始上传复制新建单物流信息：%s", JSONObject.toJSONString(TradeUtils.toSids(trades)))));
        List<Trade> toDoList = trades.stream().filter(trade -> TradeTagUtils.checkIfExistTag(trade, SystemTags.TAG_TRADE_COPY)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(toDoList)) {
            Map<String, Trade> tradeMap = new HashMap<>();
            Map<Long, List<Trade>> userIdTradesMap = TradeUtils.groupByUserId(trades);
            Map<Long, User> userMap = new HashMap<>();
            for (Map.Entry<Long, List<Trade>> entry : userIdTradesMap.entrySet()) {
                staffAssembleBusiness.getUser(staff, entry.getKey(), userMap);
            }
            staff.setUserIdMap(userMap);
            staff.setUsers(new ArrayList<>(userMap.values()));
            List<String> tids = toDoList.stream().map(TradeBase::getTid).collect(Collectors.toList());
            List<String> realTids = tids.stream().map(str -> str.split("-").length <= 2 ? str.split("-")[0] : str.split("-")[0] + "-" + str.split("-")[1]).collect(Collectors.toList());
            logger.info(LogHelper.buildLog(staff, String.format("复制新建单上传realTids：%s", JSONObject.toJSONString(realTids))));
            for (Trade trade : toDoList) {
                String str = trade.getTid();
                tradeMap.put(str.split("-").length <= 2 ? str.split("-")[0] : str.split("-")[0] + "-" + str.split("-")[1], trade);
            }

            Map<Long, TradeMemo> tradeMemoMap = new HashMap<>();
            Map<Long, ExpressCompany> expressCompanyMap;
            expressCompanyMap = expressCompanyService.getExpressCompanyIdMap();
            List<TbTrade> trades1 = tradeSearchService.queryByTids(staff, false, realTids.toArray(new String[0]));
            List<Long> sids = TradeUtils.toSidList(trades1);
            List<Trade> trades2 = tradeSearchService.queryBySidsContainMergeTrade(staff, true, true, true, sids.toArray(new Long[0]));
            Map<Long, Trade> allTradesMap = new HashMap<>();
            for (Trade trade : trades2) {
                allTradesMap.put(trade.getSid(), trade);//合单子单
            }
            for (Trade trade : trades2) {
                Trade copyTrade = tradeMap.get(trade.getTid());
                if (TradeUtils.isMerge(trade) && copyTrade == null) {
                    Trade mainTrade = allTradesMap.get(trade.getMergeSid());
                    copyTrade = tradeMap.get(mainTrade.getTid());
                }
                if (copyTrade == null) {
                    logger.info(LogHelper.buildLog(staff, String.format("复制新建单上传的找不到原始单：%s", trade.getSid())));
                    continue;
                }
                Long expressId = copyTrade.getExpressCompanyId();
                if (expressId == null) {
                    continue;
                }
                ExpressCompany expressCompany = expressCompanyMap.get(expressId);
                logger.info(LogHelper.buildLog(staff, String.format("复制新建单上传的expressCompany：%s", JSONObject.toJSONString(expressCompany))));
                if (expressCompany == null) {
                    continue;
                }
                TradeMemo tradeMemo = TradeMemo.builder()
                        .handType(TradeMemoTypeEnum.PLAT_SELLER_EMEO_FLAG.getType())
                        .sellerMemo(String.format(" %s，%s", expressCompany.getName(), copyTrade.getOutSid()))
                        .isHandlerMemo(0)
                        .append(true)
                        .rematch(false)
                        .handFrom(TradeMemoFromEnum.WEB.getType()).build();
                tradeMemoMap.put(trade.getSid(), tradeMemo);
            }
            logger.info(LogHelper.buildLog(staff, String.format("复制新建单上传订单备注Map：%s", JSONObject.toJSONString(tradeMemoMap))));
            if (!tradeMemoMap .isEmpty()) {
                tradeUpdateSellerMemoFlagBusiness.update(staff, tradeMemoMap);
            }
        }
    }

    private void logConsign(Staff staff, List<ConsignRecord> records, long took) {
        if (logger.isDebugEnabled()) {
            for (ConsignRecord record : records) {
                logger.debug(ConsignLog.buildConsignLog(staff, record, took));
            }
        }
    }

    private void filterSplit(List<Trade> trades, Integer dummyType) {
        StringBuilder splitBuf = new StringBuilder();
        StringBuilder sysStatusBuf = new StringBuilder();
        for (Trade trade : trades) {
            if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource()) && trade.isDangkouTrade()) {
                continue;
            }
            if (CommonConstants.PLAT_FORM_TYPE_BTAS.equals(trade.getSubSource())) {
                continue;
            }
            if (CommonConstants.PLAT_FORM_TYPE_TIK_TOK_QTG.equals(trade.getSubSource())
                    || CommonConstants.PLAT_FORM_TYPE_TIK_TOK_QTG.equals(trade.getSource())
                    || CommonConstants.PLAT_FORM_TYPE_TIK_TOK_QTG.equals(trade.getSplitSource())) {
                continue;
            }
            if (CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(trade.getSubSource())
                    ||CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(trade.getSource())
                    ||CommonConstants.PLAT_FORM_TYPE_SHOPEE.equals(trade.getSplitSource())) {
                continue;
            }
            if (CommonConstants.PLAT_FORM_TYPE_SHOPEE_QTG.equals(trade.getSubSource())
                    || CommonConstants.PLAT_FORM_TYPE_SHOPEE_QTG.equals(trade.getSource())
                    || CommonConstants.PLAT_FORM_TYPE_SHOPEE_QTG.equals(trade.getSplitSource())) {
                continue;
            }
            if (CommonConstants.PLAT_FORM_TYPE_SMTQTG.equals(trade.getSubSource())
                    ||CommonConstants.PLAT_FORM_TYPE_SMTQTG.equals(trade.getSource())
                    ||CommonConstants.PLAT_FORM_TYPE_SMTQTG.equals(trade.getSplitSource())) {
                continue;
            }
            if (CommonConstants.PLAT_FORM_TYPE_SHEIN.equals(trade.getSubSource())
                    ||CommonConstants.PLAT_FORM_TYPE_SHEIN.equals(trade.getSource())
                    ||CommonConstants.PLAT_FORM_TYPE_SHEIN.equals(trade.getSplitSource())) {
                continue;
            }
            //1. 正常拆单，enableStatus == 1
            //2. 拆单与普通订单或其它合单再次合单后，所有的订单都算做拆单，主单enableStatus = 1,isSplit=true
            if (trade.getEnableStatus() - 1 == 0) {
                if (TradeUtils.isSplit(trade) && !UploadUtils.isAllowSplitTradeDummyUploadPlat(trade)) {
                    if ("sys".equals(trade.getSource()) && StrUtil.isEmpty(trade.getSubSource())) {
                        //按sku拆单且是系统单支持无需物流发货
                    } else {
                        if (splitBuf.length() > 0) {
                            splitBuf.append(",");
                        }
                        splitBuf.append(trade.getSid());
                    }
                }
                //temu的单子 dummyType为Null的
                if ((CommonConstants.PLAT_FORM_TYPE_TEMU.equals(trade.getSource())
                        || CommonConstants.PLAT_FORM_TYPE_TEMU.equals(trade.getSubSource())
                        || CommonConstants.PLAT_FORM_TYPE_TEMU.equals(trade.getSplitSource()))
                        && dummyType != null && (dummyType == 1 || dummyType == 2)) {
                    if (splitBuf.length() > 0) {
                        splitBuf.append(",");
                    }
                    splitBuf.append(trade.getSid());
                }
            }
            if (!trade.getSysStatus().equals(Trade.SYS_STATUS_FINISHED_AUDIT)) {
                if (sysStatusBuf.length() > 0) {
                    sysStatusBuf.append(",");
                }
                sysStatusBuf.append(trade.getSid());
            }
        }
        Assert.isTrue(splitBuf.length() == 0, "拆单[" + splitBuf + "]不能无需物流发货");
        Assert.isTrue(sysStatusBuf.length() == 0, "非审核成功订单[" + sysStatusBuf + "]不能无需物流发货");
    }

    public ConsignData filter(Staff staff, List<Trade> trades, ConsignHandler handler) {
        Integer allowPrint = staff.getConf().getAllowPrint();
        if (allowPrint != null && allowPrint == 0 && !isAllFxTrades(trades)) {
            throw new IllegalArgumentException("您暂时无法打印或发货,请联系在线客服进行处理！");
        }
        sysOrderStatusMappingBusiness.mapSysOrderPlatStatus(staff, trades);
        handler.sidUploadRecordsMap = uploadRecordBusiness.fillUploadRecord(staff, trades);
        return staff.getCompany().getOrderType() - 1 == 0 || staff.getCompany().getOrderType() - 2 == 0 ? filter4Buyout(staff, trades, handler) : filter4Normal(staff, trades, handler);
    }

    private ConsignData filter4Normal(Staff staff, List<Trade> trades, ConsignHandler filter) {
        ConsignData consignData = new ConsignData();
        consignData.tradeConfig = tradeConfigService.get(staff);
        consignData.splitConsignUploadSellerMemo = getSplitConsignUploadSellerMemo(staff);
        consignData.warehouseIdType = warehouseService.getWarehouseIdType(staff);
        consignData.fxTradeMap = getFxTradeMap(trades);
        consignData.customerBalanceMap = getCustomerBalanceVOMap(staff, trades, consignData.tradeConfig);
        consignData.fxCashFlowData = qiMenFxCashFlowBusiness.verifyBalance(staff, TradeUtils.getQimenFxTrades(trades));
        consignData.operateType = filter.getOperateType();
        for (Trade trade : trades) {
            filter.check(staff, trade, consignData);
        }
        return consignData;
    }

    private boolean getSplitConsignUploadSellerMemo(Staff staff) {
        try {
            String configValue = tradeConfigNewService.get(staff, TradeConfigEnum.UPLOAD_SELLER_MEMO_WHEN_CONSIGNING_SPLIT).getConfigValue();
            UploadSellerMemoWhenConsigningSplit uploadSellerMemoWhenConsigningSplit = JSON.parseObject(configValue, UploadSellerMemoWhenConsigningSplit.class);
            return NumberUtils.isEquals(uploadSellerMemoWhenConsigningSplit.getEnableStatus(), 1);
        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, "获取配置出错，uploadSellerMemoWhenConsigningSplit"), e);
        }
        return false;
    }

    public Map<Long, CmCustomerBalanceVO> getCustomerBalanceVOMap(Staff staff, List<Trade> trades, TradeConfig tradeConfig) {
        // 没有开通配置不做查询处理
        if (!tradeConfig.tradeConsignVerifyCustomerBalance()) {
            return null;
        }
        Set<Long> userIds = trades.stream().map(Trade::getUserId).collect(Collectors.toSet());
        List<CmCustomerBalanceVO> cmCustomerBalanceVOList = cmCustomerDubboService.getListCmCustomerBalanceVO(staff, userIds);
        Map<Long, CmCustomerBalanceVO> customerBalanceVOMap = new HashMap<>();
        cmCustomerBalanceVOList.forEach(a -> {
            if (CollectionUtils.isNotEmpty(a.getUserIdList())) {
                a.getUserIdList().forEach(userId -> {
                    customerBalanceVOMap.computeIfAbsent(userId, k -> a);
                });
            }
        });
        return customerBalanceVOMap;
    }

    private ConsignData filter4Buyout(Staff staff, List<Trade> trades, ConsignHandler handler) {
        ConsignData consignData = new ConsignData();
        consignData.tradeConfig = tradeConfigService.get(staff);
        consignData.splitConsignUploadSellerMemo = getSplitConsignUploadSellerMemo(staff);
        consignData.warehouseIdType = warehouseService.getWarehouseIdType(staff);
        consignData.fxTradeMap = getFxTradeMap(trades);
        consignData.customerBalanceMap = getCustomerBalanceVOMap(staff, trades, consignData.tradeConfig);
        consignData.fxCashFlowData = qiMenFxCashFlowBusiness.verifyBalance(staff, TradeUtils.getQimenFxTrades(trades));
        Map<Long, List<Trade>> mergeTradesMap = new HashMap<>();
        for (Trade trade : trades) {
            if (com.raycloud.dmj.domain.trade.utils.TradeUtils.isMerge(staff, trade)) {
                mergeTradesMap.computeIfAbsent(trade.getMergeSid(), k -> new ArrayList<>()).add(trade);
            }
        }
        for (Trade trade : trades) {
            //已扣过费的订单以及合单后的隐藏订单不再检查单量是否可用
            if (trade.getIsDeduct() - 1 == 0 || trade.getEnableStatus() - 2 == 0) {
                if (handler.check(staff, trade, consignData)) {
                    trade.setIsDeduct(1);
                }
            } else {
                if (handler.check(staff, trade, consignData)) {
                    trade.setIsDeduct(1);
                    consignData.deductNum++;
                    consignData.deductTradeSids.add(trade.getSid());
                }
            }
        }
        return consignData;
    }

    /**
     * 发货后校正订单成本价、净重、体积
     */
    private void calculateCost(Staff staff, List<Trade> trades, List<Trade> updateList) {
        if (updateList.isEmpty()) {
            return;
        }
        DevLogBuilder builder = DevLogBuilder.forDev(staff, LogBusinessEnum.CONSIGN).append("发货后校正订单成本价、净重、体积 耗时统计").append("tradeSize",trades.size()).startTimer();
        Map<Long, Trade> updateTradeMap = TradeUtils.toMapBySid(updateList);
        Map<Long, List<Trade>> mergeTrades = new HashMap<>();
        List<Trade> updateTrades = new ArrayList<>();
        List<Trade> updateMergeTrades = new ArrayList<>();
        for (Trade trade : trades) {
            builder.reBaseTimer();
            cancelExcep(staff, trade);
            builder.recordMutiTimer("cancelExcep");
            trade.setSaleFee(TradeUtils.calculateTradeSaleFee(trade));
            trade.setVolume(TradeUtils.calculateVolumeWithPackma(trade));
            trade.setNetWeight(TradeUtils.calculateTradeNetWeightWithPackma(trade));
            Trade updateTrade = updateTradeMap.get(trade.getSid());
            if (trade.getSid() - trade.getMergeSid() != 0 && updateTrade != null) {//非合单或合单后的次单
                builder.reBaseTimer();
                cancelExcep(staff, updateTrade);
                builder.recordMutiTimer("cancelExcep2");
                updateTrade.setSaleFee(trade.getSaleFee());
                updateTrade.setVolume(trade.getVolume());
                updateTrade.setNetWeight(trade.getNetWeight());
            }
            if (trade.getMergeSid() > 0) {//合单
                mergeTrades.computeIfAbsent(trade.getMergeSid(), ms -> new ArrayList<>()).add(trade);
            }
            if (updateTrade != null) {
                // 临时将净重放入更新订单中，然后进行计算逻辑，
                // 如果是非合单就可以直接赋值当前订单净重，如果是合单，后续逻辑会将新的净重覆盖
                updateTrade.setNetWeight(trade.getNetWeight());
                // 因为计算逻辑中有根据是否称重判断，故此处需要设置原始值
                updateTrade.setIsWeigh(trade.getIsWeigh());
                updateTrade.setLogisticsCompanyId(trade.getLogisticsCompanyId());
                // 运费计算需要收件人信息
                updateTrade.setReceiverState(trade.getReceiverState());
                updateTrade.setReceiverCity(trade.getReceiverCity());
                updateTrade.setReceiverDistrict(trade.getReceiverDistrict());
                updateTrades.add(updateTrade);
            }
        }
        if (updateTrades.size() > 0) {
            // 发货的时候计算理论运费 感觉这里可能会慢
            LogKit.took(() -> {
                builder.reBaseTimer();
                tradeCalculateTheoryPostFeeBusiness.calculateTheoryFreight(staff, updateTrades, BusinessNodeEnum.TRADE_SEND_GOODS);
                builder.recordTimer("calFreight("+updateTrades.size()+")");
                return null;
            }, staff, logger, 2000L);
        }
        if (!mergeTrades.isEmpty()) {
            builder.append("mergeSize",mergeTrades.size());
            for (Map.Entry<Long, List<Trade>> entry : mergeTrades.entrySet()) {
                Trade mainTrade = null;
                List<Trade> entryTrade = entry.getValue();
                double vc = 0, vv = 0, vw = 0;
                for (int i = 0; i < entry.getValue().size(); i++) {
                    Trade trade = entry.getValue().get(i);
                    vc += trade.getCost();
                    vv += trade.getVolume();
                    vw += trade.getNetWeight();
                    if (trade.getMergeSid() - trade.getSid() == 0) {
                        mainTrade = trade;
                    } else {
                        //将子订单的理论运费置为空
//                        updateTradeMap.get(entryTrade.get(i).getSid()).setTheoryPostFee(0.0);
                    }
                }
                if (mainTrade != null) {
                    mainTrade.setVolume(vv);
                    Object packmaVolume = TradeExtUtils.getExtraFieldValue(mainTrade.getTradeExt(), TradeExtraFieldEnum.PACKMA_VOLUME.getField());
                    if (packmaVolume != null) {
                        double packmaVolumeDouble = Double.parseDouble(packmaVolume.toString());
                        if (!MathUtils.equalsZero(packmaVolumeDouble)) {
                            mainTrade.setVolume(packmaVolumeDouble);
                        }
                    }
                    mainTrade.setNetWeight(vw);
                    Trade updateTrade = updateTradeMap.get(mainTrade.getSid());
                    if (updateTrade != null) {
                        builder.reBaseTimer();
                        cancelExcep(staff, updateTrade);
                        builder.recordMutiTimer("cancelExcep3");
                        updateTrade.setVolume(mainTrade.getVolume());
                        updateTrade.setNetWeight(mainTrade.getNetWeight());
                        updateMergeTrades.add(updateTrade);
                    }
                }
            }
            // 合单发货的时重新计算理论运费
            LogKit.took(() -> {
                builder.reBaseTimer();
                tradeCalculateTheoryPostFeeBusiness.calculateTheoryFreight(staff, updateMergeTrades, BusinessNodeEnum.TRADE_SEND_GOODS);
                builder.recordTimer("calMergeFreight("+updateMergeTrades.size()+")");
                return null;
            }, staff, logger, 2000L);
            for (Map.Entry<Long, List<Trade>> entry : mergeTrades.entrySet()) {
                for (Trade trade : entry.getValue()) {
                    if (!(trade.getMergeSid() - trade.getSid() == 0)) {
                        if (updateTradeMap.get(trade.getSid()) != null) {
                            //主单不作处理，将子订单的理论运费置为空
                            updateTradeMap.get(trade.getSid()).setTheoryPostFee(0.0);
                            //主单不作处理，将子订单的实际运费置为空
                            updateTradeMap.get(trade.getSid()).setActualPostFee("0.0");
                        }
                    }
                }
            }
            builder.startWatch().appendDevTook(3000L).printDebug(logger);
        }
    }

    private void cancelExcep(Staff staff, Trade trade) {
        trade.setIsExcep(0);
        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.HALT, 0L);
        TradeExceptUtils.clearCustomExcept(staff, trade);
        TradeExceptUtils.setStockStatus(staff, trade, Trade.STOCK_STATUS_NORMAL);
        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.ADDRESS_CHANGED, 0L);
        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.SELLER_MEMO_UPDATE, 0L);
        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.BLACK_NICK, 0L);
        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.UNATTAINABLE, 0L);
        TradeExceptUtils.updateExcept(staff, trade, ExceptEnum.PART_PAY_EXCEPT, 0L);
        TradeExceptUtils.clearTradePointExcept(staff,trade,ExceptEnum.INSUFFICIENT,false);
        List<Order> orders4Trade = TradeUtils.getOrders4Trade(trade);
        for(Order order:orders4Trade){
            OrderExceptUtils.updateExceptOrder(staff,order,ExceptEnum.SMALL_REFUND_EXCEPT,0L);
        }
        TradeExceptUtils.updateExcept(staff,trade,ExceptEnum.SMALL_REFUND_EXCEPT,0L);
    }

    /**
     * 订阅物流 系统订单订阅
     */
    private void logisticsSubscribe(Staff staff, List<Trade> trades) {
        if (TradeLogisticsTrackingUtils.isOpenLogisticsTracking(staff, tradeConfigService)) {
            List<Trade> needSubscribes = new ArrayList<>();
            for (Trade trade : trades) {
//                if (CommonConstants.PLAT_FORM_TYPE_SYS.equals(trade.getSource())) {
                Trade needSubscribe = new TbTrade();
                needSubscribe.setCompanyId(trade.getCompanyId());
                needSubscribe.setUserId(trade.getUserId());
                needSubscribe.setSid(trade.getSid());
                needSubscribe.setSource(trade.getSource());
                needSubscribe.setTemplateId(trade.getTemplateId());
                needSubscribe.setTemplateType(trade.getTemplateType());
                needSubscribe.setOutSid(trade.getOutSid());
                needSubscribe.setConsignTime(trade.getConsignTime());
                needSubscribe.setWarehouseId(trade.getWarehouseId());
                needSubscribe.setTid(trade.getTid());
                needSubscribe.setTaobaoId(trade.getTaobaoId());
                needSubscribe.setAddressMd5(trade.getAddressMd5());
                needSubscribe.setDestId(trade.getDestId());
                needSubscribe.setSubSource(trade.getSubSource());
                needSubscribe.setConvertType(trade.getConvertType());
                needSubscribe.setBelongType(trade.getBelongType());
                needSubscribes.add(needSubscribe);
//                }
            }

            if (CollectionUtils.isNotEmpty(needSubscribes)) {
//                eventCenter.fireEvent(this, new EventInfo("trade.logistics.subscribe").setArgs(new Object[]{staff}), needSubscribes);
                List<Long> sids = needSubscribes.stream().map(Trade::getSid).collect(Collectors.toList());
                logger.debug(LogHelper.buildLog(staff, "发送物流订阅事件成功,订单号:" + JSONObject.toJSONString(sids)));
            }
        }
    }

    private boolean isAllFxTrades(List<Trade> tradeList) {
        for (Trade trade : tradeList) {
            if (!TradeUtils.isFxOrMixTrade(trade)) {
                return false;
            }
        }
        return true;
    }

    public Map<String, Trade> getFxTradeMap(List<Trade> tradeList) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return Maps.newHashMap();
        }
        Map<String, Trade> fxTradeMap = new HashMap<>();
        return getFxTradeMapByMix(fxTradeMap, tradeList, 1);
    }

    public Map<String, Trade> getFxTradeMapByMix(Map<String, Trade> fxTradeMap, List<Trade> tradeList, int level) {
        //最多向上查找3次
        if (!TradeUtils.checkFxAndGxLevel(level)) {
            return fxTradeMap;
        }
        List<Trade> gxTradeList = tradeList.stream().filter(TradeUtils::isGxOrMixTrade).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(gxTradeList)) {
            return fxTradeMap;
        }
        Map<Long, List<Trade>> sourceIdMap = gxTradeList.stream().collect(Collectors.groupingBy(Trade::getSourceId));
        //获取分销且供销的订单，用于获取最上层的分销订单
        List<Trade> mixTrade = new ArrayList<>();
        for (Map.Entry<Long, List<Trade>> entry : sourceIdMap.entrySet()) {
            Long sourceId = entry.getKey();
            List<Trade> trades = entry.getValue();
            //获取分销信息
            Staff fxStaff = staffService.queryFullByCompanyId(sourceId);
            if (fxStaff == null) {
                continue;
            }
            List<String> fxSids = TradeUtils.toTidList(trades);
            List<Trade> fxTrades = tradeSearchService.queryBySids(fxStaff, false, fxSids.stream().map(Long::parseLong).toArray(Long[]::new));
            Map<String, Trade> collect = fxTrades.stream().collect(Collectors.toMap(t -> t.getSid().toString(), t -> t, (k1, k2) -> k2));
            fxTradeMap.putAll(collect);
            mixTrade.addAll(fxTrades.stream().filter(TradeUtils::isGxAndFxTrade).collect(Collectors.toList()));
        }
        //针对，分销且供销的订单，回调方法，直到获取到分销订单
        return getFxTradeMapByMix(fxTradeMap, mixTrade, ++level);
    }

    public Map<String, Trade> getAllTradeMapByFx(List<Trade> tradeList) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return Maps.newHashMap();
        }
        Map<String, Trade> tradeMap = new HashMap<>();
        return getAllTradeMapByFx(tradeMap, tradeList, 1);
    }

    public Map<String, Trade> getAllTradeMapByFx(Map<String, Trade> tradeMap, List<Trade> tradeList, int level) {
        //最多向上查找3次
        if (!TradeUtils.checkFxAndGxLevel(level)) {
            return tradeMap;
        }
        tradeMap.putAll(tradeList.stream().collect(Collectors.toMap(t -> t.getSid().toString(), t -> t, (k1, k2) -> k2)));
        tradeMap.putAll(tradeList.stream().filter(TradeUtils::isMerge).collect(Collectors.toMap(t -> t.getMergeSid().toString(), t -> t, (k1, k2) -> k2)));
        List<Trade> fxTradeList = tradeList.stream().filter(TradeUtils::isFxOrMixTrade).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fxTradeList)) {
            return tradeMap;
        }
        Map<Long, List<Trade>> destIdMap = fxTradeList.stream().collect(Collectors.groupingBy(Trade::getDestId));
        //获取分销且供销的订单，用于获取最上层的供销订单
        List<Trade> mixTrade = new ArrayList<>();
        for (Map.Entry<Long, List<Trade>> entry : destIdMap.entrySet()) {
            Long destId = entry.getKey();
            List<Trade> trades = entry.getValue();
            Staff gxStaff = staffService.queryFullByCompanyId(destId);
            if (gxStaff == null) {
                continue;
            }
            List<Trade> gxTrades = TradeUtils.toTrades(tradeSearchService.queryByTids(gxStaff, false, getGxTids(trades).toArray(new String[0])));
            Map<String, Trade> collect = gxTrades.stream().filter(t1 -> !TradeUtils.isCancel(t1)).collect(Collectors.toMap(t -> t.getSid().toString(), t -> t, (k1, k2) -> k2));
            tradeMap.putAll(collect);
            mixTrade.addAll(gxTrades.stream().filter(t -> TradeUtils.isGxAndFxTrade(t) && !TradeUtils.isCancel(t)).collect(Collectors.toList()));
        }
        //针对，分销且供销的订单，回调方法，直到获取到供销订单
        return getAllTradeMapByFx(tradeMap, mixTrade, ++level);
    }

    private Set<String> getGxTids(List<Trade> trades) {
        Set<String> fxSids = Sets.newHashSet();
        trades.forEach(t -> {
            if (TradeUtils.isMerge(t)) {
                fxSids.add(String.valueOf(t.getMergeSid()));
            } else {
                fxSids.add(String.valueOf(t.getSid()));
            }
        });
        return fxSids;

    }

    private List<Trade> buildUpdateTrade(List<Trade> tradeList) {
        List<Trade> updateList = new ArrayList<>(tradeList.size());
        for (Trade trade : tradeList) {
            Trade update = new TbTrade();
            update.setSid(trade.getSid());
            update.setOutSid(trade.getOutSid());
            updateList.add(update);
        }
        return updateList;
    }

    private void sendO2oAutoPickEvent(Staff staff, List<Trade> finishAuditTrades) {
        if(CollectionUtils.isNotEmpty(finishAuditTrades)) {
            List<Trade> o2oTrades = finishAuditTrades.stream().filter(trade -> PlatformUtils.isTradeO2o(trade)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(o2oTrades)) {
                TradeConfigNew configNew = tradeConfigNewService.get(staff,TradeConfigEnum.O2O_TRADE_SUB_STOCK);
                boolean isSub = configNew != null && configNew.isOpen();
                if(!isSub) {
                    TradeConfigNew autoPickConfig = tradeConfigNewService.get(staff, TradeConfigEnum.O2O_TRADE_AUTO_PICK_SET);
                    Map<Long, User> userMap = new HashMap<>();
                    for (Trade trade : o2oTrades) {
                        String tid = trade.getTid();
                        User user = staffAssembleBusiness.getUser(staff, trade.getUserId(), userMap);
                        if (autoPickConfig != null && "4".equals(autoPickConfig.getConfigValue())) {
                            eventCenter.fireEvent(this, new EventInfo("o2o.trade.auto.pick").setArgs(new Object[]{user, tid, trade}), null);
                        }
                    }
                }
            }
        }
    }

}

package com.raycloud.dmj.business.presell;

import com.google.common.collect.Lists;
import com.raycloud.dmj.business.payment.support.PaymentCalculateSupports;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.dao.trade.PresellItemDao;
import com.raycloud.dmj.dao.trade.PresellRuleDao;
import com.raycloud.dmj.domain.Page;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.ProgressEnum;
import com.raycloud.dmj.domain.item.*;
import com.raycloud.dmj.domain.progress.ProgressData;
import com.raycloud.dmj.domain.sku.DmjSku;
import com.raycloud.dmj.domain.stock.StockConstants;
import com.raycloud.dmj.domain.trade.enums.*;
import com.raycloud.dmj.domain.trade.enums.TradeRuleOperationTypeEnum;
import com.raycloud.dmj.domain.trade.presell.PresellAutoIdentifyTypeEnum;
import com.raycloud.dmj.domain.trade.presell.PresellAutoUnlockEnum;
import com.raycloud.dmj.domain.trade.rule.TradeRule;
import com.raycloud.dmj.domain.trade.rule.convert.PresellConvertUtils;
import com.raycloud.dmj.domain.trade.utils.PresellItemUtils;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.user.Shop;
import com.raycloud.dmj.domain.utils.*;
import com.raycloud.dmj.item.search.api.DmjItemCommonSearchApi;
import com.raycloud.dmj.item.search.dto.DmjItemDto;
import com.raycloud.dmj.item.search.request.*;
import com.raycloud.dmj.item.search.response.QueryMiniItemByOuterIdListResponse;
import com.raycloud.dmj.services.dubbo.IItemServiceDubbo;
import com.raycloud.dmj.services.item.IItemServiceWrapper;
import com.raycloud.dmj.services.trade.rule.ITradeRuleService;
import com.raycloud.dmj.services.trades.*;
import com.raycloud.dmj.services.trades.support.utils.ConfigTraceUtils;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.utils.LogHelper;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.*;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raycloud.dmj.enums.PresellRuleSaveItemTypeEnums.*;

/**
 * <AUTHOR>
 * @created 2019-05-07 11:07
 */
@Service
public class PresellRuleBusiness {

    @Resource
    PresellRuleDao presellRuleDao;
    @Resource
    PresellItemDao presellItemDao;
    @Resource
    IItemServiceWrapper itemServiceWrapper;
    @Resource
    ITradeQueryService tradeQueryService;
    @Resource
    IShopService shopService;
    @Resource
    IdWorkerService idWorkerService;
    @Resource
    IProgressService progressService;
    @Resource
    DmjItemCommonSearchApi dmjItemCommonSearchApi;
    @Resource
    IItemServiceDubbo itemServiceDubbo;
    @Resource
    ITradeRuleService tradeRuleService;


    public List<PresellRule> queryRuleList(Staff staff, Map<String, Object> params, Page page, int queryItem) {
        if (params == null) {
            params = new HashMap<>();
        }
        params.put("page", page);
        List<PresellRule> rules = presellRuleDao.queryList(staff, params);
        if (!rules.isEmpty()) {
            fillShopNames(staff, rules);
            fillItems(staff, rules, queryItem);
        }
        return rules;
    }

    public Long queryRuleCount(Staff staff, Map<String, Object> params) {
        return presellRuleDao.queryCount(staff, params);
    }

    public PresellRule queryById(Staff staff, Long id, int queryItem) {
        PresellRule rule = presellRuleDao.queryById(staff, id);
        if (rule != null) {
            List<PresellRule> ruleList = new ArrayList<>();
            ruleList.add(rule);
            fillShopNames(staff, ruleList);
            fillItems(staff, ruleList, queryItem);
        }
        return rule;
    }

    public PresellRule queryById(Staff staff, Long id, Map<Long, PresellRule> ruleMap) {
        PresellRule rule = ruleMap.get(id);
        if (rule == null) {
            rule = presellRuleDao.queryById(staff, id);
            if (rule != null) {
                ruleMap.put(id, rule);
            }
        }
        return rule;
    }

    @Transactional
    public void saveRule(Staff staff, PresellRule rule) {
        if (rule.getId() == null || rule.getId() <= 0) {//新建
            add(staff, rule);
        } else {//更新
            update(staff, rule);
            presellItemDao.deleteByRuleId(staff, rule.getId(), PresellItemUtils.RULE_TYPE_PRESELL);
        }
        if (CollectionUtils.isNotEmpty(rule.getItems())) {
            for (PresellItem item : rule.getItems()) {
                item.setRuleId(rule.getId());
                item.setRuleType(PresellItemUtils.RULE_TYPE_PRESELL);
            }
            presellItemDao.batchInsert(staff, rule.getItems());
        }
    }

    private void add(Staff staff, PresellRule rule) {
        checkInsert(staff, rule);
        rule.setEnableStatus(1);
        rule.setId(idWorkerService.nextId());
        presellRuleDao.updatePriority(staff, true, rule.getPriority(), null);
        presellRuleDao.insert(staff, rule);
        rule.setBusinessType(TradeBusinessRuleEnum.PRESELL_RULE.getBusinessId());
        tradeRuleService.initTradeRule(staff, rule);
    }

    private void update(Staff staff, PresellRule rule) {
        PresellRule origin = checkUpdate(staff, rule);
        if (rule.getPriority() != null) {
            if (rule.getPriority() > origin.getPriority()) {
                presellRuleDao.updatePriority(staff, false, origin.getPriority(), rule.getPriority());
            } else if (rule.getPriority() < origin.getPriority()) {
                presellRuleDao.updatePriority(staff, true, rule.getPriority(), origin.getPriority());
            }
        }
        rule.setEnableStatus(1);
        //应付金额字段处理
        updatePayment(rule, origin);
        //处理平台商品信息
        updatePlatformItem(rule, origin);

        presellRuleDao.update(staff, rule);

        //老的不存在，先保存一份老的数据
        origin.setBusinessType(TradeBusinessRuleEnum.PRESELL_RULE.getBusinessId());
        if (StringUtils.isBlank(origin.getName())) {
            origin.setName(rule.getName());
        }
        if (Objects.isNull(origin.getIsOpen())){
            origin.setIsOpen(rule.getIsOpen());
        }
        TradeRule oldRule = tradeRuleService.initTradeRule(staff, origin);
        //更新
        tradeRuleService.modify(staff, PresellConvertUtils.convert(staff, rule, oldRule, TradeRuleOperationTypeEnum.UPDATE));
    }

    private void updatePlatformItem(PresellRule rule, PresellRule origin) {
        //本次更新使用的是金额，处理老规则的平台商品信息。
        if (rule.needProcessPayment()){
            Optional.ofNullable(origin.getPlatformItemUrl())
                    .ifPresent(platformItemUrl -> rule.setPlatformItemUrl(StringUtils.EMPTY));
            Optional.ofNullable(origin.getSkuPropertiesName())
                    .ifPresent(skuPropertiesName -> rule.setSkuPropertiesName(StringUtils.EMPTY));
            Optional.ofNullable(origin.getAutoIdentifyPropertiesName())
                    .ifPresent(maxPayment -> rule.setAutoIdentifyPropertiesName(StringUtils.EMPTY));
            Optional.ofNullable(origin.getAutoIdentifyType())
                    .ifPresent(maxPayment -> rule.setAutoIdentifyType(PresellAutoIdentifyTypeEnum.DEFAULT_AUTO_IDENTIFY_TYPE.getType()));
        }
    }

    private static void updatePayment(PresellRule rule, PresellRule origin) {
        //更新场景下，若本次使用的是商品信息则要把原记录的应付金额信息清空
        if (CollectionUtils.isNotEmpty(rule.getItems())){
            Optional.ofNullable(origin.getMinPayment())
                    .ifPresent(minPayment -> rule.setMinPayment(StringUtils.EMPTY));
            Optional.ofNullable(origin.getMaxPayment())
                    .ifPresent(minPayment -> rule.setMaxPayment(StringUtils.EMPTY));
        }
        if (StringUtils.isNotBlank(origin.getMinPayment()) && StringUtils.isBlank(rule.getMinPayment())){
            rule.setMinPayment(StringUtils.EMPTY);
        }
        if (StringUtils.isNotBlank(origin.getMaxPayment()) && StringUtils.isBlank(rule.getMaxPayment())){
            rule.setMaxPayment(StringUtils.EMPTY);
        }
    }


    @Transactional
    public void saveRuleItem(Staff staff, List<PresellItem> items, List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        if (CollectionUtils.isEmpty(ruleIds)) {
            List<List<PresellItem>> splitList = Lists.partition(items, 100);
            //分批次调用
            for (List<PresellItem> psList : splitList) {
                if (CollectionUtils.isNotEmpty(psList)) {
                    saveItem(staff, psList);
                }
            }
        } else {
            for (Long ruleId : ruleIds) {
                List<List<PresellItem>> splitList = Lists.partition(items, 100);
                //分批次调用
                for (List<PresellItem> psList : splitList) {
                    if (CollectionUtils.isNotEmpty(psList)) {
                        for (PresellItem presellItem : psList) {
                            presellItem.setRuleId(ruleId);
                        }
                        saveItem(staff, psList);
                    }
                }
            }
        }
    }

    @Transactional
    public void saveRuleLogItem(Staff staff, List<PresellItem> items) {
        if (CollectionUtils.isNotEmpty(items)) {
            List<List<PresellItem>> splitList = Lists.partition(items, 1000);
            //分批次调用
            for (List<PresellItem> psList : splitList) {
                if (CollectionUtils.isNotEmpty(psList)) {
                    saveItemLog(staff, psList);
                }
            }
        }
    }

    @Transactional
    public void updateRuleItem(Staff staff, Integer ruleType, List<PresellItem> items, List<Long> deleteIds) {
        //需要删除的
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            deleteRuleItem(staff, ruleType, deleteIds);
        }
        saveRuleItem(staff, items, null);
    }

    private void saveItem(Staff staff, List<PresellItem> items) {
        Long ruleId = items.get(0).getRuleId();
        Integer ruleType = items.get(0).getRuleType();
        Integer itemType = items.get(0).getItemType();
        if (Objects.equals(PLATFORM_ITEM.getItemType(), itemType)) {
            List<String> platformItemIds = items.stream().map(PresellItem::getPlatformItemId).distinct().collect(Collectors.toList());
            List<String> platformSkuIds = items.stream().map(PresellItem::getPlatformSkuId).distinct().collect(Collectors.toList());
            presellItemDao.deleteByIdsForPlatformItem(staff, null, ruleType, ruleId, platformItemIds, platformSkuIds);
        } else {
            List<Long> sysItemIds = items.stream().map(PresellItem::getSysItemId).distinct().collect(Collectors.toList());
            List<Long> sysSkuIds = items.stream().map(PresellItem::getSysSkuId).distinct().collect(Collectors.toList());
            if (sysSkuIds.contains(-1L) || sysSkuIds.contains(0L)) {
                sysSkuIds.add(-1L);
                sysSkuIds.add(0L);
            }
            presellItemDao.deleteByIds(staff, null, ruleType, ruleId, sysItemIds, sysSkuIds);
        }
        items = distinct(items, PresellRuleBusiness::keyExtractor);
        presellItemDao.batchInsert(staff, items);
    }

    private static String keyExtractor(PresellItem item) {
        Integer itemType = item.getItemType();
        if (Objects.equals(PLATFORM_ITEM.getItemType(), itemType)) {
            return item.getPlatformItemId() + "_" + item.getPlatformSkuId();
        }else {
            return item.getSysItemId() + "_" + item.getSysSkuId();
        }
    }

    private static <E> List<E> distinct(Collection<E> coll, Function<? super E, ?> keyExtractor) {
        return new ArrayList<>(coll.stream()
                .collect(Collectors.toMap(
                        keyExtractor,
                        Function.identity(),
                        (existing, replacement) -> existing))
                .values());
    }

    public void saveItemLog(Staff staff, List<PresellItem> items) {
        for (PresellItem item : items) {
            item.setVersionId(staff.getClueId());
            item.setStaffName(staff.getName());
            item.setId(null);
        }
        presellItemDao.logBatchInsert(staff, items);
    }

    @Transactional
    public void deleteRuleItem(Staff staff, Integer ruleType, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<List<Long>> splitList = Lists.partition(ids, 100);
        //分批次调用
        for (List<Long> psList : splitList) {
            if (CollectionUtils.isNotEmpty(psList)) {
                presellItemDao.deleteByIds(staff, psList, ruleType, null, null, null);
            }
        }
    }

    public List<DmjSku> queryPresellItem(Staff staff, QueryItemSkuCondition queryItemSkuCondition) {
        return presellItemDao.queryPresellItemItemSku(staff, queryItemSkuCondition);
    }

    public List<PresellItemSkuVo> queryPresellItemSkus(Staff staff, QueryItemSkuCondition queryItemSkuCondition) {
        if (4 == queryItemSkuCondition.getRuleType() || needAccurateQueryWarehouseOuterId(queryItemSkuCondition)) {
            String queryText = queryItemSkuCondition.getQueryText();
            if (StringUtils.isNotEmpty(queryText) && queryText.contains(",")) {
                List<String> queryTextList = Arrays.asList(queryText.split(","));
                queryItemSkuCondition.setQueryTextList(queryTextList);
                queryItemSkuCondition.setQueryText(null);
            }
        }
        return presellItemDao.queryPresellItemSkus(staff, queryItemSkuCondition);
    }

    /**
     * 分仓规则是否需要精确批量查询商家编码
     */
    private boolean needAccurateQueryWarehouseOuterId(QueryItemSkuCondition queryItemSkuCondition) {
        return  3 == queryItemSkuCondition.getRuleType() && Objects.equals(queryItemSkuCondition.getIsAccurate(), "1");
    }

    public List<PresellItemSkuVo> queryPresellItemSkusLog(Staff staff, QueryItemSkuCondition queryItemSkuCondition) {
        return presellItemDao.queryPresellItemSkusLog(staff, queryItemSkuCondition);
    }

    /**
     * 获取添加到匹配规则从的平台商品
     */
    public Map<String, Object> queryPresellPlatformItem(Staff staff, QueryItemSkuCondition queryItemSkuCondition) {
        checkParams(queryItemSkuCondition);
        if (4 == queryItemSkuCondition.getRuleType() && "outerId".equals(queryItemSkuCondition.getQueryType()) ) {
            String queryText = queryItemSkuCondition.getQueryText();
            if (StringUtils.isNotEmpty(queryText) && queryText.contains(",")) {
                List<String> queryTextList = Arrays.asList(queryText.split(","));
                queryItemSkuCondition.setQueryTextList(queryTextList);
                queryItemSkuCondition.setQueryText(null);
            }
        }
        List<PresellItemSkuVo> platformSkus = presellItemDao.queryPresellPlatformItemSku(staff, queryItemSkuCondition);
        Map<String, Object> result = new HashMap<>();
        result.put("total", queryPresellPlatformItemCount(staff, queryItemSkuCondition));
        result.put("list", platformSkus);
        return result;
    }

    /**
     * 参数不为空校验
     */
    public void checkParams(QueryItemSkuCondition queryItemSkuCondition) {
        Assert.notNull(queryItemSkuCondition.getPageNo(), "分页号码不能为空");
        Assert.notNull(queryItemSkuCondition.getPageSize(), "分页大小不能为空");
        Assert.notNull(queryItemSkuCondition.getQueryType(), "查询类型不能为空：商家编码、商品名称、平台商品id、规格名称");
        Assert.notNull(queryItemSkuCondition.getProdType(), "平台商品标识不能为空：正常商品、套件商品");
        Assert.notNull(queryItemSkuCondition.getIsAccurate(), "模糊查询或者精准查询标志位不能为空");
        Assert.notNull(queryItemSkuCondition.getRuleId(), "规则id不能为空");
    }

    public int queryPresellItemCount(Staff staff, QueryItemSkuCondition queryItemSkuCondition) {
        return presellItemDao.queryPresellItemItemSkuCount(staff, queryItemSkuCondition);
    }

    public int queryPresellItemCountLog(Staff staff, QueryItemSkuCondition queryItemSkuCondition) {
        return presellItemDao.queryPresellItemItemSkuCountLog(staff, queryItemSkuCondition);
    }

    public int queryPresellPlatformItemCount(Staff staff, QueryItemSkuCondition queryItemSkuCondition) {
        return presellItemDao.queryPresellPlatformItemSkuCount(staff, queryItemSkuCondition);
    }


    @Transactional
    public PresellRule deleteRule(Staff staff, Long id) {
        PresellRule rule = checkDelete(staff, id);
        presellRuleDao.delete(staff, id);
        presellItemDao.deleteByRuleId(staff, id, PresellItemUtils.RULE_TYPE_PRESELL);
        rule.setBusinessType(TradeBusinessRuleEnum.PRESELL_RULE.getBusinessId());
        tradeRuleService.remove(staff, rule);
        return rule;
    }


    public PresellRule closeRule(Staff staff, Long id) {
        PresellRule origin = presellRuleDao.queryById(staff, id);
        Assert.isTrue(origin != null, "该预售规则已被删除！");
        origin.setItems(presellItemDao.queryItemsByRuleId(staff, origin.getId(), PresellItemUtils.RULE_TYPE_PRESELL));
        if (StringUtils.isBlank(origin.getName())) {
            origin.setName(NumberUtils.long2Str(origin.getId()));
        }
        origin.setBusinessType(TradeBusinessRuleEnum.PRESELL_RULE.getBusinessId());
        TradeRule oldRule = tradeRuleService.initTradeRule(staff, origin);

        PresellRule update = new PresellRule();
        update.setId(id);
        //关闭规则等同于将规则的结束时间设为当前时间减去1s,避免时间误差
        Date endTime = DateUtils.addSeconds(new Date(), -1);
        update.setEndTime(endTime);
        presellRuleDao.update(staff, update);
        origin.setEndTime(update.getEndTime());
        origin.setIsOpen(0);
        tradeRuleService.modify(staff, PresellConvertUtils.convert(staff, origin, oldRule, TradeRuleOperationTypeEnum.UPDATE));
        return origin;
    }

    private void checkInsert(Staff staff, PresellRule rule) {
        checkTime(rule);
        if (rule.getName() != null) {
            rule.setName(rule.getName().trim());
        }
        Assert.isTrue(rule.getName() != null && rule.getName().length() > 0, "预售规则名称不能为空");
        Assert.isNull(presellRuleDao.queryByName(staff, rule.getName(), null), "预售规则名称[" + rule.getName() + "]已存在，请检查进行中或已结束的规则里面是否有重复的规则名称！");
        Assert.isTrue(StringUtils.isNotBlank(rule.getUserIds()), "至少要选择一个店铺");
        checkPayment(rule);
        checkItems(staff, rule);
    }

    private void checkPayment(PresellRule rule) {
        BigDecimal minPayment = NumberUtils.strToDecimalElseThrow(rule.getMinPayment());
        boolean hasMinPayment = Objects.nonNull(minPayment);
        BigDecimal maxPayment = NumberUtils.strToDecimalElseThrow(rule.getMaxPayment());
        boolean hasMaxPayment = Objects.nonNull(maxPayment);
        if (hasMinPayment){
            Assert.isTrue(minPayment.compareTo(BigDecimal.ZERO) >= 0, "最小应付金额不能为负数");
        }
        if (hasMinPayment && hasMaxPayment){
            Assert.isTrue(minPayment.compareTo(maxPayment) < 0, "最小应付金额不能大于或等于最大应付金额");
        }
        List<PresellItem> ruleItems = rule.getItems();
        if (rule.needProcessPayment() && CollectionUtils.isNotEmpty(ruleItems)){
            throw new TradeException("应付金额条件和添加商品只能选择一个");
        }
        Integer autoUnlock = rule.getAutoUnlock();
        if (rule.needProcessPayment() && (Objects.equals(PresellAutoUnlockEnum.STOCK_NORMAL.getType(), autoUnlock))){
            throw new TradeException("设置应付金额条件后不能再设置 订单满足库存就解锁");
        }
        if (rule.needProcessPayment() && rule.isUnlockIfStockNormal()){
            throw new TradeException("设置应付金额条件后不能再设置 当库存满足时，忽略时间按库存解锁");
        }
    }

    private PresellRule checkUpdate(Staff staff, PresellRule rule) {
        checkTime(rule);
        PresellRule origin = presellRuleDao.queryById(staff, rule.getId());
        Assert.isTrue(origin != null, "该预售规则已被删除！");
        if (rule.getName() != null) {
            rule.setName(rule.getName().trim());
            //更改了名称，需要检查名称是否已被占用
            if (!rule.getName().equals(origin.getName())) {
                PresellRule obj = presellRuleDao.queryByName(staff, rule.getName(), null);
                Assert.isTrue(obj == null || obj.getId() - rule.getId() == 0, "预售规则名称[" + rule.getName() + "]已存在，请检查进行中或已结束的规则里面是否有重复的规则名称！");
            }
        }
        Assert.isTrue(StringUtils.isNotBlank(rule.getUserIds()), "至少要选择一个店铺");
        checkPayment(rule);
        checkItems(staff, rule);
        origin.setItems(presellItemDao.queryItemsByRuleId(staff, origin.getId(), PresellItemUtils.RULE_TYPE_PRESELL));
        return origin;
    }

    private void checkTime(PresellRule rule) {
        if (rule.getStartTimeStamp() != null && rule.getStartTimeStamp() > 0 && rule.getStartTime() == null) {
            rule.setStartTime(new Date(rule.getStartTimeStamp()));
        }
        if (rule.getEndTimeStamp() != null && rule.getEndTimeStamp() > 0 && rule.getEndTime() == null) {
            rule.setEndTime(new Date(rule.getEndTimeStamp()));
        }
        if (rule.getUnlockTimeStamp() != null && rule.getUnlockTimeStamp() > 0 && rule.getUnlockTime() == null) {
            rule.setUnlockTime(new Date(rule.getUnlockTimeStamp()));
        }
        Assert.isTrue(rule.getStartTime() != null && rule.getEndTime() != null, "预售规则开始时间与结束时间不能为空！");
        if (rule.getAutoUnlock() == PresellAutoUnlockEnum.ASSIGN_TIME.getType()) {
            Assert.isTrue(rule.getUnlockTime() != null, "按时间解锁时预售解锁时间不能为空");
        } else if (rule.getAutoUnlock() == PresellAutoUnlockEnum.PAYTIME_ADD_DELAY.getType()) {
            Assert.isTrue(rule.getDelayHours() != null, "按付款后多少小时解锁必须要设置延后小时数");
        }
    }


    private void checkItems(Staff staff, PresellRule rule) {
        //检查是否有商品已被删除或停用
        if (CollectionUtils.isNotEmpty(rule.getItems())) {
            List<Long> sysItemIds = new ArrayList<>(), sysSkuIds = new ArrayList<>();
            for (PresellItem item : rule.getItems()) {
                sysItemIds.add(item.getSysItemId());
                if (item.getSysSkuId() != null && item.getSysSkuId() > 0) {
                    sysSkuIds.add(item.getSysSkuId());
                }
            }
            Map<Long, DmjItem> itemMap = DMJItemUtils.itemList2Map(itemServiceWrapper.queryBySysItemIds(staff, sysItemIds, "sysItemId,activeStatus,outerId"));
            Map<Long, DmjSku> skuMap = DmjSkuUtils.skuList2Map(itemServiceWrapper.queryBySysSkuIds(staff, sysSkuIds, "sysItemId,sysSkuId,outerId"));
            for (PresellItem item : rule.getItems()) {
                DmjItem dmjItem = itemMap.get(item.getSysItemId());
                Assert.isTrue(dmjItem != null, "部分商品已被删除，请重新添加！");
                Assert.isTrue(dmjItem.getActiveStatus() == null || dmjItem.getActiveStatus() - 1 == 0, "部分商品已被停用，请重新添加！");
                if (item.getSysSkuId() != null && item.getSysSkuId() > 0) {
                    DmjSku dmjSku = skuMap.get(item.getSysSkuId());
                    Assert.isTrue(dmjSku != null, "部分SKU已被删除，请重新添加！");
                }
            }
        } else if(!rule.needProcessPayment()){
            Assert.isTrue(StringUtils.isNotBlank(rule.getPlatformItemUrl())
                            || StringUtils.isNotBlank(rule.getSkuPropertiesName())
                            || StringUtils.isNotBlank(rule.getAutoIdentifyPropertiesName())
                    , "没有指定系统商品时,【平台商品链接】或【规格关键字】或【预售识别关键字】至少需要指定一项！");
        }
    }

    private PresellRule checkDelete(Staff staff, Long id) {
        PresellRule rule = presellRuleDao.queryById(staff, id);
        Assert.isTrue(rule != null, "该预售规则已被删除！");
        Assert.isTrue(rule.getEndTime().before(new Date()), "只有关闭或已结束的预售规则才能删除！");
        List<Trade> trades = tradeQueryService.queryTradesByPresellRule(staff, id, false, new Page().setPageSize(1));
        Assert.isTrue(trades == null || trades.isEmpty(), "该规则有尚未解锁的预售订单,禁止删除！");
        return rule;
    }

    private void fillShopNames(Staff staff, List<PresellRule> rules) {
        Set<Long> allUserIds = new HashSet<>();
        Map<PresellRule, Set<Long>> ruleMap = new HashMap<>();
        for (PresellRule rule : rules) {
            Set<Long> userIdSet = Strings.getAsLongSet(rule.getUserIds(), ",", true);
            if (!userIdSet.isEmpty()) {
                allUserIds.addAll(userIdSet);
                ruleMap.put(rule, userIdSet);
            }
        }
        if (!allUserIds.isEmpty()) {
            List<Shop> shopList = shopService.queryByUserIds(staff, allUserIds.toArray(new Long[0]));
            ruleMap.forEach((rule, userIdSet) -> {
                StringBuilder shopNames = new StringBuilder();
                shopList.forEach(shop -> {
                    if (userIdSet.contains(shop.getUserId())) {
                        shopNames.append(StringUtils.isNotBlank(shop.getShortTitle()) ? shop.getShortTitle() : shop.getTitle()).append(",");
                    }
                });
                if (shopNames.length() > 0) {
                    rule.setShopNames(shopNames.substring(0, shopNames.length() - 1));
                }
            });
        }
    }

    public void fillItems(Staff staff, List<PresellRule> rules, int queryItem) {
        if (!rules.isEmpty() && queryItem >= 1) {
            rules.forEach(rule -> rule.setItems(presellItemDao.queryItemsByRuleId(staff, rule.getId(), PresellItemUtils.RULE_TYPE_PRESELL)));
            if (queryItem >= 2) {
                fillItemDetail(staff, rules);
            }
        }
    }

    private void fillItemDetail(Staff staff, List<PresellRule> rules) {
        List<Long> deleteIds = new ArrayList<>();
        for (PresellRule rule : rules) {
            if (rule.getItems() == null) {
                rule.setItems(presellItemDao.queryItemsByRuleId(staff, rule.getId(), PresellItemUtils.RULE_TYPE_PRESELL));
            }
            if (!rule.getItems().isEmpty()) {
                List<Long> sysItemIds = new ArrayList<>(), sysSkuIds = new ArrayList<>();
                for (PresellItem item : rule.getItems()) {
                    sysItemIds.add(item.getSysItemId());
                    if (item.getSysSkuId() > 0) {
                        sysSkuIds.add(item.getSysSkuId());
                    }
                }
                Map<Long, DmjItem> itemMap = DMJItemUtils.itemList2Map(itemServiceWrapper.queryBySysItemIds(staff, sysItemIds, "sysItemId,title,shortTitle,outerId,remark,type"));
                Map<Long, DmjSku> skuMap = DmjSkuUtils.skuList2Map(itemServiceWrapper.queryBySysSkuIds(staff, sysSkuIds, "sysItemId,sysSkuId,outerId,propertiesName,propertiesAlias,remark,type"));
                for (PresellItem item : rule.getItems()) {
                    DmjItem dmjItem = itemMap.get(item.getSysItemId());
                    if (dmjItem == null) {
                        deleteIds.add(item.getId());
                        continue;
                    }
                    item.setOuterId(dmjItem.getOuterId());
                    item.setRemark(dmjItem.getRemark());
                    item.setTitle(dmjItem.getTitle());
                    item.setShortTitle(dmjItem.getShortTitle());
                    item.setPicPath(dmjItem.getPicPath());
                    if (item.getSysSkuId() > 0) {
                        DmjSku dmjSku = skuMap.get(item.getSysSkuId());
                        if (dmjSku == null) {
                            deleteIds.add(item.getId());
                            continue;
                        }
                        item.setOuterId(dmjSku.getOuterId());
                        item.setRemark(dmjSku.getRemark());
                        item.setSkuPropertiesName(dmjSku.getPropertiesName());
                        item.setSkuPropertiesAlias(dmjSku.getPropertiesAlias());
                        if (dmjSku.getPicPath() != null && !StockConstants.PATH_NO_PIC.equals(dmjSku.getPicPath())) {
                            item.setPicPath(dmjSku.getPicPath());
                        }
                    }
                }
            }
            //排序，编辑态下和日志展示的商品信息顺序一致
            List<PresellItem> sortedPreSellItemCollect = rule.getItems().stream()
                    .sorted(Comparator.comparing(PresellItem::getSysItemId, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(PresellItem::getSysSkuId, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            rule.setItems(sortedPreSellItemCollect);
        }
        //如果存在已删除的商品货sku仍然在配置中,需要将配置删除
        if (!deleteIds.isEmpty()) {
            presellItemDao.deleteByIds(staff, deleteIds, PresellItemUtils.RULE_TYPE_PRESELL, null, null, null);
        }
    }

    public String queryItemOutIds(Staff staff, List<PresellItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return "";
        }
        List<Long> sysItemIds = new ArrayList<>(), sysSkuIds = new ArrayList<>();
        for (PresellItem item : items) {
            if (item.getSysSkuId() != null && item.getSysSkuId() != -1 && item.getSysSkuId() != 0) {
                sysSkuIds.add(item.getSysSkuId());
            } else if (item.getSysItemId() != null) {
                sysItemIds.add(item.getSysItemId());
            }
        }
        return ConfigTraceUtils.queryItemOutIds(staff, sysItemIds, sysSkuIds, itemServiceWrapper);
    }

    public List<PresellItem> queryPresellItemsByIds(Staff staff, Integer ruleType, List<Long> ids) {
        return presellItemDao.queryItemsByIds(staff, ruleType, ids);
    }

    public List<PresellItem> queryPresellItemsOperLog(Staff staff, PresellItemQueryParams presellItemQueryParams) {
        return presellItemDao.queryPresellItemsLogByRuleIds(staff, presellItemQueryParams);
    }

    public Integer countPresellItemsOperLog(Staff staff, PresellItemQueryParams presellItemQueryParams) {
        return presellItemDao.countPresellItemsLogByRuleIds(staff, presellItemQueryParams);
    }

    public List<PresellItem> queryItemsByRuleId(Staff staff, PresellItemQueryParams presellItemQueryParams) {
        return presellItemDao.queryItemsByRuleId(staff, presellItemQueryParams.getRuleId(), presellItemQueryParams.getRuleType());
    }

    public Integer countItemsByRuleId(Staff staff, PresellItemQueryParams presellItemQueryParams) {
        return presellItemDao.queryItemsByRuleIAndItemTypeCount(staff, presellItemQueryParams.getRuleId(), presellItemQueryParams.getRuleType(), null, null, null, 60000);
    }

    public enum importItemType {
        item,
        sku;
    }

    public enum importNumberType {
        integer,
        decimal;
    }


    public Map<String, Object> queryItemByOutIds(Staff staff, String[][] data, String itemType, String numberType) {
        //默认按sku查询, 当入参为商家编码, 则查询该商家编码下的所有规格
        if (StringUtils.isBlank(itemType)) itemType = importItemType.sku.name();
        if (StringUtils.isBlank(numberType)) numberType = importNumberType.integer.name();
        Map<String, Object> dataMap = new HashMap<>();
        List<String> errorMsgs = new LinkedList<>();
        if (SpelCondition.FIELD_PLATFORM_ITEM_SKU_ID.equals(itemType)
                || SpelCondition.FIELD_PLATFORM_ITEM_NUM_I_ID.equals(itemType)) {
            ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_WAREHOUSE_RULE_IMPORT);
            dataMap.put("ItemList", Arrays.stream(data).map(s -> s[0]).collect(Collectors.toList()));
            dataMap.put("errorOuterIds", errorMsgs);
            dataMap.put("failCount", 0);
            dataMap.put("successCount", data.length);
            progressData.setCountCurrent(data.length);
            progressData.setProgress(2);
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_WAREHOUSE_RULE_IMPORT, progressData);
            return dataMap;
        }
        int progressCount = 0;
        Map<String, PresellItem> resultItemMap = new LinkedHashMap<>();
        List<String> outerIds = new LinkedList<>();
        Map<String, Integer> numMap = new HashMap<>();
        Map<String, Double> priceMap = new HashMap<>();
        Integer scale = PaymentCalculateSupports.getInstance().getScale(staff.getCompanyId());
        try {
            int rowIndex = 0;
            List<String> errorList = new ArrayList<>();
            for (String[] row : data) {
                rowIndex++;
                String outerId = (StringUtils.isBlank(row[0]) ? null : row[0].trim());
                int itemNum = 0;
                double price = 0D;
                if (row.length > 1 && StringUtils.isNotBlank(row[1])) {
                    if (importNumberType.integer.name().equalsIgnoreCase(numberType)) {
                        try {
                            int temInt = Integer.parseInt(row[1].trim());
                            if (temInt <= 0) {
                                errorList.add("第" + rowIndex + "行导入错误, 第二列字段必须为大于0的整数");
                                continue;
                            }
                            itemNum = Integer.parseInt(row[1].trim());
                            price = BigDecimal.valueOf(Double.parseDouble(row[1].trim())).setScale(Objects.nonNull(scale) ? scale : 3, RoundingMode.HALF_UP).doubleValue();
                        } catch (Exception e) {
                            errorList.add("第" + rowIndex + "行导入错误, 第二列字段无法转换成整数, 请确认");
                            continue;
                        }
                    } else if (importNumberType.decimal.name().equalsIgnoreCase(numberType)) {
                        try {
                            double temDou = Double.parseDouble(row[1].trim());
                            if (temDou <= 0D) {
                                errorList.add("第" + rowIndex + "行导入错误, 第二列字段必须为大于0的小数");
                                continue;
                            }
                            price = BigDecimal.valueOf(Double.parseDouble(row[1].trim())).setScale(Objects.nonNull(scale) ? scale : 3, RoundingMode.HALF_UP).doubleValue();
                        } catch (Exception e1) {
                            errorList.add("第" + rowIndex + "行导入错误, 第二列字段无法转换成小数, 请确认");
                            continue;
                        }
                    }
                }

                if (null == outerId) {
                    continue;
                }
                progressCount++;

                outerIds.add(outerId);
                // KMERP-231261，key转为小写，方便后续匹配
                numMap.put(outerId.toLowerCase(), itemNum);
                priceMap.put(outerId.toLowerCase(), price);
                if (outerIds.size() == 500) {
                    processResultList(staff, outerIds, itemType, numMap, priceMap, resultItemMap, errorMsgs);
                    outerIds.clear();
                    ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_WAREHOUSE_RULE_IMPORT);
                    progressData.setCountCurrent(progressCount);
                    progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_WAREHOUSE_RULE_IMPORT, progressData);
                }
            }

            if (CollectionUtils.isNotEmpty(errorList)) {
                throw new Exception(StringUtils.join(errorList, ";"));
            }

            if (outerIds.size() > 0) {
                processResultList(staff, outerIds, itemType, numMap, priceMap, resultItemMap, errorMsgs);
                ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_WAREHOUSE_RULE_IMPORT);
                progressData.setCountCurrent(progressCount);
                progressData.setProgress(2);
                progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_WAREHOUSE_RULE_IMPORT, progressData);
            }

        } catch (Exception e) {
            Logs.error(LogHelper.buildLog(staff, "批量导入商品异常堆栈信息"), e);
            ProgressData progressData = progressService.getOrCreate(staff, ProgressEnum.PROGRESS_TRADE_WAREHOUSE_RULE_IMPORT);
            progressData.setProgress(2);
            progressData.setErrorMsg(Collections.singletonList("批量导入商品异常" + e.getMessage()));
            progressService.updateProgress(staff, ProgressEnum.PROGRESS_TRADE_WAREHOUSE_RULE_IMPORT, progressData);
            throw new TradeException("批量导入商品异常:" + e.getMessage());
        }
        dataMap.put("ItemList", Lists.newArrayList(resultItemMap.values()));
        dataMap.put("errorOuterIds", errorMsgs);
        if (errorMsgs.size() > 0) {
            dataMap.put("failCount", errorMsgs.size());
            dataMap.put("successCount", progressCount - errorMsgs.size());
        } else {
            dataMap.put("failCount", 0);
            dataMap.put("successCount", progressCount);
        }
        return dataMap;
    }

    private void processResultList(Staff staff, List<String> outerIds, String itemType, Map<String, Integer> numMap, Map<String, Double> priceMap, Map<String, PresellItem> resultItemMap, List<String> errorMsgs) {
        if (CollectionUtils.isEmpty(outerIds)) {
            return;
        }


        StaffRequest staffRequest = StaffRequest.builder().companyId(staff.getCompanyId()).staffId(staff.getId()).build();
        DmjItemSearchField dmjItemSearchField =
                DmjItemSearchField
                        .DmjItemSearchFieldBuilder
                        .builder()
                        .dmjItemFields("sys_item_id,pic_path,outer_id,type,title,short_title,remark,active_status")
                        .dmjSkuFields("sys_item_id,sys_sku_id,properties_name,properties_alias,pic_path,outer_id,remark,active_status")
                        .build();
        QueryMiniItemByOuterIdListRequest request = new QueryMiniItemByOuterIdListRequest();
        request.setStaffRequest(staffRequest);
        request.setOuterIdList(outerIds);
        request.setDmjItemSearchField(dmjItemSearchField);
        request.setQueryMiniItem(false);
        request.setFillDmsFxPrice(false);
        if (StringUtils.equalsIgnoreCase(itemType, importItemType.sku.name())) {
            // 按SKU导入时, 需要填充查询SKU
            request.setNeedFillSku(true);
        }

        QueryMiniItemByOuterIdListResponse response = dmjItemCommonSearchApi.queryMiniItemByOuterIdList(request);

        List<DmjItemDto> list = response.getList();

        Map<String, List<PresellItem>> resultMap = new LinkedHashMap<>();
        Map<String, DmjItemDto> dmjItemMap = new LinkedHashMap<>();

        for (DmjItemDto dmjItemDto : list) {
            PresellItem presellItem = new PresellItem();
            presellItem.setType(dmjItemDto.getType());
            presellItem.setTitle(dmjItemDto.getTitle());
            presellItem.setShortTitle(dmjItemDto.getShortTitle());
            presellItem.setSysItemId(dmjItemDto.getSysItemId());
            presellItem.setPicPath(dmjItemDto.getPicPath());
            presellItem.setSortKey(dmjItemDto.getOuterId());
            if (dmjItemDto.getSysSkuId() > 0 && StringUtils.equalsIgnoreCase(itemType, importItemType.sku.name())) {
                presellItem.setOuterId(dmjItemDto.getSkuOuterId());
                presellItem.setSysSkuId(dmjItemDto.getSysSkuId());
                presellItem.setPicPath(dmjItemDto.getPicPath());
                presellItem.setSkuPropertiesAlias(dmjItemDto.getPropertiesAlias());
                presellItem.setSkuPropertiesName(dmjItemDto.getPropertiesName());
                presellItem.setRemark(dmjItemDto.getSkuRemark());
                // KMERP-231261，key转为小写，方便后续匹配
                resultMap.computeIfAbsent(presellItem.getOuterId().toLowerCase(), k -> new LinkedList<>()).add(presellItem);
            }else {
                presellItem.setOuterId(dmjItemDto.getOuterId());
                presellItem.setRemark(dmjItemDto.getRemark());
            }
            if (MapUtils.isNotEmpty(numMap)) {
                presellItem.setNum(numMap.get(dmjItemDto.getOuterId().toLowerCase()));
            }
            if (MapUtils.isNotEmpty(priceMap)) {
                presellItem.setPrice(priceMap.get(dmjItemDto.getOuterId().toLowerCase()));
            }
            // KMERP-231261，key转为小写，方便后续匹配
            resultMap.computeIfAbsent(presellItem.getSortKey().toLowerCase(), k -> new LinkedList<>()).add(presellItem);
            dmjItemMap.put(dmjItemDto.getOuterId().toLowerCase(), dmjItemDto);
        }

        //根据导入的outerId顺序
        for (String outerId : outerIds) {
            List<PresellItem> presellItems = resultMap.get(outerId.toLowerCase());
            DmjItemDto dmjItemDto = dmjItemMap.get(outerId.toLowerCase());
            if (Objects.nonNull(dmjItemDto) && "停用".equals(dmjItemDto.getActiveStatus())) {
                errorMsgs.add(outerId);
                continue;
            }
            if (CollectionUtils.isNotEmpty(presellItems)) {
                for (PresellItem presellItem : presellItems) {
                    resultItemMap.put(presellItem.getOuterId(), presellItem);
                }
            }else {
                errorMsgs.add(outerId);
            }
        }
    }

    /**
     * 根据ruleId删除规则商品
     */
    public int deleteByRuleId(Staff staff, Long ruleId, Integer ruleType) {
        return presellItemDao.deleteByRuleId(staff, ruleId, ruleType);
    }
}

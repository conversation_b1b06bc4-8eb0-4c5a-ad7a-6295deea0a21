package com.raycloud.dmj.business.common;

import com.alibaba.fastjson.JSONArray;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Sets;
import com.raycloud.dmj.Logs;
import com.raycloud.dmj.Strings;
import com.raycloud.dmj.domain.basis.Supplier;
import com.raycloud.dmj.domain.enums.ConvertQiMenSourceEnum;
import com.raycloud.dmj.domain.enums.TradeTypeEnum;
import com.raycloud.dmj.domain.pt.WarehouseAllocate;
import com.raycloud.dmj.domain.trade.address.TradeAddressUtils;
import com.raycloud.dmj.domain.trade.except.OrderExceptUtils;
import com.raycloud.dmj.domain.trade.item.utils.TradeItemUtils;
import com.raycloud.dmj.domain.trade.orderitemtag.OrderItemTag;
import com.raycloud.dmj.domain.trade.rule.AddressUtils;
import com.raycloud.dmj.domain.trade.type.TradeType;
import com.raycloud.dmj.domain.trades.Order;
import com.raycloud.dmj.domain.trades.OrderExt;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.spel.SpelCondition;
import com.raycloud.dmj.domain.trades.utils.*;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.domain.utils.CommonConstants;
import com.raycloud.dmj.except.enums.ExceptEnum;
import com.raycloud.dmj.newfx.trades.Constants;
import com.raycloud.dmj.services.trades.support.utils.AddressParseUtil;
import com.raycloud.dmj.services.trades.support.utils.ForbidAddressUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.raycloud.dmj.domain.trades.spel.SpelCondition.*;

/**
 * 简单封装了 {@link Trade}的部分字段，用于分析自动审核的表达式
 *
 * <AUTHOR>
 */
@Slf4j
public class SimpleTrade implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -79023993076433168L;

    private Long sid;

    private Long userId;

    private Long companyId;

    private Double payment;

    private String buyerMessage;

    private String sellerMemo;

    private String sellerMemoString;

    private String sysMemo;

    private List<String> outerId = new ArrayList<>();

    /**
     * 平台纯商品的商家编码
     * 这里的判断逻辑是order下outerSkuId为空时 outerIid不为空
     */
    private List<String> pureItemOuterIid = new ArrayList<>();

    private List<String> outerIid = new ArrayList<>();

    private List<String> outerSkuId = new ArrayList<>();

    //系统商家规格编码
    private List<String> systemOuterSkuId = new ArrayList<>();

    public List<String> getSystemOuterSkuId() {
        return systemOuterSkuId;
    }

    public void setSystemOuterSkuId(List<String> systemOuterSkuId) {
        this.systemOuterSkuId = systemOuterSkuId;
    }

    /**
     * 智能审核用到，单个比较
     */
    private Integer sellerFlag;
    /**
     * 匹配仓库用到，可能有多个
     */
    private String sellerFlagString;

    private String invoiceName;

    private Integer itemNum;

    private Integer itemKindNum;

    private Double netWeight;

    private Double volume;

    private String subSource;

    private String receiverCountry;

    private String receiverState;

    private String receiverCity;

    private String receiverDistrict;

    private String receiverStreet;

    private String receiverAddress;

    private String receiverName;

    private String receiverMobile;

    private String buyerNick;

    private String source;

    private String sourceId;

    private String taobaoId;

    /**
     * 子订单的isVirtual这个字段的拼接字符串，1,1,0,1,0,1的形式,在自动审核中用作判断条件
     */
    private String orderIsVirtualStr;

    private Double grossProfit;

    private Double grossProfitRate;

    private Long templateId;

    private Integer templateType;

    private Set<String> sysItemSkuIds = new HashSet<>();

    private Set<Long> sysItemIds = new HashSet<>();

    private Set<Long> brandIds = new HashSet<>();

    private Set<Long> supplierIds = new HashSet<>();

    private Set<String> numiids = new HashSet<>();

    private Set<String> skuIds = new HashSet<>();

    private Set<String> skuPropertiesNames = new HashSet<>();

    private Set<String> platformItemTitleNameSet = new HashSet<>();

    private String tagIds;

    private List<Order> orders = new ArrayList<>();

    /**
     * 是否处理买家留言： 0：未处理 1：已处理
     */
    private Integer isHandlerMessage;

    /**
     * 是否处理买家留言： 0：未处理 1：已处理
     */
    private Integer isHandlerMemo;

    private String tradeFrom;
    private String type;
    private Integer isPresell;
    private Integer splitType;
    private Integer mergeType;
    private Integer isUrgent;
    private Integer scalping;
    private Integer isStore;
    private Integer isTmallDelivery;
    private Integer belongType;
    private Integer convertType;


    private Double postFee;
    private Double paymentDiff;

    /**
     * 预计发货时间
     */
    private Long timeoutActionTime;

    /**
     * 是否有可合订单
     */
    private Integer checkManualMergeCount;

    /**
     * 常态合作码
     */
    private String cooperationNo;

    private String stockKey;

    /**
     * 主商家编码
     */
    private Long sysItemId;

    private Long sysSkuId;

    /**
     * 达人ID
     * 使用新字段 orderExt的  authorNo
     * 兼容旧字段 orderExt的  authorId    (后续会下线)
     */
    private String authorNo;

    /**
     * 订单为放心购代发订单时 存储放心购商家代发店铺id
     * 取值 trade_ext mallMaskId
     */
    private Long fxgMallMaskId;

    private List<Long> itemTagIds = Lists.newArrayList();
    private List<Long> tradeTagIds = Lists.newArrayList();

    /**
     * 是否包含赠品
     */
    private String gift;

    /**
     * 新版 订单类型
     */
    private Map<String, TradeType> tradeTypeMap;

    /**
     * 仓库编码
     */
    private Long warehouseId;

    private Integer nonConsignItemNum;

    private Long logisticsCompanyId;

    public String getReceiverStreet() {
        return receiverStreet;
    }

    public void setReceiverStreet(String receiverStreet) {
        this.receiverStreet = receiverStreet;
    }

    public Integer getConvertType() {
        return convertType;
    }

    public void setConvertType(Integer convertType) {
        this.convertType = convertType;
    }

    public Long getSysSkuId() {
        return sysSkuId;
    }

    public void setSysSkuId(Long sysSkuId) {
        this.sysSkuId = sysSkuId;
    }

    public Long getSysItemId() {
        return sysItemId;
    }

    public void setSysItemId(Long sysItemId) {
        this.sysItemId = sysItemId;
    }

    public String getStockKey() {
        return stockKey;
    }

    public void setStockKey(String stockKey) {
        this.stockKey = stockKey;
    }

    public List<Long> getItemTagIds() {
        return itemTagIds;
    }

    public void setItemTagIds(List<Long> itemTagIds) {
        this.itemTagIds = itemTagIds;
    }

    /**
     * 平台仓
     */
    private String platformWarehouse;

    /**
     * 达人名称set
     */
    private Set<String> authorNames = new HashSet<>();

    public String getPlatformWarehouse() {
        return platformWarehouse;
    }

    public void setPlatformWarehouse(String platformWarehouse) {
        this.platformWarehouse = platformWarehouse;
    }

    /**
     * 订单对应供应商分类
     */
    private Map<Long, Set<String>> orderSupplierTypeMap = new HashMap<>();

    public Map<Long, Set<String>> getOrderSupplierTypeMap() {
        return orderSupplierTypeMap;
    }

    public void setOrderSupplierTypeMap(Map<Long, Set<String>> orderSupplierTypeMap) {
        this.orderSupplierTypeMap = orderSupplierTypeMap;
    }

    public String getSellerFlagString() {
        return sellerFlagString;
    }

    public void setSellerFlagString(String sellerFlagString) {
        this.sellerFlagString = sellerFlagString;
    }

    public Double getPostFee() {
        return postFee;
    }

    public void setPostFee(Double postFee) {
        this.postFee = postFee;
    }

    public List<Order> getOrders() {
        return orders;
    }

    public void setOrders(List<Order> orders) {
        this.orders = orders;
    }

    public String getTradeFrom() {
        return tradeFrom;
    }

    public void setTradeFrom(String tradeFrom) {
        this.tradeFrom = tradeFrom;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public Integer getIsHandlerMessage() {
        return isHandlerMessage;
    }

    public void setIsHandlerMessage(Integer isHandlerMessage) {
        this.isHandlerMessage = isHandlerMessage;
    }

    public Integer getIsHandlerMemo() {
        return isHandlerMemo;
    }

    public void setIsHandlerMemo(Integer isHandlerMemo) {
        this.isHandlerMemo = isHandlerMemo;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }


    public Set<Long> getBrandIds() {
        return brandIds;
    }

    public void setBrandIds(Set<Long> brandIds) {
        this.brandIds = brandIds;
    }

    public Set<Long> getSupplierIds() {
        return supplierIds;
    }

    public void setSupplierIds(Set<Long> supplierIds) {
        this.supplierIds = supplierIds;
    }

    public Double getPayment() {
        if (null == payment)
            payment = 0.0D;
        return payment;
    }

    public void setPayment(Double payment) {
        this.payment = payment;
    }

    public String getBuyerMessage() {
        if (null == buyerMessage)
            buyerMessage = "";
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getSellerMemo() {
        if (null == sellerMemo)
            sellerMemo = "";
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public String getSysMemo() {
        if (null == sysMemo)
            sysMemo = "";
        return sysMemo;
    }

    public void setSysMemo(String sysMemo) {
        this.sysMemo = sysMemo;
    }

    public List<String> getOuterId() {
        return outerId;
    }

    public void setOuterId(List<String> outerId) {
        this.outerId = outerId;
    }

    public List<String> getOuterIid() {
        return outerIid;
    }

    public void setOuterIid(List<String> outerIid) {
        this.outerIid = outerIid;
    }

    public List<String> getOuterSkuId() {
        return outerSkuId;
    }

    public void setOuterSkuId(List<String> outerSkuId) {
        this.outerSkuId = outerSkuId;
    }

    public Integer getSellerFlag() {
        if (null == sellerFlag)
            sellerFlag = 0;
        return sellerFlag;
    }

    public void setSellerFlag(Integer sellerFlag) {
        this.sellerFlag = sellerFlag;
    }

    public String getInvoiceName() {
        if (null == invoiceName) {
            invoiceName = "";
        }
        return invoiceName;
    }

    public Integer getItemNum() {
        if (null == itemNum)
            itemNum = 0;
        return itemNum;
    }

    public void setItemNum(Integer itemNum) {
        this.itemNum = itemNum;
    }

    public Integer getItemKindNum() {
        if (null == itemKindNum)
            itemKindNum = 0;
        return itemKindNum;
    }

    public void setItemKindNum(Integer itemKindNum) {
        this.itemKindNum = itemKindNum;
    }

    public Double getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(Double netWeight) {
        this.netWeight = netWeight;
    }

    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public void setInvoiceName(String invoiceName) {
        this.invoiceName = invoiceName;
    }

    public String getOrderIsVirtualStr() {
        return orderIsVirtualStr;
    }

    public void setOrderIsVirtualStr(String orderIsVirtualStr) {
        this.orderIsVirtualStr = orderIsVirtualStr;
    }

    public Set<String> getSysItemSkuIds() {
        return sysItemSkuIds;
    }

    public Set<String> getNumiids() {
        return numiids;
    }

    public void setNumiids(Set<String> numiids) {
        this.numiids = numiids;
    }


    public Set<String> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(Set<String> skuIds) {
        this.skuIds = skuIds;
    }


    public Set<String> getSkuPropertiesNames() {
        return skuPropertiesNames;
    }

    public void setSkuPropertiesNames(Set<String> skuPropertiesNames) {
        this.skuPropertiesNames = skuPropertiesNames;
    }

    public void setSysItemSkuIds(Set<String> sysItemSkuIds) {
        this.sysItemSkuIds = sysItemSkuIds;
    }

    public String getSubSource() {
        return subSource;
    }

    public void setSubSource(String subSource) {
        this.subSource = subSource;
    }

    public String getReceiverCountry() {
        return receiverCountry;
    }

    public void setReceiverCountry(String receiverCountry) {
        this.receiverCountry = receiverCountry;
    }

    public String getReceiverState() {
        return receiverState;
    }

    public void setReceiverState(String receiverState) {
        this.receiverState = receiverState;
    }

    public String getReceiverCity() {
        return receiverCity;
    }

    public void setReceiverCity(String receiverCity) {
        this.receiverCity = receiverCity;
    }

    public String getReceiverDistrict() {
        return receiverDistrict;
    }

    public void setReceiverDistrict(String receiverDistrict) {
        this.receiverDistrict = receiverDistrict;
    }

    public String getTagIds() {
        return tagIds;
    }

    public void setTagIds(String tagIds) {
        this.tagIds = tagIds;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public Long getTimeoutActionTime() {
        return timeoutActionTime;
    }

    public void setTimeoutActionTime(Long timeoutActionTime) {
        this.timeoutActionTime = timeoutActionTime;
    }

    public Integer getCheckManualMergeCount() {
        return checkManualMergeCount;
    }

    public void setCheckManualMergeCount(Integer checkManualMergeCount) {
        this.checkManualMergeCount = checkManualMergeCount;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getCooperationNo() {
        return cooperationNo;
    }

    public void setCooperationNo(String cooperationNo) {
        this.cooperationNo = cooperationNo;
    }

    public Set<String> getAuthorNames() {
        return authorNames;
    }

    public void setAuthorNames(Set<String> authorNames) {
        this.authorNames = authorNames;
    }

    public Set<String> getPlatformItemTitleNameSet() {
        return platformItemTitleNameSet;
    }

    public void setPlatformItemTitleNameSet(Set<String> platformItemTitleNameSet) {
        this.platformItemTitleNameSet = platformItemTitleNameSet;
    }

    public String getGift() {
        if (null == gift) {
            gift = "";
        }
        return gift;
    }

    public void setGift(String gift) {
        this.gift = gift;
    }

    public Map<String, TradeType> getTradeTypeMap() {
        return tradeTypeMap;
    }

    public void setTradeTypeMap(Map<String, TradeType> tradeTypeMap) {
        this.tradeTypeMap = tradeTypeMap;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Long logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    /**
     * 这里是默认不做套件转单品处理
     * 兼容老代码
     */
    public static SimpleTrade build(Trade trade) {
        return buildFieldsFromTrade(trade, false);
    }

    /**
     * 这里将老代码整理下
     * 是否套件转单品
     * 这里做这个功能是有问题的  比如 trade已经做了套件转单品  但是标签规则里还是选的套件 不是套件明细的话  还是按照套件明细处理的
     */
    public static SimpleTrade buildFieldsFromTrade(Trade trade, boolean isSuitToSingleItem) {
        SimpleTrade st = new SimpleTrade();

        st.setSid(trade.getSid());
        st.setUserId(trade.getUserId());
        st.setCompanyId(trade.getCompanyId());
        st.setTaobaoId(String.valueOf(trade.getTaobaoId()));
        st.setSourceId(String.valueOf(trade.getSourceId()));
        st.setInvoiceName(trade.getInvoiceName());
        st.setPayment(NumberUtils.str2Double(trade.getPayment()));
        st.setSellerFlag(trade.getSellerFlag() == null ? 0 : trade.getSellerFlag().intValue());
        //-1代表无旗帜
        st.setSellerFlagString(StringUtils.isBlank(trade.getSellerFlagString()) ? "-1" : trade.getSellerFlagString());
        if (StringUtils.isNotBlank(trade.getSellerMemoString())) {
            st.setSellerMemo(trade.getSellerMemoString());
        } else {
            st.setSellerMemo(StringUtils.isNotBlank(trade.getSellerMemo()) ? trade.getSellerMemo() : "");
        }
        st.setSysMemo(StringUtils.isNotBlank(trade.getSysMemo()) ? trade.getSysMemo() : "");
        st.setBuyerMessage(trade.getBuyerMessage());
        st.setNetWeight(trade.getNetWeight());
        st.setVolume(trade.getVolume());
        st.setSubSource(trade.getSubSource());
        st.setReceiverCountry(StringUtils.trimToEmpty(trade.getReceiverCountry()));
        st.setReceiverState(StringUtils.trimToEmpty(trade.getReceiverState()));
        st.setReceiverCity(StringUtils.trimToEmpty(trade.getReceiverCity()));
        st.setReceiverDistrict(StringUtils.trimToEmpty(trade.getReceiverDistrict()));
        st.setReceiverStreet(StringUtils.trimToEmpty(trade.getReceiverStreet()));
        st.setReceiverName(StringUtils.trimToEmpty(trade.getReceiverName()));
        st.setReceiverAddress(StringUtils.trimToEmpty(trade.getReceiverAddress()));
        st.setTagIds(trade.getTagIds());
        st.setGrossProfit(trade.getGrossProfit() != null && !Objects.equals(trade.getGrossProfit(), 0D) ? trade.getGrossProfit() : TradeUtils.calculateGrossProfit(trade));
        st.setGrossProfitRate((st.getGrossProfit() != null && st.getPayment() != null && st.getPayment() != 0) ? (st.getGrossProfit() / st.getPayment()) * 100 : 0D);
        st.setPaymentDiff(trade.getPaymentDiff() == null ? TradeUtils.calculatePaymentDiff(trade) : trade.getPaymentDiff());
        st.setPostFee(StringUtils.isBlank(trade.getPostFee()) ? 0 : Double.parseDouble(trade.getPostFee()));
        st.setIsHandlerMemo(trade.getIsHandlerMemo());
        st.setIsHandlerMessage(trade.getIsHandlerMessage());
        st.setReceiverMobile(StringUtils.trimToEmpty(trade.getReceiverMobile()));
        st.setBuyerNick(trade.getBuyerNick());
        st.setTradeFrom(trade.getTradeFrom());
        st.setTimeoutActionTime(trade.getTimeoutActionTime() != null ? trade.getTimeoutActionTime().getTime() : 0L);
        st.setCheckManualMergeCount(trade.getCheckManualMergeCount() != null ? trade.getCheckManualMergeCount() : 0);
        st.setTemplateId(trade.getTemplateId());
        st.setTemplateType(trade.getTemplateType());
        st.setSource(trade.getSource());
        st.setType(trade.getType());
        st.setIsPresell(trade.getIsPresell());
        st.setSplitType(trade.getSplitType());
        st.setMergeType(trade.getMergeType());
        st.setIsUrgent(trade.getIsUrgent());
        st.setScalping(trade.getScalping());
        st.setIsStore(trade.getIsStore());
        st.setIsTmallDelivery(trade.getIsTmallDelivery());
        st.setBelongType(trade.getBelongType());
        st.setConvertType(trade.getConvertType());
        st.setTradeTagIds(trade.getTagIds());
        st.setOrders(TradeUtils.getOrders4Trade(trade));
        st.setPlatformWarehouse(trade.getTradeExt() == null ? "" : trade.getTradeExt().getStoreName());
        st.setFxgMallMaskId(trade.getTradeExt() == null ? null : trade.getTradeExt().getMallMaskId());
        st.setGift(CollectionUtils.isNotEmpty(TradeUtils.getGiftOrders4Trade(Collections.singletonList(trade))) ? "1" : "");
        st.setTradeTypeMap(trade.getTradeTypeMap());
        st.setWarehouseId(trade.getWarehouseId());
        st.setLogisticsCompanyId(trade.getLogisticsCompanyId());

        List<Order> originOrders = TradeUtils.getOrders4Trade(trade);
        return buildFieldFromOrders(st, trade, isSuitToSingleItem ? buildSingleItemOrders(originOrders) : originOrders);
    }

    /**
     * 根据order 构造属性
     * 老代码拆分出来
     */
    private static SimpleTrade buildFieldFromOrders(SimpleTrade st, Trade trade, List<Order> orders) {
        st.setOrders(orders);

        StringBuilder orderIsVirtualBuild = new StringBuilder();
        StringBuilder cooperationNos = new StringBuilder();
        cooperationNos.append(trade.getTradeExt() == null ? "" : trade.getTradeExt().getCooperationNo());
        //商品数量这里需要重算一边 因为trade没有套件转单品时存的是套件维度的
        int itemNum = 0;
        int nonConsignItemNum = 0;
        for (Order order : orders) {
            if (order.getEnableStatus() == null || order.getEnableStatus() == 0 ||
                    order.getItemSysId() == null || order.getNum() == null) {
                continue;
            }

            itemNum += order.getNum();
            if (order.ifNonConsign()) {
                nonConsignItemNum += order.getNum();
            }
            orderIsVirtualBuild.append(order.getIsVirtual()).append(",");

            OrderExt orderExt = order.getOrderExt();

            if (!Trade.SYS_STATUS_CLOSED.equals(order.getSysStatus())) {
                st.getOuterId().add(order.getSysOuterId());
                st.getOuterSkuId().add(order.getOuterSkuId());

                if (
                        (order.getOuterSkuId() == null || order.getOuterSkuId().isEmpty()) &&
                                order.getOuterIid() != null && !order.getOuterIid().isEmpty()
                ) {
                    st.pureItemOuterIid.add(order.getOuterIid());
                }
            }

            st.getSysItemSkuIds().add(TradeItemUtils.getItemKey(order.getItemSysId(), order.getSkuSysId()));
            st.sysItemIds.add(order.getItemSysId());

            if (order.getNumIid() != null && order.getNumIid().trim().length() > 0) {
                st.getNumiids().add(order.getNumIid());
            }

            if (order.getSkuId() != null && order.getSkuId().trim().length() > 0) {
                st.getSkuIds().add(order.getSkuId());
            }

            if (order.getSysOuterId() != null && order.getSysOuterId().trim().length() > 0) {
                st.getSystemOuterSkuId().add(order.getSysOuterId());
            }


            if (order.getSkuPropertiesName() != null && order.getSkuPropertiesName().trim().length() > 0) {
                st.getSkuPropertiesNames().add(order.getSkuPropertiesName());
            }

            if (order.getTitle() != null && order.getTitle().trim().length() > 0) {
                st.getPlatformItemTitleNameSet().add(order.getTitle());
            }

            if (CollectionUtils.isNotEmpty(order.getBrandIds())) {
                st.getBrandIds().addAll(new HashSet<>(order.getBrandIds()));
            }

            log.debug("商品供应商设置 tid:[{}] , sid:[{}] , oid:[{}] , supplierIds:[{}]", order.getTid(), order.getSid(), order.getOid(), order.getSupplierIds());
            if (CollectionUtils.isNotEmpty(order.getSupplierIds())) {
                st.getSupplierIds().addAll(new HashSet<>(order.getSupplierIds()));
            }

            if (orderExt != null && orderExt.getCooperationNoJitx() != null) {
                cooperationNos.append(",").append(orderExt.getCooperationNoJitx());
            }

            if (orderExt != null && StringUtils.isNotBlank(order.getOrderExt().getAuthorName())) {
                st.getAuthorNames().add(orderExt.getAuthorName());
            }

            if (orderExt != null) {
                if (StringUtils.isNotBlank(orderExt.getAuthorNo())) {
                    st.authorNo = orderExt.getAuthorNo();
                } else if (orderExt.getAuthorId() != null) {
                    st.authorNo = orderExt.getAuthorId().toString();
                }
            }

            if (CollectionUtils.isNotEmpty(order.getSupplierList())) {
                st.getOrderSupplierTypeMap().put(order.getId(), order.getSupplierList().stream().map(Supplier::getCategoryId).
                        filter(StringUtils::isNotBlank).collect(Collectors.toSet()));
            }

            List<OrderItemTag> orderItemTagList = order.getOrderItemTags();
            if (orderItemTagList != null && !orderItemTagList.isEmpty()) {
                st.itemTagIds.addAll(orderItemTagList.stream().filter(orderItemTag -> Objects.equals(1, orderItemTag.getEnableStatus())).map(OrderItemTag::getItemTagId).collect(Collectors.toList()));
            }
        }

        if (orderIsVirtualBuild.length() > 0) {
            st.setOrderIsVirtualStr(orderIsVirtualBuild.substring(0, orderIsVirtualBuild.length() - 1));
        }

        st.setCooperationNo(cooperationNos.toString());
        st.itemNum = itemNum;
        st.nonConsignItemNum = nonConsignItemNum;
        st.itemKindNum = trade.getItemKindNum();

        return st;
    }

    /**
     * 套件转单品 orders
     */
    private static List<Order> buildSingleItemOrders(List<Order> orders) {
        List<Order> singleItemOrders = new ArrayList<>();
        for (Order order : orders) {
            List<Order> suits = order.getSuits();
            if (order.isSuit() && suits != null && !suits.isEmpty()) {
                for (Order suit : suits) {
                    //把套件的附表信息写到套件明细中
                    suit.setOrderExt(order.getOrderExt());
                    //需要把系统修改商品的状态写到套件明细里
                    suit.setSysItemChanged(order.getSysItemChanged());
                    singleItemOrders.add(suit);
                }

                continue;
            }

            singleItemOrders.add(order);
        }

        return singleItemOrders;
    }

    public boolean validateSellerFlagMore(String sellerFlagValue, String operator) {
        //没有设置旗帜
        if (StringUtils.isBlank(sellerFlagValue)) {
            return true;
        }
        //订单没有旗帜
        if (StringUtils.isBlank(getSellerFlagString())) {
            return false;
        }
        //规则
        List<String> sellerFlagRule = Arrays.asList(sellerFlagValue.split(","));
        //订单
        String[] sellerFlagTrade = getSellerFlagString().split(",");

        //包含
        if (SpelCondition.OPERATOR_CONTAIN.equals(operator)) {
            for (String flag : sellerFlagTrade) {
                //有一个包含 返回true
                if (sellerFlagRule.contains(flag)) {
                    return true;
                }
            }
            return false;
        } else {
            //不包含
            for (String flag : sellerFlagTrade) {
                if (sellerFlagRule.contains(flag)) {
                    return false;
                }
            }
            return true;
        }
    }


    /**
     * 商家编码是否符合
     */
    public boolean validateSysItemSkuIds(String auditSysItemSkuIds, String operator, String type) {
        //指定商品
        if (SpelCondition.OPERATOR_ASSIGN.equals(operator)) {
            if (getSysItemSkuIds().isEmpty()) {
                return false;
            }
            if ("item".equals(type)) {//按款
                for (Long sysItemId : sysItemIds) {
                    if (auditSysItemSkuIds.contains(sysItemId.toString())) {
                        return true;
                    }
                }
            } else {

                for (String sysItemSkuId : getSysItemSkuIds()) {
                    if (auditSysItemSkuIds.contains(sysItemSkuId)) {
                        return true;
                    }
                }
            }
            return false;
        } else if (SpelCondition.OPERATOR_ONLY_ASSIGN.equals(operator)) {
            //仅指定（仅包含）商品 http://doc.raycloud.com/pages/viewpage.action?pageId=31163988
            if (getSysItemSkuIds().isEmpty()) {
                return false;
            }
            if ("item".equals(type)) {//按款
                for (Long sysItemId : sysItemIds) {
                    if (!auditSysItemSkuIds.contains(sysItemId.toString())) {
                        return false;
                    }
                }
            } else {
                for (String sysItemSkuId : getSysItemSkuIds()) {
                    if (!auditSysItemSkuIds.contains(sysItemSkuId)) {
                        return false;
                    }
                }
            }
            return true;
        } else {//排除商品
            if (getSysItemSkuIds().isEmpty()) {
                return true;
            }
            if ("item".equals(type)) {//按款
                for (Long sysItemId : sysItemIds) {
                    if (auditSysItemSkuIds.contains(sysItemId.toString())) {
                        return false;
                    }
                }
            } else {
                for (String sysItemSkuId : getSysItemSkuIds()) {
                    if (auditSysItemSkuIds.contains(sysItemSkuId)) {
                        return false;
                    }
                }
            }
            return true;
        }
    }


    /**
     * 品牌是否符合
     */
    public boolean validateBrandIds(String brandIds) {
        if (getBrandIds().isEmpty()) {
            return true;
        }
        //规则
        List<Long> brandIdList = Strings.getAsLongList(brandIds, ",", true);
        //商品
        for (Long brandId : getBrandIds()) {
            if (!brandIdList.contains(brandId)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 供应商是否符合
     */
    public boolean validateSupplierIds(String supplierIds) {
        if (getSupplierIds().isEmpty()) {
            return true;
        }
        //规则
        List<Long> supplierIdList = Strings.getAsLongList(supplierIds, ",", true);
        if (CollectionUtils.isNotEmpty(supplierIdList) && supplierIdList.size() == 1 && supplierIdList.get(0) == -1) {
            return true;
        }
        //商品
        for (Long supplierId : getSupplierIds()) {
            if (!supplierIdList.contains(supplierId)) {
                return false;
            }
        }
        return true;
    }

    public boolean validateWarehouseIds(String warehouseIds) {
        if (Objects.isNull(getWarehouseId())) {
            return false;
        }
        List<Long> warehouseIdList = Strings.getAsLongList(warehouseIds, ",", true);
        return warehouseIdList.contains(getWarehouseId());
    }

    /**
     * 供应商是否符合
     */
    public boolean validateContainSupplierIds(String supplierIds) {
        if (CollectionUtils.isEmpty(getSupplierIds())) {
            return false;
        }
        //规则
        List<Long> supplierIdList = Strings.getAsLongList(supplierIds, ",", true);
        if (CollectionUtils.isNotEmpty(supplierIdList) && supplierIdList.size() == 1 && supplierIdList.get(0) == -1) {
            return true;
        }
        //商品
        for (Long supplierId : getSupplierIds()) {
            if (supplierIdList.contains(supplierId)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 快递模板是否符合
     */
    public boolean validateTemplateIds(String templateIds) {
        if (Objects.isNull(getTemplateId()) || getTemplateId() <= 0L) {
            return false;
        }

        Integer templateType = getTemplateType();
        String compareTemplateId = "";
        //快递模版类型，0表示普通快递，1表示电子面单快递，默认为0
        if (!Objects.isNull(templateType) && templateType.compareTo(0) > 0) {
            compareTemplateId = "w" + getTemplateId();
        } else {
            compareTemplateId = "" + getTemplateId();
        }

        //规则
        List<String> configTemplateIdList = Strings.getAsStringList(templateIds, ",", true);
        return configTemplateIdList.contains(compareTemplateId);
    }

    public boolean validateLogisticsCompanyIds(String logisticsCompanyIds) {
        if (StringUtils.isBlank(logisticsCompanyIds)) {
            return true;
        }
        if (Objects.isNull(getLogisticsCompanyId()) || getLogisticsCompanyId() <= 0L) {
            return false;
        }
        List<String> ids = Strings.getAsStringList(logisticsCompanyIds, ",", true);
        return ids.contains(getLogisticsCompanyId() + "");
    }

    /**
     * 1.没有配置分销店铺id集合直接返回true
     * 2.非供销单返回false
     * 3.配置的店铺id集合是否包含交易的分销店铺id
     * <p>
     * 校验分销店铺信息
     */
    public boolean validateFxUserIds(String userIds) {
        if (StringUtils.isEmpty(userIds)) {
            return true;
        }

        if (!Constants.FxDefaultUserId.equals(getUserId())) {
            return false;
        }
        // 供销单的淘宝Id就是分销的店铺id
        String userId = getTaobaoId();

        List<String> userIdList = Strings.getAsStringList(userIds, ",", true);
        return userIdList.contains(userId);
    }

    /**
     * 1.没有配置分销商id集合直接返回true
     * 2.非供销单返回false
     * 3.配置的分销商id集合是否包含订单的分销商id
     * <p>
     * 校验分销商id
     */
    public boolean validateFxCompanyIds(String companyIds) {
        if (StringUtils.isEmpty(companyIds)) {
            return true;
        }

        if (!Constants.FxDefaultUserId.equals(getUserId())) {
            return false;
        }

        String companyId = getSourceId();
        // 订单没有分销商信息
        if (StringUtils.isEmpty(companyId) || "0".equals(companyId)) {
            return true;
        }

        List<String> companyIdList = Strings.getAsStringList(companyIds, ",", true);
        return companyIdList.contains(companyId);
    }

    /**
     * 商家编码关键字是否匹配
     * 默认根据商品的系统商家编码来匹配；如果订单商品是未匹配，则根据平台商家编码来匹配
     */
    public boolean validateOuterIdKeyword(String outerIdKeyword) {

        if (StringUtils.isEmpty(outerIdKeyword)) {
            return true;
        }

        outerIdKeyword = outerIdKeyword.trim();

        //根据商品的系统商家编码来匹配
        List<String> outIdList = getOuterId();
        if (CollectionUtils.isNotEmpty(outIdList)) {
            for (String sysOuterId : outIdList) {
                if (StringUtils.isNotEmpty(sysOuterId) && sysOuterId.contains(outerIdKeyword)) {
                    return true;
                }
            }
        }

        //如果订单商品是未匹配，则根据平台商家编码来匹配
        if (isUnAllocatedTrade()) {
            List<String> outerSkuIdList = getOuterSkuId();
            if (CollectionUtils.isNotEmpty(outerSkuIdList)) {
                for (String outerSkuId : outerSkuIdList) {
                    if (StringUtils.isNotEmpty(outerSkuId) && outerSkuId.contains(outerIdKeyword)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 判断是否为商品未匹配订单
     */
    private boolean isUnAllocatedTrade() {
        //商品未匹配
        return orders.stream().anyMatch(order -> OrderExceptUtils.isContainsExcept(null, order, ExceptEnum.UNALLOCATED));
    }

    /**
     * 单品sku数量是否符合
     */
    public boolean validateSkuNum(String min, String max) {
        //通过子订单id给子订单去重
        ArrayList<Order> duplicateOrders = orders.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Order::getOid))), ArrayList::new)
        );

        long conditionMin = StringUtils.isNotBlank(min) ? Integer.parseInt(min) : 0L;
        long conditionMax = StringUtils.isNotBlank(max) ? Integer.parseInt(max) : 999999L;
        //必须是有效的order(待审核/待财审/审核完成)，不包含交易关闭/已发货/交易成功的sku
        Map<String, IntSummaryStatistics> ordersMap = duplicateOrders.stream().filter(order -> TradeStatusUtils.isWaitSellerSend(order.getSysStatus()) && order.getSysOuterId() != null)
                .collect(Collectors.groupingBy(Order::getSysOuterId, Collectors.summarizingInt(Order::getNum)));
        for (Map.Entry<String, IntSummaryStatistics> m : ordersMap.entrySet()) {
            IntSummaryStatistics value = m.getValue();
            //分组数量
            long num = value.getSum();
            if (num <= conditionMax && num >= conditionMin) {
                return true;
            }
        }
        //对于商品未匹配订单,order.getSysOuterId是为null,这里根据ourterId分组
        //纯商品的情况下skuId为空 outIid不为空这里分两种情况,纯商品和非纯商品
        List<Order> noMatchOrderList = duplicateOrders.stream().filter(order -> TradeStatusUtils.isWaitSellerSend(order.getSysStatus()) && ObjectUtils.isEmpty(order.getSysOuterId())).collect(Collectors.toList());

        List<Order> outerIidOrders = noMatchOrderList.stream().filter(order -> StringUtils.isNotBlank(order.getOuterIid())).collect(Collectors.toList());
        Map<String, IntSummaryStatistics> itemNoMatchOrder = null;
        if (CollectionUtils.isNotEmpty(outerIidOrders)) {
            itemNoMatchOrder = outerIidOrders.stream().collect(Collectors.groupingBy(Order::getOuterIid, Collectors.summarizingInt(Order::getNum)));
        } else {
            itemNoMatchOrder = noMatchOrderList.stream().collect(Collectors.groupingBy(order -> StringUtils.isBlank(order.getSkuId()) ? "" : order.getSkuId(), Collectors.summarizingInt(Order::getNum)));
        }
        if (null == itemNoMatchOrder) {
            return false;
        }
        for (Map.Entry<String, IntSummaryStatistics> m : itemNoMatchOrder.entrySet()) {
            IntSummaryStatistics value = m.getValue();
            //分组数量
            long num = value.getSum();
            if (num <= conditionMax && num >= conditionMin) {
                return true;
            }
        }
        return false;
    }

    /**
     * KMERP-120698: 所有单sku数量是否全部符合
     */
    public boolean validateAllSkuNum(String min, String max) {
        Integer conditionMin = Optional.ofNullable(min).filter(StringUtils::isNotBlank).map(Integer::valueOf).orElse(0);
        Integer conditionMax = Optional.ofNullable(max).filter(StringUtils::isNotBlank).map(Integer::valueOf).orElse(Integer.MAX_VALUE);

        List<Order> calcOrders = new ArrayList<>(orders);

        // 获取待审核状态Order
        List<Order> filteredOrders = calcOrders.stream().filter(order -> TradeStatusUtils.isWaitSellerSend(order.getSysStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredOrders)) {
            return false;
        }

        // 已匹配系统商品 > 分组统计系统商品总数
        Map<String/*系统商品ID*/, Integer> sysItemNum = filteredOrders.stream()
                .filter(order -> StringUtils.isNotBlank(order.getSysOuterId()))
                .collect(Collectors.groupingBy(Order::getSysOuterId, Collectors.summingInt(Order::getNum)));
        boolean existUnmatched = sysItemNum.values().stream().anyMatch(num -> conditionMin > num || conditionMax < num);
        if (existUnmatched) {
            return false;
        }

        // 未匹配系统商品 > 分组统计平台商品总数
        Map<String/*平台商品ID*/, Integer> platformItemNum = filteredOrders.stream()
                .filter(order -> StringUtils.isBlank(order.getSysOuterId()))
                .collect(Collectors.groupingBy(Order::getNumIid, Collectors.summingInt(Order::getNum)));
        existUnmatched = platformItemNum.values().stream().anyMatch(num -> conditionMin > num || conditionMax < num);
        return !existUnmatched;
    }

    /**
     * 单品sku实付是否符合
     */
    public boolean validateSkuAmount(String min, String max) {
        double conditionMin = StringUtils.isNotBlank(min) ? Double.parseDouble(min) : 0.00D;
        double conditionMax = StringUtils.isNotBlank(max) ? Double.parseDouble(max) : Double.MAX_VALUE;
        //必须是有效的order(待审核/待财审/审核完成)，不包含交易关闭/已发货/交易成功的sku
        List<Order> filterOrders = orders.stream().filter(order -> TradeStatusUtils.isWaitSellerSend(order.getSysStatus())).collect(Collectors.toList());
        for (Order order : filterOrders) {
            //实付金额
            double singleSkuPayment = calculateSingleSkuPayment(order.getPayment(), order.getNum());
            if (singleSkuPayment <= conditionMax && singleSkuPayment >= conditionMin) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验商品单价
     */
    public boolean validateOrderPrice(String min, String max) {
        double conditionMin = (min != null && !min.trim().isEmpty()) ? Double.parseDouble(min.trim()) : Double.MIN_VALUE;
        double conditionMax = (max != null && !max.trim().isEmpty()) ? Double.parseDouble(max.trim()) : Double.MAX_VALUE;

        if (conditionMin == Double.MIN_VALUE && conditionMax == Double.MAX_VALUE) {
            return true;
        }

        if (orders == null || orders.isEmpty()) {
            return false;
        }

        for (Order order : orders) {
            //必须是有效的order(待审核/待财审/审核完成)，不包含交易关闭/已发货/交易成功的sku
            if (order.getPrice() == null || order.getPrice().trim().isEmpty() || !TradeStatusUtils.isWaitSellerSend(order.getSysStatus())) {
                continue;
            }

            double price = Double.parseDouble(order.getPrice().trim());
            if (price <= conditionMax && price >= conditionMin) {
                return true;
            }
        }

        return false;
    }

    /**
     * 校验折扣率
     */
    public boolean validateDiscountRate(String min, String max) {
        double conditionMin = NumberUtils.str2Double(min, Double.MIN_VALUE);
        double conditionMax = NumberUtils.str2Double(max, Double.MAX_VALUE);
        if (conditionMin == Double.MIN_VALUE && conditionMax == Double.MAX_VALUE) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(orders)) {
            for (Order order : orders) {
                //必须是有效的order(待审核/待财审/审核完成)，不包含交易关闭/已发货/交易成功的sku
                if (order.getDiscountRate() != null && TradeStatusUtils.isWaitSellerSend(order.getSysStatus())) {
                    double discountRate = order.getDiscountRate();
                    if (discountRate <= conditionMax && discountRate >= conditionMin) {
                        return true;
                    }
                }
            }
        }
        return false;
    }


    /**
     * 计算单个sku的实付金额
     */
    public Double calculateSingleSkuPayment(String totalPrice, Integer num) {
        if (num == 0) {
            return 0.00D;
        }
        BigDecimal price = new BigDecimal(totalPrice);
        BigDecimal count = new BigDecimal(num);
        return price.divide(count, 2, RoundingMode.HALF_UP).doubleValue();
    }


    /**
     * 验证订单地址是否符合
     */
    public boolean validateReceiverArea(String confAreas) {
        if (confAreas == null || (confAreas = confAreas.trim()).isEmpty()) {//没有设置自动审核的地址信息时默认允许自动审核
            return true;
        }
        if (StringUtils.isBlank(getReceiverState())) {//没有省份不自动审核
            return false;
        }
        boolean stateFlag = false;
        StringTokenizer t = new StringTokenizer(confAreas, ",");
        while (t.hasMoreTokens()) {
            String area = t.nextToken();
            String[] pcs = area.split("@");
            if (getReceiverState().startsWith(pcs[0]) || pcs[0].startsWith(getReceiverState())) {//省份匹配的情况下检查是否在例外地址中
                stateFlag = true;
                if (!StringUtils.isBlank(getReceiverCity())) {
                    for (int i = 1; i < pcs.length; i++) {//配置中有例外地址的情况下
                        String[] cds = pcs[i].split("\\$");
                        if (getReceiverCity().startsWith(cds[0]) || cds[0].startsWith(getReceiverCity())) {//匹配到例外地址中的城市
                            if (cds.length <= 1) {//例外地址中没有配置地区不允许自动审核
                                return false;
                            }
                            if (!StringUtils.isBlank(getReceiverDistrict())) {
                                for (int j = 1; j < cds.length; j++) {
                                    if (getReceiverDistrict().startsWith(cds[j]) || cds[j].startsWith(getReceiverDistrict())) {
                                        return false;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return stateFlag;
    }

    /**
     * 验证订单标签是否符合
     */
    public boolean validateTradeTag(String tagIds, String op) {
        Set<String> tradeTags = new HashSet<>();
        if (getTagIds() != null) {
            String[] tagIdArr = getTagIds().split(",");
            for (String tagIdStr : tagIdArr) {
                tagIdStr = tagIdStr.trim();
                if (!tagIdStr.isEmpty()) {
                    tradeTags.add(tagIdStr);
                }
            }
        }
        if (SpelCondition.OPERATOR_NOT.equals(op)) {//订单无标签自动审核
            return tradeTags.isEmpty();
        }
        Set<String> configTags = new HashSet<>();
        if (tagIds != null) {
            String[] tagIdArr = tagIds.split(",");
            for (String tagIdStr : tagIdArr) {
                tagIdStr = tagIdStr.trim();
                if (!tagIdStr.isEmpty()) {
                    configTags.add(tagIdStr);
                }
            }
        }
        if (SpelCondition.OPERATOR_ASSIGN.equals(op)) {//包含指定标签的订单自动审核
            for (String tagId : configTags) {
                if (tradeTags.contains(tagId)) {
                    return true;
                }
            }
            return false;
        } else if (SpelCondition.OPERATOR_EXCLUDE_ASSIGN.equals(op)) {//不包含指定标签的订单自动审核
            for (String tagId : configTags) {
                if (tradeTags.contains(tagId)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 验证订单类型是否符合
     */
    public boolean validateTradeType(String typeIds, String operator) {
        if (OPERATOR_EQUAL.equalsIgnoreCase(operator) || OPERATOR_CONTAIN.equalsIgnoreCase(operator)) {
            return tradeTypInTypeIds(typeIds);
        } else if (OPERATOR_NOT_CONTAIN.equalsIgnoreCase(operator)) {
            return !tradeTypInTypeIds(typeIds);
        }
        return false;
    }


    private boolean tradeTypInTypeIds(String typeIds) {
        boolean flag = false;
        String[] typeIdArr = ArrayUtils.toStringArray(typeIds);
        if (typeIdArr != null && typeIdArr.length > 0) {
            for (String typeId : typeIdArr) {
                TradeTypeEnum eachType = TradeTypeEnum.getInstanceByCode(typeId);
                if (eachType != null && eachType.getTypeToConfirm().apply(toTrade())) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }


    /**
     * 平台商品是否符合
     */
    public boolean validatePlatformUrl(String platformUrlCondition, String operator) {
        int i = 0;
        Set<String> numiidsSet = getNumiids();
        for (String numiid : numiidsSet) {
            //指定
            if (SpelCondition.OPERATOR_ASSIGN.equals(operator)) {
                if (StringUtils.contains(platformUrlCondition, numiid)) {
                    return true;
                }
            }
            //仅指定（仅包含）商品 http://doc.raycloud.com/pages/viewpage.action?pageId=31163988
            if (SpelCondition.OPERATOR_ONLY_ASSIGN.equals(operator)) {
                if (!StringUtils.contains(platformUrlCondition, numiid)) {
                    return false;
                }
            }
            //排除
            if (SpelCondition.OPERATOR_EXCLUDE_ASSIGN.equals(operator)) {
                //不包含
                if (!StringUtils.contains(platformUrlCondition, numiid)) {
                    i++;
                }
            }
        }

        //如果是排除操作，并且不包含的商品正好等于总商品返回ture
        if (SpelCondition.OPERATOR_EXCLUDE_ASSIGN.equals(operator) && numiidsSet.size() - i == 0) {
            return true;
        }
        return CollectionUtils.isNotEmpty(numiidsSet) && SpelCondition.OPERATOR_ONLY_ASSIGN.equals(operator);
    }


    /**
     * 平台商品Id是否符合
     */
    public boolean validatePlatformNumIid(String platformUrlCondition, String operator) {
        int i = 0;
        //订单里面的商品id
        //numIidConditions 规则中保存的数据
        String[] numIidConditions = StringUtils.split(platformUrlCondition, ",");
        Set<String> numIidConditionsSet = new HashSet<>();
        Collections.addAll(numIidConditionsSet, numIidConditions);
        Set<String> numiidsSet = getNumiids();
        for (String numiid : numiidsSet) {
            //指定
            if (SpelCondition.OPERATOR_ASSIGN.equals(operator)) {
                if (numIidConditionsSet.contains(numiid)) {
                    return true;
                }
            }
            if (SpelCondition.OPERATOR_ONLY_ASSIGN.equals(operator)) {
                if (!numIidConditionsSet.contains(numiid)) {
                    return false;
                }
            }
            //排除
            if (SpelCondition.OPERATOR_EXCLUDE_ASSIGN.equals(operator)) {
                //不包含
                if (!numIidConditionsSet.contains(numiid)) {
                    i++;
                }
            }
        }

        //如果是排除操作，并且不包含的商品正好等于总商品返回ture
        if (SpelCondition.OPERATOR_EXCLUDE_ASSIGN.equals(operator) && numiidsSet.size() - i == 0) {
            return true;
        }
        return CollectionUtils.isNotEmpty(numiidsSet) && SpelCondition.OPERATOR_ONLY_ASSIGN.equals(operator);
    }


    /**
     * 平台商品Sku是否符合
     */
    public boolean validatePlatformSkuId(String platformUrlCondition, String operator) {
        int i = 0;
        //订单里面的商品id
        //skuIdConditions 规则中保存的数据
        String[] skuIdConditions = StringUtils.split(platformUrlCondition, ",");
        Set<String> skuIdConditionsSet = new HashSet<>();
        Collections.addAll(skuIdConditionsSet, skuIdConditions);
        Set<String> skuIdSet = getSkuIds();
        for (String skuId : skuIdSet) {
            //指定
            if (SpelCondition.OPERATOR_ASSIGN.equals(operator)) {
                if (skuIdConditionsSet.contains(skuId)) {
                    return true;
                }
            }
            if (SpelCondition.OPERATOR_ONLY_ASSIGN.equals(operator)) {
                if (!skuIdConditionsSet.contains(skuId)) {
                    return false;
                }
            }
            //排除
            if (SpelCondition.OPERATOR_EXCLUDE_ASSIGN.equals(operator)) {
                //不包含
                if (!skuIdConditionsSet.contains(skuId)) {
                    i++;
                }
            }
        }

        //如果是排除操作，并且不包含的商品正好等于总商品返回ture
        if (SpelCondition.OPERATOR_EXCLUDE_ASSIGN.equals(operator) && skuIdSet.size() - i == 0) {
            return true;
        }
        return CollectionUtils.isNotEmpty(skuIdSet) && SpelCondition.OPERATOR_ONLY_ASSIGN.equals(operator);
    }


    /**
     * 平台商品是否符合
     */
    public boolean validatePlatformSkuProperties(String keyWords, String operator) {
        //指定商品
        if (SpelCondition.OPERATOR_ASSIGN.equals(operator)) {
            if (getSkuPropertiesNames().isEmpty()) {
                return false;
            }
            for (String keyWord : StringUtils.split(keyWords, ",")) {
                for (String skuPropertiesName : getSkuPropertiesNames()) {
                    if (StringUtils.contains(skuPropertiesName, keyWord)) {
                        return true;
                    }
                }
            }
            return false;
        } else if (SpelCondition.OPERATOR_ONLY_ASSIGN.equals(operator)) {
            //仅指定（仅包含）商品 http://doc.raycloud.com/pages/viewpage.action?pageId=31163988
            if (getSkuPropertiesNames().isEmpty()) {
                return false;
            }
            for (String skuPropertiesName : getSkuPropertiesNames()) {
                boolean contain = false;
                for (String keyWord : StringUtils.split(keyWords, ",")) {
                    //任意一个包含则满足条件
                    if (StringUtils.contains(skuPropertiesName, keyWord)) {
                        contain = true;
                        break;
                    }
                }
                if (!contain) {
                    return false;
                }
            }
            return true;
        } else {//排除商品
            if (getSkuPropertiesNames().isEmpty()) {
                return true;
            }
            for (String keyWord : StringUtils.split(keyWords, ",")) {
                for (String skuPropertiesName : getSkuPropertiesNames()) {
                    if (StringUtils.contains(skuPropertiesName, keyWord)) {
                        return false;
                    }
                }
            }
            return true;
        }
    }

    /**
     * @author: pxh
     * @description: 效验平台商品名称关键字
     */
    public boolean validatePlatformItemTitleProperties(String keyWords, String operator) {
        return validateKeyWord(keyWords, operator, getPlatformItemTitleNameSet());
    }

    /**
     * 平台规格商家编码关键字
     */
    public boolean validatePlatformOuterIds(String keyWords, String operator) {
        //规格平台商家编码 纯商品的平台商家编码 都需校验
        return validateKeyWord(keyWords, operator, outerSkuId) || validateKeyWord(keyWords, operator, pureItemOuterIid);
    }


    /**
     * 系统规格商家编码关键字
     */
    public boolean validateSystemSkuIds(String keyWords, String operator) {
        //规格平台商家编码 纯商品的平台商家编码 都需校验
        return validateKeyWord(keyWords, operator, systemOuterSkuId);
    }


    private boolean validateKeyWord(String keyWords, String operator, Collection<String> validateCollection) {
        if (StringUtils.isBlank(keyWords)) {
            return true;
        }
        Set<String> keywordSet = Arrays.stream(StringUtils.split(keyWords, ",")).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        //包含某一个
        if (SpelCondition.OPERATOR_ASSIGN.equals(operator)) {
            return Optional.ofNullable(validateCollection).filter(CollectionUtils::isNotEmpty)
                    .map(validates -> assignKeyword(validates, keywordSet))
                    .orElse(false);
        } else if (SpelCondition.OPERATOR_ONLY_ASSIGN.equals(operator)) {
            //配置规则包含效验的数据，并且只能比效验的数据多
            return Optional.ofNullable(validateCollection).filter(CollectionUtils::isNotEmpty)
                    .map(validates -> onlyAssignKeyword(validates, keywordSet))
                    .orElse(false);
        } else {//排除指定关键字
            return Optional.ofNullable(validateCollection)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(validates -> excludeAssignKeyword(validates, keywordSet))
                    .orElse(true);
        }
    }

    /**
     * @author: pxh
     * @description: 包含
     */
    private boolean assignKeyword(Collection<String> validateCollection, Set<String> keywordSet) {
        return keywordSet.stream().anyMatch(keyword -> validateCollection.stream().anyMatch(validateData -> StringUtils.contains(validateData, keyword)));
    }

    /**
     * @author: pxh
     * @description: 仅包含（相互全部包含）
     */
    private boolean onlyAssignKeyword(Collection<String> validateCollection, Set<String> keywordSet) {
        return validateCollection.stream().allMatch(data -> keywordSet.stream().anyMatch(data::contains));
    }

    /**
     * @author: pxh
     * @description: 存在排除的数据
     */
    private boolean excludeAssignKeyword(Collection<String> validateCollection, Set<String> keywordSet) {
        return keywordSet.stream().allMatch(keyword -> validateCollection.stream().noneMatch(validateData -> StringUtils.contains(validateData, keyword)));
    }


    /**
     * 平台商品是否符合
     */
    public boolean validateAreaMatch(String areaStr, String operator) {
        WarehouseAllocate rule = new WarehouseAllocate();
        rule.setAreaStr(areaStr);
        rule.setAreas(TradeAddressUtils.parseArea(areaStr));
        Trade tempTrade = new Trade();
        tempTrade.setSubSource(getSubSource());
        tempTrade.setReceiverCountry(getReceiverCountry());
        tempTrade.setReceiverState(getReceiverState());
        tempTrade.setReceiverCity(getReceiverCity());
        tempTrade.setReceiverDistrict(getReceiverDistrict());
        tempTrade.setReceiverStreet(getReceiverStreet());
        tempTrade.setReceiverAddress(getReceiverAddress());
        return TradeMatchUtils.isAreaMatched(tempTrade, rule.getAreas());
    }

    /**
     * 校验订单中的详细的地址与外面的地址是否不一致
     */
    public boolean validateDifferentAddress(String value, String opr) {
        return AddressParseUtil.validateDifferentAddress(this);
    }

    /**
     * 双地址重载
     */
    public boolean validateDifferentAddress(String value, String opr, String doubleAddressKeywords) {
        return AddressParseUtil.validateDifferentAddress(this, doubleAddressKeywords);
    }


    //地址关键字匹配
    public boolean validateAddressWord(String addressWord, String isContains) {
        if (StringUtils.isEmpty(addressWord) || StringUtils.isEmpty(addressWord.replace(",", ""))) {
            return true;
        }
        String tradeAddress = nvl(getReceiverCountry()) +
                nvl(getReceiverState()) +
                nvl(getReceiverCity()) +
                nvl(getReceiverDistrict()) +
                nvl(getReceiverAddress());
        return validateContains(addressWord, isContains, tradeAddress);
    }

    //地址关键字匹配
    public boolean validateAddressWordStateCityDistrict(String addressWord, String isContains) {
        if (StringUtils.isEmpty(addressWord) || StringUtils.isEmpty(addressWord.replace(",", ""))) {
            return true;
        }
        String tradeAddress = nvl(getReceiverCountry()) +
                nvl(getReceiverState()) +
                nvl(getReceiverCity()) +
                nvl(getReceiverDistrict());
        return validateContains(addressWord, isContains, tradeAddress);
    }

    public boolean validateCooperationNo(String cooperationNo, String isContains) {
        if (StringUtils.isEmpty(cooperationNo) || StringUtils.isEmpty(cooperationNo.replace(",", ""))) {
            return true;
        }
        String existCooperationsNo = getCooperationNo();
        return validateContains(cooperationNo, isContains, existCooperationsNo);
    }

    public boolean validateAuthorId(String configAuthorId) {
        if (StringUtils.isEmpty(configAuthorId)) {
            return true;
        }

        //将中文逗号替换成英文逗号,去掉配置的达人名称中的空格。
        configAuthorId = configAuthorId.replace("，", ",").replace(" ", "");
        String[] configAuthorIdArr = configAuthorId.split(",");
        if (configAuthorIdArr.length > 0) {
            if (StringUtils.isBlank(authorNo)) {
                return false;
            }
            for (String currentId : configAuthorIdArr) {
                if (currentId != null && currentId.trim().equals(authorNo)) {
                    return true;
                }
            }
        } else {
            return true;
        }

        if (log.isDebugEnabled()) {
            log.debug(String.format("达人信息不匹配,配置的达人ID为：%s,当前订单的达人ID为：%s", configAuthorId, authorNo));
        }
        return false;
    }

    public boolean validateAuthorName(String configAuthorName) {
        if (StringUtils.isEmpty(configAuthorName)) {
            return true;
        }

        //将中文逗号替换成英文逗号,去掉配置的达人名称中的空格。
        configAuthorName = configAuthorName.replace("，", ",").replace(" ", "");
        String[] authorNameArr = configAuthorName.split(",");
        if (authorNameArr.length > 0) {
            if (CollectionUtils.isEmpty(getAuthorNames())) {
                return false;
            }
            for (String name : authorNameArr) {
                if (getAuthorNames().contains(name)) {
                    return true;
                }
            }
        } else {
            return true;
        }

        if (log.isDebugEnabled()) {
            log.debug(String.format("达人信息不匹配,配置的达人名称为：%s,当前订单的达人名称为：%s", configAuthorName, String.join(",", getAuthorNames())));
        }
        return false;
    }

    /**
     * 地址匹配特殊字符，如：@、#、$
     */
    public boolean validateAddressSpecialSymbols(String addressSpecialSymbols, String operator) {
        if (StringUtils.isBlank(addressSpecialSymbols)) {
            return true;
        }

        return validateContains(addressSpecialSymbols, operator, getReceiverAddress());
    }

    //是否包含
    public boolean validateContains(String key, String isContains, String value) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(key.replace(",", ""))) {
            return true;
        }
        if (StringUtils.isEmpty(value)) {
            return false;
        }
        boolean needContains = SpelCondition.OPERATOR_CONTAIN.equals(isContains);
        StringTokenizer tokenizer = new StringTokenizer(key, ",");
        boolean iscontains = false;
        while (tokenizer.hasMoreTokens()) {
            String val = tokenizer.nextToken();
            if (!iscontains) {
                iscontains = value.contains(val);
            }
        }
        return needContains == iscontains;
    }

    public boolean validateCheckManualMergeCount(String mergePromptNotAutoAudit) {
        // 如果未开启则不管
        if (StringUtils.isBlank(mergePromptNotAutoAudit) || "0".equals(mergePromptNotAutoAudit)) {
            return true;
        }
        // 开启了，订单有合单标记，则该订单不满足
        return !Objects.equals(this.checkManualMergeCount, 1);
    }

    public boolean validateFxgDfMallMaskIds(String configFxgDfMallMaskIds) {
        if (StringUtils.isBlank(configFxgDfMallMaskIds) || !isDfSource(this)) {
            return true;
        }

        if (Objects.isNull(fxgMallMaskId)) {
            return false;
        }

        if (log.isDebugEnabled()) {
            log.debug("sid:{}, fxgMallMaskId:{}, configFxgDfUserIds:{}", sid, fxgMallMaskId, configFxgDfMallMaskIds);
        }
        return configFxgDfMallMaskIds.contains(fxgMallMaskId.toString());
    }

    /**
     * 放心购 || 拼多多厂家代发
     *
     * @param trade
     * @return
     */
    public static boolean isDfSource(SimpleTrade trade) {
        if (null == trade) {
            return false;
        }
        String tradeSource = trade.getSource();
        return CommonConstants.PLAT_FORM_TYPE_FXG_DF.equals(tradeSource) ||
                (Objects.equals(tradeSource, CommonConstants.PLAT_FORM_TYPE_PDD) &&
                        Objects.equals(trade.getSubSource(), CommonConstants.PLAT_FORM_TYPE_FDS)
                );

    }

    public boolean validateItemTagIds(String itemTagIds, String opr) {
        if (itemTagIds == null || itemTagIds.isEmpty()) {
            return true;
        }
        if (Objects.equals(SpelCondition.OPERATOR_ASSEMBLE, opr) || Objects.equals(SpelCondition.OPERATOR_NOT, opr)) {
            // 组合,无标签 不在这处理
            return true;
        }

        Set<Long> configItemTagIdSet = Sets.newHashSet();
        for (String s : itemTagIds.split(",")) {
            configItemTagIdSet.add(Long.parseLong(s));
        }

        Logs.debug(String.format("simpleTrade.itemTagIds: {%s}", this.getItemTagIds()));
        if (SpelCondition.OPERATOR_NOT_CONTAIN.equals(opr)) {
            //不包含
            if (this.getItemTagIds().isEmpty()) {
                return true;
            }

            //全部不包含在配置id里
            for (Long itemTagId : this.getItemTagIds()) {
                if (configItemTagIdSet.contains(itemTagId)) {
                    return false;
                }
            }
            return true;
        } else {
            //包含
            if (this.getItemTagIds().isEmpty()) {
                return false;
            }

            //至少有一个包含在配置里
            for (Long itemTagId : this.getItemTagIds()) {
                if (configItemTagIdSet.contains(itemTagId)) {
                    return true;
                }
            }
            return false;
        }
    }

    public boolean validateQimenSources(String qimenSources, String op) {
        // 非奇门订单直接通过
        if (!CommonConstants.PLAT_FORM_TYPE_QIMEN.equals(source) || StringUtils.isBlank(qimenSources)) {
            return true;
        }

        Logs.debug(String.format("simpleTrade  source:{%s} subSource: {%s}", this.getSource(), this.getSubSource()));

        String platformSource = ConvertQiMenSourceEnum.getPlatformSourceByQiMenSource(this.getSubSource());

        String[] qimenSourceSet = qimenSources.split(",");
        for (String qimenSource : qimenSourceSet) {
            if (ConvertQiMenSourceEnum.getPlatformSourceByQiMenSource(qimenSource).equalsIgnoreCase(platformSource)) {
                return true;
            }
        }
        return false;
    }

    public boolean validateTradeTagNew(String tradeTagIds, String opr) {
        if (tradeTagIds == null || tradeTagIds.isEmpty()) {
            return true;
        }
        Set<Long> configTradeTagIdSet = Sets.newHashSet();
        for (String s : tradeTagIds.split(",")) {
            configTradeTagIdSet.add(Long.parseLong(s));
        }
        if (SpelCondition.OPERATOR_NOT_CONTAIN.equals(opr)) {
            //不包含
            if (this.getTradeTagIds().isEmpty()) {
                return true;
            }
            //全部不包含在配置id里
            for (Long tradeTagId : this.getTradeTagIds()) {
                if (configTradeTagIdSet.contains(tradeTagId)) {
                    return false;
                }
            }
            return true;
        } else {
            //包含
            if (this.getTradeTagIds().isEmpty()) {
                return false;
            }

            //至少有一个包含在配置里
            for (Long tradeTagId : this.getTradeTagIds()) {
                if (configTradeTagIdSet.contains(tradeTagId)) {
                    return true;
                }
            }
            return false;
        }
    }

    public Double getGrossProfit() {
        if (grossProfit == null) {
            return 0.0D;
        }
        return grossProfit;
    }

    public void setGrossProfit(Double grossProfit) {
        this.grossProfit = grossProfit;
    }

    public Double getGrossProfitRate() {
        if (grossProfitRate == null) {
            return 0.0D;
        }
        return grossProfitRate;
    }

    public void setGrossProfitRate(Double grossProfitRate) {
        this.grossProfitRate = grossProfitRate;
    }

    private String nvl(String str) {
        return StringUtils.isEmpty(str) ? "" : str;
    }

    public void setPaymentDiff(Double paymentDiff) {
        this.paymentDiff = paymentDiff;
    }

    public Double getPaymentDiff() {
        return paymentDiff;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getIsPresell() {
        return isPresell;
    }

    public void setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
    }

    public Integer getSplitType() {
        return splitType;
    }

    public void setSplitType(Integer splitType) {
        this.splitType = splitType;
    }

    public Integer getMergeType() {
        return mergeType;
    }

    public void setMergeType(Integer mergeType) {
        this.mergeType = mergeType;
    }

    public Integer getIsUrgent() {
        return isUrgent;
    }

    public void setIsUrgent(Integer isUrgent) {
        this.isUrgent = isUrgent;
    }

    public Integer getScalping() {
        return scalping;
    }

    public void setScalping(Integer scalping) {
        this.scalping = scalping;
    }

    public Integer getIsStore() {
        return isStore;
    }

    public void setIsStore(Integer isStore) {
        this.isStore = isStore;
    }

    public Integer getIsTmallDelivery() {
        return isTmallDelivery;
    }

    public void setIsTmallDelivery(Integer isTmallDelivery) {
        this.isTmallDelivery = isTmallDelivery;
    }

    public Integer getBelongType() {
        return belongType;
    }

    public void setBelongType(Integer belongType) {
        this.belongType = belongType;
    }

    public Long getFxgMallMaskId() {
        return fxgMallMaskId;
    }

    public void setFxgMallMaskId(Long fxgMallMaskId) {
        this.fxgMallMaskId = fxgMallMaskId;
    }

    private Trade toTrade() {
        Trade trade = new Trade();
        trade.setType(getType());
        trade.setIsPresell(getIsPresell());
        trade.setSplitType(getSplitType());
        trade.setMergeType(getMergeType());
        trade.setIsUrgent(getIsUrgent());
        trade.setScalping(getScalping());
        trade.setIsStore(getIsStore());
        trade.setIsTmallDelivery(getIsTmallDelivery());
        trade.setBelongType(getBelongType());
        trade.setConvertType(getConvertType());
        trade.setSource(getSource());
        trade.setSubSource(getSubSource());
        trade.setCheckManualMergeCount(getCheckManualMergeCount());
        trade.setTradeTypeMap(getTradeTypeMap());
        return trade;
    }

    /**
     * @author: pxh
     * @description: 按款匹配赠品兼容老的代码逻辑
     * @date: 2021/9/7 2:43 下午
     * @param: sysItemIdStr
     * @param: skuIds
     * @param: minItemNumStr
     * @param: maxItemNumStr
     * @param: condition
     * @return: boolean
     */
    public boolean validateSysItemSkuIdsAndItemNum(String sysItemIdStr, String skuIds, String minItemNumStr, String maxItemNumStr, String condition) {
        return validateSysItemSkuIdsAndItemNum(sysItemIdStr, skuIds, minItemNumStr, maxItemNumStr, condition, null);
    }

    public boolean validateSysItemSkuIdsAndItemNum(String sysItemIdStr, String skuIds, String minItemNumStr, String maxItemNumStr, String condition, String matchType) {

        Long inputSysItemId = StringUtils.isNotBlank(sysItemIdStr) ? Long.parseLong(sysItemIdStr) : -1L;
        Long minItemNum = StringUtils.isNotBlank(minItemNumStr) ? Long.parseLong(minItemNumStr) : -1L;
        Long maxItemNum = StringUtils.isNotBlank(maxItemNumStr) ? Long.parseLong(maxItemNumStr) : -1L;
        //按款匹配赠品，不判断skuId
        boolean isMatchGiftBySysItemId = "1".equals(matchType);

        if (StringUtils.isBlank(condition)) {
            return Boolean.FALSE;
        }

        List<ElCondition> elConditionList = JSONArray.parseArray(condition, ElCondition.class);
        if (CollectionUtils.isEmpty(elConditionList)) {
            return Boolean.FALSE;
        }

        if (!inputSysItemId.equals(getSysItemId())) {
            return Boolean.FALSE;
        }

        if (!(minItemNum > 0
                && maxItemNum > 0
                && getItemNum() > 0)) {
            return Boolean.FALSE;
        }

        if (!(getItemNum() >= minItemNum && getItemNum() <= maxItemNum)) {
            return Boolean.FALSE;
        }

        if (StringUtils.isNotBlank(skuIds) && !isMatchGiftBySysItemId) {
            if (Objects.isNull(getSysSkuId())) {
                return Boolean.FALSE;
            }
            Optional<String> optional = Stream.of(skuIds.split(",")).filter(skuId -> StringUtils.isNotBlank(skuId) && getSysSkuId().equals(Long.valueOf(skuId))).findFirst();
            if (!optional.isPresent()) {
                return Boolean.FALSE;
            }
        }

        ElCondition elCondition = elConditionList.get(0);
        if (StringUtils.isBlank(elCondition.getValue())) {
            return Boolean.FALSE;
        }

        Set<String> valueSet = Stream.of(elCondition.getValue().split(",")).collect(Collectors.toSet());

        if (SpelCondition.FIELD_MESSAGE.equals(elCondition.getField())) {
            Optional<String> optional = valueSet.stream().filter(message -> getBuyerMessage().contains(message)).findFirst();
            return optional.isPresent();
        }

        if (SpelCondition.FIELD_MEMO.equals(elCondition.getField())) {
            Optional<String> optional = valueSet.stream().filter(memo -> getSellerMemo().contains(memo)).findFirst();
            return optional.isPresent();
        }
        return Boolean.FALSE;
    }

    public String getSellerMemoString() {
        return sellerMemoString;
    }

    public void setSellerMemoString(String sellerMemoString) {
        this.sellerMemoString = sellerMemoString;
    }

    public Integer getNonConsignItemNum() {
        return nonConsignItemNum;
    }

    public void setNonConsignItemNum(Integer nonConsignItemNum) {
        this.nonConsignItemNum = nonConsignItemNum;
    }

    @Data
    public static class ElCondition {
        private String field;
        private String operator;
        private String value;
    }

    public boolean validatePlatformWarehouse(String configValue, String isContains) {

        if (StringUtils.isEmpty(configValue)) {
            return true;
        }

        String platformWarehouse = getPlatformWarehouse();
        if (StringUtils.isNotBlank(configValue) && StringUtils.isBlank(platformWarehouse)) {
            return false;
        }

        //configValue 是这样的格式 ["成都商超A个护清洁仓3号库", "1号仓", "5号库"]
        if (configValue.contains(platformWarehouse)) {
            return true;
        } else {
            if (log.isDebugEnabled()) {
                log.debug(String.format("平台仓不匹配,配置的平台仓信息为：%s,当前订单的平台仓为：%s", configValue, platformWarehouse));
            }
            return false;
        }
    }

    /**
     * 供应商分类匹配
     *
     * @param matchType 匹配类型
     */
    public boolean supplierCategoryIdMatch(String supplierCategoryIds, String matchType) {
        if (StringUtils.isBlank(supplierCategoryIds) || StringUtils.isEmpty(matchType)) {
            return false;
        }

        if (MapUtils.isEmpty(orderSupplierTypeMap)) {
            return false;
        }

        Set<String> configSupplierCategoryIdSet = Arrays.stream(StringUtils.split(supplierCategoryIds, ",")).collect(Collectors.toSet());
        Set<String> orderSupplierCategoryIdSet = orderSupplierTypeMap.entrySet().stream()
                .filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                .flatMap(entry -> entry.getValue().stream()).collect(Collectors.toSet());
        //包含
        if (SpelCondition.OPERATOR_CONTAIN.equalsIgnoreCase(matchType)) {
            return configSupplierCategoryIdSet.stream().anyMatch(orderSupplierCategoryIdSet::contains);
        }
        //不包含
        if (SpelCondition.OPERATOR_NOT_CONTAIN.equalsIgnoreCase(matchType)) {
            return configSupplierCategoryIdSet.stream().noneMatch(orderSupplierCategoryIdSet::contains);
        }
        //仅包含（可以比配置的供应商少，不能比配置的供应商多）
        if (SpelCondition.OPERATOR_ONLY_ASSIGN.equalsIgnoreCase(matchType)) {
            Set<String> orderSupplierTypeSet = orderSupplierTypeMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(orderSupplierTypeSet)) {
                return false;
            }
            return configSupplierCategoryIdSet.containsAll(orderSupplierTypeSet);
        }
        return false;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(String taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * 系统禁发地址匹配
     */
    public boolean matchForbidAddress(String value, String opr) {
        return ForbidAddressUtils.matchForbidAddress(this);
    }

    public List<Long> getTradeTagIds() {
        return tradeTagIds;
    }

    public void setTradeTagIds(String tradeTagIds) {
        Set<Long> tagIds = ArrayUtils.toLongSet(tradeTagIds);
        if (CollectionUtils.isNotEmpty(tagIds)) {
            this.tradeTagIds.addAll(tagIds);
        }
    }
}

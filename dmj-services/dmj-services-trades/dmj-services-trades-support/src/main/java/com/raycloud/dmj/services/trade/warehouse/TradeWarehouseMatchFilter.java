package com.raycloud.dmj.services.trade.warehouse;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.config.TradeConfigContext;
import com.raycloud.dmj.domain.trade.except.*;
import com.raycloud.dmj.domain.trade.utils.*;
import com.raycloud.dmj.domain.trade.warehouse.*;
import com.raycloud.dmj.domain.trades.*;
import com.raycloud.dmj.domain.trades.utils.NumberUtils;
import com.raycloud.dmj.domain.trades.utils.TradeStatusUtils;
import com.raycloud.dmj.except.enums.ExceptEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.raycloud.dmj.domain.rematch.enums.EventEnum.*;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @Date 2022/11/4 10:54
 * @Description
 */
@Service
public class TradeWarehouseMatchFilter {

    public List<Trade> filterSync(Staff staff, TradeWarehouseContext context, List<Trade> trades, TradeImportResult tir) {
        if (CollectionUtils.isEmpty(trades)) {
            return trades;
        }
        List<Trade> result = new ArrayList<>();
        for (Trade trade : trades) {
            //尚未匹配仓库的订单匹配仓库
            if (trade.getWarehouseId() == null || trade.getWarehouseId() <= 0 || (trade.getIsCancelDistributorAttribute() != null && trade.getIsCancelDistributorAttribute().equals(1))) {
                result.add(trade);
                continue;
            }

            //待付款变成待审核
            if (Trade.SYS_STATUS_WAIT_BUYER_PAY.equals(trade.getOldSysStatus()) && TradeStatusUtils.isWaitAudit(trade.getSysStatus()) && !NumberUtils.isEquals(trade.getIsCancelDistributorAttribute(), 1)) {
                tir.event2needRematchSidSet.computeIfAbsent(EVENT_TRADE_BE_WAIT_AUDIT, k -> new HashSet<>()).add(trade.getSid());
                continue;
            }

            // 取消风控异常需要重新匹配仓库
            boolean riskChange = !TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RISK) && trade.getOrigin() != null && TradeExceptUtils.isContainExcept(staff, trade.getOrigin(), ExceptEnum.RISK);
            if (riskChange) {
                tir.event2needRematchSidSet.computeIfAbsent(EVENT_TRADE_CANCEL_RISK, k -> new HashSet<>()).add(trade.getSid());
                continue;
            }

            //留言备注||旗帜有变更，需要重算
            TradeConfigContext configContext = context.getConfigContext();
            if (trade.getOrigin() != null
                    && configContext.isChangeMemoRecalculationWarehouse()
                    && TradeStatusUtils.isWaitAudit(trade.getSysStatus())
                    && (!StringUtils.equals(StringUtils.stripToEmpty(trade.getSellerMemo()), StringUtils.stripToEmpty(trade.getOrigin().getSellerMemo())) || !NumberUtils.isEquals(trade.getSellerFlag(), trade.getOrigin().getSellerFlag()))) {
                tir.event2needRematchSidSet.computeIfAbsent(EVENT_TRADE_CHANGE_SELLER_MEMO_FLAG, k -> new HashSet<>()).add(trade.getSid());
                context.getReMatchSids().add(trade.getSid());
            }
        }
        return result;
    }

    public List<TradeWarehouseMatchResult> filterSplit(Staff staff, TradeWarehouseContext context, List<TradeWarehouseMatchResult> matchResults) {
        Long defaultWarehouseId = context.getDefaultWarehouse().getId();
        for (TradeWarehouseMatchResult matchResult : matchResults) {
            Trade trade = matchResult.originTrade;

            if (Objects.isNull(matchResult.resultData) && MapUtils.isNotEmpty(matchResult.toWarehouseResultSplitData)) {
                // 存在拆分

                String matchMsg = null;
                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RISK)) {
                    // 风控订单
                    matchMsg = "分仓触发拆分,风控订单不允许拆分,匹配到默认仓";
                }
                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.HALT)) {
                    // 挂起订单
                    matchMsg = "分仓触发拆分,挂起订单不允许拆分,匹配到默认仓";
                }
                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.BLACK_NICK)) {
                    // 黑名单订单
                    matchMsg = "分仓触发拆分,黑名单订单不允许拆分,匹配到默认仓";
                }
                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.UPLOAD_EXCEPT)) {
                    // 上传异常订单
                    matchMsg = "分仓触发拆分,上传异常订单不允许拆分,匹配到默认仓";
                }
                if (TradeExceptUtils.isContainExcept(staff, trade, ExceptEnum.RELATION_CHANGED)) {
                    // 商品对应关系改动异常订单
                    matchMsg = "分仓触发拆分,商品对应关系改动异常订单不允许拆分,匹配到默认仓";
                }
                if (PlatformUtils.isJdVCGongXiaoDaOrder(trade)) {
                    // 工小达订单
                    matchMsg = "分仓触发拆分,工小达订单不允许拆分,匹配到默认仓";
                }

                if (matchMsg != null) {
                    matchResult.resultData = new TradeWarehouseMatchResult.ResultData(defaultWarehouseId, matchMsg);
                    matchResult.toWarehouseResultSplitData.clear();
                }
            }
        }
        return matchResults;
    }

}

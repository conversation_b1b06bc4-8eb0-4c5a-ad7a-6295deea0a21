package com.raycloud.dmj.services.trade.item.entity;

import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.trade.item.TradeItemContext;
import com.raycloud.dmj.domain.trades.Trade;
import com.raycloud.dmj.domain.trades.TradeQueryParams;
import com.raycloud.dmj.services.trade.item.common.TradeItemQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23
 * @description 实体编码转换查询
 */
@Service
public class TradeEntityCodeConvertQuery {

    @Resource
    TradeItemQuery tradeItemQuery;
    @Resource
    TradeEntityCodeConvertFill tradeEntityCodeConvertFill;
    @Resource
    TradeEntityCodeConvertFilter TradeEntityCodeConvertFilter;

    public List<Trade> query(Staff staff, TradeItemContext itemContext, TradeQueryParams params) {
        return fillAndFilter(staff, itemContext, tradeItemQuery.query(staff, params));
    }


    public List<Trade> fillAndFilter(Staff staff, TradeItemContext itemContext, List<Trade> trades) {
        if (CollectionUtils.isNotEmpty(trades)) {
            tradeEntityCodeConvertFill.fill(staff, itemContext, trades);
            TradeEntityCodeConvertFilter.filter(staff, itemContext, trades);
        }
        return trades;
    }

}
